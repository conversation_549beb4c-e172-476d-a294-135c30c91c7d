const detailIndex = "2";// 开票计划所在明细
const detailStr = "detail_" + detailIndex;
const field_contract_code = "zyhtbh";//主表-字段名-主要合同编号
const field_plan_code = "kpjhbh";//明细表-字段名-计划编号

let existPlanCodes = []; //合同对应的已存在的计划编号数组
let fi_contract_code = "";  //主表-字段id-主要合同编号
let fi_plan_code = "";  //明细表-字段id-计划编号
let latest_contract_code = ""; //最新的主要合同编号
let maxNumber = 0; //当前已有计划最大编号

/**
 * 初始化
 */
function init() {
    fi_contract_code = WfForm.convertFieldNameToId(field_contract_code); //主表-字段id-主要合同编号
    fi_plan_code = WfForm.convertFieldNameToId(field_plan_code, detailStr);//明细表-字段id-计划编号
}

/**
 * 页面加载完毕执行
 */
jQuery(document).ready(function () {
    //初始化
    init();
    //初始化加载时，可能直接就有了合同编号
    //设置延迟，每100毫秒执行一次，满足条件后，删除t1延时器
    let limitTime = 5 * 1000; //设置加载延迟最大值 单位毫秒
    let startTime = new Date();
    let t1 = window.setInterval(function () {
        let currentTime = new Date();
        let diffTime = currentTime - startTime;
        //超过限制延时
        if (diffTime > limitTime) {
            window.clearInterval(t1);
            console.log("超时没有明细行数据，纯新建逻辑")
        } else {
            let count = WfForm.getDetailRowCount(detailStr)
            if (count && count * 1 > 0) {
                window.clearInterval(t1);
                console.log("初始就有明细行");
                let fv_contract_code = WfForm.getFieldValue(fi_contract_code);
                if (fv_contract_code) {
                    console.log("初始就有合同编号，重新计算计划编号");
                    latest_contract_code = fv_contract_code;
                    //获取已有的合同的对应开票计划编号
                    getExistPlanCodes(fv_contract_code);
                    //设置当前最大的计划编号
                    setMaxExistCodesNumber();
                    //重设计划编号
                    resetPlanCode();
                }
            }
        }
    }, 50);

    //合同编号变化，获取最新的计划编号数组
    WfForm.bindFieldChangeEvent(fi_contract_code, function (obj, id, value) {
        console.log("选择合同变化", obj, id, value);
        existPlanCodes = []; //先清空已有的计划编号数组
        maxNumber = 0; //当前已有计划最大编号
        //设置当前最新选择的合同编号
        latest_contract_code = value;
        //清空明细2
        WfForm.delDetailRow(detailStr, "all");//删除明细所有行
        if (value) {
            //获取已有的合同的对应开票计划编号
            getExistPlanCodes(value);
            //设置当前最大的计划编号
            setMaxExistCodesNumber();
        }
        console.log("existPlanCodes：", existPlanCodes)
        console.log("maxNumber", maxNumber);

    });

    //新增明细后，给新增的计划编号赋值
    WfForm.registerAction(WfForm.ACTION_ADDROW + detailIndex, function (index) {
        resetPlanCode();
    });
    //删除明细后，给新增的计划编号赋值
    WfForm.registerAction(WfForm.ACTION_DELROW + detailIndex, function (arg) {
        resetPlanCode();
    });

    //删除明细前，校验是否勾选有已存在的计划编号
    WfForm.registerCheckEvent(WfForm.OPER_DELROW + detailIndex, function (callback) {
        console.log("删除明细前执行逻辑");
        let hasError = false;
        let checkedRows = WfForm.getDetailCheckedRowIndexStr(detailStr);    //输出选中行1，3...等等
        if (checkedRows) {
            let rowArr = checkedRows.split(",");
            for (let i = 0; i < rowArr.length; i++) {
                let rowIndex = rowArr[i];
                if (rowIndex !== "") {
                    let fm_plan_code = fi_plan_code + "_" + rowIndex;
                    let fv_plan_code = WfForm.getFieldValue(fm_plan_code) + ""
                    if (existPlanCodes.indexOf(fv_plan_code) >= 0) {
                        hasError = true;
                    }
                }
            }
        }
        if (hasError) {
            WfForm.showMessage("不可删除合同已有的开票计划", 2, 5);  //错误信息，10s后消失
        } else {
            callback(); //允许继续删除行调用callback，不调用代表阻断删除
        }
    });
})

/**
 * 获取合同对应已有的开票计划
 * @param contractCode
 */
function getExistPlanCodes(contractCode) {
    let sql = "select kpjhbh from uf_kpjhtz where zyhtbh = '" + contractCode + "' "
    let result = window.sdCommonUtil.queryData(sql, []);
    if (result && result.length > 0) {
        result.forEach(function (item) {
            existPlanCodes.push(item.kpjhbh);
        })
    }
}

/**
 * 重设计划编号
 */
function resetPlanCode() {
    let currentMaxNumber = maxNumber;
    let rowArr = WfForm.getDetailAllRowIndexStr(detailStr).split(",");
    for (let i = 0; i < rowArr.length; i++) {
        let rowIndex = rowArr[i];
        if (rowIndex !== "") {
            let fm_plan_code = fi_plan_code + "_" + rowIndex;
            let fv_plan_code = WfForm.getFieldValue(fm_plan_code);
            //新增的数据，设置计划编号
            if (existPlanCodes.indexOf(fv_plan_code) < 0) {
                currentMaxNumber++;
                let newCode = "KPJH-" + latest_contract_code + "-" + currentMaxNumber;
                WfForm.changeFieldValue(fm_plan_code, {value: newCode});
            }
        }
    }
}

/**
 * 获取已存在的计划最大的编号数字
 * @returns {number}
 */
function setMaxExistCodesNumber() {
    maxNumber = 0; // 默认最大的编号为0
    //遍历明细行标,获取明细行各个数据
    if (existPlanCodes.length > 0) {
        existPlanCodes.forEach(function (item) {
            let currentNumber = getLastNumber(item) * 1;
            if (currentNumber > maxNumber) {
                maxNumber = currentNumber;
            }
        })
    }
}

/**
 * 获取文本最后一个杠后面的数字
 * @param planCode
 * @returns {*}
 */
function getLastNumber(planCode) {
    // 使用 split 方法将字符串按斜杠分割成数组
    const parts = planCode.split('-');
    // 获取数组的最后一个元素
    return parts[parts.length - 1];
}

