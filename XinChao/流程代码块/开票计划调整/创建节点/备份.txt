<script>
  jQuery(document).ready(function(){
  WfForm.registerAction(WfForm.ACTION_ADDROW+"1", function(index){
    indexcol = WfForm.getDetailAllRowIndexStr("detail_1")
    array = indexcol.split(',')
    i = 1
    array.forEach(function(item){
      let fid = WfForm.convertFieldNameToId("xh", "detail_1")
      WfForm.changeSingleField(fid+'_'+item, {value:i}, {viewAttr:"1"});
      i=i+1;
    })
  });
  WfForm.registerAction(WfForm.ACTION_DELROW+"1", function(index){
    indexcol = WfForm.getDetailAllRowIndexStr("detail_1")
    array = indexcol.split(',')
    i = 1
    array.forEach(function(item){
      let fid = WfForm.convertFieldNameToId("xh", "detail_1")
      WfForm.changeSingleField(fid+'_'+item, {value:i}, {viewAttr:"1"});
      i=i+1;
    })
  });

  let t1 = window.setInterval(function(){
    let rowCnt = parseInt($("#indexnum0").val())
    if(rowCnt > 0){
      indexcol = WfForm.getDetailAllRowIndexStr("detail_1")
      array = indexcol.split(',')
      i = 1
      array.forEach(function(item){
        let fid = WfForm.convertFieldNameToId("xh", "detail_1")
        WfForm.changeSingleField(fid+'_'+item, {value:i}, {viewAttr:"1"});
        i=i+1;
      })
    window.clearInterval(t1)
    }
  },100)
})
</script>

<style>
 #delbutton0{
display:none !important;
}
</style>
