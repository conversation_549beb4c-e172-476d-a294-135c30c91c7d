package com.engine.cy.checkcus.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.cy.checkcus.util.FileUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.toolbox.doc.DownloadManager;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 * @FileName SqlExecuteCmd
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/20
 */
public class CusCheckCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CusCheckCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    private final BaseBean bb;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String id = Util.null2String(params.get("fileid"));
        String colname = Util.null2String(params.get("colname"));
        String depid = Util.null2String(params.get("depid"));
        File file = null;
        try {
            file = new File(DownloadManager.getFileByImageFileId(Integer.parseInt(id)));
        } catch (IOException e) {
            result.put("status", "0");
            result.put("dec", "获取文件出错" + e.getMessage());
            return result;
        }

        try {
            return FileUtils.searchFilecolSam4excel(depid, colname, file, result);
        } catch (Exception e) {
            result.put("status", "0");
            result.put("dec", e.getMessage());
            return result;
        }
    }
}