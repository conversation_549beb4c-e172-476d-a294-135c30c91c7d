package com.engine.cy.checkcus.util;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.io.File;
import java.io.IOException;
import java.util.Map;

public class FileUtils {

    public static Map<String, Object> searchFilecolSam4excel(String depid, String name, File file, Map<String, Object> result) throws IOException, BiffException {
        String filename = file.getName();
        String[] filenamearray = filename.split("\\.");
        if (!"xls".equals(filenamearray[filenamearray.length - 1])) {
            result.put("dec", "检测不是excel文件");
            result.put("status", "0");
            return result;
        }
        Workbook workBook = Workbook.getWorkbook(file);
        //2、获取工作表中的第一页（sheet1）
        Sheet sheet1 = workBook.getSheet(0);
        int colindex = -1;
        for (int i = 0; i < sheet1.getColumns(); i++) {
            Cell cell = sheet1.getCell(i, 0);
            String value = cell.getContents().trim();
            name = name.trim();
            if (value.equals(name)) {
                colindex = i;
            }
        }
        if (colindex == -1) {
            result.put("dec", "未获取到配置字段");
            result.put("status", "0");
            return result;
        }
        for (int i = 1; i < sheet1.getRows(); i++) {
            Cell cell = sheet1.getCell(colindex, i);
            String contents = cell.getContents();
            new BaseBean().writeLog(contents);
            RecordSet rs = new RecordSet();
            boolean sel = rs.execute("select id as crmtype from crm_customertype where fullname = '" + contents.trim() + "'");
            new BaseBean().writeLog(sel + "select id as crmtype from crm_customertype where fullname = '" + contents.trim() + "'");
            if (!sel) {
                result.put("dec", "无需校验" + "select id as crmtype from crm_customertype where fullname = '" + contents.trim() + "'");
                result.put("status", "1");
                return result;
            }
            if (rs.next()) {
                String crmtype = rs.getString("crmtype");
                RecordSet recordSet = new RecordSet();
                String sql = "select kcjbm,khlx from uf_khlxcjqx where khlx ='" + crmtype + "' and  sfqyck='0' and ','+cast(kcjbm as varchar)+',' like '%," + depid + ",%' union all select kcjbm,khlx from uf_khlxcjqx where khlx ='" + crmtype + "' and sfqyck='1' ";
                boolean flag = recordSet.execute(sql);
                if (flag) {
                    if (!recordSet.next()) {
                        result.put("dec", "您无权限创建客户类型为“+" + contents + "+”的客户");
                        result.put("status", "0");
                        return result;
                    }
                } else {
                    result.put("dec", recordSet.getExceptionMsg() + sql);
                    result.put("status", "0");
                    return result;
                }
            }
        }
        result.put("dec", "success");
        result.put("status", "1");
        return result;
    }

}
