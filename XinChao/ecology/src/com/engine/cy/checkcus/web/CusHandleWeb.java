package com.engine.cy.checkcus.web;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.cy.checkcus.service.CuscheckHandleService;
import com.engine.cy.checkcus.service.imp.CuscheckHandleServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName SqlHandleWeb
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/20
 */
public class CusHandleWeb {

    private CuscheckHandleService getService(User user) {
        return ServiceUtil.getService(CuscheckHandleServiceImpl.class, user);
    }


    /**
     * 查询sql接口
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/check")
    @Produces(MediaType.TEXT_PLAIN)
    public String check(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "saveOrUpdate---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).CusCheck(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "saveOrUpdate，result为：" + result);
        return JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue);
    }
}
