package com.engine.cy.checkcus.service.imp;

import com.engine.core.impl.Service;
import com.engine.cy.checkcus.cmd.CusCheckCmd;
import com.engine.cy.checkcus.service.CuscheckHandleService;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName SqlHandleServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/20
 */
public class CuscheckHandleServiceImpl extends Service implements CuscheckHandleService {

    @Override
    public Map<String, Object> CusCheck(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CusCheckCmd(params, user));
    }

}
  