package com.engine.gyl.report.crm.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName CrmReportService
 * @Description CRM报表
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/28
 */
public interface CrmReportService {
    /**
     * 获取权限
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getAuthority(Map<String, Object> params, User user);

    /**
     * 查询客户
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> queryCustomer(Map<String, Object> params, User user);

    Map<String, Object> queryContacts(Map<String, Object> params, User user);

    Map<String, Object> queryBusinessNumber(Map<String, Object> params, User user);

    Map<String, Object> queryContractNumber(Map<String, Object> params, User user);

    Map<String, Object> queryBusinessAmount(Map<String, Object> params, User user);

    Map<String, Object> queryContractAmount(Map<String, Object> params, User user);

    Map<String, Object> queryKaipiaoAmount(Map<String, Object> params, User user);

    Map<String, Object> queryDaokuanAmount(Map<String, Object> params, User user);

}
