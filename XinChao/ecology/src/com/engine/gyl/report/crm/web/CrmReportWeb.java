package com.engine.gyl.report.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.gyl.report.crm.service.CrmReportService;
import com.engine.gyl.report.crm.service.impl.CrmReportServiceImpl;
import com.engine.parent.common.util.SDUtil;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName CrmReportWeb
 * @Description CRM报表
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/28
 */
public class CrmReportWeb {
    private CrmReportService getService(User user) {
        return ServiceUtil.getService(CrmReportServiceImpl.class, user);
    }

    /**
     * 获取默认有的权限
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/getAuthority")
    @Produces(MediaType.TEXT_PLAIN)
    public String getAuthority(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getAuthority(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询客户数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryCustomer")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryCustomer(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryCustomer(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询联系人数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryContacts")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryContacts(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryContacts(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询商机数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryBusinessNumber")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryBusinessNumber(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryBusinessNumber(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询合同数数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryContractNumber")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryContractNumber(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryContractNumber(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询商机预期收益金额数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryBusinessAmount")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryBusinessAmount(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryBusinessAmount(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询合同总金额数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryContractAmount")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryContractAmount(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryContractAmount(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询开票总金额数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryKaipiaoAmount")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryKaipiaoAmount(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryKaipiaoAmount(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询到款总金额数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/queryDaokuanAmount")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryDaokuanAmount(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryDaokuanAmount(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", SDUtil.getExceptionDetail(ex));
        }
        return JSONObject.toJSONString(result);
    }


}
