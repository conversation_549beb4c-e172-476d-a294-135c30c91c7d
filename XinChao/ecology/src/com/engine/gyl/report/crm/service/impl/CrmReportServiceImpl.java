package com.engine.gyl.report.crm.service.impl;


import com.engine.core.impl.Service;
import com.engine.gyl.report.crm.cmd.*;
import com.engine.gyl.report.crm.service.CrmReportService;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName CrmReportServiceImpl
 * @Description CRM报表
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/29
 */
public class CrmReportServiceImpl extends Service implements CrmReportService {
    @Override
    public Map<String, Object> getAuthority(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetCrmReportAuthorityCmd(params, user));
    }

    @Override
    public Map<String, Object> queryCustomer(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmCustomerCmd(params, user));
    }

    @Override
    public Map<String, Object> queryContacts(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmContactsCmd(params, user));
    }

    @Override
    public Map<String, Object> queryBusinessNumber(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmBusinessNumberCmd(params, user));
    }

    @Override
    public Map<String, Object> queryContractNumber(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmContractNumberCmd(params, user));
    }

    @Override
    public Map<String, Object> queryBusinessAmount(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmBusinessAmountCmd(params, user));
    }

    @Override
    public Map<String, Object> queryContractAmount(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmContractAmountCmd(params, user));
    }

    @Override
    public Map<String, Object> queryKaipiaoAmount(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmKaipiaoAmountCmd(params, user));
    }

    @Override
    public Map<String, Object> queryDaokuanAmount(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCrmDaokuanAmountCmd(params, user));
    }
}
