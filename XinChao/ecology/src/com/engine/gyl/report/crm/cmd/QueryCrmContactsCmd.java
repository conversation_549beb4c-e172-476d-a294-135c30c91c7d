package com.engine.gyl.report.crm.cmd;

import com.alibaba.fastjson.JSONArray;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.gyl.report.crm.dto.CrmReportCondition;
import com.engine.gyl.report.crm.util.CrmReportUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName QueryCrmContactsCmd
 * @Description 查询Crm联系人
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/28
 */
public class QueryCrmContactsCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;

    public QueryCrmContactsCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "----START");
        Map<String, Object> result = new HashMap<>();
        result.put("status", "1");
        bb.writeLog("params:" + params);
        //step 1: 查询是否有配置所有权限
        CrmReportCondition condtion = CrmReportCondition.packageParam(
                Util.null2String(params.get("condition")),
                Util.null2String(params.get("auth")));
        bb.writeLog("condtion:" + condtion);
        //step 2: 查询pie数据
        JSONArray pieData = queryPieData(condtion);
        result.put("pieData", queryPieData(condtion));
        //step 3: 计算总数
        BigDecimal totalNum = BigDecimal.valueOf(0);
        if (!pieData.isEmpty()) {
            for (int i = 0; i < pieData.size(); i++) {
                totalNum = totalNum.add(SDUtil.getBigDecimalValue(pieData.getJSONObject(i).get("value")));
            }
        }
        result.put("totalNum", totalNum);
        //step 4: 查询同比环比数据
        result.putAll(queryCompareData(condtion));
        return result;
    }

    /**
     * 查询pie图标数据
     *
     * @param condtion
     * @return
     */
    private JSONArray queryPieData(CrmReportCondition condtion) {
        String baseSql = " select " +
                " (case when a.lxrzt  = '1' or a.lxrzt is null then '在职' " +
                " when a.lxrzt  = '2' then '离职' else '其他' end) as name, " +
                " count(a.id) as value " +
                " from CRM_CustomerContacter a " +
                " left join HrmResource b on (a.createrid = b.id) " +
                " where 1=1 ";

        StringBuilder sb = new StringBuilder();
        sb.append(baseSql);
        sb.append(CrmReportUtil.appendAuthSql(condtion, "b.departmentid", "b.id"));
        //根据选择的条件拼接sql
        //年份和月份都有
        if (!condtion.getYearMonth().isEmpty()) {
            sb.append(" and (left(a.createdate,7) = '").append(condtion.getYearMonth()).append("' ) ");
        }
        //只有年份
        if (!condtion.getYear().isEmpty() && condtion.getMonth().isEmpty()) {
            sb.append(" and (left(a.createdate,4) = '").append(condtion.getYear()).append("' ) ");
        }
        sb.append(" group by a.lxrzt ");
        bb.writeLog("queryPieData sql :" + sb);
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sb.toString())) {
            return QueryUtil.getJSONList(rs);
        }
        return new JSONArray();
    }

    /**
     * 查询同比环比数据
     *
     * @param condtion
     * @return
     */
    private Map<String, String> queryCompareData(CrmReportCondition condtion) {
        Map<String, String> result = new HashMap<>();
        BigDecimal currentCnt, lastYearCnt, lastMonthCnt;
        BigDecimal tongbi;
        BigDecimal huanbi;
        result.put("tongbi", "0");
        result.put("huanbi", "0");
        //只选年 同比: 比一年数据，和去年比， 环比为0
        if (!condtion.getYear().isEmpty() && condtion.getMonth().isEmpty()) {
            RecordSet rs = new RecordSet();
            //选择年的上一年
            int lastYear = Integer.parseInt(condtion.getYear()) - 1;
            String sql = "  select  " +
                    " count( " +
                    " case when left(a.createdate,4) = '" + condtion.getYear() + "' then a.id " +
                    " else null end " +
                    " ) as current_cnt, " +
                    "  count( " +
                    " case when left(a.createdate,4) = '" + lastYear + "' then a.id " +
                    " else null end " +
                    " ) as last_cnt " +
                    "  from CRM_CustomerContacter a " +
                    " left join HrmResource b on (a.createrid = b.id) " +
                    " where 1=1 ";
            sql += CrmReportUtil.appendAuthSql(condtion, "b.departmentid", "b.id");
            bb.writeLog("queryCompareData sql1 :" + sql);
            if (rs.executeQuery(sql)) {
                if (rs.next()) {
                    currentCnt = SDUtil.getBigDecimalValue(rs.getString("current_cnt"));
                    lastYearCnt = SDUtil.getBigDecimalValue(rs.getString("last_cnt"));
                    //计算同比
                    tongbi = CrmReportUtil.calculateTongHuanBi(currentCnt, lastYearCnt);
                    result.put("tongbi", tongbi.toString());
                }
            }
        }
        //年和月都选 同比：比较当前选的年月和-上一个年月的比较
        //         环比：比较当前年月和-上个月的比较
        if (!condtion.getYearMonth().isEmpty()) {
            RecordSet rs = new RecordSet();
            //选择年的上一年
            int lastYear = Integer.parseInt(condtion.getYear()) - 1;
            //选择年上一年的同月
            String lastYearMonth = lastYear + "-" + condtion.getMonth();
            //获取上个月
            String lastMonth = CrmReportUtil.getLastMonth(condtion.getYearMonth());
            //当前年月
            String sql = "  select  " +
                    " count( " +
                    " case when left(a.createdate,7) = '" + condtion.getYearMonth() + "' then a.id " +
                    " else null end " +
                    " ) as current_year_cnt, " +
                    "  count( " +
                    " case when left(a.createdate,7) = '" + lastYearMonth + "' then a.id " +
                    " else null end " +
                    " ) as last_year_cnt, " +
                    "   count( " +
                    " case when left(a.createdate,7) = '" + lastMonth + "' then a.id " +
                    " else null end " +
                    " ) as last_month_cnt " +
                    "  from CRM_CustomerContacter a " +
                    "  left join HrmResource b on (a.createrid = b.id) " +
                    "  where 1=1 ";
            sql += CrmReportUtil.appendAuthSql(condtion, "b.departmentid", "b.id");
            bb.writeLog("queryCompareData sql2 :" + sql);
            if (rs.executeQuery(sql)) {
                if (rs.next()) {
                    currentCnt = SDUtil.getBigDecimalValue(rs.getString("current_year_cnt"));
                    lastYearCnt = SDUtil.getBigDecimalValue(rs.getString("last_year_cnt"));
                    lastMonthCnt = SDUtil.getBigDecimalValue(rs.getString("last_month_cnt"));
                    //计算同比
                    tongbi = CrmReportUtil.calculateTongHuanBi(currentCnt, lastYearCnt);
                    //计算环比
                    huanbi = CrmReportUtil.calculateTongHuanBi(currentCnt, lastMonthCnt);
                    result.put("tongbi", tongbi.toString());
                    result.put("huanbi", huanbi.toString());
                }
            }
        }
        bb.writeLog("queryCompareData", result);
        return result;
    }


}
