package com.engine.gyl.report.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName GetCrmReportAuthorityCmd
 * @Description 获取CRM报表默认有的权限
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/28
 */
public class GetCrmReportAuthorityCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;

    public GetCrmReportAuthorityCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "1");
        //step 1: 查询是否有配置所有权限
        if (checkHasAll()) {
            result.put("hasAll", "1");
        } else {
            //step 2: 查询当前人部门矩阵有该人的所有部门
            result.put("deptAuth", getDept());
            //step 3: 查询当前人所有下级人员
            result.put("userAuth", getAllUser());
        }
        return result;
    }

    /**
     * 检查是否有所有权限
     *
     * @return
     */
    private boolean checkHasAll() {
        boolean result = false;
        String xmStr;
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_bbckqx ";
        bb.writeLog("checkHasAll sql:" + sql);
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                xmStr = Util.null2String(rs.getString("xm"));
                String[] xmArray = xmStr.split(CommonCst.COMMA_EN);
                for (String str : xmArray) {
                    if (str.equals(String.valueOf(user.getUID()))) {
                        result = true;
                        break;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 查询部门矩阵，有当前人的所有部门
     *
     * @return
     */
    private String getDept() {
        String deptStr = "";
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        //查询【客户报表查看】字段khbbck有当前人的所有部门列表
        String sql = "select id,khbbck as ry from matrixtable_2 where ',' + khbbck + ',' LIKE '%,' + '" + user.getUID() + "' + ',%' ";
        bb.writeLog("getDept sql:" + sql);
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                sb.append(Util.null2String(rs.getString("id"))).append(CommonCst.COMMA_EN);
            }
        }
        if (!sb.toString().isEmpty()) {
            deptStr = sb.substring(0, sb.length() - 1);
        }
        return deptStr;
    }

    /**
     * 查询所有下级人员
     *
     * @return
     */
    private String getAllUser() {
        String userStr = "";
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        String sql = "WITH  subordinates AS ( " +
                "  SELECT id, lastname, managerid " +
                "  FROM HrmResource " +
                "  WHERE id = ? " +
                "  and status < 4 " +
                "  UNION ALL " +
                "  SELECT e.id, e.lastname, e.managerid " +
                "  FROM HrmResource e " +
                "  JOIN subordinates s ON s.id = e.managerid " +
                "   where  e.status < 4 " +
                ") " +
                "SELECT * " +
                "FROM subordinates ";
        if (rs.executeQuery(sql, user.getUID())) {
            while (rs.next()) {
                sb.append(Util.null2String(rs.getString("id"))).append(CommonCst.COMMA_EN);
            }
        }
        if (!sb.toString().isEmpty()) {
            userStr = sb.substring(0, sb.length() - 1);
        }
        return userStr;
    }
}
