package com.engine.gyl.report.crm.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import weaver.general.Util;

/**
 * @FileName CrmReportCondition
 * @Description CRM报表条件类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/29
 */
@Data
public class CrmReportCondition {
    //选择的年
    private String year;
    //选择的月
    private String month;
    //选择的年和月
    private String yearMonth;
    //选择的多部门
    private String dept;
    //选择的多人力
    private String user;
    //是否有所有权限
    private String hasAll;
    //部门权限
    private String deptAuth;
    //人员权限
    private String userAuth;

    /**
     * 组装参数
     *
     * @param condition
     * @param auth
     * @return
     */
    public static CrmReportCondition packageParam(String condition, String auth) {
        CrmReportCondition ct = new CrmReportCondition();
        try {
            if (StringUtils.isNotBlank(condition)) {
                JSONObject conditionJSON = JSONObject.parseObject(condition);
                String yearStr = Util.null2String(conditionJSON.get("year"));
                ct.setYear(yearStr);
                String monthStr = Util.null2String(conditionJSON.get("month"));
                ct.setMonth(monthStr);
                if (!monthStr.isEmpty()) {
                    ct.setMonth(String.format("%02d", Integer.parseInt(monthStr)));
                }
                if (!yearStr.isEmpty() && !monthStr.isEmpty()) {
                    ct.setYearMonth(yearStr + "-" + String.format("%02d", Integer.parseInt(monthStr)));
                } else {
                    ct.setYearMonth("");
                }
                ct.setDept(Util.null2String(conditionJSON.get("dept")));
                ct.setUser(Util.null2String(conditionJSON.get("user")));

            }
            if (StringUtils.isNotBlank(auth)) {
                JSONObject authJSON = JSONObject.parseObject(auth);
                ct.setHasAll(Util.null2String(authJSON.get("hasAll")));
                ct.setDeptAuth(Util.null2String(authJSON.get("deptAuth")));
                ct.setUserAuth(Util.null2String(authJSON.get("userAuth")));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return ct;

    }
}
