package com.engine.gyl.report.crm.util;

import com.engine.gyl.report.crm.dto.CrmReportCondition;
import com.engine.parent.common.constant.CommonCst;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @FileName CrmReportUtil
 * @Description CrmReportUtil工具类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/30
 */
public class CrmReportUtil {
    /**
     * 拼接权限sql
     *
     * @param condtion
     * @param deptField
     * @param userField
     * @return
     */
    public static String appendAuthSql(CrmReportCondition condtion, String deptField, String userField) {
        StringBuilder sb = new StringBuilder();
        //没有所有权限, 则需要限定 部门或者人员范围
        if (!"1".equals(condtion.getHasAll())) {
            sb.append("and ( ");

            if (condtion.getDeptAuth().isEmpty()) {
                sb.append(" (").append(deptField).append(" = -1) ");
            } else {
                String[] deptAuthArray = condtion.getDeptAuth().split(CommonCst.COMMA_EN);
                sb.append(appendOrSql(deptAuthArray, deptField));
            }

            sb.append(" or ");

            if (condtion.getUserAuth().isEmpty()) {
                sb.append(" (").append(userField).append(" = -1) ");
            } else {
                String[] userAuthArray = condtion.getUserAuth().split(CommonCst.COMMA_EN);
                sb.append(appendOrSql(userAuthArray, userField));

            }

            sb.append(" ) ");
        }

        //有选择部门
        if (!condtion.getDept().isEmpty()) {
            sb.append(" and ");
            sb.append(appendOrSql(condtion.getDept().split(CommonCst.COMMA_EN), deptField));

        }
        //有选择人员
        if (!condtion.getUser().isEmpty()) {
            sb.append(" and ");
            sb.append(appendOrSql(condtion.getUser().split(CommonCst.COMMA_EN), userField));
        }
        return sb.toString();
    }

    /**
     * 获取上个月
     *
     * @param yearMonth 例如: 2023-02
     * @return 2023-01
     */
    public static String getLastMonth(String yearMonth) {
        //将当前年月改为1号
        String dateStr = yearMonth + "-01";
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate previousMonth = date.minusMonths(1); // 上个月
        return previousMonth.format(DateTimeFormatter.ISO_LOCAL_DATE).substring(0, 7);
    }

    /**
     * 拼接or的sql
     *
     * @param strArray
     * @param fieldName
     * @return
     */
    public static String appendOrSql(String[] strArray, String fieldName) {
        StringBuilder sb = new StringBuilder();
        if (strArray.length > 0) {
            sb.append(" ( ");
            for (int i = 0; i < strArray.length; i++) {
                if (i > 0) {
                    sb.append(" or ");
                }
                sb.append(" (").append(fieldName).append(" = '").append(strArray[i]).append("') ");
            }
            sb.append(" ) ");
        }
        return sb.toString();
    }

    /**
     * 计算同比环比
     *
     * @param currentNum
     * @param lastNum
     * @return
     */
    public static BigDecimal calculateTongHuanBi(BigDecimal currentNum, BigDecimal lastNum) {
        if (lastNum.compareTo(BigDecimal.valueOf(0)) == 0) {
            return BigDecimal.valueOf(0);
        }
        return (currentNum.subtract(lastNum)).divide(lastNum, 2, RoundingMode.HALF_UP);

    }

}
