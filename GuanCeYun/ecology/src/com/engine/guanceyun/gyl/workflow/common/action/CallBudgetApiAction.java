package com.engine.guanceyun.gyl.workflow.common.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudstore.dev.api.util.Util_DataCache;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.parent.workflow.util.WfUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @FileName CallBudgetApiAction.java
 * @Description 调用预算接口，将预算信息回写到流程
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/8
 */
@Getter
@Setter
public class CallBudgetApiAction extends BaseBean implements Action {
    //---Action参数---
    /**
     * 功能列表的功能id
     */
    private String functionId;
    //---Action参数---

    /**
     * 设置token缓存的名称
     */
    private static final String CACHE_TOKEN_NAME = "YKB_TOKEN";
    /**
     * 参数名：errorMsg
     */
    private static final String VARIABLE_ERROR_MSG = "errorMsg";
    /**
     * 参数名：accessToken
     */
    private static final String VARIABLE_TOKEN = "accessToken";

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //出错信息
        //获取action相关信息
        ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
        executeData(actionInfo.getRequestId(), functionId);
        writeLog(this.getClass().getName() + "---END");
        //这里默认无失败
        return ActionUtil.handleResult("", requestInfo);
    }

    /**
     * 实际执行
     *
     * @param requestid
     * @param functionId
     * @return
     */
    public String executeData(String requestid, String functionId) {
        writeLog("requestid:" + requestid);
        writeLog("functionId:" + functionId);
        // 使用ConcurrentLinkedQueue来收集错误信息
        String errorMsg = "";
        try {

            //step 1 : 根据requestid获取流程相关信息
            WfInfo wfInfo = WfUtil.getWfInfoByReqId(requestid);
            writeLog("wfInfo:" + wfInfo);
            if (wfInfo.getFormtableName().isEmpty()) {
                errorMsg = "未获取到requestid:" + requestid + "的相关流程信息";
            } else {
                //step 2: 获取主表数据
                RecordSet rs = new RecordSet();
                if (!rs.executeQuery("select * from " + wfInfo.getFormtableName() + " where requestid = ?", requestid)) {
                    errorMsg = "查询流程主表数据出错:" + rs.getExceptionMsg();
                } else {
                    Map<String, Object> mainData = QueryUtil.getMap(rs);
                    writeLog("mainData:" + mainData);
                    //step 3 : 根据workflowid，functionid，读取配置
                    JSONObject config = WfConfigUtil.getJSONObjectConfig(functionId, wfInfo.getWorkflowId());
                    writeLog("config:" + config);
                    //step 4: 校验配置
                    String checkErrorMsg = checkConfig(config);
                    if (checkErrorMsg.isEmpty()) {
                        //STEP 5 : 使用newFixedThreadPool线程池，多线程处理每一组数据
                        JSONArray detailConfig = config.getJSONArray("config");
                        writeLog("detailConfig:" + detailConfig);
                        ExecutorService executorService = Executors.newFixedThreadPool(5);
                        CountDownLatch latch = new CountDownLatch(detailConfig.size());
                        for (int i = 0; i < detailConfig.size(); i++) {
                            JSONObject eachConfig = detailConfig.getJSONObject(i);
                            executorService.submit(() -> {
                                try {
                                    //step 6:处理每一组数据
                                    process(mainData, wfInfo.getFormtableName(), config, eachConfig);
                                } finally {
                                    latch.countDown(); // 线程完成任务后减少计数器
                                }
                            });
                        }
                        try {
                            // 等待所有线程完成任务，超时30分钟
                            if (!latch.await(30, TimeUnit.MINUTES)) {
                                errorMsg = "超时30分钟未执行完所有线程任务";
                            }
                        } catch (InterruptedException e) {
                            errorMsg = "latch.await() 出错：" + SDUtil.getExceptionDetail(e);
                        } finally {
                            executorService.shutdown();
                        }
                    } else {
                        errorMsg = "校验配置出错：" + checkErrorMsg;
                    }
                }
            }
        } catch (Exception e) {
            errorMsg = "executeData 异常：" + SDUtil.getExceptionDetail(e);
        }
        writeLog("executeData errorMsg:" + errorMsg);
        return errorMsg;
    }

    /**
     * 自定义处理每一组配置的预算数据
     *
     * @param mainData
     * @param mainTableName
     * @param config
     * @param eachConfig
     * @return
     */
    private void process(Map<String, Object> mainData, String mainTableName, JSONObject config, JSONObject eachConfig) {
        writeLog("处理当前组数据，当前配置为：" + eachConfig);
        String errorMsg = "";
        String budgetId, budgetVersion;
        JSONObject jo;
        JSONObject esbParam;
        EsbEventResult er;
        String apiName, apiAnnual, apiActive;
        String updateErrorSql = "";
        String field_error_msg = Util.null2String(eachConfig.get("field_error_msg"));
        String field_error_flag = Util.null2String(eachConfig.get("field_error_flag"));
        JSONArray write_back_config = eachConfig.getJSONArray("write_back_config");
        String mainId = Util.null2String(mainData.get("id"));
        String index = Util.null2String(eachConfig.get("index"));
        try {
            // 在这里执行你的业务逻辑
            String esb_token = Util.null2String(config.get("esb_token"));
            String esb_budgets = Util.null2String(config.get("esb_budgets"));
            String budgets_param_active = Util.null2String(config.get("budgets_param_active"));
            String field_annual = Util.null2String(config.get("field_annual"));
            String field_annual_value = Util.null2String(mainData.get(field_annual));
            writeLog("field_annual_value:" + field_annual_value);

            String budgets_param_name = Util.null2String(eachConfig.get("budgets_param_name"));

            //如果是明细表配置，则这个是明细表名
            String detailTableName = mainTableName + "_dt" + index;

            // step 1 ：先将所有标志位清空
            RecordSet rs = new RecordSet();
            if ("0".equals(index)) {
                updateErrorSql = " update " + mainTableName + " set ";
                if (!rs.executeUpdate("update " + mainTableName + " set " + field_error_flag + " = null," + field_error_msg + "=null where id = ?", mainId)) {
                    errorMsg = "清空主表预算标识失败：" + rs.getExceptionMsg();
                }
            } else {
                updateErrorSql = " update " + detailTableName + " set ";
                if (!rs.executeUpdate("update " + detailTableName + " set " + field_error_flag + " = null," + field_error_msg + "=null where mainid = ?", mainId)) {
                    errorMsg = "清空明细表预算标识失败：" + rs.getExceptionMsg();
                }
            }
            if (errorMsg.isEmpty()) {
                // step 2: 调ESB接口，获取预算包列表
                esbParam = new JSONObject();
                //step 1： 获取获取预算包列表，匹配所有到对应的预算id
                er = callEsbEvent(esb_token, esb_budgets, esbParam);
                writeLog("EsbEventResult:" + er);
                //是否有匹配搭配的预算包
                boolean hasMatchedOne = false;
                if (er.isSuccess()) {
                    JSONArray items = er.getData().getJSONArray("items");
                    writeLog("预算包items:" + items);
                    //匹配的预算包名称可能有多个，多个时，要获取多个预算
                    for (int i = 0; i < items.size(); i++) {
                        jo = items.getJSONObject(i);
                        apiName = Util.null2String(jo.get("name"));
                        apiActive = Util.null2String(jo.get("active"));
                        apiAnnual = Util.null2String(jo.getJSONObject("period").get("annual"));
                        writeLog("apiName:" + apiName);
                        writeLog("apiActive:" + apiActive);
                        writeLog("apiAnnual:" + apiAnnual);

                        //按照name包含，周期匹配，active匹配，找到对应的budgetId和budgetVersion
                        //是否匹配到budgetid，默认没有
                        boolean matchedBudgetId = false;
                        if (checkName(budgets_param_name, apiName) && field_annual_value.equals(apiAnnual)) {
                            budgetId = Util.null2String(jo.get("id"));
                            budgetVersion = Util.null2String(jo.get("version"));
                            //active 参数，如果不配则不校验active参数
                            if (!budgets_param_active.isEmpty()) {
                                if (budgets_param_active.equals(apiActive)) {
                                    matchedBudgetId = true;
                                }
                            } else {
                                matchedBudgetId = true;
                            }
                            if (matchedBudgetId) {
                                hasMatchedOne = true;
                                writeLog("匹配到的预算包budgetId：" + budgetId);
                                //step 2: 执行每个配置详细方法， 获取预算节点配置和路径信息，并回写预算数据
                                ConcurrentLinkedQueue<String> updateSqls = handleOneConfig(mainId, mainTableName, config, eachConfig, budgetId, budgetVersion);
                                writeLog("process updateSqls:" + updateSqls);
                                if (!updateSqls.isEmpty()) {
                                    //step 7: 执行所有需要更新的update sql
                                    RecordSetTrans rst = new RecordSetTrans();
                                    rst.setAutoCommit(false);
                                    try {
                                        for (String sql : updateSqls) {
                                            if (!rst.executeUpdate(sql)) {
                                                rst.rollback();
                                                errorMsg = "执行最终返写sql出错：" + rst.getMsg();
                                                break;
                                            }
                                        }
                                        if (errorMsg.isEmpty()) {
                                            rst.commit();
                                        }
                                    } catch (Exception e) {
                                        rst.rollback();
                                        errorMsg = "执行最终返写sql异常：" + SDUtil.getExceptionDetail(e);

                                    }
                                }
                            }
                        }
                    }
                } else {
                    errorMsg = "获取预算包列表接口出错：" + er.getErroMsg();
                }
                if (!hasMatchedOne) {
                    errorMsg = "当前配置未匹配到预算包";
                }
            }

        } catch (Exception e) {
            errorMsg = "process处理异常：" + SDUtil.getExceptionDetail(e);
        }

        if (!errorMsg.isEmpty()) {
            writeLog("process errorMsg:" + errorMsg);
            RecordSet rs = new RecordSet();
            //因为流程字段是单行文本设置的999，这里做下截取
            // 确保 endIndex 不超过字符串长度
            int endIndex = Math.min(999, errorMsg.length());
            errorMsg = errorMsg.substring(0, endIndex);
            //如果有出错，则将预算都回写为0，并更新错误信息
            updateErrorSql += field_error_flag + " = 1, ";
            if (!write_back_config.isEmpty()) {
                updateErrorSql += generateUpdateZeroSql(write_back_config) + ",";
            }

            if ("0".equals(index)) {
                updateErrorSql += field_error_msg + " = '" + errorMsg + "' where id = " + mainId;
            } else {
                updateErrorSql += field_error_msg + " = '" + errorMsg + "' where mainid = " + mainId;
            }
            writeLog("process updateErrorSql:" + updateErrorSql);
            if (!rs.executeUpdate(updateErrorSql)) {
                writeLog("process updateErrorSql error:" + rs.getExceptionMsg());
            }
        }

    }

    /**
     * 校验name是否匹配，只要包含就可以
     *
     * @param budgets_param_name
     * @param apiName
     * @return
     */
    private boolean checkName(String budgets_param_name, String apiName) {
        String[] nameArray = budgets_param_name.split(CommonCst.COMMA_EN);
        for (String name : nameArray) {
            if (apiName.contains(name)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理一组配置数据
     *
     * @param mainId
     * @param mainTableName
     * @param config
     * @param eachConfig
     * @param budgetId
     * @param budgetVersion
     * @return
     */
    private ConcurrentLinkedQueue<String> handleOneConfig(String mainId,
                                                          String mainTableName,
                                                          JSONObject config,
                                                          JSONObject eachConfig,
                                                          String budgetId,
                                                          String budgetVersion) {
        // 使用ConcurrentLinkedQueue来收集sql列表
        ConcurrentLinkedQueue<String> updateSqls = new ConcurrentLinkedQueue<>();
        try {
            writeLog("handleOneConfig ---START");
            String field_error_flag = Util.null2String(eachConfig.get("field_error_flag"));
            String index = Util.null2String(eachConfig.get("index"));
            writeLog("index:" + index);
            writeLog("mainId:" + mainId);
            //主表
            if ("0".equals(index)) {
                //step 2: 获取主表数据
                //成功的跳过
                RecordSet rs = new RecordSet();
                if (!rs.executeQuery("select * from " + mainTableName +
                        " where (" + field_error_flag + " is null or " + field_error_flag + " = '1') " +
                        " and id = ?", mainId)) {
                    writeLog("查询主表数据出错:" + rs.getExceptionMsg());
                } else {
                    Map<String, Object> mainData = QueryUtil.getMap(rs);
                    //主表信息
                    String updateSql = processDetailData(
                            mainTableName,
                            mainData,
                            config,
                            eachConfig,
                            budgetId,
                            budgetVersion);
                    updateSqls.add(updateSql);
                }

            } else {
                //明细表
                RecordSet rs = new RecordSet();
                String detailTableName = mainTableName + "_dt" + index;
                //获取不是成功的数据
                if (rs.executeQuery("select * from " + detailTableName +
                        " where (" + field_error_flag + " is null or " + field_error_flag + " = '1') " +
                        " and mainid = ?", mainId)) {
                    List<Map<String, Object>> wfDetailData = QueryUtil.getMapList(rs);
                    if (!wfDetailData.isEmpty()) {
                        //明细表每行数据都要执行,每行数据使用线程池执行
                        //STEP 2 : 使用newFixedThreadPool线程池，多线程处理每一行明细数据, 使用CountDownLatch
                        ExecutorService executorService = Executors.newFixedThreadPool(5);
                        CountDownLatch latch = new CountDownLatch(wfDetailData.size());
                        for (Map<String, Object> eachDetail : wfDetailData) {
                            executorService.submit(() -> {
                                try {
                                    //处理明细的每一行数据
                                    String updateSql = processDetailData(
                                            detailTableName,
                                            eachDetail,
                                            config,
                                            eachConfig,
                                            budgetId,
                                            budgetVersion
                                    );
                                    updateSqls.add(updateSql);
                                } finally {
                                    latch.countDown(); // 线程完成任务后减少计数器
                                }
                            });
                        }
                        try {
                            // 等待所有线程完成任务，超时10分钟
                            if (!latch.await(10, TimeUnit.MINUTES)) {
                                writeLog("超时未执行完所有线程任务，请检查，当前配置为：" + eachConfig);
                            }
                        } catch (InterruptedException e) {
                            writeLog("latch.await() 出错：" + SDUtil.getExceptionDetail(e));
                        } finally {
                            executorService.shutdown();
                        }
                    } else {
                        writeLog("当前明细无数据，跳过执行当前组,当前配置为：" + eachConfig);
                    }

                } else {
                    writeLog("查询明细表数据出错：" + rs.getExceptionMsg() + ",当前配置为：" + eachConfig);
                }
            }
        } catch (Exception e) {
            writeLog("handleOneConfig 异常：" + SDUtil.getExceptionDetail(e));
        }

        return updateSqls;
    }

    /**
     * 处理具体的每一条数据，获取最终的更新sql
     *
     * @param tableName
     * @param currentData
     * @param config
     * @param currentConfig
     * @param budgetId
     * @param budgetVersion
     * @return
     */
    private String processDetailData(String tableName,
                                     Map<String, Object> currentData,
                                     JSONObject config,
                                     JSONObject currentConfig,
                                     String budgetId,
                                     String budgetVersion) {
        String updateSql = " update " + tableName + " set ";
        JSONObject eachMoneyExt, matchedMoneyExt = null;
        String errorMsg = "";
        JSONArray write_back_config = currentConfig.getJSONArray("write_back_config");
        String field_error_msg = Util.null2String(currentConfig.get("field_error_msg"));
        String field_error_flag = Util.null2String(currentConfig.get("field_error_flag"));
        String dataId = Util.null2String(currentData.get("id"));
        try {
            String esb_token = Util.null2String(config.get("esb_token"));
            String esb_budget_node = Util.null2String(config.get("esb_budget_node"));

            String field_period = Util.null2String(currentConfig.get("field_period"));
            String dimension_fields = Util.null2String(currentConfig.get("dimension_fields"));

            String[] dimension_fields_array = dimension_fields.split(CommonCst.COMMA_EN);
            String field_period_value = Util.null2String(currentData.get(field_period));

            writeLog("field_period_value:" + field_period_value);
            if (field_period_value.isEmpty()) {
                errorMsg = "周期字段：" + field_period + "，存在空值";
            } else {
                if (write_back_config.isEmpty()) {
                    errorMsg = "回写字段未配置";
                } else {
                    //所有维度字段值拼接
                    StringBuilder allDimension = new StringBuilder();
                    for (String dimensionField : dimension_fields_array) {
                        if (!Util.null2String(currentData.get(dimensionField)).isEmpty()) {
                            allDimension.append(currentData.get(dimensionField));
                        } else {
                            errorMsg = "维度字段：" + dimensionField + "，存在空值！";
                            break;
                        }
                    }
                    if (errorMsg.isEmpty()) {
                        //节点编码
                        String nodeCode = allDimension.toString();
                        JSONObject esbParam = new JSONObject();
                        esbParam.put("budgetId", budgetId);
                        esbParam.put("budgetVersion", budgetVersion);
                        esbParam.put("code", nodeCode);
                        EsbEventResult er = callEsbEvent(esb_token, esb_budget_node, esbParam);
                        if (er.isSuccess()) {
                            //如果只有0条数据，ESB处理为失败
                            //匹配只取第一条
                            JSONObject itemsFirst = er.getData().getJSONArray("items").getJSONObject(0);
                            writeLog("itemsFirst:" + itemsFirst);
                            JSONObject budgetNodeEntity = itemsFirst.getJSONObject("budgetNodeEntity");
                            writeLog("budgetNodeEntity:" + budgetNodeEntity);
                            //取moneyExts参数节点
                            JSONArray moneyExts = budgetNodeEntity.getJSONArray("moneyExts");
                            writeLog("moneyExts:" + moneyExts);
                            if (!moneyExts.isEmpty()) {
                                for (int i = 0; i < moneyExts.size(); i++) {
                                    eachMoneyExt = moneyExts.getJSONObject(i);
                                    if (field_period_value.equals(Util.null2String(eachMoneyExt.get("periodTime")))) {
                                        matchedMoneyExt = eachMoneyExt;
                                        break;
                                    }
                                }
                                if (matchedMoneyExt == null) {
                                    errorMsg = "moneyExts参数节点未匹配到对应周期的预算数据，请检查，当前节点编码为：" + nodeCode;
                                } else {
                                    //根据匹配到的预算数据，根据要回写的配置，拼接update语句，并设置标识为成功
                                    updateSql += field_error_flag + " = 0, ";
                                    updateSql += generateUpdateSql(matchedMoneyExt, write_back_config) + " where id = " + dataId;
                                    writeLog("generateUpdateSql updateSql:" + updateSql);

                                }
                            } else {
                                errorMsg = "moneyExts参数节点数据为空，请检查，当前节点编码为：" + nodeCode;
                            }
                        } else {
                            errorMsg = er.getErroMsg();
                        }
                    }
                }

            }
        } catch (Exception e) {
            errorMsg = "processDetailData 异常：" + SDUtil.getExceptionDetail(e);
        }
        writeLog("processDetailData errorMsg:" + errorMsg);
        if (!errorMsg.isEmpty()) {
            //因为流程字段是单行文本设置的999，这里做下截取
            // 确保 endIndex 不超过字符串长度
            int endIndex = Math.min(999, errorMsg.length());
            errorMsg = errorMsg.substring(0, endIndex);
            //如果有出错，则将预算都回写为0，并更新错误信息
            updateSql += field_error_flag + " = 1, ";
            if (!write_back_config.isEmpty()) {
                updateSql += generateUpdateZeroSql(write_back_config) + ",";
            }
            updateSql += field_error_msg + " = '" + errorMsg + "' where id = " + dataId;
        }

        writeLog("final updateSql:" + updateSql);
        return updateSql;
    }

    /**
     * 生成更新sql set字段部分
     *
     * @param matchedMoneyExt
     * @param writeBackConfig
     * @return
     */
    private String generateUpdateSql(JSONObject matchedMoneyExt, JSONArray writeBackConfig) {
        StringBuilder sbUpdate = new StringBuilder();
        JSONObject jo;
        String api_field, wf_field;
        String api_field_value;
        for (int i = 0; i < writeBackConfig.size(); i++) {
            jo = writeBackConfig.getJSONObject(i);
            api_field = Util.null2String(jo.get("api_field"));
            wf_field = Util.null2String(jo.get("wf_field"));
            api_field_value = Util.null2String(matchedMoneyExt.get(api_field));
            sbUpdate.append(wf_field).append(" = ").append("'").append(api_field_value).append("' ");
            //最后一次遍历，不拼接逗号
            if (i != writeBackConfig.size() - 1) {
                sbUpdate.append(", ");
            }
        }
        return sbUpdate.toString();
    }

    /**
     * 生成更新sql set预算字段都为0，出错时候执行
     *
     * @param writeBackConfig
     * @return
     */
    private String generateUpdateZeroSql(JSONArray writeBackConfig) {
        StringBuilder sbUpdate = new StringBuilder();
        JSONObject jo;
        String wf_field;
        for (int i = 0; i < writeBackConfig.size(); i++) {
            jo = writeBackConfig.getJSONObject(i);
            wf_field = Util.null2String(jo.get("wf_field"));

            sbUpdate.append(wf_field).append(" = 0");
            //最后一次遍历，不拼接逗号
            if (i != writeBackConfig.size() - 1) {
                sbUpdate.append(", ");
            }
        }
        return sbUpdate.toString();
    }

    /**
     * 获取token
     *
     * @param esbTokenEvent
     * @return
     */
    private Map<String, String> getToken(String esbTokenEvent) {
        Map<String, String> result = new HashMap<>();
        String accessToken;
        try {
            //STEP 1: 获取jvm缓存里的token，获取到直接返回
            if (Util_DataCache.containsKey(CACHE_TOKEN_NAME)) {
                accessToken = Util.null2String(Util_DataCache.getObjVal(CACHE_TOKEN_NAME));
                result.put(VARIABLE_TOKEN, accessToken);
            } else {
                //从ESB接口拿token
                result = callEsbToken(esbTokenEvent);
                if (StringUtils.isBlank(result.get(VARIABLE_ERROR_MSG))) {
                    //将获取到的token放入本地jvm缓存
                    Util_DataCache.setObjVal(CACHE_TOKEN_NAME, result.get(VARIABLE_TOKEN));
                }
            }
        } catch (Exception e) {
            result.put(VARIABLE_ERROR_MSG, "获取token异常：" + SDUtil.getExceptionDetail(e));
        }

        return result;

    }


    /**
     * 调用ESB事件
     *
     * @param esbTokenEvent
     * @param esbEventName
     * @param esbParam
     * @return
     */
    private EsbEventResult callEsbEvent(String esbTokenEvent, String esbEventName, JSONObject esbParam) {
        EsbEventResult er = new EsbEventResult();
        Map<String, String> map;
        //step 1: 获取token
        String token;
        try {
            Map<String, String> mapToken = getToken(esbTokenEvent);
            writeLog("mapToken", mapToken);
            if (StringUtils.isNotBlank(mapToken.get(VARIABLE_ERROR_MSG))) {
                er.setSuccess(false);
                er.setErroMsg(mapToken.get(VARIABLE_ERROR_MSG));
            } else {
                token = mapToken.get(VARIABLE_TOKEN);
                //step 2 :调用ESB事件
                esbParam.put(VARIABLE_TOKEN, token);
                er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                if (er.isSuccess()) {
                    if ("授权已过期".equals(Util.null2String(er.getData()))) {
                        writeLog("授权过期，重新获取token");
                        //如果token过期，重新获取token
                        map = callEsbToken(esbTokenEvent);
                        if (StringUtils.isBlank(map.get(VARIABLE_ERROR_MSG))) {
                            //重新获取token后，再走一次业务接口,并放入缓存
                            //将获取到的token放入本地jvm缓存
                            Util_DataCache.setObjVal(CACHE_TOKEN_NAME, map.get(VARIABLE_TOKEN));
                            esbParam.put(VARIABLE_TOKEN, map.get(VARIABLE_TOKEN));
                            er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                        } else {
                            er.setSuccess(false);
                            er.setErroMsg("重新获取token失败：" + map.get(VARIABLE_ERROR_MSG));
                        }
                    }
                }
            }
        } catch (Exception e) {
            er.setSuccess(false);
            er.setErroMsg("callEsbEvent 异常：" + SDUtil.getExceptionDetail(e));
        }

        return er;
    }

    /**
     * 调用ESB token事件
     *
     * @param esbTokenEvent
     * @return
     */
    private Map<String, String> callEsbToken(String esbTokenEvent) {
        Map<String, String> result = new HashMap<>();
        EsbEventResult er = EsbUtil.callEsbEvent(esbTokenEvent, "");
        if (er.isSuccess()) {
            if (Util.null2String(er.getData().get(VARIABLE_TOKEN)).isEmpty()) {
                result.put(VARIABLE_ERROR_MSG, er.getData().toJSONString());
            } else {
                result.put(VARIABLE_TOKEN, Util.null2String(er.getData().get(VARIABLE_TOKEN)));
            }
        }
        return result;
    }

    /**
     * 校验配置
     *
     * @param config
     * @return
     */
    private String checkConfig(JSONObject config) {
        //出错信息
        String errorMsg = "";
        //ESB事件名称
        String esb_token, esb_budgets, esb_budget_node, field_annual;
        JSONArray detailConfig;
        JSONObject jo;
        try {
            // 校验一：校验必填参数
            if (config != null && !config.isEmpty()) {
                esb_token = Util.null2String(config.get("esb_token"));
                esb_budgets = Util.null2String(config.get("esb_budgets"));
                esb_budget_node = Util.null2String(config.get("esb_budget_node"));
                detailConfig = config.getJSONArray("config");
                field_annual = Util.null2String(config.get("field_annual"));
                if (esb_token.isEmpty() ||
                        esb_budgets.isEmpty() ||
                        esb_budget_node.isEmpty() ||
                        field_annual.isEmpty() ||
                        detailConfig.isEmpty()) {
                    errorMsg = "缺失必填项：ESB事件名或预算明细配置，请检查！";
                } else {
                    //校验 detailConfig每一项必填
                    for (int i = 0; i < detailConfig.size(); i++) {
                        jo = detailConfig.getJSONObject(i);
                        if (Util.null2String(jo.get("index")).isEmpty() ||
                                Util.null2String(jo.get("field_period")).isEmpty() ||
                                Util.null2String(jo.get("dimension_fields")).isEmpty() ||
                                Util.null2String(jo.get("budgets_param_name")).isEmpty() ||
                                jo.getJSONArray("write_back_config").isEmpty()) {
                            errorMsg = "缺失必填项：预算明细配置缺失必填参数，请检查！";
                        }
                    }
                }
            } else {
                errorMsg = "未找到相应的预算接口配置信息，请检查！";
            }
        } catch (Exception e) {
            errorMsg = "checkConfig 异常：" + SDUtil.getExceptionDetail(e);
        }

        return errorMsg;
    }


}
