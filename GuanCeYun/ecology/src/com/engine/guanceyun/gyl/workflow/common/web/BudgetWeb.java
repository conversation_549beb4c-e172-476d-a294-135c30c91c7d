package com.engine.guanceyun.gyl.workflow.common.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.guanceyun.gyl.workflow.common.service.BudgetService;
import com.engine.guanceyun.gyl.workflow.common.service.impl.BudgetServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName BudgetWeb.java
 * @Description 预算接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/12
 */
public class BudgetWeb {
    private BudgetService getService(User user) {
        return ServiceUtil.getService(BudgetServiceImpl.class, user);
    }

    /**
     * 刷新预算
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refreshBudget")
    @Produces({MediaType.TEXT_PLAIN})
    public String refreshBudget(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", true);
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).refreshBudget(params);
            } else {
                result.put("status", false);
                result.put("errorMsg", "user info error");
            }
        } catch (Exception e) {
            result.put("status", false);
            result.put("errorMsg", "catch exception : " + e.getMessage());
        }
        return JSONObject.toJSONString(result);
    }
}
