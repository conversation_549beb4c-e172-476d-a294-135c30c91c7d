package com.engine.guanceyun.gyl.workflow.common.service.impl;

import com.engine.core.impl.Service;
import com.engine.guanceyun.gyl.workflow.common.action.CallBudgetApiAction;
import com.engine.guanceyun.gyl.workflow.common.service.BudgetService;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName BudgetServiceImpl.java
 * @Description 预算实现
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/12
 */
public class BudgetServiceImpl extends Service implements BudgetService {
    /**
     * 刷新预算
     *
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> refreshBudget(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        String errorMsg;
        //step 1: 校验参数
        if (Util.null2String(params.get("requestid")).isEmpty() ||
                Util.null2String(params.get("functionId")).isEmpty()) {
            errorMsg = "缺失requestid或functionid参数";
        } else {
            //step 2：调用action执行逻辑
            CallBudgetApiAction action = new CallBudgetApiAction();
            errorMsg = action.executeData(Util.null2String(params.get("requestid")), Util.null2String(params.get("functionId")));
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        return result;
    }
}
