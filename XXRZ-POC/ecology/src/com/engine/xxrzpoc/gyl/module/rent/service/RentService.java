package com.engine.xxrzpoc.gyl.module.rent.service;

import weaver.hrm.User;

import java.util.Map;

public interface RentService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> calculateRent(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> savemoddata(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> savewfdata(Map<String, Object> params, User user);
}
