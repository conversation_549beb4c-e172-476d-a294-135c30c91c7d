package com.engine.xxrzpoc.gyl.module.rent.service.impl;

import com.engine.core.impl.Service;
import com.engine.xxrzpoc.gyl.module.rent.cmd.CalculateRentCmd;
import com.engine.xxrzpoc.gyl.module.rent.cmd.SaveModDataCmd;
import com.engine.xxrzpoc.gyl.module.rent.cmd.SaveWfDataCmd;
import com.engine.xxrzpoc.gyl.module.rent.service.RentService;
import weaver.hrm.User;

import java.util.Map;

public class RentServiceImpl extends Service implements RentService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> calculateRent(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CalculateRentCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> savemoddata(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveModDataCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> savewfdata(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveWfDataCmd(params, user));
    }
}
