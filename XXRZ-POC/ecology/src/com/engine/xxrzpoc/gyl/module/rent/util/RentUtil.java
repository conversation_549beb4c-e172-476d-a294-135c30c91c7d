package com.engine.xxrzpoc.gyl.module.rent.util;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

public class RentUtil {
    /**
     * 增加日期
     *
     * @param dateStr
     * @param increments
     * @param monthsToAdd
     * @return
     */
    public static String incrementDate(String dateStr, int increments, int monthsToAdd) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            // 解析日期字符串
            LocalDate date = LocalDate.parse(dateStr, formatter);
            // 累加月份
            for (int i = 0; i < increments; i++) {
                date = date.plusMonths(monthsToAdd);
            }
            // 返回格式化后的字符串
            return date.format(formatter);
        } catch (DateTimeParseException e) {
            return "输入日期格式错误: " + dateStr;
        }
    }

    /**
     * 计算等额本息还款的每期还款额
     *
     * @param rate 每期利率（年利率 / 期数）
     * @param nper 还款总期数
     * @param pv   本金（贷款金额）
     * @return 每期还款额（负值表示付款）
     */
    public static BigDecimal calculatePMT(BigDecimal rate, int nper, BigDecimal pv) {
        MathContext mc = new MathContext(15, RoundingMode.HALF_UP);

        // 如果利率为0，直接返回等额的本金还款
        if (rate.compareTo(BigDecimal.ZERO) == 0) {
            return pv.divide(BigDecimal.valueOf(nper), mc).negate();
        }

        // (1 + rate)^nper
        BigDecimal onePlusRate = BigDecimal.ONE.add(rate, mc);
        BigDecimal factor = onePlusRate.pow(nper, mc);

        // 计算PMT值
        // 结果为负表示流出
        return pv.multiply(rate, mc).multiply(factor, mc)
                .divide(factor.subtract(BigDecimal.ONE, mc), mc)
                .negate();
    }

    /**
     * 计算XIRR
     *
     * @param cashFlows   现金流列表（正值为流入，负值为流出）
     * @param dateStrings 与现金流对应的日期字符串列表，格式为yyyy-MM-dd
     * @param guess       初始估计值（通常可以使用0.1，即10%）
     * @return 计算的XIRR值，如果无法计算返回0
     */
    public static BigDecimal calculateXIRR(List<BigDecimal> cashFlows, List<String> dateStrings, BigDecimal guess) {
        final BigDecimal tol = new BigDecimal("1e-6"); // 误差容限
        final int maxIter = 1000; // 最大迭代次数
        BigDecimal xirr = guess;
        MathContext mc = new MathContext(15, RoundingMode.HALF_UP); // 设置计算精度
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将日期字符串解析为 LocalDate
        List<LocalDate> dates = dateStrings.stream()
                .map(date -> LocalDate.parse(date, formatter))
                .collect(Collectors.toList());

        for (int i = 0; i < maxIter; i++) {
            BigDecimal npv = BigDecimal.ZERO;
            BigDecimal dNpv = BigDecimal.ZERO;

            for (int j = 0; j < cashFlows.size(); j++) {
                long days = ChronoUnit.DAYS.between(dates.get(0), dates.get(j));
                BigDecimal exp = BigDecimal.valueOf(days / 365.0);
                // 使用Math.pow计算指数
                double rateFactorDouble = Math.pow(xirr.add(BigDecimal.ONE).doubleValue(), exp.doubleValue());
                BigDecimal rateFactor = BigDecimal.valueOf(rateFactorDouble);
                BigDecimal term = cashFlows.get(j).divide(rateFactor, mc);
                npv = npv.add(term, mc);

                BigDecimal derivativeTerm = cashFlows.get(j)
                        .multiply(BigDecimal.valueOf(-days))
                        .divide(rateFactor.multiply(BigDecimal.ONE.add(xirr, mc), mc).multiply(BigDecimal.valueOf(365), mc), mc);
                dNpv = dNpv.add(derivativeTerm, mc);
            }

            // 如果dNpv为0，避免除以0的情况
            if (dNpv.compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            }

            BigDecimal newGuess = xirr.subtract(npv.divide(dNpv, mc), mc);

            if (newGuess.subtract(xirr).abs().compareTo(tol) < 0) {
                return newGuess;
            }
            xirr = newGuess;
        }
        return BigDecimal.ZERO; // 如果无法在指定迭代次数内找到结果，返回0
    }


    public static void main(String[] args) {
        String aa = "2024-11-08";
        int bb = 2;
        int cc = 3;
        String dd = incrementDate(aa, bb, cc);
        System.out.println(dd);
    }
}
