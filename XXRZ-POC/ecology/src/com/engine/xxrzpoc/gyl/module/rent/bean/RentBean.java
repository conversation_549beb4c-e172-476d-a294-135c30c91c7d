package com.engine.xxrzpoc.gyl.module.rent.bean;

import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Data;
import weaver.conn.RecordSet;

import java.math.BigDecimal;


@Data
public class RentBean {
    private int id;
    /**
     * 起租日
     */
    private String qzr;
    /**
     * 还款间隔(月)
     */
    private Integer hkjgy;
    /**
     * 还款次数
     */
    private Integer hzcs;
    /**
     * 测算利率
     */
    private BigDecimal csll;
    /**
     * 融资额（本金）
     */
    private BigDecimal rze;
    /**
     * 服务费
     */
    private BigDecimal fwf;
    /**
     * 保证金
     */
    private BigDecimal bzj;
    /**
     * 保证金返还金额
     */
    private BigDecimal bzjfhje;

    @Data
    public static class Detail1 {
        private int id;
        private int mainid;
        /**
         * 期次
         */
        private Integer qc;
        /**
         * 计划日期
         */
        private String jhrq;
        /**
         * 租金
         */
        private BigDecimal zj;
        /**
         * 本金
         */
        private BigDecimal bj;
        /**
         * 利息
         */
        private BigDecimal lx;
        /**
         * 剩余本金
         */
        private BigDecimal sybj;

    }

    @Data
    public static class Detail2 {
        private int id;
        private int mainid;
        /**
         * 计划日期
         */
        private String jhrq;
        /**
         * 编号
         */
        private Integer bh;
        /**
         * 金额
         */
        private BigDecimal je;
        /**
         * 收付款对象
         */
        private String sfkdx;
        /**
         * 费用类型
         * 0服务费
         * 1保证金
         * 2融资款
         * 3保证金返还
         */
        private Integer fylx;
        /**
         * 收款/付款
         * 0收款
         * 1付款
         */
        private Integer skfk;
        /**
         * 结算方式
         * 0电汇
         */
        private Integer jsfs;

    }

    @Data
    public static class Detail3 {
        private int id;
        private int mainid;
        /**
         * 计划日期
         */
        private String jhrq;
        /**
         * 计划流入
         */
        private BigDecimal jhlr;
        /**
         * 计划流入详情
         */
        private String jhlrxq;
        /**
         * 计划流出
         */
        private BigDecimal jhlc;
        /**
         * 计划流程详情
         */
        private String jhlcxq;
        /**
         * 计划净流量
         */
        private BigDecimal jhjlc;
    }

    public final static String TABLE_NAME = "uf_swtj";
    public final static String TABLE_NAME_DT1 = "uf_swtj_dt1";
    public final static String TABLE_NAME_DT2 = "uf_swtj_dt2";
    public final static String TABLE_NAME_DT3 = "uf_swtj_dt3";


    public static RentBean getInstance(String billid) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery("select * from " + TABLE_NAME + " where id = ?", billid)) {
            return QueryUtil.getObj(rs, RentBean.class);
        }
        return null;
    }


}
