package com.engine.wanhong.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.wanhong.tpw.common.bean.PayerInfo;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SyncPayerInfoJob extends BaseCronJob {
    BaseBean bb = new BaseBean();
    private String eventLogoType;
    private List<String> orgIds;

    @Override
    public void execute() {
        try {
            bb.writeLog("SyncPayerInfoJob start");
            resetParams();
            init();
            if (StringUtils.isNotBlank(eventLogoType)) {
                //获取token
                String token = "";
                JSONObject params = new JSONObject();
                Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
                String random = ApiUtil.generateSecureRandomString();
                params.put("appId", Util.null2String(environmentConfig.get("APPID")));
                params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
                params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
                params.put("random", random);
                params.put("sign", ApiUtil.getTokenSign(random));
                EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
                if (er.isSuccess()) {
                    JSONObject data = er.getData();
                    if (data != null) {
                        token = Util.null2String(data.getString("access_token"));
                    }
                } else {
                    bb.writeLog("SyncPayerInfoJob ESB getToken error : " + JSONObject.toJSONString(er));
                }
                if (StringUtils.isNotBlank(token)) {
                    //查询组织结构 QueryOrganizationalStructure  上传其他业务附件 UploadOtherBusinessAttachments
                    params.put("access_token", token);
                    EsbEventResult orgStructER = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                    List<PayerInfo> newData = new ArrayList<>();
                    if (orgStructER.isSuccess()) {
                        JSONObject dataObg = orgStructER.getData();
                        String dataStr = Util.null2String(dataObg.get("data"));
                        if (StringUtils.isNotBlank(dataStr)) {
                            JSONObject data = JSONObject.parseObject(dataStr);
                            bb.writeLog("SyncPayerInfoJob ESB response info " + JSONObject.toJSONString(data));
                            if (data != null) {
                                String orgId = Util.null2String(data.get("orgId"));
                                String orgName = Util.null2String(data.get("orgName"));
                                PayerInfo payerInfo = new PayerInfo();
                                payerInfo.setFkfid(orgId);
                                payerInfo.setFkfmc(orgName);
                                if (!orgIds.contains(orgId)) {
                                    newData.add(payerInfo);
                                }
                                String subStructs = Util.null2String(data.get("subStructs"));
                                if (StringUtils.isNotBlank(subStructs)) {
                                    JSONArray subStructsArray = JSONArray.parseArray(subStructs);
                                    if (subStructsArray != null && !subStructsArray.isEmpty()) {
                                        for (int i = 0; i < subStructsArray.size(); i++) {
                                            payerInfo = new PayerInfo();
                                            JSONObject subStruct = (JSONObject) subStructsArray.get(i);
                                            payerInfo.setFkfmc(Util.null2String(subStruct.get("orgName")));
                                            payerInfo.setFkfid(Util.null2String(subStruct.get("orgId")));
                                            if (!orgIds.contains(Util.null2String(subStruct.get("orgId")))) {
                                                newData.add(payerInfo);
                                            }
                                        }
                                    }
                                }
                                if (!newData.isEmpty()) {
                                    ModuleResult mr = ModuleDataUtil.insertObjList(newData, PayerInfo.TABLE_NAME, ModuleDataUtil.getModuleIdByName(PayerInfo.TABLE_NAME), 1);
                                    bb.writeLog("SyncPayerInfoJob update " + PayerInfo.TABLE_NAME + " result " + JSONObject.toJSONString(mr));
                                }
                            } else {
                                bb.writeLog("SyncPayerInfoJob ESB gget Organizational structure data is null" + JSONObject.toJSONString(orgStructER));
                            }
                        }


                    } else {
                        bb.writeLog("SyncPayerInfoJob ESB get Organizational structure is error : " + JSONObject.toJSONString(orgStructER));
                    }
                } else {
                    bb.writeLog("SyncPayerInfoJob ESB eventLogoType param  is null");
                }
            }
        } catch (Exception e) {
            bb.writeLog("SyncPayerInfoJob Exception:" + SDUtil.getExceptionDetail(e));
        }
    }

    private void init() {
        String sql = "select * from uf_fkfid";
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery(sql);
        while (recordSet.next()) {
            orgIds.add(recordSet.getString("fkfid"));
        }
    }

    private void resetParams() {
        orgIds = new ArrayList<>();
    }
}

