package com.engine.wanhong.tpw.workflow;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName CompleteArchivePackageAction
 * @Description 完成归档包
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/26
 */
@Getter
@Setter
public class CompleteArchivePackageAction implements Action {
    private BaseBean logger = new BaseBean();
    //事件标识
    private String eventLogoType;
    //归档包id字段名
    private String gdbIdField;
    //企业ID字段
    private String qyIdField;


    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = ""; // 错误信息
        try {
            logger.writeLog("CompleteArchivePackageAction----START");
            if (StringUtils.isBlank(eventLogoType))
                return ActionUtil.handleResult("action配置事件标识未配置", requestInfo);
            if (StringUtils.isBlank(qyIdField))
                return ActionUtil.handleResult("action配置企业ID字段未配置", requestInfo);
            if (StringUtils.isBlank(gdbIdField))
                return ActionUtil.handleResult("action配置归档包ID字段未配置", requestInfo);
            // 获取流程信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            Map<String, String> mainData = actionInfo.getMainData();
            String qyId = mainData.get(qyIdField);
            String gdbId = mainData.get(gdbIdField);
            String token = "";
            JSONObject params = new JSONObject();
            Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
            String random = ApiUtil.generateSecureRandomString();
            params.put("appId", Util.null2String(environmentConfig.get("APPID")));
            params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
            params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
            params.put("random", random);
            params.put("sign", ApiUtil.getTokenSign(random));
            EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
            if (er.isSuccess()) {
                JSONObject data = er.getData();
                if (data != null) {
                    token = Util.null2String(data.getString("access_token"));
                }
            } else {
                errorMsg = "Action getToken error : " + JSONObject.toJSONString(er);
                logger.writeLog("Action getToken error : " + JSONObject.toJSONString(er));
            }
            if (StringUtils.isNotBlank(token)) {
                params.put("orgId", qyId);
                params.put("archivePackageId", gdbId);
                String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                String sign = ApiUtil.generateMD5Sign(generateSignature);
                params.put("sign", sign);
                params.put("access_token", token);
                EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                if (!evr.isSuccess())
                    errorMsg = "ESB提交完成归档包接口失败 批次号:" + evr.getSerialNumber();
            } else {
                errorMsg = "CompleteArchivePackageAction 获取token失败";
            }
        } catch (Exception e) {
            errorMsg = "系统异常：" + SDUtil.getExceptionDetail(e);
            logger.writeLog("CompleteArchivePackageAction Exception: " + SDUtil.getExceptionDetail(e));
        }
        logger.writeLog("CompleteArchivePackageAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

}

