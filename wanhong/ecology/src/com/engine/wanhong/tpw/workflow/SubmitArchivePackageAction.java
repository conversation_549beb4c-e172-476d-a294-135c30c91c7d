package com.engine.wanhong.tpw.workflow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.opensaml.xml.signature.J;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName SubmitArchivePackageAction
 * @Description 提交归档包
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/10
 */
@Getter
@Setter
public class SubmitArchivePackageAction implements Action {
    private BaseBean logger = new BaseBean();
    //事件标识
    private String eventLogoType;
    //归档包名称字段
    private String gdbmcField;
    //企业ID字段
    private String qyIdField;
//    //归档年份字段
//    private String gdnfField;
//    //待归档的凭证册列表字段
//    private String pzclbField;
    //归档包ID字段
    private String gdbidField;
    //下载地址
//    private String xzdzField;

    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = ""; // 错误信息
        RecordSet recordSet = null;
        try {
            logger.writeLog("SubmitArchivePackageAction----START");
            // 初始化 RecordSet
            recordSet = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isBlank(eventLogoType))
                return ActionUtil.handleResult("action配置事件标识未配置", requestInfo);
            if (StringUtils.isBlank(gdbmcField))
                return ActionUtil.handleResult("action配置归档包名称字段未配置", requestInfo);
//            if (StringUtils.isBlank(gdnfField))
//                return ActionUtil.handleResult("action配置归档年份字段未配置", requestInfo);
//            if (StringUtils.isBlank(pzclbField))
//                return ActionUtil.handleResult("action配置待归档的凭证册列表字段未配置", requestInfo);
            if (StringUtils.isBlank(qyIdField))
                return ActionUtil.handleResult("action配置企业ID字段未配置", requestInfo);
            if (StringUtils.isBlank(gdbidField))
                return ActionUtil.handleResult("action配置归档包ID字段未配置", requestInfo);
//            if (StringUtils.isBlank(xzdzField))
//                return ActionUtil.handleResult("action配置下载地址未配置", requestInfo);
            // 获取流程信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            String formtableName = actionInfo.getFormtableName();
            String requestId = actionInfo.getRequestId();
            Map<String, String> mainData = actionInfo.getMainData();
            String qyId = mainData.get(qyIdField);
            String gdbmc = mainData.get(gdbmcField);


            ArrayList<String> accountingVoucherBookIds = new ArrayList<>();
            //明细表集合
            Map<Integer, List<Map<String, String>>> detailDatas = actionInfo.getDetailData();
            String pzcrqflag = "";
            ArrayList<String> pzcxxs = new ArrayList<>();
            if (!detailDatas.isEmpty()) {
                List<Map<String, String>> detailData = detailDatas.get(1);
                for (int i = 0; i < detailData.size(); i++) {
                    Map<String, String> detail = detailData.get(i);
                    String pzcxx = detail.get("pzcxx");
                    if(StringUtils.isNotBlank(pzcxx)){
                        pzcxxs.add(pzcxx);
                        List<Map<String, Object>> mainDataByReqId = getMainDataByReqId(pzcxx);
                        Map<String, Object> stringObjectMap = mainDataByReqId.get(0);
                        if(stringObjectMap != null){
                            String pzcid = Util.null2String(stringObjectMap.get("pzcid"));
                            accountingVoucherBookIds.add(pzcid);
                            String pzcrq = Util.null2String(stringObjectMap.get("pzcrq"));
                            if(StringUtils.isNotBlank(pzcrqflag) && StringUtils.isNotBlank(pzcrq)){
                                String substring1 = pzcrqflag.substring(0, 4);
                                String substring2= pzcrq.substring(0, 4);
                                if(substring1.equals(substring2)){

                                }else {
                                    return ActionUtil.handleResult("提交归档包action出错，流程明细中归档年份不一致", requestInfo);
                                }
                            }
                            pzcrqflag = pzcrq;
                        }
                    }
                }
            }


            String token = "";
            JSONObject params = new JSONObject();
            Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
            String random = ApiUtil.generateSecureRandomString();
            params.put("appId", Util.null2String(environmentConfig.get("APPID")));
            params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
            params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
            params.put("random", random);
            params.put("sign", ApiUtil.getTokenSign(random));
            EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
            if (er.isSuccess()) {
                JSONObject data = er.getData();
                if (data != null) {
                    token = Util.null2String(data.getString("access_token"));
                }
            } else {
                errorMsg = "Action getToken error : " + JSONObject.toJSONString(er);
                logger.writeLog("Action getToken error : " + JSONObject.toJSONString(er));
            }


            if (StringUtils.isNotBlank(token)) {
                params.put("orgId", qyId);
                params.put("archiveYear", pzcrqflag.substring(0,4));
                params.put("archivePackageName", gdbmc);
                params.put("keepPeriod", "永久");
//                JSONArray accountingVoucherBookId = new JSONArray();
//                String accountingVoucherIdsStr = String.join(",", accountingVoucherBookIds);
//                accountingVoucherBookId.add(accountingVoucherIdsStr);
                params.put("accountingVoucherBookIds", accountingVoucherBookIds);
                String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                String sign = ApiUtil.generateMD5Sign(generateSignature);
                params.put("sign", sign);
                params.put("access_token", token);
                EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                if (evr.isSuccess()) {
                    JSONObject data = evr.getData();
                    if (data != null) {
                        String code = Util.null2String(data.getString("code"));
                        String msg = Util.null2String(data.getString("msg"));
                        if (StringUtils.isNotBlank(msg) && msg.equals("成功") && StringUtils.isNotBlank(code) && code.equals("success")) {
                            String dataStr = Util.null2String(data.getString("data"));
                            if (StringUtils.isNotBlank(dataStr)) {
                                JSONObject jsonObject = JSONObject.parseObject(dataStr);
                                String archivePackageId = Util.null2String(jsonObject.getString("archivePackageId"));
                                if (StringUtils.isNotBlank(archivePackageId)) {
                                    //回写流程明细
                                    String writebackWorlflowSql = "update " + formtableName + " set " + gdbidField + " = ?  where requestId = ?";
                                    recordSet.executeUpdate(writebackWorlflowSql, archivePackageId, requestId);
                                    //回写建模
                                    String writebackModuleSql = "update uf_pzc set gdbid = '"+archivePackageId+"' , gdbzt  = 1 where id in  ("+String.join(",",pzcxxs)+")";
                                    recordSet.executeUpdate(writebackModuleSql);
                                }
                            }
                        } else {
                            errorMsg = msg;
                        }
                    }
                } else {
                    errorMsg = "ESB提交归档包接口失败";
                }

            } else {
                return ActionUtil.handleResult("SubmitArchivePackageAction 获取token失败", requestInfo);
            }
            if (StringUtils.isNotBlank(errorMsg)) {
                return ActionUtil.handleResult("提交凭证:" + errorMsg, requestInfo);
            }


        } catch (Exception e) {
            errorMsg = "系统异常：" + SDUtil.getExceptionDetail(e);
            logger.writeLog("SubmitArchivePackageAction Exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            if (recordSet != null) {
                DBUtil.clearThreadLocalRecordSet();
            }
        }
        logger.writeLog("SubmitArchivePackageAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    public List<Map<String, Object>> getMainDataByReqId(String lcbh) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        List<Map<String, Object>> list = new ArrayList<>();
        String sql = " select * from uf_pzc where id = ?";
        if (rs.executeQuery(sql, lcbh)) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }


}
