package com.engine.wanhong.tpw.workflow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName SubmitVoucheBookAction
 * @Description 提交凭证册
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/10
 */
@Getter
@Setter
public class SubmitVoucheBookAction implements Action {
    private BaseBean logger = new BaseBean();
    private String eventLogoType;
    private String pzcmcField;
    private String qyIdField;
    private String mxpzidFiled;
    private String pzcidField;

    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = ""; // 错误信息
        RecordSet recordSet = null;
        try {
            logger.writeLog("SubmitVoucheBookAction----START");
            // 初始化 RecordSet
            recordSet = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isBlank(eventLogoType))
                return ActionUtil.handleResult("action配置事件标识未配置", requestInfo);
            if (StringUtils.isBlank(pzcmcField))
                return ActionUtil.handleResult("action配置凭证册名称字段未配置", requestInfo);
            if (StringUtils.isBlank(qyIdField))
                return ActionUtil.handleResult("action配置企业ID字段未配置", requestInfo);
            if (StringUtils.isBlank(mxpzidFiled))
                return ActionUtil.handleResult("action配置待关联的凭证ID列表字段未配置", requestInfo);
            if (StringUtils.isBlank(pzcidField))
                return ActionUtil.handleResult("action配置凭证册ID字段未配置", requestInfo);
            JSONArray accountingVoucherIdsArray = new JSONArray();
            // 获取流程信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            String formtableName = actionInfo.getFormtableName();
            String requestId = actionInfo.getRequestId();
            Map<String, String> mainData = actionInfo.getMainData();
            String qyId = mainData.get(qyIdField);
            String pzcmc = mainData.get(pzcmcField);

            String token = "";
            JSONObject params = new JSONObject();
            Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
            String random = ApiUtil.generateSecureRandomString();
            params.put("appId", Util.null2String(environmentConfig.get("APPID")));
            params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
            params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
            params.put("random", random);
            params.put("sign", ApiUtil.getTokenSign(random));
            EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
            if (er.isSuccess()) {
                JSONObject data = er.getData();
                if (data != null) {
                    token = Util.null2String(data.getString("access_token"));
                }
            } else {
                errorMsg = "Action getToken error : " + JSONObject.toJSONString(er);
                logger.writeLog("Action getToken error : " + JSONObject.toJSONString(er));
            }
            ArrayList<String> accountingVoucherIds = new ArrayList<>();
            //明细表集合
            Map<Integer, List<Map<String, String>>> detailDatas = actionInfo.getDetailData();
            ArrayList<String> ids = new ArrayList<>();
            if (!detailDatas.isEmpty()) {
                List<Map<String, String>> detailData = detailDatas.get(1);
                for (int i = 0; i < detailData.size(); i++) {
                    Map<String, String> detail = detailData.get(i);
                    String pzxx = detail.get("pzxx");
                    List<Map<String, Object>> mainDataByReqId = getMainDataByReqId(pzxx);
                    Map<String, Object> stringObjectMap = mainDataByReqId.get(0);
                    if(StringUtils.isNotBlank(Util.null2String(stringObjectMap.get(mxpzidFiled)))){
                        ids.add(pzxx);
                        accountingVoucherIdsArray.add(Util.null2String(stringObjectMap.get(mxpzidFiled)));
                    }
                }
            }

            if (StringUtils.isNotBlank(token)) {
                params.put("orgId", qyId);
                params.put("accountingDocumentBookName", pzcmc);
                params.put("accountingVoucherIds", accountingVoucherIdsArray);
                String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                String sign = ApiUtil.generateMD5Sign(generateSignature);
                params.put("sign", sign);
                params.put("access_token", token);
                EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                if (evr.isSuccess()) {
                    JSONObject data = evr.getData();
                    if (data != null) {
                        String code = Util.null2String(data.getString("code"));
                        String msg = Util.null2String(data.getString("msg"));
                        if (StringUtils.isNotBlank(msg) && msg.equals("成功") && StringUtils.isNotBlank(code) && code.equals("success")) {
                            String dataStr = Util.null2String(data.getString("data"));
                            if (StringUtils.isNotBlank(dataStr)) {
                                JSONObject jsonObject = JSONObject.parseObject(dataStr);
                                String accountingVoucherBookId = Util.null2String(jsonObject.getString("accountingVoucherBookId"));
                                if (StringUtils.isNotBlank(accountingVoucherBookId)) {
                                    //回写流程明细
                                    String writebackWorlflowSql = "update " + formtableName + " set "+pzcidField+" = ? where requestId = ?";
                                    recordSet.executeUpdate(writebackWorlflowSql, accountingVoucherBookId,requestId);
                                    //回写建模信息
                                    if(!ids.isEmpty()){
                                        String writebackModuleSql = "update uf_pzxx set pzcid = ? , pzczt = 1 where id in ("+String.join(",",ids)+")";
                                        recordSet.executeUpdate(writebackModuleSql,accountingVoucherBookId);
                                    }
                                }
                            }
                        } else {
                            errorMsg = msg;
                        }
                    }
                } else {
                    errorMsg = "ESB上传归档票据文件接口失败";
                }

            } else {
                return ActionUtil.handleResult("SubmitVoucheBookAction 获取token失败", requestInfo);
            }
            if (StringUtils.isNotBlank(errorMsg)) {
                return ActionUtil.handleResult("提交凭证:" + errorMsg, requestInfo);
            }


        } catch (Exception e) {
            errorMsg = "系统异常：" + SDUtil.getExceptionDetail(e);
            logger.writeLog("SubmitVoucheBookAction Exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            if (recordSet != null) {
                DBUtil.clearThreadLocalRecordSet();
            }
        }
        logger.writeLog("SubmitVoucheBookAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    public List<Map<String, Object>> getMainDataByReqId(String lcbh) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        List<Map<String, Object>> list = new ArrayList<>();
        String sql = " select * from uf_pzxx where id = ?";
        if (rs.executeQuery(sql, lcbh)) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }
}