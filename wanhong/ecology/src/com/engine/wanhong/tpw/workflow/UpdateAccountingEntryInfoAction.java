package com.engine.wanhong.tpw.workflow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * @FileName UpdateAccountingEntryInfoAction
 * @Description 更新票据的会计记账信息
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/9
 */
@Getter
@Setter
public class UpdateAccountingEntryInfoAction implements Action {
    private BaseBean logger = new BaseBean();
    private String eventLogoType;

    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = ""; // 错误信息
        RecordSet recordSet = null;
        try {
            logger.writeLog("UpdateAccountingEntryInfoAction----START");
            // 初始化 RecordSet
            recordSet = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isBlank(eventLogoType))
                return ActionUtil.handleResult("action配置事件标识未配置", requestInfo);
            // 获取流程信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            //明细表集合
            Map<Integer, List<Map<String, String>>> detailDatas = actionInfo.getDetailData();
            if (!detailDatas.isEmpty()) {
                List<Map<String, String>> detailData = detailDatas.get(1);
                for (int i = 0; i < detailData.size(); i++) {
                    String token = "";
                    JSONObject params = new JSONObject();
                    Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
                    String random = ApiUtil.generateSecureRandomString();
                    params.put("appId", Util.null2String(environmentConfig.get("APPID")));
                    params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
                    params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
                    params.put("random", random);
                    params.put("sign", ApiUtil.getTokenSign(random));
                    EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
                    if (er.isSuccess()) {
                        JSONObject data = er.getData();
                        if (data != null) {
                            token = Util.null2String(data.getString("access_token"));
                        }
                    } else {
                        errorMsg = "Action getToken error : " + JSONObject.toJSONString(er);
                        logger.writeLog("Action getToken error : " + JSONObject.toJSONString(er));
                    }
                    if (StringUtils.isNotBlank(token)) {
                        Map<String, String> row = detailData.get(i);
                        String pzxx = Util.null2String(row.get("pzxx"));
                        if (StringUtils.isNotBlank(pzxx)) {
                            List<Map<String, Object>> mainDataByReqId = getMainDataByReqId(pzxx);
                            if (!mainDataByReqId.isEmpty()) {
                                Map<String, Object> mainDataMap = mainDataByReqId.get(0);
                                if (mainDataMap != null) {
                                    String pjid = Util.null2String(mainDataMap.get("pjid"));
                                    String mainId = Util.null2String(mainDataMap.get("id"));
                                    if (StringUtils.isNotBlank(pjid)) {
                                        String[] fieldArray = pjid.split(",");
                                        for (String field : fieldArray) {
                                            //企业ID
                                            params.put("orgId", Util.null2String(mainDataMap.get("fkfqyid")));
                                            //票据ID
                                            params.put("invoiceId", field);
                                            //票据的状态信息
                                            JSONObject invoiceStatusInfo = new JSONObject();
                                            //是否红冲
                                            invoiceStatusInfo.put("isRed", "N");
                                            //是否验真
                                            invoiceStatusInfo.put("isVerified", "Y");
                                            //是否入账
                                            invoiceStatusInfo.put("isBooked", "Y");
                                            //确认用途归期
                                            invoiceStatusInfo.put("usageConfirmationPeriod", Util.null2String(mainDataMap.get("qrytgq")));
                                            params.put("invoiceStatusInfo", invoiceStatusInfo);
                                            //会计基本信息
                                            JSONObject accountingBaseInfo = new JSONObject();
                                            //会计统一社会编码
                                            accountingBaseInfo.put("accountingSocietyNumber", Util.null2String(mainDataMap.get("hjtyshbm")));
                                            //会计实体名称
                                            accountingBaseInfo.put("accountingEntityName", Util.null2String(mainDataMap.get("hjstmc")));
                                            params.put("accountingBaseInfo", accountingBaseInfo);
                                            //借贷会计信息
                                            JSONArray debitCreditAccountingInfos = new JSONArray();
                                            //通过明细的记录中关联的流程 获取所关联流程的明细信息
                                            List<Map<String, Object>> detailsDataByReqIds = getDetailsDataByReqId(mainId);
                                            if (!detailsDataByReqIds.isEmpty()) {
                                                for (int j = 0; j < detailsDataByReqIds.size(); j++) {
                                                    Map<String, Object> detailsData = detailsDataByReqIds.get(j);
                                                    JSONObject debitCreditAccountingInfo = new JSONObject();
                                                    //总账科目名称
                                                    debitCreditAccountingInfo.put("generalLedgerSubjectName", Util.null2String(detailsData.get("zzkmmc")));
                                                    //明细科目名称
                                                    debitCreditAccountingInfo.put("subsidiaryLedgerSubjectName", Util.null2String(detailsData.get("mxkmmc")));
                                                    //入账金额
                                                    debitCreditAccountingInfo.put("recordedAmount", Util.null2String(detailsData.get("rzje")));
                                                    //借贷方向
                                                    debitCreditAccountingInfo.put("creditOrDebit", Util.null2String(detailsData.get("jdfx")));
                                                    debitCreditAccountingInfos.add(debitCreditAccountingInfo);
                                                }
                                            }
                                            params.put("debitCreditAccountingInfo", debitCreditAccountingInfos);
                                            String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                                            String sign = ApiUtil.generateMD5Sign(generateSignature);
                                            params.put("sign", sign);
                                            params.put("access_token", token);
                                            EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                                            if (evr.isSuccess()) {
                                                JSONObject data = evr.getData();
                                                if (data != null) {
                                                    String code = Util.null2String(data.getString("code"));
                                                    String msg = Util.null2String(data.getString("msg"));
                                                    if (StringUtils.isNotBlank(msg) && msg.equals("成功") && StringUtils.isNotBlank(code) && code.equals("success")) {

                                                    } else {
                                                        return ActionUtil.handleResult(eventLogoType + ": ESB更新票据的会计记账信息失败！事件批次号：" + evr.getSerialNumber() + msg, requestInfo);
                                                    }
                                                } else {
                                                    return ActionUtil.handleResult(eventLogoType + ": ESBB事件调用data数据返回为空！事件批次号：" + evr.getSerialNumber(), requestInfo);
                                                }
                                            } else {
                                                return ActionUtil.handleResult(eventLogoType + ": ESB事件更新票据的会计记账信息失败！事件批次号：" + evr.getSerialNumber(), requestInfo);
                                            }
                                        }
                                    } else {
                                        return ActionUtil.handleResult("票据ID为空", requestInfo);
                                    }
                                }

                            }
                        }

                    } else {
                        return ActionUtil.handleResult("UpdateAccountingEntryInfoAction 获取token失败", requestInfo);
                    }
                    if (StringUtils.isNotBlank(errorMsg)) {
                        return ActionUtil.handleResult("更新票据的会计记账信息:" + errorMsg, requestInfo);
                    }
                }
            }
        } catch (Exception e) {
            errorMsg = "系统异常：" + SDUtil.getExceptionDetail(e);
            logger.writeLog("UpdateAccountingEntryInfoAction Exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            if (recordSet != null) {
                DBUtil.clearThreadLocalRecordSet();
            }
        }
        logger.writeLog("UpdateAccountingEntryInfoAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    public List<Map<String, Object>> getDetailsDataByReqId(String mainid) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        List<Map<String, Object>> list = new ArrayList<>();
        String sql = " select * from uf_pzxx_dt3 where mainid = ?";
        if (rs.executeQuery(sql, mainid)) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }

    public List<Map<String, Object>> getMainDataByReqId(String lcbh) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        List<Map<String, Object>> list = new ArrayList<>();
        String sql = " select * from uf_pzxx where id = ?";
        if (rs.executeQuery(sql, lcbh)) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }

//    public static void main(String[] args) throws UnsupportedEncodingException {
//        JSONObject params = new JSONObject();
////        params.put("appId", "lQggN8LM2ZW2vKKPI9PbKntdSE4YlVcr");
////        params.put("appSecret", "CyU0oITiAgXBYP7PWKXXFxLixn4RvbFI0Y5aUer2Ex7bjO8TqOPg7mtzzEsKJmgo");
////        params.put("grantType", "client_credentials");
////        params.put("random", "z4vW4lwz");
//        params.put("orgId", "1863769310548201472");
//        params.put("archivePackageId", "1868941661858435072");
////        params.put("keepPeriod", "永久");
////        JSONArray objects = new JSONArray();
////        objects.add("1867494236274823168");
////        objects.add("1867442443243229184");
////        params.put("accountingVoucherBookIds", objects);
////        params.put("archivePackageName", "上海长宁工业资产经营有限公司-241217归档包");
////        params.put("archivePackageId", "1868939813386719232");
//        String generateSignature = ApiUtil.generateSignature(params) + "|CyU0oITiAgXBYP7PWKXXFxLixn4RvbFI0Y5aUer2Ex7bjO8TqOPg7mtzzEsKJmgo";
//        System.out.println("排序后字符串：" + generateSignature);
//        String sign = ApiUtil.generateMD5Sign(generateSignature);
//        System.out.println("加签后字符串: " + sign);
//
//        // Step 1: 创建参数对象并转化为 JSON 字符串
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("orgId", "1863769310548201472");
//        jsonObject.put("archivePackageId", "1868941661858435072");
//
//        // Step 2: Base64 编码
//        String base64Encoded = encodeBase64(JSONObject.toJSONString(jsonObject));
//        System.out.println("Base64 编码: " + base64Encoded);
//
//        // Step 3: URL 编码
//        String urlEncoded = encodeUrl(base64Encoded);
//        System.out.println("URL 编码: " + urlEncoded);
//
//    }
//
//    // Base64 编码方法
//    private static String encodeBase64(String data) {
//        return Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8));
//    }
//
//    // URL 编码方法
//    private static String encodeUrl(String data) throws UnsupportedEncodingException {
//        return URLEncoder.encode(data, "UTF-8");
//    }


}

