package com.engine.wanhong.tpw.workflow;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;

import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName SubmitCredentialsAction
 * @Description 提交凭证
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/9
 */
@Getter
@Setter
public class SubmitCredentialsAction implements Action {
    private BaseBean logger = new BaseBean();
    private String eventLogoType;

    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = ""; // 错误信息
        RecordSet recordSet = null;
        try {
            logger.writeLog("SubmitCredentialsAction----START");
            // 初始化 RecordSet
            recordSet = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isBlank(eventLogoType))
                return ActionUtil.handleResult("action配置事件标识未配置", requestInfo);
            // 获取流程信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);

            //明细表集合
            Map<Integer, List<Map<String, String>>> detailDatas = actionInfo.getDetailData();
            if (!detailDatas.isEmpty()) {
                List<Map<String, String>> detailData = detailDatas.get(1);
                for (int i = 0; i < detailData.size(); i++) {
                    String token = "";
                    JSONObject params = new JSONObject();
                    Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
                    String random = ApiUtil.generateSecureRandomString();
                    params.put("appId", Util.null2String(environmentConfig.get("APPID")));
                    params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
                    params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
                    params.put("random", random);
                    params.put("sign", ApiUtil.getTokenSign(random));
                    EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
                    if (er.isSuccess()) {
                        JSONObject data = er.getData();
                        if (data != null) {
                            token = Util.null2String(data.getString("access_token"));
                        }
                    } else {
                        errorMsg = "Action getToken error : " + JSONObject.toJSONString(er);
                        logger.writeLog("Action getToken error : " + JSONObject.toJSONString(er));
                    }
                    if (StringUtils.isNotBlank(token)) {
                        Map<String, String> detail = detailData.get(i);
                        String lcbh = Util.null2String(detail.get("pzxx"));
                        if (StringUtils.isNotBlank(lcbh)) {
                            List<Map<String, Object>> mainDataByReqId = getMainDataByReqId(lcbh);
                            if (!mainDataByReqId.isEmpty()) {
                                Map<String, Object> mainDataMap = mainDataByReqId.get(0);
                                if (mainDataMap != null) {
                                    String mainId = Util.null2String(mainDataMap.get("id"));
                                    params.put("orgId", Util.null2String(mainDataMap.get("fkfqyid")));
                                    params.put("accountingDocumentNumber", Util.null2String(mainDataMap.get("pzbh")));
                                    params.put("postPeriod", Util.null2String(mainDataMap.get("jzrq")));
                                    params.put("accountingPeriod", Util.null2String(mainDataMap.get("hjqj")));
                                    JSONArray invoiceIds = new JSONArray();
                                    String pjids = Util.null2String(mainDataMap.get("pjid"));
                                    if(StringUtils.isNotBlank(pjids)){
                                        String[] pjidArr =  pjids.split(",");
                                        for(String pjid:pjidArr){
                                            invoiceIds.add(Util.null2String(pjid));
                                        }
                                    }
                                    params.put("invoiceIds", invoiceIds);
                                    params.put("accountingDocumentName", Util.null2String(mainDataMap.get("pzmc")));
                                    JSONArray otherAttachIds = new JSONArray();
//                                    if (StringUtils.isNotBlank(Util.null2String(mainDataMap.get("qtfjid")))) {
//                                        otherAttachIds.add(Util.null2String(mainDataMap.get("qtfjid")));
//                                    }
                                    String qtfjids = Util.null2String(mainDataMap.get("qtfjid"));
                                    if(StringUtils.isNotBlank(qtfjids)){
                                        String[] qtfjidsArr =  qtfjids.split(",");
                                        for(String qtfjid:qtfjidsArr){
                                            otherAttachIds.add(Util.null2String(qtfjid));
                                        }
                                    }
                                    params.put("otherAttachIds", otherAttachIds);
                                    String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                                    String sign = ApiUtil.generateMD5Sign(generateSignature);
                                    params.put("sign", sign);
                                    params.put("access_token", token);
                                    EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                                    if (evr.isSuccess()) {
                                        JSONObject data = evr.getData();
                                        if (data != null) {
                                            String code = Util.null2String(data.getString("code"));
                                            String msg = Util.null2String(data.getString("msg"));
                                            if (StringUtils.isNotBlank(msg) && msg.equals("成功") && StringUtils.isNotBlank(code) && code.equals("success")) {
                                                String dataStr = Util.null2String(data.getString("data"));
                                                if (StringUtils.isNotBlank(dataStr)) {
                                                    JSONObject jsonObject = JSONObject.parseObject(dataStr);
                                                    String accountingVoucherId = Util.null2String(jsonObject.getString("accountingVoucherId"));
                                                    if (StringUtils.isNotBlank(accountingVoucherId)) {
                                                        //更新建模数据
                                                        String updateModuleSql = "update uf_pzxx set pzid = ? , pzzt = 1 where id = ?";
                                                        recordSet.executeUpdate(updateModuleSql, accountingVoucherId, lcbh);
                                                    }
                                                }
                                            } else {
                                                errorMsg = "事件批次号：" + evr.getSerialNumber() + msg;
                                            }
                                        }
                                    } else {
                                        errorMsg = "ESB提交凭证接口失败！事件批次号：" + evr.getSerialNumber();
                                    }
                                }
                            }
                        }

                    } else {
                        return ActionUtil.handleResult("SubmitCredentialsAction 获取token失败", requestInfo);
                    }
                    if (StringUtils.isNotBlank(errorMsg)) {
                        return ActionUtil.handleResult("提交凭证:" + errorMsg, requestInfo);
                    }
                }
            }

        } catch (Exception e) {
            errorMsg = "系统异常：" + SDUtil.getExceptionDetail(e);
            logger.writeLog("SubmitCredentialsAction Exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            if (recordSet != null) {
                DBUtil.clearThreadLocalRecordSet();
            }
        }
        logger.writeLog("SubmitCredentialsAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }


    public List<Map<String, Object>> getMainDataByReqId(String lcbh) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        List<Map<String, Object>> list = new ArrayList<>();
        String sql = " select * from uf_pzxx where id = ?";
        if (rs.executeQuery(sql, lcbh)) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }

}
