package com.engine.wanhong.tpw.workflow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @FileName UploadOtherBusinessAttachmentsAction
 * @Description 上传其他业务附件
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/9
 */
@Setter
@Getter
public class UploadOtherBusinessAttachmentsAction implements Action {
    private BaseBean logger = new BaseBean();
    private String eventLogoType;
    //付款方企业id
    private String fkfidFiled;
    //附件字段名 多个字段之间用逗号拼接
    private String fjzdFiled;
    //当前服务器地址 https://oa-new.wanhong.sh.cn/
    private String host;
    //其他附件id
    private String qtfjidField;

    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = ""; // 错误信息
        RecordSet recordSet = null;
        try {
            logger.writeLog("UploadOtherBusinessAttachmentsAction----START");
            // 初始化 RecordSet
            recordSet = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isBlank(eventLogoType))
                return ActionUtil.handleResult("action配置事件标识未配置", requestInfo);
            if (StringUtils.isBlank(host))
                return ActionUtil.handleResult("action配置域名未配置", requestInfo);
            if (StringUtils.isBlank(fkfidFiled))
                return ActionUtil.handleResult("action配置付款方id字段未配置", requestInfo);
            if (StringUtils.isBlank(fjzdFiled))
                return ActionUtil.handleResult("action附件字段名未配置", requestInfo);
            if (StringUtils.isBlank(qtfjidField))
                return ActionUtil.handleResult("action其他附件id字段名未配置", requestInfo);
            // 获取流程信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            String requestId = actionInfo.getRequestId();
            //流程表单名
            String formtableName = actionInfo.getFormtableName();
            //主表数据
            Map<String, String> mainData = actionInfo.getMainData();
            //付款方id
            String fkfid = mainData.get(fkfidFiled);
            //上传其他业务附件集合
            List<String> docIds = new ArrayList<>();
            // 附件字段存在主表当中
            docIds.addAll(getDocumentIds(mainData, fjzdFiled));
            //调用ESB 发送接口
            //附件ID集合 attachIds
            ArrayList<String> attachIds = new ArrayList<>();
            if (!docIds.isEmpty()) {
                errorMsg = sendApi(docIds, fkfid, attachIds);
            }
            if (!attachIds.isEmpty()) {
                String updateSql = "update " + formtableName + " set " + qtfjidField + " = ?  where requestId = " + requestId;
                logger.writeLog("updateSql:"+updateSql);
                recordSet.executeUpdate(updateSql, String.join(",", attachIds));
            }
        } catch (Exception e) {
            errorMsg = "系统异常：" + SDUtil.getExceptionDetail(e);
            logger.writeLog("UploadOtherBusinessAttachmentsAction Exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            if (recordSet != null) {
                DBUtil.clearThreadLocalRecordSet();
            }
        }
        logger.writeLog("UploadOtherBusinessAttachmentsAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    /**
     * 获取文档 ID 列表
     *
     * @param data
     * @param fields 配置的字段（以逗号分隔）
     * @return 文档 ID 列表
     */
    private List<String> getDocumentIds(Map<String, String> data, String fields) {
        List<String> docIds = new ArrayList<>();
        if (StringUtils.isNotBlank(fields)) {
            String[] fieldArray = fields.split(",");
            for (String field : fieldArray) {
                String docId = Util.null2String(data.get(field));
                if (StringUtils.isNotBlank(docId)) {
                    String[] docs = docId.split(",");
                    docIds.addAll(Arrays.asList(docs));
                }
            }
        }
        return docIds;
    }


    //调用上传其他业务附件
    public String sendApi(List<String> docIds, String fkfid, ArrayList<String> attachIds) {
        String ereMsg = "";
        try {
            String token = "";
            JSONObject params = new JSONObject();
            Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
            String random = ApiUtil.generateSecureRandomString();
            params.put("appId", Util.null2String(environmentConfig.get("APPID")));
            params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
            params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
            params.put("random", random);
            params.put("sign", ApiUtil.getTokenSign(random));
            EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
            if (er.isSuccess()) {
                JSONObject data = er.getData();
                if (data != null) {
                    token = Util.null2String(data.getString("access_token"));
                }
            } else {
                logger.writeLog("Action getToken error : " + JSONObject.toJSONString(er));
            }
            if (StringUtils.isNotBlank(token)) {
                JSONArray attachments = new JSONArray();
                List<DocFileInfo> docFileInfos = DocUtil.getDocFileInfoByDocIds(String.join(",", docIds));
                if (!docFileInfos.isEmpty()) {
                    for (int i = 0; i < docFileInfos.size(); i++) {
                        JSONObject attachment = new JSONObject();
                        DocFileInfo docFileInfo = docFileInfos.get(i);
                        String fileName = docFileInfo.getFileName();
                        String noLoginFileDownloadUrl = docFileInfo.getNoLoginFileDownloadUrl();
                        attachment.put("attachType", "OTHER");
                        attachment.put("fileUrl", host + noLoginFileDownloadUrl);
                        attachment.put("fileName", fileName);
                        attachments.add(attachment);
                    }
                }
                if (!attachments.isEmpty()) {
                    params.put("orgId", fkfid);
                    params.put("attachments", attachments);
                    String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                    String sign = ApiUtil.generateMD5Sign(generateSignature);
                    params.put("sign", sign);
                    params.put("access_token", token);
                    EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());

                    if (evr.isSuccess()) {
                        JSONObject data = evr.getData();
                        if (data != null) {
                            logger.writeLog("data:"+JSONObject.toJSONString(data));
                            String code = Util.null2String(data.getString("code"));
                            String msg = Util.null2String(data.getString("msg"));
                            if (StringUtils.isNotBlank(msg) && msg.equals("成功") && StringUtils.isNotBlank(code) && code.equals("success")) {
                                String dataStr = Util.null2String(data.getString("data"));
                                if(StringUtils.isNotBlank(dataStr)){
                                    JSONObject jsonObject = JSONObject.parseObject(dataStr);
                                    logger.writeLog("dataStr:"+JSONObject.toJSONString(jsonObject));
                                    String attachResult = Util.null2String(jsonObject.get("attachResults"));
                                    if(StringUtils.isNotBlank(attachResult)){
                                        JSONArray attachResults = JSONArray.parseArray(attachResult);
                                        logger.writeLog("attachResults:"+JSONArray.toJSONString(attachResults));
                                        for (int i = 0; i < attachResults.size(); i++) {
                                            JSONObject attach = (JSONObject) attachResults.get(i);
                                            String status = Util.null2String(attach.get("status"));
                                            String message = Util.null2String(attach.get("message"));
                                            if (StringUtils.isNotBlank(message) && message.equals("上传成功") && StringUtils.isNotBlank(status) && status.equals("success")) {
                                                String attachId = Util.null2String(attach.get("attachId"));
                                                attachIds.add(attachId);
                                            }else {
                                                ereMsg = ereMsg + message;
                                            }
                                        }
                                    }
                                }

                            } else {
                                ereMsg = eventLogoType+"事件"+msg;
                            }
                        }
                    } else {
                        ereMsg = eventLogoType+":ESB上传其他业务附件失败";
                    }
                }
            }
        } catch (Exception e) {
            ereMsg = "调用上传其他业务附件Exception:" + SDUtil.getExceptionDetail(e);
            logger.writeLog("调用上传其他业务附件Exception:" + SDUtil.getExceptionDetail(e));
        }
        return ereMsg;
    }
}


