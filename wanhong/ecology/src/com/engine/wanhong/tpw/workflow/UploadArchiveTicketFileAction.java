package com.engine.wanhong.tpw.workflow;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.*;


/**
 * @FileName UploadArchiveTicketFileAction
 * @Description 上传归档票据文件
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/3
 */
@Getter
@Setter
public class UploadArchiveTicketFileAction implements Action {
    private BaseBean logger = new BaseBean();
    private String eventLogoType;
    //付款方企业id
    private String fkfidFiled;
    //字段所在表    0:主表 1:明细表
    private String zdszbFiled;
    //字段明细表id
    private String zdszbmxidFiled;
    //附件字段名 多个字段之间用逗号拼接
    private String fjzdFiled;
    //当前服务器地址 https://oa-new.wanhong.sh.cn/
    private String host;
    //查验结果
    private String cyjgField;
    //票据id
    private String pjidField;

    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = ""; // 错误信息
        RecordSet recordSet = null;
        try {
            logger.writeLog("UploadArchiveTicketFileAction----START");
            // 初始化 RecordSet
            recordSet = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isBlank(eventLogoType))
                return ActionUtil.handleResult("action配置事件标识未配置", requestInfo);
            if (StringUtils.isBlank(fkfidFiled))
                return ActionUtil.handleResult("action配置付款方id字段未配置", requestInfo);
            if (StringUtils.isBlank(host))
                return ActionUtil.handleResult("action配置域名未配置", requestInfo);
            if (StringUtils.isBlank(zdszbFiled))
                return ActionUtil.handleResult("action附件字段所在表未配置", requestInfo);
            if (StringUtils.isBlank(fjzdFiled))
                return ActionUtil.handleResult("action附件字段名未配置", requestInfo);
            if (StringUtils.isBlank(cyjgField))
                return ActionUtil.handleResult("action查验结果字段名未配置", requestInfo);
            if (StringUtils.isBlank(pjidField))
                return ActionUtil.handleResult("action票据id字段名未配置", requestInfo);

            // 获取流程信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            String requestId = actionInfo.getRequestId();
            //流程表单名
            String formtableName = actionInfo.getFormtableName();
            //主表数据
            Map<String, String> mainData = actionInfo.getMainData();
            //付款方id
            String fkfid = mainData.get(fkfidFiled);
            //票据id
            String pjid = mainData.get(pjidField);
            //明细表集合
            Map<Integer, List<Map<String, String>>> detailData = actionInfo.getDetailData();
            //需要查验的附件集合
            List<String> docIds = new ArrayList<>();
            if ("0".equals(zdszbFiled)) {
                // 附件字段存在主表当中
                docIds.addAll(getDocumentIds(mainData, fjzdFiled));
            } else {
                // 附件字段存在明细表当中
                if (StringUtils.isBlank(zdszbmxidFiled))
                    return ActionUtil.handleResult("action配置字段明细表id未配置", requestInfo);
                int detailTableIndex = Integer.parseInt(zdszbmxidFiled);
                List<Map<String, String>> detailRows = detailData.get(detailTableIndex);
                if (detailRows != null) {
                    docIds.addAll(getDocumentIds(detailRows, fjzdFiled));
                }
            }


            //调用ESB 发送接口
            //票据id tickeIds
            ArrayList<String> tickeIds = new ArrayList<>();
            if (!docIds.isEmpty()) {
                for (int i = 0; i < docIds.size(); i++) {
                    String docid = docIds.get(i);
                    String ereMsg = sendApi(docid, fkfid, tickeIds);
                    if (StringUtils.isNotBlank(ereMsg)) {
                        errorMsg = ereMsg;
                        break;
                    } else {

                    }
                }
            }
            String updateSql = "";
            if (StringUtils.isNotBlank(errorMsg)) { //不通过
                updateSql = "update " + formtableName + " set " + pjidField + " = ? , " + cyjgField + " = 1 where requestId = " + requestId;
            } else {//通过
                updateSql = "update " + formtableName + " set " + pjidField + " = ? , " + cyjgField + " = 0 where requestId = " + requestId;
            }
            if (StringUtils.isNotBlank(updateSql)) {
                if(StringUtils.isNotBlank(pjid)){
                    if(!tickeIds.isEmpty()){
                        recordSet.executeUpdate(updateSql, pjid+","+String.join(",", tickeIds));
                    }
                }else {
                    recordSet.executeUpdate(updateSql, String.join(",", tickeIds));
                }
            }
        } catch (Exception e) {
            errorMsg = "系统异常：" + SDUtil.getExceptionDetail(e);
            logger.writeLog("UploadArchiveTicketFileAction Exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            if (recordSet != null) {
                DBUtil.clearThreadLocalRecordSet();
            }
        }

        logger.writeLog("UploadArchiveTicketFileAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    /**
     * 获取文档 ID 列表
     *
     * @param data
     * @param fields 配置的字段（以逗号分隔）
     * @return 文档 ID 列表
     */
    private List<String> getDocumentIds(Map<String, String> data, String fields) {
        List<String> docIds = new ArrayList<>();
        if (StringUtils.isNotBlank(fields)) {
            String[] fieldArray = fields.split(",");
            for (String field : fieldArray) {
                String docId = Util.null2String(data.get(field));
                if (StringUtils.isNotBlank(docId)) {
                    String[] docs = docId.split(",");
                    docIds.addAll(Arrays.asList(docs));
                }
            }
        }
        return docIds;
    }

    /**
     * 获取文档 ID 列表 明细表行
     *
     * @param rows   数据源 明细表行
     * @param fields 配置的字段（以逗号分隔）
     * @return 文档 ID 列表
     */
    private List<String> getDocumentIds(List<Map<String, String>> rows, String fields) {
        List<String> docIds = new ArrayList<>();
        if (StringUtils.isNotBlank(fields) && rows != null) {
            String[] fieldArray = fields.split(",");
            for (Map<String, String> row : rows) {
                for (String field : fieldArray) {
                    String docId = Util.null2String(row.get(field));
                    if (StringUtils.isNotBlank(docId)) {
                        String[] docs = docId.split(",");
                        docIds.addAll(Arrays.asList(docs));
                    }
                }
            }
        }
        return docIds;
    }


    //调用上传归档票据文件接口
    public String sendApi(String docid, String fkfid, ArrayList<String> tickeIds) {
        String ereMsg = "";
        try {
            String token = "";
            JSONObject params = new JSONObject();
            Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
            String random = ApiUtil.generateSecureRandomString();
            params.put("appId", Util.null2String(environmentConfig.get("APPID")));
            params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
            params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
            params.put("random", random);
            params.put("sign", ApiUtil.getTokenSign(random));
            EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
            if (er.isSuccess()) {
                JSONObject data = er.getData();
                if (data != null) {
                    token = Util.null2String(data.getString("access_token"));
                }
            } else {
                logger.writeLog("Action getToken error : " + JSONObject.toJSONString(er));
            }
            if (StringUtils.isNotBlank(token)) {
                //处理数据
                List<DocFileInfo> docFileInfos = DocUtil.getDocFileInfoByDocId(docid);
                if (!docFileInfos.isEmpty()) {
                    DocFileInfo docFileInfo = docFileInfos.get(0);
                    logger.writeLog("文档docid:" + docid + "  " + JSONObject.toJSONString(docFileInfo));
                    String fileName = docFileInfo.getFileName();
                    String noLoginFileDownloadUrl = docFileInfo.getNoLoginFileDownloadUrl();
                    params.put("orgId", fkfid);
                    params.put("fileUrl", host + noLoginFileDownloadUrl);
                    params.put("fileName", fileName);
                    params.put("uploadType", "0");
                    String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                    String sign = ApiUtil.generateMD5Sign(generateSignature);
                    params.put("sign", sign);
                    params.put("access_token", token);
                    EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                    if (evr.isSuccess()) {
                        JSONObject data = evr.getData();
                        if (data != null) {
                            String code = Util.null2String(data.getString("code"));
                            String msg = Util.null2String(data.getString("msg"));
                            if (StringUtils.isNotBlank(msg) && msg.equals("成功") && StringUtils.isNotBlank(code) && code.equals("success")) {
                                String dataStr = Util.null2String(data.getString("data"));
                                JSONObject jsonObject = JSONObject.parseObject(dataStr);
                                String tickeId = Util.null2String(jsonObject.getString("invoiceId"));
                                tickeIds.add(tickeId);
                            } else {
                                ereMsg = eventLogoType +" " + fileName +":  "+ msg;
                            }
                        }
                    } else {
                        ereMsg = eventLogoType+"ESB上传归档票据文件接口失败";
                    }
                }
            } else {
                ereMsg = "获取token失败";
            }
        } catch (Exception e) {
            ereMsg = "调用上传归档票据文件接口Exception:" + SDUtil.getExceptionDetail(e);
            logger.writeLog("调用上传归档票据文件接口Exception:" + SDUtil.getExceptionDetail(e));
        }
        return ereMsg;
    }

    /**
     * 生成签名
     * @param params 请求参数
     * @return 签名后的字符串
     */

 /*   public static void main(String[] args) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appId", "lQggN8LM2ZW2vKKPI9PbKntdSE4YlVcr");
        jsonObject.put("appSecret", "CyU0oITiAgXBYP7PWKXXFxLixn4RvbFI0Y5aUer2Ex7bjO8TqOPg7mtzzEsKJmgo");
        jsonObject.put("grantType", "client_credentials");
        jsonObject.put("random", "wq4ESAfN");
        jsonObject.put("orgId", "1863769310548201472");
        jsonObject.put("uploadType", "0");
        jsonObject.put("fileUrl", "https://oa-new.wanhong.sh.cn/weaver/weaver.file.FileDownload?download=1&fileid=6306&ddcode=3766393637343333393132626234666564363032343663313563336131373961");
        jsonObject.put("fileName", "24317000000086735731-上海长宁工业资产经营有限公司-149000.00.pdf");
        System.out.println(JSONObject.toJSONString(jsonObject));
        String s = ApiUtil.generateSignature(jsonObject);
        System.out.println(s);
        String s1 = ApiUtil.generateMD5Sign("lQggN8LM2ZW2vKKPI9PbKntdSE4YlVcr|CyU0oITiAgXBYP7PWKXXFxLixn4RvbFI0Y5aUer2Ex7bjO8TqOPg7mtzzEsKJmgo|24317000000086735731-上海长宁工业资产经营有限公司-149000.00.pdf|https://oa-new.wanhong.sh.cn/weaver/weaver.file.FileDownload?download=1&fileid=6306&ddcode=3766393637343333393132626234666564363032343663313563336131373961|client_credentials|1863769310548201472|wq4ESAfN|0|CyU0oITiAgXBYP7PWKXXFxLixn4RvbFI0Y5aUer2Ex7bjO8TqOPg7mtzzEsKJmgo");
        String sign = ApiUtil.generateMD5Sign( "lQggN8LM2ZW2vKKPI9PbKntdSE4YlVcr|CyU0oITiAgXBYP7PWKXXFxLixn4RvbFI0Y5aUer2Ex7bjO8TqOPg7mtzzEsKJmgo|24317000000086735731-上海长宁工业资产经营有限公司-149000.00.pdf|https://oa-new.wanhong.sh.cn/weaver/weaver.file.FileDownload?download=1&fileid=6306&ddcode=3766393637343333393132626234666564363032343663313563336131373961|client_credentials|1863769310548201472|wq4ESAfN|0|CyU0oITiAgXBYP7PWKXXFxLixn4RvbFI0Y5aUer2Ex7bjO8TqOPg7mtzzEsKJmgo");
        System.out.println(s1);
        System.out.println(sign);
    }*/
}

