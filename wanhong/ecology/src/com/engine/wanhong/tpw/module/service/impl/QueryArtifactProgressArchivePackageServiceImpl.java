package com.engine.wanhong.tpw.module.service.impl;

import com.engine.core.impl.Service;
import com.engine.wanhong.tpw.module.cmd.GenerateLinkDownloadArchivePackageCmd;
import com.engine.wanhong.tpw.module.cmd.QueryArtifactProgressArchivePackageCmd;
import com.engine.wanhong.tpw.module.service.QueryArtifactProgressArchivePackageService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class QueryArtifactProgressArchivePackageServiceImpl extends Service implements QueryArtifactProgressArchivePackageService {

    @Override
    public Map<String, Object> queryArtifactProgressArchivePackage(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryArtifactProgressArchivePackageCmd(params, user));
    }
}
