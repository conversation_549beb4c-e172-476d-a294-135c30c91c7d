package com.engine.wanhong.tpw.module.service.impl;

import com.engine.core.impl.Service;
import com.engine.wanhong.tpw.module.service.GenerateLinkDownloadArchivePackageService;
import com.engine.wanhong.tpw.module.cmd.GenerateLinkDownloadArchivePackageCmd;
import weaver.hrm.User;

import java.util.Map;

public class GenerateLinkDownloadArchivePackageServiceImpl extends Service implements GenerateLinkDownloadArchivePackageService {
    @Override
    public Map<String, Object> generateLinkDownloadArchivePackage(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GenerateLinkDownloadArchivePackageCmd(params, user));
    }
}
