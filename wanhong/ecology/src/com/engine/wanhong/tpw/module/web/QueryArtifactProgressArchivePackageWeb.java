package com.engine.wanhong.tpw.module.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.wanhong.tpw.module.service.GenerateLinkDownloadArchivePackageService;
import com.engine.wanhong.tpw.module.service.QueryArtifactProgressArchivePackageService;
import com.engine.wanhong.tpw.module.service.impl.GenerateLinkDownloadArchivePackageServiceImpl;
import com.engine.wanhong.tpw.module.service.impl.QueryArtifactProgressArchivePackageServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class QueryArtifactProgressArchivePackageWeb {
    private QueryArtifactProgressArchivePackageService getService(User user) {
        return ServiceUtil.getService(QueryArtifactProgressArchivePackageServiceImpl.class, user);
    }
    @POST
    @Path("/queryArtifactProgress")
    @Produces({MediaType.TEXT_PLAIN})
    public String queryArtifactProgressArchivePackage(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            result = getService(user).queryArtifactProgressArchivePackage(ParamUtil.request2Map(request), user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}
