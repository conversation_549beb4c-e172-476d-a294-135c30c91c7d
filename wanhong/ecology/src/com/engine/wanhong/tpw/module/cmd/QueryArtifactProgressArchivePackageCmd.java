package com.engine.wanhong.tpw.module.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;


import java.util.*;

public class QueryArtifactProgressArchivePackageCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;

    public QueryArtifactProgressArchivePackageCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog("QueryArtifactProgressArchivePackageCmd---START");
        bb.writeLog("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            String orgId = "";
            String archivePackageId = "";
            if (params != null && !params.isEmpty()) {
                String billid = Util.null2String(params.get("billid"));
                String eventLogoType = Util.null2String(params.get("eventLogoType"));
                if (StringUtils.isNotBlank(billid) && StringUtils.isNotBlank(eventLogoType)) {
                    List<Map<String, Object>> mainDataByReqId = getMainDataByReqId(billid);
                    if (!mainDataByReqId.isEmpty()) {
                        Map<String, Object> mainData = mainDataByReqId.get(0);
                        orgId = Util.null2String(mainData.get("fkfqyid"));
                        archivePackageId = Util.null2String(mainData.get("gdbid"));
                    }
                    if (StringUtils.isNotBlank(orgId) && StringUtils.isNotBlank(archivePackageId)) {
                        String token = "";
                        JSONObject params = new JSONObject();
                        Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
                        String random = ApiUtil.generateSecureRandomString();
                        params.put("appId", Util.null2String(environmentConfig.get("APPID")));
                        params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
                        params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
                        params.put("random", random);
                        params.put("sign", ApiUtil.getTokenSign(random));
                        EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
                        if (er.isSuccess()) {
                            JSONObject data = er.getData();
                            if (data != null) {
                                token = Util.null2String(data.getString("access_token"));
                            }
                        } else {
                            errorMsg = "Action getToken error : " + JSONObject.toJSONString(er);
                        }
                        if (StringUtils.isNotBlank(token)) {
                            JSONObject param = new JSONObject();
                            params.put("orgId", orgId);
                            params.put("archivePackageId", archivePackageId);
                            String generateSignature = ApiUtil.generateSignature(params) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                            String sign = ApiUtil.generateMD5Sign(generateSignature);
                            params.put("sign", sign);
                            params.put("access_token", token);
                            EsbEventResult evr = EsbUtil.callEsbEvent(eventLogoType, params.toJSONString());
                            if (evr.isSuccess()) {
                                JSONObject data = evr.getData();
                                if (data != null) {
                                    String dataStr = Util.null2String(data.getString("data"));
                                    if (StringUtils.isNotBlank(dataStr)) {
                                        JSONObject jsonObject = JSONObject.parseObject(dataStr);
                                        String fileUrl = Util.null2String(jsonObject.getString("fileUrl"));
                                        String productStatus = Util.null2String(jsonObject.getString("productStatus"));
                                        String productStatusMessage = Util.null2String(jsonObject.getString("productStatusMessage"));
                                        if (StringUtils.isNotBlank(archivePackageId)) {
                                            //回写建模
                                            String writebackModuleSql = "update uf_gdb set gdbxzurl = '"+fileUrl+"' , gdbzt  = "+productStatus+" where id = "+billid;
                                            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                                            recordSet.executeUpdate(writebackModuleSql);
                                        }
                                    }
                                }
                            } else {
                                errorMsg = "ESB提交查询归档包的制品进度接口失败";
                            }
                        } else {
                            errorMsg = "token is null";
                        }
                    } else {
                        errorMsg = "orgId is null or archivePackageId is null";
                    }
                } else {
                    errorMsg = "params : billid is null or host is null";
                }
            }
        } catch (Exception e) {

        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog("QueryArtifactProgressArchivePackageCmd---END");
        return result;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public List<Map<String, Object>> getMainDataByReqId(String id) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        List<Map<String, Object>> list = new ArrayList<>();
        String sql = " select * from uf_gdb where id = ?";
        if (rs.executeQuery(sql, id)) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }

}

