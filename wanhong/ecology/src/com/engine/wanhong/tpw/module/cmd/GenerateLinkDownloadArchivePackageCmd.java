package com.engine.wanhong.tpw.module.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.wanhong.tpw.common.util.ApiUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class GenerateLinkDownloadArchivePackageCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;

    public GenerateLinkDownloadArchivePackageCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog("GenerateLinkDownloadArchivePackageCmd---START");
        bb.writeLog("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        String url = "";
        try {
            String orgId = "";
            String archivePackageId = "";
            if (params != null && !params.isEmpty()) {
                String billid = Util.null2String(params.get("billid"));
                String host = Util.null2String(params.get("apiUrl"));
                if (StringUtils.isNotBlank(billid) || StringUtils.isNotBlank(host)) {
                    List<Map<String, Object>> mainDataByReqId = getMainDataByReqId(billid);
                    if (!mainDataByReqId.isEmpty()) {
                        Map<String, Object> mainData = mainDataByReqId.get(0);
                        orgId = Util.null2String(mainData.get("fkfqyid"));
                        archivePackageId = Util.null2String(mainData.get("gdbid"));
                    }
                    if (StringUtils.isNotBlank(orgId) && StringUtils.isNotBlank(archivePackageId)) {
                        String token = "";
                        JSONObject params = new JSONObject();
                        Map<String, String> environmentConfig = ApiUtil.getEnvironmentConfig();
                        String random = ApiUtil.generateSecureRandomString();
                        params.put("appId", Util.null2String(environmentConfig.get("APPID")));
                        params.put("appSecret", Util.null2String(environmentConfig.get("AppSecret")));
                        params.put("grantType", Util.null2String(environmentConfig.get("grantType")));
                        params.put("random", random);
                        params.put("sign", ApiUtil.getTokenSign(random));
                        EsbEventResult er = EsbUtil.callEsbEvent("getToken", params.toJSONString());
                        if (er.isSuccess()) {
                            JSONObject data = er.getData();
                            if (data != null) {
                                token = Util.null2String(data.getString("access_token"));
                            }
                        } else {
                            errorMsg = "Action getToken error : " + JSONObject.toJSONString(er);
                        }
                        if (StringUtils.isNotBlank(token)) {
                            JSONObject param = new JSONObject();
                            param.put("orgId", orgId);
                            param.put("archivePackageId", archivePackageId);
                            String generateSignature = ApiUtil.generateSignature(param) + "|" + Util.null2String(environmentConfig.get("AppSecret"));
                            String sign = ApiUtil.generateMD5Sign(generateSignature);

                            // Step 1: 创建参数对象并转化为 JSON 字符串
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("orgId", orgId);
                            jsonObject.put("archivePackageId", archivePackageId);
                            // Step 2: Base64 编码
                            String base64Encoded = encodeBase64(JSONObject.toJSONString(jsonObject));
                            // Step 3: URL 编码
                            String urlEncoded = encodeUrl(base64Encoded);
                            url = host + "?Authorization_Token=" + token + "&Authorization_Sign=" + sign + "&Params=" + urlEncoded;
                        } else {
                            errorMsg = "token is null";
                        }
                    } else {
                        errorMsg = "orgId is null or archivePackageId is null";
                    }
                } else {
                    errorMsg = "params : billid is null or host is null";
                }
            }
        } catch (Exception e) {

        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        result.put("url", url);
        bb.writeLog("result:" + result);
        bb.writeLog("GenerateLinkDownloadArchivePackageCmd---END");
        return result;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    // Base64 编码方法
    private static String encodeBase64(String data) {
        return Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8));
    }

    // URL 编码方法
    private static String encodeUrl(String data) throws UnsupportedEncodingException {
        return URLEncoder.encode(data, "UTF-8");
    }

    public List<Map<String, Object>> getMainDataByReqId(String id) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        List<Map<String, Object>> list = new ArrayList<>();
        String sql = " select * from uf_gdb where id = ?";
        if (rs.executeQuery(sql, id)) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }

}
