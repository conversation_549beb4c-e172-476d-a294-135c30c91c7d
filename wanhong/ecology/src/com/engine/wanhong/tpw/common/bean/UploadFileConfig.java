package com.engine.wanhong.tpw.common.bean;

import com.engine.parent.query.util.QueryUtil;
import lombok.Data;
import weaver.conn.RecordSet;

import java.util.ArrayList;

@Data
public class UploadFileConfig {
    /**
     * 建模表名
     */
    public static final String TABLE_NAME = "uf_fpcy";

    /**
     * 数据id
     */
    private String id;
    /**
     * 流程选择
     */
    private String lcxz;
    /**
     * 是否启用 0启用
     */
    private String sfqy;

    /**
     * bz
     */
    private String bz;
    /**
     * 明细1配置
     */
    private ArrayList<DetailConfig> detailConfig;

    /**
     * @FileName VendorCheckConfig.java
     * @Description 明细1字段配置，所有字段都重复则action报错
     * <AUTHOR>
     * @Version v1.00
     * @Date 2024/2/19
     */
    @Data
    public static class DetailConfig {
        /**
         * 数据id
         */
        private String id;
        /**
         * 主表id
         */
        private String mainid;
        /**
         * 字段来源表类别
         */
        private String zdlyblb;
        /**
         * 明细下标
         */
        private String mxxb;
        /**
         * 字段
         */
        private String zd;
    }


    /**
     * 获取配置
     *
     * @param workflowid
     * @return
     */
    public static UploadFileConfig getConfig(String workflowid) {
        UploadFileConfig config = null;
        ArrayList<DetailConfig> detailConfig;
        RecordSet rs = new RecordSet();
        String mainSql = "select * from " + TABLE_NAME + " where sfqy = 0 and lcxz = ?";
        if (rs.executeQuery(mainSql, workflowid)) {
            //根据流程路径id获取配置
            config = QueryUtil.getObj(rs, UploadFileConfig.class);
            if (config != null) {
                String detailSql = "select * from " + TABLE_NAME + "_dt1 where mainid = ?";
                if (rs.executeQuery(detailSql, config.getId())) {
                    detailConfig = (ArrayList<DetailConfig>) QueryUtil.getObjList(rs, DetailConfig.class);
                    if (detailConfig != null && !detailConfig.isEmpty()) {
                        config.setDetailConfig(detailConfig);
                    }
                }
            }
        }
        return config;
    }
}

