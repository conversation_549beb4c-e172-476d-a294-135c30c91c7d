package com.engine.wanhong.tpw.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import java.nio.charset.StandardCharsets;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;

public class ApiUtil {

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final int LENGTH = 8;
    private static BaseBean bb = new BaseBean();

    // 生成 8 位安全的随机字符串
    public static String generateSecureRandomString() {
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder stringBuilder = new StringBuilder(LENGTH);

        for (int i = 0; i < LENGTH; i++) {
            int index = secureRandom.nextInt(CHARACTERS.length());
            stringBuilder.append(CHARACTERS.charAt(index));
        }

        return stringBuilder.toString();
    }

    // 获取配置信息
    public static Map<String, String> getEnvironmentConfig() {
        Map<String, String> envMap = new HashMap<>();
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        String sql = "SELECT * FROM uf_hjpz WHERE sfqy = 0";
        recordSet.executeQuery(sql);
        while (recordSet.next()) {
            envMap.put("APPID", recordSet.getString("appid"));
            envMap.put("AppSecret", recordSet.getString("appsecret"));
            //token地址
            envMap.put("tokenHostApi", recordSet.getString("hqtokendqqdz"));
            //查询组织结构地址
            envMap.put("orgStructHostApi", recordSet.getString("hqcxzzjgdqqdz"));
            //上传归档票据文件地址
            envMap.put("uploadBillFile", recordSet.getString("hqscgdpjwjdqqdz"));
            envMap.put("grantType", recordSet.getString("grantType"));
        }
        bb.writeLog("获取到的envMap："+envMap);
        return envMap;
    }

    // 获取 Token
//    public static String getToken(String randomString, String sign,JSONObject params) {
//        // 您可以在此方法中从某个API或数据库获取Token
//        String token = "";
//        try {
//            Map<String, String> environmentConfig = getEnvironmentConfig();
//            String tokenHostApi = Util.null2String(environmentConfig.get("tokenHostApi"));
//            if(StringUtils.isNotBlank(tokenHostApi)){
//                params.put("appId",Util.null2String(environmentConfig.get("APPID")));
//                params.put("appSecret",Util.null2String(environmentConfig.get("AppSecret")));
//                params.put("grantType",Util.null2String(environmentConfig.get("grantType")));
//                params.put("random",randomString);
//                params.put("sign",sign);
//                bb.writeLog("获取到的token响应信息"+JSONObject.toJSONString(params));
//                RestResult restResult = HttpUtil.postData(tokenHostApi, JSONObject.toJSONString(params));
//                bb.writeLog("获取到的token响应信息"+JSONObject.toJSONString(restResult));
//                if(restResult.isSuccess()){
//                    String body = restResult.getResponseInfo().getBody();
//                    if(StringUtils.isNotBlank(body)){
//                        JSONObject bodyObject = JSONObject.parseObject(body);
//                        if(bodyObject.containsKey("data")){
//                            String data = Util.null2String(bodyObject.getString("data"));
//                            if(StringUtils.isNotBlank(data)){
//                                JSONObject dataObject = JSONObject.parseObject(data);
//                                if( dataObject!= null && dataObject.containsKey("access_token")){
//                                    token = Util.null2String(dataObject.getString("access_token"));
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }catch (Exception e){
//            bb.writeLog("getToken Exception"+SDUtil.getExceptionDetail(e));
//        }
//        return token;
//    }

    // 使用 MD5 生成签名
    public static String generateMD5Sign(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                hexString.append(String.format("%02x", b));
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    // 获取签名
    public static String getTokenSign(String randomString) {
        String sign = "";
        try {
            Map<String, String> environmentConfig = getEnvironmentConfig();
            String APPID = Util.null2String(environmentConfig.get("APPID"));
            String AppSecret = Util.null2String(environmentConfig.get("AppSecret"));
            String grantType = Util.null2String(environmentConfig.get("grantType"));
            if (StringUtils.isNotBlank(APPID) && StringUtils.isNotBlank(AppSecret) && StringUtils.isNotBlank(grantType)) {
                // 构造签名字符串
                String signString = APPID + "|" + AppSecret + "|" + grantType + "|" + randomString+"|"+AppSecret;
                // 使用 MD5 对签名字符串加密
                sign = generateMD5Sign(signString);
            } else {
                bb.writeLog("APPID, AppSecret, 或者 grantType 为空，无法生成签名！");
            }
            bb.writeLog("加签后的字符串",sign);
        }catch (Exception e){
            bb.writeLog("getTokenSign加签 Exception :",SDUtil.getExceptionDetail(e));
        }
        return sign;
    }



    public static String uploadArchiveFile(JSONObject params) {
        String errMsg = "";
//        try {
//            Map<String, String> environmentConfig = getEnvironmentConfig();
//            String randomString = generateSecureRandomString();
//            //获取签名
//            String sign = getSign(randomString,Util.null2String(environmentConfig.get("AppSecret")) ,params);
//            //获取token
//            String token = getToken(randomString,getTokenSign(randomString));
//            String uploadBillFile = environmentConfig.get("uploadBillFile");
//            Map<String, String> headers = new HashMap<>();
//            headers.put("Authorization_Token", token);
//            headers.put("Authorization_Sign", sign);
//            //发送接口
//            RestResult restResult = HttpUtil.postDataWithHeader(uploadBillFile, headers,JSONObject.toJSONString(params));
//            if (restResult.isSuccess()) {
//                String body = restResult.getResponseInfo().getBody();
//            }
//        } catch (Exception e) {
//            errMsg = "调用上传归档票据文件接口"+SDUtil.getExceptionDetail(e);
//        }
        return errMsg;
    }

    public static String getSign(String randomString, String appSecret, JSONObject params) {
        String sign = "";
        String generateStr = generateSignature(params);
        if(StringUtils.isNotBlank(generateStr)){
            generateStr = generateStr + "|"+appSecret;
            sign = generateMD5Sign(generateStr);
        }
        return sign;
    }

    /**
     * 生成签名，仅拼接值
     * @param params 请求参数
     * @return 签名后的字符串
     */
    public static String generateSignature(JSONObject params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        List<String> keys = new ArrayList<>(params.keySet());

        // 排除不参与签名的参数
        keys.removeIf(key -> {
            Object value = params.get(key);
            return "sign".equals(key) || "base64".equals(key) || "access_token".equals(key) ||
                    value == null || value instanceof JSONObject || "".equals(Util.null2String(value)) ||
                    value instanceof List;
        });

        // 按参数名ASCII排序
        keys.sort(Comparator.naturalOrder());

        // 拼接参数值
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            if (sb.length() > 0) {
                sb.append("|");
            }
            sb.append(params.getString(key));
        }
        return sb.toString();
    }



}

