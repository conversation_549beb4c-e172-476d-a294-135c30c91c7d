package klt;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.toolbox.core.convert.Convert;
import weaver.toolbox.core.util.StrUtil;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

import java.util.ArrayList;

public class ChongXiaoAction extends BaseBean implements Action {
    private String field;

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    private String je;

    public String getJe() {
        return je;
    }

    public void setJe(String je) {
        this.je = je;
    }

    private String sfcx;

    public String getSfcx() {
        return sfcx;
    }

    public void setSfcx(String sfcx) {
        this.sfcx = sfcx;
    }

    @Override
    public String execute(RequestInfo request) {
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(request.getRequestid());
            int workflowid = Util.getIntValue(request.getWorkflowid());
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));
            writeLog("requestid = " + requestid);
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);

            RecordSet rs = new RecordSet();

            //region 获取流程主表数据
            //冲销金额
            String jine = "";
            double cxje = 0.00;
            //申请人
            String sqr  = "";
            //是否冲销
            String sfcx = "1";

            Property[] properties =request.getMainTableInfo().getProperty();
            for (int i = 0; i < properties.length; i++) {
                //主字段名称
                String name = properties[i].getName();

                if(StringUtils.isNotBlank(this.je) && this.je.equalsIgnoreCase(name)){
                    jine = Util.null2String(properties[i].getValue());
                }
                if(StringUtils.isNotBlank(this.field) && this.field.equalsIgnoreCase(name)){
                    sqr = Util.null2String(properties[i].getValue());
                }
                if(StringUtils.isNotBlank(this.sfcx) && this.sfcx.equalsIgnoreCase(name)){
                    sfcx = Util.null2String(properties[i].getValue());
                }
            }
            //endregion 获取流程主表数据

            writeLog("sfcx = " + sfcx);
            writeLog("jine = " + jine);

            if("0".equals(sfcx)){
                if(!"".equals(jine)){
                    cxje = Convert.toDouble(jine);
                }

                String sqlTemp = "select id,syjkje,jkze,cxze from uf_jktz where sqr='{}'";
                String sql     = StrUtil.format(sqlTemp,sqr);
                rs.execute(sql);

                if(rs.next()){
                    String mainid = rs.getString("id");

                    //剩余借款金额
                    double syjkje = rs.getDouble("syjkje");
                    //冲销金额
                    double cxze   = rs.getDouble("cxze");

                    writeLog("冲销金额 ===> " + cxje);
                    writeLog("剩余借款金额 ===> " + syjkje);
                    //冲销金额小于剩余借款金额
                    if(cxje <= syjkje){
                        ArrayList sqlList     = new ArrayList();

                        //插入明细表2的数据
                        String insertSqlTemp = "insert into uf_jktz_dt2(cxlc,cxje,mainid) values({},{},{})";
                        String insertSql     = StrUtil.format(insertSqlTemp,requestid,cxje,mainid);
                        sqlList.add(insertSql);
                        writeLog("insertSql ===> " + insertSql);

                        //更新主表数据
                        //剩余借款金额(新)
                        double syjkjeNew = syjkje - cxje;
                        //冲销金额(新)
                        double cxzeNew   = cxze   + cxje;
                        String updateSqlTemp = "UPDATE uf_jktz SET cxze={},syjkje={} WHERE id={}";
                        String updateSql     = StrUtil.format(updateSqlTemp,cxzeNew,syjkjeNew,mainid);
                        sqlList.add(updateSql);
                        writeLog("updateSql ===> " + updateSql);

                        ExecuteUtil.executeBatchSqlWithTrans(sqlList);
                    }else{
                        RequestManager rm = request.getRequestManager();
                        rm.setMessagecontent("冲销金额大于剩余借款金额,请重新填报冲销金额！");
                        return Action.FAILURE_AND_CONTINUE;
                    }
                }
            }
        }catch (Exception e){
            writeLog(e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }
}
