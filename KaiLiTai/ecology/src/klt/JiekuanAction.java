package klt;

import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.toolbox.core.convert.Convert;
import weaver.toolbox.core.util.StrUtil;
import weaver.toolbox.db.Entity;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.json.JSONArray;
import weaver.toolbox.json.JSONObject;
import weaver.toolbox.modeform.ModeFormUtil;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

import java.util.ArrayList;

public class JiekuanAction extends BaseBean implements Action {

    private String field;

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    @Override
    public String execute(RequestInfo request) {
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid  = Util.getIntValue(request.getRequestid());
            int workflowid = Util.getIntValue(request.getWorkflowid());
            int formid     = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));
            int cjr        = Util.getIntValue(request.getCreatorid());
            writeLog("requestid = " + requestid);
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);

            RecordSet rs  = new RecordSet();

            //region 获取流程主表数据
            //申请人
            String sqr  = "";
            //借款金额
            double jkje = 0;

            //取主表数据
            Property[] properties = request.getMainTableInfo().getProperty();
            for (int i = 0; i < properties.length; i++) {
                //主字段名称
                String name = properties[i].getName();

                //收款人姓名
                if(this.field.equalsIgnoreCase(name)){
                    sqr = Util.null2String(properties[i].getValue());
                }

                //本币金额
                if("bbje".equalsIgnoreCase(name)){
                    String jkjeTemp = Util.null2String(properties[i].getValue());
                           jkje     = Convert.toDouble(jkjeTemp);
                }
            }
            //endregion 获取流程主表数据

            //获取申请人的借款记录
            String qryRecTemp = "SELECT * FROM uf_jktz WHERE sqr='{}'";
            String sqlRec     = StrUtil.format(qryRecTemp,sqr);
            rs.execute(sqlRec);

            if(rs.getColCounts() == 0){
                //如果不存在，直接插入主表和明细表1的数据

                Entity data = new Entity();
                data.setTableName("uf_jktz");
                //主表数据
                data.set("sqr",sqr);
                //借款总额
                data.set("jkze",jkje);
                //冲销总额
                data.set("cxze",0);
                //剩余借款金额
                data.set("syjkje",jkje);

                //明细表1 借款数据
                JSONArray dt1Array = new JSONArray();
                JSONObject obj     = new JSONObject();
                //借款流程
                obj.put("jklc",requestid);
                //借款金额
                obj.put("jkje",jkje);
                dt1Array.add(obj);

                JSONObject dt1Obj  = new JSONObject();
                dt1Obj.put("dtName","uf_jktz_dt1");
                dt1Obj.put("data"  ,dt1Array);

                String formModeId    = QueryUtil.getFormmodeidByTablename("uf_jktz");
                int    formModeIdInt = Convert.toInt(formModeId);
                int    newDataId     = ModeFormUtil.insertModeData(data,formModeIdInt,cjr,dt1Obj);
                writeLog("新的借款记录 ===> " + newDataId);
            }else{
                //如果存在，则先插入明细表1的数据，再更新借款总额、计算剩余借款金额
                rs.beforFirst();
                if(rs.next()){
                    String mainid = rs.getString("id");
                    //借款总额
                    double jkze   = rs.getDouble("jkze");
                    //剩余借款金额
                    double syjkje = rs.getDouble("syjkje");

                    //借款总额(新)
                    double jkzeNew   = jkze   + jkje;
                    //剩余借款金额(新)
                    double syjkjeNew = syjkje + jkje;

                    ArrayList sqlList     = new ArrayList();

                    String updateMainTemp = "UPDATE uf_jktz SET jkze={},syjkje={} WHERE id={}";
                    String updateMain     = StrUtil.format(updateMainTemp,jkzeNew,syjkjeNew,mainid);
                    sqlList.add(updateMain);

                    String insertDt1Temp  = "INSERT INTO uf_jktz_dt1(mainid,jklc,jkje) VALUES({},{},{})";
                    String insertDt1      = StrUtil.format(insertDt1Temp,mainid,requestid,jkje);
                    sqlList.add(insertDt1);

                    ExecuteUtil.executeBatchSqlWithTrans(sqlList);
                }
            }
        } catch (Exception ex) {
            writeLog(ex.getMessage());

            RequestManager rm = request.getRequestManager();
            rm.setMessagecontent(ex.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }

}
