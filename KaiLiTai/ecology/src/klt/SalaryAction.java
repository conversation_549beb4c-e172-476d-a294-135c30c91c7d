package klt;


import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;


public class SalaryAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        RequestManager rm = request.getRequestManager();
        writeLog(this.getClass().getName() + " -- ActionDemo xxxx ");
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(request.getRequestid());   //requestid
            int cjr = Util.getIntValue(request.getCreatorid()); //创建人
            String requestname = Util.null2String(request.getRequestManager().getRequestname());    //标题
            int workflowid = Util.getIntValue(request.getWorkflowid()); //流程id
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));  //表单id
            String maintablename = "formtable_main_" + (Math.abs(formid));  //表名
            int wfstatus = request.getRequestManager().getCreater(); //（代码执行时）当前节点
            writeLog("requestid = " + requestid);   //统一日志类
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);

            String sql = "";
            RecordSet rs = new RecordSet(); //数据库操作类

            //--------业务逻辑 BEGIN---------
            sql = "select * from " + maintablename + " where requestid=" + requestid;
            rs.execute(sql);

            writeLog("sql -- " + sql);
            while (rs.next()) {
                String temp = Util.null2String(rs.getString("id"));    //通过字段从数据库操作类读取数据
                //。。。。。。
            }
            //--------业务逻辑 END---------

            writeLog("success");    //输出最终日志标示
        } catch (Exception ex) {
            // 手动报错处理，挂节点后附加操作，可以阻碍流程提交
            if (rm != null) {
                // rm.setMessage("message 错误");
                rm.setMessagecontent("CEO流程代理出错, 请联系系统管理员处理."); //设置错误提示，显示在流程表单上部
                retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }

            writeLog(ex);
        }
        return null;
    }
}
