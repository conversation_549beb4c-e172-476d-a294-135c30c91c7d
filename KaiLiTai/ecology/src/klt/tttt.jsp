<%--
  Created by IntelliJ IDEA.
  User: REN
  Date: 2020/11/20
  Time: 9:22
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
</head>
<body>
<script>
    //假期类型
    var qjlxid= WfForm.convertFieldNameToId("qjlx");
    //开始日期
    var ksrqid= WfForm.convertFieldNameToId("ksrq");
    //开始时间
    var kssjid= WfForm.convertFieldNameToId("kssj");
    //结束日期
    var jsrqid= WfForm.convertFieldNameToId("jsrq");
    //结束时间
    var jssjid= WfForm.convertFieldNameToId("jssj");
    //丧假类型
    var qsrysqrgxid= WfForm.convertFieldNameToId("qsrysqrgx1");
    //丧假 11  产假8 陪产假9 婚假10
    /*window.checkCustomize = function(){
        var flag = true;
        var slid= WfForm.convertFieldNameToId("gl");
        var sl_value = WfForm.getFieldValue(slid);
        var qjlx_value = WfForm.getFieldValue(qjlxid);
        if(sl_value>10 && qjlx_value =="14"){
            flag=false;
        }
        return flag;
    }
    */

    function changeTime(day){
        var qjlx_value = WfForm.getFieldValue(qjlxid);
        var ksrq_value = WfForm.getFieldValue(ksrqid);
        var kssj_value = WfForm.getFieldValue(kssjid);
        var qjlx_value = WfForm.getFieldValue(qjlxid);
        var date =new Date(ksrq_value);
        //9:00
        var str =kssj_value.split(":");
        var endtime=parseInt(str[0])-1
        if(endtime.length ==1){
            endtime="0"+endtime;
        }
        endtime=endtime+":00"
        date.setDate(date.getDate()+day);
        var m=date.getMonth()+1;
        if(m==1){
            m="0"+m;
        }
        var date_value=date.getFullYear()+'-'+m+'-'+date.getDate();
        console.log("date是："+date_value);
        WfForm.changeFieldValue(jsrqid, {value:date_value});
        WfForm.changeFieldValue(jssjid, {value:endtime});
        //WfForm.triggerFieldAllLinkage(ksrqid);
        //WfForm.triggerFieldAllLinkage(kssjid);
        WfForm.triggerFieldAllLinkage(jsrqid);
        WfForm.triggerFieldAllLinkage(jssjid);
        //WfForm.changeFieldAttr(jsrqid, 1);  //字段修改为只读
        // WfForm.changeFieldAttr(jssjid, 1);  //字段修改为只读
    }

    jQuery().ready(function(){
        WfForm.registerCheckEvent(WfForm.OPER_SAVE+","+WfForm.OPER_SUBMIT,function(callback){
            //... 执行自定义逻辑
            var flag = true;
            var slid= WfForm.convertFieldNameToId("gl");
            var sl_value = WfForm.getFieldValue(slid);
            var qjlx_value = WfForm.getFieldValue(qjlxid);
            console.log("司龄："+sl_value);
            if(sl_value>10 && qjlx_value =="14"){
                flag=false;
            }
            if(flag){
                callback();
            }else{
                WfForm.showConfirm("流程提交失败，工龄大于10请假类型无法选择司龄年假，请重新填写！",
                    function(){
                    },
                    function(){
                    });
            }
        });
        WfForm.bindFieldChangeEvent(ksrqid+","+kssjid+","+qjlxid+","+qsrysqrgxid,function(obj,id,value){
            var qjlx_value = WfForm.getFieldValue(qjlxid);
            var ksrq_value = WfForm.getFieldValue(ksrqid);
            var kssj_value = WfForm.getFieldValue(kssjid);
            var qjlx_value = WfForm.getFieldValue(qjlxid);
            console.log("假期类型："+qjlx_value);
            if(""!=ksrq_value && ""!=kssj_value){
                if("8"==qjlx_value ){
                    //产假
                    changeTime(128);
                }else if("11"==qjlx_value){
                    //丧假
                    var qsrysqrgx_value = WfForm.getFieldValue(qsrysqrgxid);
                    if("0"==qsrysqrgx_value ){
                        //直系
                        changeTime(3);
                    }else if("1"==qsrysqrgx_value ){
                        //非直系
                        changeTime(1);
                    }
                }else if("9"==qjlx_value){
                    //陪产假
                    changeTime(15);
                }else if("10"==qjlx_value){
                    //婚假
                    changeTime(10);
                }else{
                    WfForm.changeFieldAttr(jsrqid, 2);  //字段修改为只读
                    WfForm.changeFieldAttr(jssjid, 2);  //字段修改为只读
                    WfForm.changeFieldValue(jsrqid, {value:""});
                    WfForm.changeFieldValue(jssjid, {value:""});
                }
            }

        })

    })
</script>
<script>
    WfForm.registerCheckEvent(WfForm.OPER_SUBMIT,function(callback){
        //提交控制
        var fieldvalue = WfForm.getFieldValue("field7470");//获取时长
        var fieldvalue1 = WfForm.getFieldValue("field7464");//获取请假类型
        var fieldvalue2 = WfForm.getFieldValue("field7473");//获取丧假类型
        var fieldvalue3 = WfForm.getFieldValue("field10388");//获取工龄

        console.log(fieldvalue3);

        if(fieldvalue1 =='8' &&fieldvalue>128){
            alert("产假时长不可超过128天，无法提交该流程");

        }
        else if(fieldvalue1 =='10' &&fieldvalue>10){
            alert("婚假时长不可超过10天，无法提交该流程");

        }
        else if(fieldvalue1 =='9' &&fieldvalue>3){
            alert("陪产假时长不可超过3天，无法提交该流程");

        }
        else if(fieldvalue1 =='11' && fieldvalue2=='0' && fieldvalue>3){
            alert("直属丧假时长不可超过3天，无法提交该流程");

        }
        else if(fieldvalue1 =='11' && fieldvalue2=='1' && fieldvalue>1){
            alert("非直属丧假时长不可超过1天，无法提交该流程");

        }

        else{
            callback();
        }
    });
</script>

<script type="text/javascript">

    jQuery().ready(function(){
        WfForm.bindFieldChangeEvent("field11482,field11483",function(obj,id,value){
            var xm = WfForm.getFieldValue("field11482");
            if(""!=xm){
                WfForm.changeFieldAttr("field11483",1);
            }else if(""==xm){
                WfForm.changeFieldAttr("field11483",3);
            }
        })
    });
</script>
</body>
</html>
