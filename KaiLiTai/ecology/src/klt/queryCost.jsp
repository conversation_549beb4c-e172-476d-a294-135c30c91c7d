<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="utf-8"%>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");
    String zj =Util.null2String(request.getParameter("zj")).trim();
    BaseBean baseBean = new BaseBean();
    boolean res = false;
    String qry = "select zj,cs,zszxbz from uf_zjdyje where zj='"+zj+"'";
    baseBean.writeLog("uf_zjdyje sql========="+qry);
    rs.execute(qry);

    String jb="";
    String cs="";
    String zszxbz="";
    String str="";
    while(rs.next()){
        jb=  rs.getString("zj");
        cs= rs.getString("cs");
        zszxbz=rs.getString("zszxbz");
        str+="职级："+jb+",城市："+cs+",住宿执行标准："+zszxbz+";          ";
    }
    if(""!=str){
        res=true;
    }
    Map result = new HashMap();
    result.put("res", res);
    result.put("str", str);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>

