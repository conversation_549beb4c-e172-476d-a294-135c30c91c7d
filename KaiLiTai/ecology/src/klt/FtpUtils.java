package klt;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.util.ArrayList;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.ftp.parser.UnixFTPEntryParser;
import weaver.general.BaseBean;

public class FtpUtils {
	
	private FTPClient ftpClient = null;
	public BaseBean log = null;

	private String hostname ;
	private Integer port;
	private String username;
	private String password;
	private boolean isPassive;
	
	public FtpUtils(String hostname, Integer port, String username, String password, boolean isPassive) {
		log = new BaseBean();
		try {
			this.hostname = hostname;
			this.port = port;
			this.username = username;
			this.password = password;
			this.isPassive = isPassive;
		} catch (Exception e) {
			log.writeLog("FtpUtils:" + e.getMessage());
		}
	}
	
	/**
	 * 初始化ftp服务器
	 */
	public void initFtpClient(){
		ftpClient = new FTPClient();
		ftpClient.setControlEncoding("utf-8");
		log = new BaseBean();
		try {
			log.writeLog("连接ftp服务器:" + this.hostname + ":" + this.port);
			ftpClient.connect(hostname, port); 	 	  //连接ftp服务器
			ftpClient.login(username, password); 	  //登录ftp服务器
			int replyCode = ftpClient.getReplyCode(); //是否成功登录服务器
			if (!FTPReply.isPositiveCompletion(replyCode)) {
				log.writeLog("ftp服务器连接失败:" + this.hostname + ":" + this.port);
			}
			log.writeLog("ftp服务器连接成功:" + this.hostname + ":" + this.port);
			
			//设置访问被动模式
			if(!isPassive){
				ftpClient.setRemoteVerificationEnabled(false);
				//ftpClient.setControlEncoding("UTF-8");
				ftpClient.enterLocalPassiveMode();
				
				//由于apache不支持中文语言环境，通过定制类解析中文日期类型
				UnixFTPEntryParser u = new UnixFTPEntryParser();
				ftpClient.configure(new FTPClientConfig("com.example.ftp.UnixFTPEntryParser"));
			}
		} catch (MalformedURLException e) {
			log.writeLog("连接ftp服务器:" + e.getMessage());
		} catch (IOException e) {
			log.writeLog("连接ftp服务器:" + e.getMessage());
		}
	}

	/**
	 * 上传文件
	 * 
	 * @param pathname
	 *            ftp服务保存地址
	 * @param fileName
	 *            上传到ftp的文件名
	 * @param originfilename
	 *            待上传文件的名称（绝对地址） *
	 * @return
	 */
	public String uploadFile(String pathname, String fileName, String originfilename) {
		String errmsg = "";
		InputStream inputStream = null;
		try {
			log.writeLog("开始上传文件");
			inputStream = new FileInputStream(new File(originfilename));
			initFtpClient();
			ftpClient.setFileType(ftpClient.BINARY_FILE_TYPE);
			//CreateDirecroty(pathname);
			//ftpClient.makeDirectory(pathname);
			//ftpClient.changeWorkingDirectory(pathname);
			ftpClient.storeFile(fileName, inputStream);
			inputStream.close();
			ftpClient.logout();
			log.writeLog("上传文件成功");
		} catch (Exception e) {
			errmsg = "e.getMessage()";
			log.writeLog("上传文件失败:" + e.getMessage());
		} finally {
			if (ftpClient.isConnected()) {
				try {
					ftpClient.disconnect();
				} catch (IOException e) {
					log.writeLog(e.getMessage());
				}
			}
			if (null != inputStream) {
				try {
					inputStream.close();
				} catch (IOException e) {
					log.writeLog(e.getMessage());
				}
			}
		}
		return errmsg;
	}

	/**
	 * 上传文件
	 * 
	 * @param pathname
	 *            ftp服务保存地址
	 * @param fileName
	 *            上传到ftp的文件名
	 * @param inputStream
	 *            输入文件流
	 * @return
	 */
	public boolean uploadFile(String pathname, String fileName, InputStream inputStream) {
		boolean flag = false;
		try {
			log.writeLog("开始上传文件");
			initFtpClient();
			ftpClient.setFileType(ftpClient.BINARY_FILE_TYPE);
			CreateDirecroty(pathname);
			ftpClient.makeDirectory(pathname);
			ftpClient.changeWorkingDirectory(pathname);
			ftpClient.storeFile(fileName, inputStream);
			inputStream.close();
			ftpClient.logout();
			flag = true;
			log.writeLog("上传文件成功");
		} catch (Exception e) {
			log.writeLog("上传文件失败");
			e.printStackTrace();
		} finally {
			if (ftpClient.isConnected()) {
				try {
					ftpClient.disconnect();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (null != inputStream) {
				try {
					inputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return true;
	}

	// 改变目录路径
	public boolean changeWorkingDirectory(String directory) {
		boolean flag = true;
		try {
			log.writeLog("changeWorkingDirectory开始！");
			flag = ftpClient.changeWorkingDirectory(directory);
			if (flag) {
				log.writeLog("进入文件夹" + directory + " 成功！");
			} else {
				log.writeLog("进入文件夹" + directory + " 失败！开始创建文件夹");
			}
		} catch (IOException ioe) {
			ioe.printStackTrace();
		}
		return flag;
	}

	// 创建多层目录文件，如果有ftp服务器已存在该文件，则不创建，如果无，则创建
	public boolean CreateDirecroty(String remote) throws IOException {
		boolean success = true;
		String directory = remote + "/";
		// 如果远程目录不存在，则递归创建远程服务器目录
		if (!directory.equalsIgnoreCase("/") && !changeWorkingDirectory(new String(directory))) {
			int start = 0;
			int end = 0;
			if (directory.startsWith("/")) {
				start = 1;
			} else {
				start = 0;
			}
			end = directory.indexOf("/", start);
			String path = "";
			String paths = "";
			while (true) {
				String subDirectory = new String(remote.substring(start, end).getBytes("GBK"), "iso-8859-1");
				path = path + "/" + subDirectory;
				if (!existFile(path)) {
					if (makeDirectory(subDirectory)) {
						changeWorkingDirectory(subDirectory);
					} else {
						System.out.println("创建目录[" + subDirectory + "]失败");
						changeWorkingDirectory(subDirectory);
					}
				} else {
					changeWorkingDirectory(subDirectory);
				}

				paths = paths + "/" + subDirectory;
				start = end + 1;
				end = directory.indexOf("/", start);
				// 检查所有目录是否创建完毕
				if (end <= start) {
					break;
				}
			}
		}
		return success;
	}

	// 判断ftp服务器文件是否存在
	public boolean existFile(String path) throws IOException {
		boolean flag = false;
		FTPFile[] ftpFileArr = ftpClient.listFiles(path);
		if (ftpFileArr.length > 0) {
			flag = true;
		}
		return flag;
	}

	// 创建目录
	public boolean makeDirectory(String dir) {
		boolean flag = true;
		try {
			flag = ftpClient.makeDirectory(dir);
			if (flag) {
				System.out.println("创建文件夹" + dir + " 成功！");
			} else {
				System.out.println("创建文件夹" + dir + " 失败！");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	/**
	 * * 下载文件 *
	 * 
	 * @param pathname
	 *            FTP服务器文件目录 *
	 * @param filename
	 *            文件名称 *
	 * @param localpath
	 *            下载后的文件路径 *
	 * @return
	 */
	public boolean downloadFile(String pathname, String filename, String localpath) {
		boolean flag = false;
		OutputStream os = null;
		try {
			log.writeLog("开始下载文件");
			initFtpClient();
			// 切换FTP目录
			ftpClient.changeWorkingDirectory(pathname);
			log.writeLog("changeWorkingDirectory:" + pathname);
			FTPFile[] ftpFiles = ftpClient.listFiles();
			log.writeLog("ftpFiles Count:" + ftpFiles.length);
			for (FTPFile file : ftpFiles) {
				if (filename.equalsIgnoreCase(pathname + file.getName())) {
					File localFile = new File(localpath);
					os = new FileOutputStream(localFile);
					ftpClient.retrieveFile(file.getName(), os);
					os.close();
				}
			}
			ftpClient.logout();
			flag = true;
			log.writeLog("下载文件成功");
		} catch (Exception e) {
			log.writeLog("下载文件失败:" + e.getMessage());
		} finally {
			if (ftpClient.isConnected()) {
				try {
					ftpClient.disconnect();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (null != os) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return flag;
	}
	
	/**
	 * * 下载文件（不遍历目录） *
	 * 
	 * @param pathname
	 *            FTP服务器文件目录 *
	 * @param filename
	 *            文件名称 *
	 * @param localpath
	 *            下载后的文件路径 *
	 * @return
	 */
	public boolean downloadFileNoTraverse(String pathname, String filename, String localpath) {
		boolean flag = false;
		OutputStream os = null;
		try {
			log.writeLog("开始下载文件");
			initFtpClient();
			// 切换FTP目录
			ftpClient.changeWorkingDirectory(pathname);
			log.writeLog("changeWorkingDirectory:" + pathname);
			
			File localFile = new File(localpath);
			os = new FileOutputStream(localFile);
			ftpClient.retrieveFile(filename, os);
			os.close();
			
			ftpClient.logout();
			flag = true;
			log.writeLog("下载文件成功");
		} catch (Exception e) {
			flag = false;
			log.writeLog("下载文件失败:" + e.getMessage());
		} finally {
			if (ftpClient.isConnected()) {
				try {
					ftpClient.disconnect();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (null != os) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return flag;
	}

	
	/**
     * 递归遍历出目录下面所有文件
     *
     * @param pathName 需要遍历的目录，必须以"/"开始和结束
     * @throws IOException
     */
    public ArrayList<String> List(String pathName) throws IOException {
    	initFtpClient();
    	ArrayList<String> arFiles = new ArrayList<String>();
    	
    	//更换目录到当前目录
    	log.writeLog("changeWorkingDirectory:" + pathName);
        this.changeWorkingDirectory(pathName);
        FTPFile[] files = this.ftpClient.listFiles(pathName);
        log.writeLog(pathName + " ==== files count:" + files.length);
        for (FTPFile file : files) {
            if (file.isFile()) {
                arFiles.add(pathName + file.getName());
            } else if (file.isDirectory()) {
                // 需要加此判断。否则，ftp默认将‘项目文件所在目录之下的目录（./）’与‘项目文件所在目录向上一级目录下的目录（../）’都纳入递归，这样下去就陷入一个死循环了。需将其过滤掉。
                if (!".".equals(file.getName()) && !"..".equals(file.getName())) {
                    List(pathName + file.getName() + "/");
                }
            }
        }
        
        return arFiles;
    }
	
	/**
	 * * 删除文件 *
	 * 
	 * @param pathname
	 *            FTP服务器保存目录 *
	 * @param filename
	 *            要删除的文件名称 *
	 * @return
	 */
	public boolean deleteFile(String pathname, String filename) {
		boolean flag = false;
		try {
			log.writeLog("开始删除文件");
			initFtpClient();
			// 切换FTP目录
			ftpClient.changeWorkingDirectory(pathname);
			log.writeLog("删除文件:" + filename);
			ftpClient.dele(filename);
			ftpClient.logout();
			flag = true;
			log.writeLog("删除文件成功");
		} catch (Exception e) {
			log.writeLog("删除文件失败");
			e.printStackTrace();
		} finally {
			if (ftpClient.isConnected()) {
				try {
					ftpClient.disconnect();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return flag;
	}

}