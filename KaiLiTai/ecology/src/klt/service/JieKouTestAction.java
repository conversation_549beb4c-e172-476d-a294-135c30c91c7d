package klt.service;

import com.alibaba.fastjson.JSON;
import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.json.JSONException;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.workflow.WorkflowComInfo;

import java.util.ArrayList;
import java.util.HashMap;

public class JieKouTestAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        WorkflowComInfo workflowComInfo = new WorkflowComInfo();
        int requestid = Util.getIntValue(request.getRequestid());   //requestid
        int cjr = Util.getIntValue(request.getCreatorid()); //创建人
        String requestname = Util.null2String(request.getRequestManager().getRequestname());    //标题
        int workflowid = Util.getIntValue(request.getWorkflowid()); //流程id
        int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));  //表单id
        String maintablename = "formtable_main_" + (Math.abs(formid));  //表名
        int wfstatus = request.getRequestManager().getCreater(); //（代码执行时）当前节点
        writeLog("requestid = " + requestid);   //统一日志类
        writeLog("workflowid = " + workflowid);
        writeLog("formid = " + formid);
        HttpPostAndGetService service = new HttpPostAndGetService();
        //采购请购单
        String url = "http://192.168.2.208:8080/KaiLiTaiFWService.svc/PU_AppV/Add";
        HashMap<Object, Object> map = new HashMap<>();
        map.put("CCode", "2021080085");
        map.put("DVouchDate", "2020-12-21");
        map.put("CPersonCode", "0004");
        map.put("CDepCode", "0505");
        map.put("CPTCode", "01");
        map.put("CBusType", "人民币");
        ArrayList<Object> list = new ArrayList<>();
        HashMap<Object, Object> param = new HashMap<>();
        param.put("CInvCode", "*********");
        param.put("Iquantity", "深圳市益心达医学新技术有限公司");
        param.put("DRequirDate", "2020-12-21");
        param.put("FTaxPrice", "10");
        list.add(param);
        map.put("Items", list);
        JSONObject json = JSONObject.fromObject(map);
        writeLog("采购请购单参数：" + json.toString());
      //  String result = service.doPost(url, json.toString());
        String sendpost = sendpost(url, json.toString());
        writeLog("采购请购单返回结果：" + sendpost);

        //付款单
        url = "http://192.168.2.208:8080/KaiLiTaiFWService.svc/APApplyData/Add";
        String jsonstr = "{\n" +
                " \"CVouchID\": \"2020080120\",\n" +
                " \"DVouchDate\": \"2020-12-21\",\n" +
                " \"CCusVen\": \"V98F3500031\",\n" +
                " \"CDepCode\": \"040106\",\n" +
                " \"CPerson\": \"0864\",\n" +
                " \"Cexch_name\": \"人民币\",\n" +
                " \"IExchRate\": \"13\",\n" +
                " \"CSSCode\": \"01\",\n" +
                " \"CCode\": \"\",\n" +
                " \"CDigest\": \"\",\n" +
                " \"CDefine12\": \"浦东支行\",\n" +
                " \"CDefine13\": \"6200001551612\",\n" +
                " \"IAmt\": \"108\",\n" +
                " \"IAmt_f\": \"108\",\n" +
                " \"DPrePayDate\": \"2020-12-21\",\n" +
                " \"Items\": [{\n" +
                "  \"CMemo\": \"22\",\n" +
                "  \"CCusVen\": \"V98F3500031\",\n" +
                "  \"CKm\": \"220201\",\n" +
                "  \"CXmClass\": \"\",\n" +
                "  \"CXm\": \"\",\n" +
                "  \"CDepCode\": \"040106\",\n" +
                "  \"CItemName\": \"\",\n" +
                "  \"CConType\": \"\",\n" +
                "  \"CConID\": \"\",\n" +
                "  \"iAmt_s\": \"0\",\n" +
                "  \"IAmt\": \"108\",\n" +
                "  \"IAmt_f\": \"108\"\n" +
                " }]\n" +
                "}";

        HashMap<Object, Object> mapfu = new HashMap<>();
        mapfu.put("CVouchID", "2020080120");
        mapfu.put("DVouchDate", "2020-12-21");
        mapfu.put("CCusVen", "V98F3500031");
        mapfu.put("CDepCode", "040106");
        mapfu.put("CPerson", "0864");
        mapfu.put("Cexch_name", "人民币");
        mapfu.put("IExchRate", "13");
        mapfu.put("CSSCode", "01");
        mapfu.put("CCode", "");
        mapfu.put("CDigest", "");
        mapfu.put("CDefine12", "浦东支行");
        mapfu.put("CDefine13", "6200001551612");
        mapfu.put("IAmt", "108");
        mapfu.put("IAmt_f", "108");
        mapfu.put("DPrePayDate", "2020-12-21");
        HashMap<Object, Object> paramfu = new HashMap<>();
        ArrayList<Object> listfu = new ArrayList<>();
        paramfu.put("CMemo", "22");
        paramfu.put("CCusVen", "V98F3500031");
        paramfu.put("CKm", "220201");
        paramfu.put("CXmClass", "");
        paramfu.put("CXm", "");
        paramfu.put("CDepCode", "040106");
        paramfu.put("CItemName", "");
        paramfu.put("CConType", "");
        paramfu.put("CConID", "");
        paramfu.put("iAmt_s", "0");
        paramfu.put("IAmt", "108");
        paramfu.put("IAmt_f", "108");
        listfu.add(paramfu);
        mapfu.put("Items", listfu);
        JSONObject jsonfu = JSONObject.fromObject(mapfu);
        writeLog("付款单,参数：" + jsonfu.toString());
        //result = service.doPost(url, jsonfu.toString());
        String sendpost1 = sendpost(url, jsonfu.toString());
        writeLog("付款单,返回值：" + sendpost1);

        //供应商
        url = "http://192.168.2.208:8080/KaiLiTaiFWService.svc/VendorData/Add";
        HashMap<Object, Object> mapgo = new HashMap<>();
        mapgo.put("cVenCode", "K-0971");
        mapgo.put("cVenName", "oa对接u8测试用");
        JSONObject jsongo = JSONObject.fromObject(mapgo);
        writeLog("供应商请求参数：" + jsongo.toString());
        //result = service.doPost(url, jsongo.toString());
        String sendpost2 = sendpost(url, jsongo.toString());
        writeLog("供应商返回值：" + sendpost2);


        //应付单
        url = "http://192.168.2.208:8080/KaiLiTaiFWService.svc/AvouchDate/Add";
        jsonstr = "{" + "CVouchID: \"0000000002\",\n" +
                "\t\"DVouchDate\": \"2020-11-16\",\n" +
                "\t\"CDwCode\": \"V013200000001\",\n" +
                "\t\"CCode\": \"0005\",\n" +
                "\t\"CDepCode\": \"01\",\n" +
                "\t\"CPerson\": \"0038\",\n" +
                "\t\"Bd_c\": \"0\",\n" +
                "\t\"IAmount_fList\": \"150\",\n" +
                "\t\"IAmountList\": \"150\",\n" +
                "\t\"CDigestList\": \"采购\",\n" +
                "\t\"CexchList_name\": \"人民币\",\n" +
                "\t\"IAmount_s\": \"18\",\n" +
                "\t\"Items\": [{\n" +
                "\t\t\"CexchList_name\": \"人民币\",\n" +
                "\t\t\"IExchRateList\": \"13\",\n" +
                "\t\t\"IAmount_fList\": \"150\",\n" +
                "\t\t\"IAmountList\": \"150\",\n" +
                "\t\t\"CPersonList\": \"C0001\",\n" +
                "\t\t\"CItemCode\": \"001\",\n" +
                "\t\t\"CDigestList\": \"采购\",\n" +
                "\t\t\"CCode \": \" 112201 \",\n" +
                "\t\t\"Bd_c\": \"1\"\n" +
                "\t}]\n" +
                "}";
        HashMap<Object, Object> mapying = new HashMap<>();
        mapying.put("CVouchID", "0000000002");
        mapying.put("DVouchDate", "2020-11-16");
        mapying.put("CDwCode", "V013200000001");
        mapying.put("CCode", "0005");
        mapying.put("CDepCode", "01");
        mapying.put("CPerson", "0038");
        mapying.put("Bd_c", "0");
        mapying.put("IAmount_fList", "150");
        mapying.put("IAmountList", "150");
        mapying.put("CDigestList", "采购");
        mapying.put("CexchList_name", "人民币");
        mapying.put("IAmount_s", "18");
        HashMap<Object, Object> paramying = new HashMap<>();
        paramying.put("CexchList_name", "人民币");
        paramying.put("IExchRateList", "13");
        paramying.put("IAmount_fList", "150");
        paramying.put("IAmountList", "150");
        paramying.put("CPersonList", "C0001");
        paramying.put("CItemCode", "001");
        paramying.put("CDigestList", "采购");
        paramying.put("CCode", "112201");
        paramying.put("Bd_c", "1");
        mapying.put("Items", paramying);
        JSONObject jsonying = JSONObject.fromObject(mapying);
        writeLog("应付单参数：" + jsonying.toString());
       // result = service.doPost(url, jsonying.toString());
        String sendpost3 = sendpost(url, jsonying.toString());
        writeLog("应付单，返回值：" + sendpost3);

        return retStr;
    }

 private static String sendpost(String url,String param){
     HttpClient httpClient = null;
     PostMethod postMethod = null;
     String retStr = "";
     try {
         httpClient = new HttpClient();
         httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
         httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
         postMethod = new PostMethod(url);
         RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
         postMethod.setRequestEntity(se);
         httpClient.executeMethod(postMethod);
         if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
             retStr = postMethod.getResponseBodyAsString().trim();
             System.out.println("retStr = " + retStr);
         }
     } catch (Exception e) {
         //baseBean.writeLog(e);
     } finally {
         try {
             if (postMethod != null) {
                 postMethod.releaseConnection();
             }
         } catch (Exception e) {
             //baseBean.writeLog(e);
         }
     }
        return retStr;
 }

    public static void main(String[] args) {

        //yingfu
        String url = "http://192.168.2.208:8080/KaiLiTaiFWService.svc/VendorData/Add";
        HashMap<Object, Object> mapgo = new HashMap<>();
        mapgo.put("cVenCode", "K-0143");
        mapgo.put("cVenName", "oa对接u8测试用2");
        JSONObject jsongo = JSONObject.fromObject(mapgo);
       /* String str="{\n" +
                "\t\"CCode\": \"112301\",\n" +
                "\t\"CDepCode\": \"2802\",\n" +
                "\t\"CDigestList\": \"\",\n" +
                "\t\"IAmountList\": \"100\",\n" +
                "\t\"IAmount_fList\": \"100\",\n" +
                "\t\"DVouchDate\": \"2021-01-19\",\n" +
                "\t\"IAmount_s\": \"\",\n" +
                "\t\"CPerson\": \"K-0971\",\n" +
                "\t\"CexchList_name\": \"人民币\",\n" +
                "\t\"CVouchID\": 44044,\n" +
                "\t\"Bd_c\": \"0\",\n" +
                "\t\"Items\": [{\n" +
                "\t\t\"CCode\": \"66020702\",\n" +
                "\t\t\"CexchList_name\": \"1\",\n" +
                "\t\t\"CDigestList\": \"111\",\n" +
                "\t\t\"IExchRateList\": \"1\",\n" +
                "\t\t\"CItemCode\": \"\",\n" +
                "\t\t\"IAmountList\": \"100\",\n" +
                "\t\t\"Bd_c\": \"1\",\n" +
                "\t\t\"CPersonList\": \"K-0971\",\n" +
                "\t\t\"IAmount_fList\": \"100\"\n" +
                "\t}],\n" +
                "\t\"CDwCode\": \"K-0971\"\n" +
                "}";*/
        String sendpost2 = sendpost(url, jsongo.toString());
        //writeLog("供应商返回值：" + sendpost2);
        String json="{\"dsc\":\" 16:54:15执行导入采购请购单接口成功！\",\"succeed\":true}";
        try {
            org.json.JSONObject jsonObject = new org.json.JSONObject(sendpost2);
            String desc = jsonObject.getString("dsc");
            String succeed = jsonObject.getString("succeed");
        }catch (JSONException e){
            System.out.println("解析返回值失败："+e);
        }
    }
}
