package klt.service;

import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.lang3.StringUtils;
import utils.WriteLogtoTable.WriteLog2table;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.StaticObj;
import weaver.general.Util;
import weaver.interfaces.datasource.DataSource;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class QingGouAction extends BaseBean implements Action {
//请购单接口，对应采购申请流程
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        RequestManager rm = request.getRequestManager();
        writeLog(this.getClass().getName() + " -- Action xxxx ");
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(request.getRequestid());   //requestid
            int cjr = Util.getIntValue(request.getCreatorid()); //创建人
            String requestname = Util.null2String(request.getRequestManager().getRequestname());    //标题
            int workflowid = Util.getIntValue(request.getWorkflowid()); //流程id
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));  //表单id
            String maintablename = "formtable_main_" + (Math.abs(formid));  //表名
            int wfstatus = request.getRequestManager().getCreater(); //（代码执行时）当前节点
            int userId = request.getRequestManager().getUserId();
            String spcode = getWorkCodeById("" + userId);
            writeLog("requestid = " + requestid);   //统一日志类
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);

            /**
                 * {
             *     "CCode":"2021080085",采购请购单号
             *     "DVouchDate":"2020-12-21",日期
             *     "CPersonCode":"0004",请购人员编码
             *     "CDepCode":"0505",表头部门编码
             *     "CPTCode":"01",请购类型编码
             *     "CBusType":"人民币",业务类型
             *     "Items":[
             *         {
             *             "CInvCode":"*********",存货编码
             *             "CVenCode":"深圳市益心达医学新技术有限公司",供应商编码
             *             "Iquantity":"10",数量
             *             "DRequirDate":"2020-12-21",需求日期
             *             "FTaxPrice":"10"    原币含税单价
             *     }
             *     ]
             * }
             * */

            Property[] properties = request.getMainTableInfo().getProperty();// 获取表单主字段信息
            HashMap<String, Object> map = new HashMap<>();
            Date date = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = dateFormat.format(date);
            //币种默认 CNY  汇率1
            map.put("DVouchDate",format);
            map.put("CBusType","人民币");
            map.put("SPCPerson",spcode);

            for (int i = 0; i < properties.length; i++) {
                String field = properties[i].getName();// 主字段名称
                //供应商编码
                if("cCode".equalsIgnoreCase(field)){
                    String lcbh=Util.null2String(properties[i].getValue());
                   // String code = getCCusVenById(CDwCode);
                  map.put("CCode",lcbh);
                }

                //业务员编码
                if("sqr".equalsIgnoreCase(field)){
                    String pid=Util.null2String(properties[i].getValue());
                    String CPersonCode = getWorkCodeById(pid);
                    map.put("CPersonCode",CPersonCode);
                }

                if("cPTCode".equalsIgnoreCase(field)){
                    String type=Util.null2String(properties[i].getValue());
                   String  cPTCode="";
                   if("0".equals(type)){
                       cPTCode="01";
                   }else if("1".equals(type)){
                       cPTCode="02";
                   }else if("2".equals(type)){
                       cPTCode="03";
                   }
                    //String fukuanType = changFukuanType(fs);
                    map.put("CPTCode",cPTCode);
                }
            }
            //取明细数据
            DetailTable[] detailtable = request.getDetailTableInfo().getDetailTable();
            ArrayList<Object> dataList = new ArrayList<>();
            // 获取所有明细表
            if (detailtable.length > 0) {
                for (int i = 0; i < detailtable.length; i++) {
                    DetailTable dt = detailtable[i];
                    // 指定明细表
                    Row[] s = dt.getRow();
                    // 当前明细表的所有数据,按行存储
                    for (int j = 0; j < s.length; j++) {
                        Row r = s[j];
                        // 指定行
                        Cell c[] = r.getCell();
                        Map rowmap =new HashMap<>();
                        // 每行数据再按列存储
                        for (int k = 0; k < c.length; k++) {
                            // 指定列
                            Cell c1 = c[k];
                            // 明细字段名称
                            String name = c1.getName();
                            String value = c1.getValue();
                            if("cInvCode".equals(name)){
                                //存货编码
                                rowmap.put("CInvCode",value);
                            }
                            if("cVenCode".equals(name)){
                                //供应商编码
                                rowmap.put("cVenCode",value);
                            }
                            if("iquantity".equals(name)){
                                //供应商编码
                                rowmap.put("Iquantity",value);
                            }
                            if("dRequirDate".equals(name)){
                                //供应商编码
                                rowmap.put("DRequirDate",value);
                            }
                            if("fTaxPrice".equals(name)){
                                //供应商编码
                                rowmap.put("FTaxPrice",value);
                            }
                            // 明细字段的值
                            writeLog("name=="+name+"====value==="+value);
                        }
                        dataList.add(rowmap);
                    }
                }
            }
            map.put("Items",dataList);

            JSONObject json = JSONObject.fromObject(map);
            writeLog("json--qinggou--"+json);
            //调用接口
            BaseBean bean = new BaseBean();
            String serviceurl= bean.getPropValue("U8interface","qinggouactionurl");
            String result = sendpost(serviceurl, json.toString());
            writeLog("json--qinggou------serviceurl---"+serviceurl+"---------result-----"+result);
            WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
            if(StringUtils.isNotEmpty(result)){
                JSONObject jsonObject = JSONObject.fromObject(result);
                String succeed = jsonObject.getString("succeed");
                String dsc = jsonObject.getString("dsc");
                if("true".equalsIgnoreCase(succeed)){
                    writeLog("fukuan success");    //输出最终日志标示
                }else{
                    retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
                    rm.setMessagecontent(dsc);
                    writeLog("fukuan request failed "+dsc);    //输出最终日志标示
                }
            }

        } catch (Exception ex) {
            // 手动报错处理，挂节点后附加操作，可以阻碍流程提交
            if (rm != null) {
                // rm.setMessage("message 错误");
                rm.setMessagecontent("流程调用付款单接口失败，请联系管理员！"); //设置错误提示，显示在流程表单上部
                WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{"请购单接口，对应采购申请流程","此报错可能由于接口不通或者网络等不可控逻辑造成","重试不行请联系管理员",""+request.getRequestid()},1,103);
                retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }
            writeLog(ex);
        }

        return retStr;
    }

    private static String sendpost(String url,String param) throws IOException {
        HttpClient httpClient = null;
        PostMethod postMethod = null;
        String retStr = "";

            httpClient = new HttpClient();
            httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
            httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
            postMethod = new PostMethod(url);
            RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
            postMethod.setRequestEntity(se);
            httpClient.executeMethod(postMethod);
            if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
                retStr = postMethod.getResponseBodyAsString().trim();
                // System.out.println("retStr = " + retStr);
            }
            if (postMethod != null) {
                postMethod.releaseConnection();
            }
        return retStr;
    }

    private static String getWorkCodeById(String id ){
        RecordSet rs = new RecordSet();
        String workcode="";
        String sql="select workcode from hrmresource where id ="+id;
        rs.execute(sql);
        if (rs.next()){
            workcode = Util.null2String(rs.getString("workcode"));
        }
        return workcode;
    }
}
