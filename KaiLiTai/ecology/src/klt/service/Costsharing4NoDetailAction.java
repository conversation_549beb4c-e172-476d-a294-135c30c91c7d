package klt.service;

import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.lang3.StringUtils;
import utils.WriteLogtoTable.WriteLog2table;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Costsharing4NoDetailAction extends BaseBean implements Action {

    public String CVouchID;

    public String CCusVen;

    public String CDepCode;

    public String CPerson;

    public String CSSCode;

    public String Amount;

    public String CDigest;

    public String Receipt;

    public String CDefine12;

    public String CDefine13;

    public String IAmt;

    public String IAmt_f;

    public String DPrePayDate;

    public String SPCPerson;

    public String CXmty;

    public String CMemo;

    public String IAmtDetail; //明细金额

    public String CConID;

    public String yfxmbm; //研发项目编码

    public String CKm;

    public String uf_klt_yfxmtz;

    public String xmdm;

    public String IAmt_SQ;

    public String CCode;

    public String getCCode() {
        return CCode;
    }

    public void setCCode(String CCode) {
        this.CCode = CCode;
    }

    public String getUf_klt_yfxmtz() {
        return uf_klt_yfxmtz;
    }

    public void setUf_klt_yfxmtz(String uf_klt_yfxmtz) {
        this.uf_klt_yfxmtz = uf_klt_yfxmtz;
    }

    public String getXmdm() {
        return xmdm;
    }

    public void setXmdm(String xmdm) {
        this.xmdm = xmdm;
    }

    public String getCVouchID() {
        return CVouchID;
    }

    public void setCVouchID(String CVouchID) {
        this.CVouchID = CVouchID;
    }

    public String getCCusVen() {
        return CCusVen;
    }

    public void setCCusVen(String CCusVen) {
        this.CCusVen = CCusVen;
    }

    public String getCDepCode() {
        return CDepCode;
    }

    public void setCDepCode(String CDepCode) {
        this.CDepCode = CDepCode;
    }

    public String getCPerson() {
        return CPerson;
    }

    public void setCPerson(String CPerson) {
        this.CPerson = CPerson;
    }

    public String getCSSCode() {
        return CSSCode;
    }

    public void setCSSCode(String CSSCode) {
        this.CSSCode = CSSCode;
    }

    public String getAmount() {
        return Amount;
    }

    public void setAmount(String amount) {
        Amount = amount;
    }

    public String getCDigest() {
        return CDigest;
    }

    public void setCDigest(String CDigest) {
        this.CDigest = CDigest;
    }

    public String getReceipt() {
        return Receipt;
    }

    public void setReceipt(String receipt) {
        Receipt = receipt;
    }

    public String getCDefine12() {
        return CDefine12;
    }

    public void setCDefine12(String CDefine12) {
        this.CDefine12 = CDefine12;
    }

    public String getCDefine13() {
        return CDefine13;
    }

    public void setCDefine13(String CDefine13) {
        this.CDefine13 = CDefine13;
    }

    public String getIAmt() {
        return IAmt;
    }

    public void setIAmt(String IAmt) {
        this.IAmt = IAmt;
    }

    public String getIAmt_f() {
        return IAmt_f;
    }

    public void setIAmt_f(String IAmt_f) {
        this.IAmt_f = IAmt_f;
    }

    public String getDPrePayDate() {
        return DPrePayDate;
    }

    public void setDPrePayDate(String DPrePayDate) {
        this.DPrePayDate = DPrePayDate;
    }

    public String getSPCPerson() {
        return SPCPerson;
    }

    public void setSPCPerson(String SPCPerson) {
        this.SPCPerson = SPCPerson;
    }

    public String getCXmty() {
        return CXmty;
    }

    public void setCXmty(String CXmty) {
        this.CXmty = CXmty;
    }

    public String getCMemo() {
        return CMemo;
    }

    public void setCMemo(String CMemo) {
        this.CMemo = CMemo;
    }

    public String getIAmtDetail() {
        return IAmtDetail;
    }

    public void setIAmtDetail(String IAmtDetail) {
        this.IAmtDetail = IAmtDetail;
    }

    public String getCConID() {
        return CConID;
    }

    public void setCConID(String CConID) {
        this.CConID = CConID;
    }

    public String getYfxmbm() {
        return yfxmbm;
    }

    public void setYfxmbm(String yfxmbm) {
        this.yfxmbm = yfxmbm;
    }

    public String getCKm() {
        return CKm;
    }

    public void setCKm(String CKm) {
        this.CKm = CKm;
    }

    public String getIAmt_SQ() {
        return IAmt_SQ;
    }

    public void setIAmt_SQ(String IAmt_SQ) {
        this.IAmt_SQ = IAmt_SQ;
    }

    @Override
    public String execute(RequestInfo requestInfo) {    

        String requestid = requestInfo.getRequestid();
        Map<String,String> map = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }
        writeLog(requestid);
        HashMap<String,Object> ansmap = new HashMap<>();
        String codesupplier = getCCusVenById(map.get(CCusVen));
        //主表字段
        ansmap.put("CVouchID",map.get(CVouchID));
        ansmap.put("DVouchDate",getnowtime());
        ansmap.put("CCusVen",codesupplier);
        ansmap.put("CDepCode","");
        ansmap.put("CPerson",codeIntercept(getWorkCodeById(map.get(CPerson))));
        ansmap.put("Cexch_name","人民币");
        ansmap.put("IExchRate","1");
        ansmap.put("CSSCode",changeType(map.get(CSSCode)));
        ansmap.put("CCode",map.get(CCode));
        ansmap.put("CDigest",map.get(CDigest));
        ansmap.put("CDefine12",getbankname(map.get(CDefine12)));
        ansmap.put("CDefine13",map.get(CDefine13));
        ansmap.put("IAmt",map.get(IAmt));
        ansmap.put("IAmt_f",map.get(IAmt_f));
        ansmap.put("DPrePayDate",map.get(DPrePayDate));
        ansmap.put("SPCPerson",codeIntercept(getWorkCodeById(map.get(SPCPerson))));
        ansmap.put("CXmty",map.get(CXmty));
        ansmap.put("Amount",map.get(Amount));
        ansmap.put("Receipt",map.get(Receipt));
        writeLog(ansmap.toString());
        //取明细数据
        ArrayList<Object> dataList = new ArrayList<>();
        // 获取所有明细表
        Map<String, String> rowmap = new HashMap<>();
        //插入明细数据
        rowmap.put("CMemo", map.get(CMemo));
        rowmap.put("CDepCode",getCostCenter(map.get(CDepCode)));
        ansmap.put("CDepCode", getCostCenter(map.get(CDepCode)));
        rowmap.put("IAmt",map.get(IAmtDetail));
        rowmap.put("IAmt_f",map.get(IAmtDetail));
        rowmap.put("CKm",getCodeNameById(map.get(CKm)));
        rowmap.put("IAmt_SQ",map.get(IAmt_SQ));
        rowmap.put("CCusVen",codesupplier);
        if("0".equals(map.get(CXmty))){
            rowmap.put("CItemName",getProjectName(map.get(yfxmbm)));
            rowmap.put("CXm",getProjectCode(map.get(yfxmbm)));
        }else{
                rowmap.put("CItemName",codeIntercept(getWorkCodeById(map.get(CPerson))));
            rowmap.put("CXm","Dummy");
        }
        rowmap.put("CConID",map.get(CConID));
        dataList.add(rowmap);

        writeLog("finish明细");
        ansmap.put("Items",dataList);
        JSONObject json = JSONObject.fromObject(ansmap);
        writeLog("转换json成功"+json);
        String serviceurl="http://192.168.2.164:8080/KaiLiTaiFWService.svc/APDGYFD/Add";
        String result = "";

        try {
            result = sendpost(serviceurl,json.toString());
        } catch (Exception e) {
            requestInfo.getRequestManager().setMessagecontent("接口不通报错");
            WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
            return Action.FAILURE_AND_CONTINUE;
        }
        if(StringUtils.isNotEmpty(result)){
            JSONObject jsonObject = JSONObject.fromObject(result);
            String succeed = jsonObject.getString("succeed");
            String dsc = jsonObject.getString("dsc");
            if("true".equalsIgnoreCase(succeed)){
                writeLog("success");    //输出最终日志标示
            }else{
                requestInfo.getRequestManager().setMessagecontent(dsc);
                WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
                return Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }
        }
        WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
        return Action.SUCCESS;
    }

    private static String sendpost(String url,String param) throws IOException {
        HttpClient httpClient = null;
        PostMethod postMethod = null;
        String retStr = "";
        httpClient = new HttpClient();
        httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
        httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
        postMethod = new PostMethod(url);
        RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
        postMethod.setRequestEntity(se);
        httpClient.executeMethod(postMethod);
        if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
            retStr = postMethod.getResponseBodyAsString().trim();
            // System.out.println("retStr = " + retStr);
        }
        if (postMethod != null) {
            postMethod.releaseConnection();
        }
        return retStr;
    }

    public String getCheck(String number){
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery("select selectname from workflow_selectitem where fieldid = 16899 and selectvalue = "+number);
        if(recordSet.next()){
            return recordSet.getString("selectname");
        }
        return "";
    }
    public String getnowtime(){
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(date);
    }

    public  String getCCusVenById(String id){
        RecordSet rs = new RecordSet();
        String code="";
        String sql="select gysbm from uf_gys where id='"+id+"'";

        rs.execute(sql);
        if(rs.next()){
            code= Util.null2String(rs.getString("gysbm"));
        }
        return code;
    }

    private static String getWorkCodeById(String id ){
        RecordSet rs = new RecordSet();
        String workcode="";
        String sql="select workcode from hrmresource where id ="+id;
        rs.execute(sql);
        if (rs.next()){
            workcode = Util.null2String(rs.getString("workcode"));
        }
        return workcode;
    }

    private static String changeType(String type){
        /**
         * 0-银行转账
         * 1-现金
         * 2-贷记凭证
         * 3-银行汇票
         * 4-电汇
         * 5-现金支票
         * 6-转账支票
         * 7-其他
         *
         * 贷记凭证-05；现金-01；银行汇票-02；电汇-06；现金支票-09；转账支票-10；银行转账-11；其他-99
         *
         * */
        String CSSCode="";
        if(StringUtils.isNotEmpty(type)){

            switch(type){
                case "0":
                    CSSCode="11";
                    break;
                case "1":
                    CSSCode="01";
                    break;
                case "2":
                    CSSCode="05";
                    break;
                case "3":
                    CSSCode="02";
                    break;
                case "4":
                    CSSCode="06";
                    break;
                case "5":
                    CSSCode="09";
                    break;
                case "6":
                    CSSCode="10";
                    break;
                case "7":
                    CSSCode="99";
                    break;
            }
        }
        return CSSCode;
    }

    private static String getbankname(String str){
        String sql = "select khyxmc from  uf_gys_dt1 where id= '"+str+"'";
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery(sql);
        return recordSet.next()?recordSet.getString("khyxmc"):"";
    }


    private static String codeIntercept(String str){
        if (str.length()<3){
            return str;
        }else {
            String[] strs= str.split("-");
            if(strs.length<2){
                return strs[0];
            }
            return (strs[0]+"-"+strs[1]);
        }
    }


    private static String getCodeNameById(String id){
        RecordSet rs = new RecordSet();
        String sql="select codeName from fnabudgetfeetype where id='"+id+"'";
        rs.execute(sql);
        String code="";
        if(rs.next()){
            code=Util.null2String(rs.getString("codeName"));
        }
        return code;
    }

    private static String getCostCenter(String id){
        RecordSet rs = new RecordSet();
        String code="";
        String sql="select cbzxbm from uf_cbzx where id = '"+id+"'";
        rs.execute(sql);
        if(rs.next()){
            code=Util.null2String(rs.getString("cbzxbm"));
        }
        return code;
    }

    private  String getProjectName(String id){
        RecordSet rs = new RecordSet();
        String code="";
        String sql="select xmmc from "+uf_klt_yfxmtz+" where id = '"+id+"'";
        rs.execute(sql);
        if(rs.next()){
            code=Util.null2String(rs.getString("xmmc"));
        }
        return code;
    }


    private String getProjectCode(String id){
        RecordSet rs = new RecordSet();
        String code="";
        String sql="select "+xmdm+" from "+uf_klt_yfxmtz+" where id = '"+id+"'";
        rs.execute(sql);
        if(rs.next()){
            code=Util.null2String(rs.getString(xmdm));
        }
        return code;
    }
}
