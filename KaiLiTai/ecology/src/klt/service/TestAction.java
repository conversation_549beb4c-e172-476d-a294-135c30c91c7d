package klt.service;


import java.util.*;

public class TestAction {

public int maxFrequency(int[] nums, int k) {
Arrays.sort(nums);
int n = nums.length;
long total = 0;
int l = 0, res = 1;
for (int r = 1; r < n; ++r) {
    total += (long) (nums[r] - nums[r - 1]) * (r - l);
    while (total > k) {
        total -= nums[r] - nums[l];
        ++l;
    }
    res = Math.max(res, r - l + 1);
}
return res;
}

    public int minPairSum(int[] nums) {
        Arrays.sort(nums);
        int num = Integer.MIN_VALUE;
        for (int i = 0; i < nums.length/2; i++) {
            num = Math.max(num,nums[i]+nums[nums.length-i-1]);
        }
        return num;
    }





    class Node {
        int val;
        Node next;
        Node random;

        public Node(int val) {
            this.val = val;
            this.next = null;
            this.random = null;
        }
    }


//    public Node copyRandomList(Node head,Node copy) {
//        if(head.next == null){
//            copy.next=null;
//            return copy;
//        }
//        copy.next = new Node(head.next.val);
//        Node node =  copyRandomList(head.next,copy.next);
//        Node node2 =  head.random;
//    }


//public int maxFrequency(int[] nums, int k) {
//    Arrays.sort(nums);
//    int number=0 ,value ;
//    HashMap<Integer, Integer> map  = new HashMap<>();
//    if(nums.length<2){
//        return nums[0]+k;
//    }
//    int i =nums.length-1,j= nums.length-1;
//    while(i>=0){
//        value = nums[i];
//        while((value+k)/nums[i]>=j){
//            if(j<0){
//                return Math.max(i-j%i,j%i);
//            }
//            value += nums[j];
//            j--;
//        }
//        number = Math.max(number,j-i);
//        i--;
//
//    }
//    return number;
//}
//    public boolean isCovered(int[][] ranges, int left, int right) {
//        int[][] arrays = new int[ranges.length][2];
//
//
//    }
//
//    public static void main(String[] args) {
//        int[][] arrays = new int[][];
//
//    }



    public static String maximumTime(String time) {
        String[] strings = time.split(":");
        StringBuilder ansstring = new StringBuilder();
        char hour = strings[0].charAt(0);
        char hour2 = strings[0].charAt(1);
        char minute = strings[1].charAt(0);
        char minute2 = strings[1].charAt(1);
        if(hour2=='?'){
            if(hour!='?'){
                if(hour=='1'||hour=='0'){
                    ansstring.append(hour+"9");
                }else{
                    ansstring.append(hour+"3");
                }
            }else {
                ansstring.append("23");
            }
        }else {
            if(hour!='?'){
                ansstring.append(strings[0]);
            }else {
                if (hour2<='3'){
                    ansstring.append("2"+hour2);
                }else {
                    ansstring.append("1"+hour2);
                }
            }
        }
        ansstring.append(":");
        if(minute=='?'){
            ansstring.append('5');
        }else{
            ansstring.append(minute);
        }
        if (minute2=='?'){
            ansstring.append("9");
        }else {
            ansstring.append(minute2);
        }

        return ansstring.toString();
    }


//    public int[] restoreArray(int[][] adjacentPairs) {
//        List<List<Integer>> list = new ArrayList<>();
//        for (int[] ad : adjacentPairs){
//          List<Integer> lista =  new LinkedList<>();
//          lista.add(ad[0]);
//          lista.add(ad[1]);
//          list.add(lista);
//        }
//
//        while (list.size()>1){
//           int left = list.get(0).get(0);
//           int right = list.get(0).get(list.get(0).size()-1);
//           if()
//        }
//    }

    public static void main(String[] args) {
        maximumTime("2?:?0");
    }
}
