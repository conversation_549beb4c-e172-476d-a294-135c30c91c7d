//package klt.service;
//
//import org.apache.commons.lang3.StringUtils;
//import weaver.conn.RecordSet;
//import weaver.general.BaseBean;
//import weaver.general.Util;
//import weaver.interfaces.workflow.action.Action;
//import weaver.soa.workflow.request.Property;
//import weaver.soa.workflow.request.RequestInfo;
//import weaver.workflow.request.RequestManager;
//import weaver.workflow.workflow.WorkflowComInfo;
//
//
//public class ContractPaymentAction extends BaseBean implements Action {
//    public ContractPaymentAction() {
//    }
//
//    @Override
//    public String execute(RequestInfo requestInfo) {
//        String retStr = "1";
//        RequestManager rm = requestInfo.getRequestManager();
//        WorkflowComInfo workflowComInfo = null;
//        workflowComInfo = new WorkflowComInfo();
//        int requestid = Util.getIntValue(requestInfo.getRequestid());
//        int cjr = Util.getIntValue(requestInfo.getCreatorid());
//        String requestname = Util.null2String(requestInfo.getRequestManager().getRequestname());
//        int workflowid = Util.getIntValue(requestInfo.getWorkflowid());
//        int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));
//        (new StringBuilder()).append("formtable_main_").append(Math.abs(formid)).toString();
//        int wfstatus = requestInfo.getRequestManager().getCreater();
//        this.writeLog("requestid = " + requestid);
//        this.writeLog("workflowid = " + workflowid);
//        this.writeLog("formid = " + formid);
//        String htxx = "";
//        Property[] properties = requestInfo.getMainTableInfo().getProperty();
//
//        for(int i = 0; i < properties.length; ++i) {
//            String field = properties[i].getName();
//            if ("htxx".equalsIgnoreCase(field)) {
//                htxx = Util.null2String(properties[i].getValue());
//            }
//        }
//
//        RecordSet rs = new RecordSet();
//        boolean flag = true;
//        if (StringUtils.isBlank(htxx)) {
//            rm.setMessagecontent("合同编号为空，请检查后重试！");
//            return "0";
//        } else {
//            String sql1 = "select count(1) num from formtable_main_100 a left join workflow_requestbase wr on wr.requestid = a.requestId where wr.currentnodetype<3 and wr.currentnodetype>0 and a.htxx='" + htxx + "'";
//            rs.execute(sql1);
//            if (rs.next()) {
//                int num = Util.getIntValue(rs.getInt("num"));
//                if (num > 0) {
//                    flag = false;
//                }
//            }
//
//            String sql2 = "select count(1) num from formtable_main_102 a left join workflow_requestbase wr on wr.requestid = a.requestId where wr.currentnodetype<3 and wr.currentnodetype>0 and a.htxx='" + htxx + "'";
//            rs.execute(sql2);
//            if (rs.next()) {
//                int num = Util.getIntValue(rs.getInt("num"));
//                if (num > 0) {
//                    flag = false;
//                }
//            }
//
//            String sql3 = "select count(1) num from formtable_main_103 a left join workflow_requestbase wr on wr.requestid = a.requestId where wr.currentnodetype<3 and wr.currentnodetype>0 and a.htxx='" + htxx + "'";
//            rs.execute(sql3);
//            if (rs.next()) {
//                int num = Util.getIntValue(rs.getInt("num"));
//                if (num > 0) {
//                    flag = false;
//                }
//            }
//
//            String sql4 = "select count(1) num from formtable_main_185 a left join workflow_requestbase wr on wr.requestid = a.requestId where wr.currentnodetype<3 and wr.currentnodetype>0 and a.htxx='" + htxx + "'";
//            rs.execute(sql4);
//            if (rs.next()) {
//                int num = Util.getIntValue(rs.getInt("num"));
//                if (num > 0) {
//                    flag = false;
//                }
//            }
//
//
//            if (!flag) {
//                rm.setMessagecontent("本合同存在审批中的流程，请及时处理后再发起！");
//                retStr = "0";
//            }
//
//            return retStr;
//        }
//    }
//}
