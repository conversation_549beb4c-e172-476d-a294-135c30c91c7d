package klt.service;

import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

import java.util.HashMap;

public class GongYingShangAction extends BaseBean implements Action {
    //供应商接口
    private final String SERVICE_URL="http://192.168.2.208:8080/KaiLiTaiFWService.svc/VendorData/Add";
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        RequestManager rm = request.getRequestManager();
        writeLog(this.getClass().getName() + " -- Action xxxx ");
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(request.getRequestid());   //requestid
            int cjr = Util.getIntValue(request.getCreatorid()); //创建人
            String requestname = Util.null2String(request.getRequestManager().getRequestname());    //标题
            int workflowid = Util.getIntValue(request.getWorkflowid()); //流程id
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));  //表单id
            String maintablename = "formtable_main_" + (Math.abs(formid));  //表名
            int wfstatus = request.getRequestManager().getCreater(); //（代码执行时）当前节点
            writeLog("requestid = " + requestid);   //统一日志类
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);
           /*
           " cVenCode": "",     //供应商编码
           " cVenName": "" ,    //供应商名称
           * */
            String cVenCode="";
            String cVenName="";
            Property[] properties = request.getMainTableInfo().getProperty();// 获取表单主字段信息
            HashMap<Object, Object> map = new HashMap<>();
            for (int i = 0; i < properties.length; i++) {
                String field = properties[i].getName();// 主字段名称
                if("gysbm".equalsIgnoreCase(field)){
                    cVenCode=Util.null2String(properties[i].getValue());
                    map.put("cVenCode",cVenCode);
                }
                if("gysmc".equalsIgnoreCase(field)){
                    cVenName=Util.null2String(properties[i].getValue());
                    map.put("cVenName",cVenName);
                }
            }
            writeLog("供应商cVenCode==="+cVenCode+"======cVenName=="+cVenName);
            JSONObject json = JSONObject.fromObject(map);
            //调用接口
            HttpPostAndGetService httpService = new HttpPostAndGetService();
            String res = sendpost(SERVICE_URL, json.toString());
            //String res = httpService.doPost(SERVICE_URL, json.toString());
            if(StringUtils.isNotEmpty(res)){
                JSONObject resjson = JSONObject.fromObject(res);
                String dsc = resjson.getString("dsc");
                String succeed = resjson.getString("succeed");
                if("1".equals(succeed)){
                    writeLog("success");    //输出最终日志标示
                }else{
                    retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
                    writeLog("request failed");    //输出最终日志标示
                }
            }

        } catch (Exception ex) {
            // 手动报错处理，挂节点后附加操作，可以阻碍流程提交
            if (rm != null) {
                // rm.setMessage("message 错误");
                rm.setMessagecontent("流程调用添加供应商接口失败，请联系管理员！"); //设置错误提示，显示在流程表单上部
                retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }
            writeLog(ex);
        }

        return retStr;
    }
    private static String sendpost(String url,String param){
        HttpClient httpClient = null;
        PostMethod postMethod = null;
        String retStr = "";
        try {
            httpClient = new HttpClient();
            httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
            httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
            postMethod = new PostMethod(url);
            RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
            postMethod.setRequestEntity(se);
            httpClient.executeMethod(postMethod);
            if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
                retStr = postMethod.getResponseBodyAsString().trim();

            }
        } catch (Exception e) {
            new BaseBean().writeLog("调用接口出错："+e);
        } finally {
            try {
                if (postMethod != null) {
                    postMethod.releaseConnection();
                }
            } catch (Exception e) {
                new BaseBean().writeLog("关闭连接出错："+e);
            }
        }
        return retStr;
    }
}
