package klt.service;
import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.lang3.StringUtils;
import utils.WriteLogtoTable.WriteLog2table;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class NewSupplierAction extends BaseBean implements Action  {
    public String fieldid;

    public String getFieldid() {
        return fieldid;
    }

    public void setFieldid(String fieldid) {
        this.fieldid = fieldid;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        Map<String,String> map = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }
        String[] we =   {"gysbm",   "gysmc",   "gysjc", "tyshxydm","khyx",      "zczj",  "yxzh",      "szyx",      "fr",    "szzx",      "lxr",    "dz",    "dh",    "sj"};
        String[] them = {"cVenCode","cVenName","cVenJT","cVenSH",  "cVenKHbank","cVenZJ","cVenbankZH","cVenSCbank","cVenFR","cVenSCtype","cVenLXR","cVenDZ","cVenDH","cVenSJ"};
        HashMap<String,String> ansmap = new HashMap<>();
        int len = we.length;
        for (int i = 0; i < len; i++) {
            ansmap.put(them[i],map.get(we[i]));
        }
        ansmap.put("cVenBZ","人民币");
        ansmap.put("cVentype",getCheck(map.get("flyc")));
        JSONObject json = JSONObject.fromObject(ansmap);
        writeLog("转换json成功");
        String serviceurl="http://192.168.2.164:8080/KaiLiTaiFWService.svc/VendorData/Add";
        String result = "";
        try {
            result = sendpost(serviceurl,json.toString());
        } catch (Exception e) {
            requestInfo.getRequestManager().setMessagecontent("接口不通报错");
            WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),"接口不通报错",requestid+""},1,103);
            return Action.FAILURE_AND_CONTINUE;
        }
        if(StringUtils.isNotEmpty(result)){
            JSONObject jsonObject = JSONObject.fromObject(result);
            String succeed = jsonObject.getString("succeed");
            String dsc = jsonObject.getString("dsc");
            if("true".equalsIgnoreCase(succeed)){
                writeLog("success");    //输出最终日志标示
            }else{
                requestInfo.getRequestManager().setMessagecontent(dsc);
                WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
                return Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }
        }
        WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
        return Action.SUCCESS;
    }

    private static String sendpost(String url,String param) throws IOException {
        HttpClient httpClient = null;
        PostMethod postMethod = null;
        String retStr = "";
        httpClient = new HttpClient();
        httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
        httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
        postMethod = new PostMethod(url);
        RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
        postMethod.setRequestEntity(se);
        httpClient.executeMethod(postMethod);
        if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
            retStr = postMethod.getResponseBodyAsString().trim();
            // System.out.println("retStr = " + retStr);
        }
        if (postMethod != null) {
            postMethod.releaseConnection();
        }
        return retStr;
    }

    public String getCheck(String number){
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery("select selectname from workflow_selectitem where fieldid = "+fieldid+" and selectvalue = "+number);
        if(recordSet.next()){
            return recordSet.getString("selectname");
        }
        return "";
    }

    private static String codeIntercept(String str){
        if (str.length()<3){
            return str;
        }else {
            String[] strs= str.split("-");
            if(strs.length<2){
                return strs[0];
            }
            return (strs[0]+"-"+strs[1]);
        }
    }



}
