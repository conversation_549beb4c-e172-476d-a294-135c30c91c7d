package klt.service;

import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.lang3.StringUtils;
import utils.WriteLogtoTable.WriteLog2table;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class YinFuForTripAction extends BaseBean implements Action {
//应付单接口--差旅费用报销
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        RequestManager rm = request.getRequestManager();
        writeLog(this.getClass().getName() + " -- Action xxxx ");
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(request.getRequestid());   //requestid
            int cjr = Util.getIntValue(request.getCreatorid()); //创建人
            String requestname = Util.null2String(request.getRequestManager().getRequestname());    //标题
            int workflowid = Util.getIntValue(request.getWorkflowid()); //流程id
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));  //表单id
            String maintablename = "formtable_main_" + (Math.abs(formid));  //表名
            int wfstatus = request.getRequestManager().getCreater(); //（代码执行时）当前节点
            int userId = request.getRequestManager().getUserId();
            String spcode = getWorkCodeById("" + userId);
            writeLog("requestid = " + requestid);   //统一日志类
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);

            String CVouchID="";
            String CDwCode="";
            String CDepCode="";
            String CPerson="";
            String CPayCode="";
            String CItemCode="";
            String IAmountList="";
            String  CDigestList="";
            String isxmflag = "";
            String xmmc = "";

            String sqr="";

            Property[] properties = request.getMainTableInfo().getProperty();// 获取表单主字段信息
            Date date = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = dateFormat.format(date);
            //科目编码  在明细中需要循环去拿
            //币种  在明细中需要循环去拿
            //汇率  在明细中需要循环去拿
            //本币金额        在明细中需要循环去拿
            //数量   ""
            //金额  dt1.sjbxje
            //摘要        dt1.cwzy

            for (int i = 0; i < properties.length; i++) {
                String field = properties[i].getName();// 主字段名称
                //流程编号lcbh
                if("lcbh".equalsIgnoreCase(field)){
                    CVouchID=Util.null2String(properties[i].getValue());
                }
                //供应商
                if("sqr".equalsIgnoreCase(field)){
                    String skr=Util.null2String(properties[i].getValue());
                    sqr = getWorkCodeById(skr);
                   // CPerson=CDwCode;
                }
                //供应商
                if("skrxm".equalsIgnoreCase(field)){
                    String skr=Util.null2String(properties[i].getValue());
                    CDwCode = getWorkCodeById(skr);
                }
                //部门
                if("cbzx1".equalsIgnoreCase(field)){
                    String codeById=Util.null2String(properties[i].getValue());
                    CDepCode = getCBCodeById(codeById);
                }

                //项目编码
                if("xmbh".equalsIgnoreCase(field)){
                   CItemCode=Util.null2String(properties[i].getValue());
                }
                if("sjbxzje".equalsIgnoreCase(field)){
                    IAmountList=Util.null2String(properties[i].getValue());
                }

                if("sfxyxm".equalsIgnoreCase(field)){
                    isxmflag=Util.null2String(properties[i].getValue());
                }


                if("xmmc".equalsIgnoreCase(field)){
                    xmmc=Util.null2String(properties[i].getValue());
                }


                if("ysqk".equalsIgnoreCase(field)){
                    CDigestList=Util.null2String(properties[i].getValue()).length()<300?Util.null2String(properties[i].getValue()):Util.null2String(properties[i].getValue()).substring(0,300);
                }

                //预算科目
                writeLog("name===========>"+field);
            }
            //取明细数据
            DetailTable[] detailtable = request.getDetailTableInfo().getDetailTable();
            ArrayList<Object> dataList = new ArrayList<>();
            // 获取所有明细表
            if (detailtable.length > 0) {
                for (int i = 0; i < detailtable.length; i++) {
                    DetailTable dt = detailtable[i];
                    // 指定明细表

                    //明细摘要字段
                    String digestcol = "cwzy"+(i+1);
                    writeLog("cwzy"+digestcol);


                    Row[] s = dt.getRow();
                    // 当前明细表的所有数据,按行存储
                    for (int j = 0; j < s.length; j++) {
                        Row r = s[j];
                        // 指定行
                        Cell c[] = r.getCell();
                        Map rowmap =new HashMap<>();
                        // 每行数据再按列存储
                        for (int k = 0; k < c.length; k++) {
                            // 指定列
                            Cell c1 = c[k];
                            // 明细字段名称
                            String name = c1.getName();
                            String value = c1.getValue();
                            rowmap.put("CexchList_name","人民币");
                            rowmap.put("IExchRateList","1");
                            if(digestcol.equalsIgnoreCase(name)){
                                rowmap.put("CDigestList",value.length()<300?value:value.substring(0,300));
                            }
                            rowmap.put("Bd_c","1");
                            if("sjbxje".equalsIgnoreCase(name)){
                                rowmap.put("IAmount_fList",value);
                                rowmap.put("IAmountList",value);
                            }
                            rowmap.put("CPersonList",CPerson);
                            if("1".equals(isxmflag)){
                                if(StringUtils.isEmpty(CDwCode)){
                                    CItemCode=sqr;
                                }else {
                                    CItemCode=CDwCode;
                                }
                                //保留逻辑
                                rowmap.put("CItemCode",codeIntercept(CItemCode));
                            }else if("0".equals(isxmflag)){
                                rowmap.put("CItemCode",xmmc);
                            }
                            if("yskm".equalsIgnoreCase(name)){
                                value= getCodeNameById(value);
                                rowmap.put("CCode",value);
                            }
                            // 明细字段的值
                           writeLog("name=="+name+"====value==="+value);
                        }
                        dataList.add(rowmap);
                    }
                }
            }

            Map parammap =new HashMap<>();
            parammap.put("CVouchID",CVouchID);
            parammap.put("DVouchDate",format);
            if(StringUtils.isEmpty(CDwCode)){
                CDwCode=sqr;
            }
            parammap.put("CDwCode",codeIntercept(CDwCode));
            parammap.put("CCode","22410108");
            parammap.put("CDepCode",CDepCode);
            parammap.put("CPerson", codeIntercept(CDwCode));
            parammap.put("Bd_c","0");
            parammap.put("IAmount_fList",IAmountList);
            parammap.put("IAmountList",IAmountList);
            parammap.put("CDigest",CDigestList);
            parammap.put("CexchList_name","人民币");
            parammap.put("IAmount_s","");
            parammap.put("SPCPerson",codeIntercept(sqr));
            parammap.put("Items",dataList);

            JSONObject json = JSONObject.fromObject(parammap);
            writeLog("json--yingfutrip--"+json);
            //调用接口
            BaseBean bean = new BaseBean();
            String serviceurl= bean.getPropValue("U8interface","yingfuurl");
            String result = sendpost(serviceurl, json.toString());
            WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
            if(StringUtils.isNotEmpty(result)){
                JSONObject jsonObject = JSONObject.fromObject(result);
                String succeed = jsonObject.getString("succeed");
                String dsc = jsonObject.getString("dsc");
                if("true".equalsIgnoreCase(succeed)){
                    writeLog("yingfutrip success");    //输出最终日志标示
                }else{
                    rm.setMessagecontent(dsc);
                    retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
                    writeLog("yingfutrip request failed "+dsc);    //输出最终日志标示
                }
            }
        } catch (Exception ex) {
            // 手动报错处理，挂节点后附加操作，可以阻碍流程提交
            if (rm != null) {
                // rm.setMessage("message 错误");
                rm.setMessagecontent("流程调用应付单接口失败，请联系管理员！");
                WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{"应付单接口--差旅费用报销","此报错可能由于接口不通或者网络原因等不可控逻辑造成","如重试不行请联系管理员",request.getRequestid()},1,103);//设置错误提示，显示在流程表单上部
                retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }
            writeLog(ex);
        }

        return retStr;
    }
    private static String getCBCodeById(String id ){
        RecordSet rs = new RecordSet();
        String code="";
        String sql="select  cbzxbm from uf_cbzx where id="+id;
        rs.execute(sql);
        if(rs.next()){
            code=Util.null2String(rs.getString("cbzxbm"));
        }
        return code;
    }
    private static String changFukuanType(String type){
        /**
         * 0-银行转账
         * 1-现金
         * 2-贷记凭证
         * 3-银行汇票
         * 4-电汇
         * 5-现金支票
         * 6-转账支票
         * 7-其他
         *
         * 贷记凭证-05；现金-01；银行汇票-02；电汇-06；现金支票-09；转账支票-10；银行转账-11；其他-99
         *
         * */
        String CSSCode="";
        if(StringUtils.isNotEmpty(type)){

            switch(type){
                case "0":
                    CSSCode="11";
                    break;
                case "1":
                    CSSCode="01";
                    break;
                case "2":
                    CSSCode="05";
                    break;
                case "3":
                    CSSCode="02";
                    break;
                case "4":
                    CSSCode="06";
                    break;
                case "5":
                    CSSCode="09";
                    break;
                case "6":
                    CSSCode="10";
                    break;
                case "7":
                    CSSCode="99";
                    break;
            }
        }
        return CSSCode;
    }
    private static String getWorkCodeById(String id ){
        RecordSet rs = new RecordSet();
        String workcode="";
        String sql="select workcode from hrmresource where id ="+id;
        rs.execute(sql);
        if (rs.next()){
            workcode = Util.null2String(rs.getString("workcode"));
        }
        return workcode;
    }
    private static String getFnaCodeNameById(String id){
        RecordSet rs = new RecordSet();
        String codename="";
        String sql="select codeName from FnaBudgetfeeType where id ="+id;
        rs.execute(sql);
        if (rs.next()){
            codename = Util.null2String(rs.getString("codeName"));
        }
        return codename;
    }

    private static String codeIntercept(String str){
        if (str.length()<3){
            return str;
        }else {
            String[] strs= str.split("-");
            if(strs.length<2){
                return strs[0];
            }
             return (strs[0]+"-"+strs[1]);
        }
    }


    private static String getCodeNameById(String id){
        RecordSet rs = new RecordSet();
        String sql="select codeName from fnabudgetfeetype where id='"+id+"'";
        rs.execute(sql);
        String code="";
        if(rs.next()){
            code=Util.null2String(rs.getString("codeName"));
        }
        return code;
    }
    private static String sendpost(String url,String param) throws IOException {
        HttpClient httpClient = null;
        PostMethod postMethod = null;
        String retStr = "";

            httpClient = new HttpClient();
            httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
            httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
            postMethod = new PostMethod(url);
            RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
            postMethod.setRequestEntity(se);
            httpClient.executeMethod(postMethod);
            if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
                retStr = postMethod.getResponseBodyAsString().trim();
                // System.out.println("retStr = " + retStr);
            }
            if (postMethod != null) {
                    postMethod.releaseConnection();
            }
        return retStr;
    }

}
