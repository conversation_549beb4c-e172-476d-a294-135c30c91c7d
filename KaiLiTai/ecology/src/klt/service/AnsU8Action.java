package klt.service;
import com.wbi.util.DateHelper;
import com.wbi.util.Util;
import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.lang3.StringUtils;
import utils.WriteLogtoTable.WriteLog2table;
import weaver.interfaces.workflow.action.Action;
import weaver.general.BaseBean;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class AnsU8Action extends BaseBean implements Action{

    private String apdyid;

    private String apsqid;

    public String getApdyid() {
        return apdyid;
    }

    public void setApdyid(String apdyid) {
        this.apdyid = apdyid;
    }

    public String getApsqid() {
        return apsqid;
    }

    public void setApsqid(String apsqid) {
        this.apsqid = apsqid;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        Map<String,String> map = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }

        HashMap<String,Object> ansmap = new HashMap<>();
        ansmap.put("DVouchDate", DateHelper.getDate(new Date()));
        ansmap.put("APDYID", map.get(Util.null2String(apdyid)));
        ansmap.put("APSQID", map.get(Util.null2String(apsqid)));
        JSONObject json = JSONObject.fromObject(ansmap);
        String serviceurl="http://192.168.2.164:8080/KaiLiTaiFWService.svc/APHXSV/Add";
        String result = "";
        try {
            result = sendpost(serviceurl,json.toString());
        } catch (Exception e) {
            requestInfo.getRequestManager().setMessagecontent("接口不通报错");
            WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
            return Action.FAILURE_AND_CONTINUE;
        }
        if(StringUtils.isNotEmpty(result)){
            JSONObject jsonObject = JSONObject.fromObject(result);
            String succeed = jsonObject.getString("succeed");
            String dsc = jsonObject.getString("dsc");
            if("true".equalsIgnoreCase(succeed)){
                writeLog("success");    //输出最终日志标示
            }else{
                requestInfo.getRequestManager().setMessagecontent(dsc);
                WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
                return Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }
        }
        WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,requestid+""},1,103);
        return Action.SUCCESS;
    }
    private static String sendpost(String url,String param) throws IOException {
        HttpClient httpClient = null;
        PostMethod postMethod = null;
        String retStr = "";
        httpClient = new HttpClient();
        httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
        httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
        postMethod = new PostMethod(url);
        RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
        postMethod.setRequestEntity(se);
        httpClient.executeMethod(postMethod);
        if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
            retStr = postMethod.getResponseBodyAsString().trim();
            // System.out.println("retStr = " + retStr);
        }
        if (postMethod != null) {
            postMethod.releaseConnection();
        }
        return retStr;
    }
}
