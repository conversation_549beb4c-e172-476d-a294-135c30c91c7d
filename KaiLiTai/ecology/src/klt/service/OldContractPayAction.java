package klt.service;

import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.lang3.StringUtils;
import utils.WriteLogtoTable.WriteLog2table;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

public class OldContractPayAction extends BaseBean implements Action {
//付款单接口 对应无合同和老合同付款申请
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        RequestManager rm = request.getRequestManager();
        writeLog(this.getClass().getName() + " -- Action xxxx ");
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(request.getRequestid());   //requestid
            int cjr = Util.getIntValue(request.getCreatorid()); //创建人
            String requestname = Util.null2String(request.getRequestManager().getRequestname());    //标题
            int workflowid = Util.getIntValue(request.getWorkflowid()); //流程id
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));  //表单id
            String maintablename = "formtable_main_" + (Math.abs(formid));  //表名
            int wfstatus = request.getRequestManager().getCreater(); //（代码执行时）当前节点
            int userId = request.getRequestManager().getUserId();
            String spcode = getWorkCodeById("" + userId);
            writeLog("requestid = " + requestid);   //统一日志类
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);

            String CCusVen="";
            String CDepCode="";
            String CPerson="";
            String CCode="";
            String CSSCode="";
            String CDefine12="";
            String CDefine13="";
            String IAmt="";
            String IAmt_f="";
            String DPrePayDate="";
            /**
             * {
             *     "CVouchID":"2020080120",单据编号
             *     "DVouchDate":"2020-12-21",日期
             *
             *     "CCusVen":"V98F3500031",供应商/客户编码
             *
             *     "CDepCode":"040106",部门编码
             *     "CPerson":"0864",人员编码
             *     "Cexch_name":"人民币",币种
             *     "IExchRate":"13",汇率
             *     "CSSCode":"01",结算方式
             *     "CCode":"",结算科目编码
             *     "CDigest":"",摘要
             *     "CDefine12":"浦东支行",开户行
             *     "CDefine13":"6200001551612",银行账号
             *     "IAmt":"108",金额
             *     "IAmt_f":"108",本币金额
             *     "DPrePayDate":"2020-12-21",预计付款日期
             *     "Items":[
             *         {
             *             "CMemo":"22",表体备注
             *             "CCusVen":"V98F3500031",客户或供应商编码
             *             "CKm":"220201",科目编码
             *             "CXmClass":"",项目大类编码
             *             "CXm":"",项目编码
             *             "CDepCode":"040106",部门编码
             *             "CItemName":"",项目名称
             *             "CConType":"",合同类型
             *             "CConID":"",合同号
             *             "iAmt_s":"0",数量
             *             "IAmt":"108",金额
             *             "IAmt_f":"108"本币金额
             *         }
             *     ]
             * }
             * */

            Property[] properties = request.getMainTableInfo().getProperty();// 获取表单主字段信息
            HashMap<String, Object> map = new HashMap<>();
            Date date = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = dateFormat.format(date);
            //币种默认 CNY  汇率1
            map.put("DVouchDate",format);
            map.put("Cexch_name","人民币");
            map.put("IExchRate","1");
            map.put("SPCPerson",spcode);



            for (int i = 0; i < properties.length; i++) {
                String field = properties[i].getName();// 主字段名称
                //供应商编码
                if("lcbh".equalsIgnoreCase(field)){
                    String lcbh=Util.null2String(properties[i].getValue());
                    // String code = getCCusVenById(CDwCode);
                    map.put("CVouchID",lcbh);
                }
                if("gys".equalsIgnoreCase(field)){
                    String id=Util.null2String(properties[i].getValue());
                    // String code = getCCusVenById(CDwCode);
                     CCusVen= getNameById(id);
                    map.put("CCusVen",CCusVen);
                }
                if("fkfs".equalsIgnoreCase(field)){
                    String fs=Util.null2String(properties[i].getValue());
                    String fukuanType = changFukuanType(fs);
                    map.put("CSSCode",fukuanType);
                }

                //部门编码
                if("cbzx".equalsIgnoreCase(field)){
                    String codeById =Util.null2String(properties[i].getValue());
                    CDepCode  = getCBCodeById(codeById);
                    map.put("CDepCode",CDepCode);
                }
                if("yskm".equalsIgnoreCase(field)){
                    String yskm=Util.null2String(properties[i].getValue());
                    CCode= getCodeNameById(yskm);
                    map.put("CCode",CCode);
                }
                //业务员编码
                if("sqr".equalsIgnoreCase(field)){
                    String pid=Util.null2String(properties[i].getValue());
                    String workcode = codeIntercept(getWorkCodeById(pid));
                    map.put("CPerson", workcode);
                }
                if("yxmc".equalsIgnoreCase(field)){
                    String yxmc=Util.null2String(properties[i].getValue());
                    String bankname= getBankName(yxmc);
                    map.put("CDefine12",bankname);
                }
                if("yxzh".equalsIgnoreCase(field)){
                    String yxzh=Util.null2String(properties[i].getValue());
                    map.put("CDefine13",yxzh);
                }
                if("bbje".equalsIgnoreCase(field)){
                    IAmt=Util.null2String(properties[i].getValue());
                    map.put("IAmt",IAmt);
                    map.put("IAmt_f",IAmt);
                }
                if("htzytk".equalsIgnoreCase(field)){
                    String bzsm=Util.null2String(properties[i].getValue());
                    map.put("CDigest",bzsm);
                }
                if("fkrq".equalsIgnoreCase(field)){
                    String yqfkrq=Util.null2String(properties[i].getValue());
                    map.put("DPrePayDate",yqfkrq);
                }
            }
            ArrayList<Object> list = new ArrayList<>();
            HashMap<String, Object> rowmap = new HashMap<>();
            rowmap.put("CMemo","");
            rowmap.put("CCusVen",CCusVen);
            rowmap.put("CKm",CCode);
            rowmap.put("CXmClass","");
            rowmap.put("CXm","");
            rowmap.put("CDepCode",CDepCode);
            rowmap.put("CItemName","");
            rowmap.put("CConType","");
            rowmap.put("CConID","");
            rowmap.put("iAmt_s","0");
            rowmap.put("IAmt",IAmt);
            rowmap.put("IAmt_f",IAmt);
            list.add(rowmap);
            map.put("Items",list);
            JSONObject json = JSONObject.fromObject(map);
            writeLog("json--fukuan--"+json);
            //调用接口
            BaseBean bean = new BaseBean();
            String serviceurl= bean.getPropValue("U8interface","apapplydataurl");
            String result = sendpost(serviceurl, json.toString());
            WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{serviceurl,json.toString(),result,""+requestid},1,103);
            if(StringUtils.isNotEmpty(result)){
                JSONObject jsonObject = JSONObject.fromObject(result);
                String succeed = jsonObject.getString("succeed");
                String dsc = jsonObject.getString("dsc");
                if("true".equalsIgnoreCase(succeed)){
                    writeLog("fukuan success");    //输出最终日志标示
                }else{
                    retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
                    rm.setMessagecontent(dsc);
                    writeLog("yingfutrip request failed "+dsc);    //输出最终日志标示
                }
            }

        } catch (Exception ex) {
            // 手动报错处理，挂节点后附加操作，可以阻碍流程提交
            if (rm != null) {
                // rm.setMessage("message 错误");
                WriteLog2table.ModuleInsert("uf_logthrowtable",new String[]{"dyjk","czjson","fhjson","lc"},new String[]{"付款单接口 对应无合同和老合同付款申请","此报错可能由于接口不通或者网络等不可控逻辑造成","重试不行请联系管理员",""+request.getRequestid()},1,103);
                rm.setMessagecontent("流程调用付款单接口失败，请联系管理员！"); //设置错误提示，显示在流程表单上部
                retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }
            writeLog(ex);
        }

        return retStr;
    }
    private static String getCCusVenById(String id){
        RecordSet rs = new RecordSet();
        String code="";
        String sql="select gysbm from uf_gys where id="+id;
        rs.execute(sql);
        if(rs.next()){
            code=Util.null2String(rs.getString("gysbm"));
        }
        return code;
    }
    private static String getCodeNameById(String id){
        RecordSet rs = new RecordSet();
            String sql="select codeName from fnabudgetfeetype where id='"+id+"'";
        rs.execute(sql);
        String code="";
        if(rs.next()){
            code=Util.null2String(rs.getString("codeName"));
        }
        return code;
    }
    private static String getCBCodeById(String id ){
        RecordSet rs = new RecordSet();
        String code="";
        String sql="select  cbzxbm from uf_cbzx where id="+id;
        rs.execute(sql);
        if(rs.next()){
            code=Util.null2String(rs.getString("cbzxbm"));
        }
        return code;
    }
    private static String sendpost(String url,String param) throws IOException {
        HttpClient httpClient = null;
        PostMethod postMethod = null;
        String retStr = "";
            httpClient = new HttpClient();
            httpClient.getParams().setParameter("http.connection.timeout", Integer.valueOf(60000));
            httpClient.getParams().setParameter("http.socket.timeout", Integer.valueOf(60000));
            postMethod = new PostMethod(url);
            RequestEntity se = new StringRequestEntity(param, "application/json", "UTF-8");
            postMethod.setRequestEntity(se);
            httpClient.executeMethod(postMethod);
            if ((postMethod != null) && (postMethod.getStatusCode() == 200)) {
                retStr = postMethod.getResponseBodyAsString().trim();
                // System.out.println("retStr = " + retStr);
            }

            if (postMethod != null) {
                postMethod.releaseConnection();
            }
        return retStr;
    }
    private static String changFukuanType(String type){
        /**
         * 0-银行转账
         * 1-现金
         * 2-贷记凭证
         * 3-银行汇票
         * 4-电汇
         * 5-现金支票
         * 6-转账支票
         * 7-其他
         *
         * 贷记凭证-05；现金-01；银行汇票-02；电汇-06；现金支票-09；转账支票-10；银行转账-11；其他-99
         *
         * */
        String CSSCode="";
        if(StringUtils.isNotEmpty(type)){

            switch(type){
                case "0":
                    CSSCode="11";
                    break;
                case "1":
                    CSSCode="01";
                    break;
                case "2":
                    CSSCode="05";
                    break;
                case "3":
                    CSSCode="02";
                    break;
                case "4":
                    CSSCode="06";
                    break;
                case "5":
                    CSSCode="09";
                    break;
                case "6":
                    CSSCode="10";
                    break;
                case "7":
                    CSSCode="99";
                    break;
            }
        }
        return CSSCode;
    }
    private static String getNameById(String id){
        RecordSet rs = new RecordSet();
        String sql="select gysmc from uf_gys where id='"+id+"'";
        String name="";
        rs.execute(sql);
        if(rs.next()){
            name =Util.null2String(rs.getString("gysmc"));
        }
        return name;
    }
    private static String getWorkCodeById(String id ){
        RecordSet rs = new RecordSet();
        String workcode="";
        String sql="select workcode from hrmresource where id ="+id;
        rs.execute(sql);
        if (rs.next()){
            workcode = Util.null2String(rs.getString("workcode"));
        }
        return workcode;
    }
    private static String getBankName(String id){
        String sql="select khyxmc from uf_gys_dt1 where id="+id;
        RecordSet rs = new RecordSet();
        String bankname="";
        rs.execute(sql);
        if(rs.next()){
            bankname=Util.null2String(rs.getString("khyxmc"));
        }
        return bankname;
    }
    private static String codeIntercept(String str){
        if (str.length()<3){
            return str;
        }else {
            String[] strs= str.split("-");
            if(strs.length<2){
                return strs[0];
            }
            return (strs[0]+"-"+strs[1]);
        }
    }
}
