<%--
  Created by IntelliJ IDEA.
  User: REN
  Date: 2020/12/2
  Time: 10:38
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
</head>
<body>

<script type="text/javascript">
    var sqrqid = WfForm.convertFieldNameToId("sqrq");
    var xjksrqid = WfForm.convertFieldNameToId("xjksrq","detail_1");
    var xjjsrqid = WfForm.convertFieldNameToId("xjjsrq","detail_1");
    var map = new Map();
    function judgeFieldLength(field){
        field =field+"";
        if(field.length==1){
            field="0"+field;
        }
        return field;
    }
    WfForm.registerCheckEvent(WfForm.OPER_SAVE+","+WfForm.OPER_SUBMIT,function(callback){
        let flag=map.get("flag");
        if(flag){
            //允许执行调用callback
            callback();
        }else{
            WfForm.showConfirm("申请日期不在对应的考勤时间段内！",function(){
            },function(){
            });
        }
    });

    jQuery().ready(function () {
        WfForm.bindDetailFieldChangeEvent(xjksrqid+","+xjjsrqid,function(id,rowIndex,value){
            let sqrq_value = WfForm.getFieldValue(sqrqid);
            let xjksrq_value = WfForm.getFieldValue(xjksrqid+"_"+rowIndex);
            let xjjsrq_value = WfForm.getFieldValue(xjjsrqid+"_"+rowIndex);
            if("" !=sqrq_value && ""!=xjksrq_value && ""!=xjjsrq_value){
              let  date = new Date(sqrq_value.replace(/-/, "/"));
              let year =date.getFullYear();
              let month = date.getMonth()+1;
              let day =date.getDate();
              var ksrq="";
              var jsrq="";
                if(day<=24){
                    //说明在当前月的周期内
                    //周期为
                    if(month==1){
                        let newmonth=12;
                        ksrq =year-1+"-"+newmonth+"-"+25;
                        month=judgeFieldLength(month);
                        jsrq=year+"-"+month+"-"+24;
                    }else{
                        let newmonth=month-1;
                        newmonth= judgeFieldLength(newmonth);
                        ksrq =year+"-"+newmonth+"-"+25;
                        month=judgeFieldLength(month);
                        jsrq=year+"-"+month+"-"+24;
                    }
                }else{
                    //需要考虑跨年
                   let m=judgeFieldLength(month);
                    ksrq=year+"-"+m+"-"+25;
                    if(month==12){
                        jsrq=year+1+"-"+"01"+"-"+24
                    }else{
                        jsrq=year+"-"+(month+1)+"-"+24
                    }
                }
                console.log(ksrq,jsrq);
            }
            let  startdate = new Date(ksrq.replace(/-/, "/"));
            let start =startdate.getTime();
            let  enddate = new Date(jsrq.replace(/-/, "/"));
            let end =enddate.getTime();
            xjksrq_value= new Date(xjksrq_value.replace(/-/, "/")).getTime();
            xjjsrq_value= new Date(xjjsrq_value.replace(/-/, "/")).getTime();
            console.log(start,end,xjksrq_value,xjjsrq_value);
            if(start<=xjksrq_value && xjksrq_value<=end && start<=xjjsrq_value && xjjsrq_value<=end){
                map.set("flag",true);
            }else{
                map.set("end",false);
            }

        });
    })


</script>

</body>
</html>
