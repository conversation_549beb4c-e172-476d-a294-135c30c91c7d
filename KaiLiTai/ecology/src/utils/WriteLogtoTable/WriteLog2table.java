package utils.WriteLogtoTable;


import com.weaver.formmodel.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.formmode.data.ModeDataIdUpdate;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.hrm.User;


/**
 *  写日志工具类
 *
 */

public class WriteLog2table {

    public static void ModuleInsert(String table,String[] insertField,String[] value,int Create,int module){
//      ModeDataIdUpdate modeDataIdUpdate = new ModeDataIdUpdate();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        User user = new User(Create);   //用户id
// 初始化建模数据返回id
        int billid = ModeDataIdUpdate.getInstance().getModeDataNewIdByUUID(
                table, module, user.getUID(), "1".equals(user.getLogintype()) ? 0 : 1, DateHelper.getCurrentDate(), DateHelper.getCurrentTime());
//将数据的其他内容更新到表中
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < insertField.length; i++) {
            stringBuffer.append(insertField[i]).append("=").append("?");
            if(i!=insertField.length-1){
                stringBuffer.append(", ");
            }
        }
        RecordSet recordSet = new RecordSet();
        new BaseBean().writeLog("update "+ table +" set "+stringBuffer.toString()+" where id="+billid);
        new BaseBean().writeLog(value);
        recordSet.executeUpdate("update "+ table +" set "+stringBuffer.toString()+" where id="+billid,value);
//添加对应数据的权限
        new Thread(() -> {
            modeRightInfo.setNewRight(true);
            modeRightInfo.editModeDataShare(Create, module,billid);//新建的时候添加共享
        }).start();
    }

}
