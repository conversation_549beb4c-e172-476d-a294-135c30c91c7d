package utils.insertmodule;
import com.wbi.util.DateHelper;
import jxl.read.biff.Record;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.formmode.data.ModeDataIdUpdate;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.text.SimpleDateFormat;
import java.util.Date;

public class InsertModuleUtil {

    public static void ModuleInsert(String table,String[] insertField,String[] value,int Create,int module){
//      ModeDataIdUpdate modeDataIdUpdate = new ModeDataIdUpdate();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        User user = new User(Create);   //用户id
// 初始化建模数据返回id
        int billid = ModeDataIdUpdate.getInstance().getModeDataNewIdByUUID(
                table, module, user.getUID(), "1".equals(user.getLogintype()) ? 0 : 1, DateHelper.getCurrentDate(), DateHelper.getCurrentTime());
//将数据的其他内容更新到表中
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < insertField.length; i++) {
            stringBuffer.append(insertField[i]).append("=").append("?");
            if(i!=insertField.length-1){
                stringBuffer.append(", ");
            }
        }
        RecordSet recordSet = new RecordSet();
        recordSet.executeUpdate("update "+ table +" set "+stringBuffer.toString()+" where id="+billid,value);
//添加对应数据的权限
        new Thread(() -> {
            modeRightInfo.setNewRight(true);
            modeRightInfo.editModeDataShare(Create, module,billid);//新建的时候添加共享
        }).start();
    }
}
