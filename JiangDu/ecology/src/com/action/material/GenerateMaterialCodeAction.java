package com.action.material;

import com.action.material.utils.InsertModuleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/5/31-14:22
 * @description : 生成物料编码Action(在流程转数据之前)
 * @workflowName : 物料管理-物料信息新增
 *  @workflowNode : 归档之前(在流程转数据之前)
 */
public class GenerateMaterialCodeAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {

        String searchsql = "SELECT id FROM uf_ck group by id";
        writeLog(searchsql+"========");
        RecordSet sqlutils = new RecordSet();
        sqlutils.execute(searchsql);
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String,String> map = new HashMap<>();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }

        while(sqlutils.next()){
            RecordSet sqlutil   = new RecordSet();
            String warehose = Util.null2String(sqlutils.getString("id"));
            String sql = "SELECT id from uf_wl where bm = '"+map.get("bm")+"'";
            sqlutil.execute(sql);
            writeLog(sql);
            String productname = "";
            while (sqlutil.next()){
                productname =Util.null2String(sqlutil.getString("id"));
            }
            InsertModuleUtil.ModuleInsert("uf_wlkc"
                                        ,new String[]{"ck","wlbm","wlmc","wlz","jldw","dqkc"}
                                        ,new String[]{warehose,map.get("bm"),productname,map.get("wlz"),map.get("jldw"),"0"}
                                        ,1
                                        ,25);
        }
        return "1";
    }
}
