package com.action.material;

import com.wbi.util.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/5/31-14:22
 * @description : 生成物料编码Action
 * @workflowName : 物料管理-物料信息新增
 * @workflowNode : 归档之前(在流程转数据之后)
 */
public class InsertInventoryDataAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        String requestid = Util.null2String(requestInfo.getRequestid());
        Map<String,String> map = new HashMap<>();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }
        String wlz = map.get("wlz");
        RecordSet sqlutils = new RecordSet();
        String sql = "SElECT LEFT(bm,3) as prefix , MAX(cast(SUBSTRING(bm,4,LEN(bm)) as int))as ans FROM uf_wl WHERE wlz ="+wlz+"group by LEFT(bm,3)";
        writeLog(sql +"");
        sqlutils.execute(sql);
        Integer number = 0;
        String prefix = "";
        while(sqlutils.next()){
            number = sqlutils.getInt("ans")+1;
            prefix = sqlutils.getString("prefix");
            writeLog("ans"+number+"prefix"+prefix+"===============");
        }
        for( Property property : propertys){
                writeLog("ans"+StringUtils.leftPad(number.toString(),3,"0"));
            if("wlz".equals(property.getName())){
                RecordSet in = new RecordSet();
                if(number<100){
                    in.execute("UPDATE formtable_main_166 SET bm = '"+prefix+StringUtils.leftPad(number.toString(),3,"0")+"' where requestid = " +requestid);
                    writeLog("ans"+StringUtils.leftPad(number.toString(),3,"0"));
                }else {
                    in.execute("UPDATE formtable_main_166 SET bm = '"+prefix+number.toString()+"' where requestid = "+requestid);
                    writeLog(prefix+number.toString());
                }
            }
        }
        return "1";
    }
}
