package com.action.material;

import com.action.material.utils.InsertModuleUtil4samtable;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class AddColAction  extends BaseBean implements Action {


    private String checkcol;

    private String checkcolworkflow;
    private String thetable;

    private String updatecolJSON;
    private String insertJSON;
    private String modid;
    private String createid;

    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog("in----");
        RequestManager rm = requestInfo.getRequestManager();
        String table = requestInfo.getRequestManager().getBillTableName();
        String detail = table+"_dt1";
        int billid= requestInfo.getRequestManager().getBillid();
        String sql = "select * from "+detail+" where mainid = "+billid;
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        HashMap<String,String> map = new HashMap<>();
        JSONObject mapjson;
        try{
            mapjson = JSONObject.parseObject(insertJSON);
        }catch (Exception e){
            rm.setMessagecontent("json获取出错");
            return Action.FAILURE_AND_CONTINUE;
        }

        while (recordSet.next()){
            Set<Map.Entry<String, Object>> entries = mapjson.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                String cols1 = entry.getKey();
                map.put(cols1, Util.null2String(recordSet.getString(cols1)));
            }
            String[] checkcols  = checkcol.split(",");
            RecordSet rs = new RecordSet();
            StringBuilder sqlselect = new StringBuilder("SELECT * FROM " + thetable + " where ");
            if(checkcols.length==0){
                rm.setMessagecontent("check配置出错");
                return Action.FAILURE_AND_CONTINUE;
            }
            String[] colworkflow = checkcolworkflow.split(",");
            if(colworkflow.length!=checkcols.length){
                rm.setMessagecontent("check配置出错");
                return Action.FAILURE_AND_CONTINUE;
            }
            writeLog("colworkflow = "+ colworkflow);
            writeLog("checkcols = "+ checkcols);
            StringBuilder where = new StringBuilder(" ");
            for(int i=0;i<checkcols.length;i++){
                if("".equals(Util.null2String(map.get(colworkflow[i])))){
                    rm.setMessagecontent("判断字段为空");
                    return Action.FAILURE_AND_CONTINUE;
                }else{
                    where.append(" ").append(checkcols[i]).append("=").append(map.get(colworkflow[i])).append(" AND");
                }
            }
            writeLog("where = "+ where);
            String whereis = where.substring(0,where.length()-3);
            writeLog("whereis = "+ whereis);
            String slectsql = sqlselect+whereis;
            writeLog("sqlselect = "+ slectsql);
            String[] insert = updatecolJSON.split(",");
            rs.execute(slectsql);
            if(!rs.next()){
                Map<String,String> themap = new HashMap<>();
                Set<Map.Entry<String, Object>> entrySet = mapjson.entrySet();
                for (Map.Entry<String, Object> entry : entrySet) {
                    String cols1 = entry.getKey();
                    String cols2 = entry.getValue().toString();
                    if(!"".equals(Util.null2String(recordSet.getString(cols1)))){
                        themap.put(cols2,Util.null2String(recordSet.getString(cols1)));
                    }
                }
                writeLog(" 插入sql：" + themap);
                InsertModuleUtil4samtable.insert(thetable,themap,Integer.parseInt(modid),Integer.parseInt(createid));
            }else {
                String updatesql = "UPDATE "+thetable+" set "+insert[0]+" = "+insert[0]+"+"+map.get(insert[1])+" WHERE "+whereis;
                writeLog(" 更新sql：" + updatesql);
                RecordSet updaterecordset = new RecordSet();
                updaterecordset.execute(updatesql);
            }
        }

        return Action.SUCCESS;
    }

    public String getCheckcol() {
        return checkcol;
    }

    public void setCheckcol(String checkcol) {
        this.checkcol = checkcol;
    }

    public String getThetable() {
        return thetable;
    }

    public void setThetable(String thetable) {
        this.thetable = thetable;
    }

    public String getUpdatecolJSON() {
        return updatecolJSON;
    }

    public void setUpdatecolJSON(String updatecolJSON) {
        this.updatecolJSON = updatecolJSON;
    }

    public String getInsertJSON() {
        return insertJSON;
    }

    public void setInsertJSON(String insertJSON) {
        this.insertJSON = insertJSON;
    }

    public String getModid() {
        return modid;
    }

    public void setModid(String modid) {
        this.modid = modid;
    }

    public String getCreateid() {
        return createid;
    }

    public void setCreateid(String createid) {
        this.createid = createid;
    }

    public String getCheckcolworkflow() {
        return checkcolworkflow;
    }

    public void setCheckcolworkflow(String checkcolworkflow) {
        this.checkcolworkflow = checkcolworkflow;
    }
}
