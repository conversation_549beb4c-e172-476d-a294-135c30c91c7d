package com.action.material.utils;

import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;

import java.text.SimpleDateFormat;
import java.util.Date;


public class InsertModuleUtil {

    public static void ModuleInsert(String table,String[] insertField,String[] value,int Create,int module){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd+HH:mm:ss");//设置日期格式

        String str1 = df.format(new Date());
        String[] stringdate= str1.split("[+]");
        int len = insertField.length;
        if(insertField.length!=value.length){
            new BaseBean().writeLog("调用方法失败");
            return;
        }

        StringBuilder sql  = new StringBuilder("insert into " + table + " (");
        StringBuilder mid = new StringBuilder("formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) VALUES (");
        StringBuilder instvalue = new StringBuilder();
        StringBuilder needing = new StringBuilder( module+",'1',null,'"+stringdate[0]+"','"+stringdate[1]+"')");//注意数据库格式
        for (int i = 0; i < len; i++) {
            sql.append(insertField[i]).append(",");
            instvalue.append("'");
            instvalue.append(value[i]).append("',");
        }
        RecordSet set = new RecordSet();
        set.execute(sql.append(mid)
                .append(instvalue)
                .append(needing).toString());
        new BaseBean().writeLog(sql.append(mid)
                .append(instvalue)
                .append(needing).toString()+"插入sql");
        RecordSet set1 = new RecordSet();
        String str = "select id from "+table+" where modedatacreatertype is null";
        set1.execute(str);
        int billid = -1;
        while (set1.next()){
            billid = Integer.parseInt(set1.getString("id"));

            //权限重构部分代码
            RecordSet set2 = new RecordSet();
            set2.execute("UPDATE "+table+" SET modedatacreatertype = '0' WHERE id = "+ billid);
            new BaseBean().writeLog(billid+"=="+"UPDATE "+table+" SET modedatacreatertype = '0' WHERE id = "+ billid);
            ModeRightInfo ModeRightInfo = new ModeRightInfo();
            ModeRightInfo.setNewRight(true);
            ModeRightInfo.editModeDataShare(Create,module,billid);

        }

    }



}