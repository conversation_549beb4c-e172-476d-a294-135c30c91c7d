package com.action.material;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class SelectcolAction extends BaseBean implements Action {

    private String JsonObject;

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        JSONObject jsonObject;
        try{
           jsonObject = JSONObject.parseObject(JsonObject);
        }catch (Exception e){
            rm.setMessagecontent("json格式出错");
            return Action.FAILURE_AND_CONTINUE;
        }
        String table = requestInfo.getRequestManager().getBillTableName();
        String detail = table+"_dt1";
        int billid = requestInfo.getRequestManager().getBillid();
        String sql = "select * from "+detail+"where mainid = "+billid;
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        HashMap<String,String> hashMap= new HashMap<>();
        while (recordSet.next()){
            String xght = Util.null2String(recordSet.getString("xght"));
            String id = Util.null2String(recordSet.getString("id"));
            hashMap.put(id,xght);
        }
        for (String key : hashMap.keySet()) {
            RecordSet rs = new RecordSet();
            String sqlis = "select * from uf_httz where id ="+hashMap.get(key);
            rs.execute(sqlis);
            if (recordSet.next()){
                StringBuilder sqls =new StringBuilder(" UPDATE "+detail);
                StringBuilder sets = new StringBuilder();
                StringBuilder where = new StringBuilder( " where id ="+ key );
                Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
                for (Map.Entry<String, Object> entry : entries) {
                    String cols1 = entry.getKey();
                    String cols2 = entry.getValue().toString();
                    if(!"".equals(Util.null2String(recordSet.getString(cols2)))){
                        sets.append(cols1).append('=').append(recordSet.getString(cols2)).append(",");
                    }
                }
                if(sets.length()>0){
                  String colsql =sets.substring(0,sets.length()-1);
                  String sumsql = sqls.append(" set ").append(colsql).append(where).toString();
                  writeLog(" UPDATE SQL IS " +sumsql );
                  recordSet.execute(sumsql);
                }
            }
        }
        return Action.SUCCESS;
    }

    public String getJsonObject() {
        return JsonObject;
    }

    public void setJsonObject(String jsonObject) {
        JsonObject = jsonObject;
    }

}
