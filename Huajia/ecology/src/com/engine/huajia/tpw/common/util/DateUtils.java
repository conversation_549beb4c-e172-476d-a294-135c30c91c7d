package com.engine.huajia.tpw.common.util;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DateUtils {
    private static final Map<String, Integer> CHINESE_MONTH_MAP = new HashMap<>();

    static {
        CHINESE_MONTH_MAP.put("一", 1);
        CHINESE_MONTH_MAP.put("二", 2);
        CHINESE_MONTH_MAP.put("三", 3);
        CHINESE_MONTH_MAP.put("四", 4);
        CHINESE_MONTH_MAP.put("五", 5);
        CHINESE_MONTH_MAP.put("六", 6);
        CHINESE_MONTH_MAP.put("七", 7);
        CHINESE_MONTH_MAP.put("八", 8);
        CHINESE_MONTH_MAP.put("九", 9);
        CHINESE_MONTH_MAP.put("十", 10);
        CHINESE_MONTH_MAP.put("十一", 11);
        CHINESE_MONTH_MAP.put("十二", 12);
    }

    /**
     * 获取月份信息
     *
     * @param year  年份
     * @param month 月份(1-12)
     * @return Map包含daysInMonth(当月天数)和firstDayOfWeek(当月第一天是星期几)
     */
    public static Map<String, Integer> getMonthInfo(int year, int month) {
        Map<String, Integer> result = new HashMap<>();

        // 获取当月天数
        YearMonth yearMonth = YearMonth.of(year, month);
        int daysInMonth = yearMonth.lengthOfMonth();
        result.put("daysInMonth", daysInMonth);

        // 获取当月第一天是星期几
        LocalDate firstDay = LocalDate.of(year, month, 1);
        DayOfWeek dayOfWeek = firstDay.getDayOfWeek();

        // 映射到中文星期顺序: ['六', '日', '一', '二', '三', '四', '五']
        // Java DayOfWeek: 1(周一)到7(周日)
        int firstDayOfWeek;
        switch (dayOfWeek) {
            case SATURDAY:
                firstDayOfWeek = 0;
                break;
            case SUNDAY:
                firstDayOfWeek = 1;
                break;
            case MONDAY:
                firstDayOfWeek = 2;
                break;
            case TUESDAY:
                firstDayOfWeek = 3;
                break;
            case WEDNESDAY:
                firstDayOfWeek = 4;
                break;
            case THURSDAY:
                firstDayOfWeek = 5;
                break;
            case FRIDAY:
                firstDayOfWeek = 6;
                break;
            default:
                firstDayOfWeek = 0;
        }

        result.put("firstDayOfWeek", firstDayOfWeek);
        return result;
    }

    /**
     * 生成月份日期数据
     *
     * @param year  年份
     * @param month 月份(1-12)
     * @return 日期数组，包含42个元素(6周x7天)
     */
    public static List<String> generateDateData(int year, int month) {
        Map<String, Integer> monthInfo = getMonthInfo(year, month);
        int daysInMonth = monthInfo.get("daysInMonth");
        int firstDayOfWeek = monthInfo.get("firstDayOfWeek");

        List<String> dates = new ArrayList<>();
        int currentDate = 1;

        // 6周 x 7天 = 42个单元格
        for (int week = 0; week < 6; week++) {
            for (int day = 0; day < 7; day++) {
                // 如果是第一周且当前day小于第一天的星期几，或者日期已超过月份天数
                if ((week == 0 && day < firstDayOfWeek) || currentDate > daysInMonth) {
                    dates.add("");
                } else {
                    dates.add(String.valueOf(currentDate));
                    currentDate++;
                }
            }
        }

        return dates;
    }

    // 中文月份映射


    /**
     * 将中文月份转换为数字（如 "一月" → 1）
     */
    public static int parseChineseMonth(String chineseMonth) {
        String monthStr = chineseMonth.replace("月", "").trim();
        return CHINESE_MONTH_MAP.getOrDefault(monthStr, 1); // 默认返回1（一月）
    }

    public static void main(String[] args) {
        // 生成4月的日历数据
        List<String> aprilDates = DateUtils.generateDateData(2025, 4); // 4月

        String day = "2025-04-30".substring(8, 10); // "30"
        int dayInt = Integer.parseInt(day);
        int index = aprilDates.indexOf(String.valueOf(dayInt)); // 查找"30"

        System.out.println("Index: " + index); // 会输出正确位置


        List<String> monthDates = DateUtils.generateDateData(2025, DateUtils.parseChineseMonth("一月"));
        index = monthDates.indexOf(String.valueOf(dayInt)); // 查找"30"
        System.out.println("Index: " + index); // 会输出正确位置

    }
}
