package com.engine.interfaces.cy.action;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.engine.hrm.util.face.hrmrestful.service.HrmRestFulFromWebServiceManager;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;
import weaver.workflow.request.RequestManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CreateHrmAction extends BaseBean implements Action {

    private String datacol;

    private String department;

    private String lastname;

    private String startdate;

    private String enddate;

    private String mobile;

    private String certificatenum;

    private String detailnum;

    private String detailcol;

    private String detailvl;

    private String jobtitle;

    private String hrmrepname;

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        ArrayList<HashMap<String, String>> list = getDetailMap(requestInfo, Integer.valueOf(detailnum));
        try {
            for (HashMap<String, String> map : list) {
                if (!detailvl.equals(map.get(detailcol))) {
                    continue;
                }
                List<Map<String, Object>> ans = new ArrayList<>();
                HrmRestFulFromWebServiceManager manager = new HrmRestFulFromWebServiceManager();
                Map<String, Object> hrm = new HashMap<>();
                RecordSet rs = new RecordSet();
                if (!"".equals(map.get(certificatenum))) {
                    String sqlis = "SELECT workcode from HrmResource where certificatenum = '" + map.get(certificatenum) + "'";
                    boolean flag = rs.execute(sqlis);
                    if (flag) {
                        if (rs.next()) {
                            String workcode = rs.getString("workcode");
                            hrm.put("workcode", workcode);
                        } else {
                            hrm.put("workcode", map.get(department) + "_" + map.get(lastname));
                        }
                    } else {
                        rm.setMessagecontent(rs.getExceptionMsg() + sqlis);
                        return FAILURE_AND_CONTINUE;
                    }
                } else {
                    hrm.put("workcode", map.get(department) + "_" + map.get(lastname));
                }
                hrm.put("certificatenum", map.get(certificatenum));
                hrm.put("subcompany", "非本部");
                hrm.put("status", "正式");
                hrm.put("jobtitle", map.get(jobtitle));
                hrm.put("jobactivityid", map.get(jobtitle));
                hrm.put("locationid", "2");
                hrm.put("jobgroupid", "非管理岗位");
                hrm.put("department", map.get(department));
                hrm.put("lastname", map.get(lastname));
                hrm.put("startdate", map.get(startdate));
                hrm.put("enddate", map.get(enddate));
                hrm.put("mobile", map.get(mobile));
                Map<String, Object> cuscol = new HashMap<>();
                cuscol.put("field25", map.get(hrmrepname));
                hrm.put("base_custom_data", cuscol);
//                try {
//                    hrm.put("loginid", getPingYin(map.get(lastname)));
//                } catch (Exception e) {
//                    hrm.put("loginid", "");
//                }
//                hrm.put("password", "1");
                ans.add(hrm);
                writeLog(hrm.toString());
                writeLog(map.toString());
                Map<String, Object> rule = JSONObject.parseObject(datacol, new TypeReference<Map<String, Object>>() {
                });
                manager.parseRule(rule);
                writeLog(ans.toString());
                Map<String, Map<String, String>> result = manager.synHrmresource("workcode", ans);
                writeLog(result.toString());
                for (Map<String, String> valu :
                        result.values()) {
                    if ("-1".equals(valu.get("code"))) {
                        rm.setMessagecontent(valu.get("msg"));
                        return FAILURE_AND_CONTINUE;
                    } else {
                        RecordSet recordSet = new RecordSet();
                        String sql = "update HrmResource set enddate = '" + map.get(enddate) + "',startdate = '" + map.get(startdate) + "' where workcode  = '" + map.get(department) + "_" + map.get(lastname) + "'";
                        boolean flag = recordSet.execute(sql);
                        if (!flag) {
                            rm.setMessagecontent(recordSet.getExceptionMsg() + sql);
                            return FAILURE_AND_CONTINUE;
                        }
                    }
                }
                manager.clearRule();
            }
        } catch (Exception e) {
            rm.setMessagecontent(e.getMessage());
            return FAILURE_AND_CONTINUE;
        }
        return SUCCESS;
    }

    public static String getPingYin(String inputString) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
        String output = "";
        if (inputString != null && inputString.length() > 0
                && !"null".equals(inputString)) {
            char[] input = inputString.trim().toCharArray();
            try {
                for (int i = 0; i < input.length; i++) {
                    if (java.lang.Character.toString(input[i]).matches(
                            "[\\u4E00-\\u9FA5]+")) {
                        String[] temp = PinyinHelper.toHanyuPinyinStringArray(
                                input[i], format);
                        output += temp[0];
                    } else
                        output += java.lang.Character.toString(input[i]);
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                e.printStackTrace();
            }
        } else {
            return "*";
        }
        return output;
    }


    public ArrayList<HashMap<String, String>> getDetailMap(RequestInfo requestInfo, Integer i) {
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        if (detailtable.length <= 0) {
            return null;
        } else if (i <= detailtable.length && i > 0) {
            DetailTable dt = detailtable[i - 1];
            ArrayList<HashMap<String, String>> detaillist = new ArrayList<>();
            Row[] s = dt.getRow();
            for (Row r : s) {
                Cell[] c = r.getCell();
                HashMap<String, String> rowmap = new HashMap<>();
                for (Cell c1 : c) {
                    rowmap.put(c1.getName(), c1.getValue());
                }
                detaillist.add(rowmap);
            }
            return detaillist;
        } else {
            return null;
        }
    }

    public String getDatacol() {
        return datacol;
    }

    public void setDatacol(String datacol) {
        this.datacol = datacol;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getStartdate() {
        return startdate;
    }

    public void setStartdate(String startdate) {
        this.startdate = startdate;
    }

    public String getEnddate() {
        return enddate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCertificatenum() {
        return certificatenum;
    }

    public void setCertificatenum(String certificatenum) {
        this.certificatenum = certificatenum;
    }

    public String getDetailnum() {
        return detailnum;
    }

    public void setDetailnum(String detailnum) {
        this.detailnum = detailnum;
    }

    public String getDetailcol() {
        return detailcol;
    }

    public void setDetailcol(String detailcol) {
        this.detailcol = detailcol;
    }

    public String getDetailvl() {
        return detailvl;
    }

    public void setDetailvl(String detailvl) {
        this.detailvl = detailvl;
    }

    public String getJobtitle() {
        return jobtitle;
    }

    public void setJobtitle(String jobtitle) {
        this.jobtitle = jobtitle;
    }

    public String getHrmrepname() {
        return hrmrepname;
    }

    public void setHrmrepname(String hrmrepname) {
        this.hrmrepname = hrmrepname;
    }
}
