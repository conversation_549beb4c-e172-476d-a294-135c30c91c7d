package com.engine.interfaces.cy.action;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import weaver.workflow.request.RequestManager;

import java.util.*;

public class UpDateInProtect extends BaseBean implements Action {

    private String childrequestidcol;

    private String childtable;
    private String childdetailnum;
    private String detailnum;
    private String json;
    private String sfrb;
    private String sfrbval;

    private String sfrbdetailcol;

    private String sfrbdetailval;

    @Override
    public String execute(RequestInfo requestInfo) {
        String reqeustid = requestInfo.getRequestid();
        RequestManager rm = requestInfo.getRequestManager();
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(json);
        } catch (Exception e) {
            rm.setMessagecontent("JSON转换出错");
            return FAILURE_AND_CONTINUE;
        }
        writeLog("jsonstring" + jsonObject.toJSONString());
        HashMap<String, String> maindata = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for (Property property : propertys) {
            String str = Util.null2String(property.getName());
            String value = Util.null2String(property.getValue());
            maindata.put(str, value);
        }
        writeLog("maindata.get(sfrb)" + maindata.get(sfrb));
        if (sfrbval.equals(maindata.get(sfrb))) {
            return Action.SUCCESS;
        }
        Timer timer = new Timer();

        TimerTask task = new TimerTask() {
            public void run() {
                String sql = "SELECT id FROM " + childtable + " where " + childrequestidcol + " = '" + reqeustid + "'";
                writeLog("sql select " + sql);
                ArrayList<HashMap<String, String>> list = getDetailMap(requestInfo, Integer.parseInt(detailnum));
                RecordSet recordSet = new RecordSet();
                boolean flag = recordSet.execute(sql);
                if (flag) {
                    while (recordSet.next()) {
                        String id = Util.null2String(recordSet.getString("id"));
                        writeLog("id" + id);
                        for (HashMap<String, String> detail : list) {
                            List<String> keys = new ArrayList<>();
                            List<String> values = new ArrayList<>();
                            String detailsfrb = Util.null2String(detail.get(sfrbdetailcol));
                            if (!sfrbdetailval.equals(detailsfrb)) {
                                continue;
                            }
                            Set<Map.Entry<String, Object>> sets = jsonObject.entrySet();
                            if (!"".equals(id)) {
                                keys.add("mainid");
                                values.add("'" + id + "'");
                            } else {
                                writeLog("id查找是空" + sql);
                                return;
                            }
                            for (Map.Entry<String, Object> set : sets) {
                                String key = Util.null2String(set.getValue());
                                String value = Util.null2String(detail.get(set.getKey()));
                                if (!"".equals(value)) {
                                    keys.add(key);
                                    values.add("'" + value + "'");
                                }
                            }
                            RecordSet update = new RecordSet();
                            String detailetable = childtable + "_dt" + childdetailnum;
                            StringBuilder insertsql = new StringBuilder("INSERT INTO " + detailetable + " ");
                            insertsql.append("(").append(String.join(",", keys)).append(") ");
                            insertsql.append("VALUES(").append(String.join(",", values)).append(") ");
                            writeLog(insertsql.toString());
                            boolean ans = update.execute(insertsql.toString());
                            if (!ans) {
                                writeLog("插入数据出错" + insertsql.toString());
                            }

                        }
                        timer.cancel();
                    }
                } else {
                    writeLog(("未找到流程" + sql));
                }
            }
        };
        timer.schedule(task, 1000, 1000);
        TimerTask taskEnd = new TimerTask() {
            public void run() {
                timer.cancel();
            }
        };
        timer.schedule(taskEnd, 5000);
        return Action.SUCCESS;
    }

    public ArrayList<HashMap<String, String>> getDetailMap(RequestInfo requestInfo, Integer i) {
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        if (detailtable.length <= 0) {
            return null;
        } else if (i <= detailtable.length && i > 0) {
            DetailTable dt = detailtable[i - 1];
            ArrayList<HashMap<String, String>> detaillist = new ArrayList<>();
            Row[] s = dt.getRow();
            for (Row r : s) {
                Cell[] c = r.getCell();
                HashMap<String, String> rowmap = new HashMap<>();
                for (Cell c1 : c) {
                    rowmap.put(c1.getName(), c1.getValue());
                }
                detaillist.add(rowmap);
            }
            return detaillist;
        } else {
            return null;
        }
    }

    public String getChildrequestidcol() {
        return childrequestidcol;
    }

    public void setChildrequestidcol(String childrequestidcol) {
        this.childrequestidcol = childrequestidcol;
    }

    public String getChildtable() {
        return childtable;
    }

    public void setChildtable(String childtable) {
        this.childtable = childtable;
    }

    public String getChilddetailnum() {
        return childdetailnum;
    }

    public void setChilddetailnum(String childdetailnum) {
        this.childdetailnum = childdetailnum;
    }

    public String getDetailnum() {
        return detailnum;
    }

    public void setDetailnum(String detailnum) {
        this.detailnum = detailnum;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getSfrb() {
        return sfrb;
    }

    public void setSfrb(String sfrb) {
        this.sfrb = sfrb;
    }

    public String getSfrbval() {
        return sfrbval;
    }

    public void setSfrbval(String sfrbval) {
        this.sfrbval = sfrbval;
    }

    public String getSfrbdetailcol() {
        return sfrbdetailcol;
    }

    public void setSfrbdetailcol(String sfrbdetailcol) {
        this.sfrbdetailcol = sfrbdetailcol;
    }

    public String getSfrbdetailval() {
        return sfrbdetailval;
    }

    public void setSfrbdetailval(String sfrbdetailval) {
        this.sfrbdetailval = sfrbdetailval;
    }
}
