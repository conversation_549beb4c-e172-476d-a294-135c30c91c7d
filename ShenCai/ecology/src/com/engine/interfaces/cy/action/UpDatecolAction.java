package com.engine.interfaces.cy.action;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;


public class UpDatecolAction extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        String thisreqeustid = requestInfo.getRequestid();
        int id = rm.getBillid();
        RecordSet recordSet = new RecordSet();
        String sql = "select taba.id detiailid,taba.htxz, tabb.requestId from formtable_main_137_dt1 taba left join formtable_main_138 tabb on taba.xm = tabb.zsryxm where taba.mainid = '"+id+"'";
        boolean ans =  recordSet.execute(sql);
        if(ans){
            while (recordSet.next()){
                String requestid = Util.null2String(recordSet.getString("requestId"));
                String detiailid = Util.null2String(recordSet.getString("detiailid"));
                String htxz = Util.null2String(recordSet.getString("htxz"));
                if(!"".equals(requestid)&&!"".equals(htxz)){
                    RecordSet update = new RecordSet();
                    String updatesql = " update formtable_main_138 set htxz = '"+htxz+"' , dyrzlc = '"+thisreqeustid+"' where requestId = '"+requestid+"'";
                    String updatethis = " update formtable_main_137_dt1 set sfzsyc = '0' where id = '"+detiailid+"'";
                    boolean flag =  update.execute(updatesql);
                    if(!flag){
                            rm.setMessagecontent("执行sql出错："+updatesql+"--"+update.getExceptionMsg());
                            return FAILURE_AND_CONTINUE;
                    }
                    flag = update.execute(updatethis);
                    if(!flag){
                        rm.setMessagecontent("执行sql出错："+updatethis+"--"+update.getExceptionMsg());
                        return FAILURE_AND_CONTINUE;
                    }
                }else {
                    if("".equals(htxz)){
                        rm.setMessagecontent("htxz:"+htxz+"为空");
                        return FAILURE_AND_CONTINUE;
                    }
                }
            }
        }else {
            rm.setMessagecontent("执行sql出错："+sql+recordSet.getExceptionMsg());
            return FAILURE_AND_CONTINUE;
        }
        return SUCCESS;
    }

}
