package com.engine.interfaces.cy.action;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.*;

public class UpdateModelDetail extends BaseBean implements Action {
    
    public String table; //明细表

    public String mainidcol;//浏览按钮主键
    public String maintable;//建模主表
    public String tablenexusid;//流程对应建模字段
    public String coljson;//对应json
    
    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        JSONObject jsonObject;
        try{
            jsonObject = JSONObject.parseObject(coljson);
        }catch (Exception e){
            rm.setMessagecontent("JSON转换出错");
            return FAILURE_AND_CONTINUE;
        }
        HashMap<String,String> maindata = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            maindata.put(str, value);
        }
        StringBuilder sql =new StringBuilder("INSERT INTO "+table+" ");
        List<String> keys = new ArrayList<>();
        List<String> values = new ArrayList<>();
        if("".equals(mainidcol)){
            rm.setMessagecontent("mainidcol字段为空");
            return FAILURE_AND_CONTINUE;
        }
        String mainid = Util.null2String(maindata.get(tablenexusid));
        if("".equals(mainid)){
            rm.setMessagecontent("流程表连接字段为空");
            return FAILURE_AND_CONTINUE;
        }
        if("id".equalsIgnoreCase(mainidcol)){
            keys.add("mainid");
            values.add("'"+mainid+"'");
        }else {
            String selmainid = "SELECT * FROM "+maintable+" WHERE "+mainidcol+" ='"+mainid+"'";
            RecordSet recordSet = new RecordSet();
            boolean flag = recordSet.execute(selmainid);
            if(flag){
                if(recordSet.next()){
                    String id = Util.null2String(recordSet.getString("id"));
                    if(!"".equals(id)){
                        keys.add("mainid");
                        values.add("'"+id+"'");
                    }else{
                        rm.setMessagecontent("id查找是空"+selmainid);
                        return FAILURE_AND_CONTINUE;
                    }
                }else{
                    rm.setMessagecontent("sql没找到id"+selmainid);
                    return FAILURE_AND_CONTINUE;
                }
            }else{
                rm.setMessagecontent("查找主键出错"+selmainid);
                return FAILURE_AND_CONTINUE;
            }
        }
        Set<Map.Entry<String, Object>> sets = jsonObject.entrySet();
        for(Map.Entry<String, Object> set : sets){
            String key = Util.null2String(set.getValue());
            String value = Util.null2String(maindata.get(set.getKey()));
            if(!"".equals(value)){
                keys.add(key);
                values.add("'"+value+"'");
            }
        }
        sql.append("(").append(String.join(",",keys)).append(") ");
        sql.append("VALUES(").append(String.join(",",values)).append(") ");
        RecordSet recordSet = new RecordSet();
        boolean flag = recordSet.execute(sql.toString());
        if(flag){
            return SUCCESS;
        }else{
            rm.setMessagecontent("sql执行出错"+sql+"-"+recordSet.getExceptionMsg());
            return FAILURE_AND_CONTINUE;
        }
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getMainidcol() {
        return mainidcol;
    }

    public void setMainidcol(String mainidcol) {
        this.mainidcol = mainidcol;
    }

    public String getMaintable() {
        return maintable;
    }

    public void setMaintable(String maintable) {
        this.maintable = maintable;
    }

    public String getTablenexusid() {
        return tablenexusid;
    }

    public void setTablenexusid(String tablenexusid) {
        this.tablenexusid = tablenexusid;
    }

    public String getColjson() {
        return coljson;
    }

    public void setColjson(String coljson) {
        this.coljson = coljson;
    }
}
