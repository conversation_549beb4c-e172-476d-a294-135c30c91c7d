package com.engine.interfaces.cy.baiduhrm.cmd;

import okhttp3.*;
import org.json.JSONException;
import org.json.JSONObject;
import weaver.general.BaseBean;

import java.io.IOException;

class BaiduPhotoUtil {
//    public static final String API_KEY = "b082aaTP12nFBXh9yA41Kv09";
//    public static final String SECRET_KEY = "qMIaMEGFVwZ3Ocsw3okGLV2eWUL7K0fa";

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();


    static String getData(String base64) throws IOException, JSONException {
        new BaseBean().writeLog("in-----------");
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "id_card_side=front&image=" + base64);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return response.body().string();
    }


    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    static String getAccessToken() throws IOException, JSONException {
        String API_KEY = new BaseBean().getPropValue("BaiduPhoto", "API_KEY");
        String SECRET_KEY = new BaseBean().getPropValue("BaiduPhoto", "SECRET_KEY");
        new BaseBean().writeLog("API_KEY" + API_KEY);
        new BaseBean().writeLog("SECRET_KEY" + API_KEY);
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + API_KEY
                + "&client_secret=" + SECRET_KEY);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return new JSONObject(response.body().string()).getString("access_token");
    }

}