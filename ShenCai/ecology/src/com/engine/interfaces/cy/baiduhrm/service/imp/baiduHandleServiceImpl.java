package com.engine.interfaces.cy.baiduhrm.service.imp;

import com.engine.core.impl.Service;
import com.engine.interfaces.cy.baiduhrm.cmd.DocidgetHrmCmd;
import com.engine.interfaces.cy.baiduhrm.service.baiduHandleService;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName SqlHandleServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/20
 */
public class baiduHandleServiceImpl extends Service implements baiduHandleService {


    @Override
    public Map<String, Object> docidGethrm(Map<String, Object> params, User user) {
        return commandExecutor.execute(new DocidgetHrmCmd(params, user));
    }
}
