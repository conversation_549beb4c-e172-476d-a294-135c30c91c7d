package com.engine.interfaces.cy.baiduhrm.cmd;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.Base64;

public class ImageConverter {

    public static String convertImageToBase64(String filename) throws IOException {
        File imageFile = new File(filename);
        long fileSize = imageFile.length();

        if (fileSize > 4 * 1024 * 1024) {
            // 图片超过4MB，进行压缩处理
            BufferedImage image = ImageIO.read(imageFile);
            int newWidth = (int) (image.getWidth() * 0.5);  // 压缩为原来宽度的一半
            int newHeight = (int) (image.getHeight() * 0.5);  // 压缩为原来高度的一半

            Image resizedImage = image.getScaledInstance(newWidth, newHeight, Image.SCALE_SMOOTH);
            BufferedImage bufferedResizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            bufferedResizedImage.getGraphics().drawImage(resizedImage, 0, 0, null);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedResizedImage, "jpg", baos);
            baos.flush();
            byte[] compressedImageBytes = baos.toByteArray();
            baos.close();

            String base64Image = Base64.getEncoder().encodeToString(compressedImageBytes);
            return "data:image/jpeg;base64," + URLEncoder.encode(base64Image, "UTF-8");
        } else {
            // 图片不需要压缩
            byte[] imageBytes = Files.readAllBytes(imageFile.toPath());
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            return "data:image/jpeg;base64," + URLEncoder.encode(base64Image, "UTF-8");
        }
    }
}