package com.engine.interfaces.cy.baiduhrm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import org.json.JSONException;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.toolbox.doc.DownloadManager;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 * @FileName SqlExecuteCmd
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/20
 */
public class DocidgetHrmCmd extends AbstractCommonCommand<Map<String, Object>> {

    public DocidgetHrmCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    private final BaseBean bb;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog("instart =========== ");
        Map<String, Object> result = new HashMap<>();
        String docid = Util.null2String(params.get("docid"));
        if ("".equals(docid)) {
            result.put("status", "0");
            result.put("msg", "错误信息：参数docid为空");
            return result;
        }
        try {
            String filename = DownloadManager.getFileByDocId(Integer.parseInt(docid));
            bb.writeLog("文件名为=======" + filename);
            String basestring = ImageConverter.convertImageToBase64(filename);
            bb.writeLog("base加密url加密=======" + basestring);
            String data = BaiduPhotoUtil.getData(basestring);
            bb.writeLog("返回数据=======" + data);
            result.put("status", "1");
            result.put("data", data);
        } catch (IOException | JSONException e) {
            result.put("status", "0");
            result.put("msg", "错误信息：" + e);
        }
        return result;
    }
}