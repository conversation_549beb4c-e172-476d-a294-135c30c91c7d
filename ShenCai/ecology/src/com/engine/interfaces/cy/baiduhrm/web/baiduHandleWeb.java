package com.engine.interfaces.cy.baiduhrm.web;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.interfaces.cy.baiduhrm.service.baiduHandleService;
import com.engine.interfaces.cy.baiduhrm.service.imp.baiduHandleServiceImpl;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName SqlHandleWeb
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/20
 */
public class baiduHandleWeb {

    private baiduHandleService getService(User user) {
        return ServiceUtil.getService(baiduHandleServiceImpl.class, user);
    }

    @POST
    @Path("/getHrmdata")
    @Produces(MediaType.TEXT_PLAIN)
    public String getHrmdata(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "saveOrUpdate---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = new User(1);
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).docidGethrm(params, user);
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "saveOrUpdate，result为：" + result);
        return JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue);
    }


    @POST
    @Path("/checkHas")
    @Produces(MediaType.TEXT_PLAIN)
    public String checkHas(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "saveOrUpdate---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = new User(1);
            Map<String, Object> params = ParamUtil.request2Map(request);
            RecordSet recordSet = new RecordSet();
            String idcard = Util.null2String(params.get("idcard"));
            String sql = " SELECT * FROM uf_rck where sfzh = '" + idcard + "'";
            boolean flag = recordSet.execute(sql);
            if (flag) {
                if (recordSet.next()) {
                    result.put("status", "0");
                    result.put("errMsg", "已存在当前人");
                } else {
                    result.put("status", "1");
                    result.put("errMsg", "成功");
                }
            } else {
                result.put("status", "0");
                result.put("errMsg", "接口异常： " + recordSet.getExceptionMsg() + " sql " + sql);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "saveOrUpdate，result为：" + result);
        return JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue);
    }
}
