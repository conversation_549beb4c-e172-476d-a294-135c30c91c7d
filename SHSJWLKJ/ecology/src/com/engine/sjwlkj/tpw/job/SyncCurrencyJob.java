package com.engine.sjwlkj.tpw.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.general.BaseBean;

import java.util.*;


/**
    * @FileName CurrencySyncJob
    * @Description 思建--主数据同步(货币)
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/3/28
    */
@Getter
@Setter
public class SyncCurrencyJob extends BaseCronJob {

    private BaseBean bb = new BaseBean();
    /**
     * 主数据同步(货币)事件名
     */
    private String eventName;
    @Override
    public void execute() {
        try{
            bb.writeLog("SyncCurrencyJob----START");
            //事件参数
            String esbParam = "";
            JSONObject jsonObject = new JSONObject();
            JSONObject body = new JSONObject();
            jsonObject.put("body",body);
            if(StringUtils.isNotBlank(eventName)){
                EsbEventResult esbEventResult = EsbUtil.callEsbEvent(eventName, JSONObject.toJSONString(jsonObject));
                if(esbEventResult.isSuccess()){
                    JSONObject data = esbEventResult.getData();
                    bb.writeLog("响应信息"+data.toJSONString());
                }
            }else {
                bb.writeLog("主数据同步(货币)Job 参数未填写");
            }
        }catch(Exception e){
            bb.writeLog("主数据同步(货币)方法报错："+SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("SyncCurrencyJob----end");
    }
}
