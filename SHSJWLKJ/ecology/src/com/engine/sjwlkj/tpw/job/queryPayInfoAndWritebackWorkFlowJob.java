package com.engine.sjwlkj.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Getter
@Setter
public class queryPayInfoAndWritebackWorkFlowJob extends BaseCronJob {

    private BaseBean bb = new BaseBean();

    /**
     * 查询空投单支付信息事件名
     */
    private String eventName;

    private String batch;

    private String bzAirdropTableName;
    private ArrayList<String> kolBrokers ;
    private ArrayList<String> others ;

    private RecordSet recordSet ;
    private HashMap<String, String> bzMap ;


    @Override
    public void execute() {
        try {
            bb.writeLog("queryPayInfoAndWritebackWorkFlowJob----START");
            if (StringUtils.isNotBlank(eventName) && StringUtils.isNotBlank(batch) && StringUtils.isNotBlank(bzAirdropTableName)) {
                resetParam();
                init();
                JSONObject jsonObject = new JSONObject();
                ArrayList<String> workflowNos = new ArrayList<>();
                //搜集请求数据 流程单号列表

                recordSet.executeQuery("select * from " + bzAirdropTableName);
                while (recordSet.next()) {
                    bzMap.put(recordSet.getString("currency"), recordSet.getString("id"));
                }

                recordSet.executeQuery("select lcbh from uf_fybg where fkzt <> '1' or fkzt is null");
                while (recordSet.next()) {
                    workflowNos.add(recordSet.getString("lcbh"));
                }
                int batchSize = Integer.parseInt(batch);
                int totalSize = workflowNos.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i + batchSize, totalSize);
                    List<String> workflowNo = workflowNos.subList(i, endIndex);
                    jsonObject.put("workflowNos", workflowNo);
                    EsbEventResult esbEventResult = EsbUtil.callEsbEvent(eventName, JSONObject.toJSONString(jsonObject));
                    bb.writeLog("esbEventResult===>" + JSONObject.toJSONString(esbEventResult));
                    if (esbEventResult.isSuccess()) {
                        JSONObject data = esbEventResult.getData();
                        JSONArray resData = data.getJSONArray("data");
                        bb.writeLog("esbEventResult==data=>" + JSONObject.toJSONString(resData));
                        if (!resData.isEmpty()) {
                            disposeData(resData);
                        }
                        bb.writeLog("响应信息" + data.toJSONString());
                    }
                }
            } else {
                bb.writeLog("查询空投单支付信息并回写数据到空投流程，参数未填写");
            }
        } catch (Exception e) {
            bb.writeLog("查询空投单支付信息并回写数据到空投流程报错：" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog("queryPayInfoAndWritebackWorkFlowJob----end");
    }

    private void init() {
        recordSet.executeQuery("select lcbh from formtable_main_143");
        while (recordSet.next()) {
            kolBrokers.add(recordSet.getString("lcbh"));
        }
        recordSet.executeQuery("select lcbh from formtable_main_136");
        while (recordSet.next()) {
            others.add(recordSet.getString("lcbh"));
        }
    }

    private void resetParam() {
        recordSet = DBUtil.getThreadLocalRecordSet();
        bzMap = new HashMap<>();
        kolBrokers = new ArrayList<>();
        others = new ArrayList<>();
    }
    private void disposeData(JSONArray resDatas) {
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        CountDownLatch latch = new CountDownLatch(resDatas.size());
        for (int i = 0; i < resDatas.size(); i++) {
            JSONObject resData = (JSONObject) resDatas.get(i);
            //开启多线程
            executorService.submit(() -> {
                try {
                    //处理每一组数据
                    process(resData);
                } finally {
                    latch.countDown(); // 线程完成任务后减少计数器
                }
            });
        }
        try {
            // 等待所有线程完成任务，超时30分钟
            if (!latch.await(30, TimeUnit.MINUTES)) {
                bb.writeLog("超时未执行完所有线程任务，请检查!");
            }
        } catch (InterruptedException e) {
            bb.writeLog("latch.await() 出错：" + SDUtil.getExceptionDetail(e));
        } finally {
            executorService.shutdown();
        }
    }

    private void process(JSONObject resData) {
        //流程单号
        String workflowNo = Util.null2String(resData.getString("workflowNo"));
        //支付币种
        String currency = "";
        if (bzMap.containsKey(Util.null2String(resData.getString("currency")))) {
            currency = bzMap.get(Util.null2String(resData.getString("currency")));
        }
        //支付金额
        String paidAmount = Util.null2String(resData.getString("paidAmount"));
        // 头都 支付时间  时间戳住转换为日期
        Date date = new Date(Long.parseLong(Util.null2String(resData.getString("paymentDate"))));
        String paymentDate = formatDate(date);
        //支付状态
        String status = Util.null2String(resData.getString("status"));
        //执行更新操作
        String sql = "";
        if (StringUtils.isNotBlank(currency)) {
            sql = "update uf_fybg set fkzt='" + status + "' ,fkrq='" + paymentDate + "' ,fkje='" + paidAmount + "' ,fkbz=" + currency + " where lcbh='" + workflowNo + "'";
        } else {
            sql = "update uf_fybg set fkzt='" + status + "' ,fkrq='" + paymentDate + "' ,fkje='" + paidAmount + "'  where lcbh='" + workflowNo + "'";
        }
        bb.writeLog("更新sql语句==>" + sql);
        if (!recordSet.executeUpdate(sql)) {
            bb.writeLog("建模数据更新异常==>相关sql" +sql+"  异常信息："+ recordSet.getExceptionMsg());
        }
        //回写流程
        String formTable = "";
        //通过workflowNo找到所属流程的流程表
        if(kolBrokers.contains(workflowNo)){
            formTable = "formtable_main_143";
        }else if(others.contains(workflowNo)){
            formTable = "formtable_main_136";
        }
        if(StringUtils.isNotBlank(formTable)){
            String workflowSql = "update "+formTable+" set fkzt='" + status + "' where lcbh='" + workflowNo + "'";
            if(!recordSet.executeUpdate(workflowSql)){
                bb.writeLog("流程数据更新异常==>" + "  异常信息："+recordSet.getExceptionMsg());
            }
        }else {
            bb.writeLog("流程编号为："+workflowNo+"，在流程中未匹配到数据");
        }
    }
    public static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }
}
