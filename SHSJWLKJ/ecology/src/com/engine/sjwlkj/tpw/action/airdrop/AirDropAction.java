package com.engine.sjwlkj.tpw.action.airdrop;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.caucho.json.Json;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.weaver.general.Util;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import com.engine.parent.module.util.ModuleDataUtil;


/**
 * @FileName AirDropAction
 * @Description 空投/KOLBroker流程提交校验
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/3/28
 */
@Getter
@Setter
public class AirDropAction implements Action {
    /**
     * 退回状态字段名
     */
    private String thzt;
    /**
     * AirDrop创建事件名
     */
    private String createEventName;
    /**
     * AirDrop编辑事件名
     */
    private String editEventName;

    private BaseBean bb = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        bb.writeLog("AirDropAction----START");
        String errorMsg = "";//出错信息
        try {
            //退回状态
            RecordSet rs = new RecordSet();
            if (StringUtils.isNotBlank(thzt) && StringUtils.isNotBlank(createEventName) && StringUtils.isNotBlank(editEventName)) {
                ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
                bb.writeLog("空投/KOLBroker流程actionInfo：" + JSONObject.toJSONString(actionInfo));
                Map<String, String> mainData = actionInfo.getMainData();
                if (mainData != null) {
                    bb.writeLog("主表字段thzt数据：" + mainData.get(thzt));
                    EsbEventResult esbEventResult;
                    JSONObject result = assParams(actionInfo);
                    String errMsg = (String) result.get("errMsg");
                    Object airdropEvent = result.get("airdropEvent");
                    if (StringUtils.isBlank(errMsg)) {
                        //得到主表中的用来存储流程退回状态的值
                        if ("0".equals(mainData.get(thzt))) {
                            //触发创建空投接口
                            bb.writeLog("触发创建空投接口");
                            esbEventResult = EsbUtil.callEsbEvent(createEventName, JSONObject.toJSONString(airdropEvent));
                        } else {
                            //触发编辑空投接口
                            bb.writeLog("触发编辑空投接口");
                            esbEventResult = EsbUtil.callEsbEvent(editEventName, JSONObject.toJSONString(airdropEvent));
                        }
                        if (esbEventResult.isSuccess()) {
                            JSONObject resMsg = esbEventResult.getData();
                            bb.writeLog("调用空投ESB事件后的响应数据"+JSONObject.toJSONString(resMsg));
                            if (resMsg != null) {
                                String dataStr = resMsg.getString("data");
                                if (StringUtils.isBlank(dataStr)) {
                                    bb.writeLog("调用空投ESB事件后的响应更新数据为空");
                                }else {
                                    JSONObject data = JSONObject.parseObject(dataStr);
                                    //请求id
                                    String requestId = actionInfo.getRequestId();
                                    //表单名称
                                    String formtableName = actionInfo.getFormtableName();
                                    Float totalIssuedAmount = data.getFloat("totalIssuedAmount");
                                    bb.writeLog("调用空投ESB事件后的响应totalIssuedAmount值："+totalIssuedAmount);
                                    Float actualTotalIssuedAmount = data.getFloat("actualTotalIssuedAmount");
                                    bb.writeLog("调用空投ESB事件后的响应actualTotalIssuedAmount值"+actualTotalIssuedAmount);
                                    JSONArray summaryArray = data.getJSONArray("summary");
                                    bb.writeLog("调用空投ESB事件后的响应summary值"+JSONObject.toJSONString(summaryArray));
                                    String updateSql = "update " + formtableName + " set zffsl = " + totalIssuedAmount + " , sjffsl =  " + actualTotalIssuedAmount + " where requestId = " + requestId;
                                    rs.execute(updateSql);
                                    //查询当前流程的主表id
                                    String selectSql = "select id from " + formtableName + " where requestId = " + requestId;
                                    rs.execute(selectSql);
                                    if (rs.next()) {
                                        int id = rs.getInt("id");
                                        String detailTable = formtableName + "_dt3";
                                        if (summaryArray != null && !summaryArray.isEmpty()) {
                                            if ("0".equals(mainData.get(thzt))) {
                                                //插入操作
                                                StringBuilder sqlBuilder = new StringBuilder();
                                                sqlBuilder.append("INSERT INTO " + detailTable + " (mainid,ffbz, hl,ffbzs) VALUES ");
                                                for (int i = 0; i < summaryArray.size(); i++) {
                                                    JSONObject summary = (JSONObject) summaryArray.get(i);
                                                    String issuedToken = (String) summary.get("issuedToken");
                                                    String exchangeRate = (String) summary.get("exchangeRate");
                                                    Float issuedAmount = summary.getFloat("issuedAmount");
                                                    sqlBuilder.append("(").append(id).append(", ").append("'").append(issuedToken).append("'").append(", '").append(exchangeRate).append("'").append(", ").append(issuedAmount).append("),");
                                                }
                                                sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
                                                String sql = sqlBuilder.toString();
                                                bb.writeLog("要插入的sql语句"+sql);
                                                rs.executeUpdate(sql);
                                            } else {
//                                                for (int i = 0; i < summaryArray.size(); i++) {
//                                                    JSONObject summary = (JSONObject) summaryArray.get(i);
//                                                    String issuedToken = (String) summary.get("issuedToken");
//                                                    String exchangeRate = (String) summary.get("exchangeRate");
//                                                    Float issuedAmount = summary.getFloat("issuedAmount");
//                                                    String updatedetailSql = "update " + detailTable + " set hl =  " + exchangeRate + " , ffbzs =  " + issuedAmount + " where mainid = " + id+" and ffbz = "+issuedToken;
//                                                    rs.execute(updatedetailSql);
//                                                }
                                                //先删除
                                                String deleteSql = "delete from "+detailTable+" WHERE mainid = "+id;
                                                rs.execute(deleteSql);
                                                bb.writeLog("删除的sql语句"+deleteSql);
                                                //插入操作
                                                StringBuilder sqlBuilder = new StringBuilder();
                                                sqlBuilder.append("INSERT INTO " + detailTable + " (mainid,ffbz, hl,ffbzs) VALUES ");
                                                for (int i = 0; i < summaryArray.size(); i++) {
                                                    JSONObject summary = (JSONObject) summaryArray.get(i);
                                                    String issuedToken = (String) summary.get("issuedToken");
                                                    String exchangeRate = (String) summary.get("exchangeRate");
                                                    Float issuedAmount = summary.getFloat("issuedAmount");
                                                    sqlBuilder.append("(").append(id).append(", ").append("'").append(issuedToken).append("'").append(", '").append(exchangeRate).append("'").append(", ").append(issuedAmount).append("),");
                                                }
                                                sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
                                                String sql = sqlBuilder.toString();
                                                bb.writeLog("删除后要插入的sql语句"+sql);
                                                rs.executeUpdate(sql);
                                            }
                                        }
                                    }
                                }

                            } else {
                                errorMsg = "回写到流程表单的数据为空";
                            }
                        } else {
                            errorMsg = esbEventResult.getErroMsg();
                        }
                    } else {
                        errorMsg = errMsg;
                    }
                } else {
                    errorMsg = "主表数据不存在";
                }
            } else {
                errorMsg = "退回状态字段名|AirDrop创建事件名|AirDrop编辑事件名";
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("空投/KOLBroker流程提交校验方法报错：" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("空投/KOLBroker流程提交校验信息：" + errorMsg);
        bb.writeLog("AirDropAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    /**
     * 进行esbParam参数的组装
     */
    private JSONObject assParams(ActionInfo actionInfo) {
        JSONObject result = new JSONObject();
        String errMsg = "";
        AirdropEvent airdropEvent = new AirdropEvent();
        JSONArray airdropList = new JSONArray();
        JSONArray eventNameLang = new JSONArray();
        Map<String, String> mainData = actionInfo.getMainData();
        Map<Integer, List<Map<String, String>>> detailData = actionInfo.getDetailData();
        String eventName = "";
        if (detailData != null && detailData.size() >= 2) {
            Map<String, String> detail2map = null;
            ArrayList<Integer> numList = new ArrayList<>();
            List<Map<String, String>> detail1 = detailData.get(1);
            List<Map<String, String>> detail2 = detailData.get(2);
            if (!detail1.isEmpty()) {
                int count = 1;
                for (Map<String, String> map : detail1) {
                    JSONObject airdrop = new JSONObject();
                    airdrop.put("rowIdx",count);
                    airdrop.put("userId", map.get("uid1"));
                    airdrop.put("amount", map.get("sl"));
                    airdrop.put("valuedToken", map.get("dj"));
                    airdrop.put("issuedToken",  map.get("ffbz"));
                    airdropList.add(airdrop);
                    count++;
                }
            } else {
                errMsg = "明细表1数据异常!";
            }
            if (!detail2.isEmpty()) {
                for (int i = 0; i < detail2.size(); i++) {
                    detail2map = detail2.get(i);
                    JSONObject jo = new JSONObject();
                    //语言
                    String yy = detail2map.get("yy");
                    //活动名称
                    String sjmc = detail2map.get("sjmc");
                    //明细号
                    if ("en".equals(yy)) {
                        int id = Integer.parseInt(detail2map.get("id"));
                        numList.add(id);
                    }
                    jo.put("lang", yy);
                    jo.put("message", sjmc);
                    eventNameLang.add(jo);
                }
                if (!numList.isEmpty()) {
                    int min = Collections.min(numList);
                    for (Map<String, String> map : detail2) {
                        Integer id = Integer.valueOf(map.get("id"));
                        if (id == min) {
                            eventName = map.get("sjmc");
                        }
                    }

                }
            } else {
                errMsg = "明细表2数据异常!";
            }
            airdropEvent.setAirdropList(airdropList);
            airdropEvent.setFundingAccount(Util.null2String(mainData.get("zjzh")));
            airdropEvent.setValuedToken(Util.null2String(mainData.get("jjbz")));
            airdropEvent.setIssuedToken(Util.null2String(mainData.get("ffbz")));
            airdropEvent.setTotalAmount(Util.null2String(mainData.get("tokengshjffjz")));
            airdropEvent.setWorkflowNo(Util.null2String(mainData.get("lcbh")));
            airdropEvent.setExpenseItemNo(Util.null2String(mainData.get("fylx")));
            airdropEvent.setEventNameLang(eventNameLang.toJSONString());
            airdropEvent.setEventLink(Util.null2String(mainData.get("sjlj")));
            airdropEvent.setRemark(Util.null2String(mainData.get("bz")));
            airdropEvent.setApplicant(Util.null2String(mainData.get("yx")));
            airdropEvent.setEventName(eventName);
            airdropEvent.setRequestId(actionInfo.getRequestId());
            airdropEvent.setExpenseTypeNo(Util.null2String(mainData.get("fydl")));
        } else {
            errMsg = "明细表数据异常!";
        }
        result.put("airdropEvent", airdropEvent);
        result.put("errMsg", errMsg);
        bb.writeLog("esbParam参数：" + JSONObject.toJSONString(result));
        return result;
    }
}


