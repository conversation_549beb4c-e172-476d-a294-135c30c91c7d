package com.engine.sjwlkj.tpw.action.airdrop;

import lombok.Data;

@Data
public class ApprovalDto {
    /**
     * 若审批通过赋值为1
     * 流程退回赋值为2
     */
    private int approve;
    /**
     * 财务单号
     */
    private String workflowNo;
    /**
     * 审批人邮箱
     */
    private String approver;
    /**
     * 签字意见
     */
    private String comment;
    /**
     * 批准流程赋值为false，流程归档赋值为true
     */
    private boolean cheekCompleted;
    /**
     * requestId
     */
    private String requestId;
    /**
     * userId
     */
    private int userId;
}
