package com.engine.sjwlkj.tpw.action.airdrop;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.weaver.general.Util;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import javax.xml.bind.DatatypeConverter;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Map;

/**
    * @FileName ApprovalStatusUpdateAction
    * @Description 审批状态更新接口
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/3/29
    */

@Getter
@Setter
public class ApprovalStatusUpdateAction implements Action {
    private BaseBean bb = new BaseBean();
    /*
    * 审批状态更新接口事件名
    * */
    private String eventName;
    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = "";//出错信息
        try {
            bb.writeLog("ApprovalStatusUpdateAction----START");
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            bb.writeLog("审批状态更新接口actionInfo：" + JSONObject.toJSONString(actionInfo));
            Map<String, String> mainData = actionInfo.getMainData();
            if((!mainData.isEmpty()) && StringUtils.isNotBlank(eventName)){
                //邮箱
                String yx = mainData.get("yx");
                ApprovalDto approvalDto = new ApprovalDto();
                approvalDto.setWorkflowNo(Util.null2String(mainData.get("lcbh")));
                approvalDto.setApprover(yx);
                approvalDto.setComment(actionInfo.getRemark());
                User user = actionInfo.getUser();
                if(user != null){
                    approvalDto.setUserId(user.getUID());
                }
                approvalDto.setRequestId(actionInfo.getRequestId());
                if(1==actionInfo.getSubmitType()){
                    approvalDto.setApprove(1);
                }else{
                    approvalDto.setApprove(2);
                }
                String nextNodeType = actionInfo.getNextNodeType();
                bb.writeLog("当前流程的节点状态："+nextNodeType);
                if("3".equals(nextNodeType)){
                    approvalDto.setCheekCompleted(true);
                }else {
                    approvalDto.setCheekCompleted(false);
                }
                bb.writeLog("approvalDto===>",JSONObject.toJSONString(approvalDto));
                EsbEventResult esbEventResult = EsbUtil.callEsbEvent(eventName, JSONObject.toJSONString(approvalDto));
                if (!esbEventResult.isSuccess()) {
                    errorMsg = esbEventResult.getErroMsg();
                }
            }else {
                errorMsg = "主表数据不存在|事件名未配置";
            }
        }catch (Exception e){
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("审批状态更新Action报错：" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("ApprovalStatusUpdateAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

}
