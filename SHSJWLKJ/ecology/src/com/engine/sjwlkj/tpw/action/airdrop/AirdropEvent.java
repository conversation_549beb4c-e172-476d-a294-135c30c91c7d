package com.engine.sjwlkj.tpw.action.airdrop;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
/**
    * @FileName AirdropEvent
    * @Description 空投请求实体类
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/3/29
    */
@Data
public class AirdropEvent {
    /**
     * 明细表1
     */
    private JSONArray airdropList;
    /**
     * 资金账户
     */
    private String fundingAccount;
    /**
     * 费用类型
     */
    private String expenseItemNo;
    /**
     * 空投名称
     */
    private String eventName;
    /**
     * 计价币种
     */
    private String valuedToken;
    /**
     * 发放币种，多个逗号分割
     */
    private String issuedToken;
    /**
     * 发放总价值/个数
     */
    private String totalAmount;
    /**
     * 申请单流程号
     */
    private String workflowNo;
    /**
     * 多语言列表
     */
    private String eventNameLang;
    /**
     * 活动地址
     */
    private String eventLink;
    /**
     * 备注
     */
    private String remark;
    /**
     * 申请人邮箱
     */
    private String applicant;
    /**
     * requestId
     */
    private String requestId;
    /**
     * 费用大类
     */
    private String expenseTypeNo;
}
