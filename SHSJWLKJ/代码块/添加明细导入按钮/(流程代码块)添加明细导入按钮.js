/**
 * 使用方法：将本js代码，粘贴到指定流程的代码块的<script> </script>标签之间
 */
jQuery(document).ready(function () {
    //流程主表语言类型字段
    var dqxtyyval = WfForm.getFieldValue(WfForm.convertFieldNameToId("dqxtyy"));
    var btnnamelabel = ""
    if (dqxtyyval == 7) {
        btnnamelabel = '<button>批量导入</button>'
    } else {
        btnnamelabel = '<button>Batch import</button>'
    }
    // 创建新的按钮元素
    var newButton = $(btnnamelabel);
    // 添加 Ant Design 的按钮样式
    newButton.addClass('ant-btn ant-btn-primary');
    // 给按钮添加点击事件处理函数
    newButton.click(function () {
        //执行右击导入明细
        WfForm.doRightBtnEvent("BTN_IMPORTDETAIL");
    });
    // 将新按钮添加到 id 为 addbutton0 的 i 元素前面
    //需要在表单上，给某个格子设置id属性为btn1dr
    newButton.insertBefore('#btn1dr');
});