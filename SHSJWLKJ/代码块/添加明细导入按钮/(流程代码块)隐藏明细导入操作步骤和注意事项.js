/**
 * 使用方法：将本js代码，粘贴到指定流程代码块的<script> </script>标签之间
 * 该功能，流程测试模式 不适用
 */
$(document).ready(function () {
    // 获取需要监听变化的节点
    var targetNode = document.body;

    // 配置 MutationObserver 的选项
    var config = {childList: true, subtree: true};

    // 回调函数，用于处理 DOM 变化
    var callback = function (mutationsList, observer) {
        for (var mutation of mutationsList) {
            if (mutation.type === 'childList') {
                $(mutation.addedNodes).each(function () {
                    // 检查新增节点及其后代
                    if ($(this).hasClass('wea-group')) {
                        checkAndHide(this);
                    } else {
                        $(this).find('.wea-group').each(function () {
                            checkAndHide(this);
                        });
                    }
                });
            }
        }
    };

    // 创建 MutationObserver 实例并传入回调函数
    var observer = new MutationObserver(callback);
    observer.observe(targetNode, config);

    // 定义检查并隐藏元素的函数
    function checkAndHide(element) {
        if ($(element).find('.wea-title:contains("操作步骤")').length > 0 ||
            $(element).find('.wea-title:contains("注意事项")').length > 0 ||
            $(element).find('.wea-title:contains("Operation Steps")').length > 0 ||
            $(element).find('.wea-title:contains("Remarks")').length > 0) {
            $(element).hide();
        }
    }

    // 手动触发初始检查
    $('.wea-import-wf-detail .wea-group').each(function () {
        checkAndHide(this);
    });
});