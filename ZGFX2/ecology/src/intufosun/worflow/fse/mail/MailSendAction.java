package intufosun.worflow.fse.mail;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.toolbox.core.convert.Convert;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.extra.mail.EcNewMailUtil;
import weaver.toolbox.json.JSONArray;
import weaver.toolbox.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName MailSendAction
 * @Description 唐老师代码, 在classbean下有class文件，先bak住
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/8/3
 */
public class MailSendAction extends BaseBean implements Action {
    private String nodetype;

    @Override
    public String execute(RequestInfo requestInfo) {
        try {
            String requestId = requestInfo.getRequestid();
            int billId = requestInfo.getRequestManager().getBillid();
            String workflowId = requestInfo.getWorkflowid();
            String mainTableName = requestInfo.getRequestManager().getBillTableName();
            writeLog("MailSendAction requestid : " + requestId);

            //获取流程主表数据
            JSONObject workflowData = QueryUtil.doQuerySingleLine(mainTableName, "", "requestid", requestId);
            writeLog("流程主表数据获取" + workflowData);
            // 流程编号
            String lcbh = workflowData.getStr("lcbh");
            // 工程师邮箱
            String gcsyx = workflowData.getStr("gcsyx");
            // 工程师经理1邮箱
            String gcsjl1 = workflowData.getStr("gcsjl1");
            // 工程师经理2邮箱
            String gcsjl2 = workflowData.getStr("gcsjl2");
            // 邮件已发送1
            String yjyfs1 = workflowData.getStr("yjyfs1");
            // 邮件已发送2
            String yjyfs2 = workflowData.getStr("yjyfs2");

            Map<String, String> mainTableData = mainTableData(workflowId, this.nodetype);
            //writeLog("建模模板配置主要信息" + mainTableData);
            //收件人，邮箱地址，多地址时用英文逗号分隔
            String sendTo = Util.null2String(mainTableData.get("sjr"));
            //抄送，邮箱地址，多地址时用英文逗号分隔
            String sendCc = Util.null2String(mainTableData.get("csr"));
            //密送，邮箱地址，多地址时用英文逗号分隔
            String sendBcc = Util.null2String(mainTableData.get("msr"));
            //邮箱标题
            String emailTitle = replaceLcbh(Util.null2String(mainTableData.get("yjbt")), lcbh);

            // 判断是否已经发送邮件，已发送则跳过
            if (emailTitle.startsWith("物流支持")) {
                if ("0".equals(yjyfs2)) {
                    return Action.SUCCESS;
                }
            } else {
                if ("0".equals(yjyfs1)) {
                    return Action.SUCCESS;
                }
            }

            //邮箱正文
            String emailInfo = checkIfExistRequestId(Util.null2String(mainTableData.get("yjzwmb")), requestId);
            emailInfo = replaceLcbh2(emailInfo, lcbh);
            writeLog("处理前的邮件正文：" + emailInfo);
            //附件id如果后续有需要可更改
            String emailFileIds = "";

            //拿到建模配置表的明细表mainid
            String modeFormId = Util.null2String(mainTableData.get("mainId"));
            //获取到明细表的字段映射数据
            Map<String, Map<String, String>> detailTableData = detailTableData(modeFormId);
            //writeLog("建模邮件配置字段映射数据" + detailTableData);
            Map<String, Map<String, String>> detailFieldName = new HashMap<>();
            //模板主数据进行替换和重新构建模板明细数据
            for (String key : detailTableData.keySet()) {
                //拆分模板字段，以便下文判断是都是模板明细表
                String[] checkDetail = key.split("\\.");
                boolean isDetail = checkDetail.length > 1;
                if (!isDetail) {
                    //非模板明细表数据直接进行替换
                    String value = (String) workflowData.get(detailTableData.get(key).get("sjkzdmc"));
                    String zhtjStr = Util.null2String(Convert.toDBC(detailTableData.get(key).get("zhtj")));
                    writeLog("zhtjStr:" + zhtjStr);
                    //判断是否有字段值转换的问题
                    if (!zhtjStr.isEmpty()) {
                        if ("xlk".equals(zhtjStr)) {
                            String fieldname = detailTableData.get(key).get("sjkzdmc");
                            String values = QueryUtil.getSelectItemName(mainTableName, fieldname, value);
                            emailInfo = emailInfo.replaceAll(key, values);
                        } else {
                            String values = QueryUtil.doQueryFieldValueWithArgs(zhtjStr, "fieldvalue", value);
                            emailInfo = emailInfo.replaceAll(key, values);
                        }
                    } else {
                        emailInfo = emailInfo.replaceAll(key, value);
                    }
                } else {
                    //对模板映射字段数据进行处理重新发布
                    Map<String, String> tempData = new HashMap<>();
                    //获取到模板明细字段下表
                    String workflowDetailLine = checkDetail[0];
                    String filedName = detailTableData.get(key).get("sjkzdmc");
                    tempData.put(key, filedName);
                    if (detailFieldName.containsKey(workflowDetailLine)) {
                        detailFieldName.get(workflowDetailLine).putAll(tempData);
                    } else {
                        detailFieldName.put(workflowDetailLine, tempData);
                    }
                }
            }
            writeLog("建模字段映射流程明细表数据" + detailFieldName);
            //是否明细行数据的，返件运输方式都是顺丰，默认为true，只要有1个物流支持，就为false
            //false的条件是：1、必须有明细行数据，2.至少有一个物流支持类型的明细行
            boolean allDetailSF = true;
            //当需要处理模板明细数据时，做一下操作
            if (!detailFieldName.isEmpty()) {
                //根据流程明细表个数进行处理
                for (String key : detailFieldName.keySet()) {
                    //拿到明细表字段对照表
                    Map<String, String> fieldControl = detailFieldName.get(key);
                    //对邮件模板字段进行截取处理
                    int strStartIndex = emailInfo.indexOf("<" + key + ">");
                    int strEndIndex = emailInfo.indexOf("</" + key + ">");
                    String detailTemplate = emailInfo.substring(strStartIndex, strEndIndex).substring(("<" + key + ">").length());
                    //处理后附件数据
                    String disposedTemplate = "";
                    //获取到流程明细表的数据
                    JSONArray jsonArray = QueryUtil.doQuery(mainTableName + "_" + key, "", "mainid", String.valueOf(billId));
                    //2023-08-04 nodetype = 2时，明细行没数据就不发邮件
                    if (!jsonArray.isEmpty()) {

                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            // 物流支持节点，如果明细表2的返件运输方式为物流支持，则发送邮件
                            if (emailTitle.startsWith("物流支持")) {
                                //返件运输方式 0顺丰，1物流支持
                                String fjysfs = jsonObject.getStr("fjysfs");
                                if ("1".equals(fjysfs)) {
                                    allDetailSF = false;
                                } else {
                                    continue;
                                }
                            }

                            disposedTemplate = disposedTemplate.concat(detailTemplate);
                            //处理明细模板字段
                            for (String keys : fieldControl.keySet()) {
                                String fieldName = fieldControl.get(keys);
                                String value = Util.null2String(jsonObject.get(fieldName));
                                if ("hh".equals(fieldName)) {
                                    value = String.valueOf(i + 1);
                                }
                                String zhtjStr = Util.null2String(Convert.toDBC(detailTableData.get(keys).get("zhtj")));
                                writeLog("zhtjStr:" + zhtjStr);
                                //判断是否有字段值转换的问题
                                if (!zhtjStr.isEmpty()) {
                                    if ("xlk".equals(zhtjStr)) {
                                        writeLog(mainTableName + "_" + key + fieldName + value);
                                        String values = QueryUtil.getSelectItemName(mainTableName, fieldName, value);
                                        disposedTemplate = disposedTemplate.replaceAll(keys, values);
                                    } else {
                                        writeLog("zhtjStrSql:" + zhtjStr);
                                        String values = QueryUtil.doQueryFieldValueWithArgs(zhtjStr, "fieldvalue", value);
                                        disposedTemplate = disposedTemplate.replaceAll(keys, values);
                                    }
                                } else {
                                    disposedTemplate = disposedTemplate.replaceAll(keys, value);
                                }
                            }
                        }
                        //替换正文内容
                        emailInfo = emailInfo.replaceAll("<" + key + ">" + detailTemplate + "</" + key + ">", disposedTemplate);

                    }
                }
            }
            //是否需要发邮件，默认不发
            boolean need2SendEmail = false;
            //nodetype =1 时，都发
            if ("1".equals(nodetype)) {
                need2SendEmail = true;
            }
            //nodetype =2 时，还需要条件：必须有明细行，且至少有一个物流支持类型的明细行
            if ("2".equals(nodetype)) {
                if (!allDetailSF) {
                    need2SendEmail = true;
                } else {
                    writeLog("nodetype为2，明细行都为顺丰，跳过发邮件");
                }
            }
            if (need2SendEmail) {
                writeLog("处理过后的邮件正文" + emailInfo);
                // 如果工程师邮箱、工程师经理1邮箱、工程师经理2邮箱不为空，则发送邮件
                sendTo = addSendTo(sendTo, gcsyx);
                sendTo = addSendTo(sendTo, gcsjl1);
                sendTo = addSendTo(sendTo, gcsjl2);
                boolean mailRequestStatus = EcNewMailUtil.sendMail(sendTo, sendCc, sendBcc, emailTitle, emailInfo, emailFileIds);
                writeLog("mailRequestStatus" + mailRequestStatus);
                if (!mailRequestStatus) {
                    requestInfo.getRequestManager().setMessagecontent("邮件发送异常");
                    return Action.FAILURE_AND_CONTINUE;
                }

                // 邮件发送成功，更新状态
                String updateSql;
                if (emailTitle.startsWith("物流支持")) {
                    updateSql = "update " + mainTableName + " set yjyfs2 = '0' where id = " + billId;
                } else {
                    updateSql = "update " + mainTableName + " set yjyfs1 = '0' where id = " + billId;
                }
                ExecuteUtil.executeSql(updateSql);
            }
        } catch (Exception ex) {
            writeLog(ex.getMessage());
            requestInfo.getRequestManager().setMessagecontent("流程提交时发生异常，请联系管理员！" + ex.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    //获取邮件建模主表信息
    public Map<String, String> mainTableData(String workFlowId, String nodeId) {
        BaseBean baseBean = new BaseBean();
        String selMainTableData = "select * from uf_yjmbxxpz where workflowid = ? and nodeid = ?";
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery(selMainTableData, workFlowId, nodeId);
        Map<String, String> mainTableData = new HashMap<>();
        if (recordSet.next()) {
            // 邮件标题
            String yjbtValue = Util.null2String(recordSet.getString("yjbt"));
            mainTableData.put("yjbt", yjbtValue);
            // 邮件正文模板
            String yjzwmbValue = Util.null2String(recordSet.getString("yjzwmb"));
            mainTableData.put("yjzwmb", yjzwmbValue);
            // 收件人
            String sjrValue = Util.null2String(recordSet.getString("sjr"));
            mainTableData.put("sjr", sjrValue);
            // 抄送人
            String csrValue = Util.null2String(recordSet.getString("csr"));
            mainTableData.put("csr", csrValue);
            // 密送人
            String msrValue = Util.null2String(recordSet.getString("msr"));
            mainTableData.put("msr", msrValue);
            String mainId = Util.null2String(recordSet.getString("id"));
            mainTableData.put("mainId", mainId);
        }
        baseBean.writeLog("邮箱内容主表数据获取完成：" + mainTableData);
        return mainTableData;
    }

    //获取建模明细字段映射表
    public Map<String, Map<String, String>> detailTableData(String mainId) {
        BaseBean baseBean = new BaseBean();
        String selMainTableData = "select * from uf_yjmbxxpz_dt1 where mainid = ?";
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery(selMainTableData, mainId);
        Map<String, Map<String, String>> detailTableData = new HashMap<>();
        while (recordSet.next()) {
            Map<String, String> tempData = new HashMap<>();
            // 模板字段名称
            String key = Util.null2String(recordSet.getString("mbzdmc"));
            // 数据库字段名称
            String detailKey = Util.null2String(recordSet.getString("sjkzdmc").toLowerCase());
            // 转换条件(sql)
            String detailValue = Util.null2String(Convert.toDBC(recordSet.getString("zhtj")));
            tempData.put("sjkzdmc", detailKey);
            tempData.put("zhtj", detailValue);
            detailTableData.put(key, tempData);
        }
        baseBean.writeLog("邮箱模板对应字段映射表获取完成：" + detailTableData);
        return detailTableData;
    }

    //检查是否存在requestid
    public String checkIfExistRequestId(String emailText, String requestId) {
        String allStr1 = "";
        String[] array = emailText.split("\\$");
        for (String s : array) {
            String tempStr = s.toLowerCase();
            if ("requestid".equals(tempStr)) {
                allStr1 = allStr1.concat(requestId);
            } else {
                allStr1 = allStr1.concat(s);
            }
        }
        return allStr1;
    }

    public String replaceLcbh(String content, String lcbh) {
        return content.replace("$lcbh$", lcbh);
    }

    public String replaceLcbh2(String content, String lcbh) {
        return content.replace("#lcbh#", lcbh);
    }

    public String addSendTo(String sendTo, String newSendTo) {
        if (StringUtils.isBlank(sendTo)) {
            return newSendTo;
        } else {
            if (StringUtils.isBlank(newSendTo)) {
                return sendTo;
            }

            return sendTo + "," + newSendTo;
        }
    }
}
