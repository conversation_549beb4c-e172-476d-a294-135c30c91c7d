package com.engine.zgfx2.gyl.sap.bean;

import lombok.Data;

import java.util.List;

/**
 * @FileName SAPConfigDto
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/8/14
 */
@Data
public class SAPConfigDto {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 流程选择 workflowid
     */
    private String lcxz;
    /**
     * 流程版本
     */
    private String lcbb;
    /**
     * OA明细表序号
     */
    private Integer oamxbxh;
    /**
     * 是否启用 1启用 0未启用
     */
    private String sfqy;
    /**
     * 接口名称
     */
    private String jkmc;
    /**
     * 接口描述
     */
    private String jkms;
    /**
     * SAP接口地址
     */
    private String sapjkdz;
    /**
     * 账号
     */
    private String zh;
    /**
     * 密码
     */
    private String mm;
    /**
     * 固定HEAD
     */
    private String gdhead;
    /**
     * 备注
     */
    private String bz;
    /**
     * 明细配置
     */
    private List<DetailDto> detailData;

    @Data
    public static class DetailDto {
        /**
         * 数据id
         */
        private Integer id;
        /**
         * OA字段名
         */
        private String oazdm;
        /**
         * SAP字段名
         */
        private String sapzdm;
        /**
         * 字段描述
         */
        private String zdms;
        /**
         * 字段位置 0 主表；1明细表
         */
        private Integer zdwz;
        /**
         * 字段类型 0 OA字段值；1 固定值
         */
        private Integer zdlx;
        /**
         * 备注
         */
        private String bz;
    }
}
