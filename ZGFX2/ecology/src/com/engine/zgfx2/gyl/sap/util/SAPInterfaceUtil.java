package com.engine.zgfx2.gyl.sap.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.entity.HttpApiLog;
import com.engine.parent.http.util.HttpApiLogUtil;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.zgfx2.gyl.sap.bean.SAPConfigDto;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName SAPInterfaceUtil
 * @Description SAP接口工具类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/8/14
 */
public class SAPInterfaceUtil {


    /**
     * 获取接口配置
     *
     * @param workflowid
     * @return
     */
    public static SAPConfigDto getConfig(String workflowid) {
        SAPConfigDto result = null;
        String sql = "select * from uf_sapcfg where lcxz = ? and sfqy = 1";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, workflowid)) {
            result = QueryUtil.getObj(rs, SAPConfigDto.class);
            if (result != null) {
                String detailSql = "select * from uf_sapcfg_dt1 where mainid = ?";
                if (rs.executeQuery(detailSql, result.getId())) {
                    List<SAPConfigDto.DetailDto> detailDto = QueryUtil.getObjList(rs, SAPConfigDto.DetailDto.class);
                    if (detailDto != null) {
                        result.setDetailData(detailDto);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 调用SAP接口
     *
     * @param cfg
     * @param actionInfo
     * @param actionType 0新增 1变更
     * @return
     */
    public static Map<String, String> callInterface(SAPConfigDto cfg, ActionInfo actionInfo, String actionType) {
        Map<String, String> result = new HashMap<>();
        String errorMsg = "";
        String sapStatus, vendorCode;
        //组装body参数
        Map<String, Object> params = packageBody(cfg, actionInfo);
        //调用接口
        RestResult restResult = HttpUtil.postData(cfg.getSapjkdz(), params, cfg.getZh(), cfg.getMm());

        if (!restResult.isSuccess()) {
            errorMsg = "调用SAP接口出错：" + restResult.getMsg();
        } else {
            JSONObject body = JSONObject.parseObject(restResult.getResponseInfo().getBody());
            JSONObject commonReturn = body.getJSONObject("mt_common_return");
            if (commonReturn != null && !commonReturn.isEmpty()) {
                JSONObject BODY = commonReturn.getJSONObject("BODY");
                if (BODY != null && !BODY.isEmpty()) {
                    sapStatus = Util.null2String(BODY.get("STATUS"));
                    if ("S".equals(sapStatus)) {
                        //如果是新增供应商，则需要获取到SAP返回的供应商编码
                        if ("0".equals(actionType)) {
                            vendorCode = Util.null2String(BODY.get("KEY_VALUE01"));
                            if (vendorCode.isEmpty()) {
                                errorMsg = "SAP未返回供应商编码";
                            } else {
                                result.put("vendorCode", vendorCode);
                            }
                        }
                    } else {
                        errorMsg = "调用SAP接口出错：" + body.toJSONString();
                    }
                }
            }
        }
        result.put("errorMsg", errorMsg);
        //记录日志
        HttpApiLog apiLog = new HttpApiLog();
        apiLog.setApiname(cfg.getJkmc())
                .setDirection(0)
                .setDuration(restResult.getDuration())
                .setIpaddress(actionInfo.getClientIp())
                .setRequestdata(Util.null2String(restResult.getRequestInfo()))
                .setResponsedata(Util.null2String(restResult.getResponseInfo()))
                .setRequesttime(restResult.getStartTime())
                .setResponsetime(restResult.getEndTime())
                .setRequestuser(actionInfo.getUser().getUID())
                .setSourceid(actionInfo.getRequestId())
                .setStatus(errorMsg.isEmpty() ? "0" : "1")
                .setSourcetype(0)
                .setUrl(cfg.getSapjkdz());
        HttpApiLogUtil.insertLog(apiLog);

        return result;
    }

    /**
     * 组装body参数
     *
     * @param cfg
     * @param actionInfo
     * @return
     */
    private static Map<String, Object> packageBody(SAPConfigDto cfg, ActionInfo actionInfo) {
        Map<String, Object> result = new HashMap<>();
        JSONObject headObj = new JSONObject();
        JSONObject bodyObj = new JSONObject();
        //step 1: 设置HEAD参数
        result.put("HEAD", headObj);
        result.put("BODY", bodyObj);
        if (!cfg.getGdhead().isEmpty()) {
            headObj = JSONObject.parseObject(cfg.getGdhead());
            result.put("HEAD", headObj);
        }
        //step 2 :设置BODY参数
        //流程主表数据
        Map<String, String> wfMainData = actionInfo.getMainData();
        //流程明细表
        Map<Integer, List<Map<String, String>>> wfAllDetailData = actionInfo.getDetailData();
        //具体的某个明细表的数据
        List<Map<String, String>> wfDetailData = null;
        if (cfg.getOamxbxh() != null && cfg.getOamxbxh() > 0) {
            wfDetailData = wfAllDetailData.get(cfg.getOamxbxh());
        }
        //详细配置
        List<SAPConfigDto.DetailDto> detailDto = cfg.getDetailData();
        //明细表的配置
        List<SAPConfigDto.DetailDto> detailConfig = new ArrayList<>();
        if (!detailDto.isEmpty()) {
            //设置主表字段
            for (SAPConfigDto.DetailDto d : detailDto) {
                if (d.getZdwz() == 0) {
                    //OA字段值
                    if (d.getZdlx() == 0) {
                        bodyObj.put(d.getSapzdm(), wfMainData.get(d.getOazdm()));
                    } else if (d.getZdlx() == 1) {
                        //固定值
                        bodyObj.put(d.getSapzdm(), d.getOazdm());
                    }
                }
                //判断是否包含明细表
                if (d.getZdwz() > 0) {
                    detailConfig.add(d);
                }
            }

            //设置明细字段值
            //有明细配置，并且流程有明细数据
            if (!detailConfig.isEmpty() && wfDetailData != null && !wfDetailData.isEmpty()) {
                JSONArray bankArray = new JSONArray();
                JSONObject eachBank;
                for (Map<String, String> wfDetail : wfDetailData) {
                    eachBank = new JSONObject();
                    for (SAPConfigDto.DetailDto d : detailConfig) {
                        //OA字段值
                        if (d.getZdlx() == 0) {
                            eachBank.put(d.getSapzdm(), wfDetail.get(d.getOazdm()));
                        } else if (d.getZdlx() == 1) {
                            //固定值
                            eachBank.put(d.getSapzdm(), d.getOazdm());
                        }
                    }
                    bankArray.add(eachBank);
                }
                if (!bankArray.isEmpty()) {
                    bodyObj.put("BANK", bankArray);
                }
            }
        }
        return result;
    }


}
