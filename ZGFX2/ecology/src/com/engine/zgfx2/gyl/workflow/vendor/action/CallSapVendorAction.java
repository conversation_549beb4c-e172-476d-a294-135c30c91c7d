package com.engine.zgfx2.gyl.workflow.vendor.action;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.zgfx2.gyl.sap.bean.SAPConfigDto;
import com.engine.zgfx2.gyl.sap.util.SAPInterfaceUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.Map;

/**
 * @FileName CallSapVendorAction
 * @Description 调用SAP的供应商接口aciton
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/8/10
 */
public class CallSapVendorAction extends BaseBean implements Action {
    /**
     * 流程主表的供应商编码字段名
     */
    private final static String FIELD_VENDOR = "gysbh";

    /**
     * 申请类型 0新增 1变更
     */
    private final static String FIELD_TYPE = "sqlx";

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //出错信息
        String errorMsg;
        try {
            //获取action相关信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            Map<String, String> mainData = actionInfo.getMainData();
            //先检查表单
            errorMsg = checkParam(actionInfo);
            if (errorMsg.isEmpty()) {
                String sqlx = mainData.get(FIELD_TYPE);
                SAPConfigDto cfg = SAPInterfaceUtil.getConfig(actionInfo.getWorkflowId());
                writeLog("SAPConfigDto:" + cfg);
                if (cfg != null) {
                    Map<String, String> sapResult = SAPInterfaceUtil.callInterface(cfg, actionInfo, sqlx);
                    writeLog("sapResult:" + sapResult);
                    errorMsg = sapResult.get("errorMsg");
                    if (errorMsg.isEmpty()) {
                        if ("0".equals(sqlx) && !sapResult.get("vendorCode").isEmpty()) {
                            //新增供应商时，需要更新表单供应商编码
                            errorMsg = updateVendor(actionInfo, sapResult.get("vendorCode"));
                        }
                    }
                } else {
                    writeLog("未查询到启用的SAP接口配置信息，跳过调用SAP接口");
                }
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        writeLog("errorMsg：" + errorMsg);
        writeLog(this.getClass().getName() + "---END");

        //默认无错误信息
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    private String checkParam(ActionInfo actionInfo) {
        String errorMsg = "";
        Map<String, String> mainData = actionInfo.getMainData();
        String sqlx = Util.null2String(mainData.get(FIELD_TYPE));
        String vendorCode = Util.null2String(mainData.get(FIELD_VENDOR));
        if ("0".equals(sqlx) && !vendorCode.isEmpty()) {
            errorMsg = "新增供应商时，供应商编码有值，请检查表单！";
        }
        if ("1".equals(sqlx) && vendorCode.isEmpty()) {
            errorMsg = "更新供应商时，供应商编码为空，请检查表单！";
        }
        return errorMsg;
    }

    private String updateVendor(ActionInfo actionInfo, String vendorCode) {
        String tableName = actionInfo.getFormtableName();
        RecordSet rs = new RecordSet();
        String sql = "update " + tableName + " set " + FIELD_VENDOR + " = ? where requestid = ?";
        if (!rs.executeUpdate(sql, vendorCode, actionInfo.getRequestId())) {
            return "更新供应商编码出错：" + rs.getExceptionMsg();
        }
        return "";
    }

}
