package weaver.fuxing.action;

import org.springframework.util.StringUtils;
import weaver.conn.RecordSet;
import weaver.fuxing.service.YunJianService;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.form.FormManager;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 同步HCP数据
 */
public class ActionSyncHCP extends BaseBean implements Action {

    private String openLog = "true"; //文件日志开关,true:打开日志,false:关闭日志
    private String dbLog;   //数据库日志开关,true:打开日志,false:关闭日志

    private String testBillid;//测试的id
    private String testTb;//测试的表名

    @Override
    public String execute(RequestInfo requestInfo) {
        try {

            int billid = requestInfo.getRequestManager().getBillid();
            String src = requestInfo.getRequestManager().getSrc();
            String tablename = getTablename(requestInfo);
            if (testTb != null && !"".equals(testTb)) {
                tablename = testTb;
            }
            if (testBillid != null && !"".equals(testBillid)) {
                billid = Integer.parseInt(testBillid);
            }
            User user = requestInfo.getRequestManager().getUser();
            Map<String, String> mainParam = getMainParam(billid, tablename);
            String hcpsqlx = getValue(mainParam, "hcpsqlx");//HCP申请类型:新增　追加　取消
            String lcbh = getValue(mainParam, "lcbh");//HCP申请类型:新增　追加　取消
            String zjdhcpsqsx = getValue(mainParam, "zjdhcpsqsx");//HCP申请类型:新增　追加　取消
            String modeDataIds = "";
            if ("0".equals(hcpsqlx)) {
                log("add");
                modeDataIds = getModeDataId(lcbh);
            }
            if ("1".equals(hcpsqlx) || "2".equals(hcpsqlx)) {
                log("cancel");
                modeDataIds = zjdhcpsqsx;
            }

            if (!StringUtils.hasLength(modeDataIds)) {
                requestInfo.getRequestManager().setMessagecontent("流程流转异常,请联系管理员!");
                return Action.FAILURE_AND_CONTINUE;
            }

            boolean result = syncDataFromMode(modeDataIds);
            if (!result) {
                requestInfo.getRequestManager().setMessagecontent("同步数据到云简失败!");
                return Action.FAILURE_AND_CONTINUE;
            }

        } catch (Exception e) {
            e.printStackTrace();
            requestInfo.getRequestManager().setMessagecontent("流程流转异常,请联系管理员!");
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }

    private String getModeDataId(String lcbh) {
        String sql = "SELECT id FROM uf_hdxz where lcbh = '" + lcbh + "'";
        return getData(sql, "id");
    }

    private String getData(String sql, String fieldName) {
        log("getData sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        if (rs.next()) {
            String workcode = Util.null2String(rs.getString(fieldName));
            return workcode;
        }
        return "";
    }

    /**
     * 从建模中取数据同步到云简
     */
    private boolean syncDataFromMode(String id) {
        YunJianService yunJianService = new YunJianService();
        boolean result = yunJianService.syncHcp(id);
        return result;
    }

    private String getValue(Map<String, String> map, String key) {
        if (map.containsKey(key)) {
            return map.get(key);
        }
        return "";
    }

    /**
     * 获取主表字段,从数据库中查询主表字段
     *
     * @param billid
     * @return
     */
    private Map<String, String> getMainParam(int billid, String tableName) {
        Map<String, String> map = new HashMap<>();
        String sql = "select * from " + tableName + " where id = " + billid;
        RecordSet rs = new RecordSet();
        rs.executeSql(sql);
        if (rs.next()) {
            String[] columnNameArray = rs.getColumnName();
            for (String columnName : columnNameArray) {
                String value = Util.null2String(rs.getString(columnName));
                map.put(columnName.toLowerCase(), value);
            }
        }
        return map;
    }

    /**
     * @param billid      数据id
     * @param tableName   表名
     * @param detailIndex 明细表下标记,从1开始
     * @return
     */
    private List<Map<String, String>> getDetailParam(int billid, String tableName, int detailIndex) {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        String sql = "select * from " + tableName + "_dt" + detailIndex + " where mainid = " + billid;
        RecordSet rs = new RecordSet();
        rs.executeSql(sql);
        while (rs.next()) {
            Map<String, String> map = new HashMap<>();
            String[] columnNameArray = rs.getColumnName();
            for (String columnName : columnNameArray) {
                String value = Util.null2String(rs.getString(columnName));
                map.put(columnName.toLowerCase(), value);
            }
            list.add(map);
        }
        return list;
    }

    /**
     * 获取表名
     *
     * @param requestInfo
     * @return
     */
    private String getTablename(RequestInfo requestInfo) {
        int formId = requestInfo.getRequestManager().getFormid();
        FormManager fManager = new FormManager();
        return fManager.getTablename(formId);
    }

    /**
     * 打印日志
     *
     * @param msg
     */
    public void log(String msg) {
        String className = this.getClass().getSimpleName();
        if ("true".equals(openLog)) {
            writeLog(className + " " + msg);
        }

        if ("true".equals(dbLog)) {
            dbLog(className + " " + msg);
        }
    }

    /**
     * 将日志写入数据库
     * 创建loginfo表的建表语句:
     * CREATE TABLE loginfo(msg varchar(4000),date varchar(255),datetime varchar(255));
     *
     * @param msg
     */
    private void dbLog(String msg) {
        msg = msg.replaceAll("'", "''");
        String sql = "insert into loginfo(msg,date,datetime) values('" +
                msg + "','" +
                getCurtDate() + "','" +
                getSystemDate()
                + "')";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
    }

    /**
     * 当前时间,格式化过的
     *
     * @return
     */
    private String getCurtDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date curtDate = new Date();
        return sdf.format(curtDate);
    }

    /**
     * 当前时间，毫秒数
     *
     * @return
     */
    private String getSystemDate() {
        long l = System.currentTimeMillis();
        return String.valueOf(l);
    }

    public String getOpenLog() {
        return openLog;
    }

    public void setOpenLog(String openLog) {
        this.openLog = openLog;
    }

    public String getDbLog() {
        return dbLog;
    }

    public void setDbLog(String dbLog) {
        this.dbLog = dbLog;
    }

    public String getTestBillid() {
        return testBillid;
    }

    public void setTestBillid(String testBillid) {
        this.testBillid = testBillid;
    }

    public String getTestTb() {
        return testTb;
    }

    public void setTestTb(String testTb) {
        this.testTb = testTb;
    }
}
