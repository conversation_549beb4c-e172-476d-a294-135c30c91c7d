package weaver.fuxing.service;

import com.alibaba.fastjson.JSON;
import org.springframework.util.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.fuxing.entity.*;
import weaver.fuxing.util.HttpUtil;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 1.将OA中的部门同步给云简
 * 2.将OA中的人员同步给云简
 * 3.将部门矩阵中的内容同步给云简
 * 4.将HCP预申请建模数据同步给云简
 * 5.将CSR培训流程数据同步给云简
 */
public class YunJianService {

    private final BaseBean logger = new BaseBean();
    public boolean localTest = false;

    //同步接口日志建模id
    private String syncInterfaceLogModeId;

    private void log(Object msg) {
        if (localTest) {
            System.out.println(msg);
        } else {
            logger.writeLog(msg);
        }
    }

    public String getSyncInterfaceLogModeId() {
        return syncInterfaceLogModeId;
    }

    public void setSyncInterfaceLogModeId(String syncInterfaceLogModeId) {
        this.syncInterfaceLogModeId = syncInterfaceLogModeId;
    }

    /**
     * 查询系统参数
     *
     * @return
     */
    public Map<String, String> getSystemParams() {
        Map<String, String> map = new HashMap<>();
        String sql = "SELECT csmc,csz FROM uf_xtcs";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String csmc = Util.null2String(rs.getString("csmc"));
            String csz = Util.null2String(rs.getString("csz"));
            map.put(csmc, csz);
        }
        log("getSystemParams map = " + map.toString());
        return map;
    }


    /**
     * 获取token
     *
     * @return 返回示例：{
     * "resCode": 200000,
     * "resMsg": "success",
     * "data": {
     * "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2NTQwOTgwMjcsInVzZXJuYW1lIjoiY2xvdWRwZW5zZTkzMDA1ODI0ZDkifQ.c59ABHLkvhsBmf0QiPY3FG80LSnak0_G4qcsKwdTfis",
     * "scope": "write",
     * "token_type": "bearer",
     * "expires_in": 7200
     * }
     * }
     */
    public String getToken(Map<String, String> map) {
        try {
            StringJoiner sj = new StringJoiner("&");
            sj.add("grant_type=" + map.get("0"))
                    .add("client_id=" + map.get("1"))
                    .add("client_secret=" + map.get("2"));
            String url = map.get("3") + "?" + sj;
            log("getToken url = " + url);
            String response = HttpUtil.get(url);
            log("getToken response = " + response);
            if (StringUtils.hasLength(response)) {
                TokenResponse tokenResponse = JSON.parseObject(response, TokenResponse.class);
                if (200000 == tokenResponse.getResCode()) {
                    return tokenResponse.getData().getAccessToken();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 同步部门数据
     * 1.查询出所有的部门id和部门编码，存放到一个map中
     * 2.从HrmDepartment和HrmDepartmentDefined中将需要同步的数据查询出来
     * 3.从部门数据更新记录表查询出所有的数据，用一个list存放所有的部门id，用一个map存放部门id和更新时间
     * 4.循环步骤2查询出的数据,使用步骤3的两个数据结构筛选出哪些是新增的数据，哪些是更新的数据
     */
    public void syncDept(Map<String, String> mapEnv, String token) {
        String url = mapEnv.get("4");
        Map<String, String> compCodeMap = getCompCode();
        Map<String, String> deptCodeMap = getDeptCode();
        Map<String, String> allHrmMap = getAllHrm();

        List<String> deptIdList = new ArrayList<>();
        Map<String, String> deptTempMap = new HashMap<>();
        String sql = "SELECT bmid,gxsj,fcbs FROM uf_bmsjgxjlb";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String bmid = Util.null2String(rs.getString("bmid"));
            String gxsj = Util.null2String(rs.getString("gxsj"));
            String fcbs = Util.null2String(rs.getString("fcbs"));
            deptIdList.add(bmid);
            deptTempMap.put(bmid, gxsj + "&" + fcbs);
        }

        List<String> supdepidList = new ArrayList<>();
        supdepidList.add("0");

        while (true) {
            if (supdepidList.isEmpty()) {
                break;
            }
            String supdepidTemp = supdepidList.get(0);
            List<Map<String, String>> deptData = getDeptData(supdepidTemp);
            supdepidList.remove(0);

            List<Map<String, String>> deptDataInsertAndUpdate = new ArrayList<>();
            for (Map<String, String> deptDatum : deptData) {
                String id = deptDatum.get("id");
                supdepidList.add(id);
                if (deptIdList.contains(id)) {
                    String modified = deptDatum.get("modified");
                    String deptTempMapValue = deptTempMap.get(id);
                    String[] deptTempMapValueSplit = deptTempMapValue.split("&");
                    if (modified.equals(deptTempMapValueSplit[0])) {
                        String fcbsTemp = "";//临时的封存标识
                        if (deptTempMapValueSplit.length > 1) {
                            fcbsTemp = deptTempMapValueSplit[1];
                        }
                        String fcbs = deptDatum.get("canceled");//现在的封存标识
                        if (!fcbsTemp.equals(fcbs)) {
                            deptDataInsertAndUpdate.add(deptDatum);
                        }
                    } else {
                        deptDataInsertAndUpdate.add(deptDatum);
                    }
                } else {
                    deptDataInsertAndUpdate.add(deptDatum);
                }
            }

            if (!deptDataInsertAndUpdate.isEmpty()) {
                List<DeptRequest.DataDTO> list = new ArrayList<>();
                for (int i = 0; i < deptDataInsertAndUpdate.size(); i++) {
                    Map<String, String> map = deptDataInsertAndUpdate.get(i);
                    DeptRequest.DataDTO dataDTO = new DeptRequest.DataDTO();
                    dataDTO.setCode(map.get("departmentcode"));
                    String supdepid = map.get("supdepid");
                    String subcompanyid1 = map.get("subcompanyid1");
                    if (!"0".equals(supdepid)) {
                        dataDTO.setParentCode(deptCodeMap.get(supdepid));
                    }
                    if ("0".equals(supdepid)) {
                        dataDTO.setParentCode(compCodeMap.get(subcompanyid1));
                    }
                    dataDTO.setDepartmentName(map.get("departmentname"));
                    String supervisor = getSupervisor(map);
                    dataDTO.setSupervisor(allHrmMap.getOrDefault(supervisor, ""));
                    dataDTO.setType("D");
                    dataDTO.setCostCenterFlag("N");
                    dataDTO.setBudgetFlag("Y");
                    dataDTO.setEnabledFlag(getEnabledFlag(map));

                    list.add(dataDTO);
                }

                //通过接口同步下数据
                DeptRequest request = new DeptRequest();
                request.setBizId(getUUID());
                request.setTimestamp(getCurtTime());
                request.setData(list);
                boolean syncResult = syncDeptInterface(request, url, token);
                if (syncResult) {
                    saveJl(deptDataInsertAndUpdate, deptIdList);
                }
            }
        }
    }

    /**
     * 清空部门表中的数据
     */
    public void clearDeptRecord() {
        RecordSet rs = new RecordSet();
        String sql = "delete from uf_bmsjgxjlb";
        log("clearDeptRecord sql = " + sql);
        rs.execute(sql);
    }

    private void saveJl(List<Map<String, String>> list, List<String> deptIdList) {
        //开始更新数据到记录表中
        RecordSet rs = new RecordSet();
        String curtDate = getCurtDate();
        for (Map<String, String> map : list) {
            String id = map.get("id");
            String modified = map.get("modified");
            String canceled = map.get("canceled");
            if (StringUtils.hasLength(canceled)) {
                canceled = "'" + canceled + "'";
            } else {
                canceled = "NULL";
            }
            String jlSql = "";
            if (deptIdList.contains(id)) {
                jlSql = "UPDATE uf_bmsjgxjlb set gxsj='" + modified
                        + "',bjlgxsj='" + curtDate + "',fcbs=" + canceled + " where bmid = " + id;
            } else {
                jlSql = "insert into uf_bmsjgxjlb(bmid,gxsj,bjlcjsj,bjlgxsj,fcbs) values(" + id +
                        ",'" + modified + "','" + curtDate + "','" + curtDate + "'," + canceled + ")";
            }
            log("jlSql = " + jlSql);
            rs.execute(jlSql);
        }
    }

    private String getCurtDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date curtDate = new Date();
        return sdf.format(curtDate);
    }

    public long getCurtTime() {
        return System.currentTimeMillis();
    }

    public String getUUID() {
        return UUID.randomUUID().toString();
    }

    private String getEnabledFlag(Map<String, String> map) {
        String canceled = map.get("canceled");
        if ("1".equals(canceled)) {
            return "N";
        }
        return "Y";
    }

    private String getSupervisor(Map<String, String> map) {
        String departmentheadern1 = map.get("departmentheadern1");
        String directormanagern2 = map.get("directormanagern2");
        String managersupervisorn3 = map.get("managersupervisorn3");
        if (StringUtils.hasLength(managersupervisorn3)) {
            return managersupervisorn3;
        }
        if (StringUtils.hasLength(directormanagern2)) {
            return directormanagern2;
        }
        if (StringUtils.hasLength(departmentheadern1)) {
            return departmentheadern1;
        }
        return "";
    }

    /**
     * 查询所有的部门数据
     * 过滤掉香港分部的数据,香港分部的id为6
     *
     * @return
     */
    private List<Map<String, String>> getDeptData(String supdepid) {
        List<Map<String, String>> list = new ArrayList<>();
        String sql = "SELECT * from (SELECT h.id,h.departmentcode,h.supdepid,h.subcompanyid1,hd.deptid,h.departmentname,h.created,h.modified,hd.departmentheadern1,hd.directormanagern2,hd.managersupervisorn3,h.canceled FROM HrmDepartment h left join HrmDepartmentDefined hd on h.id = hd.deptid) t where t.supdepid = " + supdepid;
        log("getDeptData sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            Map<String, String> map = new HashMap<>();
            String[] columnNameArray = rs.getColumnName();
            String subcompanyid1 = "";
            for (String columnName : columnNameArray) {
                String value = Util.null2String(rs.getString(columnName));
                if ("departmentcode".equals(columnName) && "".equals(value)) {
                    continue;
                }
                if ("subcompanyid1".equals(columnName.toLowerCase())) {
                    subcompanyid1 = value;
                }
                map.put(columnName.toLowerCase(), value);
            }
            if (!"6".equals(subcompanyid1)) {
                list.add(map);
            }
        }
        log("getDeptData list = " + list.toString());
        return list;
    }

    /**
     * 调用同步部门数据的接口
     *
     * @param request
     * @param url
     * @throws Exception
     */
    private boolean syncDeptInterface(DeptRequest request, String url, String token) {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("access_token", token);
            String requestStr = JSON.toJSONString(request);
            log("syncDeptInterface requestStr = " + requestStr);
            String response = HttpUtil.post(url, requestStr, headers);
            log("syncDeptInterface response = " + response);
            DeptResponse deptResponse = JSON.parseObject(response, DeptResponse.class);
            if (deptResponse.getResCode() == FuXingContants.SUCCESS) {
                addSyncInterfaceLog(LogType.DEPT.getValue(), ResultStatus.SUCCESS.getValue(),
                        deptResponse.getResMsg(),
                        deptResponse.getData().getCreated() == null ? "" : deptResponse.getData().getCreated(),
                        deptResponse.getData().getUpdated() == null ? "" : deptResponse.getData().getUpdated());
                return true;
            }

            addSyncInterfaceLog(LogType.DEPT.getValue(), ResultStatus.FAIL.getValue(),
                    deptResponse.getResMsg(),
                    deptResponse.getData().getCreated() == null ? "" : deptResponse.getData().getCreated(),
                    deptResponse.getData().getUpdated() == null ? "" : deptResponse.getData().getUpdated());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 查询所有的部门编码
     *
     * @return
     */
    private Map<String, String> getDeptCode() {
        Map<String, String> map = new HashMap<>();
        String sql = "SELECT id,departmentcode FROM HrmDepartment";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String id = Util.null2String(rs.getString("id"));
            String departmentcode = Util.null2String(rs.getString("departmentcode"));
            map.put(id, departmentcode);
        }
        return map;
    }

    /**
     * 查询所有的分部编码
     *
     * @return
     */
    private Map<String, String> getCompCode() {
        Map<String, String> map = new HashMap<>();
        String sql = "SELECT id,subcompanycode FROM HrmSubCompany";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String id = Util.null2String(rs.getString("id"));
            String subcompanycode = Util.null2String(rs.getString("subcompanycode"));
            map.put(id, subcompanycode);
        }
        return map;
    }

    /**
     * 查询出所有的人员和工号的对应关系
     *
     * @return
     */
    private Map<String, String> getAllHrm() {
        Map<String, String> map = new HashMap<>();
        String sql = "SELECT id,workcode FROM hrmresource";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String id = Util.null2String(rs.getString("id"));
            String workcode = Util.null2String(rs.getString("workcode"));
            map.put(id, workcode);
        }
        return map;
    }

    public static void main(String[] args) {
        YunJianService service = new YunJianService();
        service.localTest = true;

        //测试获取token的接口 start
        Map<String, String> map = new HashMap<>();
        map.put("0", "client_credentials");
        map.put("1", "cloudpense93005824d9");
        map.put("2", "43b3aaed42afa274132f17a26a5053b2498581609a92b6da5531443391f6305ad824a587");
        map.put("3", "https://topenapi.cloudpense.com/common/unAuth/tokens/get");
        String token = "";
//        String token = service.getToken(map);
//        System.out.println(token);
        //测试获取token的接口 end

        //测试同步部门接口 start
        boolean testDept = false;
        if (testDept) {
            String syncDeptUrl = "https://topenapi.cloudpense.com/common/departments/batch";
            DeptRequest deptRequest = new DeptRequest();
            List<DeptRequest.DataDTO> list = new ArrayList<>();
            DeptRequest.DataDTO dataDTO = new DeptRequest.DataDTO();
            dataDTO.setCode("666666");
            dataDTO.setParentCode("1008");
            dataDTO.setDepartmentName("测试部门2");
            dataDTO.setSupervisor("");
            dataDTO.setType("D");
            dataDTO.setCostCenterFlag("N");
            dataDTO.setBudgetFlag("Y");
            dataDTO.setEnabledFlag("Y");
            list.add(dataDTO);
            deptRequest.setData(list);
            deptRequest.setBizId(service.getUUID());
            deptRequest.setTimestamp(service.getCurtTime());

            //{"resCode":200000,"resMsg":"success","bizId":"0fd8e59f-ce26-4933-bf46-ec724e4b167c","data":{"validatorErrors":[],"created":"此次请求新建成功了0条数据","warnings":[{"code":"1","messages":["只有类型为公司才能设置票据币种","上级编码D001不存在，默认重置为总公司"]}],"dbErrors":[],"updated":"此次请求更新成功了1条数据"}}
            service.syncDeptInterface(deptRequest, syncDeptUrl, token);
        }
        //测试同步部门接口 end

        //测试同步人员接口 start
        boolean testHrm = false;
        if (testHrm) {
            String hrmUrl = "https://topenapi.cloudpense.com/common/v2/users/batch";
            HrmRequest hrmRequest = new HrmRequest();
            List<HrmRequest.DataDTO> list = new ArrayList<>();
            HrmRequest.DataDTO dataDTO = new HrmRequest.DataDTO();
            dataDTO.setCode("E0006");
            dataDTO.setEmailAddress("<EMAIL>");
            dataDTO.setFullName("Katherine Qian 钱悦平");
            dataDTO.setDepartmentCode("100603");
            dataDTO.setBranchCode("6300");
            dataDTO.setCostCenterCode("21");
            dataDTO.setLevel("L6");
            dataDTO.setBaseCity("上海");
            dataDTO.setEnabledFlag("N");
//            dataDTO.setParentCode(map.get("managerid"));
            dataDTO.setColumn1("1");//1:内勤，2:外勤
            dataDTO.setColumn30("302190");
            list.add(dataDTO);
            hrmRequest.setData(list);
            hrmRequest.setBizId(service.getUUID());
            hrmRequest.setTimestamp(service.getCurtTime());

            service.syncHrmInterface(hrmRequest, hrmUrl, token);
        }
        //测试同步人员接口 end

        //测试同步矩阵 start
        boolean testMatrix = false;
        if (testMatrix) {
            String url = "https://topenapi.cloudpense.com/common/v2/users/batch";
            MatrixRequest request = new MatrixRequest();
            MatrixRequest.DataDTO dto = new MatrixRequest.DataDTO();
            dto.setCode("N-1");
            dto.setValue("");
            dto.setType("100603");

            dto = new MatrixRequest.DataDTO();
            dto.setCode("N-2");
            dto.setValue("");
            dto.setType("100603");

            dto = new MatrixRequest.DataDTO();
            dto.setCode("N-3");
            dto.setValue("");
            dto.setType("100603");
            service.syncMatrixInterface(request, url, token);
        }
        //测试同步矩阵 end

        //HCP预申请 start
        boolean testHcp = false;
        if (testHcp) {
            String url = "https://topenapi.cloudpense.com/common/document";
            HcpApplyRequest request = new HcpApplyRequest();
            HcpApplyRequest.DataDTO dto = new HcpApplyRequest.DataDTO();
            HcpApplyRequest.DataDTO.HeaderDTO headerDTO = new HcpApplyRequest.DataDTO.HeaderDTO();
            headerDTO.setExternalId("HCPHDYSQ-20220217-0001");//流程编号
            headerDTO.setHeaderTypeCode("ZG_1_1");//单据类型编码
            headerDTO.setStatus("approved");//单据状态
            headerDTO.setColumn21("test");//活动名称
            headerDTO.setChargeUserCode("E0006");//申请人(传员工号）
            headerDTO.setColumn22("E0006");//员工工号
            headerDTO.setSubmitDepartmentCode("100603");//申请部门
            headerDTO.setBranchCode("6300");//申请公司
            headerDTO.setTotalAmount("280000");//总金额
            headerDTO.setColumn24("慈善捐赠");//HCP活动性质
            headerDTO.setStartDatetime("2022-02-17");//活动开始日期
            headerDTO.setEndDatetime("2022-02-17");//活动结束日期
            headerDTO.setColumn26("1");//HCP活动地点
            headerDTO.setChargeDepartmentCode("16880");//成本中心
//            HcpApplyRequest.DataDTO.HeaderDTO.Column25DTO column25DTO = new HcpApplyRequest.DataDTO.HeaderDTO.Column25DTO();
//            column25DTO.setText("test");
//            column25DTO.setUrl("http://10.16.18.132:8088/spa/cube/index.html#/main/cube/card?type=0&modeId=3&formId=-67&billid=123&opentype=0&customid=2&viewfrom=fromsearchlist&_key=0eyhst");
            headerDTO.setColumn25("{\"text\": \"传OA对应的活动名称\",\"uri\": \"http://www.baidu.com\"}");
            dto.setHeader(headerDTO);
            request.setBizId(service.getUUID());
            request.setTimestamp(service.getCurtTime());
            request.setData(dto);
            HcpApplyResponse hcpApplyResponse = service.syncHcpInterface(request, url, token);
        }
        //HCP预申请 end

        //CSR培训申请 start
        boolean testCsr = false;
        if (testCsr) {
            List<MatrixRequest.DataDTO> insertOrUpdateList = new ArrayList<>();
            for (int i = 0; i < 320; i++) {
                MatrixRequest.DataDTO dto = new MatrixRequest.DataDTO();
                insertOrUpdateList.add(dto);
            }
            service.syncBatchMatrix(insertOrUpdateList, "", "");
        }
        //CSR培训申请 end

        String tbsbxx = "{\"resCode\":200000,\"resMsg\":\"success\",\"bizId\":\"135f10e0-6fde-4e8d-bb6f-ff18ab3e689f\",\"data\":{\"validatorErrors\":[],\"created\":\"此次请求新建成功了0条数据\",\"warnings\":[{\"code\":\"100804030101\",\"messages\":[\"部门code为100804030101的部门负责人198不存在\"]}],\"dbErrors\":[],\"updated\":\"此次请求更新成功了1条数据\"}}";
        DeptResponse deptResponse = JSON.parseObject(tbsbxx, DeptResponse.class);
        service.setSyncInterfaceLogModeId("10");
        StringBuilder sbUuid = new StringBuilder();
        String syncInterfaceLogSql = service.getSyncInterfaceLogSql(0, 1, tbsbxx,
                deptResponse.getData().getCreated(), deptResponse.getData().getUpdated(), sbUuid);
        System.out.println(syncInterfaceLogSql);
        System.out.println(sbUuid);
    }

    /**
     * 同步人员数据
     *
     * @param mapEnv
     * @param token
     */
    public void syncHrm(Map<String, String> mapEnv, String token) {
        String url = mapEnv.get("5");
        List<Map<String, String>> hrmDataList = getHrmDataList();
        log("hrmDataList size = " + hrmDataList.size());

        List<String> hrmIdList = new ArrayList<>();
        Map<String, String> hrmTempMap = new HashMap<>();
        String sql = "SELECT ryid,gxsj FROM uf_rysjgxjlb";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String ryid = Util.null2String(rs.getString("ryid"));
            String gxsj = Util.null2String(rs.getString("gxsj"));
            hrmIdList.add(ryid);
            hrmTempMap.put(ryid, gxsj);
        }

        List<Map<String, String>> hrmDataInsertAndUpdate = new ArrayList<>();
        for (Map<String, String> hrmDataMap : hrmDataList) {
            String id = hrmDataMap.get("id");
            String modified = hrmDataMap.get("modified");
            if (hrmIdList.contains(id)) {
                String gxsj = hrmTempMap.get(id);
                if (!gxsj.equals(modified)) {
                    hrmDataInsertAndUpdate.add(hrmDataMap);
                }
            } else {
                hrmDataInsertAndUpdate.add(hrmDataMap);
            }
        }

        if (!hrmDataInsertAndUpdate.isEmpty()) {
            log("hrmDataInsertAndUpdate size = " + hrmDataInsertAndUpdate.size());
            boolean result = true;
            List<HrmRequest.DataDTO> list = new ArrayList<>();
            List<Map<String, String>> tempList = new ArrayList<>();
            for (int i = 0; i < hrmDataInsertAndUpdate.size(); i++) {
                Map<String, String> map = hrmDataInsertAndUpdate.get(i);
                HrmRequest.DataDTO dataDTO = new HrmRequest.DataDTO();
                dataDTO.setCode(map.get("workcode"));
                dataDTO.setEmailAddress(map.get("email"));
                dataDTO.setFullName(map.get("lastname"));
                dataDTO.setDepartmentCode(map.get("deptcode"));
                dataDTO.setBranchCode(map.get("subcomcode"));
                dataDTO.setCostCenterCode(map.get("cbzx"));
                dataDTO.setLevel(map.get("dj"));
                dataDTO.setBaseCity(map.get("locationname"));
                String status = map.get("status");
                if ("4".equals(status) ||
                        "5".equals(status) ||
                        "6".equals(status) ||
                        "7".equals(status)) {
                    dataDTO.setEnabledFlag("N");
                } else {
                    dataDTO.setEnabledFlag("Y");
                }
                dataDTO.setParentCode(map.get("managerid"));
                dataDTO.setColumn1(map.get("nwq"));
                dataDTO.setColumn30(map.get("vendor"));
                dataDTO.setColumn37(map.get("cbzx_hr"));

                list.add(dataDTO);
                tempList.add(map);

                if (list.size() == 199) {
                    log("list size = " + list.size());
                    HrmRequest hrmRequest = new HrmRequest();
                    hrmRequest.setBizId(getUUID());
                    hrmRequest.setTimestamp(getCurtTime());
                    hrmRequest.setData(list);
                    boolean syncResult = syncHrmInterface(hrmRequest, url, token);
                    if (syncResult) {
                        saveHrmJl(tempList, hrmIdList);
                    } else {
                        result = false;
                        break;
                    }

                    list.clear();
                    tempList.clear();
                }
            }

            if (!result) {
                log("result = " + result);
                return;
            }

            if (!list.isEmpty() && result) {
                log("list isEmpty size = " + list.size());
                //通过接口同步下数据
                HrmRequest hrmRequest = new HrmRequest();
                hrmRequest.setBizId(getUUID());
                hrmRequest.setTimestamp(getCurtTime());
                hrmRequest.setData(list);
                boolean syncResult = syncHrmInterface(hrmRequest, url, token);
                if (syncResult) {
                    saveHrmJl(tempList, hrmIdList);
                }
            }
        }

    }

    /**
     * 同步人员接口
     *
     * @param request
     * @param url
     * @param token
     * @return
     */
    private boolean syncHrmInterface(HrmRequest request, String url, String token) {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("access_token", token);
            String requestStr = JSON.toJSONString(request);
            log("syncHrmInterface requestStr = " + requestStr);
            String response = HttpUtil.post(url, requestStr, headers);
            log("syncHrmInterface response = " + response);
            DeptResponse deptResponse = JSON.parseObject(response, DeptResponse.class);
            if (deptResponse.getResCode() == FuXingContants.SUCCESS) {
                addSyncInterfaceLog(LogType.HRM.getValue(), ResultStatus.SUCCESS.getValue(),
                        deptResponse.getResMsg(),
                        deptResponse.getData().getCreated() == null ? "" : deptResponse.getData().getCreated(),
                        deptResponse.getData().getUpdated() == null ? "" : deptResponse.getData().getUpdated());
                return true;
            }

            addSyncInterfaceLog(LogType.HRM.getValue(), ResultStatus.FAIL.getValue(),
                    deptResponse.getResMsg(),
                    deptResponse.getData().getCreated() == null ? "" : deptResponse.getData().getCreated(),
                    deptResponse.getData().getUpdated() == null ? "" : deptResponse.getData().getUpdated());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 查询CSR培训申请流程的数据
     *
     * @return
     */
    private List<Map<String, String>> getCsrDataList(String ids) {
        List<Map<String, String>> list = new ArrayList<>();
        String sql = "SELECT f.id,(select hr.workcode from HrmResource hr where hr.id = f.sqr) sqr,f.yggh,f.lcbh,(SELECT hd.departmentcode from HrmDepartment hd where hd.id = f.sqbm) sqbm,(SELECT hs.subcompanycode from HrmSubCompany hs where hs.id = f.sqfb) sqfb,wr.requestname,(SELECT uz.mc FROM uf_zg_yyzsj uz where uz.id = f.yy) yy,f.pxksrq,f.pxjsrq,f.cypxrysm FROM formtable_main_17 f,workflow_requestbase wr where f.requestid = wr.requestid and (f.sftb = 1 or f.sftb is null) and f.id in(" + ids + ")";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            Map<String, String> map = new HashMap<>();
            String[] columnNameArray = rs.getColumnName();
            for (String columnName : columnNameArray) {
                String value = Util.null2String(rs.getString(columnName));
                map.put(columnName.toLowerCase(), filterStr(value));
            }
            list.add(map);
        }
        log("getCsrDataList list = " + list.toString());
        return list;
    }

    public String getCsrMc(String yy) {
        String sql = "SELECT uz.mc FROM uf_zg_yyzsj uz where uz.id = " + yy;
        log("getCsrMc sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        if (rs.next()) {
            String mc = Util.null2String(rs.getString("mc"));
            return mc;
        }
        return "";
    }

    /**
     * 同步矩阵内容
     *
     * @param request
     * @param url
     * @param token
     * @return
     */
    private boolean syncMatrixInterface(MatrixRequest request, String url, String token) {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("access_token", token);
            String requestStr = JSON.toJSONString(request);
            log("syncMatrixInterface requestStr = " + requestStr);
            String response = HttpUtil.post(url, requestStr, headers);
            log("syncMatrixInterface response = " + response);
            DeptResponse deptResponse = JSON.parseObject(response, DeptResponse.class);
            if (deptResponse.getResCode() == FuXingContants.SUCCESS) {
                addSyncInterfaceLog(LogType.MATRIX.getValue(), ResultStatus.SUCCESS.getValue(),
                        deptResponse.getResMsg(),
                        deptResponse.getData().getCreated() == null ? "" : deptResponse.getData().getCreated(),
                        deptResponse.getData().getUpdated() == null ? "" : deptResponse.getData().getUpdated());
                return true;
            }

            addSyncInterfaceLog(LogType.MATRIX.getValue(), ResultStatus.FAIL.getValue(),
                    deptResponse.getResMsg(),
                    deptResponse.getData().getCreated() == null ? "" : deptResponse.getData().getCreated(),
                    deptResponse.getData().getUpdated() == null ? "" : deptResponse.getData().getUpdated());

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private HcpApplyResponse syncHcpInterface(HcpApplyRequest request, String url, String token) {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("access_token", token);
            String requestStr = JSON.toJSONString(request);
            log("syncHcpInterface requestStr = " + requestStr);
            String response = HttpUtil.post(url, requestStr, headers);
            log("syncHcpInterface response = " + response);
            HcpApplyResponse hcpApplyResponse = JSON.parseObject(response, HcpApplyResponse.class);
            return hcpApplyResponse;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public HcpApplyResponse syncCsrInterface(CsrApplyRequest request, String url, String token) {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("access_token", token);
            String requestStr = JSON.toJSONString(request);
            log("syncCsrInterface requestStr = " + requestStr);
            String response = HttpUtil.post(url, requestStr, headers);
            log("syncCsrInterface response = " + response);
            HcpApplyResponse hcpApplyResponse = JSON.parseObject(response, HcpApplyResponse.class);
            return hcpApplyResponse;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询所有的人员数据
     * 这里将workcode为空，或部门为空，或公司为空的数据都过滤掉了
     *
     * @return
     */
    private List<Map<String, String>> getHrmDataList() {
        List<Map<String, String>> list = new ArrayList<>();
//        String sql = "SELECT id,workcode,email,lastname,(select hd.departmentcode from HrmDepartment hd where hd.id = h.departmentid) deptCode,(SELECT hs.subcompanycode FROM HrmSubCompany hs where hs.id = h.subcompanyid1) subcomCode,(SELECT cf.field0 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') cbzx,(SELECT cf.field2 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') dj,(SELECT hl.locationname FROM hrmlocations hl WHERE hl.id = h.locationid) locationname,(select hr.workcode from HrmResource hr where hr.id = h.managerid) managerid,(SELECT cf.field1 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') nwq,(SELECT cf.field4 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') vendor,h.modified,h.status FROM HrmResource h";
//        String sql = "SELECT id,workcode,email,lastname,(select hd.departmentcode from HrmDepartment hd where hd.id = h.departmentid) deptCode,(SELECT hs.subcompanycode FROM HrmSubCompany hs where hs.id = h.subcompanyid1) subcomCode,(select bh from (SELECT cf.id,uc.bh FROM cus_fielddata cf,uf_cbzx uc where cf.scope = 'HrmCustomFieldByInfoType' and cf.field0 = uc.id) cfuc where cfuc.id = h.id) cbzx,(SELECT cf.field2 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') dj,(SELECT hl.locationname FROM hrmlocations hl WHERE hl.id = h.locationid) locationname,(select hr.workcode from HrmResource hr where hr.id = h.managerid) managerid,(SELECT cf.field1 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') nwq,(SELECT cf.field4 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') vendor,h.modified,h.status FROM HrmResource h";
        String sql = "SELECT id,workcode,email,lastname,(select hd.departmentcode from HrmDepartment hd where hd.id = h.departmentid) deptCode,(SELECT hs.subcompanycode FROM HrmSubCompany hs where hs.id = h.subcompanyid1) subcomCode,(select bh from (SELECT cf.id,uc.bh FROM cus_fielddata cf,uf_cbzx uc where cf.scope = 'HrmCustomFieldByInfoType' and cf.field0 = uc.id) cfuc where cfuc.id = h.id) cbzx,(select bh from (SELECT cf.id,uc.bh FROM cus_fielddata cf,uf_cbzx uc where cf.scope = 'HrmCustomFieldByInfoType' and cf.field5 = uc.id) cfuc where cfuc.id = h.id) cbzx_hr,(SELECT cf.field2 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') dj,(SELECT hl.locationname FROM hrmlocations hl WHERE hl.id = h.locationid) locationname,(select hr.workcode from HrmResource hr where hr.id = h.managerid) managerid,(SELECT cf.field1 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') nwq,(SELECT cf.field4 FROM cus_fielddata cf where cf.id = h.id and cf.scope = 'HrmCustomFieldByInfoType') vendor,h.modified,h.status FROM HrmResource h";

        log("getHrmDataList sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            Map<String, String> map = new HashMap<>();
            String[] columnNameArray = rs.getColumnName();
            for (String columnName : columnNameArray) {
                String value = Util.null2String(rs.getString(columnName));
                if (columnName.equals("dj")) {
                    if (StringUtils.hasLength(value)) {
                        value = "L" + value;
                    }
                }
                if (columnName.equals("nwq")) {
                    if (StringUtils.hasLength(value)) {
                        if ("1".equals(value)) {
                            value = "2";
                        } else if ("0".equals(value)) {
                            value = "1";
                        }
                    }
                }
                map.put(columnName.toLowerCase(), value);
            }
            if (StringUtils.hasLength(map.get("workcode")) &&
                    StringUtils.hasLength(map.get("deptcode")) &&
                    StringUtils.hasLength(map.get("subcomcode"))) {
                list.add(map);
            }
        }
        return list;
    }

    private void saveHrmJl(List<Map<String, String>> list, List<String> deptIdList) {
        //开始更新数据到记录表中
        RecordSet rs = new RecordSet();
        String curtDate = getCurtDate();
        for (Map<String, String> map : list) {
            String id = map.get("id");
            String modified = map.get("modified");

            String jlSql = "";
            if (deptIdList.contains(id)) {
                jlSql = "UPDATE uf_rysjgxjlb set gxsj='" + modified
                        + "',bjlgxsj='" + curtDate + "' where ryid = " + id;
            } else {
                jlSql = "insert into uf_rysjgxjlb(ryid,gxsj,bjlcjsj,bjlgxsj) values(" + id +
                        ",'" + modified + "','" + curtDate + "','" + curtDate + "')";
            }
            log("saveHrmJl jlSql = " + jlSql);
            rs.execute(jlSql);
        }
    }

    private void saveMatrixJl(List<Matrixtable> matrixRecordList) {
        //开始更新数据到记录表中
        RecordSet rs = new RecordSet();
        String curtDate = getCurtDate();
        for (Matrixtable matrixtable : matrixRecordList) {
            if (matrixtable.isInsert()) {
                String sql = "insert into uf_tbjzjlb(uuid,bmbm,departmentheadern1,directormanagern2,managersupervisorn3,bjlcjsj,bjlgxsj) values('" +
                        matrixtable.getUuid() + "','" + matrixtable.getDeptCode() + "','" + matrixtable.getDepartmentheadern1() +
                        "','" + matrixtable.getDirectormanagern2() + "','" + matrixtable.getManagersupervisorn3() + "','" +
                        curtDate + "','" + curtDate + "')";
                log("saveMatrixJl sql = " + sql);
                rs.execute(sql);
            } else {
                String sql = "update uf_tbjzjlb set bmbm = '" + matrixtable.getDeptCode() + "',departmentheadern1='" +
                        matrixtable.getDepartmentheadern1() + "',directormanagern2='" + matrixtable.getDirectormanagern2() +
                        "',managersupervisorn3='" + matrixtable.getManagersupervisorn3() + "',bjlgxsj = '" + curtDate +
                        "' where uuid='"
                        + matrixtable.getUuid() + "'";
                log("saveMatrixJl sql = " + sql);
                rs.execute(sql);
            }
        }
    }

    /**
     * 同步矩阵
     */
    public void syncMatrix(Map<String, String> mapEnv, String token) {
        String url = mapEnv.get("6");
        List<MatrixRequest.DataDTO> insertOrUpdateList = new ArrayList<>();
        List<Matrixtable> matrixRecordList = new ArrayList<>();

        List<Matrixtable> matrixList = getMatrixList();//矩阵中的所有数据
        Map<String, Matrixtable> matrixRecord = getMatrixRecord();//矩阵同步记录表

        String N1 = "ApprovalMatrix_01";
        String N2 = "ApprovalMatrix_02";
        String N3 = "ApprovalMatrix_03";

        for (Matrixtable matrixtable : matrixList) {
            if (matrixRecord.containsKey(matrixtable.getUuid())) {
                Matrixtable matrixtableOld = matrixRecord.get(matrixtable.getUuid());
                if (!matrixtableOld.same(matrixtable)) {
                    MatrixRequest.DataDTO dto = new MatrixRequest.DataDTO();
                    dto.setCode(N1);
                    dto.setValue(matrixtable.getDepartmentheadern1());
                    dto.setType(matrixtable.getDeptCode());
                    insertOrUpdateList.add(dto);

                    dto = new MatrixRequest.DataDTO();
                    dto.setCode(N2);
                    dto.setValue(matrixtable.getDirectormanagern2());
                    dto.setType(matrixtable.getDeptCode());
                    insertOrUpdateList.add(dto);

                    dto = new MatrixRequest.DataDTO();
                    dto.setCode(N3);
                    dto.setValue(matrixtable.getManagersupervisorn3());
                    dto.setType(matrixtable.getDeptCode());
                    insertOrUpdateList.add(dto);

                    matrixtable.setInsert(false);
                    matrixRecordList.add(matrixtable);
                }
            } else {
                MatrixRequest.DataDTO dto = new MatrixRequest.DataDTO();
                dto.setCode(N1);
                dto.setValue(matrixtable.getDepartmentheadern1());
                dto.setType(matrixtable.getDeptCode());
                insertOrUpdateList.add(dto);

                dto = new MatrixRequest.DataDTO();
                dto.setCode(N2);
                dto.setValue(matrixtable.getDirectormanagern2());
                dto.setType(matrixtable.getDeptCode());
                insertOrUpdateList.add(dto);

                dto = new MatrixRequest.DataDTO();
                dto.setCode(N3);
                dto.setValue(matrixtable.getManagersupervisorn3());
                dto.setType(matrixtable.getDeptCode());
                insertOrUpdateList.add(dto);

                matrixtable.setInsert(true);
                matrixRecordList.add(matrixtable);
            }
        }

        //临时的变量存储传输的数据
        log("insertOrUpdateList size = " + insertOrUpdateList.size());
        syncBatchMatrix(insertOrUpdateList, url, token);

        saveMatrixJl(matrixRecordList);
    }

    private void syncBatchMatrix(List<MatrixRequest.DataDTO> insertOrUpdateList, String url, String token) {
        List<MatrixRequest.DataDTO> insertOrUpdateListTemp = new ArrayList<>();
        for (int i = 0; i < insertOrUpdateList.size(); i++) {
            MatrixRequest.DataDTO dataDTO = insertOrUpdateList.get(i);
            insertOrUpdateListTemp.add(dataDTO);
            if (insertOrUpdateListTemp.size() == 199) {
                MatrixRequest request = new MatrixRequest();
                request.setData(insertOrUpdateListTemp);
                request.setBizId(getUUID());
                request.setTimestamp(getCurtTime());
                syncMatrixInterface(request, url, token);
                insertOrUpdateListTemp.clear();
            }
        }
        if (!insertOrUpdateListTemp.isEmpty()) {
            MatrixRequest request = new MatrixRequest();
            request.setData(insertOrUpdateListTemp);
            request.setBizId(getUUID());
            request.setTimestamp(getCurtTime());
            syncMatrixInterface(request, url, token);
        }
    }

    private Map<String, Matrixtable> getMatrixRecord() {
        Map<String, Matrixtable> map = new HashMap<>();
        String sql = "SELECT uuid,bmbm,departmentheadern1,directormanagern2,managersupervisorn3 FROM uf_tbjzjlb";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String uuid = Util.null2String(rs.getString("uuid"));
            String departmentcode = Util.null2String(rs.getString("bmbm"));
            String departmentheadern1 = Util.null2String(rs.getString("departmentheadern1"));
            String directormanagern2 = Util.null2String(rs.getString("directormanagern2"));
            String managersupervisorn3 = Util.null2String(rs.getString("managersupervisorn3"));

            Matrixtable matrixtable = new Matrixtable();
            matrixtable.setUuid(uuid);
            matrixtable.setDeptCode(departmentcode);
            matrixtable.setDepartmentheadern1(departmentheadern1);
            matrixtable.setDirectormanagern2(directormanagern2);
            matrixtable.setManagersupervisorn3(managersupervisorn3);
            map.put(uuid, matrixtable);
        }
        return map;
    }

    private List<Matrixtable> getMatrixList() {
        List<Matrixtable> list = new ArrayList<>();
        String sql = "SELECT uuid,(select hd.departmentcode from HrmDepartment hd where hd.id = m.id) departmentcode,(select h.workcode from HrmResource h where h.id = m.departmentheadern1) departmentheadern1,(select h.workcode from HrmResource h where h.id = m.directormanagern2) directormanagern2,(select h.workcode from HrmResource h where h.id = m.managersupervisorn3) managersupervisorn3 FROM Matrixtable_2 m";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String uuid = Util.null2String(rs.getString("uuid"));
            String departmentcode = Util.null2String(rs.getString("departmentcode"));
            String departmentheadern1 = Util.null2String(rs.getString("departmentheadern1"));
            String directormanagern2 = Util.null2String(rs.getString("directormanagern2"));
            String managersupervisorn3 = Util.null2String(rs.getString("managersupervisorn3"));

            Matrixtable matrixtable = new Matrixtable();
            matrixtable.setUuid(uuid);
            matrixtable.setDeptCode(departmentcode);
            matrixtable.setDepartmentheadern1(departmentheadern1);
            matrixtable.setDirectormanagern2(directormanagern2);
            matrixtable.setManagersupervisorn3(managersupervisorn3);
            list.add(matrixtable);
        }
        return list;
    }

    /**
     * 两个需求：
     * 1.流程提交时将数据同步给云简
     * 2.创建一个建模的查询页面，用户可以在此页面上选择需要同步的数据
     * 3.在流程中创建两个字段：是否同步,同步失败提示信息
     *
     * @param ids
     */
    public boolean syncCsr(String ids, String wfTbName) {
        boolean result = true;
        Map<String, String> systemParams = getSystemParams();
        String url = systemParams.get("8");
        String token = getToken(systemParams);
        List<Map<String, String>> hcpDataList = getCsrDataList(ids);
        for (Map<String, String> map : hcpDataList) {
            String id = map.get("id");

            CsrApplyRequest request = new CsrApplyRequest();
            CsrApplyRequest.DataDTO dto = new CsrApplyRequest.DataDTO();
            CsrApplyRequest.DataDTO.HeaderDTO headerDTO = new CsrApplyRequest.DataDTO.HeaderDTO();
            headerDTO.setExternalId(map.get("lcbh"));//流程编号
            headerDTO.setHeaderTypeCode("ZG_2_1");//单据类型编码
            headerDTO.setStatus("approved");//单据状态
            headerDTO.setChargeUserCode(map.get("sqr"));//申请人(传员工号）
            headerDTO.setColumn22(map.get("yggh"));//员工工号
            headerDTO.setSubmitDepartmentCode(map.get("sqbm"));//申请部门
            headerDTO.setBranchCode(map.get("sqfb"));//申请公司
            headerDTO.setDescription(map.get("requestname"));//标题
            headerDTO.setColumn27(map.get("yy"));//医院
            headerDTO.setStartDatetime(map.get("pxksrq"));//活动开始日期
            headerDTO.setEndDatetime(map.get("pxjsrq"));//活动结束日期
            headerDTO.setLongDescription(map.get("cypxrysm"));//参与培训人员说明
            headerDTO.setCreatedByCode(map.get("sqr"));//传员工工号
            dto.setHeader(headerDTO);
            request.setBizId(getUUID());
            request.setTimestamp(getCurtTime());
            request.setData(dto);
            HcpApplyResponse hcpApplyResponse = syncCsrInterface(request, url, token);
            if (hcpApplyResponse.getResCode() == 200000) {
                String sql = "update " + wfTbName + " set sftb = 0 where id = " + id;
                executeSql(sql);
            } else {
                result = false;
                String sql = "update " + wfTbName + " set sftb = 1,tbsbtsxx='" + hcpApplyResponse.getData().getError() + "' where id = " + id;
                executeSql(sql);
            }
        }
        return result;
    }

    /**
     * 在建模页面选择对应的数据，然后调用接口来同步数据给云简
     * 1.给hcp预申请建模中增加两个字段：是否同步,同步失败提示信息
     * 2.给hcp预申请流程中增加一个字段:是否同步
     * 3.当流程转数据时，默认值设置为否
     *
     * @param ids
     */
    public boolean syncHcp(String ids) {
        boolean result = true;
        Map<String, String> systemParams = getSystemParams();
        String url = systemParams.get("7");
        String modeUrl = systemParams.get("9");
        String token = getToken(systemParams);
        List<Map<String, String>> hcpDataList = getHcpDataList(ids);
        for (Map<String, String> map : hcpDataList) {
            String id = map.get("id");

            HcpApplyRequest request = new HcpApplyRequest();
            HcpApplyRequest.DataDTO dto = new HcpApplyRequest.DataDTO();
            HcpApplyRequest.DataDTO.HeaderDTO headerDTO = new HcpApplyRequest.DataDTO.HeaderDTO();
            headerDTO.setExternalId(map.get("lcbh"));//流程编号
            headerDTO.setHeaderTypeCode("ZG_1_1");//单据类型编码
            headerDTO.setStatus("approved");//单据状态
            headerDTO.setColumn21(map.get("hdmc"));//活动名称
            headerDTO.setChargeUserCode(map.get("sqr"));//申请人(传员工号）
            headerDTO.setColumn22(map.get("yggh"));//员工工号
            headerDTO.setSubmitDepartmentCode(map.get("sqbm"));//申请部门
            headerDTO.setBranchCode(map.get("sqfb"));//申请公司
            headerDTO.setTotalAmount(map.get("zje"));//总金额
            headerDTO.setColumn24(map.get("hcphdxz"));//HCP活动性质
            headerDTO.setStartDatetime(map.get("hdksrq"));//活动开始日期
            headerDTO.setEndDatetime(map.get("hdjsrq"));//活动结束日期
            headerDTO.setColumn26(map.get("hcphddd"));//HCP活动地点
            headerDTO.setChargeDepartmentCode(map.get("cbzxnew"));//成本中心
            String hdmc = map.get("hdmc");
            String rUrl = modeUrl.replace("${billid}", id);
            headerDTO.setColumn25("{\"text\": \"" + hdmc + "\",\"uri\": \"" + rUrl + "\"}");
            headerDTO.setCreatedByCode(map.get("sqr"));//传员工工号
            dto.setHeader(headerDTO);
            request.setBizId(getUUID());
            request.setTimestamp(getCurtTime());
            request.setData(dto);
            HcpApplyResponse hcpApplyResponse = syncHcpInterface(request, url, token);
            if (hcpApplyResponse.getResCode() == 200000) {
                String sql = "update uf_hdxz set sftb = 0 where id = " + id;
                log("sql = " + sql);
                executeSql(sql);
            } else {
                result = false;
                String sql = "update uf_hdxz set sftb = 1,tbsbtsxx='" + hcpApplyResponse.getResMsg() + "' where id = " + id;
                log("sql = " + sql);
                executeSql(sql);
            }
        }
        return result;
    }

    /**
     * 从uf_hdxz（HCP活动预申请）建模表中查询数据
     *
     * @param ids
     * @return
     */
    private List<Map<String, String>> getHcpDataList(String ids) {
        List<Map<String, String>> list = new ArrayList<>();
        String sql = "SELECT uh.id,uh.lcbh,uh.hdmc,(select hr.workcode from HrmResource hr where hr.id = uh.sqr) sqr,yggh,(select hd.departmentcode from HrmDepartment hd where hd.id = uh.sqbm) sqbm,(SELECT hs.subcompanycode FROM HrmSubCompany hs where hs.id = uh.sqfb) sqfb,uh.zjrmbzje zje,uh.hcphdxz,uh.hdksrq,uh.hdjsrq,uh.hcphddd,(SELECT uc.bh FROM uf_cbzx uc where uc.id = uh.cbzxnew)cbzxnew from uf_hdxz uh where uh.id in(" + ids + ")";
        log("getHcpDataList sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            Map<String, String> map = new HashMap<>();
            String[] columnNameArray = rs.getColumnName();
            for (String columnName : columnNameArray) {
                String value = Util.null2String(rs.getString(columnName));
                if ("hcphdxz".equals(columnName)) {
                    value = getHcphdxz(value);
                }
                map.put(columnName.toLowerCase(), value);
            }
            list.add(map);
        }
        return list;
    }

    /**
     * 查询活动性质表中的名称
     *
     * @param hcphdxzIds
     * @return
     */
    private String getHcphdxz(String hcphdxzIds) {
        StringJoiner sj = new StringJoiner(",");
        String sql = "SELECT hcphdxz FROM uf_HCPhdxz where id in(" + hcphdxzIds + ")";
        log("getHcphdxz sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
            String value = Util.null2String(rs.getString("hcphdxz"));
            sj.add(value);
        }
        return sj.toString();
    }

    public void executeSql(String sql) {
        log("executeSql sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);
    }

    private String filterStr(String bz) {
        if (bz == null || "".equals(bz)) {
            return bz;
        }

        bz = bz.replaceAll("<br>", " ");
        bz = bz.replaceAll("<br/>", " ");
        bz = bz.replaceAll("&nbsp;", " ");
        bz = bz.replaceAll("/n", "");
        return bz;
    }

    /**
     * 添加日志
     */
    private void addSyncInterfaceLog(int type, int tbzt, String tbsbxx,
                                     String cjcgdts, String gxcgdts) {
        StringBuilder sb = new StringBuilder();
        String sql = getSyncInterfaceLogSql(type, tbzt, tbsbxx, cjcgdts, gxcgdts, sb);
        log("addSyncInterfaceLog sql = " + sql);
        RecordSet rs = new RecordSet();
        rs.execute(sql);

        sql = "select id from uf_tbrz where modeuuid = '" + sb.toString() + "'";
        log("addSyncInterfaceLog select sql = " + sql);
        rs.execute(sql);
        int id = -1;
        if (rs.next()) {
            id = rs.getInt("id");
        }
        if (id != -1) {
            reconJur(1, id);
        }
    }

    /**
     * 重构权限
     */
    private void reconJur(int modedatacreater, int msnId) {
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        modeRightInfo.rebuildModeDataShareByEdit(modedatacreater, Integer.parseInt(getSyncInterfaceLogModeId()), msnId);
    }

    /**
     * 组装sql
     *
     * @param type    日志类型，0:部门,1:人员,2:矩阵
     * @param tbzt    同步状态
     * @param tbsbxx  同步失败信息
     * @param cjcgdts 创建成功的条数
     * @param gxcgdts 更新成功的条数
     * @return
     */
    private String getSyncInterfaceLogSql(int type, int tbzt, String tbsbxx,
                                          String cjcgdts, String gxcgdts, StringBuilder sbUuid) {
        FormmodeBaseBean fb = new FormmodeBaseBean();
        fb.setFormmodeid(Integer.parseInt(getSyncInterfaceLogModeId()));
        fb.setModedatacreater(1);
        StringBuilder sb = new StringBuilder();
        sb.append("insert into uf_tbrz(sjlx,tbzt,tbsbxx,cjcgdts,gxcgdts,")
                .append(fb.getFieldNames())
                .append(") ")
                .append("values(")
                .append(type).append(",")
                .append(tbzt).append(",")
                .append("'").append(tbsbxx).append("',")
                .append("'").append(cjcgdts).append("',")
                .append("'").append(gxcgdts).append("',")
                .append(fb.getFieldValues())
                .append(")");
        sbUuid.append(fb.getModeuuid());
        return sb.toString();
    }

}
