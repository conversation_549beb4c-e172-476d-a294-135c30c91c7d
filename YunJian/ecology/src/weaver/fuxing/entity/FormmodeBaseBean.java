package weaver.fuxing.entity;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.StringJoiner;
import java.util.UUID;

/**
 * 建模的basebean
 */
public class FormmodeBaseBean {

    private int formmodeid;
    private int modedatacreater;
    private int modedatacreatertype = 0;
    private String modedatacreatedate;
    private String modedatacreatetime;
    private String modeuuid;

    public FormmodeBaseBean() {

    }

    public int getFormmodeid() {
        return formmodeid;
    }

    public void setFormmodeid(int formmodeid) {
        this.formmodeid = formmodeid;
    }

    public int getModedatacreater() {
        return modedatacreater;
    }

    public void setModedatacreater(int modedatacreater) {
        this.modedatacreater = modedatacreater;
    }

    public int getModedatacreatertype() {
        return modedatacreatertype;
    }

    public void setModedatacreatertype(int modedatacreatertype) {
        this.modedatacreatertype = modedatacreatertype;
    }

    public String getModedatacreatedate() {
        return modedatacreatedate;
    }

    public void setModedatacreatedate(String modedatacreatedate) {
        this.modedatacreatedate = modedatacreatedate;
    }

    public String getModedatacreatetime() {
        return modedatacreatetime;
    }

    public void setModedatacreatetime(String modedatacreatetime) {
        this.modedatacreatetime = modedatacreatetime;
    }

    public String getModeuuid() {
        return modeuuid;
    }

    public void setModeuuid(String modeuuid) {
        this.modeuuid = modeuuid;
    }

    private String getCurtDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date curtDate = new Date();
        return sdf.format(curtDate);
    }

    /**
     * 获取字段名称
     *
     * @return
     */
    public String getFieldNames() {
        return "formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,modeuuid";
    }

    /**
     * 获取字段值
     *
     * @return
     */
    public String getFieldValues() {
        if (modedatacreatedate == null || "".equals(modedatacreatedate)) {
            String curtDate = getCurtDate();
            String[] curtDateSplit = curtDate.split(" ");
            modedatacreatedate = curtDateSplit[0];
            modedatacreatetime = curtDateSplit[1];
        }

        if (modeuuid == null || "".equals(modeuuid)) {
            modeuuid = UUID.randomUUID().toString();
        }

        StringJoiner sj = new StringJoiner(",");
        sj.add(String.valueOf(formmodeid));
        sj.add(String.valueOf(modedatacreater));
        sj.add(String.valueOf(modedatacreatertype));
        sj.add("'" + modedatacreatedate + "'");
        sj.add("'" + modedatacreatetime + "'");
        sj.add("'" + modeuuid + "'");
        return sj.toString();
    }
}
