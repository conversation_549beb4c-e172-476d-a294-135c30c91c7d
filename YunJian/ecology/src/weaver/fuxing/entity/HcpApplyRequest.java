package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
public class HcpApplyRequest {

    @JSONField(name = "bizId")
    private String bizId;
    @JSONField(name = "timestamp")
    private Long timestamp;
    @JSONField(name = "data")
    private DataDTO data;

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @JSONField(name = "header")
        private HeaderDTO header;

        public HeaderDTO getHeader() {
            return header;
        }

        public void setHeader(HeaderDTO header) {
            this.header = header;
        }

        public static class HeaderDTO {
            @JSONField(name = "branch_code")
            private String branchCode;
            @JSONField(name = "charge_department_code")
            private String chargeDepartmentCode;
            @JSONField(name = "column21")
            private String column21;
            @JSONField(name = "column22")
            private String column22;
            @JSONField(name = "column23")
            private String column23;
            @JSONField(name = "column24")
            private String column24;
            @JSONField(name = "column25")
            private String column25;
            @JSONField(name = "column26")
            private String column26;
            @JSONField(name = "external_id")
            private String externalId;
            @JSONField(name = "header_type_code")
            private String headerTypeCode;
            @JSONField(name = "status")
            private String status;
            @JSONField(name = "supplier_code")
            private String supplierCode;
            @JSONField(name = "submit_department_code")
            private String submitDepartmentCode;
            @JSONField(name = "total_amount")
            private String totalAmount;
            @JSONField(name = "charge_user_code")
            private String chargeUserCode;
            @JSONField(name = "start_datetime")
            private String startDatetime;
            @JSONField(name = "end_datetime")
            private String endDatetime;
            @JSONField(name = "created_by_code")
            private String createdByCode;

            public String getCreatedByCode() {
                return createdByCode;
            }

            public void setCreatedByCode(String createdByCode) {
                this.createdByCode = createdByCode;
            }

            public String getStartDatetime() {
                return startDatetime;
            }

            public void setStartDatetime(String startDatetime) {
                this.startDatetime = startDatetime;
            }

            public String getEndDatetime() {
                return endDatetime;
            }

            public void setEndDatetime(String endDatetime) {
                this.endDatetime = endDatetime;
            }

            public String getColumn26() {
                return column26;
            }

            public void setColumn26(String column26) {
                this.column26 = column26;
            }

            public String getChargeUserCode() {
                return chargeUserCode;
            }

            public void setChargeUserCode(String chargeUserCode) {
                this.chargeUserCode = chargeUserCode;
            }

            public String getTotalAmount() {
                return totalAmount;
            }

            public void setTotalAmount(String totalAmount) {
                this.totalAmount = totalAmount;
            }

            public String getSubmitDepartmentCode() {
                return submitDepartmentCode;
            }

            public void setSubmitDepartmentCode(String submitDepartmentCode) {
                this.submitDepartmentCode = submitDepartmentCode;
            }

            public String getBranchCode() {
                return branchCode;
            }

            public void setBranchCode(String branchCode) {
                this.branchCode = branchCode;
            }

            public String getChargeDepartmentCode() {
                return chargeDepartmentCode;
            }

            public void setChargeDepartmentCode(String chargeDepartmentCode) {
                this.chargeDepartmentCode = chargeDepartmentCode;
            }

            public String getColumn21() {
                return column21;
            }

            public void setColumn21(String column21) {
                this.column21 = column21;
            }

            public String getColumn22() {
                return column22;
            }

            public void setColumn22(String column22) {
                this.column22 = column22;
            }

            public String getColumn23() {
                return column23;
            }

            public void setColumn23(String column23) {
                this.column23 = column23;
            }

            public String getColumn24() {
                return column24;
            }

            public void setColumn24(String column24) {
                this.column24 = column24;
            }

            public String getColumn25() {
                return column25;
            }

            public void setColumn25(String column25) {
                this.column25 = column25;
            }

            public String getExternalId() {
                return externalId;
            }

            public void setExternalId(String externalId) {
                this.externalId = externalId;
            }

            public String getHeaderTypeCode() {
                return headerTypeCode;
            }

            public void setHeaderTypeCode(String headerTypeCode) {
                this.headerTypeCode = headerTypeCode;
            }

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public String getSupplierCode() {
                return supplierCode;
            }

            public void setSupplierCode(String supplierCode) {
                this.supplierCode = supplierCode;
            }

            public static class Column25DTO {
                @JSONField(name = "text")
                private String text;
                @JSONField(name = "url")
                private String url;

                public String getText() {
                    return text;
                }

                public void setText(String text) {
                    this.text = text;
                }

                public String getUrl() {
                    return url;
                }

                public void setUrl(String url) {
                    this.url = url;
                }
            }
        }
    }
}
