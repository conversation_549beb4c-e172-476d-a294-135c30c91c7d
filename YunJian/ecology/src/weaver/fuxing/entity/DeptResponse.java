package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * 部门接口响应实体
 *
 * <AUTHOR>
 * @date 2022/6/5
 */
public class DeptResponse {

    @JSONField(name = "resCode")
    private Integer resCode;
    @JSONField(name = "resMsg")
    private String resMsg;
    @JSONField(name = "bizId")
    private String bizId;
    @JSONField(name = "data")
    private DataDTO data;

    public Integer getResCode() {
        return resCode;
    }

    public void setResCode(Integer resCode) {
        this.resCode = resCode;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @JSONField(name = "validatorErrors")
        private List<ValidatorErrorsDTO> validatorErrors;
        @JSONField(name = "created")
        private String created;
        @JSONField(name = "warnings")
        private List<WarningsDTO> warnings;
        @JSONField(name = "dbErrors")
        private List<DbErrorsDTO> dbErrors;
        @JSONField(name = "updated")
        private String updated;

        public List<ValidatorErrorsDTO> getValidatorErrors() {
            return validatorErrors;
        }

        public void setValidatorErrors(List<ValidatorErrorsDTO> validatorErrors) {
            this.validatorErrors = validatorErrors;
        }

        public String getCreated() {
            return created;
        }

        public void setCreated(String created) {
            this.created = created;
        }

        public List<WarningsDTO> getWarnings() {
            return warnings;
        }

        public void setWarnings(List<WarningsDTO> warnings) {
            this.warnings = warnings;
        }

        public List<DbErrorsDTO> getDbErrors() {
            return dbErrors;
        }

        public void setDbErrors(List<DbErrorsDTO> dbErrors) {
            this.dbErrors = dbErrors;
        }

        public String getUpdated() {
            return updated;
        }

        public void setUpdated(String updated) {
            this.updated = updated;
        }

        public static class ValidatorErrorsDTO {
            @JSONField(name = "code")
            private String code;
            @JSONField(name = "messages")
            private List<String> messages;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public List<String> getMessages() {
                return messages;
            }

            public void setMessages(List<String> messages) {
                this.messages = messages;
            }
        }

        public static class WarningsDTO {
            @JSONField(name = "code")
            private String code;
            @JSONField(name = "messages")
            private List<String> messages;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public List<String> getMessages() {
                return messages;
            }

            public void setMessages(List<String> messages) {
                this.messages = messages;
            }
        }

        public static class DbErrorsDTO {
            @JSONField(name = "code")
            private String code;
            @JSONField(name = "messages")
            private List<String> messages;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public List<String> getMessages() {
                return messages;
            }

            public void setMessages(List<String> messages) {
                this.messages = messages;
            }
        }
    }
}
