package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSO<PERSON>ield;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 矩阵
 *
 * <AUTHOR>
 * @date 2022/6/9
 */
public class MatrixRequest {

    @JSONField(name = "bizId")
    private String bizId;
    @JSONField(name = "timestamp")
    private Long timestamp;
    @JSONField(name = "data")
    private List<DataDTO> data;

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @JSONField(name = "code")
        private String code;
        @JSONField(name = "value")
        private String value;
        @JSONField(name = "description")
        private String description;
        @JSONField(name = "type")
        private String type;
        @JSO<PERSON>ield(name = "column25")
        private String column25;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            if (StringUtils.hasLength(value)) {
                this.value = value;
            } else {
                this.value = "NULL";
            }
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            if (StringUtils.hasLength(type)) {
                this.type = type;
            } else {
                this.type = "NULL";
            }
        }

        public String getColumn25() {
            return column25;
        }

        public void setColumn25(String column25) {
            this.column25 = column25;
        }

        /**
         * 判断下，如果value和type都为空的话，不同步数据
         *
         * @return
         */
        public boolean canSync() {
            if (!StringUtils.hasLength(value)) {
                return false;
            }
            if (!StringUtils.hasLength(type)) {
                return false;
            }
            return true;
        }
    }
}
