package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2022/6/12
 */
public class HcpApplyResponse {

    @JSONField(name = "resCode")
    private Integer resCode;
    @JSONField(name = "resMsg")
    private String resMsg;
    @JSONField(name = "data")
    private DataDTO data;

    public Integer getResCode() {
        return resCode;
    }

    public void setResCode(Integer resCode) {
        this.resCode = resCode;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @JSONField(name = "error")
        private String error;
        @JSONField(name = "header")
        private HeaderDTO header;

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        public HeaderDTO getHeader() {
            return header;
        }

        public void setHeader(HeaderDTO header) {
            this.header = header;
        }

        public static class HeaderDTO {
            @JSONField(name = "status")
            private String status;
            @JSONField(name = "document_id")
            private Integer documentId;
            @JSONField(name = "document_num")
            private String documentNum;
            @JSONField(name = "column26")
            private String column26;
            @JSONField(name = "column27")
            private String column27;
            @JSONField(name = "column28")
            private String column28;
            @JSONField(name = "start_datetime")
            private String startDatetime;
            @JSONField(name = "end_datetime")
            private String endDatetime;
            @JSONField(name = "external_id")
            private String externalId;
            @JSONField(name = "header_type_code")
            private String headerTypeCode;
            @JSONField(name = "supplier_code")
            private String supplierCode;

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public Integer getDocumentId() {
                return documentId;
            }

            public void setDocumentId(Integer documentId) {
                this.documentId = documentId;
            }

            public String getDocumentNum() {
                return documentNum;
            }

            public void setDocumentNum(String documentNum) {
                this.documentNum = documentNum;
            }

            public String getColumn26() {
                return column26;
            }

            public void setColumn26(String column26) {
                this.column26 = column26;
            }

            public String getColumn27() {
                return column27;
            }

            public void setColumn27(String column27) {
                this.column27 = column27;
            }

            public String getColumn28() {
                return column28;
            }

            public void setColumn28(String column28) {
                this.column28 = column28;
            }

            public String getStartDatetime() {
                return startDatetime;
            }

            public void setStartDatetime(String startDatetime) {
                this.startDatetime = startDatetime;
            }

            public String getEndDatetime() {
                return endDatetime;
            }

            public void setEndDatetime(String endDatetime) {
                this.endDatetime = endDatetime;
            }

            public String getExternalId() {
                return externalId;
            }

            public void setExternalId(String externalId) {
                this.externalId = externalId;
            }

            public String getHeaderTypeCode() {
                return headerTypeCode;
            }

            public void setHeaderTypeCode(String headerTypeCode) {
                this.headerTypeCode = headerTypeCode;
            }

            public String getSupplierCode() {
                return supplierCode;
            }

            public void setSupplierCode(String supplierCode) {
                this.supplierCode = supplierCode;
            }
        }
    }
}
