package weaver.fuxing.entity;

/**
 * <AUTHOR>
 * @date 2022/6/10
 */
public class Matrixtable {
    private String uuid;
    private String deptCode;
    private String departmentheadern1;
    private String directormanagern2;
    private String managersupervisorn3;
    private boolean insert;//是否新增，如果是true代表是需要新增的数据

    public boolean isInsert() {
        return insert;
    }

    public void setInsert(boolean insert) {
        this.insert = insert;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDepartmentheadern1() {
        return departmentheadern1;
    }

    public void setDepartmentheadern1(String departmentheadern1) {
        this.departmentheadern1 = departmentheadern1;
    }

    public String getDirectormanagern2() {
        return directormanagern2;
    }

    public void setDirectormanagern2(String directormanagern2) {
        this.directormanagern2 = directormanagern2;
    }

    public String getManagersupervisorn3() {
        return managersupervisorn3;
    }

    public void setManagersupervisorn3(String managersupervisorn3) {
        this.managersupervisorn3 = managersupervisorn3;
    }

    /**
     * 比对下两个对象中的数据是否相等
     *
     * @return true:相同,false:有数据差异
     */
    public boolean same(Matrixtable matrixtable) {
        if (!deptCode.equals(matrixtable.getDeptCode())) {
            return false;
        }
        if (!departmentheadern1.equals(matrixtable.getDepartmentheadern1())) {
            return false;
        }
        if (!directormanagern2.equals(matrixtable.getDirectormanagern2())) {
            return false;
        }
        if (!managersupervisorn3.equals(matrixtable.getManagersupervisorn3())) {
            return false;
        }
        return true;
    }
}
