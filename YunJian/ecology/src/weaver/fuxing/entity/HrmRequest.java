package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/8
 */
public class HrmRequest {

    @JSONField(name = "bizId")
    private String bizId;
    @JSONField(name = "timestamp")
    private Long timestamp;
    @JSONField(name = "data")
    private List<DataDTO> data;

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @JSONField(name = "code")
        private String code;
        @JSONField(name = "full_name")
        private String fullName;
        @JSONField(name = "enabled_flag")
        private String enabledFlag;
        @JSONField(name = "department_code")
        private String departmentCode;
        @JSONField(name = "branch_code")
        private String branchCode;
        @JSONField(name = "base_city")
        private String baseCity;
        @JSONField(name = "email_address")
        private String emailAddress;
        @JSONField(name = "cost_center_code")
        private String costCenterCode;
        @JSONField(name = "level")
        private String level;
        @JSONField(name = "parent_code")
        private String parentCode;
        @JSONField(name = "column1")
        private String column1;
        @JSONField(name = "column30")
        private String column30;
        @JSONField(name = "column37")
        private String column37;

        public String getColumn37() {
            return column37;
        }

        public void setColumn37(String column37) {
            this.column37 = column37;
        }

        public String getParentCode() {
            return parentCode;
        }

        public void setParentCode(String parentCode) {
            this.parentCode = parentCode;
        }

        public String getColumn1() {
            return column1;
        }

        public void setColumn1(String column1) {
            this.column1 = column1;
        }

        public String getColumn30() {
            return column30;
        }

        public void setColumn30(String column30) {
            this.column30 = column30;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }

        public String getCostCenterCode() {
            return costCenterCode;
        }

        public void setCostCenterCode(String costCenterCode) {
            this.costCenterCode = costCenterCode;
        }

        public String getEmailAddress() {
            return emailAddress;
        }

        public void setEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getFullName() {
            return fullName;
        }

        public void setFullName(String fullName) {
            this.fullName = fullName;
        }

        public String getEnabledFlag() {
            return enabledFlag;
        }

        public void setEnabledFlag(String enabledFlag) {
            this.enabledFlag = enabledFlag;
        }

        public String getDepartmentCode() {
            return departmentCode;
        }

        public void setDepartmentCode(String departmentCode) {
            this.departmentCode = departmentCode;
        }

        public String getBranchCode() {
            return branchCode;
        }

        public void setBranchCode(String branchCode) {
            this.branchCode = branchCode;
        }

        public String getBaseCity() {
            return baseCity;
        }

        public void setBaseCity(String baseCity) {
            this.baseCity = baseCity;
        }
        
    }
}
