package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
public class CsrApplyRequest {

    @JSONField(name = "bizId")
    private String bizId;
    @JSONField(name = "timestamp")
    private Long timestamp;
    @JSONField(name = "data")
    private DataDTO data;

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @JSONField(name = "header")
        private HeaderDTO header;

        public HeaderDTO getHeader() {
            return header;
        }

        public void setHeader(HeaderDTO header) {
            this.header = header;
        }

        public static class HeaderDTO {
            @JSONField(name = "column22")
            private String column22;
            @JSONField(name = "column26")
            private String column26;
            @J<PERSON><PERSON>ield(name = "column27")
            private String column27;
            @JSO<PERSON>ield(name = "column28")
            private String column28;
            @JSONField(name = "start_datetime")
            private String startDatetime;
            @JSONField(name = "end_datetime")
            private String endDatetime;
            @JSONField(name = "external_id")
            private String externalId;
            @JSONField(name = "header_type_code")
            private String headerTypeCode;
            @JSONField(name = "status")
            private String status;
            @JSONField(name = "supplier_code")
            private String supplierCode;
            @JSONField(name = "charge_user_code")
            private String chargeUserCode;
            @JSONField(name = "submit_department_code")
            private String submitDepartmentCode;
            @JSONField(name = "branch_code")
            private String branchCode;
            @JSONField(name = "description")
            private String description;
            @JSONField(name = "long_description")
            private String longDescription;
            @JSONField(name = "created_by_code")
            private String createdByCode;

            public String getCreatedByCode() {
                return createdByCode;
            }

            public void setCreatedByCode(String createdByCode) {
                this.createdByCode = createdByCode;
            }

            public String getColumn22() {
                return column22;
            }

            public void setColumn22(String column22) {
                this.column22 = column22;
            }

            public String getChargeUserCode() {
                return chargeUserCode;
            }

            public void setChargeUserCode(String chargeUserCode) {
                this.chargeUserCode = chargeUserCode;
            }

            public String getSubmitDepartmentCode() {
                return submitDepartmentCode;
            }

            public void setSubmitDepartmentCode(String submitDepartmentCode) {
                this.submitDepartmentCode = submitDepartmentCode;
            }

            public String getBranchCode() {
                return branchCode;
            }

            public void setBranchCode(String branchCode) {
                this.branchCode = branchCode;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getLongDescription() {
                return longDescription;
            }

            public void setLongDescription(String longDescription) {
                this.longDescription = longDescription;
            }

            public String getColumn26() {
                return column26;
            }

            public void setColumn26(String column26) {
                this.column26 = column26;
            }

            public String getColumn27() {
                return column27;
            }

            public void setColumn27(String column27) {
                this.column27 = column27;
            }

            public String getColumn28() {
                return column28;
            }

            public void setColumn28(String column28) {
                this.column28 = column28;
            }

            public String getStartDatetime() {
                return startDatetime;
            }

            public void setStartDatetime(String startDatetime) {
                this.startDatetime = startDatetime;
            }

            public String getEndDatetime() {
                return endDatetime;
            }

            public void setEndDatetime(String endDatetime) {
                this.endDatetime = endDatetime;
            }

            public String getExternalId() {
                return externalId;
            }

            public void setExternalId(String externalId) {
                this.externalId = externalId;
            }

            public String getHeaderTypeCode() {
                return headerTypeCode;
            }

            public void setHeaderTypeCode(String headerTypeCode) {
                this.headerTypeCode = headerTypeCode;
            }

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public String getSupplierCode() {
                return supplierCode;
            }

            public void setSupplierCode(String supplierCode) {
                this.supplierCode = supplierCode;
            }
        }
    }
}
