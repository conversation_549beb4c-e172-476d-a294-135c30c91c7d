package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * 部门接口请求实体
 *
 * <AUTHOR>
 * @date 2022/6/5
 */
public class DeptRequest {

    @JSONField(name = "bizId")
    private String bizId;
    @JSONField(name = "timestamp")
    private Long timestamp;
    @JSONField(name = "data")
    private List<DataDTO> data;

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @J<PERSON>NField(name = "supervisor")
        private String supervisor;
        @JSONField(name = "type")
        private String type;
        @JSONField(name = "code")
        private String code;
        @JSONField(name = "parent_code")
        private String parentCode;
        @JSONField(name = "department_name")
        private String departmentName;
        @JSONField(name = "cost_center_flag")
        private String costCenterFlag;
        @JSONField(name = "budget_flag")
        private String budgetFlag;
        @JSONField(name = "enabled_flag")
        private String enabledFlag;

        public String getSupervisor() {
            return supervisor;
        }

        public void setSupervisor(String supervisor) {
            this.supervisor = supervisor;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getParentCode() {
            return parentCode;
        }

        public void setParentCode(String parentCode) {
            this.parentCode = parentCode;
        }

        public String getDepartmentName() {
            return departmentName;
        }

        public void setDepartmentName(String departmentName) {
            this.departmentName = departmentName;
        }

        public String getCostCenterFlag() {
            return costCenterFlag;
        }

        public void setCostCenterFlag(String costCenterFlag) {
            this.costCenterFlag = costCenterFlag;
        }

        public String getBudgetFlag() {
            return budgetFlag;
        }

        public void setBudgetFlag(String budgetFlag) {
            this.budgetFlag = budgetFlag;
        }

        public String getEnabledFlag() {
            return enabledFlag;
        }

        public void setEnabledFlag(String enabledFlag) {
            this.enabledFlag = enabledFlag;
        }
    }
}
