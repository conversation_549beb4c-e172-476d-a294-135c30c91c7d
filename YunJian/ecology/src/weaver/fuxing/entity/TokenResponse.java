package weaver.fuxing.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 获取token接口的返回实体类
 */
public class TokenResponse {

    @JSONField(name = "resCode")
    private Integer resCode;
    @JSONField(name = "resMsg")
    private String resMsg;
    @JSONField(name = "data")
    private DataDTO data;

    public Integer getResCode() {
        return resCode;
    }

    public void setResCode(Integer resCode) {
        this.resCode = resCode;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @JSONField(name = "access_token")
        private String accessToken;
        @JSONField(name = "scope")
        private String scope;
        @JSONField(name = "token_type")
        private String tokenType;
        @JSONField(name = "expires_in")
        private Integer expiresIn;

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getScope() {
            return scope;
        }

        public void setScope(String scope) {
            this.scope = scope;
        }

        public String getTokenType() {
            return tokenType;
        }

        public void setTokenType(String tokenType) {
            this.tokenType = tokenType;
        }

        public Integer getExpiresIn() {
            return expiresIn;
        }

        public void setExpiresIn(Integer expiresIn) {
            this.expiresIn = expiresIn;
        }
    }
}
