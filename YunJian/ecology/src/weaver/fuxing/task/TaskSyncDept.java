package weaver.fuxing.task;

import org.springframework.util.StringUtils;
import weaver.fuxing.service.YunJianService;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 定时同步组织架构
 */
public class TaskSyncDept extends BaseCronJob {

    private String openLog = "true";//true:打开日志,false:关闭日志
    private BaseBean baseBean = new BaseBean();
    private String syncTip;//同步标记,1:同步部门,2:同步人员,3:同步矩阵,参数值示例：1,2,3
    private String modeId;//同步接口日志建模id

    @Override
    public void execute() {
        try {

            if (!StringUtils.hasLength(getModeId())) {
                log("modeId is null");
                return;
            }

            YunJianService service = new YunJianService();
            service.setSyncInterfaceLogModeId(getModeId());
            Map<String, String> systemParams = service.getSystemParams();
            String token = service.getToken(systemParams);
            if (!StringUtils.hasLength(token)) {
                log("token is null");
                return;
            }

            if (StringUtils.hasLength(syncTip)) {
                String[] syncTipSplit = syncTip.split(",");
                List<String> syncTipList = Arrays.asList(syncTipSplit);
                if (syncTipList.contains("1")) {
                    service.syncDept(systemParams, token);
                }
                if (syncTipList.contains("2")) {
                    service.syncHrm(systemParams, token);
                }
                if (syncTipList.contains("3")) {
                    service.syncMatrix(systemParams, token);
                }
            } else {
                service.syncDept(systemParams, token);
                service.syncHrm(systemParams, token);
                service.syncMatrix(systemParams, token);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log("task execute exception");
        }
    }

    private void log(String msg) {
        String className = this.getClass().getSimpleName();
        if ("true".equals(openLog)) {
            baseBean.writeLog(className + " " + msg);
        }
    }

    public String getSyncTip() {
        return syncTip;
    }

    public void setSyncTip(String syncTip) {
        this.syncTip = syncTip;
    }

    public String getOpenLog() {
        return openLog;
    }

    public void setOpenLog(String openLog) {
        this.openLog = openLog;
    }

    public String getModeId() {
        return modeId;
    }

    public void setModeId(String modeId) {
        this.modeId = modeId;
    }
}
