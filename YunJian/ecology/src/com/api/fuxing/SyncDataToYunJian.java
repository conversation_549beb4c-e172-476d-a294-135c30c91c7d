package com.api.fuxing;

import com.weaver.general.Util;
import net.sf.json.JSONObject;
import org.springframework.util.StringUtils;
import weaver.fuxing.service.YunJianService;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * 同步数据给云简
 * 1.当用户选择建模查询页面上的数据后，将选中的数据通过接口同步给云简
 * 2.此接口主要是两个功能，第一个是同步HCP的数据，第二个是同步SCR的数据
 */
@Path("/fuxing")
public class SyncDataToYunJian {

    @POST
    @Path("/syncHcpData")
    @Produces(MediaType.TEXT_PLAIN)
    public String syncHcpData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        JSONObject joResult = new JSONObject();
        String ids = Util.null2String(request.getParameter("ids"));
        log("ids = " + ids);
        if (!StringUtils.hasLength(ids)) {
            joResult.put("code", "601");
            joResult.put("msg", "请先选择数据");
            return joResult.toString();
        }

        YunJianService service = new YunJianService();
        if (service.syncHcp(ids)) {
            joResult.put("code", "600");
            joResult.put("msg", "同步成功");
            return joResult.toString();
        } else {
            joResult.put("code", "602");
            joResult.put("msg", "同步失败");
        }

        return joResult.toString();
    }

    @POST
    @Path("/syncCsrData")
    @Produces(MediaType.TEXT_PLAIN)
    public String syncCsrData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        JSONObject joResult = new JSONObject();
        String ids = Util.null2String(request.getParameter("ids"));
        String wfTbName = Util.null2String(request.getParameter("wfTbName"));//流程表单名称
        log("ids = " + ids);
        log("wfTbName = " + wfTbName);
        if (!StringUtils.hasLength(ids)) {
            joResult.put("code", "601");
            joResult.put("msg", "请先选择数据");
            return joResult.toString();
        }

        YunJianService service = new YunJianService();
        if (service.syncCsr(ids, wfTbName)) {
            joResult.put("code", "600");
            joResult.put("msg", "同步成功");
            return joResult.toString();
        } else {
            joResult.put("code", "602");
            joResult.put("msg", "同步失败");
        }

        return joResult.toString();
    }

    public void log(String msg) {
        String className = this.getClass().getSimpleName();
        new BaseBean().writeLog(className + " " + msg);
    }
}
