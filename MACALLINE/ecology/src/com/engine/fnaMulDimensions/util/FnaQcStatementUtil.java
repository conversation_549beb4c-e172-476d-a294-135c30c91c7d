/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.fnaMulDimensions.util;

import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import weaver.conn.RecordSet;
import weaver.fna.general.FnaCommon;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.company.DepartmentComInfo;
import weaver.hrm.resource.ResourceComInfo;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * Title: FnaQcStatementUtil
 *
 * <AUTHOR>
 * @date 2020年12月24日
 * Description:
 */
public class FnaQcStatementUtil {
    DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###,###,###,###,###,###,###,###,###,##0.00");
    DecimalFormat df2 = new DecimalFormat("###,###,###,###,###,###,###,###,###,###,###,###,###,###,###,###,##0");
    public String getbgys(String csys, String zys) {
        double amount = Util.getDoubleValue(zys, 0.00) - Util.getDoubleValue(csys, 0.00);
        return df.format(amount);
    }

    public String getkysu(String zys, String spzyfs) {
        String[] otherparaArray = Util.splitString(spzyfs, "+");

        double amount = Util.getDoubleValue(zys, 0.00) - Util.getDoubleValue(otherparaArray[0], 0.00) - Util.getDoubleValue(otherparaArray[1], 0.00);
        return df.format(amount);
    }

    public String getamountdf(String amount) {
        return df.format(Util.getDoubleValue(amount, 0.00));
    }

    public String getxmmc(String fkvarchar, String numcode) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select a.fkName from FnaDimensionMember_" + numcode + " a "
                + " join FnaBudgetDimension_" + numcode + " b on a.dimensionId = b.id "
                + " where a.fkVarchar = ? and b.typeId = 1", fkvarchar);
        if (rSet.next()) {
            return Util.null2String(rSet.getString("fkName"));
        }
        return "";
    }


    public String getMemberName(String fkvarcharId, String numcode) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select a.subjectname from FnaBudgetSubject_" + numcode + " a "
                + " where a.id = ?", fkvarcharId);
        if (rSet.next()) {
            return Util.null2String(rSet.getString("subjectname"));
        }
        return "";
    }


    public String getsubjectCode(String fkvarcharId, String numcode) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select a.accountCode from FnaBudgetSubject_" + numcode + " a "
                + " where a.id = ?", fkvarcharId);
        String accountCode = "";
        if (rSet.next()) {
            accountCode = Util.null2String(rSet.getString("accountCode"));
        }
        if (!"".equals(accountCode)) {
            String subjectId = accountCode.split("_")[3];
            rSet.executeQuery("select a.bm from uf_km a "
                    + " where a.id = ?", subjectId);
            if (rSet.next()) {
                return Util.null2String(rSet.getString("bm"));
            }
        }
        return "";
    }


    public String getxmmc(String xmid) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select a.xmmc from uf_xmxx a "
                + " where a.id = ?", xmid);
        if (rSet.next()) {
            return Util.null2String(rSet.getString("xmmc"));
        }
        return "";
    }


    public String gethtlx(String htlx) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select selectname from workflow_selectitem "
                + " where fieldid = 62230	and selectvalue = ?", htlx);
        if (rSet.next()) {
            return Util.null2String(rSet.getString("selectname"));
        }
        return "";
    }

    public String getSubjectNameBrowserjm(String fieldValue) {
        BaseBean baseBean = new BaseBean();
        String result = "";
        new BaseBean().writeLog("fieldValue:" + fieldValue);
        try {
            RecordSet rSet = new RecordSet();
            rSet.executeQuery("select km from uf_htxx_dt1 where mainId = ? ", fieldValue);

            while (rSet.next()) {
                String km = Util.null2String(rSet.getString("km"));
                new BaseBean().writeLog("km:" + km);
                String kmmc = "";
                String isallsubjctname = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QC96925", "isallsubjctname")).getBytes("ISO-8859-1"), "gbk")).trim();
                new BaseBean().writeLog("isallsubjctname:" + isallsubjctname);
                if ("1".equals(isallsubjctname)) { //显示所有上级科目
                    RecordSet rs = new RecordSet();
                    String kmmc1 = "";
                    rs.executeQuery("select * from fnaaccountDtl where tableType = ? ", FnaAccTypeConstant.BUDGET_SUBJECT);
                    while (rs.next()) {
                        String tableName = Util.null2String(rs.getString("tableName"));
                        new BaseBean().writeLog("tableName:" + tableName);
                        if ((!"".equals(tableName)) && ("".equals(kmmc1))) {
                            try {
                                kmmc1 = FnaSubjectQcBrowserUtil.getsupsubjctName(tableName, km);
                                new BaseBean().writeLog("kmmc1:" + kmmc1);
                            } catch (Exception e) {
                                new BaseBean().writeLog(e.getMessage());
                            }

                        }
                    }
                    kmmc = kmmc1;
                } else {
                    kmmc = new BudgetSubjectComInfo().getSubjectname(km);
                    new BaseBean().writeLog("kmmc:" + kmmc);
                }
                if (!"".equals(kmmc)) {
                    if (!"".equals(result)) {
                        result += ",";
                    }
                    result += kmmc;
                }
            }


        } catch (Exception e) {
            // TODO: handle exception
        }
        return result;
    }

    public String getgys(String gysid) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select a.gys from uf_gysxx a "
                + " where a.id = ?", gysid);
        if (rSet.next()) {
            return Util.null2String(rSet.getString("gys"));
        }
        return "";
    }

    public String getjjfs(String jjfs) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select selectname from workflow_selectitem "
                + " where fieldid = 65463	and selectvalue = ?", jjfs);
        if (rSet.next()) {
            return Util.null2String(rSet.getString("selectname"));
        }
        return "";
    }

    public String getsfjs(String sfjs) {
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select selectname from workflow_selectitem "
                + " where fieldid = 65464	and selectvalue = ?", sfjs);
        if (rSet.next()) {
            return Util.null2String(rSet.getString("selectname"));
        }
        return "";
    }

    public String getbfs(String bl) {
        return df2.format(Util.getDoubleValue(bl, 0.00) * 100) + "%";

    }


    public String getsqr(String sqr) {
        ResourceComInfo resourceComInfo;
        try {
            resourceComInfo = new ResourceComInfo();
            return resourceComInfo.getLastname(sqr);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return "";
    }

    public String getsqbm(String sqbm) throws Exception {
        DepartmentComInfo departmentComInfo = new DepartmentComInfo();
        return departmentComInfo.getDepartmentName(sqbm);
    }

    public String getlcmc(String lc) throws Exception {
        RecordSet rs = new RecordSet();
        String splitCol = ",";
        String sql;
        StringBuilder result;
        List<String> requestName = new ArrayList<>();
        if(lc.contains(splitCol)){
            for(String str : lc.split(splitCol)){
                sql = "select requestName from workflow_requestbase where requestid =  ? "  ;
                rs.executeQuery(sql,str);
                if (rs.next()) {
                    requestName.add(Util.null2String(rs.getString("requestName")).trim());
                }
            }
        }else{
            sql = "select requestName from workflow_requestbase where requestid =  " + lc;
            rs.executeQuery(sql);
            if (rs.next()) {
                requestName.add(Util.null2String(rs.getString("requestName")).trim());
            }
        }
        if(!lc.contains(splitCol)){
            result = new StringBuilder();
            result.append("<span>");
            result.append("1.");
            result.append("</span>");
            result.append(" <a target=\"_blank\" href=\"/workflow/request/ViewRequest.jsp?requestid=");
            result.append( Util.getIntValue(lc)).append("\">");
            result.append(FnaCommon.escapeHtml(Util.null2String(requestName.get(0))));
            result.append("</a>");
        }else{
            result = new StringBuilder(" <div> ");
            int i = 0;
            for(String str : lc.split(splitCol)){
                if(i>0){
                    result.append("<br/>");
                }
                result.append("<span>");
                result.append(i+1);
                result.append(".");
                result.append("</span>");
                result.append(" <a target=\"_blank\" href=\"/workflow/request/ViewRequest.jsp?requestid=");
                result.append(str).append("\">");
                result.append(FnaCommon.escapeHtml(Util.null2String(requestName.get(i))));
                result.append("</a>");
                i++;
            }
            result.append("</div>");
        }

        return result.toString();

    }

    public String getswzfwcje(String sjjsje, String otherparam) {
        String[] otherparamarray = Util.splitString(otherparam, "+");
        double sjjsjedo = Util.getDoubleValue(sjjsje, 0.00);
        double cwsjzfje = 0.00;
        if (otherparamarray.length == 2) {
            cwsjzfje = Util.getDoubleValue(otherparamarray[1], 0.00);
        }

        if (sjjsjedo != 0) {
            double amount = sjjsjedo - cwsjzfje;
            return df.format(amount);
        } else {
            double ygjsje = Util.getDoubleValue(otherparamarray[0], 0.00);
            double amount = ygjsje - cwsjzfje;
            return df.format(amount);
        }
    }

    public String getljzfce(String sjjsje, String otherparam) {
        double amount = Util.getDoubleValue(sjjsje, 0.00) - Util.getDoubleValue(otherparam, 0.00);
        return df.format(amount);
    }


    public String bs(String param, String subjectLeve) {

        if ("1级".equals(subjectLeve)) {
            return "<font color = \"red\">" + param + "</font>";
        }
        if ("2级".equals(subjectLeve)) {
            return "<font color = \"blue\">" + param + "</font>";
        }
        if ("3级".equals(subjectLeve)) {
            return "<font color = \"orange\">" + param + "</font>";
        }
        return param;
    }

    public String getamountdfbs(String amount, String subjectLeve) {
        String dfamount = df.format(Util.getDoubleValue(amount, 0.00));
        if ("1级".equals(subjectLeve)) {
            return "<font color = \"red\">" + dfamount + "</font>";
        }
        if ("2级".equals(subjectLeve)) {
            return "<font color = \"blue\">" + dfamount + "</font>";
        }
        if ("3级".equals(subjectLeve)) {
            return "<font color = \"orange\">" + dfamount + "</font>";
        }
        return dfamount;

    }

    public String getxmmcbs(String fkvarchar, String param) {
        String[] otherparamarray = Util.splitString(param, "+");
        RecordSet rSet = new RecordSet();
        rSet.executeQuery("select a.fkName from FnaDimensionMember_" + otherparamarray[0] + " a "
                + " join FnaBudgetDimension_" + otherparamarray[0] + " b on a.dimensionId = b.id "
                + " where a.fkVarchar = ? and b.typeId = 1", fkvarchar);
        if (rSet.next()) {
            String xmmc = Util.null2String(rSet.getString("fkName"));
            if ("1级".equals(otherparamarray[1])) {
                return "<font color = \"red\">" + xmmc + "</font>";
            }
            if ("2级".equals(otherparamarray[1])) {
                return "<font color = \"blue\">" + xmmc + "</font>";
            }
            if ("3级".equals(otherparamarray[1])) {
                return "<font color = \"orange\">" + xmmc + "</font>";
            }
            return xmmc;
        }
        return "";
    }

    public String bmbsbs(String param, String subjectLeve) {

        if ("1级".equals(subjectLeve)) {
            return "<font color = \"red\">" + param + "</font>";
        }
        if ("2级".equals(subjectLeve)) {
            return "<font color = \"blue\">" + param + "</font>";
        }
        if ("3级".equals(subjectLeve)) {
            return "<font color = \"orange\">" + param + "</font>";
        } else {
            return "◆";
        }

    }


}
