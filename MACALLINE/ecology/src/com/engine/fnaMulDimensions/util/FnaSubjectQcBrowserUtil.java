/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.fnaMulDimensions.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;

import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import com.weaver.general.TimeUtil;
import weaver.hrm.resource.ResourceComInfo;

/**  
* Title: FnaSubjectQcBrowserUtil  
* <AUTHOR>  
* @date 2020年11月19日  
* Description:  
*/
public class FnaSubjectQcBrowserUtil {
	
	
	/**
	 * 
	 * @Title: getSubjectNameBrowser
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param fieldValue
	 * @param @param requestId
	 * @param @return 参数
	 * @return String 返回类型
	 * @throws
	 */
	public static String getSubjectNameBrowserjm(String fieldValue){
		BaseBean baseBean = new BaseBean();
		try{
			String isallsubjctname = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QC96925", "isallsubjctname")).getBytes("ISO-8859-1"), "gbk")).trim();
			if("1".equals(isallsubjctname)){ //显示所有上级科目
				int workflowid = 0;
				RecordSet rs = new RecordSet();
				BudgetSubjectComInfo budgetSubjectComInfo = new BudgetSubjectComInfo();
		        
		        rs.executeQuery("select * from fnaaccountDtl where tableType = ? ", FnaAccTypeConstant.BUDGET_SUBJECT);
		        while(rs.next()){
		        	String tableName = Util.null2String(rs.getString("tableName"));
		        	if(!"".equals(tableName)){
		        		String result = getsupsubjctName(tableName,fieldValue);
		        		if(!"".equals(result)){
		        			return result;
		        		}
			        }
		        }
			}else{
				return new BudgetSubjectComInfo().getSubjectname(fieldValue);
			}
		}catch (Exception e) {
			// TODO: handle exception
		}
		return "";
	}
	
	
	
	public static String getSubjectNameBrowser(String fieldValue,int requestId){
		BaseBean baseBean = new BaseBean();
		try{
			String isallsubjctname = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QC96925", "isallsubjctname")).getBytes("ISO-8859-1"), "gbk")).trim();
			if("1".equals(isallsubjctname)){ //显示所有上级科目
				
					return getSubjectNameBrowserjm(fieldValue);
				
				/*int workflowid = 0;
				RecordSet rs = new RecordSet();
				BudgetSubjectComInfo budgetSubjectComInfo = new BudgetSubjectComInfo();
				rs.executeQuery(" select a.workflowid from workflow_requestbase a" +
		                " where a.requestid = ?",requestId);

		        if(rs.next()){
		            workflowid = Util.getIntValue(rs.getString("workflowid"));
		        }else{
		            return "";
		        }
		        String accountId = getAccountInfo(""+workflowid,requestId);
		        String tableName = FnaBrowserUtils.getTableName(accountId, FnaAccTypeConstant.BUDGET_SUBJECT);
		        if("".equals(tableName)){
		        	return "";
		        }
		        return getsupsubjctName(tableName,fieldValue);*/
		        
			}else{
				return new BudgetSubjectComInfo().getSubjectname(fieldValue);
			}
		}catch (Exception e) {
			// TODO: handle exception
		}
		return "";
	}
	public static String getSubjectNameBrowser2(String fieldValue,String accountId){
		BaseBean baseBean = new BaseBean();
		try{
			String isallsubjctname = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QC96925", "isallsubjctname")).getBytes("ISO-8859-1"), "gbk")).trim();
			if("1".equals(isallsubjctname)){ //显示所有上级科目
		        String tableName = FnaBrowserUtils.getTableName(accountId, FnaAccTypeConstant.BUDGET_SUBJECT);
		        if("".equals(tableName)){
		        	return "";
		        }
		        return getsupsubjctName(tableName,fieldValue);
		        
			}else{
				return new BudgetSubjectComInfo().getSubjectname(fieldValue);
			}
		}catch (Exception e) {
			// TODO: handle exception
		}
		return "";
	}
	
	
	public static String getsupsubjctName(String tablename,String subjectId){
		Map<Integer,Integer> codeMap = new SubjectUtil().getSubjectCodeRule();
		int first_old = codeMap.get(1);
    	int second_old = codeMap.get(2);
    	int third_old = codeMap.get(3);
    	int fourth_old = codeMap.get(4);
    	int fifth_old = codeMap.get(5);
    	int sixth_old = codeMap.get(6);
    	int seventh_old = codeMap.get(7);
    	int eight_old = codeMap.get(8);
    	String supsubjectCode = "";
    	RecordSet rs = new RecordSet();
    	String sql1 = " select a.subjectCode,a.subjectName,a.subjectLevel from " + tablename + " a "+
                " where a.id = ? "+
                " ORDER BY a.subjectlevel,a.subjectcode,a.displayOrder,a.subjectname ";
    	rs.executeQuery(sql1,subjectId);
    	while(rs.next()){
			String sup1 = "-1";
			String sup2 = "";
			String sup3 = "";
			String sup4 = "";
			String sup5 = "";
			String sup6 = "";
			String sup7 = "";
			String sup8 = "";
    		int subjectLevel = Util.getIntValue(rs.getString("subjectLevel"));
            String _subjectCode = rs.getString("subjectCode");
    		if(subjectLevel == 2){
    			//sup1 = _subjectCode.substring(0,first_old);
    			supsubjectCode+="'"+sup1+"'";
    		}else if(subjectLevel == 3){
    			//sup1 = _subjectCode.substring(0,first_old);
    			sup2 = _subjectCode.substring(0,first_old+second_old);
    			supsubjectCode+="'"+sup1+"',"+"'"+sup2+"'";
    		}else if(subjectLevel == 4){
    			//sup1 = _subjectCode.substring(0,first_old);
    			sup2 = _subjectCode.substring(0,first_old+second_old);
    			sup3 = _subjectCode.substring(0,first_old+second_old+third_old);
    			supsubjectCode+="'"+sup1+"',"
    							+"'"+sup2+"',"
    							+"'"+sup3+"'";
    		}else if(subjectLevel == 5){
    			//sup1 = _subjectCode.substring(0,first_old);
    			sup2 = _subjectCode.substring(0,first_old+second_old);
    			sup3 = _subjectCode.substring(0,first_old+second_old+third_old);
    			sup4 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old);
    			supsubjectCode+="'"+sup1+"',"
						+"'"+sup2+"',"
						+"'"+sup3+"',"
						+"'"+sup4+"'";
    		}else if(subjectLevel == 6){
    			//sup1 = _subjectCode.substring(0,first_old);
    			sup2 = _subjectCode.substring(0,first_old+second_old);
    			sup3 = _subjectCode.substring(0,first_old+second_old+third_old);
    			sup4 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old);
    			sup5 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old);
    			supsubjectCode+="'"+sup1+"',"
						+"'"+sup2+"',"
						+"'"+sup3+"',"
						+"'"+sup4+"',"
						+"'"+sup5+"'";
    		}else if(subjectLevel == 7){
    			//sup1 = _subjectCode.substring(0,first_old);
    			sup2 = _subjectCode.substring(0,first_old+second_old);
    			sup3 = _subjectCode.substring(0,first_old+second_old+third_old);
    			sup4 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old);
    			sup5 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old);
    			sup6 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old+sixth_old);
    			supsubjectCode+="'"+sup1+"',"
						+"'"+sup2+"',"
						+"'"+sup3+"',"
						+"'"+sup4+"',"
						+"'"+sup5+"',"
						+"'"+sup6+"'";
    		}else if(subjectLevel == 8){
    			//sup1 = _subjectCode.substring(0,first_old);
    			sup2 = _subjectCode.substring(0,first_old+second_old);
    			sup3 = _subjectCode.substring(0,first_old+second_old+third_old);
    			sup4 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old);
    			sup5 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old);
    			sup6 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old+sixth_old);
    			sup7 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old+sixth_old+seventh_old);
    			supsubjectCode+="'"+sup1+"',"
						+"'"+sup2+"',"
						+"'"+sup3+"',"
						+"'"+sup4+"',"
						+"'"+sup5+"',"
						+"'"+sup6+"',"
						+"'"+sup7+"'";
    		}else if(subjectLevel == 9){
    			//sup1 = _subjectCode.substring(0,first_old);
    			sup2 = _subjectCode.substring(0,first_old+second_old);
    			sup3 = _subjectCode.substring(0,first_old+second_old+third_old);
    			sup4 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old);
    			sup5 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old);
    			sup6 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old+sixth_old);
    			sup7 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old+sixth_old+seventh_old);
    			sup8 = _subjectCode.substring(0,first_old+second_old+third_old+fourth_old+fifth_old+sixth_old+seventh_old+eight_old);
    			supsubjectCode+="'"+sup1+"',"
						+"'"+sup2+"',"
						+"'"+sup3+"',"
						+"'"+sup4+"',"
						+"'"+sup5+"',"
						+"'"+sup6+"',"
						+"'"+sup7+"',"
						+"'"+sup8+"'";
    		}
    		if(!"".equals(supsubjectCode)){
    			supsubjectCode+=",";
    		}
    		supsubjectCode+="'"+_subjectCode+"'";
        }
    	String supsubjectName = "";
    	rs.executeQuery("select subjectName from "+tablename+"  where subjectCode in("+supsubjectCode+") order by subjectlevel");
    	while(rs.next()){
    		if(!"".equals(supsubjectName)){
    			supsubjectName+="/";
    		}
    		supsubjectName+=Util.null2String(rs.getString("subjectName"));
    	}
    	return supsubjectName;
	}
	
	
	
	
	
	/**
	 * 获取账套信息
	 * @param params
	 * @return
	 */
	public static String getAccountInfo(String wfid,int requestid){
	    
	    //String wfid = Util.null2String(params.get("wfid"));
        //int requestid = Util.getIntValue(Util.null2String(params.get("requestid")));
	    
		RecordSet rs = new RecordSet();
		String workflowtablename = "";
		int formId = 0;
		rs.executeQuery("select b.id,b.TABLENAME from workflow_base a join workflow_bill b on a.FORMID = b.ID where a.id = ?",wfid);
		if(rs.next()){
			workflowtablename = Util.null2String(rs.getString("TABLENAME"));
			formId = Util.getIntValue(rs.getString("id"));
		}
		
		List<Map<String, String>> billfieldList = new ArrayList<Map<String, String>>();
		//获取流程主表字段信息
		rs.executeQuery("select id,fieldname from workflow_billfield  where billid =? and viewtype = 0 ", formId);
		while(rs.next()){
			String fieldid = Util.null2String(rs.getString("id"));
			String fieldName = Util.null2String(rs.getString("fieldname"));
			Map<String, String> map = new HashMap<String,String>();
			map.put("fieldid", fieldid);
			map.put("fieldName", fieldName);
			billfieldList.add(map);
		}
		
		Map<String, String> params = new HashMap<String, String>();
		rs.executeQuery("select * from "+workflowtablename+" where  requestId = ?", requestid);
		while(rs.next()){
			for(int i = 0;i<billfieldList.size();i++){
				Map<String, String> map = billfieldList.get(i);
				String fieldid = Util.null2String(map.get("fieldid"));
				String fieldName = Util.null2String(map.get("fieldName"));
				String value = Util.null2String(rs.getString(fieldName));
				params.put("field"+fieldid, value);
			}
		}
		
		String createrId = "";
        String createDate = "";
        if(requestid > 0){
            rs.executeQuery("select * from workflow_requestbase where requestid = ? ", requestid);
            if(rs.next()){
                createrId = Util.null2String(rs.getString("creater"));
                createDate = Util.null2String(rs.getString("createdate"));
            }
        }
		
		rs.executeQuery("select b.* from FnaWorkflow a join FnaWorkflowField b on a.id = b.mainid "+
				" where a.isEnable = 1 and b.packetType = -1 and a.workflowid = ? order by b.fieldType ", wfid);
		if(rs.getCounts()==0){
			return "";
		}
		
		String subCompanyId = "";
		String date = "";
		while(rs.next()){
			int fieldType = Util.getIntValue(rs.getString("fieldType"),0);
			int fieldId = Util.getIntValue(rs.getString("fieldId"),0);
			String fieldKey = "field"+fieldId;
			
			if(fieldType == 0){//分部
				if(fieldId == 0){//无字段对应（创建人分部）
				    try{
                        subCompanyId = new ResourceComInfo().getSubCompanyID(createrId);
                    }catch(Exception ignore){}
				}else{
				    subCompanyId = Util.null2String(params.get(fieldKey));
				}
			}else if(fieldType == 1){//日期
				if(fieldId == 0){//无字段对应（申请日期）
					if(requestid >= 0){
						date = createDate;
						if("".equals(date)){
							date = TimeUtil.getCurrentDateString();
						}
					}else{
						date = TimeUtil.getCurrentDateString();
					}
				}else{
					date = Util.null2String(params.get(fieldKey));
				}
			}
		}
		if("".equals(subCompanyId) || "".equals(date)){
			return "";
		}
		
		String accountId = "";
		StringBuffer buffer = new StringBuffer();
        buffer.append(" select a.id from FnaAccountInfo a join FnaAccountRangeSet b on a.id = b.accountId ");
        buffer.append(" where isArchive = 0 ");
        buffer.append(" and a.beginDate <= ? ");
        buffer.append(" and a.endDate >= ? ");
        buffer.append(" and b.orgId = ? ");
		
		rs.executeQuery(buffer.toString(), date, date, subCompanyId);
		if(rs.next()){
			accountId = Util.null2String(rs.getString("id"));
		}
		return accountId;
	}
	

}
