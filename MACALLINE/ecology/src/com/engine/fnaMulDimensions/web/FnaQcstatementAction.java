/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.fnaMulDimensions.web;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.common.util.ParamUtil;
import com.engine.fnaMulDimensions.service.FnaQcstatementService;
import com.engine.fnaMulDimensions.service.impl.FnaQcstatementServiceImpl;

import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.systeminfo.SystemEnv;
import weaver.conn.RecordSet;
import weaver.general.Util;

/**  
* Title: FnaQcstatementAction  
* <AUTHOR>  
* @date 2020年12月23日  
* Description:  
*/
public class FnaQcstatementAction {
	
	/**日志*/
    private BaseBean logger = new BaseBean();
    
    /**
     * getService
     * @param user
     * @return
     */
    private FnaQcstatementService getService(User user){
        return (FnaQcstatementServiceImpl) ServiceUtil.getService(FnaQcstatementServiceImpl.class, user);
    }
    
    /**
     * 获取项目树
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getAsyncTree")
    @Produces(MediaType.TEXT_PLAIN)
    public String getAsyncTree(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
			//
			String userId = Util.null2String(user.getUID());
			RecordSet rs = new RecordSet();
			String xmxxs = "";
			rs.executeQuery("select id from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
			while(rs.next()){
				String xmxx = Util.null2String(rs.getString("id"));
				if(!"".equals(xmxxs)){
					xmxxs+=",";
				}
				xmxxs+="'"+xmxx+"'";
			}
			logger.writeLog("xmxxs:"+xmxxs);
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
				params.put("xmxx",xmxxs);
                result = getService(user).getAsyncTree(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
    
    /**
     * 获取项目树
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
			String userId = Util.null2String(user.getUID());
			RecordSet rs = new RecordSet();
			String xmxxs = "";
			rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
			while(rs.next()){
				String xmxx = Util.null2String(rs.getString("id"));
				if(!"".equals(xmxxs)){
					xmxxs+=",";
				}
				xmxxs+="'"+xmxx+"'";
			}
			logger.writeLog("xmxxs:"+xmxxs);
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
				params.put("xmxxs",xmxxs);
                result = getService(user).getList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
    
    /**
     * 获取高级搜索
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getSearchInfo")
    @Produces(MediaType.TEXT_PLAIN)
    public String getSearchInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
			String userId = Util.null2String(user.getUID());
			RecordSet rs = new RecordSet();
			String xmxxs = "";
			rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
			while(rs.next()){
				String xmxx = Util.null2String(rs.getString("id"));
				if(!"".equals(xmxxs)){
					xmxxs+=",";
				}
				xmxxs+="'"+xmxx+"'";
			}
			logger.writeLog("xmxxs:"+xmxxs);
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getSearchInfo(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
    
    /**
     * 获取tab
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getTab")
    @Produces(MediaType.TEXT_PLAIN)
    public String getTab(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
			String userId = Util.null2String(user.getUID());
			RecordSet rs = new RecordSet();
			String xmxxs = "";
			rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
			while(rs.next()){
				String xmxx = Util.null2String(rs.getString("id"));
				if(!"".equals(xmxxs)){
					xmxxs+=",";
				}
				xmxxs+="'"+xmxx+"'";
			}
			logger.writeLog("xmxxs:"+xmxxs);
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getTab(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
    
    
    
    /**
     * 导出Excel
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getExcel")
    @Produces(MediaType.TEXT_PLAIN)
    public String getExcel(@Context HttpServletRequest request, @Context HttpServletResponse response){
    	Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
			String userId = Util.null2String(user.getUID());
			RecordSet rs = new RecordSet();
			String xmxxs = "";
			rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
			while(rs.next()){
				String xmxx = Util.null2String(rs.getString("id"));
				if(!"".equals(xmxxs)){
					xmxxs+=",";
				}
				xmxxs+="'"+xmxx+"'";
			}
			logger.writeLog("xmxxs:"+xmxxs);
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getExcel(params, user,response);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 预算相关数据穿透
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getBudgetData")
    @Produces(MediaType.TEXT_PLAIN)
    public String getBudgetData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            String userId = Util.null2String(user.getUID());
            RecordSet rs = new RecordSet();
            String xmxxs = "";
            rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
            while(rs.next()){
                String xmxx = Util.null2String(rs.getString("id"));
                if(!"".equals(xmxxs)){
                    xmxxs+=",";
                }
                xmxxs+="'"+xmxx+"'";
            }
            logger.writeLog("xmxxs:"+xmxxs);
            //预算编制只读权限
            boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getBudgetData(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }

        return JSONObject.toJSONString(result);
    }
}
