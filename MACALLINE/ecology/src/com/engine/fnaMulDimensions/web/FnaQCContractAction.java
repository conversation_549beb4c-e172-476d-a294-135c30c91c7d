package com.engine.fnaMulDimensions.web;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.fnaMulDimensions.service.FnaQCContractService;
import com.engine.fnaMulDimensions.service.impl.FnaQCContractServiceImpl;

import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.systeminfo.SystemEnv;
import com.alibaba.fastjson.JSONObject;

public class FnaQCContractAction {
	
	
	
	private FnaQCContractService getService(User user){
		return (FnaQCContractService)ServiceUtil.getService(FnaQCContractServiceImpl.class, user);
	}
	
	
	@POST
    @Path("/getQCContractList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getQCContractList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            String userId = Util.null2String(user.getUID());
            RecordSet rs = new RecordSet();
            String xmxxs = "";
            rs.executeQuery("select id from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
            while(rs.next()){
                String xmxx = Util.null2String(rs.getString("id"));
                if(!"".equals(xmxxs)){
                    xmxxs+=",";
                }
                xmxxs+="'"+xmxx+"'";
            }

        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                params.put("xmxxs",xmxxs);
                result = getService(user).getQCContractList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	/**
	 * 预估结算金额穿透
	 * @param request
	 * @param response
	 * @return
	 */
	@POST
    @Path("/getQCygContractList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getQCygContractList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getQCygContractList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	/**
	 * 实际结算金额穿透
	 * @param request
	 * @param response
	 * @return
	 */
	@POST
    @Path("/getQCsjContractList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getQCsjContractList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getQCsjContractList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	/**
	 * 累计结算金额穿透
	 * @param request
	 * @param response
	 * @return
	 */
	@POST
    @Path("/getQCljContractList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getQCljContractList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getQCljContractList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	
	/**
	 * 高级搜索
	 * @param request
	 * @param response
	 * @return
	 */
	@POST
    @Path("/getSearchInfo")
    @Produces(MediaType.TEXT_PLAIN)
    public String getSearchInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getSearchInfo(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	/**
	 * 树
	 * @param request
	 * @param response
	 * @return
	 */
	@GET
    @Path("/getAsyncTree")
    @Produces(MediaType.TEXT_PLAIN)
    public String getAsyncTree(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getAsyncTree(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	/**
	 * 合同总价金额穿透
	 * @param request
	 * @param response
	 * @return
	 */
	@POST
    @Path("/getQChtzjContractList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getQChtzjContractList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getQChtzjContractList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	/**
	 * 合同控制金额穿透
	 * @param request
	 * @param response
	 * @return
	 */
	@POST
    @Path("/getQChtkzjeContractList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getQChtkzjeContractList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getQChtkzjeContractList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }
	
	/**
	 * 导出excel
	 * @param request
	 * @param response
	 * @return
	 */
	@GET
    @Path("/getExcel")
    @Produces(MediaType.TEXT_PLAIN)
    public String getExcel(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
        	boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getExcel(params, user,response);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        
        return JSONObject.toJSONString(result);
    }

}
