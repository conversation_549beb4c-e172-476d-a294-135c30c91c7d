/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.fnaMulDimensions.service;

import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import weaver.hrm.User;

/**
 * Title: FnaQcstatementService
 *
 * <AUTHOR>
 * @date 2020年12月23日
 * Description:
 */
public interface FnaQcstatementService {

    /**
     * @param @param  params
     * @param @param  user
     * @param @return 参数
     * @return Map<String, Object> 返回类型
     * @throws
     * @Title: getAsyncTree
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    Map<String, Object> getAsyncTree(Map<String, Object> params, User user);

    /**
     * @param @param  params
     * @param @param  user
     * @param @return 参数
     * @return Map<String, Object> 返回类型
     * @throws
     * @Title: getList
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    Map<String, Object> getList(Map<String, Object> params, User user);

    /**
     * @param @param  params
     * @param @param  user
     * @param @return 参数
     * @return Map<String, Object> 返回类型
     * @throws
     * @Title: getSearchInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    Map<String, Object> getSearchInfo(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getTab(Map<String, Object> params, User user);


    /**
     * 导出Excel
     *
     * @param params
     * @param user
     * @param response
     * @return
     */
    Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response);

    /**
     * 预算相关数据穿透
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getBudgetData(Map<String, Object> params, User user);
}
