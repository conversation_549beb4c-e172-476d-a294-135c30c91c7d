package com.engine.fnaMulDimensions.service.impl;

import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.engine.core.impl.Service;
import com.engine.fnaMulDimensions.cmd.qCContract.*;
import com.engine.fnaMulDimensions.service.FnaQCContractService;

import weaver.hrm.User;

public class FnaQCContractServiceImpl extends Service implements FnaQCContractService{

	@Override
	public Map<String, Object> getQCContractList(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetQCContractListCmd(params, user));
	}

	@Override
	public Map<String, Object> getQCygContractList(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetQCygContractListCmd(params, user));
	}

	@Override
	public Map<String, Object> getQCsjContractList(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetQCsjContractListCmd(params, user));
	}

	@Override
	public Map<String, Object> getQCljContractList(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetQCljContractListCmd(params, user));
	}

	@Override
	public Map<String, Object> getSearchInfo(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetSearchInfoCmd(params, user));
	}

	@Override
	public Map<String, Object> getAsyncTree(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetAsyncTreeCmd(params, user));
	}

	@Override
	public Map<String, Object> getQChtzjContractList(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetQChtzjContractListCmd(params, user));
	}

	@Override
	public Map<String, Object> getQChtkzjeContractList(Map<String, Object> params, User user) {
		return commandExecutor.execute(new GetQChtkzjeContractListCmd(params, user));
	}

	@Override
	public Map<String, Object> getExcel(Map<String, Object> params, User user,HttpServletResponse response) {
		return commandExecutor.execute(new GetExcelCmd(params, user,response));
	}

}
