/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.fnaMulDimensions.service.impl;

import weaver.hrm.User;

import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.engine.core.impl.Service;
import com.engine.fnaMulDimensions.cmd.FnaQcstatement.*;
import com.engine.fnaMulDimensions.service.FnaQcstatementService;

/**
 * Title: FnaQcstatementServiceImpl
 *
 * <AUTHOR>
 * @date 2020年12月23日
 * Description:
 */
public class FnaQcstatementServiceImpl extends Service implements FnaQcstatementService {

    /*
     * Title: getAsyncTree
     * Description:
     * @param params
     * @param user
     * @return
     * @see com.engine.fnaMulDimensions.service.FnaQcstatementService#getAsyncTree(java.util.Map, weaver.hrm.User)
     */
    @Override
    public Map<String, Object> getAsyncTree(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetAsyncTreeCmd(params, user));
    }

    /*
     * Title: getList
     * Description:
     * @param params
     * @param user
     * @return
     * @see com.engine.fnaMulDimensions.service.FnaQcstatementService#getList(java.util.Map, weaver.hrm.User)
     */
    @Override
    public Map<String, Object> getList(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetListCmd3(params, user));
    }

    /*
     * Title: getSearchInfo
     * Description:
     * @param params
     * @param user
     * @return
     * @see com.engine.fnaMulDimensions.service.FnaQcstatementService#getSearchInfo(java.util.Map, weaver.hrm.User)
     */
    @Override
    public Map<String, Object> getSearchInfo(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetSearchInfoCmd(params, user));
    }

    @Override
    public Map<String, Object> getTab(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetTabCmd(params, user));
    }

    @Override
    public Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response) {
        return commandExecutor.execute(new GetExcelCmd(params, user, response));
    }

    @Override
    public Map<String, Object> getBudgetData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetBudgetDataCmd(params, user));
    }

}
