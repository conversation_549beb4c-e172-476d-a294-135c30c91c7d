package com.engine.fnaMulDimensions.service;

import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import weaver.hrm.User;

public interface FnaQCContractService {

	Map<String, Object> getQCContractList(Map<String, Object> params, User user);

	Map<String, Object> getQCygContractList(Map<String, Object> params, User user);

	Map<String, Object> getQCsjContractList(Map<String, Object> params, User user);

	Map<String, Object> getQCljContractList(Map<String, Object> params, User user);

	Map<String, Object> getSearchInfo(Map<String, Object> params, User user);

	Map<String, Object> getAsyncTree(Map<String, Object> params, User user);

	Map<String, Object> getQChtzjContractList(Map<String, Object> params, User user);

	Map<String, Object> getQChtkzjeContractList(Map<String, Object> params, User user);

	Map<String, Object> getExcel(Map<String, Object> params, User user,HttpServletResponse response);

}
