/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.fnaMulDimensions.cmd.qCContract;

import java.util.*;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.entity.FnaAsyncTreeNote;

import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

/**  
* Title: GetAsyncTreeCmd  
* <AUTHOR>  
* @date 2020年12月23日  
* Description:  
*/
public class GetAsyncTreeCmd extends AbstractCommonCommand<Map<String, Object>>{
	 /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetAsyncTreeCmd(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            String keyword = Util.null2String(params.get("keyword"));
            String id = Util.null2String(params.get("id"));

            RecordSet rs = new RecordSet();
            
            List<FnaAsyncTreeNote> nodeList = new LinkedList<FnaAsyncTreeNote>();
            StringBuffer queryBuffer = new StringBuffer();
            queryBuffer.append(" select a.id,a.xmmc  from uf_xmxx a ");
            queryBuffer.append(" where 1=1 ");
            if(!"".equals(keyword)){
            	queryBuffer.append(" and a.xmmc like '%"+keyword+"%'");
            }
            queryBuffer.append(" order by a.id ");
            
            rs.executeQuery(queryBuffer.toString());
            while (rs.next()) {
            	String id1 = Util.null2String(rs.getString("id"));
                String xmmc = Util.null2String(rs.getString("xmmc"));
                
                FnaAsyncTreeNote fnaAsyncTreeNote = new FnaAsyncTreeNote();
                fnaAsyncTreeNote.setId(id1);
                fnaAsyncTreeNote.setCanClick(true);
                fnaAsyncTreeNote.setIsParent(false);
                fnaAsyncTreeNote.setIsLeaf(true);
                fnaAsyncTreeNote.setName(xmmc);
                fnaAsyncTreeNote.setKey(id1);
                fnaAsyncTreeNote.setDomid(id1);
                fnaAsyncTreeNote.setIsopen(false);
                nodeList.add(fnaAsyncTreeNote);
            }    
                
            
            if (("").equals(id)) {
                Map<String, Object> data = new HashMap<String, Object>();
                Map<String, Object> rootBearera = new HashMap<String, Object>();
                rootBearera.put("canClick", true);
                rootBearera.put("isParent", true);
                rootBearera.put("name", "项目");//
                rootBearera.put("isSelected", false);
                rootBearera.put("subs", nodeList);
                rootBearera.put("id", "0");
                rootBearera.put("key", "0");
                rootBearera.put("isLeaf", false);
                rootBearera.put("domid", "0");
                rootBearera.put("isopen", true);
                data.put("rootBearer", rootBearera);
                result.put("datas", data);
            }else{
            	result.put("datas", nodeList);
            }
            result.put("status", "1");
            result.put("info", "");
        }catch (Exception e){
            result.put("status", "-1");
            result.put("errorInfo", e.getMessage());
        }
        return result;
    }
}
