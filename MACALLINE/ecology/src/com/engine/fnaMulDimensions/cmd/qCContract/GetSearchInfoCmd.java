package com.engine.fnaMulDimensions.cmd.qCContract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.systeminfo.SystemEnv;

import com.api.browser.bean.SearchConditionItem;
import com.api.browser.bean.SearchConditionOption;
import com.api.browser.util.ConditionFactory;
import com.api.browser.util.ConditionType;
import com.api.fna.util.FnaConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;


public class GetSearchInfoCmd extends AbstractCommonCommand<Map<String, Object>>{
	/**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    
    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetSearchInfoCmd(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

    	
        Map<String, Object> result = new HashMap<String, Object>();
        RecordSet rSet = new RecordSet();
        List<Map<String, Object>> grouplist = new ArrayList<Map<String, Object>>();
        ConditionFactory conditionFactory = new ConditionFactory(user);
        
        //常用条件
        Map<String, Object> groupitem1 = new HashMap<String, Object>();
        List<SearchConditionItem> itemlist1 = new LinkedList<SearchConditionItem>();
        
        SearchConditionItem xmmcItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "xmmc","fnaqccontractXm");
        xmmcItem.setLabel("项目名称");
        xmmcItem.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(xmmcItem);
        
        SearchConditionItem htmcItem = conditionFactory.createCondition(ConditionType.INPUT, -1, "htmc");
        htmcItem.setLabel("合同名称");
        itemlist1.add(htmcItem);
        
        List<SearchConditionOption> htlxOptions = new ArrayList<SearchConditionOption>();
        htlxOptions.add(new SearchConditionOption("-1",""));//未发布
        rSet.executeQuery("select selectname,selectvalue from workflow_selectitem "
				+ " where fieldid = 62230	");
        while(rSet.next()){
        	String selectname = Util.null2String(rSet.getString("selectname"));
        	String selectvalue = Util.null2String(rSet.getString("selectvalue"));
        	htlxOptions.add(new SearchConditionOption(selectvalue,selectname));
        }
        SearchConditionItem htlxItem = conditionFactory.createCondition(ConditionType.SELECT, -1, "htlx",htlxOptions);
        htlxItem.setLabel("合同类型");
        itemlist1.add(htlxItem);
        
        
        SearchConditionItem htbhItem = conditionFactory.createCondition(ConditionType.INPUT, -1, "htbh");
        htbhItem.setLabel("合同编号");
        itemlist1.add(htbhItem);
        
        /*SearchConditionItem kmmcItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "kmmc","qcctsubject");
        kmmcItem.setLabel("科目名称");
        itemlist1.add(kmmcItem);*/

        SearchConditionItem km1Item = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km_4","fnaqckmnew");
        km1Item.setLabel("一级科目");

        km1Item.getBrowserConditionParam().getDataParams().put("typeId", "4");
        km1Item.getBrowserConditionParam().getDestDataParams().put("typeId", "4");
        km1Item.getBrowserConditionParam().getConditionDataParams().put("typeId", "4");
        km1Item.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(km1Item);
        SearchConditionItem km2Item = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km_5","fnaqckmnew");
		km2Item.setLabel("二级科目");

		km2Item.getBrowserConditionParam().getDataParams().put("typeId", "5");
		km2Item.getBrowserConditionParam().getDestDataParams().put("typeId", "5");
		km2Item.getBrowserConditionParam().getConditionDataParams().put("typeId", "5");
		km2Item.getBrowserConditionParam().setIsSingle(false);
		itemlist1.add(km2Item);
		SearchConditionItem km3Item = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km_2","fnaqckmnew");
		km3Item.setLabel("三级科目");

		km3Item.getBrowserConditionParam().getDataParams().put("typeId", "2");
		km3Item.getBrowserConditionParam().getDestDataParams().put("typeId", "2");
		km3Item.getBrowserConditionParam().getConditionDataParams().put("typeId", "2");
		km3Item.getBrowserConditionParam().setIsSingle(false);
		itemlist1.add(km3Item);
        SearchConditionItem km4Item = conditionFactory.createCondition(ConditionType.INPUT, -1, "km_3");
        km4Item.setLabel("四级科目");
        itemlist1.add(km4Item);

        SearchConditionItem jfItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "jf","fnaqcgys");
        jfItem.setLabel("甲方");
        jfItem.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(jfItem);
        
        SearchConditionItem yfItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "yf","fnaqcgys");
        yfItem.setLabel("乙方");
        yfItem.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(yfItem);
        
        SearchConditionItem bfItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "bf","fnaqcgys");
        bfItem.setLabel("丙方");
        bfItem.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(bfItem);

        SearchConditionItem range = conditionFactory.createCondition(ConditionType.SCOPE, -1, new String[]{"low","high"});
        range.setLabel("已付比例(%)");
        itemlist1.add(range);

        SearchConditionItem  range1= conditionFactory.createCondition(ConditionType.SCOPE, -1, new String[]{"ranglow","ranghigh"});
        range1.setLabel("实际财务支付比例(%)");
        itemlist1.add(range1);


        SearchConditionItem qdrqItem = conditionFactory.createCondition(ConditionType.RANGEPICKER, -1, new String[]{"qdrqdatefrom","qdrqdateto"});
        qdrqItem.setLabel("签订日期");
        itemlist1.add(qdrqItem);
        
        
        SearchConditionItem fkfsItem = conditionFactory.createCondition(ConditionType.INPUT, -1, "fkfs");
        fkfsItem.setLabel("付款方式");
        itemlist1.add(fkfsItem);
        
        List<SearchConditionOption> jjfsOptions = new ArrayList<SearchConditionOption>();
        jjfsOptions.add(new SearchConditionOption("-1",""));//未发布
        rSet.executeQuery("select selectname,selectvalue from workflow_selectitem "
				+ " where fieldid = 65463	");
        while(rSet.next()){
        	String selectname = Util.null2String(rSet.getString("selectname"));
        	String selectvalue = Util.null2String(rSet.getString("selectvalue"));
        	jjfsOptions.add(new SearchConditionOption(selectvalue,selectname));
        }
        SearchConditionItem jjfsItem = conditionFactory.createCondition(ConditionType.SELECT, -1, "jjfs",jjfsOptions);
        jjfsItem.setLabel("计价方式");
        itemlist1.add(jjfsItem);
        
        
        List<SearchConditionOption> sfjsOptions = new ArrayList<SearchConditionOption>();
        sfjsOptions.add(new SearchConditionOption("-1",""));//未发布
        rSet.executeQuery("select selectname,selectvalue from workflow_selectitem "
				+ " where fieldid = 65464	");
        while(rSet.next()){
        	String selectname = Util.null2String(rSet.getString("selectname"));
        	String selectvalue = Util.null2String(rSet.getString("selectvalue"));
        	sfjsOptions.add(new SearchConditionOption(selectvalue,selectname));
        }
        SearchConditionItem sfjsItem = conditionFactory.createCondition(ConditionType.SELECT, -1, "sfjs",sfjsOptions);
        sfjsItem.setLabel("是否结算");
        itemlist1.add(sfjsItem);
        
        SearchConditionItem htzjItem = conditionFactory.createCondition(ConditionType.INPUTNUMBER, -1, "htzj");
        htzjItem.setLabel("合同总价");
        itemlist1.add(htzjItem);
        
        
        
        groupitem1.put("title", SystemEnv.getHtmlLabelName(1361, user.getLanguage()));//基本信息
        groupitem1.put("defaultshow", true);
        groupitem1.put("items", itemlist1);
        grouplist.add(groupitem1);

        result.put("status", "1");
        result.put(FnaConstant.FNA_RESULT_CONDITIONS, grouplist);
        
        return result;
    }

}
