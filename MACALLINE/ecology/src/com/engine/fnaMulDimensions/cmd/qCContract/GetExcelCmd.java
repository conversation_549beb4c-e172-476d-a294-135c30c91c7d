package com.engine.fnaMulDimensions.cmd.qCContract;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.api.fna.util.ExcelOutUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.FnaAmountPointComInfo;
import com.engine.fnaMulDimensions.util.FnaQcStatementUtil;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;

import weaver.conn.RecordSet;
import weaver.file.ExcelFile;
import weaver.file.ExcelRow;
import weaver.file.ExcelSheet;
import weaver.file.ExcelStyle;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

public class GetExcelCmd extends AbstractCommonCommand<Map<String, Object>>{
	
	/**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    /**
     * 响应
     */
    private HttpServletResponse response;
    private int amountPoint;
    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetExcelCmd(Map<String, Object> params, User user, HttpServletResponse response){
        this.params = params;
        this.user = user;
        this.response=response;
        this.amountPoint = Util.getIntValue(new FnaAmountPointComInfo().getAmountPoint(""+FnaAccTypeConstant.AMOUNT_POINT_ID));
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<String, Object>();
        try{
			String xmxx = Util.null2String(params.get("xmxx"));
			String xmmc = Util.null2String(params.get("xmmc"));
			String htmc = Util.null2String(params.get("htmc"));
			String htlx = Util.null2String(params.get("htlx"));
			String htbh = Util.null2String(params.get("htbh"));
			String km_5 = Util.null2String(params.get("km_5")); //二级科目
			String km_2 = Util.null2String(params.get("km_2"));	//三级科目
			String km_3 = Util.null2String(params.get("km_3")); //四级科目
			String km_4= Util.null2String(params.get("km_4"));	//一级科目
			String rangelow = Util.null2String(params.get("low")); // 范围
			String rangehigh = Util.null2String(params.get("high")); // 范围
			String low = Util.null2String(params.get("rangelow")); // 范围
			String high = Util.null2String(params.get("rangehigh")); // 范围
			String jf = Util.null2String(params.get("jf"));
			String yf = Util.null2String(params.get("yf"));
			String bf = Util.null2String(params.get("bf"));
			String qdrqdatefrom = Util.null2String(params.get("qdrqdatefrom"));
			String qdrqdateto = Util.null2String(params.get("qdrqdateto"));
			String fkfs = Util.null2String(params.get("fkfs"));
			String jjfs = Util.null2String(params.get("jjfs"));
			String sfjs = Util.null2String(params.get("sfjs"));
			String htzj = Util.null2String(params.get("htzj"));
			String xmxxs = Util.null2String(params.get("xmxxs"));

            
        	RecordSet rs = new RecordSet();


			String backFields = "a.id,a.xm,a.htmc,htkzje,"
					+ " a.htlx,a.htbh,a.id km,"
					+ " a.jf,a.jf as fk1 ,a.yf,a.yf as fk2 , a.bf,a.bf as fk3 ,a.qdrq,a.fktj,a.jjfs,a.sfjs,a.zbj,"
					+ " a.htje,a.ygjsje1 ygjsje,a.sjjsje,a.yfje,a.yfbl,a.cwsjzfje,a.cwsjzfbl,"
					+ " a.yfje ljzfce,a.sjjsje swzfhtje,' ' bz";
			String fromSql = " from uf_htxx a ";
			String sqlWhere = " where 1=1 ";
			String orderBy = " a.xm,a.id ";
			//String groupby = "";

			if(!"".equals(xmxxs)){
				fromSql += " and  a.id in("+xmxxs+")";
			}

			if(!"".equals(xmmc)){
				sqlWhere += " and a.xm in("+xmmc+") ";
			}
			if((!"".equals(xmxx)) && (!"0".equals(xmxx))){
				sqlWhere += " and a.xm in("+xmxx+") ";
			}
			if(!"".equals(htmc)){
				sqlWhere += " and a.htmc  like '%"+htmc+"%' ";
			}
			if(!"".equals(htlx)){
				sqlWhere += " and a.htlx  = "+htlx+" ";
			}
			if(!"".equals(htbh)){
				sqlWhere += " and a.htbh  like '%"+htbh+"%' ";
			}
			if((!"".equals(km_5))||(!"".equals(km_2))||(!"".equals(km_3))||(!"".equals(km_4))){
				String kmid = "";
				if(!"".equals(km_5)){
					kmid+=km_5;
				}
				if(!"".equals(km_2)){
					if(!"".equals(kmid)){
						kmid+=",";
					}
					kmid+=km_2;
				}
				if(!"".equals(km_4)){
					if(!"".equals(kmid)){
						kmid+=",";
					}
					kmid+=km_4;
				}

				String km = getsjkmid(kmid);
				if(!"".equals(km_3)){
					String km2 = getsjkmid2(km_3);
					km+=","+km2;
				}

				String[] strings =  km.split(",");
				StringBuilder stringBuilder = new StringBuilder();
				for (int i = 0; i < strings.length; i++) {
					if (i == strings.length - 1) {
						stringBuilder.append("km = " + strings[i]);
					} else {
						stringBuilder.append("km = " + strings[i] + "or ");
					}
				}
				sqlWhere += "  and a.id in(select mainId from uf_htxx_dt1 where " + stringBuilder.toString() +" )";
			}
			if(!"".equals(jf)){
				sqlWhere += " and a.jf in("+jf+") ";
			}
			if(!"".equals(yf)){
				sqlWhere += " and a.yf in("+yf+") ";
			}
			if(!"".equals(bf)){
				sqlWhere += " and a.bf in("+bf+") ";
			}
			if(!"".equals(qdrqdatefrom)){
				sqlWhere += " and a.qdrq  >= '"+qdrqdatefrom+"' ";
			}
			if(!"".equals(qdrqdateto)){
				sqlWhere += " and a.qdrq  <= '"+qdrqdateto+"' ";
			}
			if(!"".equals(fkfs)){
				sqlWhere += " and a.fktj  like '%"+fkfs+"%' ";
			}
			if(!"".equals(jjfs)){
				sqlWhere += " and a.jjfs  = "+jjfs+" ";
			}
			if(!"".equals(sfjs)){
				sqlWhere += " and a.sfjs  = "+sfjs+" ";
			}
			if(!"".equals(htzj)){
				sqlWhere += " and a.htje  <= "+htzj+" ";
			}
			if(!"".equals(rangelow)){
				double number = Double.parseDouble(rangelow)/100;
				sqlWhere +=" and a.yfbl >= "+ number +" ";
			}if(!"".equals(rangehigh)){
				double number = Double.parseDouble(rangehigh)/100;
				sqlWhere +=" and a.yfbl <= "+ number +" ";
			}if(!"".equals(low)){
				double number = Double.parseDouble(low)/100;
				sqlWhere +=" and a.cwsjzfbl >= "+ number +" ";
			}if(!"".equals(high)){
				double number = Double.parseDouble(high)/100;
				sqlWhere +=" and a.cwsjzfbl <= "+ number +" ";
			}


            String sql = " select "+backFields+" "+fromSql+" "+sqlWhere+" order by "+orderBy;
            new BaseBean().writeLog("GetExcelCmd_sql:"+sql);
            FnaQcStatementUtil fnaQcStatementUtil = new FnaQcStatementUtil();
            
            ExcelFile excelFile = new ExcelFile();
    		excelFile.init();
    		String sheetName= "合同台账表";
    		ExcelSheet es = new ExcelSheet();
    		excelFile.addSheet(sheetName, es);
    		
            
            rs.executeQuery(sql);
            new BaseBean().writeLog("rs.getCounts()+1:"+rs.getCounts()+1);
            es.initRowList(rs.getCounts()+1);
            for(int i = 0;i<24;i++){ //设置列的宽度
            	es.addColumnwidth(6000);
            }
            ExcelStyle excelStyle = excelFile.newExcelStyle("title");
            excelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
            excelStyle.setFontbold(ExcelStyle.Strong_Font); //字体粗细
            
            ExcelRow er = es.newExcelRow(0);
            er.addStringValue("项目名称","title");
            er.addStringValue("合同名称","title");
            er.addStringValue("合同类型","title");
            er.addStringValue("合同编号","title");
            er.addStringValue("科目名称","title");
            er.addStringValue("甲方","title");
            er.addStringValue("乙方","title");
            er.addStringValue("丙方","title");
            er.addStringValue("签订日期","title");
            er.addStringValue("付款方式","title");
            er.addStringValue("计价方式","title");
            er.addStringValue("是否结算","title");
            er.addStringValue("质保金","title");
            er.addStringValue("合同总价","title");
            er.addStringValue("合同控制金额","title");
            er.addStringValue("预估结算金额","title");
            er.addStringValue("实际结算金额","title");
            er.addStringValue("累计审批产值价款","title");
            er.addStringValue("累计实际完成比例","title");
            er.addStringValue("累计实际支付价款","title");
            er.addStringValue("累计实际支付比例","title");
            er.addStringValue("累计支付差额","title");
            er.addStringValue("尚未支付合同金额","title");
            er.addStringValue("备注","title");
            int row = 1;
            while(rs.next()){
            	String xm = Util.null2String(rs.getString("xm"));
            	String htmcq = Util.null2String(rs.getString("htmc"));
            	String htlxq = Util.null2String(rs.getString("htlx"));
            	String htbhq = Util.null2String(rs.getString("htbh"));
            	String km = Util.null2String(rs.getString("km"));
            	String jfq = Util.null2String(rs.getString("jf"));
            	String yfq = Util.null2String(rs.getString("yf"));
            	String bfq = Util.null2String(rs.getString("bf"));
            	String qdrq = Util.null2String(rs.getString("qdrq"));
            	String fktj = Util.null2String(rs.getString("fktj"));
            	String jjfsq = Util.null2String(rs.getString("jjfs"));
            	String sfjsq = Util.null2String(rs.getString("sfjs"));
            	String zbj = Util.null2String(rs.getString("zbj"));
            	String htje = Util.null2String(rs.getString("htje"));
            	String htkzje = Util.null2String(rs.getString("htkzje"));
            	String ygjsje = Util.null2String(rs.getString("ygjsje"));
            	String sjjsje = Util.null2String(rs.getString("sjjsje"));
            	String yfje = Util.null2String(rs.getString("yfje"));
            	String yfbl = Util.null2String(rs.getString("yfbl"));
            	String cwsjzfje = Util.null2String(rs.getString("cwsjzfje"));
            	String cwsjzfbl = Util.null2String(rs.getString("cwsjzfbl"));
            	String ljzfce = Util.null2String(rs.getString("ljzfce"));
            	String swzfhtje = Util.null2String(rs.getString("swzfhtje"));
            	String bz = Util.null2String(rs.getString("bz"));
            	
            	
            	
            	ExcelRow er1 = es.newExcelRow(row);
            	row++;
            	er1.addStringValue(fnaQcStatementUtil.getxmmc(xm));
            	er1.addStringValue(htmcq);
            	
            	er1.addStringValue(fnaQcStatementUtil.gethtlx(htlxq));
            	er1.addStringValue(htbhq);
            	er1.addStringValue(fnaQcStatementUtil.getSubjectNameBrowserjm(km));
            	er1.addStringValue(fnaQcStatementUtil.getgys(jfq));
            	er1.addStringValue(fnaQcStatementUtil.getgys(yfq));
            	er1.addStringValue(fnaQcStatementUtil.getgys(bfq));
            	er1.addStringValue(qdrq); 
            	er1.addStringValue(fktj); 
            	er1.addStringValue(fnaQcStatementUtil.getjjfs(jjfsq));
            	er1.addStringValue(fnaQcStatementUtil.getsfjs(sfjsq));
            	er1.addStringValue(fnaQcStatementUtil.getamountdf(zbj));
            	er1.addStringValue(fnaQcStatementUtil.getamountdf(htje));
            	er1.addStringValue(fnaQcStatementUtil.getamountdf(htkzje));
            	er1.addStringValue(fnaQcStatementUtil.getamountdf(ygjsje));
            	er1.addStringValue(fnaQcStatementUtil.getamountdf(sjjsje));
            	er1.addStringValue(fnaQcStatementUtil.getamountdf(yfje));
            	er1.addStringValue(fnaQcStatementUtil.getbfs(yfbl));
            	er1.addStringValue(fnaQcStatementUtil.getamountdf(cwsjzfje));
            	er1.addStringValue(fnaQcStatementUtil.getbfs(cwsjzfbl));
            	er1.addStringValue(fnaQcStatementUtil.getljzfce(ljzfce,cwsjzfje));
            	er1.addStringValue(fnaQcStatementUtil.getswzfwcje(swzfhtje,ygjsje+"+"+cwsjzfje));
            	er1.addStringValue(bz); 
            }
        	
            excelFile.setFilename("合同台账表");//预算数据填报模板
            ExcelOutUtil excelOutUtil = new ExcelOutUtil();
            excelOutUtil.ExcelOut(user, excelFile, response);
	        result.put("status", "1");
	    }catch (Exception e){
	        new BaseBean().writeLog("ExportDataNewCmd_Exception:"+e.getMessage());
	        result.put("status", "-1");
	        result.put("errorInfo", e.getMessage().replace("java.lang.Exception:",""));
	    }
	    return result;
    }
    
    public String getsjkmid(String km){
    	String result = "";
    	String sql = "(";
    	String[] kmArray = km.split(",");
		for(int i = 0;i<kmArray.length;i++){
			if(i>0){
				sql+=" or ";
			}
			sql += " a.ACCOUNTCODE like '%\\_"+kmArray[i]+"' escape '\\' "; 
		}
		sql+=")";
    	
    	
    	RecordSet rs = new RecordSet();
    	RecordSet rs2 = new RecordSet();
    	rs.executeQuery("select * from fnaaccountDtl where tableType = ? ", FnaAccTypeConstant.BUDGET_SUBJECT);
        while(rs.next()){
        	String tableName = Util.null2String(rs.getString("tableName"));
        	new BaseBean().writeLog("tableName:"+tableName);
        	if((!"".equals(tableName)) ){
        		String subjectCodelikesql = "";
        		String sql2 = "select a.subjectCode from "+tableName+" a where 1=1 and "+sql;
        		rs2.executeQuery(sql2);
        		while(rs2.next()){
        			String subjectCode = Util.null2String(rs2.getString("subjectCode"));
        			if(!"".equals(subjectCodelikesql)){
        				subjectCodelikesql+=" or ";        				
        			}
        			subjectCodelikesql+=" a.subjectCode like '"+subjectCode+"%'";
        		}
        		if(!"".equals(subjectCodelikesql)){
        			String sql3 = "select a.id from "+tableName+" a where 1=1 and ("+subjectCodelikesql+") and a.SUBJECTLEVEL = 5 ";
            		rs2.executeQuery(sql3);
            		while(rs2.next()){
            			String id = Util.null2String(rs2.getString("id"));
            			if(!"".equals(result)){
            				result+=",";
            			}
            			result+="'"+id+"'";
            		}
        		}
        		
	        }
        }
        return result;

    }
	public String getsjkmid2(String name){
		String result = "";
		RecordSet rs = new RecordSet();
		RecordSet rs2 = new RecordSet();
		rs.executeQuery("select * from fnaaccountDtl where tableType = ? ", FnaAccTypeConstant.BUDGET_SUBJECT);
		while(rs.next()){
			String tableName = Util.null2String(rs.getString("tableName"));
			new BaseBean().writeLog("tableName:"+tableName);
			if((!"".equals(tableName)) ){
				String sql3 = "select a.id from "+tableName+" a where 1=1 and SUBJECTNAME like '%"+name+"%' and a.SUBJECTLEVEL = 5 ";
				rs2.executeQuery(sql3);
				while(rs2.next()){
					String id = Util.null2String(rs2.getString("id"));
					if(!"".equals(result)){
						result+=",";
					}
					result+="'"+id+"'";
				}

			}
		}
		return result;
	}
}
