package com.engine.fnaMulDimensions.cmd.qCContract;


import com.api.browser.bean.*;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

public class GetQCContractListCmd extends AbstractCommonCommand<Map<String, Object>>{
	
	
	/**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetQCContractListCmd(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> datas = new HashMap<String, Object>();
        String xmxx = Util.null2String(params.get("xmxx"));
        String xmmc = Util.null2String(params.get("xmmc"));
        String htmc = Util.null2String(params.get("htmc"));
        String htlx = Util.null2String(params.get("htlx"));
        String htbh = Util.null2String(params.get("htbh"));
        String km_5 = Util.null2String(params.get("km_5")); //二级科目
        String km_2 = Util.null2String(params.get("km_2"));	//三级科目
        String km_3 = Util.null2String(params.get("km_3")); //四级科目
        String km_4= Util.null2String(params.get("km_4"));	//一级科目
        String rangelow = Util.null2String(params.get("low")); // 范围
        String rangehigh = Util.null2String(params.get("high")); // 范围
        String low = Util.null2String(params.get("rangelow")); // 范围
        String high = Util.null2String(params.get("rangehigh")); // 范围
        String jf = Util.null2String(params.get("jf"));
        String yf = Util.null2String(params.get("yf"));
        String bf = Util.null2String(params.get("bf"));
        String qdrqdatefrom = Util.null2String(params.get("qdrqdatefrom"));
        String qdrqdateto = Util.null2String(params.get("qdrqdateto"));
        String fkfs = Util.null2String(params.get("fkfs"));
        String jjfs = Util.null2String(params.get("jjfs"));
        String sfjs = Util.null2String(params.get("sfjs"));
        String htzj = Util.null2String(params.get("htzj"));
        String xmxxs = Util.null2String(params.get("xmxxs"));

        
        String backFields = "a.id,a.xm,a.htmc,htkzje,"
        		+ " a.htlx,a.htbh,a.id km,"
        		+ " a.jf,a.jf as fk1 ,a.yf,a.yf as fk2 , a.bf,a.bf as fk3 ,a.qdrq,a.fktj,a.jjfs,a.sfjs,a.zbj,"
        		+ " a.htje,a.ygjsje1 ygjsje,a.sjjsje,a.yfje,a.yfbl,a.cwsjzfje,a.cwsjzfbl,"
        		+ " a.yfje ljzfce,a.sjjsje swzfhtje,' ' bz";
        String fromSql = " from uf_htxx a ";
        String sqlWhere = " where 1=1 ";
        String orderBy = " a.xm,a.id ";
        //String groupby = "";
        String sqlprimarykey = " a.id ";

        if(!"".equals(xmxxs)){
            fromSql += " and  a.id in("+xmxxs+")";
        }

        if(!"".equals(xmmc)){
        	sqlWhere += " and a.xm in("+xmmc+") ";
        }
        if((!"".equals(xmxx)) && (!"0".equals(xmxx))){
        	sqlWhere += " and a.xm in("+xmxx+") ";
        }
        if(!"".equals(htmc)){
        	sqlWhere += " and a.htmc  like '%"+htmc+"%' ";
        }
        if(!"".equals(htlx)){
        	sqlWhere += " and a.htlx  = "+htlx+" ";
        }
        if(!"".equals(htbh)){
        	sqlWhere += " and a.htbh  like '%"+htbh+"%' ";
        }
        if((!"".equals(km_5))||(!"".equals(km_2))||(!"".equals(km_3))||(!"".equals(km_4))){
        	String kmid = "";
        	if(!"".equals(km_5)){
        		kmid+=km_5;
        	}
        	if(!"".equals(km_2)){
        		if(!"".equals(kmid)){
        			kmid+=",";
        		}
        		kmid+=km_2;
        	}
            if(!"".equals(km_4)){
                if(!"".equals(kmid)){
                    kmid+=",";
                }
                kmid+=km_4;
            }

        	String km = getsjkmid(kmid);
            if(!"".equals(km_3)){
                String km2 = getsjkmid2(km_3);
                km+=","+km2;
            }

        	String[] strings =  km.split(",");
        	StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < strings.length; i++) {
                if (i == strings.length - 1) {
                    stringBuilder.append("km = " + strings[i]);
                } else {
                    stringBuilder.append("km = " + strings[i] + "or ");
                }
            }
            sqlWhere += "  and a.id in(select mainId from uf_htxx_dt1 where " + stringBuilder.toString() +" )";
        }
        if(!"".equals(jf)){
        	sqlWhere += " and a.jf in("+jf+") ";
        }
        if(!"".equals(yf)){
        	sqlWhere += " and a.yf in("+yf+") ";
        }
        if(!"".equals(bf)){
        	sqlWhere += " and a.bf in("+bf+") ";
        }
        if(!"".equals(qdrqdatefrom)){
        	sqlWhere += " and a.qdrq  >= '"+qdrqdatefrom+"' ";
        }
        if(!"".equals(qdrqdateto)){
        	sqlWhere += " and a.qdrq  <= '"+qdrqdateto+"' ";
        }
        if(!"".equals(fkfs)){
        	sqlWhere += " and a.fktj  like '%"+fkfs+"%' ";
        }
        if(!"".equals(jjfs)){
        	sqlWhere += " and a.jjfs  = "+jjfs+" ";
        }
        if(!"".equals(sfjs)){
        	sqlWhere += " and a.sfjs  = "+sfjs+" ";
        }
        if(!"".equals(htzj)){
        	sqlWhere += " and a.htje  <= "+htzj+" ";
        }
        if(!"".equals(rangelow)){
            double number = Double.parseDouble(rangelow)/100;
            sqlWhere +=" and a.yfbl >= "+ number +" ";
        }if(!"".equals(rangehigh)){
            double number = Double.parseDouble(rangehigh)/100;
            sqlWhere +=" and a.yfbl <= "+ number +" ";
        }if(!"".equals(low)){
            double number = Double.parseDouble(low)/100;
            sqlWhere +=" and a.cwsjzfbl >= "+ number +" ";
        }if(!"".equals(high)){
            double number = Double.parseDouble(high)/100;
            sqlWhere +=" and a.cwsjzfbl <= "+ number +" ";
        }

        List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
        cols.add(new SplitTableColBean("true","id"));//名称
        
        cols.add(new SplitTableColBean("30%", "项目名称", "xm", "xm"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getxmmc"));//项目名称
        cols.add(new SplitTableColBean("30%", "合同名称", "htmc", "htmc"));//合同名称
        
        
        cols.add(new SplitTableColBean("30%", "合同类型", "htlx", "htlx"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.gethtlx"));//合同类型
        
        cols.add(new SplitTableColBean("30%", "合同编号", "htbh", "htbh"));//合同编号
        
        cols.add(new SplitTableColBean("30%", "科目名称", "km", "km"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getSubjectNameBrowserjm"));//科目名称
        
        cols.add(new SplitTableColBean("30%", "甲方", "jf", "jf"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getgys"));//甲方
        cols.add(new SplitTableColBean("true","fk1"));//甲方id

        cols.add(new SplitTableColBean("30%", "乙方", "yf", "yf"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getgys"));//乙方
        cols.add(new SplitTableColBean("true","fk2"));//甲方id//乙方id


        cols.add(new SplitTableColBean("30%", "丙方", "bf", "bf"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getgys"));//丙方
        cols.add(new SplitTableColBean("true","fk3"));//丙方id


        cols.add(new SplitTableColBean("30%", "签订日期", "qdrq", "qdrq"));//签订日期
        
        cols.add(new SplitTableColBean("30%", "付款方式", "fktj", "fktj"));//付款方式
        cols.add(new SplitTableColBean("30%", "计价方式", "jjfs", "jjfs"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getjjfs"));//计价方式
        cols.add(new SplitTableColBean("30%", "是否结算", "sfjs", "sfjs"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getsfjs"));//是否结算
        cols.add(new SplitTableColBean("30%", "质保金", "zbj", "zbj"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//质保金
        cols.add(new SplitTableColBean("30%", "合同总价", "htje", "htje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//合同总价
        cols.add(new SplitTableColBean("30%", "合同控制金额", "htkzje", "htkzje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//累计审批产值价款
        cols.add(new SplitTableColBean("30%", "预估结算金额", "ygjsje", "ygjsje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//预估结算金额
        cols.add(new SplitTableColBean("30%", "实际结算金额", "sjjsje", "sjjsje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//实际结算金额
        cols.add(new SplitTableColBean("30%", "累计审批产值价款", "yfje", "yfje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//累计审批产值价款
        cols.add(new SplitTableColBean("30%", "累计实际完成比例", "yfbl", "yfbl"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getbfs"));//累计实际完成比例
        cols.add(new SplitTableColBean("30%", "累计实际支付价款", "cwsjzfje", "cwsjzfje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//累计实际支付价款
        cols.add(new SplitTableColBean("30%", "累计实际支付比例", "cwsjzfbl", "cwsjzfbl"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getbfs"));//累计实际支付比例
        cols.add(new SplitTableColBean("30%", "累计支付差额", "ljzfce", "ljzfce"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getljzfce","column:cwsjzfje"));//累计实际支付差额
        cols.add(new SplitTableColBean("30%", "尚未支付合同金额", "swzfhtje", "swzfhtje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getswzfwcje","column:ygjsje+column:cwsjzfje"));//尚未支付合同金额
        cols.add(new SplitTableColBean("30%", "备注", "bz", "bz"));//备注
        SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
        SplitTableBean tableBean = new SplitTableBean("fnaQCContract1","none",PageIdConst.getPageSize("fnaQCContract1",user.getUID(),PageIdConst.FNA),
                "fnaQCContract1",backFields,fromSql,sqlWhere,orderBy,sqlprimarykey,"Asc",cols);
        tableBean.setSqlisdistinct("true");
        tableBean.setOperates(splitTableOperateBean);

        datas.putAll(SplitTableUtil.makeListDataResult(tableBean));
        datas.put("status", "1");
        return datas;
    }

    public String getsjkmid(String km){
    	String result = "";
    	String sql = "(";
    	String[] kmArray = km.split(",");
		for(int i = 0;i<kmArray.length;i++){
			if(i>0){
				sql+=" or ";
			}
			sql += " a.ACCOUNTCODE like '%\\_"+kmArray[i]+"' escape '\\' "; 
		}
		sql+=")";
    	RecordSet rs = new RecordSet();
    	RecordSet rs2 = new RecordSet();
    	rs.executeQuery("select * from fnaaccountDtl where tableType = ? ", FnaAccTypeConstant.BUDGET_SUBJECT);
        while(rs.next()){
        	String tableName = Util.null2String(rs.getString("tableName"));
        	new BaseBean().writeLog("tableName:"+tableName);
        	if((!"".equals(tableName)) ){
        		String subjectCodelikesql = "";
        		String sql2 = "select a.subjectCode from "+tableName+" a where 1=1 and SUBJECTLEVEL>1 and SUBJECTLEVEL<5 and "+sql;
        		rs2.executeQuery(sql2);
        		while(rs2.next()){
        			String subjectCode = Util.null2String(rs2.getString("subjectCode"));
        			if(!"".equals(subjectCode)){
                        if(!"".equals(subjectCodelikesql)){
                            subjectCodelikesql+=" or ";
                        }
                        subjectCodelikesql+=" a.subjectCode like '"+subjectCode+"%'";
                    }

        		}
        		if(!"".equals(subjectCodelikesql)){
        			String sql3 = "select a.id from "+tableName+" a where 1=1 and ("+subjectCodelikesql+") and a.SUBJECTLEVEL = 5 ";
            		rs2.executeQuery(sql3);
            		while(rs2.next()){
            			String id = Util.null2String(rs2.getString("id"));
            			if(!"".equals(result)){
            				result+=",";
            			}
            			result+="'"+id+"'";
            		}
        		}
        		
	        }
        }
        return result;
    }


    public String getsjkmid2(String name){
        String result = "";
        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();
        rs.executeQuery("select * from fnaaccountDtl where tableType = ? ", FnaAccTypeConstant.BUDGET_SUBJECT);
        while(rs.next()){
            String tableName = Util.null2String(rs.getString("tableName"));
            new BaseBean().writeLog("tableName:"+tableName);
            if((!"".equals(tableName)) ){
                String sql3 = "select a.id from "+tableName+" a where 1=1 and SUBJECTNAME like '%"+name+"%' and a.SUBJECTLEVEL = 5 ";
                rs2.executeQuery(sql3);
                while(rs2.next()){
                    String id = Util.null2String(rs2.getString("id"));
                    if(!"".equals(result)){
                        result+=",";
                    }
                    result+="'"+id+"'";
                }

            }
        }
        return result;
    }


}
