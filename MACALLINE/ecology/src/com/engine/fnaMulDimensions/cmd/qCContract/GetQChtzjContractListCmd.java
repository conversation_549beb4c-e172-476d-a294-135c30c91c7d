package com.engine.fnaMulDimensions.cmd.qCContract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.bean.SplitTableOperateBean;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;

import weaver.general.PageIdConst;
import weaver.hrm.User;
import weaver.upgradetool.wscheck.Util;

public class GetQChtzjContractListCmd extends AbstractCommonCommand<Map<String, Object>>{

	
	/**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetQChtzjContractListCmd(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> datas = new HashMap<String, Object>();
        String id = Util.null2String(params.get("id"));
        
        
        
        
        String backFields = " sqr,sqrq,htje,htsp lc1,htsp lc2 ";
        String fromSql = " uf_htxx ";
        String sqlWhere = " where 1=1 and id = "+id;
        String orderBy = " htsp ";
        //String groupby = "";
        String sqlprimarykey = " ";
        
        List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
        cols.add(new SplitTableColBean("true","lc2"));//名称
        cols.add(new SplitTableColBean("25%", "申请人", "sqr", "sqr"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getsqr"));//项目名称
        /*cols.add(new SplitTableColBean("25%", "申请部门", "sqbm", "sqbm"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getsqbm"));*/
        cols.add(new SplitTableColBean("25%", "申请日期", "sqrq", "sqrq"));//合同名称
        cols.add(new SplitTableColBean("25%", "金额", "htje", "htje"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getamountdf"));//合同名称
        cols.add(new SplitTableColBean("25%", "相关流程", "lc1", "lc1"
        		,"com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getlcmc"));//合同名称
        
        SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
        


        SplitTableBean tableBean = new SplitTableBean("fnaQChtzjContract","none",PageIdConst.getPageSize("fnaQChtzjContract",user.getUID(),PageIdConst.FNA),
                "fnaQChtzjContract",backFields,fromSql,sqlWhere,orderBy,sqlprimarykey,"Asc",cols);

        tableBean.setSqlisdistinct("true");
        tableBean.setOperates(splitTableOperateBean);

        datas.putAll(SplitTableUtil.makeListDataResult(tableBean));
        datas.put("status", "1");
        return datas;
    }

}
