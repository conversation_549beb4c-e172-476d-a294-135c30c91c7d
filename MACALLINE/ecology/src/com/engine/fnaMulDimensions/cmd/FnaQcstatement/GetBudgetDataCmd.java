package com.engine.fnaMulDimensions.cmd.FnaQcstatement;

import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.bean.SplitTableOperateBean;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/7/16-9:29
 * @description : 预算相关数据穿透
 */
public class GetBudgetDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetBudgetDataCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
        String queryFields;
        String orderBySql = "";

        queryFields = getQueryFields();
        String columNames = getColumnFields();
        String fromSql = "( " + getTotalSql() + " )";
        String whereSql = " where 1=1 ";

        SplitTableBean tableBean = new SplitTableBean("budgetData", "none", PageIdConst.getPageSize("studyforum", user.getUID(), PageIdConst.FNA),
                "budgetData", queryFields, fromSql, whereSql, orderBySql, "", "", "Asc", addCols(queryFields, columNames));

        tableBean.setSqlisdistinct("true");
        tableBean.setOperates(splitTableOperateBean);
        Map<String, Object> result = new HashMap<>(SplitTableUtil.makeListDataResult(tableBean));
        result.put("status", "1");
        return result;
    }

    /**
     * 获取总sql
     *
     * @return
     */
    public String getTotalSql() {
        String rst;
        String queryType = Util.null2String(params.get("type"));
        String subjectId = Util.null2String(params.get("subjectId"));
        switch (queryType) {
            case "1":
                rst = getChangedBudget(subjectId);
                break;
            case "2":
                rst = getWillChangeBudget(subjectId);
                break;
            case "3":
                rst = getUsedBudget(subjectId);
                break;
            case "4":
                rst = getWillUseBudget(subjectId);
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }

    /**
     * 获取已变更预算sql
     *
     * @param subjectId
     * @return
     */
    private String getChangedBudget(String subjectId) {
        return "SELECT " +
                " hr.LASTNAME AS APPLIER, " +
                " d.DEPARTMENTNAME AS APPLY_DEPT, " +
                " TBA.APPLY_DATE, " +
                " TBA.AMOUNT, " +
                " TBA.requestid AS REQUESTID  " +
                "FROM " +
                " ( " +
                " SELECT " +
                "  b.sqr AS APPLIER, " +
                "  b.sqbm AS APPLY_DEPT, " +
                "  b.sqrq AS APPLY_DATE, " +
                "  a.dzje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_73_dt1 a " +
                "  LEFT JOIN formtable_main_73 b ON a.mainid = b.id " +
                "  LEFT JOIN workflow_requestbase c ON b.requestid = c.requestid  " +
                " WHERE " +
                "  c.currentnodetype = 3" +
                "  AND a.km = '" + subjectId + "' UNION ALL " +
                " SELECT " +
                "  b.sqr AS APPLIER, " +
                "  b.sqbm AS APPLY_DEPT, " +
                "  b.sqrq AS APPLY_DATE, " +
                "  a.djje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_73_dt2 a " +
                "  LEFT JOIN formtable_main_73 b ON a.mainid = b.id " +
                "  LEFT JOIN workflow_requestbase c ON b.requestid = c.requestid  " +
                " WHERE " +
                "  c.currentnodetype  =3 " +
                "  AND a.km = '" + subjectId + "' UNION ALL " +
                " SELECT " +
                "  a.sqr AS APPLIER, " +
                "  a.sqbm AS APPLY_DEPT, " +
                "  a.sqrq AS APPLY_DATE, " +
                "  a.zbje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_72 a " +
                "  LEFT JOIN workflow_requestbase b ON a.requestid = b.requestid  " +
                " WHERE " +
                "  b.currentnodetype = 3 " +
                "  AND a.km = '" + subjectId + "'  " +
                " ) TBA " +
                " LEFT JOIN HRMRESOURCE hr ON ( hr.id = TBA.APPLIER ) " +
                " LEFT JOIN hrmdepartment d ON ( TBA.APPLY_DEPT = d.id )";
    }

    /**
     * 获取预已变更预算sql
     *
     * @param subjectId
     * @return
     */
    private String getWillChangeBudget(String subjectId) {
        return "SELECT " +
                " hr.LASTNAME AS APPLIER, " +
                " d.DEPARTMENTNAME AS APPLY_DEPT, " +
                " TBA.APPLY_DATE, " +
                " TBA.AMOUNT, " +
                " TBA.requestid AS REQUESTID  " +
                "FROM " +
                " ( " +
                " SELECT " +
                "  b.sqr AS APPLIER, " +
                "  b.sqbm AS APPLY_DEPT, " +
                "  b.sqrq AS APPLY_DATE, " +
                "  a.dzje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_73_dt1 a " +
                "  LEFT JOIN formtable_main_73 b ON a.mainid = b.id " +
                "  LEFT JOIN workflow_requestbase c ON b.requestid = c.requestid  " +
                " WHERE " +
                "  c.currentnodetype > 0 and  c.currentnodetype < 3 " +
                "  AND a.km = '" + subjectId + "' UNION ALL " +
                " SELECT " +
                "  b.sqr AS APPLIER, " +
                "  b.sqbm AS APPLY_DEPT, " +
                "  b.sqrq AS APPLY_DATE, " +
                "  a.djje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_73_dt2 a " +
                "  LEFT JOIN formtable_main_73 b ON a.mainid = b.id " +
                "  LEFT JOIN workflow_requestbase c ON b.requestid = c.requestid  " +
                " WHERE " +
                "  c.currentnodetype > 0 and  c.currentnodetype < 3 " +
                "  AND a.km = '" + subjectId + "' UNION ALL " +
                " SELECT " +
                "  a.sqr AS APPLIER, " +
                "  a.sqbm AS APPLY_DEPT, " +
                "  a.sqrq AS APPLY_DATE, " +
                "  a.zbje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_72 a " +
                "  LEFT JOIN workflow_requestbase b ON a.requestid = b.requestid  " +
                " WHERE " +
                "  b.currentnodetype > 0 and  b.currentnodetype < 3  " +
                "  AND a.km = '" + subjectId + "'  " +
                " ) TBA " +
                " LEFT JOIN HRMRESOURCE hr ON ( hr.id = TBA.APPLIER ) " +
                " LEFT JOIN hrmdepartment d ON ( TBA.APPLY_DEPT = d.id )";
    }

    /**
     * 获取已使用预算sql
     *
     * @param subjectId
     * @return
     */
    private String getUsedBudget(String subjectId) {
        return " SELECT " +
                " TBA.CONTRACT_NAME, " +
                " hr.LASTNAME AS APPLIER, " +
                " TBA.APPLY_DATE, " +
                " TBA.AMOUNT, " +
                " TBA.REQUESTID  " +
                " FROM " +
                " ( " +
                " SELECT " +
                " b.htmc AS CONTRACT_NAME, " +
                " b.sqr AS APPLIER, " +
                " b.sqrq AS APPLY_DATE, " +
                " a.ygjsje AS AMOUNT, " +
                " nvl( b.htsp, '' ) || " +
                " nvl2( c.htbgsqlc, ',' || c.htbgsqlc, '' ) || " +
                " nvl2( d.qzbgsqlc, ',' || d.qzbgsqlc, '' ) || " +
                " nvl2( e.htjslc, ',' || e.htjslc, '' ) AS REQUESTID  " +
                " FROM " +
                " uf_htxx_dt1 a " +
                " left join uf_htxx b ON a.mainid = b.id " +
                "  left join ( " +
                "   select htxx,listagg(htbgsqlc,',')  " +
                "   within group(order by htbgsqlc desc) as htbgsqlc " +
                "   from uf_htbgxxb " +
                "   group by htxx " +
                "  ) c ON (b.id = c.htxx) " +
                "  left join ( " +
                "   select htxx,listagg(qzbgsqlc,',')  " +
                "   within group(order by qzbgsqlc desc) as qzbgsqlc " +
                "   from uf_qzbgxxb " +
                "   group by htxx " +
                "  ) d ON (b.id = d.htxx) " +
                "  left join ( " +
                "   select ht,listagg(htjslc,',')  " +
                "   within group(order by htjslc desc) as htjslc " +
                "   from uf_jsxx " +
                "   group by ht " +
                "  ) e ON (b.id = e.ht ) " +
                " WHERE " +
                " a.km = '" + subjectId + "' UNION ALL " +
                " SELECT " +
                " b.fymc AS CONTRACT_NAME, " +
                " b.sqr AS APPLIER, " +
                " b.sqrq AS APPLY_DATE, " +
                " a.sqfyje AS AMOUNT, " +
                " to_char( b.fysqlc ) AS REQUESTID  " +
                " FROM " +
                " uf_fysqtz_dt1 a left join uf_fysqtz b ON a.mainid = b.id  " +
                " WHERE " +
                " a.km = '" + subjectId + "'  " +
                " ) TBA LEFT JOIN HRMRESOURCE hr ON ( " +
                " hr.id = TBA.APPLIER  " +
                " ) ";
    }

    /**
     * 获取预使用预算sql
     *
     * @param subjectId
     * @return
     */
    private String getWillUseBudget(String subjectId) {
        return " SELECT " +
                " TBA.CONTRACT_NAME, " +
                " hr.LASTNAME AS APPLIER, " +
                " d.DEPARTMENTNAME AS APPLY_DEPT, " +
                " TBA.APPLY_DATE, " +
                " TBA.AMOUNT, " +
                " TBA.requestid AS REQUESTID  " +
                " FROM " +
                " ( " +
                " SELECT " +
                " b.htmc AS CONTRACT_NAME, " +
                "  b.sqr AS APPLIER, " +
                "  b.sq AS APPLY_DEPT, " +
                "  b.sqrq AS APPLY_DATE, " +
                "  a.cdje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_119_dt1 a " +
                "  LEFT JOIN formtable_main_119 b ON a.mainid = b.id " +
                "  LEFT JOIN workflow_requestbase c ON b.requestid = c.requestid  " +
                " WHERE " +
                "  c.currentnodetype > 0 and  c.currentnodetype < 3 " +
                "  AND a.km = '" + subjectId + "' UNION ALL " +
                " SELECT " +
                "  c.requestname AS CONTRACT_NAME, " +
                "  b.sqr AS APPLIER, " +
                "  b.sqbm AS APPLY_DEPT, " +
                "  b.sqrq AS APPLY_DATE, " +
                "  a.sqfyje AS AMOUNT, " +
                "  b.requestid  " +
                " FROM " +
                "  formtable_main_98_dt1 a " +
                "  LEFT JOIN formtable_main_98 b ON a.mainid = b.id " +
                "  LEFT JOIN workflow_requestbase c ON b.requestid = c.requestid  " +
                " WHERE " +
                "  c.currentnodetype > 0 and  c.currentnodetype < 3 " +
                "  AND a.km = '" + subjectId + "'  " +
                " ) TBA " +
                " LEFT JOIN HRMRESOURCE hr ON ( hr.id = TBA.APPLIER ) " +
                " LEFT JOIN hrmdepartment d ON ( TBA.APPLY_DEPT = d.id )";
    }

    private List<SplitTableColBean> addCols(String colums, String columNames) {
        List<SplitTableColBean> cols = new ArrayList<>();
        String[] columsArr = colums.split(",");
        String[] columNamesArr = columNames.split(",");
        for (int i = 0; i < columsArr.length; i++) {
            if ("REQUESTID".equals(columsArr[i])) {
                cols.add(new SplitTableColBean("25%", columNamesArr[i], columsArr[i], columsArr[i]
                        , "com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getlcmc"));
            } else if ("AMOUNT".equals(columsArr[i])) {
                cols.add(new SplitTableColBean("25%", columNamesArr[i], columsArr[i], columsArr[i]
                        , "com.engine.util.CostUtil.getNumberValue", "column:" + columsArr[i]));
            } else {
                cols.add(new SplitTableColBean("25%", columNamesArr[i], columsArr[i], columsArr[i]));
            }
        }
        return cols;
    }


    public String getQueryFields() {
        String rst;
        String queryType = Util.null2String(params.get("type"));
        switch (queryType) {
            case "1":
            case "2":
                rst = "APPLIER,APPLY_DEPT,APPLY_DATE,AMOUNT,REQUESTID";
                break;
            case "3":
                rst = "CONTRACT_NAME,APPLIER,APPLY_DATE,AMOUNT,REQUESTID";
                break;
            case "4":
                rst = "CONTRACT_NAME,APPLIER,APPLY_DEPT,APPLY_DATE,AMOUNT,REQUESTID";
                break;
            default:
                rst = "";
                break;
        }
        return rst;

    }

    public String getColumnFields() {
        String rst;
        String queryType = Util.null2String(params.get("type"));
        switch (queryType) {
            case "1":
            case "2":
                rst = "申请人,申请部门,申请日期,金额,相关流程";
                break;
            case "3":
                rst = "合同/费用名称,申请人,申请日期,金额,相关流程";
                break;
            case "4":
                rst = "合同/费用名称,申请人,申请部门,申请日期,金额,相关流程";
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }
}
