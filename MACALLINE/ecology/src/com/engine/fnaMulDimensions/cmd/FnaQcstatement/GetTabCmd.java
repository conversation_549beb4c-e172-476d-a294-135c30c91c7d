package com.engine.fnaMulDimensions.cmd.FnaQcstatement;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fna.entity.TabBean;
import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;

import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.systeminfo.SystemEnv;

public class GetTabCmd extends AbstractCommonCommand<Map<String, Object>>{
	
	/**
     * 构造方法
     * @param params
     * @param user
     */
    public GetTabCmd(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        Map<String, Object> result = new HashMap<String, Object>();
        String accountId = Util.null2String(params.get("accountId"));
        String xmxx = Util.null2String(params.get("xmxx"));
		String xmxxs = Util.null2String(params.get("xmxxs"));
        List<TabBean> tabs = new LinkedList<TabBean>();
        try {
        	
        	RecordSet rs = new RecordSet();
        	TabBean tab = new TabBean();
        	tab.setGroupid("0");
        	tab.setViewcondition(0);
        	tab.setTitle("全部");
            tabs.add(tab);
        	BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
        	if(("".equals(xmxx))||("0".equals(xmxx))){
        		
        		/*String tableName = budgetApprovalUtil.getTableName(accountId, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE);
        		StringBuffer queryBuffer = new StringBuffer();
                queryBuffer.append(" select a.approvaTypelName,a.approvalVersGroupId,b.xmxx  from ").append(tableName).append(" a "
                		+ " join fnaQc978507Init b on a.approvalVersGroupId = b.approvalVersGroupId ");
                queryBuffer.append(" where a.approvaTypelstatus = 1 and a.approvalFillDataSataus = 1 and a.apprvoalActivation = 1 and b.accountId = '"+accountId+"' ");
                rs.executeQuery(queryBuffer.toString());
                if(rs.next()){
                	xmxx = Util.null2String(rs.getString("xmxx"));
                }*/
        		TabBean tab1 = new TabBean();
        		tab1.setGroupid("1");
        		tab1.setViewcondition(1);
        		tab1.setTitle("土地费用");
                tabs.add(tab1);
                
                TabBean tab2 = new TabBean();
                tab2.setGroupid("2");
                tab2.setViewcondition(2);
                tab2.setTitle("开发前期费用");
                tabs.add(tab2);
                
                TabBean tab3 = new TabBean();
                tab3.setGroupid("3");
                tab3.setViewcondition(3);
                tab3.setTitle("建安费用");
                tabs.add(tab3);
                
                TabBean tab4 = new TabBean();
                tab4.setGroupid("4");
                tab4.setViewcondition(4);
                tab4.setTitle("管理费用");
                tabs.add(tab4);
                
                TabBean tab5 = new TabBean();
                tab5.setGroupid("5");
                tab5.setViewcondition(5);
                tab5.setTitle("财务费用");
                tabs.add(tab5);
        		
        	}else{
        		String subjectTableName = budgetApprovalUtil.getTableName(accountId, FnaAccTypeConstant.BUDGET_SUBJECT);
            	String xmsubjectcode = "";
            	rs.executeQuery("select subjectcode from "+subjectTableName+" where accountCode = ?  ","xm_"+xmxx);
            	if(rs.next()){
            		xmsubjectcode = Util.null2String(rs.getString("subjectcode"));
            	}
            	if(!"".equals(xmsubjectcode)){
            		int i = 1;
            		//查询tab
                	rs.executeQuery("select id,SUBJECTNAME from "+subjectTableName+" "
                			+ " where subjectcode like '"+xmsubjectcode+"%' and SUBJECTLEVEL = 2 "
                			+ " order by SUBJECTCODE ");
                	while(rs.next()){
                		TabBean tab1 = new TabBean();
                        tab1.setGroupid(Util.null2String(rs.getString("id")));
                        tab1.setViewcondition(i++);
                        tab1.setTitle(Util.null2String(rs.getString("SUBJECTNAME")));
                        tabs.add(tab1);
                	}
            	}
        	}
        	
        	
            result.put("dtTab", tabs);
            result.put("status" , "1");
        }catch (Exception e){
            result.put("status" , "-1");
            result.put("errorInfo", e.getMessage());
        }
        return result;
    }

    /**
     * 日志
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

}
