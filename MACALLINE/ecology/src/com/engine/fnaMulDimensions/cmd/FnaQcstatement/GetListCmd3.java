/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.fnaMulDimensions.cmd.FnaQcstatement;

import com.api.browser.bean.*;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
import com.engine.fnaMulDimensions.util.SubjectUtil;
import com.engine.fnaMulDimensions.util.TemplateFillUtil;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;

import weaver.conn.RecordSet;
import weaver.fna.maintenance.BudgetfeeTypeComInfo;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;

/**  
* Title: GetListCmd  
* <AUTHOR>  
* @date 2020年12月23日  
* Description:  
*/
public class GetListCmd3 extends AbstractCommonCommand<Map<String, Object>>{
	/**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetListCmd3(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> datas = new HashMap<String, Object>();
		new BaseBean().writeLog("GetListCmd3Start................................");
        String accountId = Util.null2String(params.get("accountId"));
        String xmxx = Util.null2String(params.get("xmxx"));
		String xmxxs = Util.null2String(params.get("xmxxs"));
		String subject1 = Util.null2String(params.get("subject1"));
        
        String typeIds = "1,2,3,4,5";
        String[] typeArray = typeIds.split(",");
        TemplateFillUtil fillUtil = new TemplateFillUtil(user);
        String grofillDataTableName = fillUtil.getFillDataTableName(accountId,FnaAccTypeConstant.BUDGET_FILLDATA_GRO,typeIds,typeArray.length);
        RecordSet rs = new RecordSet();
        new BaseBean().writeLog("grofillDataTableName："+grofillDataTableName);
        if(!"".equals(grofillDataTableName)){
        	RecordSet rSet = new RecordSet();
        	BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
        	String subjectTableName = budgetApprovalUtil.getTableName(accountId, FnaAccTypeConstant.BUDGET_SUBJECT);
        	/*if(("".equals(xmxx))||("0".equals(xmxx))){
        		BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
        		String tableName = budgetApprovalUtil.getTableName(accountId, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE);
        		StringBuffer queryBuffer = new StringBuffer();
                queryBuffer.append(" select a.approvaTypelName,a.approvalVersGroupId,b.xmxx  from ").append(tableName).append(" a "
                		+ " join fnaQc978507Init b on a.approvalVersGroupId = b.approvalVersGroupId ");
                queryBuffer.append(" where a.approvaTypelstatus = 1 and a.approvalFillDataSataus = 1 and a.apprvoalActivation = 1 and b.accountId = '"+accountId+"' ");
                rSet.executeQuery(queryBuffer.toString());
                if(rSet.next()){
                	xmxx = Util.null2String(rSet.getString("xmxx"));
                }
        	}*/
        	new BaseBean().writeLog("xmxx11111111111："+xmxx);
        	
        	
        	
        	
        	String xmbrowser = Util.null2String(params.get("xmbrowser")); //高级搜索的项目
        	String yjkmserach = Util.null2String(params.get("km_4"));
        	String ejkmserach = Util.null2String(params.get("km_5"));
        	String sanjkmserach = Util.null2String(params.get("km_2"));
        	String sjkmserach = Util.null2String(params.get("km_3"));
        	String subjectLeaveserach = Util.null2String(params.get("subjectLeave"));
        	
        	String sqlWhere = " where 1=1 ";
        	String coltypeIds = "4,5,2,3";
        	String[] coltypeIdsArray = coltypeIds.split(",");
        	String number = grofillDataTableName.split("_")[1];
            String fillnumberCode = grofillDataTableName.split("_")[2];
        	String FnaExpenseInfo = "FnaExpenseInfo_"+number+"_"+fillnumberCode;
            
        	
        	//总预算
        	String zyssql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.budgetData) zys from "+grofillDataTableName+" a\r " +
        			"where a.isEffect = 1 and a.apprvoalActivation = 1 and a.approvalFillDataSataus = 1\r " +
        			"group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";
        	//已使用预算
        	String ysysql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.amount) sjysy from "+FnaExpenseInfo+" a\r " +
        			"where a.EXPENSESTAUS = 1 \r " +
        			"group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";
        	//预使用预算
        	String ysyyssql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.amount) ysyys from "+FnaExpenseInfo+" a\r " +
        			"where a.EXPENSESTAUS = 0 \r " +
        			"group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";
        	BaseBean baseBean = new BaseBean();
        	String gdbgjesql = "";
			new BaseBean().writeLog("2222222222222222222222222222222222222222222：");
			try {
				String formIds = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "formIds")).getBytes("ISO-8859-1"), "gbk")).trim();
				if("".equals(formIds)){
	        		datas.put("status", "-1");
	                datas.put("errorInfo", "请检查配置文件");
	                return datas;
	        	}
	        	String[] formIdsArray = formIds.split(",");
	        	
	        	for(int i = 0;i<formIdsArray.length;i++){
	        		String formId = formIdsArray[i];
	        		String yjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "yjkmfileName_"+formId)).getBytes("ISO-8859-1"), "gbk")).trim();
	        		String ejkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "ejkmfileName_"+formId)).getBytes("ISO-8859-1"), "gbk")).trim();
	        		String sanjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "sanjkmfileName_"+formId)).getBytes("ISO-8859-1"), "gbk")).trim();
	        		String sjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "sjkmfileName_"+formId)).getBytes("ISO-8859-1"), "gbk")).trim();
	        		String xmkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "xmkmfileName_"+formId)).getBytes("ISO-8859-1"), "gbk")).trim();
	        		String jefileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "jefileName_"+formId)).getBytes("ISO-8859-1"), "gbk")).trim();
	        		
	        		if(!"".equals(gdbgjesql)){
	        			gdbgjesql+=" UNION ALL ";
	        		}
	        		gdbgjesql+=" select a.requestId ";
	        		
	        		String[] formIdarray = formId.split("_"); //第一位formId,第二位：明细表
	        		String[] yjkmfileNameArray = yjkmfileName.split("_"); //第一位字段名，第二位：所属表单
	        		String[] ejkmfileNameArray = ejkmfileName.split("_");
	        		String[] sanjkmfileNameArray = sanjkmfileName.split("_");
	        		String[] sjkmfileNameArray = sjkmfileName.split("_");
	        		String[] xmkmfileNameArray = xmkmfileName.split("_");
	        		String[] jefileNameArray = jefileName.split("_");
	        		if("0".equals(yjkmfileNameArray[1])){
	        			gdbgjesql+=",a."+yjkmfileNameArray[0]+" BUDGETMEMBER_4 ";
	        		}else{
	        			gdbgjesql+=",b."+yjkmfileNameArray[0]+" BUDGETMEMBER_4 ";
	        		}
	        		if("0".equals(ejkmfileNameArray[1])){
	        			gdbgjesql+=",a."+ejkmfileNameArray[0]+" BUDGETMEMBER_5 ";
	        		}else{
	        			gdbgjesql+=",b."+ejkmfileNameArray[0]+" BUDGETMEMBER_5 ";
	        		}
	        		if("0".equals(sanjkmfileNameArray[1])){
	        			gdbgjesql+=",a."+sanjkmfileNameArray[0]+" BUDGETMEMBER_2 ";
	        		}else{
	        			gdbgjesql+=",b."+sanjkmfileNameArray[0]+" BUDGETMEMBER_2 ";
	        		}
	        		if("0".equals(sjkmfileNameArray[1])){
	        			gdbgjesql+=",a."+sjkmfileNameArray[0]+" BUDGETMEMBER_3 ";
	        		}else{
	        			gdbgjesql+=",b."+sjkmfileNameArray[0]+" BUDGETMEMBER_3 ";
	        		}
	        		if("0".equals(xmkmfileNameArray[1])){
	        			gdbgjesql+=",a."+xmkmfileNameArray[0]+" BUDGETMEMBER_1 ";
	        		}else{
	        			gdbgjesql+=",b."+xmkmfileNameArray[0]+" BUDGETMEMBER_1 ";
	        		}
	        		if("0".equals(jefileNameArray[1])){
	        			gdbgjesql+=",a."+jefileNameArray[0]+" bgje ";
	        		}else{
	        			gdbgjesql+=",b."+jefileNameArray[0]+" bgje ";
	        		}
	        		
	        		gdbgjesql+=" from formtable_main_"+formIdarray[0]+" a ";
	        		if(!"0".equals(formIdarray[1])){
	        			gdbgjesql+=" join FORMTABLE_MAIN_"+formIdarray[0]+"_DT"+formIdarray[1]+" b on a.id = b.MAINID ";
	        		}
	        	}
			} catch (UnsupportedEncodingException e) {
				new BaseBean().writeLog("333333333333333333333333333");
			}
        	new BaseBean().writeLog("4444444444444444444444444444444444444444444444444444444444444444：");
        	//审批中变更金额
        	String spzbgjesql = " select bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5,sum(bgtb.bgje) spzje from "
        			+"("+gdbgjesql+") bgtb join workflow_requestbase wr on wr.requestid = bgtb.requestId \r "
        			+"	where wr.currentnodetype > 0 and wr.currentnodetype < 3  "
        			+ " group by bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5 ";
        	//归档变更金额
        	String gdbgjeendsql = " select bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5,sum(bgtb.bgje) gdje from "
        			+"("+gdbgjesql+") bgtb join workflow_requestbase wr on wr.requestid = bgtb.requestId \r "
        			+"	where wr.currentnodetype = 3 "
        			+ " group by bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5 ";
        	
        	
        	
        	

        	
        	String orderBy = "";
        	String groupby = "";
        	String backFields = "  sum(jetb.zys) zys,sum(jetb.gdje) csys,sum(jetb.spzje) spzje ,sum(jetb.gdje) gdje ,sum(jetb.sjysy) sjysy,sum(jetb.ysyys) ysyys,sum(jetb.zys) syky  ";
        	String colsql = " zystb.zys,spzbgjetb.spzje,gdbgjetb.gdje,sjysytb.sjysy,ysyystb.ysyys,zystb.zys syky ";
        	for(int i = 0;i<coltypeIdsArray.length;i++){
            	backFields+=",jetb.BUDGETMEMBER_"+coltypeIdsArray[i]+"  ";
            	colsql+=",zystb.BUDGETMEMBER_"+coltypeIdsArray[i]+"  ";
            	if(!"".equals(orderBy)){
            		orderBy+=",";
            		groupby+=",";
            	}
            	groupby+="jetb.BUDGETMEMBER_"+coltypeIdsArray[i]+" ";
            	
            	orderBy+="jetb.BUDGETMEMBER_"+coltypeIdsArray[i];
            }
        	//if(("".equals(xmxx))||("0".equals(xmxx))){
            	backFields+="  ,jetb.BUDGETMEMBER_1  ";
            	colsql+="  ,zystb.BUDGETMEMBER_1  ";
            	groupby+=",jetb.BUDGETMEMBER_1 ";
            	
            	orderBy+=",jetb.BUDGETMEMBER_1";
    		//}
        	//groupby+=",jetb.zys,jetb.spzje,jetb.gdje,jetb.sjysy,jetb.ysyys ";
            String sqlprimarykey = "";
            
            
            String fromSql = " (select "+colsql+" from ( "+zyssql+" ) zystb \r "
        			+ " left join ("+ysysql+") sjysytb "
        					+ "  on zystb.BUDGETMEMBER_1 = sjysytb.BUDGETMEMBER_1 "
        					+ "  and zystb.BUDGETMEMBER_2 = sjysytb.BUDGETMEMBER_2 "
        					+ "  and zystb.BUDGETMEMBER_3 = sjysytb.BUDGETMEMBER_3 "
        					+ "  and zystb.BUDGETMEMBER_4 = sjysytb.BUDGETMEMBER_4 "
        					+ "  and zystb.BUDGETMEMBER_5 = sjysytb.BUDGETMEMBER_5"
        			+ " left join ("+ysyyssql+") ysyystb "
        					+ "  on zystb.BUDGETMEMBER_1 = ysyystb.BUDGETMEMBER_1 "
        					+ "  and zystb.BUDGETMEMBER_2 = ysyystb.BUDGETMEMBER_2 "
        					+ "  and zystb.BUDGETMEMBER_3 = ysyystb.BUDGETMEMBER_3 "
        					+ "  and zystb.BUDGETMEMBER_4 = ysyystb.BUDGETMEMBER_4 "
        					+ "  and zystb.BUDGETMEMBER_5 = ysyystb.BUDGETMEMBER_5"
        			+ " left join ("+spzbgjesql+") spzbgjetb "
		        			+ " on trim(''||zystb.BUDGETMEMBER_1) = ''||spzbgjetb.BUDGETMEMBER_1 "
							+ " and ''||zystb.BUDGETMEMBER_2 = ''||spzbgjetb.BUDGETMEMBER_2 "
							+ " and ''||zystb.BUDGETMEMBER_3 = ''||spzbgjetb.BUDGETMEMBER_3 "
							+ " and ''||zystb.BUDGETMEMBER_4 = ''||spzbgjetb.BUDGETMEMBER_4 "
							+ " and ''||zystb.BUDGETMEMBER_5 = ''||spzbgjetb.BUDGETMEMBER_5 "
        			+ " left join ("+gdbgjeendsql+") gdbgjetb "
        					+ " on trim(''||zystb.BUDGETMEMBER_1) = ''||gdbgjetb.BUDGETMEMBER_1 "
        					+ " and ''||zystb.BUDGETMEMBER_2 = ''||gdbgjetb.BUDGETMEMBER_2 "
        					+ " and ''||zystb.BUDGETMEMBER_3 = ''||gdbgjetb.BUDGETMEMBER_3 "
        					+ " and ''||zystb.BUDGETMEMBER_4 = ''||gdbgjetb.BUDGETMEMBER_4 "
        					+ " and ''||zystb.BUDGETMEMBER_5 = ''||gdbgjetb.BUDGETMEMBER_5 "
        			+ " where (zystb.zys <> 0 or  spzbgjetb.spzje <> 0 or  gdbgjetb.gdje <> 0  or sjysytb.sjysy <> 0 or ysyystb.ysyys <> 0 ) ";
            if((!"".equals(xmxx))&&(!"0".equals(xmxx))){
            	fromSql += " and zystb.BUDGETMEMBER_1 ='"+xmxx+"'";
    		}
            if((!"".equals(subject1))&&subject1.length()>6){
            	fromSql += " and zystb.BUDGETMEMBER_4 ='"+subject1+"'";
    		}else if(Util.getIntValue(subject1)>0){
    			String[] subjectNameArray = {"土地费用","开发前期费用","建安费用","管理费用","财务费用"};
    			fromSql += " and zystb.BUDGETMEMBER_4 in(select id from "+subjectTableName+" "
    					+ " where  SUBJECTNAME = '"+subjectNameArray[Util.getIntValue(subject1)-1]+"' and SUBJECTLEVEL = 2 )";
    		}
			if(!"".equals(xmxxs)){
				fromSql += " and zystb.BUDGETMEMBER_1 in("+xmxxs+")";
			}
            fromSql+= " ) jetb ";
            new BaseBean().writeLog("GetListCmd3sql : select "+backFields+" from "+fromSql+" "+sqlWhere+" group by "+groupby+" order by "+orderBy);
            Map<String,Object> yjmap = new HashMap<String,Object>();
            Map<String,String> xmxxmap = new HashMap<String,String>();
			String queryAllSql = " select "+backFields+" from "+fromSql+" "+sqlWhere+" group by "+groupby+" order by "+orderBy;
			rs.executeQuery(queryAllSql);
			new BaseBean().writeLog("rs................................");
			while(rs.next()){
				BigDecimal zys = getBigDecimalAmount(Util.null2String(rs.getString("zys"))); //总预算
				
				BigDecimal gdje = getBigDecimalAmount(Util.null2String(rs.getString("gdje"),"0.00")); //已变更预算
				
				BigDecimal csys = zys.subtract(gdje); //初始预算
				
				BigDecimal spzje = getBigDecimalAmount(Util.null2String(rs.getString("spzje"),"0.00")); //预变更预算
				
				BigDecimal sjysy = getBigDecimalAmount(Util.null2String(rs.getString("sjysy"),"0.00"));//已使用预算
				
				BigDecimal ysyys = getBigDecimalAmount(Util.null2String(rs.getString("ysyys"),"0.00"));//预使用预算
				
				BigDecimal syky = zys.subtract(sjysy).subtract(ysyys);//可用预算
				
				String xmxxBUDGETMEMBER_1 = Util.null2String(rs.getString("BUDGETMEMBER_1")); //项目信息
				String yjkmBUDGETMEMBER_4 = Util.null2String(rs.getString("BUDGETMEMBER_4")); //一级科目
				String ejkmBUDGETMEMBER_5 = Util.null2String(rs.getString("BUDGETMEMBER_5")); //二级科目
				String sjkmBUDGETMEMBER_2 = Util.null2String(rs.getString("BUDGETMEMBER_2")); //三级科目
				String ssjkmBUDGETMEMBER_3 = Util.null2String(rs.getString("BUDGETMEMBER_3")); //四级科目
				xmxxmap.put(yjkmBUDGETMEMBER_4, xmxxBUDGETMEMBER_1);
				Map<String,Object> yjmapvalue = (Map<String, Object>) yjmap.getOrDefault(yjkmBUDGETMEMBER_4, new HashMap<String,Object>());
				Map<String,Object> ejmapvalue = (Map<String, Object>) yjmapvalue.getOrDefault(ejkmBUDGETMEMBER_5, new HashMap<String,Object>());
				Map<String,Object> sjmapvalue = (Map<String, Object>) ejmapvalue.getOrDefault(sjkmBUDGETMEMBER_2, new HashMap<String,Object>());
				
				Map<String,String> ssjamountMap = new HashMap<String,String>();
				
				ssjamountMap.put("zys", ""+(zys.toString()));
				ssjamountMap.put("gdje", ""+(gdje.toString()));
				ssjamountMap.put("csys", ""+(csys.toString()));
				ssjamountMap.put("spzje", ""+(spzje.toString()));
				ssjamountMap.put("sjysy", ""+(sjysy.toString()));
				ssjamountMap.put("ysyys", ""+(ysyys.toString()));
				ssjamountMap.put("syky", ""+(syky.toString()));
				
				sjmapvalue.put(ssjkmBUDGETMEMBER_3,ssjamountMap);
				ejmapvalue.put(sjkmBUDGETMEMBER_2,sjmapvalue);
				yjmapvalue.put(ejkmBUDGETMEMBER_5,ejmapvalue);
				yjmap.put(yjkmBUDGETMEMBER_4,yjmapvalue);
			}
			
			String insertintosql = "insert into fnaqcstatement(xmxx,zys,csys,gdje,spzje,sjysy,ysyys,syky,subjectId,displayOrder,subjectLeve)  ";
			double displayOrder = 0.00;
			String yjselect = "";
			rs.executeUpdate("delete from fnaqcstatement ");
            for(Map.Entry<String, Object> yjva : yjmap.entrySet()){ //一级
            	String yjkmBUDGETMEMBER_4 = yjva.getKey();
            	String xmxxBUDGETMEMBER_1 = Util.null2String(xmxxmap.get(yjkmBUDGETMEMBER_4));
            	Map<String,Object> yjmapvalue = (Map<String, Object>) yjva.getValue();
            	BigDecimal yjzys = new BigDecimal("0.00");
            	BigDecimal yjgdje = new BigDecimal("0.00");
            	BigDecimal yjcsys = new BigDecimal("0.00");
            	BigDecimal yjspzje = new BigDecimal("0.00");
            	BigDecimal yjsjysy = new BigDecimal("0.00");
            	BigDecimal yjysyys = new BigDecimal("0.00");
            	BigDecimal yjsyky = new BigDecimal("0.00");
            	double yjdisplayOrder = displayOrder+1;
            	displayOrder++;
            	String ejselect = "";
            	for(Map.Entry<String, Object> ejva : yjmapvalue.entrySet()){ //二级
            		String ejkmBUDGETMEMBER_5 = ejva.getKey();
                	Map<String,Object> ejmapvalue = (Map<String, Object>) ejva.getValue();
                	BigDecimal ejzys = new BigDecimal("0.00");
                	BigDecimal ejgdje = new BigDecimal("0.00");
                	BigDecimal ejcsys = new BigDecimal("0.00");
                	BigDecimal ejspzje = new BigDecimal("0.00");
                	BigDecimal ejsjysy = new BigDecimal("0.00");
                	BigDecimal ejysyys = new BigDecimal("0.00");
                	BigDecimal ejsyky = new BigDecimal("0.00");
                	double ejdisplayOrder = displayOrder+1;
                	displayOrder++;
                	
                	
                	String sjselect = "";
                	for(Map.Entry<String, Object> sjva : ejmapvalue.entrySet()){//三级
                		String sjkmBUDGETMEMBER_2 = sjva.getKey();
                		Map<String,Object> sjmapvalue = (Map<String, Object>) sjva.getValue();
                		BigDecimal sjzys = new BigDecimal("0.00");
                    	BigDecimal sjgdje = new BigDecimal("0.00");
                    	BigDecimal sjcsys = new BigDecimal("0.00");
                    	BigDecimal sjspzje = new BigDecimal("0.00");
                    	BigDecimal sjsjysy = new BigDecimal("0.00");
                    	BigDecimal sjysyys = new BigDecimal("0.00");
                    	BigDecimal sjsyky = new BigDecimal("0.00");
                		double sjdisplayOrder = displayOrder+1;
                    	displayOrder++;
                    	
                    	String ssjselect = "";
                		for(Map.Entry<String, Object> ssjva : sjmapvalue.entrySet()){
                			String ssjkmBUDGETMEMBER_3 = ssjva.getKey();
                			Map<String,String> ssjamountMap = (Map<String, String>) ssjva.getValue();
                			BigDecimal zys = getBigDecimalAmount(Util.null2String(ssjamountMap.get("zys"),"0.00")); //总预算
                        	BigDecimal gdje = getBigDecimalAmount(Util.null2String(ssjamountMap.get("gdje"),"0.00")); //已变更预算
                        	BigDecimal csys = getBigDecimalAmount(Util.null2String(ssjamountMap.get("csys"),"0.00")); //初始预算
                        	BigDecimal spzje = getBigDecimalAmount(Util.null2String(ssjamountMap.get("spzje"),"0.00")); //预变更预算
                        	BigDecimal sjysy = getBigDecimalAmount(Util.null2String(ssjamountMap.get("sjysy"),"0.00"));//已使用预算
                        	BigDecimal ysyys = getBigDecimalAmount(Util.null2String(ssjamountMap.get("ysyys"),"0.00"));//预使用预算
                        	BigDecimal syky = getBigDecimalAmount(Util.null2String(ssjamountMap.get("syky"),"0.00"));//可用预算
                        	
                        	double ssjdisplayOrder = displayOrder+1;
                        	displayOrder++;
                        	
                        	sjzys = sjzys.add(zys);
                        	sjgdje = sjgdje.add(gdje);
                        	sjcsys = sjcsys.add(csys);
                        	sjspzje = sjspzje.add(spzje);
                        	sjsjysy = sjsjysy.add(sjysy);
                        	sjysyys = sjysyys.add(ysyys);
                        	sjsyky = sjsyky.add(syky);
                        	
                        	
                        	ejzys = ejzys.add(zys);
                        	ejgdje = ejgdje.add(gdje);
                        	ejcsys = ejcsys.add(csys);
                        	ejspzje = ejspzje.add(spzje);
                        	ejsjysy = ejsjysy.add(sjysy);
                        	ejysyys = ejysyys.add(ysyys);
                        	ejsyky = ejsyky.add(syky);
                        	
                        	yjzys = yjzys.add(zys);
                        	yjgdje = yjgdje.add(gdje);
                        	yjcsys = yjcsys.add(csys);
                        	yjspzje = yjspzje.add(spzje);
                        	yjsjysy = yjsjysy.add(sjysy);
                        	yjysyys = yjysyys.add(ysyys);
                        	yjsyky = yjsyky.add(syky);
                        	
                        	if(!"".equals(ssjselect)){
                        		ssjselect += " UNION ALL ";
                        	}
                        	ssjselect += "select "+xmxxBUDGETMEMBER_1+","+(zys.toString())+","+(csys.toString())
                        			+","+(gdje.toString())+","+(spzje.toString())+","+(sjysy.toString())
                        			+","+(ysyys.toString())+","+(syky.toString())+",'"+ssjkmBUDGETMEMBER_3+"', "+ssjdisplayOrder+",'4级' from dual ";
                		}
        				
                    	if(!"".equals(sjselect)){
                    		sjselect += " UNION ALL ";
                    	}
                    	sjselect += "select "+xmxxBUDGETMEMBER_1+","+(sjzys.toString())+","+(sjcsys.toString())
                    			+","+(sjgdje.toString())+","+(sjspzje.toString())+","+(sjsjysy.toString())
                    			+","+(sjysyys.toString())+","+(sjsyky.toString())+",'"+sjkmBUDGETMEMBER_2+"', "+sjdisplayOrder+",'3级' from dual "
                    			+" UNION ALL "+ssjselect;
                    	
                	}
                	if(!"".equals(ejselect)){
                		ejselect += " UNION ALL ";
                	}
                	ejselect += "select "+xmxxBUDGETMEMBER_1+","+(ejzys.toString())+","+(ejcsys.toString())+","+(ejgdje.toString())
                			+","+(ejspzje.toString())+","+(ejsjysy.toString())+","+(ejysyys.toString())+","+(ejsyky.toString())
                			+",'"+ejkmBUDGETMEMBER_5+"', "+ejdisplayOrder+",'2级' from dual "
                			+" UNION ALL "+sjselect;
                	
            	}
            	if(!"".equals(yjselect)){
            		yjselect += " UNION ALL ";
            	}
//            	yjselect += "select "+xmxxBUDGETMEMBER_1+","+(yjzys.toString())+","+(yjcsys.toString())+","+(yjgdje.toString())+","+(yjspzje.toString())
//            			+","+(yjsjysy.toString())+","+(yjysyys.toString())+","+(yjsyky.toString())
//            			+",'"+yjkmBUDGETMEMBER_4+"',"+yjdisplayOrder+",'1级' from dual "
//            			+" UNION ALL "+ejselect ;

            	String insertSql = "select "+xmxxBUDGETMEMBER_1+","+(yjzys.toString())+","+(yjcsys.toString())+","+(yjgdje.toString())+","+(yjspzje.toString())
						+","+(yjsjysy.toString())+","+(yjysyys.toString())+","+(yjsyky.toString())
						+",'"+yjkmBUDGETMEMBER_4+"',"+yjdisplayOrder+",'1级' from dual "
						+" UNION ALL "+ejselect ;
				rs.executeUpdate(insertintosql+" "+insertSql);
            }


            
            
            
            
            List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
            String backFields1 = "xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,gdje,spzje,sjysy,ysyys,syky,displayOrder,FORDER";
            /*String fromSql1 = " fnaqcstatement a "
            		+ " join "+subjectTableName+" b on b.id = a.SUBJECTID "
            		+ " left join uf_km c on ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,4) "
            		+ " or ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,5) ";*/
            String fromSql1 = "(select TBA.xmxx,TBA.bm,TBA.id,TBA.SUBJECTNAME,TBA.subjectId,TBA.subjectLeve,TBA.zys,TBA.csys,TBA.gdje,TBA.spzje,TBA.sjysy,TBA.ysyys,TBA.syky,TBA.displayOrder,fs.DISPLAYORDER AS FORDER FROM("
            		+ " select  a.xmxx,c.bm,c.id,b.SUBJECTNAME,a.subjectId,a.subjectLeve,a.zys,"
            		+ " a.csys,a.gdje,a.spzje,a.sjysy,a.ysyys,a.syky,a.displayOrder  "
            		+ " from fnaqcstatement a  "
            		+ " join "+subjectTableName+" b on b.id = a.SUBJECTID"  
            		+ " left join uf_km c on ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,4) "
            		+ " where 1=1  and a.SUBJECTLEVE in('1级','2级','3级')"
            		+ " UNION all "
            		+ " select  a.xmxx,c.bm,c.id,b.SUBJECTNAME,a.subjectId,a.subjectLeve,a.zys,a.csys,a.gdje,a.spzje,a.sjysy,a.ysyys,a.syky,a.displayOrder  "
            		+ " from fnaqcstatement a  "
            		+ " join "+subjectTableName+" b on b.id = a.SUBJECTID  "
            		+ " left join uf_km c on ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,5)"
            		+ " where 1=1  and a.SUBJECTLEVE ='4级'"
            		+ " ) TBA left join fnabudgetsubject_1 fs on (TBA.subjectId = fs.id)) ";
            String sqlWhere1 = " where 1=1 ";
           // String orderBy1 = " xmxx,bm,displayOrder ";
			String orderBy1 = " xmxx,bm,FORDER ";
            String groupby1 = " xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,gdje,spzje,sjysy,ysyys,syky,displayOrder,FORDER ";
            //String sqlprimarykey1 = " subjectId ";
            if(!"".equals(xmbrowser)){
            	String[] xmbrowserArray = xmbrowser.split(",");
        		String xmbrowserwherein = "";
        		for(int i = 0;i<xmbrowserArray.length;i++){
    				if(!"".equals(xmbrowserwherein)){
    					xmbrowserwherein+=",";
    				}
    				xmbrowserwherein+="'"+xmbrowserArray[i]+"'";
    			}
            	sqlWhere1 += " and xmxx in( "+xmbrowserwherein+" )";
            }
            if("".equals(subjectLeaveserach)||"0".equals(subjectLeaveserach)){
            	if("".equals(sjkmserach)){
            		if(!"".equals(sanjkmserach)){
            			subjectLeaveserach = "3";
            		}else{
            			if(!"".equals(ejkmserach)){
            				subjectLeaveserach = "2";
            			}else{
            				if(!"".equals(yjkmserach)){
                				subjectLeaveserach = "1";
                			}
            			}
            		}
            	}
            }
            if("1".equals(subjectLeaveserach)){  //判断科目等级，一级显示一级科目，二级显示一二级科目，三级显示一二三级科目
            	sqlWhere1 += " and subjectLeve = '1级' ";
            	//如果只显示一级，则只查一级相关科目
            	if(!"".equals(yjkmserach)){
            		/*String[] yjkmserachArray = yjkmserach.split(",");
            		String yjkmwherein = "";
            		for(int i = 0;i<yjkmserachArray.length;i++){
        				if(!"".equals(yjkmwherein)){
        					yjkmwherein+=",";
        				}
        				yjkmwherein+="'"+yjkmserachArray[i]+"'";
        			}*/
            		String yj = getSupSubjectAllId( subjectTableName, yjkmserach, "2");
            		sqlWhere1 += " and subjectId in("+yj+")";
            	}
            }else if("2".equals(subjectLeaveserach)){
            	sqlWhere1 += " and subjectLeve in('1级' ,'2级') ";
            	//如果显示二级科目，则查询二级相关科目及对应科目的一级科目
            	if(!"".equals(ejkmserach)){
            		
            		/*String[] ejkmserachArray = ejkmserach.split(",");
            		String ejkmwherein = "";
            		for(int i = 0;i<ejkmserachArray.length;i++){
        				if(!"".equals(ejkmwherein)){
        					ejkmwherein+=",";
        				}
        				ejkmwherein+="'"+ejkmserachArray[i]+"'";
        			}*/
            		String ej = getSupSubjectAllId( subjectTableName, ejkmserach, "3");
            		//String kmserachids = getSupSubjectAllId2( ejkmserach); 
            		sqlWhere1 += " and subjectId in('-1',"+ej+")";
            	}
            }else if("3".equals(subjectLeaveserach)){
            	sqlWhere1 += " and subjectLeve in('1级' ,'2级' ,'3级') ";
            	if(!"".equals(sanjkmserach)){
            		/*String[] sanjkmserachArray = sanjkmserach.split(",");
            		String sanjkmwherein = "";
            		for(int i = 0;i<sanjkmserachArray.length;i++){
            			if(!"".equals(sanjkmwherein)){
            				sanjkmwherein+=",";
            			}
            			sanjkmwherein+="'"+sanjkmserachArray[i]+"'";
            		}*/
            		String sj = getSupSubjectAllId( subjectTableName, sanjkmserach, "4");
            		//String kmserachids = getSupSubjectAllId2( sanjkmserach);
                	sqlWhere1 += " and subjectId in('-1',"+sj+")";
            	}
            }else{
            	//sqlWhere1 += " and subjectLeve in('1级' ,'2级' ,'3级') ";
            	if(!"".equals(sjkmserach)){
            		/*String[] sjkmserachArray = sjkmserach.split(",");
            		String sjkmwherein = "";
            		for(int i = 0;i<sjkmserachArray.length;i++){
            			if(!"".equals(sjkmwherein)){
            				sjkmwherein+=",";
            			}
            			sjkmwherein+="'"+sjkmserachArray[i]+"'";
            		}*/
            		String sj = getSupSubjectAllId( subjectTableName, sjkmserach, "5");
            		//String kmserachids = getSupSubjectAllId2byname( sjkmserach);
                	sqlWhere1 += " and subjectId in('-1',"+sj+")";
            	}
            }
            
            //添加逻辑，关联预算表取成本中心的表名，获取到对应成本中心的数据


			fromSql1+= " ";
            

            if("".equals(xmxx)||"0".equals(xmxx)){
            	cols.add(new SplitTableColBean("25%", "项目", "xmxx", "xmxx"
                		,"com.engine.util.CostUtil.getxmmcbs",""+number+"+column:subjectLeve"));//成本编码
    		}
			cols.add(new SplitTableColBean("true","subjectId"));//科目id
            cols.add(new SplitTableColBean("25%", "成本编码", "bm", "bm"
            		,"com.engine.util.CostUtil.bmbsbs","column:subjectLeve"));//成本编码
            cols.add(new SplitTableColBean("25%", "科目名称", "SUBJECTNAME", "SUBJECTNAME"

            		,"com.engine.util.CostUtil.bs","column:subjectLeve"));//科目名称

            cols.add(new SplitTableColBean("25%", "科目等级", "subjectLeve", "subjectLeve"
            		,"com.engine.util.CostUtil.bs","column:subjectLeve"));//科目等级
            cols.add(new SplitTableColBean("25%", "总预算", "zys", "zys"
            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//名称
            cols.add(new SplitTableColBean("30%", "初始预算", "csys", "csys"
            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));
            cols.add(new SplitTableColBean("30%", "已变更预算", "gdje", "gdje"
            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
			cols.add(new SplitTableColBean("30%", "预变更预算", "spzje", "spzje"
					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
            
            cols.add(new SplitTableColBean("30%", "已使用预算", "sjysy", "sjysy"
            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
            cols.add(new SplitTableColBean("30%", "预使用预算", "ysyys", "ysyys"
            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
            cols.add(new SplitTableColBean("30%", "可用预算", "syky", "syky"
            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
            
            /*cols.add(new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(24664, user.getLanguage()), "bearerId", "bearerId",
            "com.engine.fnaMulDimensions.util.TableColTransmethod.getBudgetBearerRelation","column:accountNumber+"+user.getLanguage()+""));*///关联对象

            SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
            


            SplitTableBean tableBean = new SplitTableBean("fnabudgetqcDf3","none",PageIdConst.getPageSize("fnabudgetqcDf3",user.getUID(),PageIdConst.FNA),
                    "fnabudgetqcDf3",backFields1,fromSql1,sqlWhere1,orderBy1,groupby1,sqlprimarykey,"Asc",cols);

            tableBean.setSqlisdistinct("true");
            tableBean.setOperates(splitTableOperateBean);

            datas.putAll(SplitTableUtil.makeListDataResult(tableBean));
            datas.put("status", "1");
        } else {
            datas.put("status", "-1");
            datas.put("errorInfo", "TableName is Null");
        }
        return datas;
    }
	
	
	
	public BigDecimal getBigDecimalAmount(String value){
		BigDecimal result = new BigDecimal("0.00");
		try{
			result = new BigDecimal(value);
		}catch(Exception e){
			result = new BigDecimal("0.00");
		}
		return result;
	}
	
	public String getSupSubjectAllId(String subjectTableName,String queryid,String subjectLeave){
		RecordSet rs = new RecordSet();
		Map<Integer,Integer> codeMap = new SubjectUtil().getSubjectCodeRule(); //科目编码的位数
		int first_old = codeMap.get(1);
    	int second_old = codeMap.get(2);
    	int third_old = codeMap.get(3);
    	int fourth_old = codeMap.get(4);
		String subjectCodeall = "'-1'";
		if("".equals(queryid)){
			return "";
		}
		String sql = "select a.subjectCode from "+subjectTableName+" a "
				+ " join fnaqcstatement b on a.id = b.subjectId "
				+ " where  a.SUBJECTLEVEL = "+subjectLeave+" and ( ";
		if("5".equals(subjectLeave)){
			sql+="SUBJECTNAME like '%"+queryid+"%')";
		}else{
			String[] queryidArray = queryid.split(",");
			for(int i = 0;i<queryidArray.length;i++){
				if(i>0){
					sql+=" or ";
				}
				sql += " a.ACCOUNTCODE like '%\\_"+queryidArray[i]+"' escape '\\' "; 
			}
			sql+=")";
		}
		new BaseBean().writeLog(sql);
		rs.executeQuery(sql);
		while(rs.next()){
			String subjectCode = Util.null2String(rs.getString("subjectCode"));
			if(!"".equals(subjectCodeall)){
				subjectCodeall+=",";
			}
			if("2".equals(subjectLeave)){
				subjectCodeall+="'"+subjectCode+"'";
			}else if("3".equals(subjectLeave)){ //1,2
				subjectCodeall += "'"+subjectCode.substring(0,first_old+second_old)+"' ";
				subjectCodeall+=",'"+subjectCode+"'";
			}else if("4".equals(subjectLeave)){
				subjectCodeall += "'"+subjectCode.substring(0,first_old+second_old)+"' ";
				subjectCodeall += ",'"+subjectCode.substring(0,first_old+second_old+third_old)+"' ";
				subjectCodeall+=",'"+subjectCode+"'";
			}else if("5".equals(subjectLeave)){
				subjectCodeall += "'"+subjectCode.substring(0,first_old+second_old)+"' ";
				subjectCodeall += ",'"+subjectCode.substring(0,first_old+second_old+third_old)+"' ";
				subjectCodeall += ",'"+subjectCode.substring(0,first_old+second_old+third_old+fourth_old)+"' ";
				subjectCodeall+=",'"+subjectCode+"'";
			}
		}
		String ids = "";
		rs.executeQuery("select id from "+subjectTableName+" where subjectCode in("+subjectCodeall+")");
		while(rs.next()){
			String id = Util.null2String(rs.getString("id"));
			if(!"".equals(ids)){
				ids+=",";
			}
			ids+="'"+id+"'";
		}
		return ids;
	}
	
	public String getSupSubjectAllId2(String queryid){
		RecordSet rs = new RecordSet();
		String ids = "";
		StringBuffer sql = new StringBuffer();
		sql.append("select id,mc,sj from uf_km  ");
		/*sql.append("where (1=2  ");
		sql.append("or id in (").append(queryid).append(")  ");
		sql.append(")   ");*/
		sql.append(" start with id in ("+queryid+")  ");
		sql.append(" connect by prior sj = id ");
		new BaseBean().writeLog(sql.toString());
		rs.executeQuery(sql.toString());
		while(rs.next()){
			String id = Util.null2String(rs.getString("id"));
			if(!"".equals(ids)){
				ids+=",";
			}
			ids+=id;
		}
		return ids;
	}
	
	public String getSupSubjectAllId2byname(String queryname){
		RecordSet rs = new RecordSet();
		rs.executeQuery("select id,mc,cj from uf_km  where   fl = 0 and cj = 3 and mc like '%"+queryname+"%' ");
		String queryids = "";
		while(rs.next()){
			String id = Util.null2String(rs.getString("id"));
			if(!"".equals(queryids)){
				queryids+=",";
			}
			queryids+=id;
		}
		return getSupSubjectAllId2(queryids);
	}
}
