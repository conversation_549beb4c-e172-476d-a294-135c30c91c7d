package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class getnothavingPlanProjectCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    public getnothavingPlanProjectCmd(Map<String, Object> params, User user) {
        this.params=params;
        this.user=user;
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = "SELECT " +
                " a.*, " +
                " c.departmentname  " +
                "FROM " +
                " view_xmrwztfinal a " +
                " LEFT JOIN uf_xmxx b ON a.xmmc = b.xmmc " +
                " LEFT JOIN hrmdepartment c ON b.lstjb = c.id  " +
                "WHERE " +
                " spsl = 0  " +
                " AND zxsl = 0  " +
                "ORDER BY " +
                " b.lstjb";
        rs.executeQuery(sb);
        result.put("data",  ReportUtil.getJSONList( rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}
