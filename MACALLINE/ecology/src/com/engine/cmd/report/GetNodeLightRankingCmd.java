package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:36
 * @description : 节点亮灯排名
 */
public class GetNodeLightRankingCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetNodeLightRankingCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = " SELECT ROWNUM AS " +
                " SORT, " +
                " TBC.*  " +
                " FROM " +
                " ( " +
                " SELECT " +
                "  TBB.*, " +
                "  to_char( TBB.PECTG_LIGHTS, 'fm99999999990.00' ) || '%' AS PECTG_LIGHTS_STR  " +
                " FROM " +
                "  ( " +
                "  SELECT " +
                "   TBA.*, " +
                "   NVL2( TBA.TOTAL_LIGHTS, round( TBA.RY_LIGHTS / TBA.TOTAL_LIGHTS, 4 ) * 100, 0 ) AS PECTG_LIGHTS  " +
                "  FROM " +
                "   ( " +
                "   SELECT " +
                "    count( CASE WHEN c.SELECTVALUE = 3 AND a.ldlx = 1 THEN a.id END ) AS SECD_HUD, " +
                "    COUNT( CASE WHEN c.SELECTVALUE = 2 AND a.ldlx = 1 THEN a.id END ) AS FIRST_HUD, " +
                "    count( CASE WHEN c.SELECTVALUE = 1 AND a.ldlx = 1 THEN a.id END ) AS MILEPOST_HUD, " +
                "    count( CASE WHEN c.SELECTVALUE = 3 AND a.ldlx = 2 THEN a.id END ) AS SECD_HOD, " +
                "    count( CASE WHEN c.SELECTVALUE = 2 AND a.ldlx = 2 THEN a.id END ) AS FIRST_HOD, " +
                "    count( CASE WHEN c.SELECTVALUE = 1 AND a.ldlx = 2 THEN a.id END ) AS MILEPOST_HOD, " +
                "    count( CASE WHEN c.SELECTVALUE = 3 AND a.ldlx = 0 THEN a.id END ) AS SECD_LD, " +
                "    count( CASE WHEN c.SELECTVALUE = 2 AND a.ldlx = 0 THEN a.id END ) AS FIRST_LD, " +
                "    count( CASE WHEN c.SELECTVALUE = 1 AND a.ldlx = 0 THEN a.id END ) AS MILEPOST_LD, " +
                "   nvl( count( CASE WHEN c.SELECTVALUE  in (1,2,3) and a.ldlx IN ( 1, 2 ) THEN a.id END ), 0 ) AS RY_LIGHTS, " +
                " nvl( count( CASE WHEN c.SELECTVALUE  in (1,2,3) and a.ldlx IN ( 0, 1, 2 ) THEN a.id END ), 0 ) AS TOTAL_LIGHTS, " +
                " t.mc  " +
                " FROM " +
                " uf_ldjl a " +
                " LEFT JOIN uf_xmjh u ON ( a.jhjd = u.id ) " +
                " LEFT JOIN uf_tx t ON ( t.id = u.ZZBM ) " +
                " LEFT JOIN V_MODE_SELECT VS ON ( u.FORMMODEID = VS.MODEID AND VS.FIELDNAME = 'dj' ) " +
                " LEFT JOIN workflow_selectitem c ON ( u.dj = c.selectvalue AND c.fieldid = VS.FIELDID )  " +
                " WHERE " +
                " 1 = 1  " +
                " AND u.sfsc = 1  " +
               // " AND t.mc IN ( '设计', '项目公司', '总包', '商管' )  " +
                " AND TO_CHAR( to_date( a.ldrq, 'yyyy-mm-dd' ), 'yyyy' ) = to_char( SYSDATE, 'yyyy' )  " +
                " GROUP BY " +
                " t.mc  " +
                " ) TBA  " +
                " ) TBB  " +
                " ORDER BY " +
                " TBB.PECTG_LIGHTS  " +
                " ) TBC ";
        rs.executeQuery(sb);
        result.put("data", ReportUtil.getJSONList(rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}

