package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class getDoubleWeekmeeingGradesCmd  extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public getDoubleWeekmeeingGradesCmd(Map<String, Object> params, User user) {
        this.params=params;
        this.user=user;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = "SELECT\n" +
                "\tsfwc,\n" +
                "\tcount( * ) num \n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT\n" +
                "\t\tREGEXP_SUBSTR( TBA.zzbm, '[^,]+', 1, LEVEL ) zzbm,\n" +
                "\t\tjhwcsj,\n" +
                "\t\thyzt,\n" +
                "\t\tsfwc,\n" +
                "\t\twtlx \n" +
                "\tFROM\n" +
                "\t\t( SELECT * FROM uf_szhxx_dt1 dtt LEFT JOIN uf_szhxx mat ON mat.id = dtt.mainid ) TBA \n" +
                "\tWHERE\n" +
                "\t\t1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA.zzbm, '[^,]+' ) \n" +
                "\t\tAND ROWID = PRIOR ROWID \n" +
                "\t\tAND PRIOR DBMS_RANDOM.VALUE IS NOT NULL \n" +
                "\t) TAB\n" +
                "\tLEFT JOIN uf_zzbmszh zzb ON TAB.zzbm = zzb.id \n" +
                "WHERE\n" +
                "\tTAB.hyzt IN ( 0, 2 ) \n" +
                "\tAND zzb.zt = 0 \n" +
                "\tAND (TAB.sfwc=0 OR (TAB.sfwc=1 AND TAB.jhwcsj<to_char(sysdate,'yyyy-MM-dd')))\n" +
                "\tAND TAB.wtlx = 1 \n" +
                "GROUP BY\n" +
                "\tsfwc\n";
        rs.executeQuery(sb);
        result.put("data",  ReportUtil.getJSONList( rs.getArray(), rs.getData()));
        result.put("status", "1");
        result.put("Time",getDoublemeetTime());
        return result;
    }

    public String getDoublemeetTime(){
        String sql = "SELECT\n" +
                "\thyyt\n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT\n" +
                "\t\tREGEXP_SUBSTR( TBA.zzbm, '[^,]+', 1, LEVEL ) zzbm,\n" +
                "\t\tjhwcsj,\n" +
                "\t\thyzt,\n" +
                "\t\tsfwc,\n" +
                "\t\thyyt,\n" +
                "\t\twtlx \n" +
                "\tFROM\n" +
                "\t\t( SELECT * FROM uf_szhxx_dt1 dtt LEFT JOIN uf_szhxx mat ON mat.id = dtt.mainid ) TBA \n" +
                "\tWHERE\n" +
                "\t\t1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA.zzbm, '[^,]+' ) \n" +
                "\t\tAND ROWID = PRIOR ROWID \n" +
                "\t\tAND PRIOR DBMS_RANDOM.VALUE IS NOT NULL \n" +
                "\t) TAB\n" +
                "\tLEFT JOIN uf_zzbmszh zzb ON TAB.zzbm = zzb.id \n" +
                "WHERE\n" +
                "\tTAB.hyzt IN ( 0, 2 ) \n" +
                "\tAND zzb.zt = 0 \n" +
                "\tAND (TAB.sfwc=0 OR (TAB.sfwc=1 AND TAB.jhwcsj<to_char(sysdate,'yyyy-MM-dd')))\n" +
                "\tAND TAB.wtlx = 1 \n" +
                "\t";
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        if(recordSet.next()){
            return Util.null2String(recordSet.getString("hyyt"));
        }
        return "";
    }

}
