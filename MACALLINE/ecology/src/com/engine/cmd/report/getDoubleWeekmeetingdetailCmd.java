package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class getDoubleWeekmeetingdetailCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public getDoubleWeekmeetingdetailCmd(Map<String, Object> params, User user) {
        this.params=params;
        this.user=user;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = "SELECT\n" +
                "\tNAMETAB.MC,\n" +
                "\tNVL( TABA.NUM, 0 ) one,\n" +
                "\tNVL( TABB.NUM, 0 ) zero \n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT DISTINCT\n" +
                "\t\tzzb.mc mc \n" +
                "\tFROM\n" +
                "\t\t(\n" +
                "\t\tSELECT\n" +
                "\t\t\tREGEXP_SUBSTR( TBA.zzbm, '[^,]+', 1, LEVEL ) zzbm,\n" +
                "\t\t\thyzt,\n" +
                "\t\t\tjhwcsj,\n" +
                "\t\t\tsfwc,\n" +
                "\t\t\twtlx \n" +
                "\t\tFROM\n" +
                "\t\t\t( SELECT * FROM uf_szhxx_dt1 dtt LEFT JOIN uf_szhxx mat ON mat.id = dtt.mainid ) TBA \n" +
                "\t\tWHERE\n" +
                "\t\t\t1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA.zzbm, '[^,]+' ) \n" +
                "\t\t\tAND ROWID = PRIOR ROWID \n" +
                "\t\t\tAND PRIOR DBMS_RANDOM.VALUE IS NOT NULL \n" +
                "\t\t) TAB\n" +
                "\t\tLEFT JOIN uf_zzbmszh zzb ON TAB.zzbm = zzb.id \n" +
                "\tWHERE\n" +
                "\t\tTAB.hyzt IN ( 0, 2 ) \n" +
                "\t\tAND zzb.zt = 0 \n" +
                "\t\tAND (TAB.sfwc=0 OR (TAB.sfwc=1 AND TAB.jhwcsj<to_char(sysdate,'yyyy-MM-dd')))\n" +
                "\t\tAND TAB.wtlx = 1 \n" +
                "\t) NAMETAB\n" +
                "\tLEFT JOIN (\n" +
                "\tSELECT\n" +
                "\t\tzzb.mc mc,\n" +
                "\t\tsfwc sfwc,\n" +
                "\t\tcount( * ) num \n" +
                "\tFROM\n" +
                "\t\t(\n" +
                "\t\tSELECT\n" +
                "\t\t\tREGEXP_SUBSTR( TBA.zzbm, '[^,]+', 1, LEVEL ) zzbm,\n" +
                "\t\t\thyzt,\n" +
                "\t\t\tsfwc,\n" +
                "\t\t\twtlx,\n" +
                "\t\t\tJHWCSJ,\n" +
                "\t\t\tTBA.id \n" +
                "\t\tFROM\n" +
                "\t\t\t(\n" +
                "\t\t\tSELECT\n" +
                "\t\t\t\tdtt.zzbm,\n" +
                "\t\t\t\tdtt.sfwc,\n" +
                "\t\t\t\tdtt.wtlx,\n" +
                "\t\t\t\tdtt.JHWCSJ,\n" +
                "\t\t\t\tdtt.ID,\n" +
                "\t\t\t\tmat.hyzt \n" +
                "\t\t\tFROM\n" +
                "\t\t\t\tuf_szhxx_dt1 dtt\n" +
                "\t\t\t\tLEFT JOIN uf_szhxx mat ON mat.id = dtt.mainid \n" +
                "\t\t\t) TBA \n" +
                "\t\tWHERE\n" +
                "\t\t\t1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA.zzbm, '[^,]+' ) \n" +
                "\t\t\tAND id = PRIOR id \n" +
                "\t\t\tAND PRIOR DBMS_RANDOM.VALUE IS NOT NULL \n" +
                "\t\t) TAB\n" +
                "\t\tLEFT JOIN uf_zzbmszh zzb ON TAB.zzbm = zzb.id \n" +
                "\tWHERE\n" +
                "\t\tTAB.hyzt IN ( 0, 2 ) \n" +
                "\t\tAND zzb.zt = 0 \n" +
                "\t\tAND TAB.sfwc = 1 \n" +
                "\t\tAND TAB.jhwcsj<to_char(sysdate,'yyyy-MM-dd')\n" +
                "\t\tAND TAB.wtlx = 1 \n" +
                "\tGROUP BY\n" +
                "\t\tzzb.mc,\n" +
                "\t\tTAB.sfwc \n" +
                "\t) TABA ON TABA.mc = NAMETAB.mc\n" +
                "\tLEFT JOIN (\n" +
                "\tSELECT\n" +
                "\t\tzzb.mc mc,\n" +
                "\t\tsfwc sfwc,\n" +
                "\t\tcount( * ) num \n" +
                "\tFROM\n" +
                "\t\t(\n" +
                "\t\tSELECT\n" +
                "\t\t\tREGEXP_SUBSTR( TBA.zzbm, '[^,]+', 1, LEVEL ) zzbm,\n" +
                "\t\t\thyzt,\n" +
                "\t\t\tsfwc,\n" +
                "\t\t\twtlx,\n" +
                "\t\t\tTBA.id \n" +
                "\t\tFROM\n" +
                "\t\t\t(\n" +
                "\t\t\tSELECT\n" +
                "\t\t\t\tdtt.zzbm,\n" +
                "\t\t\t\tdtt.sfwc,\n" +
                "\t\t\t\tdtt.wtlx,\n" +
                "\t\t\t\tdtt.ID,\n" +
                "\t\t\t\tmat.hyzt \n" +
                "\t\t\tFROM\n" +
                "\t\t\t\tuf_szhxx_dt1 dtt\n" +
                "\t\t\t\tLEFT JOIN uf_szhxx mat ON mat.id = dtt.mainid \n" +
                "\t\t\t) TBA \n" +
                "\t\tWHERE\n" +
                "\t\t\t1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA.zzbm, '[^,]+' ) \n" +
                "\t\t\tAND id = PRIOR id \n" +
                "\t\t\tAND PRIOR DBMS_RANDOM.VALUE IS NOT NULL \n" +
                "\t\t) TAB\n" +
                "\t\tLEFT JOIN uf_zzbmszh zzb ON TAB.zzbm = zzb.id \n" +
                "\tWHERE\n" +
                "\t\tTAB.hyzt IN ( 0, 2 ) \n" +
                "\t\tAND zzb.zt = 0 \n" +
                "\t\tAND TAB.sfwc = 0 \n" +
                "\t\tAND TAB.wtlx = 1 \n" +
                "\tGROUP BY\n" +
                "\t\tzzb.mc,\n" +
                "\t\tTAB.sfwc \n" +
                "\t) TABB ON TABb.mc = NAMETAB.mc\n" +
                "\n";

        rs.executeQuery(sb);
        List<Map<String, Object>> arrayList =  ReportUtil.getJSONList( rs.getArray(), rs.getData());

        result.put("data", arrayList);
        result.put("status", "1");
        return result;
    }

}
