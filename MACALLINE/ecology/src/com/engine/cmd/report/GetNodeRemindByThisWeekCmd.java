package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:36
 * @description : TODO
 */
public class GetNodeRemindByThisWeekCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetNodeRemindByThisWeekCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("测试钉钉获取用户id:");
        baseBean.writeLog(user.getUID());
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = "SELECT " +
                " d.xmmc, " +
                " a.id, " +
                " a.rwbh, " +
                " a.taskName, " +
                " c.selectname, " +
                " a.taskFinishDate, " +
                " b.mc, " +
                " a.hud, " +
                " a.hod  " +
                "FROM " +
                " uf_xmjh a " +
                " LEFT JOIN uf_tx b ON a.zzbm = b.id " +
                " left join V_MODE_SELECT VS ON (a.FORMMODEID = VS.MODEID AND VS.FIELDNAME = 'dj') "+
                " LEFT JOIN workflow_selectitem c ON (a.dj = c.selectvalue AND c.fieldid = VS.FIELDID) " +
                " LEFT JOIN uf_xmxx d ON a.ssxmid = d.id  " +
                " WHERE " +
                " a.sfsc = '1'  " +
                " AND a.sfdqbb = '0'  " +
                " AND a.rwzt in ('1','2')  " +
                " AND ((( " +
                " to_date( a.taskFinishDate, 'yyyy-mm-dd hh24:mi:ss' ) >= trunc( next_day( SYSDATE - 8, 1 ) + 1 ) " +
                " OR a.hud > 0 " +
                " ) " +
                " AND to_date( a.taskFinishDate, 'yyyy-mm-dd hh24:mi:ss' ) <= trunc( next_day( SYSDATE - 8, 1 ) + 7 )) " +
                " ) ";

        rs.executeQuery(sb);
        result.put("data", ReportUtil.getJSONList(rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}

