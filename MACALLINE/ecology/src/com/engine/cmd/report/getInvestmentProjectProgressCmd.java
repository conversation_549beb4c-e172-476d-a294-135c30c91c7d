package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class getInvestmentProjectProgressCmd extends AbstractCommonCommand<Map<String, Object>> {

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public getInvestmentProjectProgressCmd(Map<String, Object> params, User user) {
        this.params=params;
        this.user=user;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = "SELECT\n" +
                "\tczlhsb,\n" +
                "\tczlmjb,\n" +
                "\tjclhsb,\n" +
                "\tJCLMJB,\n" +
                "\tXMMC,\n" +
                "\tCZLHSZCL,\n" +
                "\tCZLMJZCL,\n" +
                "\tJCLHSZCL,\n" +
                "\tJCLMJZCL \n" +
                "FROM\n" +
                "\tuf_xmxx \n" +
                "WHERE\n" +
                "\tkysj <= TO_CHAR ( extract( YEAR FROM SYSDATE + INTERVAL '1' YEAR ) ) AND kysj >= TO_CHAR ( extract( YEAR FROM SYSDATE ) ) \n" +
                "ORDER BY\n" +
                "\tsjkysj DESC";
        rs.executeQuery(sb);

        result.put("data",  ReportUtil.getJSONList( rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}
