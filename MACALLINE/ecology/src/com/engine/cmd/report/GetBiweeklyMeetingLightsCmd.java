package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/7/9-13:42
 * @description : 双周会亮灯
 */
public class GetBiweeklyMeetingLightsCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetBiweeklyMeetingLightsCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        StringBuilder sb = new StringBuilder();
        String startDate = Util.null2String(params.get("startDate"));
        String endDate = Util.null2String(params.get("endDate"));
        sb.append(" SELECT ");
        sb.append(" c.mc, ");
        sb.append(" c.id, ");
        sb.append(" count( CASE WHEN a.ldlx = 0 THEN a.id END ) AS CNT_GREEN, ");
        sb.append(" count( CASE WHEN a.ldlx = 1 THEN a.id END ) AS CNT_YELLOW, ");
        sb.append(" count( CASE WHEN a.ldlx = 2 THEN a.id END ) AS CNT_RED  ");
        sb.append(" FROM ");
        sb.append(" uf_ldjl a ");
        sb.append(" LEFT JOIN uf_xmjh b ON a.jhjd = b.id ");
        sb.append(" LEFT JOIN uf_tx c ON b.zzbm = c.id  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1  ");
        sb.append(" AND c.mc IS NOT NULL  ");
        if (!startDate.isEmpty()) {
            sb.append("AND to_date(a.ldrq,'yyyy-mm-dd') >= to_date('").append(startDate).append("','yyyy-mm-dd') ");
        }
        if (!endDate.isEmpty()) {
            sb.append(" AND to_date(a.ldrq,'yyyy-mm-dd') <= to_date('").append(endDate).append("','yyyy-mm-dd') ");
        }
        sb.append(" GROUP BY ");
        sb.append(" c.mc, ");
        sb.append(" c.id  ");
        sb.append(" ORDER BY ");
        sb.append(" c.id ");
        rs.executeQuery(sb.toString());
        result.put("data", ReportUtil.getJSONList(rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}
