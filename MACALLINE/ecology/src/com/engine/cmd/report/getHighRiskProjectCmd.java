package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class getHighRiskProjectCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public getHighRiskProjectCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        RecordSet recordSet = new RecordSet();
        String sql = "select TBL.* FROM (Select a.fxd as fxd,c.xmmc as xmmc,c.id from uf_fxpg_dt2 a " +
                "left join uf_fxpg b on a.mainid = b.id  " +
                "left join uf_xmxx c on b.xmmc = c.id " +
                "where c.xmzt in ('0','1') and a.fxdj='2' " +
                "union all  " +
                "Select a.fxd as fxd,c.xmmc as xmmc,c.id from uf_fxpg_dt3 a  " +
                "left join uf_fxpg b on a.mainid = b.id  " +
                "left join uf_xmxx c on b.xmmc = c.id " +
                "where c.xmzt in ('0','1') and a.fxdj='2' ) TBL " +
                " order by TBL.id ";
        recordSet.executeQuery(sql);
        result.put("data", ReportUtil.getJSONList(recordSet.getArray(), recordSet.getData()));
        result.put("status", "1");
        return result;
    }
}
