package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:36
 * @description : 节点亮灯排名
 */
public class GetDelayedCompletionCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetDelayedCompletionCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = "SELECT " +
                " TBA.*, " +
                " TBB.MIN_TIME, " +
                " xm.xmmc, " +
                " zm.MC  " +
                "FROM " +
                " ( " +
                " SELECT " +
                "  m.XM, " +
                "  m.zzbm, " +
                "  m.YJHWCSJ, " +
                "  m.DZHWCSJ, " +
                "  m.requestid, " +
                "  r.CURRENTNODEID,  " +
                "  j.TASKNAME "+
                " FROM " +
                "  formtable_main_34 m " +
                "  LEFT JOIN workflow_requestbase r ON ( m.requestid = r.requestid )  " +
                "  left join uf_xmjh j on (m.jhjd = j.id) "+
                " WHERE " +
                "  1 = 1  " +
                "  AND r.CURRENTNODETYPE = 3  " +
                " ) TBA " +
                " LEFT JOIN ( " +
                " SELECT " +
                "  min( to_date( f.RECEIVEDATE, 'yyyy-mm-dd' ) ) AS MIN_TIME, " +
                "  f.requestid, " +
                "  f.nodeid  " +
                " FROM " +
                "  workflow_node_fix_flowtime f  " +
                " GROUP BY " +
                "  f.requestid, " +
                "  f.nodeid  " +
                " ) TBB ON ( TBA.requestid = TBB.requestid AND TBA.CURRENTNODEID = TBB.nodeid ) " +
                " LEFT JOIN uf_xmxx xm ON ( TBA.XM = xm.id ) " +
                " LEFT JOIN uf_zzbmcb zm ON ( zm.id = TBA.zzbm ) "+
                " WHERE 1=1 ";
         String startDate = Util.null2String(params.get("startDate"));
         if(!startDate.isEmpty()){
             sb += " AND TBB.MIN_TIME >= to_date('"+startDate+"','yyyy-mm-dd') ";
         }
        String endDate = Util.null2String(params.get("endDate"));
        if(!endDate.isEmpty()){
            sb += " AND TBB.MIN_TIME <= to_date('"+endDate+"','yyyy-mm-dd') ";
        }
        rs.executeQuery(sb);
        result.put("data", ReportUtil.getJSONList(rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}

