package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:36
 * @description : TODO
 */
public class GetAnnualOpeningProjectsCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public GetAnnualOpeningProjectsCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }


    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = " SELECT " +
                " xmmc, " +
                " kysj, " +
                " sjkysj  " +
                " FROM " +
                " uf_xmxx t1 " +
                " WHERE " +
                " to_date( t1.kysj, 'yyyy-mm-dd hh24:mi:ss' ) >= trunc( SYSDATE, 'YYYY' )  " +
                " AND to_date( t1.kysj, 'yyyy-mm-dd hh24:mi:ss' ) <= add_months( trunc( SYSDATE, 'YYYY' ), 12 ) -1 " +
                " order by sjkysj desc ";
        rs.executeQuery(sb);
        result.put("data",  ReportUtil.getJSONList( rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}

