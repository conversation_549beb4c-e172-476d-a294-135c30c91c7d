package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class getPerformance4modeCmd extends AbstractCommonCommand<Map<String, Object>> {


    public getPerformance4modeCmd(Map<String, Object> params, User user) {
        this.params=params;
        this.user=user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet recordSet = new RecordSet();
        //uf_xmxx
        String sql = " SELECT taba.PM,taba.TJBM,xm.XMMC,taba.BCHJFS FROM uf_xmgsjxpm taba LEFT JOIN uf_xmxx xm on taba.xmmc=xm.id ";
        recordSet.executeQuery(sql);
        result.put("data",  ReportUtil.getJSONList( recordSet.getArray(), recordSet.getData()));
        return result;
    }
}
