package com.engine.cmd.report;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class getAllLongOverdueProjectCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public getAllLongOverdueProjectCmd(Map<String, Object> params, User user) {
        this.params=params;
        this.user=user;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sb = " " +
                "SELECT " +
                " zzb.MC mc ,COUNT(zzb.MC) num " +
                "FROM " +
                " ( " +
                " SELECT " +
                "  REGEXP_SUBSTR( TBA.zzbm, '[^,]+', 1, LEVEL ) zzbm, " +
                "  TBA.nr, " +
                "  TBA.XMMC AS UNIT, " +
                "  TBA.jhwcsj, " +
                "  TBA.dat  " +
                " FROM " +
                "  ( " +
                "  SELECT " +
                "   b.xmmc, " +
                "   a.nr, " +
                "   a.jhwcsj, " +
                "   a.zzbm, " +
                "   ROUND( TO_NUMBER( SYSDATE - to_date( a.jhwcsj, 'yyyy-mm-dd' ) ) ) dat  " +
                "  FROM " +
                "   uf_szhxx_dt1 a " +
                "   LEFT JOIN uf_szhxx b ON a.mainid = b.id  " +
                "  WHERE " +
                "   hyzt IN ( 0, 2 )  " +
                "   AND wtlx = 0  " +
                "   AND sfwc = 1  " +
                "   AND ROUND( TO_NUMBER( SYSDATE - to_date( a.jhwcsj, 'yyyy-mm-dd' ) ) ) >= 30  " +
                "  ) TBA  " +
                " WHERE " +
                "  1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA.zzbm, '[^,]+' )  " +
                "  AND ROWID = PRIOR ROWID  " +
                "  AND PRIOR DBMS_RANDOM.VALUE IS NOT NULL  " +
                " ) TAB " +
                " LEFT JOIN uf_zzbmszh zzb ON TAB.zzbm = zzb.ID  " +
                " GROUP BY zzb.MC " +
                " ORDER BY num DESC";
        rs.executeQuery(sb);
        result.put("data",  ReportUtil.getJSONList( rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}
