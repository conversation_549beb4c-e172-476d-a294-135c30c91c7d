package com.engine.cmd.qualityPlan;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSetTrans;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class RefreshOrderCmd extends AbstractCommonCommand<Map<String, Object>> {
    public RefreshOrderCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "1");
        String xm = Util.null2String(params.get("xm"));
        if ("".equals(xm)) {
            result.put("status", "-1");
            return result;
        }
        RecordSetTrans rst = new RecordSetTrans();
        //自动提交设为false
        rst.setAutoCommit(false);
        try {
            String sql = "UPDATE uf_zljhdg t  " +
                    "SET XH = ( " +
                    "SELECT " +
                    " rn  " +
                    "FROM " +
                    " ( SELECT ID, XH, row_number ( ) over ( ORDER BY XH ) rn FROM uf_zljhdg where xm = ? ) tt  " +
                    "WHERE " +
                    " t.id = tt.id  " +
                    " ) where xm = ? ";
            rst.executeUpdate(sql, xm, xm);
            rst.commit();
        } catch (Exception e) {
            //异常手动回滚
            rst.rollback();
            e.printStackTrace();
            result.put("status", "-1");
        }
        return result;
    }
}
