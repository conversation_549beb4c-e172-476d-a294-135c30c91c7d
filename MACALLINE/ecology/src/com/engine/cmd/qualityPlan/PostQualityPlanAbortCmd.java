package com.engine.cmd.qualityPlan;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.workflow.util.WorkFlowUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PostQualityPlanAbortCmd extends AbstractCommonCommand<Map<String, Object>> {
    public PostQualityPlanAbortCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        String newRequestId;
        Map<String, Object> result = new HashMap<>(1);
        String ids = Util.null2String(params.get("ids")).trim();
        BaseBean baseBean = new BaseBean();
        String workflowId = Util.null2String(baseBean.getPropValue("mac_workflow", "ualityPlanAbort_workflowId"));
        String[] idArray = ids.split(",");
        Map<String, Object> mainData = getMainData(idArray[0]);
        List<Map<String, Object>> itemData = getDetailData(ids);
        if (mainData.isEmpty() || itemData.isEmpty()) {
            return result;
        }

        //流程基本信息
        JSONObject baseInfoObj = new JSONObject();
        baseInfoObj.put("workflowId", workflowId);
        baseInfoObj.put("remark", "");
        baseInfoObj.put("requestName", Util.null2String(mainData.get("XMMC")) + "[质量计划大纲审批]");
        baseInfoObj.put("isNextFlow", "0");

        //主表数据
        JSONArray mainDataArray = new JSONArray();

        //申请人
        JSONObject dataObj1 = new JSONObject();
        dataObj1.put("name", "sqr");
        dataObj1.put("value", user.getUID());
        dataObj1.put("htmlType", "3");
        dataObj1.put("type", "");
        mainDataArray.add(dataObj1);

        //申请日期
        String today = DateUtil.today();
        JSONObject dataObj2 = new JSONObject();
        dataObj2.put("name", "sqrq");
        dataObj2.put("value", today);
        dataObj2.put("htmlType", "3");
        dataObj2.put("type", "");
        mainDataArray.add(dataObj2);

        //项目
        JSONObject dataObj3 = new JSONObject();
        dataObj3.put("name", "xm");
        dataObj3.put("value", mainData.get("XM"));
        dataObj3.put("htmlType", "3");
        dataObj3.put("type", "");
        mainDataArray.add(dataObj3);

        //明细表数据
        JSONArray detailDataArray = new JSONArray();

        StringBuilder uids = new StringBuilder();
        for (Map<String, Object> detailObj : itemData) {
            String jhjd_d = Util.null2String(detailObj.get("JHJD"));
            String qssj_d = Util.null2String(detailObj.get("QSSJ"));
            String zzsj_d = Util.null2String(detailObj.get("ZZSJ"));
            String zzbm1_d = Util.null2String(detailObj.get("ZZBM1"));
            String zzbm2_d = Util.null2String(detailObj.get("ZZBM2"));
            String ysbz_d = Util.null2String(detailObj.get("YSBZ"));
            String xh_d = Util.null2String(detailObj.get("XH"));
            String zljd_d = Util.null2String(detailObj.get("ID"));
            uids.append(",").append(zljd_d);

            JSONArray lineArray = new JSONArray();

            JSONObject linedataObj1 = new JSONObject();
            linedataObj1.put("name", "jhjd");
            linedataObj1.put("value", jhjd_d);
            linedataObj1.put("htmlType", "3");
            linedataObj1.put("type", "");
            lineArray.add(linedataObj1);

            JSONObject linedataObj2 = new JSONObject();
            linedataObj2.put("name", "qssj");
            linedataObj2.put("value", qssj_d);
            linedataObj2.put("htmlType", "3");
            linedataObj2.put("type", "");
            lineArray.add(linedataObj2);

            JSONObject linedataObj3 = new JSONObject();
            linedataObj3.put("name", "zzsj");
            linedataObj3.put("value", zzsj_d);
            linedataObj3.put("htmlType", "3");
            linedataObj3.put("type", "");
            lineArray.add(linedataObj3);

            JSONObject linedataObj4 = new JSONObject();
            linedataObj4.put("name", "zzbm1");
            linedataObj4.put("value", zzbm1_d);
            linedataObj4.put("htmlType", "3");
            linedataObj4.put("type", "");
            lineArray.add(linedataObj4);

            JSONObject linedataObj5 = new JSONObject();
            linedataObj5.put("name", "zzbm2");
            linedataObj5.put("value", zzbm2_d);
            linedataObj5.put("htmlType", "3");
            linedataObj5.put("type", "");
            lineArray.add(linedataObj5);

            JSONObject linedataObj6 = new JSONObject();
            linedataObj6.put("name", "ysbz");
            linedataObj6.put("value", ysbz_d);
            linedataObj6.put("htmlType", "2");
            linedataObj6.put("type", "");
            lineArray.add(linedataObj6);

            JSONObject linedataObj7 = new JSONObject();
            linedataObj7.put("name", "xh");
            linedataObj7.put("value", xh_d);
            linedataObj7.put("htmlType", "1");
            linedataObj7.put("type", "");
            lineArray.add(linedataObj7);

            JSONObject linedataObj8 = new JSONObject();
            linedataObj8.put("name", "zljd");
            linedataObj8.put("value", zljd_d);
            linedataObj8.put("htmlType", "3");
            linedataObj8.put("type", "");
            lineArray.add(linedataObj8);

            detailDataArray.add(lineArray);
        }

        newRequestId = WorkFlowUtil.createWorkflowRequest(user.getUID(), mainDataArray, baseInfoObj, detailDataArray);

        //把申请的质量计划大纲状态设置为“审批”
        String uidsStr = uids.toString();
        if (Convert.toInt(newRequestId) > 0) {
            if (!uidsStr.equals("")) {
                RecordSet rs = new RecordSet();
                uidsStr = uidsStr.substring(1);
                String updateSql = "UPDATE uf_zljhdg SET rwzt=1,yrwzt=rwzt WHERE id IN (" + uidsStr + ") ";
                rs.executeUpdate(updateSql);
            }
        }

        result.put("data", newRequestId);
        return result;
    }

    /**
     * 获取主表数据
     *
     * @param id
     * @return
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getMainData(String id) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        //获取主表信息
        String sql = "SELECT * FROM uf_zljhdg a join uf_xmxx b on a.xm=b.id WHERE a.id= ? ";
        rs.executeQuery(sql, id);
        if (rs.next()) {
            result = (Map<String, Object>) QueryResultUtil.getJSONList(rs.getArray(), rs.getData()).get(0);
        }
        return result;
    }

    /**
     * 获取明细数据
     *
     * @param id
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getDetailData(String id) {
        List<Map<String, Object>> result = new ArrayList<>();
        RecordSet rs = new RecordSet();
        //获取主表信息
        String sql = "SELECT * FROM uf_zljhdg where id in ( " + id + " ) ";
        rs.executeQuery(sql);
        if (rs.next()) {
            result = QueryResultUtil.getJSONList(rs.getArray(), rs.getData());
        }
        return result;
    }
}
