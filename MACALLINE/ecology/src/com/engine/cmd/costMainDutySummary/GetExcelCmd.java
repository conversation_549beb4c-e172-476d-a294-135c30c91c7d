package com.engine.cmd.costMainDutySummary;

import com.api.fna.util.ExcelOutUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.FnaAmountPointComInfo;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import com.engine.util.CostUtil;
import weaver.conn.RecordSet;
import weaver.file.ExcelFile;
import weaver.file.ExcelRow;
import weaver.file.ExcelSheet;
import weaver.file.ExcelStyle;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class GetExcelCmd extends AbstractCommonCommand<Map<String, Object>> {

    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 响应
     */
    private HttpServletResponse response;
    private int amountPoint;

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetExcelCmd(Map<String, Object> params, User user, HttpServletResponse response) {
        this.params = params;
        this.user = user;
        this.response = response;
        this.amountPoint = Util.getIntValue(new FnaAmountPointComInfo().getAmountPoint("" + FnaAccTypeConstant.AMOUNT_POINT_ID));
    }

    /**
     * Command类方法实现
     *
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String sql = "";
        try {
            GetListCmd getListCmd = new GetListCmd(params, user);
            result = getListCmd.getTotalSql();

            if ("-1".equals(result.get("status"))) {
                return result;
            }
            if ("1".equals(result.get("status"))) {
                sql = Util.null2String(result.get("sql"));
            }
            RecordSet rs = new RecordSet();
            CostUtil costUtil = new CostUtil();

            ExcelFile excelFile = new ExcelFile();
            excelFile.init();
            String sheetName = "主责条线汇总表";
            rs.executeQuery(sql);
            ExcelSheet es = new ExcelSheet();
            excelFile.addSheet(sheetName, es);
            es.initRowList(rs.getCounts() + 1);
            //            er.addStringValue("项目", "title");
            //            er.addStringValue("主责条线", "title");
            //            er.addStringValue("目标成本总价", "title");
            //            er.addStringValue("目标成本建面单方", "title");
            //            er.addStringValue("调整次数", "title");
            //            er.addStringValue("调整金额", "title");
            //            er.addStringValue("执行预算总价", "title");
            //            er.addStringValue("执行预算建面单方", "title");
            //            er.addStringValue("动态成本总价", "title");
            //            er.addStringValue("动态成本建面单方", "title");
            //            er.addStringValue("实际成本总价", "title");
            //            er.addStringValue("实际成本建面单方", "title");
            //            er.addStringValue("实际审批支出总价", "title");
            //            er.addStringValue("实际审批支出建面单方", "title");
            //            er.addStringValue("实际财务支出总价", "title");
            //            er.addStringValue("实际财务支出建面单方", "title");
            //            er.addStringValue("动态成本差额比", "title");
            //            er.addStringValue("实际成本差额比", "title");
            //            er.addStringValue("建筑面积", "title");
            //            er.addStringValue("备注", "title");
            String[] titleNames = new String[]{
                    "项目", "主责条线",
                    "目标成本总价", "目标成本建面单方",
                    "调整次数", "调整金额",
                    "执行预算总价", "执行预算建面单方",
                    "动态成本总价", "动态成本建面单方",
                    "实际成本总价", "实际成本建面单方",
                    "实际审批支出总价", "实际审批支出建面单方",
                    "实际财务支出总价", "实际财务支出建面单方",
                    "动态成本差额比", "实际成本差额比",
                    "建筑面积", "备注"};
            String[] titleCodes = new String[]{
                    "PROJECTNAME", "TIAOXIAN",
                    "COSTTOTAL", "JMDF",
                    "TZCS", "TZJE",
                    "ZYS", "ZXYS_JMDF",
                    "DT_PRICE", "DTCB_JMDF",
                    "SJ_PRICE", "SJCB_JMDF",
                    "SPZC_PRICE", "SPZC_JMDF",
                    "CWZC_PRICE", "CWZC_JMDF",
                    "DT_CEB", "SJ_CEB",
                    "ZJZMJ", "REMARK"};

            for (int i = 0; i < titleNames.length; i++) {
                //设置列的宽度
                es.addColumnwidth(6000);
            }
            //标题样式
            ExcelStyle excelStyle = excelFile.newExcelStyle("title");
            //字体颜色
            excelStyle.setFontcolor(ExcelStyle.BLACK_Color);
            //字体粗细
            excelStyle.setFontbold(ExcelStyle.Strong_Font);

            //合计
            ExcelStyle summaryStyle = excelFile.newExcelStyle("summary");
            //字体颜色
            summaryStyle.setFontcolor(ExcelStyle.BLACK_Color);
            summaryStyle.setGroundcolor(ExcelStyle.LIGHT_ORANGE_Color);

            //normalStylle
            ExcelStyle normalexcelStyle = excelFile.newExcelStyle("normal");
            //字体颜色
            normalexcelStyle.setFontcolor(ExcelStyle.BLACK_Color);

            ExcelRow er = es.newExcelRow(0);
            for (String titleName : titleNames) {
                er.addStringValue(titleName, "title");
            }

//            er.addStringValue("项目", "title");
//            er.addStringValue("主责条线", "title");
//            er.addStringValue("目标成本总价", "title");
//            er.addStringValue("目标成本建面单方", "title");
//            er.addStringValue("调整次数", "title");
//            er.addStringValue("调整金额", "title");
//            er.addStringValue("执行预算总价", "title");
//            er.addStringValue("执行预算建面单方", "title");
//            er.addStringValue("动态成本总价", "title");
//            er.addStringValue("动态成本建面单方", "title");
//            er.addStringValue("实际成本总价", "title");
//            er.addStringValue("实际成本建面单方", "title");
//            er.addStringValue("实际审批支出总价", "title");
//            er.addStringValue("实际审批支出建面单方", "title");
//            er.addStringValue("实际财务支出总价", "title");
//            er.addStringValue("实际财务支出建面单方", "title");
//            er.addStringValue("动态成本差额比", "title");
//            er.addStringValue("实际成本差额比", "title");
//            er.addStringValue("建筑面积", "title");
//            er.addStringValue("备注", "title");
            int row = 1;
            String titleValue;
            while (rs.next()) {
                String stylestr = "normal";
                String TIAOXIAN = Util.null2String(rs.getString("TIAOXIAN"));
                if ("合计".equals(TIAOXIAN)) {
                    stylestr = "summary";
                }
                ExcelRow er1 = es.newExcelRow(row);
                row++;
                for (String titleCode : titleCodes) {
                    titleValue = Util.null2String(rs.getString(titleCode));
                    if ("PROJECTNAME".equals(titleCode) || "TIAOXIAN".equals(titleCode)) {
                        er1.addStringValue(titleValue, stylestr);
                    } else {
                        er1.addStringValue(costUtil.getamountdf(titleValue), stylestr);
                    }

                }
//                String PROJECTNAME = Util.null2String(rs.getString("PROJECTNAME"));
//                String TIAOXIAN = Util.null2String(rs.getString("TIAOXIAN"));
//                String COSTTOTAL = Util.null2String(rs.getString("COSTTOTAL"));
//                String TZCS = Util.null2String(rs.getString("TZCS"));
//                String TZJE = Util.null2String(rs.getString("TZJE"));
//                String ZJZMJ = Util.null2String(rs.getString("ZJZMJ"));
//                String ZYS = Util.null2String(rs.getString("ZYS"));
//                String DT_PRICE = Util.null2String(rs.getString("DT_PRICE"));
//                String SJ_PRICE = Util.null2String(rs.getString("SJ_PRICE"));
//                String SPZC_PRICE = Util.null2String(rs.getString("SPZC_PRICE"));
//                String CWZC_PRICE = Util.null2String(rs.getString("CWZC_PRICE"));
//                String JMDF = Util.null2String(rs.getString("JMDF"));
//                String ZXYS_JMDF = Util.null2String(rs.getString("ZXYS_JMDF"));
//                String DTCB_JMDF = Util.null2String(rs.getString("DTCB_JMDF"));
//                String SJCB_JMDF = Util.null2String(rs.getString("SJCB_JMDF"));
//                String SPZC_JMDF = Util.null2String(rs.getString("SPZC_JMDF"));
//                String CWZC_JMDF = Util.null2String(rs.getString("CWZC_JMDF"));
//                String DT_CEB = Util.null2String(rs.getString("DT_CEB"));
//                String SJ_CEB = Util.null2String(rs.getString("SJ_CEB"));
//                String REMARK = Util.null2String(rs.getString("REMARK"));
//
//
//                er1.addStringValue(PROJECTNAME, stylestr);
//                er1.addStringValue(TIAOXIAN, stylestr);
//                er1.addStringValue(costUtil.getamountdf(COSTTOTAL), stylestr);
//                er1.addStringValue(costUtil.getamountdf(TZCS), stylestr);
//                er1.addStringValue(costUtil.getamountdf(TZJE), stylestr);
//                er1.addStringValue(costUtil.getamountdf(ZJZMJ), stylestr);
//                er1.addStringValue(costUtil.getamountdf(ZYS), stylestr);
//                er1.addStringValue(costUtil.getamountdf(DT_PRICE), stylestr);
//                er1.addStringValue(costUtil.getamountdf(SJ_PRICE), stylestr);
//                er1.addStringValue(costUtil.getamountdf(SPZC_PRICE), stylestr);
//                er1.addStringValue(costUtil.getamountdf(CWZC_PRICE), stylestr);
//                er1.addStringValue(costUtil.getamountdf(JMDF), stylestr);
//                er1.addStringValue(costUtil.getamountdf(ZXYS_JMDF), stylestr);
//                er1.addStringValue(costUtil.getamountdf(DTCB_JMDF), stylestr);
//                er1.addStringValue(costUtil.getamountdf(SJCB_JMDF), stylestr);
//                er1.addStringValue(costUtil.getamountdf(SPZC_JMDF), stylestr);
//                er1.addStringValue(costUtil.getamountdf(CWZC_JMDF), stylestr);
//                er1.addStringValue(costUtil.getamountdf(DT_CEB), stylestr);
//                er1.addStringValue(costUtil.getamountdf(SJ_CEB), stylestr);
//                er1.addStringValue(costUtil.getamountdf(REMARK), stylestr);
            }
            excelFile.setFilename("主责条线汇总表");
            ExcelOutUtil excelOutUtil = new ExcelOutUtil();
            excelOutUtil.ExcelOut(user, excelFile, response);
            result.put("status", "1");
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("errorInfo", e.getMessage().replace("java.lang.Exception:", ""));
        }
        return result;
    }


}
