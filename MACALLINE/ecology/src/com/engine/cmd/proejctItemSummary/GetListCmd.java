package com.engine.cmd.proejctItemSummary;

import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.bean.SplitTableOperateBean;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.TemplateFillUtil;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/5/25-16:58
 * @description : 获取列表数据
 */
public class GetListCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造器
     *
     * @param params
     * @param user
     */
    public GetListCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * 主方法
     *
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> datas;
        datas = getTotalSql();
        if ("-1".equals(datas.get("status"))) {
            return datas;
        }
        if ("1".equals(datas.get("status"))) {
            datas.putAll(queryData(Util.null2String(datas.get("sql"))));
        }
        return datas;
    }

    public Map<String, Object> getTotalSql() {
        BaseBean baseBean = new BaseBean();
        Map<String, Object> result = new HashMap<>();
        baseBean.writeLog("com.engine.cmd.costMainDuty.proejctItemSummary Start................................");
        //账套id
        String accountId = Util.null2String(params.get("accountId"));
        //项目信息
        String xmxx = Util.null2String(params.get("xmxx"));
        String xmxxs = Util.null2String(params.get("xmxxs"));

        String typeIds = "1,2,3,4,5";
        String[] typeArray = typeIds.split(",");
        TemplateFillUtil fillUtil = new TemplateFillUtil(user);
        String grofillDataTableName = fillUtil.getFillDataTableName(accountId, FnaAccTypeConstant.BUDGET_FILLDATA_GRO, typeIds, typeArray.length);
        StringBuilder sb;
        baseBean.writeLog("grofillDataTableName：" + grofillDataTableName);
        if (StringUtils.isBlank(grofillDataTableName)) {
            result.put("status", "-1");
            result.put("errorInfo", "TableName is Null");
            return result;
        }
        //高级搜索-项目
        String xmbrowser = Util.null2String(params.get("xmbrowser"));
        //高级搜索-一级科目
        String km1browser = Util.null2String(params.get("km1browser"));

        String sqlWhere = " where 1=1 ";
        String coltypeIds = "4,5,2,3";

        String[] coltypeIdsArray = coltypeIds.split(",");
        String number = grofillDataTableName.split("_")[1];
        String fillnumberCode = grofillDataTableName.split("_")[2];
        String FnaExpenseInfo = "FnaExpenseInfo_" + number + "_" + fillnumberCode;

        List<String> allZZTXTables = getAllZZTXTable(grofillDataTableName);
        if (allZZTXTables.isEmpty()) {
            result.put("status", "-1");
            result.put("errorInfo", "无三级科目对应的主责条线表");
            return result;
        }

        //总预算
        String zyssql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.budgetData) zys from " + grofillDataTableName + " a\r " +
                "where a.isEffect = 1 and a.apprvoalActivation = 1 and a.approvalFillDataSataus = 1\r " +
                "group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";


        //已使用预算
        String ysysql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.amount) sjysy from " + FnaExpenseInfo + " a\r " +
                "where a.EXPENSESTAUS = 1 \r " +
                "group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";
        //预使用预算
        String ysyyssql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.amount) ysyys from " + FnaExpenseInfo + " a\r " +
                "where a.EXPENSESTAUS = 0 \r " +
                "group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";


        //从配置文件获取变更金额sql
        String gdbgjesql = "";
        try {
            String formIds = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "formIds")).getBytes(StandardCharsets.ISO_8859_1), "gbk")).trim();
            if ("".equals(formIds)) {
                result.put("status", "-1");
                result.put("errorInfo", "请检查配置文件");
                return result;
            }
            String[] formIdsArray = formIds.split(",");

            for (String formId : formIdsArray) {
                String yjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "yjkmfileName_" + formId)).getBytes(StandardCharsets.ISO_8859_1), "gbk")).trim();
                String ejkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "ejkmfileName_" + formId)).getBytes(StandardCharsets.ISO_8859_1), "gbk")).trim();
                String sanjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "sanjkmfileName_" + formId)).getBytes(StandardCharsets.ISO_8859_1), "gbk")).trim();
                String sjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "sjkmfileName_" + formId)).getBytes(StandardCharsets.ISO_8859_1), "gbk")).trim();
                String xmkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "xmkmfileName_" + formId)).getBytes(StandardCharsets.ISO_8859_1), "gbk")).trim();
                String jefileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "jefileName_" + formId)).getBytes(StandardCharsets.ISO_8859_1), "gbk")).trim();

                if (!"".equals(gdbgjesql)) {
                    gdbgjesql += " UNION ALL ";
                }
                gdbgjesql += " select a.requestId ";

                String[] formIdarray = formId.split("_"); //第一位formId,第二位：明细表
                String[] yjkmfileNameArray = yjkmfileName.split("_"); //第一位字段名，第二位：所属表单
                String[] ejkmfileNameArray = ejkmfileName.split("_");
                String[] sanjkmfileNameArray = sanjkmfileName.split("_");
                String[] sjkmfileNameArray = sjkmfileName.split("_");
                String[] xmkmfileNameArray = xmkmfileName.split("_");
                String[] jefileNameArray = jefileName.split("_");
                if ("0".equals(yjkmfileNameArray[1])) {
                    gdbgjesql += ",a." + yjkmfileNameArray[0] + " BUDGETMEMBER_4 ";
                } else {
                    gdbgjesql += ",b." + yjkmfileNameArray[0] + " BUDGETMEMBER_4 ";
                }
                if ("0".equals(ejkmfileNameArray[1])) {
                    gdbgjesql += ",a." + ejkmfileNameArray[0] + " BUDGETMEMBER_5 ";
                } else {
                    gdbgjesql += ",b." + ejkmfileNameArray[0] + " BUDGETMEMBER_5 ";
                }
                if ("0".equals(sanjkmfileNameArray[1])) {
                    gdbgjesql += ",a." + sanjkmfileNameArray[0] + " BUDGETMEMBER_2 ";
                } else {
                    gdbgjesql += ",b." + sanjkmfileNameArray[0] + " BUDGETMEMBER_2 ";
                }
                if ("0".equals(sjkmfileNameArray[1])) {
                    gdbgjesql += ",a." + sjkmfileNameArray[0] + " BUDGETMEMBER_3 ";
                } else {
                    gdbgjesql += ",b." + sjkmfileNameArray[0] + " BUDGETMEMBER_3 ";
                }
                if ("0".equals(xmkmfileNameArray[1])) {
                    gdbgjesql += ",a." + xmkmfileNameArray[0] + " BUDGETMEMBER_1 ";
                } else {
                    gdbgjesql += ",b." + xmkmfileNameArray[0] + " BUDGETMEMBER_1 ";
                }
                if ("0".equals(jefileNameArray[1])) {
                    gdbgjesql += ",a." + jefileNameArray[0] + " bgje ";
                } else {
                    gdbgjesql += ",b." + jefileNameArray[0] + " bgje ";
                }
                gdbgjesql += " from formtable_main_" + formIdarray[0] + " a ";
                if (!"0".equals(formIdarray[1])) {
                    gdbgjesql += " join FORMTABLE_MAIN_" + formIdarray[0] + "_DT" + formIdarray[1] + " b on a.id = b.MAINID ";
                }
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            baseBean.writeLog(e.getMessage());
        }

        //审批中变更金额
        String spzbgjesql = " select bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5,sum(bgtb.bgje) spzje from "
                + "(" + gdbgjesql + ") bgtb join workflow_requestbase wr on wr.requestid = bgtb.requestId \r "
                + "	where wr.currentnodetype > 0 and wr.currentnodetype < 3  "
                + " group by bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5 ";


        //归档变更金额
        String gdbgjeendsql = " select bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5,sum(bgtb.bgje) gdje from "
                + "(" + gdbgjesql + ") bgtb join workflow_requestbase wr on wr.requestid = bgtb.requestId \r "
                + "	where wr.currentnodetype = 3 "
                + " group by bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5 ";

        String orderBy = "";
        String groupby = "";
        String backFields = " sum(jetb.zys) zys,sum(jetb.gdje) gdje ";
        String colsql = " zystb.zys,gdbgjetb.gdje";
        for (String s : coltypeIdsArray) {
            backFields += ",jetb.BUDGETMEMBER_" + s + "  ";
            colsql += ",zystb.BUDGETMEMBER_" + s + "  ";
            if (!"".equals(orderBy)) {
                orderBy += ",";
                groupby += ",";
            }
            groupby += "jetb.BUDGETMEMBER_" + s + " ";

            orderBy += "jetb.BUDGETMEMBER_" + s;
        }
        backFields += "  ,jetb.BUDGETMEMBER_1  ";
        colsql += "  ,zystb.BUDGETMEMBER_1  ";
        groupby += ",jetb.BUDGETMEMBER_1 ";
        orderBy += ",jetb.BUDGETMEMBER_1";

//        String fromSql = " (select " + colsql + " from ( " + zyssql + " ) zystb  "
//                + " left join (" + gdbgjeendsql + ") gdbgjetb "
//                + " on trim(''||zystb.BUDGETMEMBER_1) = ''||gdbgjetb.BUDGETMEMBER_1 "
//                + " and ''||zystb.BUDGETMEMBER_2 = ''||gdbgjetb.BUDGETMEMBER_2 "
//                + " and ''||zystb.BUDGETMEMBER_3 = ''||gdbgjetb.BUDGETMEMBER_3 "
//                + " and ''||zystb.BUDGETMEMBER_4 = ''||gdbgjetb.BUDGETMEMBER_4 "
//                + " and ''||zystb.BUDGETMEMBER_5 = ''||gdbgjetb.BUDGETMEMBER_5 "
//                + " where (zystb.zys <> 0 or gdbgjetb.gdje <> 0 ) ";

        String fromSql = " (select " + colsql + " from ( " + zyssql + " ) zystb \r "
                + " left join (" + ysysql + ") sjysytb "
                + "  on zystb.BUDGETMEMBER_1 = sjysytb.BUDGETMEMBER_1 "
                + "  and zystb.BUDGETMEMBER_2 = sjysytb.BUDGETMEMBER_2 "
                + "  and zystb.BUDGETMEMBER_3 = sjysytb.BUDGETMEMBER_3 "
                + "  and zystb.BUDGETMEMBER_4 = sjysytb.BUDGETMEMBER_4 "
                + "  and zystb.BUDGETMEMBER_5 = sjysytb.BUDGETMEMBER_5"
                + " left join (" + ysyyssql + ") ysyystb "
                + "  on zystb.BUDGETMEMBER_1 = ysyystb.BUDGETMEMBER_1 "
                + "  and zystb.BUDGETMEMBER_2 = ysyystb.BUDGETMEMBER_2 "
                + "  and zystb.BUDGETMEMBER_3 = ysyystb.BUDGETMEMBER_3 "
                + "  and zystb.BUDGETMEMBER_4 = ysyystb.BUDGETMEMBER_4 "
                + "  and zystb.BUDGETMEMBER_5 = ysyystb.BUDGETMEMBER_5"
                + " left join (" + spzbgjesql + ") spzbgjetb "
                + " on trim(''||zystb.BUDGETMEMBER_1) = ''||spzbgjetb.BUDGETMEMBER_1 "
                + " and ''||zystb.BUDGETMEMBER_2 = ''||spzbgjetb.BUDGETMEMBER_2 "
                + " and ''||zystb.BUDGETMEMBER_3 = ''||spzbgjetb.BUDGETMEMBER_3 "
                + " and ''||zystb.BUDGETMEMBER_4 = ''||spzbgjetb.BUDGETMEMBER_4 "
                + " and ''||zystb.BUDGETMEMBER_5 = ''||spzbgjetb.BUDGETMEMBER_5 "
                + " left join (" + gdbgjeendsql + ") gdbgjetb "
                + " on trim(''||zystb.BUDGETMEMBER_1) = ''||gdbgjetb.BUDGETMEMBER_1 "
                + " and ''||zystb.BUDGETMEMBER_2 = ''||gdbgjetb.BUDGETMEMBER_2 "
                + " and ''||zystb.BUDGETMEMBER_3 = ''||gdbgjetb.BUDGETMEMBER_3 "
                + " and ''||zystb.BUDGETMEMBER_4 = ''||gdbgjetb.BUDGETMEMBER_4 "
                + " and ''||zystb.BUDGETMEMBER_5 = ''||gdbgjetb.BUDGETMEMBER_5 "
                + " where (zystb.zys <> 0 or  spzbgjetb.spzje <> 0 or  gdbgjetb.gdje <> 0  or sjysytb.sjysy <> 0 or ysyystb.ysyys <> 0 ) ";


        if ((!"".equals(xmxx)) && (!"0".equals(xmxx))) {
            fromSql += " and zystb.BUDGETMEMBER_1 ='" + xmxx + "'";
        }

        if (!"".equals(xmxxs)) {
            fromSql += " and zystb.BUDGETMEMBER_1 in(" + xmxxs + ")";
        }
        //高级搜索条件-项目
        if (!"".equals(xmbrowser)) {
            String[] xmbrowserArray = xmbrowser.split(",");
            String xmbrowserwherein = "";
            for (String s : xmbrowserArray) {
                if (!"".equals(xmbrowserwherein)) {
                    xmbrowserwherein += ",";
                }
                xmbrowserwherein += "'" + s + "'";
            }
            fromSql += " and zystb.BUDGETMEMBER_1 in(" + xmbrowserwherein + ")";
        }
        fromSql += " ) jetb ";

        String queryAllSql = " select " + backFields + " from " + fromSql + " " + sqlWhere + " group by " + groupby + " order by " + orderBy;
        String queryTotalSql = "select TB1.*,FBS.ACCOUNTCODE, " +
                " reverse(substr(reverse(FBS.ACCOUNTCODE),1,INSTR(reverse(FBS.ACCOUNTCODE),'_') - 1)) AS kmId, " +
                " substr(FBS.ACCOUNTCODE,1,instr(FBS.ACCOUNTCODE,'_',-1)-1) AS tbName  from ( " + queryAllSql + ")" +
                "TB1 " +
                " left join fnabudgetsubject_1 FBS " +
                " on (TB1.BUDGETMEMBER_2 = FBS.ID " +
                " )";
        //根据不同的表，拼接获取所有类型的数据
        int cnt = 0;
        String queryTotalSql2;
        sb = new StringBuilder();
        for (String tableName : allZZTXTables) {
            if (cnt != 0) {
                sb.append(" UNION ALL ");
            }
            sb.append(" SELECT ");
            sb.append(" TB2.*, ");
            sb.append(" TBZZ.zztx, ");
            sb.append(" VC.CNT,  ");
            sb.append(" VC.TZJE, ");
            sb.append(" VD.DT_PRICE, ");
            sb.append(" VD.SJ_PRICE, ");
            sb.append(" VD.SPZC_PRICE, ");
            sb.append(" VD.CWZC_PRICE ");
            sb.append(" FROM ( ");
            sb.append(queryTotalSql);
            sb.append("  ) TB2 ");
            sb.append(" LEFT JOIN ( ");
            sb.append(" SELECT ");
            sb.append(" a.zztx, ");
            sb.append(" b.id, ");
            sb.append(" c.xmxx  ");
            sb.append(" FROM ");
            sb.append(tableName);
            sb.append("_dt1 a  ");
            sb.append(" LEFT JOIN uf_km b ON a.bjbs = b.id ");
            sb.append(" LEFT JOIN ");
            sb.append(tableName);
            sb.append(" c ON a.mainid = c.id ");
            sb.append(" WHERE ");
            sb.append(" c.zt = 1  ");
            sb.append(" AND a.zztx IS NOT NULL  ");
            sb.append(" ) TBZZ ON ( ");
            sb.append(" TRIM( TB2.BUDGETMEMBER_1 ) = TRIM( TBZZ.xmxx )  ");
            sb.append(" AND TRIM( TBZZ.id ) = TRIM( TB2.KMID )  ");
            sb.append(" )  ");
            sb.append(" LEFT JOIN V_XMKM_AJMCOUNT VC ON ( TB2.BUDGETMEMBER_3 = VC.KM AND TRIM( TB2.BUDGETMEMBER_1 ) = VC.XM )  ");
            sb.append(" LEFT JOIN V_XMKM_PRICE VD ON  ( TB2.BUDGETMEMBER_3 = VD.KM AND TRIM( TB2.BUDGETMEMBER_1 ) = VD.XM )  ");
            sb.append(" WHERE ");
            sb.append(" TB2.tbName = '");
            sb.append(tableName);
            sb.append("'");
            //sb.append(" AND TBZZ.ZZTX IS NOT NULL ");

            cnt++;
        }
        queryTotalSql2 = sb.toString();
        sb = new StringBuilder();
        sb.append(" SELECT TB3.* , ");
        sb.append(" NVL2(TB3.zjzmj,round(TB3.COSTTOTAL/TB3.zjzmj,2),0) as JMDF, ");
        sb.append(" NVL2( TB3.zjzmj, round( TB3.ZYS / TB3.zjzmj, 2 ), 0 ) AS ZXYS_JMDF, ");
        sb.append(" NVL2( TB3.zjzmj, round( TB3.DT_PRICE / TB3.zjzmj, 2 ), 0 ) AS DTCB_JMDF, ");
        sb.append(" NVL2( TB3.zjzmj, round( TB3.SJ_PRICE / TB3.zjzmj, 2 ), 0 ) AS SJCB_JMDF, ");
        sb.append(" NVL2( TB3.zjzmj, round( TB3.SPZC_PRICE / TB3.zjzmj, 2 ), 0 ) AS SPZC_JMDF, ");
        sb.append(" NVL2( TB3.zjzmj, round( TB3.CWZC_PRICE / TB3.zjzmj, 2 ), 0 ) AS CWZC_JMDF, ");
        sb.append(" NVL2( TB3.COSTTOTAL, round( ( TB3.DT_PRICE- TB3.COSTTOTAL) / TB3.COSTTOTAL, 2 ), 0 ) AS DT_CEB, ");
        sb.append(" NVL2( TB3.COSTTOTAL, round( ( TB3.SJ_PRICE- TB3.COSTTOTAL) / TB3.COSTTOTAL, 2 ), 0 ) AS SJ_CEB, ");
        sb.append(" '' AS REMARK ");
        sb.append(" FROM ( ");
        sb.append(" SELECT ");
        sb.append(" TBBG1.fkname  AS PROJECTNAME, ");
        sb.append(" TBALL.BUDGETMEMBER_1 AS PROJECTID, ");
        sb.append(" TBG4.SUBJECTNAME AS KM1, ");
        sb.append(" TBALL.BUDGETMEMBER_4  AS SUBJECTID, ");
        sb.append(" TBG4.DISPLAYORDER  AS SUBJECTORDER, ");
        sb.append(" SUM( TBALL.ZYS - nvl( TBALL.GDJE, 0 ) ) AS COSTTOTAL,  ");
        sb.append(" NVL(SUM(TBALL.CNT),0) AS TZCS, ");
        sb.append(" SUM( TBALL.TZJE) AS TZJE, ");
        sb.append(" XM.ZJZMJ, ");
        sb.append(" SUM(TBALL.ZYS) AS ZYS, ");
        sb.append(" to_number(SUM(TBALL.DT_PRICE)) AS DT_PRICE, ");
        sb.append(" SUM(TBALL.SJ_PRICE) AS SJ_PRICE, ");
        sb.append(" SUM(TBALL.SPZC_PRICE) AS SPZC_PRICE, ");
        sb.append(" SUM(TBALL.CWZC_PRICE) AS CWZC_PRICE ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(queryTotalSql2);
        sb.append(" ) TBALL ");
        sb.append(" LEFT JOIN FnaBudgetSubject_1 TBG4 ON ( TBG4.ID = TBALL.BUDGETMEMBER_4 ) ");
        sb.append(" LEFT JOIN ( SELECT id, MC FROM uf_zzbmcb WHERE ZT = 0 ) TX ON ( TX.id = TBALL.ZZTX ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" a.fkVarchar, ");
        sb.append(" a.fkName  ");
        sb.append(" FROM ");
        sb.append(" FnaDimensionMember_1 a ");
        sb.append(" JOIN FnaBudgetDimension_1 b ON a.dimensionId = b.id  ");
        sb.append(" WHERE ");
        sb.append(" b.TYPEID = 1  ");
        sb.append(" ) TBBG1 ON ( TBBG1.fkVarchar = TRIM( TBALL.BUDGETMEMBER_1 ) )  ");
        sb.append(" LEFT JOIN uf_xmxx XM on (xm.id = TRIM(TBALL.BUDGETMEMBER_1) )  ");
        sb.append(" WHERE 1=1 ");
        //高级搜索条件-一级科目
        if (!"".equals(km1browser)) {
            String[] browserArray = km1browser.split(",");
            String browserwherein = "";
            for (String s : browserArray) {
                if (!"".equals(browserwherein)) {
                    browserwherein += ",";
                }
                browserwherein += "'" + s + "'";
            }
            //将科目ID转为科目名称
            sb.append(" AND TBG4.SUBJECTNAME in( ");
            sb.append(getKmNamesByIds(browserwherein));
            sb.append(" ) ");
        }
        sb.append(" GROUP BY ");
        sb.append(" TBBG1.fkName, ");
        sb.append(" TBALL.BUDGETMEMBER_1, ");
        sb.append(" TBG4.SUBJECTNAME, ");
        sb.append(" TBALL.BUDGETMEMBER_4, ");
        sb.append(" TBG4.DISPLAYORDER, ");
        sb.append(" XM.ZJZMJ ");

        sb.append(" ORDER BY ");
        sb.append(" TBBG1.fkName, ");
        sb.append(" TBG4.SUBJECTNAME ");

        sb.append(" ) TB3  ");
        String sql = sb.toString();
        sql = addSummarySql(sql);
        result.put("sql", sql);
        result.put("status", "1");
        return result;
    }

    private String getKmNamesByIds(String ids) {
        RecordSet rs = new RecordSet();
        String result = "";
        String sql = " select id,mc,cj from uf_km  where   id in ( ";
        sql += ids + ") ";
        rs.executeQuery(sql);
        while (rs.next()) {
            if (!"".equals(result)) {
                result += ",";
            }
            result += "'" + Util.null2String(rs.getString("mc")) + "'";
        }
        return result;
    }

    /**
     * 获取所有的三级科目对应的建模表
     *
     * @param grofillDataTableName
     * @return
     */
    public List<String> getAllZZTXTable(String grofillDataTableName) {
        List<String> result = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        String tableName;
        sb.append(" SELECT ");
        sb.append(" TB1.tbName ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" a.BUDGETMEMBER_2, ");
        sb.append(" substr( FBS.ACCOUNTCODE, 1, instr( FBS.ACCOUNTCODE, '_',- 1 ) - 1 ) AS tbName  ");
        sb.append(" FROM ");
        sb.append(grofillDataTableName);
        sb.append(" a ");
        sb.append(" LEFT JOIN fnabudgetsubject_1 FBS ON ( a.BUDGETMEMBER_2 = FBS.ID )  ");
        sb.append(" WHERE ");
        sb.append(" a.isEffect = 1  ");
        sb.append(" AND a.apprvoalActivation = 1  ");
        sb.append(" AND a.approvalFillDataSataus = 1  ");
        sb.append(" ) TB1  ");
        sb.append(" GROUP BY ");
        sb.append(" TB1.tbName ");
        rs.executeQuery(sb.toString());
        while (rs.next()) {
            tableName = rs.getString("tbName");
            result.add(tableName);
        }
        return result;
    }


    public Map<String, Object> queryData(String sql) {
        List<SplitTableColBean> cols = new ArrayList<>();
        String backFields = "";
        String orderBySql = "";

        String xmxx = Util.null2String(params.get("xmxx"));
        if ("".equals(xmxx) || "0".equals(xmxx)) {
            backFields += "PROJECTNAME,PROJECTID,";
            orderBySql = "PROJECTID,";
        }

        backFields += "KM1,SUBJECTID,SUBJECTORDER,COSTTOTAL,JMDF,TZCS,TZJE,ZYS,ZXYS_JMDF,";
        backFields += "DT_PRICE,DTCB_JMDF,SJ_PRICE,SJCB_JMDF,SPZC_PRICE,SPZC_JMDF,CWZC_PRICE,CWZC_JMDF,";
        backFields += "DT_CEB,SJ_CEB,ZJZMJ,REMARK ";
        orderBySql += "SUBJECTORDER";
        String fromSql = "( " + sql + " )";
        String whereSql = " where 1=1 ";
        if ("".equals(xmxx) || "0".equals(xmxx)) {
            //项目
            cols.add(new SplitTableColBean("25%", "项目", "PROJECTNAME"));
        }
        //项目ID
        cols.add(new SplitTableColBean("true", "PROJECTID"));

        //一级科目
        cols.add(new SplitTableColBean("25%", "一级科目", "KM1", "KM1"));
        //一级科目ID
        cols.add(new SplitTableColBean("true", "SUBJECTID"));
        //一级科目 排序
        cols.add(new SplitTableColBean("true", "SUBJECTORDER"));
        //目标成本总价
        cols.add(new SplitTableColBean("25%", "目标成本总价", "COSTTOTAL", "COSTTOTAL", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //目标成本建面单方
        cols.add(new SplitTableColBean("30%", "目标成本建面单方", "JMDF", "JMDF", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //调整次数
        cols.add(new SplitTableColBean("25%", "调整次数", "TZCS", "TZCS", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //调整金额
        cols.add(new SplitTableColBean("25%", "调整金额", "TZJE", "TZJE", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //执行预算总价
        cols.add(new SplitTableColBean("25%", "执行预算总价", "ZYS", "ZYS", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //执行预算建面单方
        cols.add(new SplitTableColBean("30%", "执行预算建面单方", "ZXYS_JMDF", "ZXYS_JMDF", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //动态成本总价
        cols.add(new SplitTableColBean("25%", "动态成本总价", "DT_PRICE", "DT_PRICE", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //动态成本建面单方
        cols.add(new SplitTableColBean("30%", "动态成本建面单方", "DTCB_JMDF", "DTCB_JMDF", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //实际成本总价
        cols.add(new SplitTableColBean("25%", "实际成本总价", "SJ_PRICE", "SJ_PRICE", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //实际成本建面单方
        cols.add(new SplitTableColBean("30%", "实际成本建面单方", "SJCB_JMDF", "SJCB_JMDF", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //实际审批支出总价
        cols.add(new SplitTableColBean("30%", "实际审批支出总价", "SPZC_PRICE", "SPZC_PRICE", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //实际审批支出建面单方
        cols.add(new SplitTableColBean("30%", "实际审批支出建面单方", "SPZC_JMDF", "SPZC_JMDF", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //实际财务支出总价
        cols.add(new SplitTableColBean("30%", "实际财务支出总价", "CWZC_PRICE", "CWZC_PRICE", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //实际财务支出建面单方
        cols.add(new SplitTableColBean("30%", "实际财务支出建面单方", "CWZC_JMDF", "CWZC_JMDF", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //动态成本差额比
        cols.add(new SplitTableColBean("25%", "动态成本差额比", "DT_CEB", "DT_CEB", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //实际成本差额比
        cols.add(new SplitTableColBean("25%", "实际成本差额比", "SJ_CEB", "SJ_CEB", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //建筑面积
        cols.add(new SplitTableColBean("25%", "建筑面积", "ZJZMJ", "ZJZMJ", "com.engine.util.CostUtil.getNumberValue", "column:KM1"));
        //备注
        cols.add(new SplitTableColBean("25%", "备注", "REMARK", "REMARK"));

        SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();


        SplitTableBean tableBean = new SplitTableBean("projectItemSummary", "none", PageIdConst.getPageSize("projectItemSummary", user.getUID(), PageIdConst.FNA),
                "projectItemSummary", backFields, fromSql, whereSql, orderBySql, "", "", "Asc", cols);

        tableBean.setSqlisdistinct("true");
        tableBean.setOperates(splitTableOperateBean);
        return SplitTableUtil.makeListDataResult(tableBean);
    }

    public String addSummarySql(String sql) {
        StringBuilder sb = new StringBuilder();
        //分类汇总项目，条线的数据
        sb.append(" SELECT TB5.* FROM ( ");
        sb.append(" SELECT ");
        sb.append(" TB4.PROJECTNAME, ");
        sb.append(" TB4.PROJECTID, ");
        sb.append(" '合计' AS KM1, ");
        sb.append(" '-1' AS SUBJECTID, ");
        sb.append(" -1 AS SUBJECTORDER, ");
        sb.append(" NVL(SUM(TB4.COSTTOTAL),0.0) AS COSTTOTAL, ");
        sb.append(" NVL(SUM(TB4.TZCS),0.0) AS TZCS, ");
        sb.append(" NVL(SUM(TB4.TZJE),0.0) AS TZJE, ");
        sb.append(" TB4.ZJZMJ AS ZJZMJ, ");
        sb.append(" NVL(SUM(TB4.ZYS),0.0) AS ZYS, ");
        sb.append(" NVL(SUM(TB4.DT_PRICE),0.0) AS DT_PRICE, ");
        sb.append(" NVL(SUM(TB4.SJ_PRICE),0.0) AS SJ_PRICE, ");
        sb.append(" NVL(SUM(TB4.SPZC_PRICE),0.0) AS SPZC_PRICE, ");
        sb.append(" NVL(SUM(TB4.CWZC_PRICE),0.0) AS CWZC_PRICE, ");
        sb.append(" NVL(SUM(TB4.JMDF),0.0) AS JMDF,");
        sb.append(" NVL(SUM(TB4.ZXYS_JMDF),0.0) AS ZXYS_JMDF, ");
        sb.append(" NVL(SUM(TB4.DTCB_JMDF),0.0) AS DTCB_JMDF, ");
        sb.append(" NVL(SUM(TB4.SJCB_JMDF),0.0) AS SJCB_JMDF, ");
        sb.append(" NVL(SUM(TB4.SPZC_JMDF),0.0) AS SPZC_JMDF, ");
        sb.append(" NVL(SUM(TB4.CWZC_JMDF),0.0) AS CWZC_JMDF, ");
        sb.append(" NVL(SUM(TB4.DT_CEB),0.0) AS DT_CEB, ");
        sb.append(" NVL(SUM(TB4.SJ_CEB),0.0) AS SJ_CEB, ");
        sb.append(" '' AS REMARK ");
        sb.append(" FROM ( ");
        sb.append(sql);
        sb.append(" ) TB4 ");
        sb.append(" GROUP BY  ");
        sb.append(" TB4.PROJECTNAME, ");
        sb.append(" TB4.PROJECTID, ");
        sb.append(" TB4.ZJZMJ ");
        sb.append(" UNION ALL ");
        sb.append(sql);
        sb.append(" ) TB5 ");
        sb.append(" ORDER BY TB5.PROJECTNAME,TB5.KM1 ");
        return sb.toString();

    }
}
