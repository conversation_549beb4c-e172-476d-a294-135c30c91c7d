package com.engine.cmd.proejctItemSummary;

import com.api.fna.util.ExcelOutUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.FnaAmountPointComInfo;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import com.engine.util.CostUtil;
import weaver.conn.RecordSet;
import weaver.file.ExcelFile;
import weaver.file.ExcelRow;
import weaver.file.ExcelSheet;
import weaver.file.ExcelStyle;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class GetExcelCmd extends AbstractCommonCommand<Map<String, Object>> {

    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 响应
     */
    private HttpServletResponse response;
    private int amountPoint;

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetExcelCmd(Map<String, Object> params, User user, HttpServletResponse response) {
        this.params = params;
        this.user = user;
        this.response = response;
        this.amountPoint = Util.getIntValue(new FnaAmountPointComInfo().getAmountPoint("" + FnaAccTypeConstant.AMOUNT_POINT_ID));
    }

    /**
     * Command类方法实现
     *
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String sql = "";
        try {
            GetListCmd getListCmd = new GetListCmd(params, user);
            result = getListCmd.getTotalSql();

            if ("-1".equals(result.get("status"))) {
                return result;
            }
            if ("1".equals(result.get("status"))) {
                sql = Util.null2String(result.get("sql"));
            }
            RecordSet rs = new RecordSet();
            CostUtil costUtil = new CostUtil();

            ExcelFile excelFile = new ExcelFile();
            excelFile.init();
            String sheetName = "项目科目汇总表";
            rs.executeQuery(sql);
            ExcelSheet es = new ExcelSheet();
            excelFile.addSheet(sheetName, es);
            es.initRowList(rs.getCounts() + 1);
            //          er.addStringValue("项目", "title");
            //            er.addStringValue("一级科目", "title");
            //            er.addStringValue("目标成本总价", "title");
            //            er.addStringValue("目标成本建面单方", "title");
            //            er.addStringValue("调整次数", "title");
            //            er.addStringValue("调整金额", "title");
            //            er.addStringValue("执行预算总价", "title");
            //            er.addStringValue("执行预算建面单方", "title");
            //            er.addStringValue("动态成本总价", "title");
            //            er.addStringValue("动态成本建面单方", "title");
            //            er.addStringValue("实际成本总价", "title");
            //            er.addStringValue("实际成本建面单方", "title");
            //            er.addStringValue("实际审批支出总价", "title");
            //            er.addStringValue("实际审批支出建面单方", "title");
            //            er.addStringValue("实际财务支出总价", "title");
            //            er.addStringValue("实际财务支出建面单方", "title");
            //            er.addStringValue("动态成本差额比", "title");
            //            er.addStringValue("实际成本差额比", "title");
            //            er.addStringValue("建筑面积", "title");
            //            er.addStringValue("备注", "title");
            String[] titleNames = new String[]{
                    "项目", "一级科目",
                    "目标成本总价", "目标成本建面单方",
                    "调整次数", "调整金额",
                    "执行预算总价", "执行预算建面单方",
                    "动态成本总价", "动态成本建面单方",
                    "实际成本总价", "实际成本建面单方",
                    "实际审批支出总价", "实际审批支出建面单方",
                    "实际财务支出总价", "实际财务支出建面单方",
                    "动态成本差额比", "实际成本差额比",
                    "建筑面积", "备注"};
            String[] titleCodes = new String[]{
                    "PROJECTNAME", "KM1",
                    "COSTTOTAL", "JMDF",
                    "TZCS", "TZJE",
                    "ZYS", "ZXYS_JMDF",
                    "DT_PRICE", "DTCB_JMDF",
                    "SJ_PRICE", "SJCB_JMDF",
                    "SPZC_PRICE", "SPZC_JMDF",
                    "CWZC_PRICE", "CWZC_JMDF",
                    "DT_CEB", "SJ_CEB",
                    "ZJZMJ", "REMARK"};

            for (int i = 0; i < 20; i++) {
                //设置列的宽度
                es.addColumnwidth(6000);
            }
            //标题样式
            ExcelStyle excelStyle = excelFile.newExcelStyle("title");
            excelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
            excelStyle.setFontbold(ExcelStyle.Strong_Font); //字体粗细

            //合计
            ExcelStyle summaryStyle = excelFile.newExcelStyle("summary");
            summaryStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
            summaryStyle.setGroundcolor(ExcelStyle.LIGHT_ORANGE_Color);

            //normalStylle
            ExcelStyle normalexcelStyle = excelFile.newExcelStyle("normal");
            normalexcelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色

            ExcelRow er = es.newExcelRow(0);
            for (String titleName : titleNames) {
                er.addStringValue(titleName, "title");
            }
//
//            er.addStringValue("项目", "title");
//            er.addStringValue("一级科目", "title");
//            er.addStringValue("目标成本总价", "title");
//            er.addStringValue("目标成本建面单方", "title");
//            er.addStringValue("调整次数", "title");
//            er.addStringValue("调整金额", "title");
//            er.addStringValue("执行预算总价", "title");
//            er.addStringValue("执行预算建面单方", "title");
//            er.addStringValue("动态成本总价", "title");
//            er.addStringValue("动态成本建面单方", "title");
//            er.addStringValue("实际成本总价", "title");
//            er.addStringValue("实际成本建面单方", "title");
//            er.addStringValue("实际审批支出总价", "title");
//            er.addStringValue("实际审批支出建面单方", "title");
//            er.addStringValue("实际财务支出总价", "title");
//            er.addStringValue("实际财务支出建面单方", "title");
//            er.addStringValue("动态成本差额比", "title");
//            er.addStringValue("实际成本差额比", "title");
//            er.addStringValue("建筑面积", "title");
//            er.addStringValue("备注", "title");
            int row = 1;
            String titleValue;
            while (rs.next()) {
                String stylestr = "normal";
                String KM1 = Util.null2String(rs.getString("KM1"));
                if ("合计".equals(KM1)) {
                    stylestr = "summary";
                }
                ExcelRow er1 = es.newExcelRow(row);
                row++;
                for (String titleCode : titleCodes) {
                    titleValue = Util.null2String(rs.getString(titleCode));
                    if ("PROJECTNAME".equals(titleCode) || "KM1".equals(titleCode)) {
                        er1.addStringValue(titleValue, stylestr);
                    } else {
                        er1.addStringValue(costUtil.getamountdf(titleValue), stylestr);
                    }
                }

//                String PROJECTNAME = Util.null2String(rs.getString("PROJECTNAME"));
//                String KM1 = Util.null2String(rs.getString("KM1"));
//                String COSTTOTAL = Util.null2String(rs.getString("COSTTOTAL"));
//                String TZCS = Util.null2String(rs.getString("TZCS"));
//                String TZJE = Util.null2String(rs.getString("TZJE"));
//                String ZJZMJ = Util.null2String(rs.getString("ZJZMJ"));
//                String ZYS = Util.null2String(rs.getString("ZYS"));
//                String DT_PRICE = Util.null2String(rs.getString("DT_PRICE"));
//                String SJ_PRICE = Util.null2String(rs.getString("SJ_PRICE"));
//                String SPZC_PRICE = Util.null2String(rs.getString("SPZC_PRICE"));
//                String CWZC_PRICE = Util.null2String(rs.getString("CWZC_PRICE"));
//                String JMDF = Util.null2String(rs.getString("JMDF"));
//                String ZXYS_JMDF = Util.null2String(rs.getString("ZXYS_JMDF"));
//                String DTCB_JMDF = Util.null2String(rs.getString("DTCB_JMDF"));
//                String SJCB_JMDF = Util.null2String(rs.getString("SJCB_JMDF"));
//                String SPZC_JMDF = Util.null2String(rs.getString("SPZC_JMDF"));
//                String CWZC_JMDF = Util.null2String(rs.getString("CWZC_JMDF"));
//                String DT_CEB = Util.null2String(rs.getString("DT_CEB"));
//                String SJ_CEB = Util.null2String(rs.getString("SJ_CEB"));
//                String REMARK = Util.null2String(rs.getString("REMARK"));
//
//
//                //项目
//                er1.addStringValue(PROJECTNAME, stylestr);
//                //一级科目
//                er1.addStringValue(KM1, stylestr);
//                //目标成本总价
//                er1.addStringValue(costUtil.getamountdf(COSTTOTAL), stylestr);
//                //目标成本建面单方
//                er1.addStringValue(costUtil.getamountdf(JMDF), stylestr);
//                //调整次数
//                er1.addStringValue(costUtil.getamountdf(TZCS), stylestr);
//                //调整金额
//                er1.addStringValue(costUtil.getamountdf(TZJE), stylestr);
//
//                //执行预算总价
//                er1.addStringValue(costUtil.getamountdf(ZYS), stylestr);
//                //执行预算建面单方
//                er1.addStringValue(costUtil.getamountdf(ZXYS_JMDF), stylestr);
//
//                //动态成本总价
//                er1.addStringValue(costUtil.getamountdf(DT_PRICE), stylestr);
//                //动态成本建面单方
//                er1.addStringValue(costUtil.getamountdf(DTCB_JMDF), stylestr);
//
//                //实际成本总价
//                er1.addStringValue(costUtil.getamountdf(SJ_PRICE), stylestr);
//                //实际成本建面单方
//                er1.addStringValue(costUtil.getamountdf(SJCB_JMDF), stylestr);
//
//                //实际审批支出总价
//                er1.addStringValue(costUtil.getamountdf(SPZC_PRICE), stylestr);
//                //实际审批支出建面单方
//                er1.addStringValue(costUtil.getamountdf(SPZC_JMDF), stylestr);
//
//                //实际财务支出总价
//                er1.addStringValue(costUtil.getamountdf(CWZC_PRICE), stylestr);
//                //实际财务支出建面单方
//                er1.addStringValue(costUtil.getamountdf(CWZC_JMDF), stylestr);
//
//                //动态成本差额比
//                er1.addStringValue(costUtil.getamountdf(DT_CEB), stylestr);
//                //实际成本差额比
//                er1.addStringValue(costUtil.getamountdf(SJ_CEB), stylestr);
//                //建筑面积
//                er1.addStringValue(costUtil.getamountdf(ZJZMJ), stylestr);
//                //备注
//                er1.addStringValue(costUtil.getamountdf(REMARK), stylestr);
            }
            excelFile.setFilename("项目科目汇总表");
            ExcelOutUtil excelOutUtil = new ExcelOutUtil();
            excelOutUtil.ExcelOut(user, excelFile, response);
            result.put("status", "1");
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("errorInfo", e.getMessage().replace("java.lang.Exception:", ""));
        }
        return result;
    }


}
