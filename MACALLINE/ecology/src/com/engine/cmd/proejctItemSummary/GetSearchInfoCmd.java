/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.cmd.proejctItemSummary;

import com.api.browser.bean.SearchConditionItem;
import com.api.browser.util.ConditionFactory;
import com.api.browser.util.ConditionType;
import com.api.fna.util.FnaConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.systeminfo.SystemEnv;

import java.util.*;

/**
 * Title: GetSearchInfoCmd
 *
 * <AUTHOR>
 * @date 2020年12月24日
 * Description:
 */
public class GetSearchInfoCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetSearchInfoCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     *
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        String xmxx = Util.null2String(params.get("xmxx"));
        String accountId = Util.null2String(params.get("accountId"));

        SearchConditionItem searchItem;
        Map<String, Object> result = new HashMap<>();
        RecordSet rSet = new RecordSet();
        List<Map<String, Object>> grouplist = new ArrayList<>();
        ConditionFactory conditionFactory = new ConditionFactory(user);

        //常用条件
        Map<String, Object> groupitem1 = new HashMap<>();
        List<SearchConditionItem> itemlist1 = new LinkedList<>();
        //如果点了项目树，不显示项目的筛选
        if ("".equals(xmxx) || "0".equals(xmxx)) {
            //项目
            searchItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "xmbrowser", "fnaxmbrowser");
            searchItem.getBrowserConditionParam().getDataParams().put("accountId", accountId);
            searchItem.getBrowserConditionParam().getDestDataParams().put("accountId", accountId);
            searchItem.getBrowserConditionParam().getConditionDataParams().put("accountId", accountId);
            searchItem.setLabel("项目");
            searchItem.getBrowserConditionParam().setIsSingle(false);
            itemlist1.add(searchItem);
        }

        //条线
        searchItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km1browser","fnaqckmnew");
        searchItem.setLabel("一级科目");
        searchItem.getBrowserConditionParam().getDataParams().put("typeId", "4");
        searchItem.getBrowserConditionParam().getDestDataParams().put("typeId", "4");
        searchItem.getBrowserConditionParam().getConditionDataParams().put("typeId", "4");
        searchItem.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(searchItem);

        //基本信息
        groupitem1.put("title", SystemEnv.getHtmlLabelName(1361, user.getLanguage()));
        groupitem1.put("defaultshow", true);
        groupitem1.put("items", itemlist1);
        grouplist.add(groupitem1);

        result.put("status", "1");
        result.put(FnaConstant.FNA_RESULT_CONDITIONS, grouplist);

        return result;
    }
}
