package com.engine.cmd.costMainDuty;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.TemplateFillUtil;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/7/22-16:44
 * @description : 主责条线明细穿透
 */
public class GetCostDetailCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetCostDetailCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(0);
        //获取所有4级科目ID
        String all4Ids = getAll4LevelCode();
        result.put("subjectId", all4Ids);
        result.put("type", params.get("type"));
        return result;
    }

    @SuppressWarnings("unchecked")
    private String getAll4LevelCode() {
        StringBuilder result = new StringBuilder();
        RecordSet rs = new RecordSet();
        StringBuilder sb = new StringBuilder();
        //账套id
        String accountId = Util.null2String(params.get("accountId"));
        //项目ID
        String proejectId = Util.null2String(params.get("proejectId"));
        //条线ID
        String txId = Util.null2String(params.get("txId"));
        //一级科目ID
        String subjectId = Util.null2String(params.get("subjectId"));
        String typeIds = "1,2,3,4,5";
        String[] typeArray = typeIds.split(",");
        TemplateFillUtil fillUtil = new TemplateFillUtil(user);
        String grofillDataTableName = fillUtil.getFillDataTableName(accountId, FnaAccTypeConstant.BUDGET_FILLDATA_GRO, typeIds, typeArray.length);
        GetListCmd cmd = new GetListCmd(params, user);
        List<String> allZZTXTables = cmd.getAllZZTXTable(grofillDataTableName);
        String sql = "SELECT " +
                " TB1.*, " +
                " FBS.ACCOUNTCODE, " +
                " reverse( substr( reverse( FBS.ACCOUNTCODE ), 1, INSTR( reverse( FBS.ACCOUNTCODE ), '_' ) - 1 ) ) AS kmId, " +
                " substr( FBS.ACCOUNTCODE, 1, instr( FBS.ACCOUNTCODE, '_',- 1 ) - 1 ) AS tbName  " +
                "FROM " +
                " ( " +
                "SELECT " +
                " a.BUDGETMEMBER_1, " +
                " a.BUDGETMEMBER_2, " +
                " a.BUDGETMEMBER_3, " +
                " a.BUDGETMEMBER_4, " +
                " a.BUDGETMEMBER_5  " +
                "FROM " + grofillDataTableName +
                "  a  " +
                "WHERE " +
                " a.isEffect = 1  " +
                " AND a.apprvoalActivation = 1  " +
                " AND a.approvalFillDataSataus = 1  " +
                "GROUP BY " +
                " a.BUDGETMEMBER_1, " +
                " a.BUDGETMEMBER_2, " +
                " a.BUDGETMEMBER_3, " +
                " a.BUDGETMEMBER_4, " +
                " a.BUDGETMEMBER_5  " +
                " ) TB1 " +
                " LEFT JOIN fnabudgetsubject_1 FBS ON ( TB1.BUDGETMEMBER_2 = FBS.ID )  " +
                "ORDER BY " +
                " TB1.BUDGETMEMBER_1";
        //根据不同的表，拼接获取所有类型的数据
        int cnt = 0;
        for (String tableName : allZZTXTables) {
            if (cnt != 0) {
                sb.append(" UNION ALL ");
            }
            sb.append(" SELECT ");
            sb.append(" TB2.*, ");
            sb.append(" TBZZ.zztx ");
            sb.append(" FROM ( ");
            sb.append(sql);
            sb.append("  ) TB2 ");
            sb.append(" LEFT JOIN ( ");
            sb.append(" SELECT ");
            sb.append(" a.zztx, ");
            sb.append(" b.id, ");
            sb.append(" c.xmxx  ");
            sb.append(" FROM ");
            sb.append(tableName);
            sb.append("_dt1 a  ");
            sb.append(" LEFT JOIN uf_km b ON a.bjbs = b.id ");
            sb.append(" LEFT JOIN ");
            sb.append(tableName);
            sb.append(" c ON a.mainid = c.id ");
            sb.append(" WHERE ");
            sb.append(" c.zt = 1  ");
            sb.append(" AND a.zztx IS NOT NULL  ");
            sb.append(" ) TBZZ ON ( ");
            sb.append(" TRIM( TB2.BUDGETMEMBER_1 ) = TRIM( TBZZ.xmxx )  ");
            sb.append(" AND TRIM( TBZZ.id ) = TRIM( TB2.KMID )  ");
            sb.append(" )  ");
            sb.append(" WHERE ");
            sb.append(" TB2.tbName = '");
            sb.append(tableName);
            sb.append("'");
            sb.append(" AND TBZZ.ZZTX IS NOT NULL ");
            cnt++;
        }
        sql = sb.toString();
        sb = new StringBuilder();
        sb.append(" SELECT TBA.* FROM ( ");
        sb.append(sql);
        sb.append(" )TBA ");
        sb.append(" where 1=1 ");
        if (!proejectId.isEmpty()) {
            sb.append("and TBA.BUDGETMEMBER_1 = '").append(proejectId).append("' ");
        }
        if (!txId.isEmpty()) {
            sb.append("and TBA.ZZTX = '").append(txId).append("' ");
        }
        if (!subjectId.isEmpty()) {
            sb.append("and TBA.BUDGETMEMBER_4 = '").append(subjectId).append("' ");
        }
        rs.executeQuery(sb.toString());
        if (rs.next()) {
            List<Map<String, Object>> list = QueryResultUtil.getJSONList(rs.getArray(), rs.getData());
            if (!list.isEmpty()) {
                int i = 0;
                for (Map<String, Object> map : list) {
                    if (i != 0) {
                        result.append(",");
                    }
                    result.append(Util.null2String(map.get("BUDGETMEMBER_3")));
                    i++;
                }
            }
        }
        return result.toString();
    }
}
