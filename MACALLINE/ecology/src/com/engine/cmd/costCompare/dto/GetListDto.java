package com.engine.cmd.costCompare.dto;

import lombok.Data;

/**
 * @FileName GetListDto.java
 * @Description 成本对比表查询 最终输出的相关参数
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/16
 */
@Data
public class GetListDto {
    private String backFields;
    private String fromSql;
    private String sqlWhere;
    private String orderBy;
    private String groupby;
    private String number;
    /**
     * 出错信息
     */
    private String errorMsg;
}
