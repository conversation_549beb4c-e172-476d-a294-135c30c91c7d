/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.cmd.costCompare;

import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.bean.SplitTableOperateBean;
import com.api.browser.util.SplitTableUtil;
import com.engine.cmd.costCompare.dto.GetListDto;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
import com.engine.fnaMulDimensions.util.SubjectUtil;
import com.engine.fnaMulDimensions.util.TemplateFillUtil;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Title: GetListCmd
 *
 * <AUTHOR>
 * @date 2020年12月23日
 * Description:
 */
public class GetListCmd3 extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetListCmd3(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    private final BaseBean bb;

    /**
     * Command类方法实现
     *
     * @param commandContext
     * @return
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> datas = new HashMap<String, Object>();
        bb.writeLog("GetListCmd3Start................................");

        String xmxx = Util.null2String(params.get("xmxx"));

        //获取dto配置项
        GetListDto dto = getListDto();
        if (!dto.getErrorMsg().isEmpty()) {
            datas.put("status", "-1");
            datas.put("errorInfo", dto.getErrorMsg());
        } else {
            //SUM
            List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
            if ("".equals(xmxx) || "0".equals(xmxx)) {
                cols.add(new SplitTableColBean("25%", "项目 ", "xmxx", "xmxx"
                        , "com.engine.util.CostUtil.getxmmcbs", dto.getNumber() + "+column:subjectLeve"));//成本编码
            }

            cols.add(new SplitTableColBean("true", "subjectID"));//subjectid

            cols.add(new SplitTableColBean("25%", "成本编码", "bm", "bm"
                    , "com.engine.util.CostUtil.bmbsbs", "column:subjectLeve"));//成本编码
            cols.add(new SplitTableColBean("25%", "科目名称", "SUBJECTNAME", "SUBJECTNAME"
                    , "com.engine.util.CostUtil.bs", "column:subjectLeve"));//科目名称
            cols.add(new SplitTableColBean("25%", "科目等级", "subjectLeve", "subjectLeve"
                    , "com.engine.util.CostUtil.bs", "column:subjectLeve"));//科目等级
            cols.add(new SplitTableColBean("25%", "目标成本总价", "csys", "csys"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "目标成本建面单方", "dwcs", "dwcs"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "执行预算总价", "zys", "zys"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));//名称
            cols.add(new SplitTableColBean("25%", "执行预算建面单方", "dwzj", "dwzj"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));//名称
            cols.add(new SplitTableColBean("25%", "动态成本总价", "dtcbz", "dtcbz"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "动态成本建面单方", "dtcbzjmd", "dtcbzjmd"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "实际成本总价", "sjcbz", "sjcbz"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));

            cols.add(new SplitTableColBean("25%", "实际成本建面单方", "sjcbzjmd", "sjcbzjmd"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "实际审批支出总价", "sjspz", "sjspz"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "实际审批支出建面单方", "sjspzjmd", "sjspzjmd"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "实际财务支出总价", "sjcwz", "sjcwz"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "实际财务支出建面单方", "sjcwzjmd", "sjcwzjmd"
                    , "com.engine.util.CostUtil.getamountdfbs", "column:subjectLeve"));
            cols.add(new SplitTableColBean("25%", "备注", "bz", "bz"
                    , "", ""));
            //xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,displayOrder,(csys/zjzmj) as dwcs,(zys/zjzmj) as dwzj,(htk+sqf) as dtcbz,(yjg+sqf) as sjcbz,(sqf+yfje) as sjspz,(fyqcw+htxcw) as sjcwz
//            cols.add(new SplitTableColBean("30%", "已变更预算", "gdje", "gdje"
//            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//			cols.add(new SplitTableColBean("30%", "预变更预算", "spzje", "spzje"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//            cols.add(new SplitTableColBean("30%", "已使用预算", "sjysy", "sjysy"
//            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//            cols.add(new SplitTableColBean("30%", "预使用预算", "ysyys", "ysyys"
//            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//            cols.add(new SplitTableColBean("30%", "可用预算", "syky", "syky"
//            		,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//			cols.add(new SplitTableColBean("30%", "htks", "htk", "htk"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//			cols.add(new SplitTableColBean("30%", "sqfs", "sqf", "sqf"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//			cols.add(new SplitTableColBean("30%", "yjgs", "yjg", "yjg"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//			cols.add(new SplitTableColBean("30%", "总面积", "zjzmj", "zjzmj"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述 总面积
//			cols.add(new SplitTableColBean("30%", "yfje", "yfje", "yfje"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//			cols.add(new SplitTableColBean("30%", "fyqcw", "fyqcw", "fyqcw"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述
//			cols.add(new SplitTableColBean("30%", "htxcw", "htxcw", "htxcw"
//					,"com.engine.util.CostUtil.getamountdfbs","column:subjectLeve"));//描述

            /*cols.add(new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(24664, user.getLanguage()), "bearerId", "bearerId",
            "com.engine.fnaMulDimensions.util.TableColTransmethod.getBudgetBearerRelation","column:accountNumber+"+user.getLanguage()+""));*///关联对象

            SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
            String sqlprimarykey = "";
            SplitTableBean tableBean = new SplitTableBean("costCompare", "none", PageIdConst.getPageSize("costCompare", user.getUID(), PageIdConst.FNA),
                    "costCompare", dto.getBackFields(), dto.getFromSql(), dto.getSqlWhere(), dto.getOrderBy(), dto.getGroupby(), sqlprimarykey, "Asc", cols);


            tableBean.setSqlisdistinct("true");
            tableBean.setOperates(splitTableOperateBean);

            datas.putAll(SplitTableUtil.makeListDataResult(tableBean));
            datas.put("status", "1");
        }


        return datas;
    }

    public GetListDto getListDto() {
        bb.writeLog("---getListDto---");
        bb.writeLog("params:" + params);
        String errorMsg = "";
        GetListDto dto = new GetListDto();

        String accountId = Util.null2String(params.get("accountId"));
        String xmxx = Util.null2String(params.get("xmxx"));
        String xmxxs = Util.null2String(params.get("xmxxs"));
        String subject1 = Util.null2String(params.get("subject1"));
        String typeIds = "1,2,3,4,5";
        String[] typeArray = typeIds.split(",");
        TemplateFillUtil fillUtil = new TemplateFillUtil(user);
        String grofillDataTableName = fillUtil.getFillDataTableName(accountId, FnaAccTypeConstant.BUDGET_FILLDATA_GRO, typeIds, typeArray.length);
        RecordSet rs = new RecordSet();
        bb.writeLog("grofillDataTableName：" + grofillDataTableName);
        if (!"".equals(grofillDataTableName)) {
            RecordSet rSet = new RecordSet();
            BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
            String subjectTableName = budgetApprovalUtil.getTableName(accountId, FnaAccTypeConstant.BUDGET_SUBJECT);
        	/*if(("".equals(xmxx))||("0".equals(xmxx))){
        		BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
        		String tableName = budgetApprovalUtil.getTableName(accountId, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE);
        		StringBuffer queryBuffer = new StringBuffer();
                queryBuffer.append(" select a.approvaTypelName,a.approvalVersGroupId,b.xmxx  from ").append(tableName).append(" a "
                		+ " join fnaQc978507Init b on a.approvalVersGroupId = b.approvalVersGroupId ");
                queryBuffer.append(" where a.approvaTypelstatus = 1 and a.approvalFillDataSataus = 1 and a.apprvoalActivation = 1 and b.accountId = '"+accountId+"' ");
                rSet.executeQuery(queryBuffer.toString());
                if(rSet.next()){
                	xmxx = Util.null2String(rSet.getString("xmxx"));
                }
        	}*/

            String xmbrowser = Util.null2String(params.get("xmbrowser")); //高级搜索的项目
            String yjkmserach = Util.null2String(params.get("km_4"));
            String ejkmserach = Util.null2String(params.get("km_5"));
            String sanjkmserach = Util.null2String(params.get("km_2"));
            String sjkmserach = Util.null2String(params.get("km_3"));
            String subjectLeaveserach = Util.null2String(params.get("subjectLeave"));

            String sqlWhere = " where 1=1 ";
            String coltypeIds = "4,5,2,3";
            String[] coltypeIdsArray = coltypeIds.split(",");
            String number = grofillDataTableName.split("_")[1];
            dto.setNumber(number);
            String fillnumberCode = grofillDataTableName.split("_")[2];
            String FnaExpenseInfo = "FnaExpenseInfo_" + number + "_" + fillnumberCode;


            //总预算
            String zyssql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.budgetData) zys from " + grofillDataTableName + " a\r " +
                    "where a.isEffect = 1 and a.apprvoalActivation = 1 and a.approvalFillDataSataus = 1\r " +
                    "group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";
            //已使用预算
            String ysysql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.amount) sjysy from " + FnaExpenseInfo + " a\r " +
                    "where a.EXPENSESTAUS = 1 \r " +
                    "group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";
            //预使用预算
            String ysyyssql = "select a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5,sum(a.amount) ysyys from " + FnaExpenseInfo + " a\r " +
                    "where a.EXPENSESTAUS = 0 \r " +
                    "group by a.BUDGETMEMBER_1,a.BUDGETMEMBER_2,a.BUDGETMEMBER_3,a.BUDGETMEMBER_4,a.BUDGETMEMBER_5";
            BaseBean baseBean = new BaseBean();
            String gdbgjesql = "";
            bb.writeLog("2222222222222222222222222222222222222222222：");
            String formIds = "";
            try {
                formIds = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "formIds")).getBytes("ISO-8859-1"), "gbk")).trim();
                if (formIds.isEmpty()) {
                    errorMsg = "未获取到配置文件QCfna96925里的formIds，请检查配置文件";
                }
            } catch (Exception e) {
                errorMsg = "未获取到配置文件QCfna96925里的formIds，请检查配置文件";
            }
            if (errorMsg.isEmpty()) {
                String[] formIdsArray = formIds.split(",");
                try {
                    for (String formId : formIdsArray) {
                        String yjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "yjkmfileName_" + formId)).getBytes("ISO-8859-1"), "gbk")).trim();
                        String ejkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "ejkmfileName_" + formId)).getBytes("ISO-8859-1"), "gbk")).trim();
                        String sanjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "sanjkmfileName_" + formId)).getBytes("ISO-8859-1"), "gbk")).trim();
                        String sjkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "sjkmfileName_" + formId)).getBytes("ISO-8859-1"), "gbk")).trim();
                        String xmkmfileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "xmkmfileName_" + formId)).getBytes("ISO-8859-1"), "gbk")).trim();
                        String jefileName = Util.null2String(new String(Util.null2String(baseBean.getPropValue("QCfna96925", "jefileName_" + formId)).getBytes("ISO-8859-1"), "gbk")).trim();

                        if (!gdbgjesql.isEmpty()) {
                            gdbgjesql += " UNION ALL ";
                        }
                        gdbgjesql += " select a.requestId ";

                        String[] formIdarray = formId.split("_"); //第一位formId,第二位：明细表
                        String[] yjkmfileNameArray = yjkmfileName.split("_"); //第一位字段名，第二位：所属表单
                        String[] ejkmfileNameArray = ejkmfileName.split("_");
                        String[] sanjkmfileNameArray = sanjkmfileName.split("_");
                        String[] sjkmfileNameArray = sjkmfileName.split("_");
                        String[] xmkmfileNameArray = xmkmfileName.split("_");
                        String[] jefileNameArray = jefileName.split("_");
                        if ("0".equals(yjkmfileNameArray[1])) {
                            gdbgjesql += ",a." + yjkmfileNameArray[0] + " BUDGETMEMBER_4 ";
                        } else {
                            gdbgjesql += ",b." + yjkmfileNameArray[0] + " BUDGETMEMBER_4 ";
                        }
                        if ("0".equals(ejkmfileNameArray[1])) {
                            gdbgjesql += ",a." + ejkmfileNameArray[0] + " BUDGETMEMBER_5 ";
                        } else {
                            gdbgjesql += ",b." + ejkmfileNameArray[0] + " BUDGETMEMBER_5 ";
                        }
                        if ("0".equals(sanjkmfileNameArray[1])) {
                            gdbgjesql += ",a." + sanjkmfileNameArray[0] + " BUDGETMEMBER_2 ";
                        } else {
                            gdbgjesql += ",b." + sanjkmfileNameArray[0] + " BUDGETMEMBER_2 ";
                        }
                        if ("0".equals(sjkmfileNameArray[1])) {
                            gdbgjesql += ",a." + sjkmfileNameArray[0] + " BUDGETMEMBER_3 ";
                        } else {
                            gdbgjesql += ",b." + sjkmfileNameArray[0] + " BUDGETMEMBER_3 ";
                        }
                        if ("0".equals(xmkmfileNameArray[1])) {
                            gdbgjesql += ",a." + xmkmfileNameArray[0] + " BUDGETMEMBER_1 ";
                        } else {
                            gdbgjesql += ",b." + xmkmfileNameArray[0] + " BUDGETMEMBER_1 ";
                        }
                        if ("0".equals(jefileNameArray[1])) {
                            gdbgjesql += ",a." + jefileNameArray[0] + " bgje ";
                        } else {
                            gdbgjesql += ",b." + jefileNameArray[0] + " bgje ";
                        }

                        gdbgjesql += " from formtable_main_" + formIdarray[0] + " a ";
                        if (!"0".equals(formIdarray[1])) {
                            gdbgjesql += " join FORMTABLE_MAIN_" + formIdarray[0] + "_DT" + formIdarray[1] + " b on a.id = b.MAINID ";
                        }
                    }
                    new BaseBean().writeLog("4444444444444444444444444444444444444444444444444444444444444444：");
                    //审批中变更金额
                    String spzbgjesql = " select bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5,sum(bgtb.bgje) spzje from "
                            + "(" + gdbgjesql + ") bgtb join workflow_requestbase wr on wr.requestid = bgtb.requestId \r "
                            + "	where wr.currentnodetype > 0 and wr.currentnodetype < 3  "
                            + " group by bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5 ";
                    //归档变更金额
                    String gdbgjeendsql = " select bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5,sum(bgtb.bgje) gdje from "
                            + "(" + gdbgjesql + ") bgtb join workflow_requestbase wr on wr.requestid = bgtb.requestId \r "
                            + "	where wr.currentnodetype = 3 "
                            + " group by bgtb.BUDGETMEMBER_1,bgtb.BUDGETMEMBER_2,bgtb.BUDGETMEMBER_3,bgtb.BUDGETMEMBER_4,bgtb.BUDGETMEMBER_5 ";

                    String orderBy = "";
                    String groupby = "";
                    String backFields = " sum(jetb.zys) zys,sum(jetb.gdje) csys,sum(jetb.spzje) spzje ,sum(jetb.gdje) gdje ," +
                            "sum(jetb.sjysy) sjysy,sum(jetb.ysyys) ysyys,sum(jetb.zys) syky ,sum(fyq.sqfyje) sqfyje,sum(htx.ygjsje) ygjsje,sum(htx.htkzje) htkzje,sum(htx.yfje) yfje,sum(fyq.cwsjzfje) fyqcw,sum(htx.cwsjzfje) htxcw ";
                    String colsql = " zystb.zys,spzbgjetb.spzje,gdbgjetb.gdje,sjysytb.sjysy,ysyystb.ysyys,zystb.zys syky ";
                    for (int i = 0; i < coltypeIdsArray.length; i++) {
                        backFields += ",jetb.BUDGETMEMBER_" + coltypeIdsArray[i] + "  ";
                        colsql += ",zystb.BUDGETMEMBER_" + coltypeIdsArray[i] + "  ";
                        if (!"".equals(orderBy)) {
                            orderBy += ",";
                            groupby += ",";
                        }
                        groupby += "jetb.BUDGETMEMBER_" + coltypeIdsArray[i] + " ";

                        orderBy += "jetb.BUDGETMEMBER_" + coltypeIdsArray[i];
                    }
                    //if(("".equals(xmxx))||("0".equals(xmxx))){
                    backFields += "  ,jetb.BUDGETMEMBER_1  ";
                    colsql += "  ,zystb.BUDGETMEMBER_1  ";
                    groupby += ",jetb.BUDGETMEMBER_1 ";

                    orderBy += ",jetb.BUDGETMEMBER_1";
                    //}
                    //groupby+=",jetb.zys,jetb.spzje,jetb.gdje,jetb.sjysy,jetb.ysyys ";
                    String sqlprimarykey = "";


                    String fromSql = " (select " + colsql + " from ( " + zyssql + " ) zystb \r "
                            + " left join (" + ysysql + ") sjysytb "
                            + "  on zystb.BUDGETMEMBER_1 = sjysytb.BUDGETMEMBER_1 "
                            + "  and zystb.BUDGETMEMBER_2 = sjysytb.BUDGETMEMBER_2 "
                            + "  and zystb.BUDGETMEMBER_3 = sjysytb.BUDGETMEMBER_3 "
                            + "  and zystb.BUDGETMEMBER_4 = sjysytb.BUDGETMEMBER_4 "
                            + "  and zystb.BUDGETMEMBER_5 = sjysytb.BUDGETMEMBER_5"
                            + " left join (" + ysyyssql + ") ysyystb "
                            + "  on zystb.BUDGETMEMBER_1 = ysyystb.BUDGETMEMBER_1 "
                            + "  and zystb.BUDGETMEMBER_2 = ysyystb.BUDGETMEMBER_2 "
                            + "  and zystb.BUDGETMEMBER_3 = ysyystb.BUDGETMEMBER_3 "
                            + "  and zystb.BUDGETMEMBER_4 = ysyystb.BUDGETMEMBER_4 "
                            + "  and zystb.BUDGETMEMBER_5 = ysyystb.BUDGETMEMBER_5"
                            + " left join (" + spzbgjesql + ") spzbgjetb "
                            + " on trim(''||zystb.BUDGETMEMBER_1) = ''||spzbgjetb.BUDGETMEMBER_1 "
                            + " and ''||zystb.BUDGETMEMBER_2 = ''||spzbgjetb.BUDGETMEMBER_2 "
                            + " and ''||zystb.BUDGETMEMBER_3 = ''||spzbgjetb.BUDGETMEMBER_3 "
                            + " and ''||zystb.BUDGETMEMBER_4 = ''||spzbgjetb.BUDGETMEMBER_4 "
                            + " and ''||zystb.BUDGETMEMBER_5 = ''||spzbgjetb.BUDGETMEMBER_5 "
                            + " left join (" + gdbgjeendsql + ") gdbgjetb "
                            + " on trim(''||zystb.BUDGETMEMBER_1) = ''||gdbgjetb.BUDGETMEMBER_1 "
                            + " and ''||zystb.BUDGETMEMBER_2 = ''||gdbgjetb.BUDGETMEMBER_2 "
                            + " and ''||zystb.BUDGETMEMBER_3 = ''||gdbgjetb.BUDGETMEMBER_3 "
                            + " and ''||zystb.BUDGETMEMBER_4 = ''||gdbgjetb.BUDGETMEMBER_4 "
                            + " and ''||zystb.BUDGETMEMBER_5 = ''||gdbgjetb.BUDGETMEMBER_5 "
                            + " where (zystb.zys <> 0 or  spzbgjetb.spzje <> 0 or  gdbgjetb.gdje <> 0  or sjysytb.sjysy <> 0 or ysyystb.ysyys <> 0 ) ";
                    if ((!"".equals(xmxx)) && (!"0".equals(xmxx))) {
                        fromSql += " and zystb.BUDGETMEMBER_1 ='" + xmxx + "'";
                    }
                    if ((!"".equals(subject1)) && subject1.length() > 6) {
                        fromSql += " and zystb.BUDGETMEMBER_4 ='" + subject1 + "'";
                    } else if (Util.getIntValue(subject1) > 0) {
                        String[] subjectNameArray = {"土地费用", "开发前期费用", "建安费用", "管理费用", "财务费用"};
                        fromSql += " and zystb.BUDGETMEMBER_4 in(select id from " + subjectTableName + " "
                                + " where  SUBJECTNAME = '" + subjectNameArray[Util.getIntValue(subject1) - 1] + "' and SUBJECTLEVEL = 2 )";
                    }
                    if (!"".equals(xmxxs)) {
                        fromSql += " and zystb.BUDGETMEMBER_1 in(" + xmxxs + ")";
                    }
                    fromSql += " ) jetb LEFT JOIN (SELECT sum(sqfyje) sqfyje,sum(cwsjzfje) cwsjzfje,km from uf_fysqtz_dt1 GROUP BY km) fyq ON jetb.BUDGETMEMBER_3 = fyq.km LEFT JOIN (SELECT sum(ygjsje) ygjsje,sum(htkzje) htkzje,SUM(yfje) yfje,sum(cwsjzfje) cwsjzfje,km from uf_htxx_dt1 GROUP BY km) htx ON htx.km = jetb.BUDGETMEMBER_3 ";           ///这里加
                    new BaseBean().writeLog("GetListCmd3sql : select " + backFields + " from " + fromSql + " " + sqlWhere + " group by " + groupby + " order by " + orderBy);
                    Map<String, Object> yjmap = new HashMap<String, Object>();
                    Map<String, String> xmxxmap = new HashMap<String, String>();
                    String queryAllSql = " select " + backFields + " from " + fromSql + " " + sqlWhere + " group by " + groupby + " order by " + orderBy;
                    rs.executeQuery(queryAllSql);
                    new BaseBean().writeLog("rs................................");
                    while (rs.next()) {

                        BigDecimal ygj = getBigDecimalAmount(Util.null2String(rs.getString("ygjsje"), "0.00"));

                        BigDecimal sqf = getBigDecimalAmount(Util.null2String(rs.getString("sqfyje"), "0.00"));

                        BigDecimal htk = getBigDecimalAmount(Util.null2String(rs.getString("htkzje"), "0.00"));

                        BigDecimal yfje = getBigDecimalAmount(Util.null2String(rs.getString("yfje"), "0.00"));

                        BigDecimal fyqcw = getBigDecimalAmount(Util.null2String(rs.getString("fyqcw"), "0.00"));

                        BigDecimal htxcw = getBigDecimalAmount(Util.null2String(rs.getString("htxcw"), "0.00"));

                        BigDecimal zys = getBigDecimalAmount(Util.null2String(rs.getString("zys"), "0.00")); //总预算

                        BigDecimal gdje = getBigDecimalAmount(Util.null2String(rs.getString("gdje"), "0.00")); //已变更预算

                        BigDecimal csys = zys.subtract(gdje); //初始预算

                        BigDecimal spzje = getBigDecimalAmount(Util.null2String(rs.getString("spzje"), "0.00")); //预变更预算

                        BigDecimal sjysy = getBigDecimalAmount(Util.null2String(rs.getString("sjysy"), "0.00"));//已使用预算

                        BigDecimal ysyys = getBigDecimalAmount(Util.null2String(rs.getString("ysyys"), "0.00"));//预使用预算

                        BigDecimal syky = zys.subtract(sjysy).subtract(ysyys);//可用预算

                        String xmxxBUDGETMEMBER_1 = Util.null2String(rs.getString("BUDGETMEMBER_1")); //项目信息
                        String yjkmBUDGETMEMBER_4 = Util.null2String(rs.getString("BUDGETMEMBER_4")); //一级科目
                        String ejkmBUDGETMEMBER_5 = Util.null2String(rs.getString("BUDGETMEMBER_5")); //二级科目
                        String sjkmBUDGETMEMBER_2 = Util.null2String(rs.getString("BUDGETMEMBER_2")); //三级科目
                        String ssjkmBUDGETMEMBER_3 = Util.null2String(rs.getString("BUDGETMEMBER_3")); //四级科目
                        xmxxmap.put(yjkmBUDGETMEMBER_4, xmxxBUDGETMEMBER_1);
                        Map<String, Object> yjmapvalue = (Map<String, Object>) yjmap.getOrDefault(yjkmBUDGETMEMBER_4, new HashMap<String, Object>());
                        Map<String, Object> ejmapvalue = (Map<String, Object>) yjmapvalue.getOrDefault(ejkmBUDGETMEMBER_5, new HashMap<String, Object>());
                        Map<String, Object> sjmapvalue = (Map<String, Object>) ejmapvalue.getOrDefault(sjkmBUDGETMEMBER_2, new HashMap<String, Object>());

                        Map<String, String> ssjamountMap = new HashMap<String, String>();

                        ssjamountMap.put("yfje", "" + (yfje.toString()));
                        ssjamountMap.put("fyqcw", "" + (fyqcw.toString()));
                        ssjamountMap.put("htxcw", "" + (htxcw.toString()));
                        ssjamountMap.put("htk", "" + (htk.toString()));
                        ssjamountMap.put("sqf", "" + (sqf.toString()));
                        ssjamountMap.put("ygj", "" + (ygj.toString()));
                        ssjamountMap.put("zys", "" + (zys.toString()));
                        ssjamountMap.put("gdje", "" + (gdje.toString()));
                        ssjamountMap.put("csys", "" + (csys.toString()));
                        ssjamountMap.put("spzje", "" + (spzje.toString()));
                        ssjamountMap.put("sjysy", "" + (sjysy.toString()));
                        ssjamountMap.put("ysyys", "" + (ysyys.toString()));
                        ssjamountMap.put("syky", "" + (syky.toString()));

                        sjmapvalue.put(ssjkmBUDGETMEMBER_3, ssjamountMap);
                        ejmapvalue.put(sjkmBUDGETMEMBER_2, sjmapvalue);
                        yjmapvalue.put(ejkmBUDGETMEMBER_5, ejmapvalue);
                        yjmap.put(yjkmBUDGETMEMBER_4, yjmapvalue);
                    }
                    new BaseBean().writeLog("yjmap:" + yjmap);

                    String insertintosql = "insert into fnaqcstatement(xmxx,zys,csys,gdje,spzje,sjysy,ysyys,syky,subjectId,displayOrder,subjectLeve,htk,sqf,yjg,htxcw,fyqcw,yfje)  "; //text
                    double displayOrder = 0.00;
                    String yjselect = "";
                    rs.executeUpdate("delete from fnaqcstatement ");
                    String usexmxx = "";
                    //合计
                    //
                    for (Map.Entry<String, Object> yjva : yjmap.entrySet()) { //一级
                        String yjkmBUDGETMEMBER_4 = yjva.getKey();
                        String xmxxBUDGETMEMBER_1 = Util.null2String(xmxxmap.get(yjkmBUDGETMEMBER_4));
                        usexmxx = xmxxBUDGETMEMBER_1;
                        Map<String, Object> yjmapvalue = (Map<String, Object>) yjva.getValue();
                        BigDecimal yjzys = new BigDecimal("0.00");
                        BigDecimal yjgdje = new BigDecimal("0.00");
                        BigDecimal yjcsys = new BigDecimal("0.00");
                        BigDecimal yjspzje = new BigDecimal("0.00");
                        BigDecimal yjsjysy = new BigDecimal("0.00");
                        BigDecimal yjysyys = new BigDecimal("0.00");
                        BigDecimal yjsyky = new BigDecimal("0.00");
                        BigDecimal htkone = new BigDecimal("0.00");
                        BigDecimal sqfone = new BigDecimal("0.00");
                        BigDecimal yjgone = new BigDecimal("0.00");
                        BigDecimal htxcwone = new BigDecimal("0.00");
                        BigDecimal fyqcwone = new BigDecimal("0.00");
                        BigDecimal yfjecwone = new BigDecimal("0.00");

                        double yjdisplayOrder = displayOrder + 1;
                        displayOrder++;
                        String ejselect = "";
                        for (Map.Entry<String, Object> ejva : yjmapvalue.entrySet()) { //二级
                            String ejkmBUDGETMEMBER_5 = ejva.getKey();
                            Map<String, Object> ejmapvalue = (Map<String, Object>) ejva.getValue();
                            BigDecimal ejzys = new BigDecimal("0.00");
                            BigDecimal ejgdje = new BigDecimal("0.00");
                            BigDecimal ejcsys = new BigDecimal("0.00");
                            BigDecimal ejspzje = new BigDecimal("0.00");
                            BigDecimal ejsjysy = new BigDecimal("0.00");
                            BigDecimal ejysyys = new BigDecimal("0.00");
                            BigDecimal ejsyky = new BigDecimal("0.00");
                            BigDecimal htktwo = new BigDecimal("0.00");
                            BigDecimal sqftwo = new BigDecimal("0.00");
                            BigDecimal yjgtwo = new BigDecimal("0.00");
                            BigDecimal htxcwtwo = new BigDecimal("0.00");
                            BigDecimal fyqcwtwo = new BigDecimal("0.00");
                            BigDecimal yfjecwtwo = new BigDecimal("0.00");
                            double ejdisplayOrder = displayOrder + 1;
                            displayOrder++;


                            String sjselect = "";
                            for (Map.Entry<String, Object> sjva : ejmapvalue.entrySet()) {//三级
                                String sjkmBUDGETMEMBER_2 = sjva.getKey();
                                Map<String, Object> sjmapvalue = (Map<String, Object>) sjva.getValue();
                                BigDecimal sjzys = new BigDecimal("0.00");
                                BigDecimal sjgdje = new BigDecimal("0.00");
                                BigDecimal sjcsys = new BigDecimal("0.00");
                                BigDecimal sjspzje = new BigDecimal("0.00");
                                BigDecimal sjsjysy = new BigDecimal("0.00");
                                BigDecimal sjysyys = new BigDecimal("0.00");
                                BigDecimal sjsyky = new BigDecimal("0.00");
                                BigDecimal htkthree = new BigDecimal("0.00");
                                BigDecimal sqfthree = new BigDecimal("0.00");
                                BigDecimal yjgthree = new BigDecimal("0.00");
                                BigDecimal htxcwthree = new BigDecimal("0.00");
                                BigDecimal fyqcwthree = new BigDecimal("0.00");
                                BigDecimal yfjecwthree = new BigDecimal("0.00");
                                double sjdisplayOrder = displayOrder + 1;
                                displayOrder++;

                                String ssjselect = "";
                                for (Map.Entry<String, Object> ssjva : sjmapvalue.entrySet()) {
                                    String ssjkmBUDGETMEMBER_3 = ssjva.getKey();
                                    Map<String, String> ssjamountMap = (Map<String, String>) ssjva.getValue();
                                    BigDecimal zys = getBigDecimalAmount(Util.null2String(ssjamountMap.get("zys"), "0.00")); //总预算
                                    BigDecimal gdje = getBigDecimalAmount(Util.null2String(ssjamountMap.get("gdje"), "0.00")); //已变更预算
                                    BigDecimal csys = getBigDecimalAmount(Util.null2String(ssjamountMap.get("csys"), "0.00")); //初始预算
                                    BigDecimal spzje = getBigDecimalAmount(Util.null2String(ssjamountMap.get("spzje"), "0.00")); //预变更预算
                                    BigDecimal sjysy = getBigDecimalAmount(Util.null2String(ssjamountMap.get("sjysy"), "0.00"));//已使用预算
                                    BigDecimal ysyys = getBigDecimalAmount(Util.null2String(ssjamountMap.get("ysyys"), "0.00"));//预使用预算
                                    BigDecimal syky = getBigDecimalAmount(Util.null2String(ssjamountMap.get("syky"), "0.00"));//可用预算
                                    BigDecimal htkfo = getBigDecimalAmount(Util.null2String(ssjamountMap.get("htk"), "0.00"));
                                    BigDecimal sqffo = getBigDecimalAmount(Util.null2String(ssjamountMap.get("sqf"), "0.00"));
                                    BigDecimal yjgfo = getBigDecimalAmount(Util.null2String(ssjamountMap.get("ygj"), "0.00"));
                                    BigDecimal htxcwfo = getBigDecimalAmount(Util.null2String(ssjamountMap.get("htxcw"), "0.00"));
                                    if ("899543663.57".equals(htxcwfo.toString())) {
                                        System.out.println(1);
                                    }
                                    BigDecimal fyqcwfo = getBigDecimalAmount(Util.null2String(ssjamountMap.get("fyqcw"), "0.00"));
                                    BigDecimal yfjefo = getBigDecimalAmount(Util.null2String(ssjamountMap.get("yfje"), "0.00"));

                                    double ssjdisplayOrder = displayOrder + 1;
                                    displayOrder++;

                                    sjzys = sjzys.add(zys);
                                    sjgdje = sjgdje.add(gdje);
                                    sjcsys = sjcsys.add(csys);
                                    sjspzje = sjspzje.add(spzje);
                                    sjsjysy = sjsjysy.add(sjysy);
                                    sjysyys = sjysyys.add(ysyys);
                                    sjsyky = sjsyky.add(syky);
                                    htkthree = htkthree.add(htkfo);
                                    sqfthree = sqfthree.add(sqffo);
                                    yjgthree = yjgthree.add(yjgfo);
                                    htxcwthree = htxcwthree.add(htxcwfo);
                                    fyqcwthree = fyqcwthree.add(fyqcwfo);
                                    yfjecwthree = yfjecwthree.add(yfjefo);

                                    ejzys = ejzys.add(zys);
                                    ejgdje = ejgdje.add(gdje);
                                    ejcsys = ejcsys.add(csys);
                                    ejspzje = ejspzje.add(spzje);
                                    ejsjysy = ejsjysy.add(sjysy);
                                    ejysyys = ejysyys.add(ysyys);
                                    ejsyky = ejsyky.add(syky);
                                    htktwo = htktwo.add(htkfo);
                                    sqftwo = sqftwo.add(sqffo);
                                    yjgtwo = yjgtwo.add(yjgfo);
                                    htxcwtwo = htxcwtwo.add(htxcwfo);
                                    fyqcwtwo = fyqcwtwo.add(fyqcwfo);
                                    yfjecwtwo = yfjecwtwo.add(yfjefo);

                                    yjzys = yjzys.add(zys);
                                    yjgdje = yjgdje.add(gdje);
                                    yjcsys = yjcsys.add(csys);
                                    yjspzje = yjspzje.add(spzje);
                                    yjsjysy = yjsjysy.add(sjysy);
                                    yjysyys = yjysyys.add(ysyys);
                                    yjsyky = yjsyky.add(syky);
                                    htkone = htkone.add(htkfo);
                                    sqfone = sqfone.add(sqffo);
                                    yjgone = yjgone.add(yjgfo);
                                    htxcwone = htxcwone.add(htxcwfo);
                                    fyqcwone = fyqcwone.add(fyqcwfo);
                                    yfjecwone = yfjecwone.add(yfjefo);

                                    if (!"".equals(ssjselect)) {
                                        ssjselect += " UNION ALL ";
                                    }
                                    ssjselect += "select " + xmxxBUDGETMEMBER_1 + "," + (zys.toString()) + "," + (csys.toString())
                                            + "," + (gdje.toString()) + "," + (spzje.toString()) + "," + (sjysy.toString())
                                            + "," + (ysyys.toString()) + "," + (syky.toString()) + ",'" + ssjkmBUDGETMEMBER_3 + "', " + ssjdisplayOrder + ",'4级'," + (htkfo.toString()) + "," + (sqffo.toString()) + "," + (yjgfo.toString()) + "," + (htxcwfo.toString()) + "," + (fyqcwfo.toString()) + "," + (yfjefo.toString()) + " from dual ";
                                }

                                if (!"".equals(sjselect)) {
                                    sjselect += " UNION ALL ";
                                }
                                sjselect += "select " + xmxxBUDGETMEMBER_1 + "," + (sjzys.toString()) + "," + (sjcsys.toString())
                                        + "," + (sjgdje.toString()) + "," + (sjspzje.toString()) + "," + (sjsjysy.toString())
                                        + "," + (sjysyys.toString()) + "," + (sjsyky.toString()) + ",'" + sjkmBUDGETMEMBER_2 + "', " + sjdisplayOrder + ",'3级'," + (htkthree.toString()) + "," + (sqfthree.toString()) + "," + (yjgthree.toString()) + "," + (htxcwthree.toString()) + "," + (fyqcwthree.toString()) + "," + (yfjecwthree.toString()) + " from dual "
                                        + " UNION ALL " + ssjselect;

                            }
                            if (!"".equals(ejselect)) {
                                ejselect += " UNION ALL ";
                            }
                            ejselect += "select " + xmxxBUDGETMEMBER_1 + "," + (ejzys.toString()) + "," + (ejcsys.toString()) + "," + (ejgdje.toString())
                                    + "," + (ejspzje.toString()) + "," + (ejsjysy.toString()) + "," + (ejysyys.toString()) + "," + (ejsyky.toString())
                                    + ",'" + ejkmBUDGETMEMBER_5 + "', " + ejdisplayOrder + ",'2级'," + (htktwo.toString()) + "," + (sqftwo.toString()) + "," + (yjgtwo.toString()) + "," + (htxcwtwo.toString()) + "," + (fyqcwtwo.toString()) + "," + (yfjecwtwo.toString()) + " from dual "
                                    + " UNION ALL " + sjselect;

                        }
                        if (!"".equals(yjselect)) {
                            yjselect += " UNION ALL ";
                        }
//            	yjselect += "select "+xmxxBUDGETMEMBER_1+","+(yjzys.toString())+","+(yjcsys.toString())+","+(yjgdje.toString())+","+(yjspzje.toString())
//            			+","+(yjsjysy.toString())+","+(yjysyys.toString())+","+(yjsyky.toString())
//            			+",'"+yjkmBUDGETMEMBER_4+"',"+yjdisplayOrder+",'1级',"+ htkone+","+sqfone+","+yjgone+"," + htxcwone  +","+fyqcwone+","+yfjecwone+" from dual "
//            			+" UNION ALL "+ejselect ;

                        String insertSql = "select " + xmxxBUDGETMEMBER_1 + "," + (yjzys.toString()) + "," + (yjcsys.toString()) + "," + (yjgdje.toString()) + "," + (yjspzje.toString())
                                + "," + (yjsjysy.toString()) + "," + (yjysyys.toString()) + "," + (yjsyky.toString())
                                + ",'" + yjkmBUDGETMEMBER_4 + "'," + yjdisplayOrder + ",'1级'," + (htkone.toString()) + "," + (sqfone.toString()) + "," + (yjgone.toString()) + "," + (htxcwone.toString()) + "," + (fyqcwone.toString()) + "," + (yfjecwone.toString()) + " from dual "
                                + " UNION ALL " + ejselect;

                        //xmxx,zys,csys,gdje,spzje,sjysy,ysyys,syky,subjectId,displayOrder,subjectLeve,htk,sqf,yjg,htxcw,fyqcw,yfje
                        rs.executeUpdate(insertintosql + " " + insertSql);
                        //sum
//				sumzys = sumzys.add(yjzys);
//				sumcsys = sumcsys.add(yjcsys);
//				sumhtk = sumhtk.add(htkone);
//				sumsqf = sumsqf.add(sqfone);
//				sumyjg = sumyjg.add(yjgone);
//				sumhtx = sumhtx.add(htxcwone);
//				sumfyq = sumfyq.add(fyqcwone);
//				sumyfj = sumyfj.add(yfjecwone);
                        //rs.executeUpdate(insertintosql+" "+yjselect);
                    }
//            //sum
//			String sumsql = " select '-1' as xmxx, " +
//							"'A.00' as bm ," +
//							"-1 as id , " +
//							"'合计' as SUBJECTNAME , " +
//							"'-1' as subjectID ," +
//							"'0级' as subjectLeve ," +
//							""+sumzys.toString()+" as zys ," +
//							""+sumcsys.toString()+" as csys ," +
//							"0 as displayOrder ," +
//							""+sumhtk.toString()+" as htk ," +
//							""+sumsqf.toString()+" as sqf ," +
//							""+sumyjg.toString()+" as yjg ," +
//							"(select zjzmj from uf_xmxx where id = "+usexmxx+") as zjzmj ," +
//							""+sumyfj.toString()+" as yfje ," +
//							""+sumfyq.toString()+" as fyqcw," +
//							""+sumhtx.toString()+" as htxcw from dual";
////            rs.execute(insertintosql+" "+sumsql);


                    //htk,sqf,yjg,zjzmj,yfje,fyqcw,htxcw,
                    String backFields1 = "xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,displayOrder,(csys/zjzmj) as dwcs,(zys/zjzmj) as dwzj,(to_number(htk)+to_number(sqf)) as dtcbz , (to_number(yjg)+to_number(sqf)) as sjcbz, (to_number(sqf)+to_number(yfje)) as sjspz,(to_number(fyqcw)+to_number(htxcw)) as sjcwz , (htk+sqf)/zjzmj as dtcbzjmd,(yjg+sqf)/zjzmj as sjcbzjmd,(sqf+yfje)/zjzmj as sjspzjmd,(fyqcw+htxcw)/zjzmj as sjcwzjmd,FORDER " +
                            "";
                    String backFields2 = "xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,displayOrder, dwcs,dwzj,dtcbz,sjcbz,sjspz,sjcwz ,dtcbzjmd, sjcbzjmd,sjspzjmd,sjcwzjmd ,''as bz,FORDER";
            /*String fromSql1 = " fnaqcstatement a "
            		+ " join "+subjectTableName+" b on b.id = a.SUBJECTID "
            		+ " left join uf_km c on ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,4) "
            		+ " or ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,5) ";*/
                    String fromSql1 = "  " +
                            "(  " +
                            "SELECT " +
                            " TBA.* ,fs.DISPLAYORDER AS FORDER  " +
                            "FROM " +
                            " ( " +
                            " SELECT " +
                            "  a.xmxx, " +
                            "  c.bm, " +
                            "  c.id, " +
                            "  b.SUBJECTNAME, " +
                            "  a.subjectId, " +
                            "  a.subjectLeve, " +
                            "  a.zys, " +
                            "  a.csys, " +
                            "  a.displayOrder, " +
                            "  a.htk, " +
                            "  a.sqf, " +
                            "  a.yjg, " +
                            "  xma.zjzmj, " +
                            "  a.yfje, " +
                            "  a.fyqcw, " +
                            "  a.htxcw  " +
                            " FROM " +
                            "  fnaqcstatement a " +
                            "  JOIN FnaBudgetSubject_1 b ON b.id = a.SUBJECTID " +
                            "  LEFT JOIN uf_km c ON '' || c.id = regexp_substr( b.ACCOUNTCODE, '[^_]+', 1, 4 ) " +
                            "  LEFT JOIN uf_xmxx xma ON xma.id = a.xmxx  " +
                            " WHERE " +
                            "  1 = 1  " +
                            "  AND a.SUBJECTLEVE IN ( '1级', '2级', '3级' ) UNION ALL " +
                            " SELECT " +
                            "  a.xmxx, " +
                            "  c.bm, " +
                            "  c.id, " +
                            "  b.SUBJECTNAME, " +
                            "  a.subjectId, " +
                            "  a.subjectLeve, " +
                            "  a.zys, " +
                            "  a.csys, " +
                            "  a.displayOrder, " +
                            "  a.htk, " +
                            "  a.sqf, " +
                            "  a.yjg, " +
                            "  xma.zjzmj, " +
                            "  a.yfje, " +
                            "  a.fyqcw, " +
                            "  a.htxcw  " +
                            " FROM " +
                            "  fnaqcstatement a " +
                            "  JOIN FnaBudgetSubject_1 b ON b.id = a.SUBJECTID " +
                            "  LEFT JOIN uf_km c ON '' || c.id = regexp_substr( b.ACCOUNTCODE, '[^_]+', 1, 5 ) " +
                            "  LEFT JOIN uf_xmxx xma ON xma.id = a.xmxx  " +
                            " WHERE " +
                            "  1 = 1  " +
                            "  AND a.SUBJECTLEVE = '4级'  " +
                            " ) TBA " +
                            " LEFT JOIN fnabudgetsubject_1 fs ON ( TBA.subjectId = fs.id ) " +
                            " )";

                    String sqlWhere1 = " where 1=1 ";
                    String orderBy1 = " xmxx,bm,FORDER";
                    String groupby1 = " xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,displayOrder,zjzmj,htk,sqf,yjg,yfje,fyqcw,htxcw,FORDER";
                    String groupby3 = "";
                    //(csys/zjzmj) as dwcs,(zys/zjzmj) as dwzj,(htk+sqf) as dtcbz,(yjg+sqf) as sjcbz,(sqf+yfje) as sjspz,(fyqcw+htxcw) as sjcwz ,(htk+sqf)/zjzmj as dtcbzjmd,(yjg+sqf)/zjzmj as sjcbzjmd,(sqf+yfje)/zjzmj as sjspzjmd,(fyqcw+htxcw)/zjzmj as sjcwzjmd
                    String groupby2 = " xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,displayOrder,dwcs,dwzj,dtcbz,sjcbz,sjspz,sjcwz,dtcbzjmd,sjcbzjmd,sjspzjmd,sjcwzjmd,FORDER";
                    //String sqlprimarykey1 = " subjectId ";
                    if (!"".equals(xmbrowser)) {
                        String[] xmbrowserArray = xmbrowser.split(",");
                        String xmbrowserwherein = "";
                        for (int i = 0; i < xmbrowserArray.length; i++) {
                            if (!"".equals(xmbrowserwherein)) {
                                xmbrowserwherein += ",";
                            }
                            xmbrowserwherein += "'" + xmbrowserArray[i] + "'";
                        }
                        sqlWhere1 += " and xmxx in( " + xmbrowserwherein + ", '-1')";
                    }
                    if ("".equals(subjectLeaveserach) || "0".equals(subjectLeaveserach)) {
                        if ("".equals(sjkmserach)) {
                            if (!"".equals(sanjkmserach)) {
                                subjectLeaveserach = "3";
                            } else {
                                if (!"".equals(ejkmserach)) {
                                    subjectLeaveserach = "2";
                                } else {
                                    if (!"".equals(yjkmserach)) {
                                        subjectLeaveserach = "1";
                                    }
                                }
                            }
                        }
                    }
                    if ("1".equals(subjectLeaveserach)) {  //判断科目等级，一级显示一级科目，二级显示一二级科目，三级显示一二三级科目
                        sqlWhere1 += " and subjectLeve in('1级' , '0级') ";
                        //如果只显示一级，则只查一级相关科目
                        if (!"".equals(yjkmserach)) {
            		/*String[] yjkmserachArray = yjkmserach.split(",");
            		String yjkmwherein = "";
            		for(int i = 0;i<yjkmserachArray.length;i++){
        				if(!"".equals(yjkmwherein)){
        					yjkmwherein+=",";
        				}
        				yjkmwherein+="'"+yjkmserachArray[i]+"'";
        			}*/
                            String yj = getSupSubjectAllId(subjectTableName, yjkmserach, "2");
                            sqlWhere1 += " and subjectId in('-1'," + yj + ")";

                        }
                    } else if ("2".equals(subjectLeaveserach)) {
                        sqlWhere1 += " and subjectLeve in('1级' ,'2级', '0级') ";
                        //如果显示二级科目，则查询二级相关科目及对应科目的一级科目
                        if (!"".equals(ejkmserach)) {

            		/*String[] ejkmserachArray = ejkmserach.split(",");
            		String ejkmwherein = "";
            		for(int i = 0;i<ejkmserachArray.length;i++){
        				if(!"".equals(ejkmwherein)){
        					ejkmwherein+=",";
        				}
        				ejkmwherein+="'"+ejkmserachArray[i]+"'";
        			}*/
                            String ej = getSupSubjectAllId(subjectTableName, ejkmserach, "3");
                            //String kmserachids = getSupSubjectAllId2( ejkmserach);
                            sqlWhere1 += " and subjectId in('-1'," + ej + ")";
                        }
                    } else if ("3".equals(subjectLeaveserach)) {
                        sqlWhere1 += " and subjectLeve in('1级' ,'2级' ,'3级','0级') ";
                        if (!"".equals(sanjkmserach)) {
            		/*String[] sanjkmserachArray = sanjkmserach.split(",");
            		String sanjkmwherein = "";
            		for(int i = 0;i<sanjkmserachArray.length;i++){
            			if(!"".equals(sanjkmwherein)){
            				sanjkmwherein+=",";
            			}
            			sanjkmwherein+="'"+sanjkmserachArray[i]+"'";
            		}*/
                            String sj = getSupSubjectAllId(subjectTableName, sanjkmserach, "4");
                            //String kmserachids = getSupSubjectAllId2( sanjkmserach);
                            sqlWhere1 += " and subjectId in('-1'," + sj + ")";
                        }
                    } else {
                        //sqlWhere1 += " and subjectLeve in('1级' ,'2级' ,'3级') ";
                        if (!"".equals(sjkmserach)) {
            		/*String[] sjkmserachArray = sjkmserach.split(",");
            		String sjkmwherein = "";
            		for(int i = 0;i<sjkmserachArray.length;i++){
            			if(!"".equals(sjkmwherein)){
            				sjkmwherein+=",";
            			}
            			sjkmwherein+="'"+sjkmserachArray[i]+"'";
            		}*/
                            String sj = getSupSubjectAllId(subjectTableName, sjkmserach, "5");
                            //String kmserachids = getSupSubjectAllId2byname( sjkmserach);
                            sqlWhere1 += " and subjectId in('-1'," + sj + ")";
                        }
                    }

                    //SUM
                    String sumFields = " SUM(zys) AS zys, SUM(csys) AS csys," +
                            "SUM( csys / zjzmj ) AS dwcs, " +
                            "SUM( zys / zjzmj ) AS dwzj, " +
                            "SUM( to_number(htk)+to_number(sqf)) AS dtcbz, " +
                            "SUM( to_number(yjg)+to_number(sqf)) AS sjcbz, " +
                            "SUM( to_number(sqf)+to_number(yfje)) AS sjspz, " +
                            "SUM( to_number(fyqcw)+to_number(htxcw)) AS sjcwz, " +
                            "SUM( ( htk + sqf ) / zjzmj ) AS dtcbzjmd, " +
                            "SUM( ( yjg + sqf ) / zjzmj ) AS sjcbzjmd, " +
                            "SUM( ( sqf + yfje ) / zjzmj ) AS sjspzjmd, " +
                            "SUM( ( fyqcw + htxcw ) / zjzmj ) AS sjcwzjmd  ";
                    String serchsumsql = "SELECT " + sumFields + "from" + fromSql1 + " " + sqlWhere1 + " AND subjectLeve ='1级'";
                    RecordSet recordSet = new RecordSet();
                    recordSet.execute(serchsumsql);
                    //			BigDecimal sumzys = new BigDecimal("0.00");
//			BigDecimal sumcsys = new BigDecimal("0.00");
//			BigDecimal sumhtk = new BigDecimal("0.00");
//			BigDecimal sumsqf = new BigDecimal("0.00");
//			BigDecimal sumyjg = new BigDecimal("0.00");
//			BigDecimal sumhtx = new BigDecimal("0.00");
//			BigDecimal sumfyq = new BigDecimal("0.00");
//			BigDecimal sumyfj = new BigDecimal("0.00");
                    String sumzys = "";
                    String sumcsys = "";
                    String sumdwcs = "";
                    String sumdwzj = "";
                    String sumdtcbz = "";
                    String sumsjcbz = "";
                    String sumsjspz = "";
                    String sumsjcwz = "";
                    String sumdtcbzjmd = "";
                    String sumsjcbzjmd = "";
                    String sumsjspzjmd = "";
                    String sumsjcwzjmd = "";
                    while (recordSet.next()) {
                        sumzys = Util.null2String(recordSet.getString("zys"));
                        sumcsys = Util.null2String(recordSet.getString("csys"));
                        sumdwcs = Util.null2String(recordSet.getString("dwcs"));
                        sumdwzj = Util.null2String(recordSet.getString("dwzj"));
                        sumdtcbz = Util.null2String(recordSet.getString("dtcbz"));
                        sumsjcbz = Util.null2String(recordSet.getString("sjcbz"));
                        sumsjspz = Util.null2String(recordSet.getString("sjspz"));
                        sumsjcwz = Util.null2String(recordSet.getString("sjcwz"));
                        sumdtcbzjmd = Util.null2String(recordSet.getString("dtcbzjmd"));
                        sumsjcbzjmd = Util.null2String(recordSet.getString("sjcbzjmd"));
                        sumsjspzjmd = Util.null2String(recordSet.getString("sjspzjmd"));
                        sumsjcwzjmd = Util.null2String(recordSet.getString("sjcwzjmd"));
                    }
                    //xmxx,bm,id,SUBJECTNAME,subjectId,subjectLeve,zys,csys,displayOrder,(csys/zjzmj) as dwcs,
                    // (zys/zjzmj) as dwzj,(htk+sqf) as dtcbz,(yjg+sqf) as sjcbz,
                    // (sqf+yfje) as sjspz,(fyqcw+htxcw) as sjcwz ,(htk+sqf)/zjzmj as dtcbzjmd,
                    // (yjg+sqf)/zjzmj as sjcbzjmd,(sqf+yfje)/zjzmj as sjspzjmd,(fyqcw+htxcw)/zjzmj as sjcwzjmd
                    String fromSql2 = "( select '-1' as xmxx, " +
                            "'A.00' as bm ," +
                            " -1 as id ," +
                            "'合计' as SUBJECTNAME ," +
                            "'-1' as subjectID ," +
                            "'0级' as subjectLeve ," +
                            "" + sumzys + " as zys ," +
                            "" + sumcsys + " as csys ," +
                            "0 as displayOrder ," +
                            "" + sumdwcs + " as dwcs ," +
                            "" + sumdwzj + " as dwzj ," +
                            "" + sumdtcbz + " as dtcbz  ," +
                            "" + sumsjcbz + " as sjcbz ," +
                            "" + sumsjspz + " as sjspz ," +
                            "" + sumsjcwz + " as sjcwz ," +
                            "" + sumdtcbzjmd + " as dtcbzjmd ," +
                            "" + sumsjcbzjmd + " as sjcbzjmd ," +
                            "" + sumsjspzjmd + " as sjspzjmd ," +
                            "" + sumsjcwzjmd + " as sjcwzjmd , " +
                            "        -1        as FORDER " +
                            " from dual" +
                            " UNION all" +
                            " SELECT " + backFields1 + " from " + fromSql1 + sqlWhere1 + " GROUP BY " + groupby1 + " ORDER BY " + orderBy1 + " )";
                    //设置  backFields2, fromSql2, sqlWhere1, orderBy1, groupby2
                    dto.setBackFields(backFields2);
                    dto.setFromSql(fromSql2);
                    dto.setSqlWhere(sqlWhere1);
                    dto.setOrderBy(orderBy1);
                    dto.setGroupby(groupby2);
                } catch (Exception e) {
                    errorMsg = e.getMessage();
                }
            }

        } else {
            errorMsg = "未获取到grofillDataTableName";
        }
        dto.setErrorMsg(errorMsg);
        bb.writeLog("dto :" + dto);
        return dto;


    }

    public BigDecimal getBigDecimalAmount(String value) {
        BigDecimal result = new BigDecimal("0.00");
        try {
            result = new BigDecimal(value);
        } catch (Exception e) {
            result = new BigDecimal("0.00");
        }
        return result;
    }

    public String getSupSubjectAllId(String subjectTableName, String queryid, String subjectLeave) {
        RecordSet rs = new RecordSet();
        Map<Integer, Integer> codeMap = new SubjectUtil().getSubjectCodeRule(); //科目编码的位数
        int first_old = codeMap.get(1);
        int second_old = codeMap.get(2);
        int third_old = codeMap.get(3);
        int fourth_old = codeMap.get(4);
        String subjectCodeall = "'-1'";
        if ("".equals(queryid)) {
            return "";
        }
        String sql = "select a.subjectCode from " + subjectTableName + " a "
                + " join fnaqcstatement b on a.id = b.subjectId "
                + " where  a.SUBJECTLEVEL = " + subjectLeave + " and ( ";
        if ("5".equals(subjectLeave)) {
            sql += "SUBJECTNAME like '%" + queryid + "%')";
        } else {
            String[] queryidArray = queryid.split(",");
            for (int i = 0; i < queryidArray.length; i++) {
                if (i > 0) {
                    sql += " or ";
                }
                sql += " a.ACCOUNTCODE like '%\\_" + queryidArray[i] + "' escape '\\' ";
            }
            sql += ")";
        }
        new BaseBean().writeLog(sql);
        rs.executeQuery(sql);
        while (rs.next()) {
            String subjectCode = Util.null2String(rs.getString("subjectCode"));
            if (!"".equals(subjectCodeall)) {
                subjectCodeall += ",";
            }
            if ("2".equals(subjectLeave)) {
                subjectCodeall += "'" + subjectCode + "'";
            } else if ("3".equals(subjectLeave)) { //1,2
                subjectCodeall += "'" + subjectCode.substring(0, first_old + second_old) + "' ";
                subjectCodeall += ",'" + subjectCode + "'";
            } else if ("4".equals(subjectLeave)) {
                subjectCodeall += "'" + subjectCode.substring(0, first_old + second_old) + "' ";
                subjectCodeall += ",'" + subjectCode.substring(0, first_old + second_old + third_old) + "' ";
                subjectCodeall += ",'" + subjectCode + "'";
            } else if ("5".equals(subjectLeave)) {
                subjectCodeall += "'" + subjectCode.substring(0, first_old + second_old) + "' ";
                subjectCodeall += ",'" + subjectCode.substring(0, first_old + second_old + third_old) + "' ";
                subjectCodeall += ",'" + subjectCode.substring(0, first_old + second_old + third_old + fourth_old) + "' ";
                subjectCodeall += ",'" + subjectCode + "'";
            }
        }
        String ids = "";
        rs.executeQuery("select id from " + subjectTableName + " where subjectCode in(" + subjectCodeall + ")");
        while (rs.next()) {
            String id = Util.null2String(rs.getString("id"));
            if (!"".equals(ids)) {
                ids += ",";
            }
            ids += "'" + id + "'";
        }
        return ids;
    }

    public String getSupSubjectAllId2(String queryid) {
        RecordSet rs = new RecordSet();
        String ids = "";
        StringBuffer sql = new StringBuffer();
        sql.append("select id,mc,sj from uf_km  ");
		/*sql.append("where (1=2  ");
		sql.append("or id in (").append(queryid).append(")  ");
		sql.append(")   ");*/
        sql.append(" start with id in (" + queryid + ")  ");
        sql.append(" connect by prior sj = id ");
        new BaseBean().writeLog(sql.toString());
        rs.executeQuery(sql.toString());
        while (rs.next()) {
            String id = Util.null2String(rs.getString("id"));
            if (!"".equals(ids)) {
                ids += ",";
            }
            ids += id;
        }
        return ids;
    }

    public String getSupSubjectAllId2byname(String queryname) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select id,mc,cj from uf_km  where  fl = 0 and cj = 3 and mc like '%" + queryname + "%' ");
        String queryids = "";
        while (rs.next()) {
            String id = Util.null2String(rs.getString("id"));
            if (!"".equals(queryids)) {
                queryids += ",";
            }
            queryids += id;
        }
        return getSupSubjectAllId2(queryids);
    }
}
//SELECT a.xmxx,a.bm,a.id,a.SUBJECTNAME,a.subjectId,a.subjectLeve,a.zys,a.csys,a.gdje,a.spzje,a.sjysy,a.ysyys,a.syky,a.displayOrder,b.sqfyje,c.ygjsje,c.htkzje
//from (select a.xmxx,c.bm,c.id,b.SUBJECTNAME,a.subjectId,a.subjectLeve,a.zys,a.csys,a.gdje,a.spzje,a.sjysy,a.ysyys,a.syky,a.displayOrder FROM fnaqcstatement a join FnaBudgetSubject_1 b on b.id = a.SUBJECTID left join uf_km c on ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,4) where 1=1 and a.SUBJECTLEVE in('1级','2级','3级') UNION all  select  a.xmxx,c.bm,c.id,b.SUBJECTNAME,a.subjectId,a.subjectLeve,a.zys,a.csys,a.gdje,a.spzje,a.sjysy,a.ysyys,a.syky,a.displayOrder  from fnaqcstatement a join FnaBudgetSubject_1 b on b.id = a.SUBJECTID  left join uf_km c on ''||c.id = regexp_substr(b.ACCOUNTCODE,'[^_]+',1,5)  where 1=1  and a.SUBJECTLEVE ='4级') a LEFT JOIN  uf_fysqtz_dt1 b on b.km = a.subjectid left join uf_htxx_dt1 c on c.km = a.subjectid
//ORDER BY displayOrder