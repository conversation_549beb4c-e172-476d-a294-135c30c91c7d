/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.cmd.costCompare;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.systeminfo.SystemEnv;

import com.api.browser.bean.SearchConditionItem;
import com.api.browser.bean.SearchConditionOption;
import com.api.browser.util.ConditionFactory;
import com.api.browser.util.ConditionType;
import com.api.fna.util.FnaConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
/**
 * Title: GetSearchInfoCmd
 * <AUTHOR>
 * @date 2020年12月24日
 * Description:
 */
public class GetSearchInfoCmd extends AbstractCommonCommand<Map<String, Object>>{
    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetSearchInfoCmd(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        String xmxx = Util.null2String(params.get("xmxx"));
        String accountId = Util.null2String(params.get("accountId"));

        Map<String, Object> result = new HashMap<String, Object>();
        RecordSet rSet = new RecordSet();
        List<Map<String, Object>> grouplist = new ArrayList<Map<String, Object>>();
        ConditionFactory conditionFactory = new ConditionFactory(user);

        //常用条件
        Map<String, Object> groupitem1 = new HashMap<String, Object>();
        List<SearchConditionItem> itemlist1 = new LinkedList<SearchConditionItem>();

        if("".equals(xmxx) || "0".equals(xmxx)){ //如果点了项目树，不显示项目的筛选
            //项目
            SearchConditionItem xmtem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "xmbrowser","fnaxmbrowser");
            xmtem.getBrowserConditionParam().getDataParams().put("accountId", accountId);
            xmtem.getBrowserConditionParam().getDestDataParams().put("accountId", accountId);
            xmtem.getBrowserConditionParam().getConditionDataParams().put("accountId", accountId);
            xmtem.setLabel("项目");
            xmtem.getBrowserConditionParam().setIsSingle(false);
            itemlist1.add(xmtem);


        }/*else{
        	String[] typeIdArray = {"4,5,2"};
        	String[] typeNameArray = {"一级科目","二级科目","三级科目"};
        	for(int i = 0;i<typeIdArray.length;i++){
        		String typeId = typeIdArray[i];
        		String typeName = typeNameArray[i];
        		SearchConditionItem kmItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km_"+typeId,"fnaqcstats");
        		kmItem.setLabel(typeName);

        		kmItem.getBrowserConditionParam().getDataParams().put("accountId", accountId);
        		kmItem.getBrowserConditionParam().getDestDataParams().put("accountId", accountId);
        		kmItem.getBrowserConditionParam().getConditionDataParams().put("accountId", accountId);

        		kmItem.getBrowserConditionParam().getDataParams().put("xmxx", xmxx);
        		kmItem.getBrowserConditionParam().getDestDataParams().put("xmxx", xmxx);
        		kmItem.getBrowserConditionParam().getConditionDataParams().put("xmxx", xmxx);

        		kmItem.getBrowserConditionParam().getDataParams().put("typeId", typeId);
        		kmItem.getBrowserConditionParam().getDestDataParams().put("typeId", typeId);
        		kmItem.getBrowserConditionParam().getConditionDataParams().put("typeId", typeId);
        		kmItem.getBrowserConditionParam().setIsSingle(false);

                itemlist1.add(kmItem);
        	}

        }*/
        SearchConditionItem km1Item = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km_4","fnaqckmnew");
        km1Item.setLabel("一级科目");
        km1Item.getBrowserConditionParam().getDataParams().put("typeId", "4");
        km1Item.getBrowserConditionParam().getDestDataParams().put("typeId", "4");
        km1Item.getBrowserConditionParam().getConditionDataParams().put("typeId", "4");
        km1Item.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(km1Item);

        SearchConditionItem km2Item = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km_5","fnaqckmnew");
        km2Item.setLabel("二级科目");
        km2Item.getBrowserConditionParam().getDataParams().put("typeId", "5");
        km2Item.getBrowserConditionParam().getDestDataParams().put("typeId", "5");
        km2Item.getBrowserConditionParam().getConditionDataParams().put("typeId", "5");
        km2Item.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(km2Item);

        SearchConditionItem km3Item = conditionFactory.createCondition(ConditionType.BROWSER, -1, "km_2","fnaqckmnew");
        km3Item.setLabel("三级科目");
        km3Item.getBrowserConditionParam().getDataParams().put("typeId", "2");
        km3Item.getBrowserConditionParam().getDestDataParams().put("typeId", "2");
        km3Item.getBrowserConditionParam().getConditionDataParams().put("typeId", "2");
        km3Item.getBrowserConditionParam().setIsSingle(false);
        itemlist1.add(km3Item);
        SearchConditionItem km4Item = conditionFactory.createCondition(ConditionType.INPUT, -1, "km_3");
        km4Item.setLabel("四级科目");
        itemlist1.add(km4Item);

        List<SearchConditionOption> subjectLeaveOptions = new ArrayList<SearchConditionOption>();
        subjectLeaveOptions.add(new SearchConditionOption("0",""));//
        subjectLeaveOptions.add(new SearchConditionOption("1","一级"));//一级
        subjectLeaveOptions.add(new SearchConditionOption("2","二级"));//二级
        subjectLeaveOptions.add(new SearchConditionOption("3","三级"));//三级
        SearchConditionItem subjectLeaveItem = conditionFactory.createCondition(ConditionType.SELECT, "-1", "subjectLeave", subjectLeaveOptions);
        subjectLeaveItem.setLabel("科目等级");
        itemlist1.add(subjectLeaveItem);

        groupitem1.put("title", SystemEnv.getHtmlLabelName(1361, user.getLanguage()));//基本信息
        groupitem1.put("defaultshow", true);
        groupitem1.put("items", itemlist1);
        grouplist.add(groupitem1);

        result.put("status", "1");
        result.put(FnaConstant.FNA_RESULT_CONDITIONS, grouplist);

        return result;
    }
}
