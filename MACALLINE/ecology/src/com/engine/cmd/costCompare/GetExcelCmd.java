package com.engine.cmd.costCompare;

import com.api.fna.util.ExcelOutUtil;
import com.engine.cmd.costCompare.dto.GetListDto;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.FnaAmountPointComInfo;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import com.engine.util.CostUtil;
import weaver.conn.RecordSet;
import weaver.file.ExcelFile;
import weaver.file.ExcelRow;
import weaver.file.ExcelSheet;
import weaver.file.ExcelStyle;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class GetExcelCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 响应
     */
    private HttpServletResponse response;
    private int amountPoint;

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetExcelCmd(Map<String, Object> params, User user, HttpServletResponse response) {
        this.params = params;
        this.user = user;
        this.response = response;
        this.amountPoint = Util.getIntValue(new FnaAmountPointComInfo().getAmountPoint("" + FnaAccTypeConstant.AMOUNT_POINT_ID));
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> datas = new HashMap<String, Object>();
        new BaseBean().writeLog("GetExcelCmd--START");

        GetListCmd3 cmd = new GetListCmd3(params, user);
        GetListDto dto = cmd.getListDto();
        if (!dto.getErrorMsg().isEmpty()) {
            datas.put("status", "-1");
            datas.put("errorInfo", dto.getErrorMsg());
        } else {
            ArrayList<String> col = new ArrayList<>();
            col.add("xmxx");
            col.add("bm");
            col.add("SUBJECTNAME");
            col.add("subjectLeve");
            col.add("zys");
            col.add("dwzj");
            col.add("csys");
            col.add("dwcs");
            col.add("dtcbz");
            col.add("dtcbzjmd");
            col.add("sjcbz");
            col.add("sjcbzjmd");
            col.add("sjspz");
            col.add("sjspzjmd");
            col.add("sjcwz");
            col.add("sjcwzjmd");

            ArrayList<String> colex = new ArrayList<>();
            colex.add("项目");
            colex.add("编码");
            colex.add("项目名称");
            colex.add("项目等级");
            colex.add("执行预算总价");
            colex.add("执行预算建面单方");
            colex.add("目标成本总价");
            colex.add("目标成本建面单方");
            colex.add("动态成本总价");
            colex.add("动态成本建面单方");
            colex.add("实际成本总价");
            colex.add("实际成本建面单方");
            colex.add("实际审批支出总价");
            colex.add("实际审批支出建面单方");
            colex.add("实际财务支出合计");
            colex.add("实际财务支出建面单方");

            try {
                createExcel("SELECT " + dto.getBackFields() + " from " + dto.getFromSql() + dto.getSqlWhere() + "GROUP BY " + dto.getGroupby() + " ORDER BY " + dto.getOrderBy(),
                        "成本对比表",
                        col,
                        colex,
                        user,
                        response);
                datas.put("status", "1");
            } catch (IOException e) {
                datas.put("errorInfo", e.getMessage());
                datas.put("status", "-1");
            }
        }


        return datas;

    }


    public static int createExcel(String sql, String sheetname, ArrayList<String> cols, ArrayList<String> excelcols, User user, HttpServletResponse response) throws IOException {
        if (cols.size() != excelcols.size()) {
            return -1;
        }
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        CostUtil costUtil = new CostUtil();
        ExcelFile excelFile = new ExcelFile();
        excelFile.init();
        ExcelSheet es = new ExcelSheet();
        excelFile.addSheet(sheetname, es);
        es.initRowList(recordSet.getCounts() + 1);
        for (int i = 0; i < cols.size(); i++) {
            //设置列的宽度
            es.addColumnwidth(6000);
        }
        //标题样式
        ExcelStyle excelStyle = excelFile.newExcelStyle("title");
        excelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
        excelStyle.setFontbold(ExcelStyle.Strong_Font); //字体粗细
        //合计
        ExcelStyle summaryStyle = excelFile.newExcelStyle("summary");
        summaryStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
        summaryStyle.setGroundcolor(ExcelStyle.LIGHT_ORANGE_Color);

        //一级
        ExcelStyle first = excelFile.newExcelStyle("first");
        first.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
        first.setGroundcolor(ExcelStyle.LIGHT_GREEN_Color);

        //二级
        ExcelStyle second = excelFile.newExcelStyle("second");
        second.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
        second.setGroundcolor(ExcelStyle.LIGHT_YELLOW_Color);

        //三级
        ExcelStyle thrid = excelFile.newExcelStyle("thrid");
        thrid.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
        thrid.setGroundcolor(ExcelStyle.LIGHT_TURQUOISE_Color);

        //normalStylle
        ExcelStyle normalexcelStyle = excelFile.newExcelStyle("normal");
        normalexcelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
        ExcelRow er = es.newExcelRow(0);
        for (int i = 0; i < cols.size(); i++) {
            er.addStringValue(excelcols.get(i), "title");
        }
        int row = 1;
        while (recordSet.next()) {
            String flag = "";
            ExcelRow er1 = es.newExcelRow(row);
            //颜色设置
            String style = Util.null2String(recordSet.getString("SUBJECTLEVE"));
            if ("0级".equals(style)) {
                flag = "summary";
            } else if ("1级".equals(style)) {
                flag = "first";
            } else if ("2级".equals(style)) {
                flag = "second";
            } else if ("3级".equals(style)) {
                flag = "thrid";
            } else {
                flag = "normal";
            }
            //
            for (int i = 0; i < cols.size(); i++) {
                String inst = Util.null2String(recordSet.getString(cols.get(i)));
                // 前四个为非数字
                if (i < 4) {
                    er1.addStringValue(inst, flag);
                } else {
                    //设置颜色
                    er1.addStringValue(costUtil.getamountdf(inst), flag);
                }
            }
            row++;
        }
        excelFile.setFilename(sheetname);//预算数据填报模板
        ExcelOutUtil excelOutUtil = new ExcelOutUtil();
        excelOutUtil.ExcelOut(user, excelFile, response);
        return 1;
    }

//    public String getSupSubjectAllId(String subjectTableName, String queryid, String subjectLeave) {
//        RecordSet rs = new RecordSet();
//        Map<Integer, Integer> codeMap = new SubjectUtil().getSubjectCodeRule(); //科目编码的位数
//        int first_old = codeMap.get(1);
//        int second_old = codeMap.get(2);
//        int third_old = codeMap.get(3);
//        int fourth_old = codeMap.get(4);
//        String subjectCodeall = "'-1'";
//        if ("".equals(queryid)) {
//            return "";
//        }
//        String sql = "select a.subjectCode from " + subjectTableName + " a "
//                + " join fnaqcstatement b on a.id = b.subjectId "
//                + " where  a.SUBJECTLEVEL = " + subjectLeave + " and ( ";
//        if ("5".equals(subjectLeave)) {
//            sql += "SUBJECTNAME like '%" + queryid + "%')";
//        } else {
//            String[] queryidArray = queryid.split(",");
//            for (int i = 0; i < queryidArray.length; i++) {
//                if (i > 0) {
//                    sql += " or ";
//                }
//                sql += " a.ACCOUNTCODE like '%\\_" + queryidArray[i] + "' escape '\\' ";
//            }
//            sql += ")";
//        }
//        new BaseBean().writeLog(sql);
//        rs.executeQuery(sql);
//        while (rs.next()) {
//            String subjectCode = Util.null2String(rs.getString("subjectCode"));
//            if (!"".equals(subjectCodeall)) {
//                subjectCodeall += ",";
//            }
//            if ("2".equals(subjectLeave)) {
//                subjectCodeall += "'" + subjectCode + "'";
//            } else if ("3".equals(subjectLeave)) { //1,2
//                subjectCodeall += "'" + subjectCode.substring(0, first_old + second_old) + "' ";
//                subjectCodeall += ",'" + subjectCode + "'";
//            } else if ("4".equals(subjectLeave)) {
//                subjectCodeall += "'" + subjectCode.substring(0, first_old + second_old) + "' ";
//                subjectCodeall += ",'" + subjectCode.substring(0, first_old + second_old + third_old) + "' ";
//                subjectCodeall += ",'" + subjectCode + "'";
//            } else if ("5".equals(subjectLeave)) {
//                subjectCodeall += "'" + subjectCode.substring(0, first_old + second_old) + "' ";
//                subjectCodeall += ",'" + subjectCode.substring(0, first_old + second_old + third_old) + "' ";
//                subjectCodeall += ",'" + subjectCode.substring(0, first_old + second_old + third_old + fourth_old) + "' ";
//                subjectCodeall += ",'" + subjectCode + "'";
//            }
//        }
//        String ids = "";
//        rs.executeQuery("select id from " + subjectTableName + " where subjectCode in(" + subjectCodeall + ")");
//        while (rs.next()) {
//            String id = Util.null2String(rs.getString("id"));
//            if (!"".equals(ids)) {
//                ids += ",";
//            }
//            ids += "'" + id + "'";
//        }
//        return ids;
//    }

//    public BigDecimal getBigDecimalAmount(String value) {
//        BigDecimal result = new BigDecimal("0.00");
//        try {
//            result = new BigDecimal(value);
//        } catch (Exception e) {
//            result = new BigDecimal("0.00");
//        }
//        return result;
//    }
//
//    public String getSupSubjectAllId2(String queryid) {
//        RecordSet rs = new RecordSet();
//        String ids = "";
//        StringBuffer sql = new StringBuffer();
//        sql.append("select id,mc,sj from uf_km  ");
//		/*sql.append("where (1=2  ");
//		sql.append("or id in (").append(queryid).append(")  ");
//		sql.append(")   ");*/
//        sql.append(" start with id in (" + queryid + ")  ");
//        sql.append(" connect by prior sj = id ");
//        new BaseBean().writeLog(sql.toString());
//        rs.executeQuery(sql.toString());
//        while (rs.next()) {
//            String id = Util.null2String(rs.getString("id"));
//            if (!"".equals(ids)) {
//                ids += ",";
//            }
//            ids += id;
//        }
//        return ids;
//    }

}
