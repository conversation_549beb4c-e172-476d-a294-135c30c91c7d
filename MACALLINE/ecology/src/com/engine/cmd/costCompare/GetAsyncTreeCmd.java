/**
 * Copyright (c) 2001-2019 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.engine.cmd.costCompare;

import java.util.*;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.entity.FnaAsyncTreeNote;
import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;

import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.general.BaseBean;

/**
 * Title: GetAsyncTreeCmd
 * <AUTHOR>
 * @date 2020年12月23日
 * Description:
 */
public class GetAsyncTreeCmd extends AbstractCommonCommand<Map<String, Object>>{
    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     * @param params
     * @param user
     */
    public GetAsyncTreeCmd(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }

    /**
     * Command类方法实现
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            String accountId = Util.null2String(params.get("accountId"));
            String keyword = Util.null2String(params.get("keyword"));
            String id = Util.null2String(params.get("id"));
            String xmxxs = Util.null2String(params.get("xmxx"));
            BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
            String tableName = budgetApprovalUtil.getTableName(accountId, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE);

            RecordSet rs = new RecordSet();

            List<FnaAsyncTreeNote> nodeList = new LinkedList<FnaAsyncTreeNote>();
            if (!"".equals(tableName)) {
                StringBuffer queryBuffer = new StringBuffer();
                queryBuffer.append(" select a.approvaTypelName,a.approvalVersGroupId,b.xmxx  from ").append(tableName).append(" a "
                        + " join fnaQc978507Init b on a.approvalVersGroupId = b.approvalVersGroupId ");
                queryBuffer.append(" where a.approvaTypelstatus = 1 and a.approvalFillDataSataus = 1 and a.apprvoalActivation = 1 and b.accountId = '"+accountId+"' ");
                if(!"".equals(keyword)){
                    queryBuffer.append(" and a.approvaTypelName like '%"+keyword+"%'");
                }
                if(!"".equals(xmxxs)){
                    queryBuffer.append(" and b.xmxx in("+xmxxs+") ");
                }
                queryBuffer.append(" order by a.approvaTypelName,a.approvalVersGroupId ");
                new BaseBean().writeLog("queryBuffer.toString():"+queryBuffer.toString());
                rs.executeQuery(queryBuffer.toString());
                while (rs.next()) {
                    String approvaTypelName = Util.null2String(rs.getString("approvaTypelName"));
                    String approvalVersGroupId = Util.null2String(rs.getString("approvalVersGroupId"));
                    String xmxx = Util.null2String(rs.getString("xmxx"));

                    FnaAsyncTreeNote fnaAsyncTreeNote = new FnaAsyncTreeNote();
                    fnaAsyncTreeNote.setId(xmxx);
                    fnaAsyncTreeNote.setCanClick(true);
                    fnaAsyncTreeNote.setIsParent(false);
                    fnaAsyncTreeNote.setIsLeaf(true);
                    fnaAsyncTreeNote.setName(approvaTypelName);
                    fnaAsyncTreeNote.setKey(xmxx);
                    fnaAsyncTreeNote.setDomid(xmxx);
                    fnaAsyncTreeNote.setIsopen(false);
                    nodeList.add(fnaAsyncTreeNote);
                }

            }

            if (("").equals(id)) {
                Map<String, Object> data = new HashMap<String, Object>();
                Map<String, Object> rootBearera = new HashMap<String, Object>();
                rootBearera.put("canClick", true);
                rootBearera.put("isParent", true);
                rootBearera.put("name", "项目");//
                rootBearera.put("isSelected", false);
                rootBearera.put("subs", nodeList);
                rootBearera.put("id", "0");
                rootBearera.put("key", "0");
                rootBearera.put("isLeaf", false);
                rootBearera.put("domid", "0");
                rootBearera.put("isopen", true);
                data.put("rootBearer", rootBearera);
                result.put("datas", data);
            }else{
                result.put("datas", nodeList);
            }
            result.put("status", "1");
            result.put("info", "");
        }catch (Exception e){
            result.put("status", "-1");
            result.put("errorInfo", e.getMessage());
        }
        return result;
    }
}
