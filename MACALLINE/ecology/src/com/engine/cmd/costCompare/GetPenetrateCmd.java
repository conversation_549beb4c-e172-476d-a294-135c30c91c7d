package com.engine.cmd.costCompare;

import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.bean.SplitTableOperateBean;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetPenetrateCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    public GetPenetrateCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        String subjectId = Util.null2String(params.get("subjectId"));
        String[] subjects =  subjectId.split(",");
        String type = Util.null2String(params.get("type"));
        int number = Integer.parseInt(type)-1;

        StringBuilder where = new StringBuilder(" WHERE ");
        for (int i = 0; i < subjects.length-1; i++) {
            if(!"".equals(subjects[i])){
                where.append(" KM = '").append(subjects[i]).append("' or ");
            }
        }
        if("".equals(subjects[subjects.length-1])){
            where.append(" 1 = 1 ");
        }else {
            where.append(" KM = '").append(subjects[subjects.length-1]).append("'");
        }

        String[] sqls = {
              "\t(\n" +
                      "\tSELECT\n" +
                      "\t\ttabb.LASTNAME AS sqr,\n" +
                      "\t\ttab.sqrq,\n" +
                      "\t\ttab.dzje,\n" +
                      "\t\ttab.requestid,\n" +
                      "\t\ttab.KM\n" +
                      "\tFROM\n" +
                      "\t\t(\n" +
                      "\t\tSELECT\n" +
                      "\t\t\tb.sqr,\n" +
                      "\t\t\tb.sqrq,\n" +
                      "\t\t\ta.dzje,\n" +
                      "\t\t\tb.requestid,\n" +
                      "\t\t\ta.km \n" +
                      "\t\tFROM\n" +
                      "\t\t\tformtable_main_73_dt1 a\n" +
                      "\t\t\tLEFT JOIN formtable_main_73 b ON a.mainid = b.id\n" +
                      "\t\t\tLEFT JOIN workflow_requestbase c ON b.requestid = c.requestid \n" +
                      "\t\tWHERE\n" +
                      "\t\t\tc.currentnodetype = 3 UNION ALL\n" +
                      "\t\tSELECT\n" +
                      "\t\t\tb.sqr,\n" +
                      "\t\t\tb.sqrq,\n" +
                      "\t\t\ta.djje,\n" +
                      "\t\t\tb.requestid,\n" +
                      "\t\t\ta.km \n" +
                      "\t\tFROM\n" +
                      "\t\t\tformtable_main_73_dt2 a\n" +
                      "\t\t\tLEFT JOIN formtable_main_73 b ON a.mainid = b.id\n" +
                      "\t\t\tLEFT JOIN workflow_requestbase c ON b.requestid = c.requestid \n" +
                      "\t\tWHERE\n" +
                      "\t\t\tc.currentnodetype = 3 UNION ALL\n" +
                      "\t\tSELECT\n" +
                      "\t\t\ta.sqr,\n" +
                      "\t\t\ta.sqrq,\n" +
                      "\t\t\ta.zbje,\n" +
                      "\t\t\ta.requestid,\n" +
                      "\t\t\ta.km \n" +
                      "\t\tFROM\n" +
                      "\t\t\tformtable_main_72 a\n" +
                      "\t\t\tLEFT JOIN workflow_requestbase b ON a.requestid = b.requestid \n" +
                      "\t\tWHERE\n" +
                      "\t\t\tb.currentnodetype = 3 \n" +
                      "\t\t) tab\n" +
                      "\t\tLEFT JOIN hrmresource tabb ON tab.SQR = tabb.id \n" +
                      where.toString()+" "+
                      "\t) ",


          "( SELECT\n" +
                  "\tHTMC as htmc,\n" +
                  "\tHTBH as htbh,\n" +
                  "\tHTKZJE as htkzje,\n" +
                  "\twm_concat ( REQUESTID ) as requestid\t\n" +
                  "\tFROM\n" +
                  "\t\t(\n" +
                  "\t\tSELECT\n" +
                  "\t\t\tb.htmc,\n" +
                  "\t\t\tb.htbh,\n" +
                  "\t\t\ta.htkzje,\n" +
                  "\t\t\tNVL2( b.htsp, b.htsp, '' ) || NVL2( c.htbgsqlc, ',' || c.htbgsqlc, '' ) || NVL2( d.qzbgsqlc, ',' || d.qzbgsqlc, '' ) AS requestid,\n" +
                  "\t\t\ta.KM \n" +
                  "\t\tFROM\n" +
                  "\t\t\tuf_htxx_dt1 a\n" +
                  "\t\t\tLEFT JOIN uf_htxx b ON a.mainid = b.id\n" +
                  "\t\t\tLEFT JOIN uf_htbgxxb c ON b.id = c.htxx\n" +
                  "\t\t\tLEFT JOIN uf_qzbgxxb d ON b.id = d.htxx UNION ALL\n" +
                  "\t\tSELECT\n" +
                  "\t\t\tb.fymc,\n" +
                  "\t\t\t' ' AS htbh,\n" +
                  "\t\t\ta.sqfyje,\n" +
                  "\t\t\tTO_CHAR( b.fysqlc ),\n" +
                  "\t\t\ta.KM \n" +
                  "\t\tFROM\n" +
                  "\t\t\tuf_fysqtz_dt1 a\n" +
                  "\t\t\tLEFT JOIN uf_fysqtz b ON a.mainid = b.id \n" +
                  "\t\t) \n" +
                  where.toString()+" "+
                  "\tGROUP BY\n" +
                  "\t\tHTBH,\n" +
                  "\t\tHTMC,\n" +
                  "\tHTKZJE \n" +
                  "\t)",


          "( SELECT\n" +
                  "\thtmc,\n" +
                  "\thtbh,\n" +
                  "\tygjsje,\n" +
                  "\twm_concat(htjslc) as htjslc\n" +
                  "\tFROM\n" +
                  "\t\t(\n" +
                  "\t\tSELECT\n" +
                  "\t\t\tb.htmc,\n" +
                  "\t\t\tb.htbh,\n" +
                  "\t\t\ta.ygjsje,\n" +
                  "\t\t\te.htjslc,\n" +
                  "\t\t\ta.KM \n" +
                  "\t\tFROM\n" +
                  "\t\t\tuf_htxx_dt1 a\n" +
                  "\t\t\tLEFT JOIN uf_htxx b ON a.mainid = b.id\n" +
                  "\t\t\tLEFT JOIN uf_jsxx e ON b.id = e.ht UNION ALL\n" +
                  "\t\tSELECT\n" +
                  "\t\t\tb.fymc,\n" +
                  "\t\t\t' ' AS htbh,\n" +
                  "\t\t\ta.sqfyje,\n" +
                  "\t\t\tb.fysqlc,\n" +
                  "\t\t\ta.KM \n" +
                  "\t\tFROM\n" +
                  "\t\t\tuf_fysqtz_dt1 a\n" +
                  "\t\t\tLEFT JOIN uf_fysqtz b ON a.mainid = b.id \n" +
                  "\t\t) \n" +
                  where.toString() +" "+
                  "\tGROUP BY\n" +
                  "\thtbh,htmc,ygjsje\n" +
                  "\t)" ,


          "( SELECT\n" +
                  "\thtmc,\n" +
                  "\thtbh,\n" +
                  "\tyfje,\n" +
                  "\twm_concat(htzflc) as htzflc\n" +
                  "\tFROM\n" +
                  "\t\t(\n" +
                  "\t\tSELECT\n" +
                  "\t\t\tb.htmc,\n" +
                  "\t\t\tb.htbh,\n" +
                  "\t\t\ta.yfje,\n" +
                  "\t\t\tc.htzflc,\n" +
                  "\t\t\ta.KM \n" +
                  "\t\tFROM\n" +
                  "\t\t\tuf_htxx_dt1 a\n" +
                  "\t\t\tLEFT JOIN uf_htxx b ON a.mainid = b.id\n" +
                  "\t\t\tLEFT JOIN uf_zfxx c ON b.id = c.htxx UNION ALL\n" +
                  "\t\tSELECT\n" +
                  "\t\t\tb.fymc,\n" +
                  "\t\t\t'' AS htbh,\n" +
                  "\t\t\ta.sqfyje,\n" +
                  "\t\t\tb.fysqlc,\n" +
                  "\t\t\ta.KM \n" +
                  "\t\tFROM\n" +
                  "\t\t\tuf_fysqtz_dt1 a\n" +
                  "\t\t\tLEFT JOIN uf_fysqtz b ON a.mainid = b.id \n" +
                  "\t\t)\n" +
                  where.toString() +" "+
                  "\t\t\tGROUP BY htmc,htbh,yfje )",

                "\t(\n" +
                        "\t\t(\n" +
                        "\t\tSELECT\n" +
                        "\t\t\t* \n" +
                        "\t\tFROM\n" +
                        "\t\t\t(\n" +
                        "\t\t\tSELECT\n" +
                        "\t\t\t\tb.htmc,\n" +
                        "\t\t\t\tb.htbh,\n" +
                        "\t\t\t\ta.cwsjzfje,\n" +
                        "\t\t\t\tb.id,\n" +
                        "\t\t\t\t'1' AS typeone,\n" +
                        "\t\t\t\ta.KM \n" +
                        "\t\t\tFROM\n" +
                        "\t\t\t\tuf_htxx_dt1 a\n" +
                        "\t\t\t\tLEFT JOIN uf_htxx b ON a.mainid = b.id\n" +
                        "\t\t\t\tLEFT JOIN uf_zfxx c ON b.id = c.htxx UNION ALL\n" +
                        "\t\t\tSELECT\n" +
                        "\t\t\t\tb.fymc,\n" +
                        "\t\t\t\t' ' AS htbh,\n" +
                        "\t\t\t\ta.cwsjzfje,\n" +
                        "\t\t\t\tb.id,\n" +
                        "\t\t\t\t'2' AS typeone,\n" +
                        "\t\t\t\ta.KM \n" +
                        "\t\t\tFROM\n" +
                        "\t\t\t\tuf_fysqtz_dt1 a\n" +
                        "\t\t\t\tLEFT JOIN uf_fysqtz b ON a.mainid = b.id \n" +
                        "\t\t\t) \n" +
                        where.toString()+" "+
                        "\t\t) \n" +
                        "\t) "
        };


        String[] colc ={    "sqr,sqrq,dzje,requestid",
                            "htmc,htbh,htkzje,requestid",
                            "htmc,htbh,ygjsje,htjslc",
                            "htmc,htbh,yfje,htzflc",
                            "htmc,htbh,cwsjzfje,id,typeone"
                            };

        String[] name ={"申请人,申请日期,已变更预算,相关信息"
                        ,"合同/费用名称,合同/费用编号,动态控制金额,相关信息"
                        ,"合同/费用名称,合同/费用编号,实际控制金额,相关信息"
                        ,"合同/费用名称,合同/费用编号,实际审批支付金额,相关信息"
                        ,"合同/费用名称,合同/费用编号,实际财务支付金额,相关信息"};


//        StringBuilder where = new StringBuilder(" WHERE ");
//        for (int i = 0; i < subjects.length-1; i++) {
//            if(!"".equals(subjects[i])){
//                where.append(" KM = '").append(subjects[i]).append("' or ");
//            }
//        }
//        if("".equals(subjects[subjects.length-1])){
//            where.append(" 1 = 1 ");
//        }else {
//            where.append(" KM = '").append(subjects[subjects.length-1]).append("'");
//        }
        String[] ans = name[number].split(",");
        String[] coans = colc[number].split(",");
        List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
        for (int i = 0; i < ans.length-2; i++) {
            cols.add(new SplitTableColBean("25%", ans[i],coans[i], coans[i]));
        }
        cols.add(new SplitTableColBean("25%", ans[ans.length-2],coans[ans.length-2], coans[ans.length-2], "com.engine.util.CostUtil.getamountdfbs2" ));
        if(number==4){
            cols.add(new SplitTableColBean("25%", ans[ans.length-1], coans[ans.length-1], coans[ans.length-1]));
            cols.add(new SplitTableColBean("true",coans[coans.length-1]));
        }else{
            cols.add(new SplitTableColBean("25%", ans[ans.length-1], coans[ans.length-1], coans[ans.length-1]
                    , "com.engine.fnaMulDimensions.util.FnaQcStatementUtil.getlcmc"));
        }
//        List<SplitTableColBean> cols2 = new ArrayList<SplitTableColBean>();
//        cols.add(new SplitTableColBean("25%", "", "sqr", "sql"));
//        cols.add(new SplitTableColBean("25%", "申请日期", "sqrq", "sqlq"));
//        cols.add(new SplitTableColBean("25%", "已变更预算", "dzje", "dzje"));
//        cols.add(new SplitTableColBean("25%", "相关流程", "requestid","requestid"));
        SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();

        SplitTableBean tableBean = new SplitTableBean("fnabudgepenetrate","none",PageIdConst.getPageSize("fnabudgetqcDf3",user.getUID(),PageIdConst.FNA),
                "fnabudgepenetrate",colc[number],sqls[number], " where 1=1 ","","","","Asc",cols);
        tableBean.setSqlisdistinct("true");

        tableBean.setOperates(splitTableOperateBean);

        result.putAll(SplitTableUtil.makeListDataResult(tableBean));

        result.put("status", "1");
        return result;
    }

}
