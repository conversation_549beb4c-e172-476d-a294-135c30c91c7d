package com.engine.cmd.costCenter;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.vo.costCenter.DetailDifferLogVo;
import com.engine.vo.costCenter.MainDifferLogVo;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

public class RecordLogCmd extends AbstractCommonCommand<Map<String, Object>> {
    private static final String mainTableName = "uf_mbcbrz";
    private static final String detailTableName = "uf_mbcbrz_dt1";
    private static int moudleId;
    private static Map<String, Object> mapField;

    public RecordLogCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        BaseBean bb = new BaseBean();
        moudleId = Util.getIntValue(bb.getPropValue("mac_formmode", "targetCostLog_moduleId"));
        mapField = new LinkedHashMap<>();
        mapField.put("detail_index", "行下标");
        mapField.put("bjbs", "本级标识");
        mapField.put("cbbm", "成本编码");
        mapField.put("cbkm", "成本科目");
        mapField.put("yszb", "原始指标");
        mapField.put("xs", "系数");
        mapField.put("sw", "单位");
        mapField.put("gcl", "工程量");
        mapField.put("swy", "单价（元）");
        mapField.put("hjwy", "合价（万元）");
        mapField.put("jmsfym2", "建面单方（元/m2）");
        mapField.put("gcljsjfyym", "工程量及单价费用说明");
        mapField.put("zztx", "主责条线");
        mapField.put("sjhy", "涉及合约");
        mapField.put("zbfs", "招标方式");
        mapField.put("cj", "层级");
        mapField.put("sjbs", "上级标识");
        mapField.put("spmb", "是否模版");

    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        int billId;
        MainDifferLogVo mainVo;
        DetailDifferLogVo detailVo;
        List<DetailDifferLogVo> listDetailVo;
        int level;
        //旧数据
        String oldData = Util.null2String(params.get("oldData"));
        //新数据
        String newData = Util.null2String(params.get("newData"));
        //项目信息
        String xmxx = Util.null2String(params.get("xmxx"));
        if (oldData.isEmpty()) {
            result.put("status", "-1");
            result.put("msg", "旧数据为空");
            return result;
        }
        if (newData.isEmpty()) {
            result.put("status", "-1");
            result.put("msg", "新数据为空");
            return result;
        }
        //根据项目id查询项目基本信息状态
        RecordSet rs = new RecordSet();
        String status = "";
        rs.executeQuery("select zt from uf_xmxx where id = ?", xmxx);
        if (rs.next()) {
            status = rs.getString("zt");
        }
        if (!"1".equals(status)) {
            result.put("status", "-1");
            result.put("msg", "状态不可保存日志");
            return result;
        }
        //旧数据对象
        JSONObject joOldData = JSONObject.parseObject(oldData);
        //新数据对象
        JSONObject joNewData = JSONObject.parseObject(newData);

        Map<String, Object> mapOld = new HashMap<>();
        for (Object o : joOldData.entrySet()) {
            Map.Entry<String, Object> entry = (Map.Entry<String, Object>) o;
            mapOld.put(entry.getKey(), entry.getValue());
        }

        Map<String, Object> mapNew = new HashMap<>();
        for (Object o : joNewData.entrySet()) {
            Map.Entry<String, Object> entry = (Map.Entry<String, Object>) o;
            mapNew.put(entry.getKey(), entry.getValue());
        }

        String diffSheet;
        MapDifference<String, Object> difference = Maps.difference(mapOld, mapNew);
        Map<String, MapDifference.ValueDifference<Object>> differenceMap = difference.entriesDiffering();
        for (Map.Entry<String, MapDifference.ValueDifference<Object>> o : differenceMap.entrySet()) {
            diffSheet = o.getKey();
            //旧数据
            JSONObject joOld = joOldData.getJSONObject(diffSheet);
            JSONArray jaOld = joOld.getJSONArray("detail_1");

            //新数据
            JSONObject joNew = joNewData.getJSONObject(diffSheet);
            JSONArray jaNew = joNew.getJSONArray("detail_1");

            Map<String, Object> mapOldDetails = new HashMap<>();
            for (int i = 0; i < jaOld.size(); i++) {
                Map<String, Object> eachJoOld = jaOld.getJSONObject(i);
                mapOldDetails.put(Util.null2String(eachJoOld.get("bjbs")), eachJoOld);
            }

            Map<String, Object> mapNewDetails = new HashMap<>();
            for (int i = 0; i < jaNew.size(); i++) {
                Map<String, Object> eachJoNew = jaNew.getJSONObject(i);
                mapNewDetails.put(Util.null2String(eachJoNew.get("bjbs")), eachJoNew);
            }

            //从旧数据找新数据差异
            for (int i = 0; i < jaOld.size(); i++) {
                Map<String, Object> eachJoOld = jaOld.getJSONObject(i);
                String sheetKey = Util.null2String(eachJoOld.get("bjbs"));
                //从新数据里找该key，如果找不到，则表示该数据被删除
                if (!mapNewDetails.containsKey(sheetKey)) {
                    mainVo = new MainDifferLogVo();
                    mainVo.setXm(xmxx);
                    mainVo.setSheetName(diffSheet);
                    mainVo.setBjbs(sheetKey);
                    mainVo.setXgr(user.getUID());
                    mainVo.setXgsj(TimeUtil.getCurrentTimeString());
                    mainVo.setXgkm(Util.null2String(eachJoOld.get("cbkm")));
                    level = Util.getIntValue((Util.null2String(eachJoOld.get("cj")))) - 1;
                    mainVo.setKmdj(Util.null2String(level));
                    billId = addMainLog(mainVo);
                    listDetailVo = new ArrayList<>();
                    for (Map.Entry<String, Object> entryField : mapField.entrySet()) {
                        for (Map.Entry<String, Object> entry : eachJoOld.entrySet()) {
                            if (entryField.getKey().equals(entry.getKey())) {
                                String oldValue = "";
                                if (entry.getValue() instanceof JSONArray) {
                                    //如果属性值不是String,还需要在比较一层
                                    if (((JSONArray) entry.getValue()).size() > 0) {
                                        oldValue = Util.null2String(((JSONArray) entry.getValue()).getJSONObject(0).getString("name"));
                                    }
                                } else {
                                    oldValue = Util.null2String(entry.getValue());
                                }
                                detailVo = new DetailDifferLogVo();
                                detailVo.setMainId(billId);
                                detailVo.setXglx(entry.getKey());
                                detailVo.setXgqsj(oldValue);
                                detailVo.setXghsj("");
                                listDetailVo.add(detailVo);
                                break;
                            }
                        }
                    }
                    addDetailLog(listDetailVo, billId);
                } else {
                    Map<String, Object> eachJoNew = (Map<String, Object>) mapNewDetails.get(sheetKey);
                    MapDifference<String, Object> EachDifference = Maps.difference(eachJoOld, eachJoNew);
                    if (!EachDifference.areEqual()) {
                        //具体某行数据有差异
                        Map<String, MapDifference.ValueDifference<Object>> EachDifferenceMap = EachDifference.entriesDiffering();
                        listDetailVo = new ArrayList<>();
                        //循环该Map,将所有该行数据有变化的属性记录
                        //循环有序的key map，按顺序插入
                        for (Map.Entry<String, Object> entry : mapField.entrySet()) {
                            for (Map.Entry<String, MapDifference.ValueDifference<Object>> eachProp : EachDifferenceMap.entrySet()) {
                                if (entry.getKey().equals(eachProp.getKey())) {
                                    MapDifference.ValueDifference<Object> vo = EachDifferenceMap.get(eachProp.getKey());
                                    Object leftObj = vo.leftValue();
                                    Object rightObj = vo.rightValue();
                                    String oldValue = "", newValue = "";
                                    if (leftObj instanceof JSONArray) {
                                        if (((JSONArray) leftObj).size() > 0) {
                                            //如果属性值不是String,还需要在比较一层
                                            oldValue = Util.null2String(((JSONArray) leftObj).getJSONObject(0).getString("name"));
                                        }
                                        if (((JSONArray) rightObj).size() > 0) {
                                            //如果属性值不是String,还需要在比较一层
                                            newValue = Util.null2String(((JSONArray) rightObj).getJSONObject(0).getString("name"));
                                        }
                                    } else {
                                        oldValue = Util.null2String(leftObj);
                                        newValue = Util.null2String(rightObj);
                                    }

                                    if (oldValue.equals(newValue)) {
                                        continue;
                                    }
                                    //如果只有detail_index发生变化，说明是新增的数据发生了序号变化，不算变化
                                    if (!"detail_index".equals(eachProp.getKey())) {
                                        detailVo = new DetailDifferLogVo();
                                        detailVo.setXglx(eachProp.getKey());
                                        detailVo.setXgqsj(oldValue);
                                        detailVo.setXghsj(newValue);
                                        listDetailVo.add(detailVo);
                                    }
                                    break;
                                }
                            }
                        }

                        if (listDetailVo.size() != 0) {
                            mainVo = new MainDifferLogVo();
                            mainVo.setXm(xmxx);
                            mainVo.setSheetName(diffSheet);
                            mainVo.setBjbs(sheetKey);
                            mainVo.setXgr(user.getUID());
                            mainVo.setXgsj(TimeUtil.getCurrentTimeString());
                            mainVo.setXgkm(Util.null2String(eachJoOld.get("cbkm")));
                            level = Util.getIntValue((Util.null2String(eachJoOld.get("cj")))) - 1;
                            mainVo.setKmdj(Util.null2String(level));
                            billId = addMainLog(mainVo);
                            if (billId != -1) {
                                addDetailLog(listDetailVo, billId);
                            }
                        }
                    }
                }
            }
            //从新数据找旧数据，没找到则是新增的数据
            for (int i = 0; i < jaNew.size(); i++) {
                Map<String, Object> eachJoNew = jaNew.getJSONObject(i);
                String sheetKey = Util.null2String(eachJoNew.get("bjbs"));
                if (!mapOldDetails.containsKey(sheetKey)) {
                    //本级标识字段旧数据中没有，表示是新增的数据
                    mainVo = new MainDifferLogVo();
                    mainVo.setXm(xmxx);
                    mainVo.setSheetName(diffSheet);
                    mainVo.setBjbs(sheetKey);
                    mainVo.setXgr(user.getUID());
                    mainVo.setXgsj(TimeUtil.getCurrentTimeString());
                    mainVo.setXgkm(Util.null2String(eachJoNew.get("cbkm")));
                    level = Util.getIntValue((Util.null2String(eachJoNew.get("cj")))) - 1;
                    mainVo.setKmdj(Util.null2String(level));
                    billId = addMainLog(mainVo);
                    listDetailVo = new ArrayList<>();
                    for (Map.Entry<String, Object> entryField : mapField.entrySet()) {
                        for (Map.Entry<String, Object> entry : eachJoNew.entrySet()) {
                            if (entryField.getKey().equals(entry.getKey())) {
                                String newValue = "";
                                if (entry.getValue() instanceof JSONArray) {
                                    if (((JSONArray) entry.getValue()).size() > 0) {
                                        //如果属性值不是String,还需要在比较一层
                                        newValue = Util.null2String(((JSONArray) entry.getValue()).getJSONObject(0).getString("name"));
                                    }
                                } else {
                                    newValue = Util.null2String(entry.getValue());
                                }
                                detailVo = new DetailDifferLogVo();
                                detailVo.setMainId(billId);
                                detailVo.setXglx(entry.getKey());
                                detailVo.setXgqsj("");
                                detailVo.setXghsj(newValue);
                                listDetailVo.add(detailVo);
                                break;
                            }
                        }
                    }
                    addDetailLog(listDetailVo, billId);
                }
            }
        }
        result.put("status", "1");
        return result;
    }

    private int addMainLog(MainDifferLogVo vo) {
        int biilId = -1;
        String insertFileds = "yjkm,bjbs,xgkm,kmdj,xgr,xgsj,xm";
        String[] values = new String[7];
        values[0] = vo.getSheetName();
        values[1] = vo.getBjbs();
        values[2] = vo.getXgkm();
        values[3] = vo.getKmdj();
        values[4] = Util.null2String(vo.getXgr());
        values[5] = vo.getXgsj();
        values[6] = vo.getXm();
        try {
            biilId = InsertModuleUtil.ModuleInsert(mainTableName, insertFileds.split(","), values, user.getUID(), moudleId, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return biilId;
    }

    private void addDetailLog(List<DetailDifferLogVo> list, int billId) {
        String insertFileds = "mainid,xglx,xgqsj,xghsj";
        List<Object> values;
        for (DetailDifferLogVo vo : list) {
            values = new ArrayList<>();
            values.add(billId);
            values.add(mapField.get(vo.getXglx()));
            values.add(vo.getXgqsj());
            values.add(vo.getXghsj());
            try {
                InsertModuleUtil.DetailModuleInsert(detailTableName, insertFileds.split(","), values);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
