package com.engine.cmd.costMainDutySummarytable;

import com.engine.cmd.costMainDutySummary.GetListCmd;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class costMainDutySummarytable extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public costMainDutySummarytable(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<String, Object>();
        GetListCmd getListCmd = new GetListCmd(params,user);
        Map<String, Object> map = getListCmd.getTotalSql();
        String sql =  map.get("sql").toString();
        String sumsql = "SELECT taba.PROJECTID,XM.xmmc,taba.DT_CEB,taba.SJ_CEB FROM ( "+sql+" ) taba LEFT JOIN uf_xmxx XM ON ( taba.PROJECTID = XM.ID ) WHERE taba.TIAOXIAN = '合计'";

        RecordSet recordSet = new RecordSet();
        recordSet.execute(sumsql);
        result.put("data", ReportUtil.getJSONList(recordSet.getArray(), recordSet.getData()));
        result.put("status", "1");
        return result;
    }


}
