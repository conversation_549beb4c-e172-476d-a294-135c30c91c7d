package com.engine.cmd.updatemanagergrades;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class UpdateManagerGrades extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public UpdateManagerGrades(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<String, Object>();
        String ssxm = Util.null2String(params.get("ssxm"));
        String ksrq = Util.null2String(params.get("ksrq"));
        String jsrq = Util.null2String(params.get("jsrq"));
        String typeszb = Util.null2String(params.get("typeszb"));

        if("".equals(ssxm)||"".equals(ksrq)||"".equals(jsrq)||"".equals(typeszb)){
            result.put("status", "-1");
            return result;
        }

        RecordSet rs = new RecordSet();
        HashMap<String, Object> map = new HashMap<>();
        String sql = "select count(*) ans from uf_ldjl a left join uf_xmjh b on a.jhjd=b.id where a.xm= "+ssxm+" and a.ldrq>= '"+ksrq+"' and a.ldrq<= '"+jsrq+"' and b.zzbm= "+typeszb+" and ";
        String[] type = {
                "a.ldlx=1 and b.dj=1",
                "a.ldlx=2 and b.dj=1",
                "a.ldlx=1 and b.dj=2",
                "a.ldlx=2 and b.dj=2",
                "a.ldlx=1 and b.dj=3",
                "a.ldlx=2 and b.dj=3"
        };
        double [] number = {-5,-10,-2.5,-5,-1.5,-3};
        for (int i = 0; i < type.length; i++) {
            rs.execute(sql+type[i]);
            String ans = "";
            if(rs.next()){
                ans =  Util.null2String(rs.getString("ans"));
            }
            map.put("value"+i,string2double(ans)*number[i]);
        }
        result.put("data", map);
        result.put("status", "1");
        return result;
    }

    public static double string2double(String str){
        if("".equals(str)){
            return 0.00;
        }
        return Double.parseDouble(str);
    }
}
