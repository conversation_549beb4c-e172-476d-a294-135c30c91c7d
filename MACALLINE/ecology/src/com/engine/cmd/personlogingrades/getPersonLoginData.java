package com.engine.cmd.personlogingrades;

import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.bean.SplitTableOperateBean;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostUtil;
import weaver.conn.RecordSetTrans;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class getPersonLoginData extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public getPersonLoginData(Map<String, Object> params, User user){
        this.params = params;
        this.user = user;
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<String, Object>();
        //带事务控制
        RecordSetTrans rst = new RecordSetTrans();
        //自动提交设为false
        rst.setAutoCommit(false);

        String dlv = "";

        try {
            String starttime = Util.null2String(params.get("startDate"));
            String endtime = Util.null2String(params.get("endDate"));
            if("".equals(starttime)){
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd+HH:mm:ss");//设置日期格式
                String str1 = df.format(new Date());
                String[] stringdate= str1.split("[+]");
                starttime = stringdate[0];
            }
            if("".equals(endtime)){
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd+HH:mm:ss");//设置日期格式
                String str1 = df.format(new Date());
                String[] stringdate= str1.split("[+]");
                endtime = stringdate[0];
            }
            String getperson =
                    "SELECT " +
                            "KHYBM, " +
                            "KHYDW, " +
                            "Ry  " +
                            "FROM " +
                            "uf_xtdlkhry  " +
                            "WHERE " +
                            "Zt = '0'  " +
                            "GROUP BY " +
                            "KHYBM, " +
                            "KHYDW, " +
                            "RY  " +
                            "ORDER BY " +
                            "KHYBM, " +
                            "KHYDW ";
            rst.executeUpdate("DELETE FROM LOGINGRADES");

            Date starttimestamp;
            Date endtimestamp;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try{
                starttimestamp=sdf.parse(starttime);
                endtimestamp=sdf.parse(endtime);
            }catch (ParseException e){
                result.put("status", "1");
                e.printStackTrace();
                return result;
            }
            Calendar c = Calendar.getInstance();
            c.setTime(starttimestamp);
            int days2 = 1 ;  //实际部门中存在的最长工作日
            int days = 0;
            rst.execute(getperson);
            while (rst.next()){
                String depart = Util.null2String(rst.getString("KHYBM"));
                String unit = Util.null2String(rst.getString("KHYDW"));
                String pers = Util.null2String(rst.getString("Ry"));
                days = 0;
                while(c.getTime().getTime()<=endtimestamp.getTime()){
                    User user = new User(Integer.parseInt(pers));
                    HrmScheduleDiffUtil manager = new HrmScheduleDiffUtil();
                    manager.setUser(user);
                    String dat = sdf.format(c.getTime());
                    boolean isworkday = manager.getIsWorkday(dat);
                    if(isworkday){
                        days++;
                    }
                    RecordSetTrans recordSetTrans=new RecordSetTrans();
                    if(isworkday&&!hadlogined(pers,dat,starttime,endtime,recordSetTrans)){
                        String sql = "INSERT INTO LOGINGRADES (DEPART,UNIT,PERSON,NONEDATE) VALUES('"+depart+"','"+unit+"','"+pers+"','"+dat+"')";
                        rst.executeQuery(sql);
                    }
                    c.add(Calendar.DAY_OF_MONTH, 1);
                }
                days2 = Math.max(days,days2);
                c.setTime(starttimestamp);
            }
            String sqltable = "(SELECT " +
                    "TBBM.selectname BM, " +
                    "DW.selectname DW, " +
                    "NVL(SUM(GRADES),0) GRADES, " +
                    "COUNT(RY) RYS " +
                    "FROM " +
                    "uf_xtdlkhry xtd " +
                    "LEFT JOIN( " +
                    "SELECT " +
                    "-1*COUNT(PERSON) GRADES, " +
                    "PERSON " +
                    "FROM " +
                    "LOGINGRADES  " +
                    "GROUP BY " +
                    "PERSON " +
                    ") tab on tab.PERSON = xtd.RY " +
                    "LEFT JOIN (select selectvalue,selectname FROM " +
                    "workflow_selectitem WHERE fieldid= "+EcologyNumber.DEPARTMENT +
                    ") TBBM ON (xtd.KHYBM = TBBM.selectvalue) " +
                    "LEFT JOIN (select selectvalue,selectname FROM " +
                    "workflow_selectitem WHERE fieldid= "+EcologyNumber.UNITS +
                    ") DW ON (xtd.KHYDW = DW.selectvalue) " +
                    "GROUP BY " +
                    "TBBM.selectname, " +
                    "DW.selectname " +
                    "ORDER BY " +
                    "TBBM.selectname, " +
                    "DW.selectname)";
            String sql = "SELECT SUM(GRADES) grades,SUM(RYS) rys FROM " + sqltable  ;
            rst.execute(sql);
            if(rst.next()){
                double grades = rst.getInt("GRADES");
                double rys = rst.getInt("rys");
                dlv = String.valueOf((1.00 + grades/(rys * days2))*100.00) ;
            }
            rst.commit();
        } catch (Exception e) {
            //异常手动回滚
            rst.rollback();
            e.printStackTrace();
        }

        String colms = "BM,DW,GRADES,RYS,GRADES/RYS RYB,bmid,dwid";
        String fromsql = " " +
                "(SELECT " +
                "TBBM.selectname BM, " +
                "xtd.KHYBM bmid, " +
                "DW.selectname DW, " +
                "xtd.KHYDW dwid, " +
                "NVL(SUM(GRADES),0) GRADES, " +
                "COUNT(RY) RYS " +
                "FROM " +
                "uf_xtdlkhry xtd " +
                "LEFT JOIN( " +
                "SELECT " +
                "-1*COUNT(PERSON) GRADES, " +
                "PERSON " +
                "FROM " +
                "LOGINGRADES  " +
                "GROUP BY " +
                "PERSON " +
                ") tab on tab.PERSON = xtd.RY " +
                "LEFT JOIN (select selectvalue,selectname FROM " +
                "workflow_selectitem WHERE fieldid= "+EcologyNumber.DEPARTMENT+") TBBM ON (xtd.KHYBM = TBBM.selectvalue) " +
                "LEFT JOIN (select selectvalue,selectname FROM " +
                "workflow_selectitem WHERE fieldid= "+EcologyNumber.UNITS+") DW ON (xtd.KHYDW = DW.selectvalue) " +
                "GROUP BY " +
                "TBBM.selectname, " +
                "xtd.KHYBM, " +
                "DW.selectname, " +
                "xtd.KHYDW " +
                "ORDER BY " +
                "xtd.KHYBM, " +
                "xtd.KHYDW)";
        String wheresql = " where 1=1 ";
        String Orderby = "bmid,dwid";
        String groupby = "BM,bmid,DW,dwid,GRADES,RYS";

        List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
        cols.add(new SplitTableColBean("25%", "部门", "BM", "BM"
                ,"",""));
        cols.add(new SplitTableColBean("25%", "单位", "DW", "DW"
                ,"",""));
        cols.add(new SplitTableColBean("25%", "分数", "GRADES", "GRADES"
                ,"",""));
        cols.add(new SplitTableColBean("25%", "人数", "RYS", "RYS"
                ,"",""));
        cols.add(new SplitTableColBean("25%", "扣分汇总", "RYB", "RYB"
                ,"com.engine.util.CostUtil.getNumberValue",""));
//        cols.add(new SplitTableColBean("25%", "本期登陆率", "DLV", "DLV"
//                ,"com.engine.util.CostUtil.getNumberValue",""));
        SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();

        SplitTableBean tableBean = new SplitTableBean("getPesonLoginData","none", PageIdConst.getPageSize("getPesonLoginData",user.getUID(),PageIdConst.FNA),
                "getPesonLoginData",colms,fromsql,wheresql,Orderby,groupby,"","Asc",cols);
        // needing
        tableBean.setSqlisdistinct("true");
        tableBean.setOperates(splitTableOperateBean);
        result.putAll(SplitTableUtil.makeListDataResult(tableBean));
        CostUtil costUtil = new CostUtil();
        result.put("dlv",costUtil.getamountdf(dlv));
        result.put("status", "1");
        return result;
    }

    public boolean hadlogined(String id,String date,String startDate,String endDate,RecordSetTrans rst) throws Exception {
        String sql =  "SELECT * from HRMSYSMAINTENANCELOG " +
                        "where OPERATEDATE >= '"+startDate+"'" +
                        "AND OPERATETYPE = '6'" +
                        "AND OPERATEDATE <= '"+endDate+"'" +
                        "AND RELATEDID = '"+id+"'" +
                        "AND OPERATEDATE = '"+date+"'";
        rst.execute(sql);
        return rst.next();

    }
}
