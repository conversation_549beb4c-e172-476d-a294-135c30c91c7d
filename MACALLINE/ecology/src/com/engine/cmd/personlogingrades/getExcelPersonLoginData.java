package com.engine.cmd.personlogingrades;

import com.api.fna.util.ExcelOutUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.fnaMulDimensions.util.FnaAmountPointComInfo;
import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
import com.engine.util.CostUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.file.ExcelFile;
import weaver.file.ExcelRow;
import weaver.file.ExcelSheet;
import weaver.file.ExcelStyle;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class getExcelPersonLoginData  extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 响应
     */
    private HttpServletResponse response;
    private int amountPoint;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public getExcelPersonLoginData(Map<String, Object> params, User user, HttpServletResponse response) {
        this.params = params;
        this.user = user;
        this.response = response;
        this.amountPoint = Util.getIntValue(new FnaAmountPointComInfo().getAmountPoint("" + FnaAccTypeConstant.AMOUNT_POINT_ID));
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        //带事务控制
        RecordSetTrans rst = new RecordSetTrans();
        //自动提交设为false
        rst.setAutoCommit(false);
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            String starttime = Util.null2String(params.get("startDate"));
            String endtime = Util.null2String(params.get("endDate"));
            if("".equals(starttime)){
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd+HH:mm:ss");//设置日期格式
                String str1 = df.format(new Date());
                String[] stringdate= str1.split("[+]");
                starttime = stringdate[0];
            }
            if("".equals(endtime)){
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd+HH:mm:ss");//设置日期格式
                String str1 = df.format(new Date());
                String[] stringdate= str1.split("[+]");
                endtime = stringdate[0];
            }
            String getperson =
                    "SELECT " +
                            "KHYBM, " +
                            "KHYDW, " +
                            "Ry  " +
                            "FROM " +
                            "uf_xtdlkhry  " +
                            "WHERE " +
                            "Zt = '0'  " +
                            "GROUP BY " +
                            "KHYBM, " +
                            "KHYDW, " +
                            "RY  " +
                            "ORDER BY " +
                            "KHYBM, " +
                            "KHYDW ";
            rst.executeUpdate("DELETE FROM LOGINGRADES");
            rst.execute(getperson);
            Date starttimestamp;
            Date endtimestamp;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try{
                starttimestamp=sdf.parse(starttime);
                endtimestamp=sdf.parse(endtime);
            }catch (ParseException e){
                result.put("status", "1");
                e.printStackTrace();
                return result;
            }
            Calendar c = Calendar.getInstance();
            c.setTime(starttimestamp);
            while (rst.next()){
                String depart = Util.null2String(rst.getString("KHYBM"));
                String unit = Util.null2String(rst.getString("KHYDW"));
                String pers = Util.null2String(rst.getString("Ry"));
                while(c.getTime().getTime()<=endtimestamp.getTime()){
                    User user = new User(Integer.parseInt(pers));
                    HrmScheduleDiffUtil manager = new HrmScheduleDiffUtil();
                    manager.setUser(user);
                    String dat = sdf.format(c.getTime());
                    boolean isworkday = manager.getIsWorkday(dat);
                    if(isworkday&&!hadlogined(pers,dat,starttime,endtime)){
                        String sql = "INSERT INTO LOGINGRADES (DEPART,UNIT,PERSON,NONEDATE) VALUES('"+depart+"','"+unit+"','"+pers+"','"+dat+"')";
                        rst.executeQuery(sql);
                    }
                    c.add(Calendar.DAY_OF_MONTH, 1);
                }
                c.setTime(starttimestamp);
            }
            rst.commit();
        } catch (Exception e) {
            //异常手动回滚
            rst.rollback();
            e.printStackTrace();
        }



        String colms = "BM,DW,GRADES,RYS,GRADES/RYS RYB,bmid,dwid";
        String fromsql = " " +
                "(SELECT " +
                "TBBM.selectname BM, " +
                "xtd.KHYBM bmid, " +
                "DW.selectname DW, " +
                "xtd.KHYDW dwid, " +
                "NVL(SUM(GRADES),0) GRADES, " +
                "COUNT(RY) RYS " +
                "FROM " +
                "uf_xtdlkhry xtd " +
                "LEFT JOIN( " +
                "SELECT " +
                "-1*COUNT(PERSON) GRADES, " +
                "PERSON " +
                "FROM " +
                "LOGINGRADES  " +
                "GROUP BY " +
                "PERSON " +
                ") tab on tab.PERSON = xtd.RY " +
                "LEFT JOIN (select selectvalue,selectname FROM " +
                "workflow_selectitem WHERE fieldid= "+EcologyNumber.DEPARTMENT+") TBBM ON (xtd.KHYBM = TBBM.selectvalue) " +
                "LEFT JOIN (select selectvalue,selectname FROM " +
                "workflow_selectitem WHERE fieldid= "+EcologyNumber.UNITS+") DW ON (xtd.KHYDW = DW.selectvalue) " +
                "GROUP BY " +
                "TBBM.selectname, " +
                "xtd.KHYBM, " +
                "DW.selectname, " +
                "xtd.KHYDW " +
                "ORDER BY " +
                "xtd.KHYBM, " +
                "xtd.KHYDW)";
        String wheresql = " where 1=1 ";
        String Orderby = "bmid,dwid";
        String groupby = "BM,bmid,DW,dwid,GRADES,RYS";

        ArrayList<String> col = new ArrayList<>();
        col.add("BM");
        col.add("DW");
        col.add("GRADES");
        col.add("RYS");
        col.add("RYB");

        ArrayList<String> colex = new ArrayList<>();
        colex.add("部门");
        colex.add("单位");
        colex.add("分数");
        colex.add("人数");
        colex.add("扣分汇总");

        try {
            createExcel("select "+colms+" FROM "+fromsql+" "+wheresql+" group by "+groupby+" order by "+Orderby,
                            "登陆情况成绩汇总表",
                                col,
                                colex,
                                user,
                                response);
            result.put("status", "1");
        } catch (IOException e) {
            result.put("status", "-1");
            e.printStackTrace();
        }
        return result;
    }


    public static int createExcel(String sql,String sheetname, ArrayList<String> cols,ArrayList<String> excelcols,User user,HttpServletResponse response) throws IOException {
        if(cols.size()!=excelcols.size()){
            return -1;
        }
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        CostUtil costUtil = new CostUtil();
        ExcelFile excelFile = new ExcelFile();
        excelFile.init();
        ExcelSheet es = new ExcelSheet();
        excelFile.addSheet(sheetname, es);
        es.initRowList(recordSet.getCounts() + 1);
        for (int i = 0; i < cols.size(); i++) {
            //设置列的宽度
            es.addColumnwidth(6000);
        }
        //标题样式
        ExcelStyle excelStyle = excelFile.newExcelStyle("title");
        excelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
        excelStyle.setFontbold(ExcelStyle.Strong_Font); //字体粗细

        ExcelRow er = es.newExcelRow(0);
        for (int i = 0; i < cols.size(); i++) {
            er.addStringValue(excelcols.get(i), "title");
        }
        int row = 1 ;
        while (recordSet.next()){
            String flag = "title";
            ExcelRow er1 = es.newExcelRow(row);
            for (int i = 0; i <cols.size() ; i++) {
                String inst = Util.null2String(recordSet.getString(cols.get(i)));
                if ("RYB".equals(cols.get(i))){
                    er1.addStringValue(costUtil.getamountdf(inst), flag);
//                er1.addStringValue(costUtil.getamountdf(inst), flag); 小数格式化
                }else {
                    er1.addStringValue(inst, flag);
                }
            }
            row++;
        }
        excelFile.setFilename(sheetname);//预算数据填报模板
        ExcelOutUtil excelOutUtil = new ExcelOutUtil();
        excelOutUtil.ExcelOut(user, excelFile, response);
        return 1;
    }


    public boolean hadlogined(String id,String date,String startDate,String endDate) {
        String sql = "SELECT * from HRMSYSMAINTENANCELOG " +
                "where OPERATEDATE >= '" + startDate + "'" +
                "AND OPERATETYPE = '6'" +
                "AND OPERATEDATE <= '" + endDate + "'" +
                "AND RELATEDID = '" + id + "'" +
                "AND OPERATEDATE = '" + date + "'";
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        return recordSet.next();
    }


    public String changeunit(String str){
        String sql = "select selectvalue,selectname FROM workflow_selectitem WHERE fieldid= "+EcologyNumber.UNITS+" AND selectname = '"+str+"'";
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        if(recordSet.next()){
            return Util.null2String(recordSet.getString("selectvalue"));
        }
        return "";
    }

    public String changedepart(String str){
        String sql = "select selectvalue,selectname FROM workflow_selectitem WHERE fieldid= "+EcologyNumber.DEPARTMENT+" AND selectname = '"+str+"'";
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        if(recordSet.next()){
            return Util.null2String(recordSet.getString("selectvalue"));
        }
        return "";
    }
}
