package com.engine.cmd.milestoneAndLightSummary;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class MilestoneAndLightSummaryCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public MilestoneAndLightSummaryCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<String, Object>();
        String type = Util.null2String(params.get("type"));
        String table = "uf_jdldpm";
        String orderby = " order by taba.PECTG_LIGHTS_STR";
        String sumsql = "SELECT taba.*,tabb.mc FROM  "+table+"  taba left join uf_tx tabb on taba.tx = tabb.id "+orderby;
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sumsql);
        result.put("data", ReportUtil.getJSONList(recordSet.getArray(), recordSet.getData()));
        result.put("status", "1");
        return result;
    }

}
