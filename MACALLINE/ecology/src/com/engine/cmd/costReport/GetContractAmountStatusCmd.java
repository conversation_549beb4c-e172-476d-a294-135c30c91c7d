package com.engine.cmd.costReport;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class GetContractAmountStatusCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetContractAmountStatusCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sql = getTotalSql();
        rs.executeQuery(sql);
        result.put("data", ReportUtil.getJSONList(rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }

    private String getTotalSql() {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT ");
        sb.append(" TBA.*, ");
        sb.append(" TBA.STAUS0_TOTAL_AMOUNT + TBA.STAUS1_TOTAL_AMOUNT + TBA.STAUS2_TOTAL_AMOUNT AS TOTAL_AMOUNT, ");
        sb.append(" TBA.STAUS0_CONTROL_AMOUNT + TBA.STAUS1_CONTROL_AMOUNT + TBA.STAUS2_CONTROL_AMOUNT AS CONTROL_AMOUNT, ");
        sb.append(" TBA.STAUS0_PLAN_AMOUNT + TBA.STAUS1_PLAN_AMOUNT + TBA.STAUS2_PLAN_AMOUNT AS PLAN_AMOUNT, ");
        sb.append(" TBA.STAUS0_REAL_AMOUNT + TBA.STAUS1_REAL_AMOUNT + TBA.STAUS2_REAL_AMOUNT AS REAL_AMOUNT, ");
        sb.append(" TBA.STAUS0_PAID_AMOUNT + TBA.STAUS1_PAID_AMOUNT + TBA.STAUS2_PAID_AMOUNT AS PAID_AMOUNT, ");
        sb.append(" TBA.STAUS0_APPROVE_AMOUNT + TBA.STAUS1_APPROVE_AMOUNT + TBA.STAUS2_APPROVE_AMOUNT AS APPROVE_AMOUNT, ");
        sb.append(" TBA.STAUS0_NOTPAID_AMOUNT + TBA.STAUS1_NOTPAID_AMOUNT + TBA.STAUS2_NOTPAID_AMOUNT AS NOTPAID_AMOUNT,  ");
        sb.append(" TBA.STAUS0_CW_AMOUNT + TBA.STAUS1_CW_AMOUNT + TBA.STAUS2_CW_AMOUNT AS CW_AMOUNT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" A.XM, ");
        sb.append(" XM.XMMC, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.HTJE ELSE 0 END ) AS STAUS0_TOTAL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.HTKZJE ELSE 0 END ) AS STAUS0_CONTROL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.YGJSJE1 ELSE 0 END ) AS STAUS0_PLAN_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.SJJSJE ELSE 0 END ) AS STAUS0_REAL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.YFJE ELSE 0 END ) AS STAUS0_PAID_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.SPZJE ELSE 0 END ) AS STAUS0_APPROVE_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.WFJE ELSE 0 END ) AS STAUS0_NOTPAID_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 0 THEN A.CWSJZFJE ELSE 0 END ) AS STAUS0_CW_AMOUNT, ");

        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.HTJE ELSE 0 END ) AS STAUS1_TOTAL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.HTKZJE ELSE 0 END ) AS STAUS1_CONTROL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.YGJSJE1 ELSE 0 END ) AS STAUS1_PLAN_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.SJJSJE ELSE 0 END ) AS STAUS1_REAL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.YFJE ELSE 0 END ) AS STAUS1_PAID_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.SPZJE ELSE 0 END ) AS STAUS1_APPROVE_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.WFJE ELSE 0 END ) AS STAUS1_NOTPAID_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 1 THEN A.CWSJZFJE ELSE 0 END ) AS STAUS1_CW_AMOUNT, ");

        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.HTJE ELSE 0 END ) AS STAUS2_TOTAL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.HTKZJE ELSE 0 END ) AS STAUS2_CONTROL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.YGJSJE1 ELSE 0 END ) AS STAUS2_PLAN_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.SJJSJE ELSE 0 END ) AS STAUS2_REAL_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.YFJE ELSE 0 END ) AS STAUS2_PAID_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.SPZJE ELSE 0 END ) AS STAUS2_APPROVE_AMOUNT, ");
        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.WFJE ELSE 0 END ) AS STAUS2_NOTPAID_AMOUNT,  ");
        sb.append(" SUM( CASE WHEN A.HTZT = 2 THEN A.CWSJZFJE  ELSE 0 END ) AS STAUS2_CW_AMOUNT ");

        sb.append(" FROM ");
        sb.append(" UF_HTXX A LEFT JOIN UF_XMXX XM ON ( A.XM = XM.ID )  ");
        sb.append(" WHERE ");
        sb.append(" A.HTZT IN ( 0, 1, 2 )  ");
        if (!CostReportUtil.checkHrmDept(user) && user.getUID() != 1) {
            sb.append(" and (A.XMZH= ");
            sb.append(user.getUID());
            sb.append(" or A.TJBFZR= ");
            sb.append(user.getUID()).append(" )");
        }


        sb.append(" GROUP BY ");
        sb.append(" A.XM, ");
        sb.append(" XM.XMMC  ");
        sb.append(" ORDER BY ");
        sb.append(" A.XM ");
        sb.append(" ) TBA ");
        return sb.toString();
    }
}
