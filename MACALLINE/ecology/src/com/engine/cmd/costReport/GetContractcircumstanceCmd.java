package com.engine.cmd.costReport;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class GetContractcircumstanceCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetContractcircumstanceCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sql  = "";
        if(CostReportUtil.checkHrmDept(user)||user.getUID()==1){
            sql =  getTotalSql(false,"");
        }else {
            sql =  getTotalSql(true,  String.valueOf(user.getUID()));
        }
        rs.executeQuery(sql);
        result.put("data",  ReportUtil.getJSONList( rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }

    private String getTotalSql(boolean flag, String userid) {
        return flag?
                "SELECT\n" +
                        "\tTAB.XMMC,\n" +
                        "\tnvl( zt0, 0 ) AS zt0,\n" +
                        "\tnvl( zt1, 0 ) AS zt1,\n" +
                        "\tnvl( zt2, 0 ) AS zt2,\n" +
                        "\tnvl( zt3, 0 ) AS zt3,\n" +
                        "\tnvl( zt4, 0 ) AS zt4,\n" +
                        "\t(\n" +
                        "\t\tnvl( zt0, 0 ) + nvl( zt1, 0 ) + nvl( zt2, 0 ) + nvl( zt3, 0 )+nvl( zt4, 0 )\n" +
                        "\t) allnum \n" +
                        "FROM\n" +
                        "\t(\n" +
                        "\tSELECT\n" +
                        "\t\tnum,\n" +
                        "\t\tans,\n" +
                        "\t\txmtb.id,"+
                        "\t\txmtb.xmmc \n" +
                        "\tFROM\n" +
                        "\t\t(\n" +
                        "SELECT \n" +
                        "num,\n" +
                        "xm,\n" +
                        "htzt,\n" +
                        "ans\n" +
                        "FROM(\n" +
                        "with s1 as (\n" +
                        "SELECT\n" +
                        "\tCOUNT( htzt ) num,\n" +
                        "\txm,\n" +
                        "\thtzt,\n" +
                        "\t0 AS lylx,\n" +
                        "\t'00' AS ans \n" +
                        "FROM\n" +
                        "\tuf_htxx a \n" +
                        "WHERE\n" +
                        "\ta.htzt = 0\n" +
                        "\tAND (xmzh = "+userid+" OR  tjbfzr ="+userid+" )\n" +
                        "GROUP BY\n" +
                        "\txm,\n" +
                        "\thtzt \n" +
                        "ORDER BY\n" +
                        "\txm \n" +
                        "),\n" +
                        "s2 as (\n" +
                        "SELECT\n" +
                        "\tCOUNT( htzt ) num,\n" +
                        "\txm,\n" +
                        "\thtzt,\n" +
                        "\tlylx,\n" +
                        "\tTO_CHAR( htzt ) || TO_CHAR( lylx ) AS ans \n" +
                        "FROM\n" +
                        "\tuf_htxx a \n" +
                        "WHERE\n" +
                        "\ta.htzt IN ( 1, 2 )\n" +
                        "\tAND (xmzh = "+userid+"  OR  tjbfzr = "+userid+")\n" +
                        "GROUP BY\n" +
                        "\txm,\n" +
                        "\thtzt,\n" +
                        "\tlylx \n" +
                        "ORDER BY\n" +
                        "\txm  \n" +
                        ")\n" +
                        "SELECT * FROM s1\n" +
                        "UNION ALL\n" +
                        "SELECT * FROM s2\n" +
                        ")\n" +
                        " \n" +
                        "\t\t) TABA\n" +
                        "\tLEFT JOIN uf_xmxx xmtb ON TABA.xm = xmtb.id \n" +
                        "\t) PIVOT ( SUM( num ) FOR ans IN ( '00' AS zt0, '10' AS zt1, '11' AS zt2, '20' AS zt3 ,'21' AS zt4 ) ) TAB  ORDER BY id"
                :
                "SELECT\n" +
                        "\tTAB.XMMC,\n" +
                        "\tnvl( zt0, 0 ) AS zt0,\n" +
                        "\tnvl( zt1, 0 ) AS zt1,\n" +
                        "\tnvl( zt2, 0 ) AS zt2,\n" +
                        "\tnvl( zt3, 0 ) AS zt3,\n" +
                        "\tnvl( zt4, 0 ) AS zt4,\n" +
                        "\t(\n" +
                        "\t\tnvl( zt0, 0 ) + nvl( zt1, 0 ) + nvl( zt2, 0 ) + nvl( zt3, 0 )+nvl( zt4, 0 )\n" +
                        "\t) allnum \n" +
                        "FROM\n" +
                        "\t(\n" +
                        "\tSELECT\n" +
                        "\t\tnum,\n" +
                        "\t\tans,\n" +
                        "\t\txmtb.id,"+
                        "\t\txmtb.xmmc \n" +
                        "\tFROM\n" +
                        "\t\t(\n" +
                        "SELECT \n" +
                        "num,\n" +
                        "xm,\n" +
                        "htzt,\n" +
                        "ans\n" +
                        "FROM(\n" +
                        "with s1 as (\n" +
                        "SELECT\n" +
                        "\tCOUNT( htzt ) num,\n" +
                        "\txm,\n" +
                        "\thtzt,\n" +
                        "\t0 AS lylx,\n" +
                        "\t'00' AS ans \n" +
                        "FROM\n" +
                        "\tuf_htxx a \n" +
                        "WHERE\n" +
                        "\ta.htzt = 0\n" +
                        "GROUP BY\n" +
                        "\txm,\n" +
                        "\thtzt \n" +
                        "ORDER BY\n" +
                        "\txm \n" +
                        "),\n" +
                        "s2 as (\n" +
                        "SELECT\n" +
                        "\tCOUNT( htzt ) num,\n" +
                        "\txm,\n" +
                        "\thtzt,\n" +
                        "\tlylx,\n" +
                        "\tTO_CHAR( htzt ) || TO_CHAR( lylx ) AS ans \n" +
                        "FROM\n" +
                        "\tuf_htxx a \n" +
                        "WHERE\n" +
                        "\ta.htzt IN ( 1, 2 ) \n" +
                        "GROUP BY\n" +
                        "\txm,\n" +
                        "\thtzt,\n" +
                        "\tlylx \n" +
                        "ORDER BY\n" +
                        "\txm  \n" +
                        ")\n" +
                        "SELECT * FROM s1\n" +
                        "UNION ALL\n" +
                        "SELECT * FROM s2\n" +
                        ")\n" +
                        " \n" +
                        "\t\t) TABA\n" +
                        "\tLEFT JOIN uf_xmxx xmtb ON TABA.xm = xmtb.id \n" +
                        "\t) PIVOT ( SUM( num ) FOR ans IN ( '00' AS zt0, '10' AS zt1, '11' AS zt2, '20' AS zt3 ,'21' AS zt4 ) ) TAB  ORDER BY id ";
    }



}
