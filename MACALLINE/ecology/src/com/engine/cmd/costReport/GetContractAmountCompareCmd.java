package com.engine.cmd.costReport;

import com.engine.cmd.proejctItemSummary.GetListCmd;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class GetContractAmountCompareCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetContractAmountCompareCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        String sql;
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        GetListCmd getListCmd = new GetListCmd(params, user);
        sql = Util.null2String(getListCmd.getTotalSql().get("sql"));
        sb.append(" SELECT TBTOP1.*, ");
        sb.append(" XM.XMMC AS NEW_PRJNAME ");
        sb.append(" FROM ( ");
        sb.append(sql);
        sb.append(" )TBTOP1  ");
        sb.append("  LEFT JOIN uf_xmxx XM ON ( TBTOP1.PROJECTID = XM.ID ) ");
        sb.append(" WHERE TBTOP1.KM1 = '合计' ");
        if (!CostReportUtil.checkHrmDept(user) && user.getUID() != 1) {
            sb.append(" and (XM.xmz= ");
            sb.append(user.getUID());
            sb.append(" or XM.tjz= ");
            sb.append(user.getUID()).append(" )");
        }
        sb.append(" order by TBTOP1.PROJECTID ");
        rs.executeQuery(sb.toString());
        result.put("data", ReportUtil.getJSONList(rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}
