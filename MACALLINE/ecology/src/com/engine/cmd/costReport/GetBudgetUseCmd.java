package com.engine.cmd.costReport;

import com.engine.cmd.proejctItemSummary.GetListCmd;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取预算使用
 */
public class GetBudgetUseCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetBudgetUseCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        String sql;
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        GetListCmd getListCmd = new GetListCmd(params, user);
        sql = Util.null2String(getListCmd.getTotalSql().get("sql"));
        sb.append(" SELECT TBTOP1.*,   ");
        sb.append(" XM.XMMC AS NEW_PRJNAME, ");
        sb.append(" TB1.USED_CONTRACT, ");
        sb.append(" TB2.USED_COST, ");
        sb.append(" NVL(TB3_1.PLAN_CONTRACT,0) + NVL(TB3_2.PLAN_CONTRACT,0) +  ");
        sb.append(" NVL(TB3_3.PLAN_CONTRACT,0) + NVL(TB3_4.PLAN_CONTRACT,0) AS PLAN_CONTRACT, ");
        sb.append(" TB4.PLAN_COST, ");
        sb.append(" (NVL(TBTOP1.ZYS,0)-NVL(TB1.USED_CONTRACT,0)-NVL(TB2.USED_COST,0)");
        sb.append(" -( NVL(TB3_1.PLAN_CONTRACT,0) + NVL(TB3_2.PLAN_CONTRACT,0)");
        sb.append(" +NVL(TB3_3.PLAN_CONTRACT,0) + NVL(TB3_4.PLAN_CONTRACT,0) )-NVL(TB4.PLAN_COST,0)) AS UNUSED_COST ");
        sb.append(" FROM ( ");
        sb.append(sql);
        sb.append(" )TBTOP1  ");
        sb.append("  LEFT JOIN uf_xmxx XM ON ( trim(TBTOP1.PROJECTID) = XM.ID ) ");
//        sb.append(" LEFT JOIN ( ");
//        sb.append(" SELECT ");
//        sb.append(" sum( a.htje ) AS USED_CONTRACT, ");
//        sb.append(" a.xm  ");
//        sb.append(" FROM ");
//        sb.append(" formtable_main_119 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
//        sb.append(" WHERE ");
//        sb.append(" 1 = 1  ");
//        sb.append(" AND b.currentnodetype = 3  ");
//        sb.append(" GROUP BY ");
//        sb.append(" a.xm  ");
//        sb.append(" ) TB1 ON (trim(TBTOP1.PROJECTID) = TB1.XM)   ");

        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" sum( a.ygjsje ) AS USED_CONTRACT, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" uf_htxx a  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm ");
        sb.append(" ) TB1 ON (trim(TBTOP1.PROJECTID) = TB1.XM)   ");

        sb.append(" LEFT JOIN ( SELECT ");
        sb.append(" sum( a.fyje ) AS USED_COST, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_98 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype = 3  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm  ");
        sb.append(" ) TB2 ON (trim(TBTOP1.PROJECTID) = TB2.XM)   ");

//        sb.append(" LEFT JOIN ( SELECT ");
//        sb.append(" sum( a.htje ) AS PLAN_CONTRACT, ");
//        sb.append(" a.xm  ");
//        sb.append(" FROM ");
//        sb.append(" formtable_main_119 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
//        sb.append(" WHERE ");
//        sb.append(" b.currentnodetype IN ( 1, 2 )  ");
//        sb.append(" GROUP BY ");
//        sb.append(" a.xm  ");
//        sb.append(" ) TB3 ON (trim(TBTOP1.PROJECTID) = TB3.XM)  ");

        sb.append(" LEFT JOIN (  ");
        sb.append(" SELECT ");
        sb.append(" sum( a.htje ) AS PLAN_CONTRACT, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_119 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype IN ( 1, 2 )  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm ");
        sb.append(" ) TB3_1 ON (trim(TBTOP1.PROJECTID) = TB3_1.XM)  ");

        sb.append(" LEFT JOIN (  ");
        sb.append(" SELECT ");
        sb.append(" sum( a.bgze ) AS PLAN_CONTRACT, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_103 a ");
        sb.append(" LEFT JOIN workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype IN ( 1, 2 )  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm ");
        sb.append(" ) TB3_2 ON (trim(TBTOP1.PROJECTID) = TB3_2.XM)  ");

        sb.append(" LEFT JOIN (  ");
        sb.append(" SELECT ");
        sb.append(" sum( a.bgze ) AS PLAN_CONTRACT, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_185 a ");
        sb.append(" LEFT JOIN workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype IN ( 1, 2 )  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm ");
        sb.append(" ) TB3_3 ON (trim(TBTOP1.PROJECTID) = TB3_3.XM)  ");

        sb.append(" LEFT JOIN (  ");
        sb.append(" SELECT ");
        sb.append(" sum( a.sjjsjejygjsje ) AS PLAN_CONTRACT, ");
        sb.append(" b.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_102_dt1 a ");
        sb.append(" LEFT JOIN formtable_main_102 b ON a.mainid = b.id ");
        sb.append(" LEFT JOIN workflow_requestbase c ON b.requestid = c.requestid  ");
        sb.append(" WHERE ");
        sb.append(" c.currentnodetype IN ( 1, 2 )  ");
        sb.append(" GROUP BY ");
        sb.append(" b.xm ");
        sb.append(" ) TB3_4 ON (trim(TBTOP1.PROJECTID) = TB3_4.XM)  ");

        sb.append(" LEFT JOIN ( SELECT ");
        sb.append(" sum( a.fyje ) AS PLAN_COST, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_98 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype IN ( 1, 2 )  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm  ");
        sb.append(" ) TB4 ON (trim(TBTOP1.PROJECTID) = TB4.XM) ");
        sb.append(" WHERE TBTOP1.KM1 = '合计' ");
        if (!CostReportUtil.checkHrmDept(user) && user.getUID() != 1) {
            sb.append(" and (XM.xmz= ");
            sb.append(user.getUID());
            sb.append(" or XM.tjz= ");
            sb.append(user.getUID()).append(" )");
        }
        sb.append(" order by TBTOP1.PROJECTID,TBTOP1.SUBJECTORDER ");
        rs.executeQuery(sb.toString());
        result.put("data", QueryResultUtil.getJSONArrayList(rs));
        result.put("status", "1");
        return result;
    }
}
