package com.engine.cmd.costReport;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.cmd.proejctItemSummary.GetListCmd;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class GetCostAnalysisCompareCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetCostAnalysisCompareCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        JSONArray listData;
        JSONObject jo;
        JSONObject jsonObject;
        JSONArray resultData = new JSONArray();
        Map<String, Object> mapProject = new LinkedHashMap<>();
        String sql;
        StringBuilder sb = new StringBuilder();
        String PROJECTID, PROJECTNAME, SUBJECTID, KM1, COSTTOTAL, ZYS, DT_PRICE, SJ_PRICE, SPZC_PRICE, CWZC_PRICE;
        RecordSet rs = new RecordSet();
        GetListCmd getListCmd = new GetListCmd(params, user);
        sql = Util.null2String(getListCmd.getTotalSql().get("sql"));
        sb.append(" SELECT TBTOP1.*, ");
        sb.append(" XM.XMMC AS NEW_PRJNAME ");
        sb.append(" FROM ( ");
        sb.append(sql);
        sb.append(" )TBTOP1  ");
        sb.append("  LEFT JOIN uf_xmxx XM ON ( TBTOP1.PROJECTID = XM.ID ) ");
        sb.append(" WHERE 1=1 ");
        if (!CostReportUtil.checkHrmDept(user) && user.getUID() != 1) {
            sb.append(" and (XM.xmz= ");
            sb.append(user.getUID());
            sb.append(" or XM.tjz= ");
            sb.append(user.getUID()).append(" )");
        }
        sb.append(" order by TBTOP1.PROJECTID,TBTOP1.SUBJECTORDER ");
        rs.executeQuery(sb.toString());
        listData = QueryResultUtil.getJSONArrayList(rs);
        if (!listData.isEmpty()) {
            JSONArray jsonSubject;
            for (int i = 0; i < listData.size(); i++) {
                jo = listData.getJSONObject(i);
                PROJECTID = jo.getString("PROJECTID");
                PROJECTNAME = jo.getString("PROJECTNAME");
                SUBJECTID = jo.getString("SUBJECTID");
                KM1 = jo.getString("KM1");
                COSTTOTAL = jo.getString("COSTTOTAL");
                ZYS = jo.getString("ZYS");
                DT_PRICE = jo.getString("DT_PRICE");
                SJ_PRICE = jo.getString("SJ_PRICE");
                SPZC_PRICE = jo.getString("SPZC_PRICE");
                CWZC_PRICE = jo.getString("CWZC_PRICE");

                if (mapProject.containsKey(PROJECTNAME)) {
                    jsonSubject = (JSONArray) mapProject.get(PROJECTNAME);
                } else {
                    jsonSubject = new JSONArray();

                }
                jsonObject = new JSONObject();
                jsonObject.put("PROJECTID", PROJECTID);
                jsonObject.put("PROJECTNAME", PROJECTNAME);
                jsonObject.put("SUBJECTID", SUBJECTID);
                jsonObject.put("KM1", KM1);
                jsonObject.put("COSTTOTAL", COSTTOTAL);
                jsonObject.put("ZYS", ZYS);
                jsonObject.put("DT_PRICE", DT_PRICE);
                jsonObject.put("SJ_PRICE", SJ_PRICE);
                jsonObject.put("SPZC_PRICE", SPZC_PRICE);
                jsonObject.put("CWZC_PRICE", CWZC_PRICE);
                jsonSubject.add(jsonObject);
                if (!mapProject.containsKey(PROJECTNAME)) {
                    mapProject.put(PROJECTNAME, jsonSubject);
                }
            }
        }


        if (!mapProject.isEmpty()) {
            JSONObject obj;
            for (Map.Entry<String, Object> m : mapProject.entrySet()) {
                obj = new JSONObject();
                obj.put("PROJECTNAME", m.getKey());
                obj.put("SUBJECT", m.getValue());
                resultData.add(obj);
            }

        }
        result.put("data", resultData);
        result.put("status", "1");
        return result;
    }
}
