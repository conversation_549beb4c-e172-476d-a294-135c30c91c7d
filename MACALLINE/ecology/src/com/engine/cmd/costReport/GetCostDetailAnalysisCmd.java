package com.engine.cmd.costReport;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.cmd.costMainDuty.GetListCmd;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.general.Util;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 获取成本明细分析
 */
public class GetCostDetailAnalysisCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetCostDetailAnalysisCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        String sql;
        JSONArray listData;
        JSONObject jo;
        JSONArray resultData = new JSONArray();
        String PROJECTNAME;
        String fields = "PROJECTID,NEW_PRJNAME,TIAOXIAN," +
                "COSTTOTAL_TUDI,COSTTOTAL_QIANQI,COSTTOTAL_JIANAN,COSTTOTAL_GUANLI,COSTTOTAL_CAIWU," +
                "ZYS_TUDI,ZYS_QIANQI,ZYS_JIANAN,ZYS_GUANLI,ZYS_CAIWU," +
                "DT_PRICE_TUDI,DT_PRICE_QIANQI,DT_PRICE_JIANAN,DT_PRICE_GUANLI,DT_PRICE_CAIWU," +
                "SJ_PRICE_TUDI,SJ_PRICE_QIANQI,SJ_PRICE_JIANAN,SJ_PRICE_GUANLI,SJ_PRICE_CAIWU," +
                "SPZC_PRICE_TUDI,SPZC_PRICE_QIANQI,SPZC_PRICE_JIANAN,SPZC_PRICE_GUANLI,SPZC_PRICE_CAIWU," +
                "CWZC_PRICE_TUDI,CWZC_PRICE_QIANQI,CWZC_PRICE_JIANAN,CWZC_PRICE_GUANLI,CWZC_PRICE_CAIWU," +
                "COSTTOTAL,ZYS,DT_PRICE,SJ_PRICE,SPZC_PRICE,CWZC_PRICE";
        Map<String, Object> mapProject = new LinkedHashMap<>();
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        GetListCmd getListCmd = new GetListCmd(params, user);

        sql = Util.null2String(getListCmd.getTotalSql().get("sql"));
        sb.append(" SELECT TBTOP1.PROJECTID,   ");
        //sb.append(" TBTOP1.PROJECTNAME, ");
        sb.append(" XM.XMMC AS NEW_PRJNAME, ");
        sb.append(" TBTOP1.TIAOXIAN, ");
        sb.append(" TBTOP1.TIAOXIAN_XH, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '土地费用' then  TBTOP1.COSTTOTAL else 0 end ) AS COSTTOTAL_TUDI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '开发前期费用' then  TBTOP1.COSTTOTAL else 0 end ) AS COSTTOTAL_QIANQI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '建安费用' then  TBTOP1.COSTTOTAL else 0 end ) AS COSTTOTAL_JIANAN, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '管理费用' then  TBTOP1.COSTTOTAL else 0 end ) AS COSTTOTAL_GUANLI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '财务费用' then  TBTOP1.COSTTOTAL else 0 end ) AS COSTTOTAL_CAIWU, ");

        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '土地费用' then  TBTOP1.ZYS else 0 end ) AS ZYS_TUDI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '开发前期费用' then  TBTOP1.ZYS else 0 end ) AS ZYS_QIANQI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '建安费用' then  TBTOP1.ZYS else 0 end ) AS ZYS_JIANAN, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '管理费用' then  TBTOP1.ZYS else 0 end ) AS ZYS_GUANLI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '财务费用' then  TBTOP1.ZYS else 0 end ) AS ZYS_CAIWU, ");

        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '土地费用' then  TBTOP1.DT_PRICE else 0 end ) AS DT_PRICE_TUDI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '开发前期费用' then  TBTOP1.DT_PRICE else 0 end ) AS DT_PRICE_QIANQI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '建安费用' then  TBTOP1.DT_PRICE else 0 end ) AS DT_PRICE_JIANAN, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '管理费用' then  TBTOP1.DT_PRICE else 0 end ) AS DT_PRICE_GUANLI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '财务费用' then  TBTOP1.DT_PRICE else 0 end ) AS DT_PRICE_CAIWU, ");

        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '土地费用' then  TBTOP1.SJ_PRICE else 0 end ) AS SJ_PRICE_TUDI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '开发前期费用' then  TBTOP1.SJ_PRICE else 0 end ) AS SJ_PRICE_QIANQI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '建安费用' then  TBTOP1.SJ_PRICE else 0 end ) AS SJ_PRICE_JIANAN, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '管理费用' then  TBTOP1.SJ_PRICE else 0 end ) AS SJ_PRICE_GUANLI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '财务费用' then  TBTOP1.SJ_PRICE else 0 end ) AS SJ_PRICE_CAIWU, ");

        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '土地费用' then  TBTOP1.SPZC_PRICE else 0 end ) AS SPZC_PRICE_TUDI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '开发前期费用' then  TBTOP1.SPZC_PRICE else 0 end ) AS SPZC_PRICE_QIANQI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '建安费用' then  TBTOP1.SPZC_PRICE else 0 end ) AS SPZC_PRICE_JIANAN, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '管理费用' then  TBTOP1.SPZC_PRICE else 0 end ) AS SPZC_PRICE_GUANLI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '财务费用' then  TBTOP1.SPZC_PRICE else 0 end ) AS SPZC_PRICE_CAIWU, ");

        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '土地费用' then  TBTOP1.CWZC_PRICE else 0 end ) AS CWZC_PRICE_TUDI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '开发前期费用' then  TBTOP1.CWZC_PRICE else 0 end ) AS CWZC_PRICE_QIANQI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '建安费用' then  TBTOP1.CWZC_PRICE else 0 end ) AS CWZC_PRICE_JIANAN, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '管理费用' then  TBTOP1.CWZC_PRICE else 0 end ) AS CWZC_PRICE_GUANLI, ");
        sb.append(" SUM( case when TBTOP1.SUBJECTNAME = '财务费用' then  TBTOP1.CWZC_PRICE else 0 end ) AS CWZC_PRICE_CAIWU, ");

        sb.append(" SUM(TBTOP1.COSTTOTAL) AS COSTTOTAL, ");
        sb.append(" SUM(TBTOP1.ZYS) AS ZYS, ");
        sb.append(" SUM(TBTOP1.DT_PRICE) AS DT_PRICE, ");
        sb.append(" SUM(TBTOP1.SJ_PRICE) AS SJ_PRICE, ");
        sb.append(" SUM(TBTOP1.SPZC_PRICE) AS SPZC_PRICE, ");
        sb.append(" SUM(TBTOP1.CWZC_PRICE) AS CWZC_PRICE  ");
        sb.append(" FROM ( ");
        sb.append(sql);
        sb.append(" )TBTOP1  ");
        sb.append("  LEFT JOIN uf_xmxx XM ON ( trim(TBTOP1.PROJECTID) = XM.ID ) ");
        if (!CostReportUtil.checkHrmDept(user) && user.getUID() != 1) {
            sb.append(" and (XM.xmz= ");
            sb.append(user.getUID());
            sb.append(" or XM.tjz= ");
            sb.append(user.getUID()).append(" )");
        }
        sb.append(" group by TBTOP1.PROJECTID,XM.XMMC,TBTOP1.TIAOXIAN,TBTOP1.TIAOXIAN_XH ");
        sb.append(" order by TBTOP1.PROJECTID,TBTOP1.TIAOXIAN_XH ");
        rs.executeQuery(sb.toString());
        String[] columns = rs.getColumnName();
        listData = QueryResultUtil.getJSONArrayList(rs);
        if (!listData.isEmpty()) {
            JSONArray jsonSubject;
            for (int i = 0; i < listData.size(); i++) {
                jo = listData.getJSONObject(i);
                PROJECTNAME = jo.getString("NEW_PRJNAME");
                if (mapProject.containsKey(PROJECTNAME)) {
                    jsonSubject = (JSONArray) mapProject.get(PROJECTNAME);
                } else {
                    jsonSubject = new JSONArray();
                }
                jsonSubject.add(assemblyData(jo, fields));
                if (!mapProject.containsKey(PROJECTNAME)) {
                    mapProject.put(PROJECTNAME, jsonSubject);
                }
            }
        }


        if (!mapProject.isEmpty()) {
            JSONObject obj;
            for (Map.Entry<String, Object> m : mapProject.entrySet()) {
                obj = new JSONObject();
                obj.put("NEW_PRJNAME", m.getKey());
                obj.put("ITEM", m.getValue());
                resultData.add(obj);
            }
        }

        result.put("data", resultData);
        result.put("status", "1");
        return result;
    }

    /**
     * 拼装数据
     *
     * @param jo
     * @param fields
     * @return
     */
    private JSONObject assemblyData(JSONObject jo, String fields) {
        JSONObject jsonObject = new JSONObject();
        for (String str : fields.split(",")) {
            jsonObject.put(str, jo.getString(str));
        }
        return jsonObject;
    }
}
