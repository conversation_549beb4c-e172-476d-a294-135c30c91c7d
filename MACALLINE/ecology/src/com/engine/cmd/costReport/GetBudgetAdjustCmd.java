package com.engine.cmd.costReport;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取预算调整分析
 */
public class GetBudgetAdjustCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetBudgetAdjustCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        String sql;
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        sb.append(" SELECT ");
        sb.append(" t.id, ");
        sb.append(" t.xmmc, ");
        sb.append(" NVL( TB1.CNT, 0 ) AS CNT_ADD, ");
        sb.append(" NVL( TB2.CNT, 0 ) AS CNT_ALLOCATION, ");
        sb.append(" NVL( TB3.amount, 0 ) AS AMOUNT  ");
        sb.append(" FROM ");
        sb.append(" uf_xmxx t left join ( ");
        sb.append(" SELECT ");
        sb.append(" count( a.id ) AS CNT, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_72 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype = 3  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm  ");
        sb.append(" ) TB1 ON ( t.id = TB1.XM ) LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" count( a.id ) AS cnt, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_73 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype = 3  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm  ");
        sb.append(" ) TB2 ON ( t.id = TB2.XM ) left join ( ");
        sb.append(" SELECT ");
        sb.append(" sum( a.zbje ) AS amount, ");
        sb.append(" a.xm  ");
        sb.append(" FROM ");
        sb.append(" formtable_main_72 a left join workflow_requestbase b ON a.requestid = b.requestid  ");
        sb.append(" WHERE ");
        sb.append(" b.currentnodetype = 3  ");
        sb.append(" GROUP BY ");
        sb.append(" a.xm  ");
        sb.append(" ) TB3 ON ( ");
        sb.append(" t.id = TB3.XM) ");
        sb.append(" WHERE 1=1 ");

        if (!CostReportUtil.checkHrmDept(user) && user.getUID() != 1) {
            sb.append(" and (t.xmz= ");
            sb.append(user.getUID());
            sb.append(" or t.tjz= ");
            sb.append(user.getUID()).append(" )");
        }
        sb.append(" order by t.ID ");
        rs.executeQuery(sb.toString());
        result.put("data", QueryResultUtil.getJSONArrayList(rs));
        result.put("status", "1");
        return result;
    }
}
