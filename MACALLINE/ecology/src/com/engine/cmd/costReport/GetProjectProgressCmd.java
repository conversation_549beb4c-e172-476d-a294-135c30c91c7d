package com.engine.cmd.costReport;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.util.CostReportUtil;
import com.engine.util.ReportUtil;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class GetProjectProgressCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetProjectProgressCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT ");
        sb.append(" XM.ID, ");
        sb.append(" XM.XMMC, ");
        sb.append(" NVL ( TB1.CNT_EMERGENCY_BIDDING, 0 ) AS CNT_EMERGENCY_BIDDING, ");
        sb.append(" NVL ( TB1.CNT_NORMAL_BIDDING, 0 ) AS CNT_NORMAL_BIDDING, ");
        sb.append(" NVL ( TB2.CNT_SIGNED_CONTRACT, 0 ) AS CNT_SIGNED_CONTRACT, ");
        sb.append(" NVL ( TB2.SUM_CONTRACT_AMOUNT, 0 ) AS SUM_CONTRACT_AMOUNT, ");
        sb.append(" NVL ( TB3.CNT_CHANGED_CONTRACT, 0 ) AS CNT_CHANGED_CONTRACT, ");
        sb.append(" NVL ( TB3.SUM_CHANGED_AMOUNT, 0 ) AS SUM_CHANGED_AMOUNT, ");
        sb.append(" NVL ( TB4.CNT_ESCALATION_CONTRACT, 0 ) AS CNT_ESCALATION_CONTRACT, ");
        sb.append(" NVL ( TB4.SUM_ESCALATION_AMOUNT, 0 ) AS SUM_ESCALATION_AMOUNT, ");
        sb.append(" NVL ( TB5.CNT_VISA_CONTRACT, 0 ) AS CNT_VISA_CONTRACT, ");
        sb.append(" NVL ( TB5.SUM_VISA_AMOUNT, 0 ) AS SUM_VISA_AMOUNT, ");
        sb.append(" NVL ( TB6.CNT_DESIGN_CONTRACT, 0 ) AS CNT_DESIGN_CONTRACT, ");
        sb.append(" NVL ( TB6.SUM_DESIGN_AMOUNT, 0 ) AS SUM_DESIGN_AMOUNT  ");
        sb.append(" FROM ");
        sb.append(" UF_XMXX XM ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" XM, ");
        sb.append(" COUNT( CASE WHEN zt = 1 THEN id END ) AS CNT_EMERGENCY_BIDDING, ");
        sb.append(" COUNT( CASE WHEN zt = 0 THEN id END ) AS CNT_NORMAL_BIDDING  ");
        sb.append(" FROM ");
        sb.append(" uf_zblx  ");
        sb.append(" GROUP BY ");
        sb.append(" XM  ");
        sb.append(" ) TB1 ON ( TB1.XM = XM.ID ) ");
        sb.append(" LEFT JOIN ( SELECT XM, COUNT( 1 ) AS CNT_SIGNED_CONTRACT, sum( htje ) AS SUM_CONTRACT_AMOUNT FROM uf_htxx GROUP BY XM ) TB2 ON ( TB2.XM = XM.ID ) ");
        sb.append(" LEFT JOIN ( SELECT XM, COUNT( 1 ) AS CNT_CHANGED_CONTRACT, sum( bgze ) AS SUM_CHANGED_AMOUNT FROM uf_htbgxxb GROUP BY XM ) TB3 ON ( TB3.XM = XM.ID ) ");
        sb.append(" LEFT JOIN ( SELECT XM, COUNT( 1 ) AS CNT_ESCALATION_CONTRACT, sum( bgze ) AS SUM_ESCALATION_AMOUNT FROM uf_qzbgxxb GROUP BY XM ) TB4 ON ( TB4.XM = XM.ID ) ");
        sb.append(" LEFT JOIN ( SELECT XM, COUNT( 1 ) AS CNT_VISA_CONTRACT, sum( bgze ) AS SUM_VISA_AMOUNT FROM uf_xcqzjm GROUP BY XM ) TB5 ON ( TB5.XM = XM.ID ) ");
        sb.append(" LEFT JOIN ( SELECT XM, COUNT( 1 ) AS CNT_DESIGN_CONTRACT, sum( bgze ) AS SUM_DESIGN_AMOUNT FROM uf_sjbgxxb GROUP BY XM ) TB6 ON ( TB6.XM = XM.ID )  ");
        sb.append(" WHERE 1=1 ");
        if (!CostReportUtil.checkHrmDept(user) && user.getUID() != 1) {
            sb.append(" and (XM.xmz= ");
            sb.append(user.getUID());
            sb.append(" or XM.tjz= ");
            sb.append(user.getUID()).append(" )");
        }
        sb.append(" ORDER BY ");
        sb.append(" XM.ID ");
        rs.executeQuery(sb.toString());
        result.put("data", ReportUtil.getJSONList(rs.getArray(), rs.getData()));
        result.put("status", "1");
        return result;
    }
}
