package com.engine.cmd.studyforum;

import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.bean.SplitTableOperateBean;
import com.api.browser.util.SplitTableUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/5/19-17:46
 * @description : 获取列表数据
 */
public class GetListCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造器
     *
     * @param params
     * @param user
     */
    public GetListCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * 主方法
     *
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
        String queryFields;
        String orderBySql = "DEPID,UNIT";

        queryFields = getQueryFields();
        String columNames = getColumnFields();
        String fromSql = "( " + getTotalSql() + " )";
        String whereSql = " where 1=1 ";

        SplitTableBean tableBean = new SplitTableBean("studyforum", "none", PageIdConst.getPageSize("studyforum", user.getUID(), PageIdConst.FNA),
                "studyforum", queryFields, fromSql, whereSql, orderBySql, "", "", "Asc", addCols(queryFields, columNames));

        tableBean.setSqlisdistinct("true");
        tableBean.setOperates(splitTableOperateBean);
        Map<String, Object> result = new HashMap<>(SplitTableUtil.makeListDataResult(tableBean));
        result.put("status", "1");
        return result;
    }

    public String getTotalSql() {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT ");
        sb.append(" TBE.*, ");
        sb.append(" to_char(NVL2( TBN8.CNT_PEOPLE, ROUND( TBE.TOTAL_SCORE / TBN8.CNT_PEOPLE, 2 ), 0.00 ),'FM90.90') AS AVERAGE_SCORE, ");
        sb.append(" to_char(NVL2( TBN9.CNT_PEOPLE, ROUND( TBE.TOTAL_SCORE / TBN9.CNT_PEOPLE, 2 ), 0.00 ),'FM90.90') AS DEP_AVERAGE_SCORE  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBD.ID, ");
        sb.append(" TBD.UNIT, ");
        sb.append(" HR.LASTNAME, ");
        sb.append(" DP.DEPARTMENTNAME, ");
        sb.append(" DP.ID AS DEPID,");
        sb.append(" HJ.JOBTITLENAME, ");
        sb.append(" NVL( TBN1.CNT_NEWPOST, 0 ) AS CNT_NEWPOST, ");
        sb.append(" NVL( TBN1.CNT_ORIGINALPOST, 0 ) AS CNT_ORIGINALPOST, ");
        sb.append(" NVL( TBN2.CNT_LIKE, 0 ) AS CNT_LIKE, ");
        sb.append(" NVL( TBN3.CNT_DISED, 0 ) AS CNT_DISED, ");
        sb.append(" NVL( TBN4.CNT_DIS, 0 ) AS CNT_DIS, ");
        sb.append(" NVL( TBN5.CNT_GOODTHEME, 0 ) AS CNT_GOODTHEME, ");
        sb.append(" NVL( TBN6.CNT_GOODDIS, 0 ) AS CNT_GOODDIS, ");
        sb.append(" NVL( TBN6.CNT_POPDIS, 0 ) AS CNT_POPDIS, ");
        sb.append(" NVL( TBN7.CNT_POPLIKE, 0 ) AS CNT_POPLIKE, ");
        sb.append(" ( ");
        sb.append(" NVL( TBN1.CNT_ORIGINALPOST, 0 ) + NVL( TBN2.CNT_LIKE, 0 ) + NVL( TBN3.CNT_DISED, 0 ) + NVL( TBN4.CNT_DIS, 0 ) + NVL( TBN5.CNT_GOODTHEME, 0 ) + NVL( TBN6.CNT_GOODDIS, 0 ) + NVL( TBN6.CNT_POPDIS, 0 ) + NVL( TBN7.CNT_POPLIKE, 0 )  ");
        sb.append(" ) AS TOTAL_SCORE  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBC.ID, ");
        sb.append(" TBC.UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBB.ID, ");
        sb.append(" TBB.UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" REGEXP_SUBSTR( TBA.IDS, '[^,]+', 1, LEVEL ) ID, ");
        sb.append(" TBA.XMMC AS UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TO_CHAR( SUBSTR( CY, 1, 4000 ) ) AS IDS, ");
        sb.append(" XM.XMMC  ");
        sb.append(" FROM ");
        sb.append(" uf_xmzzjg_dt2 d ");
        sb.append(" LEFT JOIN uf_xmzzjg M ON ( D.MAINID = M.ID ) ");
        sb.append(" LEFT JOIN UF_XMXX XM ON ( XM.ID = M.XM )  ");
        sb.append(" WHERE ");
        sb.append(" xmgsgw NOT IN ( 3, 8, 9 )  ");
        sb.append(" ) TBA  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA.IDS, '[^,]+' )  ");
        sb.append(" AND ROWID = PRIOR ROWID  ");
        sb.append(" AND PRIOR DBMS_RANDOM.VALUE IS NOT NULL  ");
        sb.append(" ) TBB UNION ALL ");
        sb.append(" SELECT ");
        sb.append(" TO_CHAR( hr.ID ) AS ID, ");
        sb.append(" d.DEPARTMENTNAME AS UNIT  ");
        sb.append(" FROM ");
        sb.append(" hrmresource hr ");
        sb.append(" LEFT JOIN hrmdepartment d ON ( hr.departmentid = d.ID )  ");
        sb.append(" WHERE ");
        sb.append(" hr.jobtitle = 24  ");
        sb.append(" OR hr.departmentid IN ( 21, 22, 23, 24 )  ");
        sb.append(" ) TBC  ");
        sb.append(" WHERE ");
        sb.append(" TBC.ID IS NOT NULL  ");
        sb.append(" GROUP BY ");
        sb.append(" TBC.ID, ");
        sb.append(" TBC.UNIT  ");
        sb.append(" ) TBD ");
        sb.append(" LEFT JOIN hrmresource HR ON ( HR.ID = TBD.ID ) ");
        sb.append(" LEFT JOIN hrmdepartment DP ON ( HR.departmentid = DP.ID ) ");
        sb.append(" LEFT JOIN HRMJOBTITLES HJ ON ( HR.jobtitle = HJ.ID ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" c.CREATER AS ID, ");
        sb.append(" Count( 1 ) * 5 AS CNT_NEWPOST, ");
        sb.append(" Count( CASE WHEN c.category = 1 THEN 1 END ) * 10 AS CNT_ORIGINALPOST  ");
        sb.append(" FROM ");
        sb.append(" cowork_items c ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh u ON ( c.typeid = u.xzlx )  ");
        sb.append(" WHERE ");
        sb.append(" c.status = 1  ");
        sb.append(" AND u.bklx = 1  ");
        addDateParams(sb, "c.createdate");
        sb.append(" GROUP BY ");
        sb.append(" c.CREATER  ");
        sb.append(" ) TBN1 ON ( TBN1.ID = TBD.ID ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" TBLIKE.ID, ");
        sb.append(" SUM( CNT_LIKE ) AS CNT_LIKE  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" c.CREATER AS ID, ");
        sb.append(" NVL( TBV.CNT_LIKE, 0 ) + NVL( TBI.CNT_LIKE, 0 ) AS CNT_LIKE  ");
        sb.append(" FROM ");
        sb.append(" cowork_items c ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh u ON ( c.typeid = u.xzlx ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" count( 1 ) AS CNT_LIKE, ");
        sb.append(" coworkid  ");
        sb.append(" FROM ");
        sb.append(" cowork_itemvotes  ");
        sb.append(" WHERE ");
        sb.append(" status = 0  ");
        addDateParams(sb, "to_char( to_date( CREATETIME, 'yyyy-mm-dd hh24:mi:ss' ), 'yyyy-mm-dd' )");
        sb.append(" GROUP BY ");
        sb.append(" coworkid  ");
        sb.append(" ) TBV ON ( c.id = TBV.coworkid ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" count( 1 ) AS CNT_LIKE, ");
        sb.append(" b.discussant  ");
        sb.append(" FROM ");
        sb.append(" cowork_votes a ");
        sb.append(" LEFT JOIN cowork_discuss b ON a.discussid = b.id ");
        sb.append(" LEFT JOIN cowork_items c ON b.coworkid = c.id ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh d ON c.typeid = d.xzlx  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1  ");
        sb.append(" AND a.status = 0  ");
        sb.append(" AND b.isdel = 0  ");
        sb.append(" AND c.status = 1  ");
        sb.append(" AND d.bklx = 1  ");
        addDateParams(sb, "a.CREATEDATE");
        sb.append(" GROUP BY ");
        sb.append(" b.discussant  ");
        sb.append(" ) TBI ON ( c.id = TBI.discussant )  ");
        sb.append(" WHERE ");
        sb.append(" c.status = 1  ");
        sb.append(" AND u.bklx = 1  ");
        //sb.append(" AND c.category = 1  ");
        sb.append(" ) TBLIKE  ");
        sb.append(" GROUP BY ");
        sb.append(" TBLIKE.ID  ");
        sb.append(" ) TBN2 ON ( TBN2.ID = TBD.ID ) ");

        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT TMP3.USERID, ");
        sb.append(" SUM(TMP3.CNT_DISED) AS CNT_DISED ");
        sb.append("  FROM ( ");
        sb.append(" SELECT ");
        sb.append(" d.commentuserid AS USERID , ");
        sb.append(" count( d.id ) * 2 AS CNT_DISED  ");
        sb.append(" FROM ");
        sb.append(" cowork_discuss d ");
        sb.append(" LEFT JOIN cowork_items i ON ( d.coworkid = i.id ) ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh u ON ( i.typeid = u.xzlx )  ");
        sb.append(" WHERE ");
        sb.append(" i.status = 1  ");
        sb.append(" AND u.bklx = 1  ");
        sb.append(" AND d.isdel = 0  ");
        addDateParams(sb, "d.CREATEDATE");
        sb.append(" GROUP BY ");
        sb.append(" d.commentuserid  ");
        sb.append(" UNION ALL ");
        sb.append(" SELECT ");
        sb.append(" b.creater AS USERID, ");
        sb.append(" count( a.id ) AS CNT_DISED  ");
        sb.append(" FROM ");
        sb.append(" cowork_discuss a ");
        sb.append(" LEFT JOIN cowork_items b ON a.coworkid = b.id ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh c ON b.typeid = c.xzlx  ");
        sb.append(" WHERE ");
        sb.append(" a.isdel = 0  ");
        sb.append(" AND b.STATUS = 1  ");
        sb.append(" AND c.bklx = 1  ");
        sb.append(" AND b.category = 1  ");
        sb.append(" AND a.commentid = 0  ");
        addDateParams(sb, "a.createdate");
        sb.append(" GROUP BY ");
        sb.append(" b.creater ");
        sb.append(" )TMP3 GROUP BY TMP3.USERID ");
        sb.append(" ) TBN3 ON ( TBN3.USERID = TBD.ID ) ");

        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" d.discussant, ");
        sb.append(" count( d.id ) AS CNT_DIS  ");
        sb.append(" FROM ");
        sb.append(" cowork_discuss d ");
        sb.append(" LEFT JOIN cowork_items i ON ( d.coworkid = i.id ) ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh u ON ( i.typeid = u.xzlx )  ");
        sb.append(" WHERE ");
        sb.append(" i.status = 1  ");
        sb.append(" AND u.bklx = 1  ");
        //sb.append(" AND i.category = 1  ");
        sb.append(" AND d.ISDEL = 0  ");
        addDateParams(sb, "d.CREATEDATE ");
        sb.append(" GROUP BY ");
        sb.append(" d.discussant  ");
        sb.append(" ) TBN4 ON ( TBN4.discussant = TBD.ID ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" I.CREATER, ");
        sb.append(" COUNT( I.ID ) * 10 AS CNT_GOODTHEME  ");
        sb.append(" FROM ");
        sb.append(" cowork_items I ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh U ON ( I.typeid = U.xzlx )  ");
        sb.append(" WHERE ");
        sb.append(" I.status = 1  ");
        sb.append(" AND U.bklx = 1  ");
        //sb.append(" AND I.category = 1  ");
        sb.append(" AND I.highquality = 1  ");
        addDateParams(sb, "i.CREATEDATE");
        sb.append(" GROUP BY ");
        sb.append(" I.CREATER  ");
        sb.append(" ) TBN5 ON ( TBN5.CREATER = TBD.ID ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" d.commentuserid, ");
        sb.append(" count( CASE WHEN u.bklx = 1 AND i.highquality = 1 THEN d.id END ) * 10 AS CNT_GOODDIS, ");
        sb.append(" count( CASE WHEN u.bklx = 0 THEN d.id END ) * 2 AS CNT_POPDIS  ");
        sb.append(" FROM ");
        sb.append(" cowork_discuss d ");
        sb.append(" LEFT JOIN cowork_items i ON ( d.coworkid = i.id ) ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh u ON ( i.typeid = u.xzlx )  ");
        sb.append(" WHERE ");
        sb.append(" i.status = 1  ");
        //sb.append(" AND i.category = 1  ");
        sb.append(" AND d.isdel = 0  ");
        addDateParams(sb, "d.CREATEDATE ");
        sb.append(" GROUP BY ");
        sb.append(" d.commentuserid  ");
        sb.append(" ) TBN6 ON ( TBN6.commentuserid = TBD.ID ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" count( 1 ) AS CNT_POPLIKE, ");
        sb.append(" b.discussant  ");
        sb.append(" FROM ");
        sb.append(" cowork_votes a ");
        sb.append(" LEFT JOIN cowork_discuss b ON a.discussid = b.id ");
        sb.append(" LEFT JOIN cowork_items c ON b.coworkid = c.id ");
        sb.append(" LEFT JOIN uf_xxsqztkhwh d ON c.typeid = d.xzlx  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1  ");
        sb.append(" AND a.status = 0  ");
        sb.append(" AND b.isdel = 0  ");
        sb.append(" AND c.status = 1  ");
        sb.append(" AND d.bklx = 0  ");
        addDateParams(sb, "a.CREATEDATE ");
        sb.append(" GROUP BY ");
        sb.append(" b.discussant  ");
        sb.append(" ) TBN7 ON ( TBN7.discussant = TBD.ID )  ");
        sb.append(" ) TBE ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" COUNT( TBY.CY ) AS CNT_PEOPLE, ");
        sb.append(" TBY.UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBD.CY, ");
        sb.append(" TBD.UNIT, ");
        sb.append(" DP.DEPARTMENTNAME,  ");
        sb.append(" DP.ID  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBC.CY, ");
        sb.append(" TBC.UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBB.CY, ");
        sb.append(" TBB.UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" REGEXP_SUBSTR( TBA1.CYS, '[^,]+', 1, LEVEL ) CY, ");
        sb.append(" TBA1.XMMC AS UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" d.id, ");
        sb.append(" TO_CHAR( SUBSTR( d.CY, 1, 4000 ) ) AS CYS, ");
        sb.append(" XM.XMMC  ");
        sb.append(" FROM ");
        sb.append(" uf_xmzzjg_dt2 d ");
        sb.append(" LEFT JOIN uf_xmzzjg M ON ( D.MAINID = M.ID ) ");
        sb.append(" LEFT JOIN UF_XMXX XM ON ( XM.ID = M.XM )  ");
        sb.append(" WHERE ");
        sb.append(" d.xmgsgw NOT IN ( 3, 8, 9 )  ");
        sb.append(" ) TBA1  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA1.CYS, '[^,]+' )  ");
        sb.append(" AND ID = PRIOR ID  ");
        sb.append(" AND PRIOR DBMS_RANDOM.VALUE IS NOT NULL  ");
        sb.append(" ) TBB UNION ALL ");
        sb.append(" SELECT ");
        sb.append(" TO_CHAR( hr.ID ) AS CY, ");
        sb.append(" d.DEPARTMENTNAME AS UNIT  ");
        sb.append(" FROM ");
        sb.append(" hrmresource hr ");
        sb.append(" LEFT JOIN hrmdepartment d ON ( hr.departmentid = d.ID )  ");
        sb.append(" WHERE ");
        sb.append(" hr.jobtitle = 24  ");
        sb.append(" OR hr.departmentid IN ( 21, 22, 23, 24 )  ");
        sb.append(" ) TBC  ");
        sb.append(" WHERE ");
        sb.append(" TBC.CY IS NOT NULL  ");
        sb.append(" GROUP BY ");
        sb.append(" TBC.CY, ");
        sb.append(" TBC.UNIT  ");
        sb.append(" ) TBD ");
        sb.append(" LEFT JOIN hrmresource HR ON ( HR.ID = TBD.CY ) ");
        sb.append(" LEFT JOIN hrmdepartment DP ON ( HR.departmentid = DP.ID ) ");
        sb.append(" LEFT JOIN HRMJOBTITLES HJ ON ( HR.jobtitle = HJ.ID )  ");
        sb.append(" ) TBY  ");
        sb.append(" GROUP BY ");
        sb.append(" TBY.UNIT  ");
        sb.append(" ) TBN8 ON ( TBE.UNIT = TBN8.UNIT ) ");
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" COUNT( TBD.CY ) AS CNT_PEOPLE, ");
        sb.append(" DP.DEPARTMENTNAME  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBC.CY, ");
        sb.append(" TBC.UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" TBB.CY, ");
        sb.append(" TBB.UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" REGEXP_SUBSTR( TBA1.CYS, '[^,]+', 1, LEVEL ) CY, ");
        sb.append(" TBA1.XMMC AS UNIT  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" d.id, ");
        sb.append(" TO_CHAR( SUBSTR( d.CY, 1, 4000 ) ) AS CYS, ");
        sb.append(" XM.XMMC  ");
        sb.append(" FROM ");
        sb.append(" uf_xmzzjg_dt2 d ");
        sb.append(" LEFT JOIN uf_xmzzjg M ON ( D.MAINID = M.ID ) ");
        sb.append(" LEFT JOIN UF_XMXX XM ON ( XM.ID = M.XM )  ");
        sb.append(" WHERE ");
        sb.append(" d.xmgsgw NOT IN ( 3, 8, 9 )  ");
        sb.append(" ) TBA1  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1 CONNECT BY LEVEL <= REGEXP_COUNT ( TBA1.CYS, '[^,]+' )  ");
        sb.append(" AND ID = PRIOR ID  ");
        sb.append(" AND PRIOR DBMS_RANDOM.VALUE IS NOT NULL  ");
        sb.append(" ) TBB UNION ALL ");
        sb.append(" SELECT ");
        sb.append(" TO_CHAR( hr.ID ) AS CY, ");
        sb.append(" d.DEPARTMENTNAME AS UNIT  ");
        sb.append(" FROM ");
        sb.append(" hrmresource hr ");
        sb.append(" LEFT JOIN hrmdepartment d ON ( hr.departmentid = d.ID )  ");
        sb.append(" WHERE ");
        sb.append(" hr.jobtitle = 24  ");
        sb.append(" OR hr.departmentid IN ( 21, 22, 23, 24 )  ");
        sb.append(" ) TBC  ");
        sb.append(" WHERE ");
        sb.append(" TBC.CY IS NOT NULL  ");
        sb.append(" GROUP BY ");
        sb.append(" TBC.CY, ");
        sb.append(" TBC.UNIT  ");
        sb.append(" ) TBD ");
        sb.append(" LEFT JOIN hrmresource HR ON ( HR.ID = TBD.CY ) ");
        sb.append(" LEFT JOIN hrmdepartment DP ON ( HR.departmentid = DP.ID ) ");
        sb.append(" LEFT JOIN HRMJOBTITLES HJ ON ( HR.jobtitle = HJ.ID )  ");
        sb.append(" GROUP BY ");
        sb.append(" DP.DEPARTMENTNAME  ");
        sb.append(" ) TBN9 ON ( TBE.DEPARTMENTNAME = TBN9.DEPARTMENTNAME ) ");
        sb.append(" ORDER BY TBE.DEPID ");
        return sb.toString();
    }


    private List<SplitTableColBean> addCols(String colums, String columNames) {
        List<SplitTableColBean> cols = new ArrayList<>();
        String[] columsArr = colums.split(",");
        String[] columNamesArr = columNames.split(",");
        for (int i = 0; i < columsArr.length; i++) {
            if ("DEPID".equals(columsArr[i])) {
                cols.add(new SplitTableColBean("true", columsArr[i]));

            } else {
                cols.add(new SplitTableColBean("25%", columNamesArr[i], columsArr[i], columsArr[i]));
            }
        }
        return cols;
    }

    private void addDateParams(StringBuilder sb, String column) {
        String startDate = Util.null2String(params.get("startDate"));
        String endDate = Util.null2String(params.get("endDate"));
        if (!startDate.isEmpty()) {
            sb.append(" and ");
            sb.append(column);
            sb.append(" >= '");
            sb.append(startDate);
            sb.append("' ");
        }
        if (!endDate.isEmpty()) {
            sb.append(" and ");
            sb.append(column);
            sb.append(" <= '");
            sb.append(endDate);
            sb.append("' ");
        }
    }

    public String getQueryFields() {
        String rst = "";
        rst += "DEPARTMENTNAME,DEPID,UNIT,JOBTITLENAME,LASTNAME,CNT_NEWPOST,CNT_ORIGINALPOST,";
        rst += "CNT_LIKE,CNT_DISED,CNT_DIS,CNT_GOODTHEME,CNT_GOODDIS,CNT_POPDIS,";
        rst += "CNT_POPLIKE,TOTAL_SCORE,AVERAGE_SCORE,DEP_AVERAGE_SCORE";
        return rst;
    }

    public String getColumnFields() {
        String rst = "";
        rst += "部门,部门ID,单位,职务,姓名,发新帖,发原创帖,被点赞,被评论,去评论,优质主题,优质帖/评论,人气活动发帖,";
        rst += "人气活动被点赞,总分,人均得分,推进部人均分";
        return rst;
    }
}
