package com.engine.cmd.studyforum;

import com.api.fna.util.ExcelOutUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.file.ExcelFile;
import weaver.file.ExcelRow;
import weaver.file.ExcelSheet;
import weaver.file.ExcelStyle;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class GetExcelCmd extends AbstractCommonCommand<Map<String, Object>> {

    /**
     * 日志信息
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 响应
     */
    private HttpServletResponse response;

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public GetExcelCmd(Map<String, Object> params, User user, HttpServletResponse response) {
        this.params = params;
        this.user = user;
        this.response = response;
    }

    /**
     * Command类方法实现
     *
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String sql;
        try {
            GetListCmd getListCmd = new GetListCmd(params, user);
            sql = getListCmd.getTotalSql();
            RecordSet rs = new RecordSet();
            ExcelFile excelFile = new ExcelFile();
            excelFile.init();
            String sheetName = "学习社区积分表";
            rs.executeQuery(sql);
            ExcelSheet es = new ExcelSheet();
            excelFile.addSheet(sheetName, es);
            es.initRowList(rs.getCounts() + 1);
            for (int i = 0; i < 11; i++) {
                //设置列的宽度
                es.addColumnwidth(6000);
            }
            //标题样式
            ExcelStyle excelStyle = excelFile.newExcelStyle("title");
            excelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
            excelStyle.setFontbold(ExcelStyle.Strong_Font); //字体粗细

            //合计
            ExcelStyle summaryStyle = excelFile.newExcelStyle("summary");
            summaryStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色
            summaryStyle.setGroundcolor(ExcelStyle.LIGHT_ORANGE_Color);

            //normalStylle
            ExcelStyle normalexcelStyle = excelFile.newExcelStyle("normal");
            normalexcelStyle.setFontcolor(ExcelStyle.BLACK_Color); //字体颜色

            ExcelRow er = es.newExcelRow(0);
            String columnNames = getListCmd.getColumnFields();
            this.addTitles(columnNames, er);

            String columnCodes = getListCmd.getQueryFields();
            String eachCodeValue;
            String stylestr = "normal";
            int row = 1;
            while (rs.next()) {
                ExcelRow er1 = es.newExcelRow(row);
                for (String code : columnCodes.split(",")) {
                    eachCodeValue = Util.null2String(rs.getString(code));
                    er1.addStringValue(eachCodeValue, stylestr);
                }
                row++;
            }
            excelFile.setFilename(sheetName);
            ExcelOutUtil excelOutUtil = new ExcelOutUtil();
            excelOutUtil.ExcelOut(user, excelFile, response);
            result.put("status", "1");
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("errorInfo", e.getMessage().replace("java.lang.Exception:", ""));
        }
        return result;
    }


    private void addTitles(String colsNames, ExcelRow er) {
        if (!colsNames.isEmpty()) {
            for (String col : colsNames.split(",")) {
                er.addStringValue(col, "title");
            }
        }
    }
}
