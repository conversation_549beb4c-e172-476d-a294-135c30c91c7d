package com.engine.action.cornJob;

import com.weaver.general.BaseBean;
import com.weaver.general.TimeUtil;
import weaver.conn.RecordSet;
import weaver.cowork.CoworkService;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;

public class ProjectPlan extends BaseCronJob {
    @Override
    public void execute() {
        RecordSet rSet=new RecordSet();
        String sql1="SELECT id FROM hrmresource";
        rSet.execute(sql1);
        String names = ",";
        String lastname = "";
        while(rSet.next()){
            List<String> lab = new ArrayList<String>();
            String userid = rSet.getString("id");
            RecordSet recordSet=new RecordSet();
            String sql="select id,name,isUsed,labelType,labelOrder,labelColor,textColor from cowork_label where labelType != 'coworkArea' and userid="+userid;
            int index = 1;
            sql=sql+" order by labelOrder";

            boolean flag=recordSet.execute(sql);
            new BaseBean().writeLog(sql);
            while (recordSet.next()) {
                String labelOrder=recordSet.getString("labelOrder");
                String labelname=recordSet.getString("name");
                lab.add(labelname);
                index = Math.max(index, Util.getIntValue(labelOrder)+1);
            }

            if(index==1){
                //此处为e8逻辑，e9不需要处理
/*	 	sql="select id,name,isUsed,labelType,labelOrder,labelColor,textColor from cowork_label where userid="+userid;
	 	recordSet.execute(sql);
	 	if(!recordSet.next()){ //避免重复创建
	 		sql="insert into cowork_label(userid,name,isUsed,labelOrder,labelType) values("+userid+",'25426',1,1,'unread')";
	 		recordSet.execute(sql);
	 		sql="insert into cowork_label(userid,name,isUsed,labelOrder,labelType) values("+userid+",'15533',1,2,'important')";
	 		recordSet.execute(sql);
	 		sql="insert into cowork_label(userid,name,isUsed,labelOrder,labelType) values("+userid+",'16636',1,3,'hidden')";
	 		recordSet.execute(sql);
	 		sql="insert into cowork_label(userid,name,isUsed,labelOrder,labelType) values("+userid+",'17694',1,4,'coworkArea')";
	 		recordSet.execute(sql);
	 		index = 4;
		 }
		 */
            }

            if(index>0){
                CoworkService coworkService=new CoworkService();

                sql="select * from (select typeid ,count(typeid) as total from ("+
                        " select t1.typeid,"+
                        " case when  t3.sourceid is not null then 1 when t2.cotypeid is not null then 0 end as jointype"+
                        " from cowork_items  t1 left join "+
                        //关注的协作
                        " ("+coworkService.getManagerShareSql(userid)+")  t2 on t1.typeid=t2.cotypeid left join "+
                        //直接参与的协作
                        " ("+coworkService.getPartnerShareSql(userid)+")  t3 on t3.sourceid=t1.id"+
                        " left join (select t7.id,t7.typename,t8.id as mainid,t8.typename as mainname from cowork_types  t7 left join cowork_maintypes  t8 on t7.departmentid=t8.id)  t9 on t1.typeid=t9.id"+
                        //阅读|重要|隐藏
                        " ) t where jointype is not null group by typeid) tt where total <>0";
                recordSet.execute(sql);
                while(recordSet.next()){
                    String typeid=recordSet.getString("typeid");
                    if(lab.contains(typeid)){
                        continue;
                    }
                    String sqls = "select * from cowork_label where name = ? and userid = ?";
                    RecordSet rs = new RecordSet();
                    rs.execute("insert into cowork_label (userid,name,labelColor,createdate,createtime,isUsed,labelOrder,textColor,labelType) values("+userid+",'"+typeid+"','','"+TimeUtil.getCurrentDateString()+"','"+ TimeUtil.getOnlyCurrentTimeString()+"',1,"+index+++""+",'','typePlate')");
//                        rs.executeQuery(sqls, typeid,userid);
//                        if(!rs.next()){
//                            //	index++;
//                    }

                }
            }

        }
    }

}
