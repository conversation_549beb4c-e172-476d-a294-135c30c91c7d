package com.engine.action.qualityPlan;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.workflow.WorkflowComInfo;

/**
 * 质量计划中止action
 */
public class QualityPlanAbortAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(requestInfo.getRequestid());
            int workflowid = Util.getIntValue(requestInfo.getWorkflowid());
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));
            String maintablename = "formtable_main_" + -1 * formid;
            String dt1name = maintablename + "_dt1";
            this.writeLog("requestid = " + requestid);
            this.writeLog("workflowid = " + workflowid);
            this.writeLog("formid = " + formid);
            RecordSet rs = new RecordSet();
            String qryMain = "select * from " + maintablename + "  where requestid =" + requestid;
            rs.execute(qryMain);
            if (rs.next()) {
                String mainId = Util.null2String(rs.getString("id"));
                RecordSet rs1 = new RecordSet();
                String qryDt1 = "select wmsys.wm_concat(zljd) as zljds from " + dt1name + " where mainid=" + mainId;
                rs1.execute(qryDt1);

                while (rs1.next()) {
                    String zljds = Util.null2String(rs1.getString("zljds"));
                    RecordSet rs2 = new RecordSet();
                    String updateDt1 = "UPDATE uf_zljhdg SET rwzt=7 WHERE id IN (" + zljds + ")";
                    rs2.execute(updateDt1);
                }
            }
        } catch (Exception e) {
            this.writeLog(e);
        }

        return Action.SUCCESS;
    }
}
