package com.engine.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @CreateTime : 2021/88-11:34
 * @description : 成本报表service
 */
public interface CostReportService {
    /**
     * 合同金额状态
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getContractAmountStatus(Map<String, Object> params, User user);

    /**
     * 成本总价对比表
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getContractAmountCompare(Map<String, Object> params, User user);

    /**
     * 项目进程分析表看板需求
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getProjectProgress(Map<String, Object> params, User user);

    /**
     * 成本分析对比表
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getCostAnalysisCompare(Map<String, Object> params, User user);

    /**
     * 主责部门分析对比表
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getMainDutyAnalysisCompare(Map<String, Object> params, User user);

    /**
     * 预算使用情况
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getBudgetUse(Map<String, Object> params, User user);

    /**
     * 预算调整分析
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getBudgetAdjust(Map<String, Object> params, User user);

    /**
     * 成本明细分析对比表
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getCostDetailAnalysis(Map<String, Object> params, User user);

    /**
     * 获取设计变更金额对比
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getChangeAmountCompare(Map<String, Object> params, User user);
}
