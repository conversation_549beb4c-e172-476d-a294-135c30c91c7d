package com.engine.service;

import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface StudyforumService {

    /**
     * @param @param  params
     * @param @param  user
     * @param @return 参数
     * @return Map<String, Object> 返回类型
     * @throws
     * @Title: getSearchInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    Map<String, Object> getSearchInfo(Map<String, Object> params, User user);

    /**
     * @param @param  params
     * @param @param  user
     * @param @return 参数
     * @return Map<String, Object> 返回类型
     * @throws
     * @Title: getList
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    Map<String, Object> getList(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getTab(Map<String, Object> params, User user);

    /**
     * 导出Excel
     *
     * @param params
     * @param user
     * @param response
     * @return
     */
    Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response);
}
