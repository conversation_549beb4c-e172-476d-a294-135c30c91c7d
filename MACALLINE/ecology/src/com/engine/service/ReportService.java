package com.engine.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:29
 * @description : TODO
 */
public interface ReportService {
    /**
     * 年度开业项目报表数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getAnnualOpeningProjects(Map<String, Object> params, User user);

    /**
     * 年度招商项目进度
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getInvestmentProjectProgress(Map<String, Object> params, User user);

    /**
     * 本周节点提醒
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getNodeRemindByThisWeek(Map<String, Object> params, User user);

    /**
     * 高风险投资表
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> gethightriskproject(Map<String, Object> params, User user);

    /**
     * 双周会汇总
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getDoubleWeekmeeingGrades(Map<String, Object> params, User user);

    /**
     * 双周会明细
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getDoubleWeekmeetingdetail(Map<String, Object> params, User user);

    /**
     * 节点亮灯排名
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getNodeLightRanking(Map<String, Object> params, User user);

    /**
     * 延期完成事项汇总
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getDelayedCompletion(Map<String, Object> params, User user);


    /**
     * 有计划项目
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> gethavingPlanProject(Map<String, Object> params, User user);


    /**
     * 无计划项目
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getnothavingPlanProject(Map<String, Object> params, User user);

    /**
     * 双周会亮灯
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getBiweeklyMeetingLights(Map<String, Object> params, User user);


    /**
     * 长期逾期项目
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getLongOverdueProject(Map<String, Object> params, User user);

    /**
     * 长期逾期项目汇总
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getAllLongOverdueProject(Map<String, Object> params, User user);

}
