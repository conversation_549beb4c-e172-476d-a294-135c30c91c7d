package com.engine.service;

import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface PersonLogingradesService {
    /**
     * @Title: getGrandslogin
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param @param params
     * @param @param user
     * @param @return 参数
     * @return Map<String,Object> 返回类型
     * @throws
     */
    Map<String, Object> getGrandslogin(Map<String, Object> params, User user);

     Map<String, Object> getGrandsdetail(Map<String, Object> params, User user);

     Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response);
}
