package com.engine.service;

import weaver.hrm.User;

import java.util.Map;

public interface QualityPlanService {
    /**
     * 生成质量计划中止流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> postQualityPlanAbort(Map<String, Object> params, User user);

    /**
     * 刷新序号
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> refreshOrder(Map<String, Object> params, User user);
}
