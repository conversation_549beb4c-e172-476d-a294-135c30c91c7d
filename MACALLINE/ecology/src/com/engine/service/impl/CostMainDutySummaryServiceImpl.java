package com.engine.service.impl;

import com.engine.cmd.costMainDutySummary.GetExcelCmd;
import com.engine.cmd.costMainDutySummary.GetListCmd;
import com.engine.cmd.costMainDutySummary.GetSearchInfoCmd;
import com.engine.cmd.costMainDutySummary.GetTabCmd;
import com.engine.service.CostMainDutySummaryService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class CostMainDutySummaryServiceImpl extends Service implements CostMainDutySummaryService {


    @Override
    public Map<String, Object> getSearchInfo(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetSearchInfoCmd(params, user));
    }

    @Override
    public Map<String, Object> getList(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetListCmd(params, user));
    }

    @Override
    public Map<String, Object> getTab(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetTabCmd(params, user));
    }

    @Override
    public Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response) {
        return commandExecutor.execute(new GetExcelCmd(params, user,response));
    }
}
