package com.engine.service.impl;

import com.engine.cmd.costReport.*;
import com.engine.core.impl.Service;
import com.engine.service.CostReportService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 */
public class CostReportServiceImpl extends Service implements CostReportService {
    @Override
    public Map<String, Object> getContractAmountStatus(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetContractAmountStatusCmd(params, user));
    }

    @Override
    public Map<String, Object> getContractAmountCompare(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetContractAmountCompareCmd(params, user));
    }

    @Override
    public Map<String, Object> getProjectProgress(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetProjectProgressCmd(params, user));
    }

    @Override
    public Map<String, Object> getCostAnalysisCompare(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetCostAnalysisCompareCmd(params, user));
    }

    @Override
    public Map<String, Object> getMainDutyAnalysisCompare(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetMainDutyAnalysisCompareCmd(params, user));
    }

    @Override
    public Map<String, Object> getBudgetUse(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetBudgetUseCmd(params, user));
    }

    @Override
    public Map<String, Object> getBudgetAdjust(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetBudgetAdjustCmd(params, user));
    }

    @Override
    public Map<String, Object> getCostDetailAnalysis(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetCostDetailAnalysisCmd(params, user));
    }

    @Override
    public Map<String, Object> getChangeAmountCompare(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetChangeAmountCompareCmd(params, user));
    }

}
