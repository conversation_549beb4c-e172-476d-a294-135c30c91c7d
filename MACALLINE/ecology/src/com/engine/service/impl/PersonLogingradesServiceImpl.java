package com.engine.service.impl;


import com.engine.cmd.personlogingrades.getDetailLoginData;
import com.engine.cmd.personlogingrades.getExcelPersonLoginData;
import com.engine.cmd.personlogingrades.getPersonLoginData;
import com.engine.core.impl.Service;
import com.engine.service.PersonLogingradesService;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class PersonLogingradesServiceImpl extends Service implements PersonLogingradesService {

    @Override
    public Map<String, Object> getGrandslogin(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getPersonLoginData(params, user));
    }

    @Override
    public Map<String, Object> getGrandsdetail(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getDetailLoginData(params, user));
    }

    @Override
    public Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response) {
        return commandExecutor.execute(new getExcelPersonLoginData(params, user, response));
    }
}
