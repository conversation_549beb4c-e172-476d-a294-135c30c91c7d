package com.engine.service.impl;

import com.engine.cmd.costCenter.RecordLogCmd;
import com.engine.core.impl.Service;
import com.engine.service.CostCenterService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 */
public class CostCenterServiceImpl extends Service implements CostCenterService {
    @Override
    public Map<String, Object> recordLog(Map<String, Object> params, User user) {
        return commandExecutor.execute(new RecordLogCmd(params, user));
    }
}
