package com.engine.service.impl;
import com.engine.cmd.costCompare.*;
import com.engine.core.impl.Service;
import com.engine.service.CostCompareService;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class CostCompareServiceImpl extends Service implements CostCompareService {

    @Override
    public Map<String, Object> getAsyncTree(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetAsyncTreeCmd(params, user));
    }

    @Override
    public Map<String, Object> getSearchInfo(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetSearchInfoCmd(params, user));
    }

    @Override
    public Map<String, Object> getList(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetListCmd3(params, user));
    }

    @Override
    public Map<String, Object> getTab(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetTabCmd(params, user));
    }

    @Override
    public Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response) {
        return commandExecutor.execute(new GetExcelCmd(params,user,response));
    }

    @Override
    public Map<String, Object> GetPenetrate(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetPenetrateCmd(params,user));
    }

}
