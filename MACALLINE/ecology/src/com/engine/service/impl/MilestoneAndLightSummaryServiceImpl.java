package com.engine.service.impl;

import com.engine.cmd.milestoneAndLightSummary.MilestoneAndLightSummaryCmd;
import com.engine.core.impl.Service;
import com.engine.service.MilestoneAndLightSummaryService;
import weaver.hrm.User;

import java.util.Map;

public class MilestoneAndLightSummaryServiceImpl  extends Service implements MilestoneAndLightSummaryService {
    @Override
    public Map<String, Object> getMilestoneAndLightSummaryService(Map<String, Object> params, User user) {
        return commandExecutor.execute(new MilestoneAndLightSummaryCmd(params, user));
    }
}
