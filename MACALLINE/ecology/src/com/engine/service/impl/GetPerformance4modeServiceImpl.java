package com.engine.service.impl;

import com.engine.cmd.report.getPerformance4modeCmd;
import com.engine.core.impl.Service;
import com.engine.service.GetPerformance4modeService;
import weaver.hrm.User;

import java.util.Map;

public class GetPerformance4modeServiceImpl extends Service implements GetPerformance4modeService {
    @Override
    public Map<String, Object> getPerformance4mode(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getPerformance4modeCmd(params, user));
    }
}
