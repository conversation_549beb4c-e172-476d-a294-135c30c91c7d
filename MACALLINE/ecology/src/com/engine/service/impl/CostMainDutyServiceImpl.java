package com.engine.service.impl;

import com.engine.cmd.costCompare.GetPenetrateCmd;
import com.engine.cmd.costMainDuty.*;
import com.engine.service.CostMainDutyService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class CostMainDutyServiceImpl extends Service implements CostMainDutyService {


    @Override
    public Map<String, Object> getSearchInfo(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetSearchInfoCmd(params, user));
    }

    @Override
    public Map<String, Object> getList(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetListCmd(params, user));
    }

    @Override
    public Map<String, Object> getTab(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetTabCmd(params, user));
    }

    @Override
    public Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response) {
        return commandExecutor.execute(new GetExcelCmd(params, user, response));
    }

    @Override
    public Map<String, Object> getCostDetail(Map<String, Object> params, User user) {
        Map<String, Object> map = commandExecutor.execute(new GetCostDetailCmd(params, user));
        return commandExecutor.execute(new GetPenetrateCmd(map, user));
    }
}
