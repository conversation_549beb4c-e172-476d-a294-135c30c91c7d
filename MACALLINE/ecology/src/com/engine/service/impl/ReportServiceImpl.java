package com.engine.service.impl;

import com.engine.cmd.report.*;
import com.engine.core.impl.Service;
import com.engine.service.ReportService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:30
 * @description :
 */
public class ReportServiceImpl extends Service implements ReportService {
    @Override
    public Map<String, Object> getAnnualOpeningProjects(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetAnnualOpeningProjectsCmd(params, user));
    }

    @Override
    public Map<String, Object> getInvestmentProjectProgress(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getInvestmentProjectProgressCmd(params, user));
    }

    @Override
    public Map<String, Object> getNodeRemindByThisWeek(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetNodeRemindByThisWeekCmd(params, user));
    }

    @Override
    public Map<String, Object> gethightriskproject(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getHighRiskProjectCmd(params, user));
    }

    @Override
    public Map<String, Object> getDoubleWeekmeeingGrades(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getDoubleWeekmeeingGradesCmd(params, user));
    }

    @Override
    public Map<String, Object> getDoubleWeekmeetingdetail(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getDoubleWeekmeetingdetailCmd(params, user));
    }

    @Override
    public Map<String, Object> getDelayedCompletion(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetDelayedCompletionCmd(params, user));
    }

    @Override
    public Map<String, Object> getNodeLightRanking(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetNodeLightRankingCmd(params, user));
    }

    @Override
    public Map<String, Object> gethavingPlanProject(Map<String, Object> params, User user) {
        return commandExecutor.execute(new gethavingPlanProjectCmd(params, user));
    }

    @Override
    public Map<String, Object> getnothavingPlanProject(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getnothavingPlanProjectCmd(params, user));
    }

    @Override
    public Map<String, Object> getBiweeklyMeetingLights(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetBiweeklyMeetingLightsCmd(params, user));
    }

    @Override
    public Map<String, Object> getLongOverdueProject(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getLongOverdueProjectCmd(params, user));
    }

    @Override
    public Map<String, Object> getAllLongOverdueProject(Map<String, Object> params, User user) {
        return commandExecutor.execute(new getAllLongOverdueProjectCmd(params, user));
    }

}
