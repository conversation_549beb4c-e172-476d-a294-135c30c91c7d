package com.engine.service.impl;

import com.engine.cmd.updatemanagergrades.UpdateManagerGrades;
import com.engine.core.impl.Service;
import com.engine.service.UpdateManagerGradesService;
import weaver.hrm.User;

import java.util.Map;

public class UpdateManagerGradesServiceImpl extends Service implements UpdateManagerGradesService {
    @Override
    public Map<String, Object> UpdateManagerGrades(Map<String, Object> params, User user) {
        return commandExecutor.execute(new UpdateManagerGrades(params, user));
    }
}
