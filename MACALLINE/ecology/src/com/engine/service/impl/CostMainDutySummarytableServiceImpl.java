package com.engine.service.impl;

import com.engine.cmd.costMainDutySummarytable.costMainDutySummarytable;
import com.engine.core.impl.Service;
import com.engine.service.CostMainDutySummarytableService;
import weaver.hrm.User;

import java.util.Map;

public class CostMainDutySummarytableServiceImpl extends Service implements CostMainDutySummarytableService {
    @Override
    public Map<String, Object> getCostMainDutySummarytable(Map<String, Object> params, User user) {
        return commandExecutor.execute(new costMainDutySummarytable(params, user));
    }
}
