package com.engine.service.impl;

import com.engine.cmd.qualityPlan.*;
import com.engine.core.impl.Service;
import com.engine.service.QualityPlanService;
import weaver.hrm.User;

import java.util.Map;

public class QualityPlanServiceImpl extends Service implements QualityPlanService {
    @Override
    public Map<String, Object> postQualityPlanAbort(Map<String, Object> params, User user) {
        return commandExecutor.execute(new PostQualityPlanAbortCmd(params, user));
    }

    @Override
    public Map<String, Object> refreshOrder(Map<String, Object> params, User user) {
        return commandExecutor.execute(new RefreshOrderCmd(params, user));
    }
}
