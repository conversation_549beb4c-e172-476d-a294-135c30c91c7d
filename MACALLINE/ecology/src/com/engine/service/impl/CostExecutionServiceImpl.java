package com.engine.service.impl;

import com.engine.cmd.costReport.GetContractcircumstanceCmd;
import com.engine.core.impl.Service;
import com.engine.service.CostExecutionService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 */
public class CostExecutionServiceImpl extends Service implements CostExecutionService {
    
    @Override
    public Map<String, Object> getCostExecutionService(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetContractcircumstanceCmd(params, user));
    }
}
