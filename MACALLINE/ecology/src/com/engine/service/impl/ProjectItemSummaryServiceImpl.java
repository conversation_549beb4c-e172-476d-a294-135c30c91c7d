package com.engine.service.impl;


import com.engine.cmd.proejctItemSummary.GetExcelCmd;
import com.engine.cmd.proejctItemSummary.GetListCmd;
import com.engine.cmd.proejctItemSummary.GetSearchInfoCmd;
import com.engine.cmd.proejctItemSummary.GetTabCmd;
import com.engine.service.ProjectItemSummaryService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class ProjectItemSummaryServiceImpl extends Service implements ProjectItemSummaryService {


    @Override
    public Map<String, Object> getSearchInfo(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetSearchInfoCmd(params, user));
    }

    @Override
    public Map<String, Object> getList(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetListCmd(params, user));
    }

    @Override
    public Map<String, Object> getTab(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetTabCmd(params, user));
    }

    @Override
    public Map<String, Object> getExcel(Map<String, Object> params, User user, HttpServletResponse response) {
        return commandExecutor.execute(new GetExcelCmd(params, user,response));
    }
}
