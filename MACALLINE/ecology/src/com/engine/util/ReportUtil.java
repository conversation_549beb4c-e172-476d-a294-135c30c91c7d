package com.engine.util;

import weaver.conn.RecordSetData;

import java.util.*;

/**
 * <AUTHOR> <PERSON>
 * @CreateTime : 2021/6/2-9:38
 * @description : 报表查询工具类
 */
public class ReportUtil {

    public static List<Map<String, Object>> getJSONList(Vector<Object> v, RecordSetData rd) {
        List<Map<String, Object>> result = new ArrayList<>();
        String[] columnNames = rd.getColumnName();
        Map<String, Object> map;
        int j;
        for (Object o : v) {
            Object[] objArray = (Object[]) o;
            map = new HashMap<>();
            for (j = 0; j < objArray.length; ++j) {
                map.put(columnNames[j], objArray[j]);
            }
            result.add(map);
        }
        return result;
    }
    
}
