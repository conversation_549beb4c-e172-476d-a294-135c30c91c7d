package com.engine.util;

import weaver.conn.RecordSet;
import weaver.hrm.User;

/**
 * 成本报表工具类
 */
public class CostReportUtil {

    /**
     * 检查人员是否符合部门类型
     *
     * @param user
     * @return
     */
    public static boolean checkHrmDept(User user) {
        RecordSet rs = new RecordSet();
        String sql = " select ID from HRMRESOURCE H where 1=1 " +
                " and H.ID = ? " +
                " and H.DEPARTMENTID in (21,22,23,24) ";
        return rs.executeQuery(sql, user.getUID());
    }

}
