/*
 *
 * Copyright (c) 2001-2018 泛微软件.
 * 泛微协同商务系统,版权所有.
 * 
 */
package com.api.browser.service.impl;

import com.api.browser.bean.ListHeadBean;
import com.api.browser.service.BrowserService;
import com.api.browser.util.BoolAttr;
import com.api.browser.util.BrowserBaseUtil;
import com.api.browser.util.BrowserConstant;
import com.api.browser.util.BrowserDataType;
import com.engine.fnaMulDimensions.entity.FnaBrowserTreeNodeBean;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Mar 11, 2019
 * 科目
 */
public class FnaqckmnewBrowserService extends BrowserService{

	@Override
	public Map<String, Object> getBrowserData(Map<String, Object> params) throws Exception {
		Map<String, Object> apidatas = new HashMap<String, Object>();
        if(user == null){
            apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, null);
            return apidatas;
        }
        apidatas.put(BrowserConstant.BROWSER_RESULT_TYPE, BrowserDataType.TREE_DATA.getTypeid());
        List<FnaBrowserTreeNodeBean> nodeDatas = this.getTreeNodeInfo(params);
        apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, nodeDatas);   
        return apidatas;
	}

	
	/**
     * 获取穿梭浏览框选中列表数据
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getMultBrowserDestData(Map<String, Object> params) throws Exception {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        
        String selectids  = Util.null2String(params.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
            List<Map<String,Object>> datas  = new ArrayList<Map<String,Object>>();

            StringBuffer buffer = new StringBuffer();
            buffer.append(" select id,mc,cj from uf_km ");
            buffer.append(" where  fl = 0  ");
            buffer.append(" and id in (").append(selectids).append(")");

            rs.executeQuery(buffer.toString());
            StringBuffer ids = new StringBuffer();
            while(rs.next()){
                String id = Util.null2String(rs.getString("id"));
                String mc =  Util.null2String(rs.getString("mc"));
                Map<String,Object> item = new HashMap<String,Object>();
                ids.append(id).append(",");
                item.put("id",id);
                item.put("mc", mc);
                datas.add(item);
            }

            List<ListHeadBean> tableHeadColumns =  new ArrayList<ListHeadBean>();
            tableHeadColumns.add(new ListHeadBean("id",BoolAttr.TRUE).setIsPrimarykey(BoolAttr.TRUE));
            tableHeadColumns.add(new ListHeadBean("mc","",1,BoolAttr.TRUE));

            apidatas.put(BrowserConstant.BROWSER_RESULT_COLUMN, tableHeadColumns);
            apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(datas,ids.toString(),"id"));
            apidatas.put(BrowserConstant.BROWSER_RESULT_TYPE, BrowserDataType.LIST_ALL_DATA.getTypeid());


        return apidatas;

    }
	/**
	 * 获取树形结构
	 * @param params
	 * @return
	 * @throws Exception
	 */
	private List<FnaBrowserTreeNodeBean> getTreeNodeInfo(Map<String, Object> params) throws Exception{
		List<FnaBrowserTreeNodeBean> nodes  = new ArrayList<FnaBrowserTreeNodeBean>();
    	String typeId = Util.null2String(params.get("typeId"));
		
    	int subjectLevel1 = 0;
    	if("5".equals(typeId)){
    		subjectLevel1 = 1;
    	}else if("2".equals(typeId)){
    		subjectLevel1 = 2;
    	}
		RecordSet rs = new RecordSet();
		
		RecordSet rs2 = new RecordSet();
		String sql = " select id,mc,cj from uf_km  where   fl = 0 ";
		String supIdFull = Util.null2String(params.get("id"));
		if(!"".equals(supIdFull)){
			sql += "  and sj = "+supIdFull+" ";
		}else{
			sql += " and cj = 0 ";
		}
		sql+=" order by bm ";
		rs.executeQuery(sql);
		while(rs.next()){
		    String id = Util.null2String(rs.getString("id"));
		    
			String mc = Util.null2String(rs.getString("mc"));
			int cj = Util.getIntValue(rs.getString("cj"));
			boolean isParent = false;
			if(subjectLevel1 != cj){
				isParent = true;
			}
			
			String title = mc;
			
			new BaseBean().writeLog("title:"+title);
			FnaBrowserTreeNodeBean node = new FnaBrowserTreeNodeBean();
            node.setId(id);
            node.setName(mc);
            node.setPid(supIdFull);
            node.setParent(isParent);
            node.setCanClick(!isParent);
            node.setOrgWholePathspan(title);
            nodes.add(node);

		}
		return nodes;
	}
	
	

	
}
