/**
 * Copyright (c) 2001-2018 泛微软件.
 * 泛微协同商务系统,版权所有.
 */
package com.api.browser.service.impl;

import com.api.browser.bean.ListHeadBean;
import com.api.browser.bean.SearchConditionItem;
import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.service.BrowserService;
import com.api.browser.util.*;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  多维度预算多预算科目浏览按钮
 * <AUTHOR>
 * @Date 2018/11/7 16:18
 *
 *
 **/
public class FnaqccontractXmBrowserService extends BrowserService {

	/**
     * 高级搜索
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getBrowserConditionInfo(Map<String, Object> params) throws Exception {

        Map<String, Object> apidatas = new HashMap<String, Object>();

        if(user == null){
            apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, null);
            return apidatas;
        }

        List<SearchConditionItem> conditions = new ArrayList<SearchConditionItem>();
        ConditionFactory conditionFactory = new ConditionFactory(user);
        SearchConditionItem ser = conditionFactory.createCondition(ConditionType.INPUT, -1, "xm");
        ser.setLabel("项目");
        conditions.add(ser.setIsQuickSearch(true));
        apidatas.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, conditions);

        return apidatas;
    }



    /**
     * 获取浏览按钮数据
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getBrowserData(Map<String, Object> params)
            throws Exception {

        Map<String, Object> apidatas = new HashMap<String, Object>();

        if(user == null){
            apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, null);
            return apidatas;
        }
        RecordSet rs = new RecordSet();

        apidatas.put(BrowserConstant.BROWSER_RESULT_TYPE, BrowserDataType.LIST_SPLIT_DATA.getTypeid());
        SplitTableBean tableBean = getTableList(params);
        apidatas.putAll(SplitTableUtil.makeListDataResult(tableBean));
            

        return apidatas;
    }


    /**
     * 获取穿梭浏览框选中列表数据
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getMultBrowserDestData(Map<String, Object> params) throws Exception {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        
        
        String selectids  = Util.null2String(params.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
        List<Map<String,Object>> datas  = new ArrayList<Map<String,Object>>();

        StringBuffer ids = new StringBuffer();
        rs.executeQuery("select a.id,a.xmmc  from uf_xmxx a "
        		+ " where a.id in("+selectids+")");
        while(rs.next()){
        	String xmmc = Util.null2String(rs.getString("xmmc"));
        	String id = Util.null2String(rs.getString("id"));
        	Map<String,Object> item = new HashMap<String,Object>();
            ids.append(id).append(",");
            item.put("id",id);
            item.put("xmmc", xmmc);
            datas.add(item);
        }

        List<ListHeadBean> tableHeadColumns =  new ArrayList<ListHeadBean>();
        tableHeadColumns.add(new ListHeadBean("id",BoolAttr.TRUE).setIsPrimarykey(BoolAttr.TRUE));
        tableHeadColumns.add(new ListHeadBean("xmmc","",1,BoolAttr.TRUE));

        apidatas.put(BrowserConstant.BROWSER_RESULT_COLUMN, tableHeadColumns);
        apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(datas,ids.toString(),"id"));
        apidatas.put(BrowserConstant.BROWSER_RESULT_TYPE, BrowserDataType.LIST_ALL_DATA.getTypeid());    


        return apidatas;

    }

    /**
     * 项目列表数据
     * @param params
     * @return
     * @throws Exception
     */
    private SplitTableBean getTableList(Map<String, Object> params) throws Exception{
    	String qryName = Util.null2String(params.get("xm"));
        String pageSize = "20";
        
        String backFields = " a.id,a.xmmc ";
        String fromSql  = " uf_xmxx a   ";
        String sqlWhere = " where 1=1  ";
        if(!"".equals(qryName)){
        	sqlWhere += " and a.xmmc like '%"+qryName+"%'";
        }

        String orderBy ="a.id";

        List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
        cols.add(new SplitTableColBean("true", "id"));
        cols.add(new SplitTableColBean("50%", "项目", "xmmc", "xmmc",1).setIsInputCol(BoolAttr.TRUE));//名称

        SplitTableBean tableBean = new SplitTableBean("fnaqccontractxmBrowserList","none",pageSize,
                "fnaqccontractxmBrowserList",backFields,fromSql,sqlWhere,orderBy,"","ASC",cols);
        tableBean.setSqlisdistinct("true");

        return tableBean;

    }
}
