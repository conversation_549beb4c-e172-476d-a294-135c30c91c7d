package com.api.browser.service.impl;

import com.api.browser.bean.ListHeadBean;
import com.api.browser.bean.SearchConditionItem;
import com.api.browser.bean.SplitTableBean;
import com.api.browser.bean.SplitTableColBean;
import com.api.browser.service.BrowserService;
import com.api.browser.util.*;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/5/26-16:38
 * @description : TODO
 */
public class CostMainDutyBrowserService  extends BrowserService {
    /**
     * 高级搜索
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getBrowserConditionInfo(Map<String, Object> params) throws Exception {

        Map<String, Object> apidatas = new HashMap<String, Object>();

        if(user == null){
            apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, null);
            return apidatas;
        }

        List<SearchConditionItem> conditions = new ArrayList<SearchConditionItem>();
        ConditionFactory conditionFactory = new ConditionFactory(user);
        SearchConditionItem ser = conditionFactory.createCondition(ConditionType.INPUT, -1, "tx");
        ser.setLabel("主责条线");
        conditions.add(ser.setIsQuickSearch(true));
        apidatas.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, conditions);

        return apidatas;
    }



    /**
     * 获取浏览按钮数据
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getBrowserData(Map<String, Object> params)
            throws Exception {

        Map<String, Object> apidatas = new HashMap<>();
        if(user == null){
            apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, null);
            return apidatas;
        }
        RecordSet rs = new RecordSet();

        apidatas.put(BrowserConstant.BROWSER_RESULT_TYPE, BrowserDataType.LIST_SPLIT_DATA.getTypeid());
        SplitTableBean tableBean = getTableList(params);
        apidatas.putAll(SplitTableUtil.makeListDataResult(tableBean));

        return apidatas;
    }


    /**
     * 获取穿梭浏览框选中列表数据
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getMultBrowserDestData(Map<String, Object> params) throws Exception {
        Map<String, Object> apidatas = new HashMap<>();
        RecordSet rs = new RecordSet();
        String tx = "",id= "";
        Map<String,Object> item;
        String selectids  = Util.null2String(params.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
        List<Map<String,Object>> datas  = new ArrayList<>();

        StringBuilder sb = new StringBuilder();

        sb.append(" SELECT ");
        sb.append(" a.id,   ");
        sb.append(" a.mc   ");
        sb.append(" FROM   ");
        sb.append(" uf_zzbmcb a  ");
        sb.append(" WHERE ");
        sb.append(" a.zt = 0  ");
        sb.append(" and a.id in ( ");
        sb.append(selectids);
        sb.append(" ) ");
        sb.append(" GROUP BY ");
        sb.append(" a.id,a.mc ");
        sb.append(" ORDER BY ");
        sb.append(" a.id ");
        rs.executeQuery(sb.toString());
        sb = new StringBuilder();
        while(rs.next()){
            tx = Util.null2String(rs.getString("mc"));
            id = Util.null2String(rs.getString("id"));
            item = new HashMap<>();
            sb.append(id).append(",");
            item.put("id",id);
            item.put("mc", tx);
            datas.add(item);
        }

        List<ListHeadBean> tableHeadColumns =  new ArrayList<>();
        tableHeadColumns.add(new ListHeadBean("id",BoolAttr.TRUE).setIsPrimarykey(BoolAttr.TRUE));
        tableHeadColumns.add(new ListHeadBean("mc","",1,BoolAttr.TRUE));

        apidatas.put(BrowserConstant.BROWSER_RESULT_COLUMN, tableHeadColumns);
        apidatas.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(datas,sb.toString(),"id"));
        apidatas.put(BrowserConstant.BROWSER_RESULT_TYPE, BrowserDataType.LIST_ALL_DATA.getTypeid());
        return apidatas;

    }

    /**
     * 项目列表数据
     * @param params
     * @return
     * @throws Exception
     */
    private SplitTableBean getTableList(Map<String, Object> params) throws Exception{
        String qryName = Util.null2String(params.get("tx"));
        StringBuilder sb = new StringBuilder();
        String pageSize = "20";

        sb.append(" SELECT ");
        sb.append(" a.id,   ");
        sb.append(" a.mc   ");
        sb.append(" FROM   ");
        sb.append(" uf_zzbmcb a  ");
        sb.append(" WHERE ");
        sb.append(" a.zt = 0  ");
        if(!"".equals(qryName)){
            sb.append(" and a.mc like '%");
            sb.append(qryName);
            sb.append("%'");
        }
        sb.append(" GROUP BY ");
        sb.append(" a.id,a.mc  ");
        sb.append(" ORDER BY ");
        sb.append(" a.id ");

        String backFields = " id,mc ";
        String fromSql = "( " + sb + " )";
        String whereSql = " where 1=1 ";

        List<SplitTableColBean> cols = new ArrayList<SplitTableColBean>();
        cols.add(new SplitTableColBean("true", "id"));
        //名称
        cols.add(new SplitTableColBean("50%", "主责条线", "mc", "mc",1).setIsInputCol(BoolAttr.TRUE));

        SplitTableBean tableBean = new SplitTableBean("costMainDutyBrowserList","none",pageSize,
                "costMainDutyBrowserList",backFields,fromSql,whereSql,"","","ASC",cols);
        tableBean.setSqlisdistinct("true");

        return tableBean;

    }
}
