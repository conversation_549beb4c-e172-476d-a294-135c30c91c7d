package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.CostMainDutySummaryService;
import com.engine.service.impl.CostMainDutySummaryServiceImpl;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.systeminfo.SystemEnv;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * 成本主责条线汇总表api
 */
@Path("/cost/mainDutySummary/v1")
public class CostMainDutySummaryApi extends BaseBean {


    /**日志*/
    private BaseBean logger = new BaseBean();

    private CostMainDutySummaryService getService(User user){
        return (CostMainDutySummaryService) ServiceUtil.getService(CostMainDutySummaryServiceImpl.class, user);
    }


    /**
     * 获取高级搜索
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getSearchInfo")
    @Produces(MediaType.TEXT_PLAIN)
    public String getSearchInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {

        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            String userId = Util.null2String(user.getUID());
            RecordSet rs = new RecordSet();
            String xmxxs = "";
            rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
            while(rs.next()){
                String xmxx = Util.null2String(rs.getString("id"));
                if(!"".equals(xmxxs)){
                    xmxxs+=",";
                }
                xmxxs+="'"+xmxx+"'";
            }
            logger.writeLog("xmxxs:"+xmxxs);
            boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getSearchInfo(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }

        return JSONObject.toJSONString(result);
    }

    /**
     * 获取列表数据
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getList(@Context HttpServletRequest request, @Context HttpServletResponse response) {

        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            String userId = Util.null2String(user.getUID());
            RecordSet rs = new RecordSet();
            String xmxxs = "";
            rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
            while(rs.next()){
                String xmxx = Util.null2String(rs.getString("id"));
                if(!"".equals(xmxxs)){
                    xmxxs+=",";
                }
                xmxxs+="'"+xmxx+"'";
            }
            logger.writeLog("xmxxs:"+xmxxs);
            boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                params.put("xmxxs",xmxxs);
                result = getService(user).getList(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 获取tab
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getTabs")
    @Produces(MediaType.TEXT_PLAIN)
    public String getTab(@Context HttpServletRequest request, @Context HttpServletResponse response) {

        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            String userId = Util.null2String(user.getUID());
            RecordSet rs = new RecordSet();
            String xmxxs = "";
            rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?",userId,userId);
            while(rs.next()){
                String xmxx = Util.null2String(rs.getString("id"));
                if(!"".equals(xmxxs)){
                    xmxxs+=",";
                }
                xmxxs+="'"+xmxx+"'";
            }
            logger.writeLog("xmxxs:"+xmxxs);
            boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit",user) || canView || canEdit||(!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getTab(params, user);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }

        return JSONObject.toJSONString(result);
    }

    /**
     * 导出Excel
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getExcel")
    @Produces(MediaType.TEXT_PLAIN)
    public String getExcel(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            String userId = Util.null2String(user.getUID());
            RecordSet rs = new RecordSet();
            String xmxxs = "";
            rs.executeQuery("select * from uf_xmxx where tjz = ? or xmz = ?", userId, userId);
            while (rs.next()) {
                String xmxx = Util.null2String(rs.getString("id"));
                if (!"".equals(xmxxs)) {
                    xmxxs += ",";
                }
                xmxxs += "'" + xmxx + "'";
            }
            logger.writeLog("xmxxs:" + xmxxs);
            boolean canView = HrmUserVarify.checkUserRight("BudgetAuthorityRule:readOnly", user);//预算编制只读权限
            boolean canEdit = (HrmUserVarify.checkUserRight("FnaBudgetEdit:Edit", user) ||
                    HrmUserVarify.checkUserRight("BudgetAuthorityRule:edit", user));//财务预算维护、预算编制权限
            if (HrmUserVarify.checkUserRight("FnaMultiTemplateDataFill:Edit", user) || canView || canEdit || (!"".equals(xmxxs))) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getExcel(params, user, response);
            } else {
                result.put("status", "4");
                result.put("message", SystemEnv.getHtmlLabelName(2012, user.getLanguage()));//对不起，您暂时没有权限！
            }
        }
        return JSONObject.toJSONString(result);
    }

}
