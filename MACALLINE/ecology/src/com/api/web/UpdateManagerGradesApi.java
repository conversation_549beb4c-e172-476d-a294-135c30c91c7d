package com.api.web;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.UpdateManagerGradesService;
import com.engine.service.impl.UpdateManagerGradesServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

@Path("/updatemanager/v1")
public class UpdateManagerGradesApi extends BaseBean {

    private UpdateManagerGradesService getService(User user) {
        return ServiceUtil.getService(UpdateManagerGradesServiceImpl.class, user);
    }
    /**
     * 获取高级搜索
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/get")
    @Produces(MediaType.TEXT_PLAIN)
    public String getSearchInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).UpdateManagerGrades(params, user);
        }
        return JSONObject.toJSONString(result);
    }
}
