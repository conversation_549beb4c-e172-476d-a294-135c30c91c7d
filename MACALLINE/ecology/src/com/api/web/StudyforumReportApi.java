package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.StudyforumService;
import com.engine.service.impl.StudyforumServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 学习社区积分api
 */
@Path("/report/studyForum/v1")
public class StudyforumReportApi extends BaseBean {


    private StudyforumService getService(User user) {
        return ServiceUtil.getService(StudyforumServiceImpl.class, user);
    }

    /**
     * 获取高级搜索
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getSearchInfo")
    @Produces(MediaType.TEXT_PLAIN)
    public String getSearchInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getSearchInfo(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 获取列表数据
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getList")
    @Produces(MediaType.TEXT_PLAIN)
    public String getList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getList(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 导出Excel
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getExcel")
    @Produces(MediaType.TEXT_PLAIN)
    public String getExcel(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getExcel(params, user, response);
        }

        return JSONObject.toJSONString(result);
    }

}
