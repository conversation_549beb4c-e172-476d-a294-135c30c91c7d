package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.PersonLogingradesService;
import com.engine.service.impl.PersonLogingradesServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

@Path("/cost/getlogingrades/v1")
public class getLoginGradesApi extends BaseBean {

    /**日志*/
    private BaseBean logger = new BaseBean();

    private PersonLogingradesService getService(User user){
        return (PersonLogingradesService) ServiceUtil.getService(PersonLogingradesServiceImpl.class, user);
    }
    /**
     * 登录评分总报表
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getgradeslogin")
    @Produces(MediaType.TEXT_PLAIN)
    public String getGrandeslogin(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getGrandslogin(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    @GET
    @Path("/getgradesdetail")
    @Produces(MediaType.TEXT_PLAIN)
    public String getGrandesdetail(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getGrandsdetail(params, user);
        }
        return JSONObject.toJSONString(result);
    }


    @GET
    @Path("/getExcel")
    @Produces(MediaType.TEXT_PLAIN)
    public String getExcel(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getExcel(params, user, response);
        }
        return JSONObject.toJSONString(result);
    }



}
