package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.CostCenterService;
import com.engine.service.impl.CostCenterServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 成本中心接口
 */
@Path("/costcenter")
public class CostCenterApi {
    private CostCenterService getService(User user) {
        return ServiceUtil.getService(CostCenterServiceImpl.class, user);
    }

    /**
     * 记录日志
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/recordLog")
    @Produces(MediaType.TEXT_PLAIN)
    public String recordLog(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).recordLog(params, user);
        }
        return JSONObject.toJSONString(result);
    }

}
