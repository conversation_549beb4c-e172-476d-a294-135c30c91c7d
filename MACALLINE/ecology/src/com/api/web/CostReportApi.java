package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.CostReportService;
import com.engine.service.impl.CostReportServiceImpl;
import com.weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 成本报表接口
 */
@Path("/report/cost")
public class CostReportApi extends BaseBean {
    private CostReportService getService(User user) {
        return ServiceUtil.getService(CostReportServiceImpl.class, user);
    }

    /**
     * 合同金额状态
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getContractAmountStatus")
    @Produces(MediaType.TEXT_PLAIN)
    public String getContractAmountStatus(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getContractAmountStatus(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 成本总价对比表
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getContractAmountCompare")
    @Produces(MediaType.TEXT_PLAIN)
    public String getContractAmountCompare(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getContractAmountCompare(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 项目进程分析表看板需求
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getProjectProgress")
    @Produces(MediaType.TEXT_PLAIN)
    public String getProjectProgress(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getProjectProgress(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 成本分析对比表
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getCostAnalysisCompare")
    @Produces(MediaType.TEXT_PLAIN)
    public String getCostAnalysis(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getCostAnalysisCompare(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 主责部门分析对比表
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getMainDutyAnalysisCompare")
    @Produces(MediaType.TEXT_PLAIN)
    public String getMainDutyAnalysisCompare(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getMainDutyAnalysisCompare(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 预算使用情况
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getBudgetUse")
    @Produces(MediaType.TEXT_PLAIN)
    public String getBudgetUse(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getBudgetUse(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 预算调整分析
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getBudgetAdjust")
    @Produces(MediaType.TEXT_PLAIN)
    public String getBudgetAdjust(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getBudgetAdjust(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 成本明细分析对比表
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getCostDetailAnalysis")
    @Produces(MediaType.TEXT_PLAIN)
    public String getCostDetailAnalysis(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getCostDetailAnalysis(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 获取设计变更金额对比
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getChangeAmountCompare")
    @Produces(MediaType.TEXT_PLAIN)
    public String getChangeAmountCompare(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getChangeAmountCompare(params, user);
        }
        return JSONObject.toJSONString(result);
    }
}
