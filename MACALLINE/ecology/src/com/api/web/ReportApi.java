package com.api.web;


import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.ReportService;
import com.engine.service.impl.ReportServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:28
 * @description : 报表数据
 */

@Path("/report/v1")
public class ReportApi extends BaseBean {


    private ReportService getService(User user) {
        return ServiceUtil.getService(ReportServiceImpl.class, user);
    }

    /**
     * 年度开业项目报表
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/annualOpeningProjects")
    @Produces(MediaType.TEXT_PLAIN)
    public String getAnnualOpeningProjects(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getAnnualOpeningProjects(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 年度招商项目进度
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/investmentProjectProgress")
    @Produces(MediaType.TEXT_PLAIN)
    public String getInvestmentProjectProgress(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getInvestmentProjectProgress(params, user);
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 本周节点提醒
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/nodeRemindByThisWeek")
    @Produces(MediaType.TEXT_PLAIN)
    public String getNodeRemindByThisWeek(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);

            result = getService(user).getNodeRemindByThisWeek(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 高风险项目表
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/hightriskproject")
    @Produces(MediaType.TEXT_PLAIN)
    public String gethightriskproject(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).gethightriskproject(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 双周会汇总
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getDoubleWeekmeeingGrades")
    @Produces(MediaType.TEXT_PLAIN)
    public String getDoubleWeekmeeingGrades(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getDoubleWeekmeeingGrades(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 双周会明细
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getDoubleWeekmeetingdetail")
    @Produces(MediaType.TEXT_PLAIN)
    public String getDoubleWeekmeetingdetail(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getDoubleWeekmeetingdetail(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 延期完成事项汇总
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/delayedCompletion")
    @Produces(MediaType.TEXT_PLAIN)
    public String getDelayedCompletion(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getDelayedCompletion(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 节点亮灯排名
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/nodeLightRanking")
    @Produces(MediaType.TEXT_PLAIN)
    public String getNodeLightRanking(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getNodeLightRanking(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 有计划项目
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/havingPlanProject")
    @Produces(MediaType.TEXT_PLAIN)
    public String getHavingPlanProject(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).gethavingPlanProject(params, user);
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 无项目计划
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/nohavingPlanProject")
    @Produces(MediaType.TEXT_PLAIN)
    public String getnoHavingPlanProject(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getnothavingPlanProject(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 双周会亮灯
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/biweeklyMeetingLights")
    @Produces(MediaType.TEXT_PLAIN)
    public String getBiweeklyMeetingLights(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getBiweeklyMeetingLights(params, user);
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 长期逾期项目
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getLongOverdueProject")
    @Produces(MediaType.TEXT_PLAIN)
    public String getLongOverdueProject(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getLongOverdueProject(params, user);
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 长期逾期项目总和
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getAllLongOverdueProject")
    @Produces(MediaType.TEXT_PLAIN)
    public String getAllLongOverdueProject(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getAllLongOverdueProject(params, user);
        }
        return JSONObject.toJSONString(result);
    }
}
