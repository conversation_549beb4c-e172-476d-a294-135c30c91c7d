package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.CostExecutionService;
import com.engine.service.impl.CostExecutionServiceImpl;
import com.weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 成本报表接口
 */
@Path("/report/costexecution/v1")
public class CostExecutionApi extends BaseBean {
    private CostExecutionService getService(User user) {
        return ServiceUtil.getService(CostExecutionServiceImpl.class, user);
    }

    /**
     * 执行情况
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getCostExecution")
    @Produces(MediaType.TEXT_PLAIN)
    public String getCostExecution(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getCostExecutionService(params, user);
        }
        return JSONObject.toJSONString(result);
    }
}
