package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.GetPerformance4modeService;
import com.engine.service.impl.GetPerformance4modeServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

@Path("/report/getperformance4mode/v1")
public class getPerformance4modeApi extends BaseBean {

    private GetPerformance4modeService getService(User user) {
        return ServiceUtil.getService(GetPerformance4modeServiceImpl.class, user);
    }

    @GET
    @Path("/getperformance")
    @Produces(MediaType.TEXT_PLAIN)
    public String getperformance(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        if(user != null){
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).getPerformance4mode(params, user);
        }
        return JSONObject.toJSONString(result);
    }
}
