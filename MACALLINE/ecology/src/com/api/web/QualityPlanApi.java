package com.api.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.service.QualityPlanService;
import com.engine.service.impl.QualityPlanServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 质量计划api
 */
@Path("/qualityPlan")
public class QualityPlanApi {

    private QualityPlanService getService(User user) {
        return ServiceUtil.getService(QualityPlanServiceImpl.class, user);
    }

    /**
     * 年度开业项目报表
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/postQualityPlanAbort")
    @Produces(MediaType.TEXT_PLAIN)
    public String postQualityPlanAbort(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).postQualityPlanAbort(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 刷新序号
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refreshOrder")
    @Produces(MediaType.TEXT_PLAIN)
    public String refreshOrder(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).refreshOrder(params, user);
        }
        return JSONObject.toJSONString(result);
    }
}
