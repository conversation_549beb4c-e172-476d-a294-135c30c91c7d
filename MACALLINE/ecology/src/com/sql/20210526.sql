--已在正式环境执行，请勿重复执行！！！

--插入自定义浏览按钮，主责条线的高级搜索
INSERT INTO WF_BROWSER_CONFIG ( TYPE, CLAZZ, DESCRIPTION, ICONBGCOLOR )
VALUES
( 'costMainDutyBrowser', 'com.api.browser.service.impl.CostMainDutyBrowserService', '主责条线', '#B32E37' );

--新增视图，项目，科目维度的合计价格信息
CREATE OR REPLACE FORCE VIEW "ECOLOGY"."V_XMKM_PRICE" ("XM", "KM", "DT_PRICE", "SJ_PRICE", "SPZC_PRICE", "CWZC_PRICE") AS
SELECT
    TBA.XM,
    TBA.KM,
    SUM( TBA.DT_PRICE ) AS DT_PRICE,
    SUM( TBA.SJ_PRICE ) AS SJ_PRICE,
    SUM( TBA.SPZC_PRICE ) AS SPZC_PRICE,
    SUM( TBA.CWZC_PRICE ) AS CWZC_PRICE
FROM
    (
        SELECT
            m.XM,
            dt1.KM,
            SUM( dt1.htkzje ) AS DT_PRICE,
            SUM( dt1.ygjsje ) AS SJ_PRICE,
            SUM( dt1.yfje ) AS SPZC_PRICE,
            SUM( dt1.cwsjzfje ) AS CWZC_PRICE
        FROM
            uf_htxx_dt1 dt1
                LEFT JOIN uf_htxx m ON ( m.id = dt1.MAINID )
        GROUP BY
            m.XM,
            dt1.KM UNION ALL
        SELECT
            m.XM,
            dt1.KM,
            SUM( dt1.sqfyje ) AS DT_PRICE,
            SUM( dt1.sqfyje ) AS SJ_PRICE,
            SUM( dt1.sqfyje ) AS SPZC_PRICE,
            SUM( dt1.cwsjzfje ) AS CWZC_PRICE
        FROM
            uf_fysqtz_dt1 dt1
                LEFT JOIN uf_fysqtz m ON ( m.id = dt1.MAINID )
        GROUP BY
            m.XM,
            dt1.KM
    ) TBA
GROUP BY
    TBA.XM,
    TBA.KM;

----新增视图，项目，科目维度的调整次数，金额信息
CREATE OR REPLACE FORCE VIEW "ECOLOGY"."V_XMKM_AJMCOUNT" ("XM", "KM", "CNT", "TZJE") AS
SELECT
    TB1.XM,
    TB1.KM,
    SUM( TB1.CNT ) AS CNT,
    SUM(TB1.TZJE) AS TZJE
FROM
    (
        SELECT
            a.XM,
            a.KM,
            count( a.REQUESTID ) AS cnt,
            sum(zbje) as TZJE
        FROM
            FORMTABLE_MAIN_72 a
                LEFT JOIN workflow_requestBase b ON a.REQUESTID = b.requestid
        WHERE
                b.CURRENTNODETYPE = 3
        GROUP BY
            a.xm,
            a.km UNION ALL
        SELECT
            m.XM,
            dt1.km,
            count( m.REQUESTID ) AS cnt,
            sum(dt1.dzje) AS TZJE
        FROM
            FORMTABLE_MAIN_73_dt1 dt1
                LEFT JOIN FORMTABLE_MAIN_73 m ON m.id = dt1.mainid
                LEFT JOIN workflow_requestBase b ON m.REQUESTID = b.requestid
        GROUP BY
            m.XM,
            dt1.km UNION ALL
        SELECT
            m.XM,
            dt2.km,
            count( m.REQUESTID ) AS cnt,
            sum(dt2.djje) AS TZJE
        FROM
            FORMTABLE_MAIN_73_dt2 dt2
                LEFT JOIN FORMTABLE_MAIN_73 m ON m.id = dt2.mainid
                LEFT JOIN workflow_requestBase b ON m.REQUESTID = b.requestid
        GROUP BY
            m.XM,
            dt2.km
    ) TB1
GROUP BY
    TB1.XM,
    TB1.KM;

--更改FNAQCSTATEMENT表结构


SELECT
TBBM.SELECTNAME BM, DW.SELECTNAME DW , TBA.grades GRADES, TBA.rs RYS ,TBA.grades/TBA.rs RYB
FROM
(
SELECT
	KHYBM,
	KHYDW,
	- 1 * COUNT( ry ) * ( TO_DATE( '2021-06-10', 'yyyy-mm-dd' ) - TO_DATE( '2021-06-10', 'yyyy-mm-dd' )+1) b1,
	- 1 * COUNT( ry ) * ( TO_DATE( '2021-06-10', 'yyyy-mm-dd' ) - TO_DATE( '2021-06-10', 'yyyy-mm-dd' )+1) + NVL(SUM(sjfs), 0)  grades,
	NVL(SUM(sjfs), 0)  rys,
	COUNT( ry ) rs
FROM
	uf_xtdlkhry ufmo
	LEFT JOIN (
	SELECT
		COUNT( RELATEDID ) sjfs,
		RELATEDID
	FROM
		(
		SELECT
			OPERATEDATE，RELATEDID
		FROM
			HRMSYSMAINTENANCELOG
		WHERE
			TO_DATE( OPERATEDATE, 'yyyy-mm-dd' ) >= TO_DATE( '2021-06-10', 'yyyy-mm-dd' )
			AND TO_DATE( OPERATEDATE, 'yyyy-mm-dd' ) <= TO_DATE( '2021-06-10', 'yyyy-mm-dd' )
			AND OPERATETYPE = '6'
		GROUP BY
			OPERATEDATE，RELATEDID
		)
	GROUP BY
		RELATEDID
	) sumsj ON sumsj.RELATEDID = ufmo.RY
	WHERE ufmo.zt = '0'
GROUP BY
	KHYBM,
	KHYDW
) TBA
LEFT JOIN (select selectvalue,selectname FROM
workflow_selectitem WHERE fieldid= 77962
) TBBM ON (TBA.KHYBM = TBBM.selectvalue)
LEFT JOIN (select selectvalue,selectname FROM
workflow_selectitem WHERE fieldid= 77963
) DW ON (TBA.KHYDW = DW.selectvalue)
 where 1=1
GROUP BY TBBM.SELECTNAME,DW.SELECTNAME,TBA.grades,TBA.rs
ORDER BY TBBM.SELECTNAME,DW.SELECTNAME





CREATE TABLE "ECOLOGY"."GRADESLOG"
   (	"unit" VARCHAR2(255) NOT NULL ENABLE,
	"department" VARCHAR2(255) NOT NULL ENABLE,
	"job" VARCHAR2(255) NOT NULL ENABLE,
	"name" VARCHAR2(255) NOT NULL ENABLE,
	"notlogindate" VARCHAR2(255) NOT NULL ENABLE,
	"id" NUMBER NOT NULL ENABLE
   ) SEGMENT CREATION DEFERRED
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  TABLESPACE "ECOLOGY"


SELECT
ry
FROM
uf_xtdlkhry
WHERE
KHYBM = ''
AND KHYDW = '';


SELECT
OPERATEDATE，RELATEDID
FROM
HRMSYSMAINTENANCELOG
WHERE
TO_DATE( OPERATEDATE, 'yyyy-mm-dd' ) >= TO_DATE( '2021-06-09', 'yyyy-mm-dd' )
AND TO_DATE( OPERATEDATE, 'yyyy-mm-dd' ) <= TO_DATE( '2021-06-09', 'yyyy-mm-dd' )
AND OPERATETYPE = '6'
AND RELATEDID IN ('','1')
GROUP BY
OPERATEDATE，RELATEDID

SELECT
TBBM.SELECTNAME,
DW.SELECTNAME,
PERSON,
NONEDATE
FROM
LOGINGRADES TBA
LEFT JOIN (select selectvalue,selectname FROM
workflow_selectitem WHERE fieldid= 77962
) TBBM ON (TBA.DEPART = TBBM.selectvalue)
LEFT JOIN (select selectvalue,selectname FROM
workflow_selectitem WHERE fieldid= 77963
) DW ON (TBA.UNIT = DW.selectvalue)

