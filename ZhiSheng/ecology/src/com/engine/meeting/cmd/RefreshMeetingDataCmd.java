package com.engine.meeting.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.InsertModuleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 门户新闻CMD
 *
 * <AUTHOR>
 */
public class RefreshMeetingDataCmd extends AbstractCommonCommand<Map<String, Object>> {

    public RefreshMeetingDataCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>(2);
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        //默认成功
        result.put("status", "1");
        boolean rsFlag;
        try {
            RecordSet rs = new RecordSet();
            String sql = "SELECT " +
                    " a.memberid, " +
                    " b.xm, " +
                    " b.hyxs, " +
                    " b.begindate + ' ' + b.begintime AS begintime, " +
                    " b.enddate + ' ' + b.endtime AS endtime, " +
                    " b.meetingtype, " +
                    " b.name, " +
                    " case when a.isattend = '1' then '是' " +
                    " when a.isattend = '2' then '否' else '' end as isattend, " +
                    " a.recRemark  " +
                    "FROM " +
                    " meeting_member2 a " +
                    " INNER JOIN meeting b ON ( a.meetingid = b.id )  " +
                    "ORDER BY " +
                    " a.meetingid";
            bb.writeLog("sql:" + sql);
            rsFlag = rs.executeQuery(sql);
            if (!rsFlag) {
                result.put("status", "-1");
                result.put("msg", rs.getMsg());
            } else {
                String tableName = Util.null2String(bb.getPropValue("zhisheng_mode", "uf_hychmx"));
                int moduleId = Util.getIntValue(bb.getPropValue("zhisheng_mode", "uf_hychmx_module"));

                //先删除原数据
                RecordSet rsDel = new RecordSet();
                rsDel.executeUpdate("delete from " + tableName);
                List<String> insertFields = new ArrayList<>();
                insertFields.add("chr");
                insertFields.add("xm");
                insertFields.add("hykssj");
                insertFields.add("hyjssj");
                insertFields.add("hylx");
                insertFields.add("hyxs");
                insertFields.add("hyzt");
                insertFields.add("sfch");
                insertFields.add("bzsm");
                List<Object> values;
                int creatId = SDUtil.getSystemMangerByLoginId();
                while (rs.next()) {
                    values = new ArrayList<>();
                    values.add(rs.getInt("memberid"));
                    values.add(rs.getString("xm"));
                    values.add(rs.getString("begintime"));
                    values.add(rs.getString("endtime"));
                    values.add(rs.getInt("meetingtype"));
                    values.add(rs.getInt("hyxs"));
                    values.add(rs.getString("name"));
                    values.add(rs.getString("isattend"));
                    values.add(rs.getString("recRemark"));
                    InsertModuleUtil.ModuleInsert(tableName, insertFields, values, creatId, moduleId, null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("status", "-1");
            result.put("msg", e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

}
