package com.engine.meeting.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * 门户新闻服务
 */
public interface MeetingDataService {
    /**
     * 刷新配置数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> refreshMeeting(Map<String, Object> params, User user);

    /**
     * 刷新会议汇总数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> refreshMeetingSummary(Map<String, Object> params, User user);
}
