package com.engine.meeting.service.impl;

import com.engine.core.impl.Service;
import com.engine.meeting.cmd.RefreshMeetingDataCmd;
import com.engine.meeting.cmd.RefreshMeetingSummaryDataCmd;
import com.engine.meeting.service.MeetingDataService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 */
public class MeetingDataServiceImpl extends Service implements MeetingDataService {
    @Override
    public Map<String, Object> refreshMeeting(Map<String, Object> params, User user) {
        return commandExecutor.execute(new RefreshMeetingDataCmd(params, user));
    }

    @Override
    public Map<String, Object> refreshMeetingSummary(Map<String, Object> params, User user) {
        return commandExecutor.execute(new RefreshMeetingSummaryDataCmd(params, user));
    }
}
