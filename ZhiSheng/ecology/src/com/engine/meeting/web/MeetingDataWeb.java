package com.engine.meeting.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.meeting.service.MeetingDataService;
import com.engine.meeting.service.impl.MeetingDataServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 会议数据
 */
public class MeetingDataWeb {

    private MeetingDataService getService(User user) {
        return ServiceUtil.getService(MeetingDataServiceImpl.class, user);
    }


    /**
     * 重新生成建模数据
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refreshMeeting")
    @Produces(MediaType.TEXT_PLAIN)
    public String refreshMeeting(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).refreshMeeting(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 重新生成建模数据
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refreshMeetingSummary")
    @Produces(MediaType.TEXT_PLAIN)
    public String refreshMeetingSummary(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).refreshMeetingSummary(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }

}
