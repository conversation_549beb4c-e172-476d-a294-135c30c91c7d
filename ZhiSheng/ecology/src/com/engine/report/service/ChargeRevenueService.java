package com.engine.report.service;

import java.util.Map;

public interface ChargeRevenueService {
    /**
     * 获取主列表数据
     *
     * @param params
     * @return
     */
    Map<String, Object> tableData(Map<String, Object> params);

    /**
     * 获取高级搜索条件
     *
     * @param params
     * @return
     */
    Map<String, Object> tableCondition(Map<String, Object> params);

    /**
     * 获取明细数据
     *
     * @param params
     * @return
     */
    Map<String, Object> tableDetailData(Map<String, Object> params);

    /**
     * 获取高级搜索条件
     *
     * @param params
     * @return
     */
    Map<String, Object> detailTableCondition(Map<String, Object> params);
}
