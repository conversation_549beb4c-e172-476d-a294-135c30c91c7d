package com.engine.report.service.Impl;


import com.engine.core.impl.Service;
import com.engine.report.cmd.chargeRevenue.TableConditionCmd;
import com.engine.report.cmd.chargeRevenue.TableDataCmd;
import com.engine.report.cmd.chargeRevenueDetail.DetailTableConditionCmd;
import com.engine.report.cmd.chargeRevenueDetail.TableDetailDataCmd;
import com.engine.report.service.ChargeRevenueService;

import java.util.Map;


public class ChargeRevenueServiceImpl extends Service implements ChargeRevenueService {

    @Override
    public Map<String, Object> tableData(Map<String, Object> params) {
        return commandExecutor.execute(new TableDataCmd(user, params));
    }

    @Override
    public Map<String, Object> tableCondition(Map<String, Object> params) {
        return commandExecutor.execute(new TableConditionCmd(user, params));
    }

    @Override
    public Map<String, Object> tableDetailData(Map<String, Object> params) {
        return commandExecutor.execute(new TableDetailDataCmd(user, params));
    }

    @Override
    public Map<String, Object> detailTableCondition(Map<String, Object> params) {
        return commandExecutor.execute(new DetailTableConditionCmd(user, params));
    }
}
