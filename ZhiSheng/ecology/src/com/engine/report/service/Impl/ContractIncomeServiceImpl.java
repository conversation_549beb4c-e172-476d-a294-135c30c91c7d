package com.engine.report.service.Impl;


import com.engine.core.impl.Service;
import com.engine.report.cmd.contractIncome.TableConditionCmd;
import com.engine.report.cmd.contractIncome.TableDataCmd;
import com.engine.report.cmd.contractIncomeDetail.DetailTableConditionCmd;
import com.engine.report.cmd.contractIncomeDetail.TableDetailDataCmd;
import com.engine.report.service.ContractIncomeService;

import java.util.Map;


public class ContractIncomeServiceImpl extends Service implements ContractIncomeService {

    @Override
    public Map<String, Object> tableData(Map<String, Object> params) {
        return commandExecutor.execute(new TableDataCmd(user, params));
    }

    @Override
    public Map<String, Object> tableCondition(Map<String, Object> params) {
        return commandExecutor.execute(new TableConditionCmd(user, params));
    }

    @Override
    public Map<String, Object> tableDetailData(Map<String, Object> params) {
        return commandExecutor.execute(new TableDetailDataCmd(user, params));
    }

    @Override
    public Map<String, Object> detailTableCondition(Map<String, Object> params) {
        return commandExecutor.execute(new DetailTableConditionCmd(user, params));
    }
}
