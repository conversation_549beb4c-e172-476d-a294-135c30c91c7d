package com.engine.report.cmd.contractIncome;

import com.cloudstore.eccom.constant.WeaBoolAttr;
import com.cloudstore.eccom.pc.table.WeaTable;
import com.cloudstore.eccom.pc.table.WeaTableColumn;
import com.cloudstore.eccom.result.WeaResultMsg;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import org.apache.commons.lang.StringUtils;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


/**
 * 表单数据查询CMD
 */
public class TableDataCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;

    public TableDataCmd(User user, Map<String, Object> params) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> apidatas = new HashMap<>();
        //角色判断参考代码
//        if(!HrmUserVarify.checkUserRight("LogView:View", user)){
//            apidatas.put("hasRight", false);
//            return apidatas;
//        }

        if (null == user) {
            apidatas.put("hasRight", false);
            return apidatas;
        }
        bb.writeLog("params:" + params);
        try {
            //返回消息结构体
            WeaResultMsg result = new WeaResultMsg(false);
            String pageID = String.valueOf(UUID.randomUUID());
            String pageUid = pageID + "_" + user.getUID();
            String pageSize = PageIdConst.getPageSize(pageID, user.getUID());
            String sqlwhere = "";

            //新建一个weatable
            WeaTable table = new WeaTable();
            table.setPageUID(pageUid);
            table.setPageID(pageID);
            table.setPagesize(pageSize);

            //这里设置星号，这里用到PIVOT函数，无法确定固定字段名，得用*取所有
            table.setBackfields("*");
            table.setSqlform(" (" + getTotalSql() + ") TBTOP");
            table.setSqlwhere(sqlwhere);
            table.setSqlorderby("xm");
            table.setSqlprimarykey("xm");
            table.setSqlisdistinct("false");
            //设置标题名称
            setColumnName(table);

            //设置左侧check默认不存在
            table.setCheckboxList(null);
            table.setCheckboxpopedom(null);

            result.putAll(table.makeDataResult());
            result.put("hasRight", true);
            result.success();
            apidatas = result.getResultMap();

        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch excption:" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return apidatas;
    }

    /**
     * 获取查询总sql
     *
     * @return
     */
    public String getTotalSql() {
        //查询条件
        //项目
        String project = Util.null2String(params.get("project"));
        //年份
        String year = Util.null2String(params.get("year"));
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT ");
        sb.append(" TBA.xm, ");
        sb.append(" TBF.subcompanyname, ");
        sb.append(" Convert(decimal(18,2),TBA.htzje) as htzje, ");
        sb.append(" Convert(decimal(18,2),TBB.ndysje) as ndysje, ");
        sb.append(" Convert(decimal(18,2),TBC.bnss) as bnss, ");
        sb.append(" Convert(decimal(18,2),TBD.ysje) as ysje, ");
        sb.append(" Convert(decimal(18,2), (TBA.htzje - TBD.ysje)) as wsje, ");
        sb.append(" Convert(decimal(18,2), TBE.qsje) as qsje ");
        sb.append(" FROM ");
        //TBA
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" a.xm, ");
        sb.append(" sum( a.htzje ) AS htzje  ");
        sb.append(" FROM ");
        sb.append(" uf_yxht a  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1  ");
        //搜索条件,这里可以放高级搜索的的条件
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT ( a.sqrq, 4 ) <= ").append(year);
        } else {
            sb.append(" AND LEFT ( a.sqrq, 4 ) <= YEAR(GETDATE()) ");
        }
        if (StringUtils.isNotBlank(project)) {
            String[] prjArray = project.split(CommonCst.COMMA_EN);
            sb.append(" and ( ");
            for (int i = 0; i < prjArray.length; i++) {
                if (i > 0) {
                    sb.append(" or ");
                }
                sb.append(" (a.xm = '").append(prjArray[i]).append("') ");
            }
            sb.append("  ) ");
        }

        sb.append(" GROUP BY ");
        sb.append(" a.xm  ");
        sb.append(" ) TBA ");
        //TBB
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" b.xm, ");
        sb.append(" sum( ndysje ) AS ndysje  ");
        sb.append(" FROM ");
        sb.append(" uf_yxht_dt1 a ");
        sb.append(" LEFT JOIN uf_yxht b ON ( a.mainid = b.id )  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1  ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND a.nf = ").append(year);
        } else {
            sb.append(" AND a.nf = YEAR (GETDATE()) ");
        }
        sb.append(" GROUP BY ");
        sb.append(" b.xm  ");
        sb.append(" ) TBB ON ( TBA.xm = TBB.xm ) ");
        //TBC
        sb.append(" LEFT JOIN ( SELECT xm, sum( je ) AS bnss FROM uf_yxhtsr ");
        sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT ( fyrq, 4 ) = ").append(year);
        } else {
            sb.append(" AND LEFT ( fyrq, 4 ) = YEAR ( GETDATE ())");
        }
        sb.append(" GROUP BY xm ) TBC ON ( TBA.xm = TBC.xm ) ");
        //TBD
        sb.append(" LEFT JOIN ( SELECT xm, sum( je ) AS ysje FROM uf_yxhtsr ");
        sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT (fyrq, 4) <= ").append(year);
        } else {
            sb.append(" AND LEFT (fyrq, 4) <= YEAR(GETDATE()) ");
        }
        sb.append(" GROUP BY xm ) TBD ON (TBA.xm = TBD.xm) ");
        //TBE
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" s.*  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" v.xm, ");
        sb.append(" v.qsje,( ");
        sb.append(" tjrq + ' ' + tjsj + ':00'  ");
        sb.append(" ) AS mytime, ");
        sb.append(" row_number() over ( PARTITION BY xm ORDER BY tjrq + ' ' + tjsj + ':00' DESC ) AS order_num  ");
        sb.append(" FROM ");
        sb.append(" uf_yxhtqs v  ");
        sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT ( tjrq, 4 ) <= ").append(year);
        } else {
            sb.append(" AND LEFT ( tjrq, 4 ) <= YEAR(GETDATE()) ");
        }
        sb.append(" ) s  ");
        sb.append(" WHERE ");
        sb.append(" s.order_num = 1  ");
        sb.append(" ) TBE ON ( TBA.xm = TBE.xm ) ");
        //TBF
        sb.append(" left join view_fbxx TBF on (TBA.xm = TBF.id) ");

        bb.writeLog("totalsql: " + sb);
        return sb.toString();
    }


    /**
     * 设置标题栏名称
     *
     * @param table
     */
    private void setColumnName(WeaTable table) {
        table.getColumns().add(new WeaTableColumn("xm").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        table.getColumns().add(new WeaTableColumn("20%", "项目", "subcompanyname", "subcompanyname"));
        table.getColumns().add(new WeaTableColumn("20%", "合同金额", "htzje", "htzje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "本年应收", "ndysje", "ndysje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "本年实收", "bnss", "bnss", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "合同已收金额", "ysje", "ysje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "合同未收金额", "wsje", "wsje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "合同欠收金额", "qsje", "qsje", "com.engine.report.util.TransUtil.getNumberValue"));
    }

}
