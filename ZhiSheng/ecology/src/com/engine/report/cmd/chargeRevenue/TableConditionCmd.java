package com.engine.report.cmd.chargeRevenue;

import com.api.browser.bean.BrowserBean;
import com.api.browser.bean.SearchConditionGroup;
import com.api.browser.bean.SearchConditionItem;
import com.api.browser.util.ConditionFactory;
import com.api.browser.util.ConditionType;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 列表查询条件
 */
public class TableConditionCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    public TableConditionCmd(User user, Map<String, Object> params) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        Map<String, Object> apidatas = new HashMap<>();
        //角色判断参考代码
//        if(!HrmUserVarify.checkUserRight("LogView:View", user)){
//            apidatas.put("hasRight", false);
//            return apidatas;
//        }

        if (null == user) {
            apidatas.put("hasRight", false);
            return apidatas;
        }

        apidatas.put("hasRight", true);

        ConditionFactory conditionFactory = new ConditionFactory(user);

        //条件组
        List<SearchConditionGroup> addGroups = new ArrayList<>();
        List<SearchConditionItem> conditionItems = new ArrayList<>();

        //项目浏览框类型。162代表自定义多选
        String projectBrowserType = Util.null2String(bb.getPropValue("zhisheng_report", "browser_project_type"));
        //文本输入框
        SearchConditionItem selfItem = conditionFactory.createCondition(ConditionType.BROWSER, -1, "project", projectBrowserType);
        selfItem.setColSpan(2);//定义一行显示条件数，默认值为2,当值为1时标识该条件单独占一行
        selfItem.setFieldcol(16);    //条件输入框所占宽度，默认值18
        selfItem.setLabelcol(8);
        selfItem.setViewAttr(2); //	 编辑权限 1：只读，2：可编辑， 3：必填 默认2
        selfItem.setLabel("项目"); //设置文本值 这个将覆盖多语言标签的值
        BrowserBean browserBean = selfItem.getBrowserConditionParam();
        Map<String, Object> conditionDataParams = new HashMap<>();
        conditionDataParams.put("type", "browser.zs_xm");
        browserBean.setConditionDataParams(conditionDataParams);
        browserBean.setTitle("项目");
        Map<String, Object> completeParams = new HashMap<>();
        completeParams.put("type", projectBrowserType);
        completeParams.put("fielddbtype", "browser.zs_xm");

        browserBean.setCompleteParams(completeParams);
        browserBean.getDestDataParams().put("type", "browser.zs_xm");
        browserBean.getDataParams().put("type", "browser.zs_xm");
        selfItem.setBrowserConditionParam(browserBean);
        conditionItems.add(selfItem);


        //日期范围
        SearchConditionItem datePicker = conditionFactory.createCondition(ConditionType.RANGEPICKER, -1, "datePicker");
        datePicker.setColSpan(2);//定义一行显示条件数，默认值为2,当值为1时标识该条件单独占一行
        datePicker.setFieldcol(16);    //条件输入框所占宽度，默认值18
        datePicker.setLabelcol(8);
        datePicker.setViewAttr(2);  //	 编辑权限  1：只读，2：可编辑， 3：必填   默认2
        datePicker.setLabel("日期选择"); //设置文本值  这个将覆盖多语言标签的值
        conditionItems.add(datePicker);

//        //文本输入框
//        SearchConditionItem requestname = conditionFactory.createCondition(ConditionType.INPUT, 502327, "requestname");
//        requestname.setColSpan(2);//定义一行显示条件数，默认值为2,当值为1时标识该条件单独占一行
//        requestname.setFieldcol(16);    //条件输入框所占宽度，默认值18
//        requestname.setLabelcol(8);
//        requestname.setViewAttr(3);  //	 编辑权限  1：只读，2：可编辑， 3：必填   默认2
//        requestname.setLabel("请求标题"); //设置文本值  这个将覆盖多语言标签的值
//        requestname.setRules("required");
//        conditionItems.add(requestname);
//
//
//        //文本输入框
//        SearchConditionItem creater = conditionFactory.createCondition(ConditionType.BROWSER, 502327, "creater", "17");
//        creater.setColSpan(2);//定义一行显示条件数，默认值为2,当值为1时标识该条件单独占一行
//        creater.setFieldcol(16);    //条件输入框所占宽度，默认值18
//        creater.setLabelcol(8);
//        creater.setViewAttr(2);  //	 编辑权限  1：只读，2：可编辑， 3：必填   默认2
//        creater.setLabel("创建人"); //设置文本值  这个将覆盖多语言标签的值
//        conditionItems.add(creater);
//
//        //下拉选择框类
//        SearchConditionItem demo_select = conditionFactory.createCondition(ConditionType.SELECT, 502327, "state");
//        List<SearchConditionOption> selectOptions = new ArrayList<>();  //设置选项值
//        selectOptions.add(new SearchConditionOption("0", "", true));
//        selectOptions.add(new SearchConditionOption("1", "开始"));
//        selectOptions.add(new SearchConditionOption("2", "流转"));
//        selectOptions.add(new SearchConditionOption("3", "撤销"));
//        selectOptions.add(new SearchConditionOption("4", "结束"));
//        demo_select.setOptions(selectOptions);
//        demo_select.setColSpan(2);
//        demo_select.setFieldcol(16);
//        demo_select.setLabelcol(8);
//        demo_select.setIsQuickSearch(false);
//        demo_select.setLabel("状态");
//        conditionItems.add(demo_select);
//
//
//        //下拉选择框类
//        SearchConditionItem type = conditionFactory.createCondition(ConditionType.SELECT, 502327, "type");
//        List<SearchConditionOption> typeOptions = new ArrayList<>();  //设置选项值
//        typeOptions.add(new SearchConditionOption("0", "", true));
//        typeOptions.add(new SearchConditionOption("1", "类型一"));
//        typeOptions.add(new SearchConditionOption("2", "类型二"));
//        typeOptions.add(new SearchConditionOption("3", "类型三"));
//        type.setOptions(typeOptions);
//        type.setColSpan(2);
//        type.setFieldcol(16);
//        type.setLabelcol(8);
//        type.setIsQuickSearch(false);
//        type.setLabel("类型");
//        conditionItems.add(type);

        addGroups.add(new SearchConditionGroup("", true, conditionItems));

        apidatas.put("condition", addGroups);

        return apidatas;

    }
}
