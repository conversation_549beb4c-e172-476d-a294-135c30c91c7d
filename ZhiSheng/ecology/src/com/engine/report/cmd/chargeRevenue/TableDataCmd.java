package com.engine.report.cmd.chargeRevenue;

import com.cloudstore.eccom.constant.WeaBoolAttr;
import com.cloudstore.eccom.pc.table.WeaTable;
import com.cloudstore.eccom.pc.table.WeaTableColumn;
import com.cloudstore.eccom.result.WeaResultMsg;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;


/**
 * 表单数据查询CMD
 */
public class TableDataCmd extends AbstractCommonCommand<Map<String, Object>> {

    private RecordSet rs;
    private StringBuilder sb;
    private final BaseBean bb;

    public TableDataCmd(User user, Map<String, Object> params) {
        this.user = user;
        this.params = params;
        rs = null;
        sb = null;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> apidatas = new HashMap<>();
        //角色判断参考代码
//        if(!HrmUserVarify.checkUserRight("LogView:View", user)){
//            apidatas.put("hasRight", false);
//            return apidatas;
//        }

        if (null == user) {
            apidatas.put("hasRight", false);
            return apidatas;
        }
        bb.writeLog("params:" + params);
        try {
            //返回消息结构体
            WeaResultMsg result = new WeaResultMsg(false);

            String pageID = String.valueOf(UUID.randomUUID());
            String pageUid = pageID + "_" + user.getUID();
            String pageSize = PageIdConst.getPageSize(pageID, user.getUID());
            String sqlwhere = "";
            List<String> allColmns = getAllColumn();

            //新建一个weatable
            WeaTable table = new WeaTable();
            table.setPageUID(pageUid);
            table.setPageID(pageID);
            table.setPagesize(pageSize);
            //设置字段id
            String fileds = getColumnFieldId(allColmns);
            //这里设置星号，这里用到PIVOT函数，无法确定固定字段名，得用*取所有
            table.setBackfields("*");
            table.setSqlform(" (" + getTotalSql() + ") TBTOP");
            table.setSqlwhere(sqlwhere);
            table.setSqlorderby("xm");
            table.setSqlprimarykey("xm");
            table.setSqlisdistinct("false");
            //设置标题名称
            setColumnName(table, allColmns);

            //设置左侧check默认不存在
            table.setCheckboxList(null);
            table.setCheckboxpopedom(null);

            result.putAll(table.makeDataResult());
            result.put("hasRight", true);
            result.success();
            apidatas = result.getResultMap();

        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch excption:" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return apidatas;
    }

    /**
     * 获取查询总sql
     *
     * @return
     */
    public String getTotalSql() {
        List<String> allColmns = getAllColumn();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT TBL.* FROM ( ");
        sb.append(" SELECT * FROM ");
        sb.append(" (select a.xm,d.subcompanyname,isnull(a.je,0) as je,c.id as kmid from uf_fyzcxx a ");
        sb.append(" left join uf_wdykm_dt1 b on (a.kmbh = b.kmbm) ");
        sb.append(" left join uf_wdykm c on (b.mainid = c.id) ");
        sb.append(" left join view_fbxx d on (a.xm = d.id) ");
        sb.append(" where 1=1 ");

        //搜索条件,这里可以放高级搜索的的条件
        String project = Util.null2String(params.get("project"));
        if (StringUtils.isNotBlank(project)) {
            String[] prjArray = project.split(CommonCst.COMMA_EN);
            sb.append(" and ( ");
            for (int i = 0; i < prjArray.length; i++) {
                if (i > 0) {
                    sb.append(" or ");
                }
                sb.append(" (a.xm = '").append(prjArray[i]).append("') ");
            }
            sb.append("  ) ");
        }
        //时间段(参数格式为 2022-02-18,2022-03-03)
        String datePicker = Util.null2String(params.get("datePicker"));
        if (StringUtils.isNotBlank(datePicker)) {
            //开始日期
            String startDate = datePicker.substring(0, datePicker.indexOf(","));
            String endDate = datePicker.substring(datePicker.indexOf(",") + 1);
            sb.append("  and a.sqrq >= '").append(startDate).append("' ");
            sb.append("  and a.sqrq <= '").append(endDate).append("' ");
        }
        sb.append(" ) ");
        sb.append(" P ");
        sb.append(" PIVOT(SUM(P.je) FOR kmid IN (  ");
        for (int i = 0; i < allColmns.size(); i++) {
            sb.append("[").append(allColmns.get(i)).append("]");
            if (i < allColmns.size() - 1) {
                sb.append(",");
            }
        }
        sb.append(" ) ) AS PVT ");
        sb.append(" ) TBL ");

        bb.writeLog("totalsql: " + sb);
        return sb.toString();
    }

    /**
     * 获取字段id
     *
     * @param allColmns
     * @return
     */
    public String getColumnFieldId(List<String> allColmns) {
        sb = new StringBuilder();
        sb.append("*");
        for (String allColmn : allColmns) {
            sb.append(",");
            sb.append(allColmn);

        }
        return sb.toString();
    }


    /**
     * 获取column数据
     *
     * @return
     */
    private List<String> getAllColumn() {
        List<String> result = new ArrayList<>();
        rs = new RecordSet();
        rs.executeQuery("select id from uf_wdykm where zt =0 order by xh ");
        while (rs.next()) {
            result.add(Util.null2String(rs.getString("id")));
        }
        return result;
    }

    /**
     * 设置标题栏名称
     *
     * @param table
     * @param allColmns
     */
    private void setColumnName(WeaTable table, List<String> allColmns) {
        Map<String, String> colMap = getColumnsMap();
        table.getColumns().add(new WeaTableColumn("xm").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        table.getColumns().add(new WeaTableColumn("20%", "项目", "subcompanyname", "subcompanyname"));
        for (String s : allColmns) {
            table.getColumns().add(new WeaTableColumn("20%", colMap.get(s), s, s, "com.engine.report.util.TransUtil.getNumberValue"));
        }
    }

    /**
     * 获取字段Map
     *
     * @return
     */
    private Map<String, String> getColumnsMap() {
        Map<String, String> result = new HashMap<>();
        rs = new RecordSet();
        rs.executeQuery("select id,wd from uf_wdykm  where zt =0 GROUP BY id,wd  ");
        while (rs.next()) {
            result.put(Util.null2String(rs.getString("id")), Util.null2String(rs.getString("wd")));
        }
        return result;
    }
}
