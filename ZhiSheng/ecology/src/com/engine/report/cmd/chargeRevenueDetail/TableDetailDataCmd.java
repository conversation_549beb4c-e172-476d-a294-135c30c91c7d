package com.engine.report.cmd.chargeRevenueDetail;

import com.cloudstore.eccom.constant.WeaBoolAttr;
import com.cloudstore.eccom.pc.table.WeaTable;
import com.cloudstore.eccom.pc.table.WeaTableColumn;
import com.cloudstore.eccom.result.WeaResultMsg;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import org.apache.commons.lang.StringUtils;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


/**
 * 表单数据查询CMD
 */
public class TableDetailDataCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;

    public TableDetailDataCmd(User user, Map<String, Object> params) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> apidatas = new HashMap<>();
        //角色判断参考代码
//        if(!HrmUserVarify.checkUserRight("LogView:View", user)){
//            apidatas.put("hasRight", false);
//            return apidatas;
//        }

        if (null == user) {
            apidatas.put("hasRight", false);
            return apidatas;
        }
        bb.writeLog("params:" + params);
        try {
            //返回消息结构体
            WeaResultMsg result = new WeaResultMsg(false);
            String pageID = String.valueOf(UUID.randomUUID());
            String pageUid = pageID + "_" + user.getUID();
            String pageSize = PageIdConst.getPageSize(pageID, user.getUID());
            String sqlwhere = "";

            //新建一个weatable
            WeaTable table = new WeaTable();
            table.setPageUID(pageUid);
            table.setPageID(pageID);
            table.setPagesize(pageSize);

            //这里设置星号，这里用到PIVOT函数，无法确定固定字段名，得用*取所有
            table.setBackfields("*");
            table.setSqlform(" (" + getTotalSql() + ") TBTOP");
            table.setSqlwhere(sqlwhere);
            //排序字段
            table.setSqlorderby("kmbh,je desc");
            //主键
            table.setSqlprimarykey("newid");
            table.setSqlisdistinct("false");
            //设置标题名称
            setColumnName(table);

            //设置左侧check默认不存在
            table.setCheckboxList(null);
            table.setCheckboxpopedom(null);

            result.putAll(table.makeDataResult());
            result.put("hasRight", true);
            result.success();
            apidatas = result.getResultMap();

        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch excption:" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return apidatas;
    }

    /**
     * 获取查询总sql
     *
     * @return
     */
    public String getTotalSql() {
        //查询条件
        //项目id
        String project = Util.null2String(params.get("project"));
        //维度id
        String wdid = Util.null2String(params.get("wdid"));
        //时间段(参数格式为 2022-02-18,2022-03-03)
        String datePicker = Util.null2String(params.get("datePicker"));
        //科目名称
        String kmmcCondtion = Util.null2String(params.get("kmmc"));
        //科目编号
        String kmbhCondtion = Util.null2String(params.get("kmbh"));
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  NEWID() AS newid, ");
        sb.append(" TBL.* FROM(select a.xm,c.id as wdid,a.kmmc as kmid,a.kmmc,a.kmbh,sum(a.je) as je from uf_fyzcxx a ");
        sb.append("  left join uf_wdykm_dt1 b on (a.kmbh = b.kmbm) ");
        sb.append("  left join uf_wdykm c on (b.mainid = c.id) ");
        sb.append("  where 1=1 ");
        sb.append(" and a.xm = '").append(project).append("' ");
        sb.append(" and c.id = '").append(wdid).append("' ");
        if (StringUtils.isNotBlank(datePicker)) {
            //开始日期
            String startDate = datePicker.substring(0, datePicker.indexOf(","));
            //结束日期
            String endDate = datePicker.substring(datePicker.indexOf(",") + 1);
            sb.append("  and a.sqrq >= '").append(startDate).append("' ");
            sb.append("  and a.sqrq <= '").append(endDate).append("' ");
        }
        if (StringUtils.isNotBlank(kmmcCondtion)) {
            String[] prjArray = kmmcCondtion.split(CommonCst.COMMA_EN);
            sb.append(" and ( ");
            for (int i = 0; i < prjArray.length; i++) {
                if (i > 0) {
                    sb.append(" or ");
                }
                sb.append(" (a.kmmc = '").append(prjArray[i]).append("') ");
            }
            sb.append("  ) ");
        }
        if (StringUtils.isNotBlank(kmbhCondtion)) {
            sb.append(" and a.kmbh like '%").append(kmbhCondtion).append("%' ");
        }
        sb.append("  group by a.xm,c.id,a.kmmc,a.kmbh)TBL ");

        bb.writeLog("totalsql: " + sb);
        return sb.toString();
    }


    /**
     * 设置标题栏名称
     *
     * @param table
     */
    private void setColumnName(WeaTable table) {
        //主键uuid
        table.getColumns().add(new WeaTableColumn("newid").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        //项目id
        table.getColumns().add(new WeaTableColumn("xm").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        //维度id
        table.getColumns().add(new WeaTableColumn("wdid").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        //科目id
        table.getColumns().add(new WeaTableColumn("kmid").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        table.getColumns().add(new WeaTableColumn("20%", "科目名称", "kmmc", "kmmc", "com.engine.report.cmd.chargeRevenueDetail.DetailTransMethod.transKmmc"));
        table.getColumns().add(new WeaTableColumn("20%", "科目编号", "kmbh", "kmbh"));
        table.getColumns().add(new WeaTableColumn("20%", "金额", "je", "je", "com.engine.report.util.TransUtil.getNumberValue"));
    }

}
