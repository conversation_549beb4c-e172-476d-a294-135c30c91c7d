package com.engine.report.cmd.chargeRevenueDetail;

import com.engine.parent.common.constant.CommonCst;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;

/**
 * 转换结果值
 */
public class DetailTransMethod {

    /**
     * 转换科目名称
     *
     * @param param
     * @return
     */
    public static String transKmmc(String param) {
        StringBuilder result = new StringBuilder();
        RecordSet rs = new RecordSet();
        String sql = "select left(allSupSubjectIds,len(allSupSubjectIds)-1) as subid,name from FnaBudgetfeeType where id = ?";
        rs.executeQuery(sql, param);
        if (rs.next()) {
            String subid = Util.null2String(rs.getString("subid"));
            if (StringUtils.isNotBlank(subid)) {
                StringBuilder sb = new StringBuilder();
                sb.append(" select a.name from FnaBudgetfeeType a where 1=1 ");
                String[] prjArray = subid.split(CommonCst.COMMA_EN);
                sb.append(" and ( ");
                for (int i = 0; i < prjArray.length; i++) {
                    if (i > 0) {
                        sb.append(" or ");
                    }
                    sb.append(" (a.id = ").append(prjArray[i]).append(") ");
                }
                sb.append("  ) ");
                rs.executeQuery(sb.toString());
                while (rs.next()) {
                    result.append(Util.null2String(rs.getString("name"))).append("/");
                }
            } else {
                return Util.null2String(rs.getString("name"));
            }
        }
        if (StringUtils.isNotBlank(result.toString())) {
            return result.substring(0, result.length() - 1);
        } else {
            return param;
        }
    }

}
