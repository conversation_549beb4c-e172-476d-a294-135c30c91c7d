package com.engine.report.cmd.contractIncomeDetail;

import com.cloudstore.eccom.constant.WeaBoolAttr;
import com.cloudstore.eccom.pc.table.WeaTable;
import com.cloudstore.eccom.pc.table.WeaTableColumn;
import com.cloudstore.eccom.result.WeaResultMsg;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import org.apache.commons.lang.StringUtils;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


/**
 * 表单数据查询CMD
 */
public class TableDetailDataCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;

    public TableDetailDataCmd(User user, Map<String, Object> params) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> apidatas = new HashMap<>();
        //角色判断参考代码
//        if(!HrmUserVarify.checkUserRight("LogView:View", user)){
//            apidatas.put("hasRight", false);
//            return apidatas;
//        }

        if (null == user) {
            apidatas.put("hasRight", false);
            return apidatas;
        }
        bb.writeLog("params:" + params);
        try {
            //返回消息结构体
            WeaResultMsg result = new WeaResultMsg(false);
            String pageID = String.valueOf(UUID.randomUUID());
            String pageUid = pageID + "_" + user.getUID();
            String pageSize = PageIdConst.getPageSize(pageID, user.getUID());
            String sqlwhere = "";

            //新建一个weatable
            WeaTable table = new WeaTable();
            table.setPageUID(pageUid);
            table.setPageID(pageID);
            table.setPagesize(pageSize);

            //这里设置星号，这里用到PIVOT函数，无法确定固定字段名，得用*取所有
            table.setBackfields("*");
            table.setSqlform(" (" + getTotalSql() + ") TBTOP");
            table.setSqlwhere(sqlwhere);
            table.setSqlorderby("id");
            table.setSqlprimarykey("id");
            table.setSqlisdistinct("false");
            //设置标题名称
            setColumnName(table);

            //设置左侧check默认不存在
            table.setCheckboxList(null);
            table.setCheckboxpopedom(null);

            result.putAll(table.makeDataResult());
            result.put("hasRight", true);
            result.success();
            apidatas = result.getResultMap();

        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch excption:" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return apidatas;
    }

    /**
     * 获取查询总sql
     *
     * @return
     */
    public String getTotalSql() {
        //查询条件
        //项目
        String project = Util.null2String(params.get("project"));
        //年份
        String year = Util.null2String(params.get("year"));
        //合同名称
        String htmcCondition = Util.null2String(params.get("htmc"));
        //合同编号
        String htbhCondition = Util.null2String(params.get("htbh"));
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT ");
        sb.append(" TBA.id, ");
        sb.append(" TBA.htbh, ");
        sb.append(" TBA.htmc, ");
        sb.append(" Convert(decimal(18,2),TBA.htzje) as htzje, ");
        sb.append(" Convert(decimal(18,2),TBB.ndysje) as ndysje, ");
        sb.append(" Convert(decimal(18,2),TBC.bnss) as bnss, ");
        sb.append(" Convert(decimal(18,2),TBD.ysje) as ysje, ");
        sb.append(" Convert(decimal(18,2),(TBA.htzje - TBD.ysje)) as wsje, ");
        sb.append(" Convert(decimal(18,2),TBE.qsje) as qsje ");
        sb.append(" FROM ");
        //TBA
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" a.id, ");
        sb.append(" a.htbh, ");
        sb.append(" a.htmc, ");
        sb.append(" a.htzje  ");
        sb.append(" FROM ");
        sb.append(" uf_yxht a  ");
        sb.append(" WHERE ");
        sb.append(" 1 = 1  ");
        //搜索条件,这里可以放高级搜索的的条件
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT ( a.sqrq, 4 ) <= ").append(year);
        } else {
            sb.append(" AND LEFT ( a.sqrq, 4 ) <= YEAR(GETDATE()) ");
        }
        //合同名称
        if (StringUtils.isNotBlank(htmcCondition)) {
            sb.append(" AND a.htmc like '%").append(htmcCondition).append("%' ");
        }
        //合同编号
        if (StringUtils.isNotBlank(htbhCondition)) {
            sb.append(" AND a.htbh like '%").append(htbhCondition).append("%' ");
        }
        sb.append(" and a.xm = '").append(project).append("' ");

        sb.append(" ) TBA ");
        //TBB
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" a.mainid, ");
        sb.append(" sum( ndysje ) AS ndysje  ");
        sb.append(" FROM ");
        sb.append(" uf_yxht_dt1 a ");

        sb.append(" WHERE ");
        sb.append(" 1 = 1  ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND a.nf = ").append(year);
        } else {
            sb.append(" AND a.nf = YEAR (GETDATE()) ");
        }
        sb.append(" GROUP BY ");
        sb.append(" a.mainid  ");
        sb.append(" ) TBB ON ( TBA.id = TBB.mainid ) ");
        //TBC
        sb.append(" LEFT JOIN ( SELECT htmc, sum( je ) AS bnss FROM uf_yxhtsr ");
        sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT ( fyrq, 4 ) = ").append(year);
        } else {
            sb.append(" AND LEFT ( fyrq, 4 ) = YEAR ( GETDATE ())");
        }
        sb.append(" GROUP BY htmc ) TBC ON ( TBA.id = TBC.htmc ) ");
        //TBD
        sb.append(" LEFT JOIN ( SELECT htmc, sum( je ) AS ysje FROM uf_yxhtsr ");
        sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT (fyrq, 4) <= ").append(year);
        } else {
            sb.append(" AND LEFT (fyrq, 4) <= YEAR(GETDATE()) ");
        }
        sb.append(" GROUP BY htmc ) TBD ON (TBA.id = TBD.htmc) ");
        //TBE
        sb.append(" LEFT JOIN ( ");
        sb.append(" SELECT ");
        sb.append(" s.*  ");
        sb.append(" FROM ");
        sb.append(" ( ");
        sb.append(" SELECT ");
        sb.append(" v.htmc, ");
        sb.append(" v.qsje,( ");
        sb.append(" tjrq + ' ' + tjsj + ':00'  ");
        sb.append(" ) AS mytime, ");
        sb.append(" row_number() over ( PARTITION BY htmc ORDER BY tjrq + ' ' + tjsj + ':00' DESC ) AS order_num  ");
        sb.append(" FROM ");
        sb.append(" uf_yxhtqs v ");
        sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(year)) {
            sb.append(" AND LEFT ( tjrq, 4 ) <= ").append(year);
        } else {
            sb.append(" AND LEFT ( tjrq, 4 ) <= YEAR(GETDATE()) ");
        }
        sb.append(" ) s  ");
        sb.append(" WHERE ");
        sb.append(" s.order_num = 1  ");
        sb.append(" ) TBE ON ( TBA.id = TBE.htmc ) ");

        bb.writeLog("totalsql: " + sb);
        return sb.toString();
    }


    /**
     * 设置标题栏名称
     *
     * @param table
     */
    private void setColumnName(WeaTable table) {
        table.getColumns().add(new WeaTableColumn("id").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        table.getColumns().add(new WeaTableColumn("20%", "合同名称", "htmc", "htmc"));
        table.getColumns().add(new WeaTableColumn("20%", "合同编码", "htbh", "htbh"));
        table.getColumns().add(new WeaTableColumn("20%", "合同金额", "htzje", "htzje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "本年应收", "ndysje", "ndysje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "本年实收", "bnss", "bnss", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "合同已收金额", "ysje", "ysje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "合同未收金额", "wsje", "wsje", "com.engine.report.util.TransUtil.getNumberValue"));
        table.getColumns().add(new WeaTableColumn("20%", "合同欠收金额", "qsje", "qsje", "com.engine.report.util.TransUtil.getNumberValue"));
    }

}
