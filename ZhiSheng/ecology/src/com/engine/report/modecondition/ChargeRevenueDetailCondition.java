package com.engine.report.modecondition;

import org.apache.commons.lang.StringUtils;
import weaver.formmode.customjavacode.AbstractCustomSqlConditionJavaCode;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.Map;

/**
 * 说明
 * 修改时
 * 类名要与文件名保持一致
 * class文件存放位置与路径保持一致。
 * 请把编译后的class文件，放在对应的目录中才能生效
 * 注意 同一路径下java名不能相同。
 *
 * <AUTHOR>
 */
public class ChargeRevenueDetailCondition extends AbstractCustomSqlConditionJavaCode {

    /**
     * 生成SQL查询限制条件
     *
     * @param param param包含(但不限于)以下数据
     *              user 当前用户
     * @return 返回的查询限制条件的格式举例为: t1.a = '1' and t1.b = '3' and t1.c like '%22%'
     * 其中t1为表单主表表名的别名
     */
    public String generateSqlCondition(Map<String, Object> param) throws Exception {
        String sqlCondition = "";
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("param:" + param);
        String xm = Util.null2String(param.get("xm"));
        String wdid = Util.null2String(param.get("wdid"));
        String kmbh = Util.null2String(param.get("kmbh"));
        String datePicker = Util.null2String(param.get("datePicker"));
        bb.writeLog("datePicker:" + datePicker);
        //固定条件
        sqlCondition += " t1.xm = '" + xm + "' and t1.wdid = '" + wdid + "' and t1.kmbh = '" + kmbh + "' ";
        //动态查询条件
        if (StringUtils.isNotBlank(datePicker) && !"undefined".equals(datePicker)) {
            //开始日期
            String startDate = datePicker.substring(0, datePicker.indexOf(","));
            String endDate = datePicker.substring(datePicker.indexOf(",") + 1);
            sqlCondition += " and t1.sqrq >= '" + startDate + "' ";
            sqlCondition += " and t1.sqrq <= '" + endDate + "' ";
        }
        return sqlCondition;
    }
}
