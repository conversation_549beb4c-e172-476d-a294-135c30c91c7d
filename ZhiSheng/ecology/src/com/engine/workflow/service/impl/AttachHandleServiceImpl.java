package com.engine.workflow.service.impl;

import com.engine.core.impl.Service;
import com.engine.workflow.cmd.Word2PdfCmd;
import com.engine.workflow.service.AttachHandleService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 */
public class AttachHandleServiceImpl extends Service implements AttachHandleService {
    @Override
    public Map<String, Object> word2pdf(Map<String, Object> params, User user) {
        return commandExecutor.execute(new Word2PdfCmd(params, user));
    }
}
