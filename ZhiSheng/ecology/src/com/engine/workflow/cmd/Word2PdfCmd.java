package com.engine.workflow.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.util.AttachHandleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.toolbox.doc.DocUtil;
import weaver.toolbox.doc.DownloadManager;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 门户新闻CMD
 */
public class Word2PdfCmd extends AbstractCommonCommand<Map<String, Object>> {

    public Word2PdfCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", 1);
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        FileInputStream in = null;
        File docFile;
        String fileName;
        StringBuilder newIds = new StringBuilder();
        int catId;
        try {
            //水印文字
            String waterText = Util.null2String(params.get("waterText"));
            //pdf的存储目录id
            catId = Util.getIntValue(Util.null2String(params.get("pdfDocId")));
            //待转换的word附件
            String docids = Util.null2String(params.get("docids"));
            if ("".equals(docids)) {
                result.put("status", 1);
                result.put("msg", "无docid传入");
                return result;
            }
            //表单上已经有的pdf文档id，先删除
            String pdfids = Util.null2String(params.get("pdfids"));
            delPdfDoc(pdfids);
            String[] docidArray = docids.split(CommonCst.COMMA_EN);
            for (String j : docidArray) {
                docFile = new File(DownloadManager.getFileByDocId(Integer.parseInt(j)));
                if (docFile.exists()) {
                    fileName = docFile.getName();
                    int lastOne = fileName.lastIndexOf(".");
                    String extName = fileName.substring((lastOne + 1));
                    //判断是word文件
                    if ("docx".equalsIgnoreCase(extName) || "doc".equalsIgnoreCase(extName)) {
                        String fileNameWithoutExt = fileName.substring(0, lastOne);
                        String newPath = docFile.getAbsolutePath();
                        String newFilePath = newPath.replace(fileName, fileNameWithoutExt + ".pdf");
                        String pdfName = fileNameWithoutExt + ".pdf";
                        AttachHandleUtil.doc2Pdf(newPath, newFilePath, waterText);
                        in = new FileInputStream(newFilePath);
                        //如果参数没传参目录id，则pdf存储和word文件同目录
                        if (catId == -1) {
                            catId = getCatgId(Integer.parseInt(j));
                        }
                        int newDocId = DocUtil.createDocWithFile(in, user, pdfName, catId);
                        newIds.append(newDocId).append(",");
                        //关闭文件流
                        try {
                            in.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("status", -1);
            result.put("msg", e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if (!"".equals(newIds.toString())) {
            newIds = new StringBuilder(newIds.substring(0, newIds.length() - 1));
        }
        result.put("newIds", newIds.toString());
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    /**
     * 删除已有的pdf文档
     *
     * @param pdfids
     */
    private void delPdfDoc(String pdfids) {
        //如果有已存在的pdf文档，先删除
        if (!"".equals(pdfids)) {
            StringBuilder sb = new StringBuilder();
            RecordSet rs = new RecordSet();
            sb.append(" delete from docdetail where 1=1 and ( ");
            String[] pdfidsArray = pdfids.split(CommonCst.COMMA_EN);
            for (int i = 0; i < pdfidsArray.length; i++) {
                sb.append(" (id = ").append(pdfidsArray[i]).append(") ");
                if (i != pdfidsArray.length - 1) {
                    sb.append(" or ");
                }
            }
            sb.append(" ) ");
            writeLog("删除已有pdf sql" + sb);
            rs.executeUpdate(sb.toString());
        }
    }

    /**
     * 根据docid获取目录id
     *
     * @param docid
     * @return
     */
    private int getCatgId(int docid) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select SECCATEGORY from docdetail where id = ? ", docid);
        if (rs.next()) {
            return rs.getInt("SECCATEGORY");
        } else {
            return -1;
        }
    }
}
