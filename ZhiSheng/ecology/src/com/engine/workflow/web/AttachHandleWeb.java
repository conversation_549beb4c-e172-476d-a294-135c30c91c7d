package com.engine.workflow.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.workflow.service.AttachHandleService;
import com.engine.workflow.service.impl.AttachHandleServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 附件处理
 */
public class AttachHandleWeb {

    private AttachHandleService getService(User user) {
        return ServiceUtil.getService(AttachHandleServiceImpl.class, user);
    }


    /**
     * 附件word文档转pdf
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/word2pdf")
    @Produces(MediaType.TEXT_PLAIN)
    public String word2pdf(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).word2pdf(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }

}
