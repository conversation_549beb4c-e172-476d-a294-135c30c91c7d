package com.engine.portal.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.http.dto.ApiResult;
import com.engine.parent.http.util.HttpUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * 门户新闻CMD
 */
public class NewsDataCmd extends AbstractCommonCommand<Map<String, Object>> {

    public NewsDataCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        try {
            String newsUrl = Util.null2String(params.get("newsUrl"));
            ApiResult re = HttpUtil.getDataWithGBK(newsUrl, result);
            if (re.isSuccessFlag()) {
                JSONObject jo = JSONObject.parseObject(re.getData().toString());
                JSONArray ja = jo.getJSONArray("data");
                result.put("status", "1");
                result.put("data", ja);
            } else {
                result.put("status", "-1");
                result.put("msg", re.getResMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("status", "-1");
            result.put("msg", e.getMessage());
        }
        return result;
    }
}
