package com.engine.portal.service.impl;

import com.engine.core.impl.Service;
import com.engine.portal.service.NewsDataService;
import com.engine.portal.cmd.NewsDataCmd;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 */
public class NewsDataServiceImpl extends Service implements NewsDataService {
    @Override
    public Map<String, Object> getNewsList(Map<String, Object> params, User user) {
        return commandExecutor.execute(new NewsDataCmd(params, user));
    }
}
