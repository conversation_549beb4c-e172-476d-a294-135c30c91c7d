package com.engine.shmhxy2.gyl.portal.yiwangtong.web;

import com.alibaba.fastjson.JSON;
import com.engine.common.util.ServiceUtil;
import com.engine.shmhxy2.gyl.portal.yiwangtong.service.WebSiteService;
import com.engine.shmhxy2.gyl.portal.yiwangtong.service.impl.WebSiteServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import java.util.List;

/**
 * @FileName WebSiteWeb.java
 * @Description 一网通办二开接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/3/13
 */
public class WebSiteWeb {

    private WebSiteService getService(User user) {
        return ServiceUtil.getService(WebSiteServiceImpl.class, user);
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getSingleWebsiteContent")
    @Produces({"application/json"})
    public String getSingleWebsiteContent(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        List singleWebsiteContent = this.getService(user).getSingleWebsiteContent(request, user);
        return JSON.toJSONString(singleWebsiteContent);
    }

}
