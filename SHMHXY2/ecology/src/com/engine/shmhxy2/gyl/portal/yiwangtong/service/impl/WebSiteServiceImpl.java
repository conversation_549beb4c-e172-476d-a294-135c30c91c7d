package com.engine.shmhxy2.gyl.portal.yiwangtong.service.impl;

import com.engine.core.impl.Service;
import com.engine.shmhxy2.gyl.portal.yiwangtong.service.WebSiteService;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


public class WebSiteServiceImpl extends Service implements WebSiteService {
    /**
     * @param request
     * @param user
     * @return
     */
    @Override
    public List getSingleWebsiteContent(HttpServletRequest request, User user) {
        BaseBean baseBean = new BaseBean();
        String check = request.getParameter("check");
        int userDepartment = user.getUserDepartment();
        List returnList = new LinkedList();
        String menuId = request.getParameter("id");
        RecordSet rs = new RecordSet();
        String checkSql = "";
        if ("sysadmin".equals(user.getLoginid())) {
            checkSql = "SELECT id,wdmc,xgbm,gw FROM uf_cxwdmkbd WHERE id=" + check;
        } else {
            //checkSql = "SELECT id,wdmc,xgbm FROM uf_cxwdmkbd WHERE id=" + check + " " +
            //       " AND ( xgbm like '" + userDepartment + ",%' OR xgbm like '%," + userDepartment + "' OR xgbm like '%," + userDepartment + ",%' OR xgbm like '" + userDepartment + "')";
            //2025-3-13 修改逻辑，去除部门筛选增加岗位筛选
            baseBean.writeLog("current userid :" + user.getUID());
            String jobtile = getUserjobTitle(user.getUID());
            baseBean.writeLog("current usert jobtile:" + jobtile);
            checkSql = "SELECT id,wdmc,xgbm,gw FROM uf_cxwdmkbd WHERE id=" + check + " " +
                    " AND ( gw like '" + jobtile + ",%' OR gw like '%," + jobtile + "' OR gw like '%," + jobtile + ",%' OR gw like '" + jobtile + "')";
        }
        baseBean.writeLog("checkSql:" + checkSql);
        rs.executeQuery(checkSql, new Object[0]);
        if (rs.next()) {
            String id = rs.getString("id");
            RecordSet rs1 = new RecordSet();
            String sql = "SELECT id,mc FROM uf_nrmkbd WHERE szcd=" + menuId + " AND sfqy=0 ORDER BY sort ASC";
            rs1.executeQuery(sql, new Object[0]);

            while (rs1.next()) {
                Map tabMap = new HashMap();
                String tabId = rs1.getString("id");
                String mc = rs1.getString("mc");
                RecordSet rs2 = new RecordSet();
                String contentSql = "SELECT id,nrmc,fq,zn,tb FROM uf_nrmkbd_dt1  \n\tWHERE mainid= " + tabId + "\tAND sfqy=0  \tAND ( szwd like '" + id + ",%' OR szwd like '%," + id + "' OR szwd like '%," + id + ",%' OR szwd like '" + id + "') \n\tORDER BY sort ASC\n";
                rs2.executeQuery(contentSql, new Object[0]);
                (new BaseBean()).writeLog("contentSql:" + contentSql);
                List contentList = new LinkedList();

                while (rs2.next()) {
                    Map contentMap = new HashMap();
                    String tb = rs2.getString("tb");
                    Integer imagefileid = null;
                    if (StringUtils.isNotBlank(tb)) {
                        Integer imgId = Integer.valueOf(tb);
                        String imgurl = "select IMAGEFILEID from docimagefile where DOCID=" + imgId;
                        RecordSet rs3 = new RecordSet();
                        rs3.executeQuery(imgurl, new Object[0]);
                        if (rs3.next()) {
                            imagefileid = rs3.getInt("IMAGEFILEID");
                        }
                    }

                    String nrmc = rs2.getString("nrmc");
                    String fq = rs2.getString("fq");
                    String zn = rs2.getString("zn");
                    contentMap.put("title", nrmc);
                    contentMap.put("herf", fq);
                    contentMap.put("file", zn);
                    contentMap.put("icon", imagefileid == null ? "" : "/weaver/weaver.file.FileDownload?fileid=" + imagefileid);
                    contentList.add(contentMap);
                }

                tabMap.put("title", mc);
                tabMap.put("child", contentList);
                tabMap.put("viewcondition", tabId);
                returnList.add(tabMap);
            }
        }

        return returnList;
    }


    /**
     * 校验是否配置了岗位
     *
     * @param checkid
     * @return
     */
    private boolean checkHasJobtitle(String checkid) {
        RecordSet rs = new RecordSet();
        String sql = "select id from uf_cxwdmkbd where id =" + checkid + " and gw is not null";
        if (rs.executeQuery(sql)) {
            return rs.next();
        }
        return false;
    }


    /**
     * 校验是否配置了岗位
     *
     * @param userid
     * @return
     */
    private String getUserjobTitle(Integer userid) {
        RecordSet rs = new RecordSet();
        String sql = "select jobtitle from hrmresource where id =" + userid;
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                return Util.null2String(rs.getString("jobtitle"));
            }
        }
        return "";
    }
}
