/**
 *联系人卡片，移动电话输入框拦截
 */
ecodeSDK.overwritePropsFnQueueMapSet("WeaInput", {
    fn: (newProps) => {
        // 判断页面地址
        const {hash} = window.location
        if (hash.indexOf("#/main/crm/contacterView?") < 0) return;
        if (newProps.title && newProps.title.indexOf("移动电话") >= 0) {
            console.log("移动电话 WeaInput", newProps)
            const oldonBlur = newProps.onBlur; // 必须保留原有的点击事件
            newProps.onBlur = (...arg) => {
                if (typeof oldonBlur === "function") {
                    let phoneNumber = arg[0] || "";
                    if (checkPhoneNumber(phoneNumber)) {
                        oldonBlur(...arg); // 调用原保存事件
                    }
                }
            };
        }
    },
});


function checkPhoneNumber(phoneNumber) {
    const {Modal} = window.antd
    // 判断是否有值
    if (phoneNumber) {
        // 校验值是否为11位数字
        if (!/^\d{11}$/.test(phoneNumber)) {
            // 如果不是11位，提示报错
            Modal.warning({
                title: "提示",
                content: "联系人的移动电话: " + phoneNumber + "，不是11位号码，请检查！",
            });
            return false;
        }
    }
    return true;
}