/**
 * 按钮点击执行
 */
function execute(billid) {
    window.ecCom.WeaTools.createDialog({
        title: '客户已存在',
        url: "/spa/custom/static/index.html#/main/cs/app/436f9654706145109ab5a404b304d498_page",
        icon: "icon-coms-workflow",
        iconBgcolor: "#0079DE",
        style: {width: "350px", height: 180},
        callback: (customerid) => { // 数据通信
            console.log("回调的customerid:" + customerid);
            updateClue(billid, customerid);
        },
        onCancel: () => { // 关闭通信
        }
    }, undefined, (dialog) => {
        // 由于组件异步化可能导致第一次没有加载到组件，所以需要在回调中调用
        dialog.show();
    });
}

function updateClue(billid, customerid) {
    console.log("updateClue billid:" + billid);
    const {http} = window.GRSDK
    const {WeaLoadingGlobal} = window.ecCom
    const {message} = window.antd
    if (billid && customerid) {
        //调用接口
        let apiParam = {
            clueid: billid,
            customerid: customerid,
        }
        console.log("当前新建客户有线索id:" + billid + "，保存客户成功后，将当前客户回写到线索中")
        WeaLoadingGlobal.start({
            tip: "执行中..." // 自定义tip文字
        });
        http.postAC("/api/sd/crm/existCustomer2Clue", apiParam, (result) => {
            WeaLoadingGlobal.destroy(); // 销毁遮罩loading
            console.log("existCustomer2Clue result ", result);
            //成功
            if (result && result.status === true) {
                message.success("执行成功！", 2);
                ModeForm.reloadCard();

            } else {
                message.error("执行失败！" + JSON.stringify(result), 3)
            }
        });
    } else {
        message.error("缺失参数billid或customerid", 3)
    }
}

window.modext_xiansuo_kfycz = execute;