//读取config
const config = ecodeSDK.getCom("${appId}", "config");
const jzdsParamName = config.jzdsParamName; //竞争对手-查询条件字段id
const rqParamName = config.rqParamName; //日期-查询条件字段id
const query_customid = config.query_customid //建模查询id

const xsjhlxParamId = config.xsjhlxParamId //查询条件-字段id-销售机会类型

const api_winlost = "/api/sd/crmreport/winlost";

const api_getCountData = "/api/cube/search/getCountData"
const api_getGroupData = "/api/cube/search/getGroupData"
const api_getList = "/api/cube/search/getList"
const api_datas = "/api/ec/dev/table/datas"
const check_hash = "#/main/cube/search?customid=" + query_customid

let sdQueryFlag = false //查询接口是否是系统自动触发的，false时不查询数据出来

const {http, modext, time} = window.GRSDK

let runFlag = false; //是否是当前页面执行flag
let jzdsValue = "";//竞争对手-值
let rqValue = "" //日期-值
let xsjhlx_Value = "" //销售机会类型-值

/**
 * 拦截接口请求
 * 去除查询条件
 */
ecodeSDK.rewriteApiParamsQueueSet({
    fn: (url, method, params, type, _fetchParams) => {
        if (url.indexOf(api_getCountData) >= 0 ||
            url.indexOf(api_getGroupData) >= 0 ||
            url.indexOf(api_getList) >= 0
        ) {
            if (params.customid === query_customid) {
                runFlag = true;
                console.log("sdQueryFlag", sdQueryFlag)
                // 如果是手动触发的，则去除条件
                if (sdQueryFlag) {
                    // 删除对象中的某个属性
                    delete params[jzdsParamName];
                    delete params[rqParamName];
                    delete params[xsjhlxParamId];
                } else {
                    //系统触发的，加个年条件，这样查出来数据就是空的,这样就是为了保证必须点击查询才可以搜索数据
                    params[rqParamName] = "6,2024-04-10,2024-05-07";
                }
                return {
                    url: url, // 接口路径
                    method: method, // 请求类型
                    params: params, // 	请求参数
                }
            }
        }
        return {
            url: url, // 接口路径
            method: method, // 请求类型
            params: params, // 	请求参数
        };
    },
    desc: "赢单输单对比汇总报表",
});


/**
 * 执行查询
 */
function execute() {
    const {time} = window.GRSDK
    sdQueryFlag = true;
    //构建查询接口参数
    let param = {
        xsjd: config.xsjdParamValue //销售阶段，只查这俩阶段的
    }
    let searchParam = ModeList.getTopSearchDatas()

    //----竞争对手---
    jzdsValue = searchParam[jzdsParamName];
    if (jzdsValue) {
        param.jzds = jzdsValue;
    }
    //----日期---
    rqValue = searchParam[rqParamName];
    let firstValue = rqValue;
    if (rqValue && Array.isArray(rqValue) && rqValue.length && rqValue.length > 0) {
        firstValue = rqValue[0];
    }
    console.log("rqValue", rqValue)
    console.log("firstValue", firstValue)
    if (firstValue === "0") {
        //全部
        //全部不设置开始结束日期
    } else if (firstValue === "3") {
        //本月
        let dateArray = time.getMonthStartAndEnd();
        param.beginDate = dateArray[0];
        param.endDate = dateArray[1];
    } else if (firstValue === "71") {
        //下个月
        let dateArray = time.getNextMonthStartAndEnd();
        param.beginDate = dateArray[0];
        param.endDate = dateArray[1];
    } else if (firstValue === "7") {
        //上个月
        let dateArray = time.getLastMonthStartAndEnd();
        param.beginDate = dateArray[0];
        param.endDate = dateArray[1];
    } else if (firstValue === "4") {
        //本季
        let dateArray = time.getCurrentQuarterStartAndEnd();
        param.beginDate = dateArray[0];
        param.endDate = dateArray[1];
    } else if (firstValue === "5") {
        //本年
        let dateArray = time.getCurrentYearStartAndEnd();
        param.beginDate = dateArray[0];
        param.endDate = dateArray[1];
    } else if (firstValue === "6") {
        //指定日期范围
        //未选择开始结束日期，是全部
        //有数组，则是选了开始结束日期
        if (rqValue && Array.isArray(rqValue) && rqValue.length && rqValue.length === 3) {
            //指定日期范围
            //未选择开始结束日期
            param.beginDate = rqValue[1];
            param.endDate = rqValue[2];
        }
    }
    //----销售机会类型----
    xsjhlx_Value = searchParam[xsjhlxParamId];
    if (xsjhlx_Value) {
        param.xsjhlx = xsjhlx_Value;
    }

    console.log("queryParam", param)
    //请求接口获取数据
    window.top.ecCom.WeaLoadingGlobal.start({
        tip: "查询中..." // 自定义tip文字
    });
    http.postAC(api_winlost, param, function (result) {
        window.top.ecCom.WeaLoadingGlobal.destroy();
        if (result && result.status && result.status === true) {
            window.antd.message.success("查询成功！");
        } else {
            window.antd.message.error("查询失败！");
        }
        console.log("api_winlost result", result)
        ModeList.reloadTableAll();
        sdQueryFlag = false;
    });
}


//返回数据修改
//修改标题栏
ecodeSDK.rewriteApiDataQueueSet({
    fn: (url, params, data) => {
        // 判断接口
        if (url.indexOf(api_datas) >= 0 && runFlag) {
            //修改数据
            let datas = data.datas;
            if (datas && datas.length && datas.length > 0) {
                //将同比显示为百分比
                datas.forEach((item, index) => {
                    //胜率
                    let num = item.shenglv
                    item.shenglvspan = (num * 100).toFixed(2) + "%"
                })
            }
        }
        return data;
    },
});


ecodeSDK.overwritePropsFnQueueMapSet('WeaDateGroup', { //组件名
    fn: (newProps) => { //newProps代表组件参数
        //进行位置判断
        let hash = window.location.hash;
        if (hash.startsWith(check_hash)) {
            if (newProps.fieldid && newProps.fieldid === rqParamName) {
                newProps.datas = [
                    {
                        "name": "全部",
                        "value": "0"
                    },
                    {
                        "name": "本月",
                        "value": "3"
                    },
                    {
                        "name": "下个月",
                        "value": "71"
                    },
                    {
                        "name": "上个月",
                        "value": "7"
                    },
                    {
                        "name": "本季",
                        "value": "4"
                    },
                    {
                        "name": "本年",
                        "value": "5"
                    },
                    {
                        "name": "指定日期范围",
                        "value": "6"
                    }
                ]
            }
        }

    },
    order: 1, //排序字段，如果存在同一个页面复写了同一个组件，控制顺序时使用
    desc: '在这里写此复写的作用，在调试的时候方便查找'
});


modext.winlost_query = execute