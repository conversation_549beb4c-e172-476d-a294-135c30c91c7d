package com.engine.xxrz2.gyl.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.xxrz2.gyl.job.bean.AttendanceData;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @FileName AttendanceJob.java
 * @Description 鸿图-考勤数据同步
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/25
 */
@Getter
@Setter
public class AttendanceJob extends BaseCronJob {

    //---Job参数---
    /**
     * 是否只查昨日的数据 1为是，其他为否
     */
    private String onlyYesterday;
    /**
     * ESB事件名称，分页查询考勤
     */
    private String esbEventName;
    /**
     * 建模表名
     */
    private String moduleTableName;
    /**
     * 每次查询的分页数量
     */
    private String pageSize;

    //---Job参数---

    private BaseBean bb;


    private volatile Map<String, String> oaHrmMap;

    /**
     * 执行
     */
    @Override
    public void execute() {
        bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        String errorMsg = "";
        try {
            if (StringUtils.isNotBlank(esbEventName) && StringUtils.isNotBlank(moduleTableName)) {
                int moduleId = ModuleDataUtil.getModuleIdByName(moduleTableName);
                if (moduleId == -1) {
                    bb.writeLog("moduleTableName：" + moduleTableName + "未匹配到建模id");
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    JSONObject esbParam = new JSONObject();
                    esbParam.put("pageNum", "1");
                    esbParam.put("pageSize", pageSize);
                    String today = TimeUtil.getToday();
                    String yesterday = TimeUtil.dateAdd(today, -1);
                    String startTime, endTime;
                    //首先把当前OA的人员信息push到Map
                    pushOaHrm2Map();

                    //只查昨日的,设置ESB两个参数
                    if ("1".equals(Util.null2String(onlyYesterday))) {
                        startTime = yesterday + " 00:00:00";
                        endTime = yesterday + " 23:59:59";
                        // 将日期字符串解析为Date对象
                        Date startTimeDate = sdf.parse(startTime);
                        Date endTimeDate = sdf.parse(endTime);
                        // 获取毫秒级别的时间戳
                        long startTimeStamp = startTimeDate.getTime();
                        long endTimeStamp = endTimeDate.getTime();
                        esbParam.put("startTime", String.valueOf(startTimeStamp));
                        esbParam.put("endTime", String.valueOf(endTimeStamp));
                    }
                    List<AttendanceData> listData = new ArrayList<>();
                    int pageSizeInt = Integer.parseInt(pageSize);
                    errorMsg = process(listData, errorMsg, esbParam, 1, 0, pageSizeInt);
                    bb.writeLog("process errorMsg:" + errorMsg);
                    bb.writeLog("listData size:" + listData);
                    if (errorMsg.isEmpty()) {
                        //执行插入到建模
                        insert2Module(listData, moduleId, yesterday);
                    }
                }

            } else {
                bb.writeLog("esbEventName 或moduleTableName 参数为空");
            }
        } catch (Exception e) {
            bb.writeLog(SDUtil.getExceptionDetail(e));
        }
    }

    private void insert2Module(List<AttendanceData> list, int moduleId, String yesterday) {
        if (list.isEmpty()) {
            bb.writeLog("list为空，跳过插入");
        } else {
            //设置统一的同步时间
            String tbsj = TimeUtil.getCurrentTimeString();
            for (AttendanceData data : list) {
                data.setTbsj(tbsj);
            }
            RecordSet rs = new RecordSet();
            //只插昨日的
            if ("1".equals(Util.null2String(onlyYesterday))) {
                //先删除昨日的数据
                if (rs.executeUpdate("delete from " + moduleTableName + " where oarq = ?", yesterday)) {
                    doInsert2Module(list, moduleId);
                } else {
                    bb.writeLog("删除昨日" + yesterday + "建模数据出错：" + rs.getExceptionMsg());
                }
            } else {
                //全量
                if (rs.executeUpdate("TRUNCATE TABLE " + moduleTableName)) {
                    doInsert2Module(list, moduleId);
                } else {
                    bb.writeLog("删除所有建模数据出错：" + rs.getExceptionMsg());
                }
            }
        }
    }

    private void doInsert2Module(List<AttendanceData> list, int moduleId) {
        List<String> insertFields = new ArrayList<>();
        Object fieldValue;
        try {
            Class<?> beanClass = AttendanceData.class;
            Field[] fields = beanClass.getDeclaredFields();
            for (Field field : fields) {
                insertFields.add(field.getName());
            }
            List<List<Object>> values = new ArrayList<>();
            List<Object> eachValue;
            for (AttendanceData eachData : list) {
                eachValue = new ArrayList<>();
                for (Field field : fields) {
                    //设置可访问私有字段
                    field.setAccessible(true);
                    //获取字段值
                    fieldValue = field.get(eachData);
                    eachValue.add(fieldValue);
                }
                values.add(eachValue);
            }

            ModuleInsertBean insertBean = new ModuleInsertBean();
            insertBean.setTableName(moduleTableName)
                    .setFields(insertFields)
                    .setValues(values)
                    .setCreatorId(1)
                    .setModuleId(moduleId);
            ModuleDataUtil.insertAc(insertBean);
        } catch (Exception e) {
            bb.writeLog("doInsert2Module 异常：" + SDUtil.getExceptionDetail(e));
        }

    }

    /**
     * 分页查询，递归
     *
     * @param errorMsg
     * @param esbParam
     * @param attempt
     * @return
     */
    private String process(List<AttendanceData> listData, String errorMsg, JSONObject esbParam, int attempt, int executedSize, int pageSizeInt) {
        bb.writeLog("第 " + attempt + " 次执行process");
        JSONObject data;
        int total;
        esbParam.put("pageNum", String.valueOf(attempt));
        try {
            EsbEventResult er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
            if (er.isSuccess()) {
                data = er.getData().getJSONObject("data");
                JSONArray list = data.getJSONArray("list");
                if (!list.isEmpty()) {
                    listData.addAll(pushList(list));
                }
                //判断是否需要递归执行下一次
                executedSize += pageSizeInt;
                total = data.getIntValue("total");
                if (total > executedSize) {
                    process(listData, errorMsg, esbParam, attempt + 1, executedSize, pageSizeInt);
                } else {
                    return "";
                }
            } else {
                errorMsg = er.getErroMsg();
            }
        } catch (Exception e) {
            errorMsg = "processJob 异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    private List<AttendanceData> pushList(JSONArray list) {
        List<AttendanceData> result = new ArrayList<>();
        JSONObject jo;
        JSONArray attTimeResult;
        for (int i = 0; i < list.size(); i++) {
            jo = list.getJSONObject(i);
            attTimeResult = jo.getJSONArray("attTimeResult");
            //根据attTimeResult插入多条数据
            addListByAttTimeResult(attTimeResult, jo, result);
        }
        return result;
    }

    private String parseKaoqinDate(String timestampString) {
        String result = "";
        try {
            long timestampMillis = Long.parseLong(timestampString); // 将字符串解析为长整数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            // 将时间戳转换为日期字符串
            result = sdf.format(new Date(timestampMillis));
        } catch (Exception e) {
            bb.writeLog("parseKaoqinDate异常:" + SDUtil.getExceptionDetail(e));
        }
        return result;
    }

    /**
     * 获取上班打卡时间eg:09:00:00，需要
     *
     * @return
     */
    private void addListByAttTimeResult(JSONArray attTimeResult, JSONObject eachList, List<AttendanceData> list) {
        JSONObject jo;
        String inPunch, outPunch;
        AttendanceData attendanceData;
        try {
            String personName = Util.null2String(eachList.get("personName"));
            String attendanceDate = Util.null2String(eachList.get("attendanceDate"));
            String uuid = Util.null2String(eachList.get("uuid"));
            String status = Util.null2String(eachList.get("status"));
            String personCode = Util.null2String(eachList.get("personCode"));

            for (int i = 0; i < attTimeResult.size(); i++) {
                jo = attTimeResult.getJSONObject(i);
                inPunch = Util.null2String(jo.get("inPunch"));
                outPunch = Util.null2String(jo.get("outPunch"));

                if (!inPunch.isEmpty() && !"--".equals(inPunch)) {
                    attendanceData = new AttendanceData();
                    //转换日期时间戳为格式化日期
                    attendanceData.setUuid(uuid)
                            .setStatus(status)
                            .setPersonCode(personCode)
                            .setPersonName(personName)
                            .setAttendanceDate(attendanceDate)
                            .setOarq(parseKaoqinDate(attendanceDate))
                            .setKqsj(inPunch)
                            .setKqlx("0");
                    //判断是否人员名称是是否匹配OA人员
                    if (oaHrmMap.containsKey(personName)) {
                        attendanceData.setOalogin(oaHrmMap.get(personName));
                    }
                    list.add(attendanceData);
                }
                if (!outPunch.isEmpty() && !"--".equals(outPunch)) {
                    attendanceData = new AttendanceData();
                    //转换日期时间戳为格式化日期
                    attendanceData.setUuid(uuid)
                            .setStatus(status)
                            .setPersonCode(personCode)
                            .setPersonName(personName)
                            .setAttendanceDate(attendanceDate)
                            .setOarq(parseKaoqinDate(attendanceDate))
                            .setKqsj(outPunch)
                            .setKqlx("1");
                    //判断是否人员名称是是否匹配OA人员
                    if (oaHrmMap.containsKey(personName)) {
                        attendanceData.setOalogin(oaHrmMap.get(personName));
                    }
                    list.add(attendanceData);
                }
            }
        } catch (Exception e) {
            bb.writeLog("addListByAttTimeResult 异常：" + SDUtil.getExceptionDetail(e));
        }

//        //取最后一个对象
//        if (!attTimeResult.isEmpty()) {
//            JSONObject finalObj = attTimeResult.getJSONObject(attTimeResult.size() - 1);
//            return Util.null2String(finalObj.get("inPunch"));
//        }
    }

    /**
     * 获取上班打卡时间eg:09:00:00，需要
     *
     * @return
     */
    private String getKaoqinTime(JSONArray attTimeResult) {
        //取最后一个对象
        if (!attTimeResult.isEmpty()) {
            JSONObject finalObj = attTimeResult.getJSONObject(attTimeResult.size() - 1);
            return Util.null2String(finalObj.get("inPunch"));
        }
        return "";
    }

    private void pushOaHrm2Map() {
        oaHrmMap = new HashMap<>();
        RecordSet rs = new RecordSet();
        String sql = "select lastname,loginid from hrmresource ";
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                if (!Util.null2String(rs.getString("lastname")).isEmpty() && !Util.null2String(rs.getString("loginid")).isEmpty()) {
                    oaHrmMap.put(Util.null2String(rs.getString("lastname")), Util.null2String(rs.getString("loginid")));
                }
            }
        }
    }

}
