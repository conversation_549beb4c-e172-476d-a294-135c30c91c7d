package com.engine.xxrz2.gyl.job.bean;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)//可以使用链式set
public class AttendanceData {
    /**
     * 每个考勤数据的uuid
     */
    private String uuid;
    /**
     * 状态
     */
    private String status;
    /**
     * 人员工号
     */
    private String personCode;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 考勤日期 时间戳格式 13位 毫秒
     */
    private String attendanceDate;
    /**
     * 签到时间 09:00:01 格式 弃用
     */
    private String inPunch;
    /**
     * 匹配到的OA登录账号
     */
    private String oalogin;
    /**
     * OA中的考勤日期 yyyy-MM-dd格式
     */
    private String oarq;
    /**
     * 同步时间
     */
    private String tbsj;

    /**
     * 考勤时间 09:00:01 格式，
     */
    private String kqsj;
    /**
     * 考勤类型 0inPunch 1outPunch
     */
    private String kqlx;

}
