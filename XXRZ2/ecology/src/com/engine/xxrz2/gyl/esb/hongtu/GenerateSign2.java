package com.engine.xxrz2.gyl.esb.hongtu;

import com.engine.parent.common.util.SDUtil;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @FileName GenerateSign.java
 * @Description 生成签名
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/25
 */
public class GenerateSign2 {

    /**
     * 生成签名和时间戳
     *
     * @param params
     * @return
     */
    public Map<String, Object> execute(Map<String, Object> params) {
        BaseBean bb = new BaseBean();
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            bb.writeLog("params：" + params);
            //6位随机码
            String cnonce = Util.null2String(params.get("cnonce"));
            // 要请求的url,看要请求的接⼝说明
            String url = Util.null2String(params.get("url"));
            // appkey,固定，
            String cappKey = Util.null2String(params.get("cappKey"));
            // 秘钥，固定
            String secret = Util.null2String(params.get("secret"));
            //当前页码
            Integer pageNum = Integer.parseInt(Util.null2String(params.get("pageNum")));
            //分页数
            Integer pageSize = Integer.parseInt(Util.null2String(params.get("pageSize")));
            //开始时间
            String startTime = Util.null2String(params.get("startTime"));
            //结束时间
            String endTime = Util.null2String(params.get("endTime"));
            //personTag
            String personTag = Util.null2String(params.get("personTag"));
            //上面参数为必填
            if (cnonce.isEmpty() ||
                    url.isEmpty() ||
                    cappKey.isEmpty() ||
                    secret.isEmpty() ||
                    personTag.isEmpty()) {
                errorMsg = "缺失必要参数";
            } else {
                // 时间戳，⽤户⾃⼰⽣成
                String ctimestamp = String.valueOf(System.currentTimeMillis());
                result.put("ctimestamp", ctimestamp);
                //请求参数，form表单⽅式，鸿图⼤部分接⼝都是body传参，故这⾥⼤部分都是""，如果有参数，查看⽂档说明
                String requestParam = "";
                // 请求⽅式,看要请求的接⼝说明
                String method = "POST";
                // 构建请求体，看要请求的接⼝说明，这里必须使用顺序map
                Map<String, Object> bodyParam = new LinkedHashMap<>();
                bodyParam.put("pageNum", pageNum);
                bodyParam.put("pageSize", pageSize);
                if (!startTime.isEmpty()) {
                    bodyParam.put("startTime", startTime);
                }
                if (!endTime.isEmpty()) {
                    bodyParam.put("endTime", endTime);
                }
                bodyParam.put("personTag", personTag);
                bb.writeLog("bodyParam:" + bodyParam);
                // 计算签名
                String csign = SignUtlis.sign(ctimestamp, cnonce, bodyParam, requestParam,
                        url, method, cappKey, secret);
                result.put("csign", csign);
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        return result;
    }

    public static void main(String[] args) {
        // 时间戳，⽤户⾃⼰⽣成
        String ctimestamp = String.valueOf(System.currentTimeMillis());
        ctimestamp = "1695609733253";
        System.out.println(ctimestamp);

        // 6位随机码，⽤户⾃⼰⽣成
        String cnonce = "123456";
        //请求参数，form表单⽅式，鸿图⼤部分接⼝都是body传参，故这⾥⼤部分都是""，如果有参数，查看⽂档说明
        String requestParam = "";
        // 要请求的url,看要请求的接⼝说明
        String url = "/v1/api/attendance/record/list";
        // 请求⽅式,看要请求的接⼝说明
        String method = "POST";
        // appkey,固定，
        String cappKey = "appkey1";
        // 秘钥，固定
        String secret = "sdfajk3242324fa!djq7";

        Map<String, Object> orderedMap = new LinkedHashMap<>();
        orderedMap.put("pageNum", 1);
        orderedMap.put("pageSize", 10);
//        orderedMap.put("startTime", "1695484800000");
//        orderedMap.put("endTime", "1695571199000");
        // 构建请求体，看要请求的接⼝说明

        // 计算签名
        String csign = SignUtlis.sign(ctimestamp, cnonce, orderedMap, requestParam,
                url, method, cappKey, secret);
        System.out.println(csign);
    }

}

