package com.engine.xxrz2.gyl.esb.hongtu;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

public class SignUtlis {
    private static final String SIGN_FORMAT = "%s-%s-%s-%s-%s-%s-%s-%s";

    /**
     * 签名算法⽣成卡密昂
     *
     * @param ctimestamp
     * @param cnonce
     * @param requestBody  请求体对象
     * @param requestParam
     * @param url
     * @param method
     * @param cappKey      通⾏记录回调
     * @param secret
     * @return
     */

    public static String sign(String ctimestamp, String cnonce, Object requestBody,
                              String requestParam, String url, String method, String cappKey, String secret) {
        String requestBodyMd5 = "";
        if (requestBody != null) {
            String requestBodyJson = JSONObject.toJSONString(requestBody);
            if (StringUtils.isNotEmpty(requestBodyJson)) {
                requestBodyMd5 = DigestUtil.encryptMd5(requestBodyJson);
            }
        }
        String signStr = String.format(SIGN_FORMAT, url, method, requestParam, requestBodyMd5, secret, ctimestamp, cnonce, cappKey);
        String sign = DigestUtil.encryptMd5(signStr);
        return sign;
    }
}

