package com.utils;

import com.alibaba.fastjson.JSONObject;
import okhttp3.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/7/8-18:13
 * @description : REST请求工具类
 */
public class HttpUtil {
    /**
     *  带数据头传输的http方法 重载 postData方法
     * @param url
     * @param head
     * @param params
     * @return
     */
    public static ApiResult postData(String url,Map<String, String> head, Map<String, Object> params) {
        ApiResult res = new ApiResult();
        res.setSuccessFlag(false);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().
                    connectTimeout(60, TimeUnit.SECONDS)
                    .writeTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            String aa = JSONObject.toJSONString(params, true);
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(params, true));
            Request.Builder builder = new Request.Builder();
            for (Map.Entry<String, String> entry : head.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
            Request request = builder
                    .url(url)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            JSONObject jo = JSONObject.parseObject(response.body().string());
            if (response.code() == ApiResultCst.API_SUCCESS_CODE) {
                String code = jo.getString("state");
                if (ApiResultCst.API_RESULT_SUCCESS.equals(code)) {
                    res.setSuccessFlag(true);
                    res.setResMsg(jo.getString("msg"));
                }else{
                    HashMap<String,String> stateMap = ApiResultCst.getStateMap();
                    if(stateMap.containsKey(code)){
                        res.setResMsg(stateMap.get(code));
                    }else{
                        res.setResMsg(jo.getString("msg"));
                    }
                }
                res.setData(jo.getString("data"));
            } else {
                res.setResMsg("调用失败，失败Code:" + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.setResMsg("执行异常：" + e.getMessage());
        }
        return res;
    }

    /**
     * 发送post接口,效果等同postman发送raw格式数据
     *
     * @param url
     * @param params
     * @return
     */
    public static ApiResult postData(String url, Map<String, Object> params) {
        ApiResult res = new ApiResult();
        res.setSuccessFlag(false);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().
                    connectTimeout(60, TimeUnit.SECONDS)
                    .writeTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            String aa = JSONObject.toJSONString(params, true);
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(params, true));
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            JSONObject jo = JSONObject.parseObject(response.body().string());
            if (response.code() == ApiResultCst.API_SUCCESS_CODE) {
                String code = jo.getString("state");
                if (ApiResultCst.API_RESULT_SUCCESS.equals(code)) {
                    res.setSuccessFlag(true);
                    res.setResMsg(jo.getString("msg"));
                }else{
                    HashMap<String,String> stateMap = ApiResultCst.getStateMap();
                    if(stateMap.containsKey(code)){
                        res.setResMsg(stateMap.get(code));
                    }else{
                        res.setResMsg(jo.getString("msg"));
                    }
                }
                res.setData(jo.getString("data"));
            } else {
                res.setResMsg("调用失败，失败Code:" + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.setResMsg("执行异常：" + e.getMessage());
        }
        return res;
    }

    /**
     * 发送get接口,效果等同postman发送raw格式数据
     *
     * @param url
     * @param params
     * @return
     */
    public static ApiResult getDataWithGBK(String url, Map<String, Object> params) {
        ApiResult res = new ApiResult();
        res.setSuccessFlag(false);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .method("GET", null)
                    .addHeader("Content-Type", "application/json;charset=utf-8")
                    .build();
            Response response = client.newCall(request).execute();
            String gbkValue = new String(response.body().bytes(), "GBK");
            JSONObject jo = JSONObject.parseObject(gbkValue);
            if (response.code() == ApiResultCst.API_SUCCESS_CODE) {
                res.setSuccessFlag(true);
                res.setData(jo);
            } else {
                res.setResMsg("调用失败，失败Code:" + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.setResMsg("执行异常：" + e.getMessage());
        }
        return res;
    }
}
