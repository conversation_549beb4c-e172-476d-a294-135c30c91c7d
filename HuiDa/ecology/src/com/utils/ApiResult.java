package com.utils;

/**
 * <AUTHOR> <PERSON>
 * @CreateTime : 2021/7/9-9:11
 * @description : 接口返回类
 */
public class ApiResult {
    /**
     * 返回成功失败标识 true 成功，false 失败
     */

    private boolean successFlag;
    /**
     * 返回信息
     */
    private String resMsg;
    /**
     * 返回的数据
     */
    private Object data;

    public boolean isSuccessFlag() {
        return successFlag;
    }

    public void setSuccessFlag(boolean successFlag) {
        this.successFlag = successFlag;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
