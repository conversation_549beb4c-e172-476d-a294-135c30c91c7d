package com.engine.job;

import com.engine.util.spdb.CallSPDBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName SPDBhuidanDownloadJob
 * @Description 浦发银行回单下载job 自行执行
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/16
 */
@Getter
@Setter
public class SPDBhuidanDownloadJob extends BaseCronJob {
    //加密地址，bisafe签名服务地址和端口
    private String signUrl;
    //验签地址，bisafe签名服务地址和端口
    private String verifyUrl;
    //目标地址，bisafe所在地址
    private String host;
    //bisafe nchttp服务监听端口
    private String port;
    //目标端口，bisafe文件传输服务监听端口
    private String downloadPort;
    //下载文件保存路径
    private String downloadFilePath;
    //流程表单名，多个逗号隔开
    private String formtables;
    //摘要代码字段
    private String remarkField;
    //多文档字段
    private String docField;
    //账号
    private String acctNo;
    //客户号
    private String masterId;
    //接口日志建模id
    private String logModuleId;
    //wj12接口下载的文件存到的文档目录id
    private String catId;
    //默认的查询前多少天的数据,比如提前7天，填-7
    private String beginBeforeDays;
    //默认的查询前多少天的数据，比如结束日期查到昨天，填-1，今天填0
    private String endBeforeDays;

    //基类
    private static final BaseBean bb = new BaseBean();

    /**
     * step1-使用8924 接口查询指定日期的账户明细（默认查询前7天的数据，如果指定开始结束日期，则查指定日期的数据）
     * step2-根据8924接口匹配流程上的摘要代码字段
     * step3-使用DY47 T＋0 接口获取回单编号
     * step4-做完DY47查询完回单流水号后，需要5分钟后再进行回单的下载步骤
     * step5-使用WJ12 接口获取回单文件
     * step6-回单文件反写到对应摘要代码字段的流程上
     */
    @Override
    public void execute() {
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            int beginBeforeDaysInt = Integer.parseInt(beginBeforeDays);
            int endBeforeDaysInt = Integer.parseInt(endBeforeDays);
            String beginBeforeDate = TimeUtil.dateAdd(TimeUtil.getToday(), beginBeforeDaysInt).replace("-", "");
            String endBeforeDate = TimeUtil.dateAdd(TimeUtil.getToday(), endBeforeDaysInt).replace("-", "");

            bb.writeLog("beginBeforeDaysInt: " + beginBeforeDaysInt);
            bb.writeLog("endBeforeDaysInt: " + endBeforeDaysInt);
            bb.writeLog("beginBeforeDate: " + beginBeforeDate);
            bb.writeLog("endBeforeDate: " + endBeforeDate);

            bb.writeLog("最终beginDate: " + beginBeforeDate);
            bb.writeLog("最终endDate: " + endBeforeDate);
            if (StringUtils.isNotBlank(beginBeforeDate) && StringUtils.isNotBlank(endBeforeDate)) {
                //执行
                CallSPDBUtil.executeHuidanDownload(
                        signUrl,
                        verifyUrl,
                        host,
                        port,
                        downloadPort,
                        downloadFilePath,
                        formtables,
                        remarkField,
                        docField,
                        acctNo,
                        masterId,
                        logModuleId,
                        catId,
                        "-1",
                        beginBeforeDate,
                        endBeforeDate);
            }
        } catch (Exception e) {
            bb.writeLog(this.getClass().getName() + "异常：" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

}
