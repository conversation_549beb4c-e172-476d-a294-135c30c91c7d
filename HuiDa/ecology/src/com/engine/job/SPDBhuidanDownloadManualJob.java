package com.engine.job;

import com.engine.util.spdb.CallSPDBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName SPDBhuidanDownloadManualJob
 * @Description 浦发银行回单下载job 手动执行，指定日期
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/16
 */
@Getter
@Setter
public class SPDBhuidanDownloadManualJob extends BaseCronJob {
    //加密地址，bisafe签名服务地址和端口
    private String signUrl;
    //验签地址，bisafe签名服务地址和端口
    private String verifyUrl;
    //目标地址，bisafe所在地址
    private String host;
    //bisafe nchttp服务监听端口
    private String port;
    //目标端口，bisafe文件传输服务监听端口
    private String downloadPort;
    //下载文件保存路径
    private String downloadFilePath;
    //流程表单名，多个逗号隔开
    private String formtables;
    // 摘要代码字段
    private String remarkField;
    //多文档字段
    private String docField;
    //账号
    private String acctNo;
    //客户号
    private String masterId;
    //接口日志建模id
    private String logModuleId;
    //wj12接口下载的文件存到的文档目录id
    private String catId;
    //指定某一个requestid，不指定填-1
    private String specificRequestid;
    //指定开始日期配置yyyyMMdd，不填取提前天数的配置
    private String beginDate;
    //指定结束日期配置yyyyMMdd，不填取提前天数的配置
    private String endDate;

    //基类
    private static final BaseBean bb = new BaseBean();

    /**
     * step1-使用8924 接口查询指定日期的账户明细（默认查询前7天的数据，如果指定开始结束日期，则查指定日期的数据）
     * step2-根据8924接口匹配流程上的摘要代码字段
     * step3-使用DY47 T＋0 接口获取回单编号
     * step4-做完DY47查询完回单流水号后，需要5分钟后再进行回单的下载步骤
     * step5-使用WJ12 接口获取回单文件
     * step6-回单文件反写到对应摘要代码字段的流程上
     */
    @Override
    public void execute() {
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            bb.writeLog("指定beginDate: " + beginDate);
            bb.writeLog("指定endDate: " + endDate);
            if (StringUtils.isNotBlank(beginDate) && StringUtils.isNotBlank(endDate)) {
                //执行
                CallSPDBUtil.executeHuidanDownload(
                        signUrl,
                        verifyUrl,
                        host,
                        port,
                        downloadPort,
                        downloadFilePath,
                        formtables,
                        remarkField,
                        docField,
                        acctNo,
                        masterId,
                        logModuleId,
                        catId,
                        specificRequestid,
                        beginDate,
                        endDate);
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog(this.getClass().getName() + "异常：" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

}
