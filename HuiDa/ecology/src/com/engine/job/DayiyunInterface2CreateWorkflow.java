package com.engine.job;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.workflow.util.WorkFlowUtil;
import com.utils.ApiResult;
import com.utils.ApiResultCst;
import com.utils.HttpUtil;
import com.utils.Token;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;


public class DayiyunInterface2CreateWorkflow extends BaseCronJob {

    private BaseBean bb;

    private String corpCode;

    private String userName;

    private String password;

    private String tokenurl;

    private String url;

    private String returnurl;

    private String workflowid;

    private String remark;

    private String requestName;

    private String isNextFlow;

    private JSONArray colmap;

    private JSONArray educationBackground;

    private JSONArray projectExperience;

    private JSONArray familyRelationship;


    private JSONArray baseinfoMap;

    private String callbackflag;

    private String applyId;

    private void initBaseBean() {
        bb = new BaseBean();
        String colmapstring =  bb.getPropValue("props4baida","personalInformation");
        if(!"".equals(colmapstring)&&colmapstring!=null){
            colmap = JSONObject.parseArray(colmapstring);
        }
        String colmapstringedu =  bb.getPropValue("props4baida","educationBackground");
        if(!"".equals(colmapstringedu)&&colmapstringedu!=null){
            educationBackground = JSONObject.parseArray(colmapstringedu);
        }
        String colmapstringproject =  bb.getPropValue("props4baida","workingExperience");
        if(!"".equals(colmapstringproject)&&colmapstringproject!=null){
            projectExperience = JSONObject.parseArray(colmapstringproject);
        }
        String colmapstringpersonal =  bb.getPropValue("props4baida","familyRelationship");
        if(!"".equals(colmapstringpersonal)&&colmapstringpersonal!=null){
            familyRelationship = JSONObject.parseArray(colmapstringpersonal);
        }

        String baseinfo =  bb.getPropValue("props4baida","baseinfo");
        if(!"".equals(baseinfo)&&baseinfo!=null){
            baseinfoMap = JSONObject.parseArray(baseinfo);
        }
    }

    @Override
    public void execute() {
        try {
        initBaseBean();
            }catch (Exception e){
            bb.writeLog("初始化出错");
            return;
        }
        bb.writeLog(this.getClass().getName() + " --- START");
        try {
            Token token = this.getToken();
            if(!"0".equals(token.getState())){
                return;
            }
            HashMap<String,String> head = new HashMap<>();
            HashMap<String,Object> body = new HashMap<>();
            head.put("Authorization","Bearer "+token.getTokenstring());
            body.put("rowSize","100");
            body.put("cType","2");
            body.put("vType","0");
            ApiResult apiResult= HttpUtil.postData(url,head,body);
            bb.writeLog("获取到JSON为  "+apiResult.getData());
            if(apiResult.isSuccessFlag()){
                JSONObject jsonObject = JSONObject.parseObject(apiResult.getData().toString());
                JSONArray array = jsonObject.getJSONArray("resume");
                for (int i = 0; i < array.size(); i++) {
                    JSONObject person = array.getJSONObject(i);
                    cn.hutool.json.JSONArray mainjsonarray = new cn.hutool.json.JSONArray();
                    cn.hutool.json.JSONObject baseobject = new cn.hutool.json.JSONObject();
                    baseobject.put("workflowId",workflowid);
                    baseobject.put("remark",remark);
                    baseobject.put("requestName",requestName);
                    baseobject.put("isNextFlow",isNextFlow);
                    JSONObject pes = person.getJSONObject("resumeContent");

                    JSONObject personnal = pes.getJSONObject("personalInformation");
                    for (int j = 0; j <colmap.size(); j++) {
                        cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                        JSONObject maincol = colmap.getJSONObject(j);
                        String value = maincol.getString("value");
                        temp.put("name",maincol.getString("name"));
                        temp.put("value",personnal.getString(value));
                        temp.put("htmlType",maincol.getString("htmlType"));
                        temp.put("type",maincol.getString("type"));
                        mainjsonarray.add(temp);
                    }

                    // base表新增逻辑
                    JSONObject baseinfo = person.getJSONObject("basicInfo");
                    for (int j = 0; j <baseinfoMap.size(); j++) {
                        cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                        JSONObject maincol = baseinfoMap.getJSONObject(j);
                        String value = maincol.getString("value");
                        temp.put("name",maincol.getString("name"));
                        temp.put("value",baseinfo.getString(value));
                        temp.put("htmlType",maincol.getString("htmlType"));
                        temp.put("type",maincol.getString("type"));
                        mainjsonarray.add(temp);
                    }
                    //applyid获取
                    cn.hutool.json.JSONObject applyIdCol = new cn.hutool.json.JSONObject();
                    applyIdCol.put("name",applyId);
                    applyIdCol.put("value",person.getString("applyId"));
                    applyIdCol.put("htmlType","1");
                    applyIdCol.put("type","1");
                    mainjsonarray.add(applyIdCol);

                    cn.hutool.json.JSONArray educationBackgroundIn = new cn.hutool.json.JSONArray();
                    JSONArray eduarray = pes.getJSONArray("educationBackground");
                    for (int j = 0; j < eduarray.size(); j++) {
                        cn.hutool.json.JSONArray tempArray = new cn.hutool.json.JSONArray();
                        JSONObject tempObject = eduarray.getJSONObject(j);
                        for (int k = 0; k < educationBackground.size(); k++) {
                            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                            JSONObject maincol = educationBackground.getJSONObject(k);
                            String value = maincol.getString("value");
                            temp.put("name",maincol.getString("name"));
                            temp.put("value",tempObject.getString(value));
                            temp.put("htmlType",maincol.getString("htmlType"));
                            temp.put("type",maincol.getString("type"));
                            tempArray.add(temp);
                        }
                        educationBackgroundIn.add(tempArray);
                    }

                    cn.hutool.json.JSONArray projectExperienceIn = new cn.hutool.json.JSONArray();
                    JSONArray projectarray = pes.getJSONArray("workingExperience");
                    for (int j = 0; j < projectarray.size(); j++) {
                        JSONObject tempObject = projectarray.getJSONObject(j);
                        cn.hutool.json.JSONArray tempArray = new cn.hutool.json.JSONArray();
                        for (int k = 0; k < projectExperience.size(); k++) {
                            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                            JSONObject maincol = projectExperience.getJSONObject(k);
                            String value = maincol.getString("value");
                            temp.put("name",maincol.getString("name"));
                            temp.put("value",tempObject.getString(value));
                            temp.put("htmlType",maincol.getString("htmlType"));
                            temp.put("type",maincol.getString("type"));
                            tempArray.add(temp);
                        }
                        projectExperienceIn.add(tempArray);
                    }

                    cn.hutool.json.JSONArray personalInformationIn = new cn.hutool.json.JSONArray();
                    JSONArray personal = pes.getJSONArray("familyRelationship");
                    for (int j = 0; j < personal.size(); j++) {
                        JSONObject tempObject = personal.getJSONObject(j);
                        cn.hutool.json.JSONArray tempArray = new cn.hutool.json.JSONArray();
                        for (int k = 0; k < familyRelationship.size(); k++) {
                            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                            JSONObject maincol = familyRelationship.getJSONObject(k);
                            String value = maincol.getString("value");
                            temp.put("name",maincol.getString("name"));
                            temp.put("value",tempObject.getString(value));
                            temp.put("htmlType",maincol.getString("htmlType"));
                            temp.put("type",maincol.getString("type"));
                            tempArray.add(temp);
                        }
                        personalInformationIn.add(tempArray);
                    }
                    bb.writeLog("插入所有字段 " + mainjsonarray.toString()+"------");
                    bb.writeLog("插入所有字段 " + educationBackgroundIn.toString()+"------");
                    bb.writeLog("插入所有字段 " + projectExperienceIn.toString()+"------");
                    bb.writeLog("插入所有字段 " + personalInformationIn.toString()+"------");
                    String ans = WorkFlowUtil.createWorkflowRequest(
                            Integer.parseInt(bb.getPropValue("props4baida","create")),
                            mainjsonarray,
                            baseobject,educationBackgroundIn,projectExperienceIn,personalInformationIn);
                    bb.writeLog("已读返回结果="+ans);
                    if(Integer.parseInt(ans)>0&& "1".equals(callbackflag)){
                        HashMap<String,String> headend = new HashMap<>();
                        HashMap<String,Object> bodyend = new HashMap<>();
                        headend.put("Authorization","Bearer "+token.getTokenstring());
                        bodyend.put("applyIds",person.getString("applyId"));
                        ApiResult recall= HttpUtil.postData(returnurl,headend,bodyend);
                        if(recall.isSuccessFlag()){
                            bb.writeLog("返回成功");
                        }else {
                            bb.writeLog("已读返回结果="+recall.getResMsg());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + " --- END");
    }

    public Token getToken() throws Exception{
        Map<String, Object> map = new HashMap<>();
        map.put("corpCode",corpCode);
        map.put("userName",userName);
        map.put("password",password);
        ApiResult apiResult = HttpUtil.postData(tokenurl,map);
        if(apiResult.isSuccessFlag()&&apiResult.getData()!=null){
            JSONObject jsonObject = JSONObject.parseObject(apiResult.getData().toString());
            return new Token(ApiResultCst.API_RESULT_SUCCESS,jsonObject.getString("token"));
        }else{
            bb.writeLog("interface catch exception：" + apiResult.getResMsg());
            return new Token("-1","");
        }
    }


    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCallbackflag() {
        return callbackflag;
    }

    public void setCallbackflag(String callbackflag) {
        this.callbackflag = callbackflag;
    }

    public BaseBean getBb() {
        return bb;
    }

    public void setBb(BaseBean bb) {
        this.bb = bb;
    }

    public String getCorpCode() {
        return corpCode;
    }

    public void setCorpCode(String corpCode) {
        this.corpCode = corpCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getTokenurl() {
        return tokenurl;
    }

    public void setTokenurl(String tokenurl) {
        this.tokenurl = tokenurl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getWorkflowid() {
        return workflowid;
    }

    public void setWorkflowid(String workflowid) {
        this.workflowid = workflowid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRequestName() {
        return requestName;
    }

    public void setRequestName(String requestName) {
        this.requestName = requestName;
    }

    public String getIsNextFlow() {
        return isNextFlow;
    }

    public void setIsNextFlow(String isNextFlow) {
        this.isNextFlow = isNextFlow;
    }

    public String getReturnurl() {
        return returnurl;
    }

    public void setReturnurl(String returnurl) {
        this.returnurl = returnurl;
    }
}
