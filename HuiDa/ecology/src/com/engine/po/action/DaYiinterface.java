package com.engine.po.action;

import com.alibaba.fastjson.JSONObject;
import com.utils.ApiResult;
import com.utils.ApiResultCst;
import com.utils.HttpUtil;
import com.utils.Token;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;
import java.util.Map;

public class DaYiinterface extends BaseBean implements Action {

    private String corpCode;

    private String userName;

    private String password;

    private String tokenurl;

    private String url;

    private String applyIds;

    private String applyStatus;

    private String remark;

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        Token token = null;
        try {
            token = this.getToken();
        } catch (Exception e) {
            rm.setMessagecontent("获取token出错");
            return Action.FAILURE_AND_CONTINUE;
        }
        if(!"0".equals(token.getState())){
            rm.setMessagecontent("token状态出错");
            return Action.FAILURE_AND_CONTINUE;
        }
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> mainmap = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            mainmap.put(str, value);
        }
        HashMap<String,String> head = new HashMap<>();
        HashMap<String,Object> body = new HashMap<>();
        head.put("Authorization","Bearer "+token.getTokenstring());
        body.put("applyIds",mainmap.get(applyIds));
        body.put("applyStatus",mainmap.get(applyStatus));
        body.put("remark",mainmap.get(remark));
        ApiResult apiResult= HttpUtil.postData(url,head,body);
        if(apiResult.isSuccessFlag()){
            return Action.SUCCESS;
        }
        rm.setMessagecontent("返回状态码不正确--"+apiResult.getResMsg());
        return Action.FAILURE_AND_CONTINUE;
    }
    public Token getToken() throws Exception{
        Map<String, Object> map = new HashMap<>();
        map.put("corpCode",corpCode);
        map.put("userName",userName);
        map.put("password",password);
        ApiResult apiResult = HttpUtil.postData(tokenurl,map);
        if(apiResult.isSuccessFlag()&&apiResult.getData()!=null){
            JSONObject jsonObject = JSONObject.parseObject(apiResult.getData().toString());
            return new Token(ApiResultCst.API_RESULT_SUCCESS,jsonObject.getString("token"));
        }else{
            writeLog("interface catch exception：" + apiResult.getResMsg());
            return new Token("-1","");
        }
    }


    public String getCorpCode() {
        return corpCode;
    }

    public void setCorpCode(String corpCode) {
        this.corpCode = corpCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getTokenurl() {
        return tokenurl;
    }

    public void setTokenurl(String tokenurl) {
        this.tokenurl = tokenurl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getApplyIds() {
        return applyIds;
    }

    public void setApplyIds(String applyIds) {
        this.applyIds = applyIds;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
