package com.engine.po.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.httputils.ApiResult;
import com.engine.parent.httputils.HttpUtil;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


public class Send2sapJEContract extends BaseBean implements Action {

    private String sapurl;

    private String sfdh;

    private String Company;

    private String PostDate;

    private String Remarks;

    private String U_S_OA_DocId ;

    private String U_S_OA_CreateTime;

    private String U_S_OA_TransType;

    private String U_S_OA_ActFiller;

    private String AccountCode;

    private String CardCode;

    private String Amount;

    private String FCCurrency;

    private String ProfitCode;

    private String PrjCode;

    private String bhsjehz;

    private String yhkm;

    @Override
    public String execute(RequestInfo requestInfo) {
        //获取主表数据
        Map<String, String> mainmap = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            mainmap.put(str, value);
        }
        writeLog("==sfdh== is" +mainmap.get(sfdh) + "-" + !"0".equals(mainmap.get(sfdh)));
        if(!"0".equals(mainmap.get(sfdh))){
            return Action.SUCCESS;
        }
        // 获取所有明细表
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        ArrayList<ArrayList<HashMap<String,String>>> detail = new ArrayList<>();
        if (detailtable.length > 0) {
            for (DetailTable dt : detailtable) {
                ArrayList<HashMap<String,String>> detaillist = new ArrayList<>();
                Row[] s = dt.getRow();
                for (Row r : s) {
                    Cell[] c = r.getCell();
                    HashMap<String, String> rowmap = new HashMap<>();
                    for (Cell c1 : c) {
                        rowmap.put(c1.getName(),c1.getValue());
                    }
                    detaillist.add(rowmap);
                }
                detail.add(detaillist);
            }
        }
        Map<String,Object> map = new HashMap<>();
        map.put("Company",mainmap.get(Company));
        map.put("PostDate",mainmap.get(PostDate));
        map.put("Remarks",mainmap.get(Remarks));
        map.put("U_S_OA_DocId",mainmap.get(U_S_OA_DocId));
        map.put("U_S_OA_CreateTime",mainmap.get(U_S_OA_CreateTime));
        map.put("U_S_OA_TransType", "".equals(mainmap.get(U_S_OA_TransType))?U_S_OA_TransType:mainmap.get(U_S_OA_TransType));
        map.put("U_S_OA_ActFiller",mainmap.get(U_S_OA_ActFiller));
        JSONArray jsonArray = new JSONArray();
        if(detailtable.length>0) {
            ArrayList<HashMap<String,String>> detailone = detail.get(0);
            for (int i = 0; i < detailone.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("AccountCode", detailone.get(i).get(AccountCode));
                jsonObject.put("Direct", "D");
                jsonObject.put("CardCode", mainmap.get(CardCode));
                jsonObject.put("Amount", detailone.get(i).get(Amount));
                jsonObject.put("FCCurrency", mainmap.get(FCCurrency));
                jsonObject.put("ProfitCode", detailone.get(i).get(ProfitCode));
                jsonObject.put("PrjCode", detailone.get(i).get(PrjCode));
                jsonArray.add(jsonObject);
            }
        }
        JSONObject jsonObject2th = new JSONObject();
        jsonObject2th.put("AccountCode",mainmap.get(yhkm));
        jsonObject2th.put("Direct","C");
        jsonObject2th.put("CardCode",mainmap.get(CardCode));
        jsonObject2th.put("Amount",mainmap.get(bhsjehz));
        jsonObject2th.put("FCCurrency",mainmap.get(FCCurrency));
        jsonObject2th.put("ProfitCode","");
        jsonObject2th.put("PrjCode","");
        jsonArray.add(jsonObject2th);
        map.put("Lines",jsonArray);
        writeLog("==JSON== is " + JSONObject.toJSONString(map));
        writeLog("==sapurl== is " + JSONObject.toJSONString(map));
        ApiResult apiResult = HttpUtil.postData(sapurl, map, (jsonObject, res) -> {
            writeLog("==jsonobject is " + jsonObject.toJSONString());
            if("0".equals(jsonObject.getString("Code"))){
                res.setSuccessFlag(true);
            }
            res.setData(jsonObject.getString("DocEntry"));
            res.setResMsg(jsonObject.getString("Message"));
            return res;
        });
        writeLog("==apidata== is " + apiResult.getData());
        writeLog("==apimsg== is " + apiResult.getResMsg());
        if(!apiResult.isSuccessFlag()){
            requestInfo.getRequestManager().setMessagecontent(apiResult.getResMsg());
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    public String getSapurl() {
        return sapurl;
    }

    public void setSapurl(String sapurl) {
        this.sapurl = sapurl;
    }

    public String getSfdh() {
        return sfdh;
    }

    public void setSfdh(String sfdh) {
        this.sfdh = sfdh;
    }

    public String getCompany() {
        return Company;
    }

    public void setCompany(String company) {
        Company = company;
    }

    public String getPostDate() {
        return PostDate;
    }

    public void setPostDate(String postDate) {
        PostDate = postDate;
    }

    public String getRemarks() {
        return Remarks;
    }

    public void setRemarks(String remarks) {
        Remarks = remarks;
    }

    public String getU_S_OA_DocId() {
        return U_S_OA_DocId;
    }

    public void setU_S_OA_DocId(String u_S_OA_DocId) {
        U_S_OA_DocId = u_S_OA_DocId;
    }

    public String getU_S_OA_CreateTime() {
        return U_S_OA_CreateTime;
    }

    public void setU_S_OA_CreateTime(String u_S_OA_CreateTime) {
        U_S_OA_CreateTime = u_S_OA_CreateTime;
    }

    public String getU_S_OA_TransType() {
        return U_S_OA_TransType;
    }

    public void setU_S_OA_TransType(String u_S_OA_TransType) {
        U_S_OA_TransType = u_S_OA_TransType;
    }

    public String getU_S_OA_ActFiller() {
        return U_S_OA_ActFiller;
    }

    public void setU_S_OA_ActFiller(String u_S_OA_ActFiller) {
        U_S_OA_ActFiller = u_S_OA_ActFiller;
    }

    public String getAccountCode() {
        return AccountCode;
    }

    public void setAccountCode(String accountCode) {
        AccountCode = accountCode;
    }

    public String getCardCode() {
        return CardCode;
    }

    public void setCardCode(String cardCode) {
        CardCode = cardCode;
    }

    public String getAmount() {
        return Amount;
    }

    public void setAmount(String amount) {
        Amount = amount;
    }

    public String getFCCurrency() {
        return FCCurrency;
    }

    public void setFCCurrency(String FCCurrency) {
        this.FCCurrency = FCCurrency;
    }

    public String getProfitCode() {
        return ProfitCode;
    }

    public void setProfitCode(String profitCode) {
        ProfitCode = profitCode;
    }

    public String getPrjCode() {
        return PrjCode;
    }

    public void setPrjCode(String prjCode) {
        PrjCode = prjCode;
    }

    public String getBhsjehz() {
        return bhsjehz;
    }

    public void setBhsjehz(String bhsjehz) {
        this.bhsjehz = bhsjehz;
    }

    public String getYhkm() {
        return yhkm;
    }

    public void setYhkm(String yhkm) {
        this.yhkm = yhkm;
    }
}
