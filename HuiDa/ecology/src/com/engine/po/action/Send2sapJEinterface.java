package com.engine.po.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.httputils.ApiResult;
import com.engine.parent.httputils.HttpUtil;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


public class Send2sapJEinterface  extends BaseBean implements Action {

    private String sapurl;

    private String Company;

    private String PostDate;

    private String Remarks;

    private String U_S_OA_DocId ;

    private String U_S_OA_CreateTime;

    private String U_S_OA_TransType;

    private String U_S_OA_ActFiller;

    private String fklx;

    private String AccountCode;

    private String CardCode;

    private String Amount;

    private String Amount2;

    private String FCCurrency;

    private String ProfitCode;

    private String PrjCode;

    private String skmdm;

    private String sehz;

    private String fkkm;

    private String bhsjehz;

    private String yhkm;

    private String Memo;

    @Override
    public String execute(RequestInfo requestInfo) {
        //获取主表数据
        Map<String, String> mainmap = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            mainmap.put(str, value);
        }
        // 获取所有明细表
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        ArrayList<ArrayList<HashMap<String,String>>> detail = new ArrayList<>();
        if (detailtable.length > 0) {
            for (DetailTable dt : detailtable) {
                ArrayList<HashMap<String,String>> detaillist = new ArrayList<>();
                Row[] s = dt.getRow();
                for (Row r : s) {
                    Cell[] c = r.getCell();
                    HashMap<String, String> rowmap = new HashMap<>();
                    for (Cell c1 : c) {
                        rowmap.put(c1.getName(),c1.getValue());
                    }
                    detaillist.add(rowmap);
                }
                detail.add(detaillist);
            }
        }
        Map<String,Object> map = new HashMap<>();
        map.put("Company",mainmap.get(Company));
        map.put("PostDate",mainmap.get(PostDate));
        map.put("Remarks",mainmap.get(Remarks));
        map.put("U_S_OA_DocId",mainmap.get(U_S_OA_DocId));
        map.put("U_S_OA_CreateTime",mainmap.get(U_S_OA_CreateTime));
        map.put("U_S_OA_TransType",mainmap.get(U_S_OA_TransType));
        map.put("U_S_OA_ActFiller",mainmap.get(U_S_OA_ActFiller));
        JSONArray jsonArray = new JSONArray();
        if("0".equals(mainmap.get(fklx))||"1".equals(mainmap.get(fklx))){
            ArrayList<HashMap<String,String>> detailone = detail.get(0);
            JSONObject jsonObject2th = new JSONObject();
            jsonObject2th.put("AccountCode",mainmap.get(fkkm));
            jsonObject2th.put("Direct","D");
            jsonObject2th.put("CardCode",mainmap.get(CardCode));
            jsonObject2th.put("Amount",new BigDecimal(Util.null2o(mainmap.get(sehz))).add(new BigDecimal(Util.null2o(mainmap.get(bhsjehz)))));
            jsonObject2th.put("FCCurrency",mainmap.get(FCCurrency));
            jsonObject2th.put("ProfitCode","");
            jsonObject2th.put("PrjCode","");
            if(detailone.size()>0){
                jsonObject2th.put("Memo", detailone.get(0).get(Memo));
            }
            jsonArray.add(jsonObject2th);
            JSONObject jsonObject1th = new JSONObject();
            jsonObject1th.put("AccountCode",mainmap.get(yhkm));
            jsonObject1th.put("Direct","C");
            jsonObject1th.put("CardCode",mainmap.get(CardCode));
            jsonObject1th.put("Amount",new BigDecimal(Util.null2o(mainmap.get(sehz))).add(new BigDecimal(Util.null2o(mainmap.get(bhsjehz)))));
            jsonObject1th.put("FCCurrency",mainmap.get(FCCurrency));
            jsonObject1th.put("ProfitCode","");
            jsonObject1th.put("PrjCode","");
            jsonArray.add(jsonObject1th);
        }else{
            //明细行
            if(detailtable.length>0) {
                ArrayList<HashMap<String,String>> detailone = detail.get(0);
                for (int i = 0; i < detailone.size(); i++) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("AccountCode", detailone.get(i).get(AccountCode));
                    jsonObject.put("Direct", "D");
                    jsonObject.put("CardCode", mainmap.get(CardCode));
                    jsonObject.put("Amount",("0".equals(mainmap.get(fklx))||"1".equals(mainmap.get(fklx)))?detailone.get(i).get(Amount2):detailone.get(i).get(Amount));
                    jsonObject.put("FCCurrency", mainmap.get(FCCurrency));
                    jsonObject.put("ProfitCode", detailone.get(i).get(ProfitCode));
                    jsonObject.put("Memo", detailone.get(i).get(Memo));
                    jsonObject.put("PrjCode", detailone.get(i).get(PrjCode));
                    jsonArray.add(jsonObject);
                }
            }
            if(!"".equals(mainmap.get(sehz))&&new BigDecimal(mainmap.get(sehz)).compareTo(new BigDecimal("0.00"))>0){
                //第2行
                JSONObject jsonObject3th = new JSONObject();
                jsonObject3th.put("AccountCode",mainmap.get(skmdm));
                jsonObject3th.put("Direct","D");
                jsonObject3th.put("CardCode",mainmap.get(CardCode));
                jsonObject3th.put("Amount",mainmap.get(sehz));
                jsonObject3th.put("FCCurrency",mainmap.get(FCCurrency));
                jsonObject3th.put("ProfitCode","");
                jsonObject3th.put("PrjCode","");
                jsonArray.add(jsonObject3th);
            }
            //第3行
            JSONObject jsonObject4th = new JSONObject();
            jsonObject4th.put("AccountCode",mainmap.get(fkkm));
            jsonObject4th.put("Direct","C");
            jsonObject4th.put("CardCode",mainmap.get(CardCode));
            jsonObject4th.put("Amount",new BigDecimal(Util.null2o(mainmap.get(sehz))).add(new BigDecimal(Util.null2o(mainmap.get(bhsjehz)))));
            jsonObject4th.put("FCCurrency",mainmap.get(FCCurrency));
            jsonObject4th.put("ProfitCode","");
            jsonObject4th.put("PrjCode","");
            jsonArray.add(jsonObject4th);
            //第四行
//            JSONObject jsonObject5th = new JSONObject();
//            jsonObject5th.put("AccountCode",mainmap.get(fkkm));
//            jsonObject5th.put("Direct","D");
//            jsonObject5th.put("CardCode",mainmap.get(CardCode));
//            jsonObject5th.put("Amount",new BigDecimal(Util.null2o(mainmap.get(sehz))).add(new BigDecimal(Util.null2o(mainmap.get(bhsjehz)))));
//            jsonObject5th.put("FCCurrency",mainmap.get(FCCurrency));
//            jsonObject5th.put("ProfitCode","");
//            jsonObject5th.put("PrjCode","");
//            jsonArray.add(jsonObject5th);
            //第五行
//            JSONObject jsonObject6th = new JSONObject();
//            jsonObject6th.put("AccountCode",mainmap.get(yhkm));
//            jsonObject6th.put("Direct","C");
//            jsonObject6th.put("CardCode",mainmap.get(CardCode));
//            jsonObject6th.put("Amount",new BigDecimal(Util.null2o(mainmap.get(sehz))).add(new BigDecimal(Util.null2o(mainmap.get(bhsjehz)))));
//            jsonObject6th.put("FCCurrency",mainmap.get(FCCurrency));
//            jsonObject6th.put("ProfitCode","");
//            jsonObject6th.put("PrjCode","");
//            jsonArray.add(jsonObject6th);
        }
        map.put("Lines",jsonArray);
        writeLog("==JSON== is " + JSONObject.toJSONString(map));
        writeLog("==sapurl== is " + JSONObject.toJSONString(map));
        ApiResult apiResult = HttpUtil.postData(sapurl, map, (jsonObject, res) -> {
            writeLog("==jsonobject is " + jsonObject.toJSONString());
            if(0==jsonObject.getIntValue("Code")){
                res.setSuccessFlag(true);
            }
            res.setData(jsonObject.getString("DocEntry"));
            res.setResMsg(jsonObject.getString("Message"));
            return res;
        });
        writeLog("==apidata== is " + apiResult.getData());
        writeLog("==apimsg== is " + apiResult.getResMsg());
        if(!apiResult.isSuccessFlag()){
            requestInfo.getRequestManager().setMessagecontent(apiResult.getResMsg());
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    public String getMemo() {
        return Memo;
    }

    public void setMemo(String memo) {
        Memo = memo;
    }

    public String getSapurl() {
        return sapurl;
    }

    public void setSapurl(String sapurl) {
        this.sapurl = sapurl;
    }

    public String getCompany() {
        return Company;
    }

    public void setCompany(String company) {
        Company = company;
    }

    public String getPostDate() {
        return PostDate;
    }

    public void setPostDate(String postDate) {
        PostDate = postDate;
    }

    public String getRemarks() {
        return Remarks;
    }

    public void setRemarks(String remarks) {
        Remarks = remarks;
    }

    public String getU_S_OA_DocId() {
        return U_S_OA_DocId;
    }

    public void setU_S_OA_DocId(String u_S_OA_DocId) {
        U_S_OA_DocId = u_S_OA_DocId;
    }

    public String getU_S_OA_CreateTime() {
        return U_S_OA_CreateTime;
    }

    public void setU_S_OA_CreateTime(String u_S_OA_CreateTime) {
        U_S_OA_CreateTime = u_S_OA_CreateTime;
    }

    public String getU_S_OA_TransType() {
        return U_S_OA_TransType;
    }

    public void setU_S_OA_TransType(String u_S_OA_TransType) {
        U_S_OA_TransType = u_S_OA_TransType;
    }

    public String getU_S_OA_ActFiller() {
        return U_S_OA_ActFiller;
    }

    public void setU_S_OA_ActFiller(String u_S_OA_ActFiller) {
        U_S_OA_ActFiller = u_S_OA_ActFiller;
    }

    public String getFklx() {
        return fklx;
    }

    public void setFklx(String fklx) {
        this.fklx = fklx;
    }

    public String getAccountCode() {
        return AccountCode;
    }

    public void setAccountCode(String accountCode) {
        AccountCode = accountCode;
    }

    public String getCardCode() {
        return CardCode;
    }

    public void setCardCode(String cardCode) {
        CardCode = cardCode;
    }

    public String getAmount() {
        return Amount;
    }

    public void setAmount(String amount) {
        Amount = amount;
    }

    public String getFCCurrency() {
        return FCCurrency;
    }

    public void setFCCurrency(String FCCurrency) {
        this.FCCurrency = FCCurrency;
    }

    public String getProfitCode() {
        return ProfitCode;
    }

    public void setProfitCode(String profitCode) {
        ProfitCode = profitCode;
    }

    public String getPrjCode() {
        return PrjCode;
    }

    public void setPrjCode(String prjCode) {
        PrjCode = prjCode;
    }

    public String getSkmdm() {
        return skmdm;
    }

    public void setSkmdm(String skmdm) {
        this.skmdm = skmdm;
    }

    public String getSehz() {
        return sehz;
    }

    public void setSehz(String sehz) {
        this.sehz = sehz;
    }

    public String getFkkm() {
        return fkkm;
    }

    public void setFkkm(String fkkm) {
        this.fkkm = fkkm;
    }

    public String getBhsjehz() {
        return bhsjehz;
    }

    public void setBhsjehz(String bhsjehz) {
        this.bhsjehz = bhsjehz;
    }

    public String getYhkm() {
        return yhkm;
    }

    public void setYhkm(String yhkm) {
        this.yhkm = yhkm;
    }

    public String getAmount2() {
        return Amount2;
    }

    public void setAmount2(String amount2) {
        Amount2 = amount2;
    }
}
