package com.engine.util.spdb;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.general.BaseBean;

import java.io.InputStream;
import java.util.*;

/**
 * 浦发银行接口-8924接口工具类
 */
public class Call8924Util {
    /**
     * D8924口
     *
     * @param signUrl
     * @param verifyUrl
     * @param host
     * @param port
     * @return
     */
    public static Map<String, Object> send8924(String signUrl,
                                               String verifyUrl,
                                               String host,
                                               String port,
                                               String beginDate,
                                               String endDate,
                                               String acctNo,
                                               String masterId,
                                               int startQuery,
                                               int queryNum) {

        Map<String, Object> resultData;
        Map<String, Object> result = new HashMap<>();
        result.put("flag", "1");
        result.put("list", null);
        String erroMsg;
        BaseBean bb = new BaseBean();
        try {
            //获取body明文报文
            String bodyXml = get8924BodyXml(acctNo, beginDate, endDate, startQuery, queryNum);

            //将body明文报文进行签名, 得到签名后的密文
            InputStream in = SPDBInterfaceUtil.send(bodyXml.getBytes("GBK"), SPDBInterfaceUtil.signContentType, signUrl);
            String sign = SPDBInterfaceUtil.parseSignContent(in);
            in.close();

            //获取head明文报文
            String headXml = get8924HeadXml(masterId);
            String originXml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><packet>" + headXml + bodyXml + "</packet>";

            result.put("send", originXml);
            String xmlString = "<?xml version=\"1.0\" encoding=\"gb2312\"?><packet>" + headXml + "<body><signature>" + sign + "</signature></body></packet>";//拼接报文
            //xml长度
            String xmlLength = SPDBInterfaceUtil.fixLeftNum(xmlString.length() + 6);
            xmlString = xmlLength + xmlString;
            //这里设置暂停2分钟，该接口不能连续多次调用
            bb.writeLog("8924设置暂停2分钟，该接口不能连续多次调用");
            Thread.sleep(2 * 1000 * 60);
            bb.writeLog("2分钟结束");
            String rcv = SPDBInterfaceUtil.sendByHttp(xmlString, host, port);//发送交易
            result.put("rcv", rcv);

            //step3: 将返回报文中的signature密文进行验签，获取交易返回的body明文
            if (rcv.contains("<signature>")) {
                String retSign = rcv.substring(rcv.indexOf("<signature>") + 11, rcv.indexOf("</signature>"));
                InputStream in2 = SPDBInterfaceUtil.send(retSign.getBytes(), SPDBInterfaceUtil.verifyContentType, verifyUrl);
                Map<String, String> verify = SPDBInterfaceUtil.parseVerifyContent(in2);
                in2.close();
                if (verify != null) {
                    String plainStr = verify.get("plain");
                    //获取到所有的回单文件编号
                    resultData = get8924AllInfo(plainStr);
                    result.put("resultData", resultData);
                }
                result.put("rcv", verify);
            } else {
                result.put("rcv", rcv);
                result.put("flag", "0");
                erroMsg = SPDBInterfaceUtil.getErroMsg(rcv);
                result.put("erroMsg", erroMsg);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("flag", "0");
            result.put("erroMsg", "8924程序异常：" + e.getMessage());
        }
        return result;

    }

    private static String get8924HeadXml(String masterId) {
        Element headElement, eachParam;
        //这是在创建一个根节点
        headElement = DocumentHelper.createElement("head");
        //创建head节点
        //用时间戳作为packetID，满足当天内唯一性的要求
        String packId = String.valueOf(System.currentTimeMillis());
        //body节点添加参数
        eachParam = headElement.addElement("transCode");
        eachParam.setText("8924");
        eachParam = headElement.addElement("signFlag");
        eachParam.setText("1");
        eachParam = headElement.addElement("masterID");
        eachParam.setText(masterId);
        eachParam = headElement.addElement("yqzlNo");
        eachParam.setText("");
        eachParam = headElement.addElement("packetID");
        eachParam.setText(packId);
        eachParam = headElement.addElement("timeStamp");
        eachParam.setText(SPDBInterfaceUtil.getCurrentTimeString());
        return headElement.asXML();
    }

    private static String get8924BodyXml(String acctNo, String beginDate, String endDate, int startQuery, int queryNum) {
        Element bodyElement, eachParam;
        //这是在创建一个根节点
        bodyElement = DocumentHelper.createElement("body");
        //body节点添加参数
        //账号
        eachParam = bodyElement.addElement("acctNo");
        eachParam.setText(acctNo);
        //开始日期
        eachParam = bodyElement.addElement("beginDate");
        eachParam.setText(beginDate);
        //结束日期
        eachParam = bodyElement.addElement("endDate");
        eachParam.setText(endDate);
        //查询的笔数
        eachParam = bodyElement.addElement("queryNumber");
        eachParam.setText(String.valueOf(queryNum));
        //查询的起始笔数
        eachParam = bodyElement.addElement("beginNumber");
        eachParam.setText(String.valueOf(startQuery));
        //交易金额
        eachParam = bodyElement.addElement("transAmount");
        eachParam.setText("");
        //对方帐号
        eachParam = bodyElement.addElement("subAccount");
        eachParam.setText("");
        //对方户名
        eachParam = bodyElement.addElement("subAcctName");
        eachParam.setText("");


        return bodyElement.asXML();
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> get8924AllInfo(String xml) throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> listResult = new ArrayList<>();
        Map<String, Object> eachMap;
        Document doc;

        //字符串转xml
        doc = DocumentHelper.parseText(xml);
        //获取根节点
        Element rootElt = doc.getRootElement(); // 获取根节点
        //获取跟交易总笔数
        String totalCount = rootElt.elementTextTrim("totalCount");
        result.put("totalCount", totalCount);
        Element lists = rootElt.element("lists");
        List<Element> list = lists.elements("list");
        for (Element eachList : list) {
            Iterator<Element> itList = eachList.elementIterator();
            eachMap = new HashMap<>();
            while (itList.hasNext()) {
                Element eList = itList.next();
                String eachText = eList.getTextTrim();
                String eachName = eList.getName();
                //交易流水号
                if ("seqNo".equals(eachName)) {
                    eachMap.put("seqNo", eachText);
                }
                //传票序号
                if ("seqNum".equals(eachName)) {
                    eachMap.put("seqNum", eachText);
                }
                //摘要代码
                if ("remark".equals(eachName)) {
                    eachMap.put("remark", eachText);
                }
            }
            listResult.add(eachMap);
        }
        result.put("list", listResult);
        return result;
    }
}
