package com.engine.util.spdb;

import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import weaver.general.BaseBean;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * 浦发银行接口工具类
 */
public class SPDBInterfaceUtil {
    /**
     * 加密报文头ContentType
     */
    public static final String signContentType = "INFOSEC_SIGN/1.0";
    /**
     * 验签报文头ContentType
     */
    public static final String verifyContentType = "INFOSEC_VERIFY_SIGN/1.0";
    /**
     * 通讯协议
     */
    private static final String sendType = "http";

    /**
     * @param content, contentType, url
     * @return java.io.InputStream
     * @describe 签名验签公共发送报文类
     * <AUTHOR>
     * @date 2019/12/6
     */
    public static InputStream send(byte[] content, String contentType, String url) throws IOException {
        BaseBean bb = new BaseBean();
        bb.writeLog("SPDBInterfaceUtil ---send START");
        URL urll = new URL(url);
        HttpURLConnection con1 = (HttpURLConnection) urll.openConnection();//配置报文头
        con1.setDoInput(true);
        con1.setDoOutput(true);
        con1.setRequestMethod("POST");
        con1.setRequestProperty("Content-Type", contentType);
        con1.setRequestProperty("Content-Length", String.valueOf(content.length));
        con1.connect();
        con1.getOutputStream().write(content);
        con1.getOutputStream().flush();
        bb.writeLog("SPDBInterfaceUtil ---send getResponseCode" + con1.getResponseCode());
        bb.writeLog("SPDBInterfaceUtil ---send getResponseMessage" + con1.getResponseMessage());
        return con1.getInputStream();
    }

    public static String parseSignContent(InputStream in) {
        try {
            Element el = parse(in);
            String result = el.element("head").element("result").getTextTrim();
            if (result.equals("0"))
                return el.element("body").element("sign").getText();
            else
                return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static Element parse(InputStream io) throws DocumentException, UnsupportedEncodingException {
        SAXReader reader = new SAXReader();
        reader.setValidation(false);
        InputStreamReader bi = new InputStreamReader(io, "GBK");
        Document doc;
        doc = reader.read(bi);
        return doc.getRootElement();
    }

    /**
     * 左侧补0,6位数，补0
     *
     * @return
     */
    public static String fixLeftNum(int num) {
        return String.format("%06d", num);
    }

    /**
     * @param xmlString
     * @return java.lang.String
     * @describe HTTP协议发送交易
     * <AUTHOR>
     * @date 2019/12/6
     */
    public static String sendByHttp(String xmlString, String host, String port) throws IOException {
        //每次调用该
        InputStream in = null;
        ByteArrayOutputStream outputStream = null;
        OutputStream out = null;
        String contentstr = "";
        byte[] sendByte = xmlString.getBytes();
        String urlStr = "http://" + host + ":" + port + "/";
        try {
            URL url = new URL(urlStr);
            HttpURLConnection connection;
            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(30 * 1000);
            connection.setReadTimeout(30 * 1000);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            out = connection.getOutputStream();
            out.write(sendByte);
            out.flush();
            out.close();
            in = connection.getInputStream();
//            int fileLength = connection.getContentLength();
//            int downloadLength = 0;
            outputStream = new ByteArrayOutputStream();//输出流
            byte[] bytes = new byte[1024];
            int size;
            while ((size = in.read(bytes)) != -1) {
                outputStream.write(bytes, 0, size);//将读到的字节写入输出流
            }
            contentstr = outputStream.toString();
            contentstr = contentstr.substring(6);
//            //读取返回的前6个字节，获得报文总长度
//            byte[] lengthdata = new byte[6];
//            int i = in.read(lengthdata);
//            String lengthstr = (new String(lengthdata)).trim();
//            int length = Integer.parseInt(lengthstr);
//            //根据报文总长度读取后续报文
//            rcv = new byte[length];
//            int j = 0;
//            byte[] rcvtmp = new byte[10240];
//            int srcindex = 0;
//            while (true) {
//                j = in.read(rcvtmp);
//                if (j < 0)
//                    break;
//                System.arraycopy(rcvtmp, 0, rcv, srcindex, j);
//                srcindex = srcindex + j;
//            }
//            in.close();
//            contentstr = new String(rcv, "GBK");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    System.out.println("in.close()流关闭发生异常");
                }

            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    System.out.println("outputStream.close()流关闭发生异常");
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    System.out.println("out.close()流关闭发生异常");
                }
            }
        }
        return contentstr;
    }

    public static Map<String, String> parseVerifyContent(InputStream in) {
        Map<String, String> context = new HashMap<>();
        try {
            Element el = parse(in);
            String result = el.element("head").element("result").getTextTrim();
            if (result.equals("0")) {
                String content =
                        el.element("body").element("sic").element("body").asXML();
                String DN = el.element("body").element("certdn").getText();
                context.put("plain", content);
                context.put("dn", DN);
                return context;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @return java.lang.String
     * @describe 文件下载Demo
     */
    public static Map<String, Object> sendFileByDownload(String send,
                                                         String host,
                                                         String port,
                                                         String verifyUrl,
                                                         String downloadFilePath) {
        BaseBean bb = new BaseBean();
        Map<String, Object> result = new HashMap<>();
        String erroMsg;
        //默认失败
        result.put("flag", "0");
        result.put("filePathName", "");
        result.put("fileName", "");
        //请求url，fileTransfer/不能少
        String urlstr = "http://" + host + ":" + port + "/" + "fileTransfer/";
        byte[] rcv;
        String contentstr;
        InputStream in = null;
        OutputStream out = null;
        FileOutputStream fileOutputStream = null;
        ByteArrayOutputStream outputStream = null;
        try {


            URL url = new URL(urlstr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.getOutputStream().write(send.getBytes());
            connection.getOutputStream().flush();

            //先读取前6个字节，获取报文（不含文件流）长度
            byte[] lengthdata = new byte[6];
            in = connection.getInputStream();
            in.read(lengthdata);
            String lengthstr = (new String(lengthdata)).trim();
            int length = Integer.parseInt(lengthstr);
            rcv = new byte[length];
            int j = 0;
            byte[] rcvtmp = new byte[10240];
            int srcindex = 0;
            //读取报文内容（不含文件流）
            while (j >= 0) {
                j = in.read(rcvtmp);
                if (j < 0)
                    break;
                System.arraycopy(rcvtmp, 0, rcv, srcindex, j);
                srcindex = srcindex + j;
                if (srcindex == length - 6) {
                    break;
                }
            }
            contentstr = new String(rcv);
            if (contentstr.contains("<signature>")) {
                //将返回报文中的signature密文进行验签，获取交易返回的body明文
                String retSign = contentstr.substring(contentstr.indexOf("<signature>") + 11, contentstr.indexOf("</signature>"));
                InputStream in2 = send(retSign.getBytes(), verifyContentType, verifyUrl);
                Map<String, String> verify = parseVerifyContent(in2);
                in2.close();
                bb.writeLog("WJ12 verify: " + verify);
                if (verify != null) {
                    String plainStr = verify.getOrDefault("plain", "");

                    Map<String, String> mapResult = getWJ12FileData(plainStr);
                    //获取返回报文中的文件长度和文件名
                    String retfileLengthStr = mapResult.get("fileLength");
                    String retfileName = mapResult.get("fileName");
                    if (StringUtils.isNotBlank(retfileLengthStr) && StringUtils.isNotBlank(retfileName)) {
                        int fileLeng = Integer.parseInt(retfileLengthStr);
                        String filePathName = downloadFilePath + "\\" + retfileName;
                        File tempFile = new File(filePathName);
                        if (tempFile.exists()) {
                            tempFile.delete();
                        }
                        fileOutputStream = new FileOutputStream(tempFile);
                        byte[] readTemp = new byte[8096];
                        int k;
                        int byteCounts = 0;
                        while (true) {
                            //设置缓字节冲数组，继续读流，读到文件输出流之中
                            k = in.read(readTemp);
                            if (k < 0) {
                                break;
                            }
                            fileOutputStream.write(readTemp, 0, k);
                            byteCounts += k;
                        }
                        fileOutputStream.close();
                        result.put("flag", "1");
                        result.put("filePathName", filePathName);
                        result.put("fileName", retfileName);
                        bb.writeLog("共计接收文件字节数为：" + byteCounts);
                    }
                }
                result.put("rcv", verify);
            } else {
                result.put("rcv", contentstr);
                result.put("flag", "0");
                erroMsg = SPDBInterfaceUtil.getErroMsg(contentstr);
                result.put("erroMsg", erroMsg);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("flag", "0");
            result.put("erroMsg", "WJ12程序异常：" + e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    bb.writeLog("in.close()流关闭发生异常");
                }

            }
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    bb.writeLog("fileOutputStream.close()流关闭发生异常");
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    bb.writeLog("out.close()流关闭发生异常");
                }
            }
        }
        return result;
    }

    public static Map<String, String> getWJ12FileData(String xml) {
        Map<String, String> map = new HashMap<>();
        Document doc;
        try {
            //字符串转xml
            doc = DocumentHelper.parseText(xml);
            //获取根节点
            Element rootElt = doc.getRootElement(); // 获取根节点
            String fileLength = rootElt.elementTextTrim("fileLength");
            String fileName = rootElt.elementTextTrim("fileName");
            map.put("fileLength", fileLength);
            map.put("fileName", fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 获取当前时间 格式为"yyyy'-'MM'-'dd' 'HH:mm:ss"
     *
     * @return
     */
    public static String getCurrentTimeString() {
        String timestrformart = "yyyy'-'MM'-'dd' 'HH:mm:ss";
        SimpleDateFormat SDF = new SimpleDateFormat(timestrformart);
        Calendar calendar = Calendar.getInstance();
        return SDF.format(calendar.getTime());
    }

    /**
     * 截取字符串str中指定字符 strStart、strEnd之间的字符串
     *
     * @param str
     * @param strStart
     * @param strEnd
     * @return
     */
    public static String subStringBetween(String str, String strStart, String strEnd) {

        /* 找出指定的2个字符在 该字符串里面的 位置 */
        int strStartIndex = str.indexOf(strStart);
        int strEndIndex = str.indexOf(strEnd);

        /* index 为负数 即表示该字符串中 没有该字符 */
        if (strStartIndex < 0) {
            return "字符串 :---->" + str + "<---- 中不存在 " + strStart + ", 无法截取目标字符串";
        }
        if (strEndIndex < 0) {
            return "字符串 :---->" + str + "<---- 中不存在 " + strEnd + ", 无法截取目标字符串";
        }
        /* 开始截取 */
        return str.substring(strStartIndex, strEndIndex).substring(strStart.length());
    }

    /**
     * 获取失败信息
     *
     * @param str
     * @return
     */
    public static String getErroMsg(String str) {
        String returnCode = SPDBInterfaceUtil.subStringBetween(str, "<returnCode>", "</returnCode>");
        String returnMsg = SPDBInterfaceUtil.subStringBetween(str, "<returnMsg>", "</returnMsg>");
        return "接口出错：returnCode:" + returnCode + ",returnMsg:" + returnMsg;
    }


}
