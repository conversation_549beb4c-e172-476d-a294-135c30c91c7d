package com.engine.util.spdb;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.InputStream;
import java.util.*;

/**
 * 浦发银行接口-DY47接口工具类
 */
public class DY47Util {
    /**
     * DY47接口
     *
     * @param signUrl
     * @param verifyUrl
     * @param host
     * @param port
     * @return
     */
    public static Map<String, Object> sendDY47(String signUrl,
                                               String verifyUrl,
                                               String host,
                                               String port,
                                               String serialNum,
                                               String subpoenaSeqNo,
                                               String beginDate,
                                               String endDate,
                                               String acctNo,
                                               String masterId) {

        List<String> list;
        Map<String, Object> result = new HashMap<>();
        result.put("flag", "0");
        result.put("list", null);
        String erroMsg;
        try {
            //获取body明文报文
            String bodyXml = getDY47BodyXml(acctNo, beginDate, endDate, serialNum, subpoenaSeqNo);

            //将body明文报文进行签名, 得到签名后的密文
            InputStream in = SPDBInterfaceUtil.send(bodyXml.getBytes("GBK"), SPDBInterfaceUtil.signContentType, signUrl);
            String sign = SPDBInterfaceUtil.parseSignContent(in);
            in.close();

            //获取head明文报文
            String headXml = getDY47HeadXml(masterId);
            String originXml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><packet>" + headXml + bodyXml + "</packet>";

            result.put("send", originXml);
            String xmlString = "<?xml version=\"1.0\" encoding=\"gb2312\"?><packet>" + headXml + "<body><signature>" + sign + "</signature></body></packet>";//拼接报文
            //xml长度
            String xmlLength = SPDBInterfaceUtil.fixLeftNum(xmlString.length() + 6);
            xmlString = xmlLength + xmlString;
            //发送交易
            String rcv = SPDBInterfaceUtil.sendByHttp(xmlString, host, port);

            //step3: 将返回报文中的signature密文进行验签，获取交易返回的body明文
            if (rcv.contains("<signature>")) {
                String retSign = rcv.substring(rcv.indexOf("<signature>") + 11, rcv.indexOf("</signature>"));
                InputStream in2 = SPDBInterfaceUtil.send(retSign.getBytes(), SPDBInterfaceUtil.verifyContentType, verifyUrl);
                Map<String, String> verify = SPDBInterfaceUtil.parseVerifyContent(in2);
                in2.close();
                if (verify != null) {
                    String plainStr = verify.get("plain");
                    //获取到所有的回单文件编号
                    list = getDY47AllAcceptNo(plainStr);
                    result.put("list", list);
                    result.put("flag", "1");
                }
                result.put("rcv", verify);
            } else {
                result.put("rcv", rcv);
                result.put("flag", "0");
                erroMsg = SPDBInterfaceUtil.getErroMsg(rcv);
                result.put("erroMsg", erroMsg);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("flag", "0");
            result.put("erroMsg", "DY47程序异常：" + e.getMessage());
        }
        return result;

    }

    private static String getDY47HeadXml(String masterId) {
        Element headElement, eachParam;
        //这是在创建一个根节点
        headElement = DocumentHelper.createElement("head");
        //创建head节点
        //用时间戳作为packetID，满足当天内唯一性的要求
        String packId = String.valueOf(System.currentTimeMillis());
        //body节点添加参数
        eachParam = headElement.addElement("transCode");
        eachParam.setText("DY47");
        eachParam = headElement.addElement("signFlag");
        eachParam.setText("1");
        eachParam = headElement.addElement("masterID");
        eachParam.setText(masterId);
        eachParam = headElement.addElement("yqzlNo");
        eachParam.setText("");
        eachParam = headElement.addElement("packetID");
        eachParam.setText(packId);
        eachParam = headElement.addElement("timeStamp");
        eachParam.setText(SPDBInterfaceUtil.getCurrentTimeString());
        return headElement.asXML();
    }

    private static String getDY47BodyXml(String acctNo, String beginDate, String endDate, String serialNum, String subpoenaSeqNo) {
        Element bodyElement, eachParam;
        //这是在创建一个根节点
        bodyElement = DocumentHelper.createElement("body");
        //body节点添加参数
        eachParam = bodyElement.addElement("billDownloadChanel");
        eachParam.setText("1");
        eachParam = bodyElement.addElement("acctNo");
        eachParam.setText(acctNo);
        eachParam = bodyElement.addElement("singleOrBatchFlag");
        eachParam.setText("0");
        eachParam = bodyElement.addElement("beginNumber");
        eachParam.setText("");
        eachParam = bodyElement.addElement("queryNumber");
        eachParam.setText("");
        eachParam = bodyElement.addElement("businessCode");
        eachParam.setText("");
        eachParam = bodyElement.addElement("backhostGyno");
        eachParam.setText(serialNum);
        eachParam = bodyElement.addElement("debitFlag");
        eachParam.setText("");
        eachParam = bodyElement.addElement("subpoenaSeqNo");
        eachParam.setText(subpoenaSeqNo);
        eachParam = bodyElement.addElement("oppositeBankNo");
        eachParam.setText("");
        eachParam = bodyElement.addElement("oppositeBankName");
        eachParam.setText("");
        eachParam = bodyElement.addElement("oppositeAcctNo");
        eachParam.setText("");
        eachParam = bodyElement.addElement("oppositeAcctName");
        eachParam.setText("");
        eachParam = bodyElement.addElement("beginDate");
        eachParam.setText(beginDate);
        eachParam = bodyElement.addElement("endDate");
        eachParam.setText(endDate);
        eachParam = bodyElement.addElement("mixAmount");
        eachParam.setText("");
        eachParam = bodyElement.addElement("maxAmount");
        eachParam.setText("");
        eachParam = bodyElement.addElement("ascOrDes");
        eachParam.setText("");
        eachParam = bodyElement.addElement("summary");
        eachParam.setText("");
        eachParam = bodyElement.addElement("remark");
        eachParam.setText("");
        eachParam = bodyElement.addElement("reservedOne");
        eachParam.setText("");
        eachParam = bodyElement.addElement("reservedTwo");
        eachParam.setText("");
        eachParam = bodyElement.addElement("reservedThree");
        eachParam.setText("");

        return bodyElement.asXML();
    }

    @SuppressWarnings("unchecked")
    public static List<String> getDY47AllAcceptNo(String xml) throws Exception {
        List<String> listResult = new ArrayList<>();
        Document doc;

        //字符串转xml
        doc = DocumentHelper.parseText(xml);
        //获取根节点
        Element rootElt = doc.getRootElement();
        Element lists = rootElt.element("lists");
        List<Element> list = lists.elements("list");
        for (Element eachList : list) {
            Iterator<Element> itList = eachList.elementIterator();
            while (itList.hasNext()) {
                Element eList = itList.next();
                String eachText = eList.getTextTrim();
                String eachName = eList.getName();
                if ("acceptNo".equals(eachName)) {
                    listResult.add(eachText);
                }
            }
        }
        return listResult;
    }
}
