package com.engine.util.spdb;

import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.entity.ApiLog;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.util.file.File2DocUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class CallSPDBUtil {
    //基类
    private static final BaseBean bb = new BaseBean();


    /**
     * 执行回单下载逻辑
     *
     * @param signUrl           加密地址，bisafe签名服务地址和端口
     * @param verifyUrl         验签地址，bisafe签名服务地址和端口
     * @param host              目标地址，bisafe所在地址
     * @param port              bisafe nchttp服务监听端口
     * @param downloadPort      目标端口，bisafe文件传输服务监听端口
     * @param downloadFilePath  下载文件保存路径
     * @param formtables        流程表单名，多个逗号隔开
     * @param remarkField       摘要代码字段
     * @param docField          多文档字段
     * @param acctNo            账号
     * @param masterId          客户号
     * @param logModuleId       接口日志建模id
     * @param catId             wj12接口下载的文件存到的文档目录id
     * @param specificRequestid 指定某一个requestid，不指定填-1
     * @param beginDate         查询的开始日期
     * @param endDate           查询的结束日期
     */
    public static void executeHuidanDownload(String signUrl,
                                             String verifyUrl,
                                             String host,
                                             String port,
                                             String downloadPort,
                                             String downloadFilePath,
                                             String formtables,
                                             String remarkField,
                                             String docField,
                                             String acctNo,
                                             String masterId,
                                             String logModuleId,
                                             String catId,
                                             String specificRequestid,
                                             String beginDate,
                                             String endDate
    ) {
        RecordSet rs;
        String remark, requestId, seqNo, seqNum, filePathName, fileName, eachRemark;
        boolean rsFlag;
        List<String> allAcceptNum, eachDY47List, requestIdList, acceptNumList;

        //存储requestid对应的回单编码列表
        Map<String, List<String>> mapRequestid = new HashMap<>();
        //存储DY47List对应的回单编码列表
        Map<String, List<String>> mapDY47List = new HashMap<>();
        //存储requestid对应的表名
        Map<String, String> mapTableRequest = new HashMap<>();
        //WJ12结果
        Map<String, Object> WJ12Result;
        //8924结果列表
        List<Map<String, Object>> resultDataList8924Total;
        //8924结果列表，有效的数据，和流程的remark字段进行匹配过的
        List<Map<String, Object>> resultDataList8924Valid = new ArrayList<>();
        int docId;
        StringBuilder allDocId;
        try {
            bb.writeLog("formtables: " + formtables);
            bb.writeLog("传来的beginDate: " + beginDate);
            bb.writeLog("传来的endDate: " + endDate);
            String[] tables = formtables.split(CommonCst.COMMA_EN);
            //遍历所有配置的表单
            for (String tableName : tables) {
                //获取表单数据
                rs = new RecordSet();
                String sql = "select requestid," + remarkField + " from " + tableName +
                        " where 1=1 and (" + remarkField + " is not null and " + remarkField + " != '') ";
                if (StringUtils.isNotBlank(specificRequestid) && !"-1".equals(specificRequestid)) {
                    sql += " and requestid = '" + specificRequestid + "' ";
                }
                bb.writeLog("sql: " + sql);
                rsFlag = rs.executeQuery(sql);
                if (rsFlag) {
                    while (rs.next()) {
                        requestId = Util.null2String(rs.getString("requestid"));
                        remark = Util.null2String(rs.getString(remarkField));
                        //将remark对应所有的requestid 缓存到map
                        if (mapRequestid.containsKey(remark)) {
                            requestIdList = mapRequestid.get(remark);
                        } else {
                            requestIdList = new ArrayList<>();
                        }
                        requestIdList.add(requestId);
                        mapRequestid.put(remark, requestIdList);
                        //存储requestid对应的表名
                        mapTableRequest.put(requestId, tableName);
                    }
                } else {
                    bb.writeLog("执行查询表单数据错误：" + rs.getMsg() + "；" + rs.getExceptionMsg());
                }
            }
            bb.writeLog("mapRequestid:");
            bb.writeLog(mapRequestid);
            if (!mapRequestid.isEmpty()) {
                //8924
                resultDataList8924Total = CallSPDBUtil.call8924(signUrl,
                        verifyUrl,
                        host,
                        port,
                        beginDate,
                        endDate,
                        acctNo,
                        masterId,
                        logModuleId);

                if (!resultDataList8924Total.isEmpty()) {
                    //匹配有效的8924结果集
                    for (Map<String, Object> m : resultDataList8924Total) {
                        //摘要代码
                        remark = Util.null2String(m.get("remark"));
                        for (Map.Entry<String, List<String>> entry : mapRequestid.entrySet()) {
                            eachRemark = entry.getKey();
                            if (eachRemark.equals(remark)) {
                                resultDataList8924Valid.add(m);
                            }
                        }
                    }
                    bb.writeLog("resultDataList8924Valid:");
                    bb.writeLog(resultDataList8924Valid);
                    //遍历有效的8924数据
                    if (!resultDataList8924Valid.isEmpty()) {
                        for (Map<String, Object> m : resultDataList8924Valid) {
                            //交易流水号
                            seqNo = Util.null2String(m.get("seqNo"));
                            //传票序号
                            seqNum = Util.null2String(m.get("seqNum"));
                            //摘要代码
                            remark = Util.null2String(m.get("remark"));
                            //调用DY47
                            allAcceptNum = CallSPDBUtil.callDY47(signUrl,
                                    verifyUrl,
                                    host,
                                    port,
                                    seqNo,
                                    seqNum,
                                    beginDate,
                                    endDate,
                                    acctNo,
                                    masterId,
                                    logModuleId);
                            //将remark对应所有的allAcceptNum 缓存到map
                            if (mapDY47List.containsKey(remark)) {
                                acceptNumList = mapDY47List.get(remark);
                            } else {
                                acceptNumList = new ArrayList<>();
                            }
                            acceptNumList.addAll(allAcceptNum);
                            mapDY47List.put(remark, acceptNumList);
                        }
                        bb.writeLog("mapDY47List:");
                        bb.writeLog(mapDY47List);
                        //暂停5分钟后，执行WJ12
                        Thread.sleep(5 * 1000);
                        for (Map.Entry<String, List<String>> entry : mapDY47List.entrySet()) {
                            eachRemark = entry.getKey();
                            eachDY47List = entry.getValue();
                            allDocId = new StringBuilder();
                            for (String s : eachDY47List) {
                                WJ12Result = CallSPDBUtil.callWJ12(signUrl,
                                        verifyUrl,
                                        host,
                                        downloadPort,
                                        s,
                                        downloadFilePath,
                                        masterId,
                                        acctNo,
                                        logModuleId);
                                if ("1".equals(WJ12Result.get("flag"))) {
                                    filePathName = Util.null2String(WJ12Result.get("filePathName"));
                                    fileName = Util.null2String(WJ12Result.get("fileName"));
                                    bb.writeLog("filePathName:" + filePathName);
                                    bb.writeLog("fileName:" + fileName);
                                    bb.writeLog("catId:" + catId);
                                    docId = File2DocUtil.saveFile2Doc(filePathName, fileName, Integer.parseInt(catId));
                                    bb.writeLog("docId:" + docId);
                                    allDocId.append(docId).append(CommonCst.COMMA_EN);
                                }
                            }
                            bb.writeLog("allDocId:");
                            bb.writeLog(allDocId);
                            if (!"".equals(allDocId.toString())) {
                                allDocId = new StringBuilder(allDocId.substring(0, allDocId.length() - 1));
                                //更新表单的多文档字段
                                updateTableDoc(allDocId.toString(),
                                        mapRequestid,
                                        eachRemark,
                                        mapTableRequest,
                                        docField);
                            }
                        }
                    } else {
                        bb.writeLog("有效的8924数据为空");
                    }
                } else {
                    bb.writeLog("8924接口无数据");
                }
            } else {
                bb.writeLog("mapRequestid为空");
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("executeHuidanDownload异常：" + e.getMessage() + Arrays.toString(e.getStackTrace()));
        }
    }


    /**
     * 更新表单的多文档字段
     *
     * @param docIds
     * @param mapRequestid
     * @param remark
     */
    private static void updateTableDoc(String docIds,
                                       Map<String, List<String>> mapRequestid,
                                       String remark,
                                       Map<String, String> mapTableRequest,
                                       String docField) {
        RecordSet rs = new RecordSet();
        String sql;
        String tableName;
        List<String> listRequestid = mapRequestid.getOrDefault(remark, null);
        if (!listRequestid.isEmpty()) {
            for (String resquestid : listRequestid) {
                tableName = mapTableRequest.getOrDefault(resquestid, null);
                if (tableName != null) {
                    sql = "update " + tableName + " set " + docField + " = ? where requestid = ? ";
                    rs.executeUpdate(sql, docIds, resquestid);
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> call8924(String signUrl,
                                                     String verifyUrl,
                                                     String host,
                                                     String port,
                                                     String beginDate,
                                                     String endDate,
                                                     String acctNo,
                                                     String masterId,
                                                     String logModuleId
    ) {
        bb.writeLog("CallSPDBUtil---call8924 ---START");
        Map<String, Object> call8924Result;
        Map<String, Object> resultData8924;
        List<Map<String, Object>> resultDataList8924;
        List<Map<String, Object>> resultDataList8924Total = null;

        int firstQueryStart = 1;
        int firstQueryNum = 50;
        int maxQueryNum = 50;
        int eachQueryStart = 1;
        int eachQueryNum;
        int totalCount;
        ApiLog log;

        log = new ApiLog();
        log.setRequestId("");
        log.setMethodName("8924");
        log.setSendTime(TimeUtil.getCurrentTimeString());
        //8924 一次只能查询50条数据，超过50次需要多次查询
        call8924Result = Call8924Util.send8924(signUrl,
                verifyUrl,
                host,
                port,
                beginDate,
                endDate,
                acctNo,
                masterId,
                firstQueryStart,
                firstQueryNum);
        //插入接口日志
        recordLog(log, call8924Result, logModuleId);
        bb.writeLog("第一次 call8924Result:");
        bb.writeLog(call8924Result);
        bb.writeLog("第一次 call8924Result end");
        if ("1".equals(Util.null2String(call8924Result.get("flag")))) {
            resultData8924 = (Map<String, Object>) call8924Result.get("resultData");
            totalCount = Integer.parseInt((String) resultData8924.get("totalCount"));
            resultDataList8924 = (List<Map<String, Object>>) resultData8924.get("list");
            resultDataList8924Total = new ArrayList<>(resultDataList8924);
            if (totalCount > firstQueryNum) {
                int leftCount = totalCount - firstQueryNum;

                BigDecimal leftCountBig = new BigDecimal(leftCount);
                BigDecimal firstQueryNumBig = new BigDecimal(firstQueryNum);
                BigDecimal div = leftCountBig.divide(firstQueryNumBig, 2, RoundingMode.UP);
                //计算还要查询接口的次数
                int leftTimes = div.setScale(0, RoundingMode.UP).intValue();
                for (int i = 1; i <= leftTimes; i++) {
                    eachQueryStart += maxQueryNum;
                    if (i == leftTimes) {
                        eachQueryNum = leftCount;
                    } else {
                        eachQueryNum = maxQueryNum;
                        leftCount = leftCount - maxQueryNum;
                    }

                    int cc = i + 1;
                    bb.writeLog("第" + cc + "次调用8924，eachQueryStart:" + eachQueryStart + ";eachQueryNum:" + eachQueryNum);
                    log = new ApiLog();
                    log.setRequestId("");
                    log.setMethodName("8924");
                    log.setSendTime(TimeUtil.getCurrentTimeString());
                    call8924Result = Call8924Util.send8924(signUrl,
                            verifyUrl,
                            host,
                            port,
                            beginDate,
                            endDate,
                            acctNo,
                            masterId,
                            eachQueryStart,
                            eachQueryNum);
                    //插入接口日志
                    recordLog(log, call8924Result, logModuleId);
                    if ("1".equals(Util.null2String(call8924Result.get("flag")))) {
                        resultData8924 = (Map<String, Object>) call8924Result.get("resultData");
                        resultDataList8924 = (List<Map<String, Object>>) resultData8924.get("list");
                        resultDataList8924Total.addAll(resultDataList8924);
                    }
                }
            }
        }
        bb.writeLog("resultDataList8924Total :");
        bb.writeLog(resultDataList8924Total);
        bb.writeLog("CallSPDBUtil---call8924 ---END");
        return resultDataList8924Total;
    }

    /**
     * DY47
     * 根据8924查询的参数进行调用DY47
     *
     * @param signUrl
     * @param verifyUrl
     * @param host
     * @param port
     * @param seqNo
     * @param seqNum
     * @param beginDate
     * @param endDate
     * @param acctNo
     * @param masterId
     * @param logModuleId
     * @return
     */
    @SuppressWarnings("unchecked")
    public static List<String> callDY47(String signUrl,
                                        String verifyUrl,
                                        String host,
                                        String port,
                                        String seqNo,
                                        String seqNum,
                                        String beginDate,
                                        String endDate,
                                        String acctNo,
                                        String masterId,
                                        String logModuleId) {
        bb.writeLog("CallSPDBUtil---callDY47 ---START");
        Map<String, Object> DY47Result;
        List<String> allAcceptNum = null;
        ApiLog log;
        log = new ApiLog();
        log.setRequestId("");
        log.setMethodName("DY47");
        log.setSendTime(TimeUtil.getCurrentTimeString());

        DY47Result = DY47Util.sendDY47(signUrl,
                verifyUrl,
                host,
                port,
                seqNo,
                seqNum,
                beginDate,
                endDate,
                acctNo,
                masterId);
        //插入接口日志
        recordLog(log, DY47Result, logModuleId);
        bb.writeLog("DY47Result:");
        bb.writeLog(DY47Result);
        bb.writeLog("DY47Result end");
        if ("1".equals(DY47Result.get("flag"))) {
            allAcceptNum = (List<String>) DY47Result.get("list");
            bb.writeLog("DY47Result list:");
            bb.writeLog(allAcceptNum);
        }
        bb.writeLog("CallSPDBUtil---callDY47 ---END");
        return allAcceptNum;
    }


    /**
     * 根据都有DY47查询的参数进行调用WJ12
     *
     * @param signUrl
     * @param verifyUrl
     * @param host
     * @param port
     * @param fileDownloadPar
     * @param downloadFilePath
     * @param masterId
     * @param acctNo
     * @return
     */
    public static Map<String, Object> callWJ12(String signUrl,
                                               String verifyUrl,
                                               String host,
                                               String port,
                                               String fileDownloadPar,
                                               String downloadFilePath,
                                               String masterId,
                                               String acctNo,
                                               String logModuleId
    ) {
        bb.writeLog("CallSPDBUtil---callWJ12 ---START");
        Map<String, Object> wj12Result;
        ApiLog log;
        log = new ApiLog();
        log.setRequestId("");
        log.setMethodName("WJ12");
        log.setSendTime(TimeUtil.getCurrentTimeString());

        wj12Result = WJ12Util.sendWJ02(signUrl,
                verifyUrl,
                host,
                port,
                fileDownloadPar,
                downloadFilePath,
                masterId,
                acctNo);
        //插入接口日志
        recordLog(log, wj12Result, logModuleId);
        bb.writeLog("WJ12Result:");
        bb.writeLog(wj12Result);
        bb.writeLog("WJ12Result end");
        bb.writeLog("CallSPDBUtil---callWJ12 ---END");
        return wj12Result;
    }

    /**
     * 记录日志
     *
     * @param log
     * @param apiResult
     * @param logModuleId
     */
    private static void recordLog(ApiLog log, Map<String, Object> apiResult, String logModuleId) {
        //发送报文
        String sendStr = Util.null2String(apiResult.get("send"));
        //接收的报文
        String rcvStr = Util.null2String(apiResult.get("rcv"));
        //成功失败表示，1成功0失败
        String flagStr = Util.null2String(apiResult.get("flag"));
        //失败信息
        String erroMsgStr = Util.null2String(apiResult.get("erroMsg"));
        log.setSendData(sendStr);
        log.setResponseData(rcvStr);
        log.setSuccessFlag(Integer.parseInt(flagStr));
        log.setResponseTime(SDUtil.formatDate(new Date()));
        log.setResultInfo(erroMsgStr);
        ApiLogUtil.insertLog(log, 1, Integer.parseInt(logModuleId));
    }

    public static void main(String[] args) throws InterruptedException {
        //加密地址，bisafe签名服务地址和端口
        String signUrl = "http://**************:6779";
        //验签地址，bisafe签名服务地址和端口
        String verifyUrl = "http://**************:6779";
        //目标地址，bisafe所在地址
        String host = "**************";
        //bisafe nchttp服务监听端口
        String httpPort = "6777";
        //bisafe文件传输服务监听端口
        String downloadPort = "6778";
        //下载文件目录
        String downloadPath = "D:\\";
        String masterId = "2000040752";
        String accNo = "952A9997220008092";
        String beginDate = "20220622";
        String endDate = "20220622";

        List<Map<String, Object>> resultDataList8924Total = null;
        List<String> allAcceptNum;
        String seqNo = "999703420002";
        String seqNum = "1";
        String fileCode = "";
        //8924
        // resultDataList8924Total = call8924();
//        if (resultDataList8924Total != null) {
//            for (Map<String, Object> map : resultDataList8924Total) {
//                //交易流水号
//                String seqNo = (String) map.get("seqNo");
//                //传票序号
//                String seqNum = (String) map.get("seqNum");
//                //摘要代码
//                String remark = (String) map.get("remark");
//            }
//        }

        System.out.println("final");
    }
}
