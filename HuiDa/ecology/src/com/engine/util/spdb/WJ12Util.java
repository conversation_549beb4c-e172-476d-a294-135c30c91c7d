package com.engine.util.spdb;

import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.general.BaseBean;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 浦发银行接口-WJ12接口工具类
 */
public class WJ12Util {

    /**
     * 调用WJ02接口，下载回单
     *
     * @param signUrl
     * @param verifyUrl
     * @param host
     * @param port
     * @param fileDownloadPar
     * @param downloadFilePath
     * @return 文件全路径
     */
    public static Map<String, Object> sendWJ02(String signUrl,
                                               String verifyUrl,
                                               String host,
                                               String port,
                                               String fileDownloadPar,
                                               String downloadFilePath,
                                               String masterId,
                                               String acctNo) {
        Map<String, Object> result = new HashMap<>();
        BaseBean bb = new BaseBean();
        try {
            //获取body明文报文
            String bodyXml = getWJ12BodyXml(masterId, acctNo, fileDownloadPar);
            //将body明文报文进行签名, 得到签名后的密文
            InputStream in = SPDBInterfaceUtil.send(bodyXml.getBytes("GBK"), SPDBInterfaceUtil.signContentType, signUrl);
            String sign = SPDBInterfaceUtil.parseSignContent(in);
            in.close();

            //获取head明文报文
            String headXml = getWJ12HeadXml(masterId);
            String originXml = "<?xml version=\"1.0\" encoding=\"gb2312\"?><packet>" + headXml + bodyXml + "</packet>";


            String xmlString = "<?xml version=\"1.0\" encoding=\"gb2312\"?><packet>" + headXml + "<body><signature>" + sign + "</signature></body></packet>";//拼接报文
            //xml长度
            String xmlLength = SPDBInterfaceUtil.fixLeftNum(xmlString.length() + 6);
            xmlString = xmlLength + xmlString;
            //发送交易
            result = SPDBInterfaceUtil.sendFileByDownload(xmlString, host, port, verifyUrl, downloadFilePath);
            //请求报文
            result.put("send", originXml);

        } catch (Exception e) {
            e.printStackTrace();
            result.put("flag", "0");
            result.put("erroMsg", "WJ12程序异常：" + e.getMessage());
        }
        return result;
    }

    private static String getWJ12HeadXml(String masterId) {
        Element headElement, eachParam;
        //这是在创建一个根节点
        headElement = DocumentHelper.createElement("head");
        //创建head节点
        //用时间戳作为packetID，满足当天内唯一性的要求
        String packId = String.valueOf(System.currentTimeMillis());
        //body节点添加参数
        eachParam = headElement.addElement("transCode");
        eachParam.setText("WJ12");
        eachParam = headElement.addElement("signFlag");
        eachParam.setText("1");
        eachParam = headElement.addElement("masterID");
        eachParam.setText(masterId);
        eachParam = headElement.addElement("yqzlNo");
        eachParam.setText("");
        eachParam = headElement.addElement("packetID");
        eachParam.setText(packId);
        eachParam = headElement.addElement("timeStamp");
        eachParam.setText(SPDBInterfaceUtil.getCurrentTimeString());
        return headElement.asXML();
    }

    private static String getWJ12BodyXml(String masterId, String acctNo, String fileDownloadPar) {
        Element bodyElement, eachParam;
        //这是在创建一个根节点
        bodyElement = DocumentHelper.createElement("body");
        //body节点添加参数
        eachParam = bodyElement.addElement("transMasterID");
        eachParam.setText(masterId);
        eachParam = bodyElement.addElement("acctNo");
        eachParam.setText(acctNo);
        eachParam = bodyElement.addElement("fileDownloadFlag");
        eachParam.setText("1");
        eachParam = bodyElement.addElement("fileDownloadPar");
        eachParam.setText(fileDownloadPar);
        eachParam = bodyElement.addElement("remark1");
        eachParam.setText("");
        eachParam = bodyElement.addElement("remark2");
        eachParam.setText("");
        eachParam = bodyElement.addElement("remark3");
        eachParam.setText("");
        eachParam = bodyElement.addElement("remark4");
        eachParam.setText("");
        eachParam = bodyElement.addElement("remark5");
        eachParam.setText("");
        eachParam = bodyElement.addElement("remark6");
        eachParam.setText("");

        return bodyElement.asXML();
    }
}
