package com.engine.util.file;

import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.toolbox.doc.DocUtil;

import java.io.FileInputStream;
import java.io.IOException;

/**
 * 文件转泛微文档工具类
 */
public class File2DocUtil {
    /**
     * 保存文件到文档
     *
     * @param filePathName
     * @param filenName
     * @param catId
     * @return
     */
    public static int saveFile2Doc(String filePathName, String filenName, int catId) {
        FileInputStream in = null;
        int docId = -1;
        BaseBean bb = new BaseBean();
        try {
            in = new FileInputStream(filePathName);
            User user = new User(1);
            bb.writeLog("getUsername:" + user.getUsername());
            docId = DocUtil.createDocWithFile(in, user, filenName, catId);
            bb.writeLog("docId:" + docId);
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("saveFile2Doc expt: " + e.getMessage());

        } finally {
            //关闭文件流
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return docId;
    }
}
