package com.engine.action;

import com.weaver.formmodel.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

public class WorkflowtimeAction  extends BaseBean implements Action {

    private String times;
    private String sqrcol;
    private String sqrq;
    private String sout;

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm =  requestInfo.getRequestManager();
        String table = rm.getBillTableName();
        HashMap<String,String> maindata = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            maindata.put(str, value);
        }
        Date date = DateHelper.parseDate(maindata.get(sqrq));
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        String ye =String.valueOf( ca.get(Calendar.YEAR));
        String mon =String.valueOf( ca.get(Calendar.MONTH)+1);
        String rqr = Util.null2String(maindata.get(sqrcol));
        if("".equals(ye)||"".equals(mon)||"".equals(rqr)){
            rm.setMessagecontent("关键字段为空");
            return FAILURE_AND_CONTINUE;
        }
        writeLog(ye+"-"+mon+"-"+"rqr");
        String sqlselect ="select * from\n" +
                "(\n" +
                "select count(*) num,CONVERT(  varchar(10),YEAR(base.createdate) ) ye, CONVERT(  varchar(10),MONTH(base.createdate)) mon,taba."+sqrcol+" sqr from \n" +
                " "+table+" taba\n" +
                "left join workflow_requestbase base \n" +
                "on base.requestid = taba.requestid where base.currentnodetype<>0 group by CONVERT(  varchar(10),YEAR(base.createdate) ) , CONVERT(  varchar(10),MONTH(base.createdate)),taba."+sqrcol+" \n" +
                ") taba where taba.sqr = '"+rqr+"' and ye = '"+ye+"' and mon = '"+mon+"'";
        RecordSet select = new RecordSet();
        writeLog("sqlis"+sqlselect);
        boolean flag = select.execute(sqlselect);
        if(flag){
            if(select.next()){
                String num=Util.null2o(select.getString("num"));
                writeLog(num);
                if(Integer.parseInt(num)>Integer.parseInt(times)){
                    rm.setMessagecontent(sout);
                    return FAILURE_AND_CONTINUE;
                }else{
                    return SUCCESS;
                }
            }else{
                return SUCCESS;
            }
        }else{
            rm.setMessagecontent("sql 执行出错 " +select.getExceptionMsg()+sqlselect);
            return FAILURE_AND_CONTINUE;
        }

    }

    public String getTimes() {
        return times;
    }

    public void setTimes(String times) {
        this.times = times;
    }

    public String getSqrcol() {
        return sqrcol;
    }

    public void setSqrcol(String sqrcol) {
        this.sqrcol = sqrcol;
    }

    public String getSqrq() {
        return sqrq;
    }

    public void setSqrq(String sqrq) {
        this.sqrq = sqrq;
    }

    public String getSout() {
        return sout;
    }

    public void setSout(String sout) {
        this.sout = sout;
    }
}
