package com.engine.oa4erp.service.Impl;
import com.engine.oa4erp.cmd.SearchErpCmd;
import com.engine.core.impl.Service;
import com.engine.oa4erp.service.SearchErpService;
import weaver.hrm.User;

import java.util.Map;


public class SearchErpServiceImpl extends Service implements SearchErpService {
    @Override
    public Map<String, Object> searchservice(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SearchErpCmd(params, user));
    }
}
