package com.engine.finance.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 借款和冲销action
 *
 * <AUTHOR>
 */
public class BorrowAndMeltAction extends BaseBean implements Action {

    /**
     * 初始金额字段名
     */
    private static final String CSJE_FIELD = "CSJE";
    /**
     * 借款操作名称
     */
    private static final String BORROW_ACTION = "borrow";
    /**
     * 冲销操作名称
     */
    private static final String MELT_ACTION = "melt";

    /**
     * 零值
     */
    private static final String ZERO_STR = "0";
    /**
     * 建模主表名
     */
    private String moduleMainTable;
    /**
     * 建模模块id
     */
    private String moduleId;
    /**
     * 操作名称
     */
    private String actionName;

    /**
     * 是否是否冲销借款字段名
     */
    private String chongXiaoField;

    @Override
    public String execute(RequestInfo requestInfo) {
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();
        writeLog(this.getClass().getName() + "---START");
        try {
            //请求id
            String requestId = requestInfo.getRequestid();
            //获取主表信息
            Property[] propertys = requestInfo.getMainTableInfo().getProperty();
            Map<String, String> map = new HashMap<>(6);
            for (Property property : propertys) {
                String str = property.getName();
                String value = Util.null2String(property.getValue());
                map.put(str, value);
            }
            writeLog("map");
            writeLog(map);
            //主表数据id
            int mainid;
            //申请人
            int applyer = -1;
            if (BORROW_ACTION.equals(actionName)) {
                //借款
                applyer = Util.getIntValue(map.get("sqr"));
            } else if (MELT_ACTION.equals(actionName)) {
                //冲销
                applyer = Util.getIntValue(map.get("gsyfkdx"));
            }
            //查询建模里申请人是否存在，存在则更新数据，不存在则插入数据
            mainid = queryMainData(applyer);
            writeLog("mainid: ");
            writeLog(mainid);
            if (mainid != -1) {
                //人存在，需要插入明细信息，并更新主表
                //获取已存在的明细数据
                BigDecimal jkje = getSumJkje(mainid);
                BigDecimal hkje = getSumHkje(mainid);
                JSONObject mainData = getMainData(mainid);
                BigDecimal csje = new BigDecimal(0);
                if (mainData.containsKey(CSJE_FIELD)) {
                    csje = SDUtil.getBigDecimalValue(mainData.getString(CSJE_FIELD));
                }
                writeLog("csje:" + csje);
                //借款
                if (BORROW_ACTION.equals(actionName)) {
                    //先插入明细表一条数据
                    insertDetail1Data(mainid, requestId, map);
                    //加上本次流程的借款金额+初始金额
                    jkje = jkje.add(SDUtil.getBigDecimalValue(map.get("sjjkjx"))).add(csje);
                    //计算剩余金额
                    BigDecimal syje = jkje.subtract(hkje);
                    //更新主表
                    updateMainData(mainid, jkje, hkje, syje);
                } else if (MELT_ACTION.equals(actionName)) {
                    String chongXiaoValue = map.get(chongXiaoField);
                    writeLog("chongXiaoValue");
                    writeLog(chongXiaoValue);
                    //插入还款金额时，需判断主表已勾选是否冲销，是的情况才插入明细2
                    if (ZERO_STR.equals(chongXiaoValue)) {
                        //还款
                        insertDetail2Data(mainid, requestId, map);
                        //借款金额+初始金额
                        jkje = jkje.add(csje);
                        //加上本次流程的还款金额
                        hkje = hkje.add(SDUtil.getBigDecimalValue(map.get("bccxje")));
                        //计算剩余金额
                        BigDecimal syje = jkje.subtract(hkje);
                        //更新主表
                        updateMainData(mainid, jkje, hkje, syje);
                    }
                }

            } else {
                //人不存在
                if (BORROW_ACTION.equals(actionName)) {
                    //插入主表
                    mainid = inserMainData(applyer, map);
                    //插入明细1
                    insertDetail1Data(mainid, requestId, map);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            writeLog("Exception:" + e.getMessage());
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent("执行出错: " + e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
        writeLog(this.getClass().getName() + "---END");
        return Action.SUCCESS;
    }

    public int queryMainData(int userId) {
        RecordSet rs = new RecordSet();
        boolean recordFlag = rs.executeQuery("select id from " + moduleMainTable + " where sqr = ? ", userId);
        if (!recordFlag) {
            throw new RuntimeException(rs.getMsg());
        } else {
            if (rs.next()) {
                return rs.getInt("ID");
            } else {
                return -1;
            }
        }
    }

    /**
     * 插入主表
     *
     * @param userId 用户id
     * @param map    数据map
     * @return 返回插入数据id
     */
    public int inserMainData(int userId, Map<String, String> map) {
        int mainId;
        double zero = 0.00;
        List<String> insertFields = new ArrayList<>();
        //申请人
        insertFields.add("sqr");
        //人员编号
        insertFields.add("rybh");
        //部门
        insertFields.add("sqbm");
        //借款总额
        insertFields.add("jkze");
        //还款总额
        insertFields.add("hkze");
        //剩余借款
        insertFields.add("syjk");
        //初始金额
        insertFields.add("csje");
        List<Object> values;
        values = new ArrayList<>();
        values.add(userId);
        values.add(map.get("rybh"));
        values.add(Util.getIntValue(map.get("sqbm")));
        //借款
        values.add(SDUtil.getBigDecimalValue(map.get("sjjkjx")));
        values.add(zero);
        values.add(SDUtil.getBigDecimalValue(map.get("sjjkjx")));
        values.add(zero);
        try {
            mainId = InsertModuleUtil.ModuleInsert(moduleMainTable, insertFields, values, userId, Util.getIntValue(moduleId), null);
            if (mainId == -1) {
                throw new RuntimeException("插入建模数据失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
        return mainId;
    }

    /**
     * 插入明细1 借款
     *
     * @param mainid    主表id
     * @param requestId 请求id
     * @param map       map
     */
    public void insertDetail1Data(int mainid, String requestId, Map<String, String> map) {
        List<String> insertFields = new ArrayList<>();
        //主表id
        insertFields.add("mainid");
        //借款日期
        insertFields.add("jkrq");
        //借款金额
        insertFields.add("jkje");
        //借款流程
        insertFields.add("jklc");
        List<Object> values;
        values = new ArrayList<>();
        values.add(mainid);
        values.add(map.get("sqsj"));
        values.add(SDUtil.getBigDecimalValue(map.get("sjjkjx")));
        values.add(requestId);
        String[] arr = insertFields.toArray(new String[insertFields.size()]);
        try {
            InsertModuleUtil.DetailModuleInsert(moduleMainTable + "_dt1", arr, values);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 插入明细2 还款
     *
     * @param mainid    主表id
     * @param requestId 请求id
     * @param map       数据map
     */
    public void insertDetail2Data(int mainid, String requestId, Map<String, String> map) {
        List<String> insertFields = new ArrayList<>();
        //主表id
        insertFields.add("mainid");
        //冲销日期
        insertFields.add("hkrq");
        //冲销金额
        insertFields.add("hkje");
        //冲销流程
        insertFields.add("hklc");
        List<Object> values;
        values = new ArrayList<>();
        values.add(mainid);
        values.add(map.get("sqsj"));
        values.add(SDUtil.getBigDecimalValue(map.get("bccxje")));
        values.add(requestId);
        String[] arr = insertFields.toArray(new String[insertFields.size()]);
        try {
            InsertModuleUtil.DetailModuleInsert(moduleMainTable + "_dt2", arr, values);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }


    /**
     * 更新主表数据
     *
     * @param mainid 主表id
     * @param jkje   借款金额
     * @param hkje   还款金额
     * @param syje   剩余金额
     */
    public void updateMainData(int mainid, BigDecimal jkje, BigDecimal hkje, BigDecimal syje) {
        RecordSet rs = new RecordSet();
        List<Object> sqlParamsList = new ArrayList<>();
        sqlParamsList.add(jkje);
        sqlParamsList.add(hkje);
        sqlParamsList.add(syje);
        sqlParamsList.add(mainid);
        boolean recordFlag = rs.executeUpdate("update " + moduleMainTable + " set jkze= ?, hkze= ?, syjk = ? where id = ? ", sqlParamsList);
        if (!recordFlag) {
            writeLog(rs.getMsg());
            throw new RuntimeException(rs.getMsg());
        }
    }

    /**
     * 获取主表数据
     *
     * @param mainid 主表id
     * @return 返回主表数据
     */
    public JSONObject getMainData(int mainid) {
        JSONObject result = new JSONObject();
        RecordSet rs = new RecordSet();
        String sql = " select * from " + moduleMainTable + " where id = ? ";
        rs.executeQuery(sql, mainid);
        if (rs.next()) {
            result = QueryResultUtil.getJSONArrayList(rs).getJSONObject(0);
        }
        return result;
    }

    /**
     * 获取借款金额合计
     *
     * @param mainid 主表id
     * @return 返回借款金额合计
     */
    public BigDecimal getSumJkje(int mainid) {
        BigDecimal result = new BigDecimal(0);
        RecordSet rs = new RecordSet();
        String sql = " select * from " + moduleMainTable + "_dt1 where mainid = ? ";
        rs.executeQuery(sql, mainid);
        while (rs.next()) {
            result = result.add(SDUtil.getBigDecimalValue(rs.getString("jkje")));
        }
        return result;
    }

    /**
     * 获取还款金额合计
     *
     * @param mainid 主表id
     * @return 返回还款金额合计
     */
    public BigDecimal getSumHkje(int mainid) {
        BigDecimal result = new BigDecimal(0);
        RecordSet rs = new RecordSet();
        String sql = " select * from " + moduleMainTable + "_dt2 where mainid = ? ";
        rs.executeQuery(sql, mainid);
        while (rs.next()) {
            result = result.add(SDUtil.getBigDecimalValue(rs.getString("hkje")));
        }
        return result;
    }

    public String getModuleMainTable() {
        return moduleMainTable;
    }

    public void setModuleMainTable(String moduleMainTable) {
        this.moduleMainTable = moduleMainTable;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getChongXiaoField() {
        return chongXiaoField;
    }

    public void setChongXiaoField(String chongXiaoField) {
        this.chongXiaoField = chongXiaoField;
    }
}
