package com.engine.finance.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 还款流程action
 *
 * <AUTHOR>
 */
public class FundRepaymentAction extends BaseBean implements Action {

    /**
     * 初始金额字段名
     */
    private static final String CSJE_FIELD = "CSJE";
    /**
     * 剩余借款字段名
     */
    private static final String SYJK_FIELD = "SYJK";
    /**
     * 建模主表名
     */
    private String moduleMainTable;
    /**
     * 建模模块id
     */
    private String moduleId;
    /**
     * 还款类型字段名
     */
    private String repayTypeField;
    /**
     * 转移人字段名
     */
    private String transferField;
    /**
     * 申请人字段名
     */
    private String sqrField;
    /**
     * 剩余借款金额字段名
     */
    private String leftBorrowAmountField;
    /**
     * 还款金额字段名
     */
    private String repayAmountField;


    @Override
    public String execute(RequestInfo requestInfo) {
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();
        writeLog(this.getClass().getName() + "---START");
        String erroMsg = "";
        int zyrMainId = -1;
        int sqrMainId;
        try {
            //请求id
            String requestId = requestInfo.getRequestid();
            int repayType = -1, transfer = -1, sqr = -1;
            BigDecimal leftBorrowAmount = BigDecimal.valueOf(-1);
            BigDecimal repayAmount = BigDecimal.valueOf(-1);
            //获取主表信息
            Property[] propertys = requestInfo.getMainTableInfo().getProperty();
            Map<String, String> mapProp = new HashMap<>(6);
            for (Property property : propertys) {
                if (repayTypeField.equals(property.getName())) {
                    repayType = Util.getIntValue(property.getValue());
                }
                if (transferField.equals(property.getName())) {
                    transfer = Util.getIntValue(property.getValue());
                }
                if (leftBorrowAmountField.equals(property.getName())) {
                    leftBorrowAmount = SDUtil.getBigDecimalValue(property.getValue());
                }
                if (repayAmountField.equals(property.getName())) {
                    repayAmount = SDUtil.getBigDecimalValue(property.getValue());
                }
                if (sqrField.equals(property.getName())) {
                    sqr = Util.getIntValue(property.getValue());
                }
                mapProp.put(property.getName(), Util.null2String(property.getValue()));
            }
            //STEP 1 : 校验参数配置
            if (repayTypeField == null || "".equals(repayTypeField) || repayType == -1) {
                erroMsg = "还款类型字段名参数未正确配置，请检查！";
            }
            if (leftBorrowAmountField == null || "".equals(leftBorrowAmountField)) {
                erroMsg = "剩余借款金额字段名参数未正确配置，请检查！";
            }
            if (repayAmountField == null || "".equals(repayAmountField)) {
                erroMsg = "还款金额字段名参数未正确配置，请检查！";
            }
            if (moduleMainTable == null || "".equals(moduleMainTable)) {
                erroMsg = "建模主表名参数未正确配置，请检查！";
            }
            if (moduleId == null || "".equals(moduleId)) {
                erroMsg = "建模模块id参数未正确配置，请检查！";
            }
            if (sqrField == null || "".equals(sqrField) || sqr == -1) {
                erroMsg = "申请人参数未正确配置，请检查！";
            }
            if (!"".equals(erroMsg)) {
                //流程提交失败信息编号
                rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
                //流程提交失败信息内容
                rm.setMessagecontent("校验出错: " + erroMsg);
                return Action.FAILURE_AND_CONTINUE;
            }
            //当还款类型选择转移时，校验转移
            if (repayType == 1) {
                //校验参数
                if (transferField == null || "".equals(transferField) || transfer == -1) {
                    erroMsg = "转移人字段名参数未正确配置，请检查！";
                }
            }
            if (!"".equals(erroMsg)) {
                //流程提交失败信息编号
                rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
                //流程提交失败信息内容
                rm.setMessagecontent("校验出错: " + erroMsg);
                return Action.FAILURE_AND_CONTINUE;
            }
            writeLog("STEP1 参数校验完成");
            writeLog("repayType： " + repayType);
            writeLog("leftBorrowAmount ：" + leftBorrowAmount);
            writeLog("repayAmount ：" + repayAmount);
            writeLog("mapProp ：" + mapProp);

            //实例化 【BorrowAndMeltAction】，需要用到其中的一些方法
            BorrowAndMeltAction bam = new BorrowAndMeltAction();
            bam.setModuleMainTable(moduleMainTable);
            bam.setModuleId(moduleId);
            //STEP 2 : 校验业务逻辑

            //根据申请人查询申请人的台账
            sqrMainId = bam.queryMainData(sqr);
            if (sqrMainId == -1) {
                erroMsg = "未在备用金台账中找到申请人信息，请检查！";
            }
            if (!"".equals(erroMsg)) {
                //流程提交失败信息编号
                rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
                //流程提交失败信息内容
                rm.setMessagecontent("校验出错: " + erroMsg);
                return Action.FAILURE_AND_CONTINUE;
            }

            //获取申请人的台账信息
            //获取申请人已存在的台账明细数据
            BigDecimal jkjeSqr = bam.getSumJkje(sqrMainId);
            BigDecimal hkjeSqr = bam.getSumHkje(sqrMainId);
            JSONObject mainDataSqr = bam.getMainData(sqrMainId);
            writeLog("申请人台账信息：" + mainDataSqr.toJSONString());
            BigDecimal csjeSqr = new BigDecimal(0);
            if (mainDataSqr.containsKey(CSJE_FIELD)) {
                csjeSqr = SDUtil.getBigDecimalValue(mainDataSqr.getString(CSJE_FIELD));
            }
            writeLog("申请人初始金额:" + csjeSqr);
            //校验申请人的剩余借款金额
            BigDecimal leftBorrowAmountEx = SDUtil.getBigDecimalValue(mainDataSqr.getString(SYJK_FIELD));
            writeLog("申请人的剩余借款金额：leftBorrowAmountEx: " + leftBorrowAmountEx);
            if (!leftBorrowAmountEx.equals(leftBorrowAmount)) {
                erroMsg = "表单的剩余借款金额，与申请人台账中的剩余借款金额不一致，请检查！";
            }

            // 还款校验，还款金额必须小于等于建模里的【剩余借款金额】
            if (repayType == 0) {
                //校验参数
                if (repayAmount.compareTo(leftBorrowAmountEx) > 0) {
                    erroMsg = "还款金额必须小于等于备用金台账中的剩余借款金额，请检查！";
                }
            }

            if (!"".equals(erroMsg)) {
                //流程提交失败信息编号
                rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
                //流程提交失败信息内容
                rm.setMessagecontent("校验出错: " + erroMsg);
                return Action.FAILURE_AND_CONTINUE;
            }

            writeLog("STEP2 业务逻辑校验完成");
            // STEP 3 : 处理业务逻辑

            if (repayType == 0) {
                //还款
                //插入台账明细2
                Map<String, String> map = new HashMap<>(2);
                //还款日期
                map.put("sqsj", mapProp.get("sqrq"));
                //还款金额
                map.put("bccxje", String.valueOf(repayAmount));
                writeLog("还款insertDetail2Data：map " + map);
                bam.insertDetail2Data(sqrMainId, requestId, map);

                //借款金额+初始金额
                BigDecimal jkjeHk = jkjeSqr.add(csjeSqr);
                //加上本次流程的还款金额
                BigDecimal hkjeHk = hkjeSqr.add(repayAmount);
                //计算剩余金额
                BigDecimal syje = jkjeHk.subtract(hkjeHk);
                //更新主表
                writeLog("还款updateMainData： " + sqrMainId + "," + jkjeHk + "," + hkjeHk + "," + syje);
                bam.updateMainData(sqrMainId, jkjeHk, hkjeHk, syje);
            } else if (repayType == 1) {
                //转移
                Map<String, String> map = new HashMap<>(4);
                //人员编号
                map.put("rybh", getWorkCode(transfer));
                //申请部门
                map.put("sqbm", mapProp.get("sqbm"));
                //申请日期
                map.put("sqsj", mapProp.get("sqrq"));
                //借款金额
                map.put("sjjkjx", String.valueOf(leftBorrowAmount));

                //先查询转移人是否有台账信息，没有则需要增加
                zyrMainId = bam.queryMainData(transfer);
                writeLog("transfer:" + transfer);
                writeLog("zyrMainId:" + zyrMainId);
                if (zyrMainId == -1) {
                    //插入主表
                    writeLog("转移inserMainData：transfer,map: " + transfer + "," + map);
                    int newId = bam.inserMainData(transfer, map);
                    //插入明细1
                    writeLog("转移insertDetail1Data：newId,requestId,map: " + newId + "," + requestId + "," + map);
                    bam.insertDetail1Data(newId, requestId, map);
                } else {
                    //获取转移人已存在的明细数据
                    BigDecimal jkje = bam.getSumJkje(zyrMainId);
                    BigDecimal hkje = bam.getSumHkje(zyrMainId);
                    JSONObject mainData = bam.getMainData(zyrMainId);
                    BigDecimal csje = new BigDecimal(0);
                    if (mainData.containsKey(CSJE_FIELD)) {
                        csje = SDUtil.getBigDecimalValue(mainData.getString(CSJE_FIELD));
                    }
                    writeLog("转移人台账信息：" + mainData.toJSONString());
                    writeLog("转移人的csje： " + csje);
                    //转移人存在的情况
                    writeLog("转移人存在insertDetail1Data：zyrMainId，requestId，map:" +
                            " " + zyrMainId + "," + requestId + "," + map);
                    //先插入转移人明细表一条数据
                    bam.insertDetail1Data(zyrMainId, requestId, map);
                    //再更新转移人的主表信息
                    //加上本次流程的借款金额+初始金额
                    BigDecimal jkjeTotal = jkje.add(csje).add(leftBorrowAmount);
                    //计算剩余金额
                    BigDecimal syje = jkjeTotal.subtract(hkje);
                    //更新转移人主表
                    writeLog("转移人存在updateMainData：zyrMainId，jkjeTotal，hkjeTotal,syje:" +
                            " " + zyrMainId + "," + jkjeTotal + "," + hkje + "," + syje);
                    bam.updateMainData(zyrMainId, jkjeTotal, hkje, syje);
                }
                //再更新申请人的台账信息,把申请人的剩余借款金额清空，插入明细2
                //插入台账明细2
                map = new HashMap<>(2);
                //还款日期
                map.put("sqsj", mapProp.get("sqrq"));
                //还款金额
                map.put("bccxje", String.valueOf(leftBorrowAmount));
                writeLog("转移人存在申请人insertDetail2Data：sqrMainId，requestId，map:" +
                        " " + sqrMainId + "," + requestId + "," + map);
                bam.insertDetail2Data(sqrMainId, requestId, map);
                //借款金额+初始金额
                BigDecimal jkjeTotal2 = jkjeSqr.add(csjeSqr);
                //加上本次流程的还款金额
                BigDecimal hkje2 = hkjeSqr.add(leftBorrowAmount);
                //计算剩余金额
                BigDecimal syje2 = jkjeTotal2.subtract(hkje2);
                //更新主表
                writeLog("转移人存在申请人updateMainData：sqrMainId，jkjeTotal2，hkje2,syje2:" +
                        " " + sqrMainId + "," + jkjeTotal2 + "," + hkje2 + "," + syje2);
                bam.updateMainData(sqrMainId, jkjeTotal2, hkje2, syje2);
            }

        } catch (Exception e) {
            e.printStackTrace();
            writeLog("Exception:" + e.getMessage());
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent("执行出错: " + e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
        writeLog(this.getClass().getName() + "---END");
        return Action.SUCCESS;
    }


    /**
     * 获取人员编号
     *
     * @param userId
     * @return
     */
    private String getWorkCode(int userId) {
        RecordSet rs = new RecordSet();
        String sql = "select workcode from hrmresource where id = ?";
        rs.executeQuery(sql, userId);
        if (rs.next()) {
            return rs.getString("WORKCODE");
        } else {
            return "";
        }
    }


    public String getModuleMainTable() {
        return moduleMainTable;
    }

    public void setModuleMainTable(String moduleMainTable) {
        this.moduleMainTable = moduleMainTable;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }


    public String getRepayTypeField() {
        return repayTypeField;
    }

    public void setRepayTypeField(String repayTypeField) {
        this.repayTypeField = repayTypeField;
    }

    public String getTransferField() {
        return transferField;
    }

    public void setTransferField(String transferField) {
        this.transferField = transferField;
    }

    public String getLeftBorrowAmountField() {
        return leftBorrowAmountField;
    }

    public void setLeftBorrowAmountField(String leftBorrowAmountField) {
        this.leftBorrowAmountField = leftBorrowAmountField;
    }

    public String getRepayAmountField() {
        return repayAmountField;
    }

    public void setRepayAmountField(String repayAmountField) {
        this.repayAmountField = repayAmountField;
    }

    public String getSqrField() {
        return sqrField;
    }

    public void setSqrField(String sqrField) {
        this.sqrField = sqrField;
    }
}
