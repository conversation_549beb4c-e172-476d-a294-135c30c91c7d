package com.engine.contract.action;

import weaver.hrm.User;

import java.math.BigDecimal;

public class ExecuteData {

    private String type;

    private BigDecimal hadpaid;

    private BigDecimal nopaid;

    private User user;

    private String detailid;

    private String addorreduce;

    public ExecuteData(String type, BigDecimal hadpaid, BigDecimal nopaid, User user, String detailid, String addorreduce) {
        this.type = type;
        this.hadpaid = hadpaid;
        this.nopaid = nopaid;
        this.user = user;
        this.detailid = detailid;
        this.addorreduce = addorreduce;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getHadpaid() {
        return hadpaid;
    }

    public void setHadpaid(BigDecimal hadpaid) {
        this.hadpaid = hadpaid;
    }

    public BigDecimal getNopaid() {
        return nopaid;
    }

    public void setNopaid(BigDecimal nopaid) {
        this.nopaid = nopaid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getDetailid() {
        return detailid;
    }

    public void setDetailid(String detailid) {
        this.detailid = detailid;
    }

    public String getAddorreduce() {
        return addorreduce;
    }

    public void setAddorreduce(String addorreduce) {
        this.addorreduce = addorreduce;
    }
}
