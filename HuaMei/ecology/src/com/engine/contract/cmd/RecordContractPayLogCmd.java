package com.engine.contract.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.pojo.ContracPayLog;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.module.util.InsertModuleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 记录合同付款日志CMD
 *
 * <AUTHOR>
 */
public class RecordContractPayLogCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final ContracPayLog log;

    public RecordContractPayLogCmd(ContracPayLog log, User user) {
        this.log = log;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        result.put("status", 1);
        try {
            String logTableName = Util.null2String(bb.getPropValue("huamei_module", "ContractPayLog_Table"));
            int logTableModuleId = Util.getIntValue(bb.getPropValue("huamei_module", "ContractPayLog_Module"));
            List<String> insertFields = new ArrayList<>();
            insertFields.add("htmc");
            insertFields.add("fklc");
            insertFields.add("lcbh");
            insertFields.add("sqr");
            insertFields.add("sqbm");
            insertFields.add("sqrq");
            insertFields.add("sqje");
            insertFields.add("lczt");
            bb.writeLog("insertFields:" + insertFields);
            List<Object> values;
            int creatId = user.getUID();
            bb.writeLog("log:" + log);
            //先根据流程id更新数据，如果更新成功则跳过插入
            int updateCount = updateLog(log, logTableName);
            if (updateCount < 1) {
                values = new ArrayList<>();
                values.add(log.getHtmc());
                values.add(log.getFklc());
                values.add(log.getLcbh());
                values.add(log.getSqr());
                values.add(log.getSqbm());
                values.add(log.getSqrq());
                values.add(log.getSqje());
                values.add(log.getLczt());
                bb.writeLog("values:" + values);
                InsertModuleUtil.ModuleInsert(logTableName, insertFields, values, creatId, logTableModuleId, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            writeLog("Exception：" + e.getMessage());
            result.put("status", -1);
            result.put("msg", e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 更新log
     *
     * @param log
     * @param tableName
     * @return
     */
    private int updateLog(ContracPayLog log, String tableName) {
        BaseBean bb = new BaseBean();
        RecordSet rs = new RecordSet();
        String sql = " update " + tableName + " set lczt = ?, sqje = ? ,lcbh = ? where fklc = ? ";
        bb.writeLog("sql :" + sql);
        bb.writeLog("lczt :" + log.getLczt());
        bb.writeLog("sqje :" + log.getSqje());
        bb.writeLog("lcbh :" + log.getLcbh());
        bb.writeLog("fklc :" + log.getFklc());
        boolean flag = rs.executeUpdate(sql, log.getLczt(), log.getSqje(), log.getLcbh(), log.getFklc());
        bb.writeLog("flag :" + flag);
        return rs.getUpdateCount();
    }
}
