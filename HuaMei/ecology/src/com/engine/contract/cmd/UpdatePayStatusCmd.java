package com.engine.contract.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * 更新合同付款明细状态
 *
 * <AUTHOR>
 */
public class UpdatePayStatusCmd extends AbstractCommonCommand<Map<String, Object>> {


    public UpdatePayStatusCmd(Map<String, Object> map, User user) {
        this.params = map;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        result.put("status", 1);
        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);
        try {
            String detailTableName = Util.null2String(params.get("tableName"));
            int id = Util.getIntValue(Util.null2String(params.get("id")));
            int status = Util.getIntValue(Util.null2String(params.get("status")));
            rst.executeUpdate("update " + detailTableName + " set fkzt = ? where id = ? ", status, id);
            rst.commit();
        } catch (Exception e) {
            e.printStackTrace();
            rst.rollback();
            writeLog("Exception：" + e.getMessage());
            result.put("status", -1);
            result.put("msg", e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}
