package com.engine.contract.pojo;

import java.math.BigDecimal;

/**
 * 合同付款日志bean
 *
 * <AUTHOR>
 */
public class ContracPayLog {
    /**
     * 合同名称
     */
    private String htmc;
    /**
     * 付款流程请求id
     */
    private BigDecimal fklc;
    /**
     * 流程编号
     */
    private String lcbh;
    /**
     * 申请人
     */
    private int sqr;
    /**
     * 申请部门
     */
    private int sqbm;
    /**
     * 申请日期
     */
    private String sqrq;
    /**
     * 申请金额
     */
    private BigDecimal sqje;
    /**
     * 流程状态
     */
    private int lczt;

    public String getHtmc() {
        return htmc;
    }

    public void setHtmc(String htmc) {
        this.htmc = htmc;
    }

    public BigDecimal getFklc() {
        return fklc;
    }

    public void setFklc(BigDecimal fklc) {
        this.fklc = fklc;
    }

    public String getLcbh() {
        return lcbh;
    }

    public void setLcbh(String lcbh) {
        this.lcbh = lcbh;
    }

    public int getSqr() {
        return sqr;
    }

    public void setSqr(int sqr) {
        this.sqr = sqr;
    }

    public int getSqbm() {
        return sqbm;
    }

    public void setSqbm(int sqbm) {
        this.sqbm = sqbm;
    }

    public String getSqrq() {
        return sqrq;
    }

    public void setSqrq(String sqrq) {
        this.sqrq = sqrq;
    }

    public BigDecimal getSqje() {
        return sqje;
    }

    public void setSqje(BigDecimal sqje) {
        this.sqje = sqje;
    }

    public int getLczt() {
        return lczt;
    }

    public void setLczt(int lczt) {
        this.lczt = lczt;
    }
}
