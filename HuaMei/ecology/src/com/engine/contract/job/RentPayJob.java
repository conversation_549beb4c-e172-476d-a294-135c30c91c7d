package com.engine.contract.job;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.engine.parent.workflow.util.WorkFlowUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 租赁付款job
 */
public class RentPayJob extends BaseCronJob {
    /**
     * 建模表单名称
     */
    private String moudleTableName;
    /**
     * 提前几天检查台账生成流程
     */
    private String beforeDays;
    /**
     * 流程id
     */
    private String workflowId;
    /**
     * 流程标题
     */
    private String requestName;
    /**
     * 基类
     */
    private BaseBean bb;

    /**
     * 特定的角色id，用于创建流程的创建人
     */
    private String roleId;

    /**
     * 租赁类别选项，用于判断创建流程使用申请人为创建人，多个逗号隔开
     */
    private String sqrTypes;

    /**
     * 初始化
     */
    private void init() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        init();
        bb.writeLog(this.getClass().getName() + "---START");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String retCreate;
        try {
            //获取付款日期前14天的台账
            RecordSet rs = new RecordSet();
            int beforeDaysInt = Integer.parseInt(beforeDays);
            Date date = new Date();
            String dateStr = sdf.format(date);
            String beforeDate = TimeUtil.dateAdd(dateStr, beforeDaysInt);
            bb.writeLog("查询的付款日期 :" + beforeDate);
            String sql = "select * from " + moudleTableName + " where fkrq = ? and fklc is null " +
                    " and （sfyfk = 1 or sfyfk is null) and (sfxcffk = 0 or sfxcffk is null) ";
            bb.writeLog("select sql :" + sql);
            rs.executeQuery(sql, beforeDate);
            Map<String, Object> mapKey = new HashMap<>(6);
            com.alibaba.fastjson.JSONArray jaResult = QueryResultUtil.getJSONArrayList(rs);
            //相同合同号的，创建同一个流程
            while (rs.next()) {
                if (!"".equals(Util.null2String(rs.getString("htmc")))) {
                    mapKey.put(rs.getString("htmc"), "");
                }
            }
            for (Map.Entry<String, Object> entry : mapKey.entrySet()) {
                System.out.println("key: " + entry.getKey() + "; value: " + entry.getValue());
                retCreate = createWorkflow(jaResult, entry.getKey());
                if ("erro".equals(retCreate)) {
                    bb.writeLog("创建流程失败：" + rs.getString("id"));
                    break;
                }
                if (SDUtil.getBigDecimalValue(retCreate).compareTo(BigDecimal.valueOf(0)) < 0) {
                    bb.writeLog("创建流程失败，失败代码：" + retCreate);
                    break;
                }
                //更新台账
                updateRentPayTable(entry.getKey(), retCreate, beforeDate);
            }

        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

    /**
     * 创建根据每条查询出的建模数据创建流程
     *
     * @param jaResult
     * @param htmc
     * @return
     */
    private String createWorkflow(com.alibaba.fastjson.JSONArray jaResult, String htmc) {
        String retRequest;
        int creater = -1;
        JSONObject jo;
        JSONArray ja;
        JSONArray mainData = new JSONArray();
        JSONObject baseInfo = new JSONObject();
        JSONArray detailData = new JSONArray();
        baseInfo.set("workflowId", workflowId);
        baseInfo.set("requestName", requestName);
        baseInfo.set("isNextFlow", "0");

        //租赁类型
        String rentType = "";


        //合同号
        jo = new JSONObject();
        jo.set("name", "htmc");
        jo.set("value", htmc);
        jo.set("htmlType", "3");
        jo.set("type", "");
        mainData.add(jo);


        //明细信息
        com.alibaba.fastjson.JSONArray detailWorkflow = getDetailData(jaResult, htmc);
        com.alibaba.fastjson.JSONObject joDetail;

        if (!detailWorkflow.isEmpty()) {
            for (int i = 0; i < detailWorkflow.size(); i++) {
                joDetail = detailWorkflow.getJSONObject(i);
                creater = joDetail.getIntValue("SQR");
                //根据合同号查询，租赁类别
                rentType = getRentType(htmc);
                bb.writeLog("rentType:" + rentType);
                ja = new JSONArray();
                jo = new JSONObject();
                //费用明细
                jo.set("name", "fymx");
                jo.set("value", joDetail.getString("FYMX"));
                jo.set("htmlType", "5");
                jo.set("type", "");
                ja.add(jo);

                //付款节点
                jo = new JSONObject();
                jo.set("name", "fkjd");
                jo.set("value", joDetail.getString("FKJD"));
                jo.set("htmlType", "5");
                jo.set("type", "");
                ja.add(jo);

                //付款日期
                jo = new JSONObject();
                jo.set("name", "fkrq");
                jo.set("value", joDetail.getString("FKRQ"));
                jo.set("htmlType", "3");
                jo.set("type", "2");
                ja.add(jo);

                //开始日期
                jo = new JSONObject();
                jo.set("name", "ksrq");
                jo.set("value", joDetail.getString("KSRQ"));
                jo.set("htmlType", "3");
                jo.set("type", "2");
                ja.add(jo);

                //结束日期
                jo = new JSONObject();
                jo.set("name", "jsrq");
                jo.set("value", joDetail.getString("JSRQ"));
                jo.set("htmlType", "3");
                jo.set("type", "2");
                ja.add(jo);

                //成本中心
                jo = new JSONObject();
                jo.set("name", "cbzx");
                jo.set("value", joDetail.getString("CBZX"));
                jo.set("htmlType", "3");
                jo.set("type", "");
                ja.add(jo);

                //付款金额
                jo = new JSONObject();
                jo.set("name", "fkje");
                jo.set("value", joDetail.getString("FKJE"));
                jo.set("htmlType", "1");
                jo.set("type", "");
                ja.add(jo);

                //应付总额
                jo = new JSONObject();
                jo.set("name", "yfze");
                jo.set("value", joDetail.getString("YKZE"));
                jo.set("htmlType", "1");
                jo.set("type", "");
                ja.add(jo);


                //发票类型
                jo = new JSONObject();
                jo.set("name", "fplx");
                jo.set("value", joDetail.getString("FPLX"));
                jo.set("htmlType", "5");
                jo.set("type", "");
                ja.add(jo);

                //税率
                jo = new JSONObject();
                jo.set("name", "sl");
                jo.set("value", joDetail.getString("SL"));
                jo.set("htmlType", "5");
                jo.set("type", "");
                ja.add(jo);

                //净额
                jo = new JSONObject();
                jo.set("name", "je");
                jo.set("value", joDetail.getString("JE"));
                jo.set("htmlType", "1");
                jo.set("type", "");
                ja.add(jo);

                detailData.add(ja);
            }
            //根据租赁类别判断创建流程的创建人,如果租赁类型属于参数的类型值，则创建的流程使用申请人，反之，用特定角色的人
            boolean flag = false;
            if (StringUtils.isNotBlank(sqrTypes) && sqrTypes.contains(CommonCst.COMMA_EN)) {
                String[] arry = sqrTypes.split(CommonCst.COMMA_EN);
                for (String str : arry) {
                    if (str.equals(rentType)) {
                        flag = true;
                        break;
                    }
                }
            } else {
                if (sqrTypes.equals(rentType)) {
                    flag = true;
                }
            }
            bb.writeLog("flag:" + flag);
            //如果是指定租赁类型的，需要使用特定角色id下的人作为创建人
            if (flag) {
                RecordSet rs = new RecordSet();
                rs.executeQuery("select resourceid from HrmRoleMembers where roleid = ?", roleId);
                if (rs.next()) {
                    creater = rs.getInt("resourceid");
                    bb.writeLog("resourceid:" + creater);
                }
            }

            //申请人
            jo = new JSONObject();
            jo.set("name", "sqr");
            jo.set("value", creater);
            jo.set("htmlType", "3");
            jo.set("type", "1");
            mainData.add(jo);

            //部门
            jo = new JSONObject();
            jo.set("name", "sqbm");
            jo.set("value", getSqrDep(creater));
            jo.set("htmlType", "3");
            jo.set("type", "4");
            mainData.add(jo);

            //申请日期
            String today = TimeUtil.getToday();
            jo = new JSONObject();
            jo.set("name", "sqrq");
            jo.set("value", today);
            jo.set("htmlType", "3");
            jo.set("type", "2");
            mainData.add(jo);
        }

        retRequest = WorkFlowUtil.createWorkflowRequest(creater, mainData, baseInfo, detailData);
        return retRequest;
    }

    /**
     * 根据合同编号获取明细信息
     *
     * @param jaResult
     * @param htmc
     * @return
     */
    private com.alibaba.fastjson.JSONArray getDetailData(com.alibaba.fastjson.JSONArray jaResult, String htmc) {
        com.alibaba.fastjson.JSONArray ret = new com.alibaba.fastjson.JSONArray();
        com.alibaba.fastjson.JSONObject jo;
        for (int i = 0; i < jaResult.size(); i++) {
            jo = jaResult.getJSONObject(i);
            if (htmc.equals(jo.getString("HTMC"))) {
                ret.add(jo);
            }
        }
        return ret;
    }

    /**
     * 创建流程成功后，更新租赁合同台账
     */
    private void updateRentPayTable(String htmc, String newFlowRequestId, String fkrq) {
//        //更新付款流程字段
//        RecordSet rsNew = new RecordSet();
//        String sql = "update " + moudleTableName + " set fklc = ? where htmc = ? and fklc is null and (sfxcffk = 0 or sfxcffk is null) ";
//        bb.writeLog("update fklc sql:" + sql);
//        rsNew.executeUpdate(sql, newFlowRequestId, htmc);

        //更新新付款流程字段,是否已付款字段
        RecordSet rsNew = new RecordSet();
        String sql = "update " + moudleTableName + " set fklc = ?,sfyfk = 0 where htmc = ? and fkrq = ? and (sfxcffk = 0 or sfxcffk is null) ";
        bb.writeLog("update  sql:" + sql);
        rsNew.executeUpdate(sql, newFlowRequestId, htmc, fkrq);
    }


    /**
     * 获取部门
     *
     * @param userId
     * @return
     */
    private String getSqrDep(int userId) {
        String dep = "";
        RecordSet rs = new RecordSet();
        rs.executeQuery("select DEPARTMENTID from hrmresource where id = ? ", userId);
        if (rs.next()) {
            dep = rs.getString("DEPARTMENTID");
        }
        return dep;
    }

    /**
     * 根据合同id获取租赁类别
     *
     * @param htmc
     * @return
     */
    private String getRentType(String htmc) {
        String rst = "";
        RecordSet rs = new RecordSet();
        rs.executeQuery("select zllb from uf_cdzl where id = ? ", htmc);
        if (rs.next()) {
            rst = rs.getString("zllb");
        }
        return rst;
    }

    public String getMoudleTableName() {
        return moudleTableName;
    }

    public void setMoudleTableName(String moudleTableName) {
        this.moudleTableName = moudleTableName;
    }

    public String getBeforeDays() {
        return beforeDays;
    }

    public void setBeforeDays(String beforeDays) {
        this.beforeDays = beforeDays;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getRequestName() {
        return requestName;
    }

    public void setRequestName(String requestName) {
        this.requestName = requestName;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getSqrTypes() {
        return sqrTypes;
    }

    public void setSqrTypes(String sqrTypes) {
        this.sqrTypes = sqrTypes;
    }
}
