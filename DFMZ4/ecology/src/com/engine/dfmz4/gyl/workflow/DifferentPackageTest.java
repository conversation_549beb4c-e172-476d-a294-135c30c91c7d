package com.engine.dfmz4.gyl.workflow;

// 注意：这个类在不同的包中
// 当前包：com.engine.dfmz4.gyl.workflow
// 目标包：com.engine.dfmz4.gyl.workflow.htlr

import com.engine.dfmz4.gyl.workflow.htlr.PackageExample;

/**
 * 不同包测试类
 * 
 * 当前包名：com.engine.dfmz4.gyl.workflow
 * 目标包名：com.engine.dfmz4.gyl.workflow.htlr
 * 
 * 虽然看起来很相似，但这是两个不同的包！
 */
public class DifferentPackageTest {
    
    public static void testAccess() {
        System.out.println("=== 在不同包的DifferentPackageTest类中访问PackageExample ===");
        
        // ❌ 无法访问private类
        // PackageExample.PrivateClass privateObj = new PackageExample.PrivateClass(); // 编译错误
        
        // ❌ 无法访问package-private类（重点！）
        // PackageExample.PackageClass packageObj = new PackageExample.PackageClass(); // 编译错误
        // 错误信息：PackageClass is not public in PackageExample; cannot be accessed from outside package
        
        // ✅ 可以访问public类
        PackageExample.PublicClass publicObj = new PackageExample.PublicClass();
        System.out.println("public: " + publicObj.data);
        
        // ❌ 无法访问private方法
        // PackageExample.privateMethod(); // 编译错误
        
        // ❌ 无法访问package-private方法（重点！）
        // PackageExample.packageMethod(); // 编译错误
        
        // ✅ 可以访问public方法
        PackageExample.publicMethod();
    }
    
    public static void main(String[] args) {
        testAccess();
    }
}
