package com.engine.dfmz4.gyl.workflow.htlr;


import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @FileName CheckYWLYJDAction.java
 * @Description 合同数据录入流程（收入）流程，业务履约进度校验
 * 用不到了，改为前端ecode校验了
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/9
 */
@Getter
@Setter
public class CheckYWLYJDAction extends BaseSDAction implements Action {
    //Action参数---START---
    /**
     * 流程主表字段-合同金额类型
     * htjelx
     */
    private String field_htjelx;
    /**
     * 流程主表字段-校验结果
     * cwlyjdglywlyjdxy
     */
    private String field_check_result;
    //Action参数---END---

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 自定义公共变量
     * 使用ThreadLocal管理
     */
    private static class Params {
        private String checkResult = "T"; //默认成功 //校验接口  T成功 F失败
    }

    private Params getParam() {
        return getThreadLocalCustomParam(this.getClass(), Params.class, Params::new);
    }


    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), Params.class, "合同数据录入流程（收入）流程，业务履约进度校验");
        if (getActionError().isEmpty()) {
            try {
                //校验参数
                checkParams();
                if (getActionError().isEmpty()) {
                    //执行业务逻辑
                    execuetMy();
                }
            } catch (Exception e) {
                getThreadLocalBaseParam().actionError = "action执行异常:" + SDUtil.getExceptionDetail(e);
                appendLog(getActionError());
                log.error("action执行异常:", e);
            } finally {
                appendLog(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
                appendLog("action默认走成功，不拦截提交");
                //后置执行操作
                afterExecute();
            }
        }
        //action结束返回,这里默认走成功，不拦截
        return actionReturnAlwaysSuccess(this.getClass(), Params.class);
    }

    /**
     * 后置执行操作
     */
    private void afterExecute() {
        try {
            //更新校验结果
            updateCheckResult();
        } catch (Exception e) {
            log.error("后置动作执行异常：", e);
        }
    }

    /**
     * 执行业务逻辑
     */
    private void execuetMy() {
        BigDecimal amount1, amount2;
        String ywlyjd;
        String htjelx = Util.null2String(getThreadLocalBaseParam().wfMainData.get(field_htjelx)); //合同金额类型
        appendLog("合同金额类型:" + htjelx);
        //只有类型为1固定金额时，才需要校验，其他的直接成功
        if ("1".equals(htjelx)) {
            //获取明细4的汇总数据
            String detailTable = getThreadLocalBaseParam().actionInfo.getFormtableName() + "_dt4";
            String sql = "select a.ywlyjd,sum(a.hsje) as hsje,b.skjehs from " + detailTable + " a " +
                    " left join uf_skjh b on (a.ywlcjd = b.id)" +
                    " where 1=1  " +
                    " and a.mainid = ? " +
                    " and a.srqrlx in (0,3) " + // 收入确认类型=按月每月/验收确认
                    " and a.ywlyjd is not null " +
                    " group by a.ywlyjd ";
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            appendLog("查询履约进度明细数据sql:" + sql + ",参数:" + getThreadLocalBaseParam().actionInfo.getId());
            if (rs.executeQuery(sql, getThreadLocalBaseParam().actionInfo.getId())) {
                while (rs.next()) {
                    ywlyjd = Util.null2String(rs.getString("ywlyjd"));
                    amount1 = SDUtil.getBigDecimalValue(rs.getString("hsje"));
                    amount2 = SDUtil.getBigDecimalValue(rs.getString("skjehs"));
                    if (amount1.compareTo(amount2) != 0) {
                        appendLog("业务履约id:" + ywlyjd + ",明细合计金额:" + amount1 + ",收款计划台账金额：" + amount2 + ",不匹配");
                        getParam().checkResult = "F";
                        break;
                    }
                }
            } else {
                appendLog("查询履约进度明细数据sql出错:" + rs.getExceptionMsg());
            }
        }
    }

    /**
     * 更新校验结果
     */
    private void updateCheckResult() {
        List<Object> params = new ArrayList<>();
        params.add(getParam().checkResult);
        params.add(getThreadLocalBaseParam().actionInfo.getId());
        String sql = "update " + getThreadLocalBaseParam().actionInfo.getFormtableName() + " set " + field_check_result + "=? where id=? ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        appendLog("更新校验结果sql:" + sql + ";参数:" + params);
        if (rs.executeUpdate(sql, params)) {
            appendLog("更新校验结果sql成功!");
        } else {
            appendLog("更新校验结果sql失败:" + rs.getExceptionMsg());
        }
    }


    /**
     * 校验参数
     *
     * @return
     */
    private void checkParams() {
        if (StringUtils.isBlank(field_htjelx) || StringUtils.isBlank(field_check_result)) {
            getThreadLocalBaseParam().actionError = "缺失action参数";
            appendLog(getActionError());
        }
    }
}
