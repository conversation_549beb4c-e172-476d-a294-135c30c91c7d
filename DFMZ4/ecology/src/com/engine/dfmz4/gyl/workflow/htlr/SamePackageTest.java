package com.engine.dfmz4.gyl.workflow.htlr;

/**
 * 同包测试类
 * 
 * 包名：com.engine.dfmz4.gyl.workflow.htlr （与PackageExample相同）
 */
public class SamePackageTest {
    
    public static void testAccess() {
        System.out.println("=== 在同包的SamePackageTest类中访问PackageExample ===");
        
        // ❌ 无法访问private类
        // PackageExample.PrivateClass privateObj = new PackageExample.PrivateClass(); // 编译错误
        
        // ✅ 可以访问package-private类
        PackageExample.PackageClass packageObj = new PackageExample.PackageClass();
        System.out.println("package: " + packageObj.data);  // ✅ 可以访问package-private字段
        
        // ✅ 可以访问public类
        PackageExample.PublicClass publicObj = new PackageExample.PublicClass();
        System.out.println("public: " + publicObj.data);
        
        // ❌ 无法访问private方法
        // PackageExample.privateMethod(); // 编译错误
        
        // ✅ 可以访问package-private方法
        PackageExample.packageMethod();
        
        // ✅ 可以访问public方法
        PackageExample.publicMethod();
    }
    
    public static void main(String[] args) {
        testAccess();
    }
}
