package com.engine.dfmz4.gyl.workflow.htlr;


import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName CheckSZXMAction.java
 * @Description 合同数据录入流程（收入）流程，收支项目校验
 * 用不到了，改为前端ecode校验了
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/9
 */
@Getter
@Setter
public class CheckSZXMAction extends BaseSDAction implements Action {
    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    //Action参数---START---
    /**
     * 流程主表字段-合同金额类型
     * htjelx
     */
    private String field_htjelx;
    /**
     * 流程主表字段-校验结果
     * cwlyjdglszxmxy
     */
    private String field_check_result;
    //Action参数---END---

    /**
     * 自定义公共变量
     * 使用ThreadLocal管理
     */
    private static class Params {
        private String checkResult = "T"; //默认成功 //校验接口  T成功 F失败
    }

    private Params getParam() {
        return getThreadLocalCustomParam(this.getClass(), Params.class, Params::new);
    }

    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), Params.class, "合同数据录入流程（收入）流程，收支项目校验");
        if (getActionError().isEmpty()) {
            try {
                //校验参数
                checkParams();
                if (getActionError().isEmpty()) {
                    //执行业务逻辑
                    execuetMy();
                }
            } catch (Exception e) {
                getThreadLocalBaseParam().actionError = "action执行异常:" + SDUtil.getExceptionDetail(e);
                appendLog(getActionError());
                log.error("action执行异常:", e);
            } finally {
                appendLog(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
                appendLog("action默认走成功，不拦截提交");
                //后置执行操作
                afterExecute();
            }
        }
        //action结束返回,这里默认走成功，不拦截
        return actionReturnAlwaysSuccess();
    }

    /**
     * 后置执行操作
     */
    private void afterExecute() {
        try {
            //更新校验结果
            updateCheckResult();
        } catch (Exception e) {
            log.error("后置动作执行异常：", e);
        }
    }

    /**
     * 执行业务逻辑
     */
    private void execuetMy() {
        String htjelx = Util.null2String(getThreadLocalBaseParam().wfMainData.get(field_htjelx)); //合同金额类型
        appendLog("合同金额类型:" + htjelx);
        //只有类型为1固定金额时，才需要校验，其他的直接成功
        if ("1".equals(htjelx)) {
            Map<String, BigDecimal> mapLvYue = getLvYueDetailAmount();
            Map<String, BigDecimal> mapShouzhi = getShouzhiDetailAmount();
            appendLog("履约明细金额map:" + mapLvYue);
            appendLog("收支明细金额map:" + mapShouzhi);
            //遍历履约明细map
            for (Map.Entry<String, BigDecimal> entry : mapLvYue.entrySet()) {
                String key = entry.getKey();
                BigDecimal amount = entry.getValue();
                BigDecimal compareAmount = mapShouzhi.getOrDefault(key, null);
                if (compareAmount == null || amount.compareTo(compareAmount) != 0) {
                    appendLog("客户+收支:" + key + "，履约合计金额为:" + amount + ",收支明细合计金额为:" + Util.null2String(compareAmount) + ",不匹配");
                    getParam().checkResult = "F";
                    break;
                }
            }
        }
    }

    /**
     * 获取明细4履约进度的金额map
     * key为 客户名称+收支项目
     *
     * @return
     */
    private Map<String, BigDecimal> getLvYueDetailAmount() {
        Map<String, BigDecimal> result = new HashMap<>();
        String khmc, glszxm, key;
        BigDecimal amount;
        //获取明细4的汇总数据
        String detailTable = getThreadLocalBaseParam().actionInfo.getFormtableName() + "_dt4";
        String sql = "select a.glszxm,a.khmc,sum(a.hsje) as hsje from " + detailTable + " a " +
                " where 1=1  " +
                " and a.mainid = ? " +
                " and a.srqrlx in (0,3) " + // 收入确认类型=按月每月/验收确认
                " and a.glszxm is not null " +
                " and a.khmc is not null " +
                " group by a.glszxm,a.khmc ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        appendLog("查询履约进度明细数据sql:" + sql + ",参数:" + getThreadLocalBaseParam().actionInfo.getId());
        if (rs.executeQuery(sql, getThreadLocalBaseParam().actionInfo.getId())) {
            while (rs.next()) {
                khmc = Util.null2String(rs.getString("khmc"));
                glszxm = Util.null2String(rs.getString("glszxm"));
                key = khmc + "_" + glszxm;
                amount = SDUtil.getBigDecimalValue(rs.getString("hsje"));
                result.put(key, amount);
            }
        } else {
            appendLog("查询履约进度明细数据sql出错:" + rs.getExceptionMsg());
        }
        return result;
    }

    /**
     * 获取明细3收支项目的的金额map
     * key为 客户名称+收支项目
     *
     * @return
     */
    private Map<String, BigDecimal> getShouzhiDetailAmount() {
        Map<String, BigDecimal> result = new HashMap<>();
        String khmc, glszxm, key;
        BigDecimal amount;
        //获取明细4的汇总数据
        String detailTable = getThreadLocalBaseParam().actionInfo.getFormtableName() + "_dt3";
        String sql = "select a.szxm,a.khmc,sum(a.je) as hsje from " + detailTable + " a " +
                " where 1=1  " +
                " and a.mainid = ? " +
                " and a.srqrlx in (0,3) " + // 收入确认类型=按月每月/验收确认
                " and a.szxm is not null " +
                " and a.khmc is not null " +
                " group by a.szxm,a.khmc ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        appendLog("查询收支项目明细数据sql:" + sql + ",参数:" + getThreadLocalBaseParam().actionInfo.getId());
        if (rs.executeQuery(sql, getThreadLocalBaseParam().actionInfo.getId())) {
            while (rs.next()) {
                khmc = Util.null2String(rs.getString("khmc"));
                glszxm = Util.null2String(rs.getString("szxm"));
                key = khmc + "_" + glszxm;
                amount = SDUtil.getBigDecimalValue(rs.getString("hsje"));
                result.put(key, amount);
            }
        } else {
            appendLog("查询收支项目明细数据sql出错:" + rs.getExceptionMsg());
        }
        return result;
    }

    /**
     * 更新校验结果
     */
    private void updateCheckResult() {
        List<Object> params = new ArrayList<>();
        params.add(getParam().checkResult);
        params.add(getThreadLocalBaseParam().actionInfo.getId());
        String sql = "update " + getThreadLocalBaseParam().actionInfo.getFormtableName() + " set " + field_check_result + "=? where id=? ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        appendLog("更新校验结果sql:" + sql + ";参数:" + params);
        if (rs.executeUpdate(sql, params)) {
            appendLog("更新校验结果sql成功!");
        } else {
            appendLog("更新校验结果sql失败:" + rs.getExceptionMsg());
        }
    }


    /**
     * 校验参数
     *
     * @return
     */
    private void checkParams() {
        if (StringUtils.isBlank(field_htjelx) || StringUtils.isBlank(field_check_result)) {
            getThreadLocalBaseParam().actionError = "缺失action参数";
            appendLog(getActionError());
        }
    }
}
