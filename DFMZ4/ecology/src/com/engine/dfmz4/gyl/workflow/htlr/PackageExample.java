package com.engine.dfmz4.gyl.workflow.htlr;

/**
 * 演示package-private访问权限的例子
 * 
 * 包名：com.engine.dfmz4.gyl.workflow.htlr
 */
public class PackageExample {
    
    // 1. private - 只能在PackageExample类内部访问
    private static class PrivateClass {
        private String data = "private数据";
    }
    
    // 2. package-private - 同包内的类都可以访问（注意：没有修饰符）
    static class PackageClass {
        String data = "package数据";  // 这个字段也是package-private
    }
    
    // 3. public - 任何地方都可以访问
    public static class PublicClass {
        public String data = "public数据";
    }
    
    // package-private方法
    static void packageMethod() {
        System.out.println("这是package-private方法");
    }
    
    // private方法
    private static void privateMethod() {
        System.out.println("这是private方法");
    }
    
    // public方法
    public static void publicMethod() {
        System.out.println("这是public方法");
    }
    
    public static void main(String[] args) {
        System.out.println("=== 在PackageExample类内部访问 ===");
        
        // 在同一个类内部，可以访问所有级别
        PrivateClass privateObj = new PrivateClass();  // ✅ 可以访问
        PackageClass packageObj = new PackageClass();  // ✅ 可以访问
        PublicClass publicObj = new PublicClass();     // ✅ 可以访问
        
        System.out.println("private: " + privateObj.data);
        System.out.println("package: " + packageObj.data);
        System.out.println("public: " + publicObj.data);
        
        privateMethod();  // ✅ 可以调用
        packageMethod();  // ✅ 可以调用
        publicMethod();   // ✅ 可以调用
    }
}
