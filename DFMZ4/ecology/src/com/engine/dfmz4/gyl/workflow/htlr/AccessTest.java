package com.engine.dfmz4.gyl.workflow.htlr;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 演示package-private访问权限
 *
 * 当前包名：com.engine.dfmz4.gyl.workflow.htlr
 *
 * 同包的类：
 * - CheckSZXMAction.java (同包，可以访问package-private)
 * - BuildContractDetailAction.java (同包，可以访问package-private)
 *
 * 不同包的类：
 * - BaseSDAction.java (包名：com.engine.parent.workflow，不同包，无法访问package-private)
 */
public class AccessTest {
    
    // 模拟BaseSDAction的threadLocalMap
    private static final ConcurrentHashMap<String, Object> testMap = new ConcurrentHashMap<>();
    
    // 模拟BaseSDAction的getThreadLocalCustomParam方法
    public static <T> T getThreadLocalCustomParam(Class<?> actionClass, Class<T> paramClass, Supplier<T> supplier) {
        String key = actionClass.getName() + "#" + paramClass.getSimpleName();
        System.out.println("尝试访问: " + key);
        
        try {
            // 这里会尝试访问paramClass
            T instance = supplier.get(); // 这里可能会出问题
            System.out.println("成功创建实例: " + instance.getClass().getName());
            return instance;
        } catch (Exception e) {
            System.out.println("访问失败: " + e.getMessage());
            throw e;
        }
    }
    
    // 模拟CheckSZXMAction
    public static class TestAction {

        // 1. private内部类 - 只能在TestAction内部访问
        private static class PrivateParams {
            private String privateField = "private字段";
            public String value = "private类";

            // private构造函数
            private PrivateParams() {}
        }

        // 2. package-private内部类 - 同包内的类都可以访问（注意：没有任何修饰符）
        static class PackageParams {
            String packageField = "package字段"; // 也是package-private字段
            public String value = "package类";

            // package-private构造函数
            PackageParams() {}
        }

        // 3. public内部类 - 任何地方都可以访问
        public static class PublicParams {
            public String publicField = "public字段";
            public String value = "public类";

            // public构造函数
            public PublicParams() {}
        }
        
        public void testPrivateAccess() {
            System.out.println("=== 测试private内部类访问 ===");
            try {
                // 这在编译时是OK的，但运行时可能有问题
                PrivateParams result = getThreadLocalCustomParam(
                    this.getClass(), 
                    PrivateParams.class, 
                    PrivateParams::new
                );
                System.out.println("private访问成功: " + result.value);
            } catch (Exception e) {
                System.out.println("private访问失败: " + e.getMessage());
            }
        }
        
        public void testPackageAccess() {
            System.out.println("=== 测试package内部类访问 ===");
            try {
                PackageParams result = getThreadLocalCustomParam(
                    this.getClass(), 
                    PackageParams.class, 
                    PackageParams::new
                );
                System.out.println("package访问成功: " + result.value);
            } catch (Exception e) {
                System.out.println("package访问失败: " + e.getMessage());
            }
        }
        
        public void testPublicAccess() {
            System.out.println("=== 测试public内部类访问 ===");
            try {
                PublicParams result = getThreadLocalCustomParam(
                    this.getClass(), 
                    PublicParams.class, 
                    PublicParams::new
                );
                System.out.println("public访问成功: " + result.value);
            } catch (Exception e) {
                System.out.println("public访问失败: " + e.getMessage());
            }
        }
    }
    
    public static void main(String[] args) {
        TestAction action = new TestAction();
        
        action.testPrivateAccess();
        action.testPackageAccess();
        action.testPublicAccess();
    }
}
