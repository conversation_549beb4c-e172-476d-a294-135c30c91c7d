package com.engine.dfmz4.gyl.workflow.htlr;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 测试private内部类的访问权限问题
 */
public class AccessTest {
    
    // 模拟BaseSDAction的threadLocalMap
    private static final ConcurrentHashMap<String, Object> testMap = new ConcurrentHashMap<>();
    
    // 模拟BaseSDAction的getThreadLocalCustomParam方法
    public static <T> T getThreadLocalCustomParam(Class<?> actionClass, Class<T> paramClass, Supplier<T> supplier) {
        String key = actionClass.getName() + "#" + paramClass.getSimpleName();
        System.out.println("尝试访问: " + key);
        
        try {
            // 这里会尝试访问paramClass
            T instance = supplier.get(); // 这里可能会出问题
            System.out.println("成功创建实例: " + instance.getClass().getName());
            return instance;
        } catch (Exception e) {
            System.out.println("访问失败: " + e.getMessage());
            throw e;
        }
    }
    
    // 模拟CheckSZXMAction
    public static class TestAction {
        
        // private内部类
        private static class PrivateParams {
            public String value = "private";
        }
        
        // package-private内部类
        static class PackageParams {
            public String value = "package";
        }
        
        // public内部类
        public static class PublicParams {
            public String value = "public";
        }
        
        public void testPrivateAccess() {
            System.out.println("=== 测试private内部类访问 ===");
            try {
                // 这在编译时是OK的，但运行时可能有问题
                PrivateParams result = getThreadLocalCustomParam(
                    this.getClass(), 
                    PrivateParams.class, 
                    PrivateParams::new
                );
                System.out.println("private访问成功: " + result.value);
            } catch (Exception e) {
                System.out.println("private访问失败: " + e.getMessage());
            }
        }
        
        public void testPackageAccess() {
            System.out.println("=== 测试package内部类访问 ===");
            try {
                PackageParams result = getThreadLocalCustomParam(
                    this.getClass(), 
                    PackageParams.class, 
                    PackageParams::new
                );
                System.out.println("package访问成功: " + result.value);
            } catch (Exception e) {
                System.out.println("package访问失败: " + e.getMessage());
            }
        }
        
        public void testPublicAccess() {
            System.out.println("=== 测试public内部类访问 ===");
            try {
                PublicParams result = getThreadLocalCustomParam(
                    this.getClass(), 
                    PublicParams.class, 
                    PublicParams::new
                );
                System.out.println("public访问成功: " + result.value);
            } catch (Exception e) {
                System.out.println("public访问失败: " + e.getMessage());
            }
        }
    }
    
    public static void main(String[] args) {
        TestAction action = new TestAction();
        
        action.testPrivateAccess();
        action.testPackageAccess();
        action.testPublicAccess();
    }
}
