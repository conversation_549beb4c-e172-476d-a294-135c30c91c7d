package com.engine.wallaby3.gyl.job.dto;

import lombok.Data;

@Data
public class SyncHrmDto {

    //---必填字段---
    /**
     * 编号，唯一标识；确认是新增还是修改
     */
    private String workcode;
    /**
     * 分部；2种方式：1) 分部全路径（副作用：未找到，会新增）；
     * 2) 以 {JSON}{“subcompanycode” : “fw”} 为值的字符串，指定分部编码。(不会新增)，示例：”subcompany”:”{JSON}{\”subcompanycode\”:\”fw\”}” ;
     */
    private String subcompany;
    /**
     * 部门；1) 部门全路径（副作用：未找到，会新增），
     * 2) 以 {JSON}{“departmentcode”:”fw”} 为值的字符串，指定部门编码（不会新增），示例：”department”:”{JSON}{\”departmentcode\”:\”fw\”}”;
     */
    private String department;
    /**
     * 人员名称
     */
    private String lastname;
    /**
     * 岗位名称: 2种方式：1） 名称 （会新建）
     * 2）以 {JSON}{“jobtitlecode”:”fw”} 为值的字符串，指定岗位编码（不会新增）,示例：”jobtitle”:”{JSON}{\”jobtitlecode\”:\”fw\”}”;
     */
    private String jobtitle;
    /**
     * 状态; 枚举：试用,正式,临时,试用延期, 解聘,离职,退休,无效
     */
    private String status;
    /**
     * 办公地点; 关联数据表：HRMLOCATIONS
     */
    private String locationid;
    //---必填字段---

    //---非必填字段---
    /**
     * 生日; 格式：yyyy-MM-dd
     */
    private String birthday;
    /**
     * 性别；枚举值: 女 、 男 ,默认:男
     */
    private String sex;
    /**
     * 邮箱;
     */
    private String email;
    /**
     * 办公室电话;
     */
    private String telephone;
    /**
     * 移动电话;
     */
    private String mobile;
    /**
     * 登录名称
     */
    private String loginid;
    /**
     * 主次账号标志 主账号 、次账号
     */
    private String accounttype;
    /**
     * accounttype为次账号 才有效。 仅支持编号
     */
    private String belongto;
    /**
     * 身份证号码；如果有值，必须唯一
     */
    private String certificatenum;
    /**
     * 入职日期; 格式：yyyy-MM-dd
     */
    private String companystartdate;
    /**
     * 参加工作日期; 格式：yyyy-MM-dd
     */
    private String workstartdate;
    /**
     * 直接上级; 仅支持编号
     */
    private String managerid;
    /**
     * 安全级别
     */
    private String seclevel;

    //辅助字段，不同步
    /**
     * 原始岗位编码
     */
    private String orginJobtitle;
    
    //---新增字段--自定义字段--基础表里
    /**
     * 本地工号
     */
    private String field5;


}
