package com.engine.wallaby3.gyl.job;

import lombok.Getter;
import lombok.Setter;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName SyncSFOrgJob.java
 * @Description 同步SF组织架构（部门、岗位、人员）
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/26
 */
@Getter
@Setter
public class SyncSFOrgJob extends BaseCronJob {
    //---Job参数---
//    /**
//     * 不同步的部门开头配置，多个以英文逗号隔开
//     * 例如配置：DV001,DV002
//     * 如果从接口获取到的部门编码为 DV001123,DV002123 ，则不同步这些部门，他们的下级部门也不同步
//     */
//    private String ignoredDeptPrefix;
//    /**
//     * 人员基本信息-自定义字段名-SF同步时间
//     */
//    private String fieldSyncTime;
//    /**
//     * 人员基本信息-自定义字段名-是否是SF同步
//     */
//    private String fieldFlagSF;
//    /**
//     * job参数-指定的职务id
//     */
//    private String jobactivityid;

    //---Job参数---

    private BaseBean bb;

    /**
     * 执行
     * 按照同步顺序执行（人员相关信息依赖于OA的部门和岗位）
     * <p>
     * 部门-岗位-人员
     */
    @Override
    public void execute() {
        bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");

        // STEP 1 :  同步部门
        bb.writeLog(" STEP 1 :  同步部门--START");
        SyncSFDeptJob syncSFDeptJob = new SyncSFDeptJob();
        syncSFDeptJob.execute();
        bb.writeLog(" STEP 1 :  同步部门--END");

        // STEP 2 :  同步岗位
        bb.writeLog(" STEP 2 :  同步岗位--START");
        SyncSFPostionJob syncSFPostionJob = new SyncSFPostionJob();
        // syncSFPostionJob.setJobactivityid(jobactivityid);
        syncSFPostionJob.execute();
        bb.writeLog(" STEP 2 :  同步岗位--END");

        // STEP 3 :  同步人员
        bb.writeLog(" STEP 3 :  同步人员--START");
        SyncSFHrmJob syncSFHrmJob = new SyncSFHrmJob();
        // syncSFHrmJob.setFieldFlagSF(fieldFlagSF);
        // syncSFHrmJob.setFieldSyncTime(fieldSyncTime);
        syncSFHrmJob.execute();
        bb.writeLog(" STEP 3 :  同步人员--END");


        bb.writeLog(this.getClass().getName() + "---END");
    }

}
