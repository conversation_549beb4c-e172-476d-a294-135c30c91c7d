package com.engine.wallaby3.gyl.job.bean;

import lombok.Data;

/**
 * @FileName SFSyncLog.java
 * @Description 组织架构同步日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/2
 */
@Data
public class SFSyncLog {
    /**
     * 同步来源
     */
    private String tbly;
    /**
     * 同步名称
     */
    private String tbmc;
    /**
     * 同步日期
     */
    private String tbrq;
    /**
     * 同步开始时间
     */
    private String tbkssj;
    /**
     * 同步结束时间
     */
    private String tbjssj;
    /**
     * 同步耗时(秒)
     */
    private String tbhsm;
    /**
     * 同步结果
     */
    private String tbjg;
    /**
     * 同步信息
     */
    private String tbxx;
    /**
     * 备注
     */
    private String bz;


}
