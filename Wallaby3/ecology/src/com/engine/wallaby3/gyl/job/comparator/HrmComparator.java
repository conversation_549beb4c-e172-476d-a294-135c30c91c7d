package com.engine.wallaby3.gyl.job.comparator;

import com.engine.wallaby3.gyl.job.dto.SyncHrmDto;
import org.apache.commons.lang3.StringUtils;
import weaver.general.Util;

import java.util.Comparator;

/**
 * @FileName HrmComparator.java
 * @Description 人员比较器，根据所属上级排序
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/10/7
 */
public class HrmComparator implements Comparator<SyncHrmDto> {
    @Override
    public int compare(SyncHrmDto o1, SyncHrmDto o2) {
        // 处理上级工号为空的情况
        if (StringUtils.isBlank(o1.getManagerid()) && StringUtils.isBlank(o2.getManagerid())) {
            return 0; // 自反性，两者相等
        } else if (StringUtils.isBlank(o1.getManagerid())) {
            return -1; // o1为空，排在前面
        } else if (StringUtils.isBlank(o2.getManagerid())) {
            return 1; // o2为空，排在前面
        }
        // 比较上级工号，如果相同，说明同级，按其他条件排序
        int result = Util.null2String(o1.getManagerid()).compareTo(Util.null2String(o2.getManagerid()));
        if (result == 0) {
            // 在这里可以添加其他条件来排序
            // 姓名
            result = Util.null2String(o1.getLastname()).compareTo(Util.null2String(o2.getLastname()));
        }
        return result;
    }
}
