package com.engine.moduleorder.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.utils.InsertModuleUtil4samtable;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

/**
 * 服务单和客户单
 * {type : '' ,jsondata:{maindata:[{col:'',value'',type,''}]}}
 */
public class ServiceOrderAndClientCmd extends AbstractCommonCommand<Map<String, Object>> {

    private BaseBean bb;

    public ServiceOrderAndClientCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String jsondata = Util.null2String(params.get("jsondata"));
        String type = Util.null2String(params.get("type"));
        JSONObject jsonObject;
        JSONArray mainArray;
        JSONObject mainObject;
        bb.writeLog("start-----");
        try {
            jsonObject = JSONObject.parseObject(jsondata);
            mainArray = jsonObject.getJSONArray("maindata");
            mainObject = changeCol(mainArray);
        }catch (Exception e){
            result.put("status",false);
            result.put("msg","jsondata字段转换出错");
            bb.writeLog("jsondata字段转换出错");
            return result;
        }
        try{
            if("".equals(type)){
                result.put("status",false);
                result.put("msg","类型为空查询失败");
            }else if("0".equals(type)){
                String keycol = Util.null2String(bb.getPropValue("interface4module","KeyCol0"));
                String tablename = Util.null2String(bb.getPropValue("interface4module","TableName0"));
                String moduleid = Util.null2String(bb.getPropValue("interface4module","ModuleId0"));
                if("".equals(keycol)||"".equals(tablename)||"".equals(moduleid)){
                    result.put("status",false);
                    result.put("msg","配置文件获取失败");
                    return result;
                }
                bb.writeLog("mainObject"+mainObject);
                boolean flag=InsertUpdata(mainObject,keycol,tablename,moduleid);
                if(!flag){
                    result.put("status",false);
                    result.put("msg","插入数据时出错");
                }else {
                    result.put("status",true);
                    result.put("msg","插入数据成功");
                }
            }else if("1".equals(type)){
                String keycol =  Util.null2String(bb.getPropValue("interface4module","KeyCol1"));
                String tablename = Util.null2String(bb.getPropValue("interface4module","TableName1"));
                String moduleid = Util.null2String(bb.getPropValue("interface4module","ModuleId1"));
                if("".equals(keycol)||"".equals(tablename)||"".equals(moduleid)){
                    result.put("status",false);
                    result.put("msg","配置文件获取失败");
                    return result;
                }
                bb.writeLog("mainObject"+mainObject);
                boolean flag=InsertUpdata(mainObject,keycol,tablename,moduleid);
                if(!flag){
                    result.put("status",false);
                    result.put("msg","插入数据时出错或插入了空无效数据");
                }else {
                    result.put("status",true);
                    result.put("msg","插入数据成功");
                }
            }else {
                result.put("status",false);
                result.put("msg","未知其他类型尚未开发");
            }
        }catch (Exception e){
            result.put("status",false);
            result.put("msg","接口运行出错");
        }
        bb.writeLog("end-----");
        return result;
    }

    public JSONObject changeCol(JSONArray jsonArray){
        JSONObject result  = new JSONObject();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject =jsonArray.getJSONObject(i);
            String type = Util.null2String(jsonObject.getString("type"));
            String col = Util.null2String(jsonObject.getString("col"));
            String value = Util.null2String(jsonObject.getString("value"));
            String retvalue = getcolValue(type,value);
            if(!"".equals(col)){
                result.put(col,retvalue);
            }
        }
        return result;
    }
    //转换规则扩展
    public String getcolValue(String type, String value){
        if("".equals(type)||"0".equals(type)){
            return value;
        }
        else if ("workcode2id".equals(type)){
            return workcode2id(value);
        }
        else{
            return value;
        }
    }
    public String workcode2id(String value){
        if("".equals(value)){
            return "";
        }
        RecordSet recordSet = new RecordSet();
        recordSet.execute("SELECT id from (SELECT id, REPLACE(REPLACE(REPLACE(workcode,CHAR(9),''),CHAR(10),''),CHAR(13),'') workcode from hrmresource) taba WHERE taba.workcode = '"+value+"'");
        if(recordSet.next()){
            return Util.null2String(recordSet.getString("id"));
        }
        return "";
    }
    //sql server 数据库
    public boolean InsertUpdata(JSONObject jsonObject,String keycol,String tablename,String moduleid){
        RecordSet search = new RecordSet();
        search.execute("SELECT * from "+ tablename + " where "+ keycol + " = '"+jsonObject.getString(keycol)+"'");
        if("".equals(jsonObject.getString(keycol))){
            return false;
        }
        if(!search.next()){
            Map<String,String> map = new HashMap<>();
            if("".equals(tablename)){
                return false;
            }
            ArrayList<String> list = getmodulecolnostand(tablename);
            for (String col : list) {
                if (jsonObject.getString(col) != null && !"".equals(jsonObject.getString(col))) {
                    map.put(col, jsonObject.getString(col));
                }
            }
            int mainid = InsertModuleUtil4samtable.insert(tablename,map,1,Integer.parseInt(moduleid));
            if(mainid<0){
                new RecordSet().execute("delete from "+tablename+" where id = '"+(-1)*mainid+"'");
                return false;
            }
            return true;
        }else{
            RecordSet recordSet = new RecordSet();
            ArrayList<String> list = getmodulecolnostand(tablename);
            StringBuilder stringBuilder = new StringBuilder();
            int flag = 0;
            ArrayList<String> value = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                String col = list.get(i);
                if(!keycol.equals(col)&&jsonObject.getString(col)!=null&&!"".equals(jsonObject.getString(col))){
                    stringBuilder.append(col).append("=").append("?");
                    value.add(jsonObject.getString(col));
                    if(i!=list.size()-1){
                        stringBuilder.append(",");
                    }
                    flag++;
                }
            }
            if(stringBuilder.charAt(stringBuilder.length()-1)==','){
                stringBuilder.deleteCharAt(stringBuilder.length()-1);
            }
            if(flag>0){
                recordSet.executeUpdate("update "+ tablename +" set "+ stringBuilder.toString() +" where "+keycol+" = '"+jsonObject.getString(keycol)+"'",value.toArray());
            }
            return true;
        }
    }
    // sql server 适用
    public ArrayList<String> getmodulecolnostand(String table){
        RecordSet recordSet = new RecordSet();
        recordSet.execute("select name from sys.columns where object_id=object_id('"+table+"')");
        ArrayList<String> list = new ArrayList<>();
        HashSet<String> set = new HashSet<>();
        set.add("id");
        set.add("MODEUUID");
        set.add("requestId");
        set.add("modedatamodifydatetime");
        set.add("modedatamodifier");
        set.add("modedatacreatetime");
        set.add("modedatacreatertype");
        set.add("modedatacreater");
        set.add("modedatacreatedate");
        set.add("formmodeid");
        while (recordSet.next()){
           String index =  recordSet.getString(1);
            if(!"".equals(Util.null2String(index))
                    &&!set.contains(index)){
                list.add(index);
            }
        }
        return list;
    }

}
