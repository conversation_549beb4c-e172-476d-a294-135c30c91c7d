package com.engine.moduleorder.service.impl;

import com.engine.moduleorder.cmd.ServiceOrderAndClientCmd;
import com.engine.moduleorder.service.ServiceOrderAndClientService;
import weaver.hrm.User;
import com.engine.core.impl.Service;
import java.util.Map;


public class ServiceOrderAndClientServiceImpl extends Service implements ServiceOrderAndClientService {

    @Override
    public Map<String, Object> syncData2module(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ServiceOrderAndClientCmd(params, user));
    }
}
