package com.engine.common.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaverboot.frame.ioc.anno.classAnno.WeaIocReplaceComponent;
import com.weaverboot.frame.ioc.anno.methodAnno.WeaReplaceAfter;
import com.weaverboot.frame.ioc.handler.replace.weaReplaceParam.impl.WeaAfterReplaceParam;

import java.util.Iterator;
import java.util.Map;

/**
 * 无侵入式接口拦截处理
 */
@WeaIocReplaceComponent
public class AddButtionServiceImpl {

    @WeaReplaceAfter(value = "/api/proj/pctask/gettaskform", order = 1, description = "测试拦截后置")
    public String deletebuttonafteraop(WeaAfterReplaceParam weaAfterReplaceParam) {
        String data = weaAfterReplaceParam.getData();
        JSONObject result = JSONObject.parseObject(data);
        JSONArray jsonArray = result.getJSONArray("fieldinfo");
        boolean flag = false;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if ("基本信息".equals(jsonObject.getString("title"))) {
                JSONArray array = jsonObject.getJSONArray("items");
                for (int j = 0; j < array.size(); j++) {
                    JSONObject item = array.getJSONObject(j);
                    if (item.getJSONArray("domkey").size() > 0 && "rwzt".equals(item.getJSONArray("domkey").get(0)) && "1".equals(item.getString("value"))) {
                        flag = true;
                    }
                }
            }
        }
        if (flag) {
            JSONArray menu = result.getJSONArray("rightMenus");
            Iterator<Object> o = menu.iterator();
            while (o.hasNext()) {
                JSONObject jo = (JSONObject) o.next();
                if ("编辑".equals(jo.getString("menuName"))) {
                    o.remove(); //这种方式OK的
                }
            }
        }
        return result.toJSONString();
    }


    @WeaReplaceAfter(value = "/api/proj/portal/getRightMenu", order = 1, description = "测试拦截后置")
    public String deletetaskadd1(WeaAfterReplaceParam weaAfterReplaceParam) {
        String data = weaAfterReplaceParam.getData();
        JSONObject result = JSONObject.parseObject(data);
        if("true".equals(result.getString("isright"))){
            JSONArray jsonArray = result.getJSONArray("rightMenus");
            Iterator<Object> o = jsonArray.iterator();
            while (o.hasNext()) {
                JSONObject jo = (JSONObject) o.next();
                if("添加任务".equals(jo.getString("menuName"))) {
                    //ja.remove(jo); //不要用这种方式删除，会报出ConcurrentModificationException
                    o.remove(); //这种方式OK的
                }
            }
        }
        return result.toJSONString();
    }


}
