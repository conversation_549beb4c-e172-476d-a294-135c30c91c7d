package com.engine.hrm.job;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * 同步在职人员JOB
 */
public class SyncHrmNumJob extends BaseCronJob {
    /**
     * 基类
     */
    private BaseBean bb;

    private void initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        initBaseBean();
        bb.writeLog(this.getClass().getName() + " --- START");
        try {
            RecordSet recordSet = new RecordSet();
            recordSet.execute("UPDATE taba \n" +
                    "SET taba.zbrs = tabb.num,\n" +
                    "taba.qbrs = taba.bzs - tabb.num \n" +
                    "FROM\n" +
                    "\tuf_bzgltz taba\n" +
                    "\tLEFT JOIN (\n" +
                    "\tSELECT\n" +
                    "\t\ttabb.id,\n" +
                    "\t\tCOUNT(*) num \n" +
                    "\tFROM\n" +
                    "\t\thrmresource taba\n" +
                    "\t\tLEFT JOIN hrmdepartment tabb ON taba.DEPARTMENTID = tabb.id \n" +
                    "\tWHERE\n" +
                    "\t\ttabb.id IN ( SELECT bm FROM uf_bzgltz ) \n" +
                    "\t\tAND taba.STATUS < 5 \n" +
                    "\tGROUP BY\n" +
                    "\t\ttabb.id \n" +
                    "\t) tabb ON taba.bm = tabb.id");
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + " --- END");
    }

}
