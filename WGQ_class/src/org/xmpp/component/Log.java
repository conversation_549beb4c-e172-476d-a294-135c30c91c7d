package org.xmpp.component;

@Deprecated
public interface Log {
  void error(String paramString);
  
  void error(String paramString, Throwable paramThrowable);
  
  void error(Throwable paramThrowable);
  
  void warn(String paramString);
  
  void warn(String paramString, Throwable paramThrowable);
  
  void warn(Throwable paramThrowable);
  
  void info(String paramString);
  
  void info(String paramString, Throwable paramThrowable);
  
  void info(Throwable paramThrowable);
  
  void debug(String paramString);
  
  void debug(String paramString, Throwable paramThrowable);
  
  void debug(Throwable paramThrowable);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/xmpp/component/Log.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */