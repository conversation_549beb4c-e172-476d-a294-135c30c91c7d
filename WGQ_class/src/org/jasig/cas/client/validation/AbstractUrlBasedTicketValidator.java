/*     */ package org.jasig.cas.client.validation;
/*     */ 
/*     */ import com.cloudstore.api.util.Util_Redis;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.net.MalformedURLException;
/*     */ import java.net.URL;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import org.jasig.cas.client.util.CommonUtils;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractUrlBasedTicketValidator
/*     */   implements TicketValidator
/*     */ {
/*  56 */   protected final Log log = LogFactory.getLog(getClass());
/*     */   
/*     */   protected HostnameVerifier hostnameVerifier;
/*     */   
/*     */   private String casServerUrlPrefix;
/*     */   
/*     */   private boolean renew;
/*     */   
/*     */   private Map<String, String> customParameters;
/*     */   
/*     */   private String encoding;
/*     */   
/*     */   private Logger logger;
/*     */   
/*     */   public String getCasServerUrlPrefix() {
/*  71 */     return this.casServerUrlPrefix;
/*     */   }
/*     */   
/*     */   public void setCasServerUrlPrefix(String paramString) {
/*  75 */     this.casServerUrlPrefix = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void populateUrlAttributeMap(Map<String, String> paramMap) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected final String constructValidationUrl(String paramString1, String paramString2) {
/* 131 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 133 */     this.log.debug("Placing URL parameters in map.");
/* 134 */     hashMap.put("ticket", paramString1);
/* 135 */     hashMap.put("service", encodeUrl(paramString2));
/*     */     
/* 137 */     if (this.renew) {
/* 138 */       hashMap.put("renew", "true");
/*     */     }
/*     */     
/* 141 */     this.log.debug("Calling template URL attribute map.");
/* 142 */     populateUrlAttributeMap((Map)hashMap);
/*     */     
/* 144 */     this.log.debug("Loading custom parameters from configuration.");
/* 145 */     if (this.customParameters != null) {
/* 146 */       hashMap.putAll(this.customParameters);
/*     */     }
/*     */ 
/*     */     
/* 150 */     BaseBean baseBean = new BaseBean();
/* 151 */     Util_Redis util_Redis = Util_Redis.getIstance();
/* 152 */     String str1 = "";
/* 153 */     String str2 = "";
/* 154 */     if (util_Redis == null) {
/* 155 */       StaticObj staticObj = StaticObj.getInstance();
/* 156 */       str1 = Util.null2String(staticObj.getObject("casServerUrlPrefix"));
/* 157 */       str2 = Util.null2String(staticObj.getObject("serverNameConfig"));
/*     */     } else {
/* 159 */       str1 = Util.null2String(util_Redis.get("casServerUrlPrefix"));
/* 160 */       str2 = Util.null2String(util_Redis.get("serverNameConfig"));
/*     */     } 
/*     */     
/* 163 */     this.logger.info("multi_cas_server_url :" + str1);
/* 164 */     this.logger.info("multi_server_name :" + str2);
/* 165 */     if (str2.contains(",") && 
/* 166 */       !"".equals(str1)) {
/* 167 */       String[] arrayOfString1 = str1.split(",");
/* 168 */       String[] arrayOfString2 = str2.split(",");
/* 169 */       String str = "";
/* 170 */       int i = 0;
/*     */       try {
/* 172 */         URL uRL = new URL(paramString2);
/* 173 */         str = uRL.getHost();
/* 174 */         i = uRL.getPort();
/* 175 */         this.logger.info("serverName :" + str);
/* 176 */       } catch (MalformedURLException malformedURLException) {
/* 177 */         malformedURLException.printStackTrace();
/*     */       } 
/* 179 */       boolean bool = false;
/* 180 */       for (byte b1 = 0; b1 < arrayOfString2.length; b1++) {
/* 181 */         if (i == 80) {
/* 182 */           if (arrayOfString2[b1].indexOf(str) >= 0) {
/* 183 */             this.casServerUrlPrefix = arrayOfString1[b1];
/* 184 */             bool = true;
/*     */             
/*     */             break;
/*     */           } 
/* 188 */         } else if (arrayOfString2[b1].indexOf(str + ":" + i) >= 0) {
/* 189 */           this.casServerUrlPrefix = arrayOfString1[b1];
/* 190 */           bool = true;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */       
/* 196 */       if (!bool) {
/* 197 */         this.casServerUrlPrefix = arrayOfString1[0];
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 206 */     String str3 = getUrlSuffix();
/* 207 */     StringBuilder stringBuilder = new StringBuilder(hashMap.size() * 10 + this.casServerUrlPrefix.length() + str3.length() + 1);
/*     */     
/* 209 */     byte b = 0;
/*     */     
/* 211 */     stringBuilder.append(this.casServerUrlPrefix);
/* 212 */     if (!this.casServerUrlPrefix.endsWith("/")) {
/* 213 */       stringBuilder.append("/");
/*     */     }
/* 215 */     stringBuilder.append(str3);
/*     */     
/* 217 */     for (Map.Entry<Object, Object> entry : hashMap.entrySet()) {
/* 218 */       String str4 = (String)entry.getKey();
/* 219 */       String str5 = (String)entry.getValue();
/*     */       
/* 221 */       if (str5 != null) {
/* 222 */         stringBuilder.append((b++ == 0) ? "?" : "&");
/* 223 */         stringBuilder.append(str4);
/* 224 */         stringBuilder.append("=");
/* 225 */         stringBuilder.append(str5);
/*     */       } 
/*     */     } 
/* 228 */     this.logger.info("================constructValidationUrl:" + stringBuilder.toString());
/* 229 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected final String encodeUrl(String paramString) {
/* 240 */     if (paramString == null) {
/* 241 */       return null;
/*     */     }
/*     */     
/*     */     try {
/* 245 */       return URLEncoder.encode(paramString, "UTF-8");
/* 246 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 247 */       return paramString;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Assertion validate(String paramString1, String paramString2) throws TicketValidationException {
/* 274 */     String str = constructValidationUrl(paramString1, paramString2);
/* 275 */     if (this.log.isDebugEnabled()) {
/* 276 */       this.log.debug("Constructing validation url: " + str);
/*     */     }
/*     */     
/*     */     try {
/* 280 */       this.log.debug("Retrieving response from server.");
/* 281 */       String str1 = retrieveResponseFromServer(new URL(str), paramString1);
/*     */       
/* 283 */       if (str1 == null) {
/* 284 */         throw new TicketValidationException("The CAS server returned no response.");
/*     */       }
/*     */       
/* 287 */       if (this.log.isDebugEnabled()) {
/* 288 */         this.log.debug("Server response: " + str1);
/*     */       }
/*     */       
/* 291 */       return parseResponseFromServer(str1);
/* 292 */     } catch (MalformedURLException malformedURLException) {
/* 293 */       throw new TicketValidationException(malformedURLException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public final void setRenew(boolean paramBoolean) {
/* 298 */     this.renew = paramBoolean;
/*     */   }
/*     */   
/*     */   public final void setCustomParameters(Map<String, String> paramMap) {
/* 302 */     this.customParameters = paramMap;
/*     */   }
/*     */   
/*     */   public final void setHostnameVerifier(HostnameVerifier paramHostnameVerifier) {
/* 306 */     this.hostnameVerifier = paramHostnameVerifier;
/*     */   }
/*     */   
/*     */   public final void setEncoding(String paramString) {
/* 310 */     this.encoding = paramString;
/*     */   }
/*     */   
/*     */   protected final String getEncoding() {
/* 314 */     return this.encoding;
/*     */   }
/*     */   protected AbstractUrlBasedTicketValidator(String paramString) {
/* 317 */     this.logger = LoggerFactory.getLogger(getClass());
/*     */     this.casServerUrlPrefix = paramString;
/*     */     CommonUtils.assertNotNull(this.casServerUrlPrefix, "casServerUrlPrefix cannot be null.");
/*     */   }
/*     */   
/*     */   protected abstract String getUrlSuffix();
/*     */   
/*     */   protected abstract void setDisableXmlSchemaValidation(boolean paramBoolean);
/*     */   
/*     */   protected abstract Assertion parseResponseFromServer(String paramString) throws TicketValidationException;
/*     */   
/*     */   protected abstract String retrieveResponseFromServer(URL paramURL, String paramString);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jasig/cas/client/validation/AbstractUrlBasedTicketValidator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */