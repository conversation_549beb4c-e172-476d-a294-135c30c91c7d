/*     */ package org.jasig.cas.client.validation;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.servlet.FilterChain;
/*     */ import javax.servlet.FilterConfig;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.jasig.cas.client.util.AbstractCasFilter;
/*     */ import org.jasig.cas.client.util.CommonUtils;
/*     */ import org.jasig.cas.client.util.ReflectUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractTicketValidationFilter
/*     */   extends AbstractCasFilter
/*     */ {
/*     */   private TicketValidator ticketValidator;
/*     */   private boolean redirectAfterValidation = false;
/*     */   private boolean exceptionOnValidationFailure = true;
/*     */   private boolean useSession = true;
/*     */   
/*     */   protected TicketValidator getTicketValidator(FilterConfig paramFilterConfig) {
/*  28 */     return this.ticketValidator;
/*     */   }
/*     */   
/*     */   protected HostnameVerifier getHostnameVerifier(FilterConfig paramFilterConfig) {
/*  32 */     String str1 = getPropertyFromInitParams(paramFilterConfig, "hostnameVerifier", (String)null);
/*  33 */     this.log.trace("Using hostnameVerifier parameter: " + str1);
/*  34 */     String str2 = getPropertyFromInitParams(paramFilterConfig, "hostnameVerifierConfig", (String)null);
/*  35 */     this.log.trace("Using hostnameVerifierConfig parameter: " + str2);
/*  36 */     if (str1 != null) {
/*  37 */       return (str2 != null) ? (HostnameVerifier)ReflectUtils.newInstance(str1, new Object[] { str2 }) : (HostnameVerifier)ReflectUtils.newInstance(str1, new Object[0]);
/*     */     }
/*  39 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void initInternal(FilterConfig paramFilterConfig) throws ServletException {
/*  44 */     setExceptionOnValidationFailure(parseBoolean(getPropertyFromInitParams(paramFilterConfig, "exceptionOnValidationFailure", "true")));
/*  45 */     this.log.trace("Setting exceptionOnValidationFailure parameter: " + this.exceptionOnValidationFailure);
/*  46 */     setRedirectAfterValidation(parseBoolean(getPropertyFromInitParams(paramFilterConfig, "redirectAfterValidation", "true")));
/*  47 */     this.log.trace("Setting redirectAfterValidation parameter: " + this.redirectAfterValidation);
/*  48 */     setUseSession(parseBoolean(getPropertyFromInitParams(paramFilterConfig, "useSession", "true")));
/*  49 */     this.log.trace("Setting useSession parameter: " + this.useSession);
/*  50 */     setTicketValidator(getTicketValidator(paramFilterConfig));
/*  51 */     super.initInternal(paramFilterConfig);
/*     */   }
/*     */   
/*     */   public void init() {
/*  55 */     super.init();
/*  56 */     CommonUtils.assertNotNull(this.ticketValidator, "ticketValidator cannot be null.");
/*     */   }
/*     */   
/*     */   protected boolean preFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/*  60 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void onSuccessfulValidation(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Assertion paramAssertion) {}
/*     */ 
/*     */   
/*     */   protected void onFailedValidation(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {}
/*     */   
/*     */   public final void doFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/*  70 */     if (preFilter(paramServletRequest, paramServletResponse, paramFilterChain)) {
/*  71 */       HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/*  72 */       HttpServletResponse httpServletResponse = (HttpServletResponse)paramServletResponse;
/*  73 */       String str = CommonUtils.safeGetParameter(httpServletRequest, getArtifactParameterName());
/*  74 */       if (CommonUtils.isNotBlank(str)) {
/*  75 */         if (this.log.isDebugEnabled()) {
/*  76 */           this.log.debug("Attempting to validate ticket: " + str);
/*     */         }
/*     */         
/*     */         try {
/*  80 */           Assertion assertion = this.ticketValidator.validate(str, constructServiceUrl(httpServletRequest, httpServletResponse));
/*  81 */           if (this.log.isDebugEnabled()) {
/*  82 */             this.log.debug("Successfully authenticated user: " + assertion.getPrincipal().getName());
/*     */           }
/*     */           
/*  85 */           httpServletRequest.setAttribute("_const_cas_assertion_", assertion);
/*     */           
/*  87 */           httpServletRequest.getSession().setAttribute("_const_cas_ticket_", str);
/*  88 */           if (this.useSession) {
/*  89 */             httpServletRequest.getSession().setAttribute("_const_cas_assertion_", assertion);
/*     */           }
/*     */           
/*  92 */           onSuccessfulValidation(httpServletRequest, httpServletResponse, assertion);
/*  93 */           if (this.redirectAfterValidation) {
/*  94 */             this.log.debug("Redirecting after successful ticket validation.");
/*  95 */             httpServletResponse.sendRedirect(constructServiceUrl(httpServletRequest, httpServletResponse));
/*     */             return;
/*     */           } 
/*  98 */         } catch (TicketValidationException ticketValidationException) {
/*  99 */           httpServletResponse.setStatus(403);
/* 100 */           this.log.warn(ticketValidationException, (Throwable)ticketValidationException);
/* 101 */           onFailedValidation(httpServletRequest, httpServletResponse);
/* 102 */           if (this.exceptionOnValidationFailure) {
/* 103 */             throw new ServletException(ticketValidationException);
/*     */           }
/*     */           
/*     */           return;
/*     */         } 
/*     */       } 
/*     */       
/* 110 */       paramFilterChain.doFilter((ServletRequest)httpServletRequest, (ServletResponse)httpServletResponse);
/*     */     } 
/*     */   }
/*     */   
/*     */   public final void setTicketValidator(TicketValidator paramTicketValidator) {
/* 115 */     this.ticketValidator = paramTicketValidator;
/*     */   }
/*     */   
/*     */   public final void setRedirectAfterValidation(boolean paramBoolean) {
/* 119 */     this.redirectAfterValidation = paramBoolean;
/*     */   }
/*     */   
/*     */   public final void setExceptionOnValidationFailure(boolean paramBoolean) {
/* 123 */     this.exceptionOnValidationFailure = paramBoolean;
/*     */   }
/*     */   
/*     */   public final void setUseSession(boolean paramBoolean) {
/* 127 */     this.useSession = paramBoolean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jasig/cas/client/validation/AbstractTicketValidationFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */