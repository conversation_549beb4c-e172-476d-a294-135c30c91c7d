/*     */ package org.jasig.cas.client.session;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import org.apache.http.client.methods.HttpPost;
/*     */ import org.apache.http.impl.client.DefaultHttpClient;
/*     */ import org.apache.http.message.BasicNameValuePair;
/*     */ import org.jasig.cas.client.util.CommonUtils;
/*     */ import org.jasig.cas.client.util.XmlUtils;
/*     */ import weaver.session.util.RedisSessionUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class SingleSignOutHandler
/*     */ {
/*  24 */   private final Log log = LogFactory.getLog(getClass());
/*     */   
/*  26 */   private SessionMappingStorage sessionMappingStorage = new HashMapBackedSessionMappingStorage();
/*     */ 
/*     */   
/*  29 */   private String artifactParameterName = "ticket";
/*     */   
/*  31 */   private String logoutParameterName = "logoutRequest";
/*     */   
/*  33 */   private String logoutParameterClusterName = "logoutRequestCluster";
/*     */   
/*     */   private String clusterNodeUrls;
/*     */   
/*     */   public void setSessionMappingStorage(SessionMappingStorage paramSessionMappingStorage) {
/*  38 */     this.sessionMappingStorage = paramSessionMappingStorage;
/*     */   }
/*     */   
/*     */   public SessionMappingStorage getSessionMappingStorage() {
/*  42 */     return this.sessionMappingStorage;
/*     */   }
/*     */   
/*     */   public void setArtifactParameterName(String paramString) {
/*  46 */     this.artifactParameterName = paramString;
/*     */   }
/*     */   
/*     */   public void setLogoutParameterName(String paramString) {
/*  50 */     this.logoutParameterName = paramString;
/*     */   }
/*     */   
/*     */   public void setClusterNodeUrls(String paramString) {
/*  54 */     this.clusterNodeUrls = paramString;
/*     */   }
/*     */   
/*     */   public void init() {
/*  58 */     CommonUtils.assertNotNull(this.artifactParameterName, "artifactParameterName cannot be null.");
/*  59 */     CommonUtils.assertNotNull(this.logoutParameterName, "logoutParameterName cannot be null.");
/*  60 */     CommonUtils.assertNotNull(this.sessionMappingStorage, "sessionMappingStorage cannote be null.");
/*     */   }
/*     */   
/*     */   public boolean isTokenRequest(HttpServletRequest paramHttpServletRequest) {
/*  64 */     return CommonUtils.isNotBlank(CommonUtils.safeGetParameter(paramHttpServletRequest, this.artifactParameterName));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isLogoutRequest(HttpServletRequest paramHttpServletRequest) {
/*  74 */     return ("POST".equals(paramHttpServletRequest.getMethod()) && !isMultipartRequest(paramHttpServletRequest) && 
/*  75 */       CommonUtils.isNotBlank(CommonUtils.safeGetParameter(paramHttpServletRequest, this.logoutParameterName)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isLogoutRequestFromClusterNode(HttpServletRequest paramHttpServletRequest) {
/*  88 */     return (!isMultipartRequest(paramHttpServletRequest) && "true".equals(paramHttpServletRequest.getParameter(this.logoutParameterClusterName)));
/*     */   }
/*     */   
/*     */   public void recordSession(HttpServletRequest paramHttpServletRequest) {
/*  92 */     HttpSession httpSession = paramHttpServletRequest.getSession(true);
/*     */     
/*  94 */     String str = CommonUtils.safeGetParameter(paramHttpServletRequest, this.artifactParameterName);
/*     */     
/*  96 */     if (this.log.isDebugEnabled()) {
/*  97 */       this.log.debug("Recording session for token " + str);
/*     */     }
/*     */     try {
/* 100 */       this.sessionMappingStorage.removeBySessionById(httpSession.getId());
/* 101 */     } catch (Exception exception) {}
/*     */ 
/*     */     
/* 104 */     this.sessionMappingStorage.addSessionById(str, httpSession);
/*     */   }
/*     */ 
/*     */   
/*     */   public void destroySession(HttpServletRequest paramHttpServletRequest) {
/* 109 */     String str1 = CommonUtils.safeGetParameter(paramHttpServletRequest, this.logoutParameterName);
/* 110 */     if (this.log.isTraceEnabled()) {
/* 111 */       this.log.trace("Logout request:\n" + str1);
/*     */     }
/* 113 */     String str2 = XmlUtils.getTextForElement(str1, "SessionIndex");
/* 114 */     if (CommonUtils.isNotBlank(str2)) {
/* 115 */       HttpSession httpSession = this.sessionMappingStorage.removeSessionByMappingId(str2);
/*     */       
/* 117 */       if (httpSession != null) {
/* 118 */         this.log.info("destroySession session在当前节点------");
/* 119 */         String str = httpSession.getId();
/*     */         
/* 121 */         if (this.log.isDebugEnabled()) {
/* 122 */           this.log.info("Invalidating session [" + str + "] for token [" + str2 + "]");
/*     */         }
/*     */         try {
/* 125 */           httpSession.invalidate();
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         }
/* 131 */         catch (IllegalStateException illegalStateException) {
/* 132 */           this.log.error("Error invalidating session.", illegalStateException);
/*     */         } 
/*     */       } else {
/* 135 */         this.log.info("destroySession session不在当前节点------");
/*     */         
/* 137 */         destroySessionOfClusterNodes(str2);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void destroySessionOfClusterNodes(String paramString) {
/* 150 */     this.log.info("destroySessionOfClusterNodes--begin-----:" + paramString);
/* 151 */     if (this.clusterNodeUrls != null && this.clusterNodeUrls.length() > 0) {
/* 152 */       this.log.info(this.clusterNodeUrls);
/* 153 */       String[] arrayOfString = this.clusterNodeUrls.split(",");
/* 154 */       for (String str : arrayOfString) {
/* 155 */         DefaultHttpClient defaultHttpClient = new DefaultHttpClient();
/*     */         
/* 157 */         HttpPost httpPost = new HttpPost(str);
/* 158 */         ArrayList<BasicNameValuePair> arrayList = new ArrayList();
/* 159 */         arrayList.add(new BasicNameValuePair(this.logoutParameterClusterName, "true"));
/* 160 */         arrayList.add(new BasicNameValuePair(this.artifactParameterName, paramString));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 171 */     this.log.info("destroySessionOfClusterNodes--end-----:" + paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void destroySessionFromClusterNode(HttpServletRequest paramHttpServletRequest) {
/* 180 */     String str = paramHttpServletRequest.getParameter(this.artifactParameterName);
/* 181 */     this.log.info("destroySessionFromClusterNode----begin---:" + str);
/* 182 */     if (CommonUtils.isNotBlank(str)) {
/* 183 */       HttpSession httpSession = this.sessionMappingStorage.removeSessionByMappingId(str);
/*     */       
/* 185 */       if (httpSession != null) {
/* 186 */         String str1 = httpSession.getId();
/*     */         
/* 188 */         if (this.log.isDebugEnabled()) {
/* 189 */           this.log.debug("Invalidating session[" + str1 + "] for token [" + str + "]");
/*     */         }
/*     */         try {
/* 192 */           httpSession.invalidate();
/* 193 */           this.log.info("注销用户--------------");
/*     */ 
/*     */           
/* 196 */           RedisSessionUtil redisSessionUtil = new RedisSessionUtil();
/* 197 */           ArrayList<String> arrayList = new ArrayList();
/* 198 */           arrayList.add(str1);
/* 199 */           redisSessionUtil.removeSessoinAll(arrayList);
/*     */           
/* 201 */           this.log.info("删除redis-完成");
/* 202 */         } catch (IllegalStateException illegalStateException) {
/* 203 */           this.log.error("Error invalidating session", illegalStateException);
/*     */         } 
/*     */       } 
/* 206 */       this.log.info("注销用户 11--------------");
/*     */     } 
/* 208 */     this.log.info("destroySessionFromClusterNode----end---:" + str);
/*     */   }
/*     */   
/*     */   private boolean isMultipartRequest(HttpServletRequest paramHttpServletRequest) {
/* 212 */     return (paramHttpServletRequest.getContentType() != null && paramHttpServletRequest.getContentType().toLowerCase().startsWith("multipart"));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jasig/cas/client/session/SingleSignOutHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */