/*    */ package org.jasig.cas.client.session;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpSession;
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class HashMapBackedSessionMappingStorage
/*    */   implements SessionMappingStorage
/*    */ {
/* 16 */   public static final Map<String, HttpSession> MANAGED_SESSIONS = new HashMap<>();
/* 17 */   private final Map<String, String> ID_TO_SESSION_KEY_MAPPING = new HashMap<>();
/* 18 */   private final Log log = LogFactory.getLog(getClass());
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public synchronized void addSessionById(String paramString, HttpSession paramHttpSession) {
/* 24 */     this.ID_TO_SESSION_KEY_MAPPING.put(paramHttpSession.getId(), paramString);
/* 25 */     this; MANAGED_SESSIONS.put(paramString, paramHttpSession);
/*    */   }
/*    */   
/*    */   public synchronized void removeBySessionById(String paramString) {
/* 29 */     if (this.log.isDebugEnabled()) {
/* 30 */       this.log.debug("Attempting to remove Session=[" + paramString + "]");
/*    */     }
/*    */     
/* 33 */     String str = this.ID_TO_SESSION_KEY_MAPPING.get(paramString);
/* 34 */     if (this.log.isDebugEnabled()) {
/* 35 */       if (str != null) {
/* 36 */         this.log.debug("Found mapping for session.  Session Removed.");
/*    */       } else {
/* 38 */         this.log.debug("No mapping for session found.  Ignoring.");
/*    */       } 
/*    */     }
/*    */     
/* 42 */     this; MANAGED_SESSIONS.remove(str);
/* 43 */     this.ID_TO_SESSION_KEY_MAPPING.remove(paramString);
/*    */   }
/*    */   
/*    */   public synchronized HttpSession removeSessionByMappingId(String paramString) {
/* 47 */     this; HttpSession httpSession = MANAGED_SESSIONS.get(paramString);
/* 48 */     if (httpSession != null) {
/* 49 */       removeBySessionById(httpSession.getId());
/*    */     }
/*    */     
/* 52 */     return httpSession;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jasig/cas/client/session/HashMapBackedSessionMappingStorage.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */