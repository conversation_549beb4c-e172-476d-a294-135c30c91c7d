/*    */ package org.jasig.cas.client.session;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.FilterChain;
/*    */ import javax.servlet.FilterConfig;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletRequest;
/*    */ import javax.servlet.ServletResponse;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import org.jasig.cas.client.util.AbstractConfigurationFilter;
/*    */ 
/*    */ public class SingleSignOutFilter extends AbstractConfigurationFilter {
/* 13 */   private static final SingleSignOutHandler handler = new SingleSignOutHandler();
/*    */   
/*    */   public void init(FilterConfig paramFilterConfig) throws ServletException {
/* 16 */     if (!isIgnoreInitConfiguration()) {
/* 17 */       handler.setArtifactParameterName(getPropertyFromInitParams(paramFilterConfig, "artifactParameterName", "ticket"));
/* 18 */       handler.setLogoutParameterName(getPropertyFromInitParams(paramFilterConfig, "logoutParameterName", "logoutRequest"));
/*    */       
/* 20 */       handler.setClusterNodeUrls(getPropertyFromInitParams(paramFilterConfig, "clusterNodeUrls", ""));
/*    */     } 
/* 22 */     handler.init();
/*    */   }
/*    */   
/*    */   public void setArtifactParameterName(String paramString) {
/* 26 */     handler.setArtifactParameterName(paramString);
/*    */   }
/*    */   
/*    */   public void setLogoutParameterName(String paramString) {
/* 30 */     handler.setLogoutParameterName(paramString);
/*    */   }
/*    */   
/*    */   public void setSessionMappingStorage(SessionMappingStorage paramSessionMappingStorage) {
/* 34 */     handler.setSessionMappingStorage(paramSessionMappingStorage);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void doFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/* 41 */     HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/* 42 */     if (handler.isTokenRequest(httpServletRequest))
/* 43 */     { handler.recordSession(httpServletRequest); }
/* 44 */     else { if (handler.isLogoutRequest(httpServletRequest)) {
/* 45 */         handler.destroySession(httpServletRequest); return;
/*    */       } 
/* 47 */       if (handler.isLogoutRequestFromClusterNode(httpServletRequest)) {
/*    */         
/* 49 */         handler.destroySessionFromClusterNode(httpServletRequest);
/*    */         return;
/*    */       }  }
/*    */     
/* 53 */     paramFilterChain.doFilter(paramServletRequest, paramServletResponse);
/*    */   }
/*    */ 
/*    */   
/*    */   public void destroy() {}
/*    */ 
/*    */   
/*    */   protected static SingleSignOutHandler getSingleSignOutHandler() {
/* 61 */     return handler;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jasig/cas/client/session/SingleSignOutFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */