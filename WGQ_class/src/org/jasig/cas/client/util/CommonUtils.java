/*     */ package org.jasig.cas.client.util;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.MalformedURLException;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import java.net.URLEncoder;
/*     */ import java.security.KeyManagementException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.SecureRandom;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Collection;
/*     */ import java.util.Date;
/*     */ import java.util.TimeZone;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.net.ssl.HttpsURLConnection;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import javax.net.ssl.TrustManager;
/*     */ import javax.net.ssl.X509TrustManager;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import org.jasig.cas.client.proxy.ProxyGrantingTicketStorage;
/*     */ import org.jasig.cas.client.validation.ProxyList;
/*     */ import org.jasig.cas.client.validation.ProxyListEditor;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class CommonUtils
/*     */ {
/*  58 */   private static final Log LOG = LogFactory.getLog(CommonUtils.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static final String PARAM_PROXY_GRANTING_TICKET_IOU = "pgtIou";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static final String PARAM_PROXY_GRANTING_TICKET = "pgtId";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String formatForUtcTime(Date paramDate) {
/*  75 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
/*  76 */     simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
/*  77 */     return simpleDateFormat.format(paramDate);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void assertNotNull(Object paramObject, String paramString) {
/*  88 */     if (paramObject == null) {
/*  89 */       throw new IllegalArgumentException(paramString);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void assertNotEmpty(Collection<?> paramCollection, String paramString) {
/* 101 */     assertNotNull(paramCollection, paramString);
/* 102 */     if (paramCollection.isEmpty()) {
/* 103 */       throw new IllegalArgumentException(paramString);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void assertTrue(boolean paramBoolean, String paramString) {
/* 115 */     if (!paramBoolean) {
/* 116 */       throw new IllegalArgumentException(paramString);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isEmpty(String paramString) {
/* 127 */     return (paramString == null || paramString.length() == 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isNotEmpty(String paramString) {
/* 138 */     return !isEmpty(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isBlank(String paramString) {
/* 149 */     return (isEmpty(paramString) || paramString.trim().length() == 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isNotBlank(String paramString) {
/* 160 */     return !isBlank(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String constructRedirectUrl(String paramString1, String paramString2, String paramString3, boolean paramBoolean1, boolean paramBoolean2) {
/*     */     try {
/* 175 */       return paramString1 + ((paramString1.indexOf("?") != -1) ? "&" : "?") + paramString2 + "=" + 
/* 176 */         URLEncoder.encode(paramString3, "UTF-8") + (paramBoolean1 ? "&renew=true" : "") + (paramBoolean2 ? "&gateway=true" : "");
/*     */     
/*     */     }
/* 179 */     catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 180 */       throw new RuntimeException(unsupportedEncodingException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void readAndRespondToProxyReceptorRequest(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, ProxyGrantingTicketStorage paramProxyGrantingTicketStorage) throws IOException {
/* 185 */     String str1 = paramHttpServletRequest.getParameter("pgtIou");
/*     */     
/* 187 */     String str2 = paramHttpServletRequest.getParameter("pgtId");
/*     */     
/* 189 */     if (isBlank(str2) || isBlank(str1)) {
/* 190 */       paramHttpServletResponse.getWriter().write("");
/*     */       
/*     */       return;
/*     */     } 
/* 194 */     if (LOG.isDebugEnabled()) {
/* 195 */       LOG.debug("Received proxyGrantingTicketId [" + str2 + "] for proxyGrantingTicketIou [" + str1 + "]");
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 200 */     paramProxyGrantingTicketStorage.save(str1, str2);
/*     */     
/* 202 */     if (LOG.isDebugEnabled()) {
/* 203 */       LOG.debug("Successfully saved proxyGrantingTicketId [" + str2 + "] for proxyGrantingTicketIou [" + str1 + "]");
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 208 */     paramHttpServletResponse.getWriter().write("<?xml version=\"1.0\"?>");
/* 209 */     paramHttpServletResponse.getWriter().write("<casClient:proxySuccess xmlns:casClient=\"http://www.yale.edu/tp/casClient\" />");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String constructServiceUrl(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, String paramString1, String paramString2, String paramString3, boolean paramBoolean) {
/* 227 */     if (isNotBlank(paramString1)) {
/* 228 */       return paramBoolean ? paramHttpServletResponse.encodeURL(paramString1) : paramString1;
/*     */     }
/*     */     
/* 231 */     StringBuilder stringBuilder = new StringBuilder();
/*     */ 
/*     */     
/* 234 */     if (!paramString2.startsWith("https://") && !paramString2.startsWith("http://")) {
/* 235 */       stringBuilder.append(paramHttpServletRequest.isSecure() ? "https://" : "http://");
/*     */     }
/*     */     
/* 238 */     stringBuilder.append(paramString2);
/* 239 */     stringBuilder.append(paramHttpServletRequest.getRequestURI());
/*     */     
/* 241 */     if (isNotBlank(paramHttpServletRequest.getQueryString())) {
/* 242 */       int i = paramHttpServletRequest.getQueryString().indexOf(paramString3 + "=");
/*     */       
/* 244 */       if (i == 0) {
/* 245 */         String str1 = paramBoolean ? paramHttpServletResponse.encodeURL(stringBuilder.toString()) : stringBuilder.toString();
/* 246 */         if (LOG.isDebugEnabled()) {
/* 247 */           LOG.debug("serviceUrl generated: " + str1);
/*     */         }
/* 249 */         return cleanupUrl(str1);
/*     */       } 
/*     */       
/* 252 */       stringBuilder.append("?");
/*     */       
/* 254 */       if (i == -1) {
/* 255 */         stringBuilder.append(paramHttpServletRequest.getQueryString());
/* 256 */       } else if (i > 0) {
/*     */         
/* 258 */         int j = paramHttpServletRequest.getQueryString().indexOf("&" + paramString3 + "=");
/*     */         
/* 260 */         if (j == -1) {
/* 261 */           stringBuilder.append(paramHttpServletRequest.getQueryString());
/* 262 */         } else if (j > 0) {
/* 263 */           stringBuilder.append(paramHttpServletRequest.getQueryString().substring(0, j));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 269 */     String str = paramBoolean ? paramHttpServletResponse.encodeURL(stringBuilder.toString()) : stringBuilder.toString();
/* 270 */     if (LOG.isDebugEnabled()) {
/* 271 */       LOG.debug("serviceUrl generated: " + str);
/*     */     }
/* 273 */     return cleanupUrl(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String safeGetParameter(HttpServletRequest paramHttpServletRequest, String paramString) {
/* 290 */     if ("POST".equals(paramHttpServletRequest.getMethod()) && "logoutRequest".equals(paramString)) {
/* 291 */       LOG.debug("safeGetParameter called on a POST HttpServletRequest for LogoutRequest.  Cannot complete check safely.  Reverting to standard behavior for this Parameter");
/* 292 */       return paramHttpServletRequest.getParameter(paramString);
/*     */     } 
/* 294 */     return (paramHttpServletRequest.getQueryString() == null || paramHttpServletRequest.getQueryString().indexOf(paramString) == -1) ? null : paramHttpServletRequest.getParameter(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getResponseFromServer(URL paramURL, String paramString) {
/* 305 */     return getResponseFromServer(paramURL, HttpsURLConnection.getDefaultHostnameVerifier(), paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getResponseFromServer(URL paramURL, HostnameVerifier paramHostnameVerifier, String paramString) {
/* 318 */     String str = paramURL.getProtocol();
/* 319 */     if ("https".equalsIgnoreCase(str)) {
/* 320 */       TrustManager[] arrayOfTrustManager = { new X509TrustManager()
/*     */           {
/*     */             public void checkClientTrusted(X509Certificate[] param1ArrayOfX509Certificate, String param1String) throws CertificateException {}
/*     */ 
/*     */ 
/*     */             
/*     */             public void checkServerTrusted(X509Certificate[] param1ArrayOfX509Certificate, String param1String) throws CertificateException {}
/*     */ 
/*     */ 
/*     */             
/*     */             public X509Certificate[] getAcceptedIssuers() {
/* 331 */               return null;
/*     */             }
/*     */           } };
/*     */       
/* 335 */       HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
/* 336 */       SSLContext sSLContext = null;
/*     */       try {
/* 338 */         sSLContext = SSLContext.getInstance("TLS");
/* 339 */       } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/* 340 */         noSuchAlgorithmException.printStackTrace();
/* 341 */         LOG.error(noSuchAlgorithmException.getMessage());
/*     */       } 
/*     */       try {
/* 344 */         sSLContext.init(null, arrayOfTrustManager, new SecureRandom());
/* 345 */       } catch (KeyManagementException keyManagementException) {
/* 346 */         keyManagementException.printStackTrace();
/* 347 */         LOG.error(keyManagementException.getMessage());
/*     */       } 
/* 349 */       HttpsURLConnection.setDefaultSSLSocketFactory(sSLContext.getSocketFactory());
/*     */     } 
/*     */ 
/*     */     
/* 353 */     URLConnection uRLConnection = null; try {
/*     */       BufferedReader bufferedReader;
/* 355 */       uRLConnection = paramURL.openConnection();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 361 */       if (isEmpty(paramString)) {
/* 362 */         bufferedReader = new BufferedReader(new InputStreamReader(uRLConnection.getInputStream()));
/*     */       } else {
/* 364 */         bufferedReader = new BufferedReader(new InputStreamReader(uRLConnection.getInputStream(), paramString));
/*     */       } 
/*     */ 
/*     */       
/* 368 */       StringBuilder stringBuilder = new StringBuilder(255);
/*     */       String str1;
/* 370 */       while ((str1 = bufferedReader.readLine()) != null) {
/* 371 */         stringBuilder.append(str1);
/* 372 */         stringBuilder.append("\n");
/*     */       } 
/* 374 */       return stringBuilder.toString();
/* 375 */     } catch (Exception exception) {
/* 376 */       LOG.error(exception.getMessage(), exception);
/* 377 */       throw new RuntimeException(exception);
/*     */     } finally {
/* 379 */       if (uRLConnection != null && uRLConnection instanceof HttpURLConnection) {
/* 380 */         ((HttpURLConnection)uRLConnection).disconnect();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getResponseFromServer(String paramString1, String paramString2) {
/*     */     try {
/* 394 */       return getResponseFromServer(new URL(paramString1), paramString2);
/* 395 */     } catch (MalformedURLException malformedURLException) {
/* 396 */       throw new IllegalArgumentException(malformedURLException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static ProxyList createProxyList(String paramString) {
/* 401 */     if (isBlank(paramString)) {
/* 402 */       return new ProxyList();
/*     */     }
/*     */     
/* 405 */     ProxyListEditor proxyListEditor = new ProxyListEditor();
/* 406 */     proxyListEditor.setAsText(paramString);
/* 407 */     return (ProxyList)proxyListEditor.getValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void sendRedirect(HttpServletResponse paramHttpServletResponse, String paramString) {
/*     */     try {
/* 418 */       paramHttpServletResponse.sendRedirect(paramString);
/* 419 */     } catch (Exception exception) {
/* 420 */       LOG.warn(exception.getMessage(), exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static final String cleanupUrl(String paramString) {
/* 427 */     if (paramString == null) {
/* 428 */       return null;
/*     */     }
/* 430 */     paramString = paramString.replace(";JSESSIONID", ";jsessionid");
/* 431 */     int i = paramString.indexOf(";jsessionid");
/* 432 */     if (i == -1) {
/* 433 */       return paramString;
/*     */     }
/*     */     
/* 436 */     int j = paramString.indexOf("?");
/* 437 */     if (j < i) {
/* 438 */       return paramString.substring(0, paramString.indexOf(";jsessionid"));
/*     */     }
/* 440 */     return paramString.substring(0, i) + paramString.substring(j);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jasig/cas/client/util/CommonUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */