/*      */ package org.quartz.core;
/*      */ 
/*      */ import com.alibaba.fastjson.JSON;
/*      */ import java.io.InputStream;
/*      */ import java.lang.management.ManagementFactory;
/*      */ import java.rmi.NotBoundException;
/*      */ import java.rmi.Remote;
/*      */ import java.rmi.RemoteException;
/*      */ import java.rmi.registry.LocateRegistry;
/*      */ import java.rmi.registry.Registry;
/*      */ import java.rmi.server.UnicastRemoteObject;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Collection;
/*      */ import java.util.Collections;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedList;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Properties;
/*      */ import java.util.Random;
/*      */ import java.util.Set;
/*      */ import java.util.Timer;
/*      */ import java.util.TimerTask;
/*      */ import javax.management.MBeanServer;
/*      */ import javax.management.ObjectName;
/*      */ import org.quartz.Calendar;
/*      */ import org.quartz.InterruptableJob;
/*      */ import org.quartz.Job;
/*      */ import org.quartz.JobDataMap;
/*      */ import org.quartz.JobDetail;
/*      */ import org.quartz.JobExecutionContext;
/*      */ import org.quartz.JobExecutionException;
/*      */ import org.quartz.JobKey;
/*      */ import org.quartz.JobListener;
/*      */ import org.quartz.ListenerManager;
/*      */ import org.quartz.Matcher;
/*      */ import org.quartz.ObjectAlreadyExistsException;
/*      */ import org.quartz.SchedulerContext;
/*      */ import org.quartz.SchedulerException;
/*      */ import org.quartz.SchedulerListener;
/*      */ import org.quartz.SchedulerMetaData;
/*      */ import org.quartz.Trigger;
/*      */ import org.quartz.TriggerBuilder;
/*      */ import org.quartz.TriggerKey;
/*      */ import org.quartz.TriggerListener;
/*      */ import org.quartz.UnableToInterruptJobException;
/*      */ import org.quartz.core.jmx.QuartzSchedulerMBean;
/*      */ import org.quartz.impl.SchedulerRepository;
/*      */ import org.quartz.impl.matchers.GroupMatcher;
/*      */ import org.quartz.simpl.PropertySettingJobFactory;
/*      */ import org.quartz.spi.JobFactory;
/*      */ import org.quartz.spi.OperableTrigger;
/*      */ import org.quartz.spi.SchedulerPlugin;
/*      */ import org.quartz.spi.SchedulerSignaler;
/*      */ import org.quartz.spi.ThreadExecutor;
/*      */ import org.quartz.utils.Key;
/*      */ import org.quartz.utils.UpdateChecker;
/*      */ import org.slf4j.Logger;
/*      */ import org.slf4j.LoggerFactory;
/*      */ import weaver.integration.logging.Logger;
/*      */ import weaver.integration.logging.LoggerFactory;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class QuartzScheduler
/*      */   implements RemotableQuartzScheduler
/*      */ {
/*  112 */   private Logger newlog = LoggerFactory.getLogger(QuartzScheduler.class);
/*  113 */   private static String VERSION_MAJOR = "UNKNOWN";
/*  114 */   private static String VERSION_MINOR = "UNKNOWN";
/*  115 */   private static String VERSION_ITERATION = "UNKNOWN";
/*      */   
/*      */   static {
/*  118 */     Properties properties = new Properties();
/*  119 */     InputStream inputStream = null;
/*      */     try {
/*  121 */       inputStream = QuartzScheduler.class.getResourceAsStream("quartz-build.properties");
/*  122 */       if (inputStream != null) {
/*  123 */         properties.load(inputStream);
/*  124 */         String str = properties.getProperty("version");
/*  125 */         if (str != null)
/*  126 */         { String[] arrayOfString = str.split("\\.");
/*  127 */           VERSION_MAJOR = arrayOfString[0];
/*  128 */           VERSION_MINOR = arrayOfString[1];
/*  129 */           if (arrayOfString.length > 2) {
/*  130 */             VERSION_ITERATION = arrayOfString[2];
/*      */           } else {
/*  132 */             VERSION_ITERATION = "0";
/*      */           }  }
/*  134 */         else { LoggerFactory.getLogger(QuartzScheduler.class).error("Can't parse Quartz version from quartz-build.properties"); }
/*      */ 
/*      */       
/*      */       } 
/*  138 */     } catch (Exception exception) {
/*  139 */       LoggerFactory.getLogger(QuartzScheduler.class).error("Error loading version info from quartz-build.properties.", exception);
/*      */     } finally {
/*      */       
/*  142 */       if (inputStream != null) {
/*  143 */         try { inputStream.close(); } catch (Exception exception) {}
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private QuartzSchedulerResources resources;
/*      */ 
/*      */ 
/*      */   
/*      */   private QuartzSchedulerThread schedThread;
/*      */ 
/*      */ 
/*      */   
/*      */   private ThreadGroup threadGroup;
/*      */ 
/*      */ 
/*      */   
/*  163 */   private SchedulerContext context = new SchedulerContext();
/*      */   
/*  165 */   private ListenerManager listenerManager = (ListenerManager)new ListenerManagerImpl();
/*      */   
/*  167 */   private HashMap<String, JobListener> internalJobListeners = new HashMap<>(10);
/*      */   
/*  169 */   private HashMap<String, TriggerListener> internalTriggerListeners = new HashMap<>(10);
/*      */   
/*  171 */   private ArrayList<SchedulerListener> internalSchedulerListeners = new ArrayList<>(10);
/*      */   
/*  173 */   private JobFactory jobFactory = (JobFactory)new PropertySettingJobFactory();
/*      */   
/*  175 */   ExecutingJobsManager jobMgr = null;
/*      */   
/*  177 */   ErrorLogger errLogger = null;
/*      */   
/*      */   private SchedulerSignaler signaler;
/*      */   
/*  181 */   private Random random = new Random();
/*      */   
/*  183 */   private ArrayList<Object> holdToPreventGC = new ArrayList(5);
/*      */   
/*      */   private boolean signalOnSchedulingChange = true;
/*      */   
/*      */   private volatile boolean closed = false;
/*      */   
/*      */   private volatile boolean shuttingDown = false;
/*      */   private boolean boundRemotely = false;
/*  191 */   private QuartzSchedulerMBean jmxBean = null;
/*      */   
/*  193 */   private Date initialStart = null;
/*      */ 
/*      */   
/*      */   private final Timer updateTimer;
/*      */   
/*  198 */   private final Logger log = LoggerFactory.getLogger(getClass());
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public QuartzScheduler(QuartzSchedulerResources paramQuartzSchedulerResources, long paramLong1, @Deprecated long paramLong2) throws SchedulerException {
/*  222 */     this.resources = paramQuartzSchedulerResources;
/*  223 */     if (paramQuartzSchedulerResources.getJobStore() instanceof JobListener) {
/*  224 */       addInternalJobListener((JobListener)paramQuartzSchedulerResources.getJobStore());
/*      */     }
/*      */     
/*  227 */     this.schedThread = new QuartzSchedulerThread(this, paramQuartzSchedulerResources);
/*  228 */     ThreadExecutor threadExecutor = paramQuartzSchedulerResources.getThreadExecutor();
/*  229 */     threadExecutor.execute((Thread)this.schedThread);
/*  230 */     if (paramLong1 > 0L) {
/*  231 */       this.schedThread.setIdleWaitTime(paramLong1);
/*      */     }
/*      */     
/*  234 */     this.jobMgr = new ExecutingJobsManager();
/*  235 */     addInternalJobListener((JobListener)this.jobMgr);
/*  236 */     this.errLogger = new ErrorLogger();
/*  237 */     addInternalSchedulerListener((SchedulerListener)this.errLogger);
/*      */     
/*  239 */     this.signaler = (SchedulerSignaler)new SchedulerSignalerImpl(this, this.schedThread);
/*      */     
/*  241 */     if (shouldRunUpdateCheck()) {
/*  242 */       this.updateTimer = scheduleUpdateCheck();
/*      */     } else {
/*  244 */       this.updateTimer = null;
/*      */     } 
/*  246 */     getLog().info("Quartz Scheduler v." + getVersion() + " created.");
/*      */   }
/*      */ 
/*      */   
/*      */   public void initialize() throws SchedulerException {
/*      */     try {
/*  252 */       bind();
/*  253 */     } catch (Exception exception) {
/*  254 */       throw new SchedulerException("Unable to bind scheduler to RMI Registry.", exception);
/*      */     } 
/*      */ 
/*      */     
/*  258 */     if (this.resources.getJMXExport()) {
/*      */       try {
/*  260 */         registerJMX();
/*  261 */       } catch (Exception exception) {
/*  262 */         throw new SchedulerException("Unable to register scheduler with MBeanServer.", exception);
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  311 */     getLog().info("Scheduler meta-data: " + (new SchedulerMetaData(
/*  312 */           getSchedulerName(), 
/*  313 */           getSchedulerInstanceId(), getClass(), this.boundRemotely, (runningSince() != null), 
/*  314 */           isInStandbyMode(), isShutdown(), runningSince(), 
/*  315 */           numJobsExecuted(), getJobStoreClass(), 
/*  316 */           supportsPersistence(), isClustered(), getThreadPoolClass(), 
/*  317 */           getThreadPoolSize(), getVersion())).toString());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getVersion() {
/*  329 */     return getVersionMajor() + "." + getVersionMinor() + "." + 
/*  330 */       getVersionIteration();
/*      */   }
/*      */   
/*      */   public static String getVersionMajor() {
/*  334 */     return VERSION_MAJOR;
/*      */   }
/*      */   
/*      */   private boolean shouldRunUpdateCheck() {
/*  338 */     if (this.resources.isRunUpdateCheck() && !Boolean.getBoolean("org.quartz.scheduler.skipUpdateCheck") && 
/*  339 */       !Boolean.getBoolean("org.terracotta.quartz.skipUpdateCheck")) {
/*  340 */       return true;
/*      */     }
/*  342 */     return false;
/*      */   }
/*      */   
/*      */   public static String getVersionMinor() {
/*  346 */     return VERSION_MINOR;
/*      */   }
/*      */   
/*      */   public static String getVersionIteration() {
/*  350 */     return VERSION_ITERATION;
/*      */   }
/*      */   
/*      */   public SchedulerSignaler getSchedulerSignaler() {
/*  354 */     return this.signaler;
/*      */   }
/*      */   
/*      */   public Logger getLog() {
/*  358 */     return this.log;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Timer scheduleUpdateCheck() {
/*  365 */     Timer timer = new Timer(true);
/*  366 */     timer.scheduleAtFixedRate((TimerTask)new UpdateChecker(), 1000L, 604800000L);
/*  367 */     return timer;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void registerJMX() throws Exception {
/*  374 */     String str = this.resources.getJMXObjectName();
/*  375 */     MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();
/*  376 */     this.jmxBean = (QuartzSchedulerMBean)new QuartzSchedulerMBeanImpl(this);
/*  377 */     mBeanServer.registerMBean(this.jmxBean, new ObjectName(str));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void unregisterJMX() throws Exception {
/*  384 */     String str = this.resources.getJMXObjectName();
/*  385 */     MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();
/*  386 */     mBeanServer.unregisterMBean(new ObjectName(str));
/*  387 */     this.jmxBean.setSampledStatisticsEnabled(false);
/*  388 */     getLog().info("Scheduler unregistered from name '" + str + "' in the local MBeanServer.");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void bind() throws RemoteException {
/*  397 */     String str1 = this.resources.getRMIRegistryHost();
/*      */     
/*  399 */     if (str1 == null || str1.length() == 0) {
/*      */       return;
/*      */     }
/*      */     
/*  403 */     RemotableQuartzScheduler remotableQuartzScheduler = null;
/*      */     
/*  405 */     if (this.resources.getRMIServerPort() > 0) {
/*      */       
/*  407 */       remotableQuartzScheduler = (RemotableQuartzScheduler)UnicastRemoteObject.exportObject((Remote)this, this.resources.getRMIServerPort());
/*      */     } else {
/*      */       
/*  410 */       remotableQuartzScheduler = (RemotableQuartzScheduler)UnicastRemoteObject.exportObject((Remote)this);
/*      */     } 
/*      */     
/*  413 */     Registry registry = null;
/*      */     
/*  415 */     if (this.resources.getRMICreateRegistryStrategy().equals("as_needed")) {
/*      */ 
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  423 */         registry = LocateRegistry.getRegistry(this.resources
/*  424 */             .getRMIRegistryPort());
/*  425 */         registry.list();
/*  426 */       } catch (Exception exception) {
/*  427 */         registry = LocateRegistry.createRegistry(this.resources
/*  428 */             .getRMIRegistryPort());
/*      */       } 
/*  430 */     } else if (this.resources.getRMICreateRegistryStrategy().equals("always")) {
/*      */       
/*      */       try {
/*  433 */         registry = LocateRegistry.createRegistry(this.resources
/*  434 */             .getRMIRegistryPort());
/*  435 */       } catch (Exception exception) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  441 */         registry = LocateRegistry.getRegistry(this.resources
/*  442 */             .getRMIRegistryPort());
/*      */       } 
/*      */     } else {
/*  445 */       registry = LocateRegistry.getRegistry(this.resources
/*  446 */           .getRMIRegistryHost(), this.resources.getRMIRegistryPort());
/*      */     } 
/*      */     
/*  449 */     String str2 = this.resources.getRMIBindName();
/*      */     
/*  451 */     registry.rebind(str2, (Remote)remotableQuartzScheduler);
/*      */     
/*  453 */     this.boundRemotely = true;
/*      */     
/*  455 */     getLog().info("Scheduler bound to RMI registry under name '" + str2 + "'");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void unBind() throws RemoteException {
/*  464 */     String str1 = this.resources.getRMIRegistryHost();
/*      */     
/*  466 */     if (str1 == null || str1.length() == 0) {
/*      */       return;
/*      */     }
/*      */     
/*  470 */     Registry registry = LocateRegistry.getRegistry(this.resources
/*  471 */         .getRMIRegistryHost(), this.resources.getRMIRegistryPort());
/*      */     
/*  473 */     String str2 = this.resources.getRMIBindName();
/*      */     
/*      */     try {
/*  476 */       registry.unbind(str2);
/*  477 */       UnicastRemoteObject.unexportObject((Remote)this, true);
/*  478 */     } catch (NotBoundException notBoundException) {}
/*      */ 
/*      */     
/*  481 */     getLog().info("Scheduler un-bound from name '" + str2 + "' in RMI registry");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSchedulerName() {
/*  490 */     return this.resources.getName();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSchedulerInstanceId() {
/*  499 */     return this.resources.getInstanceId();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ThreadGroup getSchedulerThreadGroup() {
/*  508 */     if (this.threadGroup == null) {
/*  509 */       this
/*  510 */         .threadGroup = new ThreadGroup("QuartzScheduler:" + getSchedulerName());
/*  511 */       if (this.resources.getMakeSchedulerThreadDaemon()) {
/*  512 */         this.threadGroup.setDaemon(true);
/*      */       }
/*      */     } 
/*      */     
/*  516 */     return this.threadGroup;
/*      */   }
/*      */   
/*      */   public void addNoGCObject(Object paramObject) {
/*  520 */     this.holdToPreventGC.add(paramObject);
/*      */   }
/*      */   
/*      */   public boolean removeNoGCObject(Object paramObject) {
/*  524 */     return this.holdToPreventGC.remove(paramObject);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public SchedulerContext getSchedulerContext() throws SchedulerException {
/*  533 */     return this.context;
/*      */   }
/*      */   
/*      */   public boolean isSignalOnSchedulingChange() {
/*  537 */     return this.signalOnSchedulingChange;
/*      */   }
/*      */   
/*      */   public void setSignalOnSchedulingChange(boolean paramBoolean) {
/*  541 */     this.signalOnSchedulingChange = paramBoolean;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void start() throws SchedulerException {
/*  562 */     if (this.shuttingDown || this.closed) {
/*  563 */       throw new SchedulerException("The Scheduler cannot be restarted after shutdown() has been called.");
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  569 */     notifySchedulerListenersStarting();
/*      */     
/*  571 */     if (this.initialStart == null) {
/*  572 */       this.initialStart = new Date();
/*  573 */       this.resources.getJobStore().schedulerStarted();
/*  574 */       startPlugins();
/*      */     } else {
/*  576 */       this.resources.getJobStore().schedulerResumed();
/*      */     } 
/*      */     
/*  579 */     this.schedThread.togglePause(false);
/*      */     
/*  581 */     getLog().info("Scheduler " + this.resources
/*  582 */         .getUniqueIdentifier() + " started.");
/*      */     
/*  584 */     notifySchedulerListenersStarted();
/*      */   }
/*      */ 
/*      */   
/*      */   public void startDelayed(final int seconds) throws SchedulerException {
/*  589 */     if (this.shuttingDown || this.closed) {
/*  590 */       throw new SchedulerException("The Scheduler cannot be restarted after shutdown() has been called.");
/*      */     }
/*      */ 
/*      */     
/*  594 */     Thread thread = new Thread(new Runnable() { public void run() {
/*      */             try {
/*  596 */               Thread.sleep(seconds * 1000L);
/*  597 */             } catch (InterruptedException interruptedException) {} try {
/*  598 */               QuartzScheduler.this.start();
/*  599 */             } catch (SchedulerException schedulerException) {
/*  600 */               QuartzScheduler.this.getLog().error("Unable to start secheduler after startup delay.", (Throwable)schedulerException);
/*      */             } 
/*      */           } }
/*      */       );
/*  604 */     thread.start();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void standby() {
/*  617 */     this.resources.getJobStore().schedulerPaused();
/*  618 */     this.schedThread.togglePause(true);
/*  619 */     getLog().info("Scheduler " + this.resources
/*  620 */         .getUniqueIdentifier() + " paused.");
/*  621 */     notifySchedulerListenersInStandbyMode();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isInStandbyMode() {
/*  630 */     return this.schedThread.isPaused();
/*      */   }
/*      */   
/*      */   public Date runningSince() {
/*  634 */     if (this.initialStart == null)
/*  635 */       return null; 
/*  636 */     return new Date(this.initialStart.getTime());
/*      */   }
/*      */   
/*      */   public int numJobsExecuted() {
/*  640 */     return this.jobMgr.getNumJobsFired();
/*      */   }
/*      */   
/*      */   public Class<?> getJobStoreClass() {
/*  644 */     return this.resources.getJobStore().getClass();
/*      */   }
/*      */   
/*      */   public boolean supportsPersistence() {
/*  648 */     return this.resources.getJobStore().supportsPersistence();
/*      */   }
/*      */   
/*      */   public boolean isClustered() {
/*  652 */     return this.resources.getJobStore().isClustered();
/*      */   }
/*      */   
/*      */   public Class<?> getThreadPoolClass() {
/*  656 */     return this.resources.getThreadPool().getClass();
/*      */   }
/*      */   
/*      */   public int getThreadPoolSize() {
/*  660 */     return this.resources.getThreadPool().getPoolSize();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void shutdown() {
/*  675 */     shutdown(false);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void shutdown(boolean paramBoolean) {
/*  694 */     if (this.shuttingDown || this.closed) {
/*      */       return;
/*      */     }
/*      */     
/*  698 */     this.shuttingDown = true;
/*      */     
/*  700 */     getLog().info("Scheduler " + this.resources
/*  701 */         .getUniqueIdentifier() + " shutting down.");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  726 */     standby();
/*      */     
/*  728 */     this.schedThread.halt(paramBoolean);
/*      */     
/*  730 */     notifySchedulerListenersShuttingdown();
/*      */     
/*  732 */     if ((this.resources.isInterruptJobsOnShutdown() && !paramBoolean) || (this.resources
/*  733 */       .isInterruptJobsOnShutdownWithWait() && paramBoolean)) {
/*  734 */       List<JobExecutionContext> list = getCurrentlyExecutingJobs();
/*  735 */       for (JobExecutionContext jobExecutionContext : list) {
/*  736 */         if (jobExecutionContext.getJobInstance() instanceof InterruptableJob) {
/*      */           try {
/*  738 */             ((InterruptableJob)jobExecutionContext.getJobInstance()).interrupt();
/*  739 */           } catch (Throwable throwable) {
/*      */             
/*  741 */             getLog().warn("Encountered error when interrupting job {} during shutdown: {}", jobExecutionContext.getJobDetail().getKey(), throwable);
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/*  746 */     this.resources.getThreadPool().shutdown(paramBoolean);
/*      */     
/*  748 */     this.closed = true;
/*      */     
/*  750 */     if (this.resources.getJMXExport()) {
/*      */       try {
/*  752 */         unregisterJMX();
/*  753 */       } catch (Exception exception) {}
/*      */     }
/*      */ 
/*      */     
/*  757 */     if (this.boundRemotely) {
/*      */       try {
/*  759 */         unBind();
/*  760 */       } catch (RemoteException remoteException) {}
/*      */     }
/*      */ 
/*      */     
/*  764 */     shutdownPlugins();
/*      */     
/*  766 */     this.resources.getJobStore().shutdown();
/*      */     
/*  768 */     notifySchedulerListenersShutdown();
/*      */     
/*  770 */     SchedulerRepository.getInstance().remove(this.resources.getName());
/*      */     
/*  772 */     this.holdToPreventGC.clear();
/*      */     
/*  774 */     if (this.updateTimer != null) {
/*  775 */       this.updateTimer.cancel();
/*      */     }
/*  777 */     getLog().info("Scheduler " + this.resources
/*  778 */         .getUniqueIdentifier() + " shutdown complete.");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isShutdown() {
/*  788 */     return this.closed;
/*      */   }
/*      */   
/*      */   public boolean isShuttingDown() {
/*  792 */     return this.shuttingDown;
/*      */   }
/*      */   
/*      */   public boolean isStarted() {
/*  796 */     return (!this.shuttingDown && !this.closed && !isInStandbyMode() && this.initialStart != null);
/*      */   }
/*      */   
/*      */   public void validateState() throws SchedulerException {
/*  800 */     if (isShutdown()) {
/*  801 */       throw new SchedulerException("The Scheduler has been shutdown.");
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<JobExecutionContext> getCurrentlyExecutingJobs() {
/*  825 */     return this.jobMgr.getExecutingJobs();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Date scheduleJob(JobDetail paramJobDetail, Trigger paramTrigger) throws SchedulerException {
/*  852 */     validateState();
/*      */     
/*  854 */     if (paramJobDetail == null) {
/*  855 */       throw new SchedulerException("JobDetail cannot be null");
/*      */     }
/*      */     
/*  858 */     if (paramTrigger == null) {
/*  859 */       throw new SchedulerException("Trigger cannot be null");
/*      */     }
/*      */     
/*  862 */     if (paramJobDetail.getKey() == null) {
/*  863 */       throw new SchedulerException("Job's key cannot be null");
/*      */     }
/*      */     
/*  866 */     if (paramJobDetail.getJobClass() == null) {
/*  867 */       throw new SchedulerException("Job's class cannot be null");
/*      */     }
/*      */     
/*  870 */     OperableTrigger operableTrigger = (OperableTrigger)paramTrigger;
/*      */     
/*  872 */     if (paramTrigger.getJobKey() == null) {
/*  873 */       operableTrigger.setJobKey(paramJobDetail.getKey());
/*  874 */     } else if (!paramTrigger.getJobKey().equals(paramJobDetail.getKey())) {
/*  875 */       throw new SchedulerException("Trigger does not reference given job!");
/*      */     } 
/*      */ 
/*      */     
/*  879 */     operableTrigger.validate();
/*      */     
/*  881 */     Calendar calendar = null;
/*  882 */     if (paramTrigger.getCalendarName() != null) {
/*  883 */       calendar = this.resources.getJobStore().retrieveCalendar(paramTrigger.getCalendarName());
/*      */     }
/*  885 */     Date date = operableTrigger.computeFirstFireTime(calendar);
/*      */     
/*  887 */     if (date == null) {
/*  888 */       throw new SchedulerException("Based on configured schedule, the given trigger '" + paramTrigger
/*  889 */           .getKey() + "' will never fire.");
/*      */     }
/*      */     
/*  892 */     this.resources.getJobStore().storeJobAndTrigger(paramJobDetail, operableTrigger);
/*  893 */     notifySchedulerListenersJobAdded(paramJobDetail);
/*  894 */     notifySchedulerThread(paramTrigger.getNextFireTime().getTime());
/*  895 */     notifySchedulerListenersSchduled(paramTrigger);
/*      */     
/*  897 */     return date;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Date scheduleJob(Trigger paramTrigger) throws SchedulerException {
/*  913 */     validateState();
/*      */     
/*  915 */     if (paramTrigger == null) {
/*  916 */       throw new SchedulerException("Trigger cannot be null");
/*      */     }
/*      */     
/*  919 */     OperableTrigger operableTrigger = (OperableTrigger)paramTrigger;
/*      */     
/*  921 */     operableTrigger.validate();
/*      */     
/*  923 */     Calendar calendar = null;
/*  924 */     if (paramTrigger.getCalendarName() != null) {
/*  925 */       calendar = this.resources.getJobStore().retrieveCalendar(paramTrigger.getCalendarName());
/*  926 */       if (calendar == null) {
/*  927 */         throw new SchedulerException("Calendar not found: " + paramTrigger
/*  928 */             .getCalendarName());
/*      */       }
/*      */     } 
/*  931 */     Date date = operableTrigger.computeFirstFireTime(calendar);
/*      */     
/*  933 */     if (date == null) {
/*  934 */       throw new SchedulerException("Based on configured schedule, the given trigger '" + paramTrigger
/*  935 */           .getKey() + "' will never fire.");
/*      */     }
/*      */     
/*  938 */     this.resources.getJobStore().storeTrigger(operableTrigger, false);
/*  939 */     notifySchedulerThread(paramTrigger.getNextFireTime().getTime());
/*  940 */     notifySchedulerListenersSchduled(paramTrigger);
/*      */     
/*  942 */     return date;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void addJob(JobDetail paramJobDetail, boolean paramBoolean) throws SchedulerException {
/*  964 */     addJob(paramJobDetail, paramBoolean, false);
/*      */   }
/*      */   
/*      */   public void addJob(JobDetail paramJobDetail, boolean paramBoolean1, boolean paramBoolean2) throws SchedulerException {
/*  968 */     validateState();
/*      */     
/*  970 */     if (!paramBoolean2 && !paramJobDetail.isDurable()) {
/*  971 */       throw new SchedulerException("Jobs added with no trigger must be durable.");
/*      */     }
/*      */ 
/*      */     
/*  975 */     this.resources.getJobStore().storeJob(paramJobDetail, paramBoolean1);
/*  976 */     notifySchedulerThread(0L);
/*  977 */     notifySchedulerListenersJobAdded(paramJobDetail);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean deleteJob(JobKey paramJobKey) throws SchedulerException {
/*  991 */     validateState();
/*      */     
/*  993 */     boolean bool = false;
/*      */     
/*  995 */     List<? extends Trigger> list = getTriggersOfJob(paramJobKey);
/*  996 */     for (Trigger trigger : list) {
/*  997 */       if (!unscheduleJob(trigger.getKey())) {
/*      */ 
/*      */ 
/*      */         
/* 1001 */         StringBuilder stringBuilder = (new StringBuilder()).append("Unable to unschedule trigger [").append(trigger.getKey()).append("] while deleting job [").append(paramJobKey).append("]");
/*      */         
/* 1003 */         throw new SchedulerException(stringBuilder.toString());
/*      */       } 
/* 1005 */       bool = true;
/*      */     } 
/*      */     
/* 1008 */     bool = (this.resources.getJobStore().removeJob(paramJobKey) || bool) ? true : false;
/* 1009 */     if (bool) {
/* 1010 */       notifySchedulerThread(0L);
/* 1011 */       notifySchedulerListenersJobDeleted(paramJobKey);
/*      */     } 
/* 1013 */     return bool;
/*      */   }
/*      */   
/*      */   public boolean deleteJobs(List<JobKey> paramList) throws SchedulerException {
/* 1017 */     validateState();
/*      */     
/* 1019 */     boolean bool = false;
/*      */     
/* 1021 */     bool = this.resources.getJobStore().removeJobs(paramList);
/* 1022 */     notifySchedulerThread(0L);
/* 1023 */     for (JobKey jobKey : paramList)
/* 1024 */       notifySchedulerListenersJobDeleted(jobKey); 
/* 1025 */     return bool;
/*      */   }
/*      */   
/*      */   public void scheduleJobs(Map<JobDetail, Set<? extends Trigger>> paramMap, boolean paramBoolean) throws SchedulerException {
/* 1029 */     validateState();
/*      */ 
/*      */     
/* 1032 */     for (Map.Entry<JobDetail, Set<? extends Trigger>> entry : paramMap.entrySet()) {
/* 1033 */       JobDetail jobDetail = (JobDetail)entry.getKey();
/* 1034 */       if (jobDetail == null)
/*      */         continue; 
/* 1036 */       Set set = (Set)entry.getValue();
/* 1037 */       if (set == null)
/*      */         continue; 
/* 1039 */       for (Trigger trigger : set) {
/* 1040 */         OperableTrigger operableTrigger = (OperableTrigger)trigger;
/* 1041 */         operableTrigger.setJobKey(jobDetail.getKey());
/*      */         
/* 1043 */         operableTrigger.validate();
/*      */         
/* 1045 */         Calendar calendar = null;
/* 1046 */         if (trigger.getCalendarName() != null) {
/* 1047 */           calendar = this.resources.getJobStore().retrieveCalendar(trigger.getCalendarName());
/* 1048 */           if (calendar == null) {
/* 1049 */             throw new SchedulerException("Calendar '" + trigger
/* 1050 */                 .getCalendarName() + "' not found for trigger: " + trigger.getKey());
/*      */           }
/*      */         } 
/* 1053 */         Date date = operableTrigger.computeFirstFireTime(calendar);
/*      */         
/* 1055 */         if (date == null) {
/* 1056 */           throw new SchedulerException("Based on configured schedule, the given trigger will never fire.");
/*      */         }
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1062 */     this.resources.getJobStore().storeJobsAndTriggers(paramMap, paramBoolean);
/* 1063 */     notifySchedulerThread(0L);
/* 1064 */     for (JobDetail jobDetail : paramMap.keySet()) {
/* 1065 */       notifySchedulerListenersJobAdded(jobDetail);
/*      */     }
/*      */   }
/*      */   
/*      */   public void scheduleJob(JobDetail paramJobDetail, Set<? extends Trigger> paramSet, boolean paramBoolean) throws SchedulerException {
/* 1070 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1071 */     hashMap.put(paramJobDetail, paramSet);
/* 1072 */     scheduleJobs((Map)hashMap, paramBoolean);
/*      */   }
/*      */   
/*      */   public boolean unscheduleJobs(List<TriggerKey> paramList) throws SchedulerException {
/* 1076 */     validateState();
/*      */     
/* 1078 */     boolean bool = false;
/*      */     
/* 1080 */     bool = this.resources.getJobStore().removeTriggers(paramList);
/* 1081 */     notifySchedulerThread(0L);
/* 1082 */     for (TriggerKey triggerKey : paramList)
/* 1083 */       notifySchedulerListenersUnscheduled(triggerKey); 
/* 1084 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean unscheduleJob(TriggerKey paramTriggerKey) throws SchedulerException {
/* 1094 */     validateState();
/*      */     
/* 1096 */     if (this.resources.getJobStore().removeTrigger(paramTriggerKey)) {
/* 1097 */       notifySchedulerThread(0L);
/* 1098 */       notifySchedulerListenersUnscheduled(paramTriggerKey);
/*      */     } else {
/* 1100 */       return false;
/*      */     } 
/*      */     
/* 1103 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Date rescheduleJob(TriggerKey paramTriggerKey, Trigger paramTrigger) throws SchedulerException {
/* 1122 */     validateState();
/*      */     
/* 1124 */     if (paramTriggerKey == null) {
/* 1125 */       throw new IllegalArgumentException("triggerKey cannot be null");
/*      */     }
/* 1127 */     if (paramTrigger == null) {
/* 1128 */       throw new IllegalArgumentException("newTrigger cannot be null");
/*      */     }
/*      */     
/* 1131 */     OperableTrigger operableTrigger = (OperableTrigger)paramTrigger;
/* 1132 */     Trigger trigger = getTrigger(paramTriggerKey);
/* 1133 */     if (trigger == null) {
/* 1134 */       return null;
/*      */     }
/* 1136 */     operableTrigger.setJobKey(trigger.getJobKey());
/*      */     
/* 1138 */     operableTrigger.validate();
/*      */     
/* 1140 */     Calendar calendar = null;
/* 1141 */     if (paramTrigger.getCalendarName() != null) {
/* 1142 */       calendar = this.resources.getJobStore().retrieveCalendar(paramTrigger
/* 1143 */           .getCalendarName());
/*      */     }
/* 1145 */     Date date = operableTrigger.computeFirstFireTime(calendar);
/*      */     
/* 1147 */     if (date == null) {
/* 1148 */       throw new SchedulerException("Based on configured schedule, the given trigger will never fire.");
/*      */     }
/*      */ 
/*      */     
/* 1152 */     if (this.resources.getJobStore().replaceTrigger(paramTriggerKey, operableTrigger)) {
/* 1153 */       notifySchedulerThread(paramTrigger.getNextFireTime().getTime());
/* 1154 */       notifySchedulerListenersUnscheduled(paramTriggerKey);
/* 1155 */       notifySchedulerListenersSchduled(paramTrigger);
/*      */     } else {
/* 1157 */       return null;
/*      */     } 
/*      */     
/* 1160 */     return date;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private String newTriggerId() {
/* 1166 */     long l = this.random.nextLong();
/* 1167 */     if (l < 0L) {
/* 1168 */       l = -l;
/*      */     }
/* 1170 */     return "MT_" + 
/* 1171 */       Long.toString(l, 30 + (int)(System.currentTimeMillis() % 7L));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void triggerJob(JobKey paramJobKey, JobDataMap paramJobDataMap) throws SchedulerException {
/* 1182 */     validateState();
/*      */     
/* 1184 */     OperableTrigger operableTrigger = (OperableTrigger)TriggerBuilder.newTrigger().withIdentity(newTriggerId(), "DEFAULT").forJob(paramJobKey).build();
/* 1185 */     operableTrigger.computeFirstFireTime(null);
/* 1186 */     if (paramJobDataMap != null) {
/* 1187 */       operableTrigger.setJobDataMap(paramJobDataMap);
/*      */     }
/*      */     
/* 1190 */     boolean bool = true;
/* 1191 */     while (bool) {
/*      */       try {
/* 1193 */         this.resources.getJobStore().storeTrigger(operableTrigger, false);
/* 1194 */         bool = false;
/* 1195 */       } catch (ObjectAlreadyExistsException objectAlreadyExistsException) {
/* 1196 */         operableTrigger.setKey(new TriggerKey(newTriggerId(), "DEFAULT"));
/*      */       } 
/*      */     } 
/*      */     
/* 1200 */     notifySchedulerThread(operableTrigger.getNextFireTime().getTime());
/* 1201 */     notifySchedulerListenersSchduled((Trigger)operableTrigger);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void triggerJob(OperableTrigger paramOperableTrigger) throws SchedulerException {
/* 1210 */     validateState();
/*      */     
/* 1212 */     paramOperableTrigger.computeFirstFireTime(null);
/*      */     
/* 1214 */     boolean bool = true;
/* 1215 */     while (bool) {
/*      */       try {
/* 1217 */         this.resources.getJobStore().storeTrigger(paramOperableTrigger, false);
/* 1218 */         bool = false;
/* 1219 */       } catch (ObjectAlreadyExistsException objectAlreadyExistsException) {
/* 1220 */         paramOperableTrigger.setKey(new TriggerKey(newTriggerId(), "DEFAULT"));
/*      */       } 
/*      */     } 
/*      */     
/* 1224 */     notifySchedulerThread(paramOperableTrigger.getNextFireTime().getTime());
/* 1225 */     notifySchedulerListenersSchduled((Trigger)paramOperableTrigger);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void pauseTrigger(TriggerKey paramTriggerKey) throws SchedulerException {
/* 1235 */     validateState();
/*      */     
/* 1237 */     this.resources.getJobStore().pauseTrigger(paramTriggerKey);
/* 1238 */     notifySchedulerThread(0L);
/* 1239 */     notifySchedulerListenersPausedTrigger(paramTriggerKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void pauseTriggers(GroupMatcher<TriggerKey> paramGroupMatcher) throws SchedulerException {
/* 1250 */     validateState();
/*      */     
/* 1252 */     if (paramGroupMatcher == null) {
/* 1253 */       paramGroupMatcher = GroupMatcher.groupEquals("DEFAULT");
/*      */     }
/*      */     
/* 1256 */     Collection collection = this.resources.getJobStore().pauseTriggers(paramGroupMatcher);
/* 1257 */     notifySchedulerThread(0L);
/* 1258 */     for (String str : collection) {
/* 1259 */       notifySchedulerListenersPausedTriggers(str);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void pauseJob(JobKey paramJobKey) throws SchedulerException {
/* 1271 */     validateState();
/*      */     
/* 1273 */     this.resources.getJobStore().pauseJob(paramJobKey);
/* 1274 */     notifySchedulerThread(0L);
/* 1275 */     notifySchedulerListenersPausedJob(paramJobKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void pauseJobs(GroupMatcher<JobKey> paramGroupMatcher) throws SchedulerException {
/* 1287 */     validateState();
/*      */     
/* 1289 */     if (paramGroupMatcher == null) {
/* 1290 */       paramGroupMatcher = GroupMatcher.groupEquals("DEFAULT");
/*      */     }
/*      */     
/* 1293 */     Collection collection = this.resources.getJobStore().pauseJobs(paramGroupMatcher);
/* 1294 */     notifySchedulerThread(0L);
/* 1295 */     for (String str : collection) {
/* 1296 */       notifySchedulerListenersPausedJobs(str);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void resumeTrigger(TriggerKey paramTriggerKey) throws SchedulerException {
/* 1313 */     validateState();
/*      */     
/* 1315 */     this.resources.getJobStore().resumeTrigger(paramTriggerKey);
/* 1316 */     notifySchedulerThread(0L);
/* 1317 */     notifySchedulerListenersResumedTrigger(paramTriggerKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void resumeTriggers(GroupMatcher<TriggerKey> paramGroupMatcher) throws SchedulerException {
/* 1334 */     validateState();
/*      */     
/* 1336 */     if (paramGroupMatcher == null) {
/* 1337 */       paramGroupMatcher = GroupMatcher.groupEquals("DEFAULT");
/*      */     }
/*      */     
/* 1340 */     Collection collection = this.resources.getJobStore().resumeTriggers(paramGroupMatcher);
/* 1341 */     notifySchedulerThread(0L);
/* 1342 */     for (String str : collection) {
/* 1343 */       notifySchedulerListenersResumedTriggers(str);
/*      */     }
/*      */   }
/*      */   
/*      */   public Set<String> getPausedTriggerGroups() throws SchedulerException {
/* 1348 */     return this.resources.getJobStore().getPausedTriggerGroups();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void resumeJob(JobKey paramJobKey) throws SchedulerException {
/* 1365 */     validateState();
/*      */     
/* 1367 */     this.resources.getJobStore().resumeJob(paramJobKey);
/* 1368 */     notifySchedulerThread(0L);
/* 1369 */     notifySchedulerListenersResumedJob(paramJobKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void resumeJobs(GroupMatcher<JobKey> paramGroupMatcher) throws SchedulerException {
/* 1387 */     validateState();
/*      */     
/* 1389 */     if (paramGroupMatcher == null) {
/* 1390 */       paramGroupMatcher = GroupMatcher.groupEquals("DEFAULT");
/*      */     }
/*      */     
/* 1393 */     Collection collection = this.resources.getJobStore().resumeJobs(paramGroupMatcher);
/* 1394 */     notifySchedulerThread(0L);
/* 1395 */     for (String str : collection) {
/* 1396 */       notifySchedulerListenersResumedJobs(str);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void pauseAll() throws SchedulerException {
/* 1416 */     validateState();
/*      */     
/* 1418 */     this.resources.getJobStore().pauseAll();
/* 1419 */     notifySchedulerThread(0L);
/* 1420 */     notifySchedulerListenersPausedTriggers(null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void resumeAll() throws SchedulerException {
/* 1437 */     validateState();
/*      */     
/* 1439 */     this.resources.getJobStore().resumeAll();
/* 1440 */     notifySchedulerThread(0L);
/* 1441 */     notifySchedulerListenersResumedTrigger(null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getJobGroupNames() throws SchedulerException {
/* 1451 */     validateState();
/*      */     
/* 1453 */     return this.resources.getJobStore().getJobGroupNames();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Set<JobKey> getJobKeys(GroupMatcher<JobKey> paramGroupMatcher) throws SchedulerException {
/* 1464 */     validateState();
/*      */     
/* 1466 */     if (paramGroupMatcher == null) {
/* 1467 */       paramGroupMatcher = GroupMatcher.groupEquals("DEFAULT");
/*      */     }
/*      */     
/* 1470 */     return this.resources.getJobStore().getJobKeys(paramGroupMatcher);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<? extends Trigger> getTriggersOfJob(JobKey paramJobKey) throws SchedulerException {
/* 1480 */     validateState();
/*      */     
/* 1482 */     return this.resources.getJobStore().getTriggersForJob(paramJobKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getTriggerGroupNames() throws SchedulerException {
/* 1493 */     validateState();
/*      */     
/* 1495 */     return this.resources.getJobStore().getTriggerGroupNames();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Set<TriggerKey> getTriggerKeys(GroupMatcher<TriggerKey> paramGroupMatcher) throws SchedulerException {
/* 1506 */     validateState();
/*      */     
/* 1508 */     if (paramGroupMatcher == null) {
/* 1509 */       paramGroupMatcher = GroupMatcher.groupEquals("DEFAULT");
/*      */     }
/*      */     
/* 1512 */     return this.resources.getJobStore().getTriggerKeys(paramGroupMatcher);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JobDetail getJobDetail(JobKey paramJobKey) throws SchedulerException {
/* 1522 */     validateState();
/*      */     
/* 1524 */     return this.resources.getJobStore().retrieveJob(paramJobKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Trigger getTrigger(TriggerKey paramTriggerKey) throws SchedulerException {
/* 1534 */     validateState();
/*      */     
/* 1536 */     return (Trigger)this.resources.getJobStore().retrieveTrigger(paramTriggerKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkExists(JobKey paramJobKey) throws SchedulerException {
/* 1548 */     validateState();
/*      */     
/* 1550 */     return this.resources.getJobStore().checkExists(paramJobKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkExists(TriggerKey paramTriggerKey) throws SchedulerException {
/* 1563 */     validateState();
/*      */     
/* 1565 */     return this.resources.getJobStore().checkExists(paramTriggerKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void clear() throws SchedulerException {
/* 1576 */     validateState();
/*      */     
/* 1578 */     this.resources.getJobStore().clearAllSchedulingData();
/* 1579 */     notifySchedulerListenersUnscheduled(null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Trigger.TriggerState getTriggerState(TriggerKey paramTriggerKey) throws SchedulerException {
/* 1591 */     validateState();
/*      */     
/* 1593 */     return this.resources.getJobStore().getTriggerState(paramTriggerKey);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void addCalendar(String paramString, Calendar paramCalendar, boolean paramBoolean1, boolean paramBoolean2) throws SchedulerException {
/* 1607 */     validateState();
/*      */     
/* 1609 */     this.resources.getJobStore().storeCalendar(paramString, paramCalendar, paramBoolean1, paramBoolean2);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean deleteCalendar(String paramString) throws SchedulerException {
/* 1623 */     validateState();
/*      */     
/* 1625 */     return this.resources.getJobStore().removeCalendar(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Calendar getCalendar(String paramString) throws SchedulerException {
/* 1635 */     validateState();
/*      */     
/* 1637 */     return this.resources.getJobStore().retrieveCalendar(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCalendarNames() throws SchedulerException {
/* 1647 */     validateState();
/*      */     
/* 1649 */     return this.resources.getJobStore().getCalendarNames();
/*      */   }
/*      */   
/*      */   public ListenerManager getListenerManager() {
/* 1653 */     return this.listenerManager;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void addInternalJobListener(JobListener paramJobListener) {
/* 1663 */     if (paramJobListener.getName() == null || paramJobListener
/* 1664 */       .getName().length() == 0) {
/* 1665 */       throw new IllegalArgumentException("JobListener name cannot be empty.");
/*      */     }
/*      */ 
/*      */     
/* 1669 */     synchronized (this.internalJobListeners) {
/* 1670 */       this.newlog.info("-----------任务（" + paramJobListener.getName() + ") 加入调度 !");
/* 1671 */       this.internalJobListeners.put(paramJobListener.getName(), paramJobListener);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean removeInternalJobListener(String paramString) {
/* 1685 */     synchronized (this.internalJobListeners) {
/* 1686 */       return (this.internalJobListeners.remove(paramString) != null);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<JobListener> getInternalJobListeners() {
/* 1697 */     synchronized (this.internalJobListeners) {
/* 1698 */       this.newlog.info(JSON.toJSONString("-------job 监听器：" + Collections.unmodifiableList(new LinkedList(this.internalJobListeners.values()))));
/* 1699 */       return Collections.unmodifiableList(new LinkedList<>(this.internalJobListeners.values()));
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JobListener getInternalJobListener(String paramString) {
/* 1710 */     synchronized (this.internalJobListeners) {
/* 1711 */       return this.internalJobListeners.get(paramString);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void addInternalTriggerListener(TriggerListener paramTriggerListener) {
/* 1722 */     if (paramTriggerListener.getName() == null || paramTriggerListener
/* 1723 */       .getName().length() == 0) {
/* 1724 */       throw new IllegalArgumentException("TriggerListener name cannot be empty.");
/*      */     }
/*      */ 
/*      */     
/* 1728 */     synchronized (this.internalTriggerListeners) {
/* 1729 */       this.internalTriggerListeners.put(paramTriggerListener.getName(), paramTriggerListener);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean removeinternalTriggerListener(String paramString) {
/* 1743 */     synchronized (this.internalTriggerListeners) {
/* 1744 */       return (this.internalTriggerListeners.remove(paramString) != null);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<TriggerListener> getInternalTriggerListeners() {
/* 1755 */     synchronized (this.internalTriggerListeners) {
/* 1756 */       return Collections.unmodifiableList(new LinkedList<>(this.internalTriggerListeners.values()));
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public TriggerListener getInternalTriggerListener(String paramString) {
/* 1767 */     synchronized (this.internalTriggerListeners) {
/* 1768 */       return this.internalTriggerListeners.get(paramString);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void addInternalSchedulerListener(SchedulerListener paramSchedulerListener) {
/* 1779 */     synchronized (this.internalSchedulerListeners) {
/* 1780 */       this.internalSchedulerListeners.add(paramSchedulerListener);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean removeInternalSchedulerListener(SchedulerListener paramSchedulerListener) {
/* 1794 */     synchronized (this.internalSchedulerListeners) {
/* 1795 */       return this.internalSchedulerListeners.remove(paramSchedulerListener);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<SchedulerListener> getInternalSchedulerListeners() {
/* 1806 */     synchronized (this.internalSchedulerListeners) {
/* 1807 */       return Collections.unmodifiableList(new ArrayList<>(this.internalSchedulerListeners));
/*      */     } 
/*      */   }
/*      */   
/*      */   protected void notifyJobStoreJobComplete(OperableTrigger paramOperableTrigger, JobDetail paramJobDetail, Trigger.CompletedExecutionInstruction paramCompletedExecutionInstruction) {
/* 1812 */     this.resources.getJobStore().triggeredJobComplete(paramOperableTrigger, paramJobDetail, paramCompletedExecutionInstruction);
/*      */   }
/*      */   
/*      */   protected void notifyJobStoreJobVetoed(OperableTrigger paramOperableTrigger, JobDetail paramJobDetail, Trigger.CompletedExecutionInstruction paramCompletedExecutionInstruction) {
/* 1816 */     this.resources.getJobStore().triggeredJobComplete(paramOperableTrigger, paramJobDetail, paramCompletedExecutionInstruction);
/*      */   }
/*      */   
/*      */   protected void notifySchedulerThread(long paramLong) {
/* 1820 */     if (isSignalOnSchedulingChange()) {
/* 1821 */       this.signaler.signalSchedulingChange(paramLong);
/*      */     }
/*      */   }
/*      */ 
/*      */   
/*      */   private List<TriggerListener> buildTriggerListenerList() throws SchedulerException {
/* 1827 */     LinkedList<TriggerListener> linkedList = new LinkedList();
/* 1828 */     linkedList.addAll(getListenerManager().getTriggerListeners());
/* 1829 */     linkedList.addAll(getInternalTriggerListeners());
/*      */     
/* 1831 */     return linkedList;
/*      */   }
/*      */ 
/*      */   
/*      */   private List<JobListener> buildJobListenerList() throws SchedulerException {
/* 1836 */     LinkedList<JobListener> linkedList = new LinkedList();
/* 1837 */     linkedList.addAll(getListenerManager().getJobListeners());
/* 1838 */     linkedList.addAll(getInternalJobListeners());
/*      */     
/* 1840 */     return linkedList;
/*      */   }
/*      */   
/*      */   private List<SchedulerListener> buildSchedulerListenerList() {
/* 1844 */     LinkedList<SchedulerListener> linkedList = new LinkedList();
/* 1845 */     linkedList.addAll(getListenerManager().getSchedulerListeners());
/* 1846 */     linkedList.addAll(getInternalSchedulerListeners());
/*      */     
/* 1848 */     return linkedList;
/*      */   }
/*      */   
/*      */   private boolean matchJobListener(JobListener paramJobListener, JobKey paramJobKey) {
/* 1852 */     List list = getListenerManager().getJobListenerMatchers(paramJobListener.getName());
/* 1853 */     if (list == null)
/* 1854 */       return true; 
/* 1855 */     for (Matcher matcher : list) {
/* 1856 */       if (matcher.isMatch((Key)paramJobKey))
/* 1857 */         return true; 
/*      */     } 
/* 1859 */     return false;
/*      */   }
/*      */   
/*      */   private boolean matchTriggerListener(TriggerListener paramTriggerListener, TriggerKey paramTriggerKey) {
/* 1863 */     List list = getListenerManager().getTriggerListenerMatchers(paramTriggerListener.getName());
/* 1864 */     if (list == null)
/* 1865 */       return true; 
/* 1866 */     for (Matcher matcher : list) {
/* 1867 */       if (matcher.isMatch((Key)paramTriggerKey))
/* 1868 */         return true; 
/*      */     } 
/* 1870 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean notifyTriggerListenersFired(JobExecutionContext paramJobExecutionContext) throws SchedulerException {
/* 1876 */     boolean bool = false;
/*      */ 
/*      */     
/* 1879 */     List<TriggerListener> list = buildTriggerListenerList();
/*      */ 
/*      */     
/* 1882 */     for (TriggerListener triggerListener : list) {
/*      */       try {
/* 1884 */         if (!matchTriggerListener(triggerListener, paramJobExecutionContext.getTrigger().getKey()))
/*      */           continue; 
/* 1886 */         triggerListener.triggerFired(paramJobExecutionContext.getTrigger(), paramJobExecutionContext);
/*      */         
/* 1888 */         if (triggerListener.vetoJobExecution(paramJobExecutionContext.getTrigger(), paramJobExecutionContext)) {
/* 1889 */           bool = true;
/*      */         }
/* 1891 */       } catch (Exception exception) {
/*      */ 
/*      */         
/* 1894 */         SchedulerException schedulerException = new SchedulerException("TriggerListener '" + triggerListener.getName() + "' threw exception: " + exception.getMessage(), exception);
/* 1895 */         throw schedulerException;
/*      */       } 
/*      */     } 
/*      */     
/* 1899 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifyTriggerListenersMisfired(Trigger paramTrigger) throws SchedulerException {
/* 1906 */     List<TriggerListener> list = buildTriggerListenerList();
/*      */ 
/*      */     
/* 1909 */     for (TriggerListener triggerListener : list) {
/*      */       try {
/* 1911 */         if (!matchTriggerListener(triggerListener, paramTrigger.getKey()))
/*      */           continue; 
/* 1913 */         triggerListener.triggerMisfired(paramTrigger);
/* 1914 */       } catch (Exception exception) {
/*      */ 
/*      */         
/* 1917 */         SchedulerException schedulerException = new SchedulerException("TriggerListener '" + triggerListener.getName() + "' threw exception: " + exception.getMessage(), exception);
/* 1918 */         throw schedulerException;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifyTriggerListenersComplete(JobExecutionContext paramJobExecutionContext, Trigger.CompletedExecutionInstruction paramCompletedExecutionInstruction) throws SchedulerException {
/* 1926 */     List<TriggerListener> list = buildTriggerListenerList();
/*      */ 
/*      */     
/* 1929 */     for (TriggerListener triggerListener : list) {
/* 1930 */       this.newlog.info(" -------计划任务调度 ,判断是否完成任务");
/* 1931 */       this.newlog.info(" -------计划任务调度 ,监听器 ：" + triggerListener.getName());
/*      */       try {
/* 1933 */         if (!matchTriggerListener(triggerListener, paramJobExecutionContext.getTrigger().getKey())) {
/* 1934 */           this.newlog.info(" -------计划任务调度 ,任务未完成 ： " + JSON.toJSONString(paramJobExecutionContext.getJobDetail()));
/*      */           continue;
/*      */         } 
/* 1937 */         triggerListener.triggerComplete(paramJobExecutionContext.getTrigger(), paramJobExecutionContext, paramCompletedExecutionInstruction);
/*      */         
/* 1939 */         this.newlog.info(" -------计划任务调度 ,任务(" + paramJobExecutionContext.getJobDetail().getKey() + ") 完成 ! ");
/* 1940 */       } catch (Exception exception) {
/*      */ 
/*      */         
/* 1943 */         SchedulerException schedulerException = new SchedulerException("TriggerListener '" + triggerListener.getName() + "' threw exception: " + exception.getMessage(), exception);
/* 1944 */         throw schedulerException;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifyJobListenersToBeExecuted(JobExecutionContext paramJobExecutionContext) throws SchedulerException {
/* 1952 */     List<JobListener> list = buildJobListenerList();
/*      */ 
/*      */     
/* 1955 */     for (JobListener jobListener : list) {
/*      */ 
/*      */       
/*      */       try {
/* 1959 */         if (!matchJobListener(jobListener, paramJobExecutionContext.getJobDetail().getKey())) {
/* 1960 */           this.newlog.info(" -------" + paramJobExecutionContext.getJobDetail().getKey() + "  跳过");
/*      */           
/*      */           continue;
/*      */         } 
/* 1964 */         jobListener.jobToBeExecuted(paramJobExecutionContext);
/* 1965 */       } catch (Exception exception) {
/* 1966 */         this.newlog.error(exception);
/*      */ 
/*      */         
/* 1969 */         SchedulerException schedulerException = new SchedulerException("JobListener '" + jobListener.getName() + "' threw exception: " + exception.getMessage(), exception);
/* 1970 */         throw schedulerException;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifyJobListenersWasVetoed(JobExecutionContext paramJobExecutionContext) throws SchedulerException {
/* 1978 */     List<JobListener> list = buildJobListenerList();
/*      */ 
/*      */     
/* 1981 */     for (JobListener jobListener : list) {
/*      */       try {
/* 1983 */         if (!matchJobListener(jobListener, paramJobExecutionContext.getJobDetail().getKey()))
/*      */           continue; 
/* 1985 */         jobListener.jobExecutionVetoed(paramJobExecutionContext);
/* 1986 */       } catch (Exception exception) {
/*      */ 
/*      */         
/* 1989 */         SchedulerException schedulerException = new SchedulerException("JobListener '" + jobListener.getName() + "' threw exception: " + exception.getMessage(), exception);
/* 1990 */         throw schedulerException;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifyJobListenersWasExecuted(JobExecutionContext paramJobExecutionContext, JobExecutionException paramJobExecutionException) throws SchedulerException {
/* 1998 */     List<JobListener> list = buildJobListenerList();
/*      */ 
/*      */     
/* 2001 */     for (JobListener jobListener : list) {
/*      */       try {
/* 2003 */         if (!matchJobListener(jobListener, paramJobExecutionContext.getJobDetail().getKey()))
/*      */           continue; 
/* 2005 */         jobListener.jobWasExecuted(paramJobExecutionContext, paramJobExecutionException);
/* 2006 */       } catch (Exception exception) {
/*      */ 
/*      */         
/* 2009 */         SchedulerException schedulerException = new SchedulerException("JobListener '" + jobListener.getName() + "' threw exception: " + exception.getMessage(), exception);
/* 2010 */         throw schedulerException;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersError(String paramString, SchedulerException paramSchedulerException) {
/* 2017 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2020 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2022 */         schedulerListener.schedulerError(paramString, paramSchedulerException);
/* 2023 */       } catch (Exception exception) {
/* 2024 */         getLog()
/* 2025 */           .error("Error while notifying SchedulerListener of error: ", exception);
/*      */ 
/*      */         
/* 2028 */         getLog().error("  Original error (for notification) was: " + paramString, (Throwable)paramSchedulerException);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersSchduled(Trigger paramTrigger) {
/* 2036 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2039 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2041 */         schedulerListener.jobScheduled(paramTrigger);
/* 2042 */       } catch (Exception exception) {
/* 2043 */         getLog().error("Error while notifying SchedulerListener of scheduled job.  Triger=" + paramTrigger
/*      */             
/* 2045 */             .getKey(), exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersUnscheduled(TriggerKey paramTriggerKey) {
/* 2052 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2055 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2057 */         if (paramTriggerKey == null) {
/* 2058 */           schedulerListener.schedulingDataCleared(); continue;
/*      */         } 
/* 2060 */         schedulerListener.jobUnscheduled(paramTriggerKey);
/* 2061 */       } catch (Exception exception) {
/* 2062 */         getLog().error("Error while notifying SchedulerListener of unscheduled job.  Triger=" + ((paramTriggerKey == null) ? "ALL DATA" : (String)paramTriggerKey), exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersFinalized(Trigger paramTrigger) {
/* 2071 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2074 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2076 */         schedulerListener.triggerFinalized(paramTrigger);
/* 2077 */       } catch (Exception exception) {
/* 2078 */         getLog().error("Error while notifying SchedulerListener of finalized trigger.  Triger=" + paramTrigger
/*      */             
/* 2080 */             .getKey(), exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersPausedTrigger(TriggerKey paramTriggerKey) {
/* 2087 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2090 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2092 */         schedulerListener.triggerPaused(paramTriggerKey);
/* 2093 */       } catch (Exception exception) {
/* 2094 */         getLog().error("Error while notifying SchedulerListener of paused trigger: " + paramTriggerKey, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersPausedTriggers(String paramString) {
/* 2103 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2106 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2108 */         schedulerListener.triggersPaused(paramString);
/* 2109 */       } catch (Exception exception) {
/* 2110 */         getLog().error("Error while notifying SchedulerListener of paused trigger group." + paramString, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersResumedTrigger(TriggerKey paramTriggerKey) {
/* 2119 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2122 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2124 */         schedulerListener.triggerResumed(paramTriggerKey);
/* 2125 */       } catch (Exception exception) {
/* 2126 */         getLog().error("Error while notifying SchedulerListener of resumed trigger: " + paramTriggerKey, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersResumedTriggers(String paramString) {
/* 2135 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2138 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2140 */         schedulerListener.triggersResumed(paramString);
/* 2141 */       } catch (Exception exception) {
/* 2142 */         getLog().error("Error while notifying SchedulerListener of resumed group: " + paramString, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersPausedJob(JobKey paramJobKey) {
/* 2151 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2154 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2156 */         schedulerListener.jobPaused(paramJobKey);
/* 2157 */       } catch (Exception exception) {
/* 2158 */         getLog().error("Error while notifying SchedulerListener of paused job: " + paramJobKey, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersPausedJobs(String paramString) {
/* 2167 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2170 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2172 */         schedulerListener.jobsPaused(paramString);
/* 2173 */       } catch (Exception exception) {
/* 2174 */         getLog().error("Error while notifying SchedulerListener of paused job group: " + paramString, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersResumedJob(JobKey paramJobKey) {
/* 2183 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2186 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2188 */         schedulerListener.jobResumed(paramJobKey);
/* 2189 */       } catch (Exception exception) {
/* 2190 */         getLog().error("Error while notifying SchedulerListener of resumed job: " + paramJobKey, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersResumedJobs(String paramString) {
/* 2199 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2202 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2204 */         schedulerListener.jobsResumed(paramString);
/* 2205 */       } catch (Exception exception) {
/* 2206 */         getLog().error("Error while notifying SchedulerListener of resumed job group: " + paramString, exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersInStandbyMode() {
/* 2215 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2218 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2220 */         schedulerListener.schedulerInStandbyMode();
/* 2221 */       } catch (Exception exception) {
/* 2222 */         getLog().error("Error while notifying SchedulerListener of inStandByMode.", exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersStarted() {
/* 2231 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2234 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2236 */         schedulerListener.schedulerStarted();
/* 2237 */       } catch (Exception exception) {
/* 2238 */         getLog().error("Error while notifying SchedulerListener of startup.", exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersStarting() {
/* 2247 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2250 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2252 */         schedulerListener.schedulerStarting();
/* 2253 */       } catch (Exception exception) {
/* 2254 */         getLog().error("Error while notifying SchedulerListener of startup.", exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersShutdown() {
/* 2263 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2266 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2268 */         schedulerListener.schedulerShutdown();
/* 2269 */       } catch (Exception exception) {
/* 2270 */         getLog().error("Error while notifying SchedulerListener of shutdown.", exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersShuttingdown() {
/* 2279 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2282 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2284 */         schedulerListener.schedulerShuttingdown();
/* 2285 */       } catch (Exception exception) {
/* 2286 */         getLog().error("Error while notifying SchedulerListener of shutdown.", exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersJobAdded(JobDetail paramJobDetail) {
/* 2295 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2298 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2300 */         schedulerListener.jobAdded(paramJobDetail);
/* 2301 */       } catch (Exception exception) {
/* 2302 */         getLog().error("Error while notifying SchedulerListener of JobAdded.", exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void notifySchedulerListenersJobDeleted(JobKey paramJobKey) {
/* 2311 */     List<SchedulerListener> list = buildSchedulerListenerList();
/*      */ 
/*      */     
/* 2314 */     for (SchedulerListener schedulerListener : list) {
/*      */       try {
/* 2316 */         schedulerListener.jobDeleted(paramJobKey);
/* 2317 */       } catch (Exception exception) {
/* 2318 */         getLog().error("Error while notifying SchedulerListener of JobAdded.", exception);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setJobFactory(JobFactory paramJobFactory) throws SchedulerException {
/* 2327 */     if (paramJobFactory == null) {
/* 2328 */       throw new IllegalArgumentException("JobFactory cannot be set to null!");
/*      */     }
/*      */     
/* 2331 */     getLog().info("JobFactory set to: " + paramJobFactory);
/*      */     
/* 2333 */     this.jobFactory = paramJobFactory;
/*      */   }
/*      */   
/*      */   public JobFactory getJobFactory() {
/* 2337 */     return this.jobFactory;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean interrupt(JobKey paramJobKey) throws UnableToInterruptJobException {
/* 2355 */     List<JobExecutionContext> list = getCurrentlyExecutingJobs();
/*      */     
/* 2357 */     JobDetail jobDetail = null;
/* 2358 */     Job job = null;
/*      */     
/* 2360 */     boolean bool = false;
/*      */     
/* 2362 */     for (JobExecutionContext jobExecutionContext : list) {
/* 2363 */       jobDetail = jobExecutionContext.getJobDetail();
/* 2364 */       if (paramJobKey.equals(jobDetail.getKey())) {
/* 2365 */         job = jobExecutionContext.getJobInstance();
/* 2366 */         if (job instanceof InterruptableJob) {
/* 2367 */           ((InterruptableJob)job).interrupt();
/* 2368 */           bool = true; continue;
/*      */         } 
/* 2370 */         throw new UnableToInterruptJobException("Job " + jobDetail
/* 2371 */             .getKey() + " can not be interrupted, since it does not implement " + InterruptableJob.class
/*      */             
/* 2373 */             .getName());
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 2378 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean interrupt(String paramString) throws UnableToInterruptJobException {
/* 2393 */     List<JobExecutionContext> list = getCurrentlyExecutingJobs();
/*      */     
/* 2395 */     Job job = null;
/*      */     
/* 2397 */     for (JobExecutionContext jobExecutionContext : list) {
/* 2398 */       if (jobExecutionContext.getFireInstanceId().equals(paramString)) {
/* 2399 */         job = jobExecutionContext.getJobInstance();
/* 2400 */         if (job instanceof InterruptableJob) {
/* 2401 */           ((InterruptableJob)job).interrupt();
/* 2402 */           return true;
/*      */         } 
/* 2404 */         throw new UnableToInterruptJobException("Job " + jobExecutionContext
/* 2405 */             .getJobDetail().getKey() + " can not be interrupted, since it does not implement " + InterruptableJob.class
/*      */             
/* 2407 */             .getName());
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 2412 */     return false;
/*      */   }
/*      */   
/*      */   private void shutdownPlugins() {
/* 2416 */     Iterator<SchedulerPlugin> iterator = this.resources.getSchedulerPlugins().iterator();
/* 2417 */     while (iterator.hasNext()) {
/* 2418 */       SchedulerPlugin schedulerPlugin = iterator.next();
/* 2419 */       schedulerPlugin.shutdown();
/*      */     } 
/*      */   }
/*      */   
/*      */   private void startPlugins() {
/* 2424 */     Iterator<SchedulerPlugin> iterator = this.resources.getSchedulerPlugins().iterator();
/* 2425 */     while (iterator.hasNext()) {
/* 2426 */       SchedulerPlugin schedulerPlugin = iterator.next();
/* 2427 */       schedulerPlugin.start();
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/quartz/core/QuartzScheduler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */