/*     */ package org.quartz.impl;
/*     */ 
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import org.quartz.Calendar;
/*     */ import org.quartz.JobDataMap;
/*     */ import org.quartz.JobDetail;
/*     */ import org.quartz.JobExecutionContext;
/*     */ import org.quartz.JobKey;
/*     */ import org.quartz.ListenerManager;
/*     */ import org.quartz.Scheduler;
/*     */ import org.quartz.SchedulerContext;
/*     */ import org.quartz.SchedulerException;
/*     */ import org.quartz.SchedulerMetaData;
/*     */ import org.quartz.Trigger;
/*     */ import org.quartz.TriggerKey;
/*     */ import org.quartz.UnableToInterruptJobException;
/*     */ import org.quartz.core.QuartzScheduler;
/*     */ import org.quartz.impl.matchers.GroupMatcher;
/*     */ import org.quartz.spi.JobFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class StdScheduler
/*     */   implements Scheduler
/*     */ {
/*     */   private QuartzScheduler sched;
/*     */   
/*     */   public StdScheduler(QuartzScheduler paramQuartzScheduler) {
/*  71 */     this.sched = paramQuartzScheduler;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSchedulerName() {
/*  88 */     return this.sched.getSchedulerName();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSchedulerInstanceId() {
/*  97 */     return this.sched.getSchedulerInstanceId();
/*     */   }
/*     */   
/*     */   public SchedulerMetaData getMetaData() {
/* 101 */     return new SchedulerMetaData(getSchedulerName(), 
/* 102 */         getSchedulerInstanceId(), getClass(), false, isStarted(), 
/* 103 */         isInStandbyMode(), isShutdown(), this.sched.runningSince(), this.sched
/* 104 */         .numJobsExecuted(), this.sched.getJobStoreClass(), this.sched
/* 105 */         .supportsPersistence(), this.sched.isClustered(), this.sched.getThreadPoolClass(), this.sched
/* 106 */         .getThreadPoolSize(), this.sched.getVersion());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SchedulerContext getContext() throws SchedulerException {
/* 116 */     return this.sched.getSchedulerContext();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void start() throws SchedulerException {
/* 131 */     this.sched.start();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void startDelayed(int paramInt) throws SchedulerException {
/* 140 */     this.sched.startDelayed(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void standby() {
/* 150 */     this.sched.standby();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isStarted() {
/* 168 */     return (this.sched.runningSince() != null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isInStandbyMode() {
/* 177 */     return this.sched.isInStandbyMode();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void shutdown() {
/* 186 */     this.sched.shutdown();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void shutdown(boolean paramBoolean) {
/* 195 */     this.sched.shutdown(paramBoolean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isShutdown() {
/* 204 */     return this.sched.isShutdown();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<JobExecutionContext> getCurrentlyExecutingJobs() {
/* 213 */     return this.sched.getCurrentlyExecutingJobs();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void clear() throws SchedulerException {
/* 228 */     this.sched.clear();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Date scheduleJob(JobDetail paramJobDetail, Trigger paramTrigger) throws SchedulerException {
/* 239 */     return this.sched.scheduleJob(paramJobDetail, paramTrigger);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Date scheduleJob(Trigger paramTrigger) throws SchedulerException {
/* 248 */     return this.sched.scheduleJob(paramTrigger);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addJob(JobDetail paramJobDetail, boolean paramBoolean) throws SchedulerException {
/* 258 */     this.sched.addJob(paramJobDetail, paramBoolean);
/*     */   }
/*     */ 
/*     */   
/*     */   public void addJob(JobDetail paramJobDetail, boolean paramBoolean1, boolean paramBoolean2) throws SchedulerException {
/* 263 */     this.sched.addJob(paramJobDetail, paramBoolean1, paramBoolean2);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean deleteJobs(List<JobKey> paramList) throws SchedulerException {
/* 268 */     return this.sched.deleteJobs(paramList);
/*     */   }
/*     */   
/*     */   public void scheduleJobs(Map<JobDetail, Set<? extends Trigger>> paramMap, boolean paramBoolean) throws SchedulerException {
/* 272 */     this.sched.scheduleJobs(paramMap, paramBoolean);
/*     */   }
/*     */   
/*     */   public void scheduleJob(JobDetail paramJobDetail, Set<? extends Trigger> paramSet, boolean paramBoolean) throws SchedulerException {
/* 276 */     this.sched.scheduleJob(paramJobDetail, paramSet, paramBoolean);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean unscheduleJobs(List<TriggerKey> paramList) throws SchedulerException {
/* 281 */     return this.sched.unscheduleJobs(paramList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteJob(JobKey paramJobKey) throws SchedulerException {
/* 291 */     return this.sched.deleteJob(paramJobKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean unscheduleJob(TriggerKey paramTriggerKey) throws SchedulerException {
/* 301 */     return this.sched.unscheduleJob(paramTriggerKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Date rescheduleJob(TriggerKey paramTriggerKey, Trigger paramTrigger) throws SchedulerException {
/* 311 */     return this.sched.rescheduleJob(paramTriggerKey, paramTrigger);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void triggerJob(JobKey paramJobKey) throws SchedulerException {
/* 321 */     triggerJob(paramJobKey, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void triggerJob(JobKey paramJobKey, JobDataMap paramJobDataMap) throws SchedulerException {
/* 331 */     this.sched.triggerJob(paramJobKey, paramJobDataMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void pauseTrigger(TriggerKey paramTriggerKey) throws SchedulerException {
/* 341 */     this.sched.pauseTrigger(paramTriggerKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void pauseTriggers(GroupMatcher<TriggerKey> paramGroupMatcher) throws SchedulerException {
/* 350 */     this.sched.pauseTriggers(paramGroupMatcher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void pauseJob(JobKey paramJobKey) throws SchedulerException {
/* 360 */     this.sched.pauseJob(paramJobKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Set<String> getPausedTriggerGroups() throws SchedulerException {
/* 367 */     return this.sched.getPausedTriggerGroups();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void pauseJobs(GroupMatcher<JobKey> paramGroupMatcher) throws SchedulerException {
/* 376 */     this.sched.pauseJobs(paramGroupMatcher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resumeTrigger(TriggerKey paramTriggerKey) throws SchedulerException {
/* 386 */     this.sched.resumeTrigger(paramTriggerKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resumeTriggers(GroupMatcher<TriggerKey> paramGroupMatcher) throws SchedulerException {
/* 395 */     this.sched.resumeTriggers(paramGroupMatcher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resumeJob(JobKey paramJobKey) throws SchedulerException {
/* 405 */     this.sched.resumeJob(paramJobKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resumeJobs(GroupMatcher<JobKey> paramGroupMatcher) throws SchedulerException {
/* 414 */     this.sched.resumeJobs(paramGroupMatcher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void pauseAll() throws SchedulerException {
/* 423 */     this.sched.pauseAll();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resumeAll() throws SchedulerException {
/* 432 */     this.sched.resumeAll();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getJobGroupNames() throws SchedulerException {
/* 441 */     return this.sched.getJobGroupNames();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<? extends Trigger> getTriggersOfJob(JobKey paramJobKey) throws SchedulerException {
/* 451 */     return this.sched.getTriggersOfJob(paramJobKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Set<JobKey> getJobKeys(GroupMatcher<JobKey> paramGroupMatcher) throws SchedulerException {
/* 460 */     return this.sched.getJobKeys(paramGroupMatcher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getTriggerGroupNames() throws SchedulerException {
/* 469 */     return this.sched.getTriggerGroupNames();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Set<TriggerKey> getTriggerKeys(GroupMatcher<TriggerKey> paramGroupMatcher) throws SchedulerException {
/* 478 */     return this.sched.getTriggerKeys(paramGroupMatcher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JobDetail getJobDetail(JobKey paramJobKey) throws SchedulerException {
/* 488 */     return this.sched.getJobDetail(paramJobKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Trigger getTrigger(TriggerKey paramTriggerKey) throws SchedulerException {
/* 498 */     return this.sched.getTrigger(paramTriggerKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Trigger.TriggerState getTriggerState(TriggerKey paramTriggerKey) throws SchedulerException {
/* 508 */     return this.sched.getTriggerState(paramTriggerKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCalendar(String paramString, Calendar paramCalendar, boolean paramBoolean1, boolean paramBoolean2) throws SchedulerException {
/* 518 */     this.sched.addCalendar(paramString, paramCalendar, paramBoolean1, paramBoolean2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteCalendar(String paramString) throws SchedulerException {
/* 527 */     return this.sched.deleteCalendar(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Calendar getCalendar(String paramString) throws SchedulerException {
/* 536 */     return this.sched.getCalendar(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getCalendarNames() throws SchedulerException {
/* 545 */     return this.sched.getCalendarNames();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkExists(JobKey paramJobKey) throws SchedulerException {
/* 554 */     return this.sched.checkExists(paramJobKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkExists(TriggerKey paramTriggerKey) throws SchedulerException {
/* 564 */     return this.sched.checkExists(paramTriggerKey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setJobFactory(JobFactory paramJobFactory) throws SchedulerException {
/* 579 */     this.sched.setJobFactory(paramJobFactory);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ListenerManager getListenerManager() throws SchedulerException {
/* 586 */     return this.sched.getListenerManager();
/*     */   }
/*     */   
/*     */   public boolean interrupt(JobKey paramJobKey) throws UnableToInterruptJobException {
/* 590 */     return this.sched.interrupt(paramJobKey);
/*     */   }
/*     */   
/*     */   public boolean interrupt(String paramString) throws UnableToInterruptJobException {
/* 594 */     return this.sched.interrupt(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Trigger getTrigger(String paramString1, String paramString2) throws SchedulerException {
/* 601 */     TriggerKey triggerKey = new TriggerKey(paramString1, paramString2);
/* 602 */     return getTrigger(triggerKey);
/*     */   }
/*     */ 
/*     */   
/*     */   public JobDetail getJobDetail(String paramString1, String paramString2) throws SchedulerException {
/* 607 */     JobKey jobKey = new JobKey(paramString1, paramString2);
/* 608 */     return getJobDetail(jobKey);
/*     */   }
/*     */ 
/*     */   
/*     */   public void resumeTrigger(String paramString1, String paramString2) throws SchedulerException {
/* 613 */     TriggerKey triggerKey = new TriggerKey(paramString1, paramString2);
/* 614 */     resumeTrigger(triggerKey);
/*     */   }
/*     */ 
/*     */   
/*     */   public void pauseTrigger(String paramString1, String paramString2) throws SchedulerException {
/* 619 */     TriggerKey triggerKey = new TriggerKey(paramString1, paramString2);
/* 620 */     pauseTrigger(triggerKey);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean unscheduleJob(String paramString1, String paramString2) throws SchedulerException {
/* 625 */     TriggerKey triggerKey = new TriggerKey(paramString1, paramString2);
/* 626 */     return unscheduleJob(triggerKey);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean deleteJob(String paramString1, String paramString2) throws SchedulerException {
/* 631 */     JobKey jobKey = new JobKey(paramString1, paramString2);
/* 632 */     return deleteJob(jobKey);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/quartz/impl/StdScheduler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */