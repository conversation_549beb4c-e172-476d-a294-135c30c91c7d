/*     */ package org.quartz.impl.jdbcjobstore.oracle;
/*     */ 
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.ObjectInputStream;
/*     */ import java.math.BigDecimal;
/*     */ import java.sql.Blob;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import org.quartz.Calendar;
/*     */ import org.quartz.JobDetail;
/*     */ import org.quartz.impl.jdbcjobstore.StdJDBCDelegate;
/*     */ import org.quartz.impl.jdbcjobstore.TriggerPersistenceDelegate;
/*     */ import org.quartz.spi.OperableTrigger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DMDelegate
/*     */   extends StdJDBCDelegate
/*     */ {
/*     */   public static final String INSERT_ORACLE_JOB_DETAIL = "INSERT INTO {0}JOB_DETAILS (SCHED_NAME, JOB_NAME, JOB_GROUP, DESCRIPTION, JOB_CLASS_NAME, IS_DURABLE, IS_NONCONCURRENT, IS_UPDATE_DATA, REQUESTS_RECOVERY, JOB_DATA)  VALUES({1}, ?, ?, ?, ?, ?, ?, ?, ?, EMPTY_BLOB())";
/*     */   public static final String UPDATE_ORACLE_JOB_DETAIL = "UPDATE {0}JOB_DETAILS SET DESCRIPTION = ?, JOB_CLASS_NAME = ?, IS_DURABLE = ?, IS_NONCONCURRENT = ?, IS_UPDATE_DATA = ?, REQUESTS_RECOVERY = ?, JOB_DATA = EMPTY_BLOB()  WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ?";
/*     */   public static final String UPDATE_ORACLE_JOB_DETAIL_BLOB = "UPDATE {0}JOB_DETAILS SET JOB_DATA = ?  WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ?";
/*     */   public static final String SELECT_ORACLE_JOB_DETAIL_BLOB = "SELECT JOB_DATA FROM {0}JOB_DETAILS WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ? FOR UPDATE";
/*     */   public static final String UPDATE_ORACLE_TRIGGER = "UPDATE {0}TRIGGERS SET JOB_NAME = ?, JOB_GROUP = ?, DESCRIPTION = ?, NEXT_FIRE_TIME = ?, PREV_FIRE_TIME = ?, TRIGGER_STATE = ?, TRIGGER_TYPE = ?, START_TIME = ?, END_TIME = ?, CALENDAR_NAME = ?, MISFIRE_INSTR = ?, PRIORITY = ? WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?";
/*     */   public static final String SELECT_ORACLE_TRIGGER_JOB_DETAIL_BLOB = "SELECT JOB_DATA FROM {0}TRIGGERS WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ? FOR UPDATE";
/*     */   public static final String UPDATE_ORACLE_TRIGGER_JOB_DETAIL_BLOB = "UPDATE {0}TRIGGERS SET JOB_DATA = ?  WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?";
/*     */   public static final String UPDATE_ORACLE_TRIGGER_JOB_DETAIL_EMPTY_BLOB = "UPDATE {0}TRIGGERS SET JOB_DATA = EMPTY_BLOB()  WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?";
/*     */   public static final String INSERT_ORACLE_CALENDAR = "INSERT INTO {0}CALENDARS (SCHED_NAME, CALENDAR_NAME, CALENDAR)  VALUES({1}, ?, EMPTY_BLOB())";
/*     */   public static final String SELECT_ORACLE_CALENDAR_BLOB = "SELECT CALENDAR FROM {0}CALENDARS WHERE SCHED_NAME = {1} AND CALENDAR_NAME = ? FOR UPDATE";
/*     */   public static final String UPDATE_ORACLE_CALENDAR_BLOB = "UPDATE {0}CALENDARS SET CALENDAR = ?  WHERE SCHED_NAME = {1} AND CALENDAR_NAME = ?";
/*     */   
/*     */   protected Object getObjectFromBlob(ResultSet paramResultSet, String paramString) throws ClassNotFoundException, IOException, SQLException {
/* 132 */     Object object = null;
/* 133 */     InputStream inputStream = paramResultSet.getBinaryStream(paramString);
/* 134 */     if (inputStream != null) {
/* 135 */       ObjectInputStream objectInputStream = new ObjectInputStream(inputStream);
/*     */       try {
/* 137 */         object = objectInputStream.readObject();
/*     */       } finally {
/* 139 */         objectInputStream.close();
/*     */       } 
/*     */     } 
/*     */     
/* 143 */     return object;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int insertJobDetail(Connection paramConnection, JobDetail paramJobDetail) throws IOException, SQLException {
/* 150 */     ByteArrayOutputStream byteArrayOutputStream = serializeJobData(paramJobDetail.getJobDataMap());
/* 151 */     byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
/* 152 */     PreparedStatement preparedStatement = null;
/* 153 */     ResultSet resultSet = null;
/*     */     
/*     */     try {
/* 156 */       preparedStatement = paramConnection.prepareStatement(rtp("INSERT INTO {0}JOB_DETAILS (SCHED_NAME, JOB_NAME, JOB_GROUP, DESCRIPTION, JOB_CLASS_NAME, IS_DURABLE, IS_NONCONCURRENT, IS_UPDATE_DATA, REQUESTS_RECOVERY, JOB_DATA)  VALUES({1}, ?, ?, ?, ?, ?, ?, ?, ?, EMPTY_BLOB())"));
/* 157 */       preparedStatement.setString(1, paramJobDetail.getKey().getName());
/* 158 */       preparedStatement.setString(2, paramJobDetail.getKey().getGroup());
/* 159 */       preparedStatement.setString(3, paramJobDetail.getDescription());
/* 160 */       preparedStatement.setString(4, paramJobDetail.getJobClass().getName());
/* 161 */       setBoolean(preparedStatement, 5, paramJobDetail.isDurable());
/* 162 */       setBoolean(preparedStatement, 6, paramJobDetail.isConcurrentExectionDisallowed());
/* 163 */       setBoolean(preparedStatement, 7, paramJobDetail.isPersistJobDataAfterExecution());
/* 164 */       setBoolean(preparedStatement, 8, paramJobDetail.requestsRecovery());
/*     */       
/* 166 */       preparedStatement.executeUpdate();
/* 167 */       preparedStatement.close();
/*     */       
/* 169 */       preparedStatement = paramConnection.prepareStatement(rtp("SELECT JOB_DATA FROM {0}JOB_DETAILS WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ? FOR UPDATE"));
/* 170 */       preparedStatement.setString(1, paramJobDetail.getKey().getName());
/* 171 */       preparedStatement.setString(2, paramJobDetail.getKey().getGroup());
/*     */       
/* 173 */       resultSet = preparedStatement.executeQuery();
/*     */       
/* 175 */       int i = 0;
/*     */       
/* 177 */       Blob blob = null;
/* 178 */       if (resultSet.next()) {
/* 179 */         blob = writeDataToBlob(resultSet, 1, arrayOfByte);
/*     */       } else {
/* 181 */         return i;
/*     */       } 
/*     */       
/* 184 */       resultSet.close();
/* 185 */       preparedStatement.close();
/*     */       
/* 187 */       preparedStatement = paramConnection.prepareStatement(rtp("UPDATE {0}JOB_DETAILS SET JOB_DATA = ?  WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ?"));
/* 188 */       preparedStatement.setBlob(1, blob);
/* 189 */       preparedStatement.setString(2, paramJobDetail.getKey().getName());
/* 190 */       preparedStatement.setString(3, paramJobDetail.getKey().getGroup());
/*     */       
/* 192 */       i = preparedStatement.executeUpdate();
/*     */       
/* 194 */       return i;
/*     */     } finally {
/* 196 */       closeResultSet(resultSet);
/* 197 */       closeStatement(preparedStatement);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Object getJobDataFromBlob(ResultSet paramResultSet, String paramString) throws ClassNotFoundException, IOException, SQLException {
/* 206 */     if (canUseProperties()) {
/* 207 */       return paramResultSet.getBinaryStream(paramString);
/*     */     }
/*     */ 
/*     */     
/* 211 */     return getObjectFromBlob(paramResultSet, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int updateJobDetail(Connection paramConnection, JobDetail paramJobDetail) throws IOException, SQLException {
/* 218 */     ByteArrayOutputStream byteArrayOutputStream = serializeJobData(paramJobDetail.getJobDataMap());
/* 219 */     byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */     
/* 221 */     PreparedStatement preparedStatement1 = null;
/* 222 */     PreparedStatement preparedStatement2 = null;
/* 223 */     ResultSet resultSet = null;
/*     */     
/*     */     try {
/* 226 */       preparedStatement1 = paramConnection.prepareStatement(rtp("UPDATE {0}JOB_DETAILS SET DESCRIPTION = ?, JOB_CLASS_NAME = ?, IS_DURABLE = ?, IS_NONCONCURRENT = ?, IS_UPDATE_DATA = ?, REQUESTS_RECOVERY = ?, JOB_DATA = EMPTY_BLOB()  WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ?"));
/* 227 */       preparedStatement1.setString(1, paramJobDetail.getDescription());
/* 228 */       preparedStatement1.setString(2, paramJobDetail.getJobClass().getName());
/* 229 */       setBoolean(preparedStatement1, 3, paramJobDetail.isDurable());
/* 230 */       setBoolean(preparedStatement1, 4, paramJobDetail.isConcurrentExectionDisallowed());
/* 231 */       setBoolean(preparedStatement1, 5, paramJobDetail.isPersistJobDataAfterExecution());
/* 232 */       setBoolean(preparedStatement1, 6, paramJobDetail.requestsRecovery());
/* 233 */       preparedStatement1.setString(7, paramJobDetail.getKey().getName());
/* 234 */       preparedStatement1.setString(8, paramJobDetail.getKey().getGroup());
/*     */       
/* 236 */       preparedStatement1.executeUpdate();
/* 237 */       preparedStatement1.close();
/*     */       
/* 239 */       preparedStatement1 = paramConnection.prepareStatement(rtp("SELECT JOB_DATA FROM {0}JOB_DETAILS WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ? FOR UPDATE"));
/* 240 */       preparedStatement1.setString(1, paramJobDetail.getKey().getName());
/* 241 */       preparedStatement1.setString(2, paramJobDetail.getKey().getGroup());
/*     */       
/* 243 */       resultSet = preparedStatement1.executeQuery();
/*     */       
/* 245 */       int i = 0;
/*     */       
/* 247 */       if (resultSet.next()) {
/* 248 */         Blob blob = writeDataToBlob(resultSet, 1, arrayOfByte);
/* 249 */         preparedStatement2 = paramConnection.prepareStatement(rtp("UPDATE {0}JOB_DETAILS SET JOB_DATA = ?  WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ?"));
/*     */         
/* 251 */         preparedStatement2.setBlob(1, blob);
/* 252 */         preparedStatement2.setString(2, paramJobDetail.getKey().getName());
/* 253 */         preparedStatement2.setString(3, paramJobDetail.getKey().getGroup());
/*     */         
/* 255 */         i = preparedStatement2.executeUpdate();
/*     */       } 
/*     */       
/* 258 */       return i;
/*     */     } finally {
/*     */       
/* 261 */       closeResultSet(resultSet);
/* 262 */       closeStatement(preparedStatement1);
/* 263 */       closeStatement(preparedStatement2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int insertTrigger(Connection paramConnection, OperableTrigger paramOperableTrigger, String paramString, JobDetail paramJobDetail) throws SQLException, IOException {
/* 271 */     byte[] arrayOfByte = null;
/* 272 */     if (paramOperableTrigger.getJobDataMap().size() > 0) {
/* 273 */       arrayOfByte = serializeJobData(paramOperableTrigger.getJobDataMap()).toByteArray();
/*     */     }
/*     */     
/* 276 */     PreparedStatement preparedStatement = null;
/* 277 */     ResultSet resultSet = null;
/*     */     
/* 279 */     int i = 0;
/*     */     
/*     */     try {
/* 282 */       preparedStatement = paramConnection.prepareStatement(rtp("INSERT INTO {0}TRIGGERS (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, JOB_NAME, JOB_GROUP, DESCRIPTION, NEXT_FIRE_TIME, PREV_FIRE_TIME, TRIGGER_STATE, TRIGGER_TYPE, START_TIME, END_TIME, CALENDAR_NAME, MISFIRE_INSTR, JOB_DATA, PRIORITY)  VALUES({1}, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"));
/* 283 */       preparedStatement.setString(1, paramOperableTrigger.getKey().getName());
/* 284 */       preparedStatement.setString(2, paramOperableTrigger.getKey().getGroup());
/* 285 */       preparedStatement.setString(3, paramOperableTrigger.getJobKey().getName());
/* 286 */       preparedStatement.setString(4, paramOperableTrigger.getJobKey().getGroup());
/* 287 */       preparedStatement.setString(5, paramOperableTrigger.getDescription());
/* 288 */       preparedStatement.setBigDecimal(6, new BigDecimal(String.valueOf(paramOperableTrigger
/* 289 */               .getNextFireTime().getTime())));
/* 290 */       long l1 = -1L;
/* 291 */       if (paramOperableTrigger.getPreviousFireTime() != null) {
/* 292 */         l1 = paramOperableTrigger.getPreviousFireTime().getTime();
/*     */       }
/* 294 */       preparedStatement.setBigDecimal(7, new BigDecimal(String.valueOf(l1)));
/* 295 */       preparedStatement.setString(8, paramString);
/*     */       
/* 297 */       TriggerPersistenceDelegate triggerPersistenceDelegate = findTriggerPersistenceDelegate(paramOperableTrigger);
/*     */       
/* 299 */       String str = "BLOB";
/* 300 */       if (triggerPersistenceDelegate != null)
/* 301 */         str = triggerPersistenceDelegate.getHandledTriggerTypeDiscriminator(); 
/* 302 */       preparedStatement.setString(9, str);
/*     */       
/* 304 */       preparedStatement.setBigDecimal(10, new BigDecimal(String.valueOf(paramOperableTrigger
/* 305 */               .getStartTime().getTime())));
/* 306 */       long l2 = 0L;
/* 307 */       if (paramOperableTrigger.getEndTime() != null) {
/* 308 */         l2 = paramOperableTrigger.getEndTime().getTime();
/*     */       }
/* 310 */       preparedStatement.setBigDecimal(11, new BigDecimal(String.valueOf(l2)));
/* 311 */       preparedStatement.setString(12, paramOperableTrigger.getCalendarName());
/* 312 */       preparedStatement.setInt(13, paramOperableTrigger.getMisfireInstruction());
/* 313 */       preparedStatement.setBinaryStream(14, (InputStream)null, 0);
/* 314 */       preparedStatement.setInt(15, paramOperableTrigger.getPriority());
/*     */       
/* 316 */       i = preparedStatement.executeUpdate();
/*     */       
/* 318 */       if (arrayOfByte != null) {
/* 319 */         preparedStatement.close();
/*     */ 
/*     */         
/* 322 */         preparedStatement = paramConnection.prepareStatement(rtp("UPDATE {0}TRIGGERS SET JOB_DATA = EMPTY_BLOB()  WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?"));
/* 323 */         preparedStatement.setString(1, paramOperableTrigger.getKey().getName());
/* 324 */         preparedStatement.setString(2, paramOperableTrigger.getKey().getGroup());
/* 325 */         preparedStatement.executeUpdate();
/* 326 */         preparedStatement.close();
/*     */         
/* 328 */         preparedStatement = paramConnection.prepareStatement(rtp("SELECT JOB_DATA FROM {0}TRIGGERS WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ? FOR UPDATE"));
/* 329 */         preparedStatement.setString(1, paramOperableTrigger.getKey().getName());
/* 330 */         preparedStatement.setString(2, paramOperableTrigger.getKey().getGroup());
/*     */         
/* 332 */         resultSet = preparedStatement.executeQuery();
/*     */         
/* 334 */         Blob blob = null;
/* 335 */         if (resultSet.next()) {
/* 336 */           blob = writeDataToBlob(resultSet, 1, arrayOfByte);
/*     */         } else {
/* 338 */           return 0;
/*     */         } 
/*     */         
/* 341 */         resultSet.close();
/* 342 */         preparedStatement.close();
/*     */         
/* 344 */         preparedStatement = paramConnection.prepareStatement(rtp("UPDATE {0}TRIGGERS SET JOB_DATA = ?  WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?"));
/* 345 */         preparedStatement.setBlob(1, blob);
/* 346 */         preparedStatement.setString(2, paramOperableTrigger.getKey().getName());
/* 347 */         preparedStatement.setString(3, paramOperableTrigger.getKey().getGroup());
/*     */         
/* 349 */         preparedStatement.executeUpdate();
/*     */       } 
/*     */       
/* 352 */       if (triggerPersistenceDelegate == null) {
/* 353 */         insertBlobTrigger(paramConnection, paramOperableTrigger);
/*     */       } else {
/* 355 */         triggerPersistenceDelegate.insertExtendedTriggerProperties(paramConnection, paramOperableTrigger, paramString, paramJobDetail);
/*     */       } 
/*     */     } finally {
/* 358 */       closeResultSet(resultSet);
/* 359 */       closeStatement(preparedStatement);
/*     */     } 
/*     */     
/* 362 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int updateTrigger(Connection paramConnection, OperableTrigger paramOperableTrigger, String paramString, JobDetail paramJobDetail) throws SQLException, IOException {
/* 370 */     boolean bool = paramOperableTrigger.getJobDataMap().isDirty();
/* 371 */     byte[] arrayOfByte = null;
/* 372 */     if (bool && paramOperableTrigger.getJobDataMap().size() > 0) {
/* 373 */       arrayOfByte = serializeJobData(paramOperableTrigger.getJobDataMap()).toByteArray();
/*     */     }
/*     */     
/* 376 */     PreparedStatement preparedStatement1 = null;
/* 377 */     PreparedStatement preparedStatement2 = null;
/* 378 */     ResultSet resultSet = null;
/*     */     
/* 380 */     int i = 0;
/*     */ 
/*     */     
/*     */     try {
/* 384 */       preparedStatement1 = paramConnection.prepareStatement(rtp("UPDATE {0}TRIGGERS SET JOB_NAME = ?, JOB_GROUP = ?, DESCRIPTION = ?, NEXT_FIRE_TIME = ?, PREV_FIRE_TIME = ?, TRIGGER_STATE = ?, TRIGGER_TYPE = ?, START_TIME = ?, END_TIME = ?, CALENDAR_NAME = ?, MISFIRE_INSTR = ?, PRIORITY = ? WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?"));
/*     */       
/* 386 */       preparedStatement1.setString(1, paramOperableTrigger.getJobKey().getName());
/* 387 */       preparedStatement1.setString(2, paramOperableTrigger.getJobKey().getGroup());
/* 388 */       preparedStatement1.setString(3, paramOperableTrigger.getDescription());
/* 389 */       long l1 = -1L;
/* 390 */       if (paramOperableTrigger.getNextFireTime() != null) {
/* 391 */         l1 = paramOperableTrigger.getNextFireTime().getTime();
/*     */       }
/* 393 */       preparedStatement1.setBigDecimal(4, new BigDecimal(String.valueOf(l1)));
/* 394 */       long l2 = -1L;
/* 395 */       if (paramOperableTrigger.getPreviousFireTime() != null) {
/* 396 */         l2 = paramOperableTrigger.getPreviousFireTime().getTime();
/*     */       }
/* 398 */       preparedStatement1.setBigDecimal(5, new BigDecimal(String.valueOf(l2)));
/* 399 */       preparedStatement1.setString(6, paramString);
/*     */       
/* 401 */       TriggerPersistenceDelegate triggerPersistenceDelegate = findTriggerPersistenceDelegate(paramOperableTrigger);
/*     */       
/* 403 */       String str = "BLOB";
/* 404 */       if (triggerPersistenceDelegate != null) {
/* 405 */         str = triggerPersistenceDelegate.getHandledTriggerTypeDiscriminator();
/*     */       }
/* 407 */       preparedStatement1.setString(7, str);
/*     */       
/* 409 */       preparedStatement1.setBigDecimal(8, new BigDecimal(String.valueOf(paramOperableTrigger
/* 410 */               .getStartTime().getTime())));
/* 411 */       long l3 = 0L;
/* 412 */       if (paramOperableTrigger.getEndTime() != null) {
/* 413 */         l3 = paramOperableTrigger.getEndTime().getTime();
/*     */       }
/* 415 */       preparedStatement1.setBigDecimal(9, new BigDecimal(String.valueOf(l3)));
/* 416 */       preparedStatement1.setString(10, paramOperableTrigger.getCalendarName());
/* 417 */       preparedStatement1.setInt(11, paramOperableTrigger.getMisfireInstruction());
/* 418 */       preparedStatement1.setInt(12, paramOperableTrigger.getPriority());
/* 419 */       preparedStatement1.setString(13, paramOperableTrigger.getKey().getName());
/* 420 */       preparedStatement1.setString(14, paramOperableTrigger.getKey().getGroup());
/*     */       
/* 422 */       i = preparedStatement1.executeUpdate();
/*     */       
/* 424 */       if (bool) {
/* 425 */         preparedStatement1.close();
/*     */ 
/*     */         
/* 428 */         preparedStatement1 = paramConnection.prepareStatement(rtp("UPDATE {0}TRIGGERS SET JOB_DATA = EMPTY_BLOB()  WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?"));
/* 429 */         preparedStatement1.setString(1, paramOperableTrigger.getKey().getName());
/* 430 */         preparedStatement1.setString(2, paramOperableTrigger.getKey().getGroup());
/* 431 */         preparedStatement1.executeUpdate();
/* 432 */         preparedStatement1.close();
/*     */         
/* 434 */         preparedStatement1 = paramConnection.prepareStatement(rtp("SELECT JOB_DATA FROM {0}TRIGGERS WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ? FOR UPDATE"));
/* 435 */         preparedStatement1.setString(1, paramOperableTrigger.getKey().getName());
/* 436 */         preparedStatement1.setString(2, paramOperableTrigger.getKey().getGroup());
/*     */         
/* 438 */         resultSet = preparedStatement1.executeQuery();
/*     */         
/* 440 */         if (resultSet.next()) {
/* 441 */           Blob blob = writeDataToBlob(resultSet, 1, arrayOfByte);
/* 442 */           preparedStatement2 = paramConnection.prepareStatement(rtp("UPDATE {0}TRIGGERS SET JOB_DATA = ?  WHERE SCHED_NAME = {1} AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?"));
/*     */           
/* 444 */           preparedStatement2.setBlob(1, blob);
/* 445 */           preparedStatement2.setString(2, paramOperableTrigger.getKey().getName());
/* 446 */           preparedStatement2.setString(3, paramOperableTrigger.getKey().getGroup());
/*     */           
/* 448 */           preparedStatement2.executeUpdate();
/*     */         } 
/*     */       } 
/*     */       
/* 452 */       if (triggerPersistenceDelegate == null) {
/* 453 */         updateBlobTrigger(paramConnection, paramOperableTrigger);
/*     */       } else {
/* 455 */         triggerPersistenceDelegate.updateExtendedTriggerProperties(paramConnection, paramOperableTrigger, paramString, paramJobDetail);
/*     */       } 
/*     */     } finally {
/* 458 */       closeResultSet(resultSet);
/* 459 */       closeStatement(preparedStatement1);
/* 460 */       closeStatement(preparedStatement2);
/*     */     } 
/*     */     
/* 463 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public int insertCalendar(Connection paramConnection, String paramString, Calendar paramCalendar) throws IOException, SQLException {
/* 469 */     ByteArrayOutputStream byteArrayOutputStream = serializeObject(paramCalendar);
/*     */     
/* 471 */     PreparedStatement preparedStatement1 = null;
/* 472 */     PreparedStatement preparedStatement2 = null;
/* 473 */     ResultSet resultSet = null;
/*     */     
/*     */     try {
/* 476 */       preparedStatement1 = paramConnection.prepareStatement(rtp("INSERT INTO {0}CALENDARS (SCHED_NAME, CALENDAR_NAME, CALENDAR)  VALUES({1}, ?, EMPTY_BLOB())"));
/* 477 */       preparedStatement1.setString(1, paramString);
/*     */       
/* 479 */       preparedStatement1.executeUpdate();
/* 480 */       preparedStatement1.close();
/*     */       
/* 482 */       preparedStatement1 = paramConnection.prepareStatement(rtp("SELECT CALENDAR FROM {0}CALENDARS WHERE SCHED_NAME = {1} AND CALENDAR_NAME = ? FOR UPDATE"));
/* 483 */       preparedStatement1.setString(1, paramString);
/*     */       
/* 485 */       resultSet = preparedStatement1.executeQuery();
/*     */       
/* 487 */       if (resultSet.next()) {
/* 488 */         Blob blob = writeDataToBlob(resultSet, 1, byteArrayOutputStream.toByteArray());
/* 489 */         preparedStatement2 = paramConnection.prepareStatement(rtp("UPDATE {0}CALENDARS SET CALENDAR = ?  WHERE SCHED_NAME = {1} AND CALENDAR_NAME = ?"));
/*     */         
/* 491 */         preparedStatement2.setBlob(1, blob);
/* 492 */         preparedStatement2.setString(2, paramString);
/*     */         
/* 494 */         return preparedStatement2.executeUpdate();
/*     */       } 
/*     */       
/* 497 */       return 0;
/*     */     } finally {
/*     */       
/* 500 */       closeResultSet(resultSet);
/* 501 */       closeStatement(preparedStatement1);
/* 502 */       closeStatement(preparedStatement2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public int updateCalendar(Connection paramConnection, String paramString, Calendar paramCalendar) throws IOException, SQLException {
/* 509 */     ByteArrayOutputStream byteArrayOutputStream = serializeObject(paramCalendar);
/*     */     
/* 511 */     PreparedStatement preparedStatement1 = null;
/* 512 */     PreparedStatement preparedStatement2 = null;
/* 513 */     ResultSet resultSet = null;
/*     */     
/*     */     try {
/* 516 */       preparedStatement1 = paramConnection.prepareStatement(rtp("SELECT CALENDAR FROM {0}CALENDARS WHERE SCHED_NAME = {1} AND CALENDAR_NAME = ? FOR UPDATE"));
/* 517 */       preparedStatement1.setString(1, paramString);
/*     */       
/* 519 */       resultSet = preparedStatement1.executeQuery();
/*     */       
/* 521 */       if (resultSet.next()) {
/* 522 */         Blob blob = writeDataToBlob(resultSet, 1, byteArrayOutputStream.toByteArray());
/* 523 */         preparedStatement2 = paramConnection.prepareStatement(rtp("UPDATE {0}CALENDARS SET CALENDAR = ?  WHERE SCHED_NAME = {1} AND CALENDAR_NAME = ?"));
/*     */         
/* 525 */         preparedStatement2.setBlob(1, blob);
/* 526 */         preparedStatement2.setString(2, paramString);
/*     */         
/* 528 */         return preparedStatement2.executeUpdate();
/*     */       } 
/*     */       
/* 531 */       return 0;
/*     */     } finally {
/*     */       
/* 534 */       closeResultSet(resultSet);
/* 535 */       closeStatement(preparedStatement1);
/* 536 */       closeStatement(preparedStatement2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int updateJobData(Connection paramConnection, JobDetail paramJobDetail) throws IOException, SQLException {
/* 544 */     ByteArrayOutputStream byteArrayOutputStream = serializeJobData(paramJobDetail.getJobDataMap());
/* 545 */     byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */     
/* 547 */     PreparedStatement preparedStatement1 = null;
/* 548 */     PreparedStatement preparedStatement2 = null;
/* 549 */     ResultSet resultSet = null;
/*     */     
/*     */     try {
/* 552 */       preparedStatement1 = paramConnection.prepareStatement(rtp("SELECT JOB_DATA FROM {0}JOB_DETAILS WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ? FOR UPDATE"));
/* 553 */       preparedStatement1.setString(1, paramJobDetail.getKey().getName());
/* 554 */       preparedStatement1.setString(2, paramJobDetail.getKey().getGroup());
/*     */       
/* 556 */       resultSet = preparedStatement1.executeQuery();
/*     */       
/* 558 */       int i = 0;
/*     */       
/* 560 */       if (resultSet.next()) {
/* 561 */         Blob blob = writeDataToBlob(resultSet, 1, arrayOfByte);
/* 562 */         preparedStatement2 = paramConnection.prepareStatement(rtp("UPDATE {0}JOB_DETAILS SET JOB_DATA = ?  WHERE SCHED_NAME = {1} AND JOB_NAME = ? AND JOB_GROUP = ?"));
/*     */         
/* 564 */         preparedStatement2.setBlob(1, blob);
/* 565 */         preparedStatement2.setString(2, paramJobDetail.getKey().getName());
/* 566 */         preparedStatement2.setString(3, paramJobDetail.getKey().getGroup());
/*     */         
/* 568 */         i = preparedStatement2.executeUpdate();
/*     */       } 
/*     */       
/* 571 */       return i;
/*     */     } finally {
/* 573 */       closeResultSet(resultSet);
/* 574 */       closeStatement(preparedStatement1);
/* 575 */       closeStatement(preparedStatement2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected Blob writeDataToBlob(ResultSet paramResultSet, int paramInt, byte[] paramArrayOfbyte) throws SQLException {
/* 582 */     Blob blob = paramResultSet.getBlob(paramInt);
/*     */     
/* 584 */     if (blob == null) {
/* 585 */       throw new SQLException("Driver's Blob representation is null!");
/*     */     }
/*     */     
/* 588 */     if (blob instanceof Blob)
/*     */     {
/*     */       
/* 591 */       return blob;
/*     */     }
/* 593 */     throw new SQLException("Driver's Blob representation is of an unsupported type: " + blob
/*     */         
/* 595 */         .getClass().getName());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/quartz/impl/jdbcjobstore/oracle/DMDelegate.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */