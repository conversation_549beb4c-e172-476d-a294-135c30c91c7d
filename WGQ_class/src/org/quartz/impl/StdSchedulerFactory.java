/*      */ package org.quartz.impl;
/*      */ 
/*      */ import java.beans.BeanInfo;
/*      */ import java.beans.IntrospectionException;
/*      */ import java.beans.Introspector;
/*      */ import java.beans.PropertyDescriptor;
/*      */ import java.io.BufferedInputStream;
/*      */ import java.io.ByteArrayInputStream;
/*      */ import java.io.File;
/*      */ import java.io.FileInputStream;
/*      */ import java.io.IOException;
/*      */ import java.io.InputStream;
/*      */ import java.lang.reflect.InvocationTargetException;
/*      */ import java.lang.reflect.Method;
/*      */ import java.security.AccessControlException;
/*      */ import java.util.Collection;
/*      */ import java.util.Enumeration;
/*      */ import java.util.Locale;
/*      */ import java.util.Properties;
/*      */ import org.quartz.JobListener;
/*      */ import org.quartz.Matcher;
/*      */ import org.quartz.Scheduler;
/*      */ import org.quartz.SchedulerConfigException;
/*      */ import org.quartz.SchedulerException;
/*      */ import org.quartz.SchedulerFactory;
/*      */ import org.quartz.TriggerListener;
/*      */ import org.quartz.core.JobRunShellFactory;
/*      */ import org.quartz.core.QuartzScheduler;
/*      */ import org.quartz.core.QuartzSchedulerResources;
/*      */ import org.quartz.ee.jta.JTAAnnotationAwareJobRunShellFactory;
/*      */ import org.quartz.ee.jta.JTAJobRunShellFactory;
/*      */ import org.quartz.ee.jta.UserTransactionHelper;
/*      */ import org.quartz.impl.jdbcjobstore.JobStoreSupport;
/*      */ import org.quartz.impl.jdbcjobstore.Semaphore;
/*      */ import org.quartz.impl.matchers.EverythingMatcher;
/*      */ import org.quartz.management.ManagementRESTServiceConfiguration;
/*      */ import org.quartz.simpl.RAMJobStore;
/*      */ import org.quartz.simpl.SimpleThreadPool;
/*      */ import org.quartz.spi.ClassLoadHelper;
/*      */ import org.quartz.spi.InstanceIdGenerator;
/*      */ import org.quartz.spi.JobFactory;
/*      */ import org.quartz.spi.JobStore;
/*      */ import org.quartz.spi.SchedulerPlugin;
/*      */ import org.quartz.spi.ThreadExecutor;
/*      */ import org.quartz.spi.ThreadPool;
/*      */ import org.quartz.utils.ConnectionProvider;
/*      */ import org.quartz.utils.DBConnectionManager;
/*      */ import org.quartz.utils.JNDIConnectionProvider;
/*      */ import org.quartz.utils.PoolingConnectionProvider;
/*      */ import org.quartz.utils.PropertiesParser;
/*      */ import org.slf4j.Logger;
/*      */ import org.slf4j.LoggerFactory;
/*      */ import weaver.integration.logging.Logger;
/*      */ import weaver.integration.logging.LoggerFactory;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class StdSchedulerFactory
/*      */   implements SchedulerFactory
/*      */ {
/*      */   public static final String PROPERTIES_FILE = "org.quartz.properties";
/*      */   public static final String PROP_SCHED_INSTANCE_NAME = "org.quartz.scheduler.instanceName";
/*      */   public static final String PROP_SCHED_INSTANCE_ID = "org.quartz.scheduler.instanceId";
/*      */   public static final String PROP_SCHED_INSTANCE_ID_GENERATOR_PREFIX = "org.quartz.scheduler.instanceIdGenerator";
/*      */   public static final String PROP_SCHED_INSTANCE_ID_GENERATOR_CLASS = "org.quartz.scheduler.instanceIdGenerator.class";
/*      */   public static final String PROP_SCHED_THREAD_NAME = "org.quartz.scheduler.threadName";
/*      */   public static final String PROP_SCHED_SKIP_UPDATE_CHECK = "org.quartz.scheduler.skipUpdateCheck";
/*      */   public static final String PROP_SCHED_BATCH_TIME_WINDOW = "org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow";
/*      */   public static final String PROP_SCHED_MAX_BATCH_SIZE = "org.quartz.scheduler.batchTriggerAcquisitionMaxCount";
/*      */   public static final String PROP_SCHED_JMX_EXPORT = "org.quartz.scheduler.jmx.export";
/*      */   public static final String PROP_SCHED_JMX_OBJECT_NAME = "org.quartz.scheduler.jmx.objectName";
/*      */   public static final String PROP_SCHED_JMX_PROXY = "org.quartz.scheduler.jmx.proxy";
/*      */   public static final String PROP_SCHED_JMX_PROXY_CLASS = "org.quartz.scheduler.jmx.proxy.class";
/*      */   public static final String PROP_SCHED_RMI_EXPORT = "org.quartz.scheduler.rmi.export";
/*      */   public static final String PROP_SCHED_RMI_PROXY = "org.quartz.scheduler.rmi.proxy";
/*      */   public static final String PROP_SCHED_RMI_HOST = "org.quartz.scheduler.rmi.registryHost";
/*      */   public static final String PROP_SCHED_RMI_PORT = "org.quartz.scheduler.rmi.registryPort";
/*      */   public static final String PROP_SCHED_RMI_SERVER_PORT = "org.quartz.scheduler.rmi.serverPort";
/*      */   public static final String PROP_SCHED_RMI_CREATE_REGISTRY = "org.quartz.scheduler.rmi.createRegistry";
/*      */   public static final String PROP_SCHED_RMI_BIND_NAME = "org.quartz.scheduler.rmi.bindName";
/*      */   public static final String PROP_SCHED_WRAP_JOB_IN_USER_TX = "org.quartz.scheduler.wrapJobExecutionInUserTransaction";
/*      */   public static final String PROP_SCHED_USER_TX_URL = "org.quartz.scheduler.userTransactionURL";
/*      */   public static final String PROP_SCHED_IDLE_WAIT_TIME = "org.quartz.scheduler.idleWaitTime";
/*      */   public static final String PROP_SCHED_DB_FAILURE_RETRY_INTERVAL = "org.quartz.scheduler.dbFailureRetryInterval";
/*      */   public static final String PROP_SCHED_MAKE_SCHEDULER_THREAD_DAEMON = "org.quartz.scheduler.makeSchedulerThreadDaemon";
/*      */   public static final String PROP_SCHED_SCHEDULER_THREADS_INHERIT_CONTEXT_CLASS_LOADER_OF_INITIALIZING_THREAD = "org.quartz.scheduler.threadsInheritContextClassLoaderOfInitializer";
/*      */   public static final String PROP_SCHED_CLASS_LOAD_HELPER_CLASS = "org.quartz.scheduler.classLoadHelper.class";
/*      */   public static final String PROP_SCHED_JOB_FACTORY_CLASS = "org.quartz.scheduler.jobFactory.class";
/*      */   public static final String PROP_SCHED_JOB_FACTORY_PREFIX = "org.quartz.scheduler.jobFactory";
/*      */   public static final String PROP_SCHED_INTERRUPT_JOBS_ON_SHUTDOWN = "org.quartz.scheduler.interruptJobsOnShutdown";
/*      */   public static final String PROP_SCHED_INTERRUPT_JOBS_ON_SHUTDOWN_WITH_WAIT = "org.quartz.scheduler.interruptJobsOnShutdownWithWait";
/*      */   public static final String PROP_SCHED_CONTEXT_PREFIX = "org.quartz.context.key";
/*      */   public static final String PROP_THREAD_POOL_PREFIX = "org.quartz.threadPool";
/*      */   public static final String PROP_THREAD_POOL_CLASS = "org.quartz.threadPool.class";
/*      */   public static final String PROP_JOB_STORE_PREFIX = "org.quartz.jobStore";
/*      */   public static final String PROP_JOB_STORE_LOCK_HANDLER_PREFIX = "org.quartz.jobStore.lockHandler";
/*      */   public static final String PROP_JOB_STORE_LOCK_HANDLER_CLASS = "org.quartz.jobStore.lockHandler.class";
/*      */   public static final String PROP_TABLE_PREFIX = "tablePrefix";
/*      */   public static final String PROP_SCHED_NAME = "schedName";
/*      */   public static final String PROP_JOB_STORE_CLASS = "org.quartz.jobStore.class";
/*      */   public static final String PROP_JOB_STORE_USE_PROP = "org.quartz.jobStore.useProperties";
/*      */   public static final String PROP_DATASOURCE_PREFIX = "org.quartz.dataSource";
/*      */   public static final String PROP_CONNECTION_PROVIDER_CLASS = "connectionProvider.class";
/*      */   @Deprecated
/*      */   public static final String PROP_DATASOURCE_DRIVER = "driver";
/*      */   @Deprecated
/*      */   public static final String PROP_DATASOURCE_URL = "URL";
/*      */   @Deprecated
/*      */   public static final String PROP_DATASOURCE_USER = "user";
/*      */   @Deprecated
/*      */   public static final String PROP_DATASOURCE_PASSWORD = "password";
/*      */   @Deprecated
/*      */   public static final String PROP_DATASOURCE_MAX_CONNECTIONS = "maxConnections";
/*      */   @Deprecated
/*      */   public static final String PROP_DATASOURCE_VALIDATION_QUERY = "validationQuery";
/*      */   public static final String PROP_DATASOURCE_JNDI_URL = "jndiURL";
/*      */   public static final String PROP_DATASOURCE_JNDI_ALWAYS_LOOKUP = "jndiAlwaysLookup";
/*      */   public static final String PROP_DATASOURCE_JNDI_INITIAL = "java.naming.factory.initial";
/*      */   public static final String PROP_DATASOURCE_JNDI_PROVDER = "java.naming.provider.url";
/*      */   public static final String PROP_DATASOURCE_JNDI_PRINCIPAL = "java.naming.security.principal";
/*      */   public static final String PROP_DATASOURCE_JNDI_CREDENTIALS = "java.naming.security.credentials";
/*      */   public static final String PROP_PLUGIN_PREFIX = "org.quartz.plugin";
/*      */   public static final String PROP_PLUGIN_CLASS = "class";
/*      */   public static final String PROP_JOB_LISTENER_PREFIX = "org.quartz.jobListener";
/*      */   public static final String PROP_TRIGGER_LISTENER_PREFIX = "org.quartz.triggerListener";
/*      */   public static final String PROP_LISTENER_CLASS = "class";
/*      */   public static final String DEFAULT_INSTANCE_ID = "NON_CLUSTERED";
/*      */   public static final String AUTO_GENERATE_INSTANCE_ID = "AUTO";
/*      */   public static final String PROP_THREAD_EXECUTOR = "org.quartz.threadExecutor";
/*      */   public static final String PROP_THREAD_EXECUTOR_CLASS = "org.quartz.threadExecutor.class";
/*      */   public static final String SYSTEM_PROPERTY_AS_INSTANCE_ID = "SYS_PROP";
/*      */   public static final String MANAGEMENT_REST_SERVICE_ENABLED = "org.quartz.managementRESTService.enabled";
/*      */   public static final String MANAGEMENT_REST_SERVICE_HOST_PORT = "org.quartz.managementRESTService.bind";
/*  135 */   private SchedulerException initException = null;
/*  136 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*  137 */   private String propSrc = null;
/*      */   private PropertiesParser cfg;
/*  139 */   private final Logger log = LoggerFactory.getLogger(getClass());
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static final String configPro = "org.quartz.scheduler.instanceName: DefaultQuartzScheduler\norg.quartz.scheduler.rmi.export: false\norg.quartz.scheduler.rmi.proxy: false\norg.quartz.scheduler.wrapJobExecutionInUserTransaction: false\n\norg.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool\norg.quartz.threadPool.threadCount: 100\norg.quartz.scheduler.batchTriggerAcquisitionMaxCount: 100\norg.quartz.threadPool.threadPriority: 5\norg.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread: true\n\norg.quartz.jobStore.misfireThreshold: 60000\n\norg.quartz.jobStore.class: org.quartz.simpl.RAMJobStore";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public StdSchedulerFactory() {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public StdSchedulerFactory(Properties paramProperties) throws SchedulerException {
/*  164 */     initialize(paramProperties);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public StdSchedulerFactory(String paramString) throws SchedulerException {
/*  170 */     initialize(paramString);
/*      */   }
/*      */ 
/*      */   
/*      */   public Logger getLog() {
/*  175 */     return this.log;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void initialize() throws SchedulerException {
/*  181 */     if (this.cfg != null) {
/*      */       return;
/*      */     }
/*  184 */     if (this.initException != null) {
/*  185 */       throw this.initException;
/*      */     }
/*      */     
/*  188 */     String str1 = System.getProperty("org.quartz.properties");
/*  189 */     String str2 = (str1 != null) ? str1 : "quartz.properties";
/*  190 */     this.logger.info("  -----------requestedFile : " + str1);
/*  191 */     File file = new File(str2);
/*      */     
/*  193 */     Properties properties = new Properties();
/*      */     
/*  195 */     InputStream inputStream = new ByteArrayInputStream("org.quartz.scheduler.instanceName: DefaultQuartzScheduler\norg.quartz.scheduler.rmi.export: false\norg.quartz.scheduler.rmi.proxy: false\norg.quartz.scheduler.wrapJobExecutionInUserTransaction: false\n\norg.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool\norg.quartz.threadPool.threadCount: 100\norg.quartz.scheduler.batchTriggerAcquisitionMaxCount: 100\norg.quartz.threadPool.threadPriority: 5\norg.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread: true\n\norg.quartz.jobStore.misfireThreshold: 60000\n\norg.quartz.jobStore.class: org.quartz.simpl.RAMJobStore".getBytes());
/*      */     
/*      */     try {
/*  198 */       if (file.exists()) {
/*      */         
/*      */         try {
/*  201 */           this.logger.info("  -----------初始化计划任务配置 1 -----------");
/*  202 */           if (str1 != null) {
/*  203 */             this.propSrc = "specified file: '" + str1 + "'";
/*      */           } else {
/*  205 */             this.propSrc = "default file in current working dir: 'quartz.properties'";
/*      */           } 
/*      */           
/*  208 */           inputStream = new BufferedInputStream(new FileInputStream(str2));
/*  209 */           properties.load(inputStream);
/*      */         }
/*  211 */         catch (IOException iOException) {
/*  212 */           this.initException = new SchedulerException("Properties file: '" + str2 + "' could not be read.", iOException);
/*      */           
/*  214 */           throw this.initException;
/*      */         } 
/*  216 */       } else if (str1 != null) {
/*      */         
/*  218 */         this.logger.info("  -----------初始化计划任务配置 2 ----------- requestedFile:" + str1);
/*  219 */         inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(str1);
/*      */         
/*  221 */         if (inputStream == null) {
/*  222 */           this.initException = new SchedulerException("Properties file: '" + str1 + "' could not be found.");
/*      */           
/*  224 */           throw this.initException;
/*      */         } 
/*      */         
/*  227 */         this.propSrc = "specified file: '" + str1 + "' in the class resource path.";
/*      */         
/*  229 */         inputStream = new BufferedInputStream(inputStream);
/*      */         try {
/*  231 */           properties.load(inputStream);
/*  232 */         } catch (IOException iOException) {
/*  233 */           this.initException = new SchedulerException("Properties file: '" + str1 + "' could not be read.", iOException);
/*      */           
/*  235 */           throw this.initException;
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/*  240 */         this.logger.info("  -----------初始化计划任务配置 3 -----------");
/*  241 */         this.propSrc = "default resource file in Quartz package: 'quartz.properties'";
/*      */         
/*  243 */         ClassLoader classLoader = getClass().getClassLoader();
/*  244 */         if (classLoader == null)
/*  245 */           classLoader = findClassloader(); 
/*  246 */         if (classLoader == null) {
/*  247 */           throw new SchedulerConfigException("Unable to find a class loader on the current thread or class.");
/*      */         }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  255 */         if (inputStream == null) {
/*  256 */           inputStream = classLoader.getResourceAsStream("org/quartz/quartz.properties");
/*      */         }
/*      */         
/*  259 */         if (inputStream == null) {
/*  260 */           this.initException = new SchedulerException("Default quartz.properties not found in class path");
/*      */           
/*  262 */           throw this.initException;
/*      */         } 
/*      */         try {
/*  265 */           properties.load(inputStream);
/*  266 */         } catch (IOException iOException) {
/*  267 */           this.initException = new SchedulerException("Resource properties file: 'org/quartz/quartz.properties' could not be read from the classpath.", iOException);
/*      */           
/*  269 */           throw this.initException;
/*      */         } 
/*      */       } 
/*      */     } finally {
/*  273 */       if (inputStream != null) {
/*  274 */         try { inputStream.close(); }
/*      */         
/*  276 */         catch (IOException iOException) {}
/*      */       }
/*      */     } 
/*  279 */     initialize(overrideWithSysProps(properties));
/*      */   }
/*      */ 
/*      */   
/*      */   private Properties overrideWithSysProps(Properties paramProperties) {
/*  284 */     Properties properties = null;
/*      */     try {
/*  286 */       properties = System.getProperties();
/*  287 */     } catch (AccessControlException accessControlException) {
/*  288 */       getLog().warn("Skipping overriding quartz properties with System properties during initialization because of an AccessControlException.  This is likely due to not having read/write access for java.util.PropertyPermission as required by java.lang.System.getProperties().  To resolve this warning, either add this permission to your policy file or use a non-default version of initialize().", accessControlException);
/*      */     } 
/*      */     
/*  291 */     if (properties != null) {
/*  292 */       paramProperties.putAll(properties);
/*      */     }
/*      */     
/*  295 */     return paramProperties;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void initialize(String paramString) throws SchedulerException {
/*  301 */     if (this.cfg != null) {
/*      */       return;
/*      */     }
/*      */     
/*  305 */     if (this.initException != null) {
/*  306 */       throw this.initException;
/*      */     }
/*      */     
/*  309 */     InputStream inputStream = null;
/*  310 */     Properties properties = new Properties();
/*      */     
/*  312 */     inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(paramString);
/*      */     
/*      */     try {
/*  315 */       if (inputStream != null) {
/*  316 */         inputStream = new BufferedInputStream(inputStream);
/*  317 */         this.propSrc = "the specified file : '" + paramString + "' from the class resource path.";
/*      */       } else {
/*  319 */         inputStream = new BufferedInputStream(new FileInputStream(paramString));
/*  320 */         this.propSrc = "the specified file : '" + paramString + "'";
/*      */       } 
/*  322 */       properties.load(inputStream);
/*  323 */     } catch (IOException iOException) {
/*  324 */       this.initException = new SchedulerException("Properties file: '" + paramString + "' could not be read.", iOException);
/*      */       
/*  326 */       throw this.initException;
/*      */     } finally {
/*      */       
/*  329 */       if (inputStream != null) {
/*  330 */         try { inputStream.close(); }
/*  331 */         catch (IOException iOException) {}
/*      */       }
/*      */     } 
/*  334 */     initialize(properties);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void initialize(InputStream paramInputStream) throws SchedulerException {
/*  340 */     if (this.cfg != null) {
/*      */       return;
/*      */     }
/*      */     
/*  344 */     if (this.initException != null) {
/*  345 */       throw this.initException;
/*      */     }
/*      */     
/*  348 */     Properties properties = new Properties();
/*      */     
/*  350 */     if (paramInputStream != null) {
/*      */       try {
/*  352 */         properties.load(paramInputStream);
/*  353 */         this.propSrc = "an externally opened InputStream.";
/*  354 */       } catch (IOException iOException) {
/*  355 */         this.initException = new SchedulerException("Error loading property data from InputStream", iOException);
/*      */         
/*  357 */         throw this.initException;
/*      */       } 
/*      */     } else {
/*  360 */       this.initException = new SchedulerException("Error loading property data from InputStream - InputStream is null.");
/*      */       
/*  362 */       throw this.initException;
/*      */     } 
/*      */     
/*  365 */     initialize(properties);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void initialize(Properties paramProperties) throws SchedulerException {
/*  371 */     if (this.propSrc == null) {
/*  372 */       this.propSrc = "an externally provided properties instance.";
/*      */     }
/*      */     
/*  375 */     this.cfg = new PropertiesParser(paramProperties);
/*      */   }
/*      */   private Scheduler instantiate() throws SchedulerException {
/*      */     DefaultThreadExecutor defaultThreadExecutor;
/*  379 */     if (this.cfg == null) {
/*  380 */       initialize();
/*      */     }
/*      */     
/*  383 */     if (this.initException != null) {
/*  384 */       throw this.initException;
/*      */     }
/*      */     
/*  387 */     JobStore jobStore = null;
/*  388 */     ThreadPool threadPool = null;
/*  389 */     QuartzScheduler quartzScheduler = null;
/*  390 */     DBConnectionManager dBConnectionManager = null;
/*  391 */     String str1 = null;
/*  392 */     Properties properties1 = null;
/*  393 */     String str2 = null;
/*  394 */     boolean bool1 = false;
/*  395 */     boolean bool2 = false;
/*  396 */     long l1 = -1L;
/*  397 */     long l2 = 15000L;
/*      */     
/*  399 */     SchedulerRepository schedulerRepository = SchedulerRepository.getInstance();
/*      */     
/*  401 */     String str3 = this.cfg.getStringProperty("org.quartz.scheduler.instanceName", "QuartzScheduler");
/*      */     
/*  403 */     String str4 = this.cfg.getStringProperty("org.quartz.scheduler.threadName", str3 + "_QuartzSchedulerThread");
/*      */     
/*  405 */     String str5 = this.cfg.getStringProperty("org.quartz.scheduler.instanceId", "NON_CLUSTERED");
/*      */     
/*  407 */     if (str5.equals("AUTO")) {
/*  408 */       bool2 = true;
/*  409 */       str1 = this.cfg.getStringProperty("org.quartz.scheduler.instanceIdGenerator.class", "org.quartz.simpl.SimpleInstanceIdGenerator");
/*      */     }
/*  411 */     else if (str5.equals("SYS_PROP")) {
/*  412 */       bool2 = true;
/*  413 */       str1 = "org.quartz.simpl.SystemPropertyInstanceIdGenerator";
/*      */     } 
/*      */     
/*  416 */     str2 = this.cfg.getStringProperty("org.quartz.scheduler.userTransactionURL", str2);
/*      */     
/*  418 */     if (str2 != null && str2.trim().length() == 0) {
/*  419 */       str2 = null;
/*      */     }
/*      */     
/*  422 */     String str6 = this.cfg.getStringProperty("org.quartz.scheduler.classLoadHelper.class", "org.quartz.simpl.CascadingClassLoadHelper");
/*      */     
/*  424 */     bool1 = this.cfg.getBooleanProperty("org.quartz.scheduler.wrapJobExecutionInUserTransaction", bool1);
/*      */     
/*  426 */     String str7 = this.cfg.getStringProperty("org.quartz.scheduler.jobFactory.class", null);
/*      */     
/*  428 */     l1 = this.cfg.getLongProperty("org.quartz.scheduler.idleWaitTime", l1);
/*      */     
/*  430 */     if (l1 > -1L && l1 < 1000L) {
/*  431 */       throw new SchedulerException("org.quartz.scheduler.idleWaitTime of less than 1000ms is not legal.");
/*      */     }
/*      */     
/*  434 */     l2 = this.cfg.getLongProperty("org.quartz.scheduler.dbFailureRetryInterval", l2);
/*  435 */     if (l2 < 0L) {
/*  436 */       throw new SchedulerException("org.quartz.scheduler.dbFailureRetryInterval of less than 0 ms is not legal.");
/*      */     }
/*      */     
/*  439 */     boolean bool3 = this.cfg.getBooleanProperty("org.quartz.scheduler.makeSchedulerThreadDaemon");
/*      */     
/*  441 */     boolean bool4 = this.cfg.getBooleanProperty("org.quartz.scheduler.threadsInheritContextClassLoaderOfInitializer");
/*      */     
/*  443 */     boolean bool5 = this.cfg.getBooleanProperty("org.quartz.scheduler.skipUpdateCheck", true);
/*  444 */     long l3 = this.cfg.getLongProperty("org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow", 0L);
/*  445 */     int i = this.cfg.getIntProperty("org.quartz.scheduler.batchTriggerAcquisitionMaxCount", 1);
/*      */     
/*  447 */     boolean bool6 = this.cfg.getBooleanProperty("org.quartz.scheduler.interruptJobsOnShutdown", false);
/*  448 */     boolean bool7 = this.cfg.getBooleanProperty("org.quartz.scheduler.interruptJobsOnShutdownWithWait", false);
/*      */     
/*  450 */     boolean bool8 = this.cfg.getBooleanProperty("org.quartz.scheduler.jmx.export");
/*  451 */     String str8 = this.cfg.getStringProperty("org.quartz.scheduler.jmx.objectName");
/*      */     
/*  453 */     boolean bool9 = this.cfg.getBooleanProperty("org.quartz.scheduler.jmx.proxy");
/*  454 */     String str9 = this.cfg.getStringProperty("org.quartz.scheduler.jmx.proxy.class");
/*      */     
/*  456 */     boolean bool10 = this.cfg.getBooleanProperty("org.quartz.scheduler.rmi.export", false);
/*  457 */     boolean bool11 = this.cfg.getBooleanProperty("org.quartz.scheduler.rmi.proxy", false);
/*  458 */     String str10 = this.cfg.getStringProperty("org.quartz.scheduler.rmi.registryHost", "localhost");
/*  459 */     int j = this.cfg.getIntProperty("org.quartz.scheduler.rmi.registryPort", 1099);
/*  460 */     int k = this.cfg.getIntProperty("org.quartz.scheduler.rmi.serverPort", -1);
/*  461 */     String str11 = this.cfg.getStringProperty("org.quartz.scheduler.rmi.createRegistry", "never");
/*      */     
/*  463 */     String str12 = this.cfg.getStringProperty("org.quartz.scheduler.rmi.bindName");
/*      */     
/*  465 */     if (bool9 && bool11) {
/*  466 */       throw new SchedulerConfigException("Cannot proxy both RMI and JMX.");
/*      */     }
/*      */     
/*  469 */     boolean bool12 = this.cfg.getBooleanProperty("org.quartz.managementRESTService.enabled", false);
/*  470 */     String str13 = this.cfg.getStringProperty("org.quartz.managementRESTService.bind", "0.0.0.0:9889");
/*      */     
/*  472 */     Properties properties2 = this.cfg.getPropertyGroup("org.quartz.context.key", true);
/*      */     
/*  474 */     if (bool11) {
/*      */       
/*  476 */       if (bool2) {
/*  477 */         str5 = "NON_CLUSTERED";
/*      */       }
/*      */       
/*  480 */       String str = (str12 == null) ? QuartzSchedulerResources.getUniqueIdentifier(str3, str5) : str12;
/*      */       
/*  482 */       RemoteScheduler remoteScheduler = new RemoteScheduler(str, str10, j);
/*      */       
/*  484 */       schedulerRepository.bind((Scheduler)remoteScheduler);
/*      */       
/*  486 */       return (Scheduler)remoteScheduler;
/*      */     } 
/*      */     
/*  489 */     ClassLoadHelper classLoadHelper = null;
/*      */     try {
/*  491 */       classLoadHelper = (ClassLoadHelper)loadClass(str6).newInstance();
/*      */     }
/*  493 */     catch (Exception exception) {
/*  494 */       throw new SchedulerConfigException("Unable to instantiate class load helper class: " + exception.getMessage(), exception);
/*      */     } 
/*      */     
/*  497 */     classLoadHelper.initialize();
/*      */     
/*  499 */     if (bool9) {
/*  500 */       if (bool2) {
/*  501 */         str5 = "NON_CLUSTERED";
/*      */       }
/*      */       
/*  504 */       if (str9 == null) {
/*  505 */         throw new SchedulerConfigException("No JMX Proxy Scheduler class provided");
/*      */       }
/*      */       
/*  508 */       RemoteMBeanScheduler remoteMBeanScheduler = null;
/*      */       try {
/*  510 */         remoteMBeanScheduler = classLoadHelper.loadClass(str9).newInstance();
/*      */       }
/*  512 */       catch (Exception exception) {
/*  513 */         throw new SchedulerConfigException("Unable to instantiate RemoteMBeanScheduler class.", exception);
/*      */       } 
/*      */       
/*  516 */       if (str8 == null) {
/*  517 */         str8 = QuartzSchedulerResources.generateJMXObjectName(str3, str5);
/*      */       }
/*      */       
/*  520 */       remoteMBeanScheduler.setSchedulerObjectName(str8);
/*      */       
/*  522 */       properties1 = this.cfg.getPropertyGroup("org.quartz.scheduler.jmx.proxy", true);
/*      */       try {
/*  524 */         setBeanProps(remoteMBeanScheduler, properties1);
/*  525 */       } catch (Exception exception) {
/*  526 */         this.initException = new SchedulerException("RemoteMBeanScheduler class '" + str9 + "' props could not be configured.", exception);
/*      */         
/*  528 */         throw this.initException;
/*      */       } 
/*      */       
/*  531 */       remoteMBeanScheduler.initialize();
/*      */       
/*  533 */       schedulerRepository.bind((Scheduler)remoteMBeanScheduler);
/*      */       
/*  535 */       return (Scheduler)remoteMBeanScheduler;
/*      */     } 
/*      */     
/*  538 */     JobFactory jobFactory = null;
/*  539 */     if (str7 != null) {
/*      */       try {
/*  541 */         jobFactory = classLoadHelper.loadClass(str7).newInstance();
/*      */       }
/*  543 */       catch (Exception exception) {
/*  544 */         throw new SchedulerConfigException("Unable to instantiate JobFactory class: " + exception.getMessage(), exception);
/*      */       } 
/*      */       
/*  547 */       properties1 = this.cfg.getPropertyGroup("org.quartz.scheduler.jobFactory", true);
/*      */       try {
/*  549 */         setBeanProps(jobFactory, properties1);
/*  550 */       } catch (Exception exception) {
/*  551 */         this.initException = new SchedulerException("JobFactory class '" + str7 + "' props could not be configured.", exception);
/*      */         
/*  553 */         throw this.initException;
/*      */       } 
/*      */     } 
/*      */     
/*  557 */     InstanceIdGenerator instanceIdGenerator = null;
/*  558 */     if (str1 != null) {
/*      */       try {
/*  560 */         instanceIdGenerator = classLoadHelper.loadClass(str1).newInstance();
/*      */       }
/*  562 */       catch (Exception exception) {
/*  563 */         throw new SchedulerConfigException("Unable to instantiate InstanceIdGenerator class: " + exception.getMessage(), exception);
/*      */       } 
/*      */       
/*  566 */       properties1 = this.cfg.getPropertyGroup("org.quartz.scheduler.instanceIdGenerator", true);
/*      */       try {
/*  568 */         setBeanProps(instanceIdGenerator, properties1);
/*  569 */       } catch (Exception exception) {
/*  570 */         this.initException = new SchedulerException("InstanceIdGenerator class '" + str1 + "' props could not be configured.", exception);
/*      */         
/*  572 */         throw this.initException;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  577 */     String str14 = this.cfg.getStringProperty("org.quartz.threadPool.class", SimpleThreadPool.class.getName());
/*      */     
/*  579 */     if (str14 == null) {
/*  580 */       this.initException = new SchedulerException("ThreadPool class not specified. ");
/*      */       
/*  582 */       throw this.initException;
/*      */     } 
/*      */     
/*      */     try {
/*  586 */       threadPool = classLoadHelper.loadClass(str14).newInstance();
/*  587 */     } catch (Exception exception) {
/*  588 */       this.initException = new SchedulerException("ThreadPool class '" + str14 + "' could not be instantiated.", exception);
/*      */       
/*  590 */       throw this.initException;
/*      */     } 
/*  592 */     properties1 = this.cfg.getPropertyGroup("org.quartz.threadPool", true);
/*      */     try {
/*  594 */       setBeanProps(threadPool, properties1);
/*  595 */     } catch (Exception exception) {
/*  596 */       this.initException = new SchedulerException("ThreadPool class '" + str14 + "' props could not be configured.", exception);
/*      */       
/*  598 */       throw this.initException;
/*      */     } 
/*      */     
/*  601 */     String str15 = this.cfg.getStringProperty("org.quartz.jobStore.class", RAMJobStore.class.getName());
/*      */     
/*  603 */     if (str15 == null) {
/*  604 */       this.initException = new SchedulerException("JobStore class not specified. ");
/*      */       
/*  606 */       throw this.initException;
/*      */     } 
/*      */     
/*      */     try {
/*  610 */       jobStore = classLoadHelper.loadClass(str15).newInstance();
/*  611 */     } catch (Exception exception) {
/*  612 */       this.initException = new SchedulerException("JobStore class '" + str15 + "' could not be instantiated.", exception);
/*      */       
/*  614 */       throw this.initException;
/*      */     } 
/*      */     
/*  617 */     SchedulerDetailsSetter.setDetails(jobStore, str3, str5);
/*      */     
/*  619 */     properties1 = this.cfg.getPropertyGroup("org.quartz.jobStore", true, new String[] { "org.quartz.jobStore.lockHandler" });
/*      */     try {
/*  621 */       setBeanProps(jobStore, properties1);
/*  622 */     } catch (Exception exception) {
/*  623 */       this.initException = new SchedulerException("JobStore class '" + str15 + "' props could not be configured.", exception);
/*      */       
/*  625 */       throw this.initException;
/*      */     } 
/*      */     
/*  628 */     if (jobStore instanceof JobStoreSupport) {
/*      */       
/*  630 */       String str = this.cfg.getStringProperty("org.quartz.jobStore.lockHandler.class");
/*  631 */       if (str != null) {
/*      */         try {
/*  633 */           Semaphore semaphore = classLoadHelper.loadClass(str).newInstance();
/*      */           
/*  635 */           properties1 = this.cfg.getPropertyGroup("org.quartz.jobStore.lockHandler", true);
/*      */           
/*  637 */           if (semaphore instanceof org.quartz.impl.jdbcjobstore.TablePrefixAware) {
/*  638 */             properties1.setProperty("tablePrefix", ((JobStoreSupport)jobStore).getTablePrefix());
/*      */             
/*  640 */             properties1.setProperty("schedName", str3);
/*      */           } 
/*      */ 
/*      */           
/*      */           try {
/*  645 */             setBeanProps(semaphore, properties1);
/*  646 */           } catch (Exception exception) {
/*  647 */             this.initException = new SchedulerException("JobStore LockHandler class '" + str + "' props could not be configured.", exception);
/*      */             
/*  649 */             throw this.initException;
/*      */           } 
/*      */           
/*  652 */           ((JobStoreSupport)jobStore).setLockHandler(semaphore);
/*  653 */           getLog().info("Using custom data access locking (synchronization): " + str);
/*  654 */         } catch (Exception exception) {
/*  655 */           this.initException = new SchedulerException("JobStore LockHandler class '" + str + "' could not be instantiated.", exception);
/*      */           
/*  657 */           throw this.initException;
/*      */         } 
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  664 */     String[] arrayOfString1 = this.cfg.getPropertyGroups("org.quartz.dataSource");
/*  665 */     for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/*  666 */       PropertiesParser propertiesParser = new PropertiesParser(this.cfg.getPropertyGroup("org.quartz.dataSource." + arrayOfString1[b1], true));
/*      */       
/*  668 */       String str = propertiesParser.getStringProperty("connectionProvider.class", null);
/*      */       
/*  670 */       if (str != null) {
/*  671 */         ConnectionProvider connectionProvider = null;
/*      */         try {
/*  673 */           connectionProvider = classLoadHelper.loadClass(str).newInstance();
/*  674 */         } catch (Exception exception) {
/*  675 */           this.initException = new SchedulerException("ConnectionProvider class '" + str + "' could not be instantiated.", exception);
/*      */           
/*  677 */           throw this.initException;
/*      */         } 
/*      */ 
/*      */         
/*      */         try {
/*  682 */           propertiesParser.getUnderlyingProperties().remove("connectionProvider.class");
/*      */           
/*  684 */           if (connectionProvider instanceof PoolingConnectionProvider) {
/*  685 */             populateProviderWithExtraProps((PoolingConnectionProvider)connectionProvider, propertiesParser.getUnderlyingProperties());
/*      */           } else {
/*  687 */             setBeanProps(connectionProvider, propertiesParser.getUnderlyingProperties());
/*      */           } 
/*  689 */           connectionProvider.initialize();
/*  690 */         } catch (Exception exception) {
/*  691 */           this.initException = new SchedulerException("ConnectionProvider class '" + str + "' props could not be configured.", exception);
/*      */           
/*  693 */           throw this.initException;
/*      */         } 
/*      */         
/*  696 */         dBConnectionManager = DBConnectionManager.getInstance();
/*  697 */         dBConnectionManager.addConnectionProvider(arrayOfString1[b1], connectionProvider);
/*      */       } else {
/*  699 */         String str17 = propertiesParser.getStringProperty("jndiURL", null);
/*      */         
/*  701 */         if (str17 != null) {
/*  702 */           boolean bool = propertiesParser.getBooleanProperty("jndiAlwaysLookup");
/*      */           
/*  704 */           String str18 = propertiesParser.getStringProperty("java.naming.factory.initial");
/*      */           
/*  706 */           String str19 = propertiesParser.getStringProperty("java.naming.provider.url");
/*      */           
/*  708 */           String str20 = propertiesParser.getStringProperty("java.naming.security.principal");
/*      */           
/*  710 */           String str21 = propertiesParser.getStringProperty("java.naming.security.credentials");
/*      */           
/*  712 */           Properties properties = null;
/*  713 */           if (null != str18 || null != str19 || null != str20 || null != str21) {
/*      */             
/*  715 */             properties = new Properties();
/*  716 */             if (str18 != null) {
/*  717 */               properties.put("java.naming.factory.initial", str18);
/*      */             }
/*      */             
/*  720 */             if (str19 != null) {
/*  721 */               properties.put("java.naming.provider.url", str19);
/*      */             }
/*      */             
/*  724 */             if (str20 != null) {
/*  725 */               properties.put("java.naming.security.principal", str20);
/*      */             }
/*      */             
/*  728 */             if (str21 != null) {
/*  729 */               properties.put("java.naming.security.credentials", str21);
/*      */             }
/*      */           } 
/*      */           
/*  733 */           JNDIConnectionProvider jNDIConnectionProvider = new JNDIConnectionProvider(str17, properties, bool);
/*      */           
/*  735 */           dBConnectionManager = DBConnectionManager.getInstance();
/*  736 */           dBConnectionManager.addConnectionProvider(arrayOfString1[b1], (ConnectionProvider)jNDIConnectionProvider);
/*      */         } else {
/*  738 */           String str18 = propertiesParser.getStringProperty("driver");
/*  739 */           String str19 = propertiesParser.getStringProperty("URL");
/*      */           
/*  741 */           if (str18 == null) {
/*  742 */             this.initException = new SchedulerException("Driver not specified for DataSource: " + arrayOfString1[b1]);
/*      */             
/*  744 */             throw this.initException;
/*      */           } 
/*  746 */           if (str19 == null) {
/*  747 */             this.initException = new SchedulerException("DB URL not specified for DataSource: " + arrayOfString1[b1]);
/*      */             
/*  749 */             throw this.initException;
/*      */           } 
/*      */           try {
/*  752 */             PoolingConnectionProvider poolingConnectionProvider = new PoolingConnectionProvider(propertiesParser.getUnderlyingProperties());
/*  753 */             dBConnectionManager = DBConnectionManager.getInstance();
/*  754 */             dBConnectionManager.addConnectionProvider(arrayOfString1[b1], (ConnectionProvider)poolingConnectionProvider);
/*      */             
/*  756 */             populateProviderWithExtraProps(poolingConnectionProvider, propertiesParser.getUnderlyingProperties());
/*  757 */           } catch (Exception exception) {
/*  758 */             this.initException = new SchedulerException("Could not initialize DataSource: " + arrayOfString1[b1], exception);
/*      */             
/*  760 */             throw this.initException;
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  769 */     String[] arrayOfString2 = this.cfg.getPropertyGroups("org.quartz.plugin");
/*  770 */     SchedulerPlugin[] arrayOfSchedulerPlugin = new SchedulerPlugin[arrayOfString2.length];
/*  771 */     for (byte b2 = 0; b2 < arrayOfString2.length; b2++) {
/*  772 */       Properties properties = this.cfg.getPropertyGroup("org.quartz.plugin." + arrayOfString2[b2], true);
/*      */       
/*  774 */       String str = properties.getProperty("class", (String)null);
/*      */       
/*  776 */       if (str == null) {
/*  777 */         this.initException = new SchedulerException("SchedulerPlugin class not specified for plugin '" + arrayOfString2[b2] + "'");
/*      */         
/*  779 */         throw this.initException;
/*      */       } 
/*  781 */       SchedulerPlugin schedulerPlugin = null;
/*      */       try {
/*  783 */         schedulerPlugin = classLoadHelper.loadClass(str).newInstance();
/*      */       }
/*  785 */       catch (Exception exception) {
/*  786 */         this.initException = new SchedulerException("SchedulerPlugin class '" + str + "' could not be instantiated.", exception);
/*      */         
/*  788 */         throw this.initException;
/*      */       } 
/*      */       try {
/*  791 */         setBeanProps(schedulerPlugin, properties);
/*  792 */       } catch (Exception exception) {
/*  793 */         this.initException = new SchedulerException("JobStore SchedulerPlugin '" + str + "' props could not be configured.", exception);
/*      */         
/*  795 */         throw this.initException;
/*      */       } 
/*      */       
/*  798 */       arrayOfSchedulerPlugin[b2] = schedulerPlugin;
/*      */     } 
/*      */     
/*  801 */     Class[] arrayOfClass = { String.class };
/*  802 */     String[] arrayOfString3 = this.cfg.getPropertyGroups("org.quartz.jobListener");
/*  803 */     JobListener[] arrayOfJobListener = new JobListener[arrayOfString3.length];
/*  804 */     for (byte b3 = 0; b3 < arrayOfString3.length; b3++) {
/*  805 */       Properties properties = this.cfg.getPropertyGroup("org.quartz.jobListener." + arrayOfString3[b3], true);
/*      */       
/*  807 */       String str = properties.getProperty("class", (String)null);
/*      */       
/*  809 */       if (str == null) {
/*  810 */         this.initException = new SchedulerException("JobListener class not specified for listener '" + arrayOfString3[b3] + "'");
/*      */         
/*  812 */         throw this.initException;
/*      */       } 
/*  814 */       JobListener jobListener = null;
/*      */       try {
/*  816 */         jobListener = classLoadHelper.loadClass(str).newInstance();
/*      */       }
/*  818 */       catch (Exception exception) {
/*  819 */         this.initException = new SchedulerException("JobListener class '" + str + "' could not be instantiated.", exception);
/*      */         
/*  821 */         throw this.initException;
/*      */       } 
/*      */       try {
/*  824 */         Method method = null;
/*      */         try {
/*  826 */           method = jobListener.getClass().getMethod("setName", arrayOfClass);
/*      */         }
/*  828 */         catch (NoSuchMethodException noSuchMethodException) {}
/*      */ 
/*      */         
/*  831 */         if (method != null) {
/*  832 */           method.invoke(jobListener, new Object[] { arrayOfString3[b3] });
/*      */         }
/*  834 */         setBeanProps(jobListener, properties);
/*  835 */       } catch (Exception exception) {
/*  836 */         this.initException = new SchedulerException("JobListener '" + str + "' props could not be configured.", exception);
/*      */         
/*  838 */         throw this.initException;
/*      */       } 
/*  840 */       arrayOfJobListener[b3] = jobListener;
/*      */     } 
/*      */     
/*  843 */     String[] arrayOfString4 = this.cfg.getPropertyGroups("org.quartz.triggerListener");
/*  844 */     TriggerListener[] arrayOfTriggerListener = new TriggerListener[arrayOfString4.length]; byte b4;
/*  845 */     for (b4 = 0; b4 < arrayOfString4.length; b4++) {
/*  846 */       Properties properties = this.cfg.getPropertyGroup("org.quartz.triggerListener." + arrayOfString4[b4], true);
/*      */       
/*  848 */       String str = properties.getProperty("class", (String)null);
/*      */       
/*  850 */       if (str == null) {
/*  851 */         this.initException = new SchedulerException("TriggerListener class not specified for listener '" + arrayOfString4[b4] + "'");
/*      */         
/*  853 */         throw this.initException;
/*      */       } 
/*  855 */       TriggerListener triggerListener = null;
/*      */       try {
/*  857 */         triggerListener = classLoadHelper.loadClass(str).newInstance();
/*      */       }
/*  859 */       catch (Exception exception) {
/*  860 */         this.initException = new SchedulerException("TriggerListener class '" + str + "' could not be instantiated.", exception);
/*      */         
/*  862 */         throw this.initException;
/*      */       } 
/*      */       try {
/*  865 */         Method method = null;
/*      */         try {
/*  867 */           method = triggerListener.getClass().getMethod("setName", arrayOfClass);
/*  868 */         } catch (NoSuchMethodException noSuchMethodException) {}
/*      */         
/*  870 */         if (method != null) {
/*  871 */           method.invoke(triggerListener, new Object[] { arrayOfString4[b4] });
/*      */         }
/*  873 */         setBeanProps(triggerListener, properties);
/*  874 */       } catch (Exception exception) {
/*  875 */         this.initException = new SchedulerException("TriggerListener '" + str + "' props could not be configured.", exception);
/*      */         
/*  877 */         throw this.initException;
/*      */       } 
/*  879 */       arrayOfTriggerListener[b4] = triggerListener;
/*      */     } 
/*      */     
/*  882 */     b4 = 0;
/*  883 */     boolean bool13 = false;
/*      */     
/*  885 */     String str16 = this.cfg.getStringProperty("org.quartz.threadExecutor.class");
/*      */     
/*  887 */     if (str16 != null) {
/*  888 */       properties1 = this.cfg.getPropertyGroup("org.quartz.threadExecutor", true);
/*      */       try {
/*  890 */         ThreadExecutor threadExecutor = classLoadHelper.loadClass(str16).newInstance();
/*  891 */         this.log.info("Using custom implementation for ThreadExecutor: " + str16);
/*      */         
/*  893 */         setBeanProps(threadExecutor, properties1);
/*  894 */       } catch (Exception exception) {
/*  895 */         this.initException = new SchedulerException("ThreadExecutor class '" + str16 + "' could not be instantiated.", exception);
/*      */         
/*  897 */         throw this.initException;
/*      */       } 
/*      */     } else {
/*  900 */       this.log.info("Using default implementation for ThreadExecutor");
/*  901 */       defaultThreadExecutor = new DefaultThreadExecutor();
/*      */     } 
/*      */     
/*      */     try {
/*      */       JTAAnnotationAwareJobRunShellFactory jTAAnnotationAwareJobRunShellFactory;
/*  906 */       JTAJobRunShellFactory jTAJobRunShellFactory = null;
/*      */       
/*  908 */       if (str2 != null) {
/*  909 */         UserTransactionHelper.setUserTxLocation(str2);
/*      */       }
/*      */       
/*  912 */       if (bool1) {
/*  913 */         jTAJobRunShellFactory = new JTAJobRunShellFactory();
/*      */       } else {
/*  915 */         jTAAnnotationAwareJobRunShellFactory = new JTAAnnotationAwareJobRunShellFactory();
/*      */       } 
/*      */       
/*  918 */       if (bool2) {
/*      */         try {
/*  920 */           str5 = "NON_CLUSTERED";
/*  921 */           if (jobStore.isClustered()) {
/*  922 */             str5 = instanceIdGenerator.generateInstanceId();
/*      */           }
/*  924 */         } catch (Exception exception) {
/*  925 */           getLog().error("Couldn't generate instance Id!", exception);
/*  926 */           throw new IllegalStateException("Cannot run without an instance id.");
/*      */         } 
/*      */       }
/*      */       
/*  930 */       if (jobStore.getClass().getName().startsWith("org.terracotta.quartz")) {
/*      */         try {
/*  932 */           String str = (String)jobStore.getClass().getMethod("getUUID", new Class[0]).invoke(jobStore, new Object[0]);
/*  933 */           if (str5.equals("NON_CLUSTERED")) {
/*  934 */             str5 = "TERRACOTTA_CLUSTERED,node=" + str;
/*  935 */             if (str8 == null) {
/*  936 */               str8 = QuartzSchedulerResources.generateJMXObjectName(str3, str5);
/*      */             }
/*  938 */           } else if (str8 == null) {
/*  939 */             str8 = QuartzSchedulerResources.generateJMXObjectName(str3, str5 + ",node=" + str);
/*      */           } 
/*  941 */         } catch (Exception exception) {
/*  942 */           throw new RuntimeException("Problem obtaining node id from TerracottaJobStore.", exception);
/*      */         } 
/*      */         
/*  945 */         if (null == this.cfg.getStringProperty("org.quartz.scheduler.jmx.export")) {
/*  946 */           bool8 = true;
/*      */         }
/*      */       } 
/*      */       
/*  950 */       if (jobStore instanceof JobStoreSupport) {
/*  951 */         JobStoreSupport jobStoreSupport = (JobStoreSupport)jobStore;
/*  952 */         jobStoreSupport.setDbRetryInterval(l2);
/*  953 */         if (bool4) {
/*  954 */           jobStoreSupport.setThreadsInheritInitializersClassLoadContext(bool4);
/*      */         }
/*  956 */         jobStoreSupport.setThreadExecutor((ThreadExecutor)defaultThreadExecutor);
/*      */       } 
/*      */       
/*  959 */       QuartzSchedulerResources quartzSchedulerResources = new QuartzSchedulerResources();
/*  960 */       quartzSchedulerResources.setName(str3);
/*  961 */       quartzSchedulerResources.setThreadName(str4);
/*  962 */       quartzSchedulerResources.setInstanceId(str5);
/*  963 */       quartzSchedulerResources.setJobRunShellFactory((JobRunShellFactory)jTAAnnotationAwareJobRunShellFactory);
/*  964 */       quartzSchedulerResources.setMakeSchedulerThreadDaemon(bool3);
/*  965 */       quartzSchedulerResources.setThreadsInheritInitializersClassLoadContext(bool4);
/*  966 */       quartzSchedulerResources.setRunUpdateCheck(!bool5);
/*  967 */       quartzSchedulerResources.setBatchTimeWindow(l3);
/*  968 */       quartzSchedulerResources.setMaxBatchSize(i);
/*  969 */       quartzSchedulerResources.setInterruptJobsOnShutdown(bool6);
/*  970 */       quartzSchedulerResources.setInterruptJobsOnShutdownWithWait(bool7);
/*  971 */       quartzSchedulerResources.setJMXExport(bool8);
/*  972 */       quartzSchedulerResources.setJMXObjectName(str8);
/*      */       
/*  974 */       if (bool12) {
/*  975 */         ManagementRESTServiceConfiguration managementRESTServiceConfiguration = new ManagementRESTServiceConfiguration();
/*  976 */         managementRESTServiceConfiguration.setBind(str13);
/*  977 */         managementRESTServiceConfiguration.setEnabled(bool12);
/*  978 */         quartzSchedulerResources.setManagementRESTServiceConfiguration(managementRESTServiceConfiguration);
/*      */       } 
/*      */       
/*  981 */       if (bool10) {
/*  982 */         quartzSchedulerResources.setRMIRegistryHost(str10);
/*  983 */         quartzSchedulerResources.setRMIRegistryPort(j);
/*  984 */         quartzSchedulerResources.setRMIServerPort(k);
/*  985 */         quartzSchedulerResources.setRMICreateRegistryStrategy(str11);
/*  986 */         quartzSchedulerResources.setRMIBindName(str12);
/*      */       } 
/*      */       
/*  989 */       SchedulerDetailsSetter.setDetails(threadPool, str3, str5);
/*      */       
/*  991 */       quartzSchedulerResources.setThreadExecutor((ThreadExecutor)defaultThreadExecutor);
/*  992 */       defaultThreadExecutor.initialize();
/*      */       
/*  994 */       quartzSchedulerResources.setThreadPool(threadPool);
/*  995 */       if (threadPool instanceof SimpleThreadPool && bool4)
/*      */       {
/*  997 */         ((SimpleThreadPool)threadPool).setThreadsInheritContextClassLoaderOfInitializingThread(bool4);
/*      */       }
/*  999 */       threadPool.initialize();
/* 1000 */       b4 = 1;
/*      */       
/* 1002 */       quartzSchedulerResources.setJobStore(jobStore);
/*      */       
/* 1004 */       for (byte b5 = 0; b5 < arrayOfSchedulerPlugin.length; b5++) {
/* 1005 */         quartzSchedulerResources.addSchedulerPlugin(arrayOfSchedulerPlugin[b5]);
/*      */       }
/*      */       
/* 1008 */       quartzScheduler = new QuartzScheduler(quartzSchedulerResources, l1, l2);
/* 1009 */       bool13 = true;
/*      */       
/* 1011 */       Scheduler scheduler = instantiate(quartzSchedulerResources, quartzScheduler);
/*      */       
/* 1013 */       if (jobFactory != null) {
/* 1014 */         quartzScheduler.setJobFactory(jobFactory);
/*      */       }
/*      */       byte b6;
/* 1017 */       for (b6 = 0; b6 < arrayOfSchedulerPlugin.length; b6++) {
/* 1018 */         arrayOfSchedulerPlugin[b6].initialize(arrayOfString2[b6], scheduler, classLoadHelper);
/*      */       }
/*      */       
/* 1021 */       for (b6 = 0; b6 < arrayOfJobListener.length; b6++) {
/* 1022 */         quartzScheduler.getListenerManager().addJobListener(arrayOfJobListener[b6], (Matcher)EverythingMatcher.allJobs());
/*      */       }
/* 1024 */       for (b6 = 0; b6 < arrayOfTriggerListener.length; b6++) {
/* 1025 */         quartzScheduler.getListenerManager().addTriggerListener(arrayOfTriggerListener[b6], (Matcher)EverythingMatcher.allTriggers());
/*      */       }
/*      */       
/* 1028 */       for (String str17 : properties2.keySet()) {
/* 1029 */         String str18 = properties2.getProperty(str17);
/* 1030 */         scheduler.getContext().put(str17, str18);
/*      */       } 
/*      */       
/* 1033 */       jobStore.setInstanceId(str5);
/* 1034 */       jobStore.setInstanceName(str3);
/* 1035 */       jobStore.setThreadPoolSize(threadPool.getPoolSize());
/* 1036 */       jobStore.initialize(classLoadHelper, quartzScheduler.getSchedulerSignaler());
/*      */       
/* 1038 */       jTAAnnotationAwareJobRunShellFactory.initialize(scheduler);
/*      */       
/* 1040 */       quartzScheduler.initialize();
/*      */       
/* 1042 */       getLog().info("Quartz scheduler '" + scheduler.getSchedulerName() + "' initialized from " + this.propSrc);
/*      */       
/* 1044 */       getLog().info("Quartz scheduler version: " + quartzScheduler.getVersion());
/*      */       
/* 1046 */       quartzScheduler.addNoGCObject(schedulerRepository);
/*      */       
/* 1048 */       if (dBConnectionManager != null) {
/* 1049 */         quartzScheduler.addNoGCObject(dBConnectionManager);
/*      */       }
/*      */       
/* 1052 */       schedulerRepository.bind(scheduler);
/* 1053 */       return scheduler;
/*      */     }
/* 1055 */     catch (SchedulerException schedulerException) {
/* 1056 */       shutdownFromInstantiateException(threadPool, quartzScheduler, b4, bool13);
/* 1057 */       throw schedulerException;
/*      */     }
/* 1059 */     catch (RuntimeException runtimeException) {
/* 1060 */       shutdownFromInstantiateException(threadPool, quartzScheduler, b4, bool13);
/* 1061 */       throw runtimeException;
/*      */     }
/* 1063 */     catch (Error error) {
/* 1064 */       shutdownFromInstantiateException(threadPool, quartzScheduler, b4, bool13);
/* 1065 */       throw error;
/*      */     } 
/*      */   }
/*      */   
/*      */   private void populateProviderWithExtraProps(PoolingConnectionProvider paramPoolingConnectionProvider, Properties paramProperties) throws Exception {
/* 1070 */     Properties properties = new Properties();
/* 1071 */     properties.putAll(paramProperties);
/*      */     
/* 1073 */     properties.remove("driver");
/* 1074 */     properties.remove("URL");
/* 1075 */     properties.remove("user");
/* 1076 */     properties.remove("password");
/* 1077 */     properties.remove("idleConnectionValidationSeconds");
/* 1078 */     properties.remove("maxConnections");
/* 1079 */     properties.remove("maxCachedStatementsPerConnection");
/* 1080 */     properties.remove("validateOnCheckout");
/* 1081 */     properties.remove("validationQuery");
/* 1082 */     setBeanProps(paramPoolingConnectionProvider.getDataSource(), properties);
/*      */   }
/*      */   
/*      */   private void shutdownFromInstantiateException(ThreadPool paramThreadPool, QuartzScheduler paramQuartzScheduler, boolean paramBoolean1, boolean paramBoolean2) {
/*      */     try {
/* 1087 */       if (paramBoolean2)
/* 1088 */       { paramQuartzScheduler.shutdown(false); }
/* 1089 */       else if (paramBoolean1)
/* 1090 */       { paramThreadPool.shutdown(false); } 
/* 1091 */     } catch (Exception exception) {
/* 1092 */       getLog().error("Got another exception while shutting down after instantiation exception", exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   protected Scheduler instantiate(QuartzSchedulerResources paramQuartzSchedulerResources, QuartzScheduler paramQuartzScheduler) {
/* 1098 */     return new StdScheduler(paramQuartzScheduler);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void setBeanProps(Object paramObject, Properties paramProperties) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException, IntrospectionException, SchedulerConfigException {
/* 1105 */     paramProperties.remove("class");
/*      */     
/* 1107 */     BeanInfo beanInfo = Introspector.getBeanInfo(paramObject.getClass());
/* 1108 */     PropertyDescriptor[] arrayOfPropertyDescriptor = beanInfo.getPropertyDescriptors();
/* 1109 */     PropertiesParser propertiesParser = new PropertiesParser(paramProperties);
/*      */     
/* 1111 */     Enumeration<String> enumeration = paramProperties.keys();
/* 1112 */     while (enumeration.hasMoreElements()) {
/* 1113 */       String str1 = enumeration.nextElement();
/* 1114 */       String str2 = str1.substring(0, 1).toUpperCase(Locale.US);
/* 1115 */       String str3 = "set" + str2 + str1.substring(1);
/*      */       
/* 1117 */       Method method = getSetMethod(str3, arrayOfPropertyDescriptor);
/*      */       
/*      */       try {
/* 1120 */         if (method == null) {
/* 1121 */           throw new NoSuchMethodException("No setter for property '" + str1 + "'");
/*      */         }
/*      */         
/* 1124 */         Class[] arrayOfClass = method.getParameterTypes();
/* 1125 */         if (arrayOfClass.length != 1) {
/* 1126 */           throw new NoSuchMethodException("No 1-argument setter for property '" + str1 + "'");
/*      */         }
/*      */         
/* 1129 */         PropertiesParser propertiesParser1 = propertiesParser;
/* 1130 */         String str = propertiesParser.getStringProperty(str1);
/* 1131 */         if (str != null && str.startsWith("$@")) {
/* 1132 */           str = str.substring(2);
/* 1133 */           propertiesParser1 = this.cfg;
/*      */         } else {
/*      */           
/* 1136 */           str = str1;
/*      */         } 
/* 1138 */         if (arrayOfClass[0].equals(int.class)) {
/* 1139 */           method.invoke(paramObject, new Object[] { Integer.valueOf(propertiesParser1.getIntProperty(str)) }); continue;
/* 1140 */         }  if (arrayOfClass[0].equals(long.class)) {
/* 1141 */           method.invoke(paramObject, new Object[] { Long.valueOf(propertiesParser1.getLongProperty(str)) }); continue;
/* 1142 */         }  if (arrayOfClass[0].equals(float.class)) {
/* 1143 */           method.invoke(paramObject, new Object[] { Float.valueOf(propertiesParser1.getFloatProperty(str)) }); continue;
/* 1144 */         }  if (arrayOfClass[0].equals(double.class)) {
/* 1145 */           method.invoke(paramObject, new Object[] { Double.valueOf(propertiesParser1.getDoubleProperty(str)) }); continue;
/* 1146 */         }  if (arrayOfClass[0].equals(boolean.class)) {
/* 1147 */           method.invoke(paramObject, new Object[] { Boolean.valueOf(propertiesParser1.getBooleanProperty(str)) }); continue;
/* 1148 */         }  if (arrayOfClass[0].equals(String.class)) {
/* 1149 */           method.invoke(paramObject, new Object[] { propertiesParser1.getStringProperty(str) }); continue;
/*      */         } 
/* 1151 */         throw new NoSuchMethodException("No primitive-type setter for property '" + str1 + "'");
/*      */       
/*      */       }
/* 1154 */       catch (NumberFormatException numberFormatException) {
/*      */         
/* 1156 */         throw new SchedulerConfigException("Could not parse property '" + str1 + "' into correct data type: " + numberFormatException.toString());
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   private Method getSetMethod(String paramString, PropertyDescriptor[] paramArrayOfPropertyDescriptor) {
/* 1163 */     for (byte b = 0; b < paramArrayOfPropertyDescriptor.length; b++) {
/* 1164 */       Method method = paramArrayOfPropertyDescriptor[b].getWriteMethod();
/*      */       
/* 1166 */       if (method != null && method.getName().equals(paramString)) {
/* 1167 */         return method;
/*      */       }
/*      */     } 
/*      */     
/* 1171 */     return null;
/*      */   }
/*      */ 
/*      */   
/*      */   private Class<?> loadClass(String paramString) throws ClassNotFoundException, SchedulerConfigException {
/*      */     try {
/* 1177 */       ClassLoader classLoader = findClassloader();
/* 1178 */       if (classLoader != null)
/* 1179 */         return classLoader.loadClass(paramString); 
/* 1180 */       throw new SchedulerConfigException("Unable to find a class loader on the current thread or class.");
/* 1181 */     } catch (ClassNotFoundException classNotFoundException) {
/* 1182 */       if (getClass().getClassLoader() != null)
/* 1183 */         return getClass().getClassLoader().loadClass(paramString); 
/* 1184 */       throw classNotFoundException;
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   private ClassLoader findClassloader() {
/* 1190 */     if (Thread.currentThread().getContextClassLoader() == null && getClass().getClassLoader() != null) {
/* 1191 */       Thread.currentThread().setContextClassLoader(getClass().getClassLoader());
/*      */     }
/* 1193 */     return Thread.currentThread().getContextClassLoader();
/*      */   }
/*      */   
/*      */   private String getSchedulerName() {
/* 1197 */     return this.cfg.getStringProperty("org.quartz.scheduler.instanceName", "QuartzScheduler");
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Scheduler getScheduler() throws SchedulerException {
/* 1203 */     if (this.cfg == null) {
/* 1204 */       initialize();
/*      */     }
/*      */     
/* 1207 */     SchedulerRepository schedulerRepository = SchedulerRepository.getInstance();
/*      */     
/* 1209 */     Scheduler scheduler = schedulerRepository.lookup(getSchedulerName());
/*      */     
/* 1211 */     if (scheduler != null) {
/* 1212 */       if (scheduler.isShutdown()) {
/* 1213 */         schedulerRepository.remove(getSchedulerName());
/*      */       } else {
/* 1215 */         return scheduler;
/*      */       } 
/*      */     }
/*      */     
/* 1219 */     scheduler = instantiate();
/*      */     
/* 1221 */     return scheduler;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static Scheduler getDefaultScheduler() throws SchedulerException {
/* 1227 */     StdSchedulerFactory stdSchedulerFactory = new StdSchedulerFactory();
/*      */     
/* 1229 */     return stdSchedulerFactory.getScheduler();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Scheduler getScheduler(String paramString) throws SchedulerException {
/* 1235 */     return SchedulerRepository.getInstance().lookup(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Collection<Scheduler> getAllSchedulers() throws SchedulerException {
/* 1241 */     return SchedulerRepository.getInstance().lookupAll();
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/quartz/impl/StdSchedulerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */