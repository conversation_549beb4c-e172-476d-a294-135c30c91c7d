/*    */ package org.quartz.impl;
/*    */ 
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import java.util.HashMap;
/*    */ import org.quartz.Scheduler;
/*    */ import org.quartz.SchedulerException;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SchedulerRepository
/*    */ {
/* 16 */   private HashMap<String, Scheduler> schedulers = new HashMap<>();
/*    */   
/*    */   private static SchedulerRepository inst;
/*    */   
/*    */   public static synchronized SchedulerRepository getInstance() {
/* 21 */     if (inst == null) {
/* 22 */       inst = new SchedulerRepository();
/*    */     }
/*    */     
/* 25 */     return inst;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public synchronized void bind(Scheduler paramScheduler) throws SchedulerException {
/* 33 */     this.schedulers.put(paramScheduler.getSchedulerName(), paramScheduler);
/*    */   }
/*    */   
/*    */   public synchronized boolean remove(String paramString) {
/* 37 */     return (this.schedulers.remove(paramString) != null);
/*    */   }
/*    */   
/*    */   public synchronized Scheduler lookup(String paramString) {
/* 41 */     return this.schedulers.get(paramString);
/*    */   }
/*    */   
/*    */   public synchronized Collection<Scheduler> lookupAll() {
/* 45 */     return Collections.unmodifiableCollection(this.schedulers.values());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/quartz/impl/SchedulerRepository.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */