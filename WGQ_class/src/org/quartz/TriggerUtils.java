/*     */ package org.quartz;
/*     */ 
/*     */ import java.util.Collections;
/*     */ import java.util.Date;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import org.quartz.impl.triggers.CronTriggerImpl;
/*     */ import org.quartz.impl.triggers.SimpleTriggerImpl;
/*     */ import org.quartz.spi.OperableTrigger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TriggerUtils
/*     */ {
/*     */   public static List<Date> computeFireTimes(OperableTrigger paramOperableTrigger, Calendar paramCalendar, int paramInt) {
/*  80 */     LinkedList<Date> linkedList = new LinkedList();
/*     */     
/*  82 */     OperableTrigger operableTrigger = (OperableTrigger)paramOperableTrigger.clone();
/*     */     
/*  84 */     if (operableTrigger.getNextFireTime() == null) {
/*  85 */       operableTrigger.computeFirstFireTime(paramCalendar);
/*     */     }
/*     */     
/*  88 */     for (byte b = 0; b < paramInt; ) {
/*  89 */       Date date = operableTrigger.getNextFireTime();
/*  90 */       if (date != null) {
/*  91 */         linkedList.add(date);
/*  92 */         operableTrigger.triggered(paramCalendar);
/*     */         
/*     */         b++;
/*     */       } 
/*     */     } 
/*     */     
/*  98 */     return Collections.unmodifiableList(linkedList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Date computeEndTimeToAllowParticularNumberOfFirings(OperableTrigger paramOperableTrigger, Calendar paramCalendar, int paramInt) {
/* 120 */     OperableTrigger operableTrigger = (OperableTrigger)paramOperableTrigger.clone();
/*     */     
/* 122 */     if (operableTrigger.getNextFireTime() == null) {
/* 123 */       operableTrigger.computeFirstFireTime(paramCalendar);
/*     */     }
/*     */     
/* 126 */     byte b1 = 0;
/* 127 */     Date date = null;
/*     */     
/* 129 */     for (byte b2 = 0; b2 < paramInt; ) {
/* 130 */       Date date1 = operableTrigger.getNextFireTime();
/* 131 */       if (date1 != null) {
/* 132 */         b1++;
/* 133 */         operableTrigger.triggered(paramCalendar);
/* 134 */         if (b1 == paramInt) {
/* 135 */           date = date1;
/*     */         }
/*     */         
/*     */         b2++;
/*     */       } 
/*     */     } 
/* 141 */     if (date == null) {
/* 142 */       return null;
/*     */     }
/* 144 */     date = new Date(date.getTime() + 1000L);
/*     */     
/* 146 */     return date;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Date> computeFireTimesBetween(OperableTrigger paramOperableTrigger, Calendar paramCalendar, Date paramDate1, Date paramDate2) {
/* 174 */     LinkedList<Date> linkedList = new LinkedList();
/*     */     
/* 176 */     OperableTrigger operableTrigger = (OperableTrigger)paramOperableTrigger.clone();
/*     */     
/* 178 */     if (operableTrigger.getNextFireTime() == null) {
/* 179 */       operableTrigger.setStartTime(paramDate1);
/* 180 */       operableTrigger.setEndTime(paramDate2);
/* 181 */       operableTrigger.computeFirstFireTime(paramCalendar);
/*     */     } 
/*     */     
/* 184 */     int i = 10000000;
/* 185 */     while (i > 0) {
/* 186 */       i--;
/* 187 */       Date date = operableTrigger.getNextFireTime();
/* 188 */       if (date != null) {
/* 189 */         if (date.before(paramDate1)) {
/* 190 */           operableTrigger.triggered(paramCalendar);
/*     */           continue;
/*     */         } 
/* 193 */         if (date.after(paramDate2)) {
/*     */           break;
/*     */         }
/* 196 */         linkedList.add(date);
/* 197 */         operableTrigger.triggered(paramCalendar);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 203 */     return Collections.unmodifiableList(linkedList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Trigger makeSecondlyTrigger() {
/* 211 */     return makeSecondlyTrigger(1, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeSecondlyTrigger(String paramString) {
/* 216 */     return makeSecondlyTrigger(paramString, 1, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeSecondlyTrigger(int paramInt) {
/* 221 */     return makeSecondlyTrigger(paramInt, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeSecondlyTrigger(int paramInt1, int paramInt2) {
/* 226 */     SimpleTriggerImpl simpleTriggerImpl = new SimpleTriggerImpl();
/* 227 */     simpleTriggerImpl.setRepeatInterval(paramInt1 * 1000L);
/* 228 */     simpleTriggerImpl.setRepeatCount(paramInt2);
/* 229 */     simpleTriggerImpl.setStartTime(new Date());
/* 230 */     return (Trigger)simpleTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeSecondlyTrigger(String paramString, int paramInt1, int paramInt2) {
/* 235 */     SimpleTriggerImpl simpleTriggerImpl = (SimpleTriggerImpl)makeSecondlyTrigger(paramInt1, paramInt2);
/* 236 */     simpleTriggerImpl.setName(paramString);
/* 237 */     return (Trigger)simpleTriggerImpl;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static Trigger makeMinutelyTrigger() {
/* 243 */     return makeMinutelyTrigger(1, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeMinutelyTrigger(String paramString) {
/* 248 */     return makeMinutelyTrigger(paramString, 1, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeMinutelyTrigger(int paramInt) {
/* 253 */     return makeMinutelyTrigger(paramInt, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeMinutelyTrigger(int paramInt1, int paramInt2) {
/* 258 */     SimpleTriggerImpl simpleTriggerImpl = new SimpleTriggerImpl();
/* 259 */     simpleTriggerImpl.setRepeatInterval(paramInt1 * 60000L);
/* 260 */     simpleTriggerImpl.setRepeatCount(paramInt2);
/* 261 */     simpleTriggerImpl.setStartTime(new Date());
/* 262 */     return (Trigger)simpleTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeMinutelyTrigger(String paramString, int paramInt1, int paramInt2) {
/* 267 */     SimpleTriggerImpl simpleTriggerImpl = (SimpleTriggerImpl)makeMinutelyTrigger(paramInt1, paramInt2);
/* 268 */     simpleTriggerImpl.setName(paramString);
/* 269 */     return (Trigger)simpleTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeHourlyTrigger() {
/* 274 */     return makeHourlyTrigger(1, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeHourlyTrigger(String paramString) {
/* 279 */     return makeHourlyTrigger(paramString, 1, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeHourlyTrigger(int paramInt) {
/* 284 */     return makeHourlyTrigger(paramInt, -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeHourlyTrigger(int paramInt1, int paramInt2) {
/* 289 */     SimpleTriggerImpl simpleTriggerImpl = new SimpleTriggerImpl();
/* 290 */     simpleTriggerImpl.setRepeatInterval(paramInt1 * 3600000L);
/* 291 */     simpleTriggerImpl.setRepeatCount(paramInt2);
/* 292 */     simpleTriggerImpl.setStartTime(new Date());
/* 293 */     return (Trigger)simpleTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeHourlyTrigger(String paramString, int paramInt1, int paramInt2) {
/* 298 */     SimpleTriggerImpl simpleTriggerImpl = (SimpleTriggerImpl)makeHourlyTrigger(paramInt1, paramInt2);
/* 299 */     simpleTriggerImpl.setName(paramString);
/* 300 */     return (Trigger)simpleTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeWeeklyTrigger(int paramInt1, int paramInt2, int paramInt3) {
/* 305 */     validateDayOfWeek(paramInt1);
/* 306 */     validateHour(paramInt2);
/* 307 */     validateMinute(paramInt3);
/* 308 */     CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
/*     */     
/*     */     try {
/* 311 */       cronTriggerImpl.setCronExpression("0 " + paramInt3 + " " + paramInt2 + " ? * " + paramInt1);
/*     */     }
/* 313 */     catch (Exception exception) {
/*     */       
/* 315 */       return null;
/*     */     } 
/* 317 */     cronTriggerImpl.setStartTime(new Date());
/* 318 */     return (Trigger)cronTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeWeeklyTrigger(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/* 323 */     CronTriggerImpl cronTriggerImpl = (CronTriggerImpl)makeWeeklyTrigger(paramInt1, paramInt2, paramInt3);
/* 324 */     cronTriggerImpl.setName(paramString);
/* 325 */     return (Trigger)cronTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeMonthlyTrigger(int paramInt1, int paramInt2, int paramInt3) {
/* 330 */     validateDayOfMonth(paramInt1);
/* 331 */     validateHour(paramInt2);
/* 332 */     validateMinute(paramInt3);
/* 333 */     CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
/*     */     
/*     */     try {
/* 336 */       if (paramInt1 != -1) {
/* 337 */         cronTriggerImpl.setCronExpression("0 " + paramInt3 + " " + paramInt2 + " " + paramInt1 + " * ?");
/*     */       } else {
/* 339 */         cronTriggerImpl.setCronExpression("0 " + paramInt3 + " " + paramInt2 + " L * ?");
/*     */       } 
/* 341 */     } catch (Exception exception) {
/*     */       
/* 343 */       return null;
/*     */     } 
/* 345 */     cronTriggerImpl.setStartTime(new Date());
/* 346 */     return (Trigger)cronTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Trigger makeMonthlyTrigger(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/* 351 */     CronTriggerImpl cronTriggerImpl = (CronTriggerImpl)makeMonthlyTrigger(paramInt1, paramInt2, paramInt3);
/* 352 */     cronTriggerImpl.setName(paramString);
/* 353 */     return (Trigger)cronTriggerImpl;
/*     */   }
/*     */ 
/*     */   
/*     */   private static void validateDayOfWeek(int paramInt) {
/* 358 */     if (paramInt < 1 || paramInt > 7) {
/* 359 */       throw new IllegalArgumentException("Invalid day of week.");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void validateDayOfMonth(int paramInt) {
/* 366 */     if ((paramInt < 1 || paramInt > 31) && paramInt != -1) {
/* 367 */       throw new IllegalArgumentException("Invalid day of month.");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void validateMonth(int paramInt) {
/* 374 */     if (paramInt < 1 || paramInt > 12) {
/* 375 */       throw new IllegalArgumentException("Invalid month (must be >= 1 and <= 12.");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void validateYear(int paramInt) {
/* 382 */     if (paramInt < 1970 || paramInt > 2099) {
/* 383 */       throw new IllegalArgumentException("Invalid year (must be >= 1970 and <= 2099.");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void validateHour(int paramInt) {
/* 390 */     if (paramInt < 0 || paramInt > 23) {
/* 391 */       throw new IllegalArgumentException("Invalid hour (must be >= 0 and <= 23).");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void validateMinute(int paramInt) {
/* 398 */     if (paramInt < 0 || paramInt > 59) {
/* 399 */       throw new IllegalArgumentException("Invalid minute (must be >= 0 and <= 59).");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void validateSecond(int paramInt) {
/* 406 */     if (paramInt < 0 || paramInt > 59)
/* 407 */       throw new IllegalArgumentException("Invalid second (must be >= 0 and <= 59)."); 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/quartz/TriggerUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */