/*     */ package org.hsqldb.server;
/*     */ 
/*     */ import java.io.DataInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.io.PrintWriter;
/*     */ import javax.servlet.ServletConfig;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.hsqldb.DatabaseManager;
/*     */ import org.hsqldb.DatabaseURL;
/*     */ import org.hsqldb.HsqlException;
/*     */ import org.hsqldb.Session;
/*     */ import org.hsqldb.SessionInterface;
/*     */ import org.hsqldb.lib.DataOutputStream;
/*     */ import org.hsqldb.lib.HsqlByteArrayOutputStream;
/*     */ import org.hsqldb.persist.HsqlProperties;
/*     */ import org.hsqldb.result.Result;
/*     */ import org.hsqldb.rowio.RowInputBinary;
/*     */ import org.hsqldb.rowio.RowOutputBinary;
/*     */ import org.hsqldb.rowio.RowOutputInterface;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class Servlet
/*     */   extends HttpServlet
/*     */ {
/*     */   private static final int BUFFER_SIZE = 256;
/*     */   private String dbType;
/*     */   private String dbPath;
/*     */   private String errorStr;
/*     */   private RowOutputBinary rowOut;
/*     */   private RowInputBinary rowIn;
/*     */   private int iQueries;
/*  37 */   private static long lModified = 0L;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init(ServletConfig paramServletConfig) {
/*     */     try {
/*  44 */       super.init(paramServletConfig);
/*  45 */       this.rowOut = new RowOutputBinary(256, 1);
/*  46 */       this.rowIn = new RowInputBinary(this.rowOut);
/*  47 */     } catch (ServletException servletException) {
/*  48 */       log(servletException.toString());
/*     */     } 
/*     */     
/*  51 */     String str1 = getInitParameter("hsqldb.server.database");
/*  52 */     if (str1 == null) {
/*  53 */       str1 = ".";
/*     */     }
/*     */     
/*  56 */     String str2 = getInitParameter("hsqldb.server.use_web-inf_path");
/*  57 */     if (!str1.equals(".") && "true".equalsIgnoreCase(str2)) {
/*  58 */       str1 = getServletContext().getRealPath("/") + "WEB-INF/" + str1;
/*     */     }
/*     */     
/*  61 */     HsqlProperties hsqlProperties = DatabaseURL.parseURL(str1, false, false);
/*  62 */     log("Database filename = " + str1);
/*  63 */     if (hsqlProperties == null) {
/*  64 */       this.errorStr = "Bad Database name";
/*     */     } else {
/*  66 */       this.dbPath = hsqlProperties.getProperty("database");
/*  67 */       this.dbType = hsqlProperties.getProperty("connection_type");
/*     */       
/*     */       try {
/*  70 */         DatabaseManager.getDatabase(this.dbType, this.dbPath, hsqlProperties);
/*  71 */       } catch (HsqlException hsqlException) {
/*  72 */         this.errorStr = hsqlException.getMessage();
/*     */       } 
/*     */     } 
/*     */     
/*  76 */     if (this.errorStr == null) {
/*  77 */       log("Initialization completed.");
/*     */     } else {
/*  79 */       log("Database could not be initialised.");
/*  80 */       log(this.errorStr);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   protected long getLastModified(HttpServletRequest paramHttpServletRequest) {
/*  86 */     return lModified++;
/*     */   }
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException, ServletException {
/*  91 */     User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/*  92 */     if (user == null) {
/*     */       return;
/*     */     }
/*     */     
/*  96 */     String str = paramHttpServletRequest.getQueryString();
/*  97 */     if (str == null || str.length() == 0) {
/*  98 */       paramHttpServletResponse.setContentType("text/html");
/*  99 */       paramHttpServletResponse.setHeader("Pragma", "no-cache");
/* 100 */       PrintWriter printWriter = paramHttpServletResponse.getWriter();
/* 101 */       printWriter.println("<html><head><title>HSQL Database Engine Servlet</title>");
/* 102 */       printWriter.println("</head><body><h1>HSQL Database Engine Servlet</h1>");
/* 103 */       printWriter.println("The servlet is running.<p>");
/* 104 */       if (this.errorStr == null) {
/* 105 */         printWriter.println("The database is also running.<p>");
/* 106 */         printWriter.println("Database name: " + this.dbType + this.dbPath + "<p>");
/* 107 */         printWriter.println("Queries processed: " + this.iQueries + "<p>");
/*     */       } else {
/* 109 */         printWriter.println("<h2>The database is not running!</h2>");
/* 110 */         printWriter.println("The error message is:<p>");
/* 111 */         printWriter.println(this.errorStr);
/*     */       } 
/*     */       
/* 114 */       printWriter.println("</body></html>");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException, ServletException {
/* 121 */     User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/* 122 */     if (user == null) {
/*     */       return;
/*     */     }
/*     */     
/* 126 */     synchronized (this) {
/* 127 */       DataInputStream dataInputStream = null;
/* 128 */       DataOutputStream dataOutputStream = null;
/*     */       try {
/*     */         Result result2;
/* 131 */         dataInputStream = new DataInputStream((InputStream)paramHttpServletRequest.getInputStream());
/* 132 */         int i = dataInputStream.readInt();
/* 133 */         long l = dataInputStream.readLong();
/* 134 */         byte b = dataInputStream.readByte();
/* 135 */         Session session = DatabaseManager.getSession(i, l);
/* 136 */         Result result1 = Result.newResult(session, b, dataInputStream, this.rowIn);
/* 137 */         result1.setDatabaseId(i);
/* 138 */         result1.setSessionId(l);
/* 139 */         int j = result1.getType();
/*     */         
/* 141 */         if (j == 31) {
/*     */           try {
/* 143 */             session = DatabaseManager.newSession(this.dbType, this.dbPath, result1.getMainString(), result1.getSubString(), new HsqlProperties(), result1.getZoneString(), result1.getUpdateCount());
/* 144 */             result1.readAdditionalResults((SessionInterface)null, dataInputStream, this.rowIn);
/* 145 */             result2 = Result.newConnectionAcknowledgeResponse(session.getDatabase(), session.getId(), session.getDatabase().getDatabaseID());
/* 146 */           } catch (HsqlException hsqlException) {
/* 147 */             result2 = Result.newErrorResult((Throwable)hsqlException);
/*     */           } 
/*     */         } else {
/* 150 */           if (j == 32 || j == 10) {
/* 151 */             paramHttpServletResponse.setHeader("Cache-Control", "no-cache");
/* 152 */             paramHttpServletResponse.setContentType("application/octet-stream");
/* 153 */             paramHttpServletResponse.setContentLength(6);
/* 154 */             dataOutputStream = new DataOutputStream((OutputStream)paramHttpServletResponse.getOutputStream());
/* 155 */             dataOutputStream.writeByte(32);
/* 156 */             dataOutputStream.writeInt(4);
/* 157 */             dataOutputStream.writeByte(0);
/* 158 */             dataOutputStream.close();
/*     */             
/*     */             return;
/*     */           } 
/* 162 */           int k = result1.getDatabaseId();
/* 163 */           long l1 = result1.getSessionId();
/* 164 */           session = DatabaseManager.getSession(k, l1);
/* 165 */           result1.readLobResults((SessionInterface)session, dataInputStream, this.rowIn);
/* 166 */           result2 = session.execute(result1);
/*     */         } 
/*     */         
/* 169 */         HsqlByteArrayOutputStream hsqlByteArrayOutputStream = new HsqlByteArrayOutputStream();
/* 170 */         DataOutputStream dataOutputStream1 = new DataOutputStream((OutputStream)hsqlByteArrayOutputStream);
/* 171 */         result2.write((SessionInterface)session, dataOutputStream1, (RowOutputInterface)this.rowOut);
/* 172 */         paramHttpServletResponse.setHeader("Cache-Control", "no-cache");
/* 173 */         paramHttpServletResponse.setContentType("application/octet-stream");
/* 174 */         paramHttpServletResponse.setContentLength(hsqlByteArrayOutputStream.size());
/* 175 */         dataOutputStream = new DataOutputStream((OutputStream)paramHttpServletResponse.getOutputStream());
/* 176 */         hsqlByteArrayOutputStream.writeTo((OutputStream)dataOutputStream);
/* 177 */         this.iQueries++;
/* 178 */       } catch (HsqlException hsqlException) {
/*     */       
/*     */       } finally {
/* 181 */         if (dataOutputStream != null) {
/* 182 */           dataOutputStream.close();
/*     */         }
/*     */         
/* 185 */         if (dataInputStream != null)
/* 186 */           dataInputStream.close(); 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/hsqldb/server/Servlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */