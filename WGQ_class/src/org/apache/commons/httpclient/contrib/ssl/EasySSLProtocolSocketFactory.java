/*     */ package org.apache.commons.httpclient.contrib.ssl;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.net.InetAddress;
/*     */ import java.net.InetSocketAddress;
/*     */ import java.net.Socket;
/*     */ import java.net.UnknownHostException;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import javax.net.ssl.SSLSocketFactory;
/*     */ import javax.net.ssl.TrustManager;
/*     */ import org.apache.commons.httpclient.ConnectTimeoutException;
/*     */ import org.apache.commons.httpclient.HttpClientError;
/*     */ import org.apache.commons.httpclient.params.HttpConnectionParams;
/*     */ import org.apache.commons.httpclient.protocol.ProtocolSocketFactory;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EasySSLProtocolSocketFactory
/*     */   implements ProtocolSocketFactory
/*     */ {
/* 101 */   private static final Log LOG = LogFactory.getLog(EasySSLProtocolSocketFactory.class);
/*     */   
/* 103 */   private SSLContext sslcontext = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static SSLContext createEasySSLContext() {
/*     */     try {
/* 114 */       SSLContext sSLContext = SSLContext.getInstance("SSL");
/* 115 */       sSLContext.init(null, new TrustManager[] { new EasyX509TrustManager(null) }, null);
/*     */ 
/*     */ 
/*     */       
/* 119 */       return sSLContext;
/* 120 */     } catch (Exception exception) {
/* 121 */       LOG.error(exception.getMessage(), exception);
/* 122 */       throw new HttpClientError(exception.toString());
/*     */     } 
/*     */   }
/*     */   
/*     */   private SSLContext getSSLContext() {
/* 127 */     if (this.sslcontext == null) {
/* 128 */       this.sslcontext = createEasySSLContext();
/*     */     }
/* 130 */     return this.sslcontext;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Socket createSocket(String paramString, int paramInt1, InetAddress paramInetAddress, int paramInt2) throws IOException, UnknownHostException {
/* 143 */     return getSSLContext().getSocketFactory().createSocket(paramString, paramInt1, paramInetAddress, paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Socket createSocket(String paramString, int paramInt1, InetAddress paramInetAddress, int paramInt2, HttpConnectionParams paramHttpConnectionParams) throws IOException, UnknownHostException, ConnectTimeoutException {
/* 179 */     if (paramHttpConnectionParams == null) {
/* 180 */       throw new IllegalArgumentException("Parameters may not be null");
/*     */     }
/* 182 */     int i = paramHttpConnectionParams.getConnectionTimeout();
/* 183 */     SSLSocketFactory sSLSocketFactory = getSSLContext().getSocketFactory();
/* 184 */     if (i == 0) {
/* 185 */       return sSLSocketFactory.createSocket(paramString, paramInt1, paramInetAddress, paramInt2);
/*     */     }
/* 187 */     Socket socket = sSLSocketFactory.createSocket();
/* 188 */     InetSocketAddress inetSocketAddress1 = new InetSocketAddress(paramInetAddress, paramInt2);
/* 189 */     InetSocketAddress inetSocketAddress2 = new InetSocketAddress(paramString, paramInt1);
/* 190 */     socket.bind(inetSocketAddress1);
/* 191 */     socket.connect(inetSocketAddress2, i);
/* 192 */     return socket;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Socket createSocket(String paramString, int paramInt) throws IOException, UnknownHostException {
/* 201 */     return getSSLContext().getSocketFactory().createSocket(paramString, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean equals(Object paramObject) {
/* 208 */     return (paramObject != null && paramObject.getClass().equals(EasySSLProtocolSocketFactory.class));
/*     */   }
/*     */   
/*     */   public int hashCode() {
/* 212 */     return EasySSLProtocolSocketFactory.class.hashCode();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/apache/commons/httpclient/contrib/ssl/EasySSLProtocolSocketFactory.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */