/*     */ package org.apache.commons.httpclient.contrib.ssl;
/*     */ 
/*     */ import java.security.KeyStore;
/*     */ import java.security.KeyStoreException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.security.cert.X509Certificate;
/*     */ import javax.net.ssl.TrustManager;
/*     */ import javax.net.ssl.TrustManagerFactory;
/*     */ import javax.net.ssl.X509TrustManager;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EasyX509TrustManager
/*     */   implements X509TrustManager
/*     */ {
/*  65 */   private X509TrustManager standardTrustManager = null;
/*     */ 
/*     */   
/*  68 */   private static final Log LOG = LogFactory.getLog(EasyX509TrustManager.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public EasyX509TrustManager(KeyStore paramKeyStore) throws NoSuchAlgorithmException, KeyStoreException {
/*  75 */     TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
/*  76 */     trustManagerFactory.init(paramKeyStore);
/*  77 */     TrustManager[] arrayOfTrustManager = trustManagerFactory.getTrustManagers();
/*  78 */     if (arrayOfTrustManager.length == 0) {
/*  79 */       throw new NoSuchAlgorithmException("no trust manager found");
/*     */     }
/*  81 */     this.standardTrustManager = (X509TrustManager)arrayOfTrustManager[0];
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void checkClientTrusted(X509Certificate[] paramArrayOfX509Certificate, String paramString) throws CertificateException {
/*  88 */     this.standardTrustManager.checkClientTrusted(paramArrayOfX509Certificate, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void checkServerTrusted(X509Certificate[] paramArrayOfX509Certificate, String paramString) throws CertificateException {
/*  95 */     if (paramArrayOfX509Certificate != null && LOG.isDebugEnabled()) {
/*  96 */       LOG.debug("Server certificate chain:");
/*  97 */       for (byte b = 0; b < paramArrayOfX509Certificate.length; b++) {
/*  98 */         LOG.debug("X509Certificate[" + b + "]=" + paramArrayOfX509Certificate[b]);
/*     */       }
/*     */     } 
/* 101 */     if (paramArrayOfX509Certificate != null && paramArrayOfX509Certificate.length == 1) {
/* 102 */       paramArrayOfX509Certificate[0].checkValidity();
/*     */     } else {
/* 104 */       this.standardTrustManager.checkServerTrusted(paramArrayOfX509Certificate, paramString);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public X509Certificate[] getAcceptedIssuers() {
/* 112 */     return this.standardTrustManager.getAcceptedIssuers();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/apache/commons/httpclient/contrib/ssl/EasyX509TrustManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */