/*    */ package org.artofsolving.jodconverter;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.io.FilenameUtils;
/*    */ import org.artofsolving.jodconverter.document.DefaultDocumentFormatRegistry;
/*    */ import org.artofsolving.jodconverter.document.DocumentFormat;
/*    */ import org.artofsolving.jodconverter.document.DocumentFormatRegistry;
/*    */ import org.artofsolving.jodconverter.office.OfficeException;
/*    */ import org.artofsolving.jodconverter.office.OfficeManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OfficeDocumentConverter
/*    */ {
/*    */   private final OfficeManager officeManager;
/*    */   private final DocumentFormatRegistry formatRegistry;
/* 39 */   private Map<String, ?> defaultLoadProperties = createDefaultLoadProperties();
/*    */   
/*    */   public OfficeDocumentConverter(OfficeManager paramOfficeManager) {
/* 42 */     this(paramOfficeManager, (DocumentFormatRegistry)new DefaultDocumentFormatRegistry());
/*    */   }
/*    */   
/*    */   public OfficeDocumentConverter(OfficeManager paramOfficeManager, DocumentFormatRegistry paramDocumentFormatRegistry) {
/* 46 */     this.officeManager = paramOfficeManager;
/* 47 */     this.formatRegistry = paramDocumentFormatRegistry;
/*    */   }
/*    */   
/*    */   private Map<String, Object> createDefaultLoadProperties() {
/* 51 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 52 */     hashMap.put("Hidden", Boolean.valueOf(true));
/* 53 */     hashMap.put("ReadOnly", Boolean.valueOf(true));
/* 54 */     hashMap.put("UpdateDocMode", Short.valueOf((short)1));
/* 55 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public void setDefaultLoadProperties(Map<String, ?> paramMap) {
/* 59 */     this.defaultLoadProperties = paramMap;
/*    */   }
/*    */   
/*    */   public DocumentFormatRegistry getFormatRegistry() {
/* 63 */     return this.formatRegistry;
/*    */   }
/*    */   
/*    */   public void convert(File paramFile1, File paramFile2) throws OfficeException {
/* 67 */     String str = FilenameUtils.getExtension(paramFile2.getName());
/* 68 */     DocumentFormat documentFormat = this.formatRegistry.getFormatByExtension(str);
/* 69 */     convert(paramFile1, paramFile2, documentFormat);
/*    */   }
/*    */   
/*    */   public void convert(File paramFile1, File paramFile2, DocumentFormat paramDocumentFormat) throws OfficeException {
/* 73 */     String str = FilenameUtils.getExtension(paramFile1.getName());
/* 74 */     DocumentFormat documentFormat = this.formatRegistry.getFormatByExtension(str);
/* 75 */     StandardConversionTask standardConversionTask = new StandardConversionTask(paramFile1, paramFile2, paramDocumentFormat);
/* 76 */     standardConversionTask.setDefaultLoadProperties(this.defaultLoadProperties);
/* 77 */     standardConversionTask.setInputFormat(documentFormat);
/* 78 */     this.officeManager.execute(standardConversionTask);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/OfficeDocumentConverter.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */