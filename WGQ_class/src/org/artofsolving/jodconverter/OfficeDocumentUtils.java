/*    */ package org.artofsolving.jodconverter;
/*    */ 
/*    */ import com.sun.star.lang.XComponent;
/*    */ import com.sun.star.lang.XServiceInfo;
/*    */ import org.artofsolving.jodconverter.document.DocumentFamily;
/*    */ import org.artofsolving.jodconverter.office.OfficeException;
/*    */ import org.artofsolving.jodconverter.office.OfficeUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class OfficeDocumentUtils
/*    */ {
/*    */   private OfficeDocumentUtils() {
/* 33 */     throw new AssertionError("utility class must not be instantiated");
/*    */   }
/*    */   
/*    */   public static DocumentFamily getDocumentFamily(XComponent paramXComponent) throws OfficeException {
/* 37 */     XServiceInfo xServiceInfo = (XServiceInfo)OfficeUtils.cast(XServiceInfo.class, paramXComponent);
/* 38 */     if (xServiceInfo.supportsService("com.sun.star.text.GenericTextDocument"))
/*    */     {
/*    */       
/* 41 */       return DocumentFamily.TEXT; } 
/* 42 */     if (xServiceInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument"))
/* 43 */       return DocumentFamily.SPREADSHEET; 
/* 44 */     if (xServiceInfo.supportsService("com.sun.star.presentation.PresentationDocument"))
/* 45 */       return DocumentFamily.PRESENTATION; 
/* 46 */     if (xServiceInfo.supportsService("com.sun.star.drawing.DrawingDocument")) {
/* 47 */       return DocumentFamily.DRAWING;
/*    */     }
/* 49 */     throw new OfficeException("document of unknown family: " + xServiceInfo.getImplementationName());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/OfficeDocumentUtils.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */