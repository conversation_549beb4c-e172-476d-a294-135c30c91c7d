/*     */ package org.artofsolving.jodconverter.cli;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import org.apache.commons.cli.CommandLine;
/*     */ import org.apache.commons.cli.HelpFormatter;
/*     */ import org.apache.commons.cli.Option;
/*     */ import org.apache.commons.cli.Options;
/*     */ import org.apache.commons.cli.ParseException;
/*     */ import org.apache.commons.cli.PosixParser;
/*     */ import org.apache.commons.io.FileUtils;
/*     */ import org.apache.commons.io.FilenameUtils;
/*     */ import org.artofsolving.jodconverter.OfficeDocumentConverter;
/*     */ import org.artofsolving.jodconverter.document.DefaultDocumentFormatRegistry;
/*     */ import org.artofsolving.jodconverter.document.DocumentFormatRegistry;
/*     */ import org.artofsolving.jodconverter.document.JsonDocumentFormatRegistry;
/*     */ import org.artofsolving.jodconverter.office.DefaultOfficeManagerConfiguration;
/*     */ import org.artofsolving.jodconverter.office.OfficeManager;
/*     */ import org.json.JSONException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Convert
/*     */ {
/*     */   public static final int STATUS_OK = 0;
/*     */   public static final int STATUS_MISSING_INPUT_FILE = 1;
/*     */   public static final int STATUS_INVALID_ARGUMENTS = 255;
/*  50 */   private static final Option OPTION_OUTPUT_FORMAT = new Option("o", "output-format", true, "output format (e.g. pdf)");
/*  51 */   private static final Option OPTION_PORT = new Option("p", "port", true, "office socket port (optional; defaults to 2002)");
/*  52 */   private static final Option OPTION_REGISTRY = new Option("r", "registry", true, "document formats registry configuration file (optional)");
/*  53 */   private static final Option OPTION_TIMEOUT = new Option("t", "timeout", true, "maximum conversion time in seconds (optional; defaults to 120)");
/*  54 */   private static final Option OPTION_USER_PROFILE = new Option("u", "user-profile", true, "use settings from the given user installation dir (optional)");
/*  55 */   private static final Options OPTIONS = initOptions();
/*     */   
/*     */   private static final int DEFAULT_OFFICE_PORT = 2002;
/*     */   
/*     */   private static Options initOptions() {
/*  60 */     Options options = new Options();
/*  61 */     options.addOption(OPTION_OUTPUT_FORMAT);
/*  62 */     options.addOption(OPTION_PORT);
/*  63 */     options.addOption(OPTION_REGISTRY);
/*  64 */     options.addOption(OPTION_TIMEOUT);
/*  65 */     options.addOption(OPTION_USER_PROFILE);
/*  66 */     return options;
/*     */   }
/*     */   public static void main(String[] paramArrayOfString) throws ParseException, JSONException, IOException {
/*     */     DefaultDocumentFormatRegistry defaultDocumentFormatRegistry;
/*  70 */     PosixParser posixParser = new PosixParser();
/*  71 */     CommandLine commandLine = posixParser.parse(OPTIONS, paramArrayOfString);
/*     */     
/*  73 */     String str = null;
/*  74 */     if (commandLine.hasOption(OPTION_OUTPUT_FORMAT.getOpt())) {
/*  75 */       str = commandLine.getOptionValue(OPTION_OUTPUT_FORMAT.getOpt());
/*     */     }
/*     */     
/*  78 */     int i = 2002;
/*  79 */     if (commandLine.hasOption(OPTION_PORT.getOpt())) {
/*  80 */       i = Integer.parseInt(commandLine.getOptionValue(OPTION_PORT.getOpt()));
/*     */     }
/*     */     
/*  83 */     String[] arrayOfString = commandLine.getArgs();
/*  84 */     if ((str == null && arrayOfString.length != 2) || arrayOfString.length < 1) {
/*  85 */       String str1 = "java -jar jodconverter-core.jar [options] input-file output-file\nor [options] -o output-format input-file [input-file...]";
/*     */       
/*  87 */       HelpFormatter helpFormatter = new HelpFormatter();
/*  88 */       helpFormatter.printHelp(str1, OPTIONS);
/*  89 */       System.exit(255);
/*     */     } 
/*     */ 
/*     */     
/*  93 */     if (commandLine.hasOption(OPTION_REGISTRY.getOpt())) {
/*  94 */       File file = new File(commandLine.getOptionValue(OPTION_REGISTRY.getOpt()));
/*  95 */       JsonDocumentFormatRegistry jsonDocumentFormatRegistry = new JsonDocumentFormatRegistry(FileUtils.readFileToString(file));
/*     */     } else {
/*  97 */       defaultDocumentFormatRegistry = new DefaultDocumentFormatRegistry();
/*     */     } 
/*     */     
/* 100 */     DefaultOfficeManagerConfiguration defaultOfficeManagerConfiguration = new DefaultOfficeManagerConfiguration();
/* 101 */     defaultOfficeManagerConfiguration.setPortNumber(i);
/* 102 */     if (commandLine.hasOption(OPTION_TIMEOUT.getOpt())) {
/* 103 */       int j = Integer.parseInt(commandLine.getOptionValue(OPTION_TIMEOUT.getOpt()));
/* 104 */       defaultOfficeManagerConfiguration.setTaskExecutionTimeout((j * 1000));
/*     */     } 
/* 106 */     if (commandLine.hasOption(OPTION_USER_PROFILE.getOpt())) {
/* 107 */       String str1 = commandLine.getOptionValue(OPTION_USER_PROFILE.getOpt());
/* 108 */       defaultOfficeManagerConfiguration.setTemplateProfileDir(new File(str1));
/*     */     } 
/*     */     
/* 111 */     OfficeManager officeManager = defaultOfficeManagerConfiguration.buildOfficeManager();
/* 112 */     officeManager.start();
/* 113 */     OfficeDocumentConverter officeDocumentConverter = new OfficeDocumentConverter(officeManager, (DocumentFormatRegistry)defaultDocumentFormatRegistry);
/*     */     try {
/* 115 */       if (str == null) {
/* 116 */         File file1 = new File(arrayOfString[0]);
/* 117 */         File file2 = new File(arrayOfString[1]);
/* 118 */         officeDocumentConverter.convert(file1, file2);
/*     */       } else {
/* 120 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 121 */           File file1 = new File(arrayOfString[b]);
/* 122 */           String str1 = FilenameUtils.getBaseName(arrayOfString[b]) + "." + str;
/* 123 */           File file2 = new File(FilenameUtils.getFullPath(arrayOfString[b]) + str1);
/* 124 */           officeDocumentConverter.convert(file1, file2);
/*     */         } 
/*     */       } 
/*     */     } finally {
/* 128 */       officeManager.stop();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/cli/Convert.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */