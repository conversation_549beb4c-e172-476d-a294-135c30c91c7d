/*     */ package org.artofsolving.jodconverter;
/*     */ 
/*     */ import com.sun.star.frame.XComponentLoader;
/*     */ import com.sun.star.frame.XStorable;
/*     */ import com.sun.star.io.IOException;
/*     */ import com.sun.star.lang.IllegalArgumentException;
/*     */ import com.sun.star.lang.XComponent;
/*     */ import com.sun.star.task.ErrorCodeIOException;
/*     */ import com.sun.star.util.CloseVetoException;
/*     */ import com.sun.star.util.XCloseable;
/*     */ import java.io.File;
/*     */ import java.util.Map;
/*     */ import org.artofsolving.jodconverter.office.OfficeContext;
/*     */ import org.artofsolving.jodconverter.office.OfficeException;
/*     */ import org.artofsolving.jodconverter.office.OfficeTask;
/*     */ import org.artofsolving.jodconverter.office.OfficeUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractConversionTask
/*     */   implements OfficeTask
/*     */ {
/*     */   private final File inputFile;
/*     */   private final File outputFile;
/*     */   
/*     */   public AbstractConversionTask(File paramFile1, File paramFile2) {
/*  49 */     this.inputFile = paramFile1;
/*  50 */     this.outputFile = paramFile2;
/*     */   }
/*     */   
/*     */   protected abstract Map<String, ?> getLoadProperties(File paramFile);
/*     */   
/*     */   protected abstract Map<String, ?> getStoreProperties(File paramFile, XComponent paramXComponent);
/*     */   
/*     */   public void execute(OfficeContext paramOfficeContext) throws OfficeException {
/*  58 */     XComponent xComponent = null;
/*     */ 
/*     */     
/*     */     try {
/*  62 */       xComponent = loadDocument(paramOfficeContext, this.inputFile);
/*  63 */       modifyDocument(xComponent);
/*  64 */       storeDocument(xComponent, this.outputFile);
/*  65 */     } catch (OfficeException officeException) {
/*     */       
/*  67 */       throw officeException;
/*  68 */     } catch (Exception exception) {
/*     */       
/*  70 */       throw new OfficeException("conversion failed", exception);
/*     */     } finally {
/*     */       
/*  73 */       if (xComponent != null) {
/*  74 */         XCloseable xCloseable = (XCloseable)OfficeUtils.cast(XCloseable.class, xComponent);
/*  75 */         if (xCloseable != null) {
/*     */           try {
/*  77 */             xComponent.dispose();
/*     */             
/*  79 */             xCloseable.close(true);
/*     */           }
/*  81 */           catch (CloseVetoException closeVetoException) {}
/*     */         
/*     */         }
/*     */         else {
/*     */           
/*  86 */           xComponent.dispose();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private XComponent loadDocument(OfficeContext paramOfficeContext, File paramFile) throws OfficeException {
/*  95 */     if (!paramFile.exists()) {
/*  96 */       throw new OfficeException("input document not found");
/*     */     }
/*  98 */     XComponentLoader xComponentLoader = (XComponentLoader)OfficeUtils.cast(XComponentLoader.class, paramOfficeContext.getService("com.sun.star.frame.Desktop"));
/*  99 */     Map<String, ?> map = getLoadProperties(paramFile);
/* 100 */     XComponent xComponent = null;
/*     */     try {
/* 102 */       xComponent = xComponentLoader.loadComponentFromURL(OfficeUtils.toUrl(paramFile), "_blank", 0, OfficeUtils.toUnoProperties(map));
/* 103 */     } catch (IllegalArgumentException illegalArgumentException) {
/* 104 */       throw new OfficeException("could not load document: " + paramFile.getName(), illegalArgumentException);
/* 105 */     } catch (ErrorCodeIOException errorCodeIOException) {
/* 106 */       throw new OfficeException("could not load document: " + paramFile.getName() + "; errorCode: " + errorCodeIOException.ErrCode, errorCodeIOException);
/* 107 */     } catch (IOException iOException) {
/* 108 */       throw new OfficeException("could not load document: " + paramFile.getName(), iOException);
/*     */     } 
/* 110 */     if (xComponent == null) {
/* 111 */       throw new OfficeException("could not load document: " + paramFile.getName());
/*     */     }
/* 113 */     return xComponent;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void modifyDocument(XComponent paramXComponent) throws OfficeException {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void storeDocument(XComponent paramXComponent, File paramFile) throws OfficeException {
/* 130 */     Map<String, ?> map = getStoreProperties(paramFile, paramXComponent);
/* 131 */     if (map == null) {
/* 132 */       throw new OfficeException("unsupported conversion");
/*     */     }
/*     */     try {
/* 135 */       ((XStorable)OfficeUtils.cast(XStorable.class, paramXComponent)).storeToURL(OfficeUtils.toUrl(paramFile), OfficeUtils.toUnoProperties(map));
/* 136 */     } catch (ErrorCodeIOException errorCodeIOException) {
/* 137 */       throw new OfficeException("could not store document: " + paramFile.getName() + "; errorCode: " + errorCodeIOException.ErrCode, errorCodeIOException);
/* 138 */     } catch (IOException iOException) {
/* 139 */       throw new OfficeException("could not store document: " + paramFile.getName(), iOException);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/AbstractConversionTask.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */