/*    */ package org.artofsolving.jodconverter.document;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.io.InputStream;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.json.JSONArray;
/*    */ import org.json.JSONException;
/*    */ import org.json.JSONObject;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JsonDocumentFormatRegistry
/*    */   extends SimpleDocumentFormatRegistry
/*    */ {
/*    */   public JsonDocumentFormatRegistry(InputStream paramInputStream) throws JSONException, IOException {
/* 34 */     readJsonArray(IOUtils.toString(paramInputStream));
/*    */   }
/*    */   
/*    */   public JsonDocumentFormatRegistry(String paramString) throws JSONException {
/* 38 */     readJsonArray(paramString);
/*    */   }
/*    */   
/*    */   private void readJsonArray(String paramString) throws JSONException {
/* 42 */     JSONArray jSONArray = new JSONArray(paramString);
/* 43 */     for (byte b = 0; b < jSONArray.length(); b++) {
/* 44 */       JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 45 */       DocumentFormat documentFormat = new DocumentFormat();
/* 46 */       documentFormat.setName(jSONObject.getString("name"));
/* 47 */       documentFormat.setExtension(jSONObject.getString("extension"));
/* 48 */       documentFormat.setMediaType(jSONObject.getString("mediaType"));
/* 49 */       if (jSONObject.has("inputFamily")) {
/* 50 */         documentFormat.setInputFamily(DocumentFamily.valueOf(jSONObject.getString("inputFamily")));
/*    */       }
/* 52 */       if (jSONObject.has("loadProperties")) {
/* 53 */         documentFormat.setLoadProperties(toJavaMap(jSONObject.getJSONObject("loadProperties")));
/*    */       }
/* 55 */       if (jSONObject.has("storePropertiesByFamily")) {
/* 56 */         JSONObject jSONObject1 = jSONObject.getJSONObject("storePropertiesByFamily");
/* 57 */         for (String str : JSONObject.getNames(jSONObject1)) {
/* 58 */           Map<String, ?> map = toJavaMap(jSONObject1.getJSONObject(str));
/* 59 */           documentFormat.setStoreProperties(DocumentFamily.valueOf(str), map);
/*    */         } 
/*    */       } 
/* 62 */       addFormat(documentFormat);
/*    */     } 
/*    */   }
/*    */   
/*    */   private Map<String, ?> toJavaMap(JSONObject paramJSONObject) throws JSONException {
/* 67 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 68 */     for (String str : JSONObject.getNames(paramJSONObject)) {
/* 69 */       Object object = paramJSONObject.get(str);
/* 70 */       if (object instanceof JSONObject) {
/* 71 */         hashMap.put(str, toJavaMap((JSONObject)object));
/*    */       } else {
/* 73 */         hashMap.put(str, object);
/*    */       } 
/*    */     } 
/* 76 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/document/JsonDocumentFormatRegistry.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */