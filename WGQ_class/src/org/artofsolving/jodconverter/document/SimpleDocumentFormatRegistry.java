/*    */ package org.artofsolving.jodconverter.document;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashSet;
/*    */ import java.util.List;
/*    */ import java.util.Set;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SimpleDocumentFormatRegistry
/*    */   implements DocumentFormatRegistry
/*    */ {
/* 28 */   private List<DocumentFormat> documentFormats = new ArrayList<DocumentFormat>();
/*    */   
/*    */   public void addFormat(DocumentFormat paramDocumentFormat) {
/* 31 */     this.documentFormats.add(paramDocumentFormat);
/*    */   }
/*    */   
/*    */   public DocumentFormat getFormatByExtension(String paramString) {
/* 35 */     if (paramString == null) {
/* 36 */       return null;
/*    */     }
/* 38 */     String str = paramString.toLowerCase();
/*    */     
/* 40 */     for (DocumentFormat documentFormat : this.documentFormats) {
/* 41 */       if (documentFormat.getExtension().equals(str)) {
/* 42 */         return documentFormat;
/*    */       }
/*    */     } 
/* 45 */     return null;
/*    */   }
/*    */   
/*    */   public DocumentFormat getFormatByMediaType(String paramString) {
/* 49 */     if (paramString == null) {
/* 50 */       return null;
/*    */     }
/*    */     
/* 53 */     for (DocumentFormat documentFormat : this.documentFormats) {
/* 54 */       if (documentFormat.getMediaType().equals(paramString)) {
/* 55 */         return documentFormat;
/*    */       }
/*    */     } 
/* 58 */     return null;
/*    */   }
/*    */   
/*    */   public Set<DocumentFormat> getOutputFormats(DocumentFamily paramDocumentFamily) {
/* 62 */     HashSet<DocumentFormat> hashSet = new HashSet();
/* 63 */     for (DocumentFormat documentFormat : this.documentFormats) {
/* 64 */       if (documentFormat.getStoreProperties(paramDocumentFamily) != null) {
/* 65 */         hashSet.add(documentFormat);
/*    */       }
/*    */     } 
/* 68 */     return hashSet;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/document/SimpleDocumentFormatRegistry.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */