/*     */ package org.artofsolving.jodconverter.document;
/*     */ 
/*     */ import java.util.Collections;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DefaultDocumentFormatRegistry
/*     */   extends SimpleDocumentFormatRegistry
/*     */ {
/*     */   public DefaultDocumentFormatRegistry() {
/*  28 */     DocumentFormat documentFormat1 = new DocumentFormat("Portable Document Format", "pdf", "application/pdf");
/*  29 */     documentFormat1.setStoreProperties(DocumentFamily.TEXT, Collections.singletonMap("FilterName", "writer_pdf_Export"));
/*  30 */     documentFormat1.setStoreProperties(DocumentFamily.SPREADSHEET, Collections.singletonMap("FilterName", "calc_pdf_Export"));
/*  31 */     documentFormat1.setStoreProperties(DocumentFamily.PRESENTATION, Collections.singletonMap("FilterName", "impress_pdf_Export"));
/*  32 */     documentFormat1.setStoreProperties(DocumentFamily.DRAWING, Collections.singletonMap("FilterName", "draw_pdf_Export"));
/*  33 */     addFormat(documentFormat1);
/*     */     
/*  35 */     DocumentFormat documentFormat2 = new DocumentFormat("Macromedia Flash", "swf", "application/x-shockwave-flash");
/*  36 */     documentFormat2.setStoreProperties(DocumentFamily.PRESENTATION, Collections.singletonMap("FilterName", "impress_flash_Export"));
/*  37 */     documentFormat2.setStoreProperties(DocumentFamily.DRAWING, Collections.singletonMap("FilterName", "draw_flash_Export"));
/*  38 */     addFormat(documentFormat2);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  47 */     DocumentFormat documentFormat3 = new DocumentFormat("HTML", "html", "text/html");
/*     */ 
/*     */     
/*  50 */     documentFormat3.setInputFamily(DocumentFamily.TEXT);
/*  51 */     documentFormat3.setStoreProperties(DocumentFamily.TEXT, Collections.singletonMap("FilterName", "HTML (StarWriter)"));
/*  52 */     documentFormat3.setStoreProperties(DocumentFamily.SPREADSHEET, Collections.singletonMap("FilterName", "HTML (StarCalc)"));
/*  53 */     documentFormat3.setStoreProperties(DocumentFamily.PRESENTATION, Collections.singletonMap("FilterName", "impress_html_Export"));
/*  54 */     addFormat(documentFormat3);
/*     */     
/*  56 */     DocumentFormat documentFormat4 = new DocumentFormat("OpenDocument Text", "odt", "application/vnd.oasis.opendocument.text");
/*  57 */     documentFormat4.setInputFamily(DocumentFamily.TEXT);
/*  58 */     documentFormat4.setStoreProperties(DocumentFamily.TEXT, Collections.singletonMap("FilterName", "writer8"));
/*  59 */     addFormat(documentFormat4);
/*     */     
/*  61 */     DocumentFormat documentFormat5 = new DocumentFormat("OpenOffice.org 1.0 Text Document", "sxw", "application/vnd.sun.xml.writer");
/*  62 */     documentFormat5.setInputFamily(DocumentFamily.TEXT);
/*  63 */     documentFormat5.setStoreProperties(DocumentFamily.TEXT, Collections.singletonMap("FilterName", "StarOffice XML (Writer)"));
/*  64 */     addFormat(documentFormat5);
/*     */     
/*  66 */     DocumentFormat documentFormat6 = new DocumentFormat("Microsoft Word", "doc", "application/msword");
/*  67 */     documentFormat6.setInputFamily(DocumentFamily.TEXT);
/*  68 */     documentFormat6.setStoreProperties(DocumentFamily.TEXT, Collections.singletonMap("FilterName", "MS Word 97"));
/*  69 */     addFormat(documentFormat6);
/*     */     
/*  71 */     DocumentFormat documentFormat7 = new DocumentFormat("Microsoft Word 2007 XML", "docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
/*  72 */     documentFormat7.setInputFamily(DocumentFamily.TEXT);
/*  73 */     addFormat(documentFormat7);
/*     */     
/*  75 */     DocumentFormat documentFormat8 = new DocumentFormat("Rich Text Format", "rtf", "text/rtf");
/*  76 */     documentFormat8.setInputFamily(DocumentFamily.TEXT);
/*  77 */     documentFormat8.setStoreProperties(DocumentFamily.TEXT, Collections.singletonMap("FilterName", "Rich Text Format"));
/*  78 */     addFormat(documentFormat8);
/*     */     
/*  80 */     DocumentFormat documentFormat9 = new DocumentFormat("WordPerfect", "wpd", "application/wordperfect");
/*  81 */     documentFormat9.setInputFamily(DocumentFamily.TEXT);
/*  82 */     addFormat(documentFormat9);
/*     */     
/*  84 */     DocumentFormat documentFormat10 = new DocumentFormat("Plain Text", "txt", "text/plain");
/*  85 */     documentFormat10.setInputFamily(DocumentFamily.TEXT);
/*  86 */     LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<Object, Object>();
/*  87 */     linkedHashMap1.put("FilterName", "Text (encoded)");
/*  88 */     linkedHashMap1.put("FilterOptions", "");
/*  89 */     documentFormat10.setLoadProperties((Map)linkedHashMap1);
/*  90 */     documentFormat10.setStoreProperties(DocumentFamily.TEXT, (Map)linkedHashMap1);
/*  91 */     addFormat(documentFormat10);
/*     */     
/*  93 */     DocumentFormat documentFormat11 = new DocumentFormat("MediaWiki wikitext", "wiki", "text/x-wiki");
/*  94 */     documentFormat11.setStoreProperties(DocumentFamily.TEXT, Collections.singletonMap("FilterName", "MediaWiki"));
/*     */ 
/*     */     
/*  97 */     DocumentFormat documentFormat12 = new DocumentFormat("OpenDocument Spreadsheet", "ods", "application/vnd.oasis.opendocument.spreadsheet");
/*  98 */     documentFormat12.setInputFamily(DocumentFamily.SPREADSHEET);
/*  99 */     documentFormat12.setStoreProperties(DocumentFamily.SPREADSHEET, Collections.singletonMap("FilterName", "calc8"));
/* 100 */     addFormat(documentFormat12);
/*     */     
/* 102 */     DocumentFormat documentFormat13 = new DocumentFormat("OpenOffice.org 1.0 Spreadsheet", "sxc", "application/vnd.sun.xml.calc");
/* 103 */     documentFormat13.setInputFamily(DocumentFamily.SPREADSHEET);
/* 104 */     documentFormat13.setStoreProperties(DocumentFamily.SPREADSHEET, Collections.singletonMap("FilterName", "StarOffice XML (Calc)"));
/* 105 */     addFormat(documentFormat13);
/*     */     
/* 107 */     DocumentFormat documentFormat14 = new DocumentFormat("Microsoft Excel", "xls", "application/vnd.ms-excel");
/* 108 */     documentFormat14.setInputFamily(DocumentFamily.SPREADSHEET);
/* 109 */     documentFormat14.setStoreProperties(DocumentFamily.SPREADSHEET, Collections.singletonMap("FilterName", "MS Excel 97"));
/* 110 */     addFormat(documentFormat14);
/*     */     
/* 112 */     DocumentFormat documentFormat15 = new DocumentFormat("Microsoft Excel 2007 XML", "xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
/* 113 */     documentFormat15.setInputFamily(DocumentFamily.SPREADSHEET);
/* 114 */     addFormat(documentFormat15);
/*     */     
/* 116 */     DocumentFormat documentFormat16 = new DocumentFormat("Comma Separated Values", "csv", "text/csv");
/* 117 */     documentFormat16.setInputFamily(DocumentFamily.SPREADSHEET);
/* 118 */     LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<Object, Object>();
/* 119 */     linkedHashMap2.put("FilterName", "Text - txt - csv (StarCalc)");
/* 120 */     linkedHashMap2.put("FilterOptions", "44,34,0");
/* 121 */     documentFormat16.setLoadProperties((Map)linkedHashMap2);
/* 122 */     documentFormat16.setStoreProperties(DocumentFamily.SPREADSHEET, (Map)linkedHashMap2);
/* 123 */     addFormat(documentFormat16);
/*     */     
/* 125 */     DocumentFormat documentFormat17 = new DocumentFormat("Tab Separated Values", "tsv", "text/tab-separated-values");
/* 126 */     documentFormat17.setInputFamily(DocumentFamily.SPREADSHEET);
/* 127 */     LinkedHashMap<Object, Object> linkedHashMap3 = new LinkedHashMap<Object, Object>();
/* 128 */     linkedHashMap3.put("FilterName", "Text - txt - csv (StarCalc)");
/* 129 */     linkedHashMap3.put("FilterOptions", "9,34,0");
/* 130 */     documentFormat17.setLoadProperties((Map)linkedHashMap3);
/* 131 */     documentFormat17.setStoreProperties(DocumentFamily.SPREADSHEET, (Map)linkedHashMap3);
/* 132 */     addFormat(documentFormat17);
/*     */     
/* 134 */     DocumentFormat documentFormat18 = new DocumentFormat("OpenDocument Presentation", "odp", "application/vnd.oasis.opendocument.presentation");
/* 135 */     documentFormat18.setInputFamily(DocumentFamily.PRESENTATION);
/* 136 */     documentFormat18.setStoreProperties(DocumentFamily.PRESENTATION, Collections.singletonMap("FilterName", "impress8"));
/* 137 */     addFormat(documentFormat18);
/*     */     
/* 139 */     DocumentFormat documentFormat19 = new DocumentFormat("OpenOffice.org 1.0 Presentation", "sxi", "application/vnd.sun.xml.impress");
/* 140 */     documentFormat19.setInputFamily(DocumentFamily.PRESENTATION);
/* 141 */     documentFormat19.setStoreProperties(DocumentFamily.PRESENTATION, Collections.singletonMap("FilterName", "StarOffice XML (Impress)"));
/* 142 */     addFormat(documentFormat19);
/*     */     
/* 144 */     DocumentFormat documentFormat20 = new DocumentFormat("Microsoft PowerPoint", "ppt", "application/vnd.ms-powerpoint");
/* 145 */     documentFormat20.setInputFamily(DocumentFamily.PRESENTATION);
/* 146 */     documentFormat20.setStoreProperties(DocumentFamily.PRESENTATION, Collections.singletonMap("FilterName", "MS PowerPoint 97"));
/* 147 */     addFormat(documentFormat20);
/*     */     
/* 149 */     DocumentFormat documentFormat21 = new DocumentFormat("Microsoft PowerPoint 2007 XML", "pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
/* 150 */     documentFormat21.setInputFamily(DocumentFamily.PRESENTATION);
/* 151 */     addFormat(documentFormat21);
/*     */     
/* 153 */     DocumentFormat documentFormat22 = new DocumentFormat("OpenDocument Drawing", "odg", "application/vnd.oasis.opendocument.graphics");
/* 154 */     documentFormat22.setInputFamily(DocumentFamily.DRAWING);
/* 155 */     documentFormat22.setStoreProperties(DocumentFamily.DRAWING, Collections.singletonMap("FilterName", "draw8"));
/* 156 */     addFormat(documentFormat22);
/*     */     
/* 158 */     DocumentFormat documentFormat23 = new DocumentFormat("Scalable Vector Graphics", "svg", "image/svg+xml");
/* 159 */     documentFormat23.setStoreProperties(DocumentFamily.DRAWING, Collections.singletonMap("FilterName", "draw_svg_Export"));
/* 160 */     addFormat(documentFormat23);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/document/DefaultDocumentFormatRegistry.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */