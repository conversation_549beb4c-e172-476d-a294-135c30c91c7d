/*     */ package org.artofsolving.jodconverter.document;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocumentFormat
/*     */ {
/*     */   private String name;
/*     */   private String extension;
/*     */   private String mediaType;
/*     */   private DocumentFamily inputFamily;
/*     */   private Map<String, ?> loadProperties;
/*     */   private Map<DocumentFamily, Map<String, ?>> storePropertiesByFamily;
/*     */   
/*     */   public DocumentFormat() {}
/*     */   
/*     */   public DocumentFormat(String paramString1, String paramString2, String paramString3) {
/*  38 */     this.name = paramString1;
/*  39 */     this.extension = paramString2;
/*  40 */     this.mediaType = paramString3;
/*     */   }
/*     */   
/*     */   public String getName() {
/*  44 */     return this.name;
/*     */   }
/*     */   
/*     */   public void setName(String paramString) {
/*  48 */     this.name = paramString;
/*     */   }
/*     */   
/*     */   public String getExtension() {
/*  52 */     return this.extension;
/*     */   }
/*     */   
/*     */   public void setExtension(String paramString) {
/*  56 */     this.extension = paramString;
/*     */   }
/*     */   
/*     */   public String getMediaType() {
/*  60 */     return this.mediaType;
/*     */   }
/*     */   
/*     */   public void setMediaType(String paramString) {
/*  64 */     this.mediaType = paramString;
/*     */   }
/*     */   
/*     */   public DocumentFamily getInputFamily() {
/*  68 */     return this.inputFamily;
/*     */   }
/*     */   
/*     */   public void setInputFamily(DocumentFamily paramDocumentFamily) {
/*  72 */     this.inputFamily = paramDocumentFamily;
/*     */   }
/*     */   
/*     */   public Map<String, ?> getLoadProperties() {
/*  76 */     return this.loadProperties;
/*     */   }
/*     */   
/*     */   public void setLoadProperties(Map<String, ?> paramMap) {
/*  80 */     this.loadProperties = paramMap;
/*     */   }
/*     */   
/*     */   public Map<DocumentFamily, Map<String, ?>> getStorePropertiesByFamily() {
/*  84 */     return this.storePropertiesByFamily;
/*     */   }
/*     */   
/*     */   public void setStorePropertiesByFamily(Map<DocumentFamily, Map<String, ?>> paramMap) {
/*  88 */     this.storePropertiesByFamily = paramMap;
/*     */   }
/*     */   
/*     */   public void setStoreProperties(DocumentFamily paramDocumentFamily, Map<String, ?> paramMap) {
/*  92 */     if (this.storePropertiesByFamily == null) {
/*  93 */       this.storePropertiesByFamily = new HashMap<DocumentFamily, Map<String, ?>>();
/*     */     }
/*  95 */     this.storePropertiesByFamily.put(paramDocumentFamily, paramMap);
/*     */   }
/*     */   
/*     */   public Map<String, ?> getStoreProperties(DocumentFamily paramDocumentFamily) {
/*  99 */     if (this.storePropertiesByFamily == null) {
/* 100 */       return null;
/*     */     }
/* 102 */     return this.storePropertiesByFamily.get(paramDocumentFamily);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/document/DocumentFormat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */