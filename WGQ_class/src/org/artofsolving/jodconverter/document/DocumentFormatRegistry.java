package org.artofsolving.jodconverter.document;

import java.util.Set;

public interface DocumentFormatRegistry {
  DocumentFormat getFormatByExtension(String paramString);
  
  DocumentFormat getFormatByMediaType(String paramString);
  
  Set<DocumentFormat> getOutputFormats(DocumentFamily paramDocumentFamily);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/document/DocumentFormatRegistry.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */