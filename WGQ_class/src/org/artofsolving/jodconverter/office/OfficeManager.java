package org.artofsolving.jodconverter.office;

public interface OfficeManager {
  void execute(OfficeTask paramOfficeTask) throws OfficeException;
  
  void start() throws OfficeException;
  
  void stop() throws OfficeException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/OfficeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */