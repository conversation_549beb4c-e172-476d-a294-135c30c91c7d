/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class PooledOfficeManagerSettings
/*    */   extends ManagedOfficeProcessSettings
/*    */ {
/*    */   public static final long DEFAULT_TASK_EXECUTION_TIMEOUT = 120000L;
/*    */   public static final int DEFAULT_MAX_TASKS_PER_PROCESS = 200;
/* 26 */   private long taskExecutionTimeout = 120000L;
/* 27 */   private int maxTasksPerProcess = 200;
/*    */   
/*    */   public PooledOfficeManagerSettings(UnoUrl paramUnoUrl) {
/* 30 */     super(paramUnoUrl);
/*    */   }
/*    */   
/*    */   public long getTaskExecutionTimeout() {
/* 34 */     return this.taskExecutionTimeout;
/*    */   }
/*    */   
/*    */   public void setTaskExecutionTimeout(long paramLong) {
/* 38 */     this.taskExecutionTimeout = paramLong;
/*    */   }
/*    */   
/*    */   public int getMaxTasksPerProcess() {
/* 42 */     return this.maxTasksPerProcess;
/*    */   }
/*    */   
/*    */   public void setMaxTasksPerProcess(int paramInt) {
/* 46 */     this.maxTasksPerProcess = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/PooledOfficeManagerSettings.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */