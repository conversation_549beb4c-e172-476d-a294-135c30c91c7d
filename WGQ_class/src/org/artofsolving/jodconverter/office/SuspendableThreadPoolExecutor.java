/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ import java.util.concurrent.LinkedBlockingQueue;
/*    */ import java.util.concurrent.ThreadFactory;
/*    */ import java.util.concurrent.ThreadPoolExecutor;
/*    */ import java.util.concurrent.TimeUnit;
/*    */ import java.util.concurrent.locks.Condition;
/*    */ import java.util.concurrent.locks.ReentrantLock;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class SuspendableThreadPoolExecutor
/*    */   extends ThreadPoolExecutor
/*    */ {
/*    */   private boolean available = false;
/* 31 */   private ReentrantLock suspendLock = new ReentrantLock();
/* 32 */   private Condition availableCondition = this.suspendLock.newCondition();
/*    */   
/*    */   public SuspendableThreadPoolExecutor(ThreadFactory paramThreadFactory) {
/* 35 */     super(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), paramThreadFactory);
/*    */   }
/*    */ 
/*    */   
/*    */   protected void beforeExecute(Thread paramThread, Runnable paramRunnable) {
/* 40 */     super.beforeExecute(paramThread, paramRunnable);
/* 41 */     this.suspendLock.lock();
/*    */     try {
/* 43 */       while (!this.available) {
/* 44 */         this.availableCondition.await();
/*    */       }
/* 46 */     } catch (InterruptedException interruptedException) {
/* 47 */       paramThread.interrupt();
/*    */     } finally {
/* 49 */       this.suspendLock.unlock();
/*    */     } 
/*    */   }
/*    */   
/*    */   public void setAvailable(boolean paramBoolean) {
/* 54 */     this.suspendLock.lock();
/*    */     try {
/* 56 */       this.available = paramBoolean;
/* 57 */       if (paramBoolean) {
/* 58 */         this.availableCondition.signalAll();
/*    */       }
/*    */     } finally {
/* 61 */       this.suspendLock.unlock();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/SuspendableThreadPoolExecutor.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */