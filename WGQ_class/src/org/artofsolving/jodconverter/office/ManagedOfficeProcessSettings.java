/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import java.io.File;
/*     */ import org.artofsolving.jodconverter.process.ProcessManager;
/*     */ import org.artofsolving.jodconverter.process.PureJavaProcessManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class ManagedOfficeProcessSettings
/*     */ {
/*     */   public static final long DEFAULT_RETRY_INTERVAL = 250L;
/*     */   private final UnoUrl unoUrl;
/*  31 */   private File officeHome = OfficeUtils.getDefaultOfficeHome();
/*     */   private String[] runAsArgs;
/*     */   private File templateProfileDir;
/*  34 */   private File workDir = new File(System.getProperty("java.io.tmpdir"));
/*  35 */   private ProcessManager processManager = (ProcessManager)new PureJavaProcessManager();
/*  36 */   private long retryTimeout = 120000L;
/*  37 */   private long retryInterval = 250L;
/*     */   
/*     */   public ManagedOfficeProcessSettings(UnoUrl paramUnoUrl) {
/*  40 */     this.unoUrl = paramUnoUrl;
/*     */   }
/*     */   
/*     */   public UnoUrl getUnoUrl() {
/*  44 */     return this.unoUrl;
/*     */   }
/*     */   
/*     */   public File getOfficeHome() {
/*  48 */     return this.officeHome;
/*     */   }
/*     */   
/*     */   public void setOfficeHome(File paramFile) {
/*  52 */     this.officeHome = paramFile;
/*     */   }
/*     */   
/*     */   public String[] getRunAsArgs() {
/*  56 */     return this.runAsArgs;
/*     */   }
/*     */   
/*     */   public void setRunAsArgs(String[] paramArrayOfString) {
/*  60 */     this.runAsArgs = paramArrayOfString;
/*     */   }
/*     */   
/*     */   public File getTemplateProfileDir() {
/*  64 */     return this.templateProfileDir;
/*     */   }
/*     */   
/*     */   public void setTemplateProfileDir(File paramFile) {
/*  68 */     this.templateProfileDir = paramFile;
/*     */   }
/*     */   
/*     */   public File getWorkDir() {
/*  72 */     return this.workDir;
/*     */   }
/*     */   
/*     */   public void setWorkDir(File paramFile) {
/*  76 */     this.workDir = paramFile;
/*     */   }
/*     */   
/*     */   public ProcessManager getProcessManager() {
/*  80 */     return this.processManager;
/*     */   }
/*     */   
/*     */   public void setProcessManager(ProcessManager paramProcessManager) {
/*  84 */     this.processManager = paramProcessManager;
/*     */   }
/*     */   
/*     */   public long getRetryTimeout() {
/*  88 */     return this.retryTimeout;
/*     */   }
/*     */   
/*     */   public void setRetryTimeout(long paramLong) {
/*  92 */     this.retryTimeout = paramLong;
/*     */   }
/*     */   
/*     */   public long getRetryInterval() {
/*  96 */     return this.retryInterval;
/*     */   }
/*     */   
/*     */   public void setRetryInterval(long paramLong) {
/* 100 */     this.retryInterval = paramLong;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/ManagedOfficeProcessSettings.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */