/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ import java.util.concurrent.ThreadFactory;
/*    */ import java.util.concurrent.atomic.AtomicInteger;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class NamedThreadFactory
/*    */   implements ThreadFactory
/*    */ {
/* 29 */   private static final AtomicInteger threadIndex = new AtomicInteger(0);
/*    */   
/*    */   private final String baseName;
/*    */   private final boolean daemon;
/*    */   
/*    */   public NamedThreadFactory(String paramString) {
/* 35 */     this(paramString, true);
/*    */   }
/*    */   
/*    */   public NamedThreadFactory(String paramString, boolean paramBoolean) {
/* 39 */     this.baseName = paramString;
/* 40 */     this.daemon = paramBoolean;
/*    */   }
/*    */   
/*    */   public Thread newThread(Runnable paramRunnable) {
/* 44 */     Thread thread = new Thread(paramRunnable, this.baseName + "-" + threadIndex.getAndIncrement());
/* 45 */     thread.setDaemon(this.daemon);
/* 46 */     return thread;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/NamedThreadFactory.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */