/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import com.sun.star.beans.PropertyValue;
/*     */ import com.sun.star.uno.UnoRuntime;
/*     */ import java.io.File;
/*     */ import java.util.Map;
/*     */ import org.artofsolving.jodconverter.util.PlatformUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OfficeUtils
/*     */ {
/*     */   public static final String SERVICE_DESKTOP = "com.sun.star.frame.Desktop";
/*     */   
/*     */   private OfficeUtils() {
/*  34 */     throw new AssertionError("utility class must not be instantiated");
/*     */   }
/*     */   
/*     */   public static <T> T cast(Class<T> paramClass, Object paramObject) {
/*  38 */     return (T)UnoRuntime.queryInterface(paramClass, paramObject);
/*     */   }
/*     */   
/*     */   public static PropertyValue property(String paramString, Object paramObject) {
/*  42 */     PropertyValue propertyValue = new PropertyValue();
/*  43 */     propertyValue.Name = paramString;
/*  44 */     propertyValue.Value = paramObject;
/*  45 */     return propertyValue;
/*     */   }
/*     */ 
/*     */   
/*     */   public static PropertyValue[] toUnoProperties(Map<String, ?> paramMap) {
/*  50 */     PropertyValue[] arrayOfPropertyValue = new PropertyValue[paramMap.size()];
/*  51 */     byte b = 0;
/*  52 */     for (Map.Entry<String, ?> entry : paramMap.entrySet()) {
/*  53 */       Object object = entry.getValue();
/*  54 */       if (object instanceof Map) {
/*  55 */         Map<String, ?> map = (Map)object;
/*  56 */         object = toUnoProperties(map);
/*     */       } 
/*  58 */       arrayOfPropertyValue[b++] = property((String)entry.getKey(), object);
/*     */     } 
/*  60 */     return arrayOfPropertyValue;
/*     */   }
/*     */   
/*     */   public static String toUrl(File paramFile) {
/*  64 */     String str1 = paramFile.toURI().getRawPath();
/*  65 */     String str2 = str1.startsWith("//") ? ("file:" + str1) : ("file://" + str1);
/*  66 */     return str2.endsWith("/") ? str2.substring(0, str2.length() - 1) : str2;
/*     */   }
/*     */   
/*     */   public static File getDefaultOfficeHome() {
/*  70 */     if (System.getProperty("office.home") != null) {
/*  71 */       return new File(System.getProperty("office.home"));
/*     */     }
/*  73 */     if (PlatformUtils.isWindows()) {
/*     */       
/*  75 */       String str = System.getenv("ProgramFiles(x86)");
/*  76 */       if (str == null) {
/*  77 */         str = System.getenv("ProgramFiles");
/*     */       }
/*  79 */       return findOfficeHome(new String[] { str + File.separator + "OpenOffice.org 3", str + File.separator + "LibreOffice 3" });
/*     */     } 
/*     */ 
/*     */     
/*  83 */     if (PlatformUtils.isMac()) {
/*  84 */       return findOfficeHome(new String[] { "/Applications/OpenOffice.org.app/Contents", "/Applications/LibreOffice.app/Contents" });
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  90 */     return findOfficeHome(new String[] { "/opt/openoffice.org3", "/opt/libreoffice", "/usr/lib/openoffice", "/usr/lib/libreoffice" });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static File findOfficeHome(String... paramVarArgs) {
/* 100 */     for (String str : paramVarArgs) {
/* 101 */       File file = new File(str);
/* 102 */       if (getOfficeExecutable(file).isFile()) {
/* 103 */         return file;
/*     */       }
/*     */     } 
/* 106 */     return null;
/*     */   }
/*     */   
/*     */   public static File getOfficeExecutable(File paramFile) {
/* 110 */     if (PlatformUtils.isMac()) {
/* 111 */       return new File(paramFile, "MacOS/soffice.bin");
/*     */     }
/* 113 */     return new File(paramFile, "program/soffice.bin");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/OfficeUtils.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */