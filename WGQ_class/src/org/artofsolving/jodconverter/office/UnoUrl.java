/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class UnoUrl
/*    */ {
/*    */   private final String acceptString;
/*    */   private final String connectString;
/*    */   
/*    */   private UnoUrl(String paramString1, String paramString2) {
/* 41 */     this.acceptString = paramString1;
/* 42 */     this.connectString = paramString2;
/*    */   }
/*    */   
/*    */   public static UnoUrl socket(int paramInt) {
/* 46 */     String str = "socket,host=127.0.0.1,port=" + paramInt;
/* 47 */     return new UnoUrl(str, str + ",tcpNoDelay=1");
/*    */   }
/*    */   
/*    */   public static UnoUrl pipe(String paramString) {
/* 51 */     String str = "pipe,name=" + paramString;
/* 52 */     return new UnoUrl(str, str);
/*    */   }
/*    */   
/*    */   public String getAcceptString() {
/* 56 */     return this.acceptString;
/*    */   }
/*    */   
/*    */   public String getConnectString() {
/* 60 */     return this.connectString;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 65 */     return this.connectString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/UnoUrl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */