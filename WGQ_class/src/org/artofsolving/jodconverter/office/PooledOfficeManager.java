/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import java.util.concurrent.ExecutionException;
/*     */ import java.util.concurrent.Future;
/*     */ import java.util.concurrent.TimeUnit;
/*     */ import java.util.concurrent.TimeoutException;
/*     */ import java.util.logging.Logger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class PooledOfficeManager
/*     */   implements OfficeManager
/*     */ {
/*     */   private final PooledOfficeManagerSettings settings;
/*     */   private final ManagedOfficeProcess managedOfficeProcess;
/*     */   private final SuspendableThreadPoolExecutor taskExecutor;
/*     */   private volatile boolean stopping = false;
/*     */   private int taskCount;
/*     */   private Future<?> currentTask;
/*  37 */   private final Logger logger = Logger.getLogger(getClass().getName());
/*     */   
/*  39 */   private OfficeConnectionEventListener connectionEventListener = new OfficeConnectionEventListener() {
/*     */       public void connected(OfficeConnectionEvent param1OfficeConnectionEvent) {
/*  41 */         PooledOfficeManager.this.taskCount = 0;
/*  42 */         PooledOfficeManager.this.taskExecutor.setAvailable(true);
/*     */       }
/*     */       public void disconnected(OfficeConnectionEvent param1OfficeConnectionEvent) {
/*  45 */         PooledOfficeManager.this.taskExecutor.setAvailable(false);
/*  46 */         if (PooledOfficeManager.this.stopping) {
/*     */           
/*  48 */           PooledOfficeManager.this.stopping = false;
/*     */         } else {
/*  50 */           PooledOfficeManager.this.logger.warning("connection lost unexpectedly; attempting restart");
/*  51 */           if (PooledOfficeManager.this.currentTask != null) {
/*  52 */             PooledOfficeManager.this.currentTask.cancel(true);
/*     */           }
/*  54 */           PooledOfficeManager.this.managedOfficeProcess.restartDueToLostConnection();
/*     */         } 
/*     */       }
/*     */     };
/*     */   
/*     */   public PooledOfficeManager(UnoUrl paramUnoUrl) {
/*  60 */     this(new PooledOfficeManagerSettings(paramUnoUrl));
/*     */   }
/*     */   
/*     */   public PooledOfficeManager(PooledOfficeManagerSettings paramPooledOfficeManagerSettings) {
/*  64 */     this.settings = paramPooledOfficeManagerSettings;
/*  65 */     this.managedOfficeProcess = new ManagedOfficeProcess(paramPooledOfficeManagerSettings);
/*  66 */     this.managedOfficeProcess.getConnection().addConnectionEventListener(this.connectionEventListener);
/*  67 */     this.taskExecutor = new SuspendableThreadPoolExecutor(new NamedThreadFactory("OfficeTaskThread"));
/*     */   }
/*     */   
/*     */   public void execute(final OfficeTask task) throws OfficeException {
/*  71 */     Future<?> future = this.taskExecutor.submit(new Runnable() {
/*     */           public void run() {
/*  73 */             if (PooledOfficeManager.this.settings.getMaxTasksPerProcess() > 0 && ++PooledOfficeManager.this.taskCount == PooledOfficeManager.this.settings.getMaxTasksPerProcess() + 1) {
/*  74 */               PooledOfficeManager.this.logger.info(String.format("reached limit of %d maxTasksPerProcess: restarting", new Object[] { Integer.valueOf(PooledOfficeManager.access$600(this.this$0).getMaxTasksPerProcess()) }));
/*  75 */               PooledOfficeManager.this.taskExecutor.setAvailable(false);
/*  76 */               PooledOfficeManager.this.stopping = true;
/*  77 */               PooledOfficeManager.this.managedOfficeProcess.restartAndWait();
/*     */             } 
/*     */             
/*  80 */             task.execute(PooledOfficeManager.this.managedOfficeProcess.getConnection());
/*     */           }
/*     */         });
/*  83 */     this.currentTask = future;
/*     */     try {
/*  85 */       future.get(this.settings.getTaskExecutionTimeout(), TimeUnit.MILLISECONDS);
/*  86 */     } catch (TimeoutException timeoutException) {
/*  87 */       this.managedOfficeProcess.restartDueToTaskTimeout();
/*  88 */       throw new OfficeException("task did not complete within timeout", timeoutException);
/*  89 */     } catch (ExecutionException executionException) {
/*  90 */       if (executionException.getCause() instanceof OfficeException) {
/*  91 */         throw (OfficeException)executionException.getCause();
/*     */       }
/*  93 */       throw new OfficeException("task failed", executionException.getCause());
/*     */     }
/*  95 */     catch (Exception exception) {
/*  96 */       throw new OfficeException("task failed", exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void start() throws OfficeException {
/* 101 */     this.managedOfficeProcess.startAndWait();
/*     */   }
/*     */   
/*     */   public void stop() throws OfficeException {
/* 105 */     this.taskExecutor.setAvailable(false);
/* 106 */     this.stopping = true;
/* 107 */     this.taskExecutor.shutdownNow();
/* 108 */     this.managedOfficeProcess.stopAndWait();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/PooledOfficeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */