/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ import java.util.EventObject;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class OfficeConnectionEvent
/*    */   extends EventObject
/*    */ {
/*    */   private static final long serialVersionUID = 2060652797570876077L;
/*    */   
/*    */   public OfficeConnectionEvent(OfficeConnection paramOfficeConnection) {
/* 28 */     super(paramOfficeConnection);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/OfficeConnectionEvent.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */