/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ import java.net.ConnectException;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class ExternalOfficeManager
/*    */   implements OfficeManager
/*    */ {
/*    */   private final OfficeConnection connection;
/*    */   private final boolean connectOnStart;
/*    */   
/*    */   public ExternalOfficeManager(UnoUrl paramUnoUrl, boolean paramBoolean) {
/* 55 */     this.connection = new OfficeConnection(paramUnoUrl);
/* 56 */     this.connectOnStart = paramBoolean;
/*    */   }
/*    */   
/*    */   public void start() throws OfficeException {
/* 60 */     if (this.connectOnStart) {
/* 61 */       synchronized (this.connection) {
/* 62 */         connect();
/*    */       } 
/*    */     }
/*    */   }
/*    */   
/*    */   public void stop() {
/* 68 */     synchronized (this.connection) {
/* 69 */       if (this.connection.isConnected()) {
/* 70 */         this.connection.disconnect();
/*    */       }
/*    */     } 
/*    */   }
/*    */   
/*    */   public void execute(OfficeTask paramOfficeTask) throws OfficeException {
/* 76 */     synchronized (this.connection) {
/* 77 */       if (!this.connection.isConnected()) {
/* 78 */         connect();
/*    */       }
/* 80 */       paramOfficeTask.execute(this.connection);
/*    */     } 
/*    */   }
/*    */   
/*    */   private void connect() {
/*    */     try {
/* 86 */       this.connection.connect();
/* 87 */     } catch (ConnectException connectException) {
/* 88 */       throw new OfficeException("could not connect to external office process", connectException);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/ExternalOfficeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */