/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import com.sun.star.frame.XDesktop;
/*     */ import com.sun.star.lang.DisposedException;
/*     */ import java.net.ConnectException;
/*     */ import java.util.concurrent.ExecutorService;
/*     */ import java.util.concurrent.Executors;
/*     */ import java.util.concurrent.Future;
/*     */ import java.util.logging.Level;
/*     */ import java.util.logging.Logger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class ManagedOfficeProcess
/*     */ {
/*  33 */   private static final Integer EXIT_CODE_NEW_INSTALLATION = Integer.valueOf(81);
/*     */   
/*     */   private final ManagedOfficeProcessSettings settings;
/*     */   
/*     */   private final OfficeProcess process;
/*     */   
/*     */   private final OfficeConnection connection;
/*  40 */   private ExecutorService executor = Executors.newSingleThreadExecutor(new NamedThreadFactory("OfficeProcessThread"));
/*     */   
/*  42 */   private final Logger logger = Logger.getLogger(getClass().getName());
/*     */   
/*     */   public ManagedOfficeProcess(ManagedOfficeProcessSettings paramManagedOfficeProcessSettings) throws OfficeException {
/*  45 */     this.settings = paramManagedOfficeProcessSettings;
/*  46 */     this.process = new OfficeProcess(paramManagedOfficeProcessSettings.getOfficeHome(), paramManagedOfficeProcessSettings.getUnoUrl(), paramManagedOfficeProcessSettings.getRunAsArgs(), paramManagedOfficeProcessSettings.getTemplateProfileDir(), paramManagedOfficeProcessSettings.getWorkDir(), paramManagedOfficeProcessSettings.getProcessManager());
/*     */     
/*  48 */     this.connection = new OfficeConnection(paramManagedOfficeProcessSettings.getUnoUrl());
/*     */   }
/*     */   
/*     */   public OfficeConnection getConnection() {
/*  52 */     return this.connection;
/*     */   }
/*     */   
/*     */   public void startAndWait() throws OfficeException {
/*  56 */     Future<?> future = this.executor.submit(new Runnable() {
/*     */           public void run() {
/*  58 */             ManagedOfficeProcess.this.doStartProcessAndConnect();
/*     */           }
/*     */         });
/*     */     try {
/*  62 */       future.get();
/*  63 */     } catch (Exception exception) {
/*  64 */       throw new OfficeException("failed to start and connect", exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void stopAndWait() throws OfficeException {
/*  69 */     Future<?> future = this.executor.submit(new Runnable() {
/*     */           public void run() {
/*  71 */             ManagedOfficeProcess.this.doStopProcess();
/*     */           }
/*     */         });
/*     */     try {
/*  75 */       future.get();
/*  76 */     } catch (Exception exception) {
/*  77 */       throw new OfficeException("failed to start and connect", exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void restartAndWait() {
/*  82 */     Future<?> future = this.executor.submit(new Runnable() {
/*     */           public void run() {
/*  84 */             ManagedOfficeProcess.this.doStopProcess();
/*  85 */             ManagedOfficeProcess.this.doStartProcessAndConnect();
/*     */           }
/*     */         });
/*     */     try {
/*  89 */       future.get();
/*  90 */     } catch (Exception exception) {
/*  91 */       throw new OfficeException("failed to restart", exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void restartDueToTaskTimeout() {
/*  96 */     this.executor.execute(new Runnable() {
/*     */           public void run() {
/*  98 */             ManagedOfficeProcess.this.doTerminateProcess();
/*     */           }
/*     */         });
/*     */   }
/*     */ 
/*     */   
/*     */   public void restartDueToLostConnection() {
/* 105 */     this.executor.execute(new Runnable() {
/*     */           public void run() {
/*     */             try {
/* 108 */               ManagedOfficeProcess.this.doEnsureProcessExited();
/* 109 */               ManagedOfficeProcess.this.doStartProcessAndConnect();
/* 110 */             } catch (OfficeException officeException) {
/* 111 */               ManagedOfficeProcess.this.logger.log(Level.SEVERE, "could not restart process", officeException);
/*     */             } 
/*     */           }
/*     */         });
/*     */   }
/*     */   
/*     */   private void doStartProcessAndConnect() throws OfficeException {
/*     */     try {
/* 119 */       this.process.start();
/* 120 */       (new Retryable() {
/*     */           protected void attempt() throws TemporaryException, Exception {
/*     */             try {
/* 123 */               ManagedOfficeProcess.this.connection.connect();
/* 124 */             } catch (ConnectException connectException) {
/* 125 */               Integer integer = ManagedOfficeProcess.this.process.getExitCode();
/* 126 */               if (integer == null)
/*     */               {
/* 128 */                 throw new TemporaryException(connectException); } 
/* 129 */               if (integer.equals(ManagedOfficeProcess.EXIT_CODE_NEW_INSTALLATION)) {
/*     */ 
/*     */                 
/* 132 */                 ManagedOfficeProcess.this.logger.log(Level.WARNING, "office process died with exit code 81; restarting it");
/* 133 */                 ManagedOfficeProcess.this.process.start(true);
/* 134 */                 throw new TemporaryException(connectException);
/*     */               } 
/* 136 */               throw new OfficeException("office process died with exit code " + integer);
/*     */             }
/*     */           
/*     */           }
/*     */         }).execute(this.settings.getRetryInterval(), this.settings.getRetryTimeout());
/* 141 */     } catch (Exception exception) {
/* 142 */       throw new OfficeException("could not establish connection", exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void doStopProcess() {
/*     */     try {
/* 148 */       XDesktop xDesktop = OfficeUtils.<XDesktop>cast(XDesktop.class, this.connection.getService("com.sun.star.frame.Desktop"));
/* 149 */       xDesktop.terminate();
/* 150 */     } catch (DisposedException disposedException) {
/*     */     
/* 152 */     } catch (Exception exception) {
/*     */       
/* 154 */       doTerminateProcess();
/*     */     } 
/* 156 */     doEnsureProcessExited();
/*     */   }
/*     */   
/*     */   private void doEnsureProcessExited() throws OfficeException {
/*     */     try {
/* 161 */       int i = this.process.getExitCode(this.settings.getRetryInterval(), this.settings.getRetryTimeout());
/* 162 */       this.logger.info("process exited with code " + i);
/* 163 */     } catch (RetryTimeoutException retryTimeoutException) {
/* 164 */       doTerminateProcess();
/*     */     } 
/* 166 */     this.process.deleteProfileDir();
/*     */   }
/*     */   
/*     */   private void doTerminateProcess() throws OfficeException {
/*     */     try {
/* 171 */       int i = this.process.forciblyTerminate(this.settings.getRetryInterval(), this.settings.getRetryTimeout());
/* 172 */       this.logger.info("process forcibly terminated with code " + i);
/* 173 */     } catch (Exception exception) {
/* 174 */       throw new OfficeException("could not terminate process", exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/ManagedOfficeProcess.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */