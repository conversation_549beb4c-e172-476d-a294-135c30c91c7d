/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import java.io.File;
/*     */ import org.artofsolving.jodconverter.process.LinuxProcessManager;
/*     */ import org.artofsolving.jodconverter.process.ProcessManager;
/*     */ import org.artofsolving.jodconverter.process.PureJavaProcessManager;
/*     */ import org.artofsolving.jodconverter.util.PlatformUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DefaultOfficeManagerConfiguration
/*     */ {
/*     */   public static final long DEFAULT_RETRY_TIMEOUT = 120000L;
/*  33 */   private File officeHome = OfficeUtils.getDefaultOfficeHome();
/*  34 */   private OfficeConnectionProtocol connectionProtocol = OfficeConnectionProtocol.SOCKET;
/*  35 */   private int[] portNumbers = new int[] { 2002 };
/*  36 */   private String[] pipeNames = new String[] { "office" };
/*  37 */   private String[] runAsArgs = null;
/*  38 */   private File templateProfileDir = null;
/*  39 */   private File workDir = new File(System.getProperty("java.io.tmpdir"));
/*  40 */   private long taskQueueTimeout = 30000L;
/*  41 */   private long taskExecutionTimeout = 120000L;
/*  42 */   private int maxTasksPerProcess = 200;
/*  43 */   private long retryTimeout = 120000L;
/*     */   
/*  45 */   private ProcessManager processManager = null;
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setOfficeHome(String paramString) throws NullPointerException, IllegalArgumentException {
/*  48 */     checkArgumentNotNull("officeHome", paramString);
/*  49 */     return setOfficeHome(new File(paramString));
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setOfficeHome(File paramFile) throws NullPointerException, IllegalArgumentException {
/*  53 */     checkArgumentNotNull("officeHome", paramFile);
/*  54 */     checkArgument("officeHome", paramFile.isDirectory(), "must exist and be a directory");
/*  55 */     this.officeHome = paramFile;
/*  56 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setConnectionProtocol(OfficeConnectionProtocol paramOfficeConnectionProtocol) throws NullPointerException {
/*  60 */     checkArgumentNotNull("connectionProtocol", paramOfficeConnectionProtocol);
/*  61 */     this.connectionProtocol = paramOfficeConnectionProtocol;
/*  62 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setPortNumber(int paramInt) {
/*  66 */     this.portNumbers = new int[] { paramInt };
/*  67 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setPortNumbers(int... paramVarArgs) throws NullPointerException, IllegalArgumentException {
/*  71 */     checkArgumentNotNull("portNumbers", paramVarArgs);
/*  72 */     checkArgument("portNumbers", (paramVarArgs.length > 0), "must not be empty");
/*  73 */     this.portNumbers = paramVarArgs;
/*  74 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setPipeName(String paramString) throws NullPointerException {
/*  78 */     checkArgumentNotNull("pipeName", paramString);
/*  79 */     this.pipeNames = new String[] { paramString };
/*  80 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setPipeNames(String... paramVarArgs) throws NullPointerException, IllegalArgumentException {
/*  84 */     checkArgumentNotNull("pipeNames", paramVarArgs);
/*  85 */     checkArgument("pipeNames", (paramVarArgs.length > 0), "must not be empty");
/*  86 */     this.pipeNames = paramVarArgs;
/*  87 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setRunAsArgs(String... paramVarArgs) {
/*  91 */     this.runAsArgs = paramVarArgs;
/*  92 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setTemplateProfileDir(File paramFile) throws IllegalArgumentException {
/*  96 */     if (paramFile != null) {
/*  97 */       checkArgument("templateProfileDir", paramFile.isDirectory(), "must exist and be a directory");
/*     */     }
/*  99 */     this.templateProfileDir = paramFile;
/* 100 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setWorkDir(File paramFile) {
/* 112 */     checkArgumentNotNull("workDir", paramFile);
/* 113 */     this.workDir = paramFile;
/* 114 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setTaskQueueTimeout(long paramLong) {
/* 118 */     this.taskQueueTimeout = paramLong;
/* 119 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setTaskExecutionTimeout(long paramLong) {
/* 123 */     this.taskExecutionTimeout = paramLong;
/* 124 */     return this;
/*     */   }
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setMaxTasksPerProcess(int paramInt) {
/* 128 */     this.maxTasksPerProcess = paramInt;
/* 129 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setProcessManager(ProcessManager paramProcessManager) throws NullPointerException {
/* 144 */     checkArgumentNotNull("processManager", paramProcessManager);
/* 145 */     this.processManager = paramProcessManager;
/* 146 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DefaultOfficeManagerConfiguration setRetryTimeout(long paramLong) {
/* 157 */     this.retryTimeout = paramLong;
/* 158 */     return this;
/*     */   }
/*     */   
/*     */   public OfficeManager buildOfficeManager() throws IllegalStateException {
/* 162 */     if (this.officeHome == null)
/* 163 */       throw new IllegalStateException("officeHome not set and could not be auto-detected"); 
/* 164 */     if (!this.officeHome.isDirectory())
/* 165 */       throw new IllegalStateException("officeHome doesn't exist or is not a directory: " + this.officeHome); 
/* 166 */     if (!OfficeUtils.getOfficeExecutable(this.officeHome).isFile()) {
/* 167 */       throw new IllegalStateException("invalid officeHome: it doesn't contain soffice.bin: " + this.officeHome);
/*     */     }
/* 169 */     if (this.templateProfileDir != null && !isValidProfileDir(this.templateProfileDir)) {
/* 170 */       throw new IllegalStateException("templateProfileDir doesn't appear to contain a user profile: " + this.templateProfileDir);
/*     */     }
/* 172 */     if (!this.workDir.isDirectory()) {
/* 173 */       throw new IllegalStateException("workDir doesn't exist or is not a directory: " + this.workDir);
/*     */     }
/*     */     
/* 176 */     if (this.processManager == null) {
/* 177 */       this.processManager = findBestProcessManager();
/*     */     }
/*     */     
/* 180 */     int i = (this.connectionProtocol == OfficeConnectionProtocol.PIPE) ? this.pipeNames.length : this.portNumbers.length;
/* 181 */     UnoUrl[] arrayOfUnoUrl = new UnoUrl[i];
/* 182 */     for (byte b = 0; b < i; b++) {
/* 183 */       arrayOfUnoUrl[b] = (this.connectionProtocol == OfficeConnectionProtocol.PIPE) ? UnoUrl.pipe(this.pipeNames[b]) : UnoUrl.socket(this.portNumbers[b]);
/*     */     }
/* 185 */     return new ProcessPoolOfficeManager(this.officeHome, arrayOfUnoUrl, this.runAsArgs, this.templateProfileDir, this.workDir, this.retryTimeout, this.taskQueueTimeout, this.taskExecutionTimeout, this.maxTasksPerProcess, this.processManager);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private ProcessManager findBestProcessManager() {
/* 203 */     if (PlatformUtils.isLinux()) {
/* 204 */       LinuxProcessManager linuxProcessManager = new LinuxProcessManager();
/* 205 */       if (this.runAsArgs != null) {
/* 206 */         linuxProcessManager.setRunAsArgs(this.runAsArgs);
/*     */       }
/* 208 */       return (ProcessManager)linuxProcessManager;
/*     */     } 
/*     */ 
/*     */     
/* 212 */     return (ProcessManager)new PureJavaProcessManager();
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean isSigarAvailable() {
/*     */     try {
/* 218 */       Class.forName("org.hyperic.sigar.Sigar", false, getClass().getClassLoader());
/* 219 */       return true;
/* 220 */     } catch (ClassNotFoundException classNotFoundException) {
/* 221 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   private void checkArgumentNotNull(String paramString, Object paramObject) throws NullPointerException {
/* 226 */     if (paramObject == null) {
/* 227 */       throw new NullPointerException(paramString + " must not be null");
/*     */     }
/*     */   }
/*     */   
/*     */   private void checkArgument(String paramString1, boolean paramBoolean, String paramString2) throws IllegalArgumentException {
/* 232 */     if (!paramBoolean) {
/* 233 */       throw new IllegalArgumentException(paramString1 + " " + paramString2);
/*     */     }
/*     */   }
/*     */   
/*     */   private boolean isValidProfileDir(File paramFile) {
/* 238 */     return (new File(paramFile, "user")).isDirectory();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/DefaultOfficeManagerConfiguration.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */