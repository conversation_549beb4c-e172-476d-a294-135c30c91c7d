/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OfficeException
/*    */   extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   
/*    */   public OfficeException(String paramString) {
/* 26 */     super(paramString);
/*    */   }
/*    */   
/*    */   public OfficeException(String paramString, Throwable paramThrowable) {
/* 30 */     super(paramString, paramThrowable);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/OfficeException.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */