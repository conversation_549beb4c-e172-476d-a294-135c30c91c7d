/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import com.sun.star.beans.XPropertySet;
/*     */ import com.sun.star.bridge.XBridge;
/*     */ import com.sun.star.bridge.XBridgeFactory;
/*     */ import com.sun.star.comp.helper.Bootstrap;
/*     */ import com.sun.star.connection.NoConnectException;
/*     */ import com.sun.star.connection.XConnection;
/*     */ import com.sun.star.connection.XConnector;
/*     */ import com.sun.star.lang.EventObject;
/*     */ import com.sun.star.lang.XComponent;
/*     */ import com.sun.star.lang.XEventListener;
/*     */ import com.sun.star.lang.XMultiComponentFactory;
/*     */ import com.sun.star.uno.XComponentContext;
/*     */ import java.net.ConnectException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.concurrent.atomic.AtomicInteger;
/*     */ import java.util.logging.Logger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class OfficeConnection
/*     */   implements OfficeContext
/*     */ {
/*  42 */   private static AtomicInteger bridgeIndex = new AtomicInteger();
/*     */   
/*     */   private final UnoUrl unoUrl;
/*     */   
/*     */   private XComponent bridgeComponent;
/*     */   
/*     */   private XMultiComponentFactory serviceManager;
/*     */   private XComponentContext componentContext;
/*  50 */   private final List<OfficeConnectionEventListener> connectionEventListeners = new ArrayList<OfficeConnectionEventListener>();
/*     */   
/*     */   private volatile boolean connected = false;
/*     */   
/*  54 */   private XEventListener bridgeListener = new XEventListener() {
/*     */       public void disposing(EventObject param1EventObject) {
/*  56 */         if (OfficeConnection.this.connected) {
/*  57 */           OfficeConnection.this.connected = false;
/*  58 */           OfficeConnection.this.logger.info(String.format("disconnected: '%s'", new Object[] { OfficeConnection.access$100(this.this$0) }));
/*  59 */           OfficeConnectionEvent officeConnectionEvent = new OfficeConnectionEvent(OfficeConnection.this);
/*  60 */           for (OfficeConnectionEventListener officeConnectionEventListener : OfficeConnection.this.connectionEventListeners) {
/*  61 */             officeConnectionEventListener.disconnected(officeConnectionEvent);
/*     */           }
/*     */         } 
/*     */       }
/*     */     };
/*     */ 
/*     */   
/*  68 */   private final Logger logger = Logger.getLogger(getClass().getName());
/*     */   
/*     */   public OfficeConnection(UnoUrl paramUnoUrl) {
/*  71 */     this.unoUrl = paramUnoUrl;
/*     */   }
/*     */   
/*     */   public void addConnectionEventListener(OfficeConnectionEventListener paramOfficeConnectionEventListener) {
/*  75 */     this.connectionEventListeners.add(paramOfficeConnectionEventListener);
/*     */   }
/*     */   
/*     */   public void connect() throws ConnectException {
/*  79 */     this.logger.fine(String.format("connecting with connectString '%s'", new Object[] { this.unoUrl }));
/*     */     try {
/*  81 */       XComponentContext xComponentContext = Bootstrap.createInitialComponentContext(null);
/*  82 */       XMultiComponentFactory xMultiComponentFactory = xComponentContext.getServiceManager();
/*  83 */       XConnector xConnector = OfficeUtils.<XConnector>cast(XConnector.class, xMultiComponentFactory.createInstanceWithContext("com.sun.star.connection.Connector", xComponentContext));
/*  84 */       XConnection xConnection = xConnector.connect(this.unoUrl.getConnectString());
/*  85 */       XBridgeFactory xBridgeFactory = OfficeUtils.<XBridgeFactory>cast(XBridgeFactory.class, xMultiComponentFactory.createInstanceWithContext("com.sun.star.bridge.BridgeFactory", xComponentContext));
/*  86 */       String str = "jodconverter_" + bridgeIndex.getAndIncrement();
/*  87 */       XBridge xBridge = xBridgeFactory.createBridge(str, "urp", xConnection, null);
/*  88 */       this.bridgeComponent = OfficeUtils.<XComponent>cast(XComponent.class, xBridge);
/*  89 */       this.bridgeComponent.addEventListener(this.bridgeListener);
/*  90 */       this.serviceManager = OfficeUtils.<XMultiComponentFactory>cast(XMultiComponentFactory.class, xBridge.getInstance("StarOffice.ServiceManager"));
/*  91 */       XPropertySet xPropertySet = OfficeUtils.<XPropertySet>cast(XPropertySet.class, this.serviceManager);
/*  92 */       this.componentContext = OfficeUtils.<XComponentContext>cast(XComponentContext.class, xPropertySet.getPropertyValue("DefaultContext"));
/*  93 */       this.connected = true;
/*  94 */       this.logger.info(String.format("connected: '%s'", new Object[] { this.unoUrl }));
/*  95 */       OfficeConnectionEvent officeConnectionEvent = new OfficeConnectionEvent(this);
/*  96 */       for (OfficeConnectionEventListener officeConnectionEventListener : this.connectionEventListeners) {
/*  97 */         officeConnectionEventListener.connected(officeConnectionEvent);
/*     */       }
/*  99 */     } catch (NoConnectException noConnectException) {
/* 100 */       throw new ConnectException(String.format("connection failed: '%s'; %s", new Object[] { this.unoUrl, noConnectException.getMessage() }));
/* 101 */     } catch (Exception exception) {
/* 102 */       throw new OfficeException("connection failed: " + this.unoUrl, exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public boolean isConnected() {
/* 107 */     return this.connected;
/*     */   }
/*     */   
/*     */   public synchronized void disconnect() {
/* 111 */     this.logger.fine(String.format("disconnecting: '%s'", new Object[] { this.unoUrl }));
/* 112 */     this.bridgeComponent.dispose();
/*     */   }
/*     */   
/*     */   public Object getService(String paramString) {
/*     */     try {
/* 117 */       return this.serviceManager.createInstanceWithContext(paramString, this.componentContext);
/* 118 */     } catch (Exception exception) {
/* 119 */       throw new OfficeException(String.format("failed to obtain service '%s'", new Object[] { paramString }), exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/OfficeConnection.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */