/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.concurrent.ArrayBlockingQueue;
/*     */ import java.util.concurrent.BlockingQueue;
/*     */ import java.util.concurrent.TimeUnit;
/*     */ import java.util.logging.Logger;
/*     */ import org.artofsolving.jodconverter.process.ProcessManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class ProcessPoolOfficeManager
/*     */   implements OfficeManager
/*     */ {
/*     */   private final BlockingQueue<PooledOfficeManager> pool;
/*     */   private final PooledOfficeManager[] pooledManagers;
/*     */   private final long taskQueueTimeout;
/*     */   private volatile boolean running = false;
/*  37 */   private final Logger logger = Logger.getLogger(ProcessPoolOfficeManager.class.getName());
/*     */ 
/*     */ 
/*     */   
/*     */   public ProcessPoolOfficeManager(File paramFile1, UnoUrl[] paramArrayOfUnoUrl, String[] paramArrayOfString, File paramFile2, File paramFile3, long paramLong1, long paramLong2, long paramLong3, int paramInt, ProcessManager paramProcessManager) {
/*  42 */     this.taskQueueTimeout = paramLong2;
/*  43 */     this.pool = new ArrayBlockingQueue<PooledOfficeManager>(paramArrayOfUnoUrl.length);
/*  44 */     this.pooledManagers = new PooledOfficeManager[paramArrayOfUnoUrl.length];
/*  45 */     for (byte b = 0; b < paramArrayOfUnoUrl.length; b++) {
/*  46 */       PooledOfficeManagerSettings pooledOfficeManagerSettings = new PooledOfficeManagerSettings(paramArrayOfUnoUrl[b]);
/*  47 */       pooledOfficeManagerSettings.setRunAsArgs(paramArrayOfString);
/*  48 */       pooledOfficeManagerSettings.setTemplateProfileDir(paramFile2);
/*  49 */       pooledOfficeManagerSettings.setWorkDir(paramFile3);
/*  50 */       pooledOfficeManagerSettings.setOfficeHome(paramFile1);
/*  51 */       pooledOfficeManagerSettings.setRetryTimeout(paramLong1);
/*  52 */       pooledOfficeManagerSettings.setTaskExecutionTimeout(paramLong3);
/*  53 */       pooledOfficeManagerSettings.setMaxTasksPerProcess(paramInt);
/*  54 */       pooledOfficeManagerSettings.setProcessManager(paramProcessManager);
/*  55 */       this.pooledManagers[b] = new PooledOfficeManager(pooledOfficeManagerSettings);
/*     */     } 
/*  57 */     this.logger.info("ProcessManager implementation is " + paramProcessManager.getClass().getSimpleName());
/*     */   }
/*     */   
/*     */   public synchronized void start() throws OfficeException {
/*  61 */     for (byte b = 0; b < this.pooledManagers.length; b++) {
/*  62 */       this.pooledManagers[b].start();
/*  63 */       releaseManager(this.pooledManagers[b]);
/*     */     } 
/*  65 */     this.running = true;
/*     */   }
/*     */   
/*     */   public void execute(OfficeTask paramOfficeTask) throws IllegalStateException, OfficeException {
/*  69 */     if (!this.running) {
/*  70 */       throw new IllegalStateException("this OfficeManager is currently stopped");
/*     */     }
/*  72 */     PooledOfficeManager pooledOfficeManager = null;
/*     */     try {
/*  74 */       pooledOfficeManager = acquireManager();
/*  75 */       if (pooledOfficeManager == null) {
/*  76 */         throw new OfficeException("no office manager available");
/*     */       }
/*  78 */       pooledOfficeManager.execute(paramOfficeTask);
/*     */     } finally {
/*  80 */       if (pooledOfficeManager != null) {
/*  81 */         releaseManager(pooledOfficeManager);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public synchronized void stop() throws OfficeException {
/*  87 */     this.running = false;
/*  88 */     this.logger.info("stopping");
/*  89 */     this.pool.clear();
/*  90 */     for (byte b = 0; b < this.pooledManagers.length; b++) {
/*  91 */       this.pooledManagers[b].stop();
/*     */     }
/*  93 */     this.logger.info("stopped");
/*     */   }
/*     */   
/*     */   private PooledOfficeManager acquireManager() {
/*     */     try {
/*  98 */       return this.pool.poll(this.taskQueueTimeout, TimeUnit.MILLISECONDS);
/*  99 */     } catch (InterruptedException interruptedException) {
/* 100 */       throw new OfficeException("interrupted", interruptedException);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void releaseManager(PooledOfficeManager paramPooledOfficeManager) {
/*     */     try {
/* 106 */       this.pool.put(paramPooledOfficeManager);
/* 107 */     } catch (InterruptedException interruptedException) {
/* 108 */       throw new OfficeException("interrupted", interruptedException);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/ProcessPoolOfficeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */