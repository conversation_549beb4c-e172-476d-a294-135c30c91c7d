/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ abstract class Retryable
/*    */ {
/*    */   protected abstract void attempt() throws TemporaryException, Exception;
/*    */   
/*    */   public void execute(long paramLong1, long paramLong2) throws RetryTimeoutException, Exception {
/* 30 */     execute(0L, paramLong1, paramLong2);
/*    */   }
/*    */   
/*    */   public void execute(long paramLong1, long paramLong2, long paramLong3) throws RetryTimeoutException, Exception {
/* 34 */     long l = System.currentTimeMillis();
/* 35 */     if (paramLong1 > 0L) {
/* 36 */       sleep(paramLong1);
/*    */     }
/*    */     while (true) {
/*    */       try {
/* 40 */         attempt();
/*    */         return;
/* 42 */       } catch (TemporaryException temporaryException) {
/* 43 */         if (System.currentTimeMillis() - l < paramLong3) {
/* 44 */           sleep(paramLong2); continue;
/*    */         }  break;
/*    */       } 
/* 47 */     }  throw new RetryTimeoutException(temporaryException.getCause());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void sleep(long paramLong) {
/*    */     try {
/* 55 */       Thread.sleep(paramLong);
/* 56 */     } catch (InterruptedException interruptedException) {}
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/Retryable.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */