/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class TemporaryException
/*    */   extends Exception
/*    */ {
/*    */   private static final long serialVersionUID = 7237380113208327295L;
/*    */   
/*    */   public TemporaryException(Throwable paramThrowable) {
/* 30 */     super(paramThrowable);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/TemporaryException.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */