/*    */ package org.artofsolving.jodconverter.office;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ExternalOfficeManagerConfiguration
/*    */ {
/* 23 */   private OfficeConnectionProtocol connectionProtocol = OfficeConnectionProtocol.SOCKET;
/* 24 */   private int portNumber = 2002;
/* 25 */   private String pipeName = "office";
/*    */   private boolean connectOnStart = true;
/*    */   
/*    */   public ExternalOfficeManagerConfiguration setConnectionProtocol(OfficeConnectionProtocol paramOfficeConnectionProtocol) {
/* 29 */     this.connectionProtocol = paramOfficeConnectionProtocol;
/* 30 */     return this;
/*    */   }
/*    */   
/*    */   public ExternalOfficeManagerConfiguration setPortNumber(int paramInt) {
/* 34 */     this.portNumber = paramInt;
/* 35 */     return this;
/*    */   }
/*    */   
/*    */   public ExternalOfficeManagerConfiguration setPipeName(String paramString) {
/* 39 */     this.pipeName = paramString;
/* 40 */     return this;
/*    */   }
/*    */   
/*    */   public ExternalOfficeManagerConfiguration setConnectOnStart(boolean paramBoolean) {
/* 44 */     this.connectOnStart = paramBoolean;
/* 45 */     return this;
/*    */   }
/*    */   
/*    */   public OfficeManager buildOfficeManager() {
/* 49 */     UnoUrl unoUrl = (this.connectionProtocol == OfficeConnectionProtocol.SOCKET) ? UnoUrl.socket(this.portNumber) : UnoUrl.pipe(this.pipeName);
/* 50 */     return new ExternalOfficeManager(unoUrl, this.connectOnStart);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/ExternalOfficeManagerConfiguration.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */