/*     */ package org.artofsolving.jodconverter.office;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Map;
/*     */ import java.util.logging.Logger;
/*     */ import org.apache.commons.io.FileUtils;
/*     */ import org.artofsolving.jodconverter.process.ProcessManager;
/*     */ import org.artofsolving.jodconverter.process.ProcessQuery;
/*     */ import org.artofsolving.jodconverter.util.PlatformUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class OfficeProcess
/*     */ {
/*     */   private final File officeHome;
/*     */   private final UnoUrl unoUrl;
/*     */   private final String[] runAsArgs;
/*     */   private final File templateProfileDir;
/*     */   private final File instanceProfileDir;
/*     */   private final ProcessManager processManager;
/*     */   private Process process;
/*  46 */   private long pid = -1L;
/*     */   
/*  48 */   private final Logger logger = Logger.getLogger(getClass().getName());
/*     */   
/*     */   public OfficeProcess(File paramFile1, UnoUrl paramUnoUrl, String[] paramArrayOfString, File paramFile2, File paramFile3, ProcessManager paramProcessManager) {
/*  51 */     this.officeHome = paramFile1;
/*  52 */     this.unoUrl = paramUnoUrl;
/*  53 */     this.runAsArgs = paramArrayOfString;
/*  54 */     this.templateProfileDir = paramFile2;
/*  55 */     this.instanceProfileDir = getInstanceProfileDir(paramFile3, paramUnoUrl);
/*  56 */     this.processManager = paramProcessManager;
/*     */   }
/*     */   
/*     */   public void start() throws IOException {
/*  60 */     start(false);
/*     */   }
/*     */   
/*     */   public void start(boolean paramBoolean) throws IOException {
/*  64 */     ProcessQuery processQuery = new ProcessQuery("soffice.bin", this.unoUrl.getAcceptString());
/*  65 */     long l = this.processManager.findPid(processQuery);
/*  66 */     if (l != -1L) {
/*  67 */       throw new IllegalStateException(String.format("a process with acceptString '%s' is already running; pid %d", new Object[] { this.unoUrl.getAcceptString(), Long.valueOf(l) }));
/*     */     }
/*     */     
/*  70 */     if (!paramBoolean) {
/*  71 */       prepareInstanceProfileDir();
/*     */     }
/*  73 */     ArrayList<String> arrayList = new ArrayList();
/*  74 */     File file = OfficeUtils.getOfficeExecutable(this.officeHome);
/*  75 */     if (this.runAsArgs != null) {
/*  76 */       arrayList.addAll(Arrays.asList(this.runAsArgs));
/*     */     }
/*  78 */     arrayList.add(file.getAbsolutePath());
/*  79 */     arrayList.add("-accept=" + this.unoUrl.getAcceptString() + ";urp;");
/*  80 */     arrayList.add("-env:UserInstallation=" + OfficeUtils.toUrl(this.instanceProfileDir));
/*  81 */     arrayList.add("-headless");
/*  82 */     arrayList.add("-nocrashreport");
/*  83 */     arrayList.add("-nodefault");
/*  84 */     arrayList.add("-nofirststartwizard");
/*  85 */     arrayList.add("-nolockcheck");
/*  86 */     arrayList.add("-nologo");
/*  87 */     arrayList.add("-norestore");
/*  88 */     ProcessBuilder processBuilder = new ProcessBuilder(arrayList);
/*  89 */     if (PlatformUtils.isWindows()) {
/*  90 */       addBasisAndUrePaths(processBuilder);
/*     */     }
/*  92 */     this.logger.info(String.format("starting process with acceptString '%s' and profileDir '%s'", new Object[] { this.unoUrl, this.instanceProfileDir }));
/*  93 */     this.process = processBuilder.start();
/*  94 */     this.pid = this.processManager.findPid(processQuery);
/*  95 */     this.logger.info("started process" + ((this.pid != -1L) ? ("; pid = " + this.pid) : ""));
/*     */   }
/*     */   
/*     */   private File getInstanceProfileDir(File paramFile, UnoUrl paramUnoUrl) {
/*  99 */     String str = ".jodconverter_" + paramUnoUrl.getAcceptString().replace(',', '_').replace('=', '-');
/* 100 */     return new File(paramFile, str);
/*     */   }
/*     */   
/*     */   private void prepareInstanceProfileDir() throws OfficeException {
/* 104 */     if (this.instanceProfileDir.exists()) {
/* 105 */       this.logger.warning(String.format("profile dir '%s' already exists; deleting", new Object[] { this.instanceProfileDir }));
/* 106 */       deleteProfileDir();
/*     */     } 
/* 108 */     if (this.templateProfileDir != null) {
/*     */       try {
/* 110 */         FileUtils.copyDirectory(this.templateProfileDir, this.instanceProfileDir);
/* 111 */       } catch (IOException iOException) {
/* 112 */         throw new OfficeException("failed to create profileDir", iOException);
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   public void deleteProfileDir() {
/* 118 */     if (this.instanceProfileDir != null) {
/*     */       try {
/* 120 */         FileUtils.deleteDirectory(this.instanceProfileDir);
/* 121 */       } catch (IOException iOException) {
/* 122 */         File file = new File(this.instanceProfileDir.getParentFile(), this.instanceProfileDir.getName() + ".old." + System.currentTimeMillis());
/* 123 */         if (this.instanceProfileDir.renameTo(file)) {
/* 124 */           this.logger.warning("could not delete profileDir: " + iOException.getMessage() + "; renamed it to " + file);
/*     */         } else {
/* 126 */           this.logger.severe("could not delete profileDir: " + iOException.getMessage());
/*     */         } 
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   private void addBasisAndUrePaths(ProcessBuilder paramProcessBuilder) throws IOException {
/* 134 */     File file1 = new File(this.officeHome, "basis-link");
/* 135 */     if (!file1.isFile()) {
/* 136 */       this.logger.fine("no %OFFICE_HOME%/basis-link found; assuming it's OOo 2.x and we don't need to append URE and Basic paths");
/*     */       return;
/*     */     } 
/* 139 */     String str1 = FileUtils.readFileToString(file1).trim();
/* 140 */     File file2 = new File(this.officeHome, str1);
/* 141 */     File file3 = new File(file2, "program");
/* 142 */     File file4 = new File(file2, "ure-link");
/* 143 */     String str2 = FileUtils.readFileToString(file4).trim();
/* 144 */     File file5 = new File(file2, str2);
/* 145 */     File file6 = new File(file5, "bin");
/* 146 */     Map<String, String> map = paramProcessBuilder.environment();
/*     */ 
/*     */     
/* 149 */     String str3 = "PATH";
/* 150 */     for (String str : map.keySet()) {
/* 151 */       if ("PATH".equalsIgnoreCase(str)) {
/* 152 */         str3 = str;
/*     */       }
/*     */     } 
/* 155 */     String str4 = (String)map.get(str3) + ";" + file6.getAbsolutePath() + ";" + file3.getAbsolutePath();
/* 156 */     this.logger.fine(String.format("setting %s to \"%s\"", new Object[] { str3, str4 }));
/* 157 */     map.put(str3, str4);
/*     */   }
/*     */   
/*     */   public boolean isRunning() {
/* 161 */     if (this.process == null) {
/* 162 */       return false;
/*     */     }
/* 164 */     return (getExitCode() == null);
/*     */   }
/*     */   
/*     */   private class ExitCodeRetryable
/*     */     extends Retryable {
/*     */     private int exitCode;
/*     */     
/*     */     protected void attempt() throws TemporaryException, Exception {
/*     */       try {
/* 173 */         this.exitCode = OfficeProcess.this.process.exitValue();
/* 174 */       } catch (IllegalThreadStateException illegalThreadStateException) {
/* 175 */         throw new TemporaryException(illegalThreadStateException);
/*     */       } 
/*     */     }
/*     */     private ExitCodeRetryable() {}
/*     */     public int getExitCode() {
/* 180 */       return this.exitCode;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public Integer getExitCode() {
/*     */     try {
/* 187 */       return Integer.valueOf(this.process.exitValue());
/* 188 */     } catch (IllegalThreadStateException illegalThreadStateException) {
/* 189 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public int getExitCode(long paramLong1, long paramLong2) throws RetryTimeoutException {
/*     */     try {
/* 195 */       ExitCodeRetryable exitCodeRetryable = new ExitCodeRetryable();
/* 196 */       exitCodeRetryable.execute(paramLong1, paramLong2);
/* 197 */       return exitCodeRetryable.getExitCode();
/* 198 */     } catch (RetryTimeoutException retryTimeoutException) {
/* 199 */       throw retryTimeoutException;
/* 200 */     } catch (Exception exception) {
/* 201 */       throw new OfficeException("could not get process exit code", exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public int forciblyTerminate(long paramLong1, long paramLong2) throws IOException, RetryTimeoutException {
/* 206 */     this.logger.info(String.format("trying to forcibly terminate process: '" + this.unoUrl + "'" + ((this.pid != -1L) ? (" (pid " + this.pid + ")") : ""), new Object[0]));
/* 207 */     this.processManager.kill(this.process, this.pid);
/* 208 */     return getExitCode(paramLong1, paramLong2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/OfficeProcess.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */