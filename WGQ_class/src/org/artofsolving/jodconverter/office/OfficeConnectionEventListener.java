package org.artofsolving.jodconverter.office;

import java.util.EventListener;

interface OfficeConnectionEventListener extends EventListener {
  void connected(OfficeConnectionEvent paramOfficeConnectionEvent);
  
  void disconnected(OfficeConnectionEvent paramOfficeConnectionEvent);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/office/OfficeConnectionEventListener.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */