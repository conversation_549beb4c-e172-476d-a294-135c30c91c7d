/*    */ package org.artofsolving.jodconverter.process;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PureJavaProcessManager
/*    */   implements ProcessManager
/*    */ {
/*    */   public long findPid(ProcessQuery paramProcessQuery) {
/* 24 */     return -1L;
/*    */   }
/*    */   
/*    */   public void kill(Process paramProcess, long paramLong) {
/* 28 */     paramProcess.destroy();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/process/PureJavaProcessManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */