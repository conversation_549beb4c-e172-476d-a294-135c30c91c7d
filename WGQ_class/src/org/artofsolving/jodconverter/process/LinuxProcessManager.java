/*    */ package org.artofsolving.jodconverter.process;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LinuxProcessManager
/*    */   implements ProcessManager
/*    */ {
/* 38 */   private static final Pattern PS_OUTPUT_LINE = Pattern.compile("^\\s*(\\d+)\\s+(.*)$");
/*    */   
/*    */   private String[] runAsArgs;
/*    */   
/*    */   public void setRunAsArgs(String... paramVarArgs) {
/* 43 */     this.runAsArgs = paramVarArgs;
/*    */   }
/*    */   
/*    */   protected String[] psCommand() {
/* 47 */     return new String[] { "/bin/ps", "-e", "-o", "pid,args" };
/*    */   }
/*    */   
/*    */   public long findPid(ProcessQuery paramProcessQuery) throws IOException {
/* 51 */     String str = Pattern.quote(paramProcessQuery.getCommand()) + ".*" + Pattern.quote(paramProcessQuery.getArgument());
/* 52 */     Pattern pattern = Pattern.compile(str);
/* 53 */     for (String str1 : execute(psCommand())) {
/* 54 */       Matcher matcher = PS_OUTPUT_LINE.matcher(str1);
/* 55 */       if (matcher.matches()) {
/* 56 */         String str2 = matcher.group(2);
/* 57 */         Matcher matcher1 = pattern.matcher(str2);
/* 58 */         if (matcher1.find()) {
/* 59 */           return Long.parseLong(matcher.group(1));
/*    */         }
/*    */       } 
/*    */     } 
/* 63 */     return -1L;
/*    */   }
/*    */   
/*    */   public void kill(Process paramProcess, long paramLong) throws IOException {
/* 67 */     execute(new String[] { "/bin/kill", "-KILL", Long.toString(paramLong) });
/*    */   }
/*    */   
/*    */   private List<String> execute(String... paramVarArgs) throws IOException {
/*    */     String[] arrayOfString;
/* 72 */     if (this.runAsArgs != null) {
/* 73 */       arrayOfString = new String[this.runAsArgs.length + paramVarArgs.length];
/* 74 */       System.arraycopy(this.runAsArgs, 0, arrayOfString, 0, this.runAsArgs.length);
/* 75 */       System.arraycopy(paramVarArgs, 0, arrayOfString, this.runAsArgs.length, paramVarArgs.length);
/*    */     } else {
/* 77 */       arrayOfString = paramVarArgs;
/*    */     } 
/* 79 */     Process process = (new ProcessBuilder(arrayOfString)).start();
/*    */     
/* 81 */     return IOUtils.readLines(process.getInputStream());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/process/LinuxProcessManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */