/*    */ package org.artofsolving.jodconverter.process;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ProcessQuery
/*    */ {
/*    */   private final String command;
/*    */   private final String argument;
/*    */   
/*    */   public ProcessQuery(String paramString1, String paramString2) {
/* 27 */     this.command = paramString1;
/* 28 */     this.argument = paramString2;
/*    */   }
/*    */   
/*    */   public String getCommand() {
/* 32 */     return this.command;
/*    */   }
/*    */   
/*    */   public String getArgument() {
/* 36 */     return this.argument;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/process/ProcessQuery.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */