package org.artofsolving.jodconverter.process;

import java.io.IOException;

public interface ProcessManager {
  public static final long PID_UNKNOWN = -1L;
  
  void kill(Process paramProcess, long paramLong) throws IOException;
  
  long findPid(ProcessQuery paramProcessQuery) throws IOException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/process/ProcessManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */