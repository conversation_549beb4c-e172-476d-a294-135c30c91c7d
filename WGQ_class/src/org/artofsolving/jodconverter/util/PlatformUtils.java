/*    */ package org.artofsolving.jodconverter.util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PlatformUtils
/*    */ {
/* 23 */   private static final String OS_NAME = System.getProperty("os.name").toLowerCase();
/*    */   
/*    */   private PlatformUtils() {
/* 26 */     throw new AssertionError("utility class must not be instantiated");
/*    */   }
/*    */   
/*    */   public static boolean isLinux() {
/* 30 */     return OS_NAME.startsWith("linux");
/*    */   }
/*    */   
/*    */   public static boolean isMac() {
/* 34 */     return OS_NAME.startsWith("mac");
/*    */   }
/*    */   
/*    */   public static boolean isWindows() {
/* 38 */     return OS_NAME.startsWith("windows");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/util/PlatformUtils.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */