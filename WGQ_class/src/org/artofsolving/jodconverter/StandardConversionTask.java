/*    */ package org.artofsolving.jodconverter;
/*    */ 
/*    */ import com.sun.star.lang.XComponent;
/*    */ import com.sun.star.util.XRefreshable;
/*    */ import java.io.File;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.artofsolving.jodconverter.document.DocumentFamily;
/*    */ import org.artofsolving.jodconverter.document.DocumentFormat;
/*    */ import org.artofsolving.jodconverter.office.OfficeException;
/*    */ import org.artofsolving.jodconverter.office.OfficeUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class StandardConversionTask
/*    */   extends AbstractConversionTask
/*    */ {
/*    */   private final DocumentFormat outputFormat;
/*    */   private Map<String, ?> defaultLoadProperties;
/*    */   private DocumentFormat inputFormat;
/*    */   
/*    */   public StandardConversionTask(File paramFile1, File paramFile2, DocumentFormat paramDocumentFormat) {
/* 42 */     super(paramFile1, paramFile2);
/* 43 */     this.outputFormat = paramDocumentFormat;
/*    */   }
/*    */   
/*    */   public void setDefaultLoadProperties(Map<String, ?> paramMap) {
/* 47 */     this.defaultLoadProperties = paramMap;
/*    */   }
/*    */   
/*    */   public void setInputFormat(DocumentFormat paramDocumentFormat) {
/* 51 */     this.inputFormat = paramDocumentFormat;
/*    */   }
/*    */ 
/*    */   
/*    */   protected void modifyDocument(XComponent paramXComponent) throws OfficeException {
/* 56 */     XRefreshable xRefreshable = (XRefreshable)OfficeUtils.cast(XRefreshable.class, paramXComponent);
/* 57 */     if (xRefreshable != null) {
/* 58 */       xRefreshable.refresh();
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   protected Map<String, ?> getLoadProperties(File paramFile) {
/* 64 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 65 */     if (this.defaultLoadProperties != null) {
/* 66 */       hashMap.putAll(this.defaultLoadProperties);
/*    */     }
/* 68 */     if (this.inputFormat != null && this.inputFormat.getLoadProperties() != null) {
/* 69 */       hashMap.putAll(this.inputFormat.getLoadProperties());
/*    */     }
/* 71 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   protected Map<String, ?> getStoreProperties(File paramFile, XComponent paramXComponent) {
/* 76 */     DocumentFamily documentFamily = OfficeDocumentUtils.getDocumentFamily(paramXComponent);
/* 77 */     return this.outputFormat.getStoreProperties(documentFamily);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/artofsolving/jodconverter/StandardConversionTask.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */