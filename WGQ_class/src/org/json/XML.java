/*     */ package org.json;
/*     */ 
/*     */ import java.util.Iterator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class XML
/*     */ {
/*  39 */   public static final Character AMP = new Character('&');
/*     */ 
/*     */   
/*  42 */   public static final Character APOS = new Character('\'');
/*     */ 
/*     */   
/*  45 */   public static final Character BANG = new Character('!');
/*     */ 
/*     */   
/*  48 */   public static final Character EQ = new Character('=');
/*     */ 
/*     */   
/*  51 */   public static final Character GT = new Character('>');
/*     */ 
/*     */   
/*  54 */   public static final Character LT = new Character('<');
/*     */ 
/*     */   
/*  57 */   public static final Character QUEST = new Character('?');
/*     */ 
/*     */   
/*  60 */   public static final Character QUOT = new Character('"');
/*     */ 
/*     */   
/*  63 */   public static final Character SLASH = new Character('/');
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String escape(String paramString) {
/*  77 */     StringBuffer stringBuffer = new StringBuffer(); byte b; int i;
/*  78 */     for (b = 0, i = paramString.length(); b < i; b++) {
/*  79 */       char c = paramString.charAt(b);
/*  80 */       switch (c) {
/*     */         case '&':
/*  82 */           stringBuffer.append("&amp;");
/*     */           break;
/*     */         case '<':
/*  85 */           stringBuffer.append("&lt;");
/*     */           break;
/*     */         case '>':
/*  88 */           stringBuffer.append("&gt;");
/*     */           break;
/*     */         case '"':
/*  91 */           stringBuffer.append("&quot;");
/*     */           break;
/*     */         default:
/*  94 */           stringBuffer.append(c); break;
/*     */       } 
/*     */     } 
/*  97 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean parse(XMLTokener paramXMLTokener, JSONObject paramJSONObject, String paramString) throws JSONException {
/* 113 */     JSONObject jSONObject = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 127 */     Object object = paramXMLTokener.nextToken();
/*     */ 
/*     */ 
/*     */     
/* 131 */     if (object == BANG) {
/* 132 */       char c = paramXMLTokener.next();
/* 133 */       if (c == '-') {
/* 134 */         if (paramXMLTokener.next() == '-') {
/* 135 */           paramXMLTokener.skipPast("-->");
/* 136 */           return false;
/*     */         } 
/* 138 */         paramXMLTokener.back();
/* 139 */       } else if (c == '[') {
/* 140 */         object = paramXMLTokener.nextToken();
/* 141 */         if (object.equals("CDATA") && 
/* 142 */           paramXMLTokener.next() == '[') {
/* 143 */           String str1 = paramXMLTokener.nextCDATA();
/* 144 */           if (str1.length() > 0) {
/* 145 */             paramJSONObject.accumulate("content", str1);
/*     */           }
/* 147 */           return false;
/*     */         } 
/*     */         
/* 150 */         throw paramXMLTokener.syntaxError("Expected 'CDATA['");
/*     */       } 
/* 152 */       byte b = 1;
/*     */       while (true)
/* 154 */       { object = paramXMLTokener.nextMeta();
/* 155 */         if (object == null)
/* 156 */           throw paramXMLTokener.syntaxError("Missing '>' after '<!'."); 
/* 157 */         if (object == LT) {
/* 158 */           b++;
/* 159 */         } else if (object == GT) {
/* 160 */           b--;
/*     */         } 
/* 162 */         if (b <= 0)
/* 163 */           return false;  } 
/* 164 */     }  if (object == QUEST) {
/*     */ 
/*     */ 
/*     */       
/* 168 */       paramXMLTokener.skipPast("?>");
/* 169 */       return false;
/* 170 */     }  if (object == SLASH) {
/*     */ 
/*     */ 
/*     */       
/* 174 */       object = paramXMLTokener.nextToken();
/* 175 */       if (paramString == null) {
/* 176 */         throw paramXMLTokener.syntaxError("Mismatched close tag" + object);
/*     */       }
/* 178 */       if (!object.equals(paramString)) {
/* 179 */         throw paramXMLTokener.syntaxError("Mismatched " + paramString + " and " + object);
/*     */       }
/* 181 */       if (paramXMLTokener.nextToken() != GT) {
/* 182 */         throw paramXMLTokener.syntaxError("Misshaped close tag");
/*     */       }
/* 184 */       return true;
/*     */     } 
/* 186 */     if (object instanceof Character) {
/* 187 */       throw paramXMLTokener.syntaxError("Misshaped tag");
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 192 */     String str = (String)object;
/* 193 */     object = null;
/* 194 */     jSONObject = new JSONObject();
/*     */     while (true) {
/* 196 */       if (object == null) {
/* 197 */         object = paramXMLTokener.nextToken();
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 202 */       if (object instanceof String) {
/* 203 */         String str1 = (String)object;
/* 204 */         object = paramXMLTokener.nextToken();
/* 205 */         if (object == EQ) {
/* 206 */           object = paramXMLTokener.nextToken();
/* 207 */           if (!(object instanceof String)) {
/* 208 */             throw paramXMLTokener.syntaxError("Missing value");
/*     */           }
/* 210 */           jSONObject.accumulate(str1, object);
/* 211 */           object = null; continue;
/*     */         } 
/* 213 */         jSONObject.accumulate(str1, "");
/*     */         continue;
/*     */       } 
/*     */       break;
/*     */     } 
/* 218 */     if (object == SLASH) {
/* 219 */       if (paramXMLTokener.nextToken() != GT) {
/* 220 */         throw paramXMLTokener.syntaxError("Misshaped tag");
/*     */       }
/* 222 */       paramJSONObject.accumulate(str, jSONObject);
/* 223 */       return false;
/*     */     } 
/*     */ 
/*     */     
/* 227 */     if (object == GT) {
/*     */       while (true) {
/* 229 */         object = paramXMLTokener.nextContent();
/* 230 */         if (object == null) {
/* 231 */           if (str != null) {
/* 232 */             throw paramXMLTokener.syntaxError("Unclosed tag " + str);
/*     */           }
/* 234 */           return false;
/* 235 */         }  if (object instanceof String) {
/* 236 */           String str1 = (String)object;
/* 237 */           if (str1.length() > 0) {
/* 238 */             jSONObject.accumulate("content", str1);
/*     */           }
/*     */           
/*     */           continue;
/*     */         } 
/* 243 */         if (object == LT && 
/* 244 */           parse(paramXMLTokener, jSONObject, str)) {
/* 245 */           if (jSONObject.length() == 0) {
/* 246 */             paramJSONObject.accumulate(str, "");
/* 247 */           } else if (jSONObject.length() == 1 && jSONObject.opt("content") != null) {
/*     */             
/* 249 */             paramJSONObject.accumulate(str, jSONObject.opt("content"));
/*     */           } else {
/* 251 */             paramJSONObject.accumulate(str, jSONObject);
/*     */           } 
/* 253 */           return false;
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/* 258 */     throw paramXMLTokener.syntaxError("Misshaped tag");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject toJSONObject(String paramString) throws JSONException {
/* 280 */     JSONObject jSONObject = new JSONObject();
/* 281 */     XMLTokener xMLTokener = new XMLTokener(paramString);
/* 282 */     while (xMLTokener.more() && xMLTokener.skipPast("<")) {
/* 283 */       parse(xMLTokener, jSONObject, null);
/*     */     }
/* 285 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(Object paramObject) throws JSONException {
/* 296 */     return toString(paramObject, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(Object paramObject, String paramString) throws JSONException {
/* 309 */     StringBuffer stringBuffer = new StringBuffer();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 318 */     if (paramObject instanceof JSONObject) {
/*     */ 
/*     */ 
/*     */       
/* 322 */       if (paramString != null) {
/* 323 */         stringBuffer.append('<');
/* 324 */         stringBuffer.append(paramString);
/* 325 */         stringBuffer.append('>');
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 330 */       JSONObject jSONObject = (JSONObject)paramObject;
/* 331 */       Iterator<E> iterator = jSONObject.keys();
/* 332 */       while (iterator.hasNext()) {
/* 333 */         String str1 = iterator.next().toString();
/* 334 */         Object object = jSONObject.get(str1);
/* 335 */         if (object instanceof String) {
/* 336 */           String str2 = (String)object;
/*     */         } else {
/* 338 */           Object object1 = null;
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 343 */         if (str1.equals("content")) {
/* 344 */           if (object instanceof JSONArray) {
/* 345 */             JSONArray jSONArray = (JSONArray)object;
/* 346 */             int i = jSONArray.length();
/* 347 */             for (byte b = 0; b < i; b++) {
/* 348 */               if (b > 0) {
/* 349 */                 stringBuffer.append('\n');
/*     */               }
/* 351 */               stringBuffer.append(escape(jSONArray.get(b).toString()));
/*     */             }  continue;
/*     */           } 
/* 354 */           stringBuffer.append(escape(object.toString()));
/*     */           
/*     */           continue;
/*     */         } 
/*     */         
/* 359 */         if (object instanceof JSONArray) {
/* 360 */           JSONArray jSONArray = (JSONArray)object;
/* 361 */           int i = jSONArray.length();
/* 362 */           for (byte b = 0; b < i; b++)
/* 363 */             stringBuffer.append(toString(jSONArray.get(b), str1));  continue;
/*     */         } 
/* 365 */         if (object.equals("")) {
/* 366 */           stringBuffer.append('<');
/* 367 */           stringBuffer.append(str1);
/* 368 */           stringBuffer.append("/>");
/*     */           
/*     */           continue;
/*     */         } 
/*     */         
/* 373 */         stringBuffer.append(toString(object, str1));
/*     */       } 
/*     */       
/* 376 */       if (paramString != null) {
/*     */ 
/*     */ 
/*     */         
/* 380 */         stringBuffer.append("</");
/* 381 */         stringBuffer.append(paramString);
/* 382 */         stringBuffer.append('>');
/*     */       } 
/* 384 */       return stringBuffer.toString();
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 389 */     if (paramObject instanceof JSONArray) {
/* 390 */       JSONArray jSONArray = (JSONArray)paramObject;
/* 391 */       int i = jSONArray.length();
/* 392 */       for (byte b = 0; b < i; b++) {
/* 393 */         stringBuffer.append(toString(jSONArray.opt(b), (paramString == null) ? "array" : paramString));
/*     */       }
/*     */       
/* 396 */       return stringBuffer.toString();
/*     */     } 
/* 398 */     String str = (paramObject == null) ? "null" : escape(paramObject.toString());
/* 399 */     return (paramString == null) ? ("\"" + str + "\"") : ((str.length() == 0) ? ("<" + paramString + "/>") : ("<" + paramString + ">" + str + "</" + paramString + ">"));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/XML.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */