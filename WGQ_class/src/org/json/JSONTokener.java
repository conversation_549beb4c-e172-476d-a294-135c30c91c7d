/*     */ package org.json;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JSONTokener
/*     */ {
/*     */   private int myIndex;
/*     */   private String mySource;
/*     */   
/*     */   public JSONTokener(String paramString) {
/*  54 */     this.myIndex = 0;
/*  55 */     this.mySource = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void back() {
/*  65 */     if (this.myIndex > 0) {
/*  66 */       this.myIndex--;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int dehexchar(char paramChar) {
/*  79 */     if (paramChar >= '0' && paramChar <= '9') {
/*  80 */       return paramChar - 48;
/*     */     }
/*  82 */     if (paramChar >= 'A' && paramChar <= 'F') {
/*  83 */       return paramChar - 55;
/*     */     }
/*  85 */     if (paramChar >= 'a' && paramChar <= 'f') {
/*  86 */       return paramChar - 87;
/*     */     }
/*  88 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean more() {
/*  98 */     return (this.myIndex < this.mySource.length());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public char next() {
/* 108 */     if (more()) {
/* 109 */       char c = this.mySource.charAt(this.myIndex);
/* 110 */       this.myIndex++;
/* 111 */       return c;
/*     */     } 
/* 113 */     return Character.MIN_VALUE;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public char next(char paramChar) throws JSONException {
/* 125 */     char c = next();
/* 126 */     if (c != paramChar) {
/* 127 */       throw syntaxError("Expected '" + paramChar + "' and instead saw '" + c + "'");
/*     */     }
/*     */     
/* 130 */     return c;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String next(int paramInt) throws JSONException {
/* 144 */     int i = this.myIndex;
/* 145 */     int j = i + paramInt;
/* 146 */     if (j >= this.mySource.length()) {
/* 147 */       throw syntaxError("Substring bounds error");
/*     */     }
/* 149 */     this.myIndex += paramInt;
/* 150 */     return this.mySource.substring(i, j);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public char nextClean() throws JSONException {
/*     */     char c;
/*     */     label38: 
/* 162 */     do { c = next();
/* 163 */       if (c == '/') {
/* 164 */         switch (next()) {
/*     */           case '/':
/*     */             while (true) {
/* 167 */               c = next();
/* 168 */               if (c != '\n') { if (c != '\r') { if (c == '\000')
/*     */                     continue label38;  continue; }  continue label38; }  continue label38;
/*     */             } 
/*     */           case '*':
/* 172 */             while (true) { c = next();
/* 173 */               if (c == '\000') {
/* 174 */                 throw syntaxError("Unclosed comment");
/*     */               }
/* 176 */               if (c == '*') {
/* 177 */                 if (next() == '/') {
/*     */                   continue label38;
/*     */                 }
/* 180 */                 back();
/*     */               }  }
/*     */           
/*     */         } 
/*     */         
/* 185 */         back();
/* 186 */         return '/';
/*     */       } 
/* 188 */       if (c != '#')
/*     */         continue;  while (true)
/* 190 */       { c = next();
/* 191 */         if (c != '\n') { if (c != '\r') { if (c == '\000')
/* 192 */               continue label38;  continue; }  continue label38; }  continue label38; }  } while (c != '\000' && c <= ' ');
/* 193 */     return c;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String nextString(char paramChar) throws JSONException {
/* 212 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     while (true) {
/* 214 */       char c = next();
/* 215 */       switch (c) {
/*     */         case '\000':
/*     */         case '\n':
/*     */         case '\r':
/* 219 */           throw syntaxError("Unterminated string");
/*     */         case '\\':
/* 221 */           c = next();
/* 222 */           switch (c) {
/*     */             case 'b':
/* 224 */               stringBuffer.append('\b');
/*     */               continue;
/*     */             case 't':
/* 227 */               stringBuffer.append('\t');
/*     */               continue;
/*     */             case 'n':
/* 230 */               stringBuffer.append('\n');
/*     */               continue;
/*     */             case 'f':
/* 233 */               stringBuffer.append('\f');
/*     */               continue;
/*     */             case 'r':
/* 236 */               stringBuffer.append('\r');
/*     */               continue;
/*     */             case 'u':
/* 239 */               stringBuffer.append((char)Integer.parseInt(next(4), 16));
/*     */               continue;
/*     */             case 'x':
/* 242 */               stringBuffer.append((char)Integer.parseInt(next(2), 16));
/*     */               continue;
/*     */           } 
/* 245 */           stringBuffer.append(c);
/*     */           continue;
/*     */       } 
/*     */       
/* 249 */       if (c == paramChar) {
/* 250 */         return stringBuffer.toString();
/*     */       }
/* 252 */       stringBuffer.append(c);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String nextTo(char paramChar) {
/* 265 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     while (true) {
/* 267 */       char c = next();
/* 268 */       if (c == paramChar || c == '\000' || c == '\n' || c == '\r') {
/* 269 */         if (c != '\000') {
/* 270 */           back();
/*     */         }
/* 272 */         return stringBuffer.toString().trim();
/*     */       } 
/* 274 */       stringBuffer.append(c);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String nextTo(String paramString) {
/* 287 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     while (true) {
/* 289 */       char c = next();
/* 290 */       if (paramString.indexOf(c) >= 0 || c == '\000' || c == '\n' || c == '\r') {
/*     */         
/* 292 */         if (c != '\000') {
/* 293 */           back();
/*     */         }
/* 295 */         return stringBuffer.toString().trim();
/*     */       } 
/* 297 */       stringBuffer.append(c);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object nextValue() throws JSONException {
/* 310 */     char c = nextClean();
/*     */ 
/*     */     
/* 313 */     switch (c) {
/*     */       case '"':
/*     */       case '\'':
/* 316 */         return nextString(c);
/*     */       case '{':
/* 318 */         back();
/* 319 */         return new JSONObject(this);
/*     */       case '(':
/*     */       case '[':
/* 322 */         back();
/* 323 */         return new JSONArray(this);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 335 */     StringBuffer stringBuffer = new StringBuffer();
/* 336 */     char c1 = c;
/* 337 */     while (c >= ' ' && ",:]}/\\\"[{;=#".indexOf(c) < 0) {
/* 338 */       stringBuffer.append(c);
/* 339 */       c = next();
/*     */     } 
/* 341 */     back();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 347 */     String str = stringBuffer.toString().trim();
/* 348 */     if (str.equals("")) {
/* 349 */       throw syntaxError("Missing value");
/*     */     }
/* 351 */     if (str.equalsIgnoreCase("true")) {
/* 352 */       return Boolean.TRUE;
/*     */     }
/* 354 */     if (str.equalsIgnoreCase("false")) {
/* 355 */       return Boolean.FALSE;
/*     */     }
/* 357 */     if (str.equalsIgnoreCase("null")) {
/* 358 */       return JSONObject.NULL;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 369 */     if ((c1 >= '0' && c1 <= '9') || c1 == '.' || c1 == '-' || c1 == '+') {
/* 370 */       if (c1 == '0') {
/* 371 */         if (str.length() > 2 && (str.charAt(1) == 'x' || str.charAt(1) == 'X')) {
/*     */           
/*     */           try {
/* 374 */             return new Integer(Integer.parseInt(str.substring(2), 16));
/*     */           }
/* 376 */           catch (Exception exception) {}
/*     */         } else {
/*     */ 
/*     */           
/*     */           try {
/* 381 */             return new Integer(Integer.parseInt(str, 8));
/* 382 */           } catch (Exception exception) {}
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/*     */       try {
/* 388 */         return new Integer(str);
/* 389 */       } catch (Exception exception) {
/*     */         try {
/* 391 */           return new Long(str);
/* 392 */         } catch (Exception exception1) {
/*     */           try {
/* 394 */             return new Double(str);
/* 395 */           } catch (Exception exception2) {
/* 396 */             return str;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 401 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public char skipTo(char paramChar) {
/* 414 */     int i = this.myIndex;
/*     */     while (true) {
/* 416 */       char c = next();
/* 417 */       if (c == '\000') {
/* 418 */         this.myIndex = i;
/* 419 */         return c;
/*     */       } 
/* 421 */       if (c == paramChar) {
/* 422 */         back();
/* 423 */         return c;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean skipPast(String paramString) {
/* 433 */     this.myIndex = this.mySource.indexOf(paramString, this.myIndex);
/* 434 */     if (this.myIndex < 0) {
/* 435 */       this.myIndex = this.mySource.length();
/* 436 */       return false;
/*     */     } 
/* 438 */     this.myIndex += paramString.length();
/* 439 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONException syntaxError(String paramString) {
/* 451 */     return new JSONException(paramString + toString());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toString() {
/* 461 */     return " at character " + this.myIndex + " of " + this.mySource;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/JSONTokener.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */