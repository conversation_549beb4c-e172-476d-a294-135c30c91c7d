/*     */ package org.json;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.Writer;
/*     */ import java.lang.reflect.Array;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JSONArray
/*     */ {
/*     */   private ArrayList myArrayList;
/*     */   
/*     */   public JSONArray() {
/*  97 */     this.myArrayList = new ArrayList();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray(JSONTokener paramJSONTokener) throws JSONException {
/* 106 */     this(); byte b;
/* 107 */     char c = paramJSONTokener.nextClean();
/*     */     
/* 109 */     if (c == '[') {
/* 110 */       b = 93;
/* 111 */     } else if (c == '(') {
/* 112 */       b = 41;
/*     */     } else {
/* 114 */       throw paramJSONTokener.syntaxError("A JSONArray text must start with '['");
/*     */     } 
/* 116 */     if (paramJSONTokener.nextClean() == ']') {
/*     */       return;
/*     */     }
/* 119 */     paramJSONTokener.back();
/*     */     while (true) {
/* 121 */       if (paramJSONTokener.nextClean() == ',') {
/* 122 */         paramJSONTokener.back();
/* 123 */         this.myArrayList.add(null);
/*     */       } else {
/* 125 */         paramJSONTokener.back();
/* 126 */         this.myArrayList.add(paramJSONTokener.nextValue());
/*     */       } 
/* 128 */       c = paramJSONTokener.nextClean();
/* 129 */       switch (c) {
/*     */         case ',':
/*     */         case ';':
/* 132 */           if (paramJSONTokener.nextClean() == ']') {
/*     */             return;
/*     */           }
/* 135 */           paramJSONTokener.back();
/*     */           continue;
/*     */         case ')':
/*     */         case ']':
/* 139 */           if (b != c)
/* 140 */             throw paramJSONTokener.syntaxError("Expected a '" + new Character(b) + "'");  return;
/*     */       } 
/*     */       break;
/*     */     } 
/* 144 */     throw paramJSONTokener.syntaxError("Expected a ',' or ']'");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray(String paramString) throws JSONException {
/* 158 */     this(new JSONTokener(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray(Collection<?> paramCollection) {
/* 167 */     this.myArrayList = (paramCollection == null) ? new ArrayList() : new ArrayList(paramCollection);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray(Object paramObject) throws JSONException {
/* 178 */     this();
/* 179 */     if (paramObject.getClass().isArray()) {
/* 180 */       int i = Array.getLength(paramObject);
/* 181 */       for (byte b = 0; b < i; b++) {
/* 182 */         put(Array.get(paramObject, b));
/*     */       }
/*     */     } else {
/* 185 */       throw new JSONException("JSONArray initial value should be a string or collection or array.");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object get(int paramInt) throws JSONException {
/* 198 */     Object object = opt(paramInt);
/* 199 */     if (object == null) {
/* 200 */       throw new JSONException("JSONArray[" + paramInt + "] not found.");
/*     */     }
/* 202 */     return object;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getBoolean(int paramInt) throws JSONException {
/* 216 */     Object object = get(paramInt);
/* 217 */     if (object.equals(Boolean.FALSE) || (object instanceof String && ((String)object).equalsIgnoreCase("false")))
/*     */     {
/*     */       
/* 220 */       return false; } 
/* 221 */     if (object.equals(Boolean.TRUE) || (object instanceof String && ((String)object).equalsIgnoreCase("true")))
/*     */     {
/*     */       
/* 224 */       return true;
/*     */     }
/* 226 */     throw new JSONException("JSONArray[" + paramInt + "] is not a Boolean.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double getDouble(int paramInt) throws JSONException {
/* 239 */     Object object = get(paramInt);
/*     */     try {
/* 241 */       return (object instanceof Number) ? ((Number)object).doubleValue() : Double.valueOf((String)object).doubleValue();
/*     */     
/*     */     }
/* 244 */     catch (Exception exception) {
/* 245 */       throw new JSONException("JSONArray[" + paramInt + "] is not a number.");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getInt(int paramInt) throws JSONException {
/* 261 */     Object object = get(paramInt);
/* 262 */     return (object instanceof Number) ? ((Number)object).intValue() : (int)getDouble(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray getJSONArray(int paramInt) throws JSONException {
/* 275 */     Object object = get(paramInt);
/* 276 */     if (object instanceof JSONArray) {
/* 277 */       return (JSONArray)object;
/*     */     }
/* 279 */     throw new JSONException("JSONArray[" + paramInt + "] is not a JSONArray.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getJSONObject(int paramInt) throws JSONException {
/* 292 */     Object object = get(paramInt);
/* 293 */     if (object instanceof JSONObject) {
/* 294 */       return (JSONObject)object;
/*     */     }
/* 296 */     throw new JSONException("JSONArray[" + paramInt + "] is not a JSONObject.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long getLong(int paramInt) throws JSONException {
/* 310 */     Object object = get(paramInt);
/* 311 */     return (object instanceof Number) ? ((Number)object).longValue() : (long)getDouble(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getString(int paramInt) throws JSONException {
/* 323 */     return get(paramInt).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isNull(int paramInt) {
/* 333 */     return JSONObject.NULL.equals(opt(paramInt));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String join(String paramString) throws JSONException {
/* 346 */     int i = length();
/* 347 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 349 */     for (byte b = 0; b < i; b++) {
/* 350 */       if (b > 0) {
/* 351 */         stringBuffer.append(paramString);
/*     */       }
/* 353 */       stringBuffer.append(JSONObject.valueToString(this.myArrayList.get(b)));
/*     */     } 
/* 355 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int length() {
/* 365 */     return this.myArrayList.size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object opt(int paramInt) {
/* 376 */     return (paramInt < 0 || paramInt >= length()) ? null : this.myArrayList.get(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean optBoolean(int paramInt) {
/* 390 */     return optBoolean(paramInt, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean optBoolean(int paramInt, boolean paramBoolean) {
/*     */     try {
/* 405 */       return getBoolean(paramInt);
/* 406 */     } catch (Exception exception) {
/* 407 */       return paramBoolean;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double optDouble(int paramInt) {
/* 421 */     return optDouble(paramInt, Double.NaN);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double optDouble(int paramInt, double paramDouble) {
/*     */     try {
/* 436 */       return getDouble(paramInt);
/* 437 */     } catch (Exception exception) {
/* 438 */       return paramDouble;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int optInt(int paramInt) {
/* 452 */     return optInt(paramInt, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int optInt(int paramInt1, int paramInt2) {
/*     */     try {
/* 466 */       return getInt(paramInt1);
/* 467 */     } catch (Exception exception) {
/* 468 */       return paramInt2;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray optJSONArray(int paramInt) {
/* 480 */     Object object = opt(paramInt);
/* 481 */     return (object instanceof JSONArray) ? (JSONArray)object : null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject optJSONObject(int paramInt) {
/* 494 */     Object object = opt(paramInt);
/* 495 */     return (object instanceof JSONObject) ? (JSONObject)object : null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long optLong(int paramInt) {
/* 508 */     return optLong(paramInt, 0L);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long optLong(int paramInt, long paramLong) {
/*     */     try {
/* 522 */       return getLong(paramInt);
/* 523 */     } catch (Exception exception) {
/* 524 */       return paramLong;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String optString(int paramInt) {
/* 538 */     return optString(paramInt, "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String optString(int paramInt, String paramString) {
/* 551 */     Object object = opt(paramInt);
/* 552 */     return (object != null) ? object.toString() : paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(boolean paramBoolean) {
/* 563 */     put(paramBoolean ? Boolean.TRUE : Boolean.FALSE);
/* 564 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(Collection paramCollection) {
/* 575 */     put(new JSONArray(paramCollection));
/* 576 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(double paramDouble) throws JSONException {
/* 588 */     Double double_ = new Double(paramDouble);
/* 589 */     JSONObject.testValidity(double_);
/* 590 */     put(double_);
/* 591 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt) {
/* 602 */     put(new Integer(paramInt));
/* 603 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(long paramLong) {
/* 614 */     put(new Long(paramLong));
/* 615 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(Map paramMap) {
/* 626 */     put(new JSONObject(paramMap));
/* 627 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(Object paramObject) {
/* 639 */     this.myArrayList.add(paramObject);
/* 640 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt, boolean paramBoolean) throws JSONException {
/* 654 */     put(paramInt, paramBoolean ? Boolean.TRUE : Boolean.FALSE);
/* 655 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt, Collection paramCollection) throws JSONException {
/* 669 */     put(paramInt, new JSONArray(paramCollection));
/* 670 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt, double paramDouble) throws JSONException {
/* 685 */     put(paramInt, new Double(paramDouble));
/* 686 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt1, int paramInt2) throws JSONException {
/* 700 */     put(paramInt1, new Integer(paramInt2));
/* 701 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt, long paramLong) throws JSONException {
/* 715 */     put(paramInt, new Long(paramLong));
/* 716 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt, Map paramMap) throws JSONException {
/* 730 */     put(paramInt, new JSONObject(paramMap));
/* 731 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray put(int paramInt, Object paramObject) throws JSONException {
/* 748 */     JSONObject.testValidity(paramObject);
/* 749 */     if (paramInt < 0) {
/* 750 */       throw new JSONException("JSONArray[" + paramInt + "] not found.");
/*     */     }
/* 752 */     if (paramInt < length()) {
/* 753 */       this.myArrayList.set(paramInt, paramObject);
/*     */     } else {
/* 755 */       while (paramInt != length()) {
/* 756 */         put(JSONObject.NULL);
/*     */       }
/* 758 */       put(paramObject);
/*     */     } 
/* 760 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject toJSONObject(JSONArray paramJSONArray) throws JSONException {
/* 774 */     if (paramJSONArray == null || paramJSONArray.length() == 0 || length() == 0) {
/* 775 */       return null;
/*     */     }
/* 777 */     JSONObject jSONObject = new JSONObject();
/* 778 */     for (byte b = 0; b < paramJSONArray.length(); b++) {
/* 779 */       jSONObject.put(paramJSONArray.getString(b), opt(b));
/*     */     }
/* 781 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toString() {
/*     */     try {
/* 798 */       return '[' + join(",") + ']';
/* 799 */     } catch (Exception exception) {
/* 800 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toString(int paramInt) throws JSONException {
/* 817 */     return toString(paramInt, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   String toString(int paramInt1, int paramInt2) throws JSONException {
/* 832 */     int i = length();
/* 833 */     if (i == 0) {
/* 834 */       return "[]";
/*     */     }
/*     */     
/* 837 */     StringBuffer stringBuffer = new StringBuffer("[");
/* 838 */     if (i == 1) {
/* 839 */       stringBuffer.append(JSONObject.valueToString(this.myArrayList.get(0), paramInt1, paramInt2));
/*     */     } else {
/*     */       
/* 842 */       int j = paramInt2 + paramInt1;
/* 843 */       stringBuffer.append('\n'); byte b;
/* 844 */       for (b = 0; b < i; b++) {
/* 845 */         if (b > 0) {
/* 846 */           stringBuffer.append(",\n");
/*     */         }
/* 848 */         for (byte b1 = 0; b1 < j; b1++) {
/* 849 */           stringBuffer.append(' ');
/*     */         }
/* 851 */         stringBuffer.append(JSONObject.valueToString(this.myArrayList.get(b), paramInt1, j));
/*     */       } 
/*     */       
/* 854 */       stringBuffer.append('\n');
/* 855 */       for (b = 0; b < paramInt2; b++) {
/* 856 */         stringBuffer.append(' ');
/*     */       }
/*     */     } 
/* 859 */     stringBuffer.append(']');
/* 860 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Writer write(Writer paramWriter) throws JSONException {
/*     */     try {
/* 875 */       boolean bool = false;
/* 876 */       int i = length();
/*     */       
/* 878 */       paramWriter.write(91);
/*     */       
/* 880 */       for (byte b = 0; b < i; b++) {
/* 881 */         if (bool) {
/* 882 */           paramWriter.write(44);
/*     */         }
/* 884 */         JSONObject jSONObject = (JSONObject)this.myArrayList.get(b);
/* 885 */         if (jSONObject instanceof JSONObject) {
/* 886 */           ((JSONObject)jSONObject).write(paramWriter);
/* 887 */         } else if (jSONObject instanceof JSONArray) {
/* 888 */           ((JSONArray)jSONObject).write(paramWriter);
/*     */         } else {
/* 890 */           paramWriter.write(JSONObject.valueToString(jSONObject));
/*     */         } 
/* 892 */         bool = true;
/*     */       } 
/* 894 */       paramWriter.write(93);
/* 895 */       return paramWriter;
/* 896 */     } catch (IOException iOException) {
/* 897 */       throw new JSONException(iOException);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/JSONArray.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */