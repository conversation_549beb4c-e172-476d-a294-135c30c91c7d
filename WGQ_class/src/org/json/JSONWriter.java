/*     */ package org.json;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.Writer;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JSONWriter
/*     */ {
/*     */   private static final int maxdepth = 20;
/*     */   private boolean comma;
/*     */   protected char mode;
/*     */   private char[] stack;
/*     */   private int top;
/*     */   protected Writer writer;
/*     */   
/*     */   public JSONWriter(Writer paramWriter) {
/*  97 */     this.comma = false;
/*  98 */     this.mode = 'i';
/*  99 */     this.stack = new char[20];
/* 100 */     this.top = 0;
/* 101 */     this.writer = paramWriter;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JSONWriter append(String paramString) throws JSONException {
/* 111 */     if (paramString == null) {
/* 112 */       throw new JSONException("Null pointer");
/*     */     }
/* 114 */     if (this.mode == 'o' || this.mode == 'a') {
/*     */       try {
/* 116 */         if (this.comma && this.mode == 'a') {
/* 117 */           this.writer.write(44);
/*     */         }
/* 119 */         this.writer.write(paramString);
/* 120 */       } catch (IOException iOException) {
/* 121 */         throw new JSONException(iOException);
/*     */       } 
/* 123 */       if (this.mode == 'o') {
/* 124 */         this.mode = 'k';
/*     */       }
/* 126 */       this.comma = true;
/* 127 */       return this;
/*     */     } 
/* 129 */     throw new JSONException("Value out of sequence.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter array() throws JSONException {
/* 142 */     if (this.mode == 'i' || this.mode == 'o' || this.mode == 'a') {
/* 143 */       push('a');
/* 144 */       append("[");
/* 145 */       this.comma = false;
/* 146 */       return this;
/*     */     } 
/* 148 */     throw new JSONException("Misplaced array.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JSONWriter end(char paramChar1, char paramChar2) throws JSONException {
/* 159 */     if (this.mode != paramChar1) {
/* 160 */       throw new JSONException((paramChar1 == 'o') ? "Misplaced endObject." : "Misplaced endArray.");
/*     */     }
/*     */     
/* 163 */     pop(paramChar1);
/*     */     try {
/* 165 */       this.writer.write(paramChar2);
/* 166 */     } catch (IOException iOException) {
/* 167 */       throw new JSONException(iOException);
/*     */     } 
/* 169 */     this.comma = true;
/* 170 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter endArray() throws JSONException {
/* 180 */     return end('a', ']');
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter endObject() throws JSONException {
/* 190 */     return end('k', '}');
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter key(String paramString) throws JSONException {
/* 202 */     if (paramString == null) {
/* 203 */       throw new JSONException("Null key.");
/*     */     }
/* 205 */     if (this.mode == 'k') {
/*     */       try {
/* 207 */         if (this.comma) {
/* 208 */           this.writer.write(44);
/*     */         }
/* 210 */         this.writer.write(JSONObject.quote(paramString));
/* 211 */         this.writer.write(58);
/* 212 */         this.comma = false;
/* 213 */         this.mode = 'o';
/* 214 */         return this;
/* 215 */       } catch (IOException iOException) {
/* 216 */         throw new JSONException(iOException);
/*     */       } 
/*     */     }
/* 219 */     throw new JSONException("Misplaced key.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter object() throws JSONException {
/* 233 */     if (this.mode == 'i') {
/* 234 */       this.mode = 'o';
/*     */     }
/* 236 */     if (this.mode == 'o' || this.mode == 'a') {
/* 237 */       append("{");
/* 238 */       push('k');
/* 239 */       this.comma = false;
/* 240 */       return this;
/*     */     } 
/* 242 */     throw new JSONException("Misplaced object.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void pop(char paramChar) throws JSONException {
/* 253 */     if (this.top <= 0 || this.stack[this.top - 1] != paramChar) {
/* 254 */       throw new JSONException("Nesting error.");
/*     */     }
/* 256 */     this.top--;
/* 257 */     this.mode = (this.top == 0) ? 'd' : this.stack[this.top - 1];
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void push(char paramChar) throws JSONException {
/* 266 */     if (this.top >= 20) {
/* 267 */       throw new JSONException("Nesting too deep.");
/*     */     }
/* 269 */     this.stack[this.top] = paramChar;
/* 270 */     this.mode = paramChar;
/* 271 */     this.top++;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter value(boolean paramBoolean) throws JSONException {
/* 283 */     return append(paramBoolean ? "true" : "false");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter value(double paramDouble) throws JSONException {
/* 293 */     return value(new Double(paramDouble));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter value(long paramLong) throws JSONException {
/* 303 */     return append(Long.toString(paramLong));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONWriter value(Object paramObject) throws JSONException {
/* 316 */     return append(JSONObject.valueToString(paramObject));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/JSONWriter.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */