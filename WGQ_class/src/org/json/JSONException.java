/*    */ package org.json;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JSONException
/*    */   extends Exception
/*    */ {
/*    */   private Throwable cause;
/*    */   
/*    */   public JSONException(String paramString) {
/* 16 */     super(paramString);
/*    */   }
/*    */   
/*    */   public JSONException(Throwable paramThrowable) {
/* 20 */     super(paramThrowable.getMessage());
/* 21 */     this.cause = paramThrowable;
/*    */   }
/*    */   
/*    */   public Throwable getCause() {
/* 25 */     return this.cause;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/JSONException.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */