/*      */ package org.json;
/*      */ 
/*      */ import java.io.IOException;
/*      */ import java.io.Writer;
/*      */ import java.lang.reflect.Field;
/*      */ import java.lang.reflect.Method;
/*      */ import java.util.Collection;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.Map;
/*      */ import java.util.TreeSet;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class JSONObject
/*      */   implements Cloneable
/*      */ {
/*      */   private HashMap myHashMap;
/*      */   
/*      */   public Object clone() {
/*  100 */     JSONObject jSONObject = null;
/*      */     try {
/*  102 */       jSONObject = (JSONObject)super.clone();
/*  103 */     } catch (Exception exception) {
/*      */       
/*  105 */       exception.printStackTrace();
/*      */     } 
/*  107 */     return jSONObject;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static final class Null
/*      */   {
/*      */     private Null() {}
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     protected final Object clone() {
/*  122 */       return this;
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     public boolean equals(Object param1Object) {
/*  133 */       return (param1Object == null || param1Object == this);
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     public String toString() {
/*  142 */       return "null";
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  159 */   public static final Object NULL = new Null();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject() {
/*  166 */     this.myHashMap = new HashMap<Object, Object>();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject(JSONObject paramJSONObject, String[] paramArrayOfString) throws JSONException {
/*  179 */     this();
/*  180 */     for (byte b = 0; b < paramArrayOfString.length; b++) {
/*  181 */       putOpt(paramArrayOfString[b], paramJSONObject.opt(paramArrayOfString[b]));
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject(JSONTokener paramJSONTokener) throws JSONException {
/*  192 */     this();
/*      */ 
/*      */ 
/*      */     
/*  196 */     if (paramJSONTokener.nextClean() != '{') {
/*  197 */       throw paramJSONTokener.syntaxError("A JSONObject text must begin with '{'");
/*      */     }
/*      */     while (true) {
/*  200 */       char c = paramJSONTokener.nextClean();
/*  201 */       switch (c) {
/*      */         case '\000':
/*  203 */           throw paramJSONTokener.syntaxError("A JSONObject text must end with '}'");
/*      */         case '}':
/*      */           return;
/*      */       } 
/*  207 */       paramJSONTokener.back();
/*  208 */       String str = paramJSONTokener.nextValue().toString();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  215 */       c = paramJSONTokener.nextClean();
/*  216 */       if (c == '=') {
/*  217 */         if (paramJSONTokener.next() != '>') {
/*  218 */           paramJSONTokener.back();
/*      */         }
/*  220 */       } else if (c != ':') {
/*  221 */         throw paramJSONTokener.syntaxError("Expected a ':' after a key");
/*      */       } 
/*  223 */       put(str, paramJSONTokener.nextValue());
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  229 */       switch (paramJSONTokener.nextClean()) {
/*      */         case ',':
/*      */         case ';':
/*  232 */           if (paramJSONTokener.nextClean() == '}') {
/*      */             return;
/*      */           }
/*  235 */           paramJSONTokener.back(); continue;
/*      */         case '}':
/*      */           return;
/*      */       }  break;
/*      */     } 
/*  240 */     throw paramJSONTokener.syntaxError("Expected a ',' or '}'");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject(Map<?, ?> paramMap) {
/*  252 */     this.myHashMap = (paramMap == null) ? new HashMap<Object, Object>() : new HashMap<Object, Object>(paramMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject(Object paramObject) {
/*  278 */     this();
/*  279 */     Class<?> clazz = paramObject.getClass();
/*  280 */     Method[] arrayOfMethod = clazz.getMethods();
/*  281 */     for (byte b = 0; b < arrayOfMethod.length; b++) {
/*      */       try {
/*  283 */         Method method = arrayOfMethod[b];
/*  284 */         String str1 = method.getName();
/*  285 */         String str2 = "";
/*  286 */         if (str1.startsWith("get")) {
/*  287 */           str2 = str1.substring(3);
/*  288 */         } else if (str1.startsWith("is")) {
/*  289 */           str2 = str1.substring(2);
/*      */         } 
/*  291 */         if (str2.length() > 0 && Character.isUpperCase(str2.charAt(0)) && (method.getParameterTypes()).length == 0) {
/*      */ 
/*      */           
/*  294 */           if (str2.length() == 1) {
/*  295 */             str2 = str2.toLowerCase();
/*  296 */           } else if (!Character.isUpperCase(str2.charAt(1))) {
/*  297 */             str2 = str2.substring(0, 1).toLowerCase() + str2.substring(1);
/*      */           } 
/*      */           
/*  300 */           put(str2, method.invoke(paramObject, null));
/*      */         } 
/*  302 */       } catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject(Object paramObject, String[] paramArrayOfString) {
/*  320 */     this();
/*  321 */     Class<?> clazz = paramObject.getClass();
/*  322 */     for (byte b = 0; b < paramArrayOfString.length; b++) {
/*  323 */       String str = paramArrayOfString[b];
/*      */       try {
/*  325 */         Field field = clazz.getField(str);
/*  326 */         Object object = field.get(paramObject);
/*  327 */         put(str, object);
/*  328 */       } catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject(String paramString) throws JSONException {
/*  344 */     this(new JSONTokener(paramString));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject accumulate(String paramString, Object paramObject) throws JSONException {
/*  362 */     testValidity(paramObject);
/*  363 */     Object object = opt(paramString);
/*  364 */     if (object == null) {
/*  365 */       put(paramString, (paramObject instanceof JSONArray) ? (new JSONArray()).put(paramObject) : paramObject);
/*      */     
/*      */     }
/*  368 */     else if (object instanceof JSONArray) {
/*  369 */       ((JSONArray)object).put(paramObject);
/*      */     } else {
/*  371 */       put(paramString, (new JSONArray()).put(object).put(paramObject));
/*      */     } 
/*  373 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject append(String paramString, Object paramObject) throws JSONException {
/*  390 */     testValidity(paramObject);
/*  391 */     Object object = opt(paramString);
/*  392 */     if (object == null) {
/*  393 */       put(paramString, (new JSONArray()).put(paramObject));
/*  394 */     } else if (object instanceof JSONArray) {
/*  395 */       put(paramString, ((JSONArray)object).put(paramObject));
/*      */     } else {
/*  397 */       throw new JSONException("JSONObject[" + paramString + "] is not a JSONArray.");
/*      */     } 
/*      */     
/*  400 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String doubleToString(double paramDouble) {
/*  411 */     if (Double.isInfinite(paramDouble) || Double.isNaN(paramDouble)) {
/*  412 */       return "null";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  417 */     String str = Double.toString(paramDouble);
/*  418 */     if (str.indexOf('.') > 0 && str.indexOf('e') < 0 && str.indexOf('E') < 0) {
/*  419 */       while (str.endsWith("0")) {
/*  420 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*  422 */       if (str.endsWith(".")) {
/*  423 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*      */     } 
/*  426 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Object get(String paramString) throws JSONException {
/*  438 */     Object object = opt(paramString);
/*  439 */     if (object == null) {
/*  440 */       throw new JSONException("JSONObject[" + quote(paramString) + "] not found.");
/*      */     }
/*      */     
/*  443 */     return object;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean getBoolean(String paramString) throws JSONException {
/*  456 */     Object object = get(paramString);
/*  457 */     if (object.equals(Boolean.FALSE) || (object instanceof String && ((String)object).equalsIgnoreCase("false")))
/*      */     {
/*      */       
/*  460 */       return false; } 
/*  461 */     if (object.equals(Boolean.TRUE) || (object instanceof String && ((String)object).equalsIgnoreCase("true")))
/*      */     {
/*      */       
/*  464 */       return true;
/*      */     }
/*  466 */     throw new JSONException("JSONObject[" + quote(paramString) + "] is not a Boolean.");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public double getDouble(String paramString) throws JSONException {
/*  479 */     Object object = get(paramString);
/*      */     try {
/*  481 */       return (object instanceof Number) ? ((Number)object).doubleValue() : Double.valueOf((String)object).doubleValue();
/*      */     
/*      */     }
/*  484 */     catch (Exception exception) {
/*  485 */       throw new JSONException("JSONObject[" + quote(paramString) + "] is not a number.");
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getInt(String paramString) throws JSONException {
/*  501 */     Object object = get(paramString);
/*  502 */     return (object instanceof Number) ? ((Number)object).intValue() : (int)getDouble(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONArray getJSONArray(String paramString) throws JSONException {
/*  516 */     Object object = get(paramString);
/*  517 */     if (object instanceof JSONArray) {
/*  518 */       return (JSONArray)object;
/*      */     }
/*  520 */     throw new JSONException("JSONObject[" + quote(paramString) + "] is not a JSONArray.");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject getJSONObject(String paramString) throws JSONException {
/*  534 */     Object object = get(paramString);
/*  535 */     if (object instanceof JSONObject) {
/*  536 */       return (JSONObject)object;
/*      */     }
/*  538 */     throw new JSONException("JSONObject[" + quote(paramString) + "] is not a JSONObject.");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public long getLong(String paramString) throws JSONException {
/*  553 */     Object object = get(paramString);
/*  554 */     return (object instanceof Number) ? ((Number)object).longValue() : (long)getDouble(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String[] getNames(JSONObject paramJSONObject) {
/*  565 */     int i = paramJSONObject.length();
/*  566 */     if (i == 0) {
/*  567 */       return null;
/*      */     }
/*  569 */     Iterator<String> iterator = paramJSONObject.keys();
/*  570 */     String[] arrayOfString = new String[i];
/*  571 */     byte b = 0;
/*  572 */     while (iterator.hasNext()) {
/*  573 */       arrayOfString[b] = iterator.next();
/*  574 */       b++;
/*      */     } 
/*  576 */     return arrayOfString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String[] getNames(Object paramObject) {
/*  586 */     if (paramObject == null) {
/*  587 */       return null;
/*      */     }
/*  589 */     Class<?> clazz = paramObject.getClass();
/*  590 */     Field[] arrayOfField = clazz.getFields();
/*  591 */     int i = arrayOfField.length;
/*  592 */     if (i == 0) {
/*  593 */       return null;
/*      */     }
/*  595 */     String[] arrayOfString = new String[i];
/*  596 */     for (byte b = 0; b < i; b++) {
/*  597 */       arrayOfString[b] = arrayOfField[b].getName();
/*      */     }
/*  599 */     return arrayOfString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getString(String paramString) throws JSONException {
/*  611 */     return get(paramString).toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean has(String paramString) {
/*  621 */     return this.myHashMap.containsKey(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isNull(String paramString) {
/*  633 */     return NULL.equals(opt(paramString));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Iterator keys() {
/*  643 */     return this.myHashMap.keySet().iterator();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int length() {
/*  653 */     return this.myHashMap.size();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONArray names() {
/*  664 */     JSONArray jSONArray = new JSONArray();
/*  665 */     Iterator iterator = keys();
/*  666 */     while (iterator.hasNext()) {
/*  667 */       jSONArray.put(iterator.next());
/*      */     }
/*  669 */     return (jSONArray.length() == 0) ? null : jSONArray;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String numberToString(Number paramNumber) throws JSONException {
/*  680 */     if (paramNumber == null) {
/*  681 */       throw new JSONException("Null pointer");
/*      */     }
/*  683 */     testValidity(paramNumber);
/*      */ 
/*      */ 
/*      */     
/*  687 */     String str = paramNumber.toString();
/*  688 */     if (str.indexOf('.') > 0 && str.indexOf('e') < 0 && str.indexOf('E') < 0) {
/*  689 */       while (str.endsWith("0")) {
/*  690 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*  692 */       if (str.endsWith(".")) {
/*  693 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*      */     } 
/*  696 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Object opt(String paramString) {
/*  706 */     return (paramString == null) ? null : this.myHashMap.get(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean optBoolean(String paramString) {
/*  719 */     return optBoolean(paramString, false);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean optBoolean(String paramString, boolean paramBoolean) {
/*      */     try {
/*  734 */       return getBoolean(paramString);
/*  735 */     } catch (Exception exception) {
/*  736 */       return paramBoolean;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject put(String paramString, Collection paramCollection) throws JSONException {
/*  750 */     put(paramString, new JSONArray(paramCollection));
/*  751 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public double optDouble(String paramString) {
/*  765 */     return optDouble(paramString, Double.NaN);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public double optDouble(String paramString, double paramDouble) {
/*      */     try {
/*  781 */       Object object = opt(paramString);
/*  782 */       return (object instanceof Number) ? ((Number)object).doubleValue() : (new Double((String)object)).doubleValue();
/*      */     }
/*  784 */     catch (Exception exception) {
/*  785 */       return paramDouble;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int optInt(String paramString) {
/*  800 */     return optInt(paramString, 0);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int optInt(String paramString, int paramInt) {
/*      */     try {
/*  816 */       return getInt(paramString);
/*  817 */     } catch (Exception exception) {
/*  818 */       return paramInt;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONArray optJSONArray(String paramString) {
/*  832 */     Object object = opt(paramString);
/*  833 */     return (object instanceof JSONArray) ? (JSONArray)object : null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject optJSONObject(String paramString) {
/*  846 */     Object object = opt(paramString);
/*  847 */     return (object instanceof JSONObject) ? (JSONObject)object : null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public long optLong(String paramString) {
/*  861 */     return optLong(paramString, 0L);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public long optLong(String paramString, long paramLong) {
/*      */     try {
/*  877 */       return getLong(paramString);
/*  878 */     } catch (Exception exception) {
/*  879 */       return paramLong;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String optString(String paramString) {
/*  893 */     return optString(paramString, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String optString(String paramString1, String paramString2) {
/*  906 */     Object object = opt(paramString1);
/*  907 */     return (object != null) ? object.toString() : paramString2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject put(String paramString, boolean paramBoolean) throws JSONException {
/*  920 */     put(paramString, paramBoolean ? Boolean.TRUE : Boolean.FALSE);
/*  921 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject put(String paramString, double paramDouble) throws JSONException {
/*  934 */     put(paramString, new Double(paramDouble));
/*  935 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject put(String paramString, int paramInt) throws JSONException {
/*  948 */     put(paramString, new Integer(paramInt));
/*  949 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject put(String paramString, long paramLong) throws JSONException {
/*  962 */     put(paramString, new Long(paramLong));
/*  963 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject put(String paramString, Map paramMap) throws JSONException {
/*  976 */     put(paramString, new JSONObject(paramMap));
/*  977 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject put(String paramString, Object paramObject) throws JSONException {
/*  993 */     if (paramString == null) {
/*  994 */       throw new JSONException("Null key.");
/*      */     }
/*  996 */     if (paramObject != null) {
/*  997 */       testValidity(paramObject);
/*  998 */       this.myHashMap.put(paramString, paramObject);
/*      */     } else {
/* 1000 */       remove(paramString);
/*      */     } 
/* 1002 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject putOpt(String paramString, Object paramObject) throws JSONException {
/* 1017 */     if (paramString != null && paramObject != null) {
/* 1018 */       put(paramString, paramObject);
/*      */     }
/* 1020 */     return this;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String quote(String paramString) {
/* 1033 */     if (paramString == null || paramString.length() == 0) {
/* 1034 */       return "\"\"";
/*      */     }
/*      */ 
/*      */     
/* 1038 */     char c = Character.MIN_VALUE;
/*      */     
/* 1040 */     int i = paramString.length();
/* 1041 */     StringBuffer stringBuffer = new StringBuffer(i + 4);
/*      */ 
/*      */     
/* 1044 */     stringBuffer.append('"');
/* 1045 */     for (byte b = 0; b < i; b++) {
/* 1046 */       char c1 = c;
/* 1047 */       c = paramString.charAt(b);
/* 1048 */       switch (c) {
/*      */         case '"':
/*      */         case '\\':
/* 1051 */           stringBuffer.append('\\');
/* 1052 */           stringBuffer.append(c);
/*      */           break;
/*      */         case '/':
/* 1055 */           if (c1 == '<') {
/* 1056 */             stringBuffer.append('\\');
/*      */           }
/* 1058 */           stringBuffer.append(c);
/*      */           break;
/*      */         case '\b':
/* 1061 */           stringBuffer.append("\\b");
/*      */           break;
/*      */         case '\t':
/* 1064 */           stringBuffer.append("\\t");
/*      */           break;
/*      */         case '\n':
/* 1067 */           stringBuffer.append("\\n");
/*      */           break;
/*      */         case '\f':
/* 1070 */           stringBuffer.append("\\f");
/*      */           break;
/*      */         case '\r':
/* 1073 */           stringBuffer.append("\\r");
/*      */           break;
/*      */         default:
/* 1076 */           if (c < ' ' || (c >= '' && c < ' ') || (c >= ' ' && c < '℀')) {
/*      */             
/* 1078 */             String str = "000" + Integer.toHexString(c);
/* 1079 */             stringBuffer.append("\\u" + str.substring(str.length() - 4)); break;
/*      */           } 
/* 1081 */           stringBuffer.append(c);
/*      */           break;
/*      */       } 
/*      */     } 
/* 1085 */     stringBuffer.append('"');
/* 1086 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Object remove(String paramString) {
/* 1096 */     return this.myHashMap.remove(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Iterator sortedKeys() {
/* 1106 */     return (new TreeSet(this.myHashMap.keySet())).iterator();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   static void testValidity(Object paramObject) throws JSONException {
/* 1115 */     if (paramObject != null) {
/* 1116 */       if (paramObject instanceof Double) {
/* 1117 */         if (((Double)paramObject).isInfinite() || ((Double)paramObject).isNaN()) {
/* 1118 */           throw new JSONException("JSON does not allow non-finite numbers.");
/*      */         }
/*      */       }
/* 1121 */       else if (paramObject instanceof Float && ((
/* 1122 */         (Float)paramObject).isInfinite() || ((Float)paramObject).isNaN())) {
/* 1123 */         throw new JSONException("JSON does not allow non-finite numbers.");
/*      */       } 
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONArray toJSONArray(JSONArray paramJSONArray) throws JSONException {
/* 1140 */     if (paramJSONArray == null || paramJSONArray.length() == 0) {
/* 1141 */       return null;
/*      */     }
/* 1143 */     JSONArray jSONArray = new JSONArray();
/* 1144 */     for (byte b = 0; b < paramJSONArray.length(); b++) {
/* 1145 */       jSONArray.put(opt(paramJSONArray.getString(b)));
/*      */     }
/* 1147 */     return jSONArray;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String toString() {
/*      */     try {
/* 1164 */       Iterator<Object> iterator = keys();
/* 1165 */       StringBuffer stringBuffer = new StringBuffer("{");
/*      */       
/* 1167 */       while (iterator.hasNext()) {
/* 1168 */         if (stringBuffer.length() > 1) {
/* 1169 */           stringBuffer.append(',');
/*      */         }
/* 1171 */         Object object = iterator.next();
/* 1172 */         stringBuffer.append(quote(object.toString()));
/* 1173 */         stringBuffer.append(':');
/* 1174 */         stringBuffer.append(valueToString(this.myHashMap.get(object)));
/*      */       } 
/* 1176 */       stringBuffer.append('}');
/* 1177 */       return stringBuffer.toString();
/* 1178 */     } catch (Exception exception) {
/* 1179 */       return null;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String toString(int paramInt) throws JSONException {
/* 1197 */     return toString(paramInt, 0);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   String toString(int paramInt1, int paramInt2) throws JSONException {
/* 1216 */     int i = length();
/* 1217 */     if (i == 0) {
/* 1218 */       return "{}";
/*      */     }
/* 1220 */     Iterator<Object> iterator = sortedKeys();
/* 1221 */     StringBuffer stringBuffer = new StringBuffer("{");
/* 1222 */     int j = paramInt2 + paramInt1;
/*      */     
/* 1224 */     if (i == 1) {
/* 1225 */       Object object = iterator.next();
/* 1226 */       stringBuffer.append(quote(object.toString()));
/* 1227 */       stringBuffer.append(": ");
/* 1228 */       stringBuffer.append(valueToString(this.myHashMap.get(object), paramInt1, paramInt2));
/*      */     } else {
/*      */       
/* 1231 */       while (iterator.hasNext()) {
/* 1232 */         Object object = iterator.next();
/* 1233 */         if (stringBuffer.length() > 1) {
/* 1234 */           stringBuffer.append(",\n");
/*      */         } else {
/* 1236 */           stringBuffer.append('\n');
/*      */         } 
/* 1238 */         for (byte b = 0; b < j; b++) {
/* 1239 */           stringBuffer.append(' ');
/*      */         }
/* 1241 */         stringBuffer.append(quote(object.toString()));
/* 1242 */         stringBuffer.append(": ");
/* 1243 */         stringBuffer.append(valueToString(this.myHashMap.get(object), paramInt1, j));
/*      */       } 
/*      */       
/* 1246 */       if (stringBuffer.length() > 1) {
/* 1247 */         stringBuffer.append('\n');
/* 1248 */         for (byte b = 0; b < paramInt2; b++) {
/* 1249 */           stringBuffer.append(' ');
/*      */         }
/*      */       } 
/*      */     } 
/* 1253 */     stringBuffer.append('}');
/* 1254 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   static String valueToString(Object paramObject) throws JSONException {
/* 1280 */     if (paramObject == null || paramObject.equals(null)) {
/* 1281 */       return "null";
/*      */     }
/* 1283 */     if (paramObject instanceof JSONString) {
/*      */       String str;
/*      */       try {
/* 1286 */         str = ((JSONString)paramObject).toJSONString();
/* 1287 */       } catch (Exception exception) {
/* 1288 */         throw new JSONException(exception);
/*      */       } 
/* 1290 */       if (str instanceof String) {
/* 1291 */         return str;
/*      */       }
/* 1293 */       throw new JSONException("Bad value from toJSONString: " + str);
/*      */     } 
/* 1295 */     if (paramObject instanceof Number) {
/* 1296 */       return numberToString((Number)paramObject);
/*      */     }
/* 1298 */     if (paramObject instanceof Boolean || paramObject instanceof JSONObject || paramObject instanceof JSONArray)
/*      */     {
/* 1300 */       return paramObject.toString();
/*      */     }
/* 1302 */     if (paramObject instanceof Map) {
/* 1303 */       return (new JSONObject((Map)paramObject)).toString();
/*      */     }
/* 1305 */     if (paramObject instanceof Collection) {
/* 1306 */       return (new JSONArray((Collection)paramObject)).toString();
/*      */     }
/* 1308 */     if (paramObject.getClass().isArray()) {
/* 1309 */       return (new JSONArray(paramObject)).toString();
/*      */     }
/* 1311 */     return quote(paramObject.toString());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   static String valueToString(Object paramObject, int paramInt1, int paramInt2) throws JSONException {
/* 1331 */     if (paramObject == null || paramObject.equals(null)) {
/* 1332 */       return "null";
/*      */     }
/*      */     try {
/* 1335 */       if (paramObject instanceof JSONString) {
/* 1336 */         String str = ((JSONString)paramObject).toJSONString();
/* 1337 */         if (str instanceof String) {
/* 1338 */           return str;
/*      */         }
/*      */       } 
/* 1341 */     } catch (Exception exception) {}
/*      */ 
/*      */     
/* 1344 */     if (paramObject instanceof Number) {
/* 1345 */       return numberToString((Number)paramObject);
/*      */     }
/* 1347 */     if (paramObject instanceof Boolean) {
/* 1348 */       return paramObject.toString();
/*      */     }
/* 1350 */     if (paramObject instanceof JSONObject) {
/* 1351 */       return ((JSONObject)paramObject).toString(paramInt1, paramInt2);
/*      */     }
/* 1353 */     if (paramObject instanceof JSONArray) {
/* 1354 */       return ((JSONArray)paramObject).toString(paramInt1, paramInt2);
/*      */     }
/* 1356 */     if (paramObject instanceof Map) {
/* 1357 */       return (new JSONObject((Map)paramObject)).toString(paramInt1, paramInt2);
/*      */     }
/* 1359 */     if (paramObject instanceof Collection) {
/* 1360 */       return (new JSONArray((Collection)paramObject)).toString(paramInt1, paramInt2);
/*      */     }
/* 1362 */     if (paramObject.getClass().isArray()) {
/* 1363 */       return (new JSONArray(paramObject)).toString(paramInt1, paramInt2);
/*      */     }
/* 1365 */     return quote(paramObject.toString());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Writer write(Writer paramWriter) throws JSONException {
/*      */     try {
/* 1380 */       boolean bool = false;
/* 1381 */       Iterator<Object> iterator = keys();
/* 1382 */       paramWriter.write(123);
/*      */       
/* 1384 */       while (iterator.hasNext()) {
/* 1385 */         if (bool) {
/* 1386 */           paramWriter.write(44);
/*      */         }
/* 1388 */         Object object = iterator.next();
/* 1389 */         paramWriter.write(quote(object.toString()));
/* 1390 */         paramWriter.write(58);
/* 1391 */         Object object1 = this.myHashMap.get(object);
/* 1392 */         if (object1 instanceof JSONObject) {
/* 1393 */           ((JSONObject)object1).write(paramWriter);
/* 1394 */         } else if (object1 instanceof JSONArray) {
/* 1395 */           ((JSONArray)object1).write(paramWriter);
/*      */         } else {
/* 1397 */           paramWriter.write(valueToString(object1));
/*      */         } 
/* 1399 */         bool = true;
/*      */       } 
/* 1401 */       paramWriter.write(125);
/* 1402 */       return paramWriter;
/* 1403 */     } catch (IOException iOException) {
/* 1404 */       throw new JSONException(iOException);
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/JSONObject.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */