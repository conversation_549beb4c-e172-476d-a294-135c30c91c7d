/*     */ package org.json;
/*     */ 
/*     */ import java.util.Iterator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JSONML
/*     */ {
/*     */   private static JSONArray parse(XMLTokener x, JSONArray ja) throws JSONException {
/*     */     label85: while (true) {
/*  63 */       Object t = x.nextContent();
/*  64 */       if (t == XML.LT) {
/*  65 */         t = x.nextToken();
/*  66 */         if (t instanceof Character) {
/*     */ 
/*     */ 
/*     */           
/*  70 */           if (t == XML.BANG) {
/*  71 */             char c = x.next();
/*  72 */             if (c == '-') {
/*  73 */               if (x.next() == '-') {
/*  74 */                 x.skipPast("-->");
/*     */               }
/*  76 */               x.back(); continue;
/*  77 */             }  if (c == '[') {
/*  78 */               t = x.nextToken();
/*  79 */               if (t.equals("CDATA") && x.next() == '[') {
/*  80 */                 x.nextCDATA(); continue;
/*     */               } 
/*  82 */               throw x.syntaxError("Expected 'CDATA['");
/*     */             } 
/*     */             
/*  85 */             int i = 1;
/*     */             while (true)
/*  87 */             { t = x.nextMeta();
/*  88 */               if (t == null)
/*  89 */                 throw x.syntaxError("Missing '>' after '<!'."); 
/*  90 */               if (t == XML.LT) {
/*  91 */                 i++;
/*  92 */               } else if (t == XML.GT) {
/*  93 */                 i--;
/*     */               } 
/*  95 */               if (i <= 0)
/*     */                 continue label85;  }  break;
/*  97 */           }  if (t == XML.QUEST) {
/*     */ 
/*     */ 
/*     */             
/* 101 */             x.skipPast("?>"); continue;
/* 102 */           }  if (t == XML.SLASH) {
/*     */ 
/*     */ 
/*     */             
/* 106 */             t = x.nextToken();
/* 107 */             if (ja == null) {
/* 108 */               throw x.syntaxError("Mismatched close tag '" + t + "'");
/*     */             }
/* 110 */             if (!t.equals(ja.get(0))) {
/* 111 */               throw x.syntaxError("Mismatched '" + ja.get(0) + "' and '" + t + "'");
/*     */             }
/* 113 */             if (x.nextToken() != XML.GT) {
/* 114 */               throw x.syntaxError("Misshaped close tag");
/*     */             }
/* 116 */             return null;
/*     */           } 
/* 118 */           throw x.syntaxError("Misshaped tag");
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 124 */         JSONArray newja = new JSONArray();
/* 125 */         JSONObject attributes = new JSONObject();
/* 126 */         if (ja != null) {
/* 127 */           ja.put(newja);
/*     */         }
/* 129 */         newja.put(t);
/* 130 */         t = null;
/*     */         while (true) {
/* 132 */           if (t == null) {
/* 133 */             t = x.nextToken();
/*     */           }
/* 135 */           if (t == null) {
/* 136 */             throw x.syntaxError("Misshaped tag");
/*     */           }
/* 138 */           if (!(t instanceof String)) {
/*     */             break;
/*     */           }
/*     */ 
/*     */ 
/*     */           
/* 144 */           String s = (String)t;
/* 145 */           t = x.nextToken();
/* 146 */           if (t == XML.EQ) {
/* 147 */             t = x.nextToken();
/* 148 */             if (!(t instanceof String)) {
/* 149 */               throw x.syntaxError("Missing value");
/*     */             }
/* 151 */             attributes.accumulate(s, t);
/* 152 */             t = null; continue;
/*     */           } 
/* 154 */           attributes.accumulate(s, "");
/*     */         } 
/*     */         
/* 157 */         if (attributes.length() > 0) {
/* 158 */           newja.put(attributes);
/*     */         }
/*     */ 
/*     */ 
/*     */         
/* 163 */         if (t == XML.SLASH) {
/* 164 */           if (x.nextToken() != XML.GT) {
/* 165 */             throw x.syntaxError("Misshaped tag");
/*     */           }
/* 167 */           if (ja == null) {
/* 168 */             return newja;
/*     */           }
/*     */           
/*     */           continue;
/*     */         } 
/* 173 */         if (t == XML.GT) {
/* 174 */           parse(x, newja);
/* 175 */           if (ja == null)
/* 176 */             return newja; 
/*     */           continue;
/*     */         } 
/* 179 */         throw x.syntaxError("Misshaped tag");
/*     */       } 
/*     */ 
/*     */       
/* 183 */       if (ja != null) {
/* 184 */         ja.put(t);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray toJSONArray(String string) throws JSONException {
/* 204 */     return toJSONArray(new XMLTokener(string));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray toJSONArray(XMLTokener x) throws JSONException {
/* 221 */     return parse(x, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void stringify(JSONArray ja, StringBuffer b) throws JSONException {
/*     */     int i;
/* 243 */     b.append('<');
/* 244 */     b.append(ja.get(0));
/* 245 */     Object o = ja.opt(1);
/* 246 */     if (o instanceof JSONObject) {
/*     */ 
/*     */ 
/*     */       
/* 250 */       JSONObject jo = (JSONObject)o;
/* 251 */       Iterator keys = jo.keys();
/* 252 */       while (keys.hasNext()) {
/* 253 */         String k = keys.next().toString();
/* 254 */         Object v = jo.get(k).toString();
/* 255 */         b.append(' ');
/* 256 */         b.append(k);
/* 257 */         b.append("=\"");
/* 258 */         b.append(XML.escape((String)v));
/* 259 */         b.append('"');
/*     */       } 
/* 261 */       i = 2;
/*     */     } else {
/* 263 */       i = 1;
/*     */     } 
/* 265 */     int len = ja.length();
/*     */     
/* 267 */     if (i >= len) {
/* 268 */       b.append("/>");
/*     */     } else {
/* 270 */       b.append('>');
/* 271 */       while (i < len) {
/* 272 */         Object v = ja.get(i);
/* 273 */         if (v instanceof JSONArray) {
/* 274 */           stringify((JSONArray)v, b);
/*     */         } else {
/* 276 */           b.append(XML.escape(v.toString()));
/*     */         } 
/* 278 */         i++;
/*     */       } 
/* 280 */       b.append("</");
/* 281 */       b.append(ja.get(0));
/* 282 */       b.append('>');
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(JSONArray ja) throws JSONException {
/* 293 */     StringBuffer b = new StringBuffer();
/* 294 */     stringify(ja, b);
/* 295 */     return b.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/JSONML.class
 * Java compiler version: 1 (45.3)
 * JD-Core Version:       1.1.3
 */