/*     */ package org.json;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CDL
/*     */ {
/*     */   private static String getValue(JSONTokener paramJSONTokener) throws JSONException {
/*     */     char c;
/*     */     do {
/*  58 */       c = paramJSONTokener.next();
/*  59 */     } while (c <= ' ' && c != '\000');
/*  60 */     switch (c) {
/*     */       case '\000':
/*  62 */         return null;
/*     */       case '"':
/*     */       case '\'':
/*  65 */         return paramJSONTokener.nextString(c);
/*     */       case ',':
/*  67 */         paramJSONTokener.back();
/*  68 */         return "";
/*     */     } 
/*  70 */     paramJSONTokener.back();
/*  71 */     return paramJSONTokener.nextTo(',');
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray rowToJSONArray(JSONTokener paramJSONTokener) throws JSONException {
/*  82 */     JSONArray jSONArray = new JSONArray();
/*     */     label19: while (true) {
/*  84 */       String str = getValue(paramJSONTokener);
/*  85 */       if (str == null) {
/*  86 */         return null;
/*     */       }
/*  88 */       jSONArray.put(str);
/*     */       while (true) {
/*  90 */         char c = paramJSONTokener.next();
/*  91 */         if (c == ',') {
/*     */           continue label19;
/*     */         }
/*  94 */         if (c != ' ') {
/*  95 */           if (c == '\n' || c == '\r' || c == '\000') {
/*  96 */             return jSONArray;
/*     */           }
/*  98 */           throw paramJSONTokener.syntaxError("Bad character '" + c + "' (" + c + ").");
/*     */         } 
/*     */       } 
/*     */       break;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject rowToJSONObject(JSONArray paramJSONArray, JSONTokener paramJSONTokener) throws JSONException {
/* 117 */     JSONArray jSONArray = rowToJSONArray(paramJSONTokener);
/* 118 */     return (jSONArray != null) ? jSONArray.toJSONObject(paramJSONArray) : null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray toJSONArray(String paramString) throws JSONException {
/* 129 */     return toJSONArray(new JSONTokener(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray toJSONArray(JSONTokener paramJSONTokener) throws JSONException {
/* 140 */     return toJSONArray(rowToJSONArray(paramJSONTokener), paramJSONTokener);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray toJSONArray(JSONArray paramJSONArray, String paramString) throws JSONException {
/* 153 */     return toJSONArray(paramJSONArray, new JSONTokener(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray toJSONArray(JSONArray paramJSONArray, JSONTokener paramJSONTokener) throws JSONException {
/* 166 */     if (paramJSONArray == null || paramJSONArray.length() == 0) {
/* 167 */       return null;
/*     */     }
/* 169 */     JSONArray jSONArray = new JSONArray();
/*     */     while (true) {
/* 171 */       JSONObject jSONObject = rowToJSONObject(paramJSONArray, paramJSONTokener);
/* 172 */       if (jSONObject == null) {
/*     */         break;
/*     */       }
/* 175 */       jSONArray.put(jSONObject);
/*     */     } 
/* 177 */     if (jSONArray.length() == 0) {
/* 178 */       return null;
/*     */     }
/* 180 */     return jSONArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String rowToString(JSONArray paramJSONArray) {
/* 191 */     StringBuffer stringBuffer = new StringBuffer();
/* 192 */     for (byte b = 0; b < paramJSONArray.length(); b++) {
/* 193 */       if (b > 0) {
/* 194 */         stringBuffer.append(',');
/*     */       }
/* 196 */       Object object = paramJSONArray.opt(b);
/* 197 */       if (object != null) {
/* 198 */         String str = object.toString();
/* 199 */         if (str.indexOf(',') >= 0) {
/* 200 */           if (str.indexOf('"') >= 0) {
/* 201 */             stringBuffer.append('\'');
/* 202 */             stringBuffer.append(str);
/* 203 */             stringBuffer.append('\'');
/*     */           } else {
/* 205 */             stringBuffer.append('"');
/* 206 */             stringBuffer.append(str);
/* 207 */             stringBuffer.append('"');
/*     */           } 
/*     */         } else {
/* 210 */           stringBuffer.append(str);
/*     */         } 
/*     */       } 
/*     */     } 
/* 214 */     stringBuffer.append('\n');
/* 215 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(JSONArray paramJSONArray) throws JSONException {
/* 228 */     JSONObject jSONObject = paramJSONArray.optJSONObject(0);
/* 229 */     if (jSONObject != null) {
/* 230 */       JSONArray jSONArray = jSONObject.names();
/* 231 */       if (jSONArray != null) {
/* 232 */         return rowToString(jSONArray) + toString(jSONArray, paramJSONArray);
/*     */       }
/*     */     } 
/* 235 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(JSONArray paramJSONArray1, JSONArray paramJSONArray2) throws JSONException {
/* 249 */     if (paramJSONArray1 == null || paramJSONArray1.length() == 0) {
/* 250 */       return null;
/*     */     }
/* 252 */     StringBuffer stringBuffer = new StringBuffer();
/* 253 */     for (byte b = 0; b < paramJSONArray2.length(); b++) {
/* 254 */       JSONObject jSONObject = paramJSONArray2.optJSONObject(b);
/* 255 */       if (jSONObject != null) {
/* 256 */         stringBuffer.append(rowToString(jSONObject.toJSONArray(paramJSONArray1)));
/*     */       }
/*     */     } 
/* 259 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/CDL.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */