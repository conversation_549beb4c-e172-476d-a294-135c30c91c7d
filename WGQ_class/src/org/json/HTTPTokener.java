/*    */ package org.json;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HTTPTokener
/*    */   extends JSONTokener
/*    */ {
/*    */   public HTTPTokener(String paramString) {
/* 40 */     super(paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String nextToken() throws JSONException {
/* 52 */     StringBuffer stringBuffer = new StringBuffer();
/*    */     while (true) {
/* 54 */       char c = next();
/* 55 */       if (!Character.isWhitespace(c)) {
/* 56 */         if (c == '"' || c == '\'') {
/* 57 */           char c1 = c;
/*    */           while (true) {
/* 59 */             c = next();
/* 60 */             if (c < ' ') {
/* 61 */               throw syntaxError("Unterminated string.");
/*    */             }
/* 63 */             if (c == c1) {
/* 64 */               return stringBuffer.toString();
/*    */             }
/* 66 */             stringBuffer.append(c);
/*    */           }  break;
/*    */         } 
/*    */         while (true) {
/* 70 */           if (c == '\000' || Character.isWhitespace(c)) {
/* 71 */             return stringBuffer.toString();
/*    */           }
/* 73 */           stringBuffer.append(c);
/* 74 */           c = next();
/*    */         } 
/*    */         break;
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/HTTPTokener.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */