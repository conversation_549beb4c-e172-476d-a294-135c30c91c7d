/*     */ package org.json;
/*     */ 
/*     */ import java.util.Collection;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Test
/*     */ {
/*     */   public static void main(String[] paramArrayOfString) {
/*     */     class Obj
/*     */       implements JSONString
/*     */     {
/*     */       public String aString;
/*     */       public double aNumber;
/*     */       public boolean aBoolean;
/*     */       
/*     */       public Obj(Test this$0, double param1Double, boolean param1Boolean) {
/*  36 */         this.aString = (String)this$0;
/*  37 */         this.aNumber = param1Double;
/*  38 */         this.aBoolean = param1Boolean;
/*     */       }
/*     */       
/*     */       public double getNumber() {
/*  42 */         return this.aNumber;
/*     */       }
/*     */       
/*     */       public String getString() {
/*  46 */         return this.aString;
/*     */       }
/*     */       
/*     */       public boolean isBoolean() {
/*  50 */         return this.aBoolean;
/*     */       }
/*     */       
/*     */       public String getBENT() {
/*  54 */         return "All uppercase key";
/*     */       }
/*     */       
/*     */       public String getX() {
/*  58 */         return "x";
/*     */       }
/*     */       
/*     */       public String toJSONString() {
/*  62 */         return "{" + JSONObject.quote(this.aString) + ":" + JSONObject.doubleToString(this.aNumber) + "}";
/*     */       }
/*     */       
/*     */       public String toString() {
/*  66 */         return getString() + " " + getNumber() + " " + isBoolean() + "." + getBENT() + " " + getX();
/*     */       }
/*     */     };
/*     */ 
/*     */     
/*  71 */     Obj obj = new Obj("A beany object", 42.0D, true);
/*     */     
/*     */     try {
/*  74 */       JSONObject jSONObject = XML.toJSONObject("<![CDATA[This is a collection of test patterns and examples for org.json.]]>  Ignore the stuff past the end.  ");
/*     */ 
/*     */       
/*  77 */       jSONObject = new JSONObject(obj);
/*     */ 
/*     */       
/*  80 */       JSONStringer jSONStringer = new JSONStringer();
/*  81 */       String str = jSONStringer.object().key("foo").value("bar").key("baz").array().object().key("quux").value("Thanks, Josh!").endObject().endArray().key("obj keys").value(JSONObject.getNames(obj)).endObject().toString();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  98 */       jSONStringer = new JSONStringer();
/*  99 */       jSONStringer.array();
/* 100 */       jSONStringer.value(1L);
/* 101 */       jSONStringer.array();
/* 102 */       jSONStringer.value((Object)null);
/* 103 */       jSONStringer.array();
/* 104 */       jSONStringer.object();
/* 105 */       jSONStringer.key("empty-array").array().endArray();
/* 106 */       jSONStringer.key("answer").value(42L);
/* 107 */       jSONStringer.key("null").value((Object)null);
/* 108 */       jSONStringer.key("false").value(false);
/* 109 */       jSONStringer.key("true").value(true);
/* 110 */       jSONStringer.key("big").value(1.23456789E96D);
/* 111 */       jSONStringer.key("small").value(1.23456789E-80D);
/* 112 */       jSONStringer.key("empty-object").object().endObject();
/* 113 */       jSONStringer.key("long");
/* 114 */       jSONStringer.value(Long.MAX_VALUE);
/* 115 */       jSONStringer.endObject();
/* 116 */       jSONStringer.value("two");
/* 117 */       jSONStringer.endArray();
/* 118 */       jSONStringer.value(true);
/* 119 */       jSONStringer.endArray();
/* 120 */       jSONStringer.value(98.6D);
/* 121 */       jSONStringer.value(-100.0D);
/* 122 */       jSONStringer.object();
/* 123 */       jSONStringer.endObject();
/* 124 */       jSONStringer.object();
/* 125 */       jSONStringer.key("one");
/* 126 */       jSONStringer.value(1.0D);
/* 127 */       jSONStringer.endObject();
/* 128 */       jSONStringer.value(obj);
/* 129 */       jSONStringer.endArray();
/*     */ 
/*     */       
/* 132 */       int[] arrayOfInt = { 1, 2, 3 };
/* 133 */       JSONArray jSONArray2 = new JSONArray(arrayOfInt);
/* 134 */       String[] arrayOfString = { "aString", "aNumber", "aBoolean" };
/* 135 */       jSONObject = new JSONObject(obj, arrayOfString);
/* 136 */       jSONObject.put("Testing JSONString interface", obj);
/*     */ 
/*     */       
/* 139 */       jSONObject = new JSONObject("{slashes: '///', closetag: '</script>', backslash:'\\\\', ei: {quotes: '\"\\''},eo: {a: '\"quoted\"', b:\"don't\"}, quotes: [\"'\", '\"']}");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 144 */       jSONObject = new JSONObject("/*comment*/{foo: [true, false,9876543210,    0.0, 1.00000001,  1.000000000001, 1.00000000000000001, .00000000000000001, 2.00, 0.1, 2e100, -32,[],{}, \"string\"],   to   : null, op : 'Good',ten:10} postfix comment");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 149 */       jSONObject.put("String", "98.6");
/* 150 */       jSONObject.put("JSONObject", new JSONObject());
/* 151 */       jSONObject.put("JSONArray", new JSONArray());
/* 152 */       jSONObject.put("int", 57);
/* 153 */       jSONObject.put("double", 1.2345678901234568E29D);
/* 154 */       jSONObject.put("true", true);
/* 155 */       jSONObject.put("false", false);
/* 156 */       jSONObject.put("null", JSONObject.NULL);
/* 157 */       jSONObject.put("bool", "true");
/* 158 */       jSONObject.put("zero", -0.0D);
/* 159 */       jSONObject.put("\\u2028", " ");
/* 160 */       jSONObject.put("\\u2029", " ");
/* 161 */       JSONArray jSONArray1 = jSONObject.getJSONArray("foo");
/* 162 */       jSONArray1.put(666);
/* 163 */       jSONArray1.put(2001.99D);
/* 164 */       jSONArray1.put("so \"fine\".");
/* 165 */       jSONArray1.put("so <fine>.");
/* 166 */       jSONArray1.put(true);
/* 167 */       jSONArray1.put(false);
/* 168 */       jSONArray1.put(new JSONArray());
/* 169 */       jSONArray1.put(new JSONObject());
/* 170 */       jSONObject.put("keys", JSONObject.getNames(jSONObject));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 183 */       jSONObject = XML.toJSONObject("<xml one = 1 two=' \"2\" '><five></five>First \t&lt;content&gt;<five></five> This is \"content\". <three>  3  </three>JSON does not preserve the sequencing of elements and contents.<three>  III  </three>  <three>  T H R E E</three><four/>Content text is an implied structure in XML. <six content=\"6\"/>JSON does not have implied structure:<seven>7</seven>everything is explicit.<![CDATA[CDATA blocks<are><supported>!]]></xml>");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 188 */       jSONObject = XML.toJSONObject("<mapping><empty/>   <class name = \"Customer\">      <field name = \"ID\" type = \"string\">         <bind-xml name=\"ID\" node=\"attribute\"/>      </field>      <field name = \"FirstName\" type = \"FirstName\"/>      <field name = \"MI\" type = \"MI\"/>      <field name = \"LastName\" type = \"LastName\"/>   </class>   <class name = \"FirstName\">      <field name = \"text\">         <bind-xml name = \"text\" node = \"text\"/>      </field>   </class>   <class name = \"MI\">      <field name = \"text\">         <bind-xml name = \"text\" node = \"text\"/>      </field>   </class>   <class name = \"LastName\">      <field name = \"text\">         <bind-xml name = \"text\" node = \"text\"/>      </field>   </class></mapping>");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 194 */       jSONObject = XML.toJSONObject("<?xml version=\"1.0\" ?><Book Author=\"Anonymous\"><Title>Sample Book</Title><Chapter id=\"1\">This is chapter 1. It is not very long or interesting.</Chapter><Chapter id=\"2\">This is chapter 2. Although it is longer than chapter 1, it is not any more interesting.</Chapter></Book>");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 199 */       jSONObject = XML.toJSONObject("<!DOCTYPE bCard 'http://www.cs.caltech.edu/~adam/schemas/bCard'><bCard><?xml default bCard        firstname = ''        lastname  = '' company   = '' email = '' homepage  = ''?><bCard        firstname = 'Rohit'        lastname  = 'Khare'        company   = 'MCI'        email     = '<EMAIL>'        homepage  = 'http://pest.w3.org/'/><bCard        firstname = 'Adam'        lastname  = 'Rifkin'        company   = 'Caltech Infospheres Project'        email     = '<EMAIL>'        homepage  = 'http://www.cs.caltech.edu/~adam/'/></bCard>");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 204 */       jSONObject = XML.toJSONObject("<?xml version=\"1.0\"?><customer>    <firstName>        <text>Fred</text>    </firstName>    <ID>fbs0001</ID>    <lastName> <text>Scerbo</text>    </lastName>    <MI>        <text>B</text>    </MI></customer>");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 209 */       jSONObject = XML.toJSONObject("<!ENTITY tp-address PUBLIC '-//ABC University::Special Collections Library//TEXT (titlepage: name and address)//EN' 'tpspcoll.sgm'><list type='simple'><head>Repository Address </head><item>Special Collections Library</item><item>ABC University</item><item>Main Library, 40 Circle Drive</item><item>Ourtown, Pennsylvania</item><item>17654 USA</item></list>");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 214 */       jSONObject = XML.toJSONObject("<test intertag status=ok><empty/>deluxe<blip sweet=true>&amp;&quot;toot&quot;&toot;&#x41;</blip><x>eks</x><w>bonus</w><w>bonus2</w></test>");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 219 */       jSONObject = HTTP.toJSONObject("GET / HTTP/1.0\nAccept: image/gif, image/x-xbitmap, image/jpeg, image/pjpeg, application/vnd.ms-powerpoint, application/vnd.ms-excel, application/msword, */*\nAccept-Language: en-us\nUser-Agent: Mozilla/4.0 (compatible; MSIE 5.5; Windows 98; Win 9x 4.90; T312461; Q312461)\nHost: www.nokko.com\nConnection: keep-alive\nAccept-encoding: gzip, deflate\n");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 224 */       jSONObject = HTTP.toJSONObject("HTTP/1.1 200 Oki Doki\nDate: Sun, 26 May 2002 17:38:52 GMT\nServer: Apache/1.3.23 (Unix) mod_perl/1.26\nKeep-Alive: timeout=15, max=100\nConnection: Keep-Alive\nTransfer-Encoding: chunked\nContent-Type: text/html\n");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 229 */       jSONObject = new JSONObject("{nix: null, nux: false, null: 'null', 'Request-URI': '/', Method: 'GET', 'HTTP-Version': 'HTTP/1.0'}");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 237 */       jSONObject = XML.toJSONObject("<?xml version='1.0' encoding='UTF-8'?>\n\n<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsi=\"http://www.w3.org/1999/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/1999/XMLSchema\"><SOAP-ENV:Body><ns1:doGoogleSearch xmlns:ns1=\"urn:GoogleSearch\" SOAP-ENV:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\"><key xsi:type=\"xsd:string\">GOOGLEKEY</key> <q xsi:type=\"xsd:string\">'+search+'</q> <start xsi:type=\"xsd:int\">0</start> <maxResults xsi:type=\"xsd:int\">10</maxResults> <filter xsi:type=\"xsd:boolean\">true</filter> <restrict xsi:type=\"xsd:string\"></restrict> <safeSearch xsi:type=\"xsd:boolean\">false</safeSearch> <lr xsi:type=\"xsd:string\"></lr> <ie xsi:type=\"xsd:string\">latin1</ie> <oe xsi:type=\"xsd:string\">latin1</oe></ns1:doGoogleSearch></SOAP-ENV:Body></SOAP-ENV:Envelope>");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 260 */       jSONObject = new JSONObject("{Envelope: {Body: {\"ns1:doGoogleSearch\": {oe: \"latin1\", filter: true, q: \"'+search+'\", key: \"GOOGLEKEY\", maxResults: 10, \"SOAP-ENV:encodingStyle\": \"http://schemas.xmlsoap.org/soap/encoding/\", start: 0, ie: \"latin1\", safeSearch:false, \"xmlns:ns1\": \"urn:GoogleSearch\"}}}}");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 265 */       jSONObject = CookieList.toJSONObject("  f%oo = b+l=ah  ; o;n%40e = t.wo ");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 270 */       jSONObject = Cookie.toJSONObject("f%oo=blah; secure ;expires = April 24, 2002");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 275 */       jSONObject = new JSONObject("{script: 'It is not allowed in HTML to send a close script tag in a string<script>because it confuses browsers</script>so we insert a backslash before the /'}");
/*     */ 
/*     */ 
/*     */       
/* 279 */       JSONTokener jSONTokener = new JSONTokener("{op:'test', to:'session', pre:1}{op:'test', to:'session', pre:2}");
/* 280 */       jSONObject = new JSONObject(jSONTokener);
/*     */ 
/*     */       
/* 283 */       char c = jSONTokener.skipTo('{');
/*     */       
/* 285 */       jSONObject = new JSONObject(jSONTokener);
/*     */ 
/*     */ 
/*     */       
/* 289 */       jSONArray1 = CDL.toJSONArray("No quotes, 'Single Quotes', \"Double Quotes\"\n1,'2',\"3\"\n,'It is \"good,\"', \"It works.\"\n\n");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 296 */       jSONArray1 = new JSONArray(" [\"<escape>\", next is an implied null , , ok,] ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 302 */       jSONObject = new JSONObject("{ fun => with non-standard forms ; forgiving => This package can be used to parse formats that are similar to but not stricting conforming to JSON; why=To make it easier to migrate existing data to JSON,one = [[1.00]]; uno=[[{1=>1}]];'+':+6e66 ;pluses=+++;empty = '' , 'double':0.666,true: TRUE, false: FALSE, null=NULL;[true] = [[!,@;*]]; string=>  o. k. ; # comment\r oct=0666; hex=0x666; dec=666; o=0999; noh=0x0x}");
/*     */ 
/*     */       
/* 305 */       if (!jSONObject.getBoolean("true") || !jSONObject.getBoolean("false"));
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 310 */       jSONObject = new JSONObject(jSONObject, new String[] { "dec", "oct", "hex", "missing" });
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 316 */       jSONObject = new JSONObject("{string: \"98.6\", long: 2147483648, int: 2147483647, longer: 9223372036854775807, double: 9223372036854775808}");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 340 */       jSONObject.put("good sized", Long.MAX_VALUE);
/*     */ 
/*     */       
/* 343 */       jSONArray1 = new JSONArray("[2147483647, 2147483648, 9223372036854775807, 9223372036854775808]");
/*     */ 
/*     */ 
/*     */       
/* 347 */       Iterator<String> iterator = jSONObject.keys();
/* 348 */       while (iterator.hasNext()) {
/* 349 */         str = iterator.next();
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 355 */       jSONObject = new JSONObject();
/* 356 */       jSONObject.accumulate("stooge", "Curly");
/* 357 */       jSONObject.accumulate("stooge", "Larry");
/* 358 */       jSONObject.accumulate("stooge", "Moe");
/* 359 */       jSONArray1 = jSONObject.getJSONArray("stooge");
/* 360 */       jSONArray1.put(5, "Shemp");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 366 */       str = "<xml empty><a></a><a>1</a><a>22</a><a>333</a></xml>";
/* 367 */       jSONObject = XML.toJSONObject(str);
/*     */ 
/*     */ 
/*     */       
/* 371 */       str = "<book><chapter>Content of the first chapter</chapter><chapter>Content of the second chapter      <chapter>Content of the first subchapter</chapter>      <chapter>Content of the second subchapter</chapter></chapter><chapter>Third Chapter</chapter></book>";
/* 372 */       jSONObject = XML.toJSONObject(str);
/*     */ 
/*     */ 
/*     */       
/* 376 */       Collection collection = null;
/* 377 */       Map map = null;
/*     */       
/* 379 */       jSONObject = new JSONObject(map);
/* 380 */       jSONArray1 = new JSONArray(collection);
/* 381 */       jSONObject.append("stooge", "Joe DeRita");
/* 382 */       jSONObject.append("stooge", "Shemp");
/* 383 */       jSONObject.accumulate("stooges", "Curly");
/* 384 */       jSONObject.accumulate("stooges", "Larry");
/* 385 */       jSONObject.accumulate("stooges", "Moe");
/* 386 */       jSONObject.accumulate("stoogearray", jSONObject.get("stooges"));
/* 387 */       jSONObject.put("map", map);
/* 388 */       jSONObject.put("collection", collection);
/* 389 */       jSONObject.put("array", jSONArray1);
/* 390 */       jSONArray1.put(map);
/* 391 */       jSONArray1.put(collection);
/*     */ 
/*     */       
/* 394 */       str = "{plist=Apple; AnimalSmells = { pig = piggish; lamb = lambish; worm = wormy; }; AnimalSounds = { pig = oink; lamb = baa; worm = baa;  Lisa = \"Why is the worm talking like a lamb?\" } ; AnimalColors = { pig = pink; lamb = black; worm = pink; } } ";
/* 395 */       jSONObject = new JSONObject(str);
/*     */ 
/*     */       
/* 398 */       str = " (\"San Francisco\", \"New York\", \"Seoul\", \"London\", \"Seattle\", \"Shanghai\")";
/* 399 */       jSONArray1 = new JSONArray(str);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 405 */       System.out.print("Exception: ");
/*     */       try {
/* 407 */         jSONArray1 = new JSONArray();
/* 408 */         jSONArray1.put(Double.NEGATIVE_INFINITY);
/* 409 */         jSONArray1.put(Double.NaN);
/*     */       }
/* 411 */       catch (Exception exception) {}
/*     */ 
/*     */       
/* 414 */       System.out.print("Exception: ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 420 */       System.out.print("Exception: ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 426 */       System.out.print("Exception: ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 432 */       System.out.print("Exception: ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 438 */       System.out.print("Exception: ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 444 */       System.out.print("Exception: ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 450 */       System.out.print("Exception: ");
/*     */       try {
/* 452 */         jSONObject = XML.toJSONObject("<a><b>    ");
/* 453 */       } catch (Exception exception) {}
/*     */ 
/*     */       
/* 456 */       System.out.print("Exception: ");
/*     */       try {
/* 458 */         jSONObject = XML.toJSONObject("<a></b>    ");
/* 459 */       } catch (Exception exception) {}
/*     */ 
/*     */       
/* 462 */       System.out.print("Exception: ");
/*     */       try {
/* 464 */         jSONObject = XML.toJSONObject("<a></a    ");
/* 465 */       } catch (Exception exception) {}
/*     */ 
/*     */       
/* 468 */       System.out.print("Exception: ");
/*     */       try {
/* 470 */         jSONArray2 = new JSONArray(new Object());
/*     */       }
/* 472 */       catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */       
/* 476 */       System.out.print("Exception: ");
/*     */       try {
/* 478 */         str = "[)";
/* 479 */         jSONArray1 = new JSONArray(str);
/*     */       }
/* 481 */       catch (Exception exception) {}
/*     */ 
/*     */     
/*     */     }
/* 485 */     catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/Test.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */