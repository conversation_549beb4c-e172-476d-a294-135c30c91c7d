/*    */ package org.json;
/*    */ 
/*    */ import java.util.Iterator;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CookieList
/*    */ {
/*    */   public static JSONObject toJSONObject(String paramString) throws JSONException {
/* 50 */     JSONObject jSONObject = new JSONObject();
/* 51 */     JSONTokener jSONTokener = new JSONTokener(paramString);
/* 52 */     while (jSONTokener.more()) {
/* 53 */       String str = Cookie.unescape(jSONTokener.nextTo('='));
/* 54 */       jSONTokener.next('=');
/* 55 */       jSONObject.put(str, Cookie.unescape(jSONTokener.nextTo(';')));
/* 56 */       jSONTokener.next();
/*    */     } 
/* 58 */     return jSONObject;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String toString(JSONObject paramJSONObject) throws JSONException {
/* 72 */     boolean bool = false;
/* 73 */     Iterator<E> iterator = paramJSONObject.keys();
/*    */     
/* 75 */     StringBuffer stringBuffer = new StringBuffer();
/* 76 */     while (iterator.hasNext()) {
/* 77 */       String str = iterator.next().toString();
/* 78 */       if (!paramJSONObject.isNull(str)) {
/* 79 */         if (bool) {
/* 80 */           stringBuffer.append(';');
/*    */         }
/* 82 */         stringBuffer.append(Cookie.escape(str));
/* 83 */         stringBuffer.append("=");
/* 84 */         stringBuffer.append(Cookie.escape(paramJSONObject.getString(str)));
/* 85 */         bool = true;
/*    */       } 
/*    */     } 
/* 88 */     return stringBuffer.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/CookieList.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */