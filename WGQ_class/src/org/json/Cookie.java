/*     */ package org.json;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Cookie
/*     */ {
/*     */   public static String escape(String paramString) {
/*  49 */     String str = paramString.trim();
/*  50 */     StringBuffer stringBuffer = new StringBuffer();
/*  51 */     int i = str.length();
/*  52 */     for (byte b = 0; b < i; b++) {
/*  53 */       char c = str.charAt(b);
/*  54 */       if (c < ' ' || c == '+' || c == '%' || c == '=' || c == ';') {
/*  55 */         stringBuffer.append('%');
/*  56 */         stringBuffer.append(Character.forDigit((char)(c >>> 4 & 0xF), 16));
/*  57 */         stringBuffer.append(Character.forDigit((char)(c & 0xF), 16));
/*     */       } else {
/*  59 */         stringBuffer.append(c);
/*     */       } 
/*     */     } 
/*  62 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject toJSONObject(String paramString) throws JSONException {
/*  83 */     JSONObject jSONObject = new JSONObject();
/*     */     
/*  85 */     JSONTokener jSONTokener = new JSONTokener(paramString);
/*  86 */     jSONObject.put("name", jSONTokener.nextTo('='));
/*  87 */     jSONTokener.next('=');
/*  88 */     jSONObject.put("value", jSONTokener.nextTo(';'));
/*  89 */     jSONTokener.next();
/*  90 */     while (jSONTokener.more()) {
/*  91 */       String str2, str1 = unescape(jSONTokener.nextTo("=;"));
/*  92 */       if (jSONTokener.next() != '=') {
/*  93 */         if (str1.equals("secure")) {
/*  94 */           Boolean bool = Boolean.TRUE;
/*     */         } else {
/*  96 */           throw jSONTokener.syntaxError("Missing '=' in cookie parameter.");
/*     */         } 
/*     */       } else {
/*  99 */         str2 = unescape(jSONTokener.nextTo(';'));
/* 100 */         jSONTokener.next();
/*     */       } 
/* 102 */       jSONObject.put(str1, str2);
/*     */     } 
/* 104 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(JSONObject paramJSONObject) throws JSONException {
/* 119 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 121 */     stringBuffer.append(escape(paramJSONObject.getString("name")));
/* 122 */     stringBuffer.append("=");
/* 123 */     stringBuffer.append(escape(paramJSONObject.getString("value")));
/* 124 */     if (paramJSONObject.has("expires")) {
/* 125 */       stringBuffer.append(";expires=");
/* 126 */       stringBuffer.append(paramJSONObject.getString("expires"));
/*     */     } 
/* 128 */     if (paramJSONObject.has("domain")) {
/* 129 */       stringBuffer.append(";domain=");
/* 130 */       stringBuffer.append(escape(paramJSONObject.getString("domain")));
/*     */     } 
/* 132 */     if (paramJSONObject.has("path")) {
/* 133 */       stringBuffer.append(";path=");
/* 134 */       stringBuffer.append(escape(paramJSONObject.getString("path")));
/*     */     } 
/* 136 */     if (paramJSONObject.optBoolean("secure")) {
/* 137 */       stringBuffer.append(";secure");
/*     */     }
/* 139 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String unescape(String paramString) {
/* 151 */     int i = paramString.length();
/* 152 */     StringBuffer stringBuffer = new StringBuffer();
/* 153 */     for (byte b = 0; b < i; b++) {
/* 154 */       char c = paramString.charAt(b);
/* 155 */       if (c == '+') {
/* 156 */         c = ' ';
/* 157 */       } else if (c == '%' && b + 2 < i) {
/* 158 */         int j = JSONTokener.dehexchar(paramString.charAt(b + 1));
/* 159 */         int k = JSONTokener.dehexchar(paramString.charAt(b + 2));
/* 160 */         if (j >= 0 && k >= 0) {
/* 161 */           c = (char)(j * 16 + k);
/* 162 */           b += 2;
/*     */         } 
/*     */       } 
/* 165 */       stringBuffer.append(c);
/*     */     } 
/* 167 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/Cookie.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */