/*     */ package org.json;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class XMLTokener
/*     */   extends JSONTokener
/*     */ {
/*  42 */   public static final HashMap entity = new HashMap<Object, Object>(8); static {
/*  43 */     entity.put("amp", XML.AMP);
/*  44 */     entity.put("apos", XML.APOS);
/*  45 */     entity.put("gt", XML.GT);
/*  46 */     entity.put("lt", XML.LT);
/*  47 */     entity.put("quot", XML.QUOT);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public XMLTokener(String paramString) {
/*  55 */     super(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String nextCDATA() throws JSONException {
/*     */     int i;
/*  66 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     do {
/*  68 */       char c = next();
/*  69 */       if (c == '\000') {
/*  70 */         throw syntaxError("Unclosed CDATA");
/*     */       }
/*  72 */       stringBuffer.append(c);
/*  73 */       i = stringBuffer.length() - 3;
/*  74 */     } while (i < 0 || stringBuffer.charAt(i) != ']' || stringBuffer.charAt(i + 1) != ']' || stringBuffer.charAt(i + 2) != '>');
/*     */     
/*  76 */     stringBuffer.setLength(i);
/*  77 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object nextContent() throws JSONException {
/*     */     while (true) {
/*  96 */       char c = next();
/*  97 */       if (!Character.isWhitespace(c)) {
/*  98 */         if (c == '\000') {
/*  99 */           return null;
/*     */         }
/* 101 */         if (c == '<') {
/* 102 */           return XML.LT;
/*     */         }
/* 104 */         StringBuffer stringBuffer = new StringBuffer();
/*     */         while (true) {
/* 106 */           if (c == '<' || c == '\000') {
/* 107 */             back();
/* 108 */             return stringBuffer.toString().trim();
/*     */           } 
/* 110 */           if (c == '&') {
/* 111 */             stringBuffer.append(nextEntity(c));
/*     */           } else {
/* 113 */             stringBuffer.append(c);
/*     */           } 
/* 115 */           c = next();
/*     */         } 
/*     */         break;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object nextEntity(char paramChar) throws JSONException {
/*     */     char c;
/* 128 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     while (true) {
/* 130 */       c = next();
/* 131 */       if (Character.isLetterOrDigit(c) || c == '#')
/* 132 */       { stringBuffer.append(Character.toLowerCase(c)); continue; }  break;
/* 133 */     }  if (c == ';') {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 139 */       String str = stringBuffer.toString();
/* 140 */       Object object = entity.get(str);
/* 141 */       return (object != null) ? object : (paramChar + str + ";");
/*     */     } 
/*     */     throw syntaxError("Missing ';' in XML entity: &" + stringBuffer);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object nextMeta() throws JSONException {
/*     */     char c;
/*     */     char c1;
/*     */     do {
/* 158 */       c = next();
/* 159 */     } while (Character.isWhitespace(c));
/* 160 */     switch (c) {
/*     */       case '\000':
/* 162 */         throw syntaxError("Misshaped meta tag");
/*     */       case '<':
/* 164 */         return XML.LT;
/*     */       case '>':
/* 166 */         return XML.GT;
/*     */       case '/':
/* 168 */         return XML.SLASH;
/*     */       case '=':
/* 170 */         return XML.EQ;
/*     */       case '!':
/* 172 */         return XML.BANG;
/*     */       case '?':
/* 174 */         return XML.QUEST;
/*     */       case '"':
/*     */       case '\'':
/* 177 */         c1 = c;
/*     */         while (true) {
/* 179 */           c = next();
/* 180 */           if (c == '\000') {
/* 181 */             throw syntaxError("Unterminated string");
/*     */           }
/* 183 */           if (c == c1) {
/* 184 */             return Boolean.TRUE;
/*     */           }
/*     */         } 
/*     */     } 
/*     */     while (true) {
/* 189 */       c = next();
/* 190 */       if (Character.isWhitespace(c)) {
/* 191 */         return Boolean.TRUE;
/*     */       }
/* 193 */       switch (c) { case '\000':
/*     */         case '!':
/*     */         case '"':
/*     */         case '\'':
/*     */         case '/':
/*     */         case '<':
/*     */         case '=':
/*     */         case '>':
/*     */         case '?':
/*     */           break; } 
/* 203 */     }  back();
/* 204 */     return Boolean.TRUE;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object nextToken() throws JSONException {
/*     */     char c;
/*     */     char c1;
/*     */     do {
/* 224 */       c = next();
/* 225 */     } while (Character.isWhitespace(c));
/* 226 */     switch (c) {
/*     */       case '\000':
/* 228 */         throw syntaxError("Misshaped element");
/*     */       case '<':
/* 230 */         throw syntaxError("Misplaced '<'");
/*     */       case '>':
/* 232 */         return XML.GT;
/*     */       case '/':
/* 234 */         return XML.SLASH;
/*     */       case '=':
/* 236 */         return XML.EQ;
/*     */       case '!':
/* 238 */         return XML.BANG;
/*     */       case '?':
/* 240 */         return XML.QUEST;
/*     */ 
/*     */ 
/*     */       
/*     */       case '"':
/*     */       case '\'':
/* 246 */         c1 = c;
/* 247 */         stringBuffer = new StringBuffer();
/*     */         while (true) {
/* 249 */           c = next();
/* 250 */           if (c == '\000') {
/* 251 */             throw syntaxError("Unterminated string");
/*     */           }
/* 253 */           if (c == c1) {
/* 254 */             return stringBuffer.toString();
/*     */           }
/* 256 */           if (c == '&') {
/* 257 */             stringBuffer.append(nextEntity(c)); continue;
/*     */           } 
/* 259 */           stringBuffer.append(c);
/*     */         } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 266 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     while (true)
/* 268 */     { stringBuffer.append(c);
/* 269 */       c = next();
/* 270 */       if (Character.isWhitespace(c)) {
/* 271 */         return stringBuffer.toString();
/*     */       }
/* 273 */       switch (c)
/*     */       { case '\000':
/*     */         case '!':
/*     */         case '/':
/*     */         case '=':
/*     */         case '>':
/*     */         case '?':
/*     */         case '[':
/*     */         case ']':
/* 282 */           back();
/* 283 */           return stringBuffer.toString();
/*     */         case '"':
/*     */         case '\'':
/*     */         case '<':
/* 287 */           break; }  }  throw syntaxError("Bad character in a name");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/XMLTokener.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */