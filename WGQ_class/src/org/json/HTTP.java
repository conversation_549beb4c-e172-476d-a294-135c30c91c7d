/*     */ package org.json;
/*     */ 
/*     */ import java.util.Iterator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HTTP
/*     */ {
/*     */   public static final String CRLF = "\r\n";
/*     */   
/*     */   public static JSONObject toJSONObject(String paramString) throws JSONException {
/*  72 */     JSONObject jSONObject = new JSONObject();
/*  73 */     HTTPTokener hTTPTokener = new HTTPTokener(paramString);
/*     */ 
/*     */     
/*  76 */     String str = hTTPTokener.nextToken();
/*  77 */     if (str.toUpperCase().startsWith("HTTP")) {
/*     */ 
/*     */ 
/*     */       
/*  81 */       jSONObject.put("HTTP-Version", str);
/*  82 */       jSONObject.put("Status-Code", hTTPTokener.nextToken());
/*  83 */       jSONObject.put("Reason-Phrase", hTTPTokener.nextTo(false));
/*  84 */       hTTPTokener.next();
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */       
/*  90 */       jSONObject.put("Method", str);
/*  91 */       jSONObject.put("Request-URI", hTTPTokener.nextToken());
/*  92 */       jSONObject.put("HTTP-Version", hTTPTokener.nextToken());
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  97 */     while (hTTPTokener.more()) {
/*  98 */       String str1 = hTTPTokener.nextTo(':');
/*  99 */       hTTPTokener.next(':');
/* 100 */       jSONObject.put(str1, hTTPTokener.nextTo(false));
/* 101 */       hTTPTokener.next();
/*     */     } 
/* 103 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(JSONObject paramJSONObject) throws JSONException {
/* 128 */     Iterator<E> iterator = paramJSONObject.keys();
/*     */     
/* 130 */     StringBuffer stringBuffer = new StringBuffer();
/* 131 */     if (paramJSONObject.has("Status-Code") && paramJSONObject.has("Reason-Phrase")) {
/* 132 */       stringBuffer.append(paramJSONObject.getString("HTTP-Version"));
/* 133 */       stringBuffer.append(' ');
/* 134 */       stringBuffer.append(paramJSONObject.getString("Status-Code"));
/* 135 */       stringBuffer.append(' ');
/* 136 */       stringBuffer.append(paramJSONObject.getString("Reason-Phrase"));
/* 137 */     } else if (paramJSONObject.has("Method") && paramJSONObject.has("Request-URI")) {
/* 138 */       stringBuffer.append(paramJSONObject.getString("Method"));
/* 139 */       stringBuffer.append(' ');
/* 140 */       stringBuffer.append('"');
/* 141 */       stringBuffer.append(paramJSONObject.getString("Request-URI"));
/* 142 */       stringBuffer.append('"');
/* 143 */       stringBuffer.append(' ');
/* 144 */       stringBuffer.append(paramJSONObject.getString("HTTP-Version"));
/*     */     } else {
/* 146 */       throw new JSONException("Not enough material for an HTTP header.");
/*     */     } 
/* 148 */     stringBuffer.append("\r\n");
/* 149 */     while (iterator.hasNext()) {
/* 150 */       String str = iterator.next().toString();
/* 151 */       if (!str.equals("HTTP-Version") && !str.equals("Status-Code") && !str.equals("Reason-Phrase") && !str.equals("Method") && !str.equals("Request-URI") && !paramJSONObject.isNull(str)) {
/*     */ 
/*     */         
/* 154 */         stringBuffer.append(str);
/* 155 */         stringBuffer.append(": ");
/* 156 */         stringBuffer.append(paramJSONObject.getString(str));
/* 157 */         stringBuffer.append("\r\n");
/*     */       } 
/*     */     } 
/* 160 */     stringBuffer.append("\r\n");
/* 161 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/json/HTTP.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */