/*     */ package org.gnu.stealthp.rsslib;
/*     */ 
/*     */ import java.util.Hashtable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class RSSObject
/*     */ {
/*     */   protected String about;
/*     */   protected String title;
/*     */   protected String link;
/*     */   protected String description;
/*     */   protected String pdate;
/*     */   protected RSSDoublinCoreModule dc;
/*  32 */   protected Hashtable dc_container = new Hashtable<Object, Object>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTitle(String paramString) {
/*  40 */     this.title = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAboutAttribute(String paramString) {
/*  48 */     this.about = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLink(String paramString) {
/*  56 */     this.link = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDescription(String paramString) {
/*  64 */     this.description = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPubDate(String paramString) {
/*  72 */     this.pdate = paramString;
/*     */   }
/*     */   
/*     */   public void setRSSDoublinCoreModule(RSSDoublinCoreModule paramRSSDoublinCoreModule) {
/*  76 */     this.dc = paramRSSDoublinCoreModule;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAboutAttribute() {
/*  84 */     return this.about;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTitle() {
/*  92 */     return this.title;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPubDate() {
/* 100 */     return this.pdate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLink() {
/* 108 */     return this.link;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDescription() {
/* 116 */     return this.description;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RSSDoublinCoreModule getRSSDoublinCoreModule() {
/* 124 */     if (this.dc != null)
/* 125 */       return this.dc; 
/* 126 */     return RSSDoublinCoreModule.buildDcModule(this.dc_container);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addDoublinCoreElement(String paramString1, String paramString2) {
/* 137 */     if (this.dc_container.containsKey(paramString1)) {
/* 138 */       this.dc_container.remove(paramString1);
/*     */     }
/* 140 */     this.dc_container.put(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getDoublinCoreElements() {
/* 149 */     if (this.dc_container.size() == 0) {
/* 150 */       return null;
/*     */     }
/* 152 */     return this.dc_container;
/*     */   }
/*     */   
/*     */   public abstract String toString();
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSObject.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */