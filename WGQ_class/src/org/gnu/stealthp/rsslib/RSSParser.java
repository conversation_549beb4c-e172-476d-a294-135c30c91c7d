/*     */ package org.gnu.stealthp.rsslib;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import javax.xml.parsers.ParserConfigurationException;
/*     */ import javax.xml.parsers.SAXParserFactory;
/*     */ import org.xml.sax.SAXException;
/*     */ import org.xml.sax.helpers.DefaultHandler;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RSSParser
/*     */ {
/*  30 */   private SAXParserFactory factory = RSSFactory.getInstance();
/*     */   
/*     */   private DefaultHandler hnd;
/*     */   
/*     */   private File f;
/*     */   
/*     */   public RSSParser() {
/*  37 */     this.validate = false;
/*     */   }
/*     */ 
/*     */   
/*     */   private URL u;
/*     */   private InputStream in;
/*     */   private boolean validate;
/*     */   
/*     */   public void setHandler(DefaultHandler paramDefaultHandler) {
/*  46 */     this.hnd = paramDefaultHandler;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setXmlResource(String paramString) throws RSSException {
/*  55 */     this.f = new File(paramString);
/*     */     try {
/*  57 */       this.in = new FileInputStream(this.f);
/*  58 */     } catch (Exception exception) {
/*  59 */       throw new RSSException("RSSParser::setXmlResource fails: " + exception.getMessage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setXmlResource(URL paramURL) throws RSSException {
/*  70 */     this.u = paramURL;
/*  71 */     Object object = null;
/*     */     try {
/*  73 */       URLConnection uRLConnection = this.u.openConnection();
/*  74 */       this.in = this.u.openStream();
/*  75 */       if (uRLConnection.getContentLength() == -1) {
/*  76 */         fixZeroLength();
/*     */       }
/*     */     }
/*  79 */     catch (IOException iOException) {
/*  80 */       throw new RSSException("RSSParser::setXmlResource fails: " + iOException.getMessage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setValidate(boolean paramBoolean) {
/*  90 */     this.validate = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void parseXmlFile(String paramString, DefaultHandler paramDefaultHandler, boolean paramBoolean) throws RSSException {
/* 101 */     RSSParser rSSParser = new RSSParser();
/* 102 */     rSSParser.setXmlResource(paramString);
/* 103 */     rSSParser.setHandler(paramDefaultHandler);
/* 104 */     rSSParser.setValidate(paramBoolean);
/* 105 */     rSSParser.parse();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void parseXmlFile(URL paramURL, DefaultHandler paramDefaultHandler, boolean paramBoolean) throws RSSException {
/* 116 */     RSSParser rSSParser = new RSSParser();
/* 117 */     rSSParser.setXmlResource(paramURL);
/* 118 */     rSSParser.setHandler(paramDefaultHandler);
/* 119 */     rSSParser.setValidate(paramBoolean);
/* 120 */     rSSParser.parse();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void fixZeroLength() throws IOException, RSSException {
/* 130 */     File file = File.createTempFile(".rsslib4jbugfix", ".tmp");
/* 131 */     file.deleteOnExit();
/* 132 */     FileWriter fileWriter = new FileWriter(file);
/* 133 */     BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(this.in));
/* 134 */     BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);
/* 135 */     String str = "";
/* 136 */     while ((str = bufferedReader.readLine()) != null) {
/* 137 */       str = Util.filterForXml(str);
/* 138 */       bufferedWriter.write(str + "\n");
/*     */     } 
/* 140 */     bufferedWriter.flush();
/* 141 */     bufferedWriter.close();
/* 142 */     bufferedReader.close();
/* 143 */     fileWriter.close();
/* 144 */     setXmlResource(file.getAbsolutePath());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void free() {
/* 152 */     this.factory = null;
/* 153 */     this.f = null;
/* 154 */     this.in = null;
/* 155 */     this.hnd = null;
/* 156 */     System.gc();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void parse() throws RSSException {
/*     */     try {
/* 165 */       this.factory.setValidating(this.validate);
/*     */       
/* 167 */       this.factory.newSAXParser().parse(this.in, this.hnd);
/*     */     }
/* 169 */     catch (SAXException sAXException) {
/* 170 */       throw new RSSException("RSSParser::parse fails: " + sAXException.getMessage());
/*     */     }
/* 172 */     catch (ParserConfigurationException parserConfigurationException) {
/* 173 */       throw new RSSException("RSSParser::parse fails: " + parserConfigurationException.getMessage());
/*     */     }
/* 175 */     catch (IOException iOException) {
/* 176 */       throw new RSSException("RSSParser::parse fails: " + iOException.getMessage());
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSParser.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */