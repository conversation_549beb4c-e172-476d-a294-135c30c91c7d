/*    */ package org.gnu.stealthp.rsslib;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RSSImage
/*    */   extends RSSObject
/*    */ {
/*    */   private String url;
/*    */   private String w;
/*    */   private String h;
/*    */   
/*    */   public void setUrl(String paramString) {
/* 29 */     this.url = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getUrl() {
/* 37 */     return this.url;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setWidth(String paramString) {
/* 45 */     this.w = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setHeight(String paramString) {
/* 53 */     this.h = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getWidth() {
/* 61 */     return this.w;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getHeight() {
/* 69 */     return this.h;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toHTML() {
/* 77 */     String str = "<a href=\"" + this.link + "\">";
/* 78 */     str = str + "<img src=\"" + this.url + "\" border=\"0\" ";
/* 79 */     str = str + ((this.w != null) ? ("width=\"" + this.w + " \"") : " ");
/* 80 */     str = str + ((this.h != null) ? ("height=\"" + this.h + " \"") : " ");
/* 81 */     str = str + ((this.title != null) ? ("alt=\"" + this.title + "\"") : "");
/* 82 */     str = str + "/>";
/* 83 */     str = str + "</a>";
/* 84 */     return str;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 92 */     return "TITLE: " + this.title + "\n" + "LINK: " + this.link + "\n" + "URL: " + this.url;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSImage.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */