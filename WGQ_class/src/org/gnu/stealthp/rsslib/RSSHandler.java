/*     */ package org.gnu.stealthp.rsslib;
/*     */ 
/*     */ import org.xml.sax.Attributes;
/*     */ import org.xml.sax.helpers.DefaultHandler;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RSSHandler
/*     */   extends DefaultHandler
/*     */ {
/* 106 */   private StringBuffer buff = new StringBuffer();
/* 107 */   private String current_tag = null; private RSSItem itm; private RSSImage img; private RSSSequence seq; private RSSSequenceElement seq_elem; private RSSTextInput input; private RSSSyndicationModule sy; private boolean reading_chan = false; private boolean reading_item = false; private boolean reading_image = false; private boolean reading_seq = false; private boolean reading_input = false; private boolean have_dc = false; public static final String CHANNEL_TAG = "channel"; public static final String TITLE_TAG = "title"; public static final String LINK_TAG = "link";
/* 108 */   private RSSChannel chan = new RSSChannel();
/*     */   
/*     */   public static final String DESCRIPTION_TAG = "description";
/*     */   
/*     */   public static final String ITEM_TAG = "item";
/*     */   
/*     */   public static final String IMAGE_TAG = "image";
/*     */   
/*     */   public static final String IMAGE_W_TAG = "width";
/*     */   
/*     */   public static final String IMAGE_H_TAG = "height";
/*     */   
/*     */   public static final String URL_TAG = "url";
/*     */   
/*     */   public static final String SEQ_TAG = "rdf:seq";
/*     */   
/*     */   public static final String SEQ_ELEMENT_TAG = "rdf:li";
/*     */   public static final String TEXTINPUT_TAG = "textinput";
/*     */   public static final String NAME_TAG = "name";
/*     */   public static final String LANGUAGE_TAG = "language";
/*     */   public static final String MANAGING_TAG = "managingEditor";
/*     */   public static final String WMASTER_TAG = "webMaster";
/*     */   public static final String COPY_TAG = "copyright";
/*     */   
/*     */   public void startElement(String paramString1, String paramString2, String paramString3, Attributes paramAttributes) {
/* 133 */     if (tagIsEqual(paramString3, "channel")) {
/* 134 */       this.reading_chan = true;
/* 135 */       processChanAboutAttribute(paramAttributes);
/*     */     } 
/*     */     
/* 138 */     if (tagIsEqual(paramString3, "item")) {
/* 139 */       this.reading_item = true;
/* 140 */       this.reading_chan = false;
/* 141 */       this.itm = new RSSItem();
/* 142 */       processItemAboutAttribute(paramAttributes);
/*     */     } 
/*     */     
/* 145 */     if (tagIsEqual(paramString3, "image")) {
/* 146 */       this.reading_image = true;
/* 147 */       this.reading_chan = false;
/* 148 */       this.img = new RSSImage();
/*     */     } 
/*     */     
/* 151 */     if (tagIsEqual(paramString3, "rdf:seq")) {
/* 152 */       this.reading_seq = true;
/* 153 */       this.seq = new RSSSequence();
/*     */     } 
/*     */     
/* 156 */     if (tagIsEqual(paramString3, "textinput")) {
/* 157 */       this.reading_input = true;
/* 158 */       this.reading_chan = false;
/* 159 */       this.input = new RSSTextInput();
/*     */     } 
/*     */ 
/*     */     
/* 163 */     if (tagIsEqual(paramString3, "rdf:li")) {
/* 164 */       processSeqElement(paramAttributes);
/*     */     }
/* 166 */     if (paramString3.toUpperCase().startsWith("SY:")) {
/* 167 */       this.sy = new RSSSyndicationModule();
/*     */     }
/* 169 */     this.current_tag = paramString3;
/*     */   }
/*     */   public static final String PUB_DATE_TAG = "pubDate"; public static final String LAST_B_DATE_TAG = "lastBuildDate"; public static final String GENERATOR_TAG = "generator"; public static final String DOCS_TAG = "docs"; public static final String TTL_TAG = "ttl";
/*     */   public static final String AUTHOR_TAG = "author";
/*     */   public static final String COMMENTS_TAG = "comments";
/*     */   public static final String CLOUD_TAG = "cloud";
/*     */   public static final String RATING_TAG = "rating";
/*     */   public static final String SKIPH_TAG = "skipHours";
/*     */   public static final String SKIPD_TAG = "skipDays";
/*     */   public static final String CATEGORY_TAG = "category";
/*     */   public static final String DC_TITLE_TAG = "dc:title";
/*     */   public static final String DC_CREATOR_TAG = "dc:creator";
/*     */   public static final String DC_SUBJECT_TAG = "dc:subject";
/*     */   
/*     */   public void endElement(String paramString1, String paramString2, String paramString3) {
/* 184 */     String str = this.buff.toString().trim();
/*     */     
/* 186 */     if (paramString3.equals(this.current_tag)) {
/* 187 */       str = this.buff.toString().trim();
/* 188 */       this.buff = new StringBuffer();
/*     */     } 
/*     */ 
/*     */     
/* 192 */     if (this.reading_chan) {
/* 193 */       processChannel(paramString3, str);
/*     */     }
/* 195 */     if (this.reading_item) {
/* 196 */       processItem(paramString3, str);
/*     */     }
/* 198 */     if (this.reading_image) {
/* 199 */       processImage(paramString3, str);
/*     */     }
/* 201 */     if (this.reading_input) {
/* 202 */       processTextInput(paramString3, str);
/*     */     }
/* 204 */     if (tagIsEqual(paramString3, "channel")) {
/* 205 */       this.reading_chan = false;
/* 206 */       this.chan.setSyndicationModule(this.sy);
/*     */     } 
/*     */     
/* 209 */     if (tagIsEqual(paramString3, "item")) {
/* 210 */       this.reading_item = false;
/* 211 */       this.chan.addItem(this.itm);
/*     */     } 
/*     */     
/* 214 */     if (tagIsEqual(paramString3, "image")) {
/* 215 */       this.reading_image = false;
/* 216 */       this.chan.setRSSImage(this.img);
/*     */     } 
/*     */ 
/*     */     
/* 220 */     if (tagIsEqual(paramString3, "rdf:seq")) {
/* 221 */       this.reading_seq = false;
/* 222 */       this.chan.addRSSSequence(this.seq);
/*     */     } 
/*     */     
/* 225 */     if (tagIsEqual(paramString3, "textinput")) {
/* 226 */       this.reading_input = false;
/* 227 */       this.chan.setRSSTextInput(this.input);
/*     */     } 
/*     */   }
/*     */   public static final String DC_DESCRIPTION_TAG = "dc:description"; public static final String DC_PUBLISHER_TAG = "dc:publisher"; public static final String DC_CONTRIBUTOR_TAG = "dc:contributor"; public static final String DC_DATE_TAG = "dc:date"; public static final String DC_TYPE_TAG = "dc:type";
/*     */   public static final String DC_FORMAT_TAG = "dc:format";
/*     */   public static final String DC_IDENTIFIER_TAG = "dc:identifier";
/*     */   public static final String DC_SOURCE_TAG = "dc:source";
/*     */   public static final String DC_LANGUAGE_TAG = "dc:language";
/*     */   public static final String DC_RELATION_TAG = "dc:relation";
/*     */   public static final String DC_COVERAGE_TAG = "dc:coverage";
/*     */   public static final String DC_RIGHTS_TAG = "dc:rights";
/*     */   public static final String SY_PERIOD_TAG = "sy:updatePeriod";
/*     */   public static final String SY_FREQ_TAG = "sy:updateFrequency";
/*     */   public static final String SY_BASE_TAG = "sy:updateBase";
/*     */   
/*     */   public void characters(char[] paramArrayOfchar, int paramInt1, int paramInt2) {
/* 243 */     String str = new String(paramArrayOfchar, paramInt1, paramInt2);
/*     */ 
/*     */     
/* 246 */     if (str.trim().length() == 0) {
/*     */       return;
/*     */     }
/* 249 */     this.buff.append(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processImage(String paramString1, String paramString2) {
/* 260 */     if (tagIsEqual(paramString1, "title")) {
/* 261 */       this.img.setTitle(paramString2);
/*     */     }
/* 263 */     if (tagIsEqual(paramString1, "link")) {
/* 264 */       this.img.setLink(paramString2);
/*     */     }
/* 266 */     if (tagIsEqual(paramString1, "url")) {
/* 267 */       this.img.setUrl(paramString2);
/*     */     }
/* 269 */     if (tagIsEqual(paramString1, "width")) {
/* 270 */       this.img.setWidth(paramString2);
/*     */     }
/* 272 */     if (tagIsEqual(paramString1, "height")) {
/* 273 */       this.img.setHeight(paramString2);
/*     */     }
/* 275 */     if (tagIsEqual(paramString1, "description")) {
/* 276 */       this.img.setDescription(paramString2);
/*     */     }
/* 278 */     if (paramString1.toUpperCase().startsWith("DC:")) {
/* 279 */       processDoublinCoreTags(paramString1, paramString2, this.img);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processTextInput(String paramString1, String paramString2) {
/* 292 */     if (tagIsEqual(paramString1, "title")) {
/* 293 */       this.input.setTitle(paramString2);
/*     */     }
/* 295 */     if (tagIsEqual(paramString1, "link")) {
/* 296 */       this.input.setLink(paramString2);
/*     */     }
/* 298 */     if (tagIsEqual(paramString1, "name")) {
/* 299 */       this.input.setInputName(paramString2);
/*     */     }
/* 301 */     if (tagIsEqual(paramString1, "description")) {
/* 302 */       this.input.setDescription(paramString2);
/*     */     }
/* 304 */     if (paramString1.toUpperCase().startsWith("DC:")) {
/* 305 */       processDoublinCoreTags(paramString1, paramString2, this.input);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processItem(String paramString1, String paramString2) {
/* 316 */     if (tagIsEqual(paramString1, "title")) {
/* 317 */       this.itm.setTitle(paramString2);
/*     */     }
/* 319 */     if (tagIsEqual(paramString1, "link")) {
/* 320 */       this.itm.setLink(paramString2);
/*     */     }
/* 322 */     if (tagIsEqual(paramString1, "description")) {
/* 323 */       this.itm.setDescription(paramString2);
/*     */     }
/* 325 */     if (tagIsEqual(paramString1, "pubDate")) {
/* 326 */       this.itm.setPubDate(paramString2);
/*     */     }
/* 328 */     if (tagIsEqual(paramString1, "pubDate")) {
/* 329 */       this.itm.setPubDate(paramString2);
/*     */     }
/* 331 */     if (tagIsEqual(paramString1, "author")) {
/* 332 */       this.itm.setAuthor(paramString2);
/*     */     }
/* 334 */     if (tagIsEqual(paramString1, "comments")) {
/* 335 */       this.itm.setComments(paramString2);
/*     */     }
/* 337 */     if (paramString1.toUpperCase().startsWith("DC:")) {
/* 338 */       processDoublinCoreTags(paramString1, paramString2, this.itm);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processChannel(String paramString1, String paramString2) {
/* 349 */     if (tagIsEqual(paramString1, "title")) {
/* 350 */       this.chan.setTitle(paramString2);
/*     */     }
/* 352 */     if (tagIsEqual(paramString1, "link")) {
/* 353 */       this.chan.setLink(paramString2);
/*     */     }
/* 355 */     if (tagIsEqual(paramString1, "description")) {
/* 356 */       this.chan.setDescription(paramString2);
/*     */     }
/* 358 */     if (tagIsEqual(paramString1, "copyright")) {
/* 359 */       this.chan.setCopyright(paramString2);
/*     */     }
/* 361 */     if (tagIsEqual(paramString1, "pubDate")) {
/* 362 */       this.chan.setPubDate(paramString2);
/*     */     }
/* 364 */     if (tagIsEqual(paramString1, "lastBuildDate")) {
/* 365 */       this.chan.setLastBuildDate(paramString2);
/*     */     }
/* 367 */     if (tagIsEqual(paramString1, "generator")) {
/* 368 */       this.chan.setGenerator(paramString2);
/*     */     }
/* 370 */     if (tagIsEqual(paramString1, "docs")) {
/* 371 */       this.chan.setDocs(paramString2);
/*     */     }
/* 373 */     if (tagIsEqual(paramString1, "ttl")) {
/* 374 */       this.chan.setTTL(paramString2);
/*     */     }
/* 376 */     if (tagIsEqual(paramString1, "language")) {
/* 377 */       this.chan.setLanguage(paramString2);
/*     */     }
/*     */     
/* 380 */     if (paramString1.toUpperCase().startsWith("DC:")) {
/* 381 */       processDoublinCoreTags(paramString1, paramString2, this.chan);
/*     */     }
/* 383 */     if (paramString1.toUpperCase().startsWith("SY:")) {
/* 384 */       processSyndicationTags(paramString1, paramString2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processDoublinCoreTags(String paramString1, String paramString2, RSSObject paramRSSObject) {
/* 395 */     paramRSSObject.addDoublinCoreElement(paramString1.toLowerCase(), paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void processSyndicationTags(String paramString1, String paramString2) {
/* 401 */     this; if (tagIsEqual(paramString1, "sy:updateBase")) {
/* 402 */       this.sy.setSyUpdateBase(paramString2);
/*     */     }
/* 404 */     this; if (tagIsEqual(paramString1, "sy:updateFrequency")) {
/* 405 */       this.sy.setSyUpdateFrequency(paramString2);
/*     */     }
/* 407 */     this; if (tagIsEqual(paramString1, "sy:updatePeriod")) {
/* 408 */       this.sy.setSyUpdatePeriod(paramString2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processSeqElement(Attributes paramAttributes) {
/* 417 */     String str = paramAttributes.getValue(0);
/* 418 */     this.seq_elem = new RSSSequenceElement();
/* 419 */     this.seq_elem.setResource(str);
/* 420 */     this.seq.addElement(this.seq_elem);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processItemAboutAttribute(Attributes paramAttributes) {
/* 431 */     String str = paramAttributes.getValue(0);
/* 432 */     this.itm.setAboutAttribute(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processChanAboutAttribute(Attributes paramAttributes) {
/* 442 */     String str = paramAttributes.getValue(0);
/* 443 */     this.chan.setAboutAttribute(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processInputAboutAttribute(Attributes paramAttributes) {
/* 453 */     String str = paramAttributes.getValue(0);
/* 454 */     this.input.setAboutAttribute(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static boolean tagIsEqual(String paramString1, String paramString2) {
/* 466 */     return paramString1.equalsIgnoreCase(paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RSSChannel getRSSChannel() {
/* 476 */     return this.chan;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSHandler.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */