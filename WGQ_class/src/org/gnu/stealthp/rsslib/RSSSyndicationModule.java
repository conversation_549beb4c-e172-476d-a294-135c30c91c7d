/*    */ package org.gnu.stealthp.rsslib;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RSSSyndicationModule
/*    */ {
/*    */   private String updatePeriod;
/*    */   private String updateFrequency;
/*    */   private String updateBase;
/*    */   
/*    */   public void setSyUpdatePeriod(String paramString) {
/* 43 */     this.updatePeriod = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setSyUpdateFrequency(String paramString) {
/* 51 */     this.updateFrequency = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setSyUpdateBase(String paramString) {
/* 59 */     this.updateBase = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getSyUpdatePeriod() {
/* 67 */     return this.updatePeriod;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getSyUpdateFrequency() {
/* 75 */     return this.updateFrequency;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getSyUpdateBase() {
/* 83 */     return this.updateBase;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 91 */     return "UPD_PERIOD: " + this.updatePeriod + "\nUPD_FREQ: " + this.updateFrequency + "\nUPD_BASE: " + this.updateBase;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSSyndicationModule.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */