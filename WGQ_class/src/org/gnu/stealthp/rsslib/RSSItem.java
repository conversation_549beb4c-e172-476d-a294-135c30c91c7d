/*     */ package org.gnu.stealthp.rsslib;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RSSItem
/*     */   extends RSSObject
/*     */ {
/*     */   private String date;
/*     */   private String auth;
/*     */   private String comm;
/*     */   
/*     */   public String getDate() {
/*  31 */     if (getDoublinCoreElements() == null) {
/*  32 */       if (getPubDate() == null) {
/*  33 */         this.date = null;
/*  34 */         return null;
/*     */       } 
/*  36 */       this.date = getPubDate();
/*  37 */       return this.date;
/*     */     } 
/*     */     
/*  40 */     this.date = (String)getDoublinCoreElements().get("dc:date");
/*  41 */     return this.date;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDate(String paramString) {
/*  50 */     this.date = paramString;
/*  51 */     if (getDoublinCoreElements() != null) {
/*  52 */       if (getDoublinCoreElements().containsKey("dc:date")) {
/*  53 */         addDoublinCoreElement("dc:date", paramString);
/*     */       } else {
/*  55 */         if (getPubDate() != null)
/*  56 */           setPubDate(paramString); 
/*  57 */         this.date = paramString;
/*     */       } 
/*     */     }
/*  60 */     this.date = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAuthor(String paramString) {
/*  68 */     this.auth = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setComments(String paramString) {
/*  76 */     this.comm = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getComments() {
/*  84 */     return this.comm;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAuthor() {
/*  92 */     return this.auth;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toString() {
/* 100 */     return "ABOUT ATTRIBUTE: " + this.about + "\n" + "TITLE: " + this.title + "\n" + "LINK: " + this.link + "\n" + "DESCRIPTION: " + this.description + "\n" + "DATE: " + getDate();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSItem.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */