/*    */ package org.gnu.stealthp.rsslib;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RSSTextInput
/*    */   extends RSSObject
/*    */ {
/*    */   private String name;
/*    */   
/*    */   public void setInputName(String paramString) {
/* 27 */     this.name = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getInputName() {
/* 35 */     return this.name;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getFormAction() {
/* 43 */     return getLink();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 51 */     return "FORM ACTION: " + getFormAction() + "\n" + "INPUT NAME: " + getInputName() + "\n" + "DESCRIPTION: " + getDescription();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toHTML() {
/* 61 */     return "<form method\"GET\" action=\"" + getFormAction() + "\">\n" + getDescription() + "<br>\n" + "<input type=\"text\" name=\"" + getInputName() + "\">\n</form>";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSTextInput.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */