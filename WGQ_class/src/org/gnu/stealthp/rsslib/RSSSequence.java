/*    */ package org.gnu.stealthp.rsslib;
/*    */ 
/*    */ import java.util.LinkedList;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RSSSequence
/*    */ {
/* 23 */   private LinkedList list = new LinkedList();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void addElement(RSSSequenceElement paramRSSSequenceElement) {
/* 31 */     this.list.add(paramRSSSequenceElement);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public LinkedList getElementList() {
/* 39 */     return this.list;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getListSize() {
/* 47 */     return this.list.size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 55 */     String str = "SEQUENCE HAS " + getListSize() + " ELEMENTS.\n";
/* 56 */     for (byte b = 0; b < this.list.size(); b++) {
/* 57 */       RSSSequenceElement rSSSequenceElement = this.list.get(b);
/* 58 */       str = str + rSSSequenceElement.toString() + "\n";
/*    */     } 
/* 60 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSSequence.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */