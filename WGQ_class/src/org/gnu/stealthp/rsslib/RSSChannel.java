/*     */ package org.gnu.stealthp.rsslib;
/*     */ 
/*     */ import java.util.LinkedList;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RSSChannel
/*     */   extends RSSObject
/*     */ {
/*  38 */   private LinkedList rss_items = new LinkedList();
/*     */   
/*     */   private RSSImage img;
/*     */   
/*     */   private RSSSequence seq;
/*     */   private RSSTextInput input;
/*     */   
/*     */   public void setLanguage(String paramString) {
/*  46 */     this.lang = paramString;
/*     */   }
/*     */   private RSSSyndicationModule sy; private String lang; private String copy; private String managing;
/*     */   private String master;
/*     */   private String bdate;
/*     */   private String udate;
/*     */   
/*     */   public void setCopyright(String paramString) {
/*  54 */     this.copy = paramString;
/*     */   }
/*     */   private String category;
/*     */   private String gen;
/*     */   private String t;
/*     */   private String docs;
/*     */   
/*     */   public void setLastBuildDate(String paramString) {
/*  62 */     this.bdate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setManagingEditor(String paramString) {
/*  70 */     this.managing = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWebMaster(String paramString) {
/*  78 */     this.master = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setGenerator(String paramString) {
/*  86 */     this.gen = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTTL(String paramString) {
/*  94 */     this.t = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDocs(String paramString) {
/* 102 */     this.docs = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRSSImage(RSSImage paramRSSImage) {
/* 110 */     this.img = paramRSSImage;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRSSTextInput(RSSTextInput paramRSSTextInput) {
/* 118 */     this.input = paramRSSTextInput;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLastBuildDate() {
/* 126 */     return this.bdate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCopyright() {
/* 134 */     return this.copy;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGenerator() {
/* 142 */     return this.gen;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTTL() {
/* 150 */     return this.t;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDocs() {
/* 158 */     return this.docs;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLanguage() {
/* 166 */     return this.lang;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWebMaster() {
/* 174 */     return this.master;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RSSTextInput getRSSTextInput() {
/* 182 */     return this.input;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addItem(RSSItem paramRSSItem) {
/* 190 */     this.rss_items.add(paramRSSItem);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addRSSSequence(RSSSequence paramRSSSequence) {
/* 198 */     this.seq = paramRSSSequence;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RSSImage getRSSImage() {
/* 206 */     return this.img;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public LinkedList getItems() {
/* 214 */     return this.rss_items;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RSSSequence getItemsSequence() {
/* 223 */     return this.seq;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSyndicationModule(RSSSyndicationModule paramRSSSyndicationModule) {
/* 231 */     this.sy = paramRSSSyndicationModule;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RSSSyndicationModule getRSSSyndicationModule() {
/* 239 */     return this.sy;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toString() {
/* 247 */     return "ABOUT ATTRIBUTE: " + this.about + "\n" + "TITLE: " + this.title + "\n" + "LINK: " + this.link + "\n" + "DESCRIPTION: " + this.description + "\nLANGUAGE: " + this.lang;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSChannel.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */