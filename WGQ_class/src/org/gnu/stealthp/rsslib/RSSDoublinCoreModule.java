/*     */ package org.gnu.stealthp.rsslib;
/*     */ 
/*     */ import java.util.Enumeration;
/*     */ import java.util.Hashtable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RSSDoublinCoreModule
/*     */ {
/*     */   private String title;
/*     */   private String creator;
/*     */   private String subject;
/*     */   private String description;
/*     */   private String publisher;
/*     */   private String contributor;
/*     */   private String date;
/*     */   private String type;
/*     */   private String format;
/*     */   private String identifier;
/*     */   private String source;
/*     */   private String language;
/*     */   private String relation;
/*     */   private String coverage;
/*     */   private String rights;
/*     */   
/*     */   public void setDcTitle(String paramString) {
/*  72 */     this.title = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcCreator(String paramString) {
/*  80 */     this.creator = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcSubject(String paramString) {
/*  88 */     this.subject = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcDescription(String paramString) {
/*  96 */     this.description = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcPublisher(String paramString) {
/* 104 */     this.publisher = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcContributor(String paramString) {
/* 112 */     this.contributor = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcDate(String paramString) {
/* 120 */     this.date = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcType(String paramString) {
/* 128 */     this.type = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcFormat(String paramString) {
/* 136 */     this.format = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcIdentifier(String paramString) {
/* 144 */     this.identifier = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcSource(String paramString) {
/* 152 */     this.source = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcLanguage(String paramString) {
/* 160 */     this.language = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcRelation(String paramString) {
/* 168 */     this.relation = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcCoverage(String paramString) {
/* 176 */     this.coverage = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDcRights(String paramString) {
/* 184 */     this.rights = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcTitle() {
/* 192 */     return this.title;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcCreator() {
/* 200 */     return this.creator;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcSubject() {
/* 208 */     return this.subject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcDescription() {
/* 216 */     return this.description;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcPublisher() {
/* 224 */     return this.publisher;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcContributor() {
/* 232 */     return this.contributor;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcDate() {
/* 240 */     return this.date;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcType() {
/* 248 */     return this.type;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcFormat() {
/* 256 */     return this.format;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcIdentifier() {
/* 264 */     return this.identifier;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcSource() {
/* 272 */     return this.source;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcLanguage() {
/* 280 */     return this.language;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcRelation() {
/* 288 */     return this.relation;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcCoverage() {
/* 296 */     return this.coverage;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDcRight() {
/* 304 */     return this.title;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toString() {
/* 312 */     return "DC SUBJECT: " + this.subject + "\nDC CREATOR: " + this.creator;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static RSSDoublinCoreModule buildDcModule(Hashtable paramHashtable) {
/* 323 */     if (paramHashtable == null || paramHashtable.size() == 0) {
/* 324 */       return null;
/*     */     }
/* 326 */     RSSDoublinCoreModule rSSDoublinCoreModule = new RSSDoublinCoreModule();
/*     */     
/* 328 */     Hashtable hashtable = paramHashtable;
/*     */     
/* 330 */     Enumeration<String> enumeration = paramHashtable.keys();
/*     */     
/* 332 */     while (enumeration.hasMoreElements()) {
/*     */       
/* 334 */       String str1 = enumeration.nextElement();
/* 335 */       String str2 = (String)hashtable.get(str1);
/*     */       
/* 337 */       if (str2 == null) {
/*     */         continue;
/*     */       }
/* 340 */       if (RSSHandler.tagIsEqual("dc:title", str1)) {
/* 341 */         rSSDoublinCoreModule.setDcTitle(str2);
/*     */       }
/* 343 */       if (RSSHandler.tagIsEqual("dc:creator", str1)) {
/* 344 */         rSSDoublinCoreModule.setDcCreator(str2);
/*     */       }
/* 346 */       if (RSSHandler.tagIsEqual("dc:subject", str1)) {
/* 347 */         rSSDoublinCoreModule.setDcSubject(str2);
/*     */       }
/* 349 */       if (RSSHandler.tagIsEqual("dc:description", str1)) {
/* 350 */         rSSDoublinCoreModule.setDcDescription(str2);
/*     */       }
/* 352 */       if (RSSHandler.tagIsEqual("dc:publisher", str1)) {
/* 353 */         rSSDoublinCoreModule.setDcPublisher(str2);
/*     */       }
/* 355 */       if (RSSHandler.tagIsEqual("dc:contributor", str1)) {
/* 356 */         rSSDoublinCoreModule.setDcContributor(str2);
/*     */       }
/* 358 */       if (RSSHandler.tagIsEqual("dc:date", str1)) {
/* 359 */         rSSDoublinCoreModule.setDcDate(str2);
/*     */       }
/* 361 */       if (RSSHandler.tagIsEqual("dc:type", str1)) {
/* 362 */         rSSDoublinCoreModule.setDcType(str2);
/*     */       }
/* 364 */       if (RSSHandler.tagIsEqual("dc:format", str1)) {
/* 365 */         rSSDoublinCoreModule.setDcFormat(str2);
/*     */       }
/* 367 */       if (RSSHandler.tagIsEqual("dc:identifier", str1)) {
/* 368 */         rSSDoublinCoreModule.setDcIdentifier(str2);
/*     */       }
/* 370 */       if (RSSHandler.tagIsEqual("dc:source", str1)) {
/* 371 */         rSSDoublinCoreModule.setDcSource(str2);
/*     */       }
/* 373 */       if (RSSHandler.tagIsEqual("dc:language", str1)) {
/* 374 */         rSSDoublinCoreModule.setDcLanguage(str2);
/*     */       }
/* 376 */       if (RSSHandler.tagIsEqual("dc:relation", str1)) {
/* 377 */         rSSDoublinCoreModule.setDcRelation(str2);
/*     */       }
/* 379 */       if (RSSHandler.tagIsEqual("dc:coverage", str1)) {
/* 380 */         rSSDoublinCoreModule.setDcCoverage(str2);
/*     */       }
/* 382 */       if (RSSHandler.tagIsEqual("dc:rights", str1)) {
/* 383 */         rSSDoublinCoreModule.setDcRights(str2);
/*     */       }
/*     */     } 
/* 386 */     return rSSDoublinCoreModule;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSDoublinCoreModule.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */