/*    */ package org.gnu.stealthp.rsslib;
/*    */ 
/*    */ import javax.xml.parsers.SAXParserFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RSSFactory
/*    */ {
/*    */   private static SAXParserFactory factory;
/*    */   
/*    */   protected static SAXParserFactory getInstance() {
/* 25 */     if (factory == null)
/* 26 */       factory = SAXParserFactory.newInstance(); 
/* 27 */     return factory;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/gnu/stealthp/rsslib/RSSFactory.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */