/*     */ package org.dom4j.io;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.CharArrayReader;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.Reader;
/*     */ import java.net.URL;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.DocumentException;
/*     */ import org.dom4j.DocumentFactory;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.ElementHandler;
/*     */ import org.dom4j.QName;
/*     */ import org.xmlpull.v1.XmlPullParser;
/*     */ import org.xmlpull.v1.XmlPullParserException;
/*     */ import org.xmlpull.v1.XmlPullParserFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class XPPPacketReader
/*     */ {
/*     */   private DocumentFactory factory;
/*     */   private XmlPullParser xppParser;
/*     */   private XmlPullParserFactory xppFactory;
/*     */   private DispatchHandler dispatchHandler;
/*     */   
/*     */   public XPPPacketReader() {}
/*     */   
/*     */   public XPPPacketReader(DocumentFactory paramDocumentFactory) {
/*  57 */     this.factory = paramDocumentFactory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(File paramFile) throws DocumentException, IOException, XmlPullParserException {
/*  70 */     String str = paramFile.getAbsolutePath();
/*  71 */     return read(new BufferedReader(new FileReader(paramFile)), str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(URL paramURL) throws DocumentException, IOException, XmlPullParserException {
/*  82 */     String str = paramURL.toExternalForm();
/*  83 */     return read(createReader(paramURL.openStream()), str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(String paramString) throws DocumentException, IOException, XmlPullParserException {
/* 103 */     if (paramString.indexOf(':') >= 0)
/*     */     {
/* 105 */       return read(new URL(paramString));
/*     */     }
/*     */ 
/*     */     
/* 109 */     return read(new File(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(InputStream paramInputStream) throws DocumentException, IOException, XmlPullParserException {
/* 121 */     return read(createReader(paramInputStream));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(Reader paramReader) throws DocumentException, IOException, XmlPullParserException {
/* 132 */     getXPPParser().setInput(paramReader);
/* 133 */     return parseDocument();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(char[] paramArrayOfchar) throws DocumentException, IOException, XmlPullParserException {
/* 144 */     getXPPParser().setInput(new CharArrayReader(paramArrayOfchar));
/* 145 */     return parseDocument();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(InputStream paramInputStream, String paramString) throws DocumentException, IOException, XmlPullParserException {
/* 157 */     return read(createReader(paramInputStream), paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document read(Reader paramReader, String paramString) throws DocumentException, IOException, XmlPullParserException {
/* 169 */     Document document = read(paramReader);
/* 170 */     document.setName(paramString);
/* 171 */     return document;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public XmlPullParser getXPPParser() throws XmlPullParserException {
/* 179 */     if (this.xppParser == null) {
/* 180 */       this.xppParser = getXPPFactory().newPullParser();
/*     */     }
/* 182 */     return this.xppParser;
/*     */   }
/*     */   
/*     */   public XmlPullParserFactory getXPPFactory() throws XmlPullParserException {
/* 186 */     if (this.xppFactory == null) {
/* 187 */       this.xppFactory = XmlPullParserFactory.newInstance();
/*     */     }
/* 189 */     this.xppFactory.setNamespaceAware(true);
/* 190 */     return this.xppFactory;
/*     */   }
/*     */   
/*     */   public void setXPPFactory(XmlPullParserFactory paramXmlPullParserFactory) {
/* 194 */     this.xppFactory = paramXmlPullParserFactory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DocumentFactory getDocumentFactory() {
/* 201 */     if (this.factory == null) {
/* 202 */       this.factory = DocumentFactory.getInstance();
/*     */     }
/* 204 */     return this.factory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDocumentFactory(DocumentFactory paramDocumentFactory) {
/* 215 */     this.factory = paramDocumentFactory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addHandler(String paramString, ElementHandler paramElementHandler) {
/* 228 */     getDispatchHandler().addHandler(paramString, paramElementHandler);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeHandler(String paramString) {
/* 238 */     getDispatchHandler().removeHandler(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDefaultHandler(ElementHandler paramElementHandler) {
/* 251 */     getDispatchHandler().setDefaultHandler(paramElementHandler);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Document parseDocument() throws DocumentException, IOException, XmlPullParserException {
/* 257 */     DocumentFactory documentFactory = getDocumentFactory();
/* 258 */     Document document = documentFactory.createDocument();
/* 259 */     Element element = null;
/* 260 */     XmlPullParser xmlPullParser = getXPPParser();
/* 261 */     byte b = 0; while (true) {
/*     */       String str2; QName qName; String str1; int j; Element element1;
/* 263 */       int k, m, n, i = -1;
/* 264 */       i = xmlPullParser.nextToken();
/* 265 */       switch (i) {
/*     */         case 8:
/* 267 */           str2 = xmlPullParser.getText();
/* 268 */           j = str2.indexOf(" ");
/* 269 */           if (j >= 0) {
/* 270 */             document.addProcessingInstruction(str2.substring(0, j), str2.substring(j + 1));
/*     */             continue;
/*     */           } 
/* 273 */           document.addProcessingInstruction(str2, "");
/*     */ 
/*     */         
/*     */         case 9:
/* 277 */           if (element != null) {
/* 278 */             element.addComment(xmlPullParser.getText()); continue;
/*     */           } 
/* 280 */           document.addComment(xmlPullParser.getText());
/*     */ 
/*     */         
/*     */         case 5:
/* 284 */           str2 = xmlPullParser.getText();
/* 285 */           if (element != null) {
/* 286 */             element.addCDATA(str2);
/*     */             continue;
/*     */           } 
/* 289 */           if (str2.trim().length() > 0) {
/* 290 */             throw new DocumentException("Cannot have text content outside of the root document");
/*     */           }
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         case 6:
/* 297 */           str2 = xmlPullParser.getText();
/* 298 */           if (element != null) {
/* 299 */             element.addText(str2);
/*     */             continue;
/*     */           } 
/* 302 */           if (str2.trim().length() > 0) {
/* 303 */             throw new DocumentException("Cannot have an entityref outside of the root document");
/*     */           }
/*     */ 
/*     */ 
/*     */         
/*     */         case 1:
/* 309 */           return document;
/*     */         
/*     */         case 2:
/* 312 */           qName = (xmlPullParser.getPrefix() == null) ? documentFactory.createQName(xmlPullParser.getName(), xmlPullParser.getNamespace()) : documentFactory.createQName(xmlPullParser.getName(), xmlPullParser.getPrefix(), xmlPullParser.getNamespace());
/* 313 */           element1 = null;
/*     */ 
/*     */ 
/*     */           
/* 317 */           if ("jabber:client".equals(qName.getNamespaceURI()) || "jabber:server".equals(qName.getNamespaceURI()) || "jabber:component:accept".equals(qName.getNamespaceURI()) || "http://jabber.org/protocol/httpbind".equals(qName.getNamespaceURI())) {
/*     */ 
/*     */ 
/*     */             
/* 321 */             element1 = documentFactory.createElement(xmlPullParser.getName());
/*     */           } else {
/*     */             
/* 324 */             element1 = documentFactory.createElement(qName);
/*     */           } 
/* 326 */           k = xmlPullParser.getNamespaceCount(xmlPullParser.getDepth() - 1);
/* 327 */           m = xmlPullParser.getNamespaceCount(xmlPullParser.getDepth());
/* 328 */           for (n = k; n < m; n++) {
/* 329 */             if (xmlPullParser.getNamespacePrefix(n) != null)
/* 330 */               element1.addNamespace(xmlPullParser.getNamespacePrefix(n), xmlPullParser.getNamespaceUri(n)); 
/* 331 */           }  for (n = 0; n < xmlPullParser.getAttributeCount(); n++) {
/* 332 */             QName qName1 = (xmlPullParser.getAttributePrefix(n) == null) ? documentFactory.createQName(xmlPullParser.getAttributeName(n)) : documentFactory.createQName(xmlPullParser.getAttributeName(n), xmlPullParser.getAttributePrefix(n), xmlPullParser.getAttributeNamespace(n));
/* 333 */             element1.addAttribute(qName1, xmlPullParser.getAttributeValue(n));
/*     */           } 
/* 335 */           if (element != null) {
/* 336 */             element.add(element1);
/*     */           } else {
/*     */             
/* 339 */             document.add(element1);
/*     */           } 
/* 341 */           element = element1;
/* 342 */           b++;
/*     */ 
/*     */         
/*     */         case 3:
/* 346 */           if (element != null) {
/* 347 */             element = element.getParent();
/*     */           }
/* 349 */           b--;
/* 350 */           if (b < 1) {
/* 351 */             return document;
/*     */           }
/*     */ 
/*     */         
/*     */         case 4:
/* 356 */           str1 = xmlPullParser.getText();
/* 357 */           if (element != null) {
/* 358 */             element.addText(str1);
/*     */             continue;
/*     */           } 
/* 361 */           if (str1.trim().length() > 0) {
/* 362 */             throw new DocumentException("Cannot have text content outside of the root document");
/*     */           }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected DispatchHandler getDispatchHandler() {
/* 376 */     if (this.dispatchHandler == null) {
/* 377 */       this.dispatchHandler = new DispatchHandler();
/*     */     }
/* 379 */     return this.dispatchHandler;
/*     */   }
/*     */   
/*     */   protected void setDispatchHandler(DispatchHandler paramDispatchHandler) {
/* 383 */     this.dispatchHandler = paramDispatchHandler;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Reader createReader(InputStream paramInputStream) throws IOException {
/* 390 */     return new BufferedReader(new InputStreamReader(paramInputStream));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/dom4j/io/XPPPacketReader.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */