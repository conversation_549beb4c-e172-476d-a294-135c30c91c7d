/*     */ package org.dom4j.io;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.Reader;
/*     */ import org.xmlpull.mxp1.MXParser;
/*     */ import org.xmlpull.v1.XmlPullParserException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MXParser
/*     */   extends MXParser
/*     */ {
/*  34 */   private long lastHeartbeat = 0L;
/*     */ 
/*     */ 
/*     */   
/*     */   protected int nextImpl() throws XmlPullParserException, IOException {
/*  39 */     this.text = null;
/*  40 */     this.pcEnd = this.pcStart = 0;
/*  41 */     this.usePC = false;
/*  42 */     this.bufStart = this.posEnd;
/*  43 */     if (this.pastEndTag) {
/*  44 */       this.pastEndTag = false;
/*  45 */       this.depth--;
/*  46 */       this.namespaceEnd = this.elNamespaceCount[this.depth];
/*     */     } 
/*  48 */     if (this.emptyElementTag) {
/*  49 */       this.emptyElementTag = false;
/*  50 */       this.pastEndTag = true;
/*  51 */       return this.eventType = 3;
/*     */     } 
/*     */ 
/*     */     
/*  55 */     if (this.depth > 0) {
/*     */       char c;
/*  57 */       if (this.seenStartTag) {
/*  58 */         this.seenStartTag = false;
/*  59 */         return this.eventType = parseStartTag();
/*     */       } 
/*  61 */       if (this.seenEndTag) {
/*  62 */         this.seenEndTag = false;
/*  63 */         return this.eventType = parseEndTag();
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  69 */       if (this.seenMarkup) {
/*  70 */         this.seenMarkup = false;
/*  71 */         c = '<';
/*  72 */       } else if (this.seenAmpersand) {
/*  73 */         this.seenAmpersand = false;
/*  74 */         c = '&';
/*     */       } else {
/*  76 */         c = more();
/*     */       } 
/*  78 */       this.posStart = this.pos - 1;
/*     */ 
/*     */       
/*  81 */       boolean bool1 = false;
/*     */ 
/*     */       
/*  84 */       boolean bool2 = false;
/*     */ 
/*     */ 
/*     */       
/*     */       while (true) {
/*  89 */         if (c == '<') {
/*  90 */           if (bool1)
/*     */           {
/*  92 */             if (this.tokenize) {
/*  93 */               this.seenMarkup = true;
/*  94 */               return this.eventType = 4;
/*     */             } 
/*     */           }
/*  97 */           c = more();
/*  98 */           if (c == '/') {
/*  99 */             if (!this.tokenize && bool1) {
/* 100 */               this.seenEndTag = true;
/*     */               
/* 102 */               return this.eventType = 4;
/*     */             } 
/* 104 */             return this.eventType = parseEndTag();
/* 105 */           }  if (c == '!') {
/* 106 */             c = more();
/* 107 */             if (c == '-') {
/*     */               
/* 109 */               parseComment();
/* 110 */               if (this.tokenize) return this.eventType = 9; 
/* 111 */               if (!this.usePC && bool1) {
/* 112 */                 bool2 = true;
/*     */               } else {
/* 114 */                 this.posStart = this.pos;
/*     */               } 
/* 116 */             } else if (c == '[') {
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 121 */               parseCDSect(bool1);
/* 122 */               if (this.tokenize) return this.eventType = 5; 
/* 123 */               int i = this.posStart;
/* 124 */               int j = this.posEnd;
/* 125 */               int k = j - i;
/*     */ 
/*     */               
/* 128 */               if (k > 0) {
/* 129 */                 bool1 = true;
/* 130 */                 if (!this.usePC) {
/* 131 */                   bool2 = true;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/*     */                 }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/*     */               }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 173 */               throw new XmlPullParserException("unexpected character in markup " + printable(c), this, null);
/*     */             }
/*     */           
/* 176 */           } else if (c == '?') {
/* 177 */             parsePI();
/* 178 */             if (this.tokenize) return this.eventType = 8; 
/* 179 */             if (!this.usePC && bool1) {
/* 180 */               bool2 = true;
/*     */             } else {
/* 182 */               this.posStart = this.pos;
/*     */             } 
/*     */           } else {
/* 185 */             if (isNameStartChar(c)) {
/* 186 */               if (!this.tokenize && bool1) {
/* 187 */                 this.seenStartTag = true;
/*     */                 
/* 189 */                 return this.eventType = 4;
/*     */               } 
/* 191 */               return this.eventType = parseStartTag();
/*     */             } 
/* 193 */             throw new XmlPullParserException("unexpected character in markup " + printable(c), this, null);
/*     */           
/*     */           }
/*     */         
/*     */         }
/* 198 */         else if (c == '&') {
/*     */ 
/*     */           
/* 201 */           if (this.tokenize && bool1) {
/* 202 */             this.seenAmpersand = true;
/* 203 */             return this.eventType = 4;
/*     */           } 
/* 205 */           int i = this.posStart + this.bufAbsoluteStart;
/* 206 */           int j = this.posEnd + this.bufAbsoluteStart;
/* 207 */           char[] arrayOfChar = parseEntityRef();
/* 208 */           if (this.tokenize) return this.eventType = 6;
/*     */           
/* 210 */           if (arrayOfChar == null) {
/* 211 */             if (this.entityRefName == null) {
/* 212 */               this.entityRefName = newString(this.buf, this.posStart, this.posEnd - this.posStart);
/*     */             }
/* 214 */             throw new XmlPullParserException("could not resolve entity named '" + printable(this.entityRefName) + "'", this, null);
/*     */           } 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 220 */           this.posStart = i - this.bufAbsoluteStart;
/* 221 */           this.posEnd = j - this.bufAbsoluteStart;
/* 222 */           if (!this.usePC) {
/* 223 */             if (bool1) {
/* 224 */               joinPC();
/* 225 */               bool2 = false;
/*     */             } else {
/* 227 */               this.usePC = true;
/* 228 */               this.pcStart = this.pcEnd = 0;
/*     */             } 
/*     */           }
/*     */ 
/*     */           
/* 233 */           for (byte b = 0; b < arrayOfChar.length; b++) {
/*     */             
/* 235 */             if (this.pcEnd >= this.pc.length) ensurePC(this.pcEnd); 
/* 236 */             this.pc[this.pcEnd++] = arrayOfChar[b];
/*     */           } 
/*     */           
/* 239 */           bool1 = true;
/*     */         }
/*     */         else {
/*     */           
/* 243 */           if (bool2) {
/*     */             
/* 245 */             joinPC();
/*     */             
/* 247 */             bool2 = false;
/*     */           } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 258 */           bool1 = true;
/*     */           
/* 260 */           boolean bool3 = false;
/* 261 */           boolean bool4 = (!this.tokenize || !this.roundtripSupported) ? true : false;
/*     */           
/* 263 */           boolean bool5 = false;
/* 264 */           boolean bool6 = false;
/*     */ 
/*     */           
/*     */           do {
/* 268 */             if (this.eventType == 3 && (c == ' ' || c == '\n' || c == '\t'))
/*     */             {
/*     */               
/* 271 */               this.lastHeartbeat = System.currentTimeMillis();
/*     */             }
/* 273 */             if (c == ']')
/* 274 */             { if (bool5) {
/* 275 */                 bool6 = true;
/*     */               } else {
/* 277 */                 bool5 = true;
/*     */               }  }
/* 279 */             else { if (bool6 && c == '>') {
/* 280 */                 throw new XmlPullParserException("characters ]]> are not allowed in content", this, null);
/*     */               }
/*     */               
/* 283 */               if (bool5) {
/* 284 */                 bool6 = bool5 = false;
/*     */               } }
/*     */ 
/*     */             
/* 288 */             if (bool4)
/*     */             {
/* 290 */               if (c == '\r') {
/* 291 */                 bool3 = true;
/* 292 */                 this.posEnd = this.pos - 1;
/*     */                 
/* 294 */                 if (!this.usePC) {
/* 295 */                   if (this.posEnd > this.posStart) {
/* 296 */                     joinPC();
/*     */                   } else {
/* 298 */                     this.usePC = true;
/* 299 */                     this.pcStart = this.pcEnd = 0;
/*     */                   } 
/*     */                 }
/*     */                 
/* 303 */                 if (this.pcEnd >= this.pc.length) ensurePC(this.pcEnd); 
/* 304 */                 this.pc[this.pcEnd++] = '\n';
/* 305 */               } else if (c == '\n') {
/*     */                 
/* 307 */                 if (!bool3 && this.usePC) {
/* 308 */                   if (this.pcEnd >= this.pc.length) ensurePC(this.pcEnd); 
/* 309 */                   this.pc[this.pcEnd++] = '\n';
/*     */                 } 
/* 311 */                 bool3 = false;
/*     */               } else {
/* 313 */                 if (this.usePC) {
/* 314 */                   if (this.pcEnd >= this.pc.length) ensurePC(this.pcEnd); 
/* 315 */                   this.pc[this.pcEnd++] = c;
/*     */                 } 
/* 317 */                 bool3 = false;
/*     */               } 
/*     */             }
/*     */             
/* 321 */             c = more();
/* 322 */           } while (c != '<' && c != '&');
/* 323 */           this.posEnd = this.pos - 1;
/*     */           continue;
/*     */         } 
/* 326 */         c = more();
/*     */       } 
/*     */     } 
/* 329 */     if (this.seenRoot) {
/* 330 */       return parseEpilog();
/*     */     }
/* 332 */     return parseProlog();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long getLastHeartbeat() {
/* 345 */     return this.lastHeartbeat;
/*     */   }
/*     */   
/*     */   public void resetInput() {
/* 349 */     Reader reader = this.reader;
/* 350 */     String str = this.inputEncoding;
/* 351 */     reset();
/* 352 */     this.reader = reader;
/* 353 */     this.inputEncoding = str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/dom4j/io/MXParser.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */