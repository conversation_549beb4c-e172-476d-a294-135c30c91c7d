/*     */ package org.jabber.JabberHTTPBind;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.net.InetAddress;
/*     */ import java.security.MessageDigest;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.xml.parsers.DocumentBuilder;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class JHBServlet
/*     */   extends HttpServlet
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   public static final String APP_VERSION = "1.1.1";
/*     */   public static final String APP_NAME = "Jabber HTTP Binding Servlet";
/*     */   public static final boolean DEBUG = false;
/*     */   public static final int DEBUG_LEVEL = 2;
/*     */   private DocumentBuilder db;
/*     */   private Janitor janitor;
/*     */   private static JHBServlet srv;
/*     */   
/*     */   public void init() throws ServletException {}
/*     */   
/*     */   public void destroy() {}
/*     */   
/*     */   public static String hex(byte[] array) {
/*  67 */     StringBuffer sb = new StringBuffer();
/*  68 */     for (int i = 0; i < array.length; i++) {
/*  69 */       sb.append(
/*  70 */           Integer.toHexString(array[i] & 0xFF | 0x100).toLowerCase().substring(1, 3));
/*     */     }
/*  72 */     return sb.toString();
/*     */   }
/*     */   
/*     */   public static String sha1(String message) {
/*     */     try {
/*  77 */       MessageDigest sha = MessageDigest.getInstance("SHA-1");
/*  78 */       return hex(sha.digest(message.getBytes()));
/*  79 */     } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/*     */       
/*  81 */       return null;
/*     */     } 
/*     */   }
/*     */   public static void dbg(String msg) {
/*  85 */     dbg(msg, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void dbg(String msg, int lvl) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean isValidRoute(String route) {
/* 136 */     if (!route.startsWith("xmpp:")) {
/* 137 */       return false;
/*     */     }
/*     */     
/* 140 */     route = route.substring("xmpp:".length());
/*     */     
/*     */     int port;
/*     */     
/* 144 */     if ((port = route.lastIndexOf(":")) != -1) {
/*     */       try {
/* 146 */         int p = Integer.parseInt(route.substring(port + 1));
/* 147 */         if (p < 0 || p > 65535) {
/* 148 */           return false;
/*     */         }
/* 150 */       } catch (NumberFormatException nfe) {
/* 151 */         return false;
/*     */       } 
/* 153 */       route = route.substring(0, port);
/*     */     } 
/*     */     
/*     */     try {
/* 157 */       InetAddress.getByName(route);
/* 158 */     } catch (Exception e) {
/* 159 */       return false;
/*     */     } 
/*     */     
/* 162 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jabber/JabberHTTPBind/JHBServlet.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */