/*    */ package org.jabber.JabberHTTPBind;
/*    */ 
/*    */ import java.io.BufferedReader;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStreamReader;
/*    */ import java.net.SocketException;
/*    */ import java.net.SocketTimeoutException;
/*    */ import javax.net.ssl.SSLSocket;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SSLSocketReader
/*    */   extends BufferedReader
/*    */ {
/*    */   private SSLSocket sock;
/*    */   
/*    */   public SSLSocketReader(SSLSocket paramSSLSocket) throws IOException {
/* 34 */     super(new InputStreamReader(paramSSLSocket.getInputStream(), "UTF-8"));
/* 35 */     this.sock = paramSSLSocket;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean ready() {
/*    */     try {
/* 47 */       int i = this.sock.getSoTimeout();
/* 48 */       this.sock.setSoTimeout(10);
/*    */       
/* 50 */       mark(1);
/*    */       
/*    */       try {
/* 53 */         read();
/* 54 */       } catch (SocketTimeoutException socketTimeoutException) {
/* 55 */         this.sock.setSoTimeout(i);
/* 56 */         return false;
/*    */       } 
/*    */       
/* 59 */       reset();
/* 60 */       this.sock.setSoTimeout(i);
/*    */       
/* 62 */       return true;
/*    */     }
/* 64 */     catch (SocketException socketException) {
/* 65 */       throw new RuntimeException("SSLSocketReader unable to set socket timeout: \n" + socketException);
/* 66 */     } catch (IOException iOException) {
/* 67 */       throw new RuntimeException("SSLSocketReader unable to access inputstream: \n" + iOException);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jabber/JabberHTTPBind/SSLSocketReader.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */