/*    */ package org.jabber.JabberHTTPBind;
/*    */ 
/*    */ import java.util.Enumeration;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Janitor
/*    */   implements Runnable
/*    */ {
/*    */   public static final int SLEEPMILLIS = 1000;
/*    */   private boolean keep_running = true;
/*    */   
/*    */   public void run() {
/* 34 */     while (this.keep_running) {
/* 35 */       for (Enumeration<Session> enumeration = Session.getSessions(); enumeration.hasMoreElements(); ) {
/* 36 */         Session session = enumeration.nextElement();
/*    */ 
/*    */         
/* 39 */         if (System.currentTimeMillis() - session.getLastActive() > 60000L)
/*    */         {
/*    */           
/* 42 */           session.terminate();
/*    */         }
/*    */       } 
/*    */       try {
/* 46 */         Thread.sleep(1000L);
/* 47 */       } catch (InterruptedException interruptedException) {
/* 48 */         interruptedException.printStackTrace();
/*    */       } 
/*    */     } 
/*    */   }
/*    */   
/*    */   public void stop() {
/* 54 */     this.keep_running = false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jabber/JabberHTTPBind/Janitor.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */