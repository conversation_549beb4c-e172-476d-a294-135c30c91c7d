/*     */ package org.jabber.JabberHTTPBind;
/*     */ 
/*     */ import java.util.Hashtable;
/*     */ import javax.naming.directory.Attributes;
/*     */ import javax.naming.directory.DirContext;
/*     */ import javax.naming.directory.InitialDirContext;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DNSUtil
/*     */ {
/*     */   private static DirContext context;
/*     */   
/*     */   static {
/*     */     try {
/*  31 */       Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*  32 */       hashtable.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
/*  33 */       context = new InitialDirContext(hashtable);
/*     */     }
/*  35 */     catch (Exception exception) {
/*     */       
/*  37 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static HostAddress resolveXMPPServerDomain(String paramString, int paramInt) {
/*  60 */     if (context == null)
/*     */     {
/*  62 */       return new HostAddress(paramString, paramInt);
/*     */     }
/*  64 */     String str = paramString;
/*  65 */     int i = paramInt;
/*     */ 
/*     */     
/*     */     try {
/*  69 */       Attributes attributes = context.getAttributes("_xmpp-client._tcp." + paramString);
/*  70 */       String str1 = (String)attributes.get("SRV").get();
/*  71 */       String[] arrayOfString = str1.split(" ");
/*  72 */       i = Integer.parseInt(arrayOfString[arrayOfString.length - 2]);
/*  73 */       str = arrayOfString[arrayOfString.length - 1];
/*     */     }
/*  75 */     catch (Exception exception) {
/*     */ 
/*     */       
/*     */       try {
/*     */         
/*  80 */         Attributes attributes = context.getAttributes("_jabber-client._tcp." + paramString);
/*  81 */         String str1 = (String)attributes.get("SRV").get();
/*  82 */         String[] arrayOfString = str1.split(" ");
/*  83 */         i = Integer.parseInt(arrayOfString[arrayOfString.length - 2]);
/*  84 */         str = arrayOfString[arrayOfString.length - 1];
/*     */       }
/*  86 */       catch (Exception exception1) {}
/*     */     } 
/*     */ 
/*     */     
/*  90 */     if (str.endsWith("."))
/*     */     {
/*  92 */       str = str.substring(0, str.length() - 1);
/*     */     }
/*  94 */     return new HostAddress(str, i);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static class HostAddress
/*     */   {
/*     */     private String host;
/*     */ 
/*     */     
/*     */     private int port;
/*     */ 
/*     */     
/*     */     private HostAddress(String param1String, int param1Int) {
/* 108 */       this.host = param1String;
/* 109 */       this.port = param1Int;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public String getHost() {
/* 119 */       return this.host;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public int getPort() {
/* 129 */       return this.port;
/*     */     }
/*     */ 
/*     */     
/*     */     public String toString() {
/* 134 */       return this.host + ":" + this.port;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jabber/JabberHTTPBind/DNSUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */