/*     */ package org.jabber.JabberHTTPBind;
/*     */ 
/*     */ import java.io.StringWriter;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.xml.transform.Transformer;
/*     */ import javax.xml.transform.TransformerFactory;
/*     */ import javax.xml.transform.dom.DOMSource;
/*     */ import javax.xml.transform.stream.StreamResult;
/*     */ import org.w3c.dom.Document;
/*     */ import org.w3c.dom.Element;
/*     */ import org.w3c.dom.Node;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Response
/*     */ {
/*  37 */   private static TransformerFactory tff = TransformerFactory.newInstance();
/*     */   
/*     */   public static final String STATUS_LEAVING = "leaving";
/*     */   
/*     */   public static final String STATUS_PENDING = "pending";
/*     */   public static final String STATUS_DONE = "done";
/*     */   private long cDate;
/*     */   private Document doc;
/*     */   private Element body;
/*     */   private long rid;
/*  47 */   private String contentType = "text/xml; charset=utf-8";
/*     */ 
/*     */ 
/*     */   
/*     */   private String status;
/*     */ 
/*     */ 
/*     */   
/*     */   private HttpServletRequest req;
/*     */ 
/*     */   
/*     */   private boolean aborted;
/*     */ 
/*     */ 
/*     */   
/*     */   public Response(Document paramDocument) {
/*  63 */     this.doc = paramDocument;
/*     */     
/*  65 */     this.body = this.doc.createElement("body");
/*  66 */     this.doc.appendChild(this.body);
/*     */     
/*  68 */     this.body.setAttribute("xmlns", "http://jabber.org/protocol/httpbind");
/*     */     
/*  70 */     this.cDate = System.currentTimeMillis();
/*     */     
/*  72 */     setStatus("pending");
/*     */   }
/*     */   
/*     */   public Response(Document paramDocument, HttpServletRequest paramHttpServletRequest) {
/*  76 */     this(paramDocument);
/*  77 */     this.req = paramHttpServletRequest;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Response setAttribute(String paramString1, String paramString2) {
/*  88 */     this.body.setAttribute(paramString1, paramString2);
/*  89 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Response setContentType(String paramString) {
/*  99 */     this.contentType = paramString;
/* 100 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Response addNode(Node paramNode, String paramString) {
/*     */     try {
/* 114 */       if (!((Element)paramNode).getAttribute("xmlns").equals(paramString))
/* 115 */         ((Element)paramNode).setAttribute("xmlns", paramString); 
/* 116 */     } catch (ClassCastException classCastException) {}
/* 117 */     this.body.appendChild(this.doc.importNode(paramNode, true));
/* 118 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void send(HttpServletResponse paramHttpServletResponse) {
/* 125 */     StringWriter stringWriter = new StringWriter();
/* 126 */     StreamResult streamResult = new StreamResult(stringWriter);
/*     */     try {
/* 128 */       Transformer transformer = tff.newTransformer();
/* 129 */       transformer.setOutputProperty("omit-xml-declaration", "yes");
/* 130 */       transformer.transform(new DOMSource(this.doc.getDocumentElement()), streamResult);
/* 131 */       paramHttpServletResponse.setContentType(this.contentType);
/* 132 */       JHBServlet.dbg("sending response [" + getRID() + "]: " + streamResult.getWriter().toString(), 2);
/* 133 */       paramHttpServletResponse.getWriter().println(streamResult.getWriter().toString());
/* 134 */       JHBServlet.dbg("sent response for " + getRID(), 3);
/* 135 */     } catch (Exception exception) {
/* 136 */       JHBServlet.dbg("XML.toString(Document): " + exception, 1);
/*     */     } 
/* 138 */     setStatus("done");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized String getStatus() {
/* 144 */     return this.status;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setStatus(String paramString) {
/* 150 */     JHBServlet.dbg("response status " + paramString + " for " + getRID(), 3);
/* 151 */     this.status = paramString;
/*     */   }
/*     */   public long getRID() {
/* 154 */     return this.rid;
/*     */   }
/*     */   public Response setRID(long paramLong) {
/* 157 */     this.rid = paramLong;
/* 158 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized long getCDate() {
/* 164 */     return this.cDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized HttpServletRequest getReq() {
/* 171 */     return this.req;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setReq(HttpServletRequest paramHttpServletRequest) {
/* 178 */     this.req = paramHttpServletRequest;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized boolean isAborted() {
/* 185 */     return this.aborted;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setAborted(boolean paramBoolean) {
/* 192 */     this.aborted = paramBoolean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jabber/JabberHTTPBind/Response.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */