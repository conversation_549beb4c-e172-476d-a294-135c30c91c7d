/*     */ package org.jabber.JabberHTTPBind;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.io.StringReader;
/*     */ import java.io.StringWriter;
/*     */ import java.net.InetSocketAddress;
/*     */ import java.net.Socket;
/*     */ import java.net.UnknownHostException;
/*     */ import java.util.Enumeration;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Iterator;
/*     */ import java.util.NoSuchElementException;
/*     */ import java.util.Random;
/*     */ import java.util.TreeMap;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.net.ssl.HandshakeCompletedEvent;
/*     */ import javax.net.ssl.HandshakeCompletedListener;
/*     */ import javax.net.ssl.SSLSocket;
/*     */ import javax.net.ssl.SSLSocketFactory;
/*     */ import javax.xml.parsers.DocumentBuilder;
/*     */ import javax.xml.parsers.DocumentBuilderFactory;
/*     */ import javax.xml.transform.Transformer;
/*     */ import javax.xml.transform.TransformerFactory;
/*     */ import javax.xml.transform.dom.DOMSource;
/*     */ import javax.xml.transform.stream.StreamResult;
/*     */ import org.w3c.dom.Document;
/*     */ import org.w3c.dom.NodeList;
/*     */ import org.xml.sax.InputSource;
/*     */ import org.xml.sax.SAXException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Session
/*     */ {
/*     */   public static final String DEFAULT_CONTENT = "text/xml; charset=utf-8";
/*     */   public static final int MAX_INACTIVITY = 60;
/*     */   public static final int MAX_REQUESTS = 2;
/*     */   public static final int MAX_WAIT = 300;
/*     */   public static final int MIN_POLLING = 2;
/*     */   private static final int READ_TIMEOUT = 1;
/*     */   private static final int SOCKET_TIMEOUT = 6000;
/*     */   public static final int DEFAULT_XMPPPORT = 5222;
/*     */   protected static final String SESS_START = "starting";
/*     */   protected static final String SESS_ACTIVE = "active";
/*     */   protected static final String SESS_TERM = "term";
/* 113 */   private static Hashtable sessions = new Hashtable<Object, Object>();
/*     */   
/* 115 */   private static TransformerFactory tff = TransformerFactory.newInstance(); private String authid;
/*     */   
/*     */   private static String createSessionID(int paramInt) {
/* 118 */     String str1 = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_";
/*     */     
/* 120 */     Random random = new Random();
/*     */     
/* 122 */     String str2 = new String();
/* 123 */     for (byte b = 0; b < paramInt; b++)
/* 124 */       str2 = str2 + str1.charAt(random.nextInt(str1.length())); 
/* 125 */     return str2;
/*     */   }
/*     */   
/*     */   public static Session getSession(String paramString) {
/* 129 */     return (Session)sessions.get(paramString);
/*     */   }
/*     */   
/*     */   public static Enumeration getSessions() {
/* 133 */     return sessions.elements();
/*     */   }
/*     */   
/*     */   public static int getNumSessions() {
/* 137 */     return sessions.size();
/*     */   }
/*     */   
/*     */   public static void stopSessions() {
/* 141 */     for (Enumeration<Session> enumeration = sessions.elements(); enumeration.hasMoreElements();) {
/* 142 */       ((Session)enumeration.nextElement()).terminate();
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   boolean authidSent = false;
/*     */ 
/*     */   
/*     */   boolean streamFeatures = false;
/*     */ 
/*     */   
/* 155 */   private String content = "text/xml; charset=utf-8";
/*     */   
/*     */   private DocumentBuilder db;
/*     */   
/* 159 */   private int hold = 1;
/*     */   
/* 161 */   private String inQueue = "";
/*     */   
/*     */   private BufferedReader br;
/*     */   
/*     */   private String key;
/*     */   
/*     */   private long lastActive;
/*     */   
/* 169 */   private long lastPoll = 0L;
/*     */ 
/*     */   
/*     */   private OutputStreamWriter osw;
/*     */ 
/*     */   
/*     */   private TreeMap responses;
/*     */   
/* 177 */   private String status = "starting";
/*     */   
/*     */   private String sid;
/*     */   
/*     */   public Socket sock;
/*     */   
/*     */   private String to;
/*     */   
/* 185 */   private DNSUtil.HostAddress host = null;
/*     */   
/* 187 */   private int wait = 300;
/*     */   
/* 189 */   private String xmllang = "en";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean reinit = false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean secure = false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean pauseForHandshake = false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Pattern streamPattern;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Pattern stream10Test;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Pattern stream10Pattern;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int init_retry;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private long lastDoneRID;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized Response addResponse(Response paramResponse) {
/* 357 */     while (this.responses.size() > 0 && this.responses.size() >= 2)
/* 358 */       this.responses.remove(this.responses.firstKey()); 
/* 359 */     return this.responses.put(new Long(paramResponse.getRID()), paramResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Session(String paramString1, String paramString2) throws UnknownHostException, IOException {
/* 368 */     this.init_retry = 0; this.to = paramString1; int i = 5222; this.sock = new Socket(); setLastActive(); try { this.db = DocumentBuilderFactory.newInstance().newDocumentBuilder(); } catch (Exception exception) {} if (paramString2 != null && !paramString2.equals("")) { JHBServlet.dbg("Trying to use 'route' attribute to open a socket...", 3); if (paramString2.startsWith("xmpp:"))
/*     */         paramString2 = paramString2.substring("xmpp:".length());  int j; if ((j = paramString2.lastIndexOf(":")) != -1) { try { int k = Integer.parseInt(paramString2.substring(j + 1)); if (k >= 0 && k <= 65535) { i = k; JHBServlet.dbg("...route attribute holds a valid port (" + i + ").", 3); }  } catch (NumberFormatException numberFormatException) {} paramString2 = paramString2.substring(0, j); }  JHBServlet.dbg("Trying to open a socket to '" + paramString2 + "', using port " + i + ".", 3); try { this.sock.connect(new InetSocketAddress(paramString2, i), 6000); } catch (Exception exception) { JHBServlet.dbg("Failed to open a socket using the 'route' attribute", 3); }  }  if (this.sock == null || !this.sock.isConnected()) { this.sock = new Socket(); JHBServlet.dbg("Trying to use 'to' attribute to open a socket...", 3); this.host = DNSUtil.resolveXMPPServerDomain(paramString1, 5222); try { JHBServlet.dbg("Trying to open a socket to '" + this.host.getHost() + "', using port " + this.host.getPort() + ".", 3); this.sock.connect(new InetSocketAddress(this.host.getHost(), this.host.getPort()), 6000); } catch (UnknownHostException unknownHostException) { JHBServlet.dbg("Failed to open a socket using the 'to' attribute: " + unknownHostException.toString(), 3); throw unknownHostException; } catch (IOException iOException) { JHBServlet.dbg("Failed to open a socket using the 'to' attribute: " + iOException.toString(), 3); throw iOException; }  }  try { if (this.sock.isConnected())
/*     */         JHBServlet.dbg("Succesfully connected to " + paramString1, 2);  this.sock.setSoTimeout(6000); this.osw = new OutputStreamWriter(this.sock.getOutputStream(), "UTF-8"); this.osw.write("<stream:stream to='" + this.to + "'" + " xmlns='jabber:client' " + " xmlns:stream='http://etherx.jabber.org/streams'" + " version='1.0'" + ">"); this.osw.flush(); while (sessions.get(this.sid = createSessionID(24)) != null); JHBServlet.dbg("creating session with id " + this.sid, 2); sessions.put(this.sid, this); this.responses = new TreeMap<Object, Object>(); this.br = new BufferedReader(new InputStreamReader(this.sock.getInputStream(), "UTF-8")); this.streamPattern = Pattern.compile(".*<stream:stream[^>]*id=['|\"]([^'|^\"]+)['|\"][^>]*>.*", 32); this.stream10Pattern = Pattern.compile(".*<stream:stream[^>]*id=['|\"]([^'|^\"]+)['|\"][^>]*>.*(<stream.*)$", 32); this.stream10Test = Pattern.compile(".*<stream:stream[^>]*version=['|\"]1.0['|\"][^>]*>.*", 32); setStatus("active"); }
/*     */     catch (IOException iOException) { throw iOException; }
/* 372 */      } public NodeList checkInQ(long paramLong) throws IOException { NodeList nodeList = null;
/*     */     
/* 374 */     this.inQueue += readFromSocket(paramLong);
/*     */     
/* 376 */     JHBServlet.dbg("inQueue: " + this.inQueue, 2);
/*     */     
/* 378 */     if (this.init_retry < 1000 && (this.authid == null || isReinit()) && this.inQueue.length() > 0) {
/*     */       
/* 380 */       this.init_retry++;
/* 381 */       if (this.stream10Test.matcher(this.inQueue).matches()) {
/* 382 */         Matcher matcher = this.stream10Pattern.matcher(this.inQueue);
/* 383 */         if (matcher.matches()) {
/* 384 */           this.authid = matcher.group(1);
/* 385 */           this.inQueue = matcher.group(2);
/* 386 */           JHBServlet.dbg("inQueue: " + this.inQueue, 2);
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 391 */           this.streamFeatures = (this.inQueue.length() > 0);
/*     */         } else {
/* 393 */           JHBServlet.dbg("failed to get stream features", 2);
/*     */           try {
/* 395 */             Thread.sleep(5L);
/* 396 */           } catch (InterruptedException interruptedException) {}
/*     */           
/* 398 */           return checkInQ(paramLong);
/*     */         } 
/*     */       } else {
/*     */         
/* 402 */         Matcher matcher = this.streamPattern.matcher(this.inQueue);
/* 403 */         if (matcher.matches()) {
/* 404 */           this.authid = matcher.group(1);
/*     */         } else {
/* 406 */           JHBServlet.dbg("failed to get authid", 2);
/*     */           try {
/* 408 */             Thread.sleep(5L);
/* 409 */           } catch (InterruptedException interruptedException) {}
/*     */           
/* 411 */           return checkInQ(paramLong);
/*     */         } 
/*     */       } 
/* 414 */       this.init_retry = 0;
/*     */     } 
/*     */ 
/*     */     
/* 418 */     if (!this.inQueue.equals("")) {
/*     */       
/*     */       try {
/*     */ 
/*     */ 
/*     */         
/* 424 */         Document document = null;
/* 425 */         if (this.streamFeatures) {
/* 426 */           document = this.db.parse(new InputSource(new StringReader("<doc>" + this.inQueue + "</doc>")));
/*     */         } else {
/*     */           
/*     */           try {
/* 430 */             document = this.db.parse(new InputSource(new StringReader("<doc xmlns='jabber:client'>" + this.inQueue + "</doc>")));
/*     */           
/*     */           }
/* 433 */           catch (SAXException sAXException) {
/*     */             
/*     */             try {
/* 436 */               document = this.db.parse(new InputSource(new StringReader("<stream:stream>" + this.inQueue)));
/*     */               
/* 438 */               terminate();
/* 439 */             } catch (SAXException sAXException1) {}
/*     */           } 
/*     */         } 
/* 442 */         if (document != null)
/* 443 */           nodeList = document.getFirstChild().getChildNodes(); 
/* 444 */         if (this.streamFeatures) {
/* 445 */           for (byte b = 0; b < nodeList.item(0).getChildNodes().getLength(); b++) {
/* 446 */             if (nodeList.item(0).getChildNodes().item(b).getNodeName().equals("starttls"))
/*     */             {
/* 448 */               if (!isReinit()) {
/* 449 */                 JHBServlet.dbg("starttls present, trying to use it", 2);
/*     */ 
/*     */ 
/*     */                 
/* 453 */                 this.osw.write("<starttls xmlns='urn:ietf:params:xml:ns:xmpp-tls'/>");
/*     */                 
/* 455 */                 this.osw.flush();
/* 456 */                 String str = readFromSocket(paramLong);
/* 457 */                 JHBServlet.dbg(str, 2);
/*     */                 
/*     */                 try {
/* 460 */                   SSLSocketFactory sSLSocketFactory = (SSLSocketFactory)SSLSocketFactory.getDefault();
/*     */ 
/*     */                   
/* 463 */                   SSLSocket sSLSocket = (SSLSocket)sSLSocketFactory.createSocket(this.sock, this.sock.getInetAddress().getHostName(), this.sock.getPort(), false);
/*     */ 
/*     */ 
/*     */ 
/*     */                   
/* 468 */                   sSLSocket.addHandshakeCompletedListener(new HandShakeFinished(this));
/*     */ 
/*     */                   
/* 471 */                   this.pauseForHandshake = true;
/* 472 */                   JHBServlet.dbg("initiating handshake");
/* 473 */                   sSLSocket.startHandshake();
/*     */                   try {
/* 475 */                     while (this.pauseForHandshake) {
/* 476 */                       JHBServlet.dbg(".");
/* 477 */                       Thread.sleep(5L);
/*     */                     } 
/* 479 */                   } catch (InterruptedException interruptedException) {}
/*     */ 
/*     */                   
/* 482 */                   JHBServlet.dbg("TLS Handshake complete", 2);
/*     */                   
/* 484 */                   this.sock = sSLSocket;
/* 485 */                   this.sock.setSoTimeout(6000);
/*     */                   
/* 487 */                   this.br = new SSLSocketReader(sSLSocket);
/*     */ 
/*     */                   
/* 490 */                   this.osw = new OutputStreamWriter(sSLSocket.getOutputStream(), "UTF-8");
/*     */ 
/*     */                   
/* 493 */                   this.inQueue = "";
/* 494 */                   setReinit(true);
/* 495 */                   this.osw.write("<stream:stream to='" + this.to + "'" + " xmlns='jabber:client' " + " xmlns:stream='http://etherx.jabber.org/streams'" + " version='1.0'" + ">");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                   
/* 502 */                   this.osw.flush();
/*     */                   
/* 504 */                   return checkInQ(paramLong);
/* 505 */                 } catch (Exception exception) {
/* 506 */                   JHBServlet.dbg("STARTTLS failed: " + exception.toString(), 1);
/*     */                   
/* 508 */                   setReinit(false);
/* 509 */                   if (isSecure()) {
/* 510 */                     if (!this.sock.getInetAddress().getHostName().equals("localhost") && !getResponse(paramLong).getReq().getServerName().equals(this.sock.getInetAddress().getHostName())) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                       
/* 521 */                       JHBServlet.dbg("secure connection requested but failed", 2);
/*     */ 
/*     */ 
/*     */                       
/* 525 */                       throw new IOException();
/*     */                     } 
/*     */ 
/*     */                     
/* 529 */                     JHBServlet.dbg("secure requested and we're local", 1);
/*     */ 
/*     */                   
/*     */                   }
/*     */                   else {
/*     */ 
/*     */                     
/* 536 */                     JHBServlet.dbg("tls failed but we don't need to be secure", 2);
/*     */                   } 
/*     */ 
/*     */ 
/*     */                   
/* 541 */                   if (this.sock.isClosed()) {
/* 542 */                     JHBServlet.dbg("socket closed", 1);
/*     */                     
/* 544 */                     Socket socket = new Socket();
/* 545 */                     socket.connect(this.sock.getRemoteSocketAddress(), 6000);
/*     */ 
/*     */                     
/* 548 */                     this.sock = socket;
/* 549 */                     this.sock.setSoTimeout(6000);
/* 550 */                     this.br = new BufferedReader(new InputStreamReader(this.sock.getInputStream(), "UTF-8"));
/*     */ 
/*     */ 
/*     */                     
/* 554 */                     this.osw = new OutputStreamWriter(this.sock.getOutputStream(), "UTF-8");
/*     */ 
/*     */ 
/*     */                     
/* 558 */                     this.inQueue = "";
/* 559 */                     setReinit(true);
/*     */                     
/* 561 */                     this.osw.write("<stream:stream to='" + this.to + "'" + " xmlns='jabber:client' " + " xmlns:stream='http://etherx.jabber.org/streams'" + " version='1.0'" + ">");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                     
/* 569 */                     this.osw.flush();
/*     */                     
/* 571 */                     return checkInQ(paramLong);
/*     */                   } 
/*     */                 } 
/*     */               } else {
/* 575 */                 nodeList.item(0).removeChild(nodeList.item(0).getChildNodes().item(b));
/*     */               } 
/*     */             }
/*     */           } 
/*     */         }
/* 580 */         if (document != null)
/* 581 */           this.inQueue = ""; 
/* 582 */       } catch (SAXException sAXException) {
/* 583 */         setReinit(false);
/* 584 */         JHBServlet.dbg("failed to parse inQueue: " + this.inQueue + "\n" + sAXException.toString(), 1);
/*     */         
/* 586 */         return null;
/*     */       } 
/*     */     }
/* 589 */     setReinit(false);
/* 590 */     setLastActive();
/* 591 */     return nodeList; }
/*     */ 
/*     */   
/*     */   private class HandShakeFinished
/*     */     implements HandshakeCompletedListener {
/*     */     private Session sess;
/*     */     
/*     */     public HandShakeFinished(Session param1Session1) {
/* 599 */       this.sess = param1Session1;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void handshakeCompleted(HandshakeCompletedEvent param1HandshakeCompletedEvent) {
/* 605 */       JHBServlet.dbg("startTLS: Handshake is complete", 2);
/*     */       
/* 607 */       this.sess.pauseForHandshake = false;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized boolean checkValidRID(long paramLong) {
/*     */     try {
/* 621 */       if (paramLong <= ((Long)this.responses.lastKey()).longValue() + 2L && paramLong >= ((Long)this.responses.firstKey()).longValue())
/*     */       {
/*     */         
/* 624 */         return true;
/*     */       }
/* 626 */       JHBServlet.dbg("invalid request id: " + paramLong + " (last: " + ((Long)this.responses.lastKey()).longValue() + ")", 1);
/*     */ 
/*     */       
/* 629 */       return false;
/*     */     }
/* 631 */     catch (NoSuchElementException noSuchElementException) {
/* 632 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String getAuthid() {
/* 637 */     return this.authid;
/*     */   }
/*     */   
/*     */   public String getContent() {
/* 641 */     return this.content;
/*     */   }
/*     */   
/*     */   public int getHold() {
/* 645 */     return this.hold;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized String getKey() {
/* 652 */     return this.key;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized long getLastActive() {
/* 659 */     return this.lastActive;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized long getLastPoll() {
/* 666 */     return this.lastPoll;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized Response getResponse(long paramLong) {
/* 677 */     return (Response)this.responses.get(new Long(paramLong));
/*     */   }
/*     */   
/*     */   public String getSID() {
/* 681 */     return this.sid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTo() {
/* 689 */     return this.to;
/*     */   }
/*     */   
/*     */   public int getWait() {
/* 693 */     return this.wait;
/*     */   }
/*     */   
/*     */   public String getXMLLang() {
/* 697 */     return this.xmllang;
/*     */   }
/*     */   
/*     */   public synchronized int numPendingRequests() {
/* 701 */     byte b = 0;
/* 702 */     Iterator<Response> iterator = this.responses.values().iterator();
/* 703 */     while (iterator.hasNext()) {
/* 704 */       Response response = iterator.next();
/* 705 */       if (!response.getStatus().equals("done"))
/* 706 */         b++; 
/*     */     } 
/* 708 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized long getLastDoneRID() {
/* 714 */     return this.lastDoneRID;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String readFromSocket(long paramLong) throws IOException {
/* 723 */     String str = "";
/* 724 */     char[] arrayOfChar = new char[16];
/* 725 */     int i = 0;
/*     */     
/* 727 */     Response response = getResponse(paramLong);
/*     */     
/* 729 */     while (!this.sock.isClosed() && !isStatus("term")) {
/* 730 */       setLastActive();
/*     */       try {
/* 732 */         if (this.br.ready()) {
/*     */           
/* 734 */           while (this.br.ready() && (i = this.br.read(arrayOfChar, 0, arrayOfChar.length)) >= 0)
/* 735 */             str = str + new String(arrayOfChar, 0, i); 
/*     */           break;
/*     */         } 
/* 738 */         if ((this.hold == 0 && response != null && System.currentTimeMillis() - response.getCDate() > 200L) || (this.hold > 0 && ((response != null && System.currentTimeMillis() - response.getCDate() >= (getWait() * 1000)) || numPendingRequests() > getHold() || !str.equals(""))) || response.isAborted()) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 753 */           JHBServlet.dbg("readFromSocket done for " + paramLong, 3);
/*     */           
/*     */           break;
/*     */         } 
/*     */         try {
/* 758 */           Thread.sleep(1L);
/*     */         }
/* 760 */         catch (InterruptedException interruptedException) {
/* 761 */           System.err.println(interruptedException.toString());
/*     */         }
/*     */       
/* 764 */       } catch (IOException iOException) {
/* 765 */         System.err.println("Can't read from socket");
/* 766 */         terminate();
/*     */       } 
/*     */     } 
/*     */     
/* 770 */     if (this.sock.isClosed()) {
/* 771 */       throw new IOException();
/*     */     }
/* 773 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Session sendNodes(NodeList paramNodeList) {
/* 787 */     String str = "";
/* 788 */     StreamResult streamResult = new StreamResult();
/*     */     
/*     */     try {
/* 791 */       Transformer transformer = tff.newTransformer();
/* 792 */       transformer.setOutputProperty("omit-xml-declaration", "yes");
/*     */       
/* 794 */       for (byte b = 0; b < paramNodeList.getLength(); b++) {
/* 795 */         streamResult.setWriter(new StringWriter());
/* 796 */         transformer.transform(new DOMSource(paramNodeList.item(b)), streamResult);
/* 797 */         String str1 = streamResult.getWriter().toString();
/* 798 */         str = str + str1;
/*     */       } 
/* 800 */     } catch (Exception exception) {
/* 801 */       JHBServlet.dbg("XML.toString(Document): " + exception, 1);
/*     */     } 
/*     */     
/*     */     try {
/* 805 */       if (isReinit()) {
/* 806 */         JHBServlet.dbg("Reinitializing Stream!", 2);
/* 807 */         this.osw.write("<stream:stream to='" + this.to + "'" + " xmlns='jabber:client' " + " xmlns:stream='http://etherx.jabber.org/streams'" + " version='1.0'" + ">");
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 812 */       this.osw.write(str);
/* 813 */       this.osw.flush();
/* 814 */     } catch (IOException iOException) {
/* 815 */       JHBServlet.dbg(this.sid + " failed to write to stream", 1);
/*     */     } 
/*     */     
/* 818 */     return this;
/*     */   }
/*     */   
/*     */   public Session setContent(String paramString) {
/* 822 */     this.content = paramString;
/* 823 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Session setHold(int paramInt) {
/* 831 */     if (paramInt < 2 && paramInt >= 0)
/* 832 */       this.hold = paramInt; 
/* 833 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setKey(String paramString) {
/* 841 */     this.key = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setLastActive() {
/* 848 */     this.lastActive = System.currentTimeMillis();
/*     */   }
/*     */   
/*     */   public synchronized void setLastDoneRID(long paramLong) {
/* 852 */     this.lastDoneRID = paramLong;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setLastPoll() {
/* 859 */     this.lastPoll = System.currentTimeMillis();
/*     */   }
/*     */   
/*     */   public int setWait(int paramInt) {
/* 863 */     if (paramInt < 0)
/* 864 */       paramInt = 0; 
/* 865 */     if (paramInt > 300)
/* 866 */       paramInt = 300; 
/* 867 */     this.wait = paramInt;
/* 868 */     return paramInt;
/*     */   }
/*     */   
/*     */   public Session setXMLLang(String paramString) {
/* 872 */     this.xmllang = paramString;
/* 873 */     return this;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized boolean isReinit() {
/* 880 */     return this.reinit;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized boolean isSecure() {
/* 887 */     return this.secure;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setReinit(boolean paramBoolean) {
/* 895 */     this.reinit = paramBoolean;
/*     */   }
/*     */   
/*     */   public synchronized void setStatus(String paramString) {
/* 899 */     this.status = paramString;
/*     */   }
/*     */   
/*     */   public synchronized boolean isStatus(String paramString) {
/* 903 */     return (this.status == paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void terminate() {
/* 911 */     JHBServlet.dbg("terminating session " + getSID(), 2);
/* 912 */     setStatus("term");
/* 913 */     synchronized (this.sock) {
/* 914 */       if (!this.sock.isClosed()) {
/*     */         try {
/* 916 */           this.osw.write("</stream:stream>");
/* 917 */           this.osw.flush();
/* 918 */           this.sock.close();
/* 919 */         } catch (IOException iOException) {}
/*     */       }
/*     */       
/* 922 */       this.sock.notifyAll();
/*     */     } 
/* 924 */     sessions.remove(this.sid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void setSecure(boolean paramBoolean) {
/* 932 */     this.secure = paramBoolean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/jabber/JabberHTTPBind/Session.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */