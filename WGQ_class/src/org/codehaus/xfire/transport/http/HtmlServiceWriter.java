/*     */ package org.codehaus.xfire.transport.http;
/*     */ 
/*     */ import java.io.OutputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.xml.stream.XMLStreamException;
/*     */ import javax.xml.stream.XMLStreamWriter;
/*     */ import org.codehaus.xfire.service.Service;
/*     */ import org.codehaus.xfire.util.STAXUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HtmlServiceWriter
/*     */ {
/*     */   private HttpServletRequest request;
/*     */   private static final String XHTML_STRICT_DTD = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">";
/*     */   
/*     */   public HtmlServiceWriter() {}
/*     */   
/*     */   public HtmlServiceWriter(HttpServletRequest paramHttpServletRequest) {
/*  37 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void write(OutputStream paramOutputStream, Collection<?> paramCollection) throws XMLStreamException {
/*  50 */     XMLStreamWriter xMLStreamWriter = STAXUtils.createXMLStreamWriter(paramOutputStream, null, null);
/*  51 */     xMLStreamWriter.writeStartDocument();
/*  52 */     writePreamble(xMLStreamWriter, "XFire Services");
/*     */     
/*  54 */     xMLStreamWriter.writeStartElement("body");
/*  55 */     xMLStreamWriter.writeStartElement("p");
/*     */     
/*  57 */     xMLStreamWriter.writeEndElement();
/*  58 */     if (!paramCollection.isEmpty()) {
/*     */       
/*  60 */       xMLStreamWriter.writeStartElement("p");
/*  61 */       xMLStreamWriter.writeCharacters("Available Services:");
/*  62 */       xMLStreamWriter.writeEndElement();
/*  63 */       xMLStreamWriter.writeStartElement("ul");
/*  64 */       int i = this.request.getServerPort();
/*  65 */       StringBuffer stringBuffer = new StringBuffer();
/*     */       
/*  67 */       stringBuffer.append(this.request.getScheme()).append("://").append(this.request.getServerName());
/*  68 */       if (i != 80 && i != 443 && i != 0) {
/*  69 */         stringBuffer.append(':').append(i);
/*     */       }
/*  71 */       stringBuffer.append("".equals(this.request.getContextPath()) ? "/" : this.request.getContextPath());
/*     */ 
/*     */       
/*  74 */       if (stringBuffer.lastIndexOf("/") + 1 == stringBuffer.length()) {
/*  75 */         stringBuffer.deleteCharAt(stringBuffer.lastIndexOf("/"));
/*     */       }
/*  77 */       if (this.request.getServletPath().indexOf("/") > 0) {
/*  78 */         stringBuffer.append("/");
/*     */       }
/*     */ 
/*     */       
/*  82 */       stringBuffer.append(this.request.getServletPath());
/*  83 */       stringBuffer.append("/");
/*  84 */       String str = stringBuffer.toString();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  92 */       ArrayList<?> arrayList = new ArrayList();
/*  93 */       arrayList.addAll(paramCollection);
/*  94 */       Collections.sort(arrayList, new ServiceComperator());
/*  95 */       paramCollection = arrayList;
/*  96 */       for (Service service : paramCollection) {
/*     */ 
/*     */ 
/*     */         
/* 100 */         String str1 = str + service.getSimpleName() + "?wsdl";
/*     */         
/* 102 */         xMLStreamWriter.writeStartElement("li");
/* 103 */         xMLStreamWriter.writeCharacters(service.getSimpleName());
/* 104 */         Object object = service.getProperty("wsdl.generation.disabled");
/*     */         
/* 106 */         if (object == null || "false".equals(object.toString().toLowerCase())) {
/* 107 */           xMLStreamWriter.writeCharacters(" ");
/* 108 */           xMLStreamWriter.writeStartElement("a");
/* 109 */           xMLStreamWriter.writeAttribute("href", str1);
/* 110 */           xMLStreamWriter.writeCharacters("[wsdl]");
/* 111 */           xMLStreamWriter.writeEndElement();
/*     */         } 
/* 113 */         xMLStreamWriter.writeEndElement();
/*     */       } 
/*     */     } 
/* 116 */     xMLStreamWriter.writeComment("Just filling space according to http://support.microsoft.com/default.aspx?scid=kb;en-us;Q294807");
/* 117 */     xMLStreamWriter.writeComment("Just filling space according to http://support.microsoft.com/default.aspx?scid=kb;en-us;Q294807");
/* 118 */     xMLStreamWriter.writeEmptyElement("br");
/* 119 */     xMLStreamWriter.writeEmptyElement("br");
/* 120 */     xMLStreamWriter.writeEmptyElement("br");
/* 121 */     xMLStreamWriter.writeEmptyElement("br");
/* 122 */     xMLStreamWriter.writeEmptyElement("br");
/* 123 */     xMLStreamWriter.writeCharacters("       Generated by XFire ( http://xfire.codehaus.org ) ");
/* 124 */     xMLStreamWriter.writeEmptyElement("hr");
/* 125 */     xMLStreamWriter.writeEndDocument();
/* 126 */     xMLStreamWriter.flush();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void write(OutputStream paramOutputStream, Service paramService) throws XMLStreamException {
/* 141 */     XMLStreamWriter xMLStreamWriter = STAXUtils.createXMLStreamWriter(paramOutputStream, null, null);
/* 142 */     xMLStreamWriter.writeStartDocument();
/* 143 */     String str = paramService.getSimpleName() + " Web Service";
/* 144 */     writePreamble(xMLStreamWriter, str);
/*     */     
/* 146 */     xMLStreamWriter.writeStartElement("body");
/* 147 */     xMLStreamWriter.writeStartElement("h1");
/* 148 */     xMLStreamWriter.writeCharacters(str);
/* 149 */     xMLStreamWriter.writeEndElement();
/*     */     
/* 151 */     xMLStreamWriter.writeEndDocument();
/* 152 */     xMLStreamWriter.flush();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void writePreamble(XMLStreamWriter paramXMLStreamWriter, String paramString) throws XMLStreamException {
/* 158 */     paramXMLStreamWriter.writeDTD("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">");
/* 159 */     paramXMLStreamWriter.writeStartElement("html");
/* 160 */     paramXMLStreamWriter.writeStartElement("head");
/* 161 */     paramXMLStreamWriter.writeStartElement("title");
/* 162 */     paramXMLStreamWriter.writeCharacters(paramString);
/* 163 */     paramXMLStreamWriter.writeEndElement();
/* 164 */     paramXMLStreamWriter.writeEndElement();
/*     */   }
/*     */ 
/*     */   
/*     */   class ServiceComperator
/*     */     implements Comparator
/*     */   {
/*     */     public int compare(Object param1Object1, Object param1Object2) {
/* 172 */       return ((Service)param1Object1).getSimpleName().compareToIgnoreCase(((Service)param1Object2).getSimpleName());
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/codehaus/xfire/transport/http/HtmlServiceWriter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */