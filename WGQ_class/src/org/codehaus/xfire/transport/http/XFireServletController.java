/*     */ package org.codehaus.xfire.transport.http;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import javax.servlet.ServletContext;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.xml.stream.XMLInputFactory;
/*     */ import javax.xml.stream.XMLOutputFactory;
/*     */ import javax.xml.stream.XMLStreamException;
/*     */ import javax.xml.stream.XMLStreamReader;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import org.codehaus.xfire.MessageContext;
/*     */ import org.codehaus.xfire.XFire;
/*     */ import org.codehaus.xfire.XFireFactory;
/*     */ import org.codehaus.xfire.XFireRuntimeException;
/*     */ import org.codehaus.xfire.attachments.Attachments;
/*     */ import org.codehaus.xfire.attachments.StreamedAttachments;
/*     */ import org.codehaus.xfire.exchange.InMessage;
/*     */ import org.codehaus.xfire.handler.AbstractHandler;
/*     */ import org.codehaus.xfire.handler.Handler;
/*     */ import org.codehaus.xfire.service.Service;
/*     */ import org.codehaus.xfire.service.ServiceRegistry;
/*     */ import org.codehaus.xfire.transport.Channel;
/*     */ import org.codehaus.xfire.transport.Session;
/*     */ import org.codehaus.xfire.transport.Transport;
/*     */ import org.codehaus.xfire.transport.TransportManager;
/*     */ import org.codehaus.xfire.util.STAXUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class XFireServletController
/*     */ {
/*     */   public static final String HTTP_SERVLET_REQUEST = "XFireServletController.httpServletRequest";
/*     */   public static final String HTTP_SERVLET_RESPONSE = "XFireServletController.httpServletResponse";
/*     */   public static final String HTTP_SERVLET_CONTEXT = "XFireServletController.httpServletContext";
/*  47 */   private static ThreadLocal requests = new ThreadLocal();
/*  48 */   private static ThreadLocal responses = new ThreadLocal();
/*  49 */   private static final Log logger = LogFactory.getLog(XFireServletController.class);
/*     */   
/*     */   protected XFire xfire;
/*     */   
/*     */   protected SoapHttpTransport transport;
/*     */   
/*     */   protected ServletContext servletContext;
/*     */   
/*     */   public XFireServletController(XFire paramXFire) {
/*  58 */     this(paramXFire, null);
/*     */   }
/*     */ 
/*     */   
/*     */   public XFireServletController(XFire paramXFire, ServletContext paramServletContext) {
/*  63 */     this.xfire = paramXFire;
/*  64 */     this.servletContext = paramServletContext;
/*     */ 
/*     */     
/*  67 */     this.transport = (SoapHttpTransport)new XFireServletTransport();
/*     */     
/*  69 */     this.transport.addFaultHandler((Handler)new FaultResponseCodeHandler());
/*     */     
/*  71 */     Transport transport = getTransportManager().getTransport("http://schemas.xmlsoap.org/soap/http");
/*     */     
/*  73 */     if (transport != null) getTransportManager().unregister(transport);
/*     */     
/*  75 */     getTransportManager().register((Transport)this.transport);
/*     */   }
/*     */ 
/*     */   
/*     */   public static HttpServletRequest getRequest() {
/*  80 */     return requests.get();
/*     */   }
/*     */ 
/*     */   
/*     */   public static HttpServletResponse getResponse() {
/*  85 */     return responses.get();
/*     */   }
/*     */ 
/*     */   
/*     */   protected TransportManager getTransportManager() {
/*  90 */     return getXFire().getTransportManager();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 101 */     String str = getService(paramHttpServletRequest);
/* 102 */     if (str == null) str = "";
/*     */     
/* 104 */     ServiceRegistry serviceRegistry = getServiceRegistry();
/*     */     
/* 106 */     paramHttpServletResponse.setHeader("Content-Type", "UTF-8");
/*     */ 
/*     */     
/*     */     try {
/* 110 */       requests.set(paramHttpServletRequest);
/* 111 */       responses.set(paramHttpServletResponse);
/*     */       
/* 113 */       boolean bool = serviceRegistry.hasService(str);
/* 114 */       if (str.length() == 0 || !bool) {
/*     */         
/* 116 */         if (!bool)
/*     */         {
/* 118 */           paramHttpServletResponse.setStatus(404);
/*     */         }
/*     */         
/* 121 */         generateServices(paramHttpServletRequest, paramHttpServletResponse);
/*     */         
/*     */         return;
/*     */       } 
/* 125 */       if (isWSDLRequest(paramHttpServletRequest))
/*     */       {
/* 127 */         generateWSDL(paramHttpServletResponse, str);
/*     */       }
/*     */       else
/*     */       {
/* 131 */         invoke(paramHttpServletRequest, paramHttpServletResponse, str);
/*     */       }
/*     */     
/* 134 */     } catch (Exception exception) {
/*     */       
/* 136 */       logger.error("Couldn't invoke servlet request.", exception);
/*     */       
/* 138 */       if (exception instanceof ServletException)
/*     */       {
/* 140 */         throw (ServletException)exception;
/*     */       }
/*     */ 
/*     */       
/* 144 */       throw new ServletException(exception);
/*     */     
/*     */     }
/*     */     finally {
/*     */       
/* 149 */       requests.set(null);
/* 150 */       responses.set(null);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   protected boolean isWSDLRequest(HttpServletRequest paramHttpServletRequest) {
/* 156 */     return (paramHttpServletRequest.getQueryString() != null && paramHttpServletRequest.getQueryString().trim().equalsIgnoreCase("wsdl"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void generateService(HttpServletResponse paramHttpServletResponse, String paramString) throws ServletException, IOException {
/* 163 */     paramHttpServletResponse.setContentType("text/html");
/* 164 */     Service service = getServiceRegistry().getService(paramString);
/* 165 */     HtmlServiceWriter htmlServiceWriter = new HtmlServiceWriter();
/*     */     
/*     */     try {
/* 168 */       htmlServiceWriter.write((OutputStream)paramHttpServletResponse.getOutputStream(), service);
/*     */     }
/* 170 */     catch (XMLStreamException xMLStreamException) {
/*     */       
/* 172 */       throw new ServletException("Error writing HTML services list", xMLStreamException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void generateServices(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 183 */     paramHttpServletResponse.setContentType("text/html");
/*     */     
/* 185 */     HtmlServiceWriter htmlServiceWriter = new HtmlServiceWriter(paramHttpServletRequest);
/*     */ 
/*     */     
/*     */     try {
/* 189 */       Object object = XFireFactory.newInstance().getXFire().getProperty("services.list.disabled");
/* 190 */       if (object != null && "true".equals(object.toString().toLowerCase())) {
/* 191 */         paramHttpServletResponse.getOutputStream().write("Services list disabled".getBytes());
/*     */       } else {
/* 193 */         htmlServiceWriter.write((OutputStream)paramHttpServletResponse.getOutputStream(), getServiceRegistry().getServices());
/*     */       }
/*     */     
/* 196 */     } catch (XMLStreamException xMLStreamException) {
/*     */       
/* 198 */       throw new ServletException("Error writing HTML services list", xMLStreamException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected MessageContext createMessageContext(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, String paramString) {
/* 207 */     XFireHttpSession xFireHttpSession = new XFireHttpSession(paramHttpServletRequest);
/* 208 */     MessageContext messageContext = new MessageContext();
/* 209 */     messageContext.setXFire(getXFire());
/* 210 */     messageContext.setSession((Session)xFireHttpSession);
/* 211 */     messageContext.setService(getService(paramString));
/* 212 */     messageContext.setProperty("XFireServletController.httpServletRequest", paramHttpServletRequest);
/* 213 */     messageContext.setProperty("XFireServletController.httpServletResponse", paramHttpServletResponse);
/*     */     
/* 215 */     if (this.servletContext != null) {
/* 216 */       messageContext.setProperty("XFireServletController.httpServletContext", this.servletContext);
/*     */     }
/* 218 */     return messageContext;
/*     */   }
/*     */ 
/*     */   
/*     */   protected Channel createChannel(MessageContext paramMessageContext) throws ServletException {
/* 223 */     HttpServletRequest httpServletRequest = (HttpServletRequest)paramMessageContext.getProperty("XFireServletController.httpServletRequest");
/*     */     
/*     */     try {
/* 226 */       return this.transport.createChannel(httpServletRequest.getRequestURI());
/*     */     }
/* 228 */     catch (Exception exception) {
/*     */       
/* 230 */       logger.debug("Couldn't open channel.", exception);
/* 231 */       throw new ServletException("Couldn't open channel.", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void invoke(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, String paramString) throws ServletException, IOException, UnsupportedEncodingException {
/* 248 */     paramHttpServletResponse.setStatus(200);
/* 249 */     paramHttpServletResponse.setBufferSize(8192);
/*     */     
/* 251 */     MessageContext messageContext = createMessageContext(paramHttpServletRequest, paramHttpServletResponse, paramString);
/* 252 */     Channel channel = createChannel(messageContext);
/*     */ 
/*     */ 
/*     */     
/* 256 */     messageContext.setProperty("xfire.stax.input.factory", XMLInputFactory.newInstance());
/* 257 */     messageContext.setProperty("xfire.stax.output.factory", XMLOutputFactory.newInstance());
/*     */ 
/*     */     
/* 260 */     String str1 = getSoapAction(paramHttpServletRequest);
/* 261 */     String str2 = paramHttpServletRequest.getContentType();
/* 262 */     if (null == str2) {
/*     */       
/* 264 */       paramHttpServletResponse.setContentType("text/html; charset=UTF-8");
/*     */ 
/*     */       
/* 267 */       paramHttpServletResponse.getWriter().write("<html><body>Invalid SOAP request.</body></html>");
/* 268 */       paramHttpServletResponse.getWriter().close();
/*     */     }
/* 270 */     else if (str2.toLowerCase().indexOf("multipart/related") != -1) {
/*     */ 
/*     */ 
/*     */       
/* 274 */       String str3 = paramHttpServletRequest.getContentType().replaceAll("--=_part_", "--=_Part_");
/*     */ 
/*     */       
/* 277 */       StreamedAttachments streamedAttachments = new StreamedAttachments(messageContext, (InputStream)paramHttpServletRequest.getInputStream(), str3);
/*     */       
/* 279 */       String str4 = getEncoding(streamedAttachments.getSoapContentType());
/*     */       
/* 281 */       XMLStreamReader xMLStreamReader = STAXUtils.createXMLStreamReader(streamedAttachments.getSoapMessage().getDataHandler().getInputStream(), str4, messageContext);
/*     */ 
/*     */ 
/*     */       
/* 285 */       InMessage inMessage = new InMessage(xMLStreamReader, paramHttpServletRequest.getRequestURI());
/* 286 */       inMessage.setProperty("SOAPAction", str1);
/* 287 */       inMessage.setAttachments((Attachments)streamedAttachments);
/*     */       
/* 289 */       channel.receive(messageContext, inMessage);
/*     */ 
/*     */       
/*     */       try {
/* 293 */         xMLStreamReader.close();
/*     */       }
/* 295 */       catch (XMLStreamException xMLStreamException) {
/*     */         
/* 297 */         throw new XFireRuntimeException("Could not close XMLStreamReader.");
/*     */       }
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 303 */       String str = paramHttpServletRequest.getCharacterEncoding();
/* 304 */       str = dequote(str);
/* 305 */       XMLStreamReader xMLStreamReader = STAXUtils.createXMLStreamReader((InputStream)paramHttpServletRequest.getInputStream(), str, messageContext);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 310 */       InMessage inMessage = new InMessage(xMLStreamReader, paramHttpServletRequest.getRequestURI());
/* 311 */       inMessage.setProperty("SOAPAction", str1);
/* 312 */       channel.receive(messageContext, inMessage);
/*     */ 
/*     */       
/*     */       try {
/* 316 */         xMLStreamReader.close();
/*     */       }
/* 318 */       catch (XMLStreamException xMLStreamException) {
/*     */         
/* 320 */         throw new XFireRuntimeException("Could not close XMLStreamReader.");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   protected String dequote(String paramString) {
/* 327 */     if (paramString != null && paramString.length() > 0 && ((
/* 328 */       paramString.charAt(0) == '"' && paramString.charAt(paramString.length() - 1) == '"') || (paramString.charAt(0) == '\'' && paramString.charAt(paramString.length() - 1) == '\'')))
/*     */     {
/* 330 */       paramString = paramString.substring(1, paramString.length() - 1);
/*     */     }
/*     */     
/* 333 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   protected String getSoapAction(HttpServletRequest paramHttpServletRequest) {
/* 338 */     String str = paramHttpServletRequest.getHeader("SOAPAction");
/*     */     
/* 340 */     if (str != null && str.startsWith("\"") && str.endsWith("\"") && str.length() >= 2)
/*     */     {
/* 342 */       str = str.substring(1, str.length() - 1);
/*     */     }
/*     */     
/* 345 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   protected String getEncoding(String paramString) throws ServletException {
/* 350 */     if (paramString == null) {
/* 351 */       return "UTF-8";
/*     */     }
/* 353 */     int i = paramString.indexOf("type=");
/* 354 */     if (i == -1) return null;
/*     */     
/* 356 */     int j = paramString.indexOf("charset=", i);
/* 357 */     if (j == -1) return null;
/*     */     
/* 359 */     int k = paramString.indexOf("\"", j);
/* 360 */     if (k == -1) k = paramString.indexOf(";", j); 
/* 361 */     if (k == -1) throw new ServletException("Invalid content type: " + paramString);
/*     */     
/* 363 */     return paramString.substring(j + 8, k);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void generateWSDL(HttpServletResponse paramHttpServletResponse, String paramString) throws ServletException, IOException {
/* 376 */     Service service = getXFire().getServiceRegistry().getService(paramString);
/* 377 */     Object object = service.getProperty("wsdl.generation.disabled");
/* 378 */     boolean bool = "true".equalsIgnoreCase((object != null) ? object.toString() : null);
/* 379 */     if (bool) {
/* 380 */       logger.warn("WSDL generation disabled for service :" + paramString);
/* 381 */       paramHttpServletResponse.sendError(404, "No wsdl is avaiable for this service");
/*     */       
/*     */       return;
/*     */     } 
/* 385 */     paramHttpServletResponse.setStatus(200);
/* 386 */     paramHttpServletResponse.setContentType("text/xml");
/*     */     
/* 388 */     getXFire().generateWSDL(paramString, (OutputStream)paramHttpServletResponse.getOutputStream());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected String getService(HttpServletRequest paramHttpServletRequest) {
/* 396 */     String str2, str1 = paramHttpServletRequest.getPathInfo();
/*     */     
/* 398 */     if (str1 == null) {
/* 399 */       return null;
/*     */     }
/*     */ 
/*     */     
/* 403 */     if (str1.startsWith("/")) {
/*     */       
/* 405 */       str2 = str1.substring(1);
/*     */     }
/*     */     else {
/*     */       
/* 409 */       str2 = str1;
/*     */     } 
/*     */     
/* 412 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   protected Service getService(String paramString) {
/* 417 */     return getXFire().getServiceRegistry().getService(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public XFire getXFire() {
/* 422 */     return this.xfire;
/*     */   }
/*     */ 
/*     */   
/*     */   public ServiceRegistry getServiceRegistry() {
/* 427 */     return this.xfire.getServiceRegistry();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static class FaultResponseCodeHandler
/*     */     extends AbstractHandler
/*     */   {
/*     */     public FaultResponseCodeHandler() {
/* 436 */       setPhase("transport");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public void invoke(MessageContext param1MessageContext) {
/* 445 */       HttpServletResponse httpServletResponse = XFireServletController.getResponse();
/* 446 */       if (httpServletResponse != null)
/* 447 */         httpServletResponse.setStatus(500); 
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/codehaus/xfire/transport/http/XFireServletController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */