/*     */ package org.codehaus.xfire.util;
/*     */ 
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.io.Reader;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.xml.namespace.NamespaceContext;
/*     */ import javax.xml.parsers.DocumentBuilder;
/*     */ import javax.xml.stream.XMLInputFactory;
/*     */ import javax.xml.stream.XMLOutputFactory;
/*     */ import javax.xml.stream.XMLStreamException;
/*     */ import javax.xml.stream.XMLStreamReader;
/*     */ import javax.xml.stream.XMLStreamWriter;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import org.codehaus.xfire.MessageContext;
/*     */ import org.codehaus.xfire.XFireRuntimeException;
/*     */ import org.codehaus.xfire.util.stax.DepthXMLStreamReader;
/*     */ import org.w3c.dom.Attr;
/*     */ import org.w3c.dom.CDATASection;
/*     */ import org.w3c.dom.Comment;
/*     */ import org.w3c.dom.Document;
/*     */ import org.w3c.dom.Element;
/*     */ import org.w3c.dom.EntityReference;
/*     */ import org.w3c.dom.NamedNodeMap;
/*     */ import org.w3c.dom.Node;
/*     */ import org.w3c.dom.NodeList;
/*     */ import org.w3c.dom.ProcessingInstruction;
/*     */ import org.w3c.dom.Text;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class STAXUtils
/*     */ {
/*     */   private static final String XML_NS = "http://www.w3.org/2000/xmlns/";
/*     */   
/*     */   public static boolean skipToStartOfElement(XMLStreamReader in) throws XMLStreamException {
/*  49 */     for (int code = in.getEventType(); code != 8; code = in.next()) {
/*  50 */       if (code == 1) {
/*  51 */         return true;
/*     */       }
/*     */     } 
/*     */     
/*  55 */     return false;
/*     */   }
/*     */   
/*     */   public static boolean toNextElement(DepthXMLStreamReader dr) {
/*  59 */     if (dr.getEventType() == 1)
/*  60 */       return true; 
/*  61 */     if (dr.getEventType() == 2) {
/*  62 */       return false;
/*     */     }
/*     */     try {
/*  65 */       int depth = dr.getDepth();
/*     */       
/*  67 */       for (int event = dr.getEventType(); dr.getDepth() >= depth && dr.hasNext(); event = dr.next()) {
/*  68 */         if (event == 1 && dr.getDepth() == depth + 1) {
/*  69 */           return true;
/*     */         }
/*     */         
/*  72 */         if (event == 2) {
/*  73 */           depth--;
/*     */         }
/*     */       } 
/*     */       
/*  77 */       return false;
/*  78 */     } catch (XMLStreamException var3) {
/*  79 */       throw new XFireRuntimeException("Couldn't parse stream.", var3);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean skipToStartOfElement(DepthXMLStreamReader in) throws XMLStreamException {
/*  85 */     for (int code = in.getEventType(); code != 8; code = in.next()) {
/*  86 */       if (code == 1) {
/*  87 */         return true;
/*     */       }
/*     */     } 
/*     */     
/*  91 */     return false;
/*     */   }
/*     */   
/*     */   public static void copy(XMLStreamReader reader, XMLStreamWriter writer) throws XMLStreamException {
/*  95 */     int read = 0;
/*     */     
/*  97 */     for (int event = reader.getEventType(); reader.hasNext(); event = reader.next()) {
/*  98 */       switch (event) {
/*     */         case 1:
/* 100 */           read++;
/* 101 */           writeStartElement(reader, writer);
/*     */           break;
/*     */         case 2:
/* 104 */           writer.writeEndElement();
/* 105 */           read--;
/* 106 */           if (read <= 0) {
/*     */             return;
/*     */           }
/*     */           break;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         case 4:
/* 121 */           writer.writeCharacters(reader.getText());
/*     */           break;
/*     */         case 12:
/* 124 */           writer.writeCData(reader.getText());
/*     */           break;
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void writeStartElement(XMLStreamReader reader, XMLStreamWriter writer) throws XMLStreamException {
/* 131 */     String local = reader.getLocalName();
/* 132 */     String uri = reader.getNamespaceURI();
/* 133 */     String prefix = reader.getPrefix();
/* 134 */     if (prefix == null) {
/* 135 */       prefix = "";
/*     */     }
/*     */     
/* 138 */     String boundPrefix = writer.getPrefix(uri);
/* 139 */     boolean writeElementNS = false;
/* 140 */     if (boundPrefix == null || !prefix.equals(boundPrefix)) {
/* 141 */       writeElementNS = true;
/*     */     }
/*     */     
/* 144 */     if (uri != null && uri.length() > 0) {
/* 145 */       if (prefix.length() == 0) {
/* 146 */         writer.writeStartElement(local);
/* 147 */         writer.setDefaultNamespace(uri);
/*     */       } else {
/* 149 */         writer.writeStartElement(prefix, local, uri);
/* 150 */         writer.setPrefix(prefix, uri);
/*     */       } 
/*     */     } else {
/* 153 */       writer.writeStartElement(reader.getLocalName());
/*     */     } 
/*     */ 
/*     */     
/*     */     int i;
/*     */     
/* 159 */     for (i = 0; i < reader.getNamespaceCount(); i++) {
/* 160 */       String nsURI = reader.getNamespaceURI(i);
/* 161 */       String nsPrefix = reader.getNamespacePrefix(i);
/* 162 */       if (nsURI == null) {
/* 163 */         nsURI = "";
/*     */       }
/*     */       
/* 166 */       if (nsPrefix == null) {
/* 167 */         nsPrefix = "";
/*     */       }
/*     */       
/* 170 */       if (nsPrefix.length() == 0) {
/* 171 */         writer.writeDefaultNamespace(nsURI);
/*     */       } else {
/* 173 */         writer.writeNamespace(nsPrefix, nsURI);
/*     */       } 
/*     */       
/* 176 */       if (uri != null && nsURI.equals(uri) && nsPrefix.equals(prefix)) {
/* 177 */         writeElementNS = false;
/*     */       }
/*     */     } 
/*     */     
/* 181 */     if (writeElementNS && uri != null) {
/* 182 */       if (prefix != null && prefix.length() != 0) {
/* 183 */         writer.writeNamespace(prefix, uri);
/*     */       } else {
/* 185 */         writer.writeDefaultNamespace(uri);
/*     */       } 
/*     */     }
/*     */     
/* 189 */     for (i = 0; i < reader.getAttributeCount(); i++) {
/* 190 */       String nsURI = reader.getAttributeNamespace(i);
/* 191 */       String nsPrefix = reader.getAttributePrefix(i);
/* 192 */       if (nsURI != null && nsURI.length() != 0) {
/* 193 */         if (nsPrefix != null && nsPrefix.length() != 0) {
/* 194 */           writer.writeAttribute(reader.getAttributePrefix(i), reader.getAttributeNamespace(i), reader.getAttributeLocalName(i), reader.getAttributeValue(i));
/*     */         } else {
/* 196 */           writer.writeAttribute(reader.getAttributeNamespace(i), reader.getAttributeLocalName(i), reader.getAttributeValue(i));
/*     */         } 
/*     */       } else {
/* 199 */         writer.writeAttribute(reader.getAttributeLocalName(i), reader.getAttributeValue(i));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static void writeDocument(Document d, XMLStreamWriter writer, boolean repairing) throws XMLStreamException {
/* 206 */     writeDocument(d, writer, true, repairing);
/*     */   }
/*     */   
/*     */   public static void writeDocument(Document d, XMLStreamWriter writer, boolean writeProlog, boolean repairing) throws XMLStreamException {
/* 210 */     if (writeProlog) {
/* 211 */       writer.writeStartDocument();
/*     */     }
/*     */     
/* 214 */     Element root = d.getDocumentElement();
/* 215 */     writeElement(root, writer, repairing);
/* 216 */     if (writeProlog) {
/* 217 */       writer.writeEndDocument();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void writeElement(Element e, XMLStreamWriter writer, boolean repairing) throws XMLStreamException {
/* 223 */     String prefix = e.getPrefix();
/* 224 */     String ns = e.getNamespaceURI();
/* 225 */     String localName = e.getLocalName();
/* 226 */     if (prefix == null) {
/* 227 */       prefix = "";
/*     */     }
/*     */     
/* 230 */     if (localName == null) {
/* 231 */       localName = e.getNodeName();
/* 232 */       if (localName == null) {
/* 233 */         throw new IllegalStateException("Element's local name cannot be null!");
/*     */       }
/*     */     } 
/*     */     
/* 237 */     String decUri = null;
/* 238 */     NamespaceContext ctxt = writer.getNamespaceContext();
/* 239 */     if (ctxt != null) {
/* 240 */       decUri = ctxt.getNamespaceURI(prefix);
/*     */     }
/*     */     
/* 243 */     boolean declareNamespace = !(decUri != null && decUri.equals(ns));
/* 244 */     if (ns != null && ns.length() != 0) {
/* 245 */       writer.writeStartElement(prefix, localName, ns);
/*     */     } else {
/* 247 */       writer.writeStartElement(localName);
/*     */     } 
/*     */     
/* 250 */     NamedNodeMap attrs = e.getAttributes();
/*     */     
/* 252 */     for (int i = 0; i < attrs.getLength(); i++) {
/* 253 */       Node attr = attrs.item(i);
/* 254 */       String name = attr.getNodeName();
/* 255 */       String attrPrefix = "";
/* 256 */       int prefixIndex = name.indexOf(':');
/* 257 */       if (prefixIndex != -1) {
/* 258 */         attrPrefix = name.substring(0, prefixIndex);
/* 259 */         name = name.substring(prefixIndex + 1);
/*     */       } 
/*     */       
/* 262 */       if (attrPrefix.equals("xmlns")) {
/* 263 */         writer.writeNamespace(name, attr.getNodeValue());
/* 264 */         if (name.equals(prefix) && attr.getNodeValue().equals(ns)) {
/* 265 */           declareNamespace = false;
/*     */         }
/* 267 */       } else if (name.equals("xmlns") && attrPrefix.equals("")) {
/* 268 */         writer.writeNamespace("", attr.getNodeValue());
/* 269 */         if (attr.getNodeValue().equals(ns)) {
/* 270 */           declareNamespace = false;
/*     */         }
/*     */       } else {
/* 273 */         writer.writeAttribute(attrPrefix, attr.getNamespaceURI(), name, attr.getNodeValue());
/*     */       } 
/*     */     } 
/*     */     
/* 277 */     if (declareNamespace && repairing) {
/* 278 */       writer.writeNamespace(prefix, ns);
/*     */     }
/*     */     
/* 281 */     NodeList nodes = e.getChildNodes();
/*     */     
/* 283 */     for (int j = 0; j < nodes.getLength(); j++) {
/* 284 */       Node n = nodes.item(j);
/* 285 */       writeNode(n, writer, repairing);
/*     */     } 
/*     */     
/* 288 */     writer.writeEndElement();
/*     */   }
/*     */   
/*     */   public static void writeNode(Node n, XMLStreamWriter writer, boolean repairing) throws XMLStreamException {
/* 292 */     if (n instanceof Element) {
/* 293 */       writeElement((Element)n, writer, repairing);
/* 294 */     } else if (n instanceof Text) {
/* 295 */       writer.writeCharacters(((Text)n).getNodeValue());
/* 296 */     } else if (n instanceof CDATASection) {
/* 297 */       writer.writeCData(((CDATASection)n).getData());
/* 298 */     } else if (n instanceof Comment) {
/* 299 */       writer.writeComment(((Comment)n).getData());
/* 300 */     } else if (n instanceof EntityReference) {
/* 301 */       writer.writeEntityRef(((EntityReference)n).getNodeValue());
/* 302 */     } else if (n instanceof ProcessingInstruction) {
/* 303 */       ProcessingInstruction pi = (ProcessingInstruction)n;
/* 304 */       writer.writeProcessingInstruction(pi.getTarget(), pi.getData());
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static Document read(DocumentBuilder builder, XMLStreamReader reader, boolean repairing) throws XMLStreamException {
/* 310 */     Document doc = builder.newDocument();
/* 311 */     readDocElements(doc, reader, repairing);
/* 312 */     return doc;
/*     */   }
/*     */   
/*     */   private static Document getDocument(Node parent) {
/* 316 */     return (parent instanceof Document) ? (Document)parent : parent.getOwnerDocument();
/*     */   }
/*     */   
/*     */   private static Element startElement(Node parent, XMLStreamReader reader, boolean repairing) throws XMLStreamException {
/* 320 */     Document doc = getDocument(parent);
/* 321 */     Element e = doc.createElementNS(reader.getNamespaceURI(), reader.getLocalName());
/* 322 */     if (reader.getPrefix() != null && reader.getPrefix() != "") {
/* 323 */       e.setPrefix(reader.getPrefix());
/*     */     }
/*     */     
/* 326 */     parent.appendChild(e);
/*     */ 
/*     */     
/*     */     int att;
/*     */     
/* 331 */     for (att = 0; att < reader.getNamespaceCount(); att++) {
/* 332 */       String name = reader.getNamespaceURI(att);
/* 333 */       String prefix = reader.getNamespacePrefix(att);
/* 334 */       declare(e, name, prefix);
/*     */     } 
/*     */     
/* 337 */     for (att = 0; att < reader.getAttributeCount(); att++) {
/* 338 */       String name = reader.getAttributeLocalName(att);
/* 339 */       String prefix = reader.getAttributePrefix(att);
/* 340 */       if (prefix != null && prefix.length() > 0) {
/* 341 */         name = String.valueOf(prefix) + ":" + name;
/*     */       }
/*     */       
/* 344 */       Attr attr = doc.createAttributeNS(reader.getAttributeNamespace(att), name);
/* 345 */       attr.setValue(reader.getAttributeValue(att));
/* 346 */       e.setAttributeNode(attr);
/*     */     } 
/*     */     
/* 349 */     reader.next();
/* 350 */     readDocElements(e, reader, repairing);
/* 351 */     if (repairing && !isDeclared(e, reader.getNamespaceURI(), reader.getPrefix())) {
/* 352 */       declare(e, reader.getNamespaceURI(), reader.getPrefix());
/*     */     }
/*     */     
/* 355 */     return e;
/*     */   }
/*     */   
/*     */   private static boolean isDeclared(Element e, String namespaceURI, String prefix) {
/*     */     Attr att;
/* 360 */     if (prefix != null && prefix.length() > 0) {
/* 361 */       att = e.getAttributeNodeNS("http://www.w3.org/2000/xmlns/", "xmlns:" + prefix);
/*     */     } else {
/* 363 */       att = e.getAttributeNode("xmlns");
/*     */     } 
/*     */     
/* 366 */     if (att != null && att.getNodeValue().equals(namespaceURI)) {
/* 367 */       return true;
/*     */     }
/* 369 */     return (e.getParentNode() instanceof Element) ? isDeclared((Element)e.getParentNode(), namespaceURI, prefix) : false;
/*     */   }
/*     */ 
/*     */   
/*     */   public static void readDocElements(Node parent, XMLStreamReader reader, boolean repairing) throws XMLStreamException {
/* 374 */     Document doc = getDocument(parent);
/* 375 */     int event = reader.getEventType();
/*     */     
/* 377 */     while (reader.hasNext()) {
/* 378 */       switch (event) {
/*     */         case 1:
/* 380 */           startElement(parent, reader, repairing);
/* 381 */           if (parent instanceof Document) {
/* 382 */             if (reader.hasNext()) {
/* 383 */               reader.next();
/*     */             }
/*     */             return;
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 2:
/*     */           return;
/*     */         case 3:
/* 392 */           parent.appendChild(doc.createProcessingInstruction(reader.getPITarget(), reader.getPIData()));
/*     */           break;
/*     */         case 4:
/* 395 */           if (parent != null) {
/* 396 */             parent.appendChild(doc.createTextNode(reader.getText()));
/*     */           }
/*     */           break;
/*     */         case 5:
/* 400 */           if (parent != null) {
/* 401 */             parent.appendChild(doc.createComment(reader.getText()));
/*     */           }
/*     */           break;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         case 9:
/* 412 */           parent.appendChild(doc.createProcessingInstruction(reader.getPITarget(), reader.getPIData()));
/*     */           break;
/*     */         case 12:
/* 415 */           parent.appendChild(doc.createCDATASection(reader.getText()));
/*     */           break;
/*     */       } 
/* 418 */       if (reader.hasNext()) {
/* 419 */         event = reader.next();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private static void declare(Element node, String uri, String prefix) {
/* 426 */     if (prefix != null && prefix.length() > 0) {
/* 427 */       node.setAttributeNS("http://www.w3.org/2000/xmlns/", "xmlns:" + prefix, uri);
/* 428 */     } else if (uri != null) {
/* 429 */       node.setAttributeNS("http://www.w3.org/2000/xmlns/", "xmlns", uri);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static XMLStreamWriter createXMLStreamWriter(OutputStream out, String encoding, MessageContext ctx) {
/* 435 */     XMLOutputFactory factory = getXMLOutputFactory(ctx);
/* 436 */     if (encoding == null) {
/* 437 */       encoding = "UTF-8";
/*     */     }
/*     */     
/*     */     try {
/* 441 */       XMLStreamWriter writer = factory.createXMLStreamWriter(out, encoding);
/* 442 */       return writer;
/* 443 */     } catch (XMLStreamException var5) {
/* 444 */       throw new XFireRuntimeException("Couldn't parse stream.", var5);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static XMLOutputFactory getXMLOutputFactory(MessageContext ctx) {
/* 449 */     if (ctx == null) {
/* 450 */       return xmlOututFactory;
/*     */     }
/* 452 */     Object outFactoryObj = ctx.getContextualProperty("xfire.stax.output.factory");
/* 453 */     if (outFactoryObj instanceof XMLOutputFactory)
/* 454 */       return (XMLOutputFactory)outFactoryObj; 
/* 455 */     if (outFactoryObj instanceof String) {
/* 456 */       String outFactory = (String)outFactoryObj;
/* 457 */       XMLOutputFactory xof = (XMLOutputFactory)factories.get(outFactory);
/* 458 */       if (xof == null) {
/* 459 */         xof = (XMLOutputFactory)createFactory(outFactory, ctx);
/* 460 */         factories.put(outFactory, xof);
/*     */       } 
/*     */       
/* 463 */       return xof;
/*     */     } 
/* 465 */     return xmlOututFactory;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static XMLInputFactory getXMLInputFactory(MessageContext ctx) {
/* 471 */     if (ctx == null) {
/* 472 */       return xmlInputFactory;
/*     */     }
/* 474 */     Object inFactoryObj = ctx.getContextualProperty("xfire.stax.input.factory");
/* 475 */     if (inFactoryObj instanceof XMLInputFactory)
/* 476 */       return (XMLInputFactory)inFactoryObj; 
/* 477 */     if (inFactoryObj instanceof String) {
/* 478 */       String inFactory = (String)inFactoryObj;
/* 479 */       XMLInputFactory xif = (XMLInputFactory)factories.get(inFactory);
/* 480 */       if (xif == null) {
/* 481 */         xif = (XMLInputFactory)createFactory(inFactory, ctx);
/* 482 */         configureFactory(xif, ctx);
/* 483 */         factories.put(inFactory, xif);
/*     */       } 
/*     */       
/* 486 */       return xif;
/*     */     } 
/* 488 */     if (!inFactoryConfigured) {
/* 489 */       configureFactory(xmlInputFactory, ctx);
/* 490 */       inFactoryConfigured = true;
/*     */     } 
/*     */     
/* 493 */     return xmlInputFactory;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static Boolean getBooleanProperty(MessageContext ctx, String name) {
/* 499 */     Object value = ctx.getContextualProperty(name);
/* 500 */     return (value != null) ? Boolean.valueOf(value.toString()) : null;
/*     */   }
/*     */   
/*     */   private static void configureFactory(XMLInputFactory xif, MessageContext ctx) {
/* 504 */     Boolean value = getBooleanProperty(ctx, "javax.xml.stream.isValidating");
/* 505 */     if (value != null) {
/* 506 */       xif.setProperty("javax.xml.stream.isValidating", value);
/*     */     }
/*     */     
/* 509 */     value = getBooleanProperty(ctx, "javax.xml.stream.isNamespaceAware");
/* 510 */     if (value != null) {
/* 511 */       xif.setProperty("javax.xml.stream.isNamespaceAware", value);
/*     */     }
/*     */     
/* 514 */     value = getBooleanProperty(ctx, "javax.xml.stream.isCoalescing");
/* 515 */     if (value != null) {
/* 516 */       xif.setProperty("javax.xml.stream.isCoalescing", value);
/*     */     }
/*     */     
/* 519 */     value = getBooleanProperty(ctx, "javax.xml.stream.isReplacingEntityReferences");
/* 520 */     if (value != null) {
/* 521 */       xif.setProperty("javax.xml.stream.isReplacingEntityReferences", value);
/*     */     }
/*     */     
/* 524 */     value = getBooleanProperty(ctx, "javax.xml.stream.isSupportingExternalEntities");
/* 525 */     if (value != null) {
/* 526 */       xif.setProperty("javax.xml.stream.isSupportingExternalEntities", value);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   private static Object createFactory(String factory, MessageContext ctx) {
/* 532 */     Class factoryClass = null;
/*     */     
/*     */     try {
/* 535 */       factoryClass = ClassLoaderUtils.loadClass(factory, ctx.getClass());
/* 536 */       return factoryClass.newInstance();
/* 537 */     } catch (Exception var4) {
/* 538 */       logger.error("Can't create factory for class : " + factory, var4);
/* 539 */       throw new XFireRuntimeException("Can't create factory for class : " + factory);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static XMLStreamReader createXMLStreamReader(InputStream in, String encoding, MessageContext ctx) {
/* 544 */     XMLInputFactory factory = getXMLInputFactory(ctx);
/* 545 */     if (encoding == null) {
/* 546 */       encoding = "UTF-8";
/*     */     }
/*     */     
/*     */     try {
/* 550 */       factory.setProperty("javax.xml.stream.isSupportingExternalEntities", Boolean.FALSE);
/* 551 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 556 */       factory.setProperty("javax.xml.stream.supportDTD", Boolean.FALSE);
/* 557 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 562 */       return factory.createXMLStreamReader(in, encoding);
/* 563 */     } catch (XMLStreamException var5) {
/* 564 */       throw new XFireRuntimeException("Couldn't parse stream.", var5);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static XMLStreamReader createXMLStreamReader(Reader reader) {
/* 569 */     return createXMLStreamReader(reader, null);
/*     */   }
/*     */   
/*     */   public static XMLStreamReader createXMLStreamReader(Reader reader, MessageContext context) {
/* 573 */     XMLInputFactory factory = getXMLInputFactory(context);
/*     */     
/*     */     try {
/* 576 */       return factory.createXMLStreamReader(reader);
/* 577 */     } catch (XMLStreamException var4) {
/* 578 */       throw new XFireRuntimeException("Couldn't parse stream.", var4);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/* 583 */   private static final Log logger = LogFactory.getLog(STAXUtils.class);
/* 584 */   private static final XMLInputFactory xmlInputFactory = XMLInputFactory.newInstance();
/* 585 */   private static final XMLOutputFactory xmlOututFactory = XMLOutputFactory.newInstance();
/* 586 */   private static final Map factories = new HashMap<Object, Object>();
/*     */   private static boolean inFactoryConfigured;
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/org/codehaus/xfire/util/STAXUtils.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */