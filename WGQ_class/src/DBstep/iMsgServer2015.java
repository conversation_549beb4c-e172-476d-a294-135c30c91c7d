/*     */ package DBstep;
/*     */ 
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.apache.commons.fileupload.FileItem;
/*     */ import org.apache.commons.fileupload.FileItemFactory;
/*     */ import org.apache.commons.fileupload.FileUploadException;
/*     */ import org.apache.commons.fileupload.disk.DiskFileItemFactory;
/*     */ import org.apache.commons.fileupload.servlet.ServletFileUpload;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ public class iMsgServer2015
/*     */   extends BaseBean {
/*  25 */   private Hashtable<String, String> saveFormParam = new Hashtable<>();
/*  26 */   private Hashtable<String, String> sendFormParam = new Hashtable<>();
/*  27 */   private List list = new ArrayList();
/*     */   private InputStream fileContentStream;
/*  29 */   private String fileName = "";
/*  30 */   private byte[] mFileBody = null;
/*  31 */   private String sendType = "";
/*  32 */   private int FFileSize = 0;
/*     */ 
/*     */   
/*     */   private static final String MsgError = "404";
/*     */   
/*     */   private String ReturnValue;
/*     */ 
/*     */   
/*     */   public String getReturnValue() {
/*  41 */     return this.ReturnValue;
/*     */   }
/*     */   
/*     */   public void setReturnValue(String paramString) {
/*  45 */     this.ReturnValue = paramString;
/*     */   }
/*     */   
/*     */   public String getSendType() {
/*  49 */     return this.sendType;
/*     */   }
/*     */   
/*     */   public void setSendType(String paramString) {
/*  53 */     this.sendType = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void Load(HttpServletRequest paramHttpServletRequest) throws FileUploadException, IOException {
/*  63 */     paramHttpServletRequest.setCharacterEncoding("UTF-8");
/*  64 */     DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
/*  65 */     ServletFileUpload servletFileUpload = new ServletFileUpload((FileItemFactory)diskFileItemFactory);
/*     */ 
/*     */     
/*  68 */     List<FileItem> list = servletFileUpload.parseRequest(paramHttpServletRequest);
/*     */ 
/*     */     
/*  71 */     writeLog("文件上传");
/*  72 */     if (list != null && list.size() > 0) {
/*  73 */       for (byte b = 0; b < list.size(); b++) {
/*  74 */         FileItem fileItem = list.get(b);
/*  75 */         if (fileItem.isFormField()) {
/*  76 */           processFormField(fileItem);
/*     */         } else {
/*  78 */           processUploadedFile(fileItem);
/*     */         } 
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void processFormField(FileItem paramFileItem) throws UnsupportedEncodingException {
/*  91 */     String str1 = paramFileItem.getFieldName();
/*  92 */     String str2 = "";
/*  93 */     str2 = paramFileItem.getString("utf-8");
/*  94 */     if (this.sendType.equalsIgnoreCase("JSON")) {
/*  95 */       JSONObject jSONObject = JSONObject.fromObject(str2);
/*  96 */       Iterator<String> iterator = jSONObject.keySet().iterator();
/*  97 */       while (iterator.hasNext()) {
/*  98 */         str1 = iterator.next();
/*  99 */         str2 = jSONObject.getString(str1);
/* 100 */         this.saveFormParam.put(str1, str2);
/*     */       } 
/*     */       return;
/*     */     } 
/* 104 */     this.saveFormParam.put(str1, str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void processUploadedFile(FileItem paramFileItem) throws IOException {
/* 115 */     this.fileName = paramFileItem.getName();
/* 116 */     if (this.fileName.indexOf("/") >= 0) {
/* 117 */       this.fileName = this.fileName.substring(this.fileName.lastIndexOf("/") + 1);
/* 118 */     } else if (this.fileName.indexOf("\\") >= 0) {
/* 119 */       this.fileName = this.fileName.substring(this.fileName.lastIndexOf("\\") + 1);
/*     */     } 
/* 121 */     this.fileContentStream = paramFileItem.getInputStream();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String GetMsgByName(String paramString) {
/* 131 */     return this.saveFormParam.get(paramString);
/*     */   }
/*     */   
/*     */   public String SetMsgByName(String paramString1, String paramString2) {
/* 135 */     return this.saveFormParam.put(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void MsgTextClear() {
/* 141 */     this.saveFormParam.clear();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void ListClear() {
/* 148 */     this.list.clear();
/*     */   }
/*     */ 
/*     */   
/*     */   public int MsgFileSize() {
/* 153 */     return this.FFileSize;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean DelFile(String paramString) {
/* 158 */     File file = new File(paramString);
/* 159 */     if (file.exists()) {
/* 160 */       file.delete();
/*     */     } else {
/* 162 */       SetMsgByName("DelFileState", "失败");
/* 163 */       return false;
/*     */     } 
/* 165 */     SetMsgByName("DelFileState", "成功");
/* 166 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public int GetFieldCount() {
/* 171 */     return this.saveFormParam.size();
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean MakeDirectory(String paramString) {
/* 176 */     File file = new File(paramString);
/* 177 */     file.mkdirs();
/* 178 */     return file.isDirectory();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String GetFieldName(int paramInt) {
/* 184 */     int i = this.list.size();
/* 185 */     if (i == 0) {
/* 186 */       this.saveFormParam.remove("OPTION");
/* 187 */       this.saveFormParam.remove("TEMPLATE");
/* 188 */       Iterator<String> iterator = this.saveFormParam.keySet().iterator();
/* 189 */       while (iterator.hasNext()) {
/* 190 */         String str1 = iterator.next();
/* 191 */         this.list.add(str1);
/*     */       } 
/*     */     } 
/* 194 */     String str = this.list.get(paramInt).toString();
/* 195 */     writeLog(str);
/* 196 */     return str;
/*     */   }
/*     */   
/*     */   public void MsgFileBody(byte[] paramArrayOfbyte) {
/* 200 */     if (paramArrayOfbyte != null) {
/* 201 */       this.FFileSize = paramArrayOfbyte.length;
/* 202 */       this.mFileBody = paramArrayOfbyte;
/*     */     } else {
/* 204 */       this.mFileBody = paramArrayOfbyte;
/*     */     } 
/*     */   }
/*     */   
/*     */   public byte[] MsgFileBody() throws IOException {
/* 209 */     this.mFileBody = null;
/* 210 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 211 */     byte[] arrayOfByte = new byte[4096];
/* 212 */     int i = 0;
/* 213 */     while (-1 != (i = this.fileContentStream.read(arrayOfByte))) {
/* 214 */       byteArrayOutputStream.write(arrayOfByte, 0, i);
/*     */     }
/* 216 */     this.mFileBody = byteArrayOutputStream.toByteArray();
/*     */     
/* 218 */     return this.mFileBody;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean MsgFileSave(String paramString) {
/* 246 */     return false;
/*     */   }
/*     */   
/*     */   public boolean MsgFileLoad(String paramString) throws IOException {
/* 250 */     File file = new File(paramString);
/* 251 */     if (file.exists()) {
/* 252 */       this.fileContentStream = new FileInputStream(new File(paramString));
/* 253 */       MsgFileBody();
/*     */     } else {
/* 255 */       this.mFileBody = new byte[0];
/*     */     } 
/* 257 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String GetHashToJson() {
/* 263 */     JSONObject jSONObject = new JSONObject();
/* 264 */     for (String str : this.saveFormParam.keySet()) {
/*     */       
/*     */       try {
/* 267 */         jSONObject.put(new String(str.getBytes("UTF-8"), "ISO8859-1"), new String(((String)this.saveFormParam.get(str)).getBytes("UTF-8"), "ISO8859-1"));
/* 268 */       } catch (Exception exception) {
/* 269 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 272 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void GetMarlListToJson(HttpServletResponse paramHttpServletResponse) {
/*     */     try {
/* 278 */       String str = "";
/* 279 */       for (String str1 : this.saveFormParam.keySet()) {
/*     */         
/* 281 */         str = this.saveFormParam.get(str1);
/* 282 */         writeLog("方法GetMarlListToJson-------------------key：" + str1);
/* 283 */         writeLog("方法GetMarlListToJson-------------------MarlListKey：" + str);
/* 284 */         paramHttpServletResponse.setHeader(new String(str1.getBytes("UTF-8"), "ISO8859-1"), new String(str.getBytes("UTF-8"), "ISO8859-1"));
/*     */       } 
/* 286 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/*     */       
/* 288 */       unsupportedEncodingException.printStackTrace();
/* 289 */       writeLog("GetMarlListToJson,exception:", unsupportedEncodingException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void Send(HttpServletResponse paramHttpServletResponse) throws IOException {
/*     */     try {
/* 301 */       paramHttpServletResponse.reset();
/* 302 */       paramHttpServletResponse.setHeader("content-type", "text/html;charset=ISO8859-1");
/* 303 */       paramHttpServletResponse.setCharacterEncoding("ISO8859-1");
/* 304 */       GetMarlListToJson(paramHttpServletResponse);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 311 */       if (this.mFileBody != null && this.mFileBody.length != 0) {
/* 312 */         writeLog("send mFileBody.length=" + this.mFileBody.length);
/*     */         
/* 314 */         paramHttpServletResponse.setContentLength(this.mFileBody.length);
/* 315 */         paramHttpServletResponse.getOutputStream().write(this.mFileBody, 0, this.mFileBody.length);
/* 316 */         paramHttpServletResponse.getOutputStream().flush();
/* 317 */         paramHttpServletResponse.getOutputStream().close();
/*     */       } else {
/*     */         
/* 320 */         paramHttpServletResponse.setHeader("MsgError", "404");
/*     */       } 
/* 322 */       paramHttpServletResponse.flushBuffer();
/* 323 */     } catch (Exception exception) {
/* 324 */       paramHttpServletResponse.setHeader("MsgError", "404");
/* 325 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/DBstep/iMsgServer2015.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */