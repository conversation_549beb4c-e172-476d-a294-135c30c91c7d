/*     */ package DBstep;
/*     */ 
/*     */ import java.sql.Connection;
/*     */ import java.sql.Date;
/*     */ import java.sql.DriverManager;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.Statement;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class iDBManager2000
/*     */ {
/*  19 */   public String ClassString = null;
/*  20 */   public String ConnectionString = null;
/*  21 */   public String UserName = null;
/*  22 */   public String PassWord = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Connection Conn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Statement Stmt;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public iDBManager2000() {
/*  43 */     this.ClassString = "com.microsoft.jdbc.sqlserver.SQLServerDriver";
/*  44 */     this.ConnectionString = "jdbc:microsoft:sqlserver://127.0.0.1:1433;DatabaseName=DBDemo;User=******;Password=******";
/*  45 */     this.UserName = "******";
/*  46 */     this.PassWord = "******";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean OpenConnection() {
/*  71 */     boolean mResult = true;
/*     */     
/*     */     try {
/*  74 */       Class.forName(this.ClassString);
/*  75 */       if (this.UserName == null && this.PassWord == null) {
/*     */         
/*  77 */         this.Conn = DriverManager.getConnection(this.ConnectionString);
/*     */       }
/*     */       else {
/*     */         
/*  81 */         this.Conn = DriverManager.getConnection(this.ConnectionString, this.UserName, this.PassWord);
/*     */       } 
/*     */       
/*  84 */       this.Stmt = this.Conn.createStatement();
/*  85 */       mResult = true;
/*     */     }
/*  87 */     catch (Exception e) {
/*     */       
/*  89 */       System.out.println(e.toString());
/*  90 */       mResult = false;
/*     */     } 
/*  92 */     return mResult;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void CloseConnection() {
/*     */     try {
/* 100 */       this.Stmt.close();
/* 101 */       this.Conn.close();
/*     */     }
/* 103 */     catch (Exception e) {
/*     */       
/* 105 */       System.out.println(e.toString());
/*     */     } 
/*     */   }
/*     */   
/*     */   public String GetDateTime() {
/* 110 */     Calendar cal = Calendar.getInstance();
/* 111 */     SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 112 */     String mDateTime = formatter.format(cal.getTime());
/* 113 */     return mDateTime;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Date GetDate() {
/* 119 */     Calendar cal = Calendar.getInstance();
/* 120 */     SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
/* 121 */     String mDateTime = formatter.format(cal.getTime());
/* 122 */     return Date.valueOf(mDateTime);
/*     */   }
/*     */ 
/*     */   
/*     */   public int GetMaxID(String vTableName, String vFieldName) {
/* 127 */     int mResult = 0;
/* 128 */     String mSql = new String();
/* 129 */     mSql = String.valueOf(String.valueOf((new StringBuffer("select max(")).append(vFieldName).append(")+1 as MaxID from ").append(vTableName)));
/* 130 */     if (OpenConnection()) {
/*     */ 
/*     */       
/*     */       try {
/* 134 */         ResultSet result = ExecuteQuery(mSql);
/* 135 */         if (result.next())
/*     */         {
/* 137 */           mResult = result.getInt("MaxID");
/*     */         }
/* 139 */         result.close();
/*     */       }
/* 141 */       catch (Exception e) {
/*     */         
/* 143 */         System.out.println(e.toString());
/*     */       } 
/* 145 */       CloseConnection();
/*     */     } 
/* 147 */     return mResult;
/*     */   }
/*     */ 
/*     */   
/*     */   public ResultSet ExecuteQuery(String SqlString) {
/* 152 */     ResultSet result = null;
/*     */     
/*     */     try {
/* 155 */       result = this.Stmt.executeQuery(SqlString);
/*     */     }
/* 157 */     catch (Exception e) {
/*     */       
/* 159 */       System.out.println(e.toString());
/*     */     } 
/* 161 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public int ExecuteUpdate(String SqlString) {
/* 166 */     int result = 0;
/*     */     
/*     */     try {
/* 169 */       result = this.Stmt.executeUpdate(SqlString);
/*     */     }
/* 171 */     catch (Exception e) {
/*     */       
/* 173 */       System.out.println(e.toString());
/*     */     } 
/* 175 */     return result;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/DBstep/iDBManager2000.class
 * Java compiler version: 1 (45.3)
 * JD-Core Version:       1.1.3
 */