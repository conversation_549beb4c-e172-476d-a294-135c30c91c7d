/*      */ package DBstep;
/*      */ 
/*      */ import java.io.ByteArrayInputStream;
/*      */ import java.io.ByteArrayOutputStream;
/*      */ import java.io.File;
/*      */ import java.io.FileInputStream;
/*      */ import java.io.FileOutputStream;
/*      */ import java.io.RandomAccessFile;
/*      */ import java.io.UnsupportedEncodingException;
/*      */ import javax.servlet.ServletInputStream;
/*      */ import javax.servlet.ServletOutputStream;
/*      */ import javax.servlet.ServletRequest;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class iMsgServer2000
/*      */ {
/*   40 */   private String VERSION = "DBSTEP V3.0";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   51 */   private String TableBase64 = "FxcYg3UZvtEz50Na8G476=mLDI/jVfC9dsoMAiBhJSu2qPKe+QRbXry1TnkWHlOpw";
/*      */   
/*      */   private File FMsgFile;
/*      */   
/*   55 */   private String FMsgText = new String();
/*   56 */   private String FError = new String();
/*   57 */   private String FVersion = new String();
/*   58 */   private long FFileSize = 0L;
/*      */   
/*      */   private String FTempName;
/*   61 */   public int BuffSize = 100;
/*      */   public boolean CryptFile = false;
/*   63 */   public String Charset = "GB2312";
/*      */   
/*      */   static final int S11 = 7;
/*      */   
/*      */   static final int S12 = 12;
/*      */   
/*      */   static final int S13 = 17;
/*      */   
/*      */   static final int S14 = 22;
/*      */   
/*      */   static final int S21 = 5;
/*      */   
/*      */   static final int S22 = 9;
/*      */   
/*      */   static final int S23 = 14;
/*      */   
/*      */   static final int S24 = 20;
/*      */   
/*      */   static final int S31 = 4;
/*      */   
/*      */   static final int S32 = 11;
/*      */   
/*      */   static final int S33 = 16;
/*      */   
/*      */   static final int S34 = 23;
/*      */   static final int S41 = 6;
/*      */   static final int S42 = 10;
/*      */   static final int S43 = 15;
/*      */   static final int S44 = 21;
/*   92 */   static final byte[] PADDING = new byte[] { Byte.MIN_VALUE };
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   99 */   private long[] state = new long[4];
/*  100 */   private long[] count = new long[2];
/*      */   
/*  102 */   private byte[] buffer = new byte[64];
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String digestHexStr;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  112 */   private byte[] digest = new byte[16];
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getMD5ofStr(String inbuf) {
/*  119 */     md5Init();
/*  120 */     md5Update(inbuf.getBytes(), inbuf.length());
/*  121 */     md5Final();
/*  122 */     this.digestHexStr = "";
/*  123 */     for (int i = 0; i < 16; i++) {
/*  124 */       this.digestHexStr = String.valueOf(this.digestHexStr) + byteHEX(this.digest[i]);
/*      */     }
/*  126 */     return this.digestHexStr;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private void md5Init() {
/*  132 */     this.count[0] = 0L;
/*  133 */     this.count[1] = 0L;
/*      */ 
/*      */     
/*  136 */     this.state[0] = 1732584193L;
/*  137 */     this.state[1] = 4023233417L;
/*  138 */     this.state[2] = 2562383102L;
/*  139 */     this.state[3] = 271733878L;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private long F(long x, long y, long z) {
/*  150 */     return x & y | (x ^ 0xFFFFFFFFFFFFFFFFL) & z;
/*      */   }
/*      */ 
/*      */   
/*      */   private long G(long x, long y, long z) {
/*  155 */     return x & z | y & (z ^ 0xFFFFFFFFFFFFFFFFL);
/*      */   }
/*      */ 
/*      */   
/*      */   private long H(long x, long y, long z) {
/*  160 */     return x ^ y ^ z;
/*      */   }
/*      */   
/*      */   private long I(long x, long y, long z) {
/*  164 */     return y ^ (x | z ^ 0xFFFFFFFFFFFFFFFFL);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private long FF(long a, long b, long c, long d, long x, long s, long ac) {
/*  174 */     a += F(b, c, d) + x + ac;
/*  175 */     a = ((int)a << (int)s | (int)a >>> (int)(32L - s));
/*  176 */     a += b;
/*  177 */     return a;
/*      */   }
/*      */   
/*      */   private long GG(long a, long b, long c, long d, long x, long s, long ac) {
/*  181 */     a += G(b, c, d) + x + ac;
/*  182 */     a = ((int)a << (int)s | (int)a >>> (int)(32L - s));
/*  183 */     a += b;
/*  184 */     return a;
/*      */   }
/*      */   
/*      */   private long HH(long a, long b, long c, long d, long x, long s, long ac) {
/*  188 */     a += H(b, c, d) + x + ac;
/*  189 */     a = ((int)a << (int)s | (int)a >>> (int)(32L - s));
/*  190 */     a += b;
/*  191 */     return a;
/*      */   }
/*      */   
/*      */   private long II(long a, long b, long c, long d, long x, long s, long ac) {
/*  195 */     a += I(b, c, d) + x + ac;
/*  196 */     a = ((int)a << (int)s | (int)a >>> (int)(32L - s));
/*  197 */     a += b;
/*  198 */     return a;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void md5Update(byte[] inbuf, int inputLen) {
/*      */     int i;
/*  208 */     byte[] block = new byte[64];
/*  209 */     int index = (int)(this.count[0] >>> 3L) & 0x3F;
/*      */     
/*  211 */     this.count[0] = this.count[0] + (inputLen << 3); if (this.count[0] + (inputLen << 3) < (inputLen << 3))
/*  212 */       this.count[1] = this.count[1] + 1L; 
/*  213 */     this.count[1] = this.count[1] + (inputLen >>> 29);
/*      */     
/*  215 */     int partLen = 64 - index;
/*      */ 
/*      */     
/*  218 */     if (inputLen >= partLen) {
/*  219 */       md5Memcpy(this.buffer, inbuf, index, 0, partLen);
/*  220 */       md5Transform(this.buffer);
/*      */       
/*  222 */       for (i = partLen; i + 63 < inputLen; i += 64) {
/*      */         
/*  224 */         md5Memcpy(block, inbuf, 0, i, 64);
/*  225 */         md5Transform(block);
/*      */       } 
/*  227 */       index = 0;
/*      */     }
/*      */     else {
/*      */       
/*  231 */       i = 0;
/*      */     } 
/*      */     
/*  234 */     md5Memcpy(this.buffer, inbuf, index, i, inputLen - i);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void md5Final() {
/*  243 */     byte[] bits = new byte[8];
/*      */ 
/*      */ 
/*      */     
/*  247 */     Encode(bits, this.count, 8);
/*      */ 
/*      */     
/*  250 */     int index = (int)(this.count[0] >>> 3L) & 0x3F;
/*  251 */     int padLen = (index < 56) ? (56 - index) : (120 - index);
/*  252 */     md5Update(PADDING, padLen);
/*      */ 
/*      */     
/*  255 */     md5Update(bits, 8);
/*      */ 
/*      */     
/*  258 */     Encode(this.digest, this.state, 16);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void md5Memcpy(byte[] output, byte[] input, int outpos, int inpos, int len) {
/*  273 */     for (int i = 0; i < len; i++) {
/*  274 */       output[outpos + i] = input[inpos + i];
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void md5Transform(byte[] block) {
/*  282 */     long a = this.state[0], b = this.state[1], c = this.state[2], d = this.state[3];
/*  283 */     long[] x = new long[16];
/*      */     
/*  285 */     Decode(x, block, 64);
/*      */ 
/*      */     
/*  288 */     a = FF(a, b, c, d, x[0], 7L, 3614090360L);
/*  289 */     d = FF(d, a, b, c, x[1], 12L, 3905402710L);
/*  290 */     c = FF(c, d, a, b, x[2], 17L, 606105819L);
/*  291 */     b = FF(b, c, d, a, x[3], 22L, 3250441966L);
/*  292 */     a = FF(a, b, c, d, x[4], 7L, 4118548399L);
/*  293 */     d = FF(d, a, b, c, x[5], 12L, 1200080426L);
/*  294 */     c = FF(c, d, a, b, x[6], 17L, 2821735955L);
/*  295 */     b = FF(b, c, d, a, x[7], 22L, 4249261313L);
/*  296 */     a = FF(a, b, c, d, x[8], 7L, 1770035416L);
/*  297 */     d = FF(d, a, b, c, x[9], 12L, 2336552879L);
/*  298 */     c = FF(c, d, a, b, x[10], 17L, 4294925233L);
/*  299 */     b = FF(b, c, d, a, x[11], 22L, 2304563134L);
/*  300 */     a = FF(a, b, c, d, x[12], 7L, 1804603682L);
/*  301 */     d = FF(d, a, b, c, x[13], 12L, 4254626195L);
/*  302 */     c = FF(c, d, a, b, x[14], 17L, 2792965006L);
/*  303 */     b = FF(b, c, d, a, x[15], 22L, 1236535329L);
/*      */ 
/*      */     
/*  306 */     a = GG(a, b, c, d, x[1], 5L, 4129170786L);
/*  307 */     d = GG(d, a, b, c, x[6], 9L, 3225465664L);
/*  308 */     c = GG(c, d, a, b, x[11], 14L, 643717713L);
/*  309 */     b = GG(b, c, d, a, x[0], 20L, 3921069994L);
/*  310 */     a = GG(a, b, c, d, x[5], 5L, 3593408605L);
/*  311 */     d = GG(d, a, b, c, x[10], 9L, 38016083L);
/*  312 */     c = GG(c, d, a, b, x[15], 14L, 3634488961L);
/*  313 */     b = GG(b, c, d, a, x[4], 20L, 3889429448L);
/*  314 */     a = GG(a, b, c, d, x[9], 5L, 568446438L);
/*  315 */     d = GG(d, a, b, c, x[14], 9L, 3275163606L);
/*  316 */     c = GG(c, d, a, b, x[3], 14L, 4107603335L);
/*  317 */     b = GG(b, c, d, a, x[8], 20L, 1163531501L);
/*  318 */     a = GG(a, b, c, d, x[13], 5L, 2850285829L);
/*  319 */     d = GG(d, a, b, c, x[2], 9L, 4243563512L);
/*  320 */     c = GG(c, d, a, b, x[7], 14L, 1735328473L);
/*  321 */     b = GG(b, c, d, a, x[12], 20L, 2368359562L);
/*      */ 
/*      */     
/*  324 */     a = HH(a, b, c, d, x[5], 4L, 4294588738L);
/*  325 */     d = HH(d, a, b, c, x[8], 11L, 2272392833L);
/*  326 */     c = HH(c, d, a, b, x[11], 16L, 1839030562L);
/*  327 */     b = HH(b, c, d, a, x[14], 23L, 4259657740L);
/*  328 */     a = HH(a, b, c, d, x[1], 4L, 2763975236L);
/*  329 */     d = HH(d, a, b, c, x[4], 11L, 1272893353L);
/*  330 */     c = HH(c, d, a, b, x[7], 16L, 4139469664L);
/*  331 */     b = HH(b, c, d, a, x[10], 23L, 3200236656L);
/*  332 */     a = HH(a, b, c, d, x[13], 4L, 681279174L);
/*  333 */     d = HH(d, a, b, c, x[0], 11L, 3936430074L);
/*  334 */     c = HH(c, d, a, b, x[3], 16L, 3572445317L);
/*  335 */     b = HH(b, c, d, a, x[6], 23L, 76029189L);
/*  336 */     a = HH(a, b, c, d, x[9], 4L, 3654602809L);
/*  337 */     d = HH(d, a, b, c, x[12], 11L, 3873151461L);
/*  338 */     c = HH(c, d, a, b, x[15], 16L, 530742520L);
/*  339 */     b = HH(b, c, d, a, x[2], 23L, 3299628645L);
/*      */ 
/*      */     
/*  342 */     a = II(a, b, c, d, x[0], 6L, 4096336452L);
/*  343 */     d = II(d, a, b, c, x[7], 10L, 1126891415L);
/*  344 */     c = II(c, d, a, b, x[14], 15L, 2878612391L);
/*  345 */     b = II(b, c, d, a, x[5], 21L, 4237533241L);
/*  346 */     a = II(a, b, c, d, x[12], 6L, 1700485571L);
/*  347 */     d = II(d, a, b, c, x[3], 10L, 2399980690L);
/*  348 */     c = II(c, d, a, b, x[10], 15L, 4293915773L);
/*  349 */     b = II(b, c, d, a, x[1], 21L, 2240044497L);
/*  350 */     a = II(a, b, c, d, x[8], 6L, 1873313359L);
/*  351 */     d = II(d, a, b, c, x[15], 10L, 4264355552L);
/*  352 */     c = II(c, d, a, b, x[6], 15L, 2734768916L);
/*  353 */     b = II(b, c, d, a, x[13], 21L, 1309151649L);
/*  354 */     a = II(a, b, c, d, x[4], 6L, 4149444226L);
/*  355 */     d = II(d, a, b, c, x[11], 10L, 3174756917L);
/*  356 */     c = II(c, d, a, b, x[2], 15L, 718787259L);
/*  357 */     b = II(b, c, d, a, x[9], 21L, 3951481745L);
/*      */     
/*  359 */     this.state[0] = this.state[0] + a;
/*  360 */     this.state[1] = this.state[1] + b;
/*  361 */     this.state[2] = this.state[2] + c;
/*  362 */     this.state[3] = this.state[3] + d;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void Encode(byte[] output, long[] input, int len) {
/*  372 */     for (int i = 0, j = 0; j < len; i++, j += 4) {
/*  373 */       output[j] = (byte)(int)(input[i] & 0xFFL);
/*  374 */       output[j + 1] = (byte)(int)(input[i] >>> 8L & 0xFFL);
/*  375 */       output[j + 2] = (byte)(int)(input[i] >>> 16L & 0xFFL);
/*  376 */       output[j + 3] = (byte)(int)(input[i] >>> 24L & 0xFFL);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void Decode(long[] output, byte[] input, int len) {
/*  387 */     for (int i = 0, j = 0; j < len; i++, j += 4) {
/*  388 */       output[i] = b2iu(input[j]) | b2iu(input[j + 1]) << 8L | 
/*  389 */         b2iu(input[j + 2]) << 16L | b2iu(input[j + 3]) << 24L;
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static long b2iu(byte b) {
/*  398 */     return ((b < 0) ? (b & 0xFF) : b);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static String byteHEX(byte ib) {
/*  406 */     char[] Digit = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 
/*  407 */         'b', 'c', 'd', 'e', 'f' };
/*  408 */     char[] ob = new char[2];
/*  409 */     ob[0] = Digit[ib >>> 4 & 0xF];
/*  410 */     ob[1] = Digit[ib & 0xF];
/*  411 */     String s = new String(ob);
/*  412 */     return s;
/*      */   }
/*      */   
/*      */   private String MD5Stream(byte[] Value) {
/*  416 */     md5Init();
/*  417 */     md5Update(Value, Value.length);
/*  418 */     md5Final();
/*  419 */     this.digestHexStr = "";
/*  420 */     for (int i = 0; i < 16; i++) {
/*  421 */       this.digestHexStr = String.valueOf(this.digestHexStr) + byteHEX(this.digest[i]);
/*      */     }
/*  423 */     return this.digestHexStr;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public iMsgServer2000() {
/*  452 */     this.FMsgText = "";
/*  453 */     this.FError = "";
/*  454 */     this.FVersion = "DBSTEP V3.0";
/*      */     
/*      */     try {
/*  457 */       this.FMsgFile = File.createTempFile("~GG", ".tmp");
/*  458 */       this.FTempName = this.FMsgFile.getName();
/*      */     }
/*  460 */     catch (Exception e) {
/*  461 */       this.FError = String.valueOf(this.FError) + e.toString();
/*  462 */       System.out.println(e.toString());
/*      */     } 
/*      */     
/*  465 */     this.FMsgFile.deleteOnExit();
/*      */   }
/*      */   
/*      */   protected void finalize() {
/*      */     try {
/*  470 */       if (this.FTempName.matches(this.FMsgFile.getName())) {
/*  471 */         this.FMsgFile.delete();
/*      */       }
/*  473 */     } catch (Exception e) {
/*  474 */       this.FError = String.valueOf(this.FError) + e.toString();
/*  475 */       System.out.println(e.toString());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected String FormatHead(String vString) {
/*  483 */     if (vString.length() > 16) {
/*  484 */       return vString.substring(0, 16);
/*      */     }
/*  486 */     for (int i = vString.length() + 1; i < 17; i++) {
/*  487 */       vString = vString.concat(" ");
/*      */     }
/*  489 */     return vString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private byte[] MsgToStream(byte[] mStream) {
/*  497 */     int HeadSize = 64;
/*  498 */     int BodySize = 0;
/*  499 */     int ErrorSize = 0;
/*  500 */     long FileSize = 0L;
/*  501 */     long Position = 0L;
/*      */ 
/*      */     
/*  504 */     int BlockSize = 1024 * this.BuffSize;
/*  505 */     byte[] BlockBuf = new byte[BlockSize];
/*  506 */     ByteArrayOutputStream mWite = new ByteArrayOutputStream();
/*      */     
/*  508 */     int CurSize = 0;
/*  509 */     int CurRead = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/*  516 */       BodySize = (this.FMsgText.getBytes(this.Charset)).length;
/*  517 */       ErrorSize = (this.FError.getBytes(this.Charset)).length;
/*  518 */       FileSize = this.FMsgFile.length();
/*  519 */       this.FFileSize = FileSize;
/*  520 */       if (FileSize > 0L && 
/*  521 */         !this.CryptFile) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  547 */         FileInputStream mRead = new FileInputStream(this.FMsgFile);
/*      */         
/*  549 */         CurSize = BlockSize;
/*  550 */         while (FileSize > 0L) {
/*  551 */           if (FileSize - BlockSize < BlockSize) {
/*  552 */             CurSize = (int)FileSize;
/*  553 */             BlockBuf = new byte[CurSize];
/*      */           } 
/*      */ 
/*      */           
/*  557 */           CurRead = 0;
/*  558 */           while (CurRead < CurSize) {
/*  559 */             CurRead += mRead
/*  560 */               .read(BlockBuf, CurRead, CurSize - CurRead);
/*      */           }
/*      */ 
/*      */           
/*  564 */           mWite.write(BlockBuf, 0, CurSize);
/*  565 */           FileSize -= CurSize;
/*      */         } 
/*  567 */         this.FFileSize = mWite.size();
/*  568 */         mRead.close();
/*  569 */         mWite.close();
/*      */       } 
/*      */ 
/*      */       
/*  573 */       ByteArrayOutputStream mBuffer = new ByteArrayOutputStream();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  581 */       String HeadString = String.valueOf(FormatHead(this.FVersion)) + 
/*  582 */         FormatHead(String.valueOf(BodySize)) + 
/*  583 */         FormatHead(String.valueOf(ErrorSize)) + 
/*  584 */         FormatHead(String.valueOf(this.FFileSize));
/*  585 */       mBuffer.write(HeadString.getBytes(), 0, HeadSize);
/*  586 */       Position += HeadSize;
/*      */       
/*  588 */       if (BodySize > 0) {
/*  589 */         mBuffer.write(this.FMsgText.getBytes());
/*      */       }
/*      */       
/*  592 */       Position += BodySize;
/*      */       
/*  594 */       if (ErrorSize > 0) {
/*  595 */         mBuffer.write(this.FError.getBytes(this.Charset));
/*      */       }
/*      */ 
/*      */       
/*  599 */       Position += ErrorSize;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  605 */       if (this.FFileSize > 0L)
/*      */       {
/*      */         
/*  608 */         mBuffer.write(mWite.toByteArray());
/*      */       }
/*      */ 
/*      */ 
/*      */       
/*  613 */       mBuffer.close();
/*      */ 
/*      */       
/*  616 */       mStream = mBuffer.toByteArray();
/*      */       
/*  618 */       return mStream;
/*  619 */     } catch (Exception e) {
/*  620 */       this.FError = String.valueOf(this.FError) + e.toString();
/*  621 */       System.out.println(e.toString());
/*  622 */       return null;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean MsgToStream(HttpServletResponse response) {
/*  630 */     int HeadSize = 64;
/*  631 */     int BodySize = 0;
/*  632 */     int ErrorSize = 0;
/*  633 */     long FileSize = 0L;
/*  634 */     long Position = 0L;
/*  635 */     int BlockSize = 1024 * this.BuffSize;
/*  636 */     byte[] BlockBuf = new byte[BlockSize];
/*  637 */     ByteArrayOutputStream mWite = new ByteArrayOutputStream();
/*      */     
/*  639 */     int CurSize = 0;
/*  640 */     int CurRead = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/*  646 */       ServletOutputStream out = response.getOutputStream();
/*      */       
/*  648 */       BodySize = (this.FMsgText.getBytes(this.Charset)).length;
/*  649 */       ErrorSize = (this.FError.getBytes(this.Charset)).length;
/*  650 */       FileSize = this.FMsgFile.length();
/*  651 */       this.FFileSize = FileSize;
/*  652 */       if (FileSize > 0L && 
/*  653 */         !this.CryptFile) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  681 */         FileInputStream mRead = new FileInputStream(this.FMsgFile);
/*      */         
/*  683 */         CurSize = BlockSize;
/*  684 */         while (FileSize > 0L) {
/*  685 */           if (FileSize - BlockSize < BlockSize) {
/*  686 */             CurSize = (int)FileSize;
/*  687 */             BlockBuf = new byte[CurSize];
/*      */           } 
/*      */ 
/*      */           
/*  691 */           CurRead = 0;
/*  692 */           while (CurRead < CurSize) {
/*  693 */             CurRead += mRead
/*  694 */               .read(BlockBuf, CurRead, CurSize - CurRead);
/*      */           }
/*      */ 
/*      */           
/*  698 */           mWite.write(BlockBuf, 0, CurSize);
/*  699 */           FileSize -= CurSize;
/*      */         } 
/*  701 */         this.FFileSize = mWite.size();
/*  702 */         mRead.close();
/*  703 */         mWite.close();
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  711 */       String HeadString = String.valueOf(FormatHead(this.FVersion)) + 
/*  712 */         FormatHead(String.valueOf(BodySize)) + 
/*  713 */         FormatHead(String.valueOf(ErrorSize)) + 
/*  714 */         FormatHead(String.valueOf(this.FFileSize));
/*  715 */       out.write(HeadString.getBytes());
/*      */       
/*  717 */       Position += HeadSize;
/*      */       
/*  719 */       if (BodySize > 0) {
/*  720 */         out.write(this.FMsgText.getBytes());
/*      */       }
/*      */ 
/*      */       
/*  724 */       Position += BodySize;
/*      */       
/*  726 */       if (ErrorSize > 0) {
/*  727 */         out.write(this.FError.getBytes(this.Charset));
/*      */       }
/*      */ 
/*      */       
/*  731 */       Position += ErrorSize;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  737 */       if (this.FFileSize > 0L) {
/*  738 */         out.write(mWite.toByteArray());
/*      */       }
/*      */       
/*  741 */       out.flush();
/*  742 */       return true;
/*  743 */     } catch (Exception e) {
/*  744 */       this.FError = String.valueOf(this.FError) + e.toString();
/*  745 */       System.out.println(e.toString());
/*  746 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public byte[] MsgVariant() {
/*  753 */     byte[] mStream = (byte[])null;
/*  754 */     return MsgToStream(mStream);
/*      */   }
/*      */ 
/*      */   
/*      */   private static int byteToInt(byte[] b) {
/*  759 */     int s = 0;
/*  760 */     for (int i = 3; i > 0; i--) {
/*  761 */       if (b[i] >= 0) {
/*  762 */         s += b[i];
/*      */       } else {
/*  764 */         s = s + 256 + b[i];
/*      */       } 
/*  766 */       s *= 256;
/*      */     } 
/*  768 */     if (b[0] >= 0) {
/*  769 */       s += b[0];
/*      */     } else {
/*  771 */       s = s + 256 + b[0];
/*      */     } 
/*  773 */     return s;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public byte[] ToDocument(byte[] Value) {
/*  779 */     byte[] mIntBuf = new byte[4];
/*  780 */     byte[] mFlagBuf = { 68, 73, 82, 71 };
/*  781 */     byte[] mOutBuf = (byte[])null;
/*  782 */     int HeadFlag = 0;
/*  783 */     int Signature = 0;
/*  784 */     int WordSize = 0;
/*  785 */     int PageSize = 0;
/*  786 */     int FlagSize = 0;
/*      */ 
/*      */     
/*      */     try {
/*  790 */       HeadFlag = byteToInt(mFlagBuf);
/*      */       
/*  792 */       ByteArrayInputStream mStream = new ByteArrayInputStream(Value);
/*      */       
/*  794 */       mStream.read(mIntBuf, 0, 4);
/*  795 */       Signature = byteToInt(mIntBuf);
/*      */       
/*  797 */       mStream.read(mIntBuf, 0, 4);
/*  798 */       WordSize = byteToInt(mIntBuf);
/*  799 */       mStream.read(mIntBuf, 0, 4);
/*  800 */       PageSize = byteToInt(mIntBuf);
/*  801 */       mStream.read(mIntBuf, 0, 4);
/*  802 */       FlagSize = byteToInt(mIntBuf);
/*      */       
/*  804 */       if (Signature != HeadFlag) {
/*  805 */         mStream.reset();
/*  806 */         WordSize = mStream.available();
/*      */       } 
/*  808 */       mOutBuf = new byte[WordSize];
/*  809 */       mStream.read(mOutBuf, 0, WordSize);
/*      */       
/*  811 */       return mOutBuf;
/*  812 */     } catch (Exception e) {
/*  813 */       this.FError = String.valueOf(this.FError) + e.toString();
/*  814 */       System.out.println(e.toString());
/*  815 */       return mOutBuf;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean StreamToMsg(byte[] mStream) {
/*  866 */     int HeadSize = 64;
/*  867 */     int BodySize = 0;
/*  868 */     int ErrorSize = 0;
/*  869 */     long FileSize = 0L;
/*  870 */     long Position = 0L;
/*  871 */     int CurRead = 0;
/*  872 */     String Md5Value = "";
/*  873 */     String Md5Calcu = "";
/*      */ 
/*      */     
/*      */     try {
/*  877 */       Position = 0L;
/*  878 */       String HeadString = new String(mStream, (int)Position, HeadSize);
/*  879 */       this.FVersion = HeadString.substring(0, 15);
/*  880 */       BodySize = Integer.parseInt(HeadString.substring(16, 31).trim());
/*  881 */       ErrorSize = Integer.parseInt(HeadString.substring(32, 47).trim());
/*  882 */       FileSize = Integer.parseInt(HeadString.substring(48, 63).trim());
/*  883 */       this.FFileSize = FileSize;
/*      */       
/*  885 */       Position += HeadSize;
/*  886 */       if (BodySize > 0) {
/*  887 */         this.FMsgText = new String(mStream, (int)Position, BodySize);
/*      */       }
/*      */       
/*  890 */       Position += BodySize;
/*  891 */       if (ErrorSize > 0) {
/*  892 */         this.FError = new String(mStream, (int)Position, ErrorSize);
/*      */       }
/*      */       
/*  895 */       Position += ErrorSize;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  911 */       if (FileSize > 0L) {
/*  912 */         if (!this.CryptFile) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  937 */           ByteArrayInputStream mRead = new ByteArrayInputStream(
/*  938 */               mStream, (int)Position, (int)FileSize);
/*  939 */           FileOutputStream mWite = new FileOutputStream(this.FMsgFile);
/*      */           
/*  941 */           int BlockSize = 1024 * this.BuffSize;
/*  942 */           int CurSize = 0;
/*      */           
/*  944 */           byte[] BlockBuf = new byte[BlockSize];
/*      */           
/*  946 */           CurSize = BlockSize;
/*  947 */           while (FileSize > 0L) {
/*  948 */             if (FileSize < BlockSize) {
/*  949 */               CurSize = (int)FileSize;
/*  950 */               BlockBuf = new byte[CurSize];
/*      */             } 
/*      */ 
/*      */             
/*  954 */             CurRead = 0;
/*  955 */             while (CurRead < CurSize) {
/*  956 */               CurRead += mRead
/*  957 */                 .read(BlockBuf, CurRead, CurSize - CurRead);
/*      */             }
/*      */ 
/*      */             
/*  961 */             mWite.write(BlockBuf, 0, CurSize);
/*  962 */             FileSize -= CurSize;
/*      */           } 
/*      */ 
/*      */           
/*  966 */           mWite.close();
/*  967 */           mRead.close();
/*      */         } 
/*      */         
/*  970 */         this.FFileSize = (int)this.FMsgFile.length();
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  976 */       return true;
/*  977 */     } catch (Exception e) {
/*  978 */       this.FError = String.valueOf(this.FError) + e.toString();
/*  979 */       System.out.println(e.toString());
/*  980 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean StreamToMsg(HttpServletRequest request) {
/*  989 */     int HeadSize = 64;
/*  990 */     int BodySize = 0;
/*  991 */     int ErrorSize = 0;
/*  992 */     long FileSize = 0L;
/*  993 */     int CurRead = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/* 1003 */       ServletInputStream mRead = request.getInputStream();
/* 1004 */       byte[] BlockBuf = new byte[HeadSize];
/* 1005 */       mRead.read(BlockBuf, 0, HeadSize);
/*      */       
/* 1007 */       String HeadString = new String(BlockBuf, 0, HeadSize);
/*      */       
/* 1009 */       this.FVersion = HeadString.substring(0, 15);
/* 1010 */       BodySize = Integer.parseInt(HeadString.substring(16, 31).trim());
/* 1011 */       ErrorSize = Integer.parseInt(HeadString.substring(32, 47).trim());
/* 1012 */       this.FFileSize = Integer.parseInt(HeadString.substring(48, 63).trim());
/* 1013 */       FileSize = this.FFileSize;
/*      */ 
/*      */       
/* 1016 */       if (BodySize > 0) {
/* 1017 */         BlockBuf = new byte[BodySize];
/* 1018 */         CurRead = 0;
/* 1019 */         while (CurRead < BodySize) {
/* 1020 */           CurRead += mRead
/* 1021 */             .read(BlockBuf, CurRead, BodySize - CurRead);
/*      */         }
/*      */ 
/*      */ 
/*      */         
/* 1026 */         this.FMsgText = new String(BlockBuf, 0, BodySize);
/*      */       } 
/*      */ 
/*      */       
/* 1030 */       if (ErrorSize > 0) {
/* 1031 */         BlockBuf = new byte[ErrorSize];
/* 1032 */         mRead.read(BlockBuf, 0, ErrorSize);
/* 1033 */         this.FError = new String(BlockBuf, 0, ErrorSize);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1038 */       if (FileSize > 0L) {
/* 1039 */         if (!this.CryptFile) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1063 */           int BlockSize = 1024 * this.BuffSize;
/* 1064 */           int CurSize = 0;
/* 1065 */           FileOutputStream mWite = new FileOutputStream(this.FMsgFile);
/*      */ 
/*      */ 
/*      */           
/* 1069 */           BlockBuf = new byte[BlockSize];
/*      */           
/* 1071 */           CurSize = BlockSize;
/* 1072 */           while (FileSize > 0L) {
/* 1073 */             if (FileSize < BlockSize) {
/* 1074 */               CurSize = (int)FileSize;
/* 1075 */               BlockBuf = new byte[CurSize];
/*      */             } 
/*      */ 
/*      */             
/* 1079 */             CurRead = 0;
/* 1080 */             while (CurRead < CurSize) {
/* 1081 */               CurRead += mRead
/* 1082 */                 .read(BlockBuf, CurRead, CurSize - CurRead);
/*      */             }
/*      */ 
/*      */ 
/*      */             
/* 1087 */             mWite.write(BlockBuf, 0, CurSize);
/* 1088 */             FileSize -= CurSize;
/*      */           } 
/*      */ 
/*      */           
/* 1092 */           mWite.close();
/* 1093 */           mRead.close();
/*      */         } 
/*      */         
/* 1096 */         this.FFileSize = (int)this.FMsgFile.length();
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1102 */       return true;
/* 1103 */     } catch (Exception e) {
/* 1104 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1105 */       System.out.println(e.toString());
/* 1106 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void MsgVariant(byte[] mStream) {
/* 1113 */     StreamToMsg(mStream);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean SavePackage(String FileName, ServletRequest request) {
/* 1120 */     byte[] BlockBuf = new byte[1024 * this.BuffSize];
/*      */     try {
/* 1122 */       int mLength = request.getContentLength();
/* 1123 */       int mIndex = 0;
/* 1124 */       ServletInputStream mRead = request.getInputStream();
/* 1125 */       FileOutputStream mFile = new FileOutputStream(FileName);
/* 1126 */       while (mLength > 0) {
/* 1127 */         mIndex = mRead.read(BlockBuf);
/* 1128 */         mFile.write(BlockBuf, 0, mIndex);
/* 1129 */         mLength -= mIndex;
/*      */       } 
/* 1131 */       mFile.close();
/* 1132 */       return true;
/* 1133 */     } catch (Exception e) {
/* 1134 */       e.printStackTrace();
/* 1135 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean MsgFileSave(String FileName) {
/* 1145 */     long FileSize = 0L;
/*      */     
/* 1147 */     int BlockSize = 1024 * this.BuffSize;
/* 1148 */     FileSize = this.FMsgFile.length();
/*      */     try {
/* 1150 */       FileOutputStream mFile = new FileOutputStream(FileName);
/* 1151 */       FileInputStream mThis = new FileInputStream(this.FMsgFile);
/* 1152 */       byte[] BlockBuf = new byte[BlockSize];
/*      */       
/* 1154 */       while (FileSize > 0L) {
/* 1155 */         int CurSize = mThis.read(BlockBuf, 0, BlockSize);
/*      */         
/* 1157 */         mFile.write(BlockBuf, 0, CurSize);
/* 1158 */         FileSize -= CurSize;
/*      */       } 
/* 1160 */       mThis.close();
/* 1161 */       mFile.close();
/* 1162 */       return true;
/* 1163 */     } catch (Exception e) {
/* 1164 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1165 */       System.out.println(e.toString());
/* 1166 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean MsgFileLoad(String FileName) {
/*      */     try {
/* 1174 */       if (this.FTempName.matches(this.FMsgFile.getName())) {
/* 1175 */         this.FMsgFile.delete();
/*      */       }
/* 1177 */       this.FMsgFile = new File(FileName);
/* 1178 */       if (this.FMsgFile.exists()) {
/* 1179 */         this.FFileSize = this.FMsgFile.length();
/* 1180 */         return true;
/*      */       } 
/* 1182 */       return false;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/* 1196 */     catch (Exception e) {
/* 1197 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1198 */       System.out.println(e.toString());
/* 1199 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public long FileSizeByName(String FileName) {
/*      */     try {
/* 1207 */       File mFile = new File(FileName);
/* 1208 */       return mFile.length();
/*      */     }
/* 1210 */     catch (Exception e) {
/* 1211 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1212 */       System.out.println(e.toString());
/* 1213 */       return 0L;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String MsgTextBody() {
/* 1221 */     return this.FMsgText;
/*      */   }
/*      */ 
/*      */   
/*      */   public byte[] MsgFileBody() {
/* 1226 */     byte[] mBuffer = new byte[(int)this.FFileSize];
/*      */     try {
/* 1228 */       FileInputStream mFile = new FileInputStream(this.FMsgFile);
/* 1229 */       mFile.read(mBuffer, 0, (int)this.FFileSize);
/* 1230 */       mFile.close();
/* 1231 */     } catch (Exception e) {
/* 1232 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1233 */       System.out.println(e.toString());
/*      */     } 
/* 1235 */     return mBuffer;
/*      */   }
/*      */   
/*      */   public String MsgError() {
/* 1239 */     return this.FError;
/*      */   }
/*      */   
/*      */   public String MsgVersion() {
/* 1243 */     return this.FVersion;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void MsgTextBody(String Value) {
/* 1250 */     this.FMsgText = Value;
/*      */   }
/*      */ 
/*      */   
/*      */   public void MsgFileBody(byte[] Value) {
/* 1255 */     MsgFileClear();
/*      */     try {
/* 1257 */       FileOutputStream mFile = new FileOutputStream(this.FMsgFile);
/* 1258 */       mFile.write(Value);
/* 1259 */       mFile.close();
/* 1260 */     } catch (Exception e) {
/* 1261 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1262 */       System.out.println(this.FError);
/*      */     } 
/* 1264 */     this.FFileSize = Value.length;
/*      */   }
/*      */   
/*      */   public void MsgError(String Value) {
/* 1268 */     this.FError = Value;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int MsgFileSize() {
/* 1275 */     return (int)this.FFileSize;
/*      */   }
/*      */   
/*      */   public void MsgFileSize(int value) {
/* 1279 */     this.FFileSize = value;
/*      */   }
/*      */   
/*      */   public void MsgFileClear() {
/* 1283 */     this.FFileSize = 0L;
/* 1284 */     if (this.FTempName.matches(this.FMsgFile.getName())) {
/* 1285 */       this.FMsgFile.delete();
/*      */     }
/*      */     try {
/* 1288 */       this.FMsgFile = File.createTempFile("~GG", ".tmp");
/* 1289 */       this.FTempName = this.FMsgFile.getName();
/* 1290 */     } catch (Exception e) {
/* 1291 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1292 */       System.out.println(e.toString());
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void MsgTextClear() {
/* 1298 */     this.FMsgText = "";
/*      */   }
/*      */   
/*      */   public void MsgErrorClear() {
/* 1302 */     this.FError = "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String DecodeBase64(String Value) {
/* 1358 */     ByteArrayOutputStream o = new ByteArrayOutputStream();
/* 1359 */     String m = "";
/*      */     
/* 1361 */     byte[] d = new byte[4];
/*      */     try {
/* 1363 */       int count = 0;
/* 1364 */       byte[] x = Value.getBytes();
/* 1365 */       while (count < x.length) {
/* 1366 */         for (int n = 0; n <= 3; n++) {
/* 1367 */           if (count >= x.length) {
/* 1368 */             d[n] = 64;
/*      */           }
/*      */           else {
/*      */             
/* 1372 */             int y = this.TableBase64.indexOf(x[count]);
/* 1373 */             if (y < 0) {
/* 1374 */               y = 65;
/*      */             }
/* 1376 */             d[n] = (byte)y;
/*      */           } 
/* 1378 */           count++;
/*      */         } 
/* 1380 */         o.write((byte)(((d[0] & 0x3F) << 2) + ((d[1] & 0x30) >> 4)));
/* 1381 */         if (d[2] != 64) {
/* 1382 */           o.write((byte)(((d[1] & 0xF) << 4) + ((d[2] & 0x3C) >> 2)));
/* 1383 */           if (d[3] != 64) {
/* 1384 */             o.write((byte)(((d[2] & 0x3) << 6) + (d[3] & 0x3F)));
/*      */           }
/*      */         } 
/*      */       } 
/* 1388 */     } catch (StringIndexOutOfBoundsException e) {
/*      */       
/* 1390 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1391 */       System.out.println(e.toString());
/*      */     } 
/*      */ 
/*      */     
/*      */     try {
/* 1396 */       m = o.toString(this.Charset);
/*      */     }
/* 1398 */     catch (UnsupportedEncodingException ea) {
/* 1399 */       System.out.println(ea.toString());
/*      */     } 
/*      */     
/* 1402 */     return m;
/*      */   }
/*      */   
/*      */   public String EncodeBase64(String Value) {
/* 1406 */     ByteArrayOutputStream o = new ByteArrayOutputStream();
/*      */ 
/*      */ 
/*      */     
/* 1410 */     byte[] d = new byte[4];
/*      */     try {
/* 1412 */       int count = 0;
/*      */       
/* 1414 */       byte[] x = Value.getBytes(this.Charset);
/* 1415 */       while (count < x.length) {
/* 1416 */         byte c = x[count];
/* 1417 */         count++;
/* 1418 */         d[0] = (byte)((c & 0xFC) >> 2);
/* 1419 */         d[1] = (byte)((c & 0x3) << 4);
/* 1420 */         if (count < x.length) {
/* 1421 */           c = x[count];
/* 1422 */           count++;
/* 1423 */           d[1] = (byte)(d[1] + (byte)((c & 0xF0) >> 4));
/* 1424 */           d[2] = (byte)((c & 0xF) << 2);
/* 1425 */           if (count < x.length) {
/* 1426 */             c = x[count];
/* 1427 */             count++;
/* 1428 */             d[2] = (byte)(d[2] + ((c & 0xC0) >> 6));
/* 1429 */             d[3] = (byte)(c & 0x3F);
/*      */           } else {
/* 1431 */             d[3] = 64;
/*      */           } 
/*      */         } else {
/* 1434 */           d[2] = 64;
/* 1435 */           d[3] = 64;
/*      */         } 
/* 1437 */         for (int n = 0; n <= 3; n++) {
/* 1438 */           o.write(this.TableBase64.charAt(d[n]));
/*      */         }
/*      */       } 
/* 1441 */     } catch (StringIndexOutOfBoundsException e) {
/* 1442 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1443 */       System.out.println(e.toString());
/* 1444 */     } catch (UnsupportedEncodingException ea) {
/* 1445 */       System.out.println(ea.toString());
/*      */     } 
/* 1447 */     return o.toString();
/*      */   }
/*      */   
/*      */   public int GetFieldCount() {
/* 1451 */     int i = 0;
/* 1452 */     int j = 0;
/* 1453 */     i = this.FMsgText.indexOf("\r\n", i + 1);
/* 1454 */     while (i != -1) {
/* 1455 */       j++;
/* 1456 */       i = this.FMsgText.indexOf("\r\n", i + 1);
/*      */     } 
/* 1458 */     return j;
/*      */   }
/*      */   
/*      */   public String GetFieldName(int Index) {
/* 1462 */     int i = 0;
/* 1463 */     int j = 0;
/* 1464 */     int k = 0;
/* 1465 */     int n = 0;
/* 1466 */     String mFieldString = "";
/* 1467 */     String mFieldName = "";
/* 1468 */     String mReturn = "";
/*      */ 
/*      */     
/* 1471 */     while (i != -1 && j < Index) {
/* 1472 */       i = this.FMsgText.indexOf("\r\n", i + 1);
/* 1473 */       if (i != -1) {
/* 1474 */         j++;
/*      */       }
/*      */     } 
/* 1477 */     k = this.FMsgText.indexOf("\r\n", i + 1);
/* 1478 */     if (i != -1 && k != -1) {
/* 1479 */       if (i == 0) {
/* 1480 */         mFieldString = this.FMsgText.substring(i, k);
/*      */       } else {
/* 1482 */         mFieldString = this.FMsgText.substring(i + 2, k);
/*      */       } 
/* 1484 */       n = mFieldString.indexOf("=", 0);
/* 1485 */       if (n != -1) {
/* 1486 */         mFieldName = mFieldString.substring(0, n);
/* 1487 */         mReturn = mFieldName;
/*      */       } 
/*      */     } 
/* 1490 */     return mReturn;
/*      */   }
/*      */   
/*      */   public String GetFieldValue(int Index) {
/* 1494 */     int i = 0;
/* 1495 */     int j = 0;
/* 1496 */     int k = 0;
/* 1497 */     int n = 0;
/* 1498 */     String mFieldString = "";
/* 1499 */     String mFieldValue = "";
/* 1500 */     String mReturn = "";
/* 1501 */     while (i != -1 && j < Index) {
/* 1502 */       i = this.FMsgText.indexOf("\r\n", i + 1);
/* 1503 */       if (i != -1) {
/* 1504 */         j++;
/*      */       }
/*      */     } 
/* 1507 */     k = this.FMsgText.indexOf("\r\n", i + 1);
/*      */     
/* 1509 */     if (i != -1 && k != -1) {
/* 1510 */       if (i == 0) {
/* 1511 */         mFieldString = this.FMsgText.substring(i, k);
/*      */       } else {
/* 1513 */         mFieldString = this.FMsgText.substring(i + 2, k);
/*      */       } 
/* 1515 */       n = mFieldString.indexOf("=", 0);
/* 1516 */       if (n != -1) {
/* 1517 */         mFieldValue = mFieldString.substring(n + 1, mFieldString
/* 1518 */             .length());
/* 1519 */         mReturn = DecodeBase64(mFieldValue);
/*      */       } 
/*      */     } 
/* 1522 */     return mReturn;
/*      */   }
/*      */   
/*      */   public String GetFieldText() {
/* 1526 */     return this.FMsgText.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String GetMsgByName(String FieldName) {
/* 1532 */     int i = 0;
/* 1533 */     int j = 0;
/* 1534 */     String mReturn = "";
/*      */     
/* 1536 */     String mFieldName = FieldName.trim().concat("=");
/* 1537 */     i = this.FMsgText.indexOf(mFieldName);
/* 1538 */     if (i != -1) {
/* 1539 */       j = this.FMsgText.indexOf("\r\n", i + 1);
/* 1540 */       i += mFieldName.length();
/* 1541 */       if (j != -1) {
/* 1542 */         String mFieldValue = this.FMsgText.substring(i, j);
/* 1543 */         mReturn = DecodeBase64(mFieldValue);
/* 1544 */         return mReturn;
/*      */       } 
/* 1546 */       return mReturn;
/*      */     } 
/*      */     
/* 1549 */     return mReturn;
/*      */   }
/*      */ 
/*      */   
/*      */   public void SetMsgByName(String FieldName, String FieldValue) {
/* 1554 */     String mFieldText = "";
/* 1555 */     String mFieldHead = "";
/* 1556 */     String mFieldNill = "";
/*      */     
/* 1558 */     int i = 0;
/* 1559 */     int j = 0;
/* 1560 */     boolean f = false;
/* 1561 */     String mFieldName = FieldName.trim().concat("=");
/* 1562 */     String mFieldValue = EncodeBase64(FieldValue);
/* 1563 */     mFieldText = String.valueOf(mFieldName) + mFieldValue + "\r\n";
/*      */ 
/*      */ 
/*      */     
/* 1567 */     String[] FT = this.FMsgText.replace("\r\n", "=").split("=");
/* 1568 */     for (int x = 0; x < FT.length; x++) {
/* 1569 */       String nft = FT[x];
/* 1570 */       if (nft.equals(FieldName)) {
/*      */         
/* 1572 */         i = this.FMsgText.indexOf(String.valueOf(FieldName) + "=");
/* 1573 */         if (i != -1) {
/* 1574 */           j = this.FMsgText.indexOf("\r\n", i + 1);
/* 1575 */           if (j != -1) {
/* 1576 */             mFieldHead = this.FMsgText.substring(0, i);
/* 1577 */             mFieldNill = this.FMsgText.substring(j + 2);
/* 1578 */             f = true;
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/* 1583 */     if (f) {
/* 1584 */       this.FMsgText = (new StringBuffer(String.valueOf(mFieldHead) + mFieldText + mFieldNill))
/* 1585 */         .toString();
/*      */     } else {
/* 1587 */       this.FMsgText = this.FMsgText.concat(mFieldText);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean MakeDirectory(String FilePath) {
/* 1594 */     File mFile = new File(FilePath);
/* 1595 */     mFile.mkdirs();
/* 1596 */     return mFile.isDirectory();
/*      */   }
/*      */   
/*      */   public boolean MKDirectory(String FilePath) {
/* 1600 */     File mFile = new File(FilePath);
/* 1601 */     mFile.mkdirs();
/* 1602 */     return mFile.isDirectory();
/*      */   }
/*      */   
/*      */   public boolean RMDirectory(String FilePath) {
/* 1606 */     File mFile = new File(FilePath);
/* 1607 */     if (mFile.isDirectory()) {
/* 1608 */       mFile.delete();
/*      */     }
/* 1610 */     return true;
/*      */   }
/*      */   
/*      */   public boolean DelFile(String FileName) {
/* 1614 */     File mFile = new File(FileName);
/* 1615 */     if (mFile.exists()) {
/* 1616 */       mFile.delete();
/*      */     }
/* 1618 */     return true;
/*      */   }
/*      */   
/*      */   public boolean DelTree(String FilePath) {
/* 1622 */     File mFile = new File(FilePath);
/* 1623 */     if (mFile.isDirectory()) {
/* 1624 */       mFile.delete();
/*      */     }
/* 1626 */     return true;
/*      */   }
/*      */   
/*      */   public int LoadFilePoint(String FileName) {
/* 1630 */     int i = 0;
/* 1631 */     int j = 0;
/* 1632 */     int mSize = 0;
/*      */     
/* 1634 */     String mText = "";
/* 1635 */     String mReturn = "-1";
/* 1636 */     String mFieldName = "INDEX=";
/*      */     
/*      */     try {
/* 1639 */       File mFile = new File(String.valueOf(FileName) + ".fp");
/* 1640 */       mSize = (int)mFile.length();
/* 1641 */       byte[] mBuffer = new byte[mSize];
/* 1642 */       FileInputStream mStream = new FileInputStream(mFile);
/* 1643 */       mStream.read(mBuffer, 0, mSize);
/* 1644 */       mStream.close();
/* 1645 */       mText = new String(mBuffer);
/* 1646 */     } catch (Exception e) {
/* 1647 */       this.FError = String.valueOf(this.FError) + e.toString();
/*      */       
/* 1649 */       return Integer.parseInt(mReturn);
/*      */     } 
/*      */     
/* 1652 */     i = mText.indexOf(mFieldName);
/* 1653 */     if (i != -1) {
/* 1654 */       j = mText.indexOf("\r\n", i + 1);
/* 1655 */       i += mFieldName.length();
/* 1656 */       if (j != -1) {
/* 1657 */         mReturn = mText.substring(i, j - i);
/*      */         
/* 1659 */         return Integer.parseInt(mReturn);
/*      */       } 
/* 1661 */       return Integer.parseInt(mReturn);
/*      */     } 
/*      */     
/* 1664 */     return Integer.parseInt(mReturn);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean SaveFilePoint(String FileName, int FCount) {
/* 1670 */     int i = 0;
/* 1671 */     int j = 0;
/* 1672 */     int mSize = 0;
/*      */     
/* 1674 */     String mFieldName = "INDEX=";
/* 1675 */     String mCount = "";
/*      */     try {
/* 1677 */       FileOutputStream mFile = new FileOutputStream(FileName);
/* 1678 */       mCount = String.valueOf(mFieldName) + FCount + "\r\n";
/* 1679 */       byte[] mBuffer = mCount.getBytes();
/* 1680 */       mSize = mBuffer.length;
/* 1681 */       mFile.write(mBuffer, 0, mSize);
/* 1682 */       mFile.close();
/* 1683 */       return true;
/* 1684 */     } catch (Exception e) {
/* 1685 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1686 */       System.out.println("SaveFilePoint:" + this.FError);
/* 1687 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean LoadStreamFromFile(String FileName, int Index) {
/* 1697 */     int mPosition = 0;
/* 1698 */     int mSize = 0;
/* 1699 */     int mLength = 0;
/*      */     
/*      */     try {
/* 1702 */       File mLocal = new File(FileName);
/* 1703 */       mSize = (int)mLocal.length();
/*      */       
/* 1705 */       FileInputStream mFile = new FileInputStream(mLocal);
/*      */       
/* 1707 */       FileOutputStream mThis = new FileOutputStream(this.FMsgFile);
/*      */       
/* 1709 */       mPosition = Index * this.BuffSize * 1024;
/* 1710 */       if (mPosition + this.BuffSize * 1024 < mSize) {
/* 1711 */         mLength = this.BuffSize * 1024;
/*      */       } else {
/* 1713 */         mLength = mSize - mPosition;
/*      */       } 
/*      */       
/* 1716 */       mFile.skip(mPosition);
/*      */       
/* 1718 */       byte[] mBuffer = new byte[mLength];
/* 1719 */       mFile.read(mBuffer);
/* 1720 */       mFile.close();
/* 1721 */       mThis.write(mBuffer);
/* 1722 */       mThis.close();
/* 1723 */     } catch (Exception e) {
/* 1724 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1725 */       System.out.println("LoadStreamFromFile:" + this.FError);
/* 1726 */       return false;
/*      */     } 
/* 1728 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean SaveStreamToFile(String FileName, int Index) {
/* 1743 */     if (Index == 0) {
/* 1744 */       DelFile(FileName);
/*      */     }
/*      */     
/*      */     try {
/* 1748 */       RandomAccessFile mFile = new RandomAccessFile(FileName, "rw");
/* 1749 */       FileInputStream mThis = new FileInputStream(this.FMsgFile);
/* 1750 */       byte[] mBuffer = new byte[(int)this.FFileSize];
/* 1751 */       mThis.read(mBuffer);
/* 1752 */       mThis.close();
/* 1753 */       mFile.seek(mFile.length());
/* 1754 */       mFile.write(mBuffer);
/* 1755 */       mFile.close();
/* 1756 */     } catch (Exception e) {
/* 1757 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1758 */       System.out.println("SaveStreamToFile:" + this.FError);
/* 1759 */       return false;
/*      */     } 
/* 1761 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean SaveFromStream(String FileName, int Index) {
/* 1776 */     if (Index == 0) {
/* 1777 */       DelFile(FileName);
/*      */     }
/*      */     
/*      */     try {
/* 1781 */       RandomAccessFile mFile = new RandomAccessFile(FileName, "rw");
/* 1782 */       FileInputStream mThis = new FileInputStream(this.FMsgFile);
/* 1783 */       byte[] mBuffer = new byte[(int)this.FFileSize];
/* 1784 */       mThis.read(mBuffer);
/* 1785 */       mThis.close();
/* 1786 */       mFile.seek(mFile.length());
/* 1787 */       mFile.write(mBuffer);
/* 1788 */       mFile.close();
/* 1789 */     } catch (Exception e) {
/* 1790 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1791 */       System.out.println("SaveFromStream:" + this.FError);
/* 1792 */       return false;
/*      */     } 
/* 1794 */     return true;
/*      */   }
/*      */   
/*      */   public boolean DecodeBase64ToFile(String Value, String FileName) {
/* 1798 */     ByteArrayOutputStream o = new ByteArrayOutputStream();
/*      */     
/* 1800 */     boolean mResult = false;
/*      */     
/* 1802 */     byte[] d = new byte[4];
/*      */     
/*      */     try {
/* 1805 */       int count = 0;
/* 1806 */       byte[] x = Value.getBytes();
/* 1807 */       while (count < x.length) {
/* 1808 */         for (int n = 0; n <= 3; n++) {
/* 1809 */           if (count >= x.length) {
/* 1810 */             d[n] = 64;
/*      */           } else {
/*      */             
/* 1813 */             int y = this.TableBase64.indexOf(x[count]);
/* 1814 */             if (y < 0) {
/* 1815 */               y = 65;
/*      */             }
/* 1817 */             d[n] = (byte)y;
/*      */           } 
/* 1819 */           count++;
/*      */         } 
/* 1821 */         o.write((byte)(((d[0] & 0x3F) << 2) + ((d[1] & 0x30) >> 4)));
/* 1822 */         if (d[2] != 64) {
/* 1823 */           o
/* 1824 */             .write((byte)(((d[1] & 0xF) << 4) + ((d[2] & 0x3C) >> 2)));
/* 1825 */           if (d[3] != 64) {
/* 1826 */             o
/* 1827 */               .write((byte)(((d[2] & 0x3) << 6) + (d[3] & 0x3F)));
/*      */           }
/*      */         } 
/*      */       } 
/* 1831 */       FileOutputStream mFile = new FileOutputStream(FileName);
/* 1832 */       byte[] mBuffer = o.toByteArray();
/* 1833 */       int mSize = mBuffer.length;
/* 1834 */       mFile.write(mBuffer, 0, mSize);
/* 1835 */       mFile.close();
/* 1836 */       mResult = true;
/* 1837 */     } catch (Exception e) {
/* 1838 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1839 */       mResult = false;
/* 1840 */       System.out.println(e.toString());
/*      */     } 
/* 1842 */     return mResult;
/*      */   }
/*      */   
/*      */   public boolean SaveFromFile(String FileName, int FileCount) {
/* 1846 */     int mIndex = 0;
/*      */     
/* 1848 */     String mPkName = "";
/*      */ 
/*      */ 
/*      */     
/* 1852 */     mPkName = String.valueOf(FileName) + ".fp";
/* 1853 */     DelFile(mPkName);
/*      */     try {
/* 1855 */       FileOutputStream mFile = new FileOutputStream(FileName);
/* 1856 */       for (mIndex = 0; mIndex <= FileCount; mIndex++) {
/* 1857 */         mPkName = String.valueOf(FileName) + "." + mIndex;
/* 1858 */         File nTemp = new File(mPkName);
/* 1859 */         FileInputStream mTemp = new FileInputStream(nTemp);
/* 1860 */         byte[] mBuffer = new byte[(int)nTemp.length()];
/* 1861 */         mTemp.read(mBuffer, 0, (int)nTemp.length());
/* 1862 */         mFile.write(mBuffer, 0, (int)nTemp.length());
/* 1863 */         mTemp.close();
/* 1864 */         nTemp.delete();
/*      */       } 
/*      */       
/* 1867 */       mFile.close();
/* 1868 */     } catch (Exception e) {
/* 1869 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1870 */       System.out.println("SaveFromFile:" + this.FError);
/* 1871 */       return false;
/*      */     } 
/* 1873 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void Load(HttpServletRequest request) {
/* 1880 */     this.Charset = request.getHeader("charset");
/* 1881 */     System.out.println("LoadCharset:" + this.Charset);
/* 1882 */     if (this.Charset == null) {
/* 1883 */       this.Charset = "GB2312";
/*      */     }
/* 1885 */     if (!StreamToMsg(request)) {
/* 1886 */       System.out.println("StreamToMsg Error");
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void Send(HttpServletResponse response) {
/* 1894 */     if (!MsgToStream(response)) {
/* 1895 */       System.out.println("MsgToStream Error");
/*      */     }
/*      */     
/*      */     try {
/* 1899 */       if (this.FTempName.matches(this.FMsgFile.getName())) {
/* 1900 */         this.FMsgFile.delete();
/*      */       }
/* 1902 */     } catch (Exception e) {
/* 1903 */       this.FError = String.valueOf(this.FError) + e.toString();
/* 1904 */       System.out.println(e.toString());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public byte[] ReadPackage(HttpServletRequest request) {
/* 1911 */     byte[] mStream = (byte[])null;
/* 1912 */     int totalRead = 0;
/* 1913 */     int readBytes = 0;
/* 1914 */     int totalBytes = 0;
/*      */     
/* 1916 */     this.Charset = request.getHeader("charset");
/* 1917 */     System.out.println("ReadPackageCharset:" + this.Charset);
/* 1918 */     if (this.Charset == null) {
/* 1919 */       this.Charset = "GB2312";
/*      */     }
/*      */     try {
/* 1922 */       totalBytes = request.getContentLength();
/* 1923 */       mStream = new byte[totalBytes];
/* 1924 */       while (totalRead < totalBytes) {
/*      */         
/* 1926 */         readBytes = request.getInputStream().read(mStream, totalRead, 
/* 1927 */             totalBytes - totalRead);
/* 1928 */         totalRead += readBytes;
/*      */       } 
/*      */ 
/*      */       
/* 1932 */       MsgVariant(mStream);
/*      */     }
/* 1934 */     catch (Exception e) {
/* 1935 */       System.out.println("ReadPackage:" + e.toString());
/*      */     } 
/* 1937 */     return mStream;
/*      */   }
/*      */ 
/*      */   
/*      */   public void SendPackage(HttpServletResponse response) {
/*      */     try {
/* 1943 */       ServletOutputStream OutBinarry = response.getOutputStream();
/* 1944 */       OutBinarry.write(MsgVariant());
/* 1945 */       OutBinarry.flush();
/* 1946 */       OutBinarry.close();
/* 1947 */     } catch (Exception e) {
/* 1948 */       System.out.println("SendPackage:" + e.toString());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String Version() {
/* 1991 */     return "8,8,0,32";
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static String Version(String SoftwareName) {
/* 1997 */     String mVersion = "0,0,0,0";
/* 1998 */     if (SoftwareName.equalsIgnoreCase("HandWrite") || 
/* 1999 */       SoftwareName.equalsIgnoreCase("")) {
/* 2000 */       mVersion = "4,0,0,8";
/*      */     }
/* 2002 */     if (SoftwareName.equalsIgnoreCase("iWebSignature")) {
/* 2003 */       mVersion = "5,8,0,0";
/*      */     }
/* 2005 */     return mVersion;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static String VersionEx() {
/* 2011 */     return "高级版本";
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static String VersionEx(String SoftwareName) {
/* 2017 */     String mVersionEx = "错误版本";
/* 2018 */     if (SoftwareName.equalsIgnoreCase("HandWrite") || 
/* 2019 */       SoftwareName.equalsIgnoreCase("")) {
/* 2020 */       mVersionEx = "高级版本";
/*      */     }
/* 2022 */     if (SoftwareName.equalsIgnoreCase("iWebSignature")) {
/* 2023 */       mVersionEx = "标准版本";
/*      */     }
/* 2025 */     return mVersionEx;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static String CopyRight(String SoftwareName) {
/* 2031 */     String mVersionEx = "错误版本";
/* 2032 */     if (SoftwareName.equalsIgnoreCase("HandWrite") || 
/* 2033 */       SoftwareName.equalsIgnoreCase("")) {
/* 2034 */       mVersionEx = "高级版本 江西金格科技股份有限公司，所权所有";
/*      */     }
/* 2036 */     if (SoftwareName.equalsIgnoreCase("iWebSignature")) {
/* 2037 */       mVersionEx = "标准版本 江西金格科技股份有限公司，所权所有";
/*      */     }
/* 2039 */     return mVersionEx;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String VersionDesc() {
/* 2047 */     String mVersionDesc = "www.kinggrid.com KingGrid-iMsgServer2000";
/* 2048 */     return mVersionDesc;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String ProdDesc() {
/* 2055 */     String mProdDescX = " 金格科技自主创新开发的实时安全传输技术——DBPacketTM通讯协议包，采用前端和后端方式设计，实现客户端与服务器端的数据安全可靠传递、交互。金格科技是专注于“可信应用软件”研究与开发的自主创新型高新技术企业，凭借完全自主创新的核心技术和强大的研发力量，为社会提供安全可靠的可信应用软件产品、技术和服务，日益成长为中国可信应用产业的积极推动者和可信应用软件领域的领军企业。";
/* 2056 */     String mProdDesc = "Kinggrid:iWebOffice2000/iWebOffice2003/iWebOffice2003/iWebOffice2009/iWebOffice2012/";
/* 2057 */     mProdDesc = String.valueOf(mProdDesc) + "iWebRevision/iWebPDF/iWebPicture/iWebFile2005/iWebBarcode/";
/* 2058 */     mProdDesc = String.valueOf(mProdDesc) + "iSignature/iSolutions";
/* 2059 */     return mProdDesc;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/DBstep/iMsgServer2000.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */