/*     */ package DBstep;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.security.MessageDigest;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MD5Util
/*     */ {
/*     */   public static String getMD5(InputStream paramInputStream) throws NoSuchAlgorithmException, IOException {
/*  24 */     StringBuffer stringBuffer = new StringBuffer();
/*  25 */     MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/*  26 */     byte[] arrayOfByte1 = new byte[1024];
/*     */     
/*  28 */     int i = 0;
/*  29 */     while ((i = paramInputStream.read(arrayOfByte1)) != -1) {
/*  30 */       messageDigest.update(arrayOfByte1, 0, i);
/*     */     }
/*  32 */     byte[] arrayOfByte2 = messageDigest.digest();
/*     */ 
/*     */     
/*  35 */     for (byte b = 0; b < arrayOfByte2.length; b++) {
/*  36 */       stringBuffer.append(Integer.toString((arrayOfByte2[b] & 0xFF) + 256, 16).substring(1));
/*     */     }
/*  38 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public static String getBodyMD5(byte[] paramArrayOfbyte) throws NoSuchAlgorithmException, IOException {
/*  42 */     StringBuffer stringBuffer = new StringBuffer();
/*  43 */     MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/*  44 */     int i = paramArrayOfbyte.length;
/*  45 */     messageDigest.update(paramArrayOfbyte, 0, i);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  54 */     byte[] arrayOfByte = messageDigest.digest();
/*     */ 
/*     */     
/*  57 */     for (byte b = 0; b < arrayOfByte.length; b++) {
/*  58 */       stringBuffer.append(Integer.toString((arrayOfByte[b] & 0xFF) + 256, 16).substring(1));
/*     */     }
/*  60 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getMD5(File paramFile) throws NoSuchAlgorithmException, IOException {
/*  72 */     FileInputStream fileInputStream = new FileInputStream(paramFile);
/*  73 */     return getMD5(fileInputStream);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getMD5(String paramString) throws NoSuchAlgorithmException, IOException {
/*  85 */     FileInputStream fileInputStream = new FileInputStream(paramString);
/*  86 */     return getMD5(fileInputStream);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean md5CheckSum(InputStream paramInputStream, String paramString) throws NoSuchAlgorithmException, IOException {
/*  99 */     return getMD5(paramInputStream).equals(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean md5CheckSum(File paramFile, String paramString) throws NoSuchAlgorithmException, IOException {
/* 112 */     return getMD5(paramFile).equals(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean md5CheckSum(String paramString1, String paramString2) throws NoSuchAlgorithmException, IOException {
/* 125 */     return getMD5(paramString1).equals(paramString2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/DBstep/MD5Util.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */