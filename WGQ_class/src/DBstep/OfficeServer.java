/*     */ package DBstep;
/*     */ 
/*     */ import com.api.odoc.util.OdocFileUtil;
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.filter.HTMLFilter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OfficeServer
/*     */   extends HttpServlet
/*     */ {
/*  30 */   private iMsgServer2015 MsgObj = new iMsgServer2015();
/*  31 */   private BaseBean baseBean = new BaseBean();
/*     */   String mOption;
/*     */   String mUserName;
/*     */   String mRecordID;
/*     */   String mFileName;
/*     */   String mFileType;
/*     */   String mCommand;
/*     */   String mInfo;
/*     */   String mImageName;
/*     */   String mTemplate;
/*     */   String mContent;
/*     */   String mRemoteFile;
/*     */   byte[] mFileBody;
/*     */   String mImgPath;
/*  45 */   int mFileSize = 0;
/*     */   
/*     */   String mFilePath;
/*     */   
/*     */   String mDirectory;
/*     */   
/*     */   String mMarkList;
/*     */   
/*     */   String mMarkName;
/*     */   String mPassword;
/*     */   String mFileDate;
/*     */   private String mDateTime;
/*     */   private String mHostName;
/*     */   private String mMarkGuid;
/*     */   String mDescript;
/*     */   int imagefileid;
/*     */   private String mOfficePrints;
/*     */   private int mCopies;
/*     */   
/*     */   protected void service(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  65 */     this.mCommand = "";
/*  66 */     this.mFilePath = paramHttpServletRequest.getSession().getServletContext().getRealPath("");
/*     */     try {
/*  68 */       this.baseBean.writeLog("----------------------进入插件调用");
/*  69 */       if (paramHttpServletRequest.getMethod().equalsIgnoreCase("POST")) {
/*  70 */         this.MsgObj.setSendType("JSON");
/*  71 */         this.MsgObj.Load(paramHttpServletRequest);
/*  72 */         this.mOption = this.MsgObj.GetMsgByName("OPTION");
/*  73 */         this.mUserName = this.MsgObj.GetMsgByName("USERNAME");
/*  74 */         this.baseBean.writeLog("----------------" + this.mOption);
/*     */         
/*  76 */         if (this.mOption.equalsIgnoreCase("LOADMARKLIST")) {
/*  77 */           this.baseBean.writeLog("----------------------进入插件调用：LOADMARKLIST");
/*  78 */           this.MsgObj.MsgTextClear();
/*  79 */           if (LoadMarkList(paramHttpServletRequest, paramHttpServletResponse)) {
/*  80 */             this.MsgObj.SetMsgByName("MARKLIST", this.mMarkList);
/*  81 */             this.baseBean.writeLog("----------------------mMarkList：" + this.mMarkList);
/*  82 */             this.baseBean.writeLog("mMarkList====" + this.mMarkList);
/*  83 */             this.baseBean.writeLog("创建印章列表成功");
/*     */           } else {
/*  85 */             this.baseBean.writeLog("创建印章列表失败!");
/*     */           } 
/*  87 */         } else if (this.mOption.equalsIgnoreCase("LOADMARKIMAGE")) {
/*  88 */           this.mMarkName = this.MsgObj.GetMsgByName("IMAGENAME");
/*  89 */           this.mUserName = this.MsgObj.GetMsgByName("USERNAME");
/*  90 */           this.mPassword = this.MsgObj.GetMsgByName("PASSWORD");
/*  91 */           this.MsgObj.MsgTextClear();
/*  92 */           if (LoadMarkImage(paramHttpServletRequest, paramHttpServletResponse, this.mMarkName, this.mPassword)) {
/*  93 */             this.MsgObj.SetMsgByName("IMAGETYPE", this.mFileType);
/*  94 */             if (this.imagefileid > 0) {
/*  95 */               InputStream inputStream = ImageFileManager.getInputStreamById(this.imagefileid);
/*  96 */               byte[] arrayOfByte = IOUtils.toByteArray(inputStream);
/*  97 */               this.mFileBody = arrayOfByte;
/*  98 */               this.MsgObj.MsgFileBody(this.mFileBody);
/*     */             } else {
/* 100 */               this.MsgObj.MsgFileLoad(this.mImgPath);
/*     */             } 
/*     */ 
/*     */             
/* 104 */             this.MsgObj.SetMsgByName("ZORDER", "5");
/* 105 */             this.MsgObj.SetMsgByName("STATUS", "打开成功!");
/* 106 */             this.baseBean.writeLog("打开印章文件成功");
/*     */           } else {
/* 108 */             this.mFileBody = null;
/*     */             
/* 110 */             this.MsgObj.SetMsgByName("STATUS", "签名或密码错误!");
/* 111 */             this.baseBean.writeLog("签名或密码错误!");
/*     */           } 
/* 113 */         } else if (this.mOption.equalsIgnoreCase("INSERTIMAGE")) {
/* 114 */           String str1 = this.MsgObj.GetMsgByName("BOOKMARKNAME") + "";
/* 115 */           String str2 = this.MsgObj.GetMsgByName("requestid");
/* 116 */           String str3 = this.MsgObj.GetMsgByName("isInsertImageNew") + "";
/* 117 */           RecordSet recordSet = new RecordSet();
/* 118 */           BaseBean baseBean = new BaseBean();
/* 119 */           baseBean.writeLog("officeServer.java====INSERTIMAGE==============bookMarkName=" + str1 + "requestid=" + str2 + "isInsertImageNew=" + str3);
/* 120 */           if ("1".equals(str3)) {
/* 121 */             String str4 = this.MsgObj.GetMsgByName("imagefileid4pic") + "";
/* 122 */             recordSet.executeQuery("select imagefilename from imagefile  where imagefileid=?", new Object[] { str4 });
/*     */             
/* 124 */             String str5 = Util.null2String(recordSet.getString("imagefilename"));
/* 125 */             baseBean.writeLog("officeServer.java====INSERTIMAGE==============fileName=" + str5 + "imagefileid4pic=" + str4);
/* 126 */             if (recordSet.next() && StringUtil.isNotNull(new String[] { str5, str4 })) {
/* 127 */               String str6 = OdocFileUtil.getFileExt(str5);
/* 128 */               if (StringUtil.isNull(str6)) {
/* 129 */                 str6 = ".jpg";
/*     */               } else {
/* 131 */                 str6 = "." + str6;
/*     */               } 
/* 133 */               baseBean.writeLog("officeServer.java====INSERTIMAGE==============picType=" + str6);
/* 134 */               OdocFileUtil.getFileFromByte(OdocFileUtil.inputStream2Byte(ImageFileManager.getInputStreamById(Integer.valueOf(str4).intValue())), GCONST.getRootPath(), str5);
/* 135 */               String str7 = GCONST.getRootPath() + File.separator + str5;
/* 136 */               baseBean.writeLog("officeServerver.java====INSERTIMAGE==============fullPath=" + str7);
/* 137 */               this.MsgObj.MsgTextClear();
/* 138 */               if (this.MsgObj.MsgFileLoad(str7)) {
/* 139 */                 this.MsgObj.SetMsgByName("IMAGETYPE", str7);
/* 140 */                 this.MsgObj.SetMsgByName("POSITION", str1);
/* 141 */                 this.MsgObj.SetMsgByName("STATUS", "插入图片成功!");
/* 142 */                 baseBean.writeLog("插入图片成功!");
/*     */               } else {
/* 144 */                 baseBean.writeLog("插入图片失败!");
/*     */               }
/*     */             
/*     */             } 
/*     */           } else {
/*     */             
/* 150 */             this.mImageName = this.MsgObj.GetMsgByName("IMAGENAME");
/* 151 */             baseBean.writeLog("officeServerver.java====mImageName1=" + this.mImageName);
/* 152 */             this.mImageName = (new HTMLFilter()).filter(this.mImageName);
/* 153 */             baseBean.writeLog("officeServerver.java====mImageName2=" + this.mImageName);
/* 154 */             this.mFilePath += "\\Document\\" + this.mImageName;
/* 155 */             this.MsgObj.MsgTextClear();
/* 156 */             if (this.MsgObj.MsgFileLoad(this.mFilePath)) {
/* 157 */               this.MsgObj.SetMsgByName("STATUS", "获取图片成功!");
/* 158 */               baseBean.writeLog("获取图片成功!");
/*     */             } else {
/* 160 */               baseBean.writeLog("获取图片成功!");
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 165 */         this.baseBean.writeLog("SendPackage");
/* 166 */         this.MsgObj.Send(paramHttpServletResponse);
/*     */       } 
/* 168 */     } catch (Exception exception) {
/* 169 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean LoadMarkList(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 175 */     boolean bool = false;
/* 176 */     BaseBean baseBean = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 185 */       RecordSet recordSet = new RecordSet();
/* 186 */       String str = "select markName from DocSignature where hrmresid=?";
/* 187 */       baseBean.writeLog("=============签名列表sql:" + str + ",mUserName:" + this.mUserName);
/* 188 */       this.mMarkList = "";
/* 189 */       recordSet.executeQuery(str, new Object[] { this.mUserName });
/* 190 */       while (recordSet.next()) {
/* 191 */         this.mMarkList += recordSet.getString("MarkName") + "\\r\\n";
/*     */       }
/* 193 */       baseBean.writeLog("=============签名列表mMarkList:" + this.mMarkList);
/* 194 */       bool = true;
/* 195 */     } catch (Exception exception) {
/* 196 */       baseBean.writeLog("============LoadMarkList error", exception);
/* 197 */       bool = false;
/*     */     } 
/* 199 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean LoadMarkImage(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, String paramString1, String paramString2) {
/* 204 */     boolean bool = false;
/* 205 */     BaseBean baseBean = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 214 */       RecordSet recordSet = new RecordSet();
/* 215 */       String str = "select markType,markPath,imagefileid from DocSignature where hrmresid=? and markName=? ";
/* 216 */       recordSet.executeQuery(str, new Object[] { this.mUserName, paramString1 });
/* 217 */       if (recordSet.next()) {
/* 218 */         this.mImgPath = recordSet.getString("MarkPath");
/* 219 */         this.mFileType = recordSet.getString("MarkType");
/* 220 */         this.imagefileid = Util.getIntValue(recordSet.getString("imagefileid"), -1);
/* 221 */         bool = true;
/*     */       } 
/* 223 */     } catch (Exception exception) {
/* 224 */       baseBean.writeLog("============LoadMarkImage error", exception);
/* 225 */       bool = false;
/*     */     } 
/* 227 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/DBstep/OfficeServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */