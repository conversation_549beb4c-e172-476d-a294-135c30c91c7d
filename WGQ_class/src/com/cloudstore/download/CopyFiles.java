/*    */ package com.cloudstore.download;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileOutputStream;
/*    */ import java.io.IOException;
/*    */ import java.nio.channels.FileChannel;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CopyFiles
/*    */ {
/*    */   public static void copyFiles(File paramFile1, File paramFile2) {
/* 22 */     FileInputStream fileInputStream = null;
/* 23 */     FileOutputStream fileOutputStream = null;
/* 24 */     FileChannel fileChannel1 = null;
/* 25 */     FileChannel fileChannel2 = null;
/*    */     try {
/* 27 */       if (!paramFile1.exists()) {
/* 28 */         paramFile1.createNewFile();
/*    */       }
/* 30 */       if (!paramFile2.exists()) {
/* 31 */         paramFile2.createNewFile();
/*    */       }
/* 33 */       fileInputStream = new FileInputStream(paramFile1);
/* 34 */       fileOutputStream = new FileOutputStream(paramFile2);
/* 35 */       fileChannel1 = fileInputStream.getChannel();
/* 36 */       fileChannel2 = fileOutputStream.getChannel();
/* 37 */       fileChannel1.transferTo(0L, fileChannel1.size(), fileChannel2);
/* 38 */     } catch (IOException iOException) {
/* 39 */       iOException.printStackTrace();
/*    */     } finally {
/*    */       try {
/* 42 */         fileInputStream.close();
/* 43 */         fileChannel1.close();
/* 44 */         fileOutputStream.close();
/* 45 */         fileChannel2.close();
/* 46 */       } catch (IOException iOException) {
/* 47 */         iOException.printStackTrace();
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/download/CopyFiles.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */