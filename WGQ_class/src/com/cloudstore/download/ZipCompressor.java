/*    */ package com.cloudstore.download;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileNotFoundException;
/*    */ import java.io.FileOutputStream;
/*    */ import java.io.IOException;
/*    */ import java.util.zip.ZipEntry;
/*    */ import java.util.zip.ZipOutputStream;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ZipCompressor
/*    */ {
/*    */   public static void createZip(String paramString1, String paramString2) {
/* 23 */     FileOutputStream fileOutputStream = null;
/* 24 */     ZipOutputStream zipOutputStream = null;
/*    */     try {
/* 26 */       fileOutputStream = new FileOutputStream(paramString2);
/* 27 */       zipOutputStream = new ZipOutputStream(fileOutputStream);
/* 28 */       writeZip(new File(paramString1), "", zipOutputStream);
/* 29 */     } catch (FileNotFoundException fileNotFoundException) {
/* 30 */       fileNotFoundException.printStackTrace();
/*    */     } finally {
/* 32 */       if (zipOutputStream != null) {
/*    */         try {
/* 34 */           zipOutputStream.close();
/* 35 */         } catch (IOException iOException) {
/* 36 */           iOException.printStackTrace();
/*    */         } 
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private static void writeZip(File paramFile, String paramString, ZipOutputStream paramZipOutputStream) {
/* 49 */     if (paramFile.exists())
/* 50 */       if (paramFile.isDirectory()) {
/* 51 */         paramString = paramString + paramFile.getName() + File.separator;
/* 52 */         File[] arrayOfFile = paramFile.listFiles();
/* 53 */         if (arrayOfFile.length != 0) {
/* 54 */           for (File file : arrayOfFile) {
/* 55 */             writeZip(file, paramString, paramZipOutputStream);
/*    */           }
/*    */         } else {
/*    */           try {
/* 59 */             paramZipOutputStream.putNextEntry(new ZipEntry(paramString));
/* 60 */           } catch (IOException iOException) {
/* 61 */             iOException.printStackTrace();
/*    */           } 
/*    */         } 
/*    */       } else {
/* 65 */         FileInputStream fileInputStream = null;
/*    */         try {
/* 67 */           fileInputStream = new FileInputStream(paramFile);
/* 68 */           ZipEntry zipEntry = new ZipEntry(paramString + paramFile.getName());
/* 69 */           paramZipOutputStream.putNextEntry(zipEntry);
/* 70 */           byte[] arrayOfByte = new byte[1024];
/* 71 */           int i = 0;
/* 72 */           while ((i = fileInputStream.read(arrayOfByte)) != -1) {
/* 73 */             paramZipOutputStream.write(arrayOfByte, 0, i);
/* 74 */             paramZipOutputStream.flush();
/*    */           } 
/* 76 */         } catch (FileNotFoundException fileNotFoundException) {
/* 77 */           fileNotFoundException.printStackTrace();
/* 78 */         } catch (IOException iOException) {
/*    */         
/*    */         } finally {
/* 81 */           if (fileInputStream != null)
/*    */             try {
/* 83 */               fileInputStream.close();
/* 84 */             } catch (IOException iOException) {
/* 85 */               iOException.printStackTrace();
/*    */             }  
/*    */         } 
/*    */       }  
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/download/ZipCompressor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */