/*    */ package com.cloudstore.download;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AppInfoXml
/*    */ {
/*    */   private String name;
/*    */   private String code;
/*    */   private String desc;
/*    */   private String url;
/*    */   private String key;
/*    */   private String appImg;
/*    */   private String version;
/*    */   
/*    */   public String getName() {
/* 19 */     return this.name;
/*    */   }
/*    */   
/*    */   public void setName(String paramString) {
/* 23 */     this.name = paramString;
/*    */   }
/*    */   
/*    */   public String getCode() {
/* 27 */     return this.code;
/*    */   }
/*    */   
/*    */   public void setCode(String paramString) {
/* 31 */     this.code = paramString;
/*    */   }
/*    */   
/*    */   public String getDesc() {
/* 35 */     return this.desc;
/*    */   }
/*    */   
/*    */   public void setDesc(String paramString) {
/* 39 */     this.desc = paramString;
/*    */   }
/*    */   
/*    */   public String getUrl() {
/* 43 */     return this.url;
/*    */   }
/*    */   
/*    */   public void setUrl(String paramString) {
/* 47 */     this.url = paramString;
/*    */   }
/*    */   
/*    */   public String getKey() {
/* 51 */     return this.key;
/*    */   }
/*    */   
/*    */   public void setKey(String paramString) {
/* 55 */     this.key = paramString;
/*    */   }
/*    */   
/*    */   public String getAppImg() {
/* 59 */     return this.appImg;
/*    */   }
/*    */   
/*    */   public void setAppImg(String paramString) {
/* 63 */     this.appImg = paramString;
/*    */   }
/*    */   
/*    */   public String getVersion() {
/* 67 */     return this.version;
/*    */   }
/*    */   
/*    */   public void setVersion(String paramString) {
/* 71 */     this.version = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/download/AppInfoXml.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */