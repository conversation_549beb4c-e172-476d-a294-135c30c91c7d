/*     */ package com.cloudstore.download;
/*     */ 
/*     */ import com.cloudstore.api.util.Util_Key;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.util.UUID;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.DocumentHelper;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.OutputFormat;
/*     */ import org.dom4j.io.XMLWriter;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CreateFile
/*     */ {
/*  33 */   private String destPath = "";
/*  34 */   private String appPath = "";
/*  35 */   private String uuid = "";
/*     */   
/*     */   public String getUuid() {
/*  38 */     return this.uuid;
/*     */   }
/*     */   
/*     */   public String getAppPath() {
/*  42 */     return this.appPath;
/*     */   }
/*     */   
/*     */   public String getDestPath() {
/*  46 */     return this.destPath;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CreateFile(String paramString) {
/*  53 */     this.destPath = paramString;
/*  54 */     this.uuid = "a" + UUID.randomUUID().toString().replaceAll("-", "");
/*  55 */     this.appPath = paramString + File.separator + this.uuid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createFiles(AppInfoXml paramAppInfoXml) {
/*  63 */     String str = this.uuid;
/*  64 */     File file1 = new File(this.destPath + File.separator + "app");
/*  65 */     File file2 = new File(this.appPath);
/*  66 */     copyFiles(file1, file2, paramAppInfoXml);
/*  67 */     ZipCompressor.createZip(this.appPath, this.destPath + File.separator + str + ".zip");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteFiles() {
/*  75 */     File file1 = new File(this.destPath + File.separator + this.uuid);
/*  76 */     File file2 = new File(this.destPath + File.separator + this.uuid + ".zip");
/*  77 */     if (file1.exists()) {
/*  78 */       deleteAppFile(file1);
/*     */     }
/*  80 */     if (file2.exists()) {
/*  81 */       deleteAppZip(file2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteAppZip(File paramFile) {
/*  90 */     paramFile.delete();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteAppFile(File paramFile) {
/*  98 */     if (paramFile.isFile()) {
/*  99 */       paramFile.delete();
/* 100 */     } else if (paramFile.isDirectory()) {
/* 101 */       File[] arrayOfFile = paramFile.listFiles();
/* 102 */       for (byte b = 0; b < arrayOfFile.length; b++) {
/* 103 */         deleteAppFile(arrayOfFile[b]);
/*     */       }
/*     */     } 
/* 106 */     paramFile.delete();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void copyFiles(File paramFile1, File paramFile2, AppInfoXml paramAppInfoXml) {
/* 116 */     if (paramFile1.exists()) {
/* 117 */       if (!paramFile2.exists()) {
/* 118 */         paramFile2.mkdirs();
/*     */       }
/* 120 */       if (paramFile1.isDirectory()) {
/* 121 */         File[] arrayOfFile = paramFile1.listFiles();
/* 122 */         for (File file : arrayOfFile) {
/* 123 */           if (file.isDirectory()) {
/* 124 */             paramFile2.mkdirs();
/* 125 */             if (file.getName().startsWith("app0")) {
/* 126 */               copyFiles(file, new File(paramFile2.getAbsolutePath() + File.separator + this.uuid), paramAppInfoXml);
/*     */             } else {
/* 128 */               if (file.getName().contains("config")) {
/* 129 */                 File file1 = new File(paramFile2.getAbsolutePath() + File.separator + "config");
/* 130 */                 if (!file1.exists()) {
/* 131 */                   file1.mkdirs();
/*     */                 }
/* 133 */                 String str1 = file1 + File.separator + "appInfo.xml";
/* 134 */                 writeAppInfoXml(paramAppInfoXml, str1);
/* 135 */                 String str2 = file1 + File.separator + "allKey.license";
/* 136 */                 createLicense(str2, paramAppInfoXml);
/*     */               } 
/* 138 */               copyFiles(file, new File(paramFile2.getAbsolutePath() + File.separator + file.getName()), paramAppInfoXml);
/*     */             } 
/*     */           } else {
/* 141 */             File file1 = new File(paramFile2.getAbsolutePath() + File.separator + file.getName());
/* 142 */             if (!file1.exists()) {
/*     */               try {
/* 144 */                 file1.createNewFile();
/* 145 */               } catch (IOException iOException) {
/* 146 */                 iOException.printStackTrace();
/*     */               } 
/*     */             }
/* 149 */             if (!"appInfo.xml".equalsIgnoreCase(file.getName()) && !"allKey.license".equalsIgnoreCase(file.getName())) {
/* 150 */               CopyFiles.copyFiles(file, file1);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createLicense(String paramString, AppInfoXml paramAppInfoXml) {
/* 163 */     File file = new File(paramString);
/* 164 */     if (!file.exists()) {
/*     */       try {
/* 166 */         file.createNewFile();
/* 167 */       } catch (IOException iOException) {
/* 168 */         iOException.printStackTrace();
/*     */       } 
/*     */     }
/*     */     try {
/* 172 */       Util_Key util_Key = new Util_Key();
/* 173 */       OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
/* 174 */       BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter);
/* 175 */       String str = "";
/*     */       try {
/* 177 */         str = util_Key.getCommonKey(this.uuid, paramAppInfoXml.getName());
/* 178 */       } catch (Exception exception) {
/* 179 */         exception.printStackTrace();
/*     */       } 
/* 181 */       bufferedWriter.write(str);
/* 182 */       bufferedWriter.flush();
/*     */       
/* 184 */       bufferedWriter.close();
/* 185 */     } catch (IOException iOException) {
/* 186 */       iOException.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeAppInfoXml(AppInfoXml paramAppInfoXml, String paramString) {
/*     */     try {
/* 234 */       File file = new File(paramString);
/* 235 */       if (!file.exists()) {
/* 236 */         file.createNewFile();
/*     */       }
/* 238 */       Document document = createDom(paramAppInfoXml);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 246 */       OutputFormat outputFormat = new OutputFormat("    ", true);
/* 247 */       outputFormat.setEncoding("UTF-8");
/* 248 */       XMLWriter xMLWriter = new XMLWriter(new FileOutputStream(file), outputFormat);
/* 249 */       xMLWriter.write(document);
/* 250 */       xMLWriter.flush();
/* 251 */       xMLWriter.close();
/*     */     }
/* 253 */     catch (FileNotFoundException fileNotFoundException) {
/* 254 */       fileNotFoundException.printStackTrace();
/* 255 */     } catch (IOException iOException) {
/* 256 */       iOException.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document createDom(AppInfoXml paramAppInfoXml) {
/* 267 */     Element element1 = DocumentHelper.createElement("root");
/* 268 */     Document document = DocumentHelper.createDocument(element1);
/* 269 */     String str = paramAppInfoXml.getName();
/* 270 */     System.out.println("=====================value:" + str);
/* 271 */     element1.addElement("name").addText(str);
/* 272 */     element1.addElement("desc").addText(paramAppInfoXml.getDesc() + "");
/* 273 */     element1.addElement("appImg").addText("");
/* 274 */     element1.addElement("code").addText(paramAppInfoXml.getCode() + "");
/* 275 */     element1.addElement("version").addText(paramAppInfoXml.getVersion() + "");
/*     */     
/* 277 */     Element element2 = element1.addElement("pages");
/* 278 */     Element element3 = element2.addElement("page");
/* 279 */     element3.addElement("key").addText(paramAppInfoXml.getKey() + "");
/* 280 */     element3.addElement("name").addText(str);
/* 281 */     element3.addElement("url").addText(paramAppInfoXml.getUrl() + "");
/*     */     
/* 283 */     return document;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/download/CreateFile.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */