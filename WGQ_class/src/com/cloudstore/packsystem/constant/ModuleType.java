/*    */ package com.cloudstore.packsystem.constant;
/*    */ 
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum ModuleType
/*    */ {
/* 16 */   BS_MAIN(2001, "" + SystemEnv.getHtmlLabelName(10004812, ThreadVarLanguage.getLang()) + ""),
/* 17 */   BS_CPT(2002, "" + SystemEnv.getHtmlLabelName(535, ThreadVarLanguage.getLang()) + ""),
/* 18 */   BS_CRM(2003, "" + SystemEnv.getHtmlLabelName(30043, ThreadVarLanguage.getLang()) + ""),
/* 19 */   BS_DOCUMENT(2004, "" + SystemEnv.getHtmlLabelName(58, ThreadVarLanguage.getLang()) + ""),
/* 20 */   BS_HRM(2005, "" + SystemEnv.getHtmlLabelName(10004813, ThreadVarLanguage.getLang()) + ""),
/* 21 */   BS_HRMPUBLIC(2006, "" + SystemEnv.getHtmlLabelName(10004814, ThreadVarLanguage.getLang()) + ""),
/* 22 */   BS_MEETING(2007, "" + SystemEnv.getHtmlLabelName(34076, ThreadVarLanguage.getLang()) + ""),
/* 23 */   BS_COWORK(2008, "" + SystemEnv.getHtmlLabelName(26269, ThreadVarLanguage.getLang()) + ""),
/* 24 */   BS_BLOG(2009, "微博"),
/*    */   
/* 26 */   BS_MOBILEMODE(2010, "" + SystemEnv.getHtmlLabelName(81788, ThreadVarLanguage.getLang()) + ""),
/* 27 */   BS_PORTAL(2011, "" + SystemEnv.getHtmlLabelName(582, ThreadVarLanguage.getLang()) + ""),
/* 28 */   BS_SMALLAPP(2012, "" + SystemEnv.getHtmlLabelName(10004815, ThreadVarLanguage.getLang()) + ""),
/* 29 */   BS_THEMEBRARYTARGET(2013, "" + SystemEnv.getHtmlLabelName(25025, ThreadVarLanguage.getLang()) + ""),
/*    */   
/* 31 */   BS_WORKFLOW(2014, "" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + ""),
/* 32 */   BS_WORKPLAN(2015, "" + SystemEnv.getHtmlLabelName(2211, ThreadVarLanguage.getLang()) + ""),
/* 33 */   BS_EMAIL(2016, "" + SystemEnv.getHtmlLabelName(34077, ThreadVarLanguage.getLang()) + ""),
/* 34 */   BS_INTEGRATION(2017, "" + SystemEnv.getHtmlLabelName(26267, ThreadVarLanguage.getLang()) + ""),
/* 35 */   BS_FNA(2018, "" + SystemEnv.getHtmlLabelName(189, ThreadVarLanguage.getLang()) + ""),
/* 36 */   BS_ODOC(2019, "" + SystemEnv.getHtmlLabelName(33424, ThreadVarLanguage.getLang()) + ""),
/* 37 */   BS_SECURITY(2020, "" + SystemEnv.getHtmlLabelName(120, ThreadVarLanguage.getLang()) + ""),
/*    */   
/* 39 */   MAINASYNC(1001, "" + SystemEnv.getHtmlLabelName(10004816, ThreadVarLanguage.getLang()) + ""),
/* 40 */   MAIN(1002, "" + SystemEnv.getHtmlLabelName(10004817, ThreadVarLanguage.getLang()) + ""),
/* 41 */   TMAIN(1003, "" + SystemEnv.getHtmlLabelName(10004817, ThreadVarLanguage.getLang()) + "Test"),
/* 42 */   CUBE(1004, "" + SystemEnv.getHtmlLabelName(10004818, ThreadVarLanguage.getLang()) + ""),
/* 43 */   BLOG(1005, "前台异步入口"),
/*    */   
/* 45 */   COWORK(1006, "" + SystemEnv.getHtmlLabelName(26269, ThreadVarLanguage.getLang()) + ""),
/* 46 */   CPT(1007, "" + SystemEnv.getHtmlLabelName(535, ThreadVarLanguage.getLang()) + ""),
/* 47 */   CRM(1008, "" + SystemEnv.getHtmlLabelName(30043, ThreadVarLanguage.getLang()) + ""),
/* 48 */   CRMREPORT(1009, "" + SystemEnv.getHtmlLabelName(16533, ThreadVarLanguage.getLang()) + ""),
/* 49 */   DEMO(1010, "demo" + SystemEnv.getHtmlLabelName(82159, ThreadVarLanguage.getLang()) + ""),
/*    */   
/* 51 */   DEVELOP(1011, "" + SystemEnv.getHtmlLabelName(10004819, ThreadVarLanguage.getLang()) + ""),
/* 52 */   DOCUMENT(1012, "" + SystemEnv.getHtmlLabelName(58, ThreadVarLanguage.getLang()) + ""),
/* 53 */   EMAIL(1013, "" + SystemEnv.getHtmlLabelName(34077, ThreadVarLanguage.getLang()) + ""),
/* 54 */   ESEARCH(1014, "" + SystemEnv.getHtmlLabelName(31953, ThreadVarLanguage.getLang()) + ""),
/* 55 */   FNA(1015, "" + SystemEnv.getHtmlLabelName(189, ThreadVarLanguage.getLang()) + ""),
/* 56 */   HRM(1016, "" + SystemEnv.getHtmlLabelName(10004813, ThreadVarLanguage.getLang()) + ""),
/*    */   
/* 58 */   INTE(1017, "" + SystemEnv.getHtmlLabelName(26267, ThreadVarLanguage.getLang()) + ""),
/* 59 */   MEETING(1018, "" + SystemEnv.getHtmlLabelName(34076, ThreadVarLanguage.getLang()) + ""),
/* 60 */   ODOC(1019, "" + SystemEnv.getHtmlLabelName(33424, ThreadVarLanguage.getLang()) + ""),
/* 61 */   PORTAL(1020, "" + SystemEnv.getHtmlLabelName(582, ThreadVarLanguage.getLang()) + ""),
/* 62 */   PRJ(1021, "" + SystemEnv.getHtmlLabelName(25106, ThreadVarLanguage.getLang()) + ""),
/*    */   
/* 64 */   SMALLAPP(1022, "" + SystemEnv.getHtmlLabelName(10004820, ThreadVarLanguage.getLang()) + ""),
/* 65 */   THEME(1023, "" + SystemEnv.getHtmlLabelName(25025, ThreadVarLanguage.getLang()) + ""),
/* 66 */   WORKFLOW(1024, "" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + ""),
/* 67 */   WORKFLOWPUBLIC(1025, "" + SystemEnv.getHtmlLabelName(10004821, ThreadVarLanguage.getLang()) + ""),
/*    */   
/* 69 */   WORKFLOWFORM(1026, "" + SystemEnv.getHtmlLabelName(19532, ThreadVarLanguage.getLang()) + ""),
/* 70 */   CUSTOMSETTING(1027, "" + SystemEnv.getHtmlLabelName(18166, ThreadVarLanguage.getLang()) + ""),
/* 71 */   WORKPLAN(1028, "" + SystemEnv.getHtmlLabelName(2211, ThreadVarLanguage.getLang()) + ""),
/* 72 */   WORKRELATE(1029, "" + SystemEnv.getHtmlLabelName(22824, ThreadVarLanguage.getLang()) + "");
/*    */ 
/*    */   
/*    */   protected int code;
/*    */ 
/*    */   
/*    */   protected String name;
/*    */ 
/*    */ 
/*    */   
/*    */   public int getCode() {
/* 83 */     return this.code;
/*    */   }
/*    */   
/*    */   public String getName() {
/* 87 */     return this.name;
/*    */   }
/*    */   
/*    */   ModuleType(int paramInt1, String paramString1) {
/* 91 */     this.code = paramInt1;
/* 92 */     this.name = paramString1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/constant/ModuleType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */