/*    */ package com.cloudstore.packsystem.constant;
/*    */ 
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum ModuleStatusType
/*    */ {
/* 16 */   RUNNING(1, "" + SystemEnv.getHtmlLabelName(10004811, ThreadVarLanguage.getLang()) + ""),
/* 17 */   NONE(2, "" + SystemEnv.getHtmlLabelName(83519, ThreadVarLanguage.getLang()) + ""),
/* 18 */   ERROR(3, "错误"),
/* 19 */   SUCCESS(4, "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "");
/*    */   
/*    */   private int code;
/*    */   
/*    */   private String name;
/*    */ 
/*    */   
/*    */   public String getName() {
/* 27 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public int getCode() {
/* 32 */     return this.code;
/*    */   }
/*    */ 
/*    */   
/*    */   ModuleStatusType(int paramInt1, String paramString1) {
/* 37 */     this.code = paramInt1;
/* 38 */     this.name = paramString1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/constant/ModuleStatusType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */