/*    */ package com.cloudstore.packsystem.constant;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum SystemType
/*    */ {
/* 15 */   Any("any"),
/* 16 */   Linux("Linux"),
/* 17 */   Mac_OS("Mac OS"),
/* 18 */   Mac_OS_X("Mac OS X"),
/* 19 */   Windows("Windows"),
/* 20 */   OS2("OS/2"),
/* 21 */   Solaris("Solaris"),
/* 22 */   SunOS("SunOS"),
/* 23 */   MPEiX("MPE/iX"),
/* 24 */   HP_UX("HP-UX"),
/* 25 */   AIX("AIX"),
/* 26 */   OS390("OS/390"),
/* 27 */   FreeBSD("FreeBSD"),
/* 28 */   Irix("Irix"),
/* 29 */   Digital_Unix("Digital Unix"),
/* 30 */   NetWare_411("NetWare"),
/* 31 */   OSF1("OSF1"),
/* 32 */   OpenVMS("OpenVMS"),
/* 33 */   Others("Others");
/*    */   
/*    */   SystemType(String paramString1) {
/* 36 */     this.description = paramString1;
/*    */   }
/*    */   private String description;
/*    */   public String toString() {
/* 40 */     return this.description;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/constant/SystemType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */