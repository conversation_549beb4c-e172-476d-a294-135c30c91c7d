/*     */ package com.cloudstore.packsystem.service;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.cloudstore.packsystem.constant.ModuleType;
/*     */ import com.cloudstore.packsystem.util.Util_FrontPack;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.QueryParam;
/*     */ import javax.ws.rs.core.Context;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/ec/dev/pack")
/*     */ public class ServicePack
/*     */ {
/*     */   @GET
/*     */   @Path("/getlist")
/*     */   @Produces({"text/plain"})
/*     */   public String getlist(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  50 */     JSONObject jSONObject = new JSONObject();
/*  51 */     jSONObject.put("status", Boolean.valueOf(true));
/*     */     
/*     */     try {
/*  54 */       List list = Util_FrontPack.getPackList();
/*     */       
/*  56 */       jSONObject.put("datas", list);
/*  57 */       jSONObject.put("status", Boolean.valueOf(true));
/*     */     
/*     */     }
/*  60 */     catch (Exception exception) {
/*  61 */       exception.printStackTrace();
/*  62 */       jSONObject.put("status", Boolean.valueOf(false));
/*     */     } 
/*  64 */     return JSON.toJSONString(jSONObject);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/packstart")
/*     */   @Produces({"text/plain"})
/*     */   public String packJavaScript(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse, @QueryParam("module") String paramString) {
/*  73 */     JSONObject jSONObject = new JSONObject();
/*  74 */     jSONObject.put("status", Boolean.valueOf(true));
/*     */ 
/*     */     
/*     */     try {
/*  78 */       ModuleType moduleType = ModuleType.valueOf(paramString);
/*  79 */       String str = Util_FrontPack.packModule(moduleType, paramHttpServletRequest);
/*     */       
/*  81 */       jSONObject.put("message", str);
/*     */     }
/*  83 */     catch (Exception exception) {
/*  84 */       exception.printStackTrace();
/*  85 */       jSONObject.put("status", Boolean.valueOf(false));
/*     */     } 
/*  87 */     return JSON.toJSONString(jSONObject);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/packcancle")
/*     */   @Produces({"text/plain"})
/*     */   public String setStateMessage(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse, @QueryParam("module") String paramString) {
/*  96 */     JSONObject jSONObject = new JSONObject();
/*  97 */     jSONObject.put("status", Boolean.valueOf(true));
/*     */ 
/*     */     
/*     */     try {
/* 101 */       jSONObject.put("result", "");
/*     */     }
/* 103 */     catch (Exception exception) {
/* 104 */       exception.printStackTrace();
/* 105 */       jSONObject.put("status", Boolean.valueOf(false));
/*     */     } 
/* 107 */     return JSON.toJSONString(jSONObject);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/service/ServicePack.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */