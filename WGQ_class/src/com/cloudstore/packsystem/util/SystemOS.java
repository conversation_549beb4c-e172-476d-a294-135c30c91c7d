/*     */ package com.cloudstore.packsystem.util;
/*     */ 
/*     */ import com.cloudstore.packsystem.constant.SystemType;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SystemOS
/*     */ {
/*  21 */   private static String OS = System.getProperty("os.name").toLowerCase();
/*     */   
/*  23 */   private static SystemOS _instance = new SystemOS();
/*     */ 
/*     */   
/*     */   private SystemType platform;
/*     */ 
/*     */   
/*     */   public static boolean isLinux() {
/*  30 */     return (OS.indexOf("linux") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isMacOS() {
/*  34 */     return (OS.indexOf("mac") >= 0 && OS.indexOf("os") > 0 && OS.indexOf("x") < 0);
/*     */   }
/*     */   
/*     */   public static boolean isMacOSX() {
/*  38 */     return (OS.indexOf("mac") >= 0 && OS.indexOf("os") > 0 && OS.indexOf("x") > 0);
/*     */   }
/*     */   
/*     */   public static boolean isWindows() {
/*  42 */     return (OS.indexOf("windows") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isOS2() {
/*  46 */     return (OS.indexOf("os/2") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isSolaris() {
/*  50 */     return (OS.indexOf("solaris") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isSunOS() {
/*  54 */     return (OS.indexOf("sunos") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isMPEiX() {
/*  58 */     return (OS.indexOf("mpe/ix") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isHPUX() {
/*  62 */     return (OS.indexOf("hp-ux") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isAix() {
/*  66 */     return (OS.indexOf("aix") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isOS390() {
/*  70 */     return (OS.indexOf("os/390") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isFreeBSD() {
/*  74 */     return (OS.indexOf("freebsd") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isIrix() {
/*  78 */     return (OS.indexOf("irix") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isDigitalUnix() {
/*  82 */     return (OS.indexOf("digital") >= 0 && OS.indexOf("unix") > 0);
/*     */   }
/*     */   
/*     */   public static boolean isNetWare() {
/*  86 */     return (OS.indexOf("netware") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isOSF1() {
/*  90 */     return (OS.indexOf("osf1") >= 0);
/*     */   }
/*     */   
/*     */   public static boolean isOpenVMS() {
/*  94 */     return (OS.indexOf("openvms") >= 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static SystemType getOSname() {
/* 102 */     if (isAix()) {
/* 103 */       _instance.platform = SystemType.AIX;
/* 104 */     } else if (isDigitalUnix()) {
/* 105 */       _instance.platform = SystemType.Digital_Unix;
/* 106 */     } else if (isFreeBSD()) {
/* 107 */       _instance.platform = SystemType.FreeBSD;
/* 108 */     } else if (isHPUX()) {
/* 109 */       _instance.platform = SystemType.HP_UX;
/* 110 */     } else if (isIrix()) {
/* 111 */       _instance.platform = SystemType.Irix;
/* 112 */     } else if (isLinux()) {
/* 113 */       _instance.platform = SystemType.Linux;
/* 114 */     } else if (isMacOS()) {
/* 115 */       _instance.platform = SystemType.Mac_OS;
/* 116 */     } else if (isMacOSX()) {
/* 117 */       _instance.platform = SystemType.Mac_OS_X;
/* 118 */     } else if (isMPEiX()) {
/* 119 */       _instance.platform = SystemType.MPEiX;
/* 120 */     } else if (isNetWare()) {
/* 121 */       _instance.platform = SystemType.NetWare_411;
/* 122 */     } else if (isOpenVMS()) {
/* 123 */       _instance.platform = SystemType.OpenVMS;
/* 124 */     } else if (isOS2()) {
/* 125 */       _instance.platform = SystemType.OS2;
/* 126 */     } else if (isOS390()) {
/* 127 */       _instance.platform = SystemType.OS390;
/* 128 */     } else if (isOSF1()) {
/* 129 */       _instance.platform = SystemType.OSF1;
/* 130 */     } else if (isSolaris()) {
/* 131 */       _instance.platform = SystemType.Solaris;
/* 132 */     } else if (isSunOS()) {
/* 133 */       _instance.platform = SystemType.SunOS;
/* 134 */     } else if (isWindows()) {
/* 135 */       _instance.platform = SystemType.Windows;
/*     */     } else {
/* 137 */       _instance.platform = SystemType.Others;
/*     */     } 
/* 139 */     return _instance.platform;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/util/SystemOS.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */