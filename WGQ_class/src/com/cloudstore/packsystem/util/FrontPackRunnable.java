/*    */ package com.cloudstore.packsystem.util;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.Util_TableMap;
/*    */ import com.cloudstore.packsystem.constant.ModuleType;
/*    */ import java.io.BufferedReader;
/*    */ import java.io.File;
/*    */ import java.io.InputStreamReader;
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FrontPackRunnable
/*    */   implements Runnable
/*    */ {
/* 26 */   private static final Log logger = LogFactory.getLog(FrontPackRunnable.class);
/*    */   
/*    */   private String command;
/*    */   
/*    */   private String path;
/*    */   private ModuleType moduleType;
/*    */   
/*    */   public void setCommand(String paramString) {
/* 34 */     this.command = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setPath(String paramString) {
/* 39 */     this.path = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setModuleType(ModuleType paramModuleType) {
/* 44 */     this.moduleType = paramModuleType;
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/* 49 */     Runtime runtime = Runtime.getRuntime();
/* 50 */     String str = "";
/*    */     
/*    */     try {
/* 53 */       StringBuilder stringBuilder = new StringBuilder();
/* 54 */       logger.info(this.command + this.moduleType.toString().toLowerCase());
/* 55 */       logger.info(this.path);
/* 56 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(runtime.exec(this.command + this.moduleType.toString().toLowerCase(), (String[])null, new File(this.path)).getInputStream()));
/* 57 */       String str1 = null;
/* 58 */       StringBuffer stringBuffer = new StringBuffer();
/* 59 */       while ((str1 = bufferedReader.readLine()) != null) {
/* 60 */         stringBuffer.append(str1 + "\n");
/* 61 */         setCommand(this.moduleType, str.toString());
/* 62 */         logger.info(str1);
/*    */       } 
/* 64 */       str = stringBuffer.toString();
/* 65 */     } catch (Exception exception) {
/* 66 */       exception.printStackTrace();
/* 67 */       str = "build Error!";
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public static void setCommand(ModuleType paramModuleType, String paramString) {
/* 73 */     Util_TableMap.setObjVal("e9:frontPackKey:module:" + paramModuleType.getCode(), paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/util/FrontPackRunnable.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */