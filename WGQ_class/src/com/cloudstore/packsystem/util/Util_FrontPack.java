/*     */ package com.cloudstore.packsystem.util;
/*     */ 
/*     */ import com.cloudstore.api.util.Util_Ehcache;
/*     */ import com.cloudstore.api.util.Util_Redis;
/*     */ import com.cloudstore.dev.api.util.Util_Serializer;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.cloudstore.packsystem.bean.ModuleBean;
/*     */ import com.cloudstore.packsystem.constant.ModuleStatusType;
/*     */ import com.cloudstore.packsystem.constant.ModuleType;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.UUID;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.general.TimeUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Util_FrontPack
/*     */ {
/*  48 */   private static final Log logger = LogFactory.getLog(Util_FrontPack.class);
/*     */   
/*  50 */   static Util_Ehcache ec = Util_Ehcache.getIstance();
/*     */   
/*     */   public static final String frontPackModuleKey = "e9:frontPackKey:module:";
/*     */   public static final String frontPackKey = "e9:frontPackKey";
/*     */   
/*     */   protected static String getFrontPath(HttpServletRequest paramHttpServletRequest) {
/*  56 */     String str = paramHttpServletRequest.getSession().getServletContext().getRealPath("/");
/*  57 */     return str + "src4js-pctool-v1";
/*     */   }
/*     */ 
/*     */   
/*     */   protected static String getNodeJsPath(HttpServletRequest paramHttpServletRequest, String paramString) {
/*  62 */     String str = paramHttpServletRequest.getSession().getServletContext().getRealPath("/");
/*  63 */     if (SystemOS.isWindows())
/*  64 */       return str + "node10\\node_win64\\yarn.cmd build --pubModule "; 
/*  65 */     if (SystemOS.isLinux() || SystemOS.isFreeBSD() || SystemOS.isSolaris())
/*  66 */       return str + "node10/node_linux64/yarn build --pubModule "; 
/*  67 */     if (SystemOS.isMacOS() || SystemOS.isMacOSX())
/*  68 */       return str + "node10/node_mac64/yarn build --pubModule "; 
/*  69 */     return "unknow";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String packModule(ModuleType paramModuleType, HttpServletRequest paramHttpServletRequest) throws Exception {
/*  75 */     String str1 = getFrontPath(paramHttpServletRequest);
/*  76 */     String str2 = getNodeJsPath(paramHttpServletRequest, paramModuleType.toString().toLowerCase());
/*  77 */     if ("unknow".equals(str2))
/*  78 */       throw new Exception("不支持的操作系统!"); 
/*  79 */     return packSystem(paramModuleType, str2, str1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static String packSystem(ModuleType paramModuleType, String paramString1, String paramString2) {
/*  84 */     Runtime runtime = Runtime.getRuntime();
/*  85 */     String str = "";
/*     */     
/*     */     try {
/*  88 */       StringBuilder stringBuilder = new StringBuilder();
/*     */       
/*  90 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(runtime.exec(paramString1 + paramModuleType.toString().toLowerCase(), (String[])null, new File(paramString2)).getInputStream()));
/*  91 */       String str1 = null;
/*  92 */       StringBuffer stringBuffer = new StringBuffer();
/*  93 */       while ((str1 = bufferedReader.readLine()) != null) {
/*  94 */         stringBuffer.append(str1 + "\n");
/*  95 */         setCommand(paramModuleType, str.toString());
/*  96 */         logger.info(str1);
/*     */       } 
/*  98 */       str = stringBuffer.toString();
/*  99 */     } catch (Exception exception) {
/* 100 */       exception.printStackTrace();
/* 101 */       str = "build Error!";
/*     */     } 
/*     */     
/* 104 */     return str;
/*     */   }
/*     */   
/*     */   public static void setCommand(ModuleType paramModuleType, String paramString) {
/* 108 */     Util_TableMap.setObjVal("e9:frontPackKey:module:" + paramModuleType.getCode(), paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getCommand(ModuleType paramModuleType) {
/* 113 */     Object object = Util_TableMap.getObjVal("e9:frontPackKey:module:" + paramModuleType.getCode());
/* 114 */     return (object != null) ? (String)object : null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ModuleBean createModule(ModuleType paramModuleType, int paramInt, ModuleStatusType paramModuleStatusType) throws IOException {
/* 127 */     String str1 = TimeUtil.getCurrentTimeString();
/* 128 */     String str2 = str1.substring(0, 10);
/* 129 */     String str3 = str1.substring(11);
/* 130 */     ModuleBean moduleBean = new ModuleBean();
/* 131 */     moduleBean.setModuleType(paramModuleType);
/* 132 */     moduleBean.setUserId(paramInt);
/* 133 */     moduleBean.setStatus(paramModuleStatusType);
/* 134 */     moduleBean.setDate(str2);
/* 135 */     moduleBean.setTime(str3);
/*     */     
/* 137 */     return moduleBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String createGuid() {
/* 148 */     UUID uUID = UUID.randomUUID();
/* 149 */     return uUID.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<ModuleBean> getPackList() throws Exception {
/* 162 */     Object object = Util_TableMap.getObjVal("e9:frontPackKey");
/* 163 */     List<ModuleBean> list = null;
/*     */     
/* 165 */     if (object == null) {
/*     */       
/* 167 */       list = new ArrayList();
/* 168 */       for (ModuleType moduleType : ModuleType.values()) {
/*     */         
/* 170 */         ModuleBean moduleBean = new ModuleBean();
/* 171 */         moduleBean.setModuleType(moduleType);
/* 172 */         moduleBean.setStatus(ModuleStatusType.NONE);
/* 173 */         list.add(moduleBean);
/*     */       } 
/*     */       
/* 176 */       Util_TableMap.setObjVal("e9:frontPackKey", list);
/*     */     }
/*     */     else {
/*     */       
/* 180 */       list = (List)object;
/*     */     } 
/*     */     
/* 183 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ModuleBean getModuleBean(ModuleType paramModuleType) throws Exception {
/* 196 */     Object object = null;
/* 197 */     if (object == null && Util_Redis.getIstance() != null) {
/*     */       
/* 199 */       byte[] arrayOfByte = Util_Redis.getIstance().get(paramModuleType.getName().getBytes());
/* 200 */       object = Util_Serializer.deserialize(arrayOfByte);
/*     */     } 
/*     */ 
/*     */     
/* 204 */     return (object != null) ? (ModuleBean)object : null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/util/Util_FrontPack.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */