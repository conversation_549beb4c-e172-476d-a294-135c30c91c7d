/*    */ package com.cloudstore.packsystem.bean;
/*    */ 
/*    */ import com.cloudstore.packsystem.constant.ModuleStatusType;
/*    */ import com.cloudstore.packsystem.constant.ModuleType;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModuleBean
/*    */ {
/*    */   private ModuleType moduleType;
/*    */   private int userId;
/*    */   private ModuleStatusType status;
/*    */   private String command;
/*    */   private String date;
/*    */   private String time;
/*    */   
/*    */   public int getUserId() {
/* 52 */     return this.userId;
/*    */   }
/*    */   public void setUserId(int paramInt) {
/* 55 */     this.userId = paramInt;
/*    */   }
/*    */   public ModuleType getModuleType() {
/* 58 */     return this.moduleType;
/*    */   }
/*    */   public void setModuleType(ModuleType paramModuleType) {
/* 61 */     this.moduleType = paramModuleType;
/*    */   }
/*    */   public ModuleStatusType getStatus() {
/* 64 */     return this.status;
/*    */   }
/*    */   public void setStatus(ModuleStatusType paramModuleStatusType) {
/* 67 */     this.status = paramModuleStatusType;
/*    */   }
/*    */   public String getCommand() {
/* 70 */     return this.command;
/*    */   }
/*    */   public void setCommand(String paramString) {
/* 73 */     this.command = paramString;
/*    */   }
/*    */   public String getDate() {
/* 76 */     return this.date;
/*    */   }
/*    */   public void setDate(String paramString) {
/* 79 */     this.date = paramString;
/*    */   }
/*    */   public String getTime() {
/* 82 */     return this.time;
/*    */   }
/*    */   public void setTime(String paramString) {
/* 85 */     this.time = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/packsystem/bean/ModuleBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */