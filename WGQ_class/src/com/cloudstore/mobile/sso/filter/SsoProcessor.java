package com.cloudstore.mobile.sso.filter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface SsoProcessor {
  boolean before(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
  
  SsoResult process(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception;
  
  void after(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, SsoResult paramSsoResult);
  
  void throwException(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Throwable paramThrowable);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/filter/SsoProcessor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */