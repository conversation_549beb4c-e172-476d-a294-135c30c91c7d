/*    */ package com.cloudstore.mobile.sso.filter;
/*    */ 
/*    */ import com.cloudstore.mobile.sso.emobile.Emobile7Sha1Processor;
/*    */ import com.cloudstore.mobile.sso.emobile.Emobile7SsoProcessor;
/*    */ import com.cloudstore.mobile.sso.welink.WeLinkSsoProcessor;
/*    */ import java.util.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SsoProcessorFacotory
/*    */ {
/* 17 */   private static volatile SsoProcessorFacotory facotory = null;
/*    */ 
/*    */ 
/*    */   
/*    */   public static SsoProcessorFacotory getInstance() {
/* 22 */     if (Objects.isNull(facotory)) {
/* 23 */       synchronized (SsoProcessorFacotory.class) {
/* 24 */         if (Objects.isNull(facotory)) {
/* 25 */           facotory = new SsoProcessorFacotory();
/*    */         }
/*    */       } 
/*    */     }
/* 29 */     return facotory;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public SsoProcessor createProcessor(String paramString) {
/* 37 */     if ("EMOBILE7".equalsIgnoreCase(paramString)) return (SsoProcessor)new Emobile7SsoProcessor(); 
/* 38 */     if ("WELINK".equalsIgnoreCase(paramString)) return (SsoProcessor)new WeLinkSsoProcessor(); 
/* 39 */     if ("EM_SHA1".equalsIgnoreCase(paramString)) return (SsoProcessor)new Emobile7Sha1Processor(); 
/* 40 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/filter/SsoProcessorFacotory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */