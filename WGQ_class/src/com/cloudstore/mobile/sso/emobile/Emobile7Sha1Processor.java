/*     */ package com.cloudstore.mobile.sso.emobile;
/*     */ 
/*     */ import com.api.common.service.LoginCommonService;
/*     */ import com.api.common.service.impl.LoginCommonServiceImpl;
/*     */ import com.api.system.language.service.LanguageService;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import com.cloudstore.mobile.constant.AuthTypeConstant;
/*     */ import com.cloudstore.mobile.entity.Emobile7Response;
/*     */ import com.cloudstore.mobile.sso.auth.Authenticator;
/*     */ import com.cloudstore.mobile.sso.auth.DefaultAuthenticatorFactory;
/*     */ import com.cloudstore.mobile.sso.filter.AbstractSsoProcessor;
/*     */ import com.cloudstore.mobile.sso.filter.SsoResult;
/*     */ import com.cloudstore.mobile.sso.util.Emobile7SsoUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.core.exception.ECException;
/*     */ import java.io.IOException;
/*     */ import java.sql.Timestamp;
/*     */ import java.time.LocalDateTime;
/*     */ import java.time.ZoneId;
/*     */ import java.time.ZoneOffset;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.Objects;
/*     */ import java.util.Optional;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.interfaces.encode.SHA1;
/*     */ import weaver.interfaces.sso.cas.CASLoginUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Emobile7Sha1Processor
/*     */   extends AbstractSsoProcessor
/*     */ {
/*  45 */   private static final Log logger = LogFactory.getLog(Emobile7Sha1Processor.class);
/*     */   
/*     */   private Authenticator authenticator;
/*     */   
/*     */   public Emobile7Sha1Processor() {
/*  50 */     if (AuthTypeConstant.ENABLE_TOKEN && Objects.isNull(this.authenticator)) {
/*  51 */       this.authenticator = DefaultAuthenticatorFactory.getDefaultAuthenticator();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean before(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  57 */     if (Objects.isNull(paramHttpServletRequest)) return false;
/*     */ 
/*     */     
/*  60 */     if ("HEAD".equalsIgnoreCase(paramHttpServletRequest.getMethod())) return false; 
/*  61 */     if (Objects.isNull(paramHttpServletRequest.getRequestURI())) return false; 
/*  62 */     if ("/".equals(paramHttpServletRequest.getRequestURI())) return false;
/*     */ 
/*     */     
/*  65 */     boolean bool1 = false;
/*     */ 
/*     */     
/*  68 */     if (paramHttpServletRequest.getRequestURI().endsWith(".htm")) return false; 
/*  69 */     if (paramHttpServletRequest.getRequestURI().endsWith(".html")) return false; 
/*  70 */     if (paramHttpServletRequest.getRequestURI().endsWith(".css")) return false; 
/*  71 */     if (paramHttpServletRequest.getRequestURI().endsWith(".js")) return false; 
/*  72 */     if (paramHttpServletRequest.getRequestURI().indexOf("/api/ec/dev/app/refreshToken") >= 0) return false;
/*     */ 
/*     */     
/*  75 */     User user = HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/*  77 */     if (bool1 && Objects.nonNull(user) && 
/*  78 */       user.getLanguage() != bool1) {
/*  79 */       user.setLanguage(bool1);
/*     */     }
/*     */     
/*  82 */     boolean bool2 = false;
/*  83 */     if (Objects.nonNull(paramHttpServletRequest.getQueryString()) && paramHttpServletRequest.getQueryString().indexOf("em_auth_code") >= 0) {
/*  84 */       String str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_code");
/*  85 */       bool2 = (StringUtils.isNotBlank(str) && str.indexOf("_") > 0) ? true : false;
/*     */     } 
/*  87 */     if (Objects.nonNull(user) && bool2) {
/*  88 */       if (Boolean.TRUE.toString().equals(Emobile7SsoUtil.getParameter(paramHttpServletRequest, "is_refresh_token"))) return true; 
/*  89 */       if (Objects.nonNull(paramHttpServletRequest.getQueryString()) && paramHttpServletRequest.getQueryString().indexOf("em_auth_userid") >= 0) {
/*  90 */         String str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_userid");
/*  91 */         if (StringUtils.isNumeric(str) && !String.valueOf(user.getUID()).equals(str)) return true;
/*     */       
/*     */       } 
/*     */     } 
/*     */     
/*  96 */     if (Objects.isNull(user) && bool2) return true; 
/*  97 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public SsoResult process(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 102 */     SsoResult ssoResult = new SsoResult();
/* 103 */     String str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_code");
/* 104 */     if (Objects.nonNull(str) && str.indexOf("_") > -1) {
/* 105 */       String[] arrayOfString = str.split("_");
/* 106 */       if (arrayOfString.length == 4) {
/* 107 */         long l2; String str1 = arrayOfString[0];
/* 108 */         String str2 = arrayOfString[1];
/* 109 */         String str3 = arrayOfString[2];
/* 110 */         String str4 = arrayOfString[3];
/* 111 */         long l1 = Long.valueOf(str2).longValue();
/*     */         
/* 113 */         int i = Util.getIntValue(paramHttpServletRequest.getParameter("timeZoneOffset"), -100);
/* 114 */         if (i != -100) {
/* 115 */           int j = i / 60;
/* 116 */           long l = Timestamp.valueOf(LocalDateTime.now(ZoneId.ofOffset("GMT", ZoneOffset.ofHours(-j)))).getTime();
/* 117 */           l2 = l - l1;
/*     */         } else {
/* 119 */           l2 = System.currentTimeMillis() - l1;
/*     */         } 
/* 121 */         if (l2 >= -(AuthTypeConstant.SHA1_TIMEOUT * 1000) && l2 < (AuthTypeConstant.SHA1_TIMEOUT * 1000)) {
/* 122 */           SHA1 sHA1 = new SHA1();
/* 123 */           String str5 = (String)Optional.<String>ofNullable(sHA1.encode(AuthTypeConstant.SHA1_SECRET + "_" + str1 + "_" + str2)).orElseThrow(() -> new ECException("sha1 userid and tamp encode fail."));
/* 124 */           if (str5.equals(str3)) {
/*     */             
/* 126 */             LoginCommonService loginCommonService = (LoginCommonService)ServiceUtil.getService(LoginCommonServiceImpl.class);
/* 127 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 128 */             hashMap.put("userid", str1);
/* 129 */             hashMap.put("openType", String.valueOf(2));
/* 130 */             loginCommonService.doUserSession(paramHttpServletRequest, paramHttpServletResponse, hashMap);
/*     */             
/* 132 */             Map map = loginCommonService.doUserSession(paramHttpServletRequest, paramHttpServletResponse, hashMap); User user;
/* 133 */             Optional.<User>ofNullable(user = HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse)).orElseThrow(() -> new ECException(
/* 134 */                   (Objects.isNull(paramMap) || Objects.isNull(paramMap.get("errmsg"))) ? "该账号状态存在异常或者ECOLOGY授权过期" : paramMap.get("errmsg").toString()));
/*     */             
/* 136 */             user.setLanguage(LanguageService.getLanguageService().changeLang(user, str4));
/* 137 */             paramHttpServletRequest.setAttribute("@is_cost_code", "1");
/* 138 */             ssoResult.setSuccess(true);
/* 139 */             ssoResult.setAttachment("登录ok.");
/*     */           } else {
/* 141 */             logger.error("server secret is" + str5 + ",client secret is" + str3);
/* 142 */             ssoResult.setSuccess(false);
/* 143 */             ssoResult.setAttachment("em_auth_data is not equals.");
/*     */           } 
/*     */         } else {
/* 146 */           logger.error("server time is" + (System.currentTimeMillis() / 1000L) + ",client time is" + (Long.valueOf(str2).longValue() / 1000L) + "time diff is" + (l2 / 1000L));
/* 147 */           ssoResult.setSuccess(false);
/* 148 */           ssoResult.setAttachment("em_auth_code was timeout.");
/*     */         } 
/*     */       } else {
/* 151 */         ssoResult.setSuccess(false);
/* 152 */         ssoResult.setAttachment("em_auth_code fommat fail.");
/*     */       } 
/*     */     } 
/* 155 */     return ssoResult;
/*     */   }
/*     */ 
/*     */   
/*     */   public void after(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, SsoResult paramSsoResult) {
/* 160 */     if (Objects.nonNull(paramSsoResult)) {
/* 161 */       User user = HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse);
/* 162 */       if (paramSsoResult.isSuccess()) {
/*     */         
/* 164 */         CASLoginUtil.WxDingDingLoginSSO(paramHttpServletRequest, paramHttpServletResponse);
/* 165 */         paramHttpServletRequest.getSession().setAttribute("userLoginFrom", "em");
/* 166 */         if (Objects.nonNull(this.authenticator)) {
/* 167 */           Emobile7Response emobile7Response = new Emobile7Response();
/* 168 */           emobile7Response.setUserId(String.valueOf(user.getUID()));
/* 169 */           emobile7Response.setOpenType(1);
/* 170 */           this.authenticator.sign(paramHttpServletRequest, paramHttpServletResponse, emobile7Response);
/*     */         } 
/* 172 */         if (logger.isInfoEnabled()) {
/* 173 */           String str1 = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_code");
/* 174 */           String str2 = user.getFirstname() + user.getLastname() + user.getUID();
/* 175 */           String str3 = Util.null2String(paramHttpServletRequest.getAttribute("@is_cost_code"));
/* 176 */           String str4 = StringUtils.equals("1", str3) ? str1 : Emobile7SsoUtil.getAccessToken(paramHttpServletRequest, AuthTypeConstant.MOBILE_TOKEN_NAME);
/* 177 */           logger.info(String.format("用户登录ok,当前用户：%s,当前sessionId:%s,认证方式：%s,认证标识：%s", new Object[] { str2, paramHttpServletRequest.getSession().getId(), str3, str4 }));
/*     */         } 
/* 179 */       } else if (!paramSsoResult.isSuccess()) {
/* 180 */         if (Objects.nonNull(user) || Objects.isNull(paramSsoResult.getAttachment())) {
/* 181 */           paramSsoResult.setSuccess(true);
/*     */         } else {
/* 183 */           WeaResultMsg weaResultMsg = new WeaResultMsg(false);
/*     */           try {
/* 185 */             Emobile7SsoUtil.writeResponse(paramHttpServletRequest, paramHttpServletResponse, weaResultMsg.fail((String)paramSsoResult.getAttachment()));
/* 186 */           } catch (IOException iOException) {
/* 187 */             iOException.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void throwException(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Throwable paramThrowable) {
/* 196 */     if (Objects.nonNull(paramThrowable)) {
/* 197 */       paramThrowable.printStackTrace();
/* 198 */       logger.error(paramThrowable.getMessage());
/* 199 */       WeaResultMsg weaResultMsg = new WeaResultMsg(false);
/*     */       try {
/* 201 */         Emobile7SsoUtil.writeResponse(paramHttpServletRequest, paramHttpServletResponse, weaResultMsg.fail(paramThrowable.getMessage()));
/* 202 */       } catch (IOException iOException) {
/* 203 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/emobile/Emobile7Sha1Processor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */