/*     */ package com.cloudstore.mobile.sso.emobile;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.common.service.LoginCommonService;
/*     */ import com.api.common.service.impl.LoginCommonServiceImpl;
/*     */ import com.api.system.language.service.LanguageService;
/*     */ import com.cloudstore.dev.api.util.EMManager;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import com.cloudstore.mobile.constant.AuthTypeConstant;
/*     */ import com.cloudstore.mobile.entity.Emobile7Response;
/*     */ import com.cloudstore.mobile.sso.auth.Authenticator;
/*     */ import com.cloudstore.mobile.sso.auth.DefaultAuthenticatorFactory;
/*     */ import com.cloudstore.mobile.sso.filter.AbstractSsoProcessor;
/*     */ import com.cloudstore.mobile.sso.filter.SsoResult;
/*     */ import com.cloudstore.mobile.sso.util.Emobile7SsoUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.core.exception.ECException;
/*     */ import java.io.IOException;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.Objects;
/*     */ import java.util.Optional;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.sso.cas.CASLoginUtil;
/*     */ import weaver.rsa.security.RSA;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Emobile7SsoProcessor
/*     */   extends AbstractSsoProcessor
/*     */ {
/*  45 */   private static final Log logger = LogFactory.getLog(Emobile7SsoProcessor.class);
/*     */   
/*     */   private AuthCodeConsumer consumer;
/*     */   
/*     */   private Authenticator authenticator;
/*     */   
/*     */   public Emobile7SsoProcessor() {
/*  52 */     init();
/*     */   }
/*     */   
/*     */   private void init() {
/*  56 */     AuthTypeConstant.MOBILE_TOKEN_NAME = Emobile7SsoUtil.null2String(AuthTypeConstant.MOBILE_TOKEN_NAME, "mobiletoken");
/*  57 */     if (Objects.isNull(this.consumer)) this.consumer = new AuthCodeConsumer(); 
/*  58 */     if (AuthTypeConstant.ENABLE_TOKEN && Objects.isNull(this.authenticator))
/*  59 */       this.authenticator = DefaultAuthenticatorFactory.getDefaultAuthenticator(); 
/*  60 */     this.consumer.start();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean before(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  66 */     if (Objects.isNull(paramHttpServletRequest)) return false;
/*     */ 
/*     */     
/*  69 */     String str1 = paramHttpServletRequest.getRequestURI();
/*  70 */     if ("HEAD".equalsIgnoreCase(paramHttpServletRequest.getMethod())) return false; 
/*  71 */     if (Objects.isNull(str1)) return false; 
/*  72 */     if ("/".equals(str1)) return false;
/*     */ 
/*     */     
/*  75 */     boolean bool1 = false;
/*     */     
/*  77 */     if (str1.endsWith(".htm")) return false; 
/*  78 */     if (str1.endsWith(".html")) return false; 
/*  79 */     if (str1.endsWith(".css")) return false; 
/*  80 */     if (str1.endsWith(".js")) return false; 
/*  81 */     if (str1.indexOf("/api/ec/dev/app/refreshToken") >= 0) return false; 
/*  82 */     if (str1.indexOf(".") >= 0 && !str1.endsWith(".htm") && !str1.endsWith(".html") && !str1.endsWith(".jsp") && str1.length() - str1.replaceAll("\\.", "").length() == 1) {
/*  83 */       return false;
/*     */     }
/*  85 */     boolean bool2 = false;
/*  86 */     String str2 = null;
/*  87 */     if (Objects.nonNull(paramHttpServletRequest.getQueryString()) && paramHttpServletRequest.getQueryString().indexOf("em_auth_code") >= 0) {
/*  88 */       str2 = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_code");
/*  89 */       bool2 = (StringUtils.isNotBlank(str2) && str2.indexOf("_") < 0) ? true : false;
/*     */     } 
/*  91 */     boolean bool = false;
/*  92 */     if (!bool2) {
/*  93 */       str2 = paramHttpServletRequest.getHeader("emheadercode");
/*  94 */       bool2 = ((bool = StringUtils.isNotBlank(str2)) && str2.indexOf("_") < 0) ? true : false;
/*     */     } 
/*     */ 
/*     */     
/*  98 */     User user = HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/* 100 */     if (bool1 && Objects.nonNull(user) && 
/* 101 */       user.getLanguage() != bool1) {
/* 102 */       user.setLanguage(bool1);
/*     */     }
/*     */     
/* 105 */     if (Objects.nonNull(user) && bool2) {
/* 106 */       if (Boolean.TRUE.toString().equals(Emobile7SsoUtil.getParameter(paramHttpServletRequest, "is_refresh_token"))) return true; 
/* 107 */       if (Objects.nonNull(paramHttpServletRequest.getHeader("emheaderuserid")) || (Objects.nonNull(paramHttpServletRequest.getQueryString()) && paramHttpServletRequest.getQueryString().indexOf("em_auth_userid") >= 0)) {
/*     */         String str;
/* 109 */         if (bool) {
/* 110 */           str = paramHttpServletRequest.getHeader("emheaderuserid");
/*     */         } else {
/* 112 */           str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_userid");
/*     */         } 
/* 114 */         if (StringUtils.isNumeric(str) && !String.valueOf(user.getUID()).equals(str)) return true; 
/* 115 */         if (Objects.nonNull(this.consumer) && !bool) this.consumer.consume(str2); 
/* 116 */       } else if (Objects.nonNull(this.consumer) && !bool) {
/* 117 */         this.consumer.consume(str2);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 122 */     if (Objects.isNull(user)) {
/*     */       
/* 124 */       if (bool2 && Objects.nonNull(EMManager.getEMData().get("ec_url"))) return true;
/*     */       
/* 126 */       if (AuthTypeConstant.ENABLE_TOKEN && Emobile7SsoUtil.containKey(paramHttpServletRequest, AuthTypeConstant.MOBILE_TOKEN_NAME))
/* 127 */         return true; 
/*     */     } 
/* 129 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public SsoResult process(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 134 */     SsoResult ssoResult = new SsoResult();
/* 135 */     String str = paramHttpServletRequest.getHeader("emheadercode");
/* 136 */     if (StringUtils.isBlank(str)) {
/* 137 */       str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_code");
/*     */     }
/* 139 */     if (StringUtils.isBlank(str) && Objects.nonNull(this.authenticator) && Emobile7SsoUtil.containKey(paramHttpServletRequest, AuthTypeConstant.MOBILE_TOKEN_NAME)) {
/* 140 */       return this.authenticator.authenticate(paramHttpServletRequest, paramHttpServletResponse);
/*     */     }
/* 142 */     WeaResultMsg weaResultMsg = new WeaResultMsg(false);
/* 143 */     weaResultMsg.put("em_auth_code", str);
/* 144 */     Emobile7Response emobile7Response = Emobile7SsoUtil.buildResponse(EMManager.getUserInfo(str, EMManager.getAccessToken()));
/*     */     
/* 146 */     if (42001 == emobile7Response.getErrcode() || 40001 == emobile7Response.getErrcode()) {
/* 147 */       String str1 = EMManager.getAccessToken(false);
/* 148 */       if (Objects.nonNull(str1)) {
/* 149 */         logger.info("第二次 access_token登录ok");
/* 150 */         emobile7Response = Emobile7SsoUtil.buildResponse(EMManager.getUserInfo(str, str1));
/*     */       } else {
/* 152 */         EMManager.setJoinStatus(false);
/* 153 */         logger.error("access_token获取失败");
/* 154 */         ssoResult.setAttachment(weaResultMsg.fail("access_token" + SystemEnv.getHtmlLabelName(127889, ThreadVarLanguage.getLang()) + ""));
/* 155 */         return ssoResult;
/*     */       } 
/*     */     } 
/* 158 */     if (0 == emobile7Response.getErrcode())
/* 159 */     { String str1 = emobile7Response.getUserId();
/* 160 */       String str2 = emobile7Response.getMainUserId();
/* 161 */       boolean bool = false;
/*     */       
/* 163 */       if (StringUtils.isBlank(str2)) bool = true; 
/* 164 */       if (!bool && StringUtils.isNotBlank(str1) && str1.equals(str2)) {
/* 165 */         bool = true;
/*     */       }
/* 167 */       RSA rSA = new RSA();
/* 168 */       str1 = rSA.decrypt(null, str1, true);
/* 169 */       if (!bool) {
/* 170 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 171 */         if ("1".equals(resourceComInfo.getAccountType(str1))) {
/* 172 */           str2 = rSA.decrypt(null, str2, true);
/* 173 */           if (!str2.equals(resourceComInfo.getBelongTo(str1))) {
/* 174 */             logger.error("主账号与OA系统主次账号信息不符,次账号：" + str1 + ",主账号：" + str2 + ",OA系统主账号：" + resourceComInfo.getBelongTo(str1));
/* 175 */             ssoResult.setAttachment(weaResultMsg.fail("" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()) + ""));
/* 176 */             return ssoResult;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/* 181 */       LoginCommonService loginCommonService = (LoginCommonService)ServiceUtil.getService(LoginCommonServiceImpl.class);
/* 182 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 183 */       hashMap.put("userid", str1);
/* 184 */       hashMap.put("openType", String.valueOf(emobile7Response.getOpenType()));
/* 185 */       hashMap.put("CpType", emobile7Response.getCpType());
/* 186 */       hashMap.put("ClientType", Integer.valueOf(emobile7Response.getClientType()));
/* 187 */       Map map = loginCommonService.doUserSession(paramHttpServletRequest, paramHttpServletResponse, hashMap);
/* 188 */       Optional.<User>ofNullable(HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse)).orElseThrow(() -> new ECException(
/* 189 */             (Objects.isNull(paramMap) || Objects.isNull(paramMap.get("errmsg"))) ? "该账号状态存在异常或者ECOLOGY授权过期" : paramMap.get("errmsg").toString()));
/*     */       
/* 191 */       paramHttpServletRequest.setAttribute("@is_cost_code", "1");
/* 192 */       ssoResult.setSuccess(true);
/* 193 */       ssoResult.setAttachment(emobile7Response); }
/* 194 */     else { if (Objects.nonNull(this.authenticator) && Emobile7SsoUtil.containKey(paramHttpServletRequest, AuthTypeConstant.MOBILE_TOKEN_NAME)) {
/* 195 */         return this.authenticator.authenticate(paramHttpServletRequest, paramHttpServletResponse);
/*     */       }
/* 197 */       ssoResult.setSuccess(false);
/* 198 */       ssoResult.setAttachment(emobile7Response); }
/*     */ 
/*     */     
/* 201 */     return ssoResult;
/*     */   }
/*     */ 
/*     */   
/*     */   public void after(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, SsoResult paramSsoResult) {
/* 206 */     if (Objects.nonNull(paramSsoResult)) {
/* 207 */       User user = HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse);
/* 208 */       if (paramSsoResult.isSuccess() && paramSsoResult.getAttachment() instanceof Emobile7Response) {
/* 209 */         Emobile7Response emobile7Response = (Emobile7Response)paramSsoResult.getAttachment();
/* 210 */         if (Objects.nonNull(emobile7Response.getPdata())) user.setPwd(emobile7Response.getPdata()); 
/* 211 */         if (Objects.nonNull(emobile7Response.getJemUrl()))
/* 212 */           paramHttpServletRequest.getSession().setAttribute("jemUrl", emobile7Response.getJemUrl()); 
/* 213 */         if (Objects.nonNull(emobile7Response.getDeviceId()))
/* 214 */           paramHttpServletRequest.getSession().setAttribute("DeviceId", emobile7Response.getDeviceId()); 
/* 215 */         if (Objects.nonNull(emobile7Response.getLangType()))
/* 216 */           user.setLanguage(LanguageService.getLanguageService().changeLang(user, emobile7Response.getLangType())); 
/* 217 */         paramHttpServletRequest.getSession().setAttribute("weaver_user@bean", user);
/* 218 */         Emobile7SsoUtil.writeCookies(paramHttpServletRequest, paramHttpServletResponse, emobile7Response.getCdata());
/*     */         
/* 220 */         CASLoginUtil.WxDingDingLoginSSO(paramHttpServletRequest, paramHttpServletResponse);
/*     */         
/* 222 */         paramHttpServletRequest.getSession().setAttribute("@openType", Integer.valueOf(emobile7Response.getOpenType()));
/* 223 */         paramHttpServletRequest.getSession().setAttribute("userLoginFrom", "em");
/* 224 */         if (emobile7Response.getOpenType() == 1 && Objects.nonNull(this.authenticator)) {
/* 225 */           emobile7Response.setUserId(String.valueOf(user.getUID()));
/* 226 */           this.authenticator.sign(paramHttpServletRequest, paramHttpServletResponse, emobile7Response);
/*     */         } 
/* 228 */         String str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "em_auth_code");
/*     */         
/* 230 */         if (Objects.nonNull(this.consumer)) this.consumer.consume(str, true); 
/* 231 */         if (logger.isInfoEnabled()) {
/* 232 */           String str1 = user.getFirstname() + user.getLastname() + user.getUID();
/* 233 */           String str2 = Util.null2String(paramHttpServletRequest.getAttribute("@is_cost_code"));
/* 234 */           String str3 = StringUtils.equals("1", str2) ? str : Emobile7SsoUtil.getAccessToken(paramHttpServletRequest, AuthTypeConstant.MOBILE_TOKEN_NAME);
/* 235 */           logger.info(String.format("用户登录ok,当前用户：%s,当前sessionId:%s,认证方式：%s,认证标识：%s", new Object[] { str1, paramHttpServletRequest.getSession().getId(), str2, str3 }));
/*     */         } 
/* 237 */       } else if (!paramSsoResult.isSuccess()) {
/* 238 */         if (Objects.nonNull(user) || Objects.isNull(paramSsoResult.getAttachment())) {
/* 239 */           paramSsoResult.setSuccess(true);
/* 240 */         } else if (Objects.nonNull(paramSsoResult.getAttachment()) && paramSsoResult.getAttachment() instanceof WeaResultMsg) {
/*     */           try {
/* 242 */             Emobile7SsoUtil.writeResponse(paramHttpServletRequest, paramHttpServletResponse, (WeaResultMsg)paramSsoResult.getAttachment());
/*     */           }
/* 244 */           catch (IOException iOException) {
/* 245 */             iOException.printStackTrace();
/*     */           } 
/* 247 */         } else if (Objects.nonNull(paramSsoResult.getAttachment()) && paramSsoResult.getAttachment() instanceof Emobile7Response) {
/* 248 */           String str = JSONObject.toJSONString(paramSsoResult.getAttachment());
/* 249 */           logger.error("获取用户信息失败：" + str);
/*     */           try {
/* 251 */             Emobile7SsoUtil.writeResponse(paramHttpServletRequest, paramHttpServletResponse, (new WeaResultMsg(false)).fail(str));
/* 252 */           } catch (IOException iOException) {
/* 253 */             iOException.printStackTrace();
/*     */           } 
/*     */         } else {
/* 256 */           WeaResultMsg weaResultMsg = new WeaResultMsg(false);
/*     */           try {
/* 258 */             Emobile7SsoUtil.writeResponse(paramHttpServletRequest, paramHttpServletResponse, weaResultMsg.fail((String)paramSsoResult.getAttachment()));
/* 259 */           } catch (IOException iOException) {
/* 260 */             iOException.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void throwException(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Throwable paramThrowable) {
/* 269 */     if (Objects.nonNull(paramThrowable)) {
/* 270 */       paramThrowable.printStackTrace();
/* 271 */       logger.error(paramThrowable.getMessage());
/* 272 */       WeaResultMsg weaResultMsg = new WeaResultMsg(false);
/*     */       try {
/* 274 */         Emobile7SsoUtil.writeResponse(paramHttpServletRequest, paramHttpServletResponse, weaResultMsg.fail(paramThrowable.getMessage()));
/* 275 */       } catch (IOException iOException) {
/* 276 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/emobile/Emobile7SsoProcessor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */