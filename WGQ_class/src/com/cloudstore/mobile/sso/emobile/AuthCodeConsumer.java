/*    */ package com.cloudstore.mobile.sso.emobile;
/*    */ 
/*    */ import com.cloudstore.api.util.Util_Redis;
/*    */ import com.cloudstore.dev.api.util.EMManager;
/*    */ import com.cloudstore.dev.api.util.Util_DataMap;
/*    */ import java.util.concurrent.ArrayBlockingQueue;
/*    */ import java.util.concurrent.BlockingQueue;
/*    */ import java.util.concurrent.LinkedBlockingQueue;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AuthCodeConsumer
/*    */   extends Thread
/*    */ {
/*    */   private static BlockingQueue<String> unUsedQueue;
/*    */   private static BlockingQueue<String> beenUsedQueue;
/*    */   private static volatile boolean stop = false;
/*    */   private static final int TIME_OUT = 120;
/*    */   
/*    */   AuthCodeConsumer() {
/* 28 */     this; unUsedQueue = new LinkedBlockingQueue<>();
/* 29 */     this; beenUsedQueue = new ArrayBlockingQueue<>(127);
/*    */   }
/*    */   
/*    */   public void consume(String paramString) {
/* 33 */     consume(paramString, false);
/*    */   }
/*    */   
/*    */   public void consume(String paramString, boolean paramBoolean) {
/* 37 */     if (StringUtils.isBlank(paramString)) {
/*    */       return;
/*    */     }
/* 40 */     if (paramBoolean) {
/* 41 */       if (Util_Redis.getIstance() != null) {
/* 42 */         Util_DataMap.setValWithRedis(paramString, "", 120);
/* 43 */       } else if (!beenUsedQueue.offer(paramString)) {
/* 44 */         beenUsedQueue.poll();
/* 45 */         beenUsedQueue.offer(paramString);
/*    */       } 
/*    */     } else {
/* 48 */       unUsedQueue.add(paramString);
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/* 54 */     while (!stop) {
/*    */       try {
/* 56 */         String str = unUsedQueue.take();
/* 57 */         if (str != null) {
/* 58 */           if (Util_Redis.getIstance() != null) {
/* 59 */             if (Util_DataMap.getValWithRedis(str) == null) {
/* 60 */               EMManager.getUserInfo(str, EMManager.getAccessToken());
/* 61 */               Util_DataMap.setValWithRedis(str, "", 120);
/*    */             }  continue;
/* 63 */           }  if (!beenUsedQueue.contains(str)) {
/* 64 */             EMManager.getUserInfo(str, EMManager.getAccessToken());
/* 65 */             if (!beenUsedQueue.offer(str)) {
/* 66 */               beenUsedQueue.poll();
/* 67 */               beenUsedQueue.offer(str);
/*    */             } 
/*    */           } 
/*    */         } 
/* 71 */       } catch (Exception exception) {
/* 72 */         exception.printStackTrace();
/*    */       } 
/*    */     } 
/*    */   }
/*    */   
/*    */   public static void shutdown(boolean paramBoolean) {
/* 78 */     stop = paramBoolean;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/emobile/AuthCodeConsumer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */