/*    */ package com.cloudstore.mobile.sso;
/*    */ 
/*    */ import com.cloudstore.mobile.sso.filter.SsoProcessor;
/*    */ import com.cloudstore.mobile.sso.filter.SsoProcessorFacotory;
/*    */ import com.cloudstore.mobile.sso.filter.SsoResult;
/*    */ import java.io.IOException;
/*    */ import java.util.Objects;
/*    */ import javax.servlet.FilterChain;
/*    */ import javax.servlet.FilterConfig;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletRequest;
/*    */ import javax.servlet.ServletResponse;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.springframework.web.filter.GenericFilterBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MobileSsoFilter
/*    */   extends GenericFilterBean
/*    */ {
/*    */   private SsoProcessor processor;
/*    */   
/*    */   public final void doFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws ServletException, IOException {
/* 31 */     if (paramServletRequest instanceof HttpServletRequest && paramServletResponse instanceof HttpServletResponse) {
/* 32 */       HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/* 33 */       HttpServletResponse httpServletResponse = (HttpServletResponse)paramServletResponse;
/* 34 */       if (Objects.nonNull(this.processor) && this.processor.before(httpServletRequest, httpServletResponse)) {
/*    */         try {
/* 36 */           SsoResult ssoResult = this.processor.process(httpServletRequest, httpServletResponse);
/* 37 */           this.processor.after(httpServletRequest, httpServletResponse, ssoResult);
/* 38 */           if (Objects.nonNull(ssoResult) && ssoResult.isSuccess()) {
/* 39 */             paramFilterChain.doFilter((ServletRequest)httpServletRequest, (ServletResponse)httpServletResponse);
/*    */           }
/* 41 */         } catch (Exception exception) {
/* 42 */           this.processor.throwException(httpServletRequest, httpServletResponse, exception);
/*    */           return;
/*    */         } 
/*    */       } else {
/* 46 */         paramFilterChain.doFilter(paramServletRequest, paramServletResponse);
/*    */       } 
/*    */     } else {
/* 49 */       throw new ServletException("MobileSsoFilter just supports HTTP requests");
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   protected void initFilterBean() throws ServletException {
/* 55 */     FilterConfig filterConfig = getFilterConfig();
/* 56 */     String str = filterConfig.getInitParameter("AUTH_TYPE");
/* 57 */     this.processor = SsoProcessorFacotory.getInstance().createProcessor(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/MobileSsoFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */