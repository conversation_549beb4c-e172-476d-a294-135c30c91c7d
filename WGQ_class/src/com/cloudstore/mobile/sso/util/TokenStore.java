/*     */ package com.cloudstore.mobile.sso.util;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.cloudstore.api.util.Util_DateTime;
/*     */ import com.cloudstore.api.util.Util_Ehcache;
/*     */ import com.cloudstore.api.util.Util_Redis;
/*     */ import com.cloudstore.eccom.common.WeaIndexManager;
/*     */ import com.cloudstore.mobile.constant.AuthTypeConstant;
/*     */ import com.cloudstore.mobile.entity.Emobile7Response;
/*     */ import com.cloudstore.mobile.sso.auth.AuthTokenBean;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.Objects;
/*     */ import java.util.Timer;
/*     */ import java.util.TimerTask;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TokenStore
/*     */ {
/*  31 */   private static final Log logger = LogFactory.getLog(TokenStore.class);
/*     */ 
/*     */ 
/*     */   
/*     */   private static volatile boolean inited;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String generateAccessToken(Object paramObject) {
/*  41 */     return generateAccessToken(paramObject, AuthTypeConstant.MOBILE_SESSION_MIN_AGE);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String generateAccessToken(Object paramObject, int paramInt) {
/*  49 */     String str = WeaIndexManager.getGuid();
/*  50 */     if (Objects.isNull(Util_Redis.getInstance())) {
/*  51 */       AuthTokenBean authTokenBean = new AuthTokenBean();
/*  52 */       authTokenBean.setUuid(str);
/*  53 */       authTokenBean.setMsg(paramObject);
/*  54 */       Calendar calendar = Calendar.getInstance();
/*  55 */       calendar.setTime(new Date());
/*  56 */       calendar.add(13, paramInt);
/*  57 */       authTokenBean.setExpireTime((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(calendar.getTime()));
/*  58 */       authTokenBean.setType(1);
/*  59 */       paramObject = authTokenBean;
/*     */     } 
/*  61 */     save(wrapKey(str, 1), paramObject, paramInt, false);
/*  62 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String generateRefreshToken(Object paramObject) {
/*  69 */     return generateRefreshToken(paramObject, AuthTypeConstant.MOBILE_SESSION_MAX_AGE);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String generateRefreshToken(Object paramObject, int paramInt) {
/*  77 */     String str = WeaIndexManager.getGuid();
/*  78 */     if (Objects.isNull(Util_Redis.getInstance())) {
/*  79 */       AuthTokenBean authTokenBean = new AuthTokenBean();
/*  80 */       authTokenBean.setUuid(str);
/*  81 */       authTokenBean.setMsg(paramObject);
/*  82 */       Calendar calendar = Calendar.getInstance();
/*  83 */       calendar.setTime(new Date());
/*  84 */       calendar.add(13, paramInt);
/*  85 */       authTokenBean.setExpireTime((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(calendar.getTime()));
/*  86 */       authTokenBean.setType(2);
/*  87 */       paramObject = authTokenBean;
/*     */     } 
/*  89 */     save(wrapKey(str, 2), paramObject, paramInt, true);
/*  90 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void save(String paramString, Object paramObject, int paramInt, boolean paramBoolean) {
/*  97 */     if (Objects.nonNull(Util_Redis.getInstance())) {
/*  98 */       if (Objects.nonNull(paramObject) && paramObject instanceof String) {
/*  99 */         Util_Redis.getInstance().set(paramString, (String)paramObject, paramInt);
/*     */       } else {
/* 101 */         Util_Redis.getInstance().set(paramString, JSONObject.toJSONString(paramObject), paramInt);
/*     */       } 
/*     */     } else {
/* 104 */       if (paramBoolean)
/* 105 */         Util_Ehcache.getInstance().put(paramString, paramObject); 
/* 106 */       if (paramObject instanceof AuthTokenBean) {
/* 107 */         storeToken2DB((AuthTokenBean)paramObject);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean exists(String paramString, int paramInt) {
/* 118 */     String str = wrapKey(paramString, paramInt);
/* 119 */     if (Objects.nonNull(Util_Redis.getInstance())) {
/* 120 */       return Util_Redis.getInstance().exists(str);
/*     */     }
/* 122 */     if (!Util_Ehcache.getInstance().containsKey(str)) {
/* 123 */       return expiresFromDB(paramString, paramInt);
/*     */     }
/* 125 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object get(String paramString, int paramInt) {
/* 132 */     String str = wrapKey(paramString, paramInt);
/* 133 */     if (Objects.nonNull(Util_Redis.getInstance())) {
/* 134 */       String str1 = Util_Redis.getInstance().get(str);
/* 135 */       if (JSONObject.isValidObject(str1)) {
/* 136 */         Emobile7Response emobile7Response = (Emobile7Response)JSONObject.parseObject(str1, Emobile7Response.class);
/* 137 */         if (Objects.nonNull(emobile7Response)) {
/* 138 */           return emobile7Response;
/*     */         }
/*     */       } 
/* 141 */       return str1;
/*     */     } 
/* 143 */     Object object = Util_Ehcache.getInstance().get(str);
/* 144 */     if (Objects.isNull(object)) {
/* 145 */       object = getTokenFromDB(paramString, paramInt);
/*     */     }
/* 147 */     if (Objects.nonNull(object) && object instanceof AuthTokenBean) {
/* 148 */       return ((AuthTokenBean)object).getMsg();
/*     */     }
/*     */     
/* 151 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Object getMsg(String paramString) {
/* 157 */     return get(paramString, 1);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void storeToken2DB(AuthTokenBean paramAuthTokenBean) {
/* 163 */     if (Objects.nonNull(paramAuthTokenBean)) {
/* 164 */       if (Objects.isNull(Util_Redis.getInstance()) && !inited) {
/* 165 */         inited = true;
/*     */         try {
/* 167 */           Timer timer = new Timer();
/* 168 */           timer.schedule(new TimerTask()
/*     */               {
/*     */                 public void run() {
/* 171 */                   TokenStore.logger.info("clear mobile token task begin...");
/* 172 */                   TokenStore.clear();
/* 173 */                   TokenStore.logger.info("clear mobile token task end...");
/*     */                 }
/*     */               },  5000L, 43200000L);
/* 176 */         } catch (Exception exception) {
/* 177 */           exception.printStackTrace();
/* 178 */           logger.error("clear mobile token task begin fail...e:" + exception.getMessage());
/* 179 */           inited = false;
/* 180 */           clear();
/*     */         } 
/* 182 */         logger.info("clear mobile token task is start success...");
/*     */       } 
/* 184 */       RecordSet recordSet = new RecordSet();
/* 185 */       String str = "insert into ecology_mobile_token(uuid,auth_type,expire_time,msg) values(?,?,?,?)";
/* 186 */       recordSet.executeUpdate(str, new Object[] { paramAuthTokenBean.getUuid(), Integer.valueOf(paramAuthTokenBean.getType()), paramAuthTokenBean.getExpireTime(), JSONObject.toJSONString(paramAuthTokenBean.getMsg()) });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private AuthTokenBean getTokenFromDB(String paramString, int paramInt) {
/* 193 */     if (Objects.nonNull(paramString)) {
/* 194 */       RecordSet recordSet = new RecordSet();
/* 195 */       String str = "select * from ecology_mobile_token where uuid=? and auth_type=? and expire_time>=?";
/* 196 */       recordSet.executeQuery(str, new Object[] { paramString, Integer.valueOf(paramInt), Util_DateTime.getNowDateTime() });
/* 197 */       if (recordSet.next()) {
/* 198 */         AuthTokenBean authTokenBean = new AuthTokenBean();
/* 199 */         authTokenBean.setUuid(paramString);
/* 200 */         authTokenBean.setType(paramInt);
/* 201 */         authTokenBean.setExpireTime(recordSet.getString("expire_time"));
/* 202 */         String str1 = recordSet.getString("msg");
/*     */         
/*     */         try {
/* 205 */           authTokenBean.setMsg(JSONObject.parseObject(str1, Emobile7Response.class));
/* 206 */         } catch (Exception exception) {
/* 207 */           if (str1.startsWith("\"") && str1.endsWith("\"")) {
/* 208 */             str1 = str1.substring(1, str1.length() - 1);
/*     */           }
/* 210 */           authTokenBean.setMsg(str1);
/*     */         } 
/* 212 */         return authTokenBean;
/*     */       } 
/*     */     } 
/* 215 */     return null;
/*     */   }
/*     */   
/*     */   private final String wrapKey(String paramString, int paramInt) {
/* 219 */     return (paramInt == 2) ? ("m_refresh_token:" + paramString) : ("m_access_token:" + paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean expiresFromDB(String paramString, int paramInt) {
/* 226 */     if (StringUtils.isNotBlank(paramString)) {
/* 227 */       RecordSet recordSet = new RecordSet();
/* 228 */       String str = "select uuid from ecology_mobile_token where uuid=? and auth_type=? and expire_time>=?";
/* 229 */       recordSet.executeQuery(str, new Object[] { paramString, Integer.valueOf(paramInt), Util_DateTime.getNowDateTime() });
/* 230 */       return recordSet.next();
/*     */     } 
/* 232 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean clear() {
/* 239 */     RecordSet recordSet = new RecordSet();
/* 240 */     recordSet.executeUpdate("delete from ecology_mobile_token where expire_time<? ", new Object[] { Util_DateTime.getNowDateTime() });
/* 241 */     return recordSet.next();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/util/TokenStore.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */