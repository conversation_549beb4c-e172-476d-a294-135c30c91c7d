/*    */ package com.cloudstore.mobile.sso.welink;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.api.common.service.LoginCommonService;
/*    */ import com.api.common.service.impl.LoginCommonServiceImpl;
/*    */ import com.cloudstore.eccom.result.WeaResultMsg;
/*    */ import com.cloudstore.mobile.entity.Emobile7Response;
/*    */ import com.cloudstore.mobile.rpc.WeLinkUserApi;
/*    */ import com.cloudstore.mobile.sso.emobile.Emobile7SsoProcessor;
/*    */ import com.cloudstore.mobile.sso.filter.SsoResult;
/*    */ import com.cloudstore.mobile.sso.util.Emobile7SsoUtil;
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import com.engine.core.exception.ECException;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import java.util.Objects;
/*    */ import java.util.Optional;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WeLinkSsoProcessor
/*    */   extends Emobile7SsoProcessor
/*    */ {
/*    */   public boolean before(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 37 */     if (!"/api/ec/dev/app/test".equals(paramHttpServletRequest.getRequestURI())) return false; 
/* 38 */     if (Objects.nonNull(paramHttpServletRequest.getQueryString()) && paramHttpServletRequest.getQueryString().indexOf("we_link_auth_code") >= 0) {
/* 39 */       String str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "we_link_auth_code");
/* 40 */       if (StringUtils.isNotBlank(str)) {
/* 41 */         return Objects.isNull(HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse));
/*    */       }
/*    */     } 
/* 44 */     return false;
/*    */   }
/*    */ 
/*    */   
/*    */   public SsoResult process(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 49 */     SsoResult ssoResult = new SsoResult();
/* 50 */     String str = Emobile7SsoUtil.getParameter(paramHttpServletRequest, "we_link_auth_code");
/* 51 */     if (StringUtils.isNotBlank(str)) {
/* 52 */       WeLinkUserApi weLinkUserApi = new WeLinkUserApi();
/* 53 */       JSONObject jSONObject = weLinkUserApi.getOutSysUserByOpenUserCode(str);
/*    */       int i;
/* 55 */       if (Objects.nonNull(jSONObject) && 0 == jSONObject.getIntValue("errorcode") && (i = jSONObject.getIntValue("outsys_user_id")) > 0) {
/* 56 */         Emobile7Response emobile7Response = new Emobile7Response();
/* 57 */         emobile7Response.setUserId(String.valueOf(i));
/* 58 */         emobile7Response.setOpenType(1);
/*    */         
/* 60 */         LoginCommonService loginCommonService = (LoginCommonService)ServiceUtil.getService(LoginCommonServiceImpl.class);
/* 61 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 62 */         hashMap.put("userid", String.valueOf(i));
/* 63 */         hashMap.put("openType", String.valueOf(emobile7Response.getOpenType()));
/* 64 */         loginCommonService.doUserSession(paramHttpServletRequest, paramHttpServletResponse, hashMap);
/* 65 */         Map map = loginCommonService.doUserSession(paramHttpServletRequest, paramHttpServletResponse, hashMap);
/* 66 */         Optional.<User>ofNullable(HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse)).orElseThrow(() -> new ECException(
/* 67 */               (Objects.isNull(paramMap) || Objects.isNull(paramMap.get("errmsg"))) ? "该账号状态存在异常或者ECOLOGY授权过期" : paramMap.get("errmsg").toString()));
/*    */         
/* 69 */         paramHttpServletRequest.setAttribute("@is_cost_code", "4");
/* 70 */         ssoResult.setSuccess(true);
/* 71 */         ssoResult.setAttachment(emobile7Response);
/* 72 */       } else if (Objects.nonNull(jSONObject)) {
/* 73 */         ssoResult.setAttachment(jSONObject.toJSONString());
/*    */       } else {
/* 75 */         ssoResult.setAttachment((new WeaResultMsg(false)).fail("getOutSysUserByOpenUserCode" + SystemEnv.getHtmlLabelName(10004810, ThreadVarLanguage.getLang()) + "."));
/*    */       } 
/*    */     } 
/* 78 */     return ssoResult;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/sso/welink/WeLinkSsoProcessor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */