/*    */ package com.cloudstore.mobile.constant;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AuthTypeConstant
/*    */ {
/*    */   public static final String AUTH_TYPE = "AUTH_TYPE";
/*    */   public static final String EMOBILE7 = "EMOBILE7";
/*    */   public static final String WELINK = "WELINK";
/*    */   public static final String EM_SHA1 = "EM_SHA1";
/*    */   public static final String AUTH_CODE = "1";
/*    */   public static final String AUTH_DOUBLE_TOKEN = "2";
/*    */   public static final String AUTH_JWT = "3";
/*    */   public static final String MOBILE_TOKEN_NAME_STRING = "mobileTokenName";
/*    */   public static final String ACCESS_TOKEN = "m_access_token";
/*    */   public static final String REFRESH_TOKEN = "m_refresh_token";
/*    */   public static final int ACCESS_TOKEN_TYPE = 1;
/*    */   public static final int REFRESH_TOKEN_TYPE = 2;
/*    */   public static String MOBILE_TOKEN_NAME;
/* 41 */   public static String SHA1_SECRET = "emtoken";
/*    */ 
/*    */ 
/*    */   
/* 45 */   public static int SHA1_TIMEOUT = 300;
/*    */ 
/*    */ 
/*    */   
/* 49 */   public static int MOBILE_SESSION_MIN_AGE = 1800;
/*    */   public static int MOBILE_SESSION_MAX_AGE;
/*    */   public static volatile boolean ENABLE_COOKIE = false;
/*    */   public static volatile boolean ENABLE_TOKEN = true;
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/constant/AuthTypeConstant.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */