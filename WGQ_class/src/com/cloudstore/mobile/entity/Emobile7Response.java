/*     */ package com.cloudstore.mobile.entity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Emobile7Response
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6281623059010217980L;
/*     */   private int errcode;
/*     */   private String operationLogId;
/*     */   private String CpType;
/*     */   private String MainUserId;
/*     */   private int ClientType;
/*     */   private String UserId;
/*     */   private String DeviceId;
/*     */   private String LangType;
/*     */   private String errmsg;
/*     */   private String CpName;
/*  57 */   private int openType = 2;
/*     */ 
/*     */ 
/*     */   
/*     */   private String Pdata;
/*     */ 
/*     */   
/*     */   private String Cdata;
/*     */ 
/*     */   
/*     */   private String jemUrl;
/*     */ 
/*     */ 
/*     */   
/*     */   public int getErrcode() {
/*  72 */     return this.errcode;
/*     */   }
/*     */   
/*     */   public void setErrcode(int paramInt) {
/*  76 */     this.errcode = paramInt;
/*     */   }
/*     */   
/*     */   public String getOperationLogId() {
/*  80 */     return this.operationLogId;
/*     */   }
/*     */   
/*     */   public void setOperationLogId(String paramString) {
/*  84 */     this.operationLogId = paramString;
/*     */   }
/*     */   
/*     */   public String getCpType() {
/*  88 */     return this.CpType;
/*     */   }
/*     */   
/*     */   public void setCpType(String paramString) {
/*  92 */     this.CpType = paramString;
/*     */   }
/*     */   
/*     */   public String getMainUserId() {
/*  96 */     return this.MainUserId;
/*     */   }
/*     */   
/*     */   public void setMainUserId(String paramString) {
/* 100 */     this.MainUserId = paramString;
/*     */   }
/*     */   
/*     */   public int getClientType() {
/* 104 */     return this.ClientType;
/*     */   }
/*     */   
/*     */   public void setClientType(int paramInt) {
/* 108 */     this.ClientType = paramInt;
/*     */   }
/*     */   
/*     */   public String getUserId() {
/* 112 */     return this.UserId;
/*     */   }
/*     */   
/*     */   public void setUserId(String paramString) {
/* 116 */     this.UserId = paramString;
/*     */   }
/*     */   
/*     */   public String getDeviceId() {
/* 120 */     return this.DeviceId;
/*     */   }
/*     */   
/*     */   public void setDeviceId(String paramString) {
/* 124 */     this.DeviceId = paramString;
/*     */   }
/*     */   
/*     */   public String getLangType() {
/* 128 */     return this.LangType;
/*     */   }
/*     */   
/*     */   public void setLangType(String paramString) {
/* 132 */     this.LangType = paramString;
/*     */   }
/*     */   
/*     */   public String getErrmsg() {
/* 136 */     return this.errmsg;
/*     */   }
/*     */   
/*     */   public void setErrmsg(String paramString) {
/* 140 */     this.errmsg = paramString;
/*     */   }
/*     */   
/*     */   public String getCpName() {
/* 144 */     return this.CpName;
/*     */   }
/*     */   
/*     */   public void setCpName(String paramString) {
/* 148 */     this.CpName = paramString;
/*     */   }
/*     */   
/*     */   public int getOpenType() {
/* 152 */     return this.openType;
/*     */   }
/*     */   
/*     */   public void setOpenType(int paramInt) {
/* 156 */     this.openType = paramInt;
/*     */   }
/*     */   
/*     */   public String getPdata() {
/* 160 */     return this.Pdata;
/*     */   }
/*     */   
/*     */   public void setPdata(String paramString) {
/* 164 */     this.Pdata = paramString;
/*     */   }
/*     */   
/*     */   public String getCdata() {
/* 168 */     return this.Cdata;
/*     */   }
/*     */   
/*     */   public void setCdata(String paramString) {
/* 172 */     this.Cdata = paramString;
/*     */   }
/*     */   
/*     */   public String getJemUrl() {
/* 176 */     return this.jemUrl;
/*     */   }
/*     */   
/*     */   public void setJemUrl(String paramString) {
/* 180 */     this.jemUrl = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/cloudstore/mobile/entity/Emobile7Response.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */