/*      */ package com.engine.workflow.biz;
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.engine.workflow.biz.requestFlow.SaveFormDatasBiz;
/*      */ import com.engine.workflow.entity.FormFieldSetEntity;
/*      */ import com.google.common.base.Strings;
/*      */ import java.io.BufferedReader;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import oracle.sql.CLOB;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.formmode.FormModeConfig;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.LabelUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.label.LabelComInfo;
/*      */ import weaver.workflow.exceldesign.ExcelFormula;
/*      */ import weaver.workflow.field.BrowserComInfo;
/*      */ import weaver.workflow.field.FieldManager;
/*      */ import weaver.workflow.field.UserDefinedBrowserTypeComInfo;
/*      */ import weaver.workflow.field.WorkflowSubwfFieldManager;
/*      */ import weaver.workflow.form.FormManager;
/*      */ import weaver.workflow.form.QuoteFieldManager;
/*      */ import weaver.workflow.selectItem.SelectItemManager;
/*      */ 
/*      */ public class FormFieldSetBiz {
/*   37 */   private WorkflowSubwfFieldManager workflowSubwfFieldManager = new WorkflowSubwfFieldManager();
/*   38 */   private FormManager formManager = new FormManager();
/*   39 */   private LabelComInfo labelComInfo = new LabelComInfo();
/*   40 */   private BrowserComInfo browserComInfo = new BrowserComInfo();
/*   41 */   private QuoteFieldManager quoteFieldManager = new QuoteFieldManager();
/*   42 */   private RecordSetTrans recordSetTrans = new RecordSetTrans();
/*   43 */   private UserDefinedBrowserTypeComInfo userDefinedBrowserTypeComInfo = new UserDefinedBrowserTypeComInfo();
/*   44 */   private Map<String, String> field_pubchoice_map = new LinkedHashMap<>();
/*   45 */   private Map<String, String> field_pubchilchoice_map = new LinkedHashMap<>();
/*   46 */   private SelectFieldItemBiz selectFieldItemBiz = new SelectFieldItemBiz();
/*   47 */   private String labelidsCache = ",";
/*      */   private int formId;
/*      */   private int isBill;
/*      */   private int detailTableOrderId;
/*      */   private User user;
/*      */   private boolean isOracle = false;
/*      */   private boolean isMySQL = false;
/*      */   private boolean isPostgresql = false;
/*      */   private boolean isSqlServer = false;
/*      */   private boolean isDB2 = false;
/*      */   private boolean isDetail = false;
/*   58 */   private int isFromMode = 0;
/*      */   
/*      */   public FormFieldSetBiz(int paramInt1, int paramInt2, User paramUser) {
/*   61 */     RecordSet recordSet = new RecordSet();
/*   62 */     this.recordSetTrans.setAutoCommit(false);
/*   63 */     this.isOracle = recordSet.getDBType().toLowerCase().equals("oracle");
/*   64 */     this.isMySQL = recordSet.getDBType().toLowerCase().equals("mysql");
/*   65 */     this.isSqlServer = recordSet.getDBType().toLowerCase().equals("sqlserver");
/*   66 */     this.isDB2 = recordSet.getDBType().toLowerCase().equals("db2");
/*   67 */     this.isPostgresql = recordSet.getDBType().toLowerCase().equals("postgresql");
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   72 */     this.formId = paramInt1;
/*   73 */     this.isBill = paramInt2;
/*   74 */     this.user = paramUser; } public FormFieldSetBiz(int paramInt1, int paramInt2, User paramUser, boolean paramBoolean) { RecordSet recordSet = new RecordSet(); this.recordSetTrans.setAutoCommit(false); this.isOracle = recordSet.getDBType().toLowerCase().equals("oracle"); this.isMySQL = recordSet.getDBType().toLowerCase().equals("mysql");
/*      */     this.isSqlServer = recordSet.getDBType().toLowerCase().equals("sqlserver");
/*      */     this.isDB2 = recordSet.getDBType().toLowerCase().equals("db2");
/*      */     this.isPostgresql = recordSet.getDBType().toLowerCase().equals("postgresql");
/*   78 */     this.formId = paramInt1;
/*   79 */     this.isBill = paramInt2;
/*   80 */     this.user = paramUser;
/*   81 */     this.isDetail = paramBoolean; }
/*      */ 
/*      */   
/*      */   public FormFieldSetBiz(int paramInt1, int paramInt2, User paramUser, boolean paramBoolean, int paramInt3) {
/*   85 */     this(paramInt1, paramInt2, paramUser, paramBoolean);
/*   86 */     this.isFromMode = paramInt3;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getTableName(int paramInt) {
/*   95 */     RecordSet recordSet = new RecordSet();
/*   96 */     String str1 = "";
/*      */     
/*   98 */     String str2 = "select tablename from  workflow_bill where id = " + paramInt;
/*   99 */     recordSet.execute(str2);
/*  100 */     if (recordSet.next()) {
/*  101 */       str1 = recordSet.getString("tablename");
/*      */     }
/*  103 */     return str1;
/*      */   }
/*      */ 
/*      */   
/*      */   public String deleteFormField(String paramString) throws Exception {
/*  108 */     BaseBean baseBean = new BaseBean();
/*  109 */     String str = "success";
/*  110 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString, ",");
/*  111 */     ArrayList<List<String>> arrayList2 = new ArrayList();
/*  112 */     ArrayList<List<String>> arrayList3 = new ArrayList();
/*  113 */     ArrayList<String> arrayList4 = new ArrayList();
/*  114 */     RecordSet recordSet = new RecordSet();
/*  115 */     int i = 1;
/*  116 */     this.recordSetTrans.executeQuery("select max(orderid) as maxOrder from workflow_billdetailtable where billid = ? ", new Object[] { Integer.valueOf(this.formId) });
/*  117 */     if (this.recordSetTrans.next()) {
/*  118 */       int j = this.recordSetTrans.getInt("maxOrder");
/*  119 */       i += (j < 0) ? 0 : j;
/*      */     } 
/*  121 */     for (byte b1 = 0; b1 < i; b1++) {
/*  122 */       arrayList2.add(null);
/*  123 */       arrayList3.add(null);
/*      */     } 
/*      */ 
/*      */     
/*  127 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  128 */     JSONArray jSONArray = new JSONArray();
/*      */     try {
/*  130 */       new ExcelFormula(); Map map = ExcelFormula.getFormFields(this.formId + "", this.isBill + "", this.user);
/*  131 */       jSONArray = (JSONArray)map.get("fieldNameIdJson");
/*  132 */     } catch (Exception exception) {
/*  133 */       exception.printStackTrace();
/*      */     } 
/*      */ 
/*      */     
/*  137 */     for (byte b2 = 0; b2 < arrayList1.size(); b2++) {
/*  138 */       String str1 = "";
/*  139 */       String str2 = "";
/*  140 */       String str3 = arrayList1.get(b2);
/*  141 */       String str4 = null;
/*  142 */       String str5 = "";
/*  143 */       int j = 0;
/*  144 */       String str6 = "";
/*  145 */       String str7 = "";
/*  146 */       String str8 = "";
/*      */       
/*  148 */       if ("".equals(str3))
/*      */         continue; 
/*  150 */       this.recordSetTrans.executeSql("select * from workflow_billfield where id=" + str3);
/*  151 */       if (this.recordSetTrans.next()) {
/*  152 */         str2 = this.recordSetTrans.getString("fieldname");
/*  153 */         str5 = this.recordSetTrans.getString("viewtype");
/*  154 */         str4 = this.recordSetTrans.getString("fielddbtype");
/*  155 */         str6 = SystemEnv.getHtmlLabelName(this.recordSetTrans.getInt("fieldlabel"), this.user.getLanguage());
/*  156 */         str7 = this.recordSetTrans.getString("fieldHtmlType");
/*  157 */         str8 = this.recordSetTrans.getString("type");
/*  158 */         if (str5.equals("1")) {
/*  159 */           str1 = this.recordSetTrans.getString("detailtable");
/*  160 */           recordSet.executeQuery("select orderid from workflow_billdetailtable where tablename = ?", new Object[] { str1 });
/*  161 */           if (recordSet.next()) {
/*  162 */             j = Util.getIntValue(recordSet.getString("orderid"));
/*      */           }
/*  164 */         } else if (str5.equals("0")) {
/*  165 */           this.recordSetTrans.executeSql("select tablename from workflow_bill where id=" + this.formId);
/*  166 */           if (this.recordSetTrans.next()) {
/*  167 */             str1 = this.recordSetTrans.getString("tablename");
/*      */           }
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  173 */       if ("id".equalsIgnoreCase(str2) || "requestid".equalsIgnoreCase(str2)) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */       
/*  178 */       String str9 = str3; byte b;
/*  179 */       for (b = 0; b < jSONArray.size(); b++) {
/*  180 */         String str14 = jSONArray.getJSONObject(b).getString("id");
/*  181 */         if (str14.indexOf(".") > -1) {
/*  182 */           str14 = str14.split("\\.")[1];
/*      */         }
/*  184 */         if (str14.equals(str3)) {
/*  185 */           str9 = jSONArray.getJSONObject(b).getString("name");
/*      */         }
/*      */       } 
/*  188 */       b = 0;
/*  189 */       String str10 = (new ExcelFormula()).canDeleteFieldFormula(str3, str9, this.formId, this.isBill, this.user);
/*  190 */       if (str10 != null && str10.length() > 0) {
/*  191 */         str = "nodeleteForFormula";
/*  192 */         arrayList4.add(str10);
/*  193 */         b = 1;
/*      */       } 
/*      */ 
/*      */       
/*  197 */       this.recordSetTrans.executeSql("select * from workflow_formdetailinfo where rowcalstr like '%!_" + str3 + "_%' escape '!' or rowcalstr like '%!_" + str3 + "' escape '!' or colcalstr like '%!_" + str3 + "_%' escape '!' or colcalstr like '%!_" + str3 + "' escape '!' or maincalstr like '%!_" + str3 + "_%' escape '!' or maincalstr like '%!_" + str3 + "' escape '!'");
/*  198 */       if (this.recordSetTrans.next()) {
/*  199 */         str = "nodeleteForRule";
/*  200 */         if (arrayList2.size() > j && arrayList2.get(j) != null) {
/*  201 */           ((List<String>)arrayList2.get(j)).add(str6);
/*      */         } else {
/*  203 */           ArrayList<String> arrayList = new ArrayList();
/*  204 */           arrayList.add(str6);
/*  205 */           arrayList2.set(j, arrayList);
/*      */         } 
/*      */         
/*      */         continue;
/*      */       } 
/*  210 */       if (this.workflowSubwfFieldManager.hasSubWfSetting(this.recordSetTrans, Util.getIntValue(str3, 0), 1)) {
/*  211 */         str = "nodeleteForSub";
/*  212 */         if (arrayList3.size() > j && arrayList3.get(j) != null) {
/*  213 */           ((List<String>)arrayList3.get(j)).add(str6);
/*      */         } else {
/*  215 */           ArrayList<String> arrayList = new ArrayList();
/*  216 */           arrayList.add(str6);
/*  217 */           arrayList3.set(j, arrayList);
/*      */         } 
/*      */         continue;
/*      */       } 
/*  221 */       if (b != 0) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */ 
/*      */       
/*  227 */       try { if (this.recordSetTrans.getDBType().toLowerCase().equals("postgresql") && ("int".equals(str4) || str4.contains("decimal"))) {
/*  228 */           this.recordSetTrans.executeQuery("select 1 from " + str1 + " where " + str2 + " is not null ", new Object[0]);
/*      */         } else {
/*  230 */           this.recordSetTrans.executeQuery("select 1 from " + str1 + " where " + str2 + " is not null and " + str2 + " <> ''", new Object[0]);
/*  231 */         }  if (this.recordSetTrans.next())
/*  232 */           continue;  } catch (Exception exception)
/*      */       { 
/*  234 */         try { if (this.recordSetTrans.getDBType().toLowerCase().equals("sqlserver") && ("text".equals(str4) || str4.indexOf("browser") > -1)) {
/*  235 */             this.recordSetTrans.executeQuery("select 1 from " + str1 + " where len(cast(" + str2 + " as varchar(max))) > 0", new Object[0]);
/*      */           } else {
/*  237 */             this.recordSetTrans.executeQuery("select 1 from " + str1 + " where " + str2 + " is not null", new Object[0]);
/*      */           } 
/*  239 */           if (this.recordSetTrans.next())
/*  240 */             continue;  } catch (Exception exception1)
/*  241 */         { exception.printStackTrace();
/*  242 */           exception1.printStackTrace(); }
/*      */          }
/*      */       
/*      */       try {
/*  246 */         this.recordSetTrans.executeUpdate("alter table " + str1 + " drop column " + str2, new Object[0]);
/*  247 */         this.recordSetTrans.executeUpdate("delete from workflow_billfield where id=" + str3, new Object[0]);
/*  248 */         this.recordSetTrans.executeUpdate("update workflow_billfield set childfieldid=0 where childfieldid=" + str3, new Object[0]);
/*  249 */         this.recordSetTrans.executeUpdate("delete from workflow_SelectItem where isbill=1 and fieldid=" + str3, new Object[0]);
/*  250 */       } catch (Exception exception) {
/*  251 */         baseBean.writeLog("删除字段失败, sql:alter table " + str1 + " drop column " + str2);
/*  252 */         exception.printStackTrace();
/*      */       } 
/*      */ 
/*      */       
/*  256 */       if ("0".equals(str5)) {
/*  257 */         Pattern pattern = null;
/*  258 */         Matcher matcher = null;
/*  259 */         String str14 = "\\b" + str2 + "\\b";
/*  260 */         pattern = Pattern.compile(str14);
/*  261 */         ArrayList<String> arrayList5 = new ArrayList();
/*  262 */         ArrayList<String> arrayList6 = new ArrayList();
/*      */         
/*  264 */         String str15 = "select * from workflow_nodelink where condition like '%" + str2 + "%' and workflowid in (select id from workflow_base where formid=" + this.formId + ")";
/*      */         
/*  266 */         RecordSet recordSet2 = new RecordSet();
/*  267 */         if ("mysql".equals(recordSet2.getDBType())) {
/*  268 */           str15 = "select * from workflow_nodelink where `condition` like '%" + str2 + "%' and workflowid in (select id from workflow_base where formid=" + this.formId + ")";
/*      */         }
/*  270 */         ConnStatement connStatement = new ConnStatement();
/*  271 */         String str16 = "";
/*  272 */         connStatement.setStatementSql(str15, false);
/*  273 */         connStatement.executeQuery();
/*  274 */         if (connStatement.next()) {
/*  275 */           if (connStatement.getDBType().equals("oracle") && Util.null2String(connStatement.getOrgindbtype()).equals("oracle")) {
/*  276 */             CLOB cLOB = connStatement.getClob("condition");
/*  277 */             String str17 = "";
/*  278 */             StringBuffer stringBuffer = new StringBuffer("");
/*  279 */             BufferedReader bufferedReader = new BufferedReader(cLOB.getCharacterStream());
/*  280 */             while ((str17 = bufferedReader.readLine()) != null)
/*  281 */               stringBuffer = stringBuffer.append(str17); 
/*  282 */             bufferedReader.close();
/*  283 */             str16 = stringBuffer.toString();
/*      */           } else {
/*  285 */             str16 = connStatement.getString("condition");
/*      */           } 
/*      */           
/*  288 */           int k = Util.getIntValue(connStatement.getString("id"));
/*  289 */           arrayList5.add("" + k);
/*  290 */           arrayList6.add("" + str16);
/*      */         } 
/*      */ 
/*      */         
/*  294 */         for (byte b3 = 0; b3 < arrayList5.size(); b3++) {
/*  295 */           String str17 = Util.null2String(arrayList5.get(b3));
/*  296 */           String str18 = Util.null2String(arrayList6.get(b3));
/*  297 */           matcher = pattern.matcher(str18);
/*  298 */           boolean bool = matcher.find();
/*  299 */           if (bool == true) {
/*  300 */             String str19 = "update workflow_nodelink set condition='' , conditioncn='' where id=" + str17;
/*  301 */             if (connStatement.getDBType().equals("oracle") && Util.null2String(connStatement.getOrgindbtype()).equals("oracle"))
/*  302 */               str19 = "update workflow_nodelink set condition=empty_clob() , conditioncn=empty_clob() where id=" + str17; 
/*  303 */             connStatement.setStatementSql(str19);
/*  304 */             connStatement.executeUpdate();
/*      */           } 
/*      */         } 
/*  307 */         if (null != connStatement) {
/*  308 */           connStatement.close();
/*      */         }
/*      */       } 
/*      */ 
/*      */       
/*  313 */       this.recordSetTrans.executeSql("delete from  workflow_addinoperate where isnode=1 and objid in (select t1.nodeid from  workflow_flownode t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ") and (fieldid =" + str3 + " or fieldop1id = " + str3 + " or fieldop2id = " + str3 + ")");
/*      */ 
/*      */       
/*  316 */       this.recordSetTrans.executeSql("delete from  workflow_addinoperate where isnode=0 and objid in (select t1.id from  workflow_nodelink t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ") and (fieldid =" + str3 + " or fieldop1id = " + str3 + " or fieldop2id = " + str3 + ")");
/*      */ 
/*      */       
/*  319 */       this.recordSetTrans
/*  320 */         .executeSql("delete from  workflow_groupdetail where type in(5,6,31,32,7,38,42,43,8,33,9,10,47,34,11,12,13,35,14,15,44,45,46,16) and groupid in(select id from workflow_nodegroup where nodeid in (select t1.nodeid from  workflow_flownode t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ")) and objid=" + str3);
/*      */ 
/*      */       
/*  323 */       this.recordSetTrans.executeSql("delete from  workflow_nodeform where nodeid in (select t1.nodeid from  workflow_flownode t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ") and fieldid= " + str3);
/*      */       
/*  325 */       this.recordSetTrans.executeSql("delete from workflow_specialfield where isbill=1 and fieldid =" + str3);
/*      */       
/*  327 */       this.recordSetTrans.executeUpdate("delete from workflow_detailFilterSet where fieldid = ? and workflowid in (select id from workflow_base where formid = ? and isbill = '1')", new Object[] { str3, Integer.valueOf(this.formId) });
/*      */ 
/*      */       
/*  330 */       if ("1".equals(str5)) {
/*  331 */         int k = -1;
/*  332 */         int m = -1;
/*  333 */         this.recordSetTrans.executeQuery("select max(orderid) as maxOrder, max(id) as maxid from Workflow_billdetailtable where billid = ?", new Object[] { Integer.valueOf(this.formId) });
/*  334 */         if (this.recordSetTrans.next()) {
/*  335 */           k = Util.getIntValue(this.recordSetTrans.getString("maxOrder"));
/*  336 */           m = Util.getIntValue(this.recordSetTrans.getString("maxid"));
/*      */         } 
/*  338 */         int n = 0;
/*  339 */         int i1 = 0;
/*  340 */         this.recordSetTrans.executeQuery("select orderid, id from Workflow_billdetailtable where billid = ? and tablename = ?", new Object[] { Integer.valueOf(this.formId), str1 });
/*  341 */         if (this.recordSetTrans.next()) {
/*  342 */           n = Util.getIntValue(this.recordSetTrans.getString("orderid"));
/*  343 */           i1 = Util.getIntValue(this.recordSetTrans.getString("id"));
/*      */         } 
/*  345 */         if (n == k && i1 == m) {
/*      */           
/*  347 */           this.recordSetTrans.executeSql("select * from workflow_billfield where billid=" + this.formId + " and detailtable='" + str1 + "'");
/*  348 */           if (!this.recordSetTrans.next()) {
/*      */             
/*      */             try {
/*  351 */               (new FormDeleteUtil()).doDeleteFormTable(this.formId, str1, this.recordSetTrans);
/*  352 */             } catch (Exception exception) {
/*  353 */               exception.printStackTrace();
/*      */             } 
/*  355 */             this.recordSetTrans.executeSql("delete from Workflow_billdetailtable where billid=" + this.formId + " and tablename='" + str1 + "'");
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  363 */       String str11 = "";
/*  364 */       String str12 = "";
/*  365 */       String str13 = "";
/*  366 */       RecordSet recordSet1 = new RecordSet();
/*  367 */       recordSet1.executeQuery("select * from workflow_base where formid = ? and isbill = 1", new Object[] { Integer.valueOf(this.formId) });
/*  368 */       while (recordSet1.next()) {
/*  369 */         str13 = str13 + recordSet1.getString("id") + ",";
/*      */       }
/*      */       
/*  372 */       if (!"".equals(str13)) {
/*  373 */         str13 = str13.substring(0, str13.length() - 1);
/*  374 */         if ("5".equals(str7)) {
/*  375 */           str11 = "update workflow_code set selectcorrespondfield = null ,fieldsequencealone = 0 where selectcorrespondfield = ? ";
/*      */         }
/*  377 */         if ("3".equals(str7) && ("42".equals(str8) || "164".equals(str8) || "169".equals(str8) || "4".equals(str8) || "167".equals(str8))) {
/*  378 */           str11 = "update workflow_code set correspondfield = null ,struseqalone = 0 where correspondfield = ? ";
/*      */         }
/*  380 */         if ("3".equals(str7) && ("2".equals(str8) || "178".equals(str8))) {
/*  381 */           str11 = "update workflow_code set corresponddate = null ,dateseqalone = 0 where corresponddate = ? ";
/*      */         }
/*  383 */         if (!"".equals(str11)) {
/*  384 */           str11 = str11 + " and ((isbill = 1 and formid = ?) or " + Util.getSubINClause(str13, "flowid", "in") + ")";
/*  385 */           this.recordSetTrans.executeUpdate(str11, new Object[] { str3, Integer.valueOf(this.formId) });
/*      */         } 
/*      */         try {
/*  388 */           String str14 = " from workflow_coderegulate where codevalue = ? and not (showtype = 2 and (concretefield=7 or concretefield=8)) and ((isbill = 1 and formid = ?) or (" + Util.getSubINClause(str13, "workflowid", "in") + "))";
/*  389 */           String str15 = "insert into workflow_coderegulate_dellog(id,formid,showid,showtype,codevalue,codeorder,isbill,workflowid,concretefield,enablecode) select id,formid,showid,showtype,codevalue,codeorder,isbill,workflowid,concretefield,enablecode " + str14;
/*  390 */           this.recordSetTrans.executeUpdate(str15, new Object[] { str3, Integer.valueOf(this.formId) });
/*  391 */         } catch (Exception exception) {
/*  392 */           exception.printStackTrace();
/*      */         } 
/*  394 */         str12 = "delete from workflow_coderegulate where codevalue = ? and not (showtype = 2 and (concretefield=7 or concretefield=8)) and ((isbill = 1 and formid = ?) or (" + Util.getSubINClause(str13, "workflowid", "in") + "))";
/*  395 */         this.recordSetTrans.executeUpdate(str12, new Object[] { str3, Integer.valueOf(this.formId) });
/*      */       } 
/*      */       continue;
/*      */     } 
/*  399 */     if (!"success".equals(str)) {
/*  400 */       str = "";
/*  401 */       String str1 = "";
/*  402 */       if (arrayList2.size() > 0) {
/*  403 */         for (byte b = 0; b < arrayList2.size(); b++) {
/*  404 */           List<String> list = arrayList2.get(b);
/*  405 */           if (list != null && list.size() > 0) {
/*  406 */             if (!"".equals(str1)) {
/*  407 */               str1 = str1 + ",";
/*      */             }
/*  409 */             str1 = str1 + ((b == 0) ? SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()) : (SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b)) + "【";
/*  410 */             for (byte b3 = 0; b3 < list.size(); b3++) {
/*  411 */               str1 = str1 + ((b3 == 0) ? list.get(b3) : ("，" + (String)list.get(b3)));
/*      */             }
/*  413 */             str1 = str1 + "】";
/*      */           } 
/*      */         } 
/*  416 */         if (!"".equals(str1)) {
/*  417 */           str1 = str1 + SystemEnv.getHtmlLabelName(386051, this.user.getLanguage());
/*      */         }
/*      */       } 
/*  420 */       String str2 = "";
/*  421 */       if (arrayList3.size() > 0) {
/*  422 */         for (byte b = 0; b < arrayList3.size(); b++) {
/*  423 */           List<String> list = arrayList3.get(b);
/*  424 */           if (list != null && list.size() > 0) {
/*  425 */             if (!"".equals(str2)) {
/*  426 */               str2 = str2 + ",";
/*      */             }
/*  428 */             str2 = str2 + ((b == 0) ? SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()) : (SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b)) + "【";
/*  429 */             for (byte b3 = 0; b3 < list.size(); b3++) {
/*  430 */               str2 = str2 + ((b3 == 0) ? list.get(b3) : ("，" + (String)list.get(b3)));
/*      */             }
/*  432 */             str2 = str2 + "】";
/*      */           } 
/*      */         } 
/*  435 */         if (!"".equals(str2)) {
/*  436 */           str2 = str2 + SystemEnv.getHtmlLabelName(386050, this.user.getLanguage());
/*      */         }
/*      */       } 
/*  439 */       String str3 = "";
/*  440 */       if (arrayList4.size() > 0) {
/*  441 */         for (byte b = 0; b < arrayList4.size(); b++) {
/*  442 */           str3 = str3 + (String)arrayList4.get(b);
/*      */         }
/*      */       }
/*  445 */       str = str1 + str2 + str3;
/*      */     } 
/*      */     
/*  448 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, String> AddAndEditField(FormFieldSetEntity paramFormFieldSetEntity, String paramString, boolean paramBoolean) throws Exception {
/*  462 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  463 */     String str1 = "";
/*      */     
/*  465 */     String str2 = Util.null2String(paramFormFieldSetEntity.getFieldId());
/*      */     
/*  467 */     String str3 = "";
/*  468 */     int i = 0;
/*  469 */     String str4 = "";
/*  470 */     String str5 = "";
/*  471 */     String str6 = "";
/*  472 */     String str7 = "";
/*  473 */     String str8 = "";
/*  474 */     boolean bool1 = false;
/*  475 */     boolean bool2 = false;
/*  476 */     String str9 = paramBoolean ? "1" : "0";
/*  477 */     String str10 = paramBoolean ? paramString : "";
/*  478 */     int j = 0;
/*  479 */     String str11 = "";
/*  480 */     String str12 = "";
/*  481 */     String str13 = "1";
/*  482 */     int k = 0;
/*  483 */     boolean bool3 = false;
/*      */     
/*  485 */     String str14 = Util.null2String(paramFormFieldSetEntity.getSelectItemType());
/*  486 */     int m = paramFormFieldSetEntity.getPubChoiceId();
/*  487 */     int n = paramFormFieldSetEntity.getPubChilChoiceId();
/*      */     
/*  489 */     int i1 = paramFormFieldSetEntity.getImgWidth();
/*  490 */     int i2 = paramFormFieldSetEntity.getImgHeight();
/*  491 */     String str15 = Util.null2String(paramFormFieldSetEntity.getFieldLabel());
/*  492 */     str3 = Util.null2String(paramFormFieldSetEntity.getFieldName());
/*  493 */     str6 = Util.null2String(paramFormFieldSetEntity.getFieldHtmlType());
/*  494 */     str7 = Util.null2String(paramFormFieldSetEntity.getType());
/*  495 */     str8 = Util.null2String(paramFormFieldSetEntity.getDspOrder());
/*  496 */     str8 = "" + Util.getFloatValue(str8, 0.0F);
/*  497 */     str12 = Util.null2String(paramFormFieldSetEntity.getLocateType());
/*  498 */     k = paramFormFieldSetEntity.getQfws();
/*  499 */     j = paramFormFieldSetEntity.getTextHeight();
/*  500 */     str13 = Util.null2String(paramFormFieldSetEntity.getFieldShowTypes());
/*  501 */     int i3 = paramFormFieldSetEntity.getFieldGroupId();
/*  502 */     if (i3 < 0) i3 = 0;
/*      */ 
/*      */     
/*  505 */     int i4 = 0;
/*  506 */     if (paramFormFieldSetEntity.getFieldHtmlType().equals("5") && !paramFormFieldSetEntity.getType().equals("2")) {
/*  507 */       i4 = paramFormFieldSetEntity.getChildFieldId();
/*      */     }
/*      */     
/*  510 */     if (this.isBill == 1) {
/*  511 */       i = getFieldNameLabel(str2, str15);
/*      */     }
/*      */     
/*  514 */     if (str14.equals("0")) {
/*  515 */       m = 0;
/*  516 */       n = 0;
/*      */     } 
/*  518 */     if (str14.equals("1")) {
/*  519 */       n = 0;
/*      */     }
/*  521 */     if (!str6.equals("5")) {
/*  522 */       str14 = "0";
/*  523 */       n = 0;
/*  524 */       m = 0;
/*      */     } 
/*      */     
/*  527 */     if (str6.equals("1")) {
/*  528 */       int i5 = paramFormFieldSetEntity.getQfws();
/*  529 */       if (str7.equals("1")) {
/*  530 */         String str = Util.null2String(paramFormFieldSetEntity.getStrLength());
/*  531 */         if (Util.getIntValue(str, 1) <= 1)
/*  532 */           str = "1"; 
/*  533 */         if (this.isOracle) {
/*  534 */           str4 = "varchar2(" + str + ")";
/*      */         } else {
/*  536 */           str4 = "varchar(" + str + ")";
/*      */         } 
/*  538 */       }  if (str7.equals("2"))
/*  539 */         if (this.isOracle) {
/*  540 */           str4 = "integer";
/*  541 */         } else if (this.isMySQL) {
/*  542 */           str4 = "int(11)";
/*      */         } else {
/*  544 */           str4 = "int";
/*      */         }  
/*  546 */       if (str7.equals("3"))
/*  547 */         if (this.isOracle) {
/*  548 */           str4 = "number(38," + i5 + ")";
/*      */         } else {
/*  550 */           str4 = "decimal(38," + i5 + ")";
/*      */         }  
/*  552 */       if (str7.equals("4"))
/*  553 */         if (this.isOracle) {
/*  554 */           str4 = "number(15,2)";
/*      */         } else {
/*  556 */           str4 = "decimal(15,2)";
/*      */         }  
/*  558 */       if (str7.equals("5"))
/*  559 */         if (this.isOracle) {
/*  560 */           str4 = "varchar2(30)";
/*      */         } else {
/*  562 */           str4 = "varchar(30)";
/*      */         }  
/*  564 */     } else if (str6.equals("2")) {
/*  565 */       if (this.isOracle) {
/*  566 */         if (!"1".equals(str7)) {
/*  567 */           str4 = "clob";
/*      */         } else {
/*  569 */           str4 = "varchar2(4000)";
/*      */         } 
/*  571 */       } else if (this.isMySQL) {
/*  572 */         if (!"1".equals(str7)) {
/*  573 */           str4 = "longtext";
/*      */         } else {
/*  575 */           str4 = "text";
/*      */         } 
/*  577 */       } else if (this.isDB2) {
/*  578 */         str4 = "varchar(2000)";
/*      */       } else {
/*  580 */         str4 = "text";
/*      */       } 
/*  582 */     } else if (str6.equals("3")) {
/*  583 */       int i5 = Util.getIntValue(paramFormFieldSetEntity.getType(), 0);
/*  584 */       str7 = String.valueOf(i5);
/*  585 */       if (i5 > 0) {
/*  586 */         str4 = this.browserComInfo.getBrowserdbtype(str7);
/*      */       }
/*  588 */       if (i5 == 315 && this.isOracle && "text".equals(str4)) {
/*  589 */         str4 = "varchar2(4000)";
/*  590 */       } else if (i5 == 141 && this.isOracle) {
/*  591 */         str4 = "clob";
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  596 */       if (i5 == 118)
/*  597 */         if (this.isOracle) {
/*  598 */           str4 = "varchar2(200)";
/*      */         } else {
/*  600 */           str4 = "varchar(200)";
/*      */         }  
/*  602 */       if (i5 == 161 || i5 == 162) {
/*  603 */         str4 = Util.null2String(paramFormFieldSetEntity.getFieldDBType());
/*  604 */         if (i5 == 161) {
/*  605 */           if (this.isOracle) {
/*  606 */             str5 = "varchar2(1000)";
/*  607 */           } else if (this.isMySQL) {
/*  608 */             str5 = "text";
/*      */           } else {
/*  610 */             str5 = "varchar(1000)";
/*      */           }
/*      */         
/*  613 */         } else if (this.isOracle) {
/*  614 */           str5 = "clob";
/*  615 */         } else if (this.isDB2) {
/*  616 */           str5 = "varchar(2000)";
/*      */         } else {
/*  618 */           str5 = "text";
/*      */         } 
/*      */       } 
/*  621 */       if (i5 == 256 || i5 == 257) {
/*  622 */         str4 = Util.null2String(paramFormFieldSetEntity.getFieldDBType());
/*  623 */         if (i5 == 256) {
/*  624 */           if (this.isOracle) {
/*  625 */             str5 = "varchar2(1000)";
/*  626 */           } else if (this.isMySQL) {
/*  627 */             str5 = "text";
/*      */           } else {
/*  629 */             str5 = "varchar(1000)";
/*      */           } 
/*  631 */         } else if (this.isOracle) {
/*  632 */           str5 = "clob";
/*  633 */         } else if (this.isDB2) {
/*  634 */           str5 = "varchar(2000)";
/*  635 */         } else if (this.isMySQL) {
/*  636 */           str5 = "text";
/*      */         } else {
/*  638 */           str5 = "varchar(4000)";
/*      */         } 
/*      */       } 
/*  641 */       if (i5 == 224 || i5 == 225) {
/*  642 */         str4 = Util.null2String(paramFormFieldSetEntity.getFieldDBType());
/*  643 */         if (i5 == 224) {
/*  644 */           if (this.isOracle) {
/*  645 */             str5 = "varchar2(1000)";
/*      */           } else {
/*  647 */             str5 = "varchar(1000)";
/*      */           } 
/*  649 */         } else if (this.isOracle) {
/*  650 */           str5 = "clob";
/*  651 */         } else if (this.isDB2) {
/*  652 */           str5 = "varchar(2000)";
/*      */         } else {
/*  654 */           str5 = "text";
/*      */         } 
/*      */       } 
/*  657 */       if (i5 == 226 || i5 == 227) {
/*  658 */         str4 = Util.null2String(paramFormFieldSetEntity.getFieldDBType());
/*  659 */         if (i5 == 226) {
/*  660 */           if (this.isOracle) {
/*  661 */             str5 = "varchar2(1000)";
/*  662 */           } else if (this.isMySQL) {
/*  663 */             str5 = "text";
/*      */           } else {
/*  665 */             str5 = "varchar(1000)";
/*      */           } 
/*  667 */         } else if (this.isOracle) {
/*  668 */           str5 = "clob";
/*  669 */         } else if (this.isDB2) {
/*  670 */           str5 = "varchar(2000)";
/*      */         } else {
/*  672 */           str5 = "text";
/*      */         } 
/*      */       } 
/*  675 */       if (i5 == 17) {
/*  676 */         if (this.isOracle) {
/*  677 */           str4 = "clob";
/*  678 */           str5 = "clob";
/*  679 */         } else if (this.isDB2) {
/*  680 */           str5 = "varchar(2000)";
/*      */         } else {
/*  682 */           str4 = "text";
/*  683 */           str5 = "text";
/*      */         } 
/*      */       }
/*  686 */       if (i5 == 165 || i5 == 166 || i5 == 167 || i5 == 168 || i5 == 169 || i5 == 170)
/*  687 */         str11 = Util.null2String(paramFormFieldSetEntity.getTextHeight_2()); 
/*  688 */     } else if (str6.equals("4")) {
/*  689 */       str4 = "char(1)";
/*  690 */     } else if (str6.equals("5")) {
/*      */       
/*  692 */       if (str7.equals("2")) {
/*  693 */         if (this.isOracle) {
/*  694 */           str4 = "varchar2(4000)";
/*  695 */         } else if (this.isDB2) {
/*  696 */           str4 = "varchar(2000)";
/*      */         } else {
/*  698 */           str4 = "text";
/*      */         } 
/*  700 */       } else if (this.isOracle) {
/*  701 */         str4 = "integer";
/*  702 */       } else if (this.isMySQL) {
/*  703 */         str4 = "int(11)";
/*      */       } else {
/*  705 */         str4 = "int";
/*      */       } 
/*  707 */     } else if (str6.equals("6")) {
/*  708 */       if (this.isOracle)
/*  709 */       { str4 = "varchar2(4000)"; }
/*  710 */       else if (this.isDB2)
/*  711 */       { str4 = "varchar(2000)"; }
/*      */       else
/*  713 */       { str4 = "text"; } 
/*  714 */     } else if (str6.equals("7")) {
/*  715 */       if (this.isOracle)
/*  716 */       { str4 = "varchar2(4000)"; }
/*  717 */       else if (this.isDB2)
/*  718 */       { str4 = "varchar(2000)"; }
/*      */       else
/*  720 */       { str4 = "text"; } 
/*  721 */     } else if (str6.equals("9")) {
/*  722 */       if (this.isOracle) {
/*  723 */         str4 = "clob";
/*      */       } else {
/*  725 */         str4 = "text";
/*      */       } 
/*      */     } 
/*  728 */     if (this.isBill == 0) {
/*  729 */       FieldManager fieldManager = new FieldManager();
/*  730 */       fieldManager.reset();
/*      */       
/*  732 */       fieldManager.setFieldname(Util.null2String(paramFormFieldSetEntity.getFieldName()));
/*  733 */       fieldManager.setIstemplate(Util.null2String(paramFormFieldSetEntity.getIsTemplate()));
/*  734 */       fieldManager.setFielddbtype(str4);
/*  735 */       fieldManager.setFieldhtmltype(str6);
/*  736 */       fieldManager.setType(Util.getIntValue(str7));
/*  737 */       fieldManager.setSubCompanyId2(paramFormFieldSetEntity.getSubCompanyId());
/*  738 */       fieldManager.setDescription(paramFormFieldSetEntity.getFieldLabel());
/*  739 */       fieldManager.setTextheight(j);
/*  740 */       fieldManager.setTextheight_2(str11);
/*  741 */       fieldManager.setImgwidth(i1);
/*  742 */       fieldManager.setImgheight(i2);
/*  743 */       fieldManager.setQfwws(String.valueOf(k));
/*  744 */       fieldManager.setLocatetype(str12);
/*  745 */       fieldManager.setFieldshowtypes(str13);
/*      */ 
/*      */       
/*  748 */       if (paramFormFieldSetEntity.getFieldHtmlType().equals("5") && !paramFormFieldSetEntity.getType().equals("2")) {
/*  749 */         fieldManager.setChildfieldid(paramFormFieldSetEntity.getChildFieldId());
/*      */       }
/*  751 */       fieldManager.setChildfieldid(i4);
/*      */       
/*  753 */       if (Strings.isNullOrEmpty(str2)) {
/*  754 */         fieldManager.setAction("addfield");
/*      */       } else {
/*  756 */         fieldManager.setAction("editfield");
/*  757 */         fieldManager.setFieldid(Util.getIntValue(str2));
/*  758 */         fieldManager.setLanguageid(this.user.getLanguage());
/*      */       } 
/*      */       
/*  761 */       if (paramBoolean) {
/*  762 */         str1 = fieldManager.setDetailFieldInfo();
/*  763 */         if (Strings.isNullOrEmpty(str1)) {
/*  764 */           (new DetailFieldComInfo()).removeFieldCache();
/*      */         }
/*      */       } else {
/*  767 */         str1 = fieldManager.setFieldInfo();
/*  768 */         if (Strings.isNullOrEmpty(str1)) {
/*  769 */           (new FieldComInfo()).removeFieldCache();
/*      */         }
/*      */       } 
/*  772 */       RecordSet recordSet = new RecordSet();
/*      */       
/*  774 */       if (Strings.isNullOrEmpty(str2)) {
/*  775 */         if (paramBoolean) {
/*  776 */           recordSet.execute("select max(id) from workflow_formdictdetail");
/*      */         } else {
/*  778 */           recordSet.execute("select max(id) from workflow_formdict");
/*      */         } 
/*  780 */         if (recordSet.next()) {
/*  781 */           str2 = recordSet.getString(1);
/*      */         }
/*      */       } 
/*      */     } else {
/*  785 */       if ("id".equals(str3.toLowerCase()) || "requestid".equals(str3.toLowerCase()) || "formmodeid"
/*  786 */         .equals(str3.toLowerCase()) || "modedatacreater".equals(str3.toLowerCase()) || "modedatacreatertype"
/*  787 */         .equals(str3.toLowerCase()) || "modedatacreatedate".equals(str3.toLowerCase()) || "modedatacreatetime"
/*  788 */         .equals(str3.toLowerCase()) || "modeuuid".equals(str3.toLowerCase()) || "modedatamodifier"
/*  789 */         .equals(str3.toLowerCase()) || "modedatamodifydatetime".equals(str3.toLowerCase()) || "mainidorder".equals(str3.toLowerCase()) || "modedatastatisticsdate"
/*  790 */         .equals(str3.toLowerCase()) || "rn".equals(str3.toLowerCase()) || "describe".equals(str3.toLowerCase()) || "seclevel".equals(str3.toLowerCase()) || "modesecrettime".equals(str3.toLowerCase())) {
/*  791 */         return (Map)hashMap;
/*      */       }
/*  793 */       if (Strings.isNullOrEmpty(str2)) {
/*      */ 
/*      */         
/*  796 */         String str17 = (this.formId > 0 && this.isBill == 1) ? "0" : "1";
/*  797 */         String str18 = "INSERT INTO workflow_billfield(billid,fieldname,fieldlabel,fielddbtype,fieldhtmltype,type,dsporder,viewtype,fromuser,detailtable,textheight,textheight_2,childfieldid,imgwidth,imgheight,places,qfws,selectitem,linkfield,selectItemType,pubchoiceId,pubchilchoiceId,locatetype,fieldshowtypes,fieldgroupid)  VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*      */         
/*  799 */         RecordSet recordSet = new RecordSet();
/*  800 */         recordSet.executeQuery("select 1 from workflow_billfield where fieldname = ? and billid=? and detailtable=?", new Object[] { str3, Integer.valueOf(this.formId), str10 });
/*  801 */         if (recordSet.next()) {
/*  802 */           (new BaseBean()).writeLog("glb-->该字段已存在");
/*  803 */           (new BaseBean()).writeLog("glb-->fieldname=" + str3);
/*  804 */           (new BaseBean()).writeLog("glb-->billid=" + this.formId);
/*  805 */           (new BaseBean()).writeLog("glb-->detailtable=" + str10);
/*      */         } else {
/*  807 */           boolean bool = this.recordSetTrans.executeUpdate(str18, new Object[] { 
/*  808 */                 Integer.valueOf(this.formId), str3, Integer.valueOf(i), str4, str6, str7, str8, str9, str17, str10, 
/*  809 */                 Integer.valueOf(j), str11, 
/*  810 */                 "".equals(Integer.valueOf(i4)) ? null : Integer.valueOf(i4), Integer.valueOf(i1), Integer.valueOf(i2), Integer.valueOf(bool3), 
/*  811 */                 Integer.valueOf(k), Integer.valueOf(bool1), Integer.valueOf(bool2), str14, Integer.valueOf(m), Integer.valueOf(n), str12, 
/*  812 */                 "".equals(str13) ? null : str13, Integer.valueOf(i3) });
/*  813 */           if (!bool) {
/*  814 */             (new BaseBean()).writeLog("workflow_billfield insert field info fail, billid:" + this.formId + ",fieldname:" + str3);
/*      */           } else {
/*      */             
/*  817 */             addFieldToTable(str6, str7, paramString, str3, str4, str5);
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/*  824 */         String str17 = "";
/*  825 */         if (str6.equals("5") && str7.equals("2")) {
/*  826 */           str17 = ",childfieldid='0'";
/*  827 */         } else if (!"1".equals(str14)) {
/*  828 */           str17 = ",childfieldid='" + i4 + "' ";
/*      */         } 
/*      */         
/*  831 */         String str18 = "";
/*  832 */         String str19 = "";
/*  833 */         String str20 = "";
/*  834 */         String str21 = "";
/*  835 */         String str22 = "";
/*  836 */         this.recordSetTrans
/*  837 */           .executeSql("select fieldname,fielddbtype,type,detailtable,viewtype from workflow_billfield where id=" + str2);
/*  838 */         if (this.recordSetTrans.next()) {
/*  839 */           str18 = this.recordSetTrans.getString("fieldname");
/*  840 */           str19 = this.recordSetTrans.getString("fielddbtype");
/*  841 */           str20 = this.recordSetTrans.getString("type");
/*  842 */           str21 = Util.null2String(this.recordSetTrans.getString("detailtable"));
/*  843 */           str22 = Util.null2String(this.recordSetTrans.getString("viewtype"));
/*      */         } 
/*      */ 
/*      */         
/*  847 */         int i5 = Util.getIntValue(str2);
/*  848 */         boolean bool = ((i5 != -1 && isFormUsed(this.formId)) || (this.isBill == 1 && this.formId > 0)) ? true : false;
/*  849 */         if (bool) {
/*  850 */           str9 = str22;
/*  851 */           str10 = str21;
/*  852 */           str3 = str18;
/*      */         } 
/*      */         
/*  855 */         String str23 = "";
/*  856 */         if (!"".equals(str13)) {
/*  857 */           str23 = ",fieldshowtypes=" + str13;
/*      */         }
/*  859 */         this.recordSetTrans.executeSql("update workflow_billfield set billid=" + this.formId + ",fieldname='" + str3 + "',fieldlabel=" + i + ",fielddbtype='" + str4 + "',fieldhtmltype=" + str6 + ",type=" + str7 + ",dsporder=" + str8 + ",viewtype=" + str9 + ",detailtable='" + str10 + "',textheight=" + j + ",textheight_2='" + str11 + "',imgwidth=" + i1 + ",imgheight=" + i2 + " ,places=" + bool3 + " ,qfws=" + k + ",selectitem='" + bool1 + "',linkfield='" + bool2 + "',selectItemType='" + str14 + "',pubchoiceId=" + m + ",pubchilchoiceId=" + n + str23 + str17 + ",locatetype='" + str12 + "',fieldgroupid = " + i3 + " where id=" + str2);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  869 */         if (!isFieldExistInDB(paramString, str18)) {
/*  870 */           addFieldToTable(str6, str7, paramString, str18, str4, str5);
/*      */         }
/*      */         
/*  873 */         if (!str22.equals(str9) || ("1".equals(str9) && !str21.equals(str10))) {
/*  874 */           (new BaseBean()).writeLog("oldViewType" + str22);
/*  875 */           (new BaseBean()).writeLog("viewtype" + str9);
/*  876 */           (new BaseBean()).writeLog("oldDetailTable" + str21);
/*  877 */           (new BaseBean()).writeLog("detailtable" + str10);
/*  878 */           String str = "";
/*  879 */           if ("0".equals(str22)) {
/*  880 */             this.recordSetTrans.executeQuery("select tablename from workflow_bill where id = ?", new Object[] { Integer.valueOf(this.formId) });
/*  881 */             if (this.recordSetTrans.next()) {
/*  882 */               str = this.recordSetTrans.getString("tablename");
/*      */             } else {
/*  884 */               throw new Exception();
/*      */             } 
/*      */           } else {
/*  887 */             str = str21;
/*      */           } 
/*      */           
/*  890 */           if (isFieldExistInDB(str, str18)) {
/*  891 */             String str24 = "del_" + str18 + Util.getRandom();
/*  892 */             if (str24.length() > 30) {
/*  893 */               str24 = str24.substring(0, 30);
/*      */             }
/*  895 */             (new BaseBean()).writeLog("glb--->第1个changeFieldName");
/*  896 */             changeFieldName(str, str24, str18, str4);
/*      */           } 
/*      */           
/*  899 */           if (!isFieldExistInDB(paramString, str3)) {
/*  900 */             addFieldToTable(str6, str7, paramString, str3, str4, str5);
/*      */           }
/*  902 */         } else if (!str3.equals(str18) || (!str4.equals(str19) && !"".equals(str7))) {
/*  903 */           (new BaseBean()).writeLog("fieldname" + str3);
/*  904 */           (new BaseBean()).writeLog("oldfieldname" + str18);
/*  905 */           (new BaseBean()).writeLog("fielddbtype" + str4);
/*  906 */           (new BaseBean()).writeLog("oldfielddbtype" + str19);
/*  907 */           (new BaseBean()).writeLog("type" + str7);
/*  908 */           this.recordSetTrans.executeSql("select " + str18 + " from " + paramString + " where " + str18 + " is not null");
/*  909 */           if (!this.recordSetTrans.next()) {
/*  910 */             this.recordSetTrans.executeSql("alter table " + paramString + " drop column " + str18);
/*  911 */             addFieldToTable(str6, str7, paramString, str3, str4, str5);
/*      */           } else {
/*  913 */             if (!str3.equals(str18)) {
/*  914 */               (new BaseBean()).writeLog("glb--->第2个changeFieldName");
/*  915 */               changeFieldName(paramString, str3, str18, str4);
/*      */             } 
/*  917 */             if (!str4.equals(str19) && !"".equals(str7)) {
/*      */               
/*  919 */               String str24 = str4;
/*      */               
/*  921 */               if (str6.equals("3") && (str7.equals("161") || str7.equals("162") || str7
/*  922 */                 .equals("256") || str7.equals("257") || str7.equals("224") || str7
/*  923 */                 .equals("225") || str7.equals("226") || str7.equals("227")))
/*      */               {
/*  925 */                 str24 = str5;
/*      */               }
/*  927 */               String str25 = "ALTER TABLE " + paramString + " MODIFY " + str3 + " " + str24;
/*  928 */               if (this.isMySQL) {
/*  929 */                 str25 = "ALTER TABLE " + paramString + " MODIFY COLUMN " + str3 + " " + str24;
/*  930 */               } else if (this.isSqlServer) {
/*  931 */                 str25 = "ALTER TABLE " + paramString + " ALTER COLUMN " + str3 + " " + str24;
/*      */               } 
/*      */               
/*  934 */               if (this.isMySQL && str24.indexOf("varchar") > -1) {
/*      */                 try {
/*  936 */                   this.recordSetTrans.executeSql(str25);
/*  937 */                 } catch (Exception exception) {
/*  938 */                   this.recordSetTrans.executeSql("ALTER TABLE " + paramString + " MODIFY COLUMN " + str3 + " text ");
/*      */                 } 
/*      */               } else {
/*  941 */                 this.recordSetTrans.executeSql(str25);
/*      */               } 
/*      */             } 
/*      */           } 
/*  945 */         } else if (str6.equals("3") && str7.equals("257") && str20.equals("256")) {
/*  946 */           String str = "ALTER TABLE " + paramString + " MODIFY " + str3 + " " + str5;
/*  947 */           if (this.isMySQL) {
/*  948 */             str = "ALTER TABLE " + paramString + " MODIFY COLUMN " + str3 + " " + str5;
/*  949 */           } else if (this.isSqlServer) {
/*  950 */             str = "ALTER TABLE " + paramString + " ALTER COLUMN " + str3 + " " + str5;
/*      */           } 
/*  952 */           this.recordSetTrans.executeSql(str);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  957 */     String str16 = str2;
/*  958 */     if (this.isBill == 1) {
/*  959 */       if (Strings.isNullOrEmpty(str16)) {
/*  960 */         this.recordSetTrans.executeSql("select max(id) as id from workflow_billfield");
/*  961 */         if (this.recordSetTrans.next()) {
/*  962 */           str16 = this.recordSetTrans.getString("id");
/*      */         }
/*      */       } 
/*      */       
/*  966 */       if ("".equals(str2) && "5".equals(str6)) {
/*  967 */         int i5 = paramFormFieldSetEntity.getSelectSourceId();
/*  968 */         this.quoteFieldManager.copySelectItemFromsource(this.recordSetTrans, Util.getIntValue(str16), i5);
/*      */       } 
/*      */       
/*  971 */       if (str6.equals("5")) {
/*  972 */         if (str14.equals("1")) {
/*  973 */           this.field_pubchoice_map.put(str16, "" + m);
/*  974 */         } else if (str14.equals("2")) {
/*  975 */           this.field_pubchilchoice_map.put(str16, "" + n);
/*      */         } 
/*      */       }
/*      */       
/*  979 */       if (Strings.isNullOrEmpty(str16)) {
/*  980 */         this.recordSetTrans.execute("select count(nodeid) as wfnfc from workflow_nodeform where fieldid=" + str16);
/*  981 */         int i5 = 0;
/*  982 */         if (this.recordSetTrans.next()) {
/*  983 */           i5 = Util.getIntValue(this.recordSetTrans.getString(1), 0);
/*      */         }
/*  985 */         if (i5 < 1) {
/*  986 */           this.recordSetTrans.executeSql("select nodeid from workflow_flownode where workflowid in (select id from workflow_base where formid=" + this.formId + " and isbill=1)");
/*  987 */           while (this.recordSetTrans.next()) {
/*  988 */             RecordSet recordSet = new RecordSet();
/*  989 */             recordSet.executeUpdate("insert into workflow_nodeform(nodeid,fieldid,isview,isedit,ismandatory) values(" + this.recordSetTrans.getString("nodeid") + "," + str16 + ",'0','0','0')", new Object[0]);
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       }
/*      */     
/*  996 */     } else if (Strings.isNullOrEmpty(str16)) {
/*  997 */       RecordSet recordSet = new RecordSet();
/*  998 */       recordSet.execute("select max(id) from workflow_formdict");
/*  999 */       if (recordSet.next()) {
/* 1000 */         str16 = recordSet.getString(1);
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1006 */     if (!Strings.isNullOrEmpty(str2)) {
/* 1007 */       this.recordSetTrans.executeSql("delete from workflow_specialfield where isbill=" + this.isBill + " and fieldid=" + str2);
/*      */     }
/* 1009 */     if (str6.equals("7")) {
/* 1010 */       String str17 = Util.null2String(paramFormFieldSetEntity.getDisplayName());
/* 1011 */       String str18 = Util.null2String(paramFormFieldSetEntity.getLinkAddress());
/* 1012 */       String str19 = Util.null2String(paramFormFieldSetEntity.getDescriptiveText());
/* 1013 */       str19 = Util.spacetoHtml(str19);
/* 1014 */       str19 = Util.htmlFilter4UTF8(str19);
/* 1015 */       String str20 = Util.null2String(paramFormFieldSetEntity.getType());
/* 1016 */       String str21 = "";
/* 1017 */       String str22 = (this.isBill == 1) ? "0" : "1";
/*      */ 
/*      */ 
/*      */       
/* 1021 */       str17 = SaveFormDatasBiz.convertSlash(str17);
/* 1022 */       str18 = SaveFormDatasBiz.convertSlash(str18);
/* 1023 */       str19 = SaveFormDatasBiz.convertSlash(str19);
/*      */       
/* 1025 */       if (str20.equals("1")) {
/* 1026 */         str21 = "insert into workflow_specialfield(fieldid,displayname,linkaddress,isform,isbill) values(" + str16 + ",'" + str17 + "','" + str18 + "'," + str22 + "," + this.isBill + ")";
/*      */       } else {
/* 1028 */         str21 = "insert into workflow_specialfield(fieldid,descriptivetext,isform,isbill) values(" + str16 + ",'" + str19 + "'," + str22 + "," + this.isBill + ")";
/*      */       } 
/* 1030 */       this.recordSetTrans.executeSql(str21);
/*      */     } 
/*      */ 
/*      */     
/* 1034 */     if (str6.equals("5") && !"1".equals(paramFormFieldSetEntity.getSelectItemType()) && !"2".equals(paramFormFieldSetEntity.getSelectItemType())) {
/* 1035 */       paramFormFieldSetEntity.setFieldId(str16);
/* 1036 */       this.selectFieldItemBiz.saveSelectItem(paramFormFieldSetEntity, this.isBill);
/*      */     } 
/*      */     
/* 1039 */     paramFormFieldSetEntity.setFieldId(str16);
/* 1040 */     hashMap.put("fieldId", str16);
/* 1041 */     hashMap.put("saveResult", str1);
/* 1042 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected int getFieldNameLabel(String paramString1, String paramString2) throws Exception {
/* 1053 */     paramString2 = Util.StringReplace(paramString2, "\"", "");
/* 1054 */     paramString2 = Util.StringReplace(paramString2, "'", "");
/*      */     
/* 1056 */     LabelUtil labelUtil = new LabelUtil();
/* 1057 */     int i = 0;
/*      */     
/* 1059 */     if (!Strings.isNullOrEmpty(paramString1)) {
/* 1060 */       this.recordSetTrans.executeQuery("select fieldlabel from workflow_billfield where id = ? ", new Object[] { paramString1 });
/* 1061 */       if (this.recordSetTrans.next()) {
/* 1062 */         i = this.recordSetTrans.getInt("fieldlabel");
/*      */       }
/*      */     } 
/*      */     
/* 1066 */     boolean bool = false;
/* 1067 */     if (i < 0) {
/* 1068 */       this.recordSetTrans.executeQuery("select count(*) as count from workflow_billfield where fieldlabel = ? ", new Object[] { Integer.valueOf(i) });
/* 1069 */       if (this.recordSetTrans.next() && this.recordSetTrans.getInt("count") > 1) {
/* 1070 */         bool = true;
/*      */       }
/*      */     } 
/*      */     
/* 1074 */     return (i >= 0 || bool) ? labelUtil.getLabelId4Workflow(paramString2) : labelUtil.getLabelId4Workflow(i, paramString2);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void createDetailTable(String paramString) throws Exception {
/* 1083 */     boolean bool = false;
/*      */     
/* 1085 */     String str1 = (new RecordSet()).getDBType();
/* 1086 */     String str2 = "select 1 from " + paramString;
/* 1087 */     if (str1.equalsIgnoreCase("oracle")) {
/* 1088 */       str2 = "select 1 from user_tables where TABLE_NAME = upper('" + paramString + "')";
/* 1089 */     } else if (str1.toLowerCase().indexOf("sqlserver") > -1 || str1.equalsIgnoreCase("sybase")) {
/* 1090 */       str2 = "select 1 from sys.objects where name = '" + paramString + "' ";
/* 1091 */     } else if (str1.equalsIgnoreCase("informix")) {
/* 1092 */       str2 = "select 1 from systables where lower(tabname) = lower('" + paramString + "') ";
/* 1093 */     } else if (str1.equalsIgnoreCase("mysql")) {
/* 1094 */       str2 = "select 1 from information_schema.Tables where table_schema = database() and Table_Name='" + paramString + "' ";
/*      */     }
/* 1096 */     else if (str1.equalsIgnoreCase("postgresql")) {
/* 1097 */       str2 = "select 1 from information_schema.Tables where table_schema = 'public' and lower(Table_Name)=lower('" + paramString + "') ";
/*      */     }
/* 1099 */     else if (str1.equalsIgnoreCase("db2")) {
/* 1100 */       str2 = "select 1 from SYSIBM.SYSTABLES where lower(name)= lower('" + paramString + "') ";
/*      */     } 
/* 1102 */     this.recordSetTrans.executeSql(str2);
/* 1103 */     if (this.recordSetTrans.next()) bool = true;
/*      */     
/* 1105 */     if (!bool) {
/* 1106 */       if (this.isOracle) {
/* 1107 */         this.recordSetTrans.executeSql("create table " + paramString + "(id integer primary key not null,mainid integer)");
/* 1108 */       } else if (this.isMySQL) {
/* 1109 */         this.recordSetTrans.executeSql("create table " + paramString + "(id int(11) NOT NULL AUTO_INCREMENT,mainid int(11),PRIMARY KEY (`id`))");
/*      */       }
/* 1111 */       else if (this.isPostgresql) {
/* 1112 */         this.recordSetTrans.executeSql("create table " + paramString + "(id serial not null ,mainid int ,PRIMARY KEY (id))");
/*      */       } else {
/*      */         
/* 1115 */         this.recordSetTrans.executeSql("create table " + paramString + "(id int IDENTITY(1,1) primary key CLUSTERED,mainid int)");
/*      */       } 
/* 1117 */       String str = paramString + "_mId";
/* 1118 */       this.recordSetTrans.executeSql(String.format("create index %s on %s (mainid)", new Object[] { str, paramString }));
/*      */       
/* 1120 */       this.recordSetTrans.executeSql("select id from Workflow_billdetailtable where billid=" + this.formId + " and tablename='" + paramString + "'");
/* 1121 */       if (!this.recordSetTrans.next()) {
/* 1122 */         this.detailTableOrderId++;
/* 1123 */         this.recordSetTrans.executeSql("INSERT INTO workflow_billdetailtable(billid,tablename,orderid) values(" + this.formId + ",'" + paramString + "'," + this.detailTableOrderId + ")");
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void insertSapMultiBrowser(String paramString1, String paramString2) throws Exception {
/* 1136 */     this.recordSetTrans.executeSql("delete from sap_multiBrowser where mxformname='" + paramString1 + "' and  mxformid='" + this.formId + "' ");
/*      */     
/* 1138 */     this.recordSetTrans.executeSql("select * from workflow_billfield where billid=" + this.formId + " and detailtable='" + paramString1 + "'");
/* 1139 */     if (this.recordSetTrans.next() && 
/* 1140 */       !"".equals(paramString2)) {
/*      */       
/* 1142 */       this.recordSetTrans.executeSql("select count(*) s from sap_multiBrowser where browsermark='" + paramString2 + "' and  mxformid='" + this.formId + "' ");
/* 1143 */       if (this.recordSetTrans.next() && this.recordSetTrans.getInt("s") <= 0)
/*      */       {
/* 1145 */         this.recordSetTrans.executeSql("insert into sap_multiBrowser (mxformname,mxformid,browsermark,isbill) values('" + paramString1 + "'," + this.formId + ",'" + paramString2 + "','1')");
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateDspOrder(FormFieldSetEntity paramFormFieldSetEntity) throws Exception {
/* 1156 */     String str1 = "update workflow_billfield set dsporder = ? where id = ? ";
/* 1157 */     String str2 = paramFormFieldSetEntity.getDspOrder();
/* 1158 */     str2 = "" + Util.getFloatValue(str2, 0.0F);
/*      */     
/* 1160 */     this.recordSetTrans.executeUpdate(str1, new Object[] { str2, paramFormFieldSetEntity.getFieldId() });
/*      */   }
/*      */ 
/*      */   
/*      */   public void submitOperate() {
/* 1165 */     this.recordSetTrans.commit();
/* 1166 */     if (this.isBill == 1) {
/*      */       
/* 1168 */       ArrayList<String> arrayList1 = Util.TokenizerString(this.labelidsCache, ",");
/* 1169 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 1170 */         this.labelComInfo.addLabeInfoCache(arrayList1.get(b));
/*      */       }
/*      */       
/* 1173 */       SelectItemManager selectItemManager = new SelectItemManager();
/*      */       
/* 1175 */       RecordSet recordSet1 = new RecordSet();
/* 1176 */       RecordSet recordSet2 = new RecordSet();
/* 1177 */       RecordSet recordSet3 = new RecordSet();
/*      */       
/* 1179 */       ArrayList<String> arrayList2 = new ArrayList();
/* 1180 */       String str1 = "";
/* 1181 */       StringBuffer stringBuffer = new StringBuffer();
/* 1182 */       stringBuffer.append("SELECT MAX(id) fieldid FROM workflow_billfield WHERE billid = ").append(this.formId).append(" AND selectItemType='2' AND pubchilchoiceId>0");
/* 1183 */       stringBuffer.append(" GROUP BY pubchilchoiceId");
/* 1184 */       stringBuffer.append(" HAVING COUNT(pubchilchoiceId) >1");
/* 1185 */       recordSet1.executeSql(stringBuffer.toString());
/* 1186 */       while (recordSet1.next()) {
/* 1187 */         arrayList2.add(recordSet1.getString("fieldid"));
/*      */       }
/*      */       
/* 1190 */       for (Map.Entry<String, String> entry : this.field_pubchoice_map.entrySet()) {
/* 1191 */         int i = Util.getIntValue((String)entry.getKey(), 0);
/* 1192 */         int j = Util.getIntValue((String)entry.getValue(), 0);
/* 1193 */         selectItemManager.setSelectOpBypubid(this.formId + "", j, i + "", 1, this.user.getLanguage());
/*      */       } 
/* 1195 */       for (Map.Entry<String, String> entry : this.field_pubchilchoice_map.entrySet()) {
/* 1196 */         int i = Util.getIntValue((String)entry.getKey(), 0);
/* 1197 */         int j = Util.getIntValue((String)entry.getValue(), 0);
/*      */         
/* 1199 */         if (arrayList2.contains(i + "")) {
/* 1200 */           str1 = str1 + "," + i;
/*      */           continue;
/*      */         } 
/* 1203 */         selectItemManager.setSuperSelectOp(this.formId + "", 1, j, i, this.user.getLanguage());
/*      */       } 
/*      */       
/* 1206 */       if (!str1.equals("")) {
/* 1207 */         str1 = str1.substring(1);
/* 1208 */         recordSet1.executeSql("update workflow_billfield set pubchilchoiceId=0 where id in (" + str1 + ")");
/*      */       } 
/*      */       
/* 1211 */       String str2 = "";
/* 1212 */       recordSet1.executeQuery("select * from workflow_billfield where billid = ? and selectitemtype = '1' and childfieldid <> '-1' and id not in (select pubchilchoiceid from workflow_billfield where billid = ? and selectitemtype = '2')", new Object[] { Integer.valueOf(this.formId), Integer.valueOf(this.formId) });
/* 1213 */       while (recordSet1.next()) {
/* 1214 */         str2 = str2 + "," + recordSet1.getString("id");
/*      */       }
/* 1216 */       if (!str2.equals("")) {
/* 1217 */         str2 = str2.substring(1);
/* 1218 */         recordSet1.executeSql("update workflow_billfield set childfieldid = '-1' where id in (" + str2 + ")");
/*      */       } 
/*      */ 
/*      */       
/* 1222 */       if (this.isOracle) {
/* 1223 */         recordSet1.executeSql("select tablename from Workflow_billdetailtable where billid=" + this.formId);
/* 1224 */         while (recordSet1.next()) {
/* 1225 */           String str = recordSet1.getString("tablename");
/* 1226 */           recordSet2.executeSql("select * from user_triggers where upper(trigger_name)=upper('" + str + "_Id_Tr')");
/* 1227 */           if (!recordSet2.next()) {
/* 1228 */             int i = 0;
/* 1229 */             recordSet3.execute("select max(id) from " + str + "");
/* 1230 */             if (recordSet3.next()) {
/* 1231 */               i = Util.getIntValue(recordSet3.getString(1), 0);
/*      */             }
/* 1233 */             i++;
/*      */             try {
/* 1235 */               recordSet3.executeSql("select  1 from user_sequences where upper(sequence_name)=upper('" + str + "_Id')");
/* 1236 */               if (recordSet3.next()) {
/* 1237 */                 recordSet3.executeSql("drop sequence " + str + "_Id");
/*      */               }
/* 1239 */             } catch (Exception exception) {}
/* 1240 */             String str3 = "";
/* 1241 */             str3 = " MAXVALUE 9223372036854775807 ";
/* 1242 */             recordSet3.executeSql("create sequence " + str + "_Id start with " + i + " increment by 1 " + str3 + " nocycle nocache");
/* 1243 */             recordSet3.setChecksql(false);
/* 1244 */             recordSet3.executeSql("CREATE OR REPLACE TRIGGER " + str + "_Id_Tr before insert on " + str + " for each row begin select " + str + "_Id.nextval into :new.id from dual; end;");
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1250 */       recordSet1.execute("select pubchilchoiceid,id from workflow_billfield where billid = " + this.formId + " and pubchoiceid = 0 and pubchilchoiceid > 0 and pubchoiceid = 0 and pubchilchoiceid > 0 ");
/* 1251 */       while (recordSet1.next()) {
/* 1252 */         int i = Util.getIntValue(recordSet1.getString(1), 0);
/* 1253 */         int j = Util.getIntValue(recordSet1.getString(2), 0);
/* 1254 */         recordSet2.execute("update workflow_billfield set pubchoiceid =(select pubchoiceId from workflow_billfield where id = " + i + ") where id = " + j);
/*      */       } 
/*      */ 
/*      */       
/* 1258 */       if (this.isSqlServer) {
/* 1259 */         recordSet1.executeSql("update workflow_billfield set detailtable = '' where detailtable is null");
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void rollBackOperate() {
/* 1270 */     this.recordSetTrans.rollback();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public FormFieldSetEntity resolveFieldType(FormFieldSetEntity paramFormFieldSetEntity, String paramString) {
/*      */     try {
/* 1282 */       paramString = paramString.replace("＋", "+");
/* 1283 */       byte[] arrayOfByte = Base64.getDecoder().decode(paramString.replace("\r\n", ""));
/* 1284 */       paramString = new String(arrayOfByte, "UTF-8");
/* 1285 */     } catch (Exception exception) {
/* 1286 */       exception.printStackTrace();
/*      */     } 
/* 1288 */     JSONArray jSONArray = JSONArray.parseArray(paramString);
/* 1289 */     if (jSONArray == null || jSONArray.size() == 0) return paramFormFieldSetEntity; 
/* 1290 */     String str = jSONArray.get(0).toString();
/* 1291 */     if ("input".equalsIgnoreCase(str)) {
/* 1292 */       String str1 = jSONArray.get(1).toString();
/* 1293 */       paramFormFieldSetEntity.setFieldHtmlType("1");
/* 1294 */       if ("text".equals(str1)) {
/* 1295 */         paramFormFieldSetEntity.setType("1");
/* 1296 */         paramFormFieldSetEntity.setStrLength(jSONArray.getString(2));
/* 1297 */       } else if ("int".equals(str1)) {
/* 1298 */         paramFormFieldSetEntity.setType("2");
/* 1299 */       } else if ("float".equals(str1)) {
/* 1300 */         paramFormFieldSetEntity.setType("3");
/* 1301 */         paramFormFieldSetEntity.setQfws(Util.getIntValue(jSONArray.getString(2)));
/* 1302 */       } else if ("convert".equals(str1)) {
/* 1303 */         paramFormFieldSetEntity.setType("4");
/* 1304 */       } else if ("thousandth".equals(str1)) {
/* 1305 */         paramFormFieldSetEntity.setType("5");
/* 1306 */         paramFormFieldSetEntity.setQfws(Util.getIntValue(jSONArray.getString(2)));
/*      */       } 
/* 1308 */     } else if ("textarea".equalsIgnoreCase(str)) {
/* 1309 */       paramFormFieldSetEntity.setFieldHtmlType("2");
/* 1310 */       paramFormFieldSetEntity.setTextHeight(Util.getIntValue(jSONArray.getString(1), 4));
/* 1311 */       String str1 = "1";
/* 1312 */       if (jSONArray.size() == 3) {
/* 1313 */         String str2 = Util.null2String(jSONArray.getString(2));
/* 1314 */         if (str2.equals("1")) {
/* 1315 */           str1 = "2";
/*      */         }
/*      */       } 
/* 1318 */       paramFormFieldSetEntity.setType(str1);
/* 1319 */     } else if ("browser".equalsIgnoreCase(str)) {
/* 1320 */       paramFormFieldSetEntity.setFieldHtmlType("3");
/* 1321 */       int i = Util.getIntValue(jSONArray.getJSONObject(1).getString("value"));
/* 1322 */       paramFormFieldSetEntity.setType(String.valueOf(i));
/* 1323 */       if (i == 161 || i == 162) {
/* 1324 */         String str1 = Util.null2String(jSONArray.getJSONObject(2).getString("value"));
/* 1325 */         if (!str1.startsWith("browser.")) {
/* 1326 */           str1 = "browser." + str1;
/*      */         }
/* 1328 */         paramFormFieldSetEntity.setFieldDBType(str1);
/* 1329 */       } else if (i == 256 || i == 257 || i == 224 || i == 225) {
/* 1330 */         paramFormFieldSetEntity.setFieldDBType(jSONArray.getJSONObject(2).getString("value"));
/* 1331 */       } else if (i == 165 || i == 166 || i == 167 || i == 168 || i == 169 || i == 170) {
/* 1332 */         paramFormFieldSetEntity.setTextHeight_2(jSONArray.getString(2));
/* 1333 */       } else if (i == 226 || i == 227) {
/* 1334 */         paramFormFieldSetEntity.setFieldDBType(jSONArray.getString(2));
/*      */       }
/*      */     
/* 1337 */     } else if ("check".equalsIgnoreCase(str)) {
/* 1338 */       paramFormFieldSetEntity.setFieldHtmlType("4");
/* 1339 */       paramFormFieldSetEntity.setType("1");
/* 1340 */     } else if ("select".equalsIgnoreCase(str)) {
/* 1341 */       paramFormFieldSetEntity.setFieldHtmlType("5");
/* 1342 */       paramFormFieldSetEntity.setFieldShowTypes("1");
/* 1343 */       String str1 = jSONArray.get(1).toString();
/* 1344 */       String str2 = jSONArray.get(2).toString();
/* 1345 */       if ("select".equals(str1)) {
/* 1346 */         paramFormFieldSetEntity.setType("1");
/* 1347 */       } else if ("check".equals(str1)) {
/* 1348 */         paramFormFieldSetEntity.setType("2");
/* 1349 */       } else if ("radio".equals(str1)) {
/* 1350 */         paramFormFieldSetEntity.setType("3");
/*      */       } 
/* 1352 */       JSONObject jSONObject = new JSONObject();
/* 1353 */       if (this.isBill == 1) {
/* 1354 */         jSONObject = jSONArray.getJSONObject(3);
/*      */       } else {
/* 1356 */         jSONObject = jSONArray.getJSONObject(2);
/*      */       } 
/* 1358 */       if ("alone".equals(str2)) {
/* 1359 */         paramFormFieldSetEntity.setSelectItemType("0");
/* 1360 */       } else if ("pub".equals(str2)) {
/* 1361 */         paramFormFieldSetEntity.setSelectItemType("1");
/* 1362 */         paramFormFieldSetEntity.setPubChoiceId(Util.getIntValue(jSONObject.getString("value")));
/* 1363 */       } else if ("pubchild".equals(str2)) {
/* 1364 */         paramFormFieldSetEntity.setSelectItemType("2");
/* 1365 */         paramFormFieldSetEntity.setPubChilChoiceId(Util.getIntValue(jSONObject.getString("value")));
/*      */       } 
/*      */       
/* 1368 */       paramFormFieldSetEntity.setSelectItems(jSONObject.getJSONArray("datas"));
/* 1369 */       paramFormFieldSetEntity.setChildFieldId(Util.getIntValue(jSONObject.getString("subFieldId")));
/*      */ 
/*      */       
/* 1372 */       if ("check".equals(str1) || "radio".equals(str1)) {
/* 1373 */         String str3 = "";
/* 1374 */         if ("pub".equals(str2) || "pubchild".equals(str2)) {
/*      */           try {
/* 1376 */             str3 = Util.null2String(jSONArray.getJSONObject(4).getString("sort"));
/* 1377 */           } catch (Exception exception) {
/* 1378 */             str3 = "1";
/*      */           } 
/*      */         } else {
/* 1381 */           str3 = Util.null2String(jSONObject.getString("sort"));
/*      */         } 
/* 1383 */         if ("horizontal".equals(str3)) {
/* 1384 */           paramFormFieldSetEntity.setFieldShowTypes("1");
/* 1385 */         } else if ("vertical".equals(str3)) {
/* 1386 */           paramFormFieldSetEntity.setFieldShowTypes("2");
/*      */         }
/*      */       
/*      */       }
/*      */     
/* 1391 */     } else if ("upload".equalsIgnoreCase(str)) {
/* 1392 */       String str1 = Util.null2String(jSONArray.getString(1));
/* 1393 */       paramFormFieldSetEntity.setFieldHtmlType("6");
/* 1394 */       if ("file".equals(str1)) {
/* 1395 */         paramFormFieldSetEntity.setType("1");
/* 1396 */       } else if ("image".equals(str1)) {
/* 1397 */         paramFormFieldSetEntity.setType("2");
/* 1398 */         if (jSONArray.size() == 5) {
/* 1399 */           paramFormFieldSetEntity.setTextHeight(Util.getIntValue(jSONArray.getString(2), 5));
/* 1400 */           paramFormFieldSetEntity.setImgWidth(Util.getIntValue(jSONArray.getString(3), 0));
/* 1401 */           paramFormFieldSetEntity.setImgHeight(Util.getIntValue(jSONArray.getString(4), 0));
/* 1402 */         } else if (jSONArray.size() == 4) {
/* 1403 */           paramFormFieldSetEntity.setImgWidth(Util.getIntValue(jSONArray.getString(2), 0));
/* 1404 */           paramFormFieldSetEntity.setImgHeight(Util.getIntValue(jSONArray.getString(3), 0));
/*      */         } 
/*      */       } 
/* 1407 */     } else if ("peculiar".equalsIgnoreCase(str)) {
/* 1408 */       paramFormFieldSetEntity.setFieldHtmlType("7");
/* 1409 */       String str1 = Util.null2String(jSONArray.getString(1));
/* 1410 */       if ("href".equals(str1)) {
/* 1411 */         paramFormFieldSetEntity.setType("1");
/* 1412 */         paramFormFieldSetEntity.setDisplayName(Util.null2String(jSONArray.getString(2)));
/* 1413 */         paramFormFieldSetEntity.setLinkAddress(Util.null2String(jSONArray.getString(3)));
/* 1414 */       } else if ("desc".equals(str1)) {
/* 1415 */         paramFormFieldSetEntity.setType("2");
/* 1416 */         paramFormFieldSetEntity.setDescriptiveText(Util.null2String(jSONArray.getString(2)));
/*      */       } 
/* 1418 */     } else if ("mobile".equalsIgnoreCase(str)) {
/* 1419 */       paramFormFieldSetEntity.setFieldHtmlType("9");
/* 1420 */       paramFormFieldSetEntity.setType(Util.null2String(jSONArray.getString(1)));
/*      */     } 
/* 1422 */     return paramFormFieldSetEntity;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Object> getFieldTypeData(FieldManager paramFieldManager) {
/* 1431 */     return getFieldTypeData(paramFieldManager, 0, 0, 0, false);
/*      */   }
/*      */   
/*      */   public List<Object> getFieldTypeData(int paramInt1, int paramInt2) {
/* 1435 */     return getFieldTypeData(null, 1, paramInt1, paramInt2, false);
/*      */   }
/*      */   
/*      */   public List<Object> getFieldTypeData(int paramInt1, int paramInt2, boolean paramBoolean) {
/* 1439 */     return getFieldTypeData(null, 1, paramInt1, paramInt2, paramBoolean);
/*      */   }
/*      */   
/*      */   private List<Object> getFieldTypeData(FieldManager paramFieldManager, int paramInt1, int paramInt2, int paramInt3, boolean paramBoolean) {
/* 1443 */     return getFieldTypeData(paramFieldManager, paramInt1, paramInt2, paramInt3, paramBoolean, true);
/*      */   }
/*      */ 
/*      */   
/*      */   public List<Object> getFieldTypeData(FieldManager paramFieldManager, int paramInt1, int paramInt2, int paramInt3, boolean paramBoolean1, boolean paramBoolean2) {
/* 1448 */     ArrayList<String> arrayList = new ArrayList();
/* 1449 */     SelectFieldItemBiz selectFieldItemBiz = new SelectFieldItemBiz();
/* 1450 */     RecordSet recordSet1 = new RecordSet();
/* 1451 */     RecordSet recordSet2 = new RecordSet();
/*      */     
/* 1453 */     String str1 = "";
/* 1454 */     String str2 = "";
/* 1455 */     String str3 = "";
/* 1456 */     String str4 = "";
/* 1457 */     String str5 = "";
/* 1458 */     int i = 0;
/* 1459 */     int j = 0;
/* 1460 */     String str6 = "";
/*      */ 
/*      */     
/* 1463 */     int k = 0;
/*      */     
/* 1465 */     String str7 = "";
/* 1466 */     String str8 = "0";
/* 1467 */     int m = 0;
/* 1468 */     int n = 0;
/* 1469 */     String str9 = "";
/*      */     
/* 1471 */     if (paramInt1 == 1) {
/* 1472 */       String str = "select t.*,(select 1 from workflow_billfield chit where chit.id = t.childfieldid and chit.fieldhtmltype = 5 and chit.selectitemtype = 2) as existschild from workflow_billfield t where billid=" + paramInt2 + " and id = " + paramInt3 + " order by dsporder,id";
/* 1473 */       recordSet1.executeQuery(str, new Object[0]);
/* 1474 */       if (recordSet1.next()) {
/*      */         
/* 1476 */         str1 = recordSet1.getString("fielddbtype");
/* 1477 */         str2 = recordSet1.getString("fieldhtmltype");
/* 1478 */         str3 = recordSet1.getString("type");
/* 1479 */         if (str2.equals("1") && str3.equals("1")) {
/* 1480 */           str9 = str1.substring(str1.indexOf("(") + 1, str1.indexOf(")"));
/*      */         }
/* 1482 */         j = Util.getIntValue(Util.null2String(recordSet1.getString("qfws")), 2);
/* 1483 */         i = Util.getIntValue(Util.null2String(recordSet1.getString("textheight")), 0);
/* 1484 */         str6 = Util.null2String(recordSet1.getString("textheight_2"));
/* 1485 */         str4 = "" + Util.getIntValue(Util.null2String(recordSet1.getString("imgwidth")), 0);
/* 1486 */         str5 = "" + Util.getIntValue(Util.null2String(recordSet1.getString("imgheight")), 0);
/* 1487 */         str7 = Util.null2String(recordSet1.getString("fieldshowtypes"), "1");
/* 1488 */         if (str7.equals("")) str7 = "1";
/*      */ 
/*      */         
/* 1491 */         str8 = Util.null2o(recordSet1.getString("selectItemType"));
/* 1492 */         m = Util.getIntValue(Util.null2String(recordSet1.getString("pubchoiceId")), 0);
/* 1493 */         n = Util.getIntValue(Util.null2String(recordSet1.getString("pubchilchoiceId")), 0);
/*      */       } 
/*      */     } else {
/*      */       
/* 1497 */       paramInt3 = paramFieldManager.getFieldid();
/* 1498 */       str1 = paramFieldManager.getFielddbtype();
/* 1499 */       str2 = paramFieldManager.getFieldhtmltype();
/* 1500 */       str3 = paramFieldManager.getType() + "";
/* 1501 */       if (str2.equals("1") && str3.equals("1")) {
/* 1502 */         str9 = str1.substring(str1.indexOf("(") + 1, str1.indexOf(")"));
/*      */       }
/* 1504 */       j = Util.getIntValue(paramFieldManager.getQfwws());
/* 1505 */       i = paramFieldManager.getTextheight();
/* 1506 */       str6 = paramFieldManager.getTextheight_2();
/* 1507 */       str4 = paramFieldManager.getImgwidth() + "";
/* 1508 */       str5 = "" + paramFieldManager.getImgheight();
/* 1509 */       str7 = paramFieldManager.getFieldshowtypes();
/* 1510 */       k = paramFieldManager.getChildfieldid();
/*      */     } 
/*      */     
/* 1513 */     if ("1".equals(str2)) {
/* 1514 */       arrayList.add("input");
/* 1515 */       if ("1".equals(str3)) {
/* 1516 */         arrayList.add("text");
/* 1517 */         arrayList.add(str9);
/* 1518 */       } else if ("2".equals(str3)) {
/* 1519 */         arrayList.add("int");
/* 1520 */       } else if ("3".equals(str3) || "5".equals(str3)) {
/* 1521 */         int i1 = 2;
/* 1522 */         if (str3.equals("3")) {
/* 1523 */           arrayList.add("float");
/* 1524 */           int i2 = str1.indexOf(",");
/* 1525 */           if (i2 > -1) {
/* 1526 */             i1 = Util.getIntValue(str1.substring(i2 + 1, str1.length() - 1), 2);
/*      */           } else {
/* 1528 */             i1 = 2;
/*      */           } 
/*      */         } 
/* 1531 */         if (str3.equals("5")) {
/* 1532 */           arrayList.add("thousandth");
/* 1533 */           i1 = j;
/*      */         } 
/* 1535 */         arrayList.add(Integer.valueOf(i1));
/* 1536 */       } else if ("4".equals(str3)) {
/* 1537 */         arrayList.add("convert");
/*      */       } 
/* 1539 */     } else if ("2".equals(str2)) {
/* 1540 */       arrayList.add("textarea");
/* 1541 */       arrayList.add(Integer.valueOf(i));
/* 1542 */       arrayList.add("2".equals(str3) ? "1" : "0");
/* 1543 */     } else if ("3".equals(str2)) {
/* 1544 */       arrayList.add("browser");
/* 1545 */       String str = Util.formatMultiLang(SystemEnv.getHtmlLabelName(Util.getIntValue(this.browserComInfo.getBrowserlabelid(str3)), this.user.getLanguage()));
/*      */       
/* 1547 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1548 */       hashMap1.put("value", str3);
/* 1549 */       hashMap1.put("valueSpan", str);
/*      */       
/* 1551 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 1552 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1553 */       hashMap2.put("id", str3);
/* 1554 */       hashMap2.put("name", str);
/* 1555 */       arrayList1.add(hashMap2);
/* 1556 */       hashMap1.put("replaceDatas", arrayList1);
/*      */       
/* 1558 */       arrayList.add(hashMap1);
/*      */       
/* 1560 */       int i1 = Util.getIntValue(str3);
/* 1561 */       if (i1 == 256 || i1 == 257 || i1 == 161 || i1 == 162 || i1 == 224 || i1 == 225) {
/*      */         
/* 1563 */         String str10 = str1;
/* 1564 */         if (i1 == 161 || i1 == 162) {
/* 1565 */           str10 = this.userDefinedBrowserTypeComInfo.getName(str1);
/* 1566 */         } else if (i1 == 256 || i1 == 257) {
/* 1567 */           String str11 = "select treename from mode_customtree where id = " + str1;
/* 1568 */           RecordSet recordSet = new RecordSet();
/* 1569 */           recordSet.execute(str11);
/* 1570 */           if (recordSet.next()) {
/* 1571 */             str10 = recordSet.getString("treename");
/*      */           }
/*      */         } 
/* 1574 */         str10 = Util.formatMultiLang(str10);
/* 1575 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 1576 */         hashMap3.put("id", str1);
/* 1577 */         hashMap3.put("name", str10);
/*      */         
/* 1579 */         ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 1580 */         arrayList2.add(hashMap3);
/*      */         
/* 1582 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 1583 */         hashMap4.put("value", str1);
/* 1584 */         hashMap4.put("valueSpan", str10);
/* 1585 */         hashMap4.put("replaceDatas", arrayList2);
/*      */         
/* 1587 */         arrayList.add(hashMap4);
/*      */       }
/* 1589 */       else if (i1 == 165 || i1 == 166 || i1 == 167 || i1 == 168 || i1 == 169 || i1 == 170) {
/* 1590 */         arrayList.add(str6);
/* 1591 */       } else if (i1 == 226 || i1 == 227) {
/* 1592 */         arrayList.add(str1);
/*      */       } 
/* 1594 */     } else if ("4".equals(str2)) {
/* 1595 */       arrayList.add("check");
/* 1596 */     } else if ("5".equals(str2)) {
/* 1597 */       arrayList.add("select");
/*      */       
/* 1599 */       if ("2".equals(str3)) {
/* 1600 */         arrayList.add("check");
/* 1601 */       } else if ("3".equals(str3)) {
/* 1602 */         arrayList.add("radio");
/*      */       } else {
/* 1604 */         arrayList.add("select");
/*      */       } 
/*      */       
/* 1607 */       if (paramInt1 == 1) {
/* 1608 */         if ("0".equals(str8)) {
/* 1609 */           arrayList.add("alone");
/* 1610 */           Map<String, Object> map = null;
/* 1611 */           if (paramBoolean2) {
/* 1612 */             map = selectFieldItemBiz.getSelectItemDatas(paramInt3, paramInt1, paramInt2, this.isDetail ? 1 : 0, this.user);
/*      */           } else {
/* 1614 */             map = selectFieldItemBiz.getSelectItemDatas(paramInt3, paramInt1, this.isDetail ? 1 : 0, this.user);
/*      */           } 
/* 1616 */           map.put("sort", "2".equals(str7) ? "vertical" : "horizontal");
/* 1617 */           arrayList.add(map);
/* 1618 */         } else if ("1".equals(str8)) {
/* 1619 */           arrayList.add("pub");
/*      */           
/* 1621 */           String str10 = "select id,selectitemname from mode_selectitempage  where id = " + m;
/* 1622 */           RecordSet recordSet = new RecordSet();
/* 1623 */           recordSet.execute(str10);
/* 1624 */           String str11 = "";
/* 1625 */           while (recordSet.next()) {
/*      */             
/* 1627 */             str11 = recordSet.getString("id");
/* 1628 */             String str = Util.formatMultiLang(recordSet.getString("selectitemname"));
/*      */             
/* 1630 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1631 */             hashMap1.put("id", str11);
/* 1632 */             hashMap1.put("name", str);
/* 1633 */             hashMap1.put("hasdetail", "1");
/*      */             
/* 1635 */             ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 1636 */             arrayList2.add(hashMap1);
/*      */             
/* 1638 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1639 */             hashMap2.put("value", str11);
/* 1640 */             hashMap2.put("valueSpan", str);
/*      */             
/* 1642 */             hashMap2.put("replaceDatas", arrayList2);
/*      */             
/* 1644 */             arrayList.add(hashMap2);
/*      */           } 
/* 1646 */           recordSet.executeQuery("select * from mode_selectitempagedetail where mainid = ? and statelev = 1 and cancel <> 1 order by disorder", new Object[] { str11 });
/* 1647 */           ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 1648 */           while (recordSet.next()) {
/* 1649 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 1650 */             hashMap.put("key", recordSet.getString("id"));
/* 1651 */             hashMap.put("showname", recordSet.getString("name"));
/* 1652 */             hashMap.put("selected", Boolean.valueOf(false));
/* 1653 */             arrayList1.add(hashMap);
/*      */           } 
/* 1655 */           if (paramBoolean1 && "1".equals(str3)) {
/* 1656 */             arrayList.add(arrayList1);
/* 1657 */           } else if ("2".equals(str3) || "3".equals(str3)) {
/* 1658 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 1659 */             hashMap.put("value", arrayList1);
/* 1660 */             hashMap.put("sort", "2".equals(str7) ? "vertical" : "horizontal");
/* 1661 */             arrayList.add(hashMap);
/*      */           } 
/* 1663 */         } else if ("2".equals(str8)) {
/* 1664 */           arrayList.add("pubchild");
/* 1665 */           String str = "select id,fieldlabel,fieldname from workflow_billfield  where billid = " + this.formId + " and id=" + n;
/* 1666 */           RecordSet recordSet = new RecordSet();
/* 1667 */           recordSet.execute(str);
/* 1668 */           while (recordSet.next()) {
/*      */             
/* 1670 */             String str10 = recordSet.getString("id");
/* 1671 */             String str11 = Util.formatMultiLang(SystemEnv.getHtmlLabelName(recordSet.getInt("fieldlabel"), this.user.getLanguage()));
/* 1672 */             if (Strings.isNullOrEmpty(str11)) {
/* 1673 */               recordSet.getString("fieldname");
/*      */             }
/* 1675 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1676 */             hashMap1.put("id", str10);
/* 1677 */             hashMap1.put("name", str11);
/* 1678 */             hashMap1.put("hasdetail", "1");
/*      */             
/* 1680 */             ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 1681 */             arrayList1.add(hashMap1);
/*      */             
/* 1683 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1684 */             hashMap2.put("value", str10);
/* 1685 */             hashMap2.put("valueSpan", str11);
/*      */             
/* 1687 */             hashMap2.put("replaceDatas", arrayList1);
/*      */             
/* 1689 */             arrayList.add(hashMap2);
/*      */           } 
/* 1691 */           if ("2".equals(str3) || "3".equals(str3)) {
/* 1692 */             HashMap<Object, Object> hashMap = new HashMap<>();
/*      */             
/* 1694 */             hashMap.put("sort", "2".equals(str7) ? "vertical" : "horizontal");
/* 1695 */             arrayList.add(hashMap);
/*      */           } 
/*      */         } 
/*      */       } else {
/* 1699 */         Map<String, Object> map = selectFieldItemBiz.getSelectItemDatas(paramInt3, paramInt1, paramInt2, this.isDetail ? 1 : 0, this.user);
/* 1700 */         map.put("sort", "2".equals(str7) ? "vertical" : "horizontal");
/* 1701 */         map.put("subFieldId", String.valueOf(k));
/* 1702 */         arrayList.add(map);
/*      */       } 
/* 1704 */     } else if ("6".equals(str2)) {
/* 1705 */       arrayList.add("upload");
/* 1706 */       if ("1".equals(str3)) {
/* 1707 */         arrayList.add("file");
/* 1708 */       } else if ("2".equals(str3)) {
/* 1709 */         arrayList.add("image");
/* 1710 */         if (!this.isDetail) {
/* 1711 */           arrayList.add(Integer.valueOf(i));
/*      */         }
/* 1713 */         arrayList.add(str4);
/* 1714 */         arrayList.add(str5);
/*      */       } 
/* 1716 */     } else if ("7".equals(str2)) {
/* 1717 */       arrayList.add("peculiar");
/* 1718 */       String str10 = "";
/* 1719 */       String str11 = "";
/* 1720 */       String str12 = "";
/* 1721 */       recordSet2.executeQuery("select * from workflow_specialfield where fieldid = " + paramInt3 + " and isbill = " + paramInt1 + " ", new Object[0]);
/* 1722 */       recordSet2.next();
/* 1723 */       str10 = recordSet2.getString("displayname");
/* 1724 */       str11 = recordSet2.getString("linkaddress");
/* 1725 */       str12 = recordSet2.getString("descriptivetext");
/* 1726 */       if ("1".equals(str3)) {
/* 1727 */         arrayList.add("href");
/* 1728 */         arrayList.add(str10);
/* 1729 */         arrayList.add(str11);
/* 1730 */       } else if ("2".equals(str3)) {
/* 1731 */         arrayList.add("desc");
/* 1732 */         str12 = Util.StringReplace(str12, "<br>", "\n");
/* 1733 */         str12 = Util.StringReplace(str12, "&nbsp;", " ");
/* 1734 */         arrayList.add(str12);
/*      */       } 
/* 1736 */     } else if ("9".equals(str2)) {
/* 1737 */       arrayList.add("mobile");
/* 1738 */       arrayList.add(str3);
/*      */     } 
/* 1740 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getFieldInfo(int paramInt1, int paramInt2) throws Exception {
/* 1750 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1751 */     ArrayList arrayList = new ArrayList();
/*      */ 
/*      */     
/* 1754 */     if (this.isBill == 0 && paramInt1 != -1) {
/*      */       
/* 1756 */       FieldManager fieldManager = new FieldManager();
/* 1757 */       fieldManager.setFieldid(paramInt1);
/*      */       
/* 1759 */       if (this.isDetail) {
/* 1760 */         fieldManager.getDetailFieldInfo();
/*      */       } else {
/* 1762 */         fieldManager.getFieldInfo();
/*      */       } 
/* 1764 */       List<Object> list = getFieldTypeData(fieldManager);
/*      */       
/* 1766 */       String str1 = String.valueOf(fieldManager.getFieldid());
/* 1767 */       String str2 = fieldManager.getFieldname();
/* 1768 */       hashMap.put("fieldId", str1);
/* 1769 */       hashMap.put("fieldName", str2);
/* 1770 */       hashMap.put("description", fieldManager.getDescription());
/* 1771 */       hashMap.put("istemplate", fieldManager.getIstemplate());
/* 1772 */       hashMap.put("fieldType", list);
/*      */ 
/*      */       
/* 1775 */       if (!this.isDetail) {
/*      */         
/* 1777 */         ArrayList<String> arrayList1 = new ArrayList();
/*      */         
/* 1779 */         RecordSet recordSet = new RecordSet();
/* 1780 */         recordSet.executeQuery("select distinct fieldid from workflow_formfield where formid=14", new Object[0]);
/* 1781 */         while (recordSet.next()) {
/* 1782 */           arrayList1.add(recordSet.getString(1));
/*      */         }
/*      */         
/* 1785 */         if (!Util.toScreen(str2, paramInt2).equals("manager") && !Util.toScreen(str2, paramInt2).equals("president") && !arrayList1.contains(str1)) {
/* 1786 */           hashMap.put("isSysField", Boolean.valueOf(false));
/*      */         } else {
/* 1788 */           hashMap.put("isSysField", Boolean.valueOf(true));
/*      */         }
/*      */       
/*      */       }
/* 1792 */       else if (!Util.toScreen(str2, paramInt2).equals("manager") && !Util.toScreen(str2, paramInt2).equals("president")) {
/* 1793 */         hashMap.put("isSysField", Boolean.valueOf(false));
/*      */       } else {
/* 1795 */         hashMap.put("isSysField", Boolean.valueOf(true));
/*      */       }
/*      */     
/*      */     }
/* 1799 */     else if (this.isBill == 1 && paramInt1 != -1) {
/*      */       
/* 1801 */       List<Object> list = getFieldTypeData(this.formId, paramInt1);
/*      */       
/* 1803 */       String str = "select t.id,t.fieldname,t.fieldlabel,t.viewtype,t.billid,t.detailtable,t.dsporder,t.fieldgroupid from workflow_billfield t where id =" + paramInt1;
/* 1804 */       RecordSet recordSet = new RecordSet();
/* 1805 */       recordSet.execute(str);
/* 1806 */       while (recordSet.next()) {
/*      */         
/* 1808 */         hashMap.put("fieldId", Integer.valueOf(recordSet.getInt("id")));
/* 1809 */         hashMap.put("fieldName", recordSet.getString("fieldname"));
/* 1810 */         hashMap.put("fieldType", list);
/* 1811 */         int i = recordSet.getInt("fieldlabel");
/* 1812 */         hashMap.put("description", LabelUtil.getMultiLangLabel(i + ""));
/* 1813 */         hashMap.put("groupid", recordSet.getString("fieldgroupid"));
/*      */         
/* 1815 */         int j = Util.getIntValue(recordSet.getString("viewtype"));
/* 1816 */         String str1 = Util.null2String(recordSet.getString("billid"));
/*      */ 
/*      */         
/* 1819 */         if (j == 0) {
/* 1820 */           String str2 = "select tablename from workflow_bill where id=" + str1;
/* 1821 */           RecordSet recordSet1 = new RecordSet();
/* 1822 */           recordSet1.execute(str2);
/* 1823 */           while (recordSet1.next()) {
/* 1824 */             hashMap.put("ownerTable", Util.null2String(recordSet1.getString("tablename")));
/*      */           }
/*      */         } else {
/* 1827 */           hashMap.put("ownerTable", Util.null2String(recordSet.getString("detailtable")));
/*      */         } 
/*      */         
/* 1830 */         hashMap.put("orderNo", recordSet.getString("dsporder"));
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1835 */     if (this.isBill == 1 && this.formId > 0 && paramInt1 == -1) {
/*      */       
/* 1837 */       String str = "select max(dsporder) from workflow_billfield where billid=" + this.formId;
/* 1838 */       RecordSet recordSet = new RecordSet();
/* 1839 */       recordSet.execute(str);
/* 1840 */       if (recordSet.next()) {
/* 1841 */         double d1 = recordSet.getDouble(1);
/* 1842 */         double d2 = Math.ceil(d1);
/* 1843 */         hashMap.put("orderNo", Double.valueOf((d2 > d1) ? d2 : ++d2));
/*      */       } 
/*      */     } 
/* 1846 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isUsed(int paramInt1, int paramInt2) {
/* 1857 */     boolean bool = false;
/*      */     
/* 1859 */     if (paramInt1 != -1) {
/* 1860 */       if (this.isBill == 0) {
/* 1861 */         String str = "select distinct fieldid from workflow_formfield ";
/* 1862 */         HashSet<String> hashSet = new HashSet();
/* 1863 */         RecordSet recordSet = new RecordSet();
/* 1864 */         recordSet.executeQuery(str, new Object[0]);
/* 1865 */         while (recordSet.next()) {
/* 1866 */           hashSet.add(recordSet.getString(1));
/*      */         }
/* 1868 */         if (hashSet.contains(String.valueOf(paramInt1))) {
/* 1869 */           bool = true;
/*      */         }
/*      */       } 
/* 1872 */       if (this.isBill == 1)
/*      */       {
/* 1874 */         if (paramInt2 == 0) {
/* 1875 */           RecordSet recordSet = new RecordSet();
/* 1876 */           recordSet.executeQuery("select 1 from workflow_base where formid= ?", new Object[] { Integer.valueOf(this.formId) });
/* 1877 */           if (recordSet.getCounts() > 0) {
/* 1878 */             bool = true;
/*      */           }
/* 1880 */           recordSet.executeQuery("select 1 from modeinfo where formid = ?", new Object[] { Integer.valueOf(this.formId) });
/* 1881 */           if (recordSet.getCounts() > 0) {
/* 1882 */             bool = true;
/*      */           }
/*      */         } else {
/*      */           
/* 1886 */           String str = "select t.id,t.fieldname,t.fieldhtmltype,t.viewtype,t.billid,t.detailtable,t.type,t.fielddbtype from workflow_billfield t where id =" + paramInt1;
/* 1887 */           RecordSet recordSet = new RecordSet();
/* 1888 */           recordSet.execute(str);
/* 1889 */           if (recordSet.next()) {
/*      */             
/* 1891 */             int i = Util.getIntValue(recordSet.getString("viewtype"));
/* 1892 */             String str1 = Util.null2String(recordSet.getString("billid"));
/*      */             
/* 1894 */             String str2 = "";
/* 1895 */             if (i == 0) {
/* 1896 */               String str7 = "select tablename from workflow_bill where id=" + str1;
/* 1897 */               RecordSet recordSet1 = new RecordSet();
/* 1898 */               recordSet1.execute(str7);
/* 1899 */               if (recordSet1.next()) str2 = Util.null2String(recordSet1.getString("tablename")); 
/*      */             } else {
/* 1901 */               str2 = Util.null2String(recordSet.getString("detailtable"));
/*      */             } 
/* 1903 */             String str3 = recordSet.getString("fieldname");
/* 1904 */             String str4 = recordSet.getString("fieldhtmltype");
/* 1905 */             String str5 = recordSet.getString("type");
/* 1906 */             String str6 = recordSet.getString("fielddbtype");
/*      */             
/* 1908 */             bool = isFormModeFieldUsed(str2, str3, str4, str5, str6);
/*      */           } 
/*      */         } 
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1916 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean hasData(int paramInt1, int paramInt2, boolean paramBoolean, int paramInt3, int paramInt4) {
/* 1929 */     boolean bool = false;
/*      */     
/* 1931 */     if (paramInt3 != -1) {
/*      */       
/* 1933 */       String str = "";
/*      */       
/* 1935 */       if (paramInt2 == 0) {
/*      */         
/* 1937 */         String str1 = "select distinct fieldid from workflow_formfield";
/* 1938 */         String str2 = "";
/* 1939 */         RecordSet recordSet = new RecordSet();
/* 1940 */         recordSet.executeQuery(str1, new Object[0]);
/* 1941 */         while (recordSet.next()) {
/* 1942 */           str2 = str2 + "," + recordSet.getString(1);
/*      */         }
/*      */         
/* 1945 */         String str3 = " ";
/* 1946 */         if (!paramBoolean) {
/* 1947 */           recordSet.executeQuery("select distinct fieldid from workflow_formfield where formid=14", new Object[0]);
/* 1948 */           while (recordSet.next()) {
/* 1949 */             str3 = str3 + "," + recordSet.getString(1);
/*      */           }
/* 1951 */           if (!Strings.isNullOrEmpty(str3)) {
/* 1952 */             str3 = str3.substring(str3.indexOf(",") + 1);
/*      */           }
/*      */         } 
/*      */         
/* 1956 */         str1 = "select fieldname from workflow_formdict where id=?";
/* 1957 */         if (paramBoolean) {
/* 1958 */           str1 = "select fieldname from workflow_formdictdetail where id=?";
/*      */         }
/* 1960 */         recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt3) });
/* 1961 */         if (recordSet.next()) {
/* 1962 */           str = recordSet.getString("fieldname");
/*      */         }
/* 1964 */         String str4 = paramInt3 + "+" + str + "+" + this.user.getLanguage() + "+" + str3 + "+" + str2 + "+" + (paramBoolean ? "detailfield" : "mainfield");
/*      */         try {
/* 1966 */           String str5 = (new FieldMainManager()).getCheckbox(str4);
/* 1967 */           bool = "false".equals(str5);
/* 1968 */         } catch (Exception exception) {
/* 1969 */           exception.printStackTrace();
/*      */         }
/*      */       
/*      */       }
/* 1973 */       else if (paramInt2 == 1) {
/* 1974 */         RecordSet recordSet = new RecordSet();
/* 1975 */         recordSet.executeQuery("select * from workflow_billfield where id = ?", new Object[] { Integer.valueOf(paramInt3) });
/* 1976 */         if (recordSet.next()) {
/* 1977 */           str = Util.null2String(recordSet.getString("fieldname"));
/* 1978 */           String str1 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1979 */           String str2 = Util.null2String(recordSet.getString("type"));
/* 1980 */           String str3 = paramBoolean ? "1" : "0";
/* 1981 */           String str4 = Util.null2String(recordSet.getString("detailtable"));
/* 1982 */           String str5 = str + "+" + str3 + "+" + str1 + "+ " + str4 + " +" + paramInt1 + "+" + str2;
/* 1983 */           String str6 = (new FormFieldTransMethod()).getCanCheckBox(str5);
/* 1984 */           bool = !"true".equals(str6);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1990 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isFormModeFieldUsed(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 1999 */     boolean bool = false;
/* 2000 */     RecordSet recordSet = new RecordSet();
/* 2001 */     recordSet.execute("select 1 from modeinfo where formid=" + this.formId);
/* 2002 */     if (recordSet.getCounts() > 0) {
/* 2003 */       bool = true;
/*      */     }
/*      */     
/* 2006 */     FormModeConfig formModeConfig = new FormModeConfig();
/* 2007 */     boolean bool1 = formModeConfig.isFieldNoValueCanEdit();
/*      */     
/* 2009 */     if ((bool && bool1) || !bool) {
/* 2010 */       RecordSet recordSet1 = new RecordSet();
/* 2011 */       String str = "";
/* 2012 */       if (recordSet1.getDBType().equals("oracle") && paramString3.equals("4") && paramString4.equals("1")) {
/*      */         
/* 2014 */         str = "select " + paramString2 + " from " + paramString1 + " group by " + paramString2;
/*      */       }
/* 2016 */       else if (paramString5.toUpperCase().indexOf("varchar(".toUpperCase()) > -1 || paramString5.toUpperCase().indexOf("char(".toUpperCase()) > -1) {
/* 2017 */         str = "select count(1) from " + paramString1 + " where " + paramString2 + " is not null and " + paramString2 + " !='' ";
/* 2018 */       } else if (paramString5.toUpperCase().indexOf("varchar2(".toUpperCase()) > -1 || paramString5.toUpperCase().indexOf("decimal(".toUpperCase()) > -1 || paramString5.toUpperCase().indexOf("INTEGER") > -1 || paramString5
/* 2019 */         .toUpperCase().indexOf("NUMBER(") > -1 || paramString5.toUpperCase().indexOf("int".toUpperCase()) > -1) {
/* 2020 */         str = "select count(1) from " + paramString1 + " where " + paramString2 + " is not null ";
/*      */       } else {
/*      */         
/* 2023 */         str = "select " + paramString2 + " from " + paramString1 + " group by " + paramString2;
/*      */       } 
/*      */       
/* 2026 */       recordSet1.execute(str);
/* 2027 */       if (recordSet1.next()) {
/* 2028 */         return !(recordSet1.getInt(1) == 0);
/*      */       }
/*      */     } 
/* 2031 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected boolean isFieldExistInDB(String paramString1, String paramString2) {
/* 2041 */     boolean bool = false;
/*      */     try {
/* 2043 */       if (this.isOracle) {
/* 2044 */         this.recordSetTrans.executeQuery("SELECT count(*) as count FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ? and column_name = ?", new Object[] { paramString1.toUpperCase(), paramString2.toUpperCase() });
/* 2045 */       } else if (this.isMySQL) {
/* 2046 */         this.recordSetTrans.executeQuery("select count(*) as count from information_schema.columns WHERE table_name = ? and column_name = ? and table_schema = database()", new Object[] { paramString1.toLowerCase(), paramString2.toLowerCase() });
/*      */       }
/* 2048 */       else if (this.isPostgresql) {
/* 2049 */         this.recordSetTrans.executeQuery("select count(*) as count from information_schema.columns WHERE table_name = ? and column_name = ? and table_schema = 'public'", new Object[] { paramString1.toLowerCase(), paramString2.toLowerCase() });
/*      */       } else {
/* 2051 */         this.recordSetTrans.executeQuery("select COUNT(*) as count from syscolumns WHERE id=object_id(?) and name= ?", new Object[] { paramString1.toLowerCase(), paramString2.toLowerCase() });
/*      */       } 
/* 2053 */       if (this.recordSetTrans.next() && this.recordSetTrans.getInt("count") > 0) {
/* 2054 */         bool = true;
/*      */       }
/* 2056 */     } catch (Exception exception) {
/* 2057 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 2060 */     if (this.isMySQL && !bool) {
/*      */       try {
/* 2062 */         bool = this.recordSetTrans.executeQuery("select " + paramString2 + " from " + paramString1 + " limit 1 ", new Object[0]);
/* 2063 */       } catch (Exception exception) {}
/*      */     }
/* 2065 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected void addFieldToTable(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) throws Exception {
/* 2079 */     (new BaseBean()).writeLog("glb--->recordSetTrans.getDBType()==" + this.recordSetTrans.getDBType());
/* 2080 */     (new BaseBean()).writeLog("glb--->fieldhtmltype==" + paramString1);
/* 2081 */     (new BaseBean()).writeLog("glb--->type==" + paramString2);
/* 2082 */     (new BaseBean()).writeLog("glb--->tableName==" + paramString3);
/* 2083 */     (new BaseBean()).writeLog("glb--->fieldname==" + paramString4);
/* 2084 */     (new BaseBean()).writeLog("glb--->fielddbtype==" + paramString5);
/* 2085 */     (new BaseBean()).writeLog("glb--->_fielddbtype==" + paramString6);
/* 2086 */     if ("oracle".equals(this.recordSetTrans.getDBType())) {
/* 2087 */       if ("text".equals(paramString6)) {
/* 2088 */         paramString6 = "clob";
/*      */       }
/* 2090 */       if ("text".equals(paramString5)) {
/* 2091 */         paramString5 = "clob";
/*      */       }
/*      */     } 
/* 2094 */     String str = "";
/* 2095 */     if (paramString1.equals("3") && (paramString2.equals("161") || paramString2.equals("162"))) {
/* 2096 */       str = "alter table " + paramString3 + " add " + paramString4 + " " + paramString6;
/*      */     }
/* 2098 */     else if (paramString1.equals("3") && (paramString2.equals("256") || paramString2.equals("257"))) {
/* 2099 */       str = "alter table " + paramString3 + " add " + paramString4 + " " + paramString6;
/*      */     }
/* 2101 */     else if (paramString1.equals("3") && (paramString2.equals("224") || paramString2.equals("225"))) {
/* 2102 */       str = "alter table " + paramString3 + " add " + paramString4 + " " + paramString6;
/*      */     }
/* 2104 */     else if (paramString1.equals("3") && (paramString2.equals("226") || paramString2.equals("227"))) {
/* 2105 */       str = "alter table " + paramString3 + " add " + paramString4 + " " + paramString6;
/*      */     }
/* 2107 */     else if (this.isMySQL && paramString5.indexOf("varchar") != -1) {
/*      */       
/*      */       try {
/* 2110 */         str = "alter table " + paramString3 + " add " + paramString4 + " " + paramString5;
/*      */       }
/* 2112 */       catch (Exception exception) {
/* 2113 */         str = "alter table " + paramString3 + " add " + paramString4 + " text ";
/*      */       } 
/*      */     } else {
/*      */       
/* 2117 */       str = "alter table " + paramString3 + " add " + paramString4 + " " + paramString5;
/*      */     } 
/*      */     
/* 2120 */     (new BaseBean()).writeLog("glb--->数据库增加列sql=" + str);
/* 2121 */     this.recordSetTrans.executeSql(str);
/*      */   }
/*      */ 
/*      */   
/*      */   protected void changeFieldName(String paramString1, String paramString2, String paramString3, String paramString4) throws Exception {
/* 2126 */     if (this.isSqlServer) {
/* 2127 */       this.recordSetTrans.executeSql("EXEC sp_rename '" + paramString1 + ".[" + paramString3 + "]','" + paramString2 + "','COLUMN'");
/*      */     }
/* 2129 */     else if (this.isOracle) {
/* 2130 */       this.recordSetTrans.executeSql("alter table " + paramString1 + " rename column " + paramString3 + " to " + paramString2);
/*      */     }
/* 2132 */     else if (this.isMySQL) {
/* 2133 */       this.recordSetTrans.executeSql("alter table " + paramString1 + " change " + paramString3 + " " + paramString2 + " " + paramString4);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void setDetailTableOrderId(int paramInt) {
/* 2139 */     this.detailTableOrderId = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<List> splitList(List paramList) {
/* 2148 */     ArrayList<List> arrayList = new ArrayList();
/* 2149 */     byte b1 = 10;
/* 2150 */     if (paramList == null || paramList.size() == 0) {
/* 2151 */       return arrayList;
/*      */     }
/* 2153 */     int i = paramList.size();
/* 2154 */     int j = (i + b1 - 1) / b1;
/* 2155 */     for (byte b2 = 0; b2 < j; b2++) {
/* 2156 */       List list = paramList.subList(b2 * b1, ((b2 + 1) * b1 > i) ? i : (b1 * (b2 + 1)));
/* 2157 */       arrayList.add(list);
/*      */     } 
/* 2159 */     return arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   private boolean isFormUsed(int paramInt) {
/* 2164 */     boolean bool = false;
/* 2165 */     RecordSet recordSet = new RecordSet();
/* 2166 */     recordSet.execute("select 1 from workflow_base where formid=" + paramInt);
/* 2167 */     if (recordSet.getCounts() > 0) {
/* 2168 */       bool = true;
/*      */     }
/* 2170 */     return bool;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/biz/FormFieldSetBiz.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */