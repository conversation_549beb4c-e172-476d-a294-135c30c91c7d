/*     */ package com.engine.workflow.cmd.formExport;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.poi.ss.usermodel.BorderStyle;
/*     */ import org.apache.poi.ss.usermodel.HorizontalAlignment;
/*     */ import org.apache.poi.ss.usermodel.Hyperlink;
/*     */ import org.apache.poi.ss.usermodel.VerticalAlignment;
/*     */ import org.apache.poi.ss.util.CellRangeAddress;
/*     */ import org.apache.poi.xssf.usermodel.XSSFCell;
/*     */ import org.apache.poi.xssf.usermodel.XSSFCellStyle;
/*     */ import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
/*     */ import org.apache.poi.xssf.usermodel.XSSFColor;
/*     */ import org.apache.poi.xssf.usermodel.XSSFFont;
/*     */ import org.apache.poi.xssf.usermodel.XSSFRow;
/*     */ import org.apache.poi.xssf.usermodel.XSSFSheet;
/*     */ import org.apache.poi.xssf.usermodel.XSSFWorkbook;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class GetFormExportCmd extends AbstractCommonCommand<Map<String, Object>> {
/*     */   public BizLogContext getLogContext() {
/*  28 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  33 */     String str1 = null;
/*     */     try {
/*  35 */       str1 = URLDecoder.decode(URLDecoder.decode((String)this.params.get("json"), "UTF-8"), "UTF-8");
/*  36 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {}
/*     */     
/*  38 */     RecordSet recordSet = new RecordSet();
/*  39 */     recordSet.executeQuery("select value from workflow_config where name = 'export_excel_with_separated_sheets'", new Object[0]);
/*  40 */     if (recordSet.next()) {
/*  41 */       this.isSeparateSheet = "1".equals(recordSet.getString(1));
/*     */     }
/*  43 */     this.jsonMap = JSON.parseObject(str1, new Feature[] { Feature.OrderedField });
/*  44 */     this.workbook = getTableInfo(this.jsonMap);
/*     */     
/*  46 */     String str2 = (String)this.params.get("requestid");
/*  47 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  48 */     hashMap.put("document", this.workbook);
/*     */     
/*  50 */     recordSet.executeQuery("select requestname from workflow_requestbase where requestid = ?", new Object[] { str2 });
/*  51 */     if (recordSet.next()) {
/*  52 */       hashMap.put("requestname", recordSet.getString("requestname"));
/*     */     }
/*  54 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*  58 */   private XSSFWorkbook workbook = new XSSFWorkbook();
/*  59 */   private CreationHelper createHelper = (CreationHelper)this.workbook.getCreationHelper();
/*     */   private boolean isSeparateSheet = true;
/*     */   private JSONObject jsonMap;
/*  62 */   private int tableId = 0;
/*  63 */   private int[] mainColWidth = new int[50];
/*     */   
/*     */   public GetFormExportCmd(Map<String, Object> paramMap, User paramUser) {
/*  66 */     this.params = paramMap;
/*  67 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private XSSFWorkbook getTableInfo(JSONObject paramJSONObject) {
/*  78 */     XSSFSheet xSSFSheet = null;
/*  79 */     if (!this.isSeparateSheet) {
/*  80 */       xSSFSheet = this.workbook.createSheet("" + SystemEnv.getHtmlLabelName(34130, ThreadVarLanguage.getLang()) + "");
/*     */     }
/*  82 */     ArrayList arrayList = new ArrayList(paramJSONObject.keySet());
/*  83 */     for (String str : arrayList) {
/*     */       JSONObject jSONObject;
/*  85 */       if ("maintable".equals(str)) {
/*  86 */         jSONObject = paramJSONObject.getJSONObject("maintable");
/*  87 */         if (this.isSeparateSheet) {
/*  88 */           xSSFSheet = this.workbook.createSheet("" + SystemEnv.getHtmlLabelName(21778, ThreadVarLanguage.getLang()) + "");
/*     */         }
/*     */       } else {
/*  91 */         this.tableId = Util.getIntValue(str.replace("detail_", ""), 1);
/*  92 */         jSONObject = paramJSONObject.getJSONObject(str);
/*  93 */         if (this.isSeparateSheet) {
/*  94 */           xSSFSheet = this.workbook.createSheet("" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "" + this.tableId);
/*     */         }
/*     */       } 
/*  97 */       xSSFSheet.setDisplayGridlines(false);
/*     */       
/*  99 */       ArrayList<String> arrayList1 = new ArrayList(jSONObject.keySet());
/* 100 */       int i = xSSFSheet.getLastRowNum();
/* 101 */       int j = arrayList1.size(); int k;
/* 102 */       for (k = arrayList1.size() - 1; k >= 0; k--) {
/* 103 */         if (arrayList1.get(k) != null && !"".equals(arrayList1.get(k))) {
/* 104 */           if (this.isSeparateSheet) {
/* 105 */             j = Util.getIntValue(arrayList1.get(k)); break;
/*     */           } 
/* 107 */           j = Util.getIntValue(arrayList1.get(k)) + i;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/* 112 */       for (k = 0; k <= j; k++) {
/* 113 */         if (jSONObject.containsKey("" + k)) {
/*     */           XSSFRow xSSFRow;
/*     */           
/* 116 */           JSONObject jSONObject1 = jSONObject.getJSONObject("" + k);
/*     */           
/* 118 */           if (this.isSeparateSheet) {
/* 119 */             xSSFRow = xSSFSheet.createRow(k);
/*     */           }
/* 121 */           else if (k > 0 && k <= xSSFSheet.getLastRowNum()) {
/* 122 */             xSSFRow = xSSFSheet.createRow(xSSFSheet.getLastRowNum() + 1);
/*     */           } else {
/* 124 */             xSSFRow = xSSFSheet.createRow(k);
/*     */           } 
/*     */           
/* 127 */           ArrayList<String> arrayList2 = new ArrayList(jSONObject1.keySet());
/* 128 */           int m = Util.getIntValue(arrayList2.get(arrayList2.size() - 1));
/* 129 */           for (byte b = 0; b <= m; b++) {
/* 130 */             if (jSONObject1.containsKey("" + b))
/*     */             {
/*     */               
/* 133 */               configureStyle(xSSFSheet, xSSFRow, jSONObject1, xSSFSheet.getLastRowNum(), b, true); } 
/*     */           } 
/*     */         } 
/* 136 */       }  if (!this.isSeparateSheet) {
/* 137 */         for (k = 0; k < this.mainColWidth.length; k++) {
/* 138 */           int m = this.mainColWidth[k];
/* 139 */           if (m != 0)
/*     */           {
/*     */             
/* 142 */             xSSFSheet.setColumnWidth(k, m);
/*     */           }
/*     */         } 
/*     */         break;
/*     */       } 
/*     */     } 
/* 148 */     return this.workbook;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setCellStyle(JSONArray paramJSONArray, XSSFCellStyle paramXSSFCellStyle) {
/* 160 */     for (byte b = 0; b < paramJSONArray.size(); b++) {
/* 161 */       String str2; JSONObject jSONObject = (JSONObject)paramJSONArray.get(b);
/* 162 */       String str1 = jSONObject.getString("kind");
/* 163 */       if (null == str1) {
/*     */         return;
/*     */       }
/* 166 */       switch (str1) {
/*     */         case "top":
/* 168 */           str2 = jSONObject.getString("style");
/* 169 */           paramXSSFCellStyle.setBorderTop(getBorderStyle(str2));
/* 170 */           setColor(jSONObject, paramXSSFCellStyle, "top");
/*     */           break;
/*     */         
/*     */         case "bottom":
/* 174 */           str2 = jSONObject.getString("style");
/* 175 */           paramXSSFCellStyle.setBorderBottom(getBorderStyle(str2));
/* 176 */           setColor(jSONObject, paramXSSFCellStyle, "bottom");
/*     */           break;
/*     */         
/*     */         case "left":
/* 180 */           str2 = jSONObject.getString("style");
/* 181 */           paramXSSFCellStyle.setBorderLeft(getBorderStyle(str2));
/* 182 */           setColor(jSONObject, paramXSSFCellStyle, "left");
/*     */           break;
/*     */         
/*     */         case "right":
/* 186 */           str2 = jSONObject.getString("style");
/* 187 */           paramXSSFCellStyle.setBorderRight(getBorderStyle(str2));
/* 188 */           setColor(jSONObject, paramXSSFCellStyle, "right");
/*     */           break;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private BorderStyle getBorderStyle(String paramString) {
/* 205 */     switch (paramString) {
/*     */       case "1":
/* 207 */         return BorderStyle.THIN;
/*     */       case "2":
/* 209 */         return BorderStyle.MEDIUM;
/*     */       case "3":
/* 211 */         return BorderStyle.DASHED;
/*     */       case "4":
/* 213 */         return BorderStyle.THICK;
/*     */       case "5":
/* 215 */         return BorderStyle.DOUBLE;
/*     */       case "6":
/* 217 */         return BorderStyle.DOTTED;
/*     */       case "7":
/* 219 */         return BorderStyle.MEDIUM_DASHED;
/*     */     } 
/* 221 */     return BorderStyle.THIN;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean setRegionStyle(XSSFSheet paramXSSFSheet, CellRangeAddress paramCellRangeAddress, XSSFCellStyle paramXSSFCellStyle, JSONObject paramJSONObject) {
/* 234 */     if (paramJSONObject.containsKey("eborder")) {
/* 235 */       for (int i = paramCellRangeAddress.getFirstRow(); i <= paramCellRangeAddress.getLastRow(); i++) {
/* 236 */         XSSFRow xSSFRow = paramXSSFSheet.getRow(i);
/* 237 */         if (xSSFRow == null) {
/* 238 */           xSSFRow = paramXSSFSheet.createRow(i);
/*     */         }
/* 240 */         for (int j = paramCellRangeAddress.getFirstColumn(); j <= paramCellRangeAddress.getLastColumn(); j++) {
/* 241 */           XSSFCell xSSFCell = xSSFRow.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
/* 242 */           JSONArray jSONArray = paramJSONObject.getJSONArray("eborder");
/* 243 */           setCellStyle(jSONArray, paramXSSFCellStyle);
/* 244 */           xSSFCell.setCellStyle((CellStyle)paramXSSFCellStyle);
/*     */         } 
/*     */       } 
/* 247 */       return true;
/*     */     } 
/* 249 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setColor(JSONObject paramJSONObject, XSSFCellStyle paramXSSFCellStyle, String paramString) {
/* 263 */     String str = paramJSONObject.getString("color");
/* 264 */     byte[] arrayOfByte = configureColorArray(str);
/* 265 */     if (arrayOfByte[0] != 0 || arrayOfByte[1] != 0 || arrayOfByte[2] != 0) {
/* 266 */       IndexedColorMap indexedColorMap = this.workbook.getStylesSource().getIndexedColors();
/* 267 */       XSSFColor xSSFColor = new XSSFColor(arrayOfByte, indexedColorMap);
/* 268 */       switch (paramString) {
/*     */         case "top":
/* 270 */           paramXSSFCellStyle.setTopBorderColor(xSSFColor);
/*     */           break;
/*     */         case "bottom":
/* 273 */           paramXSSFCellStyle.setBottomBorderColor(xSSFColor);
/*     */           break;
/*     */         case "right":
/* 276 */           paramXSSFCellStyle.setRightBorderColor(xSSFColor);
/*     */           break;
/*     */         case "left":
/* 279 */           paramXSSFCellStyle.setLeftBorderColor(xSSFColor);
/*     */           break;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void separateProcessingDetails(XSSFSheet paramXSSFSheet) {
/* 294 */     JSONObject jSONObject = this.jsonMap.getJSONObject("detail_" + this.tableId);
/* 295 */     if (jSONObject == null) {
/*     */       return;
/*     */     }
/* 298 */     ArrayList<String> arrayList = new ArrayList(jSONObject.keySet());
/* 299 */     int i = paramXSSFSheet.getLastRowNum() + 1;
/* 300 */     int j = arrayList.size(); int k;
/* 301 */     for (k = arrayList.size() - 1; k >= 0; k--) {
/* 302 */       if (arrayList.get(k) != null && !"".equals(arrayList.get(k))) {
/* 303 */         j = Util.getIntValue(arrayList.get(k)) + i;
/*     */         break;
/*     */       } 
/*     */     } 
/* 307 */     for (k = 0; k <= j - i; k++) {
/* 308 */       if (jSONObject.containsKey("" + k)) {
/*     */ 
/*     */         
/* 311 */         XSSFRow xSSFRow = paramXSSFSheet.createRow(k + i);
/* 312 */         JSONObject jSONObject1 = jSONObject.getJSONObject("" + k);
/* 313 */         ArrayList<String> arrayList1 = new ArrayList(jSONObject1.keySet());
/* 314 */         int m = Util.getIntValue(arrayList1.get(arrayList1.size() - 1));
/* 315 */         for (byte b = 0; b <= m; b++) {
/* 316 */           if (jSONObject1.containsKey("" + b))
/*     */           {
/*     */             
/* 319 */             configureStyle(paramXSSFSheet, xSSFRow, jSONObject1, k, b, false);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void configureStyle(XSSFSheet paramXSSFSheet, XSSFRow paramXSSFRow, JSONObject paramJSONObject, int paramInt1, int paramInt2, boolean paramBoolean) {
/* 336 */     XSSFCellStyle xSSFCellStyle = this.workbook.createCellStyle();
/* 337 */     XSSFFont xSSFFont = this.workbook.createFont();
/* 338 */     xSSFCellStyle.setWrapText(true);
/* 339 */     JSONObject jSONObject = paramJSONObject.getJSONObject("" + paramInt2);
/* 340 */     XSSFCell xSSFCell = paramXSSFRow.createCell(paramInt2);
/*     */ 
/*     */     
/* 343 */     boolean bool = false;
/* 344 */     boolean bool1 = false;
/* 345 */     int i = jSONObject.getIntValue("rowspan");
/* 346 */     int j = jSONObject.getIntValue("colspan");
/* 347 */     boolean bool2 = (!this.isSeparateSheet && paramJSONObject.size() == 1 && jSONObject.containsKey("link")) ? true : false;
/* 348 */     if (!bool2 && (
/* 349 */       i > 1 || j > 1)) {
/* 350 */       CellRangeAddress cellRangeAddress = new CellRangeAddress(paramInt1, i + paramInt1 - 1, paramInt2, j + paramInt2 - 1);
/*     */       try {
/* 352 */         paramXSSFSheet.addMergedRegion(cellRangeAddress);
/* 353 */       } catch (Exception exception) {}
/*     */       
/* 355 */       bool = setRegionStyle(paramXSSFSheet, cellRangeAddress, xSSFCellStyle, jSONObject);
/* 356 */       bool1 = true;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 362 */     if (!bool1) {
/* 363 */       int k = Util.getIntValue(jSONObject.getString("width"), 120) * 30;
/* 364 */       if (paramBoolean && this.mainColWidth[paramInt2] == 0) {
/* 365 */         this.mainColWidth[paramInt2] = k;
/*     */       }
/* 367 */       paramXSSFSheet.setColumnWidth(paramInt2, k);
/*     */     } 
/* 369 */     float f = Util.getFloatValue(jSONObject.getString("height"), 30.0F);
/* 370 */     if (f > 80.0F) {
/* 371 */       paramXSSFRow.setHeightInPoints(f * 1.1F);
/*     */     } else {
/* 373 */       paramXSSFRow.setHeightInPoints(f * 0.8F);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 380 */     if (!bool && jSONObject.containsKey("eborder")) {
/* 381 */       setCellStyle(jSONObject.getJSONArray("eborder"), xSSFCellStyle);
/*     */     }
/*     */ 
/*     */     
/* 385 */     if (jSONObject.containsKey("backgroundColor")) {
/* 386 */       String str1 = jSONObject.getString("backgroundColor");
/* 387 */       byte[] arrayOfByte = configureColorArray(str1);
/* 388 */       if (arrayOfByte[0] != 0 || arrayOfByte[1] != 0 || arrayOfByte[2] != 0) {
/* 389 */         xSSFCellStyle.setFillForegroundColor(new XSSFColor(arrayOfByte, this.workbook.getStylesSource().getIndexedColors()));
/* 390 */         xSSFCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
/*     */       } 
/*     */     } 
/*     */     
/* 394 */     if (jSONObject.containsKey("font")) {
/* 395 */       JSONObject jSONObject1 = jSONObject.getJSONObject("font");
/*     */ 
/*     */       
/* 398 */       if (jSONObject1.containsKey("font-size")) {
/* 399 */         float f1 = Float.valueOf(jSONObject1.getString("font-size").replace("pt", "")).floatValue();
/* 400 */         xSSFFont.setFontHeight(f1);
/*     */       } else {
/* 402 */         xSSFFont.setFontHeight(9.0D);
/*     */       } 
/*     */ 
/*     */       
/* 406 */       if (jSONObject1.containsKey("text-align")) {
/* 407 */         switch (jSONObject1.getString("text-align")) {
/*     */           case "left":
/* 409 */             xSSFCellStyle.setAlignment(HorizontalAlignment.LEFT);
/*     */             break;
/*     */           case "center":
/* 412 */             xSSFCellStyle.setAlignment(HorizontalAlignment.CENTER);
/*     */             break;
/*     */           case "right":
/* 415 */             xSSFCellStyle.setAlignment(HorizontalAlignment.RIGHT);
/*     */             break;
/*     */           default:
/* 418 */             xSSFCellStyle.setAlignment(HorizontalAlignment.LEFT); break;
/*     */         } 
/*     */       } else {
/* 421 */         xSSFCellStyle.setAlignment(HorizontalAlignment.LEFT);
/*     */       } 
/*     */ 
/*     */       
/* 425 */       if (jSONObject1.containsKey("valign")) {
/* 426 */         String str1 = jSONObject1.getString("valign");
/* 427 */         if ("top".equals(str1)) {
/* 428 */           xSSFCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
/* 429 */         } else if ("middle".equals(str1)) {
/* 430 */           xSSFCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
/* 431 */         } else if ("bottom".equals(str1)) {
/* 432 */           xSSFCellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
/*     */         } 
/*     */       } else {
/* 435 */         xSSFCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
/*     */       } 
/*     */ 
/*     */       
/* 439 */       if (jSONObject1.containsKey("font-family")) {
/* 440 */         String str1 = jSONObject1.getString("font-family");
/* 441 */         if (str1.contains("Microsoft YaHei")) {
/* 442 */           xSSFFont.setFontName("微软雅黑");
/* 443 */         } else if (str1.contains("SimSun")) {
/* 444 */           xSSFFont.setFontName("宋体");
/* 445 */         } else if (str1.contains("SimHei")) {
/* 446 */           xSSFFont.setFontName("黑体");
/* 447 */         } else if (str1.contains("KaiTi")) {
/* 448 */           xSSFFont.setFontName("楷体");
/* 449 */         } else if (str1.contains("YouYuan")) {
/* 450 */           xSSFFont.setFontName("幼圆");
/* 451 */         } else if (str1.contains("FangSong")) {
/* 452 */           xSSFFont.setFontName("仿宋");
/* 453 */         } else if (str1.contains("仿宋_GB2312")) {
/* 454 */           xSSFFont.setFontName("仿宋_GB2312");
/* 455 */         } else if (str1.contains("方正黑体_GBK")) {
/* 456 */           xSSFFont.setFontName("方正黑体_GBK");
/* 457 */         } else if (str1.contains("方正大标宋简体")) {
/* 458 */           xSSFFont.setFontName("方正大标宋简体");
/* 459 */         } else if (str1.contains("方正小标宋简体")) {
/* 460 */           xSSFFont.setFontName("方正小标宋简体");
/*     */         } 
/*     */       } else {
/* 463 */         xSSFFont.setFontName("微软雅黑");
/*     */       } 
/*     */ 
/*     */       
/* 467 */       if (jSONObject1.containsKey("color")) {
/* 468 */         String str1 = jSONObject1.getString("color");
/* 469 */         byte[] arrayOfByte = configureColorArray(str1);
/* 470 */         if (arrayOfByte[0] != 0 || arrayOfByte[1] != 0 || arrayOfByte[2] != 0) {
/* 471 */           xSSFFont.setColor(new XSSFColor(arrayOfByte, this.workbook.getStylesSource().getIndexedColors()));
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 476 */       if (jSONObject1.containsKey("bold") && 
/* 477 */         jSONObject1.getBoolean("bold").booleanValue()) {
/* 478 */         xSSFFont.setBold(true);
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 483 */       if (jSONObject1.containsKey("italic") && 
/* 484 */         jSONObject1.getBoolean("italic").booleanValue()) {
/* 485 */         xSSFFont.setItalic(true);
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 490 */       if (jSONObject1.containsKey("deleteline") && 
/* 491 */         jSONObject1.getBoolean("deleteline").booleanValue()) {
/* 492 */         xSSFFont.setStrikeout(true);
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 497 */       if (jSONObject1.containsKey("underline") && 
/* 498 */         jSONObject1.getBoolean("underline").booleanValue()) {
/* 499 */         xSSFFont.setUnderline(FontUnderline.SINGLE);
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 506 */     String str = jSONObject.getString("text");
/* 507 */     if (str != null && !"".equals(str)) {
/* 508 */       str = str.replaceAll("\n", "br2n");
/* 509 */       str = str.replaceAll("最大+(\\d{1,})+M/个", "");
/* 510 */       if (!"".equals(str)) {
/*     */         try {
/* 512 */           if (str.contains("○") || str.contains("●") || str.contains("☐") || str.contains("☑")) {
/* 513 */             xSSFCell.setCellValue(str);
/*     */           } else {
/*     */             
/* 516 */             str = Jsoup.parse(str.replaceAll("(?i)<(br|p|li)[^>]*>", "br2n")).text().replaceAll("br2n", "\n");
/* 517 */             if (str.contains("base64")) {
/* 518 */               String[] arrayOfString = str.split("&&");
/* 519 */               for (String str1 : arrayOfString) {
/* 520 */                 configureImg(paramXSSFSheet, str1, paramInt1, paramInt2);
/*     */               }
/*     */             } else {
/* 523 */               xSSFCell.setCellValue(str);
/*     */             } 
/*     */           } 
/* 526 */         } catch (Exception exception) {
/* 527 */           writeLog(exception);
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 532 */     xSSFCellStyle.setFont((Font)xSSFFont);
/* 533 */     xSSFCellStyle.setIndention((short)1);
/* 534 */     xSSFCell.setCellStyle((CellStyle)xSSFCellStyle);
/*     */ 
/*     */     
/* 537 */     if (jSONObject.containsKey("link")) {
/* 538 */       String str1 = jSONObject.getString("link");
/* 539 */       if (this.isSeparateSheet) {
/* 540 */         Hyperlink hyperlink = this.createHelper.createHyperlink(HyperlinkType.DOCUMENT);
/* 541 */         if ("".equals(str1)) {
/* 542 */           str1 = "'" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "" + this.tableId + "'!A1";
/* 543 */           xSSFFont.setColor(new XSSFColor(new byte[] { 70, 88, -123 }, this.workbook.getStylesSource().getIndexedColors()));
/* 544 */           xSSFCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
/*     */         } else {
/* 546 */           xSSFCell.setCellValue(str);
/*     */         } 
/* 548 */         hyperlink.setAddress(str1);
/* 549 */         xSSFCell.setHyperlink(hyperlink);
/* 550 */         xSSFFont.setUnderline(FontUnderline.SINGLE);
/* 551 */       } else if (!"".equals(str1)) {
/* 552 */         if (paramJSONObject.size() == 1) {
/* 553 */           paramXSSFSheet.removeRow((Row)paramXSSFRow);
/* 554 */           paramXSSFSheet.createRow(paramInt1).setHeightInPoints(20.0F);
/*     */         } else {
/* 556 */           paramXSSFRow.removeCell((Cell)xSSFCell);
/*     */         } 
/* 558 */         this.tableId = Util.getIntValue(jSONObject.getString("text").replace("明细表", ""), 1);
/* 559 */         separateProcessingDetails(paramXSSFSheet);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] configureColorArray(String paramString) {
/* 572 */     if (paramString.length() != 7 || !paramString.matches("^#[0-9a-fA-F]{6}$")) {
/* 573 */       return new byte[] { 0, 0, 0 };
/*     */     }
/* 575 */     int[] arrayOfInt = new int[3];
/* 576 */     arrayOfInt[0] = Integer.valueOf(paramString.substring(1, 3), 16).intValue();
/* 577 */     arrayOfInt[1] = Integer.valueOf(paramString.substring(3, 5), 16).intValue();
/* 578 */     arrayOfInt[2] = Integer.valueOf(paramString.substring(5, 7), 16).intValue();
/* 579 */     byte[] arrayOfByte = new byte[3];
/* 580 */     arrayOfByte[0] = (byte)arrayOfInt[0];
/* 581 */     arrayOfByte[1] = (byte)arrayOfInt[1];
/* 582 */     arrayOfByte[2] = (byte)arrayOfInt[2];
/* 583 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void configureImg(XSSFSheet paramXSSFSheet, String paramString, int paramInt1, int paramInt2) {
/* 596 */     String str = "base64,";
/* 597 */     int i = paramString.indexOf(str) + str.length();
/* 598 */     byte[] arrayOfByte = Base64.decodeBase64(paramString.substring(i));
/* 599 */     int j = this.workbook.addPicture(arrayOfByte, 6);
/* 600 */     XSSFDrawing xSSFDrawing = paramXSSFSheet.createDrawingPatriarch();
/* 601 */     XSSFClientAnchor xSSFClientAnchor = new XSSFClientAnchor();
/* 602 */     xSSFClientAnchor.setCol1(paramInt2);
/* 603 */     xSSFClientAnchor.setCol2(paramInt2 + 1);
/* 604 */     xSSFClientAnchor.setRow1(paramInt1);
/* 605 */     xSSFClientAnchor.setRow2(paramInt1 + 1);
/* 606 */     double d1 = 100.0D;
/* 607 */     double d2 = 100.0D;
/* 608 */     double d3 = paramXSSFSheet.getColumnWidthInPixels(paramInt2);
/* 609 */     double d4 = (paramXSSFSheet.getRow(paramInt1).getHeightInPoints() / 72.0F * 96.0F);
/* 610 */     double d5 = d1 / d3;
/* 611 */     double d6 = d2 / d4;
/* 612 */     Picture picture = xSSFDrawing.createPicture((ClientAnchor)xSSFClientAnchor, j);
/* 613 */     picture.resize(d5, d6);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formExport/GetFormExportCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */