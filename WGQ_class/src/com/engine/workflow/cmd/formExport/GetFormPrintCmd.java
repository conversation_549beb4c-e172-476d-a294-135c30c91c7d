/*     */ package com.engine.workflow.cmd.formExport;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.alibaba.fastjson.parser.Feature;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Pattern;
/*     */ import org.jsoup.Jsoup;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class GetFormPrintCmd extends AbstractCommonCommand<Map<String, Object>> {
/*     */   private static final String A4 = "210mm 297mm";
/*  23 */   private StringBuffer pdfhtml = new StringBuffer();
/*  24 */   private float scale = 1.0F;
/*  25 */   private String marginLeft = "10mm";
/*  26 */   private String marginRight = "10mm";
/*  27 */   private String marginTop = "10mm";
/*  28 */   private String marginBottom = "10mm";
/*     */   
/*     */   public GetFormPrintCmd(Map<String, Object> paramMap, User paramUser) {
/*  31 */     this.params = paramMap;
/*  32 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  37 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     String str1 = null;
/*     */     try {
/*  44 */       str1 = URLDecoder.decode(URLDecoder.decode((String)this.params.get("json"), "UTF-8"), "UTF-8");
/*  45 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {}
/*     */     
/*  47 */     JSONObject jSONObject = JSON.parseObject(str1, new Feature[] { Feature.OrderedField });
/*  48 */     String str2 = (String)this.params.get("requestid");
/*  49 */     HashMap<Object, Object> hashMap = new HashMap<>(16);
/*  50 */     hashMap.put("pdfhtml", getMainTableInfo(jSONObject));
/*  51 */     RecordSet recordSet = new RecordSet();
/*  52 */     recordSet.executeQuery("select requestname from workflow_requestbase where requestid = ?", new Object[] { str2 });
/*  53 */     if (recordSet.next()) {
/*  54 */       hashMap.put("requestname", recordSet.getString("requestname"));
/*     */     }
/*  56 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getMainTableInfo(JSONObject paramJSONObject) {
/*  61 */     this.pdfhtml.append("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">")
/*  62 */       .append("<html>").append("<head><meta http-equiv=\"Content-Type\" content=\"text/html;charset=UTF-8\">")
/*  63 */       .append("<style type=\"text/css\">")
/*  64 */       .append("@page{size:210mm 297mm;")
/*  65 */       .append("margin-left:" + this.marginLeft + "; margin-right:" + this.marginRight + "; margin-top:" + this.marginTop + "; margin-bottom:" + this.marginBottom + ";} ")
/*  66 */       .append("body {font-family:Microsoft YaHei; font-size:12px}")
/*  67 */       .append("a:link {color:#123885;text-decoration:none;}")
/*  68 */       .append("</style></head>")
/*  69 */       .append("<body><table align=\"center\" width=100% style=\"border-collapse:collapse; word-wrap:break-word; word-break:break-word;page-break-inside:avoid;\">");
/*     */     
/*  71 */     getInfo(paramJSONObject, paramJSONObject.getJSONObject("maintable"));
/*  72 */     return Jsoup.parse(this.pdfhtml.toString()).html();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getDetailTableInfo(JSONObject paramJSONObject, String paramString) {
/*  83 */     int i = this.pdfhtml.lastIndexOf("<td ");
/*  84 */     int j = this.pdfhtml.lastIndexOf("background:#");
/*  85 */     if (i < j) {
/*  86 */       this.pdfhtml.replace(j, j + "background:#FFFFFF; ".length(), "");
/*     */     }
/*  88 */     j = this.pdfhtml.lastIndexOf("border-top:");
/*  89 */     if (i < j) {
/*  90 */       this.pdfhtml.replace(j, j + "border-top:1.0px solid #FFFFFF; ".length(), "");
/*     */     }
/*  92 */     j = this.pdfhtml.lastIndexOf("border-bottom:");
/*  93 */     if (i < j) {
/*  94 */       this.pdfhtml.replace(j, j + "border-bottom:1.0px solid #FFFFFF; ".length(), "");
/*     */     }
/*  96 */     j = this.pdfhtml.lastIndexOf("color:");
/*  97 */     if (i < j) {
/*  98 */       this.pdfhtml.replace(j, j + "color:#123885; ".length(), "");
/*     */     }
/* 100 */     this.pdfhtml.append("<table align=\"center\" width=100% style=\"border-collapse:collapse; word-wrap:break-word; word-break:break-word; page-break-inside:avoid; ")
/* 101 */       .append("margin-left:-1px; \">");
/* 102 */     getInfo((JSONObject)null, paramJSONObject.getJSONObject(paramString));
/* 103 */     this.pdfhtml.append("</table>");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getInfo(JSONObject paramJSONObject1, JSONObject paramJSONObject2) {
/* 114 */     ArrayList<String> arrayList = new ArrayList(paramJSONObject2.keySet());
/* 115 */     int i = arrayList.size(); int j;
/* 116 */     for (j = arrayList.size() - 1; j >= 0; j--) {
/* 117 */       if (arrayList.get(j) != null && !((String)arrayList.get(j)).equals("")) {
/* 118 */         i = Integer.valueOf(arrayList.get(j)).intValue();
/*     */         break;
/*     */       } 
/*     */     } 
/* 122 */     for (j = 0; j <= i; j++) {
/* 123 */       this.pdfhtml.append("<tr>");
/* 124 */       if (!paramJSONObject2.containsKey("" + j)) {
/* 125 */         this.pdfhtml.append("</tr>");
/*     */       } else {
/*     */         
/* 128 */         JSONObject jSONObject = paramJSONObject2.getJSONObject("" + j);
/* 129 */         if (jSONObject.size() != 0) {
/*     */ 
/*     */           
/* 132 */           ArrayList<String> arrayList1 = new ArrayList(jSONObject.keySet());
/* 133 */           int k = Integer.valueOf(arrayList1.get(arrayList1.size() - 1)).intValue();
/*     */           
/* 135 */           for (byte b = 0; b <= k; b++) {
/* 136 */             if (jSONObject.containsKey("" + b)) {
/*     */ 
/*     */               
/* 139 */               this.pdfhtml.append("<td ");
/*     */ 
/*     */               
/* 142 */               JSONObject jSONObject1 = jSONObject.getJSONObject("" + b);
/*     */ 
/*     */               
/* 145 */               int m = jSONObject1.getIntValue("rowspan");
/* 146 */               int n = jSONObject1.getIntValue("colspan");
/* 147 */               this.pdfhtml.append("colspan=" + ((n == 0) ? 1 : n) + " rowspan=" + ((m == 0) ? 1 : m) + " ");
/*     */               
/* 149 */               if (paramJSONObject1 != null) {
/* 150 */                 this.pdfhtml.append("class=\"mainTd_" + j + "_" + b + "\" ");
/*     */               } else {
/* 152 */                 this.pdfhtml.append("class=\"detailTd_" + j + "_" + b + "\" ");
/*     */               } 
/*     */               
/* 155 */               this.pdfhtml.append("style=\"");
/* 156 */               if (jSONObject1.containsKey("width")) {
/* 157 */                 if (jSONObject1.getString("width").contains("%")) {
/* 158 */                   this.pdfhtml.append("width:" + jSONObject1.getString("width") + "; ");
/*     */                 }
/* 160 */                 else if ("*".equals(jSONObject1.getString("width"))) {
/* 161 */                   this.pdfhtml.append("width:* ");
/*     */                 } else {
/* 163 */                   float f = Float.parseFloat(jSONObject1.getString("width"));
/* 164 */                   this.pdfhtml.append("width:" + getScaledSize(f) + "px; ");
/*     */                 } 
/*     */               }
/*     */               
/* 168 */               if (jSONObject1.containsKey("height")) {
/* 169 */                 float f = Float.parseFloat(jSONObject1.getString("height"));
/* 170 */                 this.pdfhtml.append("height:" + getScaledSize(f) + "px; ");
/*     */               } 
/*     */               
/* 173 */               this.pdfhtml.append("box-sizing:border-box; ");
/* 174 */               dealWithFont(jSONObject1);
/*     */ 
/*     */               
/* 177 */               if (jSONObject1.containsKey("border")) {
/* 178 */                 if (paramJSONObject1 == null) {
/* 179 */                   int i1 = this.pdfhtml.lastIndexOf("<table ");
/* 180 */                   int i2 = this.pdfhtml.substring(0, i1).lastIndexOf("<td ");
/* 181 */                   String str1 = this.pdfhtml.substring(i2, i1);
/* 182 */                   setCellStyle((b == 0 && str1.contains("border-left:")), (b == k && str1.contains("border-right:")), jSONObject1.getJSONObject("border"));
/*     */                 } else {
/* 184 */                   setCellStyle(false, false, jSONObject1.getJSONObject("border"));
/*     */                 } 
/*     */               }
/*     */ 
/*     */               
/* 189 */               if (jSONObject1.containsKey("padding")) {
/* 190 */                 JSONObject jSONObject2 = jSONObject1.getJSONObject("padding");
/* 191 */                 if (jSONObject2.containsKey("top")) {
/* 192 */                   this.pdfhtml.append("padding-top:" + jSONObject2.getString("top") + "; ");
/*     */                 }
/* 194 */                 if (jSONObject2.containsKey("left")) {
/* 195 */                   this.pdfhtml.append("padding-left:" + jSONObject2.getString("left") + "; ");
/*     */                 }
/* 197 */                 if (jSONObject2.containsKey("right")) {
/* 198 */                   this.pdfhtml.append("padding-right:" + jSONObject2.getString("right") + "; ");
/*     */                 }
/* 200 */                 if (jSONObject2.containsKey("bottom")) {
/* 201 */                   this.pdfhtml.append("padding-bottom:" + jSONObject2.getString("bottom") + "; ");
/*     */                 }
/*     */               } else {
/* 204 */                 this.pdfhtml.append("padding:0px; margin:0px; ");
/*     */               } 
/* 206 */               this.pdfhtml.append("\">");
/*     */ 
/*     */               
/* 209 */               String str = jSONObject1.getString("text");
/* 210 */               if (str != null && !"".equals(str)) {
/* 211 */                 str = str.replaceAll("\n", "br2n");
/* 212 */                 String str1 = "" + SystemEnv.getHtmlLabelName(83523, ThreadVarLanguage.getLang()) + "(\\d+)+M/" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "";
/* 213 */                 Pattern pattern = Pattern.compile(str1);
/* 214 */                 if (pattern.matcher(str).find()) {
/* 215 */                   str = str.replaceAll(str1, "");
/*     */                 }
/* 217 */                 if (str.contains("@HYPERLINK@")) {
/* 218 */                   String[] arrayOfString = str.split("@HYPERLINK@");
/* 219 */                   str = "<a href=" + arrayOfString[1] + " target=_blank>" + arrayOfString[0] + "</a>";
/*     */                 } 
/* 221 */                 if (str.matches("@DETAIL_\\d+@")) {
/* 222 */                   getDetailTableInfo(paramJSONObject1, str.replaceAll("@", "").toLowerCase());
/*     */                 } else {
/* 224 */                   str = Jsoup.parse(str.replaceAll("(?i)<(br|p|li)[^>][^style]*>", "br2n")).html();
/* 225 */                   if (jSONObject1.containsKey("mcpoint")) {
/* 226 */                     this.pdfhtml.append("<div><span style=\"vertical-align:middle; \">" + str.replaceAll("br2n", "<br/>") + "</span></div>");
/*     */                   } else {
/* 228 */                     this.pdfhtml.append("<div><span>" + str.replaceAll("br2n", "<br/></span></div>"));
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */               
/* 233 */               if (jSONObject1.containsKey("mcpoint")) {
/* 234 */                 analysisMc(jSONObject1);
/*     */               }
/*     */               
/* 237 */               this.pdfhtml.append("</td>");
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void analysisMc(JSONObject paramJSONObject) {
/* 250 */     for (Map map : paramJSONObject.getJSONArray("mcpoint")) {
/* 251 */       JSONObject jSONObject = new JSONObject(map);
/* 252 */       if (jSONObject.containsKey("field")) {
/*     */         continue;
/*     */       }
/* 255 */       if (jSONObject.containsKey("evalue")) {
/* 256 */         this.pdfhtml.append("<span style=\"display: inline-block; vertical-align:middle; ");
/* 257 */         dealWithFont(jSONObject);
/* 258 */         this.pdfhtml.append("\">");
/* 259 */         this.pdfhtml.append(jSONObject.getString("evalue").replaceAll("\\n", "<br/>"));
/*     */       } 
/* 261 */       if (jSONObject.containsKey("brsign") && "Y".equals(jSONObject.getString("brsign"))) {
/* 262 */         this.pdfhtml.append("<br/>");
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void dealWithFont(JSONObject paramJSONObject) {
/* 275 */     if (paramJSONObject.containsKey("backgroundColor")) {
/* 276 */       this.pdfhtml.append("background:" + paramJSONObject.getString("backgroundColor").toUpperCase() + "; ");
/*     */     }
/*     */     
/* 279 */     if (paramJSONObject.containsKey("font")) {
/* 280 */       JSONObject jSONObject = paramJSONObject.getJSONObject("font");
/*     */ 
/*     */       
/* 283 */       if (jSONObject.containsKey("font-size")) {
/* 284 */         float f = Float.valueOf(jSONObject.getString("font-size").replace("pt", "")).floatValue();
/* 285 */         f = f * 4.0F / 3.0F;
/* 286 */         this.pdfhtml.append("font-size:" + Math.round(getScaledSize(f)) + "px; ");
/*     */       } 
/*     */ 
/*     */       
/* 290 */       if (jSONObject.containsKey("text-align")) {
/* 291 */         this.pdfhtml.append("text-align:" + jSONObject.getString("text-align") + "; ");
/*     */       }
/*     */ 
/*     */       
/* 295 */       if (jSONObject.containsKey("vertical-align")) {
/* 296 */         this.pdfhtml.append("vertical-align:" + jSONObject.getString("vertical-align") + "; ");
/*     */       }
/*     */ 
/*     */       
/* 300 */       if (jSONObject.containsKey("font-family")) {
/* 301 */         String str = jSONObject.getString("font-family");
/* 302 */         if (str.contains("Microsoft YaHei")) {
/* 303 */           this.pdfhtml.append("font-family:Microsoft YaHei; ");
/* 304 */         } else if (str.contains("SimSun")) {
/* 305 */           this.pdfhtml.append("font-family:SimSun; ");
/* 306 */         } else if (str.contains("SimHei")) {
/* 307 */           this.pdfhtml.append("font-family:SimHei; ");
/* 308 */         } else if (str.contains("KaiTi")) {
/* 309 */           this.pdfhtml.append("font-family:KaiTi; ");
/* 310 */         } else if (str.contains("YouYuan")) {
/* 311 */           this.pdfhtml.append("font-family:YouYuan; ");
/* 312 */         } else if (str.contains("FangSong")) {
/* 313 */           this.pdfhtml.append("font-family:FangSong; ");
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 318 */       if (jSONObject.containsKey("color")) {
/* 319 */         this.pdfhtml.append("color:" + jSONObject.getString("color").toUpperCase() + "; ");
/*     */       }
/*     */ 
/*     */       
/* 323 */       if (jSONObject.containsKey("bold") && 
/* 324 */         jSONObject.getBoolean("bold").booleanValue()) {
/* 325 */         this.pdfhtml.append("font-weight:bold; ");
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 330 */       if (jSONObject.containsKey("italic") && 
/* 331 */         jSONObject.getBoolean("italic").booleanValue()) {
/* 332 */         this.pdfhtml.append("font-style:italic; ");
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 337 */       if (jSONObject.containsKey("deleteline")) {
/* 338 */         if (jSONObject.getBoolean("deleteline").booleanValue()) {
/* 339 */           this.pdfhtml.append("text-decoration:line-through; ");
/*     */         }
/* 341 */         this.pdfhtml.append("text-decoration:underline; ");
/*     */       } 
/*     */ 
/*     */       
/* 345 */       if (jSONObject.containsKey("underline") && 
/* 346 */         jSONObject.getBoolean("underline").booleanValue()) {
/* 347 */         this.pdfhtml.append("text-decoration:underline; ");
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setCellStyle(boolean paramBoolean1, boolean paramBoolean2, JSONObject paramJSONObject) {
/* 363 */     if (paramJSONObject.containsKey("top")) {
/* 364 */       String str1 = paramJSONObject.getJSONObject("top").getString("style");
/* 365 */       String str2 = paramJSONObject.getJSONObject("top").getString("color");
/* 366 */       this.pdfhtml.append("border-top:" + transStyle(str1) + str2.toUpperCase() + "; ");
/*     */     } 
/* 368 */     if (paramJSONObject.containsKey("bottom")) {
/* 369 */       String str1 = paramJSONObject.getJSONObject("bottom").getString("style");
/* 370 */       String str2 = paramJSONObject.getJSONObject("bottom").getString("color");
/* 371 */       this.pdfhtml.append("border-bottom:" + transStyle(str1) + str2.toUpperCase() + "; ");
/*     */     } 
/* 373 */     if (paramJSONObject.containsKey("left") && !paramBoolean1) {
/* 374 */       String str1 = paramJSONObject.getJSONObject("left").getString("style");
/* 375 */       String str2 = paramJSONObject.getJSONObject("left").getString("color");
/* 376 */       this.pdfhtml.append("border-left:" + transStyle(str1) + str2.toUpperCase() + "; ");
/*     */     } 
/* 378 */     if (paramJSONObject.containsKey("right") && !paramBoolean2) {
/* 379 */       String str1 = paramJSONObject.getJSONObject("right").getString("style");
/* 380 */       String str2 = paramJSONObject.getJSONObject("right").getString("color");
/* 381 */       this.pdfhtml.append("border-right:" + transStyle(str1) + str2.toUpperCase() + "; ");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String transStyle(String paramString) {
/* 394 */     if ("1".equals(paramString))
/* 395 */       return getScaledSize(1.0F) + "px solid "; 
/* 396 */     if ("2".equals(paramString))
/* 397 */       return getScaledSize(2.0F) + "px solid "; 
/* 398 */     if ("3".equals(paramString))
/* 399 */       return getScaledSize(1.0F) + "px dashed "; 
/* 400 */     if ("5".equals(paramString))
/* 401 */       return getScaledSize(3.0F) + "px solid "; 
/* 402 */     if ("6".equals(paramString))
/* 403 */       return getScaledSize(3.0F) + "px double "; 
/* 404 */     if ("7".equals(paramString))
/* 405 */       return getScaledSize(1.0F) + "px dotted "; 
/* 406 */     if ("8".equals(paramString)) {
/* 407 */       return getScaledSize(2.0F) + "px dashed ";
/*     */     }
/* 409 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private float getScaledSize(float paramFloat) {
/* 421 */     return this.scale * paramFloat;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formExport/GetFormPrintCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */