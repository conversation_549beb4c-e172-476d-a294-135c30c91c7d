/*    */ package com.engine.workflow.cmd.formExport;
/*    */ 
/*    */ import com.lowagie.text.BadElementException;
/*    */ import com.lowagie.text.Image;
/*    */ import com.lowagie.text.pdf.codec.Base64;
/*    */ import java.io.IOException;
/*    */ import org.w3c.dom.Element;
/*    */ import org.xhtmlrenderer.extend.FSImage;
/*    */ import org.xhtmlrenderer.extend.ReplacedElement;
/*    */ import org.xhtmlrenderer.extend.ReplacedElementFactory;
/*    */ import org.xhtmlrenderer.extend.UserAgentCallback;
/*    */ import org.xhtmlrenderer.layout.LayoutContext;
/*    */ import org.xhtmlrenderer.pdf.ITextFSImage;
/*    */ import org.xhtmlrenderer.pdf.ITextImageElement;
/*    */ import org.xhtmlrenderer.render.BlockBox;
/*    */ import org.xhtmlrenderer.simple.extend.FormSubmissionListener;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class B64ImgReplacedElementFactory
/*    */   implements ReplacedElementFactory
/*    */ {
/*    */   public ReplacedElement createReplacedElement(LayoutContext paramLayoutContext, BlockBox paramBlockBox, UserAgentCallback paramUserAgentCallback, int paramInt1, int paramInt2) {
/* 26 */     Element element = paramBlockBox.getElement();
/* 27 */     if (element == null) {
/* 28 */       return null;
/*    */     }
/* 30 */     String str = element.getNodeName();
/* 31 */     if (str.equals("img")) {
/* 32 */       FSImage fSImage; String str1 = element.getAttribute("src");
/*    */       
/*    */       try {
/* 35 */         fSImage = buildImage(str1, paramUserAgentCallback);
/* 36 */       } catch (BadElementException badElementException) {
/* 37 */         fSImage = null;
/* 38 */       } catch (IOException iOException) {
/* 39 */         fSImage = null;
/*    */       } 
/* 41 */       if (fSImage != null) {
/* 42 */         if (paramInt1 != -1 || paramInt2 != -1) {
/* 43 */           fSImage.scale(paramInt1, paramInt2);
/*    */         }
/* 45 */         return (ReplacedElement)new ITextImageElement(fSImage);
/*    */       } 
/*    */     } 
/* 48 */     return null;
/*    */   }
/*    */   
/*    */   protected FSImage buildImage(String paramString, UserAgentCallback paramUserAgentCallback) throws IOException, BadElementException {
/*    */     FSImage fSImage;
/* 53 */     if (paramString.startsWith("data:image/")) {
/* 54 */       String str = paramString.substring(paramString.indexOf("base64,") + "base64,".length(), paramString.length());
/* 55 */       byte[] arrayOfByte = Base64.decode(str);
/* 56 */       ITextFSImage iTextFSImage = new ITextFSImage(Image.getInstance(arrayOfByte));
/*    */     } else {
/* 58 */       fSImage = paramUserAgentCallback.getImageResource(paramString).getImage();
/*    */     } 
/* 60 */     return fSImage;
/*    */   }
/*    */   
/*    */   public void remove(Element paramElement) {}
/*    */   
/*    */   public void reset() {}
/*    */   
/*    */   public void setFormSubmissionListener(FormSubmissionListener paramFormSubmissionListener) {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formExport/B64ImgReplacedElementFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */