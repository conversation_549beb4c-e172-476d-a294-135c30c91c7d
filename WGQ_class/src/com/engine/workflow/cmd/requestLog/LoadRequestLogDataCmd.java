/*      */ package com.engine.workflow.cmd.requestLog;
/*      */ 
/*      */ import com.api.doc.detail.service.DocViewPermission;
/*      */ import com.api.doc.detail.util.DocDownloadCheckUtil;
/*      */ import com.api.odoc.util.OdocFileUtil;
/*      */ import com.api.workflow.service.RequestAuthenticationService;
/*      */ import com.api.workflow.util.ServiceUtil;
/*      */ import com.engine.common.biz.AbstractCommonCommand;
/*      */ import com.engine.common.entity.BizLogContext;
/*      */ import com.engine.common.util.AttrSignatureUtil;
/*      */ import com.engine.common.util.ServiceUtil;
/*      */ import com.engine.core.interceptor.CommandContext;
/*      */ import com.engine.hrm.biz.OrganizationShowSetBiz;
/*      */ import com.engine.workflow.biz.QysSignatureCominfo;
/*      */ import com.engine.workflow.biz.RequestLogBiz;
/*      */ import com.engine.workflow.biz.RobotNode.RobotNodeServiceBiz;
/*      */ import com.engine.workflow.biz.SecondAuthBiz;
/*      */ import com.engine.workflow.biz.WorkflowCommunicationBiz;
/*      */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*      */ import com.engine.workflow.biz.requestForm.RequestFormBiz;
/*      */ import com.engine.workflow.constant.SecondAuthType;
/*      */ import com.engine.workflow.constant.SignSource;
/*      */ import com.engine.workflow.constant.menu.SystemMenuType;
/*      */ import com.engine.workflow.entity.requestForm.RightMenu;
/*      */ import com.engine.workflow.service.RequestSecondAuthService;
/*      */ import com.engine.workflow.service.impl.RequestSecondAuthServiceImpl;
/*      */ import com.engine.workflow.util.MenuOrderSetUtil;
/*      */ import com.google.common.base.Strings;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.Map;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.dateformat.DateTransformer;
/*      */ import weaver.dateformat.UnifiedConversionInterface;
/*      */ import weaver.docs.category.SecCategoryComInfo;
/*      */ import weaver.docs.docs.DocImageManager;
/*      */ import weaver.docs.webservices.DocAttachment;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.request.RequestLogOperateName;
/*      */ import weaver.workflow.request.RequestRemarkRight;
/*      */ import weaver.workflow.request.WFLinkInfo;
/*      */ import weaver.workflow.request.WorkflowSpeechAppend;
/*      */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*      */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*      */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class LoadRequestLogDataCmd
/*      */   extends AbstractCommonCommand<Map<String, Object>>
/*      */ {
/*      */   private int requestid;
/*      */   private int workflowid;
/*      */   private int nodeid;
/*      */   private int desrequestid;
/*      */   private int isrequest;
/*      */   private boolean isprint;
/*      */   private boolean isworkflowhtmldoc;
/*      */   private String f_weaver_belongto_userid;
/*      */   private String f_weaver_belongto_usertype;
/*   74 */   private String source = "";
/*      */   
/*      */   private AttrSignatureUtil attrSignatureUtil;
/*      */   
/*      */   private String authSignatureInfo;
/*      */   
/*      */   private String authStr;
/*      */   private String authSignatureStr;
/*      */   private ResourceComInfo ResourceComInfo;
/*      */   private DepartmentComInfo DepartmentComInfo;
/*      */   private CustomerInfoComInfo CustomerInfoComInfo;
/*      */   private HttpServletRequest request;
/*      */   
/*      */   public LoadRequestLogDataCmd(HttpServletRequest paramHttpServletRequest, User paramUser, Map<String, Object> paramMap) {
/*   88 */     this.request = paramHttpServletRequest;
/*   89 */     this.user = paramUser;
/*   90 */     this.params = paramMap;
/*      */   }
/*      */   public LoadRequestLogDataCmd(HttpServletRequest paramHttpServletRequest, User paramUser, Map<String, Object> paramMap, String paramString) {
/*   93 */     this.request = paramHttpServletRequest;
/*   94 */     this.user = paramUser;
/*   95 */     this.params = paramMap;
/*   96 */     this.source = paramString;
/*      */   }
/*      */   
/*      */   public LoadRequestLogDataCmd(User paramUser, Map<String, Object> paramMap, String paramString) {
/*  100 */     this.user = paramUser;
/*  101 */     this.params = paramMap;
/*  102 */     this.source = paramString;
/*      */   }
/*      */ 
/*      */   
/*      */   public BizLogContext getLogContext() {
/*  107 */     return null;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  112 */     return executeTransfer();
/*      */   }
/*      */   public Map<String, Object> executeTransfer() {
/*      */     Map<String, Object> map;
/*  116 */     this.requestid = Util.getIntValue(Util.null2String(this.params.get("requestid")));
/*  117 */     this.f_weaver_belongto_userid = Util.null2String(this.params.get("f_weaver_belongto_userid"));
/*  118 */     this.f_weaver_belongto_usertype = Util.null2String(this.params.get("f_weaver_belongto_usertype"));
/*  119 */     this.attrSignatureUtil = new AttrSignatureUtil(this.user.getUID(), Util.null2String(this.params.get("request_header_user_agent")));
/*  120 */     String str1 = Util.null2String(this.params.get("signatureAttributesStr"));
/*  121 */     String str2 = Util.null2String(this.params.get("signatureSecretKey"));
/*  122 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  123 */     if ("".equals(this.source)) {
/*  124 */       this.attrSignatureUtil.verifySignature(str1, str2);
/*      */       
/*  126 */       RequestAuthenticationService requestAuthenticationService = new RequestAuthenticationService();
/*  127 */       requestAuthenticationService.setUser(this.user);
/*  128 */       if (!requestAuthenticationService.verify(this.request, this.requestid)) {
/*  129 */         (new BaseBean()).writeLog("被拦截：LoadRequestLogDataCmd；requestid：" + this.requestid);
/*  130 */         hashMap.put(Util.null2String(Integer.valueOf(this.requestid)), "被拦截：LoadRequestLogDataCmd");
/*  131 */         return (Map)hashMap;
/*      */       } 
/*  133 */       this.authStr = requestAuthenticationService.getAuthString();
/*  134 */       this.authSignatureStr = requestAuthenticationService.getAuthSignatureString();
/*  135 */       this.authSignatureInfo = "&authStr=" + requestAuthenticationService.getAuthString() + "&authSignatureStr=" + requestAuthenticationService.getAuthSignatureString();
/*      */     } else {
/*  137 */       this.authStr = Util.null2String(this.params.get("authStr"));
/*  138 */       this.authSignatureStr = Util.null2String(this.params.get("authSignatureStr"));
/*  139 */       this.authSignatureInfo = "&authStr=" + Util.null2String(this.params.get("authStr")) + "&authSignatureStr=" + Util.null2String(this.params.get("authSignatureStr"));
/*      */     } 
/*      */     
/*  142 */     this.workflowid = Util.getIntValue(Util.null2String(this.params.get("workflowid")), 0);
/*  143 */     this.nodeid = Util.getIntValue(Util.null2String(this.params.get("nodeid")), 0);
/*  144 */     this.isworkflowhtmldoc = "1".equals(Util.null2String(this.attrSignatureUtil.getAttribute("isworkflowhtmldoc" + this.requestid)));
/*  145 */     this.desrequestid = Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("desrequestid")));
/*  146 */     this.isrequest = Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("isrequest")));
/*  147 */     this.isprint = "1".equals(Util.null2String(this.params.get("isprint")));
/*      */     try {
/*  149 */       this.ResourceComInfo = new ResourceComInfo();
/*  150 */       this.DepartmentComInfo = new DepartmentComInfo();
/*  151 */       this.CustomerInfoComInfo = new CustomerInfoComInfo();
/*  152 */       map = getRequestLogData();
/*  153 */     } catch (Exception exception) {
/*  154 */       exception.printStackTrace();
/*  155 */       map.put("error", "1");
/*      */     } 
/*  157 */     return map;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getRequestLogData() throws Exception {
/*  161 */     RecordSet recordSet1 = new RecordSet();
/*  162 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  163 */     boolean bool1 = "1".equals(this.params.get("isFromWfForm"));
/*  164 */     long l = System.currentTimeMillis();
/*  165 */     int i = this.user.getUID();
/*  166 */     boolean bool2 = (i == 8 || i == 80 || i == 1215 || i == 1348 || i == 3724 || i == 4548) ? true : false;
/*  167 */     if (bool2) {
/*  168 */       System.out.println("requestlog-121-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  169 */       l = System.currentTimeMillis();
/*      */     } 
/*  171 */     l = System.currentTimeMillis();
/*  172 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  174 */     boolean bool3 = Util.null2String(this.params.get("loadmethod")).equals("split");
/*      */     
/*  176 */     if (bool2) {
/*  177 */       System.out.println("requestlog-122-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  178 */       l = System.currentTimeMillis();
/*      */     } 
/*  180 */     l = System.currentTimeMillis();
/*  181 */     boolean bool4 = Boolean.parseBoolean(Util.null2String(this.params.get("loadbyuser")));
/*  182 */     String str1 = Util.null2String(this.params.get("viewLogIds"));
/*  183 */     int j = Util.getIntValue(Util.null2String(this.params.get("creatorNodeId")));
/*  184 */     String str2 = Util.null2String(this.params.get("isHideInput"));
/*      */ 
/*      */     
/*  187 */     int k = Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("urger")));
/*      */     
/*  189 */     int m = Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("isintervenor")));
/*      */ 
/*      */     
/*  192 */     int n = Util.getIntValue(Util.null2String(this.params.get("forward")), 0);
/*  193 */     int i1 = Util.getIntValue(Util.null2String(this.params.get("submit")), 0);
/*      */ 
/*      */     
/*  196 */     String str3 = Util.null2String(this.params.get("isFormSignature"));
/*  197 */     boolean bool5 = "1".equals(Util.null2String(this.params.get("isFromMobile")));
/*      */     
/*  199 */     String str4 = Util.null2String(this.params.get("pgnumber"));
/*  200 */     String str5 = Util.null2String(this.params.get("maxrequestlogid"));
/*  201 */     int i2 = Util.getIntValue(Util.null2String(this.params.get("wfsignlddtcnt")), 0);
/*  202 */     String str6 = Util.null2String(this.params.get("orderbytype"));
/*  203 */     boolean bool6 = "true".equals(Util.null2String(this.params.get("isOldWf")));
/*  204 */     boolean bool7 = "1".equals(Util.null2String(Integer.valueOf(workflowAllComInfo.getShowSrc(String.valueOf(this.workflowid)))));
/*  205 */     boolean bool8 = false;
/*      */     
/*  207 */     recordSet1.executeQuery("select orderbytype, isOpenCommunication from workflow_base where id = ?", new Object[] { Integer.valueOf(this.workflowid) });
/*  208 */     if (recordSet1.next()) {
/*  209 */       str6 = Util.null2String(recordSet1.getString(1));
/*  210 */       bool8 = (recordSet1.getInt(2) == 1) ? true : false;
/*      */     } 
/*  212 */     if (bool4) {
/*  213 */       recordSet1.executeSql("SELECT nodeid FROM workflow_currentoperator WHERE requestid=" + this.requestid + " AND userid=" + this.user.getUID() + " ORDER BY receivedate desc,receivetime DESC");
/*  214 */       if (recordSet1.next()) {
/*  215 */         String str13 = recordSet1.getString("nodeid");
/*  216 */         recordSet1.executeSql("SELECT viewnodeids FROM workflow_flownode WHERE workflowid=" + this.workflowid + " AND nodeid=" + str13);
/*  217 */         String str14 = "-1";
/*  218 */         if (recordSet1.next()) {
/*  219 */           str14 = recordSet1.getString("viewnodeids");
/*      */         }
/*  221 */         if ("-1".equals(str14)) {
/*  222 */           recordSet1.executeSql("SELECT nodeid FROM workflow_flownode WHERE workflowid= " + this.workflowid + " AND EXISTS(SELECT 1 FROM workflow_nodebase WHERE id=workflow_flownode.nodeid AND (requestid IS NULL OR requestid=" + this.requestid + "))");
/*  223 */           while (recordSet1.next()) {
/*  224 */             str1 = str1 + "," + recordSet1.getString("nodeid");
/*      */           }
/*  226 */         } else if (str14 != null && !"".equals(str14)) {
/*      */           
/*  228 */           str1 = str1 + "," + str14;
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  233 */     int i3 = Util.getIntValue(str4);
/*  234 */     String str7 = "desc";
/*  235 */     if ("2".equals(str6)) {
/*  236 */       str7 = "asc";
/*      */     }
/*      */     
/*  239 */     WFLinkInfo wFLinkInfo = new WFLinkInfo();
/*  240 */     wFLinkInfo.setRequest(this.request);
/*  241 */     wFLinkInfo.setIsprint(this.isprint);
/*  242 */     ArrayList<Hashtable> arrayList = null;
/*      */ 
/*      */     
/*  245 */     String str8 = Util.null2String(this.params.get("sqlWhere"));
/*  246 */     if (!"".equals(str8)) writeLog("requestid:" + this.requestid + ",userid:" + i + ",sqlwhere:" + str8); 
/*  247 */     if ("".equals(this.source)) {
/*  248 */       str8 = str8 + wFLinkInfo.getRequestLogSearchConditionStr();
/*      */     }
/*      */     
/*  251 */     if (bool2) {
/*  252 */       System.out.println("requestlog-123-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  253 */       l = System.currentTimeMillis();
/*      */     } 
/*      */     
/*  256 */     RequestRemarkRight requestRemarkRight = new RequestRemarkRight();
/*  257 */     String str9 = requestRemarkRight.getRightCondition(this.requestid, this.workflowid, this.user.getUID());
/*  258 */     str9 = str9 + RequestLogBiz.getCustomSourceCondition(this.source, this.params);
/*  259 */     str8 = str8 + str9;
/*      */     
/*  261 */     if (bool2) {
/*  262 */       System.out.println("requestlog-124-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  263 */       l = System.currentTimeMillis();
/*      */     } 
/*      */     
/*  266 */     StringBuffer stringBuffer = new StringBuffer(str5);
/*      */     
/*  268 */     if (bool3);
/*      */ 
/*      */ 
/*      */     
/*  272 */     if (bool2) {
/*  273 */       System.out.println("requestlog-125-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  274 */       l = System.currentTimeMillis();
/*      */     } 
/*  276 */     if (str4 == null || str4.equals("")) {
/*  277 */       arrayList = wFLinkInfo.getRequestLog(this.requestid, this.workflowid, str1, str7, str9);
/*      */     } else {
/*      */       
/*  280 */       arrayList = RequestLogBiz.getRequestLog(this.requestid, this.workflowid, str1, str7, i2, i3, str8);
/*  281 */       if (RequestLogBiz.isEndLog(this.requestid, this.workflowid, str1, str7, i2, i3, str8)) {
/*  282 */         hashMap.put("isEnd", Boolean.valueOf(true));
/*      */       }
/*      */     } 
/*      */     
/*  286 */     hashMap.put("maxrequestlogid", stringBuffer.toString());
/*      */     
/*  288 */     if (bool2) {
/*  289 */       System.out.println("requestlog-126-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  290 */       l = System.currentTimeMillis();
/*      */     } 
/*      */     
/*  293 */     int i4 = 0;
/*  294 */     int i5 = 0;
/*      */     
/*  296 */     int i6 = this.user.getLanguage();
/*  297 */     RecordSet recordSet2 = new RecordSet();
/*  298 */     WorkflowRequestComInfo workflowRequestComInfo = new WorkflowRequestComInfo();
/*  299 */     DocImageManager docImageManager = new DocImageManager();
/*  300 */     SecCategoryComInfo secCategoryComInfo = new SecCategoryComInfo();
/*  301 */     RequestLogOperateName requestLogOperateName = new RequestLogOperateName();
/*  302 */     String str10 = "";
/*  303 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*      */ 
/*      */     
/*  306 */     String str11 = "";
/*  307 */     if (bool2) {
/*  308 */       System.out.println("requestlog-127-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  309 */       l = System.currentTimeMillis();
/*      */     } 
/*  311 */     l = System.currentTimeMillis();
/*      */ 
/*      */     
/*  314 */     Map map1 = SecondAuthBiz.getProtectedLogs(this.requestid);
/*  315 */     HashMap hashMap1 = (HashMap)map1.get("logIdMap");
/*  316 */     int i7 = Util.getIntValue(Util.null2String(map1.get("logId")), -1);
/*  317 */     map1.remove("logIdMap");
/*  318 */     RequestSecondAuthService requestSecondAuthService = (RequestSecondAuthService)ServiceUtil.getService(RequestSecondAuthServiceImpl.class, this.user);
/*      */     
/*  320 */     Map map2 = SecondAuthBiz.getMultiQYSSignInfo(this.requestid);
/*      */     
/*  322 */     DateTransformer dateTransformer = new DateTransformer();
/*  323 */     UnifiedConversionInterface unifiedConversionInterface = new UnifiedConversionInterface();
/*  324 */     QysSignatureCominfo qysSignatureCominfo = new QysSignatureCominfo();
/*      */     
/*  326 */     int i8 = FreeNodeBiz.getExtendNodeId(this.requestid, this.nodeid);
/*  327 */     Map<String, String> map = getOperateMenuName(i8, this.user.getLanguage());
/*  328 */     String str12 = map.get("forwardName");
/*      */     
/*  330 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  331 */       Hashtable hashtable = arrayList.get(b);
/*  332 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  333 */       int i9 = Util.getIntValue((String)hashtable.get("nodeid"), 0);
/*  334 */       int i10 = Util.getIntValue((String)hashtable.get("destnodeid"), 0);
/*  335 */       int i11 = Util.getIntValue((String)hashtable.get("nodeattribute"), 0);
/*  336 */       String str13 = Util.null2String((String)hashtable.get("nodename"));
/*  337 */       String str14 = RequestFormBiz.manageCssPollute(Util.null2String((String)hashtable.get("remark")));
/*  338 */       String str15 = Util.null2String((String)hashtable.get("operator"));
/*  339 */       String str16 = Util.null2String((String)hashtable.get("operatedate"));
/*  340 */       String str17 = Util.null2String((String)hashtable.get("operatetime"));
/*  341 */       String str18 = Util.null2String((String)hashtable.get("isRobotNode"));
/*  342 */       if (unifiedConversionInterface.getTimeZoneStatus()) {
/*      */         
/*  344 */         String[] arrayOfString = dateTransformer.getLocaleDateAndTime(str16, str17);
/*  345 */         str16 = arrayOfString[0];
/*  346 */         str17 = arrayOfString[1];
/*      */       } 
/*      */       
/*  349 */       String str19 = Util.null2String((String)hashtable.get("logtype"));
/*  350 */       String str20 = Util.null2String((String)hashtable.get("receivedPersons"));
/*  351 */       String str21 = Util.null2String((String)hashtable.get("receivedPersonids"));
/*  352 */       i4 = Util.getIntValue((String)hashtable.get("logid"), 0);
/*  353 */       String str22 = Util.null2String((String)hashtable.get("annexdocids"));
/*  354 */       String str23 = Util.null2String((String)hashtable.get("signdocids"));
/*  355 */       String str24 = Util.null2String((String)hashtable.get("signworkflowids"));
/*  356 */       String str25 = Util.null2String(hashtable.get("tmpLogId"));
/*  357 */       String str26 = Util.null2String(hashtable.get("remarkLocation"));
/*  358 */       String str27 = Util.null2String(hashtable.get("isMobile"));
/*      */       
/*  360 */       String str28 = Util.null2String((String)hashtable.get("remarkHtml"));
/*  361 */       SignSource signSource = "t".equals(str19) ? null : SignSource.getSignSource(str27);
/*  362 */       String str29 = "";
/*  363 */       if (signSource != null) {
/*  364 */         if (bool7 || signSource == SignSource.AUTO_APPROVE || signSource == SignSource.AUTO_SUBMIT) {
/*  365 */           str28 = str28 + SignSource.getSignSourceHtml(signSource, this.user.getLanguage());
/*      */         }
/*      */       } else {
/*  368 */         String str = str28;
/*  369 */         str28 = wFLinkInfo.removeRemarkSource2(str28);
/*  370 */         if (bool7) {
/*  371 */           str28 = str28 + wFLinkInfo.getRemarkSource2(str);
/*      */         } else {
/*  373 */           str28 = str28 + wFLinkInfo.getRemarkSource_WfAutoApprove(str28);
/*      */         } 
/*  375 */         str28 = str28.replaceAll("<span style='font-size:11px;color:#666;'>", "<span class='wf-requestlog-item-signsource' style='font-size:11px;color:#666;'>");
/*      */       } 
/*      */       
/*  378 */       hashMap2.put("forwardMenuName", str12);
/*      */       
/*  380 */       hashMap2.put("remarkLocation", str26);
/*      */       
/*  382 */       String str30 = RequestFormBiz.manageCssPollute(str28);
/*      */       
/*  384 */       String str31 = Util.null2String((String)hashtable.get("iframeId"));
/*      */       
/*  386 */       int i12 = Util.getIntValue(Util.null2String(hashtable.get("id")), 0);
/*      */       
/*  388 */       String str32 = Util.null2String((String)hashtable.get("operatortype"));
/*      */ 
/*      */ 
/*      */       
/*  392 */       hashMap2.put("isProtected", "0");
/*  393 */       hashMap2.put("checkSuccess", "-1");
/*  394 */       if (i12 > 0) {
/*  395 */         if (hashMap1.containsKey(String.valueOf(str25))) {
/*  396 */           int i13 = Util.getIntValue(Util.null2String((String)hashMap1.get(str25)), 0);
/*  397 */           hashMap2.put("isProtected", "1");
/*  398 */           hashMap2.put("protectType", Integer.valueOf(i13));
/*      */           
/*  400 */           if (i13 == SecondAuthType.QYS.getId()) {
/*      */ 
/*      */             
/*  403 */             String str = "";
/*  404 */             Map map3 = new HashMap<>();
/*  405 */             if (map2.containsKey(str25)) {
/*  406 */               Map map4 = (Map)map2.get(str25);
/*  407 */               if (map4 != null) {
/*  408 */                 str = Util.null2String(map4.get("signatureId"));
/*  409 */                 map3 = (Map)map4.get("certDetails");
/*      */               } 
/*      */             } 
/*  412 */             int i14 = Util.getIntValue(str, 0);
/*      */ 
/*      */             
/*  415 */             if (i14 <= 0) {
/*  416 */               str = qysSignatureCominfo.getSignatureid(str15);
/*  417 */               i14 = Util.getIntValue(str, 0);
/*      */             } 
/*  419 */             if (i14 > 0) {
/*  420 */               String str33 = DocDownloadCheckUtil.EncodeFileid(i14 + "", this.user);
/*  421 */               hashMap2.put("qys_signature_id", str33);
/*      */ 
/*      */ 
/*      */               
/*  425 */               int i15 = (int)Util.getDoubleValue(Util.null2String(map3.get("qyswidth")), 0.0D);
/*  426 */               int i16 = (int)Util.getDoubleValue(Util.null2String(map3.get("qysheight")), 0.0D);
/*  427 */               hashMap2.put("qyswidth", Integer.valueOf(i15));
/*  428 */               hashMap2.put("qysheight", Integer.valueOf(i16));
/*      */             } 
/*      */           } 
/*      */ 
/*      */           
/*  433 */           hashMap2.put("checkSuccess", Integer.valueOf(1));
/*  434 */           hashMap2.put("clickCheck", Integer.valueOf(0));
/*      */         } else {
/*      */           
/*  437 */           String str = "";
/*  438 */           Map map3 = new HashMap<>();
/*  439 */           if (map2.containsKey(str25)) {
/*  440 */             Map map4 = (Map)map2.get(str25);
/*  441 */             if (map4 != null) {
/*  442 */               str = Util.null2String(map4.get("signatureId"));
/*  443 */               map3 = (Map)map4.get("certDetails");
/*      */             } 
/*      */           } 
/*      */           
/*  447 */           int i13 = Util.getIntValue(str, 0);
/*  448 */           if (i13 > 0) {
/*  449 */             String str33 = DocDownloadCheckUtil.EncodeFileid(i13 + "", this.user);
/*  450 */             hashMap2.put("qys_signature_id", str33);
/*      */ 
/*      */             
/*  453 */             int i14 = (int)Util.getDoubleValue(Util.null2String(map3.get("qyswidth")), 0.0D);
/*  454 */             int i15 = (int)Util.getDoubleValue(Util.null2String(map3.get("qysheight")), 0.0D);
/*  455 */             hashMap2.put("qyswidth", Integer.valueOf(i14));
/*  456 */             hashMap2.put("qysheight", Integer.valueOf(i15));
/*      */           } 
/*      */         } 
/*      */         
/*  460 */         if (i12 == i7) {
/*  461 */           Map map3 = requestSecondAuthService.checkProtectDatas(map1);
/*  462 */           String str = Util.null2String(map3.get("success"));
/*  463 */           hashMap2.put("checkSuccess", str);
/*  464 */           hashMap2.put("clickCheck", Integer.valueOf(1));
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  469 */       if ((str4 != null && !str4.equals("")) || 
/*  470 */         arrayList.size() <= 10 || 
/*  471 */         b >= 10) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  477 */         i5 = 0;
/*  478 */         if (i4 > 0) {
/*  479 */           recordSet1.executeSql("select imageFileId from Workflow_FormSignRemark where requestLogId=" + i4);
/*  480 */           if (recordSet1.next()) {
/*  481 */             i5 = Util.getIntValue(recordSet1.getString("imageFileId"), 0);
/*      */           }
/*      */         } 
/*  484 */         hashMap2.put("logid", Util.null2String((String)hashtable.get("id")));
/*      */ 
/*      */         
/*  487 */         if (bool8) {
/*  488 */           hashMap2.put("hasCommunicationQuote", Boolean.valueOf(WorkflowCommunicationBiz.requestLogQuoteCheck(Util.getIntValue(Util.null2String(hashMap2.get("logid"))))));
/*      */         }
/*      */         
/*  491 */         String str33 = "0".equals(str32) ? this.ResourceComInfo.getMessagerUrls(str15) : "/messager/images/icon_m_wev8.jpg";
/*  492 */         str33 = "2".equals(str32) ? "/messager/images/robot.png" : str33;
/*      */         
/*  494 */         hashMap2.put("img_path", str33);
/*      */         
/*  496 */         int i13 = Util.getIntValue(recordSet1.getPropValue("WFSignatureImg", "showimg"), 0);
/*  497 */         int i14 = Util.getIntValue(recordSet1.getPropValue("WFSignatureImg", "imgheight"));
/*  498 */         recordSet1.execute("select * from DocSignature  where hrmresid=" + str15 + " and sealtype = 1 order by markid");
/*  499 */         String str34 = "";
/*      */         
/*  501 */         if (i13 == 1 && recordSet1.next() && "0".equals(str32)) {
/*  502 */           String str = Util.null2String(recordSet1.getString("markpath"));
/*  503 */           if (!str.equals("")) {
/*  504 */             OdocFileUtil odocFileUtil = new OdocFileUtil();
/*  505 */             String str37 = OdocFileUtil.changeParamToBase64Str(Util.getIntValue(str15, -1) + "");
/*  506 */             str34 = "/weaver/weaver.file.ImgFileDownload?userid=" + str37 + "&sealType=1";
/*      */           } 
/*      */         } 
/*  509 */         hashMap2.put("userimg", str34);
/*  510 */         hashMap2.put("signnaturImgHeight", Integer.valueOf(i14));
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  515 */         loadOperatorInfo(bool6, hashtable, j, recordSet2, (Map)hashMap2);
/*      */ 
/*      */         
/*  518 */         if (!str19.equals("t"))
/*      */         {
/*  520 */           if (i4 > 0 && i5 > 0) {
/*  521 */             str30 = str30 + "<img src=\"/weaver/weaver.file.FileDownload?fileid=" + i5 + "\"></img>";
/*      */           } else {
/*  523 */             Pattern pattern1 = Pattern.compile("(\\/workflow\\/request\\/ViewRequest(ForwardSPA)?\\.jsp\\?)");
/*  524 */             Matcher matcher1 = pattern1.matcher(str30);
/*  525 */             if (matcher1.find()) {
/*  526 */               str30 = matcher1.replaceAll("/workflow/request/ViewRequestForwardSPA.jsp?f_weaver_belongto_userid=" + this.f_weaver_belongto_userid + this.authSignatureInfo + "&desrequestid=" + this.requestid + "&");
/*      */             }
/*  528 */             Pattern pattern2 = Pattern.compile("(\\/docs\\/docs\\/DocDsp.jsp\\?)");
/*  529 */             Matcher matcher2 = pattern2.matcher(str30);
/*  530 */             if (matcher2.find()) {
/*  531 */               str30 = matcher2.replaceAll("/spa/document/index.jsp?f_weaver_belongto_userid=" + this.f_weaver_belongto_userid + this.authSignatureInfo + "&desrequestid=" + this.requestid + "&");
/*      */             }
/*      */             
/*  534 */             if (str30.indexOf("desrequestid=0") > -1)
/*  535 */               str30 = str30.replace("desrequestid=0", "desrequestid=" + this.desrequestid); 
/*  536 */             if (str30.indexOf("requestid=-1") > -1) {
/*  537 */               str30 = str30.replace("requestid=-1", "requestid=" + this.requestid);
/*      */             }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  567 */             if (!this.isprint && bool1) {
/*  568 */               str30 = RequestFormBiz.manageImgLazyLoad(str30);
/*      */             }
/*  570 */             if (this.isprint && str14.indexOf("<img") > -1) {
/*      */               
/*  572 */               String str37 = "";
/*  573 */               String str38 = "";
/*  574 */               String str39 = "";
/*  575 */               String str40 = str14;
/*  576 */               byte b2 = 0;
/*  577 */               while (str40.indexOf("<img") > -1) {
/*  578 */                 b2++;
/*  579 */                 int i15 = str40.indexOf("<img");
/*  580 */                 str37 = str40.substring(0, i15);
/*  581 */                 str38 = str38 + str37;
/*  582 */                 str40 = str40.substring(i15);
/*  583 */                 String str41 = "";
/*  584 */                 int i16 = str40.indexOf("/>");
/*  585 */                 str41 = str40.substring(0, i16);
/*  586 */                 str38 = str38 + "<div class=\"small_pic\">" + str41 + " onload=\"image_resize(this,'" + str31 + "');\" onresize=\"image_resize(this,'" + str31 + "');\" /></div><div id=\"pic_one" + b2 + "\" style=\"display:none;\">" + str41 + " class=\"maxImg\" /></div>";
/*      */                 
/*  588 */                 str40 = str40.substring(i16 + 2);
/*  589 */                 str39 = str40;
/*      */               } 
/*  591 */               str38 = str38 + str39;
/*  592 */               str14 = str38;
/*      */             } 
/*      */             
/*  595 */             String str = str14;
/*  596 */             str = Util.StringReplace(str, "&lt;br&gt;", "<br>");
/*  597 */             if (!"".equals(str) && this.isprint) {
/*  598 */               str = str + "<br>";
/*      */             }
/*  600 */             hashMap2.put("tempremark", str);
/*  601 */             hashMap2.put("pgflag", Util.null2String(str4));
/*      */           } 
/*      */         }
/*      */         
/*      */         try {
/*  606 */           String str = str30;
/*  607 */           if (str.indexOf("handwritten_xzl") > -1) {
/*  608 */             int i15 = str.indexOf("handwritten_xzl") + 21;
/*  609 */             String str37 = str.substring(i15);
/*  610 */             int i16 = str37.indexOf("src") - 2;
/*  611 */             String str38 = str37.substring(0, i16);
/*      */             
/*  613 */             String str39 = "";
/*  614 */             String str40 = DocDownloadCheckUtil.checkPermission(String.valueOf(str38), null);
/*  615 */             str39 = str39 + "<BR/><img class='handwritten_xzl' style='width:50%' name=\"handWrittenSign\" src=\"/weaver/weaver.file.FileDownload?fileid=" + str40 + "\" />";
/*      */             
/*  617 */             String str41 = "";
/*  618 */             String str42 = "";
/*  619 */             String str43 = "";
/*  620 */             String str44 = str;
/*  621 */             if (str44.indexOf("<img class=\"handwritten_xzl\"") > -1) {
/*  622 */               int i17 = str44.indexOf("<img class=\"handwritten_xzl\"");
/*  623 */               str41 = str44.substring(0, i17);
/*  624 */               str42 = str42 + str41;
/*  625 */               str44 = str44.substring(i17);
/*  626 */               int i18 = str44.indexOf("/>");
/*  627 */               str42 = str42 + str39;
/*  628 */               str44 = str44.substring(i18 + 2);
/*  629 */               str43 = str44;
/*      */             } 
/*  631 */             str42 = str42 + str43;
/*  632 */             str30 = str42;
/*      */           } 
/*  634 */         } catch (Exception exception) {
/*  635 */           exception.printStackTrace();
/*      */         } 
/*  637 */         hashMap2.put("log_remarkHtml", ServiceUtil.convertChar(str30));
/*      */         
/*  639 */         if (this.isrequest == -1) {
/*  640 */           this.isrequest = (Util.getIntValue(Util.null2String(this.params.get("isrequest"))) == -1) ? 1 : Util.getIntValue(Util.null2String(this.params.get("isrequest")));
/*      */         }
/*      */ 
/*      */         
/*  644 */         if (!str22.equals("") || !str23.equals("") || !str24.equals("")) {
/*      */           
/*  646 */           if (!str23.equals("")) {
/*  647 */             recordSet2.executeSql("select id,docsubject,accessorycount,SecCategory from docdetail where id in(" + str23 + ") order by id asc");
/*  648 */             ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  649 */             while (recordSet2.next()) {
/*  650 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  651 */               String str37 = Util.null2String(recordSet2.getString(1));
/*  652 */               String str38 = Util.toScreen(recordSet2.getString(2), i6);
/*      */               
/*  654 */               hashMap3.put("showid", str37);
/*  655 */               hashMap3.put("authStr", this.authStr);
/*  656 */               hashMap3.put("authSignatureStr", this.authSignatureStr);
/*  657 */               hashMap3.put("tempshowname", str38);
/*  658 */               hashMap3.put("filelink", "/spa/document/index.jsp?f_weaver_belongto_userid=" + this.user.getUID() + "&f_weaver_belongto_usertype=" + this.f_weaver_belongto_usertype + "&id=" + str37 + "&isrequest=" + this.isrequest + "&requestid=" + this.requestid + this.authSignatureInfo);
/*      */               
/*  660 */               arrayList2.add(hashMap3);
/*      */             } 
/*      */             
/*  663 */             hashMap2.put("signdocs", arrayList2);
/*      */           } 
/*      */           
/*  666 */           int i15 = Util.getIntValue(String.valueOf(this.attrSignatureUtil.getAttribute("slinkwfnum")));
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  671 */           if (!str24.equals("")) {
/*  672 */             ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  673 */             ArrayList<String> arrayList3 = Util.TokenizerString(str24, ",");
/*  674 */             for (byte b2 = 0; b2 < arrayList3.size(); b2++) {
/*  675 */               i15++;
/*      */               
/*  677 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  678 */               hashMap3.put("isrequest", "1");
/*  679 */               hashMap3.put("requestid", arrayList3.get(b2));
/*      */               
/*  681 */               hashMap3.put("desrequestid", Integer.valueOf(this.requestid));
/*  682 */               hashMap3.put("authSignatureInfo", this.authSignatureInfo);
/*  683 */               hashMap3.put("authStr", this.authStr);
/*  684 */               hashMap3.put("authSignatureStr", this.authSignatureStr);
/*  685 */               hashMap3.put("f_weaver_belongto_userid", this.f_weaver_belongto_userid);
/*  686 */               hashMap3.put("f_weaver_belongto_usertype", this.f_weaver_belongto_usertype);
/*  687 */               hashMap3.put("title", workflowRequestComInfo.getRequestName(arrayList3.get(b2)));
/*  688 */               arrayList2.add(hashMap3);
/*  689 */               str11 = str11 + (String)arrayList3.get(b2) + ",";
/*      */             } 
/*      */             
/*  692 */             hashMap2.put("signwfs", arrayList2);
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/*  697 */           this.attrSignatureUtil.setAttribute("haslinkworkflow", "1");
/*      */ 
/*      */           
/*  700 */           if (!str22.equals("")) {
/*  701 */             recordSet2.executeSql("select id,docsubject,accessorycount,SecCategory from docdetail where id in(" + str22 + ") order by id asc");
/*  702 */             ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  703 */             while (recordSet2.next()) {
/*      */               
/*  705 */               String str37 = Util.null2String(recordSet2.getString(1));
/*  706 */               String str38 = Util.toScreen(recordSet2.getString(2), i6);
/*  707 */               int i16 = recordSet2.getInt(3);
/*  708 */               String str39 = Util.null2String(recordSet2.getString(4));
/*  709 */               docImageManager.resetParameter();
/*  710 */               docImageManager.setDocid(Util.getIntValue(str37));
/*  711 */               docImageManager.selectDocImageInfo();
/*      */               
/*  713 */               String str40 = "";
/*  714 */               String str41 = "";
/*  715 */               String str42 = "";
/*  716 */               int i17 = 0;
/*  717 */               long l1 = 0L;
/*  718 */               if (docImageManager.next()) {
/*      */ 
/*      */                 
/*  721 */                 str40 = docImageManager.getImagefilename();
/*  722 */                 str41 = str40.substring(str40.lastIndexOf(".") + 1).toLowerCase();
/*  723 */                 str42 = docImageManager.getImagefileid();
/*  724 */                 l1 = docImageManager.getImageFileSize(Util.getIntValue(str42));
/*  725 */                 i17 = docImageManager.getVersionId();
/*      */               } 
/*  727 */               if (i16 > 1) {
/*  728 */                 str41 = "htm";
/*      */               }
/*      */               
/*  731 */               boolean bool9 = secCategoryComInfo.getNoDownload(str39).equals("1") ? true : false;
/*  732 */               String str43 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  738 */               str43 = "/spa/document/index.jsp?f_weaver_belongto_userid=" + this.user.getUID() + "&f_weaver_belongto_usertype=" + this.f_weaver_belongto_usertype + "&id=" + str37 + "&isrequest=" + this.isrequest + "&requestid=" + this.requestid + "&desrequestid=" + this.desrequestid + this.authSignatureInfo;
/*      */               
/*  740 */               String str44 = "";
/*  741 */               if (i16 == 1 && !this.isprint && ((!str41.equalsIgnoreCase("xls") && !str41.equalsIgnoreCase("doc")) || !bool9)) {
/*  742 */                 str44 = GCONST.getContextPath() + "/weaver/weaver.file.FileDownload?fileid=" + str42 + "&download=1&requestid=" + this.requestid + "&desrequestid=" + this.desrequestid + this.authSignatureInfo;
/*      */               }
/*  744 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  745 */               hashMap3.put("showid", str37);
/*  746 */               hashMap3.put("docImagefilename", str40);
/*  747 */               hashMap3.put("fileExtendName", str41);
/*  748 */               hashMap3.put("docImagefileid", str42);
/*  749 */               hashMap3.put("versionId", Integer.valueOf(i17));
/*  750 */               hashMap3.put("docImagefileSize", Long.valueOf(l1));
/*  751 */               hashMap3.put("nodownload", Boolean.valueOf(bool9));
/*  752 */               hashMap3.put("tempshowname", str38);
/*  753 */               hashMap3.put("filelink", str43);
/*  754 */               hashMap3.put("downloadlink", str44);
/*  755 */               hashMap3.put("authStr", this.authStr);
/*  756 */               hashMap3.put("authSignatureStr", this.authSignatureStr);
/*  757 */               boolean bool10 = true;
/*  758 */               if ("1".equals((new WorkflowConfigComInfo()).getValue("file_download_use_docright"))) {
/*  759 */                 DocViewPermission docViewPermission = new DocViewPermission();
/*  760 */                 bool10 = ((Boolean)docViewPermission.getShareLevel(Util.getIntValue(str37), this.user, false).get(DocViewPermission.DOWNLOAD)).booleanValue();
/*  761 */                 (new BaseBean()).writeLog("流程附件下载权限走知识组权限:docright：" + bool10 + ";showid:" + str37);
/*      */               } 
/*  763 */               hashMap3.put("showdownload", Boolean.valueOf((bool10 && !Strings.isNullOrEmpty(str44))));
/*  764 */               arrayList2.add(hashMap3);
/*      */             } 
/*      */             
/*  767 */             String[] arrayOfString1 = str22.split(",");
/*  768 */             ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/*  769 */             for (String str : arrayOfString1) {
/*  770 */               for (byte b2 = 0; b2 < arrayList2.size(); b2++) {
/*  771 */                 if (str.trim().equals(((Map)arrayList2.get(b2)).get("showid").toString().trim())) {
/*  772 */                   arrayList3.add(arrayList2.get(b2));
/*      */                   break;
/*      */                 } 
/*      */               } 
/*      */             } 
/*  777 */             if (arrayList2.size() != arrayList3.size()) {
/*  778 */               arrayList3 = arrayList2;
/*      */             }
/*  780 */             hashMap2.put("annexdocs", arrayList3);
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  785 */         byte b1 = bool5 ? 40 : 20;
/*  786 */         String[] arrayOfString = null;
/*      */         
/*  788 */         if ("3".equals(str19) || (1 == i11 && (str19.equals("0") || str19.equals("2") || str19.equals("3") || str19.equals("t")))) {
/*      */           
/*  790 */           String[] arrayOfString1 = wFLinkInfo.getForkStartReceivers(this.requestid, i4, i9, str16, str17, str19, i10);
/*  791 */           str20 = arrayOfString1[0];
/*      */         } 
/*  793 */         if (str20.length() > 0 && str20.trim().endsWith(",")) {
/*  794 */           str20 = str20.substring(0, str20.length() - 1);
/*      */         }
/*      */         
/*  797 */         arrayOfString = str20.split(",");
/*  798 */         str10 = "";
/*  799 */         if (arrayOfString.length > b1) {
/*  800 */           for (byte b2 = 0; b2 < b1; b2++) {
/*  801 */             str10 = str10 + "," + arrayOfString[b2];
/*      */           }
/*  803 */           if (str10.length() > 1) {
/*  804 */             str10 = str10.substring(1);
/*      */           }
/*      */         } else {
/*  807 */           str10 = str20;
/*      */         } 
/*      */         
/*  810 */         loadMobileInfo(hashtable, (Map)hashMap2);
/*      */ 
/*      */         
/*  813 */         hashMap2.put("receiveUser", ServiceUtil.convertChar(str10));
/*  814 */         hashMap2.put("receiveUserCount", Integer.valueOf(Util.splitString2List(str21, ",").size()));
/*      */ 
/*      */         
/*  817 */         hashMap2.put("log_operatedate", Util.toScreen(str16, i6));
/*  818 */         hashMap2.put("log_operatetime", Util.toScreen(str17, i6));
/*      */ 
/*      */ 
/*      */         
/*  822 */         hashMap2.put("log_nodename", Util.toScreen(str13, i6));
/*  823 */         String str35 = str19;
/*  824 */         String str36 = "";
/*  825 */         if ("1".equals(str18)) {
/*  826 */           str36 = Util.formatMultiLang((new RobotNodeServiceBiz()).getRobotNodeSet(Util.getIntValue(i9)).getOperateTypeName(), i6 + "");
/*      */         }
/*  828 */         if ("".equals(str36)) {
/*  829 */           str36 = requestLogOperateName.getOperateName("" + this.workflowid, "" + this.requestid, "" + i9, str35, str15, i6, str16, str17);
/*      */         }
/*      */         
/*  832 */         hashMap2.put("operationname", str36);
/*      */ 
/*      */         
/*  835 */         boolean bool = (!str2.equals("1") && (m == 1 || k == 1 || i1 == 1) && !str3.equals("1") && !str19.equals("t") && str30 != null && !"".equals(str30.trim())) ? true : false;
/*      */         
/*  837 */         hashMap2.put("isReference", Boolean.valueOf(bool));
/*      */         
/*  839 */         hashMap2.put("displaytype", str32);
/*      */ 
/*      */         
/*  842 */         hashMap2.put("forward", Integer.valueOf(n));
/*  843 */         hashMap2.put("logtype", str19);
/*      */         
/*  845 */         arrayList1.add(hashMap2);
/*      */       } 
/*  847 */     }  if (bool2) {
/*  848 */       System.out.println("requestlog-128-requestid-" + this.requestid + "-userid-" + i + "-" + (System.currentTimeMillis() - l));
/*  849 */       l = System.currentTimeMillis();
/*      */     } 
/*  851 */     hashMap.put("totalCount", Integer.valueOf(RequestLogBiz.getRequestLogTotalCount(this.requestid, this.workflowid, str1, str8)));
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  856 */     hashMap.put("loglist", arrayList1);
/*  857 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private void loadMobileInfo(Hashtable paramHashtable, Map<String, Object> paramMap) {
/*  863 */     String str1 = Util.null2String(paramHashtable.get("handwrittensignid"));
/*  864 */     (new BaseBean()).writeLog("handwrittensignid123:" + str1);
/*  865 */     ArrayList<DocAttachment> arrayList = new ArrayList();
/*  866 */     String[] arrayOfString = str1.split(",");
/*  867 */     if (!"".equals(str1)) {
/*  868 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  869 */         int j = Util.getIntValue(arrayOfString[b]);
/*  870 */         if (j > 0) {
/*  871 */           String str = WorkflowSpeechAppend.getAppend(j);
/*  872 */           paramMap.put("handWrittenSign", "data:image/png;base64," + str);
/*      */           
/*  874 */           DocAttachment docAttachment = WorkflowSpeechAppend.getAttachment(j);
/*  875 */           arrayList.add(docAttachment);
/*      */         } 
/*      */       } 
/*      */     }
/*  879 */     paramMap.put("handWrittenSignDoc", arrayList);
/*      */     
/*  881 */     int i = Util.getIntValue(Util.null2String(paramHashtable.get("speechattachmentid")));
/*  882 */     String str2 = Util.null2String(paramHashtable.get("speechAttachmente9"));
/*  883 */     if (i > 0) {
/*  884 */       if (!"".equals(str2)) {
/*  885 */         str2 = str2 + "," + i;
/*      */       } else {
/*  887 */         str2 = i + "";
/*      */       } 
/*      */     }
/*  890 */     ArrayList<String> arrayList1 = new ArrayList();
/*  891 */     if (!"".equals(str2)) {
/*  892 */       String[] arrayOfString1 = Util.splitString(str2, ",");
/*  893 */       for (String str : arrayOfString1) {
/*  894 */         int j = Util.getIntValue(str);
/*  895 */         if (j > 0) {
/*  896 */           DocAttachment docAttachment = WorkflowSpeechAppend.getAttachment(j);
/*  897 */           String str5 = docAttachment.getFiletype();
/*  898 */           if (str5.indexOf("/") >= 0) {
/*  899 */             str5 = str5.substring(str5.indexOf("/") + 1);
/*      */           }
/*  901 */           arrayList1.add("data:audio/" + str5 + ";base64," + WorkflowSpeechAppend.getAppend(j));
/*      */         } 
/*      */       } 
/*      */     } 
/*  905 */     paramMap.put("speechAttachmetnDatas", arrayList1);
/*      */ 
/*      */     
/*  908 */     String str3 = Util.null2String(paramMap.get("log_remarkHtml"));
/*  909 */     String str4 = WorkflowSpeechAppend.getElectrSignatrue(str3);
/*  910 */     if (str4 != null && !"".equals(str4)) {
/*  911 */       Pattern pattern = Pattern.compile("/weaver/weaver\\.file\\.SignatureDownLoad\\?markId=\\d+");
/*  912 */       Matcher matcher = pattern.matcher(str4);
/*  913 */       if (matcher.find()) {
/*  914 */         String str = matcher.group();
/*  915 */         paramMap.put("eletriSignature", str);
/*      */       } else {
/*  917 */         writeLog(String.format("The eletriSignature URL of '%1$s' is null.", new Object[] { str4 }));
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void loadOperatorInfo(boolean paramBoolean, Hashtable paramHashtable, int paramInt, RecordSet paramRecordSet, Map<String, Object> paramMap) {
/*  931 */     int i = this.user.getLanguage();
/*      */     
/*  933 */     boolean bool1 = false;
/*  934 */     boolean bool2 = true;
/*  935 */     String str1 = "";
/*  936 */     String str2 = "";
/*  937 */     String str3 = "";
/*  938 */     String str4 = "";
/*  939 */     String str5 = "";
/*  940 */     String str6 = "";
/*      */     
/*  942 */     String str7 = Util.null2String((String)paramHashtable.get("operatortype"));
/*  943 */     String str8 = Util.null2String((String)paramHashtable.get("operatorDept"));
/*  944 */     String str9 = Util.null2String((String)paramHashtable.get("operator"));
/*  945 */     String str10 = Util.null2String((String)paramHashtable.get("agenttype"));
/*  946 */     String str11 = Util.null2String((String)paramHashtable.get("agentorbyagentid"));
/*  947 */     int j = Util.getIntValue((String)paramHashtable.get("nodeid"), 0);
/*  948 */     if (str7.equals("0") && !"0".equals(Util.null2String(str8)) && !"".equals(Util.null2String(str8))) {
/*  949 */       str3 = Util.toScreen(str8, i);
/*  950 */       OrganizationShowSetBiz organizationShowSetBiz = new OrganizationShowSetBiz();
/*  951 */       str4 = Util.toScreen(organizationShowSetBiz.getDepartmentShow("workflow", str8), i);
/*      */     } 
/*      */     
/*  954 */     String str12 = (new RobotNodeServiceBiz()).getRobotNodeSet(j).getOperatorName();
/*  955 */     if (paramBoolean) {
/*  956 */       if (str7.equals("0")) {
/*  957 */         if (!this.isprint) {
/*  958 */           str1 = str9;
/*  959 */           str2 = Util.toScreen(this.ResourceComInfo.getLastname(str9), i);
/*      */         } else {
/*  961 */           str2 = Util.toScreen(this.ResourceComInfo.getLastname(str9), i);
/*      */         } 
/*  963 */       } else if (str7.equals("1")) {
/*  964 */         bool2 = false;
/*  965 */         if (!this.isprint) {
/*  966 */           str1 = str9;
/*  967 */           str2 = Util.toScreen(this.CustomerInfoComInfo.getCustomerInfoname(str9), i);
/*      */         } else {
/*  969 */           str2 = Util.toScreen(this.CustomerInfoComInfo.getCustomerInfoname(str9), i);
/*      */         } 
/*  971 */       } else if (str7.equals("2")) {
/*  972 */         if (!this.isprint) {
/*  973 */           str1 = str9;
/*  974 */           str2 = Util.toScreen(str12, i);
/*      */         } else {
/*  976 */           str2 = Util.toScreen(str12, i);
/*      */         } 
/*      */       } else {
/*  979 */         str2 = SystemEnv.getHtmlLabelName(468, i);
/*      */       }
/*      */     
/*  982 */     } else if (str7.equals("0")) {
/*  983 */       if (!this.isprint) {
/*  984 */         if (!str10.equals("2")) {
/*  985 */           str1 = str9;
/*  986 */           str2 = Util.toScreen(this.ResourceComInfo.getLastname(str9), i);
/*  987 */         } else if (str10.equals("2") || str10.equals("1")) {
/*  988 */           bool1 = true;
/*  989 */           str5 = str11;
/*  990 */           str6 = Util.toScreen(this.ResourceComInfo.getLastname(str11), i);
/*  991 */           str1 = str9;
/*  992 */           str2 = Util.toScreen(this.ResourceComInfo.getLastname(str9), i);
/*      */         }
/*      */       
/*      */       }
/*  996 */       else if (!str10.equals("2")) {
/*  997 */         str1 = str9;
/*  998 */         str2 = Util.toScreen(this.ResourceComInfo.getLastname(str9), i);
/*  999 */       } else if (str10.equals("2")) {
/* 1000 */         bool1 = true;
/* 1001 */         str1 = str9;
/* 1002 */         str6 = Util.toScreen(this.ResourceComInfo.getLastname(str11), i);
/* 1003 */         str2 = Util.toScreen(this.ResourceComInfo.getLastname(str9), i);
/*      */       }
/*      */     
/*      */     }
/* 1007 */     else if (str7.equals("1")) {
/* 1008 */       bool2 = false;
/* 1009 */       if (!this.isprint) {
/* 1010 */         str1 = str9;
/* 1011 */         str2 = Util.toScreen(this.CustomerInfoComInfo.getCustomerInfoname(str9), i);
/*      */       } else {
/* 1013 */         str2 = Util.toScreen(this.CustomerInfoComInfo.getCustomerInfoname(str9), i);
/*      */       } 
/* 1015 */     } else if (str7.equals("2")) {
/* 1016 */       if (!this.isprint) {
/* 1017 */         str1 = str9;
/* 1018 */         str2 = Util.toScreen(str12, i);
/*      */       } else {
/* 1020 */         str2 = Util.toScreen(str12, i);
/*      */       } 
/*      */     } else {
/* 1023 */       str2 = SystemEnv.getHtmlLabelName(468, i);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1028 */     paramMap.put("isexsAgent", Boolean.valueOf(bool1));
/* 1029 */     paramMap.put("log_agentorbyagentid", str11);
/* 1030 */     paramMap.put("displaybyagentname", str6);
/*      */ 
/*      */     
/* 1033 */     paramMap.put("isinneruser", Boolean.valueOf(bool2));
/* 1034 */     paramMap.put("displayid", str1);
/* 1035 */     paramMap.put("displayname", str2);
/*      */ 
/*      */     
/* 1038 */     paramMap.put("displaydepid", str3);
/* 1039 */     paramMap.put("displaydepname", str4);
/*      */   }
/*      */   
/*      */   public HttpServletRequest getRequest() {
/* 1043 */     return this.request;
/*      */   }
/*      */   
/*      */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/* 1047 */     this.request = paramHttpServletRequest;
/*      */   }
/*      */   
/*      */   public Map<String, String> getOperateMenuName(int paramInt1, int paramInt2) {
/* 1051 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/* 1053 */     RightMenu rightMenu = new RightMenu();
/* 1054 */     rightMenu.setSystemMenuType(SystemMenuType.FORWARD);
/* 1055 */     rightMenu.setSystemSmallType(2);
/* 1056 */     String str = MenuOrderSetUtil.getLogName(Util.getIntValue(this.workflowid), paramInt1, paramInt2, rightMenu);
/* 1057 */     hashMap.put("forwardName", str);
/* 1058 */     return (Map)hashMap;
/*      */   }
/*      */   
/*      */   public static void main(String[] paramArrayOfString) {
/* 1062 */     String str = "2020-10-11 11:22:0.00";
/* 1063 */     str = str.substring(0, 10);
/* 1064 */     System.out.println("value2222:" + str);
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/requestLog/LoadRequestLogDataCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */