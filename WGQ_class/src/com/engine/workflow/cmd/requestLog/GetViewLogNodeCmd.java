/*     */ package com.engine.workflow.cmd.requestLog;
/*     */ 
/*     */ import com.engine.common.util.AttrSignatureUtil;
/*     */ import com.engine.core.interceptor.AbstractCommand;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*     */ import com.engine.workflow.util.CollectionUtil;
/*     */ import com.google.common.collect.Maps;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.monitor.Monitor;
/*     */ import weaver.workflow.monitor.MonitorDTO;
/*     */ import weaver.workflow.report.ReportAuthorization;
/*     */ import weaver.workflow.request.WFShareAuthorization;
/*     */ import weaver.workflow.workflow.WFManager;
/*     */ import weaver.workflow.workflow.WFSubDataAggregation;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetViewLogNodeCmd
/*     */   extends AbstractCommand<Map<String, Object>>
/*     */ {
/*     */   private AttrSignatureUtil attrSignatureUtil;
/*     */   private int requestid;
/*     */   private int workflowid;
/*     */   
/*     */   public GetViewLogNodeCmd(User paramUser, Map<String, Object> paramMap) {
/*  34 */     this.user = paramUser;
/*  35 */     this.params = paramMap;
/*  36 */     this.attrSignatureUtil = new AttrSignatureUtil(paramUser.getUID(), Util.null2String(paramMap.get("request_header_user_agent")));
/*  37 */     String str1 = Util.null2String(paramMap.get("signatureAttributesStr"));
/*  38 */     String str2 = Util.null2String(paramMap.get("signatureSecretKey"));
/*  39 */     this.attrSignatureUtil.verifySignature(str1, str2);
/*  40 */     this.requestid = Util.getIntValue(Util.null2String(paramMap.get("requestid")));
/*  41 */     this.workflowid = Util.getIntValue(Util.null2String(paramMap.get("workflowid")), 0);
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  46 */     HashMap<String, String> hashMap = Maps.newHashMap();
/*     */     try {
/*  48 */       int i = Util.getIntValue(Util.null2String(this.params.get("requestid")));
/*  49 */       int j = Util.getIntValue(Util.null2String(this.params.get("workflowid")), 0);
/*  50 */       List<String> list = loadCanViewIds();
/*  51 */       if (list == null) list = new ArrayList<>(); 
/*  52 */       if (list.size() == 0) {
/*  53 */         list.add("-1");
/*     */       }
/*  55 */       hashMap.put("viewLogIds", CollectionUtil.list2String(list, ","));
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*     */     } 
/*  59 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private List<String> loadCanViewIds() throws Exception {
/*  63 */     int i = Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("desrequestid")));
/*  64 */     RecordSet recordSet1 = new RecordSet();
/*  65 */     String str1 = Util.null2String(this.attrSignatureUtil.getAttribute("reportid"));
/*  66 */     RecordSet recordSet2 = new RecordSet();
/*  67 */     ArrayList<String> arrayList = new ArrayList();
/*  68 */     String str2 = "-1";
/*  69 */     String str3 = "-1";
/*     */ 
/*     */     
/*  72 */     recordSet1.executeQuery("select nodeid from workflow_currentoperator where requestid=" + this.requestid + " and userid=" + this.user.getUID() + " order by receivedate desc ,receivetime desc", new Object[0]);
/*  73 */     if (recordSet1.next()) {
/*  74 */       int n = Util.getIntValue(this.attrSignatureUtil.getAttribute("nodeid"));
/*  75 */       int i1 = FreeNodeBiz.getExtendNodeId(this.requestid, n);
/*  76 */       loadNodeCanViewNodeIds(i1, this.workflowid, arrayList, this.requestid);
/*     */     } 
/*     */ 
/*     */     
/*  80 */     String str4 = Util.null2String(this.attrSignatureUtil.getAttribute("isfromreport"));
/*  81 */     ReportAuthorization reportAuthorization = new ReportAuthorization();
/*  82 */     if ("1".equals(str4) && this.requestid != 0) {
/*     */       try {
/*  84 */         if (reportAuthorization.checkUserReportPrivileges(str1, String.valueOf(this.requestid), this.user)) {
/*  85 */           recordSet1.executeSql("select nodeid from workflow_flownode where workflowid= " + this.workflowid + " and exists(select 1 from workflow_nodebase where id=workflow_flownode.nodeid and (requestid is null or requestid=" + this.requestid + "))");
/*  86 */           while (recordSet1.next()) {
/*  87 */             str2 = recordSet1.getString("nodeid");
/*  88 */             if (!arrayList.contains(str2)) {
/*  89 */               arrayList.add(str2);
/*     */             }
/*     */           } 
/*     */         } 
/*  93 */       } catch (Exception exception) {
/*  94 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/*  97 */     String str5 = Util.null2String(this.attrSignatureUtil.getAttribute("isfromflowreport"));
/*  98 */     if ("1".equals(str5) && this.requestid != 0 && 
/*  99 */       reportAuthorization.checkFlowReport(str1, String.valueOf(this.requestid), this.user)) {
/* 100 */       recordSet1.executeSql("select nodeid from workflow_flownode where workflowid= " + this.workflowid + " and exists(select 1 from workflow_nodebase where id=workflow_flownode.nodeid and (requestid is null or requestid=" + this.requestid + "))");
/* 101 */       while (recordSet1.next()) {
/* 102 */         str2 = recordSet1.getString("nodeid");
/* 103 */         if (!arrayList.contains(str2)) {
/* 104 */           arrayList.add(str2);
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 111 */     i = getDestRequestid(i);
/* 112 */     loadRelatedNodeIds(i, arrayList);
/*     */ 
/*     */     
/* 115 */     boolean bool = "true".equals(this.attrSignatureUtil.getAttribute("wfmonitor"));
/* 116 */     int j = Util.getIntValue(this.attrSignatureUtil.getAttribute("intervenorright"), 0);
/* 117 */     String str6 = Util.null2String(this.attrSignatureUtil.getAttribute("isurger"));
/* 118 */     if (str6.trim().equals("true") || bool || j > 0) {
/* 119 */       recordSet1.executeSql("select nodeid from workflow_flownode where workflowid= " + this.workflowid + " and exists(select 1 from workflow_nodebase where id=workflow_flownode.nodeid and (requestid is null or requestid=" + this.requestid + "))");
/* 120 */       while (recordSet1.next()) {
/* 121 */         str2 = recordSet1.getString("nodeid");
/* 122 */         if (!arrayList.contains(str2)) {
/* 123 */           arrayList.add(str2);
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 129 */     if (arrayList.size() == 0) {
/* 130 */       loadWfShareNodeIds(arrayList, i);
/*     */     }
/*     */ 
/*     */     
/* 134 */     if (arrayList.size() == 0) {
/* 135 */       loadEmShareNodeIds(arrayList);
/*     */     }
/*     */ 
/*     */     
/* 139 */     String str7 = Util.null2String(this.attrSignatureUtil.getAttribute("formmodeflag"));
/* 140 */     if (arrayList.size() == 0 && "1".equals(str7)) {
/* 141 */       loadNodeCanViewNodeIds(-1, this.workflowid, arrayList, this.requestid, true);
/*     */     }
/*     */ 
/*     */     
/* 145 */     int k = Util.getIntValue(this.attrSignatureUtil.getAttribute("iscowork"));
/* 146 */     if (arrayList.size() == 0 && k == 1) {
/* 147 */       loadNodeCanViewNodeIds(-1, this.workflowid, arrayList, this.requestid, true);
/*     */     }
/*     */ 
/*     */     
/* 151 */     int m = Util.getIntValue(this.attrSignatureUtil.getAttribute("isFromCommunication"));
/* 152 */     if (arrayList.size() == 0 && m == 1) {
/* 153 */       recordSet1.executeQuery("select nownodeid from workflow_nownode where requestid = ?", new Object[] { Integer.valueOf(this.requestid) });
/* 154 */       if (recordSet1.next()) {
/* 155 */         loadNodeCanViewNodeIds(recordSet1.getInt("nownodeid"), this.workflowid, arrayList, this.requestid, false);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 160 */     FreeNodeBiz.loadViewLogFreeNodeIds(this.requestid, arrayList);
/* 161 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadRelatedNodeIds(int paramInt, List<String> paramList) {
/*     */     try {
/* 170 */       if (paramInt > 0) {
/* 171 */         RecordSet recordSet1 = new RecordSet();
/* 172 */         RecordSet recordSet2 = new RecordSet();
/* 173 */         String str1 = "-1";
/* 174 */         String str2 = "-1";
/*     */         
/* 176 */         int i = Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("isrequest")));
/* 177 */         String str3 = WFSubDataAggregation.getAllSubRequestIds(paramInt);
/* 178 */         WFManager wFManager = new WFManager();
/* 179 */         recordSet1.executeQuery("select workflowid from workflow_requestbase where requestid = ?", new Object[] { Integer.valueOf(paramInt) });
/* 180 */         if (recordSet1.next()) {
/* 181 */           wFManager.setWfid(recordSet1.getInt("workflowid"));
/* 182 */           wFManager.getWfInfo();
/*     */         } 
/* 184 */         String str4 = wFManager.getIssignview();
/* 185 */         if ("1".equals(str4)) {
/* 186 */           recordSet1.executeSql("select  a.nodeid from  workflow_currentoperator a  where a.requestid=" + this.requestid + " and  exists (select 1 from workflow_currentoperator b where b.isremark in ('2','4') and b.requestid=" + paramInt + "  and  a.userid=b.userid) and userid=" + this.user.getUID() + " order by receivedate desc ,receivetime desc");
/*     */           
/* 188 */           if (recordSet1.next()) {
/* 189 */             loadNodeCanViewNodeIds(recordSet1.getInt("nodeid"), this.workflowid, paramList, paramInt);
/*     */           }
/*     */         } else {
/* 192 */           recordSet1.executeSql("select currentnodeid from workflow_requestbase where requestid=" + this.requestid);
/* 193 */           while (recordSet1.next()) {
/* 194 */             loadNodeCanViewNodeIds(recordSet1.getInt("currentnodeid"), this.workflowid, paramList, paramInt);
/*     */           }
/*     */           
/* 197 */           if (!"".equals(str3)) {
/*     */             
/* 199 */             recordSet1.executeSql("select a.currentnodeid from workflow_requestbase a where a.requestid=" + this.requestid + " and  exists (select 1 from workflow_currentoperator b where b.isremark in ('2','4') and b.requestid in(" + str3 + ")  and  a.creater=b.userid)");
/* 200 */             while (recordSet1.next()) {
/* 201 */               String str = recordSet1.getString("currentnodeid");
/* 202 */               recordSet2.executeSql("select viewnodeids from workflow_flownode where workflowid=" + this.workflowid + " and nodeid=" + str);
/* 203 */               if (recordSet2.next()) {
/* 204 */                 str1 = recordSet2.getString("viewnodeids");
/*     */               }
/*     */               
/* 207 */               if ("-1".equals(str1)) {
/* 208 */                 recordSet2.executeSql("select nodeid from workflow_flownode where workflowid= " + this.workflowid + " and exists(select 1 from workflow_nodebase where id=workflow_flownode.nodeid and (requestid is null or requestid in(" + str3 + ")))");
/* 209 */                 while (recordSet2.next()) {
/* 210 */                   str2 = recordSet2.getString("nodeid");
/* 211 */                   if (!paramList.contains(str2))
/* 212 */                     paramList.add(str2); 
/*     */                 }  continue;
/*     */               } 
/* 215 */               if (str1 == null || "".equals(str1)) {
/*     */                 continue;
/*     */               }
/* 218 */               String[] arrayOfString = Util.TokenizerString2(str1, ",");
/* 219 */               for (byte b = 0; b < arrayOfString.length; b++) {
/* 220 */                 if (!paramList.contains(arrayOfString[b])) {
/* 221 */                   paramList.add(arrayOfString[b]);
/*     */                 }
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 229 */         if ("1".equals(Util.null2String(this.attrSignatureUtil.getAttribute("isFromCommunication")))) {
/* 230 */           recordSet2.executeSql("select nodeid from workflow_flownode where workflowid= " + this.workflowid);
/* 231 */           while (recordSet2.next()) {
/* 232 */             str2 = recordSet2.getString("nodeid");
/* 233 */             if (!paramList.contains(str2)) {
/* 234 */               paramList.add(str2);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/* 239 */     } catch (Exception exception) {
/* 240 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadEmShareNodeIds(List<String> paramList) {
/* 250 */     if ("1".equals(this.attrSignatureUtil.getAttribute("isfromchatshare"))) {
/* 251 */       RecordSet recordSet = new RecordSet();
/* 252 */       recordSet.executeQuery("select allowViewEmShareLog from workflow_base where id = ?", new Object[] { Integer.valueOf(this.workflowid) });
/* 253 */       if (recordSet.next() && "1".equals(recordSet.getString(1))) {
/* 254 */         int i = Util.getIntValue(this.attrSignatureUtil.getAttribute("share"), -1);
/* 255 */         int j = Util.getIntValue(this.attrSignatureUtil.getAttribute("firstSharer"), -1);
/* 256 */         int k = Util.getIntValue(this.attrSignatureUtil.getAttribute("nodeid"), -1);
/* 257 */         loadNodeCanViewNodeIds(k, this.workflowid, paramList, this.requestid);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void loadWfShareNodeIds(List<String> paramList, int paramInt) {
/*     */     try {
/* 264 */       String str = Util.null2String(this.attrSignatureUtil.getAttribute("iswfshare"));
/*     */       
/* 266 */       if ("1".equals(str) && paramList.size() == 0) {
/* 267 */         RecordSet recordSet = new RecordSet();
/* 268 */         WFManager wFManager = new WFManager();
/* 269 */         String str1 = "";
/* 270 */         WFShareAuthorization wFShareAuthorization = new WFShareAuthorization();
/* 271 */         str1 = wFShareAuthorization.getSignByrstUser(String.valueOf(this.requestid), this.user);
/*     */ 
/*     */         
/* 274 */         if (!"".equals(str1)) {
/* 275 */           recordSet.executeSql("select workflowid from workflow_requestbase where requestid = " + this.requestid);
/* 276 */           if (recordSet.next()) {
/* 277 */             wFManager.setWfid(recordSet.getInt("workflowid"));
/* 278 */             wFManager.getWfInfo();
/*     */           } 
/* 280 */           String str2 = wFManager.getIssignview();
/* 281 */           if ("1".equals(str2)) {
/* 282 */             recordSet.executeSql("select  a.nodeid from  workflow_currentoperator a  where a.requestid=" + this.requestid + " and  exists (select 1 from workflow_currentoperator b where b.isremark in ('0','2','4') and b.requestid=" + this.requestid + "  and  a.userid=b.userid) and userid in (" + str1 + ") order by receivedate desc ,receivetime desc");
/*     */             
/* 284 */             if (recordSet.next()) {
/* 285 */               loadNodeCanViewNodeIds(recordSet.getInt("nodeid"), this.workflowid, paramList, this.requestid);
/*     */             }
/*     */           } else {
/* 288 */             recordSet.executeSql("select  distinct a.nodeid from  workflow_currentoperator a  where a.requestid=" + this.requestid + " and  exists (select 1 from workflow_currentoperator b where b.isremark in ('0','2','4') and b.requestid=" + this.requestid + "  and  a.userid=b.userid) and userid in (" + str1 + ") ");
/*     */             
/* 290 */             while (recordSet.next()) {
/* 291 */               loadNodeCanViewNodeIds(recordSet.getInt("nodeid"), this.workflowid, paramList, this.requestid);
/*     */             }
/*     */           } 
/* 294 */           if (paramList.size() == 0) {
/* 295 */             Monitor monitor = new Monitor();
/* 296 */             MonitorDTO monitorDTO = monitor.getMonitorInfo(str1, this.user.getUID() + "", this.workflowid + "");
/* 297 */             if (monitorDTO.getIsview()) {
/* 298 */               String str3 = "-1";
/* 299 */               recordSet.executeSql("select nodeid from workflow_flownode where workflowid= " + this.workflowid + " and exists(select 1 from workflow_nodebase where id=workflow_flownode.nodeid and (requestid is null or requestid=" + this.requestid + "))");
/* 300 */               while (recordSet.next()) {
/* 301 */                 str3 = recordSet.getString("nodeid");
/* 302 */                 if (!paramList.contains(str3)) {
/* 303 */                   paramList.add(str3);
/*     */                 }
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 310 */         if (paramInt != 0) {
/* 311 */           recordSet.executeSql("select workflowid from workflow_requestbase where requestid = " + paramInt);
/* 312 */           if (recordSet.next()) {
/* 313 */             wFManager.setWfid(recordSet.getInt("workflowid"));
/* 314 */             wFManager.getWfInfo();
/*     */           } 
/* 316 */           String str2 = wFManager.getIssignview();
/* 317 */           if ("1".equals(str2)) {
/* 318 */             recordSet.executeSql("select  a.nodeid from  workflow_currentoperator a  where a.requestid=" + this.requestid + " and  exists (select 1 from workflow_currentoperator b where b.isremark in ('0','2','4') and b.requestid=" + paramInt + "  and  a.userid=b.userid) and userid in (" + str1 + ") order by receivedate desc ,receivetime desc");
/*     */             
/* 320 */             if (recordSet.next()) {
/* 321 */               loadNodeCanViewNodeIds(recordSet.getInt("nodeid"), this.workflowid, paramList, paramInt);
/*     */             }
/*     */           } else {
/* 324 */             recordSet.executeSql("select  distinct a.nodeid from  workflow_currentoperator a  where a.requestid=" + this.requestid + " and  exists (select 1 from workflow_currentoperator b where b.isremark in ('0','2','4') and b.requestid=" + paramInt + "  and  a.userid=b.userid) and userid in (" + str1 + ")");
/*     */             
/* 326 */             while (recordSet.next()) {
/* 327 */               loadNodeCanViewNodeIds(recordSet.getInt("nodeid"), this.workflowid, paramList, paramInt);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/* 332 */     } catch (Exception exception) {
/* 333 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   public AttrSignatureUtil getAttrSignatureUtil() {
/* 338 */     return this.attrSignatureUtil;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getDestRequestid(int paramInt) {
/* 346 */     RecordSet recordSet = new RecordSet();
/* 347 */     int i = Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("isrequest")));
/* 348 */     if (i == 4) {
/* 349 */       int j = 0;
/* 350 */       String str = "";
/* 351 */       ArrayList arrayList = new ArrayList();
/* 352 */       recordSet.executeSql("select sub.subwfid,sub.isSame,sub.mainrequestid,req.requestname from workflow_subwfrequest sub left join workflow_requestbase req on req.requestid=sub.mainrequestid where sub.subrequestid=" + this.requestid);
/*     */ 
/*     */       
/* 355 */       if (recordSet.next() && 
/* 356 */         recordSet.getInt("mainrequestid") > -1) {
/* 357 */         str = Util.null2String(recordSet.getString("subwfid"));
/*     */         
/* 359 */         j = recordSet.getInt("mainrequestid");
/*     */       } 
/*     */ 
/*     */       
/* 363 */       recordSet.executeSql(" select sub.subrequestid requestid,req.requestname from workflow_subwfrequest sub left join workflow_requestbase req on req.requestid=sub.subrequestid where sub.mainrequestid=" + j + " and sub.subwfid=" + str + " and sub.subrequestid <> " + this.requestid);
/*     */ 
/*     */ 
/*     */       
/* 367 */       while (recordSet.next()) {
/* 368 */         paramInt = Util.getIntValue(recordSet.getString("requestid"), -1);
/*     */       }
/*     */     } 
/*     */     
/* 372 */     if (i == 3) {
/* 373 */       recordSet.executeSql("SELECT sub.mainrequestid requestid FROM workflow_subwfrequest sub LEFT OUTER JOIN workflow_requestbase req ON req.requestid=sub.subrequestid  where sub.subrequestid='" + this.requestid + "' ");
/*     */       
/* 375 */       if (recordSet.next()) {
/* 376 */         paramInt = recordSet.getInt("requestid");
/*     */       }
/*     */     } 
/* 379 */     if (i == 2) {
/* 380 */       recordSet.executeSql("SELECT sub.mainrequestid FROM workflow_subwfrequest sub LEFT OUTER JOIN workflow_requestbase req ON req.requestid=sub.mainrequestid  WHERE sub.subrequestid='" + this.requestid + "'");
/*     */       
/* 382 */       if (recordSet.next()) {
/* 383 */         paramInt = Util.getIntValue(recordSet.getString("mainrequestid"), -1);
/*     */       } else {
/*     */         
/* 386 */         recordSet.executeSql("SELECT mainrequestid FROM workflow_requestbase WHERE requestid='" + this.requestid + "'");
/* 387 */         if (recordSet.next()) {
/* 388 */           paramInt = Util.getIntValue(recordSet.getString("mainrequestid"), -1);
/*     */         }
/*     */       } 
/*     */     } 
/* 392 */     return paramInt;
/*     */   }
/*     */   
/*     */   private void loadNodeCanViewNodeIds(int paramInt1, int paramInt2, List<String> paramList, int paramInt3) {
/* 396 */     loadNodeCanViewNodeIds(paramInt1, paramInt2, paramList, paramInt3, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadNodeCanViewNodeIds(int paramInt1, int paramInt2, List<String> paramList, int paramInt3, boolean paramBoolean) {
/* 407 */     RecordSet recordSet = new RecordSet();
/* 408 */     String str = "";
/* 409 */     if (paramInt1 < 0) {
/* 410 */       paramInt1 = FreeNodeBiz.getExtendNodeId(paramInt1);
/*     */     }
/* 412 */     if (paramBoolean) {
/* 413 */       str = "-1";
/*     */     } else {
/* 415 */       recordSet.executeSql("select viewnodeids from workflow_flownode where workflowid=" + paramInt2 + " and nodeid=" + paramInt1);
/* 416 */       if (recordSet.next()) {
/* 417 */         str = recordSet.getString("viewnodeids");
/*     */       }
/*     */     } 
/* 420 */     if ("-1".equals(str)) {
/* 421 */       recordSet.executeSql("select nodeid from workflow_flownode where workflowid= " + paramInt2 + " and exists(select 1 from workflow_nodebase where id=workflow_flownode.nodeid and (requestid is null or requestid  = " + paramInt3 + "))");
/* 422 */       while (recordSet.next()) {
/* 423 */         String str1 = recordSet.getString("nodeid");
/* 424 */         if (!paramList.contains(str1)) {
/* 425 */           paramList.add(str1);
/*     */         }
/*     */       } 
/* 428 */     } else if (str != null && !"".equals(str)) {
/*     */       
/* 430 */       String[] arrayOfString = Util.TokenizerString2(str, ",");
/* 431 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 432 */         if (!paramList.contains(arrayOfString[b]))
/* 433 */           paramList.add(arrayOfString[b]); 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/requestLog/GetViewLogNodeCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */