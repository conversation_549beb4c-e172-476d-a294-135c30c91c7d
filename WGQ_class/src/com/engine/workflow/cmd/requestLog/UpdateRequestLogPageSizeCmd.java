/*    */ package com.engine.workflow.cmd.requestLog;
/*    */ 
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UpdateRequestLogPageSizeCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   public UpdateRequestLogPageSizeCmd(User paramUser, Map<String, Object> paramMap) {
/* 18 */     this.user = paramUser;
/* 19 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 24 */     RecordSet recordSet = new RecordSet();
/* 25 */     int i = this.user.getUID();
/* 26 */     int j = Util.getIntValue(Util.null2String(this.params.get("logpagesize")), 10);
/* 27 */     recordSet.executeSql("delete from ecology_pagesize where pageId='SIGNVIEW_VIEWID' and userid=" + i);
/* 28 */     recordSet.executeSql("insert into ecology_pagesize(pagesize,pageid,userid) values(" + j + ",'SIGNVIEW_VIEWID'," + i + ")");
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     hashMap.put("success", Boolean.valueOf(true));
/* 31 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/requestLog/UpdateRequestLogPageSizeCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */