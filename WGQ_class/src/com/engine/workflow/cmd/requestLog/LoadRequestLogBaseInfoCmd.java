/*     */ package com.engine.workflow.cmd.requestLog;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.util.AttrSignatureUtil;
/*     */ import com.engine.core.interceptor.AbstractCommand;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.SuperviseManagerBiz;
/*     */ import com.engine.workflow.biz.WorkflowCommunicationBiz;
/*     */ import com.engine.workflow.entity.requestLog.RequestLogGlobalEntity;
/*     */ import com.engine.workflow.entity.requestLog.SignRequestInfo;
/*     */ import com.engine.workflow.entity.requestLog.TriggerSetting;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WFManager;
/*     */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LoadRequestLogBaseInfoCmd
/*     */   extends AbstractCommand<Map<String, Object>>
/*     */ {
/*     */   private Map<String, Object> requestLogDatas;
/*     */   private RequestLogGlobalEntity globalEntity;
/*     */   private AttrSignatureUtil attrSignatureUtil;
/*     */   private int requestid;
/*     */   
/*     */   public LoadRequestLogBaseInfoCmd(User paramUser, Map<String, Object> paramMap) {
/*  51 */     this.user = paramUser;
/*  52 */     this.params = paramMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getRequestLogDatas() {
/*  56 */     return this.requestLogDatas;
/*     */   }
/*     */   
/*     */   public RequestLogGlobalEntity getGlobalEntity() {
/*  60 */     return this.globalEntity;
/*     */   }
/*     */   
/*     */   public AttrSignatureUtil getAttrSignatureUtil() {
/*  64 */     return this.attrSignatureUtil;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  69 */     this.globalEntity = new RequestLogGlobalEntity();
/*  70 */     this.requestLogDatas = new HashMap<>();
/*  71 */     this.attrSignatureUtil = new AttrSignatureUtil(this.user.getUID(), Util.null2String(this.params.get("request_header_user_agent")));
/*  72 */     String str1 = Util.null2String(this.params.get("signatureAttributesStr"));
/*  73 */     String str2 = Util.null2String(this.params.get("signatureSecretKey"));
/*  74 */     this.attrSignatureUtil.verifySignature(str1, str2);
/*  75 */     this.requestid = Util.getIntValue(Util.null2String(this.params.get("requestid")), -1);
/*     */     
/*  77 */     if (Util.getIntValue(this.attrSignatureUtil.getAttribute("requestid"), -1) != this.requestid) {
/*  78 */       (new BaseBean()).writeLog("被拦截：LoadRequestLogBaseInfoCmd；requestid：" + this.requestid);
/*  79 */       return this.requestLogDatas;
/*     */     } 
/*  81 */     this.globalEntity.setRequestid(Util.getIntValue(Util.null2String(this.params.get("requestid"))));
/*  82 */     this.globalEntity.setF_weaver_belongto_userid(Util.null2String(this.params.get("f_weaver_belongto_userid")));
/*  83 */     this.globalEntity.setF_weaver_belongto_usertype(Util.null2String(this.params.get("f_weaver_belongto_usertype")));
/*  84 */     this.globalEntity.setLoadmethod(Util.null2String(this.params.get("loadmethod")));
/*  85 */     initSignLoadMethod();
/*  86 */     this.requestid = Util.getIntValue(Util.null2String(this.params.get("requestid")), -1);
/*     */     
/*  88 */     this.globalEntity.setAuthSignatureInfo("&authStr=" + Util.null2String(this.params.get("authStr")) + "&authSignatureStr=" + Util.null2String(this.params.get("authSignatureStr")));
/*     */     
/*  90 */     this.globalEntity.setWorkflowid(Util.getIntValue(Util.null2String(this.params.get("workflowid")), 0));
/*  91 */     this.globalEntity.setNodeid(Util.getIntValue(Util.null2String(this.params.get("nodeid")), 0));
/*  92 */     this.globalEntity.setIsworkflowhtmldoc("1".equals(Util.null2String(this.attrSignatureUtil.getAttribute("isworkflowhtmldoc" + this.globalEntity.getRequestid()))));
/*  93 */     this.globalEntity.setDesrequestid(Util.getIntValue(Util.null2String(this.attrSignatureUtil.getAttribute("desrequestid"))));
/*  94 */     this.globalEntity.setIsprint("1".equals(Util.null2String(this.params.get("isprint"))));
/*  95 */     this.globalEntity.setIsFromCommunication(Util.null2String(this.attrSignatureUtil.getAttribute("isFromCommunication")));
/*     */     
/*  97 */     loadPortalBaseInfo();
/*     */     try {
/*  99 */       loadRequestLogInfo();
/* 100 */     } catch (Exception exception) {
/* 101 */       exception.printStackTrace();
/*     */     } 
/* 103 */     return this.requestLogDatas;
/*     */   }
/*     */ 
/*     */   
/*     */   public void loadRequestLogInfo() throws Exception {
/* 108 */     int i = this.user.getUID();
/* 109 */     long l = System.currentTimeMillis();
/* 110 */     boolean bool = (i == 8 || i == 80 || i == 1215 || i == 1348 || i == 3724 || i == 4548) ? true : false;
/*     */     
/* 112 */     String str1 = "0";
/* 113 */     String str2 = "";
/*     */     
/* 115 */     RecordSet recordSet = new RecordSet();
/* 116 */     recordSet.executeQuery("select ishidearea,ishideinput,ismode from workflow_flownode where workflowId= ? and nodeId= ? ", new Object[] { Integer.valueOf(this.globalEntity.getWorkflowid()), Integer.valueOf(this.globalEntity.getNodeid()) });
/* 117 */     if (recordSet.next()) {
/* 118 */       str1 = "" + Util.getIntValue(recordSet.getString("ishidearea"), 0);
/* 119 */       str2 = Util.null2String(recordSet.getString("ishideinput"));
/*     */     } 
/*     */     
/* 122 */     this.requestLogDatas.put("isHideInput", str2);
/* 123 */     this.requestLogDatas.put("isHideArea", str1);
/*     */     
/* 125 */     if ("0".equals(str1)) {
/*     */       
/* 127 */       int j = this.globalEntity.getRequestid();
/* 128 */       if (!"portal".equals(this.globalEntity.getLoadmethod())) {
/* 129 */         loadShowTabCondition(j);
/*     */       }
/*     */       
/* 132 */       String str = Util.null2s(Util.null2String(this.params.get("viewLogIds")), "-1");
/* 133 */       this.requestLogDatas.put("viewLogIds", str);
/* 134 */       loadWfRelatedParams();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 146 */       this.requestLogDatas.put("logCondition", getRequestLogSearchCondition(str));
/*     */     } 
/*     */   }
/*     */   
/*     */   private void initSignLoadMethod() {
/* 151 */     if (!"portal".equals(this.globalEntity.getLoadmethod())) {
/* 152 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 154 */       WorkflowConfigComInfo workflowConfigComInfo = new WorkflowConfigComInfo();
/* 155 */       String str1 = Util.null2String(workflowConfigComInfo.getValue("signInput_signListType"));
/* 156 */       recordSet.executeSql("select signlisttype from workflow_RequestUserDefault where userId = " + this.user.getUID());
/* 157 */       if (recordSet.next()) {
/* 158 */         str1 = Util.null2s(recordSet.getString("signlisttype"), str1);
/*     */       }
/* 160 */       String str2 = "0".equals(str1) ? "scroll" : "split";
/* 161 */       this.globalEntity.setLoadmethod(str2);
/* 162 */       this.requestLogDatas.put("loadmethod", str2);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void loadShowTabCondition(int paramInt) {
/* 167 */     RecordSet recordSet = new RecordSet();
/* 168 */     ArrayList<String> arrayList = new ArrayList();
/* 169 */     ArrayList<SignRequestInfo> arrayList1 = new ArrayList();
/* 170 */     int i = 0;
/* 171 */     String str1 = "0";
/* 172 */     String str2 = "";
/*     */     
/* 174 */     String str3 = "-1";
/* 175 */     boolean bool1 = false;
/* 176 */     boolean bool2 = false;
/* 177 */     boolean bool3 = false;
/*     */     
/* 179 */     boolean bool4 = false;
/* 180 */     boolean bool5 = false;
/* 181 */     boolean bool6 = false;
/*     */     
/* 183 */     String str4 = "0";
/* 184 */     String str5 = "";
/* 185 */     String str6 = "0";
/* 186 */     String str7 = "";
/*     */ 
/*     */     
/* 189 */     recordSet.executeSql("select sub.subwfid,sub.isSame,sub.mainrequestid,req.requestname,req.requestnamenew,req.workflowid from workflow_subwfrequest sub left join workflow_requestbase req on req.requestid=sub.mainrequestid where sub.subrequestid=" + this.globalEntity.getRequestid());
/* 190 */     if (recordSet.next() && 
/* 191 */       recordSet.getInt("mainrequestid") > -1) {
/* 192 */       str1 = Util.null2String(recordSet.getString("subwfid"));
/* 193 */       str2 = Util.null2String(recordSet.getString("isSame"));
/* 194 */       i = recordSet.getInt("mainrequestid");
/* 195 */       SignRequestInfo signRequestInfo = new SignRequestInfo();
/* 196 */       signRequestInfo.setRequestid(String.valueOf(i));
/* 197 */       signRequestInfo.setRequestname(recordSet.getString("requestname"));
/* 198 */       signRequestInfo.setRelwfid(recordSet.getString("workflowid"));
/* 199 */       signRequestInfo.setRequestNameNew(recordSet.getString("requestnamenew"));
/* 200 */       signRequestInfo.setType("main");
/* 201 */       arrayList1.add(signRequestInfo);
/* 202 */       bool1 = true;
/*     */     } 
/*     */ 
/*     */     
/* 206 */     String str8 = "";
/*     */     
/* 208 */     if (!bool1) {
/* 209 */       recordSet.executeQuery("select mainrequestid,requestid,requestname,requestlevel,mainrequestid,creater,creatertype,createdate,createtime,workflowId,currentstatus,currentnodeid,currentnodetype,status,remindTypes,docids,crmids,prjids,cptids , lastnodeid  from workflow_requestbase where requestid=?", new Object[] { Integer.valueOf(this.globalEntity.getRequestid()) });
/* 210 */       if (recordSet.next() && 
/* 211 */         recordSet.getInt("mainrequestid") > -1) {
/* 212 */         i = recordSet.getInt("mainrequestid");
/* 213 */         recordSet.executeSql("select workflowid,requestname,requestnamenew from workflow_requestbase where requestid = " + i);
/* 214 */         if (recordSet.next()) {
/* 215 */           str8 = recordSet.getString("workflowid");
/* 216 */           String str10 = recordSet.getString("requestname");
/* 217 */           String str11 = recordSet.getString("requestnamenew");
/* 218 */           int j = -1;
/* 219 */           recordSet.executeSql("select workflowid from workflow_requestbase where requestid = " + this.globalEntity.getRequestid());
/* 220 */           if (recordSet.next()) {
/* 221 */             j = recordSet.getInt("workflowid");
/*     */           }
/* 223 */           recordSet.executeSql("select 1 from Workflow_SubwfSet where mainworkflowid = " + str8 + " and subworkflowid =" + this.globalEntity.getWorkflowid() + " and isread = 1 union select 1 from Workflow_TriDiffWfDiffField a, Workflow_TriDiffWfSubWf b where a.id=b.triDiffWfDiffFieldId and b.isRead=1 and a.mainworkflowid=" + j + " and b.subWorkflowId=" + this.globalEntity
/* 224 */               .getWorkflowid());
/* 225 */           if (recordSet.next()) {
/* 226 */             SignRequestInfo signRequestInfo = new SignRequestInfo();
/* 227 */             signRequestInfo.setRequestid(String.valueOf(i));
/* 228 */             signRequestInfo.setRequestname(str10);
/* 229 */             signRequestInfo.setRequestNameNew(str11);
/* 230 */             signRequestInfo.setType("main");
/* 231 */             signRequestInfo.setRelwfid(str8);
/* 232 */             arrayList1.add(signRequestInfo);
/*     */             
/* 234 */             bool1 = true;
/* 235 */             bool4 = true;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 241 */     if ("".equals(str8)) {
/* 242 */       str8 = "-1";
/*     */     }
/*     */ 
/*     */     
/* 246 */     String str9 = str8 + ", " + this.globalEntity.getWorkflowid();
/* 247 */     recordSet.executeQuery("select distinct subworkflowid from Workflow_SubwfSet where mainworkflowid in (" + str9 + ") and isread = 1  and id IN ( SELECT  subwfid  FROM  Workflow_SubwfRequest where mainrequestid = ? ) ", new Object[] {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 253 */           Integer.valueOf(paramInt) });
/* 254 */     while (recordSet.next()) {
/* 255 */       str3 = str3 + "," + recordSet.getString("subworkflowid");
/*     */     }
/* 257 */     recordSet.executeQuery("select distinct b.subworkflowid from Workflow_TriDiffWfDiffField a, Workflow_TriDiffWfSubWf b  where a.id=b.triDiffWfDiffFieldId and b.isRead=1 and a.mainworkflowid in (" + str9 + ")  and b.id IN ( SELECT  subwfid  FROM  Workflow_SubwfRequest  WHERE  mainrequestid = ?  ) ", new Object[] {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 266 */           Integer.valueOf(paramInt) });
/* 267 */     while (recordSet.next()) {
/* 268 */       str3 = str3 + "," + recordSet.getString("subworkflowid");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 274 */     if (bool1) {
/* 275 */       recordSet.executeSql(" select sub.subrequestid requestid,req.requestname,req.requestnamenew,req.workflowid from workflow_subwfrequest sub left join workflow_requestbase req on req.requestid=sub.subrequestid where sub.mainrequestid=" + i + " and sub.subwfid=" + str1 + " and sub.subrequestid <> " + paramInt);
/*     */       
/* 277 */       while (recordSet.next()) {
/* 278 */         SignRequestInfo signRequestInfo = new SignRequestInfo();
/* 279 */         signRequestInfo.setRequestid(recordSet.getString("requestid"));
/* 280 */         signRequestInfo.setRequestname(recordSet.getString("requestname"));
/* 281 */         signRequestInfo.setRequestNameNew(recordSet.getString("requestnamenew"));
/* 282 */         signRequestInfo.setRelwfid(recordSet.getString("workflowid"));
/* 283 */         signRequestInfo.setType("parallel");
/* 284 */         arrayList1.add(signRequestInfo);
/*     */         
/* 286 */         arrayList.add(recordSet.getString("requestid"));
/* 287 */         bool3 = true;
/*     */       } 
/*     */       
/* 290 */       if (!"-1".equals(str8)) {
/* 291 */         recordSet.executeSql("select requestid,requestname,requestnamenew,workflowid from workflow_requestbase where mainrequestid = " + str8);
/* 292 */         while (recordSet.next()) {
/* 293 */           if (("," + str3 + ",").contains(recordSet.getString("workflowid"))) {
/* 294 */             SignRequestInfo signRequestInfo = new SignRequestInfo();
/* 295 */             signRequestInfo.setRequestid(recordSet.getString("requestid"));
/* 296 */             signRequestInfo.setRequestname(recordSet.getString("requestname"));
/* 297 */             signRequestInfo.setRequestNameNew(recordSet.getString("requestnamenew"));
/* 298 */             signRequestInfo.setRelwfid(recordSet.getString("workflowid"));
/* 299 */             signRequestInfo.setType("parallel");
/* 300 */             if (arrayList1.contains(signRequestInfo)) {
/*     */               continue;
/*     */             }
/* 303 */             arrayList1.add(signRequestInfo);
/*     */             
/* 305 */             arrayList.add(recordSet.getString("requestid"));
/* 306 */             if (!(paramInt + "").equals(recordSet.getString("requestid"))) {
/* 307 */               bool3 = true;
/* 308 */               bool6 = true;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 316 */     if (bool1 && !bool4) {
/*     */       
/* 318 */       if ("1".equals(str2)) {
/* 319 */         recordSet.executeSql("select isreadMainWfNodes,isreadMainwf, isreadParallelwfNodes,isreadParallelwf,subworkflowid from workflow_tridiffwfsubwf where id = " + str1);
/*     */       } else {
/* 321 */         recordSet.executeSql("select isreadMainWfNodes,isreadMainwf, isreadParallelwfNodes,isreadParallelwf,subworkflowid from workflow_subwfset where id = " + str1);
/*     */       } 
/* 323 */       if (recordSet.next()) {
/* 324 */         str4 = Util.null2String(recordSet.getString("isreadMainwf"));
/* 325 */         str5 = Util.null2String(recordSet.getString("isreadMainWfNodes"));
/* 326 */         str6 = Util.null2String(recordSet.getString("isreadParallelwf"));
/* 327 */         str7 = Util.null2String(recordSet.getString("isreadParallelwfNodes"));
/* 328 */         String str10 = Util.null2String(recordSet.getString("subworkflowid"));
/* 329 */         String str11 = WorkflowVersion.getAllVersionStringByWFIDs(String.valueOf(str10));
/* 330 */         if ("all".equals(str7)) {
/* 331 */           recordSet.executeSql("select nodeid from workflow_flownode where " + Util.getSubINClause(str11, "workflowid", "in"));
/* 332 */           String str = "-1";
/* 333 */           while (recordSet.next()) {
/* 334 */             str = str + "," + recordSet.getString("nodeid");
/*     */           }
/* 336 */           str7 = str;
/* 337 */         } else if (str11.indexOf(",") > -1) {
/* 338 */           String[] arrayOfString = str7.split(",");
/* 339 */           str7 = "";
/* 340 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 341 */             str7 = str7 + getChildNodeids(getParentNodeid(arrayOfString[b])) + ",";
/*     */           }
/* 343 */           str7 = str7.substring(0, str7.length() - 1);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 349 */     recordSet.executeSql("select sub.subwfid,sub.isSame,sub.subrequestid requestid,req.requestname,req.requestnamenew,req.workflowid from workflow_subwfrequest sub left join workflow_requestbase req on req.requestid=sub.subrequestid where sub.mainrequestid='" + paramInt + "' and req.workflowid in (" + 
/*     */         
/* 351 */         WorkflowVersion.getAllVersionStringByWFIDs(str3) + ") order by sub.subrequestid desc");
/* 352 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 353 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 354 */     while (recordSet.next()) {
/* 355 */       SignRequestInfo signRequestInfo = new SignRequestInfo();
/* 356 */       signRequestInfo.setRequestid(recordSet.getString("requestid"));
/* 357 */       signRequestInfo.setRequestname(recordSet.getString("requestname"));
/* 358 */       signRequestInfo.setRequestNameNew(recordSet.getString("requestnamenew"));
/* 359 */       signRequestInfo.setRelwfid(recordSet.getString("workflowid"));
/* 360 */       signRequestInfo.setType("sub");
/* 361 */       arrayList1.add(signRequestInfo);
/*     */       
/* 363 */       arrayList.add(recordSet.getString("requestid"));
/* 364 */       bool2 = true;
/*     */       
/* 366 */       str1 = Util.null2String(recordSet.getString("subwfid"));
/* 367 */       str2 = Util.null2String(recordSet.getString("isSame"));
/*     */       
/* 369 */       hashMap2.put(recordSet.getString("requestid"), str1);
/* 370 */       hashMap1.put(str1, str2);
/*     */     } 
/*     */     
/* 373 */     if (this.globalEntity.getRequestid() > 0 && !"-1".equals(str3)) {
/* 374 */       recordSet.executeSql("select requestid,requestname,requestnamenew,workflowid from workflow_requestbase where mainrequestid = " + this.globalEntity.getRequestid() + " and workflowid in (" + str3 + ")");
/* 375 */       while (recordSet.next()) {
/* 376 */         SignRequestInfo signRequestInfo = new SignRequestInfo();
/* 377 */         signRequestInfo.setRequestid(recordSet.getString("requestid"));
/* 378 */         signRequestInfo.setRequestname(recordSet.getString("requestname"));
/* 379 */         signRequestInfo.setRequestNameNew(recordSet.getString("requestnamenew"));
/* 380 */         signRequestInfo.setRelwfid(recordSet.getString("workflowid"));
/* 381 */         signRequestInfo.setType("sub");
/* 382 */         if (arrayList1.contains(signRequestInfo)) {
/*     */           continue;
/*     */         }
/* 385 */         arrayList1.add(signRequestInfo);
/*     */         
/* 387 */         arrayList.add(recordSet.getString("requestid"));
/* 388 */         bool2 = true;
/* 389 */         bool5 = true;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 394 */     ArrayList<TriggerSetting> arrayList2 = new ArrayList();
/* 395 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 396 */     boolean bool7 = bool5;
/* 397 */     if (bool2) {
/* 398 */       Iterator<String> iterator1 = hashMap1.keySet().iterator();
/* 399 */       while (iterator1.hasNext()) {
/* 400 */         String str10 = iterator1.next();
/* 401 */         String str11 = (String)hashMap1.get(str10);
/*     */ 
/*     */         
/* 404 */         if ("1".equals(str11)) {
/* 405 */           recordSet.executeSql("select id,isreadNodes,isread,subworkflowid from workflow_tridiffwfsubwf  where id = " + str10);
/*     */         } else {
/* 407 */           recordSet.executeSql("select id,isreadNodes,isread,subworkflowid from workflow_subwfset where id = " + str10);
/*     */         } 
/*     */         
/* 410 */         if (recordSet.next()) {
/* 411 */           String str12 = Util.null2String(recordSet.getString("id"));
/*     */           
/* 413 */           String str13 = Util.null2String(recordSet.getString("isread"));
/*     */           
/* 415 */           String str14 = Util.null2String(recordSet.getString("isreadNodes"));
/*     */           
/* 417 */           String str15 = Util.null2String(recordSet.getString("subworkflowid"));
/*     */           
/* 419 */           if ("all".equals(str14)) {
/* 420 */             recordSet.executeSql("select nodeid from workflow_flownode where workflowid in (" + WorkflowVersion.getAllVersionStringByWFIDs(str15) + ")");
/* 421 */             str14 = "-1";
/* 422 */             while (recordSet.next()) {
/* 423 */               str14 = str14 + "," + recordSet.getString("nodeid");
/*     */             }
/*     */           } 
/*     */           
/* 427 */           TriggerSetting triggerSetting = new TriggerSetting();
/* 428 */           triggerSetting.setSettingId(str12);
/* 429 */           triggerSetting.setIsRead(str13);
/* 430 */           triggerSetting.setIsReadNodes(str14);
/*     */           
/* 432 */           arrayList2.add(triggerSetting);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 437 */       for (byte b = 0; b < arrayList2.size(); b++) {
/* 438 */         TriggerSetting triggerSetting = arrayList2.get(b);
/* 439 */         if ("1".equals(triggerSetting.getIsRead())) bool7 = true; 
/* 440 */         hashMap3.put(triggerSetting.getSettingId(), triggerSetting);
/*     */ 
/*     */         
/* 443 */         String str = triggerSetting.getIsReadNodes();
/* 444 */         String[] arrayOfString = str.split(",");
/* 445 */         ArrayList<? extends CharSequence> arrayList3 = new ArrayList();
/* 446 */         for (String str10 : arrayOfString) {
/* 447 */           arrayList3.addAll(WorkflowVersion.getRelationNodeListByNodeID(str10));
/*     */         }
/*     */         
/* 450 */         triggerSetting.setIsReadNodes(String.join(",", arrayList3));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 456 */     boolean bool8 = (!this.globalEntity.isIsprint() && !this.globalEntity.isIsworkflowhtmldoc()) ? true : false;
/*     */     
/* 458 */     boolean bool9 = (bool1 && (("1".equals(str4) && !this.globalEntity.isIsprint() && !this.globalEntity.isIsworkflowhtmldoc()) || bool4)) ? true : false;
/*     */     
/* 460 */     boolean bool10 = (bool2 && !this.globalEntity.isIsprint() && !this.globalEntity.isIsworkflowhtmldoc() && bool7) ? true : false;
/*     */     
/* 462 */     boolean bool11 = (bool3 && ("1".equals(str6) || bool6) && !this.globalEntity.isIsprint() && !this.globalEntity.isIsworkflowhtmldoc()) ? true : false;
/*     */     
/* 464 */     Iterator<SignRequestInfo> iterator = arrayList1.iterator();
/* 465 */     while (iterator.hasNext()) {
/* 466 */       SignRequestInfo signRequestInfo = iterator.next();
/* 467 */       int j = Util.getIntValue(signRequestInfo.getRequestid());
/* 468 */       String str10 = signRequestInfo.getType();
/* 469 */       String str11 = "";
/* 470 */       String str12 = "-1";
/* 471 */       if (("main".equals(str10) && !bool9) || ("sub".equals(str10) && !bool10) || ("parallel".equals(str10) && !bool11)) {
/* 472 */         iterator.remove(); continue;
/*     */       } 
/* 474 */       if ("main".equals(str10)) {
/* 475 */         str11 = SystemEnv.getHtmlLabelName(21254, this.user.getLanguage());
/* 476 */         String str = Strings.isNullOrEmpty(signRequestInfo.getRequestNameNew()) ? signRequestInfo.getRequestname() : signRequestInfo.getRequestNameNew();
/* 477 */         str11 = str11 + " <a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequestForwardSPA.jsp";
/*     */         
/* 479 */         str11 = str11 + "?requestid=" + j + "&relaterequest=" + paramInt + "&isrequest=3&isovertime=0&desrequestid=" + this.globalEntity.getRequestid() + this.globalEntity.getAuthSignatureInfo() + "&f_weaver_belongto_userid=" + Util.null2String(this.params.get("f_weaver_belongto_userid")) + "&f_weaver_belongto_usertype=" + Util.null2String(this.params.get("f_weaver_belongto_usertype")) + "')>";
/* 480 */         str11 = str11 + " " + str + "</a>";
/* 481 */         str11 = str11 + " " + SystemEnv.getHtmlLabelName(504, this.user.getLanguage()) + ":";
/*     */         
/* 483 */         if (str4.equals("1")) {
/* 484 */           if (str5.equals("all")) {
/* 485 */             recordSet.executeSql("select distinct nodeid from workflow_requestlog where requestid = " + j);
/* 486 */             String str13 = "";
/* 487 */             while (recordSet.next()) {
/* 488 */               str13 = str13 + recordSet.getString("nodeid") + ",";
/*     */             }
/* 490 */             str13 = str13 + "-1";
/* 491 */             str12 = str13;
/*     */           } else {
/* 493 */             str12 = str5;
/*     */           } 
/*     */         }
/* 496 */         if (bool4) {
/* 497 */           recordSet.executeSql("select distinct nodeid from workflow_requestlog where requestid = " + j);
/* 498 */           String str13 = "";
/* 499 */           while (recordSet.next()) {
/* 500 */             str13 = str13 + recordSet.getString("nodeid") + ",";
/*     */           }
/* 502 */           str13 = str13 + "-1";
/* 503 */           str12 = str13;
/*     */         } 
/* 505 */       } else if ("sub".equals(str10)) {
/* 506 */         String str = (String)hashMap2.get("" + j);
/* 507 */         if (str != null && !"".equals(str)) {
/* 508 */           TriggerSetting triggerSetting = (TriggerSetting)hashMap3.get(str);
/* 509 */           if (triggerSetting != null && triggerSetting.getIsRead().equals("1")) {
/*     */             
/* 511 */             str12 = triggerSetting.getIsReadNodes();
/* 512 */             str11 = SystemEnv.getHtmlLabelName(19344, this.user.getLanguage());
/* 513 */             String str13 = Strings.isNullOrEmpty(signRequestInfo.getRequestNameNew()) ? signRequestInfo.getRequestname() : signRequestInfo.getRequestNameNew();
/* 514 */             str11 = str11 + " <a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequestForwardSPA.jsp";
/*     */             
/* 516 */             str11 = str11 + "?requestid=" + j + "&relaterequest=" + paramInt + "&isrequest=2&isovertime=0&desrequestid=" + this.globalEntity.getRequestid() + this.globalEntity.getAuthSignatureInfo() + "&f_weaver_belongto_userid=" + Util.null2String(this.params.get("f_weaver_belongto_userid")) + "&f_weaver_belongto_usertype=" + Util.null2String(this.params.get("f_weaver_belongto_usertype")) + "')>";
/* 517 */             str11 = str11 + " " + str13 + "</a>";
/* 518 */             str11 = str11 + " " + SystemEnv.getHtmlLabelName(504, this.user.getLanguage());
/*     */           } 
/*     */         } else {
/*     */           
/* 522 */           recordSet.executeQuery("select distinct nodeid from workflow_requestlog where requestid = " + j, new Object[0]);
/* 523 */           while (recordSet.next()) {
/* 524 */             str12 = str12 + "," + recordSet.getString("nodeid");
/*     */           }
/* 526 */           str11 = SystemEnv.getHtmlLabelName(19344, this.user.getLanguage());
/* 527 */           String str13 = Strings.isNullOrEmpty(signRequestInfo.getRequestNameNew()) ? signRequestInfo.getRequestname() : signRequestInfo.getRequestNameNew();
/* 528 */           str11 = str11 + " <a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequestForwardSPA.jsp";
/*     */           
/* 530 */           str11 = str11 + "?requestid=" + j + "&relaterequest=" + paramInt + "&isrequest=2&isovertime=0&desrequestid=" + this.globalEntity.getRequestid() + this.globalEntity.getAuthSignatureInfo() + "&f_weaver_belongto_userid=" + Util.null2String(this.params.get("f_weaver_belongto_userid")) + "&f_weaver_belongto_usertype=" + Util.null2String(this.params.get("f_weaver_belongto_usertype")) + "')>";
/* 531 */           str11 = str11 + " " + str13 + "</a>";
/* 532 */           str11 = str11 + " " + SystemEnv.getHtmlLabelName(504, this.user.getLanguage());
/*     */         }
/*     */       
/* 535 */       } else if ("parallel".equals(str10)) {
/* 536 */         str11 = SystemEnv.getHtmlLabelName(21255, this.user.getLanguage());
/* 537 */         String str = Strings.isNullOrEmpty(signRequestInfo.getRequestNameNew()) ? signRequestInfo.getRequestname() : signRequestInfo.getRequestNameNew();
/* 538 */         str11 = str11 + " <a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequestForwardSPA.jsp";
/*     */         
/* 540 */         str11 = str11 + "?requestid=" + j + "&relaterequest=" + paramInt + "&isrequest=4&isovertime=0&desrequestid=" + this.globalEntity.getRequestid() + this.globalEntity.getAuthSignatureInfo() + "&f_weaver_belongto_userid=" + Util.null2String(this.params.get("f_weaver_belongto_userid")) + "&f_weaver_belongto_usertype=" + Util.null2String(this.params.get("f_weaver_belongto_usertype")) + "')>";
/* 541 */         str11 = str11 + " " + str + "</a>";
/* 542 */         str11 = str11 + " " + SystemEnv.getHtmlLabelName(504, this.user.getLanguage());
/*     */         
/* 544 */         if (str6.equals("1")) {
/* 545 */           str12 = str7;
/*     */         }
/*     */       } 
/*     */       
/* 549 */       signRequestInfo.setRelviewlogs(str12);
/* 550 */       signRequestInfo.setSignshowname(str11);
/* 551 */       signRequestInfo.setRequestname(Strings.isNullOrEmpty(signRequestInfo.getRequestNameNew()) ? signRequestInfo.getRequestname() : signRequestInfo.getRequestNameNew());
/*     */     } 
/*     */ 
/*     */     
/* 555 */     for (SignRequestInfo signRequestInfo : arrayList1) {
/* 556 */       int j = Util.getIntValue(signRequestInfo.getRequestid());
/* 557 */       recordSet.executeQuery("select a.isOpenCommunication,a.isShowSignCommunicate,a.isExpendCommunicate from workflow_base a,workflow_requestbase b where a.id = b.workflowid and b.requestid = ?", new Object[] { Integer.valueOf(j) });
/* 558 */       if (recordSet.next()) {
/* 559 */         signRequestInfo.setIsOpenCommunication(Boolean.valueOf("1".equals(Util.null2String(recordSet.getString("isOpenCommunication")))));
/* 560 */         signRequestInfo.setIsShowSignCommunicate(Boolean.valueOf("1".equals(Util.null2String(recordSet.getString("isShowSignCommunicate")))));
/* 561 */         signRequestInfo.setIsExpendCommunicate(Boolean.valueOf("1".equals(Util.null2String(recordSet.getString("isExpendCommunicate")))));
/* 562 */         signRequestInfo.setHasSubmitRight(Boolean.valueOf((new SuperviseManagerBiz()).hasWorkflowViewRight(this.user, j) ? true : WorkflowCommunicationBiz.hasSubmitRight(j, this.user)));
/*     */       } 
/*     */     } 
/*     */     
/* 566 */     this.requestLogDatas.put("tablabel1", SystemEnv.getHtmlLabelName(125734, this.user.getLanguage()));
/* 567 */     this.requestLogDatas.put("tablabel2", SystemEnv.getHtmlLabelName(32572, this.user.getLanguage()));
/* 568 */     this.requestLogDatas.put("tablabel3", SystemEnv.getHtmlLabelName(84540, this.user.getLanguage()));
/* 569 */     this.requestLogDatas.put("tablabel4", SystemEnv.getHtmlLabelName(84541, this.user.getLanguage()));
/* 570 */     this.requestLogDatas.put("tablabel5", SystemEnv.getHtmlLabelName(84542, this.user.getLanguage()));
/* 571 */     this.requestLogDatas.put("userId", Integer.valueOf(this.user.getUID()));
/* 572 */     this.requestLogDatas.put("isRelatedTome", Boolean.valueOf(bool8));
/* 573 */     this.requestLogDatas.put("hasMainWfRight", Boolean.valueOf(bool9));
/* 574 */     this.requestLogDatas.put("hasChildWfRight", Boolean.valueOf(bool10));
/* 575 */     this.requestLogDatas.put("hasOldChildReq", Boolean.valueOf(bool5));
/* 576 */     this.requestLogDatas.put("hasParallelWfRight", Boolean.valueOf(bool11));
/* 577 */     this.requestLogDatas.put("hasMainReq", Boolean.valueOf(bool1));
/* 578 */     this.requestLogDatas.put("isReadMain", str4);
/* 579 */     this.requestLogDatas.put("hasChildReq", Boolean.valueOf(bool2));
/* 580 */     this.requestLogDatas.put("hasOldMainReq", Boolean.valueOf(bool4));
/* 581 */     this.requestLogDatas.put("hasParallelReq", Boolean.valueOf(bool3));
/* 582 */     this.requestLogDatas.put("isReadParallel", str6);
/* 583 */     this.requestLogDatas.put("hasOldParallelReq", Boolean.valueOf(bool6));
/* 584 */     this.requestLogDatas.put("isReadMainNodes", str5);
/* 585 */     this.requestLogDatas.put("isReadParallelNodes", str7);
/* 586 */     this.requestLogDatas.put("allrequestInfos", arrayList1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadWfRelatedParams() throws Exception {
/* 595 */     RecordSet recordSet = new RecordSet();
/* 596 */     String str1 = null;
/* 597 */     recordSet.executeSql("select isFormSignature from workflow_flownode where workflowId=" + this.globalEntity.getWorkflowid() + " and nodeId=" + this.globalEntity.getNodeid());
/* 598 */     if (recordSet.next()) {
/* 599 */       str1 = Util.null2String(recordSet.getString("isFormSignature"));
/*     */     }
/* 601 */     this.requestLogDatas.put("isFormSignature", str1);
/*     */     
/* 603 */     String str2 = "select nodeid from workflow_flownode where workflowid = " + this.globalEntity.getWorkflowid() + " and nodetype = '0'";
/* 604 */     recordSet.executeSql(str2);
/* 605 */     recordSet.next();
/* 606 */     String str3 = recordSet.getString("nodeid");
/* 607 */     this.requestLogDatas.put("creatorNodeId", str3);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 612 */     int i = 10;
/* 613 */     if ("portal".equals(this.globalEntity.getLoadmethod())) {
/* 614 */       i = Util.getIntValue(Util.null2String(this.params.get("wfsignlddtcnt")), 5);
/*     */     } else {
/* 616 */       boolean bool = "split".equals(this.globalEntity.getLoadmethod());
/* 617 */       if (bool) {
/* 618 */         recordSet.executeSql("select pageSize from ecology_pagesize where pageId = 'SIGNVIEW_VIEWID' and userid=" + this.user.getUID());
/* 619 */         if (recordSet.next())
/* 620 */           i = recordSet.getInt("pageSize"); 
/*     */       } else {
/* 622 */         i = 14;
/*     */       } 
/* 624 */       if (this.globalEntity.isIsprint() || this.globalEntity.isIsworkflowhtmldoc()) {
/* 625 */         i = Integer.MAX_VALUE;
/*     */       }
/* 627 */       this.requestLogDatas.put("wfsignlddtcnt", Integer.valueOf(i));
/*     */     } 
/* 629 */     String str4 = "1";
/* 630 */     recordSet.executeSql("select status from WorkflowSignTXStatus where userid=" + this.user.getUID());
/* 631 */     if (recordSet.next()) {
/* 632 */       str4 = recordSet.getString("status");
/*     */     }
/* 634 */     this.requestLogDatas.put("txStatus", str4);
/*     */     
/* 636 */     WFManager wFManager = new WFManager();
/* 637 */     wFManager.setWfid(this.globalEntity.getWorkflowid());
/* 638 */     wFManager.getWfInfo();
/* 639 */     String str5 = Util.null2String(wFManager.getOrderbytype());
/* 640 */     this.requestLogDatas.put("isShowSignCommunicate", wFManager.getIsShowSignCommunicate());
/* 641 */     this.requestLogDatas.put("isExpendCommunicate", wFManager.getIsExpendCommunicate());
/*     */     
/* 643 */     this.requestLogDatas.put("orderbytype", str5);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadPortalBaseInfo() {
/* 650 */     if ("portal".equals(this.globalEntity.getLoadmethod())) {
/* 651 */       String str = "select t.nodeid,t.workflowid from workflow_currentoperator t left join workflow_nodebase t1 on t.nodeid  = t1.id  where t.requestid=? and t.userid=? and t.usertype=? order by t.id desc";
/* 652 */       boolean bool = "2".equals(this.user.getLogintype()) ? true : false;
/* 653 */       RecordSet recordSet = new RecordSet();
/* 654 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(this.globalEntity.getRequestid()), Integer.valueOf(this.user.getUID()), Integer.valueOf(bool) });
/* 655 */       if (recordSet.next()) {
/* 656 */         this.globalEntity.setNodeid(Util.getIntValue(recordSet.getString(1), 0));
/* 657 */         this.globalEntity.setWorkflowid(Util.getIntValue(recordSet.getString(2), 0));
/*     */       } 
/* 659 */       if (this.globalEntity.getNodeid() < 1) {
/* 660 */         str = "select t.currentnodeid from workflow_requestbase t left join workflow_nodebase t1 on t.currentnodeid = t1.id  where t.requestid= ?";
/* 661 */         recordSet.executeQuery(str, new Object[] { Integer.valueOf(this.globalEntity.getRequestid()) });
/* 662 */         if (recordSet.next()) {
/* 663 */           this.globalEntity.setNodeid(Util.getIntValue(recordSet.getString(1), 0));
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionGroup> getRequestLogSearchCondition(String paramString) {
/* 675 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 676 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 677 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 99, "operatorid", "1"));
/* 678 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 124, "deptid", "4"));
/* 679 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 141, "subcomid", "164"));
/* 680 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 504, "content"));
/* 681 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 682 */     arrayList1.add(new SearchConditionOption("", ""));
/* 683 */     if (!"".equals(paramString)) {
/* 684 */       RecordSet recordSet = new RecordSet();
/* 685 */       recordSet.executeSql("select id,nodename from workflow_nodebase where id in (" + paramString + ") order by id");
/* 686 */       while (recordSet.next()) {
/* 687 */         arrayList1.add(new SearchConditionOption(recordSet.getString(1), recordSet.getString(2)));
/*     */       }
/*     */       
/* 690 */       recordSet.executeQuery(" select id,nodename from workflow_freenode where id in (" + paramString + ") order by nodeOrder ", new Object[0]);
/* 691 */       while (recordSet.next()) {
/* 692 */         arrayList1.add(new SearchConditionOption(recordSet.getString(1), recordSet.getString(2)));
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 697 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 15586, "nodename", arrayList1));
/* 698 */     arrayList.add(conditionFactory.createCondition(ConditionType.DATE, 21663, new String[] { "createdateselect", "createdatefrom", "createdateto" }));
/* 699 */     ArrayList<SearchConditionGroup> arrayList2 = new ArrayList();
/* 700 */     arrayList2.add(new SearchConditionGroup("", true, arrayList));
/* 701 */     return arrayList2;
/*     */   }
/*     */   
/*     */   public String getParentNodeid(String paramString) {
/* 705 */     if ("".equals(paramString)) {
/* 706 */       return paramString;
/*     */     }
/* 708 */     RecordSet recordSet = new RecordSet();
/* 709 */     recordSet.executeQuery("select parentnodeid from workflow_versionnoderelation where nodeid = ?", new Object[] { paramString });
/* 710 */     if (recordSet.next()) {
/* 711 */       String str = recordSet.getString("parentnodeid");
/* 712 */       return getParentNodeid(str);
/*     */     } 
/* 714 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getChildNodeids(String paramString) {
/* 719 */     if ("".equals(paramString)) {
/* 720 */       return paramString;
/*     */     }
/* 722 */     RecordSet recordSet = new RecordSet();
/* 723 */     recordSet.executeQuery("select nodeid from workflow_versionnoderelation where parentnodeid = ? ", new Object[] { paramString });
/* 724 */     String str = paramString;
/* 725 */     if (recordSet.next()) {
/* 726 */       recordSet.beforFirst();
/* 727 */       while (recordSet.next()) {
/* 728 */         String str1 = recordSet.getString("nodeid");
/* 729 */         str = str + "," + getChildNodeids(str1);
/*     */       } 
/*     */     } else {
/* 732 */       return paramString;
/*     */     } 
/* 734 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/requestLog/LoadRequestLogBaseInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */