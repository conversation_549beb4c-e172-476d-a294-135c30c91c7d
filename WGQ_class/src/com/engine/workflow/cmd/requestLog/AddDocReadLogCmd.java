/*    */ package com.engine.workflow.cmd.requestLog;
/*    */ 
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.docs.docs.DocReadTagUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AddDocReadLogCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   public AddDocReadLogCmd(User paramUser, Map<String, Object> paramMap) {
/* 20 */     this.user = paramUser;
/* 21 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 27 */     DocReadTagUtil docReadTagUtil = new DocReadTagUtil();
/* 28 */     String str = Util.null2String(this.params.get("docId"));
/* 29 */     docReadTagUtil.addDocReadTag(str, this.user.getUID() + "", this.user.getLogintype(), Util.null2String(this.params.get("param_ip")));
/* 30 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/requestLog/AddDocReadLogCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */