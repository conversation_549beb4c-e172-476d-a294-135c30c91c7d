/*    */ package com.engine.workflow.cmd.requestLog;
/*    */ 
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetLogAllReceiveUserCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   public GetLogAllReceiveUserCmd(Map<String, Object> paramMap, User paramUser) {
/* 18 */     this.params = paramMap;
/* 19 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 24 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 25 */     int i = Util.getIntValue(Util.null2String(this.params.get("logid")));
/* 26 */     RecordSet recordSet = new RecordSet();
/* 27 */     String str = "";
/* 28 */     recordSet.executeQuery("select receivedPersons from workflow_requestlog where logid  = ? ", new Object[] { Integer.valueOf(i) });
/* 29 */     if (recordSet.next()) {
/* 30 */       str = Util.null2String(recordSet.getString("receivedPersons"));
/*    */     }
/* 32 */     hashMap.put("receivedPersons", str);
/* 33 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/requestLog/GetLogAllReceiveUserCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */