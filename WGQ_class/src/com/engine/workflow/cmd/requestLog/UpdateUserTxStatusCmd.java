/*    */ package com.engine.workflow.cmd.requestLog;
/*    */ 
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UpdateUserTxStatusCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   public UpdateUserTxStatusCmd(User paramUser, Map<String, Object> paramMap) {
/* 21 */     this.user = paramUser;
/* 22 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 27 */     String str1 = Util.null2String(this.params.get("txstatus"));
/* 28 */     RecordSet recordSet = new RecordSet();
/* 29 */     recordSet.executeUpdate("delete from WorkflowSignTXStatus where userid = ? ", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 30 */     String str2 = "insert into WorkflowSignTXStatus(status,userid) values (" + str1 + "," + this.user.getUID() + ")";
/* 31 */     boolean bool = recordSet.executeSql(str2);
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 33 */     hashMap.put("status", Boolean.valueOf(bool));
/* 34 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/requestLog/UpdateUserTxStatusCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */