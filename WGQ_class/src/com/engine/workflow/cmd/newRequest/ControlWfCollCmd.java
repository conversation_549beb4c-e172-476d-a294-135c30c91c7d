/*    */ package com.engine.workflow.cmd.newRequest;
/*    */ 
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.util.CollectionUtil;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.workflow.WorkflowVersion;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ControlWfCollCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   public ControlWfCollCmd(Map<String, Object> paramMap, User paramUser) {
/* 22 */     this.params = paramMap;
/* 23 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 29 */     int i = this.user.getUID();
/* 30 */     String str1 = "T" + Util.null2String(this.params.get("worktypeid"));
/* 31 */     String str2 = "W" + Util.null2String(this.params.get("workflowid"));
/* 32 */     boolean bool1 = (Util.getIntValue((new StringBuilder()).append(this.params.get("workflowid")).append("").toString()) <= 0) ? true : false;
/*    */     
/* 34 */     boolean bool = "1".equals(Util.null2String(this.params.get("needall")));
/*    */     
/* 36 */     boolean bool2 = true;
/*    */     
/* 38 */     String str3 = "select selectedworkflow , isuserdefault from workflow_RequestUserDefault where userid=" + i;
/* 39 */     String str4 = "";
/* 40 */     String str5 = "0";
/* 41 */     RecordSet recordSet = new RecordSet();
/* 42 */     recordSet.executeQuery(str3, new Object[0]);
/* 43 */     while (recordSet.next()) {
/* 44 */       bool2 = false;
/* 45 */       str4 = recordSet.getString("selectedworkflow");
/* 46 */       str5 = recordSet.getString("isuserdefault");
/*    */     } 
/* 48 */     List<String> list = Util.splitString2List(str4, "|");
/* 49 */     if (bool) {
/* 50 */       if (!list.contains(str1)) {
/* 51 */         list.add(str1);
/*    */       }
/* 53 */       if (!list.contains(str2)) {
/* 54 */         list.add(str2);
/*    */       }
/*    */     } else {
/*    */       
/* 58 */       List list1 = Util.splitString2List(WorkflowVersion.getAllVersionStringByWFIDs(str2.substring(1)), ",");
/* 59 */       for (String str : list1) {
/* 60 */         if (list.contains("W" + str)) {
/* 61 */           list.remove("W" + str);
/*    */         }
/*    */       } 
/*    */       
/* 65 */       if (list.contains(str1)) {
/* 66 */         String str = str1.substring(str1.indexOf("T") + 1);
/* 67 */         if (bool1) {
/* 68 */           str3 = "select id from ofs_workflow where sysid=" + str;
/*    */         } else {
/* 70 */           str3 = "select id from workflow_base where workflowtype=" + str;
/*    */         } 
/* 72 */         recordSet.executeQuery(str3, new Object[0]);
/* 73 */         boolean bool3 = false;
/* 74 */         while (recordSet.next()) {
/* 75 */           int j = recordSet.getInt("id");
/* 76 */           if (list.contains("W" + j)) {
/* 77 */             bool3 = true;
/*    */             
/*    */             break;
/*    */           } 
/*    */         } 
/* 82 */         if (!bool3) {
/* 83 */           list.remove(str1);
/*    */         }
/*    */       } 
/*    */     } 
/* 87 */     str4 = CollectionUtil.list2String(list, "|");
/* 88 */     if (bool2) {
/* 89 */       str3 = "insert into workflow_RequestUserDefault(userid,selectedworkflow,isuserdefault,showoperator) values(?,?,1,'1')";
/* 90 */       recordSet.executeUpdate(str3, new Object[] { Integer.valueOf(i), str1 + "|" + str2 });
/*    */     } else {
/* 92 */       str3 = "update workflow_RequestUserDefault set isuserdefault=1,selectedworkflow=? where userid=?";
/* 93 */       recordSet.executeUpdate(str3, new Object[] { str4, Integer.valueOf(i) });
/*    */     } 
/* 95 */     hashMap.put("success", Boolean.valueOf(true));
/* 96 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/newRequest/ControlWfCollCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */