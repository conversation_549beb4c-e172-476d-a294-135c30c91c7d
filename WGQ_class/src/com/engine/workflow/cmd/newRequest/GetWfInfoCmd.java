/*     */ package com.engine.workflow.cmd.newRequest;
/*     */ 
/*     */ import com.engine.core.interceptor.AbstractCommand;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.portal.biz.menuinfo.MenuInfoCommonBiz;
/*     */ import com.engine.workflow.biz.NewRequestBiz;
/*     */ import com.engine.workflow.biz.mobileCenter.MobileDimensionsBiz;
/*     */ import com.engine.workflow.entity.mobileCenter.Dimensions;
/*     */ import com.engine.workflow.util.CollectionUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.ofs.util.OfsWorkflowShareUtils;
/*     */ import weaver.system.RequestDefaultComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetWfInfoCmd
/*     */   extends AbstractCommand<Map<String, Object>>
/*     */ {
/*     */   public GetWfInfoCmd(Map<String, Object> paramMap, User paramUser) {
/*  36 */     this.params = paramMap;
/*  37 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     NewRequestBiz newRequestBiz = new NewRequestBiz(this.user);
/*  43 */     String str1 = Util.null2String((String)this.params.get("ismobile"));
/*  44 */     int i = Util.getIntValue((String)this.params.get("menuid"), -1);
/*     */     
/*  46 */     String str2 = " in ";
/*  47 */     String str3 = appendWfType2WfID(Util.null2String(this.params.get("wfTypeRange")).trim(), Util.null2String(this.params.get("wfRange")).trim());
/*  48 */     String str4 = "";
/*  49 */     String str5 = "";
/*     */ 
/*     */     
/*  52 */     MobileDimensionsBiz mobileDimensionsBiz = new MobileDimensionsBiz();
/*  53 */     if ("1".equals(str1) && i != -1 && "mine".equals(mobileDimensionsBiz.getScope(i))) {
/*  54 */       Dimensions dimensions = mobileDimensionsBiz.getDimension(i);
/*  55 */       if ("6".equals(dimensions.getApptype())) {
/*  56 */         String str = dimensions.getSourcetype();
/*  57 */         if ("1".equals(str) || "3".equals(str)) {
/*  58 */           str2 = "in";
/*  59 */         } else if ("2".equals(str) || "4".equals(str)) {
/*  60 */           str2 = "not in";
/*     */         } 
/*  62 */         if ("1".equals(str) || "2".equals(str)) {
/*  63 */           str4 = mobileDimensionsBiz.getRange(i, str);
/*  64 */         } else if ("3".equals(str) || "4".equals(str)) {
/*  65 */           str5 = mobileDimensionsBiz.getRange(i, str);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  70 */     if ("1".equals(str1) && i != -1 && "wfCenter".equals(mobileDimensionsBiz.getScope(i))) {
/*  71 */       RecordSet recordSet = new RecordSet();
/*  72 */       recordSet.executeQuery("select sourcetype,contentinfo from workflow_mobilecenter_newwf where menuid = ?", new Object[] { Integer.valueOf(i) });
/*  73 */       if (recordSet.next()) {
/*  74 */         int j = Util.getIntValue(recordSet.getString("sourcetype"), 0);
/*  75 */         if (j == 1 || j == 3) {
/*  76 */           str2 = "in";
/*  77 */         } else if (j == 2 || j == 4) {
/*  78 */           str2 = "not in";
/*     */         } 
/*  80 */         if (j == 1 || j == 2) {
/*  81 */           str4 = Util.null2String(recordSet.getString("contentinfo"));
/*  82 */         } else if (j == 3 || j == 4) {
/*  83 */           str5 = Util.null2String(recordSet.getString("contentinfo"));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  88 */     String str6 = Util.null2String(this.params.get("menuidforportal"));
/*  89 */     if (!"".equals(str6)) {
/*  90 */       MenuInfoCommonBiz menuInfoCommonBiz = new MenuInfoCommonBiz();
/*  91 */       String str = menuInfoCommonBiz.getLeftMenuParams(str6);
/*  92 */       if (!"".equals(str)) {
/*  93 */         str4 = str.split("=")[1];
/*     */       }
/*     */     } 
/*     */     
/*  97 */     if (!"".equals(str3)) {
/*  98 */       str3 = "".equals(str4) ? str3 : (str3 + "," + str4);
/*     */     } else {
/* 100 */       str3 = str4;
/*     */     } 
/* 102 */     String str7 = Util.null2String(this.params.get("sqlWhere"));
/* 103 */     if (!"".equals(str5)) {
/* 104 */       str7 = str7 + " and " + Util.getSubINClause(str5, "t2.workflowtype", str2);
/*     */     }
/* 106 */     newRequestBiz.setNewRequestRange(WorkflowVersion.getAllVersionStringByWFIDs(str3));
/* 107 */     newRequestBiz.setInornot(str2);
/* 108 */     newRequestBiz.setIsmobile("1".equals(str1));
/* 109 */     newRequestBiz.setCustomSqlWhere(str7);
/* 110 */     newRequestBiz.setTypeids(str5);
/*     */ 
/*     */     
/* 113 */     List list = newRequestBiz.getWfInfo("");
/* 114 */     String str8 = newRequestBiz.getCommonuse();
/* 115 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 116 */     hashMap1.put("datas", list);
/* 117 */     hashMap1.put("commonuse", str8);
/* 118 */     hashMap1.put("user", newRequestBiz.getWfUser(String.valueOf(this.user.getUID())));
/*     */     
/* 120 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 121 */     hashMap2.put("toplabel", SystemEnv.getHtmlLabelName(16392, this.user.getLanguage()));
/* 122 */     hashMap1.put("createwflabels", hashMap2);
/*     */     
/* 124 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 125 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 126 */     hashMap3.put("title", SystemEnv.getHtmlLabelName(84382, this.user.getLanguage()));
/* 127 */     hashMap3.put("key", "1");
/* 128 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 129 */     hashMap4.put("title", SystemEnv.getHtmlLabelName(18030, this.user.getLanguage()));
/* 130 */     hashMap4.put("key", "2");
/* 131 */     arrayList.add(hashMap3);
/* 132 */     arrayList.add(hashMap4);
/* 133 */     if ("1".equals(str8)) {
/* 134 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 135 */       hashMap.put("title", SystemEnv.getHtmlLabelName(28184, this.user.getLanguage()));
/* 136 */       hashMap.put("key", "3");
/* 137 */       arrayList.add(hashMap);
/*     */     } 
/* 139 */     hashMap1.put("tabs", arrayList);
/* 140 */     hashMap1.put("userOperateHabit", getUserOperateHabit());
/* 141 */     hashMap1.put("unSupportSPAWf", NewRequestBiz.getUnSupportSPAWf(newRequestBiz.getForceUnsupportSPAWF()));
/* 142 */     hashMap1.put("openByDefaultBrowser", Util.null2String((new WorkflowConfigComInfo()).getValue("wfOpenByDefaultBrowser")));
/* 143 */     hashMap1.put("ofsWorkflowDatas", newRequestBiz.getOfsWorkflowDatas());
/* 144 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   private Map<String, Object> getUserOperateHabit() {
/* 148 */     RecordSet recordSet = new RecordSet();
/* 149 */     recordSet.executeQuery("select addWfDefaultSelectedTab,addwfmulitcol,addwfisabc from workflow_requestuserdefault where userid = ?", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 150 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 151 */     if (recordSet.next()) {
/* 152 */       hashMap.put("defaultSelectedTab", recordSet.getString("addWfDefaultSelectedTab"));
/* 153 */       hashMap.put("mulitcol", recordSet.getString("addwfmulitcol"));
/* 154 */       hashMap.put("isAbc", recordSet.getString("addwfisabc"));
/*     */     } 
/*     */     
/* 157 */     WorkflowConfigComInfo workflowConfigComInfo = new WorkflowConfigComInfo();
/* 158 */     if ("".equals(Util.null2String(hashMap.get("defaultSelectedTab")))) {
/* 159 */       hashMap.put("defaultSelectedTab", Util.null2String(workflowConfigComInfo.getValue("new_req_default_selectedtab")));
/*     */     }
/* 161 */     if ("".equals(Util.null2String(hashMap.get("mulitcol")))) {
/* 162 */       hashMap.put("mulitcol", Util.null2String(workflowConfigComInfo.getValue("new_req_default_mulitcol")));
/*     */     }
/* 164 */     if ("".equals(Util.null2String(hashMap.get("isAbc")))) {
/* 165 */       hashMap.put("isAbc", Util.null2String(workflowConfigComInfo.getValue("new_req_default_isabc")));
/*     */     }
/*     */     
/* 168 */     RequestDefaultComInfo requestDefaultComInfo = new RequestDefaultComInfo();
/* 169 */     String str = Util.null2String(requestDefaultComInfo.getCommonuse(this.user.getUID() + ""));
/* 170 */     if (!"1".equals(str) && "3".equals(Util.null2String(hashMap.get("defaultSelectedTab")))) {
/* 171 */       hashMap.put("defaultSelectedTab", "1");
/*     */     }
/*     */     
/* 174 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String appendWfType2WfID(String paramString1, String paramString2) {
/* 184 */     if ("".equals(paramString1)) {
/* 185 */       return paramString2;
/*     */     }
/* 187 */     RecordSet recordSet = new RecordSet();
/* 188 */     recordSet.executeQuery("select id from workflow_base where workflowtype in(" + paramString1 + ") and isvalid in('1','3')", new Object[0]);
/* 189 */     while (recordSet.next()) {
/* 190 */       paramString2 = paramString2 + "," + recordSet.getString("id");
/*     */     }
/*     */     
/* 193 */     List list = (new OfsWorkflowShareUtils()).getWorkflowIDsBySysid(paramString1);
/* 194 */     paramString2 = paramString2 + "," + CollectionUtil.list2String(list, ",");
/* 195 */     return paramString2.startsWith(",") ? paramString2.substring(1) : paramString2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/newRequest/GetWfInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */