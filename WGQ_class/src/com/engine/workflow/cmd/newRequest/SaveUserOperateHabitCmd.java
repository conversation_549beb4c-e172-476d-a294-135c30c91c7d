/*    */ package com.engine.workflow.cmd.newRequest;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SaveUserOperateHabitCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public SaveUserOperateHabitCmd(Map<String, Object> paramMap, User paramUser) {
/* 21 */     this.params = paramMap;
/* 22 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 27 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 33 */     String str1 = Util.null2String(Util.null2String(this.params.get("wfDefaultSelectedTabKey")), "1");
/* 34 */     String str2 = Util.null2String(this.params.get("mulitcol"));
/* 35 */     String str3 = Util.null2String(this.params.get("isAbc"));
/* 36 */     RecordSet recordSet = new RecordSet();
/* 37 */     recordSet.executeQuery("select addWfDefaultSelectedTab from workflow_requestuserdefault where userid  = ?", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 38 */     if (recordSet.next()) {
/* 39 */       recordSet.executeUpdate("update workflow_requestuserdefault set addWfDefaultSelectedTab = ?,addwfmulitcol = ?,addwfisabc = ? where userid = ?", new Object[] { str1, str2, str3, Integer.valueOf(this.user.getUID()) });
/*    */     } else {
/* 41 */       recordSet.executeUpdate("insert into workflow_requestuserdefault(userid,addWfDefaultSelectedTab,addwfmulitcol,addwfisabc,showoperator) values(?,?,?,?,'1')", new Object[] { Integer.valueOf(this.user.getUID()), str1, str2, str3 });
/*    */     } 
/* 43 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/newRequest/SaveUserOperateHabitCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */