/*    */ package com.engine.workflow.cmd.secondauth;
/*    */ 
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.util.SecondAuthUtil;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class GetAuthDataCmd
/*    */   implements Command<Map<String, Object>> {
/*    */   private User user;
/*    */   private Map<String, Object> requestParams;
/*    */   
/*    */   public User getUser() {
/* 17 */     return this.user;
/*    */   }
/*    */   
/*    */   public void setUser(User paramUser) {
/* 21 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getRequestParams() {
/* 25 */     return this.requestParams;
/*    */   }
/*    */   
/*    */   public void setRequestParams(Map<String, Object> paramMap) {
/* 29 */     this.requestParams = paramMap;
/*    */   }
/*    */   
/*    */   public GetAuthDataCmd(Map<String, Object> paramMap, User paramUser) {
/* 33 */     this.requestParams = paramMap;
/* 34 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 39 */     Map<String, Object> map = (Map)new HashMap<>();
/* 40 */     String str = Util.null2String(this.requestParams.get("authKey"));
/* 41 */     if (this.user != null) {
/* 42 */       map = SecondAuthUtil.getAuthData(str, this.user);
/*    */     }
/* 44 */     return map;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/GetAuthDataCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */