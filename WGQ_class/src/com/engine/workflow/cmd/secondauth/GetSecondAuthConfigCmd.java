/*     */ package com.engine.workflow.cmd.secondauth;
/*     */ 
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.SecondAuthBiz;
/*     */ import com.engine.workflow.biz.requestForm.RequestFormBiz;
/*     */ import com.engine.workflow.constant.SecondAuthType;
/*     */ import com.engine.workflow.entity.SecondAuthEntity;
/*     */ import com.engine.workflow.util.SecondAuthUtil;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSecondAuthConfigCmd
/*     */   implements Command<Map<String, Object>>
/*     */ {
/*     */   private User user;
/*     */   private Map<String, Object> requestParams;
/*     */   
/*     */   public User getUser() {
/*  27 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  31 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getRequestParams() {
/*  35 */     return this.requestParams;
/*     */   }
/*     */   
/*     */   public void setRequestParams(Map<String, Object> paramMap) {
/*  39 */     this.requestParams = paramMap;
/*     */   }
/*     */   
/*     */   public GetSecondAuthConfigCmd(Map<String, Object> paramMap, User paramUser) {
/*  43 */     this.requestParams = paramMap;
/*  44 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  49 */     int i = Util.getIntValue(Util.null2String(this.requestParams.get("requestid")), 0);
/*  50 */     int j = Util.getIntValue(Util.null2String(this.requestParams.get("workflowid")), 0);
/*  51 */     int k = Util.getIntValue(Util.null2String(this.requestParams.get("nodeid")), 0);
/*  52 */     String str1 = Util.null2String(this.requestParams.get("src"));
/*  53 */     String str2 = Util.null2String(this.requestParams.get("__weaver_request_url__"));
/*  54 */     boolean bool = "true".equals(Util.null2String(this.requestParams.get("_ec_ismobile")));
/*     */     
/*  56 */     if ("intervenor".equals(str1)) {
/*  57 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  58 */       hashMap.put("success", Integer.valueOf(1));
/*  59 */       return (Map)hashMap;
/*     */     } 
/*  61 */     Map<String, Object> map = SecondAuthBiz.getSecondAuthConfig(j, k, this.user, str2);
/*     */ 
/*     */     
/*  64 */     if (bool) {
/*  65 */       SecondAuthBiz.appendQysParams4Mobile(map);
/*     */     }
/*     */     
/*  68 */     getCAAuthKey(map);
/*  69 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getCAAuthKey(Map<String, Object> paramMap) {
/*  76 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isEnableAuth")));
/*  77 */     int j = Util.getIntValue(Util.null2String(paramMap.get("secondAuthType")));
/*  78 */     int k = Util.getIntValue(Util.null2String(paramMap.get("isEnableProtect")), 0);
/*  79 */     int m = Util.getIntValue(Util.null2String(paramMap.get("protectType")), 0);
/*  80 */     int n = Util.getIntValue(Util.null2String(paramMap.get("qysSignWay")), 2);
/*     */     
/*  82 */     int i1 = Util.getIntValue(Util.null2String(this.requestParams.get("requestid")), -1);
/*     */ 
/*     */     
/*  85 */     if ((i == 1 && j == SecondAuthType.CAAuth.getId()) || (i == 1 && j == SecondAuthType.QYS
/*  86 */       .getId() && n == 1) || (k == 1 && m == SecondAuthType.CAAuth
/*  87 */       .getId()) || (k == 1 && m == SecondAuthType.QYS
/*  88 */       .getId() && n == 1)) {
/*  89 */       int i2 = Util.getIntValue(Util.null2String(this.requestParams.get("workflowid")));
/*  90 */       int i3 = Util.getIntValue(Util.null2String(this.requestParams.get("nodeid")));
/*  91 */       String str1 = "";
/*  92 */       if (this.requestParams.containsKey("remark")) {
/*  93 */         str1 = Util.null2String(this.requestParams.get("remark"));
/*     */       } else {
/*  95 */         str1 = getRemark(i1, i2, i3);
/*     */       } 
/*     */       
/*  98 */       WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  99 */       String str2 = Util.null2String(workflowAllComInfo.getFormId(String.valueOf(i2)));
/* 100 */       String str3 = Util.null2String(workflowAllComInfo.getIsBill(String.valueOf(i2)));
/* 101 */       SecondAuthEntity secondAuthEntity = new SecondAuthEntity();
/*     */       
/* 103 */       User user = getUser();
/* 104 */       int i4 = user.getLanguage();
/*     */       
/* 106 */       Map map = RequestFormBiz.getFieldInfoByFormid(str2 + "", str3 + "", i4, false);
/*     */       
/* 108 */       secondAuthEntity.setAuthConfig(paramMap);
/* 109 */       secondAuthEntity.setUser(user);
/* 110 */       secondAuthEntity.setRemark(str1);
/* 111 */       secondAuthEntity.setFieldInfoMap(map);
/*     */       
/* 113 */       paramMap.putAll(SecondAuthUtil.getCAAuthKey(i1, i2, secondAuthEntity));
/*     */     } 
/*     */   }
/*     */   
/*     */   private String getRemark(int paramInt1, int paramInt2, int paramInt3) {
/* 118 */     String str = "";
/* 119 */     User user = getUser();
/* 120 */     if (user != null) {
/* 121 */       int i = user.getUID();
/* 122 */       boolean bool = false;
/* 123 */       String str1 = user.getLogintype();
/* 124 */       if ("2".equals(str1)) {
/* 125 */         bool = true;
/*     */       }
/* 127 */       String str2 = "select remark from workflow_requestlog where requestId = ? and workflowId = ? and nodeId = ? and operator = ? and operatortype = ? and logtype = ? order by logid desc";
/* 128 */       RecordSet recordSet = new RecordSet();
/* 129 */       recordSet.executeQuery(str2, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), Integer.valueOf(i), Integer.valueOf(bool), "1" });
/* 130 */       if (recordSet.next()) {
/* 131 */         str = Util.null2String(recordSet.getString("remark"));
/*     */       }
/*     */     } 
/* 134 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/GetSecondAuthConfigCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */