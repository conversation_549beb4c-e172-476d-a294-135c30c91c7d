/*    */ package com.engine.workflow.cmd.secondauth;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.EMManager;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class GetEMIDCmd
/*    */   implements Command<Map<String, Object>> {
/*    */   private User user;
/*    */   
/*    */   public GetEMIDCmd(User paramUser) {
/* 15 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 20 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 21 */     hashMap.put("success", Boolean.valueOf(false));
/*    */     
/* 23 */     if (this.user != null) {
/* 24 */       Map map = EMManager.getSystemIfo("y");
/* 25 */       if (!map.isEmpty()) {
/* 26 */         String str = (String)map.get("ec_id");
/* 27 */         hashMap.put("sysId", str);
/* 28 */         hashMap.put("success", Boolean.valueOf(true));
/*    */       } 
/*    */     } 
/* 31 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/GetEMIDCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */