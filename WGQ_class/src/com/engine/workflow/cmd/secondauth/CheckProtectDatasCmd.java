/*    */ package com.engine.workflow.cmd.secondauth;
/*    */ 
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.SecondAuthBiz;
/*    */ import com.engine.workflow.entity.SecondAuthEntity;
/*    */ import com.engine.workflow.util.SecondAuthUtil;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CheckProtectDatasCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   private User user;
/*    */   private Map<String, Object> requestParams;
/*    */   
/*    */   public User getUser() {
/* 24 */     return this.user;
/*    */   }
/*    */   
/*    */   public void setUser(User paramUser) {
/* 28 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getRequestParams() {
/* 32 */     return this.requestParams;
/*    */   }
/*    */   
/*    */   public void setRequestParams(Map<String, Object> paramMap) {
/* 36 */     this.requestParams = paramMap;
/*    */   }
/*    */   
/*    */   public CheckProtectDatasCmd(Map<String, Object> paramMap, User paramUser) {
/* 40 */     this.requestParams = paramMap;
/* 41 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 46 */     SecondAuthEntity secondAuthEntity = new SecondAuthEntity();
/*    */     
/* 48 */     int i = this.user.getLanguage();
/* 49 */     int j = Util.getIntValue(Util.null2String(this.requestParams.get("requestId")), 0);
/* 50 */     int k = Util.getIntValue(Util.null2String(this.requestParams.get("workflowId")), 0);
/* 51 */     int m = Util.getIntValue(Util.null2String(this.requestParams.get("nodeId")), 0);
/* 52 */     int n = Util.getIntValue(Util.null2String(this.requestParams.get("logId")), 0);
/* 53 */     String str1 = Util.null2String(this.requestParams.get("formId"));
/* 54 */     String str2 = Util.null2String(this.requestParams.get("isbill"));
/*    */ 
/*    */     
/* 57 */     Map map = SecondAuthBiz.getSecondAuthConfig(k, m, this.user);
/*    */ 
/*    */     
/* 60 */     secondAuthEntity.setAuthConfig(map);
/* 61 */     secondAuthEntity.setUser(this.user);
/*    */     
/* 63 */     return SecondAuthUtil.checkProtectDatas(j, k, n, m, secondAuthEntity);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/CheckProtectDatasCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */