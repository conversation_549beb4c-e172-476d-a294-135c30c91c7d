/*    */ package com.engine.workflow.cmd.secondauth;
/*    */ 
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.util.SecondAuthUtil;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class SaveCAAuthResultCmd
/*    */   implements Command<Map<String, Object>> {
/*    */   private User user;
/*    */   private Map<String, Object> requestParams;
/*    */   
/*    */   public User getUser() {
/* 16 */     return this.user;
/*    */   }
/*    */   
/*    */   public void setUser(User paramUser) {
/* 20 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getRequestParams() {
/* 24 */     return this.requestParams;
/*    */   }
/*    */   
/*    */   public void setRequestParams(Map<String, Object> paramMap) {
/* 28 */     this.requestParams = paramMap;
/*    */   }
/*    */   
/*    */   public SaveCAAuthResultCmd(Map<String, Object> paramMap, User paramUser) {
/* 32 */     this.requestParams = paramMap;
/* 33 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 38 */     String str1 = Util.null2String(this.requestParams.get("authKey"));
/* 39 */     String str2 = Util.null2String(this.requestParams.get("signValue"));
/* 40 */     return SecondAuthUtil.saveCAAuthResult(str1, str2, this.user);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/SaveCAAuthResultCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */