/*    */ package com.engine.workflow.cmd.secondauth;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.Util_DataMap;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.io.IOException;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CheckCAAuthCmd
/*    */   implements Command<Map<String, Object>>
/*    */ {
/*    */   private User user;
/*    */   private Map<String, Object> requestParams;
/*    */   
/*    */   public User getUser() {
/* 21 */     return this.user;
/*    */   }
/*    */   
/*    */   public void setUser(User paramUser) {
/* 25 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getRequestParams() {
/* 29 */     return this.requestParams;
/*    */   }
/*    */   
/*    */   public void setRequestParams(Map<String, Object> paramMap) {
/* 33 */     this.requestParams = paramMap;
/*    */   }
/*    */   
/*    */   public CheckCAAuthCmd(Map<String, Object> paramMap, User paramUser) {
/* 37 */     this.requestParams = paramMap;
/* 38 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 43 */     String str1 = Util.null2String(this.requestParams.get("authKey"));
/* 44 */     String str2 = "";
/* 45 */     boolean bool = false;
/* 46 */     if (!"".equals(str1)) {
/* 47 */       String str3 = str1 + "_data";
/* 48 */       String str4 = str1 + "_status";
/* 49 */       if (Util_DataMap.containsKey(str4)) {
/* 50 */         bool = true;
/* 51 */         str2 = Util.null2String(Util_DataMap.getObjVal(str4));
/*    */       } 
/*    */       
/* 54 */       if ("1".equals(str2)) {
/*    */         
/*    */         try {
/* 57 */           if (Util_DataMap.containsKey(str3)) {
/* 58 */             Util_DataMap.clearVal(str3);
/*    */           }
/*    */           
/* 61 */           if (Util_DataMap.containsKey(str4)) {
/* 62 */             Util_DataMap.clearVal(str4);
/*    */           }
/* 64 */         } catch (IOException iOException) {
/* 65 */           iOException.printStackTrace();
/*    */         } 
/*    */       }
/*    */     } 
/*    */     
/* 70 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 71 */     hashMap.put("authStatus", str2);
/* 72 */     hashMap.put("success", Integer.valueOf(bool));
/*    */     
/* 74 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/CheckCAAuthCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */