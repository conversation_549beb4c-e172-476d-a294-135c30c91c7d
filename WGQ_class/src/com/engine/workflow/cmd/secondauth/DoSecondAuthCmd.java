/*     */ package com.engine.workflow.cmd.secondauth;
/*     */ 
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.SecondAuthBiz;
/*     */ import com.engine.workflow.constant.SecondAuthType;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.PasswordUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.login.TokenJSCX;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoSecondAuthCmd
/*     */   implements Command<Map<String, Object>>
/*     */ {
/*     */   private User user;
/*     */   private Map<String, Object> requestParams;
/*  26 */   private Log log = LogFactory.getLog(getClass());
/*     */   
/*     */   public User getUser() {
/*  29 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  33 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getRequestParams() {
/*  37 */     return this.requestParams;
/*     */   }
/*     */   
/*     */   public void setRequestParams(Map<String, Object> paramMap) {
/*  41 */     this.requestParams = paramMap;
/*     */   }
/*     */   
/*     */   public DoSecondAuthCmd(Map<String, Object> paramMap, User paramUser) {
/*  45 */     this.requestParams = paramMap;
/*  46 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  53 */     int i = this.user.getLanguage();
/*  54 */     hashMap.put("api_status", Boolean.valueOf(false));
/*  55 */     hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(125979, i));
/*     */     
/*  57 */     int j = Util.getIntValue(Util.null2String(this.requestParams.get("authType")), 0);
/*     */     
/*  59 */     if (j == SecondAuthType.DynamicPassword.getId())
/*  60 */       return checkDynamicPassword(); 
/*  61 */     if (j == SecondAuthType.DynamicToken.getId())
/*  62 */       return checkDynamicToken(); 
/*  63 */     if (j == SecondAuthType.SecondAuthPassword.getId()) {
/*  64 */       return checkSecondPasword();
/*     */     }
/*     */     
/*  67 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> checkDynamicPassword() {
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  72 */     String str = Util.null2String(this.requestParams.get("authCode")).trim();
/*     */     
/*  74 */     RecordSet recordSet = new RecordSet();
/*  75 */     if (!"".equals(str) && this.user != null) {
/*  76 */       String str1 = TimeUtil.getCurrentTimeString();
/*  77 */       int i = this.user.getUID();
/*  78 */       int j = this.user.getLanguage();
/*  79 */       String str2 = "select salt,dyncmiaPassword,validTime from workflow_secondauth_password where userId = ? and userType = ? ";
/*  80 */       recordSet.executeQuery(str2, new Object[] { Integer.valueOf(i), Integer.valueOf(0) });
/*  81 */       if (recordSet.next()) {
/*  82 */         String str3 = Util.null2String(recordSet.getString("salt"));
/*  83 */         String str4 = Util.null2String(recordSet.getString("dyncmiaPassword"));
/*  84 */         String str5 = Util.null2String(recordSet.getString("validTime"));
/*     */         
/*  86 */         if (str5.compareTo(str1) < 0) {
/*  87 */           hashMap.put("api_status", Boolean.valueOf(false));
/*  88 */           hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501129, j));
/*     */         } else {
/*  90 */           String[] arrayOfString = PasswordUtil.encrypt(str, str3);
/*  91 */           String str6 = Util.null2String(arrayOfString[0]);
/*     */           
/*  93 */           if (str4.equals(str6)) {
/*  94 */             SecondAuthBiz.updateFreeSecretTime(this.user, SecondAuthType.DynamicPassword.getId());
/*  95 */             hashMap.put("api_status", Boolean.valueOf(true));
/*  96 */             hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(22083, j));
/*     */           } else {
/*  98 */             hashMap.put("api_status", Boolean.valueOf(false));
/*  99 */             hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501133, j));
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 105 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> checkDynamicToken() {
/* 109 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 110 */     String str = Util.null2String(this.requestParams.get("authCode")).trim();
/*     */     
/* 112 */     RecordSet recordSet = new RecordSet();
/* 113 */     if (!"".equals(str) && this.user != null) {
/* 114 */       int i = this.user.getUID();
/* 115 */       int j = this.user.getLanguage();
/* 116 */       String str1 = "select tokenkey from hrmresourcemanager where id = ?";
/* 117 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/* 118 */       int k = recordSet.getCounts();
/*     */       
/* 120 */       if (k <= 0) {
/* 121 */         str1 = "select tokenkey from hrmresource where id = ?";
/* 122 */         recordSet.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/*     */       } 
/*     */       
/* 125 */       String str2 = "";
/* 126 */       if (recordSet.next()) {
/* 127 */         str2 = Util.null2String(recordSet.getString("tokenkey"));
/*     */       }
/*     */       
/* 130 */       if ("".equals(str2)) {
/* 131 */         hashMap.put("api_status", Boolean.valueOf(false));
/* 132 */         hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501134, j));
/*     */       } else {
/* 134 */         TokenJSCX tokenJSCX = new TokenJSCX();
/* 135 */         boolean bool = false;
/*     */         
/* 137 */         str1 = "select * from tokenJscx WHERE tokenKey = ?";
/* 138 */         recordSet.executeQuery(str1, new Object[] { str2 });
/* 139 */         if (recordSet.next()) {
/* 140 */           if (str2.startsWith("1")) {
/* 141 */             bool = tokenJSCX.checkDLKey(str2, str);
/* 142 */           } else if (str2.startsWith("2")) {
/* 143 */             bool = tokenJSCX.checkDLKey(str2, str);
/* 144 */           } else if (str2.startsWith("3")) {
/* 145 */             bool = tokenJSCX.checkKey(str2, str);
/*     */           } 
/*     */           
/* 148 */           if (!bool) {
/* 149 */             hashMap.put("api_status", Boolean.valueOf(false));
/* 150 */             hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501135, j));
/*     */           } else {
/* 152 */             SecondAuthBiz.updateFreeSecretTime(this.user, SecondAuthType.DynamicToken.getId());
/* 153 */             hashMap.put("api_status", Boolean.valueOf(true));
/* 154 */             hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(22083, j));
/*     */           } 
/*     */         } else {
/* 157 */           hashMap.put("api_status", Boolean.valueOf(false));
/* 158 */           hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501136, j));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 163 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> checkSecondPasword() {
/* 167 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 168 */     String str = Util.null2String(this.requestParams.get("authCode")).trim();
/*     */     
/* 170 */     RecordSet recordSet = new RecordSet();
/* 171 */     if (!"".equals(str) && this.user != null) {
/* 172 */       int i = this.user.getUID();
/* 173 */       int j = this.user.getLanguage();
/*     */       
/* 175 */       String str1 = "select salt,secondarypwd,usesecondarypwd from hrmresourcemanager where id = ?";
/* 176 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/* 177 */       int k = recordSet.getCounts();
/* 178 */       if (k <= 0) {
/* 179 */         str1 = "select salt,secondarypwd,usesecondarypwd from hrmresource where id = ?";
/* 180 */         recordSet.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/*     */       } 
/*     */       
/* 183 */       if (recordSet.next()) {
/* 184 */         String str2 = Util.null2String(recordSet.getString("salt"));
/* 185 */         String str3 = Util.null2String(recordSet.getString("secondarypwd"));
/* 186 */         int m = Util.getIntValue(recordSet.getString("usesecondarypwd"), 0);
/*     */         
/* 188 */         if (m != 1) {
/* 189 */           hashMap.put("api_status", Boolean.valueOf(false));
/* 190 */           hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501316, j));
/* 191 */           return (Map)hashMap;
/*     */         } 
/*     */         
/* 194 */         String[] arrayOfString = PasswordUtil.encrypt(str, str2);
/* 195 */         String str4 = Util.null2String(arrayOfString[0]);
/*     */         
/* 197 */         if (str4.equals(str3)) {
/* 198 */           SecondAuthBiz.updateFreeSecretTime(this.user, SecondAuthType.SecondAuthPassword.getId());
/* 199 */           hashMap.put("api_status", Boolean.valueOf(true));
/* 200 */           hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(22083, j));
/*     */         } else {
/* 202 */           hashMap.put("api_status", Boolean.valueOf(false));
/* 203 */           hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501137, j));
/*     */         } 
/*     */       } 
/*     */     } 
/* 207 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/DoSecondAuthCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */