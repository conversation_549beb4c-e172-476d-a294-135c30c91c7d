/*     */ package com.engine.workflow.cmd.secondauth;
/*     */ 
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.PasswordUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.settings.ChgPasswdReminder;
/*     */ import weaver.hrm.settings.HrmSettingsComInfo;
/*     */ import weaver.hrm.settings.RemindSettings;
/*     */ import weaver.sms.SMSManager;
/*     */ import weaver.sms.SmsFromMouldEnum;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetDynamicPasswordCmd
/*     */   implements Command<Map<String, Object>>
/*     */ {
/*     */   private User user;
/*     */   private Map<String, Object> requestParams;
/*     */   
/*     */   public User getUser() {
/*  29 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  33 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getRequestParams() {
/*  37 */     return this.requestParams;
/*     */   }
/*     */   
/*     */   public void setRequestParams(Map<String, Object> paramMap) {
/*  41 */     this.requestParams = paramMap;
/*     */   }
/*     */   
/*     */   public GetDynamicPasswordCmd(Map<String, Object> paramMap, User paramUser) {
/*  45 */     this.requestParams = paramMap;
/*  46 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*     */     try {
/*  54 */       HrmSettingsComInfo hrmSettingsComInfo = new HrmSettingsComInfo();
/*  55 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  56 */       int i = 0;
/*  57 */       if (this.user != null) {
/*  58 */         int j = this.user.getUID();
/*  59 */         int k = this.user.getLanguage();
/*     */         
/*  61 */         String str1 = hrmSettingsComInfo.getDypadcon();
/*  62 */         int m = Util.getIntValue(hrmSettingsComInfo.getDynapasslen());
/*  63 */         String str2 = Util.null2String(resourceComInfo.getMobile(String.valueOf(j)));
/*     */         
/*  65 */         ChgPasswdReminder chgPasswdReminder = new ChgPasswdReminder();
/*  66 */         RemindSettings remindSettings = chgPasswdReminder.getRemindSettings();
/*  67 */         i = Util.getIntValue(Util.null2String(remindSettings.getValiditySec()), 0);
/*     */         
/*  69 */         String str3 = "";
/*  70 */         if (str1.equals("0")) {
/*  71 */           str3 = Util.passwordBuilderNo(m);
/*  72 */         } else if (str1.equals("1")) {
/*  73 */           str3 = Util.passwordBuilderEn(m);
/*  74 */         } else if (str1.equals("2")) {
/*  75 */           str3 = Util.passwordBuilder(m);
/*     */         } 
/*     */         
/*  78 */         SMSManager sMSManager = new SMSManager();
/*  79 */         sMSManager.setUserid("1");
/*  80 */         sMSManager.setFromMould(SmsFromMouldEnum.WORKFLOW);
/*  81 */         boolean bool = sMSManager.sendSMS(str2, SystemEnv.getHtmlLabelName(501138, k) + "：" + str3);
/*  82 */         if (bool) {
/*  83 */           String[] arrayOfString = PasswordUtil.encrypt(str3);
/*     */           
/*  85 */           String str4 = TimeUtil.getCurrentTimeString();
/*  86 */           String str5 = TimeUtil.timeAdd(str4, i);
/*     */           
/*  88 */           String str6 = Util.null2String(arrayOfString[0]);
/*  89 */           String str7 = Util.null2String(arrayOfString[1]);
/*     */           
/*  91 */           RecordSet recordSet = new RecordSet();
/*  92 */           String str8 = "delete from workflow_secondauth_password where userId = ? and userType = ?";
/*  93 */           recordSet.executeUpdate(str8, new Object[] { Integer.valueOf(j), Integer.valueOf(0) });
/*     */ 
/*     */           
/*  96 */           str8 = "insert into workflow_secondauth_password (userId, userType, dyncmiaPassword, salt, validTime) values (?, ?, ?, ?, ?)";
/*  97 */           boolean bool1 = recordSet.executeUpdate(str8, new Object[] { Integer.valueOf(j), Integer.valueOf(0), str6, str7, str5 });
/*  98 */           hashMap.put("api_status", Boolean.valueOf(true));
/*     */         } else {
/* 100 */           hashMap.put("api_status", Boolean.valueOf(false));
/* 101 */           hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501139, k));
/*     */         } 
/*     */       } else {
/* 104 */         hashMap.put("api_status", Boolean.valueOf(false));
/* 105 */         hashMap.put("api_errormsg", SystemEnv.getHtmlLabelName(501139, 7));
/*     */       } 
/*     */       
/* 108 */       hashMap.put("validSecond", Integer.valueOf(i));
/* 109 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/* 113 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/GetDynamicPasswordCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */