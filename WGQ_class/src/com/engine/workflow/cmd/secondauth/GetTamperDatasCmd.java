/*    */ package com.engine.workflow.cmd.secondauth;
/*    */ 
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.entity.SecondAuthEntity;
/*    */ import com.engine.workflow.util.SecondAuthUtil;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetTamperDatasCmd
/*    */   implements Command<Map<String, Object>>
/*    */ {
/*    */   private User user;
/*    */   private Map<String, Object> requestParams;
/*    */   
/*    */   public User getUser() {
/* 22 */     return this.user;
/*    */   }
/*    */   
/*    */   public void setUser(User paramUser) {
/* 26 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getRequestParams() {
/* 30 */     return this.requestParams;
/*    */   }
/*    */   
/*    */   public void setRequestParams(Map<String, Object> paramMap) {
/* 34 */     this.requestParams = paramMap;
/*    */   }
/*    */   
/*    */   public GetTamperDatasCmd(Map<String, Object> paramMap, User paramUser) {
/* 38 */     this.requestParams = paramMap;
/* 39 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 44 */     int i = Util.getIntValue(Util.null2String(this.requestParams.get("requestid")));
/* 45 */     int j = Util.getIntValue(Util.null2String(this.requestParams.get("workflowid")));
/* 46 */     int k = Util.getIntValue(Util.null2String(this.requestParams.get("formid")));
/* 47 */     int m = Util.getIntValue(Util.null2String(this.requestParams.get("isbill")));
/* 48 */     int n = Util.getIntValue(Util.null2String(this.requestParams.get("nodeid")));
/* 49 */     int i1 = Util.getIntValue(Util.null2String(this.requestParams.get("logid")));
/* 50 */     SecondAuthEntity secondAuthEntity = new SecondAuthEntity();
/*    */     
/* 52 */     User user = getUser();
/* 53 */     int i2 = user.getLanguage();
/*    */ 
/*    */ 
/*    */     
/* 57 */     secondAuthEntity.setUser(user);
/*    */     
/* 59 */     return SecondAuthUtil.getTamperDatas(i, j, i1, n, secondAuthEntity);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/GetTamperDatasCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */