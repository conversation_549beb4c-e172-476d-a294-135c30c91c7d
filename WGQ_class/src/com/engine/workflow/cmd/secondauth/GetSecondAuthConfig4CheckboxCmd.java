/*     */ package com.engine.workflow.cmd.secondauth;
/*     */ 
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.SecondAuthBiz;
/*     */ import com.engine.workflow.constant.SecondAuthType;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.settings.ChgPasswdReminder;
/*     */ import weaver.hrm.settings.RemindSettings;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSecondAuthConfig4CheckboxCmd
/*     */   implements Command<Map<String, Object>>
/*     */ {
/*     */   private User user;
/*     */   private Map<String, Object> requestParams;
/*     */   
/*     */   public User getUser() {
/*  27 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  31 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getRequestParams() {
/*  35 */     return this.requestParams;
/*     */   }
/*     */   
/*     */   public void setRequestParams(Map<String, Object> paramMap) {
/*  39 */     this.requestParams = paramMap;
/*     */   }
/*     */   
/*     */   public GetSecondAuthConfig4CheckboxCmd(Map<String, Object> paramMap, User paramUser) {
/*  43 */     this.requestParams = paramMap;
/*  44 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  54 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  56 */     int i = Util.getIntValue(Util.null2String(this.requestParams.get("workflowid")), 0);
/*  57 */     int j = Util.getIntValue(Util.null2String(this.requestParams.get("nodeid")), 0);
/*     */ 
/*     */     
/*  60 */     RecordSet recordSet1 = new RecordSet();
/*  61 */     RecordSet recordSet2 = new RecordSet();
/*  62 */     String str = "select checkoperation,checktype,dataptnmode,qysSignWay from systemset";
/*  63 */     recordSet1.executeSql(str);
/*     */     
/*  65 */     int k = 0;
/*  66 */     int m = 0;
/*  67 */     int n = 2;
/*  68 */     if (recordSet1.next()) {
/*  69 */       k = Util.getIntValue(Util.null2String(recordSet1.getString("checktype")), -1);
/*  70 */       m = Util.getIntValue(Util.null2String(recordSet1.getString("dataptnmode")), -1);
/*  71 */       n = Util.getIntValue(Util.null2String(recordSet1.getString("qysSignWay")), 2);
/*     */     } 
/*     */     
/*  74 */     ChgPasswdReminder chgPasswdReminder = new ChgPasswdReminder();
/*  75 */     RemindSettings remindSettings = chgPasswdReminder.getRemindSettings();
/*     */     
/*  77 */     str = "select isEnableIdCheck,isEnableDtaPtn,isEnableSignatures,wf_verified from workflow_flownode where workflowid = ? and nodeid = ?";
/*     */     
/*  79 */     recordSet1.executeQuery(str, new Object[] { Integer.valueOf(i), Integer.valueOf(j) });
/*  80 */     if (recordSet1.next()) {
/*  81 */       String str1 = Util.null2String(recordSet1.getString("isEnableIdCheck")).trim();
/*  82 */       String str2 = Util.null2String(recordSet1.getString("isEnableDtaPtn")).trim();
/*     */       
/*  84 */       boolean bool1 = (n == 1 || SecondAuthBiz.isQYSOpen()) ? true : false;
/*     */       
/*  86 */       int i1 = 0;
/*  87 */       if (k == SecondAuthType.DynamicPassword.getId()) {
/*  88 */         i1 = Util.getIntValue(Util.null2String(remindSettings.getSecondNeedDynapass()));
/*  89 */       } else if (k == SecondAuthType.DynamicToken.getId()) {
/*  90 */         i1 = Util.getIntValue(Util.null2String(remindSettings.getSecondNeedusbDt()));
/*  91 */       } else if (k == SecondAuthType.SecondAuthPassword.getId()) {
/*  92 */         i1 = Util.getIntValue(Util.null2String(remindSettings.getSecondPassword()));
/*  93 */       } else if (k == SecondAuthType.CAAuth.getId()) {
/*  94 */         i1 = Util.getIntValue(Util.null2String(remindSettings.getSecondCA()));
/*  95 */       } else if (k == SecondAuthType.QYS.getId()) {
/*  96 */         i1 = bool1 ? 1 : 0;
/*  97 */       } else if (k == SecondAuthType.RealIDAuth.getId()) {
/*  98 */         i1 = Util.getIntValue(Util.null2String(remindSettings.getSecondCL()));
/*     */       } 
/*     */       
/* 101 */       boolean bool2 = false;
/*     */       
/* 103 */       if (i1 == 1 && k > 0 && "1".equals(str1)) {
/* 104 */         bool2 = true;
/*     */       } else {
/* 106 */         bool2 = false;
/*     */       } 
/* 108 */       hashMap.put("isEnableAuth", Integer.valueOf(bool2));
/*     */       
/* 110 */       int i2 = 0;
/* 111 */       if (m == SecondAuthType.CAAuth.getId()) {
/* 112 */         i2 = Util.getIntValue(Util.null2String(remindSettings.getSecondCA()));
/* 113 */       } else if (m == SecondAuthType.QYS.getId()) {
/*     */         
/* 115 */         if (bool1) {
/* 116 */           i2 = 1;
/*     */         } else {
/* 118 */           i2 = 0;
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 123 */       if (bool2 == true && k == SecondAuthType.RealIDAuth.getId()) {
/* 124 */         i2 = 0;
/*     */       }
/*     */ 
/*     */       
/* 128 */       if (i2 == 1 && m > 0 && "1".equals(str2)) {
/* 129 */         hashMap.put("isEnableProtect", Integer.valueOf(1));
/*     */       } else {
/* 131 */         hashMap.put("isEnableProtect", Integer.valueOf(0));
/*     */       } 
/*     */       
/* 134 */       hashMap.put("secondAuthType", Integer.valueOf(k));
/* 135 */       hashMap.put("protectType", Integer.valueOf(m));
/* 136 */       hashMap.put("qysSignWay", Integer.valueOf(n));
/*     */     } 
/*     */     
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/GetSecondAuthConfig4CheckboxCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */