/*     */ package com.engine.workflow.cmd.secondauth;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.workflow.util.ServiceUtil;
/*     */ import com.cloudstore.dev.api.util.Util_DataMap;
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.SecondAuthBiz;
/*     */ import com.engine.workflow.constant.SecondAuthType;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.settings.ChgPasswdReminder;
/*     */ import weaver.hrm.settings.RemindSettings;
/*     */ 
/*     */ public class GetSecondAuthConfig4ListCmd
/*     */   implements Command<Map<String, Object>>
/*     */ {
/*     */   private User user;
/*     */   private Map<String, Object> requestParams;
/*     */   
/*     */   public User getUser() {
/*  28 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  32 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getRequestParams() {
/*  36 */     return this.requestParams;
/*     */   }
/*     */   
/*     */   public void setRequestParams(Map<String, Object> paramMap) {
/*  40 */     this.requestParams = paramMap;
/*     */   }
/*     */   
/*     */   public GetSecondAuthConfig4ListCmd(Map<String, Object> paramMap, User paramUser) {
/*  44 */     this.requestParams = paramMap;
/*  45 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  50 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  51 */     String str1 = Util.null2String(this.requestParams.get("requestIds"));
/*  52 */     String str2 = Util.null2String(this.requestParams.get("__weaver_request_url__"));
/*  53 */     boolean bool = "true".equals(Util.null2String(this.requestParams.get("_ec_ismobile")));
/*     */     
/*  55 */     RecordSet recordSet1 = new RecordSet();
/*  56 */     RecordSet recordSet2 = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  62 */     String str3 = "select checkoperation,checktype,dataptnmode,qysSignWay from systemset";
/*  63 */     recordSet1.executeSql(str3);
/*  64 */     int i = 0;
/*  65 */     String str4 = "";
/*  66 */     int j = 0;
/*  67 */     int k = 2;
/*  68 */     if (recordSet1.next()) {
/*  69 */       i = Util.getIntValue(Util.null2String(recordSet1.getString("checktype")), -1);
/*  70 */       str4 = Util.null2String(recordSet1.getString("checkoperation"));
/*  71 */       j = Util.getIntValue(Util.null2String(recordSet1.getString("dataptnmode")), -1);
/*  72 */       k = Util.getIntValue(Util.null2String(recordSet1.getString("qysSignWay")), 2);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  77 */     String str5 = TimeUtil.getCurrentTimeString();
/*  78 */     boolean bool1 = false;
/*  79 */     String str6 = SecondAuthBiz.getFreeSecretTime(this.user, i);
/*  80 */     if (str6.compareTo(str5) > 0) {
/*  81 */       bool1 = true;
/*     */     }
/*     */     
/*  84 */     if (i == SecondAuthType.QYS.getId() || i == SecondAuthType.RealIDAuth.getId()) {
/*  85 */       bool1 = false;
/*     */     }
/*     */     
/*  88 */     boolean bool2 = (k == 1 || SecondAuthBiz.isQYSOpen()) ? true : false;
/*     */ 
/*     */ 
/*     */     
/*  92 */     ChgPasswdReminder chgPasswdReminder = new ChgPasswdReminder();
/*  93 */     RemindSettings remindSettings = chgPasswdReminder.getRemindSettings();
/*  94 */     int m = 0;
/*  95 */     if (i == SecondAuthType.DynamicPassword.getId()) {
/*  96 */       m = Util.getIntValue(Util.null2String(remindSettings.getSecondNeedDynapass()));
/*  97 */     } else if (i == SecondAuthType.DynamicToken.getId()) {
/*  98 */       m = Util.getIntValue(Util.null2String(remindSettings.getSecondNeedusbDt()));
/*  99 */     } else if (i == SecondAuthType.SecondAuthPassword.getId()) {
/* 100 */       m = Util.getIntValue(Util.null2String(remindSettings.getSecondPassword()));
/* 101 */     } else if (i == SecondAuthType.CAAuth.getId()) {
/* 102 */       m = Util.getIntValue(Util.null2String(remindSettings.getSecondCA()));
/* 103 */     } else if (i == SecondAuthType.QYS.getId() && bool2 == true) {
/* 104 */       m = 1;
/* 105 */     } else if (i == SecondAuthType.RealIDAuth.getId()) {
/* 106 */       m = Util.getIntValue(Util.null2String(remindSettings.getSecondCL()));
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 111 */     boolean bool3 = false;
/* 112 */     if (j == SecondAuthType.QYS.getId() && bool2 == true && k == 2) {
/* 113 */       bool3 = true;
/*     */     }
/*     */     
/* 116 */     boolean bool4 = false;
/* 117 */     boolean bool5 = false;
/*     */     
/* 119 */     if ((m == 1 || bool3 == true) && !"".equals(str1) && this.user != null) {
/* 120 */       String[] arrayOfString = str1.split(",", -1);
/* 121 */       for (String str : arrayOfString) {
/* 122 */         str = Util.null2String(str);
/* 123 */         int n = Util.getIntValue(str, -1);
/* 124 */         if (n > 0 && filterAction(n, this.user, str4)) {
/* 125 */           int i1 = ServiceUtil.getCurrentNode(str, this.user);
/* 126 */           str3 = "select workflowid from workflow_requestbase where requestid = ?";
/* 127 */           recordSet1.executeQuery(str3, new Object[] { Integer.valueOf(n) });
/* 128 */           if (recordSet1.next()) {
/* 129 */             int i2 = Util.getIntValue(recordSet1.getString("workflowid"), 0);
/* 130 */             if (i2 > 0 && i1 > 0) {
/* 131 */               str3 = "select isEnableIdCheck,isEnableDtaPtn,isEnableSignatures,wf_verified from workflow_flownode where workflowid = ? and nodeid = ?";
/*     */               
/* 133 */               recordSet2.executeQuery(str3, new Object[] { Integer.valueOf(i2), Integer.valueOf(i1) });
/* 134 */               if (recordSet2.next()) {
/* 135 */                 String str7 = Util.null2String(recordSet2.getString("isEnableIdCheck")).trim();
/* 136 */                 String str8 = Util.null2String(recordSet2.getString("isEnableDtaPtn")).trim();
/*     */ 
/*     */                 
/* 139 */                 if (m == 1 && !bool4) {
/* 140 */                   if ("1".equals(str7) && bool1 != true) {
/* 141 */                     bool4 = true;
/*     */                   } else {
/* 143 */                     bool4 = false;
/*     */                   } 
/*     */                 }
/*     */                 
/* 147 */                 if (bool3 == true && !bool5) {
/* 148 */                   if ("1".equals(str8)) {
/* 149 */                     bool5 = true;
/*     */                   } else {
/* 151 */                     bool5 = false;
/*     */                   } 
/*     */                 }
/*     */ 
/*     */                 
/* 156 */                 if (bool4 == true && i == SecondAuthType.RealIDAuth.getId()) {
/* 157 */                   bool5 = false;
/*     */                 }
/*     */ 
/*     */                 
/* 161 */                 if ((m == 0 || bool4 == true) && (!bool3 || bool5 == true)) {
/*     */                   break;
/*     */                 }
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 171 */     hashMap.put("isEnableAuth", Integer.valueOf(bool4));
/* 172 */     hashMap.put("isEnableProtect", Integer.valueOf(bool5));
/* 173 */     hashMap.put("secondAuthType", Integer.valueOf(i));
/* 174 */     hashMap.put("protectType", Integer.valueOf(j));
/* 175 */     hashMap.put("qysSignWay", Integer.valueOf(k));
/*     */ 
/*     */ 
/*     */     
/* 179 */     hashMap.putAll(getAuthKey(str1));
/*     */ 
/*     */ 
/*     */     
/* 183 */     if (!"".equals(str2) && k != 1) {
/* 184 */       SecondAuthBiz.getQYSSignUrl(hashMap, this.user, str2);
/*     */ 
/*     */       
/* 187 */       if (bool) {
/* 188 */         SecondAuthBiz.appendQysParams4Mobile(hashMap);
/*     */       }
/*     */     } 
/*     */     
/* 192 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> getAuthKey(String paramString) {
/* 196 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 198 */     int i = this.user.getUID();
/* 199 */     boolean bool = false;
/* 200 */     String str1 = this.user.getLogintype();
/* 201 */     if ("2".equals(str1)) {
/* 202 */       bool = true;
/*     */     }
/*     */     
/* 205 */     String str2 = UUID.randomUUID().toString();
/* 206 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 207 */     hashMap2.put("isMulti", "1");
/* 208 */     hashMap2.put("isShow", "0");
/* 209 */     hashMap2.put("userId", Integer.valueOf(i));
/* 210 */     hashMap2.put("userType", Integer.valueOf(bool));
/*     */     
/* 212 */     Util_DataMap.setObjVal(str2 + "_data", JSONObject.toJSONString(hashMap2));
/* 213 */     Util_DataMap.setObjVal(str2 + "_status", "0");
/* 214 */     hashMap1.put("authKey", str2);
/* 215 */     hashMap1.put("sourcestr", "");
/* 216 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean filterAction(int paramInt, User paramUser, String paramString) {
/* 221 */     boolean bool = false;
/* 222 */     if (!"".equals(paramString)) {
/*     */       
/* 224 */       String[] arrayOfString = paramString.split(",", -1);
/* 225 */       HashSet<String> hashSet = new HashSet();
/* 226 */       for (String str : arrayOfString) {
/* 227 */         if (!"".equals(str)) {
/* 228 */           hashSet.add(str);
/*     */         }
/*     */       } 
/*     */       
/* 232 */       RecordSet recordSet = new RecordSet();
/* 233 */       int i = paramUser.getUID();
/* 234 */       boolean bool1 = false;
/* 235 */       String str1 = paramUser.getLogintype();
/* 236 */       if ("2".equals(str1)) {
/* 237 */         bool1 = true;
/*     */       }
/* 239 */       String str2 = ServiceUtil.calculateCurrentNodeSql(paramInt + "", i, bool1);
/* 240 */       recordSet.executeQuery(str2, new Object[0]);
/* 241 */       if (recordSet.next()) {
/* 242 */         int j = Util.getIntValue(recordSet.getString("isremark"), -1);
/* 243 */         int k = Util.getIntValue(recordSet.getString("takisremark"), 0);
/*     */         
/* 245 */         if (j == 0) {
/* 246 */           if (hashSet.contains("viewtype_approve")) {
/* 247 */             bool = true;
/*     */           }
/* 249 */         } else if (j == 1) {
/* 250 */           if (k == 2) {
/* 251 */             if (hashSet.contains("viewtype_tpostil")) {
/* 252 */               bool = true;
/*     */             }
/*     */           }
/* 255 */           else if (hashSet.contains("viewtype_postil")) {
/* 256 */             bool = true;
/*     */           }
/*     */         
/* 259 */         } else if (j == 9 && 
/* 260 */           hashSet.contains("viewtype_rpostil")) {
/* 261 */           bool = true;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 266 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/secondauth/GetSecondAuthConfig4ListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */