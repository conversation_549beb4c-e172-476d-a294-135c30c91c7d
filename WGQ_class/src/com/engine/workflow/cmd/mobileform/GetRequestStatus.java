/*    */ package com.engine.workflow.cmd.mobileform;
/*    */ 
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.util.CommandUtil;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ import weaver.mobile.webservices.workflow.soa.RequestStatusLog;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetRequestStatus
/*    */   extends CommandUtil<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 20 */     String str1 = null2String(this.params.get("workflowid"));
/* 21 */     String str2 = null2String(this.params.get("requestid"));
/* 22 */     int i = getIntValue(null2String(this.params.get("desrequestid")), 0);
/* 23 */     String str3 = null2String(this.params.get("isurger"));
/* 24 */     RequestStatusLog requestStatusLog = new RequestStatusLog(str1, str2);
/* 25 */     requestStatusLog.setUser(this.user);
/* 26 */     requestStatusLog.setDesrequestid(i);
/* 27 */     requestStatusLog.setIsurger(str3);
/* 28 */     Map<String, String> map = (Map)new HashMap<>();
/*    */     try {
/* 30 */       map = requestStatusLog.getStatusLogResult();
/* 31 */     } catch (Exception exception) {
/* 32 */       exception.printStackTrace();
/* 33 */       map.put("exception", exception.toString());
/*    */     } 
/*    */     
/* 36 */     return (Map)map;
/*    */   }
/*    */ 
/*    */   
/*    */   public GetRequestStatus(Map<String, Object> paramMap, User paramUser) {
/* 41 */     this.params = paramMap;
/* 42 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public GetRequestStatus() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/mobileform/GetRequestStatus.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */