/*     */ package com.engine.workflow.cmd.excelDesign;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.exception.ECException;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import com.engine.workflow.biz.excelDesign.DetailTableAttrBiz;
/*     */ import com.engine.workflow.biz.excelDesign.DoSaveFreeExcelDesignBiz;
/*     */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*     */ import com.engine.workflow.biz.requestForm.RequestSecLevelBiz;
/*     */ import com.engine.workflow.biz.workflowCore.WorkflowBaseBiz;
/*     */ import com.engine.workflow.entity.excelDesign.FieldInfo;
/*     */ import com.google.common.collect.Lists;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.exceldesign.FormTemplateManager;
/*     */ import weaver.workflow.exceldesign.OthersMethod;
/*     */ import weaver.workflow.field.FieldComInfo;
/*     */ import weaver.workflow.request.WFLinkInfo;
/*     */ import weaver.workflow.workflow.WFManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetInitDatasCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  37 */   private Map<String, Object> apidatas = new HashMap<>();
/*     */   
/*     */   private Map<String, String> fieldmap;
/*     */   
/*  41 */   private List<Object> mainfield = Lists.newArrayList();
/*     */ 
/*     */ 
/*     */   
/*  45 */   private Map<String, Object> mainTableInfo = new HashMap<>();
/*     */   
/*  47 */   private Map<String, String> mainsettingInfo = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  52 */   private Map<String, Integer> commonParam = new HashMap<>();
/*     */ 
/*     */ 
/*     */   
/*  56 */   LinkedHashMap<String, Object> detailTableInfo = new LinkedHashMap<>();
/*     */   
/*     */   public GetInitDatasCmd(User paramUser, Map<String, Object> paramMap) {
/*  59 */     this.user = paramUser;
/*  60 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  65 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  70 */     return getInitInfo();
/*     */   }
/*     */   private Map<String, Object> getInitInfo() {
/*     */     String[] arrayOfString;
/*  74 */     RecordSet recordSet1 = new RecordSet();
/*  75 */     RecordSet recordSet2 = new RecordSet();
/*  76 */     FieldComInfo fieldComInfo = new FieldComInfo();
/*  77 */     RequestSecLevelBiz requestSecLevelBiz = new RequestSecLevelBiz();
/*  78 */     int i = Util.getIntValue(Util.null2String(this.params.get("wfid")), 0);
/*  79 */     int j = Util.getIntValue(Util.null2String(this.params.get("nodeid")), -1);
/*  80 */     int k = Util.getIntValue(Util.null2String(this.params.get("configid")), 0);
/*  81 */     boolean bool1 = FormTemplateManager.isFormVirtualNode(j);
/*  82 */     int m = Util.getIntValue(Util.null2String(this.params.get("requestid")), 0);
/*  83 */     String str1 = Util.null2String(this.params.get("fromwhere"));
/*  84 */     int n = j;
/*  85 */     if (k == 0 && m != 0) {
/*  86 */       k = DoSaveFreeExcelDesignBiz.getLayoutConfigId(m);
/*     */     }
/*  88 */     boolean bool2 = DoSaveFreeExcelDesignBiz.isNewFreeLayout(Util.null2String(Integer.valueOf(k)));
/*  89 */     boolean bool3 = false;
/*  90 */     boolean bool4 = FreeNodeBiz.isFreeFlow(i);
/*  91 */     int i1 = -1;
/*  92 */     int i2 = -1;
/*  93 */     int i3 = -1;
/*     */     
/*  95 */     String str2 = "", str3 = "";
/*  96 */     int i4 = 1;
/*  97 */     if (bool1) {
/*  98 */       i1 = Util.getIntValue(Util.null2String(this.params.get("formid")), -1);
/*  99 */       i2 = Util.getIntValue(Util.null2String(this.params.get("isbill")), -1);
/* 100 */       recordSet1.execute("select colsperrow from workflow_flownodehtml_form where formid=" + i1 + " and isbill=" + i2);
/* 101 */       if (recordSet1.next())
/* 102 */         i4 = Util.getIntValue(recordSet1.getString("colsperrow"), 1); 
/* 103 */     } else if (bool2) {
/* 104 */       DoSaveFreeExcelDesignBiz doSaveFreeExcelDesignBiz = new DoSaveFreeExcelDesignBiz(this.user);
/* 105 */       doSaveFreeExcelDesignBiz.getFreeNodeFormConfig(Util.null2String(Integer.valueOf(k)));
/* 106 */       i1 = doSaveFreeExcelDesignBiz.getFormid();
/* 107 */       i2 = doSaveFreeExcelDesignBiz.getIsbill();
/* 108 */       if (j == -1) {
/* 109 */         i3 = 0;
/* 110 */         j = Util.getIntValue(doSaveFreeExcelDesignBiz.getCreateNodeid(Util.null2String(Integer.valueOf(i))));
/*     */       } else {
/* 112 */         WFLinkInfo wFLinkInfo = new WFLinkInfo();
/* 113 */         i3 = WorkflowBaseBiz.getNodeInfo(j).getNodetype();
/* 114 */         recordSet1.executeQuery("select id from workflow_currentoperator where requestid = ? and nodeid = ? and  (isreject is null or isreject<>'1')", new Object[] { Integer.valueOf(m), Integer.valueOf(j) });
/* 115 */         if (recordSet1.next()) {
/* 116 */           int i6 = wFLinkInfo.getCurrentNodeidNew(m, this.user.getUID(), Util.getIntValue(this.user.getLogintype()));
/* 117 */           if (!wFLinkInfo.getNodeType(i6).equals("0")) {
/* 118 */             bool3 = true;
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } else {
/* 123 */       WFManager wFManager = new WFManager();
/* 124 */       wFManager.setWfid(i);
/*     */       try {
/* 126 */         wFManager.getWfInfo();
/* 127 */       } catch (Exception exception) {
/* 128 */         throw new ECException(getClass().getName() + "getWfInfo异常", exception);
/*     */       } 
/* 130 */       i1 = wFManager.getFormid();
/* 131 */       i2 = Util.getIntValue(wFManager.getIsBill(), 0);
/* 132 */       str2 = wFManager.getMessageType();
/* 133 */       str3 = wFManager.getChatsType();
/* 134 */       recordSet1.execute("select colsperrow from workflow_flownodehtml where workflowid=" + i + " and nodeid=" + j);
/* 135 */       if (recordSet1.next())
/* 136 */         i4 = Util.getIntValue(recordSet1.getString("colsperrow"), 1); 
/* 137 */       recordSet1.executeQuery("select nodetype from workflow_flownode where nodeid=" + j, new Object[0]);
/* 138 */       if (recordSet1.next()) {
/* 139 */         i3 = Util.getIntValue(recordSet1.getString("nodetype"), -1);
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 146 */     this.mainTableInfo.put("name", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()));
/* 147 */     this.mainTableInfo.put("key", "0");
/*     */     
/* 149 */     this.mainsettingInfo.put("label", SystemEnv.getHtmlLabelName(23692, this.user.getLanguage()));
/* 150 */     this.mainsettingInfo.put("type", "select");
/* 151 */     this.mainsettingInfo.put("value", i4 + "");
/* 152 */     this.mainTableInfo.put("attr", this.mainsettingInfo);
/*     */     
/* 154 */     OthersMethod othersMethod = new OthersMethod();
/*     */     
/* 156 */     if (!bool2) {
/* 157 */       othersMethod.initNodeFormDatas(j, i1, i2);
/*     */     } else {
/* 159 */       othersMethod.initFreeNodeFormDatas(j, i1, i2, k);
/*     */     } 
/*     */     
/* 162 */     if (bool2) {
/* 163 */       arrayOfString = FormTemplateManager.getFreeNodeFormParams(j, i1, i2, "t1", false, k);
/*     */     } else {
/* 165 */       arrayOfString = FormTemplateManager.getNodeFormParams(j, i1, i2, "t1", false);
/*     */     } 
/* 167 */     boolean bool5 = false;
/* 168 */     if (i2 == 1) {
/* 169 */       recordSet2.executeQuery("select tablename from workflow_bill where id = " + i1, new Object[0]);
/*     */       
/* 171 */       String str = Util.null2String(recordSet2.getString("tablename"));
/* 172 */       if (recordSet2.next() && (str.equals("formtable_main_" + (i1 * -1)) || str.startsWith("uf_"))) {
/* 173 */         bool5 = true;
/*     */       }
/*     */     } 
/* 176 */     StringBuilder stringBuilder = new StringBuilder();
/* 177 */     String str4 = "";
/* 178 */     if (bool2) {
/* 179 */       str4 = "t1.fieldid,t1.isview,t1.isedit,t1.ismandatory";
/*     */     } else {
/* 181 */       str4 = "t1.fieldid,t1.isview,t1.isedit,t1.ismandatory,t1.isonlyshow,t1.isalonerow";
/*     */     } 
/*     */     
/* 184 */     if (i2 == 0) {
/* 185 */       stringBuilder.append("select * from (select distinct " + str4 + ",t1.orderid,t3.fieldlable,");
/* 186 */       if ("ORACLE".equalsIgnoreCase(recordSet1.getDBType())) {
/* 187 */         stringBuilder.append(" nvl(t2.fieldorder,-1) as fieldorder,nvl(t2.isdetail,0) as viewtype,nvl(t2.groupid,-1) as groupid");
/* 188 */       } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 189 */         stringBuilder.append(" ifnull(t2.fieldorder,-1) as fieldorder,ifnull(t2.isdetail,0) as viewtype,ifnull(t2.groupid,-1) as groupid");
/*     */       } else {
/* 191 */         stringBuilder.append(" isnull(t2.fieldorder,-1) as fieldorder,isnull(t2.isdetail,0) as viewtype,isnull(t2.groupid,-1) as groupid");
/* 192 */       }  stringBuilder.append(" from " + arrayOfString[0] + " left join workflow_formfield t2 on t1.fieldid=t2.fieldid and t2.formid=" + i1);
/* 193 */       stringBuilder.append(" left join workflow_fieldlable t3 on t2.fieldid=t3.fieldid and t2.formid=t3.formid and t3.langurageid=" + this.user.getLanguage());
/* 194 */       stringBuilder.append(" where ").append(arrayOfString[1]).append(") a ");
/* 195 */       stringBuilder.append(" order by orderid,fieldid");
/* 196 */     } else if (i2 == 1) {
/* 197 */       stringBuilder.append("select * from (select distinct " + str4 + ",t1.orderid,t2.fieldname,t2.fieldlabel,t2.fieldhtmltype,t2.type,t2.fielddbtype, ");
/* 198 */       if ("ORACLE".equalsIgnoreCase(recordSet1.getDBType())) {
/* 199 */         stringBuilder.append(" nvl(t2.dsporder,-1) as dsporder,nvl(t2.viewtype,0) as viewtype,nvl(t2.detailtable,'') as detailtable");
/* 200 */       } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 201 */         stringBuilder.append(" ifnull(t2.dsporder,-1) as dsporder,ifnull(t2.viewtype,0) as viewtype,ifnull(t2.detailtable,'') as detailtable");
/*     */       } else {
/* 203 */         stringBuilder.append(" isnull(t2.dsporder,-1) as dsporder,isnull(t2.viewtype,0) as viewtype,isnull(t2.detailtable,'') as detailtable");
/* 204 */       }  stringBuilder.append(" from " + arrayOfString[0] + " left join workflow_billfield t2 on t1.fieldid=t2.id and t2.billid=" + i1);
/* 205 */       stringBuilder.append(" where ").append(arrayOfString[1]).append(") a ");
/* 206 */       stringBuilder.append(" order by orderid,fieldid");
/*     */     } 
/* 208 */     recordSet2.executeQuery(stringBuilder.toString(), new Object[0]);
/*     */     
/* 210 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 212 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 214 */     LinkedHashMap<String, Integer> linkedHashMap = getDetailindexs(i1, i2);
/*     */     
/* 216 */     for (String str : linkedHashMap.keySet()) {
/* 217 */       int i6 = ((Integer)linkedHashMap.get(str)).intValue();
/* 218 */       hashMap2.put(i6 + "", new ArrayList());
/* 219 */       hashMap1.put(i6 + "", new ArrayList());
/*     */     } 
/* 221 */     int i5 = -1;
/*     */     
/* 223 */     String str5 = "", str6 = "", str7 = "", str8 = "", str9 = "", str10 = "";
/*     */     
/* 225 */     List<Integer> list = getSysFieldList();
/*     */     
/* 227 */     boolean bool6 = (i3 == 3) ? true : false;
/* 228 */     while (recordSet2.next()) {
/* 229 */       String str11 = "0", str12 = "0", str13 = "0", str14 = "0", str15 = "0";
/* 230 */       this.fieldmap = new HashMap<>();
/* 231 */       int i6 = recordSet2.getInt("fieldid");
/*     */       
/* 233 */       if (bool4 && i1 == -1 && !list.contains(Integer.valueOf(i6))) {
/*     */         continue;
/*     */       }
/* 236 */       int i7 = Util.getIntValue(recordSet2.getString("isview"));
/* 237 */       int i8 = Util.getIntValue(recordSet2.getString("isedit"));
/* 238 */       int i9 = Util.getIntValue(recordSet2.getString("ismandatory"));
/* 239 */       int i10 = Util.getIntValue(recordSet2.getString("isonlyshow"));
/* 240 */       int i11 = Util.getIntValue(recordSet2.getString("isalonerow"));
/* 241 */       String str16 = (i8 == 1 && !bool6) ? "1" : "0";
/* 242 */       String str17 = (i9 == 1 && !bool6) ? "1" : "0";
/* 243 */       if (n == -1) {
/* 244 */         str16 = "0";
/* 245 */         str17 = "0";
/*     */       } 
/* 247 */       String str18 = (i10 == 1) ? "1" : "0";
/* 248 */       String str19 = recordSet2.getString("orderid");
/* 249 */       int i12 = recordSet2.getInt("viewtype");
/* 250 */       if (i2 == 0) {
/* 251 */         i5 = recordSet2.getInt("groupid");
/* 252 */         str6 = Util.null2String(recordSet2.getString("fieldlable"));
/* 253 */         str7 = fieldComInfo.getFieldname(i6 + "");
/* 254 */         str8 = fieldComInfo.getFieldhtmltype(i6 + "");
/* 255 */         str9 = fieldComInfo.getFielddbtype(i6 + "");
/* 256 */         str10 = fieldComInfo.getFieldType(i6 + "");
/* 257 */       } else if (i2 == 1) {
/* 258 */         str5 = Util.null2String(recordSet2.getString("detailtable"));
/* 259 */         str6 = SystemEnv.getHtmlLabelName(recordSet2.getInt("fieldlabel"), this.user.getLanguage());
/* 260 */         str7 = Util.null2String(recordSet2.getString("fieldname"));
/* 261 */         str8 = Util.null2String(recordSet2.getString("fieldhtmltype"));
/* 262 */         str9 = Util.null2String(recordSet2.getString("fielddbtype"));
/* 263 */         str10 = Util.null2String(recordSet2.getString("type"));
/*     */         
/* 265 */         if (i12 == 0) {
/* 266 */           i5 = -1;
/*     */         } else {
/* 268 */           i5 = ((Integer)linkedHashMap.get(str5)).intValue() - 1;
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 273 */       if (i6 == -1)
/* 274 */       { if (i7 == -1) i7 = 1; 
/* 275 */         if ("1".equals(str16)) {
/* 276 */           i7 = 1;
/* 277 */           str17 = "1";
/*     */         } 
/* 279 */         str6 = SystemEnv.getHtmlLabelName(21192, this.user.getLanguage()); }
/* 280 */       else if (i6 == -2)
/* 281 */       { if (i7 == -1) i7 = 1; 
/* 282 */         str6 = SystemEnv.getHtmlLabelName(15534, this.user.getLanguage()); }
/* 283 */       else { if (i6 == -3)
/*     */           continue; 
/* 285 */         if (i6 == -5)
/*     */           continue; 
/* 287 */         if (i6 == -4) {
/*     */           
/* 289 */           str11 = "1";
/* 290 */           str12 = "1";
/* 291 */           str13 = "1";
/* 292 */           str14 = "1";
/* 293 */           i11 = 1;
/* 294 */           str6 = SystemEnv.getHtmlLabelName(17614, this.user.getLanguage());
/* 295 */         } else if (i6 == -9) {
/* 296 */           str11 = "1";
/* 297 */           str12 = "1";
/* 298 */           str13 = "1";
/*     */           
/* 300 */           str6 = SystemEnv.getHtmlLabelName(22308, this.user.getLanguage());
/* 301 */         } else if (i6 == -10) {
/* 302 */           if (!HrmClassifiedProtectionBiz.isOpenClassification())
/*     */             continue; 
/* 304 */           if ("1".equals(str16)) {
/* 305 */             i7 = 1;
/* 306 */             str17 = "1";
/*     */           } 
/* 308 */           str6 = SystemEnv.getHtmlLabelName(500520, this.user.getLanguage());
/* 309 */           if (!requestSecLevelBiz.isCreateNode(j)) {
/* 310 */             str11 = "1";
/* 311 */             str12 = "1";
/* 312 */             str13 = "1";
/*     */           } 
/*     */         }  }
/*     */       
/* 316 */       this.fieldmap.put("id", i6 + "");
/* 317 */       this.fieldmap.put("orderid", str19 + "");
/* 318 */       this.fieldmap.put("fieldid", i6 + "");
/* 319 */       this.fieldmap.put("fieldShowName", str6);
/*     */       
/* 321 */       if (i12 == 0) {
/*     */         
/* 323 */         if ("7".equals(str8)) {
/* 324 */           str16 = "0";
/* 325 */           str17 = "0";
/* 326 */           str18 = "0";
/* 327 */           str11 = "1";
/* 328 */           str12 = "1";
/* 329 */           str13 = "1";
/*     */         } 
/*     */         
/* 332 */         if ("9".equals(str8)) {
/* 333 */           str17 = "0";
/* 334 */           str18 = "0";
/* 335 */           str12 = "1";
/* 336 */           str13 = "1";
/*     */         } 
/* 338 */         if (i6 != -10 && i6 != -9 && i6 != -4) {
/* 339 */           if (bool6) {
/* 340 */             str11 = "1";
/* 341 */             str12 = "1";
/* 342 */             str13 = "1";
/* 343 */           } else if (!"1".equals(str16)) {
/* 344 */             str13 = "1";
/*     */           } 
/*     */         }
/* 347 */         if (bool2 && bool3) {
/* 348 */           str15 = "1";
/* 349 */           this.fieldmap.put("isViewDisable", str15);
/* 350 */           str11 = "1";
/* 351 */           str12 = "1";
/*     */         } 
/* 353 */         this.fieldmap.put("isView", i7 + "");
/* 354 */         this.fieldmap.put("isEdit", str16);
/* 355 */         this.fieldmap.put("isMandatory", str17);
/* 356 */         this.fieldmap.put("onlyShowAttr", str18);
/* 357 */         this.fieldmap.put("isAlonerow", i11 + "");
/* 358 */         this.fieldmap.put("isEditDisable", str11);
/* 359 */         this.fieldmap.put("isMandatoryDisable", str12);
/* 360 */         this.fieldmap.put("onlyShowAttrDisable", str13);
/* 361 */         this.fieldmap.put("isAlonerowDisable", str14);
/* 362 */         this.mainfield.add(this.fieldmap); continue;
/*     */       } 
/* 364 */       if (!"1".equals(str16)) {
/* 365 */         str13 = "1";
/*     */       }
/* 367 */       if (bool6 || "manager".equals(str7)) {
/* 368 */         str16 = "0";
/* 369 */         str17 = "0";
/* 370 */         str12 = "1";
/* 371 */         if (!bool6) {
/* 372 */           str11 = "1";
/*     */         }
/*     */       } 
/* 375 */       if (bool2 && bool3) {
/* 376 */         str15 = "1";
/* 377 */         this.fieldmap.put("isViewDisable", str15);
/* 378 */         str11 = "1";
/* 379 */         str12 = "1";
/*     */       } 
/* 381 */       this.fieldmap.put("isView", i7 + "");
/* 382 */       this.fieldmap.put("isEdit", str16);
/* 383 */       this.fieldmap.put("isMandatory", str17);
/* 384 */       this.fieldmap.put("onlyShowAttr", str18);
/* 385 */       this.fieldmap.put("isEditDisable", str11);
/* 386 */       this.fieldmap.put("isMandatoryDisable", str12);
/* 387 */       this.fieldmap.put("onlyShowAttrDisable", str13);
/* 388 */       this.fieldmap.put("isAlonerowDisable", str14);
/*     */ 
/*     */       
/* 391 */       List<Map<String, String>> list1 = (List)hashMap2.get((i5 + 1) + "");
/* 392 */       if (list1 != null) {
/* 393 */         list1.add(this.fieldmap);
/*     */       }
/*     */       
/* 396 */       FieldInfo fieldInfo = new FieldInfo(i6);
/* 397 */       fieldInfo.setFieldname(str7);
/* 398 */       fieldInfo.setFieldlabel(str6);
/* 399 */       fieldInfo.setFielddbtype(str9);
/* 400 */       fieldInfo.setHtmltype(Util.getIntValue(str8));
/* 401 */       fieldInfo.setDetailtype(Util.getIntValue(str10));
/* 402 */       List<FieldInfo> list2 = (List)hashMap1.get((i5 + 1) + "");
/* 403 */       if (list2 != null) {
/* 404 */         list2.add(fieldInfo);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 409 */     DetailTableAttrBiz detailTableAttrBiz = new DetailTableAttrBiz(i, j, i1, i2, this.user);
/* 410 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */     
/* 412 */     if (bool2) {
/* 413 */       detailTableAttrBiz.loadFreeNodeDetailAttr(hashMap3, new HashMap<>(), k);
/*     */     } else {
/* 415 */       detailTableAttrBiz.loadDetailAttr(hashMap3, new HashMap<>());
/*     */     } 
/* 417 */     if (!bool2) {
/*     */       
/* 419 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 420 */       for (String str : hashMap3.keySet()) {
/* 421 */         int i6 = Util.getIntValue(str.substring(7), -1);
/* 422 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 423 */         hashMap.put("detail_" + i6, hashMap4);
/* 424 */         hashMap4.put("detailattr", hashMap3.get(str));
/* 425 */         hashMap4.put("fields", hashMap1.get(i6 + ""));
/*     */       } 
/* 427 */       detailTableAttrBiz.getAllDetailOrderInfo(hashMap);
/*     */     } 
/* 429 */     for (String str : linkedHashMap.keySet()) {
/* 430 */       int i6 = ((Integer)linkedHashMap.get(str)).intValue();
/* 431 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 432 */       hashMap.put("name", SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + "" + i6);
/* 433 */       hashMap.put("key", Integer.valueOf(i6));
/* 434 */       hashMap.put("groupid", Integer.valueOf(i6 - 1));
/*     */       
/* 436 */       hashMap.put("attr", hashMap3.get("detail_" + i6));
/*     */       
/* 438 */       hashMap.put("fields", hashMap2.get(i6 + ""));
/* 439 */       this.detailTableInfo.put("detail_" + i6, hashMap);
/*     */     } 
/*     */     
/* 442 */     this.mainTableInfo.put("fields", this.mainfield);
/* 443 */     this.commonParam.put("wfid", Integer.valueOf(i));
/* 444 */     this.commonParam.put("nodeid", Integer.valueOf(j));
/* 445 */     this.commonParam.put("formid", Integer.valueOf(i1));
/* 446 */     this.commonParam.put("isbill", Integer.valueOf(i2));
/* 447 */     this.commonParam.put("nodetype", Integer.valueOf(i3));
/* 448 */     this.apidatas.put("main", this.mainTableInfo);
/* 449 */     this.apidatas.put("details", this.detailTableInfo);
/* 450 */     this.apidatas.put("commonParam", this.commonParam);
/*     */     
/* 452 */     return this.apidatas;
/*     */   }
/*     */   
/*     */   private List<Integer> getSysFieldList() {
/* 456 */     ArrayList<Integer> arrayList = new ArrayList();
/* 457 */     int[] arrayOfInt = { -1, -2, -3, -4, -5, -9, -10 };
/* 458 */     for (int i : arrayOfInt) {
/* 459 */       arrayList.add(Integer.valueOf(i));
/*     */     }
/* 461 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private LinkedHashMap<String, Integer> getDetailindexs(int paramInt1, int paramInt2) {
/* 469 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 470 */     RecordSet recordSet = new RecordSet();
/* 471 */     String str = "";
/* 472 */     if (paramInt2 == 0) {
/* 473 */       str = "select distinct groupid as tablename,'' as title from workflow_formfield where formid=? and isdetail='1' order by groupid";
/* 474 */     } else if (paramInt2 == 1) {
/* 475 */       str = "select distinct tablename,orderid from workflow_billdetailtable where billid = ? order by orderid";
/* 476 */     }  recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt1) });
/* 477 */     byte b = 1;
/* 478 */     while (recordSet.next()) {
/* 479 */       String str1 = Util.null2String(recordSet.getString("tablename"));
/* 480 */       if (!"".equals(str1)) linkedHashMap.put(str1, Integer.valueOf(b)); 
/* 481 */       b++;
/*     */     } 
/* 483 */     return (LinkedHashMap)linkedHashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/GetInitDatasCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */