/*    */ package com.engine.workflow.cmd.excelDesign;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.excelDesign.FieldAttrBiz;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class FieldAttrCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>> {
/*    */   public FieldAttrCmd(User paramUser, Map<String, Object> paramMap) {
/* 15 */     this.user = paramUser;
/* 16 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 21 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 27 */     String str = Util.null2String(String.valueOf(this.params.get("operation")));
/* 28 */     FieldAttrBiz fieldAttrBiz = new FieldAttrBiz(this.user);
/* 29 */     if ("getdatasource".equals(str)) {
/* 30 */       hashMap.put("options", fieldAttrBiz.getDataSourceOption());
/* 31 */     } else if ("checkdatasource".equals(str)) {
/* 32 */       String str1 = Util.null2String(String.valueOf(this.params.get("datasourceid")));
/* 33 */       hashMap.put("datasourceid", str1);
/* 34 */       hashMap.put("result", fieldAttrBiz.datasourceCheck(str1));
/* 35 */     } else if ("checksql".equals(str)) {
/* 36 */       return fieldAttrBiz.checkSql(this.params);
/*    */     } 
/*    */     
/* 39 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/FieldAttrCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */