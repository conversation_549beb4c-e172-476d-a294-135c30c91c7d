/*     */ package com.engine.workflow.cmd.excelDesign;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.excelDesign.ShowModuleBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.exceldesign.FormTemplateManager;
/*     */ 
/*     */ 
/*     */ public class ShowModuleCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  23 */   private int workflowid = 0;
/*  24 */   private int nodeid = 0;
/*  25 */   private int formid = 0;
/*     */   private int isbill;
/*  27 */   private int layouttype = 0;
/*  28 */   private String nodename = "";
/*     */   private int pageIndex;
/*     */   private int pageSize;
/*     */   private boolean isFormLayout = false;
/*  32 */   private int showsql = 0;
/*     */ 
/*     */   
/*     */   public ShowModuleCmd(User paramUser, Map<String, Object> paramMap) {
/*  36 */     this.user = paramUser;
/*  37 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  42 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  47 */     initDatas();
/*     */     
/*  49 */     return getPageDatas(this.pageIndex, this.pageSize);
/*     */   }
/*     */   
/*     */   private void initDatas() {
/*  53 */     this.workflowid = Util.getIntValue(Util.null2String(this.params.get("workflowid")), -1);
/*  54 */     this.nodeid = Util.getIntValue(Util.null2String(this.params.get("nodeid")), -1);
/*  55 */     this.formid = Util.getIntValue(Util.null2String(this.params.get("formid")), -1);
/*  56 */     this.isbill = Util.getIntValue(Util.null2String(this.params.get("isbill")), -1);
/*  57 */     this.layouttype = Util.getIntValue(Util.null2String(this.params.get("templateType")), -1);
/*  58 */     this.nodename = Util.null2String(this.params.get("nodeName"));
/*  59 */     this.pageIndex = Util.getIntValue(Util.null2String(this.params.get("pageIndex")), 1);
/*  60 */     ShowModuleBiz showModuleBiz = new ShowModuleBiz();
/*  61 */     int i = showModuleBiz.getDefaultPageSize("Wf:workflow_showModule", this.user);
/*  62 */     this.pageSize = Util.getIntValue(Util.null2String(this.params.get("pageSize")), i);
/*  63 */     this.isFormLayout = FormTemplateManager.isFormVirtualNode(this.nodeid);
/*  64 */     this.showsql = Util.getIntValue(Util.null2String(this.params.get("showsql")), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> getPageDatas(int paramInt1, int paramInt2) {
/*  70 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  71 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  72 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/*  74 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*     */ 
/*     */       
/*  77 */       int i = 0;
/*  78 */       String str1 = "select count(1) as totalnum  from  workflow_flownode fn,workflow_nodebase nb where nb.id=fn.nodeid and fn.workflowid=? and (nb.IsFreeNode is null or nb.IsFreeNode!='1')";
/*  79 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(this.workflowid) });
/*  80 */       if (recordSet.next()) {
/*  81 */         i = recordSet.getInt("totalnum");
/*     */       }
/*     */       
/*  84 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */       
/*  87 */       ArrayList<String> arrayList1 = new ArrayList();
/*     */       
/*  89 */       if (this.isFormLayout) {
/*  90 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  91 */         ArrayList arrayList2 = new ArrayList();
/*  92 */         i = 1;
/*  93 */         hashMap.put("nodeId", Integer.valueOf(this.nodeid));
/*  94 */         hashMap.put("nodeName", "");
/*  95 */         hashMap.put("nodeType", Integer.valueOf(-1));
/*  96 */         hashMap.put("modes", arrayList2);
/*  97 */         arrayList.add(hashMap);
/*     */       } else {
/*  99 */         StringBuilder stringBuilder = new StringBuilder();
/* 100 */         String str4 = " fn.nodeorder asc,fn.nodetype ";
/* 101 */         String str5 = "fn.nodeid,nb.nodename,fn.nodetype,fn.nodeorder";
/* 102 */         String str6 = this.nodename.replaceAll("\\s*", "");
/* 103 */         String str7 = "";
/* 104 */         if (!str6.isEmpty()) {
/* 105 */           str7 = "and nb.nodename like '%" + str6 + "%'";
/*     */         }
/*     */         
/* 108 */         if (recordSet.getDBType().equalsIgnoreCase("sqlserver")) {
/* 109 */           stringBuilder.append("select top ").append(paramInt2).append(" * from (select row_number() over(order by ").append(str4).append(")as rownumber, ")
/* 110 */             .append(str5).append(" from  workflow_flownode fn,workflow_nodebase nb where nb.id=fn.nodeid and fn.workflowid= ? ").append(str7).append(" and (nb.IsFreeNode is null or nb.IsFreeNode!='1') ) temp_row")
/* 111 */             .append(" where  rownumber> ").append((paramInt1 - 1) * paramInt2);
/* 112 */         } else if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 113 */           stringBuilder.append("select * from (select rownum r,e.* from (select ").append(str5).append(" from ")
/* 114 */             .append(" workflow_flownode fn,workflow_nodebase nb where nb.id=fn.nodeid and fn.workflowid= ? ").append(str7).append(" and (nb.IsFreeNode is null or nb.IsFreeNode!='1')")
/* 115 */             .append(" order by ").append(str4).append(" )e where rownum <= ").append(paramInt1 * paramInt2).append(" )t where r>").append(paramInt2 * paramInt1 - paramInt2);
/*     */         }
/* 117 */         else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 118 */           stringBuilder.append("select ").append(str5)
/* 119 */             .append(" from  workflow_flownode fn,workflow_nodebase nb where nb.id=fn.nodeid   and fn.workflowid= ? ").append(str7).append(" and (nb.IsFreeNode is null or nb.IsFreeNode!='1') ")
/* 120 */             .append("order by ").append(str4).append(" limit ").append(paramInt2).append(" offset ").append((paramInt1 - 1) * paramInt2);
/*     */         } else {
/*     */           
/* 123 */           stringBuilder.append("select ").append(str5)
/* 124 */             .append(" from  workflow_flownode fn,workflow_nodebase nb where nb.id=fn.nodeid   and fn.workflowid= ? ").append(str7).append(" and (nb.IsFreeNode is null or nb.IsFreeNode!='1') ")
/* 125 */             .append("order by ").append(str4).append(" limit ").append((paramInt1 - 1) * paramInt2).append(",").append(paramInt2);
/*     */         } 
/* 127 */         if (1 == this.showsql) {
/* 128 */           hashMap2.put("" + SystemEnv.getHtmlLabelName(10005593, ThreadVarLanguage.getLang()) + "sql", stringBuilder.toString() + "" + SystemEnv.getHtmlLabelName(32393, ThreadVarLanguage.getLang()) + "workflowid = " + this.workflowid);
/*     */         }
/* 130 */         recordSet.executeQuery(stringBuilder.toString(), new Object[] { Integer.valueOf(this.workflowid) });
/* 131 */         while (recordSet.next()) {
/* 132 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 133 */           ArrayList arrayList2 = new ArrayList();
/* 134 */           int j = recordSet.getInt("nodeid");
/* 135 */           String str = Util.formatMultiLang(Util.null2String(recordSet.getString("nodename")), this.user.getLanguage() + "");
/* 136 */           int k = recordSet.getInt("nodetype");
/* 137 */           hashMap.put("nodeId", Integer.valueOf(j));
/* 138 */           hashMap.put("nodeName", str);
/* 139 */           hashMap.put("nodeType", Integer.valueOf(k));
/* 140 */           hashMap.put("modes", arrayList2);
/* 141 */           arrayList.add(hashMap);
/* 142 */           arrayList1.add(j + "");
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 147 */       String str2 = "";
/* 148 */       String str3 = "";
/* 149 */       if (this.isFormLayout) {
/* 150 */         if (1 == this.layouttype) {
/* 151 */           str3 = " and b.formid=" + this.formid + " and b.isbill=" + this.isbill + " and b.nodeid = " + this.nodeid;
/*     */         } else {
/* 153 */           str3 = " and formid=" + this.formid + " and isbill=" + this.isbill + " and nodeid = " + this.nodeid;
/*     */         } 
/* 155 */       } else if (1 == this.layouttype) {
/* 156 */         str3 = " and b.workflowid=" + this.workflowid + " and b.nodeid in(" + String.join(",", (Iterable)arrayList1) + ") ";
/*     */       } else {
/* 158 */         str3 = " and workflowid=" + this.workflowid + " and nodeid in(" + String.join(",", (Iterable)arrayList1) + ") ";
/*     */       } 
/*     */       
/* 161 */       if (1 == this.layouttype) {
/* 162 */         str2 = "select a.printenable,a.id as printsetid,b.* from workflow_printset a,workflow_nodehtmllayout b  where a.modeid = b.id and  b.type =? " + str3 + " and a.type=1 order by b.nodeid,b.isactive desc ";
/*     */       } else {
/* 164 */         str2 = "select * from workflow_nodehtmllayout  where  type =? " + str3 + " order by nodeid,isactive desc ";
/*     */       } 
/*     */       
/* 167 */       if (1 == this.showsql) {
/* 168 */         hashMap2.put("" + SystemEnv.getHtmlLabelName(64, ThreadVarLanguage.getLang()) + "sql", str2 + "" + SystemEnv.getHtmlLabelName(10005594, ThreadVarLanguage.getLang()) + "layouttype = " + this.layouttype);
/*     */       }
/* 170 */       recordSet.executeQuery(str2, new Object[] { Integer.valueOf(this.layouttype) });
/* 171 */       byte b = 0;
/* 172 */       while (recordSet.next() && b < 'Ϩ') {
/* 173 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 174 */         int j = recordSet.getInt("nodeid");
/* 175 */         int k = recordSet.getInt("id");
/* 176 */         String str4 = recordSet.getString("layoutname");
/* 177 */         int m = recordSet.getInt("isactive");
/* 178 */         int n = recordSet.getInt("operuser");
/* 179 */         String str5 = recordSet.getString("opertime");
/*     */         
/* 181 */         hashMap.put("layoutid", Integer.valueOf(k));
/* 182 */         hashMap.put("modeName", str4);
/* 183 */         hashMap.put("modeStatus", Integer.valueOf(m));
/* 184 */         hashMap.put("operUser", Util.formatMultiLang(resourceComInfo.getLastname(n + ""), this.user.getLanguage() + ""));
/* 185 */         hashMap.put("modefiedTime", str5);
/* 186 */         hashMap.put("formid", Integer.valueOf(recordSet.getInt("formid")));
/* 187 */         hashMap.put("isbill", Integer.valueOf(recordSet.getInt("isbill")));
/* 188 */         hashMap.put("layouttype", Integer.valueOf(recordSet.getInt("type")));
/* 189 */         if (1 == this.layouttype) {
/* 190 */           int i1 = recordSet.getInt("printenable");
/* 191 */           hashMap.put("enable", Integer.valueOf(i1));
/* 192 */           hashMap.put("id", Integer.valueOf(recordSet.getInt("printsetid")));
/*     */         } 
/*     */         
/* 195 */         for (Map<Object, Object> map : arrayList) {
/* 196 */           if (j == ((Integer)map.get("nodeId")).intValue()) {
/* 197 */             ((List<HashMap<Object, Object>>)map.get("modes")).add(hashMap);
/*     */           }
/*     */         } 
/* 200 */         b++;
/*     */       } 
/* 202 */       hashMap1.put("nodeList", arrayList);
/* 203 */       hashMap1.put("total", Integer.valueOf(i));
/* 204 */       hashMap1.put("defaultPageSize", Integer.valueOf(paramInt2));
/* 205 */       if (1 == this.showsql) {
/* 206 */         hashMap1.put("sql", hashMap2);
/*     */       }
/* 208 */     } catch (Exception exception) {
/* 209 */       exception.printStackTrace();
/*     */     } 
/* 211 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/ShowModuleCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */