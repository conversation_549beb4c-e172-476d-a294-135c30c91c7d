/*     */ package com.engine.workflow.cmd.excelDesign;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.excelDesign.DesignerFrameBiz;
/*     */ import com.engine.workflow.biz.excelDesign.FormatBiz;
/*     */ import com.engine.workflow.biz.excelDesign.NodeMarkBiz;
/*     */ import com.engine.workflow.biz.excelDesign.SystemFieldBiz;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.exceldesign.ExcelFormula;
/*     */ import weaver.workflow.exceldesign.FormTemplateManager;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ public class DoLoadDesignerCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private int wfid;
/*     */   private int nodeid;
/*     */   private int formid;
/*     */   private int isbill;
/*     */   private int layoutid;
/*     */   private int layouttype;
/*     */   private boolean isCreate = true;
/*     */   private boolean isFormLayout = false;
/*     */   
/*     */   public DoLoadDesignerCmd(User paramUser, Map<String, Object> paramMap) {
/*  34 */     this.user = paramUser;
/*  35 */     this.params = paramMap;
/*  36 */     this.wfid = Util.getIntValue((new StringBuilder()).append(paramMap.get("wfid")).append("").toString(), 0);
/*  37 */     this.nodeid = Util.getIntValue((new StringBuilder()).append(paramMap.get("nodeid")).append("").toString(), 0);
/*  38 */     this.formid = Util.getIntValue((new StringBuilder()).append(paramMap.get("formid")).append("").toString(), 0);
/*  39 */     this.isbill = Util.getIntValue((new StringBuilder()).append(paramMap.get("isbill")).append("").toString(), -1);
/*  40 */     this.layoutid = Util.getIntValue((new StringBuilder()).append(paramMap.get("layoutid")).append("").toString(), 0);
/*  41 */     this.layouttype = Util.getIntValue((new StringBuilder()).append(paramMap.get("layouttype")).append("").toString(), -1);
/*  42 */     this.isCreate = (this.layoutid <= 0);
/*  43 */     this.isFormLayout = FormTemplateManager.isFormVirtualNode(this.nodeid);
/*     */   }
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  47 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  48 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  49 */     hashMap2.put("wfid", Integer.valueOf(this.wfid));
/*  50 */     hashMap2.put("nodeid", Integer.valueOf(this.nodeid));
/*  51 */     hashMap2.put("formid", Integer.valueOf(this.formid));
/*  52 */     hashMap2.put("isbill", Integer.valueOf(this.isbill));
/*  53 */     hashMap2.put("layoutid", Integer.valueOf(this.layoutid));
/*  54 */     hashMap2.put("layouttype", Integer.valueOf(this.layouttype));
/*  55 */     hashMap2.put("isCreate", Boolean.valueOf(this.isCreate));
/*  56 */     hashMap2.put("isFormLayout", Boolean.valueOf(this.isFormLayout));
/*  57 */     RecordSet recordSet = new RecordSet();
/*  58 */     if (!this.isFormLayout) {
/*  59 */       recordSet.executeQuery("select nodeid,nodename,nodetype from workflow_nodebase nb,workflow_flownode fn where nb.id=fn.nodeid and nodeid=?", new Object[] { Integer.valueOf(this.nodeid) });
/*  60 */       if (recordSet.next()) {
/*  61 */         hashMap2.put("nodename", recordSet.getString("nodename"));
/*  62 */         hashMap2.put("nodetype", Integer.valueOf(Util.getIntValue(recordSet.getString("nodetype"))));
/*     */       } 
/*     */     } 
/*  65 */     hashMap2.put("formname", (new FormTemplateManager()).getFormName(this.formid, this.isbill, this.user.getLanguage()));
/*  66 */     hashMap2.put("workflowname", (new WorkflowComInfo()).getWorkflowname(this.wfid + ""));
/*  67 */     hashMap1.put("commonParam", hashMap2);
/*  68 */     DesignerFrameBiz designerFrameBiz = new DesignerFrameBiz(this.user);
/*  69 */     hashMap1.put("propConfig", designerFrameBiz.generatePropConfig());
/*  70 */     hashMap1.put("layoutInfo", getLayoutInfo((Map)hashMap2));
/*  71 */     hashMap1.put("nodeInfo", (new NodeMarkBiz(this.user)).generateNodeInfo(this.wfid, this.nodeid, this.layoutid, this.layouttype));
/*  72 */     if (!this.isFormLayout)
/*  73 */       hashMap1.put("formulaInfo", (new ExcelFormula()).getFormulaSetting(this.wfid, this.nodeid, this.formid, this.isbill, this.layoutid, this.layouttype, this.user)); 
/*  74 */     FormatBiz formatBiz = new FormatBiz();
/*  75 */     formatBiz.setUser(this.user);
/*  76 */     hashMap1.put("fontFamilyCfg", formatBiz.generateFontFamilyCfg());
/*  77 */     hashMap1.put("fontSizeCfg", formatBiz.generateFormSizeCfg());
/*  78 */     hashMap1.put("sysFieldInfo", (new SystemFieldBiz()).generateWfInfo(this.wfid));
/*  79 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   private Map<String, Object> getLayoutInfo(Map<String, Object> paramMap) {
/*  83 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  84 */     int i = 0;
/*  85 */     String str1 = "";
/*  86 */     RecordSet recordSet = new RecordSet();
/*  87 */     if (this.isCreate)
/*  88 */     { i = 1;
/*  89 */       str1 = this.isFormLayout ? (new StringBuilder()).append(paramMap.get("formname")).append("").toString() : (new StringBuilder()).append(paramMap.get("nodename")).append("").toString();
/*  90 */       if (this.layouttype == 0) {
/*  91 */         str1 = str1 + SystemEnv.getHtmlLabelName(16450, this.user.getLanguage());
/*  92 */       } else if (this.layouttype == 1) {
/*  93 */         str1 = str1 + SystemEnv.getHtmlLabelName(128952, this.user.getLanguage());
/*  94 */       } else if (this.layouttype == 2) {
/*  95 */         str1 = str1 + "Mobile" + SystemEnv.getHtmlLabelName(16450, this.user.getLanguage());
/*     */       }  }
/*  97 */     else { recordSet.executeQuery("select nodeid,layoutname,datajson,pluginjson,scripts,initscripts,isfixed,isactive from workflow_nodehtmllayout where id=?", new Object[] { Integer.valueOf(this.layoutid) });
/*  98 */       if (recordSet.next()) {
/*  99 */         i = Util.getIntValue(recordSet.getString("isactive"), 0);
/* 100 */         str1 = Util.null2String(recordSet.getString("layoutname"));
/*     */         
/* 102 */         String str4 = Util.null2String(recordSet.getString("datajson"));
/* 103 */         String str5 = Util.null2String(recordSet.getString("pluginjson"));
/* 104 */         String str6 = Util.null2String(recordSet.getString("scripts"));
/* 105 */         str4 = str4.replaceAll("微信提醒", "邮件提醒");
/* 106 */         str5 = str5.replaceAll("微信提醒", "邮件提醒");
/* 107 */         hashMap.put("datajson", DesignerFrameBiz.skipMultiLangFilter(str4));
/* 108 */         hashMap.put("pluginjson", str5);
/* 109 */         hashMap.put("scripts", str6);
/* 110 */         hashMap.put("initscripts", recordSet.getString("initscripts"));
/* 111 */         hashMap.put("isfixed", recordSet.getString("isfixed"));
/*     */         
/* 113 */         if (!this.isFormLayout && recordSet.getInt("nodeid") == FormTemplateManager.getFORMVIRTUALNODEID())
/* 114 */           paramMap.put("nodeEditFormLayout", Integer.valueOf(1)); 
/*     */       }  }
/*     */     
/* 117 */     hashMap.put("layoutname", str1);
/* 118 */     paramMap.put("isactive", Integer.valueOf(i));
/*     */     
/* 120 */     String str2 = "";
/* 121 */     String str3 = "select distinct layoutname from workflow_nodehtmllayout where workflowid=? and nodeid=? and formid=? and isbill=? and type=? and id<>?";
/* 122 */     recordSet.executeQuery(str3, new Object[] { Integer.valueOf(this.wfid), Integer.valueOf(this.nodeid), Integer.valueOf(this.formid), Integer.valueOf(this.isbill), Integer.valueOf(this.layouttype), Integer.valueOf(this.layoutid) });
/* 123 */     while (recordSet.next()) {
/* 124 */       str2 = str2 + "$[" + Util.null2String(recordSet.getString("layoutname")) + "]$";
/*     */     }
/* 126 */     paramMap.put("otherslayoutname", str2);
/* 127 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 133 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/DoLoadDesignerCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */