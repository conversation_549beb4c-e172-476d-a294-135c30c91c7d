/*     */ package com.engine.workflow.cmd.excelDesign;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.excelDesign.DetailTableAttrBiz;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.exceldesign.FormTemplateManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetDetailAttrCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  28 */   private Map<String, Object> commonParam = new HashMap<>();
/*     */   
/*     */   public GetDetailAttrCmd(User paramUser, Map<String, Object> paramMap) {
/*  31 */     this.user = paramUser;
/*  32 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  37 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  43 */     String str1 = Util.null2String(this.params.get("wfid"));
/*  44 */     String str2 = Util.null2String(this.params.get("nodeid"));
/*  45 */     String str3 = Util.null2String(this.params.get("formid"));
/*  46 */     String str4 = Util.null2String(this.params.get("isbill"));
/*  47 */     String str5 = Util.null2String(this.params.get("src"));
/*  48 */     int i = Util.getIntValue(Util.null2String(this.params.get("groupid")), -1);
/*  49 */     if ("getDetailMergeInfo".equals(str5)) {
/*  50 */       String str = getMergeInfo(Util.getIntValue(str2, -1), i, str3);
/*  51 */       hashMap.put("mergeFields", str);
/*  52 */     } else if ("getDetailOrderInfo".equals(str5)) {
/*  53 */       Map<String, Object> map = getDetailOrderInfo(str1, str2, str3, str4);
/*  54 */       hashMap.putAll(map);
/*  55 */       this.commonParam.put("wfid", str1);
/*  56 */       this.commonParam.put("nodeid", str2);
/*  57 */       this.commonParam.put("formid", str3);
/*  58 */       this.commonParam.put("isbill", str4);
/*  59 */       hashMap.put("commonParam", this.commonParam);
/*     */     } 
/*     */     
/*  62 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> getDetailOrderInfo(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  76 */     DetailTableAttrBiz detailTableAttrBiz = new DetailTableAttrBiz(Util.getIntValue(paramString1), Util.getIntValue(paramString2), Util.getIntValue(paramString3), Util.getIntValue(paramString4), this.user);
/*     */     
/*  78 */     int i = Util.getIntValue(Util.null2String(this.params.get("detailIndex")), -1);
/*     */     
/*  80 */     String str = Util.null2String(this.params.get("searchName"));
/*  81 */     if (i <= 0) {
/*  82 */       return (Map)hashMap;
/*     */     }
/*  84 */     List list = detailTableAttrBiz.getDetailField(i);
/*  85 */     Map<?, ?> map = detailTableAttrBiz.getDetailOrderInfo(list, i);
/*  86 */     hashMap.putAll(map);
/*  87 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getMergeInfo(int paramInt1, int paramInt2, String paramString) {
/*  98 */     String str = "";
/*  99 */     boolean bool = FormTemplateManager.isFormVirtualNode(paramInt1);
/* 100 */     RecordSet recordSet = new RecordSet();
/* 101 */     if (bool) {
/* 102 */       recordSet.executeQuery("select mergefields from workflow_nodeformgroup_form where formid=? and groupid=?", new Object[] { paramString, Integer.valueOf(paramInt2) });
/*     */     } else {
/* 104 */       if (paramInt1 <= 0 || paramInt2 < 0)
/* 105 */         return ""; 
/* 106 */       recordSet.executeQuery("select mergefields from workflow_nodeformgroup where nodeid=? and groupid=?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/*     */     } 
/* 108 */     if (recordSet.next())
/* 109 */       str = Util.null2String(recordSet.getString(1)); 
/* 110 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/GetDetailAttrCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */