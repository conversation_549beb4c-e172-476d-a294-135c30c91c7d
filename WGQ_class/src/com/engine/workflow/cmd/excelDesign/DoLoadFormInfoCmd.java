/*    */ package com.engine.workflow.cmd.excelDesign;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.excelDesign.GenerateFormInfoBiz;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoLoadFormInfoCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoLoadFormInfoCmd(User paramUser, Map<String, Object> paramMap) {
/* 21 */     this.user = paramUser;
/* 22 */     this.params = paramMap;
/*    */   }
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 26 */     int i = Util.getIntValue((new StringBuilder()).append(this.params.get("wfid")).append("").toString(), 0);
/* 27 */     int j = Util.getIntValue((new StringBuilder()).append(this.params.get("nodeid")).append("").toString(), 0);
/* 28 */     int k = Util.getIntValue((new StringBuilder()).append(this.params.get("formid")).append("").toString(), 0);
/* 29 */     int m = Util.getIntValue((new StringBuilder()).append(this.params.get("isbill")).append("").toString(), -1);
/* 30 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 31 */     hashMap1.put("needCalFieldAttr", Boolean.valueOf(true));
/* 32 */     hashMap1.put("needCalFieldSum", Boolean.valueOf(true));
/* 33 */     GenerateFormInfoBiz generateFormInfoBiz = new GenerateFormInfoBiz(k, m, this.user);
/* 34 */     generateFormInfoBiz.execute(i, j, hashMap1);
/*    */     
/* 36 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 37 */     hashMap2.put("formInfo", generateFormInfoBiz.getFormInfo());
/* 38 */     return (Map)hashMap2;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 43 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/DoLoadFormInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */