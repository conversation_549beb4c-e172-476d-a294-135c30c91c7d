/*    */ package com.engine.workflow.cmd.excelDesign;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.excelDesign.ExcelSecurityBiz;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ExcelSecurityCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public ExcelSecurityCmd(Map<String, Object> paramMap, User paramUser) {
/* 19 */     this.params = paramMap;
/* 20 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 25 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     ExcelSecurityBiz excelSecurityBiz = new ExcelSecurityBiz();
/* 32 */     String str1 = Util.null2String(this.params.get("content"));
/* 33 */     String str2 = "";
/* 34 */     if ("".equals(str1)) {
/* 35 */       hashMap.put("result", str2);
/* 36 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 39 */     if ("encode".equals(this.params.get("type"))) {
/* 40 */       str2 = excelSecurityBiz.encode(str1);
/* 41 */     } else if ("decode".equals(this.params.get("type"))) {
/* 42 */       str2 = excelSecurityBiz.decode(str1);
/*    */     } 
/* 44 */     hashMap.put("result", str2);
/* 45 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/ExcelSecurityCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */