/*    */ package com.engine.workflow.cmd.excelDesign;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.LinkedHashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.exceldesign.StyleManager;
/*    */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*    */ 
/*    */ 
/*    */ public class GetInitStyleCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetInitStyleCmd(User paramUser, Map<String, Object> paramMap) {
/* 19 */     this.user = paramUser;
/* 20 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 25 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     StyleManager styleManager = new StyleManager();
/* 32 */     Object[] arrayOfObject1 = new Object[0];
/* 33 */     Object[] arrayOfObject2 = new Object[0];
/* 34 */     int i = Util.getIntValue(Util.null2String(this.params.get("styleid")), 0);
/* 35 */     String str = Util.null2String(this.params.get("method"));
/*    */ 
/*    */     
/* 38 */     if (str.equals("searchone")) {
/* 39 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 40 */       if (i > 0) {
/* 41 */         arrayOfObject1 = new Object[1];
/* 42 */         Map map = styleManager.searchSingleStyle(i);
/* 43 */         arrayOfObject1[0] = map;
/*    */       }
/*    */     
/*    */     }
/* 47 */     else if (str.equals("searchsysone")) {
/* 48 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 49 */       if (i > 0) {
/* 50 */         arrayOfObject2 = new Object[1];
/* 51 */         Map map = styleManager.getSingleSysStyle(i);
/* 52 */         arrayOfObject2[0] = map;
/*    */       }
/*    */     
/*    */     }
/* 56 */     else if (str.equals("searchall")) {
/*    */       
/* 58 */       LinkedHashMap linkedHashMap1 = styleManager.getAllSysStyle();
/* 59 */       int j = 0;
/* 60 */       j = linkedHashMap1.keySet().size();
/* 61 */       arrayOfObject2 = new Object[j];
/* 62 */       byte b1 = 0;
/* 63 */       for (Object object : linkedHashMap1.keySet()) {
/* 64 */         arrayOfObject2[b1] = linkedHashMap1.get(object);
/* 65 */         b1++;
/*    */       } 
/*    */       
/* 68 */       LinkedHashMap linkedHashMap2 = styleManager.searchAllStyle();
/* 69 */       int k = 0;
/* 70 */       WorkflowConfigComInfo workflowConfigComInfo = new WorkflowConfigComInfo();
/* 71 */       int m = Util.getIntValue(workflowConfigComInfo.getValue("exceldesign_initstyle_limit"), 100);
/* 72 */       k = linkedHashMap2.keySet().size();
/* 73 */       if (k > m) {
/* 74 */         k = m;
/*    */       }
/* 76 */       arrayOfObject1 = new Object[k];
/* 77 */       byte b2 = 0;
/* 78 */       for (Object object : linkedHashMap2.keySet()) {
/* 79 */         if (b2 >= k) {
/*    */           break;
/*    */         }
/* 82 */         arrayOfObject1[b2] = linkedHashMap2.get(object);
/* 83 */         b2++;
/*    */       } 
/*    */     } 
/*    */     
/* 87 */     hashMap.put("cusStyle", arrayOfObject1);
/* 88 */     hashMap.put("sysStyle", arrayOfObject2);
/* 89 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/GetInitStyleCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */