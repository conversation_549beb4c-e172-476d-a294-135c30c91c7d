/*    */ package com.engine.workflow.cmd.excelDesign;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.excelDesign.ExcelSecurityBiz;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class InsertCodeCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public InsertCodeCmd(Map<String, Object> paramMap, User paramUser) {
/* 16 */     this.params = paramMap;
/* 17 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 22 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 27 */     String str1 = Util.null2String(this.params.get("encryptKey"));
/* 28 */     String str2 = Util.null2String(this.params.get("content"));
/*    */ 
/*    */     
/* 31 */     ExcelSecurityBiz excelSecurityBiz = new ExcelSecurityBiz();
/* 32 */     str2 = excelSecurityBiz.decryptAES(str2, str1);
/*    */ 
/*    */     
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 36 */     if (!str2.equals("aes_false")) {
/* 37 */       str2 = excelSecurityBiz.encode(str2);
/*    */     }
/*    */     
/* 40 */     hashMap.put("result", str2);
/* 41 */     hashMap.put("status", "success");
/*    */     
/* 43 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/excelDesign/InsertCodeCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */