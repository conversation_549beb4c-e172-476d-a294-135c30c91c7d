/*    */ package com.engine.workflow.cmd.userPhrase;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.TextUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.system.RequestDefaultComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetUserPhraseCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   private Map<String, Object> params;
/*    */   private User user;
/*    */   
/*    */   public GetUserPhraseCmd(Map<String, Object> paramMap, User paramUser) {
/* 25 */     this.params = paramMap;
/* 26 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */     
/*    */     try {
/* 36 */       int i = 0;
/* 37 */       RequestDefaultComInfo requestDefaultComInfo = new RequestDefaultComInfo();
/* 38 */       i = Util.getIntValue(requestDefaultComInfo.getPhraseOrderByFrequency(this.user.getUID() + ""));
/* 39 */       String str1 = "select a.id,a.hrmId,a.phraseShort,a.phraseDesc,a.groupid,a.dsporder,case when (a.groupid = '2' and b.status is null) then '1' when (a.groupid = '2' and b.status is not null) then b.status else a.status end as status,case when (a.groupid = '2' and b.frequency is null) then 0 when (a.groupid = '2') then b.frequency else a.frequency end as frequency from sysPhrase a left join PublicPhraseStatus b on a.id = b.id and b.hrmid=? where (a.hrmId=? or a.hrmid=-1) and ((a.status='1' and a.groupid='1') or (a.groupid='2' and (b.status is null or b.status='1'))) ";
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 44 */       String str2 = Util.null2String(this.params.get("phrase_id"));
/* 45 */       if (!"".equals(str2)) {
/* 46 */         str1 = str1 + " and a.id=" + str2;
/*    */       }
/*    */       
/* 49 */       if (i == 1) {
/* 50 */         str1 = str1 + " order by frequency desc,a.dsporder,a.id";
/*    */       } else {
/* 52 */         str1 = str1 + " order by a.dsporder,a.id";
/*    */       } 
/* 54 */       RecordSet recordSet = new RecordSet();
/* 55 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(this.user.getUID()), Integer.valueOf(this.user.getUID()) });
/* 56 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 57 */       boolean bool = HrmUserVarify.checkUserRight("PublicPhrase:all", this.user);
/* 58 */       while (recordSet.next()) {
/* 59 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 60 */         hashMap1.put("phrase_id", Util.null2String(recordSet.getString("id")));
/* 61 */         hashMap1.put("phrase_name", TextUtil.removeSpecialChar(Util.null2String(recordSet.getString("phraseshort"))));
/* 62 */         hashMap1.put("phrase_content", Util.toHtml(Util.null2String(recordSet.getString("phrasedesc"))));
/* 63 */         int j = Util.getIntValue(recordSet.getString("groupid"), 0);
/* 64 */         hashMap1.put("phrase_type", Integer.valueOf(j));
/* 65 */         hashMap1.put("phrase_status", Util.null2String(recordSet.getString("status")));
/* 66 */         if (j == 1) {
/* 67 */           hashMap1.put("right_type", Integer.valueOf(3));
/* 68 */         } else if (j == 2 && bool) {
/* 69 */           hashMap1.put("right_type", Integer.valueOf(3));
/*    */         } else {
/* 71 */           hashMap1.put("right_type", Integer.valueOf(1));
/*    */         } 
/* 73 */         hashMap1.put("showorder", Double.valueOf(Util.getDoubleValue(recordSet.getString("dsporder"), 0.0D)));
/* 74 */         arrayList.add(hashMap1);
/*    */       } 
/* 76 */       hashMap.put("errcode", Integer.valueOf(0));
/* 77 */       hashMap.put("errmsg", "ok");
/* 78 */       hashMap.put("phraselist", arrayList);
/* 79 */     } catch (Exception exception) {
/* 80 */       exception.printStackTrace();
/* 81 */       hashMap.put("errcode", Integer.valueOf(1));
/* 82 */       hashMap.put("errmsg", "catch exception : " + exception.getMessage());
/*    */     } 
/* 84 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 89 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/userPhrase/GetUserPhraseCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */