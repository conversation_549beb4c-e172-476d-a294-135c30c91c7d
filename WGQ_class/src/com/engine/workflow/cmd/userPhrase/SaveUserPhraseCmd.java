/*    */ package com.engine.workflow.cmd.userPhrase;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.net.URLDecoder;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.sysPhrase.WorkflowPhrase;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SaveUserPhraseCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   private Map<String, Object> params;
/*    */   private User user;
/*    */   
/*    */   public SaveUserPhraseCmd(Map<String, Object> paramMap, User paramUser) {
/* 25 */     this.params = paramMap;
/* 26 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     writeLog(this.params.toString());
/*    */     try {
/* 33 */       String str1 = Util.null2String(this.params.get("phrase_type"));
/* 34 */       String str2 = Util.null2String(this.params.get("phrase_id"));
/* 35 */       writeLog("后端接收到的参数：phrase_name：" + this.params.get("phrase_name") + ";phrase_content：" + this.params.get("phrase_content"));
/* 36 */       String str3 = URLDecoder.decode(URLDecoder.decode(Util.null2String((String)this.params.get("phrase_name")), "utf-8"), "utf-8");
/* 37 */       String str4 = URLDecoder.decode(URLDecoder.decode(Util.null2String((String)this.params.get("phrase_content")), "utf-8"), "utf-8");
/* 38 */       double d = Util.getDoubleValue(Util.null2String(this.params.get("showorder")), 0.0D);
/* 39 */       if ("".equals(str1)) {
/* 40 */         str1 = "1";
/*    */       }
/*    */       
/* 43 */       WorkflowPhrase workflowPhrase = new WorkflowPhrase();
/*    */       
/* 45 */       boolean bool = HrmUserVarify.checkUserRight("PublicPhrase:all", this.user);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 54 */       if ("2".equals(str1) && 
/* 55 */         !bool) {
/* 56 */         hashMap.put("errcode", Integer.valueOf(2));
/* 57 */         hashMap.put("errmsg", SystemEnv.getHtmlLabelName(2012, this.user.getLanguage()));
/* 58 */         return (Map)hashMap;
/*    */       } 
/*    */ 
/*    */       
/* 62 */       int i = this.user.getUID();
/* 63 */       if ("2".equals(str1)) {
/* 64 */         i = -1;
/*    */       }
/* 66 */       if ("".equals(str2)) {
/* 67 */         RecordSet recordSet = new RecordSet();
/* 68 */         recordSet.execute("insert into sysPhrase (hrmId,phraseShort,phraseDesc,groupid,dsporder,status,frequency) values(" + i + ",'" + str3 + "','" + str4 + "','" + str1 + "'," + d + ",'1',0)");
/*    */       } else {
/*    */         
/* 71 */         char c = Util.getSeparator();
/* 72 */         String str = str2 + c + i + c + str3 + c + str4;
/*    */         
/* 74 */         RecordSet recordSet1 = new RecordSet();
/* 75 */         recordSet1.executeProc("sysPhrase_update", str);
/*    */         
/* 77 */         RecordSet recordSet2 = new RecordSet();
/* 78 */         recordSet2.executeUpdate("update sysPhrase set groupid = '" + str1 + "',dsporder = " + d + " where id = " + str2, new Object[0]);
/*    */       } 
/* 80 */       hashMap.put("errcode", Integer.valueOf(0));
/* 81 */       hashMap.put("errmsg", "ok");
/* 82 */     } catch (Exception exception) {
/* 83 */       exception.printStackTrace();
/* 84 */       hashMap.put("errcode", Integer.valueOf(1));
/* 85 */       hashMap.put("errmsg", "catch exception : " + exception.getMessage());
/*    */     } 
/* 87 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 92 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/userPhrase/SaveUserPhraseCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */