/*    */ package com.engine.workflow.cmd.userPhrase;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class DelUserPhraseCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   private Map<String, Object> params;
/*    */   private User user;
/*    */   
/*    */   public DelUserPhraseCmd(Map<String, Object> paramMap, User paramUser) {
/* 22 */     this.params = paramMap;
/* 23 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/*    */     try {
/* 30 */       boolean bool = true;
/* 31 */       boolean bool1 = HrmUserVarify.checkUserRight("PublicPhrase:all", this.user);
/*    */       
/* 33 */       if (!bool && !bool1) {
/* 34 */         hashMap.put("errcode", Integer.valueOf(2));
/* 35 */         hashMap.put("errmsg", SystemEnv.getHtmlLabelName(2012, this.user.getLanguage()));
/* 36 */         return (Map)hashMap;
/*    */       } 
/*    */       
/* 39 */       String str1 = Util.null2String(this.params.get("phrase_ids"));
/* 40 */       String str2 = "delete from sysPhrase where id in (" + str1 + ") and groupid=1 and hrmid = ?";
/* 41 */       String str3 = "";
/* 42 */       if (bool1) {
/* 43 */         str2 = "delete from sysPhrase where id in (" + str1 + ") and ((groupid=1 and hrmid = ?) or groupid =2) ";
/*    */         
/* 45 */         str3 = "delete from PublicPhraseStatus where " + Util.getSubINClause(str1, "id", "in");
/*    */       } 
/* 47 */       RecordSet recordSet = new RecordSet();
/* 48 */       recordSet.executeUpdate(str2, new Object[] { Integer.valueOf(this.user.getUID()) });
/* 49 */       if (!"".equals(str3)) {
/* 50 */         recordSet.executeUpdate(str3, new Object[0]);
/*    */       }
/* 52 */       hashMap.put("errcode", Integer.valueOf(0));
/* 53 */       hashMap.put("errmsg", "ok");
/* 54 */     } catch (Exception exception) {
/* 55 */       exception.printStackTrace();
/* 56 */       hashMap.put("errcode", Integer.valueOf(1));
/* 57 */       hashMap.put("errmsg", "catch exception : " + exception.getMessage());
/*    */     } 
/* 59 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 64 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/userPhrase/DelUserPhraseCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */