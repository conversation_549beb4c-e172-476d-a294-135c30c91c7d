/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import com.engine.workflow.entity.subRequestLog.LogInfoEnum;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.ruleDesign.RuleBusiness;
/*     */ import weaver.workflow.workflow.WFManager;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetTriDetailCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean detachable = false;
/*     */   private boolean sanyuanDetachable = false;
/*     */   private Map<String, Object> params;
/*     */   private User user;
/*     */   
/*     */   public GetTriDetailCmd(Map<String, Object> paramMap, User paramUser) {
/*  56 */     this.params = paramMap;
/*  57 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  63 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  64 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  65 */     this.sanyuanDetachable = HrmSanyuanAdminBiz.getSanyuanAble();
/*  66 */     this.detachable = manageDetachComInfo.isUseManageDetach();
/*  67 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user) || (isAdmin(this.user) && this.sanyuanDetachable)) {
/*  68 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  69 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/*  72 */       String str1 = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/*  73 */       if (str1.length() <= 0) {
/*  74 */         hashMap.put("hasRight", Boolean.valueOf(false));
/*  75 */         return (Map)hashMap;
/*     */       } 
/*  77 */     } catch (Exception exception) {
/*  78 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  81 */     ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*  82 */     ArrayList<Map<String, String>> arrayList2 = new ArrayList();
/*  83 */     String str = Util.null2String(this.params.get("subReqLogMainId"));
/*  84 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  86 */     recordSet.executeQuery("select * from wf_subReq_log where id = ?", new Object[] { str });
/*  87 */     if (recordSet.next()) {
/*  88 */       RecordSet recordSet1 = new RecordSet();
/*  89 */       int i = Util.getIntValue(recordSet.getString("isDiff"), 0);
/*  90 */       int j = recordSet.getInt("subwfSetId");
/*  91 */       int k = recordSet.getInt("mainReqId");
/*  92 */       recordSet1.executeQuery("select workflowid from workflow_requestbase where requestid= ?", new Object[] { Integer.valueOf(k) });
/*  93 */       recordSet1.next();
/*  94 */       String str1 = recordSet1.getString("workflowid");
/*     */ 
/*     */       
/*  97 */       WFManager wFManager = new WFManager();
/*  98 */       wFManager.setWfid(Util.getIntValue(str1));
/*     */       try {
/* 100 */         wFManager.getWfInfo();
/* 101 */       } catch (Exception exception) {
/* 102 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 105 */       String str2 = String.valueOf(wFManager.getFormid());
/* 106 */       String str3 = wFManager.getIsBill();
/*     */       
/* 108 */       Map<String, String> map = getNodeNameMap(str1);
/*     */       
/* 110 */       String str4 = recordSet.getString("subworkflowid");
/* 111 */       String str5 = recordSet.getString("triggerSource");
/* 112 */       String str6 = recordSet.getString("triggerSourceType");
/* 113 */       String str7 = recordSet.getString("triggerSourceOrder");
/* 114 */       String str8 = recordSet.getString("triggerType");
/* 115 */       String str9 = recordSet.getString("mainReqNodeId");
/* 116 */       String str10 = recordSet.getString("mainReqNodeTime");
/* 117 */       String str11 = recordSet.getString("subwfcreatorType");
/* 118 */       String str12 = recordSet.getString("subwfcreatorFieldId");
/*     */       
/* 120 */       String str13 = null;
/* 121 */       if (i == 0) {
/* 122 */         str13 = SystemEnv.getHtmlLabelName(533608, this.user.getLanguage());
/*     */       } else {
/* 124 */         str13 = SystemEnv.getHtmlLabelName(533609, this.user.getLanguage());
/*     */       } 
/*     */       
/* 127 */       arrayList1.add(getDetailInfo(533602, str13));
/*     */       
/* 129 */       StringBuffer stringBuffer = new StringBuffer();
/* 130 */       if (!Strings.isNullOrEmpty(str4)) {
/* 131 */         String[] arrayOfString = str4.split(",");
/* 132 */         WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 133 */         stringBuffer.append(workflowComInfo.getWorkflowname(arrayOfString[0]));
/*     */         
/* 135 */         byte b1 = 3;
/* 136 */         int m = arrayOfString.length;
/* 137 */         for (byte b2 = 1; b2 < m && b2 < b1; b2++) {
/* 138 */           stringBuffer.append("，").append(workflowComInfo.getWorkflowname(arrayOfString[b2]));
/*     */         }
/* 140 */         if (m > b1) {
/* 141 */           stringBuffer.append("...").append("（").append(SystemEnv.getHtmlLabelName(18609, this.user.getLanguage()))
/* 142 */             .append(m).append(SystemEnv.getHtmlLabelName(18256, this.user.getLanguage())).append("）");
/*     */         }
/*     */       } 
/*     */       
/* 146 */       arrayList1.add(getDetailInfo(533604, stringBuffer.toString()));
/*     */ 
/*     */       
/* 149 */       String str14 = null;
/* 150 */       if (str6.equals("main")) {
/* 151 */         str14 = SystemEnv.getHtmlLabelName(21778, this.user.getLanguage());
/* 152 */       } else if (str6.equals("detail")) {
/* 153 */         str14 = SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + str7;
/*     */       } 
/* 155 */       arrayList1.add(getDetailInfo(33383, str14));
/*     */ 
/*     */       
/* 158 */       String str15 = null;
/* 159 */       if ("1".equals(str8)) {
/* 160 */         str15 = SystemEnv.getHtmlLabelName(22051, this.user.getLanguage());
/* 161 */       } else if ("2".equals(str8)) {
/* 162 */         str15 = SystemEnv.getHtmlLabelName(22052, this.user.getLanguage());
/*     */       } 
/* 164 */       arrayList1.add(getDetailInfo(22050, str15));
/*     */ 
/*     */       
/* 167 */       arrayList1.add(getDetailInfo(19346, map.get(str9)));
/* 168 */       if ("1".equals(str8)) {
/* 169 */         String str17 = SystemEnv.getHtmlLabelName(19349, this.user.getLanguage());
/* 170 */         if ("1".equals(str10)) {
/* 171 */           str17 = SystemEnv.getHtmlLabelName(19348, this.user.getLanguage());
/*     */         }
/* 173 */         arrayList1.add(getDetailInfo(19347, str17));
/*     */       } 
/*     */ 
/*     */       
/* 177 */       String str16 = (String)RuleBusiness.getRuleInfoByRIds(7, j + "").get("ruledescs");
/* 178 */       if (i == 1) {
/* 179 */         str16 = RuleBusiness.getConditionCn(j, 8, this.user);
/*     */       }
/*     */       
/* 182 */       arrayList1.add(getDetailInfo(33384, Strings.isNullOrEmpty(str16) ? ("（" + SystemEnv.getHtmlLabelName(83230, this.user.getLanguage()) + "）") : str16));
/*     */ 
/*     */       
/* 185 */       arrayList1.add(getDetailInfo(19352, getCreatorInfo(str3, str2, str5, str6, str11, str12)));
/*     */       
/* 187 */       recordSet.executeQuery("select logtype from wf_subReq_detail_log where mainLogId = ? and logtype <>1", new Object[] { str });
/* 188 */       while (recordSet.next()) {
/* 189 */         int m = recordSet.getInt("logtype");
/* 190 */         arrayList2.add(getDetailInfo(517195, LogInfoEnum.getDesc(m)));
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 195 */     hashMap.put("hasRight", Boolean.valueOf(true));
/* 196 */     hashMap.put("detailInfo", arrayList1);
/* 197 */     hashMap.put("failInfo", arrayList2);
/* 198 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public boolean isAdmin(User paramUser) {
/* 202 */     boolean bool = false;
/* 203 */     RecordSet recordSet = new RecordSet();
/* 204 */     recordSet.execute("select count(*) from hrmresourcemanager where id=" + paramUser.getUID());
/* 205 */     if (recordSet.next() && 
/* 206 */       recordSet.getInt(1) > 0) {
/* 207 */       bool = true;
/*     */     }
/*     */     
/* 210 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 215 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/* 220 */     return super.getLogContexts();
/*     */   }
/*     */   
/*     */   private Map<String, String> getDetailInfo(int paramInt, String paramString) {
/* 224 */     String str = SystemEnv.getHtmlLabelName(paramInt, this.user.getLanguage());
/* 225 */     if (!Strings.isNullOrEmpty(str)) str = str + "："; 
/* 226 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 227 */     hashMap.put("label", str);
/* 228 */     hashMap.put("info", paramString);
/* 229 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private String getCreatorInfo(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 233 */     RecordSet recordSet = new RecordSet();
/* 234 */     String str1 = null;
/* 235 */     if (paramString1.equals("0")) {
/* 236 */       str1 = "select a.id as id,c.fieldlable as name,a.fieldhtmltype htmltype,a.type as type from workflow_formdict a,workflow_formfield b,workflow_fieldlable c where  c.isdefault='1' and c.formid = b.formid  and c.fieldid = b.fieldid and  b.fieldid= a.id and a.fieldhtmltype='3' and (a.type = 1 or a.type=17 or a.type=141 or a.type=142 or a.type=166 or a.type=165  or a.type=160) and (b.isdetail<>'1' or b.isdetail is null) and b.formid=" + paramString2 + " a.id = ? ";
/*     */ 
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */       
/* 243 */       str1 = "select id as id , fieldlabel as name,fieldhtmltype htmltype,type as type from workflow_billfield where (viewtype is null or viewtype<>1) and billid=" + paramString2 + " and fieldhtmltype = '3' and (type=1 or type=17 or type=141 or type=142 or type=166 or type=165 or type=160) and id= ?";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 251 */     if (paramString4.equals("detail")) {
/* 252 */       if (paramString1.equals("0")) {
/* 253 */         str1 = "select a.id as id,c.fieldlable as name,a.fieldhtmltype htmltype,a.type as type from workflow_formdictdetail a,workflow_formfield b,workflow_fieldlable c where  c.isdefault='1' and c.formid = b.formid  and c.fieldid = b.fieldid and  b.fieldid= a.id and a.fieldhtmltype='3' and (a.type = 1 or a.type=17 or a.type=141 or a.type=142 or a.type=166 or a.type=165  or a.type=160) and b.isdetail = 1 and b.groupid=" + paramString3 + " and b.formid=" + paramString2 + " and a.id = ?";
/*     */ 
/*     */       
/*     */       }
/*     */       else {
/*     */ 
/*     */         
/* 260 */         str1 = "select id as id , fieldlabel as name,fieldhtmltype htmltype,type as type from workflow_billfield where viewtype = 1 and billid=" + paramString2 + " and fieldhtmltype = '3' and (type=1 or type=17 or type=141 or type=142 or type=166 or type=165 or type=160) and detailtable = (select tablename from Workflow_billdetailtable where id=" + paramString3 + ") and id = ?";
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 267 */     recordSet.executeQuery(str1, new Object[] { paramString6 });
/* 268 */     recordSet.next();
/* 269 */     String str2 = "";
/* 270 */     if (paramString1.equals("0")) {
/* 271 */       str2 = recordSet.getString("name");
/*     */     } else {
/* 273 */       str2 = SystemEnv.getHtmlLabelName(Util.getIntValue(recordSet.getString("name")), this.user.getLanguage());
/*     */     } 
/* 275 */     String str3 = null;
/* 276 */     switch (paramString5) {
/*     */       case "1":
/* 278 */         str3 = SystemEnv.getHtmlLabelName(19353, this.user.getLanguage());
/*     */         break;
/*     */       case "2":
/* 281 */         str3 = SystemEnv.getHtmlLabelName(19354, this.user.getLanguage());
/*     */         break;
/*     */       case "3":
/* 284 */         str3 = SystemEnv.getHtmlLabelName(19355, this.user.getLanguage());
/* 285 */         str3 = str3 + "：" + str2;
/*     */         break;
/*     */     } 
/* 288 */     return str3;
/*     */   }
/*     */   
/*     */   private Map<String, String> getNodeNameMap(String paramString) {
/* 292 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 294 */     String str1 = "";
/* 295 */     String str2 = "";
/* 296 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 297 */     recordSet.executeQuery(" select b.id as nodeId,b.nodeName from workflow_flownode a,workflow_nodebase b where (b.IsFreeNode is null or b.IsFreeNode!='1') and a.nodeId=b.id and a.workFlowId= " + paramString, new Object[0]);
/* 298 */     while (recordSet.next()) {
/* 299 */       str1 = recordSet.getString("nodeId");
/* 300 */       str2 = recordSet.getString("nodeName");
/* 301 */       hashMap.put(str1, str2);
/*     */     } 
/* 303 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetTriDetailCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */