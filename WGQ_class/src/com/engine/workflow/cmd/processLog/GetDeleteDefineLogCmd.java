/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.hrm.bean.HrmFieldBean;
/*     */ import com.api.hrm.util.HrmFieldSearchConditionComInfo;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import com.engine.systeminfo.biz.log.LogRight;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetDeleteDefineLogCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean detachable = false;
/*     */   private boolean sanyuanDetachable = false;
/*     */   private Map<String, Object> params;
/*     */   private User user;
/*     */   
/*     */   public GetDeleteDefineLogCmd(Map<String, Object> paramMap, User paramUser) {
/*  53 */     this.params = paramMap;
/*  54 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  62 */     String str = "";
/*  63 */     this.sanyuanDetachable = HrmSanyuanAdminBiz.getSanyuanAble();
/*  64 */     this.detachable = manageDetachComInfo.isUseManageDetach();
/*  65 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user) || (isAdmin(this.user) && this.sanyuanDetachable)) {
/*  66 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  67 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/*  70 */       str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/*  71 */       if (str.length() <= 0) {
/*  72 */         hashMap.put("hasRight", Boolean.valueOf(false));
/*  73 */         return (Map)hashMap;
/*     */       } 
/*  75 */     } catch (Exception exception) {
/*  76 */       exception.printStackTrace();
/*     */     } 
/*  78 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  79 */     hashMap.put("tableSession", getTableSession());
/*  80 */     hashMap.put("conditions", getCondition(false));
/*  81 */     hashMap.put("formFields", getCondition(true));
/*  82 */     hashMap.put("topTitle", SystemEnv.getHtmlLabelName(33778, this.user.getLanguage()));
/*  83 */     hashMap.put("topTab", getTopTab());
/*  84 */     hashMap.put("detachable", Boolean.valueOf(this.detachable));
/*  85 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getCondition(boolean paramBoolean) {
/*     */     SearchConditionItem searchConditionItem3;
/*  94 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  97 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/*  99 */     String str = SystemEnv.getHtmlLabelName(10000903, this.user.getLanguage());
/* 100 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestname");
/* 101 */     searchConditionItem1.setLabelcol(6);
/* 102 */     searchConditionItem1.setFieldcol(16);
/*     */     
/* 104 */     str = SystemEnv.getHtmlLabelName(125749, this.user.getLanguage());
/* 105 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "workflowname", "-99991");
/* 106 */     searchConditionItem2.setLabelcol(6);
/* 107 */     searchConditionItem2.setFieldcol(16);
/* 108 */     searchConditionItem2.setLabel(str);
/*     */ 
/*     */     
/* 111 */     XssUtil xssUtil = new XssUtil();
/* 112 */     if (isAdmin(this.user)) {
/* 113 */       HrmFieldSearchConditionComInfo hrmFieldSearchConditionComInfo = new HrmFieldSearchConditionComInfo();
/* 114 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 115 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(513623, this.user.getLanguage()), true));
/* 116 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(513482, this.user.getLanguage()), false));
/* 117 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 99, "operator", arrayList2);
/* 118 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 119 */       SearchConditionItem searchConditionItem7 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "hrmmanagerids", "adminAccount");
/* 120 */       searchConditionItem7.getBrowserConditionParam().setIsSingle(true);
/*     */       
/* 122 */       HrmFieldBean hrmFieldBean = new HrmFieldBean("hrmResourceId", "384077", "3", "1", true);
/* 123 */       SearchConditionItem searchConditionItem8 = hrmFieldSearchConditionComInfo.getSearchConditionItem(hrmFieldBean, this.user);
/*     */       
/* 125 */       searchConditionItem8.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 126 */       searchConditionItem8.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 127 */       searchConditionItem8.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 128 */       hashMap1.put("0", searchConditionItem8);
/* 129 */       hashMap1.put("1", searchConditionItem7);
/* 130 */       searchConditionItem3.setSelectLinkageDatas(hashMap1);
/* 131 */       searchConditionItem3.setLabelcol(6);
/* 132 */       searchConditionItem3.setFieldcol(16);
/*     */     } else {
/* 134 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, 99, "hrmids", "1");
/* 135 */       searchConditionItem3.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 136 */       searchConditionItem3.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 137 */       searchConditionItem3.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 138 */       searchConditionItem3.setLabelcol(6);
/* 139 */       searchConditionItem3.setFieldcol(16);
/*     */     } 
/* 141 */     str = SystemEnv.getHtmlLabelName(503282, Util.getIntValue(this.user.getLanguage()));
/* 142 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(str, ConditionType.DATE, "dateselect");
/* 143 */     searchConditionItem4.setLabelcol(6);
/* 144 */     searchConditionItem4.setFieldcol(16);
/* 145 */     searchConditionItem4.setOptions(getDateOptions());
/*     */     
/* 147 */     str = SystemEnv.getHtmlLabelName(33569, this.user.getLanguage()) + "id";
/* 148 */     SearchConditionItem searchConditionItem5 = conditionFactory.createCondition(str, ConditionType.INPUT, "workflowid");
/* 149 */     searchConditionItem5.setLabelcol(6);
/* 150 */     searchConditionItem5.setFieldcol(16);
/*     */     
/* 152 */     SearchConditionItem searchConditionItem6 = conditionFactory.createCondition(ConditionType.SELECT, 15503, "operatetype");
/* 153 */     searchConditionItem6.setLabelcol(6);
/* 154 */     searchConditionItem6.setFieldcol(16);
/*     */     
/* 156 */     arrayList.add(searchConditionItem1);
/* 157 */     arrayList.add(searchConditionItem2);
/* 158 */     arrayList.add(searchConditionItem3);
/* 159 */     arrayList.add(searchConditionItem4);
/* 160 */     arrayList.add(searchConditionItem5);
/* 161 */     if (paramBoolean) {
/* 162 */       arrayList.add(searchConditionItem6);
/*     */     }
/*     */ 
/*     */     
/* 166 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 167 */     hashMap.put("title", SystemEnv.getHtmlLabelName(33776, this.user.getLanguage()));
/* 168 */     hashMap.put("items", arrayList);
/* 169 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/*     */ 
/*     */     
/* 172 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 173 */     arrayList1.add(hashMap);
/*     */     
/* 175 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getTableSession() {
/* 183 */     String str1 = Util.null2String(this.params.get("subCompanyId"));
/* 184 */     String str2 = Util.null2String(this.params.get("workflowname"));
/* 185 */     String str3 = Util.null2String(this.params.get("requestname"));
/* 186 */     String str4 = Util.null2String(this.params.get("workflowid"));
/* 187 */     String str5 = Util.null2String(this.params.get("operator"));
/* 188 */     if (str5.equals("0")) {
/* 189 */       str5 = Util.null2String(this.params.get("hrmResourceId"));
/* 190 */     } else if (str5.equals("1")) {
/* 191 */       str5 = Util.null2String(this.params.get("hrmmanagerids"));
/*     */     } else {
/* 193 */       str5 = Util.null2String(this.params.get("hrmids"));
/*     */     } 
/*     */     
/* 196 */     String str6 = Util.null2String(this.params.get("fromdate"));
/* 197 */     String str7 = Util.null2String(this.params.get("todate"));
/* 198 */     String str8 = Util.fromScreen(Util.null2String(this.params.get("dateselect")), this.user.getLanguage());
/* 199 */     if (!Strings.isNullOrEmpty(str8)) {
/* 200 */       String[] arrayOfString = str8.split(",");
/* 201 */       String str = arrayOfString[0];
/* 202 */       if ("".equals(str) || "0".equals(str)) {
/* 203 */         str6 = "";
/* 204 */         str7 = "";
/* 205 */       } else if (!str.equals("6")) {
/* 206 */         str6 = TimeUtil.getDateByOption(str, "0");
/* 207 */         str7 = TimeUtil.getDateByOption(str, "1");
/* 208 */       } else if ("6".equals(str)) {
/* 209 */         str6 = (arrayOfString.length >= 2) ? arrayOfString[1] : str6;
/* 210 */         str7 = (arrayOfString.length >= 3) ? arrayOfString[2] : str7;
/*     */       } 
/*     */     } 
/* 213 */     String str9 = "-1";
/*     */ 
/*     */     
/* 216 */     String str10 = " t1.request_id,t1.request_name,t1.operate_userid,t1.operate_date,t1.operate_time,t1.workflow_id,t1.client_address";
/* 217 */     String str11 = " workflow_requestdeletelog t1,workflow_base t2";
/* 218 */     String str12 = " t1.workflow_id=t2.id and t1.operate_userid = h.id ";
/*     */     
/* 220 */     if (this.sanyuanDetachable && isAdmin(this.user)) {
/* 221 */       str12 = str12 + "and t1.operate_userid = " + this.user.getUID();
/* 222 */       str11 = str11 + ",(select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     }
/* 224 */     else if (!this.detachable && this.user.getUID() != 1 && !isAdmin(this.user)) {
/* 225 */       str11 = str11 + ",(select id,lastname,subcompanyid1 from HrmResource) h ";
/*     */     } else {
/* 227 */       str11 = str11 + ",(select id,lastname,subcompanyid1 from HrmResource union all select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     } 
/*     */ 
/*     */     
/* 231 */     if (!Strings.isNullOrEmpty(str3)) {
/* 232 */       str12 = str12 + " AND t1.request_name LIKE '%" + str3 + "%'";
/*     */     }
/* 234 */     if (!Strings.isNullOrEmpty(str2)) {
/* 235 */       str9 = WorkflowVersion.getAllVersionStringByWFIDs(str2);
/* 236 */       str12 = str12 + " AND t1.workflow_id in (" + str9 + ")";
/*     */     } 
/* 238 */     if (!Strings.isNullOrEmpty(str5)) {
/* 239 */       str12 = str12 + " AND t1.operate_userid = '" + str5 + "'";
/*     */     }
/* 241 */     if (!Strings.isNullOrEmpty(str6)) {
/* 242 */       str12 = str12 + " AND t1.operate_date >= '" + str6 + "'";
/*     */     }
/* 244 */     if (!Strings.isNullOrEmpty(str7)) {
/* 245 */       str12 = str12 + " AND t1.operate_date <= '" + str7 + "'";
/*     */     }
/* 247 */     if (!Strings.isNullOrEmpty(str4)) {
/* 248 */       str12 = str12 + " AND t1.workflow_id = '" + str4 + "'";
/*     */     }
/*     */     
/* 251 */     if (this.detachable) {
/* 252 */       String str = "";
/*     */       try {
/* 254 */         str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/* 255 */       } catch (Exception exception) {
/* 256 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 259 */       if (this.user.getUID() != 1) {
/* 260 */         if (StringUtils.isNotBlank(str)) {
/* 261 */           str12 = str12 + " and h.subcompanyid1 in (" + str;
/* 262 */           if (isAdmin(this.user)) {
/* 263 */             str12 = str12 + ",-1";
/* 264 */             str12 = str12 + ") and t1.operate_userid != 1";
/*     */           } else {
/* 266 */             str12 = str12 + ")";
/*     */           } 
/*     */         } else {
/* 269 */           str12 = str12 + " and 1 = 2";
/*     */         } 
/*     */       }
/* 272 */       if (!"".equals(str1)) {
/* 273 */         if ("0".equals(str1)) {
/* 274 */           if (!isAdmin(this.user)) {
/* 275 */             str12 = str12 + " and h.subcompanyid1 <> -1 ";
/*     */           }
/*     */         } else {
/* 278 */           str12 = str12 + " and h.subcompanyid1 = " + str1 + " ";
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 283 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 284 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(99, this.user.getLanguage()), "operate_userid", "operate_userid", "weaver.workflow.report.ViewReportLog.getViewReportViewer"));
/* 285 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(97, this.user.getLanguage()), "operate_date", "operate_date", "weaver.splitepage.transform.SptmForCowork.combineDateTime", "column:operate_time"));
/* 286 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(10000903, this.user.getLanguage()), "request_name", "request_name"));
/* 287 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(125749, this.user.getLanguage()), "workflow_id", "workflow_id", "weaver.workflow.report.ViewReportLog.getViewReportWorkflowName"));
/* 288 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(17484, this.user.getLanguage()), "client_address", "client_address"));
/*     */     
/* 290 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, Util.toHtmlForSplitPage(str12), "", "request_id", arrayList);
/* 291 */     splitTableBean.setPageUID("dab029c0-70a5-47c9-bcfc-1fb0fd685c35");
/* 292 */     splitTableBean.setPageID("dab029c0-70a5-47c9-bcfc-1fb0fd685c35");
/* 293 */     splitTableBean.setTableType("none");
/* 294 */     splitTableBean.setSqlisdistinct("false");
/* 295 */     splitTableBean.setSqlsortway("ASC");
/* 296 */     splitTableBean.setInstanceid("phraseTable");
/* 297 */     splitTableBean.setPagesize("10");
/*     */     
/* 299 */     String str13 = SplitTableUtil.getTableString(splitTableBean);
/* 300 */     String str14 = "dab029c0-70a5-47c9-bcfc-1fb0fd685c35_" + Util.getEncrypt(Util.getRandom());
/* 301 */     Util_TableMap.setVal(str14, str13);
/*     */     
/* 303 */     return str14;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getDateOptions() {
/* 312 */     String str1 = "1";
/* 313 */     String str2 = Util.null2String(this.params.get("dateselect"));
/* 314 */     if (!Strings.isNullOrEmpty(str2)) {
/* 315 */       String[] arrayOfString = str2.split(",");
/* 316 */       str1 = arrayOfString[0];
/*     */     } 
/*     */     
/* 319 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 321 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), "0".equals(str1)));
/* 322 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, this.user.getLanguage()), "1".equals(str1)));
/* 323 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, this.user.getLanguage()), "2".equals(str1)));
/* 324 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, this.user.getLanguage()), "3".equals(str1)));
/* 325 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, this.user.getLanguage()), "4".equals(str1)));
/* 326 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, this.user.getLanguage()), "5".equals(str1)));
/* 327 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, this.user.getLanguage()), "6".equals(str1)));
/*     */     
/* 329 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getTopTab() {
/* 338 */     String[] arrayOfString = { "0", "1", "2", "3", "4", "5" };
/* 339 */     int[] arrayOfInt = { 332, 15537, 15539, 15541, 383465, 15384 };
/*     */     
/* 341 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 343 */     for (byte b = 0; b < 6; b++) {
/* 344 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 345 */       hashMap.put("groupid", arrayOfString[b]);
/* 346 */       hashMap.put("title", SystemEnv.getHtmlLabelName(arrayOfInt[b], this.user.getLanguage()));
/* 347 */       hashMap.put("viewcondition", arrayOfString[b]);
/* 348 */       arrayList.add(hashMap);
/*     */     } 
/* 350 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getParams() {
/* 354 */     return this.params;
/*     */   }
/*     */   
/*     */   public void setParams(Map<String, Object> paramMap) {
/* 358 */     this.params = paramMap;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 362 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 366 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 371 */     return null;
/*     */   }
/*     */   
/*     */   public boolean isAdmin(User paramUser) {
/* 375 */     boolean bool = false;
/* 376 */     RecordSet recordSet = new RecordSet();
/* 377 */     recordSet.execute("select count(*) from hrmresourcemanager where id=" + paramUser.getUID());
/* 378 */     if (recordSet.next() && 
/* 379 */       recordSet.getInt(1) > 0) {
/* 380 */       bool = true;
/*     */     }
/*     */     
/* 383 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetDeleteDefineLogCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */