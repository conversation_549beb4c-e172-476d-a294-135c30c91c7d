/*    */ package com.engine.workflow.cmd.processLog;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Calendar;
/*    */ import java.util.Date;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DeleteOldAutoFlowLog
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public BizLogContext getLogContext() {
/* 23 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public DeleteOldAutoFlowLog(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.params = paramMap;
/* 29 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 36 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user)) {
/* 37 */       hashMap.put("hasRight", Boolean.valueOf(false));
/* 38 */       return (Map)hashMap;
/*    */     } 
/* 40 */     hashMap.put("deleteFlag", deleteOldAutoFlowLog());
/* 41 */     hashMap.put("hasRight", Boolean.valueOf(true));
/* 42 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   private Boolean deleteOldAutoFlowLog() {
/* 46 */     Boolean bool = Boolean.valueOf(true);
/*    */     
/*    */     try {
/* 49 */       RecordSet recordSet = new RecordSet();
/* 50 */       String str1 = getDeleteLogData();
/* 51 */       String str2 = "delete from workflow_autoflowlog where operatedate='' or operatedate is null or  operatedate<=? ";
/* 52 */       bool = Boolean.valueOf(recordSet.executeUpdate(str2, new Object[] { str1 }));
/* 53 */     } catch (Exception exception) {
/* 54 */       exception.printStackTrace();
/*    */     } 
/* 56 */     return bool;
/*    */   }
/*    */ 
/*    */   
/*    */   private String getDeleteLogData() {
/* 61 */     Prop.getInstance(); int i = Util.getIntValue(Prop.getPropValue("wfAutoflowLog", "deleteData"));
/* 62 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 63 */     Calendar calendar = Calendar.getInstance();
/* 64 */     calendar.add(5, -i);
/* 65 */     Date date = calendar.getTime();
/* 66 */     return simpleDateFormat.format(date);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/DeleteOldAutoFlowLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */