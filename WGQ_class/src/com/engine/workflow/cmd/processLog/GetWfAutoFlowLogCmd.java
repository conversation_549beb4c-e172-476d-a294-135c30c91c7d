/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetWfAutoFlowLogCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean detachable = false;
/*     */   private boolean sanyuanDetachable = false;
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  44 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public GetWfAutoFlowLogCmd(Map<String, Object> paramMap, User paramUser) {
/*  49 */     this.params = paramMap;
/*  50 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  55 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  56 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  57 */     String str = "";
/*  58 */     this.sanyuanDetachable = HrmSanyuanAdminBiz.getSanyuanAble();
/*  59 */     this.detachable = manageDetachComInfo.isUseManageDetach();
/*  60 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user) || (isAdmin(this.user) && this.sanyuanDetachable)) {
/*  61 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  62 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*     */     try {
/*  66 */       str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/*  67 */       if (str.length() <= 0) {
/*  68 */         hashMap.put("hasRight", Boolean.valueOf(false));
/*  69 */         return (Map)hashMap;
/*     */       } 
/*  71 */     } catch (Exception exception) {
/*  72 */       exception.printStackTrace();
/*     */     } 
/*  74 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  75 */     hashMap.put("tableSession", getTableSession());
/*  76 */     hashMap.put("conditions", getConditions());
/*  77 */     hashMap.put("formFields", getConditions());
/*  78 */     hashMap.put("topTitle", SystemEnv.getHtmlLabelName(507714, this.user.getLanguage()));
/*  79 */     hashMap.put("topTab", getTopTab());
/*  80 */     hashMap.put("detachable", Boolean.valueOf(this.detachable));
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getTopTab() {
/*  91 */     String[] arrayOfString = { "0", "1", "2", "3", "4", "5" };
/*  92 */     int[] arrayOfInt = { 332, 15537, 15539, 15541, 383465, 15384 };
/*     */     
/*  94 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  96 */     for (byte b = 0; b < 6; b++) {
/*  97 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  98 */       hashMap.put("groupid", arrayOfString[b]);
/*  99 */       hashMap.put("title", SystemEnv.getHtmlLabelName(arrayOfInt[b], this.user.getLanguage()));
/* 100 */       hashMap.put("viewcondition", arrayOfString[b]);
/* 101 */       arrayList.add(hashMap);
/*     */     } 
/* 103 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private String getTableSession() {
/* 107 */     String str1 = Util.null2String(this.params.get("subCompanyId"));
/* 108 */     String str2 = Util.null2String(this.params.get("requestid"));
/* 109 */     String str3 = Util.null2String(this.params.get("requestname"));
/* 110 */     String str4 = Util.fromScreen(Util.null2String(this.params.get("dateselect")), this.user.getLanguage());
/* 111 */     String str5 = Util.null2String(this.params.get("fromdate"));
/* 112 */     String str6 = Util.null2String(this.params.get("todate"));
/* 113 */     String str7 = " t1.requestid as requestid,t2.requestname as requestname,t1.issuccess as issuccess,t1.nodeid as nodeid,t1.nextnodeid as nextnodeid,t1.type as type,t1.failedtype as failedtype,t1.operator as operator,t1.operatedate as operatedate,t1.operatetime as operatetime ";
/* 114 */     String str8 = " workflow_autoFlowLog t1,workflow_requestbase t2";
/* 115 */     String str9 = "t1.requestid = t2.requestid and t1.operator = h.id ";
/* 116 */     if (this.sanyuanDetachable && isAdmin(this.user)) {
/* 117 */       str9 = str9 + "and t1.operator = " + this.user.getUID();
/* 118 */       str8 = str8 + ",(select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     }
/* 120 */     else if (!this.detachable && this.user.getUID() != 1 && !isAdmin(this.user)) {
/* 121 */       str8 = str8 + ",(select id,lastname,subcompanyid1 from HrmResource) h ";
/*     */     } else {
/* 123 */       str8 = str8 + ",(select id,lastname,subcompanyid1 from HrmResource union all select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     } 
/*     */ 
/*     */     
/* 127 */     if (!Strings.isNullOrEmpty(str4)) {
/* 128 */       String[] arrayOfString = str4.split(",");
/* 129 */       String str = arrayOfString[0];
/* 130 */       if ("".equals(str) || "0".equals(str)) {
/* 131 */         str5 = "";
/* 132 */         str6 = "";
/* 133 */       } else if (!str.equals("6")) {
/* 134 */         str5 = TimeUtil.getDateByOption(str, "0");
/* 135 */         str6 = TimeUtil.getDateByOption(str, "1");
/* 136 */       } else if ("6".equals(str)) {
/* 137 */         str5 = (arrayOfString.length >= 2) ? arrayOfString[1] : str5;
/* 138 */         str6 = (arrayOfString.length >= 3) ? arrayOfString[2] : str6;
/*     */       } 
/*     */     } 
/* 141 */     if (!"".equals(str2)) {
/* 142 */       str9 = str9 + " and t1.requestid=" + str2;
/*     */     }
/* 144 */     if (!"".equals(str3)) {
/* 145 */       str9 = str9 + " and t2.requestname like '%" + str3 + "%'";
/*     */     }
/* 147 */     if (!"".equals(str5)) {
/* 148 */       str9 = str9 + " and t1.operatedate >='" + str5 + "'";
/*     */     }
/* 150 */     if (!"".equals(str6)) {
/* 151 */       str9 = str9 + " and t1.operatedate <='" + str6 + "'";
/*     */     }
/*     */     
/* 154 */     if (this.detachable) {
/* 155 */       String str = "";
/*     */       try {
/* 157 */         str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/* 158 */       } catch (Exception exception) {
/* 159 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 162 */       if (this.user.getUID() != 1) {
/* 163 */         if (StringUtils.isNotBlank(str)) {
/* 164 */           str9 = str9 + " and h.subcompanyid1 in (" + str;
/* 165 */           if (isAdmin(this.user)) {
/* 166 */             str9 = str9 + ",-1";
/* 167 */             str9 = str9 + ") and t1.operator != 1";
/*     */           } else {
/* 169 */             str9 = str9 + ")";
/*     */           } 
/*     */         } else {
/* 172 */           str9 = str9 + " and 1 = 2";
/*     */         } 
/*     */       }
/* 175 */       if (!"".equals(str1)) {
/* 176 */         if ("0".equals(str1)) {
/* 177 */           if (!isAdmin(this.user)) {
/* 178 */             str9 = str9 + " and h.subcompanyid1 <> -1 ";
/*     */           }
/*     */         } else {
/* 181 */           str9 = str9 + " and h.subcompanyid1 = " + str1 + " ";
/*     */         } 
/*     */       }
/*     */     } 
/* 185 */     String str10 = "operatedate desc , operatetime ";
/* 186 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 187 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(21586, this.user.getLanguage()), "requestid", "requestid"));
/* 188 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(26876, this.user.getLanguage()), "requestname", "requestname", "weaver.workflow.report.ViewReportLog.getViewReportRequest", "column:requestid"));
/* 189 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(10000906, Util.getIntValue(this.user.getLanguage())), "issuccess", "", "weaver.workflow.request.WfAutoflowLogSort.getIssuccessString", this.user.getLanguage() + ""));
/* 190 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(22256, this.user.getLanguage()), "type", "", "weaver.workflow.request.WfAutoflowLogSort.getWfautoflowTypeString", this.user.getLanguage() + ""));
/* 191 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(22045, this.user.getLanguage()), "failedtype", "", "weaver.workflow.request.WfAutoflowLogSort.getfailedWfautoflowTypeString", "column:type+" + this.user.getLanguage()));
/* 192 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(10000907, Util.getIntValue(this.user.getLanguage())), "nodeid", "", "weaver.workflow.request.WfAutoflowLogSort.getNodename"));
/* 193 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(99, this.user.getLanguage()), "operator", "", "weaver.workflow.request.WfAutoflowLogSort.getOperatorString"));
/* 194 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(514966, this.user.getLanguage()), "nextnodeid", "", "weaver.workflow.request.WfAutoflowLogSort.getNodename"));
/* 195 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(21663, this.user.getLanguage()), "operatedate"));
/* 196 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(18008, this.user.getLanguage()), "operatetime"));
/* 197 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str8, Util.toHtmlForSplitPage(str9), str10, "", arrayList);
/*     */     
/* 199 */     splitTableBean.setPageUID("f23e777e-357d-48e7-977a-980c66b33d83");
/* 200 */     splitTableBean.setPageID("f23e777e-357d-48e7-977a-980c66b33d83");
/* 201 */     splitTableBean.setTableType("none");
/* 202 */     splitTableBean.setSqlisdistinct("false");
/* 203 */     splitTableBean.setSqlsortway("desc");
/* 204 */     splitTableBean.setInstanceid("phraseTable");
/* 205 */     splitTableBean.setPagesize("10");
/*     */     
/* 207 */     String str11 = SplitTableUtil.getTableString(splitTableBean);
/* 208 */     String str12 = "f23e777e-357d-48e7-977a-980c66b33d83_" + Util.getEncrypt(Util.getRandom());
/* 209 */     Util_TableMap.setVal(str12, str11);
/*     */     
/* 211 */     return str12;
/*     */   }
/*     */   
/*     */   private List getConditions() {
/* 215 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 217 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/* 219 */     String str = SystemEnv.getHtmlLabelName(21586, this.user.getLanguage());
/* 220 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestid");
/* 221 */     searchConditionItem1.setLabelcol(6);
/* 222 */     searchConditionItem1.setFieldcol(16);
/*     */     
/* 224 */     str = SystemEnv.getHtmlLabelName(26876, this.user.getLanguage());
/* 225 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestname");
/* 226 */     searchConditionItem2.setLabelcol(6);
/* 227 */     searchConditionItem2.setFieldcol(16);
/*     */     
/* 229 */     str = SystemEnv.getHtmlLabelName(21663, this.user.getLanguage());
/* 230 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(str, ConditionType.DATE, "dateselect");
/* 231 */     searchConditionItem3.setLabelcol(6);
/* 232 */     searchConditionItem3.setFieldcol(16);
/* 233 */     searchConditionItem3.setOptions(getDateOptions());
/*     */     
/* 235 */     arrayList.add(searchConditionItem1);
/* 236 */     arrayList.add(searchConditionItem2);
/* 237 */     arrayList.add(searchConditionItem3);
/*     */     
/* 239 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 240 */     hashMap.put("title", SystemEnv.getHtmlLabelName(33776, this.user.getLanguage()));
/* 241 */     hashMap.put("items", arrayList);
/* 242 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/* 243 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 244 */     arrayList1.add(hashMap);
/*     */     
/* 246 */     return arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getDateOptions() {
/* 255 */     String str1 = "1";
/* 256 */     String str2 = Util.null2String(this.params.get("dateselect"));
/* 257 */     if (!Strings.isNullOrEmpty(str2)) {
/* 258 */       String[] arrayOfString = str2.split(",");
/* 259 */       str1 = arrayOfString[0];
/*     */     } 
/*     */     
/* 262 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 264 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), "0".equals(str1)));
/* 265 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, this.user.getLanguage()), "1".equals(str1)));
/* 266 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, this.user.getLanguage()), "2".equals(str1)));
/* 267 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, this.user.getLanguage()), "3".equals(str1)));
/* 268 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, this.user.getLanguage()), "4".equals(str1)));
/* 269 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, this.user.getLanguage()), "5".equals(str1)));
/* 270 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, this.user.getLanguage()), "6".equals(str1)));
/*     */     
/* 272 */     return arrayList;
/*     */   }
/*     */   
/*     */   public boolean isAdmin(User paramUser) {
/* 276 */     boolean bool = false;
/* 277 */     RecordSet recordSet = new RecordSet();
/* 278 */     recordSet.execute("select count(*) from hrmresourcemanager where id=" + paramUser.getUID());
/* 279 */     if (recordSet.next() && 
/* 280 */       recordSet.getInt(1) > 0) {
/* 281 */       bool = true;
/*     */     }
/*     */     
/* 284 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetWfAutoFlowLogCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */