/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.hrm.bean.HrmFieldBean;
/*     */ import com.api.hrm.util.HrmFieldSearchConditionComInfo;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import com.engine.systeminfo.biz.log.LogRight;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSubRequestLogCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean detachable = false;
/*     */   private boolean sanyuanDetachable = false;
/*     */   private Map<String, Object> params;
/*     */   private User user;
/*     */   
/*     */   public GetSubRequestLogCmd(Map<String, Object> paramMap, User paramUser) {
/*  50 */     this.params = paramMap;
/*  51 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  58 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  59 */     String str = "";
/*  60 */     this.sanyuanDetachable = HrmSanyuanAdminBiz.getSanyuanAble();
/*  61 */     this.detachable = manageDetachComInfo.isUseManageDetach();
/*  62 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user) || (isAdmin(this.user) && this.sanyuanDetachable)) {
/*  63 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  64 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/*  67 */       str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/*  68 */       if (str.length() <= 0) {
/*  69 */         hashMap.put("hasRight", Boolean.valueOf(false));
/*  70 */         return (Map)hashMap;
/*     */       } 
/*  72 */     } catch (Exception exception) {
/*  73 */       exception.printStackTrace();
/*     */     } 
/*  75 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  76 */     hashMap.put("tableSession", getTableSession());
/*  77 */     hashMap.put("conditions", getCondition());
/*  78 */     hashMap.put("formFields", getCondition());
/*  79 */     hashMap.put("topTitle", SystemEnv.getHtmlLabelName(530218, this.user.getLanguage()));
/*  80 */     hashMap.put("topTab", getTopTab());
/*  81 */     hashMap.put("detachable", Boolean.valueOf(this.detachable));
/*  82 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getCondition() {
/*     */     SearchConditionItem searchConditionItem3;
/*  91 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  94 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/*  96 */     String str = SystemEnv.getHtmlLabelName(533110, this.user.getLanguage());
/*  97 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestname");
/*  98 */     searchConditionItem1.setLabelcol(6);
/*  99 */     searchConditionItem1.setFieldcol(16);
/*     */     
/* 101 */     str = SystemEnv.getHtmlLabelName(533111, this.user.getLanguage());
/* 102 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestid");
/* 103 */     searchConditionItem2.setLabelcol(6);
/* 104 */     searchConditionItem2.setFieldcol(16);
/*     */ 
/*     */     
/* 107 */     XssUtil xssUtil = new XssUtil();
/* 108 */     if (isAdmin(this.user)) {
/* 109 */       HrmFieldSearchConditionComInfo hrmFieldSearchConditionComInfo = new HrmFieldSearchConditionComInfo();
/*     */       
/* 111 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 112 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(513623, this.user.getLanguage()), true));
/* 113 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(513482, this.user.getLanguage()), false));
/* 114 */       arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(529082, this.user.getLanguage()), false));
/*     */       
/* 116 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 533112, "operator", arrayList2);
/*     */       
/* 118 */       SearchConditionItem searchConditionItem6 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "hrmmanagerids", "adminAccount");
/* 119 */       searchConditionItem6.getBrowserConditionParam().setIsSingle(true);
/*     */       
/* 121 */       HrmFieldBean hrmFieldBean = new HrmFieldBean("hrmResourceId", "384077", "3", "1", true);
/* 122 */       SearchConditionItem searchConditionItem7 = hrmFieldSearchConditionComInfo.getSearchConditionItem(hrmFieldBean, this.user);
/* 123 */       searchConditionItem7.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 124 */       searchConditionItem7.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 125 */       searchConditionItem7.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/*     */       
/* 127 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 128 */       hashMap1.put("0", searchConditionItem7);
/* 129 */       hashMap1.put("1", searchConditionItem6);
/*     */       
/* 131 */       searchConditionItem3.setSelectLinkageDatas(hashMap1);
/*     */       
/* 133 */       searchConditionItem3.setLabelcol(6);
/* 134 */       searchConditionItem3.setFieldcol(16);
/*     */     } else {
/* 136 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, 533112, "hrmids", "1");
/* 137 */       searchConditionItem3.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 138 */       searchConditionItem3.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 139 */       searchConditionItem3.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 140 */       searchConditionItem3.setLabelcol(6);
/* 141 */       searchConditionItem3.setFieldcol(16);
/*     */     } 
/*     */     
/* 144 */     str = SystemEnv.getHtmlLabelName(533113, Util.getIntValue(this.user.getLanguage()));
/* 145 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(str, ConditionType.DATE, "triggerType");
/* 146 */     searchConditionItem4.setLabelcol(6);
/* 147 */     searchConditionItem4.setFieldcol(16);
/* 148 */     searchConditionItem4.setOptions(getTriggerType());
/*     */     
/* 150 */     str = SystemEnv.getHtmlLabelName(533114, Util.getIntValue(this.user.getLanguage()));
/* 151 */     SearchConditionItem searchConditionItem5 = conditionFactory.createCondition(str, ConditionType.DATE, "dateselect");
/* 152 */     searchConditionItem5.setLabelcol(6);
/* 153 */     searchConditionItem5.setFieldcol(16);
/* 154 */     searchConditionItem5.setOptions(getDateOptions());
/*     */     
/* 156 */     arrayList.add(searchConditionItem1);
/* 157 */     arrayList.add(searchConditionItem2);
/* 158 */     arrayList.add(searchConditionItem3);
/* 159 */     arrayList.add(searchConditionItem4);
/* 160 */     arrayList.add(searchConditionItem5);
/*     */ 
/*     */     
/* 163 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 164 */     hashMap.put("title", SystemEnv.getHtmlLabelName(33776, this.user.getLanguage()));
/* 165 */     hashMap.put("items", arrayList);
/* 166 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/*     */ 
/*     */     
/* 169 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 170 */     arrayList1.add(hashMap);
/*     */     
/* 172 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getTableSession() {
/* 181 */     String str1 = Util.null2String(this.params.get("subCompanyId"));
/* 182 */     String str2 = Util.null2String(this.params.get("requestname"));
/* 183 */     String str3 = Util.null2String(this.params.get("requestid"));
/* 184 */     String str4 = Util.null2String(this.params.get("operator"));
/*     */     
/* 186 */     String str5 = Util.null2String(this.params.get("triggerType"));
/* 187 */     if (" ,,".equals(str5)) str5 = null;
/*     */     
/* 189 */     if (str4.equals("0")) {
/* 190 */       str4 = Util.null2String(this.params.get("hrmResourceId"));
/* 191 */     } else if (str4.equals("1")) {
/* 192 */       str4 = Util.null2String(this.params.get("hrmmanagerids"));
/* 193 */     } else if (str4.equals("2")) {
/*     */       
/* 195 */       str4 = "0";
/*     */     } else {
/* 197 */       str4 = Util.null2String(this.params.get("hrmids"));
/*     */     } 
/*     */     
/* 200 */     String str6 = Util.null2String(this.params.get("fromdate"));
/* 201 */     String str7 = Util.null2String(this.params.get("todate"));
/*     */     
/* 203 */     String str8 = Util.fromScreen(Util.null2String(this.params.get("dateselect")), this.user.getLanguage());
/* 204 */     if (!Strings.isNullOrEmpty(str8)) {
/* 205 */       String[] arrayOfString = str8.split(",");
/* 206 */       String str = arrayOfString[0];
/* 207 */       if ("".equals(str) || "0".equals(str)) {
/* 208 */         str6 = "";
/* 209 */         str7 = "";
/* 210 */       } else if (!str.equals("6")) {
/* 211 */         str6 = TimeUtil.getDateByOption(str, "0");
/* 212 */         str7 = TimeUtil.getDateByOption(str, "1");
/* 213 */       } else if ("6".equals(str)) {
/* 214 */         str6 = (arrayOfString.length >= 2) ? arrayOfString[1] : str6;
/* 215 */         str7 = (arrayOfString.length >= 3) ? arrayOfString[2] : str7;
/*     */       } 
/*     */     } 
/*     */     
/* 219 */     String str9 = SystemEnv.getHtmlLabelName(529082, this.user.getLanguage());
/* 220 */     String str10 = " union all select 0 as id,'" + str9 + "' as lastname,-1 as subcompanyid1 from HrmResourceManager where id = 1 ";
/*     */     
/* 222 */     String str11 = " a.requestid,a.requestnamenew,c.nodename,h.lastname, '533601' as detail,'-1' as isdel,b.mainReqOperatorId,b.mainReqOperateTime,b.id,b.triggertype,b.subwfsetId,b.mainreqnodetime";
/*     */     
/* 224 */     String str12 = " workflow_requestbase a,wf_subReq_log b,workflow_nodebase c";
/* 225 */     String str13 = " a.requestid = b.mainReqId and b.mainReqNodeId = c.id and h.id = b.mainReqOperatorId";
/*     */     
/* 227 */     if (this.sanyuanDetachable && isAdmin(this.user)) {
/* 228 */       str13 = str13 + "and b.mainReqOperatorId = " + this.user.getUID();
/* 229 */       str12 = str12 + ",(select id ,lastname,-1 as subcompanyid1 from HrmResourceManager " + str10 + ") h";
/*     */     }
/* 231 */     else if (!this.detachable && this.user.getUID() != 1 && !isAdmin(this.user)) {
/* 232 */       str12 = str12 + ",(select id,lastname,subcompanyid1 from HrmResource ) h ";
/*     */     } else {
/* 234 */       str12 = str12 + ",(select id,lastname,subcompanyid1 from HrmResource union all select id ,lastname,-1 as subcompanyid1 from HrmResourceManager " + str10 + ") h";
/*     */     } 
/*     */ 
/*     */     
/* 238 */     if (!Strings.isNullOrEmpty(str2)) {
/* 239 */       str13 = str13 + " AND a.requestname LIKE '%" + str2 + "%'";
/*     */     }
/* 241 */     if (!Strings.isNullOrEmpty(str3)) {
/* 242 */       str13 = str13 + " AND a.requestid = " + str3;
/*     */     }
/* 244 */     if (!Strings.isNullOrEmpty(str5)) {
/* 245 */       str13 = str13 + " AND b.triggerType = '" + str5 + "'";
/*     */     }
/* 247 */     if (!Strings.isNullOrEmpty(str4)) {
/* 248 */       str13 = str13 + " AND b.mainReqOperatorId = '" + str4 + "'";
/*     */     }
/* 250 */     if (!Strings.isNullOrEmpty(str6)) {
/* 251 */       str6 = str6 + " 00:00:00";
/* 252 */       str13 = str13 + " AND b.mainReqOperateTime >= '" + str6 + "'";
/*     */     } 
/* 254 */     if (!Strings.isNullOrEmpty(str7)) {
/* 255 */       str7 = str7 + " 23:59:59";
/* 256 */       str13 = str13 + " AND b.mainReqOperateTime <= '" + str7 + "'";
/*     */     } 
/*     */ 
/*     */     
/* 260 */     if (this.detachable) {
/* 261 */       String str = "";
/*     */       try {
/* 263 */         str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/* 264 */       } catch (Exception exception) {
/* 265 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 268 */       if (this.user.getUID() != 1) {
/* 269 */         if (StringUtils.isNotBlank(str)) {
/* 270 */           str13 = str13 + " and h.subcompanyid1 in (" + str;
/* 271 */           if (isAdmin(this.user)) {
/* 272 */             str13 = str13 + ",-1";
/* 273 */             str13 = str13 + ") and b.mainReqOperatorId != 1";
/*     */           } else {
/* 275 */             str13 = str13 + ")";
/*     */           } 
/*     */         } else {
/* 278 */           str13 = str13 + " and 1 = 2";
/*     */         } 
/*     */       }
/* 281 */       if (!"".equals(str1)) {
/* 282 */         if ("0".equals(str1)) {
/* 283 */           if (!isAdmin(this.user)) {
/* 284 */             str13 = str13 + " and h.subcompanyid1 <> -1 ";
/*     */           }
/*     */         } else {
/* 287 */           str13 = str13 + " and h.subcompanyid1 = " + str1 + " ";
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 292 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 293 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(533110, this.user.getLanguage()), "requestnamenew", null, "weaver.workflow.report.ViewReportLog.getSubRequestDetail", "column:requestid+column:requestname+column:isdel"));
/*     */     
/* 295 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(533112, this.user.getLanguage()), "lastname", null));
/* 296 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(533113, this.user.getLanguage()), "triggertype", null, "weaver.workflow.report.ViewReportLog.getTriggertype", "" + this.user
/* 297 */           .getLanguage()));
/* 298 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(533145, this.user.getLanguage()), "nodename", null, "weaver.workflow.report.ViewReportLog.getNodeInfo", "column:mainreqnodetime+column:triggertype+" + this.user
/* 299 */           .getLanguage()));
/* 300 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(533146, this.user.getLanguage()), "mainReqOperateTime", "mainReqOperateTime"));
/* 301 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(533531, this.user.getLanguage()), "id", null, "weaver.workflow.report.ViewReportLog.getSubRequest", "column:subwfsetId"));
/*     */     
/* 303 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(533148, this.user.getLanguage()), "detail", null, "weaver.workflow.report.ViewReportLog.getDetailLabel", "column:id+" + this.user
/* 304 */           .getLanguage()));
/*     */     
/* 306 */     SplitTableBean splitTableBean = new SplitTableBean(str11, str12, Util.toHtmlForSplitPage(str13), "mainReqOperateTime desc", "id", arrayList);
/* 307 */     splitTableBean.setPageUID("37598c2e-3b8d-4f27-bea4-f0b4cc8c443d");
/* 308 */     splitTableBean.setPageID("37598c2e-3b8d-4f27-bea4-f0b4cc8c443d");
/* 309 */     splitTableBean.setTableType("none");
/* 310 */     splitTableBean.setSqlisdistinct("false");
/* 311 */     splitTableBean.setSqlsortway("ASC");
/* 312 */     splitTableBean.setInstanceid("phraseTable");
/* 313 */     splitTableBean.setPagesize("10");
/*     */     
/* 315 */     String str14 = SplitTableUtil.getTableString(splitTableBean);
/* 316 */     String str15 = "37598c2e-3b8d-4f27-bea4-f0b4cc8c443d_" + Util.getEncrypt(Util.getRandom());
/* 317 */     Util_TableMap.setVal(str15, str14);
/*     */     
/* 319 */     return str15;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getDateOptions() {
/* 328 */     String str1 = "1";
/* 329 */     String str2 = Util.null2String(this.params.get("dateselect"));
/* 330 */     if (!Strings.isNullOrEmpty(str2)) {
/* 331 */       String[] arrayOfString = str2.split(",");
/* 332 */       str1 = arrayOfString[0];
/*     */     } 
/*     */     
/* 335 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 337 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), "0".equals(str1)));
/* 338 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, this.user.getLanguage()), "1".equals(str1)));
/* 339 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, this.user.getLanguage()), "2".equals(str1)));
/* 340 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, this.user.getLanguage()), "3".equals(str1)));
/* 341 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, this.user.getLanguage()), "4".equals(str1)));
/* 342 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, this.user.getLanguage()), "5".equals(str1)));
/* 343 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, this.user.getLanguage()), "6".equals(str1)));
/*     */     
/* 345 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getTriggerType() {
/* 353 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 354 */     arrayList.add(new SearchConditionOption(" ", " ", true));
/* 355 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(533115, this.user.getLanguage()), false));
/* 356 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(533116, this.user.getLanguage()), false));
/*     */     
/* 358 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getTopTab() {
/* 367 */     String[] arrayOfString = { "0", "1", "2", "3", "4", "5" };
/* 368 */     int[] arrayOfInt = { 332, 15537, 15539, 15541, 383465, 15384 };
/*     */     
/* 370 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 372 */     for (byte b = 0; b < 6; b++) {
/* 373 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 374 */       hashMap.put("groupid", arrayOfString[b]);
/* 375 */       hashMap.put("title", SystemEnv.getHtmlLabelName(arrayOfInt[b], this.user.getLanguage()));
/* 376 */       hashMap.put("viewcondition", arrayOfString[b]);
/* 377 */       arrayList.add(hashMap);
/*     */     } 
/* 379 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getParams() {
/* 383 */     return this.params;
/*     */   }
/*     */   
/*     */   public void setParams(Map<String, Object> paramMap) {
/* 387 */     this.params = paramMap;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 391 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 395 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 400 */     return null;
/*     */   }
/*     */   
/*     */   public boolean isAdmin(User paramUser) {
/* 404 */     boolean bool = false;
/* 405 */     RecordSet recordSet = new RecordSet();
/* 406 */     recordSet.execute("select count(*) from hrmresourcemanager where id=" + paramUser.getUID());
/* 407 */     if (recordSet.next() && 
/* 408 */       recordSet.getInt(1) > 0) {
/* 409 */       bool = true;
/*     */     }
/*     */     
/* 412 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetSubRequestLogCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */