/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.hrm.bean.HrmFieldBean;
/*     */ import com.api.hrm.util.HrmFieldSearchConditionComInfo;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import com.engine.systeminfo.biz.log.LogRight;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetViewDefineLogCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean detachable = false;
/*     */   private boolean sanyuanDetachable = false;
/*     */   private Map<String, Object> params;
/*     */   private User user;
/*     */   
/*     */   public GetViewDefineLogCmd(Map<String, Object> paramMap, User paramUser) {
/*  53 */     this.params = paramMap;
/*  54 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  62 */     String str = "";
/*  63 */     this.sanyuanDetachable = HrmSanyuanAdminBiz.getSanyuanAble();
/*  64 */     this.detachable = manageDetachComInfo.isUseManageDetach();
/*  65 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user) || (isAdmin(this.user) && this.sanyuanDetachable)) {
/*  66 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  67 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/*  70 */       str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/*  71 */       if (str.length() <= 0) {
/*  72 */         hashMap.put("hasRight", Boolean.valueOf(false));
/*  73 */         return (Map)hashMap;
/*     */       } 
/*  75 */     } catch (Exception exception) {
/*  76 */       exception.printStackTrace();
/*     */     } 
/*  78 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  79 */     hashMap.put("tableSession", getTableSession());
/*  80 */     hashMap.put("conditions", getCondition(false));
/*  81 */     hashMap.put("formFields", getCondition(true));
/*  82 */     hashMap.put("topTitle", SystemEnv.getHtmlLabelName(33776, this.user.getLanguage()));
/*  83 */     hashMap.put("topTab", getTopTab());
/*  84 */     hashMap.put("detachable", Boolean.valueOf(this.detachable));
/*  85 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getCondition(boolean paramBoolean) {
/*     */     SearchConditionItem searchConditionItem3;
/*  94 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  97 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/*  99 */     String str = SystemEnv.getHtmlLabelName(10000905, this.user.getLanguage());
/* 100 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestname");
/* 101 */     searchConditionItem1.setLabelcol(6);
/* 102 */     searchConditionItem1.setFieldcol(16);
/*     */     
/* 104 */     str = SystemEnv.getHtmlLabelName(125749, this.user.getLanguage());
/* 105 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "workflowname", "-99991");
/* 106 */     searchConditionItem2.setLabel(str);
/* 107 */     searchConditionItem2.setLabelcol(6);
/* 108 */     searchConditionItem2.setFieldcol(16);
/*     */     
/* 110 */     XssUtil xssUtil = new XssUtil();
/*     */     
/* 112 */     if (isAdmin(this.user)) {
/* 113 */       HrmFieldSearchConditionComInfo hrmFieldSearchConditionComInfo = new HrmFieldSearchConditionComInfo();
/* 114 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 115 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(513623, this.user.getLanguage()), true));
/* 116 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(513482, this.user.getLanguage()), false));
/* 117 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 1273, "operator", arrayList2);
/* 118 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 119 */       SearchConditionItem searchConditionItem7 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "hrmmanagerids", "adminAccount");
/* 120 */       searchConditionItem7.getBrowserConditionParam().setIsSingle(true);
/*     */ 
/*     */       
/* 123 */       HrmFieldBean hrmFieldBean = new HrmFieldBean("hrmResourceId", "384077", "3", "1", true);
/* 124 */       SearchConditionItem searchConditionItem8 = hrmFieldSearchConditionComInfo.getSearchConditionItem(hrmFieldBean, this.user);
/*     */       
/* 126 */       searchConditionItem8.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 127 */       searchConditionItem8.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 128 */       searchConditionItem8.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/*     */       
/* 130 */       hashMap1.put("0", searchConditionItem8);
/* 131 */       hashMap1.put("1", searchConditionItem7);
/* 132 */       searchConditionItem3.setSelectLinkageDatas(hashMap1);
/* 133 */       searchConditionItem3.setLabelcol(6);
/* 134 */       searchConditionItem3.setFieldcol(16);
/*     */     } else {
/* 136 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, 1273, "hrmids", "1");
/* 137 */       searchConditionItem3.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 138 */       searchConditionItem3.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 139 */       searchConditionItem3.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 140 */       searchConditionItem3.setLabelcol(6);
/* 141 */       searchConditionItem3.setFieldcol(16);
/*     */     } 
/*     */     
/* 144 */     str = SystemEnv.getHtmlLabelName(503282, Util.getIntValue(this.user.getLanguage()));
/* 145 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(str, ConditionType.DATE, "dateselect");
/* 146 */     searchConditionItem4.setLabelcol(6);
/* 147 */     searchConditionItem4.setFieldcol(16);
/* 148 */     searchConditionItem4.setOptions(getDateOptions());
/*     */     
/* 150 */     SearchConditionItem searchConditionItem5 = conditionFactory.createCondition(ConditionType.SELECT, 15503, "operatetype");
/* 151 */     searchConditionItem5.setLabelcol(6);
/* 152 */     searchConditionItem5.setFieldcol(16);
/*     */     
/* 154 */     SearchConditionItem searchConditionItem6 = conditionFactory.createCondition(ConditionType.INPUT, -1, "workflowid");
/* 155 */     searchConditionItem6.setLabelcol(6);
/* 156 */     searchConditionItem6.setFieldcol(16);
/*     */     
/* 158 */     arrayList.add(searchConditionItem1);
/* 159 */     arrayList.add(searchConditionItem2);
/* 160 */     arrayList.add(searchConditionItem3);
/* 161 */     arrayList.add(searchConditionItem4);
/* 162 */     if (paramBoolean) {
/* 163 */       arrayList.add(searchConditionItem5);
/* 164 */       arrayList.add(searchConditionItem6);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 169 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 170 */     hashMap.put("title", SystemEnv.getHtmlLabelName(33776, this.user.getLanguage()));
/* 171 */     hashMap.put("items", arrayList);
/* 172 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/*     */ 
/*     */     
/* 175 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 176 */     arrayList1.add(hashMap);
/*     */     
/* 178 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getTableSession() {
/* 187 */     String str1 = Util.null2String(this.params.get("subCompanyId"));
/*     */     
/* 189 */     String str2 = Util.null2String(this.params.get("workflowname"));
/*     */     
/* 191 */     str2 = WorkflowVersion.getAllVersionStringByWFIDs(str2);
/* 192 */     String str3 = Util.null2String(this.params.get("requestname"));
/* 193 */     String str4 = Util.null2String(this.params.get("operator"));
/* 194 */     String str5 = "";
/* 195 */     if (str4.equals("0")) {
/* 196 */       str5 = Util.null2String(this.params.get("hrmResourceId"));
/* 197 */     } else if (str4.equals("1")) {
/* 198 */       str5 = Util.null2String(this.params.get("hrmmanagerids"));
/*     */     } else {
/* 200 */       str5 = Util.null2String(this.params.get("hrmids"));
/*     */     } 
/* 202 */     String str6 = Util.null2String(this.params.get("fromdate"));
/* 203 */     String str7 = Util.null2String(this.params.get("todate"));
/* 204 */     String str8 = Util.fromScreen(Util.null2String(this.params.get("dateselect")), this.user.getLanguage());
/* 205 */     String str9 = "";
/* 206 */     if (!Strings.isNullOrEmpty(str8)) {
/* 207 */       String[] arrayOfString = str8.split(",");
/* 208 */       str9 = arrayOfString[0];
/* 209 */       if (!str9.equals("") && !str9.equals("0") && !str9.equals("6")) {
/* 210 */         str6 = TimeUtil.getDateByOption(str9, "0");
/* 211 */         str7 = TimeUtil.getDateByOption(str9, "1");
/* 212 */       } else if ("6".equals(str9)) {
/* 213 */         str6 = (arrayOfString.length >= 2) ? arrayOfString[1] : str6;
/* 214 */         str7 = (arrayOfString.length >= 3) ? arrayOfString[2] : str7;
/*     */       } 
/*     */     } 
/*     */     
/* 218 */     String str10 = "  t1.id,t1.viewdate,t1.viewtime,t2.requestname,t2.workflowid,t2.creater,t1.viewer,t1.ipaddress ";
/* 219 */     String str11 = "workflow_requestViewLog t1,workflow_requestbase t2";
/* 220 */     String str12 = "t1.id=t2.requestid and t1.viewer = h.id ";
/* 221 */     String str13 = " t1.viewdate desc,t1.viewtime desc ";
/*     */     
/* 223 */     if (this.sanyuanDetachable && isAdmin(this.user)) {
/* 224 */       str12 = str12 + "and t1.viewer = " + this.user.getUID();
/* 225 */       str11 = str11 + ",(select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     }
/* 227 */     else if (!this.detachable && this.user.getUID() != 1 && !isAdmin(this.user)) {
/* 228 */       str11 = str11 + ",(select id,lastname,subcompanyid1 from HrmResource) h ";
/*     */     } else {
/* 230 */       str11 = str11 + ",(select id,lastname,subcompanyid1 from HrmResource union all select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     } 
/*     */ 
/*     */     
/* 234 */     if (!Strings.isNullOrEmpty(str2)) {
/* 235 */       str12 = str12 + " AND ";
/* 236 */       str12 = str12 + Util.getSubINClause(str2, "t2.workflowid", "in");
/*     */     } 
/* 238 */     if (!Strings.isNullOrEmpty(str3)) {
/* 239 */       str12 = str12 + " AND t2.requestname LIKE '%" + str3 + "%' ";
/*     */     }
/* 241 */     if (!Strings.isNullOrEmpty(str5)) {
/* 242 */       str12 = str12 + " AND t1.viewer ='" + str5 + "' ";
/*     */     }
/* 244 */     if (!Strings.isNullOrEmpty(str6)) {
/* 245 */       str12 = str12 + " AND viewdate >= '" + str6 + "' ";
/*     */     }
/* 247 */     if (!Strings.isNullOrEmpty(str7)) {
/* 248 */       str12 = str12 + " AND viewdate <= '" + str7 + "' ";
/*     */     }
/*     */     
/* 251 */     if (this.detachable) {
/* 252 */       String str = "";
/*     */       try {
/* 254 */         str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/* 255 */       } catch (Exception exception) {
/* 256 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 259 */       if (this.user.getUID() != 1) {
/* 260 */         if (StringUtils.isNotBlank(str)) {
/* 261 */           str12 = str12 + " and h.subcompanyid1 in (" + str;
/* 262 */           if (isAdmin(this.user)) {
/* 263 */             str12 = str12 + ",-1";
/* 264 */             str12 = str12 + ") and t1.viewer != 1";
/*     */           } else {
/* 266 */             str12 = str12 + ")";
/*     */           } 
/*     */         } else {
/* 269 */           str12 = str12 + " and 1 = 2";
/*     */         } 
/*     */       }
/* 272 */       if (!"".equals(str1)) {
/* 273 */         if ("0".equals(str1)) {
/* 274 */           if (!isAdmin(this.user)) {
/* 275 */             str12 = str12 + " and h.subcompanyid1 <> -1 ";
/*     */           }
/*     */         } else {
/* 278 */           str12 = str12 + " and h.subcompanyid1 = " + str1 + " ";
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 283 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 284 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 285 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(15530, this.user.getLanguage()), "viewer", "viewer", "weaver.workflow.report.ViewReportLog.getViewReportViewer"));
/* 286 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(1272, this.user.getLanguage()), "viewdate", "viewdate", "weaver.workflow.report.ViewReportLog.getViewReportDateTime", "column:viewtime"));
/* 287 */     arrayList.add(new SplitTableColBean("26%", SystemEnv.getHtmlLabelName(10000905, this.user.getLanguage()), "requestname", "requestname", "weaver.workflow.report.ViewReportLog.getViewReportRequest", "column:id"));
/* 288 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(125749, this.user.getLanguage()), "workflowid", "workflowid", "weaver.workflow.report.ViewReportLog.getViewReportWorkflowName"));
/* 289 */     arrayList.add(new SplitTableColBean("14%", SystemEnv.getHtmlLabelName(17484, this.user.getLanguage()), "ipaddress", "ipaddress"));
/*     */     
/* 291 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, Util.toHtmlForSplitPage(str12), str13, "id", arrayList);
/* 292 */     splitTableBean.setPageUID("dab029c0-70a5-47c9-bcfc-1fb0fd685c33");
/* 293 */     splitTableBean.setPageID("dab029c0-70a5-47c9-bcfc-1fb0fd685c33");
/* 294 */     splitTableBean.setTableType("none");
/* 295 */     splitTableBean.setSqlisdistinct("false");
/* 296 */     splitTableBean.setSqlsortway("ASC");
/* 297 */     splitTableBean.setInstanceid("phraseTable");
/* 298 */     splitTableBean.setPagesize("10");
/*     */     
/* 300 */     String str14 = SplitTableUtil.getTableString(splitTableBean);
/* 301 */     String str15 = "dab029c0-70a5-47c9-bcfc-1fb0fd685c33_" + Util.getEncrypt(Util.getRandom());
/* 302 */     Util_TableMap.setVal(str15, str14);
/*     */     
/* 304 */     return str15;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getDateOptions() {
/* 314 */     String str1 = "1";
/* 315 */     String str2 = Util.null2String(this.params.get("dateselect"));
/* 316 */     if (!Strings.isNullOrEmpty(str2)) {
/* 317 */       String[] arrayOfString = str2.split(",");
/* 318 */       str1 = arrayOfString[0];
/*     */     } 
/*     */     
/* 321 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 323 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), "0".equals(str1)));
/* 324 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, this.user.getLanguage()), "1".equals(str1)));
/* 325 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, this.user.getLanguage()), "2".equals(str1)));
/* 326 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, this.user.getLanguage()), "3".equals(str1)));
/* 327 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, this.user.getLanguage()), "4".equals(str1)));
/* 328 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, this.user.getLanguage()), "5".equals(str1)));
/* 329 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, this.user.getLanguage()), "6".equals(str1)));
/*     */     
/* 331 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getTopTab() {
/* 340 */     String[] arrayOfString = { "0", "1", "2", "3", "4", "5" };
/* 341 */     int[] arrayOfInt = { 332, 15537, 15539, 15541, 383465, 15384 };
/*     */     
/* 343 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 345 */     for (byte b = 0; b < 6; b++) {
/* 346 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 347 */       hashMap.put("groupid", arrayOfString[b]);
/* 348 */       hashMap.put("title", SystemEnv.getHtmlLabelName(arrayOfInt[b], this.user.getLanguage()));
/* 349 */       hashMap.put("viewcondition", arrayOfString[b]);
/* 350 */       arrayList.add(hashMap);
/*     */     } 
/* 352 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getParams() {
/* 356 */     return this.params;
/*     */   }
/*     */   
/*     */   public void setParams(Map<String, Object> paramMap) {
/* 360 */     this.params = paramMap;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 364 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 368 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 373 */     return null;
/*     */   }
/*     */   
/*     */   public boolean isAdmin(User paramUser) {
/* 377 */     boolean bool = false;
/* 378 */     RecordSet recordSet = new RecordSet();
/* 379 */     recordSet.execute("select count(*) from hrmresourcemanager where id=" + paramUser.getUID());
/* 380 */     if (recordSet.next() && 
/* 381 */       recordSet.getInt(1) > 0) {
/* 382 */       bool = true;
/*     */     }
/*     */     
/* 385 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetViewDefineLogCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */