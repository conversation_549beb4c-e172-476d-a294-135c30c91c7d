/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class GetRequestFlogLogDetailCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>> {
/*     */   private int id;
/*     */   private int workflowid;
/*     */   private int billid;
/*     */   private int isbill;
/*     */   private String tablename;
/*     */   
/*     */   public GetRequestFlogLogDetailCmd(Map<String, Object> paramMap, User paramUser) {
/*  30 */     this.params = paramMap;
/*  31 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   private void init() {
/*  35 */     this.id = Util.getIntValue(Util.null2String(this.params.get("id")));
/*  36 */     RecordSet recordSet = new RecordSet();
/*  37 */     String str = "select t1.workflowid,t3.isbill,t3.formid,t4.tablename from workflow_requestbase t1,      workflow_requestflowlog t2,      workflow_base t3,      workflow_bill t4 where t1.requestid = t2.requestid   and t2.id = ?  and t3.id = t1.workflowid   and t4.id = t3.formid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  46 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(this.id) });
/*  47 */     if (recordSet.next()) {
/*  48 */       this.workflowid = recordSet.getInt("workflowid");
/*  49 */       this.isbill = recordSet.getInt("isbill");
/*  50 */       this.billid = recordSet.getInt("formid");
/*  51 */       this.tablename = Util.null2String(recordSet.getString("tablename"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  58 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  63 */     init();
/*  64 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  65 */     RecordSet recordSet = new RecordSet();
/*  66 */     String str = "select submitparam, tabledata,saveLog,linkLog,operatorLog,actionlog,ruleexecuteLog,resultdata,messageInfo from workflow_requestflowlog where id = ?";
/*  67 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(this.id) });
/*  68 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/*  69 */     if (recordSet.next()) {
/*  70 */       arrayList.add(initLogMap("submitparam", dealSubmitParam(JSON.parseObject(recordSet.getString("submitparam")))));
/*  71 */       arrayList.add(initLogMap("saveLog", dealSaveLog(JSON.parseObject(recordSet.getString("saveLog")))));
/*  72 */       arrayList.add(initLogMap("linkLog", dealLinkLog(JSON.parseObject(recordSet.getString("linkLog")))));
/*  73 */       arrayList.add(initLogMap("operatorLog", dealOperatorLog(JSON.parseObject(recordSet.getString("operatorLog")))));
/*  74 */       arrayList.add(initLogMap("actionlog", dealActionlog(JSON.parseObject(recordSet.getString("actionlog")))));
/*     */       
/*  76 */       arrayList.add(initLogMap("resultdata", dealResultdata(JSON.parseObject(recordSet.getString("resultdata")))));
/*  77 */       arrayList.add(initLogMap("messageInfo", dealMessageInfo(Util.null2String(recordSet.getString("messageInfo")), JSON.parseObject(recordSet.getString("resultdata")))));
/*     */     } 
/*  79 */     hashMap.put("logList", arrayList);
/*  80 */     hashMap.put("tabs", getTabs());
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> dealSubmitParam(JSONObject paramJSONObject) {
/*  90 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  91 */     ArrayList<Map<String, Object>> arrayList1 = new ArrayList();
/*  92 */     arrayList1.add(initColumn("参数名", "paramName", "paramName", "50%"));
/*  93 */     arrayList1.add(initColumn("参数值", "paramValue", "paramValue", "50%"));
/*  94 */     hashMap.put("columns", arrayList1);
/*  95 */     ArrayList<Map<String, Object>> arrayList2 = new ArrayList();
/*  96 */     byte b = 0;
/*  97 */     Set set = paramJSONObject.keySet();
/*  98 */     for (String str : set) {
/*  99 */       b++;
/* 100 */       arrayList2.add(initColumnData(b, "paramName", str, "paramValue", paramJSONObject.get(str)));
/*     */     } 
/* 102 */     hashMap.put("datas", arrayList2);
/* 103 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> dealSaveLog(JSONObject paramJSONObject) {
/* 113 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 114 */     ArrayList<Map<String, Object>> arrayList1 = new ArrayList();
/* 115 */     arrayList1.add(initColumn("操作对象", "opkey", "opkey", "50%"));
/* 116 */     arrayList1.add(initColumn("值", "value", "value", "50%"));
/* 117 */     hashMap1.put("columns", arrayList1);
/* 118 */     ArrayList<Map<String, Object>> arrayList2 = new ArrayList();
/* 119 */     byte b = 1;
/*     */     
/* 121 */     Map map = (Map)paramJSONObject.get("saveMainDatasInfo");
/* 122 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 123 */     if (map != null) {
/* 124 */       for (Map.Entry entry : map.entrySet()) {
/* 125 */         if ("maineditfields".equals(entry.getKey()))
/* 126 */           continue;  hashMap2.put(((String)entry.getKey()).toString(), entry.getValue());
/*     */       } 
/* 128 */       arrayList2.add(initColumnData(b++, "opkey", "mainTableName", "value", this.tablename, hashMap2));
/* 129 */       if (paramJSONObject.get("saveDetailDatasInfo") != null) {
/* 130 */         List list = (List)paramJSONObject.get("saveDetailDatasInfo");
/* 131 */         for (Map map1 : list) {
/* 132 */           String str = Util.null2String(map1.get("detailTableName"));
/* 133 */           List list1 = (List)map1.get("detailDatasList");
/* 134 */           arrayList2.add(initColumnData(b++, "opkey", "detailTablename", "value", str, list1));
/*     */         } 
/* 136 */         hashMap1.put("datas", arrayList2);
/*     */       } 
/*     */     } 
/*     */     
/* 140 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   private Map<String, Object> dealLinkLog(JSONObject paramJSONObject) {
/* 144 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 145 */     ArrayList<Map<String, Object>> arrayList1 = new ArrayList();
/* 146 */     arrayList1.add(initColumn("出口名", "rule", "rule", "50%"));
/* 147 */     arrayList1.add(initColumn("条件判断结果", "result", "result", "50%"));
/* 148 */     hashMap.put("columns", arrayList1);
/* 149 */     ArrayList<Map<String, Object>> arrayList2 = new ArrayList();
/* 150 */     byte b = 1;
/* 151 */     Iterator<Map.Entry> iterator = paramJSONObject.entrySet().iterator();
/* 152 */     while (iterator.hasNext()) {
/* 153 */       Map.Entry entry = iterator.next();
/* 154 */       String str1 = entry.getKey().toString();
/* 155 */       Map map = (Map)entry.getValue();
/* 156 */       String str2 = Util.null2String(map.get("result"));
/* 157 */       str2 = str2.equals("") ? "出口条件未设置" : str2;
/* 158 */       arrayList2.add(initColumnData(b++, "rule", str1, "result", str2, map));
/*     */     } 
/* 160 */     Collections.reverse(arrayList2);
/* 161 */     hashMap.put("datas", arrayList2);
/* 162 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> dealOperatorLog(JSONObject paramJSONObject) {
/* 166 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 167 */     Map map = (Map)paramJSONObject.get("groupsLog");
/* 168 */     ArrayList<Map<String, Object>> arrayList1 = new ArrayList();
/* 169 */     arrayList1.add(initColumn("操作组名称", "groupName", "groupName", "50%"));
/* 170 */     arrayList1.add(initColumn("条件判断结果", "result", "result", "50%"));
/* 171 */     hashMap.put("columns", arrayList1);
/* 172 */     ArrayList<Map<String, Object>> arrayList2 = new ArrayList();
/* 173 */     byte b = 1;
/* 174 */     if (map != null && map.size() > 0) {
/* 175 */       Iterator<Map.Entry> iterator = map.entrySet().iterator();
/* 176 */       while (iterator.hasNext()) {
/* 177 */         Map.Entry entry = iterator.next();
/* 178 */         String str1 = entry.getKey().toString();
/* 179 */         List<Map> list = (List)entry.getValue();
/* 180 */         String str2 = "";
/* 181 */         if (list != null) {
/* 182 */           Map map1 = list.get(list.size() - 1);
/* 183 */           if (map1 != null) {
/* 184 */             Map map2 = (Map)map1.get("ruleInfo");
/* 185 */             if (map2 != null) {
/* 186 */               str2 = Util.null2String(map2.get("result"));
/*     */             }
/*     */           } 
/*     */         } 
/* 190 */         arrayList2.add(initColumnData(b++, "groupName", str1, "result", str2, list));
/*     */       } 
/*     */     } 
/* 193 */     hashMap.put("datas", arrayList2);
/* 194 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> dealResultdata(JSONObject paramJSONObject) {
/* 198 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 199 */     Map map = (Map)paramJSONObject.get("data");
/* 200 */     ArrayList<Map<String, Object>> arrayList1 = new ArrayList();
/* 201 */     arrayList1.add(initColumn("返回参数名", "resultParamName", "resultParamName", "100%"));
/* 202 */     hashMap.put("columns", arrayList1);
/* 203 */     ArrayList<Map<String, Object>> arrayList2 = new ArrayList();
/* 204 */     byte b = 1;
/* 205 */     if (map != null && map.size() > 0) {
/* 206 */       Iterator<Map.Entry> iterator = map.entrySet().iterator();
/* 207 */       while (iterator.hasNext()) {
/* 208 */         Map.Entry entry = iterator.next();
/* 209 */         String str = entry.getKey().toString();
/* 210 */         Object object = entry.getValue();
/* 211 */         arrayList2.add(initColumnData(b++, "resultParamName", str, object));
/*     */       } 
/*     */     } 
/* 214 */     hashMap.put("datas", arrayList2);
/* 215 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, Object> dealActionlog(JSONObject paramJSONObject) {
/* 220 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 221 */     JSONObject jSONObject = paramJSONObject;
/* 222 */     ArrayList<Map<String, Object>> arrayList1 = new ArrayList();
/* 223 */     arrayList1.add(initColumn("接口名", "interfaceName", "interfaceName", "50%"));
/* 224 */     arrayList1.add(initColumn("执行结果", "result", "result", "50%"));
/* 225 */     hashMap.put("columns", arrayList1);
/* 226 */     ArrayList<Map<String, Object>> arrayList2 = new ArrayList();
/* 227 */     byte b = 1;
/* 228 */     if (jSONObject != null && jSONObject.size() > 0) {
/* 229 */       Iterator<Map.Entry> iterator = jSONObject.entrySet().iterator();
/* 230 */       while (iterator.hasNext()) {
/* 231 */         Map.Entry entry = iterator.next();
/* 232 */         String str = entry.getKey().toString();
/* 233 */         Object object = entry.getValue();
/* 234 */         arrayList2.add(initColumnData(b++, "interfaceName", str, "result", object));
/*     */       } 
/*     */     } 
/* 237 */     hashMap.put("datas", arrayList2);
/* 238 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> dealMessageInfo(String paramString, JSONObject paramJSONObject) {
/* 242 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 243 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/* 244 */     arrayList.add(initColumn("失败原因", "messageInfo", "messageInfo", "100%"));
/* 245 */     hashMap1.put("columns", arrayList);
/* 246 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 247 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 248 */     hashMap2.put("key", Integer.valueOf(1));
/* 249 */     paramString = repaceHtml(paramString);
/* 250 */     if ("".equals(paramString.replace("\"", "")) && paramJSONObject != null) {
/* 251 */       Map map = (Map)paramJSONObject.get("data");
/* 252 */       if (map != null) {
/* 253 */         Map map1 = (Map)map.get("messageInfo");
/* 254 */         if (map1 != null) {
/* 255 */           String str = Util.null2String(map1.get("bottom"));
/* 256 */           str = repaceHtml(str);
/* 257 */           if (str.indexOf("，") > -1) {
/* 258 */             str = str.substring(0, str.lastIndexOf("，"));
/* 259 */             paramString = str;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 266 */     hashMap2.put("messageInfo", paramString);
/* 267 */     arrayList1.add(hashMap2);
/* 268 */     hashMap1.put("datas", arrayList1);
/* 269 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public String repaceHtml(String paramString) {
/* 274 */     if ("".equals(Util.null2String(paramString))) return ""; 
/* 275 */     paramString = paramString.replaceAll("<[^>]+>", "");
/* 276 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getTabs() {
/* 281 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/* 282 */     arrayList.add(initTab(531440, "0"));
/* 283 */     arrayList.add(initTab(531441, "1"));
/* 284 */     arrayList.add(initTab(531442, "2"));
/* 285 */     arrayList.add(initTab(531443, "3"));
/* 286 */     arrayList.add(initTab(531444, "4"));
/*     */     
/* 288 */     arrayList.add(initTab(531446, "5"));
/* 289 */     arrayList.add(initTab(27041, "6"));
/* 290 */     return arrayList;
/*     */   }
/*     */   
/*     */   private Map<String, Object> initLogMap(String paramString, Object paramObject) {
/* 294 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 295 */     hashMap.put("key", paramString);
/* 296 */     hashMap.put("data", paramObject);
/* 297 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> initTab(int paramInt, String paramString) {
/* 301 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 302 */     hashMap.put("title", SystemEnv.getHtmlLabelName(paramInt, ThreadVarLanguage.getLang()));
/* 303 */     hashMap.put("viewcondition", paramString);
/* 304 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> initColumn(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 308 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 309 */     hashMap.put("title", paramString1);
/* 310 */     hashMap.put("dataIndex", paramString2);
/* 311 */     hashMap.put("key", paramString3);
/* 312 */     hashMap.put("width", paramString4);
/* 313 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> initColumnData(int paramInt, String paramString1, Object paramObject1, String paramString2, Object paramObject2) {
/* 317 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 318 */     hashMap.put("key", Integer.valueOf(paramInt));
/* 319 */     hashMap.put(paramString1, paramObject1.toString());
/* 320 */     hashMap.put(paramString2, paramObject2.toString());
/* 321 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> initColumnData(int paramInt, String paramString, Object paramObject1, Object paramObject2) {
/* 325 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 326 */     hashMap.put("key", Integer.valueOf(paramInt));
/* 327 */     hashMap.put(paramString, paramObject1.toString());
/* 328 */     hashMap.put("description", JSONObject.toJSONString(paramObject2));
/* 329 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> initColumnData(int paramInt, String paramString1, Object paramObject1, String paramString2, Object paramObject2, Object paramObject3) {
/* 333 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 334 */     hashMap.put("key", Integer.valueOf(paramInt));
/* 335 */     hashMap.put(paramString1, paramObject1.toString());
/* 336 */     hashMap.put(paramString2, paramObject2.toString());
/* 337 */     hashMap.put("description", JSONObject.toJSONString(paramObject3));
/* 338 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetRequestFlogLogDetailCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */