/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.hrm.bean.HrmFieldBean;
/*     */ import com.api.hrm.util.HrmFieldSearchConditionComInfo;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import com.engine.systeminfo.biz.log.LogRight;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSubRequestListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean detachable = false;
/*     */   private boolean sanyuanDetachable = false;
/*     */   private Map<String, Object> params;
/*     */   private User user;
/*     */   
/*     */   public GetSubRequestListCmd(Map<String, Object> paramMap, User paramUser) {
/*  49 */     this.params = paramMap;
/*  50 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  56 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  57 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  58 */     String str = "";
/*  59 */     this.sanyuanDetachable = HrmSanyuanAdminBiz.getSanyuanAble();
/*  60 */     this.detachable = manageDetachComInfo.isUseManageDetach();
/*  61 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user) || (isAdmin(this.user) && this.sanyuanDetachable)) {
/*  62 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  63 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/*  66 */       str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/*  67 */       if (str.length() <= 0) {
/*  68 */         hashMap.put("hasRight", Boolean.valueOf(false));
/*  69 */         return (Map)hashMap;
/*     */       } 
/*  71 */     } catch (Exception exception) {
/*  72 */       exception.printStackTrace();
/*     */     } 
/*  74 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  75 */     hashMap.put("tableSession", getTableSession());
/*  76 */     hashMap.put("conditions", getCondition());
/*  77 */     hashMap.put("formFields", getCondition());
/*  78 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getCondition() {
/*     */     SearchConditionItem searchConditionItem3;
/*  87 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  90 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/*  92 */     String str = SystemEnv.getHtmlLabelName(533299, this.user.getLanguage());
/*  93 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestname");
/*  94 */     searchConditionItem1.setLabelcol(6);
/*  95 */     searchConditionItem1.setFieldcol(16);
/*     */     
/*  97 */     str = SystemEnv.getHtmlLabelName(533298, this.user.getLanguage());
/*  98 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestid");
/*  99 */     searchConditionItem2.setLabelcol(6);
/* 100 */     searchConditionItem2.setFieldcol(16);
/*     */ 
/*     */     
/* 103 */     XssUtil xssUtil = new XssUtil();
/* 104 */     if (isAdmin(this.user)) {
/* 105 */       HrmFieldSearchConditionComInfo hrmFieldSearchConditionComInfo = new HrmFieldSearchConditionComInfo();
/* 106 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 107 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(513623, this.user.getLanguage()), true));
/* 108 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(513482, this.user.getLanguage()), false));
/* 109 */       arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(529082, this.user.getLanguage()), false));
/*     */       
/* 111 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 533300, "operator", arrayList2);
/* 112 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 113 */       SearchConditionItem searchConditionItem5 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "hrmmanagerids", "adminAccount");
/* 114 */       searchConditionItem5.getBrowserConditionParam().setIsSingle(true);
/* 115 */       HrmFieldBean hrmFieldBean = new HrmFieldBean("hrmResourceId", "384077", "3", "1", true);
/* 116 */       SearchConditionItem searchConditionItem6 = hrmFieldSearchConditionComInfo.getSearchConditionItem(hrmFieldBean, this.user);
/*     */       
/* 118 */       searchConditionItem6.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 119 */       searchConditionItem6.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 120 */       searchConditionItem6.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 121 */       hashMap1.put("0", searchConditionItem6);
/* 122 */       hashMap1.put("1", searchConditionItem5);
/* 123 */       searchConditionItem3.setSelectLinkageDatas(hashMap1);
/* 124 */       searchConditionItem3.setLabelcol(6);
/* 125 */       searchConditionItem3.setFieldcol(16);
/*     */     } else {
/* 127 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, 533300, "hrmids", "1");
/* 128 */       searchConditionItem3.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 129 */       searchConditionItem3.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 130 */       searchConditionItem3.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 131 */       searchConditionItem3.setLabelcol(6);
/* 132 */       searchConditionItem3.setFieldcol(16);
/*     */     } 
/* 134 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.BROWSER, 125749, "workflowId", "-99991");
/* 135 */     searchConditionItem4.setLabelcol(6);
/* 136 */     searchConditionItem4.setFieldcol(16);
/*     */     
/* 138 */     arrayList.add(searchConditionItem1);
/* 139 */     arrayList.add(searchConditionItem2);
/* 140 */     arrayList.add(searchConditionItem3);
/* 141 */     arrayList.add(searchConditionItem4);
/*     */ 
/*     */     
/* 144 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 145 */     hashMap.put("title", SystemEnv.getHtmlLabelName(33776, this.user.getLanguage()));
/* 146 */     hashMap.put("items", arrayList);
/* 147 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/*     */ 
/*     */     
/* 150 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 151 */     arrayList1.add(hashMap);
/*     */     
/* 153 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getTableSession() {
/* 162 */     String str1 = Util.null2String(this.params.get("subCompanyId"));
/* 163 */     String str2 = Util.null2String(this.params.get("requestname"));
/* 164 */     String str3 = Util.null2String(this.params.get("requestid"));
/* 165 */     String str4 = Util.null2String(this.params.get("operator"));
/* 166 */     String str5 = Util.null2String(this.params.get("workflowId"));
/* 167 */     String str6 = Util.null2String(this.params.get("subReqLogMainId"));
/*     */     
/* 169 */     if (str4.equals("0")) {
/* 170 */       str4 = Util.null2String(this.params.get("hrmResourceId"));
/* 171 */     } else if (str4.equals("1")) {
/* 172 */       str4 = Util.null2String(this.params.get("hrmmanagerids"));
/* 173 */     } else if (str4.equals("2")) {
/*     */       
/* 175 */       str4 = "0";
/*     */     } else {
/* 177 */       str4 = Util.null2String(this.params.get("hrmids"));
/*     */     } 
/*     */     
/* 180 */     String str7 = SystemEnv.getHtmlLabelName(529082, this.user.getLanguage());
/* 181 */     String str8 = " union all select 0 as id,'" + str7 + "' as lastname,-1 as subcompanyid1 from HrmResourceManager where id = 1 ";
/*     */     
/* 183 */     String str9 = " a.requestid as id,a.requestname,a.requestnamenew,a.isdel,a.creater,a.workflowid, h.lastname,h.id as hid,c.workflowname";
/* 184 */     String str10 = " (select requestid,requestname,requestnamenew,creater,workflowid,'-1' as isdel from workflow_requestbase union all select requestid,requestname,requestnamenew,creater,workflowid,'18967' as isdel from workflow_requestbase_dellog where (isvalid is null or isvalid = 0 ) ) a,wf_subReq_detail_log b,workflow_base c";
/*     */     
/* 186 */     String str11 = " b.mainLogId = " + str6 + " and b.subReqId = a.requestid and h.id = a.creater and c.id = a.workflowid";
/*     */     
/* 188 */     if (this.sanyuanDetachable && isAdmin(this.user)) {
/* 189 */       str11 = str11 + "and a.creater = " + this.user.getUID();
/* 190 */       str10 = str10 + ",(select id ,lastname,-1 as subcompanyid1 from HrmResourceManager " + str8 + ") h";
/*     */     }
/* 192 */     else if (!this.detachable && this.user.getUID() != 1 && !isAdmin(this.user)) {
/* 193 */       str10 = str10 + ",(select id,lastname,subcompanyid1 from HrmResource) h ";
/*     */     } else {
/* 195 */       str10 = str10 + ",(select id,lastname,subcompanyid1 from HrmResource union all select id ,lastname,-1 as subcompanyid1 from HrmResourceManager " + str8 + ") h";
/*     */     } 
/*     */ 
/*     */     
/* 199 */     if (!Strings.isNullOrEmpty(str2)) {
/* 200 */       str11 = str11 + " AND a.requestname LIKE '%" + str2 + "%'";
/*     */     }
/*     */     
/* 203 */     if (!Strings.isNullOrEmpty(str3)) {
/* 204 */       str11 = str11 + " AND a.requestid =" + str3 + "";
/*     */     }
/*     */     
/* 207 */     if (!Strings.isNullOrEmpty(str4)) {
/* 208 */       str11 = str11 + " AND  a.creater = '" + str4 + "'";
/*     */     }
/* 210 */     if (!Strings.isNullOrEmpty(str5)) {
/* 211 */       str11 = str11 + " AND a.workflowid = '" + str5 + "'";
/*     */     }
/*     */     
/* 214 */     if (this.detachable) {
/* 215 */       String str = "";
/*     */       try {
/* 217 */         str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/* 218 */       } catch (Exception exception) {
/* 219 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 222 */       if (this.user.getUID() != 1) {
/* 223 */         if (StringUtils.isNotBlank(str)) {
/* 224 */           str11 = str11 + " and h.subcompanyid1 in (" + str;
/* 225 */           if (isAdmin(this.user)) {
/* 226 */             str11 = str11 + ",-1";
/* 227 */             str11 = str11 + ") and b.mainReqOperatorId != 1";
/*     */           } else {
/* 229 */             str11 = str11 + ")";
/*     */           } 
/*     */         } else {
/* 232 */           str11 = str11 + " and 1 = 2";
/*     */         } 
/*     */       }
/* 235 */       if (!"".equals(str1)) {
/* 236 */         if ("0".equals(str1)) {
/* 237 */           if (!isAdmin(this.user)) {
/* 238 */             str11 = str11 + " and h.subcompanyid1 <> -1 ";
/*     */           }
/*     */         } else {
/* 241 */           str11 = str11 + " and h.subcompanyid1 = " + str1 + " ";
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 246 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 247 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(533298, this.user.getLanguage()), "id", "id"));
/* 248 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(533299, this.user.getLanguage()), "requestnamenew", "requestid", "weaver.workflow.report.ViewReportLog.getSubRequestDetail", "column:id+column:requestname+column:isdel"));
/*     */     
/* 250 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(533532, this.user.getLanguage()), "workflowname", "requestid"));
/*     */     
/* 252 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(533300, this.user.getLanguage()), "lastname", "hid", "weaver.workflow.report.ViewReportLog.getSubRequestCreater", "column:hid"));
/*     */     
/* 254 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(19061, this.user.getLanguage()), "isdel", "hid", "weaver.workflow.report.ViewReportLog.getSubRequestStatus", "" + this.user
/* 255 */           .getLanguage()));
/*     */     
/* 257 */     SplitTableBean splitTableBean = new SplitTableBean(str9, str10, Util.toHtmlForSplitPage(str11), "", "id", arrayList);
/* 258 */     splitTableBean.setPageUID("e9611746-ce9c-4b90-ae19-6667b4ada780");
/* 259 */     splitTableBean.setPageID("e9611746-ce9c-4b90-ae19-6667b4ada780");
/* 260 */     splitTableBean.setTableType("none");
/* 261 */     splitTableBean.setSqlisdistinct("false");
/* 262 */     splitTableBean.setSqlsortway("ASC");
/* 263 */     splitTableBean.setInstanceid("phraseTable");
/* 264 */     splitTableBean.setPagesize("10");
/*     */     
/* 266 */     String str12 = SplitTableUtil.getTableString(splitTableBean);
/* 267 */     String str13 = "e9611746-ce9c-4b90-ae19-6667b4ada780_" + Util.getEncrypt(Util.getRandom());
/* 268 */     Util_TableMap.setVal(str13, str12);
/*     */     
/* 270 */     return str13;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getParams() {
/* 276 */     return this.params;
/*     */   }
/*     */   
/*     */   public void setParams(Map<String, Object> paramMap) {
/* 280 */     this.params = paramMap;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 284 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 288 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 293 */     return null;
/*     */   }
/*     */   
/*     */   public boolean isAdmin(User paramUser) {
/* 297 */     boolean bool = false;
/* 298 */     RecordSet recordSet = new RecordSet();
/* 299 */     recordSet.execute("select count(*) from hrmresourcemanager where id=" + paramUser.getUID());
/* 300 */     if (recordSet.next() && 
/* 301 */       recordSet.getInt(1) > 0) {
/* 302 */       bool = true;
/*     */     }
/*     */     
/* 305 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetSubRequestListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */