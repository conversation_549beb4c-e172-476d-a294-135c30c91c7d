/*     */ package com.engine.workflow.cmd.processLog;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.hrm.bean.HrmFieldBean;
/*     */ import com.api.hrm.util.HrmFieldSearchConditionComInfo;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import com.engine.systeminfo.biz.log.LogRight;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetOperateDefineLogCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean detachable = false;
/*     */   private boolean sanyuanDetachable = false;
/*     */   private Map<String, Object> params;
/*     */   private User user;
/*     */   
/*     */   public GetOperateDefineLogCmd(Map<String, Object> paramMap, User paramUser) {
/*  52 */     this.params = paramMap;
/*  53 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  60 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  61 */     String str = "";
/*  62 */     this.sanyuanDetachable = HrmSanyuanAdminBiz.getSanyuanAble();
/*  63 */     this.detachable = manageDetachComInfo.isUseManageDetach();
/*  64 */     if (!HrmUserVarify.checkUserRight("LogView:View", this.user) || (isAdmin(this.user) && this.sanyuanDetachable)) {
/*  65 */       hashMap.put("hasRight", Boolean.valueOf(false));
/*  66 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/*  69 */       str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/*  70 */       if (str.length() <= 0) {
/*  71 */         hashMap.put("hasRight", Boolean.valueOf(false));
/*  72 */         return (Map)hashMap;
/*     */       } 
/*  74 */     } catch (Exception exception) {
/*  75 */       exception.printStackTrace();
/*     */     } 
/*  77 */     hashMap.put("hasRight", Boolean.valueOf(true));
/*  78 */     hashMap.put("tableSession", getTableSession());
/*  79 */     hashMap.put("conditions", getCondition(false));
/*  80 */     hashMap.put("formFields", getCondition(true));
/*  81 */     hashMap.put("topTitle", SystemEnv.getHtmlLabelName(33777, this.user.getLanguage()));
/*  82 */     hashMap.put("topTab", getTopTab());
/*  83 */     hashMap.put("detachable", Boolean.valueOf(this.detachable));
/*  84 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getCondition(boolean paramBoolean) {
/*     */     SearchConditionItem searchConditionItem3;
/*  93 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  96 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/*  98 */     String str = SystemEnv.getHtmlLabelName(10000904, this.user.getLanguage());
/*  99 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(str, ConditionType.INPUT, "requestname");
/* 100 */     searchConditionItem1.setLabelcol(6);
/* 101 */     searchConditionItem1.setFieldcol(16);
/*     */     
/* 103 */     str = SystemEnv.getHtmlLabelName(125749, this.user.getLanguage());
/* 104 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "workflowname", "-99991");
/* 105 */     searchConditionItem2.setLabelcol(6);
/* 106 */     searchConditionItem2.setFieldcol(16);
/* 107 */     searchConditionItem2.setLabel(str);
/*     */ 
/*     */     
/* 110 */     XssUtil xssUtil = new XssUtil();
/* 111 */     if (isAdmin(this.user)) {
/* 112 */       HrmFieldSearchConditionComInfo hrmFieldSearchConditionComInfo = new HrmFieldSearchConditionComInfo();
/* 113 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 114 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(513623, this.user.getLanguage()), true));
/* 115 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(513482, this.user.getLanguage()), false));
/* 116 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 99, "operator", arrayList2);
/* 117 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 118 */       SearchConditionItem searchConditionItem7 = conditionFactory.createCondition(ConditionType.BROWSER, -1, "hrmmanagerids", "adminAccount");
/* 119 */       searchConditionItem7.getBrowserConditionParam().setIsSingle(true);
/*     */       
/* 121 */       HrmFieldBean hrmFieldBean = new HrmFieldBean("hrmResourceId", "384077", "3", "1", true);
/* 122 */       SearchConditionItem searchConditionItem8 = hrmFieldSearchConditionComInfo.getSearchConditionItem(hrmFieldBean, this.user);
/*     */       
/* 124 */       searchConditionItem8.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 125 */       searchConditionItem8.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 126 */       searchConditionItem8.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 127 */       hashMap1.put("0", searchConditionItem8);
/* 128 */       hashMap1.put("1", searchConditionItem7);
/* 129 */       searchConditionItem3.setSelectLinkageDatas(hashMap1);
/* 130 */       searchConditionItem3.setLabelcol(6);
/* 131 */       searchConditionItem3.setFieldcol(16);
/*     */     } else {
/* 133 */       searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, 99, "hrmids", "1");
/* 134 */       searchConditionItem3.getBrowserConditionParam().getDataParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 135 */       searchConditionItem3.getBrowserConditionParam().getCompleteParams().put("sqlwhere", xssUtil.put(LogRight.getDetachBrowserSql(this.user, false)));
/* 136 */       searchConditionItem3.getBrowserConditionParam().getConditionDataParams().put("changeType", "dismiss");
/* 137 */       searchConditionItem3.setLabelcol(6);
/* 138 */       searchConditionItem3.setFieldcol(16);
/*     */     } 
/*     */     
/* 141 */     str = SystemEnv.getHtmlLabelName(503282, Util.getIntValue(this.user.getLanguage()));
/* 142 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(str, ConditionType.DATE, "dateselect");
/* 143 */     searchConditionItem4.setLabelcol(6);
/* 144 */     searchConditionItem4.setFieldcol(16);
/* 145 */     searchConditionItem4.setOptions(getDateOptions());
/*     */     
/* 147 */     SearchConditionItem searchConditionItem5 = conditionFactory.createCondition(ConditionType.SELECT, 15503, "operatetype");
/* 148 */     searchConditionItem5.setLabelcol(6);
/* 149 */     searchConditionItem5.setFieldcol(16);
/* 150 */     searchConditionItem5.setOptions(getTypeOptions());
/*     */     
/* 152 */     SearchConditionItem searchConditionItem6 = conditionFactory.createCondition(ConditionType.INPUT, -1, "workflowid");
/* 153 */     searchConditionItem6.setLabelcol(6);
/* 154 */     searchConditionItem6.setFieldcol(16);
/*     */     
/* 156 */     arrayList.add(searchConditionItem1);
/* 157 */     arrayList.add(searchConditionItem2);
/* 158 */     arrayList.add(searchConditionItem3);
/* 159 */     arrayList.add(searchConditionItem4);
/* 160 */     arrayList.add(searchConditionItem5);
/* 161 */     if (paramBoolean) {
/* 162 */       arrayList.add(searchConditionItem6);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 167 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 168 */     hashMap.put("title", SystemEnv.getHtmlLabelName(33776, this.user.getLanguage()));
/* 169 */     hashMap.put("items", arrayList);
/* 170 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/*     */ 
/*     */     
/* 173 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 174 */     arrayList1.add(hashMap);
/*     */     
/* 176 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getTableSession() {
/* 185 */     String str1 = Util.null2String(this.params.get("subCompanyId"));
/* 186 */     String str2 = Util.null2String(this.params.get("workflowname"));
/*     */     
/* 188 */     str2 = WorkflowVersion.getAllVersionStringByWFIDs(str2);
/* 189 */     String str3 = Util.null2String(this.params.get("requestname"));
/* 190 */     String str4 = Util.null2String(this.params.get("operatetype"));
/* 191 */     String str5 = Util.null2String(this.params.get("operator"));
/* 192 */     if (str5.equals("0")) {
/*     */ 
/*     */       
/* 195 */       str5 = Util.null2String(this.params.get("hrmResourceId"));
/* 196 */     } else if (str5.equals("1")) {
/* 197 */       str5 = Util.null2String(this.params.get("hrmmanagerids"));
/*     */     } else {
/* 199 */       str5 = Util.null2String(this.params.get("hrmids"));
/*     */     } 
/*     */     
/* 202 */     String str6 = Util.null2String(this.params.get("fromdate"));
/* 203 */     String str7 = Util.null2String(this.params.get("todate"));
/* 204 */     String str8 = Util.fromScreen(Util.null2String(this.params.get("dateselect")), this.user.getLanguage());
/* 205 */     if (!Strings.isNullOrEmpty(str8)) {
/* 206 */       String[] arrayOfString = str8.split(",");
/* 207 */       String str = arrayOfString[0];
/* 208 */       if (!str.equals("") && !str.equals("0") && !str.equals("6")) {
/* 209 */         str6 = TimeUtil.getDateByOption(str, "0");
/* 210 */         str7 = TimeUtil.getDateByOption(str, "1");
/* 211 */       } else if ("6".equals(str)) {
/* 212 */         str6 = (arrayOfString.length >= 2) ? arrayOfString[1] : str6;
/* 213 */         str7 = (arrayOfString.length >= 3) ? arrayOfString[2] : str7;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 218 */     String str9 = "t1.clientip, t1.logtype,t1.operatedate,t1.operatetime,t1.workflowid,t1.operator,t1.requestid, t2.creater, t2.requestname ";
/* 219 */     String str10 = " workflow_requestLog t1,workflow_requestbase t2";
/* 220 */     String str11 = " t1.requestid=t2.requestid and t1.operator = h.id ";
/* 221 */     String str12 = " t1.operatedate desc,t1.operatetime desc ";
/*     */     
/* 223 */     if (this.sanyuanDetachable && isAdmin(this.user)) {
/* 224 */       str11 = str11 + "and t1.operator = " + this.user.getUID();
/* 225 */       str10 = str10 + ",(select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     
/*     */     }
/* 228 */     else if (!this.detachable && this.user.getUID() != 1 && !isAdmin(this.user)) {
/* 229 */       str10 = str10 + ",(select id,lastname,subcompanyid1 from HrmResource) h ";
/*     */     } else {
/* 231 */       str10 = str10 + ",(select id,lastname,subcompanyid1 from HrmResource union all select id ,lastname,-1 as subcompanyid1 from HrmResourceManager) h";
/*     */     } 
/*     */     
/* 234 */     if (!Strings.isNullOrEmpty(str2)) {
/* 235 */       str11 = str11 + " AND ";
/* 236 */       str11 = str11 + Util.getSubINClause(str2, "t1.workflowid", "in");
/*     */     } 
/* 238 */     if (!Strings.isNullOrEmpty(str3)) {
/* 239 */       str11 = str11 + " AND t2.requestname LIKE '%" + str3 + "%' ";
/*     */     }
/* 241 */     if (!Strings.isNullOrEmpty(str5)) {
/* 242 */       str11 = str11 + " AND t1.operator = '" + str5 + "' ";
/*     */     }
/*     */     
/* 245 */     if (!Strings.isNullOrEmpty(str6)) {
/* 246 */       str11 = str11 + " AND t1.operatedate >= '" + str6 + "' ";
/*     */     }
/* 248 */     if (!Strings.isNullOrEmpty(str7)) {
/* 249 */       str11 = str11 + " AND t1.operatedate <= '" + str7 + "' ";
/*     */     }
/* 251 */     if (!Strings.isNullOrEmpty(str4)) {
/* 252 */       str11 = str11 + " AND t1.logtype = '" + str4 + "' ";
/*     */     }
/*     */     
/* 255 */     if (this.detachable) {
/* 256 */       String str = "";
/*     */       try {
/* 258 */         str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "LogView:View", -1);
/* 259 */       } catch (Exception exception) {
/* 260 */         exception.printStackTrace();
/*     */       } 
/*     */       
/* 263 */       if (this.user.getUID() != 1) {
/* 264 */         if (StringUtils.isNotBlank(str)) {
/* 265 */           str11 = str11 + " and h.subcompanyid1 in (" + str;
/* 266 */           if (isAdmin(this.user)) {
/* 267 */             str11 = str11 + ",-1";
/* 268 */             str11 = str11 + ") and t1.operator != 1";
/*     */           } else {
/* 270 */             str11 = str11 + ")";
/*     */           } 
/*     */         } else {
/* 273 */           str11 = str11 + " and 1 = 2";
/*     */         } 
/*     */       }
/* 276 */       if (!"".equals(str1)) {
/* 277 */         if ("0".equals(str1)) {
/* 278 */           if (!isAdmin(this.user)) {
/* 279 */             str11 = str11 + " and h.subcompanyid1 <> -1 ";
/*     */           }
/*     */         } else {
/* 282 */           str11 = str11 + " and h.subcompanyid1 = " + str1 + " ";
/*     */         } 
/*     */       }
/*     */     } 
/* 286 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 287 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(99, this.user.getLanguage()), "operator", "creater", "weaver.workflow.report.ViewReportLog.getViewReportViewer"));
/* 288 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(15503, this.user.getLanguage()), "logtype", "logtype", "weaver.workflow.report.ViewReportLog.getLogType", this.user.getLanguage() + ""));
/* 289 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15502, this.user.getLanguage()), "operatedate", "operatedate", "weaver.workflow.report.ViewReportLog.getViewReportDateTime", "column:operatetime"));
/* 290 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(10000904, this.user.getLanguage()), "requestname", "requestname", "weaver.workflow.report.ViewReportLog.getViewReportRequest", "column:requestid"));
/* 291 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(125749, this.user.getLanguage()), "workflowid", "workflowid", "weaver.workflow.report.ViewReportLog.getViewReportWorkflowName"));
/* 292 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(17484, this.user.getLanguage()), "clientip", "clientip"));
/*     */     
/* 294 */     SplitTableBean splitTableBean = new SplitTableBean(str9, str10, Util.toHtmlForSplitPage(str11), str12, "t1.requestid", arrayList);
/* 295 */     splitTableBean.setPageUID("dab029c0-70a5-47c9-bcfc-1fb0fd685c34");
/* 296 */     splitTableBean.setPageID("dab029c0-70a5-47c9-bcfc-1fb0fd685c34");
/* 297 */     splitTableBean.setTableType("none");
/* 298 */     splitTableBean.setSqlisdistinct("false");
/* 299 */     splitTableBean.setSqlsortway("ASC");
/* 300 */     splitTableBean.setInstanceid("phraseTable");
/* 301 */     splitTableBean.setPagesize("10");
/*     */     
/* 303 */     String str13 = SplitTableUtil.getTableString(splitTableBean);
/* 304 */     String str14 = "dab029c0-70a5-47c9-bcfc-1fb0fd685c34_" + Util.getEncrypt(Util.getRandom());
/* 305 */     Util_TableMap.setVal(str14, str13);
/*     */     
/* 307 */     return str14;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getDateOptions() {
/* 318 */     String str1 = "1";
/* 319 */     String str2 = Util.null2String(this.params.get("dateselect"));
/* 320 */     if (!Strings.isNullOrEmpty(str2)) {
/* 321 */       String[] arrayOfString = str2.split(",");
/* 322 */       str1 = arrayOfString[0];
/*     */     } 
/*     */     
/* 325 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 327 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), "0".equals(str1)));
/* 328 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, this.user.getLanguage()), "1".equals(str1)));
/* 329 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, this.user.getLanguage()), "2".equals(str1)));
/* 330 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, this.user.getLanguage()), "3".equals(str1)));
/* 331 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, this.user.getLanguage()), "4".equals(str1)));
/* 332 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, this.user.getLanguage()), "5".equals(str1)));
/* 333 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, this.user.getLanguage()), "6".equals(str1)));
/*     */     
/* 335 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getTypeOptions() {
/* 345 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 348 */     arrayList.add(new SearchConditionOption("", ""));
/* 349 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(142, this.user.getLanguage())));
/* 350 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(86, this.user.getLanguage())));
/* 351 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(615, this.user.getLanguage())));
/* 352 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(236, this.user.getLanguage())));
/*     */     
/* 354 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(91, this.user.getLanguage())));
/*     */     
/* 356 */     arrayList.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(6011, this.user.getLanguage())));
/* 357 */     arrayList.add(new SearchConditionOption("9", SystemEnv.getHtmlLabelName(1006, this.user.getLanguage())));
/* 358 */     arrayList.add(new SearchConditionOption("e", SystemEnv.getHtmlLabelName(18360, this.user.getLanguage())));
/*     */     
/* 360 */     arrayList.add(new SearchConditionOption("s", SystemEnv.getHtmlLabelName(21223, this.user.getLanguage())));
/* 361 */     arrayList.add(new SearchConditionOption("r", SystemEnv.getHtmlLabelName(517785, this.user.getLanguage())));
/* 362 */     arrayList.add(new SearchConditionOption("i", SystemEnv.getHtmlLabelName(18913, this.user.getLanguage())));
/* 363 */     arrayList.add(new SearchConditionOption("h", SystemEnv.getHtmlLabelName(132319, this.user.getLanguage())));
/*     */     
/* 365 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getTopTab() {
/* 374 */     String[] arrayOfString = { "0", "1", "2", "3", "4", "5" };
/* 375 */     int[] arrayOfInt = { 332, 15537, 15539, 15541, 383465, 15384 };
/*     */     
/* 377 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 379 */     for (byte b = 0; b < 6; b++) {
/* 380 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 381 */       hashMap.put("groupid", arrayOfString[b]);
/* 382 */       hashMap.put("title", SystemEnv.getHtmlLabelName(arrayOfInt[b], this.user.getLanguage()));
/* 383 */       hashMap.put("viewcondition", arrayOfString[b]);
/* 384 */       arrayList.add(hashMap);
/*     */     } 
/* 386 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getParams() {
/* 391 */     return this.params;
/*     */   }
/*     */   
/*     */   public void setParams(Map<String, Object> paramMap) {
/* 395 */     this.params = paramMap;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 399 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 403 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 408 */     return null;
/*     */   }
/*     */   
/*     */   public boolean isAdmin(User paramUser) {
/* 412 */     boolean bool = false;
/* 413 */     RecordSet recordSet = new RecordSet();
/* 414 */     recordSet.execute("select count(*) from hrmresourcemanager where id=" + paramUser.getUID());
/* 415 */     if (recordSet.next() && 
/* 416 */       recordSet.getInt(1) > 0) {
/* 417 */       bool = true;
/*     */     }
/*     */     
/* 420 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/processLog/GetOperateDefineLogCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */