/*    */ package com.engine.workflow.cmd.formManage.fieldManage;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.TextUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetPubCheckChildOptionsCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetPubCheckChildOptionsCmd(Map<String, Object> paramMap, User paramUser) {
/* 30 */     this.params = paramMap;
/* 31 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 36 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 42 */     int i = Util.getIntValue(Util.null2String(this.params.get("mainId")), -1);
/* 43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 44 */     if (!HrmUserVarify.checkUserRight("FieldManage:All", this.user)) {
/* 45 */       hashMap.put("userRight", Boolean.valueOf(false));
/* 46 */       return (Map)hashMap;
/*    */     } 
/* 48 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 49 */     RecordSet recordSet = new RecordSet();
/* 50 */     recordSet.execute("select id,name from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=" + i + " order by disorder asc");
/* 51 */     while (recordSet.next()) {
/* 52 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 53 */       hashMap1.put("key", recordSet.getString("id"));
/* 54 */       hashMap1.put("showname", TextUtil.toBase64ForMultilang(recordSet.getString("name")));
/* 55 */       hashMap1.put("selected", Boolean.valueOf(false));
/* 56 */       arrayList.add(hashMap1);
/*    */     } 
/*    */     
/* 59 */     hashMap.put("options", arrayList);
/* 60 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/GetPubCheckChildOptionsCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */