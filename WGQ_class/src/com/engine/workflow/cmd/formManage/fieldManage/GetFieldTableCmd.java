/*     */ package com.engine.workflow.cmd.formManage.fieldManage;
/*     */ 
/*     */ import com.api.browser.bean.Checkboxpopedom;
/*     */ import com.api.browser.bean.Operate;
/*     */ import com.api.browser.bean.Popedom;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.bean.SplitTableOperateBean;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.workflow.util.PageUidFactory;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetFieldTableCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetFieldTableCmd(Map<String, Object> paramMap, User paramUser) {
/*  39 */     this.params = paramMap;
/*  40 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  45 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  52 */     if (!HrmUserVarify.checkUserRight("FieldManage:All", this.user)) {
/*  53 */       hashMap.put("userRight", Boolean.valueOf(false));
/*  54 */       return (Map)hashMap;
/*     */     } 
/*  56 */     String str1 = Util.null2String(this.params.get("tableType"));
/*  57 */     String str2 = Util.null2String(this.params.get("fieldName"));
/*  58 */     String str3 = Util.null2String(this.params.get("description"));
/*  59 */     String str4 = Util.null2String(this.params.get("istemplate"));
/*  60 */     String str5 = Util.null2String(this.params.get("fieldHtmlType"));
/*  61 */     String str6 = Util.null2String(this.params.get("fieldHtmlTypeLinkType"));
/*     */     
/*  63 */     String str7 = Util.null2String(this.params.get("isSysField"));
/*     */ 
/*     */     
/*  66 */     String str8 = "select distinct fieldid from workflow_formfield ";
/*  67 */     String str9 = "";
/*  68 */     RecordSet recordSet = new RecordSet();
/*  69 */     recordSet.executeQuery(str8, new Object[0]);
/*  70 */     while (recordSet.next()) {
/*  71 */       str9 = str9 + "," + recordSet.getString(1);
/*     */     }
/*     */ 
/*     */     
/*  75 */     String str10 = " ";
/*  76 */     if ("mainfield".equals(str1)) {
/*  77 */       RecordSet recordSet1 = new RecordSet();
/*  78 */       recordSet1.executeQuery("select distinct fieldid from workflow_formfield where formid=14", new Object[0]);
/*  79 */       while (recordSet1.next()) {
/*  80 */         str10 = str10 + "," + recordSet1.getString(1);
/*     */       }
/*  82 */       if (!Strings.isNullOrEmpty(str10)) {
/*  83 */         str10 = str10.substring(str10.indexOf(",") + 1);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  88 */     String str11 = " where 1=1 ";
/*     */     
/*  90 */     if (!Strings.isNullOrEmpty(str2)) {
/*  91 */       str11 = str11 + " and fieldname like '%" + str2 + "%'";
/*     */     }
/*  93 */     if (!Strings.isNullOrEmpty(str3)) {
/*  94 */       str11 = str11 + " and description like '%" + str3 + "%'";
/*     */     }
/*  96 */     if (!Strings.isNullOrEmpty(str4)) {
/*  97 */       if ("1".equals(str4)) {
/*  98 */         str11 = str11 + " and istemplate = '" + str4 + "'";
/*     */       } else {
/* 100 */         str11 = str11 + " and ( istemplate = '" + str4 + "'or istemplate is null ) ";
/*     */       } 
/*     */     }
/* 103 */     if (!Strings.isNullOrEmpty(str7)) {
/* 104 */       if ("1".equals(str7)) {
/* 105 */         str11 = str11 + " and ( fieldname = 'manager' or fieldname = 'president'  or id in (" + str10 + ") )";
/*     */       } else {
/* 107 */         str11 = str11 + " and (fieldname != 'manager' and fieldname != 'president'  and id not in (" + str10 + "))";
/*     */       } 
/*     */     }
/* 110 */     if (!Strings.isNullOrEmpty(str5)) {
/* 111 */       str11 = str11 + " and fieldhtmltype = '" + str5 + "'";
/*     */     }
/* 113 */     if (!Strings.isNullOrEmpty(str6)) {
/* 114 */       str11 = str11 + " and type = '" + str6 + "'";
/*     */     }
/* 116 */     String str12 = " id,fieldname,fielddbtype,fieldhtmltype,type,subcompanyid,description,textheight,childfieldid,imgheight,imgwidth,istemplate ";
/* 117 */     String str13 = " fieldhtmltype,type,fieldname ";
/* 118 */     String str14 = " workflow_formdict ";
/* 119 */     String str15 = "workflow_formdictTable";
/* 120 */     if ("detailfield".equals(str1)) {
/* 121 */       str14 = "workflow_formdictdetail";
/* 122 */       str15 = "workflow_formdictTable_detail";
/*     */     } 
/*     */ 
/*     */     
/* 126 */     String str16 = "column:id+column:fieldname+" + this.user.getLanguage() + "+" + str10 + "+" + str9 + "+" + str1;
/* 127 */     String str17 = "column:id+column:fieldname+" + this.user.getLanguage() + "+" + str10 + "+" + str1;
/* 128 */     String str18 = "column:id+column:fieldname+" + this.user.getLanguage() + "+" + str10 + "+" + str9 + "+column:description+column:fieldhtmltype+column:type+" + str1;
/* 129 */     String str19 = "column:type+" + this.user.getLanguage();
/*     */     
/* 131 */     String str20 = PageUidFactory.getWfPageUid("Wf:field_managefield");
/*     */     
/* 133 */     SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
/* 134 */     Popedom popedom = new Popedom("weaver.workflow.field.FieldMainManager.checkEditAndDel4E9", str16, "id");
/* 135 */     ArrayList<Operate> arrayList = new ArrayList();
/* 136 */     Operate operate1 = new Operate(SystemEnv.getHtmlLabelName(26473, this.user.getLanguage()), "javascript:fieldTemplateUtil.onEdit()", "0");
/* 137 */     Operate operate2 = new Operate(SystemEnv.getHtmlLabelName(23777, this.user.getLanguage()), "javascript:fieldTemplateUtil.onDel()", "1");
/* 138 */     Operate operate3 = new Operate(SystemEnv.getHtmlLabelName(128836, this.user.getLanguage()), "javascript:fieldTemplateUtil.onSetTemplate()", "2");
/* 139 */     Operate operate4 = new Operate(SystemEnv.getHtmlLabelName(83, this.user.getLanguage()), "javascript:fieldTemplateUtil.onShowLog()", "3");
/* 140 */     arrayList.add(operate1);
/* 141 */     arrayList.add(operate2);
/* 142 */     arrayList.add(operate3);
/* 143 */     arrayList.add(operate4);
/* 144 */     splitTableOperateBean.setOperate(arrayList);
/* 145 */     splitTableOperateBean.setPopedom(popedom);
/*     */     
/* 147 */     ArrayList<SplitTableColBean> arrayList1 = new ArrayList();
/* 148 */     arrayList1.add(new SplitTableColBean("true", "id"));
/* 149 */     arrayList1.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldname", "fieldname", "weaver.workflow.field.FieldMainManager.getFieldName4E9", str18));
/* 150 */     arrayList1.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "description", "description"));
/* 151 */     arrayList1.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(687, this.user.getLanguage()), "fieldhtmltype", "fieldhtmltype", "weaver.workflow.field.FieldMainManager.getFieldHtmlTypeShow", str19));
/* 152 */     arrayList1.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(383458, this.user.getLanguage()), "fielddbtype", "fielddbtype", "weaver.workflow.field.FieldMainManager.getFieldDbTypeShow", "column:fieldhtmltype+column:type"));
/* 153 */     arrayList1.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(383459, this.user.getLanguage()), "istemplate", "istemplate", "weaver.workflow.field.FieldMainManager.getTemplateField", String.valueOf(this.user.getLanguage())));
/*     */     
/* 155 */     if ("mainfield".equals(str1)) {
/* 156 */       arrayList1.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(383460, this.user.getLanguage()), "isSysField", "fieldname", "weaver.workflow.field.FieldMainManager.getSysField4E9", str17));
/*     */     }
/*     */     
/* 159 */     Checkboxpopedom checkboxpopedom = new Checkboxpopedom("check", "weaver.workflow.field.FieldMainManager.getCheckbox", str16);
/*     */     
/* 161 */     SplitTableBean splitTableBean = new SplitTableBean(str12, str14, str11, str13, "id", arrayList1);
/* 162 */     splitTableBean.setPageUID(str20);
/* 163 */     splitTableBean.setPageID(str20);
/* 164 */     splitTableBean.setTableType("checkbox");
/* 165 */     splitTableBean.setSqlisdistinct("true");
/* 166 */     splitTableBean.setOperates(splitTableOperateBean);
/* 167 */     splitTableBean.setInstanceid(str15);
/* 168 */     splitTableBean.setCheckboxpopedom(checkboxpopedom);
/* 169 */     splitTableBean.setSqlsortway("ASC");
/*     */     
/* 171 */     String str21 = SplitTableUtil.getTableString(splitTableBean);
/* 172 */     String str22 = str20 + "_" + Util.getEncrypt(Util.getRandom());
/* 173 */     Util_TableMap.setVal(str22, str21);
/* 174 */     hashMap.put("sessionkey", str22);
/* 175 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/GetFieldTableCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */