/*     */ package com.engine.workflow.cmd.formManage.fieldManage;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Map;
/*     */ import java.util.StringTokenizer;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SelectItemCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public SelectItemCmd(Map<String, Object> paramMap, User paramUser) {
/*  33 */     this.params = paramMap;
/*  34 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  39 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  45 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  46 */     if (!HrmUserVarify.checkUserRight("FieldManage:All", this.user) && !HrmUserVarify.checkUserRight("FormManage:All", this.user)) {
/*  47 */       hashMap1.put("userRight", Boolean.valueOf(false));
/*  48 */       return (Map)hashMap1;
/*     */     } 
/*  50 */     int i = Util.getIntValue(Util.null2String(this.params.get("childfieldid")));
/*  51 */     int j = Util.getIntValue(Util.null2String(this.params.get("isbill")));
/*  52 */     int k = Util.getIntValue(Util.null2String(this.params.get("isdetail")));
/*  53 */     int m = Util.getIntValue(Util.null2String(this.params.get("allselect")), -1);
/*  54 */     String str1 = Util.null2String(this.params.get("sqlwhere"));
/*     */     
/*  56 */     String str2 = "";
/*  57 */     String str3 = "";
/*  58 */     RecordSet recordSet1 = new RecordSet();
/*  59 */     if (j == 0) {
/*  60 */       if (k == 0) {
/*  61 */         recordSet1.execute("select fieldname, description from workflow_formdict where id=" + i);
/*     */       } else {
/*  63 */         recordSet1.execute("select fieldname, description from workflow_formdictdetail where id=" + i);
/*     */       } 
/*     */     } else {
/*  66 */       recordSet1.execute("select fieldname, fieldlabel from workflow_billfield where id=" + i);
/*     */     } 
/*  68 */     if (recordSet1.next()) {
/*  69 */       str2 = Util.null2String(recordSet1.getString("fieldname"));
/*  70 */       if (j == 0) {
/*  71 */         str3 = Util.null2String(recordSet1.getString("description"));
/*     */       } else {
/*  73 */         int n = Util.getIntValue(recordSet1.getString("fieldlabel"), 0);
/*  74 */         str3 = SystemEnv.getHtmlLabelName(n, this.user.getLanguage());
/*     */       } 
/*     */     } 
/*     */     
/*  78 */     String str4 = Util.null2String(this.params.get("resourceids"));
/*     */     
/*  80 */     String str5 = "";
/*  81 */     String str6 = "";
/*  82 */     RecordSet recordSet2 = new RecordSet();
/*  83 */     if (!str4.equals("")) {
/*  84 */       String str = "select id, selectname, selectvalue from workflow_SelectItem where fieldid=" + i + " and selectvalue in (" + str4 + ")";
/*  85 */       recordSet2.execute(str);
/*  86 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  87 */       while (recordSet2.next()) {
/*  88 */         String str8 = Util.null2String(recordSet2.getString("selectvalue"));
/*  89 */         String str9 = Util.null2String(recordSet2.getString("selectname"));
/*  90 */         hashtable.put(str8, str9);
/*     */       } 
/*     */       try {
/*  93 */         StringTokenizer stringTokenizer = new StringTokenizer(str4, ",");
/*  94 */         while (stringTokenizer.hasMoreTokens()) {
/*  95 */           String str8 = stringTokenizer.nextToken();
/*  96 */           if (hashtable.containsKey(str8)) {
/*  97 */             str5 = str5 + "," + str8;
/*  98 */             str6 = str6 + "," + Util.null2String((String)hashtable.get(str8)).replace(",", "~~weavercomma~~");
/*     */           } 
/*     */         } 
/* 101 */       } catch (Exception exception) {
/* 102 */         str5 = "";
/* 103 */         str6 = "";
/*     */       } 
/*     */     } 
/*     */     
/* 107 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 108 */     RecordSet recordSet3 = new RecordSet();
/* 109 */     if (!str1.equals("")) {
/* 110 */       str1 = str1 + " and fieldid=" + i;
/*     */     } else {
/* 112 */       str1 = " where fieldid=" + i;
/*     */     } 
/* 114 */     if (i == 0) {
/* 115 */       str1 = str1 + " and 1=2";
/*     */     }
/* 117 */     str1 = str1 + " and isbill=" + j;
/* 118 */     String str7 = "";
/* 119 */     if (recordSet3.getDBType().equals("oracle")) {
/* 120 */       str7 = "select distinct id, selectname, selectvalue, listorder from workflow_SelectItem " + str1;
/* 121 */       if (m != 1) str7 = str7 + "  and (cancel IS NULL OR cancel!=1)"; 
/* 122 */       str7 = str7 + " order by listorder, id asc";
/*     */     } else {
/* 124 */       str7 = "select distinct id, selectname, selectvalue, listorder from workflow_SelectItem " + str1;
/* 125 */       if (m != 1) str7 = str7 + " and (cancel IS NULL OR cancel!=1)"; 
/* 126 */       str7 = str7 + " order by listorder, id asc";
/*     */     } 
/* 128 */     recordSet3.execute(str7);
/*     */     
/* 130 */     HashMap<Object, Object> hashMap2 = null;
/* 131 */     while (recordSet3.next()) {
/* 132 */       hashMap2 = new HashMap<>();
/* 133 */       String str8 = Util.null2String(recordSet3.getString("selectvalue"));
/* 134 */       String str9 = Util.toScreen(recordSet3.getString("selectname"), this.user.getLanguage());
/* 135 */       hashMap2.put("key", str8);
/* 136 */       hashMap2.put("showname", str9);
/* 137 */       arrayList.add(hashMap2);
/*     */     } 
/*     */     
/* 140 */     hashMap1.put("fieldname", str2);
/* 141 */     hashMap1.put("description", str3);
/* 142 */     hashMap1.put("resourceids", str5);
/* 143 */     hashMap1.put("selectnames", str6);
/* 144 */     hashMap1.put("childfield", arrayList);
/* 145 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/SelectItemCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */