/*     */ package com.engine.workflow.cmd.formManage.fieldManage;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.constant.FieldHtmlType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LoadConditionCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public LoadConditionCmd(Map<String, Object> paramMap, User paramUser) {
/*  34 */     this.params = paramMap;
/*  35 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  42 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  49 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  50 */     hashMap.put("hasRight", Boolean.valueOf(false));
/*     */     
/*  52 */     if (HrmUserVarify.checkUserRight("FieldManage:All", this.user)) {
/*     */       
/*  54 */       hashMap.put("hasRight", Boolean.valueOf(true));
/*     */       
/*  56 */       ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */       
/*  58 */       ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  59 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  60 */       hashMap1.put("title", SystemEnv.getHtmlLabelName(15774, this.user.getLanguage()));
/*  61 */       hashMap1.put("items", arrayList);
/*  62 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/*     */       
/*  64 */       SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 124937, "fieldName");
/*  65 */       searchConditionItem1.setLabelcol(6);
/*  66 */       searchConditionItem1.setFieldcol(16);
/*  67 */       SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 15456, "description");
/*  68 */       searchConditionItem2.setLabelcol(6);
/*  69 */       searchConditionItem2.setFieldcol(16);
/*  70 */       SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT, 383459, "istemplate");
/*  71 */       searchConditionItem3.setLabelcol(6);
/*  72 */       searchConditionItem3.setFieldcol(16);
/*  73 */       searchConditionItem3.setOptions(getYesOrNoOptions());
/*  74 */       SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.SELECT, 383460, "isSysField");
/*  75 */       searchConditionItem4.setLabelcol(6);
/*  76 */       searchConditionItem4.setFieldcol(16);
/*  77 */       searchConditionItem4.setOptions(getYesOrNoOptions());
/*  78 */       SearchConditionItem searchConditionItem5 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 687, "fieldHtmlType");
/*  79 */       searchConditionItem5.setLabelcol(6);
/*  80 */       searchConditionItem5.setFieldcol(16);
/*  81 */       searchConditionItem5.setOptions(geHtmlTypetOptions());
/*  82 */       searchConditionItem5.setSelectLinkageDatas(getLinkOptions());
/*     */       
/*  84 */       arrayList.add(searchConditionItem1);
/*  85 */       arrayList.add(searchConditionItem2);
/*  86 */       arrayList.add(searchConditionItem3);
/*  87 */       arrayList.add(searchConditionItem4);
/*  88 */       arrayList.add(searchConditionItem5);
/*     */       
/*  90 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  91 */       arrayList1.add(hashMap1);
/*  92 */       hashMap.put("conditions", arrayList1);
/*     */     } 
/*  94 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, SearchConditionItem> getLinkOptions() {
/*  99 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 100 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 102 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.SELECT, "84113", "fieldHtmlTypeLinkType");
/* 103 */     searchConditionItem1.setLabelcol(6);
/* 104 */     searchConditionItem1.setFieldcol(18);
/* 105 */     searchConditionItem1.setOptions(getSingleLinkOptions());
/*     */     
/* 107 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, "84113", "fieldHtmlTypeLinkType");
/* 108 */     searchConditionItem2.setLabelcol(6);
/* 109 */     searchConditionItem2.setFieldcol(18);
/* 110 */     searchConditionItem2.setOptions(getBrowserLinkOptions());
/*     */     
/* 112 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT, "84113", "fieldHtmlTypeLinkType");
/* 113 */     searchConditionItem3.setLabelcol(6);
/* 114 */     searchConditionItem3.setFieldcol(18);
/* 115 */     searchConditionItem3.setOptions(getUploadLinkOptions());
/*     */     
/* 117 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.SELECT, "84113", "fieldHtmlTypeLinkType");
/* 118 */     searchConditionItem4.setLabelcol(6);
/* 119 */     searchConditionItem4.setFieldcol(18);
/* 120 */     searchConditionItem4.setOptions(getSpecialLinkOptions());
/*     */     
/* 122 */     hashMap.put("1", searchConditionItem1);
/* 123 */     hashMap.put("3", searchConditionItem2);
/* 124 */     hashMap.put("6", searchConditionItem3);
/* 125 */     hashMap.put("7", searchConditionItem4);
/* 126 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private List<SearchConditionOption> getYesOrNoOptions() {
/* 130 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 131 */     arrayList.add(new SearchConditionOption("", ""));
/* 132 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(383519, this.user.getLanguage())));
/* 133 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(383520, this.user.getLanguage())));
/* 134 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getSingleLinkOptions() {
/* 139 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 140 */     arrayList.add(new SearchConditionOption("", ""));
/* 141 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(608, this.user.getLanguage())));
/* 142 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(696, this.user.getLanguage())));
/* 143 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(697, this.user.getLanguage())));
/* 144 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(18004, this.user.getLanguage())));
/* 145 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(22395, this.user.getLanguage())));
/* 146 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getBrowserLinkOptions() {
/* 151 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 152 */     arrayList.add(new SearchConditionOption("", ""));
/*     */     
/* 154 */     String str1 = "10, 11, 64, 6, 56, 5, 3, 26,235,242,243,246,224,225,14,15,267,261,258,264,265,33,266,226";
/*     */     
/* 156 */     String str2 = "select w.typeid as groupid,w.id as itemid,w.labelid as itemlabel,w.orderid as orderid from  workflow_browserurl w left join HtmlLabelInfo h on w.labelid=h.indexid where h.languageid=" + this.user.getLanguage() + " and w.id not in (" + str1 + ") and w.useable = 1 order by groupid,orderid asc";
/*     */     
/* 158 */     RecordSet recordSet = new RecordSet();
/* 159 */     recordSet.execute(str2);
/*     */     
/* 161 */     while (recordSet.next()) {
/* 162 */       String str = recordSet.getString("itemid");
/* 163 */       int i = Util.getIntValue(recordSet.getString("itemlabel"), 0);
/*     */       
/* 165 */       arrayList.add(new SearchConditionOption(str, SystemEnv.getHtmlLabelName(i, this.user.getLanguage())));
/*     */     } 
/*     */     
/* 168 */     return arrayList;
/*     */   }
/*     */   private List<SearchConditionOption> getUploadLinkOptions() {
/* 171 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 172 */     arrayList.add(new SearchConditionOption("", ""));
/* 173 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(20798, this.user.getLanguage())));
/* 174 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(20001, this.user.getLanguage())));
/* 175 */     return arrayList;
/*     */   }
/*     */   private List<SearchConditionOption> getSpecialLinkOptions() {
/* 178 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 179 */     arrayList.add(new SearchConditionOption("", ""));
/* 180 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(21692, this.user.getLanguage())));
/* 181 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(21693, this.user.getLanguage())));
/* 182 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> geHtmlTypetOptions() {
/* 187 */     String str = Util.null2String(this.params.get("tableType"));
/*     */     
/* 189 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 190 */     arrayList.add(new SearchConditionOption("", ""));
/* 191 */     arrayList.add(new SearchConditionOption(FieldHtmlType.SINGLELINE.getIdStr(), FieldHtmlType.SINGLELINE.getLabelName(this.user.getLanguage())));
/* 192 */     arrayList.add(new SearchConditionOption(FieldHtmlType.MUTILALINE.getIdStr(), FieldHtmlType.MUTILALINE.getLabelName(this.user.getLanguage())));
/* 193 */     arrayList.add(new SearchConditionOption(FieldHtmlType.BROWSER.getIdStr(), FieldHtmlType.BROWSER.getLabelName(this.user.getLanguage())));
/* 194 */     arrayList.add(new SearchConditionOption(FieldHtmlType.CHECK.getIdStr(), FieldHtmlType.CHECK.getLabelName(this.user.getLanguage())));
/* 195 */     arrayList.add(new SearchConditionOption(FieldHtmlType.SELECT.getIdStr(), FieldHtmlType.SELECT.getLabelName(this.user.getLanguage())));
/* 196 */     arrayList.add(new SearchConditionOption(FieldHtmlType.UPLOAD.getIdStr(), FieldHtmlType.UPLOAD.getLabelName(this.user.getLanguage())));
/* 197 */     if ("mainfield".equals(str)) {
/* 198 */       arrayList.add(new SearchConditionOption(FieldHtmlType.SPECIALFIELD.getIdStr(), FieldHtmlType.SPECIALFIELD.getLabelName(this.user.getLanguage())));
/* 199 */       arrayList.add(new SearchConditionOption(FieldHtmlType.MOBILE.getIdStr(), FieldHtmlType.MOBILE.getLabelName(this.user.getLanguage())));
/*     */     } 
/* 201 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/LoadConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */