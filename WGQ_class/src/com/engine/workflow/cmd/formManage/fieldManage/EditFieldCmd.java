/*     */ package com.engine.workflow.cmd.formManage.fieldManage;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.FormFieldSetBiz;
/*     */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*     */ import com.engine.workflow.util.FormAuthorityUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EditFieldCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private boolean templateField = false;
/*     */   private int formId;
/*     */   private int isBill;
/*     */   private int fieldId;
/*     */   private int isFromMode;
/*  43 */   private String tableType = "mainfield";
/*     */   
/*  45 */   private int fieldNameLen = 30;
/*     */   
/*     */   private boolean isDetail = false;
/*     */   
/*     */   public EditFieldCmd(Map<String, Object> paramMap, User paramUser) {
/*  50 */     this.params = paramMap;
/*  51 */     this.user = paramUser;
/*  52 */     this.templateField = Boolean.parseBoolean(Util.null2String(this.params.get("templateField")));
/*  53 */     this.formId = Util.getIntValue(Util.null2String(paramMap.get("formId")));
/*  54 */     this.isBill = Util.getIntValue(Util.null2String(paramMap.get("isBill")));
/*  55 */     this.fieldId = Util.getIntValue(Util.null2String(paramMap.get("fieldId")));
/*  56 */     this.isFromMode = Util.getIntValue(Util.null2String(paramMap.get("isFromMode")), 0);
/*  57 */     this.tableType = Util.null2String(paramMap.get("tableType"));
/*  58 */     this.isDetail = "detailfield".equals(this.tableType);
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  63 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*     */     Map<String, Boolean> map;
/*  69 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  70 */     if (!checkRight()) {
/*  71 */       hashMap1.put("userRight", Boolean.valueOf(false));
/*  72 */       return (Map)hashMap1;
/*     */     } 
/*  74 */     hashMap1.put("userRight", Boolean.valueOf(true));
/*     */     
/*  76 */     String str1 = SystemEnv.getHtmlLabelName(125019, this.user.getLanguage());
/*  77 */     if (this.isDetail) {
/*  78 */       str1 = SystemEnv.getHtmlLabelName(125279, this.user.getLanguage());
/*     */     }
/*  80 */     if (this.isBill == 1) {
/*  81 */       str1 = SystemEnv.getHtmlLabelName(17998, this.user.getLanguage());
/*     */     }
/*     */     
/*  84 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  85 */     FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(this.formId, this.isBill, this.user, this.isDetail);
/*     */     
/*     */     try {
/*  88 */       map = formFieldSetBiz.getFieldInfo(this.fieldId, this.user.getLanguage());
/*  89 */       map.put("isUsed", Boolean.valueOf(formFieldSetBiz.isUsed(this.fieldId, this.isFromMode)));
/*  90 */       map.put("hasData", Boolean.valueOf(formFieldSetBiz.hasData(this.formId, this.isBill, this.isDetail, this.fieldId, this.isFromMode)));
/*  91 */       map.put("maxLength", Integer.valueOf(fieldDBValueLength((Map)map)));
/*  92 */       map.put("tableType", this.tableType);
/*  93 */       map.put("isBill", Integer.valueOf(this.isBill));
/*  94 */       map.put("templateField", Boolean.valueOf(this.templateField));
/*  95 */     } catch (Exception exception) {
/*  96 */       exception.printStackTrace();
/*     */     } 
/*  98 */     if (this.fieldId != -1) {
/*  99 */       str1 = SystemEnv.getHtmlLabelName(125021, this.user.getLanguage());
/* 100 */       if (this.isDetail) {
/* 101 */         str1 = SystemEnv.getHtmlLabelName(125041, this.user.getLanguage());
/*     */       }
/* 103 */       if (this.isBill == 1) {
/* 104 */         str1 = SystemEnv.getHtmlLabelName(15449, this.user.getLanguage()) + "：" + Util.formatMultiLang(Util.null2String(map.get("description")));
/*     */       }
/*     */     } 
/* 107 */     String str2 = Util.null2String(map.get("fieldName"));
/* 108 */     String str3 = Util.null2String(map.get("description"));
/* 109 */     String str4 = (this.fieldId != -1) ? Util.null2String(map.get("istemplate")) : "1";
/* 110 */     String str5 = Util.null2String(map.get("ownerTable"));
/* 111 */     String str6 = Util.null2String(map.get("orderNo"));
/*     */     
/* 113 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 115 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/* 117 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 124937, "fieldName");
/* 118 */     String str7 = String.format("%s%s", new Object[] { SystemEnv.getHtmlLabelName(84603, this.user.getLanguage()), SystemEnv.getHtmlLabelName(21763, this.user.getLanguage()) });
/*     */     
/* 120 */     boolean bool1 = formFieldSetBiz.isUsed(this.fieldId, this.isFromMode);
/* 121 */     if (!bool1) searchConditionItem1.setHelpfulTip(str7); 
/* 122 */     searchConditionItem1.setViewAttr(bool1 ? 1 : 3);
/* 123 */     searchConditionItem1.setLabelcol(8);
/* 124 */     searchConditionItem1.setFieldcol(10);
/* 125 */     searchConditionItem1.setValue(str2);
/* 126 */     searchConditionItem1.setLength(this.fieldNameLen);
/* 127 */     searchConditionItem1.setRules("required");
/* 128 */     searchConditionItem1.setOtherParams(new HashMap<>());
/* 129 */     searchConditionItem1.getOtherParams().put("regExp", "^[a-zA-Z]+[0-9_a-zA-Z]*$");
/*     */ 
/*     */     
/* 132 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 15456, "description");
/* 133 */     searchConditionItem2.setViewAttr(3);
/* 134 */     searchConditionItem2.setLabelcol(8);
/* 135 */     searchConditionItem2.setFieldcol(10);
/* 136 */     searchConditionItem2.setValue(TextUtil.toBase64ForMultilang(str3));
/* 137 */     searchConditionItem2.setLength(100);
/* 138 */     searchConditionItem2.setRules("required");
/* 139 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 140 */     hashMap3.put("inputType", "multilang");
/* 141 */     hashMap3.put("isBase64", Boolean.valueOf(true));
/* 142 */     searchConditionItem2.setOtherParams(hashMap3);
/*     */     
/* 144 */     arrayList.add(searchConditionItem2);
/* 145 */     if (this.isBill != 1 || this.formId <= 0) arrayList.add(searchConditionItem1);
/*     */     
/* 147 */     if (this.templateField) {
/* 148 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SWITCH, 383669, "istemplate");
/* 149 */       searchConditionItem.setValue(Boolean.valueOf(true));
/* 150 */       searchConditionItem.setLabelcol(8);
/* 151 */       searchConditionItem.setFieldcol(10);
/* 152 */       searchConditionItem.setValue(str4);
/* 153 */       searchConditionItem.setHelpfulTip(SystemEnv.getHtmlLabelName(385198, this.user.getLanguage()));
/* 154 */       searchConditionItem.getHelpfulTipProps().put("width", "150");
/* 155 */       arrayList.add(searchConditionItem);
/*     */     } else {
/*     */       
/* 158 */       SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT, 17997, "ownerTable");
/* 159 */       searchConditionItem3.setOptions(getTableOption(str5));
/* 160 */       searchConditionItem3.setLabelcol(8);
/* 161 */       searchConditionItem3.setFieldcol(10);
/*     */       
/* 163 */       if ((this.fieldId != -1 && isFormUsed(this.formId)) || (this.isBill == 1 && this.formId > 0)) {
/* 164 */         searchConditionItem3.setViewAttr(1);
/*     */       }
/* 166 */       arrayList.add(searchConditionItem3);
/*     */       
/* 168 */       SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.INPUT, 129806, "orderNo");
/* 169 */       searchConditionItem4.setLabelcol(8);
/* 170 */       searchConditionItem4.setFieldcol(10);
/* 171 */       if (!Strings.isNullOrEmpty(str6)) {
/* 172 */         searchConditionItem4.setValue(str6);
/*     */       }
/* 174 */       searchConditionItem4.setRegExp("^\\-?([0-9]{1,}[.]?[0-9]*)$");
/* 175 */       searchConditionItem4.setLength(7);
/* 176 */       arrayList.add(searchConditionItem4);
/*     */     } 
/*     */     
/* 179 */     boolean bool2 = "1".equals(Util.null2String(this.params.get("isFromMode")));
/* 180 */     if (!bool2) {
/* 181 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 517198, "groupid");
/* 182 */       searchConditionItem.setLabelcol(8);
/* 183 */       searchConditionItem.setFieldcol(10);
/* 184 */       searchConditionItem.setOptions(FieldGroupBiz.getFieldGroups(this.formId, String.valueOf(this.isBill)));
/*     */       
/* 186 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */     
/* 189 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 190 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 191 */     hashMap4.put("items", arrayList);
/* 192 */     arrayList1.add(hashMap4);
/*     */     
/* 194 */     hashMap1.put("condition", arrayList1);
/* 195 */     hashMap1.put("fieldInfo", map);
/* 196 */     hashMap1.put("title", str1);
/* 197 */     if (this.isBill == 1) {
/* 198 */       FormAuthorityUtil formAuthorityUtil = new FormAuthorityUtil();
/* 199 */       hashMap1.put("operateLevel", Integer.valueOf(formAuthorityUtil.getFormOperateLevel(this.formId, this.isBill, this.user, this.isFromMode)));
/*     */     } 
/* 201 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getTableOption(String paramString) {
/* 210 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 212 */     RecordSet recordSet1 = new RecordSet();
/* 213 */     recordSet1.executeQuery("select tablename from workflow_bill where id=" + this.formId, new Object[0]);
/* 214 */     String str = "";
/* 215 */     if (recordSet1.next()) {
/* 216 */       str = Util.null2String(recordSet1.getString("tablename"));
/*     */     }
/* 218 */     arrayList.add(new SearchConditionOption(str, SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()), Strings.isNullOrEmpty(paramString) ? true : paramString.equals(str)));
/* 219 */     RecordSet recordSet2 = new RecordSet();
/* 220 */     recordSet2.executeQuery("select tablename from Workflow_billdetailtable where billid=" + this.formId + " order by orderid", new Object[0]);
/* 221 */     byte b = 1;
/* 222 */     while (recordSet2.next()) {
/* 223 */       String str1 = Util.null2String(recordSet2.getString("tablename"));
/* 224 */       arrayList.add(new SearchConditionOption(str1, String.format("%s%s", new Object[] { SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()), Integer.valueOf(b) }), str1.equals(paramString)));
/* 225 */       b++;
/*     */     } 
/* 227 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isFormUsed(int paramInt) {
/* 237 */     boolean bool = false;
/* 238 */     RecordSet recordSet = new RecordSet();
/* 239 */     recordSet.execute("select 1 from workflow_base where formid=" + paramInt);
/* 240 */     if (recordSet.getCounts() > 0) {
/* 241 */       bool = true;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 255 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   private int fieldDBValueLength(Map<String, Object> paramMap) {
/* 260 */     int i = 0;
/* 261 */     if (this.fieldId != -1) {
/*     */       
/* 263 */       String str1 = Util.null2String(paramMap.get("fieldName"));
/* 264 */       String str2 = Util.null2String(paramMap.get("ownerTable"));
/*     */       
/* 266 */       FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(this.formId, this.isBill, this.user, this.isDetail);
/* 267 */       boolean bool = formFieldSetBiz.isUsed(this.fieldId, this.isFromMode);
/*     */       
/* 269 */       if (bool) {
/* 270 */         List list = (List)paramMap.get("fieldType");
/* 271 */         if (list != null) {
/* 272 */           String str = Util.null2String(list.get(0));
/* 273 */           if (!"input".equals(str)) {
/* 274 */             return i;
/*     */           }
/*     */         } 
/*     */         
/* 278 */         if (this.isBill == 0) {
/* 279 */           str2 = this.isDetail ? "workflow_formdetail" : "workflow_form";
/*     */         }
/*     */         
/* 282 */         if (!Strings.isNullOrEmpty(str2) && !Strings.isNullOrEmpty(str1)) {
/* 283 */           RecordSet recordSet = new RecordSet();
/* 284 */           if (recordSet.getDBType().equals("oracle")) {
/* 285 */             recordSet.execute("select max(lengthb(" + str1 + ")) from " + str2);
/* 286 */             if (recordSet.next()) {
/* 287 */               i = Util.getIntValue(recordSet.getString(1), 0);
/*     */             
/*     */             }
/*     */           
/*     */           }
/* 292 */           else if (recordSet.getDBType().equalsIgnoreCase("mysql")) {
/* 293 */             recordSet.execute("select MAX(CHAR_LENGTH(" + str1 + ")) from " + str2);
/* 294 */             if (recordSet.next()) {
/* 295 */               i = recordSet.getInt(1);
/*     */             }
/* 297 */           } else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 298 */             recordSet.execute("select MAX(CHAR_LENGTH(" + str1 + ")) from " + str2);
/* 299 */             if (recordSet.next()) {
/* 300 */               i = recordSet.getInt(1);
/*     */             }
/*     */           } else {
/* 303 */             recordSet.execute("select max(datalength(" + str1 + ")) from " + str2);
/* 304 */             if (recordSet.next())
/* 305 */               i = Util.getIntValue(recordSet.getString(1), 0); 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 310 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean checkRight() {
/* 320 */     if (this.templateField) {
/* 321 */       if (!HrmUserVarify.checkUserRight("FieldManage:All", this.user)) return false; 
/*     */     } else {
/* 323 */       FormAuthorityUtil formAuthorityUtil = new FormAuthorityUtil();
/* 324 */       int i = formAuthorityUtil.getFormOperateLevel(this.formId, this.isBill, this.user, this.isFromMode);
/* 325 */       if (i == -1) return false; 
/*     */     } 
/* 327 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/EditFieldCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */