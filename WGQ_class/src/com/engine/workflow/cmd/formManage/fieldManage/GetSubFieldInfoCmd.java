/*     */ package com.engine.workflow.cmd.formManage.fieldManage;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSubFieldInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetSubFieldInfoCmd(Map<String, Object> paramMap, User paramUser) {
/*  31 */     this.params = paramMap;
/*  32 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  37 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  44 */     if (!HrmUserVarify.checkUserRight("FieldManage:All", this.user)) {
/*  45 */       hashMap.put("userRight", Boolean.valueOf(false));
/*  46 */       return (Map)hashMap;
/*     */     } 
/*  48 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  49 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*     */     
/*  51 */     int i = Util.getIntValue(Util.null2String(this.params.get("isBill")));
/*  52 */     int j = Util.getIntValue(Util.null2String(this.params.get("fieldId")));
/*  53 */     int k = Util.getIntValue(Util.null2String(this.params.get("isDetail")));
/*     */     
/*  55 */     String str = "";
/*  56 */     if (i == 0) {
/*     */       
/*  58 */       String str1 = (k == 1) ? "workflow_formdictdetail" : "workflow_formdict";
/*  59 */       str = "select a.id,a.fieldname,a.description from " + str1 + " a,(select childfieldid from " + str1 + " where id = " + j + ") t where t.childfieldid = a.id";
/*     */ 
/*     */       
/*  62 */       String str2 = "";
/*  63 */       RecordSet recordSet1 = new RecordSet();
/*  64 */       recordSet1.execute(str);
/*  65 */       while (recordSet1.next()) {
/*  66 */         str2 = recordSet1.getString("id");
/*  67 */         String str4 = recordSet1.getString("description");
/*  68 */         if (Strings.isNullOrEmpty(str4)) {
/*  69 */           str4 = recordSet1.getString("fieldname");
/*     */         }
/*  71 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  72 */         hashMap2.put("id", str2);
/*  73 */         hashMap2.put("name", str4);
/*  74 */         arrayList1.add(hashMap2);
/*     */       } 
/*     */ 
/*     */       
/*  78 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  79 */       if (!Strings.isNullOrEmpty(str2)) {
/*     */         
/*  81 */         String str4 = "select selectvalue,selectname form workflow_SelectItem where fieldId =" + str2;
/*  82 */         RecordSet recordSet = new RecordSet();
/*  83 */         recordSet.execute(str4);
/*  84 */         while (recordSet.next()) {
/*     */           
/*  86 */           String str5 = recordSet.getString("selectvalue");
/*  87 */           String str6 = recordSet.getString("selectname");
/*  88 */           hashMap1.put(str5, str6);
/*     */         } 
/*     */       } 
/*     */       
/*  92 */       String str3 = "select * from workflow_SelectItem where fieldid = " + j + "and isbill=" + i + "order by listorder asc";
/*  93 */       RecordSet recordSet2 = new RecordSet();
/*  94 */       recordSet2.execute(str3);
/*  95 */       while (recordSet2.next())
/*     */       {
/*  97 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */         
/*  99 */         String str4 = recordSet2.getString("selectvalue");
/* 100 */         String str5 = recordSet2.getString("selectname");
/* 101 */         int m = recordSet2.getInt("id");
/* 102 */         int n = recordSet2.getInt("listorder");
/* 103 */         String str6 = recordSet2.getString("isdefault").equals("y") ? "1" : "0";
/* 104 */         String str7 = recordSet2.getString("isAccordToSubCom");
/* 105 */         String str8 = recordSet2.getString("cancel");
/*     */ 
/*     */         
/* 108 */         String str9 = recordSet2.getString("docpath");
/* 109 */         String str10 = recordSet2.getString("doccategory");
/* 110 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 111 */         hashMap3.put("id", str10);
/* 112 */         hashMap3.put("name", str9);
/*     */ 
/*     */         
/* 115 */         String str11 = recordSet2.getString("childitemid");
/* 116 */         ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */         
/* 118 */         if (!Strings.isNullOrEmpty(str11)) {
/* 119 */           String[] arrayOfString = str11.split(",");
/* 120 */           for (String str12 : arrayOfString) {
/* 121 */             String str13 = Util.null2String(hashMap1.get(str12));
/* 122 */             HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 123 */             hashMap4.put("id", str12);
/* 124 */             hashMap4.put("name", str13);
/* 125 */             arrayList.add(hashMap4);
/*     */           } 
/*     */         } 
/*     */         
/* 129 */         hashMap2.put("id", Integer.valueOf(m));
/* 130 */         hashMap2.put("selectValue", str4);
/* 131 */         hashMap2.put("selectName", str5);
/* 132 */         hashMap2.put("listOrder", Integer.valueOf(n));
/* 133 */         hashMap2.put("isDefault", str6);
/* 134 */         hashMap2.put("isAccordToSubCom", str7);
/* 135 */         hashMap2.put("cancel", str8);
/* 136 */         hashMap2.put("doc", hashMap3);
/* 137 */         hashMap2.put("item", arrayList);
/*     */         
/* 139 */         arrayList2.add(hashMap2);
/*     */       }
/*     */     
/* 142 */     } else if (i == 1) {
/*     */     
/*     */     } 
/*     */     
/* 146 */     hashMap.put("replaceDatas", arrayList1);
/* 147 */     hashMap.put("datas", arrayList2);
/* 148 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/GetSubFieldInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */