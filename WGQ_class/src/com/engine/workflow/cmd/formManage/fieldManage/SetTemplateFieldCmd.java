/*    */ package com.engine.workflow.cmd.formManage.fieldManage;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.field.DetailFieldComInfo;
/*    */ import weaver.workflow.field.FieldComInfo;
/*    */ import weaver.workflow.field.FieldManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SetTemplateFieldCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public SetTemplateFieldCmd(Map<String, Object> paramMap, User paramUser) {
/* 31 */     this.params = paramMap;
/* 32 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 37 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 44 */     if (!HrmUserVarify.checkUserRight("FieldManage:All", this.user)) {
/* 45 */       hashMap.put("userRight", Boolean.valueOf(false));
/* 46 */       return (Map)hashMap;
/*    */     } 
/* 48 */     String str = Util.null2String(this.params.get("tableType"));
/* 49 */     int i = Util.getIntValue(Util.null2String(this.params.get("fieldId")));
/*    */     
/*    */     try {
/* 52 */       FieldManager fieldManager = new FieldManager();
/* 53 */       fieldManager.setFieldid(i);
/* 54 */       fieldManager.setAction("editfield");
/* 55 */       if ("mainfield".equals(str)) {
/* 56 */         fieldManager.getFieldInfo();
/*    */       } else {
/* 58 */         fieldManager.getDetailFieldInfo();
/*    */       } 
/* 60 */       fieldManager.setIstemplate("1");
/*    */       
/* 62 */       if ("mainfield".equals(str)) {
/* 63 */         fieldManager.setFieldInfo();
/* 64 */         (new FieldComInfo()).removeFieldCache();
/*    */       } else {
/* 66 */         fieldManager.setDetailFieldInfo();
/* 67 */         (new DetailFieldComInfo()).removeFieldCache();
/*    */       } 
/* 69 */       hashMap.put("success", Boolean.valueOf(true));
/* 70 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(16746, this.user.getLanguage()));
/* 71 */     } catch (Exception exception) {
/* 72 */       hashMap.put("success", Boolean.valueOf(false));
/* 73 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(125191, this.user.getLanguage()));
/* 74 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 77 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/SetTemplateFieldCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */