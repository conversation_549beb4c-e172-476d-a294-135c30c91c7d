/*     */ package com.engine.workflow.cmd.formManage.fieldManage;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.FormFieldSetBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.DetailFieldComInfo;
/*     */ import weaver.workflow.field.FieldComInfo;
/*     */ import weaver.workflow.field.FieldMainManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DelFieldCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  35 */   private List<BizLogContext> bizLogContexts = new ArrayList<>();
/*  36 */   private BizLogContext bizLogContext = null;
/*     */ 
/*     */   
/*     */   public DelFieldCmd(Map<String, Object> paramMap, User paramUser) {
/*  40 */     this.params = paramMap;
/*  41 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  47 */     return this.bizLogContexts;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  54 */     if (!HrmUserVarify.checkUserRight("FieldManage:All", this.user)) {
/*  55 */       hashMap.put("userRight", Boolean.valueOf(false));
/*  56 */       return (Map)hashMap;
/*     */     } 
/*  58 */     String str1 = Util.null2String(this.params.get("tableType"));
/*  59 */     String str2 = Util.null2String(this.params.get("fieldId"));
/*  60 */     String[] arrayOfString = str2.split(",");
/*     */     
/*     */     try {
/*  63 */       dealLogContext(arrayOfString);
/*     */       
/*  65 */       FieldMainManager fieldMainManager = new FieldMainManager();
/*  66 */       if ("mainfield".equals(str1)) {
/*  67 */         fieldMainManager.DeleteField(arrayOfString);
/*  68 */         FieldComInfo fieldComInfo = new FieldComInfo();
/*  69 */         fieldComInfo.removeFieldCache();
/*     */       } else {
/*  71 */         fieldMainManager.DeleteDetailField(arrayOfString);
/*  72 */         DetailFieldComInfo detailFieldComInfo = new DetailFieldComInfo();
/*  73 */         detailFieldComInfo.removeFieldCache();
/*     */       } 
/*  75 */       hashMap.put("success", Boolean.valueOf(true));
/*  76 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(129429, this.user.getLanguage()));
/*     */     
/*     */     }
/*  79 */     catch (Exception exception) {
/*  80 */       exception.printStackTrace();
/*  81 */       writeLog(exception);
/*  82 */       hashMap.put("success", Boolean.valueOf(false));
/*  83 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(129430, this.user.getLanguage()));
/*     */       
/*  85 */       this.bizLogContexts = new ArrayList<>();
/*     */       
/*  87 */       this.bizLogContext = new BizLogContext();
/*  88 */       this.bizLogContext.setDateObject(new Date());
/*  89 */       this.bizLogContext.setUserid(this.user.getUID());
/*  90 */       this.bizLogContext.setBelongTypeTargetId("fieldTemplateLib");
/*  91 */       this.bizLogContext.setBelongTypeTargetName("fieldTemplateLib");
/*  92 */       this.bizLogContext.setTargetId(str2);
/*  93 */       this.bizLogContext.setDesc("" + SystemEnv.getHtmlLabelName(10005595, ThreadVarLanguage.getLang()) + ":" + exception.getMessage());
/*  94 */       this.bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  95 */       this.bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  96 */       this.bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_TEMPLATELIB_FIELD);
/*  97 */       this.bizLogContext.setOperateType(BizLogOperateType.DELETE);
/*  98 */       this.bizLogContext.setParams(this.params);
/*  99 */       this.bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*     */     } 
/*     */     
/* 102 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void dealLogContext(String[] paramArrayOfString) throws Exception {
/* 112 */     String str = Util.null2String(this.params.get("tableType"));
/* 113 */     FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(0, 0, this.user, "detailfield".equals(str));
/*     */     
/* 115 */     for (String str1 : paramArrayOfString) {
/*     */       
/* 117 */       Map map = formFieldSetBiz.getFieldInfo(Util.getIntValue(str1), this.user.getLanguage());
/*     */       
/* 119 */       String str2 = Util.null2String(map.get("description"));
/*     */       
/* 121 */       BizLogContext bizLogContext = new BizLogContext();
/*     */       
/* 123 */       bizLogContext.setDateObject(new Date());
/* 124 */       bizLogContext.setUserid(this.user.getUID());
/* 125 */       bizLogContext.setParams(this.params);
/* 126 */       bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 127 */       bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 128 */       bizLogContext.setOldValues(map);
/* 129 */       bizLogContext.setDesc("" + SystemEnv.getHtmlLabelName(702, ThreadVarLanguage.getLang()) + "[" + str2 + "]");
/* 130 */       bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/* 131 */       bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_TEMPLATELIB_FIELD);
/* 132 */       bizLogContext.setTargetId(str1);
/* 133 */       bizLogContext.setTargetName(str2);
/* 134 */       bizLogContext.setOperateType(BizLogOperateType.DELETE);
/*     */       
/* 136 */       if ("detailfield".equals(str)) {
/* 137 */         bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_TEMPLATELIB_DETAILFIELD);
/*     */       } else {
/* 139 */         bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_TEMPLATELIB_MAINFIELD);
/*     */       } 
/*     */       
/* 142 */       this.bizLogContexts.add(bizLogContext);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 150 */     return this.bizLogContext;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/fieldManage/DelFieldCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */