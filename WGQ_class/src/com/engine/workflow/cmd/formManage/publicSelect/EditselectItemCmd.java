/*     */ package com.engine.workflow.cmd.formManage.publicSelect;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.entity.PublicSelectEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.SecCategoryComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EditselectItemCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  39 */   private final int FIELD_COL_VALUE = 8;
/*  40 */   private final int LABEL_COL_VALUE = 10;
/*     */   private boolean canel = true;
/*  42 */   private ConditionFactory conditionFactory = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  51 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  56 */     if (!HrmUserVarify.checkUserRight("WORKFLOWPUBLICCHOICE:VIEW", this.user)) {
/*  57 */       return null;
/*     */     }
/*  59 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  60 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  61 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  62 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  63 */     hashMap2.put("items", getItemList());
/*  64 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(81711, this.user.getLanguage()));
/*  65 */     arrayList.add(hashMap2);
/*  66 */     hashMap1.put("conditioninfo", arrayList);
/*  67 */     hashMap1.put("columns", getColumns());
/*  68 */     hashMap1.put("datas", getdatas());
/*  69 */     hashMap1.put("canel", Boolean.valueOf(this.canel));
/*  70 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<SearchConditionItem> getItemList() {
/*  83 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  84 */     int i = Util.getIntValue(Util.null2String(this.params.get("id")), -1);
/*  85 */     RecordSet recordSet = new RecordSet();
/*  86 */     String str1 = "";
/*  87 */     String str2 = "";
/*  88 */     if (i > 0) {
/*  89 */       String str = "SELECT id,selectitemname,selectitemdesc,formids,operatetime FROM mode_selectitempage WHERE id=?";
/*  90 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(i) });
/*  91 */       if (recordSet.next()) {
/*  92 */         str1 = Util.null2String(recordSet.getString("selectitemname"));
/*  93 */         str2 = Util.null2String(recordSet.getString("selectitemdesc"));
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  98 */     SearchConditionItem searchConditionItem1 = this.conditionFactory.createCondition(ConditionType.INPUT, 195, "name");
/*  99 */     str1 = TextUtil.toBase64ForMultilang(str1);
/* 100 */     searchConditionItem1.setValue(str1);
/* 101 */     searchConditionItem1.setFieldcol(8);
/* 102 */     searchConditionItem1.setLabelcol(10);
/* 103 */     searchConditionItem1.setViewAttr(3);
/* 104 */     arrayList.add(searchConditionItem1);
/* 105 */     searchConditionItem1.setRules("required|stringLength:200");
/* 106 */     searchConditionItem1.setStringLength(200);
/* 107 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 108 */     hashMap1.put("inputType", "multilang");
/* 109 */     hashMap1.put("isBase64", Boolean.valueOf(true));
/* 110 */     searchConditionItem1.setOtherParams(hashMap1);
/*     */ 
/*     */     
/* 113 */     SearchConditionItem searchConditionItem2 = this.conditionFactory.createCondition(ConditionType.TEXTAREA, 433, "desc");
/* 114 */     searchConditionItem2.setValue(str2);
/* 115 */     searchConditionItem2.setFieldcol(8);
/* 116 */     searchConditionItem2.setLabelcol(10);
/* 117 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 118 */     hashMap2.put("maxRows", Integer.valueOf(4));
/* 119 */     hashMap2.put("minRows", Integer.valueOf(4));
/* 120 */     hashMap2.put("stringLength", Integer.valueOf(500));
/*     */ 
/*     */     
/* 123 */     searchConditionItem2.setOtherParams(hashMap2);
/* 124 */     searchConditionItem1.setViewAttr(3);
/* 125 */     arrayList.add(searchConditionItem2);
/*     */     
/* 127 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<WeaTableEditEntity> getColumns() {
/* 137 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */     
/* 139 */     arrayList.add((new WeaTableEditEntity()).setTitle("ID").setKey("id").setDataIndex("id")
/* 140 */         .setColSpan("1").setWidth("10%").setClassName("wea-table-edit-id").setCom(getComs("", ConditionType.TEXT, "1", "id", 120, 1)));
/*     */     
/* 142 */     arrayList.add((new WeaTableEditEntity()).setTitle(SystemEnv.getHtmlLabelName(15442, this.user.getLanguage()))
/* 143 */         .setKey("optiontext").setDataIndex("optiontext").setColSpan("1").setWidth("18%")
/* 144 */         .setClassName("wea-table-edit-optiontext").setCom(getComs("", ConditionType.INPUT, "3", "optiontext", 120, 3)));
/*     */ 
/*     */ 
/*     */     
/* 148 */     arrayList.add((new WeaTableEditEntity()).setTitle(SystemEnv.getHtmlLabelName(149, this.user.getLanguage())).setKey("defaultvalue").setDataIndex("defaultvalue")
/* 149 */         .setColSpan("1").setWidth("8%").setClassName("wea-table-edit-defaultvalue").setCom(getComs("", ConditionType.CHECKBOX, "1", "defaultvalue", 120, 2)));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 156 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/* 159 */     WeaTableEditComEntity weaTableEditComEntity1 = new WeaTableEditComEntity();
/* 160 */     weaTableEditComEntity1.setType(ConditionType.BROWSER);
/* 161 */     BrowserBean browserBean = new BrowserBean();
/* 162 */     browserBean.setType("doccategory");
/* 163 */     browserBean.setIcon("icon-coms-workflow");
/* 164 */     browserBean.setIconBgcolor("#0079DE");
/* 165 */     browserBean.setTitle(SystemEnv.getHtmlLabelName(128937, this.user.getLanguage()));
/* 166 */     weaTableEditComEntity1.setBrowserConditionParam(browserBean);
/* 167 */     weaTableEditComEntity1.setKey("maincategory");
/*     */ 
/*     */     
/* 170 */     arrayList1.add(weaTableEditComEntity1);
/*     */ 
/*     */     
/* 173 */     arrayList.add((new WeaTableEditEntity()).setTitle(SystemEnv.getHtmlLabelName(19207, this.user.getLanguage()))
/* 174 */         .setKey("maincategory").setDataIndex("maincategory")
/* 175 */         .setColSpan("1").setWidth("18%").setClassName("wea-table-edit-maincategory").setCom(arrayList1));
/*     */ 
/*     */ 
/*     */     
/* 179 */     ArrayList<WeaTableEditComEntity> arrayList2 = new ArrayList();
/* 180 */     WeaTableEditComEntity weaTableEditComEntity2 = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "isAccordToSubCom", 120);
/* 181 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 182 */     hashMap.put("content", SystemEnv.getHtmlLabelName(22878, this.user.getLanguage()));
/* 183 */     weaTableEditComEntity2.setOtherParams(hashMap);
/* 184 */     arrayList2.add(weaTableEditComEntity2);
/*     */ 
/*     */ 
/*     */     
/* 188 */     arrayList.add((new WeaTableEditEntity()).setTitle("").setKey("isAccordToSubCom").setDataIndex("isAccordToSubCom")
/* 189 */         .setColSpan("1").setWidth("17%").setClassName("wea-table-edit-isAccordToSubCom").setCom(arrayList2));
/*     */ 
/*     */     
/* 192 */     arrayList.add((new WeaTableEditEntity()).setTitle(SystemEnv.getHtmlLabelName(22663, this.user.getLanguage()))
/* 193 */         .setKey("suboptions").setDataIndex("id").setColSpan("1").setWidth("14%").setClassName("wea-table-edit-more"));
/*     */ 
/*     */     
/* 196 */     arrayList.add((new WeaTableEditEntity()).setTitle(SystemEnv.getHtmlLabelName(22151, this.user.getLanguage())).setKey("canel")
/* 197 */         .setDataIndex("canel").setColSpan("1").setWidth("10%").setClassName("wea-table-edit-canel").setCom(getComs("", ConditionType.CHECKBOX, "1", "canel", 120, 2)));
/*     */ 
/*     */ 
/*     */     
/* 201 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<PublicSelectEntity> getdatas() {
/* 216 */     ArrayList<PublicSelectEntity> arrayList = new ArrayList();
/* 217 */     int i = Util.getIntValue(Util.null2String(this.params.get("id")));
/* 218 */     boolean bool = false;
/* 219 */     int j = Util.getIntValue(Util.null2String(this.params.get("statelev")), 1);
/* 220 */     int k = Util.getIntValue(Util.null2String(this.params.get("pid")), 0);
/* 221 */     SecCategoryComInfo secCategoryComInfo = new SecCategoryComInfo();
/* 222 */     RecordSet recordSet1 = new RecordSet();
/*     */     
/* 224 */     String str1 = "SELECT s.selectvalue FROM workflow_billfield b , workflow_SelectItem s WHERE b.id=s.fieldid AND b.selectItemType IN ('1','2') AND s.pubid in (select id from mode_selectitempagedetail d where d.mainid = " + i + " and d.pid=" + k + ")";
/*     */ 
/*     */     
/* 227 */     recordSet1.executeSql(str1);
/* 228 */     if (recordSet1.next()) {
/* 229 */       this.canel = false;
/*     */     }
/*     */     
/* 232 */     RecordSet recordSet2 = new RecordSet();
/* 233 */     String str2 = "select b.*,(select count(1) from mode_selectitempagedetail a where a.pid=b.id ) as subcount from mode_selectitempagedetail b where mainid = " + i + " and statelev='" + j + "' and pid='" + k + "' order by disorder asc,id asc ";
/*     */ 
/*     */     
/* 236 */     recordSet2.executeSql(str2);
/* 237 */     while (recordSet2.next()) {
/* 238 */       String str3 = recordSet2.getString("id");
/* 239 */       String str4 = Util.null2String(recordSet2.getString("name"));
/* 240 */       str4 = TextUtil.toBase64ForMultilang(str4);
/* 241 */       String str5 = Util.null2String(recordSet2.getString("defaultvalue"));
/* 242 */       String str6 = Util.null2String(recordSet2.getString("pathcategory"));
/* 243 */       String str7 = Util.null2String(recordSet2.getString("maincategory")).trim();
/* 244 */       String str8 = "";
/* 245 */       String str9 = recordSet2.getString("maincategory");
/* 246 */       String str10 = "";
/* 247 */       if (!"".equals(str9) && null != str9) {
/*     */         
/* 249 */         ArrayList<String> arrayList1 = Util.TokenizerString(str9, ",");
/*     */         try {
/* 251 */           String str12 = arrayList1.get(0);
/* 252 */           String str13 = arrayList1.get(1);
/* 253 */           str10 = arrayList1.get(2);
/* 254 */           str8 = secCategoryComInfo.getAllParentName(str10, true);
/* 255 */         } catch (Exception exception) {
/* 256 */           str8 = secCategoryComInfo.getAllParentName(str9, true);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 261 */       String str11 = Util.null2String(recordSet2.getString("pid"));
/* 262 */       int m = recordSet2.getInt("subcount");
/* 263 */       double d = Util.getDoubleValue(recordSet2.getString("disorder"), 0.0D);
/* 264 */       int n = Util.getIntValue(recordSet2.getString("cancel"), 0);
/* 265 */       int i1 = Util.getIntValue(recordSet2.getString("isAccordToSubCom"), 0);
/* 266 */       PublicSelectEntity publicSelectEntity = new PublicSelectEntity(str3, str4, str5, n + "", str10, str8, m);
/*     */       
/* 268 */       publicSelectEntity.setIsAccordToSubCom(i1);
/* 269 */       arrayList.add(publicSelectEntity);
/*     */     } 
/*     */     
/* 272 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<WeaTableEditComEntity> getComs(String paramString1, ConditionType paramConditionType, String paramString2, String paramString3, int paramInt1, int paramInt2) {
/* 281 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/* 282 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity(paramString1, paramConditionType, paramString2, paramString3, paramInt1);
/* 283 */     weaTableEditComEntity.setViewAttr(paramInt2);
/* 284 */     if (paramConditionType == ConditionType.INPUT) {
/* 285 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 286 */       hashMap.put("stringLength", Integer.valueOf(1000));
/* 287 */       hashMap.put("inputType", "multilang");
/* 288 */       hashMap.put("isBase64", Boolean.valueOf(true));
/* 289 */       weaTableEditComEntity.setOtherParams(hashMap);
/*     */     } 
/*     */     
/* 292 */     arrayList.add(weaTableEditComEntity);
/* 293 */     return arrayList;
/*     */   }
/*     */   
/*     */   public EditselectItemCmd(Map<String, Object> paramMap, User paramUser) {
/* 297 */     this.params = paramMap;
/* 298 */     this.user = paramUser;
/* 299 */     this.conditionFactory = new ConditionFactory(paramUser);
/*     */   }
/*     */   
/*     */   public EditselectItemCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/publicSelect/EditselectItemCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */