/*     */ package com.engine.workflow.cmd.formManage.publicSelect;
/*     */ 
/*     */ import com.api.browser.bean.Checkboxpopedom;
/*     */ import com.api.browser.bean.Operate;
/*     */ import com.api.browser.bean.Popedom;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.bean.SplitTableOperateBean;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSelectListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public BizLogContext getLogContext() {
/*  41 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  46 */     return getSelectList(this.params);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getSelectList(Map<String, Object> paramMap) {
/*  53 */     if (!HrmUserVarify.checkUserRight("WORKFLOWPUBLICCHOICE:VIEW", this.user)) {
/*  54 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  55 */       hashMap.put("info", Integer.valueOf(0));
/*  56 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  59 */     String str1 = Util.null2String(paramMap.get("selectitemname"));
/*  60 */     String str2 = Util.null2String(paramMap.get("selectitemdesc"));
/*  61 */     int i = Util.getIntValue(Util.null2String(paramMap.get("hasdetail")));
/*  62 */     byte b = 15;
/*  63 */     String str3 = " where 1=1 ";
/*     */     
/*  65 */     if (!"".equals(str1)) {
/*  66 */       str3 = str3 + " and selectitemname like '%" + str1 + "%' ";
/*     */     }
/*  68 */     if (!"".equals(str2)) {
/*  69 */       str3 = str3 + " and selectitemdesc like '%" + str2 + "%' ";
/*     */     }
/*  71 */     if (i == 0) {
/*  72 */       str3 = str3 + " and not EXISTS (SELECT 1 FROM mode_selectitempagedetail dt WHERE m.id=dt.mainid and dt.pid!=0) ";
/*     */     }
/*  74 */     if (i == 1) {
/*  75 */       str3 = str3 + " and EXISTS (SELECT 1 FROM mode_selectitempagedetail dt WHERE m.id=dt.mainid and dt.pid!=0) ";
/*     */     }
/*     */     
/*  78 */     String str4 = " operatetime ";
/*  79 */     String str5 = " id,selectitemname,selectitemdesc,operatetime,formids,(SELECT case when COUNT(1)>0 then 1 ELSE 0 end FROM mode_selectitempagedetail d WHERE d.mainid=m.id and d.pid!=0) AS hasdetail ";
/*  80 */     String str6 = " mode_selectitempage m ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 101 */     SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
/* 102 */     Popedom popedom = new Popedom("weaver.workflow.selectItem.SelectItemManager.getCanDelList", "", "id");
/* 103 */     ArrayList<Operate> arrayList = new ArrayList();
/* 104 */     Operate operate1 = new Operate(SystemEnv.getHtmlLabelName(93, this.user.getLanguage()), "javascript:workflowselectItem.newDialog();", "0");
/* 105 */     Operate operate2 = new Operate(SystemEnv.getHtmlLabelName(91, this.user.getLanguage()), "javascript:workflowselectItem.onDel();", "1");
/* 106 */     Operate operate3 = new Operate(SystemEnv.getHtmlLabelName(33364, this.user.getLanguage()), "javascript:workflowselectItem.onQuote();", "0");
/* 107 */     Operate operate4 = new Operate(SystemEnv.getHtmlLabelName(83, this.user.getLanguage()), "javascript:workflowselectItem.onLog();", "0");
/* 108 */     arrayList.add(operate1);
/* 109 */     arrayList.add(operate2);
/* 110 */     arrayList.add(operate3);
/* 111 */     arrayList.add(operate4);
/* 112 */     splitTableOperateBean.setOperate(arrayList);
/* 113 */     splitTableOperateBean.setPopedom(popedom);
/*     */     
/* 115 */     Checkboxpopedom checkboxpopedom = new Checkboxpopedom("checkbox", "weaver.workflow.selectItem.SelectItemManager.canDel", "column:id");
/*     */     
/* 117 */     ArrayList<SplitTableColBean> arrayList1 = new ArrayList();
/* 118 */     arrayList1.add(new SplitTableColBean("true", "id"));
/* 119 */     arrayList1.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "selectitemname", "selectitemname", "com.engine.workflow.cmd.formManage.publicSelect.GetSelectListCmd.getLinkSelectItem", "column:id"));
/* 120 */     arrayList1.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "selectitemdesc", "selectitemdesc"));
/* 121 */     arrayList1.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(124889, this.user.getLanguage()), "hasdetail", "hasdetail", "weaver.workflow.selectItem.SelectItemManager.hasDetail", this.user.getLanguage() + ""));
/* 122 */     arrayList1.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(25295, this.user.getLanguage()), "operatetime", "operatetime"));
/*     */     
/* 124 */     String str7 = "3460b6a6-25e1-4a92-a35a-c3fec76df8ca";
/* 125 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str3, str4, "id", arrayList1);
/* 126 */     splitTableBean.setPageUID(str7);
/* 127 */     splitTableBean.setPageID(str7);
/* 128 */     splitTableBean.setTableType("checkbox");
/* 129 */     splitTableBean.setSqlisdistinct("true");
/* 130 */     splitTableBean.setOperates(splitTableOperateBean);
/* 131 */     splitTableBean.setInstanceid("workflowTypeListTable");
/* 132 */     splitTableBean.setCheckboxpopedom(checkboxpopedom);
/* 133 */     splitTableBean.setSqlsortway("DESC");
/*     */     
/* 135 */     String str8 = SplitTableUtil.getTableString(splitTableBean);
/* 136 */     String str9 = str7 + "_" + Util.getEncrypt(Util.getRandom());
/* 137 */     Util_TableMap.setVal(str9, str8);
/*     */     
/* 139 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 140 */     hashMap1.put("sessionkey", str9);
/* 141 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 142 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 143 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 144 */     hashMap2.put("items", getItemList());
/* 145 */     arrayList2.add(hashMap2);
/* 146 */     hashMap1.put("conditioninfo", arrayList2);
/* 147 */     hashMap1.put("info", Integer.valueOf(1));
/* 148 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/* 152 */   private ConditionFactory conditionFactory = null;
/*     */ 
/*     */   
/*     */   public List<SearchConditionItem> getItemList() {
/* 156 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 160 */     SearchConditionItem searchConditionItem1 = this.conditionFactory.createCondition(ConditionType.INPUT, 195, "selectitemname");
/* 161 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/* 164 */     SearchConditionItem searchConditionItem2 = this.conditionFactory.createCondition(ConditionType.INPUT, 433, "selectitemdesc");
/* 165 */     arrayList.add(searchConditionItem2);
/*     */ 
/*     */ 
/*     */     
/* 169 */     SearchConditionItem searchConditionItem3 = this.conditionFactory.createCondition(ConditionType.SELECT, 124889, "hasdetail");
/* 170 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 171 */     arrayList1.add(new SearchConditionOption("-1", ""));
/* 172 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(161, this.user.getLanguage())));
/* 173 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(163, this.user.getLanguage())));
/*     */     
/* 175 */     searchConditionItem3.setOptions(arrayList1);
/* 176 */     arrayList.add(searchConditionItem3);
/*     */ 
/*     */     
/* 179 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getLinkSelectItem(String paramString1, String paramString2) {
/* 183 */     String str = "";
/* 184 */     str = "<a href=javaScript:workflowselectItem.newDialog(" + paramString2 + ")>" + paramString1 + "</a>";
/* 185 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public GetSelectListCmd(Map<String, Object> paramMap, User paramUser) {
/* 193 */     this.params = paramMap;
/* 194 */     this.user = paramUser;
/* 195 */     this.conditionFactory = new ConditionFactory(paramUser);
/*     */   }
/*     */   
/*     */   public GetSelectListCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/publicSelect/GetSelectListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */