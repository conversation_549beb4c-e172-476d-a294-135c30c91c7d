/*     */ package com.engine.workflow.cmd.formManage.publicSelect;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetReferencesCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public BizLogContext getLogContext() {
/*  39 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  44 */     if (!HrmUserVarify.checkUserRight("WORKFLOWPUBLICCHOICE:VIEW", this.user)) {
/*  45 */       return null;
/*     */     }
/*     */     
/*  48 */     return getReferencesMap(this.params);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getReferencesMap(Map<String, Object> paramMap) {
/*  54 */     byte b = 15;
/*  55 */     String str1 = " where 1=1 ";
/*     */     
/*  57 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")));
/*     */     
/*  59 */     String str2 = Util.null2String(paramMap.get("formnameForSearch"));
/*  60 */     String str3 = Util.null2String(paramMap.get("formdecForSearch"));
/*  61 */     String str4 = Util.null2String(paramMap.get("formtypeForSearch"));
/*     */     
/*  63 */     if (!"".equals(str2) && !"-1".equals(str2)) {
/*  64 */       if ("".equals(str1)) {
/*  65 */         str1 = " where formname like '%" + str2 + "%' ";
/*     */       } else {
/*  67 */         str1 = str1 + " and formname like '%" + str2 + "%' ";
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*  72 */     if (!"".equals(str3)) {
/*  73 */       if ("".equals(str1)) {
/*  74 */         str1 = " where formdesc like '%" + str3 + "%' ";
/*     */       } else {
/*  76 */         str1 = str1 + " and formdesc like '%" + str3 + "%' ";
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*  81 */     if (!"".equals(str4) && !"-1".equals(str4)) {
/*  82 */       if ("0".equals(str4)) {
/*  83 */         if ("".equals(str1)) {
/*  84 */           str1 = " where id <0 ";
/*     */         } else {
/*  86 */           str1 = str1 + " and id <0 ";
/*     */         } 
/*  88 */       } else if ("1".equals(str4)) {
/*  89 */         if ("".equals(str1)) {
/*  90 */           str1 = " where isoldornew = 1 and id >0 ";
/*     */         } else {
/*  92 */           str1 = str1 + " and isoldornew = 1 and id >0 ";
/*     */         }
/*     */       
/*     */       } 
/*  96 */     } else if ("".equals(str1)) {
/*  97 */       str1 = " where isoldornew = 1 ";
/*     */     } else {
/*  99 */       str1 = str1 + " and isoldornew = 1 ";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 104 */     str1 = str1 + " and exists (SELECT billid FROM workflow_billfield b WHERE f.id=b.billid and b.selectItemType = '1' and b.pubchoiceId = " + i + " )";
/*     */ 
/*     */     
/* 107 */     String str5 = " formname,isoldornew,id ";
/* 108 */     String str6 = "";
/* 109 */     String str7 = " id,formname,formdesc,subcompanyid,isoldornew ";
/* 110 */     String str8 = " view_workflowForm_selectAll f ";
/*     */     
/* 112 */     String str9 = "column:id+column:isoldornew+0";
/* 113 */     String str10 = "column:isoldornew+" + this.user.getLanguage();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 120 */     str6 = " <table pageUid=\"fb4a6f80-3709-45fe-be74-06b36128c966\" instanceid=\"workflowFormListTable\" tabletype=\"none\" pagesize=\"" + b + "\" > <checkboxpopedom  id=\"checkbox\" />       <sql backfields=\"" + str7 + "\" sqlform=\"" + str8 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str1) + "\"  sqlorderby=\"" + str5 + "\"  sqlprimarykey=\"id\" sqlsortway=\"ASC\" sqlisdistinct=\"false\" />       <head>           <col width=\"30%\"  text=\"" + SystemEnv.getHtmlLabelName(15451, this.user.getLanguage()) + "\" column=\"formname\" otherpara=\"" + str9 + "\" orderkey=\"formname\" />           <col width=\"30%\"  text=\"" + SystemEnv.getHtmlLabelName(15452, this.user.getLanguage()) + "\" column=\"formdesc\" orderkey=\"formdesc\"/>           <col width=\"30%\"  text=\"" + SystemEnv.getHtmlLabelName(18411, this.user.getLanguage()) + "\" column=\"id\" otherpara=\"" + str10 + "\" transmethod=\"weaver.workflow.form.FormMainManager.getFormType\"/>       </head> </table>";
/*     */ 
/*     */     
/* 123 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 124 */     String str11 = "fb4a6f80-3709-45fe-be74-06b36128c966_" + Util.getEncrypt(Util.getRandom());
/* 125 */     Util_TableMap.setVal(str11, str6);
/* 126 */     hashMap1.put("sessionkey", str11);
/*     */     
/* 128 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 129 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 130 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 132 */     hashMap2.put("items", getItemList());
/* 133 */     arrayList.add(hashMap2);
/* 134 */     hashMap1.put("conditioninfo", arrayList);
/* 135 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/* 140 */   private ConditionFactory conditionFactory = null;
/*     */   
/*     */   public List<SearchConditionItem> getItemList() {
/* 143 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 146 */     SearchConditionItem searchConditionItem1 = this.conditionFactory.createCondition(ConditionType.INPUT, 15451, "formnameForSearch");
/* 147 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/* 150 */     SearchConditionItem searchConditionItem2 = this.conditionFactory.createCondition(ConditionType.INPUT, 15452, "formdecForSearch");
/* 151 */     arrayList.add(searchConditionItem2);
/*     */     
/* 153 */     SearchConditionItem searchConditionItem3 = this.conditionFactory.createCondition(ConditionType.SELECT, 18411, "formtypeForSearch");
/* 154 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 155 */     arrayList1.add(new SearchConditionOption("-1", ""));
/* 156 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(125026, this.user.getLanguage())));
/* 157 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(125027, this.user.getLanguage())));
/*     */     
/* 159 */     searchConditionItem3.setOptions(arrayList1);
/* 160 */     arrayList.add(searchConditionItem3);
/*     */ 
/*     */     
/* 163 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getWFFormNameLink(String paramString1, String paramString2) {
/* 168 */     String str1 = "";
/* 169 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 170 */     int i = Util.getIntValue(arrayOfString[0]);
/* 171 */     String str2 = Util.null2String(arrayOfString[1]);
/* 172 */     String str3 = Util.null2String(arrayOfString[2]);
/* 173 */     paramString1 = paramString1.replaceAll("<", "＜").replaceAll(">", "＞").replaceAll("'", "''");
/* 174 */     str1 = "<a href='javascript:gotab00_new('/workflow/form/editform.jsp?ajax=1&formid=" + i + "'," + i + ")'>" + paramString1 + "</a>";
/* 175 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public GetReferencesCmd(Map<String, Object> paramMap, User paramUser) {
/* 183 */     this.params = paramMap;
/* 184 */     this.user = paramUser;
/* 185 */     this.conditionFactory = new ConditionFactory(paramUser);
/*     */   }
/*     */   
/*     */   public GetReferencesCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/publicSelect/GetReferencesCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */