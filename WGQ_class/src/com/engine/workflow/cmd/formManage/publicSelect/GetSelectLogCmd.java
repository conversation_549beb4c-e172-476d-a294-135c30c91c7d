/*     */ package com.engine.workflow.cmd.formManage.publicSelect;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSelectLogCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public BizLogContext getLogContext() {
/*  40 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  46 */     if (!HrmUserVarify.checkUserRight("WORKFLOWPUBLICCHOICE:VIEW", this.user)) {
/*  47 */       return null;
/*     */     }
/*     */     
/*  50 */     return getSelectLog(this.params);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getSelectLog(Map<String, Object> paramMap) {
/*  56 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/*  57 */     String str1 = Util.null2String(paramMap.get("logmodule"));
/*  58 */     String str2 = Util.null2String(paramMap.get("startdate"));
/*  59 */     String str3 = Util.null2String(paramMap.get("enddate"));
/*  60 */     String str4 = Util.null2String(paramMap.get("operator"));
/*  61 */     String str5 = Util.null2String(paramMap.get("logtype"));
/*  62 */     String str6 = Util.null2String(paramMap.get("selectname"));
/*     */     
/*  64 */     byte b = 10;
/*     */     
/*  66 */     String str7 = " where a.logmodule='SELECTITEM'";
/*     */     
/*  68 */     if (i > 0) {
/*  69 */       str7 = str7 + " and a.objid='" + i + "' ";
/*     */     }
/*     */     
/*  72 */     if (!str2.isEmpty()) {
/*  73 */       str7 = str7 + " and optdatetime >= '" + str2 + " 00:00'";
/*     */     }
/*  75 */     if (!str3.isEmpty()) {
/*  76 */       str7 = str7 + " and optdatetime <= '" + str3 + " 23:59'";
/*     */     }
/*  78 */     if (!str4.isEmpty()) {
/*  79 */       str7 = str7 + " and operator = '" + str4 + "'";
/*     */     }
/*  81 */     if (!str5.isEmpty()) {
/*  82 */       str7 = str7 + " and logtype = '" + str5 + "'";
/*     */     }
/*     */     
/*  85 */     if (!str6.isEmpty()) {
/*  86 */       str7 = str7 + " and selectname like '%" + str6 + "%'";
/*     */     }
/*     */     
/*  89 */     String str8 = " a.id,a.objid,a.operatorname,a.logtype,a.optdatetime,a.selectname,a.ipaddress,'" + SystemEnv.getHtmlLabelName(124930, this.user.getLanguage()) + "' as proj ";
/*  90 */     String str9 = " from selectItemLog a ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 102 */     String str10 = "<table  pagesize=\"" + b + "\" tabletype=\"none\" ><sql backfields=\"" + str8 + "\" sqlform=\"" + str9 + "\" sqlprimarykey=\"a.id\" sqlsortway=\"desc\" sqldistinct=\"true\" sqlwhere=\"" + Util.toHtmlForSplitPage(str7) + "\"/><head><col width=\"15%\"  text=\"" + SystemEnv.getHtmlLabelName(17482, this.user.getLanguage()) + "\" column=\"operatorname\" orderkey=\"operatorname\"  /><col width=\"15%\"  text=\"" + SystemEnv.getHtmlLabelName(21663, this.user.getLanguage()) + "\" column=\"optdatetime\" orderkey=\"optdatetime\" /><col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(15503, this.user.getLanguage()) + "\" column=\"logtype\" orderkey=\"logtype\" otherpara=\"" + this.user.getLanguage() + "\" transmethod=\"weaver.workflow.selectItem.SelectItemManager.getLogType\"/><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(101, this.user.getLanguage()) + "\" column=\"proj\"  /><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(106, this.user.getLanguage()) + "\" column=\"selectname\" orderkey=\"selectname\"  /><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(124985, this.user.getLanguage()) + "\" column=\"ipaddress\"  /></head></table>";
/*     */ 
/*     */     
/* 105 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 106 */     String str11 = "743bdf04-e521-42ad-a5dd-b3e52c1cb7b5_" + Util.getEncrypt(Util.getRandom());
/* 107 */     Util_TableMap.setVal(str11, str10);
/* 108 */     hashMap1.put("sessionkey", str11);
/*     */     
/* 110 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 111 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 112 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 113 */     hashMap2.put("items", getItemList());
/* 114 */     arrayList.add(hashMap2);
/* 115 */     hashMap1.put("conditioninfo", arrayList);
/* 116 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<SearchConditionItem> getItemList() {
/* 121 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 125 */     SearchConditionItem searchConditionItem1 = this.conditionFactory.createCondition(ConditionType.INPUT, 195, "selectitemname");
/* 126 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/* 129 */     SearchConditionItem searchConditionItem2 = this.conditionFactory.createCondition(ConditionType.INPUT, 433, "selectitemdesc");
/* 130 */     arrayList.add(searchConditionItem2);
/*     */ 
/*     */ 
/*     */     
/* 134 */     SearchConditionItem searchConditionItem3 = this.conditionFactory.createCondition(ConditionType.SELECT, 124889, "hasdetail");
/* 135 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 136 */     arrayList1.add(new SearchConditionOption("-1", ""));
/* 137 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(161, this.user.getLanguage())));
/* 138 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(163, this.user.getLanguage())));
/*     */     
/* 140 */     searchConditionItem3.setOptions(arrayList1);
/* 141 */     arrayList.add(searchConditionItem3);
/*     */ 
/*     */     
/* 144 */     return arrayList;
/*     */   }
/*     */   
/* 147 */   private ConditionFactory conditionFactory = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public GetSelectLogCmd(Map<String, Object> paramMap, User paramUser) {
/* 155 */     this.params = paramMap;
/* 156 */     this.user = paramUser;
/* 157 */     this.conditionFactory = new ConditionFactory(paramUser);
/*     */   }
/*     */   
/*     */   public GetSelectLogCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/publicSelect/GetSelectLogCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */