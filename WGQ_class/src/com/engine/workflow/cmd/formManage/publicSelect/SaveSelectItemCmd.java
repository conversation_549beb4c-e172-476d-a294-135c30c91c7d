/*     */ package com.engine.workflow.cmd.formManage.publicSelect;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.common.util.LogUtil;
/*     */ import com.engine.core.interceptor.AbstractCommand;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.entity.PublicSelectEntity;
/*     */ import com.engine.workflow.util.ListUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.selectItem.SelectItemManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SaveSelectItemCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private SelectItemManager selectItemManager;
/*  38 */   private String deleteStr = "DELETE";
/*  39 */   private String updateStr = "EDIT";
/*  40 */   private String insertStr = "ADD";
/*  41 */   private String logmoduleStr = "SELECTITEM";
/*     */ 
/*     */ 
/*     */   
/*  45 */   private List<BizLogContext> logContextList = new ArrayList<>();
/*     */ 
/*     */ 
/*     */   
/*     */   private int jump_pid;
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  54 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  65 */     return this.logContextList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  71 */     if (!HrmUserVarify.checkUserRight("WORKFLOWPUBLICCHOICE:VIEW", this.user)) {
/*  72 */       return null;
/*     */     }
/*     */     
/*  75 */     if (Util.getIntValue(Util.null2String(this.params.get("isdel"))) == 1) {
/*  76 */       return delListItem(this.params, this.user);
/*     */     }
/*     */     
/*  79 */     return saveSelectItem(this.params, this.user);
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> delListItem(Map<String, Object> paramMap, User paramUser) {
/*  84 */     HashMap<Object, Object> hashMap = new HashMap<>(3);
/*  85 */     String str = Util.null2String(paramMap.get("delids"));
/*  86 */     if (!"".equals(str.trim())) {
/*  87 */       insertMainLog(str);
/*  88 */       this.selectItemManager.deleteSelectItem(str, paramUser, Util.null2String(paramMap.get("param_ip")));
/*     */     } 
/*     */ 
/*     */     
/*  92 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void insertMainLog(String paramString) {
/* 101 */     RecordSet recordSet1 = new RecordSet();
/*     */     
/* 103 */     String str = "";
/* 104 */     RecordSet recordSet2 = new RecordSet();
/* 105 */     str = "select id,selectitemname from mode_selectitempage where id in(" + paramString + ")";
/* 106 */     recordSet1.executeSql(str);
/* 107 */     while (recordSet1.next()) {
/* 108 */       String str1 = recordSet1.getString("id");
/* 109 */       String str2 = recordSet1.getString("selectitemname");
/* 110 */       recordSet2.executeQuery("select * from MODE_SELECTITEMPAGEDETAIL where mainid=?", new Object[] { str1 });
/* 111 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 112 */       while (recordSet2.next()) {
/* 113 */         hashMap.put("id", Integer.valueOf(recordSet2.getInt("id")));
/* 114 */         hashMap.put("name", Integer.valueOf(recordSet2.getInt("name")));
/*     */       } 
/*     */       
/* 117 */       this.logContextList.add(ListUtil.getLogContext((AbstractCommand)this, str1, str2, BizLogOperateType.DELETE, BizLogSmallType4Workflow.WORKFLOW_ENGINE_PUBLIC_SELECT, BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORM, "", "", hashMap, null));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> saveSelectItem(Map<String, Object> paramMap, User paramUser) {
/* 126 */     HashMap<Object, Object> hashMap = new HashMap<>(3);
/* 127 */     hashMap.put("id", Integer.valueOf(saveOrUpdate(paramMap, paramUser)));
/* 128 */     hashMap.put("jump_pid", Integer.valueOf(this.jump_pid));
/* 129 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public int saveOrUpdate(Map<String, Object> paramMap, User paramUser) {
/* 134 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/* 135 */     String str1 = "" + Util.null2String(Util.null2String(paramMap.get("selectitemname")));
/* 136 */     String str2 = "" + Util.fromScreen3(Util.null2String(paramMap.get("selectitemdesc")), paramUser.getLanguage());
/* 137 */     int j = editModeSelectItem(paramMap, paramUser);
/* 138 */     if (i < 1) {
/* 139 */       i = j;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 144 */     this.selectItemManager.syncPubSelectOp(i, paramUser.getLanguage());
/* 145 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int editModeSelectItem(Map<String, Object> paramMap, User paramUser) {
/* 157 */     RecordSet recordSet = new RecordSet();
/* 158 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/* 159 */     String str1 = "" + Util.null2String(Util.null2String(paramMap.get("name")));
/* 160 */     String str2 = "" + Util.null2String(paramMap.get("desc"));
/* 161 */     String str3 = Util.null2String(Util.null2String(paramMap.get("statelev")));
/* 162 */     String str4 = Util.null2String(Util.null2String(paramMap.get("delids")));
/* 163 */     String str5 = Util.null2String(paramMap.get("pid"));
/*     */ 
/*     */     
/* 166 */     int j = Util.getIntValue(Util.null2String(paramMap.get("jump_id")), 0);
/* 167 */     int k = paramUser.getUID();
/*     */     
/* 169 */     String str6 = "";
/* 170 */     BizLogOperateType bizLogOperateType = null;
/* 171 */     HashMap<Object, Object> hashMap1 = null;
/* 172 */     HashMap<Object, Object> hashMap2 = null;
/* 173 */     if (i < 1) {
/* 174 */       bizLogOperateType = BizLogOperateType.ADD;
/* 175 */       i = this.selectItemManager.addSelectItem(str1, str2, k);
/* 176 */       hashMap2 = new HashMap<>();
/* 177 */       hashMap2.put("selectitemname", str1);
/* 178 */       hashMap2.put("selectitemdesc", str2);
/*     */       
/* 180 */       if (i < 1) {
/* 181 */         recordSet.writeLog("同步选择框出错:" + str1 + "uid:" + k + "selectitemdesc:" + str2);
/* 182 */         return i;
/*     */       } 
/*     */     } else {
/* 185 */       bizLogOperateType = BizLogOperateType.UPDATE;
/* 186 */       hashMap1 = new HashMap<>();
/* 187 */       hashMap2 = new HashMap<>();
/* 188 */       hashMap2.put("selectitemname", str1);
/* 189 */       hashMap2.put("selectitemdesc", str2);
/* 190 */       RecordSet recordSet1 = new RecordSet();
/* 191 */       recordSet1.executeQuery("select * from mode_selectitempage where id=?", new Object[] { Integer.valueOf(i) });
/* 192 */       while (recordSet1.next()) {
/* 193 */         hashMap1.put("selectitemname", recordSet1.getString("selectitemname"));
/* 194 */         hashMap1.put("selectitemdesc", recordSet1.getString("selectitemdesc"));
/*     */       } 
/*     */       
/* 197 */       LogUtil.removeIntersectionEntry(hashMap1, hashMap2);
/* 198 */       String str8 = TimeUtil.getCurrentDateString();
/* 199 */       String str9 = TimeUtil.getOnlyCurrentTimeString();
/* 200 */       str6 = "update mode_selectitempage set selectitemname='" + str1 + "',selectitemdesc='" + str2 + "',operatetime='" + str8 + " " + str9 + "'  where id='" + i + "'";
/*     */       
/* 202 */       recordSet.executeSql(str6);
/*     */     } 
/*     */     
/* 205 */     this.logContextList.add(ListUtil.getLogContext((AbstractCommand)this, i + "", str1, bizLogOperateType, BizLogSmallType4Workflow.WORKFLOW_ENGINE_PUBLIC_SELECT, BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORM, "", "", hashMap1, hashMap2));
/*     */ 
/*     */ 
/*     */     
/* 209 */     String str7 = Util.null2String(paramMap.get("datas"));
/*     */     
/* 211 */     if (!"".equals(str7.trim())) {
/* 212 */       List list = null;
/*     */       try {
/* 214 */         list = JSON.parseArray(str7, PublicSelectEntity.class);
/*     */       }
/* 216 */       catch (Exception exception) {
/* 217 */         writeLog(exception);
/* 218 */         writeLog("json 解析报错 请查看json格式是否正确 数据是否有特殊符号" + str7);
/*     */       } 
/*     */       
/* 221 */       byte b = 0;
/* 222 */       for (PublicSelectEntity publicSelectEntity : list) {
/*     */         
/* 224 */         String str8 = Util.null2String(publicSelectEntity.getOptiontext()).replaceAll("'", "''");
/*     */         
/* 226 */         String str9 = Util.null2String(publicSelectEntity.getDefaultvalue());
/* 227 */         String str10 = "" + Util.null2String(publicSelectEntity.getMaincategoryspan());
/* 228 */         String str11 = "-1,-1," + Util.getIntValue(Util.null2String(publicSelectEntity.getMaincategory()), -1);
/* 229 */         int m = Util.getIntValue(Util.null2String(publicSelectEntity.getId()));
/*     */         
/* 231 */         int n = publicSelectEntity.getIsAccordToSubCom();
/* 232 */         int i1 = Util.getIntValue(Util.null2String(publicSelectEntity.getCanel()), 0);
/* 233 */         if (i1 != 1)
/* 234 */           i1 = 0; 
/* 235 */         if (!str8.equals("")) {
/*     */ 
/*     */           
/* 238 */           Map map = null;
/* 239 */           HashMap<Object, Object> hashMap = null;
/*     */           
/* 241 */           if (m < 0) {
/* 242 */             String str = this.selectItemManager.generateID();
/* 243 */             hashMap = new HashMap<>();
/* 244 */             hashMap.put("mainid", Integer.valueOf(i));
/* 245 */             hashMap.put("name", str8);
/* 246 */             hashMap.put("disorder", Integer.valueOf(b));
/* 247 */             hashMap.put("defaultvalue", str9);
/* 248 */             hashMap.put("pathcategory", str10);
/* 249 */             hashMap.put("maincategory", str11);
/* 250 */             hashMap.put("pid", str5);
/* 251 */             hashMap.put("statelev", str3);
/* 252 */             hashMap.put("isAccordToSubCom", Integer.valueOf(n));
/* 253 */             hashMap.put("cancel", Integer.valueOf(i1));
/*     */ 
/*     */ 
/*     */             
/* 257 */             str6 = "insert into mode_selectitempagedetail(mainid,name,disorder,defaultvalue,pathcategory,maincategory,pid,statelev,isAccordToSubCom,cancel,uuid)  values('" + i + "','" + str8 + "','" + b + "','" + str9 + "','" + str10 + "','" + str11 + "','" + str5 + "','" + str3 + "','" + n + "','" + i1 + "','" + str + "')";
/*     */             
/* 259 */             recordSet.executeSql(str6);
/*     */             
/* 261 */             recordSet.executeSql("select id  from mode_selectitempagedetail where mainid=" + i + " and uuid='" + str + "'");
/*     */             
/* 263 */             int i2 = 0;
/* 264 */             if (recordSet.next()) {
/* 265 */               i2 = recordSet.getInt("id");
/*     */             }
/*     */             
/* 268 */             this.logContextList.add(ListUtil.getLogContext((AbstractCommand)this, i2 + "", str8, BizLogOperateType.ADD, BizLogSmallType4Workflow.WORKFLOW_ENGINE_PUBLIC_SELECT, BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORM, i + "", str1, map, hashMap));
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 273 */             if (j != 0 && m == j) {
/* 274 */               this.jump_pid = i2;
/*     */             
/*     */             }
/*     */           
/*     */           }
/*     */           else {
/*     */             
/* 281 */             String str = "";
/* 282 */             recordSet.executeQuery("select * from mode_selectitempagedetail where id=?", new Object[] { Integer.valueOf(m) });
/* 283 */             if (recordSet.next()) {
/* 284 */               hashMap1 = new HashMap<>();
/* 285 */               hashMap1.put("mainid", recordSet.getString("id"));
/* 286 */               hashMap1.put("name", recordSet.getString("name"));
/* 287 */               hashMap1.put("disorder", recordSet.getString("disorder"));
/* 288 */               hashMap1.put("defaultvalue", recordSet.getString("defaultvalue"));
/* 289 */               hashMap1.put("pathcategory", recordSet.getString("pathcategory"));
/* 290 */               hashMap1.put("maincategory", recordSet.getString("maincategory"));
/* 291 */               hashMap1.put("pid", recordSet.getString("pid"));
/* 292 */               hashMap1.put("statelev", recordSet.getString("statelev"));
/* 293 */               hashMap1.put("isAccordToSubCom", recordSet.getString("isAccordToSubCom"));
/* 294 */               str = recordSet.getString("cancel");
/* 295 */               hashMap1.put("cancel", str);
/*     */             } 
/*     */             
/* 298 */             hashMap = new HashMap<>();
/* 299 */             hashMap.put("mainid", Integer.valueOf(i));
/* 300 */             hashMap.put("name", str8);
/* 301 */             hashMap.put("disorder", Integer.valueOf(b));
/* 302 */             hashMap.put("defaultvalue", str9);
/* 303 */             hashMap.put("pathcategory", str10);
/* 304 */             hashMap.put("maincategory", str11);
/* 305 */             hashMap.put("pid", str5);
/* 306 */             hashMap.put("statelev", str3);
/* 307 */             hashMap.put("isAccordToSubCom", Integer.valueOf(n));
/* 308 */             hashMap.put("cancel", Integer.valueOf(i1));
/* 309 */             LogUtil.removeIntersectionEntry(hashMap1, hashMap);
/*     */             
/* 311 */             if (j != 0) {
/* 312 */               this.jump_pid = j;
/*     */             }
/* 314 */             str6 = "update mode_selectitempagedetail set name ='" + str8 + "',disorder='" + b + "',defaultvalue='" + str9 + "',pathcategory='" + str10 + "',maincategory='" + str11 + "',cancel='" + i1 + "',isAccordToSubCom='" + n + "' where id='" + m + "' ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 320 */             if ("1".equals(str) && i1 != 1)
/*     */             {
/* 322 */               UnblockingOptions(m + "");
/*     */             }
/*     */ 
/*     */             
/* 326 */             if (i1 == 1) {
/* 327 */               ArrayList<String> arrayList = new ArrayList();
/* 328 */               arrayList = this.selectItemManager.getAllSubSelectItemId(arrayList, m + "", -1);
/* 329 */               String str12 = "";
/* 330 */               for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/* 331 */                 str12 = str12 + "," + (String)arrayList.get(b1);
/*     */               }
/* 333 */               if (!str12.equals("")) {
/* 334 */                 str12 = str12.substring(1);
/* 335 */                 String str13 = "update mode_selectitempagedetail set cancel=1 where id in (" + str12 + ")";
/* 336 */                 recordSet.executeSql(str13);
/*     */               } 
/*     */             } 
/*     */             
/* 340 */             recordSet.executeSql(str6);
/*     */             
/* 342 */             this.logContextList.add(ListUtil.getLogContext((AbstractCommand)this, m + "", str8, bizLogOperateType, BizLogSmallType4Workflow.WORKFLOW_ENGINE_PUBLIC_SELECT, BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORM, i + "", str1, map, hashMap));
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 349 */         b++;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 354 */       if (!str4.equals("")) {
/* 355 */         if (str4.indexOf(",") == 0) {
/* 356 */           str4 = str4.substring(1);
/*     */         }
/* 358 */         String str8 = "select * from mode_selectitempagedetail where pid in (" + str4 + ") ";
/* 359 */         recordSet.executeQuery(str8, new Object[0]);
/* 360 */         while (recordSet.next()) {
/* 361 */           hashMap1 = new HashMap<>();
/* 362 */           String str11 = recordSet.getString("id");
/* 363 */           String str12 = recordSet.getString("name");
/* 364 */           hashMap1.put("id", str11);
/* 365 */           hashMap1.put("name", str12);
/* 366 */           hashMap1.put("disorder", recordSet.getString("disorder"));
/* 367 */           hashMap1.put("defaultvalue", recordSet.getString("defaultvalue"));
/* 368 */           hashMap1.put("pathcategory", recordSet.getString("pathcategory"));
/* 369 */           hashMap1.put("maincategory", recordSet.getString("maincategory"));
/* 370 */           hashMap1.put("pid", recordSet.getString("pid"));
/* 371 */           hashMap1.put("statelev", recordSet.getString("statelev"));
/* 372 */           hashMap1.put("isAccordToSubCom", recordSet.getString("isAccordToSubCom"));
/* 373 */           hashMap1.put("cancel", recordSet.getString("cancel"));
/* 374 */           this.logContextList.add(ListUtil.getLogContext((AbstractCommand)this, str11 + "", str12, BizLogOperateType.DELETE, BizLogSmallType4Workflow.WORKFLOW_ENGINE_PUBLIC_SELECT, BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORM, i + "", str12, hashMap1, null));
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 382 */         ArrayList<String> arrayList = new ArrayList();
/* 383 */         arrayList = this.selectItemManager.getAllSubSelectItemId(arrayList, str4, -1);
/* 384 */         String str9 = "";
/* 385 */         for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/* 386 */           str9 = str9 + "," + (String)arrayList.get(b1);
/*     */         }
/*     */         
/* 389 */         String str10 = str4 + str9;
/* 390 */         str6 = "delete from mode_selectitempagedetail where  mainid = " + i + " and  id in (" + str10 + ") ";
/* 391 */         recordSet.executeSql(str6);
/*     */       } 
/*     */     } 
/*     */     
/* 395 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void UnblockingOptions(String paramString) {
/* 404 */     ArrayList<String> arrayList = new ArrayList();
/* 405 */     SelectItemManager selectItemManager = new SelectItemManager();
/* 406 */     arrayList = selectItemManager.getAllSubSelectItemId(arrayList, "" + paramString, -1);
/* 407 */     String str1 = "";
/* 408 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 409 */       str1 = str1 + "," + (String)arrayList.get(b);
/*     */     }
/* 411 */     String str2 = paramString + str1;
/* 412 */     String str3 = "update mode_selectitempagedetail set cancel=0 where id in (" + str2 + ")";
/* 413 */     (new RecordSet()).executeSql(str3);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public SaveSelectItemCmd() {
/* 419 */     this.selectItemManager = new SelectItemManager();
/*     */   }
/*     */   
/*     */   public SaveSelectItemCmd(Map<String, Object> paramMap, User paramUser) {
/* 423 */     this.selectItemManager = new SelectItemManager();
/* 424 */     this.params = paramMap;
/* 425 */     this.user = paramUser;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/publicSelect/SaveSelectItemCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */