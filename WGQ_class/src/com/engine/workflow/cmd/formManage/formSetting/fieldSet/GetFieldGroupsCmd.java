/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*    */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*    */ import com.engine.workflow.entity.WeaTableEditEntity;
/*    */ import com.engine.workflow.util.I18nUtil;
/*    */ import com.google.common.collect.Lists;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.workflow.UserWFOperateLevel;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetFieldGroupsCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetFieldGroupsCmd(Map<String, Object> paramMap, User paramUser) {
/* 31 */     this.params = paramMap;
/* 32 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 37 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 38 */     String str1 = Util.null2String(this.params.get("isBill"));
/* 39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 40 */     boolean bool = (new ManageDetachComInfo()).isUseFmManageDetach() ? true : false;
/* 41 */     int j = UserWFOperateLevel.checkWfFormOperateLevel(bool, this.user, "FormManage:All", i, Util.getIntValue(str1));
/* 42 */     if (j < 0) {
/* 43 */       hashMap.put("noRight", Boolean.valueOf(true));
/* 44 */       return (Map)hashMap;
/*    */     } 
/* 46 */     ArrayList<WeaTableEditEntity> arrayList = Lists.newArrayList();
/*    */     
/* 48 */     WeaTableEditEntity weaTableEditEntity1 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(517198, this.user.getLanguage()), "groupname", "40%", "1");
/* 49 */     weaTableEditEntity1.setKey("groupname");
/* 50 */     weaTableEditEntity1.setUseRecord(true);
/* 51 */     weaTableEditEntity1.setClassName("wea-table-edit-groupName");
/* 52 */     ArrayList<WeaTableEditComEntity> arrayList1 = Lists.newArrayList();
/* 53 */     WeaTableEditComEntity weaTableEditComEntity1 = new WeaTableEditComEntity();
/* 54 */     weaTableEditComEntity1.setType(ConditionType.INPUT);
/* 55 */     weaTableEditComEntity1.setKey("groupname");
/* 56 */     weaTableEditComEntity1.setViewAttr(3);
/* 57 */     Map<String, String> map = I18nUtil.getI18nLengthValidate("workflow_formfield_group", "groupname");
/* 58 */     map.put("inputType", "multilang");
/* 59 */     map.put("isBase64", Boolean.valueOf(true));
/* 60 */     weaTableEditComEntity1.setOtherParams(map);
/* 61 */     arrayList1.add(weaTableEditComEntity1);
/* 62 */     weaTableEditEntity1.setCom(arrayList1);
/* 63 */     arrayList.add(weaTableEditEntity1);
/*    */ 
/*    */     
/* 66 */     WeaTableEditEntity weaTableEditEntity2 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(519541, this.user.getLanguage()), "groupicon", "60%", "1");
/* 67 */     weaTableEditEntity2.setKey("groupicon");
/* 68 */     weaTableEditEntity1.setUseRecord(true);
/* 69 */     weaTableEditEntity1.setClassName("wea-table-edit-groupIcon");
/* 70 */     ArrayList<WeaTableEditComEntity> arrayList2 = Lists.newArrayList();
/* 71 */     WeaTableEditComEntity weaTableEditComEntity2 = new WeaTableEditComEntity();
/* 72 */     weaTableEditComEntity2.setType(ConditionType.INPUT);
/* 73 */     weaTableEditComEntity2.setKey("groupicon");
/* 74 */     arrayList2.add(weaTableEditComEntity2);
/*    */     
/* 76 */     weaTableEditEntity2.setCom(arrayList2);
/* 77 */     arrayList.add(weaTableEditEntity2);
/*    */     
/* 79 */     WeaTableEditEntity weaTableEditEntity3 = new WeaTableEditEntity("", "fieldgroupid", "more", "40px", "1", "wea-table-edit-more");
/* 80 */     arrayList.add(weaTableEditEntity3);
/*    */     
/* 82 */     int k = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/* 83 */     String str2 = Util.null2String(this.params.get("isBill"));
/* 84 */     List list = FieldGroupBiz.getFormGroups(k, str2, false);
/* 85 */     hashMap.put("columns", arrayList);
/* 86 */     hashMap.put("datas", list);
/* 87 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 92 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetFieldGroupsCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */