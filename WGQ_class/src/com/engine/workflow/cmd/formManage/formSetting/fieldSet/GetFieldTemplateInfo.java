/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.SelectFieldItemBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ import weaver.workflow.field.UserDefinedBrowserTypeComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetFieldTemplateInfo
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetFieldTemplateInfo() {}
/*     */   
/*     */   public GetFieldTemplateInfo(Map<String, Object> paramMap, User paramUser) {
/*  30 */     this.params = paramMap;
/*  31 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  36 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  42 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/*  43 */     UserDefinedBrowserTypeComInfo userDefinedBrowserTypeComInfo = new UserDefinedBrowserTypeComInfo();
/*  44 */     int i = Util.getIntValue(Util.null2String(this.params.get("isDetail")));
/*  45 */     String str1 = (i == 1) ? "workflow_formdictdetail" : "workflow_formdict";
/*  46 */     String str2 = Util.null2String(this.params.get("fieldIds"));
/*  47 */     RecordSet recordSet1 = new RecordSet();
/*  48 */     RecordSet recordSet2 = new RecordSet();
/*  49 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  50 */     String[] arrayOfString = str2.split(",");
/*  51 */     for (String str : arrayOfString) {
/*  52 */       recordSet2.executeQuery("select * from " + str1 + " where id=" + str, new Object[0]);
/*  53 */       if (recordSet2.next()) {
/*  54 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  55 */         hashMap1.put("fieldLabelName", TextUtil.toBase64ForMultilang(recordSet2.getString("description")));
/*  56 */         hashMap1.put("fieldName", recordSet2.getString("fieldName"));
/*  57 */         ArrayList<String> arrayList1 = new ArrayList();
/*     */         
/*  59 */         String str3 = recordSet2.getString("fieldHtmlType");
/*  60 */         String str4 = recordSet2.getString("type");
/*  61 */         String str5 = recordSet2.getString("fieldShowTypes");
/*     */         
/*  63 */         String str6 = recordSet2.getString("fielddbtype");
/*  64 */         String str7 = recordSet2.getString("textheight_2");
/*  65 */         if ("1".equals(str3)) {
/*  66 */           arrayList1.add("input");
/*  67 */           if ("1".equals(str4)) {
/*  68 */             arrayList1.add("text");
/*  69 */             int j = str6.indexOf("(");
/*  70 */             int k = str6.lastIndexOf(")");
/*  71 */             arrayList1.add(str6.substring(j + 1, k));
/*  72 */           } else if ("2".equals(str4)) {
/*  73 */             arrayList1.add("int");
/*  74 */           } else if ("3".equals(str4) || "5".equals(str4)) {
/*  75 */             String str8 = recordSet2.getString("qfws");
/*  76 */             if (str4.equals("3")) {
/*  77 */               arrayList1.add("float");
/*     */             }
/*  79 */             if (str4.equals("5")) {
/*  80 */               arrayList1.add("thousandth");
/*     */             }
/*  82 */             arrayList1.add(str8);
/*  83 */           } else if ("4".equals(str4)) {
/*  84 */             arrayList1.add("convert");
/*     */           } 
/*  86 */         } else if ("2".equals(str3)) {
/*  87 */           arrayList1.add("textarea");
/*  88 */           arrayList1.add(recordSet2.getString("textheight"));
/*  89 */           arrayList1.add("2".equals(str4) ? "1" : "0");
/*  90 */         } else if ("3".equals(str3)) {
/*  91 */           arrayList1.add("browser");
/*  92 */           String str8 = SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo.getBrowserlabelid(str4)), 7);
/*  93 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  94 */           hashMap2.put("value", str4);
/*  95 */           hashMap2.put("valueSpan", str8);
/*  96 */           ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  97 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  98 */           hashMap3.put("id", str4);
/*  99 */           hashMap3.put("name", str8);
/* 100 */           arrayList2.add(hashMap3);
/* 101 */           hashMap2.put("replaceDatas", arrayList2);
/* 102 */           arrayList1.add(hashMap2);
/*     */           
/* 104 */           int j = Util.getIntValue(str4);
/* 105 */           if (j == 161 || j == 162 || j == 256 || j == 257 || j == 224 || j == 225 || j == 226 || j == 227) {
/* 106 */             String str9 = str6;
/* 107 */             if (j == 256 || j == 257) {
/* 108 */               String str10 = "select a.id,a.treename from mode_customtree a where a.id=" + str6;
/* 109 */               recordSet1.executeSql(str10);
/* 110 */               if (recordSet1.next()) {
/* 111 */                 str9 = recordSet1.getString("treename");
/*     */               }
/* 113 */             } else if (j == 161 || j == 162) {
/* 114 */               str9 = userDefinedBrowserTypeComInfo.getName(str6);
/*     */             } 
/* 116 */             HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 117 */             hashMap4.put("id", str6);
/* 118 */             hashMap4.put("name", str9);
/*     */             
/* 120 */             ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/* 121 */             arrayList3.add(hashMap4);
/*     */             
/* 123 */             HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 124 */             hashMap5.put("value", str6);
/* 125 */             hashMap5.put("valueSpan", str9);
/* 126 */             hashMap5.put("replaceDatas", arrayList3);
/*     */             
/* 128 */             arrayList1.add(hashMap5);
/*     */           }
/* 130 */           else if (j == 165 || j == 166 || j == 167 || j == 168 || j == 169 || j == 170) {
/* 131 */             arrayList1.add(str7);
/*     */           } 
/* 133 */         } else if ("4".equals(str3)) {
/* 134 */           arrayList1.add("check");
/* 135 */         } else if ("5".equals(str3)) {
/* 136 */           arrayList1.add("select");
/* 137 */           if ("1".equals(str4)) {
/* 138 */             arrayList1.add("select");
/* 139 */           } else if ("2".equals(str4)) {
/* 140 */             arrayList1.add("check");
/* 141 */           } else if ("3".equals(str4)) {
/* 142 */             arrayList1.add("radio");
/*     */           } 
/*     */           
/* 145 */           arrayList1.add("alone");
/* 146 */           Map<String, String> map = (new SelectFieldItemBiz()).getSelectItemDatas(Util.getIntValue(str), 0, -1, i, this.user, true);
/* 147 */           arrayList1.add(map);
/*     */ 
/*     */           
/* 150 */           if ("1".equals(str5)) {
/* 151 */             map.put("sort", "horizontal");
/*     */           } else {
/* 153 */             map.put("sort", "vertical");
/*     */           } 
/* 155 */           arrayList1.add(map);
/*     */         }
/* 157 */         else if ("6".equals(str3)) {
/* 158 */           arrayList1.add("upload");
/* 159 */           if ("1".equals(str4)) {
/* 160 */             arrayList1.add("file");
/* 161 */           } else if ("2".equals(str4)) {
/* 162 */             arrayList1.add("image");
/* 163 */             if (i != 1) {
/* 164 */               arrayList1.add(recordSet2.getString("textheight"));
/*     */             }
/* 166 */             arrayList1.add(recordSet2.getString("imgwidth"));
/* 167 */             arrayList1.add(recordSet2.getString("imgheight"));
/*     */           } 
/* 169 */         } else if ("7".equals(str3)) {
/* 170 */           arrayList1.add("peculiar");
/* 171 */           String str8 = "";
/* 172 */           String str9 = "";
/* 173 */           String str10 = "";
/* 174 */           recordSet1.executeQuery("select * from workflow_specialfield where fieldid = " + str + " and isbill = 0", new Object[0]);
/* 175 */           recordSet1.next();
/* 176 */           str8 = recordSet1.getString("displayname");
/* 177 */           str9 = recordSet1.getString("linkaddress");
/* 178 */           str10 = recordSet1.getString("descriptivetext");
/* 179 */           if ("1".equals(str4)) {
/* 180 */             arrayList1.add("href");
/* 181 */             arrayList1.add(str8);
/* 182 */             arrayList1.add(str9);
/* 183 */           } else if ("2".equals(str4)) {
/* 184 */             arrayList1.add("desc");
/* 185 */             str10 = Util.StringReplace(str10, "<br>", "\n");
/* 186 */             str10 = Util.StringReplace(str10, "&nbsp;", " ");
/* 187 */             arrayList1.add(str10);
/*     */           } 
/* 189 */         } else if ("9".equals(str3)) {
/* 190 */           arrayList1.add("mobile");
/* 191 */           arrayList1.add(str4);
/*     */         } 
/* 193 */         hashMap1.put("fieldType", arrayList1);
/* 194 */         arrayList.add(hashMap1);
/*     */       } 
/* 196 */     }  hashMap.put("datas", arrayList);
/* 197 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetFieldTemplateInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */