/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.FormFieldSetBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoDeleteFormFieldCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  31 */   protected int formId = 0;
/*     */ 
/*     */   
/*  34 */   protected List<BizLogContext> logContexts = new ArrayList<>();
/*  35 */   protected Map<Integer, Map<String, Object>> oldValuesMap = new HashMap<>();
/*  36 */   protected Map<Integer, Map<String, Object>> newValuesMap = new HashMap<>();
/*     */ 
/*     */ 
/*     */   
/*     */   public DoDeleteFormFieldCmd(Map<String, Object> paramMap, User paramUser) {
/*  41 */     this.params = paramMap;
/*  42 */     this.user = paramUser;
/*  43 */     this.formId = Util.getIntValue(Util.null2String(paramMap.get("formId")));
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  48 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  54 */     getNewValues();
/*  55 */     Date date = new Date();
/*  56 */     String str1 = " SELECT NAMELABEL FROM workflow_bill WHERE ID = ? ";
/*  57 */     RecordSet recordSet = new RecordSet();
/*  58 */     recordSet.executeQuery(str1, new Object[] { Integer.valueOf(this.formId) });
/*  59 */     String str2 = "";
/*  60 */     if (recordSet.next()) {
/*  61 */       str2 = SystemEnv.getHtmlLabelName(Util.getIntValue(recordSet.getString("NAMELABEL")), this.user.getLanguage());
/*     */     }
/*     */     
/*  64 */     BizLogContext bizLogContext = new BizLogContext();
/*  65 */     String str3 = bizLogContext.createMainid();
/*  66 */     bizLogContext.setDateObject(date);
/*  67 */     bizLogContext.setUserid(this.user.getUID());
/*  68 */     bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  69 */     bizLogContext.setTargetId(this.formId + "");
/*  70 */     bizLogContext.setTargetName(str2);
/*  71 */     bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  72 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/*  73 */     bizLogContext.setBelongTypeTargetId(this.formId + "");
/*  74 */     bizLogContext.setBelongTypeTargetName(str2);
/*  75 */     bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  76 */     bizLogContext.setOperateType(BizLogOperateType.DELETE);
/*  77 */     bizLogContext.setOldValues(new HashMap<>());
/*  78 */     bizLogContext.setNewValues(new HashMap<>());
/*  79 */     bizLogContext.setMainId(str3);
/*  80 */     bizLogContext.setParams(this.params);
/*  81 */     bizLogContext.setDetail(false);
/*  82 */     bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*  83 */     bizLogContext.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005602, ThreadVarLanguage.getLang()) + "", new Object[0]));
/*     */     
/*  85 */     boolean bool = false;
/*  86 */     Set<Integer> set = this.oldValuesMap.keySet();
/*  87 */     for (Integer integer : set) {
/*  88 */       if (this.newValuesMap.get(integer) != null)
/*  89 */         continue;  Map map = this.oldValuesMap.get(integer);
/*  90 */       int i = Util.getIntValue(Util.null2String(map.get("FIELDLABEL")));
/*  91 */       String str = SystemEnv.getHtmlLabelName(i, this.user.getLanguage());
/*  92 */       BizLogContext bizLogContext1 = new BizLogContext();
/*  93 */       bizLogContext1.setDateObject(date);
/*  94 */       bizLogContext1.setUserid(this.user.getUID());
/*  95 */       bizLogContext1.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  96 */       bizLogContext1.setTargetId(integer + "");
/*  97 */       bizLogContext1.setTargetName(str);
/*  98 */       bizLogContext1.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  99 */       bizLogContext1.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 100 */       bizLogContext1.setBelongTypeTargetId(this.formId + "");
/* 101 */       bizLogContext1.setBelongTypeTargetName(str2);
/* 102 */       bizLogContext1.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 103 */       bizLogContext1.setOperateType(BizLogOperateType.DELETE);
/* 104 */       bizLogContext1.setOldValues(map);
/* 105 */       bizLogContext1.setParams(this.params);
/* 106 */       bizLogContext1.setDetail(true);
/* 107 */       bizLogContext1.setBelongMainId(str3);
/* 108 */       bizLogContext1.setGroupId(integer + "");
/* 109 */       bizLogContext1.setGroupNameLabel(i);
/* 110 */       bizLogContext1.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 111 */       bizLogContext1.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005598, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10005599, ThreadVarLanguage.getLang()) + "" + str + "(" + integer + ")", new Object[0]));
/* 112 */       this.logContexts.add(bizLogContext1);
/* 113 */       bool = true;
/*     */     } 
/* 115 */     if (bool) {
/* 116 */       this.logContexts.add(bizLogContext);
/*     */     }
/* 118 */     return this.logContexts;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getOldValues() {
/* 125 */     getValues(this.oldValuesMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getNewValues() {
/* 132 */     getValues(this.newValuesMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getValues(Map<Integer, Map<String, Object>> paramMap) {
/* 139 */     RecordSet recordSet = new RecordSet();
/* 140 */     String str = " SELECT * FROM workflow_billfield WHERE billid = ? ";
/* 141 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(this.formId) });
/* 142 */     while (recordSet.next()) {
/* 143 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 144 */       String[] arrayOfString = recordSet.getColumnName();
/* 145 */       for (String str1 : arrayOfString) {
/* 146 */         hashMap.put(str1.toUpperCase(), recordSet.getString(str1));
/*     */       }
/* 148 */       paramMap.put(Integer.valueOf(recordSet.getInt("id")), hashMap);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 154 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 155 */     String str = Util.null2String(this.params.get("fieldIds"));
/* 156 */     getOldValues();
/* 157 */     if (this.formId > 0) {
/*     */       
/* 159 */       boolean bool = false;
/* 160 */       bool = doSysFormFieldDelete(str);
/* 161 */       hashMap.put("status", bool ? "success" : "failed");
/*     */     } else {
/*     */       
/* 164 */       String str1 = doCustomFormFieldDelete(str);
/* 165 */       hashMap.put("status", str1);
/*     */     } 
/* 167 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected String doCustomFormFieldDelete(String paramString) {
/* 176 */     String str = "";
/* 177 */     List<Iterable<? extends CharSequence>> list = FormFieldSetBiz.splitList(Util.TokenizerString(paramString, ","));
/* 178 */     for (byte b = 0; b < list.size(); b++) {
/* 179 */       paramString = String.join(",", list.get(b));
/* 180 */       FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(this.formId, 1, this.user);
/*     */       try {
/* 182 */         str = formFieldSetBiz.deleteFormField(paramString);
/* 183 */         formFieldSetBiz.submitOperate();
/* 184 */       } catch (Exception exception) {
/* 185 */         str = "failed";
/* 186 */         formFieldSetBiz.rollBackOperate();
/* 187 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     
/* 191 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean doSysFormFieldDelete(String paramString) {
/* 199 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 200 */     RecordSet recordSet = new RecordSet();
/* 201 */     boolean bool = true;
/* 202 */     int i = 0;
/* 203 */     int j = 0;
/* 204 */     String str1 = "";
/* 205 */     String str2 = "";
/* 206 */     String str3 = "";
/*     */     byte b;
/* 208 */     for (b = 0; b < arrayList.size(); b++) {
/* 209 */       j = Integer.parseInt(arrayList.get(b));
/*     */       
/* 211 */       str3 = "select fieldname from  workflow_billfield where id = " + j;
/* 212 */       recordSet.execute(str3);
/* 213 */       if (recordSet.next()) {
/* 214 */         str1 = recordSet.getString("fieldname");
/*     */       }
/* 216 */       if (recordSet.getDBType().equals("mysql")) {
/* 217 */         str3 = "select t1.id from workflow_nodelink t1, workflow_base t2 where t1.wfrequestid is null and t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + " and t1.`condition` like '%" + str1 + "%'";
/*     */       } else {
/* 219 */         str3 = "select t1.id from workflow_nodelink t1, workflow_base t2 where t1.wfrequestid is null and t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + " and t1.condition like '%" + str1 + "%'";
/*     */       } 
/* 221 */       recordSet.execute(str3);
/* 222 */       while (recordSet.next()) {
/* 223 */         str2 = str2 + recordSet.getString("id") + ",";
/*     */       }
/*     */     } 
/* 226 */     if (!str2.equals("")) {
/* 227 */       str2 = str2.substring(0, str2.lastIndexOf(","));
/*     */     } else {
/*     */       
/* 230 */       for (b = 0; b < arrayList.size(); b++) {
/* 231 */         j = Integer.parseInt(arrayList.get(b));
/*     */ 
/*     */         
/* 234 */         str3 = "select fieldname , fieldhtmltype from  workflow_billfield where id = " + j;
/* 235 */         recordSet.execute(str3);
/* 236 */         if (recordSet.next()) {
/* 237 */           str1 = recordSet.getString("fieldname");
/* 238 */           i = Util.getIntValue(recordSet.getString("fieldhtmltype"), 0);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 243 */         if (bool) {
/* 244 */           str3 = "delete from  workflow_addinoperate where isnode=1 and objid in (select t1.nodeid from  workflow_flownode t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ") and (fieldid =" + j + " or fieldop1id = " + j + " or fieldop2id = " + j + ")";
/* 245 */           bool = recordSet.execute(str3);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 250 */         if (bool) {
/* 251 */           str3 = "delete from  workflow_addinoperate where isnode=0 and objid in (select t1.id from  workflow_nodelink t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ") and (fieldid =" + j + " or fieldop1id = " + j + " or fieldop2id = " + j + ")";
/* 252 */           bool = recordSet.execute(str3);
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 282 */         if (bool) {
/* 283 */           str3 = "delete from  workflow_groupdetail where type in(5,6,31,32,7,38,42,43,8,33,9,10,47,34,11,12,13,35,14,15,44,45,46,16) and groupid in(select id from workflow_nodegroup where nodeid in (select t1.nodeid from  workflow_flownode t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ")) and objid=" + j;
/* 284 */           bool = recordSet.execute(str3);
/*     */         } 
/*     */ 
/*     */         
/* 288 */         if (bool) {
/* 289 */           str3 = "delete from  workflow_nodeform where nodeid in (select t1.nodeid from  workflow_flownode t1, workflow_base t2 where t1.workflowid=t2.id and t2.isbill='1' and t2.formid=" + this.formId + ") and fieldid= " + j;
/* 290 */           bool = recordSet.execute(str3);
/*     */         } 
/*     */ 
/*     */         
/* 294 */         if (bool && i == 5) {
/* 295 */           str3 = "delete from  workflow_selectitem where isbill=1 and fieldid =" + j;
/* 296 */           bool = recordSet.execute(str3);
/*     */           
/* 298 */           str3 = "update workflow_billfield set childfieldid=0 where childfieldid=" + j;
/* 299 */           recordSet.execute(str3);
/*     */         } 
/*     */ 
/*     */         
/* 303 */         if (bool && i == 7) {
/* 304 */           str3 = "delete from workflow_specialfield where isbill=1 and fieldid =" + j;
/* 305 */           bool = recordSet.execute(str3);
/*     */         } 
/*     */ 
/*     */         
/* 309 */         if (bool) {
/* 310 */           str3 = "delete from  workflow_billfield where id = " + j;
/* 311 */           bool = recordSet.execute(str3);
/*     */         } 
/*     */ 
/*     */         
/* 315 */         if (bool && !"db2".equals(recordSet.getDBType())) {
/* 316 */           str3 = "ALTER TABLE " + FormFieldSetBiz.getTableName(this.formId) + " DROP COLUMN " + str1;
/* 317 */           bool = recordSet.execute(str3);
/*     */         } 
/*     */       } 
/*     */     } 
/* 321 */     return bool;
/*     */   }
/*     */   
/*     */   public DoDeleteFormFieldCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/DoDeleteFormFieldCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */