/*    */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.exceldesign.FormTemplateManager;
/*    */ import weaver.workflow.formimport.FormImportServices;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ImportFormFromCloudStoreCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   private String fileId;
/*    */   
/*    */   public ImportFormFromCloudStoreCmd(String paramString, Map<String, Object> paramMap, User paramUser) {
/* 35 */     this.fileId = paramString;
/* 36 */     this.params = paramMap;
/* 37 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 42 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 47 */     Map<String, Object> map = doImport();
/* 48 */     String str = (String)map.get("errormsg");
/* 49 */     boolean bool = ((Boolean)map.get("status")).booleanValue();
/* 50 */     map.put("msg", str);
/* 51 */     map.put("status", Boolean.valueOf(true));
/* 52 */     map.put("importStatus", Boolean.valueOf(bool));
/* 53 */     return map;
/*    */   }
/*    */   
/*    */   public Map<String, Object> doImport() {
/* 57 */     Map<String, Boolean> map = (Map)new HashMap<>();
/*    */     try {
/* 59 */       FormImportServices formImportServices = new FormImportServices();
/* 60 */       formImportServices.setUser(this.user);
/* 61 */       formImportServices.setRemoteAddr(Util.null2String(this.params.get("param_ip")));
/*    */       
/* 63 */       String str = Util.null2String(this.params.get("importSap"));
/* 64 */       boolean bool1 = true;
/* 65 */       if ("1".equals(str)) {
/* 66 */         bool1 = false;
/*    */       }
/* 68 */       map = formImportServices.importFormFromCloudStore(this.fileId, bool1);
/* 69 */     } catch (Exception exception) {
/* 70 */       map.put("status", Boolean.valueOf(false));
/* 71 */       exception.printStackTrace();
/*    */     } 
/* 73 */     boolean bool = ((Boolean)map.get("status")).booleanValue();
/* 74 */     if (bool) {
/* 75 */       int i = Util.getIntValue((new StringBuilder()).append(map.get("formid")).append("").toString());
/* 76 */       String str = (new FormTemplateManager()).getFormName(i, 1, this.user.getLanguage());
/* 77 */       map.put("formName", str);
/*    */     } 
/* 79 */     return (Map)map;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/ImportFormFromCloudStoreCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */