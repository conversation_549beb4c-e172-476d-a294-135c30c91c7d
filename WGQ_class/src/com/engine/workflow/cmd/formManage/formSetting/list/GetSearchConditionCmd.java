/*    */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SearchConditionOption;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetSearchConditionCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetSearchConditionCmd() {}
/*    */   
/*    */   public GetSearchConditionCmd(Map<String, Object> paramMap, User paramUser) {
/* 31 */     this.params = paramMap;
/* 32 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 37 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 44 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/* 45 */     if (bool) {
/* 46 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 47 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 48 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 49 */       hashMap1.put("items", getItemList());
/* 50 */       arrayList.add(hashMap1);
/* 51 */       hashMap.put("conditioninfo", arrayList);
/*    */     } else {
/* 53 */       hashMap.put("noRight", Boolean.valueOf(true));
/*    */     } 
/*    */     
/* 56 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   protected List<SearchConditionItem> getItemList() {
/* 60 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 61 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*    */     
/* 63 */     String str = Util.null2String(this.params.get("formType"));
/*    */ 
/*    */     
/* 66 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15451, "formnameForSearch");
/* 67 */     arrayList.add(searchConditionItem1);
/*    */     
/* 69 */     if ("".equals(str) || "allForm".equals(str)) {
/* 70 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 18411, "formtypeForSearch");
/* 71 */       searchConditionItem.setOptions(getFormTypeOptions());
/* 72 */       arrayList.add(searchConditionItem);
/*    */     } 
/*    */     
/* 75 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 15452, "formdecForSearch");
/* 76 */     arrayList.add(searchConditionItem2);
/*    */     
/* 78 */     return arrayList;
/*    */   }
/*    */   
/*    */   protected List<SearchConditionOption> getFormTypeOptions() {
/* 82 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*    */     
/* 84 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/*    */     
/* 86 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(125026, this.user.getLanguage()), false));
/*    */     
/* 88 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(125027, this.user.getLanguage()), false));
/*    */ 
/*    */     
/* 91 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/GetSearchConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */