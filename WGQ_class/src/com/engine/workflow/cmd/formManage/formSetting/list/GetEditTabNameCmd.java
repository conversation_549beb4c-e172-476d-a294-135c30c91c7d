/*    */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*    */ 
/*    */ import com.api.workflow.bean.PageTabInfo;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetEditTabNameCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetEditTabNameCmd() {}
/*    */   
/*    */   public GetEditTabNameCmd(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.params = paramMap;
/* 29 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 34 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 40 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 41 */     int j = Util.getIntValue(Util.null2String(this.params.get("isBill")));
/* 42 */     hashMap.put("datas", getTabInfos(i, j));
/* 43 */     hashMap.put("userRight", Boolean.valueOf(getUserRight()));
/* 44 */     hashMap.put("formName", getFormName(i, j));
/* 45 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   protected boolean getUserRight() {
/* 49 */     String str = "FormManage:All";
/* 50 */     int i = Util.getIntValue(Util.null2String(this.params.get("isFromMode")), 0);
/* 51 */     if (i == 1) {
/* 52 */       str = "FORMMODEFORM:ALL";
/*    */     }
/*    */     
/* 55 */     if (!HrmUserVarify.checkUserRight(str, this.user))
/*    */     {
/* 57 */       return false;
/*    */     }
/* 59 */     return true;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   protected List<PageTabInfo> getTabInfos(int paramInt1, int paramInt2) {
/* 65 */     ArrayList<PageTabInfo> arrayList = new ArrayList();
/*    */     
/* 67 */     arrayList.add(new PageTabInfo("baseInfo", SystemEnv.getHtmlLabelName(1361, this.user.getLanguage()), 0, true, "#000000"));
/*    */     
/* 69 */     arrayList.add(new PageTabInfo("eidtField", SystemEnv.getHtmlLabelName(15449, this.user.getLanguage()), 1, true, "#000000"));
/* 70 */     boolean bool = "1".equals(Util.null2String(this.params.get("isFromMode")));
/* 71 */     if (!bool)
/* 72 */       arrayList.add(new PageTabInfo("fieldGroup", SystemEnv.getHtmlLabelName(517202, this.user.getLanguage()), 4, true, "#000000")); 
/* 73 */     if (paramInt2 != 1 || paramInt1 <= 0) {
/*    */       
/* 75 */       arrayList.add(new PageTabInfo("rowRule", SystemEnv.getHtmlLabelName(18368, this.user.getLanguage()), 2, true, "#000000"));
/*    */       
/* 77 */       arrayList.add(new PageTabInfo("colRule", SystemEnv.getHtmlLabelName(18369, this.user.getLanguage()), 3, true, "#000000"));
/*    */     } 
/* 79 */     return arrayList;
/*    */   }
/*    */ 
/*    */   
/*    */   protected String getFormName(int paramInt1, int paramInt2) {
/* 84 */     String str = "";
/* 85 */     RecordSet recordSet = new RecordSet();
/* 86 */     if (paramInt2 == 1) {
/* 87 */       recordSet.executeQuery("select namelabel from workflow_bill where id = ?", new Object[] { Integer.valueOf(paramInt1) });
/* 88 */       if (recordSet.next()) {
/* 89 */         str = SystemEnv.getHtmlLabelName(Util.getIntValue(recordSet.getString("namelabel")), this.user.getLanguage());
/*    */       }
/*    */     } else {
/* 92 */       recordSet.executeQuery("select formname from workflow_formbase where id = ? ", new Object[] { Integer.valueOf(paramInt1) });
/* 93 */       if (recordSet.next()) {
/* 94 */         str = recordSet.getString("formname");
/*    */       }
/*    */     } 
/* 97 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/GetEditTabNameCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */