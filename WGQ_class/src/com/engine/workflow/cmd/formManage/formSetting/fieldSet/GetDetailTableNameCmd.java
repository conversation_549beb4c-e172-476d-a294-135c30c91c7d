/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.form.FormManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetDetailTableNameCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetDetailTableNameCmd() {}
/*    */   
/*    */   public GetDetailTableNameCmd(Map<String, Object> paramMap, User paramUser) {
/* 25 */     this.params = paramMap;
/* 26 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 31 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 37 */     String str1 = Util.null2String(this.params.get("mainTableName"));
/* 38 */     String str2 = "";
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 43 */     int i = Util.getIntValue(Util.null2String(this.params.get("detailEndIndex")), 1);
/* 44 */     String str3 = Util.null2String(this.params.get("pageNoSaveTablenames")).toLowerCase();
/* 45 */     String[] arrayOfString = str3.split(",");
/* 46 */     int j = 0;
/* 47 */     if (arrayOfString.length > 0) {
/* 48 */       String str = arrayOfString[0];
/* 49 */       RecordSet recordSet = new RecordSet();
/* 50 */       recordSet.executeQuery("select max(a.orderid) from Workflow_billdetailtable a,Workflow_billdetailtable b  where a.billid=b.billid and b.tablename = ? ", new Object[] { str });
/* 51 */       recordSet.next();
/* 52 */       j = Util.getIntValue(recordSet.getString(1), 0);
/*    */     } 
/*    */     
/* 55 */     for (; i < Integer.MAX_VALUE; i++) {
/* 56 */       j++;
/* 57 */       str2 = str1 + "_dt" + j;
/* 58 */       if (str3.indexOf(str2.toLowerCase()) == -1) {
/*    */         
/* 60 */         boolean bool = (new FormManager()).isHaveSameTableInDB(str2);
/* 61 */         if (!bool) {
/*    */           break;
/*    */         }
/*    */       } 
/*    */     } 
/* 66 */     hashMap.put("detailTableName", str2);
/* 67 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetDetailTableNameCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */