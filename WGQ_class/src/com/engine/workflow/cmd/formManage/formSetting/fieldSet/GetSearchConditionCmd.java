/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.FormFieldTypeItemBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSearchConditionCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetSearchConditionCmd() {}
/*     */   
/*     */   public GetSearchConditionCmd(Map<String, Object> paramMap, User paramUser) {
/*  32 */     this.params = paramMap;
/*  33 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  38 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  43 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  44 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  45 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  47 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  48 */     hashMap2.put("items", getItemList());
/*  49 */     arrayList.add(hashMap2);
/*     */     
/*  51 */     hashMap1.put("conditioninfo", arrayList);
/*  52 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionItem> getItemList() {
/*  56 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  57 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  60 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15456, "labelName");
/*  61 */     searchConditionItem1.setLabelcol(8);
/*  62 */     searchConditionItem1.setFieldcol(16);
/*  63 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/*  66 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, 17997, "fieldTable");
/*  67 */     searchConditionItem2.setLabelcol(8);
/*  68 */     searchConditionItem2.setFieldcol(16);
/*  69 */     searchConditionItem2.setOptions(getFieldTableOptions());
/*  70 */     arrayList.add(searchConditionItem2);
/*     */ 
/*     */     
/*  73 */     FormFieldTypeItemBiz formFieldTypeItemBiz = new FormFieldTypeItemBiz(this.user);
/*  74 */     SearchConditionItem searchConditionItem3 = formFieldTypeItemBiz.getFieldTypeItem(conditionFactory, "htmlType", "type");
/*  75 */     searchConditionItem3.setLabelcol(8);
/*  76 */     searchConditionItem3.setFieldcol(16);
/*  77 */     searchConditionItem3.setOptions(getHtmlTypeOptions());
/*  78 */     arrayList.add(searchConditionItem3);
/*     */     
/*  80 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<SearchConditionOption> getFieldTableOptions() {
/*  88 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/*  90 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     
/*  92 */     arrayList.add(new SearchConditionOption("-1", " ", true));
/*     */     
/*  94 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()), false));
/*     */     
/*  96 */     RecordSet recordSet = new RecordSet();
/*  97 */     String str = "SELECT * FROM workflow_billDetailTable WHERE BILLID = ? ORDER BY ORDERID ";
/*  98 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(i) });
/*  99 */     byte b = 1;
/* 100 */     while (recordSet.next()) {
/* 101 */       String str1 = recordSet.getString("tableName");
/* 102 */       arrayList.add(new SearchConditionOption(str1, SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b, false));
/* 103 */       b++;
/*     */     } 
/* 105 */     return arrayList;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionOption> getHtmlTypeOptions() {
/* 109 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 111 */     arrayList.add(new SearchConditionOption("-1", " ", true));
/*     */     
/* 113 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(688, this.user.getLanguage()), false));
/*     */     
/* 115 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(689, this.user.getLanguage()), false));
/*     */     
/* 117 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(695, this.user.getLanguage()), false));
/*     */     
/* 119 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(691, this.user.getLanguage()), false));
/*     */     
/* 121 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(690, this.user.getLanguage()), false));
/*     */     
/* 123 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(17616, this.user.getLanguage()), false));
/*     */     
/* 125 */     arrayList.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(21691, this.user.getLanguage()), false));
/*     */     
/* 127 */     arrayList.add(new SearchConditionOption("9", SystemEnv.getHtmlLabelName(125583, this.user.getLanguage()), false));
/* 128 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetSearchConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */