/*     */ package com.engine.workflow.cmd.formManage.formSetting.baseSet;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.biz.SimpleBizLogger;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.label.LabelComInfo;
/*     */ import weaver.workflow.form.FormComInfo;
/*     */ import weaver.workflow.form.FormManager;
/*     */ import weaver.workflow.workflow.BillComInfo;
/*     */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoEditBaseInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  32 */   protected SimpleBizLogger logger = new SimpleBizLogger();
/*     */ 
/*     */ 
/*     */   
/*     */   public DoEditBaseInfoCmd(Map<String, Object> paramMap, User paramUser) {
/*  37 */     this.params = paramMap;
/*  38 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  43 */     return this.logger.getBizLogContext();
/*     */   }
/*     */   
/*     */   protected void logBefore() {
/*  47 */     String str1 = Util.null2String(this.params.get("isBill"));
/*  48 */     String str2 = Util.null2String(this.params.get("formId"));
/*  49 */     String str3 = Util.null2String(this.params.get("formName"));
/*     */     
/*  51 */     String str4 = "select * from workflow_bill where id = " + str2;
/*  52 */     if ("0".equals(str1)) {
/*  53 */       str4 = " select * from workflow_formbase where id = " + str2;
/*     */     }
/*     */     
/*  56 */     BizLogContext bizLogContext = new BizLogContext();
/*  57 */     bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  58 */     bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  59 */     bizLogContext.setBelongTypeTargetId("");
/*  60 */     bizLogContext.setBelongTypeTargetName("");
/*  61 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  62 */     bizLogContext.setTargetName(str3);
/*  63 */     bizLogContext.setTargetId(str2 + "");
/*  64 */     bizLogContext.setParams(this.params);
/*  65 */     this.logger.setUser(this.user);
/*  66 */     this.logger.setMainSql(str4, "id");
/*     */     
/*  68 */     this.logger.before(bizLogContext);
/*     */   }
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*     */     Map<String, Object> map;
/*  73 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  74 */     logBefore();
/*     */     try {
/*  76 */       String str = Util.null2String(this.params.get("isBill"));
/*  77 */       if ("0".equals(str)) {
/*  78 */         map = doOldFormEdit();
/*     */       } else {
/*  80 */         map = doNewFormEdit();
/*     */       } 
/*  82 */     } catch (Exception exception) {
/*  83 */       exception.printStackTrace();
/*  84 */       map.put("status", "failed");
/*     */     } 
/*  86 */     return map;
/*     */   }
/*     */   
/*     */   protected Map<String, Object> doNewFormEdit() throws Exception {
/*  90 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  91 */     RecordSet recordSet = new RecordSet();
/*  92 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  93 */     String str1 = Util.null2String(this.params.get("formName"));
/*  94 */     String str2 = Util.null2String(this.params.get("formId"));
/*     */ 
/*     */ 
/*     */     
/*  98 */     if (haveSameName(Util.getIntValue(str2), str1)) {
/*     */       
/* 100 */       hashMap.put("isSameName", Boolean.valueOf(true));
/* 101 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 104 */     LabelUtil labelUtil = new LabelUtil();
/* 105 */     int i = 0;
/* 106 */     recordSetTrans.executeQuery("select namelabel from workflow_bill where id = ?", new Object[] { str2 });
/* 107 */     if (recordSetTrans.next()) {
/* 108 */       i = recordSetTrans.getInt("namelabel");
/*     */     }
/* 110 */     boolean bool = false;
/* 111 */     if (i < 0) {
/* 112 */       recordSetTrans.executeQuery("select count(*) as count from workflow_bill where namelabel = ?", new Object[] { Integer.valueOf(i) });
/* 113 */       if (recordSetTrans.next() && recordSetTrans.getInt("count") > 1) {
/* 114 */         bool = true;
/*     */       }
/*     */     } 
/* 117 */     recordSetTrans.executeQuery("select namelabel from workflow_bill where id = ?", new Object[] { str2 });
/* 118 */     i = (i >= 0 || bool) ? labelUtil.getLabelId4Workflow(str1) : labelUtil.getLabelId4Workflow(i, str1);
/* 119 */     (new LabelComInfo()).addLabeInfoCache("" + i);
/* 120 */     (new BillComInfo()).addBillCache(str2, i);
/*     */     
/* 122 */     String str3 = Util.null2String(this.params.get("formDes"));
/* 123 */     str3 = str3.replaceAll("'", "''");
/*     */     
/* 125 */     int j = Util.getIntValue(Util.null2String(this.params.get("subCompanyId")), -1);
/* 126 */     int k = Util.getIntValue(Util.null2String(this.params.get("subCompanyId3")), -1);
/* 127 */     if (j == -1) {
/* 128 */       recordSet.executeSql("select dftsubcomid from SystemSet");
/* 129 */       if (recordSetTrans.next()) j = Util.getIntValue(recordSet.getString("dftsubcomid"), -1); 
/* 130 */       if (j == -1) {
/* 131 */         recordSet.executeSql("select min(id) as id from HrmSubCompany");
/* 132 */         if (recordSet.next()) j = recordSet.getInt("id");
/*     */       
/*     */       } 
/*     */     } 
/* 136 */     if (k == -1) {
/* 137 */       recordSetTrans.executeSql("select fmdftsubcomid,dftsubcomid from SystemSet");
/* 138 */       if (recordSetTrans.next()) {
/* 139 */         k = Util.getIntValue(recordSetTrans.getString("fmdftsubcomid"), -1);
/* 140 */         if (k == -1) {
/* 141 */           k = Util.getIntValue(recordSetTrans.getString("dftsubcomid"), -1);
/*     */         }
/*     */       } 
/* 144 */       if (k == -1) {
/* 145 */         recordSetTrans.executeSql("select min(id) as id from HrmSubCompany");
/* 146 */         if (recordSetTrans.next()) k = recordSetTrans.getInt("id");
/*     */       
/*     */       } 
/*     */     } 
/*     */     
/* 151 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/* 152 */     boolean bool1 = manageDetachComInfo.isUseWfManageDetach();
/*     */     
/* 154 */     String str4 = Util.null2String(this.params.get("isFromMode"));
/* 155 */     if ("1".equals(str4)) {
/* 156 */       bool1 = manageDetachComInfo.isUseFmManageDetach();
/*     */     }
/*     */     
/* 159 */     boolean bool2 = true;
/* 160 */     if (bool1) {
/* 161 */       bool2 = recordSet.executeUpdate("update workflow_bill set subcompanyid=" + j + ",subcompanyid3=" + k + ", namelabel=" + i + ",formdes='" + str3 + "' where id=" + str2, new Object[0]);
/*     */     } else {
/* 163 */       bool2 = recordSet.executeUpdate("update workflow_bill set namelabel=" + i + ",formdes='" + str3 + "' where id=" + str2, new Object[0]);
/*     */     } 
/*     */     
/* 166 */     (new WorkflowBillComInfo()).removeWorkflowBillCache();
/* 167 */     if (bool2) {
/* 168 */       hashMap.put("status", "success");
/* 169 */       hashMap.put("formName", str1);
/*     */     } 
/* 171 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   protected Map<String, Object> doOldFormEdit() throws Exception {
/* 175 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 176 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/* 177 */     String str1 = Util.null2String(this.params.get("formName")).replaceAll("<", "＜").replaceAll(">", "＞").replaceAll("'", "''");
/*     */     
/* 179 */     if (haveSameName(i, str1)) {
/*     */       
/* 181 */       hashMap.put("isSameName", Boolean.valueOf(true));
/* 182 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 185 */     FormManager formManager = new FormManager();
/* 186 */     formManager.reset();
/* 187 */     formManager.setAction("editform");
/* 188 */     formManager.setFormid(i);
/* 189 */     formManager.setFormname(Util.null2String(str1));
/* 190 */     String str2 = Util.null2String(this.params.get("formDes")).replaceAll("<", "＜").replaceAll(">", "＞").replaceAll("'", "''");
/* 191 */     formManager.setFormdes(Util.null2String(str2));
/* 192 */     formManager.setSubCompanyId2(Util.getIntValue(Util.null2String(this.params.get("subCompanyId")), -1));
/* 193 */     formManager.setSubCompanyId3(Util.getIntValue(Util.null2String(this.params.get("subCompanyId3")), -1));
/* 194 */     formManager.setFormInfo();
/* 195 */     (new FormComInfo()).removeFormCache();
/* 196 */     hashMap.put("status", "success");
/* 197 */     hashMap.put("formName", str1);
/* 198 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean haveSameName(int paramInt, String paramString) {
/* 206 */     boolean bool = false;
/* 207 */     RecordSet recordSet = new RecordSet();
/* 208 */     recordSet.executeSql("select namelabel from workflow_bill where id !=" + paramInt);
/* 209 */     while (recordSet.next()) {
/* 210 */       int i = Util.getIntValue(Util.null2String(recordSet.getString("namelabel")), 0);
/* 211 */       if (i != 0) {
/*     */         
/* 213 */         String str = SystemEnv.getHtmlLabelName(i, this.user.getLanguage());
/* 214 */         if (str != null) {
/* 215 */           str = str.trim();
/*     */         }
/* 217 */         if (paramString.equals(str)) {
/*     */           
/* 219 */           bool = true;
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 224 */     recordSet.executeSql("select formname from workflow_formbase where id !=" + paramInt);
/* 225 */     while (recordSet.next()) {
/* 226 */       String str = Util.null2String(recordSet.getString("formname"));
/* 227 */       if (!str.equals(""))
/*     */       {
/* 229 */         if (paramString.equals(str)) {
/*     */           
/* 231 */           bool = true;
/*     */           break;
/*     */         } 
/*     */       }
/*     */     } 
/* 236 */     return bool;
/*     */   }
/*     */   
/*     */   public DoEditBaseInfoCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/baseSet/DoEditBaseInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */