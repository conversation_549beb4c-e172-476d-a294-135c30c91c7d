/*    */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.google.common.base.Strings;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoDeleteTemplateCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoDeleteTemplateCmd() {}
/*    */   
/*    */   public DoDeleteTemplateCmd(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.params = paramMap;
/* 29 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 34 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 41 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/* 42 */     if (bool) {
/* 43 */       String str1 = Util.null2String(this.params.get("modeId"));
/* 44 */       String str2 = Util.null2String(this.params.get("type"));
/* 45 */       RecordSet recordSet = new RecordSet();
/* 46 */       if (!Strings.isNullOrEmpty(str1)) {
/* 47 */         if ("temp".equals(str2)) {
/* 48 */           recordSet.executeUpdate("delete from workflow_formmode where id = ?", new Object[] { str1 });
/* 49 */           recordSet.executeUpdate("delete from workflow_printset where type=4 and modeid= ? ", new Object[] { str1 });
/*    */         } else {
/* 51 */           recordSet.executeUpdate("delete from workflow_nodehtmllayout where id = ?", new Object[] { str1 });
/* 52 */           recordSet.executeUpdate("delete from workflow_printset where type=3 and modeid= ? ", new Object[] { str1 });
/*    */         } 
/*    */       }
/* 55 */       hashMap.put("del_status", "success");
/*    */     } else {
/* 57 */       hashMap.put("noRight", Boolean.valueOf(true));
/*    */     } 
/*    */     
/* 60 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/DoDeleteTemplateCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */