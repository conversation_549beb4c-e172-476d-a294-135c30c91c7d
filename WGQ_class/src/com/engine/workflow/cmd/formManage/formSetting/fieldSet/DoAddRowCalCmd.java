/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.util.FormAuthorityUtil;
/*     */ import com.engine.workflow.util.FormSetUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.StringTokenizer;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoAddRowCalCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DoAddRowCalCmd() {}
/*     */   
/*     */   public DoAddRowCalCmd(Map<String, Object> paramMap, User paramUser) {
/*  37 */     this.params = paramMap;
/*  38 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  43 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  49 */     FormSetUtil formSetUtil = new FormSetUtil();
/*  50 */     FormAuthorityUtil formAuthorityUtil = new FormAuthorityUtil();
/*     */     
/*  52 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  53 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  55 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  57 */     ArrayList<String> arrayList1 = new ArrayList();
/*  58 */     ArrayList<String> arrayList2 = new ArrayList();
/*  59 */     ArrayList<String> arrayList3 = getSignid();
/*  60 */     ArrayList<String> arrayList4 = getSignlable();
/*  61 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  63 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*  64 */     int j = Util.getIntValue(Util.null2String(this.params.get("isBill")));
/*  65 */     if (i != -1) {
/*     */       
/*  67 */       ArrayList<SearchConditionOption> arrayList5 = new ArrayList();
/*  68 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */ 
/*     */       
/*  71 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, -1, "tableName");
/*     */       
/*  73 */       searchConditionItem.setLabelcol(0);
/*  74 */       searchConditionItem.setFieldcol(8);
/*  75 */       searchConditionItem.setOptions(arrayList5);
/*  76 */       searchConditionItem.setSelectLinkageDatas(hashMap3);
/*     */ 
/*     */ 
/*     */       
/*  80 */       if (j == 1) {
/*     */         
/*  82 */         String str1 = " select tablename from workflow_billdetailtable where billid= ? order by orderid";
/*     */         
/*  84 */         RecordSet recordSet1 = new RecordSet();
/*  85 */         recordSet1.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/*  86 */         byte b = 0;
/*  87 */         while (recordSet1.next())
/*     */         {
/*  89 */           b++;
/*  90 */           String str2 = recordSet1.getString("tablename");
/*  91 */           String str3 = SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b;
/*     */           
/*  93 */           ArrayList<SearchConditionOption> arrayList7 = new ArrayList();
/*     */ 
/*     */           
/*  96 */           String str4 = "select * from workflow_billfield where viewtype=1 and fieldhtmltype=1 and (type=2 or type=3 or type=4 or type=5) and billid= ?  and detailtable= ? order by dsporder asc,id desc";
/*     */ 
/*     */           
/*  99 */           RecordSet recordSet2 = new RecordSet();
/* 100 */           recordSet2.executeQuery(str4, new Object[] { Integer.valueOf(i), str2 });
/*     */           
/* 102 */           if (recordSet2.getCounts() > 0) {
/* 103 */             arrayList5.add(new SearchConditionOption(str2, str3));
/*     */           }
/*     */           
/* 106 */           while (recordSet2.next()) {
/*     */             
/* 108 */             String str5 = recordSet2.getString("id");
/* 109 */             String str6 = SystemEnv.getHtmlLabelName(recordSet2.getInt("fieldlabel"), this.user.getLanguage());
/*     */             
/* 111 */             arrayList1.add(str5);
/* 112 */             arrayList2.add(str6);
/*     */             
/* 114 */             arrayList7.add(new SearchConditionOption(str5, str6));
/*     */             
/* 116 */             hashMap2.put(str5, str3);
/*     */           } 
/*     */           
/* 119 */           SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.SELECT, -1, "fieldId");
/* 120 */           searchConditionItem1.setLabelcol(0);
/* 121 */           searchConditionItem1.setFieldcol(16);
/* 122 */           searchConditionItem1.setOptions(arrayList7);
/*     */           
/* 124 */           hashMap3.put(str2, searchConditionItem1);
/*     */         }
/*     */       
/*     */       } else {
/*     */         
/* 129 */         String str1 = "select distinct t1.groupId  from workflow_formfield      t1,       workflow_formdictdetail t2,       workflow_fieldlable     t3 where t1.isdetail = '1'   and t1.fieldid = t2.id   and t1.fieldid = t3.fieldid   and t3.formid = t1.formid   and t3.isdefault = 1   and t2.fieldhtmltype = 1   and type in (2, 3, 4, 5)   and t1.formid = ?  order by t1.groupId asc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 142 */         RecordSet recordSet1 = new RecordSet();
/* 143 */         recordSet1.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/* 144 */         byte b = 0;
/* 145 */         while (recordSet1.next()) {
/*     */           
/* 147 */           b++;
/* 148 */           String str2 = recordSet1.getString("groupid");
/* 149 */           String str3 = SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b;
/*     */           
/* 151 */           ArrayList<SearchConditionOption> arrayList7 = new ArrayList();
/*     */ 
/*     */           
/* 154 */           String str4 = "select t1.fieldid, t3.fieldlable, t1.groupId  from workflow_formfield      t1,       workflow_formdictdetail t2,       workflow_fieldlable     t3 where t1.isdetail = '1'   and t1.fieldid = t2.id   and t1.fieldid = t3.fieldid   and t3.formid = t1.formid   and t3.isdefault = 1   and t2.fieldhtmltype = 1   and type in (2, 3, 4, 5)   and t1.formid =  ? \tand t1.groupId= ? order by t1.groupId asc, t1.fieldid desc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 168 */           RecordSet recordSet2 = new RecordSet();
/* 169 */           recordSet2.executeQuery(str4, new Object[] { Integer.valueOf(i), str2 });
/* 170 */           if (recordSet2.getCounts() > 0) {
/* 171 */             arrayList5.add(new SearchConditionOption(str2, str3));
/*     */           }
/* 173 */           while (recordSet2.next()) {
/*     */             
/* 175 */             String str5 = recordSet2.getString("fieldid");
/* 176 */             String str6 = recordSet2.getString("fieldlable");
/*     */             
/* 178 */             arrayList1.add(str5);
/* 179 */             arrayList2.add(str6);
/*     */             
/* 181 */             arrayList7.add(new SearchConditionOption(str5, str6));
/*     */             
/* 183 */             hashMap2.put(str5, str3);
/*     */           } 
/*     */           
/* 186 */           SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.SELECT, -1, "fieldId");
/* 187 */           searchConditionItem1.setLabelcol(0);
/* 188 */           searchConditionItem1.setFieldcol(16);
/* 189 */           searchConditionItem1.setOptions(arrayList7);
/*     */           
/* 191 */           hashMap3.put(str2, searchConditionItem1);
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 198 */       String str = "select rowcalstr from workflow_formdetailinfo where formid = ?";
/* 199 */       RecordSet recordSet = new RecordSet();
/* 200 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(i) });
/* 201 */       if (recordSet.next()) {
/*     */         
/* 203 */         String str1 = recordSet.getString("rowcalstr");
/* 204 */         StringTokenizer stringTokenizer = new StringTokenizer(str1, ";");
/* 205 */         while (stringTokenizer.hasMoreTokens()) {
/*     */           
/* 207 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*     */           
/* 209 */           String str2 = stringTokenizer.nextToken();
/* 210 */           String str3 = str2;
/* 211 */           if (!Strings.isNullOrEmpty(str2)) {
/* 212 */             byte b; for (b = 0; b < arrayList3.size(); b++) {
/* 213 */               str3 = Util.StringReplace(str3, arrayList3.get(b), arrayList4.get(b));
/*     */             }
/* 215 */             for (b = 0; b < arrayList1.size(); b++) {
/* 216 */               str3 = Util.StringReplace(str3, "detailfield_" + (String)arrayList1.get(b), arrayList2.get(b));
/*     */             }
/*     */           } 
/*     */           
/* 220 */           hashMap.put("tokenStr", str3);
/* 221 */           hashMap.put("tokenValue", str2);
/*     */           
/* 223 */           String[] arrayOfString = str2.split("=");
/* 224 */           String str4 = arrayOfString[0].replace("detailfield_", "");
/* 225 */           String str5 = (String)hashMap2.get(str4);
/* 226 */           hashMap.put("table", str5);
/* 227 */           arrayList.add(hashMap);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 232 */       ArrayList<HashMap<Object, Object>> arrayList6 = new ArrayList();
/*     */       
/* 234 */       HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 235 */       hashMap4.put("title", SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()));
/* 236 */       hashMap4.put("key", "table");
/* 237 */       hashMap4.put("dataIndex", "table");
/*     */       
/* 239 */       HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 240 */       hashMap5.put("title", SystemEnv.getHtmlLabelName(83842, this.user.getLanguage()) + 
/* 241 */           SystemEnv.getHtmlLabelName(15636, this.user.getLanguage()));
/* 242 */       hashMap5.put("key", "tokenStr");
/* 243 */       hashMap5.put("dataIndex", "tokenStr");
/*     */       
/* 245 */       arrayList6.add(hashMap4);
/* 246 */       arrayList6.add(hashMap5);
/*     */       
/* 248 */       hashMap1.put("detailComp", searchConditionItem);
/* 249 */       hashMap1.put("cols", arrayList6);
/* 250 */       hashMap1.put("datas", arrayList);
/*     */       
/* 252 */       hashMap1.put("logPara", formSetUtil.getLogArray(i));
/*     */     } 
/*     */     
/* 255 */     int k = Util.getIntValue(Util.null2String(this.params.get("isFromMode")));
/*     */     
/* 257 */     hashMap1.put("operatelevel", Integer.valueOf(formAuthorityUtil.getFormOperateLevel(i, j, this.user, k)));
/* 258 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   private ArrayList<String> getSignid() {
/* 263 */     ArrayList<String> arrayList = new ArrayList();
/* 264 */     arrayList.add("+");
/* 265 */     arrayList.add("-");
/* 266 */     arrayList.add("*");
/* 267 */     arrayList.add("/");
/* 268 */     arrayList.add("=");
/* 269 */     arrayList.add("(");
/* 270 */     arrayList.add(")");
/* 271 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private ArrayList<String> getSignlable() {
/* 276 */     ArrayList<String> arrayList = new ArrayList();
/* 277 */     arrayList.add("＋");
/* 278 */     arrayList.add("－");
/* 279 */     arrayList.add("×");
/* 280 */     arrayList.add("÷");
/* 281 */     arrayList.add("＝");
/* 282 */     arrayList.add("(");
/* 283 */     arrayList.add(")");
/* 284 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/DoAddRowCalCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */