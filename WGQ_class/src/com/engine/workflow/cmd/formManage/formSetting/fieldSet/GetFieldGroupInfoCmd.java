/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionGroup;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.engine.core.interceptor.AbstractCommand;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*    */ import com.engine.workflow.util.FormAuthorityUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetFieldGroupInfoCmd
/*    */   extends AbstractCommand<Map<String, Object>>
/*    */ {
/*    */   public GetFieldGroupInfoCmd(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.params = paramMap;
/* 29 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 34 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 35 */     String str = Util.null2String(this.params.get("isBill"));
/* 36 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 37 */     boolean bool = (new ManageDetachComInfo()).isUseFmManageDetach() ? true : false;
/* 38 */     int j = (new FormAuthorityUtil()).getFormOperateLevel(i, Util.getIntValue(str), this.user, 0);
/* 39 */     hashMap1.put("operatelevel", Integer.valueOf(j));
/* 40 */     if (j < 0) {
/* 41 */       hashMap1.put("noRight", Boolean.valueOf(true));
/* 42 */       return (Map)hashMap1;
/*    */     } 
/*    */     
/* 45 */     hashMap1.put("groupTabs", FieldGroupBiz.getFieldGroupTabs(this.user, i, str));
/* 46 */     hashMap1.put("groupNamePrefix", SystemEnv.getHtmlLabelName(517201, this.user.getLanguage()));
/* 47 */     hashMap1.put("deleteGroupContent", SystemEnv.getHtmlLabelName(15097, this.user.getLanguage()));
/* 48 */     hashMap1.put("deleteGroupTips", SystemEnv.getHtmlLabelName(517493, this.user.getLanguage()));
/* 49 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*    */     
/* 51 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 30127, "groupName");
/* 52 */     searchConditionItem1.setViewAttr(3);
/* 53 */     searchConditionItem1.setRules("required|stringLength:100");
/* 54 */     searchConditionItem1.setStringLength(100);
/* 55 */     searchConditionItem1.setIsBase64(true);
/*    */     
/* 57 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 58 */     hashMap2.put("inputType", "multilang");
/* 59 */     hashMap2.put("isBase64", Boolean.valueOf(true));
/* 60 */     searchConditionItem1.setOtherParams(hashMap2);
/*    */     
/* 62 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.CUSTOM, 519541, "groupIcon");
/*    */     
/* 64 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 65 */     arrayList.add(searchConditionItem1);
/* 66 */     arrayList.add(searchConditionItem2);
/* 67 */     SearchConditionGroup searchConditionGroup = new SearchConditionGroup();
/* 68 */     searchConditionGroup.setItems(arrayList);
/*    */     
/* 70 */     hashMap1.put("addGroupConfig", searchConditionGroup);
/* 71 */     return (Map)hashMap1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetFieldGroupInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */