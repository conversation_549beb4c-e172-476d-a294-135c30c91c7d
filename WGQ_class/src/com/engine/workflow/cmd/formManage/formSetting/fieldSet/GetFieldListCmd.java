/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.bean.Checkboxpopedom;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.encrypt.biz.WfEncryptBiz;
/*     */ import com.engine.workflow.biz.DetailOrderBiz;
/*     */ import com.engine.workflow.entity.LogInfoEntity;
/*     */ import com.engine.workflow.util.FormAuthorityUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetFieldListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  40 */   private final String pageUID = "be52013a-6e4e-4da7-be06-9abaaa0d62cd";
/*  41 */   private int formid = 0;
/*  42 */   private int isbill = -1;
/*     */ 
/*     */ 
/*     */   
/*     */   public GetFieldListCmd(Map<String, Object> paramMap, User paramUser) {
/*  47 */     this.params = paramMap;
/*  48 */     this.user = paramUser;
/*  49 */     this.formid = Util.getIntValue(Util.null2String(paramMap.get("formId")), 0);
/*  50 */     this.isbill = Util.getIntValue(Util.null2String(paramMap.get("isBill")), -1);
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  55 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     String str1 = formatTableString();
/*  62 */     String str2 = "be52013a-6e4e-4da7-be06-9abaaa0d62cd_" + Util.getEncrypt(Util.getRandom());
/*  63 */     Util_TableMap.setVal(str2, str1);
/*     */     
/*  65 */     int i = Util.getIntValue(Util.null2String(this.params.get("isFromMode")));
/*     */     
/*  67 */     hashMap.put("sessionkey", str2);
/*  68 */     hashMap.put("logArray", getLogArray());
/*  69 */     hashMap.put("operatelevel", Integer.valueOf((new FormAuthorityUtil()).getFormOperateLevel(this.formid, this.isbill, this.user, i)));
/*  70 */     hashMap.put("detachable", Integer.valueOf((new ManageDetachComInfo()).isUseWfManageDetach() ? 1 : 0));
/*  71 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected List<LogInfoEntity> getLogArray() {
/*  76 */     ArrayList<LogInfoEntity> arrayList = new ArrayList();
/*     */     
/*  78 */     LogInfoEntity logInfoEntity1 = new LogInfoEntity();
/*  79 */     logInfoEntity1.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  80 */     logInfoEntity1.setLogSmallType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  81 */     logInfoEntity1.setTargetId(this.formid + "");
/*  82 */     arrayList.add(logInfoEntity1);
/*     */     
/*  84 */     LogInfoEntity logInfoEntity2 = new LogInfoEntity();
/*  85 */     logInfoEntity2.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  86 */     logInfoEntity2.setBelongType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  87 */     logInfoEntity2.setBelongTypeTargetId(this.formid + "");
/*  88 */     arrayList.add(logInfoEntity2);
/*     */     
/*  90 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected String formatTableString() {
/*  96 */     String str1 = Util.null2String(this.params.get("labelName"));
/*  97 */     String str2 = Util.null2String(this.params.get("htmlType"));
/*  98 */     String str3 = Util.null2String(this.params.get("type"));
/*  99 */     String str4 = Util.null2String(this.params.get("fieldTable"));
/* 100 */     int i = Util.getIntValue(Util.null2String(this.params.get("isFromMode")));
/*     */     
/* 102 */     String str5 = "Wf:form_editformfield";
/* 103 */     String str6 = "checkbox";
/* 104 */     String str7 = PageIdConst.getPageSize("Wf:form_editformfield", this.user.getUID());
/*     */     
/* 106 */     String str8 = "";
/* 107 */     str8 = " where billid=" + this.formid + " ";
/* 108 */     if (this.formid == 0) str8 = " where 1=2  ";
/*     */     
/* 110 */     if (!"".equals(str1)) {
/* 111 */       str8 = str8 + " and b.labelname like '%" + str1 + "%' ";
/*     */     }
/*     */     
/* 114 */     if (!"-1".equals(str2) && !"".equals(str2)) {
/* 115 */       str8 = str8 + " and a.fieldhtmltype = '" + str2 + "' ";
/*     */     }
/*     */ 
/*     */     
/* 119 */     if (i == 1) {
/* 120 */       str8 = str8 + " and a.fieldhtmltype <> '9' ";
/*     */     }
/*     */     
/* 123 */     if (!"-1".equals(str3) && !"".equals(str3)) {
/* 124 */       str8 = str8 + " and a.type = '" + str3 + "' ";
/*     */     }
/*     */     
/* 127 */     if (!"-1".equals(str4) && !"".equals(str4))
/*     */     {
/* 129 */       if ("0".equals(str4)) {
/* 130 */         str8 = str8 + " and a.viewtype = 0 ";
/*     */       } else {
/* 132 */         str8 = str8 + " and a.detailtable = '" + str4 + "' ";
/*     */       } 
/*     */     }
/*     */     
/* 136 */     String str9 = " a.viewtype,a.detailtable,a.dsporder,a.id";
/* 137 */     String str10 = " a.id,a.fieldlabel,a.fieldname,b.labelname,a.viewtype,a.fieldhtmltype,a.type,a.dsporder,a.detailtable,a.fromUser,a.billid,a.fieldgroupid,c.groupname";
/* 138 */     String str11 = " workflow_billfield a left join htmllabelinfo b on a.fieldlabel = b.indexid and b.languageid = " + this.user.getLanguage() + " left join workflow_formfield_group c on a.fieldgroupid = c.id ";
/* 139 */     String str12 = "id";
/* 140 */     String str13 = "ASC";
/* 141 */     String str14 = "false";
/*     */ 
/*     */     
/* 144 */     List<SplitTableColBean> list = createColList();
/* 145 */     Checkboxpopedom checkboxpopedom = createCheckboxpopedom();
/*     */ 
/*     */     
/* 148 */     SplitTableBean splitTableBean = new SplitTableBean();
/* 149 */     splitTableBean.setPageID(str5);
/* 150 */     splitTableBean.setPageUID("be52013a-6e4e-4da7-be06-9abaaa0d62cd");
/* 151 */     splitTableBean.setTableType(str6);
/* 152 */     splitTableBean.setPagesize(str7);
/* 153 */     splitTableBean.setBackfields(str10);
/* 154 */     splitTableBean.setSqlform(str11);
/* 155 */     splitTableBean.setSqlwhere(Util.toHtmlForSplitPage(str8));
/* 156 */     splitTableBean.setSqlorderby(str9);
/* 157 */     splitTableBean.setSqlprimarykey(str12);
/* 158 */     splitTableBean.setSqlsortway(str13);
/* 159 */     splitTableBean.setSqlisdistinct(str14);
/*     */     
/* 161 */     splitTableBean.setCols(list);
/* 162 */     splitTableBean.setCheckboxpopedom(checkboxpopedom);
/*     */     
/* 164 */     return SplitTableUtil.getTableString(splitTableBean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<SplitTableColBean> createColList() {
/* 173 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */     
/* 175 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "fieldlabel", "fieldlabel", getClass().getName() + ".getFieldname", "column:id+column:fromUser+" + this.formid + "+column:viewtype");
/* 176 */     arrayList.add(splitTableColBean1);
/*     */     
/* 178 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldName", "fieldName");
/* 179 */     arrayList.add(splitTableColBean2);
/*     */     
/* 181 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(17997, this.user.getLanguage()), "viewtype", "viewtype", getClass().getName() + ".getViewType", "column:detailtable+column:billid+" + this.user.getLanguage());
/* 182 */     arrayList.add(splitTableColBean3);
/*     */     
/* 184 */     SplitTableColBean splitTableColBean4 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(687, this.user.getLanguage()), "fieldhtmltype", "fieldhtmltype", "weaver.general.FormFieldTransMethod.getHTMLType", this.user.getLanguage() + "+column:type");
/* 185 */     arrayList.add(splitTableColBean4);
/*     */     
/* 187 */     SplitTableColBean splitTableColBean5 = new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "type", "type", "weaver.general.FormFieldTransMethod.getFieldType", "column:fieldhtmltype+column:id+" + this.user.getLanguage());
/* 188 */     arrayList.add(splitTableColBean5);
/* 189 */     if (this.formid > 0) {
/*     */       
/* 191 */       SplitTableColBean splitTableColBean = new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(73, this.user.getLanguage()), "fromUser", "fromUser", "weaver.workflow.workflow.WorkFlowFieldTransMethod.getIsAddByUser");
/* 192 */       arrayList.add(splitTableColBean);
/*     */     } 
/*     */     
/* 195 */     SplitTableColBean splitTableColBean6 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(15513, this.user.getLanguage()), "dsporder", "dsporder");
/* 196 */     arrayList.add(splitTableColBean6);
/*     */     
/* 198 */     boolean bool = "1".equals(Util.null2String(this.params.get("isFromMode")));
/* 199 */     if (!bool) {
/*     */       
/* 201 */       SplitTableColBean splitTableColBean = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(517198, this.user.getLanguage()), "groupname", "groupname");
/* 202 */       arrayList.add(splitTableColBean);
/*     */     } 
/* 204 */     if (WfEncryptBiz.isOpenEncryptSet("WORKFLOW")) {
/*     */       
/* 206 */       SplitTableColBean splitTableColBean = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(526997, this.user.getLanguage()), "id", "id", "weaver.general.FormFieldTransMethod.getEncryptSet", "column:fieldhtmltype+column:type+" + this.user.getLanguage());
/* 207 */       arrayList.add(splitTableColBean);
/*     */     } 
/*     */     
/* 210 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Checkboxpopedom createCheckboxpopedom() {
/* 219 */     Checkboxpopedom checkboxpopedom = new Checkboxpopedom();
/* 220 */     checkboxpopedom.setId("checkbox");
/*     */     
/* 222 */     if (this.formid < 0) {
/* 223 */       checkboxpopedom.setPopedompara("column:fieldname+column:viewtype+column:fieldhtmltype+column:detailtable+" + this.formid + "+column:type");
/* 224 */       checkboxpopedom.setShowmethod("weaver.general.FormFieldTransMethod.getCanCheckBox");
/*     */     } else {
/* 226 */       checkboxpopedom.setPopedompara("column:fromUser");
/* 227 */       checkboxpopedom.setShowmethod("weaver.workflow.workflow.WorkFlowFieldTransMethod.getCanCheckBox");
/*     */     } 
/*     */     
/* 230 */     return checkboxpopedom;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname(String paramString1, String paramString2) {
/* 240 */     String str1 = LabelUtil.getMultiLangLabel(paramString1);
/* 241 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 242 */     String str2 = Util.null2String(arrayOfString[0]);
/* 243 */     String str3 = Util.null2String(arrayOfString[1]);
/* 244 */     int i = Util.getIntValue(Util.null2String(arrayOfString[2]));
/* 245 */     String str4 = Util.null2String(arrayOfString[3]);
/*     */     
/* 247 */     if (i > 0 && "1".equals(str3)) {
/* 248 */       return str1;
/*     */     }
/* 250 */     return "<a href=\"javascript:worflowFieldListUtil.editForm('" + str2 + "'," + str4 + ")\">" + str1 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getViewType(String paramString1, String paramString2) {
/* 258 */     String str1 = "";
/* 259 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 260 */     int i = Util.getIntValue(arrayOfString[2]);
/* 261 */     int j = Util.getIntValue(arrayOfString[1]);
/* 262 */     String str2 = Util.null2String(arrayOfString[0]);
/* 263 */     RecordSet recordSet = new RecordSet();
/* 264 */     if (paramString1.equals("0")) {
/* 265 */       str1 = SystemEnv.getHtmlLabelName(21778, i);
/* 266 */     } else if (paramString1.equals("1")) {
/* 267 */       str1 = SystemEnv.getHtmlLabelName(19325, i);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 275 */       DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/* 276 */       Map map = detailOrderBiz.getDetailOrderMap(j);
/* 277 */       int k = ((Integer)map.get(str2)).intValue();
/* 278 */       str1 = str1 + k;
/*     */     } 
/* 280 */     return str1;
/*     */   }
/*     */   
/*     */   public GetFieldListCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetFieldListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */