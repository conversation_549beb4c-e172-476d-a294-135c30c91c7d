/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.util.FormAuthorityUtil;
/*     */ import com.engine.workflow.util.FormSetUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoAddColCalCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DoAddColCalCmd(Map<String, Object> paramMap, User paramUser) {
/*  28 */     this.params = paramMap;
/*  29 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  34 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*     */     Map<String, Object> map;
/*  40 */     FormSetUtil formSetUtil = new FormSetUtil();
/*  41 */     FormAuthorityUtil formAuthorityUtil = new FormAuthorityUtil();
/*     */     
/*  43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  44 */     int i = Util.getIntValue(Util.null2String(this.params.get("isBill")), 0);
/*  45 */     int j = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/*     */     
/*  47 */     if (i == 0) {
/*  48 */       map = getOldFormColCal();
/*     */     } else {
/*  50 */       map = getNewFormColCal();
/*     */     } 
/*     */     
/*  53 */     int k = Util.getIntValue(Util.null2String(this.params.get("isFromMode")));
/*     */     
/*  55 */     map.put("operatelevel", Integer.valueOf(formAuthorityUtil.getFormOperateLevel(j, i, this.user, k)));
/*  56 */     map.put("logPara", formSetUtil.getLogArray(j));
/*     */     
/*  58 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> getOldFormColCal() {
/*  66 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  67 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */ 
/*     */     
/*  70 */     if (i != -1) {
/*     */       
/*  72 */       String str1 = "";
/*  73 */       String str2 = "";
/*     */       
/*  75 */       RecordSet recordSet1 = new RecordSet();
/*  76 */       recordSet1.execute("select * from workflow_formdetailinfo where formid =" + i);
/*  77 */       if (recordSet1.next()) {
/*  78 */         str1 = recordSet1.getString("colcalstr");
/*  79 */         str2 = recordSet1.getString("maincalstr");
/*     */       } 
/*     */ 
/*     */       
/*  83 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  84 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  85 */       hashMap1.put("key", "");
/*  86 */       hashMap1.put("showname", "");
/*  87 */       arrayList1.add(hashMap1);
/*  88 */       RecordSet recordSet2 = new RecordSet();
/*  89 */       String str3 = "select t1.fieldid,t3.fieldlable from workflow_formfield t1,workflow_formdict t2,workflow_fieldlable t3 where (t1.isdetail<>'1' or t1.isdetail is null) and t1.fieldid=t2.id and t1.fieldid=t3.fieldid and t3.formid=t1.formid and t3.isdefault=1 and t2.fieldhtmltype=1 and type in (2,3,4,5) and t1.formid=" + i + " order by t1.fieldid desc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 101 */       recordSet2.execute(str3);
/* 102 */       while (recordSet2.next()) {
/* 103 */         hashMap1 = new HashMap<>();
/* 104 */         hashMap1.put("key", recordSet2.getString("fieldid"));
/* 105 */         hashMap1.put("showname", recordSet2.getString("fieldlable"));
/* 106 */         arrayList1.add(hashMap1);
/*     */       } 
/*     */       
/* 109 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*     */       
/* 111 */       RecordSet recordSet3 = new RecordSet();
/* 112 */       str3 = "select t1.fieldid,t3.fieldlable,t1.groupId from workflow_formfield t1,workflow_formdictdetail t2,workflow_fieldlable t3 where t1.isdetail='1' and t1.fieldid=t2.id and t1.fieldid=t3.fieldid and t3.formid=t1.formid and t3.isdefault=1 and t2.fieldhtmltype=1 and type in (2,3,4,5) and t1.formid=" + i + " order by t1.groupId asc,t1.fieldid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 123 */       recordSet3.execute(str3);
/* 124 */       ArrayList<Integer> arrayList = new ArrayList();
/* 125 */       while (recordSet3.next()) {
/*     */         
/* 127 */         String str4 = recordSet3.getString("fieldid");
/* 128 */         String str5 = recordSet3.getString("fieldlable");
/* 129 */         int j = recordSet3.getInt("groupId");
/*     */         
/* 131 */         if (!arrayList.contains(Integer.valueOf(j))) {
/* 132 */           arrayList.add(Integer.valueOf(j));
/*     */         }
/*     */         
/* 135 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 136 */         hashMap2.put("fieldId", str4);
/* 137 */         hashMap2.put("fieldName", str5);
/* 138 */         hashMap2.put("isTotal", (str1.indexOf("detailfield_" + str4) == -1) ? "0" : "1");
/*     */         
/* 140 */         hashMap2.put("detailTableTxt", SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + arrayList.size());
/* 141 */         hashMap2.put("detailTable", Integer.valueOf(j));
/*     */         
/* 143 */         String str6 = ""; byte b; int k;
/* 144 */         for (b = 0, k = arrayList1.size(); b < k; b++) {
/* 145 */           Map map = arrayList1.get(b);
/* 146 */           String str = (String)map.get("key");
/* 147 */           if (str2.indexOf("mainfield_" + str + "=detailfield_" + str4) != -1) {
/* 148 */             str6 = str;
/*     */             break;
/*     */           } 
/*     */         } 
/* 152 */         hashMap2.put("mainFieldId", str6);
/* 153 */         arrayList2.add(hashMap2);
/*     */       } 
/*     */ 
/*     */       
/* 157 */       hashMap.put("datas", arrayList2);
/* 158 */       hashMap.put("mainFieldOptions", arrayList1);
/*     */     } 
/*     */ 
/*     */     
/* 162 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> getNewFormColCal() {
/* 171 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 172 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     
/* 174 */     if (i != -1) {
/*     */       
/* 176 */       String str1 = "";
/* 177 */       String str2 = "";
/*     */       
/* 179 */       RecordSet recordSet1 = new RecordSet();
/* 180 */       recordSet1.execute("select * from workflow_formdetailinfo where formid =" + i);
/* 181 */       if (recordSet1.next()) {
/* 182 */         str1 = recordSet1.getString("colcalstr");
/* 183 */         str2 = recordSet1.getString("maincalstr");
/*     */       } 
/*     */ 
/*     */       
/* 187 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 188 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 189 */       hashMap1.put("key", "");
/* 190 */       hashMap1.put("showname", "");
/* 191 */       arrayList1.add(hashMap1);
/* 192 */       RecordSet recordSet2 = new RecordSet();
/* 193 */       recordSet2.execute("select * from workflow_billfield where viewtype=0 and fieldhtmltype=1 and (type=2 or type=3 or type=4 or type=5) and billid=" + i + " order by id");
/* 194 */       while (recordSet2.next()) {
/* 195 */         hashMap1 = new HashMap<>();
/* 196 */         hashMap1.put("key", recordSet2.getString("id"));
/* 197 */         hashMap1.put("showname", SystemEnv.getHtmlLabelName(recordSet2.getInt("fieldlabel"), this.user.getLanguage()));
/* 198 */         arrayList1.add(hashMap1);
/*     */       } 
/*     */ 
/*     */       
/* 202 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 203 */       String str3 = " select tablename from workflow_billdetailtable where billid=" + i + " order by orderid";
/* 204 */       RecordSet recordSet3 = new RecordSet();
/* 205 */       recordSet3.execute(str3);
/* 206 */       byte b = 0;
/* 207 */       while (recordSet3.next()) {
/* 208 */         b++;
/* 209 */         String str4 = recordSet3.getString("tablename");
/* 210 */         String str5 = SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b;
/* 211 */         hashMap2.put(str4, str5);
/*     */       } 
/*     */       
/* 214 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*     */       
/* 216 */       RecordSet recordSet4 = new RecordSet();
/* 217 */       recordSet4.execute("select * from workflow_billfield where viewtype=1 and fieldhtmltype=1 and (type=2 or type=3 or type=4 or type=5) and billid=" + i + " order by detailtable, dsporder, id");
/* 218 */       while (recordSet4.next()) {
/*     */         
/* 220 */         String str4 = recordSet4.getString("id");
/* 221 */         String str5 = SystemEnv.getHtmlLabelName(recordSet4.getInt("fieldlabel"), this.user.getLanguage());
/* 222 */         String str6 = recordSet4.getString("detailtable");
/*     */         
/* 224 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 225 */         hashMap3.put("fieldId", str4);
/* 226 */         hashMap3.put("fieldName", str5);
/* 227 */         hashMap3.put("isTotal", (str1.indexOf("detailfield_" + str4) == -1) ? "0" : "1");
/* 228 */         hashMap3.put("detailTableTxt", Util.null2String((String)hashMap2.get(str6)));
/* 229 */         hashMap3.put("detailTable", str6);
/*     */         
/* 231 */         String str7 = ""; byte b1; int j;
/* 232 */         for (b1 = 0, j = arrayList1.size(); b1 < j; b1++) {
/* 233 */           Map map = arrayList1.get(b1);
/* 234 */           String str = (String)map.get("key");
/* 235 */           if (str2.indexOf("mainfield_" + str + "=detailfield_" + str4) != -1) {
/* 236 */             str7 = str;
/*     */             break;
/*     */           } 
/*     */         } 
/* 240 */         hashMap3.put("mainFieldId", str7);
/* 241 */         arrayList2.add(hashMap3);
/*     */       } 
/*     */ 
/*     */       
/* 245 */       hashMap.put("datas", arrayList2);
/* 246 */       hashMap.put("mainFieldOptions", arrayList1);
/*     */     } 
/*     */ 
/*     */     
/* 250 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/DoAddColCalCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */