/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.BatchRecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UpdateFieldGroupCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public UpdateFieldGroupCmd(User paramUser, Map<String, Object> paramMap) {
/* 21 */     this.user = paramUser;
/* 22 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 27 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 34 */       int i = Util.getIntValue(Util.null2String(this.params.get("groupid")));
/* 35 */       String str1 = Util.null2String(this.params.get("fieldIds"));
/* 36 */       int j = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/* 37 */       int k = Util.getIntValue(Util.null2String(this.params.get("isBill")), -1);
/* 38 */       String str2 = "";
/* 39 */       ArrayList<ArrayList<Integer>> arrayList1 = new ArrayList();
/* 40 */       ArrayList<ArrayList<Integer>> arrayList2 = new ArrayList();
/*    */       
/* 42 */       String[] arrayOfString = Util.splitString(str1, ",");
/* 43 */       for (String str : arrayOfString) {
/* 44 */         int m = Util.getIntValue(str);
/* 45 */         ArrayList<Integer> arrayList = new ArrayList();
/* 46 */         if (m < 0) {
/* 47 */           arrayList.add(Integer.valueOf(i));
/* 48 */           arrayList.add(Integer.valueOf(j));
/* 49 */           arrayList.add(Integer.valueOf(k));
/* 50 */           arrayList.add(Integer.valueOf(m));
/* 51 */           arrayList2.add(arrayList);
/*    */         } else {
/* 53 */           arrayList.add(Integer.valueOf(i));
/* 54 */           arrayList.add(Integer.valueOf(j));
/* 55 */           arrayList.add(Integer.valueOf(m));
/* 56 */           arrayList1.add(arrayList);
/*    */         } 
/*    */       } 
/*    */       
/* 60 */       BatchRecordSet batchRecordSet = new BatchRecordSet();
/* 61 */       if (arrayList1.size() > 0) {
/* 62 */         if (k == 1) {
/* 63 */           str2 = "update workflow_billfield set fieldgroupid = ? where billid = ? and id  = ?";
/* 64 */         } else if (k == 0) {
/* 65 */           str2 = "update workflow_formfield set fieldgroupid = ? where formid = ? and fieldid  = ?";
/*    */         } 
/* 67 */         batchRecordSet.executeBatchSqlNew(str2, arrayList1);
/*    */       } 
/* 69 */       if (arrayList2.size() > 0) {
/* 70 */         str2 = "update workflow_systemfield_group set fieldgroupid = ? where formid = ? and isbill = ? and fieldid  = ? ";
/* 71 */         batchRecordSet.executeBatchSqlNew(str2, arrayList2);
/*    */       } 
/* 73 */       hashMap.put("success", Boolean.valueOf(true));
/* 74 */     } catch (Exception exception) {
/* 75 */       exception.printStackTrace();
/* 76 */       hashMap.put("success", Boolean.valueOf(false));
/*    */     } 
/* 78 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/UpdateFieldGroupCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */