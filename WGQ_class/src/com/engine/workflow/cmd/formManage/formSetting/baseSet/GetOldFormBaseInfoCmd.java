/*     */ package com.engine.workflow.cmd.formManage.formSetting.baseSet;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.entity.LogInfoEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ import weaver.workflow.form.FormManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetOldFormBaseInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  36 */   private String subCompanyId2 = "";
/*  37 */   private String subCompanyId3 = "";
/*  38 */   protected int operatelevel = -1;
/*     */ 
/*     */ 
/*     */   
/*     */   private final String FORM_RIGHT_STR = "FormManage:All";
/*     */ 
/*     */ 
/*     */   
/*     */   public GetOldFormBaseInfoCmd() {
/*  47 */     this.FORM_RIGHT_STR = "FormManage:All"; } public GetOldFormBaseInfoCmd(Map<String, Object> paramMap, User paramUser) { this.FORM_RIGHT_STR = "FormManage:All";
/*     */     this.params = paramMap;
/*     */     this.user = paramUser; }
/*     */    public BizLogContext getLogContext() {
/*  51 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  56 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  58 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  59 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  61 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  62 */     hashMap2.put("items", getItemList());
/*  63 */     arrayList.add(hashMap2);
/*     */     
/*  65 */     hashMap1.put("conditioninfo", arrayList);
/*  66 */     hashMap1.put("subCompanyId", this.subCompanyId2);
/*  67 */     hashMap1.put("subCompanyId3", this.subCompanyId3);
/*  68 */     hashMap1.put("logArray", getLogArray());
/*  69 */     hashMap1.put("operatelevel", Integer.valueOf(this.operatelevel));
/*     */ 
/*     */ 
/*     */     
/*  73 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   protected List<LogInfoEntity> getLogArray() {
/*  77 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     
/*  79 */     ArrayList<LogInfoEntity> arrayList = new ArrayList();
/*     */     
/*  81 */     LogInfoEntity logInfoEntity1 = new LogInfoEntity();
/*  82 */     logInfoEntity1.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  83 */     logInfoEntity1.setLogSmallType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  84 */     logInfoEntity1.setTargetId(i + "");
/*  85 */     arrayList.add(logInfoEntity1);
/*     */     
/*  87 */     LogInfoEntity logInfoEntity2 = new LogInfoEntity();
/*  88 */     logInfoEntity2.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  89 */     logInfoEntity2.setBelongType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  90 */     logInfoEntity2.setBelongTypeTargetId(i + "");
/*  91 */     arrayList.add(logInfoEntity2);
/*     */     
/*  93 */     return arrayList;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionItem> getItemList() {
/*  97 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  98 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 100 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 101 */     int j = Util.getIntValue(Util.null2String(this.params.get("isFromMode")));
/* 102 */     FormManager formManager = new FormManager();
/* 103 */     formManager.setFormid(i);
/*     */     try {
/* 105 */       formManager.getFormInfo();
/* 106 */     } catch (Exception exception) {
/* 107 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 110 */     int k = -1;
/*     */     
/* 112 */     this.subCompanyId2 = "" + formManager.getSubCompanyId2();
/* 113 */     this.subCompanyId3 = "" + formManager.getSubCompanyId3();
/*     */     
/* 115 */     RecordSet recordSet = new RecordSet();
/* 116 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 117 */     boolean bool = isDetachable();
/* 118 */     if (bool) {
/* 119 */       if (j == 1) {
/* 120 */         k = Util.getIntValue(this.subCompanyId3, -1);
/*     */       } else {
/* 122 */         k = Util.getIntValue(this.subCompanyId2, -1);
/*     */       } 
/* 124 */       if (k == -1)
/*     */       {
/*     */         
/* 127 */         if (k < 0) {
/* 128 */           if (this.user.getUID() == 1) {
/* 129 */             recordSet.executeProc("SystemSet_Select", "");
/* 130 */             if (recordSet.next()) {
/* 131 */               k = Util.getIntValue(recordSet.getString("wfdftsubcomid"), 0);
/*     */             }
/*     */           } else {
/* 134 */             String str = "";
/*     */             try {
/* 136 */               str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "WorkflowManage:All", 0);
/* 137 */             } catch (Exception exception) {
/* 138 */               exception.printStackTrace();
/*     */             } 
/* 140 */             if (!"".equals(str)) {
/* 141 */               k = Util.getIntValue(str.split(",")[0]);
/*     */             }
/*     */           } 
/*     */         }
/*     */       }
/* 146 */       if (j == 1) {
/* 147 */         if (!this.subCompanyId3.equals("-1")) {
/* 148 */           this.operatelevel = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "FormManage:All", Util.getIntValue(this.subCompanyId3));
/*     */         } else {
/* 150 */           this.operatelevel = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "FormManage:All", k);
/*     */         } 
/*     */       } else {
/* 153 */         this.operatelevel = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "FormManage:All", k);
/*     */       }
/*     */     
/* 156 */     } else if (HrmUserVarify.checkUserRight("FormManage:All", this.user)) {
/* 157 */       this.operatelevel = 2;
/*     */     } 
/* 159 */     if (k == 0) {
/* 160 */       this.subCompanyId2 = "";
/* 161 */       this.subCompanyId3 = "";
/*     */     } else {
/* 163 */       this.subCompanyId2 = "" + k;
/* 164 */       if (j == 1) {
/* 165 */         if (this.subCompanyId3.equals("-1")) {
/* 166 */           this.subCompanyId3 = "" + k;
/*     */         }
/*     */       } else {
/* 169 */         this.subCompanyId3 = "" + k;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 174 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15451, "formName");
/* 175 */     searchConditionItem1.setValue(TextUtil.toBase64ForMultilang(formManager.getFormname()));
/* 176 */     searchConditionItem1.setViewAttr(3);
/* 177 */     searchConditionItem1.setLabelcol(6);
/* 178 */     searchConditionItem1.setFieldcol(10);
/* 179 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 180 */     hashMap1.put("inputType", "multilang");
/* 181 */     hashMap1.put("isBase64", Boolean.valueOf(true));
/* 182 */     searchConditionItem1.setOtherParams(hashMap1);
/* 183 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/* 186 */     if (bool) {
/* 187 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subCompanyId", "164");
/* 188 */       BrowserBean browserBean = searchConditionItem.getBrowserConditionParam();
/* 189 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 190 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 191 */       hashMap.put("id", this.subCompanyId2);
/* 192 */       hashMap.put("name", (new SubCompanyComInfo()).getSubCompanyname(this.subCompanyId2));
/* 193 */       arrayList1.add(hashMap);
/* 194 */       browserBean.setReplaceDatas(arrayList1);
/* 195 */       browserBean.setHasBorder(true);
/* 196 */       searchConditionItem.setLabelcol(6);
/* 197 */       searchConditionItem.setFieldcol(10);
/* 198 */       searchConditionItem.setViewAttr(3);
/* 199 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */ 
/*     */     
/* 203 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.TEXTAREA, 15452, "formDes");
/* 204 */     searchConditionItem2.setValue(formManager.getFormdes());
/* 205 */     searchConditionItem2.setLabelcol(6);
/* 206 */     searchConditionItem2.setFieldcol(10);
/* 207 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 208 */     hashMap2.put("maxRows", Integer.valueOf(4));
/* 209 */     hashMap2.put("minRows", Integer.valueOf(4));
/* 210 */     searchConditionItem2.setOtherParams(hashMap2);
/* 211 */     arrayList.add(searchConditionItem2);
/*     */     
/* 213 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean hasUserRight() {
/* 221 */     return HrmUserVarify.checkUserRight("FormManage:All", this.user);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean isDetachable() {
/* 229 */     return (new ManageDetachComInfo()).isUseWfManageDetach();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/baseSet/GetOldFormBaseInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */