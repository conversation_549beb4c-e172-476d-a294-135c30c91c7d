/*    */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*    */ 
/*    */ import com.api.workflow.bean.PageTabInfo;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetFormTabNameCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetFormTabNameCmd() {}
/*    */   
/*    */   public GetFormTabNameCmd(Map<String, Object> paramMap, User paramUser) {
/* 29 */     this.params = paramMap;
/* 30 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 35 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 41 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/* 42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 43 */     if (bool) {
/* 44 */       hashMap.put("datas", getTabInfos());
/* 45 */       hashMap.put("detachable", Boolean.valueOf((new ManageDetachComInfo()).isUseWfManageDetach()));
/* 46 */       hashMap.put("userRight", Boolean.valueOf(bool));
/*    */     } else {
/* 48 */       hashMap.put("noRight", Boolean.valueOf(true));
/*    */     } 
/* 50 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   protected List<PageTabInfo> getTabInfos() {
/* 54 */     ArrayList<PageTabInfo> arrayList = new ArrayList();
/*    */     
/* 56 */     arrayList.add(new PageTabInfo("allForm", SystemEnv.getHtmlLabelName(125025, this.user.getLanguage()), 0, true, "#000000"));
/*    */     
/* 58 */     arrayList.add(new PageTabInfo("customForm", SystemEnv.getHtmlLabelName(125026, this.user.getLanguage()), 1, true, "#000000"));
/*    */     
/* 60 */     arrayList.add(new PageTabInfo("sysForm", SystemEnv.getHtmlLabelName(125027, this.user.getLanguage()), 2, true, "#000000"));
/*    */ 
/*    */     
/* 63 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/GetFormTabNameCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */