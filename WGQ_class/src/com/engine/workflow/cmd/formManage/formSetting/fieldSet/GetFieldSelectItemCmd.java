/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.SelectFieldItemBiz;
/*    */ import java.sql.ResultSetMetaData;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.FormFieldTransMethod;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.field.BrowserComInfo;
/*    */ import weaver.workflow.selectItem.SelectItemManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetFieldSelectItemCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/* 31 */   private int formid = 0;
/* 32 */   private int isFromMode = 0;
/* 33 */   private String tablename = "";
/*    */   private ResultSetMetaData tablename_metaData;
/*    */   private boolean canChange = false;
/*    */   private boolean isFormmodeUse = false;
/*    */   private boolean isFieldNoValueCanEdit = false;
/* 38 */   private SelectItemManager selectItemManager = new SelectItemManager();
/* 39 */   private FormFieldTransMethod formFieldTransMethod = new FormFieldTransMethod();
/* 40 */   private BrowserComInfo browserComInfo = new BrowserComInfo();
/* 41 */   private SelectFieldItemBiz selectFieldItemBiz = new SelectFieldItemBiz();
/*    */ 
/*    */ 
/*    */   
/*    */   public GetFieldSelectItemCmd(Map<String, Object> paramMap, User paramUser) {
/* 46 */     this.params = paramMap;
/* 47 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 52 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 59 */     int i = Util.getIntValue(Util.null2String(this.params.get("fieldId")));
/* 60 */     int j = Util.getIntValue(Util.null2String(this.params.get("isBill")));
/* 61 */     int k = Util.getIntValue(Util.null2String(this.params.get("billId")));
/* 62 */     int m = Util.getIntValue(Util.null2String(this.params.get("isDetail")));
/*    */     
/* 64 */     SelectFieldItemBiz selectFieldItemBiz = new SelectFieldItemBiz();
/* 65 */     Map map = selectFieldItemBiz.getSelectItemDatas(i, j, k, m, this.user);
/* 66 */     hashMap.put("datas", map.get("datas"));
/* 67 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public GetFieldSelectItemCmd() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetFieldSelectItemCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */