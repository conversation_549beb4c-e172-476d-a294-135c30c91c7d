/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoSaveCalCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  30 */   private BizLogContext bizLogContext = new BizLogContext();
/*  31 */   private Map<String, String> fieldTableMap = new HashMap<>();
/*     */   
/*     */   public DoSaveCalCmd(Map<String, Object> paramMap, User paramUser) {
/*  34 */     this.params = paramMap;
/*  35 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  41 */     String str1 = Util.null2String(this.params.get("formId"));
/*  42 */     int i = Util.getIntValue(Util.null2String(this.params.get("isBill")));
/*     */     
/*  44 */     this.bizLogContext.setDateObject(new Date());
/*  45 */     this.bizLogContext.setUserid(this.user.getUID());
/*  46 */     this.bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  47 */     this.bizLogContext.setParams(this.params);
/*  48 */     this.bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*     */     
/*  50 */     this.bizLogContext.setTargetId(str1);
/*  51 */     this.bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*     */     
/*  53 */     this.bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  54 */     this.bizLogContext.setBelongTypeTargetId(str1);
/*     */     
/*  56 */     String str2 = "";
/*  57 */     RecordSet recordSet = new RecordSet();
/*  58 */     if (i == 1) {
/*  59 */       recordSet.execute("select namelabel from workflow_bill where id=" + str1);
/*  60 */       recordSet.next();
/*  61 */       str2 = SystemEnv.getHtmlLabelName(recordSet.getInt("namelabel"), this.user.getLanguage());
/*     */     } else {
/*  63 */       recordSet.execute("select formname from workflow_formbase where id=" + str1);
/*  64 */       recordSet.next();
/*  65 */       str2 = recordSet.getString("formname");
/*     */     } 
/*  67 */     this.bizLogContext.setBelongTypeTargetName(str2);
/*     */     
/*  69 */     return this.bizLogContext;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  77 */     String str = Util.null2String(this.params.get("ruleType"));
/*  78 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/*     */     
/*  80 */     if (str.equalsIgnoreCase("col")) {
/*     */       
/*  82 */       getColLogContext();
/*     */       
/*  84 */       String str1 = "";
/*  85 */       String str2 = "";
/*  86 */       String str3 = Util.null2String(this.params.get("datas"));
/*  87 */       JSONArray jSONArray = JSONArray.parseArray(str3);
/*     */       
/*  89 */       if (jSONArray != null) {
/*     */         
/*  91 */         for (byte b = 0; b < jSONArray.size(); b++) {
/*     */           
/*  93 */           JSONObject jSONObject = jSONArray.getJSONObject(b);
/*     */           
/*  95 */           String str5 = jSONObject.getString("fieldId");
/*     */           
/*  97 */           if (!Strings.isNullOrEmpty(str5)) {
/*     */             
/*  99 */             String str6 = jSONObject.getString("isTotal");
/* 100 */             if ("1".equals(str6)) {
/* 101 */               str1 = str1 + ";detailfield_" + str5;
/*     */             }
/* 103 */             String str7 = jSONObject.getString("mainFieldId");
/*     */             
/* 105 */             if (!Strings.isNullOrEmpty(str7)) {
/* 106 */               str2 = str2 + ";mainfield_" + str7 + "=detailfield_" + str5;
/*     */             }
/*     */           } 
/*     */         } 
/* 110 */         if (!Strings.isNullOrEmpty(str2)) {
/* 111 */           str2 = str2.substring(1).replaceAll("\\s*", "");
/*     */         }
/* 113 */         if (!Strings.isNullOrEmpty(str1)) {
/* 114 */           str1 = str1.substring(1).replaceAll("\\s*", "");
/*     */         }
/*     */       } 
/*     */       
/* 118 */       String str4 = "select * from workflow_formdetailinfo where formid=" + i;
/* 119 */       RecordSet recordSet1 = new RecordSet();
/* 120 */       recordSet1.execute(str4);
/*     */       
/* 122 */       if (recordSet1.next()) {
/* 123 */         str4 = "update workflow_formdetailinfo set colcalstr='" + str1 + "',maincalstr='" + str2 + "' where formid=" + i;
/*     */       } else {
/* 125 */         str4 = "insert into workflow_formdetailinfo(formid,rowcalstr,colcalstr,maincalstr) values(" + i + ",'','" + str1 + "','" + str2 + "')";
/*     */       } 
/* 127 */       RecordSet recordSet2 = new RecordSet();
/* 128 */       recordSet2.executeUpdate(str4, new Object[0]);
/*     */     }
/* 130 */     else if (str.equalsIgnoreCase("row")) {
/*     */       
/* 132 */       getRowLogContext();
/*     */       
/* 134 */       String str1 = Util.null2String(this.params.get("datas"));
/* 135 */       String str2 = "";
/* 136 */       JSONArray jSONArray = JSONArray.parseArray(str1);
/* 137 */       for (byte b = 0; b < jSONArray.size(); b++) {
/* 138 */         JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 139 */         str2 = str2 + ";" + jSONObject.getString("tokenValue");
/*     */       } 
/* 141 */       if (!Strings.isNullOrEmpty(str2)) {
/* 142 */         str2 = str2.substring(1).replaceAll("\\s*", "");
/* 143 */         str2 = str2.replaceAll("×", "*");
/* 144 */         str2 = str2.replaceAll("÷", "/");
/*     */       } 
/*     */       
/* 147 */       String str3 = "select rowcalstr from workflow_formdetailinfo where formid=" + i;
/* 148 */       RecordSet recordSet1 = new RecordSet();
/* 149 */       recordSet1.execute(str3);
/* 150 */       if (recordSet1.next()) {
/* 151 */         str3 = "update workflow_formdetailinfo set rowcalstr='" + str2 + "' where formid=" + i;
/*     */       } else {
/* 153 */         str3 = "insert into workflow_formdetailinfo(formid,rowcalstr,colcalstr,maincalstr) values(" + i + ",'" + str2 + "','','')";
/*     */       } 
/* 155 */       RecordSet recordSet2 = new RecordSet();
/* 156 */       recordSet2.execute(str3);
/*     */     } 
/*     */ 
/*     */     
/* 160 */     hashMap.put("state", "1");
/* 161 */     hashMap.put("msg", SystemEnv.getHtmlLabelName(83551, this.user.getLanguage()));
/* 162 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getColLogContext() {
/* 170 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     
/* 172 */     String str1 = "";
/* 173 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 174 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */     
/* 177 */     String str2 = "";
/* 178 */     String str3 = "";
/* 179 */     String str4 = "";
/* 180 */     String str5 = "";
/* 181 */     RecordSet recordSet = new RecordSet();
/* 182 */     recordSet.execute("select * from workflow_formdetailinfo where formid =" + i);
/* 183 */     if (recordSet.next()) {
/* 184 */       str4 = recordSet.getString("colcalstr");
/* 185 */       str5 = recordSet.getString("maincalstr");
/* 186 */       hashMap1.put("colcalstr", str4);
/* 187 */       hashMap1.put("maincalstr", str5);
/*     */       
/* 189 */       if (!Strings.isNullOrEmpty(str4)) {
/* 190 */         String[] arrayOfString = str4.split(";");
/* 191 */         for (String str11 : arrayOfString) {
/* 192 */           String str12 = str11.replace("detailfield_", "");
/* 193 */           str2 = str2 + "、" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "[" + getFieldTableNameById(str12) + "]" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "[" + getFieldLabelName(str12) + "]";
/*     */         } 
/*     */       } 
/* 196 */       if (!Strings.isNullOrEmpty(str5)) {
/* 197 */         String[] arrayOfString = str5.split(";");
/* 198 */         for (String str11 : arrayOfString) {
/* 199 */           String[] arrayOfString1 = str11.split("=");
/* 200 */           String str12 = arrayOfString1[0].replace("mainfield_", "");
/* 201 */           String str13 = arrayOfString1[1].replace("detailfield_", "");
/* 202 */           str3 = str3 + "、" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "[" + getFieldTableNameById(str13) + "]" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "[" + getFieldLabelName(str13) + "]" + SystemEnv.getHtmlLabelName(18746, ThreadVarLanguage.getLang()) + "[" + getFieldLabelName(str12) + "]";
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 209 */     String str6 = "";
/* 210 */     String str7 = "";
/* 211 */     String str8 = "";
/* 212 */     String str9 = "";
/* 213 */     String str10 = Util.null2String(this.params.get("datas"));
/* 214 */     JSONArray jSONArray = JSONArray.parseArray(str10);
/* 215 */     if (jSONArray != null) {
/*     */       
/* 217 */       for (byte b = 0; b < jSONArray.size(); b++) {
/*     */         
/* 219 */         JSONObject jSONObject = jSONArray.getJSONObject(b);
/*     */         
/* 221 */         String str = jSONObject.getString("fieldId");
/*     */         
/* 223 */         if (!Strings.isNullOrEmpty(str)) {
/*     */           
/* 225 */           String str11 = jSONObject.getString("isTotal");
/* 226 */           if ("1".equals(str11)) {
/* 227 */             str8 = str8 + ";detailfield_" + str;
/* 228 */             str6 = str6 + "、" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "[" + getFieldTableNameById(str) + "]" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "[" + getFieldLabelName(str) + "]";
/*     */           } 
/* 230 */           String str12 = jSONObject.getString("mainFieldId");
/* 231 */           if (!Strings.isNullOrEmpty(str12)) {
/* 232 */             str9 = str9 + ";mainfield_" + str12 + "=detailfield_" + str;
/* 233 */             str7 = str7 + "、" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "[" + getFieldTableNameById(str) + "]" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "[" + getFieldLabelName(str) + "]" + SystemEnv.getHtmlLabelName(18746, ThreadVarLanguage.getLang()) + "[" + getFieldLabelName(str12) + "]";
/*     */           } 
/*     */         } 
/*     */       } 
/* 237 */       if (!Strings.isNullOrEmpty(str9)) {
/* 238 */         str9 = str9.substring(1);
/*     */       }
/* 240 */       hashMap2.put("colcalstr", str8);
/* 241 */       hashMap2.put("maincalstr", str9);
/*     */     } 
/*     */ 
/*     */     
/* 245 */     if (Strings.isNullOrEmpty(str2) && !Strings.isNullOrEmpty(str6)) {
/* 246 */       str1 = str1 + "" + SystemEnv.getHtmlLabelName(10005603, ThreadVarLanguage.getLang()) + "" + str6.substring(1);
/*     */     }
/* 248 */     if (!Strings.isNullOrEmpty(str2) && Strings.isNullOrEmpty(str6)) {
/* 249 */       str1 = str1 + "；" + str2.substring(1) + "" + SystemEnv.getHtmlLabelName(10005604, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 251 */     if (!Strings.isNullOrEmpty(str2) && !Strings.isNullOrEmpty(str6)) {
/* 252 */       str1 = str1 + "；" + SystemEnv.getHtmlLabelName(10005605, ThreadVarLanguage.getLang()) + "" + str2.substring(1) + "" + SystemEnv.getHtmlLabelName(10005606, ThreadVarLanguage.getLang()) + "" + str6.substring(1);
/*     */     }
/*     */     
/* 255 */     if (Strings.isNullOrEmpty(str3) && !Strings.isNullOrEmpty(str7)) {
/* 256 */       str1 = str1 + "；" + str7.substring(1);
/*     */     }
/* 258 */     if (!Strings.isNullOrEmpty(str3) && Strings.isNullOrEmpty(str7)) {
/* 259 */       str1 = str1 + "；" + SystemEnv.getHtmlLabelName(10005607, ThreadVarLanguage.getLang()) + "" + str3.substring(1);
/*     */     }
/* 261 */     if (!Strings.isNullOrEmpty(str3) && !Strings.isNullOrEmpty(str7)) {
/* 262 */       str1 = str1 + "；" + SystemEnv.getHtmlLabelName(10005608, ThreadVarLanguage.getLang()) + "" + str3.substring(1) + "" + SystemEnv.getHtmlLabelName(10005609, ThreadVarLanguage.getLang()) + "" + str7.substring(1);
/*     */     }
/*     */     
/* 265 */     if (Strings.isNullOrEmpty(str4) && Strings.isNullOrEmpty(str5)) {
/* 266 */       this.bizLogContext.setOperateType(BizLogOperateType.ADD);
/* 267 */     } else if (Strings.isNullOrEmpty(str8) && Strings.isNullOrEmpty(str9)) {
/* 268 */       this.bizLogContext.setOperateType(BizLogOperateType.DELETE);
/*     */     } else {
/* 270 */       this.bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/*     */     } 
/*     */     
/* 273 */     this.bizLogContext.setOldValues(hashMap1);
/* 274 */     this.bizLogContext.setNewValues(hashMap2);
/* 275 */     this.bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_COL_RULE);
/* 276 */     this.bizLogContext.setDesc(str1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getRowLogContext() {
/* 284 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     
/* 286 */     String str1 = "";
/* 287 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 288 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */ 
/*     */     
/* 292 */     String str2 = "";
/* 293 */     String str3 = "select rowcalstr from workflow_formdetailinfo where formid=" + i;
/* 294 */     RecordSet recordSet = new RecordSet();
/* 295 */     recordSet.execute(str3);
/* 296 */     if (recordSet.next()) {
/*     */       
/* 298 */       String str = recordSet.getString("rowcalstr");
/* 299 */       if (!Strings.isNullOrEmpty(str)) {
/* 300 */         String[] arrayOfString = str.split(";");
/* 301 */         for (String str7 : arrayOfString) {
/* 302 */           String str8 = getFieldTableNameByCalstr(str7);
/* 303 */           str2 = str2 + "、" + str8 + ":" + str7;
/*     */         } 
/*     */       } 
/* 306 */       hashMap1.put("rowcalstr", str);
/*     */     } 
/*     */     
/* 309 */     String str4 = "";
/* 310 */     String str5 = "";
/*     */     
/* 312 */     String str6 = Util.null2String(this.params.get("datas"));
/* 313 */     JSONArray jSONArray = JSONArray.parseArray(str6);
/* 314 */     for (byte b = 0; b < jSONArray.size(); b++) {
/*     */       
/* 316 */       JSONObject jSONObject = jSONArray.getJSONObject(b);
/*     */       
/* 318 */       String str7 = jSONObject.getString("tokenValue");
/* 319 */       String str8 = getFieldTableNameByCalstr(str7);
/*     */       
/* 321 */       String str9 = jSONObject.getString("tokenStr");
/*     */       
/* 323 */       str5 = str5 + ";" + str7;
/* 324 */       str4 = str4 + "、" + str8 + "" + SystemEnv.getHtmlLabelName(10005610, ThreadVarLanguage.getLang()) + "" + str9;
/*     */     } 
/*     */     
/* 327 */     hashMap2.put("rowcalstr", Strings.isNullOrEmpty(str5) ? str5 : str5.substring(1));
/*     */     
/* 329 */     if (!Strings.isNullOrEmpty(str2) && Strings.isNullOrEmpty(str4)) {
/* 330 */       this.bizLogContext.setOperateType(BizLogOperateType.DELETE);
/* 331 */       str1 = "" + SystemEnv.getHtmlLabelName(10005611, ThreadVarLanguage.getLang()) + "" + str2.substring(1);
/*     */     } 
/* 333 */     if (!Strings.isNullOrEmpty(str2) && !Strings.isNullOrEmpty(str4)) {
/* 334 */       this.bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/* 335 */       str1 = "" + SystemEnv.getHtmlLabelName(10005612, ThreadVarLanguage.getLang()) + "" + str2.substring(1) + "" + SystemEnv.getHtmlLabelName(10005613, ThreadVarLanguage.getLang()) + "" + str4.substring(1);
/*     */     } 
/* 337 */     if (Strings.isNullOrEmpty(str2) && !Strings.isNullOrEmpty(str4)) {
/* 338 */       this.bizLogContext.setOperateType(BizLogOperateType.ADD);
/* 339 */       str1 = "" + SystemEnv.getHtmlLabelName(10005614, ThreadVarLanguage.getLang()) + "" + str4.substring(1);
/*     */     } 
/*     */     
/* 342 */     this.bizLogContext.setOldValues(hashMap1);
/* 343 */     this.bizLogContext.setNewValues(hashMap2);
/* 344 */     this.bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_ROW_RULE);
/* 345 */     this.bizLogContext.setDesc(str1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getFieldLabelName(String paramString) {
/* 355 */     int i = Util.getIntValue(Util.null2String(this.params.get("formid")));
/* 356 */     String str = "";
/* 357 */     RecordSet recordSet = new RecordSet();
/* 358 */     recordSet.execute("select * from workflow_billfield where viewtype=0 and fieldhtmltype=1 and (type=2 or type=3 or type=4 or type=5) and billid=" + i + " and id=" + paramString);
/* 359 */     if (recordSet.next()) {
/* 360 */       str = SystemEnv.getHtmlLabelName(recordSet.getInt("fieldlabel"), this.user.getLanguage());
/*     */     }
/* 362 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String getFieldTableNameById(String paramString) {
/* 368 */     return getFieldTableName(paramString, true);
/*     */   }
/*     */   private String getFieldTableNameByCalstr(String paramString) {
/* 371 */     return getFieldTableName(paramString, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getFieldTableName(String paramString, boolean paramBoolean) {
/* 380 */     int i = Util.getIntValue(Util.null2String(this.params.get("formid")));
/*     */     
/* 382 */     if (this.fieldTableMap.isEmpty()) {
/*     */       
/* 384 */       RecordSet recordSet = new RecordSet();
/* 385 */       recordSet.execute("select tablename from workflow_billdetailtable where billid=" + i + " order by orderid");
/* 386 */       byte b = 0;
/* 387 */       while (recordSet.next()) {
/*     */         
/* 389 */         b++;
/* 390 */         String str1 = recordSet.getString("tablename");
/* 391 */         String str2 = SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b;
/*     */ 
/*     */         
/* 394 */         String str3 = "select * from workflow_billfield where viewtype=1 and fieldhtmltype=1 and (type=2 or type=3 or type=4 or type=5) and billid=" + i + "  and detailtable='" + str1 + "' order by dsporder";
/*     */ 
/*     */         
/* 397 */         RecordSet recordSet1 = new RecordSet();
/* 398 */         recordSet1.execute(str3);
/* 399 */         while (recordSet1.next()) {
/*     */           
/* 401 */           String str4 = recordSet1.getString("id");
/* 402 */           this.fieldTableMap.put(str4, str2);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 407 */     if (paramBoolean) {
/* 408 */       return Util.null2String(this.fieldTableMap.get(paramString));
/*     */     }
/* 410 */     String[] arrayOfString1 = paramString.split("=");
/* 411 */     String[] arrayOfString2 = arrayOfString1[0].split("_");
/* 412 */     String str = arrayOfString2[1];
/* 413 */     return Util.null2String(this.fieldTableMap.get(str));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/DoSaveCalCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */