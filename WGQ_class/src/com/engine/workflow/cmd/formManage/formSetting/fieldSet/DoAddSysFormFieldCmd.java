/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.FormFieldSetBiz;
/*    */ import com.engine.workflow.entity.FormFieldSetEntity;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoAddSysFormFieldCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoAddSysFormFieldCmd() {}
/*    */   
/*    */   public DoAddSysFormFieldCmd(Map<String, Object> paramMap, User paramUser) {
/* 26 */     this.params = paramMap;
/* 27 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 32 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 38 */     String str1 = Util.null2String(this.params.get("fieldLabel"));
/* 39 */     String str2 = Util.null2String(this.params.get("dspOrder"));
/* 40 */     String str3 = Util.null2String(this.params.get("fieldType"));
/* 41 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/* 42 */     String str4 = FormFieldSetBiz.getTableName(i);
/* 43 */     FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(i, 1, this.user);
/* 44 */     FormFieldSetEntity formFieldSetEntity = new FormFieldSetEntity();
/* 45 */     formFieldSetEntity.setFieldLabel(str1);
/* 46 */     formFieldSetEntity.setFieldName("field_" + Util.getRandom());
/* 47 */     formFieldSetEntity.setDspOrder(str2);
/* 48 */     formFieldSetEntity = formFieldSetBiz.resolveFieldType(formFieldSetEntity, str3);
/*    */     try {
/* 50 */       formFieldSetBiz.AddAndEditField(formFieldSetEntity, str4, false);
/* 51 */       formFieldSetBiz.submitOperate();
/* 52 */       hashMap.put("status", "success");
/* 53 */     } catch (Exception exception) {
/* 54 */       hashMap.put("status", "failed");
/* 55 */       formFieldSetBiz.rollBackOperate();
/* 56 */       exception.printStackTrace();
/*    */     } 
/* 58 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/DoAddSysFormFieldCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */