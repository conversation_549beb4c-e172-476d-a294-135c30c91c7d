/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SaveGroupInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public SaveGroupInfoCmd(User paramUser, Map<String, Object> paramMap) {
/*  23 */     this.user = paramUser;
/*  24 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  29 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  35 */     int i = Util.getIntValue(Util.null2String(this.params.get("groupid")));
/*  36 */     String str1 = Util.null2String(this.params.get("groupName"));
/*  37 */     int j = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/*  38 */     String str2 = Util.null2String(this.params.get("isBill"));
/*  39 */     String str3 = Util.null2String(this.params.get("actionType"));
/*  40 */     String str4 = Util.null2String(this.params.get("groupIcon"));
/*  41 */     RecordSet recordSet = new RecordSet();
/*  42 */     String str5 = "update workflow_formfield_group set groupname = ?,groupicon = ?,dsporder = ? where id  = ?";
/*  43 */     String str6 = "insert into workflow_formfield_group(groupname,groupicon,dsporder,formid,isbill) values (?,?,?,?,?)";
/*  44 */     if ("delete".equals(str3)) {
/*  45 */       if (i > -1) {
/*  46 */         if ("1".equals(str2)) {
/*  47 */           recordSet.executeUpdate("update workflow_billfield set fieldgroupid = 0 where billid = ? and fieldgroupid = ?", new Object[] { Integer.valueOf(j), Integer.valueOf(i) });
/*     */         } else {
/*  49 */           recordSet.executeUpdate("update workflow_formfield set fieldgroupid = 0 where formid = ? and fieldgroupid = ?", new Object[] { Integer.valueOf(j), Integer.valueOf(i) });
/*     */         } 
/*  51 */         recordSet.executeUpdate("update workflow_systemfield_group set fieldgroupid = 0 where formid = ? and isbill = ? and fieldgroupid = ? ", new Object[] { Integer.valueOf(j), str2, Integer.valueOf(i) });
/*  52 */         recordSet.executeUpdate("delete from workflow_formfield_group where id = ?", new Object[] { Integer.valueOf(i) });
/*  53 */         hashMap.put("success", Boolean.valueOf(true));
/*     */       } else {
/*  55 */         hashMap.put("success", Boolean.valueOf(false));
/*     */       } 
/*  57 */       boolean bool = deleteGroup(i, str2, j);
/*  58 */       hashMap.put("success", Boolean.valueOf(bool));
/*  59 */     } else if ("batchSave".equals(str3)) {
/*  60 */       int k = Util.getIntValue(Util.null2String(this.params.get("rowCount")));
/*  61 */       String str = Util.null2String(this.params.get("delKeys"));
/*  62 */       ArrayList<ArrayList<String>> arrayList1 = new ArrayList();
/*  63 */       ArrayList<ArrayList<String>> arrayList2 = new ArrayList();
/*     */       
/*  65 */       for (byte b = 0; b < k; b++) {
/*  66 */         int m = Util.getIntValue(Util.null2String(this.params.get("fieldgroupid_" + b)));
/*  67 */         String str7 = Util.null2String(this.params.get("groupname_" + b));
/*  68 */         String str8 = Util.null2String(this.params.get("groupicon_" + b));
/*  69 */         ArrayList<String> arrayList = new ArrayList();
/*  70 */         arrayList.add(str7);
/*  71 */         arrayList.add(str8);
/*  72 */         arrayList.add(Integer.valueOf(b));
/*  73 */         if (m > 0) {
/*  74 */           arrayList.add(Integer.valueOf(m));
/*  75 */           arrayList1.add(arrayList);
/*     */         } else {
/*  77 */           arrayList.add(Integer.valueOf(j));
/*  78 */           arrayList.add(str2);
/*  79 */           arrayList2.add(arrayList);
/*     */         } 
/*     */       } 
/*  82 */       if (arrayList1.size() > 0) {
/*  83 */         recordSet.executeBatchSql(str5, arrayList1);
/*     */       }
/*  85 */       if (arrayList2.size() > 0) {
/*  86 */         recordSet.executeBatchSql(str6, arrayList2);
/*     */       }
/*     */       
/*  89 */       if (!"".equals(str)) {
/*  90 */         String[] arrayOfString = Util.splitString(str, ",");
/*  91 */         for (String str7 : arrayOfString) {
/*  92 */           int m = Util.getIntValue(str7);
/*  93 */           if (m > 0) {
/*  94 */             deleteGroup(m, str2, j);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } else {
/*  99 */       if (i > -1) {
/* 100 */         recordSet.executeUpdate("update workflow_formfield_group set groupname = ?,groupicon = ? where id  = ?", new Object[] { str1, str4, Integer.valueOf(i) });
/*     */       } else {
/* 102 */         recordSet.executeQuery("select max(dsporder) from workflow_formfield_group where formid = ? and isbill = ?", new Object[] { Integer.valueOf(j), str2 });
/* 103 */         int k = 0;
/* 104 */         if (recordSet.next()) {
/* 105 */           k = recordSet.getInt(1) + 1;
/*     */         }
/* 107 */         recordSet.executeUpdate(str6, new Object[] { str1, str4, Integer.valueOf(k), Integer.valueOf(j), str2 });
/* 108 */         recordSet.executeQuery("select max(id) from workflow_formfield_group where formid = ? and isbill = ?", new Object[] { Integer.valueOf(j), str2 });
/* 109 */         if (recordSet.next()) {
/* 110 */           i = recordSet.getInt(1);
/*     */         }
/* 112 */         hashMap.put("newGroupid", Integer.valueOf(i));
/*     */       } 
/* 114 */       hashMap.put("success", Boolean.valueOf(true));
/*     */     } 
/* 116 */     hashMap.put("groupTabs", FieldGroupBiz.getFieldGroupTabs(this.user, j, str2));
/* 117 */     List list = FieldGroupBiz.getFormGroups(j, str2, false);
/* 118 */     hashMap.put("datas", list);
/* 119 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private boolean deleteGroup(int paramInt1, String paramString, int paramInt2) {
/* 123 */     RecordSet recordSet = new RecordSet();
/* 124 */     if (paramInt1 > -1) {
/* 125 */       if ("1".equals(paramString)) {
/* 126 */         recordSet.executeUpdate("update workflow_billfield set fieldgroupid = '0' where billid = ? and fieldgroupid = ?", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt1) });
/*     */       } else {
/* 128 */         recordSet.executeUpdate("update workflow_formfield set fieldgroupid = '0' where formid = ? and fieldgroupid = ?", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt1) });
/*     */       } 
/* 130 */       return recordSet.executeUpdate("delete from workflow_formfield_group where id = ?", new Object[] { Integer.valueOf(paramInt1) });
/*     */     } 
/* 132 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/SaveGroupInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */