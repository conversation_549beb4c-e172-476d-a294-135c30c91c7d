/*     */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.util.FormDeleteUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.form.FormComInfo;
/*     */ import weaver.workflow.form.FormMainManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoDeleteFormCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  34 */   protected List<BizLogContext> logContexts = new ArrayList<>();
/*  35 */   protected List<Map<String, Object>> formValuesList = new ArrayList<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DoDeleteFormCmd(Map<String, Object> paramMap, User paramUser) {
/*  41 */     this.params = paramMap;
/*  42 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  47 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  53 */     Date date = new Date();
/*  54 */     for (Map<String, Object> map : this.formValuesList) {
/*  55 */       String str = Util.null2String(map.get("formname"));
/*  56 */       if ("".equals(str)) {
/*  57 */         str = SystemEnv.getHtmlLabelName(Util.getIntValue(Util.null2String(map.get("namelabel"))), this.user.getLanguage());
/*     */       }
/*  59 */       BizLogContext bizLogContext = new BizLogContext();
/*  60 */       bizLogContext.setDateObject(date);
/*  61 */       bizLogContext.setUserid(this.user.getUID());
/*  62 */       bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  63 */       bizLogContext.setTargetId(Util.null2String(map.get("id")));
/*  64 */       bizLogContext.setTargetName(str);
/*  65 */       bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  66 */       bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*     */ 
/*     */ 
/*     */       
/*  70 */       bizLogContext.setOperateType(BizLogOperateType.DELETE);
/*  71 */       bizLogContext.setOldValues(map);
/*  72 */       bizLogContext.setParams(this.params);
/*  73 */       bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*  74 */       bizLogContext.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(19532, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005598, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10005615, ThreadVarLanguage.getLang()) + "" + str + "(" + map.get("id") + ")", new Object[0]));
/*  75 */       this.logContexts.add(bizLogContext);
/*     */     } 
/*  77 */     return this.logContexts;
/*     */   }
/*     */   
/*     */   protected void getOldValues(String paramString, int paramInt) {
/*  81 */     RecordSet recordSet = new RecordSet();
/*  82 */     if (paramString.endsWith(",")) {
/*  83 */       paramString = paramString.substring(0, paramString.length() - 1);
/*     */     }
/*  85 */     if (paramString.startsWith(",")) {
/*  86 */       paramString = paramString.substring(1, paramString.length());
/*     */     }
/*  88 */     String str = "";
/*  89 */     if (paramInt == 1) {
/*  90 */       str = " SELECT * FROM WORKFLOW_BILL WHERE ID IN (" + paramString + ") ";
/*     */     } else {
/*  92 */       str = " SELECT * FROM WORKFLOW_FORMBASE WHERE ID IN (" + paramString + ") ";
/*     */     } 
/*  94 */     recordSet.executeQuery(str, new Object[0]);
/*  95 */     while (recordSet.next()) {
/*  96 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  97 */       String[] arrayOfString = recordSet.getColumnName();
/*  98 */       for (String str1 : arrayOfString) {
/*  99 */         hashMap.put(str1.toLowerCase(), recordSet.getString(str1));
/*     */       }
/* 101 */       this.formValuesList.add(hashMap);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 107 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 109 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/* 110 */     if (bool) {
/*     */       try {
/* 112 */         deleteForm();
/* 113 */         hashMap.put("status", "success");
/* 114 */       } catch (Exception exception) {
/* 115 */         exception.printStackTrace();
/* 116 */         hashMap.put("status", "failed");
/*     */       } 
/*     */     } else {
/* 119 */       hashMap.put("noRight", Boolean.valueOf(true));
/*     */     } 
/*     */     
/* 122 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   protected void deleteForm() throws Exception {
/* 126 */     String[] arrayOfString1 = null;
/* 127 */     String[] arrayOfString2 = null;
/* 128 */     String str1 = Util.null2String(this.params.get("oldformids"));
/* 129 */     String str2 = Util.null2String(this.params.get("newformids"));
/*     */     
/* 131 */     if (!"".equals(str1)) {
/* 132 */       getOldValues(str1, 0);
/*     */     }
/* 134 */     if (!"".equals(str2)) {
/* 135 */       getOldValues(str2, 1);
/*     */     }
/*     */ 
/*     */     
/* 139 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 141 */     if (!"".equals(str1) && arrayOfString1 == null) {
/* 142 */       arrayOfString1 = Util.TokenizerString2(str1, ",");
/*     */     }
/* 144 */     if (!"".equals(str2) && arrayOfString2 == null) {
/* 145 */       arrayOfString2 = Util.TokenizerString2(str2, ",");
/*     */     }
/*     */     
/* 148 */     if (arrayOfString2 != null) {
/*     */       
/* 150 */       FormDeleteUtil formDeleteUtil = new FormDeleteUtil();
/*     */       
/* 152 */       for (byte b = 0; b < arrayOfString2.length; b++) {
/* 153 */         int i = Util.getIntValue(arrayOfString2[b], 0);
/*     */ 
/*     */         
/* 156 */         if (formDeleteUtil.checkFormCanDelete(i, 1)) {
/*     */ 
/*     */ 
/*     */           
/* 160 */           recordSet.executeQuery("select isvirtualform from ModeFormExtend where formid=" + i, new Object[0]);
/* 161 */           int j = 0;
/* 162 */           if (recordSet.next()) {
/* 163 */             j = recordSet.getInt("isvirtualform");
/*     */           }
/* 165 */           if (j != 1) {
/* 166 */             recordSet.executeQuery("select namelabel, tablename from workflow_bill where id=" + i, new Object[0]);
/* 167 */             if (recordSet.next()) {
/* 168 */               String str = recordSet.getString("tablename");
/*     */               
/* 170 */               formDeleteUtil.doDeleteFormTable(i, str);
/* 171 */               if (recordSet.getDBType().equals("oracle")) {
/* 172 */                 dropTriggerAndSeqForOracle(str);
/*     */               }
/*     */             } 
/*     */           } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 186 */           recordSet.executeSql("delete from workflow_bill where id=" + i);
/* 187 */           recordSet.executeQuery("select id from workflow_billfield where fieldhtmltype=5 and billid=" + i, new Object[0]);
/* 188 */           while (recordSet.next()) {
/* 189 */             int k = recordSet.getInt("id");
/* 190 */             recordSet.executeSql("delete from workflow_SelectItem where fieldid=" + k);
/*     */           } 
/* 192 */           recordSet.executeSql("delete from workflow_billfield where billid=" + i);
/*     */           
/* 194 */           recordSet.executeQuery("select id,tablename from Workflow_billdetailtable where billid=" + i, new Object[0]);
/* 195 */           while (recordSet.next()) {
/* 196 */             if (j != 1) {
/* 197 */               String str = recordSet.getString("tablename");
/*     */               
/* 199 */               formDeleteUtil.doDeleteFormTable(i, str);
/*     */             } 
/* 201 */             recordSet.executeSql("delete from Workflow_billdetailtable where id=" + recordSet.getInt("id"));
/*     */           } 
/*     */           
/* 204 */           recordSet.executeSql("delete from workflow_formdetailinfo where formid=" + i);
/* 205 */           recordSet.executeSql("delete from workflow_nodehtmllayout where formid=" + i + " and isbill=1");
/* 206 */           recordSet.executeSql("delete from workflow_nodeform_form where formid=" + i + " and isbill=1");
/* 207 */           recordSet.executeSql("delete from workflow_nodeformgroup_form where formid=" + i + " and isbill=1");
/* 208 */           recordSet.executeSql("delete from ModeFormExtend where formid=" + i);
/* 209 */           recordSet.executeSql("delete from AppFormInfo where formid=" + i);
/* 210 */           recordSet.executeSql("delete from workflow_formfield_group where formid = " + i + " and isbill = '1' ");
/* 211 */           recordSet.executeSql("delete from workflow_systemfield_group where formid = " + i + " and isbill = '1' ");
/*     */         } 
/*     */       } 
/*     */     } 
/* 215 */     if (arrayOfString1 != null) {
/* 216 */       (new FormMainManager()).DeleteForm(arrayOfString1);
/* 217 */       (new FormComInfo()).removeFormCache();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static void dropTriggerAndSeqForOracle(String paramString) {
/* 223 */     RecordSet recordSet = new RecordSet();
/* 224 */     recordSet.executeQuery("select trigger_name from user_triggers where upper(trigger_name) like upper('" + paramString + "')||'%'", new Object[0]);
/* 225 */     if (recordSet.next()) {
/* 226 */       String str = Util.null2String(recordSet.getString("trigger_name"));
/* 227 */       if ("jc".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/* 228 */         recordSet.executeUpdate("drop trigger " + str + " on " + paramString, new Object[0]);
/*     */       } else {
/*     */         
/* 231 */         recordSet.executeUpdate("drop trigger " + str, new Object[0]);
/*     */       } 
/*     */     } 
/*     */     
/* 235 */     recordSet.executeQuery("select sequence_name from user_sequences where upper(sequence_name) like upper('" + paramString + "')||'%'", new Object[0]);
/* 236 */     if (recordSet.next()) {
/* 237 */       String str = Util.null2String(recordSet.getString("sequence_name"));
/* 238 */       recordSet.executeUpdate("drop sequence " + str, new Object[0]);
/*     */     } 
/*     */ 
/*     */     
/* 242 */     recordSet.executeQuery("select * from user_indexes where upper(table_name)=upper('" + paramString + "')", new Object[0]);
/* 243 */     if (recordSet.next()) {
/* 244 */       String str = Util.null2String(recordSet.getString("index_name"));
/* 245 */       recordSet.executeUpdate("drop index " + str, new Object[0]);
/*     */     } 
/*     */   }
/*     */   
/*     */   public DoDeleteFormCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/DoDeleteFormCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */