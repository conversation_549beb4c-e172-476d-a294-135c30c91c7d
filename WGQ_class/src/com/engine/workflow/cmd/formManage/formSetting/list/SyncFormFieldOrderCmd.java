/*    */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SyncFormFieldOrderCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public SyncFormFieldOrderCmd(User paramUser, Map<String, Object> paramMap) {
/* 25 */     this.user = paramUser;
/* 26 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 34 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/* 35 */     if (bool) {
/* 36 */       int i = Util.getIntValue(Util.null2String(this.params.get("formid")));
/* 37 */       int j = Util.getIntValue(Util.null2String(this.params.get("isbill")));
/* 38 */       if (j == 1) {
/* 39 */         List<List> list = getBillFormFieldUpdateParams(i);
/* 40 */         if (list.size() > 0) {
/* 41 */           RecordSet recordSet = new RecordSet();
/* 42 */           String str = "UPDATE workflow_nodeform SET orderid  = ? WHERE fieldid = ? AND nodeid IN (SELECT nodeid FROM workflow_flownode t1 LEFT JOIN workflow_base t2 ON t1.workflowid = t2.id WHERE t2.isbill = '1' AND t2.formid = ?)";
/* 43 */           boolean bool1 = recordSet.executeBatchSql(str, list);
/* 44 */           hashMap.put("success", Boolean.valueOf(bool1));
/*    */         } 
/*    */       } 
/*    */     } else {
/* 48 */       hashMap.put("noRight", Boolean.valueOf(true));
/*    */     } 
/* 50 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   private List<List> getBillFormFieldUpdateParams(int paramInt) {
/* 54 */     RecordSet recordSet = new RecordSet();
/* 55 */     recordSet.executeQuery("select id,dsporder from workflow_billfield where billid = ?", new Object[] { Integer.valueOf(paramInt) });
/* 56 */     ArrayList<ArrayList<Double>> arrayList = new ArrayList();
/* 57 */     while (recordSet.next()) {
/* 58 */       ArrayList<Double> arrayList1 = new ArrayList();
/* 59 */       arrayList1.add(Double.valueOf(recordSet.getDouble("dsporder")));
/* 60 */       arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/* 61 */       arrayList1.add(Integer.valueOf(paramInt));
/* 62 */       arrayList.add(arrayList1);
/*    */     } 
/*    */     
/* 65 */     int[][] arrayOfInt = { { -1, -1 }, { -2, -2 }, { -3, 0 }, { -4, -1 }, { -5, 0 }, { -9, -1 } };
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 74 */     for (int[] arrayOfInt1 : arrayOfInt) {
/* 75 */       ArrayList<Integer> arrayList1 = new ArrayList();
/* 76 */       arrayList1.add(Integer.valueOf(arrayOfInt1[1]));
/* 77 */       arrayList1.add(Integer.valueOf(arrayOfInt1[0]));
/* 78 */       arrayList1.add(Integer.valueOf(paramInt));
/* 79 */       arrayList.add(arrayList1);
/*    */     } 
/* 81 */     return (List)arrayList;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 86 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/SyncFormFieldOrderCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */