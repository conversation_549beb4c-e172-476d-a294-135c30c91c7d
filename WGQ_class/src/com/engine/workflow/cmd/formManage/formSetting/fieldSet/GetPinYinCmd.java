/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetPinYinCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetPinYinCmd() {}
/*    */   
/*    */   public GetPinYinCmd(Map<String, Object> paramMap, User paramUser) {
/* 25 */     this.params = paramMap;
/* 26 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 31 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 37 */     RecordSet recordSet = new RecordSet();
/* 38 */     String str1 = Util.null2String(this.params.get("labelName")).trim();
/* 39 */     String str2 = "select dbo.getPinYin('" + str1 + "')";
/* 40 */     String str3 = recordSet.getDBType();
/* 41 */     if ("oracle".equals(str3)) {
/* 42 */       str2 = "select getPinYin('" + str1 + "') from dual";
/* 43 */     } else if ("mysql".equals(str3)) {
/* 44 */       str2 = "select getPinYin('" + str1 + "')";
/* 45 */     } else if ("postgresql".equals(str3)) {
/* 46 */       str2 = "select getPinYin('" + str1 + "')";
/*    */     } 
/* 48 */     recordSet.executeQuery(str2, new Object[0]);
/* 49 */     String str4 = "";
/* 50 */     if (recordSet.next()) {
/* 51 */       str4 = Util.null2String(recordSet.getString(1));
/* 52 */       hashMap.put("pinyin111", str4);
/*    */     } 
/* 54 */     if (!"".equals(str4)) {
/* 55 */       str4 = str4.toLowerCase();
/*    */     }
/*    */     
/* 58 */     hashMap.put("pinyin222", str4);
/*    */ 
/*    */     
/* 61 */     String str5 = ",PERCENT,PLAN,PRECISION,PRIMARY,PRINT,PROC,PROCEDURE,PUBLIC,RAISERROR,READ,READTEXT,RECONFIGURE,REFERENCES,REPLICATION,RESTORE,RESTRICT,RETURN,REVOKE,RIGHT,ROLLBACK,ROWCOUNT,ROWGUIDCOL,RULE,SAVE,SCHEMA,SELECT,SESSION_USER,SET,SETUSER,SHUTDOWN,SOME,STATISTICS,SYSTEM_USER,TABLE,TEXTSIZE,THEN,TO,TOP,TRAN,TRANSACTION,TRIGGER,TRUNCATE,TSEQUAL,UNION,UNIQUE,UPDATE,UPDATETEXT,USE,USER,VALUES,VARYING,VIEW,WAITFOR,WHEN,WHERE,WHILE,WITH,WRITETEXT,EXCEPT,EXEC,EXECUTE,EXISTS,EXIT,FETCH,FILE,FILLFACTOR,FOR,FOREIGN,FREETEXT,FREETEXTTABLE,FROM,FULL,FUNCTION,GOTO,GRANT,GROUP,HAVING,HOLDLOCK,IDENTITY,IDENTITY_INSERT,IDENTITYCOL,IF,IN,INDEX,INNER,INSERT,INTERSECT,INTO,IS,JOIN,KEY,KILL,LEFT,LIKE,LINENO,LOAD,NATIONAL,NOCHECK,NONCLUSTERED,NOT,NULL,NULLIF,OF,OFF,OFFSETS,ON,OPEN,OPENDATASOURCE,OPENQUERY,OPENROWSET,OPENXML,OPTION,OR,ORDER,OUTER,OVER,ADD,ALL,ALTER,AND,ANY,AS,ASC,AUTHORIZATION,BACKUP,BEGIN,BETWEEN,BREAK,BROWSE,BULK,BY,CASCADE,CASE,CHECK,CHECKPOINT,CLOSE,CLUSTERED,COALESCE,COLLATE,COLUMN,COMMIT,COMPUTE,CONSTRAINT,CONTAINS,CONTAINSTABLE,CONTINUE,CONVERT,CREATE,CROSS,CURRENT,CURRENT_DATE,CURRENT_TIME,CURRENT_TIMESTAMP,CURRENT_USER,CURSOR,DATABASE,DBCC,DEALLOCATE,DECLARE,DEFAULT,DELETE,DENY,DESC,DISK,DISTINCT,DISTRIBUTED,DOUBLE,DROP,DUMMY,DUMP,ELSE,END,ERRLVL,ESCAPE,";
/* 62 */     str5 = str5 + "ACCESS,ADD,ALL,ALTER,AND,ANY,AS,ASC,AUDIT,BETWEEN,BY,CHAR,";
/* 63 */     str5 = str5 + "CHECK,CLUSTER,COLUMN,COMMENT,COMPRESS,CONNECT,CREATE,CURRENT,";
/* 64 */     str5 = str5 + "DATE,DECIMAL,DEFAULT,DELETE,DESC,DISTINCT,DROP,ELSE,EXCLUSIVE,";
/* 65 */     str5 = str5 + "EXISTS,FILE,FLOAT,FOR,FROM,GRANT,GROUP,HAVING,IDENTIFIED,";
/* 66 */     str5 = str5 + "IMMEDIATE,IN,INCREMENT,INDEX,INITIAL,INSERT,INTEGER,INTERSECT,";
/* 67 */     str5 = str5 + "INTO,IS,LEVEL,LIKE,LOCK,LONG,MAXEXTENTS,MINUS,MLSLABEL,MODE,";
/* 68 */     str5 = str5 + "MODIFY,NOAUDIT,NOCOMPRESS,NOT,NOWAIT,NULL,NUMBER,OF,OFFLINE,ON,";
/* 69 */     str5 = str5 + "ONLINE,OPTION,OR,ORDER,PCTFREE,PRIOR,PRIVILEGES,PUBLIC,RAW,";
/* 70 */     str5 = str5 + "RENAME,RESOURCE,REVOKE,ROW,ROWID,ROWNUM,ROWS,SELECT,SESSION,";
/* 71 */     str5 = str5 + "SET,SHARE,SIZE,SMALLINT,START,SUCCESSFUL,SYNONYM,SYSDATE,TABLE,";
/* 72 */     str5 = str5 + "THEN,TO,TRIGGER,UID,UNION,UNIQUE,UPDATE,USER,VALIDATE,VALUES,";
/* 73 */     str5 = str5 + "VARCHAR,VARCHAR2,VIEW,WHENEVER,WHERE,WITH,";
/* 74 */     str4 = str4.replaceAll("[^0-9a-zA-Z]+", "");
/* 75 */     str4 = str4.replaceAll("^\\d+", "");
/* 76 */     if (str4.length() > 30) {
/* 77 */       str4 = str4.substring(0, 30);
/*    */     }
/* 79 */     String str6 = "";
/* 80 */     String str7 = "," + str4.toUpperCase() + ",";
/* 81 */     if (str5.indexOf(str7) > 0) {
/* 82 */       str6 = str4 + SystemEnv.getHtmlLabelName(126999, this.user.getLanguage());
/* 83 */       str4 = str4 + "1";
/* 84 */       hashMap.put("pinyin333", str4);
/*    */     } 
/* 86 */     hashMap.put("pinyin", str4);
/* 87 */     hashMap.put("msg", str6);
/* 88 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetPinYinCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */