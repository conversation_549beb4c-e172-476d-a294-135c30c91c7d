/*     */ package com.engine.workflow.cmd.formManage.formSetting.baseSet;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.biz.SimpleBizLogger;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.UserWFOperateLevel;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetBillRightSetCondition
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  34 */   protected SimpleBizLogger logger = new SimpleBizLogger();
/*     */ 
/*     */ 
/*     */   
/*     */   public GetBillRightSetCondition(Map<String, Object> paramMap, User paramUser) {
/*  39 */     this.params = paramMap;
/*  40 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  46 */     return this.logger.getBizLogContext();
/*     */   }
/*     */   
/*     */   protected void logBefore() {
/*  50 */     String str1 = Util.null2String(this.params.get("formId"));
/*  51 */     String str2 = " SELECT NAMELABEL FROM workflow_bill WHERE ID = ? ";
/*  52 */     RecordSet recordSet = new RecordSet();
/*  53 */     recordSet.executeQuery(str2, new Object[] { str1 });
/*  54 */     String str3 = "";
/*  55 */     if (recordSet.next()) {
/*  56 */       str3 = SystemEnv.getHtmlLabelName(Util.getIntValue(recordSet.getString("NAMELABEL")), this.user.getLanguage());
/*     */     }
/*  58 */     String str4 = "select * from workflow_bill where id = " + str1;
/*     */     
/*  60 */     BizLogContext bizLogContext = new BizLogContext();
/*  61 */     bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  62 */     bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  63 */     bizLogContext.setBelongTypeTargetId("");
/*  64 */     bizLogContext.setBelongTypeTargetName("");
/*  65 */     bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  66 */     bizLogContext.setTargetName(str3);
/*  67 */     bizLogContext.setTargetId(str1 + "");
/*  68 */     bizLogContext.setParams(this.params);
/*  69 */     this.logger.setUser(this.user);
/*  70 */     this.logger.setMainSql(str4, "id");
/*     */     
/*  72 */     this.logger.before(bizLogContext);
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  77 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  78 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  79 */     String str1 = Util.null2String(this.params.get("formId"));
/*  80 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  81 */     String str2 = Util.null2String(this.params.get("options"));
/*  82 */     RecordSet recordSet = new RecordSet();
/*  83 */     if ("submit".equals(str2)) {
/*  84 */       logBefore();
/*  85 */       String str = Util.null2String(this.params.get("subCompanyId"));
/*  86 */       recordSet.executeUpdate("update workflow_bill set subcompanyid=? where id=?", new Object[] { str, str1 });
/*  87 */       hashMap1.put("status", "success");
/*  88 */       return (Map)hashMap1;
/*     */     } 
/*     */ 
/*     */     
/*  92 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  93 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  95 */     int i = Util.getIntValue(Util.null2String(this.params.get("isBill")), -1);
/*  96 */     boolean bool = (new ManageDetachComInfo()).isUseFmManageDetach() ? true : false;
/*  97 */     int j = UserWFOperateLevel.checkWfFormOperateLevel(bool, this.user, "FormManage:All", Util.getIntValue(str1, 0), i);
/*     */     
/*  99 */     recordSet.executeQuery("select subcompanyid from workflow_bill where id= ?", new Object[] { str1 });
/* 100 */     recordSet.next();
/* 101 */     int k = Util.getIntValue(recordSet.getString("subcompanyid"), 0);
/*     */     
/* 103 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subCompanyId", "169");
/* 104 */     BrowserBean browserBean = searchConditionItem.getBrowserConditionParam();
/* 105 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 106 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 107 */     hashMap3.put("id", Integer.valueOf(k));
/* 108 */     hashMap3.put("name", (new SubCompanyComInfo()).getSubCompanyname(k + ""));
/* 109 */     arrayList2.add(hashMap3);
/* 110 */     browserBean.setReplaceDatas(arrayList2);
/* 111 */     browserBean.setHasBorder(true);
/* 112 */     searchConditionItem.setLabelcol(6);
/* 113 */     searchConditionItem.setFieldcol(10);
/* 114 */     searchConditionItem.setRules("required");
/* 115 */     browserBean.getDataParams().put("rightStr", "FormManage:All");
/* 116 */     browserBean.getCompleteParams().put("rightStr", "FormManage:All");
/* 117 */     browserBean.getConditionDataParams().put("rightStr", "FormManage:All");
/* 118 */     browserBean.setTitle(SystemEnv.getHtmlLabelName(141, this.user.getLanguage()));
/* 119 */     searchConditionItem.setViewAttr((j > 0) ? 3 : 1);
/* 120 */     arrayList.add(searchConditionItem);
/*     */     
/* 122 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 123 */     hashMap2.put("items", arrayList);
/* 124 */     arrayList1.add(hashMap2);
/*     */     
/* 126 */     hashMap1.put("conditioninfo", arrayList1);
/* 127 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public GetBillRightSetCondition() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/baseSet/GetBillRightSetCondition.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */