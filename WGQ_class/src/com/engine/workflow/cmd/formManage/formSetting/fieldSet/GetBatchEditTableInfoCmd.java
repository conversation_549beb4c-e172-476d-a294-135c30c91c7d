/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.FormFieldSetBiz;
/*     */ import com.engine.workflow.biz.SelectFieldItemBiz;
/*     */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import java.sql.ResultSetMetaData;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.FormModeConfig;
/*     */ import weaver.general.FormFieldTransMethod;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ import weaver.workflow.selectItem.SelectItemManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetBatchEditTableInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  35 */   private int formid = 0;
/*  36 */   private int isFromMode = 0;
/*  37 */   private String tablename = "";
/*     */   private ResultSetMetaData tablename_metaData;
/*     */   private boolean canChange = false;
/*     */   private boolean isFormmodeUse = false;
/*     */   private boolean isFieldNoValueCanEdit = false;
/*  42 */   private SelectItemManager selectItemManager = new SelectItemManager();
/*  43 */   private FormFieldTransMethod formFieldTransMethod = new FormFieldTransMethod();
/*  44 */   private BrowserComInfo browserComInfo = new BrowserComInfo();
/*  45 */   private SelectFieldItemBiz selectFieldItemBiz = new SelectFieldItemBiz();
/*     */ 
/*     */ 
/*     */   
/*     */   public GetBatchEditTableInfoCmd(Map<String, Object> paramMap, User paramUser) {
/*  50 */     this.params = paramMap;
/*  51 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  56 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  61 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  62 */     initDatas();
/*  63 */     hashMap.put("columns", getColumns());
/*     */     
/*  65 */     String str = Util.null2String(this.params.get("isAdd"));
/*     */     
/*  67 */     if (!"1".equals(str)) {
/*  68 */       ArrayList<List<Map<String, Object>>> arrayList = new ArrayList();
/*  69 */       arrayList.add(getMainTableDatas());
/*  70 */       arrayList.addAll(getDetailTableDatas());
/*  71 */       hashMap.put("datas", arrayList);
/*     */     } 
/*  73 */     hashMap.put("existFieldName", getAllExistDBName(Util.null2String(this.params.get("formId"))));
/*  74 */     hashMap.put("requestMark", this.params.get("requestMark"));
/*  75 */     hashMap.put("isMultilang", Boolean.valueOf(Util.isEnableMultiLang()));
/*  76 */     RecordSet recordSet = new RecordSet();
/*  77 */     boolean bool = (!recordSet.getDBType().toLowerCase().equals("sqlserver") && !recordSet.getDBType().toLowerCase().equals("mysql")) ? true : false;
/*     */     
/*  79 */     hashMap.put("isOracle", Boolean.valueOf(bool));
/*  80 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<WeaTableEditEntity> getColumns() {
/*  88 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */     
/*  90 */     WeaTableEditEntity weaTableEditEntity1 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "fieldLabelName", "20%", "1");
/*  91 */     weaTableEditEntity1.setUseRecord(true);
/*  92 */     weaTableEditEntity1.setClassName("wea-table-edit-fieldLabelName");
/*  93 */     arrayList.add(weaTableEditEntity1);
/*     */ 
/*     */     
/*  96 */     WeaTableEditEntity weaTableEditEntity2 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldName", "15%", "1");
/*  97 */     weaTableEditEntity2.setUseRecord(true);
/*  98 */     weaTableEditEntity2.setClassName("wea-table-edit-fieldName");
/*  99 */     arrayList.add(weaTableEditEntity2);
/*     */ 
/*     */     
/* 102 */     WeaTableEditEntity weaTableEditEntity3 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "fieldType", "55%", "1");
/* 103 */     weaTableEditEntity3.setUseRecord(true);
/* 104 */     weaTableEditEntity3.setClassName("wea-table-edit-fieldType");
/* 105 */     arrayList.add(weaTableEditEntity3);
/*     */ 
/*     */     
/* 108 */     WeaTableEditEntity weaTableEditEntity4 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(517198, this.user.getLanguage()), "fieldGroup", "10%", "1");
/* 109 */     weaTableEditEntity4.setUseRecord(true);
/* 110 */     weaTableEditEntity4.setClassName("wea-table-edit-fieldgroup");
/* 111 */     arrayList.add(weaTableEditEntity4);
/*     */     
/* 113 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void initDatas() {
/* 118 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 120 */     this.formid = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 121 */     this.isFromMode = Util.getIntValue(Util.null2String(this.params.get("isFromMode")));
/*     */     
/* 123 */     recordSet.executeQuery("select tablename from workflow_bill where id=" + this.formid, new Object[0]);
/* 124 */     if (recordSet.next()) {
/* 125 */       this.tablename = Util.null2String(recordSet.getString("tablename"));
/*     */     }
/*     */     
/* 128 */     recordSet.executeQuery("select 1 from workflow_base where formid=" + this.formid, new Object[0]);
/* 129 */     if (recordSet.getCounts() <= 0) {
/* 130 */       this.canChange = true;
/*     */     }
/*     */     
/* 133 */     recordSet.executeQuery("select 1 from modeinfo where formid=" + this.formid, new Object[0]);
/* 134 */     if (recordSet.getCounts() > 0) {
/* 135 */       this.isFormmodeUse = true;
/* 136 */       this.canChange = false;
/*     */     } 
/*     */     
/* 139 */     if (this.isFromMode == 1) {
/* 140 */       FormModeConfig formModeConfig = new FormModeConfig();
/* 141 */       this.isFieldNoValueCanEdit = formModeConfig.isFieldNoValueCanEdit();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<Map<String, Object>> getMainTableDatas() {
/* 150 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 152 */     RecordSet recordSet1 = new RecordSet();
/* 153 */     RecordSet recordSet2 = new RecordSet();
/* 154 */     String str1 = "where billid=" + this.formid + " and viewtype=0 ";
/* 155 */     if (this.isFromMode == 1)
/*     */     {
/* 157 */       str1 = str1 + " and fieldhtmltype <> '9' ";
/*     */     }
/* 159 */     String str2 = "select t.*,(select 1 from workflow_billfield chit where chit.id = t.childfieldid and chit.fieldhtmltype = 5 and chit.selectitemtype = 2) as existschild from workflow_billfield t " + str1 + " order by dsporder,id";
/* 160 */     recordSet1.executeQuery(str2, new Object[0]);
/*     */     
/* 162 */     String str3 = "";
/* 163 */     int i = 0;
/* 164 */     String str4 = "";
/* 165 */     String str5 = "";
/* 166 */     String str6 = "";
/*     */ 
/*     */ 
/*     */     
/* 170 */     byte b = 0;
/*     */     
/* 172 */     String str7 = Util.null2String(this.params.get("isBill"));
/* 173 */     List<SearchConditionOption> list = FieldGroupBiz.getFieldGroups(this.formid, str7);
/*     */     
/* 175 */     while (recordSet1.next()) {
/* 176 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 177 */       RecordSet recordSet = new RecordSet();
/* 178 */       String str8 = recordSet1.getString("id");
/* 179 */       str3 = recordSet1.getString("fieldname");
/* 180 */       i = recordSet1.getInt("fieldlabel");
/* 181 */       String str9 = LabelUtil.getMultiLangLabel(i + "");
/* 182 */       str4 = recordSet1.getString("fielddbtype");
/* 183 */       str5 = recordSet1.getString("fieldhtmltype");
/* 184 */       str6 = recordSet1.getString("type");
/*     */ 
/*     */       
/* 187 */       int j = Util.getIntValue(recordSet1.getString("childfieldid"));
/*     */       
/* 189 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 190 */       if (j > 0) {
/*     */         
/* 192 */         recordSet.execute("select * from workflow_SelectItem where isbill=1 and fieldid=" + j);
/* 193 */         while (recordSet.next()) {
/* 194 */           int k = Util.getIntValue(recordSet.getString("selectvalue"), -1);
/* 195 */           String str = Util.null2String(recordSet.getString("selectname"));
/* 196 */           hashtable.put("item_" + k, str);
/*     */         } 
/*     */       } 
/* 199 */       String str10 = str3 + "+0+" + str5 + "+ +" + this.formid + "+" + str6;
/* 200 */       String str11 = this.formFieldTransMethod.getCanCheckBox(str10);
/* 201 */       boolean bool = false;
/* 202 */       if (this.isFromMode != 1) {
/* 203 */         bool = !this.canChange ? true : false;
/*     */       } else {
/* 205 */         bool = !isFormModeFieldCanChange(this.isFormmodeUse, this.isFieldNoValueCanEdit, this.tablename, str3, str5, str6, str4) ? true : false;
/*     */       } 
/*     */       
/* 208 */       hashMap.put("com", getCom(bool, list));
/* 209 */       hashMap.put("fieldId", str8);
/* 210 */       hashMap.put("rowNum", Integer.valueOf(b));
/* 211 */       hashMap.put("fieldName", Util.toScreen(str3, this.user.getLanguage()));
/* 212 */       hashMap.put("fieldLabelName", TextUtil.toBase64ForMultilang(str9));
/* 213 */       hashMap.put("canDelete", Integer.valueOf("true".equals(str11) ? 1 : 0));
/*     */       
/* 215 */       ArrayList arrayList1 = new ArrayList();
/* 216 */       FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(this.formid, 1, this.user, false);
/* 217 */       List list1 = formFieldSetBiz.getFieldTypeData(null, 1, this.formid, Util.getIntValue(str8), true, false);
/*     */       
/* 219 */       hashMap.put("fieldType", list1);
/* 220 */       hashMap.put("fieldgroupid", recordSet1.getString("fieldgroupid"));
/* 221 */       hashMap.put("tableName", this.tablename);
/* 222 */       if ("1".equals(str5) && "1".equals(str6))
/*     */       {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 243 */         hashMap.put("maxLength", Integer.valueOf(-1));
/*     */       }
/* 245 */       arrayList.add(hashMap);
/* 246 */       b++;
/*     */     } 
/* 248 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<Object> getDetailTableDatas() {
/* 257 */     ArrayList<ArrayList<HashMap<Object, Object>>> arrayList = new ArrayList();
/* 258 */     RecordSet recordSet1 = new RecordSet();
/* 259 */     RecordSet recordSet2 = new RecordSet();
/* 260 */     RecordSet recordSet3 = new RecordSet();
/*     */     
/* 262 */     recordSet1.executeQuery("select * from Workflow_billdetailtable where billid=" + this.formid + " order by orderid", new Object[0]);
/* 263 */     while (recordSet1.next()) {
/* 264 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 265 */       String str1 = recordSet1.getString("tablename");
/*     */       
/* 267 */       byte b = 0;
/*     */       
/* 269 */       String str2 = "";
/* 270 */       int i = 0;
/* 271 */       String str3 = "";
/* 272 */       String str4 = "";
/* 273 */       String str5 = "";
/*     */ 
/*     */       
/* 276 */       recordSet2.executeQuery("select t.*,(select 1 from workflow_billfield chit where chit.id = t.childfieldid and chit.fieldhtmltype = 5 and chit.selectitemtype = 2) as existschild from workflow_billfield t where billid=" + this.formid + " and viewtype=1 and detailtable='" + str1 + "' order by dsporder,id", new Object[0]);
/* 277 */       while (recordSet2.next()) {
/* 278 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 279 */         String str6 = recordSet2.getString("id");
/* 280 */         str2 = recordSet2.getString("fieldname");
/* 281 */         i = recordSet2.getInt("fieldlabel");
/* 282 */         String str7 = LabelUtil.getMultiLangLabel(i + "");
/*     */ 
/*     */         
/* 285 */         str3 = recordSet2.getString("fielddbtype");
/* 286 */         str4 = recordSet2.getString("fieldhtmltype");
/* 287 */         str5 = recordSet2.getString("type");
/*     */         
/* 289 */         int j = Util.getIntValue(recordSet2.getString("childfieldid"));
/*     */         
/* 291 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 292 */         if (j > 0) {
/*     */           
/* 294 */           recordSet3.execute("select * from workflow_SelectItem where isbill=1 and fieldid=" + j);
/* 295 */           while (recordSet3.next()) {
/* 296 */             int k = Util.getIntValue(recordSet3.getString("selectvalue"), -1);
/* 297 */             String str = Util.null2String(recordSet3.getString("selectname"));
/* 298 */             hashtable.put("item_" + k, str);
/*     */           } 
/*     */         } 
/* 301 */         String str8 = str2 + "+1+" + str4 + "+" + str1 + "+" + this.formid + "+" + str5;
/* 302 */         String str9 = this.formFieldTransMethod.getCanCheckBox(str8);
/* 303 */         boolean bool = false;
/* 304 */         if (this.isFromMode != 1) {
/* 305 */           bool = !this.canChange ? true : false;
/*     */         } else {
/* 307 */           bool = !isFormModeFieldCanChange(this.isFormmodeUse, this.isFieldNoValueCanEdit, str1, str2, str4, str5, str3) ? true : false;
/*     */         } 
/*     */         
/* 310 */         hashMap.put("com", getCom(bool, (List<SearchConditionOption>)null));
/* 311 */         hashMap.put("fieldId", str6);
/* 312 */         hashMap.put("rowNum", Integer.valueOf(b));
/* 313 */         hashMap.put("fieldName", Util.toScreen(str2, this.user.getLanguage()));
/* 314 */         hashMap.put("fieldLabelName", TextUtil.toBase64ForMultilang(str7));
/*     */         
/* 316 */         hashMap.put("canDelete", Integer.valueOf("true".equals(str9) ? 1 : 0));
/*     */         
/* 318 */         ArrayList arrayList2 = new ArrayList();
/* 319 */         FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(this.formid, 1, this.user, true);
/* 320 */         List list = formFieldSetBiz.getFieldTypeData(null, 1, this.formid, Util.getIntValue(str6), true, false);
/* 321 */         hashMap.put("fieldType", list);
/* 322 */         hashMap.put("tableName", str1);
/* 323 */         if ("1".equals(str4) && "1".equals(str5))
/*     */         {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 343 */           hashMap.put("maxLength", Integer.valueOf(-1));
/*     */         }
/* 345 */         arrayList1.add(hashMap);
/* 346 */         b++;
/*     */       } 
/* 348 */       arrayList.add(arrayList1);
/*     */     } 
/* 350 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Map<String, List<Object>> getCom(boolean paramBoolean, List<SearchConditionOption> paramList) {
/* 358 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 360 */     hashMap.put("fieldLabelName", getInputCom("fieldLabelName", false, true));
/*     */     
/* 362 */     hashMap.put("fieldName", getInputCom("fieldName", paramBoolean));
/*     */     
/* 364 */     hashMap.put("fieldType", getFieldTypeCom(paramBoolean));
/*     */ 
/*     */     
/* 367 */     if (paramList != null) {
/* 368 */       hashMap.put("fieldgroupid", getSelectCom("fieldgroupid", false, paramList));
/*     */     }
/* 370 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<Object> getInputCom(String paramString, boolean paramBoolean1, boolean paramBoolean2) {
/* 378 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 379 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 380 */     hashMap1.put("key", paramString);
/* 381 */     hashMap1.put("type", ConditionType.INPUT);
/* 382 */     hashMap1.put("viewAttr", Integer.valueOf(paramBoolean1 ? 1 : 3));
/* 383 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 384 */     hashMap2.put("stringLength", Integer.valueOf(250));
/* 385 */     if (paramBoolean2) {
/* 386 */       hashMap2.put("inputType", "multilang");
/* 387 */       hashMap2.put("isBase64", Boolean.valueOf(true));
/*     */     } 
/* 389 */     hashMap1.put("otherParams", hashMap2);
/* 390 */     arrayList.add(hashMap1);
/* 391 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   protected List<Object> getInputCom(String paramString, boolean paramBoolean) {
/* 395 */     return getInputCom(paramString, paramBoolean, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<Object> getFieldTypeCom(boolean paramBoolean) {
/* 403 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 404 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 405 */     hashMap.put("key", "fieldType");
/* 406 */     hashMap.put("viewAttr", Integer.valueOf(paramBoolean ? 1 : 2));
/* 407 */     hashMap.put("type", ConditionType.CUSTOMFIELD);
/* 408 */     arrayList.add(hashMap);
/* 409 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   protected List<Object> getSelectCom(String paramString, boolean paramBoolean, List<SearchConditionOption> paramList) {
/* 413 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 414 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 415 */     hashMap.put("key", paramString);
/* 416 */     hashMap.put("viewAttr", Integer.valueOf(paramBoolean ? 1 : 2));
/* 417 */     hashMap.put("type", ConditionType.SELECT);
/*     */     try {
/* 419 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 420 */       for (SearchConditionOption searchConditionOption : paramList) {
/* 421 */         arrayList1.add(searchConditionOption.clone());
/*     */       }
/* 423 */       hashMap.put("options", arrayList1);
/* 424 */     } catch (Exception exception) {
/* 425 */       writeLog(exception);
/*     */     } 
/* 427 */     arrayList.add(hashMap);
/* 428 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   protected boolean isFormModeFieldCanChange(boolean paramBoolean1, boolean paramBoolean2, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 433 */     if ((paramBoolean1 && paramBoolean2) || !paramBoolean1) {
/* 434 */       RecordSet recordSet = new RecordSet();
/* 435 */       String str = "";
/* 436 */       if ((recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("sqlserver")) && paramString3
/* 437 */         .equals("4") && paramString4
/* 438 */         .equals("1")) {
/* 439 */         str = "select count(1) from " + paramString1 + " where " + paramString2 + " is not null ";
/*     */       }
/* 441 */       else if (paramString5.toUpperCase().indexOf("char(".toUpperCase()) > -1) {
/* 442 */         str = "select count(1) from " + paramString1 + " where " + paramString2 + " is not null and " + paramString2 + " !='' ";
/* 443 */       } else if (paramString5.toUpperCase().indexOf("varchar(".toUpperCase()) > -1 || paramString5
/* 444 */         .toUpperCase().indexOf("text".toUpperCase()) > -1 || paramString5
/* 445 */         .toUpperCase().indexOf("varchar2(".toUpperCase()) > -1 || paramString5
/* 446 */         .toUpperCase().indexOf("decimal(".toUpperCase()) > -1 || paramString5
/* 447 */         .toUpperCase().indexOf("INTEGER") > -1 || paramString5
/* 448 */         .toUpperCase().indexOf("NUMBER(") > -1 || paramString5
/* 449 */         .toUpperCase().indexOf("CLOB") > -1 || paramString5
/* 450 */         .toUpperCase().indexOf("BROWSER") > -1 || paramString5
/* 451 */         .toUpperCase().indexOf("INT") > -1) {
/* 452 */         str = "select count(1) from " + paramString1 + " where " + paramString2 + " is not null ";
/*     */       } else {
/* 454 */         str = "select count(1) from " + paramString1 + " where " + paramString2 + " is not null and " + paramString2 + " !='' ";
/*     */       } 
/*     */       
/* 457 */       recordSet.executeSql(str);
/* 458 */       if (recordSet.next()) {
/* 459 */         return (recordSet.getInt(1) == 0);
/*     */       }
/*     */     } 
/* 462 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<List<String>> getAllExistDBName(String paramString) {
/* 471 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/* 472 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 474 */     recordSet.executeQuery("select fieldname from workflow_billfield  where billid = ? and viewtype = 0", new Object[] { paramString });
/* 475 */     ArrayList<String> arrayList1 = new ArrayList();
/* 476 */     while (recordSet.next()) {
/* 477 */       arrayList1.add(Util.null2String(recordSet.getString("fieldname")));
/*     */     }
/* 479 */     arrayList.add(arrayList1);
/*     */     
/* 481 */     recordSet.executeQuery("select tablename from workflow_billdetailtable where billid = ? ", new Object[] { paramString });
/* 482 */     ArrayList<String> arrayList2 = new ArrayList();
/* 483 */     while (recordSet.next()) {
/* 484 */       arrayList2.add(Util.null2String(recordSet.getString("tablename")));
/*     */     }
/* 486 */     for (String str : arrayList2) {
/* 487 */       ArrayList<String> arrayList3 = new ArrayList();
/* 488 */       recordSet.executeQuery("select fieldname from workflow_billfield  where billid = ? and viewtype = 1 and detailtable = ?", new Object[] { paramString, str });
/* 489 */       while (recordSet.next()) {
/* 490 */         arrayList3.add(Util.null2String(recordSet.getString("fieldname")));
/*     */       }
/* 492 */       arrayList.add(arrayList3);
/*     */     } 
/* 494 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public GetBatchEditTableInfoCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetBatchEditTableInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */