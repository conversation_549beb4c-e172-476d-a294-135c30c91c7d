/*     */ package com.engine.workflow.cmd.formManage.formSetting.baseSet;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.entity.LogInfoEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetBaseInfoConditionCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  36 */   private final String FORM_RIGHT_STR = "FormManage:All";
/*  37 */   private String subCompanyId2 = "";
/*  38 */   private String subCompanyId3 = "";
/*     */   private ManageDetachComInfo manageDetachComInfo;
/*  40 */   protected int operatelevel = -1;
/*     */ 
/*     */ 
/*     */   
/*     */   public GetBaseInfoConditionCmd(Map<String, Object> paramMap, User paramUser) {
/*  45 */     this.params = paramMap;
/*  46 */     this.user = paramUser;
/*  47 */     this.manageDetachComInfo = new ManageDetachComInfo();
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  52 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  57 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  58 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  59 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  61 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  62 */     hashMap2.put("items", getItemList());
/*  63 */     arrayList.add(hashMap2);
/*     */     
/*  65 */     hashMap1.put("conditioninfo", arrayList);
/*  66 */     hashMap1.put("subCompanyId", this.subCompanyId2);
/*  67 */     hashMap1.put("subCompanyId3", this.subCompanyId3);
/*  68 */     hashMap1.put("logArray", getLogArray());
/*  69 */     hashMap1.put("operatelevel", Integer.valueOf(this.operatelevel));
/*  70 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   protected List<LogInfoEntity> getLogArray() {
/*  74 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     
/*  76 */     ArrayList<LogInfoEntity> arrayList = new ArrayList();
/*     */     
/*  78 */     LogInfoEntity logInfoEntity1 = new LogInfoEntity();
/*  79 */     logInfoEntity1.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  80 */     logInfoEntity1.setLogSmallType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  81 */     logInfoEntity1.setTargetId(i + "");
/*  82 */     arrayList.add(logInfoEntity1);
/*     */     
/*  84 */     LogInfoEntity logInfoEntity2 = new LogInfoEntity();
/*  85 */     logInfoEntity2.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  86 */     logInfoEntity2.setBelongType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  87 */     logInfoEntity2.setBelongTypeTargetId(i + "");
/*  88 */     arrayList.add(logInfoEntity2);
/*     */     
/*  90 */     return arrayList;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionItem> getItemList() {
/*  94 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  95 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  97 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  98 */     String str1 = "FormManage:All";
/*  99 */     int i = Util.getIntValue(Util.null2String(this.params.get("isFromMode")), 0);
/* 100 */     if (i == 1) {
/* 101 */       str1 = "FORMMODEFORM:ALL";
/*     */     }
/* 103 */     int j = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 104 */     int k = Util.getIntValue(Util.null2String(this.params.get("isBill")));
/* 105 */     boolean bool1 = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/* 106 */     boolean bool2 = (new ManageDetachComInfo()).isUseFmManageDetach() ? true : false;
/*     */     
/* 108 */     String str2 = "";
/* 109 */     String str3 = "";
/*     */ 
/*     */     
/* 112 */     RecordSet recordSet = new RecordSet();
/* 113 */     recordSet.executeQuery("select isvirtualform from ModeFormExtend where formid=" + j, new Object[0]);
/* 114 */     int m = 0;
/* 115 */     if (recordSet.next()) {
/* 116 */       m = recordSet.getInt("isvirtualform");
/*     */     }
/*     */     
/* 119 */     int n = -1;
/* 120 */     String str4 = "";
/*     */     
/* 122 */     recordSet.executeQuery("select * from workflow_bill where id=" + j, new Object[0]);
/* 123 */     if (recordSet.next()) {
/*     */       
/* 125 */       str2 = LabelUtil.getMultiLangLabel(recordSet.getString("namelabel"));
/*     */       
/* 127 */       str3 = recordSet.getString("formdes");
/*     */       
/* 129 */       n = recordSet.getInt("subcompanyid");
/* 130 */       this.subCompanyId2 = "" + n;
/* 131 */       this.subCompanyId3 = recordSet.getString("subcompanyid3");
/* 132 */       str4 = recordSet.getString("tablename");
/*     */     } 
/*     */     
/* 135 */     boolean bool3 = true;
/* 136 */     recordSet.executeQuery("select * from workflow_base where formid=" + j, new Object[0]);
/* 137 */     if (recordSet.next()) bool3 = false;
/*     */     
/* 139 */     recordSet.executeQuery("select * from modeinfo where formid=" + j, new Object[0]);
/* 140 */     if (recordSet.next()) bool3 = false;
/*     */     
/* 142 */     if (m == 1) {
/* 143 */       bool3 = false;
/*     */     }
/* 145 */     if (i == 1) {
/* 146 */       if (bool2 == true) {
/* 147 */         if (this.subCompanyId3.equals("")) {
/* 148 */           this.subCompanyId3 = "" + this.user.getUserSubCompany1();
/*     */         }
/* 150 */         this.operatelevel = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), str1, Util.getIntValue(this.subCompanyId3, -1));
/*     */       }
/* 152 */       else if (HrmUserVarify.checkUserRight(str1, this.user)) {
/* 153 */         this.operatelevel = 2;
/*     */       }
/*     */     
/* 156 */     } else if (bool1 == true) {
/* 157 */       this.operatelevel = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), str1, n);
/*     */     }
/* 159 */     else if (HrmUserVarify.checkUserRight(str1, this.user)) {
/* 160 */       this.operatelevel = 2;
/*     */     } 
/*     */ 
/*     */     
/* 164 */     if (this.operatelevel < 2) {
/* 165 */       bool3 = false;
/*     */     }
/*     */     
/* 168 */     boolean bool4 = (j > 0 && k == 1) ? true : false;
/*     */ 
/*     */ 
/*     */     
/* 172 */     if (i == 1) {
/*     */       
/* 174 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 15190, "showFormId");
/* 175 */       searchConditionItem.setValue(Integer.valueOf(j));
/* 176 */       searchConditionItem.setViewAttr(1);
/* 177 */       searchConditionItem.setLabelcol(6);
/* 178 */       searchConditionItem.setFieldcol(10);
/* 179 */       searchConditionItem.setLabel(SystemEnv.getHtmlLabelName(700, this.user.getLanguage()) + " Id");
/* 180 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 181 */       hashMap.put("hasBorder", Boolean.valueOf(true));
/* 182 */       searchConditionItem.setOtherParams(hashMap);
/* 183 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */ 
/*     */     
/* 187 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15451, "formName");
/* 188 */     searchConditionItem1.setValue(TextUtil.toBase64ForMultilang(Util.toScreenToEdit(str2, this.user.getLanguage())));
/* 189 */     searchConditionItem1.setLabelcol(6);
/* 190 */     searchConditionItem1.setFieldcol(10);
/* 191 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 192 */     hashMap1.put("hasBorder", Boolean.valueOf(true));
/* 193 */     hashMap1.put("inputType", "multilang");
/* 194 */     hashMap1.put("isBase64", Boolean.valueOf(true));
/* 195 */     searchConditionItem1.setOtherParams(hashMap1);
/* 196 */     searchConditionItem1.setViewAttr(bool4 ? 1 : 3);
/* 197 */     searchConditionItem1.setRules("required");
/* 198 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/* 201 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 15190, "tableName");
/* 202 */     searchConditionItem2.setValue(str4);
/* 203 */     searchConditionItem2.setViewAttr(1);
/* 204 */     searchConditionItem2.setLabelcol(6);
/* 205 */     searchConditionItem2.setFieldcol(10);
/* 206 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 207 */     hashMap2.put("hasBorder", Boolean.valueOf(true));
/* 208 */     searchConditionItem2.setOtherParams(hashMap2);
/* 209 */     arrayList.add(searchConditionItem2);
/*     */ 
/*     */     
/* 212 */     if (bool1 == true && !Util.null2String(this.params.get("isFromMode")).equals("1")) {
/* 213 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subCompanyId", "169");
/* 214 */       BrowserBean browserBean = searchConditionItem.getBrowserConditionParam();
/* 215 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 216 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 217 */       hashMap.put("id", this.subCompanyId2);
/* 218 */       hashMap.put("name", (new SubCompanyComInfo()).getSubCompanyname(this.subCompanyId2));
/* 219 */       arrayList1.add(hashMap);
/* 220 */       browserBean.setReplaceDatas(arrayList1);
/* 221 */       browserBean.setHasBorder(true);
/* 222 */       browserBean.getDataParams().put("rightStr", "FormManage:All");
/* 223 */       browserBean.getCompleteParams().put("rightStr", "FormManage:All");
/* 224 */       browserBean.getConditionDataParams().put("rightStr", "FormManage:All");
/* 225 */       browserBean.setTitle(SystemEnv.getHtmlLabelName(141, this.user.getLanguage()));
/* 226 */       searchConditionItem.setLabelcol(6);
/* 227 */       searchConditionItem.setFieldcol(10);
/* 228 */       searchConditionItem.setViewAttr(bool4 ? 1 : 3);
/* 229 */       searchConditionItem.setRules("required");
/* 230 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */     
/* 233 */     if (Util.null2String(this.params.get("isFromMode")).equals("1") && this.manageDetachComInfo.isUseFmManageDetach()) {
/* 234 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subCompanyId3", "164");
/* 235 */       BrowserBean browserBean = searchConditionItem.getBrowserConditionParam();
/* 236 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 237 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 238 */       hashMap.put("id", this.subCompanyId3);
/* 239 */       hashMap.put("name", (new SubCompanyComInfo()).getSubCompanyname(this.subCompanyId3));
/* 240 */       arrayList1.add(hashMap);
/* 241 */       browserBean.setReplaceDatas(arrayList1);
/* 242 */       browserBean.setTitle(SystemEnv.getHtmlLabelName(141, this.user.getLanguage()));
/* 243 */       browserBean.setHasBorder(true);
/* 244 */       searchConditionItem.setLabelcol(6);
/* 245 */       searchConditionItem.setFieldcol(10);
/* 246 */       searchConditionItem.setViewAttr(bool4 ? 1 : 3);
/* 247 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 252 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.TEXTAREA, 15452, "formDes");
/* 253 */     searchConditionItem3.setValue(Util.toScreenToEdit(str3, this.user.getLanguage()));
/* 254 */     searchConditionItem3.setLabelcol(6);
/* 255 */     searchConditionItem3.setFieldcol(10);
/* 256 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 257 */     if (bool4) {
/* 258 */       searchConditionItem3.setConditionType(ConditionType.INPUT);
/* 259 */       hashMap3.put("hasBorder", Boolean.valueOf(true));
/*     */     } else {
/* 261 */       hashMap3.put("minRows", Integer.valueOf(4));
/* 262 */       hashMap3.put("maxRows", Integer.valueOf(4));
/*     */     } 
/* 264 */     searchConditionItem3.setOtherParams(hashMap3);
/* 265 */     searchConditionItem3.setViewAttr(bool4 ? 1 : 2);
/* 266 */     arrayList.add(searchConditionItem3);
/*     */ 
/*     */     
/* 269 */     return arrayList;
/*     */   }
/*     */   
/*     */   public GetBaseInfoConditionCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/baseSet/GetBaseInfoConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */