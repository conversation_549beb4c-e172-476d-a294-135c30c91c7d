/*    */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.exceldesign.FormTemplateManager;
/*    */ import weaver.workflow.formimport.FormImportServices;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoFormImportCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoFormImportCmd() {}
/*    */   
/*    */   public DoFormImportCmd(Map<String, Object> paramMap, User paramUser) {
/* 29 */     this.params = paramMap;
/* 30 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 35 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*    */     Map<String, Object> map;
/* 41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 43 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/* 44 */     if (bool) {
/* 45 */       map = doImport();
/* 46 */       boolean bool1 = ((Boolean)map.get("status")).booleanValue();
/* 47 */       map.remove("status");
/* 48 */       map.put("importStatus", Boolean.valueOf(bool1));
/*    */     } else {
/* 50 */       map.put("noRight", Boolean.valueOf(true));
/*    */     } 
/* 52 */     return map;
/*    */   }
/*    */   
/*    */   public Map<String, Object> doImport() {
/* 56 */     Map<String, Boolean> map = (Map)new HashMap<>();
/*    */     try {
/* 58 */       FormImportServices formImportServices = new FormImportServices();
/* 59 */       formImportServices.setUser(this.user);
/* 60 */       formImportServices.setRemoteAddr(Util.null2String(this.params.get("param_ip")));
/*    */       
/* 62 */       int i = Util.getIntValue(Util.null2String(this.params.get("fieldId")));
/* 63 */       String str = Util.null2String(this.params.get("importSap"));
/* 64 */       boolean bool1 = true;
/* 65 */       if ("1".equals(str)) {
/* 66 */         bool1 = false;
/*    */       }
/* 68 */       map = formImportServices.importFormByXmlFile(i, bool1);
/* 69 */     } catch (Exception exception) {
/* 70 */       map.put("status", Boolean.valueOf(false));
/* 71 */       exception.printStackTrace();
/*    */     } 
/* 73 */     boolean bool = ((Boolean)map.get("status")).booleanValue();
/* 74 */     if (bool) {
/* 75 */       int i = Util.getIntValue((new StringBuilder()).append(map.get("formid")).append("").toString());
/* 76 */       String str = (new FormTemplateManager()).getFormName(i, 1, this.user.getLanguage());
/* 77 */       map.put("formName", str);
/*    */     } 
/* 79 */     return (Map)map;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/DoFormImportCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */