/*     */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.label.LabelComInfo;
/*     */ import weaver.workflow.form.FormComInfo;
/*     */ import weaver.workflow.form.FormManager;
/*     */ import weaver.workflow.workflow.BillComInfo;
/*     */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoAddFormCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  37 */   private BizLogContext logContext = new BizLogContext();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DoAddFormCmd(Map<String, Object> paramMap, User paramUser) {
/*  43 */     this.params = paramMap;
/*  44 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  49 */     this.logContext.setDateObject(new Date());
/*  50 */     this.logContext.setUserid(this.user.getUID());
/*  51 */     this.logContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  52 */     this.logContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  53 */     this.logContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  54 */     this.logContext.setOperateType(BizLogOperateType.ADD);
/*  55 */     this.logContext.setParams(this.params);
/*  56 */     this.logContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/*  57 */     return this.logContext;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  64 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/*  65 */     if (bool) {
/*  66 */       String str6; RecordSet recordSet = new RecordSet();
/*  67 */       ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  68 */       boolean bool1 = recordSet.getDBType().equals("oracle");
/*  69 */       boolean bool2 = recordSet.getDBType().equals("db2");
/*  70 */       boolean bool3 = recordSet.getDBType().equals("sqlserver");
/*     */       
/*  72 */       int i = -1;
/*  73 */       String str1 = Util.null2String(this.params.get("from"));
/*  74 */       if ("".equals(str1)) {
/*  75 */         str1 = "addDefineForm";
/*     */       }
/*  77 */       String str2 = Util.null2String(this.params.get("formName"));
/*  78 */       int j = Util.getIntValue(Util.null2String(this.params.get("oldFormId")), 0);
/*  79 */       String str3 = Util.null2String(this.params.get("formDes"));
/*  80 */       int k = Util.getIntValue(Util.null2String(this.params.get("isFromMode")), 0);
/*  81 */       int m = Util.getIntValue(Util.null2String(this.params.get("subcompanyId")), -1);
/*  82 */       int n = Util.getIntValue(Util.null2String(this.params.get("subcompanyId3")), -1);
/*  83 */       String str4 = Util.null2String(this.params.get("appid"));
/*  84 */       String str5 = Util.null2String(this.params.get("saveas_srcid"));
/*  85 */       str2 = str2.replaceAll("<", "＜").replaceAll(">", "＞").replaceAll("'", "''");
/*  86 */       str2 = Util.toHtmlForSplitPage(str2);
/*     */       
/*  88 */       boolean bool4 = false;
/*  89 */       recordSet.executeSql("select namelabel from workflow_bill");
/*     */       
/*  91 */       while (recordSet.next()) {
/*  92 */         int i1 = Util.getIntValue(Util.null2String(recordSet.getString("namelabel")), 0);
/*  93 */         if (i1 != 0) {
/*  94 */           str6 = SystemEnv.getHtmlLabelName(i1, this.user.getLanguage());
/*  95 */           if (str6 != null) {
/*  96 */             str6 = str6.trim();
/*     */           }
/*  98 */           if (str2.equals(str6)) {
/*  99 */             bool4 = true;
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/* 104 */       recordSet.executeSql("select formname from workflow_formbase");
/* 105 */       while (recordSet.next()) {
/* 106 */         String str = Util.null2String(recordSet.getString("formname"));
/* 107 */         if (!str.equals("") && 
/* 108 */           str2.equals(str)) {
/* 109 */           bool4 = true;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/* 114 */       if (bool4) {
/*     */         
/* 116 */         hashMap.put("isSameName", Boolean.valueOf(bool4));
/* 117 */         return (Map)hashMap;
/*     */       } 
/*     */       
/* 120 */       FormManager formManager = new FormManager();
/* 121 */       str3 = str3.replaceAll("<", "＜").replaceAll(">", "＞").replaceAll("'", "''");
/* 122 */       str3 = Util.toHtmlForSplitPage(str3);
/*     */       
/* 124 */       if (k == 1) {
/* 125 */         i = formManager.getNewFormId();
/* 126 */         str6 = "uf_" + Util.null2String(this.params.get("tablename"));
/* 127 */         if (str1.equals("addDefineForm")) {
/* 128 */           boolean bool5 = formManager.isHaveSameTableInDB(str6);
/* 129 */           if (bool5);
/*     */         }
/*     */       
/*     */       } else {
/*     */         
/* 134 */         Map<String, Object> map = getFormInfo(formManager);
/* 135 */         str6 = Util.null2String(map.get("tableName"));
/* 136 */         i = Util.getIntValue(Util.null2String(map.get("formId")));
/*     */       } 
/* 138 */       if (i < -1) {
/* 139 */         String str8 = recordSet.getDBType();
/* 140 */         String str9 = null;
/* 141 */         String str10 = null;
/* 142 */         String str11 = null;
/* 143 */         boolean bool5 = false;
/* 144 */         str2 = str2.replaceAll("<", "＜").replaceAll(">", "＞");
/* 145 */         RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 146 */         recordSetTrans.setAutoCommit(false);
/*     */         try {
/* 148 */           int i1 = (new LabelUtil()).getLabelId4Workflow(str2);
/*     */           
/* 150 */           if (m != -1) {
/* 151 */             n = m;
/* 152 */           } else if (n != -1 && n != 0) {
/* 153 */             m = n;
/*     */           } else {
/* 155 */             if (m == -1) {
/* 156 */               m = Util.getIntValue((new ManageDetachComInfo()).getWfdftsubcomid(), -1);
/* 157 */               if (m == -1) {
/* 158 */                 recordSetTrans.executeSql("select min(id) as id from HrmSubCompany");
/* 159 */                 if (recordSetTrans.next())
/* 160 */                   m = recordSetTrans.getInt("id"); 
/*     */               } 
/*     */             } 
/* 163 */             if (n == -1 || n == 0) {
/* 164 */               n = Util.getIntValue(manageDetachComInfo.getWfdftsubcomid(), -1);
/* 165 */               if (n == -1 || n == 0) {
/* 166 */                 recordSetTrans.executeSql("select min(id) as id from HrmSubCompany");
/* 167 */                 if (recordSetTrans.next()) {
/* 168 */                   n = recordSetTrans.getInt("id");
/*     */                 }
/*     */               } 
/*     */             } 
/* 172 */             if (k == 1) {
/* 173 */               recordSetTrans.executeSql("select fmdetachable,fmdftsubcomid,dftsubcomid from SystemSet");
/* 174 */               if (recordSetTrans.next()) {
/* 175 */                 int i2 = Util.getIntValue(recordSetTrans.getString("fmdetachable"), 0);
/* 176 */                 if (i2 == 1) {
/* 177 */                   n = Util.getIntValue(recordSetTrans.getString("fmdftsubcomid"), -1);
/* 178 */                   if (n == -1 || n == 0) {
/* 179 */                     n = Util.getIntValue(recordSetTrans.getString("dftsubcomid"), -1);
/*     */                   }
/*     */                 } else {
/* 182 */                   n = -1;
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/* 187 */           recordSetTrans.executeSql("insert into workflow_bill(id,namelabel,tablename,detailkeyfield,formdes,subcompanyid,subCompanyId3) values(" + i + "," + i1 + ",'" + str6 + "','mainid','" + str3 + "'," + m + "," + n + ")");
/* 188 */           if ("oracle".equals(str8)) {
/* 189 */             recordSetTrans.executeSql("create table " + str6 + "(id integer primary key not null, requestId integer)");
/* 190 */           } else if ("mysql".equals(str8)) {
/* 191 */             recordSetTrans.executeSql("create table `" + str6 + "` (id int(11) NOT NULL AUTO_INCREMENT,requestId int(11) ,PRIMARY KEY (`id`) )");
/*     */           }
/* 193 */           else if ("postgresql".equals(str8)) {
/* 194 */             recordSetTrans.executeSql("create table " + str6 + "(id serial primary key , requestId integer)");
/*     */           } else {
/*     */             
/* 197 */             recordSetTrans.executeSql("create table " + str6 + "(id int IDENTITY(1,1) primary key CLUSTERED, requestId integer)");
/*     */           } 
/* 199 */           str9 = "ft_main_" + Math.abs(i) + "_req_index";
/* 200 */           recordSetTrans.executeSql(String.format("create index %s on %s (requestid)", new Object[] { str9, str6 }));
/* 201 */           if (!StringHelper.isEmpty(str4)) {
/* 202 */             recordSetTrans.executeSql("insert into AppFormInfo(appid,formid)values(" + str4 + "," + i + ")");
/*     */           }
/* 204 */           recordSetTrans.commit();
/* 205 */           if ("oracle".equals(str8)) {
/* 206 */             String str = " MAXVALUE 9223372036854775807 ";
/* 207 */             str10 = String.format("%s_Id", new Object[] { str6 });
/* 208 */             recordSet.executeSql("create sequence " + str10 + " start with 1 increment by 1 " + str + " nocycle nocache");
/* 209 */             recordSet.setChecksql(false);
/* 210 */             str11 = String.format("%s_Id_Tr", new Object[] { str6 });
/* 211 */             recordSet.executeSql("CREATE OR REPLACE TRIGGER " + str11 + " before insert on " + str6 + " for each row begin select " + str6 + "_Id.nextval into :new.id from dual; end;");
/*     */           } 
/* 213 */           (new LabelComInfo()).addLabeInfoCache("" + i1);
/* 214 */           (new BillComInfo()).addBillCache("" + i);
/* 215 */           WorkflowBillComInfo workflowBillComInfo = new WorkflowBillComInfo();
/* 216 */           workflowBillComInfo.addWorkflowBillCache(String.valueOf(i));
/* 217 */           bool5 = true;
/* 218 */         } catch (Exception exception) {
/* 219 */           bool5 = false;
/* 220 */           recordSetTrans.rollback();
/*     */         } 
/*     */         
/* 223 */         if (bool5 == true) {
/* 224 */           if ("saveAsNew".equals(str1))
/* 225 */             j = Util.getIntValue(str5, 0); 
/* 226 */           if (j != 0) {
/* 227 */             formManager.setFormInfoByTemplate(i, j, k, this.user);
/*     */           }
/*     */         } 
/* 230 */         if (bool3) {
/* 231 */           recordSet.executeSql("update workflow_billfield set detailtable = '' where detailtable is null");
/*     */         }
/*     */       } 
/* 234 */       (new FormComInfo()).addCache(i + "");
/* 235 */       hashMap.put("formId", Integer.valueOf(i));
/* 236 */       hashMap.put("formName", str2);
/*     */       
/* 238 */       String str7 = " select * from workflow_bill where id = ? ";
/* 239 */       recordSet.executeQuery(str7, new Object[] { Integer.valueOf(i) });
/* 240 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 241 */       if (recordSet.next()) {
/* 242 */         String[] arrayOfString = recordSet.getColumnName();
/* 243 */         for (String str : arrayOfString) {
/* 244 */           hashMap1.put(str, recordSet.getString(str));
/*     */         }
/*     */       } 
/* 247 */       this.logContext.setNewValues(hashMap1);
/* 248 */       this.logContext.setTargetId(i + "");
/* 249 */       this.logContext.setTargetName(str2);
/* 250 */       this.logContext.setDesc(this.user.getLastname() + 
/* 251 */           SystemEnv.getHtmlLabelName(526430, this.user.getLanguage()) + "：{" + str2 + "( " + i + " )} ");
/*     */     } else {
/* 253 */       hashMap.put("noRight", Boolean.valueOf(true));
/*     */     } 
/* 255 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> getFormInfo(FormManager paramFormManager) {
/* 263 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 264 */     String str = null;
/* 265 */     int i = paramFormManager.getNewFormId();
/*     */     try {
/* 267 */       byte b = 0;
/* 268 */       boolean bool = true;
/* 269 */       while (bool) {
/* 270 */         i += b;
/* 271 */         str = "formtable_main_" + (i * -1);
/* 272 */         bool = paramFormManager.isHaveSameTableInDB(str);
/* 273 */         b--;
/*     */       }
/*     */     
/* 276 */     } catch (Exception exception) {
/* 277 */       exception.printStackTrace();
/*     */     } 
/* 279 */     hashMap.put("tableName", str);
/* 280 */     hashMap.put("formId", Integer.valueOf(i));
/* 281 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public DoAddFormCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/DoAddFormCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */