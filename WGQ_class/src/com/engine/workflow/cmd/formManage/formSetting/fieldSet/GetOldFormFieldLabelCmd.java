/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.cloudstore.dev.api.util.TextUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetOldFormFieldLabelCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetOldFormFieldLabelCmd() {}
/*    */   
/*    */   public GetOldFormFieldLabelCmd(Map<String, Object> paramMap, User paramUser) {
/* 24 */     this.params = paramMap;
/* 25 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 30 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 35 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 36 */     String str1 = Util.null2String(this.params.get("formId"));
/* 37 */     String str2 = Util.null2String(this.params.get("fieldIds"));
/* 38 */     String[] arrayOfString = str2.split(",");
/* 39 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 40 */     RecordSet recordSet = new RecordSet();
/* 41 */     for (String str3 : arrayOfString) {
/* 42 */       String[] arrayOfString1 = new String[3];
/* 43 */       String str4 = " select fieldlable,langurageid from workflow_fieldlable where formid= ? and fieldid = ?";
/* 44 */       recordSet.executeQuery(str4, new Object[] { str1, str3 });
/* 45 */       while (recordSet.next()) {
/* 46 */         String str5 = recordSet.getString("langurageid");
/* 47 */         String str6 = recordSet.getString("fieldlable");
/* 48 */         if ("7".equals(str5)) {
/* 49 */           arrayOfString1[0] = str6; continue;
/* 50 */         }  if ("8".equals(str5)) {
/* 51 */           arrayOfString1[1] = str6; continue;
/* 52 */         }  if ("9".equals(str5)) {
/* 53 */           arrayOfString1[2] = str6;
/*    */         }
/*    */       } 
/* 56 */       hashMap2.put(str3, TextUtil.toBase64ForMultilang(Util.toMultiLangScreenFromArray(arrayOfString1)));
/*    */     } 
/* 58 */     hashMap1.put("labelDatas", hashMap2);
/* 59 */     return (Map)hashMap1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetOldFormFieldLabelCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */