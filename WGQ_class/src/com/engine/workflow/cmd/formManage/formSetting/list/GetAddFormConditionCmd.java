/*     */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetAddFormConditionCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetAddFormConditionCmd() {}
/*     */   
/*     */   public GetAddFormConditionCmd(Map<String, Object> paramMap, User paramUser) {
/*  35 */     this.params = paramMap;
/*  36 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  41 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  48 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/*  49 */     if (bool) {
/*  50 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  51 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/*  53 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/*  54 */       hashMap1.put("items", getItemList());
/*  55 */       arrayList.add(hashMap1);
/*     */       
/*  57 */       hashMap.put("conditioninfo", arrayList);
/*  58 */       hashMap.put("hasFormUserRight", Boolean.valueOf(HrmUserVarify.checkUserRight("FormManage:All", this.user)));
/*     */     } else {
/*  60 */       hashMap.put("noRight", Boolean.valueOf(true));
/*     */     } 
/*     */     
/*  63 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionItem> getItemList() {
/*  67 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  68 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  69 */     boolean bool = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/*  70 */     String str1 = Util.null2String(this.params.get("subCompanyId"));
/*  71 */     String str2 = Util.null2String(this.params.get("type"));
/*     */     
/*  73 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15451, "formName");
/*  74 */     searchConditionItem1.setViewAttr(3);
/*  75 */     searchConditionItem1.setFieldcol(16);
/*  76 */     searchConditionItem1.setLabelcol(6);
/*  77 */     searchConditionItem1.setRules("required|stringLength:600");
/*  78 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  79 */     hashMap1.put("inputType", "multilang");
/*     */     
/*  81 */     searchConditionItem1.setOtherParams(hashMap1);
/*  82 */     arrayList.add(searchConditionItem1);
/*  83 */     if (!"saveAsNew".equals(str2)) {
/*     */       
/*  85 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 385386, "oldFormId", "wfFormBrowser");
/*  86 */       searchConditionItem.setFieldcol(16);
/*  87 */       searchConditionItem.setLabelcol(6);
/*  88 */       BrowserBean browserBean = searchConditionItem.getBrowserConditionParam();
/*     */       
/*  90 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  91 */       hashMap.put("noNeedTypeSelect", "1");
/*  92 */       hashMap.put("isBill", "0");
/*  93 */       hashMap.put("noNeedVirtual", "1");
/*     */       
/*  95 */       browserBean.getConditionDataParams().putAll(hashMap);
/*  96 */       browserBean.getDataParams().putAll(hashMap);
/*  97 */       browserBean.getCompleteParams().putAll(hashMap);
/*  98 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */     
/* 101 */     if (bool == true) {
/* 102 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subcompanyId", "169");
/* 103 */       searchConditionItem.setViewAttr(3);
/* 104 */       searchConditionItem.setRules("required");
/* 105 */       searchConditionItem.setFieldcol(16);
/* 106 */       searchConditionItem.setLabelcol(6);
/* 107 */       BrowserBean browserBean = searchConditionItem.getBrowserConditionParam();
/* 108 */       browserBean.getDataParams().put("rightStr", "FormManage:All");
/* 109 */       browserBean.getCompleteParams().put("rightStr", "FormManage:All");
/* 110 */       browserBean.getConditionDataParams().put("rightStr", "FormManage:All");
/* 111 */       browserBean.setTitle(SystemEnv.getHtmlLabelName(141, this.user.getLanguage()));
/* 112 */       browserBean.setReplaceDatas(getSubcompanyBrowserData(str1));
/* 113 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */     
/* 116 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.TEXTAREA, 15452, "formDes");
/* 117 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 118 */     hashMap2.put("minRows", Integer.valueOf(4));
/* 119 */     hashMap2.put("maxRows", Integer.valueOf(4));
/* 120 */     searchConditionItem2.setFieldcol(16);
/* 121 */     searchConditionItem2.setLabelcol(6);
/* 122 */     searchConditionItem2.setOtherParams(hashMap2);
/* 123 */     searchConditionItem2.setRules("stringLength:500");
/* 124 */     arrayList.add(searchConditionItem2);
/*     */     
/* 126 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<Map<String, Object>> getSubcompanyBrowserData(String paramString) {
/* 135 */     RecordSet recordSet = new RecordSet();
/* 136 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 137 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 138 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 139 */     if (paramString != null && paramString.length() != 0) {
/* 140 */       hashMap.put("id", paramString);
/* 141 */       hashMap.put("name", subCompanyComInfo.getSubCompanyname(paramString));
/*     */     } else {
/* 143 */       String str = "";
/* 144 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 145 */       if (this.user.getUID() == 1) {
/* 146 */         recordSet.executeProc("SystemSet_Select", "");
/* 147 */         if (recordSet.next()) {
/* 148 */           str = recordSet.getString("wfdftsubcomid");
/*     */         }
/*     */       } else {
/* 151 */         int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FormManage:All", 1);
/* 152 */         if (arrayOfInt.length > 0) {
/* 153 */           str = String.valueOf(arrayOfInt[0]);
/*     */         }
/*     */       } 
/* 156 */       hashMap.put("id", str);
/* 157 */       hashMap.put("name", subCompanyComInfo.getSubCompanyname(str));
/*     */     } 
/* 159 */     arrayList.add(hashMap);
/*     */     
/* 161 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/GetAddFormConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */