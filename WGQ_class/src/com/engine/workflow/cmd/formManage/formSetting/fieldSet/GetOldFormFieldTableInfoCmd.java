/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.FormFieldTransMethod;
/*     */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*     */ import com.engine.workflow.entity.LogInfoEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import com.engine.workflow.util.FormAuthorityUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.DetailFieldComInfo;
/*     */ import weaver.workflow.field.FieldComInfo;
/*     */ import weaver.workflow.form.FormFieldMainManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetOldFormFieldTableInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  37 */   protected FieldComInfo fieldComInfo = new FieldComInfo();
/*  38 */   protected DetailFieldComInfo detailFieldComInfo = new DetailFieldComInfo();
/*  39 */   protected FormFieldTransMethod formFieldTransMethod = new FormFieldTransMethod();
/*  40 */   protected List<Object> datasList = new ArrayList();
/*  41 */   protected List<Object> browserReplaceDatas = new ArrayList();
/*     */ 
/*     */ 
/*     */   
/*     */   public GetOldFormFieldTableInfoCmd(Map<String, Object> paramMap, User paramUser) {
/*  46 */     this.params = paramMap;
/*  47 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  52 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  58 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*  59 */     hashMap.put("columns", getColumns());
/*  60 */     getDatasListAndReplaceDataList();
/*  61 */     hashMap.put("datasArray", this.datasList);
/*  62 */     hashMap.put("replaceDatasArray", this.browserReplaceDatas);
/*  63 */     hashMap.put("logArray", getLogArray(i));
/*  64 */     hashMap.put("operatelevel", Integer.valueOf((new FormAuthorityUtil()).getFormOperateLevel(i, 0, this.user)));
/*  65 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected List<LogInfoEntity> getLogArray(int paramInt) {
/*  70 */     ArrayList<LogInfoEntity> arrayList = new ArrayList();
/*     */     
/*  72 */     LogInfoEntity logInfoEntity1 = new LogInfoEntity();
/*  73 */     logInfoEntity1.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  74 */     logInfoEntity1.setLogSmallType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  75 */     logInfoEntity1.setTargetId(paramInt + "");
/*  76 */     arrayList.add(logInfoEntity1);
/*     */     
/*  78 */     LogInfoEntity logInfoEntity2 = new LogInfoEntity();
/*  79 */     logInfoEntity2.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  80 */     logInfoEntity2.setBelongType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  81 */     logInfoEntity2.setBelongTypeTargetId(paramInt + "");
/*  82 */     arrayList.add(logInfoEntity2);
/*     */     
/*  84 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<WeaTableEditEntity> getColumns() {
/*  92 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */     
/*  94 */     WeaTableEditEntity weaTableEditEntity1 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldName", "20%", "1");
/*  95 */     weaTableEditEntity1.setCom(getInputCom("fieldName", true));
/*  96 */     weaTableEditEntity1.setClassName("wea-table-edit-fieldName");
/*  97 */     arrayList.add(weaTableEditEntity1);
/*     */ 
/*     */     
/* 100 */     WeaTableEditEntity weaTableEditEntity2 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(687, this.user.getLanguage()), "fieldType", "20%", "1");
/* 101 */     weaTableEditEntity2.setCom(getInputCom("fieldType", true));
/* 102 */     weaTableEditEntity2.setClassName("wea-table-edit-fieldType");
/* 103 */     arrayList.add(weaTableEditEntity2);
/*     */ 
/*     */     
/* 106 */     WeaTableEditEntity weaTableEditEntity3 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(383458, this.user.getLanguage()), "fieldDBType", "20%", "1");
/* 107 */     weaTableEditEntity3.setCom(getInputCom("fieldDBType", true));
/* 108 */     weaTableEditEntity3.setClassName("wea-table-edit-fieldDBType");
/* 109 */     arrayList.add(weaTableEditEntity3);
/*     */ 
/*     */     
/* 112 */     WeaTableEditEntity weaTableEditEntity4 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "fieldShowName", "30%", "1");
/* 113 */     weaTableEditEntity4.setCom(getInputCom("fieldShowName", false, true));
/* 114 */     weaTableEditEntity4.setClassName("wea-table-edit-fieldShowName");
/* 115 */     arrayList.add(weaTableEditEntity4);
/*     */ 
/*     */     
/* 118 */     WeaTableEditEntity weaTableEditEntity5 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(517198, this.user.getLanguage()), "groupname", "10%", "1");
/* 119 */     weaTableEditEntity5.setCom(getInputCom("groupname", true));
/* 120 */     weaTableEditEntity5.setClassName("wea-table-edit-groupname");
/* 121 */     arrayList.add(weaTableEditEntity5);
/*     */     
/* 123 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getDatasListAndReplaceDataList() {
/* 130 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     try {
/* 132 */       FormFieldMainManager formFieldMainManager = new FormFieldMainManager();
/*     */       
/* 134 */       formFieldMainManager.setFormid(i);
/* 135 */       formFieldMainManager.selectFormField();
/* 136 */       ArrayList<Map<String, Object>> arrayList = new ArrayList();
/* 137 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*     */       
/* 139 */       Map map = FieldGroupBiz.getFieldGroupInfo(i, "0");
/*     */       
/* 141 */       while (formFieldMainManager.next()) {
/* 142 */         Map<String, Object> map1 = getFieldData(formFieldMainManager, false);
/* 143 */         int j = formFieldMainManager.getFieldGroupId();
/* 144 */         String str1 = "";
/* 145 */         if (j > 0) {
/* 146 */           str1 = Util.null2String((String)map.get(Integer.valueOf(j)));
/*     */         }
/* 148 */         map1.put("groupname", str1);
/* 149 */         arrayList.add(map1);
/*     */         
/* 151 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 152 */         hashMap.put("id", map1.get("fieldId"));
/* 153 */         hashMap.put("name", map1.get("fieldName"));
/* 154 */         arrayList1.add(hashMap);
/*     */       } 
/* 156 */       this.datasList.add(arrayList);
/* 157 */       this.browserReplaceDatas.add(arrayList1);
/*     */ 
/*     */       
/* 160 */       String str = "select distinct groupId from workflow_formfield where formid=? and isdetail='1' order by groupId";
/* 161 */       RecordSet recordSet = new RecordSet();
/* 162 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(i) });
/* 163 */       while (recordSet.next()) {
/* 164 */         formFieldMainManager.setFormid(i);
/* 165 */         formFieldMainManager.setGroupId(recordSet.getInt(1));
/* 166 */         formFieldMainManager.selectDetailFormField();
/* 167 */         ArrayList<Map<String, Object>> arrayList2 = new ArrayList();
/* 168 */         ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/* 169 */         while (formFieldMainManager.next()) {
/* 170 */           Map<String, Object> map1 = getFieldData(formFieldMainManager, true);
/* 171 */           arrayList2.add(map1);
/*     */           
/* 173 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 174 */           hashMap.put("id", map1.get("fieldId"));
/* 175 */           hashMap.put("name", map1.get("fieldName"));
/* 176 */           arrayList3.add(hashMap);
/*     */         } 
/* 178 */         this.datasList.add(arrayList2);
/* 179 */         this.browserReplaceDatas.add(arrayList3);
/*     */       }
/*     */     
/* 182 */     } catch (Exception exception) {
/* 183 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Map<String, Object> getFieldData(FormFieldMainManager paramFormFieldMainManager, boolean paramBoolean) throws Exception {
/* 195 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 196 */     RecordSet recordSet = new RecordSet();
/* 197 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 198 */     int j = paramFormFieldMainManager.getFieldid();
/* 199 */     String str1 = paramBoolean ? this.detailFieldComInfo.getFieldname(j + "") : this.fieldComInfo.getFieldname(j + "");
/* 200 */     String str2 = paramFormFieldMainManager.getFieldDbType();
/* 201 */     String[] arrayOfString = new String[3];
/* 202 */     recordSet.executeQuery("select fieldlable, langurageid from WORKFLOW_FIELDLABLE where formid = ? and fieldid = ?", new Object[] { Integer.valueOf(i), Integer.valueOf(j) });
/* 203 */     String str3 = "";
/* 204 */     while (recordSet.next()) {
/* 205 */       int k = Util.getIntValue(recordSet.getString("langurageid"), 0);
/* 206 */       str3 = Util.null2String(recordSet.getString("fieldlable"));
/* 207 */       switch (k) {
/*     */         case 7:
/* 209 */           arrayOfString[0] = str3;
/*     */ 
/*     */         
/*     */         case 8:
/* 213 */           arrayOfString[1] = str3;
/*     */ 
/*     */         
/*     */         case 9:
/* 217 */           arrayOfString[2] = str3;
/*     */       } 
/*     */ 
/*     */     
/*     */     } 
/* 222 */     Map<String, String> map = getFieldType(j, paramBoolean);
/* 223 */     hashMap.put("fieldId", Integer.valueOf(j));
/* 224 */     hashMap.put("fieldName", str1);
/* 225 */     hashMap.put("fieldType", this.formFieldTransMethod.fieldTypeTransmethod(map.get("htmlType"), map.get("type"), this.user.getLanguage()));
/* 226 */     hashMap.put("fieldDBType", str2);
/* 227 */     hashMap.put("fieldShowName", TextUtil.toBase64ForMultilang(Util.toMultiLangScreenFromArray(arrayOfString)));
/* 228 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<WeaTableEditComEntity> getInputCom(String paramString, boolean paramBoolean1, boolean paramBoolean2) {
/* 237 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/* 238 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity();
/* 239 */     weaTableEditComEntity.setKey(paramString);
/* 240 */     weaTableEditComEntity.setType(ConditionType.INPUT);
/* 241 */     weaTableEditComEntity.setViewAttr(paramBoolean1 ? 1 : 2);
/* 242 */     if (paramBoolean2) {
/* 243 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 244 */       hashMap.put("inputType", "multilang");
/* 245 */       hashMap.put("isBase64", Boolean.valueOf(true));
/* 246 */       weaTableEditComEntity.setOtherParams(hashMap);
/*     */     } 
/* 248 */     arrayList.add(weaTableEditComEntity);
/* 249 */     return arrayList;
/*     */   }
/*     */   
/*     */   protected List<WeaTableEditComEntity> getInputCom(String paramString, boolean paramBoolean) {
/* 253 */     return getInputCom(paramString, paramBoolean, false);
/*     */   }
/*     */   
/*     */   protected Map<String, String> getFieldType(int paramInt, boolean paramBoolean) {
/* 257 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 258 */     String str1 = paramBoolean ? "WORKFLOW_FORMDICTDETAIL" : "WORKFLOW_FORMDICT";
/* 259 */     String str2 = " SELECT * FROM " + str1 + " WHERE ID = ? ";
/* 260 */     RecordSet recordSet = new RecordSet();
/* 261 */     recordSet.executeQuery(str2, new Object[] { Integer.valueOf(paramInt) });
/* 262 */     if (recordSet.next()) {
/* 263 */       hashMap.put("htmlType", recordSet.getString("fieldhtmltype"));
/* 264 */       hashMap.put("type", recordSet.getString("type"));
/*     */     } 
/* 266 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public GetOldFormFieldTableInfoCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetOldFormFieldTableInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */