/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.FormFieldSetBiz;
/*    */ import com.engine.workflow.biz.SelectFieldItemBiz;
/*    */ import com.engine.workflow.entity.FormFieldSetEntity;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoSaveSelectItemsCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoSaveSelectItemsCmd() {}
/*    */   
/*    */   public DoSaveSelectItemsCmd(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.params = paramMap;
/* 29 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 34 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 40 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 41 */     String str1 = Util.null2String(this.params.get("fieldId"));
/* 42 */     String str2 = Util.null2String(this.params.get("typeDatas"));
/* 43 */     FormFieldSetBiz formFieldSetBiz = new FormFieldSetBiz(i, 1, this.user);
/*    */     
/* 45 */     FormFieldSetEntity formFieldSetEntity = new FormFieldSetEntity();
/* 46 */     SelectFieldItemBiz selectFieldItemBiz = new SelectFieldItemBiz();
/* 47 */     formFieldSetEntity.setFieldId(str1);
/* 48 */     formFieldSetEntity = formFieldSetBiz.resolveFieldType(formFieldSetEntity, str2);
/* 49 */     selectFieldItemBiz.saveSelectItem(formFieldSetEntity, 1);
/*    */ 
/*    */     
/* 52 */     if (!"".equals(formFieldSetEntity.getFieldId())) {
/* 53 */       RecordSet recordSet = new RecordSet();
/* 54 */       recordSet.executeUpdate("update workflow_billfield set childfieldid = ? where id = ?", new Object[] { Integer.valueOf(formFieldSetEntity.getChildFieldId()), formFieldSetEntity.getFieldId() });
/*    */     } 
/*    */     
/* 57 */     hashMap.put("status", "success");
/* 58 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/DoSaveSelectItemsCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */