/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*     */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetBatchEditTabCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetBatchEditTabCmd() {}
/*     */   
/*     */   public GetBatchEditTabCmd(Map<String, Object> paramMap, User paramUser) {
/*  31 */     this.params = paramMap;
/*  32 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  37 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  43 */     hashMap.put("datas", getTabInfos());
/*  44 */     hashMap.put("columns", getColumns());
/*  45 */     hashMap.put("userLanguage", Integer.valueOf(this.user.getLanguage()));
/*  46 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected List<Map<String, Object>> getTabInfos() {
/*  51 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/*     */     
/*  53 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  54 */     RecordSet recordSet1 = new RecordSet();
/*     */ 
/*     */     
/*  57 */     String str1 = " select tablename from workflow_bill where id = ? ";
/*  58 */     String str2 = "";
/*  59 */     recordSet1.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/*  60 */     if (recordSet1.next()) {
/*  61 */       str2 = recordSet1.getString("tablename");
/*     */     }
/*  63 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  64 */     hashMap.put("groupid", "main");
/*  65 */     hashMap.put("title", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()));
/*  66 */     hashMap.put("viewcondition", Integer.valueOf(0));
/*  67 */     hashMap.put("showcount", Boolean.valueOf(true));
/*  68 */     hashMap.put("color", "#000000");
/*  69 */     hashMap.put("formName", str2);
/*  70 */     arrayList.add(hashMap);
/*     */ 
/*     */     
/*  73 */     String str3 = "select * from Workflow_billdetailtable where billid= ? order by orderid";
/*  74 */     recordSet1.executeQuery(str3, new Object[] { Integer.valueOf(i) });
/*  75 */     RecordSet recordSet2 = new RecordSet();
/*  76 */     byte b = 0;
/*  77 */     while (recordSet1.next()) {
/*  78 */       b++;
/*  79 */       String str4 = recordSet1.getString("tableName");
/*  80 */       String str5 = "dt_" + b;
/*  81 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  82 */       hashMap1.put("groupid", str5);
/*  83 */       hashMap1.put("title", SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b);
/*  84 */       hashMap1.put("viewcondition", Integer.valueOf(b));
/*  85 */       hashMap1.put("showcount", Boolean.valueOf(true));
/*  86 */       hashMap1.put("color", "#000000");
/*  87 */       hashMap1.put("formName", str4);
/*  88 */       String str6 = "";
/*  89 */       recordSet2.executeQuery("select browsermark from sap_multibrowser where mxformname = ? and mxformid = ? and isbill = ? ", new Object[] { str4, Integer.valueOf(i), Integer.valueOf(1) });
/*  90 */       if (recordSet2.next()) {
/*  91 */         str6 = recordSet2.getString("browsermark");
/*     */       }
/*  93 */       hashMap1.put("sapMark", str6);
/*  94 */       arrayList.add(hashMap1);
/*     */     } 
/*  96 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<WeaTableEditEntity> getColumns() {
/* 104 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */     
/* 106 */     WeaTableEditEntity weaTableEditEntity1 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "fieldLabelName", "20%", "1");
/* 107 */     weaTableEditEntity1.setUseRecord(true);
/* 108 */     weaTableEditEntity1.setClassName("wea-table-edit-fieldLabelName");
/* 109 */     arrayList.add(weaTableEditEntity1);
/*     */ 
/*     */     
/* 112 */     WeaTableEditEntity weaTableEditEntity2 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldName", "15%", "1");
/* 113 */     weaTableEditEntity2.setUseRecord(true);
/* 114 */     weaTableEditEntity2.setClassName("wea-table-edit-fieldName");
/* 115 */     arrayList.add(weaTableEditEntity2);
/*     */ 
/*     */     
/* 118 */     WeaTableEditEntity weaTableEditEntity3 = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "fieldType", "55%", "1");
/* 119 */     weaTableEditEntity3.setUseRecord(true);
/* 120 */     weaTableEditEntity3.setClassName("wea-table-edit-fieldType");
/* 121 */     arrayList.add(weaTableEditEntity3);
/*     */     
/* 123 */     boolean bool = "1".equals(Util.null2String(this.params.get("isFromMode")));
/* 124 */     if (!bool) {
/*     */       
/* 126 */       WeaTableEditEntity weaTableEditEntity = new WeaTableEditEntity(SystemEnv.getHtmlLabelName(517198, this.user.getLanguage()), "fieldgroupid", "10%", "1");
/* 127 */       weaTableEditEntity.setUseRecord(true);
/* 128 */       weaTableEditEntity.setClassName("wea-table-edit-fieldgroup");
/*     */       
/* 130 */       ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/* 131 */       int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 132 */       String str = Util.null2String(this.params.get("isBill"));
/* 133 */       List list = FieldGroupBiz.getFieldGroups(i, str);
/* 134 */       WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity();
/* 135 */       weaTableEditComEntity.setViewAttr(2);
/* 136 */       weaTableEditComEntity.setOptions(list);
/* 137 */       weaTableEditComEntity.setType(ConditionType.SELECT);
/* 138 */       weaTableEditComEntity.setKey("fieldgroupid");
/* 139 */       arrayList1.add(weaTableEditComEntity);
/*     */       
/* 141 */       weaTableEditEntity.setCom(arrayList1);
/*     */       
/* 143 */       arrayList.add(weaTableEditEntity);
/*     */     } 
/*     */     
/* 146 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetBatchEditTabCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */