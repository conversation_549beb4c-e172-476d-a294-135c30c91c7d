/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.bean.Checkboxpopedom;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.biz.wfPathSet.FieldGroupBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.SqlUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.UserWFOperateLevel;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetFieldGroupListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetFieldGroupListCmd(Map<String, Object> paramMap, User paramUser) {
/*  33 */     this.params = paramMap;
/*  34 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public GetFieldGroupListCmd() {}
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  43 */     int i = Util.getIntValue(Util.null2String(this.params.get("isBill")), -1);
/*  44 */     int j = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/*  45 */     boolean bool = (new ManageDetachComInfo()).isUseFmManageDetach() ? true : false;
/*  46 */     int k = UserWFOperateLevel.checkWfFormOperateLevel(bool, this.user, "FormManage:All", j, i);
/*  47 */     if (k < 0) {
/*  48 */       hashMap.put("noRight", Boolean.valueOf(true));
/*  49 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  52 */     String str1 = getTableString();
/*  53 */     String str2 = "00ee73b6-9497-42c5-9551-b151219c2e30_" + Util.getEncrypt(Util.getRandom());
/*  54 */     Util_TableMap.setVal(str2, str1);
/*  55 */     hashMap.put("sessionkey", str2);
/*  56 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  62 */     return null;
/*     */   }
/*     */   
/*     */   protected String getTableString() {
/*  66 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")), 0);
/*  67 */     String str1 = Util.null2String(this.params.get("isBill"));
/*  68 */     FieldGroupBiz.initSystemFieldGroup(i, str1);
/*     */     
/*  70 */     String str2 = Util.null2String(this.params.get("labelName"));
/*  71 */     String str3 = Util.null2String(this.params.get("htmlType"));
/*  72 */     String str4 = Util.null2String(this.params.get("type"));
/*     */     
/*  74 */     int j = Util.getIntValue(Util.null2String(this.params.get("groupId")), 0);
/*     */     
/*  76 */     String str5 = "Wf:form_editformfield";
/*  77 */     String str6 = "checkbox";
/*  78 */     String str7 = PageIdConst.getPageSize("Wf:form_editformfield", this.user.getUID());
/*     */     
/*  80 */     String str8 = " viewtype,dsporder,id";
/*  81 */     String str9 = "id";
/*  82 */     String str10 = "ASC";
/*  83 */     String str11 = "false";
/*  84 */     String str12 = "";
/*  85 */     if (!"".equals(str2)) {
/*  86 */       str12 = str12 + " where t.labelname like '%" + str2 + "%' ";
/*     */     }
/*  88 */     String str13 = " id,fieldname,labelname,viewtype,fieldhtmltype,type,dsporder,fieldgroupid,groupname ";
/*  89 */     String str14 = "";
/*  90 */     StringBuilder stringBuilder = new StringBuilder();
/*  91 */     stringBuilder.append(" (select a.fieldid as id, '' as fieldname,b.labelname,0 as viewtype,'0' as fieldhtmltype,0 as type,a.dsporder,a.fieldgroupid,c.groupname ");
/*  92 */     stringBuilder.append(" from workflow_systemfield_group a left join htmllabelInfo b on a.fieldlabel = b.indexid and b.languageid = ").append(this.user.getLanguage());
/*  93 */     stringBuilder.append(" left join workflow_formfield_group c on a.fieldgroupid = c.id where a.formid = ").append(i).append(" and a.isbill = '").append(str1).append("' and a.fieldgroupid = ").append(j);
/*  94 */     stringBuilder.append(" union ");
/*  95 */     if ("1".equals(str1)) {
/*  96 */       stringBuilder.append(" select  a.id,a.fieldname,b.labelname,a.viewtype,a.fieldhtmltype,a.type,a.dsporder, ").append(SqlUtil.nvl("a.fieldgroupid", "0")).append(" as fieldgroupid,c.groupname ");
/*  97 */       stringBuilder.append(" from workflow_billfield a left join htmllabelinfo b on a.fieldlabel = b.indexid and b.languageid = ").append(this.user.getLanguage());
/*  98 */       stringBuilder.append("  left join workflow_formfield_group c on a.fieldgroupid = c.id where billid = ").append(i).append(" and viewtype = '0' and a.fieldgroupid = ").append(j);
/*  99 */       stringBuilder.append(" ) t ");
/*     */     } else {
/* 101 */       stringBuilder.append(" select t2.id,t2.fieldname,t3.fieldlable as labelname,0 AS viewtype,t2.fieldhtmltype,t2.type,t1.fieldorder AS dsporder, ");
/* 102 */       stringBuilder.append(SqlUtil.nvl("t1.fieldgroupid", "0")).append(" as fieldgroupid,t4.groupname ");
/* 103 */       stringBuilder.append(" from workflow_formfield t1 left join workflow_formdict t2 on t1.fieldid = t2.id ");
/* 104 */       stringBuilder.append(" left join workflow_fieldlable t3 on t1.fieldid  = t3.fieldid and t3.formid  = t1.formid and t3.langurageid = ").append(this.user.getLanguage());
/* 105 */       stringBuilder.append(" left join workflow_formfield_group t4 on t1.fieldgroupid  =  t4.id ");
/* 106 */       stringBuilder.append(" where t1.formid= ").append(i).append(" and t1.fieldgroupid = ").append(j).append(" and (t1.isdetail <> '1'  OR t1.isdetail IS NULL)");
/* 107 */       stringBuilder.append(" ) t ");
/*     */     } 
/* 109 */     str14 = stringBuilder.toString();
/*     */ 
/*     */     
/* 112 */     List<SplitTableColBean> list = createColList(i, 1);
/* 113 */     Checkboxpopedom checkboxpopedom = createCheckboxpopedom();
/*     */ 
/*     */     
/* 116 */     SplitTableBean splitTableBean = new SplitTableBean();
/* 117 */     splitTableBean.setPageID(str5);
/* 118 */     splitTableBean.setPageUID("00ee73b6-9497-42c5-9551-b151219c2e30");
/* 119 */     splitTableBean.setTableType(str6);
/* 120 */     splitTableBean.setPagesize(str7);
/* 121 */     splitTableBean.setBackfields(str13);
/* 122 */     splitTableBean.setSqlform(str14);
/* 123 */     splitTableBean.setSqlwhere(Util.toHtmlForSplitPage(str12));
/* 124 */     splitTableBean.setSqlorderby(str8);
/* 125 */     splitTableBean.setSqlprimarykey(str9);
/* 126 */     splitTableBean.setSqlsortway(str10);
/* 127 */     splitTableBean.setSqlisdistinct(str11);
/*     */     
/* 129 */     splitTableBean.setCols(list);
/* 130 */     splitTableBean.setCheckboxpopedom(checkboxpopedom);
/*     */     
/* 132 */     return SplitTableUtil.getTableString(splitTableBean);
/*     */   }
/*     */   
/*     */   protected Checkboxpopedom createCheckboxpopedom() {
/* 136 */     Checkboxpopedom checkboxpopedom = new Checkboxpopedom();
/* 137 */     checkboxpopedom.setId("checkbox");
/* 138 */     checkboxpopedom.setPopedompara("");
/* 139 */     checkboxpopedom.setShowmethod("true");
/* 140 */     return checkboxpopedom;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<SplitTableColBean> createColList(int paramInt1, int paramInt2) {
/* 150 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */     
/* 152 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "labelname", "labelname");
/* 153 */     arrayList.add(splitTableColBean1);
/*     */     
/* 155 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldName", "fieldName");
/* 156 */     arrayList.add(splitTableColBean2);
/*     */     
/* 158 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(17997, this.user.getLanguage()), "viewtype", "viewtype", getClass().getName() + ".getViewType", "column:id+" + this.user.getLanguage());
/* 159 */     arrayList.add(splitTableColBean3);
/*     */     
/* 161 */     SplitTableColBean splitTableColBean4 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(687, this.user.getLanguage()), "fieldhtmltype", "fieldhtmltype", "weaver.general.FormFieldTransMethod.getHTMLType", this.user.getLanguage() + "+column:type");
/* 162 */     arrayList.add(splitTableColBean4);
/*     */     
/* 164 */     SplitTableColBean splitTableColBean5 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "type", "type", "weaver.general.FormFieldTransMethod.getFieldType", "column:fieldhtmltype+column:id+" + this.user.getLanguage());
/* 165 */     arrayList.add(splitTableColBean5);
/*     */     
/* 167 */     SplitTableColBean splitTableColBean6 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(15513, this.user.getLanguage()), "dsporder", "dsporder");
/* 168 */     arrayList.add(splitTableColBean6);
/*     */     
/* 170 */     SplitTableColBean splitTableColBean7 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(517198, this.user.getLanguage()), "groupname", "groupname");
/* 171 */     arrayList.add(splitTableColBean7);
/* 172 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getViewType(String paramString1, String paramString2) {
/* 177 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 178 */     int i = Util.getIntValue(arrayOfString[0]);
/* 179 */     int j = Util.getIntValue(arrayOfString[1], 7);
/* 180 */     if (i < 0) {
/* 181 */       return SystemEnv.getHtmlLabelName(28415, j);
/*     */     }
/* 183 */     return SystemEnv.getHtmlLabelName(21778, j);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname(String paramString1, String paramString2) {
/* 194 */     String str1 = LabelUtil.getMultiLangLabel(paramString1);
/* 195 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 196 */     String str2 = Util.null2String(arrayOfString[0]);
/* 197 */     String str3 = Util.null2String(arrayOfString[1]);
/* 198 */     int i = Util.getIntValue(Util.null2String(arrayOfString[2]));
/* 199 */     String str4 = Util.null2String(arrayOfString[3]);
/*     */     
/* 201 */     if (i > 0 && "1".equals(str3)) {
/* 202 */       return str1;
/*     */     }
/* 204 */     return "<a href=\"javascript:worflowFieldListUtil.editForm('" + str2 + "'," + str4 + ")\">" + str1 + "</a>";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetFieldGroupListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */