/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetAddFieldConditionCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  27 */   protected int formId = 0;
/*  28 */   protected int isBill = -1;
/*     */   
/*     */   protected boolean isSysForm = false;
/*     */   
/*     */   protected boolean isCustomForm = false;
/*     */   
/*     */   public GetAddFieldConditionCmd(Map<String, Object> paramMap, User paramUser) {
/*  35 */     this.params = paramMap;
/*  36 */     this.user = paramUser;
/*  37 */     this.formId = Util.getIntValue(Util.null2String(paramMap.get("formId")));
/*  38 */     this.isBill = Util.getIntValue(Util.null2String(paramMap.get("isBill")));
/*  39 */     this.isSysForm = (this.formId > 0 && this.isBill == 1);
/*  40 */     this.isCustomForm = (this.formId < 0 && this.isBill == 1);
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  45 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  50 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  51 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  52 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  54 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  55 */     hashMap2.put("items", getItemList());
/*  56 */     arrayList.add(hashMap2);
/*     */     
/*  58 */     hashMap1.put("conditioninfo", arrayList);
/*  59 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionItem> getItemList() {
/*  63 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  64 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  67 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15456, "fieldLabel");
/*  68 */     searchConditionItem1.setLabelcol(8);
/*  69 */     searchConditionItem1.setFieldcol(16);
/*  70 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/*  73 */     if (this.isCustomForm) {
/*  74 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 124937, "fieldName");
/*  75 */       searchConditionItem.setLabelcol(8);
/*  76 */       searchConditionItem.setFieldcol(16);
/*  77 */       arrayList.add(searchConditionItem);
/*     */     } 
/*     */ 
/*     */     
/*  81 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, 124937, "fieldTable");
/*  82 */     searchConditionItem2.setLabelcol(8);
/*  83 */     searchConditionItem2.setFieldcol(16);
/*  84 */     searchConditionItem2.setOptions(getFieldTableOptions());
/*  85 */     if (this.isSysForm) {
/*  86 */       searchConditionItem2.setViewAttr(1);
/*  87 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  88 */       hashMap.put("hasBorder", Boolean.valueOf(true));
/*  89 */       searchConditionItem2.setOtherParams(hashMap);
/*     */     } 
/*  91 */     arrayList.add(searchConditionItem2);
/*     */ 
/*     */     
/*  94 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.INPUT, 15513, "dspOrder");
/*  95 */     searchConditionItem3.setLabelcol(8);
/*  96 */     searchConditionItem3.setFieldcol(16);
/*  97 */     arrayList.add(searchConditionItem3);
/*     */     
/*  99 */     return arrayList;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionOption> getFieldTableOptions() {
/* 103 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 105 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()), true));
/* 106 */     return arrayList;
/*     */   }
/*     */   
/*     */   public GetAddFieldConditionCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetAddFieldConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */