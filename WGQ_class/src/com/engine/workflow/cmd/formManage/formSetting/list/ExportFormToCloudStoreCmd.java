/*     */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*     */ 
/*     */ import com.cloudstore.dev.util.EcodeUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.google.common.base.Strings;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.file.FileManage;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.system.SystemComInfo;
/*     */ import weaver.workflow.formexport.FormExportServices;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ExportFormToCloudStoreCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public ExportFormToCloudStoreCmd(Map<String, Object> paramMap, User paramUser) {
/*  31 */     this.params = paramMap;
/*  32 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  37 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     return formExport();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Map<String, Object> formExport() {
/*  52 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  53 */     int i = Util.getIntValue(Util.null2String(this.params.get("formid")), 0);
/*  54 */     int j = Util.getIntValue(Util.null2String(this.params.get("isbill")));
/*  55 */     String str1 = Util.null2String(this.params.get("title"));
/*  56 */     int k = (new LabelUtil()).getLabelId4Workflow(str1);
/*  57 */     String str2 = str1 + ".xml";
/*     */     
/*  59 */     String str3 = "workflowChart";
/*  60 */     String str4 = EcodeUtil.getModuleAppId(i, str3);
/*     */     
/*  62 */     FormExportServices formExportServices = new FormExportServices(k);
/*  63 */     String str5 = formExportServices.exportFormReturnXmlStr(i, j);
/*     */     
/*  65 */     String str6 = createXmlFile(str5, str2, str4);
/*     */     
/*  67 */     if (!Strings.isNullOrEmpty(str6)) {
/*  68 */       hashMap.put("demotype", str3);
/*  69 */       hashMap.put("title", str1);
/*  70 */       hashMap.put("appId", str4);
/*  71 */       hashMap.put("filePath", str6);
/*     */     } 
/*     */     
/*  74 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String createXmlFile(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/*  87 */       SystemComInfo systemComInfo = new SystemComInfo();
/*  88 */       String str1 = FileUpload.getCreateDir(systemComInfo.getFilesystem()) + "workflow";
/*  89 */       String str2 = str1 + File.separatorChar + paramString3 + File.separatorChar + "datas" + File.separatorChar;
/*  90 */       if (str2 != null) FileManage.createDir(str2); 
/*  91 */       str2 = str2 + paramString2;
/*  92 */       FileWriter fileWriter = new FileWriter(str2);
/*  93 */       fileWriter.write(paramString1);
/*  94 */       fileWriter.close();
/*  95 */       return str1;
/*  96 */     } catch (Exception exception) {
/*  97 */       exception.printStackTrace();
/*  98 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void copyImage(String paramString1, String paramString2) {
/* 109 */     FileInputStream fileInputStream = null;
/* 110 */     FileOutputStream fileOutputStream = null;
/*     */     try {
/* 112 */       fileInputStream = new FileInputStream(paramString1);
/* 113 */       fileOutputStream = new FileOutputStream(paramString2);
/* 114 */       int i = 0;
/* 115 */       byte[] arrayOfByte = new byte[1024];
/* 116 */       while ((i = fileInputStream.read(arrayOfByte)) != -1) {
/* 117 */         fileOutputStream.write(arrayOfByte, 0, i);
/*     */       }
/* 119 */     } catch (IOException iOException) {
/* 120 */       iOException.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 123 */         if (fileOutputStream != null) fileOutputStream.close(); 
/* 124 */         if (fileInputStream != null) fileInputStream.close(); 
/* 125 */       } catch (IOException iOException) {
/* 126 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/ExportFormToCloudStoreCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */