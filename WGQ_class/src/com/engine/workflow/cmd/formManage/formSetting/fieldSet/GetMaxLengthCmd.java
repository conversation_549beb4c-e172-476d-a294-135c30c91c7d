/*    */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetMaxLengthCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetMaxLengthCmd() {}
/*    */   
/*    */   public GetMaxLengthCmd(Map<String, Object> paramMap, User paramUser) {
/* 21 */     this.params = paramMap;
/* 22 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 32 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 40 */     String str1 = Util.null2String(this.params.get("fieldName"));
/* 41 */     String str2 = Util.null2String(this.params.get("tableName"));
/*    */     
/* 43 */     int i = 0;
/* 44 */     RecordSet recordSet = new RecordSet();
/* 45 */     if (recordSet.getDBType().equals("oracle")) {
/* 46 */       recordSet.executeQuery("select max(lengthb(" + str1 + ")) from " + str2, new Object[0]);
/* 47 */       if (recordSet.next()) {
/* 48 */         i = Util.getIntValue(recordSet.getString(1), 0);
/* 49 */         if (i != 0 && recordSet.getOrgindbtype().equalsIgnoreCase("gs")) {
/* 50 */           recordSet.executeQuery("select " + str1 + " from " + str2 + " limit 1", new Object[0]);
/* 51 */           i = recordSet.getData().getColumnDisplaySize(str1);
/*    */         } 
/*    */       } 
/* 54 */     } else if (recordSet.getDBType().equalsIgnoreCase("mysql")) {
/* 55 */       recordSet.executeQuery("select max(LENGTH(" + str1 + ")) from " + str2, new Object[0]);
/* 56 */       if (recordSet.next()) i = Util.getIntValue(recordSet.getString(1), 0);
/*    */     
/* 58 */     } else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 59 */       recordSet.executeQuery("select max(LENGTH(" + str1 + ")) from " + str2, new Object[0]);
/* 60 */       if (recordSet.next()) i = Util.getIntValue(recordSet.getString(1), 0);
/*    */     
/*    */     } else {
/* 63 */       recordSet.executeQuery("select max(datalength(" + str1 + ")) from " + str2, new Object[0]);
/* 64 */       if (recordSet.next()) i = Util.getIntValue(recordSet.getString(1), 0);
/*    */     
/*    */     } 
/* 67 */     hashMap.put("maxLength", Integer.valueOf(i));
/*    */     
/* 69 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/GetMaxLengthCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */