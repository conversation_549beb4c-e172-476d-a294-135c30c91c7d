/*     */ package com.engine.workflow.cmd.formManage.formSetting.list;
/*     */ 
/*     */ import com.api.browser.bean.Checkboxpopedom;
/*     */ import com.api.browser.bean.Operate;
/*     */ import com.api.browser.bean.Popedom;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.bean.SplitTableOperateBean;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.workflow.entity.LogInfoEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.LabelUtil;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetFormListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  44 */   private final String pageUID = "3dfbedf9-7e2a-441b-a280-54e34649bbeb";
/*  45 */   protected int operatelevel = 0;
/*     */ 
/*     */ 
/*     */   
/*     */   public GetFormListCmd(Map<String, Object> paramMap, User paramUser) {
/*  50 */     this.params = paramMap;
/*  51 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  56 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  61 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  63 */     boolean bool = (HrmUserVarify.checkUserRight("FORMMODEFORM:ALL", this.user) || HrmUserVarify.checkUserRight("FormManage:All", this.user)) ? true : false;
/*  64 */     if (bool) {
/*  65 */       String str1 = formatTableString();
/*  66 */       String str2 = "3dfbedf9-7e2a-441b-a280-54e34649bbeb_" + Util.getEncrypt(Util.getRandom());
/*  67 */       Util_TableMap.setVal(str2, str1);
/*  68 */       hashMap.put("sessionkey", str2);
/*  69 */       hashMap.put("logArray", getLogArray());
/*  70 */       hashMap.put("operatelevel", Integer.valueOf(this.operatelevel));
/*     */     } else {
/*  72 */       hashMap.put("noRight", Boolean.valueOf(true));
/*     */     } 
/*     */     
/*  75 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected List<LogInfoEntity> getLogArray() {
/*  80 */     ArrayList<LogInfoEntity> arrayList = new ArrayList();
/*     */     
/*  82 */     LogInfoEntity logInfoEntity1 = new LogInfoEntity();
/*  83 */     logInfoEntity1.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  84 */     logInfoEntity1.setLogSmallType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  85 */     arrayList.add(logInfoEntity1);
/*     */     
/*  87 */     LogInfoEntity logInfoEntity2 = new LogInfoEntity();
/*  88 */     logInfoEntity2.setLogType(BizLogType.WORKFLOW_ENGINE.getCode() + "");
/*  89 */     logInfoEntity2.setBelongType(BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM.getCode() + "");
/*  90 */     arrayList.add(logInfoEntity2);
/*     */     
/*  92 */     return arrayList;
/*     */   }
/*     */   
/*     */   protected String formatTableString() {
/*  96 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*     */     
/*  98 */     String str1 = Util.null2String(this.params.get("formType"));
/*     */     
/* 100 */     String str2 = Util.toScreenToEdit(Util.null2String(this.params.get("formnameForSearch")), this.user.getLanguage());
/* 101 */     String str3 = Util.toScreenToEdit(Util.null2String(this.params.get("formdecForSearch")), this.user.getLanguage());
/* 102 */     String str4 = Util.null2String(this.params.get("formtypeForSearch"));
/*     */     
/* 104 */     boolean bool = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/* 105 */     int i = -1;
/* 106 */     if (bool == true) {
/* 107 */       i = Util.getIntValue(Util.null2String(this.params.get("subCompanyId")), -1);
/* 108 */       if (i > 0) {
/* 109 */         this.operatelevel = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "FormManage:All", i);
/*     */       } else {
/* 111 */         int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FormManage:All", 2);
/* 112 */         if (null != arrayOfInt && arrayOfInt.length > 0) {
/* 113 */           this.operatelevel = 2;
/*     */         } else {
/* 115 */           arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FormManage:All", 1);
/* 116 */           if (null != arrayOfInt && arrayOfInt.length > 0) {
/* 117 */             this.operatelevel = 1;
/*     */           }
/*     */         }
/*     */       
/*     */       } 
/* 122 */     } else if (HrmUserVarify.checkUserRight("FormManage:All", this.user)) {
/* 123 */       this.operatelevel = 2;
/*     */     } 
/* 125 */     String str5 = "Wf:form_manageform";
/* 126 */     String str6 = "workflowFormListTable";
/* 127 */     String str7 = "checkbox";
/* 128 */     String str8 = PageIdConst.getPageSize("Wf:form_manageform", this.user.getUID());
/*     */     
/* 130 */     String str9 = "";
/*     */     
/* 132 */     if ("".equals(str1)) str1 = "allForm"; 
/* 133 */     if ("allForm".equals(str1)) {
/* 134 */       str9 = " where not exists (select 1 from ModeFormExtend b where a.id = b.formid and isvirtualform=1) ";
/* 135 */     } else if ("customForm".equals(str1)) {
/* 136 */       str9 = " where not exists (select 1 from ModeFormExtend b where a.id = b.formid and isvirtualform=1) and (isoldornew = 0 or id <0) ";
/* 137 */     } else if ("sysForm".equals(str1)) {
/* 138 */       str9 = " where isoldornew = 1 and id >0 ";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 144 */     if (bool == true) {
/* 145 */       if (i > 0) {
/* 146 */         str9 = str9 + " and subcompanyid = " + i + " ";
/*     */       }
/* 148 */       if (this.user.getUID() != 1) {
/* 149 */         String str = "";
/*     */         try {
/* 151 */           str = (new SubCompanyComInfo()).getRightSubCompany(this.user.getUID(), "FormManage:All", -1);
/* 152 */         } catch (Exception exception) {
/* 153 */           exception.printStackTrace();
/*     */         } 
/* 155 */         if (StringUtils.isNotBlank(str)) {
/* 156 */           str9 = str9 + " and (" + Util.getSubINClause(str, "subcompanyid", "IN") + ") ";
/*     */         } else {
/* 158 */           str9 = str9 + " and 1 = 2";
/*     */         }
/*     */       
/*     */       } 
/* 162 */     } else if (this.operatelevel <= 0) {
/* 163 */       str9 = " and 1 = 2 ";
/*     */     } 
/*     */ 
/*     */     
/* 167 */     if (!"".equals(str2)) {
/* 168 */       str9 = str9 + " and formname like '%" + str2 + "%' ";
/*     */     }
/*     */     
/* 171 */     if (!"".equals(str3)) {
/* 172 */       str9 = str9 + " and formdesc like '%" + str3 + "%' ";
/*     */     }
/*     */     
/* 175 */     if (!"".equals(str4)) {
/* 176 */       if ("1".equals(str4)) {
/* 177 */         str9 = str9 + " and (isoldornew = 0 or id <0) ";
/* 178 */       } else if ("2".equals(str4)) {
/* 179 */         str9 = str9 + " and isoldornew = 1 and id >0 ";
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 185 */     if (this.operatelevel == -1) {
/* 186 */       str9 = str9 + " and 1=2 ";
/*     */     }
/*     */     
/* 189 */     RecordSet recordSet = new RecordSet();
/* 190 */     String str10 = " formname,isoldornew,formid ";
/* 191 */     String str11 = "";
/* 192 */     if (recordSet.getDBType().toLowerCase().equals("mysql")) {
/* 193 */       str11 = str11 + " @rownum := @rownum +1 AS id, ";
/* 194 */     } else if (recordSet.getDBType().toLowerCase().equals("oracle")) {
/* 195 */       str11 = str11 + " rownum as id, ";
/*     */     } else {
/* 197 */       str11 = str11 + " ROW_NUMBER() OVER(ORDER BY formname, isoldornew, id) id, ";
/*     */     } 
/* 199 */     str11 = str11 + " id as formid,formname,formdesc,subcompanyid,isoldornew, (select 1 from ModeFormExtend b where a.id = b.formid and isvirtualform=1) as isvirtual ";
/* 200 */     String str12 = " view_workflowForm_selectAll a ";
/* 201 */     if (recordSet.getDBType().toLowerCase().equals("mysql")) {
/* 202 */       str12 = str12 + " ,(SELECT @rownum := 0) r ";
/*     */     }
/* 204 */     String str13 = "id";
/* 205 */     String str14 = "ASC";
/* 206 */     String str15 = "false";
/*     */ 
/*     */     
/* 209 */     SplitTableOperateBean splitTableOperateBean = createOperateBean(bool);
/* 210 */     List<SplitTableColBean> list = createColList(bool, this.operatelevel);
/* 211 */     Checkboxpopedom checkboxpopedom = createCheckboxpopedom(bool);
/*     */ 
/*     */     
/* 214 */     SplitTableBean splitTableBean = new SplitTableBean();
/* 215 */     splitTableBean.setPageID(str5);
/* 216 */     splitTableBean.setPageUID("3dfbedf9-7e2a-441b-a280-54e34649bbeb");
/* 217 */     splitTableBean.setTableType(str7);
/* 218 */     splitTableBean.setPagesize(str8);
/* 219 */     splitTableBean.setInstanceid(str6);
/* 220 */     splitTableBean.setBackfields(str11);
/* 221 */     splitTableBean.setSqlform(str12);
/* 222 */     splitTableBean.setSqlwhere(Util.toHtmlForSplitPage(str9));
/* 223 */     splitTableBean.setSqlorderby(str10);
/* 224 */     splitTableBean.setSqlprimarykey(str13);
/* 225 */     splitTableBean.setSqlsortway(str14);
/* 226 */     splitTableBean.setSqlisdistinct(str15);
/*     */     
/* 228 */     splitTableBean.setOperates(splitTableOperateBean);
/* 229 */     splitTableBean.setCols(list);
/* 230 */     splitTableBean.setCheckboxpopedom(checkboxpopedom);
/*     */     
/* 232 */     return SplitTableUtil.getTableString(splitTableBean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected List<SplitTableColBean> createColList(int paramInt1, int paramInt2) {
/* 240 */     String str1 = paramInt2 + "+0+column:isoldornew+" + this.user.getLanguage();
/* 241 */     String str2 = paramInt2 + "+1+column:isoldornew+" + this.user.getLanguage();
/* 242 */     String str3 = paramInt2 + "+2+column:isoldornew+" + this.user.getLanguage();
/*     */     
/* 244 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */     
/* 246 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15451, this.user.getLanguage()), "formname", "formname", getClass().getName() + ".getWFFormNameLink", "column:formid+column:isoldornew+column:isvirtual");
/* 247 */     arrayList.add(splitTableColBean1);
/*     */     
/* 249 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(18411, this.user.getLanguage()), "formid", null, getClass().getName() + ".getFormType", "column:isoldornew+" + this.user.getLanguage() + "+column:isvirtual");
/* 250 */     splitTableColBean2.setKey("formType");
/* 251 */     arrayList.add(splitTableColBean2);
/*     */     
/* 253 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15452, this.user.getLanguage()), "formdesc", "formdesc");
/* 254 */     arrayList.add(splitTableColBean3);
/*     */     
/* 256 */     SplitTableColBean splitTableColBean4 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(16450, this.user.getLanguage()), "formid", null, "weaver.workflow.exceldesign.FormTemplateManager.transFormTemplateLink", str1);
/* 257 */     splitTableColBean4.setKey("showTemp");
/* 258 */     arrayList.add(splitTableColBean4);
/*     */     
/* 260 */     SplitTableColBean splitTableColBean5 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(125554, this.user.getLanguage()), "formid", null, "weaver.workflow.exceldesign.FormTemplateManager.transFormTemplateLink", str3);
/* 261 */     splitTableColBean5.setKey("mobileTemp");
/* 262 */     arrayList.add(splitTableColBean5);
/*     */     
/* 264 */     SplitTableColBean splitTableColBean6 = new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(128952, this.user.getLanguage()), "formid", null, "weaver.workflow.exceldesign.FormTemplateManager.transFormTemplateLink", str2);
/* 265 */     splitTableColBean6.setKey("printTemp");
/* 266 */     arrayList.add(splitTableColBean6);
/*     */     
/* 268 */     if (paramInt1 == 1) {
/* 269 */       SplitTableColBean splitTableColBean = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname");
/* 270 */       arrayList.add(splitTableColBean);
/*     */     } 
/* 272 */     SplitTableColBean splitTableColBean7 = new SplitTableColBean("1%", " ", "isoldornew", null);
/* 273 */     splitTableColBean7.setHide("true");
/* 274 */     splitTableColBean7.setCustomCol("false");
/* 275 */     arrayList.add(splitTableColBean7);
/* 276 */     SplitTableColBean splitTableColBean8 = new SplitTableColBean("1%", " ", "formid", null);
/* 277 */     splitTableColBean8.setHide("true");
/* 278 */     splitTableColBean8.setKey("formid");
/* 279 */     splitTableColBean8.setCustomCol("false");
/* 280 */     arrayList.add(splitTableColBean8);
/* 281 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Checkboxpopedom createCheckboxpopedom(int paramInt) {
/* 290 */     Checkboxpopedom checkboxpopedom = new Checkboxpopedom();
/* 291 */     checkboxpopedom.setId("checkbox");
/* 292 */     checkboxpopedom.setPopedompara("column:formid+column:isoldornew+" + getAllRef() + "+column:subcompanyid+" + this.user.getUID() + "+" + paramInt);
/* 293 */     checkboxpopedom.setShowmethod("weaver.workflow.form.FormMainManager.getWfFormCheck");
/*     */     
/* 295 */     return checkboxpopedom;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected SplitTableOperateBean createOperateBean(int paramInt) {
/* 303 */     SplitTableOperateBean splitTableOperateBean = new SplitTableOperateBean();
/* 304 */     ArrayList<Operate> arrayList = new ArrayList();
/*     */     
/* 306 */     Popedom popedom = new Popedom();
/* 307 */     String str = "weaver.workflow.form.FormMainManager.getEditAndDel";
/* 308 */     popedom.setColumn("formid");
/* 309 */     popedom.setTransmethod(str);
/* 310 */     popedom.setOtherpara("column:formid+column:isoldornew+" + getAllRef() + "+column:subcompanyid+" + this.user.getUID() + "+" + paramInt);
/*     */ 
/*     */     
/* 313 */     Operate operate1 = new Operate();
/* 314 */     operate1.setHref("javascript:workflowFormSetUtil.editForm();");
/* 315 */     operate1.setText(SystemEnv.getHtmlLabelName(26473, this.user.getLanguage()));
/* 316 */     operate1.setOtherpara("column:formid+column:isoldornew");
/* 317 */     operate1.setTarget("_self");
/* 318 */     operate1.setIndex("0");
/*     */ 
/*     */     
/* 321 */     Operate operate2 = new Operate();
/* 322 */     operate2.setHref("javascript:workflowFormSetUtil.saveAs();");
/* 323 */     operate2.setText(SystemEnv.getHtmlLabelName(350, this.user.getLanguage()));
/* 324 */     operate2.setTarget("_self");
/* 325 */     operate2.setOtherpara("column:formid");
/* 326 */     operate2.setIndex("2");
/*     */     
/* 328 */     Operate operate3 = new Operate();
/* 329 */     operate3.setHref("javascript:workflowFormSetUtil.syncFieldOrder();");
/* 330 */     operate3.setText(SystemEnv.getHtmlLabelName(523476, this.user.getLanguage()));
/* 331 */     operate3.setTarget("_self");
/* 332 */     operate3.setOtherpara("column:formid+column:isoldornew");
/* 333 */     operate3.setIndex("5");
/*     */ 
/*     */     
/* 336 */     Operate operate4 = new Operate();
/* 337 */     operate4.setHref("javascript:workflowFormSetUtil.exportForm();");
/* 338 */     operate4.setOtherpara("column:formid+column:isoldornew");
/* 339 */     operate4.setText(SystemEnv.getHtmlLabelName(17416, this.user.getLanguage()));
/* 340 */     operate4.setTarget("_self");
/* 341 */     operate4.setIndex("3");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 351 */     Operate operate5 = new Operate();
/* 352 */     operate5.setHref("javascript:workflowFormSetUtil.openLog();");
/*     */     
/* 354 */     operate5.setText(SystemEnv.getHtmlLabelName(83, this.user.getLanguage()));
/* 355 */     operate5.setTarget("_self");
/* 356 */     operate5.setOtherpara("column:formid");
/* 357 */     operate5.setIndex("4");
/*     */ 
/*     */     
/* 360 */     Operate operate6 = new Operate();
/* 361 */     operate6.setHref("javascript:workflowFormSetUtil.del();");
/* 362 */     operate6.setOtherpara("column:formid+column:isoldornew");
/* 363 */     operate6.setText(SystemEnv.getHtmlLabelName(23777, this.user.getLanguage()));
/* 364 */     operate6.setTarget("_self");
/* 365 */     operate6.setIndex("1");
/*     */     
/* 367 */     arrayList.add(operate1);
/* 368 */     arrayList.add(operate2);
/* 369 */     arrayList.add(operate3);
/* 370 */     arrayList.add(operate4);
/*     */     
/* 372 */     arrayList.add(operate6);
/* 373 */     arrayList.add(operate5);
/*     */ 
/*     */     
/* 376 */     splitTableOperateBean.setPopedom(popedom);
/* 377 */     splitTableOperateBean.setOperate(arrayList);
/*     */     
/* 379 */     return splitTableOperateBean;
/*     */   }
/*     */   
/*     */   protected String getAllRef() {
/* 383 */     String str = "";
/* 384 */     RecordSet recordSet = new RecordSet();
/* 385 */     recordSet.executeQuery("SELECT DISTINCT formid FROM workflow_base where isbill='0' ", new Object[0]);
/* 386 */     while (recordSet.next()) {
/* 387 */       str = str + "," + recordSet.getString(1);
/*     */     }
/* 389 */     if (!"".equals(str)) str = str + ","; 
/* 390 */     if ("".equals(str)) str = "-1"; 
/* 391 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFFormNameLink(String paramString1, String paramString2) {
/* 401 */     String str1 = "";
/* 402 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 403 */     int i = Util.getIntValue(arrayOfString[0]);
/* 404 */     String str2 = Util.null2String(arrayOfString[1]);
/* 405 */     String str3 = Util.null2String(arrayOfString[2]);
/* 406 */     if ("1".equals(str2)) {
/* 407 */       RecordSet recordSet = new RecordSet();
/* 408 */       recordSet.executeQuery("select namelabel from workflow_bill where id = ?", new Object[] { Integer.valueOf(i) });
/* 409 */       if (recordSet.next()) {
/* 410 */         paramString1 = LabelUtil.getMultiLangLabel(recordSet.getString("namelabel"));
/*     */       }
/*     */     } 
/* 413 */     paramString1 = paramString1.replaceAll("<", "＜").replaceAll(">", "＞").replaceAll("'", "''");
/* 414 */     str1 = "<a href=\"javascript:workflowFormSetUtil.editForm('','" + i + "+" + str2 + "')\">" + paramString1 + "</a>";
/* 415 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormType(String paramString1, String paramString2) {
/* 425 */     String str1 = "";
/* 426 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 427 */     String str2 = Util.null2String(arrayOfString[0]);
/* 428 */     int i = Util.getIntValue(arrayOfString[1]);
/* 429 */     String str3 = Util.null2String(arrayOfString[2]);
/* 430 */     if ("1".equals(str3)) {
/* 431 */       str1 = SystemEnv.getHtmlLabelName(33885, i);
/* 432 */     } else if ("0".equals(str2)) {
/* 433 */       str1 = SystemEnv.getHtmlLabelName(125026, i);
/*     */     }
/* 435 */     else if (Util.getIntValue(paramString1) > 0) {
/* 436 */       str1 = SystemEnv.getHtmlLabelName(125027, i);
/*     */     } else {
/* 438 */       str1 = SystemEnv.getHtmlLabelName(125026, i);
/*     */     } 
/*     */     
/* 441 */     return str1;
/*     */   }
/*     */   
/*     */   public GetFormListCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/list/GetFormListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */