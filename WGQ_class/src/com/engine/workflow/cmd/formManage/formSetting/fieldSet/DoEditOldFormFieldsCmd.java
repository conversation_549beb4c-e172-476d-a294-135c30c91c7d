/*     */ package com.engine.workflow.cmd.formManage.formSetting.fieldSet;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.constant.BizLogOperateType;
/*     */ import com.engine.common.constant.BizLogSmallType;
/*     */ import com.engine.common.constant.BizLogSmallType4Workflow;
/*     */ import com.engine.common.constant.BizLogType;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.common.util.LogUtil;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.FieldCommon;
/*     */ import weaver.workflow.field.WorkflowSubwfFieldManager;
/*     */ import weaver.workflow.form.FormFieldMainManager;
/*     */ import weaver.workflow.form.FormFieldlabelMainManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoEditOldFormFieldsCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*  37 */   protected List<BizLogContext> logContexts = new ArrayList<>();
/*  38 */   protected Map<String, Map<String, Object>> oldValuesMap = new HashMap<>();
/*  39 */   protected Map<String, Map<String, Object>> newValuesMap = new HashMap<>();
/*  40 */   protected Map<String, Map<String, Object>> oldFieldLabelMap = new HashMap<>();
/*  41 */   protected Map<String, Map<String, Object>> newFieldLabelMap = new HashMap<>();
/*     */ 
/*     */ 
/*     */   
/*     */   public DoEditOldFormFieldsCmd(Map<String, Object> paramMap, User paramUser) {
/*  46 */     this.params = paramMap;
/*  47 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  52 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public List<BizLogContext> getLogContexts() {
/*  58 */     getNewValues();
/*  59 */     Date date = new Date();
/*  60 */     String str1 = Util.null2String(this.params.get("formId"));
/*  61 */     String str2 = " select formname from workflow_formbase where id = ? ";
/*  62 */     RecordSet recordSet = new RecordSet();
/*  63 */     recordSet.executeQuery(str2, new Object[] { str1 });
/*  64 */     String str3 = "";
/*  65 */     if (recordSet.next()) {
/*  66 */       str3 = recordSet.getString("formname");
/*     */     }
/*     */     
/*  69 */     BizLogContext bizLogContext1 = new BizLogContext();
/*  70 */     String str4 = bizLogContext1.createMainid();
/*  71 */     bizLogContext1.setDateObject(date);
/*  72 */     bizLogContext1.setUserid(this.user.getUID());
/*  73 */     bizLogContext1.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  74 */     bizLogContext1.setTargetId(str1);
/*  75 */     bizLogContext1.setTargetName(str3);
/*  76 */     bizLogContext1.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  77 */     bizLogContext1.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/*  78 */     bizLogContext1.setBelongTypeTargetId(str1);
/*  79 */     bizLogContext1.setBelongTypeTargetName(str3);
/*  80 */     bizLogContext1.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/*  81 */     bizLogContext1.setOperateType(BizLogOperateType.UPDATE);
/*  82 */     bizLogContext1.setOldValues(new HashMap<>());
/*  83 */     bizLogContext1.setNewValues(new HashMap<>());
/*  84 */     bizLogContext1.setMainId(str4);
/*  85 */     bizLogContext1.setParams(this.params);
/*  86 */     bizLogContext1.setDetail(false);
/*  87 */     bizLogContext1.setClientIp(Util.null2String(this.params.get("param_ip")));
/*  88 */     bizLogContext1.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005596, ThreadVarLanguage.getLang()) + "", new Object[0]));
/*     */ 
/*     */     
/*  91 */     BizLogContext bizLogContext2 = new BizLogContext();
/*  92 */     String str5 = bizLogContext2.createMainid();
/*  93 */     bizLogContext2.setDateObject(date);
/*  94 */     bizLogContext2.setUserid(this.user.getUID());
/*  95 */     bizLogContext2.setUsertype(Util.getIntValue(this.user.getLogintype()));
/*  96 */     bizLogContext2.setTargetId(str1);
/*  97 */     bizLogContext2.setTargetName(str3);
/*  98 */     bizLogContext2.setLogType(BizLogType.WORKFLOW_ENGINE);
/*  99 */     bizLogContext2.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 100 */     bizLogContext2.setBelongTypeTargetId(str1);
/* 101 */     bizLogContext2.setBelongTypeTargetName(str3);
/* 102 */     bizLogContext2.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 103 */     bizLogContext2.setOperateType(BizLogOperateType.DELETE);
/* 104 */     bizLogContext2.setOldValues(new HashMap<>());
/* 105 */     bizLogContext2.setNewValues(new HashMap<>());
/* 106 */     bizLogContext2.setMainId(str5);
/* 107 */     bizLogContext2.setParams(this.params);
/* 108 */     bizLogContext2.setDetail(false);
/* 109 */     bizLogContext2.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 110 */     bizLogContext2.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005596, ThreadVarLanguage.getLang()) + "", new Object[0]));
/*     */ 
/*     */     
/* 113 */     BizLogContext bizLogContext3 = new BizLogContext();
/* 114 */     String str6 = bizLogContext3.createMainid();
/* 115 */     bizLogContext3.setDateObject(date);
/* 116 */     bizLogContext3.setUserid(this.user.getUID());
/* 117 */     bizLogContext3.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 118 */     bizLogContext3.setTargetId(str1);
/* 119 */     bizLogContext3.setTargetName(str3);
/* 120 */     bizLogContext3.setLogType(BizLogType.WORKFLOW_ENGINE);
/* 121 */     bizLogContext3.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 122 */     bizLogContext3.setBelongTypeTargetId(str1);
/* 123 */     bizLogContext3.setBelongTypeTargetName(str3);
/* 124 */     bizLogContext3.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 125 */     bizLogContext3.setOperateType(BizLogOperateType.ADD);
/* 126 */     bizLogContext3.setOldValues(new HashMap<>());
/* 127 */     bizLogContext3.setNewValues(new HashMap<>());
/* 128 */     bizLogContext3.setMainId(str6);
/* 129 */     bizLogContext3.setParams(this.params);
/* 130 */     bizLogContext3.setDetail(false);
/* 131 */     bizLogContext3.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 132 */     bizLogContext3.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005596, ThreadVarLanguage.getLang()) + "", new Object[0]));
/*     */     
/* 134 */     boolean bool1 = false;
/* 135 */     boolean bool2 = false;
/* 136 */     boolean bool3 = false;
/*     */     
/* 138 */     Set<String> set1 = this.newValuesMap.keySet();
/* 139 */     for (String str7 : set1) {
/* 140 */       if (this.oldValuesMap.get(str7) == null) {
/*     */         
/* 142 */         Map map = this.newValuesMap.get(str7);
/* 143 */         String str10 = (String)map.get("FIELDID");
/* 144 */         String str11 = "";
/* 145 */         BizLogContext bizLogContext4 = new BizLogContext();
/* 146 */         bizLogContext4.setDateObject(date);
/* 147 */         bizLogContext4.setUserid(this.user.getUID());
/* 148 */         bizLogContext4.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 149 */         bizLogContext4.setTargetId(str10);
/* 150 */         bizLogContext4.setTargetName(str11);
/* 151 */         bizLogContext4.setLogType(BizLogType.WORKFLOW_ENGINE);
/* 152 */         bizLogContext4.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 153 */         bizLogContext4.setBelongTypeTargetId(str1);
/* 154 */         bizLogContext4.setBelongTypeTargetName(str3);
/* 155 */         bizLogContext4.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 156 */         bizLogContext4.setOperateType(BizLogOperateType.ADD);
/* 157 */         bizLogContext4.setNewValues(map);
/* 158 */         bizLogContext4.setParams(this.params);
/* 159 */         bizLogContext4.setDetail(true);
/* 160 */         bizLogContext4.setBelongMainId(str6);
/* 161 */         bizLogContext4.setGroupId("0");
/* 162 */         bizLogContext4.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 163 */         bizLogContext4.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005585, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10005597, ThreadVarLanguage.getLang()) + "" + str11 + "(" + str10 + ")", new Object[0]));
/* 164 */         this.logContexts.add(bizLogContext4);
/* 165 */         bool1 = true;
/*     */         continue;
/*     */       } 
/* 168 */       Map<String, String> map1 = (Map)this.newValuesMap.get(str7);
/* 169 */       String str8 = (String)map1.get("FIELDID");
/* 170 */       String str9 = "";
/* 171 */       BizLogContext bizLogContext = new BizLogContext();
/* 172 */       bizLogContext.setDateObject(date);
/* 173 */       bizLogContext.setUserid(this.user.getUID());
/* 174 */       bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 175 */       bizLogContext.setTargetId(str8);
/* 176 */       bizLogContext.setTargetName(str9);
/* 177 */       bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/* 178 */       bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 179 */       bizLogContext.setBelongTypeTargetId(str1);
/* 180 */       bizLogContext.setBelongTypeTargetName(str3);
/* 181 */       bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 182 */       bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/* 183 */       Map<String, String> map2 = (Map)this.oldValuesMap.get(str7);
/* 184 */       LogUtil.removeIntersectionEntry(map2, map1);
/* 185 */       if (map2.size() == 0 || map1.size() == 0)
/* 186 */         continue;  map2.put("FIELDID", str8);
/* 187 */       map1.put("FIELDID", str8);
/* 188 */       bizLogContext.setNewValues(map1);
/* 189 */       bizLogContext.setOldValues(map2);
/* 190 */       bizLogContext.setParams(this.params);
/* 191 */       bizLogContext.setDetail(true);
/* 192 */       bizLogContext.setBelongMainId(str4);
/* 193 */       bizLogContext.setGroupId("0");
/* 194 */       bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 195 */       bizLogContext.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005600, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10005601, ThreadVarLanguage.getLang()) + "" + str9 + "(" + str8 + ")", new Object[0]));
/* 196 */       this.logContexts.add(bizLogContext);
/* 197 */       bool2 = true;
/*     */     } 
/*     */ 
/*     */     
/* 201 */     Set<String> set2 = this.oldValuesMap.keySet();
/* 202 */     for (String str : set2) {
/* 203 */       if (this.newValuesMap.get(str) == null) {
/*     */         
/* 205 */         Map map = this.oldValuesMap.get(str);
/* 206 */         String str7 = (String)map.get("FIELDID");
/* 207 */         String str8 = "";
/* 208 */         BizLogContext bizLogContext = new BizLogContext();
/* 209 */         bizLogContext.setDateObject(date);
/* 210 */         bizLogContext.setUserid(this.user.getUID());
/* 211 */         bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 212 */         bizLogContext.setTargetId(str7);
/* 213 */         bizLogContext.setTargetName(str8);
/* 214 */         bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/* 215 */         bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 216 */         bizLogContext.setBelongTypeTargetId(str1);
/* 217 */         bizLogContext.setBelongTypeTargetName(str3);
/* 218 */         bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 219 */         bizLogContext.setOperateType(BizLogOperateType.DELETE);
/* 220 */         bizLogContext.setOldValues(map);
/* 221 */         bizLogContext.setParams(this.params);
/* 222 */         bizLogContext.setDetail(true);
/* 223 */         bizLogContext.setBelongMainId(str5);
/* 224 */         bizLogContext.setGroupId("0");
/* 225 */         bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 226 */         bizLogContext.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(261, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005598, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10005599, ThreadVarLanguage.getLang()) + "" + str8 + "(" + str7 + ")", new Object[0]));
/* 227 */         this.logContexts.add(bizLogContext);
/* 228 */         bool3 = true;
/*     */       } 
/*     */     } 
/*     */     
/* 232 */     Set<String> set3 = this.newFieldLabelMap.keySet();
/* 233 */     for (String str7 : set3) {
/* 234 */       if (this.oldFieldLabelMap.get(str7) == null) {
/*     */         
/* 236 */         Map map = this.newFieldLabelMap.get(str7);
/* 237 */         String str10 = (String)map.get("FIELDID");
/* 238 */         String str11 = "";
/* 239 */         BizLogContext bizLogContext4 = new BizLogContext();
/* 240 */         bizLogContext4.setDateObject(date);
/* 241 */         bizLogContext4.setUserid(this.user.getUID());
/* 242 */         bizLogContext4.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 243 */         bizLogContext4.setTargetId(str10);
/* 244 */         bizLogContext4.setTargetName(str11);
/* 245 */         bizLogContext4.setLogType(BizLogType.WORKFLOW_ENGINE);
/* 246 */         bizLogContext4.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 247 */         bizLogContext4.setBelongTypeTargetId(str1);
/* 248 */         bizLogContext4.setBelongTypeTargetName(str3);
/* 249 */         bizLogContext4.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 250 */         bizLogContext4.setOperateType(BizLogOperateType.ADD);
/* 251 */         bizLogContext4.setNewValues(map);
/* 252 */         bizLogContext4.setParams(this.params);
/* 253 */         bizLogContext4.setDetail(true);
/* 254 */         bizLogContext4.setBelongMainId(str6);
/* 255 */         bizLogContext4.setGroupId("1");
/* 256 */         bizLogContext4.setGroupNameLabel(15456);
/* 257 */         bizLogContext4.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 258 */         bizLogContext4.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(15456, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005585, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10005597, ThreadVarLanguage.getLang()) + "" + str11 + "(" + str10 + ")", new Object[0]));
/* 259 */         this.logContexts.add(bizLogContext4);
/* 260 */         bool1 = true;
/*     */         continue;
/*     */       } 
/* 263 */       Map<String, String> map1 = (Map)this.newFieldLabelMap.get(str7);
/* 264 */       String str8 = (String)map1.get("FIELDID");
/* 265 */       String str9 = "";
/* 266 */       BizLogContext bizLogContext = new BizLogContext();
/* 267 */       bizLogContext.setDateObject(date);
/* 268 */       bizLogContext.setUserid(this.user.getUID());
/* 269 */       bizLogContext.setUsertype(Util.getIntValue(this.user.getLogintype()));
/* 270 */       bizLogContext.setTargetId(str8);
/* 271 */       bizLogContext.setTargetName(str9);
/* 272 */       bizLogContext.setLogType(BizLogType.WORKFLOW_ENGINE);
/* 273 */       bizLogContext.setLogSmallType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FIELD);
/* 274 */       bizLogContext.setBelongTypeTargetId(str1);
/* 275 */       bizLogContext.setBelongTypeTargetName(str3);
/* 276 */       bizLogContext.setBelongType((BizLogSmallType)BizLogSmallType4Workflow.WORKFLOW_ENGINE_FORMSET_FORM);
/* 277 */       bizLogContext.setOperateType(BizLogOperateType.UPDATE);
/* 278 */       Map<String, String> map2 = (Map)this.oldFieldLabelMap.get(str7);
/* 279 */       LogUtil.removeIntersectionEntry(map2, map1);
/* 280 */       if (map2.size() == 0 || map1.size() == 0)
/* 281 */         continue;  map2.put("FIELDID", str8);
/* 282 */       map1.put("FIELDID", str8);
/* 283 */       bizLogContext.setNewValues(map1);
/* 284 */       bizLogContext.setOldValues(map2);
/* 285 */       bizLogContext.setParams(this.params);
/* 286 */       bizLogContext.setDetail(true);
/* 287 */       bizLogContext.setBelongMainId(str4);
/* 288 */       bizLogContext.setGroupId("1");
/* 289 */       bizLogContext.setGroupNameLabel(15456);
/* 290 */       bizLogContext.setClientIp(Util.null2String(this.params.get("param_ip")));
/* 291 */       bizLogContext.setDesc(String.format(this.user.getLastname() + "" + SystemEnv.getHtmlLabelName(10005584, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(15456, ThreadVarLanguage.getLang()) + "]" + SystemEnv.getHtmlLabelName(10005600, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10005601, ThreadVarLanguage.getLang()) + "" + str9 + "(" + str8 + ")", new Object[0]));
/* 292 */       this.logContexts.add(bizLogContext);
/* 293 */       bool2 = true;
/*     */     } 
/*     */     
/* 296 */     if (bool1) this.logContexts.add(bizLogContext3); 
/* 297 */     if (bool2) this.logContexts.add(bizLogContext1); 
/* 298 */     if (bool3) this.logContexts.add(bizLogContext2);
/*     */     
/* 300 */     return this.logContexts;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getOldValues() {
/* 307 */     getValues(this.oldValuesMap, this.oldFieldLabelMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getNewValues() {
/* 314 */     getValues(this.newValuesMap, this.newFieldLabelMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void getValues(Map<String, Map<String, Object>> paramMap1, Map<String, Map<String, Object>> paramMap2) {
/* 321 */     getValues(" SELECT * FROM workflow_formfield WHERE formid = ? ", paramMap1);
/* 322 */     getValues("select * from workflow_fieldlable where formid = ?", paramMap2);
/*     */   }
/*     */   
/*     */   protected void getValues(String paramString, Map<String, Map<String, Object>> paramMap) {
/* 326 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 327 */     RecordSet recordSet = new RecordSet();
/* 328 */     recordSet.executeQuery(paramString, new Object[] { Integer.valueOf(i) });
/* 329 */     while (recordSet.next()) {
/* 330 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 331 */       String[] arrayOfString = recordSet.getColumnName();
/* 332 */       for (String str : arrayOfString) {
/* 333 */         hashMap.put(str.toUpperCase(), recordSet.getString(str));
/*     */       }
/* 335 */       paramMap.put(i + "_" + recordSet.getString("fieldid"), hashMap);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 342 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 344 */     getOldValues();
/*     */     
/* 346 */     FormFieldMainManager formFieldMainManager = new FormFieldMainManager();
/* 347 */     WorkflowSubwfFieldManager workflowSubwfFieldManager = new WorkflowSubwfFieldManager();
/* 348 */     FieldCommon fieldCommon = new FieldCommon();
/* 349 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 351 */     int i = Util.getIntValue(Util.null2String(this.params.get("formId")));
/* 352 */     int j = Util.getIntValue(Util.null2String(this.params.get("tableNum")));
/* 353 */     String str1 = Util.null2String(this.params.get("fieldIds_0"));
/* 354 */     String str2 = Util.null2String(this.params.get("allDetailFieldIds"));
/*     */     
/*     */     try {
/* 357 */       formFieldMainManager.resetParameter();
/* 358 */       formFieldMainManager.setGroupId(0);
/* 359 */       formFieldMainManager.setFormid(i);
/*     */ 
/*     */       
/* 362 */       if (formFieldMainManager.checkByRef(str1, str2)) {
/* 363 */         hashMap.put("message", "hasRule");
/* 364 */         return (Map)hashMap;
/*     */       } 
/*     */ 
/*     */       
/* 368 */       if (workflowSubwfFieldManager.hasSubWfSettingForForm(str1 + "," + str2, i, 0)) {
/* 369 */         hashMap.put("message", "hasChildWf");
/* 370 */         return (Map)hashMap;
/*     */       } 
/*     */ 
/*     */       
/* 374 */       formFieldMainManager.deleteCodeRegulate(str1, i + "");
/*     */ 
/*     */       
/* 377 */       formFieldMainManager.deleteFormfield(str1);
/* 378 */       formFieldMainManager.saveFormfield(str1);
/*     */ 
/*     */       
/* 381 */       formFieldMainManager.deleteAllDetailFormfield();
/*     */ 
/*     */       
/* 384 */       String str3 = Util.null2String(this.params.get("fieldIds_1"));
/* 385 */       String str4 = Util.null2String(this.params.get("newSapMultiBrowserValue_1"));
/* 386 */       formFieldMainManager.setGroupId(0);
/* 387 */       formFieldMainManager.deleteDetailFormfield(str3);
/* 388 */       formFieldMainManager.saveDetailFormfield(str3);
/* 389 */       if (!"".equals(str4)) {
/* 390 */         recordSet.executeQuery("select * from sap_multiBrowser where mxformname='0' and mxformid= ? ", new Object[] { Integer.valueOf(i) });
/* 391 */         if (!recordSet.next()) {
/* 392 */           String str = " insert  into  sap_multiBrowser (mxformname,mxformid,browsermark,isbill) values(?,?,?,?)";
/* 393 */           recordSet.executeUpdate(str, new Object[] { "0", "" + i, str4, "0" });
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 398 */       recordSet.executeUpdate("delete from  sap_multiBrowser where mxformid= ? and mxformname!='0' and isbill=0", new Object[] { Integer.valueOf(i) });
/*     */       
/* 400 */       for (byte b1 = 1; b1 < j - 1; b1++) {
/* 401 */         String str5 = Util.null2String(this.params.get("fieldIds_" + (b1 + 1)));
/* 402 */         String str6 = Util.null2String(this.params.get("newSapMultiBrowserValue_" + (b1 + 1)));
/* 403 */         formFieldMainManager.setGroupId(b1);
/* 404 */         formFieldMainManager.deleteDetailFormfield(str5);
/* 405 */         formFieldMainManager.saveDetailFormfield(str5);
/* 406 */         if (!"".equals(str6)) {
/* 407 */           String str = " insert  into  sap_multiBrowser (mxformname,mxformid,browsermark,isbill) values(?,?,?,?)";
/* 408 */           recordSet.executeUpdate(str, new Object[] { b1 + "", i + "", str6, "0" });
/*     */         } 
/*     */       } 
/*     */       
/* 412 */       fieldCommon.initNewFieldIsView("" + i, Util.TokenizerString2(str1, ","), Util.TokenizerString2(str2, ","));
/*     */ 
/*     */       
/* 415 */       formFieldMainManager.deleteNodefield();
/*     */ 
/*     */       
/* 418 */       formFieldMainManager.deleteDetailFilterSet();
/*     */ 
/*     */       
/* 421 */       FormFieldlabelMainManager formFieldlabelMainManager = new FormFieldlabelMainManager();
/* 422 */       int k = Util.getIntValue(Util.null2String(this.params.get("isdefault")));
/* 423 */       if (k == -1) k = this.user.getLanguage();
/*     */       
/* 425 */       int m = Util.getIntValue(Util.null2String(this.params.get("length")));
/*     */ 
/*     */       
/* 428 */       formFieldlabelMainManager.setFormid(i);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 433 */       for (byte b2 = 0; b2 < m; b2++) {
/* 434 */         int n = Util.getIntValue(Util.null2String(this.params.get("fieldId_" + b2)));
/* 435 */         String str = Util.null2String(this.params.get("fieldLabel_" + b2));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 444 */         for (byte b = 0; b < Util.getActiveLanguageIds().size(); b++) {
/* 445 */           int i1 = Util.getIntValue(Util.getActiveLanguageIds().get(b));
/* 446 */           String str5 = "0";
/* 447 */           if (i1 < 1)
/*     */             break; 
/* 449 */           if (i1 == k)
/* 450 */             str5 = "1"; 
/* 451 */           formFieldlabelMainManager.resetParameter();
/* 452 */           formFieldlabelMainManager.setFormid(i);
/* 453 */           formFieldlabelMainManager.setFieldid(n);
/* 454 */           formFieldlabelMainManager.setLanguageid(i1);
/* 455 */           formFieldlabelMainManager.setFieldlabel(Util.formatMultiLang(str, i1 + ""));
/* 456 */           formFieldlabelMainManager.setIsdefault(str5);
/* 457 */           formFieldlabelMainManager.saveFormfieldlabel();
/*     */         } 
/*     */       } 
/*     */       
/* 461 */       hashMap.put("message", "success");
/* 462 */     } catch (Exception exception) {
/* 463 */       exception.printStackTrace();
/* 464 */       hashMap.put("message", "failed");
/*     */     } 
/*     */     
/* 467 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   protected void saveFieldLabel() {}
/*     */   
/*     */   public DoEditOldFormFieldsCmd() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/formManage/formSetting/fieldSet/DoEditOldFormFieldsCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */