/*     */ package com.engine.workflow.cmd.emAppCount;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import com.engine.workflow.biz.mobileCenter.WorkflowCenterTabBiz;
/*     */ import com.engine.workflow.biz.requestList.RequestAttentionBiz;
/*     */ import com.engine.workflow.util.CommonUtil;
/*     */ import com.engine.workflow.util.PortalRequestListUtil;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.request.todo.OfsSettingObject;
/*     */ import weaver.workflow.request.todo.RequestUtil;
/*     */ 
/*     */ 
/*     */ public class EmWfCenterCountInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private String menuidNew;
/*     */   
/*     */   public EmWfCenterCountInfoCmd(String paramString, User paramUser) {
/*  29 */     this.menuidNew = paramString;
/*  30 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  36 */     String str1 = CommonUtil.getDBJudgeNullFun((new RecordSet()).getDBType());
/*  37 */     int i = 0;
/*  38 */     boolean bool1 = false;
/*  39 */     boolean bool2 = HrmClassifiedProtectionBiz.isOpenClassification();
/*  40 */     String str2 = (new HrmClassifiedProtectionBiz()).getMaxResourceSecLevel(this.user);
/*  41 */     String str3 = CommonUtil.getAllUserid(this.user);
/*  42 */     boolean bool3 = "2".equals(this.user.getLogintype()) ? true : false;
/*  43 */     String str4 = " select distinct ";
/*  44 */     String str5 = " t1.createdate,t1.createtime,t1.creater,t1.currentnodeid,t1.currentnodetype,t1.lastoperator,t1.creatertype,t1.lastoperatortype,t1.lastoperatedate,t1.lastoperatetime,t1.requestid,t1.requestname,t1.requestlevel,t1.workflowid,t2.receivedate,t2.receivetime, t2.userid";
/*  45 */     String str6 = " from workflow_requestbase t1,workflow_currentoperator t2,workflow_base t3 ";
/*  46 */     String str7 = " where t1.requestid=t2.requestid and t2.islasttimes = 1 and t1.workflowid = t3.id and t2.userid in (" + str3 + ") and t2.usertype = '" + bool3 + "' and (t1.deleted <> 1 or t1.deleted is null or t1.deleted = '')  ";
/*  47 */     str7 = str7 + " and (" + str1 + "(t1.currentstatus,-1) = -1 or (" + str1 + "(t1.currentstatus,-1)=0 and t1.creater in (" + str3 + "))) ";
/*     */     
/*  49 */     String str8 = " select createdate,createtime,creatorid as creater,-1 as currentnodeid,'' as currentnodetype,-1 as lastoperator,0 as creatertype,0 as lastoperatortype,'' as lastoperatedate,'' as lastoperatetime,requestid,requestname,0 as requestlevel,workflowid,receivedate,receivetime,userid from ofs_todo_data ";
/*     */ 
/*     */     
/*  52 */     String str9 = " select createdate,createtime,creatorid as creater,-1 as currentnodeid,'' as currentnodetype,-1 as lastoperator,0 as creatertype,0 as lastoperatortype,'' as lastoperatedate,'' as lastoperatetime,requestid,requestname,0 as requestlevel,workflowid,receivedate,receivetime,userid from ofs_done_data ";
/*     */     
/*  54 */     boolean bool4 = false;
/*  55 */     boolean bool5 = false;
/*  56 */     String str10 = "";
/*  57 */     String str11 = "";
/*  58 */     String str12 = "0";
/*     */     
/*  60 */     RecordSet recordSet = new RecordSet();
/*  61 */     recordSet.executeQuery("select id,viewtype,cornercountType from workflow_mobilecenter_tabinfo where menuid = ? and iscorner = '1' ", new Object[] { this.menuidNew });
/*  62 */     if (recordSet.next()) {
/*  63 */       str10 = recordSet.getString("id");
/*  64 */       str11 = recordSet.getString("viewtype");
/*  65 */       str12 = Util.null2String(recordSet.getString("cornercountType"));
/*     */     }
/*  67 */     else if (WorkflowCenterTabBiz.isInitTab(Util.getIntValue(this.menuidNew))) {
/*  68 */       recordSet.executeQuery("select id,viewtype from workflow_mobilecenter_tabinfo where menuid = ?  order by orderno", new Object[] { this.menuidNew });
/*  69 */       if (recordSet.next()) {
/*  70 */         str10 = recordSet.getString("id");
/*  71 */         str11 = recordSet.getString("viewtype");
/*     */       } else {
/*  73 */         hashMap.put("count", Integer.valueOf(0));
/*  74 */         hashMap.put("unread", Integer.valueOf(0));
/*  75 */         return (Map)hashMap;
/*     */       } 
/*     */     } else {
/*  78 */       str10 = "-1";
/*  79 */       str11 = "1";
/*     */     } 
/*     */ 
/*     */     
/*  83 */     String str13 = WorkflowCenterTabBiz.getWfCenterTabWhere(str10, str11, this.menuidNew);
/*  84 */     if (str13 != null && !"".equals(str13)) {
/*  85 */       PortalRequestListUtil portalRequestListUtil = new PortalRequestListUtil();
/*  86 */       Map map = portalRequestListUtil.getPortalSqlWhere(str13, this.user.getUID() + "", bool3, this.user);
/*  87 */       (new BaseBean()).writeLog(JSONObject.toJSONString(map));
/*  88 */       String str = Util.null2String((String)map.get("whereclause"));
/*  89 */       if (!"".equals(str)) {
/*  90 */         if (str.trim().startsWith("and")) {
/*  91 */           str7 = str7 + " " + str;
/*     */         } else {
/*  93 */           str7 = str7 + " and " + str;
/*     */         } 
/*     */       }
/*     */     } 
/*  97 */     if ("1".equals(str12)) {
/*  98 */       str7 = str7 + " and t2.viewtype=0 ";
/*     */       
/* 100 */       str7 = str7 + " and (t2.isremark not in(5) and (((t2.isprocessed is null or t2.isprocessed = '') and (t2.isremark in('1','7','8','9','11') or (t2.isremark='0' and (t2.takisremark is null or t2.takisremark=0)))) or (t2.isremark in(2,4) or (t2.isremark=0 and t2.takisremark =-2)))) ";
/*     */     } 
/* 102 */     String str14 = "";
/* 103 */     String str15 = "";
/* 104 */     StringBuilder stringBuilder = new StringBuilder();
/* 105 */     if ("14".equals(str11)) {
/*     */       
/* 107 */       String str = "";
/* 108 */       if (str13 != null && !"".equals(str13)) {
/* 109 */         Map map1 = (Map)JSON.parseObject(str13, Map.class);
/* 110 */         Map map2 = WorkflowCenterTabBiz.getDefTabWhere(map1);
/* 111 */         if (map2 != null) {
/* 112 */           str = (String)map2.get("whereclause");
/*     */         }
/*     */       } 
/* 115 */       str14 = " t1.createdate,t1.createtime,t1.creater,t1.currentnodeid,t1.currentnodetype,t1.lastoperator,t1.creatertype,t1.lastoperatortype,t1.lastoperatedate,t1.lastoperatetime,t1.requestid,t1.requestname,t1.requestlevel,t1.workflowid,'' as receivedate,'' as receivetime, sup.userid";
/* 116 */       str15 = "from workflow_requestbase t1 join workflow_superviseoperator sup on t1.requestid = sup.requestid join workflow_attention att on t1.requestid = att.requestid ";
/* 117 */       stringBuilder.append(" where att.userid = " + this.user.getUID() + " AND att.usertype=" + bool3 + " ");
/* 118 */       stringBuilder.append(" and sup.userid = " + this.user.getUID() + " and sup.usertype=" + bool3 + " ");
/* 119 */       if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 120 */         stringBuilder.append(" and (t1.deleted <> 1 or t1.deleted is null) ");
/*     */       } else {
/* 122 */         stringBuilder.append(" and (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') ");
/* 123 */       }  stringBuilder.append(" and att.id in (SELECT MAX(id) FROM workflow_attention WHERE requestid>0 and userid=" + this.user.getUID() + " AND usertype=" + bool3 + " " + RequestAttentionBiz.getAtypeSqlwhere("", "") + " GROUP BY requestid)");
/* 124 */       stringBuilder.append(" and exists (select 1 from workflow_base where id = t1.workflowid and isvalid in (1,3)) ");
/* 125 */       stringBuilder.append(RequestAttentionBiz.getAtypeSqlwhere("att", ""));
/* 126 */       if (bool2) {
/* 127 */         stringBuilder.append(" and t1.seclevel >= " + str2 + " ");
/*     */       }
/* 129 */       stringBuilder.append(" and (" + str1 + "(t1.currentstatus,-1) = -1 or (" + str1 + "(t1.currentstatus,-1)=0 and t1.creater=" + this.user.getUID() + ")) ");
/* 130 */       stringBuilder.append(" and not exists (select 1 from workflow_currentoperator c where c.requestid = t1.requestid and  c.userid = " + this.user.getUID() + " and c.usertype=" + bool3 + " )");
/*     */       
/* 132 */       if (!"".equals(str)) {
/* 133 */         stringBuilder.append(str.replaceAll("t2.", "sup."));
/*     */       }
/* 135 */       if ("1".equals(str12)) {
/* 136 */         str15 = str15 + " left join (select id, max(viewdate) as viewdate, max(viewtime) as viewtime from workflow_requestviewlog where viewer = " + this.user.getUID() + " group by id) wrl on t1.requestid = wrl.id ";
/* 137 */         stringBuilder.append(" and (t1.lastoperator <> ").append(this.user.getUID()).append(" or t1.lastoperatortype <> ").append(bool3).append(")");
/* 138 */         stringBuilder.append(" and (wrl.viewdate is null or ");
/* 139 */         String str17 = "";
/* 140 */         String str18 = "";
/* 141 */         if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/* 142 */           str17 = "wrl.viewdate||' ' ||wrl.viewtime";
/* 143 */           str18 = "t1.lastoperatedate|| ' ' ||t1.lastoperatetime";
/* 144 */         } else if (recordSet.getDBType().equals("mysql")) {
/* 145 */           str17 = "concat(wrl.viewdate,' ',wrl.viewtime)";
/* 146 */           str18 = "concat(t1.lastoperatedate,' ',t1.lastoperatetime)";
/* 147 */         } else if (recordSet.getDBType().equals("sqlserver")) {
/* 148 */           str17 = "wrl.viewdate + ' ' + wrl.viewtime";
/* 149 */           str18 = "t1.lastoperatedate + ' ' + t1.lastoperatetime";
/*     */         } 
/* 151 */         stringBuilder.append(str17).append(" < ").append(str18);
/* 152 */         stringBuilder.append(") ");
/*     */       } 
/*     */ 
/*     */       
/* 156 */       str6 = " from workflow_requestbase t1,workflow_currentoperator t2,workflow_attention att ";
/* 157 */       str7 = str7.replace("and t1.workflowid = t3.id", "and t1.requestid = att.requestid and att.userid = '" + this.user.getUID() + "' and att.usertype = 0");
/* 158 */       str7 = str7 + " and exists (select 1 from workflow_base where id = t1.workflowid and isvalid in (1,3)) ";
/*     */     } 
/*     */     
/* 161 */     RequestUtil requestUtil = new RequestUtil();
/* 162 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/* 163 */     boolean bool6 = (ofsSettingObject.getIsuse() == 1) ? true : false;
/* 164 */     boolean bool7 = ofsSettingObject.getShowdone().equals("1");
/* 165 */     if (bool6 && ("1".equals(str11) || "2".equals(str11) || "4".equals(str11) || "10".equals(str11) || ("3".equals(str11) && bool7) || "".equals(str11) || Util.getIntValue(str11) > 13 || "11".equals(str11) || "5".equals(str11))) {
/* 166 */       bool4 = true;
/* 167 */       bool5 = true;
/* 168 */       String str17 = " where 1 = 1 ";
/* 169 */       String str18 = " where 1 = 1 ";
/* 170 */       Map map = (new PortalRequestListUtil()).getPortalSqlOs(str13, str3, this.user);
/* 171 */       if (map != null) {
/* 172 */         String str19 = (String)map.get("whereclause_os");
/* 173 */         String str20 = (String)map.get("whereclause_osDone");
/* 174 */         if (!"".equals(str19)) {
/* 175 */           str17 = str17 + " " + str19;
/*     */         }
/* 177 */         if (!"".equals(str20)) {
/* 178 */           str18 = str18 + " " + str20;
/*     */         }
/*     */       } 
/* 181 */       if ("1".equals(str12)) {
/* 182 */         str17 = str17 + " and viewtype = '0' ";
/* 183 */         str18 = str18 + " and viewtype = '0' ";
/*     */       } 
/* 185 */       str8 = str8 + " " + str17;
/* 186 */       str9 = str9 + " " + str18;
/* 187 */       if ("1".equals(str11) || "11".equals(str11) || "5".equals(str11)) {
/* 188 */         bool5 = false;
/* 189 */       } else if ("2".equals(str11) || "3".equals(str11)) {
/* 190 */         bool4 = false;
/*     */       } 
/*     */     } 
/*     */     
/* 194 */     int j = 0;
/* 195 */     if ("14".equals(str11)) {
/* 196 */       String str = "select count(1) as totalcount from (" + str4 + " " + str14 + " " + str15 + " " + stringBuilder.toString() + ") t1";
/* 197 */       recordSet.executeQuery(str, new Object[0]);
/* 198 */       if (recordSet.next()) {
/* 199 */         j = recordSet.getInt(1);
/*     */       }
/*     */     } 
/* 202 */     String str16 = "select count(1) as totalcount from (" + str4 + str5 + str6 + str7 + (bool4 ? (" union all " + str8) : "") + (bool5 ? (" union all " + str9) : "") + ") tmp_base ";
/* 203 */     recordSet.executeQuery(str16, new Object[0]);
/* 204 */     if (recordSet.next()) {
/* 205 */       i = recordSet.getInt("totalcount");
/*     */     }
/* 207 */     hashMap.put("count", Integer.valueOf(i + j));
/* 208 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 213 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/emAppCount/EmWfCenterCountInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */