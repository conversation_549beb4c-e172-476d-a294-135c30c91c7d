/*     */ package com.engine.workflow.cmd.emAppCount;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import com.engine.workflow.biz.mobileCenter.MobileDimensionsBiz;
/*     */ import com.engine.workflow.biz.requestList.OfsRequestListBiz;
/*     */ import com.engine.workflow.util.CommonUtil;
/*     */ import com.engine.workflow.util.WorkflowDimensionUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.AllManagers;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ public class EmDoingCountInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>> {
/*     */   private HttpServletRequest request;
/*     */   private String menuidNew;
/*     */   
/*     */   public EmDoingCountInfoCmd(HttpServletRequest paramHttpServletRequest, User paramUser) {
/*  31 */     this.request = paramHttpServletRequest;
/*  32 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public EmDoingCountInfoCmd(String paramString, User paramUser) {
/*  36 */     this.menuidNew = paramString;
/*  37 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  42 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  43 */     hashMap2.put("ismobile", "1");
/*  44 */     boolean bool = HrmClassifiedProtectionBiz.isOpenClassification();
/*  45 */     String str1 = (new HrmClassifiedProtectionBiz()).getMaxResourceSecLevel(this.user);
/*     */     
/*  47 */     String str2 = "";
/*  48 */     if (this.request != null) {
/*  49 */       str2 = Util.null2String(this.request.getParameter("menuids"));
/*     */     } else {
/*  51 */       str2 = this.menuidNew;
/*     */     } 
/*  53 */     ArrayList arrayList = new ArrayList();
/*     */     
/*  55 */     List<String> list = Util.splitString2List(str2, ",");
/*  56 */     if (!list.contains("-1")) {
/*  57 */       list.add("-1");
/*     */     }
/*  59 */     list.remove("");
/*  60 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */     
/*  62 */     for (String str3 : list) {
/*  63 */       String str4 = "";
/*  64 */       int i = Util.getIntValue(str3, -1);
/*  65 */       if (i != -1) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  71 */         Map map = (new MobileDimensionsBiz()).getMobileRangeSql(i);
/*  72 */         str4 = (String)map.get("whereclause");
/*     */       } 
/*  74 */       hashMap3.put(str3, str4);
/*     */     } 
/*     */     try {
/*  77 */       if (this.user == null)
/*  78 */         return null; 
/*  79 */       RecordSet recordSet1 = new RecordSet();
/*  80 */       RecordSet recordSet2 = new RecordSet();
/*  81 */       String str3 = CommonUtil.getDBJudgeNullFun(recordSet1.getDBType());
/*  82 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  83 */       AllManagers allManagers = new AllManagers();
/*     */       
/*  85 */       String str4 = "";
/*  86 */       allManagers.getAll(str4);
/*  87 */       if ("".equals(str4))
/*  88 */         str4 = "" + this.user.getUID(); 
/*  89 */       boolean bool1 = false;
/*  90 */       if (str4.equals("" + this.user.getUID()))
/*  91 */         bool1 = true; 
/*  92 */       boolean bool2 = false;
/*  93 */       if ("2".equals(this.user.getLogintype()))
/*  94 */         bool2 = true; 
/*  95 */       String str5 = String.valueOf(this.user.getUID());
/*  96 */       String str6 = String.valueOf(this.user.getUID());
/*  97 */       String str7 = "";
/*  98 */       recordSet2.executeSql("select * from HrmUserSetting where resourceId = " + str5);
/*  99 */       if (recordSet2.next()) {
/* 100 */         str7 = recordSet2.getString("belongtoshow");
/*     */       }
/* 102 */       if (!bool1)
/* 103 */         str7 = ""; 
/* 104 */       if (!"".equals(this.user.getBelongtoids())) {
/* 105 */         str6 = str5 + "," + this.user.getBelongtoids();
/*     */       }
/* 107 */       boolean bool3 = false;
/* 108 */       if ("".equals(str4) || str5.equals(str4)) {
/* 109 */         str4 = str5;
/* 110 */         bool3 = true;
/*     */       } else {
/* 112 */         recordSet1.executeSql("SELECT * FROM HrmResource WHERE ID = " + str4 + " AND managerStr LIKE '%," + str5 + ",%'");
/* 113 */         if (recordSet1.next()) {
/* 114 */           bool3 = true;
/*     */         }
/*     */       } 
/* 117 */       StringBuffer stringBuffer = new StringBuffer();
/* 118 */       stringBuffer.append("select t2.workflowtype, t2.workflowid from workflow_currentoperator t2,workflow_requestbase t1");
/* 119 */       stringBuffer.append("\t  where t1.requestid=t2.requestid ");
/*     */       
/* 121 */       stringBuffer.append("   and (t2.isprocessing = '' or t2.isprocessing is null) ");
/* 122 */       stringBuffer.append("   and t2.islasttimes = 1 ");
/* 123 */       if ("1".equals(str7)) {
/* 124 */         stringBuffer.append("\t    and t2.userid in (").append(str6);
/*     */       } else {
/* 126 */         stringBuffer.append("\t    and t2.userid in (").append(str4);
/*     */       } 
/* 128 */       stringBuffer.append("  ) and t2.usertype = ").append(bool2);
/* 129 */       stringBuffer.append(" and  t2.workflowid in (select id from workflow_base )  ");
/*     */       
/* 131 */       if (recordSet1.getDBType().equalsIgnoreCase("postgresql")) {
/* 132 */         stringBuffer.append("  and (t1.deleted <> 1 or t1.deleted is null) ");
/*     */       } else {
/* 134 */         stringBuffer.append("  and (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') ");
/* 135 */       }  stringBuffer.append(" and (" + str3 + "(t1.currentstatus,-1) = -1 or (" + str3 + "(t1.currentstatus,-1)=0 and t1.creater=" + this.user.getUID() + ")) ");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 140 */       if (bool) {
/* 141 */         stringBuffer.append(" and t1.seclevel >= " + str1 + " ");
/*     */       }
/*     */       
/* 144 */       if (!bool3) {
/* 145 */         if ("1".equals(str7)) {
/* 146 */           stringBuffer.append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE t2.workflowid = b.workflowid AND t2.requestid = b.requestid AND b.userid in (" + str6 + ") and b.usertype= " + bool2 + ") ");
/*     */         } else {
/*     */           
/* 149 */           stringBuffer.append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE t2.workflowid = b.workflowid AND t2.requestid = b.requestid AND b.userid in (" + this.user
/* 150 */               .getUID() + ") and b.usertype= " + bool2 + ") ");
/*     */         } 
/*     */       }
/* 153 */       stringBuffer.append(" and ((t2.isremark = '0' and (t2.takisremark is null or t2.takisremark=0)) or t2.isremark in ('1','5','7','8','9','11') )");
/* 154 */       String str8 = stringBuffer.toString();
/*     */       
/* 156 */       for (String str9 : list) {
/* 157 */         String str10 = "";
/* 158 */         ArrayList<String> arrayList1 = new ArrayList();
/* 159 */         ArrayList<String> arrayList2 = new ArrayList();
/* 160 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 161 */         Hashtable<Object, Object> hashtable2 = new Hashtable<>();
/* 162 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 163 */         StringBuffer stringBuffer1 = new StringBuffer();
/*     */         
/* 165 */         stringBuffer1.append(str8);
/* 166 */         String str11 = WorkflowDimensionUtils.getEmAppSqlWhere(str9, "or", this.user);
/* 167 */         if (!"".equals(str11)) {
/* 168 */           stringBuffer1.append(" and (1=2 " + str11 + " ) ");
/*     */         }
/* 170 */         stringBuffer1.append((String)hashMap3.get(str9));
/* 171 */         stringBuffer1.append(" group by t2.workflowtype, t2.workflowid order by t2.workflowtype, t2.workflowid ");
/* 172 */         recordSet2.executeSql(stringBuffer1.toString());
/* 173 */         while (recordSet2.next()) {
/* 174 */           String str14 = Util.null2String(recordSet2.getString("workflowid"));
/* 175 */           String str15 = Util.null2String(recordSet2.getString("workflowtype"));
/* 176 */           RecordSet recordSet = new RecordSet();
/*     */           
/* 178 */           recordSet.executeQuery("select isvalid from workflow_base where id = ?", new Object[] { str14 });
/* 179 */           String str16 = "";
/* 180 */           if (recordSet.next())
/* 181 */             str16 = Util.null2String(recordSet.getString(1)); 
/* 182 */           if (!"1".equals(str16) && !"3".equals(str16))
/*     */             continue; 
/* 184 */           if (arrayList1.indexOf(str15) == -1)
/* 185 */             arrayList1.add(str15); 
/* 186 */           if (arrayList2.indexOf(str14) == -1)
/* 187 */             arrayList2.add(str14); 
/*     */         } 
/* 189 */         StringBuffer stringBuffer2 = new StringBuffer();
/* 190 */         StringBuffer stringBuffer3 = new StringBuffer();
/* 191 */         for (String str : arrayList1) {
/* 192 */           stringBuffer2.append(",").append(str);
/*     */         }
/* 194 */         for (String str : arrayList2) {
/* 195 */           stringBuffer3.append(",").append(WorkflowVersion.getAllVersionStringByWFIDs(str));
/*     */         }
/* 197 */         if (stringBuffer3.length() > 0) {
/* 198 */           stringBuffer3 = stringBuffer3.delete(0, 1);
/*     */         }
/* 200 */         if (stringBuffer3.length() > 0) {
/* 201 */           recordSet1.executeQuery("select distinct workflowtype from workflow_base where id in(" + stringBuffer3.toString() + ")", new Object[0]);
/* 202 */           arrayList1.clear();
/* 203 */           stringBuffer2.setLength(0);
/* 204 */           while (recordSet1.next()) {
/* 205 */             arrayList1.add(recordSet1.getString("workflowtype"));
/* 206 */             stringBuffer2.append(",").append(recordSet1.getString("workflowtype"));
/*     */           } 
/*     */         } 
/*     */         
/* 210 */         if (stringBuffer2.length() > 0) {
/* 211 */           stringBuffer2 = stringBuffer2.delete(0, 1);
/*     */         }
/*     */         
/* 214 */         stringBuffer = new StringBuffer();
/* 215 */         String str12 = " ((isremark='0' and (takisremark is null or takisremark=0)) or isremark in ('1','7','8','9','11')) and islasttimes=1 and isprocessed is null ";
/* 216 */         if ("1".equals(str7)) {
/* 217 */           stringBuffer.append("select a.workflowtype, a.workflowid, a.viewtype, count(a.requestid) workflowcount ");
/*     */         } else {
/* 219 */           stringBuffer.append("select a.workflowtype, a.workflowid, a.viewtype, count(distinct a.requestid) workflowcount ");
/*     */         } 
/* 221 */         stringBuffer.append("\t  from workflow_currentoperator a,workflow_requestbase t1 ");
/* 222 */         stringBuffer.append("\t  where ").append(str12);
/* 223 */         stringBuffer.append("\t    and (isprocessing = '' or isprocessing is null) ");
/* 224 */         stringBuffer.append(((String)hashMap3.get(str9)).replaceAll("t2.", "a."));
/* 225 */         if (!"".equals(str11)) {
/* 226 */           stringBuffer.append(" and (1=2 " + str11.replaceAll("t2.", "a.") + " ) ");
/*     */         }
/* 228 */         if ("1".equals(str7)) {
/* 229 */           stringBuffer.append("\t    and userid in (").append(str6);
/*     */         } else {
/* 231 */           stringBuffer.append("\t    and userid in (").append(str4);
/*     */         } 
/* 233 */         stringBuffer.append("\t)    and usertype = ").append(bool2);
/* 234 */         if (!"".equals(stringBuffer2.toString())) {
/* 235 */           stringBuffer.append("\t    and a.workflowtype in ( ").append(stringBuffer2).append(") ");
/*     */         }
/*     */         
/* 238 */         if (!"".equals(stringBuffer3.toString())) {
/* 239 */           stringBuffer.append("\t    and a.workflowid in (").append(stringBuffer3).append(")");
/*     */         }
/*     */ 
/*     */         
/* 243 */         if (recordSet1.getDBType().equalsIgnoreCase("postgresql")) {
/* 244 */           stringBuffer.append("\tand t1.requestid = a.requestid and (t1.deleted <> 1 or t1.deleted is null ) ");
/*     */         } else {
/* 246 */           stringBuffer.append("\tand t1.requestid = a.requestid and (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') ");
/* 247 */         }  stringBuffer.append(" and (" + str3 + "(t1.currentstatus,-1) = -1 or (" + str3 + "(t1.currentstatus,-1)=0 and t1.creater=" + this.user.getUID() + ")) ");
/* 248 */         if (bool) {
/* 249 */           stringBuffer.append(" and t1.seclevel >= " + str1 + " ");
/*     */         }
/*     */         
/* 252 */         if (!"".equals(str10)) {
/* 253 */           stringBuffer.append(" and a.nodeid in (" + WorkflowVersion.getAllRelationNodeStringByNodeIDs(str10) + ") ");
/*     */         }
/* 255 */         if (!bool3) {
/* 256 */           if ("1".equals(str7)) {
/* 257 */             stringBuffer.append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE a.workflowid = b.workflowid AND a.requestid = b.requestid AND b.userid in (" + str6 + ") and b.usertype= " + bool2 + ") ");
/*     */           } else {
/*     */             
/* 260 */             stringBuffer.append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE a.workflowid = b.workflowid AND a.requestid = b.requestid AND b.userid in (" + this.user
/* 261 */                 .getUID() + ") and b.usertype= " + bool2 + ") ");
/*     */           } 
/*     */         }
/* 264 */         stringBuffer.append(" group by a.viewtype, a.workflowtype, a.workflowid");
/*     */         
/* 266 */         recordSet1.executeSql(stringBuffer.toString());
/* 267 */         while (recordSet1.next()) {
/* 268 */           String str = Util.null2String(recordSet1.getString("workflowid"));
/* 269 */           int k = Util.getIntValue(recordSet1.getString("workflowcount"), 0);
/* 270 */           int m = Util.getIntValue(recordSet1.getString("viewtype"), 2);
/* 271 */           int n = arrayList2.indexOf(str);
/* 272 */           if (n != -1) {
/* 273 */             int i1 = hashtable1.containsKey(str) ? ((Integer)hashtable1.get(str)).intValue() : 0;
/* 274 */             i1 += k;
/* 275 */             hashtable1.put(str, Integer.valueOf(i1));
/* 276 */             if (m == 0) {
/* 277 */               int i2 = hashtable2.containsKey(str) ? ((Integer)hashtable2.get(str)).intValue() : 0;
/* 278 */               i2 += k;
/* 279 */               hashtable2.put(str, Integer.valueOf(i2));
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 285 */         stringBuffer = new StringBuffer();
/* 286 */         if ("1".equals(str7)) {
/* 287 */           stringBuffer.append("select a.workflowtype,a.workflowid, count(distinct a.requestid) overcount ");
/*     */         } else {
/* 289 */           stringBuffer.append("select a.workflowtype,a.workflowid, count(a.requestid) overcount ");
/*     */         } 
/* 291 */         stringBuffer.append("  from workflow_currentoperator a,workflow_requestbase t1 ");
/* 292 */         stringBuffer.append("  where (((isremark='0' and (takisremark is null or takisremark=0 )) and (isprocessed = '0' or isprocessed = '3' or isprocessed = '2' )) or ");
/* 293 */         stringBuffer.append("        isremark = '5') ");
/* 294 */         stringBuffer.append("    and islasttimes = 1 ");
/* 295 */         stringBuffer.append(((String)hashMap3.get(str9)).replaceAll("t2.", "a."));
/* 296 */         if (!"".equals(str11)) {
/* 297 */           stringBuffer.append(" and (1=2 " + str11.replaceAll("t2.", "a.") + " ) ");
/*     */         }
/* 299 */         if ("1".equals(str7)) {
/* 300 */           stringBuffer.append("\t    and userid in (").append(str6);
/*     */         } else {
/* 302 */           stringBuffer.append("\t    and userid in (").append(str4);
/*     */         } 
/* 304 */         stringBuffer.append("   ) and usertype = ").append(bool2);
/* 305 */         if (!"".equals(stringBuffer2.toString())) {
/* 306 */           stringBuffer.append("\t    and a.workflowtype in ( ").append(stringBuffer2).append(") ");
/*     */         }
/*     */         
/* 309 */         if (!"".equals(stringBuffer3.toString())) {
/* 310 */           stringBuffer.append("\t    and a.workflowid in (").append(stringBuffer3).append(")");
/*     */         }
/*     */ 
/*     */         
/* 314 */         if (recordSet1.getDBType().equalsIgnoreCase("postgresql")) {
/* 315 */           stringBuffer.append("  and t1.requestid = a.requestid and (t1.deleted <> 1 or t1.deleted is null ) ");
/*     */         } else {
/* 317 */           stringBuffer.append("  and t1.requestid = a.requestid and (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') ");
/* 318 */         }  stringBuffer.append(" and (" + str3 + "(t1.currentstatus,-1) = -1 or (" + str3 + "(t1.currentstatus,-1)=0 and t1.creater=" + this.user.getUID() + ")) ");
/* 319 */         if (bool) {
/* 320 */           stringBuffer.append(" and t1.seclevel >= " + str1 + " ");
/*     */         }
/*     */         
/* 323 */         if (!"".equals(str10)) {
/* 324 */           stringBuffer.append(" and a.nodeid in (" + WorkflowVersion.getAllRelationNodeStringByNodeIDs(str10) + ") ");
/*     */         }
/* 326 */         if (!bool3) {
/* 327 */           stringBuffer.append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE a.workflowid = b.workflowid AND a.requestid = b.requestid AND b.userid=" + this.user
/* 328 */               .getUID() + " and b.usertype= " + bool2 + ") ");
/*     */         }
/* 330 */         stringBuffer.append(" GROUP BY a.workflowtype, a.workflowid ");
/* 331 */         recordSet2.executeSql(stringBuffer.toString());
/*     */         
/* 333 */         while (recordSet2.next()) {
/* 334 */           String str = Util.null2String(recordSet2.getString("workflowid"));
/* 335 */           int k = recordSet2.getInt("overcount");
/* 336 */           int m = arrayList2.indexOf(str);
/* 337 */           if (m != -1) {
/* 338 */             int n = hashtable1.containsKey(str) ? ((Integer)hashtable1.get(str)).intValue() : 0;
/* 339 */             n += k;
/* 340 */             hashtable1.put(str, Integer.valueOf(n));
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 346 */         HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 347 */         int i = 0;
/* 348 */         int j = 0;
/* 349 */         ArrayList<String> arrayList3 = new ArrayList();
/* 350 */         arrayList3.addAll(arrayList2);
/* 351 */         ArrayList<String> arrayList4 = new ArrayList();
/* 352 */         for (byte b = 0; b < arrayList1.size(); b++) {
/* 353 */           String str = arrayList1.get(b);
/* 354 */           for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 355 */             String str14 = arrayList2.get(b1);
/* 356 */             RecordSet recordSet = new RecordSet();
/* 357 */             recordSet.executeQuery("select workflowtype from workflow_base where id = ?", new Object[] { str14 });
/* 358 */             String str15 = "";
/* 359 */             if (recordSet.next())
/* 360 */               str15 = Util.null2String(recordSet.getString(1)); 
/* 361 */             if (str15.equals(str)) {
/*     */ 
/*     */               
/* 364 */               if (!arrayList4.contains(str14)) {
/* 365 */                 arrayList4.add(str14);
/*     */               }
/* 367 */               int k = Util.getIntValue((new StringBuilder()).append(hashtable1.get(str14)).append("").toString(), 0);
/* 368 */               int m = Util.getIntValue((new StringBuilder()).append(hashtable2.get(str14)).append("").toString(), 0);
/* 369 */               i += k;
/* 370 */               j += m;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 378 */         HashMap<Object, Object> hashMap6 = new HashMap<>();
/* 379 */         List list1 = new ArrayList();
/* 380 */         hashMap2.put("menuid", str9);
/* 381 */         recordSet1.executeQuery("select typeids from workflow_mobileconfig where menuid = ?", new Object[] { str9 });
/* 382 */         String str13 = "";
/* 383 */         if (recordSet1.next()) {
/* 384 */           str13 = Util.null2String(recordSet1.getString("typeids"));
/*     */         }
/* 386 */         if (!"".equals(str13)) {
/* 387 */           list1.addAll(Arrays.asList(str13.split(",")));
/*     */         } else {
/* 389 */           list1 = WorkflowDimensionUtils.getTypeidList("emDoingApp");
/*     */         } 
/* 391 */         Map map = (new OfsRequestListBiz(hashMap2, this.user)).extendCountData("doing", hashMap5, this.user, list1, str4);
/* 392 */         if (map != null) {
/* 393 */           i += Util.getIntValue((new StringBuilder()).append(map.get("totalAllCount")).append("").toString(), 0);
/* 394 */           j += Util.getIntValue((new StringBuilder()).append(map.get("totalNewCount")).append("").toString(), 0);
/*     */         } 
/* 396 */         if (!"-1".equals(str9)) {
/* 397 */           hashMap4.put("count", Integer.valueOf(i));
/* 398 */           hashMap4.put("unread", Integer.valueOf(j));
/*     */           
/* 400 */           hashMap1.put(str9, hashMap4); continue;
/*     */         } 
/* 402 */         hashMap1.put("count", Integer.valueOf(i));
/* 403 */         hashMap1.put("unread", Integer.valueOf(j));
/*     */       }
/*     */     
/* 406 */     } catch (Exception exception) {
/* 407 */       exception.printStackTrace();
/*     */     } 
/* 409 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 414 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/emAppCount/EmDoingCountInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */