/*     */ package com.engine.workflow.cmd.emAppCount;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import com.engine.workflow.biz.mobileCenter.MobileDimensionsBiz;
/*     */ import com.engine.workflow.biz.requestList.OfsRequestListBiz;
/*     */ import com.engine.workflow.util.CommonUtil;
/*     */ import com.engine.workflow.util.WorkflowDimensionUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.AllManagers;
/*     */ import weaver.workflow.request.todo.OfsSettingObject;
/*     */ import weaver.workflow.request.todo.RequestUtil;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ public class EmCCCountInfoCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private String menuidNew;
/*     */   
/*     */   public EmCCCountInfoCmd(String paramString, User paramUser) {
/*  30 */     this.menuidNew = paramString;
/*  31 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  35 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  36 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  37 */     RequestUtil requestUtil = new RequestUtil();
/*  38 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  39 */     boolean bool1 = (ofsSettingObject.getIsuse() == 1) ? true : false;
/*  40 */     hashMap2.put("ismobile", "1");
/*  41 */     boolean bool = HrmClassifiedProtectionBiz.isOpenClassification();
/*  42 */     String str1 = (new HrmClassifiedProtectionBiz()).getMaxResourceSecLevel(this.user);
/*     */     
/*  44 */     String str2 = "";
/*  45 */     str2 = this.menuidNew;
/*  46 */     ArrayList arrayList = new ArrayList();
/*     */     
/*  48 */     List<String> list = Util.splitString2List(str2, ",");
/*  49 */     if (!list.contains("-1")) {
/*  50 */       list.add("-1");
/*     */     }
/*  52 */     list.remove("");
/*  53 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */     
/*  55 */     for (String str4 : list) {
/*  56 */       String str5 = "";
/*  57 */       int i = Util.getIntValue(str4, -1);
/*  58 */       if (i != -1) {
/*  59 */         MobileDimensionsBiz mobileDimensionsBiz = new MobileDimensionsBiz();
/*  60 */         String str6 = mobileDimensionsBiz.getSourcetypeNew(i);
/*  61 */         String str7 = mobileDimensionsBiz.getRange(i, str6);
/*  62 */         if (!"".equals(str6) && !"".equals(str7)) {
/*  63 */           String str8 = "in";
/*  64 */           if ("2".equals(str6) || "4".equals(str6)) {
/*  65 */             str8 = "not in";
/*     */           }
/*  67 */           String str9 = "t2.workflowid";
/*  68 */           if ("3".equals(str6) || "4".equals(str6)) {
/*  69 */             str9 = "t3.workflowtype";
/*     */           }
/*  71 */           str5 = " and " + Util.getSubINClause(str7, str9, str8);
/*     */         } 
/*     */       } 
/*  74 */       hashMap3.put(str4, str5);
/*     */     } 
/*     */     
/*  77 */     String str3 = "";
/*  78 */     boolean bool2 = true;
/*  79 */     str3 = OfsRequestListBiz.getOfsConfigWhere(bool2, "");
/*     */     try {
/*  81 */       if (this.user == null)
/*  82 */         return null; 
/*  83 */       RecordSet recordSet1 = new RecordSet();
/*  84 */       RecordSet recordSet2 = new RecordSet();
/*  85 */       String str4 = CommonUtil.getDBJudgeNullFun(recordSet1.getDBType());
/*  86 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  87 */       AllManagers allManagers = new AllManagers();
/*     */       
/*  89 */       str3 = str3 + OfsRequestListBiz.getOfsUrlWhere(bool2, "");
/*     */       
/*  91 */       String str5 = "";
/*  92 */       allManagers.getAll(str5);
/*  93 */       if ("".equals(str5))
/*  94 */         str5 = "" + this.user.getUID(); 
/*  95 */       boolean bool3 = false;
/*  96 */       if (str5.equals("" + this.user.getUID()))
/*  97 */         bool3 = true; 
/*  98 */       boolean bool4 = false;
/*  99 */       if ("2".equals(this.user.getLogintype()))
/* 100 */         bool4 = true; 
/* 101 */       String str6 = String.valueOf(this.user.getUID());
/* 102 */       String str7 = String.valueOf(this.user.getUID());
/* 103 */       String str8 = "";
/* 104 */       recordSet2.executeSql("select * from HrmUserSetting where resourceId = " + str6);
/* 105 */       if (recordSet2.next()) {
/* 106 */         str8 = recordSet2.getString("belongtoshow");
/*     */       }
/* 108 */       if (!bool3)
/* 109 */         str8 = ""; 
/* 110 */       if (!"".equals(this.user.getBelongtoids())) {
/* 111 */         str7 = str6 + "," + this.user.getBelongtoids();
/*     */       }
/* 113 */       boolean bool5 = false;
/* 114 */       if ("".equals(str5) || str6.equals(str5)) {
/* 115 */         str5 = str6;
/* 116 */         bool5 = true;
/*     */       } else {
/* 118 */         recordSet1.executeSql("SELECT * FROM HrmResource WHERE ID = " + str5 + " AND managerStr LIKE '%," + str6 + ",%'");
/* 119 */         if (recordSet1.next()) {
/* 120 */           bool5 = true;
/*     */         }
/*     */       } 
/* 123 */       StringBuffer stringBuffer = new StringBuffer();
/*     */       
/* 125 */       for (String str9 : list) {
/* 126 */         byte b1 = 0;
/* 127 */         byte b2 = 0;
/* 128 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         
/* 130 */         stringBuffer = new StringBuffer();
/* 131 */         stringBuffer.append("select t2.viewtype,t2.requestid ");
/* 132 */         stringBuffer.append("\t  from workflow_requestbase t1,workflow_currentoperator t2,workflow_base t3 ");
/* 133 */         stringBuffer.append(" \t  where islasttimes=1 and isprocessed is null ");
/* 134 */         stringBuffer.append(" \t  and t2.workflowid = t3.id and t3.isvalid in('1','3') ");
/* 135 */         stringBuffer.append(WorkflowDimensionUtils.getToDoSqlWhere("54", "and"));
/* 136 */         stringBuffer.append("\t    and (isprocessing = '' or isprocessing is null) ");
/* 137 */         stringBuffer.append((String)hashMap3.get(str9));
/* 138 */         String str10 = WorkflowDimensionUtils.getEmAppSqlWhere(str9, "or", this.user, "emCopyApp");
/* 139 */         if (!"".equals(str10)) {
/* 140 */           stringBuffer.append(" and (1=2 " + str10 + " ) ");
/*     */         }
/* 142 */         if ("1".equals(str8)) {
/* 143 */           stringBuffer.append("\t    and t2.userid in (").append(str7);
/*     */         } else {
/* 145 */           stringBuffer.append("\t    and t2.userid in (").append(str5);
/*     */         } 
/* 147 */         stringBuffer.append("\t)    and usertype = ").append(bool4);
/* 148 */         if (recordSet1.getDBType().equalsIgnoreCase("postgresql")) {
/* 149 */           stringBuffer.append("\tand t1.requestid = t2.requestid and (t1.deleted <> 1 or t1.deleted is null) ");
/*     */         } else {
/* 151 */           stringBuffer.append("\tand t1.requestid = t2.requestid and (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') ");
/* 152 */         }  stringBuffer.append(" and (" + str4 + "(t1.currentstatus,-1) = -1 or (" + str4 + "(t1.currentstatus,-1)=0 and t1.creater=" + this.user.getUID() + ")) ");
/* 153 */         if (bool) {
/* 154 */           stringBuffer.append(" and t1.seclevel >= " + str1 + " ");
/*     */         }
/* 156 */         if (!bool5) {
/* 157 */           if ("1".equals(str8)) {
/* 158 */             stringBuffer.append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE t2.workflowid = b.workflowid AND t2.requestid = b.requestid AND b.userid in (" + str7 + ") and b.usertype= " + bool4 + ") ");
/*     */           } else {
/*     */             
/* 161 */             stringBuffer.append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE t2.workflowid = b.workflowid AND t2.requestid = b.requestid AND b.userid in (" + this.user
/* 162 */                 .getUID() + ") and b.usertype= " + bool4 + ") ");
/*     */           } 
/*     */         }
/*     */         
/* 166 */         stringBuffer.append(" union select viewtype,requestid from ofs_todo_data where islasttimes = 1 ");
/* 167 */         if ("1".equals(str8)) {
/* 168 */           stringBuffer.append("\t    and userid in (").append(str7).append(") ");
/*     */         } else {
/* 170 */           stringBuffer.append("\t    and userid in (").append(str5).append(") ");
/*     */         } 
/* 172 */         stringBuffer.append(((String)hashMap3.get(str9)).replaceAll("t2.", "").replaceAll("t3.workflowtype", "sysid"));
/* 173 */         stringBuffer.append(WorkflowDimensionUtils.getOsSqlWhere("54", "and"));
/* 174 */         stringBuffer.append(bool1 ? str3 : " and 1=2");
/*     */         
/* 176 */         recordSet1.executeSql(stringBuffer.toString());
/* 177 */         while (recordSet1.next()) {
/* 178 */           String str = recordSet1.getString("viewtype");
/* 179 */           if ("0".equals(str)) {
/* 180 */             b2++;
/*     */           }
/* 182 */           b1++;
/*     */         } 
/*     */         
/* 185 */         if (!"-1".equals(str9)) {
/* 186 */           hashMap.put("count", Integer.valueOf(b1));
/* 187 */           hashMap.put("unread", Integer.valueOf(b2));
/*     */           
/* 189 */           hashMap1.put(str9, hashMap); continue;
/*     */         } 
/* 191 */         hashMap1.put("count", Integer.valueOf(b1));
/* 192 */         hashMap1.put("unread", Integer.valueOf(b2));
/*     */       }
/*     */     
/* 195 */     } catch (Exception exception) {
/* 196 */       exception.printStackTrace();
/*     */     } 
/* 198 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 203 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/emAppCount/EmCCCountInfoCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */