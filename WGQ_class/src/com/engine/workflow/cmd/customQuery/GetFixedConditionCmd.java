/*    */ package com.engine.workflow.cmd.customQuery;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.requestList.SearchConditionBiz;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetFixedConditionCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetFixedConditionCmd(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.params = paramMap;
/* 29 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 35 */     String str1 = "";
/* 36 */     if ("1".equals(Util.null2String(this.params.get("ismobile"))) || "true".equals(Util.null2String(this.params.get("_ec_ismobile")))) {
/* 37 */       str1 = "1";
/*    */     }
/* 39 */     if ("1".equals(str1)) {
/* 40 */       boolean bool = !"".equals(Util.null2String(this.params.get("mobileTabId"))) ? true : false;
/* 41 */       if (bool && !"10".equals(Util.null2String(this.params.get("viewType")))) {
/* 42 */         hashMap.put("conditioninfo", SearchConditionBiz.getSearchIndexCondition(str1, "customQuery", this.user, false));
/*    */       } else {
/* 44 */         hashMap.put("conditioninfo", SearchConditionBiz.getSearchIndexCondition(str1, "customQuery", this.user));
/*    */       } 
/*    */     } else {
/*    */       
/* 48 */       hashMap.put("condition", SearchConditionBiz.getSearchIndexCondition("customQuery", this.user));
/*    */     } 
/*    */     
/* 51 */     WorkflowConfigComInfo workflowConfigComInfo = new WorkflowConfigComInfo();
/* 52 */     String str2 = workflowConfigComInfo.getValue("wf_search_defaultbtn");
/* 53 */     if ("".equals(str2) || str2 == null) {
/* 54 */       str2 = "batchSubmit";
/*    */     }
/* 56 */     hashMap.put("operBtnKey", str2);
/*    */ 
/*    */ 
/*    */     
/* 60 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 65 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/customQuery/GetFixedConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */