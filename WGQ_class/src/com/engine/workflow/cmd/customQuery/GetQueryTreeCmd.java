/*     */ package com.engine.workflow.cmd.customQuery;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.common.service.impl.PortalCommonServiceImpl;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import com.engine.portal.biz.menuinfo.MenuInfoCommonBiz;
/*     */ import com.engine.workflow.biz.WorkflowCenterBiz;
/*     */ import com.engine.workflow.biz.mobileCenter.WorkflowCenterTabBiz;
/*     */ import com.engine.workflow.biz.requestList.OfsRequestListBiz;
/*     */ import com.engine.workflow.biz.requestList.RequestAttentionBiz;
/*     */ import com.engine.workflow.entity.TreeNodeEntity;
/*     */ import com.engine.workflow.util.CommonUtil;
/*     */ import com.engine.workflow.util.PortalRequestListUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Escape;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.login.Base64;
/*     */ import weaver.workflow.request.WFShareAuthorization;
/*     */ import weaver.workflow.request.todo.OfsSettingObject;
/*     */ import weaver.workflow.request.todo.RequestUtil;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetQueryTreeCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetQueryTreeCmd(Map<String, Object> paramMap, User paramUser) {
/*  41 */     this.params = paramMap;
/*  42 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  47 */     String str1 = "";
/*  48 */     if ("1".equals(Util.null2String(this.params.get("ismobile"))) || "true".equals(Util.null2String(this.params.get("_ec_ismobile")))) {
/*  49 */       str1 = "1";
/*     */     }
/*  51 */     boolean bool1 = "true".equals(this.params.get("belongPathTree"));
/*  52 */     String str2 = Util.null2String(this.params.get("name"));
/*  53 */     String str3 = CommonUtil.getAllUserid(this.user);
/*  54 */     boolean bool2 = "2".equals(this.user.getLogintype()) ? true : false;
/*  55 */     String str4 = "", str5 = "";
/*  56 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  58 */     RequestUtil requestUtil = new RequestUtil();
/*  59 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  60 */     boolean bool3 = (ofsSettingObject.getIsuse() == 1 && (new OfsRequestListBiz()).supportOfs4OtherCall(this.user)) ? true : false;
/*  61 */     boolean bool4 = ofsSettingObject.getShowdone().equals("1");
/*     */ 
/*     */     
/*  64 */     String str6 = getPortalJsonstr();
/*     */ 
/*     */     
/*  67 */     String str7 = "";
/*  68 */     if (!"".equals(str6.trim())) {
/*  69 */       Map map = (Map)JSON.parseObject(str6, Map.class);
/*  70 */       str7 = Util.null2String(map.get("viewType"));
/*     */     } 
/*     */     
/*  73 */     Map map1 = (new PortalRequestListUtil()).getPortalSqlWhere(str6, str3, bool2, this.user);
/*     */     
/*  75 */     String str8 = Util.null2String(this.params.get("mobileTabId"));
/*  76 */     Map map2 = null;
/*  77 */     String str9 = "";
/*  78 */     if (!"".equals(str8)) {
/*  79 */       String str = Util.null2String(this.params.get("menuid"));
/*  80 */       str7 = Util.null2String(this.params.get("viewType"));
/*  81 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  82 */       hashMap1.put("mobileTabId", str8);
/*  83 */       hashMap1.put("menuid", str);
/*  84 */       hashMap1.put("viewType", str7);
/*  85 */       str9 = WorkflowCenterTabBiz.getWfCenterTabWhere(hashMap1);
/*  86 */       if (!"".equals(str9.trim())) {
/*  87 */         Map map = (Map)JSON.parseObject(str9, Map.class);
/*  88 */         str7 = Util.null2String(map.get("viewType"));
/*     */       } 
/*  90 */       map2 = (new PortalRequestListUtil()).getPortalSqlWhere(str9, str3, bool2, this.user);
/*     */     } 
/*     */     
/*  93 */     String str10 = CommonUtil.getDBJudgeNullFun(recordSet.getDBType());
/*     */     
/*  95 */     String str11 = "select distinct t1.workflowid from workflow_requestbase t1,workflow_currentoperator t2,workflow_base t3  where t1.requestid = t2.requestid and t2.workflowid = t3.id and " + (recordSet.getDBType().equalsIgnoreCase("postgresql") ? "(t1.deleted <> 1 or t1.deleted is null )" : "(t1.deleted <> 1 or t1.deleted is null or t1.deleted='')") + " and t2.userid in (" + str3 + ") and t2.usertype=" + bool2 + " and t2.islasttimes=1 and (" + str10 + "(t1.currentstatus,-1) = -1 or (" + str10 + "(t1.currentstatus,-1)=0 and t1.creater in (" + str3 + "))) ";
/*     */ 
/*     */ 
/*     */     
/*  99 */     if (HrmClassifiedProtectionBiz.isOpenClassification()) {
/* 100 */       str11 = str11 + " and t1.seclevel >= " + (new HrmClassifiedProtectionBiz()).getMaxResourceSecLevelById2(this.user.getUID() + "") + " ";
/*     */     }
/* 102 */     String str12 = "";
/*     */     
/* 104 */     if (map1 == null && map2 == null) {
/* 105 */       String str = WorkflowCenterBiz.getRejectCondition(this.user, Util.null2String(this.params.get("rejectTime")), Util.null2String(this.params.get("rejectTimeFrom")), Util.null2String(this.params.get("rejectTimeTo")), Util.null2String(this.params.get("rejectType")));
/* 106 */       if (!"".equals(str)) {
/* 107 */         str11 = str11 + str;
/* 108 */         bool3 = false;
/*     */       } 
/*     */       
/* 111 */       str12 = transferCondition(str11);
/* 112 */       if ("".equals(str)) {
/* 113 */         String str13 = Util.null2String(getShareRange(true));
/* 114 */         if (!"".equals(str13) && !"".equals(str12)) {
/* 115 */           str12 = str12 + "," + str13;
/* 116 */         } else if (!"".equals(str13)) {
/* 117 */           str12 = str13;
/*     */         } 
/*     */       } 
/* 120 */     } else if (map1 != null) {
/* 121 */       String str = (String)map1.get("whereclause");
/* 122 */       if (str != null && !str.trim().equals("")) {
/* 123 */         if (str.trim().startsWith("and")) {
/* 124 */           str11 = str11 + str;
/*     */         } else {
/* 126 */           str11 = str11 + " and " + str;
/*     */         } 
/*     */       }
/* 129 */       if ("14".equals(str7)) {
/* 130 */         String str13 = Util.null2String((String)map1.get("wfAndTypeWhere"));
/* 131 */         StringBuilder stringBuilder = new StringBuilder();
/* 132 */         String str14 = "select distinct t1.workflowid from workflow_requestbase t1 , workflow_superviseoperator sup, workflow_attention att ";
/* 133 */         stringBuilder.append(" where t1.requestid = sup.requestid and t1.requestid = att.requestid and att.userid = '").append(this.user.getUID()).append("' and att.usertype = '").append(bool2).append("' ")
/* 134 */           .append(" and sup.userid = '").append(this.user.getUID()).append("' and sup.usertype = '").append(bool2).append("' ");
/* 135 */         if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 136 */           stringBuilder.append(" and (t1.deleted <> 1 or t1.deleted is null ) ");
/*     */         } else {
/* 138 */           stringBuilder.append(" and (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') ");
/* 139 */         }  stringBuilder.append(" and att.id in (SELECT MAX(id) FROM workflow_attention WHERE requestid>0 and userid=" + this.user.getUID() + " AND usertype=" + bool2 + " " + RequestAttentionBiz.getAtypeSqlwhere("", "") + " GROUP BY requestid)");
/* 140 */         stringBuilder.append(RequestAttentionBiz.getAtypeSqlwhere("att", ""));
/* 141 */         if (HrmClassifiedProtectionBiz.isOpenClassification()) {
/* 142 */           stringBuilder.append(" and t1.seclevel >= " + (new HrmClassifiedProtectionBiz()).getMaxResourceSecLevel(this.user) + " ");
/*     */         }
/* 144 */         stringBuilder.append(" and (" + str10 + "(t1.currentstatus,-1) = -1 or (" + str10 + "(t1.currentstatus,-1)=0 and t1.creater=" + this.user.getUID() + ")) ");
/* 145 */         stringBuilder.append(" and not exists (select 1 from workflow_currentoperator c where c.requestid = t1.requestid and  c.userid = " + this.user.getUID() + " and c.usertype=" + bool2 + " )");
/* 146 */         stringBuilder.append(str13.replaceAll("t2.", "sup."));
/*     */         
/* 148 */         str11 = str11 + " union all " + str14 + " " + stringBuilder.toString();
/*     */       } 
/* 150 */       str12 = transferCondition(str11);
/* 151 */     } else if (map2 != null) {
/* 152 */       String str = (String)map2.get("whereclause");
/* 153 */       if (str != null && !"".equals(str.trim())) {
/* 154 */         if (str.trim().startsWith("and")) {
/* 155 */           str11 = str11 + str;
/*     */         } else {
/* 157 */           str11 = str11 + " and " + str;
/*     */         } 
/*     */       }
/* 160 */       str12 = transferCondition(str11);
/*     */     } 
/*     */ 
/*     */     
/* 164 */     if (!"".equals(str12)) {
/* 165 */       str5 = " and " + Util.getSubINClause(str12, "a.id", "in");
/*     */     } else {
/* 167 */       str5 = " and 1=2 ";
/*     */     } 
/* 169 */     ArrayList<String> arrayList1 = new ArrayList();
/* 170 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */     
/* 172 */     if (bool1) {
/* 173 */       str4 = "select b.id as typeid,b.typename,a.id as id,workflowname as name,b.dsporder as dspordertype  from workflow_base a, workflow_type b where a.workflowtype=b.id and a.isvalid in ('1','3') ";
/*     */       
/* 175 */       if ("1".equals(str1) && !"".equals(str2)) {
/* 176 */         str4 = str4 + " and (a.workflowname like '%" + str2 + "%' or b.typename  like '%" + str2 + "%' ) ";
/*     */       }
/* 178 */       str4 = str4 + str5 + " order by b.dsporder,b.id,a.dsporder,a.workflowname";
/*     */     } else {
/* 180 */       str4 = "select id,formid,isbill from workflow_base a where a.isvalid in ('1','3') and (a.activeversionid is null or a.activeversionid=0 or a.activeversionid in(select id from workflow_base where isvalid=1)) " + str5;
/* 181 */       recordSet.executeQuery(str4, new Object[0]);
/* 182 */       while (recordSet.next()) {
/* 183 */         arrayList1.add(recordSet.getString("id"));
/* 184 */         arrayList2.add(recordSet.getString("formid") + "|" + recordSet.getString("isbill"));
/*     */       } 
/* 186 */       str4 = "select a.id as typeid,a.typename,b.id,b.customname as name,formid,isbill,workflowids  from workflow_customquerytype a,workflow_custom b where a.id=b.querytypeid order by a.showorder,a.id,b.id";
/*     */     } 
/*     */     
/* 189 */     writeLog("流程中心more页面树sql输出：username:" + this.user.getLastname() + ";params:" + this.params.toString() + ";jsonstr:" + str6);
/* 190 */     writeLog("流程中心more页面树sql输出：belongPathTree：" + bool1 + ";sql:" + str4);
/* 191 */     ArrayList<TreeNodeEntity> arrayList = new ArrayList();
/* 192 */     ArrayList<String> arrayList3 = new ArrayList();
/* 193 */     ArrayList<String> arrayList4 = new ArrayList();
/* 194 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 195 */     recordSet.executeQuery(str4, new Object[0]);
/* 196 */     while (recordSet.next()) {
/* 197 */       String str13 = recordSet.getString("typeid");
/* 198 */       String str14 = CommonUtil.convertChar(recordSet.getString("typename"));
/* 199 */       String str15 = recordSet.getString("id");
/* 200 */       String str16 = CommonUtil.convertChar(recordSet.getString("name"));
/* 201 */       if (!bool1) {
/* 202 */         String str = Util.null2String(recordSet.getString("workflowids")).trim();
/* 203 */         if (arrayList2.indexOf(recordSet.getString("formid") + "|" + recordSet.getString("isbill")) == -1)
/*     */           continue; 
/* 205 */         if (!"".equals(str)) {
/* 206 */           String str17 = WorkflowVersion.getAllVersionStringByWFIDs(str);
/* 207 */           boolean bool = false;
/* 208 */           for (String str18 : str17.split(",")) {
/* 209 */             if (arrayList1.indexOf(str18) > -1) {
/* 210 */               bool = true;
/*     */               break;
/*     */             } 
/*     */           } 
/* 214 */           if (!bool) {
/*     */             continue;
/*     */           }
/*     */         } 
/* 218 */       } else if (!"1".equals(workflowAllComInfo.getIsValid(str15))) {
/* 219 */         if (!"3".equals(workflowAllComInfo.getIsValid(str15))) {
/*     */           continue;
/*     */         }
/* 222 */         str15 = workflowAllComInfo.getActiveversionid(str15);
/*     */       } 
/*     */ 
/*     */       
/* 226 */       List<TreeNodeEntity> list = null;
/* 227 */       int i = arrayList3.indexOf(str13);
/* 228 */       if (i == -1) {
/* 229 */         TreeNodeEntity treeNodeEntity = new TreeNodeEntity();
/* 230 */         treeNodeEntity.setDomid("type_" + str13);
/* 231 */         treeNodeEntity.setKey(str13);
/* 232 */         treeNodeEntity.setName(str14);
/* 233 */         treeNodeEntity.setIsopen(true);
/* 234 */         treeNodeEntity.setHaschild(true);
/* 235 */         list = new ArrayList();
/* 236 */         treeNodeEntity.setChilds(list);
/* 237 */         arrayList3.add(str13);
/* 238 */         treeNodeEntity.setDsporder(Util.getFloatValue(recordSet.getString("dspordertype"), 0.0F));
/* 239 */         arrayList.add(treeNodeEntity);
/*     */       } else {
/* 241 */         list = ((TreeNodeEntity)arrayList.get(i)).getChilds();
/*     */       } 
/* 243 */       if (arrayList4.indexOf(str15) == -1) {
/* 244 */         arrayList4.add(str15);
/* 245 */         TreeNodeEntity treeNodeEntity = new TreeNodeEntity();
/* 246 */         treeNodeEntity.setDomid("wf_" + str15);
/* 247 */         treeNodeEntity.setKey(str15);
/* 248 */         treeNodeEntity.setName(str16);
/* 249 */         treeNodeEntity.setIsopen(false);
/* 250 */         treeNodeEntity.setHaschild(false);
/* 251 */         list.add(treeNodeEntity);
/*     */       } 
/*     */     } 
/* 254 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 255 */     if ("1".equals(str1)) {
/* 256 */       hashMap.put("treedata", arrayList);
/* 257 */       hashMap.put("type", Integer.valueOf(3));
/*     */     } else {
/* 259 */       hashMap.put("tree", arrayList);
/*     */     } 
/* 261 */     if (bool3 && bool1 && ("1".equals(str7) || "2".equals(str7) || "4".equals(str7) || "10".equals(str7) || ("3".equals(str7) && bool4) || "".equals(str7) || Util.getIntValue(str7) > 13 || "11".equals(str7) || "5".equals(str7))) {
/* 262 */       if (map1 == null) {
/* 263 */         str6 = str9;
/*     */       }
/* 265 */       List<TreeNodeEntity> list = getTreeOs(str6, str3, this.user);
/* 266 */       arrayList.addAll(list);
/* 267 */       if (list.size() > 0) {
/* 268 */         arrayList.sort((paramTreeNodeEntity1, paramTreeNodeEntity2) -> (paramTreeNodeEntity1.getDsporder() == paramTreeNodeEntity2.getDsporder()) ? 0 : ((paramTreeNodeEntity1.getDsporder() < paramTreeNodeEntity2.getDsporder()) ? -1 : 1));
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 276 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<TreeNodeEntity> getTreeOs(String paramString1, String paramString2, User paramUser) {
/* 286 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 288 */     ArrayList<TreeNodeEntity> arrayList = new ArrayList();
/* 289 */     ArrayList<String> arrayList1 = new ArrayList();
/* 290 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */     
/* 292 */     String str1 = "";
/* 293 */     String str2 = "";
/* 294 */     String str3 = "";
/*     */     
/* 296 */     String str4 = Util.null2String(this.params.get("name"));
/* 297 */     Map map = (new PortalRequestListUtil()).getPortalSqlOs(paramString1, paramString2, paramUser);
/* 298 */     boolean bool1 = "1".equals(Util.null2String((String)map.get("showDoing")));
/* 299 */     boolean bool2 = "1".equals(Util.null2String((String)map.get("showDone")));
/* 300 */     if (map != null) {
/* 301 */       str2 = (String)map.get("whereclause_os");
/* 302 */       str3 = (String)map.get("whereclause_osDone");
/*     */     } 
/* 304 */     if (bool1 && bool2) {
/* 305 */       str1 = str1 + " and (exists (select 1 from ofs_todo_data where workflowid = a.workflowid  " + str2;
/* 306 */       str1 = str1 + " union all (select 1 from ofs_done_data where workflowid = a.workflowid  " + str3 + ")) ";
/* 307 */     } else if (bool1) {
/* 308 */       str1 = str1 + " and exists (select 1 from ofs_todo_data where workflowid = a.workflowid  " + str2 + ") ";
/*     */     } else {
/* 310 */       str1 = str1 + " and exists (select 1 from ofs_done_data where workflowid = a.workflowid  " + str3 + ") ";
/*     */     } 
/*     */ 
/*     */     
/* 314 */     boolean bool = "1".equals(this.params.get("ismobile")) ? true : false;
/* 315 */     str1 = str1 + OfsRequestListBiz.getOfsConfigWhere(bool, "b.");
/*     */ 
/*     */ 
/*     */     
/* 319 */     RequestUtil requestUtil = new RequestUtil();
/* 320 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/* 321 */     String str5 = ofsSettingObject.getShowsysname();
/*     */ 
/*     */     
/* 324 */     String str6 = "select b.sysid as typeid,b.sysfullname as typename,b.sysshortname as typenameShort,a.workflowid as id,workflowname as name,b.showorder  from ofs_workflow a, ofs_sysinfo b where a.sysid=b.sysid  " + (("1".equals(this.params.get("ismobile")) && !"".equals(str4)) ? (" and (" + ("2".equals(str5) ? " b.sysfullname " : " b.sysshortname ") + " like '%" + str4 + "%' or a.workflowname like '%" + str4 + "%')") : "") + str1 + " order by b.sysid desc,a.workflowname";
/*     */     
/* 326 */     recordSet.executeQuery(str6, new Object[0]);
/* 327 */     while (recordSet.next()) {
/* 328 */       String str7 = recordSet.getString("typeid");
/* 329 */       String str8 = "2".equals(str5) ? CommonUtil.convertChar(recordSet.getString("typename")) : CommonUtil.convertChar(recordSet.getString("typenameShort"));
/* 330 */       String str9 = recordSet.getString("id");
/* 331 */       String str10 = CommonUtil.convertChar(recordSet.getString("name"));
/*     */       
/* 333 */       List<TreeNodeEntity> list = null;
/* 334 */       int i = arrayList1.indexOf(str7);
/* 335 */       if (i == -1) {
/* 336 */         TreeNodeEntity treeNodeEntity = new TreeNodeEntity();
/* 337 */         treeNodeEntity.setDomid("type_" + str7);
/* 338 */         treeNodeEntity.setKey(str7);
/* 339 */         treeNodeEntity.setName(str8);
/* 340 */         treeNodeEntity.setIsopen(true);
/* 341 */         treeNodeEntity.setHaschild(true);
/* 342 */         list = new ArrayList();
/* 343 */         treeNodeEntity.setChilds(list);
/* 344 */         arrayList1.add(str7);
/* 345 */         treeNodeEntity.setDsporder(Util.getFloatValue(recordSet.getString("showorder"), 0.0F));
/* 346 */         arrayList.add(treeNodeEntity);
/*     */       } else {
/* 348 */         list = ((TreeNodeEntity)arrayList.get(i)).getChilds();
/*     */       } 
/* 350 */       if (arrayList2.indexOf(str9) == -1) {
/* 351 */         arrayList2.add(str9);
/* 352 */         TreeNodeEntity treeNodeEntity = new TreeNodeEntity();
/* 353 */         treeNodeEntity.setDomid("wf_" + str9);
/* 354 */         treeNodeEntity.setKey(str9);
/* 355 */         treeNodeEntity.setName(str10);
/* 356 */         treeNodeEntity.setIsopen(false);
/* 357 */         treeNodeEntity.setHaschild(false);
/* 358 */         list.add(treeNodeEntity);
/*     */       } 
/*     */     } 
/* 361 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object getShareRange(boolean paramBoolean) {
/* 368 */     ArrayList<String> arrayList = new ArrayList();
/* 369 */     String str = "";
/* 370 */     WFShareAuthorization wFShareAuthorization = new WFShareAuthorization();
/* 371 */     Map map = wFShareAuthorization.getRequestShareByUser(this.user);
/* 372 */     if (!map.isEmpty() && map.containsKey("wfid")) {
/* 373 */       String str1 = (String)map.get("wfid");
/* 374 */       for (String str2 : str1.split(",")) {
/* 375 */         if (!"".equals(str2.trim())) {
/* 376 */           arrayList.add(str2);
/* 377 */           str = str + "," + str2;
/*     */         } 
/*     */       } 
/*     */     } 
/* 381 */     if (str.startsWith(","))
/* 382 */       str = str.substring(1); 
/* 383 */     return paramBoolean ? str : arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/* 388 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   private String getPortalJsonstr() {
/* 393 */     String str1 = "";
/* 394 */     String str2 = Util.null2String(this.params.get("menuidforportal"));
/* 395 */     if (!"".equals(str2)) {
/* 396 */       MenuInfoCommonBiz menuInfoCommonBiz = new MenuInfoCommonBiz();
/* 397 */       str1 = menuInfoCommonBiz.getLeftMenuParams(str2);
/* 398 */       if (!"".equals(str1)) {
/* 399 */         str1 = Escape.unescape(str1.split("=")[2]);
/*     */       }
/*     */     } else {
/* 402 */       String str3 = Util.null2String(this.params.get("eid"));
/* 403 */       String str4 = Util.null2String(this.params.get("tabid"));
/* 404 */       String str5 = Util.null2String(this.params.get("formData"));
/* 405 */       if (!"".equals(str5)) {
/* 406 */         str5 = new String(Base64.decode(str5));
/*     */       }
/* 408 */       String str6 = Util.null2String(this.params.get("synergyRequestid"));
/* 409 */       String str7 = Util.null2String(this.params.get("synergyWorkflowid"));
/* 410 */       PortalCommonServiceImpl portalCommonServiceImpl = new PortalCommonServiceImpl();
/* 411 */       if (!"".equals(str3) || !"".equals(str4)) {
/* 412 */         str1 = portalCommonServiceImpl.getPortalWorkflowParams(str3, str4, str6, str5, str7, this.user);
/*     */       }
/*     */     } 
/* 415 */     if ("".equals(str1)) {
/* 416 */       str1 = Util.null2String(this.params.get("jsonstr"));
/*     */     }
/* 418 */     return str1;
/*     */   }
/*     */   
/*     */   private String transferCondition(String paramString) {
/* 422 */     StringBuffer stringBuffer = new StringBuffer();
/* 423 */     RecordSet recordSet = new RecordSet();
/* 424 */     recordSet.executeQuery(paramString, new Object[0]);
/* 425 */     while (recordSet.next()) {
/* 426 */       stringBuffer.append(recordSet.getString("workflowid")).append(",");
/*     */     }
/* 428 */     if (stringBuffer.length() > 0) {
/* 429 */       return stringBuffer.substring(0, stringBuffer.length() - 1);
/*     */     }
/* 431 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/customQuery/GetQueryTreeCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */