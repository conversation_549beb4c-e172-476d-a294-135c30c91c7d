/*    */ package com.engine.workflow.cmd.customQuery;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.engine.workflow.biz.FormFieldConditionBiz;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.workflow.WorkflowComInfo;
/*    */ import weaver.workflow.workflow.WorkflowVersion;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetCustomConditionCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetCustomConditionCmd(Map<String, Object> paramMap, User paramUser) {
/* 26 */     this.params = paramMap;
/* 27 */     this.user = paramUser;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 33 */     RecordSet recordSet = new RecordSet();
/* 34 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 35 */     String str1 = "", str2 = "", str3 = "", str4 = "";
/* 36 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 37 */     String str5 = Util.null2String(this.params.get("workflowid"));
/* 38 */     String str6 = Util.null2String(this.params.get("source"));
/* 39 */     if ("changeWf".equals(str6)) {
/* 40 */       str2 = workflowComInfo.getFormId(str5);
/* 41 */       str3 = workflowComInfo.getIsBill(str5);
/* 42 */       recordSet.executeQuery("select id,customname,workflowids from workflow_custom where formid=? and isbill=? order by id", new Object[] { str2, str3 });
/* 43 */       while (recordSet.next()) {
/* 44 */         String str = WorkflowVersion.getAllVersionStringByWFIDs(recordSet.getString("workflowids"));
/* 45 */         if (!"".equals(str) && ("," + str + ",").indexOf("," + str5 + ",") == -1)
/*    */           continue; 
/* 47 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 48 */         hashMap1.put("key", recordSet.getString("id"));
/* 49 */         hashMap1.put("showname", recordSet.getString("customname"));
/* 50 */         arrayList.add(hashMap1);
/* 51 */         if ("".equals(str1))
/* 52 */           str1 = recordSet.getString("id"); 
/*    */       } 
/* 54 */     } else if ("changeCustomid".equals(str6)) {
/* 55 */       str1 = Util.null2String(this.params.get("customid"));
/* 56 */       recordSet.executeQuery("select formid,isbill,customname,workflowids from workflow_custom where id=?", new Object[] { str1 });
/* 57 */       if (recordSet.next()) {
/* 58 */         str2 = recordSet.getString("formid");
/* 59 */         str3 = recordSet.getString("isbill");
/* 60 */         str4 = recordSet.getString("workflowids");
/* 61 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 62 */         hashMap1.put("key", str1);
/* 63 */         hashMap1.put("showname", recordSet.getString("customname"));
/* 64 */         arrayList.add(hashMap1);
/*    */       } 
/* 66 */       if (!"".equals(str5) && (!str2.equals(workflowComInfo.getFormId(str5)) || !str3.equals(workflowComInfo.getIsBill(str5))))
/* 67 */         hashMap.put("needClearWf", Boolean.valueOf(true)); 
/*    */     } 
/* 69 */     hashMap.put("customid", str1);
/* 70 */     hashMap.put("formid", str2);
/* 71 */     hashMap.put("isbill", str3);
/* 72 */     hashMap.put("wfRanges", str4);
/* 73 */     hashMap.put("customOption", arrayList);
/* 74 */     if ("".equals(str1) || "".equals(str2) || "".equals(str3)) {
/* 75 */       return (Map)hashMap;
/*    */     }
/* 77 */     String str7 = "";
/* 78 */     if (str3.equals("0")) {
/*    */ 
/*    */       
/* 81 */       String str = "select c.fieldid,d.fieldlable,b.fieldname,b.fieldhtmltype,b.type,b.fielddbtype from workflow_formdict b  inner join workflow_formfield c on b.id=c.fieldid and c.formid=" + str2 + " left join workflow_fieldlable d on d.fieldid=c.fieldid and d.formid=" + str2 + " and d.langurageid=" + this.user.getLanguage();
/*    */ 
/*    */       
/* 84 */       str7 = "select a.queryorder,a.showorder,a.fieldid as id,t.fieldname as name,t.fieldlable as label, t.fieldhtmltype as htmltype,t.type as detailtype,t.fielddbtype from Workflow_CustomDspField a,  (" + str + " union " + str.replace("workflow_formdict", "workflow_formdictdetail") + ")t  where a.fieldid=t.fieldid and a.customid=" + str1 + " and a.ifquery='1'";
/*    */     }
/* 86 */     else if (str3.equals("1")) {
/*    */ 
/*    */       
/* 89 */       str7 = "select a.queryorder,a.showorder,b.id as id,b.fieldname as name,c.labelname as label,b.fieldhtmltype as htmltype,b.type as detailtype,b.fielddbtype from Workflow_CustomDspField a  inner join workflow_billfield b on a.fieldid=b.id  left join htmllabelinfo c on b.fieldlabel=c.indexid and c.languageid=" + this.user.getLanguage() + " where a.customid=" + str1 + " and a.ifquery='1'";
/*    */     } 
/*    */     
/* 92 */     str7 = "select * from (" + str7 + ") wrapt order by queryorder,showorder,id";
/* 93 */     hashMap.put("customCondition", (new FormFieldConditionBiz()).generateConditions(str7, this.user, Util.getIntValue(str2, 0), Util.getIntValue(str3), false));
/* 94 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 99 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/engine/workflow/cmd/customQuery/GetCustomConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */