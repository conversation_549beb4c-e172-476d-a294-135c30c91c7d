/*     */ package com.customization.qc574527.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.customization.qc574527.bean.RightMenu;
/*     */ import com.customization.qc574527.bean.RightMenuType;
/*     */ import com.customization.qc574527.service.ManuallyDispatchService;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ManuallyDispatchAction
/*     */   extends BaseBean
/*     */ {
/*     */   @GET
/*     */   @Path("/getData")
/*     */   @Produces({"text/plain"})
/*     */   public String getData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  50 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  51 */     ManuallyDispatchService manuallyDispatchService = new ManuallyDispatchService();
/*  52 */     boolean bool = manuallyDispatchService.hasRight(paramHttpServletRequest, paramHttpServletResponse);
/*  53 */     String str = "";
/*  54 */     if (bool) {
/*  55 */       str = manuallyDispatchService.getListData(paramHttpServletRequest, paramHttpServletResponse);
/*     */     }
/*  57 */     hashMap.put("sessionkey", str);
/*  58 */     hashMap.put("hasRight", Boolean.valueOf(bool));
/*  59 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/condition")
/*     */   @Produces({"text/plain"})
/*     */   public String getConDition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  70 */     ManuallyDispatchService manuallyDispatchService = new ManuallyDispatchService();
/*     */     try {
/*  72 */       hashMap.put("condition", ManuallyDispatchService.getSearchCondition(paramHttpServletRequest, paramHttpServletResponse));
/*  73 */     } catch (Exception exception) {
/*  74 */       exception.printStackTrace();
/*  75 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  76 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/*  78 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/rightMenu")
/*     */   @Produces({"text/plain"})
/*     */   public String getRightMenu(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  88 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  90 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  91 */       ArrayList<RightMenu> arrayList = new ArrayList();
/*  92 */       int i = user.getLanguage();
/*     */       
/*  94 */       String str = Util.null2String(getPropValue("WorkflowDispatch", "mutlDispatch")).trim();
/*  95 */       if ("1".equals(str)) {
/*  96 */         arrayList.add(new RightMenu(i, RightMenuType.BTN_BATCHSUBMIT, "batchDispatch()", true, true));
/*     */       }
/*  98 */       hashMap.put("rightMenus", arrayList);
/*  99 */     } catch (Exception exception) {
/* 100 */       exception.printStackTrace();
/* 101 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 102 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 104 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/chooseDialog")
/*     */   @Produces({"text/plain"})
/*     */   public String chooseDialog(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 119 */     ManuallyDispatchService manuallyDispatchService = new ManuallyDispatchService();
/* 120 */     Map map = manuallyDispatchService.chooseDialog(paramHttpServletRequest, paramHttpServletResponse);
/* 121 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/onSubmit")
/*     */   @Produces({"text/plain"})
/*     */   public String onSubmit(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 137 */     ManuallyDispatchService manuallyDispatchService = new ManuallyDispatchService();
/*     */     
/* 139 */     Map map = manuallyDispatchService.submitDispatch(paramHttpServletRequest, paramHttpServletResponse);
/* 140 */     return JSON.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/grabSingle")
/*     */   @Produces({"text/plain"})
/*     */   public String grabSingle(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 154 */     ManuallyDispatchService manuallyDispatchService = new ManuallyDispatchService();
/*     */     
/* 156 */     Map map = manuallyDispatchService.grabSingle(paramHttpServletRequest, paramHttpServletResponse);
/* 157 */     return JSON.toJSONString(map);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/web/ManuallyDispatchAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */