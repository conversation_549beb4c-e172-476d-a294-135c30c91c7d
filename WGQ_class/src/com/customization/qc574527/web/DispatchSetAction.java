/*     */ package com.customization.qc574527.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.customization.qc574527.bean.RightMenu;
/*     */ import com.customization.qc574527.bean.RightMenuType;
/*     */ import com.customization.qc574527.service.DispatchSetService;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DispatchSetAction
/*     */   extends BaseBean
/*     */ {
/*     */   @GET
/*     */   @Path("/getData")
/*     */   @Produces({"text/plain"})
/*     */   public String getData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  50 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  51 */     DispatchSetService dispatchSetService = new DispatchSetService();
/*  52 */     boolean bool = dispatchSetService.hasRight(paramHttpServletRequest, paramHttpServletResponse);
/*  53 */     String str = "";
/*  54 */     if (bool) {
/*  55 */       str = dispatchSetService.getListData(paramHttpServletRequest, paramHttpServletResponse);
/*     */     }
/*  57 */     hashMap.put("sessionkey", str);
/*  58 */     hashMap.put("hasRight", Boolean.valueOf(bool));
/*  59 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/condition")
/*     */   @Produces({"text/plain"})
/*     */   public String getConDition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  70 */     DispatchSetService dispatchSetService = new DispatchSetService();
/*     */     try {
/*  72 */       hashMap.put("condition", DispatchSetService.getSearchCondition(paramHttpServletRequest, paramHttpServletResponse));
/*  73 */     } catch (Exception exception) {
/*  74 */       exception.printStackTrace();
/*  75 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  76 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/*  78 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/rightMenu")
/*     */   @Produces({"text/plain"})
/*     */   public String getRightMenu(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  88 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  90 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  91 */       ArrayList<RightMenu> arrayList = new ArrayList();
/*  92 */       int i = user.getLanguage();
/*  93 */       arrayList.add(new RightMenu(i, RightMenuType.BTN_ADD, "onNewRule()", false, true));
/*  94 */       arrayList.add(new RightMenu(i, RightMenuType.BTN_DELETE, "onDelete()", true, true));
/*  95 */       hashMap.put("rightMenus", arrayList);
/*  96 */     } catch (Exception exception) {
/*  97 */       exception.printStackTrace();
/*  98 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  99 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 101 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/chooseDialog")
/*     */   @Produces({"text/plain"})
/*     */   public String chooseDialog(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 116 */     DispatchSetService dispatchSetService = new DispatchSetService();
/* 117 */     Map map = dispatchSetService.chooseDialog(paramHttpServletRequest, paramHttpServletResponse);
/* 118 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getRuleDetailList")
/*     */   @Produces({"text/plain"})
/*     */   public String getRuleDetailList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 133 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 134 */     DispatchSetService dispatchSetService = new DispatchSetService();
/* 135 */     String str = dispatchSetService.getRuleDetailList(paramHttpServletRequest, paramHttpServletResponse);
/* 136 */     hashMap.put("sessionkey1", str);
/* 137 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/saveDialog1")
/*     */   @Produces({"text/plain"})
/*     */   public String saveDialog1(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 151 */     String str = Util.null2String(paramHttpServletRequest.getParameter("dialogType"));
/* 152 */     DispatchSetService dispatchSetService = new DispatchSetService();
/*     */     
/* 154 */     Map map = dispatchSetService.saveRuleInfo(paramHttpServletRequest, paramHttpServletResponse);
/* 155 */     return JSON.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/deleteRule")
/*     */   @Produces({"text/plain"})
/*     */   public String deleteRule(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 169 */     DispatchSetService dispatchSetService = new DispatchSetService();
/*     */     
/* 171 */     Map map = dispatchSetService.deleteRule(paramHttpServletRequest, paramHttpServletResponse);
/* 172 */     return JSON.toJSONString(map);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/web/DispatchSetAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */