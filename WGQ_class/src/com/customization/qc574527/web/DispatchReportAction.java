/*     */ package com.customization.qc574527.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.customization.qc574527.bean.RightMenu;
/*     */ import com.customization.qc574527.bean.RightMenuType;
/*     */ import com.customization.qc574527.service.DispatchReportService;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DispatchReportAction
/*     */   extends BaseBean
/*     */ {
/*     */   @GET
/*     */   @Path("/getData")
/*     */   @Produces({"text/plain"})
/*     */   public String getData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  54 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  55 */     DispatchReportService dispatchReportService = new DispatchReportService();
/*  56 */     String str = dispatchReportService.getListData(paramHttpServletRequest, paramHttpServletResponse);
/*  57 */     hashMap.put("sessionkey", str);
/*  58 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/condition")
/*     */   @Produces({"text/plain"})
/*     */   public String getConDition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     DispatchReportService dispatchReportService = new DispatchReportService();
/*     */     try {
/*  71 */       hashMap.put("condition", DispatchReportService.getSearchCondition(paramHttpServletRequest, paramHttpServletResponse));
/*  72 */     } catch (Exception exception) {
/*  73 */       exception.printStackTrace();
/*  74 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  75 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/*  77 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/rightMenu")
/*     */   @Produces({"text/plain"})
/*     */   public String getRightMenu(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  87 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  89 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  90 */       ArrayList<RightMenu> arrayList = new ArrayList();
/*  91 */       int i = user.getLanguage();
/*  92 */       arrayList.add(new RightMenu(i, RightMenuType.BTN_EXCEL, "doExcel()", true, true));
/*  93 */       hashMap.put("rightMenus", arrayList);
/*  94 */     } catch (Exception exception) {
/*  95 */       exception.printStackTrace();
/*  96 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  97 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/*  99 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/exportExcel")
/*     */   @Produces({"text/plain"})
/*     */   public String exportExcel(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 114 */     DispatchReportService dispatchReportService = new DispatchReportService();
/* 115 */     Map map = dispatchReportService.exportExcel(paramHttpServletRequest, paramHttpServletResponse);
/* 116 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/web/DispatchReportAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */