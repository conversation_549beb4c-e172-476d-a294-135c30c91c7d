/*     */ package com.customization.qc574527.service;
/*     */ 
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.customization.qc574527.util.DispatchPageUidFactory;
/*     */ import com.customization.qc574527.util.DispatchTransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ExcelFile;
/*     */ import weaver.file.ExcelRow;
/*     */ import weaver.file.ExcelSheet;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DispatchReportService
/*     */ {
/*     */   public static List<Map<String, Object>> getSearchCondition(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  56 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  57 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*     */     
/*  59 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  60 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  61 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */     
/*  64 */     hashMap2 = new HashMap<>();
/*  65 */     hashMap2.put("colSpan", Integer.valueOf(2));
/*  66 */     hashMap2.put("conditionType", "INPUT");
/*  67 */     hashMap2.put("domkey", new String[] { "resourcename" });
/*  68 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/*  69 */     hashMap2.put("isQuickSearch", Boolean.valueOf(true));
/*  70 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(21965, user.getLanguage()));
/*  71 */     hashMap2.put("labelcol", Integer.valueOf(6));
/*  72 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/*  73 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/*  76 */     hashMap2 = new HashMap<>();
/*  77 */     List list = DispatchTransMethod.getDateTypeOptions("", user.getLanguage());
/*  78 */     Map map = DispatchTransMethod.getFormItemForBrowserDate("orderdate", SystemEnv.getHtmlLabelName(97, user.getLanguage()), "", 2, list, null);
/*     */     
/*  80 */     arrayList2.add(map);
/*     */     
/*  82 */     hashMap1.put("title", SystemEnv.getHtmlLabelName(32905, user.getLanguage()));
/*  83 */     hashMap1.put("defaultshow", Boolean.valueOf(true));
/*  84 */     hashMap1.put("items", arrayList2);
/*  85 */     arrayList1.add(hashMap1);
/*     */     
/*  87 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getListData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  99 */     String str1 = "com.customization.qc574527.util.DispatchTransMethod";
/*     */     
/* 101 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 102 */     boolean bool = false;
/* 103 */     if (HrmUserVarify.checkUserRight("workflow:Dispatching", user)) {
/* 104 */       bool = true;
/*     */     }
/* 106 */     String str2 = DispatchPageUidFactory.getDispatchPageUid("dispatchcount");
/*     */     
/* 108 */     String str3 = str2;
/*     */ 
/*     */     
/* 111 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 113 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 115 */     Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 116 */     while (enumeration.hasMoreElements()) {
/* 117 */       String str = enumeration.nextElement();
/* 118 */       hashMap.put(str, Util.null2String(paramHttpServletRequest.getParameter(str)));
/*     */     } 
/*     */     
/* 121 */     String str4 = "";
/* 122 */     String str5 = "";
/* 123 */     String str6 = " where 1 = 1 and requestid>0 ";
/* 124 */     String str7 = "";
/*     */ 
/*     */     
/* 127 */     String str8 = Util.null2String((String)hashMap.get("resourcename"));
/*     */ 
/*     */     
/* 130 */     String str9 = Util.null2String((String)hashMap.get("orderdate_selectType"));
/* 131 */     String str10 = Util.null2String((String)hashMap.get("orderdate_fromDate"));
/* 132 */     String str11 = Util.null2String((String)hashMap.get("orderdate_toDate"));
/* 133 */     Map map = DispatchTransMethod.getFromDateAndEndDate(str9, str10, str11);
/* 134 */     String str12 = Util.null2String((String)map.get("fromDate"));
/* 135 */     String str13 = Util.null2String((String)map.get("toDate"));
/*     */     
/* 137 */     if ("".equals(str12)) {
/* 138 */       str12 = TimeUtil.getCurrentDateString();
/*     */     }
/*     */     
/* 141 */     if ("".equals(str13)) {
/* 142 */       str13 = TimeUtil.getCurrentDateString();
/*     */     }
/*     */     
/* 145 */     if (!"".equals(str8)) {
/* 146 */       str6 = str6 + " and exists (select 1 from hrmresource t where t.lastname like '%" + str8 + "%' and workflow_dispatchrecord.userid=t.id) ";
/*     */     }
/*     */     
/* 149 */     if (!"".equals(str12)) {
/* 150 */       str6 = str6 + " and operationdate >= '" + str12 + "' ";
/*     */     }
/*     */     
/* 153 */     if (!"".equals(str13)) {
/* 154 */       str6 = str6 + " and operationdate <= '" + str13 + "'";
/*     */     }
/*     */     
/* 157 */     str5 = str5 + " workflow_dispatchrecord ";
/*     */     
/* 159 */     str7 = str7 + " userid ";
/*     */     
/* 161 */     String str14 = Util.null2String(recordSet.getPropValue("WorkflowDispatch", "grabSingleCount")).trim();
/*     */     
/* 163 */     String str15 = str12 + "+" + str13;
/*     */     
/* 165 */     str6 = str6 + " group by userid ";
/* 166 */     String str16 = "";
/* 167 */     str4 = str4 + " userid,userid as username,userid as autocount,userid as mancount,userid as gracount,userid as allcount ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 178 */     str16 = " <table  pageId=\"" + str3 + "\" pageUid=\"" + str2 + "\" instanceid=\"\" tabletype=\"none\" pagesize=\"" + PageIdConst.getPageSize(str3, user.getUID()) + "\" >       <sql backfields=\"" + str4 + "\" sqlform=\"" + str5 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str6) + "\"  sqlorderby=\"" + str7 + "\"  sqlprimarykey=\"userid\" sqlsortway=\"ASC\" sqlisdistinct=\"true\" />       <head>           <col width=\"8%\"  text=\"" + SystemEnv.getHtmlLabelName(1933, user.getLanguage()) + "\" column=\"userid\" transmethod=\"weaver.hrm.resource.ResourceComInfo.getWorkcode\" />           <col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(81812, user.getLanguage()) + "\" column=\"username\" orderkey=\"userid\" transmethod=\"weaver.hrm.resource.ResourceComInfo.getMulResourcename1\" /> \t\t\t<col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(10000188, user.getLanguage()) + SystemEnv.getHtmlLabelName(1331, user.getLanguage()) + "\" column=\"autocount\" otherpara=\"" + str15 + "\" transmethod=\"" + str1 + ".getAutoCount\"/> \t\t\t<col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(503747, user.getLanguage()) + SystemEnv.getHtmlLabelName(1331, user.getLanguage()) + "\" column=\"mancount\" otherpara=\"" + str15 + "\" transmethod=\"" + str1 + ".getManuallyCount\"/>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 183 */     if ("1".equals(str14)) {
/* 184 */       str16 = str16 + " \t\t\t<col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(10000405, user.getLanguage()) + "\" column=\"gracount\" otherpara=\"" + str15 + "\" transmethod=\"" + str1 + ".getGrabCount\"/>";
/*     */     }
/*     */ 
/*     */     
/* 188 */     str16 = str16 + " \t\t\t<col width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(10000406, user.getLanguage()) + SystemEnv.getHtmlLabelName(27602, user.getLanguage()) + "\" column=\"allcount\" otherpara=\"" + str15 + "\" transmethod=\"" + str1 + ".getDispatchCount\"/>       </head> </table>";
/*     */ 
/*     */ 
/*     */     
/* 192 */     String str17 = str2 + "_" + Util.getEncrypt(Util.getRandom());
/* 193 */     Util_TableMap.setVal(str17, str16);
/* 194 */     return str17;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> exportExcel(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 204 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 205 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 207 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 208 */       DispatchTransMethod dispatchTransMethod = new DispatchTransMethod();
/* 209 */       ExcelSheet excelSheet = new ExcelSheet();
/* 210 */       String str1 = "userid";
/* 211 */       String str2 = "";
/* 212 */       String str3 = " where 1 = 1 and requestid>0 ";
/* 213 */       String str4 = "";
/* 214 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */       
/* 216 */       String str5 = Util.null2String(paramHttpServletRequest.getParameter("resourcename"));
/*     */ 
/*     */       
/* 219 */       String str6 = Util.null2String(paramHttpServletRequest.getParameter("orderdate_selectType"));
/* 220 */       String str7 = Util.null2String(paramHttpServletRequest.getParameter("orderdate_fromDate"));
/* 221 */       String str8 = Util.null2String(paramHttpServletRequest.getParameter("orderdate_toDate"));
/* 222 */       Map map = DispatchTransMethod.getFromDateAndEndDate(str6, str7, str8);
/* 223 */       String str9 = Util.null2String((String)map.get("fromDate"));
/* 224 */       String str10 = Util.null2String((String)map.get("toDate"));
/*     */       
/* 226 */       if ("".equals(str9)) {
/* 227 */         str9 = TimeUtil.getCurrentDateString();
/*     */       }
/*     */       
/* 230 */       if ("".equals(str10)) {
/* 231 */         str10 = TimeUtil.getCurrentDateString();
/*     */       }
/*     */       
/* 234 */       if (!"".equals(str5)) {
/* 235 */         str3 = str3 + " and exists (select 1 from hrmresource t where t.lastname like '%" + str5 + "%' and workflow_dispatchrecord.userid=t.id) ";
/*     */       }
/*     */       
/* 238 */       if (!"".equals(str9)) {
/* 239 */         str3 = str3 + " and operationdate >= '" + str9 + "' ";
/*     */       }
/*     */       
/* 242 */       if (!"".equals(str10)) {
/* 243 */         str3 = str3 + " and operationdate <= '" + str10 + "'";
/*     */       }
/* 245 */       str2 = str2 + " workflow_dispatchrecord ";
/* 246 */       str4 = str4 + " order by userid ASC ";
/*     */       
/* 248 */       String str11 = Util.null2String(recordSet.getPropValue("WorkflowDispatch", "grabSingleCount")).trim();
/*     */       
/* 250 */       String str12 = str9 + "+" + str10;
/*     */       
/* 252 */       str3 = str3 + " group by userid ";
/*     */       
/* 254 */       String str13 = "select " + str1 + " from " + str2 + str3 + str4;
/*     */       
/* 256 */       ExcelRow excelRow = excelSheet.newExcelRow();
/*     */       
/* 258 */       excelRow.addStringValue(SystemEnv.getHtmlLabelName(1933, user.getLanguage()));
/*     */       
/* 260 */       excelRow.addStringValue(SystemEnv.getHtmlLabelName(1867, user.getLanguage()) + 
/* 261 */           SystemEnv.getHtmlLabelName(413, user.getLanguage()));
/*     */       
/* 263 */       excelRow.addStringValue(SystemEnv.getHtmlLabelName(81855, user.getLanguage()) + 
/* 264 */           SystemEnv.getHtmlLabelName(128469, user.getLanguage()) + 
/* 265 */           SystemEnv.getHtmlLabelName(1331, user.getLanguage()));
/*     */       
/* 267 */       excelRow.addStringValue(SystemEnv.getHtmlLabelName(128468, user.getLanguage()) + 
/* 268 */           SystemEnv.getHtmlLabelName(128469, user.getLanguage()) + 
/* 269 */           SystemEnv.getHtmlLabelName(1331, user.getLanguage()));
/* 270 */       if ("1".equals(str11))
/*     */       {
/* 272 */         excelRow.addStringValue(SystemEnv.getHtmlLabelName(381925, user.getLanguage()) + 
/* 273 */             SystemEnv.getHtmlLabelName(1331, user.getLanguage()));
/*     */       }
/*     */       
/* 276 */       excelRow.addStringValue(SystemEnv.getHtmlLabelName(128469, user.getLanguage()) + 
/* 277 */           SystemEnv.getHtmlLabelName(19083, user.getLanguage()) + 
/* 278 */           SystemEnv.getHtmlLabelName(27602, user.getLanguage()));
/* 279 */       excelSheet.addColumnwidth(4000);
/*     */       
/* 281 */       ExcelFile excelFile = new ExcelFile();
/* 282 */       excelFile.init();
/* 283 */       recordSet.execute(str13);
/* 284 */       while (recordSet.next()) {
/* 285 */         excelRow = excelSheet.newExcelRow();
/* 286 */         String str = Util.null2String(recordSet.getString(1));
/*     */         
/* 288 */         excelRow.addStringValue(resourceComInfo.getWorkcode(str));
/*     */         
/* 290 */         excelRow.addStringValue(resourceComInfo.getResourcename(str));
/*     */         
/* 292 */         excelRow.addStringValue(dispatchTransMethod.getAutoCount(str, str9 + "+" + str10));
/*     */         
/* 294 */         excelRow.addStringValue(dispatchTransMethod.getManuallyCount(str, str9 + "+" + str10));
/* 295 */         if ("1".equals(str11))
/*     */         {
/* 297 */           excelRow.addStringValue(dispatchTransMethod.getGrabCount(str, str9 + "+" + str10));
/*     */         }
/*     */         
/* 300 */         excelRow.addStringValue(dispatchTransMethod.getDispatchCount(str, str9 + "+" + str10));
/*     */       } 
/* 302 */       excelFile.setFilename(SystemEnv.getHtmlLabelName(10000407, user.getLanguage()));
/* 303 */       excelFile.addSheet(SystemEnv.getHtmlLabelName(352, user.getLanguage()), excelSheet);
/* 304 */       paramHttpServletRequest.getSession(true).setAttribute("ExcelFile", excelFile);
/* 305 */       hashMap.put("msg", "success");
/* 306 */     } catch (Exception exception) {
/* 307 */       recordSet.writeLog(exception.getMessage());
/*     */     } 
/* 309 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/service/DispatchReportService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */