/*     */ package com.customization.qc574527.service;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.customization.qc574527.bean.RightMenu;
/*     */ import com.customization.qc574527.bean.RightMenuType;
/*     */ import com.customization.qc574527.util.DispatchPageUidFactory;
/*     */ import com.customization.qc574527.util.DispatchTransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DispatchSetService
/*     */ {
/*     */   public boolean hasRight(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  42 */     boolean bool = false;
/*     */     
/*  44 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  45 */     if (HrmUserVarify.checkUserRight("workflow:Dispatching", user)) {
/*  46 */       bool = true;
/*     */     }
/*  48 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Map<String, Object>> getSearchCondition(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  55 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  56 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*     */     
/*  58 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  59 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  60 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */     
/*  63 */     hashMap2 = new HashMap<>();
/*  64 */     hashMap2.put("colSpan", Integer.valueOf(2));
/*  65 */     hashMap2.put("conditionType", "INPUT");
/*  66 */     hashMap2.put("domkey", new String[] { "rulename" });
/*  67 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/*  68 */     hashMap2.put("isQuickSearch", Boolean.valueOf(true));
/*  69 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(19829, user.getLanguage()));
/*  70 */     hashMap2.put("labelcol", Integer.valueOf(6));
/*  71 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/*  72 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/*  75 */     hashMap2 = new HashMap<>();
/*  76 */     hashMap2.put("colSpan", Integer.valueOf(2));
/*  77 */     hashMap2.put("conditionType", "BROWSER");
/*  78 */     hashMap2.put("domkey", new String[] { "disworker" });
/*  79 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/*  80 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(10000408, user.getLanguage()));
/*  81 */     hashMap2.put("labelcol", Integer.valueOf(6));
/*  82 */     hashMap2.put("hasAdvanceSerach", Boolean.valueOf(true));
/*  83 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/*  84 */     hashMap2.put("rules", "");
/*  85 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  86 */     hashMap3.put("type", "1");
/*  87 */     hashMap3.put("isSingle", Boolean.valueOf(true));
/*  88 */     hashMap3.put("title", SystemEnv.getHtmlLabelName(33210, user.getLanguage()));
/*  89 */     hashMap2.put("browserConditionParam", hashMap3);
/*  90 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/*  93 */     hashMap2 = new HashMap<>();
/*  94 */     hashMap2.put("colSpan", Integer.valueOf(2));
/*  95 */     hashMap2.put("conditionType", "BROWSER");
/*  96 */     hashMap2.put("domkey", new String[] { "disrole" });
/*  97 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/*  98 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(10000409, user.getLanguage()));
/*  99 */     hashMap2.put("labelcol", Integer.valueOf(6));
/* 100 */     hashMap2.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 101 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/* 102 */     hashMap2.put("rules", "");
/* 103 */     hashMap3 = new HashMap<>();
/* 104 */     hashMap3.put("type", "65");
/* 105 */     hashMap3.put("isSingle", Boolean.valueOf(true));
/* 106 */     hashMap3.put("title", SystemEnv.getHtmlLabelName(33210, user.getLanguage()));
/* 107 */     hashMap2.put("browserConditionParam", hashMap3);
/* 108 */     arrayList2.add(hashMap2);
/*     */     
/* 110 */     hashMap1.put("title", SystemEnv.getHtmlLabelName(32905, user.getLanguage()));
/* 111 */     hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 112 */     hashMap1.put("items", arrayList2);
/* 113 */     arrayList1.add(hashMap1);
/*     */     
/* 115 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getListData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 127 */     String str1 = "com.customization.qc574527.util.DispatchTransMethod";
/*     */     
/* 129 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/* 131 */     String str2 = DispatchPageUidFactory.getDispatchPageUid("dispatchsetlist");
/*     */     
/* 133 */     String str3 = str2;
/*     */ 
/*     */     
/* 136 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 138 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 140 */     Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 141 */     while (enumeration.hasMoreElements()) {
/* 142 */       String str = enumeration.nextElement();
/* 143 */       hashMap.put(str, Util.null2String(paramHttpServletRequest.getParameter(str)));
/*     */     } 
/*     */ 
/*     */     
/* 147 */     String str4 = "";
/* 148 */     String str5 = " workflow_dispatch t1 ";
/* 149 */     String str6 = " where 1=1 and (t1.rulename is not null or t1.rulename !='') ";
/* 150 */     String str7 = "";
/*     */ 
/*     */     
/* 153 */     String str8 = Util.null2String((String)hashMap.get("rulename"));
/* 154 */     if (!"".equals(str8)) {
/* 155 */       str6 = str6 + " and t1.rulename like '%" + str8 + "%'";
/*     */     }
/*     */ 
/*     */     
/* 159 */     String str9 = Util.null2String((String)hashMap.get("disworker"));
/* 160 */     if (!"".equals(str9)) {
/* 161 */       if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/* 162 */         str6 = str6 + " and ','+t1.disworker+',' like '%," + str9 + ",%' and distype=0 ";
/* 163 */       } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/* 164 */         str6 = str6 + " and CONCAT(',',t1.disworker,',') like '%," + str9 + ",%' and distype=0 ";
/*     */       } else {
/* 166 */         str6 = str6 + " and ','||t1.disworker||',' like '%," + str9 + ",%' and distype=0 ";
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 171 */     String str10 = Util.null2String((String)hashMap.get("disrole"));
/* 172 */     if (!"".equals(str10)) {
/* 173 */       if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/* 174 */         str6 = str6 + " and ','+t1.disworker+',' like '%," + str10 + ",%' and distype=1 ";
/* 175 */       } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/* 176 */         str6 = str6 + " and CONCAT(',',t1.disworker,',') like '%," + str10 + ",%' and distype=1 ";
/*     */       } else {
/* 178 */         str6 = str6 + " and ','||t1.disworker||',' like '%," + str10 + ",%' and distype=1 ";
/*     */       } 
/*     */     }
/*     */     
/* 182 */     str7 = str7 + " t1.id ";
/*     */ 
/*     */     
/* 185 */     String str11 = "checkbox";
/*     */     
/* 187 */     str4 = str4 + " t1.id,t1.rulename,t1.saturationrule,t1.disinstructions, t1.disworkload,t1.disworker,t1.distype";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 201 */     String str12 = "<table  pageId=\"" + str3 + "\" pageUid=\"" + str2 + "\" instanceid=\"DispatchSetList_1\"  tabletype=\"" + str11 + "\"  pagesize=\"" + PageIdConst.getPageSize(str3, user.getUID()) + "\"  ><sql backfields=\"" + str4 + "\" sqlform=\"" + str5 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str6) + "\" sqlprimarykey=\"t1.id\" sqlorderby=\"" + str7 + "\" sqlsortway=\"asc\" sqlisdistinct=\"true\" /><head><col width=\"20%\" text=\"\" column=\"id\"  hide=\"true\" /><col width=\"15%\" display=\"true\" text=\"" + SystemEnv.getHtmlLabelName(19829, user.getLanguage()) + "\" column=\"rulename\"  orderkey=\"rulename\" /><col width=\"15%\"  text=\"" + SystemEnv.getHtmlLabelName(10000408, user.getLanguage()) + "\" column=\"disworker\" orderkey=\"t1.disworker\" otherpara=\"column:distype+" + user.getLanguage() + "\" transmethod=\"" + str1 + ".getShowDisObj\"/><col width=\"15%\"  text=\"" + SystemEnv.getHtmlLabelName(10000410, user.getLanguage()) + "\" column=\"saturationrule\" orderkey=\"t1.saturationrule\" otherpara=\"" + user.getLanguage() + "\"  transmethod=\"" + str1 + ".getSaturationrule\"/><col width=\"9%\"  text=\"" + SystemEnv.getHtmlLabelName(10000411, user.getLanguage()) + "\" column=\"disworkload\" orderkey=\"t1.disworkload\"   /><col width=\"10%\" text=\"" + SystemEnv.getHtmlLabelName(10000412, user.getLanguage()) + "\" column=\"disinstructions\" orderkey=\"t1.disinstructions\" /></head>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 206 */     String str13 = "<operates width=\"5%\">    <operate href=\"javascript:onEdit()\" text=\"" + SystemEnv.getHtmlLabelName(93, user.getLanguage()) + "\" target=\"_self\" index=\"0\"/>    <operate href=\"javascript:onDelete()\" text=\"" + SystemEnv.getHtmlLabelName(91, user.getLanguage()) + "\" target=\"_self\" index=\"1\"/></operates>";
/*     */     
/* 208 */     str12 = str12 + str13 + "</table>";
/* 209 */     String str14 = str2 + "_" + Util.getEncrypt(Util.getRandom());
/* 210 */     Util_TableMap.setVal(str14, str12);
/* 211 */     return str14;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> chooseDialog(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 221 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 223 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 224 */       ConditionFactory conditionFactory = new ConditionFactory(user);
/* 225 */       RecordSet recordSet = new RecordSet();
/* 226 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("dialogType"));
/* 227 */       int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("ruleid")));
/* 228 */       int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("ruledetailid")));
/*     */       
/* 230 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*     */ 
/*     */       
/* 233 */       ArrayList<RightMenu> arrayList = new ArrayList();
/* 234 */       int k = user.getLanguage();
/*     */ 
/*     */       
/* 237 */       String str2 = "";
/*     */       
/* 239 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 240 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 241 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/*     */       
/* 243 */       if ("onNewRule".equals(str1) || "onEditRule".equals(str1)) {
/* 244 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(1361, user.getLanguage()));
/*     */         
/* 246 */         String str3 = "";
/*     */         
/* 248 */         String str4 = "";
/*     */         
/* 250 */         String str5 = "";
/*     */         
/* 252 */         String str6 = "";
/*     */         
/* 254 */         String str7 = "";
/*     */         
/* 256 */         String str8 = "";
/*     */         
/* 258 */         if ("onNewRule".equals(str1)) {
/*     */           
/* 260 */           str2 = SystemEnv.getHtmlLabelName(84551, user.getLanguage());
/*     */         } else {
/*     */           
/* 263 */           str2 = SystemEnv.getHtmlLabelName(84552, user.getLanguage());
/* 264 */           String str = "select * from workflow_dispatch where id = " + i;
/* 265 */           recordSet.execute(str);
/* 266 */           if (recordSet.next()) {
/*     */             
/* 268 */             str3 = Util.null2String(recordSet.getString("rulename"));
/*     */             
/* 270 */             str4 = Util.null2String(recordSet.getString("disworker"));
/*     */             
/* 272 */             str8 = Util.null2String(recordSet.getString("distype"));
/*     */             
/* 274 */             str5 = Util.null2String(recordSet.getString("disworkload"));
/*     */             
/* 276 */             str6 = Util.null2String(recordSet.getString("disinstructions"));
/*     */             
/* 278 */             str7 = Util.null2String(recordSet.getString("saturationrule"));
/*     */           } 
/*     */         } 
/*     */         
/* 282 */         if ("".equals(str8)) {
/* 283 */           str8 = "0";
/*     */         }
/* 285 */         if ("".equals(str7)) {
/* 286 */           str7 = "0";
/*     */         }
/*     */         
/* 289 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */         
/* 291 */         hashMap2 = new HashMap<>();
/* 292 */         hashMap2.put("colSpan", Integer.valueOf(2));
/* 293 */         hashMap2.put("conditionType", "INPUT");
/* 294 */         hashMap2.put("domkey", new String[] { "rulename" });
/* 295 */         hashMap2.put("fieldcol", Integer.valueOf(12));
/* 296 */         hashMap2.put("label", SystemEnv.getHtmlLabelName(19829, user.getLanguage()));
/* 297 */         hashMap2.put("labelcol", Integer.valueOf(6));
/* 298 */         hashMap2.put("viewAttr", Integer.valueOf(3));
/* 299 */         hashMap2.put("value", str3);
/* 300 */         hashMap2.put("rules", "required");
/* 301 */         arrayList2.add(hashMap2);
/*     */ 
/*     */         
/* 304 */         hashMap2 = new HashMap<>();
/* 305 */         hashMap2.put("colSpan", Integer.valueOf(2));
/* 306 */         hashMap2.put("conditionType", "TEXTAREA");
/* 307 */         hashMap2.put("domkey", new String[] { "disinstructions" });
/* 308 */         hashMap2.put("fieldcol", Integer.valueOf(12));
/* 309 */         hashMap2.put("label", SystemEnv.getHtmlLabelName(128469, user.getLanguage()) + 
/* 310 */             SystemEnv.getHtmlLabelName(579, user.getLanguage()) + 
/* 311 */             SystemEnv.getHtmlLabelName(85, user.getLanguage()));
/* 312 */         hashMap2.put("labelcol", Integer.valueOf(6));
/* 313 */         hashMap2.put("viewAttr", Integer.valueOf(2));
/* 314 */         hashMap2.put("value", str6);
/* 315 */         arrayList2.add(hashMap2);
/*     */ 
/*     */         
/* 318 */         hashMap2 = new HashMap<>();
/* 319 */         hashMap2.put("formItemType", "SELECT_LINKAGE");
/* 320 */         hashMap2.put("conditionType", "SELECT_LINKAGE");
/* 321 */         hashMap2.put("labelcol", Integer.valueOf(6));
/* 322 */         hashMap2.put("colSpan", Integer.valueOf(2));
/* 323 */         hashMap2.put("selectWidth", "100px");
/* 324 */         hashMap2.put("domkey", new String[] { "distype", "dishrm", "disrole" });
/* 325 */         hashMap2.put("fieldcol", Integer.valueOf(17));
/* 326 */         hashMap2.put("viewAttr", Integer.valueOf(3));
/* 327 */         hashMap2.put("rules", "selectLinkageRequired");
/* 328 */         hashMap2.put("label", SystemEnv.getHtmlLabelName(10000413, user.getLanguage()));
/*     */         
/* 330 */         ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 331 */         String str9 = "";
/* 332 */         String str10 = "";
/* 333 */         if ("0".equals(str8)) {
/* 334 */           str9 = str4;
/* 335 */         } else if ("1".equals(str8)) {
/* 336 */           str10 = str4;
/*     */         } 
/* 338 */         arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(179, user.getLanguage()), "0".equals(str8)));
/* 339 */         arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(122, user.getLanguage()), "1".equals(str8)));
/* 340 */         hashMap2.put("options", arrayList3);
/*     */         
/* 342 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 343 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 344 */         Map<Object, Object> map = DispatchTransMethod.getFormItemForBrowser("dishrm", SystemEnv.getHtmlLabelName(179, user.getLanguage()), "17", str9, 3, null, "", null, null);
/*     */         
/* 346 */         hashMap3.put("0", map);
/*     */         
/* 348 */         map = new HashMap<>();
/* 349 */         map = DispatchTransMethod.getFormItemForBrowser("disrole", SystemEnv.getHtmlLabelName(122, user.getLanguage()), "65", str10, 3, null, "", null, null);
/*     */         
/* 351 */         hashMap3.put("1", map);
/*     */         
/* 353 */         hashMap2.put("selectLinkageDatas", hashMap3);
/* 354 */         arrayList2.add(hashMap2);
/*     */ 
/*     */         
/* 357 */         hashMap2 = new HashMap<>();
/* 358 */         hashMap2.put("colSpan", Integer.valueOf(2));
/* 359 */         hashMap2.put("conditionType", "INPUTNUMBER");
/* 360 */         hashMap2.put("domkey", new String[] { "disworkload" });
/* 361 */         hashMap2.put("fieldcol", Integer.valueOf(12));
/* 362 */         hashMap2.put("label", SystemEnv.getHtmlLabelName(128469, user.getLanguage()) + 
/* 363 */             SystemEnv.getHtmlLabelName(503745, user.getLanguage()));
/* 364 */         hashMap2.put("labelcol", Integer.valueOf(6));
/* 365 */         hashMap2.put("viewAttr", Integer.valueOf(3));
/* 366 */         hashMap2.put("rules", "required");
/* 367 */         hashMap2.put("value", str5);
/* 368 */         hashMap2.put("precision", "0");
/* 369 */         arrayList2.add(hashMap2);
/*     */ 
/*     */         
/* 372 */         hashMap2 = new HashMap<>();
/* 373 */         hashMap2.put("colSpan", Integer.valueOf(2));
/* 374 */         hashMap2.put("conditionType", "SELECT");
/* 375 */         hashMap2.put("domkey", new String[] { "saturationrule" });
/* 376 */         hashMap2.put("fieldcol", Integer.valueOf(12));
/* 377 */         hashMap2.put("label", SystemEnv.getHtmlLabelName(382732, user.getLanguage()) + 
/* 378 */             SystemEnv.getHtmlLabelName(579, user.getLanguage()));
/* 379 */         hashMap2.put("labelcol", Integer.valueOf(6));
/* 380 */         hashMap2.put("viewAttr", Integer.valueOf(2));
/* 381 */         hashMap2.put("value", str7);
/*     */         
/* 383 */         arrayList3 = new ArrayList<>();
/* 384 */         arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(19083, user.getLanguage()) + 
/* 385 */               SystemEnv.getHtmlLabelName(503757, user.getLanguage()), "0".equals(str7)));
/* 386 */         arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(539, user.getLanguage()) + 
/* 387 */               SystemEnv.getHtmlLabelName(503757, user.getLanguage()), "1".equals(str7)));
/* 388 */         hashMap2.put("options", arrayList3);
/*     */         
/* 390 */         arrayList2.add(hashMap2);
/*     */ 
/*     */ 
/*     */         
/* 394 */         arrayList.add(new RightMenu(k, RightMenuType.BTN_SAVE, "saveDialog1()", true, true));
/* 395 */         arrayList.add(new RightMenu(k, RightMenuType.BTN_BACK, "closeDialog1()", true, true));
/*     */       } 
/*     */ 
/*     */       
/* 399 */       if ("addRuleDetail".equals(str1) || "editRuleDetail".equals(str1)) {
/* 400 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(1361, user.getLanguage()));
/*     */         
/* 402 */         String str3 = "";
/*     */         
/* 404 */         String str4 = "";
/*     */         
/* 406 */         String str5 = "";
/*     */         
/* 408 */         String str6 = "";
/*     */         
/* 410 */         String str7 = "";
/*     */         
/* 412 */         String str8 = "";
/*     */         
/* 414 */         String str9 = "";
/*     */         
/* 416 */         if ("addRuleDetail".equals(str1)) {
/*     */           
/* 418 */           str2 = SystemEnv.getHtmlLabelName(1421, user.getLanguage());
/*     */         } else {
/*     */           
/* 421 */           str2 = SystemEnv.getHtmlLabelName(93, user.getLanguage());
/* 422 */           str3 = " select t.workflowid,t.approvalfield,t.disnodeid,t1.workflowname,t2.nodename,t3.fieldname,t3.fieldlabel from workflow_dispatch_dt1 t,workflow_base t1,workflow_nodebase t2,workflow_billfield t3 where t.id= " + j + " and t1.id=t.workflowid and t2.id = t.disnodeid and t.approvalfield=t3.id";
/*     */ 
/*     */           
/* 425 */           recordSet.execute(str3);
/* 426 */           if (recordSet.next()) {
/* 427 */             str4 = Util.null2String(recordSet.getString("workflowid"));
/* 428 */             str5 = Util.null2String(recordSet.getString("workflowname"));
/* 429 */             str6 = Util.null2String(recordSet.getString("approvalfield"));
/* 430 */             str7 = SystemEnv.getHtmlLabelName(Util.getIntValue(recordSet.getString("fieldlabel")), user.getLanguage());
/* 431 */             str8 = Util.null2String(recordSet.getString("disnodeid"));
/* 432 */             str9 = Util.null2String(recordSet.getString("nodename"));
/*     */           } 
/*     */         } 
/*     */         
/* 436 */         str2 = str2 + SystemEnv.getHtmlLabelName(10000414, user.getLanguage());
/*     */         
/* 438 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */         
/* 440 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 441 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/*     */         
/* 443 */         hashMap3.put("showname", str5);
/* 444 */         hashMap4.put("isbill", "1");
/* 445 */         hashMap4.put("noNeedActiveWfId", "1");
/* 446 */         hashMap2 = new HashMap<>();
/* 447 */         Map map = DispatchTransMethod.getFormItemForBrowser("workflowid", SystemEnv.getHtmlLabelName(81651, user.getLanguage()), "-99991", str4, 3, null, "", hashMap4, hashMap3);
/*     */         
/* 449 */         arrayList2.add(map);
/*     */ 
/*     */         
/* 452 */         hashMap3.put("showname", str7);
/* 453 */         hashMap4 = new HashMap<>();
/* 454 */         hashMap4.put("workflowId", str4);
/* 455 */         hashMap4.put("type", "1");
/* 456 */         hashMap4.put("htType", "3");
/* 457 */         hashMap4.put("isBill", "1");
/* 458 */         hashMap4.put("isDetail", "0");
/* 459 */         map = DispatchTransMethod.getFormItemForBrowser("approvalfield", SystemEnv.getHtmlLabelName(10000415, user.getLanguage()), "formField", str6, 3, null, "", hashMap4, hashMap3);
/*     */         
/* 461 */         arrayList2.add(map);
/*     */ 
/*     */         
/* 464 */         hashMap3.put("showname", str9);
/* 465 */         hashMap4 = new HashMap<>();
/* 466 */         hashMap4.put("workflowid", str4);
/* 467 */         map = DispatchTransMethod.getFormItemForBrowser("disnodeid", SystemEnv.getHtmlLabelName(10000416, user.getLanguage()), "workflowNode", str8, 3, null, "", hashMap4, hashMap3);
/*     */         
/* 469 */         arrayList2.add(map);
/*     */         
/* 471 */         arrayList.add(new RightMenu(k, RightMenuType.BTN_SAVE, "saveDialog2()", true, true));
/* 472 */         arrayList.add(new RightMenu(k, RightMenuType.BTN_BACK, "closeDialog2()", true, true));
/*     */       } 
/*     */ 
/*     */       
/* 476 */       if ("onEditRule".equals(str1)) {
/* 477 */         String str = getRuleDetailList(paramHttpServletRequest, paramHttpServletResponse);
/* 478 */         hashMap.put("sessionkey1", str);
/*     */       } 
/*     */ 
/*     */       
/* 482 */       hashMap1.put("items", arrayList2);
/* 483 */       arrayList1.add(hashMap1);
/* 484 */       hashMap.put("conditioninfo", arrayList1);
/* 485 */       hashMap.put("status", "1");
/* 486 */       hashMap.put("dialogtitle", str2);
/* 487 */       hashMap.put("rightMenu", arrayList);
/*     */     }
/* 489 */     catch (Exception exception) {
/* 490 */       exception.printStackTrace();
/* 491 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 492 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 494 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRuleDetailList(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 507 */     String str1 = "com.customization.qc574527.util.DispatchTransMethod";
/*     */     
/* 509 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/* 511 */     String str2 = DispatchPageUidFactory.getDispatchPageUid("dispatchdetaillist");
/*     */     
/* 513 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("ruleid")));
/*     */     
/* 515 */     String str3 = str2;
/*     */     
/* 517 */     String str4 = "checkbox";
/* 518 */     String str5 = "t1.id,t1.workflowid,t1.approvalfield,t3.fieldname,t3.fieldlabel,t1.disnodeid,t.workflowname,t2.nodename ";
/* 519 */     String str6 = " t1.id ";
/* 520 */     String str7 = " workflow_dispatch_dt1 t1,workflow_base t,workflow_nodebase t2,workflow_billfield t3 ";
/* 521 */     String str8 = " where t1.mainid=" + i + " and t1.workflowid=t.id and t1.disnodeid = t2.id and t3.id=t1.approvalfield and t1.mainid>0 ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 532 */     String str9 = "<table  pageId=\"" + str3 + "\" pageUid=\"" + str2 + "\" instanceid=\"DispatchSetDetail\"  tabletype=\"" + str4 + "\"  pagesize=\"" + PageIdConst.getPageSize(str3, user.getUID()) + "\"  ><sql backfields=\"" + str5 + "\" sqlform=\"" + str7 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str8) + "\" sqlprimarykey=\"t1.id\" sqlorderby=\"" + str6 + "\" sqlsortway=\"asc\" sqlisdistinct=\"true\" /><head><col width=\"20%\" text=\"\" column=\"id\"  hide=\"true\" /><col width=\"40%\" display=\"true\" text=\"" + SystemEnv.getHtmlLabelName(81651, user.getLanguage()) + "\" column=\"workflowname\"  orderkey=\"workflowname\" /><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(10000415, user.getLanguage()) + "\" column=\"fieldlabel\" orderkey=\"fieldlabel\" otherpara=\"" + user.getLanguage() + "\"  transmethod=\"" + str1 + ".getFieldlabel\"/><col width=\"30%\"  text=\"" + SystemEnv.getHtmlLabelName(10000416, user.getLanguage()) + "\" column=\"nodename\" orderkey=\"nodename\" /></head>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 537 */     String str10 = "<operates width=\"5%\">    <operate href=\"javascript:onEdit()\" text=\"" + SystemEnv.getHtmlLabelName(93, user.getLanguage()) + "\" target=\"_self\" index=\"0\"/>    <operate href=\"javascript:onDelete()\" text=\"" + SystemEnv.getHtmlLabelName(91, user.getLanguage()) + "\" target=\"_self\" index=\"0\"/></operates>";
/*     */     
/* 539 */     str9 = str9 + str10 + "</table>";
/* 540 */     String str11 = str2 + "_" + Util.getEncrypt(Util.getRandom());
/* 541 */     Util_TableMap.setVal(str11, str9);
/* 542 */     return str11;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> saveRuleInfo(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 554 */     RecordSet recordSet = new RecordSet();
/* 555 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("ruleid"));
/* 556 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("ruledetailid")));
/* 557 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("dialogType"));
/* 558 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 559 */     String str2 = "";
/* 560 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 561 */     if ("onNewRule".equals(str1)) {
/*     */       
/* 563 */       String str3 = Util.null2String(paramHttpServletRequest.getParameter("rulename"));
/*     */       
/* 565 */       int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("distype")), 0);
/*     */       
/* 567 */       String str4 = "";
/* 568 */       if (0 == k) {
/* 569 */         str4 = Util.null2String(paramHttpServletRequest.getParameter("dishrm"));
/* 570 */       } else if (1 == k) {
/* 571 */         str4 = Util.null2String(paramHttpServletRequest.getParameter("disrole"));
/*     */       } 
/*     */ 
/*     */       
/* 575 */       int m = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("disworkload")));
/*     */       
/* 577 */       String str5 = Util.null2String(paramHttpServletRequest.getParameter("disinstructions"));
/*     */       
/* 579 */       int n = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("saturationrule")));
/* 580 */       String str6 = TimeUtil.getCurrentTimeString();
/*     */       
/* 582 */       str2 = "insert into workflow_dispatch (disworker,distype,disworkload,disinstructions,saturationrule,rulename,operator,operationtime) values ('" + str4 + "'," + k + "," + m + ",'" + str5 + "'," + n + ",'" + str3 + "'," + user.getUID() + ",'" + str6 + "')";
/* 583 */       recordSet.execute(str2);
/* 584 */       str2 = "select max(id) from workflow_dispatch where operator=" + user.getUID() + " and operationtime='" + str6 + "'";
/* 585 */       recordSet.execute(str2);
/* 586 */       if (recordSet.next()) {
/* 587 */         i = Util.getIntValue(recordSet.getString(1));
/*     */       }
/* 589 */     } else if ("onEditRule".equals(str1)) {
/*     */       
/* 591 */       String str3 = Util.null2String(paramHttpServletRequest.getParameter("rulename"));
/*     */       
/* 593 */       int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("distype")), 0);
/*     */       
/* 595 */       String str4 = "";
/* 596 */       if (0 == k) {
/* 597 */         str4 = Util.null2String(paramHttpServletRequest.getParameter("dishrm"));
/* 598 */       } else if (1 == k) {
/* 599 */         str4 = Util.null2String(paramHttpServletRequest.getParameter("disrole"));
/*     */       } 
/*     */ 
/*     */       
/* 603 */       int m = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("disworkload")));
/*     */       
/* 605 */       String str5 = Util.null2String(paramHttpServletRequest.getParameter("disinstructions"));
/*     */       
/* 607 */       int n = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("saturationrule")));
/* 608 */       String str6 = TimeUtil.getCurrentTimeString();
/*     */       
/* 610 */       str2 = "update workflow_dispatch set disworker='" + str4 + "',distype=" + k + ",disworkload=" + m + ",disinstructions='" + str5 + "',saturationrule=" + n + ",rulename='" + str3 + "',operator=" + user.getUID() + ",operationtime='" + str6 + "' where id=" + i;
/*     */       
/* 612 */       recordSet.execute(str2);
/* 613 */     } else if ("addRuleDetail".equals(str1)) {
/*     */       
/* 615 */       int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("workflowid")));
/*     */       
/* 617 */       int m = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("approvalfield")));
/*     */       
/* 619 */       int n = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("disnodeid")));
/* 620 */       str2 = "insert into workflow_dispatch_dt1 (mainid,workflowid,approvalfield,disnodeid) values (" + i + "," + k + "," + m + "," + n + ")";
/* 621 */       recordSet.execute(str2);
/* 622 */     } else if ("editRuleDetail".equals(str1)) {
/*     */       
/* 624 */       int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("workflowid")));
/*     */       
/* 626 */       int m = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("approvalfield")));
/*     */       
/* 628 */       int n = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("disnodeid")));
/* 629 */       str2 = "update workflow_dispatch_dt1 set workflowid=" + k + ",approvalfield=" + m + ",disnodeid=" + n + " where id=" + j + " and mainid =" + i;
/*     */       
/* 631 */       recordSet.execute(str2);
/*     */     } 
/* 633 */     if ("addRuleDetail".equals(str1) || "editRuleDetail".equals(str1)) {
/* 634 */       String str = getRuleDetailList(paramHttpServletRequest, paramHttpServletResponse);
/* 635 */       hashMap.put("sessionkey1", str);
/*     */     } 
/*     */     
/* 638 */     hashMap.put("success", "1");
/* 639 */     hashMap.put("ruleid", i + "");
/* 640 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> deleteRule(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 652 */     RecordSet recordSet = new RecordSet();
/* 653 */     String str1 = "";
/* 654 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 656 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("tempids"));
/* 657 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("type"));
/* 658 */     if ("delrule".equals(str3)) {
/* 659 */       if (!"".equals(str2)) {
/* 660 */         str1 = "delete from workflow_dispatch_dt1 where mainid in (" + str2 + ")";
/* 661 */         recordSet.execute(str1);
/* 662 */         str1 = "delete from workflow_dispatch where id in (" + str2 + ")";
/* 663 */         recordSet.execute(str1);
/*     */       } 
/* 665 */     } else if ("delruledetail".equals(str3)) {
/*     */       
/* 667 */       if (!"".equals(str2)) {
/* 668 */         str1 = "delete from workflow_dispatch_dt1 where id in (" + str2 + ")";
/* 669 */         recordSet.execute(str1);
/*     */       } 
/* 671 */       String str = getRuleDetailList(paramHttpServletRequest, paramHttpServletResponse);
/* 672 */       hashMap.put("sessionkey1", str);
/*     */     } 
/* 674 */     hashMap.put("success", "1");
/* 675 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/service/DispatchSetService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */