/*     */ package com.customization.qc574527.service;
/*     */ 
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.customization.qc574527.bean.RightMenu;
/*     */ import com.customization.qc574527.bean.RightMenuType;
/*     */ import com.customization.qc574527.util.DispatchPageUidFactory;
/*     */ import com.customization.qc574527.util.DispatchTransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.soa.workflow.request.RequestService;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ManuallyDispatchService
/*     */ {
/*     */   public boolean hasRight(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  44 */     boolean bool = false;
/*     */     
/*  46 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  47 */     if (HrmUserVarify.checkUserRight("workflow:Dispatching", user)) {
/*  48 */       bool = true;
/*     */     }
/*  50 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Map<String, Object>> getSearchCondition(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  57 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  58 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*     */     
/*  60 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  61 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  62 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */     
/*  65 */     hashMap2 = new HashMap<>();
/*  66 */     hashMap2.put("colSpan", Integer.valueOf(2));
/*  67 */     hashMap2.put("conditionType", "INPUT");
/*  68 */     hashMap2.put("domkey", new String[] { "requestname" });
/*  69 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/*  70 */     hashMap2.put("isQuickSearch", Boolean.valueOf(true));
/*  71 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(229, user.getLanguage()));
/*  72 */     hashMap2.put("labelcol", Integer.valueOf(6));
/*  73 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/*  74 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/*  77 */     hashMap2 = new HashMap<>();
/*  78 */     hashMap2.put("colSpan", Integer.valueOf(2));
/*  79 */     hashMap2.put("conditionType", "INPUT");
/*  80 */     hashMap2.put("domkey", new String[] { "rulename" });
/*  81 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/*  82 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(19829, user.getLanguage()));
/*  83 */     hashMap2.put("labelcol", Integer.valueOf(6));
/*  84 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/*  85 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/*  88 */     hashMap2 = new HashMap<>();
/*  89 */     hashMap2.put("colSpan", Integer.valueOf(2));
/*  90 */     hashMap2.put("conditionType", "INPUT");
/*  91 */     hashMap2.put("domkey", new String[] { "wfcode" });
/*  92 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/*  93 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(19502, user.getLanguage()));
/*  94 */     hashMap2.put("labelcol", Integer.valueOf(6));
/*  95 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/*  96 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/*  99 */     hashMap2 = new HashMap<>();
/* 100 */     hashMap2.put("colSpan", Integer.valueOf(2));
/* 101 */     hashMap2.put("conditionType", "BROWSER");
/* 102 */     hashMap2.put("domkey", new String[] { "workflowid" });
/* 103 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/* 104 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(125749, user.getLanguage()));
/* 105 */     hashMap2.put("labelcol", Integer.valueOf(6));
/* 106 */     hashMap2.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 107 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/* 108 */     hashMap2.put("rules", "");
/* 109 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 110 */     hashMap3.put("type", "-99991");
/* 111 */     hashMap3.put("isSingle", Boolean.valueOf(true));
/* 112 */     hashMap3.put("title", SystemEnv.getHtmlLabelName(18499, user.getLanguage()));
/* 113 */     hashMap2.put("browserConditionParam", hashMap3);
/* 114 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/* 117 */     hashMap2 = new HashMap<>();
/* 118 */     hashMap2.put("colSpan", Integer.valueOf(2));
/* 119 */     hashMap2.put("conditionType", "BROWSER");
/* 120 */     hashMap2.put("domkey", new String[] { "createrid" });
/* 121 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/* 122 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(882, user.getLanguage()));
/* 123 */     hashMap2.put("labelcol", Integer.valueOf(6));
/* 124 */     hashMap2.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 125 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/* 126 */     hashMap2.put("rules", "");
/* 127 */     hashMap3 = new HashMap<>();
/* 128 */     hashMap3.put("type", "1");
/* 129 */     hashMap3.put("isSingle", Boolean.valueOf(true));
/* 130 */     hashMap3.put("title", SystemEnv.getHtmlLabelName(882, user.getLanguage()));
/* 131 */     hashMap2.put("browserConditionParam", hashMap3);
/* 132 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/* 135 */     hashMap2 = new HashMap<>();
/* 136 */     hashMap2.put("colSpan", Integer.valueOf(2));
/* 137 */     hashMap2.put("conditionType", "INPUT");
/* 138 */     hashMap2.put("domkey", new String[] { "workcode" });
/* 139 */     hashMap2.put("fieldcol", Integer.valueOf(12));
/* 140 */     hashMap2.put("label", SystemEnv.getHtmlLabelName(10000417, user.getLanguage()));
/* 141 */     hashMap2.put("labelcol", Integer.valueOf(6));
/* 142 */     hashMap2.put("viewAttr", Integer.valueOf(2));
/* 143 */     arrayList2.add(hashMap2);
/*     */ 
/*     */     
/* 146 */     hashMap2 = new HashMap<>();
/* 147 */     List list = DispatchTransMethod.getDateTypeOptions("", user.getLanguage());
/* 148 */     Map map = DispatchTransMethod.getFormItemForBrowserDate("createdate", SystemEnv.getHtmlLabelName(722, user.getLanguage()), "", 2, list, null);
/*     */     
/* 150 */     arrayList2.add(map);
/*     */     
/* 152 */     hashMap1.put("title", SystemEnv.getHtmlLabelName(32905, user.getLanguage()));
/* 153 */     hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 154 */     hashMap1.put("items", arrayList2);
/* 155 */     arrayList1.add(hashMap1);
/*     */     
/* 157 */     return (List)arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getListData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 169 */     String str1 = "com.customization.qc574527.util.DispatchTransMethod";
/*     */     
/* 171 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 172 */     boolean bool = false;
/* 173 */     if (HrmUserVarify.checkUserRight("workflow:Dispatching", user)) {
/* 174 */       bool = true;
/*     */     }
/* 176 */     String str2 = DispatchPageUidFactory.getDispatchPageUid("manuallydispatch");
/*     */     
/* 178 */     String str3 = str2;
/*     */ 
/*     */     
/* 181 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 183 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 185 */     Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 186 */     while (enumeration.hasMoreElements()) {
/* 187 */       String str = enumeration.nextElement();
/* 188 */       hashMap.put(str, Util.null2String(paramHttpServletRequest.getParameter(str)));
/*     */     } 
/*     */ 
/*     */     
/* 192 */     String str4 = Util.null2String(recordSet.getPropValue("WorkflowDispatch", "mutlDispatch")).trim();
/*     */     
/* 194 */     String str5 = "";
/* 195 */     String str6 = "";
/* 196 */     String str7 = " where (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') and t1.requestid = t2.requestid and t1.workflowid in (select distinct tt1.workflowid from workflow_dispatch_dt1 tt1) and t2.usertype=0 and  (t1.deleted=0 or t1.deleted is null) and t2.isremark=0  and (t1.deleted=0 or t1.deleted is null) and t2.islasttimes=1  and t1.workflowid in (select id from workflow_base where   (isvalid='1' or isvalid='3') )  and t2.nodeid in (select distinct tt2.disnodeid from workflow_dispatch_dt1 tt2) ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 202 */     if (recordSet.getDBType().equalsIgnoreCase("postgresql"))
/*     */     {
/* 204 */       str7 = " where (t1.deleted <> 1 or t1.deleted is null ) and t1.requestid = t2.requestid and t1.workflowid in (select distinct tt1.workflowid from workflow_dispatch_dt1 tt1) and t2.usertype=0 and  (t1.deleted=0 or t1.deleted is null) and t2.isremark=0  and (t1.deleted=0 or t1.deleted is null) and t2.islasttimes=1  and t1.workflowid in (select id from workflow_base where   (isvalid='1' or isvalid='3') )  and t2.nodeid in (select distinct tt2.disnodeid from workflow_dispatch_dt1 tt2) ";
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 211 */     String str8 = "";
/*     */ 
/*     */     
/* 214 */     String str9 = Util.null2String((String)hashMap.get("rulename"));
/*     */ 
/*     */     
/* 217 */     String str10 = Util.null2String((String)hashMap.get("requestname"));
/*     */ 
/*     */     
/* 220 */     String str11 = Util.null2String((String)hashMap.get("wfcode"));
/*     */ 
/*     */     
/* 223 */     String str12 = Util.null2String((String)hashMap.get("workflowid"));
/*     */ 
/*     */     
/* 226 */     String str13 = Util.null2String((String)hashMap.get("createdate_selectType"));
/* 227 */     String str14 = Util.null2String((String)hashMap.get("createdate_fromDate"));
/* 228 */     String str15 = Util.null2String((String)hashMap.get("createdate_toDate"));
/* 229 */     Map map = DispatchTransMethod.getFromDateAndEndDate(str13, str14, str15);
/*     */     
/* 231 */     String str16 = Util.null2String((String)map.get("fromDate"));
/* 232 */     String str17 = Util.null2String((String)map.get("toDate"));
/*     */ 
/*     */     
/* 235 */     String str18 = Util.null2String((String)hashMap.get("createrid"));
/*     */ 
/*     */     
/* 238 */     String str19 = Util.null2String((String)hashMap.get("workcode"));
/*     */     
/* 240 */     if (!str9.equals("")) {
/* 241 */       str9 = Util.toHtml(str9);
/* 242 */       str9 = Util.StringReplace(str9, "'", "''");
/* 243 */       str7 = str7 + " and exists (select 1 from workflow_dispatch tt1 where tt1.rulename like '%" + str9 + "%' and tt1.id in (select distinct tt2.mainid from workflow_dispatch_dt1 tt2 where tt2.disnodeid = t2.nodeid)) ";
/*     */     } 
/*     */ 
/*     */     
/* 247 */     if (!str10.equals("")) {
/* 248 */       str10 = Util.toHtml(str10);
/* 249 */       str10 = Util.StringReplace(str10, "'", "''");
/* 250 */       str7 = str7 + " and t1.requestname like '%" + str10 + "%'";
/*     */     } 
/*     */ 
/*     */     
/* 254 */     if (!str19.equals("")) {
/* 255 */       str7 = str7 + " and t1.requestmark like '%" + str11 + "%' ";
/*     */     }
/*     */     
/* 258 */     if (!str12.equals("")) {
/* 259 */       str7 = str7 + " and t1.workflowid in (" + WorkflowVersion.getAllVersionStringByWFIDs(str12) + ") ";
/*     */     }
/*     */ 
/*     */     
/* 263 */     if (!"".equals(str16)) {
/* 264 */       str7 = str7 + " and t1.createdate >= '" + str16 + "' ";
/*     */     }
/*     */     
/* 267 */     if (!"".equals(str17)) {
/* 268 */       str7 = str7 + " and t1.createdate <= '" + str17 + "'";
/*     */     }
/*     */     
/* 271 */     if (!"".equals(str18)) {
/* 272 */       str7 = str7 + " and t1.creater = " + str18 + " ";
/*     */     }
/* 274 */     if (!"".equals(str19)) {
/* 275 */       str7 = str7 + " and t1.creatertype= '0' and t1.creater in(select id from hrmresource where workcode like '%" + str19 + "%') ";
/*     */     }
/*     */ 
/*     */     
/* 279 */     if (!bool) {
/* 280 */       str7 = str7 + " and t2.userid =" + user.getUID();
/*     */     }
/*     */     
/* 283 */     str6 = str6 + " workflow_requestbase t1,workflow_currentoperator t2 ";
/*     */     
/* 285 */     str8 = str8 + " t1.requestid ";
/*     */ 
/*     */     
/* 288 */     String str20 = "none";
/* 289 */     if ("1".equals(str4)) {
/* 290 */       str20 = "checkbox";
/*     */     }
/*     */     
/* 293 */     str5 = str5 + " t1.requestid,t1.requestmark,t1.createdate, t1.createtime,t1.creater, t1.creatertype, t1.workflowid, t1.status,t1.requestlevel,t1.currentnodeid,t2.isremark,t2.nodeid ";
/*     */     
/* 295 */     String str21 = user.getLanguage() + "+" + user.getUID() + "+column:userid";
/* 296 */     String str22 = "column:requestid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 308 */     String str23 = "<table  pageId=\"" + str3 + "\" pageUid=\"" + str2 + "\"  instanceid=\"ManuallyDispatchTab\"  tabletype=\"" + str20 + "\"  pagesize=\"" + PageIdConst.getPageSize(str3, user.getUID()) + "\"  ><sql backfields=\"" + str5 + "\" sqlform=\"" + str6 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str7) + "\" sqlprimarykey=\"t1.requestid\" sqlorderby=\"" + str8 + "\" sqlsortway=\"asc\" sqlisdistinct=\"true\" /><head><col width=\"15%\" display=\"true\" text=\"" + SystemEnv.getHtmlLabelName(1334, user.getLanguage()) + "\" column=\"requestid\"  orderkey=\"requestid\" otherpara=\"" + user.getUID() + "\" transmethod=\"" + str1 + ".getRequestname\" /><col width=\"15%\"  text=\"" + SystemEnv.getHtmlLabelName(259, user.getLanguage()) + "\" column=\"workflowid\" orderkey=\"t1.workflowid\" transmethod=\"weaver.workflow.workflow.WorkflowComInfo.getWorkflowname\" /><col width=\"9%\"  text=\"" + SystemEnv.getHtmlLabelName(882, user.getLanguage()) + "\" column=\"creater\" orderkey=\"t1.creater\"  otherpara=\"column:creatertype\" transmethod=\"weaver.general.WorkFlowTransMethod.getWFSearchResultName\" /><col width=\"10%\" id=\"createdate\" text=\"" + SystemEnv.getHtmlLabelName(722, user.getLanguage()) + "\" column=\"createdate\" orderkey=\"t1.createdate,t1.createtime\" otherpara=\"column:createtime\" transmethod=\"weaver.general.WorkFlowTransMethod.getWFSearchResultCreateTime\" /><col width=\"8%\" id=\"quick\" text=\"" + SystemEnv.getHtmlLabelName(15534, user.getLanguage()) + "\" column=\"requestlevel\"  orderkey=\"t1.requestlevel\" transmethod=\"weaver.general.WorkFlowTransMethod.getWFSearchResultUrgencyDegree\" otherpara=\"" + user.getLanguage() + "\"/><col width=\"8%\" id=\"hurry\" text=\"" + SystemEnv.getHtmlLabelName(18564, user.getLanguage()) + "\" column=\"currentnodeid\" transmethod=\"weaver.general.WorkFlowTransMethod.getCurrentNode\"/><col width=\"8%\" text=\"" + SystemEnv.getHtmlLabelName(19829, user.getLanguage()) + "\" column=\"workflowid\"  transmethod=\"" + str1 + ".getRuleName\"/><col width=\"15%\" orderkey=\"t1.requestmark\"  text=\"" + SystemEnv.getHtmlLabelName(19502, user.getLanguage()) + "\" column=\"requestmark\"/></head>";
/*     */ 
/*     */     
/* 311 */     String str24 = "<operates width=\"5%\">    <operate href=\"javascript:onDispatch()\" text=\"" + SystemEnv.getHtmlLabelName(128469, user.getLanguage()) + "\" target=\"_self\" index=\"0\"/></operates>";
/*     */     
/* 313 */     str23 = str23 + str24 + "</table>";
/* 314 */     String str25 = str2 + "_" + Util.getEncrypt(Util.getRandom());
/* 315 */     Util_TableMap.setVal(str25, str23);
/* 316 */     return str25;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> chooseDialog(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 326 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 328 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 329 */       ConditionFactory conditionFactory = new ConditionFactory(user);
/* 330 */       RecordSet recordSet = new RecordSet();
/* 331 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("dialogType"));
/* 332 */       String str2 = Util.null2String(paramHttpServletRequest.getParameter("requestids"));
/*     */ 
/*     */       
/* 335 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */       
/* 338 */       ArrayList<RightMenu> arrayList1 = new ArrayList();
/* 339 */       int i = user.getLanguage();
/*     */ 
/*     */       
/* 342 */       String str3 = "";
/*     */       
/* 344 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 345 */       ArrayList<Map> arrayList2 = new ArrayList();
/* 346 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/*     */       
/* 348 */       hashMap1.put("title", SystemEnv.getHtmlLabelName(10000408, user.getLanguage()));
/*     */       
/* 350 */       if ("onDispatch".equals(str1)) {
/*     */         
/* 352 */         str3 = SystemEnv.getHtmlLabelName(128469, user.getLanguage());
/* 353 */       } else if ("batchDispatch".equals(str1)) {
/*     */         
/* 355 */         str3 = SystemEnv.getHtmlLabelName(10000418, user.getLanguage());
/*     */       } 
/*     */ 
/*     */       
/* 359 */       String str4 = "0";
/* 360 */       String str5 = "";
/* 361 */       if (!"".equals(str2)) {
/* 362 */         String str = "select distinct workflowid from workflow_requestbase where requestid in (" + str2 + ")";
/* 363 */         recordSet.execute(str);
/* 364 */         while (recordSet.next()) {
/* 365 */           str5 = str5 + "," + recordSet.getString("workflowid");
/*     */         }
/* 367 */         if (!"".equals(str5)) {
/* 368 */           str5 = str5.substring(1);
/* 369 */           str = "select t.disworker from workflow_dispatch t where t.id in( select distinct t1.mainid from workflow_dispatch_dt1 t1 where t1.workflowid in (" + str5 + ")) and t.distype=0";
/* 370 */           recordSet.execute(str);
/* 371 */           while (recordSet.next()) {
/* 372 */             str4 = str4 + "," + recordSet.getString("disworker");
/*     */           }
/*     */           
/* 375 */           str = "select distinct tt.resourceid from HrmRoleMembers tt where tt.roleid in( select t.disworker from workflow_dispatch t where t.id in( select distinct t1.mainid from workflow_dispatch_dt1 t1 where t1.workflowid in (" + str5 + ")) and t.distype=1)";
/* 376 */           recordSet.execute(str);
/* 377 */           while (recordSet.next()) {
/* 378 */             str4 = str4 + "," + recordSet.getString("resourceid");
/*     */           }
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 384 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 385 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 386 */       XssUtil xssUtil = new XssUtil();
/* 387 */       String str6 = Util.getSubINClause(str4, "t1.id", "in");
/* 388 */       String str7 = Util.getSubINClause(str4, "hr.id", "in");
/* 389 */       hashMap3.put("completesqlwhere", xssUtil.put(str6));
/* 390 */       hashMap3.put("searchsqlwhere", xssUtil.put(str7));
/* 391 */       Map map = DispatchTransMethod.getFormItemForBrowser("resourceid", SystemEnv.getHtmlLabelName(1867, user.getLanguage()), "1", "", 3, null, "", null, hashMap3);
/*     */       
/* 393 */       arrayList2.add(map);
/*     */ 
/*     */       
/* 396 */       arrayList1.add(new RightMenu(i, RightMenuType.BTN_SUBMIT, "onSubmit()", true, true));
/* 397 */       arrayList1.add(new RightMenu(i, RightMenuType.BTN_BACK, "closeDialog1()", true, true));
/*     */ 
/*     */       
/* 400 */       hashMap1.put("items", arrayList2);
/* 401 */       arrayList.add(hashMap1);
/* 402 */       hashMap.put("conditioninfo", arrayList);
/* 403 */       hashMap.put("status", "1");
/* 404 */       hashMap.put("dialogtitle", str3);
/* 405 */       hashMap.put("rightMenu", arrayList1);
/*     */     }
/* 407 */     catch (Exception exception) {
/* 408 */       exception.printStackTrace();
/* 409 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 410 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 412 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> submitDispatch(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 426 */     RecordSet recordSet1 = new RecordSet();
/* 427 */     RecordSet recordSet2 = new RecordSet();
/* 428 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 430 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 431 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("requestids"));
/* 432 */       String str2 = Util.null2String(paramHttpServletRequest.getParameter("resourceid"));
/* 433 */       RequestService requestService = new RequestService();
/* 434 */       String str3 = TimeUtil.getCurrentDateString();
/* 435 */       if (!"".equals(str1) && !"".equals(str2)) {
/*     */         
/* 437 */         String[] arrayOfString = Util.TokenizerString2(str1, ",");
/* 438 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 439 */           String str5 = arrayOfString[b];
/* 440 */           String str6 = "select distinct workflowid from workflow_requestbase where requestid =" + str5;
/* 441 */           recordSet1.execute(str6);
/* 442 */           recordSet1.next();
/* 443 */           String str7 = Util.null2String(recordSet1.getString("workflowid"));
/* 444 */           str6 = "select * from workflow_dispatch_dt1 where workflowid=" + str7;
/* 445 */           recordSet1.execute(str6);
/* 446 */           if (recordSet1.next()) {
/* 447 */             int i = Util.getIntValue(recordSet1.getString("mainid"), 0);
/*     */             
/* 449 */             String str8 = "select t1.tablename from workflow_bill  t1 where t1.id=(select t.formid from workflow_base t where t.id=" + str7 + ")";
/* 450 */             recordSet2.executeSql(str8);
/* 451 */             recordSet2.next();
/* 452 */             String str9 = Util.null2String(recordSet2.getString("tablename"));
/*     */             
/* 454 */             String str10 = Util.null2String(recordSet1.getString("approvalfield"));
/* 455 */             str8 = "select t1.fieldname from workflow_billfield t1 where t1.id=" + str10 + "";
/* 456 */             recordSet2.executeSql(str8);
/* 457 */             recordSet2.next();
/* 458 */             String str11 = Util.null2String(recordSet2.getString("fieldname"));
/*     */             
/* 460 */             int j = Util.getIntValue(recordSet1.getString("disnodeid"), 0);
/*     */             
/* 462 */             str8 = "update " + str9 + " set " + str11 + "=" + str2 + " where requestid=" + str5;
/*     */             
/* 464 */             recordSet2.executeSql(str8);
/*     */             
/* 466 */             str8 = "select t1.* from workflow_currentoperator t1 where t1.id = (select max(id) from workflow_currentoperator where requestid=" + str5 + " and nodeid =" + j + " and isremark=0)";
/*     */             
/* 468 */             recordSet2.executeSql(str8);
/* 469 */             int k = 0;
/* 470 */             if (recordSet2.next()) {
/* 471 */               k = Util.getIntValue(recordSet2.getString("userid"), 0);
/*     */             }
/*     */             
/* 474 */             requestService.nextNodeBySubmit(null, Util.getIntValue(str5), k, 
/* 475 */                 SystemEnv.getHtmlLabelName(503747, user.getLanguage()));
/*     */ 
/*     */             
/* 478 */             String str12 = "insert into workflow_dispatchrecord (userid,dispatchtype,requestid,workflowid,operationdate) values (" + str2 + ",2," + str5 + "," + str7 + ",'" + str3 + "')";
/* 479 */             recordSet2.executeSql(str12);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 487 */       String str4 = getListData(paramHttpServletRequest, paramHttpServletResponse);
/* 488 */       hashMap.put("sessionkey1", str4);
/* 489 */       hashMap.put("success", "1");
/* 490 */     } catch (Exception exception) {
/* 491 */       exception.printStackTrace();
/* 492 */       hashMap.put("api_status", "false");
/* 493 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 495 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> grabSingle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 506 */     RecordSet recordSet1 = new RecordSet();
/* 507 */     RecordSet recordSet2 = new RecordSet();
/* 508 */     RecordSet recordSet3 = new RecordSet();
/* 509 */     RecordSet recordSet4 = new RecordSet();
/* 510 */     RecordSet recordSet5 = new RecordSet();
/* 511 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 513 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 514 */       RequestService requestService = new RequestService();
/*     */ 
/*     */       
/* 517 */       String str1 = user.getUID() + "";
/* 518 */       String str2 = "";
/* 519 */       String str3 = "";
/* 520 */       String str4 = "";
/* 521 */       String str5 = TimeUtil.getCurrentDateString();
/* 522 */       String str6 = "n";
/* 523 */       if ("sqlserver".equalsIgnoreCase(recordSet1.getDBType())) {
/* 524 */         str2 = "select * from workflow_dispatch t where (','+t.disworker+',' like '%," + str1 + ",%' and t.distype=0 )";
/* 525 */       } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 526 */         str2 = "select * from workflow_dispatch t where (CONCAT(',',t.disworker,',') like '%," + str1 + ",%' and t.distype=0 )";
/*     */       } else {
/* 528 */         str2 = "select * from workflow_dispatch t where (','||t.disworker||',' like '%," + str1 + ",%' and t.distype=0 ) ";
/*     */       } 
/* 530 */       str2 = str2 + " or (t.distype=1 and exists (select 1 from HrmRoleMembers tt where tt.roleid= t.disworker and tt.resourceid=" + str1 + "))";
/* 531 */       recordSet1.execute(str2);
/* 532 */       while (recordSet1.next()) {
/* 533 */         int i = Util.getIntValue(Util.null2String(recordSet1.getString("disworkload")));
/* 534 */         int j = Util.getIntValue(Util.null2String(recordSet1.getString("saturationrule")));
/* 535 */         String str8 = Util.null2String(recordSet1.getString("id"));
/* 536 */         int k = 0;
/*     */         
/* 538 */         String str9 = "";
/* 539 */         if ("sqlserver".equals(recordSet1.getDBType())) {
/* 540 */           str9 = " isnull(t1.currentstatus,-1) ";
/* 541 */         } else if ("mysql".equals(recordSet1.getDBType())) {
/* 542 */           str9 = " IFNULL(t1.currentstatus,-1) ";
/*     */         }
/* 544 */         else if ("postgresql".equals(recordSet1.getDBType())) {
/* 545 */           str9 = " isnull(t1.currentstatus,-1) ";
/*     */         } else {
/*     */           
/* 548 */           str9 = " NVL(t1.currentstatus,-1) ";
/*     */         } 
/* 550 */         if (j == 0) {
/*     */           
/* 552 */           str3 = "select count( distinct t1.requestid) as dbnum from workflow_requestbase t1,workflow_currentoperator t2  where  (t1.deleted <> 1 or t1.deleted is null or t1.deleted='') and t1.requestid = t2.requestid and t2.userid = " + str1 + " and t1.workflowid in (select tt1.workflowid from workflow_dispatch_dt1 tt1 where tt1.mainid=" + str8 + ") and t2.usertype=0 and  (t1.deleted=0 or t1.deleted is null) and t2.isremark=0  and (t1.deleted=0 or t1.deleted is null) and t2.islasttimes=1  and (" + str9 + " = -1 )  and t1.workflowid in (select id from workflow_base where   (isvalid='1' or isvalid='3') ) ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 559 */           if (recordSet1.getDBType().equalsIgnoreCase("postgresql"))
/*     */           {
/* 561 */             str3 = "select count( distinct t1.requestid) as dbnum from workflow_requestbase t1,workflow_currentoperator t2  where  (t1.deleted <> 1 or t1.deleted is null ) and t1.requestid = t2.requestid and t2.userid = " + str1 + " and t1.workflowid in (select tt1.workflowid from workflow_dispatch_dt1 tt1 where tt1.mainid=" + str8 + ") and t2.usertype=0 and  (t1.deleted=0 or t1.deleted is null) and t2.isremark=0  and (t1.deleted=0 or t1.deleted is null) and t2.islasttimes=1  and (" + str9 + " = -1 )  and t1.workflowid in (select id from workflow_base where   (isvalid='1' or isvalid='3') ) ";
/*     */ 
/*     */ 
/*     */           
/*     */           }
/*     */ 
/*     */         
/*     */         }
/* 569 */         else if (j == 1) {
/*     */           
/* 571 */           str3 = "select count( distinct tt3.requestid) as dbnum from workflow_dispatchrecord tt3 where tt3.userid = " + str1 + " and tt3.operationdate ='" + str5 + "' and (tt3.dispatchtype =1 or tt3.dispatchtype = 2 ) and tt3.workflowid in (select t.workflowid from workflow_dispatch_dt1 t where t.mainid=" + str8 + ") ";
/*     */         } 
/*     */ 
/*     */         
/* 575 */         if (!"".equals(str3)) {
/* 576 */           recordSet2.execute(str3);
/* 577 */           recordSet2.next();
/* 578 */           k = Util.getIntValue(recordSet2.getString("dbnum"), 0);
/*     */         } 
/*     */         
/* 581 */         if (k >= i && k > 0) {
/* 582 */           str6 = "c";
/* 583 */           str4 = str8;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/* 588 */       String str7 = "";
/* 589 */       if ("c".equals(str6)) {
/*     */         
/* 591 */         str2 = "select * from workflow_dispatch_dt1 where mainid =" + str4;
/* 592 */         recordSet2.executeSql(str2);
/*     */         
/* 594 */         while (recordSet2.next()) {
/* 595 */           int i = Util.getIntValue(recordSet2.getString("disnodeid"), 0);
/*     */           
/* 597 */           String str8 = Util.null2String(recordSet2.getString("approvalfield"));
/* 598 */           str3 = "select t1.fieldname from workflow_billfield t1 where t1.id=" + str8 + "";
/* 599 */           recordSet5.executeSql(str3);
/* 600 */           recordSet5.next();
/* 601 */           String str9 = Util.null2String(recordSet5.getString("fieldname"));
/*     */           
/* 603 */           int j = Util.getIntValue(recordSet2.getString("workflowid"), 0);
/*     */           
/* 605 */           str3 = "select t1.tablename from workflow_bill  t1 where t1.id=(select t.formid from workflow_base t where t.id=" + j + ")";
/* 606 */           recordSet5.executeSql(str3);
/* 607 */           recordSet5.next();
/* 608 */           String str10 = Util.null2String(recordSet5.getString("tablename"));
/*     */ 
/*     */           
/* 611 */           str3 = " select distinct t.requestid from workflow_requestbase t where t.currentnodeid = " + i + " and t.workflowid=" + j;
/* 612 */           recordSet3.executeSql(str3);
/* 613 */           if (recordSet3.next()) {
/* 614 */             int k = Util.getIntValue(recordSet3.getString("requestid"), 0);
/* 615 */             str3 = "update " + str10 + " set " + str9 + "=" + str1 + " where requestid=" + k;
/*     */             
/* 617 */             recordSet5.executeSql(str3);
/*     */             
/* 619 */             str3 = "select t1.* from workflow_currentoperator t1 where t1.id = (select max(id) from workflow_currentoperator where requestid=" + k + " and nodeid =" + i + " and isremark=0)";
/*     */             
/* 621 */             recordSet5.executeSql(str3);
/* 622 */             int m = 0;
/* 623 */             if (recordSet5.next()) {
/* 624 */               m = Util.getIntValue(recordSet5.getString("userid"), 0);
/*     */             }
/*     */             
/* 627 */             requestService.nextNodeBySubmit(null, k, m, SystemEnv.getHtmlLabelName(381925, user.getLanguage()));
/*     */ 
/*     */             
/* 630 */             str3 = "insert into workflow_dispatchrecord (userid,dispatchtype,requestid,workflowid,operationdate) values (" + str1 + ",3," + k + "," + j + ",'" + str5 + "')";
/* 631 */             recordSet4.executeSql(str3);
/* 632 */             str7 = "1";
/*     */           } 
/* 634 */           if ("1".equals(str7)) {
/*     */             break;
/*     */           }
/*     */         } 
/*     */       } else {
/*     */         
/* 640 */         str7 = "2";
/*     */       } 
/* 642 */       hashMap.put("result", str7);
/* 643 */       hashMap.put("success", "1");
/* 644 */     } catch (Exception exception) {
/* 645 */       exception.printStackTrace();
/* 646 */       hashMap.put("api_status", "false");
/* 647 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 649 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/service/ManuallyDispatchService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */