/*    */ package com.customization.qc574527.bean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum RightMenuType
/*    */ {
/*  8 */   BTN_ADD("84551", "icon-coms-Batch-add"),
/*  9 */   BTN_DELETE("32136", "icon-coms-Batch-delete"),
/*    */   
/* 11 */   BTN_SUBMIT("725", "icon-coms-Approval"),
/* 12 */   BTN_BATCHSUBMIT("20839,128469", "icon-coms-Approval"),
/* 13 */   BTN_SAVE("86", "icon-coms-Preservation"),
/* 14 */   BTN_BACK("201", "icon-coms-Reset"),
/* 15 */   BTN_EDIT("93", "icon-coms-edit"),
/* 16 */   BTN_SEARCH("197", "icon-coms-search"),
/*    */   
/* 18 */   BTN_EXCEL("28304", "icon-coms-export"),
/*    */   
/* 20 */   BTN_STORE("28111", "icon-coms-Collection"),
/* 21 */   BTN_HELP("275", "icon-coms-help");
/*    */   
/*    */   private String labelids;
/*    */   
/*    */   private String icon;
/*    */ 
/*    */   
/*    */   RightMenuType(String paramString1, String paramString2) {
/* 29 */     this.labelids = paramString1;
/* 30 */     this.icon = paramString2;
/*    */   }
/*    */   
/*    */   public String getLabelids() {
/* 34 */     return this.labelids;
/*    */   }
/*    */   
/*    */   public void setLabelids(String paramString) {
/* 38 */     this.labelids = paramString;
/*    */   }
/*    */   
/*    */   public String getIcon() {
/* 42 */     return this.icon;
/*    */   }
/*    */   
/*    */   public void setIcon(String paramString) {
/* 46 */     this.icon = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/bean/RightMenuType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */