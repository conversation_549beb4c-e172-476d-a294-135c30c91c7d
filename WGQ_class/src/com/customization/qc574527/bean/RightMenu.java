/*    */ package com.customization.qc574527.bean;
/*    */ 
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RightMenu
/*    */ {
/*    */   private String menuName;
/*    */   private String menuFun;
/*    */   private String menuIcon;
/*    */   private RightMenuType type;
/*    */   private String isTop;
/*    */   private String isControl;
/*    */   private String params;
/* 18 */   public static String TABLE_VIEW_PARAM = "tableViewType";
/* 19 */   public static String TABLE_VIEW_VIEW = "view";
/* 20 */   public static String TABLE_VIEW_LIST = "list";
/*    */   
/*    */   public RightMenu(int paramInt, RightMenuType paramRightMenuType, String paramString, boolean paramBoolean) {
/* 23 */     this.menuName = SystemEnv.getHtmlLabelNames(paramRightMenuType.getLabelids(), paramInt);
/* 24 */     this.menuFun = paramString;
/* 25 */     this.menuIcon = paramRightMenuType.getIcon();
/* 26 */     this.type = paramRightMenuType;
/* 27 */     this.isTop = paramBoolean ? "1" : "0";
/*    */   }
/*    */   
/*    */   public RightMenu(int paramInt, RightMenuType paramRightMenuType, String paramString, boolean paramBoolean1, boolean paramBoolean2) {
/* 31 */     this.menuName = SystemEnv.getHtmlLabelNames(paramRightMenuType.getLabelids(), paramInt);
/* 32 */     this.menuFun = paramString;
/* 33 */     this.menuIcon = paramRightMenuType.getIcon();
/* 34 */     this.type = paramRightMenuType;
/* 35 */     this.isControl = paramBoolean1 ? "1" : "0";
/* 36 */     this.isTop = paramBoolean2 ? "1" : "0";
/*    */   }
/*    */ 
/*    */   
/*    */   public RightMenu() {}
/*    */   
/*    */   public RightMenuType getType() {
/* 43 */     return this.type;
/*    */   }
/*    */   
/*    */   public void setType(RightMenuType paramRightMenuType) {
/* 47 */     this.type = paramRightMenuType;
/*    */   }
/*    */   
/*    */   public String getMenuName() {
/* 51 */     return this.menuName;
/*    */   }
/*    */   
/*    */   public void setMenuName(String paramString) {
/* 55 */     this.menuName = paramString;
/*    */   }
/*    */   
/*    */   public String getMenuFun() {
/* 59 */     return this.menuFun;
/*    */   }
/*    */   
/*    */   public void setMenuFun(String paramString) {
/* 63 */     this.menuFun = paramString;
/*    */   }
/*    */   
/*    */   public String getMenuIcon() {
/* 67 */     return this.menuIcon;
/*    */   }
/*    */   
/*    */   public void setMenuIcon(String paramString) {
/* 71 */     this.menuIcon = paramString;
/*    */   }
/*    */   
/*    */   public String getIsTop() {
/* 75 */     return this.isTop;
/*    */   }
/*    */   
/*    */   public void setIsTop(String paramString) {
/* 79 */     this.isTop = paramString;
/*    */   }
/*    */   
/*    */   public String getParams() {
/* 83 */     return this.params;
/*    */   }
/*    */   
/*    */   public void setParams(String paramString) {
/* 87 */     this.params = paramString;
/*    */   }
/*    */   
/*    */   public String getIsControl() {
/* 91 */     return this.isControl;
/*    */   }
/*    */   
/*    */   public void setIsControl(String paramString) {
/* 95 */     this.isControl = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/bean/RightMenu.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */