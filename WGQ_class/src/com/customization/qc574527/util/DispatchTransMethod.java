/*     */ package com.customization.qc574527.util;
/*     */ 
/*     */ import com.api.workflow.service.WorkflowShareService;
/*     */ import com.api.workflow.util.WorkFlowSPATransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DispatchTransMethod
/*     */ {
/*     */   public String getShowDisObj(String paramString1, String paramString2) {
/*  39 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  40 */     String str = "";
/*  41 */     RolesComInfo rolesComInfo = null;
/*     */     try {
/*  43 */       rolesComInfo = new RolesComInfo();
/*  44 */     } catch (Exception exception) {
/*  45 */       exception.printStackTrace();
/*     */     } 
/*  47 */     if (arrayOfString.length == 2) {
/*  48 */       int i = Util.getIntValue(arrayOfString[0]);
/*  49 */       if (0 == i) {
/*  50 */         str = getResourceName(paramString1);
/*  51 */       } else if (1 == i) {
/*  52 */         int j = Util.getIntValue(arrayOfString[1], 7);
/*  53 */         str = SystemEnv.getHtmlLabelName(122, j) + ":" + rolesComInfo.getRolesname(paramString1);
/*     */       } 
/*     */     } 
/*  56 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourceName(String paramString) {
/*  65 */     String str = "";
/*  66 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/*  68 */       resourceComInfo = new ResourceComInfo();
/*  69 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/*  70 */       for (byte b = 0; b < arrayOfString.length; b++)
/*     */       {
/*  72 */         str = str + "<a href=\"javascript:openhrm(" + arrayOfString[b] + ")\" onclick=\"pointerXY(event);\" >" + resourceComInfo.getResourcename(arrayOfString[b]) + "</a>&nbsp;";
/*     */       }
/*  74 */     } catch (Exception exception) {
/*  75 */       exception.printStackTrace();
/*     */     } 
/*  77 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSaturationrule(String paramString1, String paramString2) {
/*  87 */     int i = Util.getIntValue(paramString2, 7);
/*  88 */     String str = SystemEnv.getHtmlLabelName(10000189, i);
/*  89 */     if ("1".equals(paramString1)) {
/*  90 */       str = SystemEnv.getHtmlLabelName(10000190, i);
/*     */     }
/*  92 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldlabel(String paramString1, String paramString2) {
/* 102 */     int i = Util.getIntValue(paramString2, 7);
/* 103 */     int j = Util.getIntValue(paramString1);
/* 104 */     return SystemEnv.getHtmlLabelName(j, i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRuleName(String paramString) {
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     String str = "";
/* 116 */     paramString = Util.null2String(paramString).trim();
/* 117 */     if (!"".equals(paramString)) {
/* 118 */       String str1 = "select distinct t.rulename from workflow_dispatch t where t.id in( select distinct t1.mainid from workflow_dispatch_dt1 t1 where t1.workflowid=" + paramString + ")";
/* 119 */       recordSet.execute(str1);
/* 120 */       while (recordSet.next()) {
/* 121 */         String str2 = Util.null2String(recordSet.getString("rulename"));
/* 122 */         if (!"".equals(str2)) {
/* 123 */           str = str + "," + str2;
/*     */         }
/*     */       } 
/*     */     } 
/* 127 */     if (!"".equals(str)) {
/* 128 */       str = str.substring(1);
/*     */     }
/* 130 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestname(String paramString1, String paramString2) {
/* 140 */     RecordSet recordSet = new RecordSet();
/* 141 */     String str1 = Util.null2String(paramString1).trim();
/* 142 */     WorkFlowSPATransMethod workFlowSPATransMethod = new WorkFlowSPATransMethod();
/* 143 */     String str2 = "";
/* 144 */     String str3 = "";
/* 145 */     String str4 = "";
/* 146 */     if (!"".equals(str1)) {
/* 147 */       String str = "select requestname,currentnodeid,workflowid from workflow_requestbase where requestid=" + str1;
/* 148 */       recordSet.execute(str);
/* 149 */       if (recordSet.next()) {
/* 150 */         str2 = Util.null2String(recordSet.getString("requestname"));
/* 151 */         str3 = Util.null2String(recordSet.getString("currentnodeid"));
/* 152 */         str4 = Util.null2String(recordSet.getString("workflowid"));
/*     */         
/* 154 */         str = "select 1 from workflow_currentoperator where requestid=" + str1 + " and userid=" + paramString2;
/* 155 */         recordSet.execute(str);
/* 156 */         if (!recordSet.next()) {
/*     */           
/* 158 */           WorkflowShareService workflowShareService = new WorkflowShareService();
/* 159 */           String[] arrayOfString = { paramString2 };
/*     */           
/* 161 */           str = "select id,userid from workflow_currentoperator where requestid=" + str1 + " and nodeid = " + str3 + " and islasttimes = 1 and isremark = '0' ";
/*     */           
/* 163 */           recordSet.execute(str);
/* 164 */           if (recordSet.next()) {
/* 165 */             int i = Util.getIntValue(recordSet.getString("id"));
/* 166 */             String str5 = Util.null2String(recordSet.getString("userid"));
/*     */             
/* 168 */             str = "select 1 from Workflow_SharedScope where wfid =" + str4 + " and requestid = " + str1 + " and iscanread = 1 and currentnodeid = " + str3 + " and userid = " + paramString2;
/*     */             
/* 170 */             recordSet.execute(str);
/* 171 */             if (!recordSet.next()) {
/* 172 */               workflowShareService.saveShareInfo("5", null, 0, 0, 0, null, arrayOfString, null, null, null, str3, str1, str4, 1, str5, i);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 179 */     str2 = " <a href='javaScript:void(0)' onClick=javaScript:openSPA4Single('/main/workflow/req?requestid=" + str1 + "&_workflowid=" + str4 + "&_workflowtype=&isovertime=0'," + str1 + ")>" + str2 + "</a><span id='wflist_" + str1 + "span'></span>";
/*     */ 
/*     */     
/* 182 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCompanyCode(String paramString) {
/* 191 */     ResourceComInfo resourceComInfo = null;
/* 192 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 194 */       resourceComInfo = new ResourceComInfo();
/* 195 */     } catch (Exception exception) {}
/*     */     
/* 197 */     String str1 = "";
/* 198 */     String str2 = resourceComInfo.getSubCompanyID(paramString);
/* 199 */     String str3 = "select t.subcompanycode from hrmsubcompany t where id='" + str2 + "'";
/* 200 */     recordSet.execute(str3);
/* 201 */     if (recordSet.next()) {
/* 202 */       str1 = Util.null2String(recordSet.getString(1));
/*     */     }
/* 204 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartCode(String paramString) {
/* 213 */     ResourceComInfo resourceComInfo = null;
/* 214 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 216 */       resourceComInfo = new ResourceComInfo();
/* 217 */     } catch (Exception exception) {}
/*     */     
/* 219 */     String str1 = "";
/* 220 */     String str2 = resourceComInfo.getDepartmentID(paramString);
/* 221 */     String str3 = "select departmentcode from hrmdepartment t where id='" + str2 + "'";
/* 222 */     recordSet.execute(str3);
/* 223 */     if (recordSet.next()) {
/* 224 */       str1 = Util.null2String(recordSet.getString(1));
/*     */     }
/* 226 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGrabCount(String paramString1, String paramString2) {
/* 233 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 234 */     RecordSet recordSet = new RecordSet();
/* 235 */     int i = 0;
/* 236 */     String str1 = "";
/* 237 */     if (arrayOfString.length >= 2) {
/* 238 */       if (!"".equals(arrayOfString[0].trim())) {
/* 239 */         str1 = str1 + " and operationdate >= '" + arrayOfString[0].trim() + "'";
/*     */       }
/*     */       
/* 242 */       if (!"".equals(arrayOfString[1].trim())) {
/* 243 */         str1 = str1 + " and operationdate <= '" + arrayOfString[1].trim() + "'";
/*     */       }
/*     */     } 
/*     */     
/* 247 */     String str2 = "select count(id)  from workflow_dispatchrecord where userid =" + paramString1 + " and dispatchtype=3 " + str1;
/* 248 */     recordSet.execute(str2);
/* 249 */     if (recordSet.next()) {
/* 250 */       i = Util.getIntValue(recordSet.getString(1), 0);
/*     */     }
/* 252 */     return i + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAutoCount(String paramString1, String paramString2) {
/* 259 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 260 */     RecordSet recordSet = new RecordSet();
/* 261 */     int i = 0;
/* 262 */     String str1 = "";
/* 263 */     if (arrayOfString.length >= 2) {
/* 264 */       if (!"".equals(arrayOfString[0].trim())) {
/* 265 */         str1 = str1 + " and operationdate >= '" + arrayOfString[0].trim() + "'";
/*     */       }
/*     */       
/* 268 */       if (!"".equals(arrayOfString[1].trim())) {
/* 269 */         str1 = str1 + " and operationdate <= '" + arrayOfString[1].trim() + "'";
/*     */       }
/*     */     } 
/* 272 */     String str2 = "select count(id)  from workflow_dispatchrecord where userid =" + paramString1 + " and dispatchtype=1 " + str1;
/* 273 */     recordSet.execute(str2);
/* 274 */     if (recordSet.next()) {
/* 275 */       i = Util.getIntValue(recordSet.getString(1), 0);
/*     */     }
/* 277 */     return i + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getManuallyCount(String paramString1, String paramString2) {
/* 284 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 285 */     RecordSet recordSet = new RecordSet();
/* 286 */     int i = 0;
/* 287 */     String str1 = "";
/* 288 */     if (arrayOfString.length >= 2) {
/* 289 */       if (!"".equals(arrayOfString[0].trim())) {
/* 290 */         str1 = str1 + " and operationdate >= '" + arrayOfString[0].trim() + "'";
/*     */       }
/*     */       
/* 293 */       if (!"".equals(arrayOfString[1].trim())) {
/* 294 */         str1 = str1 + " and operationdate <= '" + arrayOfString[1].trim() + "'";
/*     */       }
/*     */     } 
/* 297 */     String str2 = "select count(id)  from workflow_dispatchrecord where userid =" + paramString1 + " and dispatchtype=2 " + str1;
/* 298 */     recordSet.execute(str2);
/* 299 */     if (recordSet.next()) {
/* 300 */       i = Util.getIntValue(recordSet.getString(1), 0);
/*     */     }
/* 302 */     return i + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDispatchCount(String paramString1, String paramString2) {
/* 309 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 310 */     RecordSet recordSet = new RecordSet();
/* 311 */     int i = 0;
/* 312 */     String str1 = "";
/* 313 */     if (arrayOfString.length >= 2) {
/* 314 */       if (!"".equals(arrayOfString[0].trim())) {
/* 315 */         str1 = str1 + " and operationdate >= '" + arrayOfString[0].trim() + "'";
/*     */       }
/*     */       
/* 318 */       if (!"".equals(arrayOfString[1].trim())) {
/* 319 */         str1 = str1 + " and operationdate <= '" + arrayOfString[1].trim() + "'";
/*     */       }
/*     */     } 
/* 322 */     String str2 = "select count(id)  from workflow_dispatchrecord where userid =" + paramString1 + " " + str1;
/* 323 */     recordSet.execute(str2);
/* 324 */     if (recordSet.next()) {
/* 325 */       i = Util.getIntValue(recordSet.getString(1), 0);
/*     */     }
/* 327 */     return i + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDisworkload(String paramString) {
/* 334 */     RecordSet recordSet = new RecordSet();
/* 335 */     int i = 0;
/* 336 */     String str = "";
/* 337 */     if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/* 338 */       str = "select * from workflow_dispatch where ','+disworker+',' like '%," + paramString + ",%' ";
/* 339 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/* 340 */       str = "select * from workflow_dispatch where CONCAT(',',disworker,',') like '%," + paramString + ",%' ";
/*     */     } else {
/* 342 */       str = "select * from workflow_dispatch where ','||disworker||',' like '%," + paramString + ",%' ";
/*     */     } 
/* 344 */     recordSet.execute(str);
/* 345 */     if (recordSet.next()) {
/* 346 */       int j = Util.getIntValue(Util.null2String(recordSet.getString("disworkload")), 0);
/* 347 */       i = j;
/*     */     } 
/* 349 */     return i + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Map<String, Object>> getDateTypeOptions(String paramString, int paramInt) {
/* 360 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 361 */     HashMap<Object, Object> hashMap = null;
/* 362 */     hashMap = new HashMap<>();
/* 363 */     hashMap.put("key", "0");
/* 364 */     hashMap.put("selected", Boolean.valueOf("".equals(paramString)));
/* 365 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(332, paramInt));
/* 366 */     arrayList.add(hashMap);
/*     */     
/* 368 */     hashMap = new HashMap<>();
/* 369 */     hashMap.put("key", "1");
/* 370 */     hashMap.put("selected", Boolean.valueOf("1".equals(paramString)));
/* 371 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(15537, paramInt));
/* 372 */     arrayList.add(hashMap);
/*     */     
/* 374 */     hashMap = new HashMap<>();
/* 375 */     hashMap.put("key", "2");
/* 376 */     hashMap.put("selected", Boolean.valueOf("2".equals(paramString)));
/* 377 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(15539, paramInt));
/* 378 */     arrayList.add(hashMap);
/*     */     
/* 380 */     hashMap = new HashMap<>();
/* 381 */     hashMap.put("key", "3");
/* 382 */     hashMap.put("selected", Boolean.valueOf("3".equals(paramString)));
/* 383 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(15541, paramInt));
/* 384 */     arrayList.add(hashMap);
/*     */     
/* 386 */     hashMap = new HashMap<>();
/* 387 */     hashMap.put("key", "7");
/* 388 */     hashMap.put("selected", Boolean.valueOf("7".equals(paramString)));
/* 389 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(27347, paramInt));
/* 390 */     arrayList.add(hashMap);
/*     */     
/* 392 */     hashMap = new HashMap<>();
/* 393 */     hashMap.put("key", "4");
/* 394 */     hashMap.put("selected", Boolean.valueOf("4".equals(paramString)));
/* 395 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(21904, paramInt));
/* 396 */     arrayList.add(hashMap);
/*     */     
/* 398 */     hashMap = new HashMap<>();
/* 399 */     hashMap.put("key", "5");
/* 400 */     hashMap.put("selected", Boolean.valueOf("5".equals(paramString)));
/* 401 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(15384, paramInt));
/* 402 */     arrayList.add(hashMap);
/*     */     
/* 404 */     hashMap = new HashMap<>();
/* 405 */     hashMap.put("key", "8");
/* 406 */     hashMap.put("selected", Boolean.valueOf("8".equals(paramString)));
/* 407 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(81716, paramInt));
/* 408 */     arrayList.add(hashMap);
/*     */     
/* 410 */     hashMap = new HashMap<>();
/* 411 */     hashMap.put("key", "6");
/* 412 */     hashMap.put("selected", Boolean.valueOf("6".equals(paramString)));
/* 413 */     hashMap.put("showname", SystemEnv.getHtmlLabelName(32530, paramInt));
/* 414 */     arrayList.add(hashMap);
/*     */     
/* 416 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getFormItemForBrowserDate(String paramString1, String paramString2, String paramString3, int paramInt, List<Map<String, Object>> paramList, Map<String, Object> paramMap) {
/* 421 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 422 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 424 */     if ("".equals(Util.null2String(paramString3))) {
/* 425 */       arrayList.add(paramString1 + "_selectType");
/* 426 */       arrayList.add(paramString1 + "_fromDate");
/* 427 */       arrayList.add(paramString1 + "_toDate");
/* 428 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 429 */       hashMap1.put(paramString1 + "_selectType", "");
/* 430 */       hashMap.put("value", hashMap1);
/*     */     } else {
/* 432 */       arrayList.add(paramString1);
/* 433 */       hashMap.put("value", paramString3);
/*     */     } 
/* 435 */     int i = 2;
/* 436 */     int j = 6;
/* 437 */     int k = 17;
/* 438 */     if (null != paramMap) {
/* 439 */       if (!"".equals(Util.null2String(paramMap.get("colSpan")))) {
/* 440 */         i = Util.getIntValue((String)paramMap.get("colSpan"));
/*     */       }
/* 442 */       if (!"".equals(Util.null2String(paramMap.get("labelcol")))) {
/* 443 */         j = Util.getIntValue((String)paramMap.get("labelcol"));
/*     */       }
/* 445 */       if (!"".equals(Util.null2String(paramMap.get("fieldcol")))) {
/* 446 */         k = Util.getIntValue((String)paramMap.get("fieldcol"));
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 451 */     hashMap.put("label", paramString2);
/* 452 */     hashMap.put("colSpan", Integer.valueOf(i));
/* 453 */     hashMap.put("labelcol", Integer.valueOf(j));
/* 454 */     hashMap.put("fieldcol", Integer.valueOf(k));
/* 455 */     hashMap.put("domkey", arrayList);
/* 456 */     hashMap.put("formItemType", "DATE");
/* 457 */     hashMap.put("conditionType", "DATE");
/* 458 */     hashMap.put("viewAttr", Integer.valueOf(paramInt));
/* 459 */     hashMap.put("options", paramList);
/* 460 */     if (paramInt == 3) {
/* 461 */       hashMap.put("rules", "required");
/*     */     }
/* 463 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getFormItemForBrowser(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5, String paramString6, Map<String, Object> paramMap, Map<String, String> paramMap1) {
/* 469 */     RecordSet recordSet = new RecordSet();
/* 470 */     String str1 = ",17,";
/* 471 */     String str2 = ",274,13,63,";
/* 472 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 473 */     ArrayList<String> arrayList = new ArrayList();
/* 474 */     arrayList.add(paramString1);
/*     */     
/* 476 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 478 */     paramString4 = Util.null2String(paramString4);
/* 479 */     if (!"".equals(paramString4)) {
/* 480 */       String str = "";
/* 481 */       List<HashMap<Object, Object>> list = new ArrayList();
/* 482 */       if ("17".equals(paramString3) || "65".equals(paramString3)) {
/* 483 */         list = getBrowserReplaceDatas(paramString3, paramString4, paramString5);
/* 484 */         str = getBrowserShowName(paramString3, paramString4, paramString5);
/* 485 */       } else if ("-99991".equals(paramString3) || "formField".equals(paramString3) || "workflowNode".equals(paramString3)) {
/* 486 */         str = paramMap1.get("showname");
/* 487 */         HashMap<Object, Object> hashMap = new HashMap<>(2);
/* 488 */         hashMap.put("id", paramString4);
/* 489 */         hashMap.put("name", str);
/* 490 */         list.add(hashMap);
/*     */       } 
/* 492 */       hashMap1.put("showName", str);
/* 493 */       hashMap2.put("replaceDatas", list);
/*     */     } 
/* 495 */     if (null == paramMap) {
/* 496 */       paramMap = new HashMap<>();
/*     */     }
/*     */     
/* 499 */     hashMap2.put("asynLoadAll", Boolean.valueOf(false));
/* 500 */     hashMap2.put("hasAddBtn", Boolean.valueOf(false));
/* 501 */     if (str2.indexOf("," + paramString3 + ",") > -1) {
/* 502 */       hashMap2.put("hasAdvanceSerach", Boolean.valueOf(false));
/*     */     } else {
/* 504 */       hashMap2.put("hasAdvanceSerach", Boolean.valueOf(true));
/*     */     } 
/* 506 */     hashMap2.put("isAutoComplete", Integer.valueOf(1));
/* 507 */     hashMap2.put("isDetail", Integer.valueOf(0));
/* 508 */     hashMap2.put("isMultCheckbox", Boolean.valueOf(false));
/* 509 */     hashMap2.put("linkUrl", paramString6);
/* 510 */     hashMap2.put("title", paramString2);
/* 511 */     hashMap2.put("type", paramString3);
/* 512 */     hashMap2.put("viewAttr", Integer.valueOf(paramInt));
/* 513 */     if (str1.indexOf("," + paramString3 + ",") > -1) {
/* 514 */       hashMap2.put("isSingle", Boolean.valueOf(false));
/*     */     } else {
/* 516 */       hashMap2.put("isSingle", Boolean.valueOf(true));
/*     */     } 
/* 518 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 519 */     hashMap3.putAll(paramMap);
/* 520 */     hashMap2.put("dataParams", paramMap);
/* 521 */     if ("1".equals(paramString3)) {
/*     */       
/* 523 */       if (paramMap1.containsKey("completesqlwhere")) {
/* 524 */         String str = Util.null2String(paramMap1.get("completesqlwhere"));
/* 525 */         hashMap3.put("sqlwhere", str);
/*     */       } 
/* 527 */       if (paramMap1.containsKey("searchsqlwhere")) {
/* 528 */         String str = Util.null2String(paramMap1.get("searchsqlwhere"));
/* 529 */         paramMap.put("sqlwhere", str);
/*     */       } 
/*     */     } 
/*     */     
/* 533 */     hashMap2.put("completeParams", hashMap3);
/* 534 */     hashMap1.put("browserConditionParam", hashMap2);
/* 535 */     hashMap1.put("label", paramString2);
/* 536 */     hashMap1.put("colSpan", Integer.valueOf(2));
/* 537 */     hashMap1.put("labelcol", Integer.valueOf(6));
/* 538 */     hashMap1.put("fieldcol", Integer.valueOf(17));
/* 539 */     hashMap1.put("domkey", arrayList);
/* 540 */     hashMap1.put("formItemType", "BROWSER");
/* 541 */     hashMap1.put("conditionType", "BROWSER");
/* 542 */     hashMap1.put("value", (paramString4 == null) ? "" : paramString4);
/* 543 */     hashMap1.put("viewAttr", Integer.valueOf(paramInt));
/* 544 */     hashMap1.put("browserType", paramString3);
/* 545 */     if (paramInt == 3) {
/* 546 */       hashMap1.put("rules", "required");
/*     */     }
/* 548 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public static List getBrowserReplaceDatas(String paramString1, String paramString2, String paramString3) {
/* 552 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 553 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString2, ",");
/* 554 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 555 */       String str1 = arrayList1.get(b);
/* 556 */       String str2 = getBrowserShowName(paramString1, str1, paramString3);
/* 557 */       HashMap<Object, Object> hashMap = new HashMap<>(2);
/* 558 */       hashMap.put("id", str1);
/* 559 */       hashMap.put("name", str2);
/* 560 */       arrayList.add(hashMap);
/*     */     } 
/* 562 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBrowserShowName(String paramString1, String paramString2, String paramString3) {
/* 571 */     RecordSet recordSet = new RecordSet();
/* 572 */     BaseBean baseBean = new BaseBean();
/* 573 */     String str = "";
/*     */     try {
/* 575 */       ArrayList<String> arrayList = Util.TokenizerString(paramString2, ",");
/* 576 */       if (paramString1.equals("1") || paramString1.equals("17")) {
/* 577 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 579 */           str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 582 */       else if (paramString1.equals("65")) {
/* 583 */         str = str + (new RolesComInfo()).getRolesname(paramString2) + ",";
/*     */       } 
/* 585 */       if (str.endsWith(",")) {
/* 586 */         str = str.substring(0, str.length() - 1);
/*     */       }
/* 588 */     } catch (Exception exception) {
/* 589 */       baseBean.writeLog(exception);
/*     */     } 
/* 591 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, String> getFromDateAndEndDate(String paramString1, String paramString2, String paramString3) {
/* 603 */     String str1 = TimeUtil.getCurrentDateString();
/*     */     
/* 605 */     String str2 = "";
/* 606 */     String str3 = "";
/* 607 */     if (paramString1.equals("1")) {
/*     */       
/* 609 */       str2 = str1;
/* 610 */       str3 = str1;
/* 611 */     } else if (paramString1.equals("2")) {
/*     */       
/* 613 */       str2 = TimeUtil.getFirstDayOfWeek();
/* 614 */     } else if (paramString1.equals("3")) {
/*     */       
/* 616 */       str2 = TimeUtil.getFirstDayOfMonth();
/* 617 */     } else if (paramString1.equals("4")) {
/*     */       
/* 619 */       str2 = TimeUtil.getFirstDayOfSeason();
/* 620 */     } else if (paramString1.equals("5")) {
/*     */       
/* 622 */       str2 = TimeUtil.getFirstDayOfTheYear();
/* 623 */     } else if (paramString1.equals("6")) {
/*     */       
/* 625 */       str2 = paramString2;
/* 626 */       str3 = paramString3;
/* 627 */     } else if (paramString1.equals("7")) {
/*     */       
/* 629 */       str2 = TimeUtil.getLastMonthBeginDay();
/* 630 */       str3 = TimeUtil.getLastMonthEndDay();
/* 631 */     } else if (paramString1.equals("8")) {
/*     */       
/* 633 */       str2 = TimeUtil.getFirstDayOfLastYear();
/* 634 */       str3 = TimeUtil.getEndDayOfLastYear();
/*     */     } 
/* 636 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 637 */     hashMap.put("fromDate", str2);
/* 638 */     hashMap.put("toDate", str3);
/* 639 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/util/DispatchTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */