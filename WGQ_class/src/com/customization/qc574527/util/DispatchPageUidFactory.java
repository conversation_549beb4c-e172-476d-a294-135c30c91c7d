/*    */ package com.customization.qc574527.util;
/*    */ 
/*    */ import java.util.UUID;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DispatchPageUidFactory
/*    */ {
/*    */   public static String getDispatchPageUid(String paramString) {
/* 13 */     String str = "";
/* 14 */     if ("dispatchsetlist".equals(paramString)) {
/* 15 */       str = "26846dd0-ef97-44d3-ab0f-7b71a9d57730";
/* 16 */     } else if ("dispatchdetaillist".equals(paramString)) {
/* 17 */       str = "ad8750fb-ea3b-44f7-b7bf-e56e64e5315f";
/* 18 */     } else if ("manuallydispatch".equals(paramString)) {
/* 19 */       str = "40bebf2c-d566-4ec5-9fcf-************";
/* 20 */     } else if ("dispatchcount".equals(paramString)) {
/* 21 */       str = "49e94fdd-1708-4a1b-b063-c8694bf5e844";
/*    */     } 
/* 23 */     return str;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static void main(String[] paramArrayOfString) {
/* 29 */     UUID uUID = UUID.randomUUID();
/* 30 */     System.out.println(uUID);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc574527/util/DispatchPageUidFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */