/*    */ package com.customization.cube.list;
/*    */ 
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.cube.cmd.app.WaterMarkImg;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = WaterMarkImg.class, desc = "前端获取查询水印的时候更改查询错误的工作流href")
/*    */ public class CustomChangeListHrefLinkCmd
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 20 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 22 */     WaterMarkImg waterMarkImg = (WaterMarkImg)paramCommand;
/* 23 */     User user = waterMarkImg.getUser();
/*    */     
/* 25 */     Map map = waterMarkImg.getParams();
/* 26 */     if (user == null) {
/* 27 */       return (Map)hashMap;
/*    */     }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 46 */     return (Map)nextExecute(paramCommand);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/cube/list/CustomChangeListHrefLinkCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */