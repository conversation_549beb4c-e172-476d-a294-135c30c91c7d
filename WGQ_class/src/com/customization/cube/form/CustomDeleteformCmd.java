/*     */ package com.customization.cube.form;
/*     */ 
/*     */ import com.engine.core.interceptor.AbstractCommandProxy;
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.cube.biz.RightHelper;
/*     */ import com.engine.cube.cmd.form.DeleteformCmd;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomDeleteformCmd
/*     */   extends AbstractCommandProxy<Map<String, Object>>
/*     */ {
/*     */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/*     */     Map<String, String> map1;
/*  21 */     DeleteformCmd deleteformCmd = (DeleteformCmd)paramCommand;
/*     */     
/*  23 */     Map map = deleteformCmd.getParams();
/*     */ 
/*     */ 
/*     */     
/*  27 */     deleteformCmd.setParams(map);
/*  28 */     User user = deleteformCmd.getUser();
/*     */     
/*  30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  31 */     if (!RightHelper.checkBackRight("FORMMODEAPP:ALL", user, hashMap)) {
/*  32 */       return (Map)hashMap;
/*     */     }
/*     */     
/*  35 */     int i = Util.getIntValue(Util.null2String(map.get("formid")));
/*  36 */     RecordSet recordSet = new RecordSet();
/*  37 */     String str = "select a.tablename,m.isvirtualform from workflow_bill a left join ModeFormExtend m on a.id=m.formid where a.id=?";
/*  38 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(i) });
/*  39 */     if (recordSet.next()) {
/*  40 */       boolean bool = Util.null2String(recordSet.getString("isvirtualform")).equals("1");
/*  41 */       String str1 = Util.null2String(recordSet.getString("tablename"));
/*     */       
/*  43 */       if (bool || i > 0) {
/*  44 */         map1 = (Map)nextExecute(paramCommand);
/*     */       } else {
/*  46 */         boolean bool1 = checkFormCanDelete(i, 1);
/*  47 */         if (!bool1) {
/*  48 */           map1.put("errorCode", "-1");
/*  49 */           map1.put("error", SystemEnv.getHtmlLabelName(125644, user.getLanguage()));
/*     */         } else {
/*  51 */           boolean bool2 = doDeleteFormTable(i, str1);
/*  52 */           if (bool2) {
/*  53 */             RecordSet recordSet1 = new RecordSet();
/*  54 */             recordSet1.executeUpdate("delete from workflow_bill where id=?", new Object[] { Integer.valueOf(i) });
/*  55 */             recordSet1.executeQuery("select id from workflow_billfield where fieldhtmltype=5 and billid=?", new Object[] { Integer.valueOf(i) });
/*  56 */             while (recordSet1.next()) {
/*  57 */               int j = recordSet1.getInt("id");
/*  58 */               recordSet1.executeUpdate("delete from workflow_SelectItem where fieldid=?", new Object[] { Integer.valueOf(j) });
/*     */             } 
/*  60 */             recordSet1.executeUpdate("delete from workflow_billfield where billid=" + i, new Object[0]);
/*     */             
/*  62 */             recordSet1.executeQuery("select id,tablename from Workflow_billdetailtable where billid=?", new Object[] { Integer.valueOf(i) });
/*  63 */             while (recordSet1.next()) {
/*  64 */               if (!bool) {
/*  65 */                 String str2 = recordSet1.getString("tablename");
/*  66 */                 doDeleteFormTable(i, str2);
/*     */               } 
/*  68 */               recordSet1.executeUpdate("delete from Workflow_billdetailtable where id=" + recordSet1.getInt("id"), new Object[0]);
/*     */             } 
/*     */             
/*  71 */             recordSet1.executeUpdate("delete from workflow_formdetailinfo where formid=?", new Object[] { Integer.valueOf(i) });
/*  72 */             recordSet1.executeUpdate("delete from workflow_nodehtmllayout where formid=? and isbill=1", new Object[] { Integer.valueOf(i) });
/*  73 */             recordSet1.executeUpdate("delete from workflow_nodeform_form where formid=? and isbill=1", new Object[] { Integer.valueOf(i) });
/*  74 */             recordSet1.executeUpdate("delete from workflow_nodeformgroup_form where formid=? and isbill=1", new Object[] { Integer.valueOf(i) });
/*  75 */             recordSet1.executeUpdate("delete from ModeFormExtend where formid=?", new Object[] { Integer.valueOf(i) });
/*  76 */             recordSet1.executeUpdate("delete from AppFormInfo where formid=?", new Object[] { Integer.valueOf(i) });
/*  77 */             recordSet1.executeUpdate("delete from Formmodelog where objid=?", new Object[] { Integer.valueOf(i) });
/*     */           } else {
/*  79 */             map1.put("errorCode", "-1");
/*  80 */             map1.put("error", SystemEnv.getHtmlLabelName(388508, user.getLanguage()));
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  86 */     return (Map)map1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkFormCanDelete(int paramInt1, int paramInt2) {
/*  96 */     RecordSet recordSet = new RecordSet();
/*  97 */     boolean bool = false;
/*     */ 
/*     */     
/* 100 */     if (paramInt2 == 1 && paramInt1 < 0) {
/*     */ 
/*     */       
/* 103 */       recordSet.executeQuery("select count(formid) from workflow_base where formid=? ", new Object[] { Integer.valueOf(paramInt1) });
/* 104 */       if (recordSet.next() && recordSet.getInt(1) == 0) {
/* 105 */         bool = true;
/*     */       } else {
/* 107 */         return false;
/*     */       } 
/*     */ 
/*     */       
/* 111 */       recordSet.executeQuery("select count(1) from modeinfo where formid = ? ", new Object[] { Integer.valueOf(paramInt1) });
/* 112 */       if (recordSet.next() && recordSet.getInt(1) == 0) {
/* 113 */         bool = true;
/*     */       } else {
/* 115 */         return false;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 120 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean doDeleteFormTable(int paramInt, String paramString) {
/* 130 */     RecordSet recordSet = new RecordSet();
/* 131 */     boolean bool = true;
/* 132 */     paramString = Util.null2String(paramString).toLowerCase();
/* 133 */     recordSet.executeQuery("select 1 from " + paramString, new Object[0]);
/* 134 */     if (recordSet.next()) {
/*     */       
/* 136 */       byte b = 1;
/* 137 */       String str1 = "";
/*     */       while (true) {
/* 139 */         str1 = "f" + Math.abs(paramInt) + "_b" + b + "_" + paramString;
/* 140 */         if (str1.length() > 30) {
/* 141 */           str1 = str1.substring(0, 30);
/*     */         }
/* 143 */         if (!checkTableExists(str1)) {
/*     */           break;
/*     */         }
/* 146 */         b++;
/*     */       } 
/* 148 */       String str2 = "";
/* 149 */       if ("oracle".equals(recordSet.getDBType().toLowerCase())) {
/* 150 */         str2 = "ALTER TABLE " + paramString + " RENAME TO " + str1;
/* 151 */       } else if ("mysql".equals(recordSet.getDBType().toLowerCase())) {
/* 152 */         str2 = "ALTER TABLE " + paramString + " RENAME TO " + str1;
/*     */       }
/* 154 */       else if ("postgresql".equals(recordSet.getDBType().toLowerCase())) {
/* 155 */         str2 = "ALTER TABLE " + paramString + " RENAME TO " + str1;
/*     */       } else {
/*     */         
/* 158 */         str2 = "EXEC sp_rename '" + paramString + "', '" + str1 + "'";
/*     */       } 
/* 160 */       bool = recordSet.executeUpdate(str2, new Object[0]);
/*     */     } else {
/*     */       
/* 163 */       bool = recordSet.executeUpdate("drop table " + paramString, new Object[0]);
/*     */     } 
/*     */     
/* 166 */     if ("oracle".equals(recordSet.getDBType().toLowerCase())) {
/* 167 */       recordSet.executeQuery("select * from user_triggers where upper(trigger_name)=upper('" + paramString + "_Id_Trigger')", new Object[0]);
/* 168 */       if (recordSet.next()) {
/* 169 */         recordSet.executeUpdate("drop trigger " + paramString + "_Id_Trigger", new Object[0]);
/*     */       }
/* 171 */       recordSet.executeUpdate("drop sequence " + paramString + "_Id", new Object[0]);
/*     */     } 
/*     */     
/* 174 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkTableExists(String paramString) {
/* 183 */     RecordSet recordSet = new RecordSet();
/* 184 */     String str1 = recordSet.getDBType();
/* 185 */     String str2 = "";
/* 186 */     boolean bool = false;
/* 187 */     if (str1.equalsIgnoreCase("oracle")) {
/* 188 */       str2 = "select 1 from user_tables where TABLE_NAME = upper('" + paramString + "')";
/* 189 */     } else if (str1.toLowerCase().indexOf("sqlserver") > -1 || str1.equalsIgnoreCase("sybase")) {
/* 190 */       str2 = "select 1 from sysobjects where name = '" + paramString + "' ";
/* 191 */     } else if (str1.equalsIgnoreCase("informix")) {
/* 192 */       str2 = "select 1 from systables where lower(tabname) = lower('" + paramString + "') ";
/* 193 */     } else if (str1.equalsIgnoreCase("mysql")) {
/* 194 */       str2 = "select 1 from information_schema.Tables where LOWER(Table_Name)=LOWER('" + paramString + "') ";
/*     */     }
/* 196 */     else if (str1.equalsIgnoreCase("postgresql")) {
/* 197 */       str2 = "select 1 from information_schema.Tables where LOWER(Table_Name)=LOWER('" + paramString + "') ";
/*     */     }
/* 199 */     else if (str1.equalsIgnoreCase("db2")) {
/* 200 */       str2 = "select 1 from SYSIBM.SYSTABLES where lower(name)= lower('" + paramString + "') ";
/*     */     } else {
/* 202 */       str2 = "select 1 from " + paramString;
/*     */     } 
/* 204 */     recordSet.executeQuery(str2, new Object[0]);
/* 205 */     if (recordSet.next()) {
/* 206 */       bool = true;
/*     */     }
/*     */ 
/*     */     
/* 210 */     if (!bool) {
/* 211 */       bool = recordSet.executeQuery("select 1 from " + paramString, new Object[0]);
/*     */     }
/*     */     
/* 214 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/cube/form/CustomDeleteformCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */