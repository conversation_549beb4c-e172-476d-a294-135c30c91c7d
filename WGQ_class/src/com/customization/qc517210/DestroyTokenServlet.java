/*    */ package com.customization.qc517210;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import java.util.HashMap;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DestroyTokenServlet
/*    */   extends HttpServlet
/*    */ {
/* 19 */   private Logger logger = LoggerFactory.getLogger(DestroyTokenServlet.class);
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException {
/* 21 */     this.logger.info("================DestroyTokenServlet destroyToken start...===============================");
/* 22 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 23 */     boolean bool = false;
/*    */     try {
/* 25 */       String str = paramHttpServletRequest.getParameter("qystoken");
/* 26 */       if (str != null) {
/* 27 */         RecordSet recordSet = new RecordSet();
/* 28 */         bool = recordSet.executeUpdate("update sso_login_oa_lzgaj set isdestroyed='1' where token=?", new Object[] { str });
/*    */       } 
/*    */       
/* 31 */       hashMap.put("hasDestroyed", Boolean.valueOf(bool));
/* 32 */       paramHttpServletResponse.getWriter().write(JSON.toJSONString(hashMap));
/*    */     }
/* 34 */     catch (Exception exception) {
/* 35 */       exception.printStackTrace();
/* 36 */       this.logger.info("================DestroyTokenServlet destroyToken error:" + exception.getMessage());
/*    */     } 
/* 38 */     this.logger.info("================DestroyTokenServlet destroyToken end...===============================");
/*    */   }
/*    */ 
/*    */   
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException {
/* 43 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc517210/DestroyTokenServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */