/*     */ package com.customization.qc517210;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import java.io.IOException;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.OnLineMonitor;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.login.VerifyLogin;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ 
/*     */ public class GetUserServlet
/*     */   extends HttpServlet
/*     */ {
/*  24 */   private Logger logger = LoggerFactory.getLogger(GetUserServlet.class);
/*     */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException {
/*  26 */     this.logger.info("================GetUserServlet getUser start...===============================");
/*  27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  28 */     String str = "";
/*     */     try {
/*  30 */       String str1 = paramHttpServletRequest.getParameter("qystoken");
/*  31 */       String str2 = paramHttpServletRequest.getParameter("targeturl");
/*  32 */       if (str1 != null) {
/*  33 */         RecordSet recordSet = new RecordSet();
/*  34 */         recordSet.execute("SELECT * FROM  SSO_LOGIN_OA_QYS WHERE token='" + str1 + "' AND isdestroyed !='1'");
/*  35 */         this.logger.info("SELECT * FROM  SSO_LOGIN_OA_QYS WHERE token='" + str1 + "' AND isdestroyed !='1'");
/*  36 */         if (recordSet.next()) {
/*  37 */           str = recordSet.getString("loginid");
/*     */         }
/*     */         
/*  40 */         if (!str.equals("")) {
/*  41 */           String str3 = getLogintype(paramHttpServletRequest);
/*     */           
/*  43 */           recordSet.executeSql("select * from HrmResource where loginid='" + str + "' and status<4 and (accounttype !=1 or accounttype is null)");
/*     */           
/*  45 */           User user = null;
/*  46 */           if (recordSet.next()) {
/*     */             
/*  48 */             paramHttpServletRequest.getSession(true).setAttribute("weaver_login_type", "OALogin");
/*     */             
/*  50 */             if (str3.equals("1")) {
/*     */               
/*  52 */               user = new User();
/*  53 */               user.setUid(recordSet.getInt("id"));
/*  54 */               user.setLoginid(recordSet.getString("loginid"));
/*  55 */               user.setFirstname(recordSet.getString("firstname"));
/*  56 */               user.setLastname(recordSet.getString("lastname"));
/*  57 */               user.setAliasname(recordSet.getString("aliasname"));
/*  58 */               user.setTitle(recordSet.getString("title"));
/*  59 */               user.setTitlelocation(recordSet.getString("titlelocation"));
/*  60 */               user.setSex(recordSet.getString("sex"));
/*  61 */               user.setPwd(recordSet.getString("password"));
/*  62 */               String str4 = recordSet.getString("systemlanguage");
/*  63 */               user.setLanguage(Util.getIntValue(str4, 0));
/*     */               
/*  65 */               user.setTelephone(recordSet.getString("telephone"));
/*  66 */               user.setMobile(recordSet.getString("mobile"));
/*  67 */               user.setMobilecall(recordSet.getString("mobilecall"));
/*  68 */               user.setEmail(recordSet.getString("email"));
/*  69 */               user.setCountryid(recordSet.getString("countryid"));
/*  70 */               user.setLocationid(recordSet.getString("locationid"));
/*  71 */               user.setResourcetype(recordSet.getString("resourcetype"));
/*  72 */               user.setStartdate(recordSet.getString("startdate"));
/*  73 */               user.setEnddate(recordSet.getString("enddate"));
/*  74 */               user.setContractdate(recordSet.getString("contractdate"));
/*  75 */               user.setJobtitle(recordSet.getString("jobtitle"));
/*  76 */               user.setJobgroup(recordSet.getString("jobgroup"));
/*  77 */               user.setJobactivity(recordSet.getString("jobactivity"));
/*  78 */               user.setJoblevel(recordSet.getString("joblevel"));
/*  79 */               user.setSeclevel(recordSet.getString("seclevel"));
/*  80 */               user.setUserDepartment(Util.getIntValue(recordSet.getString("departmentid"), 0));
/*  81 */               user.setUserSubCompany1(Util.getIntValue(recordSet.getString("subcompanyid1"), 0));
/*  82 */               user.setUserSubCompany2(Util.getIntValue(recordSet.getString("subcompanyid2"), 0));
/*  83 */               user.setUserSubCompany3(Util.getIntValue(recordSet.getString("subcompanyid3"), 0));
/*  84 */               user.setUserSubCompany4(Util.getIntValue(recordSet.getString("subcompanyid4"), 0));
/*  85 */               user.setManagerid(recordSet.getString("managerid"));
/*  86 */               user.setAssistantid(recordSet.getString("assistantid"));
/*  87 */               user.setPurchaselimit(recordSet.getString("purchaselimit"));
/*  88 */               user.setCurrencyid(recordSet.getString("currencyid"));
/*  89 */               user.setLastlogindate(recordSet.getString("currentdate"));
/*  90 */               user.setLogintype("1");
/*  91 */               user.setAccount(recordSet.getString("account"));
/*     */               
/*  93 */               user.setLoginip(paramHttpServletRequest.getRemoteAddr());
/*  94 */               paramHttpServletRequest.getSession(true).setMaxInactiveInterval(86400);
/*  95 */               paramHttpServletRequest.getSession(true).setAttribute("weaver_user@bean", user);
/*  96 */               paramHttpServletRequest.getSession(true).setAttribute("browser_isie", getisIE(paramHttpServletRequest));
/*     */ 
/*     */               
/*  99 */               if (user.getUID() != 1) {
/* 100 */                 VerifyLogin verifyLogin = new VerifyLogin();
/* 101 */                 List list = verifyLogin.getAccountsById(user.getUID());
/* 102 */                 paramHttpServletRequest.getSession(true).setAttribute("accounts", list);
/*     */               } 
/*     */               
/* 105 */               paramHttpServletRequest.getSession(true).setAttribute("moniter", new OnLineMonitor("" + user.getUID(), user.getLoginip()));
/* 106 */               Util.setCookie(paramHttpServletResponse, "loginfileweaver", "/login/Login.jsp?logintype=1", 172800);
/* 107 */               Util.setCookie(paramHttpServletResponse, "loginidweaver", "" + user.getUID(), 172800);
/* 108 */               Util.setCookie(paramHttpServletResponse, "languageidweaver", str4, 172800);
/*     */ 
/*     */ 
/*     */               
/* 112 */               SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 113 */               sysMaintenanceLog.resetParameter();
/* 114 */               sysMaintenanceLog.setRelatedId(recordSet.getInt("id"));
/* 115 */               sysMaintenanceLog.setRelatedName((recordSet.getString("firstname") + " " + recordSet.getString("lastname")).trim());
/* 116 */               sysMaintenanceLog.setOperateType("6");
/* 117 */               sysMaintenanceLog.setOperateDesc("");
/* 118 */               sysMaintenanceLog.setOperateItem("60");
/* 119 */               sysMaintenanceLog.setOperateUserid(recordSet.getInt("id"));
/* 120 */               sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/*     */               try {
/* 122 */                 sysMaintenanceLog.setSysLogInfo();
/* 123 */               } catch (Exception exception) {
/* 124 */                 exception.printStackTrace();
/*     */               }
/*     */             
/* 127 */             } else if (str3.equals("2")) {
/*     */               
/* 129 */               user = new User();
/* 130 */               user.setUid(recordSet.getInt("id"));
/* 131 */               user.setLoginid(str);
/* 132 */               user.setPwd(recordSet.getString("PortalPassword"));
/* 133 */               user.setFirstname(recordSet.getString("name"));
/* 134 */               String str4 = recordSet.getString("language");
/* 135 */               user.setLanguage(Util.getIntValue(str4, 0));
/* 136 */               user.setUserDepartment(Util.getIntValue(recordSet.getString("department"), 0));
/* 137 */               user.setUserSubCompany1(Util.getIntValue(recordSet.getString("subcompanyid1"), 0));
/* 138 */               user.setManagerid(recordSet.getString("manager"));
/* 139 */               user.setCountryid(recordSet.getString("country"));
/* 140 */               user.setEmail(recordSet.getString("email"));
/* 141 */               user.setAgent(Util.getIntValue(recordSet.getString("agent"), 0));
/* 142 */               user.setType(Util.getIntValue(recordSet.getString("type"), 0));
/* 143 */               user.setParentid(Util.getIntValue(recordSet.getString("parentid"), 0));
/* 144 */               user.setProvince(Util.getIntValue(recordSet.getString("province"), 0));
/* 145 */               user.setCity(Util.getIntValue(recordSet.getString("city"), 0));
/* 146 */               user.setLogintype("2");
/* 147 */               user.setSeclevel(recordSet.getString("seclevel"));
/* 148 */               user.setLoginip(paramHttpServletRequest.getRemoteAddr());
/* 149 */               paramHttpServletRequest.getSession(true).setAttribute("weaver_user@bean", user);
/* 150 */               paramHttpServletRequest.getSession(true).setAttribute("browser_isie", getisIE(paramHttpServletRequest));
/*     */               
/* 152 */               Util.setCookie(paramHttpServletResponse, "loginfileweaver", "/login/Login.jsp?logintype=1", 172800);
/* 153 */               Util.setCookie(paramHttpServletResponse, "loginidweaver", "" + user.getUID(), 172800);
/* 154 */               Util.setCookie(paramHttpServletResponse, "languageidweaver", str4, 172800);
/*     */               
/* 156 */               char c = Util.getSeparator();
/* 157 */               Calendar calendar = Calendar.getInstance();
/* 158 */               String str5 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/* 159 */               String str6 = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/*     */               
/* 161 */               String str7 = "" + recordSet.getInt("id") + c + str5 + c + str6 + c + paramHttpServletRequest.getRemoteAddr();
/* 162 */               recordSet.executeProc("CRM_LoginLog_Insert", str7);
/*     */               
/* 164 */               byte b = 2;
/* 165 */               String str8 = "";
/* 166 */               RecordSet recordSet1 = new RecordSet();
/* 167 */               recordSet1.executeProc("SysRemindInfo_InserCrmcontact", "" + recordSet.getInt("id") + b + "1" + b + "0");
/* 168 */               str8 = " select count(*) from CRM_ContactLog where isfinished = 0 and contactdate ='" + str5 + "' and agentid =" + recordSet.getInt("id");
/* 169 */               recordSet1.executeSql(str8);
/* 170 */               if (recordSet1.next() && 
/* 171 */                 Util.getIntValue(recordSet1.getString(1), 0) > 0) {
/* 172 */                 recordSet1.executeProc("SysRemindInfo_InserCrmcontact", "" + recordSet.getInt("id") + b + "1" + b + "1");
/*     */               }
/*     */             } 
/*     */           } 
/*     */           
/* 177 */           if (user != null) {
/* 178 */             hashMap.put("loginid", user.getLoginid());
/* 179 */             hashMap.put("user", JSON.toJSONString(user));
/*     */           } else {
/* 181 */             hashMap.put("loginid", "");
/*     */           } 
/*     */           
/* 184 */           if (!"".equals(str2)) {
/* 185 */             paramHttpServletResponse.sendRedirect(str2);
/*     */             return;
/*     */           } 
/* 188 */           paramHttpServletResponse.getWriter().write("no targeturl!!!!\n");
/* 189 */           paramHttpServletResponse.getWriter().write(JSON.toJSONString(hashMap));
/*     */         } else {
/*     */           
/* 192 */           paramHttpServletResponse.getWriter().write("token is invalid!!!");
/*     */         }
/*     */       
/*     */       } 
/* 196 */     } catch (Exception exception) {
/* 197 */       exception.printStackTrace();
/*     */       try {
/* 199 */         paramHttpServletResponse.getWriter().write("getUser error!!!");
/* 200 */       } catch (IOException iOException) {
/* 201 */         iOException.printStackTrace();
/* 202 */         this.logger.error("getUser error:" + iOException.getMessage());
/*     */       } 
/* 204 */       this.logger.info("================GetUserServlet getUser error:" + exception.getMessage());
/*     */     } 
/* 206 */     this.logger.info("================GetUserServlet getUser end...===============================");
/*     */   }
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException {
/* 211 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getLogintype(HttpServletRequest paramHttpServletRequest) {
/* 221 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("logintype"));
/* 222 */     String str2 = "";
/* 223 */     User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/* 224 */     if (user != null) {
/* 225 */       str2 = Util.null2String(user.getLogintype());
/* 226 */       str1 = str2;
/*     */     } 
/*     */     
/* 229 */     if (str1.equals("")) {
/* 230 */       str1 = "1";
/*     */     }
/*     */     
/* 233 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   private String getisIE(HttpServletRequest paramHttpServletRequest) {
/* 238 */     String str1 = "true";
/* 239 */     String str2 = paramHttpServletRequest.getHeader("User-Agent").toLowerCase();
/* 240 */     if (str2.indexOf("rv:11") == -1 && str2.indexOf("msie") == -1) {
/* 241 */       str1 = "false";
/*     */     }
/* 243 */     if (str2.indexOf("rv:11") > -1 || str2.indexOf("msie") > -1) {
/* 244 */       str1 = "true";
/*     */     }
/* 246 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc517210/GetUserServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */