/*    */ package com.customization.qc517210;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import java.io.IOException;
/*    */ import java.util.Date;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.hrm.User;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.sm.SM4IntegrationUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetTokenServlet
/*    */   extends HttpServlet
/*    */ {
/* 21 */   private Logger logger = LoggerFactory.getLogger(GetTokenServlet.class);
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException {
/* 23 */     User user = (User)paramHttpServletRequest.getSession(false).getAttribute("weaver_user@bean");
/* 24 */     if (user == null) {
/*    */       try {
/* 26 */         paramHttpServletResponse.getWriter().write("no logined user!!!");
/* 27 */       } catch (IOException iOException) {
/* 28 */         iOException.printStackTrace();
/*    */       } 
/*    */       return;
/*    */     } 
/*    */     try {
/* 33 */       String str1 = "qys";
/* 34 */       String str2 = (new Date()).getTime() + "";
/* 35 */       String str3 = JSON.toJSONString(user);
/* 36 */       String str4 = "qys";
/* 37 */       String str5 = str3 + "|" + str4 + "|" + str2;
/* 38 */       String str6 = SM4IntegrationUtil.encrypt(str5);
/* 39 */       RecordSet recordSet = new RecordSet();
/* 40 */       recordSet.execute("select max(id ) maxid from sso_login_oa_qys ");
/* 41 */       recordSet.next();
/* 42 */       String str7 = recordSet.getString("maxid");
/* 43 */       if ("".equals(str7)) {
/* 44 */         str7 = "1";
/*    */       } else {
/* 46 */         str7 = (Integer.valueOf(str7).intValue() + 1) + "";
/*    */       } 
/* 48 */       recordSet.executeUpdate("insert INTO SSO_LOGIN_OA_QYS\n        ( id ,\n          appid ,\n          loginid ,\n          isuse ,\n          ts ,\n          isdestroyed ,\n          token\n        )\nVALUES  ( ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?  \n        )", new Object[] { str7, "qys", user
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */             
/* 64 */             .getLoginid(), "0", str2, "0", str6 });
/* 65 */       paramHttpServletResponse.getWriter().write(str6);
/* 66 */     } catch (Exception exception) {
/* 67 */       exception.printStackTrace();
/* 68 */       this.logger.error("=======GetTokenServlet error occured!!" + exception.getMessage());
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException {
/* 75 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/qc517210/GetTokenServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */