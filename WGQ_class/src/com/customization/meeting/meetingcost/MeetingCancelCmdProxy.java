/*    */ package com.customization.meeting.meetingcost;
/*    */ 
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.meeting.cmd.meetingbase.MeetingCancelCmd;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = MeetingCancelCmd.class, desc = "会议取消接口前执行自定义sql计算费用")
/*    */ public class MeetingCancelCmdProxy
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/* 27 */   private final BaseBean baseBean = new BaseBean();
/*    */   
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 30 */     this.baseBean.writeLog(getClass().getName() + "开始计算费用");
/* 31 */     MeetingCancelCmd meetingCancelCmd = (MeetingCancelCmd)paramCommand;
/* 32 */     Map map = meetingCancelCmd.getParams();
/* 33 */     String str1 = Util.null2String(map.get("meetingid"));
/*    */     
/* 35 */     String str2 = this.baseBean.getPropValue("meetingcost", "ufTable");
/*    */     
/* 37 */     String str3 = this.baseBean.getPropValue("meetingcost", "usedCost");
/* 38 */     String str4 = this.baseBean.getPropValue("meetingcost", "balance");
/* 39 */     String str5 = this.baseBean.getPropValue("meetingcost", "ufSupDep");
/* 40 */     String str6 = this.baseBean.getPropValue("meetingcost", "year");
/*    */     
/* 42 */     String str7 = this.baseBean.getPropValue("meetingcost", "wfTable");
/*    */     
/* 44 */     String str8 = this.baseBean.getPropValue("meetingcost", "thisUsedFee");
/* 45 */     String str9 = this.baseBean.getPropValue("meetingcost", "supDep");
/* 46 */     String str10 = this.baseBean.getPropValue("meetingcost", "budgetYear");
/*    */     
/* 48 */     RecordSet recordSet = new RecordSet();
/* 49 */     recordSet.executeQuery("select requestid from meeting where id = ?", new Object[] { str1 });
/* 50 */     recordSet.next();
/* 51 */     String str11 = Util.null2String(recordSet.getString("requestid"));
/* 52 */     this.baseBean.writeLog("计算会议费用requestId:===>" + str11);
/* 53 */     if (!"".equals(str11)) {
/*    */       
/* 55 */       String str12 = "select " + str8 + "," + str9 + "," + str10 + " from " + str7 + " where requestId = ?";
/* 56 */       this.baseBean.writeLog("计算会议费用selectSql:===>" + str11);
/* 57 */       recordSet.executeQuery(str12, new Object[] { str11 });
/* 58 */       recordSet.next();
/* 59 */       String str13 = Util.null2String(recordSet.getString(str8));
/* 60 */       String str14 = Util.null2String(recordSet.getString(str9));
/* 61 */       String str15 = Util.null2String(recordSet.getString(str10));
/* 62 */       String str16 = "update " + str2 + " set " + str3 + "=" + str3 + "-" + str13 + "," + str4 + "=" + str4 + "+" + str13 + " where " + str5 + "=? and " + str6 + "=?";
/*    */ 
/*    */       
/* 65 */       this.baseBean.writeLog("计算会议费用updateSql:===>" + str11);
/* 66 */       recordSet.executeUpdate(str16, new Object[] { str14, str15 });
/*    */     } 
/* 68 */     return (Map<String, Object>)nextExecute(paramCommand);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/meetingcost/MeetingCancelCmdProxy.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */