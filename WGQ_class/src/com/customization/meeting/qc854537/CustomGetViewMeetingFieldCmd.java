/*    */ package com.customization.meeting.qc854537;
/*    */ 
/*    */ import com.api.meeting.util.MeetingPrmUtil;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.meeting.cmd.meetingField.GetViewMeetingFieldCmd;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = GetViewMeetingFieldCmd.class, desc = "附加在类型保存上的示例代理程序")
/*    */ public class CustomGetViewMeetingFieldCmd
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 24 */     GetViewMeetingFieldCmd getViewMeetingFieldCmd = (GetViewMeetingFieldCmd)paramCommand;
/* 25 */     User user = getViewMeetingFieldCmd.getUser();
/*    */     
/* 27 */     Map map = getViewMeetingFieldCmd.getParams();
/*    */     
/* 29 */     getViewMeetingFieldCmd.setParams(map);
/*    */     
/* 31 */     Map<String, Boolean> map1 = (Map)nextExecute(paramCommand);
/* 32 */     boolean bool = false;
/* 33 */     if (!map1.containsKey("error")) {
/* 34 */       String str = Util.null2String(map.get("meetingid"));
/* 35 */       RecordSet recordSet = new RecordSet();
/* 36 */       recordSet.executeQuery("select * from meeting where id = ?", new Object[] { str });
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 43 */       MeetingPrmUtil meetingPrmUtil = new MeetingPrmUtil(user, str);
/* 44 */       if (recordSet.next() && "2".equals(recordSet.getString("meetingstatus"))) {
/* 45 */         recordSet.execute("SELECT * FROM meeting_videoset");
/* 46 */         String str1 = "";
/* 47 */         while (recordSet.next()) {
/* 48 */           if ("allowvideoMeeting".equals(recordSet.getString("keyname"))) {
/* 49 */             str1 = Util.null2String(recordSet.getString("keyvalue"));
/*    */           }
/*    */         } 
/* 52 */         if ("1".equals(str1)) {
/* 53 */           recordSet.executeQuery("select * from meeting_videolist where videomtstatus in (0,1) and frommould = ? and mouldkey = ? ", new Object[] { "MEETING", str });
/* 54 */           if (recordSet.next()) {
/* 55 */             bool = true;
/*    */           
/*    */           }
/* 58 */           else if (meetingPrmUtil.getIscaller() || meetingPrmUtil.getIscontacter() || meetingPrmUtil.getIscreater()) {
/* 59 */             bool = true;
/*    */           } 
/*    */         } 
/*    */       } 
/*    */     } 
/*    */     
/* 65 */     map1.put("isShowVideoBtn", Boolean.valueOf(bool));
/* 66 */     return (Map)map1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/qc854537/CustomGetViewMeetingFieldCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */