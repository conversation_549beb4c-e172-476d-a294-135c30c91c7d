/*    */ package com.customization.meeting.qc946563;
/*    */ 
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.meeting.cmd.meetingbase.MeetingEditCmd;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = MeetingEditCmd.class, desc = "附加在类型保存上的示例代理程序")
/*    */ public class MeetingEdit
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 27 */     MeetingEditCmd meetingEditCmd = (MeetingEditCmd)paramCommand;
/*    */     
/* 29 */     Map map = meetingEditCmd.getParams();
/* 30 */     User user = meetingEditCmd.getUser();
/*    */ 
/*    */     
/* 33 */     meetingEditCmd.setParams(map);
/*    */ 
/*    */ 
/*    */     
/* 37 */     int i = Util.getIntValue(Util.null2String(map.get("readRemind")), 0);
/* 38 */     String str = Util.null2String(map.get("meetingid"));
/* 39 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 41 */     Map<String, Object> map1 = (Map)nextExecute(paramCommand);
/*    */     
/* 43 */     if (!"".equals(str)) {
/* 44 */       recordSet.executeUpdate("update Meeting set readRemind = ? where id = ? ", new Object[] { Integer.valueOf(i), str });
/*    */     }
/*    */     
/* 47 */     return map1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/qc946563/MeetingEdit.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */