/*     */ package com.customization.meeting.qc946563;
/*     */ 
/*     */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*     */ import com.engine.core.interceptor.AbstractCommandProxy;
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.meeting.cmd.meetingbase.AfterMeetingNormalCmd;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.shwgqzc.SaveModeDate;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.meeting.Maint.MeetingRoomComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @CommandDynamicProxy(target = AfterMeetingNormalCmd.class, desc = "附加在类型保存上的示例代理程序")
/*     */ public class MeetingReadRemind
/*     */   extends AbstractCommandProxy<Map<String, Object>>
/*     */ {
/*     */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/*  31 */     AfterMeetingNormalCmd afterMeetingNormalCmd = (AfterMeetingNormalCmd)paramCommand;
/*     */     
/*  33 */     Map map = afterMeetingNormalCmd.getParams();
/*  34 */     User user = afterMeetingNormalCmd.getUser();
/*     */     
/*  36 */     afterMeetingNormalCmd.setParams(map);
/*     */     
/*  38 */     String str1 = Util.null2String(map.get("meetingid"));
/*  39 */     String str2 = Util.null2String(map.get("flag"));
/*  40 */     RecordSet recordSet1 = new RecordSet();
/*  41 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  43 */     Map<String, Boolean> map1 = (Map)nextExecute(paramCommand);
/*     */     
/*  45 */     if (!"".equals(str1) && "1".equals(str2)) {
/*  46 */       recordSet1.executeQuery("select * from Meeting where id = ? ", new Object[] { str1 });
/*  47 */       if (recordSet1.next()) {
/*  48 */         int i = Util.getIntValue(recordSet1.getString("readRemind"), 0);
/*  49 */         if (1 == i) {
/*  50 */           String str3 = recordSet1.getString("name");
/*  51 */           String str4 = recordSet1.getString("caller");
/*  52 */           String str5 = recordSet1.getString("creater");
/*  53 */           String str6 = recordSet1.getString("createdate");
/*  54 */           String str7 = recordSet1.getString("createtime");
/*  55 */           str7 = str7.substring(0, 5);
/*  56 */           String str8 = recordSet1.getString("hrmmembers");
/*  57 */           String str9 = Util.htmlFilter4UTF8(Util.null2String(recordSet1.getString("desc_n")));
/*  58 */           String str10 = recordSet1.getString("begindate");
/*  59 */           String str11 = recordSet1.getString("begintime");
/*  60 */           String str12 = recordSet1.getString("enddate");
/*  61 */           String str13 = recordSet1.getString("endtime");
/*  62 */           String str14 = recordSet1.getString("address");
/*  63 */           String str15 = recordSet1.getString("customizeAddress");
/*  64 */           String str16 = recordSet1.getString("accessorys");
/*     */           
/*  66 */           MeetingRoomComInfo meetingRoomComInfo = new MeetingRoomComInfo();
/*  67 */           ResourceComInfo resourceComInfo = null;
/*     */           try {
/*  69 */             resourceComInfo = new ResourceComInfo();
/*  70 */           } catch (Exception exception) {
/*  71 */             exception.printStackTrace();
/*     */           } 
/*     */           
/*  74 */           String str17 = "";
/*  75 */           RecordSet recordSet = new RecordSet();
/*  76 */           recordSet.executeQuery("SELECT id from HrmResource where id in(" + str8 + ") order by dsporder asc,workcode asc", new Object[0]);
/*  77 */           while (recordSet.next()) {
/*  78 */             String str = recordSet.getString(1);
/*  79 */             str17 = str17 + ("".equals(str17) ? str : ("," + str));
/*     */           } 
/*  81 */           recordSet1.writeLog("=====参会人员最初的:hrmTemp = " + str17);
/*  82 */           str8 = str17;
/*  83 */           recordSet1.writeLog("=====参会人员排序后的:hrmmembers = " + str8);
/*  84 */           String str18 = "";
/*  85 */           String[] arrayOfString = str8.split(",");
/*  86 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  87 */             str18 = str18 + ("".equals(str18) ? resourceComInfo.getResourcename(arrayOfString[b]) : ("，" + resourceComInfo.getResourcename(arrayOfString[b])));
/*     */           }
/*  89 */           recordSet1.writeLog("=====参会人员:hrmnames = " + str18);
/*  90 */           String str19 = resourceComInfo.getDepartmentID(str5);
/*  91 */           String str20 = "";
/*  92 */           if (!"".equals(str19)) {
/*  93 */             recordSet2.executeQuery("SELECT a.id , (SELECT  id FROM HRMDEPARTMENT WHERE SUPDEPID = 0 START WITH id = a.id CONNECT BY PRIOR supdepid = id) AS zsjbm FROM HRMDEPARTMENT a where id = ? ", new Object[] { str19 });
/*  94 */             if (recordSet2.next()) {
/*  95 */               str20 = recordSet2.getString(2);
/*     */             }
/*     */           } 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 102 */           String str21 = "【会议名称】" + str3 + "<br/>【会议时间】" + str10 + " " + str11 + " 至 " + str12 + " " + str13 + "<br/>【会议地点】" + meetingRoomComInfo.getMeetingRoomInfoname("" + str14) + str15 + "<br/>【主持人】" + resourceComInfo.getResourcename(str4) + "<br/>【参会人员】" + str18 + "<br/>【会议安排】" + str9 + "<br/>";
/*     */ 
/*     */ 
/*     */           
/* 106 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 107 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 108 */           hashMap1.put("cybt", "【会议通知】" + str3);
/* 109 */           hashMap1.put("cynr", str21);
/*     */           
/* 111 */           hashMap1.put("fqr", str5);
/* 112 */           hashMap1.put("fqsj", str6);
/* 113 */           hashMap1.put("ycsj", str7);
/* 114 */           hashMap1.put("cyxx", "4,5");
/* 115 */           hashMap1.put("fjxz", "2");
/* 116 */           hashMap1.put("modedatastatus", "0");
/* 117 */           hashMap1.put("bm", str19);
/* 118 */           hashMap1.put("zsjbm", str20);
/* 119 */           if (!"".equals(str16)) {
/* 120 */             hashMap2.put("fjmc", str16);
/* 121 */             hashMap2.put("fjscsj", str6 + " " + str7);
/* 122 */             hashMap2.put("fjscr", str5);
/*     */           } 
/*     */           
/* 125 */           recordSet1.writeLog("===开始调用建模会议内部传阅接口");
/* 126 */           SaveModeDate saveModeDate = new SaveModeDate();
/* 127 */           boolean bool = saveModeDate.doSave(hashMap1, str8, hashMap2, user);
/* 128 */           recordSet1.writeLog("===调用建模会议内部传阅接口结束，status = " + bool);
/* 129 */           if (bool) {
/* 130 */             recordSet1.writeLog("===会议内部传阅接口调用成功");
/* 131 */             map1.put("status", Boolean.valueOf(true));
/* 132 */             map1.put("msg", "内部传阅接口sucess");
/*     */           } else {
/* 134 */             recordSet1.writeLog("===会议内部传阅接口调用失败");
/* 135 */             map1.put("status", Boolean.valueOf(false));
/* 136 */             map1.put("msg", "内部传阅接口fail");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 143 */     return (Map)map1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/qc946563/MeetingReadRemind.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */