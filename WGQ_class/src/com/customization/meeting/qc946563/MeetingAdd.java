/*    */ package com.customization.meeting.qc946563;
/*    */ 
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.meeting.cmd.meetingbase.NewMeetingCmd;
/*    */ import com.engine.meeting.service.impl.MeetingBaseServiceImpl;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = NewMeetingCmd.class, desc = "附加在类型保存上的示例代理程序")
/*    */ public class MeetingAdd
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 31 */     NewMeetingCmd newMeetingCmd = (NewMeetingCmd)paramCommand;
/*    */     
/* 33 */     Map map = newMeetingCmd.getParams();
/* 34 */     User user = newMeetingCmd.getUser();
/*    */ 
/*    */     
/* 37 */     newMeetingCmd.setParams(map);
/*    */ 
/*    */     
/* 40 */     int i = Util.getIntValue(Util.null2String(map.get("readRemind")), 0);
/* 41 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 43 */     Map<String, Object> map1 = (Map)nextExecute(paramCommand);
/*    */     
/* 45 */     boolean bool = ((Boolean)map1.get("status")).booleanValue();
/* 46 */     if (bool) {
/* 47 */       String str = (String)map1.get("meetingid");
/* 48 */       recordSet.executeUpdate("update Meeting set readRemind = ? where id = ? ", new Object[] { Integer.valueOf(i), str });
/* 49 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 50 */       hashMap.put("meetingid", str);
/* 51 */       hashMap.put("flag", "1");
/* 52 */       MeetingBaseServiceImpl meetingBaseServiceImpl = (MeetingBaseServiceImpl)ServiceUtil.getService(MeetingBaseServiceImpl.class, new User(user.getUID()));
/* 53 */       meetingBaseServiceImpl.afterMeetingNormal(hashMap);
/*    */     } 
/*    */     
/* 56 */     return map1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/qc946563/MeetingAdd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */