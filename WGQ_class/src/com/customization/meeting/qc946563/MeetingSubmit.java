/*    */ package com.customization.meeting.qc946563;
/*    */ 
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.meeting.cmd.meetingbase.MeetingSubmitCmd;
/*    */ import com.engine.meeting.service.impl.MeetingBaseServiceImpl;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = MeetingSubmitCmd.class, desc = "附加在类型保存上的示例代理程序")
/*    */ public class MeetingSubmit
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 25 */     MeetingSubmitCmd meetingSubmitCmd = (MeetingSubmitCmd)paramCommand;
/*    */     
/* 27 */     Map map = meetingSubmitCmd.getParams();
/* 28 */     User user = meetingSubmitCmd.getUser();
/*    */ 
/*    */     
/* 31 */     meetingSubmitCmd.setParams(map);
/*    */ 
/*    */     
/* 34 */     String str = Util.null2String(map.get("meetingid"));
/*    */     
/* 36 */     Map<String, Object> map1 = (Map)nextExecute(paramCommand);
/*    */     
/* 38 */     if (!"".equals(str)) {
/* 39 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 40 */       hashMap.put("meetingid", str);
/* 41 */       hashMap.put("flag", "1");
/* 42 */       MeetingBaseServiceImpl meetingBaseServiceImpl = (MeetingBaseServiceImpl)ServiceUtil.getService(MeetingBaseServiceImpl.class, new User(user.getUID()));
/* 43 */       meetingBaseServiceImpl.afterMeetingNormal(hashMap);
/*    */     } 
/*    */     
/* 46 */     return map1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/qc946563/MeetingSubmit.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */