/*    */ package com.customization.meeting.qc946563;
/*    */ 
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.meeting.cmd.meetingbase.MeetingChangeCmd;
/*    */ import com.engine.meeting.service.impl.MeetingBaseServiceImpl;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = MeetingChangeCmd.class, desc = "附加在类型保存上的示例代理程序")
/*    */ public class MeetingChange
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 31 */     MeetingChangeCmd meetingChangeCmd = (MeetingChangeCmd)paramCommand;
/*    */     
/* 33 */     Map map = meetingChangeCmd.getParams();
/* 34 */     User user = meetingChangeCmd.getUser();
/*    */ 
/*    */     
/* 37 */     meetingChangeCmd.setParams(map);
/*    */ 
/*    */ 
/*    */     
/* 41 */     int i = Util.getIntValue(Util.null2String(map.get("readRemind")), 0);
/* 42 */     String str = Util.null2String(map.get("meetingid"));
/* 43 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 45 */     Map<String, Object> map1 = (Map)nextExecute(paramCommand);
/*    */     
/* 47 */     boolean bool = ((Boolean)map1.get("status")).booleanValue();
/* 48 */     if (bool && 
/* 49 */       !"".equals(str)) {
/* 50 */       recordSet.executeUpdate("update Meeting set readRemind = ? where id = ? ", new Object[] { Integer.valueOf(i), str });
/* 51 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 52 */       hashMap.put("meetingid", str);
/* 53 */       hashMap.put("flag", "1");
/* 54 */       MeetingBaseServiceImpl meetingBaseServiceImpl = (MeetingBaseServiceImpl)ServiceUtil.getService(MeetingBaseServiceImpl.class, new User(user.getUID()));
/* 55 */       meetingBaseServiceImpl.afterMeetingNormal(hashMap);
/*    */     } 
/*    */ 
/*    */     
/* 59 */     return map1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/qc946563/MeetingChange.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */