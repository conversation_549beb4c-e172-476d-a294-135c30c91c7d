/*     */ package com.customization.meeting.qc946563;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*     */ import com.engine.core.interceptor.AbstractCommandProxy;
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.engine.integration.util.FieldUtil;
/*     */ import com.engine.meeting.cmd.meetingField.GetMeetingBaseFieldMobxCmd;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.meeting.defined.MeetingFieldComInfo;
/*     */ import weaver.meeting.defined.MeetingFieldManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @CommandDynamicProxy(target = GetMeetingBaseFieldMobxCmd.class, desc = "附加在类型保存上的示例代理程序")
/*     */ public class GetMeetingField
/*     */   extends AbstractCommandProxy<Map<String, Object>>
/*     */ {
/*     */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/*  33 */     GetMeetingBaseFieldMobxCmd getMeetingBaseFieldMobxCmd = (GetMeetingBaseFieldMobxCmd)paramCommand;
/*     */     
/*  35 */     Map map1 = getMeetingBaseFieldMobxCmd.getParams();
/*  36 */     User user = getMeetingBaseFieldMobxCmd.getUser();
/*  37 */     byte b1 = 2;
/*  38 */     String str1 = Util.null2String(map1.get("meetingid"));
/*  39 */     boolean bool = false;
/*  40 */     if (map1.get("isChange") != null) {
/*  41 */       bool = (Util.getIntValue(map1.get("isChange").toString(), 0) == 1) ? true : false;
/*     */     }
/*  43 */     boolean bool1 = true;
/*  44 */     if (map1.get("isEdit") != null) {
/*  45 */       bool1 = Boolean.parseBoolean(map1.get("isEdit").toString());
/*     */     }
/*     */     
/*  48 */     int i = 0;
/*  49 */     RecordSet recordSet = new RecordSet();
/*  50 */     if (!"".equals(str1)) {
/*  51 */       recordSet.executeQuery("SELECT readRemind FROM Meeting WHERE id = ?", new Object[] { str1 });
/*  52 */       if (recordSet.next()) {
/*  53 */         i = Util.getIntValue(recordSet.getString("readRemind"), 0);
/*     */       }
/*     */     } 
/*  56 */     if (bool || !bool1) {
/*  57 */       b1 = 1;
/*     */     }
/*     */ 
/*     */     
/*  61 */     String str2 = recordSet.getPropValue("qc946563", "remindType");
/*     */     
/*  63 */     MeetingFieldComInfo meetingFieldComInfo = new MeetingFieldComInfo();
/*  64 */     MeetingFieldManager meetingFieldManager = null;
/*     */     try {
/*  66 */       meetingFieldManager = new MeetingFieldManager(1);
/*  67 */     } catch (Exception exception) {
/*  68 */       exception.printStackTrace();
/*     */     } 
/*  70 */     List list = meetingFieldManager.getLsGroup();
/*  71 */     Object object = null;
/*     */     
/*  73 */     Map<String, Object> map = (Map)nextExecute(paramCommand);
/*  74 */     byte b2 = 0;
/*  75 */     byte b3 = 0;
/*  76 */     List<Map> list1 = (List)map.get("datas");
/*     */     
/*  78 */     for (byte b4 = 0; b4 < list1.size(); b4++) {
/*  79 */       Map map3 = list1.get(b4);
/*  80 */       List<Map> list3 = (List)map3.get("items");
/*  81 */       for (byte b = 0; b < list3.size(); b++) {
/*  82 */         Map<String, String> map4 = list3.get(b);
/*  83 */         String str = "";
/*  84 */         if (map4.get("domkey") instanceof String) {
/*  85 */           str = (String)map4.get("domkey");
/*  86 */         } else if (map4.get("domkey") instanceof List) {
/*  87 */           List<String> list4 = (List)map4.get("domkey");
/*  88 */           str = list4.get(0);
/*     */         } 
/*  90 */         if ("".equals(str1)) {
/*  91 */           if ("begintime".equals(str)) {
/*  92 */             map4.put("value", "08:00");
/*  93 */           } else if ("endtime".equals(str)) {
/*  94 */             map4.put("value", "17:00");
/*     */           } 
/*     */         }
/*     */         
/*  98 */         if ("remindTypeNew".equals(str)) {
/*  99 */           map4.put("value", str2);
/* 100 */           if (map4.containsKey("conditionType") && map4.get("conditionType").equals("BROWSER")) {
/* 101 */             BrowserBean browserBean = (BrowserBean)map4.get("browserConditionParam");
/* 102 */             String str3 = map4.get("browserType");
/* 103 */             browserBean.setReplaceDatas(FieldUtil.getReplaceDatas(null, str3, str2));
/*     */           } 
/* 105 */           b2 = b;
/* 106 */           b3 = b4;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 112 */     Map map2 = list1.get(b3);
/* 113 */     List<Map> list2 = (List)map2.get("items");
/*     */ 
/*     */     
/* 116 */     int j = Util.getIntValue(recordSet.getPropValue("qc946563", "readRemind"), 0);
/*     */     
/* 118 */     list2.add(b2, FieldUtil.getFormItemForCheckbox("readRemind", SystemEnv.getHtmlLabelName(j, user.getLanguage()), String.valueOf(i), b1));
/*     */     
/* 120 */     return map;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/meeting/qc946563/GetMeetingField.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */