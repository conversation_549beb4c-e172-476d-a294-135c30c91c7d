/*    */ package com.customization.esb.crm;
/*    */ 
/*    */ import com.api.crm.util.CrmConstant;
/*    */ import com.customization.esb.EsbProxyCmd;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class EsbProxyCrmCmd
/*    */   extends EsbProxyCmd
/*    */ {
/*    */   public Map<String, Object> getErrorMsg(String paramString) {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     hashMap.put(CrmConstant.CRM_RESULT_STATUS, "failed");
/* 31 */     hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE, "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/*    */     
/* 33 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/crm/EsbProxyCrmCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */