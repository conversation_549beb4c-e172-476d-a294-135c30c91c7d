/*    */ package com.customization.esb.crm.chance;
/*    */ 
/*    */ import com.customization.esb.crm.EsbProxyCrmCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.crm.cmd.chance.FormUpdateCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = FormUpdateCmd.class, desc = "编辑商机代理")
/*    */ public class ProxyEditChanceCmd
/*    */   extends EsbProxyCrmCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "crm_chance_editchance_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "crm_chance_editchance_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/crm/chance/ProxyEditChanceCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */