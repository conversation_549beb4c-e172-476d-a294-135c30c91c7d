/*    */ package com.customization.esb.crm.chance;
/*    */ 
/*    */ import com.customization.esb.crm.EsbProxyCrmCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.crm.cmd.chance.DeleteCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = DeleteCmd.class, desc = "删除商机代理")
/*    */ public class ProxyDeleteChanceCmd
/*    */   extends EsbProxyCrmCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "crm_chance_deletechance_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "crm_chance_deletechance_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/crm/chance/ProxyDeleteChanceCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */