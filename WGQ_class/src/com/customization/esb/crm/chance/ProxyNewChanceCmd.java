/*    */ package com.customization.esb.crm.chance;
/*    */ 
/*    */ import com.customization.esb.crm.EsbProxyCrmCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.crm.cmd.chance.FormSaveCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = FormSaveCmd.class, desc = "新建商机代理")
/*    */ public class ProxyNewChanceCmd
/*    */   extends EsbProxyCrmCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "crm_chance_newchance_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "crm_chance_newchance_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/crm/chance/ProxyNewChanceCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */