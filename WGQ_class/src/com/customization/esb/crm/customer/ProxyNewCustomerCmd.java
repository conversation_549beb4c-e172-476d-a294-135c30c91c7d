/*    */ package com.customization.esb.crm.customer;
/*    */ 
/*    */ import com.customization.esb.crm.EsbProxyCrmCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.crm.cmd.customer.CustomerSaveCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = CustomerSaveCmd.class, desc = "新建客户代理")
/*    */ public class ProxyNewCustomerCmd
/*    */   extends EsbProxyCrmCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 28 */     return "crm_customer_newcustomer_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 37 */     return "crm_customer_newcustomer_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/crm/customer/ProxyNewCustomerCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */