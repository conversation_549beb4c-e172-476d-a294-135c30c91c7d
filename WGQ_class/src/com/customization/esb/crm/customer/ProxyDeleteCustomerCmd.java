/*    */ package com.customization.esb.crm.customer;
/*    */ 
/*    */ import com.customization.esb.crm.EsbProxyCrmCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.crm.cmd.customer.CrmDeleteCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = CrmDeleteCmd.class, desc = "删除客户代理")
/*    */ public class ProxyDeleteCustomerCmd
/*    */   extends EsbProxyCrmCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "crm_customer_deletecustomer_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "crm_customer_deletecustomer_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/crm/customer/ProxyDeleteCustomerCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */