/*    */ package com.customization.esb.meeting.meetingbase;
/*    */ 
/*    */ import com.customization.esb.EsbProxyCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.meeting.cmd.meetingbase.MeetingCancelCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = MeetingCancelCmd.class, desc = "取消会议代理")
/*    */ public class ProxyCancelMeetingCmd
/*    */   extends EsbProxyCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "meeting_meetingbase_cancelmeeting_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "meeting_meetingbase_cancelmeeting_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/meeting/meetingbase/ProxyCancelMeetingCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */