/*    */ package com.customization.esb.meeting.meetingbase;
/*    */ 
/*    */ import com.customization.esb.EsbProxyCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.meeting.cmd.meetingbase.NewMeetingCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = NewMeetingCmd.class, desc = "创建会议代理")
/*    */ public class ProxyNewMeetingCmd
/*    */   extends EsbProxyCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "meeting_meetingbase_newmeeting_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "meeting_meetingbase_newmeeting_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/meeting/meetingbase/ProxyNewMeetingCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */