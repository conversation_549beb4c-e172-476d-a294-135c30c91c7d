/*    */ package com.customization.esb.proj.project;
/*    */ 
/*    */ import com.api.prj.cmd.project.DoPrjTempletStageSaveCmd;
/*    */ import com.customization.esb.proj.EsbProxyProjCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = DoPrjTempletStageSaveCmd.class, desc = "保存阶段代理")
/*    */ public class ProxySavePrjTempletStageCmd
/*    */   extends EsbProxyProjCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 28 */     return "prj_project_saveprjtempletstage_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 37 */     return "prj_project_saveprjtempletstage_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/proj/project/ProxySavePrjTempletStageCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */