/*    */ package com.customization.esb.proj.project;
/*    */ 
/*    */ import com.api.prj.cmd.project.DoProjectOperationCmd;
/*    */ import com.customization.esb.proj.EsbProxyProjCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = DoProjectOperationCmd.class, desc = "新增、编辑、删除项目代理")
/*    */ public class ProxyDoProjectOperationCmd
/*    */   extends EsbProxyProjCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "prj_project_doprojectoperation_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "prj_project_doprojectoperation_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/proj/project/ProxyDoProjectOperationCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */