/*    */ package com.customization.esb.proj;
/*    */ 
/*    */ import com.customization.esb.EsbProxyCmd;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class EsbProxyProjCmd
/*    */   extends EsbProxyCmd
/*    */ {
/*    */   public Map<String, Object> getErrorMsg(String paramString) {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */     hashMap.put("success", Boolean.valueOf(false));
/* 29 */     hashMap.put("msg", "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/*    */     
/* 31 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/proj/EsbProxyProjCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */