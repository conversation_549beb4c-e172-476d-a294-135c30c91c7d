/*    */ package com.customization.esb.proj.task;
/*    */ 
/*    */ import com.api.prj.cmd.task.DoTaskOperationCmd;
/*    */ import com.customization.esb.proj.EsbProxyProjCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = DoTaskOperationCmd.class, desc = "新增、编辑、删除任务代理")
/*    */ public class ProxyDoTaskOperationCmd
/*    */   extends EsbProxyProjCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "prj_task_dotaskoperation_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "prj_task_dotaskoperation_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/proj/task/ProxyDoTaskOperationCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */