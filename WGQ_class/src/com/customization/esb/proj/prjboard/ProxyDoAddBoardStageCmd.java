/*    */ package com.customization.esb.proj.prjboard;
/*    */ 
/*    */ import com.api.prj.cmd.prjBoard.DoAddBoardStageCmd;
/*    */ import com.customization.esb.proj.EsbProxyProjCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = DoAddBoardStageCmd.class, desc = "新增阶段代理")
/*    */ public class ProxyDoAddBoardStageCmd
/*    */   extends EsbProxyProjCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "prj_prjboard_newboardstage_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "prj_prjboard_newboardstage_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/proj/prjboard/ProxyDoAddBoardStageCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */