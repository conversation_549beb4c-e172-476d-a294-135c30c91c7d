/*    */ package com.customization.esb.proj.prjboard;
/*    */ 
/*    */ import com.api.prj.cmd.prjBoard.DoDelBoardStageCmd;
/*    */ import com.customization.esb.proj.EsbProxyProjCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = DoDelBoardStageCmd.class, desc = "删除阶段代理")
/*    */ public class ProxyDoDelBoardStageCmd
/*    */   extends EsbProxyProjCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 27 */     return "prj_prjboard_deleteboardstage_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 36 */     return "prj_prjboard_deleteboardstage_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/proj/prjboard/ProxyDoDelBoardStageCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */