/*    */ package com.customization.esb.util;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.alibaba.fastjson.serializer.SerializerFeature;
/*    */ import com.weaver.esb.client.EsbClient;
/*    */ import com.weaver.esb.spi.EsbService;
/*    */ import com.weaver.file.Prop;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.Arrays;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.lang3.StringUtils;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EsbExecute
/*    */ {
/* 28 */   private Logger log = LoggerFactory.getLogger(EsbExecute.class);
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 33 */   private static final String[] ingoreCodes = new String[] { "-1", "100", "101", "102", "201", "202", "203", "204", "205", "301", "302" };
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONObject run(Map<String, Object> paramMap, String paramString) {
/* 42 */     EsbService esbService = EsbClient.getService();
/* 43 */     Prop.getInstance(); String str = Prop.getPropValue("esbProxy", paramString);
/*    */     
/* 45 */     if (esbService != null && StringUtils.isNotBlank(str)) {
/*    */       
/* 47 */       String str1 = esbService.execute(str, JSONObject.toJSONString(paramMap, new SerializerFeature[] { SerializerFeature.IgnoreNonFieldGetter }));
/* 48 */       this.log.info("execute event code : " + str);
/* 49 */       this.log.info("execute esb event return result: " + str1);
/*    */       
/* 51 */       return JSONObject.parseObject(str1);
/*    */     } 
/*    */     
/* 54 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isNextRun(String paramString) {
/* 63 */     if (StringUtils.isNotBlank(paramString)) {
/* 64 */       return Arrays.<String>asList(ingoreCodes).contains(paramString);
/*    */     }
/* 66 */     return Boolean.TRUE.booleanValue();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getErrorMsg(String paramString) {
/* 75 */     paramString = Util.null2String(paramString);
/* 76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 77 */     hashMap.put("error", "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/* 78 */     hashMap.put("showMsg", "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/* 79 */     hashMap.put("status", Boolean.valueOf(false));
/* 80 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/util/EsbExecute.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */