/*     */ package com.customization.esb;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.integration.util.JavaUtil;
/*     */ import com.customization.esb.util.EsbExecute;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.core.interceptor.AbstractCommandProxy;
/*     */ import com.engine.core.interceptor.Command;
/*     */ import com.weaver.general.Util;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class EsbProxyCmd
/*     */   extends AbstractCommandProxy<Map<String, Object>>
/*     */ {
/*  39 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public User user;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getData(Map<String, Object> paramMap) {
/*  61 */     HashMap<String, Object> hashMap = new HashMap<>(paramMap);
/*     */     
/*  63 */     if (!hashMap.containsKey("CreatorUser")) {
/*  64 */       Map<String, Object> map1 = getUserMap(this.user);
/*  65 */       hashMap.put("CreatorUser", map1);
/*     */     } 
/*     */     
/*  68 */     Map<String, Object> map = getCustomerData(hashMap);
/*  69 */     if (map != null && map.size() > 0) {
/*  70 */       hashMap.putAll(map);
/*     */     }
/*     */     
/*  73 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getUserMap(User paramUser) {
/*  82 */     if (paramUser != null) {
/*  83 */       JSONObject jSONObject = JSONObject.parseObject(JSON.toJSONString(paramUser));
/*     */       
/*  85 */       jSONObject.remove("pwd");
/*  86 */       return (Map<String, Object>)jSONObject;
/*     */     } 
/*  88 */     return new HashMap<>();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCustomerData(Map<String, Object> paramMap) {
/*  97 */     return new HashMap<>();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isExecuteSuccess(Map<String, Object> paramMap) {
/* 106 */     if (paramMap != null) {
/* 107 */       String str = Util.null2String(paramMap.get("status"));
/*     */       
/* 109 */       if (StringUtils.isBlank(str)) {
/* 110 */         str = Util.null2String(paramMap.get("success"));
/*     */       }
/*     */       
/* 113 */       if (StringUtils.equalsIgnoreCase("true", str) || 
/* 114 */         StringUtils.equalsIgnoreCase("1", str) || 
/* 115 */         StringUtils.equalsIgnoreCase("success", str)) {
/* 116 */         return Boolean.TRUE.booleanValue();
/*     */       }
/*     */     } 
/* 119 */     return Boolean.FALSE.booleanValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getErrorMsg(String paramString) {
/* 128 */     paramString = Util.null2String(paramString);
/* 129 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 130 */     hashMap.put("error", "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/* 131 */     hashMap.put("showMsg", "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/* 132 */     hashMap.put("status", Boolean.valueOf(false));
/*     */     
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(Command<Map<String, Object>> paramCommand) {
/* 141 */     if (paramCommand instanceof AbstractCommonCommand) {
/* 142 */       Map<String, Object> map3; AbstractCommonCommand abstractCommonCommand = (AbstractCommonCommand)paramCommand;
/*     */       
/* 144 */       this.user = abstractCommonCommand.getUser();
/*     */       
/* 146 */       Map<String, Object> map1 = abstractCommonCommand.getParams();
/*     */       
/* 148 */       EsbExecute esbExecute = new EsbExecute();
/* 149 */       String str1 = null;
/* 150 */       String str2 = null;
/*     */       
/* 152 */       Map<String, Object> map2 = getData(map1);
/*     */ 
/*     */       
/* 155 */       String str3 = getBeforeEventKey();
/* 156 */       if (!StringUtils.isBlank(str3)) {
/*     */         
/*     */         try {
/* 159 */           JSONObject jSONObject = esbExecute.run(map2, str3);
/*     */           
/* 161 */           if (jSONObject == null) {
/* 162 */             jSONObject = new JSONObject();
/*     */           }
/*     */           
/* 165 */           str1 = Util.null2String(jSONObject.getString("code"));
/* 166 */           if ("100".equals(str1)) {
/* 167 */             JSONObject jSONObject1 = jSONObject.getJSONObject("data");
/*     */             
/* 169 */             if (jSONObject1 != null && !jSONObject1.isEmpty()) {
/*     */ 
/*     */               
/* 172 */               map1.putAll((Map<? extends String, ?>)jSONObject1);
/*     */               
/* 174 */               abstractCommonCommand.setParams(map1);
/*     */             } 
/*     */           } else {
/* 177 */             str2 = jSONObject.getString("msg");
/*     */           } 
/* 179 */         } catch (Exception exception) {
/* 180 */           this.log.error(JavaUtil.getExceptionDetail(exception));
/* 181 */           str1 = null;
/* 182 */           str2 = "" + SystemEnv.getHtmlLabelName(383881, ThreadVarLanguage.getLang()) + "";
/*     */         } 
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 188 */       if (esbExecute.isNextRun(str1)) {
/* 189 */         map3 = (Map)nextExecute((Command)abstractCommonCommand);
/*     */         
/* 191 */         String str = getAfterEventKey();
/* 192 */         if (!StringUtils.isBlank(str)) {
/*     */           
/*     */           try {
/* 195 */             if (isExecuteSuccess(map3)) {
/*     */               
/* 197 */               map2.putAll(map3);
/* 198 */               esbExecute.run(map2, str);
/*     */             } 
/* 200 */           } catch (Exception exception) {
/* 201 */             this.log.error(JavaUtil.getExceptionDetail(exception));
/*     */           } 
/*     */         }
/*     */       } else {
/*     */         
/* 206 */         map3 = getErrorMsg(str2);
/*     */       } 
/* 208 */       return map3;
/*     */     } 
/*     */     
/* 211 */     return null;
/*     */   }
/*     */   
/*     */   public abstract String getBeforeEventKey();
/*     */   
/*     */   public abstract String getAfterEventKey();
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/EsbProxyCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */