/*    */ package com.customization.esb.workplan.workplanbase;
/*    */ 
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.workplan.cmd.workplanBase.AddAndEditWorkPlanCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = AddAndEditWorkPlanCmd.class, desc = "新增或编辑日程代理")
/*    */ public class ProxyAddAndEditWorkPlanCmd
/*    */   extends EsbProxyWorkPlanCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 26 */     return "workplan_workplanbase_addandeditworkplan_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 35 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/workplan/workplanbase/ProxyAddAndEditWorkPlanCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */