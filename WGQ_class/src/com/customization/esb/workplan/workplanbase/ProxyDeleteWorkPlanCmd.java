/*    */ package com.customization.esb.workplan.workplanbase;
/*    */ 
/*    */ import com.customization.esb.EsbProxyCmd;
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.workplan.cmd.workplanBase.BeforeWorkplanDelCmd;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = BeforeWorkplanDelCmd.class, desc = "删除日程代理")
/*    */ public class ProxyDeleteWorkPlanCmd
/*    */   extends EsbProxyCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 30 */     return "workplan_workplanbase_deleteworkplan_before";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 39 */     return "workplan_workplanbase_deleteworkplan_after";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getErrorMsg(String paramString) {
/* 50 */     paramString = Util.null2String(paramString);
/* 51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 52 */     hashMap.put("error", "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/* 53 */     hashMap.put("status", Boolean.valueOf(false));
/*    */     
/* 55 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/workplan/workplanbase/ProxyDeleteWorkPlanCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */