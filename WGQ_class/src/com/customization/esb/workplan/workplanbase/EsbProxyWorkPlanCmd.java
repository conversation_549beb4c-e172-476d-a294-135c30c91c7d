/*    */ package com.customization.esb.workplan.workplanbase;
/*    */ 
/*    */ import com.customization.esb.EsbProxyCmd;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class EsbProxyWorkPlanCmd
/*    */   extends EsbProxyCmd
/*    */ {
/*    */   public Map<String, Object> getCustomerData(Map<String, Object> paramMap) {
/* 25 */     String str1 = Util.null2String(paramMap.get("beginDate"));
/* 26 */     String str2 = Util.null2String(paramMap.get("beginTime"));
/* 27 */     String str3 = Util.null2String(paramMap.get("endDate")).trim();
/* 28 */     String str4 = Util.null2String(paramMap.get("endTime"));
/*    */     
/* 30 */     String str5 = Util.null2String(paramMap.get("beginDateTime")).trim();
/* 31 */     String str6 = Util.null2String(paramMap.get("endDateTime")).trim();
/*    */ 
/*    */     
/* 34 */     if ("".equals(str1)) {
/* 35 */       if (str5.length() >= 10) {
/* 36 */         str1 = str5.substring(0, 10);
/*    */       }
/* 38 */       if (str5.length() >= 16) {
/* 39 */         str2 = str5.substring(11, 16);
/*    */       }
/*    */     } else {
/* 42 */       str5 = str1 + " " + str2;
/*    */     } 
/*    */ 
/*    */     
/* 46 */     if ("".equals(str3)) {
/* 47 */       if (str6.length() >= 10) {
/* 48 */         str3 = str6.substring(0, 10);
/*    */       }
/* 50 */       if (str6.length() >= 16) {
/* 51 */         str4 = str6.substring(11, 16);
/*    */       }
/*    */     } else {
/* 54 */       str6 = str3 + " " + str4;
/*    */     } 
/*    */     
/* 57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 58 */     hashMap.put("beginDate", str1);
/* 59 */     hashMap.put("beginTime", str2);
/* 60 */     hashMap.put("endDate", str3);
/* 61 */     hashMap.put("endTime", str4);
/* 62 */     hashMap.put("beginDateTime", str5);
/* 63 */     hashMap.put("endDateTime", str6);
/*    */     
/* 65 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getErrorMsg(String paramString) {
/* 78 */     paramString = Util.null2String(paramString);
/* 79 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 80 */     hashMap.put("error", "ESB" + SystemEnv.getHtmlLabelName(10004822, ThreadVarLanguage.getLang()) + "" + paramString);
/* 81 */     hashMap.put("status", Boolean.valueOf(false));
/*    */     
/* 83 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/workplan/workplanbase/EsbProxyWorkPlanCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */