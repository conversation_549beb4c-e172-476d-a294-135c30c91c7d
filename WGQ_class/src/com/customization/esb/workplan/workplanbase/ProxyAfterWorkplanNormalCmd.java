/*    */ package com.customization.esb.workplan.workplanbase;
/*    */ 
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.workplan.cmd.workplanBase.AfterWorkplanNormalCmd;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = AfterWorkplanNormalCmd.class, desc = "新增或编辑日程代理")
/*    */ public class ProxyAfterWorkplanNormalCmd
/*    */   extends EsbProxyWorkPlanCmd
/*    */ {
/*    */   public String getBeforeEventKey() {
/* 21 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAfterEventKey() {
/* 30 */     return "workplan_workplanbase_addandeditworkplan_after";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/esb/workplan/workplanbase/ProxyAfterWorkplanNormalCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */