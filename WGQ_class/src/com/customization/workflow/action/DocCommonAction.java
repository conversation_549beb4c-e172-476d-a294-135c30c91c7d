/*    */ package com.customization.workflow.action;
/*    */ 
/*    */ import java.lang.reflect.InvocationTargetException;
/*    */ import java.lang.reflect.Method;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DocCommonAction
/*    */   implements Action
/*    */ {
/*    */   private String className;
/*    */   
/*    */   public String getClassName() {
/* 22 */     return this.className;
/*    */   }
/*    */   
/*    */   public void setClassName(String paramString) {
/* 26 */     this.className = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 31 */     BaseBean baseBean = new BaseBean();
/* 32 */     if ("".equals(getClassName()) || null == getClassName()) {
/* 33 */       throw new RuntimeException("classname  is empty");
/*    */     }
/*    */     
/* 36 */     baseBean.writeLog("requestid" + paramRequestInfo.getRequestid() + "---" + getClassName());
/*    */ 
/*    */     
/* 39 */     String[] arrayOfString = getClassName().split(",");
/* 40 */     for (String str : arrayOfString) {
/*    */       try {
/* 42 */         Class<?> clazz = Class.forName(str);
/* 43 */         Class[] arrayOfClass = clazz.getInterfaces();
/* 44 */         for (Class clazz1 : arrayOfClass) {
/* 45 */           if ("weaver.interfaces.workflow.action.Action".equals(clazz1.getName())) {
/* 46 */             Object object1 = clazz.newInstance();
/* 47 */             Method method = clazz.getMethod("execute", new Class[] { RequestInfo.class });
/* 48 */             baseBean.writeLog("requestid" + paramRequestInfo.getRequestid() + "--开始执行-" + str);
/* 49 */             Object object2 = method.invoke(object1, new Object[] { paramRequestInfo });
/* 50 */             if (!"1".equals(Util.null2String(object2))) {
/* 51 */               return Util.null2String(object2);
/*    */             }
/*    */             break;
/*    */           } 
/*    */         } 
/* 56 */       } catch (ClassNotFoundException classNotFoundException) {
/* 57 */         classNotFoundException.printStackTrace();
/* 58 */       } catch (IllegalAccessException illegalAccessException) {
/* 59 */         illegalAccessException.printStackTrace();
/* 60 */       } catch (InstantiationException instantiationException) {
/* 61 */         instantiationException.printStackTrace();
/* 62 */       } catch (NoSuchMethodException noSuchMethodException) {
/* 63 */         noSuchMethodException.printStackTrace();
/* 64 */       } catch (InvocationTargetException invocationTargetException) {
/* 65 */         invocationTargetException.printStackTrace();
/*    */       } 
/*    */     } 
/*    */ 
/*    */     
/* 70 */     return "1";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/workflow/action/DocCommonAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */