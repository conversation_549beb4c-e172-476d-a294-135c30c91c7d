/*    */ package com.customization.mobilecenter;
/*    */ 
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.workflow.cmd.mobileCenter.GetListResultCmd;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.filter.WeaverRequest;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = GetListResultCmd.class, desc = "流程中心待办事宜去掉抄送数据")
/*    */ public class CustomGetListResultCmd
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command targetCommand) {
/* 19 */     GetListResultCmd resultCmd = (GetListResultCmd)targetCommand;
/* 20 */     HttpServletRequest request = resultCmd.getRequest();
/*    */     try {
/* 22 */       String viewScope = request.getParameter("viewScope");
/* 23 */       String ismobile = request.getParameter("ismobile");
/* 24 */       int menuid = Util.getIntValue(request.getParameter("menuid"), -1);
/* 25 */       if (menuid == -1 && "1".equals(ismobile) && ("doing".equals(viewScope) || "done".equals(viewScope))) {
/* 26 */         WeaverRequest weaverRequest = new WeaverRequest(request);
/* 27 */         weaverRequest.setParameter("sqlwhere_os", " and sysid <> '-2' ");
/* 28 */         resultCmd.setRequest((HttpServletRequest)weaverRequest);
/*    */       } 
/* 30 */     } catch (Exception ex) {
/* 31 */       (new BaseBean()).writeLog(getClass().getName() + "--- 添加 待办去掉 sysid 为-2的异构系统流程失败");
/*    */     } 
/* 33 */     Map<String, Object> result = (Map<String, Object>)nextExecute(targetCommand);
/*    */     
/* 35 */     return result;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/mobilecenter/CustomGetListResultCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */