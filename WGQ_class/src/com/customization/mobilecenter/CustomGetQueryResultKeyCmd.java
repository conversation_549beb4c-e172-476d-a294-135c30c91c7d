/*    */ package com.customization.mobilecenter;
/*    */ 
/*    */ import com.engine.core.cfg.annotation.CommandDynamicProxy;
/*    */ import com.engine.core.interceptor.AbstractCommandProxy;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import com.engine.workflow.cmd.customQuery.GetQueryResultKeyCmd;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.filter.WeaverRequest;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ @CommandDynamicProxy(target = GetQueryResultKeyCmd.class, desc = "流程中心待办事宜去掉抄送数据")
/*    */ public class CustomGetQueryResultKeyCmd
/*    */   extends AbstractCommandProxy<Map<String, Object>>
/*    */ {
/*    */   public Map<String, Object> execute(Command<Map<String, Object>> targetCommand) {
/* 19 */     GetQueryResultKeyCmd getQueryResultKeyCmd = (GetQueryResultKeyCmd)targetCommand;
/* 20 */     HttpServletRequest request = getQueryResultKeyCmd.getRequest();
/*    */     try {
/* 22 */       String viewScope = request.getParameter("viewScope");
/* 23 */       String ismobile = request.getParameter("ismobile");
/* 24 */       int menuid = Util.getIntValue(request.getParameter("menuid"), -1);
/* 25 */       if ("all".equals(viewScope) && menuid == -1 && "1".equals(ismobile)) {
/* 26 */         WeaverRequest weaverRequest = new WeaverRequest(request);
/* 27 */         weaverRequest.setParameter("customid", "-1");
/* 28 */         getQueryResultKeyCmd.setRequest((HttpServletRequest)weaverRequest);
/*    */       } 
/* 30 */     } catch (Exception ex) {
/* 31 */       (new BaseBean()).writeLog(getClass().getName() + "--- 添加 去掉异构系统数据失败");
/*    */     } 
/*    */     
/* 34 */     Map<String, Object> result = (Map<String, Object>)nextExecute(targetCommand);
/* 35 */     return result;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/mobilecenter/CustomGetQueryResultKeyCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */