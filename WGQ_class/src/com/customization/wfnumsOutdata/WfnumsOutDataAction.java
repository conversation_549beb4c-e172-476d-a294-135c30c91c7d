/*     */ package com.customization.wfnumsOutdata;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.IOException;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import org.apache.commons.httpclient.HttpClient;
/*     */ import org.apache.commons.httpclient.HttpConnectionManager;
/*     */ import org.apache.commons.httpclient.HttpMethod;
/*     */ import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
/*     */ import org.apache.commons.httpclient.contrib.ssl.EasySSLProtocolSocketFactory;
/*     */ import org.apache.commons.httpclient.methods.GetMethod;
/*     */ import org.apache.commons.httpclient.protocol.Protocol;
/*     */ import org.apache.commons.httpclient.protocol.ProtocolSocketFactory;
/*     */ import org.apache.http.impl.client.DefaultHttpClient;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.ln.HttpClientUtil;
/*     */ import weaver.portal.logging.PortalLogger;
/*     */ import weaver.portal.logging.PortalLoggerFactory;
/*     */ import weaver.wechat.request.HttpManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WfnumsOutDataAction
/*     */   extends BaseBean
/*     */ {
/*  45 */   PortalLogger logger = PortalLoggerFactory.getLogger(WfnumsOutDataAction.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getData")
/*     */   @Produces({"text/plain"})
/*     */   public String getData(@Context HttpServletRequest request, @Context HttpServletResponse response) throws IOException {
/*  57 */     HashMap<String, Object> result = new HashMap<>();
/*  58 */     String tabid = Util.null2String(request.getParameter("tabid"));
/*  59 */     String pageSize = Util.null2String(request.getParameter("pageSize"));
/*  60 */     String dataJson = "";
/*  61 */     User user = HrmUserVarify.getUser(request, response);
/*     */ 
/*     */     
/*  64 */     RecordSet rs = new RecordSet();
/*  65 */     String workcode = "";
/*  66 */     rs.executeQuery("SELECT workcode FROM HrmResource where  id = ?", new Object[] { Integer.valueOf(user.getUID()) });
/*  67 */     if (rs.next()) {
/*  68 */       workcode = rs.getString("workcode");
/*     */     } else {
/*  70 */       result.put("msg", "该人员工号为空   人员id-->" + user.getUID());
/*  71 */       return JSONObject.toJSONString(result);
/*     */     } 
/*     */ 
/*     */     
/*  75 */     String html0 = "http://************:8088/services/rs/session/ba979ee998df25bc372b0ca9e9ac9ef0/createSessionNoPassword1?userId=" + workcode + "&lang=cn&bip=127.0.0.1";
/*     */     
/*  77 */     this.logger.info("二开外部数据请求AWS地址 html0" + html0);
/*  78 */     String awsStr = "";
/*     */     
/*     */     try {
/*  81 */       awsStr = HttpClientUtil.get(html0);
/*  82 */       this.logger.info("二开外部数据请求AWS地址 awsStr ----> " + awsStr);
/*  83 */     } catch (Exception e) {
/*  84 */       writeLog(e);
/*  85 */       e.printStackTrace();
/*  86 */       this.logger.info("请求外部接口报错 entity1 " + e);
/*  87 */       result.put("msg", "访问外部接口请求 AWS 出错 html --> " + html0);
/*  88 */       return JSONObject.toJSONString(result);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  93 */     String html = "";
/*     */     
/*  95 */     awsStr = URLEncoder.encode(awsStr, "UTF-8");
/*  96 */     if ("2".equals(tabid)) {
/*  97 */       html = "http://************:8088/services/rs/wl/ad2c34f3803b9c9c1a06aeafa636f752/getHistoryTaskList3?userId=" + workcode + "&taskType=0&workflowGroupName=&workflowDefUUID=&start=1&limit=" + pageSize + "&bpmPortalHost=http://************:8088/portal&sid=" + awsStr;
/*     */     } else {
/*  99 */       html = "http://************:8088/services/rs/wl/ad2c34f3803b9c9c1a06aeafa636f752/getTaskList3?userId=" + workcode + "&taskType=0&workflowGroupName=&workflowDefUUID=&start=1&limit=" + pageSize + "&bpmPortalHost=http://************:8088/portal&sid=" + awsStr;
/*     */     } 
/*     */     
/* 102 */     this.logger.info("二开外部数据请求数据地址 html" + html);
/* 103 */     DefaultHttpClient httpClient = HttpManager.getHttpClient();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 111 */       dataJson = get(html);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 128 */       this.logger.info("二开外部数据请求数据地址 dataJson" + dataJson + "--------------" + dataJson);
/* 129 */     } catch (Exception e) {
/* 130 */       writeLog(e);
/* 131 */       e.printStackTrace();
/* 132 */       this.logger.info("二开外部数据元素报错 entity2" + e);
/* 133 */       result.put("msg", "访问外部接口 请求数据 出错 html --> " + html);
/* 134 */       result.put("error", " 出错  --> " + e);
/* 135 */       return JSONObject.toJSONString(result);
/*     */     } 
/*     */ 
/*     */     
/* 139 */     return dataJson;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 145 */   private static HttpConnectionManager connectionManager = (HttpConnectionManager)new MultiThreadedHttpConnectionManager();
/*     */   
/*     */   static {
/* 148 */     ((MultiThreadedHttpConnectionManager)connectionManager).setMaxConnectionsPerHost(1);
/* 149 */     ((MultiThreadedHttpConnectionManager)connectionManager).setMaxTotalConnections(10);
/* 150 */     ((MultiThreadedHttpConnectionManager)connectionManager).setConnectionStaleCheckingEnabled(true);
/*     */     try {
/* 152 */       Protocol.registerProtocol("https", new Protocol("https", (ProtocolSocketFactory)new EasySSLProtocolSocketFactory(), 443));
/* 153 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static HttpClient getHttpClient() {
/* 159 */     HttpClient client = new HttpClient(connectionManager);
/* 160 */     client.getHttpConnectionManager().getParams().setSoTimeout(6000);
/* 161 */     client.getHttpConnectionManager().getParams().setConnectionTimeout(2000);
/* 162 */     return client;
/*     */   }
/*     */   
/*     */   public static String get(String url) {
/* 166 */     GetMethod getMethod = null;
/*     */     try {
/* 168 */       HttpClient httpClient = getHttpClient();
/* 169 */       getMethod = new GetMethod(url);
/* 170 */       int statusCode = httpClient.executeMethod((HttpMethod)getMethod);
/* 171 */       if (statusCode == 200) {
/* 172 */         String charSet = getMethod.getResponseCharSet();
/* 173 */         byte[] responseBody = getMethod.getResponseBody();
/* 174 */         return new String(responseBody, "UTF-8");
/*     */       } 
/* 176 */       return null;
/*     */     
/*     */     }
/* 179 */     catch (Exception e) {
/* 180 */       e.printStackTrace();
/*     */     } finally {
/* 182 */       if (getMethod != null) {
/* 183 */         getMethod.releaseConnection();
/*     */       }
/*     */     } 
/* 186 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/customization/wfnumsOutdata/WfnumsOutDataAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */