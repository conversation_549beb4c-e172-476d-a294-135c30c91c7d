/*     */ package com.api.kq.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.engine.kq.util.TransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ public class KQGroupBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     String str = Util.null2String(paramMap.get("advance"));
/*  30 */     if ("".equals(str) || "0".equals(str))
/*  31 */       return getBasicData(paramMap); 
/*  32 */     return getAdvanceData(paramMap);
/*     */   }
/*     */   
/*     */   private Map<String, Object> getBasicData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  38 */     String str1 = Util.null2String(paramMap.get("groupname"));
/*  39 */     String str2 = Util.null2String(paramMap.get("kqtype"));
/*  40 */     String str3 = Util.null2String(paramMap.get("sqlWhere"));
/*  41 */     String str4 = " where 1=1 and (isdelete is null or  isdelete <> '1') ";
/*  42 */     if (!str1.equals("")) {
/*  43 */       str4 = str4 + " and groupname like '%";
/*  44 */       str4 = str4 + Util.fromScreen2(str1, this.user.getLanguage());
/*  45 */       str4 = str4 + "%'";
/*     */     } 
/*     */     
/*  48 */     if (!str2.equals("")) {
/*  49 */       str4 = str4 + " and kqtype =" + str2;
/*     */     }
/*     */     
/*  52 */     if (!str3.equals("")) {
/*  53 */       str4 = str4 + " and " + str3;
/*     */     }
/*     */     
/*  56 */     str4 = SqlUtils.replaceFirstAnd(str4);
/*     */     
/*  58 */     String str5 = " id ,groupname, kqtype ";
/*  59 */     String str6 = " kq_group ";
/*     */     
/*  61 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  62 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  63 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(125818, this.user.getLanguage()), "groupname", "groupname")).setIsInputCol(BoolAttr.TRUE).setShowType(1));
/*  64 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(388704, this.user.getLanguage()), "kqtype", "kqtype", "com.engine.kq.util.TransMethod.getKQTypeName", "" + this.user.getLanguage())).setShowType(0));
/*     */     
/*  66 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", "asc", arrayList);
/*  67 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  68 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Map<String, Object> getAdvanceData(Map<String, Object> paramMap) throws Exception {
/*  72 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  73 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  74 */     boolean bool = manageDetachComInfo.isUseHrmManageDetach();
/*     */     
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  78 */     String str1 = Util.null2String(paramMap.get("groupname"));
/*  79 */     String str2 = Util.null2String(paramMap.get("kqtype"));
/*  80 */     String str3 = Util.null2String(paramMap.get("sqlWhere"));
/*  81 */     String str4 = " where 1=1 and (isdelete is null or  isdelete <> '1') ";
/*  82 */     if (!str1.equals("")) {
/*  83 */       str4 = str4 + " and groupname like '%";
/*  84 */       str4 = str4 + Util.fromScreen2(str1, this.user.getLanguage());
/*  85 */       str4 = str4 + "%'";
/*     */     } 
/*     */     
/*  88 */     if (!str2.equals("")) {
/*  89 */       str4 = str4 + " and kqtype =" + str2;
/*     */     }
/*     */     
/*  92 */     if (!str3.equals("")) {
/*  93 */       str4 = str4 + " and " + str3;
/*     */     }
/*     */     
/*  96 */     if (bool) {
/*  97 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "HrmKQGroup:Add");
/*  98 */       String str = "";
/*  99 */       if (this.user.getUID() != 1) {
/* 100 */         for (byte b = 0; arrayOfInt != null && b < arrayOfInt.length; b++) {
/* 101 */           if (str.length() > 0) str = str + ","; 
/* 102 */           str = str + arrayOfInt[b];
/*     */         } 
/* 104 */         if (str.length() > 0) {
/* 105 */           str4 = str4 + " and subcompanyid in(" + str + ")";
/*     */         } else {
/* 107 */           str4 = str4 + " and 1=2 ";
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 112 */     str4 = SqlUtils.replaceFirstAnd(str4);
/*     */     
/* 114 */     String str5 = " id,id as id1,id as id2,groupname,subcompanyid,kqtype,excludeid,signintype,ipscope,locationcheck,locationcheckscope,wificheck,isdelete ";
/* 115 */     String str6 = " kq_group ";
/*     */     
/* 117 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 118 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 119 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(388700, this.user.getLanguage()), "groupname", "groupname", "com.engine.kq.util.TransMethod.getKQGroupName", "column:id+++" + this.user.getLanguage())).setIsInputCol(BoolAttr.TRUE).setShowType(1));
/* 120 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(1859, this.user.getLanguage()), "id1", null, "com.engine.kq.util.TransMethod.getGroupUserCount"));
/* 121 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "kqtype", "kqtype", "com.engine.kq.util.TransMethod.getKQTypeName", "" + this.user.getLanguage())).setShowType(0));
/* 122 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(27961, this.user.getLanguage()), "id2", null, "com.engine.kq.util.TransMethod.getKQGroupDetial", "column:kqtype+" + this.user.getLanguage()));
/*     */     
/* 124 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", "asc", arrayList);
/* 125 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 126 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 131 */     String str = Util.null2String(paramMap.get("advance"));
/* 132 */     if ("".equals(str) || "0".equals(str))
/* 133 */       return getBasicBrowserConditionInfo(paramMap); 
/* 134 */     return getAdvanceBrowserConditionInfo(paramMap);
/*     */   }
/*     */   
/*     */   public Map<String, Object> getBasicBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 138 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 139 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 140 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/* 142 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 143 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 388700, "groupname", true));
/*     */     
/* 145 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 146 */     arrayList1.add(new SearchConditionOption("", ""));
/* 147 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(389127, this.user.getLanguage())));
/* 148 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(389128, this.user.getLanguage())));
/* 149 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(389129, this.user.getLanguage())));
/* 150 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 388704, "kqtype", arrayList1));
/*     */     
/* 152 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getAdvanceBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 156 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 157 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 158 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/* 160 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 161 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 388700, "groupname"));
/*     */     
/* 163 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 164 */     arrayList1.add(new SearchConditionOption("", ""));
/* 165 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(389127, this.user.getLanguage())));
/* 166 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(389128, this.user.getLanguage())));
/* 167 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(389129, this.user.getLanguage())));
/* 168 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 388704, "kqtype", arrayList1));
/*     */     
/* 170 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 175 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 176 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 177 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 178 */     if (this.user == null || "".equals(str1)) {
/* 179 */       return (Map)hashMap;
/*     */     }
/* 181 */     TransMethod transMethod = new TransMethod();
/* 182 */     RecordSet recordSet = new RecordSet();
/* 183 */     String str2 = " select id,groupname, kqtype from kq_group where 1=1 and (isdelete is null or  isdelete <> '1') and id in (" + str1 + ") ";
/* 184 */     recordSet.executeQuery(str2, new Object[0]);
/* 185 */     while (recordSet.next()) {
/* 186 */       String str3 = Util.null2String(recordSet.getString("kqtype"));
/* 187 */       String str4 = transMethod.getKQTypeName(str3, "" + this.user.getLanguage());
/*     */       
/* 189 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 190 */       hashMap1.put("id", recordSet.getString("id"));
/* 191 */       hashMap1.put("groupname", Util.null2String(recordSet.getString("groupname")));
/* 192 */       hashMap1.put("kqtype", str4);
/* 193 */       arrayList.add(hashMap1);
/*     */     } 
/* 195 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 196 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 197 */     arrayList1.add(new ListHeadBean("groupname", "", 1, BoolAttr.TRUE));
/* 198 */     arrayList1.add(new ListHeadBean("kqtype", "", 0));
/*     */     
/* 200 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 201 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 202 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 203 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 209 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 210 */     String str1 = paramHttpServletRequest.getParameter("sqlWhere");
/* 211 */     String str2 = paramHttpServletRequest.getParameter("q");
/* 212 */     String str3 = paramHttpServletRequest.getParameter("includeAll");
/* 213 */     RecordSet recordSet = new RecordSet();
/* 214 */     String str4 = " where 1=1 and (isdelete is null or  isdelete <> '1') and kqType <>3 ";
/* 215 */     String str5 = getSettedGroupIds();
/* 216 */     if (str5.length() > 0 && "".equals(str3)) {
/* 217 */       str4 = str4 + " and id not in ( " + str5 + " ) ";
/*     */     }
/* 219 */     if (!str2.equals("")) {
/* 220 */       str4 = str4 + " and groupname like '%";
/* 221 */       str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/* 222 */       str4 = str4 + "%'";
/*     */     } 
/*     */     
/* 225 */     if (!str1.equals("")) {
/* 226 */       str4 = str4 + " and " + str1;
/*     */     }
/*     */     
/* 229 */     String str6 = " id ,groupname, kqtype ";
/* 230 */     String str7 = " kq_group ";
/* 231 */     String str8 = "select " + str6 + " from " + str7 + str4;
/*     */     
/* 233 */     recordSet.executeQuery(str8, new Object[0]);
/* 234 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 235 */     while (recordSet.next()) {
/* 236 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 237 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 238 */       String str = Util.null2String(recordSet.getString("groupname"));
/* 239 */       hashMap1.put("name", str);
/* 240 */       arrayList.add(hashMap1);
/*     */     } 
/* 242 */     hashMap.put("datas", arrayList);
/* 243 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getSettedGroupIds() {
/* 251 */     RecordSet recordSet = new RecordSet();
/* 252 */     String str = "";
/*     */     try {
/* 254 */       recordSet.executeQuery("select groupids from kq_OvertimeRules where isdelete is null or isdelete <>'1'", new Object[0]);
/* 255 */       while (recordSet.next()) {
/* 256 */         String str1 = Util.null2String(recordSet.getString("groupids"));
/* 257 */         if (str1.length() > 0) {
/* 258 */           if (str.length() > 0) str = str + ","; 
/* 259 */           str = str + str1;
/*     */         } 
/*     */       } 
/* 262 */     } catch (Exception exception) {
/* 263 */       recordSet.writeLog(exception);
/*     */     } 
/* 265 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/kq/browser/service/impl/KQGroupBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */