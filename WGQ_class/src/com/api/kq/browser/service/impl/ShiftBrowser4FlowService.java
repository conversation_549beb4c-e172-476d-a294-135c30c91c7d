/*    */ package com.api.kq.browser.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.engine.kq.biz.KQGroupMemberComInfo;
/*    */ import com.engine.kq.entity.KQGroupEntity;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.common.DateUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ShiftBrowser4FlowService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 34 */     String str1 = Util.null2String(paramMap.get("serial"));
/* 35 */     String str2 = Util.null2String(paramMap.get("resourceId"));
/* 36 */     String str3 = Util.null2String(paramMap.get("fromDate"));
/* 37 */     if (str3.length() == 0) {
/* 38 */       str3 = DateUtil.getCurrentDate();
/*    */     }
/* 40 */     KQGroupMemberComInfo kQGroupMemberComInfo = new KQGroupMemberComInfo();
/*    */     
/* 42 */     String str4 = " where 1=1 and (isdelete is null or  isdelete <> '1') ";
/* 43 */     if (!str1.equals("")) {
/* 44 */       str4 = str4 + " and serial like '%";
/* 45 */       str4 = str4 + Util.fromScreen2(str1, this.user.getLanguage());
/* 46 */       str4 = str4 + "%'";
/*    */     } 
/* 48 */     KQGroupEntity kQGroupEntity = kQGroupMemberComInfo.getUserKQGroupInfo(str2, str3, true);
/* 49 */     if (kQGroupEntity == null) {
/* 50 */       str4 = " where 1=2 ";
/*    */     } else {
/* 52 */       writeLog("kQGroupEntity:" + JSON.toJSONString(kQGroupEntity));
/* 53 */       String str = kQGroupEntity.getKqtype();
/* 54 */       if ("2".equalsIgnoreCase(str)) {
/* 55 */         String str7 = kQGroupEntity.getSerialids();
/* 56 */         if (str7.length() > 0) {
/* 57 */           str4 = str4 + "  and ( " + Util.getSubINClause(str7, "id", "in") + " or id = -1 )";
/*    */         } else {
/* 59 */           str4 = str4 + " and (1=2 or or id = -1)";
/*    */         } 
/*    */       } else {
/* 62 */         str4 = " where 1=2 ";
/*    */       } 
/*    */     } 
/* 65 */     str4 = SqlUtils.replaceFirstAnd(str4);
/*    */     
/* 67 */     String str5 = " id ,id as aliasid,serial ";
/* 68 */     String str6 = " kq_ShiftManagement_view ";
/*    */     
/* 70 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 71 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 72 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(125818, this.user.getLanguage()), "serial", "serial"));
/* 73 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(27961, this.user.getLanguage()), "aliasid", "aliasid", "com.engine.kq.cmd.shiftmanagement.toolkit.ShiftManagementToolKit.getShiftOnOffWorkSections", this.user.getLanguage() + "")).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 75 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", "asc", arrayList);
/* 76 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 77 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 82 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 83 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 84 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 85 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 86 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 125818, "serial", true));
/* 87 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/kq/browser/service/impl/ShiftBrowser4FlowService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */