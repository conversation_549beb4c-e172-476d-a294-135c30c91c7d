/*     */ package com.api.kq.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.engine.kq.util.TransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.interfaces.WfToModeTransmethod;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class KQAttSetBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  37 */     String str1 = Util.null2String(paramMap.get("workflowname"));
/*  38 */     String str2 = Util.null2String(paramMap.get("kqtype"));
/*  39 */     String str3 = " where 1=1  ";
/*  40 */     if (!str1.equals("")) {
/*  41 */       String str = " select id from workflow_base where workflowname like '%" + str1 + "%' ";
/*  42 */       str3 = str3 + " and field001 in(" + str + ")";
/*     */     } 
/*  44 */     if (!str2.equals("")) {
/*  45 */       str3 = str3 + " and field006 in (" + str2 + ")";
/*     */     }
/*     */     
/*  48 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*     */     
/*  50 */     String str4 = " field001 as id ,field001, field006 ";
/*  51 */     String str5 = " kq_att_proc_set ";
/*     */     
/*  53 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  54 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  55 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(18104, this.user.getLanguage()), "field001", "field001", "weaver.formmode.interfaces.WfToModeTransmethod.getWorkflowNameBlank", "" + this.user.getLanguage())).setIsInputCol(BoolAttr.TRUE).setShowType(1));
/*  56 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(16070, this.user.getLanguage()), "field006", "field006", "com.engine.kq.util.TransMethod.getFlowTypeName", "" + this.user.getLanguage())).setShowType(0));
/*     */     
/*  58 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, "id", "id", "asc", arrayList);
/*  59 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  66 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  67 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  68 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  69 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18104, "workflowname", true));
/*  70 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  76 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  77 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  78 */     if (this.user == null || "".equals(str1)) {
/*  79 */       return (Map)hashMap;
/*     */     }
/*  81 */     WfToModeTransmethod wfToModeTransmethod = new WfToModeTransmethod();
/*  82 */     TransMethod transMethod = new TransMethod();
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     String str2 = " select field001 as id ,field001, field006 from kq_att_proc_set where 1=1 and field001 in (" + str1 + ") ";
/*  85 */     recordSet.executeQuery(str2, new Object[0]);
/*  86 */     while (recordSet.next()) {
/*  87 */       String str3 = Util.null2String(recordSet.getString("kqtype"));
/*  88 */       String str4 = transMethod.getKQTypeName(str3, "" + this.user.getLanguage());
/*     */       
/*  90 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  91 */       hashMap1.put("id", recordSet.getString("id"));
/*  92 */       hashMap1.put("field001", wfToModeTransmethod.getWorkflowNameBlank(recordSet.getString("field001"), this.user.getLanguage() + ""));
/*  93 */       hashMap1.put("field006", (new TransMethod()).getFlowTypeName(recordSet.getString("field006"), "" + this.user.getLanguage()));
/*  94 */       arrayList.add(hashMap1);
/*     */     } 
/*  96 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  97 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  98 */     arrayList1.add(new ListHeadBean("field001", "", 1, BoolAttr.TRUE));
/*  99 */     arrayList1.add(new ListHeadBean("field006", "", 0));
/*     */     
/* 101 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 102 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 103 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 104 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 111 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 112 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 113 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("kqtype"));
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     String str3 = " where 1=1  ";
/* 116 */     if (!str1.equals("")) {
/* 117 */       String str = " select id from workflow_base where workflowname like '%" + str1 + "%' ";
/* 118 */       str3 = str3 + " and field001 in(" + str + ")";
/*     */     } 
/* 120 */     if (!str2.equals("")) {
/* 121 */       str3 = str3 + " and field006 in (" + str2 + ")";
/*     */     }
/*     */     
/* 124 */     String str4 = " field001 as id ,field001, field006 ";
/* 125 */     String str5 = " kq_att_proc_set ";
/* 126 */     String str6 = "select " + str4 + " from " + str5 + str3;
/*     */     
/* 128 */     WfToModeTransmethod wfToModeTransmethod = new WfToModeTransmethod();
/* 129 */     recordSet.executeQuery(str6, new Object[0]);
/* 130 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 131 */     while (recordSet.next()) {
/* 132 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 133 */       hashMap1.put("id", Util.null2String(recordSet.getString("field001")));
/* 134 */       String str = Util.null2String(wfToModeTransmethod.getWorkflowNameBlank(recordSet.getString("field001"), this.user.getLanguage() + ""));
/* 135 */       hashMap1.put("name", str);
/* 136 */       arrayList.add(hashMap1);
/*     */     } 
/* 138 */     hashMap.put("datas", arrayList);
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/kq/browser/service/impl/KQAttSetBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */