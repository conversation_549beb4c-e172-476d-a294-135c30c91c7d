/*    */ package com.api.kq.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.engine.kq.cmd.shiftmanagement.toolkit.ShiftManagementToolKit;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ShiftBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 34 */     String str1 = Util.null2String(paramMap.get("serial"));
/* 35 */     String str2 = " where 1=1 and (isdelete is null or  isdelete <> '1') ";
/* 36 */     if (!str1.equals("")) {
/* 37 */       str2 = str2 + " and serial like '%";
/* 38 */       str2 = str2 + Util.fromScreen2(str1, this.user.getLanguage());
/* 39 */       str2 = str2 + "%'";
/*    */     } 
/* 41 */     str2 = SqlUtils.replaceFirstAnd(str2);
/*    */     
/* 43 */     String str3 = " id ,id as aliasid,serial ";
/* 44 */     String str4 = " kq_ShiftManagement ";
/*    */     
/* 46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 47 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 48 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(125818, this.user.getLanguage()), "serial", "serial")).setIsInputCol(BoolAttr.TRUE));
/* 49 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(27961, this.user.getLanguage()), "aliasid", "aliasid", "com.engine.kq.cmd.shiftmanagement.toolkit.ShiftManagementToolKit.getShiftOnOffWorkSections", this.user.getLanguage() + ""));
/*    */     
/* 51 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, "id", "id", "asc", arrayList);
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 59 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 60 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 61 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 62 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 125818, "serial", true));
/* 63 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 69 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 70 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 71 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 72 */     RecordSet recordSet = new RecordSet();
/* 73 */     String str2 = "select * from kq_ShiftManagement ";
/* 74 */     String str3 = " where 1=1 ";
/* 75 */     String[] arrayOfString = Util.splitString(str1, ",");
/* 76 */     if (str1.length() > 0) {
/* 77 */       str3 = str3 + " and " + Util.getSubINClause(str1, "id", "in");
/*    */     }
/* 79 */     recordSet.executeSql(str2 + str3);
/* 80 */     ShiftManagementToolKit shiftManagementToolKit = new ShiftManagementToolKit();
/* 81 */     while (recordSet.next()) {
/* 82 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 83 */       String str = recordSet.getString("id");
/* 84 */       hashMap1.put("id", str);
/* 85 */       hashMap1.put("aliasid", Util.null2String(shiftManagementToolKit.getShiftOnOffWorkSections(str, this.user.getLanguage())));
/* 86 */       hashMap1.put("serial", Util.null2String(recordSet.getString("serial")));
/* 87 */       arrayList.add(hashMap1);
/*    */     } 
/*    */     
/* 90 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 91 */     arrayList1.add((new ListHeadBean("id", "")).setHide(BoolAttr.TRUE));
/* 92 */     arrayList1.add(new ListHeadBean("serial", ""));
/* 93 */     arrayList1.add(new ListHeadBean("aliasid", "", 1, BoolAttr.TRUE));
/*    */     
/* 95 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 96 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 98 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/kq/browser/service/impl/ShiftBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */