/*    */ package com.api.kq.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SearchConditionOption;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class KQLeaveBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 26 */     String str1 = Util.null2String(paramMap.get("resourceId"));
/* 27 */     String str2 = Util.null2String(paramMap.get("leaveName"));
/* 28 */     String str3 = Util.null2String(paramMap.get("minimumUnit"));
/* 29 */     String str4 = Util.null2String(paramMap.get("computingMode"));
/*    */     
/* 31 */     String str5 = " * ";
/* 32 */     String str6 = " kq_leaveRules ";
/* 33 */     String str7 = " where (isdelete is null or isdelete!=1) ";
/* 34 */     if (!str2.equals("")) {
/* 35 */       str7 = str7 + " and leaveName like '%" + str2 + "%'";
/*    */     }
/* 37 */     if (!str3.equals("") && !str3.equals("0")) {
/* 38 */       str7 = str7 + " and minimumUnit=" + str3;
/*    */     }
/* 40 */     if (!str4.equals("") && !str4.equals("0")) {
/* 41 */       str7 = str7 + " and computingMode=" + str4;
/*    */     }
/* 43 */     if (str1.equals("")) {
/* 44 */       str7 = str7 + " and 1=2 ";
/*    */     } else {
/*    */       try {
/* 47 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*    */ 
/*    */         
/* 50 */         str7 = str7 + " and organizationType=1 and organizationId=" + resourceComInfo.getSubCompanyID(str1) + " union select * from kq_leaveRules " + str7 + " and organizationType=0  and leaveName not in (select leaveName from kq_leaverules where organizationType=1 and organizationId=" + resourceComInfo.getSubCompanyID(str1) + ")";
/* 51 */       } catch (Exception exception) {
/* 52 */         exception.printStackTrace();
/*    */       } 
/*    */     } 
/*    */     
/* 56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 57 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 58 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(388883, this.user.getLanguage()), "leaveName")).setIsInputCol(BoolAttr.TRUE));
/* 59 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(388884, this.user.getLanguage()), "minimumUnit", "", "com.engine.kq.util.KQTransMethod.getMinimumUnitName", "" + this.user.getLanguage()));
/* 60 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(388888, this.user.getLanguage()), "computingMode", "", "com.engine.kq.util.KQTransMethod.getComputingModeName", "" + this.user.getLanguage()));
/* 61 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(388892, this.user.getLanguage()), "distributionMode", "", "com.engine.kq.util.KQTransMethod.getDistributionModName", "column:balanceEnable+" + this.user.getLanguage()));
/*    */     
/* 63 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str7, "", "id", "asc", arrayList);
/* 64 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 65 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 70 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 71 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 72 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*    */     
/* 74 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 388883, "leaveName", true));
/*    */ 
/*    */     
/* 77 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 78 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(337, this.user.getLanguage())));
/* 79 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(388885, this.user.getLanguage())));
/* 80 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(388886, this.user.getLanguage())));
/* 81 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(388887, this.user.getLanguage())));
/* 82 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 388884, "minimumUnit", arrayList1));
/*    */ 
/*    */     
/* 85 */     arrayList1 = new ArrayList<>();
/* 86 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(337, this.user.getLanguage())));
/* 87 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(388890, this.user.getLanguage())));
/* 88 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(388889, this.user.getLanguage())));
/* 89 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 388884, "computingMode", arrayList1));
/*    */     
/* 91 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 92 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/kq/browser/service/impl/KQLeaveBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */