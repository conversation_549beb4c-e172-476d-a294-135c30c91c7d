/*    */ package com.api.kq.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.engine.kq.cmd.shiftmanagement.toolkit.ShiftManagementToolKit;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MutiShiftBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 35 */     String str1 = Util.null2String(paramMap.get("serial"));
/* 36 */     String str2 = " where 1=1 and (isdelete is null or  isdelete <> '1') ";
/* 37 */     if (!str1.equals("")) {
/* 38 */       str2 = str2 + " and serial like '%";
/* 39 */       str2 = str2 + Util.fromScreen2(str1, this.user.getLanguage());
/* 40 */       str2 = str2 + "%'";
/*    */     } 
/* 42 */     str2 = SqlUtils.replaceFirstAnd(str2);
/*    */     
/* 44 */     String str3 = " id ,id as aliasid,serial ";
/* 45 */     String str4 = " kq_ShiftManagement ";
/*    */     
/* 47 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 48 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 49 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(27961, this.user.getLanguage()), "aliasid", "aliasid", "com.engine.kq.cmd.shiftmanagement.toolkit.ShiftManagementToolKit.getShiftOnOffWorkSections", this.user.getLanguage() + "")).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 51 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, "id", "id", "asc", arrayList);
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 60 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 61 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 62 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 63 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 125818, "serial", true));
/* 64 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 70 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 71 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 72 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 73 */     RecordSet recordSet = new RecordSet();
/* 74 */     String str2 = "select * from kq_ShiftManagement ";
/* 75 */     String str3 = " where 1=1 ";
/* 76 */     String[] arrayOfString = Util.splitString(str1, ",");
/* 77 */     if (str1.length() > 0) {
/* 78 */       str3 = str3 + " and " + Util.getSubINClause(str1, "id", "in");
/*    */     }
/* 80 */     recordSet.executeSql(str2 + str3);
/* 81 */     ShiftManagementToolKit shiftManagementToolKit = new ShiftManagementToolKit();
/* 82 */     while (recordSet.next()) {
/* 83 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 84 */       String str = recordSet.getString("id");
/* 85 */       hashMap1.put("id", str);
/* 86 */       hashMap1.put("aliasid", Util.null2String(shiftManagementToolKit.getShiftOnOffWorkSections(str, this.user.getLanguage())));
/* 87 */       hashMap1.put("serial", Util.null2String(recordSet.getString("serial")));
/* 88 */       arrayList.add(hashMap1);
/*    */     } 
/*    */     
/* 91 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 92 */     arrayList1.add((new ListHeadBean("id", "")).setHide(BoolAttr.TRUE));
/* 93 */     arrayList1.add(new ListHeadBean("serial", ""));
/* 94 */     arrayList1.add(new ListHeadBean("aliasid", "", 1, BoolAttr.TRUE));
/*    */     
/* 96 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 98 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 99 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/kq/browser/service/impl/MutiShiftBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */