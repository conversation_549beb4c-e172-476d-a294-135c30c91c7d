/*     */ package com.api.cpt.service;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.impl.CapitalBrowserService;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.cpt.util.CapitalAssortment;
/*     */ import com.api.cpt.util.CptFormItemUtil;
/*     */ import com.api.cpt.util.CptTableType;
/*     */ import com.api.cpt.util.SearchConditionUtil;
/*     */ import com.cloudstore.dev.api.util.TextUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalTypeComInfo;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.language.LanguageComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptTypeService
/*     */   extends BaseService
/*     */ {
/*     */   public Map<String, Object> getBaseInfo(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  46 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  47 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/*  49 */     CapitalAssortment capitalAssortment = new CapitalAssortment();
/*  50 */     Map map1 = capitalAssortment.getTree(user, map, "");
/*     */ 
/*     */     
/*  53 */     ArrayList arrayList1 = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  60 */     ArrayList arrayList2 = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  68 */     List list = (new SearchConditionUtil()).getCondition("CptType", user);
/*     */     
/*  70 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  71 */     hashMap.put("pagetitle", SystemEnv.getHtmlLabelName(32722, user.getLanguage()));
/*  72 */     hashMap.put("treedata", map1);
/*  73 */     hashMap.put("countcfg", arrayList1);
/*  74 */     hashMap.put("groupinfo", arrayList2);
/*  75 */     hashMap.put("conditioninfo", list);
/*  76 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> searchResult(User paramUser, Map<String, Object> paramMap) {
/*  85 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  86 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  87 */     CapitalBrowserService capitalBrowserService = new CapitalBrowserService();
/*     */     
/*  89 */     boolean bool1 = HrmUserVarify.checkUserRight("CptCapitalTypeAdd:Add", paramUser);
/*  90 */     boolean bool2 = HrmUserVarify.checkUserRight("CptCapitalTypeEdit:Delete", paramUser);
/*  91 */     boolean bool3 = HrmUserVarify.checkUserRight("CptCapitalTypeEdit:Edit", paramUser);
/*     */     
/*  93 */     String str1 = Util.null2String(paramMap.get("name"));
/*  94 */     String str2 = Util.null2String(paramMap.get("description"));
/*     */     
/*  96 */     String str3 = " where 1=1 ";
/*     */     
/*  98 */     if (!"".equals(str1)) {
/*  99 */       str3 = str3 + " and name like '%" + str1 + "%' ";
/*     */     }
/*     */     
/* 102 */     if (!"".equals(str2)) {
/* 103 */       str3 = str3 + " and description like '%" + str2 + "%' ";
/*     */     }
/*     */     
/* 106 */     String str4 = "m.id asc";
/* 107 */     String str5 = "";
/* 108 */     String str6 = "m.id,m.name,m.description,m.typecode ";
/* 109 */     String str7 = " CptCapitalType m";
/* 110 */     String str8 = paramUser.getUID() + "+" + paramUser.getLogintype() + "+" + paramUser.getLanguage() + "+cpt_cpttype+3";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 117 */     str5 = " <table pageId=\"" + CptTableType.SEARCH_TYPES_TABLE.getPageUid() + "\" pageUid=\"" + CptTableType.SEARCH_TYPES_TABLE.getPageUid() + "\" instanceid=\"CptCapitalType\" tabletype=\"checkbox\" pagesize=\"" + CptTableType.SEARCH_TYPES_TABLE.getPageSize() + "\" >       <sql backfields=\"" + str6 + "\" sqlform=\"" + str7 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str3) + "\"  sqlorderby=\"" + str4 + "\"  sqlprimarykey=\"m.id\" sqlsortway=\"desc\" sqlisdistinct=\"true\" /> <checkboxpopedom  id=\"checkbox\" popedompara=\"column:id\" showmethod=\"weaver.cpt.util.CapitalTransUtil.getCanDelCptType\" />       <head>           <col width=\"25%\"  text=\"" + SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage())) + "\" column=\"name\" orderkey=\"name\"   />           <col width=\"50%\"  text=\"" + SystemEnv.getHtmlLabelName(433, Util.getIntValue(paramUser.getLanguage())) + "\" column=\"description\" orderkey=\"description\"  />           <col width=\"25%\"  text=\"" + SystemEnv.getHtmlLabelName(21942, Util.getIntValue(paramUser.getLanguage())) + "\" column=\"typecode\" orderkey=\"typecode\" />       </head>\t\t<operates>     <popedom  column=\"id\" otherpara='" + str8 + "' transmethod='com.api.cpt.util.ConditionUtil.getOperates'  ></popedom> ";
/*     */ 
/*     */ 
/*     */     
/* 121 */     if (bool1 || bool2 || bool3)
/*     */     {
/*     */ 
/*     */       
/* 125 */       str5 = str5 + "\t\t<operate href=\"javascript:onEdit();\" text=\"" + SystemEnv.getHtmlLabelName(93, Util.getIntValue(paramUser.getLanguage())) + "\" target=\"_self\" index=\"0\"/>\t\t<operate href=\"javascript:onDel();\" text=\"" + SystemEnv.getHtmlLabelName(91, Util.getIntValue(paramUser.getLanguage())) + "\" target=\"_self\" index=\"1\"/>\t\t<operate href=\"javascript:onLog();\" text=\"" + SystemEnv.getHtmlLabelName(83, Util.getIntValue(paramUser.getLanguage())) + "\" target=\"_self\" index=\"2\"/>";
/*     */     }
/*     */ 
/*     */     
/* 129 */     str5 = str5 + "\t\t</operates> </table>";
/*     */     
/* 131 */     String str9 = CptTableType.SEARCH_TYPES_TABLE.getPageUid() + "_" + Util.getEncrypt(Util.getRandom());
/* 132 */     Util_TableMap.setVal(str9, str5);
/* 133 */     hashMap.put("sessionkey", str9);
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> doDelete(User paramUser, Map<String, Object> paramMap, HttpServletRequest paramHttpServletRequest) throws Exception {
/* 138 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 139 */     int i = Integer.parseInt(Util.null2String(paramMap.get("id")));
/* 140 */     String str1 = Util.null2String(paramMap.get("name"));
/* 141 */     if (!HrmUserVarify.checkUserRight("CptCapitalTypeEdit:Delete", paramUser)) {
/*     */ 
/*     */       
/* 144 */       hashMap.put("success", Boolean.valueOf(false));
/* 145 */       return (Map)hashMap;
/*     */     } 
/* 147 */     char c = Util.getSeparator();
/* 148 */     String str2 = "" + i;
/* 149 */     RecordSet recordSet = new RecordSet();
/* 150 */     recordSet.executeProc("CptCapitalType_Delete", str2);
/*     */ 
/*     */     
/* 153 */     if (recordSet.next() && Util.null2String(recordSet.getString(1)).equals("-1")) {
/*     */ 
/*     */       
/* 156 */       hashMap.put("success", Boolean.valueOf(false));
/* 157 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 160 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 161 */     sysMaintenanceLog.resetParameter();
/* 162 */     sysMaintenanceLog.setRelatedId(i);
/* 163 */     sysMaintenanceLog.setRelatedName(str1);
/* 164 */     sysMaintenanceLog.setOperateType("3");
/* 165 */     sysMaintenanceLog.setOperateDesc("CptCapitalType_Delete," + str2);
/* 166 */     sysMaintenanceLog.setOperateItem("44");
/* 167 */     sysMaintenanceLog.setOperateUserid(paramUser.getUID(paramUser, "CptCapitalTypeEdit:Delete"));
/* 168 */     sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 169 */     sysMaintenanceLog.setSysLogInfo();
/*     */     
/* 171 */     CapitalTypeComInfo capitalTypeComInfo = new CapitalTypeComInfo();
/* 172 */     capitalTypeComInfo.removeCapitalTypeCache();
/*     */     
/* 174 */     hashMap.put("success", Boolean.valueOf(true));
/* 175 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> batchDelete(User paramUser, Map<String, Object> paramMap, HttpServletRequest paramHttpServletRequest) throws Exception {
/* 182 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 183 */     if (!HrmUserVarify.checkUserRight("CptCapitalTypeEdit:Delete", paramUser)) {
/*     */ 
/*     */       
/* 186 */       hashMap.put("success", Boolean.valueOf(false));
/* 187 */       return (Map)hashMap;
/*     */     } 
/* 189 */     String str1 = Util.null2String(paramMap.get("ids"));
/* 190 */     String str2 = Util.null2String(paramMap.get("name"));
/* 191 */     String[] arrayOfString = Util.TokenizerString2(str1, ",");
/* 192 */     int i = 0;
/* 193 */     RecordSet recordSet = new RecordSet();
/* 194 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 195 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 196 */       i = Util.getIntValue(arrayOfString[b]);
/* 197 */       String str = "" + i;
/*     */       
/* 199 */       recordSet.executeProc("CptCapitalType_Delete", str);
/*     */ 
/*     */       
/* 202 */       sysMaintenanceLog.resetParameter();
/* 203 */       sysMaintenanceLog.setRelatedId(i);
/* 204 */       sysMaintenanceLog.setRelatedName(str2);
/* 205 */       sysMaintenanceLog.setOperateType("3");
/* 206 */       sysMaintenanceLog.setOperateDesc("CptCapitalType_Delete," + str);
/* 207 */       sysMaintenanceLog.setOperateItem("44");
/* 208 */       sysMaintenanceLog.setOperateUserid(paramUser.getUID(paramUser, "CptCapitalTypeEdit:Delete"));
/* 209 */       sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 210 */       sysMaintenanceLog.setSysLogInfo();
/*     */     } 
/*     */     
/* 213 */     CapitalTypeComInfo capitalTypeComInfo = new CapitalTypeComInfo();
/* 214 */     capitalTypeComInfo.removeCapitalTypeCache();
/*     */     
/* 216 */     hashMap.put("success", Boolean.valueOf(true));
/* 217 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> doAdd(User paramUser, Map<String, Object> paramMap, HttpServletRequest paramHttpServletRequest) throws Exception {
/* 224 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 225 */     RecordSet recordSet = new RecordSet();
/* 226 */     String str1 = Util.null2String(paramMap.get("name")).trim();
/* 227 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), -1);
/* 228 */     String str2 = Util.null2String(paramMap.get("description"));
/* 229 */     String str3 = Util.null2String(paramMap.get("typecode"));
/* 230 */     LanguageComInfo languageComInfo = null;
/*     */     
/*     */     try {
/* 233 */       languageComInfo = new LanguageComInfo();
/* 234 */     } catch (Exception exception) {
/* 235 */       (new BaseBean()).writeLog(exception);
/*     */     } 
/* 237 */     String str4 = "";
/* 238 */     while (languageComInfo.next()) {
/* 239 */       String str = recordSet.getDBType();
/* 240 */       if (str.equals("sqlserver")) {
/* 241 */         str4 = str4 + "dbo.convToMultiLang(name," + languageComInfo.getLanguageid() + ")='" + Util.null2String(Util.formatMultiLang(str1, languageComInfo.getLanguageid())) + "' or ";
/*     */       }
/* 243 */       if (str.equals("oracle") || str.equals("mysql") || str.equals("postgresql")) {
/* 244 */         str4 = str4 + "convToMultiLang(name," + languageComInfo.getLanguageid() + ")='" + Util.null2String(Util.formatMultiLang(str1, languageComInfo.getLanguageid())) + "' or ";
/*     */       }
/*     */     } 
/* 247 */     str4 = str4.substring(0, str4.length() - 3);
/*     */     
/* 249 */     recordSet.executeSql("select * from CptCapitalType where " + str4 + " and id!=" + i);
/*     */     
/* 251 */     if (recordSet.next()) {
/* 252 */       hashMap.put("msgid", Integer.valueOf(11));
/* 253 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(703, paramUser.getLanguage()) + "：" + str1 + " " + SystemEnv.getHtmlLabelName(24943, paramUser.getLanguage()) + " ！");
/* 254 */       return (Map)hashMap;
/*     */     } 
/* 256 */     if (!HrmUserVarify.checkUserRight("CptCapitalTypeAdd:Add", paramUser)) {
/* 257 */       hashMap.put("msgid", Integer.valueOf(12));
/* 258 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(83280, paramUser.getLanguage()));
/* 259 */       return (Map)hashMap;
/*     */     } 
/* 261 */     char c = Util.getSeparator();
/*     */     
/* 263 */     String str5 = str1 + c + str2;
/* 264 */     recordSet.executeProc("CptCapitalType_Insert", str5);
/* 265 */     i = 0;
/*     */     
/* 267 */     recordSet.executeSql("select max(id) from CptCapitalType");
/* 268 */     if (recordSet.next()) {
/* 269 */       i = recordSet.getInt(1);
/*     */     }
/*     */     
/* 272 */     recordSet.executeSql("update CptCapitalType set typecode = '" + str3 + "' where id = " + i);
/*     */     
/* 274 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 275 */     sysMaintenanceLog.resetParameter();
/* 276 */     sysMaintenanceLog.setRelatedId(i);
/* 277 */     sysMaintenanceLog.setRelatedName(str1);
/* 278 */     sysMaintenanceLog.setOperateType("1");
/* 279 */     sysMaintenanceLog.setOperateDesc("CptCapitalType_Insert," + str5);
/* 280 */     sysMaintenanceLog.setOperateItem("44");
/* 281 */     sysMaintenanceLog.setOperateUserid(paramUser.getUID(paramUser, "CptCapitalTypeAdd:Add"));
/* 282 */     sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 283 */     sysMaintenanceLog.setSysLogInfo();
/* 284 */     CapitalTypeComInfo capitalTypeComInfo = new CapitalTypeComInfo();
/* 285 */     capitalTypeComInfo.removeCapitalTypeCache();
/* 286 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> doEdit(User paramUser, Map<String, Object> paramMap, HttpServletRequest paramHttpServletRequest) throws Exception {
/* 292 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 293 */     RecordSet recordSet = new RecordSet();
/* 294 */     String str1 = Util.null2String(paramMap.get("name"));
/* 295 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), -1);
/* 296 */     String str2 = Util.null2String(paramMap.get("description"));
/* 297 */     String str3 = Util.null2String(paramMap.get("typecode"));
/*     */     
/* 299 */     LanguageComInfo languageComInfo = null;
/*     */     try {
/* 301 */       languageComInfo = new LanguageComInfo();
/* 302 */     } catch (Exception exception) {
/* 303 */       (new BaseBean()).writeLog(exception);
/*     */     } 
/* 305 */     String str4 = "";
/* 306 */     while (languageComInfo.next()) {
/* 307 */       String str = recordSet.getDBType();
/* 308 */       if (str.equals("sqlserver")) {
/* 309 */         str4 = str4 + "dbo.convToMultiLang(name," + languageComInfo.getLanguageid() + ")='" + Util.null2String(Util.formatMultiLang(str1, languageComInfo.getLanguageid())) + "' or ";
/*     */       }
/* 311 */       if (str.equals("oracle") || str.equals("mysql") || str.equals("postgresql"))
/* 312 */         str4 = str4 + "convToMultiLang(name," + languageComInfo.getLanguageid() + ")='" + Util.null2String(Util.formatMultiLang(str1, languageComInfo.getLanguageid())) + "' or "; 
/*     */     } 
/* 314 */     str4 = str4.substring(0, str4.length() - 3);
/* 315 */     recordSet.executeSql("select * from CptCapitalType where (" + str4 + ") and id!=" + i);
/*     */     
/* 317 */     if (recordSet.next()) {
/* 318 */       hashMap.put("msgid", Integer.valueOf(11));
/* 319 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(703, paramUser.getLanguage()) + "：" + str1 + SystemEnv.getHtmlLabelName(24943, paramUser.getLanguage()) + "！");
/* 320 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 323 */     if (!HrmUserVarify.checkUserRight("CptCapitalTypeEdit:Edit", paramUser)) {
/* 324 */       hashMap.put("msgid", Integer.valueOf(12));
/* 325 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(83280, paramUser.getLanguage()));
/* 326 */       return (Map)hashMap;
/*     */     } 
/* 328 */     char c = Util.getSeparator();
/* 329 */     String str5 = "" + i + c + str1 + c + str2;
/* 330 */     recordSet.executeProc("CptCapitalType_Update", str5);
/*     */     
/* 332 */     recordSet.executeSql("update CptCapitalType set typecode = '" + str3 + "' where id = " + i);
/*     */ 
/*     */     
/* 335 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 336 */     sysMaintenanceLog.resetParameter();
/* 337 */     sysMaintenanceLog.setRelatedId(i);
/* 338 */     sysMaintenanceLog.setRelatedName(str1);
/* 339 */     sysMaintenanceLog.setOperateType("2");
/* 340 */     sysMaintenanceLog.setOperateDesc("CptCapitalType_Update," + str5);
/* 341 */     sysMaintenanceLog.setOperateItem("44");
/* 342 */     sysMaintenanceLog.setOperateUserid(paramUser.getUID(paramUser, "CptCapitalTypeEdit:Edit"));
/* 343 */     sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 344 */     sysMaintenanceLog.setSysLogInfo();
/*     */     
/* 346 */     CapitalTypeComInfo capitalTypeComInfo = new CapitalTypeComInfo();
/* 347 */     capitalTypeComInfo.removeCapitalTypeCache();
/*     */     
/* 349 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getFormForAdd(User paramUser, Map<String, Object> paramMap) {
/* 376 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 377 */     ArrayList<Map> arrayList1 = new ArrayList();
/* 378 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 380 */     arrayList1.add(CptFormItemUtil.getFormItemForInput("name", SystemEnv.getHtmlLabelName(195, paramUser.getLanguage()), "", 50, 3));
/* 381 */     arrayList1.add(CptFormItemUtil.getFormItemForInput("description", SystemEnv.getHtmlLabelName(433, paramUser.getLanguage()), "", 50, 2));
/* 382 */     arrayList1.add(CptFormItemUtil.getFormItemForInput("typecode", SystemEnv.getHtmlLabelName(21942, paramUser.getLanguage()), "", 50, 2));
/*     */     
/* 384 */     hashMap1.put("items", arrayList1);
/* 385 */     hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 386 */     arrayList.add(hashMap1);
/*     */     
/* 388 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 389 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(503079, paramUser.getLanguage()));
/* 390 */     hashMap2.put("fieldinfo", arrayList);
/* 391 */     return (Map)hashMap2;
/*     */   }
/*     */   
/*     */   public List<Map<String, Object>> getFormForEdit(User paramUser, Map<String, Object> paramMap) {
/* 395 */     String str1 = Util.null2String(paramMap.get("id"));
/* 396 */     RecordSet recordSet = new RecordSet();
/* 397 */     String str2 = "select * from CptCapitalType where id = " + str1;
/* 398 */     recordSet.execute(str2);
/* 399 */     String str3 = "";
/* 400 */     String str4 = "";
/* 401 */     String str5 = "";
/* 402 */     if (recordSet.next()) {
/* 403 */       str3 = recordSet.getString("name");
/* 404 */       str4 = recordSet.getString("description");
/* 405 */       str5 = recordSet.getString("typecode");
/*     */     } 
/* 407 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 409 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 410 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 411 */     hashMap.put("title", SystemEnv.getHtmlLabelName(83683, paramUser.getLanguage()));
/* 412 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/* 413 */     hashMap.put("items", arrayList1);
/* 414 */     arrayList.add(hashMap);
/*     */     
/* 416 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/*     */     
/* 418 */     SearchConditionItem searchConditionItem = null;
/* 419 */     searchConditionItem = SearchConditionUtil.createCondition(paramUser, ConditionType.INPUT, "195", "name", str3, 3);
/* 420 */     arrayList1.add(searchConditionItem);
/* 421 */     searchConditionItem = SearchConditionUtil.createCondition(paramUser, ConditionType.INPUT, "433", "description", str4, 2);
/* 422 */     arrayList1.add(searchConditionItem);
/* 423 */     searchConditionItem = SearchConditionUtil.createCondition(paramUser, ConditionType.INPUT, "21942", "typecode", str5, 2);
/* 424 */     arrayList1.add(searchConditionItem);
/*     */     
/* 426 */     return (List)arrayList;
/*     */   }
/*     */   public Map<String, Object> getFormDataForEdit(User paramUser, Map<String, Object> paramMap) {
/* 429 */     String str1 = Util.null2String(paramMap.get("id"));
/* 430 */     RecordSet recordSet = new RecordSet();
/* 431 */     String str2 = "select * from CptCapitalType where id = '" + str1 + "'";
/* 432 */     recordSet.execute(str2);
/* 433 */     String str3 = "";
/* 434 */     String str4 = "";
/* 435 */     String str5 = "";
/* 436 */     if (recordSet.next()) {
/*     */       
/* 438 */       str3 = TextUtil.toBase64ForMultilang(Util.null2String(recordSet.getString("name")));
/* 439 */       str4 = recordSet.getString("description");
/* 440 */       str5 = recordSet.getString("typecode");
/*     */     } 
/* 442 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 443 */     hashMap.put("name", str3);
/* 444 */     hashMap.put("description", str4);
/* 445 */     hashMap.put("typecode", str5);
/* 446 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/service/CptTypeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */