/*    */ package com.api.cpt.service;
/*    */ 
/*    */ import java.util.Enumeration;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BaseService
/*    */ {
/* 21 */   BaseBean loggerBean = new BaseBean();
/*    */ 
/*    */   
/*    */   protected Map<String, Object> getRequestParams(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 26 */     Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 27 */     while (enumeration.hasMoreElements()) {
/* 28 */       String str = enumeration.nextElement();
/* 29 */       hashMap.put(str, paramHttpServletRequest.getParameter(str));
/*    */     } 
/* 31 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/service/BaseService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */