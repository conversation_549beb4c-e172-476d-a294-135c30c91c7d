/*     */ package com.api.cpt.service;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.impl.CapitalBrowserService;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.cpt.util.CptFormItemUtil;
/*     */ import com.api.cpt.util.CptTableType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptMendService
/*     */   extends BaseService
/*     */ {
/*     */   public Map<String, Object> viewMend(User paramUser, Map<String, Object> paramMap) {
/*  69 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  70 */     String str1 = Util.null2String(paramMap.get("capitalid"));
/*  71 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  72 */     CapitalStateComInfo capitalStateComInfo = new CapitalStateComInfo();
/*  73 */     XssUtil xssUtil = new XssUtil();
/*  74 */     RecordSet recordSet = new RecordSet();
/*  75 */     recordSet.execute("select * from cptcapital where id = " + str1);
/*  76 */     String str2 = "";
/*  77 */     String str3 = "";
/*  78 */     String str4 = "";
/*  79 */     String str5 = "";
/*  80 */     String str6 = "";
/*  81 */     if (recordSet.next()) {
/*  82 */       str5 = Util.null2String(recordSet.getString("name"));
/*  83 */       str2 = Util.null2String(recordSet.getString("blongdepartment"));
/*  84 */       str3 = departmentComInfo.getDepartmentname(str2);
/*  85 */       str4 = Util.null2String(recordSet.getString("capitalspec"));
/*  86 */       str6 = capitalStateComInfo.getCapitalStatename(Util.null2String(recordSet.getString("stateid")));
/*     */     } 
/*     */     
/*  89 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  90 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  91 */     ArrayList<Map> arrayList1 = new ArrayList();
/*     */     
/*  93 */     Map map = CptFormItemUtil.getFormItemForInput("name", SystemEnv.getHtmlLabelName(33016, Util.getIntValue(paramUser.getLanguage())), str5, 200, 1);
/*  94 */     arrayList1.add(map);
/*     */     
/*  96 */     map = CptFormItemUtil.getFormItemForInput("blongdeptid", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), str3, 200, 1);
/*  97 */     arrayList1.add(map);
/*     */     
/*  99 */     map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), str4, 200, 1);
/* 100 */     arrayList1.add(map);
/*     */     
/* 102 */     map = CptFormItemUtil.getFormItemForInput("stateidname", SystemEnv.getHtmlLabelName(602, Util.getIntValue(paramUser.getLanguage())), str6, 200, 1);
/* 103 */     arrayList1.add(map);
/*     */     
/* 105 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 106 */     hashMap3.put("sqlwhere", xssUtil.put("where t1.type=2"));
/* 107 */     map = CptFormItemUtil.getFormItemForBrowser("maintaincompany", SystemEnv.getHtmlLabelName(1399, Util.getIntValue(paramUser.getLanguage())), "7", "", 2, "", null, hashMap3);
/* 108 */     arrayList1.add(map);
/*     */     
/* 110 */     map = CptFormItemUtil.getFormItemForDate("menddate", SystemEnv.getHtmlLabelName(1409, Util.getIntValue(paramUser.getLanguage())), DateHelper.getCurrentDate(), 2);
/* 111 */     arrayList1.add(map);
/*     */     
/* 113 */     map = CptFormItemUtil.getFormItemForDate("mendperioddate", SystemEnv.getHtmlLabelName(22457, Util.getIntValue(paramUser.getLanguage())), "", 2);
/* 114 */     arrayList1.add(map);
/*     */     
/* 116 */     map = CptFormItemUtil.getFormItemForInput("cost", SystemEnv.getHtmlLabelName(1393, Util.getIntValue(paramUser.getLanguage())), "", 20, 2);
/* 117 */     arrayList1.add(map);
/*     */     
/* 119 */     map = CptFormItemUtil.getFormItemForBrowser("operator", SystemEnv.getHtmlLabelName(1047, Util.getIntValue(paramUser.getLanguage())), "1", "", 2, "", null, null);
/* 120 */     arrayList1.add(map);
/*     */     
/* 122 */     map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/* 123 */     arrayList1.add(map);
/*     */     
/* 125 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(22459, Util.getIntValue(paramUser.getLanguage())));
/* 126 */     hashMap2.put("items", arrayList1);
/* 127 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 128 */     arrayList.add(hashMap2);
/*     */ 
/*     */ 
/*     */     
/* 132 */     hashMap1.put("mainTableInfo", arrayList);
/* 133 */     hashMap1.put("capitalid", str1);
/*     */     
/* 135 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> doCptMend(User paramUser, Map<String, Object> paramMap) throws Exception {
/* 146 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 147 */     String str1 = "";
/* 148 */     RecordSet recordSet = new RecordSet();
/* 149 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*     */     
/* 151 */     char c = Util.getSeparator();
/* 152 */     String str2 = "";
/*     */     
/* 154 */     String str3 = Util.null2String(paramMap.get("menddate"));
/* 155 */     String str4 = Util.null2String(paramMap.get("maintaincompany"));
/* 156 */     String str5 = Util.null2String(paramMap.get("operator"));
/* 157 */     String str6 = Util.null2String(paramMap.get("mendperioddate"));
/* 158 */     String str7 = Util.null2s(Util.null2String(paramMap.get("cost")), "0");
/* 159 */     String str8 = Util.null2String(paramMap.get("remark"));
/* 160 */     String str9 = Util.null2String(paramMap.get("capitalid"));
/* 161 */     String str10 = "";
/*     */     
/* 163 */     String str11 = "";
/* 164 */     str11 = "select departmentid from CptCapital where id=" + str9;
/* 165 */     recordSet.execute(str11);
/* 166 */     recordSet.next();
/* 167 */     str10 = recordSet.getString("departmentid");
/*     */     
/* 169 */     str2 = str9;
/* 170 */     str2 = str2 + c + str3;
/* 171 */     str2 = str2 + c + "";
/* 172 */     str2 = str2 + c + "";
/* 173 */     str2 = str2 + c + "1";
/* 174 */     str2 = str2 + c + "";
/* 175 */     str2 = str2 + c + "0";
/* 176 */     str2 = str2 + c + str4;
/* 177 */     str2 = str2 + c + str7;
/* 178 */     str2 = str2 + c + "4";
/* 179 */     str2 = str2 + c + str8;
/* 180 */     str2 = str2 + c + str5;
/* 181 */     str2 = str2 + c + str6;
/* 182 */     str2 = str2 + c + str10;
/* 183 */     recordSet.executeProc("CptUseLogMend_Insert2", str2);
/* 184 */     capitalComInfo.removeCapitalCache();
/*     */     
/* 186 */     hashMap.put("msg", str1);
/* 187 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getSearchCondition(User paramUser, Map<String, Object> paramMap) {
/* 198 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/*     */     
/* 200 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 201 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 202 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 203 */     hashMap.put("title", SystemEnv.getHtmlLabelName(20331, paramUser.getLanguage()));
/* 204 */     hashMap.put("defaultshow", Boolean.valueOf(true));
/*     */     
/* 206 */     SearchConditionItem searchConditionItem = null;
/* 207 */     searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 17482, "resourceid", "1");
/* 208 */     arrayList1.add(searchConditionItem);
/*     */     
/* 210 */     String[] arrayOfString = { "selectdate_select", "selectdate_start", "selectdate_end" };
/* 211 */     searchConditionItem = conditionFactory.createCondition(ConditionType.DATE, 2061, arrayOfString);
/* 212 */     arrayList1.add(searchConditionItem);
/*     */     
/* 214 */     searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 124, "checkDeptId", "4");
/* 215 */     arrayList1.add(searchConditionItem);
/*     */     
/* 217 */     searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 141, "subcomid", "164");
/* 218 */     arrayList1.add(searchConditionItem);
/*     */     
/* 220 */     hashMap.put("items", arrayList1);
/*     */     
/* 222 */     arrayList.add(hashMap);
/* 223 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCptLogList(User paramUser, Map<String, Object> paramMap) {
/* 233 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 234 */     CapitalBrowserService capitalBrowserService = new CapitalBrowserService();
/* 235 */     String str1 = Util.null2String(paramMap.get("capitalid"));
/* 236 */     String str2 = Util.null2String(paramMap.get("resourceid"));
/* 237 */     String str3 = Util.null2String(paramMap.get("checkDeptId"));
/* 238 */     String str4 = Util.null2String(paramMap.get("subcomid"));
/*     */     
/* 240 */     String str5 = " where operateitem=51 and relatedid='" + str1 + "'";
/* 241 */     if (!"".equals(str2)) {
/* 242 */       str5 = str5 + " AND operateUserId = " + str2;
/*     */     }
/*     */     
/* 245 */     str5 = str5 + " AND SysMaintenanceLog.operateItem = SystemLogItem.itemId";
/*     */     
/* 247 */     String str6 = Util.null2String(paramMap.get("selectdate_select"));
/* 248 */     String str7 = Util.null2String(paramMap.get("selectdate_start"));
/* 249 */     String str8 = Util.null2String(paramMap.get("selectdate_end"));
/* 250 */     Map map = CapitalBrowserService.getDateRangeByDateField(str6, str7, str8);
/* 251 */     String str9 = (String)map.get("startdate");
/* 252 */     String str10 = (String)map.get("enddate");
/*     */     
/* 254 */     if (!"".equals(str9)) {
/* 255 */       str5 = str5 + " and operateDate >= '" + str9 + "'";
/*     */     }
/* 257 */     if (!"".equals(str10)) {
/* 258 */       str5 = str5 + " and operateDate <= '" + str10 + "'";
/*     */     }
/* 260 */     if (!"".equals(str4)) {
/* 261 */       str5 = str5 + " AND operateUserId in  (select id from hrmresource where subcompanyid1 in (" + str4 + ") ) ";
/*     */     }
/* 263 */     if (!"".equals(str3)) {
/* 264 */       str5 = str5 + " AND operateUserId in  (select id from hrmresource where departmentid in (" + str3 + ") ) ";
/*     */     }
/*     */ 
/*     */     
/* 268 */     String str11 = "id";
/* 269 */     String str12 = "";
/* 270 */     String str13 = "id, relatedName, operateType, lableId, operateUserId, operateDate, operateTime, clientAddress";
/* 271 */     String str14 = "SysMaintenanceLog, SystemLogItem";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 284 */     str12 = "<table pageId=\"" + CptTableType.SEARCH_CPTLOG_TABLE.getPageUid() + "\" instanceid=\"SysMaintenanceLog\" pageUid=\"" + CptTableType.SEARCH_CPTLOG_TABLE.getPageUid() + "\" tabletype=\"none\" pagesize=\"" + CptTableType.SEARCH_INSTOCKHIS_TABLE.getPageSize() + "\"><sql backfields=\"" + str13 + "\" sqlform=\"" + str14 + "\" sqlprimarykey=\"id\" sqlorderby=\"" + str11 + "\" sqlsortway=\"DESC\" sqlwhere=\"" + Util.toHtmlForSplitPage(str5) + "\" sqlisdistinct=\"true\" /><head><col width=\"15%\" text=\"" + SystemEnv.getHtmlLabelName(97, paramUser.getLanguage()) + "\" column=\"operateDate\" orderkey=\"operateDate\" otherpara=\"column:operateTime\" transmethod=\"weaver.splitepage.transform.SptmForCowork.combineDateTime\" /><col width=\"10%\" text=\"" + SystemEnv.getHtmlLabelName(99, paramUser.getLanguage()) + "\" column=\"operateUserId\" orderkey=\"operateUserId\" transmethod=\"weaver.splitepage.transform.SptmForCowork.getResourceNameLink\"/><col width=\"15%\" text=\"" + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "\" column=\"operateType\" orderkey=\"operateType\"  otherpara=\"" + paramUser.getLanguage() + "\" transmethod=\"weaver.splitepage.transform.SptmForCowork.getTypeName\" /><col width=\"15%\" text=\"" + SystemEnv.getHtmlLabelName(101, paramUser.getLanguage()) + "\" column=\"lableId\"  orderkey=\"lableId\" otherpara=\"" + paramUser.getLanguage() + "\" transmethod=\"weaver.splitepage.transform.SptmForCowork.getItemLableName\"/><col width=\"20%\" text=\"" + SystemEnv.getHtmlLabelName(106, paramUser.getLanguage()) + "\" column=\"relatedName\" orderkey=\"relatedName\"/><col width=\"15%\" text=\"" + SystemEnv.getHtmlLabelName(17484, paramUser.getLanguage()) + "\" column=\"clientAddress\" orderkey=\"clientAddress\"/></head></table>";
/*     */ 
/*     */     
/* 287 */     String str15 = CptTableType.SEARCH_CPTLOG_TABLE.getPageUid() + "_" + Util.getEncrypt(Util.getRandom());
/* 288 */     Util_TableMap.setVal(str15, str12);
/* 289 */     hashMap.put("sessionkey", str15);
/* 290 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/service/CptMendService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */