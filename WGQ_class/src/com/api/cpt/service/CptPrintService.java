/*     */ package com.api.cpt.service;
/*     */ import com.api.cpt.util.CapitalAssortment;
/*     */ import com.api.cpt.util.SearchConditionUtil;
/*     */ import java.io.File;
/*     */ import java.io.Writer;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import oracle.sql.CLOB;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.WeaverConnection;
/*     */ import weaver.cpt.util.CptCardGroupComInfo;
/*     */ import weaver.cpt.util.CptFieldComInfo;
/*     */ import weaver.cpt.util.PrintUtil;
/*     */ import weaver.docs.docs.DocManager;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class CptPrintService extends BaseService {
/*  35 */   int id = -1;
/*  36 */   int subcompanyid = 0;
/*  37 */   String mouldname = "";
/*  38 */   String lastModTime = "";
/*  39 */   String mouldtext = "";
/*     */   
/*     */   public Map<String, Object> printView(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  42 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  44 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("customerids"));
/*  45 */     String str2 = "";
/*  46 */     byte b = -1;
/*  47 */     RecordSet recordSet = new RecordSet();
/*  48 */     recordSet.executeSql("select mouldtext from CPT_PRINT_Mould where id=" + b);
/*  49 */     if (recordSet.next()) {
/*  50 */       str2 = recordSet.getString("mouldtext");
/*     */     }
/*  52 */     PrintUtil printUtil = new PrintUtil();
/*  53 */     hashMap.put("printView", printUtil.parse(str2, user, str1, paramHttpServletRequest));
/*  54 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getBaseInfo(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  58 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  59 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/*  60 */     RecordSet recordSet = new RecordSet();
/*  61 */     String str1 = "";
/*     */     
/*  63 */     CapitalAssortment capitalAssortment = new CapitalAssortment();
/*  64 */     Map map1 = capitalAssortment.getTree(user, map, "");
/*  65 */     String str2 = Util.null2String(map.get("hasChild"));
/*  66 */     String str3 = Util.null2String(map.get("method"));
/*  67 */     String str4 = Util.null2String(map.get("pid"));
/*  68 */     String str5 = Util.null2String(map.get("assortMentId"));
/*     */ 
/*     */ 
/*     */     
/*  72 */     ArrayList arrayList1 = new ArrayList();
/*     */ 
/*     */     
/*  75 */     int i = 0;
/*  76 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  77 */     if ("assort".equals(str3) && !"".equals(str5)) {
/*     */       
/*  79 */       str1 = "select count(*) as cpt1 from CptCapital where isdata = 1 and (capitalgroupid = " + str5 + " or topAssortmentid = " + str5 + ")";
/*  80 */       recordSet.execute(str1);
/*  81 */       if (recordSet.next()) {
/*  82 */         i = Integer.parseInt(recordSet.getString("cpt1"));
/*  83 */         hashMap1.put("cpt1", "" + i);
/*     */       } 
/*     */       
/*  86 */       str1 = "select count(*) as cpt2 from CptCapital where isdata = 2 and (capitalgroupid = " + str5 + " or topAssortmentid = " + str5 + ")";
/*     */       
/*  88 */       recordSet.execute(str1);
/*  89 */       if (recordSet.next()) {
/*  90 */         hashMap1.put("cpt2", recordSet.getString("cpt2"));
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  96 */     ArrayList arrayList2 = new ArrayList();
/*     */ 
/*     */     
/*  99 */     List list = (new SearchConditionUtil()).getCondition("CptPrint", user);
/*     */ 
/*     */     
/* 102 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 103 */     hashMap2.put("pagetitle", SystemEnv.getHtmlLabelName(16617, user.getLanguage()));
/* 104 */     hashMap2.put("treedata", map1);
/* 105 */     hashMap2.put("countcfg", null);
/* 106 */     hashMap2.put("groupinfo", arrayList2);
/* 107 */     hashMap2.put("conditioninfo", list);
/* 108 */     hashMap2.put("topTabCount", hashMap1);
/* 109 */     return (Map)hashMap2;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getPrintSet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 113 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 114 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/* 115 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 117 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 119 */     byte b1 = -1;
/* 120 */     byte b2 = -1;
/* 121 */     String str = "";
/* 122 */     int i = 1;
/* 123 */     int j = 1;
/* 124 */     int k = 0;
/* 125 */     int m = 0;
/* 126 */     recordSet.execute("select * from CPT_PRINT_SET where id=-1");
/* 127 */     if (recordSet.next()) {
/* 128 */       str = recordSet.getString("forcepage");
/* 129 */       i = recordSet.getInt("row1");
/* 130 */       j = recordSet.getInt("col");
/*     */     } 
/* 132 */     if (i < 1) i = 1; 
/* 133 */     if (j < 1) j = 1; 
/* 134 */     if (j > 8) {
/* 135 */       m = j;
/* 136 */       j = -1;
/*     */     } 
/* 138 */     if (i > 5) {
/* 139 */       k = i;
/* 140 */       i = -1;
/*     */     } 
/* 142 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 143 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*     */     
/*     */     byte b;
/* 146 */     for (b = 1; b < 9; b++) {
/* 147 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 148 */       hashMap.put("key", b + "");
/* 149 */       if (b == j) {
/* 150 */         hashMap.put("selected", Boolean.valueOf(true));
/*     */       } else {
/* 152 */         hashMap.put("selected", Boolean.valueOf(false));
/*     */       } 
/* 154 */       hashMap.put("showname", b + "");
/* 155 */       arrayList1.add(hashMap);
/*     */     } 
/* 157 */     for (b = 1; b < 6; b++) {
/* 158 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 159 */       hashMap.put("key", b + "");
/* 160 */       if (b == i) {
/* 161 */         hashMap.put("selected", Boolean.valueOf(true));
/*     */       } else {
/* 163 */         hashMap.put("selected", Boolean.valueOf(false));
/*     */       } 
/* 165 */       hashMap.put("showname", b + "");
/* 166 */       arrayList2.add(hashMap);
/*     */     } 
/*     */     
/* 169 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 170 */     hashMap2.put("key", "-1");
/* 171 */     if (m > 0) {
/* 172 */       hashMap2.put("selected", Boolean.valueOf(true));
/*     */     } else {
/* 174 */       hashMap2.put("selected", Boolean.valueOf(false));
/*     */     } 
/* 176 */     hashMap2.put("showname", SystemEnv.getHtmlLabelName(19516, user.getLanguage()));
/* 177 */     arrayList1.add(hashMap2);
/*     */ 
/*     */     
/* 180 */     hashMap2 = new HashMap<>();
/* 181 */     hashMap2.put("key", "-1");
/* 182 */     if (k > 0) {
/* 183 */       hashMap2.put("selected", Boolean.valueOf(true));
/*     */     } else {
/* 185 */       hashMap2.put("selected", Boolean.valueOf(false));
/*     */     } 
/* 187 */     hashMap2.put("showname", SystemEnv.getHtmlLabelName(19516, user.getLanguage()));
/* 188 */     arrayList2.add(hashMap2);
/*     */     
/* 190 */     hashMap1.put("forcepage", str);
/* 191 */     hashMap1.put("colOptions", arrayList1);
/* 192 */     hashMap1.put("rowOptions", arrayList2);
/* 193 */     hashMap1.put("col", j + "");
/* 194 */     hashMap1.put("row", i + "");
/* 195 */     hashMap1.put("defCol", m + "");
/* 196 */     hashMap1.put("defRow", k + "");
/*     */     
/* 198 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public Map<String, Object> savePrintSet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 202 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 203 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/* 204 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 205 */     RecordSet recordSet = new RecordSet();
/* 206 */     if (!HrmUserVarify.checkUserRight("Cpt:LabelPrint", user)) {
/*     */       
/* 208 */       hashMap.put("msgid", "-1");
/* 209 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(503123, user.getLanguage()));
/* 210 */       return (Map)hashMap;
/*     */     } 
/* 212 */     if (map.get("defcol") != null) {
/* 213 */       int i = Util.getIntValue(Util.null2String(map.get("defcol")), -1);
/* 214 */       int j = Util.getIntValue(Util.null2String(map.get("defrow")), -1);
/* 215 */       int k = Util.getIntValue(Util.null2String(map.get("col")), -1);
/* 216 */       int m = Util.getIntValue(Util.null2String(map.get("row")), -1);
/* 217 */       String str = Util.null2String(map.get("forcepage"));
/*     */       
/* 219 */       if (i > 8 && k == -1) {
/* 220 */         k = i;
/*     */       }
/* 222 */       if (j > 5 && m == -1) {
/* 223 */         m = j;
/*     */       }
/* 225 */       if (k == -1) {
/* 226 */         k = 1;
/*     */       }
/* 228 */       if (m == -1) {
/* 229 */         m = 1;
/*     */       }
/*     */       
/* 232 */       recordSet.execute("update CPT_PRINT_SET set forcepage='" + str + "',col='" + k + "',row1='" + m + "' where id=-1");
/*     */     } 
/* 234 */     hashMap.put("msgid", "0");
/* 235 */     hashMap.put("msg", SystemEnv.getHtmlLabelName(18758, Util.getIntValue(user.getLanguage())));
/* 236 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getPrintModule(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws JSONException {
/* 240 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 241 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/* 242 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 243 */     RecordSet recordSet = new RecordSet();
/* 244 */     CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 245 */     TreeMap treeMap = cptFieldComInfo.getGroupFieldMap();
/* 246 */     CptCardGroupComInfo cptCardGroupComInfo = new CptCardGroupComInfo();
/* 247 */     cptCardGroupComInfo.setTofirstRow();
/* 248 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 249 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 250 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 251 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*     */ 
/*     */     
/* 254 */     hashMap2.put("key", "barcode");
/* 255 */     hashMap2.put("showname", SystemEnv.getHtmlLabelName(1362, user.getLanguage()));
/* 256 */     hashMap2.put("selected", Boolean.valueOf(true));
/* 257 */     arrayList1.add(hashMap2);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 265 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 266 */     hashMap4.put("showname", SystemEnv.getHtmlLabelName(126575, user.getLanguage()));
/* 267 */     hashMap4.put("key", "#[1d-barcode]");
/*     */     
/* 269 */     arrayList2.add(hashMap4);
/*     */     
/* 271 */     hashMap4 = new HashMap<>();
/* 272 */     hashMap4.put("showname", SystemEnv.getHtmlLabelName(126576, user.getLanguage()));
/* 273 */     hashMap4.put("key", "#[2d-barcode]");
/*     */     
/* 275 */     arrayList2.add(hashMap4);
/*     */     
/* 277 */     hashMap3.put("barcode", arrayList2);
/* 278 */     while (cptCardGroupComInfo.next()) {
/* 279 */       hashMap4 = new HashMap<>();
/* 280 */       String str1 = cptCardGroupComInfo.getGroupid();
/* 281 */       TreeMap treeMap1 = (TreeMap)treeMap.get(str1);
/* 282 */       if (treeMap1 == null || treeMap1.size() == 0) {
/*     */         continue;
/*     */       }
/* 285 */       int i = Util.getIntValue(cptCardGroupComInfo.getLabel(), -1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 291 */       hashMap2 = new HashMap<>();
/* 292 */       hashMap2.put("key", "_key" + i);
/* 293 */       hashMap2.put("showname", SystemEnv.getHtmlLabelName(i, user.getLanguage()));
/* 294 */       hashMap2.put("selected", Boolean.valueOf(false));
/* 295 */       arrayList1.add(hashMap2);
/*     */       
/* 297 */       arrayList2 = new ArrayList<>();
/* 298 */       if (!treeMap1.isEmpty()) {
/* 299 */         Iterator<Map.Entry> iterator = treeMap1.entrySet().iterator();
/* 300 */         while (iterator.hasNext()) {
/* 301 */           hashMap4 = new HashMap<>();
/* 302 */           Map.Entry entry = iterator.next();
/* 303 */           String str2 = (String)entry.getKey();
/* 304 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/* 305 */           int j = jSONObject.getInt("fieldlabel");
/* 306 */           String str3 = jSONObject.getString("fieldname");
/* 307 */           if ("barcode".equalsIgnoreCase(str3)) {
/*     */             continue;
/*     */           }
/* 310 */           hashMap4.put("key", "#[" + str3 + "]");
/* 311 */           hashMap4.put("showname", SystemEnv.getHtmlLabelName(j, user.getLanguage()));
/*     */           
/* 313 */           arrayList2.add(hashMap4);
/*     */         } 
/* 315 */         hashMap3.put("_key" + i, arrayList2);
/*     */       } 
/*     */     } 
/* 318 */     String str = "";
/* 319 */     recordSet.execute("select * from CPT_PRINT_Mould where id=-1");
/* 320 */     if (recordSet.next()) {
/* 321 */       str = recordSet.getString("mouldtext");
/*     */     }
/* 323 */     hashMap1.put("selectOptions", arrayList1);
/* 324 */     hashMap1.put("allOptions", hashMap3);
/*     */     
/* 326 */     hashMap1.put("mouldtext", str);
/* 327 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public Map<String, Object> saveMouldText(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 331 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 332 */     Map<String, Object> map = getRequestParams(paramHttpServletRequest, paramHttpServletResponse);
/* 333 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 334 */     RecordSet recordSet1 = new RecordSet();
/*     */ 
/*     */     
/* 337 */     FileUpload fileUpload = new FileUpload(paramHttpServletRequest);
/* 338 */     int i = user.getLanguage();
/* 339 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 341 */     this.id = -1;
/* 342 */     this.mouldname = fileUpload.getParameter("mouldname");
/* 343 */     this.mouldtext = Util.fromBaseEncoding(fileUpload.getParameter("mouldtext"), i);
/* 344 */     int j = -1;
/* 345 */     if ((j = this.mouldtext.lastIndexOf("<p><br/></p><p><br/></p><p><br/></p><p><br/></p>")) > -1) {
/* 346 */       this.mouldtext = this.mouldtext.substring(0, j);
/*     */     }
/* 348 */     int k = this.mouldtext.indexOf("/weaver/weaver.file.FileDownload?fileid=");
/* 349 */     while (k != -1) {
/* 350 */       int i1 = this.mouldtext.lastIndexOf("\"", k);
/* 351 */       String str = this.mouldtext.substring(0, i1 + 1);
/* 352 */       str = str + this.mouldtext.substring(k);
/* 353 */       this.mouldtext = str;
/* 354 */       k = this.mouldtext.indexOf("/weaver/weaver.file.FileDownload?fileid=", k + 1);
/*     */     } 
/*     */ 
/*     */     
/* 358 */     int m = Util.getIntValue(fileUpload
/* 359 */         .getParameter("olddocimagesnum"), 0); int n;
/* 360 */     for (n = 0; n < m; n++) {
/* 361 */       String str1 = Util.null2String(fileUpload.getParameter("olddocimages" + n));
/*     */       
/* 363 */       String str2 = "/weaver/weaver.file.FileDownload?fileid=" + str1 + "\"";
/*     */       
/* 365 */       if (this.mouldtext.indexOf(str2) == -1) {
/* 366 */         String str = "" + str1;
/* 367 */         recordSet2.executeProc("imagefile_DeleteByDoc", str);
/* 368 */         if (recordSet2.next()) {
/*     */           
/* 370 */           String str3 = Util.null2String(recordSet2.getString("filerealpath"));
/* 371 */           if (!str3.equals("")) {
/*     */             
/*     */             try {
/* 374 */               File file = new File(new String(str3.getBytes("ISO8859_1"), "UTF-8"));
/* 375 */               file.delete();
/* 376 */             } catch (Exception exception) {}
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 383 */     n = Util.getIntValue(fileUpload
/* 384 */         .getParameter("docimages_num"), 0);
/* 385 */     String[] arrayOfString1 = new String[n];
/*     */     
/* 387 */     for (byte b1 = 0; b1 < n; b1++) {
/* 388 */       arrayOfString1[b1] = "docimages_" + b1;
/*     */     }
/*     */ 
/*     */     
/* 392 */     String[] arrayOfString3 = fileUpload.uploadFiles(arrayOfString1);
/* 393 */     String[] arrayOfString2 = fileUpload.getFileNames();
/*     */     
/* 395 */     for (byte b2 = 0; b2 < n; b2++) {
/* 396 */       int i1 = this.mouldtext.indexOf(DocManager.getImgAltFlag(b2));
/* 397 */       if (i1 != -1) {
/* 398 */         String str = this.mouldtext.substring(0, i1);
/* 399 */         str = str + " alt=\"" + arrayOfString2[b2] + "\" ";
/* 400 */         i1 = this.mouldtext.indexOf("src=\"", i1);
/* 401 */         int i2 = this.mouldtext.indexOf("\"", i1 + 5);
/*     */         
/* 403 */         str = str + "src=\"/weaver/weaver.file.FileDownload?fileid=" + Util.getFileidOut(arrayOfString3[b2]);
/* 404 */         str = str + "\"";
/* 405 */         str = str + this.mouldtext.substring(i2 + 1);
/* 406 */         this.mouldtext = str;
/*     */       } else {
/* 408 */         String str = "" + arrayOfString3[b2];
/* 409 */         recordSet2.executeProc("imagefile_DeleteByDoc", str);
/* 410 */         if (recordSet2.next()) {
/*     */           
/* 412 */           String str1 = Util.null2String(recordSet2.getString("filerealpath"));
/* 413 */           if (!str1.equals("")) {
/*     */             
/*     */             try {
/* 416 */               File file = new File(new String(str1.getBytes("ISO8859_1"), "UTF-8"));
/* 417 */               file.delete();
/* 418 */             } catch (Exception exception) {}
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*     */     try {
/* 425 */       EditMouldInfo();
/* 426 */     } catch (Exception exception) {
/* 427 */       hashMap.put("msgid", "-1");
/* 428 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(503123, user.getLanguage()));
/* 429 */       return (Map)hashMap;
/*     */     } 
/* 431 */     hashMap.put("msgid", "0");
/* 432 */     hashMap.put("msg", SystemEnv.getHtmlLabelName(18758, Util.getIntValue(user.getLanguage())));
/* 433 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private void EditMouldInfo() throws Exception {
/* 437 */     String str = "";
/* 438 */     RecordSet recordSet = new RecordSet();
/* 439 */     boolean bool = (recordSet.getDBType().equals("oracle") && Util.null2String(recordSet.getOrgindbtype()).equals("oracle")) ? true : false;
/* 440 */     if (bool) {
/* 441 */       WeaverConnection weaverConnection = null;
/* 442 */       PreparedStatement preparedStatement = null;
/* 443 */       ResultSet resultSet = null;
/*     */       try {
/* 445 */         str = "update CPT_PRINT_Mould set  mouldname='" + this.mouldname + "',lastModTime='" + this.lastModTime + "'" + ((this.subcompanyid != 0) ? (",subcompanyid='" + this.subcompanyid + "'") : "") + ",mouldtext=empty_clob() where id='" + this.id + "'";
/* 446 */         (new RecordSet()).executeSql(str);
/*     */         
/* 448 */         weaverConnection = ConnectionPool.getInstance().getConnection();
/* 449 */         weaverConnection.setAutoCommit(false);
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 454 */         preparedStatement = weaverConnection.prepareStatement("select mouldtext from CPT_PRINT_Mould where id='-1' for update");
/* 455 */         resultSet = preparedStatement.executeQuery();
/* 456 */         if (resultSet.next()) {
/* 457 */           CLOB cLOB = (CLOB)resultSet.getClob("mouldtext");
/* 458 */           Writer writer = cLOB.getCharacterOutputStream();
/* 459 */           char[] arrayOfChar = this.mouldtext.toCharArray();
/* 460 */           writer.write(arrayOfChar, 0, arrayOfChar.length);
/* 461 */           writer.flush();
/* 462 */           writer.close();
/*     */         } 
/* 464 */         weaverConnection.commit();
/*     */       }
/* 466 */       catch (Exception exception) {
/* 467 */         (new BaseBean()).writeLog(exception.getMessage());
/*     */       } finally {
/* 469 */         if (resultSet != null) {
/*     */           try {
/* 471 */             resultSet.close();
/* 472 */           } catch (SQLException sQLException) {
/* 473 */             sQLException.printStackTrace();
/*     */           } 
/*     */         }
/* 476 */         if (preparedStatement != null) {
/*     */           try {
/* 478 */             preparedStatement.close();
/* 479 */           } catch (SQLException sQLException) {
/* 480 */             sQLException.printStackTrace();
/*     */           } 
/*     */         }
/* 483 */         if (weaverConnection != null) {
/*     */           try {
/* 485 */             weaverConnection.close();
/* 486 */           } catch (SQLException sQLException) {
/* 487 */             sQLException.printStackTrace();
/*     */           } 
/*     */         }
/*     */       } 
/*     */     } else {
/* 492 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 494 */         str = "update CPT_PRINT_Mould set mouldname=?,mouldtext=?,lastModTime=?" + ((this.subcompanyid != 0) ? ",subcompanyid=?" : "") + " where id=?";
/* 495 */         connStatement.setStatementSql(str);
/* 496 */         connStatement.setString(1, this.mouldname);
/* 497 */         connStatement.setString(2, this.mouldtext);
/* 498 */         connStatement.setString(3, this.lastModTime);
/* 499 */         if (this.subcompanyid != 0) {
/* 500 */           connStatement.setInt(4, this.subcompanyid);
/* 501 */           connStatement.setInt(5, this.id);
/*     */         } else {
/* 503 */           connStatement.setInt(4, this.id);
/*     */         } 
/*     */         
/* 506 */         connStatement.executeUpdate();
/*     */       }
/* 508 */       catch (Exception exception) {
/* 509 */         (new BaseBean()).writeLog(exception.getMessage());
/*     */       } finally {
/*     */         try {
/* 512 */           connStatement.close();
/* 513 */         } catch (Exception exception) {}
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/service/CptPrintService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */