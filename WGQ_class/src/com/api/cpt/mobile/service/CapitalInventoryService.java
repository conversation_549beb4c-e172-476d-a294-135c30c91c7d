package com.api.cpt.mobile.service;

import java.util.Map;
import weaver.hrm.User;

public interface CapitalInventoryService {
  Map<String, Object> getMyInventoryList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getInventoryListDetail(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getInventoryForm(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doInventory(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doSubmitInventory(Map<String, Object> paramMap, User paramUser);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/service/CapitalInventoryService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */