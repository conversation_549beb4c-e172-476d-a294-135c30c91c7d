package com.api.cpt.mobile.service;

import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import weaver.hrm.User;

public interface CapitalManagerService {
  Map<String, Object> getCptManagerForm(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doCptIfOverAjax(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> scanQRCodeCapital(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doCptManager(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getCptLinkage(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getCapitalForm(Map<String, Object> paramMap, User paramUser, HttpServletRequest paramHttpServletRequest);
  
  Map<String, Object> getCapitalEditForm(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doCapitalEdit(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getCapitalFlowList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getCapitalChangeList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getCapitalMendForm(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doCapitalMend(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doCapitalDel(Map<String, Object> paramMap, User paramUser);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/service/CapitalManagerService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */