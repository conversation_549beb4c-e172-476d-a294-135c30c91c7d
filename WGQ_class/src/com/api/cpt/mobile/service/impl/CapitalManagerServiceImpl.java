/*    */ package com.api.cpt.mobile.service.impl;
/*    */ 
/*    */ import com.api.cpt.mobile.cmd.manager.DoCapitalDelCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.DoCapitalEditCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.DoCapitalMendCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.DoCptIfOverAjaxCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.DoCptManagerCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.GetCapitalEditFormCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.GetCapitalFormCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.GetCapitalMendFormCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.GetCptLinkageCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.GetCptManagerFormCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.GetCptchangeListCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.GetCptflowListCmd;
/*    */ import com.api.cpt.mobile.cmd.manager.ScanQRCodeCapitalCmd;
/*    */ import com.api.cpt.mobile.service.CapitalManagerService;
/*    */ import com.engine.core.impl.Service;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CapitalManagerServiceImpl
/*    */   extends Service
/*    */   implements CapitalManagerService
/*    */ {
/*    */   public Map<String, Object> getCptManagerForm(Map<String, Object> paramMap, User paramUser) {
/* 34 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCptManagerFormCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doCptIfOverAjax(Map<String, Object> paramMap, User paramUser) {
/* 39 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoCptIfOverAjaxCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> scanQRCodeCapital(Map<String, Object> paramMap, User paramUser) {
/* 44 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new ScanQRCodeCapitalCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doCptManager(Map<String, Object> paramMap, User paramUser) {
/* 49 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoCptManagerCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCptLinkage(Map<String, Object> paramMap, User paramUser) {
/* 54 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCptLinkageCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCapitalForm(Map<String, Object> paramMap, User paramUser, HttpServletRequest paramHttpServletRequest) {
/* 59 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCapitalFormCmd(paramMap, paramUser, paramHttpServletRequest));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCapitalEditForm(Map<String, Object> paramMap, User paramUser) {
/* 64 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCapitalEditFormCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doCapitalEdit(Map<String, Object> paramMap, User paramUser) {
/* 69 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoCapitalEditCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCapitalFlowList(Map<String, Object> paramMap, User paramUser) {
/* 74 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCptflowListCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCapitalChangeList(Map<String, Object> paramMap, User paramUser) {
/* 79 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCptchangeListCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCapitalMendForm(Map<String, Object> paramMap, User paramUser) {
/* 84 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCapitalMendFormCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doCapitalMend(Map<String, Object> paramMap, User paramUser) {
/* 89 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoCapitalMendCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doCapitalDel(Map<String, Object> paramMap, User paramUser) {
/* 94 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoCapitalDelCmd(paramMap, paramUser));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/service/impl/CapitalManagerServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */