/*    */ package com.api.cpt.mobile.service.impl;
/*    */ 
/*    */ import com.api.cpt.mobile.cmd.portal.DoInstockCapitalCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.DoInstockCheckCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.DoInstockDeleteCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetCapitalListCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetCptMenuRightCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetInstockDetailCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetInstockListCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetInstockListHisCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetListSearchConditionCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetPortalCountCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetPortalInstockCmd;
/*    */ import com.api.cpt.mobile.cmd.portal.GetPortalMyCapitalCmd;
/*    */ import com.api.cpt.mobile.service.CapitalPortalService;
/*    */ import com.engine.core.impl.Service;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CapitalPortalServiceImpl
/*    */   extends Service
/*    */   implements CapitalPortalService
/*    */ {
/*    */   public Map<String, Object> getPortalInstock(Map<String, Object> paramMap, User paramUser) {
/* 31 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetPortalInstockCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getPortalMyCapital(Map<String, Object> paramMap, User paramUser) {
/* 36 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetPortalMyCapitalCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getPortalCount(Map<String, Object> paramMap, User paramUser) {
/* 41 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetPortalCountCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCapitalList(Map<String, Object> paramMap, User paramUser) {
/* 46 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCapitalListCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getListSearchCondition(Map<String, Object> paramMap, User paramUser) {
/* 51 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetListSearchConditionCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getInstockList(Map<String, Object> paramMap, User paramUser) {
/* 56 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetInstockListCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getInstockListHis(Map<String, Object> paramMap, User paramUser) {
/* 61 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetInstockListHisCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getInstockDetail(Map<String, Object> paramMap, User paramUser) {
/* 66 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetInstockDetailCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doInstockDelete(Map<String, Object> paramMap, User paramUser) {
/* 71 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoInstockDeleteCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doInstockCheck(Map<String, Object> paramMap, User paramUser) {
/* 76 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoInstockCheckCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doInstockCapital(Map<String, Object> paramMap, User paramUser) {
/* 81 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoInstockCapitalCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getCptMenuRight(Map<String, Object> paramMap, User paramUser) {
/* 86 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCptMenuRightCmd(paramMap, paramUser));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/service/impl/CapitalPortalServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */