/*    */ package com.api.cpt.mobile.service.impl;
/*    */ 
/*    */ import com.api.cpt.mobile.cmd.report.GetCapitalReportDataCmd;
/*    */ import com.api.cpt.mobile.service.CapitalReportService;
/*    */ import com.engine.core.impl.Service;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CapitalReportServiceImpl
/*    */   extends Service
/*    */   implements CapitalReportService
/*    */ {
/*    */   public Map<String, Object> getCapitalReportData(Map<String, Object> paramMap, User paramUser) {
/* 20 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetCapitalReportDataCmd(paramMap, paramUser));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/service/impl/CapitalReportServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */