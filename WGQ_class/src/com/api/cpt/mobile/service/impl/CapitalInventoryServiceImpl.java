/*    */ package com.api.cpt.mobile.service.impl;
/*    */ 
/*    */ import com.api.cpt.mobile.cmd.inventory.DoInventoryCmd;
/*    */ import com.api.cpt.mobile.cmd.inventory.DoSubmitInventoryCmd;
/*    */ import com.api.cpt.mobile.cmd.inventory.GetInventoryFormCmd;
/*    */ import com.api.cpt.mobile.cmd.inventory.GetInventoryListDetailCmd;
/*    */ import com.api.cpt.mobile.cmd.inventory.GetMyInventoryListCmd;
/*    */ import com.api.cpt.mobile.service.CapitalInventoryService;
/*    */ import com.engine.core.impl.Service;
/*    */ import com.engine.core.interceptor.Command;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CapitalInventoryServiceImpl
/*    */   extends Service
/*    */   implements CapitalInventoryService
/*    */ {
/*    */   public Map<String, Object> getMyInventoryList(Map<String, Object> paramMap, User paramUser) {
/* 24 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetMyInventoryListCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getInventoryListDetail(Map<String, Object> paramMap, User paramUser) {
/* 29 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetInventoryListDetailCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getInventoryForm(Map<String, Object> paramMap, User paramUser) {
/* 34 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new GetInventoryFormCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doInventory(Map<String, Object> paramMap, User paramUser) {
/* 39 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoInventoryCmd(paramMap, paramUser));
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> doSubmitInventory(Map<String, Object> paramMap, User paramUser) {
/* 44 */     return (Map<String, Object>)this.commandExecutor.execute((Command)new DoSubmitInventoryCmd(paramMap, paramUser));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/service/impl/CapitalInventoryServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */