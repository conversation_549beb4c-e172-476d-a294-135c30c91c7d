package com.api.cpt.mobile.service;

import java.util.Map;
import weaver.hrm.User;

public interface CapitalPortalService {
  Map<String, Object> getPortalInstock(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getPortalMyCapital(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getPortalCount(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getCapitalList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getListSearchCondition(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getInstockList(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getInstockListHis(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getInstockDetail(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doInstockDelete(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doInstockCheck(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> doInstockCapital(Map<String, Object> paramMap, User paramUser);
  
  Map<String, Object> getCptMenuRight(Map<String, Object> paramMap, User paramUser);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/service/CapitalPortalService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */