/*     */ package com.api.cpt.mobile.util;
/*     */ 
/*     */ import com.api.cpt.util.CptFormItemUtil;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ManagerFormItemUtil
/*     */ {
/*     */   public static Map<String, Object> getCptManagerForm(String paramString, User paramUser, Map<String, Object> paramMap) {
/*  39 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  40 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  41 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  42 */     XssUtil xssUtil = new XssUtil();
/*  43 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  44 */     String str1 = "";
/*  45 */     String str2 = "" + paramUser.getUID();
/*  46 */     String str3 = DateHelper.getCurrentDate();
/*     */     
/*  48 */     if ("cptuse".equals(paramString)) {
/*  49 */       if (!HrmUserVarify.checkUserRight("CptCapital:Use", paramUser)) {
/*  50 */         hashMap1.put("isright", Boolean.valueOf(false));
/*  51 */         return (Map)hashMap1;
/*     */       } 
/*  53 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/*  55 */       str1 = SystemEnv.getHtmlLabelName(886, Util.getIntValue(paramUser.getLanguage()));
/*     */       
/*  57 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/*  59 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  60 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/*  61 */       hashMap.put("cptstateid", "1");
/*  62 */       hashMap.put("inculdeNumZero", "0");
/*  63 */       hashMap.put("mtype", paramString);
/*     */       
/*  65 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/*  66 */       ArrayList<String> arrayList2 = new ArrayList();
/*  67 */       arrayList2.add("capitalspec");
/*  68 */       arrayList2.add("mark");
/*  69 */       arrayList2.add("availablenum");
/*  70 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(15312, Util.getIntValue(paramUser.getLanguage())), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/*  71 */       map.put("viewAttr", "3");
/*  72 */       map.put("linkUrl", str);
/*  73 */       map.put("linkKey", arrayList2);
/*  74 */       arrayList1.add(map);
/*     */       
/*  76 */       map = CptFormItemUtil.getFormItemForDate("stockindate", SystemEnv.getHtmlLabelName(1412, Util.getIntValue(paramUser.getLanguage())), str3, 2);
/*  77 */       arrayList1.add(map);
/*  78 */       map = CptFormItemUtil.getFormItemForBrowser("hrmid", SystemEnv.getHtmlLabelName(368, Util.getIntValue(paramUser.getLanguage())), "1", str2, 3, "", null, null);
/*  79 */       map.put("viewAttr", "3");
/*  80 */       arrayList1.add(map);
/*  81 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("capitalspec")), 200, 1);
/*  82 */       arrayList1.add(map);
/*  83 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 1);
/*  84 */       arrayList1.add(map);
/*  85 */       map = CptFormItemUtil.getFormItemForInputNumber("availablenum", SystemEnv.getHtmlLabelName(1446, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("availablenum")), 1, "", "0.1", 2, "1");
/*  86 */       arrayList1.add(map);
/*  87 */       map = CptFormItemUtil.getFormItemForInputNumber("capitalnum", SystemEnv.getHtmlLabelName(15313, Util.getIntValue(paramUser.getLanguage())), "", 3, "", "0.1", 2, "1");
/*  88 */       arrayList1.add(map);
/*  89 */       map = CptFormItemUtil.getFormItemForInput("location", SystemEnv.getHtmlLabelName(1387, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/*  90 */       arrayList1.add(map);
/*  91 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/*  92 */       arrayList1.add(map);
/*     */       
/*  94 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(886, Util.getIntValue(paramUser.getLanguage())));
/*  95 */       hashMap2.put("items", arrayList1);
/*  96 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  97 */       arrayList.add(hashMap2);
/*  98 */     } else if ("cptmove".equals(paramString)) {
/*  99 */       if (!HrmUserVarify.checkUserRight("CptCapital:MoveIn", paramUser)) {
/* 100 */         hashMap1.put("isright", Boolean.valueOf(false));
/* 101 */         return (Map)hashMap1;
/*     */       } 
/* 103 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/* 105 */       str1 = SystemEnv.getHtmlLabelName(883, Util.getIntValue(paramUser.getLanguage()));
/* 106 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/* 108 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 110 */       hashMap.put("cptstateid", "1,2");
/* 111 */       hashMap.put("inculdeNumZero", "0");
/* 112 */       hashMap.put("mtype", paramString);
/*     */       
/* 114 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/* 115 */       ArrayList<String> arrayList2 = new ArrayList();
/* 116 */       arrayList2.add("blongdepartmentname");
/* 117 */       arrayList2.add("resourcename");
/* 118 */       arrayList2.add("capitalspec");
/* 119 */       arrayList2.add("mark");
/* 120 */       arrayList2.add("availablenum");
/* 121 */       arrayList2.add("warehousename");
/* 122 */       arrayList2.add("warehouse");
/* 123 */       arrayList2.add("warehousespan");
/*     */       
/* 125 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(15309, Util.getIntValue(paramUser.getLanguage())), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/* 126 */       map.put("viewAttr", "3");
/* 127 */       map.put("linkUrl", str);
/* 128 */       map.put("linkKey", arrayList2);
/* 129 */       arrayList1.add(map);
/*     */       
/* 131 */       map = CptFormItemUtil.getFormItemForBrowser("hrmid", SystemEnv.getHtmlLabelName(15310, Util.getIntValue(paramUser.getLanguage())), "1", str2, 3, "", null, null);
/* 132 */       map.put("viewAttr", "3");
/* 133 */       arrayList1.add(map);
/* 134 */       if (capitalTransMethod.IsWareHouseOpen()) {
/* 135 */         map = CptFormItemUtil.getFormItemForInput("availablenum", SystemEnv.getHtmlLabelName(1331, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("availablenum")), 200, 3);
/* 136 */         arrayList1.add(map);
/*     */       } 
/* 138 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("capitalspec")), 200, 1);
/* 139 */       arrayList1.add(map);
/* 140 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 1);
/* 141 */       arrayList1.add(map);
/* 142 */       map = CptFormItemUtil.getFormItemForInput("blongdepartmentname", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("blongdepartmentname")), 200, 1);
/* 143 */       arrayList1.add(map);
/* 144 */       map = CptFormItemUtil.getFormItemForInput("resourcename", SystemEnv.getHtmlLabelName(1508, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("resourcename")), 200, 1);
/* 145 */       arrayList1.add(map);
/* 146 */       if (capitalTransMethod.IsWareHouseOpen()) {
/* 147 */         hashMap = new HashMap<>();
/* 148 */         hashMap.put("isfilter", "1");
/* 149 */         map = CptFormItemUtil.getFormItemForInput("warehousename", SystemEnv.getHtmlLabelName(515452, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("warehousename")), 200, 1);
/* 150 */         arrayList1.add(map);
/* 151 */         map = CptFormItemUtil.getFormItemForBrowser("warehouse", SystemEnv.getHtmlLabelName(515453, Util.getIntValue(paramUser.getLanguage())), "320", Util.null2String(paramMap.get("warehouse")), 3, "", null, hashMap);
/* 152 */         map.put("viewAttr", "3");
/* 153 */         arrayList1.add(map);
/*     */       } 
/* 155 */       map = CptFormItemUtil.getFormItemForInput("location", SystemEnv.getHtmlLabelName(1387, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/* 156 */       arrayList1.add(map);
/* 157 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/* 158 */       arrayList1.add(map);
/*     */       
/* 160 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(883, Util.getIntValue(paramUser.getLanguage())));
/* 161 */       hashMap2.put("items", arrayList1);
/* 162 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 163 */       arrayList.add(hashMap2);
/* 164 */     } else if ("cptlend".equals(paramString)) {
/* 165 */       if (!HrmUserVarify.checkUserRight("CptCapital:Lend", paramUser)) {
/* 166 */         hashMap1.put("isright", Boolean.valueOf(false));
/* 167 */         return (Map)hashMap1;
/*     */       } 
/* 169 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/* 171 */       str1 = SystemEnv.getHtmlLabelName(6051, Util.getIntValue(paramUser.getLanguage()));
/* 172 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/* 174 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 175 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 176 */       hashMap.put("cptstateid", "1");
/* 177 */       hashMap.put("cptsptcount", "1");
/* 178 */       hashMap.put("inculdeNumZero", "0");
/* 179 */       hashMap.put("mtype", paramString);
/*     */       
/* 181 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/* 182 */       ArrayList<String> arrayList2 = new ArrayList();
/* 183 */       arrayList2.add("blongdepartmentname");
/* 184 */       arrayList2.add("resourcename");
/* 185 */       arrayList2.add("capitalspec");
/* 186 */       arrayList2.add("mark");
/*     */       
/* 188 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(503027, paramUser.getLanguage()), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/* 189 */       map.put("viewAttr", "3");
/* 190 */       map.put("linkUrl", str);
/* 191 */       map.put("linkKey", arrayList2);
/* 192 */       arrayList1.add(map);
/*     */       
/* 194 */       map = CptFormItemUtil.getFormItemForBrowser("hrmid", SystemEnv.getHtmlLabelName(503028, paramUser.getLanguage()), "1", str2, 3, "", null, null);
/* 195 */       map.put("viewAttr", "3");
/* 196 */       arrayList1.add(map);
/* 197 */       map = CptFormItemUtil.getFormItemForDate("stockindate", SystemEnv.getHtmlLabelName(1404, paramUser.getLanguage()), str3, 2);
/* 198 */       arrayList1.add(map);
/* 199 */       map = CptFormItemUtil.getFormItemForInput("blongdepartmentname", SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()), Util.null2String(paramMap.get("blongdepartmentname")), 200, 1);
/* 200 */       arrayList1.add(map);
/* 201 */       map = CptFormItemUtil.getFormItemForInput("resourcename", SystemEnv.getHtmlLabelName(1508, paramUser.getLanguage()), Util.null2String(paramMap.get("resourcename")), 200, 1);
/* 202 */       arrayList1.add(map);
/* 203 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()), Util.null2String(paramMap.get("capitalspec")), 200, 1);
/* 204 */       arrayList1.add(map);
/* 205 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 1);
/* 206 */       arrayList1.add(map);
/* 207 */       map = CptFormItemUtil.getFormItemForInput("location", SystemEnv.getHtmlLabelName(1387, paramUser.getLanguage()), "", 200, 2);
/* 208 */       arrayList1.add(map);
/* 209 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()), "", 200, 2);
/* 210 */       arrayList1.add(map);
/*     */       
/* 212 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(6051, paramUser.getLanguage()));
/* 213 */       hashMap2.put("items", arrayList1);
/* 214 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 215 */       arrayList.add(hashMap2);
/* 216 */     } else if ("cptloss".equals(paramString)) {
/* 217 */       if (!HrmUserVarify.checkUserRight("CptCapital:Loss", paramUser)) {
/* 218 */         hashMap1.put("isright", Boolean.valueOf(false));
/* 219 */         return (Map)hashMap1;
/*     */       } 
/* 221 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/* 223 */       str1 = SystemEnv.getHtmlLabelName(6054, Util.getIntValue(paramUser.getLanguage()));
/* 224 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/* 226 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 227 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 228 */       hashMap.put("cptstateid", "0,1,2,3,4");
/* 229 */       hashMap.put("inculdeNumZero", "0");
/* 230 */       hashMap.put("mtype", paramString);
/*     */       
/* 232 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/* 233 */       ArrayList<String> arrayList2 = new ArrayList();
/* 234 */       arrayList2.add("blongdepartmentname");
/* 235 */       arrayList2.add("availablenum");
/* 236 */       arrayList2.add("capitalspec");
/* 237 */       arrayList2.add("mark");
/*     */       
/* 239 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(503031, paramUser.getLanguage()), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/* 240 */       map.put("viewAttr", "3");
/* 241 */       map.put("linkUrl", str);
/* 242 */       map.put("linkKey", arrayList2);
/* 243 */       arrayList1.add(map);
/*     */       
/* 245 */       map = CptFormItemUtil.getFormItemForBrowser("operator", SystemEnv.getHtmlLabelName(17482, Util.getIntValue(paramUser.getLanguage())), "1", str2, 2, "", null, null);
/* 246 */       map.put("viewAttr", "2");
/* 247 */       arrayList1.add(map);
/* 248 */       map = CptFormItemUtil.getFormItemForDate("stockindate", SystemEnv.getHtmlLabelName(1406, Util.getIntValue(paramUser.getLanguage())), str3, 2);
/* 249 */       arrayList1.add(map);
/* 250 */       map = CptFormItemUtil.getFormItemForInput("blongdepartmentname", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("blongdepartmentname")), 200, 1);
/* 251 */       arrayList1.add(map);
/* 252 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("capitalspec")), 200, 1);
/* 253 */       arrayList1.add(map);
/* 254 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 1);
/* 255 */       arrayList1.add(map);
/* 256 */       map = CptFormItemUtil.getFormItemForInputNumber("availablenum", SystemEnv.getHtmlLabelName(1446, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("availablenum")), 1, "", "0.1", 2, "1");
/* 257 */       arrayList1.add(map);
/* 258 */       map = CptFormItemUtil.getFormItemForInputNumber("capitalcount", SystemEnv.getHtmlLabelName(503032, Util.getIntValue(paramUser.getLanguage())), "", 3, "", "0.1", 2, "1");
/* 259 */       arrayList1.add(map);
/* 260 */       map = CptFormItemUtil.getFormItemForInputNumber("cost", SystemEnv.getHtmlLabelName(1393, Util.getIntValue(paramUser.getLanguage())), "", 2, "9999999999999.99", "0", 2, "1");
/* 261 */       arrayList1.add(map);
/* 262 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/* 263 */       arrayList1.add(map);
/*     */       
/* 265 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(6054, Util.getIntValue(paramUser.getLanguage())));
/* 266 */       hashMap2.put("items", arrayList1);
/* 267 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 268 */       arrayList.add(hashMap2);
/* 269 */     } else if ("cptdiscard".equals(paramString)) {
/* 270 */       if (!HrmUserVarify.checkUserRight("CptCapital:Discard", paramUser)) {
/* 271 */         hashMap1.put("isright", Boolean.valueOf(false));
/* 272 */         return (Map)hashMap1;
/*     */       } 
/* 274 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/* 276 */       str1 = SystemEnv.getHtmlLabelName(6052, Util.getIntValue(paramUser.getLanguage()));
/* 277 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/* 279 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 280 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 281 */       hashMap.put("cptstateid", "1,2,3,4");
/* 282 */       hashMap.put("inculdeNumZero", "0");
/* 283 */       hashMap.put("mtype", paramString);
/*     */       
/* 285 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/* 286 */       ArrayList<String> arrayList2 = new ArrayList();
/* 287 */       arrayList2.add("blongdepartmentname");
/* 288 */       arrayList2.add("availablenum");
/* 289 */       arrayList2.add("capitalspec");
/* 290 */       arrayList2.add("mark");
/*     */       
/* 292 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(21545, Util.getIntValue(paramUser.getLanguage())), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/* 293 */       map.put("viewAttr", "3");
/* 294 */       map.put("linkUrl", str);
/* 295 */       map.put("linkKey", arrayList2);
/* 296 */       arrayList1.add(map);
/*     */       
/* 298 */       map = CptFormItemUtil.getFormItemForBrowser("operator", SystemEnv.getHtmlLabelName(17482, Util.getIntValue(paramUser.getLanguage())), "1", str2, 2, "", null, null);
/* 299 */       map.put("viewAttr", "2");
/* 300 */       arrayList1.add(map);
/* 301 */       map = CptFormItemUtil.getFormItemForDate("stockindate", SystemEnv.getHtmlLabelName(1392, Util.getIntValue(paramUser.getLanguage())), str3, 2);
/* 302 */       arrayList1.add(map);
/* 303 */       map = CptFormItemUtil.getFormItemForInput("blongdepartmentname", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("blongdepartmentname")), 200, 1);
/* 304 */       arrayList1.add(map);
/* 305 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("capitalspec")), 200, 1);
/* 306 */       arrayList1.add(map);
/* 307 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 1);
/* 308 */       arrayList1.add(map);
/* 309 */       map = CptFormItemUtil.getFormItemForInputNumber("availablenum", SystemEnv.getHtmlLabelName(1446, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("availablenum")), 1, "", "0.1", 2, "1");
/* 310 */       arrayList1.add(map);
/* 311 */       map = CptFormItemUtil.getFormItemForInputNumber("capitalcount", SystemEnv.getHtmlLabelName(17273, paramUser.getLanguage()), "", 3, "", "0.1", 2, "1");
/* 312 */       arrayList1.add(map);
/*     */ 
/*     */       
/* 315 */       map = CptFormItemUtil.getFormItemForInputNumber("cost", SystemEnv.getHtmlLabelName(1393, paramUser.getLanguage()), "", 2, "9999999999999.99", "0", 2, "1");
/* 316 */       arrayList1.add(map);
/* 317 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/* 318 */       arrayList1.add(map);
/*     */       
/* 320 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(6052, Util.getIntValue(paramUser.getLanguage())));
/* 321 */       hashMap2.put("items", arrayList1);
/* 322 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 323 */       arrayList.add(hashMap2);
/* 324 */     } else if ("cptmend".equals(paramString)) {
/* 325 */       if (!HrmUserVarify.checkUserRight("CptCapital:Mend", paramUser)) {
/* 326 */         hashMap1.put("isright", Boolean.valueOf(false));
/* 327 */         return (Map)hashMap1;
/*     */       } 
/* 329 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/* 331 */       str1 = SystemEnv.getHtmlLabelName(22459, Util.getIntValue(paramUser.getLanguage()));
/* 332 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/* 334 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 335 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 336 */       hashMap.put("cptstateid", "1,2,3");
/* 337 */       hashMap.put("cptsptcount", "1");
/* 338 */       hashMap.put("inculdeNumZero", "0");
/* 339 */       hashMap.put("mtype", paramString);
/*     */       
/* 341 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/* 342 */       ArrayList<String> arrayList2 = new ArrayList();
/* 343 */       arrayList2.add("blongdepartmentname");
/* 344 */       arrayList2.add("statename");
/* 345 */       arrayList2.add("capitalspec");
/* 346 */       arrayList2.add("mark");
/*     */       
/* 348 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(33016, Util.getIntValue(paramUser.getLanguage())), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/* 349 */       map.put("viewAttr", "3");
/* 350 */       map.put("linkUrl", str);
/* 351 */       map.put("linkKey", arrayList2);
/* 352 */       arrayList1.add(map);
/*     */       
/* 354 */       map = CptFormItemUtil.getFormItemForBrowser("operator", SystemEnv.getHtmlLabelName(1047, Util.getIntValue(paramUser.getLanguage())), "1", str2, 2, "", null, null);
/* 355 */       map.put("viewAttr", "2");
/* 356 */       arrayList1.add(map);
/* 357 */       map = CptFormItemUtil.getFormItemForDate("menddate", SystemEnv.getHtmlLabelName(1409, Util.getIntValue(paramUser.getLanguage())), str3, 2);
/* 358 */       arrayList1.add(map);
/* 359 */       map = CptFormItemUtil.getFormItemForDate("mendperioddate", SystemEnv.getHtmlLabelName(22457, Util.getIntValue(paramUser.getLanguage())), "", 2);
/* 360 */       arrayList1.add(map);
/* 361 */       hashMap = new HashMap<>();
/* 362 */       hashMap.put("sqlwhere", xssUtil.put("where t1.type=2"));
/* 363 */       map = CptFormItemUtil.getFormItemForBrowser("maintaincompany", SystemEnv.getHtmlLabelName(1399, Util.getIntValue(paramUser.getLanguage())), "7", "", 2, "", null, hashMap);
/* 364 */       map.put("viewAttr", "2");
/* 365 */       arrayList1.add(map);
/* 366 */       map = CptFormItemUtil.getFormItemForInput("blongdepartmentname", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("blongdepartmentname")), 200, 1);
/* 367 */       arrayList1.add(map);
/* 368 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("capitalspec")), 200, 1);
/* 369 */       arrayList1.add(map);
/* 370 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 1);
/* 371 */       arrayList1.add(map);
/* 372 */       map = CptFormItemUtil.getFormItemForInput("statename", SystemEnv.getHtmlLabelName(602, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("statename")), 200, 1);
/* 373 */       arrayList1.add(map);
/* 374 */       map = CptFormItemUtil.getFormItemForInputNumber("cost", SystemEnv.getHtmlLabelName(1393, paramUser.getLanguage()), "", 2, "9999999999999.99", "0", 2, "1");
/* 375 */       arrayList1.add(map);
/* 376 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/* 377 */       arrayList1.add(map);
/*     */       
/* 379 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(22459, Util.getIntValue(paramUser.getLanguage())));
/* 380 */       hashMap2.put("items", arrayList1);
/* 381 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 382 */       arrayList.add(hashMap2);
/* 383 */     } else if ("cptback".equals(paramString)) {
/* 384 */       if (!HrmUserVarify.checkUserRight("CptCapital:Return", paramUser)) {
/* 385 */         hashMap1.put("isright", Boolean.valueOf(false));
/* 386 */         return (Map)hashMap1;
/*     */       } 
/* 388 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/* 390 */       str1 = SystemEnv.getHtmlLabelName(15305, Util.getIntValue(paramUser.getLanguage()));
/* 391 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/* 393 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 394 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 395 */       hashMap.put("cptstateid", "2,3,4");
/* 396 */       hashMap.put("inculdeNumZero", "0");
/* 397 */       hashMap.put("mtype", paramString);
/*     */       
/* 399 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/* 400 */       ArrayList<String> arrayList2 = new ArrayList();
/* 401 */       arrayList2.add("blongdepartmentname");
/* 402 */       arrayList2.add("statename");
/* 403 */       arrayList2.add("capitalspec");
/* 404 */       arrayList2.add("mark");
/* 405 */       arrayList2.add("resourcename");
/*     */       
/* 407 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(503034, paramUser.getLanguage()), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/* 408 */       map.put("viewAttr", "3");
/* 409 */       map.put("linkUrl", str);
/* 410 */       map.put("linkKey", arrayList2);
/* 411 */       arrayList1.add(map);
/*     */       
/* 413 */       map = CptFormItemUtil.getFormItemForDate("stockindate", SystemEnv.getHtmlLabelName(1413, Util.getIntValue(paramUser.getLanguage())), str3, 2);
/* 414 */       arrayList1.add(map);
/* 415 */       map = CptFormItemUtil.getFormItemForInput("blongdepartmentname", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("blongdepartmentname")), 200, 1);
/* 416 */       arrayList1.add(map);
/* 417 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("capitalspec")), 200, 1);
/* 418 */       arrayList1.add(map);
/* 419 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 1);
/* 420 */       arrayList1.add(map);
/* 421 */       map = CptFormItemUtil.getFormItemForInput("statename", SystemEnv.getHtmlLabelName(602, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("statename")), 200, 1);
/* 422 */       arrayList1.add(map);
/* 423 */       map = CptFormItemUtil.getFormItemForInput("resourcename", SystemEnv.getHtmlLabelName(503035, paramUser.getLanguage()), Util.null2String(paramMap.get("resourcename")), 200, 1);
/* 424 */       arrayList1.add(map);
/* 425 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), "", 200, 2);
/* 426 */       arrayList1.add(map);
/*     */       
/* 428 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(15305, Util.getIntValue(paramUser.getLanguage())));
/* 429 */       hashMap2.put("items", arrayList1);
/* 430 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 431 */       arrayList.add(hashMap2);
/* 432 */     } else if ("cptchange".equals(paramString)) {
/* 433 */       if (!HrmUserVarify.checkUserRight("CptCapital:Change", paramUser)) {
/* 434 */         hashMap1.put("isright", Boolean.valueOf(false));
/* 435 */         return (Map)hashMap1;
/*     */       } 
/* 437 */       hashMap1.put("isright", Boolean.valueOf(true));
/*     */       
/* 439 */       str1 = SystemEnv.getHtmlLabelName(6055, Util.getIntValue(paramUser.getLanguage()));
/* 440 */       ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*     */       
/* 442 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 443 */       hashMap.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 444 */       hashMap.put("inculdeNumZero", "0");
/* 445 */       hashMap.put("mtype", paramString);
/*     */       
/* 447 */       String str = GCONST.getContextPath() + "/api/cpt/moblie/capitalmanager/linkuse";
/* 448 */       ArrayList<String> arrayList2 = new ArrayList();
/* 449 */       arrayList2.add("mark");
/* 450 */       arrayList2.add("capitalgroupid");
/* 451 */       arrayList2.add("capitalgroupidspan");
/* 452 */       arrayList2.add("resourceid");
/* 453 */       arrayList2.add("resourceidspan");
/* 454 */       arrayList2.add("capitalspec");
/* 455 */       arrayList2.add("stockindate");
/* 456 */       arrayList2.add("location");
/* 457 */       arrayList2.add("startprice");
/* 458 */       arrayList2.add("capitalnum");
/* 459 */       arrayList2.add("blongdepartmentid");
/* 460 */       arrayList2.add("blongdepartmentidspan");
/* 461 */       arrayList2.add("warehouse");
/* 462 */       arrayList2.add("warehousespan");
/*     */       
/* 464 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("capitalid", SystemEnv.getHtmlLabelName(535, Util.getIntValue(paramUser.getLanguage())), "23", Util.null2String(paramMap.get("id")), 3, "", null, hashMap);
/* 465 */       map.put("viewAttr", "3");
/* 466 */       map.put("linkUrl", str);
/* 467 */       map.put("linkKey", arrayList2);
/* 468 */       arrayList1.add(map);
/*     */       
/* 470 */       map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("mark")), 200, 2);
/* 471 */       arrayList1.add(map);
/* 472 */       map = CptFormItemUtil.getFormItemForBrowser("capitalgroupid", SystemEnv.getHtmlLabelName(831, Util.getIntValue(paramUser.getLanguage())), "25", Util.null2String(paramMap.get("capitalgroupid")), 2, "", null, null);
/* 473 */       map.put("viewAttr", "2");
/* 474 */       arrayList1.add(map);
/* 475 */       map = CptFormItemUtil.getFormItemForBrowser("resourceid", SystemEnv.getHtmlLabelName(1508, Util.getIntValue(paramUser.getLanguage())), "1", Util.null2String(paramMap.get("resourceid")), 2, "", null, null);
/* 476 */       map.put("viewAttr", "2");
/* 477 */       arrayList1.add(map);
/* 478 */       map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("capitalspec")), 200, 2);
/* 479 */       arrayList1.add(map);
/* 480 */       map = CptFormItemUtil.getFormItemForDate("stockindate", SystemEnv.getHtmlLabelName(753, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("stockindate")), 2);
/* 481 */       arrayList1.add(map);
/* 482 */       map = CptFormItemUtil.getFormItemForInput("location", SystemEnv.getHtmlLabelName(1387, Util.getIntValue(paramUser.getLanguage())), Util.null2String(paramMap.get("location")), 200, 2);
/* 483 */       arrayList1.add(map);
/* 484 */       map = CptFormItemUtil.getFormItemForInputNumber("startprice", SystemEnv.getHtmlLabelName(726, paramUser.getLanguage()), Util.null2String(paramMap.get("startprice")), 2, "", "0", 2, "1");
/* 485 */       arrayList1.add(map);
/* 486 */       map = CptFormItemUtil.getFormItemForInputNumber("capitalnum", SystemEnv.getHtmlLabelName(1331, paramUser.getLanguage()), Util.null2String(paramMap.get("capitalnum")), 2, "", "0.1", 2, "1");
/* 487 */       arrayList1.add(map);
/* 488 */       map = CptFormItemUtil.getFormItemForBrowser("blongdepartmentid", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), "4", Util.null2String(paramMap.get("blongdepartmentid")), 2, "", null, null);
/* 489 */       map.put("viewAttr", "2");
/* 490 */       arrayList1.add(map);
/* 491 */       if (capitalTransMethod.IsWareHouseOpen()) {
/* 492 */         hashMap = new HashMap<>();
/* 493 */         hashMap.put("isfilter", "1");
/* 494 */         map = CptFormItemUtil.getFormItemForBrowser("warehouse", SystemEnv.getHtmlLabelName(711, Util.getIntValue(paramUser.getLanguage())), "320", Util.null2String(paramMap.get("warehouse")), 2, "", null, hashMap);
/* 495 */         map.put("viewAttr", "2");
/* 496 */         arrayList1.add(map);
/*     */       } 
/* 498 */       map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(503036, paramUser.getLanguage()), "", 200, 2);
/* 499 */       arrayList1.add(map);
/* 500 */       hashMap2.put("title", SystemEnv.getHtmlLabelName(6055, Util.getIntValue(paramUser.getLanguage())));
/* 501 */       hashMap2.put("items", arrayList1);
/* 502 */       hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 503 */       arrayList.add(hashMap2);
/*     */     } 
/* 505 */     hashMap1.put("title", str1);
/* 506 */     hashMap1.put("fieldinfo", arrayList);
/* 507 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getInventoryForm(String paramString1, String paramString2, String paramString3, User paramUser) {
/* 519 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 520 */     RecordSet recordSet = new RecordSet();
/* 521 */     if ("".equals(paramString2)) {
/* 522 */       return (Map)hashMap1;
/*     */     }
/* 524 */     String str1 = "";
/* 525 */     String str2 = "";
/* 526 */     String str3 = "";
/* 527 */     String str4 = "";
/*     */     
/* 529 */     String str5 = "select a.id,a.actualnum,a.remark,a.inventorydate,a.cptimgid from cpt_inventory_detail a LEFT JOIN cpt_inventory_planlist b ON a.mainid=b.id WHERE b.planliststate in (1,2,3) AND a.cptid=" + paramString2 + " and b.countuser=" + paramUser.getUID();
/*     */     
/* 531 */     if ("1".equals(paramString3)) {
/* 532 */       str5 = "select a.id,a.actualnum,a.remark,a.inventorydate,a.cptimgid from cpt_inventory_detail a where a.id=" + paramString1;
/*     */     }
/* 534 */     recordSet.execute(str5);
/* 535 */     if (recordSet.next()) {
/* 536 */       str1 = Util.null2String(recordSet.getString("inventorydate"));
/* 537 */       str2 = Util.null2String(recordSet.getString("actualnum"));
/* 538 */       str3 = Util.null2String(recordSet.getString("remark"));
/* 539 */       str4 = Util.null2String(recordSet.getString("cptimgid"));
/*     */     } 
/*     */     
/* 542 */     String str6 = "select id,name,mark,capitalspec,sptcount,resourceid,departmentid,blongdepartment,blongsubcompany,selectDate,capitalnum from CptCapital where id=" + paramString2;
/* 543 */     recordSet.execute(str6);
/* 544 */     recordSet.next();
/*     */ 
/*     */     
/* 547 */     if ("".equals(str2)) {
/* 548 */       str2 = Util.null2String(recordSet.getString("cptcapital", "capitalnum", true, true));
/*     */     }
/* 550 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/* 551 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 552 */     XssUtil xssUtil = new XssUtil();
/* 553 */     hashMap2.put("sqlwhere", xssUtil.put("where isdata=2"));
/* 554 */     Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("id", SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage())), "23", paramString2, 1, "", null, hashMap2);
/* 555 */     map.put("viewAttr", "1");
/* 556 */     arrayList.add(map);
/* 557 */     map = CptFormItemUtil.getFormItemForInput("mark", SystemEnv.getHtmlLabelName(714, Util.getIntValue(paramUser.getLanguage())), Util.null2String(recordSet.getString("cptcapital", "mark")), 200, 1);
/* 558 */     arrayList.add(map);
/* 559 */     map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(paramUser.getLanguage())), Util.null2String(recordSet.getString("cptcapital", "capitalspec")), 200, 1);
/* 560 */     arrayList.add(map);
/* 561 */     String str7 = SystemEnv.getHtmlLabelName(161, Util.getIntValue(paramUser.getLanguage()));
/* 562 */     if ("1".equals(Util.null2String(recordSet.getString("sptcount")))) {
/* 563 */       str7 = SystemEnv.getHtmlLabelName(163, Util.getIntValue(paramUser.getLanguage()));
/*     */     }
/* 565 */     map = CptFormItemUtil.getFormItemForInput("sptcount", SystemEnv.getHtmlLabelName(1363, Util.getIntValue(paramUser.getLanguage())), str7, 200, 1);
/* 566 */     arrayList.add(map);
/* 567 */     map = CptFormItemUtil.getFormItemForBrowser("resourceid", SystemEnv.getHtmlLabelName(1508, Util.getIntValue(paramUser.getLanguage())), "1", Util.null2String(recordSet.getString("resourceid")), 1, "", null, null);
/* 568 */     map.put("viewAttr", "1");
/* 569 */     arrayList.add(map);
/* 570 */     map = CptFormItemUtil.getFormItemForBrowser("departmentid", SystemEnv.getHtmlLabelName(21030, Util.getIntValue(paramUser.getLanguage())), "4", Util.null2String(recordSet.getString("departmentid")), 1, "", null, null);
/* 571 */     map.put("viewAttr", "1");
/* 572 */     arrayList.add(map);
/* 573 */     map = CptFormItemUtil.getFormItemForBrowser("blongdepartmentid", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(paramUser.getLanguage())), "4", Util.null2String(recordSet.getString("blongdepartment")), 1, "", null, null);
/* 574 */     map.put("viewAttr", "1");
/* 575 */     arrayList.add(map);
/* 576 */     map = CptFormItemUtil.getFormItemForBrowser("blongsubcompany", SystemEnv.getHtmlLabelName(19799, Util.getIntValue(paramUser.getLanguage())), "164", Util.null2String(recordSet.getString("blongsubcompany")), 1, "", null, null);
/* 577 */     map.put("viewAttr", "1");
/* 578 */     arrayList.add(map);
/* 579 */     map = CptFormItemUtil.getFormItemForDate("selectDate", SystemEnv.getHtmlLabelName(16914, Util.getIntValue(paramUser.getLanguage())), Util.null2String(recordSet.getString("selectDate")), 1);
/* 580 */     arrayList.add(map);
/* 581 */     map = CptFormItemUtil.getFormItemForInput("capitalnum", SystemEnv.getHtmlLabelName(1331, Util.getIntValue(paramUser.getLanguage())), Util.null2String(recordSet.getString("capitalnum")), 20, 1, 3, 2, null);
/* 582 */     arrayList.add(map);
/* 583 */     map = CptFormItemUtil.getFormItemForInput("inventorydate", SystemEnv.getHtmlLabelName(1415, Util.getIntValue(paramUser.getLanguage())), str1, 20, 1, 3, 2, null);
/* 584 */     arrayList.add(map);
/* 585 */     if ("1".equals(paramString3)) {
/* 586 */       map = CptFormItemUtil.getFormItemForInput("countnum", SystemEnv.getHtmlLabelName(17252, Util.getIntValue(paramUser.getLanguage())), str2, 20, 1, 3, 2, null);
/* 587 */       arrayList.add(map);
/* 588 */       map = CptFormItemUtil.getFormItemForTextArea("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), str3, 200, 1);
/* 589 */       arrayList.add(map);
/*     */       
/* 591 */       map = CptFormItemUtil.getFormItemForUpload("cptimgid", SystemEnv.getHtmlLabelName(507, paramUser.getLanguage()), str4, 1);
/* 592 */       Map<? extends String, ? extends String> map1 = CptFormItemUtil.getImgDatasList(str4, "", "1");
/* 593 */       map.putAll(map1);
/* 594 */       arrayList.add(map);
/*     */     } else {
/* 596 */       map = CptFormItemUtil.getFormItemForInputNumber("countnum", SystemEnv.getHtmlLabelName(17252, Util.getIntValue(paramUser.getLanguage())), str2, 3, "", "0", 2, "1");
/* 597 */       arrayList.add(map);
/* 598 */       map = CptFormItemUtil.getFormItemForTextArea("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(paramUser.getLanguage())), str3, 200, 2);
/* 599 */       arrayList.add(map);
/*     */       
/* 601 */       map = CptFormItemUtil.getFormItemForUpload("cptimgid", SystemEnv.getHtmlLabelName(507, paramUser.getLanguage()), str4, 2);
/* 602 */       Map<? extends String, ? extends String> map1 = CptFormItemUtil.getImgDatasList(str4, "", "1");
/* 603 */       map.putAll(map1);
/* 604 */       arrayList.add(map);
/*     */     } 
/*     */     
/* 607 */     hashMap1.put("fieldinfo", arrayList);
/* 608 */     recordSet.execute("select mobile_position from cpt_barcodesettings");
/* 609 */     recordSet.next();
/* 610 */     hashMap1.put("mobile_position", recordSet.getString(1));
/* 611 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getCptLinkageDataById(String paramString) {
/* 620 */     RecordSet recordSet = new RecordSet();
/* 621 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/* 622 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 623 */     String str = " select t1.*,t2.unitname from cptcapital t1 left outer join LgcAssetUnit t2 on t2.id=t1.unitid where  t1.id=" + paramString;
/* 624 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 625 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 626 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/* 628 */       resourceComInfo = new ResourceComInfo();
/* 629 */     } catch (Exception exception) {
/* 630 */       exception.printStackTrace();
/*     */     } 
/* 632 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 633 */     CapitalStateComInfo capitalStateComInfo = new CapitalStateComInfo();
/* 634 */     recordSet.execute(str);
/* 635 */     if (recordSet.next()) {
/* 636 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/* 637 */       hashMap.put("sptcount", Util.null2String(recordSet.getString("sptcount")));
/* 638 */       hashMap.put("mark", Util.null2String(recordSet.getString("cptcapital", "mark", true, true)));
/* 639 */       hashMap.put("capitalgroupid", Util.null2String(recordSet.getString("capitalgroupid")));
/* 640 */       hashMap.put("capitalgroupname_", capitalAssortmentComInfo.getAssortmentName(Util.null2String(recordSet.getString("capitalgroupid"))));
/* 641 */       hashMap.put("capitalgroupidspan", capitalAssortmentComInfo.getAssortmentName(Util.null2String(recordSet.getString("capitalgroupid"))));
/* 642 */       hashMap.put("capitalspec", Util.null2String(recordSet.getString("cptcapital", "capitalspec", true, true)));
/* 643 */       hashMap.put("name", Util.null2String(recordSet.getString("name")));
/* 644 */       hashMap.put("startprice", Util.null2String(recordSet.getString("cptcapital", "startprice", true, true)));
/* 645 */       hashMap.put("price", Util.null2String(recordSet.getString("cptcapital", "startprice", true, true)));
/* 646 */       hashMap.put("unitid", Util.null2String(recordSet.getString("unitid")));
/* 647 */       hashMap.put("unitname", Util.null2String(recordSet.getString("unitname")));
/* 648 */       hashMap.put("location", Util.null2String(recordSet.getString("cptcapital", "location", true, true)));
/* 649 */       hashMap.put("stockindate", Util.null2String(recordSet.getString("stockindate")));
/* 650 */       hashMap.put("selectdate", Util.null2String(recordSet.getString("selectdate")));
/* 651 */       hashMap.put("stateid", Util.null2String(recordSet.getString("stateid")));
/* 652 */       hashMap.put("statename", capitalStateComInfo.getCapitalStatename(Util.null2String(recordSet.getString("stateid"))));
/* 653 */       hashMap.put("blongsubcompanyid", Util.null2String(recordSet.getString("blongsubcompany")));
/* 654 */       hashMap.put("blongsubcompanyname", subCompanyComInfo.getSubCompanyname(Util.null2String(recordSet.getString("blongsubcompany"))));
/* 655 */       hashMap.put("blongsubcompanyidspan", subCompanyComInfo.getSubCompanyname(Util.null2String(recordSet.getString("blongsubcompany"))));
/* 656 */       hashMap.put("blongdepartmentid", Util.null2String(recordSet.getString("blongdepartment")));
/* 657 */       hashMap.put("blongdepartmentidspan", departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))));
/* 658 */       hashMap.put("blongdepartmentname", departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))));
/* 659 */       hashMap.put("blongdepartmentname_", departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))));
/* 660 */       hashMap.put("resourceid", Util.null2String(recordSet.getString("resourceid")));
/* 661 */       hashMap.put("resourceidspan", resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))));
/* 662 */       hashMap.put("resourcename", resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))));
/* 663 */       hashMap.put("resourcename_", resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))));
/* 664 */       hashMap.put("location", Util.null2String(recordSet.getString("cptcapital", "location", true, true)));
/* 665 */       hashMap.put("remark", Util.null2String(recordSet.getString("cptcapital", "remark", true, true)));
/* 666 */       if (capitalTransMethod.IsWareHouseOpen()) {
/* 667 */         hashMap.put("warehouse", Util.null2String(recordSet.getString("warehouse")));
/* 668 */         hashMap.put("warehousespan", capitalTransMethod.getWareHouseName(Util.null2String(recordSet.getString("warehouse"))));
/* 669 */         hashMap.put("warehousename", capitalTransMethod.getWareHouseName(Util.null2String(recordSet.getString("warehouse"))));
/*     */       } 
/*     */       
/* 672 */       double d1 = Util.getDoubleValue(recordSet.getString("cptcapital", "capitalnum", true, true), 0.0D);
/* 673 */       double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/* 674 */       if (d2 < 0.0D) {
/* 675 */         d2 = 0.0D;
/*     */       }
/* 677 */       double d3 = d1 - d2;
/* 678 */       if (d3 < 0.0D) d3 = 0.0D; 
/* 679 */       hashMap.put("capitalnum", "" + d1);
/* 680 */       hashMap.put("frozennum", "" + d2);
/* 681 */       hashMap.put("availablenum", "" + d3);
/*     */     } 
/* 683 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public static String getResourceSql(User paramUser, String paramString1, boolean paramBoolean1, boolean paramBoolean2, ArrayList<String> paramArrayList, String paramString2) {
/* 687 */     RecordSet recordSet = new RecordSet();
/* 688 */     String str = "";
/* 689 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/* 690 */     boolean bool = capitalTransMethod.IsWareHouseOpen();
/* 691 */     if (!paramUser.getLoginid().equalsIgnoreCase("sysadmin")) {
/* 692 */       if ("cptmove".equalsIgnoreCase(paramString2) || "move".equalsIgnoreCase(paramString2)) {
/* 693 */         String str1 = "";
/* 694 */         if (bool) {
/* 695 */           if (recordSet.getDBType().equals("oracle")) {
/* 696 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where ','||manager||',' like '%," + paramUser.getUID() + ",%') and sptcount=0 and isdata=2) ";
/* 697 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 698 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where concat(',',manager,',') like '%," + paramUser.getUID() + ",%') and sptcount=0 and isdata=2) ";
/*     */           }
/* 700 */           else if (recordSet.getDBType().equals("postgresql")) {
/* 701 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where ','||manager||',' like '%," + paramUser.getUID() + ",%') and sptcount=0 and isdata=2) ";
/*     */           } else {
/*     */             
/* 704 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where ','+manager+',' like '%," + paramUser.getUID() + ",%') and sptcount=0 and isdata=2) ";
/*     */           } 
/*     */         }
/*     */         
/* 708 */         if (paramBoolean1) {
/* 709 */           if (paramString1.length() > 0) {
/* 710 */             str = str + " and ((resourceid='" + paramUser.getUID() + "' and stateid=2) " + str1 + " ) ";
/*     */           } else {
/* 712 */             str = str + " where ((resourceid='" + paramUser.getUID() + "' and stateid=2) " + str1 + " ) ";
/*     */           } 
/* 714 */         } else if (paramBoolean2 && !paramArrayList.contains(paramUser.getUID() + "")) {
/* 715 */           if (paramString1.length() > 0) {
/* 716 */             str = str + " and ((resourceid='" + paramUser.getUID() + "' and stateid=2) " + str1 + " ) ";
/*     */           } else {
/* 718 */             str = str + " where ((resourceid='" + paramUser.getUID() + "' and stateid=2) " + str1 + " ) ";
/*     */           }
/*     */         
/* 721 */         } else if (paramString1.length() > 0) {
/* 722 */           str = str + " and (stateid=2 " + str1 + " ) ";
/*     */         } else {
/* 724 */           str = str + " where (stateid=2 " + str1 + " ) ";
/*     */         }
/*     */       
/*     */       }
/* 728 */       else if (paramBoolean1) {
/* 729 */         if (paramString1.length() > 0) {
/* 730 */           str = str + " and resourceid='" + paramUser.getUID() + "'";
/*     */         } else {
/* 732 */           str = str + " where resourceid='" + paramUser.getUID() + "'";
/*     */         } 
/* 734 */       } else if (paramBoolean2 && !paramArrayList.contains(paramUser.getUID() + "")) {
/* 735 */         if (paramString1.length() > 0) {
/* 736 */           str = str + " and resourceid='" + paramUser.getUID() + "'";
/*     */         } else {
/* 738 */           str = str + " where resourceid='" + paramUser.getUID() + "'";
/*     */         }
/*     */       
/*     */       }
/*     */     
/* 743 */     } else if ("cptmove".equalsIgnoreCase(paramString2) || "move".equalsIgnoreCase(paramString2)) {
/* 744 */       String str1 = "";
/* 745 */       if (bool) {
/* 746 */         str1 = " or id in(select id from CptCapital where sptcount=0 and isdata=2) ";
/*     */       }
/* 748 */       if (paramString1.length() > 0) {
/* 749 */         str = str + " and (stateid=2 " + str1 + " ) ";
/*     */       } else {
/* 751 */         str = str + " where (stateid=2 " + str1 + " ) ";
/*     */       } 
/*     */     } 
/*     */     
/* 755 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/util/ManagerFormItemUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */