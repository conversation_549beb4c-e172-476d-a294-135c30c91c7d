/*     */ package com.api.cpt.mobile.util;
/*     */ 
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CptFieldComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.crm.investigate.ContacterComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*     */ import weaver.formmode.browser.FormModeBrowserUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.browser.Browser;
/*     */ import weaver.interfaces.workflow.browser.BrowserBean;
/*     */ import weaver.proj.Maint.ProjectInfoComInfo;
/*     */ import weaver.proj.util.CodeUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalTransUtil
/*     */ {
/*  42 */   public static BaseBean baseBean = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getImgCapitalById(String paramString1, String paramString2) {
/*  51 */     String str = "";
/*  52 */     if (!"null".equals(paramString1) && !"".equals(paramString1) && !"0".equals(paramString1)) {
/*  53 */       String[] arrayOfString = paramString1.split(",");
/*  54 */       if (Util.getIntValue(arrayOfString[0], 0) > 0) {
/*  55 */         str = GCONST.getContextPath() + "/weaver/weaver.file.FileDownload?fileid=" + arrayOfString[0];
/*     */       } else {
/*  57 */         str = GCONST.getContextPath() + "/font/cpt/nocpt_blue.svg";
/*     */       } 
/*     */     } else {
/*  60 */       str = GCONST.getContextPath() + "/font/cpt/nocpt_blue.svg";
/*     */     } 
/*  62 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInstockSelectDate(String paramString1, String paramString2) {
/*  72 */     String str = "";
/*  73 */     RecordSet recordSet = new RecordSet();
/*  74 */     recordSet.execute("select SelectDate,contractno,customerid from CptStockInDetail where cptstockinid = " + paramString1 + " order by id ");
/*  75 */     if (recordSet.next()) {
/*  76 */       str = Util.null2String(recordSet.getString("SelectDate"));
/*     */     }
/*  78 */     return str;
/*     */   }
/*     */   
/*     */   public String getTrue(String paramString) {
/*  82 */     return "true";
/*     */   }
/*     */   
/*     */   public String getInventorystate(String paramString1, String paramString2) {
/*  86 */     if ("".equals(Util.null2String(paramString1))) {
/*  87 */       return "";
/*     */     }
/*  89 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/*     */     
/*  91 */     int i = Util.getIntValue(paramString1, 0);
/*  92 */     int j = 7;
/*  93 */     if (arrayOfString.length > 0) {
/*  94 */       j = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/*  96 */     String str = "";
/*  97 */     switch (i) {
/*     */       case 0:
/*  99 */         str = SystemEnv.getHtmlLabelName(1979, j);
/*     */         break;
/*     */       case 1:
/* 102 */         str = SystemEnv.getHtmlLabelName(384644, j);
/*     */         break;
/*     */       case 2:
/* 105 */         str = SystemEnv.getHtmlLabelName(384645, j);
/*     */         break;
/*     */       case 3:
/* 108 */         str = SystemEnv.getHtmlLabelName(19045, j);
/*     */         break;
/*     */       case 4:
/* 111 */         str = SystemEnv.getHtmlLabelName(225, j);
/*     */         break;
/*     */       case 5:
/* 114 */         str = SystemEnv.getHtmlLabelName(1398, j);
/*     */         break;
/*     */       case 6:
/* 117 */         str = SystemEnv.getHtmlLabelName(1397, j);
/*     */         break;
/*     */       case 7:
/* 120 */         str = SystemEnv.getHtmlLabelName(384647, j);
/*     */         break;
/*     */       case 8:
/* 123 */         str = SystemEnv.getHtmlLabelName(384648, j);
/*     */         break;
/*     */     } 
/*     */     
/* 127 */     return str;
/*     */   }
/*     */   
/*     */   public String getInventorySubmitStr(String paramString1, String paramString2) {
/* 131 */     if ("".equals(Util.null2String(paramString1))) {
/* 132 */       return "";
/*     */     }
/* 134 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 135 */     int i = 0;
/* 136 */     if (arrayOfString.length > 1) {
/* 137 */       i = Util.getIntValue(arrayOfString[0], 0);
/*     */     }
/* 139 */     String str = "";
/* 140 */     if (i == 3) {
/* 141 */       str = "<button class='template-dot-btn' onclick='doInventory(this," + paramString1 + ")'}'><span class='icon-cpt icon-group8 template-dot-icon'></span></button>";
/*     */     }
/*     */     
/* 144 */     return str;
/*     */   }
/*     */   
/*     */   public String getInventoryCptName(String paramString1, String paramString2) {
/* 148 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 149 */     int i = 7;
/* 150 */     if (arrayOfString.length > 0) {
/* 151 */       i = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/*     */     
/* 154 */     RecordSet recordSet = new RecordSet();
/* 155 */     recordSet.execute("select cptid,cptname from cpt_inventory_detail where id=" + paramString1);
/* 156 */     recordSet.next();
/* 157 */     String str1 = recordSet.getString(1);
/* 158 */     String str2 = recordSet.getString(2);
/* 159 */     recordSet.execute("select name from cptcapital where id=" + str1);
/* 160 */     recordSet.next();
/* 161 */     String str3 = Util.null2String(recordSet.getString(1));
/* 162 */     if ("".equals(str3)) {
/* 163 */       str2 = "<span style='color:red;'>(" + SystemEnv.getHtmlLabelName(18967, i) + ")</span>" + str2;
/*     */     } else {
/* 165 */       str2 = str3;
/*     */     } 
/* 167 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInstockState(String paramString1, String paramString2) {
/* 179 */     int i = Util.getIntValue(paramString1);
/* 180 */     String str = "82682";
/* 181 */     if (i == 1) {
/* 182 */       str = "82684";
/* 183 */     } else if (i == -1) {
/* 184 */       str = "27774";
/* 185 */     } else if (i == -2) {
/* 186 */       str = "18661";
/*     */     } 
/* 188 */     return SystemEnv.getHtmlLabelNames(str, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSptcountState(String paramString1, String paramString2) {
/* 198 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 199 */     int i = Util.getIntValue(paramString1, 0);
/* 200 */     int j = 7;
/* 201 */     if (arrayOfString.length > 0) {
/* 202 */       j = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/* 204 */     String str = SystemEnv.getHtmlLabelName(161, j);
/* 205 */     if (i == 1) {
/* 206 */       str = "<span style='color: red;'>" + SystemEnv.getHtmlLabelName(163, j) + "</span>";
/*     */     }
/* 208 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMessagerUrls(String paramString1, String paramString2) {
/* 218 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/* 220 */       resourceComInfo = new ResourceComInfo();
/* 221 */     } catch (Exception exception) {
/* 222 */       exception.printStackTrace();
/*     */     } 
/* 224 */     return resourceComInfo.getMessagerUrls(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourcename(String paramString1, String paramString2) {
/* 234 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/* 236 */       resourceComInfo = new ResourceComInfo();
/* 237 */     } catch (Exception exception) {
/* 238 */       exception.printStackTrace();
/*     */     } 
/* 240 */     return resourceComInfo.getResourcename(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentname(String paramString1, String paramString2) {
/* 250 */     DepartmentComInfo departmentComInfo = null;
/*     */     try {
/* 252 */       departmentComInfo = new DepartmentComInfo();
/* 253 */     } catch (Exception exception) {
/* 254 */       exception.printStackTrace();
/*     */     } 
/* 256 */     return departmentComInfo.getDepartmentname(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCompanyname(String paramString1, String paramString2) {
/* 266 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 267 */     return subCompanyComInfo.getSubCompanyname(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentName(String paramString1, String paramString2) {
/* 277 */     CapitalAssortmentComInfo capitalAssortmentComInfo = null;
/*     */     try {
/* 279 */       capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 280 */     } catch (Exception exception) {
/* 281 */       exception.printStackTrace();
/*     */     } 
/* 283 */     return capitalAssortmentComInfo.getAssortmentName(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWareHouseName(String paramString1, String paramString2) {
/* 293 */     RecordSet recordSet = new RecordSet();
/* 294 */     String str = "";
/* 295 */     recordSet.executeQuery("select name from CptCapitalWareHouse where id=?", new Object[] { paramString1 });
/* 296 */     if (recordSet.next()) {
/* 297 */       str = Util.null2String(recordSet.getString("name"));
/*     */     }
/* 299 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBrowserShowName(String paramString1, String paramString2) {
/* 310 */     RecordSet recordSet = new RecordSet();
/* 311 */     String str1 = "";
/* 312 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 313 */     String str2 = "";
/* 314 */     String str3 = paramString1;
/* 315 */     String str4 = "";
/* 316 */     if (arrayOfString != null && arrayOfString.length >= 2) {
/* 317 */       str2 = arrayOfString[0];
/* 318 */       str4 = arrayOfString[1];
/*     */     } 
/*     */     try {
/* 321 */       ArrayList<String> arrayList = Util.TokenizerString(str3, ",");
/* 322 */       if (str2.equals("1") || str2.equals("17")) {
/* 323 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 325 */           str1 = str1 + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 328 */       else if (str2.equals("2") || str2.equals("19")) {
/*     */         
/* 330 */         str1 = str1 + str3;
/* 331 */       } else if (str2.equals("4") || str2.equals("57")) {
/* 332 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 334 */           str1 = str1 + (new DepartmentComInfo()).getDepartmentname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 337 */       else if (str2.equals("8") || str2.equals("135")) {
/* 338 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 340 */           str1 = str1 + (new ProjectInfoComInfo()).getProjectInfoname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 343 */       else if (str2.equals("7") || str2.equals("18")) {
/* 344 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 346 */           str1 = str1 + (new CustomerInfoComInfo()).getCustomerInfoname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 349 */       else if (str2.equals("164")) {
/* 350 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 352 */           str1 = str1 + (new SubCompanyComInfo()).getSubCompanyname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 355 */       else if (str2.equals("9")) {
/* 356 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 358 */           str1 = str1 + (new DocComInfo()).getDocname(arrayList.get(b));
/*     */         }
/* 360 */       } else if (str2.equals("37")) {
/* 361 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 363 */           str1 = str1 + (new DocComInfo()).getDocname(arrayList.get(b)) + ",";
/*     */         }
/* 365 */       } else if (str2.equals("23")) {
/* 366 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 368 */           str1 = str1 + (new CapitalComInfo()).getCapitalname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 371 */       else if (str2.equals("16") || str2.equals("152")) {
/* 372 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 374 */           str1 = str1 + (new WorkflowRequestComInfo()).getRequestName(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 377 */       else if (str2.equals("67")) {
/* 378 */         ContacterComInfo contacterComInfo = new ContacterComInfo();
/* 379 */         str1 = contacterComInfo.getContacterName(str3);
/* 380 */       } else if (str2.equals("sellchance")) {
/* 381 */         recordSet.execute("select subject from CRM_SellChance where id=" + str3);
/* 382 */         if (recordSet.next()) {
/* 383 */           str1 = recordSet.getString("subject");
/*     */         }
/* 385 */       } else if (str2.equals("product")) {
/* 386 */         recordSet.execute("select assetname from LgcAssetCountry where id=" + str3);
/* 387 */         if (recordSet.next()) {
/* 388 */           str1 = recordSet.getString("assetname");
/*     */         }
/* 390 */       } else if (str2.equals("142")) {
/* 391 */         DocReceiveUnitComInfo docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/* 392 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 394 */           str1 = str1 + docReceiveUnitComInfo.getReceiveUnitName(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 397 */       else if (str2.equals("226") || str2.equals("227")) {
/* 398 */         str1 = str1 + str3;
/* 399 */       } else if (str2.equals("161") || str2.equals("162")) {
/* 400 */         if (Util.null2String(str4).length() == 0) return ""; 
/*     */         try {
/* 402 */           Browser browser = (Browser)StaticObj.getServiceByFullname(str4, Browser.class);
/* 403 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 404 */             BrowserBean browserBean = browser.searchById(arrayList.get(b));
/* 405 */             String str = Util.null2String(browserBean.getName());
/* 406 */             if (str1.equals("")) {
/* 407 */               str1 = str1 + str;
/*     */             } else {
/* 409 */               str1 = str1 + "," + str;
/*     */             } 
/*     */           } 
/* 412 */         } catch (Exception exception) {
/* 413 */           baseBean.writeLog(exception);
/*     */         } 
/*     */       } else {
/* 416 */         String str5 = "";
/* 417 */         String str6 = (new BrowserComInfo()).getBrowsertablename("" + str2);
/*     */         
/* 419 */         String str7 = (new BrowserComInfo()).getBrowsercolumname("" + str2);
/*     */ 
/*     */         
/* 422 */         String str8 = (new BrowserComInfo()).getBrowserkeycolumname("" + str2);
/* 423 */         if (!str7.equals("") && !str6.equals("") && 
/* 424 */           !str8.equals("") && !str3.equals("")) {
/*     */           
/* 426 */           str5 = "select " + str7 + " from " + str6 + " where " + str8 + " in(" + str3 + ")";
/*     */ 
/*     */           
/* 429 */           recordSet.execute(str5);
/* 430 */           while (recordSet.next()) {
/* 431 */             str1 = str1 + recordSet.getString(1) + ",";
/*     */           }
/*     */         } 
/*     */       } 
/* 435 */       if (str1.endsWith(",")) {
/* 436 */         str1 = str1.substring(0, str1.length() - 1);
/*     */       }
/* 438 */     } catch (Exception exception) {
/* 439 */       baseBean.writeLog(exception);
/*     */     } 
/* 441 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean checkmark(User paramUser, Map<String, Object> paramMap) {
/* 451 */     String str1 = Util.null2String(paramMap.get("isdata"));
/* 452 */     String str2 = Util.null2String(paramMap.get("mark"));
/* 453 */     CodeUtil codeUtil = new CodeUtil();
/* 454 */     String str3 = "1".equals(str1) ? codeUtil.getCptData1CodeUse() : codeUtil.getCptData2CodeUse();
/* 455 */     String str4 = Util.null2String(paramMap.get("capitalid"));
/* 456 */     if ("2".equals(str3)) {
/* 457 */       boolean bool = true;
/* 458 */       String str = "select * from cptcapital where mark='" + str2.trim() + "' ";
/* 459 */       if (!"".equals(str1)) {
/* 460 */         str = str + "and isdata = '" + str1 + "'";
/*     */       }
/* 462 */       if (!"".equals(str4)) {
/* 463 */         str = str + "and id <> " + str4;
/*     */       }
/* 465 */       RecordSet recordSet = new RecordSet();
/* 466 */       recordSet.execute(str);
/* 467 */       if (recordSet.next()) {
/* 468 */         bool = false;
/*     */       } else {
/* 470 */         bool = true;
/*     */       } 
/* 472 */       return bool;
/*     */     } 
/* 474 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void updateCusfieldValue(String paramString, Map<String, Object> paramMap, User paramUser) {
/* 483 */     if ("".equals(Util.null2String(paramString)) || paramMap == null || paramUser == null) {
/*     */       return;
/*     */     }
/*     */     try {
/* 487 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 488 */       CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/* 489 */       TreeMap treeMap = cptFieldComInfo.getOpenFieldMap();
/*     */       
/* 491 */       if (treeMap != null && treeMap.size() > 0) {
/* 492 */         RecordSet recordSet1 = new RecordSet();
/* 493 */         RecordSet recordSet2 = new RecordSet();
/* 494 */         recordSet2.isReturnDecryptData(true);
/* 495 */         char c = Util.getSeparator();
/* 496 */         boolean bool = false;
/* 497 */         String str1 = DateHelper.getCurrentDate();
/* 498 */         recordSet2.executeQuery("select * from CptCapital where isdata=2 and id=?", new Object[] { paramString });
/* 499 */         if (recordSet2.next()) {
/* 500 */           bool = true;
/*     */         }
/*     */         
/* 503 */         Iterator<Map.Entry> iterator = treeMap.entrySet().iterator();
/* 504 */         String str2 = "";
/*     */         
/* 506 */         ArrayList<String> arrayList = new ArrayList();
/* 507 */         while (iterator.hasNext()) {
/* 508 */           Map.Entry entry = iterator.next();
/* 509 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/* 510 */           int i = Util.getIntValue(jSONObject.getString("type"));
/* 511 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/* 512 */           String str3 = Util.null2String(jSONObject.getString("fielddbtype"));
/* 513 */           String str4 = jSONObject.getString("fieldname");
/* 514 */           String str5 = Util.null2String(paramMap.get(str4));
/* 515 */           String str6 = Util.null2String(recordSet2.getString(str4));
/* 516 */           String str7 = "";
/* 517 */           if (!str5.equals(str6) && bool) {
/* 518 */             if (j == 2 && i == 2 && 
/* 519 */               str5.endsWith("\n") && str5.substring(0, str5.lastIndexOf("\n")).equals(str6)) {
/*     */               continue;
/*     */             }
/*     */             
/* 523 */             str7 = paramString;
/* 524 */             str7 = str7 + c + str4;
/* 525 */             str7 = str7 + c + capitalTransMethod.getFieldValueByType(str6, j, i, str3, Util.null2String(jSONObject.getString("id")));
/* 526 */             str7 = str7 + c + capitalTransMethod.getFieldValueByType(str5, j, i, str3, Util.null2String(jSONObject.getString("id")));
/* 527 */             str7 = str7 + c + "" + paramUser.getUID();
/* 528 */             str7 = str7 + c + str1;
/* 529 */             recordSet1.executeProc("CptCapitalModify_Insert", str7);
/*     */           } 
/*     */           
/* 532 */           if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 533 */             if (str3.toUpperCase().indexOf("INT") >= 0) {
/* 534 */               if ("5".equals(Integer.valueOf(j))) {
/* 535 */                 if (!Util.null2String(str5).equals("")) {
/* 536 */                   str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ","; continue;
/*     */                 } 
/* 538 */                 str2 = str2 + str4 + " = NULL,";
/*     */                 continue;
/*     */               } 
/* 541 */               if (!Util.null2String(str5).equals("")) {
/* 542 */                 str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ","; continue;
/*     */               } 
/* 544 */               str2 = str2 + str4 + " = NULL,";
/*     */               continue;
/*     */             } 
/* 547 */             if (str3.toUpperCase().indexOf("NUMBER") >= 0 || str3.toUpperCase().indexOf("FLOAT") >= 0 || str3.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 548 */               int k = str3.indexOf(",");
/* 549 */               int m = 2;
/* 550 */               if (k > -1) {
/* 551 */                 m = Util.getIntValue(str3.substring(k + 1, str3.length() - 1).trim(), 2);
/*     */               } else {
/* 553 */                 m = 2;
/*     */               } 
/* 555 */               if (!Util.null2String(str5).equals("")) {
/* 556 */                 str2 = str2 + str4 + " = " + Util.getPointValue2(str5, m) + ",";
/*     */                 continue;
/*     */               } 
/* 559 */               str2 = str2 + str4 + " = NULL,"; continue;
/*     */             } 
/* 561 */             if (j == 6) {
/*     */               continue;
/*     */             }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 591 */             String str = "";
/* 592 */             if (j == 3 && (i == 161 || i == 162)) {
/* 593 */               str = Util.null2String(str5);
/* 594 */               str = str.trim();
/*     */             }
/* 596 */             else if (j == 2 && i == 2) {
/* 597 */               str = Util.toHtml100(str5);
/* 598 */               str = StringHelper.convertSpecialChar2Html(str5);
/* 599 */             } else if (j == 1 && i == 1) {
/* 600 */               str = StringHelper.convertSpecialChar2Html(str5);
/* 601 */               str = Util.toHtmlForWorkflow(str);
/* 602 */             } else if (j == 2 && i == 1) {
/* 603 */               str = Util.StringReplace(str5, " ", "&nbsp;");
/* 604 */               str = StringHelper.convertSpecialChar2Html(str);
/*     */               
/* 606 */               str = Util.toHtmlForWorkflowForMode(str);
/*     */             } else {
/* 608 */               str = Util.StringReplace(Util.toHtml10(str5), " ", "&nbsp;");
/* 609 */               str = StringHelper.convertSpecialChar2Html(str);
/* 610 */               str = Util.toHtmlForWorkflow(str);
/*     */             } 
/*     */             
/* 613 */             str = Util.StringReplace(str, "weaver2017", "+");
/*     */             
/* 615 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 616 */               str2 = str2 + str4 + " = ?,";
/* 617 */               arrayList.add(str); continue;
/*     */             } 
/* 619 */             str2 = str2 + str4 + " = '" + str + "',";
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 624 */           if (str3.toUpperCase().indexOf("INT") >= 0) {
/* 625 */             if ("5".equals(Integer.valueOf(j))) {
/* 626 */               if (!"".equals(str5)) {
/* 627 */                 str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ","; continue;
/*     */               } 
/* 629 */               str2 = str2 + str4 + " = NULL,";
/*     */               continue;
/*     */             } 
/* 632 */             if (!"".equals(str5)) {
/* 633 */               str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ","; continue;
/*     */             } 
/* 635 */             str2 = str2 + str4 + " = NULL,";
/*     */             continue;
/*     */           } 
/* 638 */           if (str3.toUpperCase().indexOf("DECIMAL") >= 0 || str3.toUpperCase().indexOf("FLOAT") >= 0) {
/* 639 */             int k = str3.indexOf(",");
/* 640 */             int m = 2;
/* 641 */             if (k > -1) {
/* 642 */               m = Util.getIntValue(str3.substring(k + 1, str3.length() - 1).trim(), 2);
/*     */             } else {
/* 644 */               m = 2;
/*     */             } 
/* 646 */             if (!"".equals(str5)) {
/* 647 */               str2 = str2 + str4 + " = " + Util.getPointValue2(str5, m) + ","; continue;
/*     */             } 
/* 649 */             str2 = str2 + str4 + " = NULL,";
/*     */             continue;
/*     */           } 
/* 652 */           if (j == 6) {
/* 653 */             String str = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*     */             continue;
/*     */           } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 684 */           String str8 = "";
/* 685 */           if (j == 2 && i == 2) {
/* 686 */             str8 = Util.toHtml100(str5);
/* 687 */             str8 = StringHelper.convertSpecialChar2Html(str5);
/* 688 */           } else if (j == 1 && i == 1) {
/* 689 */             str8 = StringHelper.convertSpecialChar2Html(str5);
/* 690 */             str8 = Util.toHtmlForWorkflow(str8);
/* 691 */           } else if (j == 2 && i == 1) {
/* 692 */             str8 = Util.StringReplace(str5, " ", "&nbsp;");
/* 693 */             str8 = StringHelper.convertSpecialChar2Html(str8);
/*     */             
/* 695 */             str8 = Util.toHtmlForWorkflowForMode(str8);
/* 696 */           } else if (j == 4 && i == 1) {
/* 697 */             str8 = Util.StringReplace(str5, " ", "&nbsp;");
/* 698 */             str8 = Util.toHtmlForWorkflow(str8);
/* 699 */             if (str8.equals("")) {
/* 700 */               str8 = "0";
/*     */             }
/*     */           } else {
/* 703 */             str8 = Util.StringReplace(Util.toHtml10(str5), " ", "&nbsp;");
/* 704 */             str8 = StringHelper.convertSpecialChar2Html(str8);
/* 705 */             str8 = Util.toHtmlForWorkflow(str8);
/*     */           } 
/* 707 */           str8 = Util.StringReplace(str8, "weaver2017", "+");
/*     */           
/* 709 */           if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 710 */             str2 = str2 + str4 + " = ?,";
/* 711 */             arrayList.add(str8); continue;
/*     */           } 
/* 713 */           str2 = str2 + str4 + " = '" + str8 + "',";
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 719 */         if (!str2.equals("")) {
/* 720 */           str2 = str2.substring(0, str2.length() - 1);
/* 721 */           str2 = " update cptcapital set  " + str2 + " where id = " + paramString;
/* 722 */           Object[] arrayOfObject = new Object[arrayList.size()];
/* 723 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 724 */             arrayOfObject[b] = arrayList.get(b);
/*     */           }
/* 726 */           recordSet1.executeSql(str2, false, arrayOfObject);
/*     */         } 
/*     */       } 
/* 729 */     } catch (Exception exception) {
/* 730 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/util/CapitalTransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */