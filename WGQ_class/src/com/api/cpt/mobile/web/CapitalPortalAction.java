/*     */ package com.api.cpt.mobile.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.cpt.mobile.service.CapitalPortalService;
/*     */ import com.api.cpt.mobile.service.impl.CapitalPortalServiceImpl;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/cpt/moblie/capitalportal")
/*     */ public class CapitalPortalAction
/*     */ {
/*     */   private CapitalPortalService getService(User paramUser) {
/*  31 */     return (CapitalPortalService)ServiceUtil.getService(CapitalPortalServiceImpl.class, paramUser);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getPortalInstock")
/*     */   @Produces({"text/plain"})
/*     */   public String getPortalInstock(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  45 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  46 */     hashMap.putAll(getService(user).getPortalInstock(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  47 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getPortalMyCapital")
/*     */   @Produces({"text/plain"})
/*     */   public String getPortalMyCapital(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  62 */     hashMap.putAll(getService(user).getPortalMyCapital(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  63 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getPortalCount")
/*     */   @Produces({"text/plain"})
/*     */   public String getPortalCount(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  77 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  78 */     hashMap.putAll(getService(user).getPortalCount(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  79 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalList")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  93 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  94 */     hashMap.putAll(getService(user).getCapitalList(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  95 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCptListCondition")
/*     */   @Produces({"text/plain"})
/*     */   public String getCptListCondition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 110 */     hashMap.putAll(getService(user).getListSearchCondition(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 111 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getInstockList")
/*     */   @Produces({"text/plain"})
/*     */   public String getInstockList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 126 */     hashMap.putAll(getService(user).getInstockList(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 127 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getInstockListHis")
/*     */   @Produces({"text/plain"})
/*     */   public String getInstockListHis(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 140 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 141 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 142 */     hashMap.putAll(getService(user).getInstockListHis(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 143 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getInstockDetail")
/*     */   @Produces({"text/plain"})
/*     */   public String getInstockDetail(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 156 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 157 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 158 */     hashMap.putAll(getService(user).getInstockDetail(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 159 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doInstockDelete")
/*     */   @Produces({"text/plain"})
/*     */   public String doInstockDelete(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 172 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 173 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 174 */     hashMap.putAll(getService(user).doInstockDelete(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 175 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doInstockCheck")
/*     */   @Produces({"text/plain"})
/*     */   public String instockOfCheckState(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 188 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 189 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 190 */     hashMap.putAll(getService(user).doInstockCheck(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 191 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doInstockCapital")
/*     */   @Produces({"text/plain"})
/*     */   public String doInstockCapital(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 204 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 205 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 206 */     hashMap.putAll(getService(user).doInstockCapital(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 207 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCptMenuRight")
/*     */   @Produces({"text/plain"})
/*     */   public String getCptMenuRight(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 220 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 221 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 222 */     hashMap.putAll(getService(user).getCptMenuRight(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 223 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/web/CapitalPortalAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */