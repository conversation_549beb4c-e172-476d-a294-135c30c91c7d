/*     */ package com.api.cpt.mobile.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.cpt.mobile.service.CapitalManagerService;
/*     */ import com.api.cpt.mobile.service.impl.CapitalManagerServiceImpl;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/cpt/moblie/capitalmanager")
/*     */ public class CapitalManagerAction
/*     */ {
/*     */   private CapitalManagerService getService(User paramUser) {
/*  31 */     return (CapitalManagerService)ServiceUtil.getService(CapitalManagerServiceImpl.class, paramUser);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCptManagerForm")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  45 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  46 */     hashMap.putAll(getService(user).getCptManagerForm(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  47 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doCptIfOverAjax")
/*     */   @Produces({"text/plain"})
/*     */   public String doCptIfOverAjax(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  62 */     hashMap.putAll(getService(user).doCptIfOverAjax(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  63 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doCptManager")
/*     */   @Produces({"text/plain"})
/*     */   public String doCptManager(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  77 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  78 */     hashMap.putAll(getService(user).doCptManager(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  79 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/scanQRCodeCapital")
/*     */   @Produces({"text/plain"})
/*     */   public String scanQRCodeCapital(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  93 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  94 */     hashMap.putAll(getService(user).scanQRCodeCapital(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  95 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/linkuse")
/*     */   @Produces({"text/plain"})
/*     */   public String getCptLinkage(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 110 */     hashMap.putAll(getService(user).getCptLinkage(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 111 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalForm")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalForm(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 126 */     hashMap.putAll(getService(user).getCapitalForm(ParamUtil.request2Map(paramHttpServletRequest), user, paramHttpServletRequest));
/* 127 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalEditForm")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalEditForm(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 140 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 141 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 142 */     hashMap.putAll(getService(user).getCapitalEditForm(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 143 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doCapitalEdit")
/*     */   @Produces({"text/plain"})
/*     */   public String doCapitalEdit(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 156 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 157 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 158 */     hashMap.putAll(getService(user).doCapitalEdit(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 159 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalFlowList")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalFlowList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 172 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 173 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 174 */     hashMap.putAll(getService(user).getCapitalFlowList(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 175 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalChangeList")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalChangeList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 188 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 189 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 190 */     hashMap.putAll(getService(user).getCapitalChangeList(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 191 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalMendForm")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalMendForm(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 204 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 205 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 206 */     hashMap.putAll(getService(user).getCapitalMendForm(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 207 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doCapitalMend")
/*     */   @Produces({"text/plain"})
/*     */   public String doCapitalMend(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 220 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 221 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 222 */     hashMap.putAll(getService(user).doCapitalMend(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 223 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doCapitalDel")
/*     */   @Produces({"text/plain"})
/*     */   public String doCapitalDel(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 236 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 237 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 238 */     hashMap.putAll(getService(user).doCapitalDel(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 239 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/web/CapitalManagerAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */