/*     */ package com.api.cpt.mobile.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.cpt.mobile.service.CapitalInventoryService;
/*     */ import com.api.cpt.mobile.service.impl.CapitalInventoryServiceImpl;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/cpt/moblie/inventory")
/*     */ public class CapitalInventoryAction
/*     */ {
/*     */   private CapitalInventoryService getService(User paramUser) {
/*  31 */     return (CapitalInventoryService)ServiceUtil.getService(CapitalInventoryServiceImpl.class, paramUser);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getMyInventoryList")
/*     */   @Produces({"text/plain"})
/*     */   public String getMyInventoryList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  45 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  46 */     hashMap.putAll(getService(user).getMyInventoryList(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  47 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getInventoryListDetail")
/*     */   @Produces({"text/plain"})
/*     */   public String getInventoryListDetail(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  62 */     hashMap.putAll(getService(user).getInventoryListDetail(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  63 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getInventoryForm")
/*     */   @Produces({"text/plain"})
/*     */   public String getInventoryForm(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  77 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  78 */     hashMap.putAll(getService(user).getInventoryForm(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  79 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doInventory")
/*     */   @Produces({"text/plain"})
/*     */   public String doInventory(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  93 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  94 */     hashMap.putAll(getService(user).doInventory(ParamUtil.request2Map(paramHttpServletRequest), user));
/*  95 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/doSubmitInventory")
/*     */   @Produces({"text/plain"})
/*     */   public String doSubmitInventory(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 110 */     hashMap.putAll(getService(user).doSubmitInventory(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 111 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/web/CapitalInventoryAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */