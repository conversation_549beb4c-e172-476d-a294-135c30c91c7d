/*    */ package com.api.cpt.mobile.web;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.api.cpt.mobile.service.CapitalReportService;
/*    */ import com.api.cpt.mobile.service.impl.CapitalReportServiceImpl;
/*    */ import com.engine.common.util.ParamUtil;
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import java.util.HashMap;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.ws.rs.GET;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.Produces;
/*    */ import javax.ws.rs.core.Context;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Path("/cpt/moblie/capitalreport")
/*    */ public class CapitalReportAction
/*    */ {
/*    */   private CapitalReportService getService(User paramUser) {
/* 31 */     return (CapitalReportService)ServiceUtil.getService(CapitalReportServiceImpl.class, paramUser);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @GET
/*    */   @Path("/getCapitalReport")
/*    */   @Produces({"text/plain"})
/*    */   public String getCapitalReport(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 45 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 46 */     hashMap.putAll(getService(user).getCapitalReportData(ParamUtil.request2Map(paramHttpServletRequest), user));
/* 47 */     return JSONObject.toJSONString(hashMap);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/web/CapitalReportAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */