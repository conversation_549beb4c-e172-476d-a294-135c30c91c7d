/*     */ package com.api.cpt.mobile;
/*     */ 
/*     */ import com.weaver.formmodel.mobile.manager.MobileUserInit;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/cpt/capitalreport")
/*     */ public class CptCapitalReportAction
/*     */   extends BaseBean
/*     */ {
/*     */   @GET
/*     */   @Path("/getCapitalGroupSum")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalGroupSum(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  70 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  71 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  72 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/*  74 */       int i = Util.getIntValue(paramHttpServletRequest.getParameter("pageindex"), 1);
/*  75 */       int j = Util.getIntValue(paramHttpServletRequest.getParameter("pagesize"), 10);
/*  76 */       int k = 0;
/*  77 */       int m = 0;
/*     */       
/*  79 */       String str1 = "";
/*  80 */       String str2 = "";
/*  81 */       String str3 = "";
/*  82 */       String str4 = "";
/*  83 */       String str5 = " where t1.isdata='2'  and  exists( select 1 from CptCapitalShareInfo  t2 where  t1.id=t2.relateditemid  and ( " + commonShareManager.getShareWhereByUser("cpt", user) + " ) ) ";
/*  84 */       if (!"".equals(str1)) {
/*  85 */         str5 = str5 + " and t1.capitalgroupid='" + str1 + "' ";
/*     */       }
/*  87 */       if (!str2.equals("")) {
/*  88 */         str5 = str5 + " and t1.createdate>='" + str2 + "' ";
/*     */       }
/*  90 */       if (!str3.equals("")) {
/*  91 */         str5 = str5 + " and t1.createdate<='" + str3 + "' ";
/*     */       }
/*  93 */       if (!str4.equals("")) {
/*  94 */         str5 = str5 + " and t1.blongsubcompany='" + str4 + "' ";
/*     */       }
/*     */       
/*  97 */       String str6 = " t5.lv1groupid,COUNT(t1.id) AS resultcount ";
/*  98 */       String str7 = " CptCapital  t1 ";
/*  99 */       if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/* 100 */         str7 = str7 + " join ( select t4.id as cptgroupid,t3.id as lv1groupid,t3.assortmentname from CptCapitalAssortment t3,CptCapitalAssortment t4 where ( t4.supassortmentstr like '%|'+convert(varchar,t3.id)+'|%' or t4.id=t3.id ) and t3.supassortmentid = 0 ) t5 on t5.cptgroupid=t1.capitalgroupid";
/*     */       } else {
/* 102 */         str7 = str7 + " join ( select t4.id as cptgroupid,t3.id as lv1groupid,t3.assortmentname from CptCapitalAssortment t3,CptCapitalAssortment t4 where ( t4.supassortmentstr like '%|'||to_char(t3.id)||'|%' or t4.id=t3.id ) and t3.supassortmentid = 0 ) t5 on t5.cptgroupid=t1.capitalgroupid";
/*     */       } 
/*     */       
/* 105 */       String str8 = "(select " + str6 + " from " + str7 + str5 + " group by t5.lv1groupid)t";
/* 106 */       String str9 = " select count(1) as c from " + str8;
/* 107 */       recordSet.executeSql(str9);
/* 108 */       if (recordSet.next()) {
/* 109 */         k = recordSet.getInt("c");
/*     */       }
/* 111 */       int n = 0;
/* 112 */       String str10 = "select sum(resultcount) sum from " + str8;
/* 113 */       recordSet.executeSql(str10);
/* 114 */       if (recordSet.next()) {
/* 115 */         n = recordSet.getInt("sum");
/*     */       }
/* 117 */       if (i <= 0) i = 1; 
/* 118 */       if (j <= 0) j = 10;
/*     */       
/* 120 */       if (k <= 0) m = 0; 
/* 121 */       m = k / j + ((k % j > 0) ? 1 : 0);
/*     */       
/* 123 */       String str11 = "";
/* 124 */       if (recordSet.getDBType().equals("oracle")) {
/* 125 */         str11 = "select *  from " + str8 + " order by lv1groupid  ";
/* 126 */         str11 = "select t1.*,rownum rn from (" + str11 + ") t1 where rownum <= " + (i * j);
/* 127 */         str11 = "select t2.* from (" + str11 + ") t2 where rn > " + ((i - 1) * j);
/*     */       }
/* 129 */       else if (i > 1) {
/* 130 */         int i1 = j;
/* 131 */         if (j * i > k) {
/* 132 */           i1 = k - j * (i - 1);
/*     */         }
/* 134 */         str11 = "select top " + (i * j) + " *  from " + str8 + " order by lv1groupid  ";
/* 135 */         str11 = "select top " + i1 + " t1.* from (" + str11 + ") t1 order by t1.lv1groupid desc";
/* 136 */         str11 = "select top " + i1 + " t2.* from (" + str11 + ") t2 order by t2.lv1groupid  ";
/*     */       } else {
/* 138 */         str11 = "select top " + j + " *  from " + str8 + " order by lv1groupid ";
/*     */       } 
/*     */       
/* 141 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 142 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 144 */         if (i <= m) {
/* 145 */           connStatement.setStatementSql(str11);
/* 146 */           connStatement.executeQuery();
/*     */           
/* 148 */           while (connStatement.next()) {
/* 149 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 150 */             String str = Util.null2String(connStatement.getString("lv1groupid"));
/* 151 */             hashMap1.put("groupid", str);
/* 152 */             hashMap1.put("groupidname", capitalAssortmentComInfo.getAssortmentName(str));
/* 153 */             hashMap1.put("resultcount", Util.null2String(connStatement.getString("resultcount")));
/* 154 */             arrayList.add(hashMap1);
/*     */           } 
/*     */         } 
/* 157 */       } catch (Exception exception) {
/* 158 */         writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalGroupSum : ", exception);
/*     */       } finally {
/* 160 */         connStatement.close();
/*     */       } 
/* 162 */       hashMap.put("sum", Integer.valueOf(n));
/* 163 */       hashMap.put("totalsize", Integer.valueOf(k));
/* 164 */       hashMap.put("datas", arrayList);
/*     */     }
/* 166 */     catch (Exception exception) {
/* 167 */       writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalGroupSum : ", exception);
/* 168 */       hashMap.put("error", "no data");
/*     */     } 
/* 170 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/* 171 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalResourceSum")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalResourceSum(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 184 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 185 */     CommonShareManager commonShareManager = new CommonShareManager();
/* 186 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 187 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 189 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*     */       
/* 191 */       int i = Util.getIntValue(paramHttpServletRequest.getParameter("pageindex"), 1);
/* 192 */       int j = Util.getIntValue(paramHttpServletRequest.getParameter("pagesize"), 10);
/* 193 */       int k = 0;
/* 194 */       int m = 0;
/*     */       
/* 196 */       String str1 = "";
/* 197 */       String str2 = "";
/* 198 */       String str3 = "";
/* 199 */       String str4 = "";
/*     */       
/* 201 */       String str5 = " where t1.isdata='2' and t1.resourceid > 1 and exists(select 1 from CptCapitalShareInfo t2 where t2.relateditemid=t1.id and ( " + commonShareManager.getShareWhereByUser("cpt", user) + " ) ) ";
/* 202 */       if (!"".equals(str1)) {
/* 203 */         str5 = str5 + " and t1.resourceid='" + str1 + "' ";
/*     */       }
/* 205 */       if (!str2.equals("")) {
/* 206 */         str5 = str5 + " and t1.createdate>='" + str2 + "' ";
/*     */       }
/* 208 */       if (!str3.equals("")) {
/* 209 */         str5 = str5 + " and t1.createdate<='" + str3 + "' ";
/*     */       }
/* 211 */       if (!str4.equals("")) {
/* 212 */         str5 = str5 + " and t1.blongsubcompany='" + str4 + "' ";
/*     */       }
/*     */       
/* 215 */       String str6 = " t1.resourceid,COUNT(t1.id) AS resultcount ";
/* 216 */       String str7 = " CptCapital  t1  ";
/*     */       
/* 218 */       String str8 = "(select " + str6 + " from " + str7 + str5 + " group by t1.resourceid)t";
/* 219 */       String str9 = " select count(1) as c from " + str8;
/* 220 */       recordSet.executeSql(str9);
/* 221 */       if (recordSet.next()) {
/* 222 */         k = recordSet.getInt("c");
/*     */       }
/* 224 */       int n = 0;
/* 225 */       String str10 = "select sum(resultcount) sum from " + str8;
/* 226 */       recordSet.executeSql(str10);
/* 227 */       if (recordSet.next()) {
/* 228 */         n = recordSet.getInt("sum");
/*     */       }
/* 230 */       if (i <= 0) i = 1; 
/* 231 */       if (j <= 0) j = 10;
/*     */       
/* 233 */       if (k <= 0) m = 0; 
/* 234 */       m = k / j + ((k % j > 0) ? 1 : 0);
/*     */       
/* 236 */       String str11 = "";
/* 237 */       if (recordSet.getDBType().equals("oracle")) {
/* 238 */         str11 = "select *  from " + str8 + " order by resourceid  ";
/* 239 */         str11 = "select t1.*,rownum rn from (" + str11 + ") t1 where rownum <= " + (i * j);
/* 240 */         str11 = "select t2.* from (" + str11 + ") t2 where rn > " + ((i - 1) * j);
/*     */       }
/* 242 */       else if (i > 1) {
/* 243 */         int i1 = j;
/* 244 */         if (j * i > k) {
/* 245 */           i1 = k - j * (i - 1);
/*     */         }
/* 247 */         str11 = "select top " + (i * j) + " *  from " + str8 + " order by resourceid  ";
/* 248 */         str11 = "select top " + i1 + " t1.* from (" + str11 + ") t1 order by t1.resourceid desc";
/* 249 */         str11 = "select top " + i1 + " t2.* from (" + str11 + ") t2 order by t2.resourceid  ";
/*     */       } else {
/* 251 */         str11 = "select top " + j + " *  from " + str8 + " order by resourceid ";
/*     */       } 
/*     */       
/* 254 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 255 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 257 */         if (i <= m) {
/* 258 */           connStatement.setStatementSql(str11);
/* 259 */           connStatement.executeQuery();
/*     */           
/* 261 */           while (connStatement.next()) {
/* 262 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 263 */             String str = Util.null2String(connStatement.getString("resourceid"));
/* 264 */             hashMap1.put("resourceid", str);
/* 265 */             hashMap1.put("resourcename", resourceComInfo.getResourcename(str));
/* 266 */             hashMap1.put("resultcount", Util.null2String(connStatement.getString("resultcount")));
/* 267 */             arrayList.add(hashMap1);
/*     */           } 
/*     */         } 
/* 270 */       } catch (Exception exception) {
/* 271 */         writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalResourceSum : ", exception);
/*     */       } finally {
/* 273 */         connStatement.close();
/*     */       } 
/* 275 */       hashMap.put("sum", Integer.valueOf(n));
/* 276 */       hashMap.put("totalsize", Integer.valueOf(k));
/* 277 */       hashMap.put("datas", arrayList);
/*     */     }
/* 279 */     catch (Exception exception) {
/* 280 */       writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalResourceSum : ", exception);
/* 281 */       hashMap.put("error", "no data");
/*     */     } 
/* 283 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/* 284 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalDeptSum")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalDeptSum(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 297 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 298 */     CommonShareManager commonShareManager = new CommonShareManager();
/* 299 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 300 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 302 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*     */       
/* 304 */       int i = Util.getIntValue(paramHttpServletRequest.getParameter("pageindex"), 1);
/* 305 */       int j = Util.getIntValue(paramHttpServletRequest.getParameter("pagesize"), 10);
/* 306 */       int k = 0;
/* 307 */       int m = 0;
/*     */       
/* 309 */       String str1 = "";
/* 310 */       String str2 = "";
/* 311 */       String str3 = "";
/* 312 */       String str4 = "";
/*     */       
/* 314 */       String str5 = " where t1.isdata='2' and t1.departmentid>0  and exists(select 1 from CptCapitalShareInfo t2 where t2.relateditemid=t1.id and (" + commonShareManager.getShareWhereByUser("cpt", user) + ") ) ";
/* 315 */       if (!"".equals(str1)) {
/* 316 */         str5 = str5 + " and t1.departmentid='" + str1 + "' ";
/*     */       }
/* 318 */       if (!str2.equals("")) {
/* 319 */         str5 = str5 + " and t1.createdate>='" + str2 + "' ";
/*     */       }
/* 321 */       if (!str3.equals("")) {
/* 322 */         str5 = str5 + " and t1.createdate<='" + str3 + "' ";
/*     */       }
/* 324 */       if (!str4.equals("")) {
/* 325 */         str5 = str5 + " and t1.blongsubcompany='" + str4 + "' ";
/*     */       }
/*     */ 
/*     */       
/* 329 */       String str6 = " t1.departmentid,COUNT(t1.id) AS resultcount ";
/* 330 */       String str7 = " CptCapital  t1  ";
/*     */       
/* 332 */       String str8 = "(select " + str6 + " from " + str7 + str5 + " group by t1.departmentid)t";
/* 333 */       String str9 = " select count(1) as c from " + str8;
/* 334 */       recordSet.executeSql(str9);
/* 335 */       if (recordSet.next()) {
/* 336 */         k = recordSet.getInt("c");
/*     */       }
/* 338 */       int n = 0;
/* 339 */       String str10 = "select sum(resultcount) sum from " + str8;
/* 340 */       recordSet.executeSql(str10);
/* 341 */       if (recordSet.next()) {
/* 342 */         n = recordSet.getInt("sum");
/*     */       }
/* 344 */       if (i <= 0) i = 1; 
/* 345 */       if (j <= 0) j = 10;
/*     */       
/* 347 */       if (k <= 0) m = 0; 
/* 348 */       m = k / j + ((k % j > 0) ? 1 : 0);
/*     */       
/* 350 */       String str11 = "";
/* 351 */       if (recordSet.getDBType().equals("oracle")) {
/* 352 */         str11 = "select *  from " + str8 + " order by departmentid ";
/* 353 */         str11 = "select t1.*,rownum rn from (" + str11 + ") t1 where rownum <= " + (i * j);
/* 354 */         str11 = "select t2.* from (" + str11 + ") t2 where rn > " + ((i - 1) * j);
/*     */       }
/* 356 */       else if (i > 1) {
/* 357 */         int i1 = j;
/* 358 */         if (j * i > k) {
/* 359 */           i1 = k - j * (i - 1);
/*     */         }
/* 361 */         str11 = "select top " + (i * j) + " *  from " + str8 + " order by departmentid  ";
/* 362 */         str11 = "select top " + i1 + " t1.* from (" + str11 + ") t1 order by t1.departmentid desc";
/* 363 */         str11 = "select top " + i1 + " t2.* from (" + str11 + ") t2 order by t2.departmentid  ";
/*     */       } else {
/* 365 */         str11 = "select top " + j + " *  from " + str8 + " order by departmentid ";
/*     */       } 
/*     */       
/* 368 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 369 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 371 */         if (i <= m) {
/* 372 */           connStatement.setStatementSql(str11);
/* 373 */           connStatement.executeQuery();
/*     */           
/* 375 */           while (connStatement.next()) {
/* 376 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 377 */             String str = Util.null2String(connStatement.getString("departmentid"));
/* 378 */             hashMap1.put("departmentid", str);
/* 379 */             hashMap1.put("departmentname", departmentComInfo.getDepartmentname(str));
/* 380 */             hashMap1.put("resultcount", Util.null2String(connStatement.getString("resultcount")));
/* 381 */             arrayList.add(hashMap1);
/*     */           } 
/*     */         } 
/* 384 */       } catch (Exception exception) {
/* 385 */         writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalDeptSum : ", exception);
/*     */       } finally {
/* 387 */         connStatement.close();
/*     */       } 
/* 389 */       hashMap.put("sum", Integer.valueOf(n));
/* 390 */       hashMap.put("totalsize", Integer.valueOf(k));
/* 391 */       hashMap.put("datas", arrayList);
/*     */     }
/* 393 */     catch (Exception exception) {
/* 394 */       writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalDeptSum : ", exception);
/* 395 */       hashMap.put("error", "no data");
/*     */     } 
/* 397 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/* 398 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCapitalStateSum")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalStateSum(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 411 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 412 */     CommonShareManager commonShareManager = new CommonShareManager();
/* 413 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 414 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 416 */       CapitalStateComInfo capitalStateComInfo = new CapitalStateComInfo();
/*     */       
/* 418 */       int i = Util.getIntValue(paramHttpServletRequest.getParameter("pageindex"), 1);
/* 419 */       int j = Util.getIntValue(paramHttpServletRequest.getParameter("pagesize"), 10);
/* 420 */       int k = 0;
/* 421 */       int m = 0;
/*     */       
/* 423 */       String str1 = "";
/* 424 */       String str2 = "";
/* 425 */       String str3 = "";
/* 426 */       String str4 = "";
/*     */       
/* 428 */       String str5 = " where t1.isdata='2'   and exists(select 1 from CptCapitalShareInfo t2 where t2.relateditemid=t1.id and (" + commonShareManager.getShareWhereByUser("cpt", user) + ") ) ";
/* 429 */       if (!"".equals(str1)) {
/* 430 */         str5 = str5 + " and t1.stateid='" + str1 + "' ";
/*     */       }
/* 432 */       if (!str2.equals("")) {
/* 433 */         str5 = str5 + " and t1.createdate>='" + str2 + "' ";
/*     */       }
/* 435 */       if (!str3.equals("")) {
/* 436 */         str5 = str5 + " and t1.createdate<='" + str3 + "' ";
/*     */       }
/* 438 */       if (!str4.equals("")) {
/* 439 */         str5 = str5 + " and t1.blongsubcompany='" + str4 + "' ";
/*     */       }
/*     */       
/* 442 */       String str6 = " t1.stateid,COUNT(t1.id) AS resultcount ";
/* 443 */       String str7 = " CptCapital  t1  ";
/*     */       
/* 445 */       String str8 = "(select " + str6 + " from " + str7 + str5 + " group by t1.stateid)t";
/* 446 */       String str9 = " select count(1) as c from " + str8;
/* 447 */       recordSet.executeSql(str9);
/* 448 */       if (recordSet.next()) {
/* 449 */         k = recordSet.getInt("c");
/*     */       }
/* 451 */       int n = 0;
/* 452 */       String str10 = "select sum(resultcount) sum from " + str8;
/* 453 */       recordSet.executeSql(str10);
/* 454 */       if (recordSet.next()) {
/* 455 */         n = recordSet.getInt("sum");
/*     */       }
/* 457 */       if (i <= 0) i = 1; 
/* 458 */       if (j <= 0) j = 10;
/*     */       
/* 460 */       if (k <= 0) m = 0; 
/* 461 */       m = k / j + ((k % j > 0) ? 1 : 0);
/*     */       
/* 463 */       String str11 = "";
/* 464 */       if (recordSet.getDBType().equals("oracle")) {
/* 465 */         str11 = "select *  from " + str8 + " order by stateid  ";
/* 466 */         str11 = "select t1.*,rownum rn from (" + str11 + ") t1 where rownum <= " + (i * j);
/* 467 */         str11 = "select t2.* from (" + str11 + ") t2 where rn > " + ((i - 1) * j);
/*     */       }
/* 469 */       else if (i > 1) {
/* 470 */         int i1 = j;
/* 471 */         if (j * i > k) {
/* 472 */           i1 = k - j * (i - 1);
/*     */         }
/* 474 */         str11 = "select top " + (i * j) + " *  from " + str8 + " order by stateid  ";
/* 475 */         str11 = "select top " + i1 + " t1.* from (" + str11 + ") t1 order by t1.stateid desc";
/* 476 */         str11 = "select top " + i1 + " t2.* from (" + str11 + ") t2 order by t2.stateid  ";
/*     */       } else {
/* 478 */         str11 = "select top " + j + " *  from " + str8 + " order by stateid ";
/*     */       } 
/*     */       
/* 481 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 482 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 484 */         if (i <= m) {
/* 485 */           connStatement.setStatementSql(str11);
/* 486 */           connStatement.executeQuery();
/*     */           
/* 488 */           while (connStatement.next()) {
/* 489 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 490 */             String str = Util.null2String(connStatement.getString("stateid"));
/* 491 */             hashMap1.put("stateid", str);
/* 492 */             hashMap1.put("stateiname", capitalStateComInfo.getCapitalStatename(str));
/* 493 */             hashMap1.put("resultcount", Util.null2String(connStatement.getString("resultcount")));
/* 494 */             arrayList.add(hashMap1);
/*     */           } 
/*     */         } 
/* 497 */       } catch (Exception exception) {
/* 498 */         writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalStateSum : ", exception);
/*     */       } finally {
/* 500 */         connStatement.close();
/*     */       } 
/* 502 */       hashMap.put("sum", Integer.valueOf(n));
/* 503 */       hashMap.put("totalsize", Integer.valueOf(k));
/* 504 */       hashMap.put("datas", arrayList);
/*     */     }
/* 506 */     catch (Exception exception) {
/* 507 */       writeLog("com.api.cpt.mobile.CptCapitalReportAction.getCapitalStateSum : ", exception);
/* 508 */       hashMap.put("error", "no data");
/*     */     } 
/* 510 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/* 511 */     return jSONObject.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/CptCapitalReportAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */