/*     */ package com.api.cpt.mobile;
/*     */ 
/*     */ import com.weaver.formmodel.mobile.manager.MobileUserInit;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.cpt.util.CptFieldManager;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.general.browserData.BrowserManager;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.proj.util.SQLUtil;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ import weaver.workflow.browserdatadefinition.ConditionField;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/cpt/cptcapitalbrowser")
/*     */ public class CptCapitalBrowserAction
/*     */   extends BaseBean
/*     */ {
/*     */   @GET
/*     */   @Path("/getCapitalBrowser")
/*     */   @Produces({"text/plain"})
/*     */   public String getCapitalBrowser(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  62 */     CptFieldManager cptFieldManager = new CptFieldManager();
/*  63 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*     */     
/*  65 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  66 */     RecordSet recordSet1 = new RecordSet();
/*     */ 
/*     */     
/*  69 */     CptWfUtil cptWfUtil = new CptWfUtil();
/*  70 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("sqlwhere"));
/*  71 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("wfid")), -1);
/*  72 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("fieldid")), -1);
/*  73 */     int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("viewtype")), -1);
/*  74 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("cptstateid"));
/*  75 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("cptsptcount"));
/*  76 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("isdata"));
/*     */     
/*  78 */     String str5 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("inculdeNumZero")), "1");
/*  79 */     String str6 = "0";
/*  80 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("billid"));
/*  81 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("wfid"));
/*  82 */     int m = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("requestid")), -1);
/*     */     
/*  84 */     if (!str7.equals("")) {
/*  85 */       int i7 = Util.getIntValue(str7);
/*  86 */       switch (i7) {
/*     */         case 220:
/*  88 */           str1 = " where isdata='2'  ";
/*  89 */           str3 = "1";
/*  90 */           str2 = "1";
/*  91 */           str5 = "0";
/*     */           break;
/*     */         case 222:
/*  94 */           str1 = " where isdata='2'  ";
/*  95 */           str3 = "1";
/*  96 */           str2 = "1,2,3";
/*  97 */           str5 = "0";
/*     */           break;
/*     */         case 224:
/* 100 */           str1 = " where isdata='2'  ";
/*     */           
/* 102 */           str2 = "4,2,3";
/* 103 */           str5 = "0";
/*     */           break;
/*     */         case 221:
/* 106 */           str1 = " where isdata='2'  ";
/* 107 */           str2 = "1,2,3,4";
/* 108 */           str5 = "0";
/*     */           break;
/*     */         case 201:
/* 111 */           str1 = " where isdata='2'  ";
/* 112 */           str2 = "1,2,3,4";
/* 113 */           str5 = "0";
/*     */           break;
/*     */       } 
/*     */     } 
/* 117 */     if (!"".equals(str8)) {
/* 118 */       String str = cptWfUtil.getWftype(str8);
/* 119 */       if (!"".equals(str)) {
/* 120 */         if ("fetch".equalsIgnoreCase(str)) {
/* 121 */           str2 = "1";
/* 122 */         } else if ("lend".equalsIgnoreCase(str)) {
/* 123 */           str2 = "1";
/* 124 */           str3 = "1";
/* 125 */         } else if ("move".equalsIgnoreCase(str)) {
/* 126 */           str2 = "2";
/* 127 */           if (str1.indexOf("where") > -1) {
/* 128 */             str1 = str1 + " and resourceid='" + user.getUID() + "'";
/*     */           } else {
/* 130 */             str1 = str1 + " where resourceid='" + user.getUID() + "'";
/*     */           } 
/* 132 */         } else if ("back".equalsIgnoreCase(str)) {
/* 133 */           str2 = "2,3,4";
/* 134 */           if (str1.indexOf("where") > -1) {
/* 135 */             str1 = str1 + " and resourceid='" + user.getUID() + "'";
/*     */           } else {
/* 137 */             str1 = str1 + " where resourceid='" + user.getUID() + "'";
/*     */           } 
/* 139 */         } else if ("discard".equalsIgnoreCase(str)) {
/* 140 */           str2 = "1,2,3,4";
/* 141 */           if (str1.indexOf("where") > -1) {
/* 142 */             str1 = str1 + " and resourceid='" + user.getUID() + "'";
/*     */           } else {
/* 144 */             str1 = str1 + " where resourceid='" + user.getUID() + "'";
/*     */           } 
/* 146 */         } else if ("mend".equalsIgnoreCase(str)) {
/* 147 */           str2 = "1,2,3";
/* 148 */           str3 = "1";
/* 149 */         } else if ("loss".equalsIgnoreCase(str)) {
/* 150 */           str2 = "1,2,3,4";
/*     */         } 
/* 152 */         str5 = "0";
/*     */       } 
/*     */     } 
/*     */     
/* 156 */     if (str1.indexOf("isdata") != -1) {
/* 157 */       if (str1.substring(str1.indexOf("isdata='") + 8, str1.indexOf("isdata='") + 9).equals("2")) {
/* 158 */         str6 = "1";
/* 159 */       } else if (str1.substring(str1.indexOf("isdata=") + 7, str1.indexOf("isdata=") + 8).equals("2")) {
/* 160 */         str6 = "1";
/*     */       }
/*     */     
/*     */     }
/* 164 */     else if (str4.equals("") || str4.equals("2")) {
/* 165 */       str6 = "1";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 170 */     recordSet1.executeSql("select cptdetachable from SystemSet");
/* 171 */     int n = 0;
/* 172 */     if (recordSet1.next()) {
/* 173 */       n = recordSet1.getInt("cptdetachable");
/*     */     }
/*     */     
/* 176 */     int i1 = user.getUserSubCompany1();
/* 177 */     int i2 = user.getUID();
/* 178 */     char c = Util.getSeparator();
/*     */ 
/*     */     
/* 181 */     String str9 = " ";
/* 182 */     String str10 = str1;
/* 183 */     String str11 = "";
/* 184 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", user)) {
/* 185 */       str11 = "Capital:Maintenance";
/*     */     }
/* 187 */     String str12 = "";
/*     */     
/* 189 */     boolean bool = false;
/* 190 */     byte b = 0;
/* 191 */     if (!str1.equals("")) {
/* 192 */       if (!bool) {
/* 193 */         bool = true;
/* 194 */         str9 = str9 + str10;
/*     */       } 
/* 196 */       if (str1.indexOf("isdata") != -1) {
/* 197 */         String str = str1.substring(str1.indexOf("isdata") + 1);
/* 198 */         int i7 = str.indexOf("'1'");
/* 199 */         int i8 = str.indexOf("'2'");
/* 200 */         int i9 = str.indexOf("1");
/* 201 */         int i10 = str.indexOf("2");
/* 202 */         if (i7 == -1 && i8 > -1) {
/* 203 */           b = 2;
/* 204 */         } else if (i7 > -1 && i8 == -1) {
/* 205 */           b = 1;
/* 206 */         } else if (i9 == -1 && i10 > -1) {
/* 207 */           b = 2;
/* 208 */         } else if (i9 > -1 && i10 == -1) {
/* 209 */           b = 1;
/*     */         }
/*     */       
/* 212 */       } else if (!bool) {
/* 213 */         bool = true;
/* 214 */         str9 = str9 + " where isdata = '2' ";
/*     */       } else {
/* 216 */         str9 = str9 + " and isdata = '2' ";
/*     */       }
/*     */     
/*     */     }
/* 220 */     else if (!bool) {
/* 221 */       bool = true;
/* 222 */       str9 = str9 + " where isdata = '" + ("0".equals(str6) ? "1" : "2") + "' ";
/*     */     } else {
/* 224 */       str9 = str9 + " and isdata = '" + ("0".equals(str6) ? "1" : "2") + "' ";
/*     */     } 
/*     */     
/* 227 */     if (b == 0) {
/* 228 */       b = 2;
/*     */     }
/* 230 */     if (!str2.equals("")) {
/* 231 */       str9 = str9 + " and stateid in (";
/* 232 */       str9 = str9 + Util.fromScreen2(str2, user.getLanguage());
/* 233 */       str9 = str9 + ") ";
/*     */     } 
/* 235 */     if (!str3.equals("")) {
/* 236 */       str9 = str9 + " and sptcount = '";
/* 237 */       str9 = str9 + Util.fromScreen2(str3, user.getLanguage());
/* 238 */       str9 = str9 + "'";
/*     */     } 
/*     */ 
/*     */     
/* 242 */     if (n == 1 && i2 != 1) {
/* 243 */       if (b == 2) {
/* 244 */         String str = "";
/* 245 */         recordSet1.executeProc("HrmRoleSR_SeByURId", "" + i2 + c + str11);
/* 246 */         while (recordSet1.next()) {
/* 247 */           str12 = recordSet1.getString("subcompanyid");
/* 248 */           str = str + ", " + str12;
/*     */         } 
/* 250 */         if (!"".equals(str)) {
/* 251 */           str = str.substring(1);
/* 252 */           str9 = str9 + " and blongsubcompany in (" + str + ") ";
/*     */         } else {
/* 254 */           str9 = str9 + " and blongsubcompany in (" + i1 + ") ";
/*     */         } 
/* 256 */       } else if (b == 1) {
/* 257 */         CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 258 */         int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(user.getUID(), str11);
/* 259 */         String str = "";
/* 260 */         for (byte b1 = 0; b1 < arrayOfInt.length; b1++) {
/* 261 */           if (arrayOfInt[b1] > 0) {
/* 262 */             str = str + (str.equals("") ? "" : ",") + arrayOfInt[b1];
/*     */           }
/*     */         } 
/* 265 */         if (str.equals(""))
/* 266 */           str = user.getUserSubCompany1() + ""; 
/* 267 */         if (!"".equals(str)) {
/* 268 */           str9 = str9 + " and blongsubcompany in (" + str + ") ";
/*     */         } else {
/* 270 */           str9 = str9 + " and blongsubcompany in (" + str + ") ";
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 276 */     if ("1".equals(str6)) {
/* 277 */       List<?> list = null;
/* 278 */       if (paramHttpServletRequest.getParameter("bdf_wfid") != null && (list = ConditionField.readAll(i, j, k)).size() > 0) {
/* 279 */         for (byte b1 = 0; b1 < list.size(); b1++) {
/* 280 */           ConditionField conditionField = (ConditionField)list.get(b1);
/* 281 */           String str23 = conditionField.getFieldName();
/* 282 */           String str24 = conditionField.getValue();
/* 283 */           boolean bool1 = conditionField.isHide();
/* 284 */           boolean bool2 = conditionField.isReadonly();
/* 285 */           if (bool1 || bool2) {
/* 286 */             if (!"".equals(str24) && "mark".equalsIgnoreCase(str23)) {
/* 287 */               str9 = str9 + " and t1.mark like '%" + str24 + "%' ";
/* 288 */             } else if (!"".equals(str24) && "fnamark".equalsIgnoreCase(str23)) {
/* 289 */               str9 = str9 + " and t1.fnamark like '%" + str24 + "%' ";
/* 290 */             } else if (!"".equals(str24) && "name".equalsIgnoreCase(str23)) {
/* 291 */               str9 = str9 + " and t1.name like '%" + str24 + "%' ";
/* 292 */             } else if (!"".equals(str24) && "capitalSpec".equalsIgnoreCase(str23)) {
/* 293 */               str9 = str9 + " and t1.capitalSpec like '%" + str24 + "%' ";
/* 294 */             } else if ("departmentid".equalsIgnoreCase(str23)) {
/* 295 */               String str = conditionField.getValueType();
/* 296 */               if ("1".equals(str)) {
/* 297 */                 str24 = resourceComInfo.getDepartmentID("" + user.getUID());
/* 298 */               } else if ("3".equals(str)) {
/* 299 */                 str24 = "";
/* 300 */                 if (conditionField.isGetValueFromFormField()) {
/* 301 */                   str24 = Util.null2String(conditionField.getDepartmentIds(Util.null2String(paramHttpServletRequest.getParameter("bdf_" + str23)).split(",")[0]));
/*     */                 }
/*     */               } 
/* 304 */               if (!"".equals(str24)) {
/* 305 */                 str9 = str9 + " and t1.departmentid='" + str24 + "' ";
/*     */               }
/*     */             } 
/*     */           }
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 314 */     String str13 = ",";
/* 315 */     if (!"1".equals(str6)) {
/* 316 */       str13 = str13 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,";
/*     */     }
/*     */     
/* 319 */     RecordSet recordSet2 = new RecordSet();
/* 320 */     StringBuffer stringBuffer = new StringBuffer();
/* 321 */     String str14 = "select t1.*,t2.fieldname,t2.fieldlabel,t2.fieldhtmltype,t2.type,t2.issystem from cpt_browdef t1,cptDefineField t2 where t1.iscondition=1 and t1.fieldid=t2.id  order by t1.displayorder";
/* 322 */     recordSet2.executeSql(str14);
/* 323 */     while (recordSet2.next()) {
/* 324 */       String str23 = Util.null2String(recordSet2.getString("fieldname"));
/* 325 */       int i7 = Util.getIntValue(recordSet2.getString("fieldhtmltype"), 0);
/* 326 */       if (str13.contains("," + str23 + ","))
/*     */         continue; 
/* 328 */       if (i7 == 2 || i7 == 6 || i7 == 7) {
/*     */         continue;
/*     */       }
/* 331 */       String str24 = Util.null2String(recordSet2.getString("fieldid"));
/* 332 */       String str25 = Util.null2String(recordSet2.getString("fielddbtype"));
/* 333 */       String str26 = Util.null2String(recordSet2.getString("type"));
/* 334 */       String str27 = Util.null2String(paramHttpServletRequest.getParameter(str23));
/* 335 */       int i8 = Util.getIntValue(recordSet2.getString("issystem"), 0);
/* 336 */       if (i8 != 1) {
/* 337 */         str27 = Util.null2String(paramHttpServletRequest.getParameter("field" + str24));
/*     */       }
/*     */       
/* 340 */       if (!"".equals(str27)) {
/* 341 */         if (i7 == 3) {
/* 342 */           boolean bool1 = "true".equalsIgnoreCase(BrowserManager.browIsSingle("" + str26));
/* 343 */           if (bool1) {
/* 344 */             stringBuffer.append(" and t1." + str23 + " ='" + str27 + "'  "); continue;
/*     */           } 
/* 346 */           String str = recordSet2.getDBType();
/* 347 */           if ("oracle".equalsIgnoreCase(str)) {
/* 348 */             stringBuffer.append(SQLUtil.filteSql(recordSet2.getDBType(), " and ','+t1." + str23 + "+',' like '%," + str27 + ",%'  ")); continue;
/*     */           } 
/* 350 */           stringBuffer.append(" and ','+convert(varchar(2000),t1." + str23 + ")+',' like '%," + str27 + ",%'  ");
/*     */           continue;
/*     */         } 
/* 353 */         if (i7 == 4) {
/* 354 */           if ("1".equals(str27))
/* 355 */             stringBuffer.append(" and t1." + str23 + " ='" + str27 + "'  ");  continue;
/*     */         } 
/* 357 */         if (i7 == 5) {
/* 358 */           stringBuffer.append(" and exists(select 1 from cpt_SelectItem ttt2 where ttt2.fieldid=" + str24 + " and ttt2.selectvalue='" + str27 + "' and ttt2.selectvalue=t1." + str23 + " ) ");
/*     */           continue;
/*     */         } 
/* 361 */         stringBuffer.append(" and t1." + str23 + " like'%" + str27 + "%'  ");
/*     */       } 
/*     */     } 
/*     */     
/* 365 */     if (stringBuffer.length() > 5) {
/* 366 */       str9 = str9 + stringBuffer.toString();
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 371 */     if ("1".equals(str6)) {
/* 372 */       CommonShareManager commonShareManager = new CommonShareManager();
/* 373 */       commonShareManager.setAliasTableName("t2");
/* 374 */       str9 = str9 + " and exists(select 1 from CptCapitalShareInfo t2 where t2.relateditemid=t1.id and (" + commonShareManager.getShareWhereByUser("cpt", user) + ") ) ";
/*     */     } 
/*     */     
/* 377 */     String str15 = " t1.id ";
/* 378 */     String str16 = " t1.*," + ("oracle".equalsIgnoreCase(recordSet1.getDBType()) ? "(nvl(capitalnum,0)-nvl(frozennum,0))" : "(isnull(capitalnum,0)-isnull(frozennum,0))") + " cptnum ";
/* 379 */     String str17 = " CptCapital t1 ";
/*     */ 
/*     */     
/* 382 */     String str18 = "";
/* 383 */     if (!"1".equals(str5) && 2 == b) {
/* 384 */       str18 = " and  ";
/* 385 */       if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 386 */         str18 = str18 + "  (nvl(capitalnum,0)-nvl(frozennum,0))>0 ";
/*     */       } else {
/* 388 */         str18 = str18 + "  (isnull(capitalnum,0)-isnull(frozennum,0))>0 ";
/*     */       } 
/*     */     } 
/* 391 */     if (m > 0 && "1".equals(str6)) {
/* 392 */       String str23 = "select t1.currentnodetype,t1.workflowid,t2.formid from workflow_requestbase t1,workflow_base t2 where t1.workflowid=t2.id and t1.requestid=" + m;
/* 393 */       int i7 = 0;
/* 394 */       int i8 = 0;
/* 395 */       int i9 = 0;
/*     */       
/* 397 */       recordSet1.executeSql(str23);
/* 398 */       while (recordSet1.next()) {
/* 399 */         i7 = recordSet1.getInt("formid");
/* 400 */         i8 = recordSet1.getInt("workflowid");
/* 401 */         i9 = recordSet1.getInt("currentnodetype");
/*     */       } 
/* 403 */       recordSet1.executeSql("select wftype  from cpt_cptwfconf where wfid=" + i8);
/* 404 */       String str24 = "";
/* 405 */       if (recordSet1.next()) {
/* 406 */         str24 = Util.null2String(recordSet1.getString("wftype"));
/*     */       }
/* 408 */       if (!"apply".equals(str24) && 
/* 409 */         i9 > 0 && i9 < 3) {
/* 410 */         CptWfUtil cptWfUtil1 = new CptWfUtil();
/* 411 */         JSONObject jSONObject1 = cptWfUtil1.getCptwfInfo("" + i8);
/* 412 */         if (jSONObject1.length() > 0) {
/* 413 */           String str25 = "formtable_main_" + -i7;
/* 414 */           recordSet1.execute("select tablename from workflow_bill where id=" + i7);
/* 415 */           while (recordSet1.next()) {
/* 416 */             str25 = recordSet1.getString("tablename");
/*     */           }
/* 418 */           String str26 = str25;
/* 419 */           String str27 = jSONObject1.getString("zcname");
/* 420 */           String str28 = jSONObject1.getString("slname");
/* 421 */           int i10 = Util.getIntValue("" + jSONObject1.getInt("zctype"), 0);
/* 422 */           if (i10 == 1) {
/* 423 */             str26 = str26 + "_dt1";
/* 424 */           } else if (i10 == 2) {
/* 425 */             str26 = str26 + "_dt2";
/* 426 */           } else if (i10 == 3) {
/* 427 */             str26 = str26 + "_dt3";
/* 428 */           } else if (i10 == 4) {
/* 429 */             str26 = str26 + "_dt4";
/*     */           } 
/* 431 */           String str29 = "";
/* 432 */           if (!str26.equals(str25)) {
/* 433 */             str29 = " select d." + str27 + " as currentzcid,sum(d." + str28 + ") as currentreqnum from " + str25 + " m ," + str26 + " d where d.mainid=m.id and m.requestid=" + m + " group by d." + str27 + " ";
/*     */           } else {
/*     */             
/* 436 */             str29 = "select m." + str27 + " as currentzcid,sum(m." + str28 + ") as currentreqnum from " + str25 + " m  where  m.requestid=" + m + " group by m." + str27 + " ";
/*     */           } 
/*     */ 
/*     */ 
/*     */           
/* 441 */           str16 = " t2.currentreqnum,t1.*," + ("oracle".equalsIgnoreCase(recordSet1.getDBType()) ? "(nvl(capitalnum,0)-nvl(frozennum,0)+nvl(currentreqnum,0))" : "(isnull(capitalnum,0)-isnull(frozennum,0)+isnull(currentreqnum,0))") + " cptnum ";
/*     */           
/* 443 */           str17 = " CptCapital t1 left outer join (" + str29 + ") t2 on t2.currentzcid=t1.id ";
/*     */           
/* 445 */           if (!bool) {
/* 446 */             bool = true;
/* 447 */             str18 = " where ";
/*     */           } else {
/* 449 */             str18 = " and  ";
/*     */           } 
/* 451 */           if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 452 */             str18 = str18 + "  (nvl(capitalnum,0)-nvl(frozennum,0)+nvl(currentreqnum,0))>0 ";
/*     */           } else {
/* 454 */             str18 = str18 + "  (isnull(capitalnum,0)-isnull(frozennum,0)+isnull(currentreqnum,0))>0 ";
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 466 */     int i3 = Util.getIntValue(paramHttpServletRequest.getParameter("pageNo"), 1);
/* 467 */     int i4 = Util.getIntValue(paramHttpServletRequest.getParameter("pageSize"), 10);
/* 468 */     int i5 = 0;
/* 469 */     int i6 = 0;
/*     */     
/* 471 */     String str19 = str9 + str18;
/*     */     
/* 473 */     String str20 = "(select " + str16 + " from " + str17 + str19 + ")t";
/* 474 */     String str21 = " select count(1) as c from " + str20;
/* 475 */     recordSet1.executeSql(str21);
/* 476 */     if (recordSet1.next()) {
/* 477 */       i5 = recordSet1.getInt("c");
/*     */     }
/* 479 */     if (i3 <= 0) i3 = 1; 
/* 480 */     if (i4 <= 0) i4 = 10;
/*     */     
/* 482 */     if (i5 <= 0) i6 = 0; 
/* 483 */     i6 = i5 / i4 + ((i5 % i4 > 0) ? 1 : 0);
/*     */     
/* 485 */     String str22 = "";
/* 486 */     if (recordSet1.getDBType().equals("oracle")) {
/* 487 */       str22 = "select *  from " + str20 + " order by id ";
/* 488 */       str22 = "select t1.*,rownum rn from (" + str22 + ") t1 where rownum <= " + (i3 * i4);
/* 489 */       str22 = "select t2.* from (" + str22 + ") t2 where rn > " + ((i3 - 1) * i4);
/*     */     }
/* 491 */     else if (i3 > 1) {
/* 492 */       int i7 = i4;
/* 493 */       if (i4 * i3 > i5) {
/* 494 */         i7 = i5 - i4 * (i3 - 1);
/*     */       }
/* 496 */       str22 = "select top " + (i3 * i4) + " *  from " + str20 + " order by id ";
/* 497 */       str22 = "select top " + i7 + " t1.* from (" + str22 + ") t1 order by t1.id desc";
/* 498 */       str22 = "select top " + i7 + " t2.* from (" + str22 + ") t2 order by t2.id ";
/*     */     } else {
/* 500 */       str22 = "select top " + i4 + " *  from " + str20 + " order by id ";
/*     */     } 
/*     */     
/* 503 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 504 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 506 */       if (i3 <= i6) {
/* 507 */         connStatement.setStatementSql(str22);
/* 508 */         connStatement.executeQuery();
/*     */         
/* 510 */         while (connStatement.next()) {
/* 511 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 512 */           hashMap1.put("id", Util.null2String(connStatement.getString("id")));
/* 513 */           hashMap1.put("mark", Util.null2String(connStatement.getString("mark")));
/* 514 */           hashMap1.put("name", Util.null2String(connStatement.getString("name")));
/* 515 */           hashMap1.put("capitalspec", Util.null2String(connStatement.getString("capitalspec")));
/*     */           
/* 517 */           String str23 = Util.null2String(connStatement.getString("stateid"));
/* 518 */           hashMap1.put("statename", cptFieldManager.getBrowserFieldvalue(str23, "18+-12+3+243"));
/*     */           
/* 520 */           String str24 = Util.null2String(connStatement.getString("departmentid"));
/* 521 */           hashMap1.put("departmentid", str24);
/* 522 */           hashMap1.put("departmentname", departmentComInfo.getDepartmentname(str24));
/*     */           
/* 524 */           String str25 = Util.null2String(connStatement.getString("capitalimageid"));
/* 525 */           if (!"".equals(str25)) {
/* 526 */             hashMap1.put("cptcapitalurl", "/weaver/weaver.file.FileDownload?fileid=" + str25);
/*     */           } else {
/* 528 */             hashMap1.put("cptcapitalurl", "");
/*     */           } 
/*     */           
/* 531 */           hashMap1.put("capitalnum", Util.null2String(Float.valueOf(connStatement.getFloat("cptnum"))));
/* 532 */           hashMap1.put("frozennum", Util.null2String(Float.valueOf(connStatement.getFloat("frozennum"))));
/*     */           
/* 534 */           String str26 = Util.null2String(connStatement.getString("resourceid"));
/* 535 */           hashMap1.put("resourceid", str26);
/* 536 */           hashMap1.put("resourceidicon", resourceComInfo.getMessagerUrls(str26));
/* 537 */           hashMap1.put("resourceidname", resourceComInfo.getResourcename(str26));
/*     */           
/* 539 */           String str27 = Util.null2String(connStatement.getString("blongdepartment"));
/* 540 */           hashMap1.put("blongdepartment", str27);
/* 541 */           hashMap1.put("blongdepartmentname", departmentComInfo.getDepartmentname(str27));
/* 542 */           hashMap1.put("sptcount", Util.null2String(connStatement.getString("sptcount")));
/* 543 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/* 546 */     } catch (Exception exception) {
/* 547 */       writeLog("com.api.cpt.mobile.CptCapitalPortalAction.getCapitalList : ", exception);
/*     */     } finally {
/* 549 */       connStatement.close();
/*     */     } 
/* 551 */     hashMap.put("totalsize", Integer.valueOf(i5));
/* 552 */     hashMap.put("datas", arrayList);
/*     */     
/* 554 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/* 555 */     return jSONObject.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/CptCapitalBrowserAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */