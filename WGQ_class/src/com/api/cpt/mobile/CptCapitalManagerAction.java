/*      */ package com.api.cpt.mobile;
/*      */ 
/*      */ import com.weaver.formmodel.mobile.manager.MobileUserInit;
/*      */ import com.weaver.formmodel.util.DateHelper;
/*      */ import java.util.HashMap;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import javax.ws.rs.GET;
/*      */ import javax.ws.rs.Path;
/*      */ import javax.ws.rs.Produces;
/*      */ import javax.ws.rs.core.Context;
/*      */ import net.sf.json.JSONObject;
/*      */ import org.json.JSONException;
/*      */ import org.json.JSONObject;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.cpt.capital.CptShare;
/*      */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*      */ import weaver.cpt.util.CptDwrUtil;
/*      */ import weaver.cpt.util.CptUtil;
/*      */ import weaver.cpt.util.CptWfUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SysMaintenanceLog;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ @Path("/cpt/cptcapitalmanager")
/*      */ public class CptCapitalManagerAction
/*      */   extends BaseBean
/*      */ {
/*      */   @GET
/*      */   @Path("/cptCapitalUseCheck")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalUseCheck(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws JSONException {
/*   56 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*      */     
/*   58 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("idnum"));
/*      */     
/*   60 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("requestid"), 0);
/*   61 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("currentnodetype"), 0);
/*   62 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("formid"), 0);
/*   63 */     int m = Util.getIntValue(paramHttpServletRequest.getParameter("workflowid"), 0);
/*      */     
/*   65 */     RecordSet recordSet = new RecordSet();
/*   66 */     CptWfUtil cptWfUtil = new CptWfUtil();
/*   67 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*      */     
/*   69 */     if (i > 0) {
/*   70 */       recordSet.executeSql("select t1.currentnodetype,t1.workflowid,t2.formid from workflow_requestbase t1,workflow_base t2 where t1.workflowid=t2.id and t1.requestid=" + i);
/*   71 */       if (recordSet.next()) {
/*   72 */         j = recordSet.getInt("currentnodetype");
/*   73 */         k = recordSet.getInt("formid");
/*   74 */         m = recordSet.getInt("workflowid");
/*      */       } 
/*      */     } 
/*      */     
/*   78 */     String str2 = "true";
/*   79 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*   80 */     if (!"".equals(str1)) {
/*   81 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*   82 */       if (i > 0 && j > 0 && j < 3) {
/*   83 */         JSONObject jSONObject1 = cptWfUtil.getCptwfInfo("" + m);
/*      */         
/*   85 */         String str3 = "formtable_main_" + -k;
/*   86 */         recordSet.executeSql("select tablename from workflow_bill where id=" + k);
/*   87 */         while (recordSet.next()) {
/*   88 */           str3 = recordSet.getString("tablename");
/*      */         }
/*   90 */         String str4 = str3;
/*   91 */         String str5 = jSONObject1.getString("zcname");
/*   92 */         String str6 = jSONObject1.getString("slname");
/*   93 */         int n = Util.getIntValue("" + jSONObject1.getInt("zctype"), 0);
/*   94 */         if (n == 1) {
/*   95 */           str4 = str4 + "_dt1";
/*   96 */         } else if (n == 2) {
/*   97 */           str4 = str4 + "_dt2";
/*   98 */         } else if (n == 3) {
/*   99 */           str4 = str4 + "_dt3";
/*  100 */         } else if (n == 4) {
/*  101 */           str4 = str4 + "_dt4";
/*      */         } 
/*  103 */         String str7 = "";
/*  104 */         if (!str4.equals(str3)) {
/*  105 */           str7 = "select d." + str5 + " as cptzcid,d." + str6 + " as cptnum from " + str3 + " m ," + str4 + " d where d.mainid=m.id and m.requestid=" + i;
/*      */         } else {
/*  107 */           str7 = "select m." + str5 + " as cptzcid,m." + str6 + " as cptnum from " + str3 + " m  where  m.requestid=" + i;
/*      */         } 
/*  109 */         recordSet.executeSql(str7);
/*  110 */         while (recordSet.next()) {
/*  111 */           String str8 = recordSet.getString("cptzcid");
/*  112 */           String str9 = recordSet.getString("cptnum");
/*      */           
/*  114 */           if (hashMap.containsKey(str8)) {
/*  115 */             hashMap.put(str8, "" + (Util.getDoubleValue("" + hashMap.get(str8), 0.0D) + Util.getDoubleValue(str9, 0.0D))); continue;
/*      */           } 
/*  117 */           hashMap.put(str8, str9);
/*      */         } 
/*      */       } 
/*      */       
/*  121 */       String[] arrayOfString = Util.TokenizerString2(str1, "|");
/*  122 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  123 */         String[] arrayOfString1 = Util.TokenizerString2(arrayOfString[b], ",");
/*  124 */         if (arrayOfString1.length >= 2) {
/*  125 */           String str = "select capitalnum,frozennum from cptcapital where id=" + arrayOfString1[0];
/*  126 */           recordSet.executeSql(str);
/*  127 */           if (recordSet.next()) {
/*  128 */             double d1 = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/*  129 */             double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/*  130 */             if (d2 < 0.0D) d2 = 0.0D; 
/*  131 */             double d3 = 0.0D;
/*  132 */             if (d1 - d2 < 0.0D) {
/*  133 */               d3 = 0.0D;
/*      */             } else {
/*  135 */               d3 = d1 - d2;
/*      */             } 
/*  137 */             if (hashMap1.containsKey(arrayOfString1[0])) {
/*  138 */               hashMap1.put(arrayOfString1[0], "" + (Util.getDoubleValue("" + hashMap1.get(arrayOfString1[0]), 0.0D) + Util.getDoubleValue(arrayOfString1[1], 0.0D)));
/*      */             } else {
/*  140 */               hashMap1.put(arrayOfString1[0], arrayOfString1[1]);
/*      */             } 
/*      */             
/*  143 */             if (d3 + Util.getDoubleValue((String)hashMap.get(arrayOfString1[0]), 0.0D) < Util.getDoubleValue("" + hashMap1.get(arrayOfString1[0]), 0.0D)) {
/*  144 */               str2 = capitalComInfo.getCapitalname(arrayOfString1[0]) + " " + SystemEnv.getHtmlLabelName(33044, user.getLanguage());
/*      */ 
/*      */               
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  154 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  155 */     hashMap2.put("msg", str2);
/*  156 */     JSONObject jSONObject = JSONObject.fromObject(hashMap2);
/*  157 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalLendCheck")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalLendCheck(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws JSONException {
/*  171 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*      */     
/*  173 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("idnum"));
/*      */     
/*  175 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("requestid"), 0);
/*  176 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("currentnodetype"), 0);
/*  177 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("formid"), 0);
/*  178 */     int m = Util.getIntValue(paramHttpServletRequest.getParameter("workflowid"), 0);
/*      */     
/*  180 */     RecordSet recordSet = new RecordSet();
/*  181 */     CptWfUtil cptWfUtil = new CptWfUtil();
/*  182 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*      */     
/*  184 */     if (i > 0) {
/*  185 */       recordSet.executeSql("select t1.currentnodetype,t1.workflowid,t2.formid from workflow_requestbase t1,workflow_base t2 where t1.workflowid=t2.id and t1.requestid=" + i);
/*  186 */       if (recordSet.next()) {
/*  187 */         j = recordSet.getInt("currentnodetype");
/*  188 */         k = recordSet.getInt("formid");
/*  189 */         m = recordSet.getInt("workflowid");
/*      */       } 
/*      */     } 
/*      */     
/*  193 */     String str2 = "true";
/*  194 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  195 */     if (!"".equals(str1)) {
/*  196 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  197 */       if (i > 0 && j > 0 && j < 3) {
/*  198 */         JSONObject jSONObject1 = cptWfUtil.getCptwfInfo("" + m);
/*      */         
/*  200 */         String str3 = "formtable_main_" + -k;
/*  201 */         recordSet.executeSql("select tablename from workflow_bill where id=" + k);
/*  202 */         while (recordSet.next()) {
/*  203 */           str3 = recordSet.getString("tablename");
/*      */         }
/*  205 */         String str4 = str3;
/*  206 */         String str5 = jSONObject1.getString("zcname");
/*  207 */         String str6 = jSONObject1.getString("slname");
/*  208 */         int n = Util.getIntValue("" + jSONObject1.getInt("zctype"), 0);
/*  209 */         if (n == 1) {
/*  210 */           str4 = str4 + "_dt1";
/*  211 */         } else if (n == 2) {
/*  212 */           str4 = str4 + "_dt2";
/*  213 */         } else if (n == 3) {
/*  214 */           str4 = str4 + "_dt3";
/*  215 */         } else if (n == 4) {
/*  216 */           str4 = str4 + "_dt4";
/*      */         } 
/*  218 */         String str7 = "";
/*  219 */         if (!str4.equals(str3)) {
/*  220 */           str7 = "select d." + str5 + " as cptzcid,d." + str6 + " as cptnum from " + str3 + " m ," + str4 + " d where d.mainid=m.id and m.requestid=" + i;
/*      */         } else {
/*  222 */           str7 = "select m." + str5 + " as cptzcid,m." + str6 + " as cptnum from " + str3 + " m  where  m.requestid=" + i;
/*      */         } 
/*  224 */         recordSet.executeSql(str7);
/*  225 */         while (recordSet.next()) {
/*  226 */           String str8 = recordSet.getString("cptzcid");
/*  227 */           String str9 = recordSet.getString("cptnum");
/*      */           
/*  229 */           if (hashMap.containsKey(str8)) {
/*  230 */             hashMap.put(str8, "" + (Util.getDoubleValue("" + hashMap.get(str8), 0.0D) + Util.getDoubleValue(str9, 0.0D))); continue;
/*      */           } 
/*  232 */           hashMap.put(str8, str9);
/*      */         } 
/*      */       } 
/*      */       
/*  236 */       String str = "";
/*  237 */       recordSet.executeSql("SELECT cptId FROM CptBorrowBuffer");
/*  238 */       while (recordSet.next()) {
/*  239 */         str = str + Util.null2String(recordSet.getString(1)) + ",";
/*      */       }
/*      */       
/*  242 */       String[] arrayOfString = Util.TokenizerString2(str1, "|");
/*  243 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  244 */         String[] arrayOfString1 = Util.TokenizerString2(arrayOfString[b], ",");
/*  245 */         if (arrayOfString1.length >= 2) {
/*  246 */           String str3 = "select capitalnum,frozennum from cptcapital where id=" + arrayOfString1[0];
/*  247 */           recordSet.executeSql(str3);
/*  248 */           if (recordSet.next()) {
/*  249 */             double d1 = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/*  250 */             double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/*  251 */             if (d2 < 0.0D) d2 = 0.0D; 
/*  252 */             double d3 = 0.0D;
/*  253 */             if (d1 - d2 < 0.0D) {
/*  254 */               d3 = 0.0D;
/*      */             } else {
/*  256 */               d3 = d1 - d2;
/*      */             } 
/*  258 */             if (hashMap1.containsKey(arrayOfString1[0])) {
/*  259 */               hashMap1.put(arrayOfString1[0], "" + (Util.getDoubleValue("" + hashMap1.get(arrayOfString1[0]), 0.0D) + Util.getDoubleValue(arrayOfString1[1], 0.0D)));
/*      */             } else {
/*  261 */               hashMap1.put(arrayOfString1[0], arrayOfString1[1]);
/*      */             } 
/*      */             
/*  264 */             if (d3 + Util.getDoubleValue((String)hashMap.get(arrayOfString1[0]), 0.0D) < Util.getDoubleValue("" + hashMap1.get(arrayOfString1[0]), 0.0D)) {
/*  265 */               str2 = capitalComInfo.getCapitalname(arrayOfString1[0]) + " " + SystemEnv.getHtmlLabelName(33044, user.getLanguage());
/*      */               break;
/*      */             } 
/*  268 */             if (str.indexOf(arrayOfString1[0] + ",") != -1) {
/*  269 */               str2 = SystemEnv.getErrorMsgName(36, user.getLanguage());
/*      */               
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  278 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  279 */     hashMap2.put("msg", str2);
/*  280 */     JSONObject jSONObject = JSONObject.fromObject(hashMap2);
/*  281 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalUse")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalUse(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  295 */     CptDwrUtil cptDwrUtil = new CptDwrUtil();
/*  296 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  297 */     RecordSet recordSet = new RecordSet();
/*  298 */     boolean bool = recordSet.getDBType().equals("oracle");
/*  299 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*      */     
/*  301 */     CptShare cptShare = new CptShare();
/*  302 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("capitalnum"));
/*  303 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("location"));
/*  304 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*  305 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("StockInDate"));
/*  306 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*  307 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("hrmid"));
/*      */     
/*  309 */     String str7 = resourceComInfo.getDepartmentID(str6);
/*  310 */     String str8 = (String)cptDwrUtil.getCptInfoMap(str5).get("sptcount");
/*      */     
/*  312 */     byte b = 2;
/*  313 */     String str9 = "";
/*      */     
/*  315 */     if (!str5.equals("")) {
/*  316 */       if (str8.equals("1")) {
/*  317 */         str9 = str5;
/*  318 */         str9 = str9 + b + str4;
/*  319 */         str9 = str9 + b + str7;
/*  320 */         str9 = str9 + b + str6;
/*  321 */         str9 = str9 + b + "1";
/*      */         
/*  323 */         str9 = str9 + b + "";
/*  324 */         str9 = str9 + b + "0";
/*  325 */         str9 = str9 + b + "2";
/*  326 */         str9 = str9 + b + str3;
/*  327 */         str9 = str9 + b + str2;
/*  328 */         str9 = str9 + b + str8;
/*      */         
/*  330 */         recordSet.executeProc("CptUseLogUse_Insert", str9);
/*      */       } else {
/*  332 */         str9 = str5;
/*  333 */         str9 = str9 + b + str4;
/*  334 */         str9 = str9 + b + str7;
/*  335 */         str9 = str9 + b + str6;
/*  336 */         str9 = str9 + b + str1;
/*      */         
/*  338 */         str9 = str9 + b + "";
/*  339 */         str9 = str9 + b + "0";
/*  340 */         str9 = str9 + b + "2";
/*  341 */         str9 = str9 + b + str3;
/*  342 */         str9 = str9 + b + str2;
/*  343 */         str9 = str9 + b + "0";
/*      */         
/*  345 */         recordSet.executeProc("CptUseLogUse_Insert", str9);
/*  346 */         recordSet.next();
/*  347 */         String str = recordSet.getString(1);
/*      */         
/*  349 */         if (str.equals("-1")) {
/*  350 */           paramHttpServletResponse.sendRedirect("CptCapitalUse.jsp?capitalid=" + str5 + "&msgid=1");
/*      */         }
/*      */       } 
/*      */       
/*  354 */       recordSet.executeProc("HrmInfoStatus_UpdateCapital", "" + str6);
/*  355 */       capitalComInfo.removeCapitalCache();
/*  356 */       cptShare.setCptShareByCpt(str5);
/*      */       
/*  358 */       if (!str2.equals("")) {
/*  359 */         recordSet.executeSql("update CptCapital set location='" + str2 + "' where id=" + str5);
/*      */       }
/*      */ 
/*      */       
/*  363 */       if ("1".equals(str8)) {
/*  364 */         String str = "";
/*  365 */         if (!bool) {
/*  366 */           str = "update CptCapital set deprestartdate='" + str4 + "' where id=" + str5 + " and (deprestartdate is null or deprestartdate='')";
/*      */         } else {
/*  368 */           str = "update CptCapital set deprestartdate='" + str4 + "' where id=" + str5 + " and deprestartdate is null";
/*      */         } 
/*  370 */         recordSet.executeSql(str);
/*      */       } 
/*      */     } 
/*      */     
/*  374 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  375 */     hashMap.put("msg", "true");
/*  376 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  377 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalChange")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalChange(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  391 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  392 */     int i = user.getUID();
/*      */     
/*  394 */     CptUtil cptUtil = new CptUtil();
/*  395 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  396 */     RecordSet recordSet = new RecordSet();
/*  397 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  398 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  399 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  400 */     CptShare cptShare = new CptShare();
/*  401 */     char c = Util.getSeparator();
/*  402 */     String str1 = DateHelper.getCurrentDate();
/*      */     
/*  404 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*  405 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("mark"));
/*  406 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("capitalgroupid"));
/*  407 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("resourceid"));
/*  408 */     String str6 = resourceComInfo.getDepartmentID(str5);
/*      */     
/*  410 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("capitalspec"));
/*  411 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("stockindate"));
/*  412 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("location"));
/*  413 */     double d1 = Util.getDoubleValue(paramHttpServletRequest.getParameter("startprice"), 0.0D);
/*  414 */     double d2 = Util.getDoubleValue(paramHttpServletRequest.getParameter("capitalnum"), 0.0D);
/*  415 */     String str10 = Util.null2String(paramHttpServletRequest.getParameter("blongdepartment"));
/*  416 */     String str11 = departmentComInfo.getSubcompanyid1(str10);
/*      */     
/*  418 */     String str12 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*      */     
/*  420 */     recordSet.executeSql("select sptcount,stateid,resourceid,departmentid,capitalgroupid,capitalnum,mark,location,capitaltypeid,capitalspec,StockInDate,startprice,blongsubcompany,blongdepartment from cptcapital where id='" + str2 + "' and isdata=2");
/*      */     
/*  422 */     if (!recordSet.next()) {
/*  423 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  424 */       hashMap1.put("msg", SystemEnv.getHtmlLabelName(503009, user.getLanguage()));
/*  425 */       JSONObject jSONObject1 = JSONObject.fromObject(hashMap1);
/*  426 */       return jSONObject1.toString();
/*      */     } 
/*  428 */     String str13 = recordSet.getString("sptcount");
/*  429 */     String str14 = Util.null2String(recordSet.getString("stateid"), "1");
/*  430 */     String str15 = recordSet.getString("resourceid");
/*  431 */     String str16 = recordSet.getString("departmentid");
/*  432 */     String str17 = recordSet.getString("capitalgroupid");
/*  433 */     double d3 = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/*  434 */     String str18 = recordSet.getString("mark");
/*  435 */     String str19 = recordSet.getString("location");
/*      */     
/*  437 */     String str20 = recordSet.getString("capitalspec");
/*  438 */     String str21 = recordSet.getString("StockInDate");
/*  439 */     double d4 = Util.getDoubleValue(recordSet.getString("startprice"), 0.0D);
/*  440 */     String str22 = recordSet.getString("blongsubcompany");
/*  441 */     String str23 = recordSet.getString("blongdepartment");
/*  442 */     if ("1".equals(str13)) {
/*  443 */       d2 = 1.0D;
/*      */     }
/*  445 */     String str24 = "";
/*      */     
/*  447 */     String str25 = "update cptcapital set ";
/*  448 */     if (!"".equals(str5) && "1".equals(str13) && !"1".equals(str14) && !"5".equals(str14) && !"-7".equals(str14)) {
/*  449 */       str25 = str25 + "resourceid='" + str5 + "',departmentid='" + str6 + "',";
/*  450 */       if (!str5.equals(str15)) {
/*  451 */         str24 = str2;
/*  452 */         str24 = str24 + c + "6";
/*  453 */         str24 = str24 + c + resourceComInfo.getResourcename(str15);
/*  454 */         str24 = str24 + c + resourceComInfo.getResourcename(str5);
/*  455 */         str24 = str24 + c + "" + i;
/*  456 */         str24 = str24 + c + str1;
/*  457 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*  459 */       if (!str6.equals(str16)) {
/*  460 */         str24 = str2;
/*  461 */         str24 = str24 + c + "82";
/*  462 */         str24 = str24 + c + departmentComInfo.getDepartmentName(str16);
/*  463 */         str24 = str24 + c + departmentComInfo.getDepartmentName(str6);
/*  464 */         str24 = str24 + c + "" + i;
/*  465 */         str24 = str24 + c + str1;
/*  466 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  469 */     if (!"".equals(str4)) {
/*  470 */       str25 = str25 + "capitalgroupid='" + str4 + "',";
/*  471 */       if (!str4.equals(str17)) {
/*  472 */         str24 = str2;
/*  473 */         str24 = str24 + c + "16";
/*  474 */         str24 = str24 + c + capitalAssortmentComInfo.getAssortmentName(str17);
/*  475 */         str24 = str24 + c + capitalAssortmentComInfo.getAssortmentName(str4);
/*  476 */         str24 = str24 + c + "" + i;
/*  477 */         str24 = str24 + c + str1;
/*  478 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  481 */     if (d2 != 0.0D) {
/*  482 */       str25 = str25 + "capitalnum='" + d2 + "',";
/*  483 */       if (d2 != d3) {
/*  484 */         str24 = str2;
/*  485 */         str24 = str24 + c + "78";
/*  486 */         str24 = str24 + c + "" + d3;
/*  487 */         str24 = str24 + c + "" + d2;
/*  488 */         str24 = str24 + c + "" + i;
/*  489 */         str24 = str24 + c + str1;
/*  490 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  493 */     if (!"".equals(str3)) if (!CptUtil.checkmarkstr(str3)) {
/*  494 */         str25 = str25 + "mark='" + str3 + "',";
/*  495 */         if (!str3.equals(str18)) {
/*  496 */           str24 = str2;
/*  497 */           str24 = str24 + c + "77";
/*  498 */           str24 = str24 + c + str18;
/*  499 */           str24 = str24 + c + str3;
/*  500 */           str24 = str24 + c + "" + i;
/*  501 */           str24 = str24 + c + str1;
/*  502 */           recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */         } 
/*      */       }  
/*  505 */     if (!"".equals(str9)) {
/*  506 */       str25 = str25 + "location='" + str9 + "',";
/*  507 */       if (!str9.equals(str19)) {
/*  508 */         str24 = str2;
/*  509 */         str24 = str24 + c + "20";
/*  510 */         str24 = str24 + c + str19;
/*  511 */         str24 = str24 + c + str9;
/*  512 */         str24 = str24 + c + "" + i;
/*  513 */         str24 = str24 + c + str1;
/*  514 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  517 */     if (!"".equals(str7)) {
/*  518 */       str25 = str25 + "capitalspec='" + str7 + "',";
/*  519 */       if (!str7.equals(str20)) {
/*  520 */         str24 = str2;
/*  521 */         str24 = str24 + c + "11";
/*  522 */         str24 = str24 + c + str20;
/*  523 */         str24 = str24 + c + str7;
/*  524 */         str24 = str24 + c + "" + i;
/*  525 */         str24 = str24 + c + str1;
/*  526 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  529 */     if (!"".equals(str8)) {
/*  530 */       str25 = str25 + "StockInDate='" + str8 + "',";
/*  531 */       if (!str8.equals(str21)) {
/*  532 */         str24 = str2;
/*  533 */         str24 = str24 + c + "79";
/*  534 */         str24 = str24 + c + str21;
/*  535 */         str24 = str24 + c + str8;
/*  536 */         str24 = str24 + c + "" + i;
/*  537 */         str24 = str24 + c + str1;
/*  538 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  541 */     if (d1 != 0.0D) {
/*  542 */       str25 = str25 + "startprice='" + d1 + "',";
/*  543 */       if (d1 != d4) {
/*  544 */         str24 = str2;
/*  545 */         str24 = str24 + c + "9";
/*  546 */         str24 = str24 + c + "" + d4;
/*  547 */         str24 = str24 + c + "" + d1;
/*  548 */         str24 = str24 + c + "" + i;
/*  549 */         str24 = str24 + c + str1;
/*  550 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  553 */     if (!"".equals(str11)) {
/*  554 */       str25 = str25 + "blongsubcompany='" + str11 + "',blongdepartment='" + str10 + "',";
/*  555 */       if (!str11.equals(str22)) {
/*  556 */         str24 = str2;
/*  557 */         str24 = str24 + c + "80";
/*  558 */         str24 = str24 + c + subCompanyComInfo.getSubCompanyname(str22);
/*  559 */         str24 = str24 + c + subCompanyComInfo.getSubCompanyname(str11);
/*  560 */         str24 = str24 + c + "" + i;
/*  561 */         str24 = str24 + c + str1;
/*  562 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*  564 */       if (!str10.equals(str23)) {
/*  565 */         str24 = str2;
/*  566 */         str24 = str24 + c + "81";
/*  567 */         str24 = str24 + c + departmentComInfo.getDepartmentName(str23);
/*  568 */         str24 = str24 + c + departmentComInfo.getDepartmentName(str10);
/*  569 */         str24 = str24 + c + "" + i;
/*  570 */         str24 = str24 + c + str1;
/*  571 */         recordSet.executeProc("CptCapitalModify_Insert", str24);
/*      */       } 
/*      */     } 
/*  574 */     str25 = str25 + "name=name where id='" + str2 + "'";
/*  575 */     recordSet.executeSql(str25);
/*      */     
/*  577 */     str25 = "insert into CptUseLog(capitalid,usedate,userequest,useresourceid,usestatus,remark,resourceid) values(";
/*  578 */     str25 = str25 + "'" + str2 + "','" + str1 + "','','-1','7','" + str12 + "','" + i + "'";
/*  579 */     str25 = str25 + ")";
/*  580 */     recordSet.executeSql(str25);
/*      */     
/*  582 */     if (!"".equals(str4) && 
/*  583 */       !str4.equals(str17)) {
/*  584 */       cptShare.freshenCptShareByCapitalgroup(str2);
/*      */     }
/*      */ 
/*      */     
/*  588 */     if (!"".equals(str5) && "1".equals(str13) && !"1".equals(str14) && !"5".equals(str14) && !"-7".equals(str14) && 
/*  589 */       !str5.equals(str15)) {
/*  590 */       cptShare.freshenCptShareByResource(str2);
/*      */     }
/*      */ 
/*      */     
/*  594 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  595 */     hashMap.put("msg", "true");
/*  596 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  597 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalMove")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalMove(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  611 */     RecordSet recordSet = new RecordSet();
/*  612 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  613 */     CptShare cptShare = new CptShare();
/*      */     
/*  615 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*  616 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("location"));
/*  617 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*  618 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("CptDept_to"));
/*  619 */     String str5 = capitalComInfo.getDepartmentid(str1);
/*  620 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("hrmid"));
/*      */     
/*  622 */     String str7 = "1";
/*  623 */     String str8 = "-4";
/*  624 */     String str9 = DateHelper.getCurrentDate();
/*  625 */     byte b = 2;
/*  626 */     String str10 = "";
/*      */     
/*  628 */     str10 = str1;
/*  629 */     str10 = str10 + b + str9;
/*  630 */     str10 = str10 + b + str4;
/*  631 */     str10 = str10 + b + str6;
/*  632 */     str10 = str10 + b + str7;
/*  633 */     str10 = str10 + b + str2;
/*  634 */     str10 = str10 + b + str8;
/*  635 */     str10 = str10 + b + str3;
/*  636 */     str10 = str10 + b + str5;
/*      */     
/*  638 */     recordSet.executeProc("Capital_Adjust", str10);
/*  639 */     recordSet.executeSql("update cptcapital set location='" + str2 + "' where id='" + str1 + "' ");
/*      */     
/*  641 */     capitalComInfo.removeCapitalCache();
/*  642 */     cptShare.setCptShareByCpt(str1);
/*      */     
/*  644 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  645 */     hashMap.put("msg", "true");
/*  646 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  647 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalLend")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalLend(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  661 */     RecordSet recordSet = new RecordSet();
/*  662 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  663 */     CptShare cptShare = new CptShare();
/*  664 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*      */     
/*  666 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("location"));
/*  667 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*  668 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("hrmid"));
/*  669 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("StockInDate"));
/*  670 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*  671 */     String str6 = resourceComInfo.getDepartmentID(str3);
/*      */     
/*  673 */     char c = Util.getSeparator();
/*  674 */     String str7 = "";
/*      */     
/*  676 */     str7 = str5;
/*  677 */     str7 = str7 + c + str4;
/*  678 */     str7 = str7 + c + str6;
/*  679 */     str7 = str7 + c + str3;
/*  680 */     str7 = str7 + c + "1";
/*  681 */     str7 = str7 + c + str1;
/*  682 */     str7 = str7 + c + "0";
/*  683 */     str7 = str7 + c + "";
/*  684 */     str7 = str7 + c + "0";
/*  685 */     str7 = str7 + c + "3";
/*  686 */     str7 = str7 + c + str2;
/*  687 */     str7 = str7 + c + "0";
/*      */     
/*  689 */     recordSet.executeProc("CptUseLogLend_Insert", str7);
/*  690 */     capitalComInfo.removeCapitalCache();
/*  691 */     cptShare.setCptShareByCpt(str5);
/*      */     
/*  693 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  694 */     hashMap.put("msg", "true");
/*  695 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  696 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalLoss")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalLoss(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  710 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  711 */     RecordSet recordSet = new RecordSet();
/*  712 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  713 */     CptDwrUtil cptDwrUtil = new CptDwrUtil();
/*      */     
/*  715 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("capitalnum"));
/*  716 */     String str2 = "" + Util.getDoubleValue(paramHttpServletRequest.getParameter("cost"), 0.0D);
/*  717 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*  718 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("operator"));
/*  719 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("StockInDate"));
/*  720 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*  721 */     String str7 = resourceComInfo.getDepartmentID(str4);
/*      */     
/*  723 */     String str8 = Util.null2String((String)cptDwrUtil.getCptInfoMap(str6).get("sptcount"));
/*      */ 
/*      */     
/*  726 */     String str9 = "";
/*  727 */     str9 = "select departmentid from CptCapital where id=" + str6;
/*  728 */     recordSet.executeSql(str9);
/*  729 */     recordSet.next();
/*  730 */     String str10 = recordSet.getString("departmentid");
/*      */     
/*  732 */     String str11 = "true";
/*  733 */     char c = Util.getSeparator();
/*  734 */     String str12 = "";
/*      */     
/*  736 */     if (str8.equals("1")) {
/*  737 */       str12 = str6;
/*  738 */       str12 = str12 + c + str5;
/*  739 */       str12 = str12 + c + str7;
/*  740 */       str12 = str12 + c + str4;
/*  741 */       str12 = str12 + c + "1";
/*  742 */       str12 = str12 + c + "";
/*  743 */       str12 = str12 + c + "0";
/*  744 */       str12 = str12 + c + "";
/*  745 */       str12 = str12 + c + str2;
/*  746 */       str12 = str12 + c + "-7";
/*  747 */       str12 = str12 + c + str3;
/*  748 */       str12 = str12 + c + "0";
/*  749 */       str12 = str12 + c + str8;
/*  750 */       str12 = str12 + c + str10;
/*      */       
/*  752 */       recordSet.executeProc("CptUseLogLoss_Insert2", str12);
/*      */     } else {
/*  754 */       str12 = str6;
/*  755 */       str12 = str12 + c + str5;
/*  756 */       str12 = str12 + c + str7;
/*  757 */       str12 = str12 + c + str4;
/*  758 */       str12 = str12 + c + str1;
/*  759 */       str12 = str12 + c + "";
/*  760 */       str12 = str12 + c + "0";
/*  761 */       str12 = str12 + c + "";
/*  762 */       str12 = str12 + c + str2;
/*  763 */       str12 = str12 + c + "-7";
/*  764 */       str12 = str12 + c + str3;
/*  765 */       str12 = str12 + c + "0";
/*  766 */       str12 = str12 + c + str8;
/*  767 */       str12 = str12 + c + str10;
/*      */       
/*  769 */       recordSet.executeProc("CptUseLogLoss_Insert2", str12);
/*  770 */       recordSet.next();
/*  771 */       String str = recordSet.getString(1);
/*      */       
/*  773 */       if (str.equals("-1")) {
/*  774 */         str11 = SystemEnv.getHtmlLabelName(1405, user.getLanguage());
/*      */       }
/*      */     } 
/*      */     
/*  778 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  779 */     hashMap.put("msg", str11);
/*  780 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  781 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalDiscard")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalDiscard(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  796 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  797 */     RecordSet recordSet = new RecordSet();
/*  798 */     CptDwrUtil cptDwrUtil = new CptDwrUtil();
/*      */     
/*  800 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("capitalnum"));
/*  801 */     String str2 = "" + Util.getDoubleValue(paramHttpServletRequest.getParameter("cost"), 0.0D);
/*  802 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*  803 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("operator"));
/*  804 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("StockInDate"));
/*  805 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*      */     
/*  807 */     String str7 = "";
/*  808 */     str7 = "select departmentid from CptCapital where id=" + str6;
/*  809 */     recordSet.executeSql(str7);
/*  810 */     recordSet.next();
/*  811 */     String str8 = recordSet.getString("departmentid");
/*      */     
/*  813 */     String str9 = Util.null2String((String)cptDwrUtil.getCptInfoMap(str6).get("sptcount"));
/*  814 */     if ("".equals(str9)) {
/*  815 */       str9 = "0";
/*      */     }
/*  817 */     String str10 = "true";
/*  818 */     char c = Util.getSeparator();
/*  819 */     String str11 = "";
/*  820 */     if (str9.equals("1")) {
/*  821 */       str11 = str6;
/*  822 */       str11 = str11 + c + str5;
/*  823 */       str11 = str11 + c + "0";
/*      */       
/*  825 */       str11 = str11 + c + str4;
/*  826 */       str11 = str11 + c + "1";
/*  827 */       str11 = str11 + c + "";
/*  828 */       str11 = str11 + c + "0";
/*  829 */       str11 = str11 + c + "";
/*  830 */       str11 = str11 + c + str2;
/*  831 */       str11 = str11 + c + "5";
/*  832 */       str11 = str11 + c + str3;
/*  833 */       str11 = str11 + c + str9;
/*  834 */       str11 = str11 + c + str8;
/*  835 */       recordSet.executeProc("CptUseLogDiscard_Insert2", str11);
/*      */     } else {
/*  837 */       str11 = str6;
/*  838 */       str11 = str11 + c + str5;
/*  839 */       str11 = str11 + c + "0";
/*      */       
/*  841 */       str11 = str11 + c + str4;
/*  842 */       str11 = str11 + c + str1;
/*  843 */       str11 = str11 + c + "";
/*  844 */       str11 = str11 + c + "0";
/*  845 */       str11 = str11 + c + "";
/*  846 */       str11 = str11 + c + str2;
/*  847 */       str11 = str11 + c + "5";
/*  848 */       str11 = str11 + c + str3;
/*  849 */       str11 = str11 + c + str9;
/*  850 */       str11 = str11 + c + str8;
/*      */       
/*  852 */       recordSet.executeProc("CptUseLogDiscard_Insert2", str11);
/*  853 */       recordSet.next();
/*  854 */       String str = recordSet.getString(1);
/*      */       
/*  856 */       if (str.equals("-1")) {
/*  857 */         str10 = SystemEnv.getHtmlLabelName(1405, user.getLanguage());
/*      */       }
/*      */     } 
/*  860 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  861 */     hashMap.put("msg", str10);
/*  862 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  863 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalMend")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalMend(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  877 */     RecordSet recordSet = new RecordSet();
/*  878 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*      */     
/*  880 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("menddate"));
/*  881 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("maintaincompany"));
/*  882 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("operator"));
/*  883 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("mendperioddate"));
/*  884 */     String str5 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("cost")), "0");
/*  885 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*  886 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*      */     
/*  888 */     String str8 = "";
/*  889 */     str8 = "select departmentid from CptCapital where id=" + str7;
/*  890 */     recordSet.executeSql(str8);
/*  891 */     recordSet.next();
/*  892 */     String str9 = recordSet.getString("departmentid");
/*      */     
/*  894 */     char c = Util.getSeparator();
/*  895 */     String str10 = "";
/*      */     
/*  897 */     str10 = str7;
/*  898 */     str10 = str10 + c + str1;
/*  899 */     str10 = str10 + c + "";
/*  900 */     str10 = str10 + c + "";
/*  901 */     str10 = str10 + c + "1";
/*  902 */     str10 = str10 + c + "";
/*  903 */     str10 = str10 + c + "0";
/*  904 */     str10 = str10 + c + str2;
/*  905 */     str10 = str10 + c + str5;
/*  906 */     str10 = str10 + c + "4";
/*  907 */     str10 = str10 + c + str6;
/*  908 */     str10 = str10 + c + str3;
/*  909 */     str10 = str10 + c + str4;
/*  910 */     str10 = str10 + c + str9;
/*      */     
/*  912 */     recordSet.executeProc("CptUseLogMend_Insert2", str10);
/*  913 */     capitalComInfo.removeCapitalCache();
/*      */     
/*  915 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  916 */     hashMap.put("msg", "true");
/*  917 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  918 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalBack")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalBack(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/*  932 */     RecordSet recordSet = new RecordSet();
/*  933 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  934 */     CptShare cptShare = new CptShare();
/*      */     
/*  936 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("method"));
/*  937 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*  938 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/*  939 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("StockInDate"));
/*      */     
/*  941 */     String str5 = "select sptcount,departmentid,stateid,resourceid,deprestartdate from CptCapital where id=" + str3;
/*  942 */     recordSet.executeSql(str5);
/*  943 */     recordSet.next();
/*  944 */     String str6 = recordSet.getString("sptcount");
/*  945 */     String str7 = recordSet.getString("departmentid");
/*  946 */     String str8 = recordSet.getString("resourceid");
/*  947 */     String str9 = recordSet.getString("stateid");
/*  948 */     String str10 = recordSet.getString("deprestartdate");
/*      */     
/*  950 */     char c = Util.getSeparator();
/*  951 */     String str11 = "";
/*  952 */     if ("backMyCpt".equalsIgnoreCase(str1)) {
/*  953 */       str11 = str3;
/*  954 */       str11 = str11 + c + TimeUtil.getCurrentDateString();
/*  955 */       str11 = str11 + c + "";
/*  956 */       str11 = str11 + c + "";
/*  957 */       str11 = str11 + c + "1";
/*  958 */       str11 = str11 + c + "";
/*  959 */       str11 = str11 + c + "0";
/*  960 */       str11 = str11 + c + "";
/*  961 */       str11 = str11 + c + "0";
/*  962 */       if (str9.equals("4") && !str8.equals("0")) {
/*  963 */         str11 = str11 + c + "2";
/*      */       } else {
/*  965 */         str11 = str11 + c + "1";
/*      */       } 
/*  967 */       str11 = str11 + c + "";
/*  968 */       str11 = str11 + c + "0";
/*  969 */       str11 = str11 + c + str6;
/*  970 */       str11 = str11 + c + str7;
/*  971 */       recordSet.executeProc("CptUseLogBack_Insert2", str11);
/*      */       
/*  973 */       capitalComInfo.deleteCapitalCache(str3);
/*  974 */       cptShare.setCptShareByCpt(str3);
/*      */ 
/*      */       
/*  977 */       if (str9.equals("4") && !str8.equals("0")) {
/*  978 */         recordSet.executeSql("update cptcapital set resourceid = " + str8 + ",departmentid = " + str7 + ",deprestartdate='" + str10 + "' where id = " + str3 + "");
/*      */       }
/*      */     } else {
/*  981 */       str11 = str3;
/*  982 */       str11 = str11 + c + str4;
/*      */ 
/*      */       
/*  985 */       str11 = str11 + c + "";
/*  986 */       str11 = str11 + c + "";
/*  987 */       str11 = str11 + c + "1";
/*  988 */       str11 = str11 + c + "";
/*  989 */       str11 = str11 + c + "0";
/*  990 */       str11 = str11 + c + "";
/*  991 */       str11 = str11 + c + "0";
/*  992 */       if (str9.equals("4") && !str8.equals("0")) {
/*  993 */         str11 = str11 + c + "2";
/*      */       } else {
/*  995 */         str11 = str11 + c + "1";
/*      */       } 
/*  997 */       str11 = str11 + c + str2;
/*  998 */       str11 = str11 + c + "0";
/*  999 */       str11 = str11 + c + str6;
/* 1000 */       str11 = str11 + c + str7;
/* 1001 */       recordSet.executeProc("CptUseLogBack_Insert2", str11);
/*      */       
/* 1003 */       cptShare.setCptShareByCpt(str3);
/*      */       
/* 1005 */       if (str9.equals("4") && !str8.equals("0")) {
/* 1006 */         recordSet.executeSql("update cptcapital set resourceid = " + str8 + ",departmentid = " + str7 + ",deprestartdate='" + str10 + "' where id = " + str3 + "");
/*      */       }
/* 1008 */       capitalComInfo.removeCapitalCache();
/*      */     } 
/*      */     
/* 1011 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1012 */     hashMap.put("msg", "true");
/* 1013 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/* 1014 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @GET
/*      */   @Path("/cptCapitalDel")
/*      */   @Produces({"text/plain"})
/*      */   public String cptCapitalDel(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 1028 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 1029 */     RecordSet recordSet = new RecordSet();
/* 1030 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 1031 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 1032 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/* 1033 */     String str2 = "" + str1;
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1038 */     boolean bool = false;
/* 1039 */     String str3 = "select * from cptcapital where datatype = " + str1;
/* 1040 */     recordSet.executeSql(str3);
/* 1041 */     if (recordSet.next()) {
/* 1042 */       bool = true;
/*      */     }
/* 1044 */     str3 = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + str1;
/* 1045 */     recordSet.executeSql(str3);
/* 1046 */     if (recordSet.next()) {
/* 1047 */       bool = true;
/*      */     }
/*      */     
/* 1050 */     if (bool) {
/* 1051 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1052 */       hashMap1.put("msg", SystemEnv.getHtmlLabelName(83610, Util.getIntValue(user.getLanguage())));
/* 1053 */       JSONObject jSONObject1 = JSONObject.fromObject(hashMap1);
/* 1054 */       return jSONObject1.toString();
/*      */     } 
/*      */     
/* 1057 */     String str4 = "";
/* 1058 */     str3 = "select * from cptcapital where id = '" + str1 + "'";
/* 1059 */     recordSet.executeSql(str3);
/* 1060 */     if (recordSet.next()) {
/* 1061 */       str4 = recordSet.getString("name");
/*      */     }
/* 1063 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 1064 */     boolean bool1 = HrmUserVarify.checkUserRight("Capital:Maintenance", user);
/* 1065 */     if (bool1) {
/* 1066 */       recordSet.executeProc("CptCapital_ForcedDelete", str2);
/* 1067 */       sysMaintenanceLog.resetParameter();
/* 1068 */       sysMaintenanceLog.setRelatedId(Util.getIntValue(str1));
/* 1069 */       sysMaintenanceLog.setRelatedName(str4);
/* 1070 */       sysMaintenanceLog.setOperateType("3");
/* 1071 */       sysMaintenanceLog.setOperateDesc("CptCapital_Delete," + str2);
/* 1072 */       sysMaintenanceLog.setOperateItem("51");
/* 1073 */       sysMaintenanceLog.setOperateUserid(user.getUID(user, "Capital:Maintenance"));
/* 1074 */       sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 1075 */       sysMaintenanceLog.setSysLogInfo();
/*      */       
/* 1077 */       capitalComInfo.deleteCapitalCache(str1);
/* 1078 */       capitalAssortmentComInfo.removeCapitalAssortmentCache();
/* 1079 */       recordSet.executeSql("delete from CptCapitalShareInfo where relateditemid=" + str1);
/*      */     } else {
/* 1081 */       recordSet.executeProc("CptCapital_Delete", str2);
/* 1082 */       sysMaintenanceLog.resetParameter();
/* 1083 */       sysMaintenanceLog.setRelatedId(Util.getIntValue(str1));
/* 1084 */       sysMaintenanceLog.setRelatedName(str4);
/* 1085 */       sysMaintenanceLog.setOperateType("3");
/* 1086 */       sysMaintenanceLog.setOperateDesc("CptCapital_Delete," + str2);
/* 1087 */       sysMaintenanceLog.setOperateItem("51");
/* 1088 */       sysMaintenanceLog.setOperateUserid(user.getUID(user, "CptCapitalEdit:Delete"));
/* 1089 */       sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 1090 */       sysMaintenanceLog.setSysLogInfo();
/*      */       
/* 1092 */       capitalComInfo.removeCapitalCache();
/* 1093 */       capitalAssortmentComInfo.removeCapitalAssortmentCache();
/* 1094 */       recordSet.executeSql("delete from CptCapitalShareInfo where relateditemid=" + str1);
/*      */     } 
/*      */     
/* 1097 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1098 */     hashMap.put("msg", "true");
/* 1099 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/* 1100 */     return jSONObject.toString();
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/CptCapitalManagerAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */