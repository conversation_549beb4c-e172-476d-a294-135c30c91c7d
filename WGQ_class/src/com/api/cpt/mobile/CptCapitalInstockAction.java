/*     */ package com.api.cpt.mobile;
/*     */ 
/*     */ import com.weaver.formmodel.mobile.manager.MobileUserInit;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.math.BigDecimal;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.job.CptLowInventoryRemindJob;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.system.code.CodeBuild;
/*     */ import weaver.workflow.msg.PoppupRemindInfoUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/cpt/capitalinstock")
/*     */ public class CptCapitalInstockAction
/*     */   extends BaseBean
/*     */ {
/*     */   @GET
/*     */   @Path("/instockCapital")
/*     */   @Produces({"text/plain"})
/*     */   public String instockCapital(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  52 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  53 */     String str1 = Util.fromScreen(paramHttpServletRequest.getParameter("id"), user.getLanguage());
/*  54 */     RecordSet recordSet = new RecordSet();
/*  55 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*     */     
/*  57 */     if (Util.null2String(str1).startsWith(",")) {
/*  58 */       str1 = str1.substring(1, str1.length());
/*     */     }
/*  60 */     if (Util.null2String(str1).endsWith(",")) {
/*  61 */       str1 = str1.substring(0, str1.length() - 1);
/*     */     }
/*  63 */     String str2 = "select m.id as mainid,d.* from CptStockInDetail d,CptStockInMain m where m.id=d.cptstockinid and m.id in(" + str1 + ") and m.ischecked=0 order by m.id,d.id ";
/*  64 */     recordSet.executeSql(str2);
/*  65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  66 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  67 */     while (recordSet.next()) {
/*  68 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  69 */       hashMap1.put("mainid", Util.null2String(recordSet.getString("mainid")));
/*  70 */       String str3 = Util.null2String(recordSet.getString("cpttype"));
/*  71 */       String str4 = Util.toScreen(capitalComInfo.getCapitalname(str3), user.getLanguage());
/*  72 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/*  73 */       hashMap1.put("cpttype", str3);
/*  74 */       hashMap1.put("cpttypename", str4);
/*  75 */       hashMap1.put("capitalspec", Util.null2String(recordSet.getString("capitalspec")));
/*  76 */       hashMap1.put("price", Util.null2String(recordSet.getString("price")));
/*  77 */       hashMap1.put("plannumber", Util.null2String(recordSet.getString("plannumber")));
/*  78 */       hashMap1.put("Invoice", Util.null2String(recordSet.getString("Invoice")));
/*  79 */       hashMap1.put("location", Util.null2String(recordSet.getString("location")));
/*  80 */       arrayList.add(hashMap1);
/*     */     } 
/*  82 */     hashMap.put("datas", arrayList);
/*  83 */     JSONObject jSONObject = JSONObject.fromObject(hashMap);
/*  84 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/instockOfCheckState")
/*     */   @Produces({"text/plain"})
/*     */   public String instockOfCheckState(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  97 */     JSONObject jSONObject = new JSONObject();
/*  98 */     boolean bool = true;
/*  99 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramHttpServletRequest.getParameter("id")), ",");
/* 100 */     String str = "";
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     if (arrayOfString != null && arrayOfString.length > 0) {
/* 103 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 104 */         str = "select ischecked from CptStockInMain where id=" + arrayOfString[b] + " and ischecked='-2' ";
/* 105 */         recordSet.executeSql(str);
/* 106 */         if (recordSet.next()) {
/* 107 */           bool = false;
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     }
/* 112 */     jSONObject.put("msg", "" + bool);
/* 113 */     return jSONObject.toString();
/*     */   }
/*     */   
/*     */   @GET
/*     */   @Path("/doBtachInstock")
/*     */   @Produces({"text/plain"})
/*     */   public String doBtachInstock(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 120 */     JSONObject jSONObject = new JSONObject();
/* 121 */     String str1 = "";
/* 122 */     char c = Util.getSeparator();
/* 123 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 124 */     String str2 = DateHelper.getCurrentDate();
/* 125 */     String str3 = DateHelper.getCurrentTime();
/*     */     
/* 127 */     String str4 = "" + user.getUID();
/*     */     
/* 129 */     String str5 = Util.fromScreen(paramHttpServletRequest.getParameter("id"), user.getLanguage());
/* 130 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("departmentid"));
/* 131 */     if ("".equals(str6)) {
/* 132 */       jSONObject.put("msg", "false");
/* 133 */       return jSONObject.toString();
/*     */     } 
/* 135 */     String str7 = "";
/* 136 */     String str8 = "";
/* 137 */     String str9 = "";
/* 138 */     String str10 = "";
/*     */     
/* 140 */     RecordSet recordSet1 = new RecordSet();
/* 141 */     RecordSet recordSet2 = new RecordSet();
/* 142 */     RecordSet recordSet3 = new RecordSet();
/* 143 */     RecordSet recordSet4 = new RecordSet();
/*     */     
/* 145 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 146 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 147 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 148 */     CodeBuild codeBuild = new CodeBuild();
/* 149 */     CptShare cptShare = new CptShare();
/* 150 */     PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/* 151 */     CptLowInventoryRemindJob cptLowInventoryRemindJob = new CptLowInventoryRemindJob();
/*     */     
/* 153 */     String[] arrayOfString = str5.split(","); byte b;
/* 154 */     for (b = 0; b < arrayOfString.length; b++) {
/* 155 */       String str11 = arrayOfString[b];
/*     */       
/* 157 */       String str12 = "select m.id as mainid,d.* from CptStockInDetail d,CptStockInMain m where m.id=d.cptstockinid and m.id = " + str11 + " and m.ischecked=0 order by m.id,d.id ";
/* 158 */       recordSet2.executeSql(str12);
/*     */       
/* 160 */       recordSet1.executeSql("select stockindate,buyerid,checkerid,supplierid from CptStockInMain where id=" + str11);
/* 161 */       if (recordSet1.next()) {
/* 162 */         str7 = Util.null2String(recordSet1.getString("buyerid"));
/* 163 */         str10 = Util.null2String(recordSet1.getString("stockindate"));
/* 164 */         str9 = Util.null2String(recordSet1.getString("checkerid"));
/* 165 */         str8 = Util.null2String(recordSet1.getString("supplierid"));
/*     */       } 
/*     */       
/* 168 */       String str13 = "";
/* 169 */       str13 = str11;
/* 170 */       str13 = str13 + c + str10;
/* 171 */       str13 = str13 + c + str7;
/* 172 */       str13 = str13 + c + str8;
/* 173 */       str13 = str13 + c + str9;
/* 174 */       str13 = str13 + c + "";
/* 175 */       str13 = str13 + c + "1";
/* 176 */       recordSet1.executeProc("CptStockInMain_Update", str13);
/* 177 */       recordSet1.executeSql("update CptStockInMain set stockindate='" + str10 + "',stockindept='" + str6 + "' where id=" + str11);
/*     */       
/* 179 */       String str14 = "";
/* 180 */       String str15 = "";
/* 181 */       String str16 = "";
/* 182 */       String str17 = "";
/* 183 */       String str18 = "";
/* 184 */       String str19 = "";
/* 185 */       String str20 = "";
/* 186 */       String str21 = "";
/* 187 */       String str22 = "";
/* 188 */       String str23 = "";
/* 189 */       String str24 = "";
/* 190 */       boolean bool = false;
/* 191 */       byte b1 = 0;
/* 192 */       int i = 0;
/*     */       
/* 194 */       while (recordSet2.next()) {
/* 195 */         str14 = Util.null2String(recordSet2.getString("id"));
/* 196 */         str15 = Util.null2String(recordSet2.getString("mainid"));
/* 197 */         if (!str15.equals(str11))
/* 198 */           continue;  str16 = Util.null2String(recordSet2.getString("cpttype"));
/* 199 */         str17 = Util.null2String(recordSet2.getString("plannumber"));
/* 200 */         str18 = Util.null2String(recordSet2.getString("price"));
/* 201 */         str19 = Util.null2String(recordSet2.getString("customerid"));
/* 202 */         str20 = Util.null2String(recordSet2.getString("capitalspec"));
/* 203 */         str21 = Util.null2String(recordSet2.getString("location"));
/* 204 */         str22 = Util.null2String(recordSet2.getString("Invoice"));
/* 205 */         str24 = Util.null2String(recordSet2.getString("contactno"));
/* 206 */         String str = Util.null2String(recordSet2.getString("selectDate"));
/*     */         
/* 208 */         recordSet3.executeProc("CptCapital_SelectByID", str16);
/* 209 */         if (recordSet3.next()) {
/* 210 */           str23 = recordSet3.getString("sptcount");
/*     */         }
/*     */         
/* 213 */         if (str23.equals("1")) {
/* 214 */           str13 = str14;
/* 215 */           recordSet1.executeProc("CptStockInDetail_Delete", str13);
/* 216 */           for (b1 = 1, i = (int)Util.getFloatValue(str17, 0.0F); b1 <= i; b1++) {
/* 217 */             str13 = str11;
/* 218 */             str13 = str13 + c + str16;
/* 219 */             str13 = str13 + c + "1";
/* 220 */             str13 = str13 + c + "1";
/* 221 */             str13 = str13 + c + str18;
/* 222 */             str13 = str13 + c + str19;
/* 223 */             str13 = str13 + c + str;
/* 224 */             str13 = str13 + c + str20;
/* 225 */             str13 = str13 + c + str21;
/* 226 */             str13 = str13 + c + str22;
/* 227 */             recordSet1.executeProc("CptStockInDetail_Insert", str13);
/* 228 */             if (recordSet1.next()) {
/* 229 */               String str49 = Util.null2String(recordSet1.getString(1));
/* 230 */               if (!str49.equals("") && !str49.equals("0"))
/* 231 */                 recordSet1.executeSql("update CptStockInDetail set contractno = '" + str24 + "' where id = " + str49); 
/*     */             } 
/*     */           } 
/*     */           continue;
/*     */         } 
/* 236 */         str13 = str14;
/* 237 */         str13 = str13 + c + str17;
/* 238 */         recordSet1.executeProc("CptStockInDetail_Update", str13);
/*     */       } 
/*     */ 
/*     */       
/* 242 */       String str25 = "0";
/* 243 */       String str26 = "0";
/* 244 */       String str27 = "1";
/*     */       
/* 246 */       String str28 = "";
/* 247 */       String str29 = "";
/* 248 */       String str30 = "";
/* 249 */       String str31 = "";
/* 250 */       String str32 = "";
/* 251 */       String str33 = "";
/* 252 */       String str34 = "";
/* 253 */       String str35 = "";
/*     */       
/* 255 */       String str36 = "";
/* 256 */       String str37 = "";
/* 257 */       String str38 = "";
/* 258 */       String str39 = "";
/* 259 */       String str40 = "";
/* 260 */       String str41 = "";
/* 261 */       String str42 = "";
/* 262 */       String str43 = "";
/*     */       
/* 264 */       String str44 = "";
/* 265 */       String str45 = "";
/* 266 */       String str46 = "";
/* 267 */       String str47 = "";
/* 268 */       String str48 = "";
/*     */       
/* 270 */       ArrayList<String> arrayList = new ArrayList();
/*     */       
/* 272 */       recordSet1.executeProc("CptStockInDetail_SByStockid", str11);
/*     */       
/* 274 */       while (recordSet1.next()) {
/*     */         
/* 276 */         str37 = recordSet1.getString("id");
/* 277 */         str28 = recordSet1.getString("cpttype");
/* 278 */         str36 = recordSet1.getString("innumber");
/* 279 */         double d = Util.getDoubleValue(str36);
/*     */         
/* 281 */         BigDecimal bigDecimal = new BigDecimal(recordSet1.getString("price"));
/* 282 */         str19 = recordSet1.getString("customerid");
/* 283 */         str20 = recordSet1.getString("capitalspec");
/* 284 */         str21 = recordSet1.getString("location");
/* 285 */         str22 = recordSet1.getString("Invoice");
/*     */         
/* 287 */         str44 = recordSet1.getString("selectDate");
/* 288 */         str24 = recordSet1.getString("contractno");
/*     */         
/* 290 */         str42 = bigDecimal.multiply(new BigDecimal(str36)).toString();
/*     */         
/* 292 */         recordSet3.executeProc("CptCapital_SelectByID", str28);
/* 293 */         if (recordSet3.next()) {
/* 294 */           str29 = recordSet3.getString("mark");
/* 295 */           str40 = recordSet3.getString("sptcount");
/*     */           
/* 297 */           str43 = recordSet3.getString("capitalgroupid");
/* 298 */           str46 = recordSet3.getString("capitaltypeid");
/*     */         } 
/*     */ 
/*     */         
/* 302 */         String str49 = "2,3,4,5,6,7,8,9";
/* 303 */         String str50 = str43;
/*     */         
/* 305 */         while (!capitalAssortmentComInfo.getSupAssortmentId(str50).equals("0"))
/*     */         {
/*     */           
/* 308 */           str50 = capitalAssortmentComInfo.getSupAssortmentId(str50);
/*     */         }
/*     */         
/* 311 */         if (bigDecimal.compareTo(new BigDecimal("2000")) == 1) {
/* 312 */           str45 = "1";
/*     */         } else {
/* 314 */           str45 = "2";
/*     */         } 
/*     */ 
/*     */         
/* 318 */         str47 = departmentComInfo.getSubcompanyid1(str6);
/* 319 */         str48 = str6;
/*     */ 
/*     */         
/* 322 */         if (str40.equals("1")) {
/* 323 */           str29 = codeBuild.getCurrentCapitalCode(departmentComInfo.getSubcompanyid1(str6), str6, str43, str46, str44, str10, str28);
/*     */         }
/*     */         
/* 326 */         recordSet3.executeProc("CptCapital_SelectByDataType", str28 + c + str6);
/* 327 */         if (!str40.equals("1") && recordSet3.next()) {
/* 328 */           str29 = recordSet3.getString("mark");
/* 329 */         } else if (!str40.equals("1")) {
/* 330 */           str29 = codeBuild.getCurrentCapitalCode(departmentComInfo.getSubcompanyid1(str6), str6, str43, str46, str44, str10, str28);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 335 */         str39 = str10;
/* 336 */         str39 = str39 + c + str48;
/* 337 */         str39 = str39 + c + str9;
/* 338 */         str39 = str39 + c + str9;
/* 339 */         str39 = str39 + c + str36;
/* 340 */         str39 = str39 + c + str21;
/* 341 */         str39 = str39 + c + str25;
/* 342 */         str39 = str39 + c + "";
/* 343 */         str39 = str39 + c + str42;
/* 344 */         str39 = str39 + c + str27;
/* 345 */         str39 = str39 + c + "";
/* 346 */         str39 = str39 + c + str29;
/* 347 */         str39 = str39 + c + str28;
/* 348 */         str39 = str39 + c + str31;
/* 349 */         str39 = str39 + c + str32;
/* 350 */         str39 = str39 + c + str33;
/* 351 */         str39 = str39 + c + str34;
/* 352 */         str39 = str39 + c + str35;
/* 353 */         str39 = str39 + c + str4;
/* 354 */         str39 = str39 + c + str2;
/* 355 */         str39 = str39 + c + str3;
/*     */         
/* 357 */         if (str40.equals("1")) {
/*     */ 
/*     */           
/* 360 */           str13 = str28;
/* 361 */           str13 = str13 + c + str19;
/* 362 */           str13 = str13 + c + "" + bigDecimal;
/* 363 */           str13 = str13 + c + str20;
/* 364 */           str13 = str13 + c + str21;
/* 365 */           str13 = str13 + c + str22;
/* 366 */           str13 = str13 + c + str10;
/* 367 */           str13 = str13 + c + str44;
/*     */           
/* 369 */           recordSet3.executeProc("CptCapital_Duplicate", str13);
/* 370 */           recordSet3.next();
/* 371 */           str41 = recordSet3.getString(1);
/*     */           
/* 373 */           str39 = str41 + c + str39;
/* 374 */           str39 = str39 + c + "" + bigDecimal;
/* 375 */           str39 = str39 + c + str19;
/* 376 */           str39 = str39 + c + str45;
/* 377 */           str39 = str39 + c + str30;
/*     */           
/* 379 */           recordSet3.executeProc("CptUseLogInStock_Insert", str39);
/*     */           
/* 381 */           recordSet3.executeSql("update cptcapital set olddepartment = " + str6 + ",blongsubcompany='" + str47 + "', blongdepartment='" + str48 + "',contractno='" + str24 + "' where id = " + str41);
/*     */           
/* 383 */           String str66 = "select * from cptcapitalparts where cptid = " + str28;
/*     */           
/* 385 */           recordSet4.executeSql(str66);
/* 386 */           while (recordSet4.next()) {
/* 387 */             str66 = "insert into cptcapitalparts (cptid,partsname,partsspec,partssum,partsweight,partssize) select " + str41 + ",partsname,partsspec,partssum,partsweight,partssize from cptcapitalparts where id = " + recordSet4.getString("id");
/* 388 */             recordSet1.executeSql(str66);
/*     */           } 
/*     */           
/* 391 */           str66 = "select * from cptcapitalequipment where cptid = " + str28;
/* 392 */           recordSet4.executeSql(str66);
/*     */           
/* 394 */           while (recordSet4.next()) {
/* 395 */             str66 = "insert into cptcapitalequipment (cptid,equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage) select " + str41 + ",equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage from cptcapitalequipment where id = " + recordSet4.getString("id");
/* 396 */             recordSet1.executeSql(str66);
/*     */           } 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 402 */           String str67 = "";
/* 403 */           String str68 = "";
/* 404 */           String str69 = "";
/* 405 */           String str70 = "";
/* 406 */           String str71 = "";
/* 407 */           String str72 = "";
/* 408 */           String str73 = "";
/* 409 */           String str74 = "";
/* 410 */           String str75 = "";
/* 411 */           String str76 = "";
/* 412 */           String str77 = "";
/* 413 */           String str78 = "";
/* 414 */           String str79 = "";
/* 415 */           String str80 = "";
/*     */           
/* 417 */           recordSet3.executeSql("select * from CptAssortmentShare where assortmentid=" + str50);
/* 418 */           while (recordSet3.next()) {
/* 419 */             str68 = recordSet3.getString("sharetype");
/* 420 */             str69 = recordSet3.getString("seclevel");
/* 421 */             str70 = recordSet3.getString("rolelevel");
/* 422 */             str71 = recordSet3.getString("sharelevel");
/* 423 */             str72 = recordSet3.getString("userid");
/* 424 */             str73 = recordSet3.getString("departmentid");
/* 425 */             str74 = recordSet3.getString("roleid");
/* 426 */             str75 = recordSet3.getString("foralluser");
/* 427 */             str76 = recordSet3.getString("subcompanyid");
/* 428 */             str77 = recordSet3.getString("seclevelMax");
/* 429 */             str78 = recordSet3.getString("jobtitleid");
/* 430 */             str79 = recordSet3.getString("joblevel");
/* 431 */             str80 = recordSet3.getString("scopeid");
/*     */             
/* 433 */             str67 = str41;
/* 434 */             str67 = str67 + c + str68;
/* 435 */             str67 = str67 + c + str69;
/* 436 */             str67 = str67 + c + str70;
/* 437 */             str67 = str67 + c + str71;
/* 438 */             str67 = str67 + c + str72;
/* 439 */             str67 = str67 + c + str73;
/* 440 */             str67 = str67 + c + str74;
/* 441 */             str67 = str67 + c + str75;
/* 442 */             str67 = str67 + c + str76;
/* 443 */             str67 = str67 + c + str50;
/*     */             
/* 445 */             recordSet2.executeProc("CptShareInfo_Insert_dft", str67);
/* 446 */             recordSet2.executeSql("select max(id) from CptCapitalShareInfo ");
/* 447 */             recordSet2.next();
/* 448 */             int j = Util.getIntValue(recordSet2.getString(1), 0);
/* 449 */             if (j > 0) {
/* 450 */               recordSet2.executeSql("update CptCapitalShareInfo set seclevelMax='" + str77 + "',jobtitleid='" + str78 + "',joblevel='" + str79 + "',scopeid='" + str80 + "' where id=" + j);
/*     */             }
/*     */           } 
/*     */           
/*     */           try {
/* 455 */             cptShare.setCptShareByCpt(str41);
/* 456 */           } catch (Exception exception) {
/* 457 */             str1 = "false";
/* 458 */             exception.printStackTrace();
/*     */           } 
/* 460 */           arrayList.add(str41);
/*     */           continue;
/*     */         } 
/* 463 */         recordSet3.executeProc("CptCapital_SelectByDataType", str28 + c + str6);
/* 464 */         if (recordSet3.next()) {
/*     */ 
/*     */           
/* 467 */           str41 = recordSet3.getString("id");
/* 468 */           BigDecimal bigDecimal1 = new BigDecimal(recordSet3.getString("startprice"));
/* 469 */           BigDecimal bigDecimal2 = new BigDecimal(recordSet3.getString("capitalnum"));
/* 470 */           bigDecimal = bigDecimal.multiply(new BigDecimal(str36));
/* 471 */           bigDecimal = bigDecimal.add(bigDecimal1.multiply(bigDecimal2));
/* 472 */           bigDecimal = bigDecimal.divide(bigDecimal2.add(new BigDecimal(str36)), 2, 0);
/*     */ 
/*     */           
/* 475 */           str39 = str41 + c + str39;
/* 476 */           str39 = str39 + c + "" + bigDecimal;
/* 477 */           str39 = str39 + c + str19;
/* 478 */           str39 = str39 + c + str45;
/* 479 */           str39 = str39 + c + str30;
/*     */ 
/*     */           
/* 482 */           recordSet3.executeProc("CptUseLogInStock_Insert", str39);
/*     */ 
/*     */           
/* 485 */           str13 = str41;
/* 486 */           str13 = str13 + c + "" + bigDecimal;
/* 487 */           str13 = str13 + c + str20;
/* 488 */           str13 = str13 + c + str19;
/* 489 */           str13 = str13 + c + str21;
/* 490 */           str13 = str13 + c + str22;
/* 491 */           str13 = str13 + c + str10;
/* 492 */           recordSet3.executeProc("CptCapital_UpdatePrice", str13);
/*     */           
/*     */           continue;
/*     */         } 
/* 496 */         str13 = str28;
/* 497 */         str13 = str13 + c + str19;
/* 498 */         str13 = str13 + c + "" + bigDecimal;
/* 499 */         str13 = str13 + c + str20;
/* 500 */         str13 = str13 + c + str21;
/* 501 */         str13 = str13 + c + str22;
/* 502 */         str13 = str13 + c + str10;
/* 503 */         str13 = str13 + c + str44;
/*     */         
/* 505 */         recordSet3.executeProc("CptCapital_Duplicate", str13);
/* 506 */         recordSet3.next();
/* 507 */         str41 = recordSet3.getString(1);
/*     */         
/* 509 */         str39 = str41 + c + str39;
/* 510 */         str39 = str39 + c + "" + bigDecimal;
/* 511 */         str39 = str39 + c + str19;
/* 512 */         str39 = str39 + c + str45;
/* 513 */         str39 = str39 + c + str30;
/*     */ 
/*     */         
/* 516 */         recordSet3.executeProc("CptUseLogInStock_Insert", str39);
/*     */         
/* 518 */         recordSet3.executeSql("update cptcapital set olddepartment = " + str6 + ",blongsubcompany='" + str47 + "', blongdepartment='" + str48 + "',contractno='" + str24 + "' ,capitalnum='" + d + "'  where id = " + str41);
/*     */         
/* 520 */         String str51 = "select * from cptcapitalparts where cptid = " + str28;
/*     */         
/* 522 */         recordSet4.executeSql(str51);
/* 523 */         while (recordSet4.next()) {
/* 524 */           str51 = "insert into cptcapitalparts (cptid,partsname,partsspec,partssum,partsweight,partssize) select " + str41 + ",partsname,partsspec,partssum,partsweight,partssize from cptcapitalparts where id = " + recordSet4.getString("id");
/* 525 */           recordSet1.executeSql(str51);
/*     */         } 
/*     */         
/* 528 */         str51 = "select * from cptcapitalequipment where cptid = " + str28;
/* 529 */         recordSet4.executeSql(str51);
/*     */         
/* 531 */         while (recordSet4.next()) {
/* 532 */           str51 = "insert into cptcapitalequipment (cptid,equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage) select " + str41 + ",equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage from cptcapitalequipment where id = " + recordSet4.getString("id");
/* 533 */           recordSet1.executeSql(str51);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 538 */         String str52 = "";
/* 539 */         String str53 = "";
/* 540 */         String str54 = "";
/* 541 */         String str55 = "";
/* 542 */         String str56 = "";
/* 543 */         String str57 = "";
/* 544 */         String str58 = "";
/* 545 */         String str59 = "";
/* 546 */         String str60 = "";
/* 547 */         String str61 = "";
/* 548 */         String str62 = "";
/* 549 */         String str63 = "";
/* 550 */         String str64 = "";
/* 551 */         String str65 = "";
/*     */         
/* 553 */         recordSet3.executeSql("select * from CptAssortmentShare where assortmentid=" + str50);
/* 554 */         while (recordSet3.next()) {
/* 555 */           str53 = recordSet3.getString("sharetype");
/* 556 */           str54 = recordSet3.getString("seclevel");
/* 557 */           str55 = recordSet3.getString("rolelevel");
/* 558 */           str56 = recordSet3.getString("sharelevel");
/* 559 */           str57 = recordSet3.getString("userid");
/* 560 */           str58 = recordSet3.getString("departmentid");
/* 561 */           str59 = recordSet3.getString("roleid");
/* 562 */           str60 = recordSet3.getString("foralluser");
/* 563 */           str61 = recordSet3.getString("subcompanyid");
/* 564 */           str62 = recordSet3.getString("seclevelMax");
/* 565 */           str63 = recordSet3.getString("jobtitleid");
/* 566 */           str64 = recordSet3.getString("joblevel");
/* 567 */           str65 = recordSet3.getString("scopeid");
/* 568 */           str52 = str41;
/* 569 */           str52 = str52 + c + str53;
/* 570 */           str52 = str52 + c + str54;
/* 571 */           str52 = str52 + c + str55;
/* 572 */           str52 = str52 + c + str56;
/* 573 */           str52 = str52 + c + str57;
/* 574 */           str52 = str52 + c + str58;
/* 575 */           str52 = str52 + c + str59;
/* 576 */           str52 = str52 + c + str60;
/* 577 */           str52 = str52 + c + str61;
/* 578 */           str52 = str52 + c + str50;
/*     */           
/* 580 */           recordSet2.executeProc("CptShareInfo_Insert_dft", str52);
/* 581 */           recordSet2.executeSql("select max(id) from CptCapitalShareInfo ");
/* 582 */           recordSet2.next();
/* 583 */           int j = Util.getIntValue(recordSet2.getString(1), 0);
/* 584 */           if (j > 0) {
/* 585 */             recordSet2.executeSql("update CptCapitalShareInfo set seclevelMax='" + str62 + "',jobtitleid='" + str63 + "',joblevel='" + str64 + "',scopeid='" + str65 + "' where id=" + j);
/*     */           }
/*     */         } 
/*     */         
/*     */         try {
/* 590 */           cptShare.setCptShareByCpt(str41);
/* 591 */         } catch (Exception exception) {
/* 592 */           str1 = "false";
/* 593 */           exception.printStackTrace();
/*     */         } 
/* 595 */         arrayList.add(str41);
/*     */       } 
/*     */ 
/*     */       
/*     */       try {
/* 600 */         capitalComInfo.addCapitalCache(arrayList);
/* 601 */       } catch (Exception exception) {
/* 602 */         str1 = "false";
/* 603 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 606 */     for (b = 0; b < arrayOfString.length; b++) {
/* 607 */       String str11 = "" + Util.getIntValue(arrayOfString[b]);
/* 608 */       int i = user.getUID();
/* 609 */       String str12 = "select checkerid from CptStockInMain where id=" + str11;
/* 610 */       recordSet1.executeSql(str12);
/* 611 */       if (recordSet1.next()) {
/* 612 */         i = Util.getIntValue(recordSet1.getString("checkerid"));
/*     */       }
/* 614 */       poppupRemindInfoUtil.updatePoppupRemindInfo(i, 11, "0", Util.getIntValue(arrayOfString[b]));
/*     */     } 
/* 616 */     CptLowInventoryRemindJob.generateReminder();
/* 617 */     if ("".equals(str1)) {
/* 618 */       str1 = "true";
/*     */     }
/* 620 */     jSONObject.put("msg", str1);
/* 621 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/instockDelCapital")
/*     */   @Produces({"text/plain"})
/*     */   public String instockDelCapital(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 634 */     JSONObject jSONObject = new JSONObject();
/* 635 */     User user = MobileUserInit.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 636 */     RecordSet recordSet = new RecordSet();
/* 637 */     String str1 = Util.fromScreen(paramHttpServletRequest.getParameter("id"), user.getLanguage());
/* 638 */     String str2 = "update CptStockInMain set ischecked = -1 where id = " + str1;
/* 639 */     recordSet.executeSql(str2);
/* 640 */     int i = user.getUID();
/* 641 */     str2 = "select checkerid from CptStockInMain where id=" + str1;
/* 642 */     recordSet.executeSql(str2);
/* 643 */     if (recordSet.next()) {
/* 644 */       i = Util.getIntValue(recordSet.getString("checkerid"));
/*     */     }
/* 646 */     PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/* 647 */     poppupRemindInfoUtil.updatePoppupRemindInfo(i, 11, "0", Util.getIntValue(str1));
/*     */     
/* 649 */     jSONObject.put("msg", "true");
/* 650 */     return jSONObject.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/CptCapitalInstockAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */