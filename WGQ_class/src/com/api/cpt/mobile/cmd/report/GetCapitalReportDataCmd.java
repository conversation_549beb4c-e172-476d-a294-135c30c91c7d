/*     */ package com.api.cpt.mobile.cmd.report;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetCapitalReportDataCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetCapitalReportDataCmd(Map<String, Object> paramMap, User paramUser) {
/*  32 */     this.user = paramUser;
/*  33 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  38 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  45 */     RecordSet recordSet = new RecordSet();
/*  46 */     CommonShareManager commonShareManager = new CommonShareManager();
/*     */     
/*  48 */     String str1 = Util.null2String(Util.null2String(this.params.get("reporttype")), "group");
/*     */     
/*  50 */     String str2 = "";
/*  51 */     String str3 = "";
/*  52 */     String str4 = "";
/*  53 */     String str5 = "";
/*  54 */     String str6 = "";
/*     */     
/*  56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  57 */     String str7 = Util.null2String(this.params.get("fromdate"));
/*  58 */     String str8 = Util.null2String(this.params.get("todate"));
/*  59 */     String str9 = Util.null2String(this.params.get("subcompanyid1"));
/*     */     
/*  61 */     if ("group".equals(str1)) {
/*  62 */       String str = Util.null2String(this.params.get("assortmentid"));
/*     */       
/*  64 */       str2 = " where t1.isdata='2' " + commonShareManager.getAssortmentSqlWhere(this.user);
/*  65 */       if (!"".equals(str)) {
/*  66 */         str2 = str2 + " and t1.capitalgroupid='" + str + "' ";
/*     */       }
/*  68 */       if (!str7.equals("")) {
/*  69 */         str2 = str2 + " and t1.createdate>='" + str7 + "' ";
/*     */       }
/*  71 */       if (!str8.equals("")) {
/*  72 */         str2 = str2 + " and t1.createdate<='" + str8 + "' ";
/*     */       }
/*  74 */       if (!str9.equals("")) {
/*  75 */         str2 = str2 + " and t1.blongsubcompany='" + str9 + "' ";
/*     */       }
/*     */       
/*  78 */       str2 = str2 + " group by t5.lv1groupid ";
/*     */       
/*  80 */       str3 = " t5.lv1groupid,t5.lv1groupid as name, COUNT(t1.id) AS resultcount ";
/*  81 */       str4 = " CptCapital  t1 ";
/*  82 */       if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/*  83 */         str4 = str4 + " join ( select t4.id as cptgroupid,t3.id as lv1groupid,t3.assortmentname from CptCapitalAssortment t3,CptCapitalAssortment t4 where ( t4.supassortmentstr like '%|'+convert(varchar,t3.id)+'|%' or t4.id=t3.id ) and t3.supassortmentid = 0 ) t5 on t5.cptgroupid=t1.capitalgroupid";
/*  84 */       } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  85 */         str4 = str4 + " join ( select t4.id as cptgroupid,t3.id as lv1groupid,t3.assortmentname from CptCapitalAssortment t3,CptCapitalAssortment t4 where ( t4.supassortmentstr like concat('%|',cast(t3.id as char),'|%') or t4.id=t3.id ) and t3.supassortmentid = 0 ) t5 on t5.cptgroupid=t1.capitalgroupid";
/*     */       } else {
/*  87 */         str4 = str4 + " join ( select t4.id as cptgroupid,t3.id as lv1groupid,t3.assortmentname from CptCapitalAssortment t3,CptCapitalAssortment t4 where ( t4.supassortmentstr like '%|'||to_char(t3.id)||'|%' or t4.id=t3.id ) and t3.supassortmentid = 0 ) t5 on t5.cptgroupid=t1.capitalgroupid";
/*     */       } 
/*  89 */       str5 = " lv1groupid";
/*  90 */       str6 = " lv1groupid";
/*  91 */       arrayList.add(new SplitTableColBean("true", "lv1groupid"));
/*  92 */       arrayList.add(new SplitTableColBean("name", "com.api.cpt.mobile.util.CapitalTransUtil.getAssortmentName", "", 0));
/*  93 */       arrayList.add(new SplitTableColBean("true", "resultcount"));
/*     */     }
/*  95 */     else if ("resource".equals(str1)) {
/*  96 */       String str = Util.null2String(this.params.get("resourceid"));
/*     */       
/*  98 */       str2 = " where t1.isdata='2' and t1.resourceid > 1 " + commonShareManager.getAssortmentSqlWhere(this.user);
/*  99 */       if (!"".equals(str)) {
/* 100 */         str2 = str2 + " and t1.resourceid='" + str + "' ";
/*     */       }
/* 102 */       if (!str7.equals("")) {
/* 103 */         str2 = str2 + " and t1.createdate>='" + str7 + "' ";
/*     */       }
/* 105 */       if (!str8.equals("")) {
/* 106 */         str2 = str2 + " and t1.createdate<='" + str8 + "' ";
/*     */       }
/* 108 */       if (!str9.equals("")) {
/* 109 */         str2 = str2 + " and t1.blongsubcompany='" + str9 + "' ";
/*     */       }
/*     */       
/* 112 */       str2 = str2 + " group by t1.resourceid ";
/*     */       
/* 114 */       str3 = " t1.resourceid,t1.resourceid as name,COUNT(t1.id) AS resultcount ";
/* 115 */       str4 = " CptCapital  t1  ";
/* 116 */       str5 = " resourceid";
/* 117 */       str6 = " resourceid";
/*     */       
/* 119 */       arrayList.add(new SplitTableColBean("true", "resourceid"));
/* 120 */       arrayList.add(new SplitTableColBean("name", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/* 121 */       arrayList.add(new SplitTableColBean("true", "resultcount"));
/*     */     }
/* 123 */     else if ("dept".equals(str1)) {
/*     */       
/* 125 */       String str = Util.null2String(this.params.get("departmentid"));
/*     */       
/* 127 */       str2 = " where t1.isdata='2' and t1.departmentid>0  " + commonShareManager.getAssortmentSqlWhere(this.user);
/* 128 */       if (!"".equals(str)) {
/* 129 */         str2 = str2 + " and t1.departmentid='" + str + "' ";
/*     */       }
/* 131 */       if (!str7.equals("")) {
/* 132 */         str2 = str2 + " and t1.createdate>='" + str7 + "' ";
/*     */       }
/* 134 */       if (!str8.equals("")) {
/* 135 */         str2 = str2 + " and t1.createdate<='" + str8 + "' ";
/*     */       }
/* 137 */       if (!str9.equals("")) {
/* 138 */         str2 = str2 + " and t1.blongsubcompany='" + str9 + "' ";
/*     */       }
/*     */       
/* 141 */       str2 = str2 + " group by t1.departmentid ";
/*     */       
/* 143 */       str3 = " t1.departmentid,t1.departmentid as name,COUNT(t1.id) AS resultcount ";
/* 144 */       str4 = " CptCapital t1  ";
/* 145 */       str5 = " departmentid";
/* 146 */       str6 = " departmentid";
/*     */       
/* 148 */       arrayList.add(new SplitTableColBean("true", "departmentid"));
/* 149 */       arrayList.add(new SplitTableColBean("name", "com.api.cpt.mobile.util.CapitalTransUtil.getDepartmentname", "", 0));
/* 150 */       arrayList.add(new SplitTableColBean("true", "resultcount"));
/*     */     }
/* 152 */     else if ("state".equals(str1)) {
/*     */       
/* 154 */       String str = Util.null2String(this.params.get("stateid"));
/*     */       
/* 156 */       str2 = " where t1.isdata='2' " + commonShareManager.getAssortmentSqlWhere(this.user);
/* 157 */       if (!"".equals(str)) {
/* 158 */         str2 = str2 + " and t1.stateid='" + str + "' ";
/*     */       }
/* 160 */       if (!str7.equals("")) {
/* 161 */         str2 = str2 + " and t1.createdate>='" + str7 + "' ";
/*     */       }
/* 163 */       if (!str8.equals("")) {
/* 164 */         str2 = str2 + " and t1.createdate<='" + str8 + "' ";
/*     */       }
/* 166 */       if (!str9.equals("")) {
/* 167 */         str2 = str2 + " and t1.blongsubcompany='" + str9 + "' ";
/*     */       }
/*     */       
/* 170 */       str2 = str2 + " group by t1.stateid ";
/*     */       
/* 172 */       str3 = " t1.stateid,t1.stateid as name,COUNT(t1.id) AS resultcount ";
/* 173 */       str4 = " CptCapital  t1  ";
/* 174 */       str5 = " stateid";
/* 175 */       str6 = " stateid";
/*     */       
/* 177 */       arrayList.add(new SplitTableColBean("true", "stateid"));
/* 178 */       arrayList.add(new SplitTableColBean("name", "com.api.cpt.mobile.util.CapitalTransUtil.getBrowserShowName", "243+ ", 0));
/* 179 */       arrayList.add(new SplitTableColBean("true", "resultcount"));
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 189 */     String str10 = "<div class='template-wrapper'>\t<div class='template-main-box'>\t\t<p class='template-row template-title'>\t\t\t<span>#{name}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(503026, this.user.getLanguage()) + "：#{resultcount}</span>\t\t</p>\t</div></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 194 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str10);
/*     */     
/* 196 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, str6, arrayList);
/*     */     try {
/* 198 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */       
/* 200 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 201 */       splitTableBean.setSqlsortway("asc");
/* 202 */     } catch (Exception exception) {
/* 203 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 207 */     int i = 0;
/* 208 */     String str11 = "(select " + str3 + " from " + str4 + str2 + ")t";
/* 209 */     String str12 = "select sum(resultcount) sum from " + str11;
/* 210 */     recordSet.execute(str12);
/* 211 */     if (recordSet.next())
/* 212 */       i = Util.getIntValue(recordSet.getString("sum"), 0); 
/* 213 */     hashMap.put("sum", Integer.valueOf(i));
/* 214 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 215 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/report/GetCapitalReportDataCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */