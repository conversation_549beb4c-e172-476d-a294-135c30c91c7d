/*     */ package com.api.cpt.mobile.cmd.inventory;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoInventoryCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DoInventoryCmd(Map<String, Object> paramMap, User paramUser) {
/*  25 */     this.user = paramUser;
/*  26 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  31 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  37 */     String str1 = Util.null2String(this.params.get("id"));
/*  38 */     String str2 = TimeUtil.getCurrentDateString();
/*  39 */     RecordSet recordSet1 = new RecordSet();
/*  40 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*     */     try {
/*  43 */       String str3 = "select a.id from cpt_inventory_detail a LEFT JOIN cpt_inventory_planlist b ON a.mainid=b.id WHERE b.planliststate in (1,2,3) AND a.cptid=" + str1 + " and b.countuser=" + this.user.getUID();
/*  44 */       String str4 = "";
/*  45 */       recordSet1.execute(str3);
/*  46 */       if (recordSet1.next()) {
/*  47 */         str4 = Util.null2String(recordSet1.getString("id"));
/*     */       }
/*  49 */       CapitalComInfo capitalComInfo = new CapitalComInfo();
/*     */       
/*  51 */       recordSet2.execute("select capitalnum from cptcapital where id=" + str1);
/*  52 */       recordSet2.next();
/*  53 */       double d1 = Util.getDoubleValue(recordSet2.getString("capitalnum"), 0.0D);
/*  54 */       double d2 = Util.getDoubleValue(Util.null2String(this.params.get("countnum")), 0.0D);
/*  55 */       String str5 = Util.null2String(this.params.get("remark"));
/*  56 */       String str6 = Util.null2String(this.params.get("cptimgid"));
/*  57 */       String str7 = "";
/*  58 */       if (d2 == d1) {
/*  59 */         str7 = "4";
/*  60 */       } else if (d2 > d1) {
/*  61 */         str7 = "5";
/*  62 */       } else if (d2 < d1) {
/*  63 */         str7 = "6";
/*     */       } 
/*  65 */       recordSet1.execute("update cpt_inventory_detail set actualnum=" + d2 + ",detailstate=" + str7 + ",inventorydate='" + str2 + "',remark='" + str5 + "',cptimgid='" + str6 + "' where id=" + str4);
/*  66 */       recordSet2.execute("select planid,mainid from cpt_inventory_detail where id=" + str4);
/*  67 */       recordSet2.next();
/*  68 */       String str8 = Util.null2String(recordSet2.getString("planid"));
/*  69 */       String str9 = Util.null2String(recordSet2.getString("mainid"));
/*     */       
/*  71 */       recordSet1.execute("update cpt_inventory_plan set inventorystate=2 where id=" + str8);
/*     */       
/*  73 */       recordSet2.execute("select count(*) as count  from cpt_inventory_detail where (detailstate=0 or detailstate=1) and planid=" + str8);
/*  74 */       recordSet2.next();
/*  75 */       String str10 = Util.null2String(recordSet2.getString(1));
/*  76 */       if ("0".equals(str10)) {
/*     */         
/*  78 */         recordSet1.execute("update cpt_inventory_planlist set planliststate=3,uncountnum=0,countednum=totalnum where id=" + str9);
/*     */       } else {
/*  80 */         recordSet1.execute("select count(*) as count  from cpt_inventory_detail where (detailstate=0 or detailstate=1) and planid=" + str8 + " and mainid=" + str9);
/*  81 */         recordSet1.next();
/*  82 */         String str = Util.null2String(recordSet1.getString(1));
/*  83 */         if ("0".equals(str)) {
/*     */           
/*  85 */           recordSet1.execute("update cpt_inventory_planlist set planliststate=3,uncountnum=0,countednum=totalnum where id=" + str9);
/*     */         } else {
/*     */           
/*  88 */           recordSet1.execute("update cpt_inventory_planlist set planliststate=2,uncountnum=" + str + " where id=" + str9);
/*     */           
/*  90 */           recordSet1.execute("update cpt_inventory_planlist set countednum=totalnum-uncountnum where id=" + str9);
/*  91 */           recordSet1.execute("update cpt_inventory_plan set inventorystate=2 where id=" + str8);
/*     */         } 
/*     */       } 
/*     */       
/*  95 */       if (CapitalTransMethod.isRecordPosition()) {
/*     */         
/*  97 */         String str11 = Util.null2String(this.params.get("address"));
/*  98 */         String str12 = Util.null2String(this.params.get("longitude"));
/*  99 */         String str13 = Util.null2String(this.params.get("latitude"));
/* 100 */         recordSet1.executeUpdate("update CptCapital set address=?,longitude=?,latitude=? where id=?", new Object[] { str11, str12, str13, str1 });
/*     */       } 
/*     */       
/* 103 */       hashMap.put("flag", Boolean.valueOf(true));
/* 104 */     } catch (Exception exception) {
/* 105 */       hashMap.put("flag", Boolean.valueOf(false));
/*     */     } 
/* 107 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/inventory/DoInventoryCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */