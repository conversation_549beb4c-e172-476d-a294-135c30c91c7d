/*     */ package com.api.cpt.mobile.cmd.inventory;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetInventoryListDetailCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetInventoryListDetailCmd(Map<String, Object> paramMap, User paramUser) {
/*  29 */     this.user = paramUser;
/*  30 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  35 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  42 */     String str1 = Util.null2String(this.params.get("inventoryId"));
/*  43 */     String str2 = Util.null2String(this.params.get("name"));
/*  44 */     String str3 = Util.null2String(this.params.get("state"));
/*     */     
/*  46 */     String str4 = "a.id,a.planid,a.mainid,a.cptid,a.actualnum,a.inventorydate,a.remark,a.detailstate,a.detailstate as detailstate1,a.cptname,b.id as delid,b.mark,b.capitalspec,b.blongsubcompany,b.blongdepartment,b.resourceid,b.departmentid,b.sptcount,b.capitalnum,b.capitalimageid ";
/*  47 */     String str5 = " cpt_inventory_detail a left join CptCapital b on a.cptid = b.id ";
/*  48 */     String str6 = " a.id ";
/*  49 */     String str7 = " a.id";
/*     */     
/*  51 */     String str8 = " where a.mainid=" + str1;
/*  52 */     if (!"".equals(str2)) {
/*  53 */       str8 = str8 + " and a.cptname like '%" + str2 + "%' ";
/*     */     }
/*  55 */     if (!"".equals(str3)) {
/*  56 */       if ("1".equals(str3)) {
/*  57 */         str8 = str8 + " and a.detailstate not in(4,5,6) ";
/*  58 */       } else if ("2".equals(str3)) {
/*  59 */         str8 = str8 + " and a.detailstate in(4,5,6) ";
/*  60 */       } else if ("3".equals(str3)) {
/*  61 */         str8 = str8 + " and a.detailstate in(5) ";
/*  62 */       } else if ("4".equals(str3)) {
/*  63 */         str8 = str8 + " and a.detailstate in(6) ";
/*     */       } 
/*     */     }
/*     */     
/*  67 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  68 */     arrayList.add(new SplitTableColBean("id", "com.api.cpt.mobile.util.CapitalTransUtil.getInventoryCptName", "" + this.user.getLanguage() + "", 0));
/*  69 */     arrayList.add(new SplitTableColBean("true", "delid"));
/*  70 */     arrayList.add(new SplitTableColBean("true", "cptid"));
/*  71 */     arrayList.add(new SplitTableColBean("true", "cptname"));
/*  72 */     arrayList.add(new SplitTableColBean("true", "mark"));
/*  73 */     arrayList.add(new SplitTableColBean("capitalnum", "com.engine.cpt.util.CapitalTransMethod.getDesensAndDecryptData", "capitalnum", 0));
/*  74 */     arrayList.add(new SplitTableColBean("true", "actualnum"));
/*  75 */     arrayList.add(new SplitTableColBean("true", "inventorydate"));
/*  76 */     arrayList.add(new SplitTableColBean("true", "detailstate"));
/*  77 */     arrayList.add(new SplitTableColBean("resourceid", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/*  78 */     arrayList.add(new SplitTableColBean("departmentid", "com.api.cpt.mobile.util.CapitalTransUtil.getDepartmentname", "", 0));
/*  79 */     arrayList.add(new SplitTableColBean("capitalimageid", "com.api.cpt.mobile.util.CapitalTransUtil.getImgCapitalById", "", 0));
/*  80 */     arrayList.add(new SplitTableColBean("detailstate1", "com.api.cpt.mobile.util.CapitalTransUtil.getInventorystate", "" + this.user.getLanguage() + "", 0));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 102 */     String str9 = "<div class='template-wrapper'>\t<div class='template-avator-box'>\t\t<img class='template-avatar' src='#{capitalimageid}'>\t</div>\t<div class='template-main-box'>\t\t<p class='template-row template-title'>\t\t\t<span>#{id}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(82306, this.user.getLanguage()) + "：#{mark}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1508, this.user.getLanguage()) + "：#{resourceidspan}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(21030, this.user.getLanguage()) + "：#{departmentidspan}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(503026, this.user.getLanguage()) + "：#{capitalnum}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1418, this.user.getLanguage()) + "：#{actualnum}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1415, this.user.getLanguage()) + "：#{inventorydate}</span>\t\t</p>\t</div>\t<div class='template-dot-box'>\t\t<div class='template-dot-#{detailstate}'>#{detailstate1}</div>\t</div></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 110 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str9);
/*     */     
/* 112 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str8, str6, str7, arrayList);
/*     */     try {
/* 114 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */       
/* 116 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 117 */       splitTableBean.setSqlsortway("desc");
/* 118 */     } catch (Exception exception) {
/* 119 */       exception.printStackTrace();
/*     */     } 
/* 121 */     hashMap.put("isright", Boolean.valueOf(true));
/* 122 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 123 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/inventory/GetInventoryListDetailCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */