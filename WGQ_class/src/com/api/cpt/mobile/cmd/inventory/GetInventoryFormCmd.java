/*    */ package com.api.cpt.mobile.cmd.inventory;
/*    */ 
/*    */ import com.api.cpt.mobile.util.ManagerFormItemUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetInventoryFormCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetInventoryFormCmd(Map<String, Object> paramMap, User paramUser) {
/* 18 */     this.user = paramUser;
/* 19 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 24 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 29 */     String str1 = Util.null2String(this.params.get("id"));
/* 30 */     String str2 = Util.null2String(this.params.get("cptid"));
/* 31 */     String str3 = Util.null2String(this.params.get("viewAttr"));
/* 32 */     return ManagerFormItemUtil.getInventoryForm(str1, str2, str3, this.user);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/inventory/GetInventoryFormCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */