/*     */ package com.api.cpt.mobile.cmd.inventory;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetMyInventoryListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetMyInventoryListCmd(Map<String, Object> paramMap, User paramUser) {
/*  29 */     this.user = paramUser;
/*  30 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  35 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  42 */     String str1 = Util.null2String(this.params.get("name"));
/*  43 */     String str2 = Util.null2String(this.params.get("mark"));
/*  44 */     String str3 = Util.null2s(Util.null2String(this.params.get("inventorystate")), "1");
/*     */     
/*  46 */     String str4 = " m.id,m.id as substr,m.name,m.description,m.mark,m.planliststate,m.planliststate as planliststate1,n.name as planname,n.planenddate,m.totalnum,m.uncountnum,m.countednum,m.countuser  ";
/*  47 */     String str5 = " cpt_inventory_planlist m,cpt_inventory_plan n";
/*  48 */     String str6 = " where m.planliststate <>0 and m.mainid =n.id and m.countuser=" + this.user.getUID();
/*     */     
/*  50 */     if (!"".equals(str1)) {
/*  51 */       str6 = str6 + " and m.name like '%" + str1 + "%' ";
/*     */     }
/*     */     
/*  54 */     if (!"".equals(str2)) {
/*  55 */       str6 = str6 + " and m.mark like '%" + str2 + "%' ";
/*     */     }
/*     */     
/*  58 */     if ("1".equals(str3)) {
/*  59 */       str6 = str6 + " and planliststate in(7,8)";
/*     */     } else {
/*  61 */       str6 = str6 + " and planliststate in(1,2,3)";
/*     */     } 
/*  63 */     String str7 = "n.id desc,m.id";
/*  64 */     String str8 = " m.id";
/*     */     
/*  66 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  67 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  68 */     arrayList.add(new SplitTableColBean("true", "planname"));
/*  69 */     arrayList.add(new SplitTableColBean("true", "planenddate"));
/*  70 */     arrayList.add(new SplitTableColBean("true", "mark"));
/*  71 */     arrayList.add(new SplitTableColBean("true", "name"));
/*  72 */     arrayList.add(new SplitTableColBean("true", "totalnum"));
/*  73 */     arrayList.add(new SplitTableColBean("true", "countednum"));
/*  74 */     arrayList.add(new SplitTableColBean("true", "planliststate"));
/*  75 */     arrayList.add(new SplitTableColBean("planliststate1", "com.api.cpt.mobile.util.CapitalTransUtil.getInventorystate", "" + this.user.getLanguage() + "", 0));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  94 */     String str9 = "<div class='template-wrapper'>\t<div class='template-main-box'>\t\t<p class='template-row template-title'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(383938, this.user.getLanguage()) + "：#{name}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(383937, this.user.getLanguage()) + "：#{mark}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(384654, this.user.getLanguage()) + "：#{planname}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(503727, this.user.getLanguage()) + "：#{planenddate}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(503728, this.user.getLanguage()) + "：#{totalnum}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(384658, this.user.getLanguage()) + "：#{countednum}</span>\t\t</p>\t</div>\t<div class='template-dot-box'>\t\t<div class='template-dot-#{planliststate}' >#{planliststate1}</div>\t</div></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 102 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str9);
/*     */     
/* 104 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str6, str7, str8, arrayList);
/*     */     try {
/* 106 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */       
/* 108 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 109 */       splitTableBean.setSqlsortway("asc");
/* 110 */     } catch (Exception exception) {
/* 111 */       exception.printStackTrace();
/*     */     } 
/* 113 */     hashMap.put("isright", Boolean.valueOf(true));
/* 114 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 115 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/inventory/GetMyInventoryListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */