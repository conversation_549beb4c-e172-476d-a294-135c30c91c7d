/*    */ package com.api.cpt.mobile.cmd.inventory;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import com.weaver.formmodel.util.DateHelper;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoSubmitInventoryCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoSubmitInventoryCmd(Map<String, Object> paramMap, User paramUser) {
/* 20 */     this.user = paramUser;
/* 21 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 26 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str = Util.null2String(this.params.get("inventoryId"));
/* 33 */     RecordSet recordSet = new RecordSet();
/*    */     try {
/* 35 */       recordSet.execute("update cpt_inventory_planlist set planliststate=7 where id=" + str);
/* 36 */       recordSet.execute("select mainid from cpt_inventory_planlist where id=" + str);
/* 37 */       recordSet.next();
/* 38 */       String str1 = recordSet.getString(1);
/*    */       
/* 40 */       recordSet.execute("select count(*) as count  from cpt_inventory_planlist where planliststate<>7 and mainid=" + str1 + " and id <>" + str);
/* 41 */       recordSet.next();
/* 42 */       String str2 = Util.null2String(recordSet.getString(1));
/* 43 */       if ("0".equals(str2)) {
/* 44 */         String str3 = DateHelper.getCurrentDate();
/*    */         
/* 46 */         recordSet.execute("update cpt_inventory_plan set inventorystate=7,actenddate='" + str3 + "' where id=" + str1);
/*    */       } 
/* 48 */       hashMap.put("flag", Boolean.valueOf(true));
/* 49 */     } catch (Exception exception) {
/* 50 */       hashMap.put("flag", Boolean.valueOf(true));
/*    */     } 
/* 52 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/inventory/DoSubmitInventoryCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */