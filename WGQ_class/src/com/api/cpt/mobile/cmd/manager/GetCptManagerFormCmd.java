/*    */ package com.api.cpt.mobile.cmd.manager;
/*    */ 
/*    */ import com.api.cpt.mobile.util.ManagerFormItemUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetCptManagerFormCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetCptManagerFormCmd(Map<String, Object> paramMap, User paramUser) {
/* 20 */     this.user = paramUser;
/* 21 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 26 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 31 */     String str = Util.null2String(this.params.get("mtype"));
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 33 */     return ManagerFormItemUtil.getCptManagerForm(str, this.user, hashMap);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/GetCptManagerFormCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */