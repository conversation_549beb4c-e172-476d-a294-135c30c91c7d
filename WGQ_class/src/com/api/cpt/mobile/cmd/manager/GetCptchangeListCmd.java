/*    */ package com.api.cpt.mobile.cmd.manager;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*    */ import com.cloudstore.dev.api.util.Util_MobileData;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetCptchangeListCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetCptchangeListCmd(Map<String, Object> paramMap, User paramUser) {
/* 30 */     this.user = paramUser;
/* 31 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 36 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 43 */     String str1 = Util.null2String(this.params.get("capitalid"));
/*    */     
/* 45 */     String str2 = " t1.id,t1.cptid,t1.field,t1.oldvalue,t1.currentvalue,t1.resourceid,t1.modifydate ";
/* 46 */     String str3 = " CptCapitalModify t1 ";
/* 47 */     String str4 = "";
/* 48 */     if (!str1.equals("")) {
/* 49 */       str4 = " where t1.cptid=" + str1;
/*    */     } else {
/* 51 */       str4 = " where 1=2";
/*    */     } 
/* 53 */     String str5 = " t1.id desc";
/* 54 */     String str6 = " t1.id";
/*    */     
/* 56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 57 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 58 */     arrayList.add(new SplitTableColBean("field", "com.engine.cpt.util.CapitalTransMethod.getFieldName", "column:cptid+" + this.user.getLanguage(), 0));
/* 59 */     arrayList.add(new SplitTableColBean("resourceimg", "com.api.cpt.mobile.util.CapitalTransUtil.getMessagerUrls", "", 0));
/* 60 */     arrayList.add(new SplitTableColBean("resourceid", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/* 61 */     arrayList.add(new SplitTableColBean("true", "modifydate"));
/* 62 */     arrayList.add(new SplitTableColBean("currentvalue", "com.engine.cpt.util.CapitalTransMethod.getDesensAndDecryptData", "column:field", 0));
/* 63 */     arrayList.add(new SplitTableColBean("oldvalue", "com.engine.cpt.util.CapitalTransMethod.getDesensAndDecryptData", "column:field", 0));
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 79 */     String str7 = "<div class='template-wrapper'>\t<div class='template-avator-box'>\t\t<img class='template-avatar' src='#{resourceimg}'>\t</div>\t<div class='template-main-box'>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(17482, this.user.getLanguage()) + "：#{resourceidspan}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(685, this.user.getLanguage()) + "：#{field}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(723, this.user.getLanguage()) + "：#{modifydate}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1450, this.user.getLanguage()) + "：#{currentvalue}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(6056, this.user.getLanguage()) + "：#{oldvalue}</span>\t\t</p>\t</div></div>";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 85 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str7);
/*    */     
/* 87 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, str6, arrayList);
/*    */     try {
/* 89 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*    */       
/* 91 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 92 */       splitTableBean.setSqlsortway("desc");
/* 93 */     } catch (Exception exception) {
/* 94 */       exception.printStackTrace();
/*    */     } 
/* 96 */     hashMap.put("isright", Boolean.valueOf(true));
/* 97 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 98 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/GetCptchangeListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */