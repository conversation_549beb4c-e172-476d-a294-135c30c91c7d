/*     */ package com.api.cpt.mobile.cmd.manager;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetCptflowListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetCptflowListCmd(Map<String, Object> paramMap, User paramUser) {
/*  30 */     this.user = paramUser;
/*  31 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  36 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  43 */     String str1 = Util.null2String(this.params.get("capitalid"));
/*     */     
/*  45 */     String str2 = " id,capitalid,usedate,usedeptid,useresourceid,usecount,useaddress,userequest,usestatus,usestatus as usestatus1,resourceid";
/*  46 */     String str3 = " CptUseLog m";
/*  47 */     String str4 = "";
/*  48 */     if (!str1.equals("")) {
/*  49 */       str4 = " where capitalid = " + str1;
/*     */     } else {
/*  51 */       str4 = " where 1=2";
/*     */     } 
/*  53 */     String str5 = " m.id desc";
/*  54 */     String str6 = " m.id";
/*     */     
/*  56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  57 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  58 */     arrayList.add(new SplitTableColBean("true", "usedate"));
/*  59 */     arrayList.add(new SplitTableColBean("usedeptid", "com.api.cpt.mobile.util.CapitalTransUtil.getDepartmentname", "", 0));
/*  60 */     arrayList.add(new SplitTableColBean("useresourceid", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/*  61 */     arrayList.add(new SplitTableColBean("resourceid", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/*  62 */     arrayList.add(new SplitTableColBean("true", "usecount"));
/*  63 */     arrayList.add(new SplitTableColBean("usestatus", "com.api.cpt.mobile.util.CapitalTransUtil.getBrowserShowName", "243+ ", 0));
/*  64 */     arrayList.add(new SplitTableColBean("true", "usestatus1"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  79 */     String str7 = "<div class='template-wrapper'>\t<div class='template-date-box'>\t\t<p class='template-row'>\t\t\t<span>#{usedate}</span>\t\t</p>\t</div>\t<div class='template-main-box'>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(390191, this.user.getLanguage()) + "：#{usedeptidspan}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(390192, this.user.getLanguage()) + "：#{useresourceidspan}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1331, this.user.getLanguage()) + "：#{usecount}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(17482, this.user.getLanguage()) + "：#{resourceidspan}</span>\t\t</p>\t</div>\t<div class='template-dot-box'>\t\t<div class='template-dot-#{usestatus1}'>#{usestatus}</div>\t</div></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  88 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str7);
/*     */     
/*  90 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, str6, arrayList);
/*     */     try {
/*  92 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */       
/*  94 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/*  95 */       splitTableBean.setSqlsortway("desc");
/*  96 */     } catch (Exception exception) {
/*  97 */       exception.printStackTrace();
/*     */     } 
/*  99 */     hashMap.put("isright", Boolean.valueOf(true));
/* 100 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 101 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/GetCptflowListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */