/*    */ package com.api.cpt.mobile.cmd.manager;
/*    */ 
/*    */ import com.api.cpt.mobile.util.ManagerFormItemUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetCptLinkageCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetCptLinkageCmd(Map<String, Object> paramMap, User paramUser) {
/* 19 */     this.user = paramUser;
/* 20 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 25 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 30 */     String str = Util.null2String(this.params.get("cptid"));
/* 31 */     return ManagerFormItemUtil.getCptLinkageDataById(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/GetCptLinkageCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */