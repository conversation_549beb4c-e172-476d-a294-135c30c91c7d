/*    */ package com.api.cpt.mobile.cmd.manager;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.cpt.capital.CapitalComInfo;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoCptIfOverAjaxCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoCptIfOverAjaxCmd(Map<String, Object> paramMap, User paramUser) {
/* 22 */     this.user = paramUser;
/* 23 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 28 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 33 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 34 */     RecordSet recordSet = new RecordSet();
/* 35 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*    */     
/* 37 */     String str1 = Util.null2String(this.params.get("poststr"));
/* 38 */     String str2 = Util.null2String(this.params.get("cpttype"));
/* 39 */     String str3 = "";
/* 40 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 41 */     String[] arrayOfString = Util.TokenizerString2(str1, "|");
/* 42 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 43 */       String[] arrayOfString1 = Util.TokenizerString2(arrayOfString[b], ",");
/* 44 */       if (arrayOfString1.length >= 2) {
/* 45 */         String str = "select capitalnum,frozennum from cptcapital where id=" + arrayOfString1[0];
/* 46 */         recordSet.execute(str);
/* 47 */         if (recordSet.next()) {
/* 48 */           double d1 = Util.getDoubleValue(recordSet.getString("cptcapital", "capitalnum", true, true), 0.0D);
/* 49 */           double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/* 50 */           if ("change".equals(str2)) {
/* 51 */             if (Util.getDoubleValue(arrayOfString1[1], 0.0D) < d2) {
/* 52 */               str3 = SystemEnv.getHtmlLabelName(503146, this.user.getLanguage()) + capitalComInfo.getCapitalname(arrayOfString1[0]) + SystemEnv.getHtmlLabelName(503147, this.user.getLanguage()) + d2;
/*    */               break;
/*    */             } 
/*    */           } else {
/* 56 */             if (d2 < 0.0D) d2 = 0.0D; 
/* 57 */             double d = 0.0D;
/* 58 */             if (d1 - d2 < 0.0D) {
/* 59 */               d = 0.0D;
/*    */             } else {
/* 61 */               d = d1 - d2;
/*    */             } 
/* 63 */             if (hashMap2.containsKey(arrayOfString1[0])) {
/* 64 */               hashMap2.put(arrayOfString1[0], "" + (Util.getDoubleValue("" + (String)hashMap2.get(arrayOfString1[0]), 0.0D) + Util.getDoubleValue(arrayOfString1[1], 0.0D)));
/*    */             } else {
/* 66 */               hashMap2.put(arrayOfString1[0], arrayOfString1[1]);
/*    */             } 
/* 68 */             if (d < Util.getDoubleValue("" + (String)hashMap2.get(arrayOfString1[0]), 0.0D)) {
/* 69 */               str3 = capitalComInfo.getCapitalname(arrayOfString1[0]) + " " + SystemEnv.getHtmlLabelName(33044, this.user.getLanguage());
/*    */               
/*    */               break;
/*    */             } 
/*    */           } 
/*    */         } 
/*    */       } 
/*    */     } 
/* 77 */     hashMap1.put("msg", str3);
/* 78 */     return (Map)hashMap1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/DoCptIfOverAjaxCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */