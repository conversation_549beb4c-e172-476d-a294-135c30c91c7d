/*     */ package com.api.cpt.mobile.cmd.manager;
/*     */ 
/*     */ import com.api.cpt.util.CptFormItemUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetCapitalMendFormCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetCapitalMendFormCmd(Map<String, Object> paramMap, User paramUser) {
/*  29 */     this.user = paramUser;
/*  30 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  35 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  42 */     String str1 = Util.null2String(this.params.get("capitalid"));
/*  43 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  44 */     CapitalStateComInfo capitalStateComInfo = new CapitalStateComInfo();
/*  45 */     XssUtil xssUtil = new XssUtil();
/*  46 */     RecordSet recordSet = new RecordSet();
/*  47 */     recordSet.execute("select * from cptcapital where id = " + str1);
/*  48 */     String str2 = "";
/*  49 */     String str3 = "";
/*  50 */     String str4 = "";
/*  51 */     String str5 = "";
/*  52 */     String str6 = "";
/*  53 */     if (recordSet.next()) {
/*  54 */       str5 = Util.null2String(recordSet.getString("name"));
/*  55 */       str2 = Util.null2String(recordSet.getString("blongdepartment"));
/*  56 */       str3 = departmentComInfo.getDepartmentname(str2);
/*  57 */       str4 = Util.null2String(recordSet.getString("capitalspec"));
/*  58 */       str6 = capitalStateComInfo.getCapitalStatename(Util.null2String(recordSet.getString("stateid")));
/*     */     } 
/*     */     
/*  61 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  62 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  63 */     ArrayList<Map> arrayList1 = new ArrayList();
/*     */     
/*  65 */     Map<String, String> map = CptFormItemUtil.getFormItemForInput("name", SystemEnv.getHtmlLabelName(33016, Util.getIntValue(this.user.getLanguage())), str5, 200, 1);
/*  66 */     arrayList1.add(map);
/*     */     
/*  68 */     map = CptFormItemUtil.getFormItemForInput("blongdeptid", SystemEnv.getHtmlLabelName(15393, Util.getIntValue(this.user.getLanguage())), str3, 200, 1);
/*  69 */     arrayList1.add(map);
/*     */     
/*  71 */     map = CptFormItemUtil.getFormItemForInput("capitalspec", SystemEnv.getHtmlLabelName(904, Util.getIntValue(this.user.getLanguage())), str4, 200, 1);
/*  72 */     arrayList1.add(map);
/*     */     
/*  74 */     map = CptFormItemUtil.getFormItemForInput("stateidname", SystemEnv.getHtmlLabelName(602, Util.getIntValue(this.user.getLanguage())), str6, 200, 1);
/*  75 */     arrayList1.add(map);
/*     */     
/*  77 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  78 */     hashMap3.put("sqlwhere", xssUtil.put("where t1.type=2"));
/*  79 */     map = CptFormItemUtil.getFormItemForBrowser("maintaincompany", SystemEnv.getHtmlLabelName(1399, Util.getIntValue(this.user.getLanguage())), "7", "", 3, "", null, hashMap3);
/*  80 */     map.put("viewAttr", "3");
/*  81 */     arrayList1.add(map);
/*     */     
/*  83 */     map = CptFormItemUtil.getFormItemForDate("menddate", SystemEnv.getHtmlLabelName(1409, Util.getIntValue(this.user.getLanguage())), DateHelper.getCurrentDate(), 2);
/*  84 */     arrayList1.add(map);
/*     */     
/*  86 */     map = CptFormItemUtil.getFormItemForDate("mendperioddate", SystemEnv.getHtmlLabelName(22457, Util.getIntValue(this.user.getLanguage())), "", 2);
/*  87 */     arrayList1.add(map);
/*     */     
/*  89 */     map = CptFormItemUtil.getFormItemForInput("cost", SystemEnv.getHtmlLabelName(1393, Util.getIntValue(this.user.getLanguage())), "", 20, 2);
/*  90 */     arrayList1.add(map);
/*     */     
/*  92 */     map = CptFormItemUtil.getFormItemForBrowser("operator", SystemEnv.getHtmlLabelName(1047, Util.getIntValue(this.user.getLanguage())), "1", "", 2, "", null, null);
/*  93 */     map.put("viewAttr", "2");
/*  94 */     arrayList1.add(map);
/*     */     
/*  96 */     map = CptFormItemUtil.getFormItemForInput("remark", SystemEnv.getHtmlLabelName(454, Util.getIntValue(this.user.getLanguage())), "", 200, 2);
/*  97 */     arrayList1.add(map);
/*     */     
/*  99 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(22459, Util.getIntValue(this.user.getLanguage())));
/* 100 */     hashMap2.put("items", arrayList1);
/* 101 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 102 */     arrayList.add(hashMap2);
/*     */ 
/*     */     
/* 105 */     hashMap1.put("mainTableInfo", arrayList);
/* 106 */     hashMap1.put("capitalid", str1);
/*     */     
/* 108 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/GetCapitalMendFormCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */