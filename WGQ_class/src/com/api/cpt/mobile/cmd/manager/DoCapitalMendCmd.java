/*    */ package com.api.cpt.mobile.cmd.manager;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.cpt.capital.CapitalComInfo;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoCapitalMendCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoCapitalMendCmd(Map<String, Object> paramMap, User paramUser) {
/* 23 */     this.user = paramUser;
/* 24 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 29 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 35 */     String str1 = "";
/* 36 */     RecordSet recordSet = new RecordSet();
/* 37 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*    */     
/* 39 */     char c = Util.getSeparator();
/* 40 */     String str2 = "";
/*    */     
/* 42 */     String str3 = Util.null2String(this.params.get("menddate"));
/* 43 */     String str4 = Util.null2String(this.params.get("maintaincompany"));
/* 44 */     String str5 = Util.null2String(this.params.get("operator"));
/* 45 */     String str6 = Util.null2String(this.params.get("mendperioddate"));
/* 46 */     String str7 = Util.null2s(Util.null2String(this.params.get("cost")), "0");
/* 47 */     String str8 = Util.null2String(this.params.get("remark"));
/* 48 */     String str9 = Util.null2String(this.params.get("capitalid"));
/* 49 */     String str10 = "";
/*    */     
/* 51 */     String str11 = "select departmentid from CptCapital where id=" + str9;
/* 52 */     recordSet.execute(str11);
/* 53 */     recordSet.next();
/* 54 */     str10 = recordSet.getString("departmentid");
/*    */     
/* 56 */     str2 = str9;
/* 57 */     str2 = str2 + c + str3;
/* 58 */     str2 = str2 + c + "";
/* 59 */     str2 = str2 + c + "";
/* 60 */     str2 = str2 + c + "1";
/* 61 */     str2 = str2 + c + "";
/* 62 */     str2 = str2 + c + "0";
/* 63 */     str2 = str2 + c + str4;
/* 64 */     str2 = str2 + c + str7;
/* 65 */     str2 = str2 + c + "4";
/* 66 */     str2 = str2 + c + str8;
/* 67 */     str2 = str2 + c + str5;
/* 68 */     str2 = str2 + c + str6;
/* 69 */     str2 = str2 + c + str10;
/* 70 */     recordSet.executeProc("CptUseLogMend_Insert2", str2);
/* 71 */     capitalComInfo.removeCapitalCache();
/*    */     
/* 73 */     hashMap.put("msg", str1);
/* 74 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/DoCapitalMendCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */