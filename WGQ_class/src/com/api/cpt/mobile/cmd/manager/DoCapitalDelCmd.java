/*    */ package com.api.cpt.mobile.cmd.manager;
/*    */ 
/*    */ import com.api.cpt.util.CptInventoryUtil;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.cpt.capital.CapitalComInfo;
/*    */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SysMaintenanceLog;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoCapitalDelCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoCapitalDelCmd(Map<String, Object> paramMap, User paramUser) {
/* 28 */     this.user = paramUser;
/* 29 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 34 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 41 */     RecordSet recordSet = new RecordSet();
/* 42 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 43 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 44 */     String str1 = Util.null2String(this.params.get("capitalid"));
/* 45 */     String str2 = "" + str1;
/*    */ 
/*    */ 
/*    */     
/* 49 */     boolean bool = false;
/* 50 */     String str3 = "select * from cptcapital where datatype = " + str1;
/* 51 */     recordSet.execute(str3);
/* 52 */     if (recordSet.next()) {
/* 53 */       bool = true;
/*    */     }
/* 55 */     str3 = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + str1;
/* 56 */     recordSet.execute(str3);
/* 57 */     if (recordSet.next()) {
/* 58 */       bool = true;
/*    */     }
/*    */     
/* 61 */     if (bool) {
/* 62 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(83610, Util.getIntValue(this.user.getLanguage())));
/* 63 */       hashMap.put("flag", Boolean.valueOf(false));
/* 64 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 67 */     String str4 = "";
/* 68 */     str3 = "select * from cptcapital where id = '" + str1 + "'";
/* 69 */     recordSet.execute(str3);
/* 70 */     if (recordSet.next()) {
/* 71 */       str4 = recordSet.getString("name");
/*    */     }
/* 73 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*    */     
/* 75 */     recordSet.executeProc("CptCapital_ForcedDelete", str2);
/* 76 */     sysMaintenanceLog.resetParameter();
/* 77 */     sysMaintenanceLog.setRelatedId(Util.getIntValue(str1));
/* 78 */     sysMaintenanceLog.setRelatedName(str4);
/* 79 */     sysMaintenanceLog.setOperateType("3");
/* 80 */     sysMaintenanceLog.setOperateDesc("CptCapital_Delete," + str2);
/* 81 */     sysMaintenanceLog.setOperateItem("51");
/* 82 */     sysMaintenanceLog.setOperateUserid(this.user.getUID(this.user, "Capital:Maintenance"));
/* 83 */     sysMaintenanceLog.setClientAddress(Util.null2String(this.params.get("param_ip")));
/*    */     try {
/* 85 */       sysMaintenanceLog.setSysLogInfo();
/* 86 */     } catch (Exception exception) {
/* 87 */       exception.printStackTrace();
/*    */     } 
/* 89 */     capitalComInfo.deleteCapitalCache(str1);
/* 90 */     capitalAssortmentComInfo.removeCapitalAssortmentCache();
/* 91 */     recordSet.execute("delete from CptCapitalShareInfo where relateditemid=" + str1);
/*    */     
/* 93 */     CptInventoryUtil cptInventoryUtil = new CptInventoryUtil();
/* 94 */     cptInventoryUtil.resetInventory(str1);
/*    */     
/* 96 */     hashMap.put("flag", Boolean.valueOf(true));
/* 97 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/DoCapitalDelCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */