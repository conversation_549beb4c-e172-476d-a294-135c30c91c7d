/*     */ package com.api.cpt.mobile.cmd.manager;
/*     */ 
/*     */ import com.api.cpt.util.CptFormItemUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalCurPrice;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.cpt.util.CptCardGroupComInfo;
/*     */ import weaver.cpt.util.CptFieldComInfo;
/*     */ import weaver.cpt.util.CptSettingsComInfo;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.proj.util.CodeUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetCapitalFormCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   private HttpServletRequest request;
/*     */   
/*     */   public GetCapitalFormCmd(Map<String, Object> paramMap, User paramUser, HttpServletRequest paramHttpServletRequest) {
/*  48 */     this.user = paramUser;
/*  49 */     this.params = paramMap;
/*  50 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  55 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  60 */     String str1 = Util.null2String(this.params.get("capitalid"));
/*     */     
/*  62 */     RecordSet recordSet1 = new RecordSet();
/*  63 */     recordSet1.executeProc("CptCapital_SelectByID", str1);
/*  64 */     recordSet1.next();
/*     */     
/*  66 */     String str2 = Util.null2String(recordSet1.getString("name"));
/*  67 */     String str3 = Util.null2String(recordSet1.getString("isdata"));
/*  68 */     String str4 = Util.null2String(recordSet1.getString("sptcount"));
/*  69 */     String str5 = Util.null2String(recordSet1.getString("capitalgroupid"));
/*  70 */     String str6 = Util.null2String(recordSet1.getString("mark"));
/*  71 */     String str7 = Util.null2String(recordSet1.getString("barcode"));
/*     */     
/*  73 */     boolean bool1 = true;
/*  74 */     if (str3.equals("2")) {
/*  75 */       bool1 = false;
/*     */     }
/*  77 */     String str8 = "1";
/*  78 */     CodeUtil codeUtil = new CodeUtil();
/*  79 */     if ("1".equals(str3)) {
/*  80 */       str8 = codeUtil.getCptData1CodeUse();
/*     */     } else {
/*  82 */       str8 = codeUtil.getCptData2CodeUse();
/*     */     } 
/*     */     
/*  85 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  86 */     boolean bool = capitalTransMethod.IsWareHouseOpen();
/*     */     
/*  88 */     CapitalCurPrice capitalCurPrice = new CapitalCurPrice();
/*  89 */     String str9 = Util.null2String(recordSet1.getString("cptcapital", "startprice", true, true));
/*  90 */     if (str9.equals("")) {
/*  91 */       str9 = "0";
/*     */     }
/*  93 */     String str10 = Util.null2String(recordSet1.getString("cptcapital", "capitalnum", true, true));
/*  94 */     CptWfUtil cptWfUtil = new CptWfUtil();
/*  95 */     RequestComInfo requestComInfo = null;
/*     */     try {
/*  97 */       requestComInfo = new RequestComInfo();
/*  98 */     } catch (Exception exception) {
/*  99 */       exception.printStackTrace();
/*     */     } 
/* 101 */     double d = Util.getDoubleValue(recordSet1.getString("cptcapital", "frozennum", true, true), 0.0D);
/* 102 */     String str11 = "";
/* 103 */     if (d > 0.0D) {
/* 104 */       str11 = str11 + "<ul style='padding-left:15px;'>";
/* 105 */       HashMap hashMap = cptWfUtil.getCptFrozenWorkflow(str1);
/* 106 */       if (hashMap != null && hashMap.size() > 0) {
/* 107 */         for (Map.Entry entry : hashMap.entrySet()) {
/* 108 */           String str20 = (String)entry.getKey();
/* 109 */           String str21 = (String)entry.getValue();
/* 110 */           String str22 = GCONST.getContextPath() + "/workflow/request/ViewRequestForwardSPA.jsp?requestid=" + str20;
/* 111 */           str11 = str11 + "<li style='padding:5px;'><a href=\"javascript:openFullWindowHaveBar('" + str22 + "')\">" + requestComInfo.getRequestname(str20) + "</a><span style='color:red;margin-left:15px;'>" + str21 + "</span></li>";
/*     */         } 
/*     */       }
/* 114 */       str11 = str11 + "</ul>";
/*     */     } 
/* 116 */     String str12 = Util.null2String(recordSet1.getString("cptcapital", "SelectDate", true, true));
/* 117 */     String str13 = Util.null2String(recordSet1.getString("cptcapital", "depreyear", true, true));
/* 118 */     String str14 = Util.null2String(recordSet1.getString("cptcapital", "deprerate", true, true));
/* 119 */     String str15 = str9;
/* 120 */     if (!bool1) {
/*     */       
/* 122 */       capitalCurPrice.setSptcount(str4);
/* 123 */       capitalCurPrice.setStartprice(str9);
/* 124 */       capitalCurPrice.setCapitalnum(str10);
/* 125 */       capitalCurPrice.setDeprestartdate(str12);
/* 126 */       capitalCurPrice.setDepreyear(str13);
/* 127 */       capitalCurPrice.setDeprerate(str14);
/* 128 */       str15 = capitalCurPrice.getCurPrice();
/*     */     } 
/*     */     
/* 131 */     RecordSet recordSet2 = new RecordSet();
/* 132 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 133 */     recordSet2.execute("SELECT supassortmentstr FROM CptCapitalAssortment WHERE id =  " + str5);
/* 134 */     String str16 = "";
/* 135 */     if (recordSet2.next()) {
/* 136 */       str16 = recordSet2.getString("supassortmentstr");
/*     */     }
/* 138 */     String[] arrayOfString = str16.split("\\|");
/* 139 */     String str17 = ""; byte b;
/* 140 */     for (b = 1; b < arrayOfString.length; b++) {
/* 141 */       str17 = str17 + capitalAssortmentComInfo.getAssortmentName(arrayOfString[b]) + " > ";
/*     */     }
/* 143 */     str17 = str17 + capitalAssortmentComInfo.getAssortmentName(str5);
/*     */     
/* 145 */     b = 0;
/* 146 */     String str18 = ",";
/* 147 */     if (bool1) {
/* 148 */       str18 = str18 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,alertnum,warehouse,address,";
/* 149 */     } else if (!"1".equals(str4)) {
/* 150 */       str18 = str18 + "deprerate,depreyear,deprestartdate,usedyear,";
/*     */     } 
/* 152 */     if (!bool) {
/* 153 */       str18 = str18 + "warehouse,";
/*     */     }
/* 155 */     if ("1".equals(str4)) {
/* 156 */       str18 = str18 + "alertnum,";
/*     */     }
/*     */     
/* 159 */     CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 160 */     CptCardGroupComInfo cptCardGroupComInfo = new CptCardGroupComInfo();
/*     */     
/* 162 */     TreeMap treeMap = cptFieldComInfo.getGroupFieldMap();
/* 163 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 164 */     ArrayList<Map<String, String>> arrayList1 = null;
/* 165 */     HashMap<Object, Object> hashMap1 = null;
/*     */     
/* 167 */     cptCardGroupComInfo.setTofirstRow();
/* 168 */     while (cptCardGroupComInfo.next()) {
/* 169 */       String str20 = cptCardGroupComInfo.getGroupid();
/* 170 */       TreeMap treeMap1 = (TreeMap)treeMap.get(str20);
/*     */       
/* 172 */       if (treeMap1 == null || treeMap1.size() == 0) {
/*     */         continue;
/*     */       }
/* 175 */       int i = Util.getIntValue(cptCardGroupComInfo.getLabel(), -1);
/* 176 */       String str21 = SystemEnv.getHtmlLabelName(i, this.user.getLanguage());
/*     */       
/* 178 */       hashMap1 = new HashMap<>();
/* 179 */       hashMap1.put("title", str21);
/* 180 */       arrayList1 = new ArrayList();
/*     */       
/* 182 */       Iterator<Map.Entry> iterator = treeMap1.entrySet().iterator();
/* 183 */       while (iterator.hasNext()) {
/*     */ 
/*     */         
/* 186 */         boolean bool4 = true;
/* 187 */         Map.Entry entry = iterator.next();
/* 188 */         JSONObject jSONObject = (JSONObject)entry.getValue();
/* 189 */         String str22 = "";
/* 190 */         String str23 = "";
/* 191 */         Integer integer = Integer.valueOf(0);
/* 192 */         String str24 = "";
/* 193 */         String str25 = "";
/* 194 */         String str26 = "";
/* 195 */         String str27 = "";
/*     */         try {
/* 197 */           str22 = "" + jSONObject.getInt("id");
/* 198 */           str23 = "" + jSONObject.getInt("fieldlabel");
/* 199 */           integer = Integer.valueOf(jSONObject.getInt("fieldhtmltype"));
/* 200 */           str24 = "" + jSONObject.getInt("type");
/* 201 */           str25 = "" + jSONObject.getInt("type");
/* 202 */           str26 = jSONObject.getString("fieldname");
/* 203 */           str27 = jSONObject.getString("fielddbtype");
/* 204 */         } catch (JSONException jSONException) {
/* 205 */           jSONException.printStackTrace();
/*     */         } 
/* 207 */         if (str18.indexOf("," + str26 + ",") != -1) {
/*     */           continue;
/*     */         }
/*     */         
/* 211 */         if ("resourceid".equals(str26) && bool1) {
/* 212 */           str23 = "1507";
/* 213 */         } else if ("mark".equals(str26) && "0".equals(str8)) {
/*     */           continue;
/*     */         } 
/*     */         
/* 217 */         int j = CptFormItemUtil.formatInputLength(str27, integer.intValue(), str25);
/* 218 */         String str28 = SystemEnv.getHtmlLabelNames(str23, this.user.getLanguage());
/* 219 */         if ("deprerate".equalsIgnoreCase(str26)) {
/* 220 */           str28 = str28 + "(%)";
/*     */         }
/* 222 */         Map<String, String> map = null;
/* 223 */         String str29 = "";
/* 224 */         if (this.params != null) {
/* 225 */           str29 = Util.null2String(recordSet1.getString(str26));
/*     */         }
/* 227 */         if ("capitalgroupid".equals(str26)) {
/* 228 */           str29 = str17;
/* 229 */           integer = Integer.valueOf(1);
/* 230 */           str24 = "1";
/* 231 */           j = 100;
/*     */         } 
/* 233 */         if (integer.intValue() == 1) {
/* 234 */           if ("barcode".equalsIgnoreCase(str26)) {
/* 235 */             str29 = CptSettingsComInfo.getBarcodeImageStr(this.request, str7, str6, str1);
/*     */           }
/* 237 */           if ("2".equals(str24) || "3".equals(str24)) {
/*     */             
/* 239 */             int k = 0;
/* 240 */             if ("3".equals(str24)) {
/*     */               
/* 242 */               String str = str27.substring(str27.indexOf(",") + 1, str27.length() - 1);
/* 243 */               k = Integer.parseInt(str);
/*     */             } 
/* 245 */             map = CptFormItemUtil.getFormItemForInputNumber(str26, str28, str29, bool4, "", "", k, "1");
/*     */           } else {
/*     */             
/* 248 */             map = CptFormItemUtil.getFormItemForInput(str26, str28, str29, j, bool4);
/*     */           }
/*     */         
/* 251 */         } else if (integer.intValue() == 2) {
/* 252 */           if ("1".equals(str24)) {
/*     */             
/* 254 */             map = CptFormItemUtil.getFormItemForTextArea(str26, str28, str29, j, bool4);
/* 255 */           } else if ("2".equals(str24)) {
/*     */             
/* 257 */             map = CptFormItemUtil.getFormItemForTextAreaHtml(str26, str28, str29, j, bool4);
/*     */           } 
/* 259 */         } else if (integer.intValue() == 3) {
/* 260 */           if (str25.equals("2") || str25.equals("19") || str25.equals("402") || str25.equals("403")) {
/* 261 */             if (str25.equals("2")) {
/* 262 */               map = CptFormItemUtil.getFormItemForDate(str26, str28, str29, bool4);
/* 263 */             } else if (str25.equals("19")) {
/* 264 */               map = CptFormItemUtil.getFormItemForTime(str26, str28, str29, bool4);
/* 265 */             } else if (str25.equals("402")) {
/* 266 */               map = CptFormItemUtil.getFormItemForYear(str26, str28, str29, bool4, "1");
/* 267 */             } else if (str25.equals("403")) {
/* 268 */               map = CptFormItemUtil.getFormItemForYearAndMonth(str26, str28, str29, bool4, "1");
/*     */             } 
/*     */           } else {
/*     */             
/* 272 */             map = CptFormItemUtil.getFormItemForBrowser(str26, str28, str25, str29, bool4, str27, null, null);
/*     */           } 
/* 274 */           map.put("viewAttr", "" + bool4);
/* 275 */         } else if (integer.intValue() == 4) {
/*     */ 
/*     */ 
/*     */           
/* 279 */           String str = SystemEnv.getHtmlLabelName(161, this.user.getLanguage());
/* 280 */           if ("1".equals(str29)) {
/* 281 */             str = SystemEnv.getHtmlLabelName(163, this.user.getLanguage());
/*     */           }
/* 283 */           map = CptFormItemUtil.getFormItemForInput(str26, str28, str, 10, bool4);
/* 284 */         } else if (integer.intValue() == 5) {
/*     */           
/* 286 */           if ("attribute".equals(str26) && 
/* 287 */             "9".equals(str29)) {
/* 288 */             str29 = "";
/*     */           }
/*     */ 
/*     */           
/* 292 */           map = CptFormItemUtil.getFormItemForSelect(str26, str28, str22, str29, bool4, false, false, this.user.getLanguage());
/* 293 */         } else if (integer.intValue() == 6) {
/*     */           
/* 295 */           map = CptFormItemUtil.getFormItemForAttachment(str26, str28, str29, bool4);
/*     */         } 
/* 297 */         arrayList1.add(map);
/*     */ 
/*     */         
/* 300 */         if (!bool1 && "startprice".equalsIgnoreCase(str26)) {
/* 301 */           map = CptFormItemUtil.getFormItemForInputNumber("currentprice", SystemEnv.getHtmlLabelName(1389, this.user.getLanguage()), str15, 1, "", "", 2, "1");
/* 302 */           arrayList1.add(map);
/*     */         } 
/*     */         
/* 305 */         if (!bool1 && "capitalnum".equalsIgnoreCase(str26) && d > 0.0D) {
/* 306 */           map = CptFormItemUtil.getFormItemForInputNumber("frozennum", SystemEnv.getHtmlLabelName(503013, this.user.getLanguage()), "" + d, 1, "", "", 2, "1");
/* 307 */           map.put("frozennumtip", str11);
/* 308 */           arrayList1.add(map);
/*     */         } 
/*     */       } 
/*     */       
/* 312 */       b++;
/* 313 */       if (b == 1) {
/*     */         
/* 315 */         String str = Util.null2String(recordSet1.getString("capitalimageid"));
/* 316 */         Map<String, String> map = CptFormItemUtil.getFormItemForUpload("capitalimageid", SystemEnv.getHtmlLabelName(74, this.user.getLanguage()), str, 1);
/* 317 */         Map map1 = CptFormItemUtil.getImgDatasList(str, "", "1");
/* 318 */         map.putAll(map1);
/* 319 */         arrayList1.add(map);
/*     */       } 
/*     */       
/* 322 */       hashMap1.put("items", arrayList1);
/* 323 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 324 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 327 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */     
/* 330 */     CommonShareManager commonShareManager = new CommonShareManager();
/* 331 */     RecordSet recordSet3 = new RecordSet();
/* 332 */     boolean bool2 = false;
/* 333 */     boolean bool3 = false;
/*     */ 
/*     */     
/* 336 */     if (HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", this.user)) {
/* 337 */       bool3 = true;
/*     */     }
/*     */     
/* 340 */     recordSet3.execute(commonShareManager.getSharLevel("cpt", str1, this.user));
/* 341 */     if (recordSet3.next()) {
/* 342 */       int i = Util.getIntValue(recordSet3.getString(1), 0);
/* 343 */       if (i == 2) {
/* 344 */         bool2 = true;
/*     */       }
/*     */     } 
/* 347 */     if (HrmUserVarify.checkUserRight("CptCapital:modify", this.user) || HrmUserVarify.checkUserRight("CptCapitalEdit:Edit", this.user)) {
/* 348 */       bool2 = true;
/*     */     }
/*     */     
/* 351 */     String str19 = Util.null2String(recordSet1.getString("stateid"));
/* 352 */     hashMap2.put("opt_edit", "false");
/* 353 */     hashMap2.put("opt_delete", "false");
/* 354 */     hashMap2.put("opt_back", "false");
/* 355 */     hashMap2.put("opt_mend", "false");
/*     */     
/* 357 */     if (bool2) {
/* 358 */       hashMap2.put("opt_edit", "true");
/*     */     }
/* 360 */     if (bool3) {
/* 361 */       hashMap2.put("opt_delete", "true");
/*     */     }
/* 363 */     if (!"1".equals(str3) && str4.equals("1") && HrmUserVarify.checkUserRight("CptCapital:Return", this.user) && (str19.equals("2") || str19.equals("3") || str19.equals("4"))) {
/* 364 */       hashMap2.put("opt_back", "true");
/*     */     }
/* 366 */     if (!"1".equals(str3) && str4.equals("1") && HrmUserVarify.checkUserRight("CptCapital:Mend", this.user) && !str19.equals("4") && !str19.equals("5") && !str19.equals("-7")) {
/* 367 */       hashMap2.put("opt_mend", "true");
/*     */     }
/* 369 */     hashMap2.put("title", str2);
/* 370 */     hashMap2.put("fieldinfo", arrayList);
/* 371 */     hashMap2.put("checkstr", "");
/* 372 */     hashMap2.put("isdata", str3);
/*     */ 
/*     */     
/* 375 */     if (CapitalTransMethod.isRecordPosition()) {
/* 376 */       recordSet1.executeQuery("select address,longitude,latitude from CptCapital where id=?", new Object[] { str1 });
/* 377 */       if (recordSet1.next()) {
/* 378 */         ArrayList<Double> arrayList2 = new ArrayList();
/* 379 */         arrayList2.add(Double.valueOf(recordSet1.getDouble("longitude")));
/* 380 */         arrayList2.add(Double.valueOf(recordSet1.getDouble("latitude")));
/* 381 */         hashMap2.put("position", arrayList2);
/* 382 */         hashMap2.put("address", Util.null2String(recordSet1.getString("address")));
/*     */       } 
/*     */     } 
/*     */     
/* 386 */     return (Map)hashMap2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/GetCapitalFormCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */