/*     */ package com.api.cpt.mobile.cmd.manager;
/*     */ 
/*     */ import com.api.cpt.mobile.util.ManagerFormItemUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ScanQRCodeCapitalCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public ScanQRCodeCapitalCmd(Map<String, Object> paramMap, User paramUser) {
/*  28 */     this.user = paramUser;
/*  29 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  34 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  40 */     RecordSet recordSet1 = new RecordSet();
/*  41 */     String str1 = Util.null2String(Util.null2String(this.params.get("scantype")), "view");
/*  42 */     String str2 = Util.null2String(this.params.get("mark"));
/*  43 */     if ("".equals(str2)) {
/*     */       
/*  45 */       hashMap.put("flag", Boolean.valueOf(false));
/*  46 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(503009, this.user.getLanguage()));
/*  47 */       return (Map)hashMap;
/*     */     } 
/*  49 */     String str3 = " where isdata=2";
/*  50 */     RecordSet recordSet2 = new RecordSet();
/*  51 */     recordSet1.execute("select * from cpt_barcodesettings");
/*  52 */     recordSet1.next();
/*  53 */     recordSet1.execute("select * from cpt_barcodesettings");
/*  54 */     recordSet1.next();
/*  55 */     String str4 = Util.null2String(recordSet1.getString("userfilter"));
/*  56 */     String str5 = Util.null2String(recordSet1.getString("useHrm"));
/*  57 */     String str6 = Util.null2String(recordSet1.getString("useHrms"));
/*  58 */     boolean bool1 = "0".equals(str4);
/*  59 */     boolean bool2 = "1".equals(str5);
/*  60 */     String str7 = Util.null2String(str6);
/*  61 */     ArrayList arrayList = Util.TokenizerString(str7, ",");
/*     */     
/*  63 */     recordSet2.execute("select id,name from CptCapital where mark='" + str2 + "' and isdata=2");
/*  64 */     if (recordSet2.next()) {
/*  65 */       String str8 = Util.null2String(recordSet2.getString("id"));
/*  66 */       String str9 = Util.null2String(recordSet2.getString("name"));
/*  67 */       String str10 = "";
/*  68 */       if (!"view".equals(str1) && !"inventory".equals(str1)) {
/*  69 */         String str11 = "";
/*  70 */         String str12 = "";
/*  71 */         String str13 = "";
/*  72 */         if ("cptuse".equals(str1)) {
/*  73 */           str11 = "1";
/*  74 */           str13 = "0";
/*  75 */           str10 = SystemEnv.getHtmlLabelName(1383, Util.getIntValue(this.user.getLanguage()));
/*  76 */         } else if ("cptmove".equals(str1)) {
/*  77 */           str13 = "0";
/*  78 */           str10 = SystemEnv.getHtmlLabelName(1437, Util.getIntValue(this.user.getLanguage()));
/*  79 */         } else if ("cptlend".equals(str1)) {
/*  80 */           str12 = "1";
/*  81 */           str11 = "1";
/*  82 */           str13 = "0";
/*  83 */           str10 = SystemEnv.getHtmlLabelName(1379, Util.getIntValue(this.user.getLanguage()));
/*  84 */         } else if ("cptloss".equals(str1)) {
/*  85 */           str11 = "1,2,3,4";
/*  86 */           str13 = "0";
/*  87 */           str10 = SystemEnv.getHtmlLabelName(1385, Util.getIntValue(this.user.getLanguage()));
/*  88 */         } else if ("cptdiscard".equals(str1)) {
/*  89 */           str11 = "1,2,3,4";
/*  90 */           str13 = "0";
/*  91 */           str10 = SystemEnv.getHtmlLabelName(1386, Util.getIntValue(this.user.getLanguage()));
/*  92 */         } else if ("cptmend".equals(str1)) {
/*  93 */           str12 = "1";
/*  94 */           str11 = "1,2,3";
/*  95 */           str13 = "0";
/*  96 */           str10 = SystemEnv.getHtmlLabelName(83557, Util.getIntValue(this.user.getLanguage()));
/*  97 */         } else if ("cptback".equals(str1)) {
/*  98 */           str11 = "4,2,3";
/*  99 */           str13 = "0";
/* 100 */           str10 = SystemEnv.getHtmlLabelName(1384, Util.getIntValue(this.user.getLanguage()));
/* 101 */         } else if ("cptchange".equals(str1)) {
/* 102 */           str10 = SystemEnv.getHtmlLabelName(19899, Util.getIntValue(this.user.getLanguage()));
/*     */         } 
/*     */         
/* 105 */         if ("cptmove".equalsIgnoreCase(str1) || "cptback".equalsIgnoreCase(str1) || "cptdiscard".equalsIgnoreCase(str1) || "cptmend".equalsIgnoreCase(str1) || "cptloss".equalsIgnoreCase(str1)) {
/* 106 */           str3 = str3 + ManagerFormItemUtil.getResourceSql(this.user, str3, bool1, bool2, arrayList, str1);
/*     */         }
/*     */         
/* 109 */         recordSet1.execute("select cptdetachable from SystemSet");
/* 110 */         int i = 0;
/* 111 */         if (recordSet1.next()) {
/* 112 */           i = recordSet1.getInt("cptdetachable");
/*     */         }
/*     */         
/* 115 */         int j = this.user.getUserSubCompany1();
/* 116 */         int k = this.user.getUID();
/* 117 */         char c = Util.getSeparator();
/*     */         
/* 119 */         String str14 = str3;
/* 120 */         String str15 = "";
/* 121 */         if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/* 122 */           str15 = "Capital:Maintenance";
/*     */         }
/* 124 */         String str16 = "";
/* 125 */         if (!str11.equals("")) {
/* 126 */           str14 = str14 + " and stateid in (";
/* 127 */           str14 = str14 + str11;
/* 128 */           str14 = str14 + ") ";
/*     */         } 
/* 130 */         if (!str12.equals("")) {
/* 131 */           str14 = str14 + " and sptcount = '";
/* 132 */           str14 = str14 + str12;
/* 133 */           str14 = str14 + "'";
/*     */         } 
/*     */ 
/*     */         
/* 137 */         if (i == 1 && k != 1) {
/* 138 */           String str = "";
/* 139 */           recordSet1.executeProc("HrmRoleSR_SeByURId", "" + k + c + str15);
/* 140 */           while (recordSet1.next()) {
/* 141 */             str16 = recordSet1.getString("subcompanyid");
/* 142 */             str = str + ", " + str16;
/*     */           } 
/* 144 */           if (!"".equals(str)) {
/* 145 */             str = str.substring(1);
/* 146 */             str14 = str14 + " and blongsubcompany in (" + str + ") ";
/*     */           } else {
/* 148 */             str14 = str14 + " and blongsubcompany in (" + j + ") ";
/*     */           } 
/*     */         } 
/* 151 */         if (!this.user.getLoginid().equalsIgnoreCase("sysadmin")) {
/* 152 */           CommonShareManager commonShareManager = new CommonShareManager();
/* 153 */           commonShareManager.setAliasTableName("t2");
/* 154 */           str14 = str14 + commonShareManager.getAssortmentSqlWhere(this.user);
/*     */         } 
/*     */         
/* 157 */         String str17 = " t1.id,t1.name ";
/* 158 */         String str18 = "";
/* 159 */         if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 160 */           str18 = ",(nvl(capitalnum,0)-nvl(frozennum,0)) cptnum";
/* 161 */         } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 162 */           str18 = ",(ifnull(capitalnum,0)-ifnull(frozennum,0)) cptnum";
/*     */         } else {
/* 164 */           str18 = ",(isnull(capitalnum,0)-isnull(frozennum,0)) cptnum";
/*     */         } 
/* 166 */         str17 = str17 + str18;
/*     */         
/* 168 */         String str19 = "";
/* 169 */         if (!"1".equals(str13)) {
/* 170 */           str19 = " and  ";
/* 171 */           if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 172 */             str19 = str19 + "  (nvl(capitalnum,0)-nvl(frozennum,0))>0 ";
/* 173 */           } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 174 */             str19 = str19 + "  (ifnull(capitalnum,0)-ifnull(frozennum,0))>0 ";
/*     */           } else {
/* 176 */             str19 = str19 + "  (isnull(capitalnum,0)-isnull(frozennum,0))>0 ";
/*     */           } 
/*     */         } 
/* 179 */         String str20 = str14 + str19 + " and id='" + str8 + "'";
/* 180 */         recordSet1.execute("select " + str17 + " from CptCapital t1 " + str20);
/* 181 */         if (recordSet1.next()) {
/* 182 */           Map map = ManagerFormItemUtil.getCptLinkageDataById(str8);
/* 183 */           Map<?, ?> map1 = ManagerFormItemUtil.getCptManagerForm(str1, this.user, map);
/* 184 */           hashMap.putAll(map1);
/* 185 */           hashMap.put("name", str9);
/* 186 */           hashMap.put("flag", Boolean.valueOf(true));
/*     */         } else {
/*     */           
/* 189 */           hashMap.put("flag", Boolean.valueOf(false));
/* 190 */           hashMap.put("msg", str9 + " " + SystemEnv.getHtmlLabelName(390012, Util.getIntValue(this.user.getLanguage())) + str10);
/*     */         }
/*     */       
/* 193 */       } else if ("view".equals(str1)) {
/* 194 */         recordSet1.execute("select cptdetachable from SystemSet");
/* 195 */         int i = 0;
/* 196 */         if (recordSet1.next()) {
/* 197 */           i = recordSet1.getInt("cptdetachable");
/*     */         }
/*     */         
/* 200 */         int j = this.user.getUserSubCompany1();
/* 201 */         int k = this.user.getUID();
/* 202 */         char c = Util.getSeparator();
/*     */         
/* 204 */         String str11 = " where isdata=2 ";
/* 205 */         String str12 = "";
/* 206 */         if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/* 207 */           str12 = "Capital:Maintenance";
/*     */         }
/* 209 */         String str13 = "";
/*     */         
/* 211 */         if (i == 1 && k != 1) {
/* 212 */           String str = "";
/* 213 */           recordSet1.executeProc("HrmRoleSR_SeByURId", "" + k + c + str12);
/* 214 */           while (recordSet1.next()) {
/* 215 */             str13 = recordSet1.getString("subcompanyid");
/* 216 */             str = str + ", " + str13;
/*     */           } 
/* 218 */           if (!"".equals(str)) {
/* 219 */             str = str.substring(1);
/* 220 */             str11 = str11 + " and blongsubcompany in (" + str + ") ";
/*     */           } else {
/* 222 */             str11 = str11 + " and blongsubcompany in (" + j + ") ";
/*     */           } 
/*     */         } 
/* 225 */         if (!this.user.getLoginid().equalsIgnoreCase("sysadmin")) {
/* 226 */           CommonShareManager commonShareManager = new CommonShareManager();
/* 227 */           commonShareManager.setAliasTableName("t2");
/* 228 */           str11 = str11 + commonShareManager.getAssortmentSqlWhere(this.user);
/*     */         } 
/*     */         
/* 231 */         String str14 = " t1.id,t1.name ";
/* 232 */         String str15 = "";
/* 233 */         if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 234 */           str15 = ",(nvl(capitalnum,0)-nvl(frozennum,0)) cptnum";
/* 235 */         } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 236 */           str15 = ",(ifnull(capitalnum,0)-ifnull(frozennum,0)) cptnum";
/*     */         } else {
/* 238 */           str15 = ",(isnull(capitalnum,0)-isnull(frozennum,0)) cptnum";
/*     */         } 
/* 240 */         str14 = str14 + str15;
/* 241 */         String str16 = str11 + " and id='" + str8 + "'";
/* 242 */         recordSet1.execute("select " + str14 + " from CptCapital t1 " + str16);
/* 243 */         if (recordSet1.next()) {
/* 244 */           hashMap.put("capitalid", str8);
/* 245 */           hashMap.put("name", str9);
/* 246 */           hashMap.put("flag", Boolean.valueOf(true));
/*     */         } else {
/*     */           
/* 249 */           hashMap.put("flag", Boolean.valueOf(false));
/* 250 */           hashMap.put("msg", str9 + " " + SystemEnv.getHtmlLabelName(17545, Util.getIntValue(this.user.getLanguage())));
/*     */         } 
/* 252 */       } else if ("inventory".equals(str1)) {
/*     */ 
/*     */         
/* 255 */         String str = "select a.id from cpt_inventory_detail a LEFT JOIN cpt_inventory_planlist b ON a.mainid=b.id WHERE b.planliststate in (1,2,3) AND a.cptid=" + str8 + " and b.countuser=" + this.user.getUID();
/* 256 */         recordSet1.execute(str);
/* 257 */         if (recordSet1.next()) {
/* 258 */           hashMap.put("capitalid", str8);
/* 259 */           hashMap.put("name", str9);
/* 260 */           hashMap.put("flag", Boolean.valueOf(true));
/*     */         } else {
/*     */           
/* 263 */           hashMap.put("flag", Boolean.valueOf(false));
/* 264 */           hashMap.put("msg", str9 + SystemEnv.getHtmlLabelName(390193, Util.getIntValue(this.user.getLanguage())));
/*     */         }
/*     */       
/*     */       } 
/*     */     } else {
/*     */       
/* 270 */       hashMap.put("flag", Boolean.valueOf(false));
/* 271 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(503009, this.user.getLanguage()));
/*     */     } 
/*     */     
/* 274 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/ScanQRCodeCapitalCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */