/*     */ package com.api.cpt.mobile.cmd.manager;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.math.BigDecimal;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CptDwrUtil;
/*     */ import weaver.cpt.util.CptUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoCptManagerCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DoCptManagerCmd(Map<String, Object> paramMap, User paramUser) {
/*  35 */     this.user = paramUser;
/*  36 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  41 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  48 */     String str1 = Util.null2String(this.params.get("mtype"));
/*  49 */     RecordSet recordSet = new RecordSet();
/*  50 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  51 */     CptShare cptShare = new CptShare();
/*  52 */     CptDwrUtil cptDwrUtil = new CptDwrUtil();
/*  53 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/*  55 */       resourceComInfo = new ResourceComInfo();
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*     */     } 
/*  59 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  60 */     boolean bool1 = capitalTransMethod.IsWareHouseOpen();
/*  61 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  62 */     boolean bool2 = recordSet.getDBType().equals("oracle");
/*  63 */     char c = Util.getSeparator();
/*     */     
/*  65 */     String str2 = "";
/*  66 */     if ("cptuse".equals(str1)) {
/*  67 */       String str3 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("capitalnum")), 0.0D));
/*  68 */       String str4 = Util.null2String(this.params.get("location"));
/*  69 */       String str5 = Util.null2String(this.params.get("remark"));
/*  70 */       String str6 = Util.null2String(this.params.get("stockindate"));
/*  71 */       String str7 = Util.null2String(this.params.get("capitalid"));
/*  72 */       String str8 = Util.null2String(this.params.get("hrmid"));
/*  73 */       String str9 = resourceComInfo.getDepartmentID(str8);
/*  74 */       String str10 = (String)cptDwrUtil.getCptInfoMap(str7).get("sptcount");
/*     */       
/*  76 */       String str11 = "";
/*  77 */       if (!str7.equals("")) {
/*  78 */         if (str10.equals("1")) {
/*  79 */           str11 = str7;
/*  80 */           str11 = str11 + c + str6;
/*  81 */           str11 = str11 + c + str9;
/*  82 */           str11 = str11 + c + str8;
/*  83 */           str11 = str11 + c + "1";
/*  84 */           str11 = str11 + c + "";
/*  85 */           str11 = str11 + c + "0";
/*  86 */           str11 = str11 + c + "2";
/*  87 */           str11 = str11 + c + str5;
/*  88 */           str11 = str11 + c + str4;
/*  89 */           str11 = str11 + c + str10;
/*  90 */           recordSet.executeProc("CptUseLogUse_Insert", str11);
/*     */         } else {
/*  92 */           str11 = str7;
/*  93 */           str11 = str11 + c + str6;
/*  94 */           str11 = str11 + c + str9;
/*  95 */           str11 = str11 + c + str8;
/*  96 */           str11 = str11 + c + str3;
/*  97 */           str11 = str11 + c + "";
/*  98 */           str11 = str11 + c + "0";
/*  99 */           str11 = str11 + c + "2";
/* 100 */           str11 = str11 + c + str5;
/* 101 */           str11 = str11 + c + str4;
/* 102 */           str11 = str11 + c + "0";
/*     */           
/* 104 */           recordSet.executeProc("CptUseLogUse_Insert", str11);
/* 105 */           recordSet.next();
/* 106 */           String str = recordSet.getString(1);
/*     */           
/* 108 */           if (str.equals("-1")) {
/* 109 */             str2 = capitalComInfo.getCapitalname(str7) + " " + SystemEnv.getHtmlLabelName(84464, this.user.getLanguage());
/*     */           }
/*     */         } 
/*     */         
/* 113 */         recordSet.executeProc("HrmInfoStatus_UpdateCapital", "" + str8);
/* 114 */         capitalComInfo.removeCapitalCache();
/*     */         try {
/* 116 */           cptShare.setCptShareByCpt(str7);
/* 117 */         } catch (Exception exception) {
/* 118 */           exception.printStackTrace();
/*     */         } 
/*     */         
/* 121 */         if (!str4.equals("")) {
/* 122 */           recordSet.execute("update CptCapital set location='" + str4 + "' where id=" + str7);
/*     */         }
/*     */ 
/*     */         
/* 126 */         if ("1".equals(str10)) {
/* 127 */           String str = "";
/* 128 */           if (!bool2) {
/* 129 */             str = "update CptCapital set deprestartdate='" + str6 + "' where id=" + str7 + " and (deprestartdate is null or deprestartdate='')";
/*     */           } else {
/* 131 */             str = "update CptCapital set deprestartdate='" + str6 + "' where id=" + str7 + " and deprestartdate is null";
/*     */           } 
/* 133 */           recordSet.execute(str);
/*     */         } 
/*     */       } 
/* 136 */     } else if ("cptmove".equals(str1)) {
/*     */ 
/*     */       
/* 139 */       String str3 = Util.null2String(this.params.get("capitalid"));
/* 140 */       String str4 = Util.null2String(this.params.get("hrmid"));
/* 141 */       String str5 = Util.null2String(this.params.get("location"));
/* 142 */       String str6 = Util.null2String(this.params.get("remark"));
/* 143 */       String str7 = resourceComInfo.getDepartmentID(str4);
/* 144 */       String str8 = capitalComInfo.getDepartmentid(str3);
/* 145 */       String str9 = "1";
/* 146 */       String str10 = "-4";
/* 147 */       String str11 = DateHelper.getCurrentDate();
/* 148 */       String str12 = Util.null2String(this.params.get("availablenum"));
/* 149 */       String str13 = Util.null2String(this.params.get("warehouse"));
/* 150 */       String str14 = capitalComInfo.getSptCount(str3);
/* 151 */       String str15 = "";
/* 152 */       String str16 = "";
/* 153 */       String str17 = "";
/* 154 */       String str18 = "";
/* 155 */       recordSet.executeQuery("select datatype,warehouse,blongdepartment,startprice from CptCapital where id=?", new Object[] { str3 });
/* 156 */       if (recordSet.next()) {
/* 157 */         str16 = Util.null2String(recordSet.getString("datatype"));
/* 158 */         str15 = Util.null2String(recordSet.getString("warehouse"));
/* 159 */         str17 = Util.null2String(recordSet.getString("blongdepartment"));
/* 160 */         str18 = Util.null2String(recordSet.getString("CptCapital", "startprice", true, true));
/*     */       } 
/*     */       
/* 163 */       if (str14.equalsIgnoreCase("0")) {
/* 164 */         String str = "";
/* 165 */         if (bool1 && !str13.equalsIgnoreCase("") && !str13.equals(str15)) {
/* 166 */           recordSet.executeQuery("select id from CptCapital where datatype=? and blongdepartment=? and warehouse=?", new Object[] { str16, str17, str13 });
/* 167 */           if (recordSet.next()) {
/* 168 */             str = recordSet.getString("id");
/*     */             
/* 170 */             recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum+" + str12 + ",startprice=(capitalnum*startprice+" + (new BigDecimal(str12))
/* 171 */                 .setScale(2, 4).multiply((new BigDecimal(str18)).setScale(2, 4)) + ")/(capitalnum+" + str12 + ") where id = " + recordSet
/* 172 */                 .getString("id"), new Object[0]);
/* 173 */             recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum-" + str12 + " where id=?", new Object[] { str3 });
/*     */           } else {
/* 175 */             str = capitalTransMethod.copyNewCpt(str3, str12, str13);
/* 176 */             if (!str.equalsIgnoreCase("")) {
/* 177 */               recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum-" + str12 + " where id=?", new Object[] { str3 });
/*     */             }
/*     */           } 
/* 180 */           recordSet.executeUpdate("insert INTO CptUseLog (capitalid, usedate, usedeptid, useresourceid, usecount, useaddress, usestatus, remark, olddeptid, warehouseid) values (?,?,?,?,?,?,?,?,?,?)", new Object[] { str3, str11, Integer.valueOf(Util.getIntValue(str7, 0)), Integer.valueOf(Util.getIntValue(str4, 0)), str12, str5, Integer.valueOf(-5), str6, Integer.valueOf(Util.getIntValue(str8, 0)), str13 });
/* 181 */           recordSet.executeUpdate("insert INTO CptUseLog (capitalid, usedate, usedeptid, useresourceid, usecount, useaddress, usestatus, remark, olddeptid, warehouseid) values (?,?,?,?,?,?,?,?,?,?)", new Object[] { str, str11, Integer.valueOf(Util.getIntValue(str7, 0)), Integer.valueOf(Util.getIntValue(str4, 0)), str12, str5, str10, str6, Integer.valueOf(Util.getIntValue(str8, 0)), str13 });
/*     */         } 
/* 183 */       } else if (str14.equalsIgnoreCase("1")) {
/* 184 */         String str = "";
/* 185 */         str = str3;
/* 186 */         str = str + c + str11;
/* 187 */         str = str + c + str7;
/* 188 */         str = str + c + str4;
/* 189 */         str = str + c + str9;
/* 190 */         str = str + c + str5;
/* 191 */         str = str + c + str10;
/* 192 */         str = str + c + str6;
/* 193 */         str = str + c + str8;
/*     */         
/* 195 */         recordSet.executeProc("Capital_Adjust", str);
/* 196 */         recordSet.execute("update cptcapital set location='" + str5 + "' where id='" + str3 + "' ");
/* 197 */         if (!str13.equalsIgnoreCase("")) {
/* 198 */           recordSet.executeUpdate("update cptcapital set warehouse=? where id=?", new Object[] { str13, str3 });
/* 199 */           recordSet.executeQuery("select max(id) as id from CptUseLog", new Object[0]);
/* 200 */           if (recordSet.next()) {
/* 201 */             String str19 = Util.null2String(recordSet.getString("id"));
/* 202 */             recordSet.executeUpdate("update CptUseLog set warehouseid=? where id=?", new Object[] { str13, str19 });
/*     */           } 
/*     */         } 
/* 205 */         capitalComInfo.removeCapitalCache();
/*     */         
/* 207 */         CptRightShareUitl.editCapitalResetRight(str3);
/*     */         try {
/* 209 */           cptShare.setCptShareByCpt(str3);
/* 210 */           if (bool1) {
/* 211 */             cptShare.freshenCptShareByWareHouse(str3);
/*     */           }
/* 213 */         } catch (Exception exception) {
/* 214 */           exception.printStackTrace();
/*     */         } 
/*     */       } 
/* 217 */     } else if ("cptlend".equals(str1)) {
/* 218 */       String str3 = Util.null2String(this.params.get("location"));
/* 219 */       String str4 = Util.null2String(this.params.get("remark"));
/* 220 */       String str5 = Util.null2String(this.params.get("hrmid"));
/* 221 */       String str6 = Util.null2String(this.params.get("stockindate"));
/* 222 */       String str7 = Util.null2String(this.params.get("capitalid"));
/* 223 */       String str8 = resourceComInfo.getDepartmentID(str5);
/*     */       
/* 225 */       String str9 = "";
/* 226 */       str9 = str7;
/* 227 */       str9 = str9 + c + str6;
/* 228 */       str9 = str9 + c + str8;
/* 229 */       str9 = str9 + c + str5;
/* 230 */       str9 = str9 + c + "1";
/* 231 */       str9 = str9 + c + str3;
/* 232 */       str9 = str9 + c + "0";
/* 233 */       str9 = str9 + c + "";
/* 234 */       str9 = str9 + c + "0";
/* 235 */       str9 = str9 + c + "3";
/* 236 */       str9 = str9 + c + str4;
/* 237 */       str9 = str9 + c + "0";
/*     */       
/* 239 */       recordSet.executeProc("CptUseLogLend_Insert", str9);
/* 240 */       capitalComInfo.removeCapitalCache();
/*     */       try {
/* 242 */         cptShare.setCptShareByCpt(str7);
/* 243 */       } catch (Exception exception) {
/* 244 */         exception.printStackTrace();
/*     */       }
/*     */     
/* 247 */     } else if ("cptloss".equals(str1)) {
/* 248 */       String str3 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("capitalcount")), 0.0D));
/* 249 */       String str4 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("cost")), 0.0D));
/* 250 */       String str5 = Util.null2String(this.params.get("remark"));
/* 251 */       String str6 = Util.null2String(this.params.get("operator"));
/* 252 */       String str7 = Util.null2String(this.params.get("stockindate"));
/* 253 */       String str8 = Util.null2String(this.params.get("capitalid"));
/* 254 */       String str9 = resourceComInfo.getDepartmentID(str6);
/* 255 */       String str10 = Util.null2String((String)cptDwrUtil.getCptInfoMap(str8).get("sptcount"));
/*     */ 
/*     */       
/* 258 */       String str11 = "select departmentid from CptCapital where id=" + str8;
/* 259 */       recordSet.execute(str11);
/* 260 */       recordSet.next();
/* 261 */       String str12 = recordSet.getString("departmentid");
/*     */       
/* 263 */       String str13 = "";
/* 264 */       if (str10.equals("1")) {
/* 265 */         str13 = str8;
/* 266 */         str13 = str13 + c + str7;
/* 267 */         str13 = str13 + c + str9;
/* 268 */         str13 = str13 + c + str6;
/* 269 */         str13 = str13 + c + "1";
/* 270 */         str13 = str13 + c + "";
/* 271 */         str13 = str13 + c + "0";
/* 272 */         str13 = str13 + c + "";
/* 273 */         str13 = str13 + c + str4;
/* 274 */         str13 = str13 + c + "-7";
/* 275 */         str13 = str13 + c + str5;
/* 276 */         str13 = str13 + c + "0";
/* 277 */         str13 = str13 + c + str10;
/* 278 */         str13 = str13 + c + str12;
/*     */         
/* 280 */         recordSet.executeProc("CptUseLogLoss_Insert2", str13);
/*     */       } else {
/* 282 */         str13 = str8;
/* 283 */         str13 = str13 + c + str7;
/* 284 */         str13 = str13 + c + str9;
/* 285 */         str13 = str13 + c + str6;
/* 286 */         str13 = str13 + c + str3;
/* 287 */         str13 = str13 + c + "";
/* 288 */         str13 = str13 + c + "0";
/* 289 */         str13 = str13 + c + "";
/* 290 */         str13 = str13 + c + str4;
/* 291 */         str13 = str13 + c + "-7";
/* 292 */         str13 = str13 + c + str5;
/* 293 */         str13 = str13 + c + "0";
/* 294 */         str13 = str13 + c + str10;
/* 295 */         str13 = str13 + c + str12;
/*     */         
/* 297 */         recordSet.executeProc("CptUseLogLoss_Insert2", str13);
/* 298 */         recordSet.next();
/* 299 */         String str = recordSet.getString(1);
/*     */         
/* 301 */         if (str.equals("-1")) {
/* 302 */           str2 = SystemEnv.getHtmlLabelName(503029, this.user.getLanguage());
/*     */         }
/*     */       } 
/* 305 */     } else if ("cptdiscard".equals(str1)) {
/* 306 */       String str3 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("capitalcount")), 0.0D));
/* 307 */       String str4 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("cost")), 0.0D));
/* 308 */       String str5 = Util.null2String(this.params.get("remark"));
/* 309 */       String str6 = Util.null2String(this.params.get("operator"));
/* 310 */       String str7 = Util.null2String(this.params.get("stockindate"));
/* 311 */       String str8 = Util.null2String(this.params.get("capitalid"));
/*     */       
/* 313 */       String str9 = "select departmentid from CptCapital where id=" + str8;
/* 314 */       recordSet.execute(str9);
/* 315 */       recordSet.next();
/* 316 */       String str10 = recordSet.getString("departmentid");
/*     */       
/* 318 */       String str11 = Util.null2String((String)cptDwrUtil.getCptInfoMap(str8).get("sptcount"));
/* 319 */       if ("".equals(str11)) {
/* 320 */         str11 = "0";
/*     */       }
/* 322 */       String str12 = "";
/* 323 */       if (str11.equals("1")) {
/* 324 */         str12 = str8;
/* 325 */         str12 = str12 + c + str7;
/* 326 */         str12 = str12 + c + "0";
/* 327 */         str12 = str12 + c + str6;
/* 328 */         str12 = str12 + c + "1";
/* 329 */         str12 = str12 + c + "";
/* 330 */         str12 = str12 + c + "0";
/* 331 */         str12 = str12 + c + "";
/* 332 */         str12 = str12 + c + str4;
/* 333 */         str12 = str12 + c + "5";
/* 334 */         str12 = str12 + c + str5;
/* 335 */         str12 = str12 + c + str11;
/* 336 */         str12 = str12 + c + str10;
/* 337 */         recordSet.executeProc("CptUseLogDiscard_Insert2", str12);
/*     */       } else {
/* 339 */         str12 = str8;
/* 340 */         str12 = str12 + c + str7;
/* 341 */         str12 = str12 + c + "0";
/* 342 */         str12 = str12 + c + str6;
/* 343 */         str12 = str12 + c + str3;
/* 344 */         str12 = str12 + c + "";
/* 345 */         str12 = str12 + c + "0";
/* 346 */         str12 = str12 + c + "";
/* 347 */         str12 = str12 + c + str4;
/* 348 */         str12 = str12 + c + "5";
/* 349 */         str12 = str12 + c + str5;
/* 350 */         str12 = str12 + c + str11;
/* 351 */         str12 = str12 + c + str10;
/*     */         
/* 353 */         recordSet.executeProc("CptUseLogDiscard_Insert2", str12);
/* 354 */         recordSet.next();
/* 355 */         String str = recordSet.getString(1);
/*     */         
/* 357 */         if (str.equals("-1")) {
/* 358 */           str2 = SystemEnv.getHtmlLabelName(503030, this.user.getLanguage());
/*     */         }
/*     */       } 
/* 361 */     } else if ("cptmend".equals(str1)) {
/* 362 */       String str3 = Util.null2String(this.params.get("menddate"));
/* 363 */       String str4 = Util.null2String(this.params.get("maintaincompany"));
/* 364 */       String str5 = Util.null2String(this.params.get("operator"));
/* 365 */       String str6 = Util.null2String(this.params.get("mendperioddate"));
/* 366 */       String str7 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("cost")), 0.0D));
/* 367 */       String str8 = Util.null2String(this.params.get("remark"));
/* 368 */       String str9 = Util.null2String(this.params.get("capitalid"));
/*     */       
/* 370 */       String str10 = "";
/* 371 */       str10 = "select departmentid from CptCapital where id=" + str9;
/* 372 */       recordSet.execute(str10);
/* 373 */       recordSet.next();
/* 374 */       String str11 = recordSet.getString("departmentid");
/*     */       
/* 376 */       String str12 = "";
/*     */       
/* 378 */       str12 = str9;
/* 379 */       str12 = str12 + c + str3;
/* 380 */       str12 = str12 + c + "";
/* 381 */       str12 = str12 + c + "" + str5;
/* 382 */       str12 = str12 + c + "1";
/* 383 */       str12 = str12 + c + "";
/* 384 */       str12 = str12 + c + "0";
/* 385 */       str12 = str12 + c + str4;
/* 386 */       str12 = str12 + c + str7;
/* 387 */       str12 = str12 + c + "4";
/* 388 */       str12 = str12 + c + str8;
/* 389 */       str12 = str12 + c + "" + this.user.getUID();
/* 390 */       str12 = str12 + c + str6;
/* 391 */       str12 = str12 + c + str11;
/*     */       
/* 393 */       recordSet.executeProc("CptUseLogMend_Insert2", str12);
/* 394 */       capitalComInfo.removeCapitalCache();
/*     */     }
/* 396 */     else if ("cptback".equals(str1)) {
/* 397 */       String str3 = Util.null2String(this.params.get("method"));
/* 398 */       String str4 = Util.null2String(this.params.get("remark"));
/* 399 */       String str5 = Util.null2String(this.params.get("capitalid"));
/* 400 */       String str6 = Util.null2String(this.params.get("stockindate"));
/*     */       
/* 402 */       String str7 = "select sptcount,departmentid,stateid,resourceid,deprestartdate from CptCapital where id=" + str5;
/* 403 */       recordSet.execute(str7);
/* 404 */       recordSet.next();
/* 405 */       String str8 = recordSet.getString("sptcount");
/* 406 */       String str9 = recordSet.getString("resourceid");
/* 407 */       String str10 = recordSet.getString("stateid");
/* 408 */       String str11 = recordSet.getString("deprestartdate");
/* 409 */       String str12 = Util.null2String(recordSet.getString("departmentid"));
/*     */       
/* 411 */       char c1 = Util.getSeparator();
/* 412 */       String str13 = "";
/* 413 */       if ("backMyCpt".equalsIgnoreCase(str3)) {
/* 414 */         str13 = str5;
/* 415 */         str13 = str13 + c1 + TimeUtil.getCurrentDateString();
/* 416 */         str13 = str13 + c1 + "";
/* 417 */         str13 = str13 + c1 + "" + this.user.getUID();
/* 418 */         str13 = str13 + c1 + "1";
/* 419 */         str13 = str13 + c1 + "";
/* 420 */         str13 = str13 + c1 + "0";
/* 421 */         str13 = str13 + c1 + "";
/* 422 */         str13 = str13 + c1 + "0";
/* 423 */         str13 = str13 + c1 + "1";
/* 424 */         str13 = str13 + c1 + "";
/* 425 */         str13 = str13 + c1 + "0";
/* 426 */         str13 = str13 + c1 + str8;
/* 427 */         recordSet.executeProc("CptUseLogBack_Insert", str13);
/*     */       } else {
/* 429 */         str13 = str5;
/* 430 */         str13 = str13 + c1 + str6;
/* 431 */         str13 = str13 + c1 + "";
/* 432 */         str13 = str13 + c1 + "" + this.user.getUID();
/* 433 */         str13 = str13 + c1 + "1";
/* 434 */         str13 = str13 + c1 + "";
/* 435 */         str13 = str13 + c1 + "0";
/* 436 */         str13 = str13 + c1 + "";
/* 437 */         str13 = str13 + c1 + "0";
/* 438 */         str13 = str13 + c1 + "1";
/* 439 */         str13 = str13 + c1 + str4;
/* 440 */         str13 = str13 + c1 + "0";
/* 441 */         str13 = str13 + c1 + str8;
/* 442 */         recordSet.executeProc("CptUseLogBack_Insert", str13);
/*     */       } 
/*     */       
/* 445 */       if (!"".equals(str9) && !"0".equals(str9) && str10.equals("4")) {
/* 446 */         recordSet.execute("select usestatus from cptuselog where capitalid='" + str5 + "' order by id desc");
/* 447 */         int i = recordSet.getCounts();
/* 448 */         while (i >= 1) {
/* 449 */           recordSet.next();
/* 450 */           if ("2".equals(recordSet.getString(1)) || "3".equals(recordSet.getString(1))) {
/*     */             break;
/*     */           }
/* 453 */           i--;
/*     */         } 
/* 455 */         String str = Util.null2String(recordSet.getString("usestatus"));
/* 456 */         recordSet.execute("update CptCapital set resourceid = '" + str9 + "',departmentid = '" + str12 + "',deprestartdate='" + str11 + "',costcenterid = null ,stateid = '" + str + "' where id = " + str5);
/*     */       } 
/*     */       
/*     */       try {
/* 460 */         cptShare.setCptShareByCpt(str5);
/* 461 */       } catch (Exception exception) {
/* 462 */         exception.printStackTrace();
/*     */       } 
/* 464 */       if (str10.equals("2") || str10.equals("3")) {
/* 465 */         recordSet.execute("Update CptCapital Set deprestartdate = null where id = " + str5);
/*     */       }
/* 467 */       capitalComInfo.removeCapitalCache();
/* 468 */     } else if ("cptchange".equals(str1)) {
/* 469 */       CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 470 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */       
/* 472 */       String str3 = Util.null2String(this.params.get("capitalid"));
/* 473 */       String str4 = Util.null2String(this.params.get("mark"));
/* 474 */       String str5 = Util.null2String(this.params.get("capitalgroupid"));
/* 475 */       String str6 = Util.null2String(this.params.get("resourceid"));
/* 476 */       String str7 = resourceComInfo.getDepartmentID(str6);
/* 477 */       String str8 = Util.null2String(this.params.get("capitalspec"));
/* 478 */       String str9 = Util.null2String(this.params.get("stockindate"));
/* 479 */       String str10 = Util.null2String(this.params.get("location"));
/* 480 */       String str11 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("startprice")), 0.0D));
/* 481 */       String str12 = Util.getNumStr(Util.getDoubleValue(Util.null2String(this.params.get("capitalnum")), 0.0D));
/* 482 */       String str13 = Util.null2String(this.params.get("blongdepartmentid"));
/* 483 */       String str14 = departmentComInfo.getSubcompanyid1(str13);
/* 484 */       String str15 = Util.null2String(this.params.get("warehouse"));
/* 485 */       String str16 = Util.null2String(this.params.get("remark"));
/*     */       
/* 487 */       recordSet.execute("select sptcount,stateid,resourceid,departmentid,capitalgroupid,capitalnum,mark,location,capitaltypeid,capitalspec,StockInDate,startprice,blongsubcompany,blongdepartment,datatype,warehouse from cptcapital where id='" + str3 + "' and isdata=2");
/*     */       
/* 489 */       recordSet.next();
/*     */       
/* 491 */       String str17 = recordSet.getString("datatype");
/* 492 */       String str18 = recordSet.getString("sptcount");
/* 493 */       String str19 = Util.null2String(recordSet.getString("stateid"), "1");
/* 494 */       String str20 = recordSet.getString("resourceid");
/* 495 */       String str21 = recordSet.getString("departmentid");
/* 496 */       String str22 = recordSet.getString("capitalgroupid");
/* 497 */       double d1 = Util.getDoubleValue(recordSet.getString("CptCapital", "capitalnum", true, true), 0.0D);
/* 498 */       String str23 = recordSet.getString("mark");
/* 499 */       String str24 = recordSet.getString("CptCapital", "location", true, true);
/* 500 */       String str25 = recordSet.getString("capitalspec");
/* 501 */       String str26 = recordSet.getString("StockInDate");
/* 502 */       double d2 = Util.getDoubleValue(recordSet.getString("CptCapital", "startprice", true, true), 0.0D);
/* 503 */       String str27 = recordSet.getString("blongsubcompany");
/* 504 */       String str28 = recordSet.getString("blongdepartment");
/* 505 */       String str29 = recordSet.getString("warehouse");
/* 506 */       if ("1".equals(str18)) {
/* 507 */         str12 = "1.0";
/*     */       }
/* 509 */       String str30 = "";
/*     */       
/* 511 */       String str31 = "update cptcapital set ";
/* 512 */       if (!"".equals(str6) && "1".equals(str18) && !"1".equals(str19) && !"5".equals(str19) && !"-7".equals(str19)) {
/* 513 */         str31 = str31 + "resourceid='" + str6 + "',departmentid='" + str7 + "',";
/* 514 */         if (!str6.equals(str20)) {
/* 515 */           str30 = str3;
/* 516 */           str30 = str30 + c + "resourceid";
/* 517 */           str30 = str30 + c + resourceComInfo.getResourcename(str20);
/* 518 */           str30 = str30 + c + resourceComInfo.getResourcename(str6);
/* 519 */           str30 = str30 + c + "" + this.user.getUID();
/* 520 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 521 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/* 523 */         if (!str7.equals(str21)) {
/* 524 */           str30 = str3;
/* 525 */           str30 = str30 + c + "departmentid";
/*     */           try {
/* 527 */             str30 = str30 + c + departmentComInfo.getDepartmentName(str21);
/* 528 */             str30 = str30 + c + departmentComInfo.getDepartmentName(str7);
/* 529 */           } catch (Exception exception) {
/* 530 */             exception.printStackTrace();
/*     */           } 
/* 532 */           str30 = str30 + c + "" + this.user.getUID();
/* 533 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 534 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 537 */       if (!"".equals(str5)) {
/* 538 */         str31 = str31 + "capitalgroupid='" + str5 + "',";
/* 539 */         if (!str5.equals(str22)) {
/* 540 */           str30 = str3;
/* 541 */           str30 = str30 + c + "capitalgroupid";
/* 542 */           str30 = str30 + c + capitalAssortmentComInfo.getAssortmentName(str22);
/* 543 */           str30 = str30 + c + capitalAssortmentComInfo.getAssortmentName(str5);
/* 544 */           str30 = str30 + c + "" + this.user.getUID();
/* 545 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 546 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 549 */       if (Util.getDoubleValue(Util.null2String(this.params.get("capitalnum")), 0.0D) != 0.0D) {
/* 550 */         str31 = str31 + "capitalnum='" + str12 + "',";
/* 551 */         if (Util.getDoubleValue(Util.null2String(this.params.get("capitalnum")), 0.0D) != d1) {
/* 552 */           str30 = str3;
/* 553 */           str30 = str30 + c + "capitalnum";
/* 554 */           str30 = str30 + c + "" + d1;
/* 555 */           str30 = str30 + c + "" + str12;
/* 556 */           str30 = str30 + c + "" + this.user.getUID();
/* 557 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 558 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 561 */       if (!"".equals(str4) && !CptUtil.checkmarkstr(str4)) {
/* 562 */         str31 = str31 + "mark='" + str4 + "',";
/* 563 */         if (!str4.equals(str23)) {
/* 564 */           str30 = str3;
/* 565 */           str30 = str30 + c + "mark";
/* 566 */           str30 = str30 + c + str23;
/* 567 */           str30 = str30 + c + str4;
/* 568 */           str30 = str30 + c + "" + this.user.getUID();
/* 569 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 570 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 573 */       if (!"".equals(str10)) {
/* 574 */         str31 = str31 + "location='" + str10 + "',";
/* 575 */         if (!str10.equals(str24)) {
/* 576 */           str30 = str3;
/* 577 */           str30 = str30 + c + "location";
/* 578 */           str30 = str30 + c + str24;
/* 579 */           str30 = str30 + c + str10;
/* 580 */           str30 = str30 + c + "" + this.user.getUID();
/* 581 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 582 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 585 */       if (!"".equals(str8)) {
/* 586 */         str31 = str31 + "capitalspec='" + str8 + "',";
/* 587 */         if (!str8.equals(str25)) {
/* 588 */           str30 = str3;
/* 589 */           str30 = str30 + c + "capitalspec";
/* 590 */           str30 = str30 + c + str25;
/* 591 */           str30 = str30 + c + str8;
/* 592 */           str30 = str30 + c + "" + this.user.getUID();
/* 593 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 594 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 597 */       if (!"".equals(str9)) {
/* 598 */         str31 = str31 + "StockInDate='" + str9 + "',";
/* 599 */         if (!str9.equals(str26)) {
/* 600 */           str30 = str3;
/* 601 */           str30 = str30 + c + "stockindate";
/* 602 */           str30 = str30 + c + str26;
/* 603 */           str30 = str30 + c + str9;
/* 604 */           str30 = str30 + c + "" + this.user.getUID();
/* 605 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 606 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 609 */       if (Util.getDoubleValue(Util.null2String(this.params.get("startprice")), 0.0D) != 0.0D) {
/* 610 */         str31 = str31 + "startprice='" + str11 + "',";
/* 611 */         if (Util.getDoubleValue(Util.null2String(this.params.get("startprice")), 0.0D) != d2) {
/* 612 */           str30 = str3;
/* 613 */           str30 = str30 + c + "startprice";
/* 614 */           str30 = str30 + c + "" + d2;
/* 615 */           str30 = str30 + c + "" + str11;
/* 616 */           str30 = str30 + c + "" + this.user.getUID();
/* 617 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 618 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 621 */       if (!"".equals(str14)) {
/* 622 */         str31 = str31 + "blongsubcompany='" + str14 + "',";
/* 623 */         if (!str14.equals(str27)) {
/* 624 */           str30 = str3;
/* 625 */           str30 = str30 + c + "blongsubcompany";
/* 626 */           str30 = str30 + c + subCompanyComInfo.getSubCompanyname(str27);
/* 627 */           str30 = str30 + c + subCompanyComInfo.getSubCompanyname(str14);
/* 628 */           str30 = str30 + c + "" + this.user.getUID();
/* 629 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 630 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/* 632 */         if (!str13.equals(str28)) {
/* 633 */           if (!bool1 && str18.equalsIgnoreCase("0")) {
/* 634 */             recordSet.executeQuery("select * from CptCapital where datatype=? and blongdepartment=?", new Object[] { str17, str13 });
/* 635 */             if (recordSet.next()) {
/*     */               
/* 637 */               recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum+" + str12 + ",startprice=(capitalnum*startprice+" + (new BigDecimal(str12))
/* 638 */                   .setScale(2, 4).multiply((new BigDecimal(d2)).setScale(2, 4)) + ")/(capitalnum+" + str12 + ") where id = " + recordSet
/* 639 */                   .getString("id"), new Object[0]);
/* 640 */               recordSet.executeUpdate("delete from CptCapital where id=?", new Object[] { str3 });
/*     */             } else {
/* 642 */               str31 = str31 + "blongdepartment='" + str13 + "',";
/*     */             } 
/*     */           } else {
/* 645 */             str31 = str31 + "blongdepartment='" + str13 + "',";
/*     */           } 
/* 647 */           str30 = str3;
/* 648 */           str30 = str30 + c + "blongdepartment";
/*     */           try {
/* 650 */             str30 = str30 + c + departmentComInfo.getDepartmentName(str28);
/* 651 */             str30 = str30 + c + departmentComInfo.getDepartmentName(str13);
/* 652 */           } catch (Exception exception) {
/* 653 */             exception.printStackTrace();
/*     */           } 
/* 655 */           str30 = str30 + c + "" + this.user.getUID();
/* 656 */           str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 657 */           recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */         } 
/*     */       } 
/* 660 */       if (bool1 && !"".equals(str15) && 
/* 661 */         !str15.equals(str29)) {
/* 662 */         if (str18.equalsIgnoreCase("0")) {
/* 663 */           recordSet.executeQuery("select * from CptCapital where datatype=? and blongdepartment=? and warehouse=?", new Object[] { str17, str13, str15 });
/* 664 */           if (recordSet.next()) {
/*     */             
/* 666 */             recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum+" + str12 + ",startprice=(capitalnum*startprice+" + (new BigDecimal(str12))
/* 667 */                 .setScale(2, 4).multiply((new BigDecimal(d2)).setScale(2, 4)) + ")/(capitalnum+" + str12 + ") where id = " + recordSet
/* 668 */                 .getString("id"), new Object[0]);
/* 669 */             recordSet.executeUpdate("delete from CptCapital where id=?", new Object[] { str3 });
/*     */           } else {
/* 671 */             str31 = str31 + "warehouse='" + str15 + "',";
/*     */           } 
/*     */         } else {
/* 674 */           str31 = str31 + "warehouse='" + str15 + "',";
/*     */         } 
/* 676 */         str30 = str3;
/* 677 */         str30 = str30 + c + "warehouse";
/* 678 */         str30 = str30 + c + capitalTransMethod.getWareHouseName(str29);
/* 679 */         str30 = str30 + c + capitalTransMethod.getWareHouseName(str15);
/* 680 */         str30 = str30 + c + "" + this.user.getUID();
/* 681 */         str30 = str30 + c + TimeUtil.getCurrentDateString();
/* 682 */         recordSet.executeProc("CptCapitalModify_Insert", str30);
/*     */       } 
/*     */       
/* 685 */       str31 = str31 + "name=name where id='" + str3 + "'";
/* 686 */       recordSet.execute(str31);
/*     */       
/* 688 */       str31 = "insert into CptUseLog(capitalid,usedate,userequest,useresourceid,usestatus,remark,resourceid,usecount) values(";
/* 689 */       if (Util.getDoubleValue(Util.null2String(this.params.get("capitalnum")), 0.0D) != 0.0D) {
/* 690 */         str31 = str31 + "'" + str3 + "','" + TimeUtil.getCurrentDateString() + "',null,'" + this.user.getUID() + "','7','" + str16 + "','" + this.user.getUID() + "','" + str12 + "'";
/*     */       } else {
/* 692 */         str31 = str31 + "'" + str3 + "','" + TimeUtil.getCurrentDateString() + "',null,'" + this.user.getUID() + "','7','" + str16 + "','" + this.user.getUID() + "','" + d1 + "'";
/*     */       } 
/* 694 */       str31 = str31 + ")";
/* 695 */       recordSet.execute(str31);
/*     */       
/* 697 */       if (!"".equals(str5) && 
/* 698 */         !str5.equals(str22)) {
/* 699 */         CptRightShareUitl.freshenCptShareByCapitalgroup(str3, str22);
/*     */       }
/*     */ 
/*     */       
/* 703 */       if (!"".equals(str6) && "1".equals(str18) && !"1".equals(str19) && !"5".equals(str19) && !"-7".equals(str19) && 
/* 704 */         !str6.equals(str20)) {
/* 705 */         cptShare.freshenCptShareByResource(str3);
/*     */       }
/*     */ 
/*     */       
/* 709 */       if (!"".equals(str15) && 
/* 710 */         !str15.equals(str29)) {
/* 711 */         cptShare.freshenCptShareByWareHouse(str3);
/*     */       }
/*     */       
/* 714 */       capitalComInfo.removeCapitalCache();
/*     */     } 
/* 716 */     hashMap.put("msg", str2);
/* 717 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/DoCptManagerCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */