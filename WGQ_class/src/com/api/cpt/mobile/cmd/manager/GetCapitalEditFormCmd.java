/*     */ package com.api.cpt.mobile.cmd.manager;
/*     */ 
/*     */ import com.api.cpt.util.CptFormItemUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CptCardGroupComInfo;
/*     */ import weaver.cpt.util.CptFieldComInfo;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.proj.util.CodeUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetCapitalEditFormCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetCapitalEditFormCmd(Map<String, Object> paramMap, User paramUser) {
/*  35 */     this.user = paramUser;
/*  36 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  41 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  47 */     String str1 = Util.null2String(this.params.get("capitalid"));
/*     */     
/*  49 */     RecordSet recordSet1 = new RecordSet();
/*  50 */     RecordSet recordSet2 = new RecordSet();
/*  51 */     recordSet1.isReturnDecryptData(true);
/*  52 */     recordSet1.executeProc("CptCapital_SelectByID", str1);
/*  53 */     recordSet1.next();
/*     */     
/*  55 */     String str2 = Util.null2String(recordSet1.getString("name"));
/*  56 */     String str3 = Util.null2String(recordSet1.getString("isdata"));
/*  57 */     String str4 = Util.null2String(recordSet1.getString("sptcount"));
/*  58 */     String str5 = Util.null2String(recordSet1.getString("capitalgroupid"));
/*     */     
/*  60 */     String str6 = "1";
/*  61 */     CodeUtil codeUtil = new CodeUtil();
/*  62 */     if ("1".equals(str3)) {
/*  63 */       str6 = codeUtil.getCptData1CodeUse();
/*     */     } else {
/*  65 */       str6 = codeUtil.getCptData2CodeUse();
/*     */     } 
/*     */     
/*  68 */     RecordSet recordSet3 = new RecordSet();
/*  69 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  70 */     XssUtil xssUtil = new XssUtil();
/*  71 */     recordSet3.execute("select supassortmentstr from CptCapitalAssortment where id =  " + str5);
/*  72 */     String str7 = "";
/*  73 */     if (recordSet3.next()) {
/*  74 */       str7 = recordSet3.getString("supassortmentstr");
/*     */     }
/*  76 */     String[] arrayOfString = str7.split("\\|");
/*  77 */     String str8 = ""; byte b;
/*  78 */     for (b = 1; b < arrayOfString.length; b++) {
/*  79 */       str8 = str8 + capitalAssortmentComInfo.getAssortmentName(arrayOfString[b]) + " > ";
/*     */     }
/*  81 */     str8 = str8 + capitalAssortmentComInfo.getAssortmentName(str5);
/*     */     
/*  83 */     b = 0;
/*  84 */     String str9 = ",";
/*  85 */     boolean bool = "1".equals(str3);
/*  86 */     if (bool) {
/*  87 */       recordSet2.execute("select * from cptcapital where isdata = 2  and datatype = " + str1 + " ");
/*  88 */       if (recordSet2.next()) {
/*  89 */         str9 = str9 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,sptcount,alertnum,warehouse,address,";
/*     */       } else {
/*  91 */         str9 = str9 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,alertnum,warehouse,address,";
/*     */       } 
/*     */     } else {
/*  94 */       str9 = str9 + "resourceid,capitalnum,stateid,deprestartdate,usedyear,currentprice,sptcount,departmentid,warehouse,";
/*  95 */       if (!"1".equals(str4)) {
/*  96 */         str9 = str9 + "depreyear,deprerate,";
/*     */       } else {
/*  98 */         str9 = str9 + "alertnum,";
/*     */       } 
/*     */     } 
/* 101 */     String str10 = "";
/* 102 */     CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 103 */     CptCardGroupComInfo cptCardGroupComInfo = new CptCardGroupComInfo();
/* 104 */     TreeMap treeMap = cptFieldComInfo.getGroupFieldMap();
/*     */     
/* 106 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 107 */     ArrayList<Map<String, String>> arrayList1 = null;
/* 108 */     HashMap<Object, Object> hashMap1 = null;
/*     */     
/* 110 */     cptCardGroupComInfo.setTofirstRow();
/* 111 */     while (cptCardGroupComInfo.next()) {
/* 112 */       String str11 = cptCardGroupComInfo.getGroupid();
/* 113 */       TreeMap treeMap1 = (TreeMap)treeMap.get(str11);
/* 114 */       if (treeMap1 == null || treeMap1.size() == 0) {
/*     */         continue;
/*     */       }
/* 117 */       int i = Util.getIntValue(cptCardGroupComInfo.getLabel(), -1);
/* 118 */       String str12 = SystemEnv.getHtmlLabelName(i, this.user.getLanguage());
/* 119 */       hashMap1 = new HashMap<>();
/* 120 */       hashMap1.put("title", str12);
/* 121 */       arrayList1 = new ArrayList();
/*     */       
/* 123 */       Iterator<Map.Entry> iterator = treeMap1.entrySet().iterator();
/* 124 */       while (iterator.hasNext()) {
/*     */ 
/*     */         
/* 127 */         byte b1 = 2;
/* 128 */         Map.Entry entry = iterator.next();
/* 129 */         JSONObject jSONObject = (JSONObject)entry.getValue();
/* 130 */         String str13 = "";
/* 131 */         String str14 = "";
/* 132 */         Integer integer = Integer.valueOf(0);
/* 133 */         String str15 = "";
/* 134 */         String str16 = "";
/* 135 */         String str17 = "";
/* 136 */         String str18 = "";
/* 137 */         int j = 0;
/* 138 */         int k = 0;
/*     */         try {
/* 140 */           str13 = "" + jSONObject.getInt("id");
/* 141 */           str14 = "" + jSONObject.getInt("fieldlabel");
/* 142 */           integer = Integer.valueOf(jSONObject.getInt("fieldhtmltype"));
/* 143 */           str15 = "" + jSONObject.getInt("type");
/* 144 */           str16 = "" + jSONObject.getInt("type");
/* 145 */           str17 = jSONObject.getString("fieldname");
/* 146 */           str18 = jSONObject.getString("fielddbtype");
/* 147 */           j = jSONObject.getInt("issystem");
/* 148 */           k = jSONObject.getInt("ismand");
/* 149 */         } catch (JSONException jSONException) {
/* 150 */           jSONException.printStackTrace();
/*     */         } 
/*     */         
/* 153 */         if (str9.indexOf("," + str17 + ",") != -1) {
/*     */           continue;
/*     */         }
/* 156 */         if ("resourceid".equals(str17) && bool) {
/* 157 */           str14 = "1507";
/* 158 */         } else if ("mark".equals(str17) && !"2".equals(str6)) {
/*     */           continue;
/*     */         } 
/*     */         
/* 162 */         if (k == 1) {
/* 163 */           b1 = 3;
/*     */         }
/* 165 */         if (str10.indexOf("," + str17 + ",") != -1) {
/* 166 */           b1 = 1;
/*     */         }
/* 168 */         int m = CptFormItemUtil.formatInputLength(str18, integer.intValue(), str16);
/* 169 */         String str19 = SystemEnv.getHtmlLabelNames(str14, this.user.getLanguage());
/* 170 */         if ("deprerate".equalsIgnoreCase(str17)) {
/* 171 */           str19 = str19 + "(%)";
/*     */         }
/* 173 */         Map<String, String> map = null;
/* 174 */         String str20 = null;
/* 175 */         if (this.params != null) {
/* 176 */           str20 = Util.null2String(recordSet1.getString(str17));
/*     */         }
/* 178 */         if ("capitalgroupid".equals(str17)) {
/* 179 */           str20 = str8;
/* 180 */           integer = Integer.valueOf(1);
/* 181 */           str15 = "1";
/* 182 */           m = 100;
/* 183 */           b1 = 1;
/*     */         } 
/*     */         
/* 186 */         if (integer.intValue() == 1) {
/* 187 */           if ("2".equals(str15) || "3".equals(str15)) {
/*     */             
/* 189 */             int n = 0;
/* 190 */             if ("3".equals(str15)) {
/*     */               
/* 192 */               String str = str18.substring(str18.indexOf(",") + 1, str18.length() - 1);
/* 193 */               n = Integer.parseInt(str);
/*     */             } 
/* 195 */             map = CptFormItemUtil.getFormItemForInputNumber(str17, str19, str20, b1, "", "", n, "1");
/*     */           } else {
/*     */             
/* 198 */             map = CptFormItemUtil.getFormItemForInput(str17, str19, str20, m, b1);
/*     */           }
/*     */         
/* 201 */         } else if (integer.intValue() == 2) {
/* 202 */           if ("1".equals(str15)) {
/*     */             
/* 204 */             map = CptFormItemUtil.getFormItemForTextArea(str17, str19, str20, m, b1);
/* 205 */           } else if ("2".equals(str15)) {
/*     */             
/* 207 */             map = CptFormItemUtil.getFormItemForTextAreaHtml(str17, str19, str20, m, b1);
/*     */           } 
/* 209 */         } else if (integer.intValue() == 3) {
/* 210 */           if (str16.equals("2") || str16.equals("19") || str16.equals("402") || str16.equals("403")) {
/* 211 */             if (str16.equals("2")) {
/* 212 */               map = CptFormItemUtil.getFormItemForDate(str17, str19, str20, b1);
/* 213 */             } else if (str16.equals("19")) {
/* 214 */               map = CptFormItemUtil.getFormItemForTime(str17, str19, str20, b1);
/* 215 */             } else if (str16.equals("402")) {
/* 216 */               map = CptFormItemUtil.getFormItemForYear(str17, str19, str20, b1, "1");
/* 217 */             } else if (str16.equals("403")) {
/* 218 */               map = CptFormItemUtil.getFormItemForYearAndMonth(str17, str19, str20, b1, "1");
/*     */             }
/*     */           
/* 221 */           } else if ("7".equals(str15) && j == 1) {
/*     */             
/* 223 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 224 */             hashMap.put("sqlwhere", xssUtil.put("where t1.type=2"));
/* 225 */             map = CptFormItemUtil.getFormItemForBrowser(str17, str19, str16, str20, b1, str18, null, hashMap);
/*     */           } else {
/*     */             
/* 228 */             map = CptFormItemUtil.getFormItemForBrowser(str17, str19, str16, str20, b1, str18, null, null);
/*     */           } 
/*     */           
/* 231 */           map.put("viewAttr", "" + b1);
/* 232 */         } else if (integer.intValue() == 4) {
/*     */           
/* 234 */           map = CptFormItemUtil.getFormItemForCheckbox(str17, str19, str20, b1);
/* 235 */         } else if (integer.intValue() == 5) {
/*     */           
/* 237 */           if ("attribute".equals(str17) && 
/* 238 */             "9".equals(str20)) {
/* 239 */             str20 = "";
/*     */           }
/*     */ 
/*     */           
/* 243 */           map = CptFormItemUtil.getFormItemForSelect(str17, str19, str13, str20, b1, false, false, this.user.getLanguage());
/* 244 */         } else if (integer.intValue() == 6) {
/*     */           
/* 246 */           map = CptFormItemUtil.getFormItemForAttachment(str17, str19, str20, b1);
/*     */         } 
/* 248 */         arrayList1.add(map);
/*     */       } 
/* 250 */       b++;
/* 251 */       if (b == 1) {
/*     */         
/* 253 */         String str = Util.null2String(recordSet1.getString("capitalimageid"));
/* 254 */         Map<String, String> map = CptFormItemUtil.getFormItemForUpload("capitalimageid", SystemEnv.getHtmlLabelName(74, this.user.getLanguage()), str, 2);
/* 255 */         Map map1 = CptFormItemUtil.getImgDatasList(str, "", "1");
/* 256 */         map.putAll(map1);
/* 257 */         arrayList1.add(map);
/*     */       } 
/*     */       
/* 260 */       hashMap1.put("items", arrayList1);
/* 261 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 262 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 265 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 266 */     hashMap2.put("title", str2);
/* 267 */     hashMap2.put("fieldinfo", arrayList);
/* 268 */     hashMap2.put("isdata", str3);
/* 269 */     return (Map)hashMap2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/manager/GetCapitalEditFormCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */