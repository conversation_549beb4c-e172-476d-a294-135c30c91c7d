/*    */ package com.api.cpt.mobile.cmd.portal;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetCptMenuRightCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetCptMenuRightCmd(Map<String, Object> paramMap, User paramUser) {
/* 20 */     this.user = paramUser;
/* 21 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 26 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 34 */     if (HrmUserVarify.checkUserRight("CptCapital:InStockCheck", this.user)) {
/* 35 */       hashMap.put("InStockCheck", Boolean.valueOf(true));
/*    */     } else {
/* 37 */       hashMap.put("cpt_InStockCheck", Boolean.valueOf(false));
/*    */     } 
/*    */ 
/*    */ 
/*    */     
/* 42 */     if (HrmUserVarify.checkUserRight("CptCapital:Use", this.user)) {
/* 43 */       hashMap.put("cptuse", Boolean.valueOf(true));
/*    */     } else {
/* 45 */       hashMap.put("cptuse", Boolean.valueOf(false));
/*    */     } 
/*    */     
/* 48 */     if (HrmUserVarify.checkUserRight("CptCapital:MoveIn", this.user)) {
/* 49 */       hashMap.put("cptmove", Boolean.valueOf(true));
/*    */     } else {
/* 51 */       hashMap.put("cptmove", Boolean.valueOf(false));
/*    */     } 
/*    */     
/* 54 */     if (HrmUserVarify.checkUserRight("CptCapital:Lend", this.user)) {
/* 55 */       hashMap.put("cptlend", Boolean.valueOf(true));
/*    */     } else {
/* 57 */       hashMap.put("cptlend", Boolean.valueOf(false));
/*    */     } 
/*    */     
/* 60 */     if (HrmUserVarify.checkUserRight("CptCapital:Loss", this.user)) {
/* 61 */       hashMap.put("cptloss", Boolean.valueOf(true));
/*    */     } else {
/* 63 */       hashMap.put("cptloss", Boolean.valueOf(false));
/*    */     } 
/*    */     
/* 66 */     if (HrmUserVarify.checkUserRight("CptCapital:Discard", this.user)) {
/* 67 */       hashMap.put("cptdiscard", Boolean.valueOf(true));
/*    */     } else {
/* 69 */       hashMap.put("cptdiscard", Boolean.valueOf(false));
/*    */     } 
/*    */     
/* 72 */     if (HrmUserVarify.checkUserRight("CptCapital:Mend", this.user)) {
/* 73 */       hashMap.put("cptmend", Boolean.valueOf(true));
/*    */     } else {
/* 75 */       hashMap.put("cptmend", Boolean.valueOf(false));
/*    */     } 
/*    */     
/* 78 */     if (HrmUserVarify.checkUserRight("CptCapital:Return", this.user)) {
/* 79 */       hashMap.put("cptback", Boolean.valueOf(true));
/*    */     } else {
/* 81 */       hashMap.put("cptback", Boolean.valueOf(false));
/*    */     } 
/*    */     
/* 84 */     if (HrmUserVarify.checkUserRight("CptCapital:Change", this.user)) {
/* 85 */       hashMap.put("cptchange", Boolean.valueOf(true));
/*    */     } else {
/* 87 */       hashMap.put("cptchange", Boolean.valueOf(false));
/*    */     } 
/*    */ 
/*    */     
/* 91 */     if (HrmUserVarify.checkUserRight("CptRpCapital:Display", this.user)) {
/* 92 */       hashMap.put("cpt_rpt_Display", Boolean.valueOf(true));
/*    */     } else {
/* 94 */       hashMap.put("cpt_rpt_Display", Boolean.valueOf(false));
/*    */     } 
/* 96 */     hashMap.put("msg", SystemEnv.getHtmlLabelName(2012, Util.getIntValue(this.user.getLanguage())));
/* 97 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetCptMenuRightCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */