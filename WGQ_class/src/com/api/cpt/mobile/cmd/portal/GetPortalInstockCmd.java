/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetPortalInstockCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetPortalInstockCmd(Map<String, Object> paramMap, User paramUser) {
/*  27 */     this.user = paramUser;
/*  28 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  33 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  39 */     if (!HrmUserVarify.checkUserRight("CptCapital:InStockCheck", this.user)) {
/*  40 */       hashMap.put("isright", Boolean.valueOf(false));
/*  41 */       return (Map)hashMap;
/*     */     } 
/*  43 */     RecordSet recordSet = new RecordSet();
/*  44 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  45 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/*  47 */       resourceComInfo = new ResourceComInfo();
/*  48 */     } catch (Exception exception) {
/*  49 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  52 */     byte b1 = 1;
/*  53 */     byte b2 = 5;
/*  54 */     int i = 0;
/*  55 */     int j = 0;
/*     */     
/*  57 */     String str1 = " m.id,m.id as tmpid,m.checkerid,m.buyerid,m.stockindate ";
/*  58 */     String str2 = " where m.ischecked = 0 and m.checkerid in(" + commonShareManager.getContainsSubuserids("" + this.user.getUID()) + ")  ";
/*  59 */     String str3 = " CptStockInMain m ";
/*     */     
/*  61 */     String str4 = "(select " + str1 + " from " + str3 + str2 + ")t";
/*  62 */     String str5 = " select count(1) as c from " + str4;
/*  63 */     recordSet.execute(str5);
/*  64 */     if (recordSet.next()) {
/*  65 */       i = recordSet.getInt("c");
/*     */     }
/*  67 */     if (i <= 0) j = 0; 
/*  68 */     j = i / b2 + ((i % b2 > 0) ? 1 : 0);
/*     */     
/*  70 */     String str6 = "";
/*  71 */     if (recordSet.getDBType().equals("oracle")) {
/*  72 */       str6 = "select *  from " + str4 + " order by id desc";
/*  73 */       str6 = "select t1.*,rownum rn from (" + str6 + ") t1 where rownum <= " + (b1 * b2);
/*  74 */       str6 = "select t2.* from (" + str6 + ") t2 where rn > " + ((b1 - 1) * b2);
/*  75 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  76 */       str6 = "select  *  from " + str4 + " order by id desc limit " + ((b1 - 1) * b2) + "," + b2;
/*     */     }
/*  78 */     else if (recordSet.getDBType().equals("postgresql")) {
/*  79 */       str6 = "select  *  from " + str4 + " order by id desc limit " + b2 + " offset " + ((b1 - 1) * b2);
/*     */     
/*     */     }
/*  82 */     else if (b1 > 1) {
/*  83 */       int k = b2;
/*  84 */       if (b2 * b1 > i) {
/*  85 */         k = i - b2 * (b1 - 1);
/*     */       }
/*  87 */       str6 = "select top " + (b1 * b2) + " *  from " + str4 + " order by id desc";
/*  88 */       str6 = "select top " + k + " t1.* from (" + str6 + ") t1 order by t1.id ";
/*  89 */       str6 = "select top " + k + " t2.* from (" + str6 + ") t2 order by t2.id desc";
/*     */     } else {
/*  91 */       str6 = "select top " + b2 + " *  from " + str4 + " order by id desc";
/*     */     } 
/*     */     
/*  94 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  95 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/*  97 */       if (b1 <= j) {
/*  98 */         connStatement.setStatementSql(str6);
/*  99 */         connStatement.executeQuery();
/*     */         
/* 101 */         while (connStatement.next()) {
/* 102 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 103 */           String str7 = Util.null2String(connStatement.getString("tmpid"));
/* 104 */           hashMap1.put("tmpid", str7);
/* 105 */           String str8 = Util.null2String(connStatement.getString("buyerid"));
/* 106 */           hashMap1.put("buyerid", str8);
/* 107 */           hashMap1.put("buyeridicon", GCONST.getContextPath() + resourceComInfo.getMessagerUrls(str8));
/* 108 */           hashMap1.put("buyeridname", resourceComInfo.getResourcename(str8));
/*     */           
/* 110 */           String str9 = Util.null2String(connStatement.getString("checkerid"));
/* 111 */           hashMap1.put("checkerid", str9);
/* 112 */           hashMap1.put("checkeridname", resourceComInfo.getResourcename(str9));
/* 113 */           hashMap1.put("stockindate", Util.null2String(connStatement.getString("stockindate")));
/*     */           
/* 115 */           recordSet.execute("select SelectDate,contractno,customerid from CptStockInDetail where cptstockinid = " + str7 + " order by id ");
/* 116 */           String str10 = "";
/* 117 */           String str11 = "";
/* 118 */           if (recordSet.next()) {
/* 119 */             str10 = Util.null2String(recordSet.getString("SelectDate"));
/* 120 */             str11 = Util.null2String(recordSet.getString("contractno"));
/*     */           } 
/* 122 */           hashMap1.put("SelectDate", str10);
/* 123 */           hashMap1.put("contractno", str11);
/* 124 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/* 127 */     } catch (Exception exception) {
/* 128 */       writeLog(new Object[] { "com.api.cpt.mobile.cmd.portal.GetInstockListCmd : ", exception });
/*     */     } finally {
/* 130 */       connStatement.close();
/*     */     } 
/* 132 */     hashMap.put("isright", Boolean.valueOf(true));
/* 133 */     hashMap.put("datas", arrayList);
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetPortalInstockCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */