/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.request.RequestCheckUser;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetPortalCountCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetPortalCountCmd(Map<String, Object> paramMap, User paramUser) {
/*  24 */     this.user = paramUser;
/*  25 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  30 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  35 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  36 */     RecordSet recordSet = new RecordSet();
/*  37 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  38 */     int i = 0;
/*     */     
/*  40 */     String str = "select count(1) num from cptCapital where isdata=2";
/*  41 */     recordSet.execute(str);
/*  42 */     if (recordSet.next()) {
/*  43 */       i = Util.getIntValue(recordSet.getString("num"));
/*     */     }
/*  45 */     hashMap1.put("sum", Integer.valueOf(i));
/*     */     
/*  47 */     str = "select count(1) num from cptCapital where isdata=2 and stateid=2 ";
/*  48 */     recordSet.execute(str);
/*  49 */     if (recordSet.next()) {
/*  50 */       i = Util.getIntValue(recordSet.getString("num"));
/*     */     }
/*  52 */     hashMap1.put("fetch_data", Integer.valueOf(i));
/*     */     
/*  54 */     str = "select count(1) num from cptCapital where isdata=2 and stateid=3 ";
/*  55 */     recordSet.execute(str);
/*  56 */     if (recordSet.next()) {
/*  57 */       i = Util.getIntValue(recordSet.getString("num"));
/*     */     }
/*  59 */     hashMap1.put("lend_data", Integer.valueOf(i));
/*     */     
/*  61 */     str = "select count(1) num from CptStockInMain m  where m.ischecked = 0 and m.checkerid in(" + commonShareManager.getContainsSubuserids("" + this.user.getUID()) + ") ";
/*  62 */     recordSet.execute(str);
/*  63 */     if (recordSet.next()) {
/*  64 */       i = Util.getIntValue(recordSet.getString("num"));
/*     */     }
/*  66 */     hashMap1.put("instock_data", Integer.valueOf(i));
/*     */     
/*  68 */     str = "select count(1) num from cpt_inventory_planlist where planliststate in(1,2,3) and countuser=" + this.user.getUID();
/*  69 */     recordSet.execute(str);
/*  70 */     if (recordSet.next()) {
/*  71 */       i = Util.getIntValue(recordSet.getString("num"));
/*     */     }
/*  73 */     hashMap1.put("inventory_data", Integer.valueOf(i));
/*     */ 
/*     */     
/*  76 */     RequestCheckUser requestCheckUser = new RequestCheckUser();
/*  77 */     requestCheckUser.setUserid(this.user.getUID());
/*  78 */     requestCheckUser.setLogintype(this.user.getLogintype());
/*     */     
/*  80 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  81 */     recordSet.execute("SELECT t1.wfid,t1.wftype,t1.ismobilewf FROM cpt_cptwfconf t1 LEFT JOIN workflow_base t2 on t1.wfid=t2.id WHERE t1.ismobilewf=1 ");
/*  82 */     while (recordSet.next()) {
/*  83 */       String str1 = "";
/*  84 */       String str2 = recordSet.getString("wftype");
/*  85 */       int j = recordSet.getInt("wfid");
/*  86 */       if ("apply".equals(str2)) {
/*  87 */         str1 = "cptapply";
/*  88 */       } else if ("applyuse".equals(str2)) {
/*  89 */         str1 = "cptapplyuse";
/*  90 */       } else if ("fetch".equals(str2)) {
/*  91 */         str1 = "cptuse";
/*  92 */       } else if ("move".equals(str2)) {
/*  93 */         str1 = "cptmove";
/*  94 */       } else if ("lend".equals(str2)) {
/*  95 */         str1 = "cptlend";
/*  96 */       } else if ("back".equals(str2)) {
/*  97 */         str1 = "cptback";
/*  98 */       } else if ("loss".equals(str2)) {
/*  99 */         str1 = "cptloss";
/* 100 */       } else if ("discard".equals(str2)) {
/* 101 */         str1 = "cptdiscard";
/* 102 */       } else if ("mend".equals(str2)) {
/* 103 */         str1 = "cptmend";
/* 104 */       } else if ("change".equals(str2)) {
/* 105 */         str1 = "cptchange";
/*     */       } 
/*     */       
/*     */       try {
/* 109 */         requestCheckUser.setWorkflowid(j);
/* 110 */         requestCheckUser.checkUser();
/* 111 */         if (requestCheckUser.getHasright() == 1) {
/* 112 */           hashMap2.put(str1, recordSet.getString("wfid"));
/*     */         }
/* 114 */       } catch (Exception exception) {
/* 115 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 118 */     hashMap1.put("wfmap", hashMap2);
/* 119 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetPortalCountCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */