/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.ExcelToDB.CapitalExcelToDB;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.proj.util.CodeUtil;
/*     */ import weaver.system.code.CodeBuild;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoInstockCheckCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DoInstockCheckCmd(Map<String, Object> paramMap, User paramUser) {
/*  27 */     this.user = paramUser;
/*  28 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  33 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  39 */     RecordSet recordSet1 = new RecordSet();
/*  40 */     RecordSet recordSet2 = new RecordSet();
/*  41 */     RecordSet recordSet3 = new RecordSet();
/*  42 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  43 */     CodeBuild codeBuild = new CodeBuild();
/*     */     
/*  45 */     String str1 = Util.null2String(this.params.get("instockid"));
/*  46 */     String str2 = "";
/*  47 */     String str3 = Util.null2String(this.params.get("cptdept_to"));
/*  48 */     String[] arrayOfString = str1.split(",");
/*     */     
/*  50 */     String str4 = "";
/*  51 */     ArrayList<String> arrayList = new ArrayList();
/*  52 */     boolean bool = false;
/*  53 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  54 */       String str5 = arrayOfString[b];
/*     */       
/*  56 */       String str6 = "select m.id as mainid,d.* from CptStockInDetail d,CptStockInMain m where m.id=d.cptstockinid and m.id = " + str5 + " and m.ischecked=0 order by m.id,d.id ";
/*  57 */       recordSet3.execute(str6);
/*     */       
/*  59 */       recordSet1.execute("select stockindate,buyerid,checkerid from CptStockInMain where id=" + str5);
/*  60 */       if (recordSet1.next()) {
/*  61 */         str2 = recordSet1.getString("stockindate");
/*     */       }
/*  63 */       String str7 = "";
/*  64 */       String str8 = "";
/*  65 */       String str9 = "";
/*  66 */       String str10 = "";
/*  67 */       String str11 = "";
/*  68 */       String str12 = "";
/*     */       
/*  70 */       while (recordSet3.next()) {
/*  71 */         str7 = Util.null2String(recordSet3.getString("id"));
/*  72 */         str8 = Util.null2String(recordSet3.getString("mainid"));
/*  73 */         if (!str8.equals(str5))
/*     */           continue; 
/*  75 */         str9 = Util.null2String(recordSet3.getString("cpttype"));
/*  76 */         str10 = Util.null2String(recordSet3.getString("plannumber"));
/*  77 */         str12 = Util.null2String(recordSet3.getString("capitalmark"));
/*     */         
/*  79 */         String str13 = "";
/*  80 */         String str14 = "";
/*     */         
/*  82 */         recordSet2.executeProc("CptCapital_SelectByID", str9);
/*  83 */         if (recordSet2.next()) {
/*  84 */           str11 = recordSet2.getString("sptcount");
/*  85 */           str13 = recordSet2.getString("capitalgroupid");
/*  86 */           str14 = recordSet2.getString("capitaltypeid");
/*     */         } 
/*     */ 
/*     */         
/*  90 */         CodeUtil codeUtil = new CodeUtil();
/*  91 */         String str15 = codeUtil.getCptData2CodeUse();
/*  92 */         if ("2".equals(str15)) {
/*  93 */           bool = CapitalExcelToDB.checkmarkstr(str12, str3, str9, str11, str10);
/*  94 */           if (bool) {
/*  95 */             arrayList.add(str12);
/*     */           }
/*     */         } 
/*     */         
/*  99 */         recordSet2.execute("select * from CptStockInDetail where id =" + str7);
/* 100 */         String str16 = "";
/* 101 */         if (recordSet2.next()) {
/* 102 */           str16 = recordSet2.getString("selectdate");
/*     */         }
/* 104 */         if (str11.equals("1")) {
/* 105 */           int i = (int)Util.getDoubleValue(str10, 0.0D);
/* 106 */           String str = codeBuild.getCurrentCapitalCodeIsOver(departmentComInfo.getSubcompanyid1(str3), str3, str13, str14, str16, str2, str9, i);
/* 107 */           if ("yes".equals(str)) {
/* 108 */             recordSet2.execute("select name from cptcapital where id =" + str9);
/* 109 */             recordSet2.next();
/* 110 */             String str18 = recordSet2.getString(1);
/* 111 */             hashMap.put("flag", Boolean.valueOf(false));
/* 112 */             hashMap.put("msg", str18 + " " + SystemEnv.getHtmlLabelName(386045, Util.getIntValue(this.user.getLanguage())) + "!");
/*     */             break;
/*     */           } 
/* 115 */           hashMap.put("flag", Boolean.valueOf(true));
/*     */           continue;
/*     */         } 
/* 118 */         String str17 = codeBuild.getCurrentCapitalCodeIsOver(departmentComInfo.getSubcompanyid1(str3), str3, str13, str14, str16, str2, str9, 1);
/* 119 */         if ("yes".equals(str17)) {
/* 120 */           recordSet2.execute("select name from cptcapital where id =" + str9);
/* 121 */           recordSet2.next();
/* 122 */           String str = recordSet2.getString(1);
/* 123 */           hashMap.put("flag", Boolean.valueOf(false));
/* 124 */           hashMap.put("msg", str + " " + SystemEnv.getHtmlLabelName(386045, Util.getIntValue(this.user.getLanguage())) + "!");
/*     */           break;
/*     */         } 
/* 127 */         hashMap.put("flag", Boolean.valueOf(true));
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 132 */     for (String str : arrayList) {
/* 133 */       str4 = str4 + str + ",";
/*     */     }
/* 135 */     if (!str4.equals("")) {
/* 136 */       str4 = str4.substring(0, str4.length() - 1);
/* 137 */       String str = SystemEnv.getHtmlLabelName(502700, this.user.getLanguage());
/* 138 */       str = str + str4;
/* 139 */       hashMap.put("flag", Boolean.valueOf(false));
/* 140 */       hashMap.put("msg", str);
/*     */     } 
/* 142 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/DoInstockCheckCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */