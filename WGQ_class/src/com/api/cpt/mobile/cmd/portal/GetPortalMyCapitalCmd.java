/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.api.cpt.mobile.util.CapitalTransUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetPortalMyCapitalCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetPortalMyCapitalCmd(Map<String, Object> paramMap, User paramUser) {
/*  30 */     this.user = paramUser;
/*  31 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  36 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  43 */     RecordSet recordSet = new RecordSet();
/*  44 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/*  46 */       resourceComInfo = new ResourceComInfo();
/*  47 */     } catch (Exception exception) {
/*  48 */       exception.printStackTrace();
/*     */     } 
/*  50 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  51 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*     */     
/*  53 */     int i = this.user.getUID();
/*  54 */     byte b1 = 2;
/*  55 */     String str1 = "";
/*  56 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/*  57 */       str1 = "Capital:Maintenance";
/*     */     }
/*  59 */     recordSet.execute("select cptdetachable from SystemSet");
/*  60 */     int j = 0;
/*  61 */     if (recordSet.next()) {
/*  62 */       j = recordSet.getInt("cptdetachable");
/*     */     }
/*  64 */     String str2 = "";
/*  65 */     String str3 = " where isdata = 2 and resourceid in( " + (new CommonShareManager()).getContainsSubuserids("" + this.user.getUID()) + ")";
/*     */     
/*  67 */     if (j == 1 && i != 1) {
/*  68 */       String str = "";
/*  69 */       recordSet.executeProc("HrmRoleSR_SeByURId", "" + i + b1 + str1);
/*  70 */       while (recordSet.next()) {
/*  71 */         str2 = recordSet.getString("subcompanyid");
/*  72 */         str = str + ", " + str2;
/*     */       } 
/*  74 */       if (!"".equals(str)) {
/*  75 */         str = str.substring(1);
/*  76 */         str3 = str3 + " and blongsubcompany in (" + str + ") ";
/*     */       } 
/*     */     } 
/*     */     
/*  80 */     byte b2 = 1;
/*  81 */     byte b3 = 5;
/*  82 */     int k = 0;
/*  83 */     int m = 0;
/*     */     
/*  85 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  86 */     commonShareManager.setAliasTableName("t2");
/*  87 */     String str4 = " t1.id,t1.mark,t1.name,t1.capitalspec,t1.stateid,t1.departmentid,t1.resourceid,t1.blongsubcompany,t1.blongdepartment,t1.capitalimageid,t1.capitalgroupid ";
/*  88 */     String str5 = str3 + commonShareManager.getAssortmentSqlWhere(this.user);
/*  89 */     str5 = str5 + " and t1.stateid <> 1 ";
/*     */     
/*  91 */     String str6 = " CptCapital  t1 ";
/*  92 */     String str7 = "(select " + str4 + " from " + str6 + str5 + ")t";
/*  93 */     String str8 = " select count(1) as c from " + str7;
/*  94 */     recordSet.execute(str8);
/*  95 */     if (recordSet.next()) {
/*  96 */       k = recordSet.getInt("c");
/*     */     }
/*  98 */     if (b2) b2 = 1; 
/*  99 */     if (b3 <= 0) b3 = 10;
/*     */     
/* 101 */     if (k <= 0) m = 0; 
/* 102 */     m = k / b3 + ((k % b3 > 0) ? 1 : 0);
/*     */     
/* 104 */     String str9 = "";
/* 105 */     if (recordSet.getDBType().equals("oracle")) {
/* 106 */       str9 = "select *  from " + str7 + " order by id ";
/* 107 */       str9 = "select t1.*,rownum rn from (" + str9 + ") t1 where rownum <= " + (b2 * b3);
/* 108 */       str9 = "select t2.* from (" + str9 + ") t2 where rn > " + ((b2 - 1) * b3);
/* 109 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 110 */       str9 = "select  *  from " + str7 + " order by id limit " + ((b2 - 1) * b3) + "," + b3;
/*     */     }
/* 112 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 113 */       str9 = "select  *  from " + str7 + " order by id limit " + b3 + "  offset " + ((b2 - 1) * b3);
/*     */     
/*     */     }
/* 116 */     else if (b2 > 1) {
/* 117 */       int n = b3;
/* 118 */       if (b3 * b2 > k) {
/* 119 */         n = k - b3 * (b2 - 1);
/*     */       }
/* 121 */       str9 = "select top " + (b2 * b3) + " *  from " + str7 + " order by id ";
/* 122 */       str9 = "select top " + n + " t1.* from (" + str9 + ") t1 order by t1.id desc";
/* 123 */       str9 = "select top " + n + " t2.* from (" + str9 + ") t2 order by t2.id ";
/*     */     } else {
/* 125 */       str9 = "select top " + b3 + " *  from " + str7 + " order by id ";
/*     */     } 
/*     */     
/* 128 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 129 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 131 */       if (b2 <= m) {
/* 132 */         connStatement.setStatementSql(str9);
/* 133 */         connStatement.executeQuery();
/*     */         
/* 135 */         while (connStatement.next()) {
/* 136 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 137 */           String str10 = Util.null2String(connStatement.getString("id"));
/* 138 */           hashMap1.put("id", str10);
/* 139 */           hashMap1.put("mark", Util.null2String(connStatement.getString("mark")));
/* 140 */           hashMap1.put("name", Util.null2String(connStatement.getString("name")));
/* 141 */           hashMap1.put("capitalspec", Util.null2String(connStatement.getString("capitalspec")));
/*     */           
/* 143 */           String str11 = Util.null2String(connStatement.getString("stateid"));
/* 144 */           hashMap1.put("stateid", str11);
/* 145 */           hashMap1.put("statename", CapitalTransUtil.getBrowserShowName(str11, "243+ "));
/*     */           
/* 147 */           String str12 = Util.null2String(connStatement.getString("blongdepartment"));
/* 148 */           hashMap1.put("blongdepartmentid", str12);
/* 149 */           hashMap1.put("blongdepartmentname", departmentComInfo.getDepartmentname(str12));
/*     */           
/* 151 */           String str13 = Util.null2String(connStatement.getString("capitalimageid"));
/*     */           
/* 153 */           if (!"null".equals(str13) && !"".equals(str13) && !"0".equals(str13)) {
/* 154 */             String[] arrayOfString = str13.split(",");
/* 155 */             if (Util.getIntValue(arrayOfString[0], 0) > 0) {
/* 156 */               hashMap1.put("cptcapitalurl", "/weaver/weaver.file.FileDownload?fileid=" + arrayOfString[0]);
/*     */             } else {
/* 158 */               hashMap1.put("cptcapitalurl", GCONST.getContextPath() + "/font/cpt/nocpt_blue.svg");
/*     */             } 
/*     */           } else {
/* 161 */             hashMap1.put("cptcapitalurl", GCONST.getContextPath() + "/font/cpt/nocpt_blue.svg");
/*     */           } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 168 */           String str14 = Util.null2String(connStatement.getString("capitalgroupid"));
/* 169 */           hashMap1.put("capitalgroupid", str14);
/* 170 */           hashMap1.put("capitalgroupidname", capitalAssortmentComInfo.getAssortmentName(str14));
/*     */ 
/*     */           
/* 173 */           boolean bool = false;
/*     */ 
/*     */           
/* 176 */           if (HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", this.user)) {
/* 177 */             bool = true;
/*     */           }
/* 179 */           hashMap1.put("opt_delete", "false");
/* 180 */           hashMap1.put("opt_back", "false");
/* 181 */           hashMap1.put("opt_mend", "false");
/* 182 */           if (bool) {
/* 183 */             hashMap1.put("opt_delete", "true");
/*     */           }
/* 185 */           if (HrmUserVarify.checkUserRight("CptCapital:Return", this.user) && (str11.equals("2") || str11.equals("3") || str11.equals("4"))) {
/* 186 */             hashMap1.put("opt_back", "true");
/*     */           }
/* 188 */           if (HrmUserVarify.checkUserRight("CptCapital:Mend", this.user) && !str11.equals("4") && !str11.equals("5") && !str11.equals("-7")) {
/* 189 */             hashMap1.put("opt_mend", "true");
/*     */           }
/*     */           
/* 192 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/* 195 */     } catch (Exception exception) {
/* 196 */       writeLog(new Object[] { "com.api.cpt.mobile.CptCapitalPortalAction.getCapitalList : ", exception });
/*     */     } finally {
/* 198 */       connStatement.close();
/*     */     } 
/*     */     
/* 201 */     hashMap.put("isright", Boolean.valueOf(true));
/* 202 */     hashMap.put("datas", arrayList);
/* 203 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetPortalMyCapitalCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */