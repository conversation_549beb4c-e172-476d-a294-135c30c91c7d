/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.cpt.util.CptGeneralUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetCapitalListCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetCapitalListCmd(Map<String, Object> paramMap, User paramUser) {
/*  33 */     this.user = paramUser;
/*  34 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  39 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  45 */     RecordSet recordSet1 = new RecordSet();
/*  46 */     RecordSet recordSet2 = new RecordSet();
/*  47 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  48 */     String str1 = Util.null2String(this.params.get("viewFrom"));
/*  49 */     String str2 = Util.null2String(this.params.get("isdata"));
/*  50 */     String str3 = Util.null2String(this.params.get("type"));
/*     */     
/*  52 */     if ("".equals(str2)) {
/*  53 */       str2 = "2";
/*     */     }
/*     */     
/*  56 */     String str4 = ",";
/*  57 */     if ("1".equals(str2)) {
/*  58 */       str4 = str4 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,StockInDate,SelectDate,Invoice,alertnum,";
/*     */     }
/*     */     
/*  61 */     recordSet1.execute("select cptdetachable from SystemSet");
/*  62 */     int i = 0;
/*  63 */     if (recordSet1.next()) {
/*  64 */       i = recordSet1.getInt("cptdetachable");
/*     */     }
/*  66 */     byte b = 2;
/*     */     
/*  68 */     String str5 = "";
/*  69 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/*  70 */       str5 = "Capital:Maintenance";
/*     */     }
/*  72 */     String str6 = "";
/*  73 */     String str7 = " isdata = " + str2;
/*     */     
/*  75 */     if ("mycpt".equals(str3)) {
/*  76 */       String str = Util.null2String(this.params.get("hrmid"));
/*  77 */       if (!"".equals(str)) {
/*  78 */         str7 = str7 + " and resourceid in( " + (new CommonShareManager()).getContainsSubuserids(str) + ")";
/*     */       } else {
/*  80 */         str7 = str7 + " and resourceid in( " + (new CommonShareManager()).getContainsSubuserids("" + this.user.getUID()) + ")";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  85 */     int j = this.user.getUID();
/*  86 */     int k = this.user.getUserSubCompany1();
/*     */     
/*  88 */     if (i == 1 && j != 1 && !"maint".equals(str1)) {
/*  89 */       String str = "";
/*  90 */       recordSet2.executeProc("HrmRoleSR_SeByURId", "" + j + b + str5);
/*  91 */       while (recordSet2.next()) {
/*  92 */         str6 = recordSet2.getString("subcompanyid");
/*  93 */         str = str + ", " + str6;
/*     */       } 
/*  95 */       if (!"".equals(str)) {
/*  96 */         str = str.substring(1);
/*  97 */         str7 = str7 + " and blongsubcompany in (" + str + ") ";
/*     */       } else {
/*  99 */         str7 = str7 + " and blongsubcompany in (" + k + ") ";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 104 */     String str8 = str4 + "name,datatype,warehouseid,capitalgroupid,";
/* 105 */     StringBuffer stringBuffer = CptGeneralUtil.getConditionCusSql(str8, "1", this.params, this.user);
/* 106 */     if (!"".equals(Util.null2String(this.params.get("name")))) {
/* 107 */       stringBuffer.append(" and t1.name like '%" + Util.null2String(this.params.get("name")) + "%'  ");
/*     */     }
/*     */     
/* 110 */     if (!"".equals(Util.null2String(this.params.get("datatype")))) {
/* 111 */       stringBuffer.append(" and t1.datatype= " + Util.null2String(this.params.get("datatype")) + " ");
/*     */     }
/*     */     
/* 114 */     if (!"".equals(Util.null2String(this.params.get("warehouseid")))) {
/* 115 */       stringBuffer.append(" and t1.warehouse= " + Util.null2String(this.params.get("warehouseid")) + " ");
/*     */     }
/*     */     
/* 118 */     if (!"".equals(Util.null2String(this.params.get("capitalgroupid"))) && !"0".equals(Util.null2String(this.params.get("capitalgroupid")))) {
/* 119 */       stringBuffer.append(" and (t1.capitalgroupid in(" + Util.null2String(this.params.get("capitalgroupid")) + ") ");
/* 120 */       String[] arrayOfString = Util.null2String(this.params.get("capitalgroupid")).split(",");
/* 121 */       for (String str : arrayOfString) {
/* 122 */         stringBuffer.append(" or t1.capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + str + "|%') ");
/*     */       }
/* 124 */       stringBuffer.append(")");
/*     */     } 
/*     */     
/* 127 */     if (stringBuffer.length() > 5) {
/* 128 */       str7 = str7 + stringBuffer.toString();
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 133 */     String str9 = " t1.id,t1.mark,t1.name,t1.capitalnum,t1.capitalspec,t1.sptcount,t1.stateid,t1.stateid as stateid1,t1.departmentid,t1.resourceid,t1.blongsubcompany,t1.blongdepartment,t1.capitalimageid ";
/* 134 */     String str10 = "";
/* 135 */     String str11 = "";
/*     */     
/* 137 */     commonShareManager.setAliasTableName("t2");
/* 138 */     str10 = " from CptCapital  t1  ";
/* 139 */     str11 = str7 + commonShareManager.getAssortmentSqlWhere(this.user);
/* 140 */     String str12 = "t1.id";
/*     */     
/* 142 */     if (str2.equals("1")) {
/* 143 */       str10 = " from CptCapital  t1 ";
/* 144 */       str11 = str7;
/*     */     } else {
/* 146 */       commonShareManager.setAliasTableName("t2");
/* 147 */       str10 = " from CptCapital  t1  ";
/* 148 */       str11 = str7 + commonShareManager.getAssortmentSqlWhere(this.user);
/* 149 */       if (str3.equals("mycpt") || str3.equals("mycptdetail")) str11 = str11 + " and t1.stateid <> 1 ";
/*     */     
/*     */     } 
/* 152 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 153 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 154 */     arrayList.add(new SplitTableColBean("capitalimageid", "com.api.cpt.mobile.util.CapitalTransUtil.getImgCapitalById", "", 0));
/* 155 */     arrayList.add(new SplitTableColBean("true", "name"));
/* 156 */     arrayList.add(new SplitTableColBean("true", "mark"));
/* 157 */     arrayList.add(new SplitTableColBean("capitalnum", "com.engine.cpt.util.CapitalTransMethod.getDesensAndDecryptData", "capitalnum", 0));
/* 158 */     arrayList.add(new SplitTableColBean("true", "capitalspec"));
/* 159 */     arrayList.add(new SplitTableColBean("sptcount", "com.api.cpt.mobile.util.CapitalTransUtil.getSptcountState", "" + this.user.getLanguage() + "", 0));
/* 160 */     arrayList.add(new SplitTableColBean("resourceid", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/* 161 */     arrayList.add(new SplitTableColBean("departmentid", "com.api.cpt.mobile.util.CapitalTransUtil.getDepartmentname", "", 0));
/* 162 */     arrayList.add(new SplitTableColBean("stateid", "com.api.cpt.mobile.util.CapitalTransUtil.getBrowserShowName", "243+ ", 0));
/* 163 */     arrayList.add(new SplitTableColBean("true", "stateid1"));
/*     */ 
/*     */     
/* 166 */     String str13 = "[    {        \"key\": \"col1\",        \"style\": {\"flex\": \"none\"},        \"configs\": [            {                \"key\": \"col1_row1\",                \"configs\": [                    {                       \"key\": \"capitalimageid\", \"isimg\": \"true\", \"style\": { \"borderRadius\": \"50%\", \"width\": 40, \"height\": 40 ,\"marginRight\": 8}                    }                ]            }        ]    },    {        \"key\": \"col2\",        \"configs\": [            {                \"key\": \"col2_row1\",                \"configs\": [                    {                       \"key\": \"name\", \"style\": {\"marginRight\": 10, \"fontSize\": 10}                    },                    {                       \"key\": \"stateid\", \"style\": {\"marginRight\": 10, \"fontSize\": 10}                    }                ],            },            {                \"key\": \"col2_row2\",                \"configs\": [                    {                       \"key\": \"mark\", \"style\": {\"marginRight\": 10, \"fontSize\": 10}                    },                    {                       \"key\": \"capitalspec\", \"style\": {\"marginRight\": 10, \"fontSize\": 10}                    }                ]            }        ],    }]";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 210 */     String str14 = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"show2\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 252 */     String str15 = "<div class='template-wrapper'>\t<div class='template-avator-box'>\t\t<img class='template-avatar' src='#{capitalimageid}'>\t</div>\t<div class='template-main-box'>\t\t<p class='template-row template-title'>\t\t\t<span>#{name}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(714, this.user.getLanguage()) + "：#{mark}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(904, this.user.getLanguage()) + "：#{capitalspec}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1363, this.user.getLanguage()) + "：#{sptcount}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1331, this.user.getLanguage()) + "：#{capitalnum}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(1508, this.user.getLanguage()) + "：#{resourceidspan}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(21030, this.user.getLanguage()) + "：#{departmentidspan}</span>\t\t</p>\t</div>\t<div class='template-dot-box'>\t\t<div class='template-dot-#{stateid1}'>#{stateid}</div>\t</div></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 264 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str15);
/*     */     
/* 266 */     SplitTableBean splitTableBean = new SplitTableBean(str9, str10, str11, str12, "t1.id", arrayList);
/*     */     try {
/* 268 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */       
/* 270 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 271 */       splitTableBean.setSqlsortway("ASC");
/* 272 */     } catch (Exception exception) {
/* 273 */       exception.printStackTrace();
/*     */     } 
/* 275 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 276 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetCapitalListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */