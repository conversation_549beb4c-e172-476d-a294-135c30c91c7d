/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.cpt.util.ConditionUtil;
/*     */ import com.api.cpt.util.FieldInfoManager;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetListSearchConditionCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetListSearchConditionCmd(Map<String, Object> paramMap, User paramUser) {
/*  30 */     this.user = paramUser;
/*  31 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  36 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  42 */     String str1 = Util.null2String(this.params.get("isdata"));
/*  43 */     String str2 = ",";
/*  44 */     if ("1".equals(str1)) {
/*  45 */       str2 = str2 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,StockInDate,SelectDate,Invoice,alertnum,";
/*     */     }
/*     */     
/*  48 */     FieldInfoManager fieldInfoManager = new FieldInfoManager();
/*  49 */     Map map = fieldInfoManager.getSearchDefFieldInfo();
/*     */     
/*  51 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  52 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  54 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  55 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*  56 */     List list = (List)map.get(ConditionUtil.COMMON_CONDITION);
/*  57 */     for (Map map1 : list) {
/*  58 */       Map<String, String> map2 = map1;
/*  59 */       String str = (String)map2.get("fieldname");
/*  60 */       map2.put("fieldValue", Util.null2String(this.params.get(str)));
/*     */       
/*  62 */       if ("sptcount".equals(str)) {
/*     */         
/*  64 */         ConditionType conditionType = ConditionType.SELECT;
/*  65 */         ArrayList<SearchConditionOption> arrayList5 = new ArrayList();
/*     */         
/*  67 */         SearchConditionOption searchConditionOption = new SearchConditionOption();
/*  68 */         searchConditionOption.setKey("");
/*  69 */         searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(332, Util.getIntValue(this.user.getLanguage())));
/*  70 */         searchConditionOption.setSelected(false);
/*  71 */         arrayList5.add(searchConditionOption);
/*     */         
/*  73 */         searchConditionOption = new SearchConditionOption();
/*  74 */         searchConditionOption.setKey("1");
/*  75 */         searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(1363, this.user.getLanguage()));
/*  76 */         searchConditionOption.setSelected(false);
/*  77 */         arrayList5.add(searchConditionOption);
/*     */         
/*  79 */         searchConditionOption = new SearchConditionOption();
/*  80 */         searchConditionOption.setKey("0");
/*  81 */         searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(125023, this.user.getLanguage()));
/*  82 */         searchConditionOption.setSelected(false);
/*  83 */         arrayList5.add(searchConditionOption);
/*  84 */         SearchConditionItem searchConditionItem = conditionFactory.createCondition(conditionType, Util.getIntValue(map2.get("fieldlabel")), str, arrayList5);
/*  85 */         arrayList1.add(searchConditionItem);
/*     */         continue;
/*     */       } 
/*  88 */       if (str2.indexOf("," + str + ",") == -1) {
/*  89 */         arrayList1.add(ConditionUtil.getCondition(map2, this.user));
/*     */       }
/*     */     } 
/*  92 */     hashMap2.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.COMMON_CONDITION, this.user.getLanguage()));
/*  93 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  94 */     hashMap2.put("items", arrayList1);
/*  95 */     arrayList.add(hashMap2);
/*     */     
/*  97 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  98 */     ArrayList<Object> arrayList2 = new ArrayList();
/*  99 */     list = (List)map.get(ConditionUtil.MANAGER_CONDITION);
/* 100 */     for (Map map1 : list) {
/* 101 */       Map<String, String> map2 = map1;
/* 102 */       String str = (String)map2.get("fieldname");
/* 103 */       map2.put("fieldValue", Util.null2String(this.params.get(str)));
/* 104 */       if ("resourceid".equals(str)) {
/* 105 */         if ("1".equals(str1)) {
/* 106 */           map2.put("fieldlabel", "1507");
/*     */         } else {
/* 108 */           map2.put("fieldlabel", "1508");
/*     */         } 
/*     */       }
/* 111 */       if (str2.indexOf("," + str + ",") == -1) {
/* 112 */         arrayList2.add(ConditionUtil.getCondition(map2, this.user));
/*     */       }
/* 114 */       if (!"1".equals(str1) && "blongsubcompany".equalsIgnoreCase(str)) {
/* 115 */         map2 = new HashMap<>();
/* 116 */         map2.put("fieldname", "datatype");
/* 117 */         map2.put("fieldid", "9999");
/* 118 */         map2.put("fielddbtype", "int");
/* 119 */         map2.put("fieldhtmltype", "3");
/* 120 */         map2.put("fieldtype", "179");
/* 121 */         map2.put("fieldlabel", "1509");
/* 122 */         map2.put("issystem", "1");
/* 123 */         arrayList2.add(ConditionUtil.getCondition(map2, this.user));
/*     */       } 
/*     */     } 
/* 126 */     hashMap3.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.MANAGER_CONDITION, this.user.getLanguage()));
/* 127 */     hashMap3.put("defaultshow", Boolean.valueOf(false));
/* 128 */     hashMap3.put("items", arrayList2);
/* 129 */     arrayList.add(hashMap3);
/*     */     
/* 131 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 132 */     ArrayList<Object> arrayList3 = new ArrayList();
/* 133 */     list = (List)map.get(ConditionUtil.APPLY_CONDITION);
/* 134 */     for (Map map1 : list) {
/* 135 */       Map<String, String> map2 = map1;
/* 136 */       String str = (String)map2.get("fieldname");
/* 137 */       map2.put("fieldValue", Util.null2String(this.params.get(str)));
/* 138 */       if (str2.indexOf("," + str + ",") == -1) {
/* 139 */         arrayList3.add(ConditionUtil.getCondition(map2, this.user));
/*     */       }
/*     */     } 
/* 142 */     hashMap4.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.APPLY_CONDITION, this.user.getLanguage()));
/* 143 */     hashMap4.put("defaultshow", Boolean.valueOf(false));
/* 144 */     hashMap4.put("items", arrayList3);
/* 145 */     arrayList.add(hashMap4);
/*     */     
/* 147 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 148 */     ArrayList<Object> arrayList4 = new ArrayList();
/* 149 */     list = (List)map.get(ConditionUtil.OTHER_CONDITION);
/* 150 */     for (Map map1 : list) {
/* 151 */       Map<String, String> map2 = map1;
/* 152 */       String str = (String)map2.get("fieldname");
/* 153 */       map2.put("fieldValue", Util.null2String(this.params.get(str)));
/* 154 */       arrayList4.add(ConditionUtil.getCondition(map2, this.user));
/*     */     } 
/*     */     
/* 157 */     hashMap5.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.OTHER_CONDITION, this.user.getLanguage()));
/* 158 */     hashMap5.put("defaultshow", Boolean.valueOf(false));
/* 159 */     hashMap5.put("items", arrayList4);
/* 160 */     arrayList.add(hashMap5);
/*     */     
/* 162 */     hashMap1.put("conditioninfo", arrayList);
/* 163 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetListSearchConditionCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */