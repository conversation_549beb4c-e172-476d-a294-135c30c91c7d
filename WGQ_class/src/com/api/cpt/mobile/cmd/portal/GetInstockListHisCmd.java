/*    */ package com.api.cpt.mobile.cmd.portal;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*    */ import com.cloudstore.dev.api.util.Util_MobileData;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.cpt.util.CommonShareManager;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetInstockListHisCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetInstockListHisCmd(Map<String, Object> paramMap, User paramUser) {
/* 30 */     this.user = paramUser;
/* 31 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 36 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 43 */     if (!HrmUserVarify.checkUserRight("CptCapital:InStockCheck", this.user)) {
/* 44 */       hashMap.put("isright", Boolean.valueOf(false));
/* 45 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 48 */     String str1 = "m.ischecked,m.checkerid,m.id,m.id as tmpid,m.buyerid as buyerimg,m.buyerid as buyername,m.stockindate,m.id as selectdate";
/* 49 */     String str2 = " where m.ischecked=1 and m.checkerid in(" + (new CommonShareManager()).getContainsSubuserids("" + this.user.getUID()) + ")  ";
/* 50 */     String str3 = " CptStockInMain m ";
/* 51 */     String str4 = " m.id desc";
/* 52 */     String str5 = " m.id";
/*    */     
/* 54 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 55 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 56 */     arrayList.add(new SplitTableColBean("true", "tmpid"));
/* 57 */     arrayList.add(new SplitTableColBean("buyerimg", "com.api.cpt.mobile.util.CapitalTransUtil.getMessagerUrls", "", 0));
/* 58 */     arrayList.add(new SplitTableColBean("buyername", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/* 59 */     arrayList.add(new SplitTableColBean("true", "stockindate"));
/* 60 */     arrayList.add(new SplitTableColBean("selectdate", "com.api.cpt.mobile.util.CapitalTransUtil.getInstockSelectDate", "", 0));
/* 61 */     arrayList.add(new SplitTableColBean("ischecked", "com.api.cpt.mobile.util.CapitalTransUtil.getInstockState", "" + this.user.getLanguage(), 0));
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 74 */     String str6 = "<div class='template-wrapper'>\t<div class='template-avator-box'>\t\t<img class='template-avatar' src='#{buyerimg}'>\t</div>\t<div class='template-main-box'>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(83587, this.user.getLanguage()) + "：#{tmpid}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(913, this.user.getLanguage()) + "：#{buyernamespan}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(16914, this.user.getLanguage()) + "：#{stockindate}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(753, this.user.getLanguage()) + "：#{selectdate}</span>\t\t</p>\t</div>\t<div class='template-dot-box'>\t\t<div class='template-dot-1'}>#{ischecked}</div>\t</div>";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 81 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str6);
/*    */ 
/*    */     
/* 84 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str3, str2, str4, str5, arrayList);
/*    */     try {
/* 86 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*    */       
/* 88 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 89 */       splitTableBean.setSqlsortway("desc");
/* 90 */     } catch (Exception exception) {
/* 91 */       exception.printStackTrace();
/*    */     } 
/* 93 */     hashMap.put("isright", Boolean.valueOf(true));
/* 94 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 95 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetInstockListHisCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */