/*    */ package com.api.cpt.mobile.cmd.portal;
/*    */ 
/*    */ import com.api.browser.bean.Checkboxpopedom;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*    */ import com.cloudstore.dev.api.util.Util_MobileData;
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.cpt.util.CommonShareManager;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetInstockListCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public GetInstockListCmd(Map<String, Object> paramMap, User paramUser) {
/* 31 */     this.user = paramUser;
/* 32 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 37 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 44 */     if (!HrmUserVarify.checkUserRight("CptCapital:InStockCheck", this.user)) {
/* 45 */       hashMap.put("isright", Boolean.valueOf(false));
/* 46 */       return (Map)hashMap;
/*    */     } 
/* 48 */     CommonShareManager commonShareManager = new CommonShareManager();
/*    */     
/* 50 */     String str1 = " m.id,m.id as tmpid,m.checkerid,m.buyerid as buyerimg,m.buyerid as buyername,m.stockindate,m.id as selectdate ";
/* 51 */     String str2 = " where m.ischecked = 0 and m.checkerid in(" + commonShareManager.getContainsSubuserids("" + this.user.getUID()) + ")  ";
/* 52 */     String str3 = " CptStockInMain m ";
/* 53 */     String str4 = " m.id ";
/* 54 */     String str5 = " m.id";
/*    */     
/* 56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 57 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 58 */     arrayList.add(new SplitTableColBean("true", "tmpid"));
/* 59 */     arrayList.add(new SplitTableColBean("buyerimg", "com.api.cpt.mobile.util.CapitalTransUtil.getMessagerUrls", "", 0));
/* 60 */     arrayList.add(new SplitTableColBean("buyername", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/* 61 */     arrayList.add(new SplitTableColBean("true", "stockindate"));
/* 62 */     arrayList.add(new SplitTableColBean("selectdate", "com.api.cpt.mobile.util.CapitalTransUtil.getInstockSelectDate", "", 0));
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 75 */     String str6 = "<div class='template-wrapper'>\t<div class='template-avator-box'>\t\t<img class='template-avatar' src='#{buyerimg}'>\t</div>\t<div class='template-main-box'>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(83587, this.user.getLanguage()) + "：#{tmpid}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(913, this.user.getLanguage()) + "：#{buyernamespan}</span>\t\t</p>\t\t<p class='template-row'>\t\t\t<span>" + SystemEnv.getHtmlLabelName(16914, this.user.getLanguage()) + "：#{stockindate}</span>\t\t\t<span>" + SystemEnv.getHtmlLabelName(753, this.user.getLanguage()) + "：#{selectdate}</span>\t\t</p>\t</div></div>";
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 80 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str6);
/*    */ 
/*    */     
/* 83 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str3, str2, str4, str5, arrayList);
/*    */     try {
/* 85 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*    */       
/* 87 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 88 */       splitTableBean.setTableType("checkbox");
/* 89 */       splitTableBean.setCheckboxpopedom(new Checkboxpopedom("checkbox", "", "com.api.cpt.mobile.util.CapitalTransUtil.getTrue"));
/* 90 */       splitTableBean.setSqlsortway("desc");
/* 91 */     } catch (Exception exception) {
/* 92 */       exception.printStackTrace();
/*    */     } 
/* 94 */     hashMap.put("isright", Boolean.valueOf(true));
/* 95 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 96 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetInstockListCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */