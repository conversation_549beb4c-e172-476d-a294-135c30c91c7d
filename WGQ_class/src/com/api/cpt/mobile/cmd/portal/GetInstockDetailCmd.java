/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.api.cpt.util.CptFormItemUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.util.CapitalTransUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetInstockDetailCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public GetInstockDetailCmd(Map<String, Object> paramMap, User paramUser) {
/*  29 */     this.user = paramUser;
/*  30 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  35 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  42 */     String str1 = Util.null2String(this.params.get("instockid"));
/*  43 */     String str2 = Util.null2String(this.params.get("viewtype"));
/*     */     
/*  45 */     RecordSet recordSet1 = new RecordSet();
/*  46 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  48 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  49 */     boolean bool = capitalTransMethod.IsWareHouseOpen();
/*     */     
/*  51 */     String str3 = "";
/*  52 */     String str4 = "";
/*  53 */     recordSet1.executeQuery("select ischecked,stockindept from CptStockInMain where id=?", new Object[] { str1 });
/*  54 */     if (recordSet1.next()) {
/*  55 */       str3 = Util.null2String(recordSet1.getString("ischecked"));
/*  56 */       str4 = Util.null2String(recordSet1.getString("stockindept"));
/*     */     } 
/*     */     
/*  59 */     String str5 = "";
/*  60 */     String str6 = "";
/*  61 */     String str7 = "";
/*  62 */     String str8 = "";
/*  63 */     String str9 = "";
/*  64 */     String str10 = "";
/*  65 */     String str11 = "";
/*  66 */     if (!HrmUserVarify.checkUserRight("CptCapital:InStockCheck", this.user)) {
/*  67 */       hashMap.put("isright", Boolean.valueOf(false));
/*  68 */       return (Map)hashMap;
/*     */     } 
/*  70 */     hashMap.put("isright", Boolean.valueOf(true));
/*     */ 
/*     */     
/*  73 */     if ("batch".equals(str2)) {
/*  74 */       if (Util.null2String(str1).startsWith(",")) {
/*  75 */         str1 = str1.substring(1, str1.length());
/*     */       }
/*  77 */       if (Util.null2String(str1).endsWith(",")) {
/*  78 */         str1 = str1.substring(0, str1.length() - 1);
/*     */       }
/*  80 */       String str = "select m.id as mainid,d.* from CptStockInDetail d,CptStockInMain m where m.id=d.cptstockinid and m.id in(" + str1 + ") and m.ischecked=0 order by m.id,d.id ";
/*  81 */       recordSet1.execute(str);
/*     */     } else {
/*  83 */       recordSet1.executeProc("CptStockInMain_SelectByid", str1);
/*  84 */       if (recordSet1.next()) {
/*  85 */         str5 = Util.null2String(recordSet1.getString("buyerid"));
/*  86 */         str7 = Util.null2String(recordSet1.getString("checkerid"));
/*  87 */         str8 = Util.null2String(recordSet1.getString("stockindate"));
/*     */       } 
/*  89 */       recordSet2.executeProc("CptStockInDetail_SByStockid", str1);
/*  90 */       if (recordSet2.next()) {
/*  91 */         str6 = Util.null2String(recordSet2.getString("selectDate"));
/*  92 */         str9 = Util.null2String(recordSet2.getString("contractno"));
/*  93 */         str10 = Util.null2String(recordSet2.getString("customerid"));
/*  94 */         str11 = Util.null2String(recordSet2.getString("warehouse"));
/*     */       } 
/*     */     } 
/*     */     
/*  98 */     if ("batch".equals(str2)) {
/*     */       
/* 100 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 101 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 102 */       ArrayList<Map<String, String>> arrayList = new ArrayList();
/*     */       
/* 104 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("cptdept_to", SystemEnv.getHtmlLabelName(15301, Util.getIntValue(this.user.getLanguage())), "4", "", 3, "", null, null);
/* 105 */       map.put("viewAttr", "3");
/* 106 */       arrayList.add(map);
/* 107 */       hashMap1.put("title", SystemEnv.getHtmlLabelName(24893, Util.getIntValue(this.user.getLanguage())));
/* 108 */       hashMap1.put("items", arrayList);
/* 109 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 110 */       arrayList1.add(hashMap1);
/*     */       
/* 112 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 113 */       CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 114 */       while (recordSet1.next()) {
/* 115 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 116 */         hashMap2.put("detailid", recordSet1.getString("id"));
/* 117 */         hashMap2.put("mainid", recordSet1.getString("mainid"));
/* 118 */         hashMap2.put("capitalid", recordSet1.getString("cpttype"));
/* 119 */         hashMap2.put("capitalidspan", capitalComInfo.getCapitalname(recordSet1.getString("cpttype")));
/* 120 */         hashMap2.put("capitalspec", recordSet1.getString("capitalspec"));
/* 121 */         hashMap2.put("price", recordSet1.getString("price"));
/* 122 */         hashMap2.put("capitalnum", recordSet1.getString("plannumber"));
/* 123 */         hashMap2.put("innumber", recordSet1.getString("plannumber"));
/* 124 */         hashMap2.put("invoice", recordSet1.getString("Invoice"));
/* 125 */         hashMap2.put("location", recordSet1.getString("location"));
/* 126 */         hashMap2.put("customerid", recordSet1.getString("customerid"));
/* 127 */         hashMap2.put("contractno", recordSet1.getString("contractno"));
/* 128 */         arrayList2.add(hashMap2);
/*     */       } 
/* 130 */       hashMap.put("fieldinfo", arrayList1);
/* 131 */       hashMap.put("datas", arrayList2);
/*     */     } else {
/*     */       
/* 134 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 135 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 136 */       ArrayList<Map<String, String>> arrayList = new ArrayList();
/*     */       
/* 138 */       Map<String, String> map = CptFormItemUtil.getFormItemForBrowser("buyerid", SystemEnv.getHtmlLabelName(913, Util.getIntValue(this.user.getLanguage())), "1", str5, 1, "", null, null);
/* 139 */       map.put("viewAttr", "1");
/* 140 */       arrayList.add(map);
/* 141 */       map = CptFormItemUtil.getFormItemForDate("stockindate_gz", SystemEnv.getHtmlLabelName(16914, Util.getIntValue(this.user.getLanguage())), str6, 1);
/* 142 */       arrayList.add(map);
/* 143 */       map = CptFormItemUtil.getFormItemForBrowser("checkerid", SystemEnv.getHtmlLabelName(901, Util.getIntValue(this.user.getLanguage())), "1", str7, 1, "", null, null);
/* 144 */       map.put("viewAttr", "1");
/* 145 */       arrayList.add(map);
/* 146 */       map = CptFormItemUtil.getFormItemForDate("stockindate", SystemEnv.getHtmlLabelName(753, Util.getIntValue(this.user.getLanguage())), str8, 1);
/* 147 */       arrayList.add(map);
/* 148 */       map = CptFormItemUtil.getFormItemForBrowser("cptdept_to", SystemEnv.getHtmlLabelName(15301, Util.getIntValue(this.user.getLanguage())), "4", str3.equals("1") ? str4 : "", 3, "", null, null);
/* 149 */       arrayList.add(map);
/* 150 */       map = CptFormItemUtil.getFormItemForInput("contactno", SystemEnv.getHtmlLabelName(21282, Util.getIntValue(this.user.getLanguage())), str9, 200, 1);
/* 151 */       arrayList.add(map);
/* 152 */       map = CptFormItemUtil.getFormItemForBrowser("customerid", SystemEnv.getHtmlLabelName(138, Util.getIntValue(this.user.getLanguage())), "7", str10, 1, "", null, null);
/* 153 */       map.put("viewAttr", "1");
/* 154 */       arrayList.add(map);
/* 155 */       if (bool) {
/* 156 */         map = CptFormItemUtil.getFormItemForBrowser("warehouse", SystemEnv.getHtmlLabelName(711, Util.getIntValue(this.user.getLanguage())), "320", str11, 1, "", null, null);
/* 157 */         map.put("viewAttr", "1");
/* 158 */         arrayList.add(map);
/*     */       } 
/* 160 */       if (str3.equals("1")) {
/* 161 */         CapitalTransUtil capitalTransUtil = new CapitalTransUtil();
/* 162 */         String str = capitalTransUtil.getCapitalInstockState(str3, "" + this.user.getLanguage());
/* 163 */         map = CptFormItemUtil.getFormItemForInput("checkstate", SystemEnv.getHtmlLabelName(602, Util.getIntValue(this.user.getLanguage())), str, 200, 1);
/* 164 */         map.put("viewAttr", "1");
/* 165 */         arrayList.add(map);
/*     */       } 
/* 167 */       hashMap1.put("title", SystemEnv.getHtmlLabelName(24893, Util.getIntValue(this.user.getLanguage())));
/* 168 */       hashMap1.put("items", arrayList);
/* 169 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 170 */       arrayList1.add(hashMap1);
/*     */       
/* 172 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 173 */       CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 174 */       recordSet2.beforFirst();
/* 175 */       while (recordSet2.next()) {
/* 176 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 177 */         hashMap2.put("detailid", recordSet2.getString("id"));
/* 178 */         hashMap2.put("mainid", recordSet1.getString("id"));
/*     */         
/* 180 */         hashMap2.put("capitalid", recordSet2.getString("cpttype"));
/* 181 */         hashMap2.put("capitalidspan", capitalComInfo.getCapitalname(recordSet2.getString("cpttype")));
/* 182 */         hashMap2.put("capitalspec", recordSet2.getString("capitalspec"));
/* 183 */         hashMap2.put("price", recordSet2.getString("price"));
/* 184 */         hashMap2.put("capitalnum", recordSet2.getString("plannumber"));
/* 185 */         hashMap2.put("innumber", recordSet2.getString("plannumber"));
/* 186 */         hashMap2.put("invoice", recordSet2.getString("Invoice"));
/* 187 */         hashMap2.put("location", recordSet2.getString("location"));
/* 188 */         hashMap2.put("customerid", recordSet2.getString("customerid"));
/* 189 */         hashMap2.put("contractno", recordSet2.getString("contractno"));
/* 190 */         arrayList2.add(hashMap2);
/*     */       } 
/* 192 */       hashMap.put("fieldinfo", arrayList1);
/* 193 */       hashMap.put("datas", arrayList2);
/* 194 */       hashMap.put("isChecked", str3);
/*     */     } 
/* 196 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/GetInstockDetailCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */