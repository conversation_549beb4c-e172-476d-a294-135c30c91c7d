/*     */ package com.api.cpt.mobile.cmd.portal;
/*     */ 
/*     */ import com.api.cpt.util.CptCommonUtil;
/*     */ import com.engine.common.biz.AbstractCommonCommand;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.core.interceptor.CommandContext;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.math.BigDecimal;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.job.CptLowInventoryRemindJob;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CptFieldManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.system.code.CodeBuild;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.msg.PoppupRemindInfoUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoInstockCapitalCmd
/*     */   extends AbstractCommonCommand<Map<String, Object>>
/*     */ {
/*     */   public DoInstockCapitalCmd(Map<String, Object> paramMap, User paramUser) {
/*  35 */     this.user = paramUser;
/*  36 */     this.params = paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public BizLogContext getLogContext() {
/*  41 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/*  46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  48 */     String str1 = Util.null2String(this.params.get("instockid"));
/*  49 */     String str2 = Util.null2String(this.params.get("cptdept_to"));
/*  50 */     if ("".equals(str2)) {
/*  51 */       hashMap.put("flag", Boolean.valueOf(false));
/*  52 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(503023, this.user.getLanguage()));
/*  53 */       return (Map)hashMap;
/*     */     } 
/*  55 */     boolean bool1 = true;
/*  56 */     String str3 = "";
/*  57 */     String str4 = "";
/*  58 */     String str5 = "";
/*  59 */     String str6 = "";
/*  60 */     String str7 = "";
/*     */     
/*  62 */     RecordSet recordSet1 = new RecordSet();
/*  63 */     RecordSet recordSet2 = new RecordSet();
/*  64 */     RecordSet recordSet3 = new RecordSet();
/*  65 */     RecordSet recordSet4 = new RecordSet();
/*  66 */     RecordSet recordSet5 = new RecordSet();
/*     */     
/*  68 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  69 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  70 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  71 */     CodeBuild codeBuild = new CodeBuild();
/*  72 */     CptShare cptShare = new CptShare();
/*  73 */     PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/*  74 */     CptLowInventoryRemindJob cptLowInventoryRemindJob = new CptLowInventoryRemindJob();
/*  75 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  76 */     boolean bool = capitalTransMethod.IsWareHouseOpen();
/*  77 */     CptCommonUtil cptCommonUtil = new CptCommonUtil();
/*     */     
/*  79 */     boolean bool2 = false;
/*  80 */     recordSet2.executeQuery("select * from cptcode where isuse=2", new Object[0]);
/*  81 */     if (recordSet2.next()) {
/*  82 */       bool2 = true;
/*     */     }
/*     */     
/*  85 */     String[] arrayOfString = str1.split(","); byte b;
/*  86 */     for (b = 0; b < arrayOfString.length; b++) {
/*  87 */       String str8 = arrayOfString[b];
/*  88 */       String str9 = "select m.id as mainid,d.* from CptStockInDetail d,CptStockInMain m where m.id=d.cptstockinid and m.id = " + str8 + " and m.ischecked=0 order by m.id,d.id ";
/*  89 */       recordSet3.execute(str9);
/*  90 */       recordSet1.execute("select stockindate,buyerid,checkerid,supplierid,warehouse from CptStockInMain where id=" + str8);
/*  91 */       if (recordSet1.next()) {
/*  92 */         str3 = Util.null2String(recordSet1.getString("buyerid"));
/*  93 */         str6 = Util.null2String(recordSet1.getString("stockindate"));
/*  94 */         str5 = Util.null2String(recordSet1.getString("checkerid"));
/*  95 */         str4 = Util.null2String(recordSet1.getString("supplierid"));
/*  96 */         str7 = Util.null2String(recordSet1.getString("warehouse"));
/*     */       } 
/*     */       
/*  99 */       char c = Util.getSeparator();
/* 100 */       String str10 = "";
/* 101 */       str10 = str8;
/* 102 */       str10 = str10 + c + str6;
/* 103 */       str10 = str10 + c + str3;
/* 104 */       str10 = str10 + c + str4;
/* 105 */       str10 = str10 + c + str5;
/* 106 */       str10 = str10 + c + "";
/* 107 */       str10 = str10 + c + "1";
/* 108 */       recordSet1.executeProc("CptStockInMain_Update", str10);
/* 109 */       recordSet1.execute("update CptStockInMain set stockindate='" + str6 + "',stockindept='" + str2 + "' where id=" + str8);
/*     */       
/* 111 */       String str11 = "";
/* 112 */       String str12 = "";
/* 113 */       String str13 = "";
/* 114 */       String str14 = "";
/* 115 */       String str15 = "";
/* 116 */       String str16 = "";
/* 117 */       String str17 = "";
/* 118 */       String str18 = "";
/* 119 */       String str19 = "";
/* 120 */       String str20 = "";
/* 121 */       String str21 = "";
/* 122 */       String str22 = "";
/* 123 */       String str23 = "";
/* 124 */       byte b1 = 0;
/* 125 */       int i = 0;
/* 126 */       while (recordSet3.next()) {
/* 127 */         str11 = Util.null2String(recordSet3.getString("id"));
/* 128 */         str12 = Util.null2String(recordSet3.getString("mainid"));
/* 129 */         if (!str12.equals(str8))
/* 130 */           continue;  str13 = Util.null2String(recordSet3.getString("cpttype"));
/* 131 */         str14 = Util.null2String(recordSet3.getString("plannumber"));
/* 132 */         str15 = Util.null2String(recordSet3.getString("price"));
/* 133 */         str16 = Util.null2String(recordSet3.getString("customerid"));
/* 134 */         str17 = Util.null2String(recordSet3.getString("capitalspec"));
/* 135 */         str18 = Util.null2String(recordSet3.getString("location"));
/* 136 */         str19 = Util.null2String(recordSet3.getString("Invoice"));
/* 137 */         str21 = Util.null2String(recordSet3.getString("contractno"));
/* 138 */         String str48 = Util.null2String(recordSet3.getString("selectDate"));
/* 139 */         str22 = Util.null2String(recordSet3.getString("capitalname"));
/* 140 */         str23 = Util.null2String(recordSet3.getString("capitalmark"));
/* 141 */         String str49 = str23;
/*     */         
/* 143 */         recordSet4.executeProc("CptCapital_SelectByID", str13);
/* 144 */         if (recordSet4.next()) {
/* 145 */           str20 = recordSet4.getString("sptcount");
/*     */         }
/*     */         
/* 148 */         if (str20.equals("1")) {
/* 149 */           str10 = str11;
/* 150 */           recordSet1.executeProc("CptStockInDetail_Delete", str10);
/* 151 */           for (b1 = 1, i = (int)Util.getFloatValue(str14, 0.0F); b1 <= i; b1++) {
/* 152 */             str10 = str8;
/* 153 */             str10 = str10 + c + str13;
/* 154 */             str10 = str10 + c + "1";
/* 155 */             str10 = str10 + c + "1";
/* 156 */             str10 = str10 + c + str15;
/* 157 */             str10 = str10 + c + str16;
/* 158 */             str10 = str10 + c + str48;
/* 159 */             str10 = str10 + c + str17;
/* 160 */             str10 = str10 + c + str18;
/* 161 */             str10 = str10 + c + str19;
/* 162 */             recordSet1.executeProc("CptStockInDetail_Insert", str10);
/* 163 */             if (recordSet1.next()) {
/* 164 */               String str = Util.null2String(recordSet1.getString(1));
/* 165 */               if (!str.equals("") && !str.equals("0")) {
/* 166 */                 recordSet1.execute("update CptStockInDetail set contractno = '" + str21 + "' where id = " + str);
/*     */               }
/* 168 */               if (i > 1 && bool2) {
/* 169 */                 str49 = str23 + "_" + b1;
/*     */               }
/* 171 */               if (!str.equals("") && !str.equals("0"))
/* 172 */                 recordSet1.executeUpdate("update CptStockInDetail set contractno = ?,capitalname = ?,capitalmark = ?,warehouse = ? where id = ?", new Object[] { str21, str22, str49, str7, str }); 
/*     */             } 
/*     */           } 
/*     */           continue;
/*     */         } 
/* 177 */         str10 = str11;
/* 178 */         str10 = str10 + c + str14;
/* 179 */         recordSet1.executeProc("CptStockInDetail_Update", str10);
/*     */       } 
/*     */       
/* 182 */       String str24 = "0";
/* 183 */       String str25 = "1";
/* 184 */       String str26 = "";
/* 185 */       String str27 = "";
/* 186 */       String str28 = "";
/* 187 */       String str29 = "";
/* 188 */       String str30 = "";
/* 189 */       String str31 = "";
/* 190 */       String str32 = "";
/* 191 */       String str33 = "";
/* 192 */       String str34 = "";
/* 193 */       String str35 = "";
/* 194 */       String str36 = "";
/* 195 */       String str37 = "";
/* 196 */       String str38 = "";
/* 197 */       String str39 = "";
/* 198 */       String str40 = "";
/* 199 */       String str41 = "";
/* 200 */       String str42 = "";
/* 201 */       String str43 = "";
/* 202 */       String str44 = "";
/* 203 */       String str45 = DateHelper.getCurrentDate();
/* 204 */       String str46 = DateHelper.getCurrentTime();
/* 205 */       String str47 = "" + this.user.getUID();
/*     */       
/* 207 */       ArrayList<String> arrayList = new ArrayList();
/* 208 */       recordSet1.execute("select lower(fieldname),fielddbtype from cptDefineField where (issystem != 1 or issystem is null) and isopen =1");
/* 209 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 210 */       while (recordSet1.next()) {
/* 211 */         hashMap1.put(recordSet1.getString(1).trim(), recordSet1.getString(2).trim());
/*     */       }
/*     */       
/* 214 */       recordSet1.executeProc("CptStockInDetail_SByStockid", str8);
/*     */       
/* 216 */       while (recordSet1.next()) {
/*     */         
/* 218 */         str26 = recordSet1.getString("cpttype");
/* 219 */         str34 = recordSet1.getString("innumber");
/* 220 */         double d = Util.getDoubleValue(str34);
/*     */         
/* 222 */         BigDecimal bigDecimal = new BigDecimal(recordSet1.getString("price"));
/* 223 */         str16 = recordSet1.getString("customerid");
/* 224 */         str17 = recordSet1.getString("capitalspec");
/* 225 */         str18 = recordSet1.getString("location");
/* 226 */         str19 = recordSet1.getString("Invoice");
/* 227 */         str40 = recordSet1.getString("selectDate");
/* 228 */         str21 = recordSet1.getString("contractno");
/* 229 */         str38 = bigDecimal.multiply(new BigDecimal(str34)).toString();
/* 230 */         str22 = recordSet1.getString("capitalname");
/* 231 */         str23 = recordSet1.getString("capitalmark");
/* 232 */         str7 = recordSet1.getString("warehouse");
/*     */         
/* 234 */         recordSet4.executeProc("CptCapital_SelectByID", str26);
/* 235 */         if (recordSet4.next()) {
/* 236 */           str27 = recordSet4.getString("mark");
/* 237 */           str36 = recordSet4.getString("sptcount");
/* 238 */           str39 = recordSet4.getString("capitalgroupid");
/* 239 */           str42 = recordSet4.getString("capitaltypeid");
/* 240 */           if (str16.equals("") || str16.equals("0")) {
/* 241 */             str16 = recordSet2.getString("customerid");
/*     */           }
/*     */         } 
/*     */         
/* 245 */         String str48 = str39;
/* 246 */         byte b2 = 10;
/* 247 */         while (b2 > 0 && 
/* 248 */           !capitalAssortmentComInfo.getSupAssortmentId(str48).equals("0")) {
/*     */ 
/*     */           
/* 251 */           str48 = capitalAssortmentComInfo.getSupAssortmentId(str48);
/* 252 */           b2--;
/*     */         } 
/*     */         
/* 255 */         if (bigDecimal.compareTo(new BigDecimal("2000")) == 1) {
/* 256 */           str41 = "1";
/*     */         } else {
/* 258 */           str41 = "2";
/*     */         } 
/* 260 */         str43 = departmentComInfo.getSubcompanyid1(str2);
/* 261 */         str44 = str2;
/*     */ 
/*     */         
/* 264 */         if (bool2 && !"".equals(str23)) {
/* 265 */           str27 = str23;
/*     */         } else {
/* 267 */           if (str36.equals("1")) {
/* 268 */             str27 = codeBuild.getCurrentCapitalCode(departmentComInfo.getSubcompanyid1(str2), str2, str39, str42, str40, str6, str26);
/*     */           }
/* 270 */           if (bool && !str7.equalsIgnoreCase("")) {
/* 271 */             recordSet2.executeQuery("select mark from CptCapital where datatype=? and blongdepartment=? and warehouse=?", new Object[] { str26, str2, str7 });
/*     */           } else {
/* 273 */             recordSet2.executeProc("CptCapital_SelectByDataType", str26 + c + str2);
/*     */           } 
/* 275 */           if (!str36.equals("1") && recordSet2.next()) {
/* 276 */             str27 = recordSet2.getString("mark");
/* 277 */           } else if (!str36.equals("1")) {
/* 278 */             str27 = codeBuild.getCurrentCapitalCode(departmentComInfo.getSubcompanyid1(str2), str2, str39, str42, str40, str6, str26);
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 284 */         str35 = str6;
/* 285 */         str35 = str35 + c + str44;
/* 286 */         str35 = str35 + c + str5;
/* 287 */         str35 = str35 + c + str5;
/* 288 */         str35 = str35 + c + str34;
/* 289 */         str35 = str35 + c + str18;
/* 290 */         str35 = str35 + c + str24;
/* 291 */         str35 = str35 + c + "";
/* 292 */         str35 = str35 + c + str38;
/* 293 */         str35 = str35 + c + str25;
/* 294 */         str35 = str35 + c + "";
/* 295 */         str35 = str35 + c + str27;
/* 296 */         str35 = str35 + c + str26;
/* 297 */         str35 = str35 + c + str29;
/* 298 */         str35 = str35 + c + str30;
/* 299 */         str35 = str35 + c + str31;
/* 300 */         str35 = str35 + c + str32;
/* 301 */         str35 = str35 + c + str33;
/* 302 */         str35 = str35 + c + str47;
/* 303 */         str35 = str35 + c + str45;
/* 304 */         str35 = str35 + c + str46;
/*     */         
/* 306 */         if (str36.equals("1")) {
/*     */ 
/*     */           
/* 309 */           str10 = str26;
/* 310 */           str10 = str10 + c + str16;
/* 311 */           str10 = str10 + c + "" + bigDecimal;
/* 312 */           str10 = str10 + c + str17;
/* 313 */           str10 = str10 + c + str18;
/* 314 */           str10 = str10 + c + str19;
/* 315 */           str10 = str10 + c + str6;
/* 316 */           str10 = str10 + c + str40;
/*     */           
/* 318 */           recordSet4.executeProc("CptCapital_Duplicate", str10);
/* 319 */           recordSet4.next();
/* 320 */           str37 = recordSet4.getString(1);
/*     */           
/* 322 */           str35 = str37 + c + str35;
/* 323 */           str35 = str35 + c + "" + bigDecimal;
/* 324 */           str35 = str35 + c + str16;
/* 325 */           str35 = str35 + c + str41;
/* 326 */           str35 = str35 + c + str28;
/*     */           
/* 328 */           recordSet4.executeProc("CptUseLogInStock_Insert", str35);
/* 329 */           recordSet4.execute("update cptcapital set createrid = '" + str3 + "',olddepartment = " + str2 + ",departmentid = null ,blongsubcompany='" + str43 + "', blongdepartment='" + str44 + "',contractno='" + str21 + "',warehouse='" + str7 + "' where id = " + str37);
/*     */ 
/*     */           
/* 332 */           if (!"".equals(str22)) {
/* 333 */             recordSet2.executeUpdate("update cptcapital set name=? where id=?", new Object[] { str22, str37 });
/*     */           }
/*     */ 
/*     */           
/* 337 */           CptFieldManager cptFieldManager1 = new CptFieldManager();
/* 338 */           cptFieldManager1.updateCptDefinedField(str26, str37);
/*     */           
/* 340 */           String str67 = "select * from cptcapitalparts where cptid = " + str26;
/* 341 */           recordSet5.execute(str67);
/* 342 */           while (recordSet5.next()) {
/* 343 */             str67 = "insert into cptcapitalparts (cptid,partsname,partsspec,partssum,partsweight,partssize) select " + str37 + ",partsname,partsspec,partssum,partsweight,partssize from cptcapitalparts where id = " + recordSet5.getString("id");
/* 344 */             recordSet1.execute(str67);
/*     */           } 
/* 346 */           str67 = "select * from cptcapitalequipment where cptid = " + str26;
/* 347 */           recordSet5.execute(str67);
/* 348 */           while (recordSet5.next()) {
/* 349 */             str67 = "insert into cptcapitalequipment (cptid,equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage) select " + str37 + ",equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage from cptcapitalequipment where id = " + recordSet5.getString("id");
/* 350 */             recordSet1.execute(str67);
/*     */           } 
/*     */ 
/*     */           
/* 354 */           String str68 = "";
/* 355 */           String str69 = "";
/* 356 */           String str70 = "";
/* 357 */           String str71 = "";
/* 358 */           String str72 = "";
/* 359 */           String str73 = "";
/* 360 */           String str74 = "";
/* 361 */           String str75 = "";
/* 362 */           String str76 = "";
/* 363 */           String str77 = "";
/* 364 */           String str78 = "";
/* 365 */           String str79 = "";
/* 366 */           String str80 = "";
/* 367 */           String str81 = "";
/*     */           
/* 369 */           recordSet4.execute("select * from CptAssortmentShare where assortmentid=" + str48);
/* 370 */           while (recordSet4.next()) {
/* 371 */             str69 = recordSet4.getString("sharetype");
/* 372 */             str70 = recordSet4.getString("seclevel");
/* 373 */             str71 = recordSet4.getString("rolelevel");
/* 374 */             str72 = recordSet4.getString("sharelevel");
/* 375 */             str73 = recordSet4.getString("userid");
/* 376 */             str74 = recordSet4.getString("departmentid");
/* 377 */             str75 = recordSet4.getString("roleid");
/* 378 */             str76 = recordSet4.getString("foralluser");
/* 379 */             str77 = recordSet4.getString("subcompanyid");
/* 380 */             str78 = recordSet4.getString("seclevelMax");
/* 381 */             str79 = recordSet4.getString("jobtitleid");
/* 382 */             str80 = recordSet4.getString("joblevel");
/* 383 */             str81 = recordSet4.getString("scopeid");
/*     */             
/* 385 */             str68 = str37;
/* 386 */             str68 = str68 + c + str69;
/* 387 */             str68 = str68 + c + str70;
/* 388 */             str68 = str68 + c + str71;
/* 389 */             str68 = str68 + c + str72;
/* 390 */             str68 = str68 + c + str73;
/* 391 */             str68 = str68 + c + str74;
/* 392 */             str68 = str68 + c + str75;
/* 393 */             str68 = str68 + c + str76;
/* 394 */             str68 = str68 + c + str77;
/* 395 */             str68 = str68 + c + str48;
/*     */             
/* 397 */             recordSet3.executeProc("CptShareInfo_Insert_dft", str68);
/* 398 */             recordSet3.execute("select max(id) from CptCapitalShareInfo ");
/* 399 */             recordSet3.next();
/* 400 */             int j = Util.getIntValue(recordSet3.getString(1), 0);
/* 401 */             if (j > 0) {
/* 402 */               recordSet3.execute("update CptCapitalShareInfo set seclevelMax='" + str78 + "',jobtitleid='" + str79 + "',joblevel='" + str80 + "',scopeid='" + str81 + "' where id=" + j);
/*     */             }
/*     */           } 
/*     */           
/*     */           try {
/* 407 */             cptShare.setCptShareByCpt(str37);
/*     */             
/* 409 */             if (bool) {
/* 410 */               cptShare.freshenCptShareByWareHouse(str37);
/*     */             }
/* 412 */           } catch (Exception exception) {
/* 413 */             bool1 = false;
/* 414 */             exception.printStackTrace();
/*     */           } 
/*     */           
/* 417 */           String str82 = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) values(" + str37 + ",1,1," + str5 + ",1) ";
/* 418 */           recordSet3.execute(str82);
/*     */ 
/*     */           
/* 421 */           recordSet3.execute("select name from cptcapital where id=" + str37);
/* 422 */           recordSet3.next();
/* 423 */           String str83 = recordSet3.getString("name");
/* 424 */           String str84 = Util.null2String(cptCommonUtil.getPinYin(str83, 7).get("pinyin"));
/* 425 */           recordSet3.execute("update cptcapital set ecology_pinyin_search='" + str84 + "' where id=" + str37);
/*     */           
/* 427 */           arrayList.add(str37);
/*     */           continue;
/*     */         } 
/* 430 */         recordSet4.executeProc("CptCapital_SelectByDataType", str26 + c + str2);
/* 431 */         if (recordSet4.next()) {
/*     */ 
/*     */           
/* 434 */           str37 = recordSet4.getString("id");
/* 435 */           BigDecimal bigDecimal1 = new BigDecimal(recordSet4.getString("CptCapital", "startprice", true, true));
/* 436 */           BigDecimal bigDecimal2 = new BigDecimal(recordSet4.getString("capitalnum"));
/* 437 */           bigDecimal = bigDecimal.multiply(new BigDecimal(str34));
/* 438 */           bigDecimal = bigDecimal.add(bigDecimal1.multiply(bigDecimal2));
/* 439 */           bigDecimal = bigDecimal.divide(bigDecimal2.add(new BigDecimal(str34)), 2, 0);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 462 */           recordSet4.executeUpdate("INSERT INTO CptUseLog(capitalid,usedate,usedeptid,useresourceid,usecount,useaddress,usestatus,fee) values(?,?,?,?,?,?,?,?)", new Object[] {
/* 463 */                 Integer.valueOf(Util.getIntValue(str37)), str6, Integer.valueOf(Util.getIntValue(str44, 0)), Integer.valueOf(Util.getIntValue(str3, 0)), Float.valueOf(Util.getFloatValue(str34, 0.0F)), str18, str25, Float.valueOf(Util.getFloatValue(str38))
/*     */               });
/*     */           
/* 466 */           BigDecimal bigDecimal3 = bigDecimal2.add(new BigDecimal(str34));
/* 467 */           recordSet4.execute("update cptcapital set createrid = '" + str3 + "',departmentid = null,startprice = '" + bigDecimal + "',capitalnum= '" + bigDecimal3 + "' where id = " + str37);
/*     */           
/* 469 */           recordSet3.execute("select * from CptCapitalShareInfo where sharetype='1' and userid =" + this.user.getUID() + " and relateditemid = " + str37);
/* 470 */           if (!recordSet3.next()) {
/* 471 */             String str = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) values(" + str37 + ",1,1," + str5 + ",1) ";
/* 472 */             recordSet3.execute(str);
/*     */           } 
/*     */           try {
/* 475 */             cptShare.setCptShareByCpt(str37);
/* 476 */           } catch (Exception exception) {
/* 477 */             bool1 = false;
/* 478 */             exception.printStackTrace();
/*     */           } 
/*     */           
/*     */           continue;
/*     */         } 
/*     */         
/* 484 */         str10 = str26;
/* 485 */         str10 = str10 + c + str16;
/* 486 */         str10 = str10 + c + "" + bigDecimal;
/* 487 */         str10 = str10 + c + str17;
/* 488 */         str10 = str10 + c + str18;
/* 489 */         str10 = str10 + c + str19;
/* 490 */         str10 = str10 + c + str6;
/* 491 */         str10 = str10 + c + str40;
/*     */         
/* 493 */         recordSet4.executeProc("CptCapital_Duplicate", str10);
/* 494 */         recordSet4.next();
/* 495 */         str37 = recordSet4.getString(1);
/*     */         
/* 497 */         str35 = str37 + c + str35;
/* 498 */         str35 = str35 + c + "" + bigDecimal;
/* 499 */         str35 = str35 + c + str16;
/* 500 */         str35 = str35 + c + str41;
/* 501 */         str35 = str35 + c + str28;
/*     */ 
/*     */         
/* 504 */         recordSet4.executeProc("CptUseLogInStock_Insert", str35);
/* 505 */         recordSet4.execute("update cptcapital set  createrid = '" + str3 + "',olddepartment = " + str2 + ",departmentid = null,blongsubcompany='" + str43 + "', blongdepartment='" + str44 + "',contractno='" + str21 + "' ,capitalnum='" + d + "',warehouse='" + str7 + "'  where id = " + str37);
/*     */ 
/*     */         
/* 508 */         if (!"".equals(str22)) {
/* 509 */           recordSet2.executeUpdate("update cptcapital set name=? where id=?", new Object[] { str22, str37 });
/*     */         }
/*     */ 
/*     */         
/* 513 */         CptFieldManager cptFieldManager = new CptFieldManager();
/* 514 */         cptFieldManager.updateCptDefinedField(str26, str37);
/*     */         
/* 516 */         String str49 = "select * from cptcapitalparts where cptid = " + str26;
/* 517 */         recordSet5.execute(str49);
/* 518 */         while (recordSet5.next()) {
/* 519 */           str49 = "insert into cptcapitalparts (cptid,partsname,partsspec,partssum,partsweight,partssize) select " + str37 + ",partsname,partsspec,partssum,partsweight,partssize from cptcapitalparts where id = " + recordSet5.getString("id");
/* 520 */           recordSet1.execute(str49);
/*     */         } 
/* 522 */         str49 = "select * from cptcapitalequipment where cptid = " + str26;
/* 523 */         recordSet5.execute(str49);
/* 524 */         while (recordSet5.next()) {
/* 525 */           str49 = "insert into cptcapitalequipment (cptid,equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage) select " + str37 + ",equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage from cptcapitalequipment where id = " + recordSet5.getString("id");
/* 526 */           recordSet1.execute(str49);
/*     */         } 
/*     */ 
/*     */         
/* 530 */         String str50 = "";
/* 531 */         String str51 = "";
/* 532 */         String str52 = "";
/* 533 */         String str53 = "";
/* 534 */         String str54 = "";
/* 535 */         String str55 = "";
/* 536 */         String str56 = "";
/* 537 */         String str57 = "";
/* 538 */         String str58 = "";
/* 539 */         String str59 = "";
/* 540 */         String str60 = "";
/* 541 */         String str61 = "";
/* 542 */         String str62 = "";
/* 543 */         String str63 = "";
/*     */         
/* 545 */         recordSet4.execute("select * from CptAssortmentShare where assortmentid=" + str48);
/* 546 */         while (recordSet4.next()) {
/* 547 */           str51 = recordSet4.getString("sharetype");
/* 548 */           str52 = recordSet4.getString("seclevel");
/* 549 */           str53 = recordSet4.getString("rolelevel");
/* 550 */           str54 = recordSet4.getString("sharelevel");
/* 551 */           str55 = recordSet4.getString("userid");
/* 552 */           str56 = recordSet4.getString("departmentid");
/* 553 */           str57 = recordSet4.getString("roleid");
/* 554 */           str58 = recordSet4.getString("foralluser");
/* 555 */           str59 = recordSet4.getString("subcompanyid");
/* 556 */           str60 = recordSet4.getString("seclevelMax");
/* 557 */           str61 = recordSet4.getString("jobtitleid");
/* 558 */           str62 = recordSet4.getString("joblevel");
/* 559 */           str63 = recordSet4.getString("scopeid");
/* 560 */           str50 = str37;
/* 561 */           str50 = str50 + c + str51;
/* 562 */           str50 = str50 + c + str52;
/* 563 */           str50 = str50 + c + str53;
/* 564 */           str50 = str50 + c + str54;
/* 565 */           str50 = str50 + c + str55;
/* 566 */           str50 = str50 + c + str56;
/* 567 */           str50 = str50 + c + str57;
/* 568 */           str50 = str50 + c + str58;
/* 569 */           str50 = str50 + c + str59;
/* 570 */           str50 = str50 + c + str48;
/*     */           
/* 572 */           recordSet3.executeProc("CptShareInfo_Insert_dft", str50);
/* 573 */           recordSet3.execute("select max(id) from CptCapitalShareInfo ");
/* 574 */           recordSet3.next();
/* 575 */           int j = Util.getIntValue(recordSet3.getString(1), 0);
/* 576 */           if (j > 0) {
/* 577 */             recordSet3.execute("update CptCapitalShareInfo set seclevelMax='" + str60 + "',jobtitleid='" + str61 + "',joblevel='" + str62 + "',scopeid='" + str63 + "' where id=" + j);
/*     */           }
/*     */         } 
/*     */         
/*     */         try {
/* 582 */           cptShare.setCptShareByCpt(str37);
/*     */           
/* 584 */           if (bool) {
/* 585 */             cptShare.freshenCptShareByWareHouse(str37);
/*     */           }
/* 587 */         } catch (Exception exception) {
/* 588 */           bool1 = false;
/* 589 */           exception.printStackTrace();
/*     */         } 
/*     */         
/* 592 */         String str64 = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) values(" + str37 + ",1,1," + str5 + ",1) ";
/* 593 */         recordSet3.execute(str64);
/*     */ 
/*     */         
/* 596 */         recordSet3.execute("select name from cptcapital where id=" + str37);
/* 597 */         recordSet3.next();
/* 598 */         String str65 = recordSet3.getString("name");
/* 599 */         String str66 = Util.null2String(cptCommonUtil.getPinYin(str65, 7).get("pinyin"));
/* 600 */         recordSet3.execute("update cptcapital set ecology_pinyin_search='" + str66 + "' where id=" + str37);
/*     */         
/* 602 */         arrayList.add(str37);
/*     */       } 
/*     */ 
/*     */       
/*     */       try {
/* 607 */         capitalComInfo.addCapitalCache(arrayList);
/* 608 */       } catch (Exception exception) {
/* 609 */         bool1 = false;
/* 610 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 613 */     for (b = 0; b < arrayOfString.length; b++) {
/* 614 */       String str8 = "" + Util.getIntValue(arrayOfString[b]);
/* 615 */       int i = this.user.getUID();
/* 616 */       String str9 = "select checkerid from CptStockInMain where id=" + str8;
/* 617 */       recordSet1.execute(str9);
/* 618 */       if (recordSet1.next()) {
/* 619 */         i = Util.getIntValue(recordSet1.getString("checkerid"));
/*     */       }
/* 621 */       poppupRemindInfoUtil.updatePoppupRemindInfo(i, 11, "0", Util.getIntValue(arrayOfString[b]));
/*     */     } 
/*     */     
/* 624 */     CptLowInventoryRemindJob.generateReminder();
/*     */     
/* 626 */     hashMap.put("flag", Boolean.valueOf(bool1));
/* 627 */     if (!bool1) {
/* 628 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(383881, this.user.getLanguage()));
/*     */     }
/* 630 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/DoInstockCapitalCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */