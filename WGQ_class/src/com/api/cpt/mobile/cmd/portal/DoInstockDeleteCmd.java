/*    */ package com.api.cpt.mobile.cmd.portal;
/*    */ 
/*    */ import com.engine.common.biz.AbstractCommonCommand;
/*    */ import com.engine.common.entity.BizLogContext;
/*    */ import com.engine.core.interceptor.CommandContext;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.msg.PoppupRemindInfoUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoInstockDeleteCmd
/*    */   extends AbstractCommonCommand<Map<String, Object>>
/*    */ {
/*    */   public DoInstockDeleteCmd(Map<String, Object> paramMap, User paramUser) {
/* 23 */     this.user = paramUser;
/* 24 */     this.params = paramMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public BizLogContext getLogContext() {
/* 29 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(CommandContext paramCommandContext) {
/* 34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 35 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 37 */     String str1 = Util.null2String(this.params.get("id"));
/* 38 */     String str2 = "update CptStockInMain set ischecked = -1 where id = " + str1;
/* 39 */     recordSet.execute(str2);
/* 40 */     int i = this.user.getUID();
/* 41 */     str2 = "select checkerid from CptStockInMain where id=" + str1;
/* 42 */     recordSet.execute(str2);
/* 43 */     if (recordSet.next()) {
/* 44 */       i = Util.getIntValue(recordSet.getString("checkerid"));
/*    */     }
/* 46 */     PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/* 47 */     poppupRemindInfoUtil.updatePoppupRemindInfo(i, 11, "0", Util.getIntValue(str1));
/* 48 */     hashMap.put("flag", Boolean.valueOf(true));
/* 49 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/mobile/cmd/portal/DoInstockDeleteCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */