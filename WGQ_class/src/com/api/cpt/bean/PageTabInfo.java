/*    */ package com.api.cpt.bean;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PageTabInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6505399999432831242L;
/*    */   private String groupid;
/*    */   private String title;
/*    */   private int viewcondition;
/*    */   private boolean showcount;
/*    */   private String color;
/*    */   
/*    */   public PageTabInfo() {}
/*    */   
/*    */   public PageTabInfo(String paramString1, String paramString2, int paramInt, boolean paramBoolean, String paramString3) {
/* 18 */     this.groupid = paramString1;
/* 19 */     this.title = paramString2;
/* 20 */     this.viewcondition = paramInt;
/* 21 */     this.showcount = paramBoolean;
/* 22 */     this.color = paramString3;
/*    */   }
/*    */   
/*    */   public String getGroupid() {
/* 26 */     return this.groupid;
/*    */   }
/*    */   public void setGroupid(String paramString) {
/* 29 */     this.groupid = paramString;
/*    */   }
/*    */   public String getTitle() {
/* 32 */     return this.title;
/*    */   }
/*    */   public void setTitle(String paramString) {
/* 35 */     this.title = paramString;
/*    */   }
/*    */   public int getViewcondition() {
/* 38 */     return this.viewcondition;
/*    */   }
/*    */   public void setViewcondition(int paramInt) {
/* 41 */     this.viewcondition = paramInt;
/*    */   }
/*    */   public boolean isShowcount() {
/* 44 */     return this.showcount;
/*    */   }
/*    */   public void setShowcount(boolean paramBoolean) {
/* 47 */     this.showcount = paramBoolean;
/*    */   }
/*    */   public String getColor() {
/* 50 */     return this.color;
/*    */   }
/*    */   public void setColor(String paramString) {
/* 53 */     this.color = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/bean/PageTabInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */