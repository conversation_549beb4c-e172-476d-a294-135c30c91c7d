/*    */ package com.api.cpt.bean;
/*    */ 
/*    */ import com.api.cpt.util.RightMenuType;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RightMenu
/*    */ {
/*    */   private String menuName;
/*    */   private String menuFun;
/*    */   private String menuIcon;
/*    */   private RightMenuType type;
/*    */   private String isTop;
/*    */   private String isControl;
/*    */   private String params;
/* 20 */   public static String TABLE_VIEW_PARAM = "tableViewType";
/* 21 */   public static String TABLE_VIEW_VIEW = "view";
/* 22 */   public static String TABLE_VIEW_LIST = "list";
/*    */   
/*    */   public RightMenu(int paramInt, RightMenuType paramRightMenuType, String paramString, boolean paramBoolean) {
/* 25 */     this.menuName = SystemEnv.getHtmlLabelNames(paramRightMenuType.getLabelids(), paramInt);
/* 26 */     this.menuFun = paramString;
/* 27 */     this.menuIcon = paramRightMenuType.getIcon();
/* 28 */     this.type = paramRightMenuType;
/* 29 */     this.isTop = paramBoolean ? "1" : "0";
/*    */   }
/*    */   
/*    */   public RightMenu(int paramInt, RightMenuType paramRightMenuType, String paramString, boolean paramBoolean1, boolean paramBoolean2) {
/* 33 */     this.menuName = SystemEnv.getHtmlLabelNames(paramRightMenuType.getLabelids(), paramInt);
/* 34 */     this.menuFun = paramString;
/* 35 */     this.menuIcon = paramRightMenuType.getIcon();
/* 36 */     this.type = paramRightMenuType;
/* 37 */     this.isControl = paramBoolean1 ? "1" : "0";
/* 38 */     this.isTop = paramBoolean2 ? "1" : "0";
/*    */   }
/*    */ 
/*    */   
/*    */   public RightMenu() {}
/*    */   
/*    */   public RightMenuType getType() {
/* 45 */     return this.type;
/*    */   }
/*    */   
/*    */   public void setType(RightMenuType paramRightMenuType) {
/* 49 */     this.type = paramRightMenuType;
/*    */   }
/*    */   
/*    */   public String getMenuName() {
/* 53 */     return this.menuName;
/*    */   }
/*    */   
/*    */   public void setMenuName(String paramString) {
/* 57 */     this.menuName = paramString;
/*    */   }
/*    */   
/*    */   public String getMenuFun() {
/* 61 */     return this.menuFun;
/*    */   }
/*    */   
/*    */   public void setMenuFun(String paramString) {
/* 65 */     this.menuFun = paramString;
/*    */   }
/*    */   
/*    */   public String getMenuIcon() {
/* 69 */     return this.menuIcon;
/*    */   }
/*    */   
/*    */   public void setMenuIcon(String paramString) {
/* 73 */     this.menuIcon = paramString;
/*    */   }
/*    */   
/*    */   public String getIsTop() {
/* 77 */     return this.isTop;
/*    */   }
/*    */   
/*    */   public void setIsTop(String paramString) {
/* 81 */     this.isTop = paramString;
/*    */   }
/*    */   
/*    */   public String getParams() {
/* 85 */     return this.params;
/*    */   }
/*    */   
/*    */   public void setParams(String paramString) {
/* 89 */     this.params = paramString;
/*    */   }
/*    */   
/*    */   public String getIsControl() {
/* 93 */     return this.isControl;
/*    */   }
/*    */   
/*    */   public void setIsControl(String paramString) {
/* 97 */     this.isControl = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/bean/RightMenu.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */