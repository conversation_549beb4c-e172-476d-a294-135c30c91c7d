/*    */ package com.api.cpt.bean;
/*    */ 
/*    */ import java.util.List;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SecTreeNode
/*    */ {
/*    */   public static final String NODE_PREV = "sec_";
/*    */   private String pid;
/*    */   private String domid;
/*    */   private String name;
/*    */   private boolean isopen = false;
/*    */   private boolean haschild = false;
/*    */   private String key;
/*    */   private boolean hasRight = false;
/*    */   private List<SecTreeNode> childs;
/*    */   
/*    */   public String getPid() {
/* 29 */     return this.pid;
/*    */   }
/*    */   
/*    */   public void setPid(String paramString) {
/* 33 */     this.pid = paramString;
/*    */   }
/*    */   
/*    */   public String getName() {
/* 37 */     return this.name;
/*    */   }
/*    */   
/*    */   public void setName(String paramString) {
/* 41 */     this.name = paramString;
/*    */   }
/*    */   
/*    */   public boolean isIsopen() {
/* 45 */     return this.isopen;
/*    */   }
/*    */   
/*    */   public void setIsopen(boolean paramBoolean) {
/* 49 */     this.isopen = paramBoolean;
/*    */   }
/*    */   
/*    */   public boolean isHaschild() {
/* 53 */     return this.haschild;
/*    */   }
/*    */   
/*    */   public void setHaschild(boolean paramBoolean) {
/* 57 */     this.haschild = paramBoolean;
/*    */   }
/*    */   
/*    */   public String getKey() {
/* 61 */     return this.key;
/*    */   }
/*    */   
/*    */   public void setKey(String paramString) {
/* 65 */     this.key = paramString;
/*    */   }
/*    */   
/*    */   public List<SecTreeNode> getChilds() {
/* 69 */     return this.childs;
/*    */   }
/*    */   
/*    */   public void setChilds(List<SecTreeNode> paramList) {
/* 73 */     this.childs = paramList;
/*    */   }
/*    */   
/*    */   public String getDomid() {
/* 77 */     return this.domid;
/*    */   }
/*    */   
/*    */   public void setDomid(String paramString) {
/* 81 */     this.domid = paramString;
/*    */   }
/*    */   
/*    */   public boolean isHasRight() {
/* 85 */     return this.hasRight;
/*    */   }
/*    */   
/*    */   public void setHasRight(boolean paramBoolean) {
/* 89 */     this.hasRight = paramBoolean;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/bean/SecTreeNode.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */