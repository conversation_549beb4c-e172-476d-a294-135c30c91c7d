/*     */ package com.api.cpt.util;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CptFieldComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptInventoryUtil
/*     */ {
/*     */   public String getInventorystate(String paramString1, String paramString2) {
/*  33 */     if ("".equals(Util.null2String(paramString1))) {
/*  34 */       return "";
/*     */     }
/*  36 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/*     */     
/*  38 */     int i = Util.getIntValue(paramString1, 0);
/*  39 */     int j = 7;
/*  40 */     if (arrayOfString.length > 0) {
/*  41 */       j = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/*  43 */     String str = "";
/*  44 */     switch (i) {
/*     */       case 0:
/*  46 */         str = SystemEnv.getHtmlLabelName(1979, j);
/*     */         break;
/*     */       case 1:
/*  49 */         str = SystemEnv.getHtmlLabelName(384644, j);
/*     */         break;
/*     */       case 2:
/*  52 */         str = SystemEnv.getHtmlLabelName(384645, j);
/*     */         break;
/*     */       case 3:
/*  55 */         str = SystemEnv.getHtmlLabelName(384646, j);
/*     */         break;
/*     */       case 4:
/*  58 */         str = "<span style='color:green;'>" + SystemEnv.getHtmlLabelName(225, j) + "</span>";
/*     */         break;
/*     */       case 5:
/*  61 */         str = "<span style='color:orange;'>" + SystemEnv.getHtmlLabelName(1398, j) + "</span>";
/*     */         break;
/*     */       case 6:
/*  64 */         str = "<span style='color:red;'>" + SystemEnv.getHtmlLabelName(1397, j) + "</span>";
/*     */         break;
/*     */       case 7:
/*  67 */         str = SystemEnv.getHtmlLabelName(384647, j);
/*     */         break;
/*     */       case 8:
/*  70 */         str = SystemEnv.getHtmlLabelName(384648, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  76 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getInventorystate_(String paramString1, String paramString2) {
/*  81 */     if ("".equals(Util.null2String(paramString1))) {
/*  82 */       return "";
/*     */     }
/*  84 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/*     */     
/*  86 */     int i = Util.getIntValue(paramString1, 0);
/*  87 */     int j = 7;
/*  88 */     if (arrayOfString.length > 0) {
/*  89 */       j = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/*  91 */     String str = "";
/*  92 */     switch (i) {
/*     */       case 0:
/*  94 */         str = SystemEnv.getHtmlLabelName(1979, j);
/*     */         break;
/*     */       case 1:
/*  97 */         str = SystemEnv.getHtmlLabelName(384644, j);
/*     */         break;
/*     */       case 2:
/* 100 */         str = SystemEnv.getHtmlLabelName(384645, j);
/*     */         break;
/*     */       case 3:
/* 103 */         str = SystemEnv.getHtmlLabelName(384646, j);
/*     */         break;
/*     */       case 4:
/* 106 */         str = SystemEnv.getHtmlLabelName(225, j);
/*     */         break;
/*     */       case 5:
/* 109 */         str = SystemEnv.getHtmlLabelName(1398, j);
/*     */         break;
/*     */       case 6:
/* 112 */         str = SystemEnv.getHtmlLabelName(1397, j);
/*     */         break;
/*     */       case 7:
/* 115 */         str = SystemEnv.getHtmlLabelName(384647, j);
/*     */         break;
/*     */       case 8:
/* 118 */         str = SystemEnv.getHtmlLabelName(384648, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 124 */     return str;
/*     */   }
/*     */   
/*     */   public String getInventorystate(String paramString, User paramUser) {
/* 128 */     if ("".equals(Util.null2String(paramString))) {
/* 129 */       return "";
/*     */     }
/* 131 */     int i = Util.getIntValue(paramString, 0);
/* 132 */     int j = paramUser.getLanguage();
/* 133 */     String str = "";
/* 134 */     switch (i) {
/*     */       case 0:
/* 136 */         str = SystemEnv.getHtmlLabelName(1979, j);
/*     */         break;
/*     */       case 1:
/* 139 */         str = SystemEnv.getHtmlLabelName(384644, j);
/*     */         break;
/*     */       case 2:
/* 142 */         str = SystemEnv.getHtmlLabelName(384645, j);
/*     */         break;
/*     */       case 3:
/* 145 */         str = SystemEnv.getHtmlLabelName(384646, j);
/*     */         break;
/*     */       case 4:
/* 148 */         str = SystemEnv.getHtmlLabelName(225, j);
/*     */         break;
/*     */       case 5:
/* 151 */         str = SystemEnv.getHtmlLabelName(1398, j);
/*     */         break;
/*     */       case 6:
/* 154 */         str = SystemEnv.getHtmlLabelName(1397, j);
/*     */         break;
/*     */       case 7:
/* 157 */         str = SystemEnv.getHtmlLabelName(384647, j);
/*     */         break;
/*     */       case 8:
/* 160 */         str = SystemEnv.getHtmlLabelName(384648, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 166 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCountuser(String paramString1, String paramString2) throws Exception {
/* 171 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*     */     
/* 173 */     return resourceComInfo.getLastname(paramString1);
/*     */   }
/*     */   
/*     */   public String getDepartment(String paramString1, String paramString2) throws Exception {
/* 177 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*     */     
/* 179 */     return departmentComInfo.getDepartmentname(paramString1);
/*     */   }
/*     */   
/*     */   public String getSubcompany(String paramString1, String paramString2) throws Exception {
/* 183 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */     
/* 185 */     return subCompanyComInfo.getSubCompanyname(paramString1);
/*     */   }
/*     */   
/*     */   public String getSptcount(String paramString1, String paramString2) {
/* 189 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 190 */     int i = 7;
/* 191 */     if (arrayOfString.length > 0) {
/* 192 */       i = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/* 194 */     if ("1".equals(paramString1))
/* 195 */       return SystemEnv.getHtmlLabelName(163, i); 
/* 196 */     if ("0".equals(paramString1)) {
/* 197 */       return SystemEnv.getHtmlLabelName(161, i);
/*     */     }
/* 199 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInventoryPlanfield(String paramString1, String paramString2) {
/* 210 */     RecordSet recordSet = new RecordSet();
/* 211 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 212 */     int i = 7;
/* 213 */     if (arrayOfString.length > 0) {
/* 214 */       i = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/* 216 */     String str1 = "";
/* 217 */     recordSet.execute("select warehouse_status from cpt_barcodesettings ");
/* 218 */     if (recordSet.next()) {
/* 219 */       str1 = Util.null2String(recordSet.getString("warehouse_status"));
/*     */     }
/* 221 */     CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 222 */     String str2 = cptFieldComInfo.getFieldType(paramString1);
/* 223 */     if ("320".equals(str2) && !"1".equals(str1)) {
/* 224 */       return "";
/*     */     }
/* 226 */     String str3 = cptFieldComInfo.getLabel(paramString1);
/* 227 */     return SystemEnv.getHtmlLabelNames(str3, i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInventoryMatrix(String paramString1, String paramString2) {
/* 238 */     RecordSet recordSet = new RecordSet();
/* 239 */     recordSet.executeQuery("select * from matrixinfo where id=? ", new Object[] { paramString1 });
/* 240 */     if (recordSet.next()) {
/* 241 */       return recordSet.getString("name");
/*     */     }
/* 243 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getOperates(String paramString1, String paramString2) throws Exception {
/* 256 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 258 */     if ("".equals(Util.null2String(paramString1)) || "".equals(Util.null2String(paramString2))) {
/* 259 */       ArrayList<String> arrayList1 = new ArrayList();
/* 260 */       for (byte b = 0; b < 20; b++) {
/* 261 */         arrayList1.add("false");
/*     */       }
/* 263 */       return arrayList1;
/*     */     } 
/*     */     
/* 266 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 267 */     int i = Util.getIntValue(arrayOfString[0]);
/* 268 */     String str1 = Util.null2String(arrayOfString[1]);
/* 269 */     int j = Util.getIntValue(arrayOfString[2]);
/* 270 */     String str2 = Util.null2String(arrayOfString[3]);
/* 271 */     int k = Util.getIntValue(arrayOfString[4]);
/* 272 */     String str3 = "";
/* 273 */     if (arrayOfString.length > 5) {
/* 274 */       str3 = Util.null2String(arrayOfString[5]);
/*     */     }
/*     */     
/* 277 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 279 */     if (!"".equals(Util.null2String(str3)) && !"null".equals(Util.null2String(str3))) {
/* 280 */       arrayList = Util.TokenizerString(str3, "_");
/*     */     } else {
/* 282 */       for (byte b = 0; b < k; b++) {
/* 283 */         arrayList.add("true");
/*     */       }
/* 285 */       if ("cptinventoryplan".equalsIgnoreCase(str2)) {
/* 286 */         recordSet.execute("select * from cpt_inventory_detail where planid=" + paramString1);
/* 287 */         if (recordSet.next()) {
/* 288 */           arrayList.set(3, "true");
/*     */         } else {
/* 290 */           arrayList.set(3, "false");
/*     */         } 
/*     */         
/* 293 */         recordSet.execute("select inventorystate from cpt_inventory_plan where id=" + paramString1);
/* 294 */         recordSet.next();
/* 295 */         String str = Util.null2String(recordSet.getString(1));
/*     */         
/* 297 */         if ("7".equals(str) || "8".equals(str)) {
/* 298 */           arrayList.set(0, "false");
/*     */         }
/* 300 */         if (!"0".equals(str)) {
/* 301 */           arrayList.set(3, "false");
/*     */         }
/*     */         
/* 304 */         if ("0".equals(str) || "7".equals(str) || "8".equals(str)) {
/* 305 */           arrayList.set(1, "true");
/*     */         } else {
/* 307 */           arrayList.set(1, "false");
/*     */         } 
/*     */         
/* 310 */         recordSet.execute("select * from cpt_inventory_plan where inventorystate not in (7,8)");
/* 311 */         recordSet.next();
/* 312 */         int m = recordSet.getCounts();
/* 313 */         if (m == 0) {
/* 314 */           arrayList.set(4, "true");
/*     */         } else {
/* 316 */           arrayList.set(4, "false");
/*     */         } 
/*     */         
/* 319 */         recordSet.execute("select * from cpt_inventory_plan where inventorystate not in (0,7,8) and id=" + paramString1);
/* 320 */         recordSet.next();
/* 321 */         int n = recordSet.getCounts();
/* 322 */         if (n == 0) {
/* 323 */           arrayList.set(5, "false");
/*     */         } else {
/* 325 */           arrayList.set(5, "true");
/*     */         } 
/* 327 */         arrayList.set(2, "true");
/*     */       } 
/*     */       
/* 330 */       if ("cptinventoryplanlist".equalsIgnoreCase(str2)) {
/*     */         
/* 332 */         recordSet.execute("select a.planliststate,a.totalnum,b.inventorystate from cpt_inventory_planlist a left join cpt_inventory_plan b on a.mainid=b.id where a.id=" + paramString1);
/* 333 */         recordSet.next();
/* 334 */         String str4 = Util.null2String(recordSet.getString("planliststate"));
/* 335 */         String str5 = Util.null2String(recordSet.getString("inventorystate"));
/* 336 */         double d = recordSet.getDouble("totalnum");
/* 337 */         if ("0".equals(str4)) {
/* 338 */           arrayList.set(0, "true");
/* 339 */           arrayList.set(1, "true");
/* 340 */           arrayList.set(2, "false");
/* 341 */           arrayList.set(3, "true");
/* 342 */           if (!"0".equals(str5) && d > 0.0D) {
/*     */             
/* 344 */             arrayList.set(4, "true");
/*     */           } else {
/* 346 */             arrayList.set(4, "false");
/*     */           } 
/*     */         } else {
/* 349 */           arrayList.set(0, "false");
/* 350 */           arrayList.set(1, "false");
/* 351 */           arrayList.set(2, "true");
/* 352 */           arrayList.set(3, "false");
/* 353 */           arrayList.set(4, "false");
/*     */         } 
/*     */       } 
/* 356 */       if ("cptinventoryplancptlist".equalsIgnoreCase(str2)) {
/* 357 */         recordSet.execute("select * from cpt_inventory_detail where id=" + paramString1);
/* 358 */         recordSet.next();
/* 359 */         String str = Util.null2String(recordSet.getString("detailstate"));
/* 360 */         if ("0".equals(str)) {
/* 361 */           arrayList.set(0, "true");
/*     */         } else {
/* 363 */           arrayList.set(0, "false");
/*     */         } 
/*     */       } 
/*     */       
/* 367 */       if ("cptinventoryplanmain".equalsIgnoreCase(str2)) {
/* 368 */         recordSet.execute("select * from cpt_inventory_detail where id=" + paramString1);
/* 369 */         recordSet.next();
/* 370 */         String str = Util.null2String(recordSet.getString("detailstate"));
/* 371 */         if ("0".equals(str)) {
/* 372 */           arrayList.set(0, "true");
/*     */         } else {
/* 374 */           arrayList.set(0, "false");
/*     */         } 
/*     */       } 
/*     */       
/* 378 */       if ("myinventoryplancptlist".equalsIgnoreCase(str2)) {
/* 379 */         recordSet.execute("select * from cpt_inventory_detail where id=" + paramString1);
/* 380 */         recordSet.next();
/* 381 */         String str4 = Util.null2String(recordSet.getString("mainid"));
/* 382 */         recordSet.execute("select * from cpt_inventory_planlist where id=" + str4);
/* 383 */         recordSet.next();
/* 384 */         String str5 = Util.null2String(recordSet.getString("planliststate"));
/* 385 */         if ("7".equals(str5) || "8".equals(str5)) {
/* 386 */           arrayList.set(0, "false");
/*     */         } else {
/* 388 */           arrayList.set(0, "true");
/*     */         } 
/*     */       } 
/*     */       
/* 392 */       if ("myinventoryplan".equalsIgnoreCase(str2)) {
/* 393 */         recordSet.execute("select * from cpt_inventory_planlist where id=" + paramString1);
/* 394 */         recordSet.next();
/* 395 */         String str = Util.null2String(recordSet.getString("planliststate"));
/* 396 */         if ("3".equals(str)) {
/* 397 */           arrayList.set(1, "true");
/*     */         } else {
/* 399 */           arrayList.set(1, "false");
/*     */         } 
/*     */       } 
/*     */     } 
/* 403 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int compare_date(String paramString1, String paramString2) {
/* 414 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*     */     try {
/* 416 */       Date date1 = simpleDateFormat.parse(paramString1);
/* 417 */       Date date2 = simpleDateFormat.parse(paramString2);
/* 418 */       if (date1.getTime() > date2.getTime())
/* 419 */         return 1; 
/* 420 */       if (date1.getTime() < date2.getTime()) {
/* 421 */         return -1;
/*     */       }
/* 423 */       return 0;
/*     */     }
/* 425 */     catch (Exception exception) {
/* 426 */       exception.printStackTrace();
/*     */       
/* 428 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelPlan(String paramString) {
/* 437 */     RecordSet recordSet = new RecordSet();
/* 438 */     recordSet.execute("select inventorystate from cpt_inventory_plan where id=" + paramString);
/* 439 */     recordSet.next();
/* 440 */     String str = Util.null2String(recordSet.getString(1));
/* 441 */     if ("0".equals(str) || "7".equals(str) || "8".equals(str)) {
/* 442 */       return "true";
/*     */     }
/* 444 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelPlanList(String paramString) {
/* 454 */     RecordSet recordSet = new RecordSet();
/* 455 */     recordSet.execute("select planliststate from cpt_inventory_planlist where id=" + paramString);
/* 456 */     recordSet.next();
/* 457 */     String str = Util.null2String(recordSet.getString(1));
/* 458 */     if ("0".equals(str) || "9".equals(str)) {
/* 459 */       return "true";
/*     */     }
/* 461 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelPlanListDetail(String paramString) {
/* 471 */     RecordSet recordSet = new RecordSet();
/* 472 */     recordSet.execute("select detailstate from cpt_inventory_detail where id=" + paramString);
/* 473 */     recordSet.next();
/* 474 */     String str = Util.null2String(recordSet.getString(1));
/* 475 */     if ("0".equals(str)) {
/* 476 */       return "true";
/*     */     }
/* 478 */     return "false";
/*     */   }
/*     */ 
/*     */   
/*     */   public String canDelCpt(String paramString) {
/* 483 */     RecordSet recordSet = new RecordSet();
/* 484 */     recordSet.execute("select detailstate from cpt_inventory_detail where id=" + paramString);
/* 485 */     recordSet.next();
/* 486 */     String str = recordSet.getString(1);
/* 487 */     if ("0".equals(str)) {
/* 488 */       return "true";
/*     */     }
/* 490 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCptName(String paramString1, String paramString2) {
/* 496 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/*     */     
/* 498 */     int i = 7;
/* 499 */     if (arrayOfString.length > 0) {
/* 500 */       i = Util.getIntValue(arrayOfString[0], 7);
/*     */     }
/*     */     
/* 503 */     RecordSet recordSet = new RecordSet();
/* 504 */     recordSet.execute("select cptid,cptname from cpt_inventory_detail where id=" + paramString1);
/* 505 */     recordSet.next();
/* 506 */     String str1 = recordSet.getString(1);
/* 507 */     String str2 = recordSet.getString(2);
/* 508 */     recordSet.execute("select name from cptcapital where id=" + str1);
/* 509 */     recordSet.next();
/* 510 */     String str3 = Util.null2String(recordSet.getString(1));
/* 511 */     if ("".equals(str3)) {
/* 512 */       str2 = "<span style='color:red;'>(" + SystemEnv.getHtmlLabelName(18967, i) + ")</span>" + str2;
/*     */     } else {
/* 514 */       str2 = str3;
/*     */     } 
/* 516 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String canInventoryCpt(String paramString) {
/* 522 */     RecordSet recordSet = new RecordSet();
/* 523 */     recordSet.execute("select detailstate from cpt_inventory_detail where id=" + paramString);
/* 524 */     recordSet.next();
/* 525 */     String str = recordSet.getString(1);
/* 526 */     if ("1".equals(str)) {
/* 527 */       return "true";
/*     */     }
/* 529 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetInventory(String paramString) {
/* 535 */     RecordSet recordSet = new RecordSet();
/* 536 */     recordSet.execute("select * from cpt_inventory_detail where mainid in (select id  from cpt_inventory_planlist where mainid in (select id from cpt_inventory_plan where inventorystate in(0,1,2,3))) and cptid = " + paramString);
/* 537 */     while (recordSet.next()) {
/* 538 */       int i = Util.getIntValue(recordSet.getString("detailstate"));
/* 539 */       String str1 = Util.getIntValues(recordSet.getString("mainid"));
/* 540 */       String str2 = Util.getIntValues(recordSet.getString("id"));
/* 541 */       RecordSet recordSet1 = new RecordSet();
/* 542 */       recordSet1.execute("select * from cpt_inventory_planlist where id = " + str1);
/* 543 */       recordSet1.next();
/* 544 */       double d1 = Util.getDoubleValue(recordSet1.getString("totalnum"));
/* 545 */       double d2 = Util.getDoubleValue(recordSet1.getString("uncountnum"));
/* 546 */       double d3 = Util.getDoubleValue(recordSet1.getString("countednum"));
/* 547 */       d1--;
/* 548 */       if (i == 0 || i == 1) {
/* 549 */         d2--;
/* 550 */         recordSet1.execute("update cpt_inventory_planlist set totalnum = " + d1 + ",uncountnum = " + d2 + " where id = " + str1);
/*     */         
/* 552 */         if (d2 == 0.0D && i != 0) {
/* 553 */           recordSet1.execute("update cpt_inventory_planlist set planliststate=3 where id=" + str1);
/*     */         }
/*     */       } else {
/* 556 */         d3--;
/* 557 */         recordSet1.execute("update cpt_inventory_planlist set totalnum = " + d1 + ",countednum = " + d3 + " where id = " + str1);
/*     */       } 
/*     */       
/* 560 */       recordSet1.execute("delete from cpt_inventory_detail where id = " + str2);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CptInventoryUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */