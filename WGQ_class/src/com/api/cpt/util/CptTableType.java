/*    */ package com.api.cpt.util;
/*    */ import java.util.UUID;
/*    */ 
/*    */ public enum CptTableType {
/*  5 */   SEARCH_CPT_TABLE(1, "89d20945-62d1-44ca-9555-fe0b6801199f", 10),
/*  6 */   SEARCH_CPTSHAREDSP_TABLE(2, "219d84f0-38f7-44be-855e-2396a0bc34e4", 10),
/*  7 */   SEARCH_CPTFLOW_TABLE(3, "86e84a30-2f12-4383-bdd6-3d1f98ed7576", 10),
/*  8 */   SEARCH_CPTMODIFY_TABLE(4, "f8462904-e035-4704-8e47-db6026c3aac8", 10),
/*  9 */   SEARCH_INSTOCKHIS_TABLE(5, "dad8b32e-2e90-46d8-98bb-af6d393be52a", 10),
/* 10 */   SEARCH_INSTOCKCHECK_TABLE(6, "9e93b25a-14b1-490c-a2b8-17c933d8585c", 10),
/* 11 */   SEARCH_INSTOCKCHECKHIS_TABLE(7, "45246bcf-2b34-4751-aea6-436cc23a1374", 10),
/* 12 */   SEARCH_CPTLOG_TABLE(8, "ac53cc94-b402-467a-812f-1701d09302f9", 10),
/* 13 */   SEARCH_TYPES_TABLE(9, "691ee257-fb92-4840-ba8f-e780d851657b", 10),
/* 14 */   SEARCH_ASSORTMENT_TABLE(10, "af2031d8-99f1-4a14-ae18-eb4d5944f546", 10),
/* 15 */   SEARCH_ASSORTMENT_SHARE_TABLE(11, "a0ab5d97-408c-4acd-b53e-16c8cbd04951", 10),
/* 16 */   SEARCH_ALERTNUM_TABLE(12, "41279732-5321-4172-a82a-7c65dada484b", 10),
/* 17 */   SEARCH_MODIFY_TABLE(13, "32279222-2321-4333-C12a-7c65dada424b", 10),
/* 18 */   SEARCH_PRINT_TABLE(14, "1ea4017f-1ebd-43e7-9485-20a169412f64", 10),
/* 19 */   CPT_GROUPREPORT(15, "582a3614-6491-4417-8614-065987874a99", 10),
/* 20 */   CPT_RESOURCEREPORT(16, "1148d48c-c844-4f9a-a142-9f46c5d6bd27", 10),
/* 21 */   CPT_DEPTREPORT(17, "9d190fc9-9f27-42a3-ac87-678713e0fdc4", 10),
/* 22 */   CPT_STATEREPORT(18, "4685b907-9283-4347-8793-5fd2513c406d", 10),
/* 23 */   CPT_CAPITALREPORT(19, "b0427315-c506-48df-9c50-4970d21d9238", 20),
/* 24 */   CPT_CPTFLOWREPORT(20, "7af5b2a5-8bd6-4389-8d5b-4be4a8d32d11", 20),
/* 25 */   SEARCH_SYSLOG_TABLE(21, "ac533394-b402-467a-812f-170233302f39", 10),
/* 26 */   SEARCH_CPT_MAINT_TABLE(22, "89d12345-6sd1-44ca-9555-fe033s21654f", 10),
/* 27 */   PLAN_TABLE(23, "f7f4d682-aa23-4050-9fb0-abe9d862e547", 10),
/* 28 */   PLAN_LIST_TABLE(24, "0b5f823c-37e4-4c6d-9bc0-f6c7640c67a0", 10),
/* 29 */   PLAN_LISTCPT_TABLE(25, "8bdc5829-eeda-4e17-9e0a-cabec59e5ede", 10),
/* 30 */   PLAN_LIST_MAINTABLE(26, "9fa1c568-e120-4e0e-ba03-48274463748f", 10),
/* 31 */   MY_PLANLIST_TABLE(27, "cb914c81-2e35-4128-bd76-d0613d38a824", 10),
/* 32 */   MyPLAN_LISTCPT_TABLE(28, "66793326-ae13-4107-b3c1-082a2be9ff51", 10),
/* 33 */   PLAN_ADDCPT_TABLE(29, "8bd25419-ccda-4c17-930a-caaasc59eddde", 10),
/* 34 */   CPT_TODBLOG(30, "034uks4d-f0h5-n95f-lasm-1poyfdi0olgu", 10),
/* 35 */   CPT_TODBLOG_DETAIL(31, "kd4xv1yh-9tl0-0cma-0fia-k6o7ilkzpahd", 10),
/* 36 */   CPT_MONITOR(32, "efbac170-1342-44ac-8c5e-97ff29c66964", 10),
/* 37 */   SEARCH_MYCPT_TABLE(33, "60f830fa-8969-4788-bcf2-8dbda9259d6a", 10),
/* 38 */   SEARCH_CPT_TABLE1(33, "eef92027-4835-41a6-b4bb-8c286ac8f4f8", 10),
/* 39 */   SEARCH_CPT_MAINT_TABLE1(34, "abf7c9d6-7200-42f8-a68e-fbd8b96d2ed3", 10),
/* 40 */   CPT_MAINT_WAREHOUSE(35, "3f99e718-0fe6-4574-b03a-9114efa130c2", 10),
/* 41 */   SEARCH_WAREHOUSE_SHARE_TABLE(36, "85726c0e-998d-4745-8050-76c4e510a219", 10),
/* 42 */   CPT_TOUPDATE_TABLE(37, "85726c0e-998d-4745-8050-76c4e520a219", 10);
/*    */   
/*    */   private String pageUid;
/*    */   private int urlType;
/*    */   private int pageSize;
/*    */   
/*    */   CptTableType(int paramInt1, String paramString1, int paramInt2) {
/* 49 */     this.pageUid = paramString1;
/* 50 */     this.urlType = paramInt1;
/* 51 */     this.pageSize = paramInt2;
/*    */   }
/*    */   
/*    */   public String getPageUid() {
/* 55 */     return this.pageUid;
/*    */   }
/*    */   
/*    */   public void setPageUid(String paramString) {
/* 59 */     this.pageUid = paramString;
/*    */   }
/*    */   
/*    */   public int getUrlType() {
/* 63 */     return this.urlType;
/*    */   }
/*    */   
/*    */   public void setUrlType(int paramInt) {
/* 67 */     this.urlType = paramInt;
/*    */   }
/*    */   
/*    */   public int getPageSize() {
/* 71 */     return this.pageSize;
/*    */   }
/*    */   
/*    */   public void setPageSize(int paramInt) {
/* 75 */     this.pageSize = paramInt;
/*    */   }
/*    */   
/*    */   public static void main(String[] paramArrayOfString) {
/* 79 */     for (byte b = 0; b < 5; b++)
/* 80 */       UUID uUID = UUID.randomUUID(); 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CptTableType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */