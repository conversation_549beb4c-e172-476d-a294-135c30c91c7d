/*      */ package com.api.cpt.util;
/*      */ 
/*      */ import com.api.browser.bean.BrowserBean;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SearchConditionOption;
/*      */ import com.api.browser.util.BrowserConfigComInfo;
/*      */ import com.api.browser.util.BrowserInitUtil;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.api.prj.util.ProjectTransMethod;
/*      */ import com.weaver.formmodel.util.DateHelper;
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.text.DecimalFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.Collections;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*      */ import weaver.cpt.util.CommonShareManager;
/*      */ import weaver.cpt.util.CptFieldComInfo;
/*      */ import weaver.filter.XssUtil;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.job.JobTitlesComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.hrm.roles.RolesComInfo;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.BrowserComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ConditionUtil
/*      */ {
/*   43 */   public static String COMMON_CONDITION = "32905";
/*   44 */   public static String MANAGER_CONDITION = "27858";
/*   45 */   public static String APPLY_CONDITION = "34081";
/*   46 */   public static String OTHER_CONDITION = "16169";
/*      */   
/*   48 */   public static String CUSTOM_SCOPE = "DocCustomFieldBySecCategory";
/*      */   
/*   50 */   public static String CUSTOM_KEY_PREV = "customField_";
/*   51 */   public static String CUSTOM_KEY_SELECT = "_select";
/*   52 */   public static String CUSTOM_KEY_OPT = "_opt";
/*   53 */   public static String CUSTOM_KEY_VALUE = "_value";
/*   54 */   public static String CUSTOM_KEY_FIELD = "_field";
/*   55 */   public static String CUSTOM_KEY_HTMLTYPE = "_htmltype";
/*   56 */   public static String CUSTOM_KEY_TYPE = "_type";
/*      */   
/*   58 */   public static String DATE_SELECT = "select";
/*   59 */   public static String DATE_FROM = "from";
/*   60 */   public static String DATE_TO = "to";
/*      */   
/*   62 */   public static String TAB_REQ_NAME = "viewcondition";
/*      */   
/*      */   public static final int TAB_ALL_VALUE = 0;
/*      */   
/*      */   public static final int TAB_TODAY_VALUE = 1;
/*      */   
/*      */   public static final int TAB_WEEK_VALUE = 2;
/*      */   
/*      */   public static final int TAB_MONTH_VALUE = 3;
/*      */   
/*      */   public static final int TAB_SESSION_VALUE = 4;
/*      */   public static final int TAB_YEAR_VALUE = 5;
/*      */   public static final int TAB_DEFAULT_VALUE = 1;
/*      */   public static final int TAB_REPLY_RANK = 0;
/*      */   public static final int TAB_READ_RANK = 1;
/*      */   public static final int TAB_DOWNLOAD_RANK = 2;
/*      */   public static final int TAB_SCORE_RANK = 3;
/*      */   public static final int TAB_DEFAULT_RANK = 0;
/*      */   public static final int TAB_SUBSCRIPTION_HISTORY = 0;
/*      */   public static final int TAB_SUBSCRIPTION_APPROVE = 1;
/*      */   public static final int TAB_SUBSCRIPTION_BACK = 2;
/*      */   public static final int TAB_SUBSCRIPTION_DEFAULT = 0;
/*      */   public static final int TAB_DATE_ALL = 0;
/*      */   public static final int TAB_DATE_DAY = 1;
/*      */   public static final int TAB_DATE_WEEK = 2;
/*      */   public static final int TAB_DATE_MONTH = 3;
/*      */   public static final int TAB_DATE_SESSION = 4;
/*      */   public static final int TAB_DATE_YEAR = 5;
/*      */   public static final int TAB_DATE_DEFAULT = 5;
/*      */   public static final String HIDE_INPUT = "13203768-adaa-42d6-91db-c61a9c4bc3bc";
/*      */   public static final String CUSTOM_FIELD = "5d5ad787-fc83-4bf8-8f18-71c18169e012";
/*      */   
/*      */   public static Object getCondition(Map paramMap, User paramUser) {
/*   95 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/*   96 */     SearchConditionItem searchConditionItem = null;
/*   97 */     RecordSet recordSet = new RecordSet();
/*      */     
/*   99 */     String str1 = Util.null2String(paramMap.get("fieldname"));
/*  100 */     String str2 = Util.null2String(paramMap.get("fieldid"));
/*  101 */     String str3 = Util.null2String(paramMap.get("fielddbtype"));
/*  102 */     String str4 = Util.null2String(paramMap.get("fieldhtmltype"));
/*  103 */     String str5 = Util.null2String(paramMap.get("fieldtype"));
/*  104 */     int i = Util.getIntValue(Util.null2String(paramMap.get("fieldlabel")));
/*  105 */     String str6 = Util.getIntValue(Util.null2String(paramMap.get("issystem")), 0) + "";
/*      */     
/*  107 */     String str7 = Util.null2String(paramMap.get("fieldValue"));
/*  108 */     String str8 = (1 == Util.getIntValue(str6)) ? str1 : ("field" + str2);
/*      */ 
/*      */     
/*  111 */     if (str1.equals("capitalgroupid")) {
/*  112 */       str5 = "317";
/*      */     }
/*      */     
/*  115 */     ConditionType conditionType = null;
/*  116 */     if ("1".equals(str4)) {
/*  117 */       conditionType = ConditionType.INPUT;
/*  118 */       if ("name".equals(str1)) {
/*  119 */         searchConditionItem = conditionFactory.createCondition(conditionType, i, str8, true);
/*      */       }
/*  121 */       else if ("2".equals(str5) || "3".equals(str5)) {
/*  122 */         conditionType = ConditionType.SCOPE;
/*  123 */         String[] arrayOfString = { str8, str8 + "_1" };
/*  124 */         searchConditionItem = conditionFactory.createCondition(conditionType, i, arrayOfString);
/*      */         
/*  126 */         int j = 0;
/*  127 */         if ("3".equals(str5)) {
/*      */           
/*  129 */           String str = str3.substring(str3.indexOf(",") + 1, str3.length() - 1);
/*  130 */           j = Integer.parseInt(str);
/*      */         } 
/*  132 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  133 */         hashMap.put("inputType", "form");
/*  134 */         hashMap.put("detailtype", Integer.valueOf(Util.getIntValue(str5)));
/*  135 */         hashMap.put("qfws", Integer.valueOf(j));
/*  136 */         hashMap.put("format", new HashMap<>());
/*  137 */         hashMap.put("style", new HashMap<>());
/*  138 */         searchConditionItem.setOtherParams(hashMap);
/*      */       } else {
/*  140 */         searchConditionItem = conditionFactory.createCondition(conditionType, i, str8);
/*      */       }
/*      */     
/*      */     }
/*  144 */     else if ("2".equals(str4)) {
/*  145 */       conditionType = ConditionType.TEXTAREA;
/*  146 */       searchConditionItem = conditionFactory.createCondition(conditionType, i, str8);
/*  147 */     } else if ("3".equals(str4)) {
/*      */       
/*  149 */       if ("1".equals(str5))
/*  150 */       { searchConditionItem = createHrmConditionWidthSelects(paramUser, i, str8); }
/*  151 */       else if ("4".equals(str5))
/*  152 */       { searchConditionItem = createDepartConditionWidthSelects(paramUser, i, str8); }
/*  153 */       else if ("164".equals(str5))
/*  154 */       { searchConditionItem = createSubcompanyConditionWidthSelects(paramUser, i, str8); }
/*  155 */       else if ("2".equals(str5))
/*  156 */       { conditionType = ConditionType.DATE;
/*  157 */         String[] arrayOfString = { str8 + "_select", str8 + "_start", str8 + "_end" };
/*  158 */         searchConditionItem = conditionFactory.createCondition(conditionType, i, arrayOfString);
/*  159 */         List<SearchConditionOption> list = getDateSelectFromTo(paramUser.getLanguage());
/*  160 */         searchConditionItem.setOptions(list); }
/*  161 */       else { if (str5.equals("19")) {
/*  162 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  163 */           hashMap.put("conditionType", "TIMERANGEPICKER");
/*  164 */           hashMap.put("label", SystemEnv.getHtmlLabelNames("" + i, paramUser.getLanguage()));
/*  165 */           hashMap.put("colSpan", Integer.valueOf(2));
/*  166 */           hashMap.put("labelcol", Integer.valueOf(6));
/*  167 */           hashMap.put("fieldcol", Integer.valueOf(18));
/*  168 */           ArrayList<String> arrayList = new ArrayList();
/*  169 */           arrayList.add(str8);
/*  170 */           arrayList.add(str8 + "_1");
/*  171 */           hashMap.put("domkey", arrayList);
/*  172 */           hashMap.put("viewAttr", Integer.valueOf(2));
/*  173 */           return hashMap;
/*  174 */         }  if (str5.equals("179") || str5.equals("23")) {
/*  175 */           Map<String, Object> map = CptFormItemUtil.getFormItemForBrowser(str8, SystemEnv.getHtmlLabelName(i, paramUser.getLanguage()), str5, "", 2, "int", null, null);
/*  176 */           map.put("fieldcol", Integer.valueOf(18));
/*  177 */           return map;
/*  178 */         }  if (str5.equals("402")) {
/*  179 */           return CptFormItemUtil.getFormItemForYear(str8, SystemEnv.getHtmlLabelName(i, paramUser.getLanguage()), "", 2, "0");
/*      */         }
/*  181 */         if (str5.equals("403")) {
/*  182 */           return CptFormItemUtil.getFormItemForYearAndMonth(str8, SystemEnv.getHtmlLabelName(i, paramUser.getLanguage()), "", 2, "0");
/*      */         }
/*      */         
/*  185 */         conditionType = ConditionType.BROWSER;
/*  186 */         SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(conditionType, i, str8, str5);
/*  187 */         if ("161".equals(str5) || "162".equals(str5) || "256".equals(str5) || "257".equals(str5)) {
/*  188 */           BrowserBean browserBean = searchConditionItem1.getBrowserConditionParam();
/*  189 */           browserBean.getDestDataParams().put("type", str3);
/*  190 */           String str = str3;
/*  191 */           if ("161".equals(str5) || "162".equals(str5)) {
/*      */             try {
/*  193 */               Browser browser = (Browser)StaticObj.getServiceByFullname(str, Browser.class);
/*  194 */               ArrayList arrayList = new ArrayList();
/*  195 */               Map<String, String> map1 = browserBean.getDataParams();
/*  196 */               map1.put("type", str);
/*  197 */               map1.put("currenttime", Long.valueOf(System.currentTimeMillis()));
/*      */               
/*  199 */               Map<String, String> map2 = browserBean.getCompleteParams();
/*  200 */               map2.put("fielddbtype", str);
/*  201 */               map2.put("type", str5);
/*      */               
/*  203 */               Map<String, String> map3 = browserBean.getConditionDataParams();
/*  204 */               map3.put("type", str);
/*      */               
/*  206 */               browserBean.setLinkUrl(browser.getHref());
/*  207 */               browserBean.setRelateFieldid(arrayList);
/*  208 */               browserBean.setHasAdvanceSerach(!"1".equals(Util.null2String(browser.getShowtree())));
/*  209 */               browserBean.setTitle(browser.getName());
/*  210 */             } catch (Exception exception) {
/*  211 */               exception.printStackTrace();
/*      */             } 
/*      */           } else {
/*  214 */             BrowserInitUtil browserInitUtil = new BrowserInitUtil();
/*  215 */             browserBean.getDataParams().put("type", str);
/*  216 */             browserBean.setType(String.valueOf(str5));
/*  217 */             browserInitUtil.initBrowser(browserBean, paramUser.getLanguage());
/*      */           }
/*      */         
/*  220 */         } else if ("7".equals(str5) && 1 == Util.getIntValue(str6)) {
/*  221 */           BrowserBean browserBean = searchConditionItem1.getBrowserConditionParam();
/*  222 */           Map<String, String> map1 = browserBean.getDataParams();
/*  223 */           XssUtil xssUtil = new XssUtil();
/*  224 */           map1.put("sqlwhere", xssUtil.put("where t1.type=2"));
/*  225 */           Map<String, String> map2 = browserBean.getCompleteParams();
/*  226 */           map2.put("sqlwhere", xssUtil.put("where t1.type=2"));
/*  227 */         } else if ("317".equals(str5) && 1 == Util.getIntValue(str6) && 
/*  228 */           !"".equals(str7)) {
/*  229 */           BrowserBean browserBean = searchConditionItem1.getBrowserConditionParam();
/*  230 */           String str = CptFormItemUtil.getBrowserShowName("25", str7, "");
/*  231 */           ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  232 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  233 */           hashMap.put("id", str7);
/*  234 */           hashMap.put("name", str);
/*  235 */           arrayList.add(hashMap);
/*  236 */           browserBean.setReplaceDatas(arrayList);
/*      */         } 
/*      */         
/*  239 */         searchConditionItem = searchConditionItem1; }
/*      */     
/*  241 */     } else if ("4".equals(str4)) {
/*  242 */       conditionType = ConditionType.CHECKBOX;
/*  243 */       searchConditionItem = conditionFactory.createCondition(conditionType, i, str8);
/*  244 */     } else if ("5".equals(str4)) {
/*      */       
/*  246 */       String str9 = "";
/*  247 */       String str10 = "";
/*  248 */       recordSet.executeQuery("select iscommon,cid from cptDefineField where id=?", new Object[] { str2 });
/*  249 */       if (recordSet.next()) {
/*  250 */         str9 = Util.null2String(recordSet.getString("iscommon"));
/*  251 */         str10 = Util.null2String(recordSet.getString("cid"));
/*      */       } 
/*  253 */       conditionType = ConditionType.SELECT;
/*  254 */       ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*  255 */       SearchConditionOption searchConditionOption = new SearchConditionOption();
/*  256 */       searchConditionOption.setKey("");
/*  257 */       searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(332, paramUser.getLanguage()));
/*  258 */       searchConditionOption.setSelected(true);
/*  259 */       arrayList.add(searchConditionOption);
/*  260 */       if (str9.equals("1")) {
/*  261 */         ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  262 */         recordSet.executeQuery("select id,name,defaultvalue,disorder from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str10 });
/*  263 */         byte b = 0;
/*  264 */         while (recordSet.next()) {
/*  265 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  266 */           hashMap.put("key", Integer.valueOf(b));
/*  267 */           hashMap.put("showname", Util.null2String(recordSet.getString("name")));
/*  268 */           hashMap.put("selected", Boolean.valueOf(recordSet.getString("defaultvalue").equals("1")));
/*  269 */           hashMap.put("disorder", Integer.valueOf(recordSet.getInt("disorder")));
/*  270 */           arrayList1.add(hashMap);
/*  271 */           b++;
/*      */         } 
/*  273 */         Collections.sort(arrayList1, (paramMap1, paramMap2) -> ((Integer)paramMap1.get("disorder")).intValue() - ((Integer)paramMap2.get("disorder")).intValue());
/*  274 */         for (Map<Object, Object> map : arrayList1) {
/*  275 */           searchConditionOption = new SearchConditionOption();
/*  276 */           searchConditionOption.setKey((new StringBuilder()).append(map.get("key")).append("").toString());
/*  277 */           searchConditionOption.setShowname((new StringBuilder()).append(map.get("showname")).append("").toString());
/*  278 */           searchConditionOption.setSelected(false);
/*  279 */           arrayList.add(searchConditionOption);
/*      */         } 
/*      */       } else {
/*  282 */         byte b = 2;
/*  283 */         recordSet.executeProc("cpt_selectitembyid_new", "" + str2 + b + '\001');
/*  284 */         while (recordSet.next()) {
/*  285 */           String str11 = Util.null2String(recordSet.getString("selectvalue"));
/*  286 */           String str12 = Util.toScreen(recordSet.getString("selectname"), paramUser.getLanguage());
/*  287 */           String str13 = Util.null2String(recordSet.getString("selectlabel"));
/*      */           
/*  289 */           if (!"".equals(str13)) {
/*  290 */             str12 = SystemEnv.getHtmlLabelNames(str13, paramUser.getLanguage());
/*      */           }
/*  292 */           searchConditionOption = new SearchConditionOption();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  298 */           searchConditionOption.setKey(str11);
/*  299 */           searchConditionOption.setShowname(str12);
/*  300 */           searchConditionOption.setSelected(false);
/*  301 */           arrayList.add(searchConditionOption);
/*      */         } 
/*      */       } 
/*      */       
/*  305 */       searchConditionItem = conditionFactory.createCondition(conditionType, i, str8, arrayList);
/*      */     } 
/*      */     
/*  308 */     return searchConditionItem;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static SearchConditionItem createHrmConditionWidthSelects(User paramUser, int paramInt, String paramString) {
/*  320 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/*  321 */     String[] arrayOfString = { "drl_" + paramString, paramString };
/*  322 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, paramInt, arrayOfString);
/*  323 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  324 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, -1, paramString, "17");
/*  325 */     BrowserBean browserBean = searchConditionItem2.getBrowserConditionParam();
/*  326 */     browserBean.setReplaceNotCtrl(true);
/*  327 */     hashMap.put("-5", searchConditionItem2);
/*  328 */     searchConditionItem1.setOptions(getDepartments(paramUser.getLanguage(), true));
/*  329 */     searchConditionItem1.setSelectLinkageDatas(hashMap);
/*  330 */     return searchConditionItem1;
/*      */   }
/*      */   
/*      */   public static List<SearchConditionOption> getDepartments(int paramInt, boolean paramBoolean) {
/*  334 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*  335 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, paramInt)));
/*  336 */     arrayList.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(21837, paramInt)));
/*  337 */     arrayList.add(new SearchConditionOption("-2", SystemEnv.getHtmlLabelName(81362, paramInt)));
/*  338 */     arrayList.add(new SearchConditionOption("-3", SystemEnv.getHtmlLabelName(30792, paramInt)));
/*  339 */     arrayList.add(new SearchConditionOption("-4", SystemEnv.getHtmlLabelName(81363, paramInt)));
/*  340 */     arrayList.add(new SearchConditionOption("-6", SystemEnv.getHtmlLabelName(15079, paramInt)));
/*  341 */     arrayList.add(new SearchConditionOption("-7", SystemEnv.getHtmlLabelName(517613, paramInt)));
/*  342 */     arrayList.add(new SearchConditionOption("-8", SystemEnv.getHtmlLabelName(517614, paramInt)));
/*  343 */     if (paramBoolean) {
/*  344 */       arrayList.add(new SearchConditionOption("-5", SystemEnv.getHtmlLabelName(33210, paramInt), true));
/*      */     }
/*  346 */     return arrayList;
/*      */   }
/*      */   
/*      */   public static String getHrmConditionSQL(String paramString1, String paramString2, String paramString3, User paramUser) {
/*  350 */     RecordSet recordSet = new RecordSet();
/*  351 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     try {
/*  353 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  354 */       if ("-1".equals(paramString2)) {
/*  355 */         stringBuffer.append(" and ").append(paramString1).append(" in ( select id from hrmresource where departmentid = ")
/*  356 */           .append(resourceComInfo.getDepartmentID(paramUser.getUID() + "")).append(" ) ");
/*  357 */       } else if ("-2".equals(paramString2)) {
/*  358 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  359 */         String str = resourceComInfo.getDepartmentID(paramUser.getUID() + "");
/*  360 */         stringBuffer.append(" and ").append(paramString1).append(" in (select id from hrmresource where departmentid in (").append(DepartmentComInfo.getAllChildDepartId(str, str)).append(" )) ");
/*  361 */       } else if ("-3".equals(paramString2)) {
/*  362 */         String str = resourceComInfo.getSubCompanyID(paramUser.getUID() + "");
/*  363 */         stringBuffer.append(" and ").append(paramString1).append(" in ( select id from hrmresource where subcompanyid1 = ").append(str).append(" ) ");
/*  364 */       } else if ("-4".equals(paramString2)) {
/*  365 */         String str = resourceComInfo.getSubCompanyID(paramUser.getUID() + "");
/*  366 */         stringBuffer.append(" and ").append(paramString1).append(" in (select id from hrmresource where subcompanyid1 in (select id from hrmsubcompany where supsubcomid=" + str + " or id= " + str + " )) ");
/*  367 */       } else if ("-5".equals(paramString2)) {
/*  368 */         if (!StringHelper.isEmpty(paramString3)) {
/*  369 */           stringBuffer.append(" and ").append(paramString1).append(" in ( ").append(paramString3).append(" ) ");
/*      */         }
/*  371 */       } else if ("-6".equals(paramString2)) {
/*  372 */         stringBuffer.append(" and ").append(paramString1).append(" = ").append(paramUser.getUID());
/*  373 */       } else if ("-7".equals(paramString2)) {
/*  374 */         stringBuffer.append(" and ").append(paramString1).append(" = ").append(paramUser.getManagerid());
/*  375 */       } else if ("-8".equals(paramString2)) {
/*  376 */         recordSet.executeQuery("select * from hrmresource where managerid = ? ", new Object[] { Integer.valueOf(paramUser.getUID()) });
/*  377 */         if (recordSet.next()) {
/*  378 */           stringBuffer.append(" and ").append(paramString1).append(" in (select id  from hrmresource where managerid = " + paramUser.getUID() + " ) ");
/*      */         } else {
/*  380 */           stringBuffer.append(" and ").append(paramString1).append(" in (0) ");
/*      */         } 
/*      */       } 
/*  383 */     } catch (Exception exception) {
/*  384 */       exception.printStackTrace();
/*      */     } 
/*  386 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static SearchConditionItem createDepartConditionWidthSelects(User paramUser, int paramInt, String paramString) {
/*  398 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/*  399 */     String[] arrayOfString = { "dbm_" + paramString, paramString };
/*  400 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, paramInt, arrayOfString);
/*  401 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  402 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, -1, paramString, "57");
/*  403 */     BrowserBean browserBean = searchConditionItem2.getBrowserConditionParam();
/*  404 */     browserBean.setReplaceNotCtrl(true);
/*  405 */     hashMap.put("-3", searchConditionItem2);
/*  406 */     searchConditionItem1.setOptions(getMoreDepart(paramUser.getLanguage()));
/*  407 */     searchConditionItem1.setSelectLinkageDatas(hashMap);
/*  408 */     return searchConditionItem1;
/*      */   }
/*      */   
/*      */   private static List<SearchConditionOption> getMoreDepart(int paramInt) {
/*  412 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*  413 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, paramInt)));
/*  414 */     arrayList.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(21837, paramInt)));
/*  415 */     arrayList.add(new SearchConditionOption("-2", SystemEnv.getHtmlLabelName(81362, paramInt)));
/*  416 */     arrayList.add(new SearchConditionOption("-3", SystemEnv.getHtmlLabelName(33255, paramInt), true));
/*  417 */     return arrayList;
/*      */   }
/*      */   
/*      */   public static String getDeptConditionSQL(String paramString1, String paramString2, String paramString3, User paramUser) {
/*  421 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     try {
/*  423 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  424 */       if ("0".equals(paramString2))
/*  425 */         return ""; 
/*  426 */       if ("-1".equals(paramString2)) {
/*  427 */         stringBuffer.append(" and ").append(paramString1).append(" in ( ").append(resourceComInfo.getDepartmentID(paramUser.getUID() + "")).append(" ) ");
/*  428 */       } else if ("-2".equals(paramString2)) {
/*  429 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  430 */         String str = resourceComInfo.getDepartmentID(paramUser.getUID() + "");
/*  431 */         stringBuffer.append(" and ").append(paramString1).append(" in (").append(DepartmentComInfo.getAllChildDepartId(str, str)).append(" ) ");
/*  432 */       } else if ("-3".equals(paramString2) && 
/*  433 */         !StringHelper.isEmpty(paramString3)) {
/*  434 */         stringBuffer.append(" and ").append(paramString1).append(" in ( ").append(paramString3).append(" ) ");
/*      */       }
/*      */     
/*  437 */     } catch (Exception exception) {
/*  438 */       exception.printStackTrace();
/*      */     } 
/*  440 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static SearchConditionItem createSubcompanyConditionWidthSelects(User paramUser, int paramInt, String paramString) {
/*  452 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/*  453 */     String[] arrayOfString = { "dfb_" + paramString, paramString };
/*  454 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, paramInt, arrayOfString);
/*  455 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  456 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, -1, paramString, "194");
/*  457 */     BrowserBean browserBean = searchConditionItem2.getBrowserConditionParam();
/*  458 */     browserBean.setReplaceNotCtrl(true);
/*  459 */     hashMap.put("-3", searchConditionItem2);
/*  460 */     searchConditionItem1.setOptions(getMoreSubcompany(paramUser.getLanguage()));
/*  461 */     searchConditionItem1.setSelectLinkageDatas(hashMap);
/*  462 */     return searchConditionItem1;
/*      */   }
/*      */   
/*      */   private static List<SearchConditionOption> getMoreSubcompany(int paramInt) {
/*  466 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*  467 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, paramInt)));
/*  468 */     arrayList.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(30792, paramInt)));
/*  469 */     arrayList.add(new SearchConditionOption("-2", SystemEnv.getHtmlLabelName(81363, paramInt)));
/*  470 */     arrayList.add(new SearchConditionOption("-3", SystemEnv.getHtmlLabelName(33256, paramInt), true));
/*  471 */     return arrayList;
/*      */   }
/*      */   
/*      */   public static String getSubcompanyConditionSQL(String paramString1, String paramString2, String paramString3, User paramUser) {
/*  475 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     try {
/*  477 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  478 */       if ("0".equals(paramString2))
/*  479 */         return ""; 
/*  480 */       if ("-1".equals(paramString2)) {
/*  481 */         String str = resourceComInfo.getSubCompanyID(paramUser.getUID() + "");
/*  482 */         stringBuffer.append(" and ").append(paramString1).append(" in ( ").append(str).append(" ) ");
/*  483 */       } else if ("-2".equals(paramString2)) {
/*  484 */         String str1 = resourceComInfo.getSubCompanyID(paramUser.getUID() + "");
/*  485 */         String str2 = SubCompanyComInfo.getAllChildSubcompanyId(str1, str1);
/*  486 */         stringBuffer.append(" and ").append(paramString1).append(" in (").append(str2).append(" ) ");
/*  487 */       } else if ("-3".equals(paramString2) && 
/*  488 */         !StringHelper.isEmpty(paramString3)) {
/*  489 */         stringBuffer.append(" and ").append(paramString1).append(" in ( ").append(paramString3).append(" ) ");
/*      */       }
/*      */     
/*  492 */     } catch (Exception exception) {
/*  493 */       exception.printStackTrace();
/*      */     } 
/*  495 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getOperates(String paramString1, String paramString2) throws Exception {
/*  506 */     if ("".equals(Util.null2String(paramString1)) || "".equals(Util.null2String(paramString2))) {
/*  507 */       ArrayList<String> arrayList1 = new ArrayList();
/*  508 */       for (byte b = 0; b < 20; b++) {
/*  509 */         arrayList1.add("false");
/*      */       }
/*  511 */       return arrayList1;
/*      */     } 
/*  513 */     ArrayList<String> arrayList = new ArrayList();
/*  514 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  515 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  516 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*      */     
/*  518 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  519 */     int i = Util.getIntValue(arrayOfString[0]);
/*  520 */     String str1 = Util.null2String(arrayOfString[1]);
/*  521 */     int j = Util.getIntValue(arrayOfString[2]);
/*  522 */     String str2 = Util.null2String(arrayOfString[3]);
/*  523 */     int k = Util.getIntValue(arrayOfString[4]);
/*  524 */     String str3 = "";
/*  525 */     if (arrayOfString.length > 5) {
/*  526 */       str3 = Util.null2String(arrayOfString[5]);
/*      */     }
/*      */     
/*  529 */     if (arrayOfString.length > 0) {
/*  530 */       if (!"".equals(Util.null2String(str3)) && !"null".equals(Util.null2String(str3))) {
/*  531 */         arrayList = Util.TokenizerString(str3, "_");
/*      */       } else {
/*  533 */         for (byte b = 0; b < k; b++) {
/*  534 */           arrayList.add("true");
/*      */         }
/*      */         
/*  537 */         User user = new User();
/*  538 */         user.setUid(i);
/*  539 */         user.setLoginid(resourceComInfo.getLoginID("" + i));
/*  540 */         user.setLogintype(str1);
/*  541 */         user.setLanguage(j);
/*  542 */         user.setJobtitle(resourceComInfo.getJobTitle("" + i));
/*      */         
/*  544 */         String str4 = capitalComInfo.getCptStateId(paramString1);
/*  545 */         String str5 = capitalComInfo.getSptCount(paramString1);
/*  546 */         if ("cpt_cptassortment".equalsIgnoreCase(str2)) {
/*  547 */           boolean bool1 = (Util.getIntValue(capitalAssortmentComInfo.getSubAssortmentCount(paramString1), 0) <= 0 && Util.getIntValue(capitalAssortmentComInfo.getCapitalCount(paramString1), 0) <= 0) ? true : false;
/*  548 */           boolean bool2 = (Util.getIntValue(capitalAssortmentComInfo.getCapitalCount(paramString1 + ""), 0) <= 0) ? true : false;
/*  549 */           boolean bool = Util.null2String(arrayOfString[6]).equals("true");
/*  550 */           arrayList.set(1, "" + bool1);
/*  551 */           arrayList.set(3, "" + bool2);
/*  552 */           arrayList.set(4, "" + bool);
/*  553 */         } else if ("cpt_cptassortment_share".equalsIgnoreCase(str2)) {
/*  554 */           arrayList.set(0, "true");
/*  555 */         } else if ("cpt_cpttype".equalsIgnoreCase(str2)) {
/*  556 */           boolean bool = true;
/*  557 */           String str = "select 1 from CptCapitalType where id=" + paramString1 + " and exists( select 1 from cptcapital where CptCapitalType.id=cptcapital.capitaltypeid)";
/*  558 */           RecordSet recordSet = new RecordSet();
/*  559 */           recordSet.executeSql(str);
/*  560 */           if (recordSet.next()) {
/*  561 */             bool = false;
/*      */           } else {
/*  563 */             bool = HrmUserVarify.checkUserRight("CptCapitalTypeEdit:Delete", user);
/*      */           } 
/*      */           
/*  566 */           arrayList.set(1, "" + bool);
/*  567 */         } else if ("cpt_cptdata1".equalsIgnoreCase(str2)) {
/*  568 */           boolean bool = true;
/*  569 */           String str = "select * from cptcapital where datatype = " + paramString1;
/*  570 */           RecordSet recordSet = new RecordSet();
/*  571 */           recordSet.executeSql(str);
/*  572 */           if (recordSet.next()) bool = false; 
/*  573 */           str = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + paramString1;
/*  574 */           recordSet.executeSql(str);
/*  575 */           if (recordSet.next()) bool = false; 
/*  576 */           arrayList.set(1, "" + bool);
/*      */         }
/*  578 */         else if ("cpt_mycptlist".equalsIgnoreCase(str2)) {
/*  579 */           RecordSet recordSet = new RecordSet();
/*  580 */           recordSet.executeProc("CptCapital_SelectByID", paramString1);
/*  581 */           recordSet.next();
/*  582 */           double d1 = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/*  583 */           double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/*      */           
/*  585 */           if (!HrmUserVarify.checkUserRight("CptCapital:Return", user) || (!str4.equals("2") && !str4.equals("3") && !str4.equals("4")) || d1 <= d2) {
/*  586 */             arrayList.set(0, "false");
/*      */           }
/*  588 */           if (!HrmUserVarify.checkUserRight("CptCapital:Mend", user) || str4.equals("4") || str4.equals("5") || d1 <= d2) {
/*  589 */             arrayList.set(1, "false");
/*      */           }
/*  591 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Edit", user) && !HrmUserVarify.checkUserRight("CptCapital:modify", user) && !canEdit(paramString1, user)) {
/*  592 */             arrayList.set(2, "false");
/*      */           }
/*      */ 
/*      */ 
/*      */           
/*  597 */           if (!canEdit(paramString1, user)) {
/*  598 */             arrayList.set(5, "false");
/*      */           }
/*  600 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", user)) {
/*  601 */             arrayList.set(9, "false");
/*      */           }
/*  603 */           arrayList.set(6, "false");
/*  604 */           arrayList.set(7, "false");
/*  605 */           arrayList.set(8, "false");
/*      */         }
/*  607 */         else if ("cpt_qrydata2list".equalsIgnoreCase(str2)) {
/*  608 */           arrayList.set(0, "false");
/*  609 */           arrayList.set(1, "false");
/*      */           
/*  611 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Edit", user) && !HrmUserVarify.checkUserRight("CptCapital:modify", user) && !canEdit(paramString1, user)) {
/*  612 */             arrayList.set(2, "false");
/*      */           }
/*  614 */           arrayList.set(3, "true");
/*  615 */           arrayList.set(4, "true");
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  620 */           if (!canEdit(paramString1, user)) {
/*  621 */             arrayList.set(5, "false");
/*      */           }
/*  623 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", user)) {
/*  624 */             arrayList.set(9, "false");
/*      */           }
/*  626 */           arrayList.set(6, "false");
/*  627 */           arrayList.set(10, "false");
/*  628 */           arrayList.set(7, "false");
/*  629 */           arrayList.set(8, "false");
/*      */         }
/*  631 */         else if ("cpt_qrydata2list_fromAssort".equalsIgnoreCase(str2)) {
/*  632 */           arrayList.set(0, "false");
/*  633 */           arrayList.set(1, "false");
/*      */           
/*  635 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Edit", user) && !HrmUserVarify.checkUserRight("CptCapital:modify", user) && !canEdit(paramString1, user)) {
/*  636 */             arrayList.set(2, "false");
/*      */           }
/*  638 */           arrayList.set(3, "true");
/*  639 */           arrayList.set(4, "true");
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  644 */           if (!canEdit(paramString1, user)) {
/*  645 */             arrayList.set(5, "false");
/*      */           }
/*  647 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", user)) {
/*  648 */             arrayList.set(9, "false");
/*      */           }
/*  650 */           if ("1".equals(str5) && !"-7".equals(str4) && !"5".equals(str4)) {
/*  651 */             arrayList.set(6, "false");
/*      */           }
/*  653 */           arrayList.set(10, "false");
/*  654 */           arrayList.set(7, "false");
/*  655 */           arrayList.set(8, "false");
/*      */         }
/*  657 */         else if ("cpt_qrydata1list".equalsIgnoreCase(str2)) {
/*  658 */           arrayList.set(0, "false");
/*  659 */           arrayList.set(1, "false");
/*  660 */           arrayList.set(2, "false");
/*  661 */           arrayList.set(3, "false");
/*  662 */           arrayList.set(4, "false");
/*  663 */           arrayList.set(5, "false");
/*  664 */           arrayList.set(6, "false");
/*  665 */           arrayList.set(7, "false");
/*  666 */           arrayList.set(8, "false");
/*  667 */           arrayList.set(9, "false");
/*  668 */           arrayList.set(10, "false");
/*  669 */           boolean bool = HrmUserVarify.checkUserRight("Capital:Maintenance", user);
/*  670 */           arrayList.set(2, "" + bool);
/*  671 */           arrayList.set(9, "" + canDeleteData1(user, paramString1));
/*  672 */         } else if ("cpt_modifylist".equalsIgnoreCase(str2)) {
/*  673 */           arrayList.set(0, "true");
/*  674 */           arrayList.set(1, "true");
/*  675 */         } else if ("cpt_qrydata1list_maint".equalsIgnoreCase(str2)) {
/*  676 */           arrayList.set(0, "false");
/*  677 */           arrayList.set(1, "false");
/*  678 */           arrayList.set(2, "false");
/*  679 */           arrayList.set(3, "false");
/*  680 */           arrayList.set(4, "false");
/*  681 */           arrayList.set(5, "false");
/*  682 */           arrayList.set(9, "false");
/*  683 */           arrayList.set(6, "false");
/*  684 */           arrayList.set(10, "true");
/*  685 */           if (HrmUserVarify.checkUserRight("Capital:Maintenance", user)) {
/*  686 */             arrayList.set(7, "" + (!iscancelled(paramString1) ? 1 : 0));
/*  687 */             arrayList.set(8, "" + iscancelled(paramString1));
/*      */           } else {
/*  689 */             arrayList.set(7, "false");
/*  690 */             arrayList.set(8, "false");
/*      */           } 
/*  692 */           boolean bool = HrmUserVarify.checkUserRight("Capital:Maintenance", user);
/*  693 */           arrayList.set(2, "" + bool);
/*  694 */           arrayList.set(9, "" + canDeleteData1(user, paramString1));
/*      */         } 
/*      */       } 
/*      */     }
/*  698 */     return arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean canEdit(String paramString, User paramUser) throws Exception {
/*  703 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  704 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  705 */     paramUser.setSeclevel(resourceComInfo.getSeclevel("" + paramUser.getUID()));
/*  706 */     String str = commonShareManager.getSharLevel("cpt", paramString, paramUser);
/*  707 */     RecordSet recordSet = new RecordSet();
/*  708 */     recordSet.execute(str);
/*  709 */     boolean bool = false;
/*  710 */     if (recordSet.next()) {
/*  711 */       int i = recordSet.getInt(1);
/*  712 */       if (i >= 2) {
/*  713 */         bool = true;
/*      */       }
/*      */     } 
/*  716 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getInstockCheckOperates(String paramString1, String paramString2) {
/*  727 */     return new ArrayList(Arrays.asList((Object[])new String[] { "true", "true" }));
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean canDeleteData1(User paramUser, String paramString) {
/*  732 */     boolean bool = false;
/*  733 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", paramUser)) {
/*  734 */       String str = "select * from cptcapital where datatype = " + paramString;
/*  735 */       RecordSet recordSet = new RecordSet();
/*  736 */       recordSet.execute(str);
/*  737 */       if (!recordSet.next()) {
/*  738 */         str = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + paramString;
/*  739 */         recordSet.execute(str);
/*  740 */         if (!recordSet.next()) {
/*  741 */           bool = true;
/*      */         }
/*      */       } 
/*      */     } 
/*  745 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<SearchConditionOption> getDateSelectFromTo(int paramInt) {
/*  752 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*  753 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, paramInt), true));
/*  754 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, paramInt)));
/*  755 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, paramInt)));
/*  756 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, paramInt)));
/*  757 */     arrayList.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(27347, paramInt)));
/*  758 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, paramInt)));
/*  759 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, paramInt)));
/*  760 */     arrayList.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(81716, paramInt)));
/*  761 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, paramInt)));
/*  762 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getShareTypeName(String paramString1, String paramString2) {
/*  772 */     if ("".equals(Util.null2String(paramString1))) {
/*  773 */       return "";
/*      */     }
/*  775 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  776 */     int i = 7;
/*  777 */     if (arrayOfString.length > 0) {
/*  778 */       i = Util.getIntValue(arrayOfString[0], 7);
/*      */     }
/*      */     
/*  781 */     int j = Util.getIntValue(paramString1, 0);
/*  782 */     String str = "";
/*  783 */     switch (j) {
/*      */       case 1:
/*  785 */         str = SystemEnv.getHtmlLabelName(179, i);
/*      */         break;
/*      */       case 2:
/*  788 */         str = SystemEnv.getHtmlLabelName(124, i);
/*      */         break;
/*      */       case 3:
/*  791 */         str = SystemEnv.getHtmlLabelName(122, i);
/*      */         break;
/*      */       case 4:
/*  794 */         str = SystemEnv.getHtmlLabelName(1340, i);
/*      */         break;
/*      */       case 5:
/*  797 */         str = SystemEnv.getHtmlLabelName(141, i);
/*      */         break;
/*      */       case 6:
/*  800 */         str = SystemEnv.getHtmlLabelName(1508, i);
/*      */         break;
/*      */       case 7:
/*  803 */         str = SystemEnv.getHtmlLabelName(882, i);
/*      */         break;
/*      */       case 8:
/*  806 */         str = SystemEnv.getHtmlLabelName(82769, i);
/*      */         break;
/*      */       case 11:
/*  809 */         str = SystemEnv.getHtmlLabelName(6086, i);
/*      */         break;
/*      */       case 100:
/*  812 */         str = SystemEnv.getHtmlLabelName(513163, i);
/*      */         break;
/*      */       case 101:
/*  815 */         str = SystemEnv.getHtmlLabelName(513164, i);
/*      */         break;
/*      */       case 102:
/*  818 */         str = SystemEnv.getHtmlLabelName(513165, i);
/*      */         break;
/*      */       case 103:
/*  821 */         str = SystemEnv.getHtmlLabelName(513166, i);
/*      */         break;
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  827 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getShareObjectName(String paramString1, String paramString2) throws Exception {
/*  839 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  840 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  841 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  842 */     RolesComInfo rolesComInfo = new RolesComInfo();
/*  843 */     JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/*      */     
/*  845 */     if ("".equals(Util.null2String(paramString1))) {
/*  846 */       return "";
/*      */     }
/*      */     
/*  849 */     paramString1 = paramString1.replace("_g", "");
/*  850 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  851 */     int i = 7;
/*  852 */     String str1 = "";
/*  853 */     if (arrayOfString.length > 1) {
/*  854 */       i = Util.getIntValue(arrayOfString[0], 7);
/*  855 */       str1 = Util.null2String(arrayOfString[1]);
/*      */     } 
/*      */     
/*  858 */     String str2 = "";
/*  859 */     int j = 0;
/*  860 */     String str3 = "select * from " + str1 + " where id=" + paramString1;
/*  861 */     RecordSet recordSet1 = new RecordSet();
/*  862 */     RecordSet recordSet2 = new RecordSet();
/*  863 */     CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*  864 */     recordSet1.execute(str3);
/*  865 */     if (recordSet1.next()) {
/*  866 */       BrowserComInfo browserComInfo; String str4; BrowserConfigComInfo browserConfigComInfo; String str5; char c; String str6, str7, str8, str9; int m; String str10, str11; int k = recordSet1.getInt("sharetype");
/*  867 */       switch (k) {
/*      */         
/*      */         case 1:
/*      */         case 6:
/*      */         case 7:
/*  872 */           str2 = "<a href=\"javascript:openhrm(" + recordSet1.getString("userid") + ")\" onclick=\"pointerXY(event);\" >" + Util.toScreen(resourceComInfo.getResourcename(recordSet1
/*  873 */                 .getString("userid")), i) + "</a>";
/*      */           break;
/*      */         
/*      */         case 2:
/*  877 */           browserComInfo = new BrowserComInfo();
/*      */           
/*  879 */           browserConfigComInfo = new BrowserConfigComInfo();
/*  880 */           c = '\004';
/*  881 */           str6 = Util.null2String(browserConfigComInfo.getLinkurl(c + ""));
/*      */           
/*  883 */           if ("".equals(str6)) {
/*  884 */             str6 = Util.null2String(browserComInfo.getLinkurl(c + ""));
/*      */           }
/*      */           
/*  887 */           str7 = browserComInfo.getBrowsertablename(c + "");
/*  888 */           str8 = browserComInfo.getBrowsercolumname(c + "");
/*  889 */           str9 = browserComInfo.getBrowserkeycolumname(c + "");
/*      */           
/*  891 */           m = Util.getIntValue(recordSet1.getString("departmentid"), 0);
/*  892 */           str10 = "select " + str8 + " from " + str7 + " where " + str9 + "=" + m;
/*  893 */           recordSet2.executeSql(str10);
/*  894 */           recordSet2.next();
/*  895 */           str11 = recordSet2.getString(1);
/*  896 */           if (!str6.equals("")) {
/*  897 */             str2 = str2 + "<a href=\"javascript:openFullWindowForXtable('" + str6 + m + "')\">";
/*      */           }
/*  899 */           str2 = str2 + Util.toScreen(str11, i);
/*  900 */           if (!str6.equals("")) {
/*  901 */             str2 = str2 + "</a>";
/*      */           }
/*      */           break;
/*      */         
/*      */         case 3:
/*  906 */           str2 = Util.toScreen(rolesComInfo
/*  907 */               .getRoleLevelName("" + Util.getIntValue(recordSet1.getString("rolelevel"), 0), "" + i), i);
/*  908 */           str2 = str2 + " / " + Util.toScreen(rolesComInfo
/*  909 */               .getRolesRemark(recordSet1.getString("roleid")), i);
/*      */           break;
/*      */ 
/*      */         
/*      */         case 9:
/*  914 */           browserComInfo = new BrowserComInfo();
/*      */           
/*  916 */           browserConfigComInfo = new BrowserConfigComInfo();
/*  917 */           c = '\007';
/*  918 */           str6 = Util.null2String(browserConfigComInfo.getLinkurl(c + ""));
/*  919 */           if ("".equals(str6)) {
/*  920 */             str6 = Util.null2String(browserComInfo.getLinkurl(c + ""));
/*      */           }
/*  922 */           str7 = browserComInfo.getBrowsertablename(c + "");
/*  923 */           str8 = browserComInfo.getBrowsercolumname(c + "");
/*  924 */           str9 = browserComInfo.getBrowserkeycolumname(c + "");
/*      */           
/*  926 */           m = Util.getIntValue(recordSet1.getString("crmid"), 0);
/*  927 */           str10 = "select " + str8 + " from " + str7 + " where " + str9 + "=" + m;
/*  928 */           recordSet2.executeSql(str10);
/*  929 */           recordSet2.next();
/*  930 */           str11 = recordSet2.getString(1);
/*  931 */           if (!str6.equals("")) {
/*  932 */             str2 = str2 + "<a href=\"javascript:openFullWindowForXtable('" + str6 + m + "')\">";
/*      */           }
/*  934 */           str2 = str2 + Util.toScreen(str11, i);
/*  935 */           if (!str6.equals("")) {
/*  936 */             str2 = str2 + "</a>";
/*      */           }
/*      */           break;
/*      */         
/*      */         case 4:
/*  941 */           str2 = "";
/*      */           break;
/*      */         
/*      */         case 5:
/*  945 */           browserComInfo = new BrowserComInfo();
/*      */           
/*  947 */           browserConfigComInfo = new BrowserConfigComInfo();
/*  948 */           c = '¤';
/*  949 */           str6 = Util.null2String(browserConfigComInfo.getLinkurl(c + ""));
/*      */           
/*  951 */           if ("".equals(str6)) {
/*  952 */             str6 = Util.null2String(browserComInfo.getLinkurl(c + ""));
/*      */           }
/*      */           
/*  955 */           str7 = browserComInfo.getBrowsertablename(c + "");
/*  956 */           str8 = browserComInfo.getBrowsercolumname(c + "");
/*  957 */           str9 = browserComInfo.getBrowserkeycolumname(c + "");
/*      */           
/*  959 */           m = Util.getIntValue(recordSet1.getString("subcompanyid"), 0);
/*  960 */           str10 = "select " + str8 + " from " + str7 + " where " + str9 + "=" + m;
/*  961 */           recordSet2.executeSql(str10);
/*  962 */           recordSet2.next();
/*  963 */           str11 = recordSet2.getString(1);
/*  964 */           if (!str6.equals("")) {
/*  965 */             str2 = str2 + "<a href=\"javascript:openFullWindowForXtable('" + str6 + m + "')\">";
/*      */           }
/*  967 */           str2 = str2 + Util.toScreen(str11, i);
/*  968 */           if (!str6.equals("")) {
/*  969 */             str2 = str2 + "</a>";
/*      */           }
/*      */           break;
/*      */         
/*      */         case 11:
/*  974 */           str4 = recordSet1.getString("joblevel");
/*  975 */           str5 = "";
/*  976 */           if ("0".equals(str4)) {
/*  977 */             str5 = SystemEnv.getHtmlLabelName(140, i);
/*      */           } else {
/*  979 */             String str = recordSet1.getString("scopeid");
/*  980 */             if ("1".equals(str4)) {
/*  981 */               str5 = SystemEnv.getHtmlLabelName(19438, i);
/*  982 */               str5 = str5 + "(" + departmentComInfo.getDepartmentNames(str) + ")";
/*  983 */             } else if ("2".equals(str4)) {
/*  984 */               str5 = SystemEnv.getHtmlLabelName(19437, i);
/*      */               
/*  986 */               str6 = "";
/*  987 */               if (str.indexOf(",") != -1) {
/*  988 */                 String[] arrayOfString1 = str.split(",");
/*  989 */                 for (byte b = 0; b < arrayOfString1.length; b++) {
/*  990 */                   str6 = str6 + subCompanyComInfo.getSubCompanyname(arrayOfString1[b]) + ",";
/*      */                 }
/*  992 */                 if (!"".equals(str6)) str6 = str6.substring(0, str6.length() - 1); 
/*      */               } else {
/*  994 */                 str6 = subCompanyComInfo.getSubCompanyname(str);
/*      */               } 
/*  996 */               str5 = str5 + "(" + str6 + ")";
/*      */             } 
/*      */           } 
/*  999 */           str2 = Util.toScreen(jobTitlesComInfo.getJobTitlesname(recordSet1.getString("jobtitleid")) + "/" + str5, i);
/*      */           break;
/*      */         case 100:
/* 1002 */           str2 = SystemEnv.getHtmlLabelNames(cptFieldComInfo.getLabel(recordSet1.getString("scopeid")), i);
/* 1003 */           j = recordSet1.getInt("orgrelation");
/* 1004 */           if (j == 1) {
/* 1005 */             str2 = str2 + "(" + SystemEnv.getHtmlLabelName(384035, i) + ")"; break;
/* 1006 */           }  if (j == 2) {
/* 1007 */             str2 = str2 + "(" + SystemEnv.getHtmlLabelName(15709, i) + ")"; break;
/* 1008 */           }  if (j == 3) {
/* 1009 */             str2 = str2 + "(" + SystemEnv.getHtmlLabelName(15762, i) + ")";
/*      */           }
/*      */           break;
/*      */         case 101:
/* 1013 */           str2 = SystemEnv.getHtmlLabelNames(cptFieldComInfo.getLabel(recordSet1.getString("scopeid")), i);
/* 1014 */           j = recordSet1.getInt("orgrelation");
/* 1015 */           if (j == 1) {
/* 1016 */             str2 = str2 + "(" + SystemEnv.getHtmlLabelName(15762, i) + ")"; break;
/* 1017 */           }  if (j == 2) {
/* 1018 */             str2 = str2 + "(" + SystemEnv.getHtmlLabelName(15765, i) + ")";
/*      */           }
/*      */           break;
/*      */         case 102:
/* 1022 */           str2 = SystemEnv.getHtmlLabelNames(cptFieldComInfo.getLabel(recordSet1.getString("scopeid")), i);
/* 1023 */           j = recordSet1.getInt("orgrelation");
/* 1024 */           if (j == 1) {
/* 1025 */             str2 = str2 + "(" + SystemEnv.getHtmlLabelName(15762, i) + ")"; break;
/* 1026 */           }  if (j == 2) {
/* 1027 */             str2 = str2 + "(" + SystemEnv.getHtmlLabelName(15765, i) + ")";
/*      */           }
/*      */           break;
/*      */         case 103:
/* 1031 */           str2 = SystemEnv.getHtmlLabelNames(cptFieldComInfo.getLabel(recordSet1.getString("scopeid")), i);
/*      */           break;
/*      */         
/*      */         case 8:
/* 1035 */           str2 = "<a href=\"javascript:openhrm(" + recordSet1.getString("userid") + ")\" onclick=\"pointerXY(event);\" >" + Util.toScreen(resourceComInfo.getResourcename(recordSet1
/* 1036 */                 .getString("userid")), i) + "</a>";
/*      */           break;
/*      */       } 
/*      */ 
/*      */ 
/*      */     
/*      */     } 
/* 1043 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevel(String paramString1, String paramString2) {
/* 1054 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 1055 */     if (arrayOfString.length > 1 && ("1".equals(arrayOfString[1]) || "11".equals(arrayOfString[1]) || "9".equals(arrayOfString[1]) || "6".equals(arrayOfString[1]) || "7".equals(arrayOfString[1]) || "100".equals(arrayOfString[1]) || "103".equals(arrayOfString[1]))) {
/* 1056 */       return "";
/*      */     }
/* 1058 */     if ("99999".equals(arrayOfString[0]) && !"".equals(paramString1))
/* 1059 */       return "≥" + paramString1; 
/* 1060 */     if (!"".equals(arrayOfString[0]) && "-99999".equals(paramString1)) {
/* 1061 */       return "≤" + arrayOfString[0];
/*      */     }
/* 1063 */     return paramString1 + " - " + arrayOfString[0];
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getShareLevelName(String paramString1, String paramString2) {
/* 1076 */     if ("".equals(Util.null2String(paramString1))) {
/* 1077 */       return "";
/*      */     }
/* 1079 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 1080 */     int i = 7;
/* 1081 */     if (arrayOfString.length > 0) {
/* 1082 */       i = Util.getIntValue(arrayOfString[0], 7);
/*      */     }
/* 1084 */     int j = Util.getIntValue(paramString1, 0);
/* 1085 */     String str = "";
/* 1086 */     switch (j) {
/*      */       case 1:
/* 1088 */         str = SystemEnv.getHtmlLabelName(367, i);
/*      */         break;
/*      */       case 2:
/* 1091 */         str = SystemEnv.getHtmlLabelName(93, i);
/*      */         break;
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1097 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getIsDefaultShareName(String paramString1, String paramString2) {
/* 1108 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/*      */     
/* 1110 */     int i = Util.getIntValue(paramString1, 0);
/* 1111 */     int j = 7;
/* 1112 */     if (arrayOfString.length > 0) {
/* 1113 */       j = Util.getIntValue(arrayOfString[0], 7);
/*      */     }
/* 1115 */     String str = SystemEnv.getHtmlLabelName(18574, j);
/* 1116 */     switch (i)
/*      */     { case 1:
/* 1118 */         str = SystemEnv.getHtmlLabelName(15059, j);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1125 */         return str; }  str = SystemEnv.getHtmlLabelName(18574, j); return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String mathPercent(String paramString1, String paramString2) throws Exception {
/* 1136 */     float f1 = Util.getFloatValue(paramString1, 0.0F);
/* 1137 */     float f2 = Util.getFloatValue(paramString2, 0.0F);
/* 1138 */     String str = "0.00";
/* 1139 */     if (f2 > 0.0F) {
/* 1140 */       DecimalFormat decimalFormat = new DecimalFormat("0.00");
/* 1141 */       str = decimalFormat.format((f1 / f2 * 100.0F));
/*      */     } 
/* 1143 */     return str;
/*      */   }
/*      */   
/*      */   public String getFloatStr(String paramString1, String paramString2) {
/* 1147 */     int i = Util.getIntValue(paramString2, 3);
/* 1148 */     return Util.getFloatStr(paramString1, i);
/*      */   }
/*      */   
/*      */   public String getTotalPrice(String paramString1, String paramString2) {
/* 1152 */     String str1 = "";
/* 1153 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 1154 */     String str2 = "";
/* 1155 */     String str3 = "";
/* 1156 */     ProjectTransMethod projectTransMethod = new ProjectTransMethod();
/* 1157 */     str2 = ProjectTransMethod.getDecryptStrByfieldname("startprice", "CptCapital", arrayOfString[0]);
/* 1158 */     str3 = ProjectTransMethod.getDecryptStrByfieldname("capitalnum", "CptCapital", arrayOfString[1]);
/*      */ 
/*      */     
/* 1161 */     float f1 = Util.getFloatValue(str2);
/*      */     
/* 1163 */     float f2 = Util.getFloatValue(str3);
/*      */     
/* 1165 */     str1 = "" + ((f1 * f2) * 1.0D);
/*      */     
/* 1167 */     return Util.getFloatStr(Util.round(str1, 2), 3);
/*      */   }
/*      */   
/*      */   public String getCurPrice(String paramString1, String paramString2) {
/* 1171 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 1172 */     String str1 = "";
/*      */     
/* 1174 */     String str2 = "";
/* 1175 */     String str3 = "";
/* 1176 */     String str4 = "";
/* 1177 */     String str5 = "";
/* 1178 */     String str6 = "";
/* 1179 */     String str7 = "";
/* 1180 */     if (arrayOfString.length >= 6) {
/* 1181 */       str2 = arrayOfString[0];
/* 1182 */       str3 = arrayOfString[1];
/* 1183 */       str4 = arrayOfString[2];
/* 1184 */       str5 = arrayOfString[3];
/* 1185 */       str6 = arrayOfString[4];
/* 1186 */       str7 = arrayOfString[5];
/*      */     } 
/* 1188 */     ProjectTransMethod projectTransMethod = new ProjectTransMethod();
/* 1189 */     str3 = ProjectTransMethod.getDecryptStrByfieldname("startprice", "CptCapital", str3);
/* 1190 */     str4 = ProjectTransMethod.getDecryptStrByfieldname("capitalnum", "CptCapital", str4);
/*      */ 
/*      */     
/* 1193 */     float f1 = Util.getFloatValue(str3);
/* 1194 */     if (f1 < 0.0F) f1 = 0.0F;
/*      */     
/* 1196 */     float f2 = Util.getFloatValue(str4);
/* 1197 */     if (f2 < 0.0F) f2 = 0.0F;
/*      */     
/* 1199 */     float f3 = Util.getFloatValue(str6);
/* 1200 */     if (f3 < 0.0F) f3 = 0.0F;
/*      */     
/* 1202 */     float f4 = Util.getFloatValue(str7);
/* 1203 */     if (f4 < 0.0F) f4 = 0.0F;
/*      */     
/* 1205 */     if (str2.equals("1")) {
/*      */       
/* 1207 */       if (Util.getFloatValue(str7) >= 100.0F || f3 == 0.0F) {
/* 1208 */         str1 = str3;
/*      */       } else {
/*      */         
/* 1211 */         int i = calcTotalMonth(str5);
/*      */         
/* 1213 */         if (i >= f3 * 12.0F) {
/* 1214 */           str1 = "" + (f1 * f4 / 100.0F);
/* 1215 */           str1 = Util.getPointValue(str1);
/*      */         } else {
/*      */           
/* 1218 */           float f5 = (100.0F - f4) / 100.0F;
/*      */           
/* 1220 */           float f6 = i / f3 * 12.0F;
/* 1221 */           str1 = "" + (f1 - f1 * f5 * f6);
/* 1222 */           str1 = Util.getPointValue(str1);
/*      */         } 
/*      */       } 
/*      */     } else {
/*      */       
/* 1227 */       str1 = "" + ((f1 * f2) * 1.0D);
/*      */     } 
/* 1229 */     return Util.getFloatStr(Util.round(str1, 2), 3);
/*      */   }
/*      */   
/*      */   private int calcTotalMonth(String paramString) {
/* 1233 */     String str = DateHelper.getCurrentDate();
/* 1234 */     int i = 0;
/* 1235 */     if (!paramString.equals("")) {
/* 1236 */       i = Util.getIntValue(str.substring(5, 7)) - Util.getIntValue(paramString.substring(5, 7)) + (Util.getIntValue(str.substring(0, 4)) - Util.getIntValue(paramString.substring(0, 4))) * 12;
/*      */     }
/* 1238 */     return i;
/*      */   }
/*      */   
/*      */   public String getFeePrice(String paramString1, String paramString2) {
/* 1242 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/* 1243 */     String str1 = "";
/* 1244 */     String str2 = "";
/* 1245 */     String str3 = "";
/* 1246 */     String str4 = "";
/* 1247 */     ProjectTransMethod projectTransMethod = new ProjectTransMethod();
/* 1248 */     if (arrayOfString.length >= 4) {
/* 1249 */       str1 = arrayOfString[0];
/* 1250 */       str2 = ProjectTransMethod.getDecryptStrByfieldname("startprice", "CptCapital", arrayOfString[1]);
/* 1251 */       str3 = arrayOfString[2];
/* 1252 */       str4 = arrayOfString[3];
/*      */     } 
/* 1254 */     String str5 = "" + Util.getDoubleValue(paramString1, 0.0D);
/* 1255 */     double d = Util.getDoubleValue(str2, 0.0D);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1265 */     return str5;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCanDelCptFromAssort(String paramString) throws Exception {
/* 1275 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1276 */     String str1 = "false";
/* 1277 */     RecordSet recordSet = new RecordSet();
/* 1278 */     String[] arrayOfString = paramString.split("\\+");
/* 1279 */     recordSet.executeSql("select isdata from cptcapital where id = " + arrayOfString[0]);
/* 1280 */     recordSet.next();
/* 1281 */     String str2 = recordSet.getString("isdata");
/* 1282 */     User user = new User();
/* 1283 */     user.setUid(Integer.parseInt(arrayOfString[1]));
/* 1284 */     user.setLogintype("1");
/* 1285 */     user.setLoginid(resourceComInfo.getLoginID(arrayOfString[1]));
/* 1286 */     user.setLanguage(Integer.parseInt(arrayOfString[2]));
/* 1287 */     if ("1".equals(str2)) {
/* 1288 */       if (HrmUserVarify.checkUserRight("Capital:Maintenance", user)) {
/* 1289 */         str1 = getCanDelCptCapitalData1(arrayOfString[0]);
/*      */       
/*      */       }
/*      */     
/*      */     }
/* 1294 */     else if (HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", user)) {
/* 1295 */       str1 = "true";
/*      */     } 
/*      */     
/* 1298 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCanDelCptCapitalData1(String paramString) {
/* 1308 */     boolean bool = true;
/* 1309 */     String str = "select * from cptcapital where datatype = " + paramString;
/* 1310 */     RecordSet recordSet = new RecordSet();
/* 1311 */     recordSet.executeSql(str);
/* 1312 */     if (recordSet.next()) bool = false; 
/* 1313 */     str = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + paramString;
/* 1314 */     recordSet.executeSql(str);
/* 1315 */     if (recordSet.next()) bool = false;
/*      */ 
/*      */     
/* 1318 */     return "" + bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWfFieldBrowserValue(String paramString1, String paramString2) throws Exception {
/* 1329 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*      */     
/* 1331 */     if (arrayOfString != null && arrayOfString.length >= 1) {
/* 1332 */       if ("0".equals(paramString1)) {
/* 1333 */         return SystemEnv.getHtmlLabelName(21778, Util.getIntValue(arrayOfString[0]));
/*      */       }
/* 1335 */       return SystemEnv.getHtmlLabelName(19325, Util.getIntValue(arrayOfString[0])) + arrayOfString[1];
/*      */     } 
/*      */     
/* 1338 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean iscancelled(String paramString) throws Exception {
/* 1349 */     RecordSet recordSet = new RecordSet();
/* 1350 */     String str = "";
/* 1351 */     recordSet.executeQuery("select cancelled from CptCapital where id=?", new Object[] { paramString });
/* 1352 */     if (recordSet.next()) {
/* 1353 */       str = Util.null2String(recordSet.getString("cancelled"));
/*      */     }
/* 1355 */     if (str.equals("1")) {
/* 1356 */       return true;
/*      */     }
/* 1358 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCanDelInstock(String paramString) {
/* 1368 */     String str = "false";
/* 1369 */     if (!"0".equals(paramString))
/*      */     {
/* 1371 */       str = "true";
/*      */     }
/* 1373 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/ConditionUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */