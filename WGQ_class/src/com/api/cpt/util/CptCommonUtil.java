/*    */ package com.api.cpt.util;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CptCommonUtil
/*    */ {
/*    */   public Map<String, Object> getPinYin(String paramString, int paramInt) {
/* 14 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 15 */     RecordSet recordSet = new RecordSet();
/* 16 */     String str1 = Util.null2String(paramString).trim();
/* 17 */     String str2 = "select dbo.getPinYin('" + str1 + "')";
/* 18 */     String str3 = recordSet.getDBType();
/* 19 */     if ("oracle".equals(str3)) {
/* 20 */       str2 = "select getPinYin('" + str1 + "') from dual";
/* 21 */     } else if ("mysql".equals(str3)) {
/* 22 */       str2 = "select getPinYin('" + str1 + "')";
/* 23 */     } else if ("postgresql".equals(str3)) {
/* 24 */       str2 = "select getPinYin('" + str1 + "')";
/*    */     } 
/* 26 */     recordSet.executeQuery(str2, new Object[0]);
/* 27 */     String str4 = "";
/* 28 */     if (recordSet.next()) {
/* 29 */       str4 = Util.null2String(recordSet.getString(1));
/* 30 */       hashMap.put("pinyin111", str4);
/*    */     } 
/* 32 */     if (!"".equals(str4)) {
/* 33 */       str4 = str4.toLowerCase();
/*    */     }
/*    */     
/* 36 */     hashMap.put("pinyin222", str4);
/*    */ 
/*    */     
/* 39 */     String str5 = ",PERCENT,PLAN,PRECISION,PRIMARY,PRINT,PROC,PROCEDURE,PUBLIC,RAISERROR,READ,READTEXT,RECONFIGURE,REFERENCES,REPLICATION,RESTORE,RESTRICT,RETURN,REVOKE,RIGHT,ROLLBACK,ROWCOUNT,ROWGUIDCOL,RULE,SAVE,SCHEMA,SELECT,SESSION_USER,SET,SETUSER,SHUTDOWN,SOME,STATISTICS,SYSTEM_USER,TABLE,TEXTSIZE,THEN,TO,TOP,TRAN,TRANSACTION,TRIGGER,TRUNCATE,TSEQUAL,UNION,UNIQUE,UPDATE,UPDATETEXT,USE,USER,VALUES,VARYING,VIEW,WAITFOR,WHEN,WHERE,WHILE,WITH,WRITETEXT,EXCEPT,EXEC,EXECUTE,EXISTS,EXIT,FETCH,FILE,FILLFACTOR,FOR,FOREIGN,FREETEXT,FREETEXTTABLE,FROM,FULL,FUNCTION,GOTO,GRANT,GROUP,HAVING,HOLDLOCK,IDENTITY,IDENTITY_INSERT,IDENTITYCOL,IF,IN,INDEX,INNER,INSERT,INTERSECT,INTO,IS,JOIN,KEY,KILL,LEFT,LIKE,LINENO,LOAD,NATIONAL,NOCHECK,NONCLUSTERED,NOT,NULL,NULLIF,OF,OFF,OFFSETS,ON,OPEN,OPENDATASOURCE,OPENQUERY,OPENROWSET,OPENXML,OPTION,OR,ORDER,OUTER,OVER,ADD,ALL,ALTER,AND,ANY,AS,ASC,AUTHORIZATION,BACKUP,BEGIN,BETWEEN,BREAK,BROWSE,BULK,BY,CASCADE,CASE,CHECK,CHECKPOINT,CLOSE,CLUSTERED,COALESCE,COLLATE,COLUMN,COMMIT,COMPUTE,CONSTRAINT,CONTAINS,CONTAINSTABLE,CONTINUE,CONVERT,CREATE,CROSS,CURRENT,CURRENT_DATE,CURRENT_TIME,CURRENT_TIMESTAMP,CURRENT_USER,CURSOR,DATABASE,DBCC,DEALLOCATE,DECLARE,DEFAULT,DELETE,DENY,DESC,DISK,DISTINCT,DISTRIBUTED,DOUBLE,DROP,DUMMY,DUMP,ELSE,END,ERRLVL,ESCAPE,";
/* 40 */     str5 = str5 + "ACCESS,ADD,ALL,ALTER,AND,ANY,AS,ASC,AUDIT,BETWEEN,BY,CHAR,";
/* 41 */     str5 = str5 + "CHECK,CLUSTER,COLUMN,COMMENT,COMPRESS,CONNECT,CREATE,CURRENT,";
/* 42 */     str5 = str5 + "DATE,DECIMAL,DEFAULT,DELETE,DESC,DISTINCT,DROP,ELSE,EXCLUSIVE,";
/* 43 */     str5 = str5 + "EXISTS,FILE,FLOAT,FOR,FROM,GRANT,GROUP,HAVING,IDENTIFIED,";
/* 44 */     str5 = str5 + "IMMEDIATE,IN,INCREMENT,INDEX,INITIAL,INSERT,INTEGER,INTERSECT,";
/* 45 */     str5 = str5 + "INTO,IS,LEVEL,LIKE,LOCK,LONG,MAXEXTENTS,MINUS,MLSLABEL,MODE,";
/* 46 */     str5 = str5 + "MODIFY,NOAUDIT,NOCOMPRESS,NOT,NOWAIT,NULL,NUMBER,OF,OFFLINE,ON,";
/* 47 */     str5 = str5 + "ONLINE,OPTION,OR,ORDER,PCTFREE,PRIOR,PRIVILEGES,PUBLIC,RAW,";
/* 48 */     str5 = str5 + "RENAME,RESOURCE,REVOKE,ROW,ROWID,ROWNUM,ROWS,SELECT,SESSION,";
/* 49 */     str5 = str5 + "SET,SHARE,SIZE,SMALLINT,START,SUCCESSFUL,SYNONYM,SYSDATE,TABLE,";
/* 50 */     str5 = str5 + "THEN,TO,TRIGGER,UID,UNION,UNIQUE,UPDATE,USER,VALIDATE,VALUES,";
/* 51 */     str5 = str5 + "VARCHAR,VARCHAR2,VIEW,WHENEVER,WHERE,WITH,";
/* 52 */     str4 = str4.replaceAll("[^0-9a-zA-Z]+", "");
/* 53 */     str4 = str4.replaceAll("^\\d+", "");
/* 54 */     if (str4.length() > 30) {
/* 55 */       str4 = str4.substring(0, 30);
/*    */     }
/* 57 */     String str6 = "";
/* 58 */     String str7 = "," + str4.toUpperCase() + ",";
/* 59 */     if (str5.indexOf(str7) > 0) {
/* 60 */       str6 = str4 + SystemEnv.getHtmlLabelName(126999, paramInt);
/* 61 */       str4 = str4 + "1";
/* 62 */       hashMap.put("pinyin333", str4);
/*    */     } 
/* 64 */     hashMap.put("pinyin", str4);
/* 65 */     hashMap.put("msg", str6);
/* 66 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CptCommonUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */