/*     */ package com.api.cpt.util;
/*     */ 
/*     */ import com.api.cpt.bean.SecTreeNode;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.proj.util.SQLUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalAssortment
/*     */ {
/*  28 */   private CapitalAssortmentComInfo cptgroup = new CapitalAssortmentComInfo();
/*  29 */   private CommonShareManager commonShareManager = new CommonShareManager();
/*     */   
/*     */   public Map<String, Object> getTree(User paramUser, Map<String, Object> paramMap, String paramString) throws Exception {
/*  32 */     String str1 = Util.null2String(paramMap.get("type"));
/*  33 */     int i = Util.getIntValue(Util.null2String(paramMap.get("subcompanyid1")), 0);
/*  34 */     String str2 = Util.null2String(paramMap.get("isdata"));
/*  35 */     RecordSet recordSet1 = new RecordSet();
/*  36 */     RecordSet recordSet2 = new RecordSet();
/*  37 */     CommonShareManager commonShareManager = new CommonShareManager();
/*     */     
/*  39 */     String str3 = "";
/*  40 */     if ("mycpt".equals(str1) && !"1".equals(str2)) {
/*  41 */       str3 = " and t1.stateid <> 1 and t1.resourceid in(" + commonShareManager.getContainsSubuserids("" + paramUser.getUID()) + ") ";
/*     */     }
/*  43 */     if ("1".equals(str2)) {
/*  44 */       str3 = " and (t1.cancelled=0 or t1.cancelled is null) ";
/*     */     }
/*     */     
/*  47 */     int j = paramUser.getUserSubCompany1();
/*  48 */     int k = paramUser.getUID();
/*  49 */     int m = 0;
/*  50 */     ArrayList<String> arrayList1 = new ArrayList();
/*  51 */     recordSet1.execute("select cptdetachable from SystemSet");
/*  52 */     if (recordSet1.next()) {
/*  53 */       m = recordSet1.getInt("cptdetachable");
/*     */     }
/*  55 */     if (m == 1 && paramUser.getUID() != 1) {
/*     */       
/*  57 */       char c = Util.getSeparator();
/*  58 */       String str4 = "";
/*  59 */       String str5 = "";
/*  60 */       if (HrmUserVarify.checkUserRight("Capital:Maintenance", paramUser)) {
/*  61 */         str4 = "Capital:Maintenance";
/*     */       }
/*  63 */       String str6 = "";
/*  64 */       recordSet2.executeProc("HrmRoleSR_SeByURId", "" + k + c + str4);
/*  65 */       while (recordSet2.next()) {
/*  66 */         str5 = recordSet2.getString("subcompanyid");
/*  67 */         str6 = str6 + ", " + str5;
/*  68 */         arrayList1.add(str5.trim());
/*     */       } 
/*  70 */       if (!"".equals(str6)) {
/*  71 */         str6 = str6.substring(1);
/*  72 */         str3 = str3 + " and t1.blongsubcompany in (" + str6 + ") ";
/*     */       } else {
/*  74 */         str3 = str3 + " and t1.blongsubcompany in (" + j + ") ";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  79 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/*  82 */     Map<String, Map<String, String>> map = getCptNumOfTree(paramUser, str3, paramString, str2);
/*     */ 
/*     */     
/*  85 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */ 
/*     */     
/*  88 */     ArrayList<String> arrayList3 = new ArrayList();
/*     */     
/*  90 */     this.cptgroup.setTofirstRow();
/*     */     
/*  92 */     while (this.cptgroup.next()) {
/*  93 */       String str4 = this.cptgroup.getAssortmentId();
/*  94 */       String str5 = this.cptgroup.getAssortmentName();
/*  95 */       String str6 = this.cptgroup.getSupAssortmentId();
/*  96 */       int n = Util.getIntValue(Util.null2String(this.cptgroup.getSubcompanyid1()), 0);
/*  97 */       String str7 = "sec_" + str4;
/*  98 */       if (i != 0 && n != i) {
/*     */         continue;
/*     */       }
/*     */       
/* 102 */       if (m == 1 && k != 1 && (
/* 103 */         (arrayList1.size() == 0) ? (
/* 104 */         j != n) : 
/*     */ 
/*     */ 
/*     */         
/* 108 */         !arrayList1.contains(n + ""))) {
/*     */         continue;
/*     */       }
/*     */ 
/*     */       
/* 113 */       SecTreeNode secTreeNode = new SecTreeNode();
/* 114 */       secTreeNode.setKey(str4);
/* 115 */       secTreeNode.setDomid(str7);
/* 116 */       secTreeNode.setPid(str6);
/* 117 */       secTreeNode.setName(Util.toScreen(str5, paramUser.getLanguage()));
/*     */ 
/*     */       
/* 120 */       Map map1 = map.get(str7);
/*     */       
/* 122 */       if (Util.getIntValue((String)map1.get("allNum")) > 0) {
/* 123 */         secTreeNode.setHasRight(true);
/*     */       }
/*     */       
/* 126 */       hashMap1.put(str7, secTreeNode);
/* 127 */       arrayList2.add(str4);
/*     */       
/* 129 */       if (Util.getIntValue(secTreeNode.getPid(), 0) == 0) {
/* 130 */         arrayList3.add(secTreeNode.getKey());
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 136 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 137 */     for (String str4 : map.keySet()) {
/* 138 */       String str5 = str4;
/* 139 */       while (hashMap1.get(str5) != null && hashMap2.get(str5) == null) {
/* 140 */         hashMap2.put(str5, hashMap1.get(str5));
/* 141 */         str5 = "sec_" + ((SecTreeNode)hashMap1.get(str5)).getPid();
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 146 */     for (String str4 : arrayList2) {
/* 147 */       SecTreeNode secTreeNode = (SecTreeNode)hashMap2.get("sec_" + str4);
/* 148 */       if (secTreeNode == null) {
/*     */         continue;
/*     */       }
/*     */       
/* 152 */       if ("mycpt".equals(str1)) {
/* 153 */         String str = (String)((Map)map.get("sec_" + str4)).get("allNum");
/* 154 */         if ("0".equals(str)) {
/*     */           continue;
/*     */         }
/*     */       } 
/*     */       
/* 159 */       if (map.get(secTreeNode.getDomid()) == null) {
/* 160 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 161 */         hashMap.put("domid", secTreeNode.getDomid());
/* 162 */         hashMap.put("keyid", secTreeNode.getKey() + "");
/* 163 */         hashMap.put("allNum", "0");
/* 164 */         hashMap.put("newNum", "0");
/* 165 */         map.put(secTreeNode.getDomid(), hashMap);
/*     */       } 
/*     */       
/* 168 */       String str5 = "sec_" + secTreeNode.getPid();
/* 169 */       if (hashMap2.get(str5) == null) {
/*     */         continue;
/*     */       }
/* 172 */       hashMap2.remove(secTreeNode);
/* 173 */       List<SecTreeNode> list = null;
/* 174 */       if (((SecTreeNode)hashMap2.get(str5)).getChilds() == null) {
/* 175 */         list = new ArrayList();
/* 176 */         ((SecTreeNode)hashMap2.get(str5)).setChilds(list);
/* 177 */         ((SecTreeNode)hashMap2.get(str5)).setHaschild(true);
/*     */       } else {
/* 179 */         ((SecTreeNode)hashMap2.get(str5)).setHaschild(true);
/* 180 */         list = ((SecTreeNode)hashMap2.get(str5)).getChilds();
/*     */       } 
/*     */       
/* 183 */       list.add(secTreeNode);
/*     */     } 
/*     */ 
/*     */     
/* 187 */     ArrayList arrayList = new ArrayList();
/* 188 */     for (String str : arrayList3) {
/* 189 */       if (hashMap2.get("sec_" + str) != null) {
/*     */         
/* 191 */         if ("mycpt".equals(str1)) {
/* 192 */           String str4 = (String)((Map)map.get("sec_" + str)).get("allNum");
/* 193 */           if (!"0".equals(str4))
/* 194 */             arrayList.add(hashMap2.get("sec_" + str)); 
/*     */           continue;
/*     */         } 
/* 197 */         arrayList.add(hashMap2.get("sec_" + str));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 205 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 206 */     hashMap3.put("treedata", arrayList);
/* 207 */     hashMap3.put("treecount", map);
/* 208 */     hashMap3.put("countcfg", getTreeNumColor(paramUser, true));
/* 209 */     return (Map)hashMap3;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getDetachableTree(User paramUser, Map<String, Object> paramMap, String paramString) throws Exception {
/* 214 */     String str1 = Util.null2String(paramMap.get("type"));
/* 215 */     int i = Util.getIntValue(Util.null2String(paramMap.get("subcompanyid1")), 0);
/* 216 */     String str2 = Util.null2String(paramMap.get("isdata"));
/* 217 */     RecordSet recordSet1 = new RecordSet();
/* 218 */     RecordSet recordSet2 = new RecordSet();
/* 219 */     CommonShareManager commonShareManager = new CommonShareManager();
/* 220 */     ArrayList<String> arrayList1 = new ArrayList();
/* 221 */     String str3 = "";
/* 222 */     if ("mycpt".equals(str1) && !"1".equals(str2)) {
/* 223 */       str3 = " and t1.stateid <> 1 and t1.resourceid in(" + commonShareManager.getContainsSubuserids("" + paramUser.getUID()) + ") ";
/*     */     }
/*     */     
/* 226 */     int j = 0;
/* 227 */     recordSet1.execute("select cptdetachable from SystemSet");
/* 228 */     if (recordSet1.next()) {
/* 229 */       j = recordSet1.getInt("cptdetachable");
/*     */     }
/*     */ 
/*     */     
/* 233 */     int k = paramUser.getUID();
/* 234 */     char c = Util.getSeparator();
/* 235 */     String str4 = "";
/*     */     
/* 237 */     String str5 = "";
/* 238 */     ArrayList arrayList2 = new ArrayList();
/* 239 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", paramUser)) {
/* 240 */       str4 = "Capital:Maintenance";
/*     */     }
/* 242 */     String str6 = "";
/* 243 */     recordSet2.executeProc("HrmRoleSR_SeByURId", "" + k + c + str4);
/* 244 */     while (recordSet2.next()) {
/* 245 */       str5 = recordSet2.getString("subcompanyid");
/* 246 */       str6 = str6 + ", " + str5;
/* 247 */       arrayList1.add(str5.trim());
/*     */     } 
/*     */     
/* 250 */     if (j == 1 && paramUser.getUID() != 1 && i == 0) {
/* 251 */       if (!"".equals(str6)) {
/* 252 */         str6 = str6.substring(1);
/* 253 */         str3 = str3 + " and t1.blongsubcompany in (" + str6 + ") ";
/*     */       } else {
/* 255 */         str3 = str3 + " and t1.blongsubcompany in (9999999) ";
/*     */       } 
/* 257 */     } else if (j == 1 && paramUser.getUID() != 1 && i > 0) {
/* 258 */       if (arrayList2.contains(i + "")) {
/* 259 */         str3 = str3 + " and t1.blongsubcompany ='" + i + "' ";
/*     */       } else {
/* 261 */         str3 = str3 + " and t1.blongsubcompany in (9999999) ";
/*     */       }
/*     */     
/* 264 */     } else if (i != 0 && i != -1) {
/* 265 */       str3 = str3 + " and t1.blongsubcompany ='" + i + "' ";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 270 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/* 273 */     Map<String, Map<String, String>> map = getCptNumOfTree(paramUser, str3, paramString, str2);
/*     */ 
/*     */     
/* 276 */     ArrayList<String> arrayList3 = new ArrayList();
/*     */ 
/*     */     
/* 279 */     ArrayList<String> arrayList4 = new ArrayList();
/*     */     
/* 281 */     this.cptgroup.setTofirstRow();
/*     */     
/* 283 */     while (this.cptgroup.next()) {
/* 284 */       String str7 = this.cptgroup.getAssortmentId();
/* 285 */       String str8 = this.cptgroup.getAssortmentName();
/* 286 */       String str9 = this.cptgroup.getSupAssortmentId();
/* 287 */       int m = Util.getIntValue(Util.null2String(this.cptgroup.getSubcompanyid1()), 0);
/* 288 */       String str10 = "sec_" + str7;
/*     */       
/* 290 */       if (i != 0 && m != i) {
/*     */         continue;
/*     */       }
/* 293 */       if (paramUser.getUID() != 1 && 
/* 294 */         j == 1 && !arrayList1.contains(m + "")) {
/*     */         continue;
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 300 */       SecTreeNode secTreeNode = new SecTreeNode();
/* 301 */       secTreeNode.setKey(str7);
/* 302 */       secTreeNode.setDomid(str10);
/* 303 */       secTreeNode.setPid(str9);
/* 304 */       secTreeNode.setName(Util.toScreen(str8, paramUser.getLanguage()));
/*     */ 
/*     */       
/* 307 */       Map map1 = map.get(str10);
/*     */       
/* 309 */       if (Util.getIntValue((String)map1.get("allNum")) > 0) {
/* 310 */         secTreeNode.setHasRight(true);
/*     */       }
/*     */       
/* 313 */       hashMap1.put(str10, secTreeNode);
/* 314 */       arrayList3.add(str7);
/*     */       
/* 316 */       if (Util.getIntValue(secTreeNode.getPid(), 0) == 0) {
/* 317 */         arrayList4.add(secTreeNode.getKey());
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 323 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 324 */     for (String str7 : map.keySet()) {
/* 325 */       String str8 = str7;
/* 326 */       while (hashMap1.get(str8) != null && hashMap2.get(str8) == null) {
/* 327 */         hashMap2.put(str8, hashMap1.get(str8));
/* 328 */         str8 = "sec_" + ((SecTreeNode)hashMap1.get(str8)).getPid();
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 333 */     for (String str7 : arrayList3) {
/* 334 */       SecTreeNode secTreeNode = (SecTreeNode)hashMap2.get("sec_" + str7);
/* 335 */       if (secTreeNode == null) {
/*     */         continue;
/*     */       }
/*     */       
/* 339 */       if ("mycpt".equals(str1)) {
/* 340 */         String str = (String)((Map)map.get("sec_" + str7)).get("allNum");
/* 341 */         if ("0".equals(str)) {
/*     */           continue;
/*     */         }
/*     */       } 
/*     */       
/* 346 */       if (map.get(secTreeNode.getDomid()) == null) {
/* 347 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 348 */         hashMap.put("domid", secTreeNode.getDomid());
/* 349 */         hashMap.put("keyid", secTreeNode.getKey() + "");
/* 350 */         hashMap.put("allNum", "0");
/* 351 */         hashMap.put("newNum", "0");
/* 352 */         map.put(secTreeNode.getDomid(), hashMap);
/*     */       } 
/*     */       
/* 355 */       String str8 = "sec_" + secTreeNode.getPid();
/* 356 */       if (hashMap2.get(str8) == null) {
/*     */         continue;
/*     */       }
/* 359 */       hashMap2.remove(secTreeNode);
/* 360 */       List<SecTreeNode> list = null;
/* 361 */       if (((SecTreeNode)hashMap2.get(str8)).getChilds() == null) {
/* 362 */         list = new ArrayList();
/* 363 */         ((SecTreeNode)hashMap2.get(str8)).setChilds(list);
/* 364 */         ((SecTreeNode)hashMap2.get(str8)).setHaschild(true);
/*     */       } else {
/* 366 */         ((SecTreeNode)hashMap2.get(str8)).setHaschild(true);
/* 367 */         list = ((SecTreeNode)hashMap2.get(str8)).getChilds();
/*     */       } 
/*     */       
/* 370 */       list.add(secTreeNode);
/*     */     } 
/*     */ 
/*     */     
/* 374 */     ArrayList arrayList5 = new ArrayList();
/* 375 */     for (String str : arrayList4) {
/* 376 */       if (hashMap2.get("sec_" + str) != null) {
/*     */         
/* 378 */         if ("mycpt".equals(str1)) {
/* 379 */           String str7 = (String)((Map)map.get("sec_" + str)).get("allNum");
/* 380 */           if (!"0".equals(str7))
/* 381 */             arrayList5.add(hashMap2.get("sec_" + str)); 
/*     */           continue;
/*     */         } 
/* 384 */         arrayList5.add(hashMap2.get("sec_" + str));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 392 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 393 */     hashMap3.put("treedata", arrayList5);
/* 394 */     hashMap3.put("treecount", map);
/* 395 */     hashMap3.put("countcfg", getTreeNumColor(paramUser, true));
/* 396 */     return (Map)hashMap3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Map<String, String>> getCptNumOfTree(User paramUser, String paramString1, String paramString2, String paramString3) throws Exception {
/*     */     Map<String, String> map;
/* 404 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 406 */     this.cptgroup.setTofirstRow();
/* 407 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 408 */     if ("1".equals(paramString3)) {
/* 409 */       map = getCapitalData1Count(paramString1);
/*     */     }
/* 411 */     else if ("cpt_monitorlist".equals(paramString2)) {
/* 412 */       map = getCapitaldata2Count1(paramUser, paramString1);
/*     */     } else {
/* 414 */       map = getCapitaldata2Count(paramUser, paramString1);
/*     */     } 
/*     */     
/* 417 */     while (this.cptgroup.next()) {
/* 418 */       String str1 = this.cptgroup.getAssortmentId();
/*     */       
/* 420 */       String str2 = "0";
/* 421 */       if (map.get(str1) != null) {
/* 422 */         str2 = map.get(str1);
/*     */       }
/* 424 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 425 */       hashMap.put("domid", "sec_" + str1);
/* 426 */       hashMap.put("keyid", str1);
/* 427 */       hashMap.put("allNum", str2);
/* 428 */       hashMap.put("newNum", "0");
/* 429 */       hashMap1.put("sec_" + str1, hashMap);
/*     */     } 
/*     */     
/* 432 */     RecordSet recordSet = new RecordSet();
/* 433 */     recordSet.execute("select a.* from CptCapitalAssortment a where a.id not in(select supassortmentid from CptCapitalAssortment)");
/* 434 */     while (recordSet.next()) {
/* 435 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 436 */       Map map1 = (Map)hashMap1.get("sec_" + str1);
/* 437 */       int i = Util.getIntValue((String)map1.get("allNum"));
/*     */       
/* 439 */       String str2 = Util.null2String(recordSet.getString("supassortmentstr"));
/* 440 */       str2 = str2.substring(str2.indexOf("|") + 1);
/* 441 */       if (str2.length() > 1) {
/* 442 */         str2 = str2.substring(0, str2.length() - 1);
/*     */       }
/* 444 */       String[] arrayOfString = str2.split("\\|");
/* 445 */       for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 446 */         Map<String, String> map2 = (Map)hashMap1.get("sec_" + arrayOfString[j]);
/* 447 */         if (map2 != null) {
/* 448 */           map2.put("allNum", Util.null2String(Integer.valueOf(i + Util.getIntValue(map2.get("allNum")))));
/* 449 */           hashMap1.put("sec_" + arrayOfString[j], map2);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 454 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getTreeNumColor(User paramUser, boolean paramBoolean) {
/* 463 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 465 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 466 */     hashMap1.put("color", "#ff3232");
/* 467 */     hashMap1.put("hovercolor", "#ff3232");
/* 468 */     hashMap1.put("isshow", Boolean.valueOf(paramBoolean));
/* 469 */     hashMap1.put("name", "newNum");
/* 470 */     hashMap1.put("title", SystemEnv.getHtmlLabelName(83438, paramUser.getLanguage()));
/* 471 */     arrayList.add(hashMap1);
/*     */     
/* 473 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 474 */     hashMap2.put("color", "#c5c5c5");
/* 475 */     hashMap2.put("hovercolor", "#c5c5c5");
/* 476 */     hashMap2.put("isshow", Boolean.valueOf(paramBoolean));
/* 477 */     hashMap2.put("name", "allNum");
/* 478 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(31405, paramUser.getLanguage()));
/* 479 */     arrayList.add(hashMap2);
/* 480 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public Map<String, String> getCapitaldata2Count(User paramUser, String paramString) {
/* 484 */     RecordSet recordSet = new RecordSet();
/* 485 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 486 */     this.commonShareManager.setAliasTableName("t2");
/* 487 */     String str = "select count(id) as cnt,t1.capitalgroupid from cptcapital t1 where t1.isdata=2";
/* 488 */     if (!paramUser.getLoginid().equalsIgnoreCase("sysadmin")) {
/* 489 */       str = str + this.commonShareManager.getAssortmentSqlWhere(paramUser);
/*     */     }
/* 491 */     if (!"".equals(Util.null2String(paramString))) {
/* 492 */       str = str + paramString;
/*     */     }
/* 494 */     str = str + " group by t1.capitalgroupid";
/* 495 */     recordSet.execute(str);
/* 496 */     while (recordSet.next()) {
/* 497 */       hashMap.put(Util.null2String(recordSet.getString("capitalgroupid")), Util.null2String(recordSet.getString("cnt")));
/*     */     }
/* 499 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, String> getCapitaldata2Count1(User paramUser, String paramString) {
/* 503 */     RecordSet recordSet = new RecordSet();
/* 504 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 505 */     this.commonShareManager.setAliasTableName("t2");
/* 506 */     String str = "select count(id) as cnt,t1.capitalgroupid from cptcapital t1 where t1.isdata=2 ";
/* 507 */     if (!"".equals(Util.null2String(paramString))) {
/* 508 */       str = str + paramString;
/*     */     }
/* 510 */     str = str + " group by t1.capitalgroupid";
/* 511 */     recordSet.execute(str);
/* 512 */     while (recordSet.next()) {
/* 513 */       hashMap.put(Util.null2String(recordSet.getString("capitalgroupid")), Util.null2String(recordSet.getString("cnt")));
/*     */     }
/* 515 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, String> getCapitalData1Count(String paramString) {
/* 519 */     RecordSet recordSet = new RecordSet();
/* 520 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 521 */     String str = "select count(id) as cnt,t1.capitalgroupid from cptcapital t1 where isdata=1 ";
/* 522 */     if (!"".equals(Util.null2String(paramString))) {
/* 523 */       str = str + paramString;
/*     */     }
/* 525 */     str = str + " group by t1.capitalgroupid";
/* 526 */     recordSet.execute(str);
/* 527 */     while (recordSet.next()) {
/* 528 */       hashMap.put(Util.null2String(recordSet.getString("capitalgroupid")), Util.null2String(recordSet.getString("cnt")));
/*     */     }
/* 530 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public void initAssortmentSub(String paramString) {
/* 534 */     RecordSet recordSet1 = new RecordSet();
/* 535 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 537 */     String str = "update CptCapitalAssortMent set subcompanyid1=" + paramString + " where supassortmentid = 0 and subcompanyid1 is null or subcompanyid1 =-1 or subcompanyid1 =0 or subcompanyid1 = ''";
/* 538 */     if ("postgresql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 539 */       str = "update CptCapitalAssortMent set subcompanyid1=" + paramString + " where supassortmentid = 0 and subcompanyid1 is null or subcompanyid1 =-1 or subcompanyid1 =0 or subcompanyid1 is null";
/*     */     }
/* 541 */     recordSet1.execute(str);
/*     */     
/* 543 */     str = "select * from CptCapitalAssortMent where supassortmentid = 0";
/* 544 */     recordSet1.execute(str);
/* 545 */     while (recordSet1.next()) {
/* 546 */       String str1 = recordSet1.getString("id");
/* 547 */       String str2 = recordSet1.getString("subcompanyid1");
/* 548 */       String str3 = recordSet2.getDBType();
/* 549 */       String str4 = "";
/* 550 */       if ("oracle".equalsIgnoreCase(str3)) {
/* 551 */         str4 = SQLUtil.filteSql(recordSet1.getDBType(), " update CptCapitalAssortMent set subcompanyid1 = " + str2 + " where '|'+supassortmentstr like '|0|" + str1 + "|%'  ");
/* 552 */       } else if ("mysql".equalsIgnoreCase(str3)) {
/* 553 */         str4 = " update CptCapitalAssortMent set subcompanyid1 = " + str2 + " where  CONCAT('|',supassortmentstr) like '|0|" + str1 + "|%'  ";
/* 554 */       } else if ("postgresql".equalsIgnoreCase(str3)) {
/* 555 */         str4 = "update CptCapitalAssortMent set subcompanyid1 = " + str2 + " where  '|'||supassortmentstr like '|0|" + str1 + "|%'  ";
/*     */       } else {
/* 557 */         str4 = "update CptCapitalAssortMent set subcompanyid1 = " + str2 + " where  '|'+supassortmentstr like '|0|" + str1 + "|%'  ";
/*     */       } 
/*     */       
/* 560 */       recordSet2.execute(str4);
/*     */     } 
/* 562 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 563 */     capitalAssortmentComInfo.removeCapitalAssortmentCache();
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 567 */     String str = "0|1|2|";
/* 568 */     str = str.substring(str.indexOf("|") + 1, str.length() - 1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CapitalAssortment.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */