/*     */ package com.api.cpt.util;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptDwrUtil
/*     */   extends BaseBean
/*     */ {
/*  26 */   private SubCompanyComInfo subCompanyComInfo = null;
/*  27 */   private DepartmentComInfo departmentComInfo = null;
/*  28 */   private ResourceComInfo resourceComInfo = null;
/*  29 */   private CapitalAssortmentComInfo capitalAssortmentComInfo = null;
/*  30 */   private CapitalStateComInfo capitalStateComInfo = null;
/*     */ 
/*     */   
/*     */   public CptDwrUtil() {
/*     */     try {
/*  35 */       this.subCompanyComInfo = new SubCompanyComInfo();
/*  36 */       this.departmentComInfo = new DepartmentComInfo();
/*  37 */       this.resourceComInfo = new ResourceComInfo();
/*  38 */       this.capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  39 */       this.capitalStateComInfo = new CapitalStateComInfo();
/*  40 */     } catch (Exception exception) {
/*  41 */       writeLog(exception.getMessage());
/*  42 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap<String, String> getCptInfoMap(String paramString) {
/*  52 */     RecordSet recordSet = new RecordSet();
/*  53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  54 */     recordSet.executeQuery("select t1.*,t2.unitname from cptcapital t1 left outer join LgcAssetUnit t2 on t2.id=t1.unitid where  t1.id= ?", new Object[] { paramString });
/*  55 */     if (recordSet.next()) {
/*  56 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/*  57 */       hashMap.put("sptcount", Util.null2String(recordSet.getString("sptcount")));
/*  58 */       hashMap.put("mark", Util.null2String(recordSet.getString("mark")));
/*  59 */       hashMap.put("capitalgroupid", Util.null2String(recordSet.getString("capitalgroupid")));
/*  60 */       hashMap.put("capitalgroupname_", getBrowserName(this.capitalAssortmentComInfo.getAssortmentName(Util.null2String(recordSet.getString("capitalgroupid"))), Util.null2String(recordSet.getString("capitalgroupid"))));
/*  61 */       hashMap.put("capitalspec", Util.null2String(recordSet.getString("capitalspec")));
/*  62 */       hashMap.put("name", Util.null2String(recordSet.getString("name")));
/*  63 */       hashMap.put("startprice", Util.null2String(recordSet.getString("startprice")));
/*  64 */       hashMap.put("unitid", Util.null2String(recordSet.getString("unitid")));
/*  65 */       hashMap.put("unitname", Util.null2String(recordSet.getString("unitname")));
/*  66 */       hashMap.put("location", Util.null2String(recordSet.getString("location")));
/*  67 */       hashMap.put("stockindate", Util.null2String(recordSet.getString("stockindate")));
/*  68 */       hashMap.put("selectdate", Util.null2String(recordSet.getString("selectdate")));
/*  69 */       hashMap.put("stateid", Util.null2String(recordSet.getString("stateid")));
/*  70 */       hashMap.put("statename", this.capitalStateComInfo.getCapitalStatename(Util.null2String(recordSet.getString("stateid"))));
/*  71 */       hashMap.put("blongsubcompanyid", Util.null2String(recordSet.getString("blongsubcompany")));
/*  72 */       hashMap.put("blongsubcompanyname", this.subCompanyComInfo.getSubCompanyname(Util.null2String(recordSet.getString("blongsubcompany"))));
/*  73 */       hashMap.put("blongdepartmentid", Util.null2String(recordSet.getString("blongdepartment")));
/*  74 */       hashMap.put("blongdepartmentname", this.departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))));
/*  75 */       hashMap.put("blongdepartmentname_", getBrowserName(this.departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))), Util.null2String(recordSet.getString("blongdepartment"))));
/*  76 */       hashMap.put("resourceid", Util.null2String(recordSet.getString("resourceid")));
/*  77 */       hashMap.put("resourcename", this.resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))));
/*  78 */       hashMap.put("resourcename_", getBrowserName(this.resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))), Util.null2String(recordSet.getString("resourceid"))));
/*     */       
/*  80 */       hashMap.put("location", Util.null2String(recordSet.getString("location")));
/*  81 */       hashMap.put("remark", Util.null2String(recordSet.getString("remark")));
/*     */       
/*  83 */       double d1 = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/*  84 */       double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/*  85 */       if (d2 < 0.0D) {
/*  86 */         d2 = 0.0D;
/*     */       }
/*  88 */       double d3 = d1 - d2;
/*  89 */       if (d3 < 0.0D) d3 = 0.0D; 
/*  90 */       hashMap.put("capitalnum", "" + d1);
/*  91 */       hashMap.put("frozennum", "" + d2);
/*  92 */       hashMap.put("availablenum", "" + d3);
/*     */     } 
/*     */     
/*  95 */     return (HashMap)hashMap;
/*     */   }
/*     */   
/*     */   public String getBrowserName(String paramString1, String paramString2) {
/*  99 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getCptWfNodeItem(String paramString1, String paramString2) {
/* 108 */     RecordSet recordSet = new RecordSet();
/* 109 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/* 110 */     if (Util.getIntValue(paramString1) <= 0) {
/* 111 */       return arrayList;
/*     */     }
/* 113 */     Boolean bool = Boolean.valueOf("".equals(paramString2) ? true : ((Util.getIntValue(paramString2) == 0)));
/*     */     
/* 115 */     recordSet.executeQuery("select b.id as triggerNodeId,a.nodeType as triggerNodeType,b.nodeName as triggerNodeName from workflow_flownode a,workflow_nodebase b where (b.IsFreeNode is null or b.IsFreeNode!='1') and a.nodeId=b.id and  a.workFlowId= ? order by a.nodeType,a.nodeId  ", new Object[] { paramString1 });
/* 116 */     while (recordSet.next()) {
/* 117 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 118 */       int i = recordSet.getInt("triggerNodeId");
/* 119 */       int j = recordSet.getInt("triggerNodeType");
/* 120 */       String str = Util.null2String(recordSet.getString("triggerNodeName"));
/*     */       
/* 122 */       hashMap.put("key", i + "");
/* 123 */       hashMap.put("showname", str);
/* 124 */       hashMap.put("nodetype", j + "");
/* 125 */       if (Util.getIntValue(paramString2) == i) {
/* 126 */         hashMap.put("selected", Boolean.valueOf(true));
/* 127 */         bool = Boolean.valueOf(false);
/*     */       } else {
/* 129 */         hashMap.put("selected", Boolean.valueOf(false));
/*     */       } 
/* 131 */       arrayList.add(hashMap);
/*     */     } 
/* 133 */     if (bool.booleanValue() && arrayList.size() > 0) {
/* 134 */       Map<String, Boolean> map = (Map)arrayList.get(arrayList.size() - 1);
/* 135 */       map.put("selected", Boolean.valueOf(true));
/* 136 */       arrayList.remove(arrayList.size() - 1);
/* 137 */       arrayList.add(map);
/*     */     } 
/* 139 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getCptWfLinkItem(String paramString1, String paramString2) {
/* 148 */     RecordSet recordSet = new RecordSet();
/* 149 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/* 150 */     if (Util.getIntValue(paramString1) <= 0) {
/* 151 */       return arrayList;
/*     */     }
/*     */     
/* 154 */     String str = "select id,nodeid,isreject,condition,conditioncn,linkname,destnodeid,nodepasstime,nodepasshour,nodepassminute,isBulidCode,ismustpass,tipsinfo,directionfrom,directionto from workflow_nodelink where wfrequestid is null and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and b.IsFreeNode='1') and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and b.IsFreeNode='1') and workflowid=? order by nodeid,id";
/* 155 */     if (recordSet.getDBType().equals("mysql")) {
/* 156 */       str = "select id,nodeid,isreject,'condition',conditioncn,linkname,destnodeid,nodepasstime,nodepasshour,nodepassminute,isBulidCode,ismustpass,tipsinfo,directionfrom,directionto from workflow_nodelink where wfrequestid is null and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and b.IsFreeNode='1') and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and b.IsFreeNode='1') and workflowid=? order by nodeid,id";
/*     */     }
/* 158 */     recordSet.executeQuery(str, new Object[] { paramString1 });
/* 159 */     Boolean bool = Boolean.valueOf((Util.getIntValue(paramString2) == 0));
/* 160 */     while (recordSet.next()) {
/* 161 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 162 */       int i = recordSet.getInt("id");
/* 163 */       String str1 = Util.null2String(recordSet.getString("linkname"));
/* 164 */       hashMap.put("key", i + "");
/* 165 */       hashMap.put("showname", str1);
/* 166 */       if (Util.getIntValue(paramString2) == i || bool.booleanValue()) {
/* 167 */         hashMap.put("selected", Boolean.valueOf(true));
/* 168 */         bool = Boolean.valueOf(false);
/*     */       } else {
/* 170 */         hashMap.put("selected", Boolean.valueOf(false));
/*     */       } 
/* 172 */       arrayList.add(hashMap);
/*     */     } 
/* 174 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CptDwrUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */