/*     */ package com.api.cpt.util;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptWfConfColumnUtil
/*     */   extends BaseBean
/*     */ {
/*     */   public List<Map<String, Object>> getDetailColumnConf(String paramString, User paramUser) {
/*  29 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  30 */     HashMap<Object, Object> hashMap1 = null;
/*  31 */     ArrayList<Map<String, Object>> arrayList1 = null;
/*  32 */     Map<String, Object> map = null;
/*  33 */     HashMap<Object, Object> hashMap2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*  39 */       if ("apply".equalsIgnoreCase(paramString)) {
/*  40 */         hashMap1 = new HashMap<>();
/*  41 */         arrayList1 = new ArrayList();
/*     */         
/*  43 */         hashMap2 = new HashMap<>();
/*     */         
/*  45 */         map = getFormItemForBrowser("-99991", "3", "wfid", "100", 3, hashMap2, paramUser);
/*  46 */         arrayList1.add(map);
/*  47 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(18015, paramUser.getLanguage()));
/*  48 */         hashMap1.put("com", arrayList1);
/*  49 */         hashMap1.put("width", "9%");
/*  50 */         hashMap1.put("dataIndex", "wfid");
/*  51 */         hashMap1.put("key", "wfid");
/*  52 */         hashMap1.put("isLink", "true");
/*  53 */         hashMap1.put("hastip", "true");
/*  54 */         arrayList.add(hashMap1);
/*     */         
/*  56 */         hashMap2 = new HashMap<>();
/*  57 */         hashMap1 = new HashMap<>();
/*  58 */         arrayList1 = new ArrayList<>();
/*     */         
/*  60 */         hashMap2.put("htmltype", Integer.valueOf(3));
/*  61 */         hashMap2.put("type", Integer.valueOf(1));
/*  62 */         map = getFormItemForBrowser("cptField", "3", "sqr", "100", 3, hashMap2, paramUser);
/*  63 */         arrayList1.add(map);
/*  64 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503101, paramUser.getLanguage()));
/*  65 */         hashMap1.put("com", arrayList1);
/*  66 */         hashMap1.put("width", "9%");
/*  67 */         hashMap1.put("dataIndex", "sqr");
/*  68 */         hashMap1.put("key", "sqr");
/*  69 */         arrayList.add(hashMap1);
/*     */         
/*  71 */         hashMap2 = new HashMap<>();
/*  72 */         hashMap1 = new HashMap<>();
/*  73 */         arrayList1 = new ArrayList<>();
/*     */         
/*  75 */         hashMap2.put("htmltype", Integer.valueOf(3));
/*  76 */         hashMap2.put("type", Integer.valueOf(179));
/*  77 */         map = getFormItemForBrowser("cptField", "3", "zczl", "100", 3, hashMap2, paramUser);
/*  78 */         arrayList1.add(map);
/*  79 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503102, paramUser.getLanguage()));
/*  80 */         hashMap1.put("com", arrayList1);
/*  81 */         hashMap1.put("width", "9%");
/*  82 */         hashMap1.put("dataIndex", "zczl");
/*  83 */         hashMap1.put("key", "zczl");
/*  84 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/*  87 */         hashMap2 = new HashMap<>();
/*  88 */         hashMap1 = new HashMap<>();
/*  89 */         arrayList1 = new ArrayList<>();
/*     */         
/*  91 */         hashMap2.put("htmltype", Integer.valueOf(1));
/*  92 */         hashMap2.put("type", "2,3");
/*  93 */         map = getFormItemForBrowser("cptField", "3", "sl", "100", 3, hashMap2, paramUser);
/*  94 */         arrayList1.add(map);
/*  95 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503103, paramUser.getLanguage()));
/*  96 */         hashMap1.put("com", arrayList1);
/*  97 */         hashMap1.put("width", "9%");
/*  98 */         hashMap1.put("dataIndex", "sl");
/*  99 */         hashMap1.put("key", "sl");
/* 100 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 103 */         hashMap2 = new HashMap<>();
/* 104 */         hashMap1 = new HashMap<>();
/* 105 */         arrayList1 = new ArrayList<>();
/*     */         
/* 107 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 108 */         hashMap2.put("type", Integer.valueOf(3));
/* 109 */         map = getFormItemForBrowser("cptField", "3", "jg", "100", 2, hashMap2, paramUser);
/* 110 */         arrayList1.add(map);
/* 111 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503104, paramUser.getLanguage()));
/* 112 */         hashMap1.put("com", arrayList1);
/* 113 */         hashMap1.put("width", "9%");
/* 114 */         hashMap1.put("dataIndex", "jg");
/* 115 */         hashMap1.put("key", "jg");
/* 116 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 119 */         hashMap2 = new HashMap<>();
/* 120 */         hashMap1 = new HashMap<>();
/* 121 */         arrayList1 = new ArrayList<>();
/*     */         
/* 123 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 124 */         hashMap2.put("type", Integer.valueOf(2));
/* 125 */         map = getFormItemForBrowser("cptField", "3", "rq", "100", 2, hashMap2, paramUser);
/* 126 */         arrayList1.add(map);
/* 127 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(82217, paramUser.getLanguage()));
/* 128 */         hashMap1.put("com", arrayList1);
/* 129 */         hashMap1.put("width", "9%");
/* 130 */         hashMap1.put("dataIndex", "rq");
/* 131 */         hashMap1.put("key", "rq");
/* 132 */         arrayList.add(hashMap1);
/*     */         
/* 134 */         hashMap2 = new HashMap<>();
/* 135 */         hashMap1 = new HashMap<>();
/* 136 */         arrayList1 = new ArrayList<>();
/*     */         
/* 138 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 139 */         hashMap2.put("type", Integer.valueOf(1));
/* 140 */         map = getFormItemForBrowser("cptField", "3", "ggxh", "100", 2, hashMap2, paramUser);
/* 141 */         arrayList1.add(map);
/* 142 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503105, paramUser.getLanguage()));
/* 143 */         hashMap1.put("com", arrayList1);
/* 144 */         hashMap1.put("width", "9%");
/* 145 */         hashMap1.put("dataIndex", "ggxh");
/* 146 */         hashMap1.put("key", "ggxh");
/* 147 */         arrayList.add(hashMap1);
/*     */         
/* 149 */         hashMap2 = new HashMap<>();
/* 150 */         hashMap1 = new HashMap<>();
/* 151 */         arrayList1 = new ArrayList<>();
/*     */         
/* 153 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 154 */         hashMap2.put("type", Integer.valueOf(1));
/* 155 */         map = getFormItemForBrowser("cptField", "3", "cfdd", "100", 2, hashMap2, paramUser);
/* 156 */         arrayList1.add(map);
/* 157 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503106, paramUser.getLanguage()));
/* 158 */         hashMap1.put("com", arrayList1);
/* 159 */         hashMap1.put("width", "9%");
/* 160 */         hashMap1.put("dataIndex", "cfdd");
/* 161 */         hashMap1.put("key", "cfdd");
/* 162 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 165 */         hashMap2 = new HashMap<>();
/* 166 */         hashMap1 = new HashMap<>();
/* 167 */         arrayList1 = new ArrayList<>();
/*     */         
/* 169 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 170 */         hashMap2.put("type", Integer.valueOf(1));
/* 171 */         map = getFormItemForBrowser("cptField", "3", "bz", "100", 2, hashMap2, paramUser);
/* 172 */         map.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 173 */         arrayList1.add(map);
/* 174 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503107, paramUser.getLanguage()));
/* 175 */         hashMap1.put("com", arrayList1);
/* 176 */         hashMap1.put("width", "9%");
/* 177 */         hashMap1.put("dataIndex", "bz");
/* 178 */         hashMap1.put("key", "bz");
/* 179 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 182 */         hashMap1 = new HashMap<>();
/* 183 */         arrayList1 = new ArrayList<>();
/* 184 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503108, paramUser.getLanguage()));
/* 185 */         hashMap1.put("com", arrayList1);
/* 186 */         hashMap1.put("width", "19%");
/* 187 */         hashMap1.put("dataIndex", "jdck");
/* 188 */         hashMap1.put("key", "jdck");
/* 189 */         hashMap1.put("useRecord", Boolean.valueOf(true));
/* 190 */         arrayList.add(hashMap1);
/*     */       }
/* 192 */       else if ("fetch".equalsIgnoreCase(paramString) || "move".equalsIgnoreCase(paramString) || "lend"
/* 193 */         .equalsIgnoreCase(paramString) || "back".equalsIgnoreCase(paramString)) {
/*     */         
/* 195 */         hashMap1 = new HashMap<>();
/* 196 */         arrayList1 = new ArrayList<>();
/*     */         
/* 198 */         hashMap2 = new HashMap<>();
/*     */         
/* 200 */         map = getFormItemForBrowser("-99991", "3", "wfid", "100", 3, hashMap2, paramUser);
/* 201 */         arrayList1.add(map);
/* 202 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(18015, paramUser.getLanguage()));
/* 203 */         hashMap1.put("com", arrayList1);
/* 204 */         hashMap1.put("width", "11%");
/* 205 */         hashMap1.put("dataIndex", "wfid");
/* 206 */         hashMap1.put("key", "wfid");
/* 207 */         hashMap1.put("isLink", "true");
/* 208 */         hashMap1.put("hastip", "true");
/* 209 */         arrayList.add(hashMap1);
/*     */         
/* 211 */         hashMap2 = new HashMap<>();
/* 212 */         hashMap1 = new HashMap<>();
/* 213 */         arrayList1 = new ArrayList<>();
/*     */         
/* 215 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 216 */         hashMap2.put("type", Integer.valueOf(1));
/* 217 */         map = getFormItemForBrowser("cptField", "3", "sqr", "100", 3, hashMap2, paramUser);
/* 218 */         arrayList1.add(map);
/* 219 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503101, paramUser.getLanguage()));
/* 220 */         hashMap1.put("com", arrayList1);
/* 221 */         hashMap1.put("width", "11%");
/* 222 */         hashMap1.put("dataIndex", "sqr");
/* 223 */         hashMap1.put("key", "sqr");
/* 224 */         arrayList.add(hashMap1);
/*     */         
/* 226 */         hashMap2 = new HashMap<>();
/* 227 */         hashMap1 = new HashMap<>();
/* 228 */         arrayList1 = new ArrayList<>();
/*     */         
/* 230 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 231 */         hashMap2.put("type", Integer.valueOf(23));
/* 232 */         map = getFormItemForBrowser("cptField", "3", "zc", "100", 3, hashMap2, paramUser);
/* 233 */         arrayList1.add(map);
/* 234 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(15552, paramUser.getLanguage()));
/* 235 */         hashMap1.put("com", arrayList1);
/* 236 */         hashMap1.put("width", "11%");
/* 237 */         hashMap1.put("dataIndex", "zc");
/* 238 */         hashMap1.put("key", "zc");
/* 239 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 242 */         hashMap2 = new HashMap<>();
/* 243 */         hashMap1 = new HashMap<>();
/* 244 */         arrayList1 = new ArrayList<>();
/*     */         
/* 246 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 247 */         hashMap2.put("type", "2,3");
/* 248 */         map = getFormItemForBrowser("cptField", "3", "sl", "100", 3, hashMap2, paramUser);
/* 249 */         arrayList1.add(map);
/* 250 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503103, paramUser.getLanguage()));
/* 251 */         hashMap1.put("com", arrayList1);
/* 252 */         hashMap1.put("width", "11%");
/* 253 */         hashMap1.put("dataIndex", "sl");
/* 254 */         hashMap1.put("key", "sl");
/* 255 */         arrayList.add(hashMap1);
/*     */         
/* 257 */         hashMap2 = new HashMap<>();
/* 258 */         hashMap1 = new HashMap<>();
/* 259 */         arrayList1 = new ArrayList<>();
/*     */         
/* 261 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 262 */         hashMap2.put("type", Integer.valueOf(2));
/* 263 */         map = getFormItemForBrowser("cptField", "3", "rq", "100", 2, hashMap2, paramUser);
/* 264 */         arrayList1.add(map);
/* 265 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(82217, paramUser.getLanguage()));
/* 266 */         hashMap1.put("com", arrayList1);
/* 267 */         hashMap1.put("width", "11%");
/* 268 */         hashMap1.put("dataIndex", "rq");
/* 269 */         hashMap1.put("key", "rq");
/* 270 */         arrayList.add(hashMap1);
/*     */         
/* 272 */         hashMap2 = new HashMap<>();
/* 273 */         hashMap1 = new HashMap<>();
/* 274 */         arrayList1 = new ArrayList<>();
/*     */         
/* 276 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 277 */         hashMap2.put("type", Integer.valueOf(1));
/* 278 */         map = getFormItemForBrowser("cptField", "3", "cfdd", "100", 2, hashMap2, paramUser);
/* 279 */         arrayList1.add(map);
/* 280 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503106, paramUser.getLanguage()));
/* 281 */         hashMap1.put("com", arrayList1);
/* 282 */         hashMap1.put("width", "11%");
/* 283 */         hashMap1.put("dataIndex", "cfdd");
/* 284 */         hashMap1.put("key", "cfdd");
/* 285 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 288 */         hashMap2 = new HashMap<>();
/* 289 */         hashMap1 = new HashMap<>();
/* 290 */         arrayList1 = new ArrayList<>();
/*     */         
/* 292 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 293 */         hashMap2.put("type", Integer.valueOf(1));
/* 294 */         map = getFormItemForBrowser("cptField", "3", "bz", "100", 2, hashMap2, paramUser);
/* 295 */         map.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 296 */         arrayList1.add(map);
/* 297 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503107, paramUser.getLanguage()));
/* 298 */         hashMap1.put("com", arrayList1);
/* 299 */         hashMap1.put("width", "11%");
/* 300 */         hashMap1.put("dataIndex", "bz");
/* 301 */         hashMap1.put("key", "bz");
/* 302 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 305 */         hashMap1 = new HashMap<>();
/* 306 */         arrayList1 = new ArrayList<>();
/* 307 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503108, paramUser.getLanguage()));
/* 308 */         hashMap1.put("com", arrayList1);
/* 309 */         hashMap1.put("width", "23%");
/* 310 */         hashMap1.put("dataIndex", "jdck");
/* 311 */         hashMap1.put("key", "jdck");
/* 312 */         hashMap1.put("useRecord", Boolean.valueOf(true));
/* 313 */         arrayList.add(hashMap1);
/*     */       }
/* 315 */       else if ("loss".equalsIgnoreCase(paramString) || "discard".equalsIgnoreCase(paramString)) {
/*     */         
/* 317 */         hashMap1 = new HashMap<>();
/* 318 */         arrayList1 = new ArrayList<>();
/*     */         
/* 320 */         hashMap2 = new HashMap<>();
/*     */         
/* 322 */         map = getFormItemForBrowser("-99991", "3", "wfid", "100", 3, hashMap2, paramUser);
/* 323 */         arrayList1.add(map);
/* 324 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(18015, paramUser.getLanguage()));
/* 325 */         hashMap1.put("com", arrayList1);
/* 326 */         hashMap1.put("width", "10%");
/* 327 */         hashMap1.put("dataIndex", "wfid");
/* 328 */         hashMap1.put("key", "wfid");
/* 329 */         hashMap1.put("isLink", "true");
/* 330 */         hashMap1.put("hastip", "true");
/* 331 */         arrayList.add(hashMap1);
/*     */         
/* 333 */         hashMap2 = new HashMap<>();
/* 334 */         hashMap1 = new HashMap<>();
/* 335 */         arrayList1 = new ArrayList<>();
/*     */         
/* 337 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 338 */         hashMap2.put("type", Integer.valueOf(1));
/* 339 */         map = getFormItemForBrowser("cptField", "3", "sqr", "100", 3, hashMap2, paramUser);
/* 340 */         arrayList1.add(map);
/* 341 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503101, paramUser.getLanguage()));
/* 342 */         hashMap1.put("com", arrayList1);
/* 343 */         hashMap1.put("width", "10%");
/* 344 */         hashMap1.put("dataIndex", "sqr");
/* 345 */         hashMap1.put("key", "sqr");
/* 346 */         arrayList.add(hashMap1);
/*     */         
/* 348 */         hashMap2 = new HashMap<>();
/* 349 */         hashMap1 = new HashMap<>();
/* 350 */         arrayList1 = new ArrayList<>();
/*     */         
/* 352 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 353 */         hashMap2.put("type", Integer.valueOf(23));
/* 354 */         map = getFormItemForBrowser("cptField", "3", "zc", "100", 3, hashMap2, paramUser);
/* 355 */         arrayList1.add(map);
/* 356 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(15552, paramUser.getLanguage()));
/* 357 */         hashMap1.put("com", arrayList1);
/* 358 */         hashMap1.put("width", "10%");
/* 359 */         hashMap1.put("dataIndex", "zc");
/* 360 */         hashMap1.put("key", "zc");
/* 361 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 364 */         hashMap2 = new HashMap<>();
/* 365 */         hashMap1 = new HashMap<>();
/* 366 */         arrayList1 = new ArrayList<>();
/*     */         
/* 368 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 369 */         hashMap2.put("type", "2,3");
/* 370 */         map = getFormItemForBrowser("cptField", "3", "sl", "100", 3, hashMap2, paramUser);
/* 371 */         arrayList1.add(map);
/* 372 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503103, paramUser.getLanguage()));
/* 373 */         hashMap1.put("com", arrayList1);
/* 374 */         hashMap1.put("width", "10%");
/* 375 */         hashMap1.put("dataIndex", "sl");
/* 376 */         hashMap1.put("key", "sl");
/* 377 */         arrayList.add(hashMap1);
/*     */         
/* 379 */         hashMap2 = new HashMap<>();
/* 380 */         hashMap1 = new HashMap<>();
/* 381 */         arrayList1 = new ArrayList<>();
/*     */         
/* 383 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 384 */         hashMap2.put("type", Integer.valueOf(3));
/* 385 */         map = getFormItemForBrowser("cptField", "3", "jg", "100", 2, hashMap2, paramUser);
/* 386 */         arrayList1.add(map);
/* 387 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503104, paramUser.getLanguage()));
/* 388 */         hashMap1.put("com", arrayList1);
/* 389 */         hashMap1.put("width", "10%");
/* 390 */         hashMap1.put("dataIndex", "jg");
/* 391 */         hashMap1.put("key", "jg");
/* 392 */         arrayList.add(hashMap1);
/*     */         
/* 394 */         hashMap2 = new HashMap<>();
/* 395 */         hashMap1 = new HashMap<>();
/* 396 */         arrayList1 = new ArrayList<>();
/*     */         
/* 398 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 399 */         hashMap2.put("type", Integer.valueOf(2));
/* 400 */         map = getFormItemForBrowser("cptField", "3", "rq", "100", 2, hashMap2, paramUser);
/* 401 */         arrayList1.add(map);
/* 402 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(82217, paramUser.getLanguage()));
/* 403 */         hashMap1.put("com", arrayList1);
/* 404 */         hashMap1.put("width", "10%");
/* 405 */         hashMap1.put("dataIndex", "rq");
/* 406 */         hashMap1.put("key", "rq");
/* 407 */         arrayList.add(hashMap1);
/*     */         
/* 409 */         hashMap2 = new HashMap<>();
/* 410 */         hashMap1 = new HashMap<>();
/* 411 */         arrayList1 = new ArrayList<>();
/*     */         
/* 413 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 414 */         hashMap2.put("type", Integer.valueOf(1));
/* 415 */         map = getFormItemForBrowser("cptField", "3", "cfdd", "100", 2, hashMap2, paramUser);
/* 416 */         arrayList1.add(map);
/* 417 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503106, paramUser.getLanguage()));
/* 418 */         hashMap1.put("com", arrayList1);
/* 419 */         hashMap1.put("width", "10%");
/* 420 */         hashMap1.put("dataIndex", "cfdd");
/* 421 */         hashMap1.put("key", "cfdd");
/* 422 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 425 */         hashMap2 = new HashMap<>();
/* 426 */         hashMap1 = new HashMap<>();
/* 427 */         arrayList1 = new ArrayList<>();
/*     */         
/* 429 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 430 */         hashMap2.put("type", Integer.valueOf(1));
/* 431 */         map = getFormItemForBrowser("cptField", "3", "bz", "100", 2, hashMap2, paramUser);
/* 432 */         map.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 433 */         arrayList1.add(map);
/* 434 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503107, paramUser.getLanguage()));
/* 435 */         hashMap1.put("com", arrayList1);
/* 436 */         hashMap1.put("width", "10%");
/* 437 */         hashMap1.put("dataIndex", "bz");
/* 438 */         hashMap1.put("key", "bz");
/* 439 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 442 */         hashMap1 = new HashMap<>();
/* 443 */         arrayList1 = new ArrayList<>();
/* 444 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503108, paramUser.getLanguage()));
/* 445 */         hashMap1.put("com", arrayList1);
/* 446 */         hashMap1.put("width", "20%");
/* 447 */         hashMap1.put("dataIndex", "jdck");
/* 448 */         hashMap1.put("key", "jdck");
/* 449 */         hashMap1.put("useRecord", Boolean.valueOf(true));
/* 450 */         arrayList.add(hashMap1);
/*     */       
/*     */       }
/* 453 */       else if ("mend".equalsIgnoreCase(paramString)) {
/*     */         
/* 455 */         hashMap1 = new HashMap<>();
/* 456 */         arrayList1 = new ArrayList<>();
/*     */         
/* 458 */         hashMap2 = new HashMap<>();
/*     */         
/* 460 */         map = getFormItemForBrowser("-99991", "3", "wfid", "100", 3, hashMap2, paramUser);
/* 461 */         arrayList1.add(map);
/* 462 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(18015, paramUser.getLanguage()));
/* 463 */         hashMap1.put("com", arrayList1);
/* 464 */         hashMap1.put("width", "9%");
/* 465 */         hashMap1.put("dataIndex", "wfid");
/* 466 */         hashMap1.put("key", "wfid");
/* 467 */         hashMap1.put("isLink", "true");
/* 468 */         hashMap1.put("hastip", "true");
/* 469 */         arrayList.add(hashMap1);
/*     */         
/* 471 */         hashMap2 = new HashMap<>();
/* 472 */         hashMap1 = new HashMap<>();
/* 473 */         arrayList1 = new ArrayList<>();
/*     */         
/* 475 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 476 */         hashMap2.put("type", Integer.valueOf(1));
/* 477 */         map = getFormItemForBrowser("cptField", "3", "sqr", "100", 3, hashMap2, paramUser);
/* 478 */         arrayList1.add(map);
/* 479 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503101, paramUser.getLanguage()));
/* 480 */         hashMap1.put("com", arrayList1);
/* 481 */         hashMap1.put("width", "9%");
/* 482 */         hashMap1.put("dataIndex", "sqr");
/* 483 */         hashMap1.put("key", "sqr");
/* 484 */         arrayList.add(hashMap1);
/*     */         
/* 486 */         hashMap2 = new HashMap<>();
/* 487 */         hashMap1 = new HashMap<>();
/* 488 */         arrayList1 = new ArrayList<>();
/*     */         
/* 490 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 491 */         hashMap2.put("type", Integer.valueOf(23));
/* 492 */         map = getFormItemForBrowser("cptField", "3", "zc", "100", 3, hashMap2, paramUser);
/* 493 */         arrayList1.add(map);
/* 494 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(15552, paramUser.getLanguage()));
/* 495 */         hashMap1.put("com", arrayList1);
/* 496 */         hashMap1.put("width", "9%");
/* 497 */         hashMap1.put("dataIndex", "zc");
/* 498 */         hashMap1.put("key", "zc");
/* 499 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 502 */         hashMap2 = new HashMap<>();
/* 503 */         hashMap1 = new HashMap<>();
/* 504 */         arrayList1 = new ArrayList<>();
/*     */         
/* 506 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 507 */         hashMap2.put("type", "2,3");
/* 508 */         map = getFormItemForBrowser("cptField", "3", "sl", "100", 3, hashMap2, paramUser);
/* 509 */         arrayList1.add(map);
/* 510 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503103, paramUser.getLanguage()));
/* 511 */         hashMap1.put("com", arrayList1);
/* 512 */         hashMap1.put("width", "9%");
/* 513 */         hashMap1.put("dataIndex", "sl");
/* 514 */         hashMap1.put("key", "sl");
/* 515 */         arrayList.add(hashMap1);
/*     */         
/* 517 */         hashMap2 = new HashMap<>();
/* 518 */         hashMap1 = new HashMap<>();
/* 519 */         arrayList1 = new ArrayList<>();
/*     */         
/* 521 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 522 */         hashMap2.put("type", Integer.valueOf(3));
/* 523 */         map = getFormItemForBrowser("cptField", "3", "jg", "100", 2, hashMap2, paramUser);
/* 524 */         arrayList1.add(map);
/* 525 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503104, paramUser.getLanguage()));
/* 526 */         hashMap1.put("com", arrayList1);
/* 527 */         hashMap1.put("width", "9%");
/* 528 */         hashMap1.put("dataIndex", "jg");
/* 529 */         hashMap1.put("key", "jg");
/* 530 */         arrayList.add(hashMap1);
/*     */         
/* 532 */         hashMap2 = new HashMap<>();
/* 533 */         hashMap1 = new HashMap<>();
/* 534 */         arrayList1 = new ArrayList<>();
/*     */         
/* 536 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 537 */         hashMap2.put("type", Integer.valueOf(2));
/* 538 */         map = getFormItemForBrowser("cptField", "3", "rq", "100", 2, hashMap2, paramUser);
/* 539 */         arrayList1.add(map);
/* 540 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(82217, paramUser.getLanguage()));
/* 541 */         hashMap1.put("com", arrayList1);
/* 542 */         hashMap1.put("width", "9%");
/* 543 */         hashMap1.put("dataIndex", "rq");
/* 544 */         hashMap1.put("key", "rq");
/* 545 */         arrayList.add(hashMap1);
/*     */         
/* 547 */         hashMap2 = new HashMap<>();
/* 548 */         hashMap1 = new HashMap<>();
/* 549 */         arrayList1 = new ArrayList<>();
/*     */         
/* 551 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 552 */         hashMap2.put("type", Integer.valueOf(2));
/* 553 */         map = getFormItemForBrowser("cptField", "3", "wxqx", "100", 2, hashMap2, paramUser);
/* 554 */         arrayList1.add(map);
/* 555 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503117, paramUser.getLanguage()));
/* 556 */         hashMap1.put("com", arrayList1);
/* 557 */         hashMap1.put("width", "9%");
/* 558 */         hashMap1.put("dataIndex", "wxqx");
/* 559 */         hashMap1.put("key", "wxqx");
/* 560 */         arrayList.add(hashMap1);
/*     */         
/* 562 */         hashMap2 = new HashMap<>();
/* 563 */         hashMap1 = new HashMap<>();
/* 564 */         arrayList1 = new ArrayList<>();
/*     */         
/* 566 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 567 */         hashMap2.put("type", Integer.valueOf(7));
/* 568 */         map = getFormItemForBrowser("cptField", "3", "wxdw", "100", 2, hashMap2, paramUser);
/* 569 */         arrayList1.add(map);
/* 570 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503118, paramUser.getLanguage()));
/* 571 */         hashMap1.put("com", arrayList1);
/* 572 */         hashMap1.put("width", "9%");
/* 573 */         hashMap1.put("dataIndex", "wxdw");
/* 574 */         hashMap1.put("key", "wxdw");
/* 575 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 578 */         hashMap2 = new HashMap<>();
/* 579 */         hashMap1 = new HashMap<>();
/* 580 */         arrayList1 = new ArrayList<>();
/*     */         
/* 582 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 583 */         hashMap2.put("type", Integer.valueOf(1));
/* 584 */         map = getFormItemForBrowser("cptField", "3", "bz", "100", 2, hashMap2, paramUser);
/* 585 */         map.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 586 */         arrayList1.add(map);
/* 587 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503107, paramUser.getLanguage()));
/* 588 */         hashMap1.put("com", arrayList1);
/* 589 */         hashMap1.put("width", "9%");
/* 590 */         hashMap1.put("dataIndex", "bz");
/* 591 */         hashMap1.put("key", "bz");
/* 592 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 595 */         hashMap1 = new HashMap<>();
/* 596 */         arrayList1 = new ArrayList<>();
/* 597 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503108, paramUser.getLanguage()));
/* 598 */         hashMap1.put("com", arrayList1);
/* 599 */         hashMap1.put("width", "19%");
/* 600 */         hashMap1.put("dataIndex", "jdck");
/* 601 */         hashMap1.put("key", "jdck");
/* 602 */         hashMap1.put("useRecord", Boolean.valueOf(true));
/* 603 */         arrayList.add(hashMap1);
/*     */       
/*     */       }
/* 606 */       else if ("change".equalsIgnoreCase(paramString)) {
/*     */         
/* 608 */         hashMap1 = new HashMap<>();
/* 609 */         arrayList1 = new ArrayList<>();
/*     */         
/* 611 */         hashMap2 = new HashMap<>();
/*     */         
/* 613 */         map = getFormItemForBrowser("-99991", "3", "wfid", "100", 3, hashMap2, paramUser);
/* 614 */         arrayList1.add(map);
/* 615 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(18015, paramUser.getLanguage()));
/* 616 */         hashMap1.put("com", arrayList1);
/* 617 */         hashMap1.put("width", "7%");
/* 618 */         hashMap1.put("dataIndex", "wfid");
/* 619 */         hashMap1.put("key", "wfid");
/* 620 */         hashMap1.put("isLink", "true");
/* 621 */         hashMap1.put("hastip", "true");
/* 622 */         arrayList.add(hashMap1);
/*     */         
/* 624 */         hashMap2 = new HashMap<>();
/* 625 */         hashMap1 = new HashMap<>();
/* 626 */         arrayList1 = new ArrayList<>();
/*     */         
/* 628 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 629 */         hashMap2.put("type", Integer.valueOf(1));
/* 630 */         map = getFormItemForBrowser("cptField", "3", "sqr", "100", 2, hashMap2, paramUser);
/* 631 */         arrayList1.add(map);
/* 632 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503101, paramUser.getLanguage()));
/* 633 */         hashMap1.put("com", arrayList1);
/* 634 */         hashMap1.put("width", "7%");
/* 635 */         hashMap1.put("dataIndex", "sqr");
/* 636 */         hashMap1.put("key", "sqr");
/* 637 */         arrayList.add(hashMap1);
/*     */         
/* 639 */         hashMap2 = new HashMap<>();
/* 640 */         hashMap1 = new HashMap<>();
/* 641 */         arrayList1 = new ArrayList<>();
/*     */         
/* 643 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 644 */         hashMap2.put("type", Integer.valueOf(23));
/* 645 */         map = getFormItemForBrowser("cptField", "3", "zc", "100", 3, hashMap2, paramUser);
/* 646 */         arrayList1.add(map);
/* 647 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(15552, paramUser.getLanguage()));
/* 648 */         hashMap1.put("com", arrayList1);
/* 649 */         hashMap1.put("width", "7%");
/* 650 */         hashMap1.put("dataIndex", "zc");
/* 651 */         hashMap1.put("key", "zc");
/* 652 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 655 */         hashMap2 = new HashMap<>();
/* 656 */         hashMap1 = new HashMap<>();
/* 657 */         arrayList1 = new ArrayList<>();
/*     */         
/* 659 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 660 */         hashMap2.put("type", "2,3");
/* 661 */         map = getFormItemForBrowser("cptField", "3", "sl", "100", 2, hashMap2, paramUser);
/* 662 */         arrayList1.add(map);
/* 663 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503103, paramUser.getLanguage()));
/* 664 */         hashMap1.put("com", arrayList1);
/* 665 */         hashMap1.put("width", "7%");
/* 666 */         hashMap1.put("dataIndex", "sl");
/* 667 */         hashMap1.put("key", "sl");
/* 668 */         arrayList.add(hashMap1);
/*     */ 
/*     */         
/* 671 */         hashMap2 = new HashMap<>();
/* 672 */         hashMap1 = new HashMap<>();
/* 673 */         arrayList1 = new ArrayList<>();
/*     */         
/* 675 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 676 */         hashMap2.put("type", "25");
/* 677 */         map = getFormItemForBrowser("cptField", "3", "zcz", "100", 2, hashMap2, paramUser);
/* 678 */         arrayList1.add(map);
/* 679 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503119, paramUser.getLanguage()));
/* 680 */         hashMap1.put("com", arrayList1);
/* 681 */         hashMap1.put("width", "7%");
/* 682 */         hashMap1.put("dataIndex", "zcz");
/* 683 */         hashMap1.put("key", "zcz");
/* 684 */         arrayList.add(hashMap1);
/*     */         
/* 686 */         hashMap2 = new HashMap<>();
/* 687 */         hashMap1 = new HashMap<>();
/* 688 */         arrayList1 = new ArrayList<>();
/*     */         
/* 690 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 691 */         hashMap2.put("type", Integer.valueOf(3));
/* 692 */         map = getFormItemForBrowser("cptField", "3", "jg", "100", 2, hashMap2, paramUser);
/* 693 */         arrayList1.add(map);
/* 694 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503104, paramUser.getLanguage()));
/* 695 */         hashMap1.put("com", arrayList1);
/* 696 */         hashMap1.put("width", "7%");
/* 697 */         hashMap1.put("dataIndex", "jg");
/* 698 */         hashMap1.put("key", "jg");
/* 699 */         arrayList.add(hashMap1);
/*     */         
/* 701 */         hashMap2 = new HashMap<>();
/* 702 */         hashMap1 = new HashMap<>();
/* 703 */         arrayList1 = new ArrayList<>();
/*     */         
/* 705 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 706 */         hashMap2.put("type", Integer.valueOf(1));
/* 707 */         map = getFormItemForBrowser("cptField", "3", "ggxh", "100", 2, hashMap2, paramUser);
/* 708 */         arrayList1.add(map);
/* 709 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503105, paramUser.getLanguage()));
/* 710 */         hashMap1.put("com", arrayList1);
/* 711 */         hashMap1.put("width", "7%");
/* 712 */         hashMap1.put("dataIndex", "ggxh");
/* 713 */         hashMap1.put("key", "ggxh");
/* 714 */         arrayList.add(hashMap1);
/*     */         
/* 716 */         hashMap2 = new HashMap<>();
/* 717 */         hashMap1 = new HashMap<>();
/* 718 */         arrayList1 = new ArrayList<>();
/*     */         
/* 720 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 721 */         hashMap2.put("type", Integer.valueOf(1));
/* 722 */         map = getFormItemForBrowser("cptField", "3", "cfdd", "100", 2, hashMap2, paramUser);
/* 723 */         arrayList1.add(map);
/* 724 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503106, paramUser.getLanguage()));
/* 725 */         hashMap1.put("com", arrayList1);
/* 726 */         hashMap1.put("width", "7%");
/* 727 */         hashMap1.put("dataIndex", "cfdd");
/* 728 */         hashMap1.put("key", "cfdd");
/* 729 */         arrayList.add(hashMap1);
/*     */         
/* 731 */         hashMap2 = new HashMap<>();
/* 732 */         hashMap1 = new HashMap<>();
/* 733 */         arrayList1 = new ArrayList<>();
/*     */         
/* 735 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 736 */         hashMap2.put("type", Integer.valueOf(1));
/* 737 */         map = getFormItemForBrowser("cptField", "3", "bz", "100", 2, hashMap2, paramUser);
/* 738 */         map.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 739 */         arrayList1.add(map);
/* 740 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503107, paramUser.getLanguage()));
/* 741 */         hashMap1.put("com", arrayList1);
/* 742 */         hashMap1.put("width", "7%");
/* 743 */         hashMap1.put("dataIndex", "bz");
/* 744 */         hashMap1.put("key", "bz");
/* 745 */         arrayList.add(hashMap1);
/*     */         
/* 747 */         hashMap2 = new HashMap<>();
/* 748 */         hashMap1 = new HashMap<>();
/* 749 */         arrayList1 = new ArrayList<>();
/*     */         
/* 751 */         hashMap2.put("htmltype", Integer.valueOf(1));
/* 752 */         hashMap2.put("type", Integer.valueOf(1));
/* 753 */         map = getFormItemForBrowser("cptField", "3", "cptno", "100", 2, hashMap2, paramUser);
/* 754 */         map.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 755 */         arrayList1.add(map);
/* 756 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(19503, paramUser.getLanguage()));
/* 757 */         hashMap1.put("com", arrayList1);
/* 758 */         hashMap1.put("width", "7%");
/* 759 */         hashMap1.put("dataIndex", "cptno");
/* 760 */         hashMap1.put("key", "cptno");
/* 761 */         arrayList.add(hashMap1);
/*     */         
/* 763 */         hashMap2 = new HashMap<>();
/* 764 */         hashMap1 = new HashMap<>();
/* 765 */         arrayList1 = new ArrayList<>();
/*     */         
/* 767 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 768 */         hashMap2.put("type", Integer.valueOf(2));
/* 769 */         map = getFormItemForBrowser("cptField", "3", "rkrq", "100", 2, hashMap2, paramUser);
/* 770 */         arrayList1.add(map);
/* 771 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503120, paramUser.getLanguage()));
/* 772 */         hashMap1.put("com", arrayList1);
/* 773 */         hashMap1.put("width", "7%");
/* 774 */         hashMap1.put("dataIndex", "rkrq");
/* 775 */         hashMap1.put("key", "rkrq");
/* 776 */         arrayList.add(hashMap1);
/*     */         
/* 778 */         hashMap2 = new HashMap<>();
/* 779 */         hashMap1 = new HashMap<>();
/* 780 */         arrayList1 = new ArrayList<>();
/*     */         
/* 782 */         hashMap2.put("htmltype", Integer.valueOf(3));
/* 783 */         hashMap2.put("type", Integer.valueOf(4));
/* 784 */         map = getFormItemForBrowser("cptField", "3", "ssbm", "100", 2, hashMap2, paramUser);
/* 785 */         arrayList1.add(map);
/* 786 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503121, paramUser.getLanguage()));
/* 787 */         hashMap1.put("com", arrayList1);
/* 788 */         hashMap1.put("width", "7%");
/* 789 */         hashMap1.put("dataIndex", "ssbm");
/* 790 */         hashMap1.put("key", "ssbm");
/* 791 */         arrayList.add(hashMap1);
/*     */ 
/*     */ 
/*     */         
/* 795 */         hashMap1 = new HashMap<>();
/* 796 */         arrayList1 = new ArrayList<>();
/* 797 */         hashMap1.put("title", SystemEnv.getHtmlLabelName(503108, paramUser.getLanguage()));
/* 798 */         hashMap1.put("com", arrayList1);
/* 799 */         hashMap1.put("width", "16%");
/* 800 */         hashMap1.put("dataIndex", "jdck");
/* 801 */         hashMap1.put("key", "jdck");
/* 802 */         hashMap1.put("useRecord", Boolean.valueOf(true));
/* 803 */         arrayList.add(hashMap1);
/*     */       }
/*     */     
/*     */     }
/* 807 */     catch (Exception exception) {
/* 808 */       exception.printStackTrace();
/* 809 */       writeLog(exception.getMessage());
/*     */     } 
/*     */     
/* 812 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private Map<String, Object> getFormItemForBrowser(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, Map paramMap, User paramUser) {
/* 816 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 817 */     hashMap1.put("label", "");
/* 818 */     hashMap1.put("type", "BROWSER");
/* 819 */     hashMap1.put("editType", paramString2);
/* 820 */     hashMap1.put("key", paramString3);
/* 821 */     hashMap1.put("viewAttr", Integer.valueOf(paramInt));
/* 822 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 823 */     hashMap2.put("dataParams", paramMap);
/* 824 */     hashMap2.put("hasAdd", Boolean.valueOf(false));
/* 825 */     hashMap2.put("hasAdvanceSerach", Boolean.valueOf(true));
/* 826 */     hashMap2.put("isAutoComplete", Integer.valueOf(1));
/* 827 */     hashMap2.put("isDetail", Integer.valueOf(0));
/* 828 */     hashMap2.put("isMultCheckbox", Boolean.valueOf(false));
/* 829 */     hashMap2.put("isSingle", Boolean.valueOf(true));
/* 830 */     hashMap2.put("linkUrl", "");
/* 831 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(18214, paramUser.getLanguage()));
/* 832 */     hashMap2.put("type", paramString1);
/*     */     
/* 834 */     hashMap2.put("type", paramString1);
/* 835 */     hashMap2.put("dataParams", paramMap);
/* 836 */     hashMap1.put("browserConditionParam", hashMap2);
/* 837 */     hashMap1.put("width", paramString4);
/* 838 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getDetailColumnDatas(String paramString, User paramUser) {
/* 847 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 848 */     RecordSet recordSet1 = new RecordSet();
/* 849 */     recordSet1.execute("select * from cpt_cptwfconf where wftype = '" + paramString + "' order by id ");
/*     */     
/* 851 */     RecordSet recordSet2 = new RecordSet();
/* 852 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 853 */     recordSet2.execute("select * from workflow_billfield where billid in(select formid from workflow_base where id in(select wfid from cpt_cptwfconf where wftype='" + paramString + "' ))");
/* 854 */     while (recordSet2.next()) {
/* 855 */       hashMap.put(recordSet2.getString("id"), recordSet2.getString("fieldlabel"));
/*     */     }
/*     */ 
/*     */     
/* 859 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 860 */     while (recordSet1.next()) {
/* 861 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 862 */       hashMap1.put("groupid", recordSet1.getString("id"));
/* 863 */       hashMap1.put("wfid", recordSet1.getString("wfid"));
/* 864 */       hashMap1.put("wfidspan", workflowComInfo.getWorkflowname(recordSet1.getString("wfid")));
/* 865 */       hashMap1.put("sqr", recordSet1.getString("sqr"));
/* 866 */       hashMap1.put("sqrspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("sqr"))), paramUser.getLanguage())));
/* 867 */       hashMap1.put("zczl", recordSet1.getString("zczl"));
/* 868 */       hashMap1.put("zczlspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("zczl"))), paramUser.getLanguage())));
/* 869 */       hashMap1.put("zc", recordSet1.getString("zc"));
/* 870 */       hashMap1.put("zcspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("zc"))), paramUser.getLanguage())));
/* 871 */       hashMap1.put("sl", recordSet1.getString("sl"));
/* 872 */       hashMap1.put("slspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("sl"))), paramUser.getLanguage())));
/* 873 */       hashMap1.put("zcz", recordSet1.getString("zcz"));
/* 874 */       hashMap1.put("zczspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("zcz"))), paramUser.getLanguage())));
/* 875 */       hashMap1.put("jg", recordSet1.getString("jg"));
/* 876 */       hashMap1.put("jgspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("jg"))), paramUser.getLanguage())));
/* 877 */       hashMap1.put("rq", recordSet1.getString("rq"));
/* 878 */       hashMap1.put("rqspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("rq"))), paramUser.getLanguage())));
/* 879 */       hashMap1.put("ggxh", recordSet1.getString("ggxh"));
/* 880 */       hashMap1.put("ggxhspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("ggxh"))), paramUser.getLanguage())));
/* 881 */       hashMap1.put("cfdd", recordSet1.getString("cfdd"));
/* 882 */       hashMap1.put("cfddspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("cfdd"))), paramUser.getLanguage())));
/* 883 */       hashMap1.put("bz", recordSet1.getString("bz"));
/* 884 */       hashMap1.put("bzspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("bz"))), paramUser.getLanguage())));
/* 885 */       hashMap1.put("wxqx", recordSet1.getString("wxqx"));
/* 886 */       hashMap1.put("wxqxspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("wxqx"))), paramUser.getLanguage())));
/* 887 */       hashMap1.put("wxdw", recordSet1.getString("wxdw"));
/* 888 */       hashMap1.put("wxdwspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("wxdw"))), paramUser.getLanguage())));
/* 889 */       hashMap1.put("isasync", recordSet1.getString("isasync"));
/* 890 */       hashMap1.put("isasyncspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("isasync"))), paramUser.getLanguage())));
/* 891 */       hashMap1.put("actname", recordSet1.getString("actname"));
/* 892 */       hashMap1.put("actnamespan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("actname"))), paramUser.getLanguage())));
/* 893 */       hashMap1.put("cptno", recordSet1.getString("cptno"));
/* 894 */       hashMap1.put("cptnospan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("cptno"))), paramUser.getLanguage())));
/* 895 */       hashMap1.put("zclx", recordSet1.getString("zclx"));
/* 896 */       hashMap1.put("zclxspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("zclx"))), paramUser.getLanguage())));
/* 897 */       hashMap1.put("rkrq", recordSet1.getString("rkrq"));
/* 898 */       hashMap1.put("rkrqspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("rkrq"))), paramUser.getLanguage())));
/* 899 */       hashMap1.put("ssbm", recordSet1.getString("ssbm"));
/* 900 */       hashMap1.put("ssbmspan", Util.null2String(SystemEnv.getHtmlLabelName(Util.getIntValue((String)hashMap.get(recordSet1.getString("ssbm"))), paramUser.getLanguage())));
/* 901 */       ArrayList<Map<String, Object>> arrayList1 = new ArrayList();
/* 902 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 903 */       arrayList1.add(getFormItemForSelect(recordSet1.getString("actname"), recordSet1.getString("wfid"), paramUser));
/* 904 */       hashMap2.put("jdck", arrayList1);
/* 905 */       hashMap1.put("com", hashMap2);
/* 906 */       arrayList.add(hashMap1);
/*     */     } 
/* 908 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getFormItemForSelect(String paramString1, String paramString2, User paramUser) {
/* 912 */     String[] arrayOfString = paramString1.split(",");
/* 913 */     String str1 = "2";
/* 914 */     String str2 = "0";
/* 915 */     String str3 = "0";
/* 916 */     String str4 = "0";
/* 917 */     if (arrayOfString.length >= 4) {
/* 918 */       str1 = arrayOfString[0];
/* 919 */       str2 = arrayOfString[1];
/* 920 */       str3 = arrayOfString[2];
/* 921 */       str4 = arrayOfString[3];
/*     */     } 
/*     */     
/* 924 */     CptDwrUtil cptDwrUtil = new CptDwrUtil();
/* 925 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 926 */     hashMap1.put("lable", "");
/* 927 */     hashMap1.put("type", "CASCADER");
/* 928 */     hashMap1.put("editType", "3");
/* 929 */     hashMap1.put("key", "jdck");
/* 930 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 931 */     hashMap2.put("type", "select");
/* 932 */     hashMap2.put("key", "acttype");
/* 933 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 934 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 935 */     hashMap3.put("key", "2");
/* 936 */     hashMap3.put("selected", Boolean.valueOf(str1.equals("2")));
/* 937 */     hashMap3.put("showname", SystemEnv.getHtmlLabelName(18214, paramUser.getLanguage()));
/* 938 */     arrayList1.add(hashMap3);
/* 939 */     hashMap3 = new HashMap<>();
/* 940 */     hashMap3.put("key", "1");
/* 941 */     hashMap3.put("selected", Boolean.valueOf(str1.equals("1")));
/* 942 */     hashMap3.put("showname", SystemEnv.getHtmlLabelName(15586, paramUser.getLanguage()));
/* 943 */     arrayList1.add(hashMap3);
/* 944 */     hashMap3 = new HashMap<>();
/* 945 */     hashMap3.put("key", "0");
/* 946 */     hashMap3.put("selected", Boolean.valueOf(str1.equals("0")));
/* 947 */     hashMap3.put("showname", SystemEnv.getHtmlLabelName(15587, paramUser.getLanguage()));
/* 948 */     arrayList1.add(hashMap3);
/*     */     
/* 950 */     hashMap2.put("options", arrayList1);
/*     */     
/* 952 */     hashMap2.put("parent", "");
/*     */     
/* 954 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*     */     
/* 956 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 957 */     hashMap5.put("type", "select");
/* 958 */     hashMap5.put("key", "actlink");
/* 959 */     hashMap5.put("options", cptDwrUtil.getCptWfLinkItem(paramString2, str3));
/* 960 */     hashMap4.put("0", hashMap5);
/*     */ 
/*     */     
/* 963 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 964 */     hashMap5 = new HashMap<>();
/* 965 */     hashMap5.put("type", "select");
/* 966 */     hashMap5.put("key", "actnode");
/* 967 */     hashMap5.put("options", cptDwrUtil.getCptWfNodeItem(paramString2, str2));
/* 968 */     arrayList2.add(hashMap5);
/*     */     
/* 970 */     hashMap5 = new HashMap<>();
/* 971 */     hashMap5.put("type", "select");
/* 972 */     hashMap5.put("key", "actmethod");
/*     */     
/* 974 */     ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/* 975 */     hashMap3 = new HashMap<>();
/* 976 */     hashMap3.put("key", "1");
/* 977 */     hashMap3.put("selected", Boolean.valueOf(str4.equals("1")));
/* 978 */     hashMap3.put("showname", SystemEnv.getHtmlLabelName(19348, paramUser.getLanguage()));
/* 979 */     arrayList3.add(hashMap3);
/*     */     
/* 981 */     hashMap3 = new HashMap<>();
/* 982 */     hashMap3.put("key", "0");
/* 983 */     hashMap3.put("selected", Boolean.valueOf(str4.equals("0")));
/* 984 */     hashMap3.put("showname", SystemEnv.getHtmlLabelName(19349, paramUser.getLanguage()));
/* 985 */     arrayList3.add(hashMap3);
/*     */     
/* 987 */     hashMap5.put("options", arrayList3);
/*     */     
/* 989 */     arrayList2.add(hashMap5);
/* 990 */     hashMap4.put("1", arrayList2);
/*     */     
/* 992 */     hashMap2.put("subChildren", hashMap4);
/* 993 */     hashMap1.put("compDef", hashMap2);
/* 994 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CptWfConfColumnUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */