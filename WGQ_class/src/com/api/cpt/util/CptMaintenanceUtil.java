/*     */ package com.api.cpt.util;
/*     */ 
/*     */ import com.api.crm.util.CrmGeneralUtil;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.cpt.util.CptFieldComInfo;
/*     */ import weaver.formmode.browser.FormModeBrowserUtil;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.proj.util.CodeUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ public class CptMaintenanceUtil {
/*     */   public static final String finalSelectFieldnames = ",isinner,attribute,";
/*     */   public static final String customerMust = ",name,mark,status,capitalgroupid,sptcount,capitaltypeid,unitid,blongsubcompany,resourceid,stptcount,currencyid,alertnum,warehouse,address,";
/*     */   public static final String customerBatch = ",stateid,mark,sptcount,capitalgroupid,resourceid,departmentid,stptcount,alertnum,capitalnum,deprestartdate,warehouse,address,";
/*     */   
/*     */   public Map<String, Object> checkDetachable(User paramUser, String paramString) {
/*  32 */     RecordSet recordSet = new RecordSet();
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  34 */     boolean bool = HrmUserVarify.checkUserRight(paramString, paramUser);
/*  35 */     if (!bool) {
/*  36 */       hashMap.put("notice", Boolean.valueOf(false));
/*     */     } else {
/*  38 */       hashMap.put("notice", Boolean.valueOf(true));
/*     */     } 
/*     */ 
/*     */     
/*  42 */     recordSet.execute("select cptdetachable from SystemSet");
/*  43 */     int i = 0;
/*  44 */     if (recordSet.next()) {
/*  45 */       i = recordSet.getInt("cptdetachable");
/*  46 */       if (i > 0) {
/*  47 */         hashMap.put("cptdetachable", Boolean.valueOf(true));
/*     */       } else {
/*  49 */         hashMap.put("cptdetachable", Boolean.valueOf(false));
/*     */       } 
/*     */     } 
/*  52 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCptViewRight(String paramString, int paramInt1, int paramInt2, User paramUser) {
/*  64 */     boolean bool = false;
/*     */     
/*  66 */     Boolean bool1 = CptFormItemUtil.isAuthorize(paramInt1);
/*  67 */     if (bool1.booleanValue()) {
/*  68 */       RecordSet recordSet1 = new RecordSet();
/*  69 */       CommonShareManager commonShareManager = new CommonShareManager();
/*     */       
/*  71 */       String str1 = "";
/*  72 */       RecordSet recordSet2 = new RecordSet();
/*  73 */       String str2 = "select t1.id,t1.isdata from cptcapital t1 where t1.id=?";
/*  74 */       recordSet2.executeQuery(str2, new Object[] { Integer.valueOf(paramInt2) });
/*  75 */       if (recordSet2.next()) {
/*  76 */         str1 = Util.null2String(recordSet2.getString("isdata"));
/*     */       }
/*  78 */       if (str1.equals("1")) {
/*  79 */         if (HrmUserVarify.checkUserRight("Capital:Maintenance", paramUser)) {
/*  80 */           bool = true;
/*     */         }
/*     */       } else {
/*     */         
/*  84 */         recordSet1.execute(commonShareManager.getSharLevel("cpt", "" + paramInt2, paramUser));
/*  85 */         if (recordSet1.next()) {
/*  86 */           bool = true;
/*     */         }
/*     */       } 
/*     */     } 
/*  90 */     return bool;
/*     */   }
/*     */   
/*     */   public static String paresNull(String paramString1, String paramString2) {
/*  94 */     if ("".equals(paramString2)) {
/*  95 */       if (paramString1.equals("mysql")) {
/*  96 */         return "null";
/*     */       }
/*  98 */       return "''";
/*     */     } 
/*     */     
/* 101 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean checkmark(User paramUser, Map<String, Object> paramMap) {
/* 112 */     RecordSet recordSet = new RecordSet();
/* 113 */     String str1 = Util.null2String(paramMap.get("isdata"));
/* 114 */     String str2 = Util.fromScreen(Util.null2String(paramMap.get("mark")), paramUser.getLanguage());
/* 115 */     CodeUtil codeUtil = new CodeUtil();
/* 116 */     String str3 = Util.null2String(paramMap.get("capitalid"));
/* 117 */     if ("".equals(str1)) {
/* 118 */       recordSet.execute("select isdata from cptcapital where id=" + str3);
/* 119 */       recordSet.next();
/* 120 */       str1 = recordSet.getString("isdata");
/*     */     } 
/* 122 */     String str4 = "1".equals(str1) ? codeUtil.getCptData1CodeUse() : codeUtil.getCptData2CodeUse();
/* 123 */     if ("2".equals(str4)) {
/* 124 */       boolean bool = true;
/* 125 */       String str = "select * from cptcapital where mark='" + str2.trim() + "' ";
/* 126 */       if (!"".equals(str1)) {
/* 127 */         str = str + "and isdata = '" + str1 + "'";
/*     */       }
/* 129 */       if (!"".equals(str3)) {
/* 130 */         str = str + "and id <> " + str3;
/*     */       }
/* 132 */       recordSet.execute(str);
/* 133 */       if (recordSet.next()) {
/* 134 */         bool = false;
/*     */       } else {
/* 136 */         bool = true;
/*     */       } 
/* 138 */       return bool;
/*     */     } 
/* 140 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void updateCusfieldValue(String paramString, Map<String, Object> paramMap, User paramUser) {
/* 149 */     if ("".equals(Util.null2String(paramString)) || paramMap == null || paramUser == null) {
/*     */       return;
/*     */     }
/*     */     try {
/* 153 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 154 */       CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/* 155 */       TreeMap treeMap = cptFieldComInfo.getOpenFieldMap();
/*     */       
/* 157 */       if (treeMap != null && treeMap.size() > 0) {
/* 158 */         RecordSet recordSet1 = new RecordSet();
/* 159 */         RecordSet recordSet2 = new RecordSet();
/* 160 */         recordSet2.isReturnDecryptData(true);
/* 161 */         char c = Util.getSeparator();
/* 162 */         boolean bool = false;
/* 163 */         String str1 = DateHelper.getCurrentDate();
/* 164 */         recordSet2.executeQuery("select * from CptCapital where id=?", new Object[] { paramString });
/* 165 */         if (recordSet2.next()) {
/* 166 */           bool = true;
/*     */         }
/*     */         
/* 169 */         Iterator<Map.Entry> iterator = treeMap.entrySet().iterator();
/* 170 */         String str2 = "";
/*     */         
/* 172 */         ArrayList<String> arrayList = new ArrayList();
/* 173 */         while (iterator.hasNext()) {
/* 174 */           Map.Entry entry = iterator.next();
/* 175 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/* 176 */           int i = Util.getIntValue(jSONObject.getString("type"));
/* 177 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/* 178 */           String str3 = Util.null2String(jSONObject.getString("fielddbtype"));
/* 179 */           String str4 = jSONObject.getString("fieldname");
/* 180 */           String str5 = Util.null2String(paramMap.get(str4));
/* 181 */           String str6 = Util.null2String(recordSet2.getString(str4));
/* 182 */           String str7 = "";
/* 183 */           String str8 = str5;
/*     */           
/* 185 */           if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 186 */             if (str3.toUpperCase().indexOf("INT") >= 0) {
/* 187 */               if ("5".equals(Integer.valueOf(j))) {
/* 188 */                 if (!Util.null2String(str5).equals("")) {
/* 189 */                   str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ",";
/*     */                 } else {
/* 191 */                   str2 = str2 + str4 + " = NULL,";
/*     */                 }
/*     */               
/* 194 */               } else if (!Util.null2String(str5).equals("")) {
/* 195 */                 str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ",";
/*     */               } else {
/* 197 */                 str2 = str2 + str4 + " = NULL,";
/*     */               }
/*     */             
/* 200 */             } else if (str3.toUpperCase().indexOf("NUMBER") >= 0 || str3.toUpperCase().indexOf("FLOAT") >= 0 || str3.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 201 */               int k = str3.indexOf(",");
/* 202 */               int m = 2;
/* 203 */               if (k > -1) {
/* 204 */                 m = Util.getIntValue(str3.substring(k + 1, str3.length() - 1).trim(), 2);
/*     */               } else {
/* 206 */                 m = 2;
/*     */               } 
/* 208 */               if (!Util.null2String(str5).equals("")) {
/* 209 */                 str2 = str2 + str4 + " = " + Util.getPointValue2(str5, m) + ",";
/*     */               } else {
/*     */                 
/* 212 */                 str2 = str2 + str4 + " = NULL,";
/*     */               } 
/* 214 */             } else if (j != 6) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 244 */               String str = "";
/* 245 */               if (j == 3 && (i == 161 || i == 162)) {
/* 246 */                 str = Util.null2String(str5);
/* 247 */                 str = str.trim();
/*     */               }
/* 249 */               else if (j == 2 && i == 2) {
/* 250 */                 str = Util.toHtml100(str5);
/* 251 */                 str = StringHelper.convertSpecialChar2Html(str5);
/* 252 */               } else if (j == 1 && i == 1) {
/* 253 */                 str = StringHelper.convertSpecialChar2Html(str5);
/* 254 */                 str = Util.toHtmlForWorkflow(str);
/* 255 */               } else if (j == 2 && i == 1) {
/* 256 */                 str = Util.StringReplace(str5, " ", "&nbsp;");
/* 257 */                 str = StringHelper.convertSpecialChar2Html(str);
/*     */                 
/* 259 */                 str = Util.toHtmlForWorkflowForMode(str);
/*     */               } else {
/* 261 */                 str = Util.StringReplace(Util.toHtml10(str5), " ", "&nbsp;");
/* 262 */                 str = StringHelper.convertSpecialChar2Html(str);
/* 263 */                 str = Util.toHtmlForWorkflow(str);
/*     */               } 
/*     */               
/* 266 */               str = Util.StringReplace(str, "weaver2017", "+");
/*     */               
/* 268 */               if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 269 */                 str2 = str2 + str4 + " = ?,";
/* 270 */                 arrayList.add(str);
/*     */               } else {
/* 272 */                 str2 = str2 + str4 + " = '" + str + "',";
/*     */               } 
/* 274 */               str8 = str;
/*     */             }
/*     */           
/*     */           }
/* 278 */           else if (str3.toUpperCase().indexOf("INT") >= 0) {
/* 279 */             if ("5".equals(Integer.valueOf(j))) {
/* 280 */               if (!"".equals(str5)) {
/* 281 */                 str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ",";
/*     */               } else {
/* 283 */                 str2 = str2 + str4 + " = NULL,";
/*     */               }
/*     */             
/* 286 */             } else if (!"".equals(str5)) {
/* 287 */               str2 = str2 + str4 + " = " + Util.getIntValue(str5) + ",";
/*     */             } else {
/* 289 */               str2 = str2 + str4 + " = NULL,";
/*     */             }
/*     */           
/* 292 */           } else if (str3.toUpperCase().indexOf("DECIMAL") >= 0 || str3.toUpperCase().indexOf("FLOAT") >= 0) {
/* 293 */             int k = str3.indexOf(",");
/* 294 */             int m = 2;
/* 295 */             if (k > -1) {
/* 296 */               m = Util.getIntValue(str3.substring(k + 1, str3.length() - 1).trim(), 2);
/*     */             } else {
/* 298 */               m = 2;
/*     */             } 
/* 300 */             if (!"".equals(str5)) {
/* 301 */               str2 = str2 + str4 + " = " + Util.getPointValue2(str5, m) + ",";
/*     */             } else {
/* 303 */               str2 = str2 + str4 + " = NULL,";
/*     */             }
/*     */           
/* 306 */           } else if (j != 6) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 338 */             String str = "";
/* 339 */             if (j == 2 && i == 2) {
/* 340 */               str = Util.toHtml100(str5);
/* 341 */               str = StringHelper.convertSpecialChar2Html(str5);
/* 342 */             } else if (j == 1 && i == 1) {
/* 343 */               str = StringHelper.convertSpecialChar2Html(str5);
/* 344 */               str = Util.toHtmlForWorkflow(str);
/* 345 */             } else if (j == 2 && i == 1) {
/* 346 */               str = Util.StringReplace(str5, " ", "&nbsp;");
/* 347 */               str = StringHelper.convertSpecialChar2Html(str);
/*     */               
/* 349 */               str = Util.toHtmlForWorkflowForMode(str);
/* 350 */             } else if (j == 4 && i == 1) {
/* 351 */               str = Util.StringReplace(str5, " ", "&nbsp;");
/* 352 */               str = Util.toHtmlForWorkflow(str);
/* 353 */               if (str.equals("")) {
/* 354 */                 str = "0";
/*     */               }
/*     */             } else {
/* 357 */               str = Util.StringReplace(Util.toHtml10(str5), " ", "&nbsp;");
/* 358 */               str = StringHelper.convertSpecialChar2Html(str);
/* 359 */               str = Util.toHtmlForWorkflow(str);
/*     */             } 
/* 361 */             str = Util.StringReplace(str, "weaver2017", "+");
/*     */             
/* 363 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 364 */               str2 = str2 + str4 + " = ?,";
/* 365 */               arrayList.add(str);
/*     */             } else {
/* 367 */               str2 = str2 + str4 + " = '" + str + "',";
/*     */             } 
/* 369 */             str8 = str;
/*     */           } 
/*     */ 
/*     */           
/* 373 */           boolean bool1 = true;
/* 374 */           if ((2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) && (str8.getBytes("UTF-8")).length > 4000) {
/* 375 */             bool1 = false;
/*     */           }
/*     */ 
/*     */           
/* 379 */           if (!bool1 || str8.equals(str6) || !bool || (
/* 380 */             j == 2 && i == 2 && 
/* 381 */             str8.endsWith("\n") && str8.substring(0, str8.lastIndexOf("\n")).equals(str6))) {
/*     */             continue;
/*     */           }
/*     */           
/* 385 */           str7 = paramString;
/* 386 */           str7 = str7 + c + str4;
/* 387 */           str7 = str7 + c + capitalTransMethod.getFieldValueByType(str6, j, i, str3, Util.null2String(jSONObject.getString("id")));
/* 388 */           str7 = str7 + c + capitalTransMethod.getFieldValueByType(str8, j, i, str3, Util.null2String(jSONObject.getString("id")));
/* 389 */           str7 = str7 + c + "" + paramUser.getUID();
/* 390 */           str7 = str7 + c + str1;
/* 391 */           recordSet1.executeProc("CptCapitalModify_Insert", str7);
/*     */         } 
/*     */         
/* 394 */         if (!str2.equals("")) {
/* 395 */           str2 = str2.substring(0, str2.length() - 1);
/* 396 */           str2 = " update cptcapital set  " + str2 + " where id = " + paramString;
/* 397 */           Object[] arrayOfObject = new Object[arrayList.size()];
/* 398 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 399 */             arrayOfObject[b] = arrayList.get(b);
/*     */           }
/* 401 */           recordSet1.executeSql(str2, false, arrayOfObject);
/*     */         } 
/*     */       } 
/* 404 */     } catch (Exception exception) {
/* 405 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   public static String getAttributeName(String paramString) {
/* 410 */     if (!"".equals(paramString)) {
/* 411 */       String str = "select * from cpt_SelectItem where fieldid = -8 and selectvalue = " + paramString;
/* 412 */       RecordSet recordSet = new RecordSet();
/* 413 */       recordSet.execute(str);
/* 414 */       if (recordSet.next()) {
/* 415 */         return recordSet.getString("selectname");
/*     */       }
/* 417 */       return "";
/*     */     } 
/*     */     
/* 420 */     return "";
/*     */   }
/*     */   
/*     */   public static boolean checkField(String paramString) {
/* 424 */     if (paramString.indexOf("newId") == -1) {
/* 425 */       RecordSet recordSet = new RecordSet();
/* 426 */       recordSet.execute("select fieldname from cptDefineField where id=" + paramString);
/* 427 */       recordSet.first();
/* 428 */       String str = "," + recordSet.getString("fieldname") + ",";
/* 429 */       if (",isinner,attribute,".contains(str)) {
/* 430 */         return false;
/*     */       }
/* 432 */       return true;
/*     */     } 
/*     */     
/* 435 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Object> getFieldTypeValue(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, boolean paramBoolean, User paramUser, String paramString7) {
/* 441 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/* 442 */     ArrayList<String> arrayList = new ArrayList();
/* 443 */     String str1 = "";
/* 444 */     String str2 = "";
/* 445 */     if (paramString3.equals("1") && (paramString4.equals("1") || paramString4.equals("3") || paramString4.equals("5"))) {
/* 446 */       str2 = paramString2.substring(paramString2.indexOf("(") + 1, paramString2.indexOf(")"));
/*     */     }
/* 448 */     if (paramString3.equals("1")) {
/* 449 */       if (paramBoolean) {
/* 450 */         str1 = "input";
/* 451 */         arrayList.add(str1);
/*     */       } else {
/* 453 */         str1 = SystemEnv.getHtmlLabelName(688, paramUser.getLanguage());
/* 454 */         arrayList.add(str1);
/*     */       } 
/* 456 */       if (paramString4.equals("1")) {
/* 457 */         if (paramBoolean) {
/* 458 */           str1 = "text";
/* 459 */           arrayList.add(str1);
/* 460 */           arrayList.add(str2);
/*     */         } else {
/* 462 */           str1 = SystemEnv.getHtmlLabelName(698, paramUser.getLanguage());
/* 463 */           arrayList.add(str1);
/* 464 */           arrayList.add(str2);
/*     */         } 
/* 466 */       } else if (paramString4.equals("2")) {
/* 467 */         if (paramBoolean) {
/* 468 */           str1 = "int";
/* 469 */           arrayList.add(str1);
/*     */         } else {
/* 471 */           str1 = SystemEnv.getHtmlLabelName(696, paramUser.getLanguage());
/* 472 */           arrayList.add(str1);
/*     */         } 
/* 474 */       } else if (paramString4.equals("3")) {
/* 475 */         String str = str2.split(",")[1];
/* 476 */         if (paramBoolean) {
/* 477 */           str1 = "float";
/* 478 */           arrayList.add(str1);
/* 479 */           arrayList.add(str);
/*     */         } else {
/* 481 */           str1 = SystemEnv.getHtmlLabelName(697, paramUser.getLanguage());
/* 482 */           arrayList.add(str1);
/* 483 */           arrayList.add(SystemEnv.getHtmlLabelName(15212, paramUser.getLanguage()));
/* 484 */           arrayList.add(str);
/*     */         } 
/*     */       } 
/* 487 */     } else if (paramString3.equals("2")) {
/* 488 */       if (paramBoolean) {
/* 489 */         str1 = "textarea";
/* 490 */         arrayList.add(str1);
/* 491 */         arrayList.add(paramString5);
/* 492 */         if (paramString4.equals("2")) {
/* 493 */           arrayList.add("1");
/*     */         } else {
/* 495 */           arrayList.add("0");
/*     */         } 
/*     */       } else {
/* 498 */         str1 = SystemEnv.getHtmlLabelName(689, paramUser.getLanguage());
/* 499 */         arrayList.add(str1);
/* 500 */         arrayList.add(SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()));
/* 501 */         arrayList.add(paramString5);
/* 502 */         if (paramString4.equals("2")) {
/* 503 */           arrayList.add(SystemEnv.getHtmlLabelName(503052, paramUser.getLanguage()));
/*     */         }
/*     */       } 
/* 506 */     } else if (paramString3.equals("3")) {
/* 507 */       if (paramBoolean) {
/* 508 */         str1 = "browser";
/* 509 */         arrayList.add(str1);
/* 510 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 511 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 512 */         ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 513 */         String str3 = paramString4;
/* 514 */         String str4 = SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo.getBrowserlabelid(paramString4 + "")), paramUser.getLanguage());
/* 515 */         hashMap1.put("value", str3);
/* 516 */         hashMap1.put("names", str4);
/* 517 */         hashMap2.put("id", str3);
/* 518 */         hashMap2.put("name", str4);
/* 519 */         arrayList1.add(hashMap2);
/* 520 */         hashMap1.put("replaceDatas", arrayList1);
/* 521 */         arrayList.add(hashMap1);
/* 522 */         if ("161".equals(paramString4) || "162".equals(paramString4) || "256".equals(paramString4) || "257".equals(paramString4) || "224".equals(paramString4) || "225".equals(paramString4)) {
/* 523 */           hashMap1 = new HashMap<>();
/* 524 */           hashMap2 = new HashMap<>();
/* 525 */           arrayList1 = new ArrayList<>();
/* 526 */           String str5 = paramString6;
/* 527 */           String str6 = CrmGeneralUtil.getCustomBrowserShowName(paramString6);
/* 528 */           hashMap2.put("id", str5);
/* 529 */           hashMap2.put("name", str6);
/* 530 */           hashMap2.put("namespan", str6);
/* 531 */           hashMap2.put("randomFieldId", str5);
/* 532 */           hashMap2.put("randomFieldIdspan", str6);
/* 533 */           hashMap2.put("showname", str6);
/* 534 */           hashMap2.put("shownamespan", str6);
/* 535 */           hashMap2.put("showtype", Integer.valueOf(1));
/* 536 */           hashMap2.put("showtypespan", "list table mode ");
/* 537 */           arrayList1.add(hashMap2);
/* 538 */           hashMap1.put("value", str5);
/* 539 */           hashMap1.put("names", str6);
/* 540 */           hashMap1.put("replaceDatas", arrayList1);
/* 541 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } else {
/* 544 */         str1 = SystemEnv.getHtmlLabelName(695, paramUser.getLanguage());
/* 545 */         arrayList.add(str1);
/* 546 */         arrayList.add(SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo.getBrowserlabelid(paramString4 + "")), paramUser.getLanguage()));
/* 547 */         if ("161".equals(paramString4) || "162".equals(paramString4) || "256".equals(paramString4) || "257".equals(paramString4) || "224".equals(paramString4) || "225".equals(paramString4)) {
/* 548 */           arrayList.add(paramString2);
/*     */         }
/*     */       } 
/* 551 */     } else if (paramString3.equals("4")) {
/* 552 */       if (paramBoolean) {
/* 553 */         str1 = "check";
/* 554 */         arrayList.add(str1);
/*     */       } else {
/* 556 */         str1 = SystemEnv.getHtmlLabelName(691, paramUser.getLanguage());
/* 557 */         arrayList.add(str1);
/*     */       } 
/* 559 */     } else if (paramString3.equals("5")) {
/* 560 */       if (paramBoolean) {
/* 561 */         str1 = "select";
/* 562 */         arrayList.add(str1);
/* 563 */         arrayList.add(paramString7);
/* 564 */         arrayList.add(paramString1);
/*     */       } else {
/* 566 */         str1 = SystemEnv.getHtmlLabelName(690, paramUser.getLanguage());
/* 567 */         arrayList.add(str1);
/* 568 */         arrayList.add(paramString7);
/*     */       } 
/* 570 */     } else if (paramString3.equals("6")) {
/* 571 */       if (paramBoolean) {
/* 572 */         str1 = "upload";
/* 573 */         arrayList.add(str1);
/*     */       } else {
/* 575 */         str1 = SystemEnv.getHtmlLabelName(17616, paramUser.getLanguage());
/* 576 */         arrayList.add(str1);
/*     */       } 
/*     */     } 
/* 579 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void saveLog(User paramUser, String paramString1, int paramInt1, int paramInt2, String paramString2, String paramString3, String paramString4) {
/* 591 */     RecordSet recordSet = new RecordSet();
/* 592 */     String str1 = TimeUtil.getOnlyCurrentTimeString();
/* 593 */     String str2 = TimeUtil.getCurrentDateString();
/*     */     
/* 595 */     String str3 = "insert into CPT_ToDBLog(submitdate,submittime,submiter,clientip,logtype,submitertype,result) values(?,?,?,?,?,?,?)";
/* 596 */     String str4 = "";
/* 597 */     str4 = paramInt1 + "," + paramInt2 + "," + (paramInt1 - paramInt2);
/*     */     
/* 599 */     recordSet.executeUpdate(str3, new Object[] { str2, str1, Integer.valueOf(paramUser.getUID()), paramString1, paramString3, paramString4, str4 });
/* 600 */     recordSet.executeQuery("select Max(id) logid from CPT_ToDBLog", new Object[0]);
/* 601 */     String str5 = "";
/* 602 */     if (recordSet.next()) {
/* 603 */       str5 = recordSet.getString("logid");
/* 604 */       recordSet.executeUpdate("update CPT_ToDBLogDetail set logid=? where logid = ?", new Object[] { str5, paramString2 });
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CptMaintenanceUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */