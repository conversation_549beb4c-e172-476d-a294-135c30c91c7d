/*    */ package com.api.cpt.util;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.common.util.xtree.TreeNode;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.company.CompanyComInfo;
/*    */ import weaver.hrm.company.SubCompanyComInfo;
/*    */ 
/*    */ 
/*    */ public class CptSubcomTreeUtil
/*    */ {
/*    */   public Map<String, Object> getTree(User paramUser, Map<String, Object> paramMap) throws Exception {
/* 15 */     String str1 = Util.null2String(paramMap.get("type"));
/* 16 */     String str2 = Util.null2String(paramMap.get("id"));
/* 17 */     String str3 = Util.null2String(paramMap.get("nodeid"));
/* 18 */     String str4 = Util.null2String(paramMap.get("init"));
/* 19 */     String str5 = Util.null2String(paramMap.get("subid"));
/*    */     
/* 21 */     String str6 = Util.null2String(paramMap.get("rightStr"));
/* 22 */     TreeNode treeNode = new TreeNode();
/* 23 */     treeNode.setTitle("envelope");
/* 24 */     if (str2.equals("")) {
/* 25 */       CompanyComInfo companyComInfo = new CompanyComInfo();
/* 26 */       TreeNode treeNode1 = new TreeNode();
/* 27 */       String str = companyComInfo.getCompanyname("1");
/* 28 */       treeNode1.setTitle(str);
/* 29 */       treeNode1.setNodeId("com_0");
/* 30 */       treeNode1.setTarget("_self");
/* 31 */       treeNode1.setIcon(GCONST.getContextPath() + "/images/treeimages/global_wev8.gif");
/*    */       
/* 33 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 34 */       subCompanyComInfo.getSubCompanyTreeListByRight(paramUser.getUID(), str6);
/* 35 */       TreeNode treeNode2 = subCompanyComInfo.getSubCompanyTreeListByRight(treeNode, "0", 0, 10, false, "subSingle", null, null);
/* 36 */       treeNode2.toString();
/*    */     } 
/* 38 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/CptSubcomTreeUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */