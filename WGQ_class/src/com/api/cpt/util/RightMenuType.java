/*    */ package com.api.cpt.util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum RightMenuType
/*    */ {
/*  9 */   BTN_SEARCH("197", "icon-coms-search"),
/* 10 */   BTN_COLUMN("32535", "icon-coms-task-list"),
/* 11 */   BTN_STORE("28111", "icon-coms-Collection"),
/* 12 */   BTN_HELP("275", "icon-coms-help"),
/*    */   
/* 14 */   BTN_APPROVE("142", "icon-coms-Approval"),
/* 15 */   BTN_REFUSED("236", "icon-coms-go-back"),
/*    */ 
/*    */   
/* 18 */   BTN_SUBSCRIPTION_SEARCH("18655", "icon-coms-search"),
/* 19 */   BTN_SUBSCRIPTION_HISTORY("17713", "icon-coms-Print-log"),
/* 20 */   BTN_SUBSCRIPTION_APPROVE("17714", "icon-coms-batch"),
/* 21 */   BTN_SUBSCRIPTION_BACK("17715", "icon-coms-Branch"),
/*    */   
/* 23 */   BTN_CREATE("82", "icon-coms-New-Flow"),
/* 24 */   BTN_ASSORT_IMPORT("519188", "icon-coms-leading-in"),
/* 25 */   BTN_ASSORT_EXPORT("132190", "icon-coms-export"),
/* 26 */   BTN_CREATE_SAME("18422", "icon-coms-New-Flow"),
/* 27 */   BTN_CREATE_ASSORT("18423", "icon-coms-New-Flow"),
/* 28 */   BTN_EDIT("93", "icon-coms-edit"),
/* 29 */   BTN_EDIT_BATCH("383518", "icon-coms-edit"),
/* 30 */   BTN_DELETE("91", "icon-coms-delete"),
/* 31 */   BTN_DELETE_BATCH("32136", "icon-coms-delete"),
/* 32 */   BTN_GIVEBACK("1384", "icon-coms-Browse-box-Delete-all"),
/* 33 */   BTN_MEND("83557", "icon-coms-integration-o"),
/* 34 */   BTN_ADD("611", "icon-coms-Batch-add"),
/* 35 */   BTN_SUBMIT("86", "icon-coms-Approval"),
/* 36 */   BTN_BACK("1290", "icon-coms-Return"),
/*    */   
/* 38 */   BTN_SUBSCRIBE("21828", "icon-coms-Workflow-o"),
/* 39 */   BTN_IMPORT_SELECT_TO_DUMMY("21826", "icon-coms-leading-in"),
/* 40 */   BTN_IMPORT_EXCEL("28343", "icon-coms-export"),
/* 41 */   BTN_BACTH_DOWNLOAD_FILE("27105", "icon-coms-down"),
/* 42 */   BTN_MINIATURE_DISPLAY("19119", "icon-coms-Modular-o"),
/* 43 */   BTN_LIST_DISPLAY("15360", "icon-coms-portal-o"),
/* 44 */   BTN_SIGN_READED("18492", "icon-coms-doc"),
/* 45 */   BTN_COPY("77", "icon-coms-form-copy"),
/* 46 */   BTN_MOVE("78", "icon-coms-Revoke"),
/* 47 */   BTN_LOG("83", "icon-coms-Print-log"),
/*    */   
/* 49 */   BTN_ADD_SHARE("611", "icon-coms-Batch-add"),
/* 50 */   BTN_DELETE_SHARE("32136", "icon-coms-Batch-delete");
/*    */   
/*    */   private String labelids;
/*    */   
/*    */   private String icon;
/*    */ 
/*    */   
/*    */   RightMenuType(String paramString1, String paramString2) {
/* 58 */     this.labelids = paramString1;
/* 59 */     this.icon = paramString2;
/*    */   }
/*    */   
/*    */   public String getLabelids() {
/* 63 */     return this.labelids;
/*    */   }
/*    */   
/*    */   public void setLabelids(String paramString) {
/* 67 */     this.labelids = paramString;
/*    */   }
/*    */   
/*    */   public String getIcon() {
/* 71 */     return this.icon;
/*    */   }
/*    */   
/*    */   public void setIcon(String paramString) {
/* 75 */     this.icon = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/RightMenuType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */