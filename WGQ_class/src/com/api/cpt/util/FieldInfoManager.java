/*      */ package com.api.cpt.util;
/*      */ 
/*      */ import com.api.browser.util.BrowserConfigComInfo;
/*      */ import com.engine.cpt.util.CapitalTransMethod;
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpSession;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*      */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*      */ import weaver.cpt.util.CptFieldComInfo;
/*      */ import weaver.cpt.util.CptSettingsComInfo;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.docs.docs.DocComInfo;
/*      */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.interfaces.workflow.browser.BrowserBean;
/*      */ import weaver.proj.Maint.ProjectInfoComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.BrowserComInfo;
/*      */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class FieldInfoManager
/*      */ {
/*      */   public Map getSearchDefFieldInfo() {
/*   45 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*   46 */     RecordSet recordSet = new RecordSet();
/*      */     
/*   48 */     String str1 = "name,mark,capitalspec,fnamark,barcode,capitalgroupid,capitaltypeid,sptcount";
/*   49 */     ArrayList arrayList1 = StringHelper.string2ArrayList(str1, ",");
/*   50 */     String str2 = "blongdepartment,blongsubcompany,departmentid,resourceid,stateid,capitalnum,replacecapitalid,isinner,startdate,enddate,manudate,stockindate,unitid,capitallevel,manufacturer,attribute,location,version,deprestartdate,alertnum,warehouse,address";
/*   51 */     ArrayList arrayList2 = StringHelper.string2ArrayList(str2, ",");
/*   52 */     String str3 = "selectdate,startprice,invoice,contractno,depreyear,deprerate,currencyid,customerid";
/*   53 */     ArrayList arrayList3 = StringHelper.string2ArrayList(str3, ",");
/*   54 */     String str4 = "remark";
/*   55 */     ArrayList arrayList4 = StringHelper.string2ArrayList(str4, ",");
/*      */     
/*   57 */     ArrayList<Map> arrayList5 = new ArrayList();
/*   58 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*   59 */     ArrayList<Map> arrayList6 = new ArrayList();
/*   60 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*   61 */     ArrayList<Map> arrayList7 = new ArrayList();
/*   62 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*   63 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*   64 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/*      */     
/*   66 */     String str5 = " select t1.*,t2.id as fieldid,t2.fieldlabel,t2.fieldhtmltype,t2.type,t2.fielddbtype,t2.issystem from CptSearchDefinition t1,cptDefineField t2  where lower(t1.fieldname)=lower(t2.fieldname) and t1.isconditions = 1 and t1.isseniorconditions = 0 and t1.mouldid = -1 and t1.searchtype='1' and t2.isopen=1 order by t1.displayorder";
/*   67 */     recordSet.execute(str5);
/*   68 */     while (recordSet.next()) {
/*   69 */       String str6 = Util.null2String(recordSet.getString("issystem"));
/*      */       
/*   71 */       String str7 = Util.null2String(recordSet.getString("fieldname")).toLowerCase();
/*   72 */       String str8 = Util.null2String(recordSet.getString("fieldid"));
/*   73 */       String str9 = Util.null2String(recordSet.getString("fieldlabel"));
/*   74 */       String str10 = Util.null2String(recordSet.getString("type"));
/*   75 */       String str11 = Util.null2String(recordSet.getString("fieldhtmltype"));
/*   76 */       String str12 = Util.null2String(recordSet.getString("fielddbtype"));
/*      */       
/*   78 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*   79 */       hashMap.put("fieldname", str7);
/*   80 */       hashMap.put("fieldid", str8);
/*   81 */       hashMap.put("fieldlabel", str9);
/*   82 */       hashMap.put("fieldtype", str10);
/*   83 */       hashMap.put("fieldhtmltype", str11);
/*   84 */       hashMap.put("fielddbtype", str12);
/*   85 */       hashMap.put("issystem", str6);
/*      */       
/*   87 */       if ("1".equals(str6)) {
/*   88 */         if (arrayList1.contains(str7)) {
/*   89 */           hashMap2.put(str7, hashMap); continue;
/*   90 */         }  if (arrayList2.contains(str7)) {
/*   91 */           hashMap3.put(str7, hashMap); continue;
/*   92 */         }  if (arrayList3.contains(str7)) {
/*   93 */           hashMap4.put(str7, hashMap); continue;
/*   94 */         }  if (arrayList4.contains(str7))
/*   95 */           arrayList.add(hashMap); 
/*      */         continue;
/*      */       } 
/*   98 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  101 */     for (String str : arrayList1) {
/*  102 */       if (hashMap2.get(str) != null) {
/*  103 */         Map map = (Map)hashMap2.get(str);
/*  104 */         arrayList5.add(map);
/*      */       } 
/*      */     } 
/*  107 */     for (String str : arrayList2) {
/*  108 */       if (hashMap3.get(str) != null) {
/*  109 */         Map map = (Map)hashMap3.get(str);
/*  110 */         arrayList6.add(map);
/*      */       } 
/*      */     } 
/*  113 */     for (String str : arrayList3) {
/*  114 */       if (hashMap4.get(str) != null) {
/*  115 */         Map map = (Map)hashMap4.get(str);
/*  116 */         arrayList7.add(map);
/*      */       } 
/*      */     } 
/*      */     
/*  120 */     hashMap1.put(ConditionUtil.COMMON_CONDITION, arrayList5);
/*  121 */     hashMap1.put(ConditionUtil.MANAGER_CONDITION, arrayList6);
/*  122 */     hashMap1.put(ConditionUtil.APPLY_CONDITION, arrayList7);
/*  123 */     hashMap1.put(ConditionUtil.OTHER_CONDITION, arrayList);
/*      */     
/*  125 */     return hashMap1;
/*      */   }
/*      */   
/*      */   public Map getSearchDefFieldInfo(String paramString) {
/*  129 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  130 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  132 */     String str1 = "name,mark,capitalspec,fnamark,barcode,capitalgroupid,capitaltypeid,sptcount";
/*  133 */     ArrayList arrayList1 = StringHelper.string2ArrayList(str1, ",");
/*  134 */     String str2 = "blongdepartment,blongsubcompany,departmentid,resourceid,stateid,capitalnum,replacecapitalid,isinner,startdate,enddate,manudate,stockindate,unitid,capitallevel,manufacturer,attribute,location,version,deprestartdate,alertnum,warehouse,address";
/*  135 */     ArrayList arrayList2 = StringHelper.string2ArrayList(str2, ",");
/*  136 */     String str3 = "selectdate,startprice,invoice,contractno,depreyear,deprerate,currencyid,customerid";
/*  137 */     ArrayList arrayList3 = StringHelper.string2ArrayList(str3, ",");
/*  138 */     String str4 = "remark";
/*  139 */     ArrayList arrayList4 = StringHelper.string2ArrayList(str4, ",");
/*      */     
/*  141 */     ArrayList<Map> arrayList5 = new ArrayList();
/*  142 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  143 */     ArrayList<Map> arrayList6 = new ArrayList();
/*  144 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  145 */     ArrayList<Map> arrayList7 = new ArrayList();
/*  146 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  147 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  148 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/*      */     
/*  150 */     String str5 = " select t1.*,t2.id as fieldid,t2.fieldlabel,t2.fieldhtmltype,t2.type,t2.fielddbtype,t2.issystem from CptSearchDefinition t1,cptDefineField t2  where lower(t1.fieldname)=lower(t2.fieldname) and t1.isconditions = 1 and t1.isseniorconditions = 0 and t1.mouldid = -1 and t1.searchtype=? and t2.isopen=1 order by t1.displayorder";
/*  151 */     recordSet.executeQuery(str5, new Object[] { paramString });
/*  152 */     while (recordSet.next()) {
/*  153 */       String str6 = Util.null2String(recordSet.getString("issystem"));
/*      */       
/*  155 */       String str7 = Util.null2String(recordSet.getString("fieldname")).toLowerCase();
/*  156 */       String str8 = Util.null2String(recordSet.getString("fieldid"));
/*  157 */       String str9 = Util.null2String(recordSet.getString("fieldlabel"));
/*  158 */       String str10 = Util.null2String(recordSet.getString("type"));
/*  159 */       String str11 = Util.null2String(recordSet.getString("fieldhtmltype"));
/*  160 */       String str12 = Util.null2String(recordSet.getString("fielddbtype"));
/*      */       
/*  162 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  163 */       hashMap.put("fieldname", str7);
/*  164 */       hashMap.put("fieldid", str8);
/*  165 */       hashMap.put("fieldlabel", str9);
/*  166 */       hashMap.put("fieldtype", str10);
/*  167 */       hashMap.put("fieldhtmltype", str11);
/*  168 */       hashMap.put("fielddbtype", str12);
/*  169 */       hashMap.put("issystem", str6);
/*      */       
/*  171 */       if ("1".equals(str6)) {
/*  172 */         if (arrayList1.contains(str7)) {
/*  173 */           hashMap2.put(str7, hashMap); continue;
/*  174 */         }  if (arrayList2.contains(str7)) {
/*  175 */           hashMap3.put(str7, hashMap); continue;
/*  176 */         }  if (arrayList3.contains(str7)) {
/*  177 */           hashMap4.put(str7, hashMap); continue;
/*  178 */         }  if (arrayList4.contains(str7))
/*  179 */           arrayList.add(hashMap); 
/*      */         continue;
/*      */       } 
/*  182 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  185 */     for (String str : arrayList1) {
/*  186 */       if (hashMap2.get(str) != null) {
/*  187 */         Map map = (Map)hashMap2.get(str);
/*  188 */         arrayList5.add(map);
/*      */       } 
/*      */     } 
/*  191 */     for (String str : arrayList2) {
/*  192 */       if (hashMap3.get(str) != null) {
/*  193 */         Map map = (Map)hashMap3.get(str);
/*  194 */         arrayList6.add(map);
/*      */       } 
/*      */     } 
/*  197 */     for (String str : arrayList3) {
/*  198 */       if (hashMap4.get(str) != null) {
/*  199 */         Map map = (Map)hashMap4.get(str);
/*  200 */         arrayList7.add(map);
/*      */       } 
/*      */     } 
/*      */     
/*  204 */     hashMap1.put(ConditionUtil.COMMON_CONDITION, arrayList5);
/*  205 */     hashMap1.put(ConditionUtil.MANAGER_CONDITION, arrayList6);
/*  206 */     hashMap1.put(ConditionUtil.APPLY_CONDITION, arrayList7);
/*  207 */     hashMap1.put(ConditionUtil.OTHER_CONDITION, arrayList);
/*      */     
/*  209 */     return hashMap1;
/*      */   }
/*      */   
/*      */   public ArrayList getTitleDefFieldInfo() {
/*  213 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  214 */     RecordSet recordSet = new RecordSet();
/*  215 */     String str = "select t1.*,t2.id as fieldid,t2.fieldlabel,t2.fieldhtmltype,t2.type,t2.issystem from CptSearchDefinition t1,cptDefineField t2 where lower(t1.fieldname)=lower(t2.fieldname) and t1.istitle = 1 and t1.mouldid = -1 and t1.searchtype='1' order by t1.displayorder ";
/*  216 */     recordSet.execute(str);
/*  217 */     while (recordSet.next()) {
/*  218 */       String str1 = Util.null2String(recordSet.getString("fieldname"));
/*  219 */       String str2 = Util.null2String(recordSet.getString("fieldid"));
/*  220 */       String str3 = Util.null2String(recordSet.getString("fieldlabel"));
/*  221 */       String str4 = Util.null2String(recordSet.getString("type"));
/*  222 */       String str5 = Util.null2String(recordSet.getString("fieldhtmltype"));
/*  223 */       String str6 = Util.null2String(recordSet.getString("fielddbtype"));
/*  224 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  225 */       hashMap.put("fieldname", str1);
/*  226 */       hashMap.put("fieldid", str2);
/*  227 */       hashMap.put("fieldlabel", str3);
/*  228 */       hashMap.put("fieldtype", str4);
/*  229 */       hashMap.put("fieldhtmltype", str5);
/*  230 */       hashMap.put("fielddbtype", str6);
/*      */       
/*  232 */       arrayList.add(hashMap);
/*      */     } 
/*  234 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserFieldvalue(String paramString1, String paramString2) throws Exception {
/*  245 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  246 */     if (arrayOfString != null && arrayOfString.length >= 4) {
/*  247 */       return getFieldvalue(null, new User(Util.getIntValue(arrayOfString[0])), null, null, Util.getIntValue(arrayOfString[1], 0), Util.getIntValue(arrayOfString[2]), Util.getIntValue(arrayOfString[3]), paramString1, 0, true);
/*      */     }
/*  249 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserCptFieldvalue(String paramString1, String paramString2) throws Exception {
/*  261 */     RecordSet recordSet = new RecordSet();
/*  262 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  263 */     String[] arrayOfString = paramString2.split("\\+");
/*  264 */     String str = "";
/*  265 */     if (arrayOfString.length >= 5) {
/*  266 */       String str1 = arrayOfString[4];
/*  267 */       if ("true".equals(str1)) {
/*  268 */         recordSet.executeQuery("select cancelled from CptCapital where id=? and isdata=1", new Object[] { paramString1 });
/*  269 */         if (recordSet.next() && 
/*  270 */           Util.null2String(recordSet.getString("cancelled")).equals("1")) {
/*  271 */           str = "<a style='color:red' href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard1?capitalid=" + paramString1 + "')\" >" + capitalComInfo.getCapitalname(paramString1) + "(" + SystemEnv.getHtmlLabelName(22205, (new User(Util.getIntValue(arrayOfString[0]))).getLanguage()) + ")</a>";
/*  272 */           return str;
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  277 */     str = "<a href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard1?capitalid=" + paramString1 + "')\" >" + capitalComInfo.getCapitalname(paramString1) + "</a>";
/*  278 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserCptUrl(String paramString1, String paramString2) throws Exception {
/*  289 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  290 */     return "<a href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard?capitalid=" + paramString1 + "')\" >" + capitalComInfo.getCapitalname(paramString1) + "</a>";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserCptFieldvalueForAtuth(String paramString1, String paramString2) throws Exception {
/*  302 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  303 */     return "<a href=\"" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard?capitalid=" + paramString1 + "\" target=\"_blank\">" + capitalComInfo.getCapitalname(paramString1) + "</a>";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserAssortFieldvalue(String paramString1, String paramString2) throws Exception {
/*  316 */     RecordSet recordSet = new RecordSet();
/*  317 */     String str1 = "";
/*  318 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/*  319 */     String str2 = browserComInfo.getBrowsertablename(paramString2.split("\\+")[3] + "");
/*  320 */     String str3 = browserComInfo.getBrowsercolumname(paramString2.split("\\+")[3] + "");
/*  321 */     String str4 = browserComInfo.getBrowserkeycolumname(paramString2.split("\\+")[3] + "");
/*      */     
/*  323 */     String str5 = "select " + str3 + " from " + str2 + " where " + str4 + "=" + paramString1;
/*  324 */     recordSet.executeSql(str5);
/*  325 */     recordSet.next();
/*  326 */     String str6 = recordSet.getString(1);
/*      */     
/*  328 */     str1 = "<a href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/engine.html#/cptmode/CptAssortMentFrame?id=" + paramString1 + "')\" >" + str6 + "</a>";
/*      */ 
/*      */     
/*  331 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserMycptFieldvalue(String paramString1, String paramString2) throws Exception {
/*  341 */     ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/*  342 */     String str = "";
/*  343 */     CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/*  344 */     cptSettingsComInfo.setTofirstRow();
/*  345 */     cptSettingsComInfo.next();
/*  346 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  347 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  348 */       str = str + "<a href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard?capitalid=" + paramString1 + "')\" >" + capitalComInfo.getCapitalname(arrayList.get(b)) + "</a>";
/*  349 */       if ("1".equals(cptSettingsComInfo.getIsopen2()))
/*      */       {
/*  351 */         str = str + "&nbsp;<a href='javaScript:opencptcode(\"" + GCONST.getContextPath() + "/CreateCptBarCode?barType=-1&capitalid=" + arrayList.get(b) + "&date=" + (new Date()).getTime() + "\"," + cptSettingsComInfo.getWidth2() + "," + cptSettingsComInfo.getHeight2() + ")' onclick=\"pointercptXY(event)\"><img src=\"" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png\" onmouseover=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode3_wev8.png'\" onmouseout=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png'\" width=\"16\" height=\"16\" style=\"vertical-align:middle;cursor: pointer;\"></a>";
/*      */       }
/*  353 */       str = str + "&nbsp;";
/*      */     } 
/*  355 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldvalue(HttpSession paramHttpSession, User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3, String paramString3, int paramInt4, boolean paramBoolean) throws Exception {
/*  362 */     RecordSet recordSet = new RecordSet();
/*  363 */     String str = "";
/*      */     
/*  365 */     if (paramInt2 == 3) {
/*  366 */       ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/*  367 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/*      */       
/*  369 */       BrowserConfigComInfo browserConfigComInfo = new BrowserConfigComInfo();
/*  370 */       String str1 = Util.null2String(browserConfigComInfo.getLinkurl(paramInt3 + ""));
/*  371 */       if (paramInt3 > 0 && 
/*  372 */         "".equals(str1)) {
/*  373 */         str1 = Util.null2String(browserComInfo.getLinkurl(paramInt3 + ""));
/*      */       }
/*      */       
/*  376 */       if (paramInt3 == 2 || paramInt3 == 19 || paramInt3 == 226 || paramInt3 == 227) {
/*  377 */         str = paramString3;
/*  378 */       } else if (paramInt3 == 1 || paramInt3 == 17) {
/*  379 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  380 */           if (paramBoolean)
/*      */           {
/*  382 */             str = str + "<a href=\"javascript:openhrm(" + arrayList.get(b) + ")\" onclick=\"pointerXY(event);\" >" + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */           }
/*      */           else
/*      */           {
/*  386 */             str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*      */           }
/*      */         
/*      */         } 
/*  390 */       } else if (paramInt3 == 23 || paramInt3 == 26 || paramInt3 == 3) {
/*  391 */         if ("0".equals(Util.null2String(paramString3))) {
/*  392 */           return "";
/*      */         }
/*  394 */         CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/*  395 */         cptSettingsComInfo.setTofirstRow();
/*  396 */         cptSettingsComInfo.next();
/*  397 */         CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  398 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  399 */           if (paramBoolean) {
/*  400 */             str = str + "<a href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard?capitalid=" + paramString3 + "')\" >" + capitalComInfo.getCapitalname(arrayList.get(b)) + "</a>";
/*  401 */             if ("1".equals(cptSettingsComInfo.getIsopen2()))
/*      */             {
/*  403 */               str = str + "&nbsp;<a href='javaScript:opencptcode(\"" + GCONST.getContextPath() + "/CreateCptBarCode?barType=-1&capitalid=" + arrayList.get(b) + "&date=" + (new Date()).getTime() + "\"," + cptSettingsComInfo.getWidth2() + "," + cptSettingsComInfo.getHeight2() + ")' onclick=\"pointercptXY(event)\"><img src=\"" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png\" onmouseover=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode3_wev8.png'\" onmouseout=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png'\" width=\"16\" height=\"16\" style=\"vertical-align:middle;cursor: pointer;\"></a>";
/*      */             }
/*  405 */             str = str + "&nbsp;";
/*      */           } else {
/*      */             
/*  408 */             str = str + capitalComInfo.getCapitalname(arrayList.get(b)) + ",";
/*      */           }
/*      */         
/*      */         } 
/*  412 */       } else if (paramInt3 == 161 || paramInt3 == 162) {
/*  413 */         str = getDefinedSingle2Multi(paramInt1, null, paramString3);
/*      */       } else {
/*  415 */         String str2 = browserComInfo.getBrowsertablename(paramInt3 + "");
/*  416 */         String str3 = browserComInfo.getBrowsercolumname(paramInt3 + "");
/*  417 */         String str4 = browserComInfo.getBrowserkeycolumname(paramInt3 + "");
/*  418 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  419 */           int i = Util.getIntValue(arrayList.get(b).toString(), 0);
/*  420 */           String str5 = "select " + str3 + " from " + str2 + " where " + str4 + "=" + i;
/*  421 */           recordSet.executeSql(str5);
/*  422 */           recordSet.next();
/*  423 */           String str6 = recordSet.getString(1);
/*  424 */           if (paramBoolean) {
/*      */             
/*  426 */             if (paramInt3 == 258 || paramInt3 == 58 || paramInt3 == 263) {
/*  427 */               str = str + Util.toScreen(str6, paramUser.getLanguage());
/*      */             } else {
/*  429 */               if (!str1.equals("")) {
/*  430 */                 str = str + "<a href=\"javascript:openFullWindowForXtable('" + str1 + i + "')\">";
/*      */               }
/*  432 */               str = str + Util.toScreen(str6, paramUser.getLanguage());
/*  433 */               if (!str1.equals("")) {
/*  434 */                 str = str + "</a>&nbsp;";
/*      */               }
/*      */             } 
/*      */           } else {
/*  438 */             str = str + str6 + ",";
/*      */           } 
/*      */         } 
/*      */       } 
/*  442 */       if (str.endsWith(",")) {
/*  443 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*  445 */     } else if (paramInt2 == 4) {
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  450 */       if ("1".equals(paramString3)) {
/*  451 */         str = "<span style='color:red;'>" + SystemEnv.getHtmlLabelName(163, paramUser.getLanguage()) + "</span>";
/*      */       } else {
/*  453 */         str = SystemEnv.getHtmlLabelName(161, paramUser.getLanguage());
/*      */       } 
/*  455 */     } else if (paramInt2 == 5) {
/*      */       
/*  457 */       recordSet.executeSql("select fieldid,selectvalue,selectname,selectlabel from cpt_SelectItem where fieldid = " + paramInt1 + "  order by listorder,id");
/*  458 */       while (recordSet.next()) {
/*  459 */         int i = recordSet.getInt("fieldid");
/*  460 */         String str1 = Util.null2String(recordSet
/*  461 */             .getString("selectvalue"));
/*  462 */         if (paramHttpSession != null)
/*  463 */           paramUser = (User)paramHttpSession.getAttribute("weaver_user@bean"); 
/*  464 */         String str2 = "";
/*  465 */         if (i == -8 || i == -21) {
/*  466 */           str2 = SystemEnv.getHtmlLabelName(recordSet.getInt("selectlabel"), paramUser.getLanguage());
/*      */         } else {
/*  468 */           str2 = Util.toScreen(recordSet.getString("selectname"), paramUser.getLanguage());
/*      */         } 
/*      */         
/*  471 */         if (str1.equals(paramString3)) {
/*  472 */           str = str + str2;
/*      */         }
/*      */       } 
/*  475 */     } else if (paramInt2 == 6) {
/*  476 */       recordSet.executeSql("select id,docsubject,accessorycount from docdetail where id in(" + paramString3 + ") order by id asc");
/*      */       
/*  478 */       while (recordSet.next()) {
/*  479 */         str = str + recordSet.getString("docsubject") + ",";
/*      */       }
/*  481 */       if (str.endsWith(",")) {
/*  482 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*  484 */     } else if (paramInt2 == 2 && paramInt3 == 1) {
/*  485 */       str = Util.toHtml(paramString3);
/*      */     } else {
/*  487 */       str = paramString3;
/*      */     } 
/*  489 */     return str;
/*      */   }
/*      */   
/*      */   public String getFieldvalue(String paramString1, String paramString2) throws Exception {
/*  493 */     RecordSet recordSet = new RecordSet();
/*  494 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*      */     
/*  496 */     String str = "";
/*      */     
/*  498 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  499 */     if (arrayOfString != null && arrayOfString.length >= 4) {
/*  500 */       int i = Util.getIntValue(arrayOfString[0], 7);
/*  501 */       int j = Util.getIntValue(arrayOfString[1], 0);
/*  502 */       int k = Util.getIntValue(arrayOfString[2]);
/*  503 */       int m = Util.getIntValue(arrayOfString[3]);
/*  504 */       boolean bool = true;
/*  505 */       if (k == 3) {
/*  506 */         ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/*  507 */         BrowserComInfo browserComInfo = new BrowserComInfo();
/*      */         
/*  509 */         BrowserConfigComInfo browserConfigComInfo = new BrowserConfigComInfo();
/*  510 */         String str1 = Util.null2String(browserConfigComInfo.getLinkurl(m + ""));
/*  511 */         if (m > 0 && 
/*  512 */           "".equals(str1)) {
/*  513 */           str1 = Util.null2String(browserComInfo.getLinkurl(m + ""));
/*      */         }
/*      */         
/*  516 */         if (m == 2 || m == 19 || m == 226 || m == 227 || m == 402 || m == 403) {
/*  517 */           str = paramString1;
/*  518 */         } else if (m == 1 || m == 17) {
/*  519 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  520 */             if (bool)
/*      */             {
/*  522 */               str = str + "<a href=\"javascript:openhrm(" + arrayList.get(b) + ")\" onclick=\"pointerXY(event);\" >" + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */             }
/*      */             else
/*      */             {
/*  526 */               str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*      */             }
/*      */           
/*      */           } 
/*  530 */         } else if (m == 23 || m == 26 || m == 3) {
/*  531 */           if ("0".equals(Util.null2String(paramString1))) {
/*  532 */             return "";
/*      */           }
/*  534 */           CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/*  535 */           cptSettingsComInfo.setTofirstRow();
/*  536 */           cptSettingsComInfo.next();
/*  537 */           CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  538 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  539 */             if (bool) {
/*  540 */               str = str + "<a href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard?capitalid=" + paramString1 + "')\" >" + capitalComInfo.getCapitalname(arrayList.get(b)) + "</a>";
/*  541 */               if ("1".equals(cptSettingsComInfo.getIsopen2()))
/*      */               {
/*  543 */                 str = str + "&nbsp;<a href='javaScript:opencptcode(\"" + GCONST.getContextPath() + "/CreateCptBarCode?barType=-1&capitalid=" + arrayList.get(b) + "&date=" + (new Date()).getTime() + "\"," + cptSettingsComInfo.getWidth2() + "," + cptSettingsComInfo.getHeight2() + ")' onclick=\"pointercptXY(event)\"><img src=\"" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png\" onmouseover=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode3_wev8.png'\" onmouseout=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png'\" width=\"16\" height=\"16\" style=\"vertical-align:middle;cursor: pointer;\"></a>";
/*      */               }
/*  545 */               str = str + "&nbsp;";
/*      */             } else {
/*      */               
/*  548 */               str = str + capitalComInfo.getCapitalname(arrayList.get(b)) + ",";
/*      */             }
/*      */           
/*      */           } 
/*  552 */         } else if (m == 161 || m == 162) {
/*  553 */           str = getDefinedSingle2Multi(j, null, paramString1);
/*      */         } else {
/*      */           
/*  556 */           String str2 = browserComInfo.getBrowsertablename(m + "");
/*  557 */           String str3 = browserComInfo.getBrowsercolumname(m + "");
/*  558 */           String str4 = browserComInfo.getBrowserkeycolumname(m + "");
/*  559 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  560 */             int n = Util.getIntValue(arrayList.get(b).toString(), 0);
/*  561 */             String str5 = "select " + str3 + " from " + str2 + " where " + str4 + "=" + n;
/*  562 */             recordSet.execute(str5);
/*  563 */             recordSet.next();
/*  564 */             String str6 = recordSet.getString(1);
/*  565 */             if (bool) {
/*      */               
/*  567 */               if (m == 258 || m == 58 || m == 263) {
/*  568 */                 str = str + Util.toScreen(str6, i);
/*      */               } else {
/*  570 */                 if (!str1.equals("")) {
/*  571 */                   str = str + "<a href=\"javascript:openFullWindowForXtable('" + str1 + n + "')\">";
/*      */                 }
/*  573 */                 str = str + Util.toScreen(str6, i);
/*  574 */                 if (!str1.equals("")) {
/*  575 */                   str = str + "</a>&nbsp;";
/*      */                 } else {
/*  577 */                   str = str + "&nbsp;";
/*      */                 } 
/*      */               } 
/*      */             } else {
/*  581 */               str = str + str6 + ",";
/*      */             } 
/*      */           } 
/*      */         } 
/*  585 */         if (str.endsWith(",")) {
/*  586 */           str = str.substring(0, str.length() - 1);
/*      */         }
/*  588 */       } else if (k == 4) {
/*  589 */         if ("1".equals(paramString1)) {
/*  590 */           str = "<span style='color:red;'>" + SystemEnv.getHtmlLabelName(163, i) + "</span>";
/*      */         } else {
/*  592 */           str = SystemEnv.getHtmlLabelName(161, i);
/*      */         } 
/*  594 */       } else if (k == 5) {
/*      */         
/*  596 */         String str1 = "";
/*  597 */         String str2 = "";
/*  598 */         recordSet.executeQuery("select iscommon,cid from cptDefineField where id=?", new Object[] { Integer.valueOf(j) });
/*  599 */         if (recordSet.next()) {
/*  600 */           str1 = Util.null2String(recordSet.getString("iscommon"));
/*  601 */           str2 = Util.null2String(recordSet.getString("cid"));
/*      */         } 
/*  603 */         if (str1.equals("1")) {
/*  604 */           byte b = 0;
/*  605 */           recordSet.executeQuery("select name from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str2 });
/*  606 */           while (recordSet.next()) {
/*  607 */             if (paramString1.equalsIgnoreCase(b + "")) {
/*  608 */               str = Util.null2String(recordSet.getString("name"));
/*      */               break;
/*      */             } 
/*  611 */             b++;
/*      */           }
/*      */         
/*      */         } else {
/*      */           
/*  616 */           recordSet.execute("select fieldid,selectvalue,selectname,selectlabel from cpt_SelectItem where fieldid = " + j + "  order by listorder,id");
/*  617 */           while (recordSet.next()) {
/*  618 */             int n = recordSet.getInt("fieldid");
/*  619 */             String str3 = Util.null2String(recordSet
/*  620 */                 .getString("selectvalue"));
/*  621 */             String str4 = "";
/*  622 */             if (n == -8 || n == -21) {
/*  623 */               str4 = SystemEnv.getHtmlLabelName(recordSet.getInt("selectlabel"), i);
/*      */             } else {
/*  625 */               str4 = Util.toScreen(recordSet.getString("selectname"), i);
/*      */             } 
/*      */             
/*  628 */             if (str3.equals(paramString1)) {
/*  629 */               str = str + str4;
/*      */             }
/*      */           } 
/*      */         } 
/*  633 */       } else if (k == 6) {
/*  634 */         recordSet.execute("select id,docsubject,accessorycount from docdetail where id in(" + paramString1 + ") order by id asc");
/*  635 */         while (recordSet.next()) {
/*  636 */           str = str + recordSet.getString("docsubject") + ",";
/*      */         }
/*  638 */         if (str.endsWith(",")) {
/*  639 */           str = str.substring(0, str.length() - 1);
/*      */         }
/*  641 */       } else if (k == 2 && m == 1) {
/*  642 */         str = Util.toHtml(paramString1);
/*      */       } else {
/*  644 */         str = paramString1;
/*      */       } 
/*  646 */       if ((k == 1 || (k == 2 && m == 1)) && !paramString1.equals("")) {
/*  647 */         str = CapitalTransMethod.getDesensAndDecryptData(str, arrayOfString[4]);
/*      */       }
/*      */     } else {
/*  650 */       str = paramString1;
/*      */     } 
/*  652 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldvalue(User paramUser, int paramInt, String paramString1, String paramString2) {
/*  660 */     RecordSet recordSet = new RecordSet();
/*  661 */     String str = "";
/*      */     try {
/*  663 */       ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/*  664 */       if (paramInt == 1 || paramInt == 17) {
/*  665 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  667 */           str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  670 */       else if (paramInt == 2 || paramInt == 19) {
/*      */         
/*  672 */         str = str + paramString1;
/*  673 */       } else if (paramInt == 4 || paramInt == 57) {
/*  674 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  676 */           str = str + (new DepartmentComInfo()).getDepartmentname(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  679 */       else if (paramInt == 8 || paramInt == 135) {
/*  680 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  682 */           str = str + (new ProjectInfoComInfo()).getProjectInfoname(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  685 */       else if (paramInt == 7 || paramInt == 18) {
/*  686 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  688 */           str = str + (new CustomerInfoComInfo()).getCustomerInfoname(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  691 */       else if (paramInt == 164) {
/*  692 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  694 */           str = str + (new SubCompanyComInfo()).getSubCompanyname(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  697 */       else if (paramInt == 9) {
/*  698 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  700 */           str = str + (new DocComInfo()).getDocname(arrayList.get(b));
/*      */         }
/*  702 */       } else if (paramInt == 37) {
/*  703 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  705 */           str = str + (new DocComInfo()).getDocname(arrayList.get(b)) + ",";
/*      */         }
/*  707 */       } else if (paramInt == 23) {
/*  708 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  710 */           str = str + (new CapitalComInfo()).getCapitalname(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  713 */       else if (paramInt == 16 || paramInt == 152) {
/*  714 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  716 */           str = str + (new WorkflowRequestComInfo()).getRequestName(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  719 */       else if (paramInt == 142) {
/*  720 */         DocReceiveUnitComInfo docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/*  721 */         for (byte b = 0; b < arrayList.size(); b++)
/*      */         {
/*  723 */           str = str + docReceiveUnitComInfo.getReceiveUnitName(arrayList.get(b)) + ",";
/*      */         }
/*      */       }
/*  726 */       else if (paramInt == 226 || paramInt == 227) {
/*  727 */         str = str + paramString1;
/*  728 */       } else if (paramInt == 161 || paramInt == 162) {
/*  729 */         if (Util.null2String(paramString2).length() == 0) return ""; 
/*      */         try {
/*  731 */           Browser browser = (Browser)StaticObj.getServiceByFullname(paramString2, Browser.class);
/*  732 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  733 */             BrowserBean browserBean = browser.searchById(arrayList.get(b));
/*  734 */             String str1 = Util.null2String(browserBean.getName());
/*  735 */             if (str.equals("")) {
/*  736 */               str = str + str1;
/*      */             } else {
/*  738 */               str = str + "," + str1;
/*      */             } 
/*      */           } 
/*  741 */         } catch (Exception exception) {
/*  742 */           exception.printStackTrace();
/*      */         } 
/*      */       } else {
/*      */         
/*  746 */         String str1 = "";
/*  747 */         String str2 = (new BrowserComInfo()).getBrowsertablename("" + paramInt);
/*      */         
/*  749 */         String str3 = (new BrowserComInfo()).getBrowsercolumname("" + paramInt);
/*      */ 
/*      */         
/*  752 */         String str4 = (new BrowserComInfo()).getBrowserkeycolumname("" + paramInt);
/*  753 */         if (!str3.equals("") && !str2.equals("") && 
/*  754 */           !str4.equals("") && !paramString1.equals("")) {
/*      */           
/*  756 */           str1 = "select " + str3 + " from " + str2 + " where " + str4 + " in(" + paramString1 + ")";
/*      */ 
/*      */           
/*  759 */           recordSet.executeSql(str1);
/*  760 */           while (recordSet.next()) {
/*  761 */             str = str + recordSet.getString(1) + ",";
/*      */           }
/*      */         } 
/*      */       } 
/*  765 */       if (str.endsWith(",")) {
/*  766 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*  768 */     } catch (Exception exception) {
/*  769 */       exception.printStackTrace();
/*      */     } 
/*  771 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSelectFieldvalue(String paramString1, String paramString2) {
/*  779 */     RecordSet recordSet = new RecordSet();
/*  780 */     String str = "";
/*      */     try {
/*  782 */       recordSet.execute("select selectname from crm_selectitem where fieldid = '" + paramString1 + "' and selectvalue = '" + paramString2 + "'");
/*  783 */       if (recordSet.next()) {
/*  784 */         return recordSet.getString("selectname");
/*      */       }
/*  786 */     } catch (Exception exception) {}
/*      */     
/*  788 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSelectFieldvalue(String paramString1, String paramString2, int paramInt) {
/*  796 */     RecordSet recordSet = new RecordSet();
/*  797 */     String str1 = "";
/*      */     
/*  799 */     String str2 = "";
/*  800 */     String str3 = "";
/*  801 */     recordSet.executeQuery("select iscommon,cid from cptDefineField where id=?", new Object[] { paramString1 });
/*  802 */     if (recordSet.next()) {
/*  803 */       str2 = Util.null2String(recordSet.getString("iscommon"));
/*  804 */       str3 = Util.null2String(recordSet.getString("cid"));
/*      */     } 
/*      */     try {
/*  807 */       if (!"".equals(paramString2)) {
/*  808 */         if (str2.equals("1")) {
/*  809 */           byte b = 0;
/*  810 */           recordSet.executeQuery("select name from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str3 });
/*  811 */           while (recordSet.next()) {
/*  812 */             if (paramString2.equalsIgnoreCase(b + "")) {
/*  813 */               str1 = Util.null2String(recordSet.getString("name"));
/*      */               break;
/*      */             } 
/*  816 */             b++;
/*      */           } 
/*      */         } else {
/*      */           
/*  820 */           recordSet.execute("select selectname,selectlabel from cpt_selectitem where fieldid = '" + paramString1 + "' and selectvalue = '" + paramString2 + "'");
/*  821 */           if (recordSet.next()) {
/*  822 */             if (paramString1.equals("-8") || paramString1.equals("-21")) {
/*  823 */               return SystemEnv.getHtmlLabelName(recordSet.getInt("selectlabel"), paramInt);
/*      */             }
/*  825 */             return recordSet.getString("selectname");
/*      */           }
/*      */         
/*      */         } 
/*      */       }
/*  830 */     } catch (Exception exception) {}
/*      */     
/*  832 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap<String, String> getCptInfoMap(String paramString) {
/*  840 */     RecordSet recordSet = new RecordSet();
/*  841 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  842 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  843 */     if ("".equals(paramString)) {
/*  844 */       return (HashMap)hashMap;
/*      */     }
/*  846 */     String str = " select t1.*,t2.unitname from cptcapital t1 left outer join LgcAssetUnit t2 on t2.id=t1.unitid where  t1.id=" + paramString;
/*  847 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  848 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  849 */     ResourceComInfo resourceComInfo = null;
/*      */     try {
/*  851 */       resourceComInfo = new ResourceComInfo();
/*  852 */     } catch (Exception exception) {
/*  853 */       exception.printStackTrace();
/*      */     } 
/*  855 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  856 */     CapitalStateComInfo capitalStateComInfo = new CapitalStateComInfo();
/*  857 */     recordSet.execute(str);
/*  858 */     if (recordSet.next()) {
/*  859 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/*  860 */       hashMap.put("sptcount", Util.null2String(recordSet.getString("sptcount")));
/*  861 */       hashMap.put("mark", Util.null2String(recordSet.getString("cptcapital", "mark", true, true)));
/*  862 */       hashMap.put("capitalgroupid", Util.null2String(recordSet.getString("capitalgroupid")));
/*  863 */       hashMap.put("capitalgroupname_", getBrowserName(capitalAssortmentComInfo.getAssortmentName(Util.null2String(recordSet.getString("capitalgroupid"))), Util.null2String(recordSet.getString("capitalgroupid"))));
/*  864 */       hashMap.put("capitalgroupidspan", getBrowserName(capitalAssortmentComInfo.getAssortmentName(Util.null2String(recordSet.getString("capitalgroupid"))), Util.null2String(recordSet.getString("capitalgroupid"))));
/*  865 */       hashMap.put("capitalspec", Util.null2String(recordSet.getString("cptcapital", "capitalspec", true, true)));
/*  866 */       hashMap.put("name", Util.null2String(recordSet.getString("name")));
/*  867 */       hashMap.put("startprice", Util.null2String(recordSet.getString("cptcapital", "startprice", true, true)));
/*  868 */       hashMap.put("price", Util.null2String(recordSet.getString("cptcapital", "startprice", true, true)));
/*  869 */       hashMap.put("unitid", Util.null2String(recordSet.getString("unitid")));
/*  870 */       hashMap.put("unitname", Util.null2String(recordSet.getString("unitname")));
/*  871 */       hashMap.put("location", Util.null2String(recordSet.getString("cptcapital", "location", true, true)));
/*  872 */       hashMap.put("stockindate", Util.null2String(recordSet.getString("stockindate")));
/*  873 */       hashMap.put("selectdate", Util.null2String(recordSet.getString("selectdate")));
/*  874 */       hashMap.put("stateid", Util.null2String(recordSet.getString("stateid")));
/*  875 */       hashMap.put("statename", capitalStateComInfo.getCapitalStatename(Util.null2String(recordSet.getString("stateid"))));
/*  876 */       hashMap.put("blongsubcompanyid", Util.null2String(recordSet.getString("blongsubcompany")));
/*  877 */       hashMap.put("blongsubcompanyname", subCompanyComInfo.getSubCompanyname(Util.null2String(recordSet.getString("blongsubcompany"))));
/*  878 */       hashMap.put("blongsubcompanyidspan", subCompanyComInfo.getSubCompanyname(Util.null2String(recordSet.getString("blongsubcompany"))));
/*  879 */       hashMap.put("blongdepartmentid", Util.null2String(recordSet.getString("blongdepartment")));
/*  880 */       hashMap.put("blongdepartmentidspan", departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))));
/*  881 */       hashMap.put("blongdepartmentname", departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))));
/*  882 */       hashMap.put("blongdepartmentname_", getBrowserName(departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))), Util.null2String(recordSet.getString("blongdepartment"))));
/*  883 */       hashMap.put("resourceid", Util.null2String(recordSet.getString("resourceid")));
/*  884 */       hashMap.put("resourceidspan", resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))));
/*  885 */       hashMap.put("resourcename", resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))));
/*  886 */       hashMap.put("resourcename_", getBrowserName(resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))), Util.null2String(recordSet.getString("resourceid"))));
/*  887 */       hashMap.put("location", Util.null2String(recordSet.getString("cptcapital", "location", true, true)));
/*  888 */       hashMap.put("remark", Util.null2String(recordSet.getString("cptcapital", "remark", true, true)));
/*  889 */       if (capitalTransMethod.IsWareHouseOpen()) {
/*  890 */         hashMap.put("warehouse", Util.null2String(recordSet.getString("warehouse")));
/*  891 */         hashMap.put("warehousespan", capitalTransMethod.getWareHouseName(Util.null2String(recordSet.getString("warehouse"))));
/*  892 */         hashMap.put("warehousename", capitalTransMethod.getWareHouseName(Util.null2String(recordSet.getString("warehouse"))));
/*      */       } 
/*      */       
/*  895 */       double d1 = Util.getDoubleValue(recordSet.getString("cptcapital", "capitalnum", true, true), 0.0D);
/*  896 */       double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/*  897 */       if (d2 < 0.0D) {
/*  898 */         d2 = 0.0D;
/*      */       }
/*  900 */       double d3 = d1 - d2;
/*  901 */       if (d3 < 0.0D) d3 = 0.0D; 
/*  902 */       if ("1".equals(Util.null2String(recordSet.getString("sptcount")))) {
/*  903 */         hashMap.put("capitalcount", "1");
/*      */       }
/*  905 */       hashMap.put("capitalnum", "" + d1);
/*  906 */       hashMap.put("frozennum", "" + d2);
/*  907 */       hashMap.put("availablenum", "" + d3);
/*      */     } 
/*  909 */     return (HashMap)hashMap;
/*      */   }
/*      */   
/*      */   public String getBrowserName(String paramString1, String paramString2) {
/*  913 */     return paramString1;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDefinedSingle2Multi(int paramInt, String paramString1, String paramString2) {
/*  919 */     if (paramString1 == null) {
/*  920 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*  921 */       paramString1 = cptFieldComInfo.getFielddbtype("" + paramInt);
/*      */     } 
/*  923 */     String str1 = "";
/*  924 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, ",");
/*  925 */     Browser browser = (Browser)StaticObj.getServiceByFullname(paramString1, Browser.class);
/*  926 */     String str2 = "";
/*  927 */     String str3 = "";
/*  928 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  929 */       BrowserBean browserBean = browser.searchById(arrayList.get(b));
/*  930 */       str2 = Util.null2String(browserBean.getName());
/*  931 */       str3 = Util.null2String(browserBean.getHref());
/*  932 */       if (str1.equals("")) {
/*  933 */         str1 = str1 + "<a href=\"javascript:openFullWindowForXtable('" + str3 + "')\" >" + str2 + "</a>";
/*      */       } else {
/*  935 */         str1 = str1 + ",<a href=\"javascript:openFullWindowForXtable('" + str3 + "')\" >" + str2 + "</a>";
/*      */       } 
/*      */     } 
/*  938 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDefinedSingle2Multi_print(int paramInt, String paramString1, String paramString2) {
/*  945 */     if (paramString1 == null) {
/*  946 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*  947 */       paramString1 = cptFieldComInfo.getFielddbtype("" + paramInt);
/*      */     } 
/*  949 */     String str = "";
/*  950 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, ",");
/*  951 */     Browser browser = (Browser)StaticObj.getServiceByFullname(paramString1, Browser.class);
/*  952 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  953 */       BrowserBean browserBean = browser.searchById(arrayList.get(b));
/*  954 */       if (str.equals("")) {
/*  955 */         str = str + Util.null2String(browserBean.getName());
/*      */       } else {
/*  957 */         str = str + "," + Util.null2String(browserBean.getName());
/*      */       } 
/*      */     } 
/*  960 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserFieldvalue_new(String paramString1, String paramString2) throws Exception {
/*  971 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  972 */     if (arrayOfString != null && arrayOfString.length >= 4) {
/*  973 */       return getFieldvalue_new(null, Util.getIntValue(arrayOfString[0], 7), null, null, Util.getIntValue(arrayOfString[1], 0), Util.getIntValue(arrayOfString[2]), Util.getIntValue(arrayOfString[3]), paramString1, 0, true);
/*      */     }
/*  975 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldvalue_new(HttpSession paramHttpSession, int paramInt1, String paramString1, String paramString2, int paramInt2, int paramInt3, int paramInt4, String paramString3, int paramInt5, boolean paramBoolean) throws Exception {
/*  983 */     RecordSet recordSet = new RecordSet();
/*  984 */     String str = "";
/*      */     
/*  986 */     if (paramInt3 == 3) {
/*  987 */       ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/*  988 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/*      */       
/*  990 */       BrowserConfigComInfo browserConfigComInfo = new BrowserConfigComInfo();
/*  991 */       String str1 = Util.null2String(browserConfigComInfo.getLinkurl(paramInt4 + ""));
/*  992 */       if (paramInt4 > 0 && 
/*  993 */         "".equals(str1)) {
/*  994 */         str1 = Util.null2String(browserComInfo.getLinkurl(paramInt4 + ""));
/*      */       }
/*      */       
/*  997 */       if (paramInt4 == 2 || paramInt4 == 19 || paramInt4 == 226 || paramInt4 == 227) {
/*  998 */         str = paramString3;
/*  999 */       } else if (paramInt4 == 1 || paramInt4 == 17) {
/* 1000 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1001 */           if (paramBoolean)
/*      */           {
/* 1003 */             str = str + "<a href=\"javascript:openhrm(" + arrayList.get(b) + ")\" onclick=\"pointerXY(event);\" >" + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */           }
/*      */           else
/*      */           {
/* 1007 */             str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*      */           }
/*      */         
/*      */         } 
/* 1011 */       } else if (paramInt4 == 23 || paramInt4 == 26 || paramInt4 == 3) {
/* 1012 */         if ("0".equals(Util.null2String(paramString3))) {
/* 1013 */           return "";
/*      */         }
/* 1015 */         CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/* 1016 */         cptSettingsComInfo.setTofirstRow();
/* 1017 */         cptSettingsComInfo.next();
/* 1018 */         CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 1019 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1020 */           if (paramBoolean) {
/* 1021 */             str = str + "<a href=\"javascript:openFullWindowForXtable('" + GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/cptcard?capitalid=" + paramString3 + "')\" >" + capitalComInfo.getCapitalname(arrayList.get(b)) + "</a>";
/* 1022 */             if ("1".equals(cptSettingsComInfo.getIsopen2()))
/*      */             {
/* 1024 */               str = str + "&nbsp;<a href='javaScript:opencptcode(\"" + GCONST.getContextPath() + "/CreateCptBarCode?barType=-1&capitalid=" + arrayList.get(b) + "&date=" + (new Date()).getTime() + "\"," + cptSettingsComInfo.getWidth2() + "," + cptSettingsComInfo.getHeight2() + ")' onclick=\"pointercptXY(event)\"><img src=\"" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png\" onmouseover=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode3_wev8.png'\" onmouseout=\"this.src='" + GCONST.getContextPath() + "/cpt/img/qrcode2_wev8.png'\" width=\"16\" height=\"16\" style=\"vertical-align:middle;cursor: pointer;\"></a>";
/*      */             }
/* 1026 */             str = str + "&nbsp;";
/*      */           } else {
/*      */             
/* 1029 */             str = str + capitalComInfo.getCapitalname(arrayList.get(b)) + ",";
/*      */           }
/*      */         
/*      */         } 
/* 1033 */       } else if (paramInt4 == 161 || paramInt4 == 162) {
/* 1034 */         str = getDefinedSingle2Multi(paramInt2, null, paramString3);
/*      */       } else {
/* 1036 */         String str2 = browserComInfo.getBrowsertablename(paramInt4 + "");
/* 1037 */         String str3 = browserComInfo.getBrowsercolumname(paramInt4 + "");
/* 1038 */         String str4 = browserComInfo.getBrowserkeycolumname(paramInt4 + "");
/* 1039 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1040 */           int i = Util.getIntValue(arrayList.get(b).toString(), 0);
/* 1041 */           String str5 = "select " + str3 + " from " + str2 + " where " + str4 + "=" + i;
/* 1042 */           recordSet.executeSql(str5);
/* 1043 */           recordSet.next();
/* 1044 */           String str6 = recordSet.getString(1);
/* 1045 */           if (paramBoolean) {
/*      */             
/* 1047 */             if (paramInt4 == 258 || paramInt4 == 58 || paramInt4 == 263) {
/* 1048 */               str = str + Util.toScreen(str6, paramInt1);
/*      */             } else {
/* 1050 */               if (!str1.equals("")) {
/* 1051 */                 str = str + "<a href=\"javascript:openFullWindowForXtable('" + str1 + i + "')\">";
/*      */               }
/* 1053 */               str = str + Util.toScreen(str6, paramInt1);
/* 1054 */               if (!str1.equals("")) {
/* 1055 */                 str = str + "</a>&nbsp;";
/*      */               }
/*      */             } 
/*      */           } else {
/* 1059 */             str = str + str6 + ",";
/*      */           } 
/*      */         } 
/*      */       } 
/* 1063 */       if (str.endsWith(",")) {
/* 1064 */         str = str.substring(0, str.length() - 1);
/*      */       }
/* 1066 */     } else if (paramInt3 == 4) {
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1071 */       if ("1".equals(paramString3)) {
/* 1072 */         str = "<span style='color:red;'>" + SystemEnv.getHtmlLabelName(163, paramInt1) + "</span>";
/*      */       } else {
/* 1074 */         str = SystemEnv.getHtmlLabelName(161, paramInt1);
/*      */       } 
/* 1076 */     } else if (paramInt3 == 5) {
/*      */       
/* 1078 */       recordSet.executeSql("select fieldid,selectvalue,selectname,selectlabel from cpt_SelectItem where fieldid = " + paramInt2 + "  order by listorder,id");
/* 1079 */       while (recordSet.next()) {
/* 1080 */         int i = recordSet.getInt("fieldid");
/* 1081 */         String str1 = Util.null2String(recordSet.getString("selectvalue"));
/* 1082 */         String str2 = "";
/* 1083 */         if (i == -8 || i == -21) {
/* 1084 */           str2 = SystemEnv.getHtmlLabelName(recordSet.getInt("selectlabel"), paramInt1);
/*      */         } else {
/* 1086 */           str2 = Util.toScreen(recordSet.getString("selectname"), paramInt1);
/*      */         } 
/*      */         
/* 1089 */         if (str1.equals(paramString3)) {
/* 1090 */           str = str + str2;
/*      */         }
/*      */       } 
/* 1093 */     } else if (paramInt3 == 6) {
/* 1094 */       recordSet.executeSql("select id,docsubject,accessorycount from docdetail where id in(" + paramString3 + ") order by id asc");
/*      */       
/* 1096 */       while (recordSet.next()) {
/* 1097 */         str = str + recordSet.getString("docsubject") + ",";
/*      */       }
/* 1099 */       if (str.endsWith(",")) {
/* 1100 */         str = str.substring(0, str.length() - 1);
/*      */       }
/* 1102 */     } else if (paramInt3 == 2 && paramInt4 == 1) {
/* 1103 */       str = Util.toHtml(paramString3);
/*      */     } else {
/* 1105 */       str = paramString3;
/*      */     } 
/* 1107 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/FieldInfoManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */