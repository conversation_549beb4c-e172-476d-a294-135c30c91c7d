/*     */ package com.api.cpt.util;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.BrowserInitUtil;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SearchConditionUtil
/*     */ {
/*     */   public List<Map<String, Object>> getCondition(String paramString, User paramUser) {
/*  23 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  24 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  25 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*  26 */     hashMap1.put("title", SystemEnv.getHtmlLabelName(32905, paramUser.getLanguage()));
/*  27 */     hashMap1.put("defaultshow", Boolean.valueOf(true));
/*  28 */     hashMap1.put("items", arrayList1);
/*  29 */     arrayList.add(hashMap1);
/*     */     
/*  31 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  32 */     ArrayList arrayList2 = new ArrayList();
/*  33 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(32843, paramUser.getLanguage()));
/*  34 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*  35 */     hashMap2.put("items", arrayList2);
/*     */     
/*  37 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/*  38 */     if ("CptType".equals(paramString))
/*  39 */     { SearchConditionItem searchConditionItem = null;
/*  40 */       searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 195, "name");
/*  41 */       arrayList1.add(searchConditionItem);
/*  42 */       searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 433, "description");
/*  43 */       arrayList1.add(searchConditionItem); }
/*  44 */     else if ("CptAssortMent".equals(paramString))
/*  45 */     { SearchConditionItem searchConditionItem = null;
/*  46 */       arrayList1.add(searchConditionItem); }
/*  47 */     else { if ("CptAlertNum".equals(paramString) || "CptModify".equals(paramString) || "CptPrint".equals(paramString)) {
/*  48 */         boolean bool = (new CapitalTransMethod()).IsWareHouseOpen();
/*  49 */         String str = ",";
/*  50 */         if (!bool) {
/*  51 */           str = str + "warehouse,";
/*     */         }
/*  53 */         FieldInfoManager fieldInfoManager = new FieldInfoManager();
/*  54 */         Map map = new HashMap<>();
/*  55 */         if ("CptAlertNum".equals(paramString)) {
/*  56 */           Map map1 = fieldInfoManager.getSearchDefFieldInfo("2");
/*  57 */         } else if ("CptModify".equals(paramString)) {
/*  58 */           Map map1 = fieldInfoManager.getSearchDefFieldInfo("3");
/*  59 */         } else if ("CptPrint".equals(paramString)) {
/*  60 */           map = fieldInfoManager.getSearchDefFieldInfo("4");
/*     */         } 
/*     */         
/*  63 */         arrayList = new ArrayList<>();
/*     */         
/*  65 */         hashMap1 = new HashMap<>();
/*  66 */         ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/*  67 */         List list = (List)map.get(ConditionUtil.COMMON_CONDITION);
/*  68 */         for (Map map1 : list) {
/*  69 */           Map map2 = map1;
/*  70 */           String str1 = (String)map2.get("fieldname");
/*     */           
/*  72 */           if ("sptcount".equals(str1)) {
/*     */             
/*  74 */             ConditionType conditionType = ConditionType.SELECT;
/*  75 */             ArrayList<SearchConditionOption> arrayList7 = new ArrayList();
/*     */             
/*  77 */             SearchConditionOption searchConditionOption = new SearchConditionOption();
/*  78 */             searchConditionOption.setKey("");
/*  79 */             searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(332, Util.getIntValue(paramUser.getLanguage())));
/*  80 */             searchConditionOption.setSelected(false);
/*  81 */             arrayList7.add(searchConditionOption);
/*     */             
/*  83 */             searchConditionOption = new SearchConditionOption();
/*  84 */             searchConditionOption.setKey("1");
/*  85 */             searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(1363, paramUser.getLanguage()));
/*  86 */             searchConditionOption.setSelected(false);
/*  87 */             arrayList7.add(searchConditionOption);
/*     */             
/*  89 */             searchConditionOption = new SearchConditionOption();
/*  90 */             searchConditionOption.setKey("0");
/*  91 */             searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(125023, paramUser.getLanguage()));
/*  92 */             searchConditionOption.setSelected(false);
/*  93 */             arrayList7.add(searchConditionOption);
/*  94 */             SearchConditionItem searchConditionItem = conditionFactory.createCondition(conditionType, Util.getIntValue((String)map2.get("fieldlabel")), str1, arrayList7);
/*  95 */             arrayList3.add(searchConditionItem);
/*     */             continue;
/*     */           } 
/*  98 */           if (str.indexOf("," + str1 + ",") == -1) {
/*  99 */             arrayList3.add(ConditionUtil.getCondition(map2, paramUser));
/*     */           }
/*     */         } 
/* 102 */         hashMap1.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.COMMON_CONDITION, paramUser.getLanguage()));
/* 103 */         hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 104 */         hashMap1.put("items", arrayList3);
/* 105 */         arrayList.add(hashMap1);
/*     */         
/* 107 */         hashMap2 = new HashMap<>();
/* 108 */         ArrayList<Object> arrayList4 = new ArrayList();
/* 109 */         list = (List)map.get(ConditionUtil.MANAGER_CONDITION);
/* 110 */         for (Map map1 : list) {
/* 111 */           Map<Object, Object> map2 = map1;
/* 112 */           String str1 = (String)map2.get("fieldname");
/* 113 */           if (str.indexOf("," + str1 + ",") == -1) {
/* 114 */             arrayList4.add(ConditionUtil.getCondition(map2, paramUser));
/*     */           }
/* 116 */           if ("blongsubcompany".equalsIgnoreCase(str1)) {
/* 117 */             map2 = new HashMap<>();
/* 118 */             map2.put("fieldname", "datatype");
/* 119 */             map2.put("fieldid", "9999");
/* 120 */             map2.put("fielddbtype", "int");
/* 121 */             map2.put("fieldhtmltype", "3");
/* 122 */             map2.put("fieldtype", "179");
/* 123 */             map2.put("fieldlabel", "1509");
/* 124 */             map2.put("issystem", "1");
/* 125 */             arrayList4.add(ConditionUtil.getCondition(map2, paramUser));
/*     */           } 
/*     */         } 
/* 128 */         hashMap2.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.MANAGER_CONDITION, paramUser.getLanguage()));
/* 129 */         hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 130 */         hashMap2.put("items", arrayList4);
/* 131 */         arrayList.add(hashMap2);
/*     */         
/* 133 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 134 */         ArrayList<Object> arrayList5 = new ArrayList();
/* 135 */         list = (List)map.get(ConditionUtil.APPLY_CONDITION);
/* 136 */         for (Map map1 : list) {
/* 137 */           Map map2 = map1;
/* 138 */           String str1 = (String)map2.get("fieldname");
/* 139 */           if (str.indexOf("," + str1 + ",") == -1) {
/* 140 */             arrayList5.add(ConditionUtil.getCondition(map2, paramUser));
/*     */           }
/*     */         } 
/* 143 */         hashMap3.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.APPLY_CONDITION, paramUser.getLanguage()));
/* 144 */         hashMap3.put("defaultshow", Boolean.valueOf(true));
/* 145 */         hashMap3.put("items", arrayList5);
/* 146 */         arrayList.add(hashMap3);
/*     */         
/* 148 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 149 */         ArrayList<Object> arrayList6 = new ArrayList();
/* 150 */         list = (List)map.get(ConditionUtil.OTHER_CONDITION);
/* 151 */         for (Map map1 : list) {
/* 152 */           Map map2 = map1;
/* 153 */           String str1 = (String)map2.get("fieldname");
/* 154 */           arrayList6.add(ConditionUtil.getCondition(map2, paramUser));
/*     */         } 
/*     */         
/* 157 */         hashMap4.put("title", SystemEnv.getHtmlLabelNames(ConditionUtil.OTHER_CONDITION, paramUser.getLanguage()));
/* 158 */         hashMap4.put("defaultshow", Boolean.valueOf(true));
/* 159 */         hashMap4.put("items", arrayList6);
/* 160 */         arrayList.add(hashMap4);
/* 161 */         return (List)arrayList;
/* 162 */       }  if ("CptCapital".equals(paramString)) {
/* 163 */         SearchConditionItem searchConditionItem = null;
/* 164 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 195, "name");
/* 165 */         arrayList1.add(searchConditionItem);
/* 166 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 433, "capitalspec");
/* 167 */         arrayList1.add(searchConditionItem);
/* 168 */       } else if ("CptInventoryPlan".equals(paramString)) {
/* 169 */         SearchConditionItem searchConditionItem = null;
/*     */         
/* 171 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 714, "mark");
/* 172 */         arrayList1.add(searchConditionItem);
/*     */         
/* 174 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 195, "flowTitle");
/* 175 */         searchConditionItem.setIsQuickSearch(true);
/* 176 */         arrayList1.add(searchConditionItem);
/*     */         
/* 178 */         searchConditionItem = conditionFactory.createCondition(ConditionType.DATEPICKER, 22168, "planbegindate");
/* 179 */         arrayList1.add(searchConditionItem);
/*     */         
/* 181 */         searchConditionItem = conditionFactory.createCondition(ConditionType.DATEPICKER, 22170, "planenddate");
/* 182 */         arrayList1.add(searchConditionItem);
/*     */         
/* 184 */         ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 185 */         arrayList3.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, Util.getIntValue(paramUser.getLanguage())), false));
/* 186 */         arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(1979, paramUser.getLanguage()), false));
/* 187 */         arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(384644, paramUser.getLanguage()), false));
/* 188 */         arrayList3.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(384645, paramUser.getLanguage()), false));
/* 189 */         arrayList3.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(384647, paramUser.getLanguage()), false));
/* 190 */         arrayList3.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(384648, paramUser.getLanguage()), false));
/*     */         
/* 192 */         searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 602, "inventorystate", arrayList3);
/* 193 */         arrayList1.add(searchConditionItem);
/*     */       }
/* 195 */       else if ("PlanMain".equals(paramString)) {
/* 196 */         SearchConditionItem searchConditionItem = null;
/*     */         
/* 198 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 30671, "listname");
/* 199 */         arrayList1.add(searchConditionItem);
/*     */         
/* 201 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 1445, "zcname");
/* 202 */         arrayList1.add(searchConditionItem);
/*     */         
/* 204 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 903, "zcmark");
/* 205 */         arrayList1.add(searchConditionItem);
/*     */         
/* 207 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 1387, "location");
/* 208 */         arrayList1.add(searchConditionItem);
/*     */         
/* 210 */         BrowserBean browserBean = new BrowserBean();
/* 211 */         browserBean.setType("1");
/* 212 */         browserBean.setViewAttr(2);
/* 213 */         browserBean.setTitle(SystemEnv.getHtmlLabelName(1416, paramUser.getLanguage()));
/* 214 */         searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 1416, "countuser", browserBean);
/* 215 */         arrayList1.add(searchConditionItem);
/*     */         
/* 217 */         ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 218 */         arrayList3.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, Util.getIntValue(paramUser.getLanguage())), false));
/* 219 */         arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(1979, paramUser.getLanguage()), false));
/* 220 */         arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(384644, paramUser.getLanguage()), false));
/* 221 */         arrayList3.add(new SearchConditionOption("10", SystemEnv.getHtmlLabelName(385411, paramUser.getLanguage()), false));
/* 222 */         arrayList3.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(225, paramUser.getLanguage()), false));
/* 223 */         arrayList3.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(1398, paramUser.getLanguage()), false));
/* 224 */         arrayList3.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(1397, paramUser.getLanguage()), false));
/* 225 */         arrayList3.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(384648, paramUser.getLanguage()), false));
/*     */         
/* 227 */         searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 383310, "pdjg", arrayList3);
/* 228 */         arrayList1.add(searchConditionItem);
/*     */       }
/* 230 */       else if ("MyInventoryList".equals(paramString)) {
/* 231 */         SearchConditionItem searchConditionItem = null;
/*     */         
/* 233 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 714, "mark");
/* 234 */         arrayList1.add(searchConditionItem);
/*     */         
/* 236 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 195, "flowTitle");
/* 237 */         searchConditionItem.setIsQuickSearch(true);
/* 238 */         arrayList1.add(searchConditionItem);
/*     */ 
/*     */         
/* 241 */         ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 242 */         arrayList3.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, Util.getIntValue(paramUser.getLanguage())), false));
/* 243 */         arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(384644, paramUser.getLanguage()), false));
/* 244 */         arrayList3.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(384645, paramUser.getLanguage()), false));
/* 245 */         arrayList3.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(384646, paramUser.getLanguage()), false));
/* 246 */         arrayList3.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(384647, paramUser.getLanguage()), false));
/* 247 */         arrayList3.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(384648, paramUser.getLanguage()), false));
/*     */         
/* 249 */         searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 602, "inventorystate", arrayList3);
/* 250 */         arrayList1.add(searchConditionItem);
/*     */       }
/* 252 */       else if ("PlanlistCpt".equals(paramString)) {
/* 253 */         SearchConditionItem searchConditionItem = null;
/*     */         
/* 255 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 1445, "zcname");
/* 256 */         arrayList1.add(searchConditionItem);
/*     */         
/* 258 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 903, "zcmark");
/* 259 */         arrayList1.add(searchConditionItem);
/*     */         
/* 261 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 904, "zcggxh");
/* 262 */         arrayList1.add(searchConditionItem);
/*     */         
/* 264 */         searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 1387, "location");
/* 265 */         arrayList1.add(searchConditionItem);
/*     */         
/* 267 */         BrowserBean browserBean1 = new BrowserBean();
/* 268 */         browserBean1.setType("164");
/* 269 */         browserBean1.setViewAttr(2);
/* 270 */         browserBean1.setTitle(SystemEnv.getHtmlLabelName(831, paramUser.getLanguage()));
/* 271 */         searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 19799, "blongsubcompany", browserBean1);
/* 272 */         arrayList1.add(searchConditionItem);
/*     */         
/* 274 */         BrowserBean browserBean2 = new BrowserBean();
/* 275 */         browserBean2.setType("4");
/* 276 */         browserBean2.setViewAttr(2);
/* 277 */         browserBean2.setTitle(SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()));
/* 278 */         searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 15393, "blongdepartment", browserBean2);
/* 279 */         arrayList1.add(searchConditionItem);
/*     */         
/* 281 */         BrowserBean browserBean3 = new BrowserBean();
/* 282 */         browserBean3.setType("4");
/* 283 */         browserBean3.setViewAttr(2);
/* 284 */         browserBean3.setTitle(SystemEnv.getHtmlLabelName(21030, paramUser.getLanguage()));
/* 285 */         searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 21030, "departmentid", browserBean3);
/* 286 */         arrayList1.add(searchConditionItem);
/*     */         
/* 288 */         BrowserBean browserBean4 = new BrowserBean();
/* 289 */         browserBean4.setType("1");
/* 290 */         browserBean4.setViewAttr(2);
/* 291 */         browserBean4.setTitle(SystemEnv.getHtmlLabelName(1508, paramUser.getLanguage()));
/* 292 */         searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 1508, "resourceid", browserBean4);
/* 293 */         arrayList1.add(searchConditionItem);
/*     */         
/* 295 */         ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 296 */         arrayList3.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, Util.getIntValue(paramUser.getLanguage())), false));
/* 297 */         arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(125023, paramUser.getLanguage()), false));
/* 298 */         arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(1363, paramUser.getLanguage()), false));
/*     */         
/* 300 */         searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 707, "sptcount", arrayList3);
/* 301 */         arrayList1.add(searchConditionItem);
/*     */         
/* 303 */         searchConditionItem = conditionFactory.createCondition(ConditionType.DATEPICKER, 1415, "inventorydate");
/* 304 */         arrayList1.add(searchConditionItem);
/*     */         
/* 306 */         ArrayList<SearchConditionOption> arrayList4 = new ArrayList();
/* 307 */         arrayList4.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, Util.getIntValue(paramUser.getLanguage())), false));
/* 308 */         arrayList4.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(1979, paramUser.getLanguage()), false));
/* 309 */         arrayList4.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(384644, paramUser.getLanguage()), false));
/* 310 */         arrayList4.add(new SearchConditionOption("10", SystemEnv.getHtmlLabelName(385411, paramUser.getLanguage()), false));
/* 311 */         arrayList4.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(225, paramUser.getLanguage()), false));
/* 312 */         arrayList4.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(1398, paramUser.getLanguage()), false));
/* 313 */         arrayList4.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(1397, paramUser.getLanguage()), false));
/* 314 */         arrayList4.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(384648, paramUser.getLanguage()), false));
/*     */         
/* 316 */         searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 383310, "pdjg", arrayList4);
/* 317 */         arrayList1.add(searchConditionItem);
/*     */       }  }
/*     */ 
/*     */     
/* 321 */     if (arrayList2.size() > 0) {
/* 322 */       arrayList.add(hashMap2);
/*     */     }
/* 324 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public static SearchConditionItem createCondition(User paramUser, ConditionType paramConditionType, String paramString1, String paramString2, String paramString3, int paramInt) {
/* 329 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/* 330 */     SearchConditionItem searchConditionItem = null;
/* 331 */     searchConditionItem = conditionFactory.createCondition(paramConditionType, paramString1, paramString2);
/* 332 */     searchConditionItem.setViewAttr(paramInt);
/* 333 */     searchConditionItem.setValue(paramString3);
/* 334 */     return searchConditionItem;
/*     */   }
/*     */   
/*     */   public static SearchConditionItem createCondition(User paramUser, ConditionType paramConditionType, String paramString1, String paramString2, String paramString3) {
/* 338 */     BrowserBean browserBean = new BrowserBean(paramString3);
/* 339 */     BrowserInitUtil browserInitUtil = new BrowserInitUtil();
/* 340 */     browserInitUtil.initBrowser(browserBean, paramUser.getLanguage());
/* 341 */     return new SearchConditionItem(paramConditionType, SystemEnv.getHtmlLabelNames(paramString1, paramUser.getLanguage()), new String[] { paramString2 }, browserBean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static SearchConditionItem createCondition(User paramUser, ConditionType paramConditionType, String paramString1, String paramString2, String paramString3, int paramInt, String paramString4) {
/* 348 */     BrowserBean browserBean = new BrowserBean(paramString4);
/* 349 */     BrowserInitUtil browserInitUtil = new BrowserInitUtil();
/* 350 */     browserInitUtil.initBrowser(browserBean, paramUser.getLanguage());
/* 351 */     SearchConditionItem searchConditionItem = new SearchConditionItem(paramConditionType, SystemEnv.getHtmlLabelNames(paramString1, paramUser.getLanguage()), new String[] { paramString2 }, browserBean);
/* 352 */     searchConditionItem.setViewAttr(paramInt);
/* 353 */     searchConditionItem.setValue(paramString3);
/*     */     
/* 355 */     return searchConditionItem;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/util/SearchConditionUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */