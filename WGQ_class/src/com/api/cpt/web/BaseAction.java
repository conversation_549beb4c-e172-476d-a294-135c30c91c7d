/*    */ package com.api.cpt.web;
/*    */ import com.api.cpt.service.CptAlertNumService;
/*    */ import com.api.cpt.service.CptCapitalInterfaceService;
/*    */ import com.api.cpt.service.CptCapitalSearchService;
/*    */ import com.api.cpt.service.CptCapitalService;
/*    */ import com.api.cpt.service.CptInstockService;
/*    */ import com.api.cpt.service.CptManagerService;
/*    */ import com.api.cpt.service.CptPrintService;
/*    */ import com.api.cpt.service.CptTableEditService;
/*    */ import com.api.cpt.service.CptTypeService;
/*    */ import java.util.Enumeration;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ public class BaseAction {
/* 18 */   CptCapitalSearchService cptSearchSerivce = new CptCapitalSearchService();
/* 19 */   CptCapitalService cptCapitalService = new CptCapitalService();
/* 20 */   CptTableEditService cptTableEditService = new CptTableEditService();
/* 21 */   CptInstockService cptInstockService = new CptInstockService();
/* 22 */   CptManagerService cptManagerService = new CptManagerService();
/* 23 */   CptMendService cptMendService = new CptMendService();
/* 24 */   CptTypeService cptTypeService = new CptTypeService();
/* 25 */   CptAssortMentService cptAssortMentService = new CptAssortMentService();
/* 26 */   CptCapitalExcelToDBService cptCapitalExcelToDBService = new CptCapitalExcelToDBService();
/* 27 */   CptPrintService cptPrintService = new CptPrintService();
/* 28 */   CptSearchDefineService cptSearchDefineService = new CptSearchDefineService();
/* 29 */   CptBasicInfoService cptBasicInfoService = new CptBasicInfoService();
/* 30 */   CapitalReportService cptReportService = new CapitalReportService();
/* 31 */   CptAlertNumService cptAlertNumService = new CptAlertNumService();
/* 32 */   CptInventoryService cptInventoryService = new CptInventoryService();
/* 33 */   CptCapitalInterfaceService cptCapitalInterfaceService = new CptCapitalInterfaceService();
/*    */   
/* 35 */   BaseBean loggerBean = new BaseBean();
/*    */ 
/*    */   
/*    */   protected Map<String, Object> getRequestParams(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 40 */     Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 41 */     while (enumeration.hasMoreElements()) {
/* 42 */       String str = enumeration.nextElement();
/* 43 */       hashMap.put(str, paramHttpServletRequest.getParameter(str));
/*    */     } 
/* 45 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/cpt/web/BaseAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */