/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ProjectTypeService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  49 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  50 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  51 */     String str2 = Util.null2String(paramMap.get("fullname"));
/*  52 */     String str3 = Util.null2String(paramMap.get("description"));
/*  53 */     String str4 = " ";
/*  54 */     boolean bool = false;
/*  55 */     if (!str1.equals("") && 
/*  56 */       !bool) {
/*  57 */       bool = true;
/*  58 */       str4 = str4 + str1;
/*     */     } 
/*     */     
/*  61 */     if (!str2.equals("")) {
/*  62 */       if (!bool) {
/*  63 */         bool = true;
/*  64 */         str4 = str4 + " where fullname like '%";
/*  65 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  66 */         str4 = str4 + "%'";
/*     */       } else {
/*  68 */         str4 = str4 + " and fullname like '%";
/*  69 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  70 */         str4 = str4 + "%'";
/*     */       } 
/*     */     }
/*  73 */     if (!str3.equals("")) {
/*  74 */       if (!bool) {
/*  75 */         bool = true;
/*  76 */         str4 = str4 + " where description like '%";
/*  77 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/*  78 */         str4 = str4 + "%'";
/*     */       } else {
/*  80 */         str4 = str4 + " and description like '%";
/*  81 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/*  82 */         str4 = str4 + "%'";
/*     */       } 
/*     */     }
/*  85 */     String str5 = " dsporder ";
/*  86 */     String str6 = " id,fullname,description,wfid,dsporder";
/*  87 */     String str7 = " Prj_ProjectType ";
/*     */     
/*  89 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  90 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  91 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname")).setIsInputCol(BoolAttr.TRUE));
/*  92 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*  93 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15057, this.user.getLanguage()), "wfid", "wfid", "weaver.workflow.workflow.WorkflowComInfo.getWorkflowname"));
/*     */     
/*  95 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str4, str5, "id", arrayList);
/*     */     try {
/*  97 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*  98 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  99 */     } catch (Exception exception) {
/* 100 */       exception.printStackTrace();
/*     */     } 
/* 102 */     splitTableBean.setSqlsortway("ASC");
/* 103 */     splitTableBean.setSqlisdistinct("true");
/* 104 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 105 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 118 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 119 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 120 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 121 */     ConditionType conditionType = ConditionType.INPUT;
/* 122 */     arrayList.add(conditionFactory.createCondition(conditionType, 399, "fullname", true));
/* 123 */     arrayList.add(conditionFactory.createCondition(conditionType, 433, "description", false));
/*     */     
/* 125 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 126 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ProjectTypeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */