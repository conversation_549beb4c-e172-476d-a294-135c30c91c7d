/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import com.engine.odoc.util.OdocNumberManageUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ 
/*     */ public class OdocShowMouldBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  36 */     String str1 = " docmould.mouldtype=2 ";
/*  37 */     str1 = str1 + Util.null2String(paramMap.get("sqlwhere"));
/*  38 */     String str2 = " docmould.id,docmould.mouldname";
/*  39 */     String str3 = Util.null2String(paramMap.get("workflowid"));
/*  40 */     String str4 = Util.null2String(paramMap.get("isGeneratePrintMould"));
/*  41 */     String str5 = Util.null2String(paramMap.get("odocTypesetMould"));
/*  42 */     int i = Util.getIntValue(Util.null2String(paramMap.get("selectValue")));
/*  43 */     byte b = Util.null2String(paramMap.get("ischina")).equals("") ? 0 : 1;
/*  44 */     if ("1".equals(Util.null2String(paramMap.get("showAll")))) {
/*  45 */       b = -1;
/*     */     }
/*  47 */     String str6 = "";
/*  48 */     if (!"".equals(str3)) {
/*     */       
/*  50 */       str2 = " docmould.id,docmould.mouldname";
/*  51 */       str6 = " docmould inner join workflow_mould on docmould.id = workflow_mould.mouldid and workflowid = " + str3;
/*     */ 
/*     */       
/*  54 */       RecordSet recordSet = new RecordSet();
/*  55 */       int j = -1;
/*  56 */       recordSet.executeQuery("select showmouldfield from workflow_createdoc where workflowid = ?", new Object[] { str3 });
/*  57 */       if (recordSet.next()) {
/*  58 */         j = recordSet.getInt("showmouldfield");
/*     */       }
/*     */       
/*  61 */       if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/*  62 */         str6 = str6 + " inner join (select  -1 as selectvalue from dual ";
/*     */       } else {
/*  64 */         str6 = str6 + " inner join (select -1 as selectvalue ";
/*     */       } 
/*  66 */       WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  67 */       int k = Util.getIntValue(workflowAllComInfo.getIsBill(str3));
/*  68 */       if (j > 0) {
/*     */         
/*  70 */         String str = "";
/*     */         
/*  72 */         int m = -1;
/*  73 */         int n = -1;
/*  74 */         if (k == 1) {
/*  75 */           str = "select fieldhtmltype,type from workflow_billfield where id = ? ";
/*     */         } else {
/*  77 */           str = "select fieldhtmltype, type from workflow_formdict where id = ? ";
/*     */         } 
/*  79 */         recordSet.executeQuery(str, new Object[] { Integer.valueOf(j) });
/*  80 */         if (recordSet.next()) {
/*  81 */           m = recordSet.getInt("fieldhtmltype");
/*  82 */           n = recordSet.getInt("type");
/*     */         } 
/*     */         
/*  85 */         if (m == 5) {
/*  86 */           str6 = str6 + " union select selectvalue from workflow_selectitem where fieldid = " + j + "  and (cancel is null or cancel = '0' or cancel = '') ";
/*  87 */         } else if (m == 3 && n == 52) {
/*     */           
/*  89 */           str6 = str6 + " union select id + 100 as selectvalue from odoc_odoctype where  (iscancel is null or iscancel = '0' or iscancel = '') ";
/*  90 */         } else if (m == 3 && n == 324) {
/*  91 */           str6 = str6 + " union select -1*id-100 as selectvalue from odoc_numberManage where isopen=1 and " + OdocNumberManageUtil.getOdocWorkflowNumberHaveRightSqlWhere(Util.getIntValue(str3), "id", "in") + " ";
/*     */         } 
/*     */       } 
/*  94 */       str6 = str6 + " ) selectitem on selectitem.selectvalue = workflow_mould.selectvalue ";
/*     */     } else {
/*  96 */       str6 = " docmould ";
/*  97 */       if (b >= 0) {
/*  98 */         str1 = str1 + " and docmould.systemtype=" + b;
/*     */       }
/* 100 */       if (i < -100) {
/* 101 */         int j = -1 * i - 100;
/* 102 */         String str = OdocNumberManageUtil.getOdocNumberMouldids(j);
/* 103 */         if ("".equals(str)) {
/* 104 */           str1 = str1 + " and 1!=1";
/*     */         } else {
/* 106 */           str1 = str1 + " and docmould.id in(" + str + ")";
/*     */         } 
/*     */       } 
/*     */     } 
/* 110 */     if (!"".equals(str4)) {
/* 111 */       str6 = " odocprintmould docmould ";
/* 112 */       str1 = " docmould.mouldtype=2 ";
/*     */     } 
/* 114 */     if ("true".equals(str5)) {
/* 115 */       str6 = " odocTypesetMould docmould ";
/* 116 */       str1 = " 1=1 ";
/*     */     } 
/* 118 */     String str7 = Util.null2String(paramMap.get("mouldname"));
/* 119 */     if (!"".equals(str7) && str7 != null)
/*     */     {
/* 121 */       str1 = str1 + " and docmould.mouldname like '%" + str7 + "%'";
/*     */     }
/* 123 */     boolean bool = (new ManageDetachComInfo()).isUseDocManageDetach();
/* 124 */     if (bool) {
/* 125 */       int j = Util.getIntValue(Util.null2String(paramMap.get("subCompanyId")));
/* 126 */       String str = getDetachableSqlWhere(j);
/* 127 */       str1 = str1 + " AND ( " + str + " ) ";
/*     */     } 
/*     */     
/* 130 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 131 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 132 */     arrayList.add(new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "mouldname", "mouldname", 1));
/*     */     
/* 134 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str6, str1, "id", "id", arrayList);
/* 135 */     if (!str3.trim().isEmpty()) {
/* 136 */       splitTableBean.setSqlisdistinct("true");
/*     */     }
/* 138 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 144 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/* 145 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 146 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/* 147 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/* 150 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 18151, "mouldname", true);
/* 151 */     arrayList1.add(searchConditionItem);
/*     */     
/* 153 */     arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(383122, Util.getIntValue(this.user.getLanguage())), true, arrayList1));
/* 154 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 155 */     return weaResultMsg.getResultMapAll();
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 160 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 161 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 162 */     String str2 = Util.null2String(paramMap.get("isGeneratePrintMould"));
/* 163 */     String str3 = Util.null2String(paramMap.get("odocTypesetMould"));
/* 164 */     if ("".equals(str1)) return (Map)hashMap; 
/* 165 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 166 */     RecordSet recordSet = new RecordSet();
/* 167 */     boolean bool = (new ManageDetachComInfo()).isUseDocManageDetach();
/* 168 */     String str4 = " 1 = 1 ";
/* 169 */     if (bool) {
/* 170 */       int i = Util.getIntValue(Util.null2String(paramMap.get("subCompanyId")));
/* 171 */       str4 = getDetachableSqlWhere(i);
/*     */     } 
/* 173 */     if (!"".equals(str2)) {
/* 174 */       recordSet.executeQuery("select * from odocprintmould where id in (" + str1 + ") AND (" + str4 + ")", new Object[0]);
/* 175 */     } else if ("true".equals(str3)) {
/* 176 */       recordSet.executeQuery("select * from odocTypesetMould where id in (" + str1 + ")", new Object[0]);
/*     */     } else {
/* 178 */       recordSet.executeQuery("select * from docmould where id in (" + str1 + ") AND (" + str4 + ")", new Object[0]);
/*     */     } 
/* 180 */     while (recordSet.next()) {
/* 181 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 182 */       hashMap1.put("id", recordSet.getString("id"));
/* 183 */       hashMap1.put("mouldname", recordSet.getString("mouldname"));
/* 184 */       arrayList.add(hashMap1);
/*     */     } 
/* 186 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 187 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 188 */     arrayList1.add(new ListHeadBean("mouldname", "", 1, BoolAttr.TRUE));
/*     */     
/* 190 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 191 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 192 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 193 */     return (Map)hashMap;
/*     */   }
/* 195 */   private BaseBean logger = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getDetachableSqlWhere(int paramInt) {
/* 204 */     String str = " 1 = 1 ";
/* 205 */     if (paramInt > 0) {
/* 206 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 207 */       int i = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "WorkflowManage:All", paramInt);
/* 208 */       if (i > -1) {
/* 209 */         str = str + " and subCompanyId = " + paramInt + " ";
/*     */       } else {
/* 211 */         str = str + " and 1 = 2 ";
/*     */       } 
/*     */     } else {
/*     */       try {
/* 215 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 216 */         String str1 = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", -1);
/* 217 */         if (!str1.equals("")) {
/* 218 */           str = str + " and (" + Util.getSubINClause(str1, "subCompanyId", "in") + ") ";
/*     */         } else {
/* 220 */           str = str + " and 1 = 2 ";
/*     */         } 
/* 222 */       } catch (Exception exception) {
/* 223 */         this.logger.writeLog(exception);
/* 224 */         str = " 1 != 1 ";
/*     */       } 
/*     */     } 
/* 227 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OdocShowMouldBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */