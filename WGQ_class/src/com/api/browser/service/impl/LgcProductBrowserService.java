/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LgcProductBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"assetname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"assortmentid\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"assetunitid\"                    }                ],                \"key\": \"col1_row2\"            }            {                \"configs\": [                    {                        \"key\": \"salesprice\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"currencyid\"                    }                ],                \"key\": \"col1_row3\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  79 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  81 */     String str1 = Util.fromScreen(Util.null2String(paramMap.get("assetname")), this.user.getLanguage());
/*  82 */     String str2 = Util.fromScreen(Util.null2String(paramMap.get("assortmentid")), this.user.getLanguage());
/*  83 */     if (str2.equals("0")) {
/*  84 */       str2 = "";
/*     */     }
/*     */     
/*  87 */     String str3 = "";
/*  88 */     if (!str1.equals(""))
/*  89 */       if (str3.equals("")) { str3 = str3 + " where t2.assetname like '%" + str1 + "%'"; }
/*  90 */       else { str3 = str3 + " and t2.assetname like '%" + str1 + "%'"; }
/*     */        
/*  92 */     if (!str2.equals(""))
/*  93 */       if (str3.equals("")) { str3 = str3 + " where t1.assortmentstr like '%|" + str2 + "|%'"; }
/*  94 */       else { str3 = str3 + " and t1.assortmentstr like '%|" + str2 + "|%'"; }
/*     */        
/*  96 */     if (str3.equals("")) {
/*  97 */       str3 = str3 + " where t1.id != 0 ";
/*     */     }
/*  99 */     str3 = str3 + " and t1.id=t2.assetid ";
/* 100 */     String str4 = "t2.assetid,t2.assetname,t1.assetunitid,t2.currencyid,t2.salesprice,t1.assortmentid,t1.assortmentstr";
/* 101 */     String str5 = "LgcAsset t1,LgcAssetCountry t2";
/* 102 */     String str6 = "t1.assortmentstr,t2.assetname";
/*     */     
/* 104 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 105 */     arrayList.add((new SplitTableColBean("true", "assetid")).setIsPrimarykey(BoolAttr.TRUE));
/* 106 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15129, this.user.getLanguage()), "assetname", "assetname")).setIsInputCol(BoolAttr.TRUE));
/* 107 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(705, this.user.getLanguage()), "assetunitid", "assetunitid", "weaver.lgc.maintenance.AssetUnitComInfo.getAssetUnitname"));
/* 108 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(649, this.user.getLanguage()), "currencyid", "currencyid", "weaver.fna.maintenance.CurrencyComInfo.getCurrencyname"));
/* 109 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(726, this.user.getLanguage()), "salesprice", "salesprice", "weaver.general.Util.getPointValue"));
/* 110 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "assortmentid", "assortmentid", "weaver.lgc.maintenance.LgcAssortmentComInfo.getAssortmentFullName"));
/*     */     
/* 112 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "t2.assetid", arrayList);
/*     */     
/*     */     try {
/* 115 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 116 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"assetname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"assortmentid\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"assetunitid\"                    }                ],                \"key\": \"col1_row2\"            }            {                \"configs\": [                    {                        \"key\": \"salesprice\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"currencyid\"                    }                ],                \"key\": \"col1_row3\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 117 */     } catch (Exception exception) {
/* 118 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 121 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 122 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 127 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 128 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 129 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 130 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 63, "assortmentid", "13"));
/* 131 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "assetname", true));
/* 132 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 133 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 137 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 138 */     BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 139 */     String str = browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse);
/* 140 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str);
/*     */     
/* 142 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/LgcProductBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */