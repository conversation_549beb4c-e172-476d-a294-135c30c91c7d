/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.engine.SAPIntegration.biz.regService.RegServiceDataBiz;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SAPServiceListService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     RegServiceDataBiz regServiceDataBiz = new RegServiceDataBiz();
/* 31 */     String str1 = Util.getIntValue(Util.null2String(paramMap.get("serviceId")), 0) + "";
/* 32 */     String str2 = Util.null2String(paramMap.get("searchName"));
/* 33 */     String str3 = Util.getIntValue(Util.null2String((String)paramMap.get("id")), 0) + "";
/* 34 */     hashMap.put("datas", regServiceDataBiz.getRegServiceTree(str1, str2, str3).get("servTree"));
/* 35 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 36 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 48 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 50 */       ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 51 */       ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 52 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30672, "regname"));
/* 53 */       hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 54 */     } catch (Exception exception) {
/* 55 */       exception.printStackTrace();
/*    */     } 
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 67 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 68 */     String str = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 69 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 70 */     RecordSet recordSet = new RecordSet();
/* 71 */     recordSet.executeQuery("select id,regname from sap_service where regname like '%" + str + "%'", new Object[0]);
/* 72 */     while (recordSet.next()) {
/* 73 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 74 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 75 */       hashMap1.put("name", Util.null2String(recordSet.getString("regname")));
/* 76 */       arrayList.add(hashMap1);
/*    */     } 
/* 78 */     hashMap.put("datas", arrayList);
/* 79 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SAPServiceListService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */