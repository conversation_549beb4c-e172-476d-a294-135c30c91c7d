/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.engine.doc.cmd.log.SanYuanUtil;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AdminAccount2BrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   private boolean canviewLogSanYuan = false;
/*     */   private boolean canviewSJLogBySanYuan = true;
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  37 */     String str1 = Util.null2String(paramMap.get("lastname"));
/*  38 */     String str2 = Util.null2String(paramMap.get("description"));
/*     */     
/*  40 */     String str3 = Util.null2String(paramMap.get("fromStr"));
/*     */     
/*  42 */     SanYuanUtil sanYuanUtil = new SanYuanUtil();
/*  43 */     this.canviewLogSanYuan = sanYuanUtil.canviewLogSanYuan(this.user);
/*  44 */     this.canviewSJLogBySanYuan = sanYuanUtil.canviewSJLogBySanYuan();
/*  45 */     boolean bool = sanYuanUtil.isAdmin(this.user);
/*     */     
/*  47 */     String str4 = "";
/*  48 */     if (!str1.equals("")) {
/*  49 */       str4 = str4 + " and lastname like '%";
/*  50 */       str4 = str4 + Util.fromScreen2(str1, 7);
/*  51 */       str4 = str4 + "%'";
/*     */     } 
/*  53 */     if (!str2.equals("")) {
/*  54 */       str4 = str4 + " and description like '%";
/*  55 */       str4 = str4 + Util.fromScreen2(str2, 7);
/*  56 */       str4 = str4 + "%'";
/*     */     } 
/*  58 */     if (str3.equalsIgnoreCase("roleMembers") || !HrmSanyuanAdminBiz.getSanyuanAble()) {
/*  59 */       str4 = str4 + " and (sanyuanType is null or sanyuanType not in (1,2,3)) ";
/*     */     }
/*  61 */     if (this.user.getUID() != 1) {
/*  62 */       if (bool && this.canviewLogSanYuan) {
/*  63 */         str4 = str4 + " and ( 1=2 ";
/*     */         
/*  65 */         if (this.canviewSJLogBySanYuan) {
/*  66 */           str4 = str4 + " or sanyuanType=3 ";
/*     */         }
/*     */       } else {
/*  69 */         str4 = str4 + " and (id=" + this.user.getUID() + " or creator=" + this.user.getUID();
/*     */       } 
/*  71 */       str4 = str4 + " ) ";
/*     */     } 
/*  73 */     if (!(new ManageDetachComInfo()).getDetachable().equals("1"))
/*     */     {
/*     */       
/*  76 */       str4 = str4 + " and id = 1 ";
/*     */     }
/*     */     
/*  79 */     str4 = SqlUtils.replaceFirstAnd(str4);
/*     */     
/*  81 */     String str5 = " id ,lastname,description ";
/*  82 */     String str6 = " HrmResourceManager ";
/*     */     
/*  84 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  85 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  86 */     arrayList.add((new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "lastname", "lastname", 1)).setIsInputCol(BoolAttr.TRUE));
/*  87 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*     */     
/*  89 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", arrayList);
/*  90 */     splitTableBean.setSqlsortway("ASC");
/*  91 */     splitTableBean.setSqlisdistinct("true");
/*  92 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  93 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  98 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  99 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 100 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 101 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 102 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "lastname", true));
/* 103 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 104 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 111 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 112 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 113 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 114 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 115 */     RecordSet recordSet = new RecordSet();
/* 116 */     String str2 = "select  id ,lastname,description from HrmResourceManager where id in (" + str1 + ")";
/* 117 */     recordSet.executeQuery(str2, new Object[0]);
/* 118 */     while (recordSet.next()) {
/* 119 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 120 */       hashMap1.put("id", recordSet.getString("id"));
/* 121 */       hashMap1.put("lastname", Util.null2String(recordSet.getString("lastname")));
/* 122 */       hashMap1.put("description", Util.null2String(recordSet.getString("description")));
/* 123 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 126 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 127 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 128 */     arrayList1.add(new ListHeadBean("lastname", "", 1, BoolAttr.TRUE));
/* 129 */     arrayList1.add(new ListHeadBean("description", "", 1, BoolAttr.TRUE));
/*     */     
/* 131 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 132 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 133 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AdminAccount2BrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */