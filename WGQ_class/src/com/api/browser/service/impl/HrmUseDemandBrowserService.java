/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class HrmUseDemandBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 24 */     String str1 = Util.null2String(paramMap.get("jobtitleName"));
/*    */     
/* 26 */     String str2 = " a.id,j.jobtitlename as name,a.demandnum ";
/* 27 */     String str3 = " from HrmUseDemand a left join HrmJobTitles j on a.demandjobtitle = j.id ";
/* 28 */     String str4 = " where 1=1 ";
/*    */     
/* 30 */     if (!str1.equals("")) {
/* 31 */       str4 = str4 + " and j.jobtitlename like '%" + str1 + "%'";
/*    */     }
/*    */     
/* 34 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 35 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 36 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(20379, this.user.getLanguage()), "name", null)).setIsInputCol(BoolAttr.TRUE));
/* 37 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(10000221, Util.getIntValue(this.user.getLanguage())), "demandnum", null));
/*    */     
/* 39 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, Util.toHtmlForSplitPage(str4), "id", "id", arrayList);
/* 40 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 41 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 45 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 46 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 47 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 48 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 20379, "jobtitleName", true));
/* 49 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 50 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmUseDemandBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */