/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MessageTypeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     String str1 = Util.null2String(paramMap.get("labelname"));
/* 32 */     String str2 = Util.null2String(paramMap.get("groupname"));
/*    */     
/* 34 */     String str3 = "type.id,type.typecode,type.sourcename as typename,grouplabel.labelname as groupname";
/* 35 */     String str4 = "ECOLOGY_MESSAGE_TYPE type LEFT JOIN ECOLOGY_MESSAGE_GROUP gp ON type.ecology_message_groupid=gp.id,HtmlLabelInfo grouplabel";
/* 36 */     String str5 = " grouplabel.indexid=gp.groupname and grouplabel.languageid=" + this.user.getLanguage();
/* 37 */     if (StringUtils.isNotBlank(str1)) {
/* 38 */       str5 = str5 + " and type.sourcename like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%'";
/*    */     }
/* 40 */     if (StringUtils.isNotBlank(str2)) {
/* 41 */       str5 = str5 + " and grouplabel.labelname like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%'";
/*    */     }
/* 43 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 44 */     arrayList.add(new SplitTableColBean("hide", "id"));
/* 45 */     arrayList.add(new SplitTableColBean("hide", "typecode"));
/* 46 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(508384, this.user.getLanguage()), "typename", "typename", "weaver.general.Util.formatMultiLang", String.valueOf(this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE));
/* 47 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()), "groupname", "groupname"));
/*    */     
/* 49 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str5, "type.id", "typecode", "asc", arrayList);
/* 50 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 51 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 56 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 57 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 58 */     RecordSet recordSet = new RecordSet();
/* 59 */     String str2 = "select type.id,type.typecode,type.sourcename as typename from ECOLOGY_MESSAGE_TYPE as type where type.sourcename like ? ";
/* 60 */     recordSet.executeQuery(str2, new Object[] { "%" + str1 + "%" });
/* 61 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 62 */     while (recordSet.next()) {
/* 63 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 64 */       hashMap1.put("id", recordSet.getString("typecode"));
/* 65 */       hashMap1.put("name", Util.formatMultiLang(recordSet.getString("typename"), String.valueOf(this.user.getLanguage())));
/* 66 */       arrayList.add(hashMap1);
/*    */     } 
/* 68 */     hashMap.put("datas", arrayList);
/* 69 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 77 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 78 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 79 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 508384, "labelname", true));
/* 80 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 33522, "groupname"));
/*    */     
/* 82 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 83 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MessageTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */