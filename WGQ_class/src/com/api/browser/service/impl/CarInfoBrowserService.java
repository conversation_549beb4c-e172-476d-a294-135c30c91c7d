/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.api.car.util.CarSetInfo;
/*     */ import com.api.car.util.CarUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarInfoBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  38 */     CarSetInfo carSetInfo = new CarSetInfo();
/*  39 */     String str1 = " where (deposit<>'1' or deposit is null) ";
/*     */     
/*  41 */     if (carSetInfo.getIsShare() != 1) {
/*  42 */       RecordSet recordSet = new RecordSet();
/*  43 */       recordSet.executeSql("select carsdetachable from SystemSet");
/*  44 */       int i = 0;
/*  45 */       if (recordSet.next()) {
/*  46 */         i = recordSet.getInt(1);
/*     */       }
/*  48 */       if (i == 1 && 
/*  49 */         this.user.getUID() != 1) {
/*  50 */         str1 = str1 + CarUtil.getDetachSql(this.user);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  55 */     String str2 = Util.null2String(paramMap.get("carNo"));
/*  56 */     String str3 = Util.null2String(paramMap.get("carType"));
/*  57 */     String str4 = Util.null2String(paramMap.get("factoryNo"));
/*  58 */     String str5 = Util.null2String(paramMap.get("startdate"));
/*  59 */     String str6 = Util.null2String(paramMap.get("enddate"));
/*     */     
/*  61 */     if (!str2.equals("")) {
/*  62 */       str1 = str1 + " and carNo like '%" + str2 + "%'";
/*     */     }
/*  64 */     if (!str3.equals("")) {
/*  65 */       str1 = str1 + " and carType=" + str3 + "";
/*     */     }
/*  67 */     if (!str4.equals("")) {
/*  68 */       str1 = str1 + " and factoryNo like '%" + str4 + "%'";
/*     */     }
/*  70 */     if (!str5.equals("")) {
/*  71 */       str1 = str1 + " and buyDate >= '" + str5 + "'";
/*     */     }
/*  73 */     if (!str6.equals("")) {
/*  74 */       str1 = str1 + " and buyDate <= '" + str6 + "'";
/*     */     }
/*     */     
/*  77 */     str1 = SqlUtils.replaceFirstAnd(str1);
/*  78 */     str1 = str1 + CarUtil.getCarShareSql(this.user);
/*  79 */     String str7 = " showorder asc,id desc ";
/*  80 */     String str8 = " id,factoryno,carno,cartype,driver,buydate";
/*  81 */     String str9 = " CarInfo a ";
/*     */     
/*  83 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  84 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  85 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(20319, this.user.getLanguage()), "carno", "carno")).setIsInputCol(BoolAttr.TRUE));
/*  86 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(20318, this.user.getLanguage()), "factoryno", "factoryno"));
/*  87 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(22256, this.user.getLanguage()), "cartype", "cartype", "weaver.car.CarTypeComInfo.getCarTypename"));
/*  88 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17649, this.user.getLanguage()), "driver", "driver", "weaver.hrm.resource.ResourceComInfo.getResourcename"));
/*  89 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(16914, this.user.getLanguage()), "buydate", "buydate"));
/*     */     
/*  91 */     SplitTableBean splitTableBean = new SplitTableBean(str8, str9, str1, str7, "id", arrayList);
/*  92 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  93 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 105 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 106 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 107 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 108 */     ConditionType conditionType = ConditionType.INPUT;
/* 109 */     arrayList.add(conditionFactory.createCondition(conditionType, 20319, "carNo", true));
/* 110 */     arrayList.add(conditionFactory.createCondition(conditionType, 20318, "factoryNo", false));
/*     */     
/* 112 */     conditionType = ConditionType.SELECT;
/* 113 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     recordSet.executeProc("CarType_Select", "");
/* 116 */     while (recordSet.next()) {
/* 117 */       int i = recordSet.getInt("id");
/* 118 */       String str = Util.toScreen(recordSet.getString("name"), this.user.getLanguage());
/* 119 */       SearchConditionOption searchConditionOption = new SearchConditionOption();
/* 120 */       searchConditionOption.setKey(i + "");
/* 121 */       searchConditionOption.setShowname(str);
/* 122 */       searchConditionOption.setSelected(false);
/* 123 */       arrayList1.add(searchConditionOption);
/*     */     } 
/* 125 */     arrayList.add(conditionFactory.createCondition(conditionType, 22256, "carType", arrayList1));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 133 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CarInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */