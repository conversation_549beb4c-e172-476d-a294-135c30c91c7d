/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.integration.util.PageUidFactory;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OfsInfoBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  38 */     String str1 = Util.null2String(paramMap.get("syscode"));
/*  39 */     String str2 = Util.null2String(paramMap.get("sysnames"));
/*     */     
/*  41 */     String str3 = " where cancel = 0 ";
/*  42 */     if (!"".equals(str1)) {
/*  43 */       str3 = str3 + " and syscode like '%" + str1 + "%'";
/*     */     }
/*  45 */     if (!"".equals(str2)) {
/*  46 */       str3 = str3 + " and (sysshortname like '%" + str2 + "%' or sysfullname like '%" + str2 + "%')";
/*     */     }
/*  48 */     String str4 = " * ";
/*  49 */     String str5 = "10";
/*  50 */     String str6 = "Ofs_sysinfo";
/*  51 */     String str7 = " Ofs_sysinfo ";
/*     */ 
/*     */     
/*  54 */     String str8 = PageUidFactory.getPageUid("intergration_browser_ofsInfo");
/*  55 */     String str9 = str8;
/*  56 */     String str10 = PageIdConst.getPageSize(str9, this.user.getUID());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  67 */     String str11 = "<table instanceid=\"ofs_workflowTable\" tabletype=\"checkbox\" pagesize=\"" + str5 + "\" > <checkboxpopedom    popedompara=\"column:workflowid\" showmethod=\"weaver.general.SplitPageTransmethod.getCheckBox\" /> <sql backfields=\"" + str4 + "\" sqlform=\"" + str7 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str3) + "\"  sqlorderby=\"sysid\"  sqlprimarykey=\"sysid\" sqlsortway=\"desc\" sqlisdistinct=\"true\" />       <head>           <col hide=\"true\" width=\"0%\" text=\"\"  column=\"sysid\" orderkey=\"sysid\" isPrimarykey='true' />           <col hide=\"false\" width=\"35%\" text=\"" + SystemEnv.getHtmlLabelName(84, this.user.getLanguage()) + "\"  column=\"syscode\" orderkey=\"syscode\"  />           <col hide=\"false\" width=\"35%\" text=\"" + SystemEnv.getHtmlLabelName(399, this.user.getLanguage()) + "\"  column=\"sysshortname\" orderkey=\"sysshortname\" isInputCol='true' />           <col hide=\"false\" width=\"30%\" text=\"" + SystemEnv.getHtmlLabelName(15767, this.user.getLanguage()) + "\"  column=\"sysfullname\" orderkey=\"sysfullname\" />       </head></table>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  72 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  73 */     String str12 = str8 + "_" + Util.getEncrypt(Util.getRandom());
/*  74 */     Util_TableMap.setVal(str12, str11);
/*  75 */     hashMap2.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  76 */     hashMap2.put(BrowserConstant.BROWSER_RESULT_DATA, str12);
/*     */     
/*  78 */     hashMap1.putAll(hashMap2);
/*     */     
/*  80 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  85 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  86 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  87 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  88 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  89 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 84, "syscode", true));
/*  90 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "sysnames", false));
/*     */     
/*  92 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  99 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 100 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, null);
/* 101 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 102 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 103 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OfsInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */