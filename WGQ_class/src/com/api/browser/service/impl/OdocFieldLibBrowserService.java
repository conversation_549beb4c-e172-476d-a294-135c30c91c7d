/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.general.browserData.BrowserManager;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class OdocFieldLibBrowserService
/*     */   extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  33 */     if (!HrmUserVarify.checkUserRight("OdocApplication", this.user)) {
/*  34 */       hashMap.put("sessionkey_state", "noright");
/*  35 */       hashMap.put("api_status", Boolean.valueOf(false));
/*  36 */       return (Map)hashMap;
/*     */     } 
/*  38 */     String str1 = Util.null2String(paramMap.get("libFieldName"));
/*  39 */     String str2 = Util.null2String(paramMap.get("dealType"));
/*  40 */     byte b = -1;
/*     */     
/*  42 */     String str3 = " and 1=1 ";
/*  43 */     if (!str1.equals("")) {
/*  44 */       str3 = str3 + " and libfieldname like '%";
/*  45 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/*  46 */       str3 = str3 + "%'";
/*     */     } 
/*  48 */     if (!"".equals(str2)) {
/*     */ 
/*     */       
/*  51 */       String[] arrayOfString = str2.split(",");
/*  52 */       if (arrayOfString.length > 1 || (str2.length() == 1 && "10".equals(arrayOfString[0]))) {
/*     */         
/*  54 */         b = 0;
/*     */       } else {
/*     */         
/*  57 */         int i = Util.getIntValue(arrayOfString[0]);
/*  58 */         if (i == 11) {
/*     */           
/*  60 */           b = 1;
/*  61 */         } else if (i == 15) {
/*     */           
/*  63 */           b = 3;
/*  64 */         } else if (i >= 12 && i <= 14) {
/*     */           
/*  66 */           b = 2;
/*     */         } else {
/*  68 */           b = 0;
/*     */         } 
/*     */       } 
/*  71 */       str3 = str3 + " and fieldType = " + b;
/*     */     } 
/*     */     
/*  74 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*  75 */     String str4 = " libid,libid id,libfieldname ";
/*  76 */     String str5 = " odoc_listfieldlib";
/*  77 */     String str6 = " libid ";
/*  78 */     writeLog("===OdocFieldLibBrowserService====select " + str4 + " from " + str5 + " " + str3);
/*     */     
/*  80 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  81 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "libid", "libid")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/*  82 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(514699, this.user.getLanguage()), "libfieldname", "libfieldname")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  83 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "id", "id", "com.engine.odoc.util.OdocCustomListUtil.getCustomListTypeName", this.user.getLanguage() + "", 1));
/*     */ 
/*     */     
/*  86 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "libid", arrayList);
/*  87 */     splitTableBean.setSqlsortway("DESC");
/*     */     
/*  89 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/*  91 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/*  92 */     } catch (Exception exception) {
/*  93 */       exception.printStackTrace();
/*     */     } 
/*  95 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  96 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 101 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 102 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 103 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 104 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "libFieldName", true));
/* 105 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 106 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 111 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 112 */     BrowserManager browserManager = new BrowserManager();
/* 113 */     browserManager.setOrderKey("libid");
/* 114 */     browserManager.setType("odocFieldLib");
/* 115 */     String str1 = " odoc_listfieldlib ";
/* 116 */     String str2 = " libid > 0 ";
/* 117 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, browserManager.getResult(paramHttpServletRequest, "libid,libfieldname ", str1, str2, 30));
/* 118 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 125 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 126 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.libFieldName");
/* 127 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OdocFieldLibBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */