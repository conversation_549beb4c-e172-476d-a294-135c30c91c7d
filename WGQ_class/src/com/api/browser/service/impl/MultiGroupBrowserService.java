/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MultiGroupBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  37 */     boolean bool = HrmUserVarify.checkUserRight("CustomGroup:Edit", this.user);
/*  38 */     String str1 = Util.null2String(paramMap.get("groupname"));
/*  39 */     String str2 = Util.null2String(paramMap.get("type"));
/*     */     
/*  41 */     String str3 = Util.null2String(paramMap.get("sqlwhere"));
/*  42 */     String str4 = Util.null2String(paramMap.get("excludeId"));
/*  43 */     if (str3.length() == 0) str3 = str3 + " where 1=1";
/*     */     
/*  45 */     if (bool) {
/*     */       
/*  47 */       str3 = str3 + " and (type=1 or (type=0 and owner = " + this.user.getUID() + "))";
/*     */     } else {
/*     */       
/*  50 */       str2 = "0";
/*  51 */       str3 = str3 + " and owner = " + this.user.getUID();
/*     */     } 
/*     */     
/*  54 */     str3 = str3 + " and (canceled is null or canceled<>1) ";
/*     */     
/*  56 */     if (!str1.equals("")) {
/*  57 */       str3 = str3 + " and name like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */     }
/*     */     
/*  60 */     if (str2.length() > 0) {
/*  61 */       str3 = str3 + " and type = " + str2;
/*     */     }
/*     */     
/*  64 */     if (str4.length() > 0) {
/*  65 */       str3 = str3 + " and id not in (" + str4 + ")";
/*     */     }
/*  67 */     String str5 = SystemEnv.getHtmlLabelName(17619, this.user.getLanguage());
/*  68 */     String str6 = SystemEnv.getHtmlLabelName(17618, this.user.getLanguage());
/*  69 */     String str7 = "id,name, type, (CASE WHEN type = 1 THEN '" + str5 + "' ELSE '" + str6 + "' END) as typename,sn ";
/*  70 */     String str8 = "HrmGroup";
/*     */     
/*  72 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  73 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  74 */     arrayList.add((new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/*  75 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "typename", "typename"));
/*     */     
/*  77 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str8, str3, "type,sn,id", "id", arrayList);
/*  78 */     splitTableBean.setSqlsortway("ASC");
/*  79 */     splitTableBean.setSqlisdistinct("true");
/*  80 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  86 */     boolean bool = HrmUserVarify.checkUserRight("CustomGroup:Edit", this.user);
/*  87 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  88 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  89 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  90 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "groupname", true));
/*     */     
/*  92 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  93 */     if (bool) {
/*  94 */       arrayList1.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/*     */     }
/*  96 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(17618, this.user.getLanguage()), !bool));
/*  97 */     if (bool) {
/*  98 */       arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(17619, this.user.getLanguage())));
/*     */     }
/* 100 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 63, "type", arrayList1));
/* 101 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 102 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 107 */     String str1 = SystemEnv.getHtmlLabelName(17619, this.user.getLanguage());
/* 108 */     String str2 = SystemEnv.getHtmlLabelName(17618, this.user.getLanguage());
/* 109 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 110 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 111 */     String str3 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 112 */     if (this.user == null || "".equals(str3)) return (Map)hashMap; 
/* 113 */     RecordSet recordSet = new RecordSet();
/* 114 */     String str4 = "select id,name,type from HrmGroup where id in (" + str3 + ")";
/* 115 */     recordSet.executeSql(str4);
/* 116 */     while (recordSet.next()) {
/* 117 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 118 */       hashMap1.put("id", recordSet.getString("id"));
/* 119 */       hashMap1.put("name", Util.null2String(recordSet.getString("name")));
/* 120 */       hashMap1.put("type", Util.null2String(recordSet.getString("type")).equals("1") ? str1 : str2);
/* 121 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 124 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 125 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 126 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/* 127 */     arrayList1.add(new ListHeadBean("type", "", 1, BoolAttr.TRUE));
/*     */     
/* 129 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 130 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 131 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 132 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MultiGroupBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */