/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DocKindBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 34 */     String str1 = Util.null2String(paramMap.get("name"));
/* 35 */     String str2 = Util.null2String(paramMap.get("desc"));
/* 36 */     String str3 = "  ";
/* 37 */     if (!str1.equals("")) {
/* 38 */       str3 = str3 + " and name like '%";
/* 39 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 40 */       str3 = str3 + "%'";
/*    */     } 
/* 42 */     if (!str2.equals("")) {
/* 43 */       str3 = str3 + " and desc_n like '%";
/* 44 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 45 */       str3 = str3 + "%'";
/*    */     } 
/* 47 */     str3 = SqlUtils.replaceFirstAnd(str3);
/* 48 */     String str4 = " id,name,desc_n ";
/* 49 */     String str5 = "DocSendDocKind";
/* 50 */     String str6 = " showOrder ";
/*    */     
/* 52 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 53 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 54 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 55 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "desc_n", "desc_n"));
/*    */     
/* 57 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/* 58 */     splitTableBean.setSqlsortway("ASC");
/* 59 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 60 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 66 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 67 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 68 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "name", true));
/* 69 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "desc"));
/* 70 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 71 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DocKindBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */