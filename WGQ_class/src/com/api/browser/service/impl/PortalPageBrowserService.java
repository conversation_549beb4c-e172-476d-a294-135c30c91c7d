/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PortalPageBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 27 */     String str1 = Util.null2String(paramMap.get("pagetype"));
/* 28 */     String str2 = Util.null2String(paramMap.get("infoname"));
/*    */     
/* 30 */     String str3 = " id,infoname,infodesc ";
/* 31 */     String str4 = " hpinfo ";
/* 32 */     String str5 = " where (infoname is not null and infoname!='') ";
/* 33 */     if (!"".equals(str1)) {
/* 34 */       if ("loginpage".equals(str1)) {
/* 35 */         str5 = str5 + " and subcompanyid=-1 ";
/* 36 */       } else if ("mainpage".equals(str1)) {
/* 37 */         str5 = str5 + " and subcompanyid!= -1 ";
/*    */       } 
/*    */     }
/* 40 */     if (!"".equals(str2)) {
/* 41 */       str5 = str5 + " and infoname like '%" + str2 + "%' ";
/*    */     }
/* 43 */     String str6 = null;
/*    */     
/* 45 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 46 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 47 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(22009, this.user.getLanguage()), "infoname", "infoname")).setIsInputCol(BoolAttr.TRUE));
/* 48 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "infodesc", "infodesc"));
/*    */     
/* 50 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str5, str6, "id", "asc", arrayList);
/* 51 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 52 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 59 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 60 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 61 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 22009, "infoname", true));
/*    */     
/* 63 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 64 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/PortalPageBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */