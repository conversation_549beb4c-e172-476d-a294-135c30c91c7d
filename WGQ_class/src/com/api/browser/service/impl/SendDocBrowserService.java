/*      */ package com.api.browser.service.impl;
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.browser.bean.ListHeadBean;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SplitTableBean;
/*      */ import com.api.browser.bean.SplitTableColBean;
/*      */ import com.api.browser.biz.Dao_Hrm4Ec;
/*      */ import com.api.browser.biz.Dao_Hrm4EcFactory;
/*      */ import com.api.browser.biz.TreeNode;
/*      */ import com.api.browser.util.BelongAttr;
/*      */ import com.api.browser.util.BoolAttr;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.BrowserDataType;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.api.browser.util.MobileJsonConfigUtil;
/*      */ import com.api.browser.util.MobileViewTypeAttr;
/*      */ import com.api.odoc.bean.BrowserTreeNode;
/*      */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*      */ import com.engine.common.service.impl.WorkflowCommonServiceImpl;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.concurrent.CopyOnWriteArrayList;
/*      */ import java.util.stream.Collectors;
/*      */ import org.apache.commons.lang3.ArrayUtils;
/*      */ import org.apache.commons.lang3.StringUtils;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.appdetach.AppDetachComInfo;
/*      */ import weaver.hrm.company.CompanyComInfo;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*      */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*      */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*      */ import weaver.hrm.resource.MutilResourceBrowser;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ public class SendDocBrowserService extends BrowserService {
/*   48 */   private String canUseuntis = "";
/*   49 */   private List<Integer> canUseuntiList = new ArrayList<>();
/*   50 */   private String IS_SINGLE_SELECT = "1";
/*      */   private Map<String, Object> orParams;
/*      */   
/*      */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*   54 */     this.orParams = paramMap;
/*   55 */     String str = Util.null2String(paramMap.get("cmd"));
/*   56 */     this.canUseuntis = getCanUseUntis(paramMap, this.user, "a");
/*   57 */     Map<String, Object> map = null;
/*   58 */     if ("List".equals(str)) {
/*   59 */       map = getList(paramMap);
/*   60 */     } else if ("Organization".equals(str)) {
/*   61 */       map = getOrganizations(paramMap);
/*   62 */     } else if ("PublicGroup".equals(str)) {
/*   63 */       map = getPrivateGroup(paramMap, Boolean.valueOf(true));
/*   64 */     } else if ("PrivateGroup".equals(str)) {
/*   65 */       map = getPrivateGroup(paramMap, Boolean.valueOf(false));
/*   66 */     } else if ("ExternalUnit".equals(str)) {
/*   67 */       map = getExternalUnit(paramMap);
/*      */     } else {
/*   69 */       paramMap.put("isSingle", "1");
/*   70 */       map = getOrganizations(paramMap);
/*      */     } 
/*   72 */     return map;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*   79 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*   80 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*   81 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*   82 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 17728, "receiveunitname", true));
/*   83 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subcompanyid", "164"));
/*   84 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*   85 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*   90 */     this.canUseuntis = getCanUseUntis(paramMap, this.user, "a");
/*   91 */     String str1 = Util.null2String(paramMap.get("CheckStrictly"));
/*   92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*   93 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*   94 */     String[] arrayOfString = this.canUseuntis.split(",");
/*   95 */     String str2 = Util.null2String(paramMap.get("types"));
/*   96 */     if (!"".equals(str2) && str2.indexOf("undefined|") < 0) {
/*   97 */       String[] arrayOfString1 = str2.split(",");
/*   98 */       for (String str : arrayOfString1)
/*      */       {
/*  100 */         String[] arrayOfString2 = str.split("\\|");
/*  101 */         if (arrayOfString2.length == 2)
/*      */         {
/*  103 */           RecordSet recordSet = new RecordSet();
/*      */           
/*  105 */           if ("dept".equals(arrayOfString2[0])) {
/*  106 */             if (!StringUtils.isNotBlank(this.canUseuntis) || ArrayUtils.contains((Object[])arrayOfString, arrayOfString2[1])) {
/*      */ 
/*      */               
/*  109 */               recordSet.executeQuery("select d1.receiveunitname,d2.subcompanyname  from docreceiveunit d1  left join hrmsubcompany d2 on d1.subcompanyid=d2.id  where d1.id=? order by d1.showorder asc", new Object[] { arrayOfString2[1] });
/*  110 */               recordSet.next();
/*  111 */               String str3 = recordSet.getString("subcompanyname");
/*  112 */               String str4 = recordSet.getString("receiveunitname");
/*  113 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  114 */               hashMap1.put("id", arrayOfString2[1]);
/*  115 */               hashMap1.put("receiveunitname", str4);
/*  116 */               hashMap1.put("subcompanyid", str3);
/*  117 */               arrayList.add(hashMap1);
/*  118 */               if ("false".equals(str1) && 
/*  119 */                 hasChild_doc(arrayOfString2[0], arrayOfString2[1]))
/*  120 */                 getUnits(arrayOfString2[0], arrayOfString2[1], arrayList); 
/*      */             } 
/*  122 */           } else if ("subgroup".equals(arrayOfString2[0]) || "subunit".equals(arrayOfString2[0])) {
/*  123 */             String str3 = "";
/*  124 */             if ("subunit".equals(arrayOfString2[0])) {
/*  125 */               str3 = "select id from docreceiveunit where id=? and subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0))  order by showorder,id asc";
/*      */             }
/*  127 */             else if (StringUtils.isBlank(this.canUseuntis)) {
/*  128 */               str3 = "select o.memberid from ODocGroupMembers o,docreceiveunit d1 where d1.id = o.memberId and d1.subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0))  and o.groupid=? order by d1.showorder asc";
/*      */             } else {
/*  130 */               str3 = "select o.memberid from ODocGroupMembers o,docreceiveunit d1 where d1.id = o.memberId and d1.subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0))  and (" + Util.getSubINClause(this.canUseuntis, "memberid", "IN") + ") and  groupid=? order by d1.showorder asc";
/*      */             } 
/*      */ 
/*      */ 
/*      */             
/*  135 */             recordSet.executeQuery(str3, new Object[] { arrayOfString2[1] });
/*  136 */             Map map = null;
/*  137 */             while (recordSet.next()) {
/*  138 */               String str4 = recordSet.getString(1);
/*  139 */               map = getUnit(str4);
/*  140 */               if (map != null) {
/*  141 */                 arrayList.add(map);
/*      */               }
/*      */             } 
/*      */           } else {
/*  145 */             String str3 = arrayOfString2[1].replace("comp_", "");
/*  146 */             if (!"0".equals(str3))
/*      */             {
/*  148 */               String str4 = "";
/*  149 */               if ("false".equals(str1)) {
/*  150 */                 if (StringUtils.isBlank(this.canUseuntis)) {
/*  151 */                   str4 = "select id from docreceiveunit  where  subcompanyid=?";
/*      */                 } else {
/*  153 */                   str4 = "select id from docreceiveunit  where  (" + Util.getSubINClause(this.canUseuntis, "id", "IN") + ") and subcompanyid=?";
/*      */                 }
/*      */               
/*      */               }
/*  157 */               else if (StringUtils.isBlank(this.canUseuntis)) {
/*  158 */                 str4 = "select id from docreceiveunit  where superiorunitid=0  and subcompanyid=?";
/*      */               } else {
/*  160 */                 str4 = "select id from docreceiveunit  where superiorunitid=0 and  (" + Util.getSubINClause(this.canUseuntis, "id", "IN") + ") and subcompanyid=?";
/*      */               } 
/*      */ 
/*      */               
/*  164 */               str4 = str4 + " and (companytype=0 or ischange=1)";
/*  165 */               str4 = str4 + " and subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0))  order by showorder,id asc";
/*  166 */               recordSet.executeQuery(str4, new Object[] { str3 });
/*  167 */               Map map = null;
/*  168 */               while (recordSet.next()) {
/*  169 */                 map = getUnit(recordSet.getString("id"));
/*  170 */                 if (map != null) {
/*  171 */                   arrayList.add(map);
/*      */                 }
/*      */               }
/*      */             
/*      */             }
/*      */           
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       }
/*      */     
/*      */     } else {
/*      */       
/*  185 */       String str3 = Util.null2String(paramMap.get("selectids"));
/*      */ 
/*      */       
/*  188 */       StringBuilder stringBuilder = new StringBuilder();
/*      */       
/*  190 */       String[] arrayOfString1 = this.canUseuntis.split(",");
/*  191 */       for (String str : str3.split(",")) {
/*  192 */         if (arrayOfString1.length > 0 && ArrayUtils.contains((Object[])arrayOfString1, str)) {
/*  193 */           if (stringBuilder.length() > 0) {
/*  194 */             stringBuilder.append("," + str);
/*      */           } else {
/*  196 */             stringBuilder.append(str);
/*      */           } 
/*      */         }
/*      */       } 
/*  200 */       writeLog("=====selectids:" + str3 + "========selectids2:" + str3.toString() + "=======canUseuntis:" + this.canUseuntis);
/*  201 */       str3 = "".equals(stringBuilder.toString().trim()) ? str3 : stringBuilder.toString();
/*      */       
/*  203 */       RecordSet recordSet = new RecordSet();
/*  204 */       String str4 = "select d1.id,d1.receiveunitname,d2.subcompanyname  from docreceiveunit d1  left join hrmsubcompany d2 on d1.subcompanyid=d2.id  where d1.id in (" + str3 + ")";
/*  205 */       str4 = str4 + " order by d1.showorder asc";
/*  206 */       recordSet.executeQuery(str4, new Object[0]);
/*  207 */       String str5 = "";
/*  208 */       String str6 = "";
/*  209 */       while (recordSet.next()) {
/*  210 */         str6 = recordSet.getString("subcompanyname");
/*  211 */         str5 = recordSet.getString("receiveunitname");
/*  212 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  213 */         hashMap1.put("id", recordSet.getString("id"));
/*  214 */         hashMap1.put("receiveunitname", str5);
/*  215 */         hashMap1.put("subcompanyid", str6);
/*  216 */         arrayList.add(hashMap1);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  222 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*      */     
/*  224 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  225 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  226 */     arrayList1.add(new ListHeadBean("receiveunitname", "", 1, BoolAttr.TRUE));
/*  227 */     arrayList1.add(new ListHeadBean("subcompanyid", ""));
/*  228 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  229 */     hashMap.put("datas", removal(arrayList));
/*  230 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<SplitMobileDataBean> getJonsConfig() {
/*  239 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/*  240 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.receiveunitname");
/*  241 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.subcompanyid");
/*  242 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getid(String paramString) {
/*  248 */     String[] arrayOfString = paramString.split("\\_");
/*  249 */     String str = "";
/*  250 */     if (arrayOfString.length == 2) {
/*  251 */       str = arrayOfString[1];
/*  252 */     } else if (arrayOfString.length == 1) {
/*  253 */       return paramString;
/*      */     } 
/*  255 */     return str;
/*      */   }
/*      */   public Map<String, Object> getList(Map<String, Object> paramMap) throws Exception {
/*  258 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  259 */     String str1 = Util.null2String(paramMap.get("subcompanyid"));
/*  260 */     String str2 = Util.null2String(paramMap.get("companyType"));
/*  261 */     String str3 = Util.null2String(paramMap.get("receiveunitname"));
/*      */     
/*  263 */     String str4 = "where 1=1 ";
/*  264 */     if (!str1.equals("")) {
/*  265 */       str4 = str4 + " and subcompanyid =" + str1 + " ";
/*      */     }
/*      */     
/*  268 */     str4 = str4 + " and (canceled is null or canceled<>'1') and (subcompanyid in (select id from hrmsubcompany where (canceled is null or canceled<>'1') ) or subcompanyid=-999)";
/*      */     
/*  270 */     if (!"".equals(str3)) {
/*  271 */       str4 = str4 + " and  receiveUnitName like '%" + str3 + "%'";
/*      */     }
/*  273 */     if (!"".equals(str2)) {
/*  274 */       str4 = str4 + " and  companyType = " + str2;
/*      */     }
/*  276 */     if (!StringUtils.isBlank(this.canUseuntis)) {
/*  277 */       str4 = str4 + " and (" + Util.getSubINClause(this.canUseuntis, "id", "IN") + ")";
/*      */     }
/*      */ 
/*      */     
/*  281 */     String str5 = "id,receiveUnitName,subcompanyid,showOrder";
/*  282 */     String str6 = "DocReceiveUnit";
/*  283 */     String str7 = "showOrder,id";
/*      */     
/*  285 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  286 */     arrayList.add((new SplitTableColBean("true", "id")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/*  287 */     arrayList.add((new SplitTableColBean("receiveunitname", null, null, 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  288 */     arrayList.add((new SplitTableColBean("subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname", null, 0)).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  289 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, str7, "id", arrayList);
/*  290 */     splitTableBean.setSqlisdistinct("true");
/*  291 */     splitTableBean.setSqlsortway("ASC");
/*  292 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*      */     try {
/*  294 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/*  295 */     } catch (Exception exception) {
/*  296 */       exception.printStackTrace();
/*      */     } 
/*  298 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  299 */     return (Map)hashMap;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getOrganizations(Map<String, Object> paramMap) {
/*      */     Map<String, Object> map;
/*  304 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  305 */     hashMap.put("status", Boolean.valueOf(false));
/*      */     
/*      */     try {
/*  308 */       CompanyComInfo companyComInfo = new CompanyComInfo();
/*  309 */       SubCompanyVirtualComInfo subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/*      */       
/*  311 */       if (null == this.user) {
/*  312 */         map = getWrongCode(402, (Map)hashMap);
/*  313 */         return map;
/*      */       } 
/*      */       
/*  316 */       String str1 = Util.null2String(paramMap.get("id"));
/*  317 */       String str2 = Util.null2String(paramMap.get("isNoAccount"));
/*  318 */       String str3 = Util.null2String(paramMap.get("sqlwhere"));
/*  319 */       String str4 = Util.null2String(paramMap.get("selectedids"));
/*  320 */       String str5 = Util.null2String(paramMap.get("virtualtype"));
/*  321 */       String str6 = Util.null2String(paramMap.get("type"));
/*  322 */       String str7 = Util.null2String(paramMap.get("alllevel"));
/*  323 */       String str8 = Util.null2String(paramMap.get("cmd"));
/*      */       
/*  325 */       String str9 = Util.null2String(paramMap.get("isSingle"));
/*  326 */       String str10 = Util.null2String(paramMap.get("isFromSuperiorUnit"));
/*      */ 
/*      */       
/*  329 */       String str11 = Util.null2String(paramMap.get("receiveunitname"));
/*  330 */       String str12 = Util.null2String(paramMap.get("subcompanyid"));
/*      */       
/*  332 */       str3 = str3 + " and subcompanyid in (select id from hrmsubcompany) ";
/*      */       
/*  334 */       str3 = str3 + getUnitsDataDefinition(paramMap, this.user) + " ";
/*      */       
/*  336 */       if (StringUtil.isNotNull(str11)) {
/*  337 */         str11 = str11.trim();
/*  338 */         str3 = str3 + " and receiveunitname like '%" + str11 + "%' ";
/*      */       } 
/*      */       
/*  341 */       if (StringUtil.isNotNull(str12)) {
/*  342 */         str12 = str12.trim();
/*  343 */         str3 = str3 + " and subcompanyid ='" + str12 + "'";
/*      */       } else {
/*  345 */         str12 = "0";
/*      */       } 
/*  347 */       ArrayList<String> arrayList = new ArrayList();
/*  348 */       if (str4.length() > 0) {
/*  349 */         String[] arrayOfString = str4.split(",");
/*  350 */         for (String str : arrayOfString) {
/*  351 */           arrayList.add(str);
/*      */         }
/*      */       } 
/*  354 */       String str13 = "";
/*  355 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  356 */       if (str8.equals("getNum")) {
/*  357 */         String str = Util.null2String(paramMap.get("nodeids"));
/*  358 */         str13 = getResourceNumJson(str, arrayList, str3);
/*  359 */         map.put("status", Boolean.valueOf(true));
/*  360 */         map.put("datas", str13);
/*      */       }
/*  362 */       else if (str1.equals("")) {
/*      */         
/*  364 */         BrowserTreeNode browserTreeNode1 = new BrowserTreeNode();
/*  365 */         String str = companyComInfo.getCompanyname("1");
/*  366 */         browserTreeNode1.setName(str);
/*  367 */         if (!"".equals(str5) && !"1".equals(str5)) {
/*  368 */           browserTreeNode1.setId(str5);
/*      */         } else {
/*      */           
/*  371 */           browserTreeNode1.setId("comp_0");
/*      */         } 
/*  373 */         browserTreeNode1.setIcon("icon-coms-Department-number");
/*  374 */         browserTreeNode1.setType("com");
/*  375 */         browserTreeNode1.setIsImgIcon(false);
/*  376 */         browserTreeNode1.setSelected(false);
/*  377 */         browserTreeNode1.setCanClick(false);
/*  378 */         browserTreeNode1.setCheckStrictly(false);
/*  379 */         browserTreeNode1.setIsParent(true);
/*  380 */         if (!str10.equals("1")) {
/*  381 */           getV3SubCompanyTreeList(browserTreeNode1, str12, str5, arrayList, str2, this.user, str3, str9);
/*      */         } else {
/*  383 */           getV3SubCompanyTreeList(browserTreeNode1, "0", str5, arrayList, str2, this.user, str3, str9, str10);
/*      */         } 
/*      */         
/*  386 */         ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  387 */         arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  388 */         arrayList1.add(new ListHeadBean("receiveunitname", "", 1, BoolAttr.TRUE));
/*  389 */         arrayList1.add(new ListHeadBean("subcompanyid", ""));
/*  390 */         map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  391 */         map.put(BrowserConstant.BROWSER_RESULT_DATA, browserTreeNode1);
/*  392 */         map.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  393 */         map.put("status", Boolean.valueOf(true));
/*  394 */       } else if (str6.equals("subcom")) {
/*      */         
/*  396 */         if (!"".equals(str5) && !"1".equals(str5)) {
/*  397 */           str5 = subCompanyVirtualComInfo.getCompanyid(str1);
/*      */         }
/*      */         
/*  400 */         getV3SubCompanyTreeList(browserTreeNode, str1, str5, arrayList, str2, this.user, str3, str9);
/*      */         
/*  402 */         map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  403 */         map.put(BrowserConstant.BROWSER_RESULT_DATA, browserTreeNode.getSubs());
/*  404 */         map.put("status", Boolean.valueOf(true));
/*  405 */       } else if (str6.equals("dept")) {
/*      */         
/*  407 */         String str = "";
/*  408 */         map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  409 */         map.put(BrowserConstant.BROWSER_RESULT_DATA, getDocDepartment(str, str1, arrayList, str2, this.user, str3, str5));
/*  410 */         map.put("status", Boolean.valueOf(true));
/*      */       }
/*      */       else {
/*      */         
/*  414 */         map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  415 */         map.put(BrowserConstant.BROWSER_RESULT_DATA, "");
/*  416 */         map.put("status", Boolean.valueOf(true));
/*      */       }
/*      */     
/*      */     }
/*  420 */     catch (Exception exception) {
/*  421 */       writeLog(exception);
/*      */     } 
/*  423 */     return map;
/*      */   }
/*      */ 
/*      */   
/*      */   private Map<String, Object> getPrivateGroup(Map<String, Object> paramMap, Boolean paramBoolean) {
/*  428 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  429 */     String str1 = Util.null2String(paramMap.get("id"));
/*  430 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*  431 */     Integer integer = Integer.valueOf(-1);
/*  432 */     String str2 = "";
/*      */     
/*  434 */     if ("".equals(str1)) {
/*  435 */       String str3 = "";
/*  436 */       if (paramBoolean.booleanValue()) {
/*  437 */         str3 = SystemEnv.getHtmlLabelName(17619, this.user.getLanguage());
/*  438 */         str2 = "icon-coms-HumanResources";
/*      */       } else {
/*  440 */         integer = Integer.valueOf(this.user.getUID());
/*  441 */         str3 = SystemEnv.getHtmlLabelName(17618, this.user.getLanguage());
/*  442 */         str2 = "icon-coms-locking";
/*      */       } 
/*  444 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  445 */       browserTreeNode.setId("0");
/*  446 */       browserTreeNode.setName(str3);
/*  447 */       browserTreeNode.setIcon(str2);
/*  448 */       browserTreeNode.setType("group");
/*  449 */       browserTreeNode.setIsParent(true);
/*  450 */       browserTreeNode.setIsImgIcon(false);
/*  451 */       browserTreeNode.setSelected(false);
/*  452 */       browserTreeNode.setCanClick(false);
/*  453 */       browserTreeNode.setCheckStrictly(false);
/*      */       
/*  455 */       RecordSet recordSet = new RecordSet();
/*  456 */       String str4 = "";
/*  457 */       if ("mysql".equals(recordSet.getDBType())) {
/*      */         
/*  459 */         str4 = " order by cast(sorting as signed) asc ";
/*  460 */       } else if ("sqlserver".equals(recordSet.getDBType())) {
/*      */         
/*  462 */         str4 = " order by cast(sorting as int) asc ";
/*      */       } else {
/*      */         
/*  465 */         str4 = " order by to_number(sorting) asc ";
/*      */       } 
/*  467 */       if (paramBoolean.booleanValue()) {
/*  468 */         recordSet.executeQuery("select * from receiveunit_group where receiveunittype=0" + str4, new Object[0]);
/*      */       } else {
/*  470 */         recordSet.executeQuery("select * from receiveunit_group where receiveunittype=1 and operationid=?" + str4, new Object[] { integer });
/*      */       } 
/*  472 */       while (recordSet.next()) {
/*  473 */         if (paramBoolean.booleanValue()) {
/*  474 */           Boolean bool = checkSharePermission(this.user, recordSet.getString("id"));
/*  475 */           if (!bool.booleanValue())
/*      */             continue; 
/*      */         } 
/*  478 */         BrowserTreeNode browserTreeNode1 = new BrowserTreeNode();
/*  479 */         browserTreeNode1.setName(recordSet.getString("RECEIVEUNITNAME"));
/*  480 */         browserTreeNode1.setId(recordSet.getString("id"));
/*  481 */         browserTreeNode1.setType("subgroup");
/*  482 */         browserTreeNode1.setIcon("icon-coms-Branch");
/*  483 */         browserTreeNode1.setIsParent(false);
/*  484 */         browserTreeNode1.setCanClick(true);
/*  485 */         browserTreeNode1.setCheckStrictly(false);
/*  486 */         browserTreeNode1.setSelected(false);
/*  487 */         browserTreeNode1.setIsImgIcon(false);
/*  488 */         if (hasChild_doc("subgroup", recordSet.getString("id"))) {
/*  489 */           browserTreeNode1.setIsParent(true);
/*  490 */           browserTreeNode1.setContainSub(true);
/*      */         } 
/*  492 */         arrayList.add(browserTreeNode1);
/*      */       } 
/*  494 */       browserTreeNode.setSubs(arrayList);
/*  495 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  496 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  497 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  498 */       arrayList1.add(new ListHeadBean("receiveunitname", "", 1, BoolAttr.TRUE));
/*  499 */       arrayList1.add(new ListHeadBean("subcompanyid", ""));
/*  500 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  501 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, browserTreeNode);
/*      */     }
/*      */     else {
/*      */       
/*  505 */       RecordSet recordSet = new RecordSet();
/*  506 */       String str = "";
/*  507 */       str = str + " and d1.subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) ";
/*  508 */       if (StringUtils.isBlank(this.canUseuntis)) {
/*  509 */         recordSet.executeQuery("select gm.memberid from ODocGroupMembers gm,docreceiveunit d1 where gm.memberid=d1.id and (d1.canceled is null or d1.canceled<>'1') and  groupid=? " + str + " order by d1.showorder asc,d1.id asc", new Object[] { str1 });
/*      */       } else {
/*  511 */         recordSet.executeQuery("select gm.memberid from ODocGroupMembers gm,docreceiveunit d1 where gm.memberid=d1.id and (d1.canceled is null or d1.canceled<>'1') and   (" + Util.getSubINClause(this.canUseuntis, "gm.memberid", "IN") + ")  and groupid=? " + str + " order by d1.showorder asc,d1.id asc", new Object[] { str1 });
/*      */       } 
/*      */       
/*  514 */       ArrayList<BrowserTreeNode> arrayList1 = new ArrayList();
/*      */       
/*  516 */       while (recordSet.next()) {
/*  517 */         String str3 = recordSet.getString("memberid");
/*      */         
/*  519 */         if (!"".equals(str3)) {
/*  520 */           BrowserTreeNode browserTreeNode = getUnitNode(str3);
/*  521 */           if (browserTreeNode != null) {
/*  522 */             String str4 = browserTreeNode.getId();
/*  523 */             browserTreeNode.setId(str4);
/*  524 */             arrayList1.add(browserTreeNode);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  529 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  530 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList1);
/*      */     } 
/*      */ 
/*      */     
/*  534 */     return (Map)hashMap;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getExternalUnit(Map<String, Object> paramMap) throws Exception {
/*  538 */     return getList(paramMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BrowserTreeNode getUnitNode(String paramString) {
/*  546 */     RecordSet recordSet = new RecordSet();
/*  547 */     recordSet.executeQuery("select id,receiveunitname,canceled from docreceiveunit where id=? and subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) ", new Object[] { paramString });
/*  548 */     BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*      */     
/*  550 */     if (recordSet.next()) {
/*  551 */       String str = recordSet.getString("id");
/*  552 */       browserTreeNode.setId(str);
/*  553 */       browserTreeNode.setName(recordSet.getString("receiveunitname"));
/*  554 */       browserTreeNode.setType("subunit");
/*  555 */       browserTreeNode.setIcon("icon-coms-Branch");
/*  556 */       browserTreeNode.setIsParent(false);
/*  557 */       browserTreeNode.setCanClick(true);
/*  558 */       browserTreeNode.setCheckStrictly(false);
/*  559 */       browserTreeNode.setSelected(false);
/*  560 */       browserTreeNode.setIsImgIcon(false);
/*  561 */       if ("1".equals(recordSet.getString("canceled"))) {
/*  562 */         return null;
/*      */       }
/*      */     } 
/*  565 */     return browserTreeNode;
/*      */   }
/*      */   
/*      */   private Map<String, Object> getWrongCode(int paramInt, Map<String, Object> paramMap) {
/*  569 */     paramMap.put("error", Integer.valueOf(paramInt));
/*  570 */     paramMap.put("status", "false");
/*  571 */     switch (paramInt)
/*      */     { case 402:
/*  573 */         paramMap.put("msg", SystemEnv.getHtmlLabelName(2011, this.user.getLanguage()));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  582 */         return paramMap;case 403: paramMap.put("msg", SystemEnv.getHtmlLabelName(501597, this.user.getLanguage())); return paramMap; }  paramMap.put("msg", SystemEnv.getHtmlLabelName(129710, this.user.getLanguage())); return paramMap;
/*      */   }
/*      */   
/*      */   private String getResourceNumJson(String paramString1, List<String> paramList, String paramString2) throws Exception {
/*  586 */     JSONArray jSONArray = new JSONArray();
/*      */     try {
/*  588 */       RecordSet recordSet = new RecordSet();
/*  589 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  590 */       MutilResourceBrowser mutilResourceBrowser = new MutilResourceBrowser();
/*  591 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*      */       
/*  593 */       Dao_Hrm4Ec dao_Hrm4Ec = null;
/*  594 */       if (!"oracle".equals(recordSet.getDBType())) {
/*  595 */         dao_Hrm4Ec = Dao_Hrm4EcFactory.getInstance().getDao("Dao_Hrm4EcSqlServer");
/*      */       } else {
/*  597 */         dao_Hrm4Ec = Dao_Hrm4EcFactory.getInstance().getDao("Dao_Hrm4EcOracle");
/*      */       } 
/*      */       
/*  600 */       String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/*  601 */       for (String str1 : arrayOfString) {
/*      */         
/*  603 */         String str2 = str1.split("_")[0];
/*  604 */         String str3 = str1.split("_")[1];
/*  605 */         String str4 = "select count(*) from HrmResourceVirtualView hr where hr.status in (0,1,2,3) and 1=1 ";
/*  606 */         JSONObject jSONObject = dao_Hrm4Ec.getResourceNumJson(str2, str3, str4, paramString2, paramList);
/*      */         
/*  608 */         if (null != jSONObject) {
/*  609 */           jSONObject.put("nodeid", str1);
/*  610 */           jSONArray.add(jSONObject);
/*      */         }
/*      */       
/*      */       } 
/*  614 */     } catch (Exception exception) {
/*  615 */       writeLog(exception);
/*      */     } 
/*      */     
/*  618 */     return jSONArray.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BrowserTreeNode getV3SubCompanyTreeList(BrowserTreeNode paramBrowserTreeNode, String paramString1, String paramString2, List paramList, String paramString3, User paramUser, String paramString4, String paramString5) throws Exception {
/*  636 */     return getV3SubCompanyTreeList(paramBrowserTreeNode, paramString1, paramString2, paramList, paramString3, paramUser, paramString4, paramString5, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BrowserTreeNode getV3SubCompanyTreeList(BrowserTreeNode paramBrowserTreeNode, String paramString1, String paramString2, List paramList, String paramString3, User paramUser, String paramString4, String paramString5, String paramString6) throws Exception {
/*  652 */     List<BrowserTreeNode> list = new ArrayList();
/*  653 */     if ("0".equals(paramString1))
/*  654 */     { if (!paramString6.equals("1")) {
/*  655 */         List<BrowserTreeNode> list1 = getSubCompanyTreeList(paramString5, paramString6, paramString4);
/*      */       } else {
/*  657 */         List<BrowserTreeNode> list1 = getSubCompanyTreeList(paramString5, paramString6);
/*      */       }  }
/*  659 */     else { list = getDocDepartment(paramString1, "0", paramList, paramString3, paramUser, paramString4, paramString2); }
/*      */     
/*  661 */     paramBrowserTreeNode.setSubs(list);
/*  662 */     return paramBrowserTreeNode;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getSubCompanyTreeList(String paramString) {
/*  670 */     return getSubCompanyTreeList(paramString, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getSubCompanyTreeList(String paramString1, String paramString2) {
/*  678 */     return getSubCompanyTreeList(paramString1, paramString2, (String)null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getSubCompanyTreeList(String paramString1, String paramString2, String paramString3) {
/*  686 */     ArrayList<BrowserTreeNode> arrayList1 = new ArrayList();
/*  687 */     ArrayList<BrowserTreeNode> arrayList2 = new ArrayList();
/*      */ 
/*      */ 
/*      */     
/*  691 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  692 */     String str1 = "";
/*      */     try {
/*  694 */       str1 = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "OdoSpecification:Edit", -1);
/*  695 */     } catch (Exception exception) {
/*  696 */       exception.printStackTrace();
/*      */     } 
/*  698 */     RecordSet recordSet1 = new RecordSet();
/*  699 */     RecordSet recordSet2 = new RecordSet();
/*  700 */     String str2 = "";
/*      */     
/*  702 */     if (!str1.equals("") && paramString2.equals("1")) {
/*  703 */       str2 = "select id as id,receiveunitname as name,-1 as companyid,subcompanyid as subcompanyid,superiorunitid as ruid,'receiveunit' as type,showorder from DOCRECEIVEUNIT where (canceled is null or canceled<>'1')  and (companytype=0 or ischange=1) and  subcompanyid in (" + str1 + ") ";
/*      */     } else {
/*  705 */       str2 = "select id as id,receiveunitname as name,-1 as companyid,subcompanyid as subcompanyid,superiorunitid as ruid,'receiveunit' as type,showorder from DOCRECEIVEUNIT where (canceled is null or canceled<>'1')  and (companytype=0 or ischange=1) ";
/*      */     } 
/*      */     
/*  708 */     if (StringUtil.isNotNull(paramString3)) {
/*  709 */       str2 = str2 + paramString3;
/*      */     }
/*  711 */     str2 = str2 + " order by showorder asc,id asc ";
/*      */     
/*  713 */     HashSet<String> hashSet = new HashSet();
/*      */     
/*  715 */     recordSet2.executeQuery(str2, new Object[0]);
/*  716 */     while (recordSet2.next()) {
/*  717 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  718 */       browserTreeNode.setId(Util.null2String(recordSet2.getString("id")));
/*  719 */       browserTreeNode.setName(Util.null2String(recordSet2.getString("name")));
/*  720 */       browserTreeNode.setPid(Util.null2String(recordSet2.getString("ruid")));
/*  721 */       browserTreeNode.setCheckStrictly(false);
/*  722 */       browserTreeNode.setSelected(false);
/*  723 */       browserTreeNode.setIsImgIcon(false);
/*  724 */       if (this.canUseuntiList.size() == 0 || this.canUseuntiList.indexOf(Integer.valueOf(recordSet2.getInt("id"))) >= 0) {
/*  725 */         browserTreeNode.setCanClick(true);
/*      */       } else {
/*  727 */         browserTreeNode.setCanClick(false);
/*      */       } 
/*  729 */       browserTreeNode.setType("dept");
/*  730 */       browserTreeNode.setIcon("icon-coms-Branch");
/*  731 */       browserTreeNode.setSubs(new ArrayList());
/*  732 */       arrayList2.add(browserTreeNode);
/*  733 */       hashSet.add(recordSet2.getString("subcompanyid"));
/*      */     } 
/*      */     
/*  736 */     DocReceiveUnitComInfo docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/*  737 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  738 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  739 */     for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/*  740 */       String str4 = ((BrowserTreeNode)arrayList2.get(b1)).getId();
/*  741 */       String str5 = Util.getIntValue(docReceiveUnitComInfo.getSubcompanyid(str4), 0) + "";
/*  742 */       if (!hashMap2.containsKey(str5 + "")) {
/*  743 */         hashMap2.put(str5, new ArrayList());
/*      */       }
/*  745 */       ((List<String>)hashMap2.get(str5)).add(str4);
/*  746 */       hashMap1.put(str4, arrayList2.get(b1));
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  752 */     List<BrowserTreeNode> list1 = makeTree(arrayList2, "0", true);
/*      */ 
/*      */ 
/*      */     
/*  756 */     RecordSet recordSet3 = new RecordSet();
/*  757 */     String str3 = "";
/*  758 */     if (!str1.equals("") && paramString2.equals("1")) {
/*  759 */       str3 = "select id,subcompanyname,supsubcomid from HrmSubCompany where (canceled is null or canceled<>1)  and id in (" + str1 + ") order by supsubcomid asc, showorder asc,subcompanyname asc";
/*      */     } else {
/*  761 */       str3 = "select id,subcompanyname,supsubcomid from HrmSubCompany where (canceled is null or canceled<>1)   order by supsubcomid asc, showorder asc,subcompanyname asc";
/*      */     } 
/*  763 */     recordSet1.executeQuery(str3, new Object[0]);
/*  764 */     boolean bool = getUnitsDataDefinition(this.orParams, this.user).isEmpty();
/*  765 */     while (recordSet1.next()) {
/*  766 */       String str = recordSet1.getString("id");
/*      */       
/*  768 */       if (!bool && !hashSet.contains(str)) {
/*      */         continue;
/*      */       }
/*  771 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  772 */       browserTreeNode.setId(Util.null2String("comp_" + str));
/*  773 */       browserTreeNode.setName(Util.null2String(recordSet1.getString("subcompanyname")));
/*  774 */       browserTreeNode.setPid(Util.null2String("comp_" + Util.getIntValue(recordSet1.getString("supsubcomid"), 0)));
/*  775 */       browserTreeNode.setCheckStrictly(false);
/*  776 */       browserTreeNode.setSelected(false);
/*  777 */       browserTreeNode.setIsImgIcon(false);
/*  778 */       if (this.IS_SINGLE_SELECT.equals(paramString1)) {
/*  779 */         browserTreeNode.setCanClick(false);
/*      */       } else {
/*  781 */         browserTreeNode.setCanClick(true);
/*      */       } 
/*  783 */       browserTreeNode.setType("subcompany");
/*  784 */       browserTreeNode.setIcon("icon-coms-LargeArea");
/*  785 */       browserTreeNode.setSubs(new ArrayList());
/*      */       
/*  787 */       if (hashMap2.containsKey(str)) {
/*  788 */         List<String> list = (List)hashMap2.get(str);
/*  789 */         for (byte b = 0; b < list.size(); b++) {
/*  790 */           String str4 = list.get(b);
/*  791 */           BrowserTreeNode browserTreeNode1 = (BrowserTreeNode)hashMap1.get(str4);
/*  792 */           int i = Util.getIntValue(str, 0);
/*  793 */           String str5 = docReceiveUnitComInfo.getCanceled(str4);
/*  794 */           String str6 = docReceiveUnitComInfo.getSuperiorUnitId(str4);
/*  795 */           if (i > 0 && !"1".equals(str5) && "0".equals(str6)) {
/*  796 */             browserTreeNode.getSubs().add(browserTreeNode1);
/*  797 */             browserTreeNode.setIsParent(true);
/*  798 */             browserTreeNode.setContainSub(true);
/*      */           } 
/*      */         } 
/*      */       } 
/*  802 */       if (!browserTreeNode.isContainSub()) {
/*  803 */         browserTreeNode.setCanClick(false);
/*      */       }
/*  805 */       arrayList1.add(browserTreeNode);
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  811 */     List<BrowserTreeNode> list2 = new ArrayList();
/*  812 */     for (byte b2 = 0; b2 < arrayList1.size(); b2++) {
/*  813 */       if (null != arrayList1.get(b2) && null != ((BrowserTreeNode)arrayList1.get(b2)).getPid() && !((BrowserTreeNode)arrayList1.get(b2)).getPid().equals("comp_0")) {
/*  814 */         list2 = getAllSuperHrmSubCompany(subCompanyComInfo, list2, ((BrowserTreeNode)arrayList1.get(b2)).getId() + "", paramString1, true);
/*      */       }
/*      */     } 
/*      */     
/*  818 */     ArrayList<BrowserTreeNode> arrayList3 = new ArrayList();
/*  819 */     for (BrowserTreeNode browserTreeNode : list2) {
/*  820 */       if (!isContain(arrayList1, browserTreeNode.getId())) {
/*  821 */         arrayList3.add(browserTreeNode);
/*      */       }
/*      */     } 
/*      */     
/*  825 */     arrayList1.addAll(arrayList3);
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  830 */     return makeTree(arrayList1, "comp_0");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getAllSuperHrmSubCompany(SubCompanyComInfo paramSubCompanyComInfo, List<BrowserTreeNode> paramList, String paramString1, String paramString2, boolean paramBoolean) {
/*  841 */     String str = paramString1.replace("comp_", "");
/*  842 */     if (!"".equals(str)) {
/*  843 */       String str1 = paramSubCompanyComInfo.getSubCompanyname(str);
/*  844 */       String str2 = paramSubCompanyComInfo.getSupsubcomid(str);
/*  845 */       if (!"0".equals(str2)) {
/*  846 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  847 */         browserTreeNode.setId(Util.null2String("comp_" + str));
/*  848 */         browserTreeNode.setName(Util.null2String(str1));
/*  849 */         browserTreeNode.setPid(Util.null2String("comp_" + str2));
/*  850 */         browserTreeNode.setCheckStrictly(false);
/*  851 */         browserTreeNode.setSelected(false);
/*  852 */         browserTreeNode.setIsImgIcon(false);
/*  853 */         if (this.IS_SINGLE_SELECT.equals(paramString2)) {
/*  854 */           browserTreeNode.setCanClick(false);
/*      */         } else {
/*  856 */           browserTreeNode.setCanClick(true);
/*      */         } 
/*  858 */         browserTreeNode.setType("subcompany");
/*  859 */         browserTreeNode.setIcon("icon-coms-LargeArea");
/*  860 */         browserTreeNode.setSubs(new ArrayList());
/*  861 */         if (!isContain(paramList, browserTreeNode.getId()) && !paramBoolean) {
/*  862 */           paramList.add(browserTreeNode);
/*      */         }
/*  864 */         paramList = getAllSuperHrmSubCompany(paramSubCompanyComInfo, paramList, "comp_" + str2, paramString2, false);
/*      */       } else {
/*  866 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  867 */         browserTreeNode.setId(Util.null2String("comp_" + str));
/*  868 */         browserTreeNode.setName(Util.null2String(str1));
/*  869 */         browserTreeNode.setPid(Util.null2String("comp_" + str2));
/*  870 */         browserTreeNode.setCheckStrictly(false);
/*  871 */         browserTreeNode.setSelected(false);
/*  872 */         browserTreeNode.setIsImgIcon(false);
/*  873 */         if (this.IS_SINGLE_SELECT.equals(paramString2)) {
/*  874 */           browserTreeNode.setCanClick(false);
/*      */         } else {
/*  876 */           browserTreeNode.setCanClick(true);
/*      */         } 
/*  878 */         browserTreeNode.setType("subcompany");
/*  879 */         browserTreeNode.setIcon("icon-coms-LargeArea");
/*  880 */         browserTreeNode.setSubs(new ArrayList());
/*  881 */         if (!isContain(paramList, browserTreeNode.getId()) && !paramBoolean) {
/*  882 */           paramList.add(browserTreeNode);
/*      */         }
/*      */       } 
/*      */     } 
/*  886 */     return paramList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isContain(List<BrowserTreeNode> paramList, String paramString) {
/*  894 */     boolean bool = false;
/*  895 */     if (null != paramString && null != paramList) {
/*  896 */       for (BrowserTreeNode browserTreeNode : paramList) {
/*  897 */         if (paramString.equals(browserTreeNode.getId())) {
/*  898 */           bool = true;
/*      */         }
/*      */       } 
/*      */     }
/*  902 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> makeTree(List<BrowserTreeNode> paramList, String paramString) {
/*  914 */     return makeTree(paramList, paramString, false);
/*      */   }
/*      */   
/*      */   private List<BrowserTreeNode> makeTree(List<BrowserTreeNode> paramList, String paramString, boolean paramBoolean) {
/*  918 */     List<?> list = (List)paramList.stream().filter(paramBrowserTreeNode -> paramBrowserTreeNode.getPid().equals(paramString)).collect(Collectors.toList());
/*      */     
/*  920 */     List list1 = (List)paramList.stream().filter(paramBrowserTreeNode -> !paramBrowserTreeNode.getPid().equals(paramString)).collect(Collectors.toList());
/*      */     
/*  922 */     if ((list == null || list.size() <= 0) && this.canUseuntiList.indexOf(Integer.valueOf(Util.getIntValue(paramString))) < 0 && paramBoolean) {
/*  923 */       return new ArrayList<>();
/*      */     }
/*  925 */     CopyOnWriteArrayList<BrowserTreeNode> copyOnWriteArrayList = new CopyOnWriteArrayList(list);
/*  926 */     copyOnWriteArrayList.forEach(paramBrowserTreeNode -> {
/*      */           makeTree(paramList1, paramBrowserTreeNode.getId(), paramBoolean).forEach(());
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           if (!paramBrowserTreeNode.getIsParent() && this.canUseuntiList.indexOf(paramBrowserTreeNode.getId()) < 0) {
/*      */             paramList2.remove(paramBrowserTreeNode);
/*      */           }
/*      */         });
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  943 */     return copyOnWriteArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BrowserTreeNode getV2SubCompanyTreeList(BrowserTreeNode paramBrowserTreeNode, String paramString1, String paramString2, List paramList, String paramString3, User paramUser, String paramString4) throws Exception {
/*  954 */     List<BrowserTreeNode> list = new ArrayList();
/*      */     
/*  956 */     if ("0".equals(paramString1)) {
/*  957 */       RecordSet recordSet = new RecordSet();
/*  958 */       recordSet.executeQuery("select distinct du.subcompanyid,hs.subcompanyname from docreceiveunit du left join hrmsubcompany hs on du.subcompanyid=hs.id where  (du.canceled is null or du.canceled<>'1') and hs.id is not null", new Object[0]);
/*  959 */       while (recordSet.next()) {
/*  960 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  961 */         browserTreeNode.setName(recordSet.getString(2));
/*  962 */         browserTreeNode.setId(recordSet.getString(1));
/*  963 */         browserTreeNode.setPid(paramString1);
/*  964 */         browserTreeNode.setIcon("icon-coms-LargeArea");
/*  965 */         browserTreeNode.setCheckStrictly(false);
/*  966 */         browserTreeNode.setSelected(false);
/*  967 */         browserTreeNode.setIsImgIcon(false);
/*  968 */         browserTreeNode.setType("subcom");
/*  969 */         browserTreeNode.setCanClick(true);
/*  970 */         if (hasChild_doc("subcom", recordSet.getString(1))) {
/*  971 */           browserTreeNode.setIsParent(true);
/*  972 */           browserTreeNode.setContainSub(true);
/*      */         } 
/*  974 */         list.add(browserTreeNode);
/*      */       } 
/*      */     } else {
/*  977 */       list = getDocDepartment(paramString1, "0", paramList, paramString3, paramUser, paramString4, paramString2);
/*      */     } 
/*  979 */     paramBrowserTreeNode.setSubs(list);
/*  980 */     return paramBrowserTreeNode;
/*      */   }
/*      */   
/*      */   private List removal(List paramList) {
/*  984 */     ArrayList<Map> arrayList = new ArrayList();
/*      */     
/*  986 */     for (Map map : paramList) {
/*  987 */       Boolean bool = Boolean.valueOf(true);
/*  988 */       for (Map map1 : arrayList) {
/*      */         
/*  990 */         if (((Map)map).get("id").equals(((Map)map1).get("id"))) {
/*  991 */           bool = Boolean.valueOf(false);
/*      */         }
/*      */       } 
/*  994 */       if (bool.booleanValue())
/*  995 */         arrayList.add(map); 
/*      */     } 
/*  997 */     return arrayList;
/*      */   }
/*      */   
/*      */   private Map getUnit(String paramString) {
/* 1001 */     RecordSet recordSet = new RecordSet();
/* 1002 */     HashMap<Object, Object> hashMap = null;
/* 1003 */     recordSet.executeQuery("select d1.id,d1.receiveunitname,d2.subcompanyname  from docreceiveunit d1 left join hrmsubcompany d2 on d1.subcompanyid=d2.id  where (d1.canceled is null or d1.canceled<>'1') and d1.id=? order by d1.showorder asc ", new Object[] { paramString });
/* 1004 */     if (recordSet.next()) {
/* 1005 */       hashMap = new HashMap<>();
/* 1006 */       hashMap.put("id", recordSet.getString("id"));
/* 1007 */       hashMap.put("receiveunitname", recordSet.getString("receiveunitname"));
/* 1008 */       hashMap.put("subcompanyid", recordSet.getString("subcompanyname"));
/*      */     } 
/* 1010 */     return hashMap;
/*      */   }
/*      */   
/*      */   private void getUnits(String paramString1, String paramString2, List<HashMap<Object, Object>> paramList) {
/* 1014 */     RecordSet recordSet = new RecordSet();
/* 1015 */     String str = "";
/* 1016 */     if ("dept".equals(paramString1)) {
/* 1017 */       if (StringUtils.isBlank(this.canUseuntis)) {
/* 1018 */         str = "select d1.id,d1.receiveunitname,d2.subcompanyname  from docreceiveunit d1 left join hrmsubcompany d2 on d1.subcompanyid=d2.id where (d1.canceled is null or d1.canceled<>'1') and superiorunitid=?";
/*      */       } else {
/* 1020 */         str = "select d1.id,d1.receiveunitname,d2.subcompanyname  from docreceiveunit d1 left join hrmsubcompany d2 on d1.subcompanyid=d2.id where (d1.canceled is null or d1.canceled<>'1') and   (" + Util.getSubINClause(this.canUseuntis, "d1.id", "IN") + ") and superiorunitid=?";
/*      */       } 
/* 1022 */     } else if ("subcom".equals(paramString1)) {
/*      */       
/* 1024 */       if (StringUtils.isBlank(this.canUseuntis)) {
/* 1025 */         str = "select d1.id,d1.receiveunitname,d2.subcompanyname  from docreceiveunit d1 left join hrmsubcompany d2 on d1.subcompanyid=d2.id where (d1.canceled is null or d1.canceled<>'1') and subcompanyid=?";
/*      */       } else {
/* 1027 */         str = "select d1.id,d1.receiveunitname,d2.subcompanyname  from docreceiveunit d1 left join hrmsubcompany d2 on d1.subcompanyid=d2.id where (d1.canceled is null or d1.canceled<>'1') and  (" + Util.getSubINClause(this.canUseuntis, "d1.id", "IN") + ") and subcompanyid=?";
/*      */       } 
/*      */     } 
/* 1030 */     str = str + " order by d1.showorder asc";
/* 1031 */     if (!"".equals(paramString2)) {
/*      */       
/* 1033 */       recordSet.executeQuery(str, new Object[] { paramString2 });
/* 1034 */       while (recordSet.next()) {
/* 1035 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 1036 */         hashMap.put("id", recordSet.getString("id"));
/* 1037 */         hashMap.put("receiveunitname", recordSet.getString("RECEIVEUNITNAME"));
/* 1038 */         hashMap.put("subcompanyid", recordSet.getString("subcompanyname"));
/* 1039 */         paramList.add(hashMap);
/* 1040 */         if (hasChild_doc("dept", recordSet.getString("id"))) {
/* 1041 */           getUnits("dept", recordSet.getString("id"), paramList);
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getDocDepartment(String paramString1, String paramString2, List paramList, String paramString3, User paramUser, String paramString4, String paramString5) {
/* 1049 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 1050 */     if (!"".equals(paramString1) && "0".equals(paramString2)) {
/* 1051 */       RecordSet recordSet = new RecordSet();
/* 1052 */       String str = "select * from docreceiveunit where (canceled is null or canceled<>'1') and SUBCOMPANYID=? ";
/* 1053 */       if (!StringUtils.isBlank(this.canUseuntis)) {
/* 1054 */         str = "select * from docreceiveunit where (canceled is null or canceled<>'1') and  (" + Util.getSubINClause(this.canUseuntis, "id", "IN") + ") and SUBCOMPANYID=? ";
/*      */       }
/* 1056 */       if (StringUtil.isNotNull(paramString4)) {
/* 1057 */         str = str + paramString4;
/*      */       }
/* 1059 */       str = str + " order by showorder,id asc ";
/* 1060 */       recordSet.executeQuery(str, new Object[] { paramString1.replace("comp_", "") });
/*      */       
/* 1062 */       while (recordSet.next()) {
/* 1063 */         if ("0".equals(recordSet.getString("SUPERIORUNITID"))) {
/* 1064 */           BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 1065 */           browserTreeNode.setName(recordSet.getString("RECEIVEUNITNAME"));
/* 1066 */           browserTreeNode.setCheckStrictly(false);
/* 1067 */           browserTreeNode.setId(recordSet.getString("ID"));
/* 1068 */           browserTreeNode.setType("dept");
/* 1069 */           browserTreeNode.setIcon("icon-coms-Branch");
/* 1070 */           browserTreeNode.setCanClick(true);
/* 1071 */           browserTreeNode.setSelected(false);
/* 1072 */           browserTreeNode.setIsImgIcon(false);
/* 1073 */           if (hasChild_doc("dept", recordSet.getString("ID"))) {
/* 1074 */             browserTreeNode.setIsParent(true);
/*      */           }
/* 1076 */           if (!"1".equals(recordSet.getString("CANCELED"))) {
/* 1077 */             arrayList.add(browserTreeNode);
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } else {
/*      */       
/* 1083 */       RecordSet recordSet = new RecordSet();
/* 1084 */       String str = "select * from docreceiveunit where (canceled is null or canceled<>'1') and SUPERIORUNITID=? ";
/* 1085 */       if (!StringUtils.isBlank(this.canUseuntis)) {
/* 1086 */         str = "select * from docreceiveunit where (canceled is null or canceled<>'1') and (" + Util.getSubINClause(this.canUseuntis, "id", "IN") + ") and SUPERIORUNITID=? ";
/*      */       }
/* 1088 */       if (StringUtil.isNotNull(paramString4)) {
/* 1089 */         str = str + paramString4;
/*      */       }
/* 1091 */       str = str + " order by showorder,id asc  ";
/*      */ 
/*      */ 
/*      */       
/* 1095 */       recordSet.executeQuery(str, new Object[] { paramString2 });
/*      */       
/* 1097 */       while (recordSet.next()) {
/* 1098 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 1099 */         browserTreeNode.setName(recordSet.getString("RECEIVEUNITNAME"));
/* 1100 */         browserTreeNode.setId(recordSet.getString("ID"));
/* 1101 */         browserTreeNode.setType("dept");
/* 1102 */         browserTreeNode.setIcon("icon-coms-Branch");
/* 1103 */         browserTreeNode.setCanClick(true);
/* 1104 */         browserTreeNode.setSelected(false);
/* 1105 */         browserTreeNode.setIsImgIcon(false);
/* 1106 */         browserTreeNode.setCheckStrictly(false);
/* 1107 */         if (hasChild_doc("dept", recordSet.getString("ID"))) {
/* 1108 */           browserTreeNode.setIsParent(true);
/*      */         }
/* 1110 */         if (!"1".equals(recordSet.getString("CANCELED"))) {
/* 1111 */           arrayList.add(browserTreeNode);
/*      */         }
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1117 */     return arrayList;
/*      */   }
/*      */   
/*      */   private boolean hasChild_doc(String paramString1, String paramString2) {
/* 1121 */     boolean bool = false;
/* 1122 */     if ("dept".equals(paramString1)) {
/* 1123 */       if (!"".equals(paramString2)) {
/* 1124 */         RecordSet recordSet = new RecordSet();
/* 1125 */         if (StringUtils.isBlank(this.canUseuntis)) {
/* 1126 */           recordSet.executeQuery("select count(*) from docreceiveunit where (canceled is null or canceled<>'1') and subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) and SUPERIORUNITID=?", new Object[] { paramString2 });
/*      */         } else {
/* 1128 */           recordSet.executeQuery("select count(*) from docreceiveunit where (canceled is null or canceled<>'1') and subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) and (" + Util.getSubINClause(this.canUseuntis, "id", "IN") + ") and SUPERIORUNITID=?", new Object[] { paramString2 });
/*      */         } 
/*      */         
/* 1131 */         if (recordSet.next() && 
/* 1132 */           recordSet.getInt(1) > 0) {
/* 1133 */           bool = true;
/*      */         }
/*      */       }
/*      */     
/* 1137 */     } else if ("subcom".equals(paramString1)) {
/* 1138 */       RecordSet recordSet = new RecordSet();
/* 1139 */       if (StringUtils.isBlank(this.canUseuntis)) {
/* 1140 */         recordSet.executeQuery("select count(*) from docreceiveunit where (canceled is null or canceled<>'1') and subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) and SUBCOMPANYID=?", new Object[] { paramString2 });
/*      */       } else {
/* 1142 */         recordSet.executeQuery("select count(*) from docreceiveunit where (canceled is null or canceled<>'1') and subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) and  (" + Util.getSubINClause(this.canUseuntis, "id", "IN") + ") and SUBCOMPANYID=?", new Object[] { paramString2 });
/*      */       } 
/*      */       
/* 1145 */       if (recordSet.next() && 
/* 1146 */         recordSet.getInt(1) > 0) {
/* 1147 */         bool = true;
/*      */       }
/*      */     }
/* 1150 */     else if ("subgroup".equals(paramString1)) {
/* 1151 */       RecordSet recordSet = new RecordSet();
/* 1152 */       if (StringUtils.isBlank(this.canUseuntis)) {
/* 1153 */         recordSet.executeQuery("select count(*) from ODocGroupMembers o,docreceiveunit d where o.memberId = d.ID and d.subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) and groupid=?", new Object[] { paramString2 });
/*      */       } else {
/* 1155 */         recordSet.executeQuery("select count(*) from ODocGroupMembers o,docreceiveunit d where o.memberId = d.ID and d.subcompanyid in(select id from hrmsubcompany where (canceled is null or canceled = '' or canceled =0)) and  (" + Util.getSubINClause(this.canUseuntis, "memberId", "IN") + ") and groupid=?", new Object[] { paramString2 });
/*      */       } 
/*      */       
/* 1158 */       if (recordSet.next() && 
/* 1159 */         recordSet.getInt(1) > 0) {
/* 1160 */         bool = true;
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1166 */     return bool;
/*      */   }
/*      */   
/*      */   private boolean hasChild(String paramString1, String paramString2) {
/* 1170 */     boolean bool = false;
/*      */     try {
/* 1172 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1173 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1174 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1175 */       SubCompanyVirtualComInfo subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/* 1176 */       DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/* 1177 */       ResourceVirtualComInfo resourceVirtualComInfo = new ResourceVirtualComInfo();
/* 1178 */       if (Util.getIntValue(paramString2) < 0) {
/* 1179 */         if (paramString1.equals("subcompany")) {
/* 1180 */           subCompanyVirtualComInfo.setTofirstRow();
/* 1181 */           while (subCompanyVirtualComInfo.next()) {
/* 1182 */             if (subCompanyVirtualComInfo.getSupsubcomid().equals(paramString2) && !"1".equals(subCompanyVirtualComInfo.getCompanyiscanceled())) {
/* 1183 */               bool = true;
/*      */             }
/*      */           } 
/* 1186 */           departmentVirtualComInfo.setTofirstRow();
/* 1187 */           while (departmentVirtualComInfo.next()) {
/* 1188 */             if (departmentVirtualComInfo.getSubcompanyid1().equals(paramString2) && !"1".equals(departmentVirtualComInfo.getDeparmentcanceled())) {
/* 1189 */               bool = true;
/*      */             }
/*      */           } 
/* 1192 */         } else if (paramString1.equals("dept")) {
/*      */           
/* 1194 */           departmentVirtualComInfo.setTofirstRow();
/* 1195 */           while (departmentVirtualComInfo.next()) {
/* 1196 */             String str = departmentVirtualComInfo.getSubcompanyid1(paramString2);
/* 1197 */             if (departmentVirtualComInfo.getSubcompanyid1().equals(str) && departmentVirtualComInfo.getDepartmentsupdepid().equals(paramString2) && !"1".equals(departmentVirtualComInfo.getDeparmentcanceled()))
/* 1198 */               bool = true; 
/*      */           } 
/* 1200 */           if (!bool) {
/* 1201 */             RecordSet recordSet = new RecordSet();
/* 1202 */             recordSet.executeSql("select count(*) from HrmResourceVirtualView t1 where t1.status in (0,1,2,3) and t1.departmentid=" + paramString2);
/* 1203 */             if (recordSet.next() && 
/* 1204 */               recordSet.getInt(1) > 0) {
/* 1205 */               bool = true;
/*      */             }
/*      */           }
/*      */         
/*      */         }
/*      */       
/* 1211 */       } else if (paramString1.equals("subcompany")) {
/* 1212 */         subCompanyComInfo.setTofirstRow();
/* 1213 */         while (subCompanyComInfo.next()) {
/* 1214 */           if (subCompanyComInfo.getSupsubcomid().equals(paramString2) && !"1".equals(subCompanyComInfo.getCompanyiscanceled())) {
/* 1215 */             bool = true;
/*      */           }
/*      */         } 
/* 1218 */         departmentComInfo.setTofirstRow();
/* 1219 */         while (departmentComInfo.next()) {
/* 1220 */           if (departmentComInfo.getSubcompanyid1().equals(paramString2) && !"1".equals(departmentComInfo.getDeparmentcanceled())) {
/* 1221 */             bool = true;
/*      */           }
/*      */         } 
/* 1224 */       } else if (paramString1.equals("dept")) {
/* 1225 */         departmentComInfo.setTofirstRow();
/* 1226 */         while (departmentComInfo.next()) {
/* 1227 */           String str = departmentComInfo.getSubcompanyid1(paramString2);
/* 1228 */           if (departmentComInfo.getSubcompanyid1().equals(str) && departmentComInfo.getDepartmentsupdepid().equals(paramString2) && !"1".equals(departmentComInfo.getDeparmentcanceled()))
/* 1229 */             bool = true; 
/*      */         } 
/* 1231 */         if (!bool) {
/* 1232 */           resourceComInfo.setTofirstRow();
/* 1233 */           while (resourceComInfo.next()) {
/* 1234 */             String str = resourceComInfo.getDepartmentID();
/* 1235 */             if (str.equals(paramString2)) {
/* 1236 */               bool = true;
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/* 1241 */     } catch (Exception exception) {
/* 1242 */       writeLog(exception);
/*      */     } 
/* 1244 */     return bool;
/*      */   }
/*      */   
/*      */   private TreeNode getResourceTreeListV2(TreeNode paramTreeNode, String paramString1, List paramList, String paramString2, User paramUser, String paramString3, String paramString4) throws Exception {
/*      */     try {
/* 1249 */       RecordSet recordSet = new RecordSet();
/* 1250 */       AppDetachComInfo appDetachComInfo = new AppDetachComInfo();
/* 1251 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1252 */       MutilResourceBrowser mutilResourceBrowser = new MutilResourceBrowser();
/* 1253 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*      */       
/* 1255 */       Dao_Hrm4Ec dao_Hrm4Ec = null;
/* 1256 */       if (!"oracle".equals(recordSet.getDBType())) {
/* 1257 */         dao_Hrm4Ec = Dao_Hrm4EcFactory.getInstance().getDao("Dao_Hrm4EcSqlServer");
/*      */       } else {
/* 1259 */         dao_Hrm4Ec = Dao_Hrm4EcFactory.getInstance().getDao("Dao_Hrm4EcOracle");
/*      */       } 
/*      */       
/* 1262 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1263 */       departmentComInfo.setTofirstRow();
/* 1264 */       if (!"".equals(paramString4)) {
/* 1265 */         String str = " select hr.id, hr.loginid, hr.account, lastname, hr.pinyinlastname, hr.subcompanyid1, hr.jobtitle,hr.workcode  from hrmresource hr where 1=1 ";
/*      */         
/* 1267 */         if (paramString3.length() > 0) {
/* 1268 */           if (paramString3.trim().startsWith("and")) {
/* 1269 */             paramString3 = " " + paramString3;
/*      */           } else {
/* 1271 */             paramString3 = " and " + paramString3;
/*      */           } 
/*      */         }
/*      */         
/* 1275 */         paramString3 = paramString3 + " and hr.status in (0,1,2,3)";
/* 1276 */         if (!paramString2.equals("1")) {
/* 1277 */           paramString3 = paramString3 + " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*      */         }
/* 1279 */         if (appDetachComInfo.isUseAppDetach()) {
/* 1280 */           String str1 = appDetachComInfo.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/* 1281 */           String str2 = (str1 != null && !"".equals(str1)) ? (" and " + str1) : "";
/* 1282 */           paramString3 = paramString3 + str2;
/*      */         } 
/* 1284 */         if (paramString3.length() > 0)
/* 1285 */           str = str + paramString3; 
/* 1286 */         str = str + "and exists (select * from hrmresourcevirtual where hr.id = resourceid and departmentid=" + paramString1 + ") order by hr.dsporder ";
/*      */ 
/*      */         
/* 1289 */         paramTreeNode = dao_Hrm4Ec.getHrmResourceV2(paramTreeNode, str, paramString2, paramList);
/* 1290 */         if (null != paramTreeNode) {
/* 1291 */           int i = paramTreeNode.getChildren().size();
/* 1292 */           for (byte b = 0; b < i; b++) {
/* 1293 */             String str1 = ((TreeNode)paramTreeNode.getChildren().get(b)).getId();
/* 1294 */             String str2 = departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID(str1));
/* 1295 */             String str3 = subCompanyComInfo.getSupsubcomid(str2);
/* 1296 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setDepartmentname(departmentComInfo.getDepartmentname(resourceComInfo.getDepartmentID(str1)));
/* 1297 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setSubcompanyname(subCompanyComInfo.getSubCompanyname(str2));
/* 1298 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setSupsubcompanyname(subCompanyComInfo.getSubCompanyname(str3));
/* 1299 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setIcon(resourceComInfo.getMessagerUrls(str1));
/* 1300 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setJobtitlename(MutilResourceBrowser.getJobTitlesname(str1));
/*      */           } 
/*      */         } 
/*      */       } else {
/* 1304 */         String str = "select hr.id, lastname, hr.pinyinlastname, hr.subcompanyid1, hr.jobtitle, loginid, account ,hr.workcode,hr.departmentid from hrmresource hr, hrmdepartment t2  where hr.departmentid=t2.id and t2.id=" + paramString1;
/*      */ 
/*      */         
/* 1307 */         if (paramString3.length() > 0) {
/* 1308 */           if (paramString3.trim().startsWith("and")) {
/* 1309 */             paramString3 = " " + paramString3;
/*      */           } else {
/* 1311 */             paramString3 = " and " + paramString3;
/*      */           } 
/*      */         }
/* 1314 */         paramString3 = paramString3 + " and hr.status in (0,1,2,3)";
/* 1315 */         if (!paramString2.equals("1")) {
/* 1316 */           paramString3 = paramString3 + " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*      */         }
/* 1318 */         if (appDetachComInfo.isUseAppDetach()) {
/*      */           
/* 1320 */           String str1 = appDetachComInfo.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/*      */           
/* 1322 */           String str2 = (str1 != null && !"".equals(str1)) ? (" and " + str1) : "";
/* 1323 */           paramString3 = paramString3 + str2;
/*      */         } 
/* 1325 */         if (paramString3.length() > 0)
/* 1326 */           str = str + paramString3; 
/* 1327 */         str = str + " order by hr.dsporder ";
/* 1328 */         paramTreeNode = dao_Hrm4Ec.getHrmResourceV2(paramTreeNode, str, paramString2, paramList);
/* 1329 */         if (null != paramTreeNode) {
/* 1330 */           int i = paramTreeNode.getChildren().size();
/* 1331 */           for (byte b = 0; b < i; b++) {
/* 1332 */             String str1 = ((TreeNode)paramTreeNode.getChildren().get(b)).getId();
/* 1333 */             String str2 = departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID(str1));
/* 1334 */             String str3 = subCompanyComInfo.getSupsubcomid(str2);
/* 1335 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setDepartmentname(departmentComInfo.getDepartmentname(resourceComInfo.getDepartmentID(str1)));
/* 1336 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setSubcompanyname(subCompanyComInfo.getSubCompanyname(str2));
/* 1337 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setSupsubcompanyname(subCompanyComInfo.getSubCompanyname(str3));
/* 1338 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setIcon(resourceComInfo.getMessagerUrls(str1));
/* 1339 */             ((TreeNode)paramTreeNode.getChildren().get(b)).setJobtitlename(MutilResourceBrowser.getJobTitlesname(str1));
/*      */           } 
/*      */         } 
/*      */       } 
/* 1343 */     } catch (Exception exception) {
/* 1344 */       writeLog(exception);
/*      */     } 
/*      */     
/* 1347 */     return paramTreeNode;
/*      */   }
/*      */   
/*      */   private Boolean checkSharePermission(User paramUser, String paramString) {
/* 1351 */     if (paramUser.isAdmin())
/* 1352 */       return Boolean.valueOf(true); 
/* 1353 */     Integer integer1 = Integer.valueOf(paramUser.getUserSubCompany1());
/* 1354 */     Integer integer2 = Integer.valueOf(paramUser.getUID());
/* 1355 */     List<Map> list = getRole(integer2);
/* 1356 */     Integer integer3 = Integer.valueOf(paramUser.getUserDepartment());
/* 1357 */     Integer integer4 = Integer.valueOf(Util.getIntValue(paramUser.getSeclevel(), -1));
/* 1358 */     String str1 = paramUser.getJoblevel();
/* 1359 */     String str2 = paramUser.getJobtitle();
/* 1360 */     RecordSet recordSet = new RecordSet();
/* 1361 */     String str3 = "select * from CommReceiveGroup where unitgroupid=?";
/* 1362 */     recordSet.executeQuery(str3, new Object[] { paramString });
/*      */     
/* 1364 */     while (recordSet.next()) {
/* 1365 */       Integer integer5 = Integer.valueOf(recordSet.getInt(3));
/* 1366 */       Integer integer6 = Integer.valueOf(recordSet.getInt(5));
/* 1367 */       Integer integer7 = Integer.valueOf(recordSet.getInt(6));
/* 1368 */       String str4 = recordSet.getString(4);
/* 1369 */       String str5 = recordSet.getString(8);
/* 1370 */       String str6 = recordSet.getString(9);
/* 1371 */       String str7 = recordSet.getString(7);
/* 1372 */       String str8 = recordSet.getString(10);
/* 1373 */       if (integer5.intValue() == 1) {
/* 1374 */         if (str4.equals(integer2.toString()))
/* 1375 */           return Boolean.valueOf(true);  continue;
/* 1376 */       }  if (integer5.intValue() == 2) {
/*      */         
/* 1378 */         if (integer4.intValue() >= integer6.intValue() && integer4.intValue() <= integer7.intValue()) {
/*      */           
/* 1380 */           if (str4.equals(integer1.toString()))
/* 1381 */             return Boolean.valueOf(true); 
/* 1382 */           if ("1".equals(str8)) {
/* 1383 */             RecordSet recordSet1 = new RecordSet();
/* 1384 */             String str = "select id from hrmsubcompany where supsubcomid=?";
/* 1385 */             recordSet1.executeQuery(str, new Object[] { str4 });
/* 1386 */             while (recordSet1.next()) {
/* 1387 */               if (recordSet1.getInt(1) == integer1.intValue())
/* 1388 */                 return Boolean.valueOf(true); 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */         continue;
/*      */       } 
/* 1394 */       if (integer5.intValue() == 3) {
/* 1395 */         if (integer4.intValue() >= integer6.intValue() && integer4.intValue() <= integer7.intValue()) {
/* 1396 */           if (str4.equals(integer3.toString()))
/* 1397 */             return Boolean.valueOf(true); 
/* 1398 */           if ("1".equals(str8)) {
/*      */             
/* 1400 */             String str = "select id from hrmdepartment where supdepid=?";
/* 1401 */             RecordSet recordSet1 = new RecordSet();
/* 1402 */             recordSet1.executeQuery(str, new Object[] { str4 });
/* 1403 */             while (recordSet1.next()) {
/* 1404 */               String str9 = recordSet1.getString(1);
/* 1405 */               if (str9.equals(integer3.toString()))
/* 1406 */                 return Boolean.valueOf(true); 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */         continue;
/*      */       } 
/* 1412 */       if (integer5.intValue() == 4) {
/* 1413 */         if (integer4.intValue() >= integer6.intValue() && integer4.intValue() <= integer7.intValue()) {
/* 1414 */           for (Map map : list) {
/* 1415 */             if (str4.equals(map.get("roleid").toString())) {
/* 1416 */               if (str7.equals("0")) {
/* 1417 */                 if ("2".equals(map.get("rolelevel").toString()))
/* 1418 */                   return Boolean.valueOf(true);  continue;
/* 1419 */               }  if (str7.equals("2")) {
/* 1420 */                 if ("0".equals(map.get("rolelevel").toString()))
/* 1421 */                   return Boolean.valueOf(true);  continue;
/*      */               } 
/* 1423 */               if (str7.equals(map.get("rolelevel").toString())) {
/* 1424 */                 return Boolean.valueOf(true);
/*      */               }
/*      */             } 
/*      */           } 
/*      */         }
/*      */         
/*      */         continue;
/*      */       } 
/*      */       
/* 1433 */       if (integer5.intValue() == 7) {
/*      */         
/* 1435 */         if (str2.equals(str4)) {
/* 1436 */           if ("1".equals(str7)) {
/* 1437 */             if (str5.equals(integer1.toString()))
/* 1438 */               return Boolean.valueOf(true);  continue;
/* 1439 */           }  if ("2".equals(str7)) {
/* 1440 */             if (str6.equals(integer3.toString()))
/* 1441 */               return Boolean.valueOf(true); 
/*      */             continue;
/*      */           } 
/* 1444 */           return Boolean.valueOf(true);
/*      */         } 
/*      */         
/*      */         continue;
/*      */       } 
/* 1449 */       if (integer5.intValue() == 5)
/*      */       {
/* 1451 */         if (integer4.intValue() >= integer6.intValue() && integer4.intValue() <= integer7.intValue())
/* 1452 */           return Boolean.valueOf(true); 
/*      */       }
/*      */     } 
/* 1455 */     return Boolean.valueOf(false);
/*      */   }
/*      */   
/*      */   private List<Map> getRole(Integer paramInteger) {
/* 1459 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1460 */     if (paramInteger != null) {
/* 1461 */       List<Map> list = (new HrmCommonServiceImpl()).getRoleInfo(paramInteger.intValue());
/* 1462 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1463 */       HashMap<Object, Object> hashMap2 = null;
/* 1464 */       for (byte b = 0; list != null && b < list.size(); b++) {
/* 1465 */         Map map = list.get(b);
/* 1466 */         hashMap2 = new HashMap<>();
/* 1467 */         hashMap2.put("roleid", map.get("roleid"));
/* 1468 */         hashMap2.put("rolelevel", map.get("rolelevel"));
/* 1469 */         arrayList.add(hashMap2);
/*      */       } 
/*      */     } 
/* 1472 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCanUseUntis(Map<String, Object> paramMap, User paramUser, String paramString) {
/* 1478 */     String str1 = getUnitsDataDefinition(paramMap, paramUser, paramString);
/*      */     
/* 1480 */     if (paramUser.isAdmin()) {
/* 1481 */       if (StringUtils.isNotBlank(str1)) {
/* 1482 */         RecordSet recordSet1 = new RecordSet();
/* 1483 */         ArrayList<Integer> arrayList1 = new ArrayList();
/* 1484 */         recordSet1.executeQuery("select id from DocReceiveUnit a where 1 = 1 " + str1, new Object[0]);
/* 1485 */         while (recordSet1.next()) {
/* 1486 */           arrayList1.add(Integer.valueOf(recordSet1.getInt(1)));
/*      */         }
/* 1488 */         if (arrayList1.size() == 0) {
/* 1489 */           arrayList1.add(Integer.valueOf(-1));
/*      */         }
/* 1491 */         this.canUseuntiList = arrayList1;
/* 1492 */         return StringUtils.join(arrayList1.toArray(), ",");
/*      */       } 
/* 1494 */       return "";
/*      */     } 
/* 1496 */     Integer integer1 = Integer.valueOf(paramUser.getUserSubCompany1());
/* 1497 */     Integer integer2 = Integer.valueOf(paramUser.getUID());
/* 1498 */     List<Map> list = getRole(integer2);
/* 1499 */     Integer integer3 = Integer.valueOf(paramUser.getUserDepartment());
/* 1500 */     Integer integer4 = Integer.valueOf(Util.getIntValue(paramUser.getSeclevel(), -1));
/* 1501 */     String str2 = paramUser.getJoblevel();
/* 1502 */     String str3 = paramUser.getJobtitle();
/*      */     
/* 1504 */     RecordSet recordSet = new RecordSet();
/* 1505 */     recordSet.executeQuery("select a.id,b.unitid,b.type,b.content,b.seclevelfrom,b.seclevelto,b.relatedlevel,b.subcompanyid,b.departmentid,b.lowerlevel from DocReceiveUnit a left join sendReUnitRestrictions b on a.id=b.unitid where 1=1 " + str1, new Object[0]);
/* 1506 */     ArrayList<Integer> arrayList = new ArrayList();
/*      */     
/* 1508 */     int i = 0;
/*      */     
/* 1510 */     Boolean bool1 = Boolean.valueOf(false);
/* 1511 */     Boolean bool2 = Boolean.valueOf(true);
/* 1512 */     while (recordSet.next()) {
/* 1513 */       int j = recordSet.getInt("id");
/* 1514 */       int k = recordSet.getInt("unitid");
/* 1515 */       i = recordSet.getInt("type");
/* 1516 */       Integer integer5 = Integer.valueOf(recordSet.getInt("seclevelfrom"));
/* 1517 */       Integer integer6 = Integer.valueOf(recordSet.getInt("seclevelto"));
/* 1518 */       String str4 = recordSet.getString("content");
/* 1519 */       String str5 = recordSet.getString("subcompanyid");
/* 1520 */       String str6 = recordSet.getString("departmentid");
/* 1521 */       String str7 = recordSet.getString("relatedlevel");
/* 1522 */       String str8 = recordSet.getString("lowerlevel");
/* 1523 */       bool1 = Boolean.valueOf(false);
/* 1524 */       bool2 = Boolean.valueOf(true);
/* 1525 */       if (i == 1) {
/* 1526 */         if (str4.equals(integer2.toString())) {
/* 1527 */           bool1 = Boolean.valueOf(true);
/*      */         }
/* 1529 */       } else if (i == 2) {
/* 1530 */         if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 1531 */           if (str4.equals(integer1.toString())) {
/* 1532 */             bool1 = Boolean.valueOf(true);
/*      */           }
/* 1534 */           if ("1".equals(str8)) {
/* 1535 */             String str = SubCompanyComInfo.getAllChildSubcompanyId(str4, "");
/* 1536 */             String[] arrayOfString = str.split(",");
/* 1537 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 1538 */               if (arrayOfString[b].equals(integer1.toString())) {
/* 1539 */                 bool1 = Boolean.valueOf(true);
/*      */                 break;
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/* 1545 */       } else if (i == 3) {
/* 1546 */         if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 1547 */           if (str4.equals(integer3.toString())) {
/* 1548 */             bool1 = Boolean.valueOf(true);
/*      */           }
/* 1550 */           if ("1".equals(str8)) {
/*      */             
/* 1552 */             ArrayList arrayList1 = new ArrayList();
/* 1553 */             DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1554 */             departmentComInfo.getAllChildDeptByDepId(arrayList1, str4);
/* 1555 */             if (arrayList1.contains(integer3.toString())) {
/* 1556 */               bool1 = Boolean.valueOf(true);
/*      */             }
/*      */           } 
/*      */         } 
/* 1560 */       } else if (i == 4) {
/* 1561 */         if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 1562 */           for (Map map : list) {
/* 1563 */             if (str4.equals(map.get("roleid").toString())) {
/* 1564 */               if (str7.equals("0")) {
/* 1565 */                 if ("2".equals(map.get("rolelevel").toString())) {
/* 1566 */                   bool1 = Boolean.valueOf(true); break;
/*      */                 }  continue;
/*      */               } 
/* 1569 */               if (str7.equals("2")) {
/* 1570 */                 if ("0".equals(map.get("rolelevel").toString())) {
/* 1571 */                   bool1 = Boolean.valueOf(true); break;
/*      */                 } 
/*      */                 continue;
/*      */               } 
/* 1575 */               if (str7.equals(map.get("rolelevel").toString())) {
/* 1576 */                 bool1 = Boolean.valueOf(true);
/*      */                 
/*      */                 break;
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         }
/* 1583 */       } else if (i == 7) {
/* 1584 */         if (str3.equals(str4)) {
/* 1585 */           if ("1".equals(str7)) {
/* 1586 */             if (str5.equals(integer1.toString())) {
/* 1587 */               bool1 = Boolean.valueOf(true);
/*      */             }
/* 1589 */           } else if ("2".equals(str7)) {
/* 1590 */             if (str6.equals(integer3.toString())) {
/* 1591 */               bool1 = Boolean.valueOf(true);
/*      */             }
/*      */           } else {
/* 1594 */             bool1 = Boolean.valueOf(true);
/*      */           } 
/*      */         }
/* 1597 */       } else if (i == 5) {
/* 1598 */         if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 1599 */           bool1 = Boolean.valueOf(true);
/*      */         }
/*      */       } else {
/* 1602 */         bool2 = Boolean.valueOf(false);
/*      */       } 
/* 1604 */       if (bool1.booleanValue() && 
/* 1605 */         !arrayList.contains(Integer.valueOf(j))) {
/* 1606 */         arrayList.add(Integer.valueOf(j));
/*      */       }
/*      */       
/* 1609 */       if (!bool2.booleanValue() && !arrayList.contains(Integer.valueOf(j))) {
/* 1610 */         arrayList.add(Integer.valueOf(j));
/*      */       }
/*      */     } 
/* 1613 */     if (arrayList.size() == 0) {
/* 1614 */       arrayList.add(Integer.valueOf(-1));
/*      */     }
/* 1616 */     this.canUseuntiList = arrayList;
/* 1617 */     return StringUtils.join(arrayList.toArray(), ",");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCanUseUntis(Map<String, Object> paramMap, User paramUser) {
/* 1627 */     String str1 = getUnitsDataDefinition(paramMap, paramUser);
/*      */     
/* 1629 */     if (paramUser.isAdmin()) {
/* 1630 */       if (StringUtils.isNotBlank(str1)) {
/* 1631 */         RecordSet recordSet = new RecordSet();
/* 1632 */         ArrayList<Integer> arrayList1 = new ArrayList();
/* 1633 */         recordSet.executeQuery("select id from DocReceiveUnit where 1 = 1 " + str1, new Object[0]);
/* 1634 */         while (recordSet.next()) {
/* 1635 */           arrayList1.add(Integer.valueOf(recordSet.getInt(1)));
/*      */         }
/* 1637 */         if (arrayList1.size() == 0) {
/* 1638 */           arrayList1.add(Integer.valueOf(-1));
/*      */         }
/* 1640 */         this.canUseuntiList = arrayList1;
/* 1641 */         return StringUtils.join(arrayList1.toArray(), ",");
/*      */       } 
/* 1643 */       return "";
/*      */     } 
/* 1645 */     Integer integer1 = Integer.valueOf(paramUser.getUserSubCompany1());
/* 1646 */     Integer integer2 = Integer.valueOf(paramUser.getUID());
/* 1647 */     List<Map> list = getRole(integer2);
/* 1648 */     Integer integer3 = Integer.valueOf(paramUser.getUserDepartment());
/* 1649 */     Integer integer4 = Integer.valueOf(Util.getIntValue(paramUser.getSeclevel(), -1));
/* 1650 */     String str2 = paramUser.getJoblevel();
/* 1651 */     String str3 = paramUser.getJobtitle();
/*      */     
/* 1653 */     RecordSet recordSet1 = new RecordSet();
/* 1654 */     RecordSet recordSet2 = new RecordSet();
/* 1655 */     recordSet1.executeQuery("select * from sendReUnitRestrictions", new Object[0]);
/* 1656 */     recordSet2.executeQuery("select id from DocReceiveUnit where 1 = 1 " + str1, new Object[0]);
/* 1657 */     ArrayList<Integer> arrayList = new ArrayList();
/*      */     
/* 1659 */     int i = 0;
/*      */     
/* 1661 */     Boolean bool1 = Boolean.valueOf(false);
/* 1662 */     Boolean bool2 = Boolean.valueOf(false);
/* 1663 */     while (recordSet2.next()) {
/* 1664 */       int j = recordSet2.getInt(1);
/* 1665 */       bool1 = Boolean.valueOf(false);
/* 1666 */       bool2 = Boolean.valueOf(false);
/* 1667 */       while (recordSet1.next()) {
/* 1668 */         int k = recordSet1.getInt(2);
/* 1669 */         if (k == j) {
/* 1670 */           i = recordSet1.getInt(3);
/* 1671 */           Integer integer5 = Integer.valueOf(recordSet1.getInt(5));
/* 1672 */           Integer integer6 = Integer.valueOf(recordSet1.getInt(6));
/* 1673 */           String str4 = recordSet1.getString(4);
/* 1674 */           String str5 = recordSet1.getString(8);
/* 1675 */           String str6 = recordSet1.getString(9);
/* 1676 */           String str7 = recordSet1.getString(7);
/* 1677 */           String str8 = recordSet1.getString(10);
/* 1678 */           bool2 = Boolean.valueOf(true);
/* 1679 */           if (i == 1) {
/* 1680 */             if (str4.equals(integer2.toString())) {
/* 1681 */               bool1 = Boolean.valueOf(true); break;
/*      */             } 
/*      */             continue;
/*      */           } 
/* 1685 */           if (i == 2) {
/*      */             
/* 1687 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/*      */               
/* 1689 */               if (str4.equals(integer1.toString())) {
/* 1690 */                 bool1 = Boolean.valueOf(true);
/*      */                 
/*      */                 break;
/*      */               } 
/* 1694 */               if ("1".equals(str8)) {
/* 1695 */                 String str = SubCompanyComInfo.getAllChildSubcompanyId(str4, "");
/* 1696 */                 String[] arrayOfString = str.split(",");
/* 1697 */                 for (byte b = 0; b < arrayOfString.length; b++) {
/* 1698 */                   if (arrayOfString[b].equals(integer1.toString())) {
/* 1699 */                     bool1 = Boolean.valueOf(true); break;
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */             }  continue;
/*      */           } 
/* 1705 */           if (i == 3) {
/* 1706 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 1707 */               if (str4.equals(integer3.toString())) {
/* 1708 */                 bool1 = Boolean.valueOf(true);
/*      */                 
/*      */                 break;
/*      */               } 
/* 1712 */               if ("1".equals(str8)) {
/*      */                 
/* 1714 */                 ArrayList arrayList1 = new ArrayList();
/* 1715 */                 DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1716 */                 departmentComInfo.getAllChildDeptByDepId(arrayList1, str4);
/* 1717 */                 if (arrayList1.contains(integer3.toString())) {
/* 1718 */                   bool1 = Boolean.valueOf(true);
/*      */                 }
/*      */               } 
/*      */             } 
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/* 1726 */           if (i == 4) {
/* 1727 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 1728 */               for (Map map : list) {
/* 1729 */                 if (str4.equals(map.get("roleid").toString())) {
/* 1730 */                   if (str7.equals("0")) {
/* 1731 */                     if ("2".equals(map.get("rolelevel").toString())) {
/* 1732 */                       bool1 = Boolean.valueOf(true); break;
/*      */                     } 
/*      */                     continue;
/*      */                   } 
/* 1736 */                   if (str7.equals("2")) {
/* 1737 */                     if ("0".equals(map.get("rolelevel").toString())) {
/* 1738 */                       bool1 = Boolean.valueOf(true);
/*      */                       break;
/*      */                     } 
/*      */                     continue;
/*      */                   } 
/* 1743 */                   if (str7.equals(map.get("rolelevel").toString())) {
/* 1744 */                     bool1 = Boolean.valueOf(true);
/*      */                   }
/*      */                 } 
/*      */               } 
/*      */             }
/*      */ 
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/* 1754 */           if (i == 7) {
/*      */             
/* 1756 */             if (str3.equals(str4)) {
/* 1757 */               if ("1".equals(str7)) {
/* 1758 */                 if (str5.equals(integer1.toString())) {
/* 1759 */                   bool1 = Boolean.valueOf(true); break;
/*      */                 } 
/*      */                 continue;
/*      */               } 
/* 1763 */               if ("2".equals(str7)) {
/* 1764 */                 if (str6.equals(integer3.toString())) {
/* 1765 */                   bool1 = Boolean.valueOf(true);
/*      */                   
/*      */                   break;
/*      */                 } 
/*      */                 continue;
/*      */               } 
/* 1771 */               bool1 = Boolean.valueOf(true);
/*      */               
/*      */               break;
/*      */             } 
/*      */             continue;
/*      */           } 
/* 1777 */           if (i == 5)
/*      */           {
/* 1779 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 1780 */               bool1 = Boolean.valueOf(true);
/*      */ 
/*      */ 
/*      */               
/*      */               break;
/*      */             } 
/*      */           }
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1791 */       if (bool1.booleanValue() && 
/* 1792 */         !arrayList.contains(Integer.valueOf(j))) {
/* 1793 */         arrayList.add(Integer.valueOf(j));
/*      */       }
/*      */ 
/*      */       
/* 1797 */       if (!bool2.booleanValue() && !arrayList.contains(Integer.valueOf(j))) {
/* 1798 */         arrayList.add(Integer.valueOf(j));
/*      */       }
/* 1800 */       recordSet1.beforFirst();
/*      */     } 
/* 1802 */     if (arrayList.size() == 0) {
/* 1803 */       arrayList.add(Integer.valueOf(-1));
/*      */     }
/* 1805 */     this.canUseuntiList = arrayList;
/* 1806 */     return StringUtils.join(arrayList.toArray(), ",");
/*      */   }
/*      */ 
/*      */   
/*      */   public String getUnitsDataDefinition(Map<String, Object> paramMap, User paramUser) {
/* 1811 */     String str = "";
/*      */     
/*      */     try {
/* 1814 */       WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/*      */       
/* 1816 */       Map map = workflowCommonServiceImpl.getDataDefinitionDataRanageSet(paramMap, paramUser, 142);
/* 1817 */       String str1 = Util.null2String(map.get("sqlWhere"));
/* 1818 */       String str2 = Util.null2String(map.get("internalUnits"));
/* 1819 */       String str3 = Util.null2String(map.get("externalUnits"));
/*      */       
/* 1821 */       if (str1.length() > 0) {
/* 1822 */         str = str + " and subcompanyid " + str1;
/*      */       }
/* 1824 */       if ("1".equals(str3) && !"0".equals(str2)) {
/* 1825 */         str = str + " and  companytype = '1' ";
/* 1826 */       } else if (!"1".equals(str3) && "0".equals(str2)) {
/* 1827 */         str = str + " and  companytype = '0' ";
/*      */       } 
/* 1829 */       writeLog("收发文单位浏览数据定义条件---------sqlWhere = " + str);
/* 1830 */     } catch (Exception exception) {
/* 1831 */       exception.printStackTrace();
/* 1832 */       writeLog("获取收发文单位浏览数据定义条件失败！");
/* 1833 */       return "";
/*      */     } 
/* 1835 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getUnitsDataDefinition(Map<String, Object> paramMap, User paramUser, String paramString) {
/* 1840 */     String str = "";
/*      */     
/*      */     try {
/* 1843 */       WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/*      */       
/* 1845 */       Map map = workflowCommonServiceImpl.getDataDefinitionDataRanageSet(paramMap, paramUser, 142);
/* 1846 */       String str1 = Util.null2String(map.get("sqlWhere"));
/* 1847 */       String str2 = Util.null2String(map.get("internalUnits"));
/* 1848 */       String str3 = Util.null2String(map.get("externalUnits"));
/*      */       
/* 1850 */       if (str1.length() > 0) {
/* 1851 */         str = str + " and " + paramString + ".subcompanyid " + str1;
/*      */       }
/* 1853 */       if ("1".equals(str3) && !"0".equals(str2)) {
/* 1854 */         str = str + " and " + paramString + ".companytype = '1' ";
/* 1855 */       } else if (!"1".equals(str3) && "0".equals(str2)) {
/* 1856 */         str = str + " and  " + paramString + ".companytype = '0' ";
/*      */       } 
/* 1858 */       writeLog("收发文单位浏览数据定义条件---------sqlWhere = " + str);
/* 1859 */     } catch (Exception exception) {
/* 1860 */       exception.printStackTrace();
/* 1861 */       writeLog("获取收发文单位浏览数据定义条件失败！");
/* 1862 */       return "";
/*      */     } 
/* 1864 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SendDocBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */