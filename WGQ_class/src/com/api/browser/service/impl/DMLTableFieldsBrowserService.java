/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.HrmSynDAO;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.dmlaction.DBTypeUtil;
/*     */ import weaver.workflow.dmlaction.commands.bases.FieldBase;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DMLTableFieldsBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  33 */     RecordSet recordSet = new RecordSet();
/*  34 */     FieldBase fieldBase = new FieldBase();
/*     */ 
/*     */ 
/*     */     
/*  38 */     String str1 = Util.null2String(paramMap.get("datasourceid"));
/*  39 */     boolean bool = Util.null2String(paramMap.get("needcheckds")).equals("true");
/*  40 */     int i = Util.getIntValue(Util.null2String(paramMap.get("dmlformid")), 0);
/*  41 */     int j = Util.getIntValue(Util.null2String(paramMap.get("dmlisdetail")), 0);
/*  42 */     String str2 = Util.null2String(paramMap.get("dmltablename"));
/*  43 */     String str3 = Util.null2String(paramMap.get("callSource"));
/*  44 */     int k = Util.getIntValue(Util.null2String(paramMap.get("ajax")), 0);
/*     */ 
/*     */ 
/*     */     
/*  48 */     String str4 = StringUtils.trim(Util.null2String(paramMap.get("fieldname")));
/*  49 */     String str5 = StringUtils.trim(Util.null2String(paramMap.get("fielddbtype")));
/*     */ 
/*     */     
/*  52 */     if ((bool && !"".equals(str1)) || !bool)
/*     */     {
/*  54 */       if (!"".equals(str2))
/*  55 */         fieldBase.getDmltableFields(this.user, recordSet, str1, i, str2, j); 
/*     */     }
/*  57 */     Map map1 = fieldBase.getAllcolnums();
/*     */     
/*  59 */     boolean bool1 = false;
/*  60 */     Map map2 = new HashMap<>();
/*  61 */     if (StringUtils.isBlank(str1) && "hrsetting".equals(str3)) {
/*  62 */       RecordSet recordSet1 = new RecordSet();
/*  63 */       byte b = -1;
/*  64 */       if ("hrmresource".equals(str2)) {
/*  65 */         bool1 = true;
/*  66 */         b = 4;
/*  67 */       } else if ("hrmjobtitles".equals(str2)) {
/*  68 */         bool1 = true;
/*  69 */         b = 3;
/*  70 */       } else if ("hrmdepartment".equals(str2)) {
/*  71 */         bool1 = true;
/*  72 */         b = 2;
/*  73 */       } else if ("hrmsubcompany".equals(str2)) {
/*  74 */         bool1 = true;
/*  75 */         b = 1;
/*     */       } 
/*  77 */       map2 = HrmSynDAO.getFeildName(b, this.user.getLanguage());
/*     */     } 
/*     */ 
/*     */     
/*  81 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  82 */     if (null != map1 && map1.size() > 0) {
/*     */       
/*  84 */       String str = DBTypeUtil.getDataSourceDbtype(recordSet, str1);
/*  85 */       boolean bool2 = true;
/*  86 */       Set set = map1.keySet();
/*  87 */       for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/*     */         
/*  89 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  90 */         String str6 = Util.null2String(iterator.next());
/*  91 */         String str7 = Util.null2String((String)map1.get(str6));
/*  92 */         boolean bool3 = DBTypeUtil.checkFieldDBType(str7, str);
/*  93 */         String str8 = Util.null2String(map2.get(str6));
/*     */ 
/*     */         
/*  96 */         if (!"".equals(str4) && str6.indexOf(str4) == -1) {
/*     */           continue;
/*     */         }
/*  99 */         if (!"".equals(str5) && str7.indexOf(str5) == -1) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 104 */         if (k == 1 && str7.indexOf("clob") > -1) {
/* 105 */           bool3 = true;
/*     */         }
/* 107 */         str6 = (str6.indexOf(" ") > 0) ? ("[" + str6 + "]") : str6;
/* 108 */         str8 = StringUtils.isBlank(str8) ? str6 : str8;
/* 109 */         if (bool1) {
/* 110 */           hashMap1.put("fieldCnName", str8);
/*     */         }
/*     */         
/* 113 */         hashMap1.put("fieldname", str6);
/* 114 */         hashMap1.put("fielddbtype", str7);
/* 115 */         hashMap1.put("iscanhandle", Boolean.valueOf(bool3));
/* 116 */         hashMap1.put("otherdata", "<span datas=\"" + str6 + "|||" + str7 + "\">" + str6 + "</span>");
/*     */         
/* 118 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 125 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*     */     
/* 127 */     if (bool1) {
/* 128 */       arrayList1.add(new ListHeadBean("fieldCnName", SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), 1));
/*     */     }
/* 130 */     arrayList1.add((new ListHeadBean("fieldname", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), 1)).setIsPrimarykey(BoolAttr.TRUE).setIsInputCol(BoolAttr.TRUE));
/* 131 */     arrayList1.add(new ListHeadBean("fielddbtype", SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), 1));
/* 132 */     arrayList1.add((new ListHeadBean("iscanhandle", "", 2)).setDisplayReturnThis(BoolAttr.FALSE).setHide(BoolAttr.TRUE));
/* 133 */     arrayList1.add((new ListHeadBean("otherdata", "", 2)).setDisplayReturnThis(BoolAttr.FALSE).setHide(BoolAttr.TRUE).setIsInputCol(BoolAttr.FALSE));
/*     */ 
/*     */ 
/*     */     
/* 137 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 138 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 139 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 140 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 146 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 147 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 148 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 149 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 150 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 685, "fieldname", true));
/* 151 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 686, "fielddbtype"));
/* 152 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 159 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 161 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, null);
/* 162 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 163 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 164 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DMLTableFieldsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */