/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ public class HrmConTempletBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  26 */     String str1 = Util.null2String(paramMap.get("templetId"));
/*  27 */     String str2 = Util.null2String(paramMap.get("templetName"));
/*  28 */     String str3 = Util.null2String(paramMap.get("templetDocId"));
/*  29 */     String str4 = Util.null2String(paramMap.get("subcompanyId"));
/*  30 */     String str5 = Util.null2String((String)paramMap.get("doctype"));
/*     */     
/*  32 */     String str6 = " * ";
/*  33 */     String str7 = " from HrmContractTemplet ";
/*  34 */     String str8 = " where 1=1 ";
/*  35 */     if (!str2.equals("")) {
/*  36 */       str8 = str8 + " and templetname like '%";
/*  37 */       str8 = str8 + Util.fromScreen2(str2, this.user.getLanguage());
/*  38 */       str8 = str8 + "%'";
/*     */     } 
/*  40 */     if (!str1.equals("")) {
/*  41 */       str8 = str8 + " and id=" + str1;
/*     */     }
/*  43 */     if (!str3.equals("")) {
/*  44 */       str8 = str8 + " and templetdocid =" + Util.fromScreen2(str3, this.user.getLanguage());
/*     */     }
/*  46 */     if (str5 != null && !"".equals(str5)) {
/*  47 */       if (str5.equals(".htm")) {
/*  48 */         str5 = "0";
/*  49 */       } else if (str5.equals(".doc")) {
/*  50 */         str5 = "2";
/*  51 */       } else if (str5.equals(".xls")) {
/*  52 */         str5 = "3";
/*  53 */       } else if (str5.equals(".wps")) {
/*  54 */         str5 = "4";
/*     */       } 
/*  56 */       str8 = str8 + " and templetdocid in (select id from DocMouldFile where mouldtype=" + str5 + ")";
/*     */     } 
/*  58 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  59 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  60 */     boolean bool = manageDetachComInfo.isUseHrmManageDetach();
/*  61 */     String str9 = manageDetachComInfo.getHrmdftsubcomid();
/*  62 */     if (bool) {
/*  63 */       if (str4.equals("") || str4.equals("-1")) {
/*  64 */         int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "HrmContractTypeAdd:Add");
/*  65 */         String str = "";
/*  66 */         int i = arrayOfInt.length / 1000 + 1;
/*  67 */         for (byte b = 1; b <= i; b++) {
/*  68 */           str = "";
/*  69 */           for (int j = (b - 1) * 1000; arrayOfInt != null && j < b * 1000 && j < arrayOfInt.length; j++) {
/*  70 */             if (str.length() > 0) {
/*  71 */               str = str + ",";
/*     */             }
/*  73 */             str = str + arrayOfInt[j];
/*     */           } 
/*  75 */           if (str.equals("")) {
/*  76 */             str = manageDetachComInfo.getHrmdftsubcomid();
/*     */           }
/*  78 */           if (str.length() > 0) {
/*  79 */             if (b == 1) {
/*  80 */               str8 = str8 + " and (subcompanyid in (" + str + ") ";
/*     */             } else {
/*  82 */               str8 = str8 + "  or subcompanyid in (" + str + ") ";
/*     */             } 
/*  84 */             if (b == i) {
/*  85 */               str8 = str8 + ")";
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } else {
/*  90 */         str8 = str8 + " and subcompanyid = " + str4;
/*     */       } 
/*     */     }
/*  93 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  94 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  95 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", null));
/*  96 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "templetname", null)).setIsInputCol(BoolAttr.TRUE));
/*  97 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(58, this.user.getLanguage()), "templetDocId", null));
/*     */     
/*  99 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, Util.toHtmlForSplitPage(str8), "id", "id", arrayList);
/* 100 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 101 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 105 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 106 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 107 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 108 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 84, "templetId", true));
/* 109 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "templetName", false));
/* 110 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 58, "templetDocId", false));
/* 111 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 112 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmConTempletBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */