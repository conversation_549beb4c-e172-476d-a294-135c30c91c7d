/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaInvoiceInterfaceService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  30 */     if (this.user == null) {
/*  31 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  32 */       return (Map)hashMap;
/*     */     } 
/*  34 */     String str1 = Util.null2String(paramMap.get("id"));
/*  35 */     String str2 = Util.null2String(paramMap.get("interfaceType"));
/*  36 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  44 */     String str3 = " id,case  when (interfaceType=1) then '" + SystemEnv.getHtmlLabelName(384751, this.user.getLanguage()) + "'  when (interfaceType=0 and type=3)  then '" + SystemEnv.getHtmlLabelName(519956, this.user.getLanguage()) + "' else '' end as interfacetype, interfaceurl,case status when 0 then '" + SystemEnv.getHtmlLabelName(309, this.user.getLanguage()) + "' else '" + SystemEnv.getHtmlLabelName(18431, this.user.getLanguage()) + "' end as interfacestatus,lastmodify ";
/*  45 */     String str4 = " from fnainvoiceinterface a ";
/*  46 */     String str5 = " where 1=1 ";
/*  47 */     if (!"".equals(str1)) {
/*  48 */       str5 = str5 + " and id in (" + str1 + ") ";
/*     */     }
/*  50 */     RecordSet recordSet = new RecordSet();
/*  51 */     recordSet.executeQuery("select " + str3 + str4 + str5 + " order by a.id", new Object[0]);
/*  52 */     while (recordSet.next()) {
/*  53 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  54 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/*  55 */       hashMap1.put("name", Util.null2String(recordSet.getString("interfaceType")) + "(ID:" + Util.null2String(recordSet.getString("id")) + ")");
/*  56 */       if ("0".equals(str2) ? 
/*  57 */         Util.null2String(recordSet.getString("interfaceType")).equals(SystemEnv.getHtmlLabelName(384751, this.user.getLanguage())) : (
/*     */ 
/*     */         
/*  60 */         "1".equals(str2) && 
/*  61 */         Util.null2String(recordSet.getString("interfaceType")).equals(SystemEnv.getHtmlLabelName(519956, this.user.getLanguage())))) {
/*     */         continue;
/*     */       }
/*     */       
/*  65 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/*  68 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  69 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  70 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(383093, this.user.getLanguage()), 1));
/*     */     
/*  72 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  73 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  74 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/*  76 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  87 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  89 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*     */     
/*  91 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  93 */     StringBuffer stringBuffer = new StringBuffer();
/*  94 */     stringBuffer.append(" select a.id, ");
/*  95 */     stringBuffer.append(" case when (interfaceType=1) then '" + SystemEnv.getHtmlLabelName(384751, this.user.getLanguage()) + "'  when (interfaceType=0 and type=3)  then '" + 
/*  96 */         SystemEnv.getHtmlLabelName(10000218, this.user.getLanguage()).substring(2, 4) + SystemEnv.getHtmlLabelName(32363, this.user.getLanguage()) + "'  else '' end as interfacetype, interfaceurl,case status when 0 then '" + 
/*     */         
/*  98 */         SystemEnv.getHtmlLabelName(309, this.user.getLanguage()) + "' else '" + 
/*  99 */         SystemEnv.getHtmlLabelName(18431, this.user.getLanguage()) + "' end as interfacestatus ");
/* 100 */     stringBuffer.append(" from fnainvoiceinterface a ");
/* 101 */     stringBuffer.append(" where id in (").append(str).append(")");
/* 102 */     stringBuffer.append(" order by id ");
/*     */     
/* 104 */     RecordSet recordSet = new RecordSet();
/* 105 */     recordSet.execute(stringBuffer.toString());
/* 106 */     while (recordSet.next()) {
/* 107 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 108 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 109 */       hashMap1.put("name", Util.null2String(recordSet.getString("interfacetype")) + "(ID:" + Util.null2String(recordSet.getString("id")) + ")");
/* 110 */       arrayList.add(hashMap1);
/*     */     } 
/* 112 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 113 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 114 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(383093, this.user.getLanguage()), 1));
/*     */     
/* 116 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 117 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 118 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 120 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 130 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 131 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 132 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 134 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/* 136 */     arrayList1.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/*     */     
/* 138 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(515601, this.user.getLanguage()), false));
/*     */     
/* 140 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(384751, this.user.getLanguage()), false));
/* 141 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 383093, "interfaceType", arrayList1);
/* 142 */     searchConditionItem.setViewAttr(2);
/* 143 */     arrayList.add(searchConditionItem);
/*     */     
/* 145 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 146 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaInvoiceInterfaceService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */