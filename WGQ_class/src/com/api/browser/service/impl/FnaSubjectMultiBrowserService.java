/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaSubjectMultiBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     if (this.user == null) {
/*  41 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  42 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  45 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  46 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  48 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name").setIsQuickSearch(true));
/*  49 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "codeName"));
/*  50 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  52 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  66 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  68 */     if (this.user == null) {
/*  69 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  70 */       return (Map)hashMap;
/*     */     } 
/*  72 */     String str1 = "";
/*  73 */     RecordSet recordSet = new RecordSet();
/*  74 */     String str2 = Util.null2String(paramMap.get("accountId"));
/*  75 */     if ("".equals(str2)) {
/*  76 */       recordSet.executeQuery(" select accountId from FnaAccountUser where userId = ? ", new Object[] { Integer.valueOf(this.user.getUID()) });
/*  77 */       if (recordSet.next()) {
/*  78 */         str2 = Util.null2String(recordSet.getString("accountId"));
/*     */       }
/*     */     } 
/*  81 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str2, Integer.valueOf(1) });
/*  82 */     if (recordSet.next()) {
/*  83 */       str1 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/*  85 */     paramMap.put("tableName", str1);
/*     */     
/*  87 */     int i = Util.getIntValue(Util.null2String(paramMap.get("qryType")), 0);
/*     */     
/*  89 */     if (i == 1) {
/*  90 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  91 */       SplitTableBean splitTableBean = getTableList(paramMap);
/*  92 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } else {
/*  94 */       String str = Util.null2String((String)paramMap.get("id"), "0");
/*  95 */       if ("0".equals(str)) {
/*  96 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  97 */         arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  98 */         arrayList.add(new ListHeadBean("subjectname", "", 1, BoolAttr.TRUE));
/*  99 */         arrayList.add(new ListHeadBean("subjectcode", ""));
/* 100 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */         
/* 102 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 103 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 104 */         if (list.size() == 0) {
/* 105 */           BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 106 */           browserTreeNode.setId("-1");
/* 107 */           browserTreeNode.setName(SystemEnv.getHtmlLabelName(1462, this.user.getLanguage()));
/* 108 */           browserTreeNode.setPid("0");
/* 109 */           browserTreeNode.setParent(true);
/* 110 */           browserTreeNode.setType("0");
/* 111 */           browserTreeNode.setCanClick(false);
/* 112 */           browserTreeNode.setIcon("icon-coms-LargeArea");
/*     */           
/* 114 */           list.add(browserTreeNode);
/*     */         } 
/*     */         
/* 117 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } else {
/* 119 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 120 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 121 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 126 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 138 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 140 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 141 */     String str2 = Util.null2String(paramMap.get("alllevel"));
/* 142 */     String str3 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 143 */     String[] arrayOfString = str3.split(",");
/*     */     
/* 145 */     RecordSet recordSet = new RecordSet();
/* 146 */     String str4 = "";
/* 147 */     if ("".equals(str1)) {
/* 148 */       recordSet.executeQuery(" select accountId from FnaAccountUser where userId = ? ", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 149 */       if (recordSet.next()) {
/* 150 */         str1 = Util.null2String(recordSet.getString("accountId"));
/*     */       }
/*     */     } 
/* 153 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(1) });
/* 154 */     if (recordSet.next()) {
/* 155 */       str4 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/*     */     
/* 158 */     if (!"".equals(str4)) {
/* 159 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */       
/* 161 */       StringBuffer stringBuffer = new StringBuffer();
/* 162 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 163 */         if (stringBuffer.length() > 0) {
/* 164 */           stringBuffer.append(",");
/*     */         }
/* 166 */         stringBuffer.append("'").append(arrayOfString[b]).append("'");
/*     */       } 
/*     */       
/* 169 */       if ("1".equals(str2)) {
/* 170 */         ArrayList<String> arrayList2 = new ArrayList();
/*     */         
/* 172 */         String str = " select a.subjectCodenew from " + str4 + " a where (isArchive <> 1  or isArchive is null)  and id in ( " + stringBuffer.toString() + ")";
/* 173 */         recordSet.executeQuery(str, new Object[0]);
/* 174 */         while (recordSet.next()) {
/* 175 */           String str5 = Util.null2String(recordSet.getString("subjectCodenew"));
/* 176 */           arrayList2.add(str5);
/*     */         } 
/*     */         
/* 179 */         StringBuffer stringBuffer1 = new StringBuffer();
/* 180 */         stringBuffer1.append(" select a.id,a.subjectname,a.subjectcode from " + str4 + " a ");
/* 181 */         stringBuffer1.append(" where (1=2 ");
/* 182 */         for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 183 */           stringBuffer1.append(" or a.subjectCodenew like '" + (String)arrayList2.get(b1) + "%' ");
/*     */         }
/* 185 */         stringBuffer1.append(" ) ");
/* 186 */         stringBuffer1.append(" and (isArchive <> 1  or isArchive is null) order by subjectlevel,subjectcodenew ");
/*     */         
/* 188 */         str3 = "";
/* 189 */         recordSet.executeQuery(stringBuffer1.toString(), new Object[0]);
/* 190 */         while (recordSet.next()) {
/* 191 */           String str5 = Util.null2String(recordSet.getString("id"));
/* 192 */           String str6 = Util.null2String(recordSet.getString("subjectname"));
/* 193 */           String str7 = Util.null2String(recordSet.getString("subjectcode"));
/*     */           
/* 195 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 196 */           hashMap1.put("id", str5);
/* 197 */           hashMap1.put("subjectname", str6);
/* 198 */           hashMap1.put("subjectcode", str7);
/* 199 */           arrayList.add(hashMap1);
/*     */           
/* 201 */           str3 = str3 + str5 + ",";
/*     */         } 
/*     */         
/* 204 */         if (!"".equals(str3)) {
/* 205 */           str3 = str3.substring(0, str3.length() - 1);
/*     */         }
/*     */       } else {
/* 208 */         StringBuffer stringBuffer1 = new StringBuffer();
/* 209 */         stringBuffer1.append(" select a.id,a.subjectname,a.subjectcode from " + str4 + " a ");
/* 210 */         stringBuffer1.append(" where (isArchive <> 1  or isArchive is null)  ");
/* 211 */         stringBuffer1.append(" and id in (").append(stringBuffer.toString()).append(")");
/*     */         
/* 213 */         recordSet.executeQuery(stringBuffer1.toString(), new Object[0]);
/* 214 */         while (recordSet.next()) {
/* 215 */           String str5 = Util.null2String(recordSet.getString("id"));
/* 216 */           String str6 = Util.null2String(recordSet.getString("subjectname"));
/* 217 */           String str7 = Util.null2String(recordSet.getString("subjectcode"));
/*     */           
/* 219 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 220 */           hashMap1.put("id", str5);
/* 221 */           hashMap1.put("subjectname", str6);
/* 222 */           hashMap1.put("subjectcode", str7);
/* 223 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 228 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 229 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 230 */       arrayList1.add(new ListHeadBean("subjectname", "", 1, BoolAttr.TRUE));
/* 231 */       arrayList1.add(new ListHeadBean("subjectcode", ""));
/*     */       
/* 233 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 234 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str3, "id"));
/* 235 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/*     */ 
/*     */     
/* 239 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 250 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 251 */     String str2 = "";
/*     */     
/* 253 */     RecordSet recordSet = new RecordSet();
/* 254 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(1) });
/* 255 */     if (recordSet.next()) {
/* 256 */       str2 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/* 258 */     String str3 = Util.null2String(paramMap.get("name"));
/*     */     
/* 260 */     String str4 = Util.null2String(paramMap.get("codeName"));
/*     */     
/* 262 */     String str5 = "10";
/*     */     
/* 264 */     String str6 = " a.id,a.subjectname,a.subjectcode,a.subjectlevel ";
/* 265 */     String str7 = " " + str2 + "  a";
/* 266 */     String str8 = " where (isarchive <> 1  or isarchive is null) ";
/*     */     
/* 268 */     if (!"".equals(str3)) {
/* 269 */       str8 = str8 + " and a.subjectname like '%" + StringEscapeUtils.escapeSql(str3) + "%' ";
/*     */     }
/* 271 */     if (!"".equals(str4)) {
/* 272 */       str8 = str8 + " and a.subjectcode like '%" + StringEscapeUtils.escapeSql(str4) + "%' ";
/*     */     }
/*     */     
/* 275 */     String str9 = "a.subjectlevel,a.subjectcode";
/* 276 */     String str10 = "a.id";
/*     */     
/* 278 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 279 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 280 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "subjectname", "subjectname")).setIsInputCol(BoolAttr.TRUE));
/* 281 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage()), "subjectcode", "subjectcode", "weaver.fna.general.FnaCommon.escapeHtml"));
/*     */ 
/*     */     
/* 284 */     SplitTableBean splitTableBean = new SplitTableBean("subjectMultiBrowserList", "none", str5, "subjectMultiBrowserList", str6, str7, str8, str9, str10, "ASC", arrayList);
/*     */     
/* 286 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 288 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) {
/* 298 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 299 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 300 */     String str2 = "";
/*     */     
/* 302 */     RecordSet recordSet1 = new RecordSet();
/* 303 */     recordSet1.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(1) });
/* 304 */     if (recordSet1.next()) {
/* 305 */       str2 = Util.null2String(recordSet1.getString("tableName"));
/*     */     }
/* 307 */     RecordSet recordSet2 = new RecordSet();
/* 308 */     RecordSet recordSet3 = new RecordSet();
/* 309 */     String str3 = "";
/* 310 */     String str4 = Util.null2String(paramMap.get("id"));
/* 311 */     recordSet1.executeQuery("select * from " + str2 + " where id=?", new Object[] { str4 });
/* 312 */     if (recordSet1.next()) {
/* 313 */       str3 = Util.null2String(recordSet1.getString("subjectcode"));
/*     */     }
/* 315 */     String str5 = "";
/* 316 */     if ("".equals(str3)) {
/* 317 */       str5 = " select id,subjectname,subjectcode from " + str2 + " where (isarchive <> 1  or isarchive is null) and subjectcode like '" + str3 + "_%' and subjectLevel = 1";
/*     */     } else {
/* 319 */       recordSet2.executeQuery("select id,subjectLevel from " + str2 + " where (isarchive <> 1  or isarchive is null) and subjectcode = ?", new Object[] { str3 });
/* 320 */       int i = 0;
/* 321 */       if (recordSet2.next()) {
/* 322 */         i = Util.getIntValue(recordSet2.getString("subjectLevel"));
/*     */       }
/* 324 */       str5 = " select id,subjectname,subjectcode from " + str2 + " where (isarchive <> 1  or isarchive is null) and subjectcode like '" + str3 + "_%' and subjectLevel = " + (i + 1);
/*     */     } 
/*     */     
/* 327 */     String str6 = " order by subjectlevel,subjectcode,displayOrder,subjectname ";
/* 328 */     str5 = str5 + str6;
/* 329 */     recordSet2.execute(str5);
/* 330 */     String str7 = "/images/treeimages/home16_wev8.gif";
/* 331 */     while (recordSet2.next()) {
/* 332 */       String str8 = recordSet2.getString("id");
/* 333 */       String str9 = recordSet2.getString("subjectcode");
/* 334 */       String str10 = recordSet2.getString("subjectname");
/* 335 */       boolean bool = true;
/* 336 */       recordSet3.executeQuery(" select count(*) cnt from " + str2 + " a where (isarchive <> 1  or isarchive is null) and subjectcode like '" + str9 + "_%'", new Object[0]);
/* 337 */       if (recordSet3.next() && recordSet3.getInt("cnt") > 0) {
/* 338 */         bool = true;
/*     */       } else {
/* 340 */         bool = false;
/*     */       } 
/*     */       
/* 343 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 344 */       browserTreeNode.setId(str8);
/* 345 */       browserTreeNode.setName(str10);
/* 346 */       browserTreeNode.setIsParent(bool);
/* 347 */       browserTreeNode.setIcon(str7);
/* 348 */       browserTreeNode.setCanClick(true);
/* 349 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 351 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaSubjectMultiBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */