/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.edc.util.EDCUtil;
/*     */ import com.wbi.core.util.ReportUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.service.CommonConstant;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EDCDataSetBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  39 */     String str1 = EDCUtil.isEmpty(Util.null2String(paramMap.get("sourceType1")).trim().toLowerCase()) ? Util.null2String(paramMap.get("sourceType")).trim().toLowerCase() : Util.null2String(paramMap.get("sourceType1")).trim().toLowerCase();
/*  40 */     String str2 = Util.null2String(paramMap.get("datasetId")).trim();
/*  41 */     String str3 = Util.null2String(paramMap.get("dbname")).trim();
/*  42 */     String str4 = "where 1=1";
/*  43 */     String str5 = "";
/*  44 */     String str6 = "";
/*  45 */     String str7 = "";
/*     */     
/*  47 */     RecordSet recordSet = new RecordSet();
/*  48 */     String str8 = recordSet.getDBType();
/*  49 */     String str9 = "";
/*  50 */     String str10 = "not";
/*  51 */     if (EDCUtil.isNotEmpty(str2)) {
/*     */       
/*  53 */       String str = Util.null2String(paramMap.get("isCheck")).trim();
/*  54 */       str10 = "1".equals(str) ? "" : "not";
/*  55 */       if ("1".equals(str)) {
/*  56 */         str10 = "";
/*  57 */         str9 = " and settinguuid = '" + str2 + "'";
/*     */       } else {
/*  59 */         str9 = " and settinguuid <> '" + str2 + "'";
/*     */       } 
/*     */     } 
/*     */     
/*  63 */     if ("sqlserver".equalsIgnoreCase(str8)) {
/*  64 */       str4 = str4 + " and xtype in ('V','U')";
/*  65 */       str4 = str4 + " and lower(t1.name) " + str10 + " in (select LOWER(sourceid) from edc_appsresource where sourcetype = 'dataset'" + str9 + ")";
/*  66 */       if (EDCUtil.isNotEmpty(str1) && !"all".equals(str1)) {
/*  67 */         if (!"other".equals(str1)) {
/*  68 */           str4 = str4 + " and LOWER(t1.name) like '" + str1 + "%'";
/*     */         } else {
/*  70 */           String str = Util.null2String(paramMap.get("modetypes")).trim();
/*  71 */           if (EDCUtil.isNotEmpty(str)) {
/*  72 */             String[] arrayOfString = str.split(",");
/*  73 */             for (String str11 : arrayOfString) {
/*  74 */               str4 = str4 + " and LOWER(t1.name) not like '" + str11 + "%'";
/*     */             }
/*     */           } 
/*     */         } 
/*     */       }
/*  79 */       if (EDCUtil.isNotEmpty(str3)) {
/*  80 */         str4 = str4 + " and LOWER(t1.name) like '%" + str3.toLowerCase() + "%'";
/*     */       }
/*  82 */       str5 = " t1.name ,t1.name dbname";
/*  83 */       str6 = " sysobjects t1 ";
/*  84 */       str7 = " t1.name ";
/*  85 */     } else if ("mysql".equalsIgnoreCase(str8)) {
/*  86 */       String str = CommonConstant.DB_MYSQL_SCHEMA;
/*  87 */       str4 = str4 + " and upper(table_schema) =upper('" + str + "') and upper(table_type) in( 'VIEW','BASE TABLE')";
/*  88 */       str4 = str4 + " and lower(t1.table_name) " + str10 + " in (select LOWER(sourceid) from edc_appsresource where sourcetype = 'dataset'" + str9 + ")";
/*  89 */       if (EDCUtil.isNotEmpty(str1) && !"all".equals(str1)) {
/*  90 */         if (!"other".equals(str1)) {
/*  91 */           str4 = str4 + " and LOWER(t1.table_name) like '" + str1 + "%'";
/*     */         } else {
/*  93 */           String str11 = Util.null2String(paramMap.get("modetypes")).trim();
/*  94 */           if (EDCUtil.isNotEmpty(str11)) {
/*  95 */             String[] arrayOfString = str11.split(",");
/*  96 */             for (String str12 : arrayOfString) {
/*  97 */               str4 = str4 + " and LOWER(t1.table_name) not like '" + str12 + "%'";
/*     */             }
/*     */           } 
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 104 */       if (EDCUtil.isNotEmpty(str3)) {
/* 105 */         str4 = str4 + " and LOWER(t1.table_name) like '%" + str3.toLowerCase() + "%'";
/*     */       }
/* 107 */       str5 = " t1.table_name name, t1.table_name dbname";
/* 108 */       str6 = " information_schema.tables t1 ";
/* 109 */       str7 = " t1.table_name ";
/*     */     }
/* 111 */     else if ("postgresql".equalsIgnoreCase(str8)) {
/* 112 */       str4 = str4 + " and table_schema =lower('public') and table_type in( 'VIEW','BASE TABLE')";
/* 113 */       str4 = str4 + " and lower(t1.table_name) " + str10 + " in (select LOWER(sourceid) from edc_appsresource where sourcetype = 'dataset'" + str9 + ")";
/* 114 */       if (EDCUtil.isNotEmpty(str1) && !"all".equals(str1)) {
/* 115 */         if (!"other".equals(str1)) {
/* 116 */           str4 = str4 + " and LOWER(t1.table_name) like '" + str1 + "%'";
/*     */         } else {
/* 118 */           String str = Util.null2String(paramMap.get("modetypes")).trim();
/* 119 */           if (EDCUtil.isNotEmpty(str)) {
/* 120 */             String[] arrayOfString = str.split(",");
/* 121 */             for (String str11 : arrayOfString) {
/* 122 */               str4 = str4 + " and LOWER(t1.table_name) not like '" + str11 + "%'";
/*     */             }
/*     */           } 
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 129 */       if (EDCUtil.isNotEmpty(str3)) {
/* 130 */         str4 = str4 + " and LOWER(t1.table_name) like '%" + str3.toLowerCase() + "%'";
/*     */       }
/* 132 */       str5 = " t1.table_name name, t1.table_name dbname";
/* 133 */       str6 = " information_schema.tables t1 ";
/* 134 */       str7 = " t1.table_name ";
/*     */     } else {
/*     */       
/* 137 */       String str = " select view_name dbname,  view_name name from user_views  union all select table_name name, table_name dbname  from user_tables ";
/*     */       
/* 139 */       str4 = str4 + " and lower(t1.name) " + str10 + " in (select LOWER(sourceid) from edc_appsresource where sourcetype = 'dataset' " + str9 + ")";
/* 140 */       if (EDCUtil.isNotEmpty(str1) && !"all".equals(str1)) {
/* 141 */         if (!"other".equals(str1)) {
/* 142 */           str4 = str4 + " and LOWER(t1.name) like '" + str1 + "%'";
/*     */         } else {
/* 144 */           String str11 = Util.null2String(paramMap.get("modetypes")).trim();
/* 145 */           if (EDCUtil.isNotEmpty(str11)) {
/* 146 */             String[] arrayOfString = str11.split(",");
/* 147 */             for (String str12 : arrayOfString) {
/* 148 */               str4 = str4 + " and LOWER(t1.name) not like '" + str12 + "%'";
/*     */             }
/*     */           } 
/*     */         } 
/*     */       }
/* 153 */       if (EDCUtil.isNotEmpty(str3)) {
/* 154 */         str4 = str4 + " and LOWER(t1.name) like '%" + str3.toLowerCase() + "%'";
/*     */       }
/* 156 */       str5 = " t1.name name, t1.dbname dbname";
/* 157 */       str6 = " (" + str + ") t1 ";
/* 158 */       str7 = " t1.name ";
/*     */     } 
/*     */ 
/*     */     
/* 162 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 167 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(514583, this.user.getLanguage()), "dbname", "dbname", "com.api.browser.service.impl.EDCDataSetBrowserService.getDBName", "", 1));
/* 168 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(514582, this.user.getLanguage()), "name", "name", 1));
/*     */     
/* 170 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, str7, "t1.name", arrayList);
/* 171 */     splitTableBean.setSqlsortway("ASC");
/* 172 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 173 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDBName(String paramString1, String paramString2) {
/* 178 */     return ReportUtil.getTableName(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 184 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 185 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 186 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 187 */     String str = Util.null2String(paramMap.get("datasetId")).trim();
/*     */ 
/*     */     
/* 190 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 514582, "dbname", true);
/*     */     
/* 192 */     arrayList.add(searchConditionItem1);
/*     */     
/* 194 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, 523886, "sourceType1", false);
/* 195 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/* 197 */     arrayList1.add(new SearchConditionOption("all", SystemEnv.getHtmlLabelName(527113, this.user.getLanguage())));
/* 198 */     arrayList1.add(new SearchConditionOption("uf", SystemEnv.getHtmlLabelName(527114, this.user.getLanguage())));
/* 199 */     arrayList1.add(new SearchConditionOption("formtable", SystemEnv.getHtmlLabelName(527115, this.user.getLanguage())));
/* 200 */     arrayList1.add(new SearchConditionOption("edc", SystemEnv.getHtmlLabelName(527116, this.user.getLanguage())));
/* 201 */     arrayList1.add(new SearchConditionOption("other", SystemEnv.getHtmlLabelName(527117, this.user.getLanguage())));
/* 202 */     searchConditionItem2.setOptions(arrayList1);
/* 203 */     arrayList.add(searchConditionItem2);
/* 204 */     if (EDCUtil.isNotEmpty(str)) {
/* 205 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.CHECKBOX, 385596, "isCheck", false);
/*     */       
/* 207 */       arrayList.add(searchConditionItem);
/*     */     } 
/* 209 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 210 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 216 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 217 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 218 */     String str2 = "";
/* 219 */     String str3 = "";
/* 220 */     RecordSet recordSet = new RecordSet();
/* 221 */     String str4 = recordSet.getDBType();
/* 222 */     if ("sqlserver".equalsIgnoreCase(str4)) {
/*     */       
/* 224 */       str3 = "SELECT t1.name ,t1.name dbname FROM sysobjects t1   where LOWER(t1.name) like '%" + str1.toLowerCase() + "%' " + str2 + "order by t1.name";
/* 225 */     } else if ("mysql".equalsIgnoreCase(str4)) {
/* 226 */       String str = CommonConstant.DB_MYSQL_SCHEMA;
/*     */ 
/*     */       
/* 229 */       str3 = "select t1.table_name name, t1.table_name dbname from information_schema.tables t1 where  UPPER(t1.table_schema) = UPPER('" + str + "') and upper(t1.table_type) in( 'VIEW','BASE TABLE') LOWER(t1.table_name) like '%" + str1.toLowerCase() + "%'  order by t1.table_name";
/*     */     
/*     */     }
/* 232 */     else if ("postgresql".equalsIgnoreCase(str4)) {
/*     */ 
/*     */       
/* 235 */       str3 = "select t1.table_name as name, t1.table_name dbname from information_schema.tables t1 where  t1.table_schema = lower('public') and t1.table_type in( 'VIEW','BASE TABLE') t1.table_name like '%" + str1.toLowerCase() + "%'  order by t1.table_name";
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */       
/* 241 */       str3 = "select t1.dbname, t1.name  from (select view_name dbname,  view_name name from user_views  union all select table_name name, table_name dbname  from user_tables) t1  where LOWER(t1.name) like '%" + str1.toLowerCase() + "%' order by t1.name";
/*     */     } 
/*     */     
/* 244 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 245 */     recordSet.executeQuery(str3, new Object[0]);
/* 246 */     while (recordSet.next()) {
/* 247 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 248 */       hashMap1.put("id", recordSet.getString("id"));
/* 249 */       hashMap1.put("name", recordSet.getString("dbname"));
/* 250 */       arrayList.add(hashMap1);
/*     */     } 
/* 252 */     hashMap.put("datas", arrayList);
/*     */     
/* 254 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 255 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EDCDataSetBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */