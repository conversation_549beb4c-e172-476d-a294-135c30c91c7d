/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AgentTemplateBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[\n    {\n      \"key\": \"col1\",\n      \"configs\": [\n        {\n          \"key\": \"col1_row1\",\n          \"configs\": [\n            { \"key\": \"name\" }\n          ]\n        },\n        {\n          \"key\": \"col1_row2\",\n          \"configs\": [\n            { \"key\": \"workflowrange\" }\n          ]\n        },\n        {\n          \"key\": \"col1_row3\",\n          \"configs\": [\n            { \"key\": \"agenterid\" }\n            \n          ]\n        }\n      ]\n    }\n  ]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  60 */     SplitTableBean splitTableBean = generateSplitTableBean(paramMap);
/*     */     
/*  62 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  63 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  64 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/*  65 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(34067, this.user.getLanguage()), "workflowrange", "", "weaver.workflow.agent.AgentManager.convertWorkflowRange", "column:rangeselect+" + this.user.getLanguage()));
/*  66 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17566, this.user.getLanguage()), "agenterid", "agenterid", "weaver.hrm.resource.ResourceComInfo.getLastname"));
/*  67 */     splitTableBean.setCols(arrayList);
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  71 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public SplitTableBean generateSplitTableBean(Map<String, Object> paramMap) {
/*  75 */     RecordSet recordSet = new RecordSet();
/*  76 */     String str1 = " where creater=" + this.user.getUID();
/*  77 */     String str2 = Util.null2String(paramMap.get("name"));
/*  78 */     String str3 = Util.null2String(paramMap.get("workflowid"));
/*  79 */     String str4 = Util.null2String(paramMap.get("agenterid"));
/*  80 */     if (!"".equals(str2))
/*  81 */       str1 = str1 + " and name like '%" + str2 + "%' "; 
/*  82 */     if (!"".equals(str3))
/*  83 */       if ("oracle".equals(recordSet.getDBType())) {
/*  84 */         str1 = str1 + " and (rangeselect='2' or ','||workflowrange||',' like '%," + str3 + ",%') ";
/*  85 */       } else if ("mysql".equals(recordSet.getDBType())) {
/*  86 */         str1 = str1 + " and (rangeselect='2' or concat(',',workflowrange,',') like '%," + str3 + ",%') ";
/*  87 */       } else if ("postgresql".equals(recordSet.getDBType())) {
/*  88 */         str1 = str1 + " and (rangeselect='2' or ','||workflowrange||',' like '%," + str3 + ",%') ";
/*     */       } else {
/*     */         
/*  91 */         str1 = str1 + " and (rangeselect='2' or ','+workflowrange+',' like '%," + str3 + ",%') ";
/*     */       }  
/*  93 */     if (!"".equals(str4)) {
/*  94 */       str1 = str1 + " and agenterid=" + str4;
/*     */     }
/*  96 */     SplitTableBean splitTableBean = new SplitTableBean();
/*  97 */     splitTableBean.setSqlprimarykey("id");
/*  98 */     splitTableBean.setBackfields("id,name,rangeselect,workflowrange,agenterid");
/*  99 */     splitTableBean.setSqlform("from workflow_agenttemplate");
/* 100 */     splitTableBean.setSqlwhere(str1);
/* 101 */     splitTableBean.setSqlorderby("id");
/* 102 */     splitTableBean.setSqlsortway("desc");
/* 103 */     if ("1".equals(Util.null2String(paramMap.get("ismobile")))) {
/*     */       
/* 105 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */       try {
/* 107 */         splitTableBean.createMobileTemplate(JSON.parseArray("[\n    {\n      \"key\": \"col1\",\n      \"configs\": [\n        {\n          \"key\": \"col1_row1\",\n          \"configs\": [\n            { \"key\": \"name\" }\n          ]\n        },\n        {\n          \"key\": \"col1_row2\",\n          \"configs\": [\n            { \"key\": \"workflowrange\" }\n          ]\n        },\n        {\n          \"key\": \"col1_row3\",\n          \"configs\": [\n            { \"key\": \"agenterid\" }\n            \n          ]\n        }\n      ]\n    }\n  ]", SplitMobileDataBean.class));
/* 108 */       } catch (Exception exception) {
/* 109 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 114 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 119 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 120 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 121 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 122 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18151, "name", true));
/* 123 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 34067, "workflowid", "-99991"));
/* 124 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 17566, "agenterid", "1"));
/*     */ 
/*     */     
/* 127 */     if ("1".equals(Util.null2String(paramMap.get("ismobile")))) {
/*     */       
/* 129 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 130 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 131 */       hashMap1.put("title", SystemEnv.getHtmlLabelName(32905, this.user.getLanguage()));
/* 132 */       hashMap1.put("defaultshow", Boolean.valueOf(true));
/* 133 */       hashMap1.put("items", arrayList);
/* 134 */       arrayList1.add(hashMap1);
/* 135 */       hashMap.put("conditioninfo", arrayList1);
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 140 */       hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 141 */     }  return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 146 */     RecordSet recordSet = new RecordSet();
/* 147 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 148 */     recordSet.executeQuery("select id,name from workflow_agenttemplate where beagentid=?", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 149 */     while (recordSet.next()) {
/* 150 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 151 */       hashMap1.put("id", recordSet.getString("id"));
/* 152 */       hashMap1.put("name", recordSet.getString("name"));
/* 153 */       arrayList.add(hashMap1);
/*     */     } 
/* 155 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 156 */     hashMap.put("datas", arrayList);
/* 157 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AgentTemplateBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */