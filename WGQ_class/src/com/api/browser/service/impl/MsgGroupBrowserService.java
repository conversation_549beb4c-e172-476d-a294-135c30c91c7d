/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MsgGroupBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     String str1 = Util.null2String(paramMap.get("groupname"));
/*    */     
/* 32 */     String str2 = "t1.id,t1.groupname,t2.labelname";
/* 33 */     String str3 = "ecology_message_group t1 left join htmllabelinfo t2 on t1.groupname=t2.indexid";
/* 34 */     String str4 = "1=1 and t2.languageid=" + this.user.getLanguage();
/* 35 */     if (StringUtils.isNotBlank(str1)) {
/* 36 */       str4 = str4 + " and t2.labelname like '%" + str1 + "%'";
/*    */     }
/* 38 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 39 */     arrayList.add(new SplitTableColBean("hide", "id"));
/* 40 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(28485, this.user.getLanguage()), "labelname", "labelname")).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 42 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, "id", "id", "asc", arrayList);
/* 43 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 44 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 49 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 50 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 51 */     RecordSet recordSet = new RecordSet();
/* 52 */     String str2 = "select t1.id,t2.labelname from ecology_message_group t1 left join HtmlLabelInfo t2 on t1.groupname=t2.indexid where t2.labelname like ? and t2.languageid=?";
/* 53 */     recordSet.executeQuery(str2, new Object[] { "%" + str1 + "%", Integer.valueOf(this.user.getLanguage()) });
/* 54 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 55 */     while (recordSet.next()) {
/* 56 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 57 */       hashMap1.put("id", recordSet.getString("id"));
/* 58 */       hashMap1.put("name", recordSet.getString("labelname"));
/* 59 */       arrayList.add(hashMap1);
/*    */     } 
/* 61 */     hashMap.put("datas", arrayList);
/* 62 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 67 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 69 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 70 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 71 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 28485, "groupname", true));
/*    */     
/* 73 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 74 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MsgGroupBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */