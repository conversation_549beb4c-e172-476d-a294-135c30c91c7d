/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MPortalPageBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 36 */     String str1 = Util.null2String(paramMap.get("infoname"));
/*    */     
/* 38 */     String str2 = " id,infoname,subcompanyid,hpcreatorid ";
/* 39 */     String str3 = " hp_mobile_hpinfo ";
/* 40 */     String str4 = " where 1=1 ";
/* 41 */     if (!"".equals(str1)) {
/* 42 */       str4 = str4 + " and infoname like '%" + str1 + "%' ";
/*    */     }
/* 44 */     String str5 = " id ";
/*    */     
/* 46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 47 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 48 */     arrayList.add(new SplitTableColBean("10%", "ID", "id", "id"));
/* 49 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "infoname", "infoname")).setIsInputCol(BoolAttr.TRUE));
/* 50 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname"));
/* 51 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(882, this.user.getLanguage()), "hpcreatorid", "hpcreatorid", "weaver.splitepage.transform.SptmForHomepage.getPortalCreator"));
/*    */     
/* 53 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, "id", "asc", arrayList);
/* 54 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 55 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 61 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 62 */     String str1 = paramHttpServletRequest.getParameter("q");
/*    */     
/* 64 */     RecordSet recordSet = new RecordSet();
/* 65 */     String str2 = "select id,infoname from hp_mobile_hpinfo where infoname like ?";
/* 66 */     recordSet.executeQuery(str2, new Object[] { "%" + str1 + "%" });
/* 67 */     while (recordSet.next()) {
/* 68 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 69 */       hashMap1.put("id", recordSet.getString("id"));
/* 70 */       hashMap1.put("name", recordSet.getString("infoname"));
/* 71 */       arrayList.add(hashMap1);
/*    */     } 
/*    */     
/* 74 */     hashMap.put("datas", arrayList);
/* 75 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 80 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 82 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 83 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 84 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "infoname", true));
/*    */     
/* 86 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 87 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MPortalPageBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */