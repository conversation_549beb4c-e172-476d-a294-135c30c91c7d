/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class GovernCategoryBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 22 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 23 */     String str1 = Util.null2String(paramMap.get("name"));
/* 24 */     int i = this.user.getLanguage();
/* 25 */     String str2 = Util.null2String(paramMap.get("superiorName"));
/* 26 */     String str3 = " t1.type = 1";
/* 27 */     if (!str1.equals("")) {
/* 28 */       str3 = str3 + " and t1.name like '%";
/* 29 */       str3 = str3 + Util.fromScreen2(str1, i);
/* 30 */       str3 = str3 + "%'";
/*    */     } 
/* 32 */     if (!str2.equals("")) {
/* 33 */       str3 = str3 + " and t2.name like '%";
/* 34 */       str3 = str3 + Util.fromScreen2(str2, i);
/* 35 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 38 */     String str4 = " t1.id ";
/* 39 */     String str5 = " t1.id,t1.name,t2.name superiorName ";
/* 40 */     String str6 = " govern_category  t1 LEFT JOIN govern_category t2 on t1.superior=t2.id  ";
/* 41 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 42 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 43 */     SplitTableColBean splitTableColBean1 = (new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(388808, this.user.getLanguage()), "name", "t1.name", "com.engine.govern.biz.CategoryTransMethod.getCategoryNameStr", Util.null2String(Integer.valueOf(i)))).setIsInputCol(BoolAttr.TRUE);
/* 44 */     arrayList.add(splitTableColBean1);
/*    */     
/* 46 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(389700, this.user.getLanguage()), "superiorName", "t2.name", "com.engine.govern.biz.CategoryTransMethod.getCategoryNameStr", Util.null2String(Integer.valueOf(i)));
/* 47 */     arrayList.add(splitTableColBean2);
/*    */     
/* 49 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str3, str4, "id", arrayList);
/* 50 */     splitTableBean.setSqlsortway("ASC");
/* 51 */     splitTableBean.setSqlisdistinct("true");
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 66 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 67 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 68 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 69 */     ConditionType conditionType = ConditionType.INPUT;
/* 70 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(conditionType, 0, "name", true);
/* 71 */     searchConditionItem1.setLabel(SystemEnv.getHtmlLabelName(388808, Util.getIntValue(this.user.getLanguage())));
/* 72 */     arrayList.add(searchConditionItem1);
/* 73 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(conditionType, 0, "superiorName", false);
/* 74 */     searchConditionItem2.setLabel(SystemEnv.getHtmlLabelName(389700, Util.getIntValue(this.user.getLanguage())));
/* 75 */     arrayList.add(searchConditionItem2);
/* 76 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 77 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/GovernCategoryBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */