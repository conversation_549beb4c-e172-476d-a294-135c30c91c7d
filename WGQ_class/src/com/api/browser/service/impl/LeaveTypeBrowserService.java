/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.engine.kq.biz.KQAttFlowSetComInfo;
/*     */ import com.engine.kq.biz.KQSettingsBiz;
/*     */ import com.engine.kq.enums.KQSettingsEnum;
/*     */ import com.engine.kq.util.KQTransMethod;
/*     */ import com.engine.kq.wfset.util.KQ122Util;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LeaveTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  33 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*  34 */     RecordSet recordSet = new RecordSet();
/*  35 */     KQ122Util kQ122Util = new KQ122Util();
/*  36 */     boolean bool = kQ122Util.is122Open();
/*  37 */     if (bool) {
/*  38 */       String str4 = this.user.getUID() + "";
/*  39 */       int i = Util.getIntValue(Util.null2String(paramMap.get("beagenter")), 0);
/*  40 */       if (i > 0)
/*     */       {
/*  42 */         str4 = i + "";
/*     */       }
/*     */       
/*  45 */       String str5 = Util.null2String(paramMap.get("wfid"));
/*  46 */       KQAttFlowSetComInfo kQAttFlowSetComInfo = new KQAttFlowSetComInfo();
/*  47 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  48 */       Map map = kQAttFlowSetComInfo.getDependCfgByWfId(str5);
/*     */       
/*  50 */       boolean bool1 = StringUtils.isNotBlank(str5);
/*     */       
/*  52 */       if (map != null && bool1) {
/*  53 */         String str = "";
/*  54 */         for (Map.Entry entry : map.entrySet()) {
/*  55 */           List<String> list = (List)entry.getValue();
/*  56 */           if (list != null && !list.isEmpty())
/*     */           {
/*  58 */             str = Util.null2String(list.get(0));
/*     */           }
/*     */         } 
/*  61 */         if (str.length() > 0) {
/*  62 */           str = "field" + str.substring(0, str.indexOf("_"));
/*  63 */           str4 = Util.null2String(paramMap.get(str));
/*     */         } 
/*     */       } 
/*     */       
/*  67 */       String str6 = Util.null2String(paramMap.get("name"));
/*  68 */       String str7 = "";
/*     */       try {
/*  70 */         if (str4 == null || "".equals(str4)) {
/*  71 */           str4 = this.user.getUID() + "";
/*     */         }
/*  73 */         boolean bool2 = KQSettingsBiz.showLeaveTypeSet(KQSettingsEnum.LEAVETYPE_UNIT.getMain_key());
/*  74 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  75 */         str7 = resourceComInfo.getSubCompanyID(str4);
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  80 */         RecordSet recordSet1 = new RecordSet();
/*  81 */         String str8 = "select * from kq_LeaveRules ";
/*  82 */         String str9 = " where (isDelete is null or isDelete <>1) and isEnable=1 ";
/*  83 */         if (!str6.equals("")) {
/*  84 */           str9 = str9 + " and leaveName like '%" + str6 + "%'";
/*     */         }
/*     */         
/*  87 */         if (bool1) {
/*  88 */           if (!str7.equals("")) {
/*  89 */             if (recordSet1.getDBType().equalsIgnoreCase("sqlserver")) {
/*  90 */               str9 = str9 + " and (scopeType=0 or (scopeType=1 and ','+scopeValue+',' like '%," + str7 + ",%') )";
/*  91 */             } else if (recordSet1.getDBType().equalsIgnoreCase("mysql")) {
/*  92 */               str9 = str9 + " and (scopeType=0 or (scopeType=1 and concat(',',scopeValue,',') like '%," + str7 + ",%') )";
/*     */             } else {
/*  94 */               str9 = str9 + " and (scopeType=0 or (scopeType=1 and ','||scopeValue||',' like '%," + str7 + ",%') )";
/*     */             } 
/*     */           } else {
/*  97 */             str9 = str9 + " and scopeType=0 ";
/*     */           } 
/*     */         }
/* 100 */         str8 = str8 + str9 + " order by showOrder,id ";
/*     */         
/* 102 */         recordSet1.executeQuery(str8, new Object[0]);
/*     */         
/* 104 */         while (recordSet1.next()) {
/* 105 */           String str10 = recordSet1.getString("leaveName");
/* 106 */           String str11 = recordSet1.getString("minimumUnit");
/* 107 */           String str12 = recordSet1.getString("computingMode");
/*     */           
/* 109 */           KQTransMethod kQTransMethod = new KQTransMethod();
/*     */ 
/*     */           
/* 112 */           String str13 = str10;
/* 113 */           if (bool2) {
/* 114 */             str13 = str13 + "(" + kQTransMethod.getMinimumUnitName4Browser(str11, "" + this.user.getLanguage()) + ")";
/*     */           }
/* 116 */           arrayList.add(new SearchConditionOption(recordSet1.getString("id"), str13));
/*     */         } 
/* 118 */       } catch (Exception exception) {
/* 119 */         exception.printStackTrace();
/*     */       } 
/* 121 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 122 */       return (Map)hashMap;
/*     */     } 
/* 124 */     String str1 = Util.null2String(paramMap.get("name"));
/* 125 */     String str2 = " and field002=1 ";
/* 126 */     if (!str1.equals("")) {
/* 127 */       str2 = str2 + " and name like '%";
/* 128 */       str2 = str2 + Util.fromScreen2(str1, 7);
/* 129 */       str2 = str2 + "%'";
/*     */     } 
/* 131 */     str2 = SqlUtils.replaceFirstAnd(str2);
/*     */ 
/*     */     
/* 134 */     String str3 = "select field004 as id ,field001 as name from hrmleavetypecolor ";
/* 135 */     str3 = str3 + str2;
/* 136 */     str3 = str3 + " order by field006 ";
/* 137 */     recordSet.executeSql(str3);
/* 138 */     while (recordSet.next()) {
/* 139 */       arrayList.add(new SearchConditionOption(recordSet.getString("id"), recordSet.getString("name")));
/*     */     }
/* 141 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 142 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/LeaveTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */