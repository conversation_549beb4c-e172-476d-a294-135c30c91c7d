/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.proj.Maint.ProjectStatusComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class ProjectBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  31 */     String str1 = Util.null2String(paramMap.get("systemIds"));
/*  32 */     if (str1.trim().startsWith(",")) {
/*  33 */       str1 = str1.substring(1);
/*     */     }
/*  35 */     String str2 = Util.null2String(paramMap.get("name"));
/*  36 */     String str3 = Util.null2String(paramMap.get("from"));
/*  37 */     String str4 = Util.null2String(paramMap.get("description"));
/*  38 */     String str5 = Util.null2String(paramMap.get("prjtype"));
/*  39 */     String str6 = Util.null2String(paramMap.get("worktype"));
/*  40 */     String str7 = Util.null2String(paramMap.get("manager"));
/*  41 */     String str8 = Util.null2String(paramMap.get("status"));
/*  42 */     String str9 = Util.null2String(paramMap.get("statusAll"));
/*  43 */     String str10 = Util.null2String(paramMap.get("sqlwhere"));
/*  44 */     String str11 = Util.null2String(paramMap.get("procode"));
/*     */     
/*  46 */     RecordSet recordSet = new RecordSet();
/*  47 */     if ("".equals(str10)) {
/*  48 */       str10 = str10 + " where 1 = 1 ";
/*     */     }
/*  50 */     if (!str2.equals("")) {
/*  51 */       str10 = str10 + " and t1.name like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */     }
/*  53 */     if (!str4.equals("")) {
/*  54 */       str10 = str10 + " and t1.description like '%" + Util.fromScreen2(str4, this.user.getLanguage()) + "%' ";
/*     */     }
/*  56 */     if (!str5.equals("")) {
/*  57 */       str10 = str10 + " and t1.prjtype = " + str5;
/*     */     }
/*  59 */     if (!str6.equals("")) {
/*  60 */       str10 = str10 + " and t1.worktype = " + str6;
/*     */     }
/*  62 */     if (!str7.equals("")) {
/*  63 */       str10 = str10 + " and t1.manager = " + str7;
/*     */     }
/*  65 */     if (!str8.equals("")) {
/*  66 */       str10 = str10 + " and t1.status in(" + str8 + ") ";
/*     */     }
/*  68 */     if (!str9.equals("")) {
/*  69 */       str10 = str10 + " and t1.status in (" + str9 + ") ";
/*     */     }
/*  71 */     if (!str11.equals("")) {
/*  72 */       str10 = str10 + " and t1.procode like '%" + Util.fromScreen2(str11, this.user.getLanguage()) + "%' ";
/*     */     }
/*     */     
/*  75 */     String str12 = "";
/*  76 */     CommonShareManager commonShareManager = new CommonShareManager();
/*  77 */     if ("prjtskimp".equalsIgnoreCase(str3)) {
/*  78 */       str12 = " (" + commonShareManager.getPrjShareWhereByUserCanEdit(this.user) + ") ";
/*     */     } else {
/*  80 */       str12 = " (" + commonShareManager.getPrjShareWhereByUser(this.user) + ") ";
/*     */     } 
/*     */     
/*  83 */     if (!"".equals(str12)) {
/*  84 */       str10 = str10 + " and " + str12;
/*     */     }
/*  86 */     String str13 = "";
/*  87 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  88 */       str13 = "t1.createdate || ' ' || t1.createtime  as createdatetime";
/*  89 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  90 */       str13 = str13 + "concat(ifnull(t1.createdate,''), ' ',ifnull(t1.createtime,'')) as createdatetime";
/*     */     }
/*  92 */     else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/*  93 */       str13 = "t1.createdate || ' ' || t1.createtime  as createdatetime";
/*     */     } else {
/*     */       
/*  96 */       str13 = str13 + "CONVERT(NVARCHAR(200),t1.createdate) + ' ' + CONVERT(NVARCHAR(100),t1.createtime) as createdatetime";
/*     */     } 
/*     */     
/*  99 */     String str14 = "t1.id, t1.name, t1.procode,t1.status," + str13 + ",t1.prjtype,t1.worktype,t1.manager";
/* 100 */     String str15 = "Prj_ProjectInfo t1 " + str10;
/*     */     
/* 102 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 103 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 104 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/* 105 */     arrayList.add(new SplitTableColBean("true", "createdatetime"));
/* 106 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17852, this.user.getLanguage()), "procode"));
/* 107 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(586, this.user.getLanguage()), "prjtype", "prjtype", "weaver.proj.Maint.ProjectTypeComInfo.getProjectTypename", 2));
/* 108 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(432, this.user.getLanguage()), "worktype", "worktype", "weaver.proj.Maint.WorkTypeComInfo.getWorkTypename", 2));
/* 109 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(144, this.user.getLanguage()), "manager", "manager", "weaver.hrm.resource.ResourceComInfo.getResourcename", 2));
/* 110 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(587, this.user.getLanguage()), "status", "status", "weaver.proj.Maint.ProjectStatusComInfo.getProjectStatusdesc"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 128 */     String str16 = "<div class='template-wrapper' style='margin-left: 15px;min-width:230px;width: 100%;-webkit-box-pack: start;-webkit-justify-content: flex-start;-ms-flex-pack: start;justify-content: flex-start;align-items: stretch;'>\t<div class='template-main-box' style='width: 100%;padding-bottom: 5px;'>\t\t<p class='template-row template-title' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 14px;color: #333;font-weight: 700;'>#{name}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(17852, this.user.getLanguage()) + "：#{procode}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 16px;float:right'>" + SystemEnv.getHtmlLabelName(587, this.user.getLanguage()) + "：#{status}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(586, this.user.getLanguage()) + "：#{prjtype}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(432, this.user.getLanguage()) + "：#{worktype}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(16573, this.user.getLanguage()) + "：#{manager}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(81517, this.user.getLanguage()) + "：#{createdatetime}</span>\t\t</p>\t</div></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 133 */     SplitTableBean splitTableBean = new SplitTableBean(str14, str15, "", "t1.id", "t1.id", arrayList);
/* 134 */     splitTableBean.setSqlsortway("DESC");
/* 135 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str16);
/* 136 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 137 */     splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 138 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 144 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 145 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 146 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 147 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true));
/* 148 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 17852, "procode"));
/*     */     
/* 150 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 151 */     arrayList1.add(new SearchConditionOption("", ""));
/* 152 */     ProjectStatusComInfo projectStatusComInfo = new ProjectStatusComInfo();
/* 153 */     while (projectStatusComInfo.next()) {
/* 154 */       String str1 = projectStatusComInfo.getProjectStatusid();
/* 155 */       String str2 = Util.toScreen(SystemEnv.getHtmlLabelName(Util.getIntValue(projectStatusComInfo.getProjectStatusname()), this.user.getLanguage()), this.user.getLanguage());
/* 156 */       arrayList1.add(new SearchConditionOption(str1, str2));
/*     */     } 
/* 158 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 587, "status", arrayList1));
/* 159 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 586, "prjtype", "244"));
/* 160 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 432, "worktype", "245"));
/* 161 */     if (!this.user.getLogintype().equals("2")) {
/* 162 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 144, "manager", "1"));
/*     */     }
/* 164 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 165 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 170 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 171 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 172 */     if ("".equals(str)) return (Map)hashMap; 
/* 173 */     RecordSet recordSet = new RecordSet();
/* 174 */     recordSet.execute("select t1.id, t1.name, t1.status,t1.prjtype,t1.worktype,t1.manager from Prj_ProjectInfo t1  where t1.id in (" + str + ")");
/* 175 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 176 */     ProjectStatusComInfo projectStatusComInfo = new ProjectStatusComInfo();
/* 177 */     while (recordSet.next()) {
/* 178 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 179 */       hashMap1.put("id", recordSet.getString("id"));
/* 180 */       hashMap1.put("name", recordSet.getString("name"));
/* 181 */       hashMap1.put("status", projectStatusComInfo.getProjectStatusdesc(recordSet.getString("status")));
/* 182 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 185 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 186 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 187 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 188 */     arrayList1.add(new ListHeadBean("status", SystemEnv.getHtmlLabelName(586, this.user.getLanguage())));
/*     */     
/* 190 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 191 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 192 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 193 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ProjectBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */