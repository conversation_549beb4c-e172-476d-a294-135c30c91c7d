/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.exceldesign.FormTemplateManager;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ 
/*     */ public class WorkflowModeBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  27 */     int i = Util.getIntValue(Util.null2String(paramMap.get("modetype")));
/*  28 */     switch (i) {
/*     */       case 0:
/*  30 */         return getGeneralBrowser(paramMap);
/*     */       case 1:
/*  32 */         return getModeBrowser(paramMap);
/*     */       case 2:
/*  34 */         return getHtmlBrowser(paramMap);
/*     */       case 3:
/*  36 */         return getMobileBrowser(paramMap);
/*     */     } 
/*  38 */     return getGeneralBrowser(paramMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  45 */     return getBrowserData(ParamUtil.request2Map(paramHttpServletRequest));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getGeneralBrowser(Map<String, Object> paramMap) {
/*  58 */     HashMap<Object, Object> hashMap = new HashMap<>(5);
/*  59 */     int i = Util.getIntValue(Util.null2String(paramMap.get("workflowid")));
/*  60 */     int j = Util.getIntValue(Util.null2String(paramMap.get("searchBywfid")));
/*     */     
/*  62 */     String str1 = Util.null2String(paramMap.get("nodename"));
/*  63 */     String str2 = Util.null2String(paramMap.get("modename"));
/*  64 */     String str3 = Util.null2String(paramMap.get("q"));
/*  65 */     if (!"".equals(str3)) {
/*  66 */       str3 = str2;
/*     */     }
/*  68 */     int k = Util.getIntValue(Util.null2String(paramMap.get("modetype")));
/*  69 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  70 */     String str4 = "";
/*  71 */     String str5 = "wfnodegeneralmode";
/*     */     
/*  73 */     int m = Util.getIntValue(workflowAllComInfo.getFormId(i + ""));
/*  74 */     int n = Util.getIntValue(workflowAllComInfo.getIsBill(i + ""));
/*  75 */     String str6 = "";
/*  76 */     String str7 = "l.id, l.modename, n.nodename,b.workflowname";
/*  77 */     String str8 = str5 + " l, workflow_base b, workflow_nodebase n";
/*  78 */     String str9 = "l.nodeid asc";
/*  79 */     String str10 = " l.nodeid=n.id and l.wfid=b.id and (n.isFreeNode != '1' OR n.isFreeNode IS null) and l.formid= " + m + " and l.isbill=" + n;
/*     */     
/*  81 */     if (j > 0) {
/*  82 */       str10 = "l.nodeid=n.id and l.wfid=b.id and (n.isFreeNode != '1' OR n.isFreeNode IS null) and b.id=" + j;
/*     */     }
/*  84 */     if (!"".equals(str2)) {
/*  85 */       str10 = str10 + " and modename like '%" + str2 + "%' ";
/*     */       
/*  87 */       RecordSet recordSet = new RecordSet();
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  97 */     if (!"".equals(str1)) {
/*  98 */       str10 = str10 + " and nodename like '%" + str1 + "%' ";
/*     */     }
/*     */ 
/*     */     
/* 102 */     System.out.println("select " + str7 + "from " + str8 + " where " + str10);
/* 103 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 104 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 105 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "modename", null, 0)).setIsInputCol(BoolAttr.TRUE));
/* 106 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, 1));
/* 107 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), "workflowname", null, 0));
/* 108 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str7, str8, str10, str9, "l.id", arrayList)));
/*     */     
/* 110 */     hashMap.put("string", "select " + str7 + " from" + str8 + " where" + str10);
/* 111 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getModeBrowser(Map<String, Object> paramMap) {
/* 117 */     HashMap<Object, Object> hashMap = new HashMap<>(5);
/* 118 */     int i = Util.getIntValue(Util.null2String(paramMap.get("workflowid")));
/*     */     
/* 120 */     int j = Util.getIntValue(Util.null2String(paramMap.get("searchBywfid")));
/*     */     
/* 122 */     String str1 = Util.null2String(paramMap.get("nodename"));
/* 123 */     String str2 = Util.null2String(paramMap.get("modename"));
/* 124 */     String str3 = Util.null2String(paramMap.get("q"));
/* 125 */     if (!"".equals(str3)) {
/* 126 */       str3 = str2;
/*     */     }
/* 128 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 129 */     String str4 = "";
/*     */     
/* 131 */     int k = Util.getIntValue(workflowAllComInfo.getFormId(i + ""));
/* 132 */     int m = Util.getIntValue(workflowAllComInfo.getIsBill(i + ""));
/* 133 */     String str5 = "";
/*     */ 
/*     */     
/* 136 */     String str6 = "workflow_nodemode.ID,modename,nodename,workflowname";
/* 137 */     String str7 = " workflow_nodemode , workflow_nodebase , workflow_base ";
/* 138 */     String str8 = "workflow_nodemode.workflowid,nodeid asc";
/*     */ 
/*     */     
/* 141 */     String str9 = "(workflow_nodebase.IsFreeNode IS NULL OR workflow_nodebase.IsFreeNode != '1') and workflow_nodemode.workflowid=workflow_base.id and workflow_nodemode.nodeid=workflow_nodebase.id and workflow_nodemode.formid=" + k + " and isbill=" + m + " and  isprint=0";
/*     */ 
/*     */     
/* 144 */     if (j > 0) {
/* 145 */       str9 = str9 + " and workflow_base.id=" + j;
/*     */     }
/* 147 */     if (!"".equals(str2)) {
/* 148 */       str9 = str9 + " and ( modename like '%" + str2 + "%' ";
/* 149 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 151 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/* 153 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 154 */           str9 = str9 + " or f_GetPy(modename) like '%" + str2.toUpperCase() + "%'";
/* 155 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 156 */           str9 = str9 + " or [dbo].f_GetPy(modename) like '%" + str2.toUpperCase() + "%'";
/* 157 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 158 */           str9 = str9 + " or getpinyin(modename) like '%" + str2.toUpperCase() + "%'";
/*     */         }  } 
/* 160 */       str9 = str9 + ")";
/*     */     } 
/*     */     
/* 163 */     if (!"".equals(str1)) {
/* 164 */       str9 = str9 + " and nodename like '%" + str1 + "%' ";
/*     */     }
/*     */ 
/*     */     
/* 168 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 169 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 170 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "modename", null, 0)).setIsInputCol(BoolAttr.TRUE));
/* 171 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, 1));
/* 172 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), "workflowname", null, 0));
/* 173 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str6, str7, str9, str8, "workflow_nodemode.ID", arrayList)));
/*     */     
/* 175 */     hashMap.put("string", "select " + str6 + " from" + str7 + " where" + str9);
/* 176 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getHtmlBrowser(Map<String, Object> paramMap) {
/* 182 */     HashMap<Object, Object> hashMap = new HashMap<>(5);
/* 183 */     int i = Util.getIntValue(Util.null2String(paramMap.get("workflowid")));
/*     */     
/* 185 */     int j = Util.getIntValue(Util.null2String(paramMap.get("searchBywfid")));
/*     */     
/* 187 */     int k = Util.getIntValue(Util.null2String(paramMap.get("printType")), 0);
/* 188 */     int m = Util.getIntValue(Util.null2String(paramMap.get("showtype")));
/* 189 */     int n = Util.getIntValue(Util.null2String(paramMap.get("notModeids")), 0);
/* 190 */     int i1 = Util.getIntValue(Util.null2String(paramMap.get("isprint")), -1);
/* 191 */     int i2 = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), -1);
/* 192 */     String str1 = Util.null2String(paramMap.get("nodename"));
/* 193 */     String str2 = Util.null2String(paramMap.get("modename"));
/* 194 */     String str3 = Util.null2String(paramMap.get("q"));
/* 195 */     if (!"".equals(str3)) {
/* 196 */       str2 = str3;
/*     */     }
/* 198 */     int i3 = Util.getIntValue(Util.null2String(paramMap.get("search_active")), -1);
/*     */     
/* 200 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 201 */     String str4 = "";
/*     */     
/* 203 */     int i4 = Util.getIntValue(workflowAllComInfo.getFormId(i + ""));
/* 204 */     int i5 = Util.getIntValue(workflowAllComInfo.getIsBill(i + ""));
/* 205 */     String str5 = "";
/*     */ 
/*     */     
/* 208 */     String str6 = "";
/* 209 */     if (j != -1) {
/* 210 */       str6 = j + "";
/*     */     }
/*     */ 
/*     */     
/* 214 */     String str7 = " and formid=" + i4 + " and isbill=" + i5 + " ";
/*     */     
/* 216 */     String str8 = " * ";
/* 217 */     RecordSet recordSet = new RecordSet();
/* 218 */     String str9 = "";
/* 219 */     String str10 = "";
/* 220 */     if (recordSet.getDBType().equals("sqlserver")) {
/* 221 */       str9 = " top (10000) ";
/* 222 */       if (i1 == 1) {
/* 223 */         str10 = " ( select * from ";
/*     */       }
/*     */     } 
/* 226 */     String str11 = str10 + " (select " + str9 + " a.id,b.workflowname,c.nodename,a.layoutname,a.isactive from workflow_nodehtmllayout a,workflow_base b,workflow_nodebase c";
/* 227 */     String str12 = " where a.nodeid=c.id and a.workflowid=b.id and a.formid=" + i4 + " and a.isbill=" + i5;
/* 228 */     if (k > 0) {
/* 229 */       str12 = str12 + " and a.type=1 ";
/* 230 */       if (i2 > 0)
/*     */       {
/* 232 */         str12 = str12 + " and a.id not in(select id from workflow_nodehtmllayout where (type=1 or type=-1) and  isactive=1 and workflowid=" + i + " and nodeid=" + i2 + ")";
/*     */       }
/*     */     } else {
/*     */       
/* 236 */       str12 = str12 + " and a.type=0";
/*     */     } 
/*     */ 
/*     */     
/* 240 */     str12 = str12 + " and exists (select 1 from workflow_nodebase nb,workflow_flownode fn where nb.id=fn.nodeid and nb.id=a.nodeid and (nb.isfreenode is null or nb.isfreenode<>'1'))";
/* 241 */     if (!"".equals(str6)) {
/* 242 */       str12 = str12 + " and (workflowid=" + str6 + " or nodeid=" + FormTemplateManager.getFORMVIRTUALNODEID() + ")";
/*     */     }
/* 244 */     if (i3 != -1 && i3 != 0) {
/* 245 */       if (i3 == 1) {
/* 246 */         str12 = str12 + " and isactive=1 ";
/* 247 */       } else if (i3 == 2) {
/* 248 */         str12 = str12 + " and isactive!=1";
/*     */       } 
/*     */     }
/*     */     
/* 252 */     if (!"".equals(str2)) {
/* 253 */       str12 = str12 + " and (layoutname like '%" + str2 + "%' ";
/*     */       
/* 255 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/* 257 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 258 */           str12 = str12 + " or f_GetPy(layoutname) like '%" + str2.toUpperCase() + "%'";
/* 259 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 260 */           str12 = str12 + " or [dbo].f_GetPy(layoutname) like '%" + str2.toUpperCase() + "%'";
/* 261 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 262 */           str12 = str12 + " or getpinyin(layoutname) like '%" + str2.toUpperCase() + "%'";
/*     */         }  } 
/* 264 */       str12 = str12 + ")";
/*     */     } 
/*     */     
/* 267 */     if (!"".equals(str1)) {
/* 268 */       str12 = str12 + " and nodename like '%" + str1 + "%' ";
/*     */     }
/*     */ 
/*     */     
/* 272 */     if (n > 0) {
/* 273 */       str12 = str12 + " and a.id != " + n + " ";
/*     */     }
/* 275 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 276 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */     
/* 278 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "layoutname", null, "com.api.browser.service.impl.WorkflowModeBrowserService.getModeFullname", "column:id+column:isactive+" + this.user
/*     */           
/* 280 */           .getLanguage(), 1))
/* 281 */         .setIsInputCol(BoolAttr.TRUE));
/* 282 */     if (m > 0) {
/* 283 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, "com.api.browser.service.impl.WorkflowModeBrowserService.getFullname", 
/* 284 */             SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), 0));
/* 285 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), "workflowname", null, "com.api.browser.service.impl.WorkflowModeBrowserService.getFullname", 
/* 286 */             SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), 0));
/*     */     }
/*     */     else {
/*     */       
/* 290 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, 0));
/*     */       
/* 292 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), "workflowname", null, 0));
/*     */     } 
/*     */ 
/*     */     
/* 296 */     String str13 = " order by workflowid asc,(select nodeorder from workflow_flownode d where d.nodeid=a.nodeid) asc,isactive desc) dd ";
/* 297 */     str11 = str11 + str12 + str13;
/*     */     
/* 299 */     if (i1 == 1) {
/*     */       
/* 301 */       String str14 = "";
/* 302 */       if (j != -1) {
/* 303 */         str14 = str14 + " and wn.workflowid=" + str6;
/*     */       }
/* 305 */       if (!"".equals(str2)) {
/* 306 */         str14 = str14 + " and (wn.MODENAME like '%" + str2 + "%' ";
/* 307 */         if (!ConnectionPool.getInstance().isNewDB())
/*     */         {
/* 309 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 310 */             str14 = str14 + " or f_GetPy(wn.MODENAME) like '%" + str2.toUpperCase() + "%'";
/* 311 */           } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 312 */             str14 = str14 + " or [dbo].f_GetPy(wn.MODENAME) like '%" + str2.toUpperCase() + "%'";
/* 313 */           } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 314 */             str14 = str14 + " or getpinyin(wn.MODENAME) like '%" + str2.toUpperCase() + "%'";
/*     */           }  } 
/* 316 */         str14 = str14 + ")";
/*     */       } 
/*     */ 
/*     */       
/* 320 */       if (!"".equals(str1)) {
/* 321 */         str14 = str14 + " and wnb.nodename like '%" + str1 + "%' ";
/*     */       }
/*     */       
/* 324 */       if (recordSet.getDBType().equals("sqlserver")) {
/* 325 */         str14 = str14 + " ) c";
/*     */       }
/*     */       
/* 328 */       String str15 = " union ALL select -wn.id as id,wb.workflowname,wnb.nodename,wn.MODENAME as layoutname,0   from workflow_nodemode wn, workflow_base wb,workflow_nodebase wnb where wn.workflowid=wb.id  and wnb.id=wn.id and wb.formid=" + i4 + " and wb.isbill=" + i5 + " and isprint=" + k + str14;
/*     */ 
/*     */       
/* 331 */       if (i3 != 1 && i3 != 2) {
/* 332 */         str11 = str11 + str15;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 337 */     hashMap.put("fromSql", str11);
/* 338 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str8, str11, "", "", "id", arrayList)));
/*     */     
/* 340 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMobileBrowser(Map<String, Object> paramMap) {
/* 352 */     int i = Util.getIntValue(Util.null2String(paramMap.get("search_active")));
/* 353 */     int j = Util.getIntValue(Util.null2String(paramMap.get("workflowid")));
/* 354 */     int k = Util.getIntValue(Util.null2String(paramMap.get("searchBywfid")));
/* 355 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 356 */     int m = Util.getIntValue(workflowAllComInfo.getFormId(j + ""));
/* 357 */     int n = Util.getIntValue(workflowAllComInfo.getIsBill(j + ""));
/* 358 */     int i1 = Util.getIntValue(Util.null2String(paramMap.get("notModeids")));
/* 359 */     String str1 = Util.null2String(paramMap.get("modename"));
/* 360 */     String str2 = " and formid=" + m + " and isbill=" + n + " ";
/* 361 */     String str3 = " a.id,a.workflowid,a.nodeid,a.layoutname,a.version,a.isactive,workflowname,NODENAME";
/* 362 */     String str4 = " workflow_nodehtmllayout a ,workflow_base  b,WORKFLOW_NODEBASE c";
/* 363 */     String str5 = " a.formid=" + m + " and a.isbill=" + n + " and a.type=2 and a.workflowid=b.id and a.nodeid=c.id";
/*     */     
/* 365 */     str5 = str5 + " and exists (select 1 from workflow_nodebase nb,workflow_flownode fn where nb.id=fn.nodeid and nb.id=a.nodeid and (nb.isfreenode is null or nb.isfreenode<>'1'))";
/*     */     
/* 367 */     if (k != -1)
/* 368 */       str5 = str5 + " and workflowid=" + k; 
/* 369 */     String str6 = Util.null2String(paramMap.get("nodename"));
/*     */ 
/*     */     
/* 372 */     if (!"".equals(str1))
/* 373 */       str5 = str5 + " and layoutname like '%" + str1 + "%' "; 
/* 374 */     if (!"".equals(str6)) {
/* 375 */       str5 = str5 + " and nodeName like '%" + str6 + "%' ";
/*     */     }
/*     */     
/* 378 */     if (i != -1 && i != 0) {
/* 379 */       if (i == 1) {
/* 380 */         str5 = str5 + " and isactive=1 ";
/* 381 */       } else if (i == 2) {
/* 382 */         str5 = str5 + " and isactive!=1 ";
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 387 */     if (i1 > 0) {
/* 388 */       str5 = str5 + " and a.id != " + i1 + " ";
/*     */     }
/* 390 */     String str7 = " workflowid asc,(select nodeorder from workflow_flownode b where b.nodeid=a.nodeid) asc,isactive desc";
/* 391 */     HashMap<Object, Object> hashMap = new HashMap<>(5);
/* 392 */     System.out.println("select " + str3 + " from " + str4 + "where" + str5);
/* 393 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 394 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */ 
/*     */     
/* 397 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "layoutname", null, "com.api.browser.service.impl.WorkflowModeBrowserService.getModeFullname", "column:id+column:isactive+" + this.user
/*     */           
/* 399 */           .getLanguage(), 1))
/* 400 */         .setIsInputCol(BoolAttr.TRUE));
/* 401 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, 1));
/* 402 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), "workflowname", null, 0));
/* 403 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str3, str4, str5, str7, "id", arrayList)));
/*     */     
/* 405 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 413 */     int i = Util.getIntValue(Util.null2String(paramMap.get("modetype")));
/*     */ 
/*     */     
/* 416 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 417 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 418 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 419 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 420 */     int j = Util.getIntValue(Util.null2String(paramMap.get("workflowid")), -1);
/* 421 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.BROWSER, 81651, "searchBywfid", "-99991");
/*     */     
/* 423 */     BrowserBean browserBean = searchConditionItem1.getBrowserConditionParam();
/* 424 */     if (j != -1 && i != 0) {
/* 425 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 426 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 427 */       hashMap1.put("id", j + "");
/* 428 */       WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 429 */       hashMap1.put("name", workflowAllComInfo.getWorkflowname("" + j));
/* 430 */       arrayList2.add(hashMap1);
/* 431 */       browserBean.setReplaceDatas(arrayList2);
/*     */     } 
/*     */     
/* 434 */     browserBean.setIconBgcolor("#0079DE");
/* 435 */     browserBean.setIcon("icon-coms-workflow");
/* 436 */     browserBean.setTitle(SystemEnv.getHtmlLabelName(385227, this.user.getLanguage()));
/*     */     
/* 438 */     arrayList.add(searchConditionItem1);
/* 439 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15070, "nodename"));
/* 440 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18151, "modename", true));
/* 441 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 442 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/* 443 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(678, this.user.getLanguage())));
/* 444 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(1477, this.user.getLanguage())));
/* 445 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, "64,23247", "search_active", arrayList1);
/* 446 */     if (i == 0 || i == 1)
/* 447 */       searchConditionItem2.setViewAttr(1); 
/* 448 */     arrayList.add(searchConditionItem2);
/* 449 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFullname(String paramString1, String paramString2) {
/* 456 */     return paramString2 + ": " + paramString1;
/*     */   }
/*     */   
/*     */   public String getModeFullname(String paramString1, String paramString2) {
/* 460 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 461 */     String str1 = arrayOfString[0];
/* 462 */     if (Util.getIntValue(str1) < 0) {
/* 463 */       return paramString1;
/*     */     }
/* 465 */     String str2 = arrayOfString[1];
/* 466 */     String str3 = arrayOfString[2];
/*     */ 
/*     */     
/* 469 */     return paramString1 + "  (" + ("1".equals(str2) ? SystemEnv.getHtmlLabelName(678, Util.getIntValue(str3)) : SystemEnv.getHtmlLabelName(1477, Util.getIntValue(str3))) + ")";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowModeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */