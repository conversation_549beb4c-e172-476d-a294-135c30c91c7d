/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.crm.ContacterShareBase;
/*     */ import weaver.crm.Maint.ContractTypeComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ContractBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"typeid\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"status\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  70 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  71 */     String str1 = Util.null2String(paramMap.get("name"));
/*  72 */     String str2 = Util.null2String(paramMap.get("typeId"));
/*  73 */     String str3 = Util.null2String(paramMap.get("status"));
/*  74 */     String str4 = " where t1.id != 0 and t1.deleted is null";
/*  75 */     String str5 = "" + this.user.getUID();
/*  76 */     String str6 = "" + this.user.getLogintype();
/*     */     
/*  78 */     if (!str1.equals("")) {
/*  79 */       str4 = str4 + " and t1.name like '%";
/*  80 */       str4 = str4 + Util.fromScreen2(str1, this.user.getLanguage());
/*  81 */       str4 = str4 + "%'";
/*     */     } 
/*  83 */     if (!str2.equals("")) {
/*  84 */       str4 = str4 + " and t1.typeId = " + str2;
/*     */     }
/*  86 */     if (!str3.equals("")) {
/*  87 */       str4 = str4 + " and t1.status = " + str3;
/*     */     }
/*     */     
/*  90 */     String str7 = "";
/*  91 */     if (str6.equals("1")) {
/*     */       
/*  93 */       ContacterShareBase contacterShareBase = new ContacterShareBase();
/*     */       
/*  95 */       String str = contacterShareBase.getTempTable("" + this.user.getUID());
/*  96 */       str7 = " CRM_Contract t1 , " + str + " t2 ,CRM_CustomerInfo  t3";
/*     */       
/*  98 */       str4 = str4 + " and t1.crmId = t3.id and t1.id = t2.relateditemid";
/*     */     } else {
/* 100 */       str7 = " CRM_Contract t1,CRM_CustomerInfo  t3  ";
/*     */       
/* 102 */       str4 = str4 + " and t1.crmId = t3.id and t1.crmId=" + this.user.getUID();
/*     */     } 
/*     */     
/* 105 */     String str8 = " t1.* ";
/* 106 */     String str9 = "t1.id";
/*     */     
/* 108 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 109 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 110 */     arrayList.add((new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 111 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(6083, this.user.getLanguage()), "typeid", "typeid", "weaver.crm.Maint.ContractTypeComInfo.getContractTypename"));
/* 112 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(602, this.user.getLanguage()), "status", "status", "weaver.crm.Maint.CRMTransMethod.getContractStatus", String.valueOf(this.user.getLanguage())));
/*     */     
/* 114 */     SplitTableBean splitTableBean = new SplitTableBean(str8, str7, str4, str9, "t1.id", arrayList);
/* 115 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */ 
/*     */     
/*     */     try {
/* 119 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 120 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"typeid\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"status\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 121 */     } catch (Exception exception) {
/* 122 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 125 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/* 127 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 132 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 133 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 134 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 135 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true));
/*     */     
/* 137 */     ContractTypeComInfo contractTypeComInfo = new ContractTypeComInfo();
/* 138 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 139 */     arrayList1.add(new SearchConditionOption("", ""));
/* 140 */     while (contractTypeComInfo.next()) {
/* 141 */       arrayList1.add(new SearchConditionOption(contractTypeComInfo.getContractTypeid(), contractTypeComInfo.getContractTypename()));
/*     */     }
/* 143 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 6083, "typeId", arrayList1));
/*     */     
/* 145 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 146 */     arrayList2.add(new SearchConditionOption("", ""));
/* 147 */     arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(615, this.user.getLanguage())));
/* 148 */     arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(359, this.user.getLanguage())));
/* 149 */     arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(6095, this.user.getLanguage())));
/* 150 */     arrayList2.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(555, this.user.getLanguage())));
/* 151 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 602, "status", arrayList2));
/* 152 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 153 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 157 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 158 */     BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 159 */     String str = browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse);
/* 160 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str);
/* 161 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ContractBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */