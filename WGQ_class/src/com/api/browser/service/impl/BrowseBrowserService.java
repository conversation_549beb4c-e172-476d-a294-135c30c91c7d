/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BrowseBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  28 */     String str1 = "1,2,3,4,5,6,7,8,9,10,11,12,14,15,16,19,26,33,56,64,160,161,162,164,165,167,169,224,241,242,243,246,256,257,258,261,264,265,267,292";
/*  29 */     String str2 = "17,18,37,57,65,135,152,166,168,170,184,194,268,269,278,293";
/*     */     
/*  31 */     String str3 = Util.null2String(paramMap.get("name"));
/*  32 */     String str4 = "w.typeid AS groupid,w.id AS type,w.labelid AS itemlabel,w.orderid AS orderid,h.labelname ";
/*  33 */     String str5 = "workflow_browserurl w LEFT JOIN HtmlLabelInfo h ON w.labelid= h.indexid ";
/*  34 */     String str6 = "h.languageid=" + this.user.getLanguage() + " AND w.id IN (" + str2 + ") AND w.browserurl IS NOT NULL AND w.useable = 1 AND w.browserurl IS NOT NULL ";
/*  35 */     if (StringUtils.isNotBlank(str3)) {
/*  36 */       str6 = str6 + " and  h.labelname like '%" + str3 + "%'";
/*     */     }
/*  38 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  39 */     SplitTableColBean splitTableColBean1 = (new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(695, this.user.getLanguage()), "labelname", "itemlabel")).setIsInputCol(BoolAttr.TRUE);
/*  40 */     splitTableColBean1.setTransmethod("com.engine.systeminfo.util.BrowserConfigManager.getBroserName");
/*  41 */     splitTableColBean1.setTransMethodForce("true");
/*  42 */     splitTableColBean1.setOtherpara("column:description");
/*  43 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("true", "type");
/*  44 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description");
/*  45 */     splitTableColBean1.setShowType(1);
/*     */     
/*  47 */     arrayList.add(splitTableColBean1);
/*  48 */     arrayList.add(splitTableColBean2);
/*     */ 
/*     */     
/*  51 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str6, "type", "type", "asc", arrayList);
/*  52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  54 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  62 */     RecordSet recordSet = new RecordSet();
/*  63 */     String str2 = "SELECT w.id AS type,h.labelname AS name FROM workflow_browserurl w LEFT JOIN HtmlLabelInfo h ON w.labelid= h.indexid AND h.languageid=? where h.labelname like ? ";
/*  64 */     recordSet.executeQuery(str2, new Object[] { Integer.valueOf(this.user.getLanguage()), "%" + str1 + "%" });
/*  65 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  66 */     while (recordSet.next()) {
/*  67 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  68 */       hashMap1.put("type", recordSet.getString("type"));
/*  69 */       String str = recordSet.getString("name");
/*  70 */       hashMap1.put("name", str);
/*  71 */       arrayList.add(hashMap1);
/*     */     } 
/*  73 */     hashMap.put("datas", arrayList);
/*  74 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  79 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  80 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  81 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  82 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/*  83 */     String str2 = Util.null2String(paramMap.get("name"));
/*  84 */     RecordSet recordSet = new RecordSet();
/*  85 */     String str3 = "SELECT w.id AS type,h.labelname AS name FROM workflow_browserurl w LEFT JOIN HtmlLabelInfo h ON w.labelid= h.indexid AND h.languageid=? where 1=1 and w.id in(" + str1 + ")";
/*  86 */     if (StringUtils.isNotBlank(str2)) {
/*  87 */       str3 = str3 + " and h.labelname like '%" + str2 + "%'";
/*     */     }
/*  89 */     recordSet.executeQuery(str3, new Object[] { Integer.valueOf(this.user.getLanguage()) });
/*  90 */     while (recordSet.next()) {
/*  91 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  92 */       String str4 = Util.null2String(recordSet.getString("type"));
/*  93 */       String str5 = Util.null2String(recordSet.getString("name"));
/*  94 */       hashMap1.put("type", str4);
/*  95 */       hashMap1.put("name", str5);
/*  96 */       arrayList.add(hashMap1);
/*     */     } 
/*  98 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  99 */     arrayList1.add(new ListHeadBean("type", BoolAttr.TRUE));
/* 100 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/*     */     
/* 102 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 103 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 104 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 105 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BrowseBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */