/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.service.Browser;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConfigComInfo;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.portal.backend.bean.PortalMenuTree;
/*     */ import com.engine.common.service.impl.WorkflowCommonServiceImpl;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PortalSystemPageService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  26 */     String str1 = Util.null2String(paramMap.get("sourcetype"));
/*  27 */     String str2 = Util.null2s(Util.null2String(paramMap.get("key")), "1");
/*  28 */     ArrayList arrayList = new ArrayList();
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  30 */     if ("1".equals(str1)) {
/*     */       
/*  32 */       hashMap.put("selectDatas", getPortalList(str1, str2));
/*     */       
/*  34 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  35 */       BrowserConfigComInfo browserConfigComInfo = new BrowserConfigComInfo();
/*  36 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  37 */       String str = browserConfigComInfo.getClazz("doccategory");
/*     */       try {
/*  39 */         Browser browser = (Browser)Class.forName(str).newInstance();
/*  40 */         browser.setBrowserType("doccategory");
/*  41 */         browser.setUser(this.user);
/*  42 */         hashMap1.putAll(browser.getBrowserData(hashMap2));
/*  43 */       } catch (Exception exception) {
/*  44 */         exception.printStackTrace();
/*  45 */         hashMap1.put("api_status", Boolean.valueOf(false));
/*  46 */         hashMap1.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */       } 
/*  48 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */       
/*  50 */       hashMap.put("datas", hashMap1.get("datas"));
/*  51 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  52 */       hashMap.put("isLoadAll", Boolean.valueOf(true));
/*     */     }
/*  54 */     else if ("2".equals(str1)) {
/*  55 */       hashMap.put("selectDatas", getPortalList(str1, str2));
/*  56 */       WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/*  57 */       List list = workflowCommonServiceImpl.getWFTreeData();
/*  58 */       hashMap.put("datas", list);
/*  59 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  60 */       hashMap.put("isLoadAll", Boolean.valueOf(true));
/*     */     } 
/*  62 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addListToTree(List<String> paramList1, List<String> paramList2, List<PortalMenuTree> paramList) {
/*  70 */     if (paramList1.size() > 0) {
/*  71 */       RecordSet recordSet = new RecordSet();
/*  72 */       String str1 = "";
/*  73 */       for (byte b = 0; b < paramList1.size(); b++) {
/*  74 */         str1 = str1 + (String)paramList1.get(b) + ",";
/*     */       }
/*  76 */       str1 = str1.substring(0, str1.length() - 1);
/*  77 */       ArrayList<String> arrayList = new ArrayList();
/*  78 */       String str2 = "select distinct id,menuname,menuhref,menutype,menuparentid from menucustom where id in (" + str1 + ")";
/*  79 */       recordSet.executeQuery(str2, new Object[0]);
/*  80 */       while (recordSet.next()) {
/*  81 */         String str3 = recordSet.getString("id");
/*  82 */         String str4 = recordSet.getString("menuname");
/*  83 */         String str5 = recordSet.getString("menuhref");
/*  84 */         String str6 = recordSet.getString("menuparentid");
/*  85 */         String str7 = Util.null2String(recordSet.getString("menutype"));
/*  86 */         boolean bool = !"".equals(Util.null2String(str5)) ? true : false;
/*  87 */         paramList.add(new PortalMenuTree(str3, false, str4, str6, bool, str5, str7));
/*  88 */         if (!"0".equals(str6)) {
/*  89 */           arrayList.add(str6);
/*     */         }
/*  91 */         paramList2.add(str7);
/*     */       } 
/*  93 */       addListToTree(arrayList, paramList2, paramList);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getPortalList(String paramString1, String paramString2) {
/* 102 */     int i = this.user.getLanguage();
/* 103 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 104 */     if ("1".equals(paramString1)) {
/* 105 */       String[] arrayOfString1 = { "1", "2", "3", "4" };
/* 106 */       String[] arrayOfString2 = new String[4];
/* 107 */       String[] arrayOfString3 = new String[4];
/* 108 */       arrayOfString2[0] = SystemEnv.getHtmlLabelName(1986, i);
/* 109 */       arrayOfString3[0] = "/spa/document/static/index.html#/main/document/add?secid=";
/* 110 */       arrayOfString2[1] = SystemEnv.getHtmlLabelName(1212, i);
/* 111 */       arrayOfString3[1] = "/spa/document/static/index.html#/main/document/search?viewcondition=0&secid=";
/* 112 */       arrayOfString2[2] = SystemEnv.getHtmlLabelName(16397, i);
/* 113 */       arrayOfString3[2] = "/spa/document/static/index.html#/main/document/search?viewcondition=1&secid=";
/* 114 */       arrayOfString2[3] = SystemEnv.getHtmlLabelName(16398, i);
/* 115 */       arrayOfString3[3] = "/spa/document/static/index.html#/main/document/search?viewcondition=2&secid=";
/* 116 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 117 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/* 118 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 119 */         hashMap1.put("key", arrayOfString1[b]);
/* 120 */         hashMap1.put("showname", arrayOfString2[b]);
/* 121 */         hashMap1.put("url", arrayOfString3[b]);
/* 122 */         arrayList.add(hashMap1);
/*     */       } 
/* 124 */       hashMap.put("selected", paramString2);
/* 125 */       hashMap.put("options", arrayList);
/* 126 */     } else if ("2".equals(paramString1)) {
/*     */       
/* 128 */       String[] arrayOfString1 = { "1", "2", "3", "4", "5", "6", "7" };
/* 129 */       String[] arrayOfString2 = new String[7];
/* 130 */       String[] arrayOfString3 = new String[7];
/* 131 */       arrayOfString2[0] = SystemEnv.getHtmlLabelName(16392, i);
/* 132 */       arrayOfString3[0] = "/spa/workflow/static/index.html#/main/workflow/add?";
/* 133 */       arrayOfString2[1] = SystemEnv.getHtmlLabelName(1207, i);
/* 134 */       arrayOfString3[1] = "/spa/workflow/static/index.html#/main/workflow/queryFlowResult?fromwhere=jsonFilter&jsonstr=";
/* 135 */       arrayOfString2[2] = SystemEnv.getHtmlLabelName(17991, i);
/* 136 */       arrayOfString3[2] = "/spa/workflow/static/index.html#/main/workflow/queryFlowResult?fromwhere=jsonFilter&jsonstr=";
/* 137 */       arrayOfString2[3] = SystemEnv.getHtmlLabelName(17992, i);
/* 138 */       arrayOfString3[3] = "/spa/workflow/static/index.html#/main/workflow/queryFlowResult?fromwhere=jsonFilter&jsonstr=";
/* 139 */       arrayOfString2[4] = SystemEnv.getHtmlLabelName(21639, i);
/* 140 */       arrayOfString3[4] = "/spa/workflow/static/index.html#/main/workflow/queryFlowResult?fromwhere=jsonFilter&jsonstr=";
/* 141 */       arrayOfString2[5] = SystemEnv.getHtmlLabelName(21640, i);
/* 142 */       arrayOfString3[5] = "/spa/workflow/static/index.html#/main/workflow/supervise?fromwhere=jsonFilter&jsonstr=";
/* 143 */       arrayOfString2[6] = SystemEnv.getHtmlLabelName(1210, i);
/* 144 */       arrayOfString3[6] = "/spa/workflow/static/index.html#/main/workflow/queryFlowResult?fromwhere=jsonFilter&jsonstr=";
/* 145 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 146 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/* 147 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 148 */         hashMap1.put("key", arrayOfString1[b]);
/* 149 */         hashMap1.put("showname", arrayOfString2[b]);
/* 150 */         hashMap1.put("url", arrayOfString3[b]);
/* 151 */         arrayList.add(hashMap1);
/*     */       } 
/* 153 */       hashMap.put("selected", paramString2);
/* 154 */       hashMap.put("options", arrayList);
/*     */     } 
/* 156 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private void handDatas(BrowserTreeNode paramBrowserTreeNode, Map<String, Object> paramMap) {
/* 161 */     paramMap.put("canClick", Boolean.valueOf(paramBrowserTreeNode.isCanClick()));
/* 162 */     paramMap.put("id", paramBrowserTreeNode.getId());
/* 163 */     paramMap.put("isLeaf", Boolean.valueOf(paramBrowserTreeNode.isSelected()));
/* 164 */     paramMap.put("isParent", Boolean.valueOf(paramBrowserTreeNode.getIsParent()));
/* 165 */     paramMap.put("name", paramBrowserTreeNode.getName());
/* 166 */     paramMap.put("pid", paramBrowserTreeNode.getPid());
/* 167 */     boolean bool = paramBrowserTreeNode.getIsParent();
/* 168 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 169 */     List<BrowserTreeNode> list = paramBrowserTreeNode.getSubs();
/* 170 */     if (bool && list != null) {
/* 171 */       for (byte b = 0; b < list.size(); b++) {
/* 172 */         BrowserTreeNode browserTreeNode = list.get(b);
/* 173 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 174 */         handDatas(browserTreeNode, (Map)hashMap);
/* 175 */         arrayList.add(hashMap);
/*     */       } 
/*     */     }
/* 178 */     paramMap.put("subs", arrayList);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/PortalSystemPageService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */