/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.fnaMulDimensions.util.DimensionUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaMemberTagBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  53 */     if (this.user == null) {
/*  54 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  55 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  58 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  59 */     SplitTableBean splitTableBean = getTableList(paramMap);
/*  60 */     if (splitTableBean == null) {
/*  61 */       ArrayList<ListHeadBean> arrayList = new ArrayList();
/*     */       
/*  63 */       arrayList.add(new ListHeadBean("memberTagName", "memberTagName", 1, BoolAttr.TRUE));
/*  64 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  65 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */     } else {
/*  67 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } 
/*     */     
/*  70 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/*  81 */     String str1 = Util.null2String(paramMap.get("memberTagName"));
/*  82 */     String str2 = Util.null2String(paramMap.get("accountId"));
/*  83 */     String str3 = Util.null2String(paramMap.get("sqlwhere"));
/*  84 */     String str4 = (new DimensionUtil()).createMemberTagTableName(str2, this.user);
/*  85 */     String str5 = "10";
/*  86 */     String str6 = " id,memberTagName,tagDescription,displayOrder ";
/*  87 */     String str7 = " " + str4;
/*  88 */     String str8 = " where 1=1 ";
/*     */     
/*  90 */     if (!"".equals(str1)) {
/*  91 */       str8 = str8 + " and memberTagName like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/*  93 */     if (!"".equals(str3)) {
/*  94 */       str8 = str8 + str3;
/*     */     }
/*  96 */     String str9 = "displayOrder";
/*  97 */     String str10 = "id";
/*     */     
/*  99 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 100 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 101 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(81323, this.user.getLanguage()), "memberTagName", "memberTagName"))
/* 102 */         .setIsInputCol(BoolAttr.TRUE).setShowType(1));
/* 103 */     return new SplitTableBean(str6, str7, str8, str9, str10, "ASC", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 114 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 115 */     RecordSet recordSet = new RecordSet();
/* 116 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 117 */     String str2 = Util.null2String(paramMap.get("accountId"));
/* 118 */     String str3 = Util.null2String(paramMap.get("sqlwhere"));
/* 119 */     String str4 = (new DimensionUtil()).createMemberTagTableName(str2, this.user);
/* 120 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 121 */     if (!"".equals(str4)) {
/*     */       
/* 123 */       String str = "";
/* 124 */       String[] arrayOfString = str1.split(",");
/* 125 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 126 */         if (!"".equals(str)) {
/* 127 */           str = str + ",";
/*     */         }
/* 129 */         str = str + "'" + arrayOfString[b] + "'";
/*     */       } 
/*     */       
/* 132 */       StringBuffer stringBuffer = new StringBuffer();
/* 133 */       stringBuffer.append(" select id,memberTagName,tagDescription,displayOrder from  " + str4);
/* 134 */       stringBuffer.append(" where 1=1  ");
/* 135 */       stringBuffer.append(" and id in (").append(str).append(") ");
/*     */       
/* 137 */       if (!"".equals(str3)) {
/* 138 */         stringBuffer.append(str3);
/*     */       }
/* 140 */       recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/* 141 */       while (recordSet.next()) {
/* 142 */         String str5 = Util.null2String(recordSet.getString("id"));
/* 143 */         String str6 = Util.null2String(recordSet.getString("memberTagName"));
/*     */         
/* 145 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 146 */         hashMap1.put("id", str5);
/* 147 */         hashMap1.put("memberTagName", str6);
/* 148 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/* 151 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 152 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 153 */     arrayList1.add(new ListHeadBean("memberTagName", "", 1, BoolAttr.TRUE));
/*     */     
/* 155 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 156 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 157 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 159 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaMemberTagBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */