/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaWorkflowService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  30 */     if (this.user == null) {
/*  31 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  32 */       return (Map)hashMap;
/*     */     } 
/*  34 */     String str1 = Util.null2String(paramMap.get("workflowname"));
/*  35 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  37 */     String str2 = " id,version,workflowname ";
/*  38 */     String str3 = " from workflow_base a ";
/*  39 */     String str4 = " where 1=1 and isvalid=1 ";
/*  40 */     if (!"".equals(str1)) {
/*  41 */       str4 = str4 + " and workflowname like '%" + str1 + "%' ";
/*     */     }
/*  43 */     str4 = str4 + " and exists (select 1 from FnaInvoiceWfInfo b where a.id = b.workflowid) ";
/*  44 */     RecordSet recordSet = new RecordSet();
/*  45 */     recordSet.executeQuery("select " + str2 + str3 + str4 + " order by a.id", new Object[0]);
/*  46 */     while (recordSet.next()) {
/*  47 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  48 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/*  49 */       String str = Util.null2String(recordSet.getString("version"));
/*  50 */       if (!"".equals(str)) {
/*  51 */         hashMap1.put("name", Util.null2String(recordSet.getString("workflowname")) + "(V" + str + ")");
/*     */       } else {
/*  53 */         hashMap1.put("name", Util.null2String(recordSet.getString("workflowname")));
/*     */       } 
/*  55 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/*  58 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  59 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  60 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(383093, this.user.getLanguage()), 1));
/*     */     
/*  62 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  63 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  64 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/*  66 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  78 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*     */     
/*  80 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  82 */     StringBuffer stringBuffer = new StringBuffer();
/*  83 */     stringBuffer.append(" select id,version,workflowname ");
/*  84 */     stringBuffer.append(" from workflow_base a ");
/*  85 */     stringBuffer.append(" where isvalid=1 and id in (").append(str).append(")");
/*  86 */     stringBuffer.append(" and exists (select 1 from FnaInvoiceWfInfo b where a.id = b.workflowid) ");
/*  87 */     stringBuffer.append(" order by id ");
/*     */     
/*  89 */     RecordSet recordSet = new RecordSet();
/*  90 */     recordSet.execute(stringBuffer.toString());
/*  91 */     while (recordSet.next()) {
/*  92 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  93 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/*  94 */       String str1 = Util.null2String(recordSet.getString("version"));
/*  95 */       if (!"".equals(str1)) {
/*  96 */         hashMap1.put("name", Util.null2String(recordSet.getString("workflowname")) + "(V" + str1 + ")");
/*     */       } else {
/*  98 */         hashMap1.put("name", Util.null2String(recordSet.getString("workflowname")));
/*     */       } 
/* 100 */       arrayList.add(hashMap1);
/*     */     } 
/* 102 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 103 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 104 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(383093, this.user.getLanguage()), 1));
/*     */     
/* 106 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 107 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 108 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 110 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaWorkflowService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */