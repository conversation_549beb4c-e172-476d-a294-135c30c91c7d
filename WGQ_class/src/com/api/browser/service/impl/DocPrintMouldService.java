/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.docs.printMould.DocMouldComInfo;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DocPrintMouldService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 26 */     String str = (String)paramMap.get("doctype");
/* 27 */     if (str != null && "".equals(str)) { str = "0"; }
/* 28 */     else if (str != null && !"".equals(str))
/* 29 */     { if (str.equals(".htm")) { str = "0"; }
/* 30 */       else if (str.equals(".doc") || str.equals(".docx")) { str = "2"; }
/* 31 */       else if (str.equals(".xls")) { str = "3"; }
/* 32 */       else if (str.equals(".wps")) { str = "4"; }
/*    */        }
/*    */     
/* 35 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 36 */     DocMouldComInfo docMouldComInfo = new DocMouldComInfo();
/* 37 */     while (docMouldComInfo.next()) {
/* 38 */       String str1 = docMouldComInfo.getDocMouldType();
/* 39 */       if (str1 == null || "".equals(str1) || Util.getIntValue(str1, 0) < 0) str1 = "0"; 
/* 40 */       if (str != null && !str1.equals(str))
/* 41 */         continue;  HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 42 */       hashMap1.put("docMouldid", docMouldComInfo.getDocMouldid());
/* 43 */       hashMap1.put("docMouldname", docMouldComInfo.getDocMouldname());
/* 44 */       arrayList.add(hashMap1);
/*    */     } 
/* 46 */     hashMap.put("datas", arrayList);
/*    */ 
/*    */     
/* 49 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 50 */     arrayList1.add((new ListHeadBean("docMouldid", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()))).setIsPrimarykey(BoolAttr.TRUE));
/* 51 */     arrayList1.add((new ListHeadBean("docMouldname", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 53 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 54 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 55 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 56 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DocPrintMouldService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */