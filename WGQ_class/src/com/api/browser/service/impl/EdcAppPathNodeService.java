/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.edc.util.DBUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class EdcAppPathNodeService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  27 */     String str1 = Util.null2String(paramMap.get("pathid"));
/*  28 */     String str2 = Util.null2String(paramMap.get("nodeid"));
/*     */     
/*  30 */     String str3 = Util.null2String(paramMap.get("templateid"));
/*  31 */     int i = Util.getIntValue((String)DBUtil.queryForObject("select type from edc_pathTemplate where id = ?", String.class, new Object[] { str3 }), 0);
/*     */     
/*  33 */     String str4 = "";
/*  34 */     String str5 = " where  id <> " + str2;
/*  35 */     if (!"".equals(str1)) {
/*  36 */       str4 = " edc_node_v ";
/*  37 */       str5 = str5 + " and pathid = " + str1;
/*     */     } else {
/*  39 */       if (i == 0) {
/*  40 */         str4 = " edc_pathTemplateNode_v ";
/*  41 */       } else if (i == 1) {
/*  42 */         str4 = " edc_pathTemplateNode ";
/*     */       } 
/*  44 */       str5 = str5 + " and templateid = " + str3;
/*     */     } 
/*  46 */     String str6 = " id,name ";
/*  47 */     String str7 = Util.null2String(paramMap.get("name"));
/*  48 */     if (!"".equals(str7)) {
/*  49 */       str5 = str5 + " and  name like '%" + str7 + "%'";
/*     */     }
/*  51 */     String str8 = " id ";
/*  52 */     String str9 = "desc";
/*  53 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  54 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  55 */     arrayList.add((new SplitTableColBean("80%", SystemEnv.getHtmlLabelName(502717, this.user.getLanguage()), "name", "name", "", 1)).setIsInputCol(BoolAttr.TRUE));
/*  56 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str4, str5, str8, "id", str9, arrayList);
/*  57 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  58 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  63 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  64 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 502717, "name", true));
/*  66 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  67 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  72 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  73 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  74 */     String str1 = Util.null2String(paramMap.get("selectids"));
/*     */     
/*  76 */     String str2 = Util.null2String(paramMap.get("pathid"));
/*  77 */     String str3 = Util.null2String(paramMap.get("nodeid"));
/*     */     
/*  79 */     String str4 = Util.null2String(paramMap.get("templateid"));
/*  80 */     int i = Util.getIntValue((String)DBUtil.queryForObject("select type from edc_pathTemplate where id = ?", String.class, new Object[] { str4 }), 0);
/*     */     
/*  82 */     String str5 = "";
/*  83 */     if (!"".equals(str2)) {
/*  84 */       str5 = " edc_node_v ";
/*     */     }
/*  86 */     else if (i == 0) {
/*  87 */       str5 = " edc_pathTemplateNode_v ";
/*  88 */     } else if (i == 1) {
/*  89 */       str5 = " edc_pathTemplateNode ";
/*     */     } 
/*     */ 
/*     */     
/*  93 */     RecordSet recordSet = new RecordSet();
/*  94 */     for (String str : str1.split(",")) {
/*  95 */       recordSet.executeQuery("select * from " + str5 + " where id = " + str, new Object[0]);
/*  96 */       while (recordSet.next()) {
/*  97 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  98 */         hashMap1.put("id", recordSet.getString("id"));
/*  99 */         hashMap1.put("name", recordSet.getString("name"));
/* 100 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/* 103 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 104 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 105 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 106 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/* 107 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 108 */     hashMap.put("datas", arrayList);
/* 109 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 114 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 115 */     String str1 = paramHttpServletRequest.getParameter("q");
/*     */     
/* 117 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("pathid"));
/* 118 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("nodeid"));
/*     */     
/* 120 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("templateid"));
/* 121 */     int i = Util.getIntValue((String)DBUtil.queryForObject("select type from edc_pathTemplate where id = ?", String.class, new Object[] { str4 }), 0);
/*     */ 
/*     */     
/* 124 */     String str5 = "";
/* 125 */     String str6 = "";
/* 126 */     if (!"".equals(str2)) {
/* 127 */       str5 = " edc_node_v ";
/* 128 */       str6 = " pathid = " + str2;
/*     */     } else {
/* 130 */       if (i == 0) {
/* 131 */         str5 = " edc_pathTemplateNode_v ";
/* 132 */       } else if (i == 1) {
/* 133 */         str5 = " edc_pathTemplateNode ";
/*     */       } 
/* 135 */       str6 = " templateid = " + str4;
/*     */     } 
/*     */     
/* 138 */     String str7 = "select id ,name from " + str5 + "  where " + str6 + " and id <> " + str3 + " and name like '%" + str1 + "%' order by id";
/*     */     
/* 140 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 141 */     RecordSet recordSet = new RecordSet();
/* 142 */     recordSet.executeQuery(str7, new Object[0]);
/* 143 */     while (recordSet.next()) {
/* 144 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 145 */       hashMap1.put("id", recordSet.getString("id"));
/* 146 */       hashMap1.put("name", recordSet.getString("name"));
/* 147 */       arrayList.add(hashMap1);
/*     */     } 
/* 149 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 150 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EdcAppPathNodeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */