/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerStatusService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  69 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  70 */     String str2 = Util.null2String(paramMap.get("fullname"));
/*  71 */     String str3 = Util.null2String(paramMap.get("description"));
/*  72 */     String str4 = Util.null2String(paramMap.get("exclude"));
/*  73 */     String str5 = " ";
/*  74 */     boolean bool = false;
/*  75 */     if (!str1.equals("") && 
/*  76 */       !bool) {
/*  77 */       bool = true;
/*  78 */       str5 = str5 + str1;
/*     */     } 
/*     */     
/*  81 */     if (!str2.equals("")) {
/*  82 */       if (!bool) {
/*  83 */         bool = true;
/*  84 */         str5 = str5 + " where fullname like '%";
/*  85 */         str5 = str5 + Util.fromScreen2(str2, this.user.getLanguage());
/*  86 */         str5 = str5 + "%'";
/*     */       } else {
/*  88 */         str5 = str5 + " and fullname like '%";
/*  89 */         str5 = str5 + Util.fromScreen2(str2, this.user.getLanguage());
/*  90 */         str5 = str5 + "%'";
/*     */       } 
/*     */     }
/*  93 */     if (!str3.equals("")) {
/*  94 */       if (!bool) {
/*  95 */         bool = true;
/*  96 */         str5 = str5 + " where description like '%";
/*  97 */         str5 = str5 + Util.fromScreen2(str3, this.user.getLanguage());
/*  98 */         str5 = str5 + "%'";
/*     */       } else {
/* 100 */         str5 = str5 + " and description like '%";
/* 101 */         str5 = str5 + Util.fromScreen2(str3, this.user.getLanguage());
/* 102 */         str5 = str5 + "%'";
/*     */       } 
/*     */     }
/* 105 */     if (!str4.equals("")) {
/* 106 */       if (!bool) {
/* 107 */         bool = true;
/* 108 */         str5 = str5 + " where id not in(" + str4 + ")";
/*     */       } else {
/* 110 */         str5 = str5 + " and id not in(" + str4 + ")";
/*     */       } 
/*     */     }
/*     */     
/* 114 */     String str6 = "id,fullname,description";
/* 115 */     String str7 = "CRM_CustomerStatus";
/* 116 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 117 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 118 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname")).setIsInputCol(BoolAttr.TRUE));
/* 119 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*     */     
/* 121 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str5, "orderkey", "id", "asc", arrayList);
/*     */     
/*     */     try {
/* 124 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 125 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 126 */     } catch (Exception exception) {
/* 127 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 130 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 131 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 136 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 137 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 138 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 139 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname", true));
/* 140 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description", false));
/* 141 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 142 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 147 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 148 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 149 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("exclude"));
/* 150 */     String str3 = " ";
/* 151 */     BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 152 */     String str4 = browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse);
/* 153 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 155 */     JSONArray jSONArray1 = JSONArray.parseArray(str4);
/* 156 */     JSONArray jSONArray2 = new JSONArray();
/* 157 */     for (byte b = 0; b < jSONArray1.size(); b++) {
/* 158 */       JSONObject jSONObject = jSONArray1.getJSONObject(b);
/* 159 */       String str = (String)jSONObject.get("id");
/* 160 */       if (!str.equals(str2)) {
/*     */ 
/*     */         
/* 163 */         String str5 = (String)jSONObject.get("name");
/* 164 */         jSONObject.put("title", str5);
/* 165 */         jSONArray2.add(jSONObject);
/*     */       } 
/* 167 */     }  hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray2.toString());
/* 168 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CustomerStatusService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */