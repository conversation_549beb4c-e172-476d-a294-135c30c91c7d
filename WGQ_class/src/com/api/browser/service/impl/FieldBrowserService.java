/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.FieldMainManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  33 */     SplitTableBean splitTableBean = new SplitTableBean();
/*  34 */     String str = Util.null2o(Util.null2String(paramMap.get("isbill")));
/*  35 */     if (str.equals("0")) {
/*  36 */       splitTableBean = getFieldBrowserSplitTable(paramMap);
/*  37 */     } else if (str.equals("1")) {
/*  38 */       splitTableBean = getFieldBrowserSplitTable4IsBill(paramMap);
/*     */     } 
/*     */     
/*  41 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  42 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getFieldBrowserSplitTable(Map<String, Object> paramMap) {
/*  52 */     String str1 = Util.null2String(paramMap.get("fieldname"));
/*  53 */     String str2 = Util.null2String(paramMap.get("description"));
/*  54 */     String str3 = Util.null2o(Util.null2String(paramMap.get("isbill")));
/*  55 */     String str4 = Util.null2String(paramMap.get("fieldid"));
/*  56 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isdetail")), 0);
/*  57 */     String str5 = Util.null2String(paramMap.get("flowattachfiled"));
/*  58 */     String str6 = Util.null2String(paramMap.get("attachMentName"));
/*  59 */     String str7 = Util.null2String(paramMap.get("isGeneratePrintAttch"));
/*     */ 
/*     */     
/*  62 */     String str8 = "where fieldhtmltype=5";
/*  63 */     if (!Strings.isNullOrEmpty(str5)) {
/*  64 */       str8 = " where fieldhtmltype=6 ";
/*     */     }
/*  66 */     if (!Strings.isNullOrEmpty(str7)) {
/*  67 */       str8 = " where fieldhtmltype=6 and type = '1' ";
/*     */     }
/*  69 */     if (!Strings.isNullOrEmpty(str2)) {
/*  70 */       str8 = str8 + " and description like '%" + str2 + "%'";
/*     */     }
/*     */     
/*  73 */     if (!Strings.isNullOrEmpty(str6)) {
/*  74 */       str8 = str8 + " and description like '%" + str6 + "%'";
/*     */     }
/*     */     
/*  77 */     if (!Strings.isNullOrEmpty(str1)) {
/*  78 */       str8 = str8 + " and fieldname like '%" + str1 + "%'";
/*     */     }
/*  80 */     if (!Strings.isNullOrEmpty(str4)) {
/*  81 */       str8 = str8 + " and id <>" + str4;
/*     */     }
/*     */     
/*  84 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */     
/*  86 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  87 */     if (Strings.isNullOrEmpty(str5)) {
/*  88 */       SplitTableColBean splitTableColBean1 = new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldname", "fieldname");
/*  89 */       arrayList.add(splitTableColBean1);
/*     */     } 
/*     */     
/*  92 */     SplitTableColBean splitTableColBean = new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "description", "description");
/*  93 */     splitTableColBean.setIsInputCol(BoolAttr.TRUE);
/*  94 */     arrayList.add(splitTableColBean);
/*     */     
/*  96 */     if (Strings.isNullOrEmpty(str5)) {
/*  97 */       String str = i + "+" + str3 + "+" + this.user.getLanguage();
/*  98 */       SplitTableColBean splitTableColBean1 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(26734, this.user.getLanguage()), "tabletype");
/*  99 */       splitTableColBean1.setTransmethod("weaver.workflow.field.FieldMainManager.getFieldTabelName");
/* 100 */       splitTableColBean1.setOtherpara(str);
/* 101 */       arrayList.add(splitTableColBean1);
/*     */     } 
/*     */     
/* 104 */     String str9 = "id, fieldname,  description";
/* 105 */     String str10 = (i == 0) ? "workflow_formdict " : "workflow_formdictdetail";
/* 106 */     String str11 = "id";
/*     */     
/* 108 */     return new SplitTableBean(str9, str10, str8, str11, "id", "asc", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getFieldBrowserSplitTable4IsBill(Map<String, Object> paramMap) {
/* 120 */     String str1 = Util.null2String(paramMap.get("fieldname"));
/* 121 */     String str2 = Util.null2String(paramMap.get("description"));
/*     */     
/* 123 */     String str3 = Util.null2String(paramMap.get("isbill"));
/* 124 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isdetail")), 0);
/* 125 */     String str4 = Util.null2String(paramMap.get("billid"));
/* 126 */     String str5 = Util.null2String(paramMap.get("fieldid"));
/* 127 */     String str6 = Util.null2String(paramMap.get("detailtable"));
/* 128 */     String str7 = Util.null2String(paramMap.get("flowattachfiled"));
/* 129 */     String str8 = Util.null2String(paramMap.get("attachMentName"));
/* 130 */     String str9 = Util.null2String(paramMap.get("isGeneratePrintAttch"));
/*     */ 
/*     */     
/* 133 */     String str10 = "id, fieldname,fieldlabel";
/*     */     
/* 135 */     String str11 = "workflow_billfield";
/* 136 */     String str12 = "id";
/*     */     
/* 138 */     String str13 = " where fieldhtmltype=5 and billid=" + str4 + " and viewtype = 0 ";
/* 139 */     if (i == 1) {
/* 140 */       str13 = " where fieldhtmltype=5 and billid=" + str4 + " and viewtype <> 0 and detailtable='" + str6 + "'";
/*     */     }
/*     */     
/* 143 */     if (!Strings.isNullOrEmpty(str2)) {
/* 144 */       str10 = "a.id as fieldid, a.fieldname,a.fieldlabel";
/* 145 */       str11 = "workflow_billfield a,htmllabelinfo b";
/*     */       
/* 147 */       str13 = " where a.fieldhtmltype=5 and a.billid=" + str4 + " and a.viewtype <> 0 and a.detailtable='" + str6 + "' and a.fieldlabel= b.indexid and b.languageid=" + this.user.getLanguage() + " and b.labelname like '%" + str2 + "%'";
/*     */     } 
/*     */     
/* 150 */     if (!Strings.isNullOrEmpty(str7)) {
/* 151 */       str13 = " where fieldhtmltype=6 and (detailtable is null or detailtable='') and billid=" + str4;
/*     */     }
/* 153 */     if (!Strings.isNullOrEmpty(str9)) {
/* 154 */       str13 = " where fieldhtmltype=6 and type = '1' and billid=" + str4;
/*     */     }
/*     */     
/* 157 */     if (!Strings.isNullOrEmpty(str8)) {
/* 158 */       str10 = " distinct t.id,t.fieldname,t.fieldlabel ";
/* 159 */       str11 = " workflow_billfield t, HtmlLabelInfo t2 ";
/* 160 */       str13 = " fieldhtmltype=6 and t.fieldlabel = t2.indexid ";
/* 161 */       str13 = str13 + " and billid =  " + str4;
/* 162 */       str13 = str13 + " and t2.languageid =  " + this.user.getLanguage();
/* 163 */       str13 = str13 + " and t2.labelname like '%" + str8 + "%'";
/*     */     } 
/*     */     
/* 166 */     if (!Strings.isNullOrEmpty(str1)) {
/* 167 */       str13 = str13 + " and fieldname like '%" + str1 + "%'";
/*     */     }
/* 169 */     if (!Strings.isNullOrEmpty(str5)) {
/* 170 */       str13 = str13 + " and id <> " + str5;
/*     */     }
/*     */     
/* 173 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */     
/* 175 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */     
/* 177 */     if (Strings.isNullOrEmpty(str7)) {
/* 178 */       SplitTableColBean splitTableColBean1 = new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldname", "fieldname");
/* 179 */       arrayList.add(splitTableColBean1);
/*     */     } 
/*     */     
/* 182 */     SplitTableColBean splitTableColBean = new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "fieldlabel", "fieldlabel", "weaver.workflow.field.FieldMainManager.getFieldLabel", String.valueOf(this.user.getLanguage()));
/* 183 */     splitTableColBean.setIsInputCol(BoolAttr.TRUE);
/* 184 */     arrayList.add(splitTableColBean);
/*     */     
/* 186 */     if (Strings.isNullOrEmpty(str7)) {
/* 187 */       String str = i + "+" + str3 + "+" + this.user.getLanguage() + "+column:id+" + str4;
/* 188 */       arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(26734, this.user.getLanguage()), "tabletype", "tabletype", "weaver.workflow.field.FieldMainManager.getFieldTabelName", str));
/*     */     } 
/*     */ 
/*     */     
/* 192 */     return new SplitTableBean(str10, str11, str13, str12, "id", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 199 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 200 */     String str = Util.null2String(paramMap.get("flowattachfiled"));
/* 201 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 202 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */ 
/*     */     
/* 205 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 206 */     if (!Strings.isNullOrEmpty(str)) {
/* 207 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 685, "attachMentName");
/* 208 */       searchConditionItem.setIsQuickSearch(Boolean.TRUE.booleanValue());
/* 209 */       searchConditionItem.setFieldcol(14);
/* 210 */       searchConditionItem.setLabelcol(8);
/* 211 */       arrayList.add(searchConditionItem);
/*     */     } else {
/* 213 */       SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 124937, "fieldname");
/* 214 */       searchConditionItem1.setIsQuickSearch(Boolean.TRUE.booleanValue());
/* 215 */       searchConditionItem1.setFieldcol(14);
/* 216 */       searchConditionItem1.setLabelcol(8);
/*     */       
/* 218 */       SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 685, "description");
/* 219 */       searchConditionItem2.setFieldcol(14);
/* 220 */       searchConditionItem2.setLabelcol(8);
/*     */       
/* 222 */       arrayList.add(searchConditionItem1);
/* 223 */       arrayList.add(searchConditionItem2);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 228 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 233 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 234 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 235 */     String str2 = Util.null2String(paramMap.get("isbill"));
/* 236 */     String str3 = Util.null2String(paramMap.get("billid"));
/* 237 */     String str4 = Util.null2String(paramMap.get("flowattachfiled"));
/* 238 */     if ("".equals(str1)) return (Map)hashMap; 
/* 239 */     FieldMainManager fieldMainManager = new FieldMainManager();
/* 240 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 241 */     RecordSet recordSet = new RecordSet();
/* 242 */     String str5 = "";
/* 243 */     if ("1".equals(str2)) {
/* 244 */       str5 = "select id , fieldname,fieldlabel from workflow_billfield where billid=" + str3 + " and id in (" + str1 + ")";
/*     */     } else {
/* 246 */       str5 = "select id , fieldname,description fieldlabel from workflow_formdict where id in (" + str1 + ")";
/*     */     } 
/* 248 */     if (!Strings.isNullOrEmpty(str4)) {
/* 249 */       str5 = str5 + "  and fieldhtmltype = 6 ";
/*     */     }
/* 251 */     recordSet.executeQuery(str5, new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 258 */     while (recordSet.next()) {
/* 259 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 260 */       hashMap1.put("id", recordSet.getString("id"));
/* 261 */       if ("1".equals(str2)) {
/* 262 */         hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(Util.getIntValue(recordSet.getString("fieldlabel")), this.user.getLanguage()));
/*     */       } else {
/* 264 */         hashMap1.put("fieldlabel", recordSet.getString("fieldlabel"));
/*     */       } 
/*     */ 
/*     */       
/* 268 */       arrayList.add(hashMap1);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 275 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 276 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 277 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FieldBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */