/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.crm.Maint.CustomerTypeComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"type\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"manager\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) {
/*  66 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  67 */     boolean bool = CustomerFullSearchUtil.isUseFullSearch(this.user.getLanguage() + "");
/*  68 */     boolean bool1 = false;
/*  69 */     if (this.user.getUID() == 1 && "1".equals(this.user.getLogintype())) {
/*  70 */       bool1 = true;
/*     */     }
/*  72 */     if (bool && !bool1)
/*     */     {
/*  74 */       return CustomerFullSearchUtil.getCustomerTableStr(paramMap, this.user);
/*     */     }
/*  76 */     String str1 = Util.null2String(paramMap.get("name"));
/*  77 */     String str2 = Util.null2String(paramMap.get("crmcode"));
/*  78 */     String str3 = Util.null2String(paramMap.get("type"));
/*  79 */     String str4 = Util.null2String(paramMap.get("City"));
/*  80 */     String str5 = Util.null2String(paramMap.get("country1"));
/*  81 */     String str6 = Util.null2String(paramMap.get("departmentid"));
/*  82 */     String str7 = Util.null2String(paramMap.get("sqlwhere"));
/*  83 */     String str8 = Util.null2String(paramMap.get("crmManager"));
/*  84 */     String str9 = Util.null2String(paramMap.get("sectorInfo"));
/*  85 */     String str10 = Util.null2String(paramMap.get("customerStatus"));
/*  86 */     String str11 = Util.null2String(paramMap.get("customerDesc"));
/*  87 */     String str12 = Util.null2String(paramMap.get("customerSize"));
/*  88 */     String str13 = Util.null2String(paramMap.get("sharelevel"));
/*  89 */     String str14 = Util.null2String(paramMap.get("seasflag"));
/*     */     
/*  91 */     if (!"".equals(str7)) {
/*  92 */       str7 = str7.replace("where", " and");
/*     */     }
/*  94 */     if (!str1.equals("")) {
/*  95 */       str7 = str7 + " and t1.name like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */     }
/*  97 */     if (!str2.equals("")) {
/*  98 */       str7 = str7 + " and t1.crmcode like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */     }
/* 100 */     if (!str3.equals(""))
/* 101 */       if (str3.indexOf(',') > -1) {
/* 102 */         str7 = str7 + " and t1.type in" + str3;
/*     */       } else {
/* 104 */         str7 = str7 + " and t1.type = " + str3;
/*     */       }  
/* 106 */     if (!str4.equals("")) {
/* 107 */       str7 = str7 + " and t1.city = " + str4;
/*     */     }
/* 109 */     if (!str5.equals("")) {
/* 110 */       str7 = str7 + " and t1.country = " + str5;
/*     */     }
/* 112 */     if (!str6.equals("")) {
/* 113 */       str7 = str7 + " and t1.department =" + str6 + " ";
/*     */     }
/* 115 */     if (!str8.equals("")) {
/* 116 */       str7 = str7 + " and t1.manager =" + str8 + " ";
/*     */     }
/*     */     
/* 119 */     if (!str9.equals("")) {
/* 120 */       str7 = str7 + " and t1.sector = " + str9;
/*     */     }
/* 122 */     if (!str10.equals("")) {
/* 123 */       str7 = str7 + " and t1.status = " + str10;
/*     */     }
/* 125 */     if (!str11.equals("")) {
/* 126 */       str7 = str7 + " and t1.description = " + str11;
/*     */     }
/* 128 */     if (!str12.equals("")) {
/* 129 */       str7 = str7 + " and t1.size_n = " + str12;
/*     */     }
/*     */     
/* 132 */     if (str14.equals("0")) {
/* 133 */       str7 = str7 + " and (seasFlag is null or seasFlag = 3) ";
/* 134 */     } else if (str14.equals("1")) {
/* 135 */       str7 = str7 + " and (seasFlag = 1 or seasFlag = 2) ";
/*     */     } 
/*     */     
/* 138 */     str7 = str7 + " and t1.id != 0 ";
/* 139 */     CrmShareBase crmShareBase = new CrmShareBase();
/* 140 */     String str15 = "";
/* 141 */     if ("2".equals(str13)) {
/* 142 */       str15 = crmShareBase.getTempTable3("" + this.user.getUID());
/*     */     } else {
/* 144 */       str15 = crmShareBase.getTempTable("" + this.user.getUID());
/*     */     } 
/*     */     
/* 147 */     String str16 = "t1.id , t1.name , t1.manager , t1.status ,t1.type";
/*     */     
/* 149 */     String str17 = " CRM_CustomerInfo t1";
/* 150 */     if (this.user.getLogintype().equals("1")) {
/* 151 */       str17 = str17 + " left join " + str15 + " t2 on t1.id = t2.relateditemid ";
/* 152 */       str7 = " where t1.deleted <> 1 and t1.id = t2.relateditemid " + str7;
/*     */     } else {
/* 154 */       str7 = " where t1.deleted <> 1 and t1.agent=" + this.user.getUID() + str7;
/*     */     } 
/* 156 */     str7 = Util.toHtmlForSplitPage(str7);
/* 157 */     str7 = SqlUtils.replaceFirstAnd(str7);
/* 158 */     String str18 = "t1.id";
/*     */     
/* 160 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 161 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */     
/* 163 */     SplitTableColBean splitTableColBean = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1268, this.user.getLanguage()), "name", "name", 1);
/* 164 */     splitTableColBean.setIsInputCol(BoolAttr.TRUE);
/* 165 */     splitTableColBean.setTablename("CRM_CustomerInfo");
/* 166 */     arrayList.add(splitTableColBean);
/*     */     
/* 168 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "type", "type", "weaver.crm.Maint.CustomerTypeComInfo.getCustomerTypename"));
/* 169 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(1278, this.user.getLanguage()), "manager", "manager", "weaver.hrm.resource.ResourceComInfo.getResourcename", 0));
/*     */ 
/*     */     
/* 172 */     SplitTableBean splitTableBean = new SplitTableBean(str16, str17, str7, str18, "t1.id", arrayList);
/* 173 */     (new BaseBean()).writeLog("oracle中介结构sql:select" + str16 + " from " + str17 + " where " + str7 + "");
/*     */     try {
/* 175 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 176 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"type\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"manager\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 177 */     } catch (Exception exception) {
/* 178 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 181 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 182 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getCustomerData(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 186 */     return CustomerFullSearchUtil.getCustomerData(paramUser, paramMap, paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public Map<String, Object> getCustomerDataE8(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 190 */     return CustomerFullSearchUtil.getCustomerDataE8(paramUser, paramMap, paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 195 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 196 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 197 */     ConditionFactory conditionFactory = new ConditionFactory(this.user, "18".equals(this.browserType));
/* 198 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 1268, "name", true);
/* 199 */     if (!"".equals(paramMap.get("name")))
/*     */     {
/* 201 */       searchConditionItem.setValue(paramMap.get("name"));
/*     */     }
/* 203 */     arrayList.add(searchConditionItem);
/* 204 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 17080, "crmcode"));
/* 205 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 63, "type", "60"));
/* 206 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 602, "customerStatus", "264", 15078));
/* 207 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 377, "country1", "258", 377));
/* 208 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 493, "City", "58"));
/* 209 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 1278, "crmManager", "1"));
/* 210 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 130432, "departmentid", "4"));
/* 211 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 433, "customerDesc", "61"));
/* 212 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 576, "customerSize", "62"));
/* 213 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 575, "sectorInfo", "63"));
/* 214 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 215 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 220 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 221 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 222 */     if ("".equals(str)) return (Map)hashMap; 
/* 223 */     RecordSet recordSet = new RecordSet();
/* 224 */     recordSet.executeSql("select t1.id , t1.name , t1.manager , t1.status ,t1.type from CRM_CustomerInfo t1 where t1.id in (" + str + ")");
/* 225 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 226 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 227 */     CustomerTypeComInfo customerTypeComInfo = new CustomerTypeComInfo();
/* 228 */     while (recordSet.next()) {
/* 229 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 230 */       hashMap1.put("id", recordSet.getString("id"));
/* 231 */       hashMap1.put("name", recordSet.getString("name"));
/* 232 */       hashMap1.put("type", customerTypeComInfo.getCustomerTypename(recordSet.getString("type")));
/* 233 */       hashMap1.put("manager", resourceComInfo.getLastname(recordSet.getString("manager")));
/*     */       
/* 235 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 238 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 239 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 240 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(1268, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 241 */     arrayList1.add(new ListHeadBean("type", SystemEnv.getHtmlLabelName(63, this.user.getLanguage())));
/* 242 */     arrayList1.add(new ListHeadBean("manager", SystemEnv.getHtmlLabelName(1278, this.user.getLanguage())));
/*     */     
/* 244 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 245 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 246 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 247 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 252 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 253 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 254 */     CustomerTypeComInfo customerTypeComInfo = new CustomerTypeComInfo();
/* 255 */     boolean bool = CustomerFullSearchUtil.isUseFullSearch(this.user.getLanguage() + "");
/* 256 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 257 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("sharelevel"));
/* 258 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("type"));
/* 259 */     boolean bool1 = false;
/* 260 */     if (this.user.getUID() == 1 && "1".equals(this.user.getLogintype())) {
/* 261 */       bool1 = true;
/*     */     }
/*     */     
/* 264 */     if (bool && !bool1) {
/* 265 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 266 */       hashMap1.put("name", str1);
/* 267 */       hashMap1.put("sharelevel", str2);
/* 268 */       if (!"".equals(str3)) {
/* 269 */         hashMap1.put("type", str3);
/*     */       }
/* 271 */       Map<String, Object> map = CustomerFullSearchUtil.getCustomerData(this.user, (Map)hashMap1, 1, 100);
/* 272 */       List<Map> list = (List)map.get("dataAll");
/* 273 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 274 */       String str = "";
/* 275 */       for (byte b = 0; b < list.size(); b++) {
/* 276 */         Map map1 = list.get(b);
/* 277 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 278 */         String str4 = (String)map1.get("id");
/* 279 */         String str5 = (String)map1.get("name");
/* 280 */         String str6 = customerTypeComInfo.getCustomerTypename((String)map1.get("type"));
/* 281 */         String str7 = resourceComInfo.getLastname((String)map1.get("manager"));
/* 282 */         hashMap2.put("id", str4);
/* 283 */         hashMap2.put("name", str5);
/* 284 */         if ("".equals(str6) && !"".equals(str7)) {
/* 285 */           str = str4 + "&nbsp;|&nbsp;" + str5 + "&nbsp;|&nbsp;" + str7;
/*     */         }
/* 287 */         if (!"".equals(str6) && "".equals(str7)) {
/* 288 */           str = str4 + "&nbsp;|&nbsp;" + str5 + "&nbsp;|&nbsp;" + str6;
/*     */         }
/* 290 */         if (!"".equals(str6) && !"".equals(str7)) {
/* 291 */           str = str4 + "&nbsp;|&nbsp;" + str5 + "&nbsp;|&nbsp;" + str6 + "&nbsp;|&nbsp;" + str7;
/*     */         }
/* 293 */         if ("".equals(str6) && "".equals(str7)) {
/* 294 */           str = str4 + "&nbsp;|&nbsp;" + str5;
/*     */         }
/* 296 */         hashMap2.put("title", str);
/* 297 */         arrayList.add(hashMap2);
/*     */       } 
/* 299 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*     */     } else {
/* 301 */       BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 302 */       String str4 = browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse);
/* 303 */       StringBuffer stringBuffer = new StringBuffer();
/* 304 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 306 */       if ("2".equals(str2)) {
/* 307 */         CrmShareBase crmShareBase = new CrmShareBase();
/* 308 */         String str = crmShareBase.getTempTable3("" + this.user.getUID());
/* 309 */         recordSet.executeQuery("select t.relateditemid from " + str + " t", new Object[0]);
/* 310 */         while (recordSet.next()) {
/* 311 */           stringBuffer.append(recordSet.getString("relateditemid")).append(",");
/*     */         }
/* 313 */         if (stringBuffer.indexOf(",") > 0) {
/* 314 */           stringBuffer.deleteCharAt(stringBuffer.length() - 1);
/*     */         }
/*     */       } 
/* 317 */       JSONArray jSONArray1 = JSONArray.parseArray(str4);
/* 318 */       String str5 = "";
/* 319 */       JSONArray jSONArray2 = new JSONArray();
/* 320 */       for (byte b = 0; b < jSONArray1.size(); b++) {
/* 321 */         JSONObject jSONObject = jSONArray1.getJSONObject(b);
/* 322 */         String str6 = (String)jSONObject.get("id");
/* 323 */         String str7 = (String)jSONObject.get("name");
/* 324 */         String str8 = "select * from Crm_Customerinfo where id=? and deleted =0";
/* 325 */         if (stringBuffer.length() > 0) {
/* 326 */           str8 = str8 + " and id in(" + stringBuffer.toString() + ") ";
/*     */         }
/* 328 */         recordSet.executeQuery(str8, new Object[] { str6 });
/* 329 */         if (recordSet.next()) {
/* 330 */           String str9 = customerTypeComInfo.getCustomerTypename(recordSet.getString("type"));
/* 331 */           String str10 = resourceComInfo.getLastname(recordSet.getString("manager"));
/* 332 */           if ("".equals(str9) && !"".equals(str10)) {
/* 333 */             str5 = str6 + "&nbsp;|&nbsp;" + str7 + "&nbsp;|&nbsp;" + str10;
/*     */           }
/* 335 */           if (!"".equals(str9) && "".equals(str10)) {
/* 336 */             str5 = str6 + "&nbsp;|&nbsp;" + str7 + "&nbsp;|&nbsp;" + str9;
/*     */           }
/* 338 */           if (!"".equals(str9) && !"".equals(str10)) {
/* 339 */             str5 = str6 + "&nbsp;|&nbsp;" + str7 + "&nbsp;|&nbsp;" + str9 + "&nbsp;|&nbsp;" + str10;
/*     */           }
/* 341 */           if ("".equals(str9) && "".equals(str10)) {
/* 342 */             str5 = str6 + "&nbsp;|&nbsp;" + str7;
/*     */           }
/*     */           
/* 345 */           jSONObject.put("title", str5);
/* 346 */           jSONArray2.add(jSONObject);
/*     */         } 
/*     */       } 
/* 349 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray2.toString());
/*     */     } 
/* 351 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CustomerBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */