/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ExternalDataBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 27 */     String str1 = " where 1=1 and showclass='2' ";
/* 28 */     String str2 = "a.id,a.name,a.showname,' ' as nullcolumn";
/* 29 */     String str3 = " datashowset a";
/*    */     
/* 31 */     String str4 = "a.id";
/* 32 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 33 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 34 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 35 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "showname", "showname"));
/* 36 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str2, str3, str1, str4, "a.id", arrayList)));
/* 37 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ExternalDataBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */