/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class MsgStateBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  24 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  25 */     String str1 = Util.null2String(paramMap.get("name"));
/*  26 */     int i = Util.getIntValue(Util.null2String(paramMap.get("stateCode")));
/*     */     
/*  28 */     String str2 = "id,statename as name,statecode";
/*  29 */     String str3 = "ECOLOGY_MESSAGE_STATE";
/*  30 */     String str4 = " 1=1 ";
/*     */     
/*  32 */     if (StringUtils.isNotBlank(str1)) {
/*  33 */       str4 = str4 + " and statename like '%" + str1 + "%'";
/*     */     }
/*  35 */     if (-1 != i) {
/*  36 */       str4 = str4 + " and statecode =" + i;
/*     */     }
/*     */     
/*  39 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  40 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  41 */     SplitTableColBean splitTableColBean1 = (new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(509550, this.user.getLanguage()), "name", "name", "weaver.general.Util.formatMultiLang", String.valueOf(this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE);
/*  42 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage()), "stateCode", "stateCode");
/*  43 */     splitTableColBean1.setShowType(1);
/*  44 */     splitTableColBean2.setShowType(0);
/*  45 */     arrayList.add(splitTableColBean1);
/*  46 */     arrayList.add(splitTableColBean2);
/*     */     
/*  48 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, "id", "id", "desc", arrayList);
/*  49 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  51 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  59 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  60 */     RecordSet recordSet = new RecordSet();
/*  61 */     String str2 = "select id,statename,statecode from ECOLOGY_MESSAGE_STATE where statename like '%" + str1 + "%'";
/*  62 */     recordSet.executeQuery(str2, new Object[0]);
/*  63 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  64 */     while (recordSet.next()) {
/*  65 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  66 */       hashMap1.put("id", recordSet.getString("id"));
/*  67 */       hashMap1.put("stateCode", recordSet.getString("statecode"));
/*  68 */       hashMap1.put("name", Util.formatMultiLang(recordSet.getString("statename"), String.valueOf(this.user.getLanguage())));
/*     */ 
/*     */       
/*  71 */       arrayList.add(hashMap1);
/*     */     } 
/*  73 */     hashMap.put("datas", arrayList);
/*  74 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  81 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  83 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  84 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  85 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 509550, "name", true));
/*  86 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "stateCode"));
/*     */ 
/*     */ 
/*     */     
/*  90 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  91 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  97 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  98 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  99 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 100 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 101 */     String str2 = Util.null2String(paramMap.get("name"));
/* 102 */     RecordSet recordSet = new RecordSet();
/* 103 */     String str3 = "select id,statename,statecode from ECOLOGY_MESSAGE_STATE where id in(" + str1 + ")";
/* 104 */     if (StringUtils.isNotBlank(str2)) {
/* 105 */       str3 = str3 + " and statename like '%" + str2 + "%'";
/*     */     }
/* 107 */     recordSet.executeQuery(str3, new Object[0]);
/* 108 */     while (recordSet.next()) {
/* 109 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 110 */       String str4 = Util.null2String(recordSet.getString("id"));
/* 111 */       int i = recordSet.getInt("statecode");
/* 112 */       String str5 = Util.formatMultiLang(recordSet.getString("statename"), String.valueOf(this.user.getLanguage()));
/* 113 */       hashMap1.put("id", str4);
/* 114 */       hashMap1.put("stateCode", Integer.valueOf(i));
/* 115 */       hashMap1.put("name", str5);
/* 116 */       arrayList.add(hashMap1);
/*     */     } 
/* 118 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 119 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 120 */     arrayList1.add(new ListHeadBean("stateCode", "", 0, BoolAttr.TRUE));
/* 121 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/*     */     
/* 123 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 124 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 125 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 126 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MsgStateBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */