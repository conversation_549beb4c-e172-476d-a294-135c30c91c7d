/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.crm.util.CrmConstant;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.crm.util.CRMLog;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.fullsearch.util.RmiConfig;
/*     */ import weaver.fullsearch.util.SearchBrowserUtils;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class CustomerFullSearchUtil
/*     */ {
/*     */   public static Map<String, Object> getCustomerTableStr(Map<String, Object> paramMap, User paramUser) {
/*  28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  29 */     String str = mapToSpitParam(paramMap);
/*     */     
/*  31 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  32 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  33 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1268, paramUser.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/*  34 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()), "type", "type", "weaver.crm.Maint.CustomerTypeComInfo.getCustomerTypename"));
/*  35 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(1278, paramUser.getLanguage()), "manager", "manager", "weaver.hrm.resource.ResourceComInfo.getResourcename", 2));
/*     */     
/*  37 */     SplitTableBean splitTableBean = new SplitTableBean();
/*  38 */     splitTableBean.setBackfields("*");
/*  39 */     splitTableBean.setSqlform("temp");
/*  40 */     splitTableBean.setSqlprimarykey("id");
/*  41 */     splitTableBean.setSqlorderby("id");
/*  42 */     splitTableBean.setSqlsortway("desc");
/*  43 */     splitTableBean.setSqlwhere("");
/*  44 */     splitTableBean.setPageBySelf("true");
/*  45 */     splitTableBean.setCols(arrayList);
/*  46 */     splitTableBean.setDatasource("com.api.browser.service.impl.CustomerBrowserService.getCustomerData");
/*  47 */     splitTableBean.setSourceparams(Util.toHtmlForSplitPage(str));
/*     */     
/*     */     try {
/*  50 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*  51 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"type\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"manager\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  52 */     } catch (Exception exception) {
/*  53 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  56 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  58 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public static Map<String, Object> getCustomerTableStr1(Map<String, Object> paramMap, User paramUser) {
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  63 */     String str1 = mapToSpitParam(paramMap);
/*     */     
/*  65 */     String str2 = Util.fromScreen2(Util.null2String(paramMap.get("keyWordName")).trim(), paramUser.getLanguage());
/*  66 */     String str3 = Util.fromScreen2(Util.null2String(paramMap.get("keyWordValue")).trim(), paramUser.getLanguage());
/*  67 */     String str4 = Util.null2s(Util.null2String(paramMap.get("ismobile")).trim(), "0");
/*     */     
/*  69 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  70 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  71 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(1268, paramUser.getLanguage()), "name", "name", "com.api.crm.util.CrmSPATransMethod.getCustomerNameRepeatCommonPro", "column:id+" + str2 + "+column:" + str3 + "+" + str4, 1)).setIsInputCol(BoolAttr.TRUE));
/*  72 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()), "type", "type", "weaver.crm.Maint.CustomerTypeComInfo.getCustomerTypename"));
/*  73 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(722, paramUser.getLanguage()), "createdate", "createdate", 0));
/*  74 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(493, paramUser.getLanguage()), "city", "city", "weaver.hrm.city.CityComInfo.getCityname", 0));
/*  75 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(1278, paramUser.getLanguage()), "manager", "manager", "com.api.crm.util.CrmSPATransMethod.getHrmNameLink", 0));
/*     */     
/*  77 */     SplitTableBean splitTableBean = new SplitTableBean();
/*  78 */     splitTableBean.setBackfields("*");
/*  79 */     splitTableBean.setSqlform("temp");
/*  80 */     splitTableBean.setSqlprimarykey("id");
/*  81 */     splitTableBean.setSqlorderby(" LEN(" + str3 + ") - LEN('" + str2 + "') ");
/*  82 */     splitTableBean.setSqlsortway("desc");
/*  83 */     splitTableBean.setSqlwhere("");
/*  84 */     splitTableBean.setPageBySelf("true");
/*  85 */     splitTableBean.setCols(arrayList);
/*  86 */     splitTableBean.setDatasource("com.api.browser.service.impl.CustomerBrowserService.getCustomerData1");
/*  87 */     splitTableBean.setSourceparams(Util.toHtmlForSplitPage(str1));
/*  88 */     splitTableBean.setPagesize("50");
/*     */     
/*     */     try {
/*  91 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*  92 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"type\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"manager\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  93 */     } catch (Exception exception) {
/*  94 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  97 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  98 */     hashMap.put(CrmConstant.CRM_RESULT_STATUS, "success");
/*  99 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getCustomerData(User paramUser, Map<String, String> paramMap, int paramInt1, int paramInt2) {
/*     */     try {
/* 106 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 107 */       linkedHashMap.put("ID", "false");
/*     */ 
/*     */       
/* 110 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 112 */       hashMap1.put("page", Integer.valueOf(paramInt1));
/* 113 */       hashMap1.put("pageSize", Integer.valueOf(paramInt2));
/* 114 */       hashMap1.put("schemaType", "CRM");
/*     */       
/* 116 */       String str1 = Util.null2String(paramMap.get("name"), "");
/*     */       
/* 118 */       String str2 = Util.null2String(paramMap.get("crmcode"), "");
/*     */       
/* 120 */       String str3 = Util.null2String(paramMap.get("type"), "");
/*     */       
/* 122 */       String str4 = Util.null2String(paramMap.get("City"), "");
/*     */       
/* 124 */       String str5 = Util.null2String(paramMap.get("country1"), "");
/*     */       
/* 126 */       String str6 = Util.null2String(paramMap.get("departmentid"), "");
/*     */       
/* 128 */       String str7 = Util.null2String(paramMap.get("crmManager"), "");
/*     */       
/* 130 */       String str8 = Util.null2String(paramMap.get("sectorInfo"), "");
/*     */       
/* 132 */       String str9 = Util.null2String(paramMap.get("customerStatus"), "");
/*     */       
/* 134 */       String str10 = Util.null2String(paramMap.get("customerDesc"), "");
/*     */       
/* 136 */       String str11 = Util.null2String(paramMap.get("customerSize"), "");
/*     */       
/* 138 */       String str12 = Util.null2String(paramMap.get("phone"), "");
/*     */       
/* 140 */       String str13 = Util.null2String(paramMap.get("address1"), "");
/*     */       
/* 142 */       String str14 = Util.null2String(paramMap.get("email"), "");
/*     */       
/* 144 */       String str15 = Util.null2String(paramMap.get("website"), "");
/*     */ 
/*     */       
/* 147 */       String str16 = Util.null2String(paramMap.get("sharelevel"));
/* 148 */       if (str16.equals("2"))
/*     */       {
/* 150 */         hashMap1.put("sharelevel", "1");
/*     */       }
/*     */       
/* 153 */       if (!str1.isEmpty()) {
/* 154 */         hashMap1.put("showTitle", str1);
/*     */       }
/* 156 */       if (!str2.isEmpty()) {
/* 157 */         hashMap1.put("crmcode", str2);
/*     */       }
/* 159 */       if (!str3.isEmpty()) {
/* 160 */         hashMap1.put("TYPE", str3.replaceAll("\\(", "").replaceAll("\\)", ""));
/*     */       }
/* 162 */       if (!str4.isEmpty()) {
/* 163 */         hashMap1.put("city", str4);
/*     */       }
/* 165 */       if (!str5.isEmpty()) {
/* 166 */         hashMap1.put("country", str5);
/*     */       }
/* 168 */       if (!str6.isEmpty()) {
/* 169 */         hashMap1.put("DEPARTMENT", str6);
/*     */       }
/* 171 */       if (!str7.isEmpty()) {
/* 172 */         hashMap1.put("crmmanager", str7);
/*     */       }
/* 174 */       if (!str8.isEmpty()) {
/* 175 */         hashMap1.put("sectorInfo", str8);
/*     */       }
/* 177 */       if (!str9.isEmpty()) {
/* 178 */         hashMap1.put("STATUS", str9);
/*     */       }
/* 180 */       if (!str10.isEmpty()) {
/* 181 */         hashMap1.put("DESCRIPTION", str10);
/*     */       }
/* 183 */       if (!str11.isEmpty()) {
/* 184 */         hashMap1.put("SIZE_N", str11);
/*     */       }
/* 186 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */       
/* 188 */       if (!str13.isEmpty()) {
/* 189 */         hashMap2.put("searchAddress1", str13);
/*     */       }
/*     */       
/* 192 */       if (!str12.isEmpty()) {
/* 193 */         hashMap2.put("phone", str12);
/*     */       }
/*     */ 
/*     */       
/* 197 */       if (!str14.isEmpty()) {
/* 198 */         hashMap2.put("email", str14);
/*     */       }
/*     */ 
/*     */       
/* 202 */       if (!str15.isEmpty()) {
/* 203 */         hashMap2.put("website", str15);
/*     */       }
/*     */ 
/*     */       
/* 207 */       hashMap1.put("wildcardMap", hashMap2);
/*     */       
/* 209 */       hashMap1.put("loginid", paramUser.getLoginid());
/* 210 */       hashMap1.put("currentUserObject", paramUser);
/* 211 */       Map map = SearchBrowserUtils.quickSearch(hashMap1, linkedHashMap, null);
/*     */       
/* 213 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */       
/* 215 */       List<Map> list = (List)map.get("result");
/* 216 */       list = (list == null) ? new ArrayList() : list;
/*     */       
/* 218 */       int i = Util.getIntValue(Util.null2String(map.get("count")), 0);
/* 219 */       for (byte b = 0; b < list.size(); b++) {
/* 220 */         Map map1 = list.get(b);
/* 221 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 222 */         hashMap.put("id", map1.get("ID"));
/* 223 */         hashMap.put("name", map1.get("title"));
/* 224 */         hashMap.put("type", map1.get("TYPE"));
/* 225 */         hashMap.put("manager", map1.get("crmmanager"));
/* 226 */         hashMap.put("city", map1.get("city"));
/* 227 */         hashMap.put("createdate", map1.get("CREATEDATE"));
/* 228 */         arrayList.add(hashMap);
/*     */       } 
/*     */       
/* 231 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 232 */       hashMap3.put("dataAll", arrayList);
/* 233 */       hashMap3.put("recordCount", Integer.valueOf(i));
/* 234 */       return (Map)hashMap3;
/* 235 */     } catch (Exception exception) {
/* 236 */       exception.printStackTrace();
/*     */       
/* 238 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static Map<String, Object> getCustomerData(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 243 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("min"), 0);
/* 244 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("current"), 0);
/* 245 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("pageSize"), 10);
/*     */     
/* 247 */     String str = Util.null2String(paramHttpServletRequest.getParameter("ismobile"));
/* 248 */     if ("1".equals(str)) {
/* 249 */       i = j;
/* 250 */       i = (i <= 0) ? 1 : i;
/*     */     } else {
/* 252 */       i = i / k + 1;
/*     */     } 
/* 254 */     return getCustomerData(paramUser, paramMap, i, k);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getCustomerDataForList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 259 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("current"), 0);
/* 260 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getAttribute("pageSize")), 10);
/* 261 */     CRMLog cRMLog = new CRMLog();
/* 262 */     cRMLog.writeLog("current:" + i);
/* 263 */     cRMLog.writeLog("pageSize:" + j);
/* 264 */     return getCustomerData(paramUser, paramMap, i, j);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getCustomerDataE8(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 269 */     boolean bool = (Util.getIntValue(paramHttpServletRequest.getParameter("pageIndex")) == 0) ? true : Util.getIntValue(paramHttpServletRequest.getParameter("pageIndex"));
/* 270 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("pageSize"), 10);
/* 271 */     return getCustomerData(paramUser, paramMap, bool, i);
/*     */   }
/*     */   
/*     */   public static String mapToSpitParam(Map<String, Object> paramMap) {
/* 275 */     StringBuffer stringBuffer = new StringBuffer();
/* 276 */     for (String str : paramMap.keySet()) {
/* 277 */       if (!str.isEmpty()) {
/* 278 */         String str1 = Util.null2String(paramMap.get(str));
/* 279 */         str1 = processSpecialChar(Util.null2String(str1));
/* 280 */         stringBuffer.append("".equals(stringBuffer.toString()) ? (str + ":" + str1) : ("+" + str + ":" + str1));
/*     */       } 
/*     */     } 
/* 283 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String processSpecialChar(String paramString) {
/* 293 */     paramString = paramString.replaceAll("[+]", "@#add#@");
/* 294 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getFullSearchRMI() {
/* 299 */     String str = RmiConfig.getRmiUrl();
/* 300 */     return "rmi://" + str + "/search";
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean isUseFullSearch(String paramString) {
/* 305 */     return SearchBrowserUtils.quickSearchValidate("CRMSEARCH", paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CustomerFullSearchUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */