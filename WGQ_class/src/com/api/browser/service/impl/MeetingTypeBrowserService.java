/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.workflow.biz.requestForm.TestWorkflowCheckBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.DBUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.meeting.MeetingShareUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MeetingTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  41 */     int i = Util.getIntValue(Util.null2String(paramMap.get("forall")), 0);
/*  42 */     String str1 = Util.null2String(Util.null2String(paramMap.get("isInterval")));
/*  43 */     String str2 = Util.null2String(paramMap.get("fullname"));
/*  44 */     String str3 = Util.null2String(paramMap.get("wfTestStr"));
/*  45 */     String str4 = " where 1 = 1 ";
/*     */     
/*  47 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  48 */     boolean bool = manageDetachComInfo.isUseMtiManageDetach();
/*  49 */     boolean bool1 = false;
/*  50 */     if (bool) {
/*  51 */       bool1 = true;
/*     */     } else {
/*  53 */       bool1 = false;
/*     */     } 
/*     */     
/*  56 */     String str5 = Util.null2String(Util.null2String(paramMap.get("subcompanyid")));
/*  57 */     if (!"".equals(str5)) {
/*  58 */       str4 = str4 + " and a.subcompanyid in(" + str5 + ",0) ";
/*     */     }
/*  60 */     if (!str2.equals("")) {
/*  61 */       str4 = str4 + " and a.name like '%";
/*  62 */       str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  63 */       str4 = str4 + "%'";
/*     */     } 
/*     */     
/*  66 */     if (i != 1) {
/*  67 */       TestWorkflowCheckBiz testWorkflowCheckBiz = new TestWorkflowCheckBiz();
/*  68 */       String[] arrayOfString = testWorkflowCheckBiz.decodeTestStr(str3);
/*  69 */       User user = new User();
/*  70 */       if (arrayOfString.length == 3 && "true".equals(arrayOfString[0])) {
/*  71 */         int j = Util.getIntValue(arrayOfString[1]);
/*  72 */         int k = Util.getIntValue(arrayOfString[2], 0);
/*  73 */         user = MeetingShareUtil.generateUserObj(j, k);
/*  74 */         str4 = str4 + MeetingShareUtil.getTypeShareSql(user);
/*     */       } else {
/*  76 */         str4 = str4 + MeetingShareUtil.getTypeShareSql(this.user);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  81 */     String str6 = " a.id,a.name,a.subcompanyid,a.desc_n,a.approver,a.approver1 ";
/*  82 */     String str7 = " Meeting_Type a ";
/*  83 */     String str8 = " a.dsporder,a.name ";
/*     */     
/*  85 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  86 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  87 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(2104, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE).setShowType(1).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/*  88 */     if (bool1 == true)
/*  89 */       arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "com.api.meeting.util.MeetingTransMethod.getMeetingSubCompany")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL)); 
/*  90 */     if (str1.equals("0"))
/*  91 */       arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15057, this.user.getLanguage()), "approver", "approver", "com.api.meeting.util.MeetingTransMethod.getWfName")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL)); 
/*  92 */     if (str1.equals("1")) {
/*  93 */       arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(82441, this.user.getLanguage()), "approver1", "approver1", "com.api.meeting.util.MeetingTransMethod.getWfName")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */     }
/*  95 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str4, str8, "a.id", arrayList);
/*  96 */     splitTableBean.setSqlsortway("ASC");
/*     */     
/*  98 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 100 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig(bool1, str1)));
/* 101 */     } catch (Exception exception) {
/* 102 */       exception.printStackTrace();
/*     */     } 
/* 104 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 105 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig(int paramInt, String paramString) {
/* 113 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 114 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.name");
/* 115 */     if (paramInt == 1) {
/* 116 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.subcompanyid");
/* 117 */       if ("1".equals(paramString)) {
/* 118 */         MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row3.approver1");
/* 119 */       } else if ("0".equals(paramString)) {
/* 120 */         MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row3.approver");
/*     */       }
/*     */     
/* 123 */     } else if ("1".equals(paramString)) {
/* 124 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.approver1");
/* 125 */     } else if ("0".equals(paramString)) {
/* 126 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.approver");
/*     */     } 
/*     */     
/* 129 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 134 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 135 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 136 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 137 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 138 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 2104, "fullname", true));
/* 139 */     boolean bool = (new ManageDetachComInfo()).isUseMtiManageDetach();
/* 140 */     if (bool) {
/* 141 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, "17868", "subcompanyid", "164"));
/*     */     }
/* 143 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 149 */     String str1 = Util.null2String(paramMap.get("selectids"));
/* 150 */     String str2 = Util.null2String(Util.null2String(paramMap.get("isInterval")));
/* 151 */     if (!"".equals(str1)) {
/* 152 */       RecordSet recordSet = new RecordSet();
/* 153 */       if (str1.startsWith(",")) str1 = str1.substring(1); 
/* 154 */       if (str1.endsWith(",")) str1 = str1.substring(0, str1.length() - 1); 
/* 155 */       ArrayList arrayList1 = new ArrayList();
/* 156 */       recordSet.executeQuery("select a.id,a.name,a.subcompanyid,a.desc_n,a.approver,a.approver1 from Meeting_Type a where id in (" + DBUtil.getParamReplace(str1) + ") order by a.dsporder,a.name", new Object[] { DBUtil.trasToList(arrayList1, new Object[] { str1 }) });
/* 157 */       ArrayList arrayList2 = new ArrayList();
/* 158 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */       
/* 161 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 162 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*     */       
/* 164 */       ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/* 165 */       boolean bool = manageDetachComInfo.isUseMtiManageDetach();
/*     */       
/* 167 */       while (recordSet.next()) {
/* 168 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 169 */         hashMap1.put("id", recordSet.getString("id"));
/* 170 */         hashMap1.put("idspan", hashMap1.get("id"));
/* 171 */         hashMap1.put("name", recordSet.getString("name"));
/* 172 */         hashMap1.put("namespan", hashMap1.get("name"));
/* 173 */         hashMap1.put("randomFieldId", recordSet.getString("id"));
/* 174 */         hashMap1.put("randomFieldIdspan", "");
/* 175 */         if (str2.equals("0")) {
/* 176 */           hashMap1.put("approver", recordSet.getString("approver"));
/* 177 */           hashMap1.put("approverspan", workflowComInfo.getWorkflowname(Util.null2String(hashMap1.get("approver"))));
/* 178 */         } else if (str2.equals("1")) {
/* 179 */           hashMap1.put("approver1", recordSet.getString("approver1"));
/* 180 */           hashMap1.put("approver1span", workflowComInfo.getWorkflowname(Util.null2String(hashMap1.get("approver1"))));
/*     */         } 
/* 182 */         if (bool) {
/* 183 */           hashMap1.put("subcompanyid", recordSet.getString("subcompanyid"));
/* 184 */           hashMap1.put("subcompanyidspan", subCompanyComInfo.getSubCompanyname(recordSet.getString("subcompanyid")));
/*     */         } 
/*     */         
/* 187 */         arrayList.add(hashMap1);
/*     */       } 
/* 189 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 190 */       hashMap.put("datas", arrayList);
/* 191 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 194 */     return new HashMap<>();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MeetingTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */