/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.weaver.integration.cominfo.IntBrowserBaseComInfo;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SAPConditionBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 32 */     IntBrowserBaseComInfo intBrowserBaseComInfo = new IntBrowserBaseComInfo();
/* 33 */     SplitTableBean splitTableBean = new SplitTableBean();
/* 34 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 36 */     String str1 = Util.null2String(paramMap.get("mark"));
/*    */     
/* 38 */     String str2 = Util.null2String(paramMap.get("conditionfield"));
/*    */     
/* 40 */     String str3 = Util.null2String(paramMap.get("conditionshowname"));
/*    */     
/* 42 */     String str4 = Util.null2String(intBrowserBaseComInfo.getId(str1));
/* 43 */     if ("".equals(str4)) {
/* 44 */       recordSet.executeQuery("select id from int_BrowserbaseInfo where mark=?", new Object[] { str1 });
/* 45 */       if (recordSet.next()) {
/* 46 */         str4 = Util.null2String(recordSet.getString("id"));
/*    */       }
/*    */     } 
/* 49 */     String str5 = " a.baseid= " + str4;
/* 50 */     if (!str2.isEmpty()) {
/* 51 */       str5 = str5 + " and a.sapfield like '%" + str2 + "%' ";
/*    */     }
/* 53 */     if (!str3.isEmpty()) {
/* 54 */       str5 = str5 + " and a.showname like '%" + str3 + "%' ";
/*    */     }
/* 56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 57 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(30671, this.user.getLanguage()), "conditiontablename", null));
/* 58 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(23481, this.user.getLanguage()), "conditionfield", null));
/* 59 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(30667, this.user.getLanguage()), "conditionshowname", null));
/* 60 */     splitTableBean.setBackfields(" a.id,b.name as conditiontablename,a.sapfield as conditionfield,a.showname as conditionshowname");
/* 61 */     splitTableBean.setSqlform(" sap_outTable a left join sap_complexname b on a.nameid=b.id ");
/* 62 */     splitTableBean.setSqlwhere(str5);
/* 63 */     splitTableBean.setSqlprimarykey("a.id");
/* 64 */     splitTableBean.setCols(arrayList);
/* 65 */     splitTableBean.setSqlorderby("a.id ");
/* 66 */     splitTableBean.setSqlsortway("ASC");
/* 67 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 68 */     hashMap = (hashMap == null) ? new HashMap<>() : hashMap;
/* 69 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 80 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 82 */       ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 83 */       ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 84 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 23481, "conditionfield", true));
/* 85 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30667, "conditionshowname"));
/* 86 */       hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 87 */       return (Map)hashMap;
/* 88 */     } catch (Exception exception) {
/* 89 */       exception.printStackTrace();
/*    */       
/* 91 */       return (Map)hashMap;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SAPConditionBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */