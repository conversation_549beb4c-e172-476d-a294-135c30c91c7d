/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import com.engine.systeminfo.dao.AppShareDao;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ public class AppShareTypeService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     if (this.user == null)
/*  30 */       return null; 
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  32 */     String str1 = "t1.id";
/*  33 */     String str2 = "id";
/*  34 */     String str3 = "asc";
/*  35 */     int i = this.user.getLanguage();
/*  36 */     AppShareDao appShareDao = new AppShareDao();
/*  37 */     String str4 = appShareDao.typeBackfields();
/*  38 */     String str5 = appShareDao.typeFromsql(i);
/*  39 */     String str6 = "1=1 ";
/*     */     
/*  41 */     String str7 = Util.null2String(paramMap.get("typename"));
/*  42 */     String str8 = Util.null2String(paramMap.get("moduleid"));
/*  43 */     if (StringUtils.isNotBlank(str7))
/*  44 */       str6 = str6 + " and t3.labelname like '%" + str7 + "%'"; 
/*  45 */     if (StringUtils.isNotBlank(str8)) {
/*  46 */       str6 = str6 + " and t1.moduleid=" + str8;
/*     */     }
/*  48 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  49 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("true", "id");
/*  50 */     SplitTableColBean splitTableColBean2 = (new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(388667, this.user.getLanguage()), "typename")).setIsInputCol(BoolAttr.TRUE);
/*  51 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()), "modulename");
/*     */     
/*  53 */     arrayList.add(splitTableColBean1);
/*  54 */     arrayList.add(splitTableColBean2);
/*  55 */     arrayList.add(splitTableColBean3);
/*     */     
/*  57 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str6, str1, str2, str3, arrayList);
/*  58 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  65 */     WeaResultMsg weaResultMsg = new WeaResultMsg(false);
/*     */     try {
/*  67 */       ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  68 */       ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/*  69 */       ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*     */       
/*  71 */       SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 388667, "typename", true);
/*  72 */       SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 33522, "moduleid", "appModule");
/*  73 */       arrayList1.add(searchConditionItem1);
/*  74 */       arrayList1.add(searchConditionItem2);
/*  75 */       arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(388790, this.user.getLanguage()), true, arrayList1));
/*  76 */       weaResultMsg.put("conditions", arrayList1);
/*  77 */       weaResultMsg.success();
/*  78 */     } catch (Exception exception) {
/*  79 */       exception.printStackTrace();
/*     */     } 
/*  81 */     return weaResultMsg.getResultMapAll();
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  88 */     RecordSet recordSet = new RecordSet();
/*  89 */     String str2 = "select t1.id,t2.labelname as typename from ecology_biz_sharetype t1 left join htmllabelinfo t2 on t1.typename=t2.indexid and t2.languageid=? where t2.labelname like ?";
/*  90 */     recordSet.executeQuery(str2, new Object[] { Integer.valueOf(this.user.getLanguage()), "%" + str1 + "%" });
/*  91 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  92 */     while (recordSet.next()) {
/*  93 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  94 */       hashMap1.put("id", recordSet.getString("id"));
/*  95 */       hashMap1.put("name", recordSet.getString("typename"));
/*  96 */       arrayList.add(hashMap1);
/*     */     } 
/*  98 */     hashMap.put("datas", arrayList);
/*     */     
/* 100 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AppShareTypeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */