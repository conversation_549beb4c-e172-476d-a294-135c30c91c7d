/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ContactLogBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"begindate\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  67 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  68 */     String str2 = Util.null2String(paramMap.get("CustomerID"));
/*  69 */     String str3 = Util.null2String(paramMap.get("name"));
/*  70 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  72 */     if (!"".equals(str1)) {
/*  73 */       str2 = str1.substring(str1.indexOf("CustomerID") + "CustomerID".length() + 1);
/*     */     }
/*     */     
/*  76 */     String str4 = String.valueOf(this.user.getUID());
/*  77 */     String str5 = this.user.getLogintype();
/*  78 */     if ("".equals(str2)) {
/*  79 */       CrmShareBase crmShareBase = new CrmShareBase();
/*  80 */       str2 = crmShareBase.getTempTable(str4);
/*     */     } else {
/*  82 */       str2 = "'" + str2 + "'";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  87 */     String str6 = "";
/*  88 */     if (!str3.equals("")) {
/*  89 */       str6 = " and (name like '%" + str3 + "%' or description like '%" + str3 + "%')";
/*     */     }
/*     */ 
/*     */     
/*  93 */     str1 = "type_n=3 and status=2 " + str6 + "AND crmid in (" + str2 + ")";
/*  94 */     String str7 = "id, begindate, begintime, description, name";
/*  95 */     String str8 = "WorkPlan";
/*  96 */     String str9 = "begindate,begintime";
/*     */     
/*  98 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  99 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 100 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(229, this.user.getLanguage()), "name", "name"));
/* 101 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(345, this.user.getLanguage()), "description", "description")).setIsInputCol(BoolAttr.TRUE));
/* 102 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(621, this.user.getLanguage()), "begindate", "begindate", "com.api.browser.service.impl.ContactLogBrowserService.getBeginDateInfo", "column:begintime"));
/*     */     
/* 104 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str8, str1, str9, "id", arrayList);
/*     */     
/*     */     try {
/* 107 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 108 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"begindate\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 109 */     } catch (Exception exception) {
/* 110 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 113 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 114 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getBeginDateInfo(String paramString1, String paramString2) {
/* 118 */     return Util.null2String(paramString1) + " " + Util.null2String(paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 133 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 135 */     BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 136 */     String str = browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/* 138 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str);
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ContactLogBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */