/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.CommonCategory;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class NetworkDiskBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  22 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  24 */     String str1 = Util.null2String(paramMap.get("categoryname"));
/*  25 */     String str2 = Util.null2String(paramMap.get("noClick"));
/*  26 */     String str3 = Util.null2String(paramMap.get("noAllDirName"));
/*     */     
/*  28 */     RecordSet recordSet = new RecordSet();
/*  29 */     recordSet.executeQuery(getSql(this.user, ""), new Object[0]);
/*     */     
/*  31 */     ArrayList<CommonCategory> arrayList = new ArrayList();
/*  32 */     ArrayList<Integer> arrayList1 = new ArrayList();
/*  33 */     int i = 0;
/*  34 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  35 */     while (recordSet.next()) {
/*  36 */       int j = recordSet.getInt("id");
/*  37 */       int k = recordSet.getInt("parentid");
/*  38 */       if (k <= 0) {
/*  39 */         i = j;
/*     */         continue;
/*     */       } 
/*  42 */       arrayList1.add(Integer.valueOf(j));
/*  43 */       CommonCategory commonCategory = new CommonCategory();
/*  44 */       commonCategory.id = j;
/*  45 */       commonCategory.parentid = k;
/*  46 */       commonCategory.name = recordSet.getString("categoryname");
/*  47 */       arrayList.add(commonCategory);
/*  48 */       hashMap2.put(Integer.valueOf(j), commonCategory);
/*     */     } 
/*     */     
/*  51 */     if (!str1.isEmpty()) {
/*  52 */       recordSet.executeQuery(getSql(this.user, str1), new Object[0]);
/*  53 */       ArrayList<Integer> arrayList2 = new ArrayList();
/*  54 */       while (recordSet.next()) {
/*  55 */         int j = recordSet.getInt("id");
/*  56 */         int k = recordSet.getInt("parentid");
/*  57 */         if (k <= 0) {
/*     */           continue;
/*     */         }
/*  60 */         arrayList2.add(Integer.valueOf(j));
/*     */       } 
/*  62 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  63 */       arrayList = new ArrayList<>();
/*  64 */       for (Integer integer : arrayList2) {
/*  65 */         if (hashMap.get(integer) != null)
/*  66 */           continue;  hashMap.put(integer, "");
/*  67 */         arrayList.add((CommonCategory)hashMap2.get(integer));
/*  68 */         int j = ((CommonCategory)hashMap2.get(integer)).parentid;
/*  69 */         while (j != i && hashMap.get(Integer.valueOf(j)) == null && hashMap2
/*  70 */           .get(Integer.valueOf(j)) != null) {
/*  71 */           hashMap.put(Integer.valueOf(j), "");
/*  72 */           arrayList.add((CommonCategory)hashMap2.get(Integer.valueOf(j)));
/*  73 */           j = ((CommonCategory)hashMap2.get(Integer.valueOf(j))).parentid;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  78 */     for (CommonCategory commonCategory : arrayList) {
/*  79 */       commonCategory.name = getPathName((Map)hashMap2, commonCategory.id, commonCategory.name);
/*     */     }
/*     */     
/*  82 */     DocCategoryService docCategoryService = new DocCategoryService();
/*  83 */     List<DocCategoryService.CategoryTreeNode> list = docCategoryService.getDocDirList(arrayList, i, arrayList1, true, true, str3);
/*  84 */     if (!str2.equals("")) {
/*  85 */       List<String> list1 = Arrays.asList(StringUtils.split(str2, ","));
/*  86 */       for (String str : list1) {
/*  87 */         docCategoryService.removeSubs(list, Util.getIntValue(str));
/*     */       }
/*     */     } 
/*  90 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*  91 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_IS_ALL, Boolean.valueOf(false));
/*  92 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*     */     
/*  94 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public String getPathName(Map<Integer, CommonCategory> paramMap, int paramInt, String paramString) {
/*  98 */     int i = ((CommonCategory)paramMap.get(Integer.valueOf(paramInt))).parentid;
/*  99 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 100 */     hashMap.put(Integer.valueOf(paramInt), "");
/* 101 */     while (paramMap.get(Integer.valueOf(i)) != null && hashMap.get(Integer.valueOf(i)) == null) {
/* 102 */       paramString = ((CommonCategory)paramMap.get(Integer.valueOf(i))).name + "/" + paramString;
/* 103 */       i = ((CommonCategory)paramMap.get(Integer.valueOf(i))).parentid;
/*     */     } 
/* 105 */     return paramString;
/*     */   }
/*     */   
/*     */   public String getSql(User paramUser, String paramString) {
/* 109 */     String str = "select id,categoryname,parentid from DocPrivateSecCategory where userid=" + paramUser.getUID();
/* 110 */     if (paramString != null && !paramString.isEmpty()) {
/* 111 */       paramString = paramString.replace("'", "''");
/* 112 */       str = str + " and (categoryname like '%" + paramString + "%' or ecology_pinyin_search like '%" + paramString + "%')";
/*     */     } 
/* 114 */     str = str + " order by id desc";
/* 115 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 124 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 125 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 126 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 130 */     if (!str.isEmpty()) {
/*     */       
/* 132 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 134 */       String str1 = str.contains(",") ? (" in (" + str + ")") : ("=" + str);
/*     */       
/* 136 */       recordSet.executeQuery("select id,categoryname from DocPrivateSecCategory where id " + str1 + " order by secorder asc,id asc", new Object[0]);
/*     */       
/* 138 */       while (recordSet.next()) {
/* 139 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 140 */         hashMap1.put("id", recordSet.getString("id"));
/* 141 */         hashMap1.put("name", recordSet.getString("categoryname"));
/*     */ 
/*     */         
/* 144 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/* 147 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 148 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 149 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/*     */ 
/*     */     
/* 152 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 153 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 154 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 155 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 160 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 161 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 162 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 163 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 24764, "categoryname", true));
/* 164 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 165 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 170 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 172 */     String str = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*     */     
/* 174 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 176 */     recordSet.executeQuery(getSql(this.user, ""), new Object[0]);
/*     */     
/* 178 */     ArrayList<CommonCategory> arrayList = new ArrayList();
/* 179 */     ArrayList<Integer> arrayList1 = new ArrayList();
/* 180 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 181 */     while (recordSet.next()) {
/* 182 */       int i = recordSet.getInt("id");
/* 183 */       int j = recordSet.getInt("parentid");
/* 184 */       if (j <= 0) {
/*     */         continue;
/*     */       }
/* 187 */       arrayList1.add(Integer.valueOf(i));
/* 188 */       CommonCategory commonCategory = new CommonCategory();
/* 189 */       commonCategory.id = i;
/* 190 */       commonCategory.parentid = j;
/* 191 */       commonCategory.name = recordSet.getString("categoryname");
/* 192 */       arrayList.add(commonCategory);
/* 193 */       hashMap2.put(Integer.valueOf(i), commonCategory);
/*     */     } 
/*     */ 
/*     */     
/* 197 */     recordSet.executeQuery(getSql(this.user, str), new Object[0]);
/* 198 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 199 */     while (recordSet.next()) {
/* 200 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 202 */       hashMap.put("id", recordSet.getString("id"));
/*     */       
/* 204 */       String str1 = recordSet.getString("categoryname");
/* 205 */       str1 = getPathName((Map)hashMap2, recordSet.getInt("id"), str1);
/* 206 */       hashMap.put("name", str1);
/* 207 */       arrayList2.add(hashMap);
/*     */     } 
/*     */     
/* 210 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList2);
/* 211 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/NetworkDiskBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */