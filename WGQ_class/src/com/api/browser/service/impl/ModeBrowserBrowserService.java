/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.cube.biz.SqlHelper;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.StringUtil;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.servicefiles.BrowserXML;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ModeBrowserBrowserService
/*     */   extends BrowserService
/*     */ {
/*  38 */   private ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  39 */   private boolean isUseFmManageDetach = this.manageDetachComInfo
/*  40 */     .isUseFmManageDetach();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  47 */     String str1 = Util.null2String(paramMap.get("comeFrom"));
/*  48 */     if (str1.equals("browserButton")) {
/*  49 */       return browserButton(paramMap);
/*     */     }
/*  51 */     String str2 = Util.null2String(paramMap.get("browsername"));
/*  52 */     String str3 = Util.null2String(paramMap.get("appid"));
/*  53 */     String str4 = " a.id,a.customname,b.treefieldname ";
/*  54 */     String str5 = " from mode_custombrowser a ,modeTreeField b ";
/*  55 */     String str6 = " where a.appid=b.id and (b.isdelete is null or b.isdelete=0 ) ";
/*     */     
/*  57 */     if (this.isUseFmManageDetach) {
/*  58 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  59 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEFORM:ALL", 0);
/*     */       
/*  61 */       if (arrayOfInt.length == 0) {
/*  62 */         str6 = str6 + " and 1=2 ";
/*     */       } else {
/*  64 */         str6 = str6 + " and " + SqlHelper.SplitSqlInCondition("b.subCompanyId", arrayOfInt);
/*     */       } 
/*     */     } 
/*  67 */     if (!str2.equals("")) {
/*  68 */       str6 = str6 + " and a.customname like '%" + str2 + "%'";
/*     */     }
/*  70 */     if (!str3.equals("")) {
/*  71 */       str6 = str6 + " and b.id = " + str3;
/*     */     }
/*  73 */     String str7 = " a.id desc";
/*  74 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  75 */     arrayList.add((new SplitTableColBean("true", "id")).setIsPrimarykey(BoolAttr.TRUE));
/*  76 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(82014, this.user.getLanguage()), "customname", "labelName"))
/*  77 */         .setIsInputCol(BoolAttr.TRUE));
/*  78 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(82186, this.user.getLanguage()), "treefieldname", "fieldname"));
/*     */     
/*  80 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str6, str7, "a.id", arrayList);
/*     */     
/*  82 */     splitTableBean.setSqlsortway("DESC");
/*  83 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  85 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  90 */     String str1 = Util.null2String(paramMap.get("comeFrom"));
/*  91 */     if (str1.equals("browserButton")) {
/*  92 */       return browserButtonConditions(paramMap);
/*     */     }
/*  94 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  95 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  96 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  97 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 82014, "browsername").setIsQuickSearch(true));
/*  98 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 18411, "formType");
/*  99 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 100 */     arrayList1.add(new SearchConditionOption("", ""));
/* 101 */     String str2 = "select * from modeTreeField where (isdelete is null or isdelete=0 ) ";
/* 102 */     RecordSet recordSet = new RecordSet();
/* 103 */     if ((new ManageDetachComInfo()).isUseFmManageDetach()) {
/* 104 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 105 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEAPP:ALL", 0);
/* 106 */       if (arrayOfInt.length == 0) {
/* 107 */         str2 = str2 + " and 1=2 ";
/*     */       } else {
/* 109 */         str2 = str2 + " and " + SqlHelper.SplitSqlInCondition("subCompanyId", arrayOfInt);
/*     */       } 
/*     */     } 
/* 112 */     str2 = str2 + " order  by showOrder asc ";
/* 113 */     recordSet.executeSql(str2);
/* 114 */     while (recordSet.next()) {
/* 115 */       String str3 = Util.null2String(recordSet.getString("id"));
/* 116 */       String str4 = Util.null2String(recordSet.getString("treeFieldName"));
/* 117 */       arrayList1.add(new SearchConditionOption(str3, str4));
/*     */     } 
/* 119 */     searchConditionItem.setOptions(arrayList1);
/* 120 */     arrayList.add(searchConditionItem);
/* 121 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 122 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, Object> browserButton(Map<String, Object> paramMap) {
/* 127 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 129 */     (new BrowserXML()).initData();
/* 130 */     String str1 = Util.null2String(paramMap.get("name"));
/* 131 */     String str2 = Util.null2String(paramMap.get("showname"));
/* 132 */     int i = Util.getIntValue(Util.null2String(paramMap.get("showtype")));
/* 133 */     String str3 = " name, showname, showtype ";
/* 134 */     String str4 = " from ( select name,showname,showtype,showclass from datashowset union select name,showname,showtype,showclass from mode_browser ) a";
/* 135 */     String str5 = " where showclass='1'";
/* 136 */     if (!str1.isEmpty()) {
/* 137 */       str5 = str5 + " and name like '%" + str1 + "%'";
/*     */     }
/* 139 */     if (!str2.isEmpty()) {
/* 140 */       str5 = str5 + " and showname like '%" + str2 + "%'";
/*     */     }
/*     */     
/* 143 */     if (i != -1) {
/* 144 */       str5 = str5 + " and showtype=" + i;
/*     */     }
/* 146 */     String str6 = " showname asc";
/* 147 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 148 */     arrayList.add(new SplitTableColBean("40%", "" + SystemEnv.getHtmlLabelName(22009, ThreadVarLanguage.getLang()) + "", "name", "name"));
/* 149 */     arrayList.add((new SplitTableColBean("30%", "" + SystemEnv.getHtmlLabelName(84, ThreadVarLanguage.getLang()) + "", "showname", "showname")).setIsPrimarykey(BoolAttr.TRUE));
/* 150 */     arrayList.add(new SplitTableColBean("30%", "" + SystemEnv.getHtmlLabelName(23130, ThreadVarLanguage.getLang()) + "", "showtype", "showtype", "weaver.general.SplitPageTransmethod.getShowType", "column:showtype" + this.user.getLanguage()));
/*     */     
/* 152 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str5, str6, "showname", arrayList);
/* 153 */     splitTableBean.setSqlsortway("ASC");
/* 154 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 155 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, Object> browserButtonConditions(Map<String, Object> paramMap) {
/* 160 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 161 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 162 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 163 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 33439, "name").setIsQuickSearch(true));
/* 164 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 84, "showname"));
/*     */     
/* 166 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 23130, "showtype");
/* 167 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 168 */     arrayList1.add(new SearchConditionOption("", ""));
/* 169 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(19525, this.user.getLanguage())));
/* 170 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(32308, this.user.getLanguage())));
/* 171 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(32309, this.user.getLanguage())));
/* 172 */     searchConditionItem.setOptions(arrayList1);
/* 173 */     arrayList.add(searchConditionItem);
/*     */     
/* 175 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 176 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 184 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 185 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     try {
/* 187 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("comeFrom"));
/* 188 */       if (StringUtil.isEmpty(str1)) {
/* 189 */         BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 190 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse));
/* 191 */         return (Map)hashMap;
/*     */       } 
/*     */ 
/*     */       
/* 195 */       String str2 = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("q")), "UTF-8").trim();
/*     */       
/* 197 */       if (!str2.isEmpty()) {
/* 198 */         RecordSet recordSet = new RecordSet();
/* 199 */         String str3 = Util.null2String(paramHttpServletRequest.getParameter("browsername"));
/* 200 */         String str4 = Util.null2String(paramHttpServletRequest.getParameter("appid"));
/* 201 */         String str5 = " a.id,a.customname,b.treefieldname ";
/* 202 */         String str6 = " from mode_custombrowser a ,modeTreeField b ";
/* 203 */         String str7 = " where a.appid=b.id and (b.isdelete is null or b.isdelete=0 ) and upper(a.customname) like upper('%" + str2 + "%') ";
/*     */         
/* 205 */         if (this.isUseFmManageDetach) {
/* 206 */           CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 207 */           int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEFORM:ALL", 0);
/*     */           
/* 209 */           if (arrayOfInt.length == 0) {
/* 210 */             str7 = str7 + " and 1=2 ";
/*     */           } else {
/* 212 */             str7 = str7 + " and " + SqlHelper.SplitSqlInCondition("b.subCompanyId", arrayOfInt);
/*     */           } 
/*     */         } 
/* 215 */         if (!str3.equals("")) {
/* 216 */           str7 = str7 + " and a.customname like '%" + str3 + "%'";
/*     */         }
/* 218 */         if (!str4.equals("")) {
/* 219 */           str7 = str7 + " and b.id = " + str4;
/*     */         }
/* 221 */         String str8 = " a.id desc";
/* 222 */         String str9 = "select " + str5 + str6 + str7 + "order by " + str8;
/* 223 */         recordSet.executeQuery(str9, new Object[0]);
/* 224 */         while (recordSet.next()) {
/* 225 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 226 */           hashMap1.put("id", recordSet.getString("id"));
/* 227 */           hashMap1.put("name", recordSet.getString("customname"));
/* 228 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/* 231 */     } catch (Exception exception) {
/* 232 */       exception.printStackTrace();
/*     */     } 
/* 234 */     hashMap.put("datas", arrayList);
/* 235 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeBrowserBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */