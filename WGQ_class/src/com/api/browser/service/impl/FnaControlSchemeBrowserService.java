/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaControlSchemeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  52 */     String str1 = Util.null2String(paramMap.get("name"));
/*  53 */     String str2 = Util.null2String(paramMap.get("code"));
/*     */     
/*  55 */     String str3 = " a.id, a.name, a.code ";
/*  56 */     String str4 = " fnaControlScheme a join FnaYearsPeriods b on a.fnayearid = b.id ";
/*  57 */     String str5 = " a.id, a.name, a.code ";
/*  58 */     String str6 = "id";
/*     */     
/*  60 */     String str7 = " where 1=1 ";
/*  61 */     if (!"".equals(str1)) {
/*  62 */       str7 = str7 + " and a.name like '%" + StringEscapeUtils.escapeSql(str1) + "%' \n";
/*     */     }
/*  64 */     if (!"".equals(str2)) {
/*  65 */       str7 = str7 + " and a.code like '%" + StringEscapeUtils.escapeSql(str2) + "%' \n";
/*     */     }
/*     */     
/*  68 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  69 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  70 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33162, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/*  71 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33163, this.user.getLanguage()), "code", "code", 0));
/*     */     
/*  73 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str7, str5, str6, arrayList);
/*  74 */     splitTableBean.setSqlsortway("ASC");
/*  75 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  76 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */     if (this.user == null) {
/*  88 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  89 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  92 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  93 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  95 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 33162, "name").setIsQuickSearch(true));
/*  96 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 33163, "code"));
/*  97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  99 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 109 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 110 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 111 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 113 */     StringBuffer stringBuffer = new StringBuffer();
/* 114 */     stringBuffer.append(" select a.id, a.name, a.code ");
/* 115 */     stringBuffer.append(" from fnaControlScheme a ");
/*     */     
/* 117 */     RecordSet recordSet = new RecordSet();
/* 118 */     recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/* 119 */     while (recordSet.next()) {
/* 120 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 121 */       hashMap1.put("id", recordSet.getString("id"));
/* 122 */       hashMap1.put("name", recordSet.getString("name"));
/* 123 */       hashMap1.put("code", recordSet.getString("code"));
/* 124 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 127 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 128 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 129 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 141 */     String str = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*     */     
/* 143 */     RecordSet recordSet = new RecordSet();
/* 144 */     StringBuffer stringBuffer = new StringBuffer();
/* 145 */     stringBuffer.append(" select a.id, a.name, a.code ");
/* 146 */     stringBuffer.append(" from fnaControlScheme a where 1=1 ");
/* 147 */     if (!"".equals(str)) {
/* 148 */       stringBuffer.append(" and (a.name like '%" + str + "%' ");
/* 149 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/* 151 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 152 */           stringBuffer.append(" or f_GetPy(a.name) like '%" + str.toUpperCase() + "%'");
/* 153 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 154 */           stringBuffer.append(" or [dbo].f_GetPy(a.name) like '%" + str.toUpperCase() + "%'");
/* 155 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 156 */           stringBuffer.append(" or getpinyin(a.name) like '%" + str.toUpperCase() + "%'");
/*     */         }  } 
/* 158 */       stringBuffer.append(")");
/*     */     } 
/* 160 */     recordSet.execute(stringBuffer.toString());
/* 161 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 162 */     while (recordSet.next()) {
/* 163 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 164 */       hashMap1.put("id", recordSet.getString("id"));
/* 165 */       hashMap1.put("name", recordSet.getString("name"));
/* 166 */       arrayList.add(hashMap1);
/*     */     } 
/* 168 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 169 */     hashMap.put("datas", arrayList);
/* 170 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaControlSchemeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */