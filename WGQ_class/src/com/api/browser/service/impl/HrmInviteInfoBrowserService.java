/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class HrmInviteInfoBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 24 */     String str1 = Util.null2String(paramMap.get("name"));
/*    */     
/* 26 */     String str2 = " a.id,a.careername,b.jobtitlename,a.createdate ";
/* 27 */     String str3 = " from HrmCareerInvite a left join HrmJobTitles b on a.careername = b.id ";
/* 28 */     String str4 = " where 1=1 ";
/*    */     
/* 30 */     if (!str1.equals("")) {
/* 31 */       str4 = str4 + " and b.jobtitlename like '%" + str1 + "%' ";
/*    */     }
/*    */     
/* 34 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 35 */     arrayList.add(new SplitTableColBean("true", "a.id"));
/* 36 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(6086, this.user.getLanguage()), "jobtitlename", null, 1)).setIsInputCol(BoolAttr.TRUE));
/* 37 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(383346, this.user.getLanguage()), "id", null, "weaver.hrm.HrmTransMethod.getCode", "12"));
/*    */     
/* 39 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, Util.toHtmlForSplitPage(str4), "a.createdate", "a.id", arrayList);
/* 40 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 41 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 45 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 46 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 47 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 48 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 20379, "name", true));
/* 49 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 50 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmInviteInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */