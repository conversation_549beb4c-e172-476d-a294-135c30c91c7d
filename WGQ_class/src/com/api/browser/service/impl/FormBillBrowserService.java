/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WfDataSource;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ 
/*     */ public class FormBillBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     String str1 = Util.null2String(paramMap.get("isDatainput"));
/*  37 */     if ("1".equals(str1)) {
/*  38 */       return getDataInputBrowserData(paramMap);
/*     */     }
/*  40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  41 */     String str2 = Util.null2String(paramMap.get("isBill"));
/*  42 */     String str3 = Util.null2String(paramMap.get("formName"));
/*  43 */     String str4 = Util.null2String(paramMap.get("from"));
/*  44 */     String str5 = Util.null2String(paramMap.get("workflowid"));
/*  45 */     String str6 = Util.null2String(paramMap.get("noNeedVirtual"));
/*  46 */     String str7 = Util.null2String(paramMap.get("isFromFreeForm"));
/*  47 */     String str8 = Util.null2String(paramMap.get("formRangeType"));
/*  48 */     String str9 = Util.null2String(paramMap.get("formRange"));
/*     */     
/*  50 */     int i = 0;
/*  51 */     int j = -1;
/*  52 */     if (!"".equals(str5)) {
/*  53 */       WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  54 */       j = Util.getIntValue(workflowAllComInfo.getIsBill(str5));
/*  55 */       i = Util.getIntValue(workflowAllComInfo.getFormId(str5));
/*     */     } 
/*  57 */     if (str7.equals("1")) {
/*  58 */       i = 0;
/*  59 */       j = -1;
/*     */     } 
/*  61 */     String str10 = addDetachableCondition("t", "subcompanyid", paramMap);
/*     */ 
/*     */     
/*  64 */     String str11 = "select id,formName,formDesc,0 as isbill,subcompanyid from WorkFlow_FormBase t where 1 = 1 ";
/*  65 */     str11 = str11 + str10;
/*     */     
/*  67 */     if (!"".equals(str3)) {
/*  68 */       str11 = str11 + " and formName like '%" + str3 + "%' ";
/*     */     }
/*     */     
/*  71 */     if (j == 0) {
/*  72 */       str11 = str11 + " and id = " + i;
/*  73 */     } else if (j == 1) {
/*  74 */       str11 = "";
/*     */     } 
/*     */     
/*  77 */     if (str7.equals("1") && str8.equals("0")) {
/*  78 */       str11 = str11 + " and id in (" + str9 + ") ";
/*  79 */     } else if (str7.equals("1") && str8.equals("1") && !str9.equals("")) {
/*  80 */       str11 = str11 + " and id not in (" + str9 + ") ";
/*     */     } 
/*     */     
/*  83 */     if ("prjwf".equalsIgnoreCase(str4)) {
/*  84 */       str11 = "";
/*     */     }
/*     */     
/*  87 */     if (!"".equals(str11)) {
/*  88 */       str11 = str11 + " union all ";
/*     */     }
/*     */     
/*  91 */     str11 = str11 + " SELECT t.id,t1.labelname as formName,t.formdes as formDesc,1 as isbill,subcompanyid FROM WorkFlow_Bill t ,HtmlLabelInfo t1 WHERE t.nameLabel = t1.indexID and t1.languageID =  " + this.user.getLanguage();
/*  92 */     str11 = str11 + str10;
/*  93 */     if (!"".equals(str3)) {
/*  94 */       str11 = str11 + " and t1.labelName like '%" + str3 + "%'";
/*     */     }
/*     */ 
/*     */     
/*  98 */     if ("1".equals(str6)) {
/*  99 */       str11 = str11 + " and not exists (select 1 from ModeFormExtend mf where t.id = mf.formid and mf.isvirtualform=1) ";
/*     */     }
/*     */     
/* 102 */     if (j == 1) {
/* 103 */       str11 = str11 + " and t.id  = " + i;
/* 104 */     } else if (j == 0) {
/* 105 */       str11 = str11 + " and 1 =  2 ";
/*     */     } 
/*     */     
/* 108 */     if (str7.equals("1") && str8.equals("0")) {
/* 109 */       str11 = str11 + " and t.id in (" + str9 + ") ";
/* 110 */     } else if (str7.equals("1") && str8.equals("1") && !str9.equals("")) {
/* 111 */       str11 = str11 + " and t.id not in (" + str9 + ") ";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 116 */     if ("report".equals(str4)) {
/* 117 */       str11 = str11 + " and t.id!=6";
/* 118 */     } else if ("prjwf".equalsIgnoreCase(str4)) {
/* 119 */       String str = Util.null2String(paramMap.get("sqlwhere"));
/* 120 */       if (!"".equals(str)) {
/* 121 */         str11 = str11 + " and " + str;
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 133 */     String str12 = "id,formDesc,formName,isbill,subcompanyid";
/* 134 */     String str13 = " ( " + str11 + " ) t where 1 = 1 ";
/*     */     
/* 136 */     if ("1".equals(str2)) {
/* 137 */       str13 = str13 + " and isbill = 1 and id in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0) ";
/* 138 */     } else if ("0".equals(str2)) {
/* 139 */       String str = Util.null2String(paramMap.get("wfMainList"));
/*     */       
/* 141 */       if (str.equals("1") || (str.equals("") && "reportSetting".equals(str4))) {
/* 142 */         str13 = str13 + " and (isbill = 0 or id not in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0)) ";
/* 143 */         if ((new ManageDetachComInfo()).isUseWfManageDetach()) {
/* 144 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 145 */           String str15 = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", -1);
/* 146 */           if (!str15.equals("")) {
/* 147 */             String str16 = Util.getSubINClause(str15, "subcompanyid", "in");
/* 148 */             if (!str16.equals("")) {
/* 149 */               str13 = str13 + " and " + str16;
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } else {
/* 154 */         str13 = str13 + " and isbill = 0 or id not in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0) ";
/*     */       } 
/*     */     } 
/* 157 */     if ("reportSetting".equals(str4) && "".equals(str2) && (
/* 158 */       new ManageDetachComInfo()).isUseWfManageDetach()) {
/* 159 */       str13 = str13 + " and (isbill = 1 and id in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0)) ";
/* 160 */       str13 = str13 + " or ((isbill = 0 or id not in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0))";
/* 161 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 162 */       String str = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowReportManage:All", -1);
/* 163 */       if (!str.equals("")) {
/* 164 */         String str15 = Util.getSubINClause(str, "subcompanyid", "in");
/* 165 */         if (!str15.equals("")) {
/* 166 */           str13 = str13 + " and " + str15;
/*     */         }
/*     */       } else {
/* 169 */         str13 = str13 + " and 1=2";
/*     */       } 
/* 171 */       str13 = str13 + ")";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 176 */     String str14 = Util.null2String(paramMap.get("searchSubId"));
/* 177 */     if (!"".equals(str14)) {
/* 178 */       str13 = str13 + " and (" + Util.getSubINClause(str14, "subcompanyid", "in") + ")";
/*     */     }
/*     */     
/* 181 */     boolean bool1 = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/*     */     
/* 183 */     boolean bool2 = (bool1 == true && Util.null2String(paramMap.get("showSubCompany")).equals("1")) ? true : false;
/* 184 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 185 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 186 */     arrayList.add(new SplitTableColBean("true", "isbill"));
/* 187 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "formName", "formName")).setShowType(1).setIsInputCol(BoolAttr.TRUE));
/* 188 */     if (bool2) {
/* 189 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname"));
/*     */     }
/* 191 */     arrayList.add((new SplitTableColBean(bool2 ? "30%" : "60%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "formDesc", "formDesc")).setShowType(0));
/*     */     
/* 193 */     SplitTableBean splitTableBean = new SplitTableBean(str12, str13, "", "id", "id", "asc", arrayList);
/* 194 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 195 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 201 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 202 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("isDatainput"));
/* 203 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("wfMainList"));
/* 204 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("from"));
/* 205 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("noNeedVirtual"));
/* 206 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("isFromFreeForm"));
/* 207 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("formRangeType"));
/* 208 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("formRange"));
/*     */ 
/*     */     
/* 211 */     if ("1".equals(str1)) {
/* 212 */       List list = (new WfDataSource()).getWorkflowTableForE9(this.user, new HashMap<>(), paramHttpServletRequest, paramHttpServletResponse);
/* 213 */       for (Map map : list) {
/* 214 */         map.put("name", map.get("tablelabelname"));
/* 215 */         map.put("tablelabelnamespan", map.get("tablelabelname"));
/*     */       } 
/*     */       
/* 218 */       hashMap.put("datas", list);
/* 219 */       return (Map)hashMap;
/*     */     } 
/* 221 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*     */     
/* 223 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("isBill"));
/* 224 */     int i = 0;
/* 225 */     int j = -1;
/* 226 */     String str10 = Util.null2String(paramHttpServletRequest.getParameter("workflowid"));
/* 227 */     if (!"".equals(str10)) {
/* 228 */       WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 229 */       j = Util.getIntValue(workflowAllComInfo.getIsBill(str10));
/* 230 */       i = Util.getIntValue(workflowAllComInfo.getFormId(str10));
/*     */     } 
/* 232 */     if (str5.equals("1")) {
/* 233 */       i = 0;
/* 234 */       j = -1;
/*     */     } 
/* 236 */     String str11 = addDetachableCondition("t", "subcompanyid", ParamUtil.request2Map(paramHttpServletRequest));
/*     */     
/* 238 */     String str12 = "select id,formName,formDesc,0 as isbill,subcompanyid from WorkFlow_FormBase t where 1 = 1 ";
/* 239 */     str12 = str12 + str11;
/* 240 */     if (!"".equals(str8)) {
/* 241 */       str12 = str12 + " and formName like '%" + str8 + "%' ";
/*     */     }
/*     */     
/* 244 */     if (j == 0) {
/* 245 */       str12 = str12 + " and id = " + i;
/* 246 */     } else if (j == 1) {
/* 247 */       str12 = "";
/*     */     } 
/*     */     
/* 250 */     if (str5.equals("1") && str6.equals("0")) {
/* 251 */       str12 = str12 + " and id in (" + str7 + ") ";
/* 252 */     } else if (str5.equals("1") && str6.equals("1") && !str7.equals("")) {
/* 253 */       str12 = str12 + " and id not in (" + str7 + ") ";
/*     */     } 
/*     */     
/* 256 */     if (!"".equals(str12)) {
/* 257 */       str12 = str12 + " union all ";
/*     */     }
/*     */     
/* 260 */     str12 = str12 + " SELECT t.id,t1.labelname as formName,t.formdes as formDesc,1 as isbill,subcompanyid FROM WorkFlow_Bill t ,HtmlLabelInfo t1 WHERE t.nameLabel = t1.indexID and t1.languageID =  " + this.user.getLanguage();
/* 261 */     str12 = str12 + str11;
/* 262 */     if (!"".equals(str8)) {
/* 263 */       str12 = str12 + " and t1.labelName like '%" + str8 + "%'";
/*     */     }
/*     */ 
/*     */     
/* 267 */     if ("1".equals(str4)) {
/* 268 */       str12 = str12 + " and not exists (select 1 from ModeFormExtend mf where t.id = mf.formid and mf.isvirtualform=1) ";
/*     */     }
/*     */     
/* 271 */     if (j == 1) {
/* 272 */       str12 = str12 + " and t.id  = " + i;
/* 273 */     } else if (j == 0) {
/* 274 */       str12 = str12 + " and 1 =  2 ";
/*     */     } 
/*     */     
/* 277 */     if (str5.equals("1") && str6.equals("0")) {
/* 278 */       str12 = str12 + " and t.id in (" + str7 + ") ";
/* 279 */     } else if (str5.equals("1") && str6.equals("1") && !str7.equals("")) {
/* 280 */       str12 = str12 + " and t.id not in (" + str7 + ") ";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 285 */     str12 = "select id,formDesc,formName,isbill,subcompanyid from ( " + str12 + " ) t ";
/*     */ 
/*     */     
/* 288 */     if ("1".equals(str9)) {
/* 289 */       str12 = str12 + " where isbill = 1 and id in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0) ";
/* 290 */     } else if ("0".equals(str9)) {
/*     */       
/* 292 */       if (str2.equals("1")) {
/* 293 */         str12 = str12 + " where (isbill = 0 or id not in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0)) ";
/* 294 */         if ((new ManageDetachComInfo()).isUseWfManageDetach()) {
/* 295 */           SubCompanyComInfo subCompanyComInfo1 = new SubCompanyComInfo();
/* 296 */           String str = subCompanyComInfo1.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", -1);
/* 297 */           if (!str.equals("")) {
/* 298 */             String str13 = Util.getSubINClause(str, "subcompanyid", "in");
/* 299 */             if (!str13.equals("")) {
/* 300 */               str12 = str12 + " and " + str13;
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } else {
/* 305 */         str12 = str12 + " where isbill = 0 or id not in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0) ";
/*     */       } 
/*     */     } 
/*     */     
/* 309 */     if ("reportSetting".equals(str3) && "".equals(str9) && (
/* 310 */       new ManageDetachComInfo()).isUseWfManageDetach()) {
/* 311 */       str12 = str12 + " where (isbill = 1 and id in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0)) ";
/* 312 */       str12 = str12 + " or ((isbill = 0 or id not in (select  id  from  view_workflowForm_selectAll   where isoldornew = 1 and id>0))";
/* 313 */       SubCompanyComInfo subCompanyComInfo1 = new SubCompanyComInfo();
/* 314 */       String str = subCompanyComInfo1.getRightSubCompany(this.user.getUID(), "WorkflowReportManage:All", -1);
/* 315 */       if (!str.equals("")) {
/* 316 */         String str13 = Util.getSubINClause(str, "subcompanyid", "in");
/* 317 */         if (!str13.equals("")) {
/* 318 */           str12 = str12 + " and " + str13;
/*     */         }
/*     */       } else {
/* 321 */         str12 = str12 + " and 1=2";
/*     */       } 
/* 323 */       str12 = str12 + ")";
/*     */     } 
/*     */ 
/*     */     
/* 327 */     RecordSet recordSet = new RecordSet();
/* 328 */     recordSet.executeQuery(str12, new Object[0]);
/* 329 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 330 */     boolean bool1 = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/* 331 */     boolean bool2 = (bool1 == true && Util.null2String(paramHttpServletRequest.getParameter("showSubCompany")).equals("1")) ? true : false;
/* 332 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 333 */     while (recordSet.next()) {
/* 334 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 335 */       String str13 = recordSet.getString("formname");
/* 336 */       hashMap1.put("id", recordSet.getString("id"));
/* 337 */       hashMap1.put("name", str13);
/* 338 */       hashMap1.put("isbill", recordSet.getString("isbill"));
/* 339 */       String str14 = recordSet.getString("subcompanyid");
/*     */       
/* 341 */       if (bool2 && !"".equals(str14)) {
/* 342 */         hashMap1.put("title", str13 + "|" + subCompanyComInfo.getSubCompanyname(str14));
/*     */       }
/* 344 */       arrayList.add(hashMap1);
/*     */     } 
/* 346 */     hashMap.put("datas", arrayList);
/* 347 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String addDetachableCondition(String paramString1, String paramString2, Map<String, Object> paramMap) {
/* 360 */     String str = Util.null2String(paramMap.get("rightStr"));
/* 361 */     if (!"".equals(str) && this.user.getUID() != 1) {
/* 362 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 363 */       int i = Util.getIntValue(Util.null2String(paramMap.get("rightlevel")));
/* 364 */       String str1 = Util.null2String(paramMap.get("subcompanyid"));
/*     */       try {
/* 366 */         if ("".equals(str1)) {
/* 367 */           str1 = subCompanyComInfo.getRightSubCompany(this.user.getUID(), str, i);
/*     */         }
/* 369 */         if ("".equals(str1)) {
/* 370 */           str1 = "-1";
/*     */         }
/* 372 */         return " and " + paramString1 + "." + paramString2 + " in (" + str1 + ") ";
/* 373 */       } catch (Exception exception) {
/* 374 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 377 */     return "";
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 382 */     String str = Util.null2String(paramMap.get("isDatainput"));
/* 383 */     if ("1".equals(str))
/* 384 */       return getDataInputBrowserConditionInfo(paramMap); 
/* 385 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 386 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 387 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 389 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 390 */     arrayList1.add(new SearchConditionOption("", ""));
/* 391 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(125026, this.user.getLanguage())));
/* 392 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(125027, this.user.getLanguage())));
/*     */     
/* 394 */     if (!"1".equals(Util.null2String(paramMap.get("noNeedTypeSelect")))) {
/* 395 */       arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 18411, "isBill", arrayList1));
/*     */     }
/* 397 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "formName", true));
/* 398 */     boolean bool1 = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/* 399 */     boolean bool2 = (bool1 == true && Util.null2String(paramMap.get("showSubCompany")).equals("1")) ? true : false;
/* 400 */     if (bool2) {
/* 401 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 17868, "searchSubId", "169"));
/*     */     }
/*     */     
/* 404 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 405 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getDataInputBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 411 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 412 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 413 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 415 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 416 */     arrayList1.add(new SearchConditionOption("", ""));
/*     */     
/* 418 */     int[] arrayOfInt = { 2118, 179, 2115, 2113, 2114, 2116, 2117, 18442, 17629 };
/*     */     
/* 420 */     for (byte b = 0; b < arrayOfInt.length; b++)
/*     */     {
/* 422 */       arrayList1.add(new SearchConditionOption(b + "", SystemEnv.getHtmlLabelName(arrayOfInt[b], this.user.getLanguage())));
/*     */     }
/*     */ 
/*     */     
/* 426 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 33523, "formName", true));
/*     */     
/* 428 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 33522, "modetype", arrayList1));
/*     */ 
/*     */     
/* 431 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 432 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 437 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 438 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 439 */     if ("".equals(str1)) return (Map)hashMap; 
/* 440 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 441 */     RecordSet recordSet = new RecordSet();
/* 442 */     String str2 = "select id,formName,formDesc,0 as isbill from WorkFlow_FormBase where id in (" + str1 + ")";
/* 443 */     str2 = str2 + " union all SELECT t.id,t1.labelname as formName,t.formdes as formDesc,1 as isbill FROM WorkFlow_Bill t ,HtmlLabelInfo t1 WHERE t.nameLabel = t1.indexID and t.id in (" + str1 + ") and t1.languageID =  " + this.user.getLanguage();
/* 444 */     recordSet.executeQuery(str2, new Object[0]);
/* 445 */     while (recordSet.next()) {
/* 446 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 447 */       hashMap1.put("id", recordSet.getString("id"));
/* 448 */       hashMap1.put("formName", recordSet.getString("formname"));
/* 449 */       hashMap1.put("isbill", recordSet.getString("isbill"));
/* 450 */       hashMap1.put("formDesc", recordSet.getString("formDesc"));
/* 451 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 454 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 455 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 456 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getDataInputBrowserData(Map<String, Object> paramMap) throws Exception {
/* 461 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 463 */     String str1 = Util.null2String(paramMap.get("wfid"));
/* 464 */     String str2 = Util.null2String(paramMap.get("fieldname"));
/* 465 */     boolean bool = false;
/* 466 */     String str3 = Util.null2String(paramMap.get("formName"));
/* 467 */     int i = Util.getIntValue(Util.null2String(paramMap.get("modetype")), -1);
/* 468 */     int j = Util.getIntValue(Util.null2String(paramMap.get("searchflag")), -1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 481 */     String str4 = "<table  datasource=\"weaver.workflow.workflow.WfDataSource.getWorkflowTableForE9\" sourceparams=\"modetype:" + i + "+tablename:" + str3 + "+searchflag:" + j + "\" instanceid=\"docMouldTable\" pagesize=\"" + PageIdConst.getPageSize("Wf:workflow_triggertablebrowser", this.user.getUID()) + "\" tabletype=\"none\"> <checkboxpopedom  id=\"checkbox\" /><sql backfields=\"*\"  sqlform=\"tmptable\" sqlorderby=\"id\"  sqlprimarykey=\"id\" sqlsortway=\"asc\"  sqldistinct=\"true\" /><head><col width=\"0%\"  isInputCol=\"false\"  isPrimarykey=\"true\"  hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"id\"/><col width=\"50%\" isInputCol=\"true\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(33523, this.user.getLanguage()) + "\" column=\"tablelabelname\"/><col width=\"50%\" isInputCol=\"false\"  hide=\"false\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" column=\"belongsTo\" text=\"" + SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()) + "\"/><col width=\"0%\" isInputCol=\"false\"  hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"other1\"/><col width=\"0%\" isInputCol=\"false\"    hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"other2\"/><col width=\"0%\" isInputCol=\"false\"  hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"other3\"/></head></table>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 489 */     String str5 = Util.getEncrypt(Util.getRandom());
/* 490 */     Util_TableMap.setVal(str5, str4);
/* 491 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/* 492 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str5);
/* 493 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FormBillBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */