/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SelectItemBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */     String str1 = Util.null2String(paramMap.get("fieldid"));
/* 29 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isbill")), 0);
/* 30 */     String str2 = "id,selectname, selectvalue, listorder ";
/* 31 */     String str3 = "workflow_SelectItem";
/* 32 */     String str4 = " where fieldid = " + str1 + " and isbill = " + i;
/* 33 */     String str5 = "listorder";
/*    */     
/* 35 */     String str6 = Util.null2String(paramMap.get("selectname"));
/* 36 */     if (!"".equals(str6)) {
/* 37 */       str4 = str4 + " and selectname like '%" + str6 + "%'";
/*    */     }
/* 39 */     if ("-2".equals(str1)) {
/* 40 */       str3 = getUnionEmergencyLevelSql();
/* 41 */       str4 = "";
/*    */     } 
/* 43 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*    */     
/* 45 */     arrayList.add(new SplitTableColBean("true", "selectvalue"));
/* 46 */     arrayList.add((new SplitTableColBean("35%", "", "selectname", "selectname", 1)).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 48 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, "selectvalue", arrayList);
/* 49 */     splitTableBean.setSqlsortway("ASC");
/* 50 */     splitTableBean.setSqlisdistinct("true");
/* 51 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 52 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 58 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 59 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 60 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "selectname", true));
/* 61 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 62 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   private String getUnionEmergencyLevelSql() {
/* 67 */     RecordSet recordSet = new RecordSet();
/* 68 */     String str = " ( select 0 as id,'" + SystemEnv.getHtmlLabelName(225, this.user.getLanguage()) + "' as selectname,0 as selectvalue, 0.00 as listorder ";
/* 69 */     if ("oracle".equals(recordSet.getDBType())) str = str + " from dual ";
/*    */     
/* 71 */     str = str + " union all select 1 as id,'" + SystemEnv.getHtmlLabelName(15533, this.user.getLanguage()) + "' as selectname,1 as selectvalue, 1.00 as listorder ";
/* 72 */     if ("oracle".equals(recordSet.getDBType())) str = str + " from dual ";
/*    */     
/* 74 */     str = str + " union all select 2 as id,'" + SystemEnv.getHtmlLabelName(2087, this.user.getLanguage()) + "' as selectname,2 as selectvalue, 2.00 as listorder ";
/* 75 */     if ("oracle".equals(recordSet.getDBType())) str = str + " from dual "; 
/* 76 */     str = str + " ) t ";
/* 77 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SelectItemBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */