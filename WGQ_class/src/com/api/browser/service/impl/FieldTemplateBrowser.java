/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.workflow.biz.FormFieldTransMethod;
/*     */ import com.engine.workflow.biz.FormFieldTypeItemBiz;
/*     */ import com.engine.workflow.util.ListUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldTemplateBrowser
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     String str1 = Util.null2String(paramMap.get("isTemplate"));
/*  41 */     String str2 = Util.null2String(paramMap.get("isDetail"));
/*  42 */     String str3 = Util.null2String(paramMap.get("fieldName"));
/*  43 */     String str4 = Util.null2String(paramMap.get("htmlType"));
/*  44 */     String str5 = Util.null2String(paramMap.get("type"));
/*  45 */     String str6 = Util.null2String(paramMap.get("formId"));
/*  46 */     String str7 = Util.null2String(paramMap.get("allSelectedDetailIds"));
/*  47 */     String str8 = Util.null2String(paramMap.get("selectedDetailIds"));
/*  48 */     String str9 = Util.null2String(paramMap.get("noNeedCustomTreeBrowser"));
/*     */ 
/*     */     
/*  51 */     String str10 = " where 1 = 1 ";
/*  52 */     String str11 = " * ";
/*  53 */     String str12 = "1".equals(str2) ? " workflow_formdictdetail " : " workflow_formdict ";
/*  54 */     String str13 = " fieldname ";
/*     */     
/*  56 */     if ("1".equals(str2))
/*     */     {
/*  58 */       if (!Strings.isNullOrEmpty(str7)) {
/*  59 */         ArrayList arrayList1 = Util.TokenizerString(str7, ",");
/*  60 */         arrayList1.removeAll(Util.TokenizerString(str8, ","));
/*  61 */         if (!arrayList1.isEmpty()) {
/*  62 */           String str = ListUtil.listToStr(arrayList1);
/*  63 */           str10 = str10 + " and id not in (" + str + ") ";
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*  68 */     if ("1".equals(str1)) {
/*  69 */       str10 = str10 + " and istemplate = 1 ";
/*     */     }
/*     */     
/*  72 */     if (!"".equals(str3)) {
/*  73 */       str10 = str10 + " and (description like '%" + str3 + "%' ";
/*  74 */       str10 = str10 + " or fieldname like '%" + str3 + "%') ";
/*     */     } 
/*     */     
/*  77 */     if (!"-1".equals(str4) && !"".equals(str4)) {
/*  78 */       str10 = str10 + " and fieldhtmltype = '" + str4 + "' ";
/*     */     }
/*     */     
/*  81 */     if (!"-1".equals(str5) && !"".equals(str5)) {
/*  82 */       str10 = str10 + " and type = '" + str5 + "' ";
/*     */     }
/*     */     
/*  85 */     if ("1".equals(str9)) {
/*  86 */       str10 = str10 + " and ( fieldhtmltype <> 3 or type not in ('256','257') ) ";
/*     */     }
/*     */     
/*  89 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  90 */     SplitTableColBean splitTableColBean = new SplitTableColBean("true", "id");
/*  91 */     splitTableColBean.setKey("id");
/*  92 */     arrayList.add(splitTableColBean);
/*  93 */     String str14 = getClass().getName() + ".fieldNameTransmethod";
/*  94 */     String str15 = "com.engine.workflow.biz.FormFieldTransMethod.fieldTypeTransmethod";
/*  95 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "fieldName", null, str14, "column:description", 1)).setIsInputCol(BoolAttr.TRUE));
/*  96 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "fieldhtmltype", null, str15, "column:type+" + this.user.getLanguage(), 0));
/*  97 */     arrayList.add(new SplitTableColBean("true", "fielddbtype"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 106 */     SplitTableBean splitTableBean = new SplitTableBean(str11, str12, str10, str13, "id", arrayList);
/* 107 */     splitTableBean.setSqlsortway("ASC");
/* 108 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 109 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 114 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 115 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 116 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 117 */     String str1 = Util.null2String(paramMap.get("noNeedCustomTreeBrowser"));
/* 118 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 120 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15456, "fieldName", true);
/* 121 */     searchConditionItem1.setLabelcol(8);
/* 122 */     searchConditionItem1.setFieldcol(16);
/* 123 */     arrayList.add(searchConditionItem1);
/*     */     
/* 125 */     String str2 = "1".equals(str1) ? "256,257" : "";
/* 126 */     FormFieldTypeItemBiz formFieldTypeItemBiz = new FormFieldTypeItemBiz(this.user, str2);
/* 127 */     SearchConditionItem searchConditionItem2 = formFieldTypeItemBiz.getFieldTypeItem(conditionFactory, "htmlType", "type");
/* 128 */     searchConditionItem2.setLabelcol(8);
/* 129 */     searchConditionItem2.setFieldcol(16);
/* 130 */     arrayList.add(searchConditionItem2);
/*     */     
/* 132 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 137 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 138 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 139 */     String str2 = Util.null2String(paramMap.get("isDetail"));
/* 140 */     String str3 = Util.null2String(paramMap.get("isTemplate"));
/*     */     
/* 142 */     RecordSet recordSet = new RecordSet();
/* 143 */     String str4 = "1".equals(str2) ? "workflow_formdictdetail" : "workflow_formdict";
/* 144 */     String str5 = " select * from " + str4 + " where id in (" + str1 + ") ";
/*     */     
/* 146 */     if ("1".equals(str3)) {
/* 147 */       str5 = str5 + " and istemplate = 1 ";
/*     */     }
/*     */     
/* 150 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 151 */     recordSet.executeQuery(str5, new Object[0]);
/* 152 */     FormFieldTransMethod formFieldTransMethod = new FormFieldTransMethod();
/* 153 */     while (recordSet.next()) {
/* 154 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 155 */       hashMap1.put("id", recordSet.getString("id"));
/* 156 */       hashMap1.put("fieldName", fieldNameTransmethod(recordSet.getString("fieldName"), recordSet.getString("description")));
/* 157 */       hashMap1.put("fieldhtmltype", recordSet.getString("fieldhtmltype"));
/* 158 */       hashMap1.put("fieldhtmltypespan", formFieldTransMethod.fieldTypeTransmethod(recordSet.getString("fieldhtmltype"), recordSet.getString("type"), this.user.getLanguage()));
/* 159 */       hashMap1.put("fielddbtype", recordSet.getString("fielddbtype"));
/*     */       
/* 161 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 164 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 165 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 166 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   protected List<SearchConditionOption> getHtmlTypeOptions() {
/* 170 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */     
/* 172 */     arrayList.add(new SearchConditionOption("-1", " ", true));
/*     */     
/* 174 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(688, this.user.getLanguage()), false));
/*     */     
/* 176 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(689, this.user.getLanguage()), false));
/*     */     
/* 178 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(695, this.user.getLanguage()), false));
/*     */     
/* 180 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(691, this.user.getLanguage()), false));
/*     */     
/* 182 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(690, this.user.getLanguage()), false));
/*     */     
/* 184 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(17616, this.user.getLanguage()), false));
/*     */     
/* 186 */     arrayList.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(21691, this.user.getLanguage()), false));
/*     */     
/* 188 */     arrayList.add(new SearchConditionOption("9", SystemEnv.getHtmlLabelName(125583, this.user.getLanguage()), false));
/* 189 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String fieldNameTransmethod(String paramString1, String paramString2) {
/* 199 */     return (paramString2 != null && paramString2.length() != 0) ? (paramString1 + "(" + paramString2 + ")") : paramString1;
/*     */   }
/*     */   
/*     */   public String fieldLabelTrans(String paramString1, String paramString2) {
/* 203 */     String[] arrayOfString1 = Util.TokenizerString2(paramString2, "+");
/* 204 */     int i = Util.getIntValue(arrayOfString1[0]);
/* 205 */     RecordSet recordSet = new RecordSet();
/* 206 */     String[] arrayOfString2 = new String[3];
/* 207 */     String str = "select fieldlable,langurageid from workflow_fieldlable where formid=" + i + " and fieldid = " + paramString1;
/* 208 */     recordSet.executeQuery(str, new Object[0]);
/* 209 */     while (recordSet.next()) {
/* 210 */       String str1 = recordSet.getString("langurageid");
/* 211 */       String str2 = recordSet.getString("fieldlable");
/* 212 */       if ("7".equals(str1)) {
/* 213 */         arrayOfString2[0] = str2; continue;
/* 214 */       }  if ("8".equals(str1)) {
/* 215 */         arrayOfString2[1] = str2; continue;
/* 216 */       }  if ("9".equals(str1)) {
/* 217 */         arrayOfString2[2] = str2;
/*     */       }
/*     */     } 
/* 220 */     return Util.toMultiLangScreenFromArray(arrayOfString2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FieldTemplateBrowser.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */