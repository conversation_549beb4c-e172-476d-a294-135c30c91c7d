/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.fnaMulDimensions.util.AccountInfoComInfo;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MultiMemberBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  52 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  54 */     if (this.user == null) {
/*  55 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  56 */       return (Map)hashMap;
/*     */     } 
/*     */ 
/*     */     
/*  60 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  62 */     String str1 = Util.null2String(paramMap.get("name"));
/*  63 */     String str2 = Util.null2String(paramMap.get("accountId"));
/*  64 */     String str3 = Util.null2String(paramMap.get("dimensionId"));
/*  65 */     String str4 = Util.null2String(paramMap.get("fnatype"));
/*  66 */     String str5 = "";
/*  67 */     String[] arrayOfString = str3.split(",");
/*  68 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  69 */       if (!"".equals(str5)) {
/*  70 */         str5 = str5 + ",";
/*     */       }
/*  72 */       str5 = str5 + "'" + arrayOfString[b] + "'";
/*     */     } 
/*  74 */     AccountInfoComInfo accountInfoComInfo = new AccountInfoComInfo();
/*  75 */     String str6 = accountInfoComInfo.getNumberCode(str2);
/*  76 */     String str7 = "FnaDimensionMember_" + str6;
/*     */     
/*  78 */     String str8 = "10";
/*  79 */     String str9 = " a.id,a.fkName,c.typeName ";
/*  80 */     String str10 = " " + str7 + " a  join FnaBudgetDimension_" + str6 + " b on a.dimensionId = b.id  join FnaDimensionType c on c.id = b.typeId ";
/*     */ 
/*     */     
/*  83 */     if ("fnainit".equals(str4)) {
/*  84 */       str10 = str10 + " join fnaMemberTagTypeMe_" + str6 + " d on d.memberid = a.id ";
/*     */     }
/*     */     
/*  87 */     String str11 = " where 1=1 and  a.dimensionId in(" + str5 + ")";
/*  88 */     if (!"".equals(str1)) {
/*  89 */       str11 = str11 + " and a.fkName like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/*  91 */     String str12 = "b.typeId,a.displayOrder";
/*  92 */     String str13 = "a.id";
/*     */     
/*  94 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  95 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  96 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "fkName", "fkName"))
/*  97 */         .setIsInputCol(BoolAttr.TRUE).setShowType(1));
/*  98 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "typeName", "typeName"));
/*     */     
/* 100 */     SplitTableBean splitTableBean = new SplitTableBean(str9, str10, str11, str12, str13, "ASC", arrayList);
/* 101 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 102 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 116 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 117 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 118 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 119 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 506675, "name").setIsQuickSearch(true));
/* 120 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 121 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 132 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 134 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 135 */     String str2 = Util.null2String(paramMap.get("accountId"));
/* 136 */     AccountInfoComInfo accountInfoComInfo = new AccountInfoComInfo();
/* 137 */     String str3 = accountInfoComInfo.getNumberCode(str2);
/* 138 */     String str4 = "FnaDimensionMember_" + str3;
/* 139 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 141 */     String[] arrayOfString = str1.split(",");
/* 142 */     String str5 = "";
/* 143 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 144 */       str5 = str5 + "'" + arrayOfString[b] + "',";
/*     */     }
/* 146 */     str5 = str5.substring(0, str5.length() - 1);
/*     */     
/* 148 */     StringBuffer stringBuffer = new StringBuffer();
/* 149 */     stringBuffer.append(" select a.id,a.fkName,c.typeName from " + str4 + " a ");
/* 150 */     stringBuffer.append("   join FnaBudgetDimension_" + str3 + " b on a.dimensionId = b.id  join FnaDimensionType c on c.id = b.typeId where 1=1");
/*     */ 
/*     */     
/* 153 */     stringBuffer.append(" and a.id in (").append(str5).append(")");
/*     */     
/* 155 */     RecordSet recordSet = new RecordSet();
/* 156 */     recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/* 157 */     while (recordSet.next()) {
/* 158 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 159 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 160 */       hashMap1.put("fkName", Util.null2String(recordSet.getString("fkName")));
/* 161 */       hashMap1.put("typeName", Util.null2String(recordSet.getString("typeName")));
/* 162 */       arrayList.add(hashMap1);
/*     */     } 
/* 164 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 165 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 166 */     arrayList1.add(new ListHeadBean("fkName", "", 1, BoolAttr.TRUE));
/* 167 */     arrayList1.add(new ListHeadBean("typeName", ""));
/*     */     
/* 169 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 170 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 171 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 173 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MultiMemberBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */