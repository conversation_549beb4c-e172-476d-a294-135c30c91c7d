/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.workflow.util.MonitorUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.todo.OfsSettingObject;
/*     */ import weaver.workflow.request.todo.RequestUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  44 */     String str1 = Util.null2String(paramMap.get("typename"));
/*  45 */     String str2 = Util.null2String(paramMap.get("typedesc"));
/*  46 */     String str3 = " where 1=1 ";
/*  47 */     if (!str1.equals("")) {
/*  48 */       str3 = str3 + " and typename like '%";
/*  49 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/*  50 */       str3 = str3 + "%'";
/*     */     } 
/*  52 */     if (!str2.equals("")) {
/*  53 */       str3 = str3 + " and typedesc like '%";
/*  54 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/*  55 */       str3 = str3 + "%'";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  60 */     if ("1".equals(Util.null2String(paramMap.get("monitor")))) {
/*  61 */       str3 = str3 + MonitorUtil.getMonitorWorkflowTypeSearchSqlWhere(this.user);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*  66 */     String str4 = "id,typename,typedesc,dsporder";
/*  67 */     String str5 = " workflow_type ";
/*  68 */     String str6 = " id ";
/*     */ 
/*     */     
/*  71 */     String str7 = "sysid, sysshortname as typename, '' as typedesc, 2147483647";
/*  72 */     String str8 = "ofs_sysinfo";
/*  73 */     String str9 = "where 1=1 and cancel = '0' ";
/*  74 */     RequestUtil requestUtil = new RequestUtil();
/*  75 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  76 */     String str10 = Util.null2String(paramMap.get("viewtype"));
/*  77 */     String str11 = Util.null2String(paramMap.get("containsOfs"));
/*  78 */     if (ofsSettingObject.getIsuse() == 1 && ("1".equals(paramMap.get("isMobileWorkflowType")) || WorkflowBrowserServiceAssist.isNeedOsForPortal(str11, str10))) {
/*  79 */       String[] arrayOfString = str3.split("and");
/*  80 */       String str = null;
/*  81 */       if (arrayOfString.length > 1) {
/*  82 */         for (byte b = 1; b < arrayOfString.length; b++) {
/*  83 */           if (arrayOfString[b].trim().contains("typename")) {
/*  84 */             str = arrayOfString[b].replaceFirst("typename", "sysshortname");
/*  85 */             str9 = str9 + " and " + str;
/*  86 */           } else if (arrayOfString[b].trim().contains("typedesc")) {
/*  87 */             str = arrayOfString[b].replaceFirst("typedesc", "sysfullname");
/*  88 */             str9 = str9 + " and " + str;
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } else {
/*  93 */       str9 = str9 + " and 1=2 ";
/*     */     } 
/*     */     
/*  96 */     String str12 = Util.null2String(paramMap.get("sysid"));
/*  97 */     if (null != str12 && !"".equals(str12)) {
/*  98 */       if ("1".equals(ofsSettingObject.getShowsysname())) {
/*  99 */         if (!ofsSettingObject.getOashortname().contains(str12)) {
/* 100 */           str3 = str3 + " and 1=2 ";
/*     */         }
/* 102 */         str9 = str9 + " and sysshortname like '%" + str12 + "%'";
/* 103 */       } else if ("2".equals(ofsSettingObject.getShowsysname())) {
/* 104 */         if (!ofsSettingObject.getOafullname().contains(str12)) {
/* 105 */           str3 = str3 + " and 1=2 ";
/*     */         }
/* 107 */         str9 = str9 + " and sysfullname like '%" + str12 + "%'";
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 113 */     str5 = "( select " + str4 + " from " + str5 + " " + str3 + " union all select " + str7 + " from " + str8 + " " + str9 + ") tx";
/*     */     
/* 115 */     str4 = "id,typename,typedesc,dsporder";
/* 116 */     str6 = "dsporder,id";
/* 117 */     str3 = "";
/*     */ 
/*     */ 
/*     */     
/* 121 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 122 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 123 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "typename", null, 1)).setIsInputCol(BoolAttr.TRUE));
/* 124 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "typedesc", null, 0));
/*     */ 
/*     */     
/* 127 */     boolean bool = (ofsSettingObject.getIsuse() == 1) ? true : false;
/* 128 */     if (bool && "1".equals(paramMap.get("isMobileWorkflowType")) && ("1".equals(ofsSettingObject.getShowsysname()) || "2".equals(ofsSettingObject.getShowsysname()))) {
/* 129 */       SplitTableColBean splitTableColBean = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(22677, this.user.getLanguage()), "dsporder", null, 0);
/* 130 */       splitTableColBean.setTransmethod(getClass().getName() + ".sysNameTransMethod");
/* 131 */       splitTableColBean.setOtherpara("column:id+" + this.user.getLanguage());
/* 132 */       arrayList.add(splitTableColBean);
/*     */     } 
/*     */ 
/*     */     
/* 136 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/* 137 */     splitTableBean.setSqlsortway("ASC");
/* 138 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String sysNameTransMethod(String paramString1, String paramString2) {
/* 148 */     String str1 = "";
/* 149 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 150 */     String str2 = arrayOfString[0];
/* 151 */     String str3 = arrayOfString[1];
/* 152 */     RequestUtil requestUtil = new RequestUtil();
/* 153 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/* 154 */     String str4 = ofsSettingObject.getShowsysname();
/* 155 */     if (Integer.parseInt(str2) < 0) {
/* 156 */       str1 = requestUtil.getSysname(str2, str4);
/*     */     }
/* 158 */     else if ("1".equals(str4)) {
/* 159 */       str1 = ofsSettingObject.getOashortname();
/* 160 */     } else if ("2".equals(str4)) {
/* 161 */       str1 = ofsSettingObject.getOafullname();
/*     */     } 
/*     */     
/* 164 */     return Util.formatMultiLang(str1, str3);
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 169 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 170 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 171 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 172 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 173 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "typename", true));
/* 174 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "typedesc"));
/*     */ 
/*     */     
/* 177 */     RequestUtil requestUtil = new RequestUtil();
/* 178 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/* 179 */     boolean bool = (ofsSettingObject.getIsuse() == 1) ? true : false;
/* 180 */     if ("1".equals(paramMap.get("isMobileWorkflowType")) && bool && ("1".equals(ofsSettingObject.getShowsysname()) || "2".equals(ofsSettingObject.getShowsysname()))) {
/* 181 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 22677, "sysid"));
/*     */     }
/*     */ 
/*     */     
/* 185 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 190 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 191 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 192 */     if ("".equals(str1)) return (Map)hashMap; 
/* 193 */     String str2 = "select id,typename,typedesc from workflow_type where id in (" + str1 + ")";
/* 194 */     RequestUtil requestUtil = new RequestUtil();
/* 195 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/* 196 */     String str3 = Util.null2String(paramMap.get("viewtype"));
/* 197 */     String str4 = Util.null2String(paramMap.get("containsOfs"));
/* 198 */     if (ofsSettingObject.getIsuse() == 1 && ("1".equals(paramMap.get("isMobileWorkflowType")) || WorkflowBrowserServiceAssist.isNeedOsForPortal(str4, str3))) {
/* 199 */       String str = " '' ";
/* 200 */       if ("1".equals(ofsSettingObject.getShowsysname())) {
/* 201 */         str = " sysshortname ";
/* 202 */       } else if ("2".equals(ofsSettingObject.getShowsysname())) {
/* 203 */         str = " sysfullname ";
/*     */       } 
/* 205 */       str2 = str2 + " union select sysid as id, sysshortname as typename, " + str + " as typedesc from ofs_sysinfo where sysid in (" + str1 + ")";
/*     */     } 
/* 207 */     RecordSet recordSet = new RecordSet();
/* 208 */     recordSet.executeQuery(str2, new Object[0]);
/* 209 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 210 */     while (recordSet.next()) {
/* 211 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 212 */       hashMap1.put("id", recordSet.getString(1));
/* 213 */       hashMap1.put("typename", recordSet.getString(2));
/* 214 */       hashMap1.put("typedesc", recordSet.getString(3));
/* 215 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 218 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 219 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 220 */     arrayList1.add(new ListHeadBean("typename", "", 1, BoolAttr.TRUE));
/* 221 */     arrayList1.add(new ListHeadBean("typedesc", "", 0));
/*     */     
/* 223 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 224 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 225 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 226 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */