/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.voting.util.BrowserType;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.DBUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.SplitPageParaBean;
/*     */ import weaver.general.SplitPageUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class VotingMultiBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  46 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*  47 */     int i = 0;
/*     */     
/*     */     try {
/*  50 */       String str1 = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("q")), "UTF-8");
/*  51 */       String str2 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageSize")), "10");
/*  52 */       String str3 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageNum")), "1");
/*     */       
/*  54 */       if (!str1.isEmpty()) {
/*  55 */         RecordSet recordSet = new RecordSet();
/*     */         
/*  57 */         String str = " istemplate !=1 and subject like '%" + str1 + "%' ";
/*     */         
/*  59 */         SplitPageParaBean splitPageParaBean = new SplitPageParaBean();
/*  60 */         SplitPageUtil splitPageUtil = new SplitPageUtil();
/*  61 */         splitPageParaBean.setSqlFrom("Voting");
/*  62 */         splitPageParaBean.setBackFields("id,subject as name");
/*  63 */         splitPageParaBean.setPrimaryKey("id");
/*  64 */         splitPageParaBean.setSqlOrderBy("id");
/*  65 */         splitPageParaBean.getClass(); splitPageParaBean.setSortWay(1);
/*  66 */         splitPageParaBean.setSqlWhere(str);
/*  67 */         splitPageUtil.setSpp(splitPageParaBean);
/*  68 */         recordSet = splitPageUtil.getCurrentPageRs(Integer.valueOf(str3).intValue(), Integer.valueOf(str2).intValue());
/*  69 */         i = splitPageUtil.getRecordCount();
/*     */         
/*  71 */         HashSet<String> hashSet = new HashSet();
/*  72 */         while (recordSet.next()) {
/*  73 */           String str4 = Util.null2String(recordSet.getString("id"));
/*  74 */           if (StringUtils.isNotEmpty(str4) && !hashSet.contains(str4)) {
/*  75 */             hashSet.add(str4);
/*  76 */             arrayList.add(getSearchObj(str4, recordSet.getString("name")));
/*     */           } 
/*     */         } 
/*     */       } 
/*  80 */     } catch (Exception exception) {
/*  81 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  84 */     hashMap.put("count", Integer.valueOf(i));
/*  85 */     hashMap.put("datas", arrayList);
/*     */     
/*  87 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private JSONObject getSearchObj(String paramString1, String paramString2) {
/*  91 */     JSONObject jSONObject = new JSONObject();
/*  92 */     jSONObject.put("id", paramString1);
/*  93 */     jSONObject.put("name", paramString2);
/*  94 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 107 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 108 */     RecordSet recordSet = new RecordSet();
/* 109 */     String str1 = " istemplate !=1 ";
/*     */     
/* 111 */     String str2 = Util.null2String(paramMap.get("votingname"));
/* 112 */     String str3 = Util.null2String(paramMap.get("createrid"));
/* 113 */     if (!str2.equals("")) {
/* 114 */       str1 = str1 + " and subject like '%" + str2 + "%'";
/*     */     }
/* 116 */     if (!str3.equals("")) {
/* 117 */       str1 = str1 + " and createrid ='" + str3 + "'";
/*     */     }
/*     */     
/* 120 */     String str4 = "  id,subject as name";
/* 121 */     String str5 = " Voting";
/*     */     
/* 123 */     String str6 = "id";
/*     */     
/* 125 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 126 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 127 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(531776, this.user.getLanguage()), "name", "name"));
/*     */     
/* 129 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str1, str6, "id", arrayList);
/* 130 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 131 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 136 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 137 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 138 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 139 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 24096, "votingname", true));
/* 140 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, "382643", "createrid", BrowserType.USER));
/* 141 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 142 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 148 */     String str = Util.null2String(paramMap.get("selectids"));
/*     */     
/* 150 */     if (!"".equals(str)) {
/* 151 */       RecordSet recordSet = new RecordSet();
/* 152 */       ArrayList arrayList = new ArrayList();
/* 153 */       recordSet.executeQuery("select id , subject as name from Voting where id in (" + DBUtil.getParamReplace(str) + ") order by id asc", new Object[] { DBUtil.trasToList(arrayList, new Object[] { str }) });
/* 154 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 155 */       while (recordSet.next()) {
/* 156 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 157 */         hashMap1.put("id", recordSet.getString("id"));
/* 158 */         hashMap1.put("idspan", hashMap1.get("id"));
/* 159 */         hashMap1.put("name", recordSet.getString("name"));
/* 160 */         hashMap1.put("namespan", hashMap1.get("name"));
/* 161 */         hashMap1.put("randomFieldId", recordSet.getString("id"));
/* 162 */         hashMap1.put("randomFieldIdspan", "");
/* 163 */         arrayList1.add(hashMap1);
/*     */       } 
/* 165 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 166 */       hashMap.put("datas", arrayList1);
/* 167 */       return (Map)hashMap;
/*     */     } 
/* 169 */     return new HashMap<>();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/VotingMultiBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */