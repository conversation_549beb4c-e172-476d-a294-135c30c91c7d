/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ProjectTempletBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"templetName\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"updatedate\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  50 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  52 */     String str1 = Util.null2String(paramMap.get("fullname"));
/*  53 */     String str2 = Util.null2String(paramMap.get("status"));
/*  54 */     String str3 = Util.null2String(paramMap.get("update_select"));
/*  55 */     String str4 = Util.null2String(paramMap.get("update_start"));
/*  56 */     String str5 = Util.null2String(paramMap.get("update_end"));
/*     */     
/*  58 */     String str6 = "";
/*  59 */     String str7 = "";
/*     */     
/*  61 */     Map<String, String> map = getDateRangeByDateField(str3, str4, str5);
/*  62 */     str6 = map.get("startdate");
/*  63 */     str7 = map.get("enddate");
/*     */ 
/*     */     
/*  66 */     String str8 = " where 1 = 1 ";
/*  67 */     if (!"".equals(str2)) {
/*  68 */       str8 = str8 + " and status='" + str2 + "' ";
/*     */     }
/*  70 */     if (!"".equals(str1)) {
/*  71 */       str8 = str8 + " and templetName like '%" + str1 + "%' ";
/*     */     }
/*  73 */     if (!"".equals(str6)) {
/*  74 */       str8 = str8 + " and updatedate >='" + str6 + "' ";
/*     */     }
/*  76 */     if (!"".equals(str7)) {
/*  77 */       str8 = str8 + " and updatedate <='" + str7 + "' ";
/*     */     }
/*  79 */     str8 = SqlUtils.replaceFirstAnd(str8);
/*  80 */     String str9 = " updatedate ";
/*  81 */     String str10 = " id,templetName,updatedate,status,( case status when '1' then 225 when '2' then 2242 else 220 end) statuslabel ";
/*  82 */     String str11 = " Prj_Template ";
/*     */     
/*  84 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  85 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  86 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "templetName", "templetName")).setIsInputCol(BoolAttr.TRUE));
/*  87 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(602, this.user.getLanguage()), "statuslabel", "statuslabel", "weaver.systeminfo.SystemEnv.getHtmlLabelNames", String.valueOf(this.user.getLanguage())));
/*  88 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(19521, this.user.getLanguage()), "updatedate", "updatedate"));
/*     */     
/*  90 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, str8, str9, "id", arrayList);
/*     */     try {
/*  92 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*  93 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"templetName\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"updatedate\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  94 */     } catch (Exception exception) {
/*  95 */       exception.printStackTrace();
/*     */     } 
/*  97 */     splitTableBean.setSqlisdistinct("true");
/*  98 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  99 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 104 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 105 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 106 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 107 */     ConditionType conditionType = ConditionType.INPUT;
/* 108 */     arrayList.add(conditionFactory.createCondition(conditionType, 18151, "fullname", true));
/*     */     
/* 110 */     conditionType = ConditionType.SELECT;
/* 111 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/* 113 */     SearchConditionOption searchConditionOption = new SearchConditionOption();
/* 114 */     searchConditionOption.setKey("");
/* 115 */     searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(332, this.user.getLanguage()));
/* 116 */     searchConditionOption.setSelected(false);
/* 117 */     arrayList1.add(searchConditionOption);
/*     */     
/* 119 */     searchConditionOption = new SearchConditionOption();
/* 120 */     searchConditionOption.setKey("0");
/* 121 */     searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(220, this.user.getLanguage()));
/* 122 */     searchConditionOption.setSelected(false);
/* 123 */     arrayList1.add(searchConditionOption);
/*     */     
/* 125 */     searchConditionOption = new SearchConditionOption();
/* 126 */     searchConditionOption.setKey("1");
/* 127 */     searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(225, this.user.getLanguage()));
/* 128 */     searchConditionOption.setSelected(false);
/* 129 */     arrayList1.add(searchConditionOption);
/*     */     
/* 131 */     searchConditionOption = new SearchConditionOption();
/* 132 */     searchConditionOption.setKey("2");
/* 133 */     searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(2242, this.user.getLanguage()));
/* 134 */     searchConditionOption.setSelected(false);
/* 135 */     arrayList1.add(searchConditionOption);
/* 136 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(conditionType, 602, "status", arrayList1);
/* 137 */     arrayList.add(searchConditionItem);
/*     */     
/* 139 */     conditionType = ConditionType.DATE;
/* 140 */     searchConditionItem = new SearchConditionItem();
/* 141 */     String str = "update";
/* 142 */     String[] arrayOfString = { str + "_select", str + "_start", str + "_end" };
/* 143 */     searchConditionItem = conditionFactory.createCondition(conditionType, 19521, arrayOfString);
/* 144 */     List<SearchConditionOption> list = getDateSelectFromTo(this.user.getLanguage());
/* 145 */     searchConditionItem.setOptions(list);
/* 146 */     arrayList.add(searchConditionItem);
/*     */     
/* 148 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 149 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, String> getDateRangeByDateField(String paramString1, String paramString2, String paramString3) {
/* 160 */     Calendar calendar = Calendar.getInstance();
/* 161 */     Date date = calendar.getTime();
/* 162 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 163 */     String str1 = "";
/* 164 */     String str2 = "";
/* 165 */     if ("1".equals(paramString1)) {
/* 166 */       str1 = DateHelper.getCurrentDate();
/* 167 */       str2 = DateHelper.getCurrentDate();
/* 168 */     } else if ("2".equals(paramString1)) {
/* 169 */       calendar.set(7, 1);
/* 170 */       date = calendar.getTime();
/* 171 */       str1 = DateHelper.convertDateIntoYYYYMMDDStr(date);
/*     */       
/* 173 */       calendar.set(7, 7);
/* 174 */       date = calendar.getTime();
/* 175 */       str2 = DateHelper.convertDateIntoYYYYMMDDStr(date);
/* 176 */     } else if ("3".equals(paramString1)) {
/* 177 */       str1 = DateHelper.getFirstDayOfMonthWeek(date);
/* 178 */       str2 = DateHelper.getLastDayOfMonthWeek(date);
/* 179 */     } else if ("4".equals(paramString1)) {
/* 180 */       str1 = getQuarterStart();
/* 181 */       str2 = getQuarterEnd();
/* 182 */     } else if ("5".equals(paramString1)) {
/* 183 */       int i = Util.getIntValue(DateHelper.getCurrentYear());
/* 184 */       str1 = getFirstDayOfYear(i);
/* 185 */       str2 = getLastDayOfYear(i);
/* 186 */     } else if ("6".equals(paramString1)) {
/* 187 */       str1 = paramString2;
/* 188 */       str2 = paramString3;
/* 189 */     } else if ("7".equals(paramString1)) {
/* 190 */       calendar.add(2, -1);
/* 191 */       date = calendar.getTime();
/* 192 */       str1 = DateHelper.getFirstDayOfMonthWeek(date);
/* 193 */       str2 = DateHelper.getLastDayOfMonthWeek(date);
/* 194 */     } else if ("8".equals(paramString1)) {
/* 195 */       int i = Util.getIntValue(DateHelper.getCurrentYear()) - 1;
/* 196 */       str1 = getFirstDayOfYear(i);
/* 197 */       str2 = getLastDayOfYear(i);
/*     */     } 
/* 199 */     hashMap.put("startdate", str1);
/* 200 */     hashMap.put("enddate", str2);
/* 201 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFirstDayOfYear(int paramInt) {
/* 210 */     Calendar calendar = Calendar.getInstance();
/* 211 */     calendar.clear();
/* 212 */     calendar.set(1, paramInt);
/* 213 */     Date date = calendar.getTime();
/* 214 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLastDayOfYear(int paramInt) {
/* 224 */     Calendar calendar = Calendar.getInstance();
/* 225 */     calendar.clear();
/* 226 */     calendar.set(1, paramInt);
/* 227 */     calendar.roll(6, -1);
/* 228 */     Date date = calendar.getTime();
/* 229 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getQuarterStart() {
/* 238 */     Calendar calendar = Calendar.getInstance();
/* 239 */     int i = calendar.get(2) + 1;
/* 240 */     if (i >= 1 && i <= 3) {
/* 241 */       calendar.set(2, 0);
/* 242 */     } else if (i >= 4 && i <= 6) {
/* 243 */       calendar.set(2, 3);
/* 244 */     } else if (i >= 7 && i <= 9) {
/* 245 */       calendar.set(2, 6);
/* 246 */     } else if (i >= 10 && i <= 12) {
/* 247 */       calendar.set(2, 9);
/* 248 */     }  calendar.set(5, 1);
/* 249 */     Date date = calendar.getTime();
/* 250 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getQuarterEnd() {
/* 259 */     Calendar calendar = Calendar.getInstance();
/* 260 */     int i = calendar.get(2) + 1;
/* 261 */     if (i >= 1 && i <= 3) {
/* 262 */       calendar.set(2, 2);
/* 263 */       calendar.set(5, 31);
/* 264 */     } else if (i >= 4 && i <= 6) {
/* 265 */       calendar.set(2, 5);
/* 266 */       calendar.set(5, 30);
/* 267 */     } else if (i >= 7 && i <= 9) {
/* 268 */       calendar.set(2, 8);
/* 269 */       calendar.set(5, 30);
/* 270 */     } else if (i >= 10 && i <= 12) {
/* 271 */       calendar.set(2, 11);
/* 272 */       calendar.set(5, 31);
/*     */     } 
/* 274 */     Date date = calendar.getTime();
/* 275 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<SearchConditionOption> getDateSelectFromTo(int paramInt) {
/* 284 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 285 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, paramInt), true));
/* 286 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, paramInt)));
/* 287 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, paramInt)));
/* 288 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, paramInt)));
/* 289 */     arrayList.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(27347, paramInt)));
/* 290 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, paramInt)));
/* 291 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, paramInt)));
/* 292 */     arrayList.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(81716, paramInt)));
/* 293 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, paramInt)));
/* 294 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ProjectTempletBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */