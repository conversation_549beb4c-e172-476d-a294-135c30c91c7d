/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.general.browserData.BrowserManager;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ public class OdocFlowBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  35 */     String str1 = Util.null2String(paramMap.get("workflowName"));
/*  36 */     String str2 = " exists ( select 1 from workflow_createDoc c where b.ID = c.WORKFLOWID AND c.STATUS = 1 and b.officaltype > -1) and b.isvalid = 1 and ( b.istemplate is null or b.istemplate != '1') and b.officaltype > -1 ";
/*  37 */     if (!"".equals(str1))
/*     */     {
/*  39 */       str2 = str2 + " and b.workflowname like '%" + str1 + "%'";
/*     */     }
/*  41 */     String str3 = " b.workflowName,b.id ";
/*  42 */     String str4 = " workflow_base b ";
/*  43 */     String str5 = " dsporder ";
/*  44 */     writeLog("===OdocFlowBrowserService===flowName:" + str1 + "=select " + str3 + " from " + str4 + " where " + str2);
/*     */     
/*  46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  47 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/*  48 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(18104, this.user.getLanguage()), "workflowName", "workflowName", 1)).setIsInputCol(BoolAttr.TRUE));
/*     */     
/*  50 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "id", arrayList);
/*  51 */     splitTableBean.setSqlsortway("ASC");
/*  52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  53 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  60 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  61 */     writeLog("====selectids:" + str);
/*  62 */     if (StringUtils.isBlank(str) && !"null".equalsIgnoreCase(str)) {
/*  63 */       return (Map)hashMap;
/*     */     }
/*  65 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  66 */     RecordSet recordSet = new RecordSet();
/*  67 */     recordSet.executeQuery("select id,workflowname from workflow_base where id in (" + str + ")", new Object[0]);
/*  68 */     while (recordSet.next()) {
/*  69 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  70 */       hashMap1.put("id", recordSet.getString("id"));
/*  71 */       hashMap1.put("workflowName", recordSet.getString("workflowname"));
/*  72 */       arrayList.add(hashMap1);
/*     */     } 
/*  74 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  75 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  76 */     arrayList1.add(new ListHeadBean("workflowName", "", 1, BoolAttr.TRUE));
/*     */     
/*  78 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  79 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/*  80 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  88 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  89 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "workflowName", true));
/*  90 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  91 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  96 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  97 */     BrowserManager browserManager = new BrowserManager();
/*  98 */     browserManager.setOrderKey("dsporder");
/*  99 */     browserManager.setType("odocFlow");
/* 100 */     String str1 = " workflow_base b ";
/* 101 */     String str2 = "  exists ( select 1 from workflow_createDoc c where b.ID = c.WORKFLOWID AND c.STATUS = 1 and b.officaltype > -1) and b.isvalid = 1 and ( b.istemplate is null or b.istemplate != '1') and b.officaltype > -1  ";
/* 102 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, browserManager.getResult(paramHttpServletRequest, "id,workflowname ", str1, str2, 30));
/* 103 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 111 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 112 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.workflowName");
/* 113 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OdocFlowBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */