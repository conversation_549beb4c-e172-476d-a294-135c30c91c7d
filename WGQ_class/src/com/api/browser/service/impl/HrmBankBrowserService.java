/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmBankBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = Util.null2String(paramMap.get("bankname"));
/* 33 */     String str2 = Util.null2String(paramMap.get("bankdesc"));
/* 34 */     String str3 = "where 1 = 1 ";
/* 35 */     String str4 = "";
/* 36 */     String str5 = "";
/* 37 */     String str6 = "";
/* 38 */     if (!str1.equals("")) {
/* 39 */       str3 = str3 + " and bankname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*    */     }
/* 41 */     if (!str2.equals("")) {
/* 42 */       str3 = str3 + " and bankdesc like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*    */     }
/*    */     
/* 45 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 46 */     str4 = " id,bankname,bankdesc ";
/* 47 */     str5 = " HrmBank ";
/* 48 */     str6 = " id ";
/*    */     
/* 50 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 51 */     arrayList.add((new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(21350, Util.getIntValue(this.user.getLanguage())), "bankname", "bankname")).setIsInputCol(BoolAttr.TRUE));
/* 52 */     arrayList.add(new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(********, Util.getIntValue(this.user.getLanguage())), "bankdesc", "bankdesc"));
/*    */     
/* 54 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/*    */     
/* 56 */     splitTableBean.setSqlsortway("ASC");
/* 57 */     splitTableBean.setSqlisdistinct("true");
/* 58 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 59 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 64 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 65 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 66 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 67 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 68 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "185,195", "bankname", true));
/* 69 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "185,433", "bankdesc"));
/* 70 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmBankBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */