/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EncryptAuthItemBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 26 */     String str1 = Util.null2String(paramMap.get("itemcode"));
/* 27 */     String str2 = "where isspecialauth = 1  and ( mouldcode !='DOCUMENT' or ( mouldcode ='DOCUMENT' and  itemcode!='SHAREBASE' and itemcode!='NODE' )) ";
/* 28 */     String str3 = "";
/* 29 */     String str4 = "";
/* 30 */     String str5 = "";
/* 31 */     if (!str1.equals("")) {
/* 32 */       str2 = str2 + " and itemcode like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*    */     }
/* 34 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 35 */     str3 = " id, mouldcode,mouldlabel,itemcode,itemlabel ";
/* 36 */     str4 = " enc_item_config_info ";
/* 37 */     str5 = " mouldcode,itemcode,id ";
/*    */     
/* 39 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 40 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, Util.getIntValue(this.user.getLanguage())), "itemlabel", "itemlabel", "weaver.systeminfo.SystemEnv.getHtmlLabelNames", "" + this.user.getLanguage())).setIsInputCol(BoolAttr.TRUE));
/* 41 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(714, Util.getIntValue(this.user.getLanguage())), "itemcode", "itemcode"));
/* 42 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(33522, Util.getIntValue(this.user.getLanguage())), "mouldlabel", "mouldlabel", "weaver.systeminfo.SystemEnv.getHtmlLabelNames", "" + this.user.getLanguage()));
/*    */     
/* 44 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "id", arrayList);
/*    */     
/* 46 */     splitTableBean.setSqlsortway("ASC");
/* 47 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 48 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EncryptAuthItemBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */