/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class InfoSiftingService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  31 */     String str1 = Util.null2String(paramMap.get("ids"));
/*  32 */     String str2 = Util.null2String(paramMap.get("j_type"));
/*     */     
/*  34 */     String str3 = Util.null2String(paramMap.get("journalId"));
/*     */ 
/*     */ 
/*     */     
/*  38 */     String str4 = Util.null2String(paramMap.get("name"));
/*     */     
/*  40 */     String str5 = Util.null2String(paramMap.get("title"));
/*  41 */     String str6 = Util.null2String(paramMap.get("reportOrg"));
/*  42 */     String str7 = Util.null2String(paramMap.get("j_column"));
/*     */     
/*  44 */     String str8 = "info_sifting";
/*  45 */     String str9 = " * ";
/*  46 */     String str10 = " type = 0 and j_type = " + str2;
/*     */     
/*  48 */     if (!"".equals(str1)) {
/*  49 */       str10 = str10 + " and id not in (" + str1 + ")";
/*     */     }
/*  51 */     if (!"".equals(str3)) {
/*  52 */       str10 = str10 + " and id not in (select siftid from info_journal_detail where mainid <> ' " + str3 + " ')";
/*     */     } else {
/*  54 */       str10 = str10 + " and status = 0";
/*     */     } 
/*     */     
/*  57 */     if (!"".equals(str4)) {
/*  58 */       str10 = str10 + " and title like '%";
/*  59 */       str10 = str10 + Util.fromScreen2(str4, this.user.getLanguage());
/*  60 */       str10 = str10 + "%'";
/*     */     } 
/*  62 */     if (!"".equals(str5)) {
/*  63 */       str10 = str10 + " and title like '%";
/*  64 */       str10 = str10 + Util.fromScreen2(str5, this.user.getLanguage());
/*  65 */       str10 = str10 + "%'";
/*     */     } 
/*  67 */     if (!"".equals(str6)) {
/*  68 */       str10 = str10 + " and reportorg =" + str6;
/*     */     }
/*  70 */     if (!"".equals(str7)) {
/*  71 */       str10 = str10 + " and j_column=" + str7;
/*     */     }
/*  73 */     String str11 = "id";
/*  74 */     String str12 = "desc";
/*  75 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  76 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  77 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(-10456, this.user.getLanguage()), "title", "title", 1));
/*  78 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(26453, Util.getIntValue(this.user.getLanguage())), "j_column", "j_column", "com.api.info.biz.InfoReportTransMethod.getJ_columnName"));
/*  79 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(10000223, Util.getIntValue(this.user.getLanguage())), "reportorg", "reportorg", "com.api.info.biz.InfoReportTransMethod.getReportOrgName"));
/*  80 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(502812, Util.getIntValue(this.user.getLanguage())), "reportdate"));
/*     */     
/*  82 */     SplitTableBean splitTableBean = new SplitTableBean(str9, str8, str10, str11, "id", str12, arrayList);
/*  83 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  84 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  90 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  92 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  93 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/*  95 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, "229", "title");
/*  96 */     searchConditionItem1.setLabelcol(8);
/*  97 */     searchConditionItem1.setFieldcol(16);
/*     */     
/*  99 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 100 */     arrayList1.add(new SearchConditionOption("", "", true));
/* 101 */     String str = Util.null2String(paramMap.get("j_type"));
/* 102 */     RecordSet recordSet = new RecordSet();
/* 103 */     recordSet.executeQuery("select * from info_journalcolumn where mainid=?", new Object[] { str });
/* 104 */     while (recordSet.next()) {
/* 105 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 106 */       String str2 = Util.null2String(recordSet.getString("name"));
/* 107 */       arrayList1.add(new SearchConditionOption(str1, str2, false));
/*     */     } 
/* 109 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, "26453", "j_column", arrayList1);
/* 110 */     searchConditionItem2.setLabelcol(8);
/* 111 */     searchConditionItem2.setFieldcol(16);
/*     */     
/* 113 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, "388482,1329", "reportOrg", "164");
/* 114 */     searchConditionItem3.setLabelcol(8);
/* 115 */     searchConditionItem3.setFieldcol(16);
/*     */     
/* 117 */     arrayList.add(searchConditionItem1);
/* 118 */     arrayList.add(searchConditionItem2);
/* 119 */     arrayList.add(searchConditionItem3);
/* 120 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 121 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/InfoSiftingService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */