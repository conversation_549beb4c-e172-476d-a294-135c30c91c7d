/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PageTemplateBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = Util.null2String(paramMap.get("templatename"));
/*    */     
/* 34 */     String str2 = " id,templatename,templatedesc ";
/* 35 */     String str3 = " pagetemplate ";
/* 36 */     String str4 = " where 1=1 ";
/* 37 */     if (!"".equals(str1)) {
/* 38 */       str4 = str4 + " and templatename like '%" + str1 + "%' ";
/*    */     }
/* 40 */     String str5 = " id ";
/*    */     
/* 42 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 43 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 44 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(22009, this.user.getLanguage()), "templatename", "templatename")).setIsInputCol(BoolAttr.TRUE));
/* 45 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "templatedesc", "templatedesc"));
/*    */     
/* 47 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, "id", "asc", arrayList);
/* 48 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 49 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 54 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 56 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 57 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 58 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 22009, "templatename", true));
/*    */     
/* 60 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 61 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/PageTemplateBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */