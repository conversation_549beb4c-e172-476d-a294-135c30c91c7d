/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptCapitalStateBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"state_name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  48 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  49 */     String str1 = Util.null2String(paramMap.get("state_name"));
/*  50 */     String str2 = Util.null2String(paramMap.get("description"));
/*  51 */     String str3 = " where id in (1,2,3,4,5,6,7,0,-4,-7,8) ";
/*  52 */     boolean bool = true;
/*  53 */     if (!str1.equals("")) {
/*  54 */       if (!bool) {
/*  55 */         bool = true;
/*  56 */         str3 = str3 + " where name like '%";
/*  57 */         str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/*  58 */         str3 = str3 + "%'";
/*     */       } else {
/*  60 */         str3 = str3 + " and name like '%";
/*  61 */         str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/*  62 */         str3 = str3 + "%'";
/*     */       } 
/*     */     }
/*  65 */     if (!str2.equals("")) {
/*  66 */       if (!bool) {
/*  67 */         bool = true;
/*  68 */         str3 = str3 + " where description like '%";
/*  69 */         str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/*  70 */         str3 = str3 + "%'";
/*     */       } else {
/*  72 */         str3 = str3 + " and description like '%";
/*  73 */         str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/*  74 */         str3 = str3 + "%'";
/*     */       } 
/*     */     }
/*  77 */     String str4 = " id ";
/*  78 */     String str5 = " id,name as state_name,description";
/*  79 */     String str6 = " CptCapitalState ";
/*     */     
/*  81 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  82 */     arrayList.add(new SplitTableColBean("30%", "ID", "id", "id"));
/*  83 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "state_name", "state_name")).setIsInputCol(BoolAttr.TRUE));
/*  84 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*     */     
/*  86 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str3, str4, "id", arrayList);
/*     */     try {
/*  88 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*  89 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"state_name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  90 */     } catch (Exception exception) {
/*  91 */       exception.printStackTrace();
/*     */     } 
/*  93 */     splitTableBean.setSqlsortway("ASC");
/*  94 */     splitTableBean.setSqlisdistinct("true");
/*  95 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  96 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 110 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 111 */     ConditionType conditionType = ConditionType.INPUT;
/* 112 */     arrayList.add(conditionFactory.createCondition(conditionType, 195, "state_name", true));
/* 113 */     arrayList.add(conditionFactory.createCondition(conditionType, 433, "description", false));
/*     */ 
/*     */     
/* 116 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 117 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CptCapitalStateBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */