/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.dmlaction.commands.bases.DMLActionBase;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ESBFormFieldBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  34 */     SplitTableBean splitTableBean = new SplitTableBean();
/*  35 */     splitTableBean = getFieldBrowserSplitTable(paramMap);
/*  36 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  38 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getFieldBrowserSplitTable(Map<String, Object> paramMap) {
/*  45 */     String str1 = Util.null2String(paramMap.get("formid"));
/*  46 */     String str2 = Util.null2String(paramMap.get("isbill"));
/*  47 */     String str3 = Util.null2String(paramMap.get("fieldname")).trim();
/*  48 */     String str4 = Util.null2String(paramMap.get("fieldtype"));
/*  49 */     String str5 = Util.null2String(paramMap.get("issearch"));
/*  50 */     str5 = "1";
/*  51 */     String str6 = Util.null2String(paramMap.get("tableinfo"));
/*     */     
/*  53 */     String str7 = Util.null2String(paramMap.get("showdetail"));
/*     */ 
/*     */     
/*  56 */     int i = -1;
/*  57 */     if (!"".equals(str6) && str6.startsWith("detail_")) {
/*     */       
/*  59 */       String[] arrayOfString = str6.split("_", -1);
/*  60 */       if (arrayOfString.length == 3) {
/*  61 */         i = Util.getIntValue(Util.null2String(arrayOfString[1]), -1);
/*  62 */         str7 = "1";
/*     */       } 
/*     */     } else {
/*  65 */       i = -1;
/*  66 */       str7 = "0";
/*     */     } 
/*     */     
/*  69 */     String str8 = "where 1=1 ";
/*     */     
/*  71 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */ 
/*     */     
/*  74 */     SplitTableColBean splitTableColBean = new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "jfieldlabel", "jfieldlabel", "weaver.general.KnowledgeTransMethod.forHtml");
/*  75 */     splitTableColBean.setIsInputCol(BoolAttr.TRUE);
/*  76 */     arrayList.add(splitTableColBean);
/*     */ 
/*     */     
/*  79 */     splitTableColBean = new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "jfieldname", "jfieldname", "weaver.general.KnowledgeTransMethod.forHtml");
/*  80 */     splitTableColBean.setIsPrimarykey(BoolAttr.TRUE);
/*  81 */     arrayList.add(splitTableColBean);
/*     */ 
/*     */     
/*  84 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "jfielddbtype", "jfielddbtype", "weaver.general.KnowledgeTransMethod.forHtml"));
/*     */ 
/*     */     
/*  87 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(83682, this.user.getLanguage()), "fielddesc", "fielddesc", "weaver.general.KnowledgeTransMethod.forHtml"));
/*     */     
/*  89 */     SplitTableBean splitTableBean = new SplitTableBean();
/*  90 */     splitTableBean.setBackfields("*");
/*  91 */     splitTableBean.setSqlform("tmptable");
/*  92 */     splitTableBean.setSqlprimarykey("id");
/*  93 */     splitTableBean.setSqlorderby("id");
/*  94 */     splitTableBean.setSqlsortway("asc");
/*  95 */     splitTableBean.setSqlwhere(str8);
/*  96 */     splitTableBean.setCols(arrayList);
/*  97 */     splitTableBean.setDatasource("weaver.workflow.action.ActionDataSource.getESBFormField");
/*  98 */     splitTableBean.setSourceparams("formid:" + str1 + "+isbill:" + str2 + "+fieldname:" + str3 + "+fieldtype:" + str4 + "+issearch:" + str5 + "+showdetail:" + str7 + "+tableid:" + i + "+tableInfo:" + str6 + "+sqlwhere:" + 
/*  99 */         Util.toHtmlForSplitPage(str8));
/*     */     
/* 101 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 110 */     String str1 = Util.null2String(paramMap.get("formid"));
/* 111 */     String str2 = Util.null2String(paramMap.get("isbill"));
/* 112 */     DMLActionBase dMLActionBase = new DMLActionBase();
/* 113 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 114 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 115 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 606, "fieldname", true));
/*     */     
/* 117 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 118 */     arrayList1.add(new SearchConditionOption("", ""));
/* 119 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(83678, this.user.getLanguage())));
/* 120 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(83679, this.user.getLanguage())));
/* 121 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(83680, this.user.getLanguage())));
/* 122 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 63, "fieldtype", arrayList1));
/*     */     
/* 124 */     arrayList1 = new ArrayList<>();
/* 125 */     List list = dMLActionBase.getDmlSource(str1, str2, this.user.getLanguage());
/* 126 */     for (Map map : list) {
/* 127 */       String str3 = Util.null2String((String)map.get("tableName"));
/* 128 */       String str4 = Util.null2String((String)map.get("tableId"));
/* 129 */       arrayList1.add(new SearchConditionOption(str4, str3));
/*     */     } 
/* 131 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 28006, "tableinfo", arrayList1));
/*     */     
/* 133 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ESBFormFieldBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */