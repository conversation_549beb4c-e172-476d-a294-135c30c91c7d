/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptFieldBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  28 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/*  29 */     String str1 = Util.null2String(paramMap.get("htmltype"));
/*  30 */     String str2 = Util.null2String(paramMap.get("type"));
/*  31 */     String str3 = Util.null2String(paramMap.get("fielddbtype"));
/*  32 */     int j = Util.getIntValue(Util.null2String(paramMap.get("tabletype")), -1);
/*  33 */     String str4 = Util.null2String(paramMap.get("fieldname"));
/*  34 */     String str5 = Util.toSqlForSplitPage(Util.null2String(paramMap.get("sqlwhere")));
/*  35 */     String str6 = Util.null2String(paramMap.get("isfrom"));
/*     */ 
/*     */ 
/*     */     
/*  39 */     RecordSet recordSet1 = new RecordSet();
/*  40 */     RecordSet recordSet2 = new RecordSet();
/*  41 */     boolean bool = false;
/*  42 */     if ("".equals(str5) && !"".equals(Integer.valueOf(i))) {
/*  43 */       bool = false;
/*     */     } else {
/*  45 */       bool = true;
/*     */     } 
/*  47 */     String str7 = "";
/*  48 */     String str8 = "";
/*  49 */     String str9 = "";
/*  50 */     String str10 = "";
/*  51 */     String str11 = "";
/*  52 */     String str12 = "";
/*     */     
/*  54 */     recordSet1.execute("select formid,isbill from workflow_base where id=" + i);
/*  55 */     if (recordSet1.next()) {
/*  56 */       str9 = recordSet1.getString("formid");
/*  57 */       str8 = recordSet1.getString("isbill");
/*     */     } 
/*     */     
/*  60 */     if (!bool) {
/*  61 */       if (str8.equals("0")) {
/*  62 */         String str = "workflow_formdict";
/*  63 */         if (j > 0) str = "workflow_formdictdetail";
/*     */ 
/*     */ 
/*     */         
/*  67 */         str10 = "a.fieldid, b.fieldlable, a.isdetail, a.fieldorder, '' as description, a.groupid as optionkey,c.fieldhtmltype,c.type ";
/*  68 */         str11 = " workflow_formfield a, workflow_fieldlable b, " + str + " c ";
/*  69 */         str5 = " where a.formid=b.formid and a.fieldid=b.fieldid AND a.fieldid = c.id and a.formid=" + str9 + " and b.langurageid = " + this.user.getLanguage();
/*     */         
/*  71 */         if (j == 0)
/*  72 */           str5 = str5 + " and a.isdetail is null "; 
/*  73 */         if (j > 0)
/*  74 */           str5 = str5 + " and a.isdetail=1 "; 
/*  75 */         if (!str4.equals(""))
/*  76 */           str5 = str5 + " and b.fieldlable like '%" + str4 + "%' "; 
/*  77 */         if (recordSet1.getDBType().equals("oracle")) {
/*  78 */           str12 = " a.isdetail desc,optionkey asc,a.fieldorder asc ";
/*     */         } else {
/*  80 */           str12 = " order by a.isdetail,optionkey,a.fieldorder ";
/*     */         } 
/*  82 */       } else if (str8.equals("1")) {
/*  83 */         str7 = "select id as fieldid,fieldlabel,viewtype as isdetail,dsporder as fieldorder, '' as description, detailtable as optionkey,fieldhtmltype,type from workflow_billfield where billid=" + str9;
/*     */         
/*  85 */         str10 = "t.orderid,t1.id as fieldid,t2.labelname,t1.viewtype as isdetail,t1.dsporder as fieldorder, '' as description, t1.detailtable as optionkey,t1.fieldhtmltype,t1.type ";
/*     */         
/*  87 */         str11 = " workflow_billfield t1 left join htmllabelinfo t2 on t1.fieldlabel = t2.indexid and t2.languageid = " + this.user.getLanguage() + " left join workflow_billdetailtable t on t1.viewtype=1 and t1.detailtable=t.tablename";
/*     */         
/*  89 */         str5 = " where t1.billid=" + str9;
/*  90 */         if (j == 0)
/*  91 */           str5 = str5 + " and t1.viewtype=0"; 
/*  92 */         if (j > 0)
/*  93 */           str5 = str5 + " and t1.viewtype=1"; 
/*  94 */         if (!"".equals(str1)) {
/*  95 */           str5 = str5 + " and t1.fieldhtmltype in(" + str1 + ")";
/*     */         }
/*  97 */         if (!"".equals(str2)) {
/*  98 */           str5 = str5 + " and t1.type in(" + str2 + ")";
/*     */         }
/* 100 */         if (!"".equals(str3)) {
/* 101 */           str5 = str5 + " and t1.fielddbtype='" + str3 + "' ";
/*     */         }
/* 103 */         if (!str4.equals("")) {
/* 104 */           str5 = str5 + " and t2.labelname like '%" + str4 + "%' ";
/*     */         }
/* 106 */         str12 = " t1.viewtype,optionkey,t1.dsporder";
/*     */       } else {
/* 108 */         str10 = "a.fieldid, b.fieldlable, a.isdetail, a.fieldorder, '' as description, a.groupid as optionkey ";
/* 109 */         str11 = " workflow_formfield a, workflow_fieldlable b ";
/* 110 */         str5 = " where a.isdetail = -1";
/*     */       } 
/*     */     } else {
/* 113 */       str8 = Util.null2String(paramMap.get("isbill"));
/* 114 */       int k = Util.getIntValue(Util.null2String(paramMap.get("isdetail")), 0);
/* 115 */       if ("1".equals(str8)) {
/* 116 */         if (k == 0)
/*     */         {
/*     */           
/* 119 */           str5 = str5 + " and viewtype = 0 ";
/* 120 */           str11 = " workflow_billfield ";
/* 121 */           str10 = " id as fieldid, fieldlabel, 0 as isdetail, 0 as fieldorder, '' as description, '' as optionkey,fieldhtmltype,type ";
/*     */         }
/*     */         else
/*     */         {
/* 125 */           str5 = str5 + " and viewtype <> 0 ";
/* 126 */           str11 = " workflow_billfield ";
/* 127 */           str10 = " id as fieldid, fieldlabel, 1 as isdetail, 0 as fieldorder, '' as description, '' as optionkey,fieldhtmltype,type ";
/*     */         }
/*     */       
/* 130 */       } else if (k == 0) {
/*     */ 
/*     */         
/* 133 */         str11 = " workflow_formdict ";
/* 134 */         str10 = " id as fieldid, fieldname as fieldlable, 0 as isdetail, 0 as fieldorder, description, '' as optionkey,fieldhtmltype,type  ";
/*     */       
/*     */       }
/*     */       else {
/*     */         
/* 139 */         str11 = " workflow_formdictdetail ";
/* 140 */         str10 = " id as fieldid, fieldname as fieldlable, 1 as isdetail, 0 as fieldorder, description, '' as optionkey,fieldhtmltype,type  ";
/*     */       } 
/*     */ 
/*     */       
/* 144 */       str12 = " id";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 154 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 155 */     arrayList.add(new SplitTableColBean("true", "fieldid"));
/* 156 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "labelname", "labelname")).setIsInputCol(BoolAttr.TRUE));
/* 157 */     String str13 = this.user.getLanguage() + "+column:orderid";
/* 158 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(26734, this.user.getLanguage()), "isdetail", "isdetail", "com.api.cpt.util.ConditionUtil.getWfFieldBrowserValue", str13));
/*     */ 
/*     */     
/* 161 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, str5, str12, "fieldid", arrayList);
/* 162 */     splitTableBean.setSqlsortway("ASC");
/* 163 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 164 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getNewGroupid(String paramString, int paramInt) {
/* 175 */     RecordSet recordSet = new RecordSet();
/* 176 */     int i = 0;
/* 177 */     String str = "";
/* 178 */     if (recordSet.getDBType().equals("oracle")) {
/* 179 */       str = " SELECT t.rid FROM (select rownum as rid,orderid from (SELECT tablename, orderid FROM Workflow_billdetailtable  WHERE billid = " + paramString + " order by id) t1) t WHERE t.orderid=" + paramInt;
/*     */     } else {
/* 181 */       str = "SELECT t.rowid FROM (SELECT ROW_NUMBER() OVER (ORDER BY ORDERid) AS rowid ,tablename,orderid FROM Workflow_billdetailtable WHERE billid=" + paramString + " ) t WHERE t.orderid=" + paramInt;
/*     */     } 
/* 183 */     recordSet.executeSql(str);
/* 184 */     if (recordSet.next()) {
/* 185 */       i = recordSet.getInt(1);
/*     */     } else {
/* 187 */       i = paramInt;
/*     */     } 
/*     */     
/* 190 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 202 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 203 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 204 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 206 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 685, "fieldname", true));
/* 207 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/* 209 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/* 210 */     searchConditionOption1.setKey("0");
/* 211 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()));
/* 212 */     searchConditionOption1.setSelected(false);
/* 213 */     arrayList1.add(searchConditionOption1);
/* 214 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/* 215 */     searchConditionOption2.setKey("1");
/* 216 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()));
/* 217 */     searchConditionOption2.setSelected(false);
/* 218 */     arrayList1.add(searchConditionOption2);
/* 219 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/* 220 */     searchConditionOption3.setKey("");
/* 221 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(332, this.user.getLanguage()));
/* 222 */     searchConditionOption3.setSelected(true);
/* 223 */     arrayList1.add(searchConditionOption3);
/* 224 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 26734, "tabletype", arrayList1));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 247 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 248 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CptFieldBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */