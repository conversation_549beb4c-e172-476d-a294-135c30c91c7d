/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CommonCheckBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     if (!HrmUserVarify.checkUserRight("WORKFLOWPUBLICCHOICE:VIEW", this.user));
/*     */ 
/*     */ 
/*     */     
/*  44 */     String str1 = Util.null2String(paramMap.get("selectitemname"));
/*  45 */     String str2 = Util.null2String(paramMap.get("selectitemdesc"));
/*  46 */     int i = Util.getIntValue(Util.null2String(paramMap.get("hasdetail")), -1);
/*     */     
/*  48 */     String str3 = " where 1=1 ";
/*     */     
/*  50 */     if (!"".equals(str1)) {
/*  51 */       str3 = str3 + " and selectitemname like '%" + str1 + "%' ";
/*     */     }
/*  53 */     if (!"".equals(str2)) {
/*  54 */       str3 = str3 + " and selectitemdesc like '%" + str2 + "%' ";
/*     */     }
/*  56 */     if (i == 0) {
/*  57 */       str3 = str3 + " and not EXISTS (SELECT 1 FROM mode_selectitempagedetail dt WHERE m.id=dt.mainid and dt.pid!=0) ";
/*     */     }
/*  59 */     if (i == 1) {
/*  60 */       str3 = str3 + " and EXISTS (SELECT 1 FROM mode_selectitempagedetail dt WHERE m.id=dt.mainid and dt.pid!=0) ";
/*     */     }
/*     */     
/*  63 */     String str4 = " operatetime ";
/*  64 */     String str5 = " id,selectitemname,selectitemdesc,operatetime,formids,(SELECT case when COUNT(1)>0 then 1 ELSE 0 end FROM mode_selectitempagedetail d WHERE d.mainid=m.id and d.pid!=0) AS hasdetail";
/*  65 */     String str6 = " mode_selectitempage m ";
/*     */     
/*  67 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  68 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  69 */     SplitTableColBean splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "selectitemname", "selectitemname");
/*  70 */     splitTableColBean.setIsInputCol(BoolAttr.TRUE);
/*  71 */     arrayList.add(splitTableColBean);
/*  72 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "selectitemdesc", "selectitemdesc"));
/*  73 */     arrayList.add(new SplitTableColBean("12%", SystemEnv.getHtmlLabelName(124889, this.user.getLanguage()), "hasdetail", "hasdetail", "weaver.workflow.selectItem.SelectItemManager.hasDetail", String.valueOf(this.user.getLanguage())));
/*  74 */     arrayList.add(new SplitTableColBean("18%", SystemEnv.getHtmlLabelName(25295, this.user.getLanguage()), "operatetime", "operatetime"));
/*     */     
/*  76 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str3, str4, "id", arrayList);
/*  77 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  78 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  84 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  85 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  87 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*     */     
/*  89 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "selectitemname", true));
/*  90 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "selectitemdesc", false));
/*     */     
/*  92 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 124889, "hasdetail", false);
/*  93 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  94 */     arrayList1.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/*  95 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(383520, this.user.getLanguage())));
/*  96 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(383519, this.user.getLanguage())));
/*  97 */     searchConditionItem.setOptions(arrayList1);
/*  98 */     arrayList.add(searchConditionItem);
/*     */     
/* 100 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 101 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 107 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*     */     
/* 109 */     RecordSet recordSet = new RecordSet();
/* 110 */     String str2 = "select id,selectitemname from mode_selectitempage m where 1=1 ";
/* 111 */     if (!Strings.isNullOrEmpty(str1)) {
/* 112 */       str2 = str2 + " and selectitemname like '%" + str1 + "%' ";
/*     */     }
/*     */     
/* 115 */     recordSet.executeQuery(str2, new Object[0]);
/*     */     
/* 117 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 119 */     while (recordSet.next()) {
/* 120 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 121 */       hashMap1.put("id", recordSet.getString("id"));
/* 122 */       hashMap1.put("name", recordSet.getString("selectitemname"));
/* 123 */       arrayList.add(hashMap1);
/*     */     } 
/* 125 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 126 */     hashMap.put("datas", arrayList);
/* 127 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CommonCheckBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */