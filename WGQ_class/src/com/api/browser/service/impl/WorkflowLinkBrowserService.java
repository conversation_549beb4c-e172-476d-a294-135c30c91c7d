/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowLinkBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  38 */     String str1 = Util.null2String(paramMap.get("workflowId"));
/*     */ 
/*     */     
/*  41 */     String str2 = Util.null2String(paramMap.get("name"));
/*  42 */     String str3 = Util.null2String(paramMap.get("nodenodeName"));
/*     */ 
/*     */     
/*  45 */     String str4 = " where 1 = 1 ";
/*  46 */     String str5 = "a.id,a.nodeid,a.linkname as name,a.linkorder,a.isbulidcode,b.nodename ";
/*  47 */     String str6 = " workflow_nodelink a,workflow_nodebase b, workflow_nodebase c ";
/*  48 */     str4 = str4 + " and wfrequestid IS NULL ";
/*  49 */     str4 = str4 + " AND a.nodeid = b.id AND workflowid = " + str1;
/*  50 */     str4 = str4 + " AND (b.isFreeNode != '1' OR b.isFreeNode IS null) ";
/*  51 */     str4 = str4 + " AND a.destnodeid = c.id AND (c.isFreeNode != '1' OR c.isFreeNode IS null) ";
/*     */     
/*  53 */     if (!"".equals(str2)) {
/*  54 */       str4 = str4 + " and a.linkname like '%" + str2 + "%' ";
/*     */     }
/*     */     
/*  57 */     if (!"".equals(str3)) {
/*  58 */       str4 = str4 + " and b.nodename like '%" + str3 + "%' ";
/*     */     }
/*     */     
/*  61 */     String str7 = " a.linkorder,a.nodeid,a.id ";
/*     */     
/*  63 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  64 */     String str8 = getClass().getName() + ".nodeNameTrans";
/*  65 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  66 */     SplitTableColBean splitTableColBean = (new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15611, this.user.getLanguage()), "name", null, 1)).setIsInputCol(BoolAttr.TRUE);
/*     */     
/*  68 */     arrayList.add(splitTableColBean);
/*  69 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, str8, "" + this.user.getLanguage(), 0));
/*     */     
/*  71 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, str7, "id", arrayList);
/*  72 */     splitTableBean.setSqlsortway("ASC");
/*  73 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  74 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  79 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  80 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("workflowId"));
/*  81 */     RecordSet recordSet = new RecordSet();
/*  82 */     String str3 = getSQL();
/*  83 */     if (!"".equals(str1)) {
/*  84 */       str3 = str3 + " and (a.linkname like '%" + str1 + "%' ";
/*  85 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/*  87 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/*  88 */           str3 = str3 + " or f_GetPy(a.linkname) like '%" + str1.toUpperCase() + "%'";
/*  89 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/*  90 */           str3 = str3 + " or [dbo].f_GetPy(a.linkname) like '%" + str1.toUpperCase() + "%'";
/*  91 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/*  92 */           str3 = str3 + " or getpinyin(a.linkname) like '%" + str1.toUpperCase() + "%'";
/*     */         }  } 
/*  94 */       str3 = str3 + ")";
/*     */     } 
/*  96 */     str3 = str3 + " ORDER BY a.linkorder,a.nodeid,a.id ";
/*  97 */     recordSet.executeQuery(str3, new Object[] { Integer.valueOf(Util.getIntValue(str2)) });
/*  98 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  99 */     while (recordSet.next()) {
/* 100 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 101 */       hashMap1.put("id", recordSet.getString("id"));
/* 102 */       hashMap1.put("name", recordSet.getString("linkname"));
/* 103 */       arrayList.add(hashMap1);
/*     */     } 
/* 105 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 106 */     hashMap.put("datas", arrayList);
/* 107 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 112 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 113 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 114 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 115 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 117 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15611, "name", true);
/* 118 */     searchConditionItem1.setLabelcol(8);
/* 119 */     searchConditionItem1.setFieldcol(16);
/* 120 */     arrayList.add(searchConditionItem1);
/*     */     
/* 122 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 15070, "nodenodeName");
/* 123 */     searchConditionItem2.setLabelcol(8);
/* 124 */     searchConditionItem2.setFieldcol(16);
/* 125 */     arrayList.add(searchConditionItem2);
/*     */     
/* 127 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 132 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 133 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 134 */     String str2 = Util.null2String(paramMap.get("workflowId"));
/* 135 */     RecordSet recordSet = new RecordSet();
/* 136 */     recordSet.executeQuery(getSQL() + " ORDER BY a.linkorder,a.nodeid,a.id ", new Object[] { str2 });
/* 137 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 138 */     while (recordSet.next()) {
/* 139 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 140 */       hashMap1.put("id", recordSet.getString("id"));
/* 141 */       hashMap1.put("nodename", nodeNameTrans(recordSet.getString("nodename"), this.user.getLanguage() + ""));
/* 142 */       hashMap1.put("name", recordSet.getString("linkname"));
/* 143 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 146 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 147 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 148 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private String getSQL() {
/* 152 */     StringBuffer stringBuffer = new StringBuffer();
/* 153 */     stringBuffer.append(" SELECT a.id,a.nodeid,a.linkname,a.linkorder,a.isbulidcode,b.nodename ")
/* 154 */       .append(" FROM workflow_nodelink a,workflow_nodebase b, workflow_nodebase c ")
/* 155 */       .append(" WHERE wfrequestid IS NULL ")
/* 156 */       .append(" AND a.nodeid = b.id AND workflowid= ? ")
/* 157 */       .append(" AND (b.isFreeNode != '1' OR b.isFreeNode IS null) ")
/* 158 */       .append(" AND a.destnodeid = c.id AND (c.isFreeNode != '1' OR c.isFreeNode IS null) ");
/*     */     
/* 160 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String nodeNameTrans(String paramString1, String paramString2) {
/* 164 */     int i = Util.getIntValue(paramString2);
/* 165 */     return SystemEnv.getHtmlLabelName(15070, i) + "：" + paramString1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowLinkBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */