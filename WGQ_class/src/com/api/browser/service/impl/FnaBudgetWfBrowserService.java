/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaBudgetWfBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  43 */     String str1 = Util.null2String(paramMap.get("name"));
/*  44 */     String str2 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*     */     
/*  46 */     String str3 = " a.id id,b.workflowname name ";
/*  47 */     String str4 = " FROM fnaFeeWfInfo a JOIN workflow_base b ON a.workflowid = b.id ";
/*  48 */     String str5 = " id, name ";
/*  49 */     String str6 = "id";
/*     */     
/*  51 */     String str7 = " where 1=1 ";
/*  52 */     if (!"".equals(str1)) {
/*  53 */       str7 = str7 + " and b.workflowname like '%" + StringEscapeUtils.escapeSql(str1) + "%' \n";
/*     */     }
/*     */     
/*  56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  57 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  58 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(23753, this.user.getLanguage()), "name", null, 1)).setIsInputCol(BoolAttr.TRUE));
/*     */     
/*  60 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str7, str5, str6, arrayList);
/*  61 */     splitTableBean.setSqlsortway("ASC");
/*  62 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  63 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  73 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  74 */     if (this.user == null) {
/*  75 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  76 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  79 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  80 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  82 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 23753, "name"));
/*  83 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  85 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  95 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  96 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  97 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  99 */     StringBuffer stringBuffer = new StringBuffer();
/* 100 */     stringBuffer.append(" SELECT a.id id,b.workflowname name ");
/* 101 */     stringBuffer.append(" FROM fnaFeeWfInfo a JOIN workflow_base b ON a.workflowid = b.id ");
/*     */     
/* 103 */     RecordSet recordSet = new RecordSet();
/* 104 */     recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/* 105 */     while (recordSet.next()) {
/* 106 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 107 */       hashMap1.put("id", recordSet.getString("id"));
/* 108 */       hashMap1.put("name", recordSet.getString("name"));
/* 109 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 112 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 113 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 114 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 126 */     String str = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 127 */     RecordSet recordSet = new RecordSet();
/* 128 */     StringBuffer stringBuffer = new StringBuffer();
/* 129 */     stringBuffer.append(" SELECT a.id id,b.workflowname name ");
/* 130 */     stringBuffer.append(" FROM fnaFeeWfInfo a JOIN workflow_base b ON a.workflowid = b.id where 1=1 ");
/* 131 */     if (!"".equals(str)) {
/* 132 */       stringBuffer.append(" and (b.workflowname like '%" + str + "%' ");
/* 133 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/* 135 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 136 */           stringBuffer.append(" or f_GetPy(b.workflowname) like '%" + str.toUpperCase() + "%'");
/* 137 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 138 */           stringBuffer.append(" or [dbo].f_GetPy(b.workflowname) like '%" + str.toUpperCase() + "%'");
/* 139 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 140 */           stringBuffer.append(" or getpinyin(b.workflowname) like '%" + str.toUpperCase() + "%'");
/*     */         }  } 
/* 142 */       stringBuffer.append(")");
/*     */     } 
/* 144 */     recordSet.execute(stringBuffer.toString());
/* 145 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 146 */     while (recordSet.next()) {
/* 147 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 148 */       hashMap1.put("id", recordSet.getString("id"));
/* 149 */       hashMap1.put("name", recordSet.getString("name"));
/* 150 */       arrayList.add(hashMap1);
/*     */     } 
/* 152 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 153 */     hashMap.put("datas", arrayList);
/* 154 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaBudgetWfBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */