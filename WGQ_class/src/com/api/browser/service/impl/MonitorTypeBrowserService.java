/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MonitorTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  37 */     String str1 = Util.null2String(paramMap.get("typename"));
/*  38 */     String str2 = Util.null2String(paramMap.get("typedesc"));
/*     */     
/*  40 */     String str3 = "where 1=1 ";
/*     */     
/*  42 */     if (!str1.equals("")) {
/*  43 */       str3 = str3 + " and typename like '%";
/*  44 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/*  45 */       str3 = str3 + "%'";
/*     */     } 
/*     */     
/*  48 */     if (!str2.equals("")) {
/*  49 */       str3 = str3 + " and typedesc like '%";
/*  50 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/*  51 */       str3 = str3 + "%'";
/*     */     } 
/*     */     
/*  54 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  55 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  56 */     arrayList.add((new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "typename", "", 1)).setIsInputCol(BoolAttr.TRUE));
/*  57 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "typedesc", "", 0));
/*     */     
/*  59 */     String str4 = "id,typename,typedesc";
/*  60 */     String str5 = "Workflow_MonitorType";
/*  61 */     String str6 = "typeorder,id";
/*  62 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", "asc", arrayList);
/*  63 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  64 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  70 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  71 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  72 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  73 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  74 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "typename", true));
/*  75 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "typedesc"));
/*  76 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  81 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  82 */     RecordSet recordSet = new RecordSet();
/*  83 */     String str2 = "where 1=1 ";
/*  84 */     if (!"".equals(str1)) {
/*  85 */       str2 = str2 + " and (typename like '%" + str1 + "%' ";
/*  86 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/*  88 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/*  89 */           str2 = str2 + " or f_GetPy(typename) like '%" + str1.toUpperCase() + "%'";
/*     */         }
/*  91 */         else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/*  92 */           str2 = str2 + " or getpinyin(typename) like '%" + str1.toUpperCase() + "%'";
/*     */         }
/*  94 */         else if ("sqlserver".equals(recordSet.getDBType())) {
/*  95 */           str2 = str2 + " or [dbo].f_GetPy(typename) like '%" + str1.toUpperCase() + "%'";
/*     */         }  } 
/*  97 */       str2 = str2 + ")";
/*     */     } 
/*  99 */     recordSet.executeQuery("select id,typename from Workflow_MonitorType " + str2 + " order by typeorder", new Object[0]);
/* 100 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 101 */     while (recordSet.next()) {
/* 102 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 103 */       hashMap1.put("id", recordSet.getString("id"));
/* 104 */       hashMap1.put("name", recordSet.getString("typename"));
/* 105 */       arrayList.add(hashMap1);
/*     */     } 
/* 107 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 108 */     hashMap.put("datas", arrayList);
/* 109 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MonitorTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */