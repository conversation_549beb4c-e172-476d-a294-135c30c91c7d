/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class OdocTypeBrowserService extends BrowserService {
/*  33 */   boolean isUseOdocManageDetach = (new ManageDetachComInfo()).isUseOdocManageDetach();
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  39 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  40 */     String str2 = Util.null2String(paramMap.get("fromModule"));
/*  41 */     String str3 = Util.null2String(paramMap.get("type_name"));
/*  42 */     String str4 = Util.null2String(paramMap.get("type_describe"));
/*  43 */     String str5 = Util.null2String(paramMap.get("isDocMouldShare"));
/*  44 */     String str6 = Util.null2String(paramMap.get("subcompanyid"));
/*     */     
/*  46 */     if (!StringUtils.isBlank(getCanUseOodcype(this.user)) && str2.equals("workflow")) {
/*  47 */       str1 = str1 + str1 + "and id in ( " + getCanUseOodcype(this.user) + ")";
/*  48 */     } else if (StringUtils.isBlank(getCanUseOodcype(this.user)) && this.user.getUID() != 1 && str2.equals("workflow")) {
/*  49 */       str1 = str1 + str1 + "and 1=2";
/*     */     } 
/*  51 */     str1 = str1 + "and (ISCANCEL<>'1' or ISCANCEL is null)";
/*  52 */     if (!"".equals(str3)) {
/*  53 */       str1 = str1 + " and type_name like '%" + str3 + "%'";
/*     */     }
/*  55 */     if (!"".equals(str4)) {
/*  56 */       str1 = str1 + " and type_describe like '%" + str4 + "%' ";
/*     */     }
/*  58 */     if (!"".equals(str6)) {
/*  59 */       str1 = str1 + " and subCompanyId in (" + str6 + ") ";
/*     */     }
/*  61 */     str1 = SqlUtils.replaceFirstAnd(str1);
/*  62 */     String str7 = "id,type_name,type_describe,subCompanyId";
/*  63 */     String str8 = "odoc_odoctype";
/*  64 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  65 */     arrayList.add((new SplitTableColBean("hide", "id")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/*  66 */     if ("true".equals(str5)) {
/*  67 */       arrayList.add((new SplitTableColBean("type_name", "", null, 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  68 */       arrayList.add((new SplitTableColBean("type_describe", "", null, 0)).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */     } else {
/*  70 */       arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "type_name", "type_name")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  71 */       arrayList.add((new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "type_describe", "type_describe")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  72 */       if (this.isUseOdocManageDetach) {
/*  73 */         arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subCompanyId", "subCompanyId", "com.engine.odoc.util.OdocStandardFrontMethodUtil.GetSubcompanyName")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */       }
/*     */     } 
/*     */     
/*  77 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str8, str1, "showorder", "id", "asc", arrayList);
/*     */     
/*  79 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/*  81 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/*  82 */     } catch (Exception exception) {
/*  83 */       exception.printStackTrace();
/*     */     } 
/*  85 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  86 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/*  93 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/*  94 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.type_name");
/*  95 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row3.type_describe");
/*  96 */     if (this.isUseOdocManageDetach) {
/*  97 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.subCompanyId");
/*     */     }
/*  99 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCanUseOodcype(User paramUser) {
/* 104 */     if (paramUser.isAdmin())
/* 105 */       return ""; 
/* 106 */     Integer integer1 = Integer.valueOf(paramUser.getUserSubCompany1());
/* 107 */     Integer integer2 = Integer.valueOf(paramUser.getUID());
/* 108 */     List<Map> list = getRole(integer2);
/* 109 */     Integer integer3 = Integer.valueOf(paramUser.getUserDepartment());
/* 110 */     Integer integer4 = Integer.valueOf(Util.getIntValue(paramUser.getSeclevel(), -1));
/* 111 */     String str1 = paramUser.getJoblevel();
/* 112 */     String str2 = paramUser.getJobtitle();
/*     */     
/* 114 */     RecordSet recordSet1 = new RecordSet();
/* 115 */     RecordSet recordSet2 = new RecordSet();
/* 116 */     recordSet1.executeQuery("select * from odoctypeshare", new Object[0]);
/* 117 */     recordSet2.executeQuery("select id from odoc_odoctype", new Object[0]);
/* 118 */     ArrayList<Integer> arrayList = new ArrayList();
/*     */     
/* 120 */     int i = 0;
/*     */     
/* 122 */     Boolean bool = Boolean.valueOf(false);
/* 123 */     while (recordSet2.next()) {
/* 124 */       int j = recordSet2.getInt(1);
/* 125 */       bool = Boolean.valueOf(false);
/* 126 */       boolean bool1 = false;
/* 127 */       while (recordSet1.next()) {
/* 128 */         int k = recordSet1.getInt(2);
/* 129 */         if (k == j) {
/* 130 */           bool1 = true;
/* 131 */           i = recordSet1.getInt(3);
/* 132 */           Integer integer5 = Integer.valueOf(recordSet1.getInt(5));
/* 133 */           Integer integer6 = Integer.valueOf(recordSet1.getInt(6));
/* 134 */           String str3 = recordSet1.getString(4);
/* 135 */           String str4 = recordSet1.getString(8);
/* 136 */           String str5 = recordSet1.getString(9);
/* 137 */           String str6 = recordSet1.getString(7);
/* 138 */           String str7 = recordSet1.getString(10);
/* 139 */           if (i == 1) {
/* 140 */             if (str3.equals(integer2.toString())) {
/* 141 */               bool = Boolean.valueOf(true); break;
/*     */             } 
/*     */             continue;
/*     */           } 
/* 145 */           if (i == 2) {
/*     */             
/* 147 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/*     */               
/* 149 */               if (str3.equals(integer1.toString())) {
/* 150 */                 bool = Boolean.valueOf(true);
/*     */                 
/*     */                 break;
/*     */               } 
/* 154 */               if ("1".equals(str7)) {
/* 155 */                 String str = SubCompanyComInfo.getAllChildSubcompanyId(str3, "");
/* 156 */                 String[] arrayOfString = str.split(",");
/* 157 */                 for (byte b = 0; b < arrayOfString.length; b++) {
/* 158 */                   if (arrayOfString[b].equals(integer1.toString())) {
/* 159 */                     bool = Boolean.valueOf(true); break;
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */             }  continue;
/*     */           } 
/* 165 */           if (i == 3) {
/* 166 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 167 */               if (str3.equals(integer3.toString())) {
/* 168 */                 bool = Boolean.valueOf(true);
/*     */                 
/*     */                 break;
/*     */               } 
/* 172 */               if ("1".equals(str7)) {
/*     */                 
/* 174 */                 ArrayList arrayList1 = new ArrayList();
/* 175 */                 DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 176 */                 departmentComInfo.getAllChildDeptByDepId(arrayList1, str3);
/* 177 */                 if (arrayList1.contains(integer3.toString())) {
/* 178 */                   bool = Boolean.valueOf(true);
/*     */                 }
/*     */               } 
/*     */             } 
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 186 */           if (i == 4) {
/* 187 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 188 */               for (Map map : list) {
/* 189 */                 if (str3.equals(map.get("roleid").toString())) {
/* 190 */                   if (str6.equals("0")) {
/* 191 */                     if ("2".equals(map.get("rolelevel").toString())) {
/* 192 */                       bool = Boolean.valueOf(true); break;
/*     */                     } 
/*     */                     continue;
/*     */                   } 
/* 196 */                   if (str6.equals("2")) {
/* 197 */                     if ("0".equals(map.get("rolelevel").toString())) {
/* 198 */                       bool = Boolean.valueOf(true);
/*     */                       break;
/*     */                     } 
/*     */                     continue;
/*     */                   } 
/* 203 */                   if (str6.equals(map.get("rolelevel").toString())) {
/* 204 */                     bool = Boolean.valueOf(true);
/*     */                   }
/*     */                 } 
/*     */               } 
/*     */             }
/*     */ 
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 214 */           if (i == 7) {
/*     */             
/* 216 */             if (str2.equals(str3)) {
/* 217 */               if ("1".equals(str6)) {
/* 218 */                 if (str4.equals(integer1.toString())) {
/* 219 */                   bool = Boolean.valueOf(true); break;
/*     */                 } 
/*     */                 continue;
/*     */               } 
/* 223 */               if ("2".equals(str6)) {
/* 224 */                 if (str5.equals(integer3.toString())) {
/* 225 */                   bool = Boolean.valueOf(true);
/*     */                   
/*     */                   break;
/*     */                 } 
/*     */                 continue;
/*     */               } 
/* 231 */               bool = Boolean.valueOf(true);
/*     */               
/*     */               break;
/*     */             } 
/*     */             continue;
/*     */           } 
/* 237 */           if (i == 5)
/*     */           {
/* 239 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 240 */               bool = Boolean.valueOf(true);
/*     */ 
/*     */ 
/*     */               
/*     */               break;
/*     */             } 
/*     */           }
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 251 */       if ((bool.booleanValue() || !bool1) && 
/* 252 */         !arrayList.contains(Integer.valueOf(j))) {
/* 253 */         arrayList.add(Integer.valueOf(j));
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 258 */       recordSet1.beforFirst();
/*     */     } 
/*     */     
/* 261 */     return StringUtils.join(arrayList.toArray(), ",");
/*     */   }
/*     */   
/*     */   private List<Map> getRole(Integer paramInteger) {
/* 265 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 266 */     if (paramInteger != null) {
/* 267 */       String str = "select roleid,rolelevel from hrmrolemembers  where resourceid=?";
/* 268 */       RecordSet recordSet = new RecordSet();
/* 269 */       recordSet.executeQuery(str, new Object[] { paramInteger });
/* 270 */       HashMap<Object, Object> hashMap = null;
/* 271 */       while (recordSet.next()) {
/* 272 */         hashMap = new HashMap<>();
/* 273 */         hashMap.put("roleid", recordSet.getString(1));
/* 274 */         hashMap.put("rolelevel", recordSet.getString(2));
/* 275 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 280 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 285 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/* 286 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 287 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/* 288 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/* 291 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 195, "type_name", true);
/* 292 */     arrayList1.add(searchConditionItem1);
/*     */     
/* 294 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 433, "type_describe", false);
/* 295 */     arrayList1.add(searchConditionItem2);
/* 296 */     if (this.isUseOdocManageDetach) {
/* 297 */       arrayList1.add(conditionFactory.createCondition(ConditionType.BROWSER, "17868", "subcompanyid", "164"));
/*     */     }
/* 299 */     arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(383122, this.user.getLanguage()), true, arrayList1));
/* 300 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 301 */     return weaResultMsg.getResultMapAll();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OdocTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */