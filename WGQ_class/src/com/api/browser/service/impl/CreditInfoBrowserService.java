/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CreditInfoBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"creditamount\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]";
/*    */   
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 47 */     String str1 = " * ";
/* 48 */     String str2 = " CRM_CreditInfo ";
/* 49 */     String str3 = " ";
/* 50 */     String str4 = " id ";
/*    */     
/* 52 */     String str5 = Util.null2String(paramMap.get("fullname"));
/* 53 */     String str6 = Util.null2String(paramMap.get("creditamount"));
/* 54 */     String str7 = Util.null2String(paramMap.get("sqlwhere"));
/* 55 */     if (!"".equals(str5)) {
/* 56 */       str3 = str3 + " and fullname like '%";
/* 57 */       str3 = str3 + Util.fromScreen2(str5, this.user.getLanguage());
/* 58 */       str3 = str3 + "%'";
/*    */     } 
/* 60 */     if (!"".equals(str6)) {
/* 61 */       str3 = str3 + " and creditamount like '%";
/* 62 */       str3 = str3 + Util.fromScreen2(str6, this.user.getLanguage());
/* 63 */       str3 = str3 + "%'";
/*    */     } 
/* 65 */     if (!"".equals(str7)) {
/* 66 */       str3 = str3 + str7;
/*    */     }
/* 68 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*    */     
/* 70 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 71 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 72 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname")).setIsInputCol(BoolAttr.TRUE));
/* 73 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "creditamount", "creditamount"));
/*    */     
/* 75 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, str4, "id", arrayList);
/*    */     
/*    */     try {
/* 78 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 79 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"creditamount\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 80 */     } catch (Exception exception) {
/* 81 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 84 */     splitTableBean.setSqlsortway("ASC");
/* 85 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 86 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 93 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 94 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 95 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname"));
/* 96 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "creditamount"));
/* 97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 98 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CreditInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */