/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WfRuleSystemVarBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 25 */     String str1 = " where id <>  3 ";
/*    */     
/* 27 */     str1 = SqlUtils.replaceFirstAnd(str1);
/*    */ 
/*    */     
/* 30 */     String str2 = " * ";
/* 31 */     String str3 = " workflow_ruleSystemVar ";
/* 32 */     String str4 = "id";
/*    */     
/* 34 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 35 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 36 */     String str5 = getClass().getName() + ".labelTrans";
/* 37 */     String str6 = "" + this.user.getLanguage();
/* 38 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "mark", null, 0));
/* 39 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(24256, this.user.getLanguage()), "varName", null, str5, str6, 1)).setIsInputCol(BoolAttr.TRUE));
/* 40 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", null, str5, str6, 0));
/*    */     
/* 42 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str1, str4, "id", arrayList);
/* 43 */     splitTableBean.setSqlsortway("ASC");
/* 44 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 45 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String labelTrans(String paramString1, String paramString2) {
/* 76 */     return SystemEnv.getHtmlLabelNames(paramString1, paramString2);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WfRuleSystemVarBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */