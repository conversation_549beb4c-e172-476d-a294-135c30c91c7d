/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaAccountSetBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  46 */     if (this.user == null) {
/*  47 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  48 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  51 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  52 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */     
/*  55 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 520564, "name");
/*  56 */     searchConditionItem1.setLabelcol(8);
/*  57 */     searchConditionItem1.setFieldcol(16);
/*  58 */     arrayList.add(searchConditionItem1);
/*     */ 
/*     */     
/*  61 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 85, "description");
/*  62 */     searchConditionItem2.setLabelcol(8);
/*  63 */     searchConditionItem2.setFieldcol(16);
/*  64 */     arrayList.add(searchConditionItem2);
/*     */     
/*  66 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  68 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  81 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  83 */     if (this.user == null) {
/*  84 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  85 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  88 */     String str1 = Util.null2String(paramMap.get("name"));
/*  89 */     String str2 = Util.null2String(paramMap.get("description"));
/*     */     
/*  91 */     String str3 = " a.id,a.accountSetName as name,a.description,a.createTime ";
/*  92 */     String str4 = " FnaAccountSet a";
/*  93 */     String str5 = " where 1 = 1 ";
/*     */     
/*  95 */     if (!"".equals(str1)) {
/*  96 */       str5 = str5 + " and a.accountSetName like '%" + str1 + "%' ";
/*     */     }
/*     */     
/*  99 */     if (!"".equals(str2)) {
/* 100 */       str5 = str5 + " and a.description like '%" + str2 + "%' ";
/*     */     }
/*     */ 
/*     */     
/* 104 */     String str6 = " a.createTime,a.accountSetName ";
/* 105 */     String str7 = "a.id";
/*     */     
/* 107 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 108 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 109 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(520564, this.user.getLanguage()), "name", "name"));
/* 110 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(85, this.user.getLanguage()), "description", "description"));
/*     */     
/* 112 */     String str8 = "10";
/* 113 */     SplitTableBean splitTableBean = new SplitTableBean("FnaAccountSetBrowserList", "none", str8, "FnaAccountSetBrowserList", str3, str4, str5, str6, str7, "ASC", arrayList);
/*     */     
/* 115 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 117 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/* 118 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/* 120 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaAccountSetBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */