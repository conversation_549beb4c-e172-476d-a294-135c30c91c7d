/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MeetingCallerBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  36 */     String str1 = Util.null2String(paramMap.get("meetingtype"));
/*  37 */     String str2 = Util.null2String(paramMap.get("fullname"));
/*  38 */     String str3 = "where 1=1 ";
/*  39 */     if (!"".equals(str1)) {
/*  40 */       RecordSet recordSet = new RecordSet();
/*     */       
/*  42 */       recordSet.executeProc("MeetingCaller_SByMeeting", str1);
/*  43 */       String str = "where ( ";
/*  44 */       boolean bool = false;
/*  45 */       while (recordSet.next()) {
/*  46 */         String str7 = recordSet.getString("callertype");
/*  47 */         int i = Util.getIntValue(recordSet.getString("seclevel"), 0);
/*  48 */         String str8 = recordSet.getString("rolelevel");
/*  49 */         String str9 = recordSet.getString("userid");
/*  50 */         String str10 = recordSet.getString("departmentid");
/*  51 */         String str11 = recordSet.getString("roleid");
/*  52 */         String str12 = recordSet.getString("subcompanyid");
/*  53 */         int j = Util.getIntValue(recordSet.getString("seclevelMax"), 0);
/*  54 */         int k = Util.getIntValue(recordSet.getString("jobtitleid"), 0);
/*  55 */         int m = Util.getIntValue(recordSet.getString("joblevel"), 0);
/*  56 */         String str13 = recordSet.getString("joblevelvalue");
/*  57 */         if (str7.equals("1")) {
/*  58 */           if (!bool) {
/*  59 */             str = str + " t1.id=" + str9;
/*     */           }
/*  61 */           if (bool == true) {
/*  62 */             str = str + " or t1.id=" + str9;
/*     */           }
/*     */         } 
/*  65 */         if (str7.equals("2")) {
/*  66 */           if (!bool) {
/*  67 */             str = str + " t1.id in (select id from hrmresource where departmentid=" + str10 + " and seclevel >=" + i + " and seclevel <= " + j + " )";
/*     */           }
/*  69 */           if (bool == true) {
/*  70 */             str = str + " or t1.id in (select id from hrmresource where departmentid=" + str10 + " and seclevel >=" + i + " and seclevel <= " + j + " )";
/*     */           }
/*     */         } 
/*  73 */         if (str7.equals("3")) {
/*  74 */           HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  75 */           List list = hrmCommonServiceImpl.getRoleMembers(Util.getIntValue(str11), str8);
/*  76 */           if (list.size() > 0) {
/*  77 */             if (!bool) {
/*  78 */               str = str + " t1.id in (select id from hrmresource where id in (" + list.toString().substring(1, list.toString().length() - 1) + ") and seclevel >=" + i + " and seclevel <= " + j + ")";
/*     */             }
/*  80 */             if (bool == true) {
/*  81 */               str = str + " or t1.id in (select id from hrmresource where id in (" + list.toString().substring(1, list.toString().length() - 1) + ") and seclevel >=" + i + " and seclevel <= " + j + ")";
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/*  86 */         if (str7.equals("4")) {
/*  87 */           if (!bool) {
/*  88 */             str = str + " t1.id in (select id from hrmresource where seclevel >=" + i + " and seclevel <= " + j + " )";
/*     */           }
/*  90 */           if (bool == true) {
/*  91 */             str = str + " or t1.id in (select id from hrmresource where seclevel >=" + i + " and seclevel <= " + j + " )";
/*     */           }
/*     */         } 
/*  94 */         if (str7.equals("5")) {
/*  95 */           if (!bool) {
/*  96 */             str = str + " t1.id in (select id from hrmresource where subcompanyid1=" + str12 + " and seclevel >=" + i + " and seclevel <= " + j + " )";
/*     */           }
/*  98 */           if (bool == true) {
/*  99 */             str = str + " or t1.id in (select id from hrmresource where subcompanyid1=" + str12 + " and seclevel >=" + i + " and seclevel <= " + j + " )";
/*     */           }
/*     */         } 
/* 102 */         if (str7.equals("8")) {
/* 103 */           if (!bool) {
/* 104 */             str = str + " t1.id in (select id from hrmresource where jobtitle=" + k;
/* 105 */             if (m == 1) {
/* 106 */               str = str + " and subcompanyid1 in (" + str13 + ")";
/* 107 */             } else if (m == 2) {
/* 108 */               str = str + " and departmentid in (" + str13 + ")";
/*     */             } 
/* 110 */             str = str + ")";
/*     */           } 
/* 112 */           if (bool == true) {
/* 113 */             str = str + " or t1.id in (select id from hrmresource where jobtitle=" + k;
/* 114 */             if (m == 1) {
/* 115 */               str = str + " and subcompanyid1 in (" + str13 + ")";
/* 116 */             } else if (m == 2) {
/* 117 */               str = str + " and departmentid in (" + str13 + ")";
/*     */             } 
/* 119 */             str = str + ")";
/*     */           } 
/*     */         } 
/* 122 */         if (!bool) bool = true;
/*     */       
/*     */       } 
/* 125 */       if (!str.equals("where ( ") && str.length() > 1) {
/* 126 */         str = str + " )";
/* 127 */         str3 = str;
/*     */       } 
/*     */     } 
/* 130 */     if (!str2.equals("")) {
/* 131 */       str3 = str3 + " and (t1.lastname like '%";
/* 132 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 133 */       str3 = str3 + "%'";
/* 134 */       str3 = str3 + " or t1.pinyinlastname like '%";
/* 135 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 136 */       str3 = str3 + "%')";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 141 */     String str4 = " t1.id,t1.lastname,t1.jobtitle,t1.departmentid,t1.subcompanyid1 ";
/* 142 */     String str5 = " HrmResource t1 ";
/* 143 */     str3 = str3 + " and t1.status in (0,1,2,3) and t1.loginid<>' '";
/* 144 */     String str6 = "t1.lastname";
/*     */     
/* 146 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 147 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 148 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(413, this.user.getLanguage()), "lastname", "lastname")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/* 149 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(6086, this.user.getLanguage()), "jobtitle", "jobtitle", "weaver.hrm.job.JobTitlesComInfo.getJobTitlesname")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/* 150 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(18939, this.user.getLanguage()), "departmentid", "departmentid", "weaver.hrm.company.DepartmentComInfo.getDepartmentname")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/* 151 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(141, this.user.getLanguage()), "subcompanyid1", "subcompanyid1", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */     
/* 153 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "t1.id", arrayList);
/* 154 */     splitTableBean.setSqlsortway("ASC");
/*     */     
/* 156 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 158 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/* 159 */     } catch (Exception exception) {
/* 160 */       exception.printStackTrace();
/*     */     } 
/* 162 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 163 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 171 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 172 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.lastname");
/* 173 */     MobileJsonConfigUtil.addKey(arrayList, "col2.col2_row1.departmentid");
/* 174 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.subcompanyid1");
/* 175 */     MobileJsonConfigUtil.addKey(arrayList, "col2.col2_row2.jobtitle");
/* 176 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 181 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 182 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 183 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 184 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 185 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 413, "fullname", true));
/* 186 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MeetingCallerBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */