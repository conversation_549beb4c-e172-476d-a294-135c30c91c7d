/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.engine.fnaMulDimensions.util.AccountInfoComInfo;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaApprovalNodeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  42 */     AccountInfoComInfo accountInfoComInfo = new AccountInfoComInfo();
/*     */     
/*  44 */     String str1 = Util.null2String(paramMap.get("typeIdComb"));
/*  45 */     String[] arrayOfString = str1.split(",");
/*     */     
/*  47 */     String str2 = Util.null2String(paramMap.get("name"));
/*     */     
/*  49 */     RecordSet recordSet1 = new RecordSet();
/*  50 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  52 */     ArrayList<String> arrayList = new ArrayList();
/*  53 */     StringBuffer stringBuffer1 = new StringBuffer();
/*  54 */     stringBuffer1.append(" select * from FnaAccountInfo where 1=1 ");
/*  55 */     stringBuffer1.append(" and accountType = 0 or accountType is null ");
/*  56 */     stringBuffer1.append(" and showType = 0 or showType is null ");
/*  57 */     recordSet1.executeQuery(stringBuffer1.toString(), new Object[0]);
/*  58 */     while (recordSet1.next()) {
/*  59 */       String str = Util.null2String(recordSet1.getString("id"));
/*  60 */       arrayList.add(str);
/*     */     } 
/*     */     
/*  63 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  64 */     recordSet1.executeQuery(" select * from FnaAccountDtl where tableType = 12 ", new Object[0]);
/*  65 */     int i = recordSet1.getCounts();
/*  66 */     byte b1 = 0;
/*  67 */     stringBuffer2.append(" select * from (  ");
/*  68 */     while (recordSet1.next()) {
/*  69 */       String str = Util.null2String(recordSet1.getString("tableName"));
/*     */       
/*  71 */       stringBuffer2.append(" select a.accountId, a.numberCode, ");
/*  72 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 1) then a.dimensionTypeId else 0 end) dtId1, ");
/*  73 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 2) then a.dimensionTypeId else 0 end) dtId2, ");
/*  74 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 3) then a.dimensionTypeId else 0 end) dtId3, ");
/*  75 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 4) then a.dimensionTypeId else 0 end) dtId4, ");
/*  76 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 5) then a.dimensionTypeId else 0 end) dtId5, ");
/*  77 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 6) then a.dimensionTypeId else 0 end) dtId6, ");
/*  78 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 7) then a.dimensionTypeId else 0 end) dtId7, ");
/*  79 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 8) then a.dimensionTypeId else 0 end) dtId8, ");
/*  80 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 9) then a.dimensionTypeId else 0 end) dtId9, ");
/*  81 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 10) then a.dimensionTypeId else 0 end) dtId10, ");
/*  82 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 11) then a.dimensionTypeId else 0 end) dtId11, ");
/*  83 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 12) then a.dimensionTypeId else 0 end) dtId12, ");
/*  84 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 13) then a.dimensionTypeId else 0 end) dtId13, ");
/*  85 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 14) then a.dimensionTypeId else 0 end) dtId14, ");
/*  86 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 15) then a.dimensionTypeId else 0 end) dtId15, ");
/*  87 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 16) then a.dimensionTypeId else 0 end) dtId16, ");
/*  88 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 17) then a.dimensionTypeId else 0 end) dtId17, ");
/*  89 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 18) then a.dimensionTypeId else 0 end) dtId18, ");
/*  90 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 19) then a.dimensionTypeId else 0 end) dtId19, ");
/*  91 */       stringBuffer2.append(" sum(case when (a.dimensionTypeId = 20) then a.dimensionTypeId else 0 end) dtId20, ");
/*  92 */       stringBuffer2.append(" count(*) cnt ");
/*  93 */       stringBuffer2.append(" from ").append(str).append(" a ");
/*  94 */       stringBuffer2.append(" group by a.accountId, a.numberCode ");
/*     */       
/*  96 */       if (b1 != i - 1) {
/*  97 */         stringBuffer2.append(" union ");
/*     */       }
/*  99 */       b1++;
/*     */     } 
/* 101 */     stringBuffer2.append(" ) t  ");
/* 102 */     stringBuffer2.append(" where 1=1  ");
/* 103 */     for (byte b2 = 0; b2 < arrayOfString.length; b2++) {
/* 104 */       int j = Integer.parseInt(arrayOfString[b2]);
/* 105 */       stringBuffer2.append(" and t.dtId").append(j).append(" = ").append(j);
/*     */     } 
/* 107 */     stringBuffer2.append(" and t.cnt = ").append(arrayOfString.length);
/*     */     
/* 109 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 110 */     recordSet2.executeQuery(stringBuffer2.toString(), new Object[0]);
/*     */     
/* 112 */     while (recordSet2.next()) {
/* 113 */       String str3 = Util.null2String(recordSet2.getString("accountId"));
/* 114 */       String str4 = accountInfoComInfo.getNumberCode(str3);
/* 115 */       String str5 = accountInfoComInfo.getAccountName(str3);
/* 116 */       String str6 = Util.null2String(recordSet2.getString("numberCode"));
/*     */       
/* 118 */       if (!arrayList.contains(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/* 122 */       StringBuffer stringBuffer = new StringBuffer();
/* 123 */       stringBuffer.append(" select ba.id,ba.filldatatablename,ba.approvalname,bat.approvatypelname ");
/* 124 */       stringBuffer.append(" from FnaBudgetApproval_").append(str4).append(" ba ");
/* 125 */       stringBuffer.append(" join FnaBudgetApprovalType_").append(str4).append(" bat ");
/* 126 */       stringBuffer.append(" on ba.approvalTypeId = bat.id ");
/* 127 */       stringBuffer.append(" where ba.filldatatablename = ? ");
/* 128 */       stringBuffer.append(" and ba.nodestatus = 1 ");
/* 129 */       stringBuffer.append(" and ba.nodeType = 1 ");
/* 130 */       stringBuffer.append(" and bat.approvaTypelstatus = 1 ");
/* 131 */       stringBuffer.append(" and bat.approvalFillDataSataus = 1 ");
/* 132 */       if (!"".equals(str2)) {
/* 133 */         stringBuffer.append(" and ba.approvalname like '%").append(str2).append("%' ");
/*     */       }
/* 135 */       recordSet1.executeQuery(stringBuffer.toString(), new Object[] { "FnaBudgetDFgro_" + str4 + "_" + str6 });
/* 136 */       while (recordSet1.next()) {
/* 137 */         String str7 = Util.null2String(recordSet1.getString("id"));
/* 138 */         String str8 = Util.null2String(recordSet1.getString("approvalname"));
/* 139 */         String str9 = Util.null2String(recordSet1.getString("approvatypelname"));
/*     */         
/* 141 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 142 */         hashMap1.put("id", str7);
/* 143 */         hashMap1.put("name", str8);
/* 144 */         hashMap1.put("approvatypelname", str9);
/* 145 */         hashMap1.put("accountName", str5);
/* 146 */         arrayList1.add(hashMap1);
/*     */       } 
/*     */     } 
/*     */     
/* 150 */     ArrayList<ListHeadBean> arrayList2 = new ArrayList();
/* 151 */     arrayList2.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 152 */     arrayList2.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(502717, this.user.getLanguage()), 1));
/* 153 */     arrayList2.add(new ListHeadBean("approvatypelname", SystemEnv.getHtmlLabelName(502138, this.user.getLanguage()), 0));
/* 154 */     arrayList2.add(new ListHeadBean("accountName", SystemEnv.getHtmlLabelName(387252, this.user.getLanguage()), 0));
/*     */     
/* 156 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList2);
/* 157 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList1);
/* 158 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 160 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 170 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 171 */     AccountInfoComInfo accountInfoComInfo = new AccountInfoComInfo();
/*     */     
/* 173 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 174 */     String[] arrayOfString = str.split(",");
/*     */     
/* 176 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 178 */     RecordSet recordSet1 = new RecordSet();
/* 179 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 181 */     recordSet1.executeQuery(" select * from FnaAccountDtl where tableType = 5 ", new Object[0]);
/* 182 */     while (recordSet1.next()) {
/* 183 */       String str1 = Util.null2String(recordSet1.getString("accountId"));
/* 184 */       String str2 = accountInfoComInfo.getNumberCode(str1);
/* 185 */       String str3 = accountInfoComInfo.getAccountName(str1);
/*     */       
/* 187 */       StringBuffer stringBuffer = new StringBuffer();
/* 188 */       stringBuffer.append(" select ba.id,ba.approvalname,bat.approvatypelname ");
/* 189 */       stringBuffer.append(" from FnaBudgetApproval_").append(str2).append(" ba ");
/* 190 */       stringBuffer.append(" join FnaBudgetApprovalType_").append(str2).append(" bat ");
/* 191 */       stringBuffer.append(" on ba.approvalTypeId = bat.id ");
/* 192 */       stringBuffer.append(" where ba.id = ? ");
/*     */       
/* 194 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 195 */         recordSet2.executeQuery(stringBuffer.toString(), new Object[] { arrayOfString[b] });
/* 196 */         if (recordSet2.next()) {
/* 197 */           String str4 = Util.null2String(recordSet2.getString("approvalname"));
/* 198 */           String str5 = Util.null2String(recordSet2.getString("approvatypelname"));
/*     */           
/* 200 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 201 */           hashMap1.put("id", arrayOfString[b]);
/* 202 */           hashMap1.put("name", str4);
/* 203 */           hashMap1.put("approvatypelname", str5);
/* 204 */           hashMap1.put("accountName", str3);
/* 205 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 210 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 211 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 212 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/* 213 */     arrayList1.add(new ListHeadBean("approvatypelname", "", 0, BoolAttr.TRUE));
/* 214 */     arrayList1.add(new ListHeadBean("accountName", "", 0, BoolAttr.TRUE));
/*     */     
/* 216 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 217 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 218 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 220 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaApprovalNodeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */