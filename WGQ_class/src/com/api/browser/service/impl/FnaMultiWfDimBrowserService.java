/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.fnaMulDimensions.entity.FnaBrowserTreeNodeBean;
/*     */ import com.engine.fnaMulDimensions.util.AccountInfoComInfo;
/*     */ import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
/*     */ import com.engine.fnaMulDimensions.util.DimensionUtil;
/*     */ import com.engine.fnaMulDimensions.util.FnaBrowserUtils;
/*     */ import com.engine.fnaMulDimensions.util.TemplateFillUtil;
/*     */ import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class FnaMultiWfDimBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  38 */     if (this.user == null) {
/*  39 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  40 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  43 */     String str = Util.null2String(paramMap.get("list"));
/*     */     
/*     */     try {
/*  46 */       if ("1".equals(str)) {
/*  47 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  48 */         SplitTableBean splitTableBean = getRecentTableList(paramMap);
/*  49 */         if (splitTableBean == null) {
/*  50 */           ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  51 */           arrayList.add(new ListHeadBean("memberName", SystemEnv.getHtmlLabelName(504430, this.user.getLanguage())));
/*     */ 
/*     */           
/*  54 */           hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  55 */           hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */         } else {
/*  57 */           hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */         }
/*     */       
/*  60 */       } else if (hastypeCondition(paramMap)) {
/*  61 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  62 */         SplitTableBean splitTableBean = getTableList(paramMap);
/*  63 */         if (splitTableBean == null) {
/*  64 */           ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  65 */           arrayList.add(new ListHeadBean("memberName", SystemEnv.getHtmlLabelName(504430, this.user.getLanguage())));
/*     */           
/*  67 */           hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  68 */           hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */         } else {
/*  70 */           hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */         } 
/*     */       } else {
/*     */         
/*  74 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  75 */         List<FnaBrowserTreeNodeBean> list = getTreeNodeInfo(paramMap);
/*  76 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       }
/*     */     
/*  79 */     } catch (Exception exception) {
/*  80 */       (new BaseBean()).writeLog(exception);
/*     */     } 
/*     */     
/*  83 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getRecentTableList(Map<String, Object> paramMap) {
/*  95 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  97 */     String str1 = FnaBrowserUtils.getAccountInfo(paramMap, this.user.getUID());
/*  98 */     if ("".equals(str1)) {
/*  99 */       return null;
/*     */     }
/*     */     
/* 102 */     AccountInfoComInfo accountInfoComInfo = new AccountInfoComInfo();
/* 103 */     String str2 = accountInfoComInfo.getNumberCode(str1);
/*     */     
/* 105 */     int i = Util.getIntValue((String)paramMap.get("fieldid"));
/* 106 */     String str3 = Util.null2String(paramMap.get("wfid"));
/* 107 */     String str4 = "";
/*     */ 
/*     */     
/* 110 */     ArrayList<String> arrayList1 = new ArrayList();
/*     */     
/* 112 */     ArrayList<String> arrayList2 = new ArrayList();
/* 113 */     String str5 = "";
/* 114 */     recordSet.executeQuery("select * from fnaDimBrowserWf where fieldid = ? and workflowId = ? order by displayOrder ", new Object[] { Integer.valueOf(i), str3 });
/* 115 */     while (recordSet.next()) {
/* 116 */       String str16 = Util.null2String(recordSet.getString("typeid"));
/* 117 */       str4 = Util.null2String(recordSet.getString("fnaseparator"));
/* 118 */       String str17 = Util.null2String(recordSet.getString("electShow"));
/* 119 */       arrayList1.add(str17);
/*     */       
/* 121 */       arrayList2.add(str16);
/* 122 */       if (!"".equals(str5)) {
/* 123 */         str5 = str5 + ",";
/*     */       }
/* 125 */       str5 = str5 + str16;
/*     */     } 
/* 127 */     if (arrayList2.size() == 0) {
/* 128 */       return null;
/*     */     }
/*     */ 
/*     */     
/* 132 */     HashSet<String> hashSet = new HashSet();
/*     */     
/* 134 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/* 137 */     String str6 = " select * from fnaWfDimTypeBrowserUser where userid = ? and fieldid = ?  and typeid in (" + str5.toString() + ") and workflowId = ? order by membergroupId desc";
/* 138 */     recordSet.executeQuery(str6, new Object[] { Integer.valueOf(this.user.getUID()), Integer.valueOf(i), str3 });
/* 139 */     while (recordSet.next()) {
/* 140 */       String str16 = Util.null2String(recordSet.getString("typeid"));
/* 141 */       String str17 = Util.null2String(recordSet.getString("memberid"));
/* 142 */       String str18 = Util.null2String(recordSet.getString("membergroupId"));
/*     */ 
/*     */       
/* 145 */       Map<String, String> map = (Map)hashMap1.get(str18);
/* 146 */       if (map == null) {
/* 147 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 148 */         hashMap.put(str16, str17);
/* 149 */         hashMap1.put(str18, hashMap); continue;
/*     */       } 
/* 151 */       map.put(str16, str17);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 156 */     for (Map.Entry<Object, Object> entry : hashMap1.entrySet()) {
/* 157 */       Map map = (Map)entry.getValue();
/* 158 */       if (map.size() != arrayList2.size()) {
/*     */         continue;
/*     */       }
/*     */       
/* 162 */       String str = "";
/* 163 */       for (String str16 : arrayList2) {
/* 164 */         String str17 = Util.null2String((String)map.get(str16));
/* 165 */         if (!"".equals(str)) {
/* 166 */           str = str + ",";
/*     */         }
/* 168 */         str = str + str17;
/*     */       } 
/* 170 */       hashSet.add(str);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 177 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 178 */     String str7 = " select a.typeId,b.fkVarchar,b.fkName from fnabudgetdimension_" + str2 + " a join fnadimensionmember_" + str2 + " b on a.id = b.dimensionid ";
/*     */     
/* 180 */     recordSet.executeQuery(str7, new Object[0]);
/* 181 */     while (recordSet.next()) {
/* 182 */       String str16 = Util.null2String(recordSet.getString("typeId"));
/* 183 */       String str17 = Util.null2String(recordSet.getString("fkVarchar"));
/* 184 */       String str18 = Util.null2String(recordSet.getString("fkName"));
/*     */       
/* 186 */       String str19 = str16 + "_" + str17;
/* 187 */       hashMap2.put(str19, str18);
/*     */     } 
/*     */ 
/*     */     
/* 191 */     String str8 = "";
/* 192 */     for (String str16 : hashSet) {
/* 193 */       ArrayList<String> arrayList3 = new ArrayList();
/* 194 */       String[] arrayOfString = str16.split(",");
/* 195 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 196 */         String str = arrayList1.get(b1);
/* 197 */         if ("1".equals(str)) {
/*     */ 
/*     */           
/* 200 */           String str21 = arrayList2.get(b1);
/* 201 */           String str22 = str21 + "_" + arrayOfString[b1];
/* 202 */           String str23 = (String)hashMap2.get(str22);
/* 203 */           arrayList3.add(str23);
/*     */         } 
/*     */       } 
/*     */       
/* 207 */       String str17 = arrayList3.get(arrayList3.size() - 1);
/* 208 */       String str18 = "";
/* 209 */       String str19 = "";
/* 210 */       for (byte b2 = 0; b2 < arrayList3.size(); b2++) {
/* 211 */         if (!"".equals(str18)) {
/* 212 */           str18 = str18 + str4;
/*     */         }
/* 214 */         if (b2 != arrayList3.size() - 1) {
/* 215 */           str18 = str18 + (String)arrayList3.get(b2);
/*     */         }
/* 217 */         if (!"".equals(str19)) {
/* 218 */           str19 = str19 + str4;
/*     */         }
/* 220 */         str19 = str19 + (String)arrayList3.get(b2);
/*     */       } 
/*     */       
/* 223 */       String str20 = str16.replace(",", "_fnaMulti_");
/* 224 */       str20 = str20 + "_fnaMulti_" + str1 + "_fnaMulti_" + str5.toString();
/*     */       
/* 226 */       if (str8.length() > 0) {
/* 227 */         str8 = str8 + " union all ";
/*     */       }
/*     */       
/* 230 */       str8 = str8 + " select '" + str20 + "' id, '" + str18 + "' supName, '" + str17 + "' lastName, '" + str19 + "' inputmemberName";
/* 231 */       if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "dm".equalsIgnoreCase(recordSet.getOrgindbtype()) || "st".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/* 232 */         str8 = str8 + " from dual ";
/*     */       }
/*     */     } 
/*     */     
/* 236 */     String str9 = "(" + str8 + ") ";
/* 237 */     if ("mysql".equalsIgnoreCase(recordSet.getDBType()) || "sqlserver".equalsIgnoreCase(recordSet.getDBType()) || "postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 238 */       str9 = str9 + " as a1";
/*     */     }
/* 240 */     if ("".equals(str8)) {
/* 241 */       return null;
/*     */     }
/*     */     
/* 244 */     String str10 = "10";
/* 245 */     String str11 = " id,supName,lastName,inputmemberName  ";
/* 246 */     String str12 = " from " + str9;
/* 247 */     String str13 = " where 1=1 ";
/*     */     
/* 249 */     (new BaseBean()).writeLog("list---sql: select " + str11 + str12 + str13);
/*     */     
/* 251 */     String str14 = "";
/* 252 */     String str15 = "id";
/*     */     
/* 254 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 255 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 256 */     arrayList.add(new SplitTableColBean("50%", "", "lastName", "lastName"));
/* 257 */     arrayList.add(new SplitTableColBean("50%", "", "supName", "supName"));
/* 258 */     arrayList.add((new SplitTableColBean("true", "inputmemberName")).setIsInputCol(BoolAttr.TRUE));
/*     */     
/* 260 */     return new SplitTableBean("FnaWfDimBrowserList", "none", str10, "FnaWfDimBrowserList", str11, str12, str13, str14, str15, "ASC", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean hastypeCondition(Map<String, Object> paramMap) {
/* 271 */     RecordSet recordSet = new RecordSet();
/* 272 */     recordSet.executeQuery("select * from FnaDimensionType ", new Object[0]);
/* 273 */     while (recordSet.next()) {
/* 274 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 275 */       String str2 = Util.null2String(paramMap.get("fnaCondition" + str1));
/* 276 */       if (!"".equals(str2)) {
/* 277 */         return true;
/*     */       }
/*     */     } 
/* 280 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 291 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 293 */     if (this.user == null) {
/* 294 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 295 */       return (Map)hashMap1;
/*     */     } 
/*     */     
/* 298 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 299 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 300 */     String str = Util.null2String(paramMap.get("wfid"));
/* 301 */     int i = Util.getIntValue((String)paramMap.get("fieldid"));
/* 302 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/* 305 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 306 */     recordSet.executeQuery(" select * from FnaDimensionType ", new Object[0]);
/* 307 */     while (recordSet.next()) {
/* 308 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 309 */       String str2 = Util.null2String(recordSet.getString("typeName"));
/* 310 */       hashMap2.put(str1, str2);
/*     */     } 
/*     */     
/* 313 */     recordSet.executeQuery("select * from fnaDimBrowserWf where fieldid = ? and workflowId = ? order by displayOrder ", new Object[] { Integer.valueOf(i), str });
/* 314 */     while (recordSet.next()) {
/* 315 */       String str1 = Util.null2String(recordSet.getString("typeid"));
/* 316 */       String str2 = Util.null2String(recordSet.getString("electShow"));
/* 317 */       String str3 = Util.null2String(recordSet.getString("advContion"));
/* 318 */       if ("1".equals(str2)) {
/* 319 */         SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, -1, "fnaCondition" + str1);
/* 320 */         searchConditionItem.setLabelcol(10);
/* 321 */         searchConditionItem.setFieldcol(14);
/* 322 */         searchConditionItem.setLabel((String)hashMap2.get(str1));
/* 323 */         if ("1".equals(str3)) {
/* 324 */           searchConditionItem.setIsQuickSearch(true);
/*     */         }
/* 326 */         arrayList.add(searchConditionItem);
/*     */       } 
/*     */     } 
/* 329 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/* 331 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 343 */     String str1 = FnaBrowserUtils.getAccountInfo(paramMap, this.user.getUID());
/* 344 */     if ("".equals(str1)) {
/* 345 */       return null;
/*     */     }
/*     */     
/* 348 */     AccountInfoComInfo accountInfoComInfo = new AccountInfoComInfo();
/* 349 */     String str2 = accountInfoComInfo.getNumberCode(str1);
/*     */     
/* 351 */     RecordSet recordSet = new RecordSet();
/* 352 */     String str3 = Util.null2String(paramMap.get("wfid"));
/* 353 */     int i = Util.getIntValue((String)paramMap.get("fieldid"));
/* 354 */     String str4 = "";
/*     */     
/* 356 */     ArrayList<String> arrayList1 = new ArrayList();
/*     */     
/* 358 */     String str5 = "";
/* 359 */     ArrayList<String> arrayList2 = new ArrayList();
/* 360 */     recordSet.executeQuery("select * from fnaDimBrowserWf where fieldid = ? and workflowId = ? order by displayOrder ", new Object[] { Integer.valueOf(i), str3 });
/* 361 */     while (recordSet.next()) {
/* 362 */       String str15 = Util.null2String(recordSet.getString("typeid"));
/* 363 */       str4 = Util.null2String(recordSet.getString("fnaseparator"));
/* 364 */       String str16 = Util.null2String(recordSet.getString("electShow"));
/* 365 */       arrayList1.add(str16);
/*     */       
/* 367 */       arrayList2.add(str15);
/* 368 */       if (!"".equals(str5)) {
/* 369 */         str5 = str5 + ",";
/*     */       }
/* 371 */       str5 = str5 + str15;
/*     */     } 
/* 373 */     if (arrayList2.size() == 0) {
/* 374 */       return null;
/*     */     }
/*     */ 
/*     */     
/* 378 */     TemplateFillUtil templateFillUtil = new TemplateFillUtil();
/* 379 */     String str6 = templateFillUtil.getFillDataTableName(str1, FnaAccTypeConstant.BUDGET_FILLDATA_GRO.intValue(), str5.toString(), arrayList2.size());
/* 380 */     if ("".equals(str6)) {
/* 381 */       return null;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 386 */     StringBuffer stringBuffer1 = new StringBuffer("'-1'");
/* 387 */     recordSet.executeQuery("select a.id from FnaBudgetTemplate_" + str2 + " a join FnaBudgetApproval_" + str2 + " b on a.approvalId = b.id  join FnaBudgetApprovalType_" + str2 + " c on b.approvalTypeId = c.id  join " + str6 + " d on d.templateId = a.id  where c.approvaTypelstatus = 1 and c.approvalFillDataSataus = 1 and c.apprvoalActivation = 1 ", new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 392 */     while (recordSet.next()) {
/* 393 */       stringBuffer1.append(",'").append(Util.null2String(recordSet.getString("id"))).append("'");
/*     */     }
/*     */ 
/*     */     
/* 397 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 398 */     StringBuffer stringBuffer3 = new StringBuffer();
/* 399 */     StringBuffer stringBuffer4 = new StringBuffer();
/* 400 */     for (String str15 : arrayList2) {
/* 401 */       String str16 = Util.null2String(paramMap.get("fnaCondition" + str15));
/*     */       
/* 403 */       if (stringBuffer2.length() > 0) {
/* 404 */         stringBuffer2.append(",");
/*     */       }
/* 406 */       stringBuffer2.append("budgetMember_").append(str15);
/*     */       
/* 408 */       if (stringBuffer3.length() > 0) {
/* 409 */         stringBuffer3.append(",");
/*     */       }
/* 411 */       stringBuffer3.append("budgetMember_").append(str15);
/*     */       
/* 413 */       if (!"".equals(str16)) {
/* 414 */         recordSet.executeQuery("select a.fkVarchar from FnaDimensionMember_" + str2 + " a  join FnaBudgetDimension_" + str2 + " b on a.dimensionId = b.id  where fkName like '%" + 
/*     */             
/* 416 */             StringEscapeUtils.escapeSql(str16) + "%' and  typeId = ?", new Object[] { str15 });
/*     */         
/* 418 */         StringBuffer stringBuffer = new StringBuffer();
/* 419 */         while (recordSet.next()) {
/* 420 */           String str = Util.null2String(recordSet.getString("fkVarchar"));
/* 421 */           if (stringBuffer.length() > 0) {
/* 422 */             stringBuffer.append(",");
/*     */           }
/* 424 */           stringBuffer.append("'").append(str).append("'");
/*     */         } 
/* 426 */         if (stringBuffer.length() > 0) {
/*     */           
/* 428 */           String[] arrayOfString = stringBuffer.toString().split(",");
/* 429 */           List<String> list = FnaCommon.initData1(arrayOfString);
/* 430 */           Integer integer = Integer.valueOf(list.size());
/* 431 */           if (stringBuffer4.length() > 0) {
/* 432 */             stringBuffer4.append(" and (1=2 ");
/* 433 */             for (byte b1 = 0; b1 < integer.intValue(); b1++) {
/* 434 */               stringBuffer4.append(" or budgetMember_").append(str15).append(" in(").append(list.get(b1)).append(")");
/*     */             }
/* 436 */             stringBuffer4.append(")"); continue;
/*     */           } 
/* 438 */           stringBuffer4.append("  (1=2 ");
/* 439 */           for (byte b = 0; b < integer.intValue(); b++) {
/* 440 */             stringBuffer4.append(" or budgetMember_").append(str15).append(" in(").append(list.get(b)).append(")");
/*     */           }
/* 442 */           stringBuffer4.append(")");
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 448 */     stringBuffer2.append(" from ").append(str6).append(" where 1=2 ");
/* 449 */     if (stringBuffer4.length() > 0) {
/* 450 */       stringBuffer2.append("  or (").append(stringBuffer4).append(") ");
/*     */     }
/* 452 */     stringBuffer2.append(" and templateId in (").append(stringBuffer1.toString()).append(") ");
/* 453 */     stringBuffer2.append(" group by ").append(stringBuffer3.toString());
/*     */     
/* 455 */     (new BaseBean()).writeLog("exeSql.toString():" + stringBuffer2.toString());
/*     */ 
/*     */     
/* 458 */     HashSet<String> hashSet = new HashSet();
/* 459 */     recordSet.executeQuery(" select " + stringBuffer2.toString(), new Object[0]);
/* 460 */     while (recordSet.next()) {
/* 461 */       String str = "";
/* 462 */       for (String str15 : arrayList2) {
/* 463 */         String str16 = Util.null2String(recordSet.getString("budgetMember_" + str15));
/* 464 */         if (!"".equals(str)) {
/* 465 */           str = str + ",";
/*     */         }
/* 467 */         str = str + str16;
/*     */       } 
/* 469 */       hashSet.add(str);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 474 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 475 */     recordSet.executeQuery("select * from FnaBudgetDimension_" + str2 + " a  join FnaDimensionMember_" + str2 + " b on a.id = b.dimensionId  where a.typeId in(" + str5 + ")", new Object[0]);
/*     */ 
/*     */     
/* 478 */     while (recordSet.next()) {
/* 479 */       String str15 = Util.null2String(recordSet.getString("fkVarchar"));
/* 480 */       String str16 = Util.null2String(recordSet.getString("fkName"));
/* 481 */       String str17 = Util.null2String(recordSet.getString("typeId"));
/* 482 */       String str18 = str17 + "_" + str15;
/* 483 */       hashMap.put(str18, str16);
/*     */     } 
/*     */ 
/*     */     
/* 487 */     String str7 = "";
/* 488 */     for (String str15 : hashSet) {
/* 489 */       ArrayList<String> arrayList3 = new ArrayList();
/* 490 */       String[] arrayOfString = str15.split(",");
/* 491 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 492 */         String str = arrayList1.get(b1);
/* 493 */         if ("1".equals(str)) {
/*     */ 
/*     */           
/* 496 */           String str18 = arrayList2.get(b1);
/* 497 */           String str19 = str18 + "_" + arrayOfString[b1];
/* 498 */           String str20 = (String)hashMap.get(str19);
/* 499 */           arrayList3.add(str20);
/*     */         } 
/*     */       } 
/* 502 */       String str16 = "";
/* 503 */       for (byte b2 = 0; b2 < arrayList3.size(); b2++) {
/* 504 */         if (!"".equals(str16)) {
/* 505 */           str16 = str16 + str4;
/*     */         }
/* 507 */         str16 = str16 + (String)arrayList3.get(b2);
/*     */       } 
/*     */       
/* 510 */       String str17 = str15.replace(",", "_fnaMulti_");
/* 511 */       str17 = str17 + "_fnaMulti_" + str1 + "_fnaMulti_" + str5.toString();
/*     */       
/* 513 */       if (str7.length() > 0) {
/* 514 */         str7 = str7 + " union all ";
/*     */       }
/*     */       
/* 517 */       str7 = str7 + " select '" + str17 + "' id,  '" + str16 + "' inputmemberName";
/* 518 */       if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "dm".equalsIgnoreCase(recordSet.getOrgindbtype()) || "st".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/* 519 */         str7 = str7 + " from dual ";
/*     */       }
/*     */     } 
/*     */     
/* 523 */     String str8 = "(" + str7 + ")";
/* 524 */     if ("mysql".equalsIgnoreCase(recordSet.getDBType()) || "sqlserver".equalsIgnoreCase(recordSet.getDBType()) || "postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 525 */       str8 = str8 + " as a1";
/*     */     }
/* 527 */     if ("".equals(str7)) {
/* 528 */       return null;
/*     */     }
/*     */     
/* 531 */     String str9 = "10";
/* 532 */     String str10 = " id,inputmemberName  ";
/* 533 */     String str11 = " from " + str8;
/* 534 */     String str12 = " where 1=1 ";
/*     */     
/* 536 */     (new BaseBean()).writeLog("222---list---sql: select " + str10 + str11 + str12);
/*     */     
/* 538 */     String str13 = "id";
/* 539 */     String str14 = "id";
/*     */     
/* 541 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 542 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 543 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(504430, this.user.getLanguage()), "inputmemberName", "inputmemberName")).setIsInputCol(BoolAttr.TRUE));
/*     */     
/* 545 */     return new SplitTableBean("FnaWfDimBrowserList", "none", str9, "FnaWfDimBrowserList", str10, str11, str12, str13, str14, "ASC", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<FnaBrowserTreeNodeBean> getTreeNodeInfo(Map<String, Object> paramMap) throws Exception {
/* 558 */     ArrayList<FnaBrowserTreeNodeBean> arrayList = new ArrayList();
/* 559 */     String str1 = FnaBrowserUtils.getAccountInfo(paramMap, this.user.getUID());
/* 560 */     if ("".equals(str1)) {
/* 561 */       return arrayList;
/*     */     }
/*     */ 
/*     */     
/* 565 */     RecordSet recordSet = new RecordSet();
/* 566 */     String str2 = Util.null2String(paramMap.get("wfid"));
/*     */     
/* 568 */     String str3 = Util.null2String(paramMap.get("id"));
/* 569 */     String str4 = "";
/* 570 */     ArrayList<String> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/* 573 */     ArrayList<String> arrayList2 = new ArrayList();
/* 574 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 575 */     int i = Util.getIntValue((String)paramMap.get("fieldid"));
/* 576 */     recordSet.executeQuery("select * from fnaDimBrowserWf where fieldid = ? and workflowId = ? order by displayOrder ", new Object[] { Integer.valueOf(i), str2 });
/* 577 */     while (recordSet.next()) {
/* 578 */       String str = Util.null2String(recordSet.getString("typeid"));
/* 579 */       str4 = Util.null2String(recordSet.getString("fnaseparator"));
/* 580 */       arrayList1.add(Util.null2String(recordSet.getString("electShow")));
/* 581 */       arrayList2.add(str);
/* 582 */       if (stringBuffer1.length() > 0) {
/* 583 */         stringBuffer1.append(",");
/*     */       }
/* 585 */       stringBuffer1.append(str);
/*     */     } 
/* 587 */     if (0 == arrayList2.size()) {
/* 588 */       return arrayList;
/*     */     }
/*     */     
/* 591 */     String[] arrayOfString = Util.splitString(str3, "_fnaMulti_");
/* 592 */     int j = arrayOfString.length;
/*     */     
/* 594 */     TemplateFillUtil templateFillUtil = new TemplateFillUtil();
/* 595 */     String str5 = templateFillUtil.getFillDataTableName(str1, FnaAccTypeConstant.BUDGET_FILLDATA_GRO.intValue(), stringBuffer1.toString(), arrayList2.size());
/*     */ 
/*     */     
/* 598 */     BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
/* 599 */     String str6 = budgetApprovalUtil.getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE.intValue());
/* 600 */     String str7 = budgetApprovalUtil.getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL.intValue());
/* 601 */     String str8 = budgetApprovalUtil.getTableName(str1, FnaAccTypeConstant.BUDGET_TEMPLATE.intValue());
/* 602 */     if ("".equals(str5) || "".equals(str6) || "".equals(str7) || "".equals(str8)) {
/* 603 */       return arrayList;
/*     */     }
/*     */     
/* 606 */     StringBuffer stringBuffer2 = new StringBuffer("'-1'");
/* 607 */     recordSet.executeQuery("select a.id from " + str8 + " a join " + str7 + " b on a.approvalId = b.id  join " + str6 + " c on b.approvalTypeId = c.id  where c.approvaTypelstatus = 1 and c.approvalFillDataSataus = 1 and apprvoalActivation = 1 ", new Object[0]);
/*     */ 
/*     */ 
/*     */     
/* 611 */     while (recordSet.next()) {
/* 612 */       stringBuffer2.append(",'").append(Util.null2String(recordSet.getString("id"))).append("'");
/*     */     }
/*     */     
/* 615 */     String str9 = (new BudgetApprovalUtil()).getTableName(str1, FnaAccTypeConstant.BUDGET_DIMENSION.intValue());
/* 616 */     String str10 = "FnaDimensionMember_" + str9.split("_")[1];
/* 617 */     StringBuffer stringBuffer3 = new StringBuffer();
/* 618 */     String str11 = "";
/* 619 */     if ("".equals(str3)) {
/* 620 */       str11 = arrayList2.get(0);
/* 621 */       stringBuffer3.append("select b.displayOrder,b.fkName,a.budgetMember_").append(str11).append("  from ").append(str5).append(" a join ")
/* 622 */         .append(str10).append(" b on a.budgetMember_").append(str11).append(" = b.fkVarchar ")
/* 623 */         .append(" join ").append(str9).append(" c on c.id = b.dimensionId where c.typeId = ").append(str11);
/*     */     } else {
/*     */       
/* 626 */       if (arrayOfString.length == arrayList2.size()) {
/* 627 */         return arrayList;
/*     */       }
/* 629 */       str11 = arrayList2.get(arrayOfString.length);
/* 630 */       stringBuffer3.append("select b.displayOrder,b.fkName,a.budgetMember_").append(str11).append(" from ").append(str5).append(" a join ")
/* 631 */         .append(str10).append(" b on a.budgetMember_").append(str11).append(" = b.fkVarchar ")
/* 632 */         .append(" join ").append(str9).append(" c on c.id = b.dimensionId  ")
/* 633 */         .append(" where 1=1 ");
/* 634 */       for (byte b = 0; b < j; b++) {
/* 635 */         stringBuffer3.append(" and a.budgetMember_").append(arrayList2.get(b)).append(" = '")
/* 636 */           .append(StringEscapeUtils.escapeSql(arrayOfString[b])).append("' ");
/*     */       }
/* 638 */       stringBuffer3.append(" and c.typeId = ").append(str11);
/*     */     } 
/*     */     
/* 641 */     stringBuffer3.append(" and a.templateId in (").append(stringBuffer2.toString()).append(") ").append(" group by  b.displayOrder,b.fkName,a.budgetMember_").append(str11).append(" order by b.displayOrder");
/*     */     
/* 643 */     (new BaseBean()).writeLog("tree---sql:" + stringBuffer3.toString());
/*     */     
/* 645 */     recordSet.executeQuery(stringBuffer3.toString(), new Object[0]);
/* 646 */     while (recordSet.next()) {
/* 647 */       String str12 = "";
/* 648 */       String str13 = "";
/* 649 */       String str14 = Util.null2String(recordSet.getString("budgetMember_" + str11));
/* 650 */       if ("".equals(str3)) {
/* 651 */         str12 = str14;
/*     */       } else {
/* 653 */         str12 = str3 + "_fnaMulti_" + str14;
/*     */       } 
/*     */       
/* 656 */       String str15 = Util.null2String(recordSet.getString("fkName"));
/*     */       
/* 658 */       boolean bool = false;
/* 659 */       if ("".equals(str3) || j < arrayList2.size() - 1) {
/* 660 */         bool = true;
/*     */       } else {
/* 662 */         str13 = dimName(str1, arrayList2, arrayList1, str12, str4);
/* 663 */         str12 = str12 + "_fnaMulti_" + str1 + "_fnaMulti_" + stringBuffer1.toString();
/*     */       } 
/*     */ 
/*     */       
/* 667 */       FnaBrowserTreeNodeBean fnaBrowserTreeNodeBean = new FnaBrowserTreeNodeBean();
/* 668 */       fnaBrowserTreeNodeBean.setId(str12);
/* 669 */       fnaBrowserTreeNodeBean.setName(str15);
/* 670 */       fnaBrowserTreeNodeBean.setPid(str3);
/* 671 */       fnaBrowserTreeNodeBean.setParent(bool);
/* 672 */       fnaBrowserTreeNodeBean.setCanClick(!bool);
/* 673 */       fnaBrowserTreeNodeBean.setOrgWholePathspan(str13);
/* 674 */       arrayList.add(fnaBrowserTreeNodeBean);
/*     */     } 
/* 676 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String dimName(String paramString1, List<String> paramList1, List<String> paramList2, String paramString2, String paramString3) {
/* 689 */     DimensionUtil dimensionUtil = new DimensionUtil();
/* 690 */     String[] arrayOfString = Util.splitString(paramString2, "_fnaMulti_");
/* 691 */     StringBuffer stringBuffer = new StringBuffer("");
/* 692 */     for (byte b = 0; b < paramList1.size(); b++) {
/* 693 */       String str = paramList2.get(b);
/* 694 */       if ("1".equals(str)) {
/*     */ 
/*     */         
/* 697 */         if (stringBuffer.length() > 0) {
/* 698 */           stringBuffer.append(paramString3);
/*     */         }
/* 700 */         stringBuffer.append(dimensionUtil.getMemberName(paramString1, paramList1.get(b), arrayOfString[b]));
/*     */       } 
/*     */     } 
/* 703 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   private String dimName(String paramString1, List<String> paramList1, List<String> paramList2, String paramString2, String paramString3, Map<String, String> paramMap) {
/* 717 */     String[] arrayOfString = Util.splitString(paramString2, "_fnaMulti_");
/* 718 */     StringBuffer stringBuffer = new StringBuffer("");
/* 719 */     for (byte b = 0; b < paramList1.size(); b++) {
/* 720 */       String str = paramList2.get(b);
/* 721 */       if ("1".equals(str)) {
/*     */ 
/*     */         
/* 724 */         if (stringBuffer.length() > 0) {
/* 725 */           stringBuffer.append(paramString3);
/*     */         }
/* 727 */         stringBuffer.append(paramMap.get((String)paramList1.get(b) + "_fnaMulti_" + arrayOfString[b]));
/*     */       } 
/*     */     } 
/*     */     
/* 731 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaMultiWfDimBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */