/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.engine.edc.biz.dataset.DataSetBiz;
/*    */ import com.engine.edc.entity.EdcDataSourceTable;
/*    */ import com.engine.edc.util.EDCUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.stream.Collectors;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class EdcLocalTablesService
/*    */   extends BrowserService {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 30 */     String str1 = Util.null2String(paramMap.get("dsName"));
/* 31 */     str1 = EDCUtil.isEmpty(str1) ? "$ECOLOGY_SYS_LOCAL_POOLNAME" : str1;
/* 32 */     String str2 = Util.null2String(paramMap.get("name"));
/*    */     
/* 34 */     int i = Util.getIntValue((String)paramMap.get("currentPage"), 1);
/* 35 */     int j = Util.getIntValue((String)paramMap.get("pageSize"), 10);
/*    */     
/* 37 */     DataSetBiz dataSetBiz = new DataSetBiz(str1);
/* 38 */     List list = dataSetBiz.listTable();
/*    */     
/* 40 */     list = (List)list.stream().filter(paramEdcDataSourceTable -> StringUtils.containsIgnoreCase(paramEdcDataSourceTable.getName(), paramString)).collect(Collectors.toList());
/*    */ 
/*    */     
/* 43 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 44 */     arrayList.add((new ListHeadBean("name", SystemEnv.getHtmlLabelName(33439, this.user.getLanguage()), 1, BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 45 */     arrayList.add(new ListHeadBean("text", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), 0));
/* 46 */     arrayList.add(new ListHeadBean("tableType", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), 0));
/*    */     
/* 48 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/* 49 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/* 50 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA_O.getTypeid()));
/* 51 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TOTAL, Integer.valueOf(list.size()));
/* 52 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CURRENT_PAGE, Integer.valueOf(i));
/* 53 */     hashMap.put(BrowserConstant.BROWSER_RESULT_PAGESIZE, Integer.valueOf(j));
/* 54 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 60 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 61 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 62 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 33439, "name", true));
/* 63 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 64 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 70 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 71 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("dsName"));
/* 72 */     str2 = EDCUtil.isEmpty(str2) ? "$ECOLOGY_SYS_LOCAL_POOLNAME" : str2;
/* 73 */     DataSetBiz dataSetBiz = new DataSetBiz(str2);
/* 74 */     List list = dataSetBiz.listTable();
/*    */     
/* 76 */     list = (List)list.stream().filter(paramEdcDataSourceTable -> StringUtils.containsIgnoreCase(paramEdcDataSourceTable.getName(), paramString)).collect(Collectors.toList());
/*    */     
/* 78 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/* 79 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EdcLocalTablesService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */