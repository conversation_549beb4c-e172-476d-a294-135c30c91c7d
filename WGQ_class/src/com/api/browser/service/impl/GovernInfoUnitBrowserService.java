/*    */ package com.api.browser.service.impl;
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.alibaba.fastjson.JSONArray;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BelongAttr;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileViewTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.general.Util;
/*    */ import weaver.general.browserData.BrowserManager;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class GovernInfoUnitBrowserService extends BrowserService {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 27 */     String str1 = Util.null2String(paramMap.get("name"));
/* 28 */     String str2 = Util.null2String(paramMap.get("subcompanyid"));
/* 29 */     String str3 = " where  t1.canceled = 0 or t1.canceled is null  ";
/* 30 */     if (!str1.equals("")) {
/* 31 */       str3 = str3 + " and t1.name like '%" + str1 + "%'";
/*    */     }
/* 33 */     if (!str2.equals("")) {
/* 34 */       str3 = str3 + " and subcompanyid =" + str2 + " ";
/*    */     }
/* 36 */     String str4 = " t1.showOrder ";
/* 37 */     String str5 = " t1.id,t1.name,t1.subcompanyid,t1.showOrder  ";
/* 38 */     String str6 = " info_customunit t1 ";
/*    */     
/* 40 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 41 */     arrayList.add((new SplitTableColBean("true", "id")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/* 42 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(502833, this.user.getLanguage()), "name", null, null, 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/* 43 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname", null, 0)).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/* 44 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str3, str4, "id", arrayList);
/* 45 */     splitTableBean.setSqlisdistinct("true");
/* 46 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 47 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 60 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 61 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 62 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 17728, "name", true));
/* 63 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subcompanyid", "164"));
/* 64 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 65 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 77 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 78 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 79 */     BrowserManager browserManager = new BrowserManager();
/* 80 */     browserManager.setType(this.browserType);
/* 81 */     browserManager.setOrderKey("t1.id");
/* 82 */     browserManager.setOrderWay("asc");
/* 83 */     String str2 = "  t1.canceled = 0 or t1.canceled is null  ";
/* 84 */     if (!str1.equals("")) {
/* 85 */       str2 = str2 + " and t1.name like '%" + str1 + "%'";
/*    */     }
/* 87 */     String str3 = browserManager.getResult(paramHttpServletRequest, "t1.id,t1.name", "info_customunit t1 ", str2, 30);
/* 88 */     JSONArray jSONArray = (JSONArray)JSON.parse(str3);
/* 89 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray);
/* 90 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/GovernInfoUnitBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */