/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BrowserTypeListService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  28 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  30 */     String str1 = Util.null2String(paramMap.get("noneedtree"));
/*  31 */     String str2 = Util.null2String(paramMap.get("excludeId"));
/*     */     
/*  33 */     String str3 = Util.null2String(paramMap.get("moduleCode"));
/*     */     
/*  35 */     boolean bool = (this.user == null) ? true : this.user.getLanguage();
/*  36 */     String str4 = "10, 11, 64, 6, 56, 5, 3, 26,235,243,246,224,225,14,15,267,261,33,266";
/*     */     
/*  38 */     if (str1.equals("1")) {
/*  39 */       str4 = str4 + ",256,257";
/*     */     }
/*  41 */     if (!Strings.isNullOrEmpty(str2)) {
/*  42 */       str4 = str4 + "," + str2;
/*     */     }
/*     */     
/*  45 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  47 */     JSONArray jSONArray = new JSONArray();
/*  48 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  49 */     ArrayList<String> arrayList = new ArrayList();
/*  50 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  51 */     String str5 = "select id,labelname,orderid,LABELID from workflow_browsertype where useable = 1";
/*  52 */     recordSet.executeSql(str5);
/*  53 */     while (recordSet.next()) {
/*  54 */       JSONObject jSONObject1 = new JSONObject();
/*     */ 
/*     */ 
/*     */       
/*  58 */       String str = Util.null2String(recordSet.getString("labelname"));
/*     */       
/*  60 */       if (!str.startsWith("~`~`")) {
/*  61 */         jSONObject1.put("groupname", (recordSet.getInt("LABELID") != -1) ? SystemEnv.getHtmlLabelName(recordSet.getInt("LABELID"), bool) : "");
/*     */       } else {
/*  63 */         jSONObject1.put("groupname", Util.formatMultiLang(recordSet.getString("labelname"), Util.null2String(Integer.valueOf(bool))));
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  72 */       jSONObject1.put("groupid", recordSet.getString("id"));
/*  73 */       jSONObject1.put("grouporderid", recordSet.getString("orderid"));
/*  74 */       arrayList.add(recordSet.getString("id"));
/*  75 */       hashMap2.put(recordSet.getString("id"), jSONObject1);
/*  76 */       hashMap3.put(recordSet.getString("id"), new JSONArray());
/*     */     } 
/*  78 */     str5 = "update workflow_browserurl set typeid = 13 where typeid is null";
/*  79 */     recordSet.executeSql(str5);
/*  80 */     str5 = "update workflow_browserurl set orderid = 0 where orderid is null";
/*  81 */     recordSet.executeSql(str5);
/*     */ 
/*     */ 
/*     */     
/*  85 */     String str6 = Util.null2String(paramMap.get("tempName"));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  90 */     str5 = "select w.typeid as groupid,w.id as itemid,w.labelid as itemlabel,w.orderid as orderid from  workflow_browserurl w left join HtmlLabelInfo h on w.labelid=h.indexid where h.languageid=" + bool + " and w.id not in (" + str4 + ") and w.useable = 1  ";
/*     */ 
/*     */ 
/*     */     
/*  94 */     String str7 = " order by groupid,orderid desc ";
/*     */     
/*  96 */     String str8 = "";
/*  97 */     if (StringUtils.isNotBlank(str3)) {
/*  98 */       str8 = " and w.id in (select browserid from workflow_browserurl_module where moduleCode=? )";
/*  99 */       recordSet.executeQuery(str5 + str8 + str7, new Object[] { str3 });
/*     */     } else {
/*     */       
/* 102 */       recordSet.executeQuery(str5 + str7, new Object[0]);
/*     */     } 
/*     */     
/* 105 */     while (recordSet.next()) {
/* 106 */       JSONObject jSONObject1 = new JSONObject();
/* 107 */       jSONObject1.put("key", recordSet.getString("itemid"));
/* 108 */       jSONObject1.put("itemorderid", recordSet.getString("orderid"));
/* 109 */       jSONObject1.put("value", SystemEnv.getHtmlLabelName(recordSet.getInt("itemlabel"), bool));
/* 110 */       if (null != hashMap3.get(recordSet.getString("groupid"))) {
/* 111 */         ((JSONArray)hashMap3.get(recordSet.getString("groupid"))).add(jSONObject1);
/*     */       }
/*     */     } 
/* 114 */     for (String str : arrayList) {
/* 115 */       if (null != hashMap3.get(str)) {
/*     */ 
/*     */         
/* 118 */         Object[] arrayOfObject1 = ((JSONArray)hashMap3.get(str)).toArray();
/* 119 */         Arrays.sort(arrayOfObject1, new BTComprator());
/* 120 */         ((JSONObject)hashMap2.get(str)).put("items", arrayOfObject1);
/*     */       } 
/*     */     } 
/* 123 */     jSONArray = JSONArray.fromObject(hashMap2.values());
/*     */     
/* 125 */     Object[] arrayOfObject = jSONArray.toArray();
/* 126 */     Arrays.sort(arrayOfObject, new BGComprator());
/*     */     
/* 128 */     JSONObject jSONObject = new JSONObject();
/* 129 */     jSONObject.put("groups", arrayOfObject);
/*     */     
/* 131 */     hashMap1.putAll((Map<?, ?>)jSONObject);
/* 132 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public JSONArray sortArray(JSONArray paramJSONArray, String paramString) {
/* 136 */     JSONArray jSONArray = new JSONArray();
/* 137 */     int[] arrayOfInt = new int[paramJSONArray.size()]; byte b;
/* 138 */     for (b = 0; b < paramJSONArray.size(); b++) {
/* 139 */       JSONObject jSONObject = (JSONObject)paramJSONArray.get(b);
/* 140 */       arrayOfInt[b] = Integer.parseInt(jSONObject.get(paramString).toString());
/*     */     } 
/* 142 */     Arrays.sort(arrayOfInt);
/* 143 */     for (b = 0; b < arrayOfInt.length; b++) {
/* 144 */       for (byte b1 = 0; b1 < paramJSONArray.size(); b1++) {
/* 145 */         JSONObject jSONObject = (JSONObject)paramJSONArray.get(b1);
/* 146 */         int i = Integer.parseInt(jSONObject.get(paramString).toString());
/* 147 */         if (i == arrayOfInt[b]) {
/* 148 */           jSONArray.add(jSONObject);
/*     */         }
/*     */       } 
/*     */     } 
/* 152 */     return jSONArray;
/*     */   }
/*     */   
/*     */   class BTComprator implements Comparator {
/*     */     public int compare(Object param1Object1, Object param1Object2) {
/* 157 */       JSONObject jSONObject1 = (JSONObject)param1Object1;
/* 158 */       JSONObject jSONObject2 = (JSONObject)param1Object2;
/* 159 */       int i = Integer.parseInt(jSONObject1.get("itemorderid").toString());
/* 160 */       int j = Integer.parseInt(jSONObject2.get("itemorderid").toString());
/*     */       
/* 162 */       return (i > j) ? 1 : -1;
/*     */     }
/*     */   }
/*     */   
/*     */   class BGComprator implements Comparator {
/*     */     public int compare(Object param1Object1, Object param1Object2) {
/* 168 */       JSONObject jSONObject1 = (JSONObject)param1Object1;
/* 169 */       JSONObject jSONObject2 = (JSONObject)param1Object2;
/* 170 */       int i = Integer.parseInt(jSONObject1.get("grouporderid").toString());
/* 171 */       int j = Integer.parseInt(jSONObject2.get("grouporderid").toString());
/*     */       
/* 173 */       return (i > j) ? 1 : -1;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 180 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 181 */     boolean bool = (this.user == null) ? true : this.user.getLanguage();
/* 182 */     String str1 = "";
/* 183 */     String str2 = "";
/* 184 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 185 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("excludeId"));
/* 186 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("moduleCode"));
/* 187 */     str3 = URLDecoder.decode(str3, "utf-8");
/* 188 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 190 */     if (!ConnectionPool.getInstance().isNewDB())
/*     */     {
/* 192 */       if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 193 */         str1 = str1 + " or f_GetPy(h.labelname) like '%" + str3.toUpperCase() + "%'";
/* 194 */         str2 = ",f_GetPy(h.labelname) py ";
/* 195 */       } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 196 */         str1 = str1 + " or [dbo].f_GetPy(h.labelname) like '%" + str3.toUpperCase() + "%'";
/* 197 */         str2 = ",[dbo].f_GetPy(h.labelname) py ";
/* 198 */       } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 199 */         str1 = str1 + " or getpinyin(h.labelname) like '%" + str3.toUpperCase() + "%'";
/* 200 */         str2 = ",getpinyin(h.labelname) py ";
/*     */       } 
/*     */     }
/*     */     
/* 204 */     String str6 = "select id,labelname" + str2 + "from workflow_browsertype where (useable is null or useable = 1)  and (labelname like '%" + str3 + "%'" + str1 + " ) order by orderid ";
/*     */ 
/*     */ 
/*     */     
/* 208 */     JSONObject jSONObject = new JSONObject();
/* 209 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 226 */     Map map = ParamUtil.request2Map(paramHttpServletRequest);
/*     */     
/* 228 */     String str7 = "10, 11, 64, 6, 56, 5, 3, 26,235,243,246,224,225,14,15,267,261,33,266";
/*     */     
/* 230 */     if (StringUtils.isNotBlank(str4))
/* 231 */       str7 = str7 + "," + str4; 
/* 232 */     String str8 = Util.null2String(map.get("noneedtree"));
/* 233 */     if (str8.equals("1")) {
/* 234 */       str7 = str7 + ",256,257";
/*     */     }
/*     */     
/* 237 */     str6 = "select wt.labelname as plabelname,h.labelname,w.typeid as groupid,w.id as itemid,w.labelid as itemlabel,w.orderid as orderid " + str2 + "from  workflow_browserurl w  left join HtmlLabelInfo h on w.labelid=h.indexid and h.languageid=" + String.valueOf(bool) + " left join workflow_browsertype wt on wt.id=w.typeid  where w.id not in (" + str7 + ")  and w.useable = 1  and (h.labelname like '%" + str3 + "%' " + str1 + " ) ";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 242 */     String str9 = " order by groupid,orderid desc ";
/*     */     
/* 244 */     if (StringUtils.isNotBlank(str5)) {
/* 245 */       String str = " and w.id in (select browserid from workflow_browserurl_module where moduleCode=? )";
/* 246 */       recordSet.executeQuery(str6 + str + str9, new Object[] { str5 });
/*     */     } else {
/*     */       
/* 249 */       recordSet.executeQuery(str6 + str9, new Object[0]);
/*     */     } 
/*     */ 
/*     */     
/* 253 */     while (recordSet.next()) {
/* 254 */       JSONObject jSONObject1 = new JSONObject();
/* 255 */       jSONObject1.put("id", recordSet.getString("itemid"));
/* 256 */       jSONObject1.put("itemorderid", recordSet.getString("orderid"));
/* 257 */       jSONObject1.put("name", recordSet.getString("labelname"));
/*     */       
/* 259 */       jSONObject1.put("title", recordSet.getString("plabelname") + "-" + recordSet.getString("labelname"));
/* 260 */       jSONObject1.put("parenttitle", recordSet.getString("plabelname"));
/* 261 */       jSONArray.add(jSONObject1);
/*     */     } 
/*     */ 
/*     */     
/* 265 */     jSONObject.put("datas", jSONArray);
/*     */     
/* 267 */     hashMap.putAll((Map<?, ?>)jSONObject);
/* 268 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BrowserTypeListService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */