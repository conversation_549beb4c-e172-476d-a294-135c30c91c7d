/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.maintenance.BudgetfeeTypeComInfo;
/*     */ import weaver.fna.maintenance.FnaSystemSetComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaBudgetfeeTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  55 */     if (this.user == null) {
/*  56 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  57 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  60 */     int i = Util.getIntValue(Util.null2String(paramMap.get("feeperiod")), 0);
/*     */     
/*  62 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  63 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15409, "qryName").setIsQuickSearch(true));
/*     */     
/*  67 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  68 */     if (i == 1) {
/*  69 */       arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(541, this.user.getLanguage()), true));
/*  70 */     } else if (i == 2) {
/*  71 */       arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(543, this.user.getLanguage()), true));
/*  72 */     } else if (i == 3) {
/*  73 */       arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(538, this.user.getLanguage()), true));
/*  74 */     } else if (i == 4) {
/*  75 */       arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(546, this.user.getLanguage()), true));
/*     */     } else {
/*  77 */       arrayList1.add(new SearchConditionOption("0", ""));
/*  78 */       arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(541, this.user.getLanguage())));
/*  79 */       arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(543, this.user.getLanguage())));
/*  80 */       arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(538, this.user.getLanguage())));
/*  81 */       arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(546, this.user.getLanguage())));
/*     */     } 
/*     */     
/*  84 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 15388, "feeperiod", arrayList1));
/*  85 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/*  86 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  88 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  99 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 101 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 102 */     String str2 = Util.null2String(paramMap.get("alllevel"));
/* 103 */     String[] arrayOfString = str1.split(",");
/*     */     
/* 105 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 107 */     BudgetfeeTypeComInfo budgetfeeTypeComInfo = new BudgetfeeTypeComInfo();
/* 108 */     FnaSystemSetComInfo fnaSystemSetComInfo = new FnaSystemSetComInfo();
/* 109 */     String str3 = Util.null2String(fnaSystemSetComInfo.get_separator()).trim();
/* 110 */     int i = Util.getIntValue(fnaSystemSetComInfo.get_enableDispalyAll());
/*     */     
/* 112 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/* 115 */     if ("1".equals(str2)) {
/* 116 */       ArrayList<String> arrayList2 = new ArrayList();
/* 117 */       String str = " select a.allSupSubjectIds from FnaBudgetfeeType a where a.id in (  ";
/* 118 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 119 */         str = str + "?";
/* 120 */         if (b1 != arrayOfString.length - 1) {
/* 121 */           str = str + ",";
/*     */         }
/*     */       } 
/* 124 */       str = str + ")";
/* 125 */       recordSet.executeQuery(str, new Object[] { Arrays.asList(arrayOfString) });
/* 126 */       while (recordSet.next()) {
/* 127 */         String str4 = Util.null2String(recordSet.getString("allSupSubjectIds"));
/* 128 */         arrayList2.add(str4 + "%");
/*     */       } 
/*     */       
/* 131 */       StringBuffer stringBuffer = new StringBuffer();
/* 132 */       stringBuffer.append(" select a.id, a.name, a.codeName, a.feelevel, a.feeperiod, a.Archive, a.isEditFeeType, a.isEditFeeTypeId, a.supsubject ");
/* 133 */       stringBuffer.append(" from FnaBudgetfeeType a ");
/* 134 */       stringBuffer.append(" where (1=2 ");
/* 135 */       for (byte b2 = 0; b2 < arrayList2.size(); b2++) {
/* 136 */         stringBuffer.append(" or a.allSupSubjectIds like ? ");
/*     */       }
/* 138 */       stringBuffer.append(" ) ");
/* 139 */       stringBuffer.append(" and (a.Archive is null or a.Archive = 0) ");
/* 140 */       stringBuffer.append(" and (a.isEditFeeType = 1) ");
/*     */       
/* 142 */       str1 = "";
/* 143 */       recordSet.executeQuery(stringBuffer.toString(), new Object[] { arrayList2 });
/* 144 */       while (recordSet.next()) {
/* 145 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 146 */         String str4 = Util.null2String(recordSet.getString("id"));
/* 147 */         String str5 = Util.null2String(recordSet.getString("name"));
/* 148 */         String str6 = str5;
/* 149 */         String str7 = str5;
/* 150 */         String str8 = recordSet.getString("supsubject");
/* 151 */         if (i == 1) {
/* 152 */           str6 = budgetfeeTypeComInfo.getSubjectFullName(str4, str3);
/*     */         }
/* 154 */         if ("".equals(str8.trim())) {
/* 155 */           str7 = "";
/*     */         } else {
/* 157 */           str7 = budgetfeeTypeComInfo.getSubjectFullName(str8, str3);
/*     */         } 
/*     */         
/* 160 */         hashMap1.put("id", str4);
/* 161 */         hashMap1.put("name", str5);
/* 162 */         hashMap1.put("fullName", str6);
/* 163 */         hashMap1.put("highName", str7);
/*     */         
/* 165 */         str1 = str1 + str4 + ",";
/* 166 */         arrayList.add(hashMap1);
/*     */       } 
/*     */       
/* 169 */       if (!"".equals(str1)) {
/* 170 */         str1 = str1.substring(0, str1.length() - 1);
/*     */       }
/*     */     } else {
/* 173 */       StringBuffer stringBuffer = new StringBuffer();
/* 174 */       stringBuffer.append(" select a.id, a.name, a.codeName, a.feelevel, a.feeperiod, a.Archive, a.isEditFeeType, a.isEditFeeTypeId, a.supsubject ");
/* 175 */       stringBuffer.append(" from FnaBudgetfeeType a ");
/* 176 */       stringBuffer.append(" where 1 = 1 ");
/* 177 */       stringBuffer.append(" and (a.Archive is null or a.Archive = 0) ");
/* 178 */       stringBuffer.append(" and (a.isEditFeeType = 1) ");
/* 179 */       stringBuffer.append(" and id in (").append(str1).append(")");
/*     */       
/* 181 */       recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/*     */       
/* 183 */       while (recordSet.next()) {
/* 184 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 185 */         String str4 = Util.null2String(recordSet.getString("id"));
/* 186 */         String str5 = Util.null2String(recordSet.getString("name"));
/* 187 */         String str6 = str5;
/* 188 */         String str7 = str5;
/* 189 */         String str8 = recordSet.getString("supsubject");
/* 190 */         if (i == 1) {
/* 191 */           str6 = budgetfeeTypeComInfo.getSubjectFullName(str4, str3);
/*     */         }
/* 193 */         if ("".equals(str8.trim())) {
/* 194 */           str7 = "";
/*     */         } else {
/* 196 */           str7 = budgetfeeTypeComInfo.getSubjectFullName(str8, str3);
/*     */         } 
/*     */         
/* 199 */         hashMap1.put("id", str4);
/* 200 */         hashMap1.put("name", str5);
/* 201 */         hashMap1.put("fullName", str6);
/* 202 */         hashMap1.put("highName", str7);
/*     */         
/* 204 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/*     */     
/* 208 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 209 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 210 */     arrayList1.add((new ListHeadBean("fullName", BoolAttr.TRUE)).setIsInputCol(BoolAttr.TRUE));
/* 211 */     arrayList1.add(new ListHeadBean("name", "", 1));
/* 212 */     arrayList1.add(new ListHeadBean("highName", ""));
/*     */     
/* 214 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 215 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 216 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 218 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 232 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 234 */     if (this.user == null) {
/* 235 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 236 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 239 */     int i = Util.getIntValue(Util.null2String(paramMap.get("qryType")), 0);
/*     */     
/* 241 */     if (i == 1) {
/* 242 */       List<Map<String, String>> list = getTableList(paramMap);
/* 243 */       ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 244 */       arrayList.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 245 */       arrayList.add((new ListHeadBean("fullName", BoolAttr.TRUE)).setIsInputCol(BoolAttr.TRUE));
/* 246 */       arrayList.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(585, this.user.getLanguage()), 1));
/* 247 */       arrayList.add(new ListHeadBean("highName", SystemEnv.getHtmlLabelName(18428, this.user.getLanguage())));
/*     */       
/* 249 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/* 250 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/* 251 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } else {
/* 253 */       int j = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/* 254 */       if (j == 0) {
/* 255 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 256 */         arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 257 */         arrayList.add((new ListHeadBean("fullName", BoolAttr.TRUE)).setIsInputCol(BoolAttr.TRUE));
/* 258 */         arrayList.add(new ListHeadBean("name", "", 1));
/* 259 */         arrayList.add(new ListHeadBean("highName", ""));
/* 260 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */         
/* 262 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 263 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 264 */         if (list.size() == 0) {
/* 265 */           BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 266 */           browserTreeNode.setId("-1");
/* 267 */           browserTreeNode.setName(SystemEnv.getHtmlLabelName(33026, this.user.getLanguage()));
/* 268 */           browserTreeNode.setPid("0");
/* 269 */           browserTreeNode.setParent(true);
/* 270 */           browserTreeNode.setType("0");
/* 271 */           browserTreeNode.setCanClick(false);
/* 272 */           browserTreeNode.setIcon("icon-coms-LargeArea");
/*     */           
/* 274 */           list.add(browserTreeNode);
/*     */         } 
/*     */         
/* 277 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } else {
/* 279 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 280 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 281 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } 
/*     */     } 
/* 284 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, String>> getTableList(Map<String, Object> paramMap) throws Exception {
/* 295 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 297 */     String str1 = Util.null2String(paramMap.get("qryName")).trim();
/* 298 */     int i = Util.getIntValue(Util.null2String(paramMap.get("feeperiod")), 0);
/* 299 */     String str2 = Util.null2String(paramMap.get("description")).trim();
/*     */ 
/*     */     
/* 302 */     BudgetfeeTypeComInfo budgetfeeTypeComInfo = new BudgetfeeTypeComInfo();
/* 303 */     FnaSystemSetComInfo fnaSystemSetComInfo = new FnaSystemSetComInfo();
/* 304 */     int j = Util.getIntValue(fnaSystemSetComInfo.get_enableDispalyAll());
/* 305 */     String str3 = Util.null2String(fnaSystemSetComInfo.get_separator()).trim();
/*     */     
/* 307 */     StringBuffer stringBuffer = new StringBuffer();
/* 308 */     stringBuffer.append(" select a.id, a.name, a.codename, a.supsubject, a.feelevel, a.supsubject ");
/* 309 */     stringBuffer.append(" from FnaBudgetfeeType a ");
/* 310 */     stringBuffer.append(" where (a.Archive is null or a.Archive = 0) ");
/* 311 */     stringBuffer.append(" and a.isEditFeeType > 0 ");
/*     */     
/* 313 */     if (str1 != "") {
/* 314 */       stringBuffer.append(" and a.name like '%" + StringEscapeUtils.escapeSql(str1) + "%' ");
/*     */     }
/* 316 */     if (i > 0) {
/* 317 */       stringBuffer.append(" and a.feeperiod = ").append(i);
/*     */     }
/* 319 */     if (str2 != "") {
/* 320 */       stringBuffer.append(" and a.description like '%" + StringEscapeUtils.escapeSql(str2) + "%' ");
/*     */     }
/* 322 */     stringBuffer.append(" ORDER BY a.feeperiod,a.displayOrder,a.codename, a.name, a.id ");
/*     */     
/* 324 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 326 */     recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/* 327 */     while (recordSet.next()) {
/* 328 */       String str4 = recordSet.getString("id");
/* 329 */       String str5 = recordSet.getString("name");
/* 330 */       String str6 = recordSet.getString("codeName");
/* 331 */       String str7 = recordSet.getString("supsubject");
/*     */       
/* 333 */       String str8 = str5;
/* 334 */       String str9 = str5;
/* 335 */       if (j == 1) {
/* 336 */         str8 = budgetfeeTypeComInfo.getSubjectFullName(str4, str3);
/*     */       }
/* 338 */       if ("".equals(str7.trim())) {
/* 339 */         str9 = "";
/*     */       } else {
/* 341 */         str9 = budgetfeeTypeComInfo.getSubjectFullName(str7, str3);
/*     */       } 
/*     */ 
/*     */       
/* 345 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 346 */       hashMap.put("id", str4);
/* 347 */       hashMap.put("name", str5);
/* 348 */       hashMap.put("codeName", str6);
/* 349 */       hashMap.put("fullName", str8);
/* 350 */       hashMap.put("highName", str9);
/*     */       
/* 352 */       arrayList.add(hashMap);
/*     */     } 
/*     */ 
/*     */     
/* 356 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) {
/* 366 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*     */     
/* 368 */     String str1 = Util.null2String(paramMap.get("id"));
/*     */     
/* 370 */     int i = Util.getIntValue(Util.null2String(paramMap.get("feeperiod")), 0);
/*     */ 
/*     */     
/* 373 */     BudgetfeeTypeComInfo budgetfeeTypeComInfo = new BudgetfeeTypeComInfo();
/* 374 */     FnaSystemSetComInfo fnaSystemSetComInfo = new FnaSystemSetComInfo();
/* 375 */     int j = Util.getIntValue(fnaSystemSetComInfo.get_enableDispalyAll());
/* 376 */     String str2 = Util.null2String(fnaSystemSetComInfo.get_separator()).trim();
/*     */     
/* 378 */     RecordSet recordSet1 = new RecordSet();
/* 379 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 381 */     if ("".equals(str1)) {
/* 382 */       str1 = "0";
/*     */     }
/*     */     
/* 385 */     int k = Util.getIntValue(str1);
/*     */     
/* 387 */     StringBuffer stringBuffer = new StringBuffer();
/* 388 */     stringBuffer.append(" select a.id, a.name, a.codename, a.feelevel, a.feeperiod, a.isEditFeeType, a.isEditFeeTypeId ");
/* 389 */     stringBuffer.append(" from FnaBudgetfeeType a ");
/* 390 */     stringBuffer.append(" where a.supsubject = ").append(k);
/* 391 */     if (i > 0) {
/* 392 */       stringBuffer.append(" and a.feeperiod = ").append(i);
/*     */     }
/* 394 */     stringBuffer.append(" and (a.Archive is null or a.Archive = 0) ");
/* 395 */     stringBuffer.append(" ORDER BY a.feeperiod,a.displayOrder,a.codename, a.name, a.id ");
/*     */     
/* 397 */     recordSet1.executeQuery(stringBuffer.toString(), new Object[0]);
/* 398 */     while (recordSet1.next()) {
/* 399 */       String str3 = recordSet1.getString("id");
/* 400 */       String str4 = recordSet1.getString("name");
/* 401 */       int m = Util.getIntValue(recordSet1.getString("feelevel"));
/* 402 */       int n = Util.getIntValue(recordSet1.getString("isEditFeeType"));
/* 403 */       int i1 = Util.getIntValue(recordSet1.getString("isEditFeeTypeId"));
/*     */       
/* 405 */       boolean bool1 = (n > 0) ? true : false;
/*     */       
/* 407 */       String str5 = "icon-coms-Branch";
/*     */       
/* 409 */       String str6 = str4;
/* 410 */       if (j == 1) {
/* 411 */         str6 = budgetfeeTypeComInfo.getSubjectFullName(str3, str2);
/*     */       }
/*     */       
/* 414 */       boolean bool2 = true;
/* 415 */       String str7 = "select count(*) cnt from FnaBudgetfeeType a where a.supsubject = " + Util.getIntValue(str3);
/* 416 */       recordSet2.executeQuery(str7, new Object[0]);
/* 417 */       if (recordSet2.next() && recordSet2.getInt("cnt") > 0) {
/* 418 */         bool2 = true;
/*     */       } else {
/* 420 */         bool2 = false;
/*     */       } 
/*     */       
/* 423 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 424 */       browserTreeNode.setId(str3);
/* 425 */       browserTreeNode.setName(str4);
/* 426 */       browserTreeNode.setPid(str1);
/* 427 */       browserTreeNode.setParent(bool2);
/* 428 */       browserTreeNode.setType(String.valueOf(m));
/* 429 */       browserTreeNode.setCanClick((bool1 == true));
/* 430 */       browserTreeNode.setIcon(str5);
/* 431 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 432 */       hashMap.put("fullName", str6);
/* 433 */       hashMap.put("isEditFeeTypeId", Integer.valueOf(i1));
/* 434 */       browserTreeNode.setProp(hashMap);
/* 435 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 437 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaBudgetfeeTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */