/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SearchConditionOption;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SmsServiceBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 33 */     String str1 = Util.null2String(paramMap.get("isTemplete"));
/* 34 */     String str2 = Util.null2String(paramMap.get("name"));
/* 35 */     String str3 = Util.null2String(paramMap.get("type"));
/* 36 */     String str4 = Util.null2String(paramMap.get("clazzname"));
/* 37 */     String str5 = Util.null2String(paramMap.get("keyword"));
/*    */     
/* 39 */     String str6 = "where 1=1 ";
/* 40 */     if (str1.equals("1")) {
/* 41 */       str6 = str6 + " and interfaceType = 1 ";
/*    */     } else {
/* 43 */       str6 = str6 + " and interfaceType = 0 ";
/*    */     } 
/* 45 */     if (str2 != null && !"".equals(str2)) {
/* 46 */       str6 = str6 + " and  lower(t1.name) like  lower('%" + str2 + "%') ";
/*    */     }
/* 48 */     if (str3 != null && !"".equals(str3)) {
/* 49 */       str6 = str6 + " and lower(t1.type) = lower('" + str3 + "') ";
/*    */     }
/* 51 */     if (str4 != null && !"".equals(str4)) {
/* 52 */       str6 = str6 + " and  lower(t1.clazzname) like  lower('%" + str4 + "%') ";
/*    */     }
/* 54 */     if (str5 != null && !"".equals(str5)) {
/* 55 */       str6 = str6 + " and  lower(t1.keyword) like  lower('%" + str5 + "%') ";
/*    */     }
/* 57 */     int i = Util.getPerpageLog();
/* 58 */     if (i < 10) i = 10;
/*    */ 
/*    */     
/* 61 */     String str7 = " t1.* ";
/* 62 */     String str8 = " sms_interface t1";
/* 63 */     String str9 = " dsporder asc";
/*    */     
/* 65 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 66 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name"));
/* 67 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(23683, this.user.getLanguage()), "clazzname", "clazzname")).setIsPrimarykey(BoolAttr.TRUE).setIsInputCol(BoolAttr.TRUE));
/* 68 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(2095, this.user.getLanguage()), "keyword", "keyword"));
/* 69 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "type", "type"));
/* 70 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(454, this.user.getLanguage()), "remark", "remark"));
/*    */     
/* 72 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str8, str6, str9, "id", arrayList);
/* 73 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 74 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 84 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 85 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 86 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 87 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true));
/* 88 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 89 */     arrayList1.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/* 90 */     arrayList1.add(new SearchConditionOption("HTTP", "HTTP"));
/* 91 */     arrayList1.add(new SearchConditionOption("WebService", "WebService"));
/* 92 */     arrayList1.add(new SearchConditionOption("SDK", "SDK"));
/* 93 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 63, "type", arrayList1));
/* 94 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 2095, "keyword"));
/* 95 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 96 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SmsServiceBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */