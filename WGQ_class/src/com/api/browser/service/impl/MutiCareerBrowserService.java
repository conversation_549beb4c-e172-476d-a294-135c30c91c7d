/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.job.EducationLevelComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MutiCareerBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  32 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  33 */     JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/*  34 */     EducationLevelComInfo educationLevelComInfo = new EducationLevelComInfo();
/*     */     
/*  36 */     String str1 = Util.null2String(paramMap.get("lastname"));
/*  37 */     String str2 = Util.null2String(paramMap.get("educationlevel"));
/*  38 */     String str3 = Util.null2String(paramMap.get("sex"));
/*  39 */     String str4 = Util.null2String(paramMap.get("jobtitle"));
/*  40 */     String str5 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  41 */     String str6 = " where 1 = 1 ";
/*  42 */     if (!str1.equals("")) {
/*  43 */       str6 = str6 + " and a.lastname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */     }
/*  45 */     if (!str2.equals("")) {
/*  46 */       str6 = str6 + " and a.educationlevel='" + str2 + "' ";
/*     */     }
/*  48 */     if (!str3.equals("")) {
/*  49 */       str6 = str6 + " and a.sex = '" + str3 + "' ";
/*     */     }
/*  51 */     if (!str4.equals("")) {
/*  52 */       str6 = str6 + " and b.careername = '" + str4 + "' ";
/*     */     }
/*  54 */     if (!"".equals(str5)) {
/*  55 */       str6 = str6 + " and a.id in (" + str5 + ")";
/*     */     }
/*  57 */     str6 = SqlUtils.replaceFirstAnd(str6);
/*  58 */     String str7 = " a.id,a.lastname,a.educationlevel,a.sex,b.careername ";
/*  59 */     String str8 = " HrmCareerApply a left join HrmCareerInvite b on a.jobtitle = b.id left join HrmJobTitles c on b.careername = c.id  ";
/*     */     
/*  61 */     RecordSet recordSet = new RecordSet();
/*  62 */     recordSet.executeSql("select " + str7 + " from " + str8 + str6 + " order by a.id");
/*  63 */     HashMap<Object, Object> hashMap2 = null;
/*  64 */     while (recordSet.next()) {
/*  65 */       hashMap2 = new HashMap<>();
/*  66 */       String str = recordSet.getString("id");
/*  67 */       hashMap2.put(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, str);
/*  68 */       hashMap2.put("id", Util.null2String(str));
/*  69 */       hashMap2.put("lastname", Util.null2String(recordSet.getString("lastname")));
/*  70 */       hashMap2.put("sex", getSexLabel(Util.null2String(recordSet.getString("sex")), this.user.getLanguage()));
/*  71 */       hashMap2.put("educationlevel", educationLevelComInfo.getEducationLevelname(recordSet.getString("educationlevel")));
/*  72 */       hashMap2.put("careername", jobTitlesComInfo.getJobTitlesname(Util.null2String(recordSet.getString("careername"))));
/*  73 */       arrayList.add(hashMap2);
/*     */     } 
/*     */ 
/*     */     
/*  77 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  78 */     arrayList1.add(new ListHeadBean(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, BoolAttr.TRUE));
/*  79 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  80 */     ListHeadBean listHeadBean = new ListHeadBean("lastname", SystemEnv.getHtmlLabelName(413, this.user.getLanguage()), 1, BoolAttr.TRUE);
/*  81 */     listHeadBean.setOldWidth("25%");
/*  82 */     arrayList1.add(listHeadBean);
/*  83 */     listHeadBean = new ListHeadBean("sex", SystemEnv.getHtmlLabelName(416, this.user.getLanguage()));
/*  84 */     listHeadBean.setOldWidth("25%");
/*  85 */     arrayList1.add(listHeadBean);
/*  86 */     listHeadBean = new ListHeadBean("educationlevel", SystemEnv.getHtmlLabelName(818, this.user.getLanguage()));
/*  87 */     listHeadBean.setOldWidth("25%");
/*  88 */     arrayList1.add(listHeadBean);
/*  89 */     listHeadBean = new ListHeadBean("careername", SystemEnv.getHtmlLabelName(1856, this.user.getLanguage()));
/*  90 */     listHeadBean.setOldWidth("25%");
/*  91 */     arrayList1.add(listHeadBean);
/*     */     
/*  93 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  94 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  95 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  96 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSexLabel(String paramString, int paramInt) {
/* 117 */     if ("0".equals(paramString))
/* 118 */       return SystemEnv.getHtmlLabelName(417, paramInt); 
/* 119 */     if ("1".equals(paramString))
/* 120 */       return SystemEnv.getHtmlLabelName(418, paramInt); 
/* 121 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 127 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 128 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 129 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 131 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 413, "lastname", true));
/*     */     
/* 133 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 134 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(417, this.user.getLanguage())));
/* 135 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(418, this.user.getLanguage())));
/* 136 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 416, "sex", arrayList1));
/*     */     
/* 138 */     String str = "select distinct b.careername from HrmCareerApply a left join HrmCareerInvite b on a.jobtitle = b.id left join HrmJobTitles c on b.careername = c.id";
/*     */     
/* 140 */     RecordSet recordSet = new RecordSet();
/* 141 */     recordSet.executeSql(str);
/* 142 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 143 */     JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 144 */     while (recordSet.next()) {
/* 145 */       String str1 = recordSet.getString("careername");
/* 146 */       String str2 = jobTitlesComInfo.getJobTitlesname(str1);
/* 147 */       arrayList2.add(new SearchConditionOption(str1, Util.toScreen(str2, this.user.getLanguage())));
/*     */     } 
/* 149 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 1856, "jobtitle", arrayList2));
/*     */     
/* 151 */     ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 152 */     EducationLevelComInfo educationLevelComInfo = new EducationLevelComInfo();
/* 153 */     while (educationLevelComInfo.next()) {
/* 154 */       arrayList3.add(new SearchConditionOption(educationLevelComInfo.getEducationLevelid(), Util.toScreen(educationLevelComInfo.getEducationLevelname(), this.user.getLanguage())));
/*     */     }
/* 156 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 818, "educationlevel", arrayList3));
/* 157 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 158 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MutiCareerBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */