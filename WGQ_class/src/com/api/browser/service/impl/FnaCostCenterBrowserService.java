/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCostCenterBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  44 */     if (this.user == null) {
/*  45 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  46 */       return (Map)hashMap;
/*     */     } 
/*  48 */     String str = Util.null2String(paramMap.get("flag"));
/*     */     
/*  50 */     if (!"true".equals(str)) {
/*  51 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  52 */       List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/*  53 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */     } else {
/*  55 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  56 */       SplitTableBean splitTableBean = getTableList(paramMap);
/*  57 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } 
/*     */     
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  73 */     if (this.user == null) {
/*  74 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  75 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  78 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  79 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  81 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "fcccode"));
/*  82 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 515, "fccname"));
/*  83 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  85 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) throws Exception {
/*  97 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*     */     
/*  99 */     RecordSet recordSet1 = new RecordSet();
/* 100 */     RecordSet recordSet2 = new RecordSet();
/* 101 */     String str1 = Util.null2String(paramMap.get("id"));
/* 102 */     String str2 = Util.null2String(paramMap.get("defaultId"));
/*     */     
/* 104 */     if ("".equals(str1)) {
/* 105 */       str1 = "0";
/*     */     }
/*     */     
/* 108 */     if ("".equals(str1)) {
/* 109 */       String str3 = "0";
/* 110 */       String str4 = SystemEnv.getHtmlLabelName(515, this.user.getLanguage());
/* 111 */       String str5 = "0";
/*     */       
/* 113 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 114 */       browserTreeNode.setId(str3);
/* 115 */       browserTreeNode.setName(str4);
/* 116 */       browserTreeNode.setType(str5);
/* 117 */       browserTreeNode.setIsParent(true);
/* 118 */       arrayList.add(browserTreeNode);
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 123 */       String str = "select a.id, a.type, a.name, a.code, a.Archive from FnaCostCenter a  where a.supFccId = " + Util.getIntValue(str1) + " and a.type = 0  and a.id <> " + str2 + " ORDER BY a.type, a.code, a.name, a.id ";
/*     */       
/* 125 */       recordSet1.executeSql(str);
/* 126 */       while (recordSet1.next()) {
/* 127 */         boolean bool; String str3 = recordSet1.getString("id");
/* 128 */         int i = Util.getIntValue(recordSet1.getString("type"));
/* 129 */         String str4 = recordSet1.getString("name");
/* 130 */         int j = Util.getIntValue(recordSet1.getString("Archive"), 0);
/*     */         
/* 132 */         String str5 = str4;
/* 133 */         if (j == 1) {
/* 134 */           str5 = str5 + "(" + SystemEnv.getHtmlLabelName(22205, this.user.getLanguage()) + ")";
/*     */           
/*     */           continue;
/*     */         } 
/* 138 */         String str6 = "/images/treeimages/home16_wev8.gif";
/*     */         
/* 140 */         String str7 = "select count(*) cnt from FnaCostCenter a where a.supFccId = " + Util.getIntValue(str3);
/* 141 */         recordSet2.executeSql(str7);
/* 142 */         if (recordSet2.next() && recordSet2.getInt("cnt") > 0) {
/* 143 */           bool = true;
/*     */         } else {
/* 145 */           bool = false;
/*     */         } 
/*     */         
/* 148 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 149 */         browserTreeNode.setId(str3);
/* 150 */         browserTreeNode.setName(str5);
/* 151 */         browserTreeNode.setIsParent(bool);
/* 152 */         browserTreeNode.setType(String.valueOf(i));
/* 153 */         browserTreeNode.setIcon(str6);
/* 154 */         browserTreeNode.setCanClick(true);
/* 155 */         arrayList.add(browserTreeNode);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 161 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 172 */     String str1 = Util.null2String(paramMap.get("fccname"));
/* 173 */     String str2 = Util.null2String(paramMap.get("fcccode"));
/* 174 */     String str3 = Util.null2String(paramMap.get("defaultId"));
/* 175 */     String str4 = "6";
/*     */     
/* 177 */     String str5 = " a.id,a.name,a.code ";
/* 178 */     String str6 = " FnaCostCenter a";
/* 179 */     String str7 = " where (a.Archive is null or a.Archive = 0) and a.type = 0 ";
/*     */     
/* 181 */     if (!"".equals(str1)) {
/* 182 */       str7 = str7 + " and a.name like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/* 184 */     if (!"".equals(str2)) {
/* 185 */       str7 = str7 + " and a.code like '%" + StringEscapeUtils.escapeSql(str2) + "%' ";
/*     */     }
/*     */     
/* 188 */     RecordSet recordSet = new RecordSet();
/* 189 */     String str8 = " WITH allsub(id)  as (  SELECT id FROM fnacostcenter where id=" + str3 + "   UNION ALL SELECT a.id FROM fnacostcenter a,allsub b where a.supFccid = b.id  ) select * from allsub";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 194 */     recordSet.execute(str8);
/* 195 */     String str9 = "";
/* 196 */     StringBuffer stringBuffer = new StringBuffer();
/* 197 */     stringBuffer.append("(");
/* 198 */     while (recordSet.next()) {
/* 199 */       str9 = Util.null2String(recordSet.getString("id"));
/* 200 */       stringBuffer.append("'");
/* 201 */       stringBuffer.append(str9);
/* 202 */       stringBuffer.append("',");
/*     */     } 
/* 204 */     stringBuffer.append("'-1')");
/* 205 */     str7 = str7 + " and a.id not in " + stringBuffer + " ";
/* 206 */     String str10 = " a.code,a.name ";
/* 207 */     String str11 = "a.id";
/*     */     
/* 209 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 210 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 211 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name"));
/* 212 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage()), "code", "code", "weaver.fna.general.FnaCommon.escapeHtml"));
/*     */ 
/*     */     
/* 215 */     SplitTableBean splitTableBean = new SplitTableBean("FccBrowserList", "none", str4, "FccBrowserList", str5, str6, str7, str10, str11, "ASC", arrayList);
/*     */     
/* 217 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 219 */     return splitTableBean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaCostCenterBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */