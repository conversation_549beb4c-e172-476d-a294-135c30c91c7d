/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.ConnectionPool;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowRuleBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */     
/* 31 */     String str1 = Util.null2String(paramMap.get("rulename"));
/*    */ 
/*    */     
/* 34 */     String str2 = " rulesrc=3 ";
/* 35 */     if (!str1.equals("")) str2 = str2 + " and rulename like '%" + str1 + "%'"; 
/* 36 */     String str3 = " id,rulename,condit ";
/* 37 */     String str4 = " rule_base ";
/* 38 */     String str5 = " id ";
/*    */     
/* 40 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 41 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 42 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(19829, this.user.getLanguage()), "rulename", null, 1)).setIsInputCol(BoolAttr.TRUE));
/* 43 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(15364, this.user.getLanguage()), "condit", null, 0));
/*    */     
/* 45 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "id", arrayList);
/* 46 */     splitTableBean.setSqlsortway("ASC");
/* 47 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 48 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 54 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 55 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 56 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 57 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 19829, "rulename", true));
/* 58 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 63 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 64 */     RecordSet recordSet = new RecordSet();
/* 65 */     String str2 = "where 1=1 ";
/* 66 */     if (!"".equals(str1)) {
/* 67 */       str2 = str2 + " and (rulename like '%" + str1 + "%' ";
/* 68 */       if (!ConnectionPool.getInstance().isNewDB())
/*    */       {
/* 70 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 71 */           str2 = str2 + " or f_GetPy(rulename) like '%" + str1.toUpperCase() + "%'";
/* 72 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 73 */           str2 = str2 + " or [dbo].f_GetPy(rulename) like '%" + str1.toUpperCase() + "%'";
/* 74 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 75 */           str2 = str2 + " or getpinyin(rulename) like '%" + str1.toUpperCase() + "%'";
/*    */         }  } 
/* 77 */       str2 = str2 + ")";
/*    */     } 
/* 79 */     recordSet.executeQuery("select id,rulename,condit from rule_base " + str2 + " order by id", new Object[0]);
/* 80 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 81 */     while (recordSet.next()) {
/* 82 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 83 */       hashMap1.put("id", recordSet.getString("id"));
/* 84 */       hashMap1.put("name", recordSet.getString("rulename"));
/* 85 */       arrayList.add(hashMap1);
/*    */     } 
/* 87 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 88 */     hashMap.put("datas", arrayList);
/* 89 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowRuleBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */