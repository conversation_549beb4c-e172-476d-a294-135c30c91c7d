/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.common.SplitPageTagFormat;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsSetManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ScheduleShiftsBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     String str1 = StringUtil.vString(paramMap.get("field001"));
/*  41 */     String str2 = StringUtil.vString(paramMap.get("field003"));
/*  42 */     String str3 = StringUtil.vString(paramMap.get("field006"));
/*  43 */     String str4 = StringUtil.vString(paramMap.get("field002"));
/*     */     
/*  45 */     String str5 = "id, tId, field001, field003, field004, field005, field006, field002, field007, last_modification_time,id as workTimeId";
/*  46 */     String str6 = "from (select t2.id, t.id as tId, t.field001, t.field003, t.field004, t.field005, t.field006, t.field002, t.field007, t.last_modification_time from hrm_schedule_shifts_set t left join hrm_schedule_shifts_set_id t2 on t.id = t2.field001 where t.delflag = 0) t";
/*  47 */     String str7 = " ";
/*  48 */     if (str1.length() > 0) {
/*  49 */       str7 = str7 + " and field001 like '%" + str1 + "%'";
/*     */     }
/*  51 */     if (str2.length() > 0) {
/*  52 */       str7 = str7 + " and field003 = " + str2;
/*     */     }
/*  54 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  55 */     if (StringUtil.vString(manageDetachComInfo.getDetachable()).equals("1")) {
/*  56 */       String str = "";
/*  57 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  58 */       ArrayList arrayList1 = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "HrmScheduling:set");
/*  59 */       for (byte b = 0; b < arrayList1.size(); ) { str = str + ((str.length() == 0) ? "" : ",") + StringUtil.vString(arrayList1.get(b)); b++; }
/*  60 */        str7 = str7 + " and field002 in (" + (StringUtil.isNull(str) ? "-99999" : str) + ")";
/*     */     } 
/*  62 */     str7 = SqlUtils.replaceFirstAnd(str7);
/*     */     
/*  64 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  65 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  66 */     arrayList.add((new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(125818, this.user.getLanguage()), "field001", "field003")).setIsInputCol(BoolAttr.TRUE));
/*  67 */     arrayList.add(new SplitTableColBean("17%", SystemEnv.getHtmlLabelName(125819, this.user.getLanguage()), "field003", "field003", "com.api.browser.service.impl.ScheduleShiftsBrowserService.getField003Name", "column:field004+column:field005+" + this.user.getLanguage()));
/*  68 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(125820, this.user.getLanguage()), "field006", "field006", "com.api.browser.service.impl.ScheduleShiftsBrowserService.colFormat", this.user.getLanguage() + ""));
/*  69 */     arrayList.add(new SplitTableColBean("43%", SystemEnv.getHtmlLabelName(125799, this.user.getLanguage()), "workTimeId", "workTimeId", "com.api.browser.service.impl.ScheduleShiftsBrowserService.getWorkTime", "+column:tId+," + this.user.getLanguage()));
/*     */     
/*  71 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str7, "last_modification_time", "id", arrayList);
/*  72 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  73 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getField003Name(String paramString1, String paramString2) {
/*  77 */     HrmScheduleShiftsSetManager hrmScheduleShiftsSetManager = new HrmScheduleShiftsSetManager();
/*  78 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  79 */     String str1 = arrayOfString[0];
/*  80 */     String str2 = arrayOfString[1];
/*  81 */     String str3 = arrayOfString[2];
/*  82 */     return hrmScheduleShiftsSetManager.getField003Name(str3, paramString1, str1, str2);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getWorkTime(String paramString1, String paramString2) {
/*  87 */     SplitPageTagFormat splitPageTagFormat = new SplitPageTagFormat();
/*  88 */     return splitPageTagFormat.colFormat(paramString1, "{cmd:class[weaver.hrm.schedule.manager.HrmScheduleShiftsDetailManager.getWorkTime(" + paramString2 + ")]}");
/*     */   }
/*     */   
/*     */   public String colFormat(String paramString1, String paramString2) {
/*  92 */     SplitPageTagFormat splitPageTagFormat = new SplitPageTagFormat();
/*  93 */     return splitPageTagFormat.colFormat("", "{cmd:array[" + paramString2 + ";default=125837,1=125899]}");
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  98 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  99 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 100 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 101 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 102 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 125818, "field001", true));
/* 103 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 104 */     arrayList1.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/* 105 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(125827, this.user.getLanguage())));
/* 106 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(125828, this.user.getLanguage())));
/* 107 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(125823, this.user.getLanguage())));
/* 108 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(125824, this.user.getLanguage())));
/* 109 */     arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(125825, this.user.getLanguage())));
/* 110 */     arrayList1.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(125826, this.user.getLanguage())));
/* 111 */     arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(19516, this.user.getLanguage())));
/* 112 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 125819, "field003", arrayList1));
/*     */     
/* 114 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ScheduleShiftsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */