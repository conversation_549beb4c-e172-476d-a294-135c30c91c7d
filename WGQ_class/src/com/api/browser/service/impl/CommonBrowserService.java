/*      */ package com.api.browser.service.impl;
/*      */ 
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.browser.bean.BrowserTreeNode;
/*      */ import com.api.browser.bean.ListHeadBean;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.service.BrowserService;
/*      */ import com.api.browser.util.BoolAttr;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.BrowserDataType;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import java.net.URLDecoder;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.Collections;
/*      */ import java.util.Comparator;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.integration.logging.Logger;
/*      */ import weaver.integration.logging.LoggerFactory;
/*      */ import weaver.integration.page.PageUtil;
/*      */ import weaver.interfaces.workflow.browser.BaseBrowser;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.interfaces.workflow.browser.BrowserBean;
/*      */ import weaver.interfaces.workflow.browser.bean.BaseBrowserInfo;
/*      */ import weaver.interfaces.workflow.browser.browsercache.BrowserCacheAction;
/*      */ import weaver.interfaces.workflow.browser.cfg.DataShowConfig;
/*      */ import weaver.interfaces.workflow.browser.util.PublicSqlDealUtil;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CommonBrowserService
/*      */   extends BrowserService
/*      */ {
/*   44 */   FormmodeBrowserService formmodeBrowserService = new FormmodeBrowserService();
/*      */ 
/*      */ 
/*      */   
/*   48 */   private Logger newlog = LoggerFactory.getLogger(CommonBrowserService.class);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*      */     Map<String, Object> map;
/*   58 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*   59 */     String str2 = Util.null2String(paramMap.get("ismobile"));
/*   60 */     String str3 = Util.null2String(paramMap.get("type"));
/*   61 */     String[] arrayOfString = str3.split("\\|");
/*   62 */     str3 = arrayOfString[0];
/*   63 */     str3 = URLDecoder.decode(str3);
/*   64 */     if (str3.indexOf("browser.") == -1) {
/*   65 */       str3 = "browser." + str3;
/*      */     }
/*   67 */     Browser browser = (Browser)StaticObj.getServiceByFullname(str3, Browser.class);
/*   68 */     String str4 = Util.null2String(browser.getFrom());
/*   69 */     if ("1".equals(str4)) {
/*   70 */       this.formmodeBrowserService.setUser(this.user);
/*   71 */       if (str1 != "" && str2.equalsIgnoreCase("1")) {
/*   72 */         return this.formmodeBrowserService.getMultBrowserDestData(paramMap);
/*      */       }
/*   74 */       return this.formmodeBrowserService.getBrowserData(paramMap);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*   79 */     int i = Util.getIntValue((String)paramMap.get("pageSize"), 10);
/*   80 */     int j = Util.getIntValue(Util.null2String(paramMap.get("min")), 1);
/*   81 */     int k = Util.getIntValue(Util.null2String(paramMap.get("max")), 10);
/*   82 */     int m = 1;
/*   83 */     Object object = paramMap.get("current");
/*   84 */     if (object != null) {
/*   85 */       m = Util.getIntValue(Util.null2String(object), 1);
/*   86 */       j = i * (m - 1) + 1;
/*   87 */       k = i * m;
/*      */     } else {
/*   89 */       m = j / i + 1;
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   95 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*   96 */     String str5 = Util.null2String(paramMap.get("node"));
/*   97 */     String str6 = Util.null2String(paramMap.get("names"));
/*   98 */     String str7 = Util.null2String(paramMap.get("currenttime"));
/*      */     
/*  100 */     String str8 = Util.null2String(paramMap.get("showOrder"));
/*      */     
/*  102 */     String str9 = Util.null2String(Util.null2String(paramMap.get("isreport")), "0");
/*  103 */     Map<String, String> map1 = paramMap.containsKey("consearch") ? dealFormParams2(paramMap, str7, false) : dealFormParams(paramMap, str7, false);
/*      */     
/*  105 */     List<String> list = new ArrayList();
/*  106 */     if (!"".equals(str1)) {
/*  107 */       String[] arrayOfString1 = str1.split(BrowserConstant.BROWSER_SPILIT);
/*  108 */       list = Arrays.asList(arrayOfString1);
/*      */     } 
/*      */     
/*  111 */     String str10 = this.user.getUID() + "";
/*  112 */     String str11 = Util.null2String(browser.getHref());
/*  113 */     str11 = Util.null2String(browser.getHref("" + str10, str11));
/*  114 */     String str12 = Util.null2String(browser.getShowtree());
/*  115 */     if ("1".equals(str12)) {
/*  116 */       if (str1 != "" && str2.equalsIgnoreCase("1")) {
/*  117 */         List list1 = getTreeBrowserList(str5, browser, str3, str9, map1, paramMap);
/*  118 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  119 */         arrayList.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  120 */         arrayList.add(new ListHeadBean("pid", BoolAttr.TRUE));
/*  121 */         arrayList.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/*  122 */         List<Map<String, String>> list2 = DealMultiTree(list1, list);
/*  123 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*  124 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list2);
/*      */       } else {
/*  126 */         map = getTreeMap(str5, browser, str3, str9, map1, paramMap);
/*      */       } 
/*      */     } else {
/*  129 */       map = getSingleBroserData(paramMap, browser, str3, str9, map1, str6, str10, j, k, str8, list);
/*      */     } 
/*  131 */     StaticObj.getInstance().putObject("browser.operatoruser.from", "0");
/*      */     
/*  133 */     if (str1 != "" && str2.equalsIgnoreCase("1")) {
/*  134 */       map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*      */     }
/*      */     
/*  137 */     map.put("current", Integer.valueOf(m));
/*  138 */     map.put("pageSize", Integer.valueOf(i));
/*  139 */     return map;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*      */     Map<String, Object> map;
/*  153 */     String str1 = Util.null2String(paramMap.get("type"));
/*  154 */     String[] arrayOfString = str1.split("\\|");
/*  155 */     str1 = arrayOfString[0];
/*  156 */     str1 = URLDecoder.decode(str1);
/*  157 */     if (str1.indexOf("browser.") == -1) {
/*  158 */       str1 = "browser." + str1;
/*      */     }
/*  160 */     Browser browser = (Browser)StaticObj.getServiceByFullname(str1, Browser.class);
/*  161 */     String str2 = Util.null2String(browser.getFrom());
/*  162 */     if ("1".equals(str2)) {
/*  163 */       this.formmodeBrowserService.setUser(this.user);
/*  164 */       return this.formmodeBrowserService.getMultBrowserDestData(paramMap);
/*      */     } 
/*      */     
/*  167 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  168 */     String str3 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  169 */     ArrayList arrayList = Util.TokenizerString(str3, ",");
/*      */     
/*  171 */     String str4 = Util.null2String(browser.getShowtree());
/*  172 */     if ("1".equals(str4)) {
/*  173 */       String str5 = Util.null2String(paramMap.get("currenttime"));
/*  174 */       Map<String, String> map1 = dealFormParams(paramMap, str5, true);
/*  175 */       String str6 = Util.null2String(paramMap.get("node"));
/*  176 */       String str7 = Util.null2String(paramMap.get("name"));
/*  177 */       String str8 = Util.null2String(Util.null2String(paramMap.get("isreport")), "0");
/*  178 */       List list = getTreeBrowserList(str6, browser, str1, str8, map1, paramMap);
/*  179 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  180 */       arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  181 */       arrayList1.add(new ListHeadBean("pid", BoolAttr.TRUE));
/*  182 */       arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/*  183 */       List<Map<String, String>> list1 = DealMultiTree(list, arrayList);
/*      */       
/*  185 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  186 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list1);
/*      */     }
/*  188 */     else if (arrayList != null && arrayList.size() > 0) {
/*  189 */       map = getBrowserData(paramMap);
/*      */     } else {
/*      */       
/*  192 */       map.put(BrowserConstant.BROWSER_RESULT_DATA, new ArrayList());
/*      */     } 
/*      */     
/*  195 */     map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  196 */     return map;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> getMultiBroserMap(Map<String, Object> paramMap, Browser paramBrowser, String paramString1, String paramString2, Map<String, String> paramMap1, String paramString3, String paramString4, List paramList) {
/*  214 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */ 
/*      */     
/*  217 */     String str1 = Util.null2String(paramBrowser.getOutPageURL());
/*  218 */     String str2 = Util.null2String(paramBrowser.getFrom());
/*  219 */     String str3 = paramBrowser.getSearch() + " ";
/*  220 */     String str4 = paramBrowser.getSearchByName() + " ";
/*  221 */     if ("".equals(str3.trim())) {
/*  222 */       paramBrowser = (Browser)StaticObj.getServiceByFullname(paramString1, Browser.class);
/*  223 */       str1 = Util.null2String(paramBrowser.getOutPageURL());
/*  224 */       str2 = Util.null2String(paramBrowser.getFrom());
/*  225 */       str3 = paramBrowser.getSearch() + " ";
/*      */     } 
/*  227 */     if (paramString2.equals("1")) {
/*  228 */       str3 = removeParam(str3);
/*  229 */       str4 = removeParam(str4);
/*      */     } 
/*  231 */     paramBrowser.initBaseBrowser("", paramString1, str2);
/*  232 */     String str5 = paramBrowser.getNamefield() + "s";
/*  233 */     int i = paramBrowser.getDatafrom();
/*  234 */     Map map1 = paramBrowser.getSearchfieldMap();
/*  235 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  236 */     initSearchValueMap(map1, paramMap, hashMap2);
/*  237 */     Map map2 = paramBrowser.getParamvalues();
/*  238 */     initSearchValueMap(map2, paramMap, map2);
/*  239 */     paramBrowser.setParamvalues(map2);
/*  240 */     str3 = replaceFieldValue(str3);
/*      */     
/*  242 */     Map<String, String> map = dealSearch(str3, str4, "", paramMap1, true);
/*  243 */     str3 = map.get("search");
/*  244 */     str4 = map.get("searchByName");
/*  245 */     if (!str1.equals("")) {
/*  246 */       return null;
/*      */     }
/*  248 */     List list = searchForList(paramBrowser, str2, i, paramString3, paramString4, str3, str4, hashMap2);
/*      */     
/*  250 */     Map map3 = paramBrowser.getShowfieldMap();
/*  251 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/*  252 */     dealTempList(list, str2, arrayList, map3, false, paramList);
/*  253 */     List<ListHeadBean> list1 = buildHeadBeans(str2, paramBrowser, map3, str5);
/*  254 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, list1);
/*  255 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  256 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  257 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> getSingleBroserData(Map<String, Object> paramMap, Browser paramBrowser, String paramString1, String paramString2, Map<String, String> paramMap1, String paramString3, String paramString4, int paramInt1, int paramInt2, String paramString5, List paramList) {
/*  275 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */ 
/*      */     
/*  278 */     String str1 = Util.null2String(paramBrowser.getOutPageURL());
/*  279 */     String str2 = Util.null2String(paramBrowser.getFrom());
/*  280 */     int i = paramBrowser.getDatafrom();
/*  281 */     paramBrowser.initBaseBrowser("", paramString1, str2);
/*  282 */     String str3 = paramBrowser.getNamefield() + "s";
/*  283 */     Map map1 = paramBrowser.getSearchfieldMap();
/*  284 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  285 */     initSearchValueMap(map1, paramMap, hashMap2);
/*  286 */     String str4 = paramBrowser.getDatafrom() + "";
/*  287 */     BaseBrowserInfo baseBrowserInfo = paramBrowser.getBaseBrowserInfo();
/*  288 */     String str5 = Util.null2String(baseBrowserInfo.getUnconditionalQuery());
/*  289 */     String str6 = Util.null2String(baseBrowserInfo.getIsPhyPage());
/*  290 */     String str7 = baseBrowserInfo.getDbtype();
/*  291 */     boolean bool = PageUtil.isSupportPage(str7);
/*  292 */     if (str4.equals("0")) {
/*  293 */       Map<String, Object> map4 = getValuesMap(paramMap);
/*  294 */       Map map5 = paramBrowser.getParamvalues();
/*  295 */       if (map5 == null || map5.size() == 0) {
/*  296 */         map5 = (Map)paramBrowser.getBaseBrowserInfo().getValuesbak().get("value");
/*      */       }
/*  298 */       Map map6 = initSearchValueMap(map5, map4);
/*  299 */       initSearchValueMap(map5, map4, map5);
/*  300 */       paramBrowser.setParamvalues(map6);
/*      */     } 
/*  302 */     if (str2.equals("2") && i == 2) {
/*  303 */       Map map4 = getValuesMap(paramMap);
/*  304 */       paramBrowser.setRequestFormInfoForImport(map4);
/*      */     } 
/*  306 */     String str8 = paramBrowser.getSearch() + " ";
/*  307 */     String str9 = paramBrowser.getSearchByName() + " ";
/*      */ 
/*      */     
/*  310 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  311 */     int j = Util.getIntValue(Util.null2String(paramMap.get("pageSize")), 10);
/*  312 */     hashMap3.put("startNo", Integer.valueOf(paramInt1));
/*  313 */     hashMap3.put("endNo", Integer.valueOf(paramInt2));
/*  314 */     hashMap3.put("pagesize", Integer.valueOf(j));
/*  315 */     int k = paramInt1 / j + 1;
/*  316 */     hashMap3.put("pageNo", Integer.valueOf(k));
/*  317 */     hashMap3.put("showOrder", paramString5);
/*      */ 
/*      */     
/*  320 */     if (paramList != null && paramList.size() > 0) {
/*  321 */       hashMap2 = new HashMap<>();
/*      */     }
/*  323 */     if (k != 1 || hashMap2.size() > 0 || (paramList != null && paramList.size() > 0))
/*      */     {
/*  325 */       if (i == 3) {
/*  326 */         str8 = paramBrowser.getSearchByName();
/*      */       
/*      */       }
/*  329 */       else if (str2.equals("2")) {
/*  330 */         str8 = paramBrowser.getSearchquery();
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  336 */     if (paramString2.equals("1")) {
/*  337 */       if (i == 3) {
/*  338 */         str8 = str8.replaceAll("[']+[$]+[_a-zA-Z0-9]+[$]+[']+[\\s]*", "''");
/*  339 */         str8 = str8.replaceAll("[$]+[_a-zA-Z0-9]+[$]+[\\s]*", "0");
/*  340 */         str8 = str8.replaceAll("[']+[\\{]+[?]+[_a-zA-Z0-9]+[\\}]+[']+[\\s]*", "''");
/*  341 */         str8 = str8.replaceAll("[\\{]+[?]+[_a-zA-Z0-9]+[\\}]+[\\s]*", "0");
/*      */         
/*  343 */         str9 = str9.replaceAll("[']+[$]+[_a-zA-Z0-9]+[$]+[']+[\\s]*", "''");
/*  344 */         str9 = str9.replaceAll("[$]+[_a-zA-Z0-9]+[$]+[\\s]*", "0");
/*  345 */         str9 = str9.replaceAll("[']+[\\{]+[?]+[_a-zA-Z0-9]+[\\}]+[']+[\\s]*", "''");
/*  346 */         str9 = str9.replaceAll("[\\{]+[?]+[_a-zA-Z0-9]+[\\}]+[\\s]*", "0");
/*      */       } else {
/*  348 */         str8 = removeParam(str8);
/*  349 */         str9 = removeParam(str9);
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  355 */     str8 = replaceFieldValue(str8);
/*      */     
/*  357 */     Map<String, String> map = dealSearch(str8, str9, str1, paramMap1, false);
/*  358 */     str1 = map.get("outPageURL");
/*  359 */     str8 = map.get("search");
/*  360 */     str9 = map.get("searchByName");
/*  361 */     if (!str1.equals("")) {
/*  362 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  363 */       hashMap.put(BrowserConstant.BROWSER_PC_URL, str1);
/*  364 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, hashMap);
/*  365 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.URL_DATA.getTypeid()));
/*  366 */       return (Map)hashMap1;
/*      */     } 
/*      */     
/*  369 */     Map map2 = paramBrowser.getShowfieldMap();
/*  370 */     List list = paramBrowser.getShowfieldList();
/*  371 */     if (hashMap2.size() == 0 && str5.equals("1")) {
/*  372 */       List<ListHeadBean> list2 = buildHeadBeans(str2, paramBrowser, list, str3);
/*  373 */       list2.add(new ListHeadBean(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, BoolAttr.TRUE));
/*  374 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, list2);
/*  375 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, new ArrayList());
/*  376 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA_O.getTypeid()));
/*  377 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_TOTAL, Integer.valueOf(0));
/*  378 */       if (DataShowConfig.CUSTOMER_SHOWSTYLE) {
/*  379 */         JSONObject jSONObject = buildJsonTemplate(str2, list, str3);
/*  380 */         hashMap1.put(BrowserConstant.BROWSER_MOBILE_TEMPLATE, jSONObject);
/*      */       } 
/*      */     } 
/*      */     
/*  384 */     Map<String, Object> map3 = null;
/*  385 */     if (1 == i && str6.equals("1") && str2.equals("2") && !"1".equals(paramBrowser.getShowtree()) && bool) {
/*  386 */       map3 = PhysicsSearchForListWithPage(paramBrowser, paramString4, str8, paramInt1, j, paramString5, hashMap2, paramList);
/*  387 */     } else if (i == 4) {
/*  388 */       map3 = paramBrowser.search(paramString4, paramMap1, hashMap2, hashMap3, paramList);
/*      */     } else {
/*      */       
/*  391 */       if (paramList != null && paramList.size() > 0 && (hashMap2 == null || hashMap2.size() == 0)) {
/*  392 */         hashMap2 = new HashMap<>();
/*  393 */         hashMap2.put("datashowset_backshowdata", "1");
/*      */       } 
/*  395 */       List list2 = searchForList(paramBrowser, str2, i, paramString3, paramString4, str8, str9, hashMap2);
/*  396 */       map3 = setDataMap(paramBrowser, list2, paramInt1, paramInt2, map2, str2, paramString5, paramList);
/*      */     } 
/*  398 */     List<ListHeadBean> list1 = buildHeadBeans(str2, paramBrowser, list, str3);
/*  399 */     list1.add(new ListHeadBean(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, BoolAttr.TRUE));
/*  400 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, list1);
/*  401 */     if (map3 == null) {
/*  402 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, new ArrayList());
/*  403 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_TOTAL, Integer.valueOf(0));
/*      */     } else {
/*  405 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, map3.get("dataAll"));
/*  406 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_TOTAL, Integer.valueOf(Util.getIntValue(Util.null2String(map3.get("recordCount")), 0)));
/*      */     } 
/*      */     
/*  409 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA_O.getTypeid()));
/*  410 */     if (DataShowConfig.CUSTOMER_SHOWSTYLE) {
/*  411 */       JSONObject jSONObject = buildJsonTemplate(str2, list, str3);
/*  412 */       hashMap1.put(BrowserConstant.BROWSER_MOBILE_TEMPLATE, jSONObject);
/*      */     } 
/*  414 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getTreeMap(String paramString1, Browser paramBrowser, String paramString2, String paramString3, Map<String, String> paramMap, Map<String, Object> paramMap1) throws Exception {
/*  428 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  429 */     List list = getTreeBrowserList(paramString1, paramBrowser, paramString2, paramString3, paramMap, paramMap1);
/*  430 */     List<BrowserTreeNode> list1 = getTreeNodeInfo(list, (BaseBrowser)paramBrowser);
/*      */     
/*  432 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  433 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list1);
/*  434 */     hashMap.put(BrowserConstant.BROWSER_RESULT_IS_ALL, Boolean.valueOf(true));
/*  435 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List getTreeBrowserList(String paramString1, Browser paramBrowser, String paramString2, String paramString3, Map<String, String> paramMap, Map<String, Object> paramMap1) throws Exception {
/*  449 */     String str1 = "";
/*  450 */     if (!"".equals(paramString1) && !"root".equals(paramString1)) {
/*  451 */       str1 = paramString1;
/*      */     }
/*  453 */     String str2 = Util.null2String(paramBrowser.getSearchquery()) + " ";
/*  454 */     String str3 = Util.null2String(paramBrowser.getOutPageURL());
/*  455 */     int i = paramBrowser.getDatafrom();
/*  456 */     if (i == 3) {
/*  457 */       str2 = paramBrowser.getSearch();
/*      */     }
/*  459 */     String str4 = Util.null2String(paramBrowser.getFrom());
/*  460 */     paramBrowser.initBaseBrowser("", paramString2, str4);
/*  461 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  462 */     String str5 = paramBrowser.getParentfield();
/*      */     
/*  464 */     if (1 == i && 
/*  465 */       "".equals(str1)) {
/*  466 */       str1 = "0";
/*      */     }
/*      */     
/*  469 */     String str6 = paramBrowser.getDatafrom() + "";
/*  470 */     if (str6.equals("0")) {
/*  471 */       Map<String, Object> map1 = getValuesMap(paramMap1);
/*  472 */       Map map2 = paramBrowser.getParamvalues();
/*  473 */       if (map2 == null || map2.size() == 0) {
/*  474 */         map2 = (Map)paramBrowser.getBaseBrowserInfo().getValuesbak().get("value");
/*      */       }
/*  476 */       Map map3 = initSearchValueMap(map2, map1);
/*  477 */       initSearchValueMap(map2, map1, map2);
/*  478 */       paramBrowser.setParamvalues(map3);
/*      */     } 
/*  480 */     if (str4.equals("2") && i == 2) {
/*  481 */       Map map1 = getValuesMap(paramMap1);
/*  482 */       paramBrowser.setRequestFormInfoForImport(map1);
/*      */     } 
/*      */     
/*  485 */     hashMap.put(str5, str1);
/*  486 */     paramBrowser.setSearchValueMap(hashMap);
/*  487 */     str2 = replaceFieldValue(str2);
/*      */     
/*  489 */     if (paramString3.equals("1"))
/*      */     {
/*  491 */       if (i == 3) {
/*  492 */         str2 = str2.replaceAll("[']+[$]+[_a-zA-Z0-9]+[$]+[']+[\\s]*", "''");
/*  493 */         str2 = str2.replaceAll("[$]+[_a-zA-Z0-9]+[$]+[\\s]*", "0");
/*  494 */         str2 = str2.replaceAll("[']+[\\{]+[?]+[_a-zA-Z0-9]+[\\}]+[']+[\\s]*", "''");
/*  495 */         str2 = str2.replaceAll("[\\{]+[?]+[_a-zA-Z0-9]+[\\}]+[\\s]*", "0");
/*      */       } else {
/*      */         
/*  498 */         str2 = removeParam(str2);
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/*  503 */     Map<String, String> map = dealSearch(str2, "", str3, paramMap, false);
/*  504 */     if (!str3.equals("")) {
/*  505 */       str3 = map.get("outPageURL");
/*      */     }
/*      */     
/*  508 */     str2 = map.get("search");
/*  509 */     if (i == 4) {
/*  510 */       Map map1 = paramBrowser.search("" + this.user.getUID(), paramMap, hashMap, null, null);
/*      */       
/*  512 */       return (List)map1.get("dataAll");
/*      */     } 
/*      */     
/*  515 */     return paramBrowser.search("" + this.user.getUID(), str2, hashMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  528 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  529 */     String str1 = Util.null2String(paramMap.get("type"));
/*  530 */     String str2 = Util.null2String(paramMap.get("currenttime"));
/*  531 */     Map map1 = dealFormParams(paramMap, str2, false);
/*  532 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  533 */     String[] arrayOfString = str1.split("\\|");
/*  534 */     str1 = arrayOfString[0];
/*  535 */     str1 = URLDecoder.decode(str1);
/*  536 */     if (str1.indexOf("browser.") == -1) {
/*  537 */       str1 = "browser." + str1;
/*      */     }
/*  539 */     Browser browser = (Browser)StaticObj.getServiceByFullname(str1, Browser.class);
/*      */     
/*  541 */     String str3 = Util.null2String(browser.getFrom());
/*  542 */     if ("1".equals(str3)) {
/*  543 */       this.formmodeBrowserService.setUser(this.user);
/*  544 */       return this.formmodeBrowserService.getBrowserConditionInfo(paramMap);
/*      */     } 
/*      */     
/*  547 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  548 */     if ("2".equals(str3)) {
/*  549 */       browser.initBaseBrowser("", str1, str3);
/*      */     }
/*  551 */     Map map2 = browser.getSearchfieldMap();
/*  552 */     Map map3 = browser.getSearchfieldShowMap();
/*  553 */     Map map4 = browser.getWokflowfieldnameMap();
/*  554 */     if ("2".equals(str3)) {
/*  555 */       Set set = map2.keySet();
/*  556 */       for (String str5 : set) {
/*      */ 
/*      */         
/*  559 */         String str6 = Util.null2String(map3.get(str5));
/*  560 */         String str7 = Util.null2String(map2.get(str5));
/*  561 */         String str8 = "";
/*  562 */         if ("".equals(str7))
/*  563 */           continue;  String str9 = Util.null2String(map4.get(str5));
/*  564 */         if (!"".equals(str9)) {
/*  565 */           str8 = (String)map1.get(str9.toLowerCase());
/*      */         }
/*  567 */         arrayList.add(madeSearchConditionItem(ConditionType.INPUT, str7, str5, str8, str6));
/*      */       } 
/*      */     } else {
/*  570 */       arrayList.add(madeSearchConditionItem(ConditionType.INPUT, browser.getNameHeader(), "names", ""));
/*      */     } 
/*      */ 
/*      */     
/*  574 */     String str4 = ((BaseBrowser)browser).getTitleField();
/*  575 */     SearchConditionItem searchConditionItem = null;
/*  576 */     byte b = -1;
/*  577 */     if (!"".equals(str4)) {
/*  578 */       for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/*  579 */         SearchConditionItem searchConditionItem1 = arrayList.get(b1);
/*  580 */         String str = searchConditionItem1.getDomkey()[0];
/*  581 */         if (str4.equals(str)) {
/*  582 */           b = b1;
/*  583 */           searchConditionItem = searchConditionItem1;
/*  584 */           searchConditionItem.setIsQuickSearch(true);
/*      */           break;
/*      */         } 
/*      */       } 
/*  588 */       if (searchConditionItem != null) {
/*  589 */         arrayList.remove(b);
/*  590 */         arrayList.add(0, searchConditionItem);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  595 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  596 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private SearchConditionItem madeSearchConditionItem(ConditionType paramConditionType, String paramString1, String paramString2, String paramString3) {
/*  609 */     SearchConditionItem searchConditionItem = new SearchConditionItem();
/*  610 */     searchConditionItem.setConditionType(paramConditionType);
/*  611 */     searchConditionItem.setLabel(paramString1);
/*  612 */     searchConditionItem.setDomkey(new String[] { paramString2 });
/*  613 */     if (!"".equals(paramString3))
/*  614 */       searchConditionItem.setValue(paramString3); 
/*  615 */     return searchConditionItem;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private SearchConditionItem madeSearchConditionItem(ConditionType paramConditionType, String paramString1, String paramString2, String paramString3, String paramString4) {
/*  630 */     SearchConditionItem searchConditionItem = new SearchConditionItem();
/*  631 */     searchConditionItem.setConditionType(paramConditionType);
/*  632 */     searchConditionItem.setLabel(paramString1);
/*  633 */     if (paramString4.equals("1")) {
/*  634 */       searchConditionItem.setDefaultDisplayInBar(true);
/*      */     }
/*  636 */     searchConditionItem.setDomkey(new String[] { paramString2 });
/*  637 */     if (!"".equals(paramString3))
/*  638 */       searchConditionItem.setValue(paramString3); 
/*  639 */     return searchConditionItem;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<ListHeadBean> buildHeadBeans(String paramString1, Browser paramBrowser, List<Map> paramList, String paramString2) {
/*  654 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  655 */     if (!paramString1.equals("2")) {
/*  656 */       arrayList.add((new ListHeadBean("ids", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  657 */       ListHeadBean listHeadBean1 = (new ListHeadBean("names", paramBrowser.getNameHeader(), 1)).setIsInputCol(BoolAttr.TRUE);
/*  658 */       listHeadBean1.setOldWidth("50%");
/*  659 */       listHeadBean1.setDisplay(BoolAttr.TRUE);
/*  660 */       arrayList.add(listHeadBean1);
/*  661 */       ListHeadBean listHeadBean2 = new ListHeadBean("descs", paramBrowser.getDescriptionHeader(), 0);
/*  662 */       listHeadBean2.setOldWidth("50%");
/*  663 */       listHeadBean2.setDisplay(BoolAttr.TRUE);
/*  664 */       arrayList.add(listHeadBean2);
/*      */     } else {
/*  666 */       arrayList.add((new ListHeadBean("ids", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  667 */       for (byte b = 0; b < paramList.size(); b++) {
/*  668 */         Map map = paramList.get(b);
/*      */         
/*  670 */         String str1 = Util.null2String(map.get("searchname"));
/*  671 */         String str2 = Util.null2String(map.get("fieldname"));
/*  672 */         String str3 = Util.null2String(map.get("width"));
/*  673 */         String str4 = str1 + "s";
/*  674 */         boolean bool = str4.equals(paramString2);
/*  675 */         ListHeadBean listHeadBean = new ListHeadBean(str4, str2, bool ? 1 : 0, bool ? BoolAttr.TRUE : BoolAttr.FALSE);
/*  676 */         if (bool) {
/*  677 */           listHeadBean.setIsInputCol(BoolAttr.TRUE);
/*      */         }
/*  679 */         listHeadBean.setOldWidth(str3);
/*  680 */         listHeadBean.setDisplay(BoolAttr.TRUE);
/*  681 */         arrayList.add(listHeadBean);
/*      */       } 
/*      */     } 
/*      */     
/*  685 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<ListHeadBean> buildHeadBeans(String paramString1, Browser paramBrowser, Map paramMap, String paramString2) {
/*  698 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  699 */     if (!paramString1.equals("2")) {
/*  700 */       arrayList.add((new ListHeadBean("ids", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  701 */       ListHeadBean listHeadBean1 = (new ListHeadBean("names", paramBrowser.getNameHeader(), 1)).setIsInputCol(BoolAttr.TRUE);
/*  702 */       listHeadBean1.setOldWidth("50%");
/*  703 */       listHeadBean1.setDisplay(BoolAttr.TRUE);
/*  704 */       arrayList.add(listHeadBean1);
/*  705 */       ListHeadBean listHeadBean2 = new ListHeadBean("descs", paramBrowser.getDescriptionHeader(), 0);
/*  706 */       listHeadBean2.setOldWidth("50%");
/*  707 */       listHeadBean2.setDisplay(BoolAttr.TRUE);
/*  708 */       arrayList.add(listHeadBean2);
/*      */     } else {
/*  710 */       Set set = paramMap.keySet();
/*  711 */       arrayList.add((new ListHeadBean("ids", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  712 */       String str = (100 / set.size()) + "%";
/*  713 */       for (Iterator iterator = set.iterator(); iterator.hasNext(); ) {
/*      */         
/*  715 */         String str1 = Util.null2String(iterator.next());
/*  716 */         String str2 = Util.null2String((String)paramMap.get(str1));
/*  717 */         String str3 = str1 + "s";
/*  718 */         boolean bool = str3.equals(paramString2);
/*  719 */         ListHeadBean listHeadBean = new ListHeadBean(str3, str2, bool ? 1 : 0, bool ? BoolAttr.TRUE : BoolAttr.FALSE);
/*  720 */         if (bool) {
/*  721 */           listHeadBean.setIsInputCol(BoolAttr.TRUE);
/*      */         }
/*  723 */         listHeadBean.setOldWidth(str);
/*  724 */         listHeadBean.setDisplay(BoolAttr.TRUE);
/*  725 */         arrayList.add(listHeadBean);
/*      */       } 
/*      */     } 
/*  728 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> setDataMap(List paramList1, int paramInt1, int paramInt2, Map paramMap, String paramString1, String paramString2, List paramList2) {
/*  742 */     ArrayList arrayList = new ArrayList();
/*  743 */     int i = 0;
/*  744 */     ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*  745 */     if (paramList2 != null && paramList2.size() > 0) {
/*  746 */       dealTempList(paramList1, paramString1, arrayList1, paramMap, false, paramList2);
/*  747 */       ArrayList<Map<String, String>> arrayList2 = new ArrayList();
/*      */       
/*  749 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  750 */       if (arrayList1.size() > 1) {
/*  751 */         for (Map<String, String> map : arrayList1) {
/*  752 */           hashMap1.put(map.get("ids"), map);
/*      */         }
/*  754 */         for (byte b = 0; b < paramList2.size(); b++) {
/*      */           
/*  756 */           if (hashMap1.get(paramList2.get(b)) != null)
/*      */           {
/*      */             
/*  759 */             arrayList2.add(hashMap1.get(paramList2.get(b))); } 
/*      */         } 
/*  761 */         arrayList1 = arrayList2;
/*  762 */         i = arrayList1.size();
/*      */       } 
/*      */     } else {
/*  765 */       if (paramList1 != null && paramList1.size() > 0) {
/*  766 */         i = paramList1.size();
/*  767 */         pageDataList(arrayList, paramList1, paramInt1, paramInt2, paramString2);
/*      */       } 
/*  769 */       dealTempList(arrayList, paramString1, arrayList1, paramMap, true, paramList2);
/*      */     } 
/*  771 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  772 */     hashMap.put("dataAll", arrayList1);
/*  773 */     hashMap.put("recordCount", "" + i);
/*  774 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> setDataMap(Browser paramBrowser, List paramList1, int paramInt1, int paramInt2, Map paramMap, String paramString1, String paramString2, List paramList2) {
/*  790 */     ArrayList arrayList = new ArrayList();
/*  791 */     int i = 0;
/*  792 */     ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*  793 */     if (paramList2 != null && paramList2.size() > 0) {
/*  794 */       dealTempList(paramList1, paramString1, arrayList1, paramMap, false, paramList2);
/*  795 */       ArrayList<Map<String, String>> arrayList2 = new ArrayList();
/*      */       
/*  797 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  798 */       if (arrayList1.size() > 1) {
/*  799 */         for (Map<String, String> map : arrayList1) {
/*  800 */           hashMap1.put(map.get("ids"), map);
/*      */         }
/*  802 */         for (byte b = 0; b < paramList2.size(); b++) {
/*      */           
/*  804 */           if (hashMap1.get(paramList2.get(b)) != null)
/*      */           {
/*      */             
/*  807 */             arrayList2.add(hashMap1.get(paramList2.get(b))); } 
/*      */         } 
/*  809 */         arrayList1 = arrayList2;
/*  810 */         i = arrayList1.size();
/*      */       } 
/*      */     } else {
/*  813 */       if (paramList1 != null && paramList1.size() > 0) {
/*  814 */         i = paramList1.size();
/*  815 */         pageDataList(arrayList, paramList1, paramInt1, paramInt2, paramString2);
/*      */       } 
/*  817 */       dealTempList(paramBrowser, arrayList, paramString1, arrayList1, paramMap, true, paramList2);
/*      */     } 
/*  819 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  820 */     hashMap.put("dataAll", arrayList1);
/*  821 */     hashMap.put("recordCount", "" + i);
/*  822 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> setDataMapWithPage(Browser paramBrowser, List paramList1, int paramInt1, int paramInt2, Map paramMap, String paramString1, String paramString2, List paramList2) {
/*  840 */     ArrayList arrayList = new ArrayList();
/*  841 */     int i = 0;
/*  842 */     ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*  843 */     if (paramList2 != null && paramList2.size() > 0) {
/*  844 */       dealTempListWithPage(paramList1, paramString1, arrayList1, paramMap, false, paramList2);
/*  845 */       ArrayList<Map<String, String>> arrayList2 = new ArrayList();
/*      */       
/*  847 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  848 */       if (arrayList1.size() > 1) {
/*  849 */         for (Map<String, String> map : arrayList1) {
/*  850 */           hashMap1.put(map.get("ids"), map);
/*      */         }
/*  852 */         for (byte b = 0; b < paramList2.size(); b++) {
/*  853 */           arrayList2.add(hashMap1.get(paramList2.get(b)));
/*      */         }
/*  855 */         arrayList1 = arrayList2;
/*  856 */         i = arrayList1.size();
/*      */       } 
/*      */     } else {
/*  859 */       if (paramList1 != null && paramList1.size() > 0) {
/*  860 */         pageDataListWithPage(arrayList, paramList1, paramInt1, paramInt2, paramString2);
/*      */       }
/*  862 */       dealTempList(arrayList, paramString1, arrayList1, paramMap, true, paramList2);
/*      */     } 
/*  864 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  865 */     hashMap.put("dataAll", arrayList1);
/*  866 */     hashMap.put("recordCount", "" + ((BaseBrowser)paramBrowser).getTotal());
/*  867 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void dealTempList(List paramList1, String paramString, List<Map<String, String>> paramList, Map paramMap, boolean paramBoolean, List paramList2) {
/*  881 */     if (null != paramList1 && paramList1.size() > 0) {
/*  882 */       if (paramList2 != null && paramList2.size() > 0) {
/*  883 */         this.newlog.info("  非物理分页  查询数据总数:" + paramList1.size() + " | 已选数据总数 :" + paramList2.size());
/*      */       }
/*  885 */       Iterator<BrowserBean> iterator = paramList1.iterator();
/*  886 */       while (iterator.hasNext()) {
/*  887 */         BrowserBean browserBean = iterator.next();
/*  888 */         String str = browserBean.getId();
/*      */         
/*  890 */         if (paramList2 != null && paramList2.size() > 0) {
/*  891 */           if (paramBoolean && 
/*  892 */             paramList2.contains(str)) {
/*      */             continue;
/*      */           }
/*      */           
/*  896 */           if (!paramBoolean && 
/*  897 */             !paramList2.contains(str)) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */         
/*  902 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  903 */         hashMap.put("ids", str);
/*  904 */         hashMap.put("randomFieldId", str);
/*  905 */         if (!"2".equals(paramString)) {
/*  906 */           String str1 = Util.null2String(browserBean.getName());
/*  907 */           String str2 = Util.null2String(browserBean.getDescription());
/*  908 */           hashMap.put("names", str1);
/*  909 */           hashMap.put("namesspan", "<span title='" + str1 + "'>" + str1 + "</span>");
/*  910 */           hashMap.put("descs", str2);
/*      */           
/*  912 */           hashMap.put("descsspan", "<span title='" + str2 + "'>" + str2 + "</span>");
/*      */         } else {
/*      */           
/*  915 */           Map map = browserBean.getValueMap();
/*  916 */           Set set = paramMap.keySet();
/*      */ 
/*      */           
/*  919 */           for (String str1 : set) {
/*      */             
/*  921 */             String str2 = Util.null2String((String)map.get(str1));
/*  922 */             hashMap.put(str1 + "s", str2);
/*  923 */             hashMap.put(str1 + "sspan", "<span title='" + str2 + "'>" + str2 + "</span>");
/*      */           } 
/*      */         } 
/*      */         
/*  927 */         if (hashMap.get("idsspan") != null) {
/*  928 */           this.newlog.info("  构造接口数据 出现idsspan，进行修复 ，  from：" + paramString + " | showfieldMap ：" + paramMap);
/*  929 */           hashMap.remove("idsspan");
/*      */         } 
/*  931 */         paramList.add(hashMap);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void dealTempList(Browser paramBrowser, List paramList1, String paramString, List<Map<String, String>> paramList, Map paramMap, boolean paramBoolean, List paramList2) {
/*  951 */     BrowserCacheAction browserCacheAction = new BrowserCacheAction();
/*  952 */     boolean bool = (paramBrowser.getBaseBrowserInfo()).isCache;
/*  953 */     String str1 = paramBrowser.getBaseBrowserInfo().getCachetablename();
/*  954 */     String str2 = paramBrowser.getBaseBrowserInfo().getNamefield();
/*  955 */     String str3 = paramBrowser.getBaseBrowserInfo().getShowname();
/*  956 */     if (null != paramList1 && paramList1.size() > 0) {
/*  957 */       if (paramList2 != null && paramList2.size() > 0) {
/*  958 */         this.newlog.info("  非物理分页  查询数据总数:" + paramList1.size() + " | 已选数据总数 :" + paramList2.size());
/*      */       }
/*  960 */       Iterator<BrowserBean> iterator = paramList1.iterator();
/*  961 */       while (iterator.hasNext()) {
/*  962 */         BrowserBean browserBean = iterator.next();
/*  963 */         String str4 = browserBean.getId();
/*      */         
/*  965 */         if (paramList2 != null && paramList2.size() > 0) {
/*  966 */           if (paramBoolean && 
/*  967 */             paramList2.contains(str4)) {
/*      */             continue;
/*      */           }
/*      */           
/*  971 */           if (!paramBoolean && 
/*  972 */             !paramList2.contains(str4)) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */         
/*  977 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  978 */         hashMap.put("ids", str4);
/*  979 */         hashMap.put("randomFieldId", str4);
/*      */         
/*  981 */         String str5 = "";
/*      */ 
/*      */         
/*  984 */         if (!"2".equals(paramString)) {
/*  985 */           String str6 = Util.null2String(browserBean.getName());
/*  986 */           String str7 = Util.null2String(browserBean.getDescription());
/*  987 */           str5 = str6;
/*  988 */           hashMap.put("names", str6);
/*  989 */           hashMap.put("namesspan", "<span title='" + str6 + "'>" + str6 + "</span>");
/*  990 */           hashMap.put("descs", str7);
/*      */           
/*  992 */           hashMap.put("descsspan", "<span title='" + str7 + "'>" + str7 + "</span>");
/*      */         } else {
/*      */           
/*  995 */           Map map = browserBean.getValueMap();
/*  996 */           Set set = paramMap.keySet();
/*  997 */           str5 = Util.null2String(map.get(str2));
/*  998 */           for (String str6 : set) {
/*      */             
/* 1000 */             String str7 = Util.null2String((String)map.get(str6));
/* 1001 */             hashMap.put(str6 + "s", str7);
/* 1002 */             hashMap.put(str6 + "sspan", "<span title='" + str7 + "'>" + str7 + "</span>");
/*      */           } 
/*      */         } 
/*      */         
/*      */         try {
/* 1007 */           if (bool && "2".equals(paramString)) {
/* 1008 */             browserCacheAction.AddCacheData(str3, str5, str1, browserBean.getId());
/*      */           }
/* 1010 */         } catch (Exception exception) {
/* 1011 */           exception.printStackTrace();
/*      */         } 
/*      */ 
/*      */         
/* 1015 */         if (hashMap.get("idsspan") != null) {
/* 1016 */           this.newlog.info("  构造接口数据 出现idsspan，进行修复 ，  from：" + paramString + " | showfieldMap ：" + paramMap);
/* 1017 */           hashMap.remove("idsspan");
/*      */         } 
/* 1019 */         paramList.add(hashMap);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void dealTempListWithPage(List paramList1, String paramString, List<Map<String, String>> paramList, Map paramMap, boolean paramBoolean, List paramList2) {
/* 1037 */     if (null != paramList1 && paramList1.size() > 0) {
/* 1038 */       Iterator<BrowserBean> iterator = paramList1.iterator();
/* 1039 */       while (iterator.hasNext()) {
/* 1040 */         BrowserBean browserBean = iterator.next();
/* 1041 */         String str = browserBean.getId();
/*      */         
/* 1043 */         if (paramList2 != null && paramList2.size() > 0) {
/* 1044 */           if (paramBoolean && 
/* 1045 */             paramList2.indexOf(str) > -1) {
/*      */             continue;
/*      */           }
/*      */           
/* 1049 */           if (!paramBoolean && 
/* 1050 */             paramList2.indexOf(str) < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */         
/* 1055 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 1056 */         hashMap.put("ids", str);
/* 1057 */         hashMap.put("randomFieldId", str);
/* 1058 */         if (!"2".equals(paramString)) {
/* 1059 */           String str1 = Util.null2String(browserBean.getName());
/* 1060 */           String str2 = Util.null2String(browserBean.getDescription());
/* 1061 */           hashMap.put("names", str1);
/* 1062 */           hashMap.put("namesspan", "<span title='" + str1 + "'>" + str1 + "</span>");
/* 1063 */           hashMap.put("descs", str2);
/*      */           
/* 1065 */           hashMap.put("descsspan", "<span title='" + str2 + "'>" + str2 + "</span>");
/* 1066 */           paramList.add(hashMap);
/*      */           
/*      */           continue;
/*      */         } 
/* 1070 */         Map map = browserBean.getValueMap();
/* 1071 */         Set set = paramMap.keySet();
/* 1072 */         for (String str1 : set) {
/*      */           
/* 1074 */           String str2 = Util.null2String((String)map.get(str1));
/* 1075 */           hashMap.put(str1 + "s", str2);
/* 1076 */           hashMap.put(str1 + "sspan", "<span title='" + str2 + "'>" + str2 + "</span>");
/*      */         } 
/* 1078 */         paramList.add(hashMap);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void pageDataList(List paramList1, List paramList2, int paramInt1, int paramInt2, String paramString) {
/* 1095 */     if ("all".equals(paramString)) {
/* 1096 */       paramList1.addAll(paramList2);
/*      */     }
/* 1098 */     else if (paramList2.size() >= paramInt2) {
/* 1099 */       for (int i = paramInt1 - 1; i < paramInt2; i++) {
/* 1100 */         paramList1.add(paramList2.get(i));
/*      */       }
/*      */     } else {
/* 1103 */       for (int i = paramInt1 - 1; i < paramList2.size(); i++) {
/* 1104 */         paramList1.add(paramList2.get(i));
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void pageDataListWithPage(List paramList1, List paramList2, int paramInt1, int paramInt2, String paramString) {
/* 1120 */     paramList1.addAll(paramList2);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List searchForList(Browser paramBrowser, String paramString1, int paramInt, String paramString2, String paramString3, String paramString4, String paramString5, Map paramMap) {
/* 1135 */     List list = null;
/* 1136 */     if (!"2".equals(paramString1)) {
/* 1137 */       if ("".equals(paramString2)) {
/* 1138 */         list = paramBrowser.search(paramString3, paramString4);
/*      */       } else {
/* 1140 */         list = paramBrowser.searchByName(paramString3, paramString2, paramString5);
/*      */       }
/*      */     
/* 1143 */     } else if (paramMap.size() > 0) {
/* 1144 */       if (paramInt == 3) {
/* 1145 */         list = paramBrowser.search(paramString3, paramString5, paramMap);
/*      */       } else {
/* 1147 */         list = paramBrowser.search(paramString3, paramString4, paramMap);
/*      */       } 
/*      */     } else {
/*      */       
/* 1151 */       list = paramBrowser.search(paramString3, paramString4);
/*      */     } 
/*      */     
/* 1154 */     return list;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List searchForListWithPage(Browser paramBrowser, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, Map paramMap, List paramList) {
/* 1172 */     List list = null;
/* 1173 */     BaseBrowser baseBrowser = (BaseBrowser)paramBrowser;
/* 1174 */     baseBrowser.setSelectedIds(paramList);
/* 1175 */     if (!"2".equals(paramString1)) {
/* 1176 */       if ("".equals(paramString2)) {
/* 1177 */         list = baseBrowser.search(paramString3, paramString4);
/*      */       } else {
/* 1179 */         list = baseBrowser.searchByName(paramString3, paramString2, paramString5);
/*      */       }
/*      */     
/* 1182 */     } else if (paramMap.size() > 0) {
/* 1183 */       list = baseBrowser.search(paramString3, paramString4, paramMap);
/*      */     } else {
/* 1185 */       list = baseBrowser.search(paramString3, paramString4);
/*      */     } 
/*      */     
/* 1188 */     return list;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map PhysicsSearchForListWithPage(Browser paramBrowser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, Map paramMap, List paramList) {
/* 1204 */     BaseBrowser baseBrowser = (BaseBrowser)paramBrowser;
/* 1205 */     return baseBrowser.search(paramString1, paramString2, paramInt1, paramInt2, paramString3, paramList, paramMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map dealFormParams2(Map<String, Object> paramMap, String paramString, boolean paramBoolean) {
/* 1217 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1218 */     if (null != paramMap && paramMap.size() > 0) {
/* 1219 */       StringBuffer stringBuffer = new StringBuffer();
/* 1220 */       Set<String> set = paramMap.keySet();
/* 1221 */       for (String str : set) {
/*      */         
/* 1223 */         String[] arrayOfString = str.split("_");
/*      */         
/* 1225 */         if (arrayOfString.length > 1 && str.startsWith("con_")) {
/* 1226 */           int i = str.lastIndexOf("_");
/* 1227 */           String str1 = arrayOfString[arrayOfString.length - 1];
/* 1228 */           stringBuffer.append(str1);
/* 1229 */           stringBuffer.append(",");
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1234 */       if (stringBuffer.length() > 0) {
/* 1235 */         String str = "select t.id,t.fieldname,t.detailtable from workflow_billfield t where t.id in(" + stringBuffer.deleteCharAt(stringBuffer.length() - 1) + ")";
/* 1236 */         RecordSet recordSet = new RecordSet();
/* 1237 */         recordSet.executeQuery(str, new Object[0]);
/* 1238 */         while (recordSet.next()) {
/* 1239 */           String str1 = recordSet.getString("fieldname");
/* 1240 */           String str2 = recordSet.getString("detailtable");
/* 1241 */           String str3 = Util.null2String(paramMap.get("con_" + recordSet.getString("id")));
/* 1242 */           str3 = returnSpecialChar(str3);
/* 1243 */           if (null == str2 || str2.equals("")) {
/* 1244 */             hashMap.put("$" + str1 + "$", str3); continue;
/*      */           } 
/* 1246 */           hashMap.put("$" + str2 + "_" + str1 + "$", str3);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1252 */     return hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map dealFormParams(Map<String, Object> paramMap, String paramString, boolean paramBoolean) {
/* 1263 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */ 
/*      */     
/* 1266 */     if (null != paramMap && paramMap.size() > 0) {
/* 1267 */       Set<String> set = paramMap.keySet();
/* 1268 */       for (String str : set) {
/*      */         
/* 1270 */         String[] arrayOfString = str.split("_");
/*      */         
/* 1272 */         if (arrayOfString.length > 1) {
/* 1273 */           int i = str.lastIndexOf("_");
/* 1274 */           String str1 = str.substring(0, i);
/* 1275 */           String str2 = arrayOfString[arrayOfString.length - 1];
/* 1276 */           if (paramString.equals(str2)) {
/* 1277 */             String str3 = Util.null2String(paramMap.get(str));
/* 1278 */             str3 = returnSpecialChar(str3);
/* 1279 */             if ("".equals(str3))
/* 1280 */               if (paramBoolean) { str3 = "''"; }
/* 1281 */               else { str3 = ""; }
/*      */                
/* 1283 */             hashMap.put("$" + str1 + "$", str3);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/* 1288 */     return hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void initSearchValueMap(Map paramMap1, Map<String, Object> paramMap, Map<String, String> paramMap2) {
/* 1299 */     if (null != paramMap1) {
/* 1300 */       Set set = paramMap1.keySet();
/* 1301 */       for (String str1 : set) {
/*      */         
/* 1303 */         String str2 = Util.null2String(paramMap.get(str1));
/* 1304 */         str2 = returnSpecialChar(str2);
/* 1305 */         if (!"".equals(str2)) {
/* 1306 */           paramMap2.put(str1, str2);
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map initSearchValueMap(Map paramMap, Map<String, Object> paramMap1) {
/* 1318 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/* 1320 */     this.newlog.info("tc-------searchfieldMap:" + paramMap);
/* 1321 */     this.newlog.info("tc-------params:" + paramMap1);
/* 1322 */     if (null != paramMap) {
/* 1323 */       Set set = paramMap.keySet();
/* 1324 */       for (String str1 : set) {
/*      */         
/* 1326 */         String str2 = (String)paramMap.get(str1);
/*      */         
/* 1328 */         Set<String> set1 = paramMap1.keySet();
/* 1329 */         for (String str3 : set1) {
/*      */           
/* 1331 */           String str4 = (String)paramMap1.get(str3);
/* 1332 */           if (str3.toLowerCase().equals(str2.toLowerCase()))
/*      */           {
/*      */             
/* 1335 */             if (!"".equals(str4)) {
/* 1336 */               hashMap.put(str1, str4);
/*      */             }
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/* 1342 */     return hashMap;
/*      */   }
/*      */   public Map getValuesMap(Map<String, Object> paramMap) {
/* 1345 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1346 */     String str = (String)paramMap.get("currenttime");
/* 1347 */     if (paramMap != null) {
/* 1348 */       Set<String> set = paramMap.keySet();
/* 1349 */       for (String str1 : set) {
/*      */         
/* 1351 */         String str2 = Util.null2String(paramMap.get(str1));
/* 1352 */         if (str != null && str1.contains(str)) {
/* 1353 */           str1 = str1.replace("_" + str, "");
/*      */ 
/*      */ 
/*      */           
/* 1357 */           hashMap.put("$" + str1 + "$", str2);
/*      */         } 
/*      */       } 
/*      */     } 
/* 1361 */     return hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void initSpecialSearchValueMap(Map paramMap1, Map<String, String> paramMap2, Map<String, String> paramMap3, Map<String, String> paramMap4) {
/* 1374 */     if (null != paramMap1) {
/* 1375 */       Set set = paramMap1.keySet();
/* 1376 */       for (String str1 : set) {
/*      */         
/* 1378 */         String str2 = Util.null2String(paramMap4.get(str1));
/* 1379 */         String str3 = "";
/* 1380 */         if (!"".equals(str2)) {
/* 1381 */           str3 = Util.null2String(paramMap2.get(str2));
/*      */         }
/* 1383 */         str3 = returnSpecialChar(str3);
/* 1384 */         if (!"".equals(str3)) {
/* 1385 */           paramMap3.put(str1, str3);
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String removeParam(String paramString) {
/* 1398 */     return PublicSqlDealUtil.removeSqlVariable(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static String returnSpecialChar(String paramString) {
/* 1408 */     paramString = paramString.replaceAll("@#add#@", "+");
/* 1409 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceFieldValue(String paramString) {
/* 1420 */     Pattern pattern = Pattern.compile("(\\$[a-zA-Z][a-zA-Z0-9_]*\\$)");
/* 1421 */     Matcher matcher = pattern.matcher(paramString);
/* 1422 */     while (matcher.find()) {
/* 1423 */       String str = matcher.group();
/* 1424 */       paramString = paramString.replace(str, str.toLowerCase());
/*      */     } 
/* 1426 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String rebuildMultiFieldValue(String paramString) {
/* 1436 */     String str = "";
/* 1437 */     if (paramString.indexOf(",") > -1) {
/* 1438 */       String[] arrayOfString = paramString.split(",");
/* 1439 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 1440 */         if (!arrayOfString[b].equals("")) {
/* 1441 */           if (str.equals("")) {
/* 1442 */             str = str + "'";
/*      */           } else {
/* 1444 */             str = str + ",'";
/*      */           } 
/*      */           
/* 1447 */           str = str + arrayOfString[b] + "'";
/*      */         } 
/*      */       } 
/*      */     } else {
/* 1451 */       str = paramString;
/*      */     } 
/*      */     
/* 1454 */     return str;
/*      */   }
/*      */   
/*      */   private List<BrowserTreeNode> getTreeNodeInfo(List paramList) throws Exception {
/* 1458 */     return getTreeNodeInfo(paramList, null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getTreeNodeInfo(List paramList, BaseBrowser paramBaseBrowser) throws Exception {
/* 1469 */     List<BrowserTreeNode> list = dealDataListToNode(paramList, paramBaseBrowser);
/*      */     
/* 1471 */     Map<String, List> map = dealDataListToMap(list);
/*      */     
/* 1473 */     final List sortList = map.get("sortList");
/*      */     
/* 1475 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1476 */     for (BrowserTreeNode browserTreeNode : list)
/*      */     {
/*      */       
/* 1479 */       hashMap1.put(browserTreeNode.getId(), browserTreeNode);
/*      */     }
/*      */     
/* 1482 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1483 */     ArrayList<Map.Entry<String, BrowserTreeNode>> arrayList = new ArrayList(hashMap1.entrySet());
/* 1484 */     Comparator<Map.Entry<String, BrowserTreeNode>> comparator = new Comparator<Map.Entry<String, BrowserTreeNode>>() {
/*      */         public int compare(Map.Entry<String, BrowserTreeNode> param1Entry1, Map.Entry<String, BrowserTreeNode> param1Entry2) {
/* 1486 */           String str1 = param1Entry1.getKey();
/* 1487 */           String str2 = param1Entry2.getKey();
/* 1488 */           int i = sortList.indexOf(str1);
/* 1489 */           int j = sortList.indexOf(str2);
/* 1490 */           return i - j;
/*      */         }
/*      */       };
/* 1493 */     Collections.sort(arrayList, comparator);
/*      */     
/* 1495 */     ArrayList<BrowserTreeNode> arrayList1 = new ArrayList();
/*      */     
/* 1497 */     for (Map.Entry<String, BrowserTreeNode> entry : arrayList) {
/* 1498 */       BrowserTreeNode browserTreeNode = (BrowserTreeNode)entry.getValue();
/* 1499 */       if ("0".equals(browserTreeNode.getPid()) || "".equals(browserTreeNode.getPid()) || browserTreeNode.getId().equals(browserTreeNode.getPid()) || null == browserTreeNode.getPid()) {
/*      */         
/* 1501 */         arrayList1.add(browserTreeNode);
/* 1502 */         hashMap2.put(browserTreeNode.getId(), browserTreeNode);
/*      */         continue;
/*      */       } 
/* 1505 */       if (hashMap1.get(browserTreeNode.getPid()) != null) {
/* 1506 */         if (((BrowserTreeNode)hashMap1.get(browserTreeNode.getPid())).getSubs() != null) {
/* 1507 */           BrowserTreeNode browserTreeNode2 = (BrowserTreeNode)hashMap1.get(browserTreeNode.getPid());
/* 1508 */           browserTreeNode2.setIsParent(true);
/* 1509 */           browserTreeNode2.getSubs().add(browserTreeNode); continue;
/*      */         } 
/* 1511 */         ArrayList<BrowserTreeNode> arrayList2 = new ArrayList();
/* 1512 */         arrayList2.add(browserTreeNode);
/* 1513 */         BrowserTreeNode browserTreeNode1 = (BrowserTreeNode)hashMap1.get(browserTreeNode.getPid());
/* 1514 */         browserTreeNode1.setIsParent(true);
/* 1515 */         browserTreeNode1.setSubs(arrayList2);
/*      */         
/*      */         continue;
/*      */       } 
/*      */       
/* 1520 */       arrayList1.add(browserTreeNode);
/* 1521 */       hashMap2.put(browserTreeNode.getId(), browserTreeNode);
/*      */     } 
/*      */ 
/*      */     
/* 1525 */     return arrayList1;
/*      */   }
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> dealDataListToNode(List paramList) {
/* 1530 */     return dealDataListToNode(paramList, null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> dealDataListToNode(List paramList, BaseBrowser paramBaseBrowser) {
/* 1539 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 1540 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1541 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1542 */     hashMap2.put("open", Boolean.valueOf(false));
/* 1543 */     Iterator<BrowserBean> iterator = paramList.iterator();
/* 1544 */     while (iterator.hasNext()) {
/* 1545 */       BrowserBean browserBean = iterator.next();
/* 1546 */       String str1 = Util.null2String(browserBean.getId());
/* 1547 */       String str2 = Util.null2String(browserBean.getName());
/* 1548 */       String str3 = Util.null2String(browserBean.getParentId());
/* 1549 */       hashMap1.put(str3, str3);
/* 1550 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode(str1, str2, str3, false);
/* 1551 */       browserTreeNode.setCanClick(true);
/* 1552 */       browserTreeNode.setProp(hashMap2);
/* 1553 */       arrayList.add(browserTreeNode);
/*      */     } 
/*      */ 
/*      */     
/* 1557 */     boolean bool = false;
/* 1558 */     if (paramBaseBrowser != null) {
/* 1559 */       bool = "1".equals(paramBaseBrowser.getOnlylowestnode());
/*      */     }
/* 1561 */     if (bool && arrayList.size() > 0) {
/* 1562 */       for (BrowserTreeNode browserTreeNode : arrayList) {
/* 1563 */         String str = browserTreeNode.getId();
/* 1564 */         if (hashMap1.containsKey(str)) {
/* 1565 */           browserTreeNode.setCanClick(false);
/*      */         }
/*      */       } 
/*      */     }
/* 1569 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, List> dealDataListToMap(List<BrowserTreeNode> paramList) {
/* 1579 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1580 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 1581 */     ArrayList<String> arrayList1 = new ArrayList();
/* 1582 */     for (BrowserTreeNode browserTreeNode : paramList) {
/* 1583 */       if ("0".equals(browserTreeNode.getPid()) || "".equals(browserTreeNode.getPid()) || browserTreeNode.getId().equals(browserTreeNode.getPid()) || null == browserTreeNode.getPid())
/*      */       {
/* 1585 */         arrayList.add(browserTreeNode);
/*      */       }
/*      */ 
/*      */ 
/*      */       
/* 1590 */       arrayList1.add(browserTreeNode.getId());
/*      */     } 
/* 1592 */     hashMap.put("lastList", arrayList);
/* 1593 */     hashMap.put("sortList", arrayList1);
/*      */     
/* 1595 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, String> dealSearch(String paramString1, String paramString2, String paramString3, Map<String, String> paramMap, boolean paramBoolean) {
/* 1609 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1610 */     if (paramMap != null && paramMap.size() > 0) {
/* 1611 */       Set<String> set = paramMap.keySet();
/* 1612 */       for (String str1 : set) {
/*      */         
/* 1614 */         String str2 = Util.null2String(paramMap.get(str1));
/* 1615 */         if (paramBoolean) {
/* 1616 */           str2 = rebuildMultiFieldValue(str2);
/*      */         }
/* 1618 */         paramString1 = paramString1.replace(str1.toLowerCase(), str2);
/* 1619 */         paramString2 = paramString2.replace(str1.toLowerCase(), str2);
/* 1620 */         if (!paramString3.equals("")) {
/* 1621 */           paramString3 = paramString3.replace(str1.toLowerCase(), str2);
/*      */         }
/*      */       } 
/*      */     } 
/* 1625 */     hashMap.put("search", paramString1);
/* 1626 */     hashMap.put("searchByName", paramString2);
/* 1627 */     hashMap.put("outPageURL", paramString3);
/* 1628 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<Map<String, String>> DealMultiTree(List paramList1, List paramList2) {
/* 1640 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1641 */     if (paramList2 == null || paramList2.size() == 0) {
/* 1642 */       return new ArrayList<>();
/*      */     }
/* 1644 */     Iterator<BrowserBean> iterator = paramList1.iterator();
/* 1645 */     while (iterator.hasNext()) {
/* 1646 */       BrowserBean browserBean = iterator.next();
/* 1647 */       String str1 = Util.null2String(browserBean.getId());
/* 1648 */       String str2 = Util.null2String(browserBean.getName());
/* 1649 */       String str3 = Util.null2String(browserBean.getParentId());
/* 1650 */       if (paramList2 != null && paramList2.size() > 0 && 
/* 1651 */         paramList2.indexOf(str1) < 0) {
/*      */         continue;
/*      */       }
/*      */       
/* 1655 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 1656 */       hashMap.put("id", str1);
/* 1657 */       hashMap.put("name", str2);
/* 1658 */       hashMap.put("pid", str3);
/* 1659 */       arrayList.add(hashMap);
/*      */     } 
/* 1661 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private JSONObject buildJsonTemplate(String paramString1, List<Map> paramList, String paramString2) {
/* 1672 */     JSONObject jSONObject1 = new JSONObject();
/* 1673 */     JSONObject jSONObject2 = new JSONObject();
/* 1674 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1675 */     jSONObject2.put("key", "col1");
/* 1676 */     if (paramString1.equals("2")) {
/* 1677 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 1678 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1679 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1680 */       hashMap1.put("key", "col1_row1");
/* 1681 */       hashMap2.put("key", paramString2);
/* 1682 */       arrayList2.add(hashMap2);
/* 1683 */       hashMap1.put("configs", arrayList2);
/* 1684 */       arrayList.add(hashMap1);
/* 1685 */       byte b1 = 2;
/* 1686 */       for (byte b2 = 0; b2 < paramList.size(); b2++) {
/* 1687 */         Map map = paramList.get(b2);
/* 1688 */         String str1 = Util.null2String(map.get("searchname"));
/* 1689 */         String str2 = str1 + "s";
/* 1690 */         if (!str2.toLowerCase().equalsIgnoreCase(paramString2)) {
/*      */ 
/*      */           
/* 1693 */           ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/* 1694 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 1695 */           HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 1696 */           hashMap3.put("key", "col1_row" + b1);
/* 1697 */           hashMap4.put("key", str2);
/* 1698 */           arrayList3.add(hashMap4);
/* 1699 */           hashMap3.put("configs", arrayList3);
/* 1700 */           arrayList.add(hashMap3);
/* 1701 */           b1++;
/*      */         } 
/*      */       } 
/*      */     } else {
/* 1705 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 1706 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1707 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1708 */       hashMap1.put("key", "col1_row1");
/* 1709 */       hashMap2.put("key", "names");
/* 1710 */       arrayList2.add(hashMap2);
/* 1711 */       hashMap1.put("configs", arrayList2);
/* 1712 */       arrayList.add(hashMap1);
/* 1713 */       ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/* 1714 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 1715 */       HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 1716 */       hashMap3.put("key", "col1_row2");
/* 1717 */       hashMap4.put("key", "descs");
/* 1718 */       arrayList3.add(hashMap4);
/* 1719 */       hashMap3.put("configs", arrayList3);
/* 1720 */       arrayList.add(hashMap3);
/*      */     } 
/* 1722 */     jSONObject2.put("configs", arrayList);
/* 1723 */     ArrayList<JSONObject> arrayList1 = new ArrayList();
/* 1724 */     arrayList1.add(jSONObject2);
/* 1725 */     jSONObject1.put("default", arrayList1);
/* 1726 */     return jSONObject1;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CommonBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */