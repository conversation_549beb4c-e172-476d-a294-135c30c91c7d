/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.systeminfo.language.LanguageComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LanguageBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*    */ 
/*    */     
/* 29 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 30 */     LanguageComInfo languageComInfo = new LanguageComInfo();
/* 31 */     HashMap<Object, Object> hashMap2 = null;
/* 32 */     while (languageComInfo.next()) {
/* 33 */       hashMap2 = new HashMap<>();
/* 34 */       hashMap2.put("languageid", languageComInfo.getLanguageid());
/* 35 */       hashMap2.put("languagename", languageComInfo.getLanguagename());
/* 36 */       arrayList.add(hashMap2);
/*    */     } 
/* 38 */     hashMap1.put("datas", arrayList);
/*    */ 
/*    */     
/* 41 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 42 */     arrayList1.add((new ListHeadBean("languageid", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()))).setIsPrimarykey(BoolAttr.TRUE));
/* 43 */     arrayList1.add((new ListHeadBean("languagename", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 45 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 46 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 47 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*    */     
/* 49 */     return (Map)hashMap1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/LanguageBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */