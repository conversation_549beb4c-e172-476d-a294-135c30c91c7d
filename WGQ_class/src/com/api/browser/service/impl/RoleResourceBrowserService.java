/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.StringTokenizer;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RoleResourceBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  54 */     RecordSet recordSet = new RecordSet();
/*  55 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  56 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  57 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  58 */     String str1 = Util.null2String(paramMap.get("src"));
/*  59 */     String str2 = Util.null2String(paramMap.get("isNoAccount"));
/*  60 */     String str3 = Util.null2String(paramMap.get("selectids"));
/*  61 */     if (str3.trim().startsWith(",")) {
/*  62 */       str3 = str3.substring(1);
/*     */     }
/*  64 */     String str4 = Tools.getURLDecode(Util.null2String(paramMap.get("roleid")));
/*  65 */     String[] arrayOfString = str4.split("_");
/*  66 */     str3 = "";
/*  67 */     String str5 = "0";
/*  68 */     if (arrayOfString.length > 0 && !"".equals(Util.null2String(arrayOfString[0]))) {
/*  69 */       str5 = "" + arrayOfString[0];
/*     */     }
/*  71 */     if (arrayOfString.length == 2) {
/*  72 */       str3 = Util.null2String("" + arrayOfString[1]);
/*     */     }
/*  74 */     StringTokenizer stringTokenizer = new StringTokenizer(str3, ",");
/*  75 */     int i = Util.getIntValue(Util.null2String(paramMap.get("pageSize")), 10);
/*  76 */     int j = Util.getIntValue(Util.null2String(paramMap.get("currentPage")), 1);
/*     */     
/*  78 */     ArrayList arrayList = new ArrayList();
/*  79 */     Object object = null;
/*     */     
/*  81 */     String str6 = Tools.getURLDecode(Util.null2String(paramMap.get("lastname")));
/*  82 */     String str7 = Tools.getURLDecode(Util.null2String(paramMap.get("q")));
/*  83 */     if ("".equals(str6) && !"".equals(str7)) {
/*  84 */       str6 = str7;
/*     */     }
/*     */ 
/*     */     
/*  88 */     int k = this.user.getUID();
/*  89 */     int m = str5.indexOf("a");
/*  90 */     int n = 0;
/*  91 */     if (m > -1) {
/*  92 */       int i1 = Util.getIntValue(str5.substring(0, m), 0);
/*  93 */       String str = str5.substring(m + 1);
/*     */       
/*  95 */       str5 = "" + i1;
/*  96 */       m = str.indexOf("b");
/*  97 */       if (m > -1) {
/*  98 */         n = Util.getIntValue(str.substring(0, m), 0);
/*  99 */         k = Util.getIntValue(str.substring(m + 1), 0);
/* 100 */         if (k <= 0) {
/* 101 */           k = this.user.getUID();
/*     */         }
/*     */       } else {
/* 104 */         n = Util.getIntValue(str);
/*     */       } 
/*     */     } 
/* 107 */     String str8 = " where 1 = 1 ";
/* 108 */     if (!str5.equals("")) {
/* 109 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 110 */       String str = hrmCommonServiceImpl.getRoleMemberIds(Util.getIntValue(str5), "0");
/* 111 */       if (!"".equals(str)) {
/* 112 */         str8 = str8 + " and (" + Util.getSubINClause(str, "a.id", "in") + ") ";
/*     */       }
/*     */     } 
/*     */     
/* 116 */     if (n != 0) {
/* 117 */       if (n == 1) {
/* 118 */         int i1 = Util.getIntValue(resourceComInfo.getSubCompanyID("" + k), 0);
/* 119 */         str8 = str8 + " and a.subcompanyid1=" + i1 + " ";
/* 120 */       } else if (n == 2) {
/* 121 */         int i1 = Util.getIntValue(resourceComInfo.getSubCompanyID("" + k), 0);
/* 122 */         int i2 = Util.getIntValue(subCompanyComInfo.getSupsubcomid("" + i1), 0);
/* 123 */         str8 = str8 + " and a.subcompanyid1=" + i2 + " ";
/* 124 */       } else if (n == 3) {
/* 125 */         int i1 = Util.getIntValue(resourceComInfo.getDepartmentID("" + k), 0);
/* 126 */         str8 = str8 + " and a.departmentid=" + i1 + " ";
/*     */       } 
/*     */     }
/*     */     
/* 130 */     String str9 = Util.null2String(paramMap.get("excludeId"));
/* 131 */     if (str9.length() == 0)
/* 132 */       str9 = str3; 
/* 133 */     if (str9.length() > 0) {
/* 134 */       str8 = str8 + " and a.id not in (" + str9 + ")";
/*     */     }
/*     */     
/* 137 */     if (str6.length() > 0) {
/* 138 */       str8 = str8 + " and ( a.pinyinlastname like '%" + Util.fromScreen2(str6, this.user.getLanguage()) + "%' or a.lastname like '%" + Util.fromScreen2(str6, this.user.getLanguage()) + "%') ";
/*     */     }
/* 140 */     str8 = str8 + " and status in (0,1,2,3)";
/* 141 */     String str10 = "a.id,a.lastname,a.departmentid,a.subcompanyid1,a.jobtitle,a.dsporder";
/* 142 */     String str11 = "from HrmResource a";
/* 143 */     String str12 = "a.dsporder,a.lastname";
/* 144 */     String str13 = "a.id";
/*     */     
/* 146 */     ArrayList<SplitTableColBean> arrayList1 = new ArrayList();
/* 147 */     arrayList1.add(new SplitTableColBean("true", "id"));
/* 148 */     arrayList1.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(413, this.user.getLanguage()), "lastname", "lastname", 1)).setIsInputCol(BoolAttr.TRUE));
/* 149 */     arrayList1.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(124, this.user.getLanguage()), "departmentid", "departmentid", "weaver.hrm.company.DepartmentComInfo.getDepartmentName", 0));
/* 150 */     arrayList1.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(141, this.user.getLanguage()), "subcompanyid1", "subcompanyid1", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname", 0));
/*     */     
/* 152 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, str8, str12, str13, arrayList1);
/* 153 */     splitTableBean.setSqlsortway("ASC");
/* 154 */     splitTableBean.setSqlisdistinct("true");
/* 155 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 156 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 161 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 162 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 163 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 164 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 165 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 413, "lastname", true));
/* 166 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 171 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 172 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 173 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 174 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 175 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */     
/* 177 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 178 */     if (this.user == null || "".equals(str)) return (Map)hashMap; 
/* 179 */     StringTokenizer stringTokenizer = new StringTokenizer(str, ",");
/* 180 */     if (stringTokenizer != null) {
/* 181 */       while (stringTokenizer.hasMoreTokens()) {
/* 182 */         String str1 = stringTokenizer.nextToken();
/* 183 */         String str2 = resourceComInfo.getLastname(str1);
/* 184 */         String str3 = departmentComInfo.getDepartmentmark(resourceComInfo.getDepartmentID(str1));
/* 185 */         String str4 = subCompanyComInfo.getSubCompanyname(resourceComInfo.getSubCompanyID(str1));
/* 186 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 187 */         hashMap1.put("id", str1);
/* 188 */         hashMap1.put("lastname", str2);
/* 189 */         hashMap1.put("departmentid", str3);
/* 190 */         hashMap1.put("subcompanyid1", str4);
/* 191 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     }
/* 194 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 195 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 196 */     arrayList1.add(new ListHeadBean("lastname", "", 1, BoolAttr.TRUE));
/* 197 */     arrayList1.add(new ListHeadBean("departmentid", "", 0, BoolAttr.FALSE));
/* 198 */     arrayList1.add(new ListHeadBean("subcompanyid1", "", 0, BoolAttr.FALSE));
/*     */     
/* 200 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 201 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 202 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 203 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 208 */     Map map = ParamUtil.request2Map(paramHttpServletRequest);
/*     */     
/* 210 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 211 */     RecordSet recordSet1 = new RecordSet();
/* 212 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 213 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 214 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 215 */     String str1 = Util.null2String(map.get("src"));
/* 216 */     String str2 = Util.null2String(map.get("isNoAccount"));
/* 217 */     String str3 = Util.null2String(map.get("selectids"));
/* 218 */     if (str3.trim().startsWith(",")) {
/* 219 */       str3 = str3.substring(1);
/*     */     }
/* 221 */     String str4 = Tools.getURLDecode(Util.null2String(map.get("roleid")));
/* 222 */     String[] arrayOfString = str4.split("_");
/* 223 */     str3 = "";
/* 224 */     String str5 = "0";
/* 225 */     if (arrayOfString.length > 0 && !"".equals(Util.null2String(arrayOfString[0]))) {
/* 226 */       str5 = "" + arrayOfString[0];
/*     */     }
/* 228 */     if (arrayOfString.length == 2) {
/* 229 */       str3 = Util.null2String("" + arrayOfString[1]);
/*     */     }
/* 231 */     StringTokenizer stringTokenizer = new StringTokenizer(str3, ",");
/* 232 */     int i = Util.getIntValue(Util.null2String(map.get("pageSize")), 10);
/* 233 */     int j = Util.getIntValue(Util.null2String(map.get("currentPage")), 1);
/*     */     
/* 235 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 236 */     Object object = null;
/*     */     
/* 238 */     String str6 = Tools.getURLDecode(Util.null2String(map.get("lastname")));
/* 239 */     String str7 = Tools.getURLDecode(Util.null2String(map.get("q")));
/* 240 */     if ("".equals(str6) && !"".equals(str7)) {
/* 241 */       str6 = str7;
/*     */     }
/* 243 */     int k = this.user.getUID();
/* 244 */     int m = str5.indexOf("a");
/* 245 */     int n = 0;
/* 246 */     if (m > -1) {
/* 247 */       int i1 = Util.getIntValue(str5.substring(0, m), 0);
/* 248 */       String str = str5.substring(m + 1);
/*     */       
/* 250 */       str5 = "" + i1;
/* 251 */       m = str.indexOf("b");
/* 252 */       if (m > -1) {
/* 253 */         n = Util.getIntValue(str.substring(0, m), 0);
/* 254 */         k = Util.getIntValue(str.substring(m + 1), 0);
/* 255 */         if (k <= 0) {
/* 256 */           k = this.user.getUID();
/*     */         }
/*     */       } else {
/* 259 */         n = Util.getIntValue(str);
/*     */       } 
/*     */     } 
/* 262 */     String str8 = " where 1 = 1 ";
/* 263 */     if (!str5.equals("")) {
/* 264 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 265 */       String str = hrmCommonServiceImpl.getRoleMemberIds(Util.getIntValue(str5), "0");
/* 266 */       if (!"".equals(str)) {
/* 267 */         str8 = str8 + " and (" + Util.getSubINClause(str, "a.id", "in") + ") ";
/*     */       }
/*     */     } 
/*     */     
/* 271 */     if (n != 0) {
/* 272 */       if (n == 1) {
/* 273 */         int i1 = Util.getIntValue(resourceComInfo.getSubCompanyID("" + k), 0);
/* 274 */         str8 = str8 + " and a.subcompanyid1=" + i1 + " ";
/* 275 */       } else if (n == 2) {
/* 276 */         int i1 = Util.getIntValue(resourceComInfo.getSubCompanyID("" + k), 0);
/* 277 */         int i2 = Util.getIntValue(subCompanyComInfo.getSupsubcomid("" + i1), 0);
/* 278 */         str8 = str8 + " and a.subcompanyid1=" + i2 + " ";
/* 279 */       } else if (n == 3) {
/* 280 */         int i1 = Util.getIntValue(resourceComInfo.getDepartmentID("" + k), 0);
/* 281 */         str8 = str8 + " and a.departmentid=" + i1 + " ";
/*     */       } 
/*     */     }
/*     */     
/* 285 */     String str9 = Util.null2String(map.get("excludeId"));
/* 286 */     if (str9.length() == 0)
/* 287 */       str9 = str3; 
/* 288 */     if (str9.length() > 0) {
/* 289 */       str8 = str8 + " and a.id not in (" + str9 + ")";
/*     */     }
/*     */     
/* 292 */     if (str6.length() > 0) {
/* 293 */       str8 = str8 + " and ( a.pinyinlastname like '%" + Util.fromScreen2(str6, this.user.getLanguage()) + "%' or a.lastname like '%" + Util.fromScreen2(str6, this.user.getLanguage()) + "%') ";
/*     */     }
/* 295 */     str8 = str8 + " and status in (0,1,2,3)";
/* 296 */     String str10 = "a.id,a.lastname,a.departmentid,a.subcompanyid1,a.jobtitle,a.dsporder";
/* 297 */     String str11 = "from HrmResource a";
/* 298 */     String str12 = "a.dsporder,a.lastname";
/* 299 */     String str13 = "a.id";
/*     */     
/* 301 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 303 */     recordSet2.executeQuery("select " + str10 + " " + str11 + " " + str8 + " order by " + str12, new Object[0]);
/*     */     
/* 305 */     while (recordSet2.next()) {
/* 306 */       String str14 = Util.null2String(recordSet2.getString("id"));
/* 307 */       String str15 = Util.null2String(recordSet2.getString("lastname"));
/* 308 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 309 */       hashMap1.put("id", str14);
/* 310 */       hashMap1.put("name", str15);
/* 311 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 314 */     hashMap.put("datas", arrayList);
/* 315 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/RoleResourceBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */