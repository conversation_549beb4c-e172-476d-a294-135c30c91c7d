/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JobGroupsBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = Util.null2String(paramMap.get("jobgroupremark"));
/* 33 */     String str2 = Util.null2String(paramMap.get("jobgroupname"));
/* 34 */     String str3 = "where 1 = 1 ";
/* 35 */     String str4 = "";
/* 36 */     String str5 = "";
/* 37 */     String str6 = "";
/* 38 */     if (!str1.equals("")) {
/* 39 */       str3 = str3 + " and jobgroupremark like '%";
/* 40 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 41 */       str3 = str3 + "%'";
/*    */     } 
/* 43 */     if (!str2.equals("")) {
/* 44 */       str3 = str3 + " and jobgroupname like '%";
/* 45 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 46 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 49 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 50 */     str4 = " id,jobgroupname, jobgroupremark ";
/* 51 */     str5 = " HrmJobGroups ";
/* 52 */     str6 = " id ";
/*    */     
/* 54 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 55 */     SplitTableColBean splitTableColBean = new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "jobgroupremark", "jobgroupremark");
/* 56 */     splitTableColBean.setOrderkey("");
/* 57 */     splitTableColBean.setIsInputCol(BoolAttr.TRUE);
/* 58 */     arrayList.add(splitTableColBean.setIsInputCol(BoolAttr.TRUE));
/* 59 */     arrayList.add(new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(15767, this.user.getLanguage()), "jobgroupname", "jobgroupname"));
/*    */     
/* 61 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/*    */     
/* 63 */     splitTableBean.setSqlsortway("ASC");
/*    */     
/* 65 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 66 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 72 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 73 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 74 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 75 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "jobgroupremark", true));
/* 76 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15767, "jobgroupname"));
/* 77 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/JobGroupsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */