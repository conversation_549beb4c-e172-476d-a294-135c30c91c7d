/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.apache.commons.collections.map.HashedMap;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.interfaces.workflow.browser.Browser;
/*     */ import weaver.interfaces.workflow.browser.BrowserBean;
/*     */ 
/*     */ public class CommonTreeCenterBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  25 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  26 */     if (this.user == null) {
/*  27 */       hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  28 */       return (Map)hashMap1;
/*     */     } 
/*  30 */     String str1 = Util.null2String(paramMap.get("node"));
/*  31 */     String str2 = Util.null2String(paramMap.get("type"));
/*  32 */     String str3 = Util.null2String(paramMap.get("currenttime"));
/*     */ 
/*     */ 
/*     */     
/*  36 */     String str4 = Util.null2String(Util.null2String(paramMap.get("isreport")), "0");
/*  37 */     Map map = dealFormParams(paramMap, str3);
/*  38 */     String str5 = "";
/*  39 */     if (!"".equals(str1) && !"root".equals(str1)) {
/*  40 */       str5 = str1;
/*     */     }
/*  42 */     Browser browser = (Browser)StaticObj.getServiceByFullname(str2, Browser.class);
/*  43 */     String str6 = Util.null2String(browser.getSearch()) + " ";
/*  44 */     String str7 = Util.null2String(browser.getFrom());
/*  45 */     browser.initBaseBrowser("", str2, str7);
/*  46 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  47 */     String str8 = browser.getParentfield();
/*  48 */     int i = browser.getDatafrom();
/*  49 */     if (1 == i && 
/*  50 */       "".equals(str5)) {
/*  51 */       str5 = "0";
/*     */     }
/*     */     
/*  54 */     hashMap2.put(str8, str5);
/*  55 */     browser.setSearchValueMap(hashMap2);
/*  56 */     str6 = replaceFieldValue(str6);
/*     */     
/*  58 */     if (str4.equals("1")) {
/*  59 */       str6 = removeParam(str6);
/*     */     }
/*     */     
/*  62 */     if (map != null && map.size() > 0) {
/*  63 */       Set set = map.keySet();
/*  64 */       for (String str9 : set) {
/*     */         
/*  66 */         String str10 = Util.null2String((String)map.get(str9));
/*  67 */         str6 = str6.replace(str9, str10);
/*     */       } 
/*     */     } 
/*  70 */     List list = browser.search("" + this.user.getUID(), str6, hashMap2);
/*  71 */     List<BrowserTreeNode> list1 = getTreeNodeInfo(list);
/*  72 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  73 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, list1);
/*  74 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(List paramList) throws Exception {
/*  84 */     List<BrowserTreeNode> list = dealDataListToNode(paramList);
/*  85 */     Map<String, List<BrowserTreeNode>> map = dealDataListToMap(list);
/*  86 */     Iterator<BrowserTreeNode> iterator = list.iterator();
/*  87 */     while (iterator.hasNext()) {
/*  88 */       BrowserTreeNode browserTreeNode = iterator.next();
/*  89 */       String str = browserTreeNode.getId();
/*  90 */       List list1 = map.get(str);
/*  91 */       if (list1 != null) {
/*  92 */         browserTreeNode.setSubs(list1);
/*  93 */         browserTreeNode.setIsParent(true);
/*     */       } 
/*     */     } 
/*  96 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> dealDataListToNode(List paramList) {
/* 105 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 106 */     HashedMap<String, Boolean> hashedMap = new HashedMap();
/* 107 */     hashedMap.put("open", Boolean.valueOf(false));
/* 108 */     Iterator<BrowserBean> iterator = paramList.iterator();
/* 109 */     while (iterator.hasNext()) {
/* 110 */       BrowserBean browserBean = iterator.next();
/* 111 */       String str1 = Util.null2String(browserBean.getId());
/* 112 */       String str2 = Util.null2String(browserBean.getName());
/* 113 */       String str3 = Util.null2String(browserBean.getParentId());
/* 114 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode(str1, str2, str3, false);
/* 115 */       browserTreeNode.setCanClick(true);
/* 116 */       browserTreeNode.setProp((Map)hashedMap);
/* 117 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 119 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, List<BrowserTreeNode>> dealDataListToMap(List<BrowserTreeNode> paramList) {
/* 128 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 129 */     HashedMap<String, Boolean> hashedMap = new HashedMap();
/* 130 */     hashedMap.put("open", Boolean.valueOf(false));
/* 131 */     Iterator<BrowserTreeNode> iterator = paramList.iterator();
/* 132 */     while (iterator.hasNext()) {
/* 133 */       BrowserTreeNode browserTreeNode = iterator.next();
/* 134 */       String str = browserTreeNode.getPid();
/* 135 */       if (hashMap.get(str) == null) {
/* 136 */         ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 137 */         arrayList.add(browserTreeNode);
/* 138 */         hashMap.put(str, arrayList); continue;
/*     */       } 
/* 140 */       List<BrowserTreeNode> list = (List)hashMap.get(str);
/* 141 */       list.add(browserTreeNode);
/* 142 */       hashMap.put(str, list);
/*     */     } 
/*     */     
/* 145 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map dealFormParams(Map<String, Object> paramMap, String paramString) {
/* 154 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 155 */     if (null != paramMap && paramMap.size() > 0) {
/* 156 */       Set<String> set = paramMap.keySet();
/* 157 */       for (String str : set) {
/*     */         
/* 159 */         String[] arrayOfString = str.split("_");
/* 160 */         if (arrayOfString.length == 2) {
/* 161 */           String str1 = arrayOfString[0];
/* 162 */           String str2 = arrayOfString[1];
/* 163 */           if (paramString.equals(str2)) {
/* 164 */             String str3 = Util.null2String(paramMap.get(str));
/* 165 */             str3 = returnSpecialChar(str3);
/* 166 */             if ("".equals(str3)) {
/* 167 */               str3 = "''";
/*     */             }
/* 169 */             hashMap.put("$" + str1 + "$", str3);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 174 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String removeParam(String paramString) {
/* 184 */     paramString = paramString.replaceAll("\\([ ]+\\{\\?", "({?");
/*     */ 
/*     */     
/* 187 */     paramString = paramString.replaceAll("\\(", " ( ");
/* 188 */     paramString = paramString.replaceAll("\\)", " ) ");
/*     */ 
/*     */     
/* 191 */     paramString = paramString.replaceAll("[\\s]+[_a-zA-Z0-9.]+[\\s]+((not|NOT)[\\s]+)?(in|IN)[\\s]+[\\(][\\s]*''''[\\s]*[\\)][\\s]+", " 1=1 ");
/* 192 */     paramString = paramString.replaceAll("[\\s]+[_a-zA-Z0-9.]+[\\s]+((not|NOT)[\\s]+)?(in|IN)[\\s]+[\\(][\\s]*[']?(\\$|\\{\\?)[_a-zA-Z0-9]+(\\$|\\})[']?[\\s]*[\\)][\\s]+", " 1=1 ");
/* 193 */     paramString = paramString.replaceAll("[\\s]+[_a-zA-Z0-9.]+[\\s]+((not|NOT)[\\s]+)?(in|IN)[\\s]+[\\(][\\s]*'*[\\s]*'*[\\s]*[\\)][\\s]+", " 1=1 ");
/*     */     
/* 195 */     paramString = paramString.replaceAll("[\\s]+[_a-zA-Z0-9.]+[\\s]+((not|NOT)[\\s]+)?(like|LIKE)[\\s]+[\\(]?[\\s]*[']?%?[,|.|/|\\|，|。|;|；]?(\\$|\\{\\?)[_a-zA-Z0-9]+(\\$|\\})[,|.|/|\\|，|。|;|；]?%?[']?[\\s]*[\\)]?[\\s]+", " 1=1 ");
/* 196 */     paramString = paramString.replaceAll("'%''%'", "'%%'");
/*     */     
/* 198 */     paramString = paramString.replaceAll("[\\s]+[_a-zA-Z0-9.]+[\\s]+((not|NOT)[\\s]+)?(between|BETWEEN)[\\s]+[']?(\\$|\\{\\?)[_a-zA-Z0-9]+(\\$|\\})[']?[\\s]+(and|AND)[\\s]+[']?(\\$|\\{\\?)[_a-zA-Z0-9]+(\\$|\\})[']?[\\s]+", " 1=1 ");
/*     */ 
/*     */     
/* 201 */     paramString = paramString.replaceAll("[\\s]+[_a-zA-Z0-9.]+[\\s]*[=<>!]{1,2}[\\s]*[']?(\\$|\\{\\?)[_a-zA-Z0-9]+(\\$|\\})[']?[\\s]+", " 1=1 ");
/*     */     
/* 203 */     paramString = paramString.replaceAll("[\\s]+[']?(\\$|\\{\\?)[_a-zA-Z0-9]+(\\$|\\})[']?[\\s]*[=<>!]{1,2}[\\s]*[_a-zA-Z0-9.]+[\\s]+", " 1=1 ");
/*     */     
/* 205 */     paramString = paramString.replaceAll("([ \\s\\(]+)[_a-zA-Z0-9.]*[\\(]?[_a-zA-Z0-9,.]*[']?[%]?\\$[_a-zA-Z0-9]+\\$[%]?[']?[_a-zA-Z0-9,.]*[\\)]?[ \\s]*([=<>!]{1,2}|not[\\s]+like|like|not[\\s]+in|in|NOT[\\s]+LIKE|LIKE|NOT[\\s]+IN|IN)[\\s]+[_a-zA-Z0-9.,\\(\\)]+([\\) \\s]+)", " $1 1=1 $3 ");
/* 206 */     paramString = paramString.replaceAll("([ \\s\\(]+)[_a-zA-Z0-9.,\\(\\)]+[ \\s]*([=<>!]{1,2}|not[\\s]+like|like|not[\\s]+in|in|NOT[\\s]+LIKE|LIKE|NOT[\\s]+IN|IN)[ \\s]*[_a-zA-Z0-9.]*[\\(]?[_a-zA-Z0-9,.]*[']?[%]?\\$[_a-zA-Z0-9]+\\$[%]?[']?[_a-zA-Z0-9,.]*[\\s]+[\\)]?([\\) \\s]+)", " $1 1=1 $3 ");
/* 207 */     paramString = paramString.replaceAll("([ \\s\\(]+)[_a-zA-Z0-9.]*[\\(]?[_a-zA-Z0-9,.]*[']?[%]?\\{\\?[a-z]+\\}[%]?[']?[_a-zA-Z0-9,.]*[\\)]?[ \\s]*([=<>!]{1,2}|not[\\s]+like|like|not[\\s]+in|in|NOT[\\s]+LIKE|LIKE|NOT[\\s]+IN|IN)[ \\s]*[_a-zA-Z0-9.,\\(\\)]+([\\) \\s]+)", " $1 1=1 $3 ");
/* 208 */     paramString = paramString.replaceAll("([ \\s\\(]+)[_a-zA-Z0-9.,\\(\\)]+[ \\s]*([=<>!]{1,2}|not[\\s]+like|like|not[\\s]+in|in|NOT[\\s]+LIKE|LIKE|NOT[\\s]+IN|IN)[ \\s]*[_a-zA-Z0-9.]*[\\(]?[_a-zA-Z0-9,.]*[']?[%]?\\{\\?[a-z]+\\}[%]?[']?[_a-zA-Z0-9,.]*[\\)]?([\\) \\s]+)", " $1 1=1 $3 ");
/*     */     
/* 210 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String returnSpecialChar(String paramString) {
/* 220 */     paramString = paramString.replaceAll("@#add#@", "+");
/* 221 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String replaceFieldValue(String paramString) {
/* 232 */     Pattern pattern = Pattern.compile("(\\$[a-zA-Z][a-zA-Z0-9_]*\\$)");
/* 233 */     Matcher matcher = pattern.matcher(paramString);
/* 234 */     while (matcher.find()) {
/* 235 */       String str = matcher.group();
/* 236 */       paramString = paramString.replace(str, str.toLowerCase());
/*     */     } 
/* 238 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CommonTreeCenterBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */