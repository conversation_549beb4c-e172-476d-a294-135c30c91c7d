/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.portal.biz.synergy.SynergyCommonBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.label.LabelComInfo;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynergyParamsBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  33 */     String str1 = Util.null2String(paramMap.get("ebaseid"));
/*  34 */     String str2 = Util.null2String(paramMap.get("wfid"));
/*  35 */     String str3 = Util.null2String(paramMap.get("hpid"));
/*  36 */     String str4 = Util.null2String(paramMap.get("htmlType"));
/*  37 */     String str5 = Util.null2String(paramMap.get("fieldType"));
/*     */ 
/*     */     
/*  40 */     String str6 = " " + ("sysparam".equals(str1) ? -1 : 0) + " as fromtype,-1 as formid,-1 as isbill,id,paramlabel,paramname,htmltype,fieldtype ";
/*  41 */     String str7 = " synergy_params_new ";
/*  42 */     String str8 = " 1=1 ";
/*  43 */     if (!"".equals(str1)) {
/*  44 */       str8 = str8 + " and ebaseid='" + str1 + "' ";
/*     */     }
/*  46 */     if ("7".equals(str1)) {
/*  47 */       str8 = str8 + " and paramname not in('usermanager','owner','ownersubcompanyid1','ownerdepartmentid','ownermanager','modifydate') ";
/*     */     }
/*  49 */     if ("8".equals(str1)) {
/*  50 */       str8 = str8 + " and paramname not in('usermanager','filingdate','workflowstate') ";
/*     */     }
/*  52 */     String str9 = " ordernum ";
/*     */     
/*  54 */     if (!"".equals(str3)) {
/*  55 */       SynergyCommonBiz synergyCommonBiz = new SynergyCommonBiz();
/*  56 */       str2 = synergyCommonBiz.getWfidByHpid(str3);
/*     */     } 
/*     */     
/*  59 */     if (!"".equals(str2)) {
/*  60 */       RecordSet recordSet = new RecordSet();
/*  61 */       recordSet.executeQuery("select formid,isbill from workflow_base where id=?", new Object[] { str2 });
/*  62 */       if (recordSet.next()) {
/*  63 */         int i = recordSet.getInt("formid");
/*  64 */         int j = recordSet.getInt("isbill");
/*     */         
/*  66 */         str6 = " fromtype,formid,isbill,id,paramlabel,paramname,htmltype,fieldtype ";
/*  67 */         str8 = " 1=1 ";
/*     */         
/*  69 */         if (j == 1) {
/*  70 */           str7 = " (select 0 as fromtype,-1 as formid,-1 as isbill,id,paramlabel,paramname,cast(htmltype as char(1)) as htmltype,fieldtype,ordernum,0 as dsporder from synergy_params_new where ebaseid='8' and paramname not in('usermanager','filingdate','workflowstate') union all select 1 as fromtype," + i + " as formid,1 as isbill,id,fieldlabel as paramlabel,fieldname as paramname,fieldhtmltype as htmltype,type as fieldtype,9999 as ordernum,dsporder from workflow_billfield where billid=" + i + " and (detailtable='' or detailtable is null)) a ";
/*  71 */           if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  72 */             str7 = " (select 0 as fromtype,-1 as formid,-1 as isbill,id,concat(paramlabel, '') as paramlabel,paramname,htmltype,fieldtype,ordernum,0 as dsporder from synergy_params_new where ebaseid='8' and paramname not in('usermanager','filingdate','workflowstate') union all select 1 as fromtype," + i + " as formid,1 as isbill,id,fieldlabel as paramlabel,fieldname as paramname,fieldhtmltype as htmltype,type as fieldtype,9999 as ordernum,dsporder from workflow_billfield where billid=" + i + " and (detailtable='' or detailtable is null)) a ";
/*     */           }
/*  74 */           str9 = " ordernum,dsporder ";
/*     */         } else {
/*  76 */           str7 = " (select 0 as fromtype,-1 as formid,-1 as isbill,id,labelname as paramlabel,paramname,cast(htmltype as char(1)) as htmltype,fieldtype,ordernum,0 as fieldorder from synergy_params_new t1, HtmlLabelInfo t2 where ebaseid='8' and paramname not in('usermanager','filingdate','workflowstate') and t1.paramlabel=t2.indexid and t2.languageid=" + this.user.getLanguage() + " union all select 1 as fromtype," + i + " as formid,0 as isbill,id,fieldlable as paramlabel,fieldname as paramname,fieldhtmltype as htmltype,type as fieldtype,9999 as ordernum,fieldorder from workflow_formfield t1, workflow_fieldlable t2, workflow_formdict t3 where t1.formid=" + i + " and t1.formid=t2.formid and t1.fieldid=t2.fieldid and t1.fieldid=t3.id and (t1.isdetail<>'1' or t1.isdetail is null) and t2.langurageid=" + this.user.getLanguage() + ") a ";
/*  77 */           if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  78 */             str7 = " (select 0 as fromtype,-1 as formid,-1 as isbill,id,labelname as paramlabel,paramname,htmltype,fieldtype,ordernum,0 as fieldorder from synergy_params_new t1, HtmlLabelInfo t2 where ebaseid='8' and paramname not in('usermanager','filingdate','workflowstate') and t1.paramlabel=t2.indexid and t2.languageid=" + this.user.getLanguage() + " union all select 1 as fromtype," + i + " as formid,0 as isbill,id,fieldlable as paramlabel,fieldname as paramname,fieldhtmltype as htmltype,type as fieldtype,9999 as ordernum,fieldorder from workflow_formfield t1, workflow_fieldlable t2, workflow_formdict t3 where t1.formid=" + i + " and t1.formid=t2.formid and t1.fieldid=t2.fieldid and t1.fieldid=t3.id and (t1.isdetail<>'1' or t1.isdetail is null) and t2.langurageid=" + this.user.getLanguage() + ") a ";
/*     */           }
/*  80 */           str9 = " ordernum,fieldorder ";
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  85 */     if (!"".equals(str4)) {
/*  86 */       str8 = str8 + " and htmltype=" + str4 + " ";
/*     */     }
/*  88 */     if (!"".equals(str5)) {
/*  89 */       str8 = str8 + " and fieldtype=" + str5 + " ";
/*     */     }
/*     */     
/*  92 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  93 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  94 */     arrayList.add(new SplitTableColBean("true", "fromtype"));
/*  95 */     arrayList.add(new SplitTableColBean("true", "formid"));
/*  96 */     arrayList.add(new SplitTableColBean("true", "isbill"));
/*     */     
/*  98 */     if ("sysparam".equals(str1)) {
/*  99 */       arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(84276, this.user.getLanguage()), "paramlabel", null, "com.api.browser.service.impl.SynergyParamsBrowserService.getFieldName", "" + this.user.getLanguage(), 1)).setIsInputCol(BoolAttr.TRUE));
/* 100 */       arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "paramname", null, 0));
/*     */     } else {
/* 102 */       arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "paramlabel", null, "com.api.browser.service.impl.SynergyParamsBrowserService.getFieldName", "" + this.user.getLanguage(), 1)).setIsInputCol(BoolAttr.TRUE));
/* 103 */       arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "paramname", null, 0));
/* 104 */       arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(687, this.user.getLanguage()), "htmltype", null, "com.api.browser.service.impl.SynergyParamsBrowserService.getHtmlTypeName", "" + this.user.getLanguage(), 0));
/* 105 */       arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "fieldtype", null, "com.api.browser.service.impl.SynergyParamsBrowserService.getFieldTypeName", "column:htmltype+column:id+" + this.user.getLanguage(), 0));
/*     */     } 
/*     */     
/* 108 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str8, str9, "id", "asc", arrayList);
/* 109 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 110 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getFieldName(String paramString1, String paramString2) {
/* 114 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 115 */     String str = arrayOfString[0];
/*     */     
/* 117 */     if (Util.getIntValue(paramString1) == -1) {
/* 118 */       return paramString1;
/*     */     }
/* 120 */     return SystemEnv.getHtmlLabelName(Util.getIntValue(paramString1), Util.getIntValue(str));
/*     */   }
/*     */ 
/*     */   
/*     */   public String getHtmlTypeName(String paramString1, String paramString2) {
/* 125 */     LabelComInfo labelComInfo = new LabelComInfo();
/*     */     
/* 127 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 128 */     String str1 = arrayOfString[0];
/*     */     
/* 130 */     String str2 = "0";
/*     */     
/* 132 */     switch (Util.getIntValue(paramString1))
/*     */     { case 1:
/* 134 */         str2 = "688";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 153 */         return labelComInfo.getLabelname(str2, str1);case 2: str2 = "689"; return labelComInfo.getLabelname(str2, str1);case 3: str2 = "32306"; return labelComInfo.getLabelname(str2, str1);case 4: str2 = "691"; return labelComInfo.getLabelname(str2, str1);case 5: str2 = "690"; return labelComInfo.getLabelname(str2, str1); }  str2 = "0"; return labelComInfo.getLabelname(str2, str1);
/*     */   }
/*     */   
/*     */   public String getFieldTypeName(String paramString1, String paramString2) {
/* 157 */     LabelComInfo labelComInfo = new LabelComInfo();
/* 158 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/*     */     
/* 160 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 161 */     String str1 = arrayOfString[0];
/* 162 */     String str2 = arrayOfString[1];
/* 163 */     String str3 = arrayOfString[2];
/*     */     
/* 165 */     String str4 = "";
/*     */     
/* 167 */     if ("1".equals(str1)) {
/* 168 */       if ("1".equals(paramString1)) {
/* 169 */         str4 = labelComInfo.getLabelname("608", str3);
/* 170 */       } else if ("2".equals(paramString1)) {
/* 171 */         str4 = labelComInfo.getLabelname("696", str3);
/* 172 */       } else if ("3".equals(paramString1)) {
/* 173 */         str4 = labelComInfo.getLabelname("697", str3);
/* 174 */       } else if ("4".equals(paramString1)) {
/* 175 */         str4 = labelComInfo.getLabelname("18004", str3);
/* 176 */       } else if ("5".equals(paramString1)) {
/* 177 */         str4 = labelComInfo.getLabelname("22395", str3);
/*     */       } 
/* 179 */     } else if ("2".equals(str1)) {
/* 180 */       str4 = labelComInfo.getLabelname("689", str3);
/* 181 */     } else if ("3".equals(str1)) {
/* 182 */       str4 = labelComInfo.getLabelname(browserComInfo.getBrowserlabelid(paramString1), str3);
/* 183 */     } else if ("4".equals(str1)) {
/* 184 */       str4 = labelComInfo.getLabelname("691", str3);
/* 185 */     } else if ("5".equals(str1)) {
/* 186 */       if ("-1".equals(paramString1)) {
/* 187 */         if ("11".equals(str2)) {
/* 188 */           str4 = labelComInfo.getLabelname("225", str3) + "," + labelComInfo.getLabelname("15533", str3) + "," + labelComInfo.getLabelname("2087", str3);
/* 189 */         } else if ("28".equals(str2)) {
/* 190 */           str4 = labelComInfo.getLabelname("2246", str3) + "," + labelComInfo.getLabelname("2245", str3) + "," + labelComInfo.getLabelname("332", str3);
/*     */         } 
/*     */       } else {
/* 193 */         RecordSet recordSet = new RecordSet();
/* 194 */         recordSet.executeQuery("select selectvalue,selectname from workflow_SelectItem where fieldid=? order by listorder,selectvalue", new Object[] { str2 });
/* 195 */         while (recordSet.next()) {
/* 196 */           str4 = str4 + recordSet.getString("selectname") + ",";
/*     */         }
/* 198 */         if (!"".equals(str4)) {
/* 199 */           str4 = str4.substring(0, str4.length() - 1);
/*     */         }
/*     */       } 
/* 202 */     } else if ("6".equals(str1)) {
/* 203 */       str4 = labelComInfo.getLabelname("17616", str3);
/* 204 */     } else if ("7".equals(str1)) {
/* 205 */       if ("1".equals(paramString1)) {
/* 206 */         str4 = labelComInfo.getLabelname("21692", str3);
/* 207 */       } else if ("2".equals(paramString1)) {
/* 208 */         str4 = labelComInfo.getLabelname("21693", str3);
/*     */       } 
/*     */     } 
/*     */     
/* 212 */     return str4;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SynergyParamsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */