/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BudgetBearerBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  37 */     if (this.user == null) {
/*  38 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  39 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  42 */     String str = Util.null2String(paramMap.get("qryType"));
/*     */     
/*  44 */     if ("1".equals(str)) {
/*  45 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  46 */       List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/*  47 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */     } else {
/*  49 */       List<Map<String, String>> list = getTableList(paramMap);
/*  50 */       ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  51 */       arrayList.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  52 */       arrayList.add(new ListHeadBean("budgetBearerName", SystemEnv.getHtmlLabelName(195, this.user.getLanguage())));
/*  53 */       arrayList.add(new ListHeadBean("codeName", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage())));
/*     */       
/*  55 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*  56 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*  57 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/*     */     
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  71 */     if (this.user == null) {
/*  72 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  73 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  76 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  77 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  79 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name"));
/*  80 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "codeName"));
/*     */     
/*  82 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  84 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) throws Exception {
/*  94 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*  95 */     RecordSet recordSet1 = new RecordSet();
/*  96 */     RecordSet recordSet2 = new RecordSet();
/*  97 */     String str1 = Util.null2String(paramMap.get("id"));
/*  98 */     String str2 = Util.null2String(paramMap.get("selfId"));
/*  99 */     String str3 = Util.null2String(paramMap.get("tableName"));
/*     */     
/* 101 */     if ("oracle".equalsIgnoreCase(recordSet1.getDBType()) && 
/* 102 */       "".equals(str1)) {
/* 103 */       str1 = "                                ";
/*     */     }
/*     */     
/* 106 */     String str4 = "select a.id, a.budgetBearerName, a.codeName, a.supId from " + str3 + " a  where a.supId = ? ";
/*     */     
/* 108 */     if (!"".equals(str2)) {
/* 109 */       str4 = str4 + " and a.id != ? ";
/*     */     }
/* 111 */     str4 = str4 + " ORDER BY a.codeName, a.budgetBearerName, a.id ";
/* 112 */     String str5 = "/images/treeimages/home16_wev8.gif";
/* 113 */     if (!"".equals(str2)) {
/* 114 */       recordSet1.executeQuery(str4, new Object[] { str1, str2 });
/*     */     } else {
/* 116 */       recordSet1.executeQuery(str4, new Object[] { str1 });
/*     */     } 
/* 118 */     while (recordSet1.next()) {
/* 119 */       String str6 = recordSet1.getString("id");
/* 120 */       String str7 = recordSet1.getString("budgetBearerName");
/*     */       
/* 122 */       boolean bool = true;
/* 123 */       String str8 = "select count(*) cnt from " + str3 + " a where a.supId = ? and a.id != ?";
/* 124 */       recordSet2.executeQuery(str8, new Object[] { str6, str2 });
/* 125 */       if (recordSet2.next() && recordSet2.getInt("cnt") > 0) {
/* 126 */         bool = true;
/*     */       } else {
/* 128 */         bool = false;
/*     */       } 
/*     */       
/* 131 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 132 */       browserTreeNode.setId(str6);
/* 133 */       browserTreeNode.setName(str7);
/* 134 */       browserTreeNode.setIsParent(bool);
/* 135 */       browserTreeNode.setIcon(str5);
/* 136 */       browserTreeNode.setCanClick(true);
/* 137 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 139 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 151 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 152 */     LinkedList<HashMap<Object, Object>> linkedList = new LinkedList();
/* 153 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 154 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("tableName"));
/* 155 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("selfId"));
/* 156 */     LinkedList<String> linkedList1 = new LinkedList();
/* 157 */     String str4 = "select a.id, a.budgetBearerName, a.codeName, a.supId from " + str2 + " a  where a.budgetBearerName like ? ORDER BY a.codeName, a.budgetBearerName, a.id ";
/*     */     
/* 159 */     RecordSet recordSet = new RecordSet();
/* 160 */     recordSet.executeQuery(str4, new Object[] { "%" + str1 + "%" });
/* 161 */     if (!"".equals(str3)) {
/* 162 */       linkedList1.add(str3);
/* 163 */       getAllChildsSql(linkedList1, str3, str2);
/*     */     } 
/* 165 */     while (recordSet.next()) {
/* 166 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 167 */       String str5 = recordSet.getString("id");
/* 168 */       String str6 = recordSet.getString("budgetBearerName");
/* 169 */       if (linkedList1.size() > 0 && linkedList1.contains(str5)) {
/*     */         continue;
/*     */       }
/* 172 */       hashMap1.put("id", str5);
/* 173 */       hashMap1.put("name", str6);
/* 174 */       linkedList.add(hashMap1);
/*     */     } 
/* 176 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, linkedList);
/*     */     
/* 178 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, String>> getTableList(Map<String, Object> paramMap) throws Exception {
/* 188 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 189 */     String str1 = Util.null2String(paramMap.get("name"));
/* 190 */     String str2 = Util.null2String(paramMap.get("tableName"));
/* 191 */     String str3 = Util.null2String(paramMap.get("codeName"));
/* 192 */     String str4 = Util.null2String(paramMap.get("selfId"));
/* 193 */     LinkedList<String> linkedList = new LinkedList();
/* 194 */     if (!"".equals(str4)) {
/* 195 */       linkedList.add(str4);
/* 196 */       getAllChildsSql(linkedList, str4, str2);
/*     */     } 
/*     */     
/* 199 */     String str5 = " select a.id,a.budgetBearerName,a.codeName from " + str2 + " a where a.budgetBearerName like ? and a.codeName like ? order by a.budgetBearerName,a.codeName";
/*     */     
/* 201 */     RecordSet recordSet = new RecordSet();
/* 202 */     recordSet.executeQuery(str5, new Object[] { "%" + str1 + "%", "%" + str3 + "%" });
/* 203 */     while (recordSet.next()) {
/* 204 */       String str6 = recordSet.getString("id");
/* 205 */       String str7 = recordSet.getString("budgetBearerName");
/* 206 */       String str8 = recordSet.getString("codeName");
/* 207 */       if (linkedList.size() > 0 && linkedList.contains(str6)) {
/*     */         continue;
/*     */       }
/* 210 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 211 */       hashMap.put("id", str6);
/* 212 */       hashMap.put("budgetBearerName", str7);
/* 213 */       hashMap.put("codeName", str8);
/*     */       
/* 215 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 218 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<String> getAllChildsSql(List<String> paramList, String paramString1, String paramString2) throws Exception {
/* 231 */     RecordSet recordSet = new RecordSet();
/* 232 */     String str = "with cte as (select * from " + paramString2 + " where supId = ? union all select  a.* from " + paramString2 + " as a,cte as b where a.supId = b.id )select * from cte ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 240 */     if ("oracle".equals(recordSet.getDBType())) {
/* 241 */       str = "select * from " + paramString2 + " start with supId = ? connect by prior id = supId";
/* 242 */     } else if ("mysql".equals(recordSet.getDBType())) {
/* 243 */       str = " select DISTINCT t.id,t.budgetBearerName,t.supId from ( select @id idlist, @lv:=@lv+1 lv, (select @id:=group_concat(id separator ',') from " + paramString2 + " where find_in_set(supId,@id)) sub  from " + paramString2 + " ,(select @id:= ? ,@lv:=0) vars  ) tl, " + paramString2 + " t  where find_in_set(t.id,tl.idlist)  order by lv asc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 250 */     else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 251 */       str = "with RECURSIVE  cte as (select * from " + paramString2 + " where supId = ? union all select  a.* from " + paramString2 + " as a,cte as b where a.supId = b.id )select * from cte ";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 259 */     recordSet.executeQuery(str, new Object[] { paramString1 });
/*     */     
/* 261 */     while (recordSet.next()) {
/* 262 */       String str1 = recordSet.getString("id");
/* 263 */       paramList.add(str1);
/*     */     } 
/* 265 */     return paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BudgetBearerBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */