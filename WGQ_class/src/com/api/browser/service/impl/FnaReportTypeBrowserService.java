/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FnaReportTypeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 39 */     String str1 = Util.null2String(paramMap.get("typeName"));
/* 40 */     String str2 = Util.null2String(paramMap.get("description"));
/*    */     
/* 42 */     String str3 = "where 1=1 ";
/*    */     
/* 44 */     if (!"".equals(str1)) {
/* 45 */       str3 = str3 + " and typeName like '%";
/* 46 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 47 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 50 */     if (!"".equals(str2)) {
/* 51 */       str3 = str3 + " and description like '%";
/* 52 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 53 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 56 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 57 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 58 */     arrayList.add((new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "typeName", "", 1)).setIsInputCol(BoolAttr.TRUE));
/* 59 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "", 0));
/*    */     
/* 61 */     String str4 = "id,typeName,description";
/* 62 */     String str5 = "FnaReportType";
/* 63 */     String str6 = "displayOrder, typeName";
/* 64 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", "asc", arrayList);
/* 65 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 66 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 72 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*    */     
/* 74 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 75 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "typeName", true));
/* 76 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/*    */     
/* 78 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 79 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaReportTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */