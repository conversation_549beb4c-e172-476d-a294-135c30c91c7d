/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.engine.SAPIntegration.biz.SAPBrowser.SAPIntegrationParamsBiz;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SAPParameterBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 30 */     SAPIntegrationParamsBiz sAPIntegrationParamsBiz = new SAPIntegrationParamsBiz();
/*    */     
/* 32 */     String str1 = Util.getIntValue(Util.null2String(paramMap.get("serviceId")), 0) + "";
/* 33 */     paramMap.put("serviceId", str1);
/*    */     
/* 35 */     String str2 = Util.getIntValue(Util.null2String(paramMap.get("type")), 0) + "";
/* 36 */     String str3 = Util.null2String(Util.null2String(paramMap.get("sapFieldArr")));
/* 37 */     String str4 = "undefined";
/* 38 */     if (paramMap.get("sapParameterArr") != null) {
/* 39 */       str4 = Util.null2String(paramMap.get("sapParameterArr"));
/*    */     }
/* 41 */     paramMap.put("type", str2);
/* 42 */     paramMap.put("sapFieldArr", str3);
/* 43 */     paramMap.put("sapParameterArr", str4);
/*    */     
/* 45 */     String str5 = Util.null2String(paramMap.get("stuOrTableValue"));
/* 46 */     paramMap.put("stuOrTableValue", str5);
/*    */     
/* 48 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isFromSvc")), 0);
/* 49 */     paramMap.put("isFromSvc", Integer.valueOf(i));
/*    */     
/* 51 */     Map<String, Object> map = sAPIntegrationParamsBiz.getRequestList(paramMap, this.user);
/* 52 */     map = (map == null) ? new HashMap<>() : map;
/* 53 */     return map;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 67 */       ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 68 */       ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*    */       
/* 70 */       int i = Util.getIntValue(Util.null2String(paramMap.get("type")), 0);
/* 71 */       if (i == 2 || i == 5 || i == 7 || i == 10) {
/* 72 */         arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30671, "sapFieldName"));
/* 73 */         arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30674, "sapFieldDesc"));
/*    */       } else {
/* 75 */         arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 23481, "sapFieldName", true));
/* 76 */         arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30667, "sapFieldDesc"));
/*    */       } 
/* 78 */       hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 79 */     } catch (Exception exception) {
/* 80 */       exception.printStackTrace();
/*    */     } 
/* 82 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SAPParameterBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */