/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MobilemodeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */     String str = Util.null2String(paramMap.get("flag"));
/* 29 */     if (str.equals("secondAuthPage")) {
/* 30 */       return getSecondAuthLayout(paramMap);
/*    */     }
/* 32 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   private Map<String, Object> getSecondAuthLayout(Map<String, Object> paramMap) {
/* 37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 38 */     String str1 = Util.null2String(paramMap.get("pageName"));
/* 39 */     String str2 = Util.null2String(paramMap.get("appName"));
/* 40 */     String str3 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*    */ 
/*    */     
/* 43 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 44 */     SplitTableColBean splitTableColBean = new SplitTableColBean("0%", "isdefault", "isdefault", "isdefault");
/* 45 */     splitTableColBean.setHide("true");
/* 46 */     arrayList.add(splitTableColBean);
/* 47 */     arrayList.add((new SplitTableColBean("15%", "id", "id", "id")).setIsPrimarykey(BoolAttr.TRUE));
/* 48 */     arrayList.add((new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(528708, this.user.getLanguage()), "pageName", "")).setIsInputCol(BoolAttr.TRUE));
/* 49 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(528709, this.user.getLanguage()), "appName", ""));
/* 50 */     SplitTableBean splitTableBean = new SplitTableBean("*", "tmptable", "", "id", "id", arrayList);
/* 51 */     splitTableBean.setDatasource("com.engine.cube.biz.MobilemodeBrowserDataSource.getSecondAuthPage");
/* 52 */     splitTableBean.setSourceparams("pageName:" + str1 + "+appName:" + str2);
/* 53 */     splitTableBean.setInstanceid("appHomepageTable");
/* 54 */     splitTableBean.setPagesize("10");
/* 55 */     splitTableBean.setTableType("none");
/* 56 */     splitTableBean.setSqlsortway("ASC");
/* 57 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*    */     
/* 59 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 64 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 65 */     String str = Util.null2String(paramMap.get("flag"));
/* 66 */     ArrayList arrayList = new ArrayList();
/* 67 */     if (str.equals("secondAuthPage")) {
/* 68 */       return getSelectPageConditions(paramMap);
/*    */     }
/*    */     
/* 71 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 72 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   private Map<String, Object> getSelectPageConditions(Map<String, Object> paramMap) {
/* 78 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 79 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 80 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 81 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 528708, "pageName");
/* 82 */     searchConditionItem1.setIsQuickSearch(true);
/* 83 */     arrayList.add(searchConditionItem1);
/* 84 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 528709, "appName");
/* 85 */     arrayList.add(searchConditionItem2);
/*    */     
/* 87 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 88 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MobilemodeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */