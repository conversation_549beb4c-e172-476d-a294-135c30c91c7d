/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.info.biz.InfoReportTransMethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class InfoReportOrgService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  32 */     String str1 = Util.null2String(paramMap.get("id"));
/*     */     
/*  34 */     String str2 = " (select t1.id,t1.pathid,t1.unitid,t2.name from info_reportunit t1, info_customunit t2 where  t1.unitid = t2.id  union all  select t1.id,t1.pathid,t1.unitid,t2.subcompanyname as name from info_reportunit t1, HrmsubCompany t2 where  t1.unitid = t2.id) t ";
/*     */ 
/*     */     
/*  37 */     String str3 = " t.id,t.unitid,t.name ";
/*  38 */     String str4 = " where t.pathid= " + str1;
/*  39 */     String str5 = Util.null2String(paramMap.get("name"));
/*  40 */     if (!"".equals(str5)) {
/*  41 */       str4 = str4 + " and  t.name like '%" + str5 + "%'";
/*     */     }
/*  43 */     String str6 = "t.id";
/*  44 */     String str7 = "desc";
/*  45 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  46 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  47 */     arrayList.add(new SplitTableColBean("true", "unitid"));
/*  48 */     arrayList.add((new SplitTableColBean("80%", SystemEnv.getHtmlLabelName(1329, this.user.getLanguage()), "name", "name", "", 1)).setIsInputCol(BoolAttr.TRUE));
/*  49 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str2, str4, str6, "unitid", str7, arrayList);
/*  50 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  51 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  55 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  56 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  57 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  58 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 17728, "name", true));
/*  59 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  65 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  66 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  67 */     ArrayList arrayList = new ArrayList();
/*  68 */     String str1 = Util.null2String(paramMap.get("selectids"));
/*  69 */     RecordSet recordSet = new RecordSet();
/*  70 */     String str2 = "select * from info_reportunit where unitid in (" + str1 + ")";
/*  71 */     recordSet.executeQuery(str2, new Object[0]);
/*  72 */     InfoReportTransMethod infoReportTransMethod = new InfoReportTransMethod();
/*  73 */     while (recordSet.next()) {
/*  74 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  75 */       hashMap.put("id", recordSet.getString("id"));
/*  76 */       hashMap.put("unitid", recordSet.getString("unitid"));
/*  77 */       hashMap.put("name", infoReportTransMethod.getReportOrgName(Util.null2String(recordSet.getString("unitid"))));
/*  78 */       hashMap2.put(recordSet.getString("unitid"), hashMap);
/*     */     } 
/*  80 */     hashMap2.forEach((paramString, paramObject) -> paramList.add(paramObject));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  91 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  92 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  93 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  94 */     arrayList1.add((new ListHeadBean("unitid", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  95 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/*  96 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  97 */     hashMap1.put("datas", arrayList);
/*  98 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 103 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 104 */     RecordSet recordSet = new RecordSet();
/* 105 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("id"));
/* 106 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 107 */     String str3 = " (select t1.id,t1.pathid,t1.unitid,t2.name from info_reportunit t1, info_customunit t2 where  t1.unitid = t2.id  union all  select t1.id,t1.pathid,t1.unitid,t2.subcompanyname name from info_reportunit t1, HrmsubCompany t2 where  t1.unitid = t2.id) t ";
/*     */ 
/*     */     
/* 110 */     String str4 = " where t.pathid= " + str1;
/* 111 */     if (!"".equals(str2)) {
/* 112 */       str4 = str4 + " and (t.name like '%" + str2 + "%' ";
/* 113 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/* 115 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 116 */           str4 = str4 + " or f_GetPy(t.name) like '%" + str2.toUpperCase() + "%'";
/* 117 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 118 */           str4 = str4 + " or [dbo].f_GetPy(t.name) like '%" + str2.toUpperCase() + "%'";
/* 119 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 120 */           str4 = str4 + " or getpinyin(t.name) like '%" + str2.toUpperCase() + "%'";
/*     */         }  } 
/* 122 */       str4 = str4 + ")";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 130 */     recordSet.executeQuery("select t.id,t.unitid,t.name from " + str3 + str4 + " order by t.id", new Object[0]);
/* 131 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 132 */     while (recordSet.next()) {
/* 133 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 134 */       hashMap1.put("id", recordSet.getString("unitid"));
/* 135 */       hashMap1.put("name", recordSet.getString("name"));
/* 136 */       arrayList.add(hashMap1);
/*     */     } 
/* 138 */     hashMap.put("datas", arrayList);
/*     */     
/* 140 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/InfoReportOrgService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */