/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OrgGroupBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     String str1 = Util.null2String(paramMap.get("orgGroupName"));
/* 31 */     String str2 = Util.null2String(paramMap.get("orgGroupDesc"));
/* 32 */     String str3 = " and (isDelete is null or isDelete='0' or isDelete='') ";
/* 33 */     if (!str1.equals("")) {
/* 34 */       str3 = str3 + " and orgGroupName like '%";
/* 35 */       str3 = str3 + Util.fromScreen2(str1, 7);
/* 36 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 39 */     if (!str2.equals("")) {
/* 40 */       str3 = str3 + " and orgGroupDesc like '%";
/* 41 */       str3 = str3 + Util.fromScreen2(str2, 7);
/* 42 */       str3 = str3 + "%'";
/*    */     } 
/* 44 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*    */     
/* 46 */     String str4 = " id ,orgGroupName,orgGroupDesc ";
/* 47 */     String str5 = " HrmOrgGroup ";
/*    */     
/* 49 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 50 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 51 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(24679, this.user.getLanguage()), "orgGroupName", "orgGroupName", 1)).setIsInputCol(BoolAttr.TRUE));
/* 52 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(24680, this.user.getLanguage()), "orgGroupDesc", "orgGroupDesc"));
/*    */     
/* 54 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, "id", "id", arrayList);
/* 55 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 56 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 61 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 62 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 63 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 64 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 24679, "orgGroupName", true));
/* 66 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 24680, "orgGroupDesc"));
/* 67 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 76 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 77 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 78 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 79 */     RecordSet recordSet = new RecordSet();
/* 80 */     String str2 = "select id ,orgGroupName,orgGroupDesc from HrmOrgGroup where id in (" + str1 + ")";
/* 81 */     recordSet.executeSql(str2);
/* 82 */     while (recordSet.next()) {
/* 83 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 84 */       hashMap1.put("id", recordSet.getString("id"));
/* 85 */       hashMap1.put("orgGroupName", Util.null2String(recordSet.getString("orgGroupName")));
/* 86 */       hashMap1.put("orgGroupDesc", Util.null2String(recordSet.getString("orgGroupDesc")));
/* 87 */       arrayList.add(hashMap1);
/*    */     } 
/*    */     
/* 90 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 91 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 92 */     arrayList1.add(new ListHeadBean("orgGroupName", "", 1, BoolAttr.TRUE));
/* 93 */     arrayList1.add(new ListHeadBean("orgGroupDesc", "", 1, BoolAttr.TRUE));
/*    */     
/* 95 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 96 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 98 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OrgGroupBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */