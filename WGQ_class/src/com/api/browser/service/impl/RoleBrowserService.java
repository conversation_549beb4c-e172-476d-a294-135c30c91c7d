/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RoleBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  35 */     boolean bool = false;
/*  36 */     String str1 = Util.null2String(paramMap.get("rolesname"));
/*  37 */     String str2 = Util.null2String(paramMap.get("rolesmark"));
/*  38 */     String str3 = Util.null2String(paramMap.get("sqlwhere"));
/*  39 */     String str4 = Util.null2String(paramMap.get("canShowSanyuan"));
/*  40 */     boolean bool1 = HrmSanyuanAdminBiz.getSanyuanAble();
/*  41 */     if (!str3.equals(""))
/*  42 */       bool = true; 
/*  43 */     if (!str1.equals("")) {
/*  44 */       if (!bool) {
/*  45 */         bool = true;
/*     */         
/*  47 */         str3 = str3 + " where rolesname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */       }
/*     */       else {
/*     */         
/*  51 */         str3 = str3 + " and rolesname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */       } 
/*     */     }
/*  54 */     if (!str2.equals("")) {
/*  55 */       if (!bool) {
/*  56 */         bool = true;
/*     */         
/*  58 */         str3 = str3 + " where rolesmark like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */       }
/*     */       else {
/*     */         
/*  62 */         str3 = str3 + " and rolesmark like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */       } 
/*     */     }
/*     */     
/*  66 */     if (!str2.equals("")) {
/*  67 */       if (!bool) {
/*  68 */         bool = true;
/*     */         
/*  70 */         str3 = str3 + " where rolesmark like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */       }
/*     */       else {
/*     */         
/*  74 */         str3 = str3 + " and rolesmark like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */       } 
/*     */     }
/*     */     
/*  78 */     if (!bool1 || (!str4.equals("") && str4.equals("false"))) {
/*  79 */       if (!bool) {
/*  80 */         bool = true;
/*  81 */         str3 = str3 + " where (sanyuanType is null or sanyuanType not in (1,2,3)) ";
/*     */       } else {
/*  83 */         str3 = str3 + " and (sanyuanType is null or sanyuanType not in (1,2,3)) ";
/*     */       } 
/*     */     }
/*     */     
/*  87 */     String str5 = "id,rolesname,rolesmark";
/*  88 */     String str6 = "HrmRoles";
/*  89 */     String str7 = "rolesmark";
/*  90 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  91 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  92 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(15068, this.user.getLanguage()), "rolesmark", "rolesmark")).setIsInputCol(BoolAttr.TRUE));
/*  93 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(25734, this.user.getLanguage()), "rolesname", "rolesname"));
/*     */     
/*  95 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str3, str7, "id", arrayList);
/*  96 */     splitTableBean.setSqlsortway("ASC");
/*  97 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  98 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 103 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 104 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 105 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 106 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "rolesmark", true));
/* 107 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 85, "rolesname"));
/* 108 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 109 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/RoleBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */