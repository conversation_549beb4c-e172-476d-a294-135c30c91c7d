/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.engine.SAPIntegration.biz.SAPBrowser.SAPBrowserIntegrationBiz;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ public class SAPSingleBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 19 */     Map<String, Object> map = (Map)new HashMap<>();
/*    */     try {
/* 21 */       SAPBrowserIntegrationBiz sAPBrowserIntegrationBiz = new SAPBrowserIntegrationBiz();
/*    */       
/* 23 */       String str1 = Util.null2String(paramMap.get("fielddbtype"));
/*    */       
/* 25 */       String str2 = Util.null2String(paramMap.get("rowIndex"));
/* 26 */       String str3 = Util.null2String(paramMap.get("wfid"));
/* 27 */       paramMap.put("detailRow", str2);
/* 28 */       paramMap.put("workflowid", str3);
/* 29 */       if ("".equals(str1)) {
/* 30 */         return map;
/*    */       }
/* 32 */       paramMap.put("mark", str1);
/*    */       
/* 34 */       map = sAPBrowserIntegrationBiz.getRequestList(paramMap, this.user);
/* 35 */     } catch (Exception exception) {
/* 36 */       exception.printStackTrace();
/*    */     } 
/* 38 */     return map;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 45 */       String str = Util.null2String(paramMap.get("fielddbtype"));
/* 46 */       int i = Util.getIntValue(Util.null2String(paramMap.get("fromNodeorReport")), 0);
/* 47 */       int j = Util.getIntValue(Util.null2String(paramMap.get("rowIndex")), 0);
/* 48 */       ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 49 */       hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 50 */       SAPBrowserIntegrationBiz sAPBrowserIntegrationBiz = new SAPBrowserIntegrationBiz();
/* 51 */       boolean bool = sAPBrowserIntegrationBiz.getDisplaySearchAdInBar(str);
/* 52 */       List<Map> list = sAPBrowserIntegrationBiz.getSearchFieldMap(str, i, j);
/* 53 */       for (byte b = 0; b < list.size(); b++) {
/* 54 */         Map map = list.get(b);
/* 55 */         String str1 = (String)map.get("name");
/* 56 */         String str2 = (String)map.get("showname");
/* 57 */         String str3 = "field" + (String)map.get("fieldid");
/* 58 */         String str4 = Util.null2String((String)map.get("constValue"));
/* 59 */         String str5 = Util.null2String(paramMap.get(str3));
/* 60 */         if (!"".equals(str4)) {
/* 61 */           str5 = str4;
/*    */         }
/* 63 */         SearchConditionItem searchConditionItem = new SearchConditionItem(ConditionType.INPUT, str2, new String[] { str1 });
/* 64 */         searchConditionItem.setValue(str5);
/*    */         
/* 66 */         if (bool) {
/* 67 */           searchConditionItem.setDefaultDisplayInBar(true);
/*    */         }
/* 69 */         arrayList.add(searchConditionItem);
/*    */       } 
/* 71 */     } catch (Exception exception) {
/* 72 */       exception.printStackTrace();
/*    */     } 
/* 74 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SAPSingleBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */