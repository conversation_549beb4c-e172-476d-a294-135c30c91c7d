/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContacterTitleBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]";
/*    */   
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 52 */     String str1 = Util.null2String(paramMap.get("fullname"));
/* 53 */     String str2 = Util.null2String(paramMap.get("description"));
/* 54 */     String str3 = " ";
/* 55 */     if (!str1.equals("")) {
/* 56 */       str3 = str3 + " and fullname like '%";
/* 57 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 58 */       str3 = str3 + "%'";
/*    */     } 
/* 60 */     if (!str2.equals("")) {
/* 61 */       str3 = str3 + " and description like '%";
/* 62 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 63 */       str3 = str3 + "%'";
/*    */     } 
/* 65 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*    */     
/* 67 */     String str4 = " id,fullname,description ";
/* 68 */     String str5 = "CRM_ContacterTitle";
/*    */     
/* 70 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 71 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 72 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname")).setIsInputCol(BoolAttr.TRUE));
/* 73 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*    */     
/* 75 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, "orderkey", "id", "asc", arrayList);
/*    */     
/*    */     try {
/* 78 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 79 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 80 */     } catch (Exception exception) {
/* 81 */       exception.printStackTrace();
/*    */     } 
/*    */ 
/*    */     
/* 85 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 86 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 91 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 92 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 93 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 94 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname", true));
/* 95 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 96 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 97 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ContacterTitleBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */