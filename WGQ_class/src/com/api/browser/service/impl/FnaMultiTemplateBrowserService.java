/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
/*     */ import com.engine.fnaMulDimensions.util.FnaBrowserUtils;
/*     */ import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaMultiTemplateBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  38 */     if (this.user == null) {
/*  39 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  40 */       return (Map)hashMap;
/*     */     } 
/*  42 */     String str = Util.null2String(paramMap.get("list"));
/*  43 */     if ("1".equals(str)) {
/*     */       
/*  45 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  46 */       List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/*  47 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */     } else {
/*  49 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  50 */       SplitTableBean splitTableBean = getTableList(paramMap);
/*  51 */       if (splitTableBean == null) {
/*  52 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  53 */         arrayList.add(new ListHeadBean("approvalName", SystemEnv.getHtmlLabelName(195, this.user.getLanguage())));
/*  54 */         arrayList.add(new ListHeadBean("approvalTypeName", SystemEnv.getHtmlLabelName(515303, this.user.getLanguage())));
/*  55 */         arrayList.add(new ListHeadBean("approvalVersion", SystemEnv.getHtmlLabelName(567, this.user.getLanguage())));
/*  56 */         arrayList.add(new ListHeadBean("apprvoalActivation", SystemEnv.getHtmlLabelName(82689, this.user.getLanguage())));
/*  57 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  58 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */       } else {
/*  60 */         hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  67 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  78 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  80 */     if (this.user == null) {
/*  81 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  82 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  85 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  86 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  88 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 195, "approvalName");
/*  89 */     searchConditionItem1.setIsQuickSearch(true);
/*  90 */     arrayList.add(searchConditionItem1);
/*     */     
/*  92 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 515303, "apprvoalTypeName");
/*  93 */     arrayList.add(searchConditionItem2);
/*     */     
/*  95 */     LinkedList<SearchConditionOption> linkedList = new LinkedList();
/*  96 */     linkedList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(82686, this.user.getLanguage())));
/*  97 */     linkedList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(737, this.user.getLanguage())));
/*  98 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT, 82689, "apprvoalActivation");
/*  99 */     searchConditionItem3.setOptions(linkedList);
/* 100 */     searchConditionItem3.setSelectWidth("225");
/* 101 */     searchConditionItem3.setViewAttr(2);
/* 102 */     arrayList.add(searchConditionItem3);
/*     */     
/* 104 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/* 106 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 118 */     String str1 = FnaBrowserUtils.getAccountInfo(paramMap, this.user.getUID());
/* 119 */     if ("".equals(str1)) {
/* 120 */       return null;
/*     */     }
/* 122 */     String str2 = FnaBrowserUtils.getTableName(str1, FnaAccTypeConstant.BUDGET_TEMPLATE.intValue());
/* 123 */     String str3 = FnaBrowserUtils.getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE.intValue());
/* 124 */     String str4 = FnaBrowserUtils.getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL.intValue());
/* 125 */     if ("".equals(str2)) {
/* 126 */       return null;
/*     */     }
/* 128 */     String str5 = Util.null2String(paramMap.get("approvalName"));
/* 129 */     String str6 = Util.null2String(paramMap.get("apprvoalTypeName"));
/* 130 */     String str7 = Util.null2String(paramMap.get("apprvoalActivation"));
/*     */     
/* 132 */     RecordSet recordSet = new RecordSet();
/* 133 */     String str8 = "";
/* 134 */     if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/*     */ 
/*     */ 
/*     */       
/* 138 */       str8 = " b.approvalName,c.approvaTypelName,c.approvalVersion,c.apprvoalActivation, a.id+'_" + str1 + "' id,  case when (c.apprvoalActivation=1) then '" + StringEscapeUtils.escapeSql(SystemEnv.getHtmlLabelName(737, this.user.getLanguage())) + "'  else '" + StringEscapeUtils.escapeSql(SystemEnv.getHtmlLabelName(82686, this.user.getLanguage())) + "' end as apprvoalActivationName ";
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 143 */       str8 = " b.approvalName,c.approvaTypelName,c.approvalVersion,c.apprvoalActivation, concat(a.id,'_" + str1 + "') id,  case when (c.apprvoalActivation=1) then '" + StringEscapeUtils.escapeSql(SystemEnv.getHtmlLabelName(737, this.user.getLanguage())) + "'  else '" + StringEscapeUtils.escapeSql(SystemEnv.getHtmlLabelName(82686, this.user.getLanguage())) + "' end as apprvoalActivationName ";
/*     */     } 
/*     */ 
/*     */     
/* 147 */     String str9 = str2 + " a  join " + str4 + " b on a.approvalId = b.id  join " + str3 + " c on c.id = b.approvalTypeId ";
/*     */ 
/*     */     
/* 150 */     String str10 = " where 1=1   and b.nodeType = 1 and b.nodeStatus = 1 and c.approvalFillDataSataus = 1 and c.approvaTypelstatus = 1 ";
/* 151 */     if (!"".equals(str5)) {
/* 152 */       str10 = str10 + " and b.approvalName like '%" + StringEscapeUtils.escapeSql(str5) + "%' ";
/*     */     }
/* 154 */     if (!"".equals(str6)) {
/* 155 */       str10 = str10 + " and c.approvaTypelName like '%" + StringEscapeUtils.escapeSql(str6) + "%' ";
/*     */     }
/* 157 */     if (!"".equals(str7)) {
/* 158 */       str10 = str10 + " and c.apprvoalActivation = " + StringEscapeUtils.escapeSql(str7) + " ";
/*     */     }
/* 160 */     String str11 = " c.approvaTypelName,c.approvalVersion ";
/* 161 */     String str12 = " a.id ";
/* 162 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 163 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 164 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "approvalName", "approvalName")).setIsInputCol(BoolAttr.TRUE));
/* 165 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(515303, this.user.getLanguage()), "approvaTypelName", "approvaTypelName"));
/* 166 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(567, this.user.getLanguage()), "approvalVersion", "approvalVersion", "com.engine.fnaMulDimensions.util.BudgetApprovalUtil.getApprovalVersion", "" + this.user
/* 167 */           .getLanguage()));
/* 168 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(82689, this.user.getLanguage()), "apprvoalActivationName", "apprvoalActivationName"));
/*     */     
/* 170 */     SplitTableBean splitTableBean = new SplitTableBean(str8, str9, str10, str11, str12, "ASC", arrayList);
/* 171 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 173 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) throws Exception {
/* 183 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 184 */     String str1 = FnaBrowserUtils.getAccountInfo(paramMap, this.user.getUID());
/* 185 */     if ("".equals(str1)) {
/* 186 */       return arrayList;
/*     */     }
/*     */     
/* 189 */     RecordSet recordSet = new RecordSet();
/* 190 */     String str2 = Util.null2String(paramMap.get("id"));
/*     */ 
/*     */     
/* 193 */     BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
/* 194 */     String str3 = budgetApprovalUtil.getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE.intValue());
/* 195 */     String str4 = budgetApprovalUtil.getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL.intValue());
/* 196 */     String str5 = budgetApprovalUtil.getTableName(str1, FnaAccTypeConstant.BUDGET_TEMPLATE.intValue());
/*     */     
/* 198 */     if ("".equals(str3) || "".equals(str4) || "".equals(str5)) {
/* 199 */       return arrayList;
/*     */     }
/*     */     
/* 202 */     String str6 = "";
/* 203 */     if ("".equals(str2)) {
/* 204 */       str6 = "select approvalVersGroupId id,approvaTypelName as name from " + str3 + " where approvalFillDataSataus = 1 and approvaTypelstatus = 1  GROUP BY approvalVersGroupId,approvaTypelName ";
/*     */     } else {
/*     */       
/* 207 */       String[] arrayOfString = str2.split("_");
/* 208 */       if ("apptype".equals(arrayOfString[1])) {
/* 209 */         str6 = "select id,approvalVersion as name from " + str3 + "  where approvalFillDataSataus = 1 and approvaTypelstatus = 1 and approvalVersGroupId = '" + arrayOfString[0] + "' GROUP BY id,approvalVersion order by approvalVersion,id ";
/*     */       }
/*     */       else {
/*     */         
/* 213 */         str6 = " select a.approvalName as name,b.id from " + str4 + " a  join " + str5 + " b on a.id = b.approvalId  where a.nodeType = 1 and a.nodeStatus = 1 and a.approvalTypeId = '" + arrayOfString[0] + "'";
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 219 */     recordSet.executeQuery(str6, new Object[0]);
/* 220 */     while (recordSet.next()) {
/* 221 */       String str7 = Util.null2String(recordSet.getString("id"));
/* 222 */       String str8 = Util.null2String(recordSet.getString("name"));
/* 223 */       boolean bool = false;
/* 224 */       if ("".equals(str2)) {
/* 225 */         bool = true;
/* 226 */         str7 = str7 + "_apptype";
/*     */       } else {
/* 228 */         String[] arrayOfString = str2.split("_");
/* 229 */         if ("apptype".equals(arrayOfString[1])) {
/* 230 */           str7 = str7 + "_version";
/* 231 */           bool = true;
/* 232 */           str8 = SystemEnv.getHtmlLabelName(567, this.user.getLanguage()) + str8;
/*     */         } else {
/* 234 */           str7 = str7 + "_" + str1;
/*     */         } 
/*     */       } 
/*     */       
/* 238 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 239 */       browserTreeNode.setId(str7);
/* 240 */       browserTreeNode.setName(str8);
/* 241 */       browserTreeNode.setPid(str2);
/* 242 */       browserTreeNode.setParent(bool);
/* 243 */       browserTreeNode.setCanClick(!bool);
/* 244 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 246 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaMultiTemplateBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */