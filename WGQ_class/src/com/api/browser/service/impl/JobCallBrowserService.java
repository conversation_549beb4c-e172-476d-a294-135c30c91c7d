/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JobCallBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 33 */     String str1 = Util.null2String(paramMap.get("name"));
/* 34 */     String str2 = Util.null2String(paramMap.get("description"));
/* 35 */     String str3 = " ";
/* 36 */     if (!str1.equals("")) {
/* 37 */       str3 = str3 + " and name like '%";
/* 38 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 39 */       str3 = str3 + "%'";
/*    */     } 
/* 41 */     if (!str2.equals("")) {
/* 42 */       str3 = str3 + " and description like '%";
/* 43 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 44 */       str3 = str3 + "%'";
/*    */     } 
/* 46 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*    */     
/* 48 */     String str4 = " id ,name,description ";
/* 49 */     String str5 = " HrmJobCall ";
/*    */     
/* 51 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 52 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 53 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 54 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*    */     
/* 56 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, "id", "id", "asc", arrayList);
/* 57 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 58 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 63 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 64 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 65 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 66 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 67 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true));
/* 68 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 69 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/JobCallBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */