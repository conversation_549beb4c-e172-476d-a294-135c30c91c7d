/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaInitSubjectIndustryService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  48 */     if (this.user == null) {
/*  49 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  50 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  53 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  54 */     SplitTableBean splitTableBean = getTableList(paramMap);
/*  55 */     if (splitTableBean == null) {
/*  56 */       ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  57 */       arrayList.add(new ListHeadBean("industryName", SystemEnv.getHtmlLabelName(501678, this.user.getLanguage())));
/*     */       
/*  59 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  60 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */     } else {
/*  62 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } 
/*     */ 
/*     */     
/*  66 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     String str1 = Util.null2String(paramMap.get("industryName"));
/*  80 */     String str2 = Util.null2String(paramMap.get("accountId"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  93 */     String[] arrayOfString = { "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "", "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()), "" + SystemEnv.getHtmlLabelName(523052, ThreadVarLanguage.getLang()) };
/*     */     
/*  95 */     StringBuffer stringBuffer = new StringBuffer();
/*  96 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  97 */       String str = arrayOfString[b];
/*  98 */       if (!"".equals(str))
/*     */       {
/*     */         
/* 101 */         if ("".equals(str2) || b != 0) {
/*     */ 
/*     */           
/* 104 */           if (stringBuffer.length() > 0) {
/* 105 */             stringBuffer.append(" union all ");
/*     */           }
/* 107 */           stringBuffer.append("select '").append(b).append("'").append(" id, ").append(" '").append(str).append("' industryName ");
/* 108 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "dm".equalsIgnoreCase(recordSet.getOrgindbtype()) || "st".equalsIgnoreCase(recordSet.getOrgindbtype()))
/* 109 */             stringBuffer.append(" from dual "); 
/*     */         } 
/*     */       }
/*     */     } 
/* 113 */     String str3 = "(" + stringBuffer.toString() + ")";
/* 114 */     if ("mysql".equalsIgnoreCase(recordSet.getDBType()) || "sqlserver".equalsIgnoreCase(recordSet.getDBType()) || "postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 115 */       str3 = str3 + " as a1";
/*     */     }
/* 117 */     if (str3.equals("()") || str3.trim().equals("() as a1")) {
/* 118 */       return null;
/*     */     }
/* 120 */     (new BaseBean()).writeLog("sb2:" + str3);
/*     */     
/* 122 */     String str4 = " id,industryName ";
/* 123 */     String str5 = " from " + str3;
/* 124 */     String str6 = " where 1=1   ";
/* 125 */     if (!"".equals(str1)) {
/* 126 */       str6 = str6 + " and industryName like '%" + StringEscapeUtils.escapeSql(str1) + "%'";
/*     */     }
/* 128 */     String str7 = " id ";
/*     */     
/* 130 */     String str8 = " id ";
/*     */     
/* 132 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 133 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 134 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(501678, this.user.getLanguage()), "industryName", "industryName")).setIsInputCol(BoolAttr.TRUE));
/* 135 */     return new SplitTableBean("fna:initSubjectIndustryList", "checkbox", PageIdConst.getPageSize("fna:initSubjectIndustryList", this.user.getUID(), "Fna"), "fna:initSubjectIndustryList", str4, str5, str6, str7, str8, "Asc", arrayList);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaInitSubjectIndustryService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */