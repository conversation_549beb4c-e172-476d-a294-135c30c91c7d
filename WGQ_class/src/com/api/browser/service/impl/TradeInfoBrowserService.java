/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TradeInfoBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"rangelower\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"rangeupper\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  54 */     String str1 = " * ";
/*  55 */     String str2 = " CRM_TradeInfo ";
/*  56 */     String str3 = " ";
/*  57 */     String str4 = " id ";
/*     */     
/*  59 */     String str5 = Util.null2String(paramMap.get("fullname"));
/*  60 */     String str6 = Util.null2String(paramMap.get("sqlwhere"));
/*  61 */     if (!"".equals(str5)) {
/*  62 */       str3 = str3 + " and fullname like '%";
/*  63 */       str3 = str3 + Util.fromScreen2(str5, this.user.getLanguage());
/*  64 */       str3 = str3 + "%'";
/*     */     } 
/*     */     
/*  67 */     if (!"".equals(str6)) {
/*  68 */       str3 = str3 + str6;
/*     */     }
/*  70 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*     */     
/*  72 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  73 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/*  74 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname")).setIsInputCol(BoolAttr.TRUE));
/*  75 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(1271, this.user.getLanguage()), "rangelower", "rangelower", "weaver.crm.Maint.CRMTransMethod.getUperOrLowperValue"));
/*  76 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(1270, this.user.getLanguage()), "rangeupper", "rangeupper", "weaver.crm.Maint.CRMTransMethod.getUperOrLowperValue"));
/*     */     
/*  78 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, str4, "id", arrayList);
/*  79 */     splitTableBean.setSqlsortway("ASC");
/*     */     
/*     */     try {
/*  82 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*  83 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"rangelower\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"rangeupper\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  84 */     } catch (Exception exception) {
/*  85 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  88 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  89 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  95 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  96 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  97 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  98 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname"));
/*  99 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 100 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/TradeInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */