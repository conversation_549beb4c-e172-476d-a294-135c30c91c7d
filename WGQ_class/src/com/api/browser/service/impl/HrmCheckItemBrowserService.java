/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmCheckItemBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 24 */     String str1 = " * ";
/* 25 */     String str2 = " from HrmCheckItem ";
/* 26 */     String str3 = " where 1=1 ";
/*    */     
/* 28 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 29 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 30 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(6117, this.user.getLanguage()), "checkitemname", null)).setIsInputCol(BoolAttr.TRUE));
/* 31 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(15752, this.user.getLanguage()), "checkitemexplain", null));
/* 32 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, Util.toHtmlForSplitPage(str3), "id", "id", arrayList);
/* 33 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 34 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmCheckItemBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */