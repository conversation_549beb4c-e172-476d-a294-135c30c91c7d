/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.engine.SAPIntegration.biz.SAPBrowser.SAPBrowserIntegrationBiz;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SAPMultiBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 17 */     Map<String, Object> map = (Map)new HashMap<>();
/*    */     try {
/* 19 */       SAPBrowserIntegrationBiz sAPBrowserIntegrationBiz = new SAPBrowserIntegrationBiz();
/* 20 */       String str1 = Util.null2String(paramMap.get("fielddbtype"));
/* 21 */       String str2 = Util.null2String(paramMap.get("rowIndex"));
/* 22 */       String str3 = Util.null2String(paramMap.get("workflowid"));
/* 23 */       paramMap.put("detailRow", str2);
/* 24 */       paramMap.put("workflowid", str3);
/* 25 */       if ("".equals(str1)) {
/* 26 */         return map;
/*    */       }
/* 28 */       paramMap.put("mark", str1);
/*    */       
/* 30 */       map = sAPBrowserIntegrationBiz.getRequestList(paramMap, this.user);
/* 31 */     } catch (Exception exception) {
/* 32 */       exception.printStackTrace();
/*    */     } 
/* 34 */     return map;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 39 */     return (new SAPSingleBrowserService()).getBrowserConditionInfo(paramMap);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SAPMultiBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */