/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class VotingInfoBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 28 */     String str1 = Util.null2String(paramMap.get("fullname"));
/* 29 */     String str2 = " where (status=1 or status=2) ";
/*    */     
/* 31 */     if (!str1.equals("")) {
/* 32 */       str2 = str2 + " and subject like '%";
/* 33 */       str2 = str2 + Util.fromScreen2(str1, this.user.getLanguage());
/* 34 */       str2 = str2 + "%'";
/*    */     } 
/*    */     
/* 37 */     String str3 = " id,subject ";
/* 38 */     String str4 = "voting";
/* 39 */     String str5 = " id ";
/*    */     
/* 41 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 42 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", ""));
/* 43 */     arrayList.add((new SplitTableColBean("70%", SystemEnv.getHtmlLabelName(24096, this.user.getLanguage()), "subject", "subject")).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 45 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "id", arrayList);
/* 46 */     splitTableBean.setSqlisdistinct("true");
/* 47 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 48 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/VotingInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */