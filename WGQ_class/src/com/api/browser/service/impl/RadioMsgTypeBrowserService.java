/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.util.Util_Object;
/*     */ import com.engine.msgcenter.bean.WeaPermission;
/*     */ import com.engine.msgcenter.util.HrmPracticalUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.collections.CollectionUtils;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class RadioMsgTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  33 */     String str1 = Util.null2String(paramMap.get("permissionType"));
/*  34 */     if (StringUtils.isBlank(str1))
/*  35 */       str1 = "broadCaster"; 
/*  36 */     String str2 = Util.null2String(paramMap.get("name"));
/*     */ 
/*     */     
/*  39 */     StringBuilder stringBuilder = new StringBuilder();
/*  40 */     stringBuilder.append("select * from ECOLOGY_HRMRESOURCE_PERMISSION where permissionType = ? ");
/*     */     
/*  42 */     List list1 = Util_Object.newInstance(WeaPermission.class, stringBuilder.toString(), new Object[] { str1 });
/*     */ 
/*     */     
/*  45 */     List list2 = HrmPracticalUtil.checkPermission(this.user.getUID(), list1);
/*     */ 
/*     */     
/*  48 */     String str3 = "id,name";
/*     */     
/*  50 */     String str4 = "ECOLOGY_BIZ_MOBILE_CONFIG ";
/*  51 */     stringBuilder.delete(0, stringBuilder.length());
/*  52 */     stringBuilder.append(" 1=1 and  enable = 'y' and");
/*     */     
/*  54 */     if (CollectionUtils.isNotEmpty(list2)) {
/*  55 */       stringBuilder.append(" id in  ( ");
/*  56 */       for (String str : list2)
/*  57 */         stringBuilder.append("'").append(str).append("'").append(","); 
/*  58 */       stringBuilder.deleteCharAt(stringBuilder.length() - 1);
/*  59 */       stringBuilder.append(")");
/*     */     } else {
/*  61 */       stringBuilder.append(" id is null");
/*     */     } 
/*     */     
/*  64 */     if (StringUtils.isNotBlank(str2)) {
/*  65 */       stringBuilder.append(" and name like '%" + str2 + "%'");
/*     */     }
/*  67 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  68 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  69 */     SplitTableColBean splitTableColBean = (new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(388297, this.user.getLanguage()), "name", "name", "weaver.general.Util.formatMultiLang", String.valueOf(this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE);
/*  70 */     splitTableColBean.setShowType(1);
/*  71 */     arrayList.add(splitTableColBean);
/*     */ 
/*     */     
/*  74 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, stringBuilder.toString(), "id", "id", "desc", arrayList);
/*  75 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */ 
/*     */ 
/*     */     
/*  79 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  85 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  86 */     String str1 = paramHttpServletRequest.getParameter("q");
/*     */ 
/*     */     
/*  89 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("permissionType"));
/*     */     
/*  91 */     if (StringUtils.isBlank(str2)) {
/*  92 */       str2 = "broadCaster";
/*     */     }
/*  94 */     StringBuilder stringBuilder = new StringBuilder();
/*  95 */     stringBuilder.append("select * from ECOLOGY_HRMRESOURCE_PERMISSION where permissionType = ? ");
/*     */     
/*  97 */     List list1 = Util_Object.newInstance(WeaPermission.class, stringBuilder.toString(), new Object[] { str2 });
/*     */ 
/*     */     
/* 100 */     List list2 = HrmPracticalUtil.checkPermission(this.user.getUID(), list1);
/*     */     
/* 102 */     stringBuilder.delete(0, stringBuilder.length());
/* 103 */     stringBuilder.append("select id,name from  ECOLOGY_BIZ_MOBILE_CONFIG where  1=1 and enable = 'y' and id in  ( ");
/* 104 */     for (String str : list2)
/* 105 */       stringBuilder.append("'").append(str).append("'").append(","); 
/* 106 */     stringBuilder.deleteCharAt(stringBuilder.length() - 1);
/* 107 */     stringBuilder.append(")")
/* 108 */       .append("and name like ?");
/*     */     
/* 110 */     RecordSet recordSet = new RecordSet();
/* 111 */     recordSet.executeQuery(stringBuilder.toString(), new Object[] { "%" + str1 + "%" });
/*     */     
/* 113 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 114 */     while (recordSet.next()) {
/* 115 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 116 */       hashMap1.put("id", recordSet.getString("id"));
/* 117 */       hashMap1.put("name", Util.formatMultiLang(recordSet.getString("name"), String.valueOf(this.user.getLanguage())));
/* 118 */       arrayList.add(hashMap1);
/*     */     } 
/* 120 */     hashMap.put("datas", arrayList);
/* 121 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 127 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 129 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 130 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 131 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 388297, "name", true));
/*     */     
/* 133 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/RadioMsgTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */