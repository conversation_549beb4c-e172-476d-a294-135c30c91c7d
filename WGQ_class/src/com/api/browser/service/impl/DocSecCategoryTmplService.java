/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DocSecCategoryTmplService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>(); ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 27 */     String str1 = "id,name,fromdir";
/* 28 */     String str2 = "DocSecCategoryTemplate";
/* 29 */     String str3 = "";
/* 30 */     String str4 = Util.null2String(paramMap.get("name"));
/* 31 */     if (!"".equals(str4)) {
/* 32 */       str3 = str3 + " name like '%" + str4 + "%'";
/*    */     }
/* 34 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id", ""));
/* 35 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", ""));
/* 36 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(19996, this.user.getLanguage()), "fromdir", "fromdir", "weaver.general.KnowledgeTransMethod.getMSSPath"));
/*    */     
/* 38 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, "id", "id", arrayList);
/* 39 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 40 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 45 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 46 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 47 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 48 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 229, "name", true));
/* 49 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 50 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DocSecCategoryTmplService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */