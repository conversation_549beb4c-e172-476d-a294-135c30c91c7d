/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class InfoReportOrgGroup
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 28 */     String str1 = Util.null2String(paramMap.get("id"));
/*    */     
/* 30 */     String str2 = "info_unitgroup";
/* 31 */     String str3 = " * ";
/* 32 */     String str4 = "where isused =1 and pathid= " + str1;
/*    */     
/* 34 */     String str5 = "id";
/* 35 */     String str6 = "desc";
/* 36 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 37 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 38 */     arrayList.add((new SplitTableColBean("80%", SystemEnv.getHtmlLabelName(1329, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/* 39 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str2, str4, str5, "id", str6, arrayList);
/* 40 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 41 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/InfoReportOrgGroup.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */