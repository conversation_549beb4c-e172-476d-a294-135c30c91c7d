/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ProjectStatusBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },        ],        \"key\": \"col1\"    }]";
/*    */   
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 46 */     String str1 = Util.null2String(paramMap.get("fullname"));
/* 47 */     String str2 = Util.null2String(paramMap.get("description"));
/* 48 */     String str3 = " ";
/* 49 */     boolean bool = false;
/* 50 */     if (!str2.equals("")) {
/* 51 */       if (!bool) {
/* 52 */         bool = true;
/* 53 */         str3 = str3 + " where t1.summary like '%";
/* 54 */         str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 55 */         str3 = str3 + "%'";
/*    */       } else {
/*    */         
/* 58 */         str3 = str3 + " and t1.summary like '%";
/* 59 */         str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 60 */         str3 = str3 + "%'";
/*    */       } 
/*    */     }
/*    */     
/* 64 */     String str4 = " t1.dsporder ";
/* 65 */     String str5 = " t1.id,t1.fullname,t1.description,t1.dsporder,t1.summary";
/* 66 */     String str6 = " Prj_ProjectStatus t1 join HtmlLabelInfo t2 on t1.fullname=t2.indexid and t2.languageid=" + this.user.getLanguage() + " ";
/* 67 */     if (!"".equals(str1)) {
/* 68 */       str6 = str6 + " and t2.labelname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*    */     }
/*    */     
/* 71 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 72 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 73 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "fullname", "fullname", "weaver.systeminfo.SystemEnv.getHtmlLabelNames", "" + this.user.getLanguage())).setIsInputCol(BoolAttr.TRUE));
/* 74 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "summary", "summary"));
/*    */     
/* 76 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str3, str4, "t1.id", arrayList);
/* 77 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 78 */     splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 79 */     splitTableBean.setSqlsortway("ASC");
/* 80 */     splitTableBean.setSqlisdistinct("true");
/* 81 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 82 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 87 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 88 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 89 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 90 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname", true));
/* 91 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 92 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 93 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ProjectStatusBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */