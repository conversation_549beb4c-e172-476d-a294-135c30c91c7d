/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CommonChildCheckBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  37 */     String str1 = Util.null2String(paramMap.get("fieldname"));
/*  38 */     String str2 = Util.null2String(paramMap.get("fieldlabel"));
/*  39 */     String str3 = Util.null2String(paramMap.get("detailtable"));
/*  40 */     String str4 = Util.null2String(paramMap.get("fieldhtmltype"));
/*  41 */     String str5 = Util.null2String(paramMap.get("billid"));
/*  42 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isdetail")), 0);
/*  43 */     int j = Util.getIntValue(Util.null2String(paramMap.get("fieldid")), 0);
/*  44 */     String str6 = Util.null2String(paramMap.get("isbill"));
/*     */     
/*  46 */     String str7 = "";
/*  47 */     str7 = " where fieldhtmltype=" + str4 + " and billid=" + str5 + " ";
/*     */     
/*  49 */     str7 = str7 + " and type <> '2' ";
/*     */     
/*  51 */     str7 = str7 + " and id <> " + j + " ";
/*     */     
/*  53 */     str7 = str7 + " and (childfieldid is null or childfieldid = 0 or childfieldid = -1 or childfieldid =" + j + ") ";
/*  54 */     if (i == 1 && Strings.isNullOrEmpty(str3)) {
/*  55 */       str3 = str3.replace(" ", "");
/*  56 */       str7 = str7 + " and detailtable='" + str3 + "' ";
/*     */     } 
/*  58 */     if (j > 0) {
/*  59 */       str7 = str7 + " and id!=" + j + " ";
/*     */     }
/*     */     
/*  62 */     if ("1".equals(str6)) {
/*  63 */       if (!"".equals(str1)) {
/*  64 */         str7 = str7 + " and fieldname like '%" + str1 + "%' ";
/*     */       }
/*  66 */       if (!Strings.isNullOrEmpty(str2)) {
/*  67 */         str7 = str7 + " and exists (SELECT 1 FROM htmllabelinfo h WHERE h.indexid=b.fieldlabel AND h.labelname LIKE '%" + str2 + "%' AND h.languageid=" + this.user.getLanguage() + ") ";
/*     */       }
/*     */     } else {
/*  70 */       if (!Strings.isNullOrEmpty(str1)) {
/*  71 */         str7 = str7 + " and fieldname like '%" + str1 + "%' ";
/*     */       }
/*  73 */       if (!Strings.isNullOrEmpty(str2)) {
/*  74 */         str7 = str7 + " and description like '%" + str2 + "%' ";
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  79 */     str7 = str7 + " and selectItemType IN ('1','2') ";
/*  80 */     str7 = str7 + "  AND EXISTS (SELECT 1 FROM mode_selectitempagedetail s1,workflow_SelectItem s2               WHERE  s1.id=s2.pubid AND s2.fieldid=b.id                      AND (s1.cancel IS NULL OR s1.cancel!='1')                      AND exists(SELECT 1 FROM mode_selectitempagedetail s3 WHERE s3.pid=s1.id AND (s3.cancel IS NULL OR s3.cancel!='1') HAVING COUNT(1)>0)  )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  87 */     String str8 = " id ";
/*  88 */     String str9 = " ";
/*  89 */     String str10 = " ";
/*     */     
/*  91 */     if ("1".equals(str6)) {
/*  92 */       if (i == 0) {
/*  93 */         str9 = " id  , fieldlabel,fieldname, 0 as isdetail, 0 as fieldorder, '' as description, '' as optionkey,fieldhtmltype,type ";
/*  94 */         str10 = " workflow_billfield b ";
/*  95 */         str7 = str7 + " and viewtype = 0 ";
/*     */       } else {
/*  97 */         str9 = " id  , fieldlabel,fieldname, 1 as isdetail, 0 as fieldorder, '' as description, '' as optionkey,fieldhtmltype,type ";
/*  98 */         str10 = " workflow_billfield b ";
/*  99 */         str7 = str7 + " and viewtype <> 0 ";
/*     */       } 
/* 101 */       str7 = str7 + " and not exists (select 1 from workflow_billfield b1 where b1.pubchilchoiceid = b.id and b1.billid=" + str5 + " )";
/*     */     }
/* 103 */     else if (i == 0) {
/* 104 */       str9 = " id   ,fieldname, fieldname as fieldlabel, 0 as isdetail, 0 as fieldorder, description, '' as optionkey,fieldhtmltype,type ";
/* 105 */       str10 = " workflow_formdict b ";
/*     */     } else {
/* 107 */       str9 = " id  ,fieldname, fieldname as fieldlabel, 1 as isdetail, 0 as fieldorder, description, '' as optionkey,fieldhtmltype,type ";
/* 108 */       str10 = " workflow_formdictdetail b ";
/*     */     } 
/*     */ 
/*     */     
/* 112 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*     */     
/* 114 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */     
/* 116 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(124937, this.user.getLanguage()), "fieldname");
/* 117 */     arrayList.add(splitTableColBean1);
/*     */     
/* 119 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()), "fieldlabel");
/* 120 */     splitTableColBean2.setTransmethod("weaver.workflow.selectItem.SelectItemManager.getFieldLable");
/* 121 */     splitTableColBean2.setOtherpara(str6 + "+" + this.user.getLanguage());
/* 122 */     splitTableColBean2.setIsInputCol(BoolAttr.TRUE);
/* 123 */     arrayList.add(splitTableColBean2);
/*     */ 
/*     */ 
/*     */     
/* 127 */     SplitTableBean splitTableBean = new SplitTableBean(str9, str10, Util.toHtmlForSplitPage(str7), str8, "id", arrayList);
/* 128 */     splitTableBean.setInstanceid("workflowTypeListTable");
/* 129 */     splitTableBean.setPagesize("15");
/* 130 */     splitTableBean.setTableType("none");
/* 131 */     splitTableBean.setSqlisdistinct("true");
/* 132 */     splitTableBean.setSqlsortway("asc");
/*     */     
/* 134 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 135 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 141 */     String str1 = Util.null2String(paramMap.get("fieldname"));
/* 142 */     String str2 = Util.null2String(paramMap.get("fieldlabel"));
/*     */     
/* 144 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 146 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 147 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/* 149 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 150 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 124937, "fieldname");
/* 151 */     searchConditionItem1.setIsQuickSearch(Boolean.TRUE.booleanValue());
/* 152 */     searchConditionItem1.setLabelcol(9);
/* 153 */     searchConditionItem1.setFieldcol(13);
/* 154 */     searchConditionItem1.setValue(str1);
/*     */     
/* 156 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 15456, "fieldlabel");
/* 157 */     searchConditionItem2.setLabelcol(9);
/* 158 */     searchConditionItem2.setFieldcol(13);
/* 159 */     searchConditionItem2.setValue(str2);
/*     */     
/* 161 */     arrayList.add(searchConditionItem1);
/* 162 */     arrayList.add(searchConditionItem2);
/*     */     
/* 164 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 171 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 172 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("billid"));
/*     */     
/* 174 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 175 */     if (!Strings.isNullOrEmpty(str2)) {
/* 176 */       RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 183 */       String str = " select id,fieldlabel        from workflow_billfield b       where fieldhtmltype = 5         and billid = " + str2 + "         and type <> '2'         and (childfieldid is null or childfieldid = 0 or childfieldid = -1 ) \t\t  and exists (SELECT 1 FROM htmllabelinfo h WHERE h.indexid=b.fieldlabel AND h.labelname LIKE '%" + str1 + "%' AND h.languageid=" + this.user.getLanguage() + ")          and selectItemType IN ('1', '2')         AND EXISTS       (SELECT 1                FROM mode_selectitempagedetail s1, workflow_SelectItem s2               WHERE s1.id = s2.pubid                 AND s2.fieldid = b.id                 AND (s1.cancel IS NULL OR s1.cancel != '1')                 AND exists (SELECT 1                        FROM mode_selectitempagedetail s3                       WHERE s3.pid = s1.id                         AND (s3.cancel IS NULL OR s3.cancel != '1')                       HAVING COUNT(1) > 0))         and viewtype = 0         and not exists (select 1                from workflow_billfield b1               where b1.pubchilchoiceid = b.id                 and b1.billid = " + str2 + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 202 */       recordSet.executeQuery(str, new Object[0]);
/*     */       
/* 204 */       while (recordSet.next()) {
/* 205 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 206 */         hashMap1.put("id", recordSet.getString("id"));
/* 207 */         hashMap1.put("name", SystemEnv.getHtmlLabelName(recordSet.getInt("fieldlabel"), this.user.getLanguage()));
/* 208 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/*     */     
/* 212 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 213 */     hashMap.put("datas", arrayList);
/* 214 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CommonChildCheckBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */