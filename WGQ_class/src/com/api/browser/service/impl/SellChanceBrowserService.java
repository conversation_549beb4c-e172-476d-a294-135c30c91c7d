/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.crm.util.SellChanceShareUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.crm.report.CRMContractTransMethod;
/*     */ import weaver.crm.sellchance.SellstatusComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SellChanceBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"subject\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"endtatusid\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"preyield\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"createdate\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  78 */     int i = Util.getIntValue((String)paramMap.get("viewer"), 0);
/*     */ 
/*     */     
/*  81 */     String str1 = Util.fromScreen((String)paramMap.get("subject"), this.user.getLanguage());
/*  82 */     String str2 = Util.fromScreen((String)paramMap.get("customer"), this.user.getLanguage());
/*  83 */     String str3 = Util.fromScreen((String)paramMap.get("sellstatusid"), this.user.getLanguage());
/*  84 */     String str4 = Util.null2String(paramMap.get("preyield"));
/*  85 */     String str5 = Util.null2String(paramMap.get("preyield_1"));
/*  86 */     String str6 = Util.fromScreen((String)paramMap.get("endtatusid"), this.user.getLanguage());
/*     */     
/*  88 */     String str7 = " where t1.id != 0 ";
/*     */     
/*  90 */     if (!str1.equals("")) {
/*  91 */       str7 = str7 + " and t1.subject like '%" + str1 + "%'";
/*     */     }
/*     */     
/*  94 */     if (!str2.equals("")) {
/*  95 */       str7 = str7 + " and t1.customerid=" + str2;
/*     */     }
/*     */     
/*  98 */     if (!str3.equals("") && !str3.equals("-1")) {
/*  99 */       str7 = str7 + " and t1.sellstatusid=" + str3;
/*     */     }
/*     */     
/* 102 */     if (!str4.equals("")) {
/* 103 */       str7 = str7 + " and t1.preyield>=" + str4;
/*     */     }
/*     */     
/* 106 */     if (!str5.equals("")) {
/* 107 */       str7 = str7 + " and t1.preyield<=" + str5;
/*     */     }
/*     */     
/* 110 */     if (!str6.equals("") && !str6.equals("-1")) {
/* 111 */       str7 = str7 + " and t1.endtatusid =" + str6;
/*     */     }
/*     */     
/* 114 */     if (i != 0) {
/* 115 */       str7 = str7 + " and t1.creater ='" + i + "'";
/*     */     }
/*     */     
/* 118 */     CrmShareBase crmShareBase = new CrmShareBase();
/*     */     
/* 120 */     String str8 = (new SellChanceShareUtil(this.user)).getTempTable();
/* 121 */     String str9 = "t1.predate";
/* 122 */     String str10 = "t1.id,t1.subject,t1.preyield,t1.createdate,t1.sellstatusid,t1.endtatusid,t1.customerid,t1.predate";
/* 123 */     String str11 = "";
/* 124 */     if (this.user.getLogintype().equals("1")) {
/* 125 */       str11 = "CRM_SellChance  t1," + str8 + " t2,CRM_CustomerInfo t3  ";
/* 126 */       str7 = str7 + "  and t3.deleted=0  and t3.id= t1.customerid and t1.id = t2.id";
/*     */     } else {
/* 128 */       str11 = "CRM_SellChance t1,CRM_CustomerInfo t3";
/* 129 */       str7 = str7 + "and t3.deleted=0 and t3.id= t1.customerid  and t1.customerid=" + this.user.getUID();
/*     */     } 
/*     */     
/* 132 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 133 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */     
/* 135 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(82534, this.user.getLanguage()), "subject", "subject", 1);
/* 136 */     splitTableColBean1.setIsInputCol(BoolAttr.TRUE);
/* 137 */     splitTableColBean1.setTablename("CRM_SellChance");
/* 138 */     arrayList.add(splitTableColBean1);
/*     */     
/* 140 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(2248, this.user.getLanguage()), "preyield", "preyield");
/* 141 */     splitTableColBean2.setTablename("CRM_SellChance");
/* 142 */     arrayList.add(splitTableColBean2);
/*     */     
/* 144 */     arrayList.add(new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(1339, this.user.getLanguage()), "createdate", "createdate"));
/* 145 */     arrayList.add(new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(2250, this.user.getLanguage()), "sellstatusid", "sellstatusid", "weaver.crm.report.CRMContractTransMethod.getCRMSellStatus"));
/* 146 */     arrayList.add(new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(15112, this.user.getLanguage()), "endtatusid", "endtatusid", "weaver.crm.report.CRMContractTransMethod.getPigeonholeStatus", "" + this.user.getLanguage()));
/*     */     
/* 148 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(136, this.user.getLanguage()), "customerid", "customerid", "weaver.crm.Maint.CustomerInfoComInfo.getCustomerInfoname");
/* 149 */     splitTableColBean3.setTablename("CRM_CustomerInfo");
/* 150 */     arrayList.add(splitTableColBean3);
/*     */     
/* 152 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, str7, str9, "t1.id", arrayList);
/*     */     
/*     */     try {
/* 155 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 156 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"subject\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"endtatusid\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"preyield\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"createdate\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 157 */     } catch (Exception exception) {
/* 158 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 161 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 162 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 167 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 168 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 169 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 170 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 82534, "subject", true));
/*     */     
/* 172 */     String[] arrayOfString = { "preyield", "preyield_1" };
/* 173 */     arrayList.add(conditionFactory.createCondition(ConditionType.SCOPE, 2248, arrayOfString));
/*     */     
/* 175 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 136, "customer", "7"));
/*     */     
/* 177 */     if (!"2".equals(this.user.getLogintype())) {
/* 178 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 2097, "viewer", "1"));
/*     */     }
/* 180 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 181 */     arrayList1.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(235, this.user.getLanguage())));
/* 182 */     String str1 = "";
/* 183 */     String str2 = "";
/* 184 */     String str3 = "select * from CRM_SellStatus ";
/* 185 */     RecordSet recordSet = new RecordSet();
/* 186 */     recordSet.executeSql(str3);
/* 187 */     while (recordSet.next()) {
/* 188 */       str1 = recordSet.getString("id");
/* 189 */       str2 = recordSet.getString("fullname");
/* 190 */       if (!str2.equals("")) {
/* 191 */         arrayList1.add(new SearchConditionOption(str1, str2));
/*     */       }
/*     */     } 
/* 194 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 2250, "sellstatusid", arrayList1));
/*     */     
/* 196 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 197 */     arrayList2.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(235, this.user.getLanguage())));
/* 198 */     arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15242, this.user.getLanguage())));
/* 199 */     arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(498, this.user.getLanguage())));
/* 200 */     arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(1960, this.user.getLanguage())));
/* 201 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 15112, "endtatusid", arrayList2));
/*     */     
/* 203 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 204 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 209 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 210 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 211 */     if ("".equals(str1)) return (Map)hashMap; 
/* 212 */     String str2 = "select a.id,a.subject,a.preyield,a.createdate,a.endtatusid,a.customerid from CRM_SellChance a where a.id in (" + str1 + ")";
/* 213 */     RecordSet recordSet = new RecordSet();
/* 214 */     recordSet.executeQuery(str2, new Object[0]);
/* 215 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 216 */     CRMContractTransMethod cRMContractTransMethod = new CRMContractTransMethod();
/* 217 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 218 */     while (recordSet.next()) {
/* 219 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 220 */       hashMap1.put("id", recordSet.getString("id"));
/* 221 */       hashMap1.put("subject", recordSet.getString("subject"));
/* 222 */       hashMap1.put("preyield", recordSet.getString("preyield"));
/* 223 */       hashMap1.put("createdate", recordSet.getString("createdate"));
/* 224 */       hashMap1.put("endtatusid", cRMContractTransMethod.getPigeonholeStatus(recordSet.getString("endtatusid"), "" + this.user.getLanguage()));
/* 225 */       hashMap1.put("customerid", customerInfoComInfo.getCustomerInfoname(recordSet.getString("customerid")));
/* 226 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 229 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 230 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 231 */     arrayList1.add(new ListHeadBean("subject", SystemEnv.getHtmlLabelName(82534, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 232 */     arrayList1.add(new ListHeadBean("preyield", SystemEnv.getHtmlLabelName(2248, this.user.getLanguage())));
/* 233 */     arrayList1.add(new ListHeadBean("createdate", SystemEnv.getHtmlLabelName(1339, this.user.getLanguage())));
/* 234 */     arrayList1.add(new ListHeadBean("endtatusid", SystemEnv.getHtmlLabelName(15112, this.user.getLanguage())));
/* 235 */     arrayList1.add(new ListHeadBean("customerid", SystemEnv.getHtmlLabelName(136, this.user.getLanguage())));
/*     */     
/* 237 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 238 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 239 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 240 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 244 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 245 */     BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 246 */     paramHttpServletRequest.getParameter("q");
/* 247 */     paramHttpServletRequest.setAttribute("sqlwhere ", "q");
/* 248 */     String str1 = browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse);
/* 249 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 250 */     SellstatusComInfo sellstatusComInfo = new SellstatusComInfo();
/* 251 */     JSONArray jSONArray1 = JSONArray.parseArray(str1);
/* 252 */     String str2 = "";
/* 253 */     JSONArray jSONArray2 = new JSONArray();
/* 254 */     RecordSet recordSet = new RecordSet();
/* 255 */     for (byte b = 0; b < jSONArray1.size(); b++) {
/* 256 */       JSONObject jSONObject = jSONArray1.getJSONObject(b);
/* 257 */       String str3 = jSONObject.getString("id");
/* 258 */       String str4 = jSONObject.getString("name");
/* 259 */       String str5 = "select sellstatusid,customerid from CRM_SellChance where id= ?";
/* 260 */       recordSet.executeQuery(str5, new Object[] { str3 });
/* 261 */       if (recordSet.next()) {
/* 262 */         String str6 = sellstatusComInfo.getSellStatusname(recordSet.getString("sellstatusid"));
/* 263 */         String str7 = recordSet.getString("customerid");
/* 264 */         String str8 = customerInfoComInfo.getCustomerInfoname(str7);
/*     */         
/* 266 */         if ("".equals(str6) && !"".equals(str8)) {
/* 267 */           str2 = str3 + "&nbsp;|&nbsp;" + str4 + "&nbsp;|&nbsp;" + str8;
/*     */         }
/* 269 */         if (!"".equals(str6) && "".equals(str8)) {
/* 270 */           str2 = str3 + "&nbsp;|&nbsp;" + str4 + "&nbsp;|&nbsp;" + str6;
/*     */         }
/* 272 */         if (!"".equals(str6) && !"".equals(str8)) {
/* 273 */           str2 = str3 + "&nbsp;|&nbsp;" + str4 + "&nbsp;|&nbsp;" + str6 + "&nbsp;|&nbsp;" + str8;
/*     */         }
/* 275 */         if ("".equals(str6) && "".equals(str8)) {
/* 276 */           str2 = str3 + "&nbsp;|&nbsp;" + str4;
/*     */         }
/*     */         
/* 279 */         jSONObject.put("customerid", str7);
/* 280 */         jSONObject.put("customeridspan", str8);
/* 281 */         jSONObject.put("title", str2);
/* 282 */         jSONArray2.add(jSONObject);
/*     */       } 
/*     */     } 
/*     */     
/* 286 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray2.toString());
/* 287 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SellChanceBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */