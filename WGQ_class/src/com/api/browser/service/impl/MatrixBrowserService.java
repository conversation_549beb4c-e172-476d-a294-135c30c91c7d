/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixinfoComInfo;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.stream.Collectors;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.matrix.MatrixManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MatrixBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  37 */     String str1 = Util.null2String(paramMap.get("name"));
/*  38 */     String str2 = Util.null2String(paramMap.get("subcompanyid"));
/*  39 */     String str3 = Util.null2String(paramMap.get("showAll"));
/*  40 */     String str4 = "where 1 = 1  ";
/*  41 */     if (str1.length() > 0) {
/*  42 */       str4 = str4 + " and name like '%" + str1 + "%'";
/*     */     }
/*  44 */     if (str2.length() > 0) {
/*  45 */       str4 = str4 + " and subcompanyid in(" + str2 + ")";
/*     */     }
/*     */     
/*  48 */     boolean bool = false;
/*  49 */     if (str3.length() > 0 && "1".equalsIgnoreCase(str3)) {
/*  50 */       bool = true;
/*     */     }
/*     */     
/*  53 */     boolean bool1 = HrmUserVarify.checkUserRight("Matrix:Maint", this.user);
/*  54 */     RecordSet recordSet = new RecordSet();
/*  55 */     String str5 = " * ";
/*  56 */     String str6 = " matrixinfo ";
/*  57 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  58 */     boolean bool2 = manageDetachComInfo.isUseHrmManageDetach();
/*  59 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  60 */     int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "Matrix:Maint");
/*  61 */     String str7 = "";
/*     */     
/*  63 */     if (this.user.getUID() != 1 && 
/*  64 */       !bool) {
/*  65 */       String str8 = "";
/*  66 */       String str9 = "";
/*  67 */       if (bool2) {
/*  68 */         for (byte b = 0; arrayOfInt != null && b < arrayOfInt.length; b++) {
/*  69 */           int i = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "Matrix:Maint", arrayOfInt[b]);
/*  70 */           if (i > 0) {
/*  71 */             str7 = str7 + "," + arrayOfInt[b];
/*     */           }
/*     */         } 
/*  74 */         if (str7.length() > 0) {
/*  75 */           str7 = str7.substring(1);
/*  76 */           str8 = " ( issystem=1 or  issystem=2 or " + Util.getSubINClause(str7, "subcompanyid", "in") + ")";
/*     */         } else {
/*  78 */           str8 = " ( 1=2 )";
/*     */         }
/*     */       
/*  81 */       } else if (bool1) {
/*  82 */         str8 = " ( 1=1 )";
/*     */       } 
/*     */       
/*  85 */       MatrixManager matrixManager = new MatrixManager();
/*  86 */       List<CharSequence> list = matrixManager.getUserPermissionMatrixids(this.user);
/*  87 */       if (list != null && !list.isEmpty()) {
/*  88 */         String str = list.stream().collect(Collectors.joining(","));
/*  89 */         str9 = " " + Util.getSubINClause(str, "id", "in");
/*     */       } 
/*  91 */       if (str9.length() > 0 && str8.length() > 0) {
/*  92 */         str4 = str4 + " and (" + str8 + " or " + str9 + ")";
/*     */       } else {
/*  94 */         if (str9.length() > 0) {
/*  95 */           str4 = str4 + " and (" + str9 + ")";
/*     */         }
/*  97 */         if (str8.length() > 0) {
/*  98 */           str4 = str4 + " and (" + str8 + ")";
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 104 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 105 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 106 */     arrayList.add((new SplitTableColBean("33%", SystemEnv.getHtmlLabelName(10000224, Util.getIntValue(this.user.getLanguage())), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/* 107 */     arrayList.add(new SplitTableColBean("33%", SystemEnv.getHtmlLabelName(10000225, Util.getIntValue(this.user.getLanguage())), "descr", "descr", 1));
/* 108 */     if (bool2) {
/* 109 */       arrayList.add(new SplitTableColBean("33%", SystemEnv.getHtmlLabelName(19799, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubcompanyname", 1));
/*     */     }
/* 111 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", arrayList);
/* 112 */     splitTableBean.setSqlsortway("ASC");
/* 113 */     splitTableBean.setSqlisdistinct("true");
/* 114 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 115 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 121 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 122 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 123 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 124 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "34066,195", "name", true));
/* 125 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/* 126 */     boolean bool = manageDetachComInfo.isUseHrmManageDetach();
/* 127 */     if (bool) {
/* 128 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 141, "subcompanyid", "164"));
/*     */     }
/* 130 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 131 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 136 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 137 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 138 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 139 */     if (this.user != null && !"".equals(str)) {
/* 140 */       String str1 = "";
/* 141 */       MatrixinfoComInfo matrixinfoComInfo = new MatrixinfoComInfo();
/* 142 */       RecordSet recordSet = new RecordSet();
/* 143 */       recordSet.executeQuery(str1, new Object[0]);
/* 144 */       if (str.length() > 0) {
/* 145 */         String[] arrayOfString = str.split(",");
/*     */         
/* 147 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 148 */           String str2 = arrayOfString[b];
/* 149 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 150 */           hashMap1.put("id", str2);
/* 151 */           hashMap1.put("name", matrixinfoComInfo.getName(str2));
/* 152 */           hashMap1.put("descr", matrixinfoComInfo.getDescr(str2));
/* 153 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/*     */       
/* 157 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 158 */       arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 159 */       arrayList1.add((new ListHeadBean("name", "")).setIsInputCol(BoolAttr.TRUE));
/* 160 */       arrayList1.add(new ListHeadBean("descr", ""));
/* 161 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 162 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 163 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */       
/* 165 */       return (Map)hashMap;
/*     */     } 
/* 167 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MatrixBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */