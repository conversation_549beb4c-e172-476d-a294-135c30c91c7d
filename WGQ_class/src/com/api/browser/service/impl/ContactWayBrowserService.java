/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContactWayBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]";
/*    */   
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 47 */     String str1 = " * ";
/* 48 */     String str2 = " CRM_ContactWay ";
/* 49 */     String str3 = " ";
/*    */     
/* 51 */     String str4 = Util.null2String(paramMap.get("fullname"));
/* 52 */     String str5 = Util.null2String(paramMap.get("description"));
/* 53 */     String str6 = Util.null2String(paramMap.get("sqlwhere"));
/* 54 */     if (!"".equals(str4)) {
/* 55 */       str3 = str3 + " and fullname like '%";
/* 56 */       str3 = str3 + Util.fromScreen2(str4, this.user.getLanguage());
/* 57 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 60 */     if (!"".equals(str5)) {
/* 61 */       str3 = str3 + " and description like '%";
/* 62 */       str3 = str3 + Util.fromScreen2(str5, this.user.getLanguage());
/* 63 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 66 */     if (!"".equals(str6)) {
/* 67 */       str3 = str3 + str6;
/*    */     }
/* 69 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*    */     
/* 71 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 72 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 73 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname")).setIsInputCol(BoolAttr.TRUE));
/* 74 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*    */     
/* 76 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, "orderkey", "id", "asc", arrayList);
/*    */     
/*    */     try {
/* 79 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 80 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 81 */     } catch (Exception exception) {
/* 82 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 85 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 86 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 93 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 94 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 95 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname"));
/* 96 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 98 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ContactWayBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */