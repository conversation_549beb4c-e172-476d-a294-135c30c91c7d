/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.engine.cube.biz.SqlHelper;
/*     */ import com.engine.cube.util.WorkFLowToModeUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ModeInfoBrowserService
/*     */   extends BrowserService
/*     */ {
/*  42 */   private ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  43 */   private boolean isUseFmManageDetach = this.manageDetachComInfo
/*  44 */     .isUseFmManageDetach();
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  49 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  50 */     String str1 = Util.null2String(paramMap.get("modename"));
/*  51 */     String str2 = Util.null2String(paramMap.get("isFromWorkflow"));
/*  52 */     String str3 = " where (t1.isdelete is null or t1.isdelete = 0) and exists (select 1 from modeTreeField t2 where t2.id = t1.modetype and (isdelete is null or isdelete = 0)) and t1.formid!=0  ";
/*  53 */     if ("true".equals(str2)) {
/*  54 */       str3 = str3 + "and not exists(select id from ModeFormExtend m where t1.formid = m.formid)";
/*     */     }
/*  56 */     if (!str1.equals("")) {
/*  57 */       str3 = str3 + " and t1.modename like '%" + str1 + "%'";
/*     */     }
/*  59 */     boolean bool = WorkFLowToModeUtil.checkWorkFlowRight(paramMap, this.user, new HashMap<>());
/*     */     
/*  61 */     if (this.isUseFmManageDetach && !bool) {
/*  62 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  63 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "ModeSetting:All", 0);
/*     */       
/*  65 */       String str7 = "";
/*  66 */       String str8 = "";
/*  67 */       ArrayList<String> arrayList1 = new ArrayList();
/*  68 */       byte b1 = 0;
/*  69 */       for (byte b2 = 0; b2 < arrayOfInt.length; b2++) {
/*  70 */         str7 = str7 + "," + arrayOfInt[b2];
/*  71 */         str8 = str8 + "," + arrayOfInt[b2];
/*  72 */         if (b1 % 1000 == 0 || b1 == arrayOfInt.length - 1) {
/*  73 */           arrayList1.add(str8);
/*  74 */           str8 = "";
/*     */         } 
/*  76 */         b1++;
/*     */       } 
/*  78 */       if (str7.equals("")) {
/*  79 */         str3 = str3 + " and 1=2 ";
/*     */       } else {
/*  81 */         String str = "";
/*  82 */         for (String str9 : arrayList1) {
/*  83 */           str9 = str9.substring(1);
/*  84 */           str = str + "  t1.subCompanyId in (" + str9 + ") or ";
/*     */         } 
/*  86 */         if (!str.equals("")) {
/*  87 */           str = str.substring(0, str.length() - 3);
/*     */         }
/*  89 */         str3 = str3 + " and (" + str + ")";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  94 */     String str4 = " t1.id,t1.modename,t1.modedesc,t1.formid ";
/*  95 */     String str5 = " modeinfo t1 ";
/*  96 */     String str6 = " t1.dsporder,t1.id ";
/*  97 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  98 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  99 */     arrayList.add(new SplitTableColBean("true", "formid"));
/* 100 */     arrayList.add((new SplitTableColBean("50%", "" + SystemEnv.getHtmlLabelName(28485, ThreadVarLanguage.getLang()) + "", "modename", "modename")).setIsInputCol(BoolAttr.TRUE));
/* 101 */     arrayList.add(new SplitTableColBean("50%", "" + SystemEnv.getHtmlLabelName(81710, ThreadVarLanguage.getLang()) + "", "modedesc", "modedesc"));
/* 102 */     SplitTableColBean splitTableColBean = new SplitTableColBean("0%", "" + SystemEnv.getHtmlLabelName(19532, ThreadVarLanguage.getLang()) + "ID", "formid", "formid");
/* 103 */     splitTableColBean.setHide("true");
/* 104 */     arrayList.add(splitTableColBean);
/* 105 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "t1.id", arrayList);
/* 106 */     splitTableBean.setSqlsortway("ASC");
/* 107 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 108 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 115 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 116 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 117 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 118 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 28485, "modename").setIsQuickSearch(true));
/* 119 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 120 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 125 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 126 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 127 */     String str2 = paramHttpServletRequest.getParameter("isFromWorkflow");
/* 128 */     String str3 = " where (t1.isdelete is null or t1.isdelete = 0) and exists (select 1 from modeTreeField t2 where t2.id = t1.modetype and (isdelete is null or isdelete = 0))  and t1.modename like '%" + str1 + "%' ";
/* 129 */     if ("true".equals(str2)) {
/* 130 */       str3 = str3 + "and not exists(select id from ModeFormExtend m where t1.formid = m.formid)";
/*     */     }
/* 132 */     Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 133 */     boolean bool = WorkFLowToModeUtil.checkWorkFlowRight(map, this.user, new HashMap<>());
/*     */     
/* 135 */     if (this.isUseFmManageDetach && !bool) {
/* 136 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 137 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEFORM:ALL", 0);
/*     */       
/* 139 */       if (arrayOfInt.length == 0) {
/* 140 */         str3 = str3 + " and 1=2 ";
/*     */       } else {
/* 142 */         str3 = str3 + " and " + SqlHelper.SplitSqlInCondition("t1.subCompanyId", arrayOfInt);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 147 */     String str4 = "select t1.id,t1.modename,t1.modedesc,t1.formid from modeinfo t1 " + str3 + " order by t1.dsporder,t1.id";
/*     */     
/* 149 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 150 */     RecordSet recordSet = new RecordSet();
/* 151 */     recordSet.executeQuery(str4, new Object[0]);
/* 152 */     while (recordSet.next()) {
/* 153 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 154 */       hashMap1.put("id", recordSet.getString("id"));
/* 155 */       hashMap1.put("name", recordSet.getString("modename"));
/* 156 */       hashMap1.put("formid", recordSet.getString("formid"));
/* 157 */       arrayList.add(hashMap1);
/*     */     } 
/* 159 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 160 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */