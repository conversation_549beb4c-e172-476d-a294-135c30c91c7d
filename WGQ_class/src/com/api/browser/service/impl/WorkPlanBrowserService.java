/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import java.text.ParseException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.WorkPlan.MutilUserUtil;
/*     */ import weaver.WorkPlan.WorkPlanShareUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fullsearch.util.SearchBrowserUtils;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class WorkPlanBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  45 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  46 */     boolean bool = false;
/*     */     
/*  48 */     boolean bool1 = SearchBrowserUtils.quickSearchValidate("WKPSEARCH", "" + this.user.getLanguage());
/*  49 */     if (bool1) {
/*  50 */       bool = true;
/*     */     }
/*     */     
/*  53 */     if (bool && (
/*  54 */       !HrmClassifiedProtectionBiz.isOpenClassification() || (HrmClassifiedProtectionBiz.isOpenClassification() && SearchBrowserUtils.isSupportSecretLevel()))) {
/*  55 */       Map<String, Object> map = getBrowserDateFromSearch(paramMap);
/*  56 */       hashMap.putAll(map);
/*  57 */       return (Map)hashMap;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  62 */     String str1 = "";
/*  63 */     String str2 = "";
/*  64 */     String str3 = "";
/*  65 */     String str4 = " where 1=1 ";
/*     */     
/*  67 */     String str5 = Util.null2String(paramMap.get("planname"));
/*  68 */     String str6 = Util.null2String(paramMap.get("urgentlevel"));
/*  69 */     String str7 = Util.null2String(paramMap.get("plantype"));
/*  70 */     String str8 = Util.null2String(paramMap.get("planstatus"));
/*  71 */     String str9 = Util.null2String(paramMap.get("createrid"));
/*  72 */     String str10 = Util.null2String(paramMap.get("receiveID"));
/*  73 */     int i = Util.getIntValue((String)paramMap.get("timeSag"), 0);
/*  74 */     int j = Util.getIntValue((String)paramMap.get("timeSagEnd"), 0);
/*  75 */     String str11 = Util.null2String(paramMap.get("begindate"));
/*  76 */     String str12 = Util.null2String(paramMap.get("enddate"));
/*  77 */     String str13 = Util.null2String(paramMap.get("begindate2"));
/*  78 */     String str14 = Util.null2String(paramMap.get("enddate2"));
/*  79 */     int k = Util.getIntValue((String)paramMap.get("secretLevel"), -1);
/*  80 */     if (k == -1 && HrmClassifiedProtectionBiz.isOpenClassification()) {
/*  81 */       HrmClassifiedProtectionBiz hrmClassifiedProtectionBiz = new HrmClassifiedProtectionBiz();
/*  82 */       k = Util.getIntValue(hrmClassifiedProtectionBiz.getMaxResourceSecLevel(this.user), -1);
/*     */     } 
/*  84 */     str4 = "where workPlan.ID = workPlanShareDetail.workID";
/*  85 */     if (!"".equals(str5) && null != str5) {
/*     */       
/*  87 */       str5 = str5.replaceAll("\"", "＂");
/*  88 */       str5 = str5.replaceAll("'", "＇");
/*  89 */       str4 = str4 + " AND workPlan.name LIKE '%" + str5 + "%'";
/*     */     } 
/*  91 */     if (!"".equals(str6) && null != str6)
/*     */     {
/*  93 */       if ("1".equals(str6)) {
/*  94 */         str4 = str4 + " AND (workPlan.urgentLevel = '1' or workPlan.urgentLevel='')";
/*     */       } else {
/*  96 */         str4 = str4 + " AND workPlan.urgentLevel = '" + str6 + "'";
/*     */       } 
/*     */     }
/*  99 */     if (!"".equals(str7) && null != str7)
/*     */     {
/* 101 */       str4 = str4 + " AND workPlan.type_n = '" + str7 + "'";
/*     */     }
/* 103 */     if (!"".equals(str8) && null != str8)
/*     */     {
/* 105 */       str4 = str4 + " AND workPlan.status = '" + str8 + "'";
/*     */     }
/* 107 */     if (!"".equals(str9) && null != str9)
/*     */     {
/* 109 */       str4 = str4 + " AND workPlan.createrid = " + str9;
/*     */     }
/* 111 */     if (k > -1) {
/* 112 */       str4 = str4 + " AND workPlan.secretLevel >= " + k;
/*     */     }
/*     */ 
/*     */     
/* 116 */     if (!"".equals(str10) && null != str10) {
/*     */       
/* 118 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 120 */       if (recordSet.getDBType().equals("oracle")) {
/* 121 */         str4 = str4 + " and ( ','||workPlan.resourceID||',' LIKE '%," + str10 + ",%') ";
/* 122 */       } else if (recordSet.getDBType().equalsIgnoreCase("mysql")) {
/* 123 */         str4 = str4 + " and ( CONCAT(',',workPlan.resourceID,',') LIKE '%," + str10 + ",%') ";
/*     */       }
/* 125 */       else if (recordSet.getDBType().equals("postgresql")) {
/* 126 */         str4 = str4 + " and ( ','||workPlan.resourceID||',' LIKE '%," + str10 + ",%') ";
/*     */       } else {
/*     */         
/* 129 */         str4 = str4 + " and ( ','+workPlan.resourceID+',' LIKE '%," + str10 + ",%') ";
/*     */       } 
/*     */     } 
/*     */     
/* 133 */     if (i != 6) {
/* 134 */       String str16 = TimeUtil.getDateByOption("" + i, "0");
/* 135 */       String str17 = TimeUtil.getDateByOption("" + i, "1");
/* 136 */       if (!str16.equals("")) {
/* 137 */         str4 = str4 + " and workPlan.beginDate >= '" + str16 + "'";
/*     */       }
/*     */       
/* 140 */       if (!str17.equals("")) {
/* 141 */         str4 = str4 + " and workPlan.beginDate <= '" + str17 + "'";
/*     */       
/*     */       }
/*     */     }
/* 145 */     else if (i == 6) {
/* 146 */       if (!"".equals(str11) && null != str11)
/*     */       {
/* 148 */         str4 = str4 + " AND workPlan.beginDate >= '" + str11 + "'";
/*     */       }
/* 150 */       if (!"".equals(str12) && null != str12)
/*     */       {
/* 152 */         str4 = str4 + " AND workPlan.beginDate <= '" + str12 + "'";
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 158 */     if (j > 0 && j < 6) {
/* 159 */       String str16 = TimeUtil.getDateByOption("" + j, "0");
/* 160 */       String str17 = TimeUtil.getDateByOption("" + j, "1");
/* 161 */       if (!str16.equals("")) {
/* 162 */         str4 = str4 + " and workPlan.endDate >= '" + str16 + "'";
/*     */       }
/*     */       
/* 165 */       if (!str17.equals("")) {
/* 166 */         str4 = str4 + " and workPlan.endDate <= '" + str17 + "'";
/*     */       
/*     */       }
/*     */     }
/* 170 */     else if (j == 6) {
/* 171 */       if (!"".equals(str13) && null != str13)
/*     */       {
/* 173 */         str4 = str4 + " AND workPlan.endDate >= '" + str13 + "'";
/*     */       }
/* 175 */       if (!"".equals(str14) && null != str14)
/*     */       {
/* 177 */         str4 = str4 + " AND workPlan.endDate <= '" + str14 + "'";
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 182 */     String str15 = WorkPlanShareUtil.getCanSeeShareSql(this.user);
/* 183 */     str1 = " workPlan.ID, workPlan.name,workPlan.resourceID, workPlan.urgentLevel, workPlan.type_n, workPlan.createrid, workPlan.status, workPlan.beginDate, workPlan.beginTime, workPlan.endDate,workPlan.endTime, workPlan.createDate, workPlan.createTime";
/* 184 */     str2 = " WorkPlan workPlan, (" + str15 + ") workPlanShareDetail ";
/* 185 */     str3 = "workPlan.beginDate, workPlan.beginTime";
/*     */ 
/*     */ 
/*     */     
/* 189 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str4, str3, "workPlan.ID", getSplitTableColList());
/* 190 */     splitTableBean.setSqlsortway("desc");
/*     */ 
/*     */     
/* 193 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 195 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/* 196 */     } catch (Exception exception) {
/* 197 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 201 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 202 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 210 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 211 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.name");
/* 212 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.beginDate");
/* 213 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row3.createrID");
/* 214 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List getSplitTableColList() {
/* 222 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 223 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 224 */     arrayList.add((new SplitTableColBean("25%", "", "name", "name", 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/* 225 */     arrayList.add((new SplitTableColBean("25%", "", "createrID", "createrID", "weaver.hrm.resource.ResourceComInfo.getResourcename")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/* 226 */     arrayList.add((new SplitTableColBean("25%", "", "beginDate", "beginDate", "com.api.meeting.util.MeetingTransMethod.getMeetingTime", "column:beginTime+column:endDate+column:endTime")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */ 
/*     */     
/* 229 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserDateFromSearch(Map<String, Object> paramMap) {
/* 238 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 240 */     int i = Util.getIntValue((String)paramMap.get("min"), 0);
/* 241 */     if (i <= 0) {
/* 242 */       i = Util.getIntValue((String)paramMap.get("min"), 0);
/*     */     }
/* 244 */     int j = Util.getIntValue((String)paramMap.get("pageSize"), 0);
/* 245 */     if (j <= 0) {
/* 246 */       j = Util.getIntValue((String)paramMap.get("pageSize"), 0);
/*     */     }
/* 248 */     int k = Util.getIntValue((String)paramMap.get("onlyCount"), 0);
/*     */ 
/*     */     
/* 251 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 252 */     linkedHashMap.put("begindate", "false");
/* 253 */     linkedHashMap.put("begintime", "false");
/*     */     
/* 255 */     k = (k == 0 && j <= 0) ? 1 : k;
/* 256 */     j = (j <= 0) ? 20 : j;
/* 257 */     i = i / j + 1;
/* 258 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 260 */     String str1 = " * ";
/* 261 */     SplitTableBean splitTableBean = new SplitTableBean();
/* 262 */     String str2 = Util.toHtmlForSplitPage(mapToSpitParam(paramMap));
/* 263 */     splitTableBean.setDatasource("com.api.browser.service.impl.WorkPlanBrowserService.getResultData");
/* 264 */     splitTableBean.setSourceparams(str2);
/* 265 */     splitTableBean.setPageUID("");
/*     */     
/* 267 */     splitTableBean.setBackfields(str1);
/* 268 */     splitTableBean.setSqlform("temp");
/* 269 */     splitTableBean.setSqlwhere("");
/* 270 */     splitTableBean.setSqlorderby("id");
/* 271 */     splitTableBean.setSqlprimarykey("id");
/* 272 */     splitTableBean.setSqlsortway("desc");
/* 273 */     splitTableBean.setPageBySelf("1");
/*     */     
/* 275 */     splitTableBean.setCols(getSplitTableColList());
/*     */ 
/*     */     
/* 278 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 280 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/* 281 */     } catch (Exception exception) {
/* 282 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 286 */     return SplitTableUtil.makeListDataResult(splitTableBean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String mapToSpitParam(Map<String, Object> paramMap) {
/* 296 */     StringBuffer stringBuffer = new StringBuffer();
/* 297 */     for (String str : paramMap.keySet()) {
/*     */       
/* 299 */       if (!str.isEmpty()) {
/*     */         
/* 301 */         String str1 = Util.null2String(paramMap.get(str));
/* 302 */         stringBuffer.append("".equals(stringBuffer.toString()) ? (str + ":" + str1) : ("+" + str + ":" + str1));
/*     */       } 
/*     */     } 
/* 305 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getResultData(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 318 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 320 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("min"), 0);
/* 321 */     if (i <= 0) {
/* 322 */       i = Util.getIntValue((String)paramHttpServletRequest.getAttribute("min"), 0);
/*     */     }
/* 324 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("pageSize"), 20);
/* 325 */     if (j <= 0);
/*     */ 
/*     */     
/* 328 */     int k = Util.getIntValue((String)paramHttpServletRequest.getAttribute("onlyCount"), 0);
/*     */ 
/*     */     
/* 331 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 332 */     linkedHashMap.put("BEGINDATE", "false");
/* 333 */     linkedHashMap.put("BEINGTIME", "false");
/*     */     
/* 335 */     k = (k == 0 && j <= 0) ? 1 : k;
/* 336 */     j = (j <= 0) ? 20 : j;
/* 337 */     i = i / j + 1;
/* 338 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     try {
/* 340 */       getSearchParams(paramMap, paramUser, (Map)hashMap2);
/* 341 */     } catch (ParseException parseException) {
/* 342 */       parseException.printStackTrace();
/*     */     } 
/*     */     
/* 345 */     hashMap2.put("loginid", paramUser.getLoginid());
/* 346 */     hashMap2.put("page", Integer.valueOf(i));
/* 347 */     hashMap2.put("pageSize", Integer.valueOf(j));
/* 348 */     hashMap2.put("schemaType", "WKP");
/* 349 */     if (MutilUserUtil.isShowBelongto(paramUser)) {
/* 350 */       hashMap2.put("belongtoShow", "1");
/*     */     }
/*     */ 
/*     */     
/* 354 */     Map map = SearchBrowserUtils.quickSearch(hashMap2, linkedHashMap, null);
/*     */     
/* 356 */     int m = Util.getIntValue(Util.null2String(map.get("count")), 0);
/* 357 */     if (k == 1) {
/* 358 */       hashMap1.put("recordCount", Integer.valueOf(m));
/* 359 */       return (Map)hashMap1;
/*     */     } 
/* 361 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/* 362 */     if (m > 0) {
/* 363 */       List<Map> list = (List)map.get("result");
/* 364 */       map.get("result");
/* 365 */       for (byte b = 0; b < list.size(); b++) {
/* 366 */         Map map1 = list.get(b);
/* 367 */         Map<String, String> map2 = list.get(b);
/* 368 */         map2.put("beginDate", map1.get("BEGINDATE"));
/* 369 */         map2.put("beginTime", Util.null2String(map1.containsKey("BEGINTIME") ? Boolean.valueOf(map1.containsKey("BEGINTIME")) : map1.get("BEINGTIME")));
/* 370 */         map2.put("createrID", (String)map1.get("CREATERID"));
/* 371 */         map2.put("endDate", (String)map1.get("ENDDATE"));
/* 372 */         map2.put("endTime", (String)map1.get("ENDTIME"));
/* 373 */         map2.put("name", (String)map1.get("title"));
/* 374 */         map2.put("id", (String)map1.get("ID"));
/* 375 */         arrayList.add(map2);
/*     */       } 
/*     */     } 
/* 378 */     hashMap1.put("recordCount", Integer.valueOf(m));
/* 379 */     hashMap1.put("dataAll", arrayList);
/* 380 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getSearchParams(Map<String, String> paramMap, User paramUser, Map<String, Object> paramMap1) throws ParseException {
/* 391 */     Iterator<String> iterator = paramMap.keySet().iterator();
/* 392 */     String str1 = "";
/* 393 */     String str2 = "";
/* 394 */     while (iterator.hasNext()) {
/* 395 */       String str3 = iterator.next();
/* 396 */       String str4 = paramMap.get(str3);
/* 397 */       if (str3.equals("timeSag")) {
/* 398 */         if (Util.getIntValue(str4, 0) > 0 && Util.getIntValue(str4, 0) < 6) {
/* 399 */           str1 = TimeUtil.getDateByOption("" + str3, "0");
/* 400 */           str2 = TimeUtil.getDateByOption("" + str3, "1");
/*     */         } else {
/* 402 */           str1 = paramMap.get("begindate");
/* 403 */           str2 = paramMap.get("enddate");
/*     */         } 
/* 405 */         paramMap1.put("beginEndDate", str1);
/* 406 */         paramMap1.put("endStartDate", str2); continue;
/* 407 */       }  if (str3.equalsIgnoreCase("begindate") || str3.equalsIgnoreCase("enddate"))
/*     */         continue; 
/* 409 */       if (str3.equalsIgnoreCase("planname")) {
/* 410 */         paramMap1.put("title", str4); continue;
/* 411 */       }  if (str3.equalsIgnoreCase("urgentlevel")) {
/* 412 */         paramMap1.put("URGENTLEVEL", str4); continue;
/* 413 */       }  if (str3.equalsIgnoreCase("planstatus")) {
/* 414 */         paramMap1.put("STATUS", str4); continue;
/* 415 */       }  if (str3.equalsIgnoreCase("plantype")) {
/* 416 */         paramMap1.put("TYPE_N", str4); continue;
/* 417 */       }  if (str3.equalsIgnoreCase("createrid")) {
/* 418 */         paramMap1.put("CREATERID", str4); continue;
/* 419 */       }  if (str3.equalsIgnoreCase("receiveID")) {
/* 420 */         paramMap1.put("RESOURCEID", str4); continue;
/* 421 */       }  if (str3.equalsIgnoreCase("min") || str3.equalsIgnoreCase("max"))
/*     */         continue; 
/* 423 */       if (str3.equalsIgnoreCase("__random__"))
/*     */         continue; 
/* 425 */       if (str3.equalsIgnoreCase("secretLevel")) {
/* 426 */         paramMap1.put("secretLevel", str4);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 441 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 443 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 444 */     arrayList1.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/* 445 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(154, this.user.getLanguage())));
/* 446 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15533, this.user.getLanguage())));
/* 447 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(2087, this.user.getLanguage())));
/*     */     
/* 449 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 450 */     arrayList2.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/* 451 */     arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(16658, this.user.getLanguage())));
/* 452 */     arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(555, this.user.getLanguage())));
/* 453 */     arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(251, this.user.getLanguage())));
/*     */     
/* 455 */     ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 456 */     arrayList3.add(new SearchConditionOption("", ""));
/* 457 */     RecordSet recordSet = new RecordSet();
/* 458 */     recordSet.executeSql("SELECT * FROM WorkPlanType ORDER BY displayOrder ASC");
/* 459 */     while (recordSet.next()) {
/* 460 */       arrayList3.add(new SearchConditionOption(recordSet.getString("workPlanTypeID"), recordSet.getString("workPlanTypeName")));
/*     */     }
/*     */     
/* 463 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 464 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 465 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 229, "planname", true));
/* 466 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 15534, "urgentlevel", arrayList1));
/* 467 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 882, "createrid", "17"));
/* 468 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 602, "planstatus", arrayList2));
/* 469 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 896, "receiveID", "17"));
/* 470 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 16094, "plantype", arrayList3));
/* 471 */     String[] arrayOfString = { "timeSag", "begindate", "enddate" };
/* 472 */     arrayList.add(conditionFactory.createCondition(ConditionType.DATE, 740, arrayOfString));
/* 473 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 474 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 485 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 486 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 487 */     if ("".equals(str)) return (Map)hashMap; 
/* 488 */     RecordSet recordSet = new RecordSet();
/* 489 */     recordSet.executeSql("select * from workplan where id in (" + str + ")");
/* 490 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 491 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 492 */     while (recordSet.next()) {
/* 493 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 494 */       hashMap1.put("id", recordSet.getString("id"));
/* 495 */       hashMap1.put("name", recordSet.getString("name"));
/* 496 */       hashMap1.put("createrid", resourceComInfo.getLastname(recordSet.getString("createrid")));
/* 497 */       hashMap1.put("begindate", recordSet.getString("begindate"));
/* 498 */       hashMap1.put("enddate", recordSet.getString("enddate"));
/* 499 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 502 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 503 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 504 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(229, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 505 */     arrayList1.add(new ListHeadBean("createrid", SystemEnv.getHtmlLabelName(882, this.user.getLanguage())));
/* 506 */     arrayList1.add(new ListHeadBean("begindate", SystemEnv.getHtmlLabelName(740, this.user.getLanguage())));
/* 507 */     arrayList1.add(new ListHeadBean("enddate", SystemEnv.getHtmlLabelName(741, this.user.getLanguage())));
/*     */     
/* 509 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 510 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 511 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 512 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkPlanBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */