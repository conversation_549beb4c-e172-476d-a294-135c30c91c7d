/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BudgetPeriodBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  37 */     if (this.user == null) {
/*  38 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  39 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  42 */     String str = Util.null2String(paramMap.get("qryType"));
/*     */     
/*  44 */     if ("1".equals(str)) {
/*  45 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  46 */       List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/*  47 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */     } else {
/*  49 */       List<Map<String, String>> list = getTableList(paramMap);
/*  50 */       ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  51 */       arrayList.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  52 */       arrayList.add(new ListHeadBean("periodName", SystemEnv.getHtmlLabelName(195, this.user.getLanguage())));
/*  53 */       arrayList.add(new ListHeadBean("codeName", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage())));
/*     */       
/*  55 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*  56 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*  57 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/*     */     
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  71 */     if (this.user == null) {
/*  72 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  73 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  76 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  77 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  79 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name"));
/*  80 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "codeName"));
/*     */     
/*  82 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  84 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) throws Exception {
/*  94 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*  95 */     RecordSet recordSet1 = new RecordSet();
/*  96 */     RecordSet recordSet2 = new RecordSet();
/*  97 */     String str1 = Util.null2String(paramMap.get("id"));
/*  98 */     String str2 = Util.null2String(paramMap.get("selfId"));
/*  99 */     String str3 = Util.null2String(paramMap.get("tableName"));
/*     */     
/* 101 */     String str4 = "select a.id, a.periodName, a.codeName, a.supId from " + str3 + " a  where a.supId = ? and a.id != ? ORDER BY a.codeName, a.periodName, a.id ";
/*     */     
/* 103 */     String str5 = "/images/treeimages/home16_wev8.gif";
/* 104 */     recordSet1.executeQuery(str4, new Object[] { str1, str2 });
/* 105 */     while (recordSet1.next()) {
/* 106 */       String str6 = recordSet1.getString("id");
/* 107 */       String str7 = recordSet1.getString("periodname");
/*     */       
/* 109 */       boolean bool = true;
/* 110 */       String str8 = "select count(*) cnt from " + str3 + " a where a.supId = ? and a.id != ?";
/* 111 */       recordSet2.executeQuery(str8, new Object[] { str6, str2 });
/* 112 */       if (recordSet2.next() && recordSet2.getInt("cnt") > 0) {
/* 113 */         bool = true;
/*     */       } else {
/* 115 */         bool = false;
/*     */       } 
/*     */       
/* 118 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 119 */       browserTreeNode.setId(str6);
/* 120 */       browserTreeNode.setName(str7);
/* 121 */       browserTreeNode.setIsParent(bool);
/* 122 */       browserTreeNode.setIcon(str5);
/* 123 */       browserTreeNode.setCanClick(true);
/* 124 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 126 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 138 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 139 */     LinkedList<HashMap<Object, Object>> linkedList = new LinkedList();
/* 140 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 141 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("tableName"));
/* 142 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("selfId"));
/* 143 */     LinkedList<String> linkedList1 = new LinkedList();
/* 144 */     String str4 = "select a.id, a.periodName, a.codeName, a.supId from " + str2 + " a  where a.periodName like ? ORDER BY a.codeName, a.periodName, a.id ";
/*     */     
/* 146 */     RecordSet recordSet = new RecordSet();
/* 147 */     recordSet.executeQuery(str4, new Object[] { "%" + str1 + "%" });
/* 148 */     if (!"".equals(str3)) {
/* 149 */       linkedList1.add(str3);
/* 150 */       getAllChildsSql(linkedList1, str3, str2);
/*     */     } 
/* 152 */     while (recordSet.next()) {
/* 153 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 154 */       String str5 = recordSet.getString("id");
/* 155 */       String str6 = recordSet.getString("periodName");
/* 156 */       if (linkedList1.size() > 0 && linkedList1.contains(str5)) {
/*     */         continue;
/*     */       }
/* 159 */       hashMap1.put("id", str5);
/* 160 */       hashMap1.put("name", str6);
/* 161 */       linkedList.add(hashMap1);
/*     */     } 
/* 163 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, linkedList);
/*     */     
/* 165 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, String>> getTableList(Map<String, Object> paramMap) throws Exception {
/* 175 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 176 */     String str1 = Util.null2String(paramMap.get("name"));
/* 177 */     String str2 = Util.null2String(paramMap.get("tableName"));
/* 178 */     String str3 = Util.null2String(paramMap.get("codeName"));
/* 179 */     String str4 = Util.null2String(paramMap.get("selfId"));
/* 180 */     LinkedList<String> linkedList = new LinkedList();
/* 181 */     if (!"".equals(str4)) {
/* 182 */       linkedList.add(str4);
/* 183 */       getAllChildsSql(linkedList, str4, str2);
/*     */     } 
/*     */     
/* 186 */     String str5 = " select a.id,a.periodName,a.codeName from " + str2 + " a where a.periodName like ? and a.codeName like ? order by a.periodName,a.codeName";
/*     */     
/* 188 */     RecordSet recordSet = new RecordSet();
/* 189 */     recordSet.executeQuery(str5, new Object[] { "%" + str1 + "%", "%" + str3 + "%" });
/* 190 */     while (recordSet.next()) {
/* 191 */       String str6 = recordSet.getString("id");
/* 192 */       String str7 = recordSet.getString("periodName");
/* 193 */       String str8 = recordSet.getString("codeName");
/* 194 */       if (linkedList.size() > 0 && linkedList.contains(str6)) {
/*     */         continue;
/*     */       }
/* 197 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 198 */       hashMap.put("id", str6);
/* 199 */       hashMap.put("codeName", str8);
/* 200 */       hashMap.put("name", str7);
/* 201 */       hashMap.put("periodName", str7);
/* 202 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 205 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<String> getAllChildsSql(List<String> paramList, String paramString1, String paramString2) throws Exception {
/* 217 */     RecordSet recordSet = new RecordSet();
/* 218 */     String str = "with cte as (select * from " + paramString2 + " where supId = ? union all select  a.* from " + paramString2 + " as a,cte as b where a.supId = b.id )select * from cte ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 226 */     if ("oracle".equals(recordSet.getDBType())) {
/* 227 */       str = "select * from " + paramString2 + " start with supId = ? connect by prior id = supId";
/* 228 */     } else if ("mysql".equals(recordSet.getDBType())) {
/* 229 */       str = " select DISTINCT t.id,t.periodName,t.supId from ( select @id idlist, @lv:=@lv+1 lv, (select @id:=group_concat(id separator ',') from " + paramString2 + " where find_in_set(supId,@id)) sub  from " + paramString2 + " ,(select @id:= ? ,@lv:=0) vars  ) tl, " + paramString2 + " t  where find_in_set(t.id,tl.idlist)  order by lv asc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 236 */     else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 237 */       str = "with RECURSIVE  cte as (select * from " + paramString2 + " where supId = ? union all select  a.* from " + paramString2 + " as a,cte as b where a.supId = b.id )select * from cte ";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 245 */     recordSet.executeQuery(str, new Object[] { paramString1 });
/*     */     
/* 247 */     while (recordSet.next()) {
/* 248 */       String str1 = recordSet.getString("id");
/* 249 */       paramList.add(str1);
/*     */     } 
/* 251 */     return paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BudgetPeriodBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */