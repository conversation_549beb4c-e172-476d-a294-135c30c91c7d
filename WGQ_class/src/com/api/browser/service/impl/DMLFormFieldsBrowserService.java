/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.integration.util.PageUidFactory;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DMLFormFieldsBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  37 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  39 */     String str1 = Util.null2String(paramMap.get("formid"));
/*  40 */     String str2 = Util.null2String(paramMap.get("isbill"));
/*  41 */     String str3 = Util.null2String(paramMap.get("fieldname")).trim();
/*  42 */     String str4 = Util.null2String(paramMap.get("fieldtype"));
/*  43 */     String str5 = Util.null2String(paramMap.get("issearch"));
/*     */     
/*  45 */     String str6 = Util.null2String(paramMap.get("showdetail"));
/*  46 */     String str7 = Util.null2String(paramMap.get("tableid"));
/*     */ 
/*     */     
/*  49 */     String str8 = PageUidFactory.getPageUid("intergration_browser_dmlFormFields");
/*  50 */     String str9 = str8;
/*  51 */     String str10 = PageIdConst.getPageSize(str9, this.user.getUID());
/*     */ 
/*     */     
/*  54 */     str5 = "1";
/*     */     
/*  56 */     String str11 = "dmlFormFieldsBrowser";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  66 */     String str12 = "<table pageUid='" + str8 + "' pageId='" + str9 + "'  datasource=\"weaver.workflow.action.ActionDataSource.getDMLFormField\" sourceparams=\"formid:" + str1 + "+isbill:" + str2 + "+fieldname:" + str3 + "+fieldtype:" + str4 + "+issearch:" + str5 + "+showdetail:" + str6 + "+tableid:" + str7 + "\" instanceid=\"Table\" pagesize=\"" + PageIdConst.getPageSize(str11, this.user.getUID()) + "\" tabletype=\"none\"> <checkboxpopedom  id=\"none\" /><sql backfields=\"*\"  sqlform=\"tmptable\" sqlorderby=\"id\"  sqlprimarykey=\"id\" sqlsortway=\"asc\"  sqldistinct=\"true\" /><head><col width=\"0%\" hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHidden\" text=\"\" column=\"bean\"/><col width=\"50%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()) + "\" column=\"jfieldlabel\" isInputCol='true' /><col width=\"50%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(685, this.user.getLanguage()) + "\" column=\"jfieldname\" isPrimaryKey='true'  /><col width=\"50%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(686, this.user.getLanguage()) + "\" column=\"jfielddbtype\"/><col width=\"50%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(83682, this.user.getLanguage()) + "\" column=\"fielddesc\"/></head></table>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  71 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  72 */     String str13 = str8 + "_" + Util.getEncrypt(Util.getRandom());
/*  73 */     Util_TableMap.setVal(str13, str12);
/*  74 */     hashMap2.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  75 */     hashMap2.put(BrowserConstant.BROWSER_RESULT_DATA, str13);
/*     */     
/*  77 */     hashMap1.putAll(hashMap2);
/*     */     
/*  79 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  84 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  85 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  86 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  87 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  88 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 606, "fieldname", true));
/*  89 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 63, "fieldtype");
/*  90 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  91 */     arrayList1.add(new SearchConditionOption("", ""));
/*  92 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(83678, this.user.getLanguage())));
/*  93 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(83679, this.user.getLanguage())));
/*  94 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(83680, this.user.getLanguage())));
/*  95 */     searchConditionItem.setOptions(arrayList1);
/*  96 */     arrayList.add(searchConditionItem);
/*  97 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 104 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 105 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, null);
/* 106 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 107 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 108 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DMLFormFieldsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */