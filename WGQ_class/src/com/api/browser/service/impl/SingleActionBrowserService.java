/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class SingleActionBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  22 */     return getSingleActionBrowser(paramMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getSingleActionBrowser(Map<String, Object> paramMap) {
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>(5);
/*  34 */     int i = Util.getIntValue(Util.null2String(paramMap.get("formid")));
/*     */     
/*  36 */     String str1 = Util.null2String(paramMap.get("actionname"));
/*  37 */     String str2 = Util.null2String(paramMap.get("actiontypelimted"));
/*  38 */     int j = Util.getIntValue(Util.null2String(paramMap.get("actiontype")));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  48 */     String str3 = "<table  datasource=\"weaver.workflow.action.ActionDataSource.getActionTable\" sourceparams=\"actiontype:" + j + "+actionname:" + str1 + "+formid:" + i + "\" instanceid=\"Table\" pagesize=\"" + PageIdConst.getPageSize("Wf:workflow_singleactionbrowser", this.user.getUID()) + "\" tabletype=\"none\"> <checkboxpopedom  id=\"none\" /><sql backfields=\"*\"  sqlform=\"tmptable\" sqlorderby=\"id\"  sqlprimarykey=\"uuid\" sqlsortway=\"asc\"  sqldistinct=\"true\" /><head><col width=\"0%\" isInputCol=\"false\" isPrimarykey=\"false\"  hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"id\"/><col width=\"50%\" isInputCol=\"true\"  hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(386719, this.user.getLanguage()) + "\" column=\"actionname\"/><col width=\"50%\" isInputCol=\"false\"  hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(383093, this.user.getLanguage()) + "\" column=\"fromtypename\"/><col width=\"0%\" isInputCol=\"false\"  hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"fromtype\"/></head></table>";
/*     */ 
/*     */ 
/*     */     
/*  52 */     String str4 = Util.getEncrypt(Util.getRandom());
/*  53 */     Util_TableMap.setVal(str4, str3);
/*  54 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  55 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str4);
/*  56 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  70 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  71 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  72 */     String str = Util.null2String(paramMap.get("noNeedTypeSearch"));
/*     */     
/*  74 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "83001,82755", "actionname", true));
/*  75 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  76 */     arrayList1.add(new SearchConditionOption("-1", ""));
/*  77 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(82986, this.user.getLanguage())));
/*  78 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(82987, this.user.getLanguage())));
/*  79 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(82988, this.user.getLanguage())));
/*     */     
/*  81 */     arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(381878, this.user.getLanguage())));
/*     */ 
/*     */ 
/*     */     
/*  85 */     if (!"1".equals(str)) {
/*  86 */       arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 383093, "actiontype", arrayList1));
/*     */     }
/*  88 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getModeFullname(String paramString1, String paramString2) {
/*  95 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*  96 */     String str1 = arrayOfString[0];
/*  97 */     String str2 = arrayOfString[1];
/*     */ 
/*     */     
/* 100 */     return paramString1 + "  (" + ("1".equals(str1) ? SystemEnv.getHtmlLabelName(678, Util.getIntValue(str2)) : SystemEnv.getHtmlLabelName(1477, Util.getIntValue(str2))) + ")";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SingleActionBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */