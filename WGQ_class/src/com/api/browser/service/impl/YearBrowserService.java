/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class YearBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 28 */     String str1 = " yearId,yearName ";
/* 29 */     String str2 = " from Workflow_FieldYear a ";
/*    */     
/* 31 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 32 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(15933, this.user.getLanguage()), "yearName", "yearName")).setIsInputCol(BoolAttr.TRUE).setIsPrimarykey(BoolAttr.TRUE));
/* 33 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, "", "yearId", "yearId", arrayList);
/* 34 */     splitTableBean.setSqlsortway("DESC");
/* 35 */     splitTableBean.setSqlorderby("yearName");
/* 36 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 37 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/YearBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */