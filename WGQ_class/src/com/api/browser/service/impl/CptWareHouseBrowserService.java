/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.proj.util.SQLUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptWareHouseBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  47 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  48 */     RecordSet recordSet = new RecordSet();
/*  49 */     String str1 = Util.null2String(paramMap.get("wfid"));
/*  50 */     String str2 = Util.null2String(paramMap.get("name"));
/*  51 */     String str3 = Util.null2String(paramMap.get("manager"));
/*  52 */     String str4 = Util.null2String(paramMap.get("subcompanyid"));
/*  53 */     String str5 = Util.null2String(paramMap.get("isfilter"));
/*  54 */     CptWfUtil cptWfUtil = new CptWfUtil();
/*  55 */     String str6 = "";
/*  56 */     if (!"".equals(str1)) {
/*  57 */       str6 = cptWfUtil.getWftype(str1);
/*     */     }
/*  59 */     String str7 = " where 1=1 ";
/*  60 */     if (!str2.equals("")) {
/*  61 */       str7 = str7 + " and name like '%";
/*  62 */       str7 = str7 + Util.fromScreen2(str2, this.user.getLanguage());
/*  63 */       str7 = str7 + "%'";
/*     */     } 
/*  65 */     String str8 = recordSet.getDBType();
/*  66 */     if (!str3.equals("")) {
/*  67 */       if ("oracle".equalsIgnoreCase(str8)) {
/*  68 */         str7 = str7 + SQLUtil.filteSql(str8, " and ','+manager+',' like '%," + str3 + ",%'  ");
/*  69 */       } else if ("mysql".equalsIgnoreCase(str8)) {
/*  70 */         str7 = str7 + " and concat(',',convert(manager , char(2000)),',') like '%," + str3 + ",%'  ";
/*     */       }
/*  72 */       else if ("postgresql".equalsIgnoreCase(str8)) {
/*  73 */         str7 = str7 + SQLUtil.filteSql(str8, " and ','+manager+',' like '%," + str3 + ",%'  ");
/*     */       } else {
/*     */         
/*  76 */         str7 = str7 + " and ','+convert(varchar(2000),manager)+',' like '%," + str3 + ",%'  ";
/*     */       } 
/*     */     }
/*  79 */     if (!str4.equals("")) {
/*  80 */       if ("oracle".equalsIgnoreCase(str8)) {
/*  81 */         str7 = str7 + SQLUtil.filteSql(str8, " and ','+subcompanyid+',' like '%," + str4 + ",%'  ");
/*  82 */       } else if ("mysql".equalsIgnoreCase(str8)) {
/*  83 */         str7 = str7 + " and concat(',',convert(subcompanyid , char(2000)),',') like '%," + str4 + ",%'  ";
/*     */       }
/*  85 */       else if ("postgresql".equalsIgnoreCase(str8)) {
/*  86 */         str7 = str7 + SQLUtil.filteSql(str8, " and ','+subcompanyid+',' like '%," + str4 + ",%'  ");
/*     */       } else {
/*     */         
/*  89 */         str7 = str7 + " and ','+convert(varchar(2000),subcompanyid)+',' like '%," + str4 + ",%'  ";
/*     */       } 
/*     */     }
/*  92 */     if ((str5.equalsIgnoreCase("1") || str6.equalsIgnoreCase("move") || str6.equalsIgnoreCase("change")) && !this.user.getLoginid().equalsIgnoreCase("sysadmin")) {
/*  93 */       if (str8.equals("oracle")) {
/*  94 */         str7 = str7 + " and ','||manager||',' like '%," + this.user.getUID() + ",%'";
/*  95 */       } else if (str8.equals("mysql")) {
/*  96 */         str7 = str7 + " and concat(',',manager,',') like '%," + this.user.getUID() + ",%'";
/*     */       }
/*  98 */       else if (str8.equals("postgresql")) {
/*  99 */         str7 = str7 + " and ','||manager||',' like '%," + this.user.getUID() + ",%'";
/*     */       } else {
/*     */         
/* 102 */         str7 = str7 + " and ','+manager+',' like '%," + this.user.getUID() + ",%'";
/*     */       } 
/*     */     }
/* 105 */     String str9 = " id ";
/* 106 */     String str10 = " id,name,manager,subcompanyid ";
/* 107 */     String str11 = " CptCapitalWareHouse ";
/*     */     
/* 109 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 110 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 111 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 112 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(1507, this.user.getLanguage()), "manager", "manager", "com.api.cpt.mobile.util.CapitalTransUtil.getBrowserShowName", "17+ "));
/* 113 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(19799, this.user.getLanguage()), "subcompanyid", "subcompanyid", "com.api.cpt.mobile.util.CapitalTransUtil.getBrowserShowName", "164+ "));
/*     */     
/* 115 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, str7, str9, "id", arrayList);
/*     */     try {
/* 117 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 118 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 119 */     } catch (Exception exception) {
/* 120 */       exception.printStackTrace();
/*     */     } 
/* 122 */     splitTableBean.setSqlsortway("ASC");
/* 123 */     splitTableBean.setSqlisdistinct("true");
/* 124 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 125 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 130 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 131 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 132 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 133 */     ConditionType conditionType = ConditionType.INPUT;
/* 134 */     arrayList.add(conditionFactory.createCondition(conditionType, 195, "name", true));
/* 135 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 1507, "manager", "17"));
/* 136 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 19799, "subcompanyid", "194"));
/* 137 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 138 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public static String getWareHouseName(String paramString) {
/* 142 */     RecordSet recordSet = new RecordSet();
/* 143 */     String str = "";
/* 144 */     recordSet.executeQuery("select name from CptCapitalWareHouse where id=?", new Object[] { paramString });
/* 145 */     if (recordSet.next()) {
/* 146 */       str = Util.null2String(recordSet.getString("name"));
/*     */     }
/* 148 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CptWareHouseBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */