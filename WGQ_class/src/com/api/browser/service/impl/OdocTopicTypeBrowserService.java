/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class OdocTopicTypeBrowserService extends BrowserService {
/*  35 */   boolean isUseOdocManageDetach = (new ManageDetachComInfo()).isUseOdocManageDetach();
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  41 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  42 */     String str2 = Util.null2String(paramMap.get("topic_name"));
/*  43 */     String str3 = Util.null2String(paramMap.get("topic_describe"));
/*  44 */     String str4 = Util.null2String(paramMap.get("subcompanyid"));
/*  45 */     String str5 = Util.null2String(paramMap.get("fromModule"));
/*     */     
/*  47 */     String str6 = "id,topic_name,topic_describe,subCompanyId";
/*  48 */     String str7 = "odoc_topictype";
/*  49 */     if (!"".equals(str2)) {
/*  50 */       str1 = str1 + " and topic_name like '%" + str2 + "%' ";
/*     */     }
/*  52 */     if (!"".equals(str3)) {
/*  53 */       str1 = str1 + " and topic_describe like '%" + str3 + "%' ";
/*     */     }
/*  55 */     if (!"".equals(str4)) {
/*  56 */       str1 = str1 + " and subCompanyId in (" + str4 + ") ";
/*     */     }
/*  58 */     if (!StringUtils.isBlank(getCanUseOodcTopic(this.user)) && str5.equals("workflow")) {
/*  59 */       str1 = str1 + str1 + "and id in ( " + getCanUseOodcTopic(this.user) + ")";
/*  60 */     } else if (StringUtils.isBlank(getCanUseOodcTopic(this.user)) && this.user.getUID() != 1 && str5.equals("workflow")) {
/*  61 */       str1 = str1 + str1 + "and 1=2";
/*     */     } 
/*  63 */     str1 = str1 + "and (ISCANCEL<>1 or ISCANCEL is null)";
/*  64 */     str1 = SqlUtils.replaceFirstAnd(str1);
/*  65 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  66 */     arrayList.add((new SplitTableColBean("hide", "id")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/*  67 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "topic_name", "topic_name")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  68 */     arrayList.add((new SplitTableColBean("70%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "topic_describe", "topic_describe")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  69 */     if (this.isUseOdocManageDetach) {
/*  70 */       arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subCompanyId", "subCompanyId", "com.engine.odoc.util.OdocStandardFrontMethodUtil.GetSubcompanyName")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */     }
/*  72 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str1, "showorder", "id", "asc", arrayList);
/*     */     
/*  74 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/*  76 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/*  77 */     } catch (Exception exception) {
/*  78 */       exception.printStackTrace();
/*     */     } 
/*  80 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 126 */     if ("".equals(str)) return (Map)hashMap; 
/* 127 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 128 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 130 */     recordSet.executeQuery("select id,topic_name,topic_describe from odoc_topictype where id in (" + str + ")", new Object[0]);
/* 131 */     while (recordSet.next()) {
/* 132 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 133 */       hashMap1.put("id", recordSet.getString("id"));
/* 134 */       hashMap1.put("topic_name", recordSet.getString("topic_name"));
/* 135 */       hashMap1.put("topic_describe", recordSet.getString("topic_describe"));
/* 136 */       arrayList.add(hashMap1);
/*     */     } 
/* 138 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 139 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 140 */     arrayList1.add(new ListHeadBean("topic_name", "", 1, BoolAttr.TRUE));
/* 141 */     arrayList1.add(new ListHeadBean("topic_describe", "", 1, BoolAttr.TRUE));
/*     */     
/* 143 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 144 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 145 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 146 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 151 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 152 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 153 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 154 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "topic_name", true));
/* 155 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "topic_describe"));
/* 156 */     if (this.isUseOdocManageDetach) {
/* 157 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, "17868", "subcompanyid", "164"));
/*     */     }
/* 159 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 160 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 167 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 168 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.topic_name");
/* 169 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row3.topic_describe");
/* 170 */     if (this.isUseOdocManageDetach) {
/* 171 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.subCompanyId");
/*     */     }
/* 173 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCanUseOodcTopic(User paramUser) {
/* 178 */     if (paramUser.isAdmin())
/* 179 */       return ""; 
/* 180 */     Integer integer1 = Integer.valueOf(paramUser.getUserSubCompany1());
/* 181 */     Integer integer2 = Integer.valueOf(paramUser.getUID());
/* 182 */     List<Map> list = getRole(integer2);
/* 183 */     Integer integer3 = Integer.valueOf(paramUser.getUserDepartment());
/* 184 */     Integer integer4 = Integer.valueOf(Util.getIntValue(paramUser.getSeclevel(), -1));
/* 185 */     String str1 = paramUser.getJoblevel();
/* 186 */     String str2 = paramUser.getJobtitle();
/*     */     
/* 188 */     RecordSet recordSet1 = new RecordSet();
/* 189 */     RecordSet recordSet2 = new RecordSet();
/* 190 */     recordSet1.executeQuery("select * from OdocTopicShare", new Object[0]);
/* 191 */     recordSet2.executeQuery("select id from odoc_topictype", new Object[0]);
/* 192 */     ArrayList<Integer> arrayList = new ArrayList();
/*     */     
/* 194 */     int i = 0;
/*     */     
/* 196 */     Boolean bool = Boolean.valueOf(false);
/* 197 */     while (recordSet2.next()) {
/* 198 */       int j = recordSet2.getInt(1);
/* 199 */       bool = Boolean.valueOf(false);
/* 200 */       boolean bool1 = false;
/* 201 */       while (recordSet1.next()) {
/* 202 */         int k = recordSet1.getInt(2);
/* 203 */         if (k == j) {
/* 204 */           bool1 = true;
/* 205 */           i = recordSet1.getInt(3);
/* 206 */           Integer integer5 = Integer.valueOf(recordSet1.getInt(5));
/* 207 */           Integer integer6 = Integer.valueOf(recordSet1.getInt(6));
/* 208 */           String str3 = recordSet1.getString(4);
/* 209 */           String str4 = recordSet1.getString(8);
/* 210 */           String str5 = recordSet1.getString(9);
/* 211 */           String str6 = recordSet1.getString(7);
/* 212 */           String str7 = recordSet1.getString(10);
/* 213 */           if (i == 1) {
/* 214 */             if (str3.equals(integer2.toString())) {
/* 215 */               bool = Boolean.valueOf(true); break;
/*     */             } 
/*     */             continue;
/*     */           } 
/* 219 */           if (i == 2) {
/*     */             
/* 221 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/*     */               
/* 223 */               if (str3.equals(integer1.toString())) {
/* 224 */                 bool = Boolean.valueOf(true);
/*     */                 
/*     */                 break;
/*     */               } 
/* 228 */               if ("1".equals(str7)) {
/* 229 */                 String str = SubCompanyComInfo.getAllChildSubcompanyId(str3, "");
/* 230 */                 String[] arrayOfString = str.split(",");
/* 231 */                 for (byte b = 0; b < arrayOfString.length; b++) {
/* 232 */                   if (arrayOfString[b].equals(integer1.toString())) {
/* 233 */                     bool = Boolean.valueOf(true); break;
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */             }  continue;
/*     */           } 
/* 239 */           if (i == 3) {
/* 240 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 241 */               if (str3.equals(integer3.toString())) {
/* 242 */                 bool = Boolean.valueOf(true);
/*     */                 
/*     */                 break;
/*     */               } 
/* 246 */               if ("1".equals(str7)) {
/*     */                 
/* 248 */                 ArrayList arrayList1 = new ArrayList();
/* 249 */                 DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 250 */                 departmentComInfo.getAllChildDeptByDepId(arrayList1, str3);
/* 251 */                 if (arrayList1.contains(integer3.toString())) {
/* 252 */                   bool = Boolean.valueOf(true);
/*     */                 }
/*     */               } 
/*     */             } 
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 260 */           if (i == 4) {
/* 261 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 262 */               for (Map map : list) {
/* 263 */                 if (str3.equals(map.get("roleid").toString())) {
/* 264 */                   if (str6.equals("0")) {
/* 265 */                     if ("2".equals(map.get("rolelevel").toString())) {
/* 266 */                       bool = Boolean.valueOf(true); break;
/*     */                     } 
/*     */                     continue;
/*     */                   } 
/* 270 */                   if (str6.equals("2")) {
/* 271 */                     if ("0".equals(map.get("rolelevel").toString())) {
/* 272 */                       bool = Boolean.valueOf(true);
/*     */                       break;
/*     */                     } 
/*     */                     continue;
/*     */                   } 
/* 277 */                   if (str6.equals(map.get("rolelevel").toString())) {
/* 278 */                     bool = Boolean.valueOf(true);
/*     */                   }
/*     */                 } 
/*     */               } 
/*     */             }
/*     */ 
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 288 */           if (i == 7) {
/*     */             
/* 290 */             if (str2.equals(str3)) {
/* 291 */               if ("1".equals(str6)) {
/* 292 */                 if (str4.equals(integer1.toString())) {
/* 293 */                   bool = Boolean.valueOf(true); break;
/*     */                 } 
/*     */                 continue;
/*     */               } 
/* 297 */               if ("2".equals(str6)) {
/* 298 */                 if (str5.equals(integer3.toString())) {
/* 299 */                   bool = Boolean.valueOf(true);
/*     */                   
/*     */                   break;
/*     */                 } 
/*     */                 continue;
/*     */               } 
/* 305 */               bool = Boolean.valueOf(true);
/*     */               
/*     */               break;
/*     */             } 
/*     */             continue;
/*     */           } 
/* 311 */           if (i == 5)
/*     */           {
/* 313 */             if (integer4.intValue() >= integer5.intValue() && integer4.intValue() <= integer6.intValue()) {
/* 314 */               bool = Boolean.valueOf(true);
/*     */ 
/*     */ 
/*     */               
/*     */               break;
/*     */             } 
/*     */           }
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 325 */       if ((bool.booleanValue() || !bool1) && 
/* 326 */         !arrayList.contains(Integer.valueOf(j))) {
/* 327 */         arrayList.add(Integer.valueOf(j));
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 332 */       recordSet1.beforFirst();
/*     */     } 
/*     */     
/* 335 */     return StringUtils.join(arrayList.toArray(), ",");
/*     */   }
/*     */   
/*     */   private List<Map> getRole(Integer paramInteger) {
/* 339 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 340 */     if (paramInteger != null) {
/* 341 */       String str = "select roleid,rolelevel from hrmrolemembers  where resourceid=?";
/* 342 */       RecordSet recordSet = new RecordSet();
/* 343 */       recordSet.executeQuery(str, new Object[] { paramInteger });
/* 344 */       HashMap<Object, Object> hashMap = null;
/* 345 */       while (recordSet.next()) {
/* 346 */         hashMap = new HashMap<>();
/* 347 */         hashMap.put("roleid", recordSet.getString(1));
/* 348 */         hashMap.put("rolelevel", recordSet.getString(2));
/* 349 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 354 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OdocTopicTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */