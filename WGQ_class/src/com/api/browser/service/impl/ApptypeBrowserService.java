/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import com.engine.systeminfo.util.AppManageTransmethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ApptypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  34 */     String str1 = "t1.id , t1.apptype , t1.module , t1.description, t2.id as tid, t2.name,t1.countsupport,t1.config,t1.viewtype";
/*  35 */     String str2 = " ecology_biz_app_type t1 left join ecology_biz_app_module t2 on t1.module=t2.id";
/*  36 */     String str3 = " 1=1 and t1.id not in (25) ";
/*  37 */     String str4 = "t1.id";
/*  38 */     String str5 = "t1.id";
/*  39 */     String str6 = "asc";
/*  40 */     String str7 = "" + this.user.getLanguage();
/*     */     
/*  42 */     String str8 = Util.null2String(paramMap.get("apptype_browser_condition"));
/*  43 */     String str9 = Util.null2String(paramMap.get("module_browser_condition"));
/*  44 */     if (StringUtils.isNotBlank(str8)) {
/*  45 */       str3 = str3 + " and t1.description like '%" + str8 + "%'";
/*     */     }
/*  47 */     if (StringUtils.isNotBlank(str9)) {
/*  48 */       str3 = str3 + " and t2.id='" + str9 + "'";
/*     */     }
/*  50 */     str3 = str3 + " and (" + Util.getSubINClause(StringUtils.join(AppManageTransmethod.checkUnstandard(), ","), "t1.id", "in") + ")";
/*  51 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  52 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  53 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(388667, this.user.getLanguage()), "apptype", "", "com.engine.systeminfo.util.AppManageTransmethod.getApptype", str7)).setIsInputCol(BoolAttr.TRUE));
/*  54 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()), "name", "", "com.engine.systeminfo.util.AppManageTransmethod.getModule", str7));
/*  55 */     arrayList.add(new SplitTableColBean("true", "countsupport"));
/*  56 */     arrayList.add(new SplitTableColBean("true", "config"));
/*  57 */     arrayList.add(new SplitTableColBean("true", "viewtype"));
/*  58 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, str4, str5, str6, arrayList);
/*  59 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  66 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  67 */     RecordSet recordSet = new RecordSet();
/*  68 */     String str2 = "select id,apptype,countsupport,config,viewtype from ECOLOGY_BIZ_APP_TYPE t1 where description like ? ";
/*  69 */     str2 = str2 + " and (" + Util.getSubINClause(StringUtils.join(AppManageTransmethod.checkUnstandard(), ","), "t1.id", "in") + ")";
/*  70 */     recordSet.executeQuery(str2, new Object[] { "%" + str1 + "%" });
/*  71 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  72 */     while (recordSet.next()) {
/*  73 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  74 */       hashMap1.put("id", recordSet.getString("id"));
/*  75 */       hashMap1.put("name", SystemEnv.getHtmlLabelName(recordSet.getInt("apptype"), this.user.getLanguage()));
/*  76 */       hashMap1.put("countsupport", recordSet.getString("countsupport"));
/*  77 */       hashMap1.put("config", recordSet.getString("config"));
/*  78 */       hashMap1.put("viewtype", recordSet.getString("viewtype"));
/*  79 */       arrayList.add(hashMap1);
/*     */     } 
/*  81 */     hashMap.put("datas", arrayList);
/*  82 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  88 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/*  89 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  90 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/*  91 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/*  94 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 388667, "apptype_browser_condition", true);
/*  95 */     arrayList1.add(searchConditionItem1);
/*     */ 
/*     */     
/*  98 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 33522, "module_browser_condition", "appModule");
/*  99 */     arrayList1.add(searchConditionItem2);
/*     */     
/* 101 */     arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(15774, Util.getIntValue(this.user.getLanguage())), true, arrayList1));
/* 102 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 103 */     return weaResultMsg.getResultMapAll();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ApptypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */