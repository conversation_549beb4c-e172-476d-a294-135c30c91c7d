/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ public class WorkflowNodeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  33 */     String str1 = Util.null2String(paramMap.get("workflowid"));
/*  34 */     String str2 = Util.null2String(paramMap.get("nodetype"));
/*  35 */     String str3 = Util.null2String(paramMap.get("nodeattribute"));
/*  36 */     String str4 = Util.null2String(paramMap.get("nodename"));
/*  37 */     String str5 = Util.null2String(paramMap.get("isFree"));
/*  38 */     String str6 = Util.null2String(paramMap.get("notNodes"));
/*  39 */     String str7 = Util.null2String(paramMap.get("nodes"));
/*  40 */     String str8 = Util.null2String(paramMap.get("noNeedActiveWfId"));
/*     */     
/*  42 */     int i = Util.getIntValue(Util.null2String(paramMap.get("showtype")), -1);
/*     */     
/*  44 */     String str9 = Util.null2String(paramMap.get("selectwfid"));
/*  45 */     String str10 = Util.null2String(paramMap.get("workflowname"));
/*     */     
/*  47 */     String str11 = (i == 1) ? "com.api.browser.service.impl.WorkflowNodeBrowserService.getNodeType1" : "com.api.browser.service.impl.WorkflowNodeBrowserService.getNodeType";
/*     */ 
/*     */     
/*  50 */     String str12 = str1;
/*  51 */     if (!"1".equals(str8)) {
/*  52 */       str12 = WorkflowVersion.getActiveVersionWFID(str1);
/*     */     }
/*  54 */     String str13 = " where ";
/*     */     
/*  56 */     if (!"".equals(str9)) {
/*  57 */       str13 = str13 + "workflowid in (" + str9 + ")";
/*     */     } else {
/*  59 */       str13 = str13 + "workflowid = " + str12;
/*     */     } 
/*     */     
/*  62 */     if (!"".equals(str2)) {
/*  63 */       str13 = str13 + " and a.nodetype in(" + str2 + ") ";
/*     */     }
/*  65 */     if (!"".equals(str3)) {
/*  66 */       str13 = str13 + " and b.nodeattribute in(" + str3 + ") ";
/*     */     }
/*     */     
/*  69 */     if (!"".equals(str4)) {
/*  70 */       str13 = str13 + " and b.nodename like '%" + str4 + "%'";
/*     */     }
/*  72 */     if (!"".equals(str10)) {
/*  73 */       str13 = str13 + " and c.workflowname like '%" + str10 + "%'";
/*     */     }
/*     */     
/*  76 */     if (str5.equals("1")) {
/*  77 */       str13 = str13 + " and (b.isfreenode <> '1' or b.isfreenode is null) ";
/*     */     }
/*  79 */     if (!str6.equals("")) {
/*  80 */       str13 = str13 + " and a.nodeid not in( " + str6 + ") ";
/*     */     }
/*  82 */     if (!str7.equals("")) {
/*  83 */       str13 = str13 + " and a.nodeid in( " + str7 + ") ";
/*     */     }
/*     */     
/*  86 */     String str14 = "c.workflowname,a.nodeid,b.nodename,a.nodetype";
/*  87 */     String str15 = "workflow_base c  left join workflow_flownode a  on c.id=a.workflowid left join workflow_nodebase b on a.nodeid = b.id";
/*  88 */     String str16 = "nodeorder,nodetype,b.id";
/*  89 */     String str17 = " asc ";
/*  90 */     RecordSet recordSet = new RecordSet();
/*  91 */     if (!"".equals(str9))
/*     */     {
/*     */       
/*  94 */       str16 = "workflowid,nodeorder,nodetype,b.id";
/*     */     }
/*  96 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  97 */     arrayList.add(new SplitTableColBean("true", "nodeid"));
/*  98 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, 1)).setIsInputCol(BoolAttr.TRUE));
/*     */     
/* 100 */     if (!"".equals(str9)) {
/* 101 */       arrayList.add(new SplitTableColBean("100px", SystemEnv.getHtmlLabelName(15536, this.user.getLanguage()), "workflowname", null, "com.api.browser.service.impl.WorkflowNodeBrowserService.getWorkflowName", this.user
/* 102 */             .getLanguage() + "", 0));
/*     */     }
/* 104 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15536, this.user.getLanguage()), "nodetype", null, str11, this.user
/* 105 */           .getLanguage() + "", 0));
/*     */     
/* 107 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str14, str15, str13, str16, "a.nodeid", str17, arrayList)));
/* 108 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 113 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 114 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 115 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 116 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 117 */     String str = Util.null2String(paramMap.get("selectwfid"));
/* 118 */     if (!"".equals(str)) {
/* 119 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 518713, "workflowname", false));
/*     */     }
/* 121 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15070, "nodename", true));
/*     */     
/* 123 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 124 */     arrayList1.add(new SearchConditionOption("", ""));
/* 125 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(125, this.user.getLanguage())));
/* 126 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(142, this.user.getLanguage())));
/* 127 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(615, this.user.getLanguage())));
/* 128 */     arrayList1.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(529067, this.user.getLanguage())));
/* 129 */     arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(529068, this.user.getLanguage())));
/* 130 */     if (!"0".equals(paramMap.get("needEnd"))) {
/* 131 */       arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(251, this.user.getLanguage())));
/*     */     }
/* 133 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 15536, "nodetype", arrayList1));
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 139 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 140 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 141 */     String str2 = Util.null2String(paramMap.get("workflowid"));
/* 142 */     String str3 = Util.null2String(paramMap.get("noNeedActiveWfId"));
/* 143 */     String str4 = str2;
/* 144 */     if (!"1".equals(str3)) {
/* 145 */       str4 = WorkflowVersion.getActiveVersionWFID(str2);
/*     */     }
/* 147 */     int i = Util.getIntValue(Util.null2String(paramMap.get("nodetype")), -1);
/*     */     
/* 149 */     int j = Util.getIntValue(Util.null2String(paramMap.get("showtype")), -1);
/* 150 */     String str5 = Util.null2String(paramMap.get("nodename"));
/* 151 */     String str6 = Util.null2String(paramMap.get("selectwfid"));
/* 152 */     String str7 = Util.null2String(paramMap.get("workflowname"));
/* 153 */     if ("".equals(str1)) return (Map)hashMap; 
/* 154 */     String str8 = " select c.workflowname,a.nodeid,b.nodename,a.nodetype from workflow_base c  left join workflow_flownode a  on c.id=a.workflowid left join workflow_nodebase b on a.nodeid = b.id where ";
/* 155 */     if (!"".equals(str6)) {
/* 156 */       str8 = str8 + "workflowid in (" + str6 + ")";
/*     */     } else {
/* 158 */       str8 = str8 + "workflowid in (" + str4 + ")";
/*     */     } 
/*     */     
/* 161 */     if (i > -1) {
/* 162 */       str8 = str8 + " and a.nodetype = " + i;
/*     */     }
/*     */     
/* 165 */     if (!"".equals(str7)) {
/* 166 */       str8 = str8 + " and c.workflowname like '%" + str7 + "%'";
/*     */     }
/*     */     
/* 169 */     if (!"".equals(str5)) {
/* 170 */       str8 = str8 + " and b.nodename like '%" + str5 + "%'";
/*     */     }
/*     */ 
/*     */     
/* 174 */     String str9 = Util.getSubINClause(str1, "a.nodeid", "in");
/*     */     
/* 176 */     str8 = str8 + " and " + str9 + " ";
/*     */     
/* 178 */     RecordSet recordSet = new RecordSet();
/* 179 */     String str10 = " order by nodeorder,nodetype,b.id ";
/* 180 */     if (!"".equals(str6)) {
/* 181 */       str10 = " order by workflowid,nodeorder,nodetype,b.id ";
/*     */     }
/* 183 */     str8 = str8 + str10;
/*     */     
/* 185 */     recordSet.executeQuery(str8, new Object[0]);
/* 186 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 187 */     while (recordSet.next()) {
/* 188 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 189 */       hashMap1.put("nodeid", recordSet.getString("nodeid"));
/* 190 */       if (!"".equals(str6))
/* 191 */         hashMap1.put("workflowname", recordSet.getString("workflowname")); 
/* 192 */       hashMap1.put("nodename", recordSet.getString("nodename"));
/* 193 */       if (j == 1) {
/* 194 */         hashMap1.put("nodetype", getNodeType1(recordSet.getString("nodetype"), this.user.getLanguage() + ""));
/*     */       } else {
/* 196 */         hashMap1.put("nodetype", getNodeType(recordSet.getString("nodetype"), this.user.getLanguage() + ""));
/*     */       } 
/* 198 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 201 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 202 */     arrayList1.add((new ListHeadBean("nodeid", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 203 */     if (!"".equals(str6))
/* 204 */       arrayList1.add((new ListHeadBean("workflowname", "", 1)).setIsInputCol(BoolAttr.TRUE)); 
/* 205 */     arrayList1.add((new ListHeadBean("nodename", "", 1)).setIsInputCol(BoolAttr.TRUE));
/* 206 */     arrayList1.add(new ListHeadBean("nodetype", "", 0));
/*     */     
/* 208 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 209 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "nodeid"));
/* 210 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 211 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 216 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 217 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 218 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("workflowid"));
/* 219 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("nodetype"));
/* 220 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("nodeattribute"));
/* 221 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("isFree"));
/* 222 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("notNodes"));
/* 223 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("nodes"));
/* 224 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("noNeedActiveWfId"));
/* 225 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("isNot"));
/* 226 */     String str10 = Util.null2String(paramHttpServletRequest.getParameter("selectwfid"));
/*     */     
/* 228 */     if ("1".equals(str9)) {
/* 229 */       hashMap.put("datas", new ArrayList());
/* 230 */       return (Map)hashMap;
/*     */     } 
/* 232 */     String str11 = " where 1=1 ";
/* 233 */     RecordSet recordSet = new RecordSet();
/* 234 */     if (!"".equals(str1)) {
/* 235 */       str11 = str11 + " and (b.nodename like '%" + str1 + "%' ";
/* 236 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/* 238 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 239 */           str11 = str11 + " or f_GetPy(b.nodename) like '%" + str1.toUpperCase() + "%'";
/* 240 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 241 */           str11 = str11 + " or [dbo].f_GetPy(b.nodename) like '%" + str1.toUpperCase() + "%'";
/* 242 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 243 */           str11 = str11 + " or getpinyin(b.nodename) like '%" + str1.toUpperCase() + "%'";
/*     */         }  } 
/* 245 */       str11 = str11 + ")";
/*     */     } 
/* 247 */     String str12 = str2;
/* 248 */     if (!"1".equals(str8)) {
/* 249 */       str12 = WorkflowVersion.getActiveVersionWFID(str2);
/*     */     }
/*     */     
/* 252 */     if (!"".equals(str10)) {
/* 253 */       str11 = str11 + "and a.workflowid in (" + str10 + ")";
/*     */     } else {
/* 255 */       str11 = str11 + "and a.workflowid = " + str12;
/*     */     } 
/*     */     
/* 258 */     if (!"".equals(str3)) {
/* 259 */       str11 = str11 + " and a.nodetype in(" + str3 + ") ";
/*     */     }
/* 261 */     if (!"".equals(str4)) {
/* 262 */       str11 = str11 + " and b.nodeattribute in(" + str4 + ") ";
/*     */     }
/* 264 */     if (str5.equals("1")) {
/* 265 */       str11 = str11 + " and (b.isfreenode <> '1' or b.isfreenode is null) ";
/*     */     }
/* 267 */     if (!str6.equals("")) {
/* 268 */       str11 = str11 + " and a.nodeid not in( " + str6 + ") ";
/*     */     }
/* 270 */     if (!str7.equals("")) {
/* 271 */       str11 = str11 + " and a.nodeid in( " + str7 + ") ";
/*     */     }
/*     */     
/* 274 */     String str13 = " c.workflowname,a.nodeid,b.nodename,a.nodetype ";
/* 275 */     String str14 = " from workflow_base c  left join workflow_flownode a on c.id=a.workflowid left join workflow_nodebase b on a.nodeid = b.id ";
/* 276 */     String str15 = " order by a.nodeorder,a.nodetype,b.id ";
/* 277 */     if (!"".equals(str10)) {
/* 278 */       str15 = " order by workflowid,nodeorder,nodetype,b.id ";
/*     */     }
/* 280 */     String str16 = " select " + str13 + str14 + str11 + str15;
/* 281 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 282 */     recordSet.executeQuery(str16, new Object[0]);
/* 283 */     while (recordSet.next()) {
/* 284 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 285 */       hashMap1.put("id", recordSet.getString("nodeid"));
/* 286 */       hashMap1.put("name", recordSet.getString("nodename"));
/* 287 */       arrayList.add(hashMap1);
/*     */     } 
/* 289 */     hashMap.put("datas", arrayList);
/* 290 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeType(String paramString1, String paramString2) {
/* 300 */     return getTypename(Util.getIntValue(paramString1), Util.getIntValue(paramString2));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeType1(String paramString1, String paramString2) {
/* 309 */     return SystemEnv.getHtmlLabelName(15536, Util.getIntValue(paramString2)) + ": " + getNodeType(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrderStr(String paramString1, String paramString2, RecordSet paramRecordSet) {
/* 318 */     String str = "";
/* 319 */     if ("oracle".equals(paramRecordSet.getDBType())) {
/* 320 */       str = str + ",instr('," + paramString1 + ",' , ','||" + paramString2 + "||',') asc";
/* 321 */     } else if ("sqlserver".equals(paramRecordSet.getDBType())) {
/* 322 */       str = str + ",charindex(','+cast(" + paramString1 + " as varchar)+',' , '," + paramString2 + ",') asc";
/* 323 */     } else if ("mysql".equals(paramRecordSet.getDBType())) {
/* 324 */       str = ",field(" + paramString2 + "," + paramString1 + ")";
/*     */     }
/* 326 */     else if ("oracle".equals(paramRecordSet.getDBType())) {
/* 327 */       str = str + ",instr('," + paramString1 + ",' , ','||" + paramString2 + "||',') asc";
/*     */     } 
/* 329 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowName(String paramString1, String paramString2) {
/* 338 */     return SystemEnv.getHtmlLabelName(125749, Util.getIntValue(paramString2)) + ": " + Util.formatMultiLang(paramString1, paramString2) + "    ";
/*     */   }
/*     */   
/*     */   public static String getTypename(int paramInt1, int paramInt2) {
/* 342 */     switch (paramInt1) {
/*     */       case 0:
/* 344 */         return SystemEnv.getHtmlLabelName(125, paramInt2);
/*     */       case 1:
/* 346 */         return SystemEnv.getHtmlLabelName(142, paramInt2);
/*     */       case 2:
/* 348 */         return SystemEnv.getHtmlLabelName(615, paramInt2);
/*     */       case 3:
/* 350 */         return SystemEnv.getHtmlLabelName(251, paramInt2);
/*     */       case 5:
/* 352 */         return SystemEnv.getHtmlLabelName(529067, paramInt2);
/*     */       case 6:
/* 354 */         return SystemEnv.getHtmlLabelName(529068, paramInt2);
/*     */     } 
/* 356 */     return paramInt1 + "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowNodeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */