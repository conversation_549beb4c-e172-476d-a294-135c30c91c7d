/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.workflow.entity.SystemFieldInfoEntity;
/*     */ import com.engine.workflow.util.LinkAgeViewAttrUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WfDataSource;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowColumnFieldService
/*     */   extends BrowserService
/*     */ {
/*     */   public static String getQueryFormFieldSQL(int paramInt1, int paramInt2) {
/*  43 */     StringBuffer stringBuffer = new StringBuffer();
/*  44 */     if (paramInt2 == 0) {
/*  45 */       stringBuffer.append(" select workflow_formfield.fieldid        as id,                                              \n");
/*  46 */       stringBuffer.append("         fieldname                        as name,                                            \n");
/*  47 */       stringBuffer.append("         workflow_fieldlable.fieldlable   as label,                                           \n");
/*  48 */       stringBuffer.append("         workflow_formfield.fieldorder    as dsporder,                                        \n");
/*  49 */       stringBuffer.append("         workflow_formdict.fielddbtype    as dbtype,                                          \n");
/*  50 */       stringBuffer.append("         workflow_formdict.fieldhtmltype  as httype,                                          \n");
/*  51 */       stringBuffer.append("         workflow_formdict.type           as type,                                            \n");
/*  52 */       stringBuffer.append("         '0'                              as viewtype,                                        \n");
/*  53 */       stringBuffer.append("         ''                               as detailtable,                                     \n");
/*  54 */       stringBuffer.append("         workflow_formfield.groupid       as groupid,                                         \n");
/*  55 */       stringBuffer.append("         'workflow_form'                  as maintablename                                    \n");
/*  56 */       stringBuffer.append("    from workflow_formfield, workflow_formdict, workflow_fieldlable                           \n");
/*  57 */       stringBuffer.append("   where workflow_fieldlable.formid = workflow_formfield.formid                               \n");
/*  58 */       stringBuffer.append("     and workflow_fieldlable.isdefault = 1                                                    \n");
/*  59 */       stringBuffer.append("     and workflow_fieldlable.fieldid = workflow_formfield.fieldid                             \n");
/*  60 */       stringBuffer.append("     and workflow_formdict.id = workflow_formfield.fieldid                                    \n");
/*  61 */       stringBuffer.append("     and workflow_formfield.formid = " + paramInt1 + "                                           \n");
/*  62 */       stringBuffer.append("     and (workflow_formfield.isdetail != '1' or workflow_formfield.isdetail is null)          \n");
/*  63 */       stringBuffer.append("  union                                                                                       \n");
/*  64 */       stringBuffer.append("  select workflow_formfield.fieldid               as id,                                      \n");
/*  65 */       stringBuffer.append("         fieldname                                as name,                                    \n");
/*  66 */       stringBuffer.append("         workflow_fieldlable.fieldlable           as label,                                   \n");
/*  67 */       stringBuffer.append("         workflow_formfield.fieldorder + 100      as dsporder,                                \n");
/*  68 */       stringBuffer.append("         workflow_formdictdetail.fielddbtype      as dbtype,                                  \n");
/*  69 */       stringBuffer.append("         workflow_formdictdetail.fieldhtmltype    as httype,                                  \n");
/*  70 */       stringBuffer.append("         workflow_formdictdetail.type             as type,                                    \n");
/*  71 */       stringBuffer.append("         '1'                                      as viewtype,                                \n");
/*  72 */       stringBuffer.append("         'workflow_formdetail'                    as detailtable,                             \n");
/*  73 */       stringBuffer.append("         workflow_formfield.groupid               as groupid,                                 \n");
/*  74 */       stringBuffer.append("         ''                                       as maintablename                            \n");
/*  75 */       stringBuffer.append("    from workflow_formfield, workflow_formdictdetail, workflow_fieldlable                     \n");
/*  76 */       stringBuffer.append("   where workflow_fieldlable.formid = workflow_formfield.formid                               \n");
/*  77 */       stringBuffer.append("     and workflow_fieldlable.isdefault = 1                                                    \n");
/*  78 */       stringBuffer.append("     and workflow_fieldlable.fieldid = workflow_formfield.fieldid                             \n");
/*  79 */       stringBuffer.append("     and workflow_formdictdetail.id = workflow_formfield.fieldid                              \n");
/*  80 */       stringBuffer.append("     and workflow_formfield.formid =" + paramInt1 + "                                            \n");
/*  81 */       stringBuffer.append("     and (workflow_formfield.isdetail = '1' or workflow_formfield.isdetail is not null)       \n");
/*     */     }
/*  83 */     else if (paramInt2 == 1) {
/*  84 */       stringBuffer.append("    select wfbf.id            as id,              \n");
/*  85 */       stringBuffer.append("           wfbf.fieldname     as name,            \n");
/*  86 */       stringBuffer.append("           wfbf.fieldlabel    as label,           \n");
/*  87 */       stringBuffer.append("           wfbf.fielddbtype   as dbtype,          \n");
/*  88 */       stringBuffer.append("           wfbf.fieldhtmltype as httype,          \n");
/*  89 */       stringBuffer.append("           wfbf.type          as type,            \n");
/*  90 */       stringBuffer.append("           wfbf.dsporder      as dsporder,        \n");
/*  91 */       stringBuffer.append("           wfbf.viewtype      as viewtype,        \n");
/*  92 */       stringBuffer.append("           wfbf.detailtable   as detailtable,     \n");
/*  93 */       stringBuffer.append("           wfb.tablename      as maintablename    \n");
/*  94 */       stringBuffer.append("      from workflow_billfield wfbf                \n");
/*  95 */       stringBuffer.append("      left join workflow_bill wfb on wfbf.billid  = wfb.id \n");
/*  96 */       stringBuffer.append("     where wfbf.billid = " + paramInt1 + "           \n");
/*     */     } 
/*     */     
/*  99 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 104 */     return datInputBrowserData(paramMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 123 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 124 */     String str = Util.null2String(paramMap.get("detailindex"));
/* 125 */     boolean bool = str.equals("") ? false : Integer.parseInt(str);
/*     */     
/* 127 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 128 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 129 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "606", "name111111111", true));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 134 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 135 */     int i = this.user.getLanguage();
/* 136 */     arrayList1.add(new SearchConditionOption("0", "", false));
/* 137 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(83678, i), true));
/* 138 */     if (!bool) {
/* 139 */       arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(83679, i)));
/*     */     }
/* 141 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, "63", "fieldtype", arrayList1));
/* 142 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 143 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 150 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 151 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 152 */     String str2 = Util.null2String(paramMap.get("workflowId"));
/* 153 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isBill")), -1);
/* 154 */     int j = Util.getIntValue(Util.null2String(paramMap.get("formId")), -1);
/* 155 */     String str3 = Util.null2String(Util.null2String(paramMap.get("nodeId")));
/* 156 */     if (i == -1 || j == -1) {
/* 157 */       RecordSet recordSet1 = new RecordSet();
/* 158 */       String str = "SELECT formid , isbill FROM workflow_base WHERE id = ?";
/* 159 */       recordSet1.executeQuery(str, new Object[] { str2 });
/* 160 */       if (recordSet1.next()) {
/* 161 */         i = recordSet1.getInt("isBill");
/* 162 */         j = recordSet1.getInt("formId");
/*     */       } 
/*     */     } 
/*     */     
/* 166 */     if (!str3.equals("")) {
/* 167 */       str1 = LinkAgeViewAttrUtil.getSelectids(paramMap, this.user);
/*     */     }
/*     */     
/* 170 */     if ("".equals(str1)) return (Map)hashMap; 
/* 171 */     String str4 = "";
/* 172 */     if (i == 0) {
/* 173 */       str4 = str4 + " select id, name,label,dsporder,dbtype, httype,type, viewtype,detailtable, groupid , maintablename from ";
/*     */     } else {
/* 175 */       str4 = str4 + " select id, name,label,dbtype,httype,type,dsporder,viewtype, detailtable, maintablename from ";
/*     */     } 
/* 177 */     str4 = str4 + " ( " + getQueryFormFieldSQL(j, i) + " ) a where id in (" + str1 + ")";
/* 178 */     if (i == 0) {
/* 179 */       str4 = str4 + " order by viewtype,groupid,dsporder ";
/*     */     } else {
/* 181 */       str4 = str4 + " order by viewtype,detailtable,dsporder ";
/*     */     } 
/* 183 */     RecordSet recordSet = new RecordSet();
/* 184 */     recordSet.executeQuery(str4, new Object[0]);
/* 185 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 186 */     while (recordSet.next()) {
/* 187 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 188 */       hashMap1.put("id", recordSet.getString("id"));
/* 189 */       if (i == 0) {
/* 190 */         String str = recordSet.getString("viewtype") + "+" + recordSet.getString("groupid") + "+" + i + "+" + this.user.getLanguage() + "+" + j;
/* 191 */         hashMap1.put("showname", doFieldNameTrans(recordSet.getString("label"), str));
/* 192 */         hashMap1.put("label", recordSet.getString("label"));
/* 193 */         hashMap1.put("tableindexspan", tableTypeTransmethod(recordSet.getString("groupid"), this.user.getLanguage() + ""));
/*     */       } else {
/* 195 */         String str = recordSet.getString("viewtype") + "+" + recordSet.getString("detailtable") + "+" + i + "+" + this.user.getLanguage() + "+" + j;
/* 196 */         hashMap1.put("showname", doFieldNameTrans(SystemEnv.getHtmlLabelName(recordSet.getInt("label"), this.user.getLanguage()), str));
/* 197 */         hashMap1.put("label", labelNameTransmethod(recordSet.getString("label"), this.user.getLanguage() + ""));
/* 198 */         hashMap1.put("tableindexspan", detailTableTrans(recordSet.getString("detailtable"), this.user.getLanguage() + "+" + recordSet.getString("viewtype")));
/*     */       } 
/* 200 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 203 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 204 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 205 */     arrayList1.add(new ListHeadBean("fieldName", "", 1));
/* 206 */     arrayList1.add(new ListHeadBean("tableType", "", 0));
/*     */     
/* 208 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 209 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 210 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> datInputBrowserData(Map<String, Object> paramMap) {
/* 220 */     int i = Util.getIntValue(Util.null2String(paramMap.get("tableType")), -1);
/* 221 */     int j = Util.getIntValue(Util.null2String(paramMap.get("istriggerField")), -1);
/* 222 */     int k = Util.getIntValue(Util.null2String(paramMap.get("pagefield")), -1);
/* 223 */     if (j == 1)
/*     */     {
/* 225 */       return searchDatainput(paramMap, i);
/*     */     }
/*     */     
/* 228 */     if (k != -1) {
/* 229 */       return datInputBrowserDataForpagefield(paramMap);
/*     */     }
/* 231 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 232 */     String str1 = Util.null2String(paramMap.get("searchtablename"));
/* 233 */     String str2 = Util.null2String(paramMap.get("fieldName"));
/* 234 */     String str3 = Util.null2String(paramMap.get("datasourceid"));
/* 235 */     String str4 = Util.null2String(paramMap.get("tablenames"));
/*     */ 
/*     */     
/* 238 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 262 */     String str5 = "<table pageId=\"Wf:workflow_triggertablefieldsbrowser\" datasource=\"weaver.workflow.workflow.WfDataSource.getWorkflowTableFieldForE9\" sourceparams=\"searchtablename:" + str1 + "+searchfieldname:" + str2 + "+datasourceid:" + str3 + "+tablenames:" + str4 + "\" instanceid=\"docMouldTable\" pagesize=\"" + PageIdConst.getPageSize("Wf:workflow_triggertablefieldsbrowser", this.user.getUID()) + "\" tabletype=\"none\"> <checkboxpopedom  id=\"checkbox\" /><sql backfields=\"*\"  sqlform=\"tmptable\" sqlorderby=\"id\"  sqlprimarykey=\"id\" sqlsortway=\"asc\"  sqldistinct=\"true\" /><head><col width=\"0%\"  isInputCol=\"false\"  hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"fieldname\"/><col width=\"50%\" isInputCol=\"false\"   hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(685, this.user.getLanguage()) + "\" column=\"fieldlabel\"/><col width=\"50%\" isInputCol=\"false\" hide=\"false\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" column=\"tablelabel\" text=\"" + SystemEnv.getHtmlLabelName(33523, this.user.getLanguage()) + "\"/><col width=\"0%\" isInputCol=\"false\" hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"tablename\"/><col width=\"0%\" isInputCol=\"false\"  hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"tabfix\"/><col width=\"0%\" isInputCol=\"true\"  hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"name\"/><col width=\"0%\" isPrimarykey=\"true\" isInputCol=\"false\"  hide=\"true\"  transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"fieldnamefix\"/> transmethod=\"weaver.workflow.design.WFDesignTransMethod.getFieldShowType\"  /></head></table>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 272 */     String str6 = Util.getEncrypt(Util.getRandom());
/* 273 */     Util_TableMap.setVal(str6, str5);
/* 274 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/* 275 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str6);
/* 276 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> datInputBrowserDataForpagefield(Map<String, Object> paramMap) {
/* 286 */     String str1 = Util.null2String(paramMap.get("workflowid"));
/* 287 */     String str2 = Util.null2String(paramMap.get("htmltype"));
/* 288 */     String str3 = Util.null2String(paramMap.get("type"));
/* 289 */     String str4 = Util.null2String(paramMap.get("fieldName"));
/* 290 */     int i = Util.getIntValue(Util.null2String(paramMap.get("tableType")));
/* 291 */     String str5 = Util.null2String(paramMap.get("sqlwhere"));
/* 292 */     int j = Util.getIntValue(Util.null2String(paramMap.get("FieldType")), -1);
/* 293 */     int k = Util.getIntValue(Util.null2String(paramMap.get("detailindex")), -1);
/* 294 */     int m = Util.getIntValue(Util.null2String(paramMap.get("browsertype")), -1);
/* 295 */     if (m == 1) {
/* 296 */       i = k;
/*     */     }
/* 298 */     else if (j > 0) {
/* 299 */       i = k;
/*     */     } 
/*     */ 
/*     */     
/* 303 */     boolean bool = false;
/* 304 */     String str6 = "";
/* 305 */     int n = 0;
/* 306 */     if ("".equals(str5) && !"".equals(str1)) {
/* 307 */       bool = false;
/*     */     } else {
/* 309 */       bool = true;
/* 310 */       str6 = Util.null2String(paramMap.get("isbill"));
/* 311 */       n = Util.getIntValue(Util.null2String(paramMap.get("isdetail")), 0);
/*     */     } 
/* 313 */     RecordSet recordSet = new RecordSet();
/* 314 */     String str7 = "";
/* 315 */     recordSet.executeSql("select formid,isbill from workflow_base where id=" + str1);
/* 316 */     if (recordSet.next()) {
/* 317 */       str7 = recordSet.getString("formid");
/* 318 */       str6 = recordSet.getString("isbill");
/*     */     } 
/*     */     
/* 321 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 332 */     String str8 = "<table pageId=\"Wf:workflow_fieldbrowser\" datasource=\"weaver.workflow.workflow.WfDataSource.getWorkflowTableTriggerField\" sourceparams=\"htmltype:" + str2 + "+ise9:1+type:" + str3 + "+isbill:" + str6 + "+isdetail:" + n + "+wfid:" + str1 + "+fieldname:" + str4 + "+tabletype:" + i + "+sqlwhere:" + Util.toHtmlForSplitPage(str5) + "+isfrom:1\" instanceid=\"docMouldTable\" pagesize=\"" + PageIdConst.getPageSize("Wf:workflow_fieldbrowser", this.user.getUID()) + "\" tabletype=\"none\"> <checkboxpopedom  id=\"checkbox\" /><sql backfields=\"*\"  sqlform=\"tmptable\" sqlorderby=\"id\"  sqlprimarykey=\"id\" sqlsortway=\"asc\"  sqldistinct=\"true\" /><head><col width=\"0%\"  isInputCol=\"false\"    isPrimarykey=\"true\" hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"@@\" column=\"fieldid\"/><col width=\"40%\"  isInputCol=\"false\"  hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(685, this.user.getLanguage()) + "\" column=\"fieldname\"/><col width=\"30%\"  isInputCol=\"false\"  hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" column=\"tabletype\" text=\"" + SystemEnv.getHtmlLabelName(17997, this.user.getLanguage()) + "\"/><col width=\"30%\"  isInputCol=\"false\" hide=\"false\"  text=\"" + SystemEnv.getHtmlLabelName(687, this.user.getLanguage()) + "\" column=\"htmltype\"  otherpara=\"column:type+" + this.user.getLanguage() + "+column:fieldid\" transmethod=\"weaver.workflow.design.WFDesignTransMethod.getFieldShowType\"  /><col width=\"0%\"  isInputCol=\"false\"   hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"detailindex\"/><col width=\"0%\"  isInputCol=\"false\"   hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"tempoption\"/><col width=\"0%\"   isInputCol=\"true\" otherpara=\"column:tabletype+column:fieldname\"  hide=\"true\" transmethod=\"com.api.browser.service.impl.WorkflowDatainputFiledBrowserService.datainputTrans\" text=\"\" column=\"fieldnamefix\"/></head></table>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 341 */     String str9 = Util.getEncrypt(Util.getRandom());
/* 342 */     Util_TableMap.setVal(str9, str8);
/* 343 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/* 344 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str9);
/* 345 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getDataInputConditionInfo(Map<String, Object> paramMap, int paramInt) throws Exception {
/* 356 */     int i = Util.getIntValue(Util.null2String(paramMap.get("browsertype")), -1);
/* 357 */     int j = Util.getIntValue(Util.null2String(paramMap.get("detailindex")), 0);
/* 358 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 359 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 360 */     String str1 = workflowAllComInfo.getIsBill(paramInt + "");
/* 361 */     String str2 = workflowAllComInfo.getFormId(paramInt + "");
/* 362 */     ArrayList arrayList = new ArrayList();
/* 363 */     RecordSet recordSet = new RecordSet();
/* 364 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 365 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 366 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 367 */     arrayList1.add(conditionFactory.createCondition(ConditionType.INPUT, 685, "fieldName", true));
/* 368 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 26734, "tableType");
/* 369 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 370 */     searchConditionItem.setOptions(arrayList2);
/* 371 */     arrayList1.add(searchConditionItem);
/*     */     
/* 373 */     if (j > 0) {
/*     */       
/* 375 */       arrayList2.add(new SearchConditionOption(j + "", 
/* 376 */             SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + j, true));
/* 377 */       return (Map)hashMap;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 382 */     if (j == 0 && i == 1) {
/* 383 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()), true));
/* 384 */       return (Map)hashMap;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 389 */     arrayList2.add(new SearchConditionOption("-1", " ", true));
/* 390 */     arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()), false));
/*     */ 
/*     */     
/* 393 */     if ("".equals(str2) || i == 1) {
/* 394 */       return (Map)hashMap;
/*     */     }
/* 396 */     if (str1.equals("0")) {
/* 397 */       recordSet.executeSql("select distinct groupid from workflow_formfield where isdetail=1 and formid=" + str2 + " order by groupid ");
/*     */     } else {
/* 399 */       recordSet.executeSql("select orderid from workflow_billdetailtable where billid=" + str2 + " order by orderid ");
/*     */     } 
/* 401 */     String str3 = "1";
/* 402 */     while (recordSet.next()) {
/* 403 */       if (str1.equals("0")) {
/*     */         
/* 405 */         str3 = (recordSet.getInt(1) + 1) + "";
/*     */       } else {
/* 407 */         str3 = recordSet.getString(1);
/*     */       } 
/*     */       
/* 410 */       arrayList2.add(new SearchConditionOption(str3, 
/* 411 */             SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + str3, false));
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 416 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getDBfiledConditionInfo(Map<String, Object> paramMap) {
/* 427 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 428 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 429 */     ArrayList arrayList = new ArrayList();
/* 430 */     RecordSet recordSet1 = new RecordSet();
/* 431 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 432 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 433 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 434 */     arrayList1.add(conditionFactory.createCondition(ConditionType.INPUT, 685, "fieldName", true));
/* 435 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 26734, "searchtablename");
/* 436 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*     */ 
/*     */     
/* 439 */     searchConditionItem.setOptions(arrayList2);
/* 440 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 448 */     String str1 = Util.null2String(paramMap.get("searchtablename"));
/* 449 */     String str2 = Util.null2String(paramMap.get("datasourceid"));
/* 450 */     String str3 = Util.toHtmlForSplitPage(Util.null2String(paramMap.get("tablenames")));
/* 451 */     ArrayList<String> arrayList3 = Util.TokenizerString(str3, ",");
/* 452 */     RecordSet recordSet2 = new RecordSet();
/* 453 */     arrayList2.add(new SearchConditionOption("", " ", arrayList3.isEmpty()));
/* 454 */     for (byte b = 0; b < arrayList3.size(); b++) {
/* 455 */       String str4 = arrayList3.get(b);
/* 456 */       String str5 = "";
/* 457 */       String str6 = "";
/* 458 */       String[] arrayOfString = str4.split("~");
/* 459 */       str5 = arrayOfString[0];
/* 460 */       str6 = arrayOfString[1];
/* 461 */       String str7 = "";
/* 462 */       String str8 = "";
/* 463 */       if (arrayOfString.length == 4) {
/* 464 */         str7 = arrayOfString[2];
/* 465 */         str8 = arrayOfString[3];
/*     */       } 
/* 467 */       if (str2 != null && !str2.equals("")) {
/* 468 */         arrayList2.add(new SearchConditionOption(str6, str6, str6.equals(str1)));
/*     */       }
/* 470 */       else if (!"".equals(str5) && !"0".equals(str5)) {
/*     */         
/* 472 */         String str9 = "";
/* 473 */         String str10 = "0";
/* 474 */         if (str6.equals("workflow_formdetail")) {
/* 475 */           str10 = "" + (Util.getIntValue(str5.substring(str5.indexOf("_") + 1, str5.length()), 0) + 1);
/* 476 */           str5 = str5.substring(0, str5.indexOf("_"));
/*     */         } 
/*     */         
/* 479 */         recordSet2.executeSql("select formname from workflow_formbase where id=" + str5);
/* 480 */         if (recordSet2.next()) str9 = recordSet2.getString("formname"); 
/* 481 */         char c = '唒';
/* 482 */         if (str6.equals("workflow_formdetail")) {
/* 483 */           c = '䭽';
/*     */         } else {
/* 485 */           str10 = "";
/*     */         } 
/* 487 */         arrayList2.add(new SearchConditionOption(str5, str9 + "(" + SystemEnv.getHtmlLabelName(c, this.user.getLanguage()) + str10 + ")", str5
/* 488 */               .equals(str1)));
/* 489 */       } else if (!str6.equals("")) {
/* 490 */         boolean bool = false;
/* 491 */         if (str6.equals(str1)) bool = true; 
/* 492 */         String str9 = "";
/* 493 */         recordSet2.executeSql("select namelabel from workflow_bill where tablename='" + str6 + "'");
/* 494 */         if (recordSet2.next()) {
/* 495 */           str9 = SystemEnv.getHtmlLabelName(recordSet2.getInt("namelabel"), this.user.getLanguage());
/* 496 */           arrayList2.add(new SearchConditionOption(str6, str9 + "(" + SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()) + ")", bool));
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 501 */         recordSet2.executeSql("select namelabel from workflow_bill where detailtablename='" + str6 + "'");
/* 502 */         if (recordSet2.next()) {
/* 503 */           str9 = SystemEnv.getHtmlLabelName(recordSet2.getInt("namelabel"), this.user.getLanguage());
/* 504 */           recordSet1.executeSql("select tablename from Workflow_billdetailtable where tablename='" + str6 + "'");
/* 505 */           if (!recordSet1.next()) {
/* 506 */             str9 = SystemEnv.getHtmlLabelName(recordSet2.getInt("namelabel"), this.user.getLanguage());
/*     */             
/* 508 */             arrayList2.add(new SearchConditionOption(str6, str9 + "(" + SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + "1)", bool));
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 513 */         recordSet2.executeSql("select billid from Workflow_billdetailtable where tablename='" + str6 + "'");
/* 514 */         if (recordSet2.next()) {
/* 515 */           String str = recordSet2.getString("billid");
/* 516 */           recordSet2.executeSql("select namelabel from workflow_bill where id=" + str);
/* 517 */           if (recordSet2.next()) {
/* 518 */             str9 = SystemEnv.getHtmlLabelName(recordSet2.getInt("namelabel"), this.user.getLanguage());
/*     */           }
/* 520 */           recordSet2.executeSql("select tablename from Workflow_billdetailtable where billid=" + str + "  ORDER BY orderid");
/* 521 */           byte b1 = 0;
/* 522 */           while (recordSet2.next()) {
/* 523 */             b1++;
/* 524 */             String str11 = Util.null2String(recordSet2.getString("tablename"));
/* 525 */             if (str11.equals(str6))
/*     */             {
/*     */               
/* 528 */               arrayList2.add(new SearchConditionOption(str6, str9 + "(" + SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b1 + ")", bool));
/*     */             }
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 535 */         String str10 = "";
/* 536 */         if (this.user.getLanguage() == 7)
/* 537 */           str10 = "select tabledesc from Sys_tabledict where tablename='" + str6 + "'"; 
/* 538 */         if (this.user.getLanguage() == 8)
/* 539 */           str10 = "select tabledescen from Sys_tabledict where tablename='" + str6 + "'"; 
/* 540 */         recordSet2.executeSql(str10);
/* 541 */         if (recordSet2.next()) {
/* 542 */           str9 = recordSet2.getString(1);
/*     */           
/* 544 */           arrayList2.add(new SearchConditionOption(str6, str9 + "(" + SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()) + ")", bool));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 552 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> searchDatainput(Map<String, Object> paramMap, int paramInt) {
/* 562 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 563 */     int i = Util.getIntValue(Util.null2String(paramMap.get("workflowid")), 0);
/* 564 */     String str1 = Util.null2String(Util.null2String(paramMap.get("htmltype")));
/* 565 */     String str2 = Util.null2String(Util.null2String(paramMap.get("type")));
/* 566 */     String str3 = Util.null2String(paramMap.get("fielddbtype"));
/*     */     
/* 568 */     String str4 = Util.toSqlForSplitPage(Util.null2String(paramMap.get("sqlwhere")));
/* 569 */     String str5 = Util.null2String(paramMap.get("isfrom"));
/* 570 */     String str6 = Util.null2String(paramMap.get("detailindex"));
/* 571 */     String str7 = Util.null2String(paramMap.get("fieldtype"));
/* 572 */     String str8 = Util.null2String(paramMap.get("name111111111"));
/* 573 */     boolean bool1 = false;
/* 574 */     String str9 = "";
/* 575 */     boolean bool2 = str6.equals("") ? false : Integer.parseInt(str6);
/* 576 */     if ("".equals(str4) && !"".equals(Integer.valueOf(i))) {
/* 577 */       bool1 = false;
/*     */     } else {
/* 579 */       bool1 = true;
/*     */     } 
/*     */     
/* 582 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 583 */     String str10 = workflowAllComInfo.getFormId(i + "");
/* 584 */     RecordSet recordSet = new RecordSet();
/* 585 */     if (str10.equals("") || str9.equals("")) {
/* 586 */       recordSet.executeSql("select formid,isbill from workflow_base where id=" + i);
/* 587 */       if (recordSet.next()) {
/* 588 */         str10 = recordSet.getString("formid");
/* 589 */         str9 = recordSet.getString("isbill");
/*     */       } 
/*     */     } 
/* 592 */     ArrayList<String> arrayList = new ArrayList();
/* 593 */     if (str9.equals("0")) {
/* 594 */       recordSet.executeSql("select distinct groupid from workflow_formfield where isdetail=1 and formid=" + str10 + " order by groupid ");
/*     */     } else {
/* 596 */       recordSet.executeSql("select orderid from workflow_billdetailtable where billid=" + str10 + " order by orderid ");
/*     */     } 
/* 598 */     while (recordSet.next()) {
/* 599 */       if (str9.equals("0")) {
/* 600 */         arrayList.add((recordSet.getInt(1) + 1) + ""); continue;
/*     */       } 
/* 602 */       arrayList.add(recordSet.getString(1));
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 624 */     String str11 = "dmlFormFieldsBrowser";
/*     */     
/* 626 */     String str12 = "1";
/*     */     
/* 628 */     if (str7.equals("0")) {
/* 629 */       str7 = "";
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 641 */     String str13 = "<table  datasource=\"weaver.workflow.exchange.rdata.RDataUtil.getDMLFormField\" sourceparams=\"formid:" + str10 + "+isbill:" + str9 + "+fieldname:" + str8 + "+fieldtype:" + str7 + "+issearch:" + str12 + "+detailindex:" + bool2 + "\" instanceid=\"Table\" pagesize=\"" + PageIdConst.getPageSize(str11, this.user.getUID()) + "\" tabletype=\"none\"> <checkboxpopedom  id=\"none\" /><sql backfields=\"*\"  sqlform=\"tmptable\" sqlorderby=\"id\"  sqlprimarykey=\"id\" sqlsortway=\"asc\"  sqldistinct=\"true\" /><head><col width=\"0%\" hide=\"true\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"\" column=\"id\"/><col width=\"20%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(15456, this.user.getLanguage()) + "\" column=\"jfieldlabel\"/><col width=\"30%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(685, this.user.getLanguage()) + "\" column=\"jfieldname\"/><col width=\"20%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(686, this.user.getLanguage()) + "\" column=\"jfielddbtype\"/><col width=\"30%\" hide=\"false\" transmethod=\"weaver.general.KnowledgeTransMethod.forHtml\" text=\"" + SystemEnv.getHtmlLabelName(63, this.user.getLanguage()) + "\" column=\"fielddesc\"/><col width=\"\" hide=\"true\" text=\"\"  column=\"id\" otherpara=\"column:id+formid:" + str10 + "+isbill:" + str9 + "+" + bool2 + "\" transmethod=\"weaver.workflow.exchange.ExchangeUtil.getFieldHtmltype\"/></head></table>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 647 */     String str14 = Util.getEncrypt(Util.getRandom());
/* 648 */     Util_TableMap.setVal(str14, str13);
/* 649 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/* 650 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str14);
/* 651 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 669 */     switch (Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("autoType")))) {
/*     */       
/*     */       case 0:
/* 672 */         return autoSearchTriggerField(paramHttpServletRequest, paramHttpServletResponse, true);
/*     */       
/*     */       case 1:
/* 675 */         return autoSearchDbField(paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */ 
/*     */     
/* 679 */     return autoSearchTriggerField(paramHttpServletRequest, paramHttpServletResponse, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> autoSearchTriggerField(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, boolean paramBoolean) {
/* 689 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 691 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("FieldType")), -1);
/* 692 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("detailindex")), -1);
/* 693 */     int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("browsertype")), -1);
/* 694 */     int m = -1;
/* 695 */     if (k == 1) {
/* 696 */       m = j;
/*     */     }
/* 698 */     else if (i > 0) {
/* 699 */       m = j;
/*     */     } 
/*     */ 
/*     */     
/* 703 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 704 */     hashMap2.put("tabletype", m + "");
/* 705 */     hashMap2.put("wfid", Util.null2String(paramHttpServletRequest.getParameter("workflowid")));
/* 706 */     if (paramBoolean)
/* 707 */       hashMap2.put("tabletype", "-1"); 
/* 708 */     if (!paramBoolean)
/* 709 */       hashMap2.put("isfrom", "1"); 
/* 710 */     hashMap2.put("fieldname", Util.null2String(paramHttpServletRequest.getParameter("q")));
/* 711 */     hashMap2.put("ise9", "1");
/*     */     
/* 713 */     List list = (new WfDataSource()).getWorkflowTableTriggerField(this.user, hashMap2, paramHttpServletRequest, paramHttpServletResponse);
/* 714 */     for (Map map : list) {
/* 715 */       map.put("id", map.get("fieldid"));
/* 716 */       map.put("name", map.get("namefix"));
/*     */     } 
/*     */     
/* 719 */     hashMap1.put("datas", list);
/* 720 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> autoSearchDbField(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 732 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/* 735 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 736 */     hashMap2.put("searchfieldname", Util.null2String(paramHttpServletRequest.getParameter("q")));
/* 737 */     hashMap2.put("datasourceid", Util.null2String(paramHttpServletRequest.getParameter("datasourceid")));
/* 738 */     hashMap2.put("tablenames", Util.null2String(paramHttpServletRequest.getParameter("tablenames")));
/*     */     
/* 740 */     List list = (new WfDataSource()).getWorkflowTableFieldForE9(this.user, hashMap2, paramHttpServletRequest, paramHttpServletResponse);
/* 741 */     for (Map map : list) {
/* 742 */       map.put("id", map.get("fieldname"));
/*     */     }
/*     */     
/* 745 */     hashMap1.put("datas", list);
/* 746 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String datainputTrans(String paramString1, String paramString2) {
/* 757 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 758 */     String str1 = arrayOfString[0];
/* 759 */     String str2 = arrayOfString[1];
/*     */     
/* 761 */     str1 = Util.forHtml(Util.null2String(str1).replaceAll("'", "\\'"));
/* 762 */     str2 = Util.forHtml(Util.null2String(str2).replaceAll("'", "\\'"));
/* 763 */     if (!"".equals(str1)) {
/* 764 */       return str1 + "." + str2;
/*     */     }
/*     */     
/* 767 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String getUnionSql(SystemFieldInfoEntity paramSystemFieldInfoEntity, int paramInt) {
/* 773 */     RecordSet recordSet = new RecordSet();
/* 774 */     String str1 = recordSet.getDBType();
/* 775 */     String str2 = "";
/* 776 */     if (paramInt == 0) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 785 */       str2 = " UNION SELECT " + paramSystemFieldInfoEntity.getId() + " AS ID,'" + paramSystemFieldInfoEntity.getLabel() + "' AS label,'" + paramSystemFieldInfoEntity.getLabel() + "' AS showname,'' AS NAME,'' AS dbtype,'-1' AS httype,-1 AS TYPE," + paramSystemFieldInfoEntity.getDsporder() + " AS dsporder,'" + paramSystemFieldInfoEntity.getViewtype() + "' AS viewtype,-1 AS tableindex,'' AS maintablename ";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 797 */       str2 = " UNION SELECT " + paramSystemFieldInfoEntity.getId() + " AS ID,'" + paramSystemFieldInfoEntity.getLabel() + "' AS label,'" + paramSystemFieldInfoEntity.getLabel() + "' AS showname,'' AS NAME,'' AS dbtype,'-1' AS httype,-1 AS TYPE," + paramSystemFieldInfoEntity.getDsporder() + " AS dsporder," + paramSystemFieldInfoEntity.getViewtype() + " AS viewtype,'-1' AS tableindex,'' AS maintablename ";
/*     */     } 
/*     */ 
/*     */     
/* 801 */     if ("oracle".equals(str1)) {
/* 802 */       str2 = str2 + " FROM DUAL ";
/*     */     }
/* 804 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   public String tableTypeTransmethod(String paramString1, String paramString2) {
/* 809 */     String str = "";
/* 810 */     int i = Util.getIntValue(paramString2);
/* 811 */     if (paramString1 == null || "".equals(paramString1)) {
/* 812 */       str = SystemEnv.getHtmlLabelName(21778, i);
/* 813 */     } else if ("-1".equals(paramString1)) {
/* 814 */       str = SystemEnv.getHtmlLabelName(468, i);
/*     */     } else {
/* 816 */       int j = Util.getIntValue(paramString1);
/* 817 */       str = SystemEnv.getHtmlLabelName(19325, i) + (j + 1);
/*     */     } 
/* 819 */     return str;
/*     */   }
/*     */   
/*     */   public String labelNameTransmethod(String paramString1, String paramString2) {
/* 823 */     String str = "";
/* 824 */     int i = Util.getIntValue(paramString2);
/* 825 */     int j = Util.getIntValue(paramString1);
/* 826 */     str = SystemEnv.getHtmlLabelName(j, i);
/* 827 */     return str;
/*     */   }
/*     */   
/*     */   public String detailTableTrans(String paramString1, String paramString2) {
/* 831 */     String str = "";
/* 832 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 833 */     int i = Util.getIntValue(arrayOfString[0]);
/* 834 */     int j = Util.getIntValue(arrayOfString[1]);
/*     */     
/* 836 */     if (j == 0) {
/* 837 */       str = SystemEnv.getHtmlLabelName(21778, i);
/* 838 */     } else if (j == -10) {
/* 839 */       str = SystemEnv.getHtmlLabelName(468, i);
/* 840 */     } else if (j == -2) {
/* 841 */       str = SystemEnv.getHtmlLabelName(15586, i);
/*     */     } else {
/* 843 */       int k = paramString1.indexOf("_dt");
/* 844 */       if (k != -1) {
/* 845 */         int m = Util.getIntValue(paramString1.substring(k + 3));
/* 846 */         str = SystemEnv.getHtmlLabelName(19325, i) + m;
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 862 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String doFieldNameTrans(String paramString1, String paramString2) {
/* 867 */     String str1 = "";
/* 868 */     String[] arrayOfString = paramString2.split("\\+");
/* 869 */     String str2 = arrayOfString[0];
/* 870 */     String str3 = arrayOfString[1];
/* 871 */     String str4 = arrayOfString[2];
/* 872 */     int i = Util.getIntValue(arrayOfString[3]);
/* 873 */     String str5 = arrayOfString[4];
/* 874 */     if (str2.equals("-2")) {
/* 875 */       str1 = SystemEnv.getHtmlLabelName(15586, i) + "." + paramString1;
/* 876 */     } else if (str2.equals("-10")) {
/* 877 */       str1 = SystemEnv.getHtmlLabelName(468, i) + "." + paramString1;
/* 878 */     } else if (str2.equals("0")) {
/* 879 */       str1 = SystemEnv.getHtmlLabelName(21778, i) + "." + paramString1;
/*     */     }
/* 881 */     else if (str4.equals("0")) {
/* 882 */       str1 = SystemEnv.getHtmlLabelName(19325, i) + "." + paramString1;
/*     */     } else {
/* 884 */       int j = str3.indexOf("_dt");
/* 885 */       if (j != -1) {
/* 886 */         int k = Util.getIntValue(str3.substring(j + 3));
/* 887 */         str1 = SystemEnv.getHtmlLabelName(19325, i) + k + "." + paramString1;
/*     */       } else {
/* 889 */         str1 = SystemEnv.getHtmlLabelName(19325, i) + "." + paramString1;
/*     */       } 
/*     */     } 
/*     */     
/* 893 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowColumnFieldService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */