/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BelongAttr;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileJsonConfigUtil;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.MobileViewTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class DocSecretLevelBrowserService
/*    */   extends BrowserService {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 29 */     String str1 = Util.null2String(paramMap.get("name"));
/* 30 */     String str2 = Util.null2String(paramMap.get("desc"));
/* 31 */     String str3 = " ";
/* 32 */     if (!str1.equals("")) {
/* 33 */       str3 = str3 + " and name like '%";
/* 34 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 35 */       str3 = str3 + "%'";
/*    */     } 
/* 37 */     if (!str2.equals("")) {
/* 38 */       str3 = str3 + " and desc_n like '%";
/* 39 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 40 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 43 */     str3 = SqlUtils.replaceFirstAnd(str3);
/* 44 */     String str4 = " id,name,desc_n,subCompanyId";
/* 45 */     String str5 = "DocSecretLevel";
/* 46 */     String str6 = " showOrder ";
/*    */     
/* 48 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 49 */     SplitTableColBean splitTableColBean = (new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id", 0)).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT);
/* 50 */     splitTableColBean.setHide("1");
/* 51 */     arrayList.add(splitTableColBean);
/* 52 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*    */     
/* 54 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "desc_n", "desc_n")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*    */     
/* 56 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/* 57 */     splitTableBean.setSqlsortway("ASC");
/*    */     
/* 59 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*    */     try {
/* 61 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/* 62 */     } catch (Exception exception) {
/* 63 */       exception.printStackTrace();
/*    */     } 
/* 65 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 66 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 72 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 73 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 74 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "name", true));
/* 75 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "desc"));
/* 76 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 77 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private List<SplitMobileDataBean> getJonsConfig() {
/* 84 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 85 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.name");
/* 86 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.desc_n");
/* 87 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DocSecretLevelBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */