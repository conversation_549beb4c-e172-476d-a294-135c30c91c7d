/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.system.language.util.LabelPageUidFactory;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class LabelBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  24 */     RecordSet recordSet = new RecordSet();
/*  25 */     recordSet.executeQuery("select * from syslanguage where activable=1 order by id asc", new Object[0]);
/*  26 */     String str1 = "";
/*  27 */     String str2 = "";
/*  28 */     String str3 = "";
/*  29 */     String str4 = Util.null2String(paramMap.get("indexdesc"));
/*  30 */     String str5 = Util.null2String(paramMap.get("id"));
/*  31 */     str1 = "Sys:system_label_manage_list_4browser";
/*  32 */     str2 = LabelPageUidFactory.getLabelPageUid(str1);
/*  33 */     String str6 = "1=1";
/*  34 */     String str7 = "(select * from ( select a.id as id,b.indexdesc, cname,ename,twname,elname,flname from (select a.indexid as id,a.labelname as cname,c.labelname as ename,d.labelname as twname,e.labelname as elname,f.labelname as flname  from (select  * from HtmlLabelInfo where languageid='#cname#')a left join (select * from HtmlLabelInfo where languageid='#twname#')d on a.indexid = d.indexid left join (select * from HtmlLabelInfo where languageid='#ename#')c on a.indexid = c.indexid left join (select * from HtmlLabelInfo where languageid='#elname#')e on a.indexid = e.indexid left join (select * from HtmlLabelInfo where languageid='#flname#')f on a.indexid = f.indexid ) a,HtmlLabelIndex b where a.id=b.id) m) ff ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  42 */     recordSet.beforFirst();
/*  43 */     if (!"".equals(str5)) {
/*  44 */       str6 = str6 + " and id = '" + str5 + "'";
/*     */     }
/*  46 */     if (recordSet.next()) {
/*  47 */       if (!"".equals(str4)) {
/*  48 */         str6 = str6 + " and ( cname like '%" + str4 + "%'";
/*     */       }
/*  50 */       str7 = str7.replaceFirst("#cname#", recordSet.getString("id"));
/*     */     } else {
/*  52 */       str7 = str7.replaceFirst("#cname#", "7");
/*     */     } 
/*  54 */     if (recordSet.next()) {
/*  55 */       if (!"".equals(str4)) {
/*  56 */         str6 = str6 + " or ename like '%" + str4 + "%'";
/*     */       }
/*  58 */       str7 = str7.replaceFirst("#ename#", recordSet.getString("id"));
/*     */     } else {
/*  60 */       str7 = str7.replaceFirst("#ename#", "8");
/*     */     } 
/*  62 */     if (recordSet.next()) {
/*  63 */       if (!"".equals(str4)) {
/*  64 */         str6 = str6 + " or twname like '%" + str4 + "%'";
/*     */       }
/*  66 */       str7 = str7.replaceFirst("#twname#", recordSet.getString("id"));
/*     */     } else {
/*  68 */       str7 = str7.replaceFirst("#twname#", "9");
/*     */     } 
/*  70 */     if (recordSet.next()) {
/*  71 */       if (!"".equals(str4)) {
/*  72 */         str6 = str6 + " or elname like '%" + str4 + "%'";
/*     */       }
/*  74 */       str7 = str7.replaceFirst("#elname#", recordSet.getString("id"));
/*     */     } else {
/*  76 */       str7 = str7.replaceFirst("#elname#", "0");
/*     */     } 
/*  78 */     if (recordSet.next()) {
/*  79 */       if (!"".equals(str4)) {
/*  80 */         str6 = str6 + " or flname like '%" + str4 + "%'";
/*     */       }
/*  82 */       str7 = str7.replaceFirst("#flname#", recordSet.getString("id"));
/*     */     } else {
/*  84 */       str7 = str7.replaceFirst("#flname#", "0");
/*     */     } 
/*  86 */     if (!"".equals(str4)) {
/*  87 */       str6 = str6 + " )";
/*     */     }
/*  89 */     recordSet.beforFirst();
/*  90 */     String str8 = "";
/*  91 */     String str9 = "none";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  98 */     str3 = "<table pageId=\"" + str1 + "\" pageUid=\"" + str2 + "\" instanceid=\"docMouldTable\" pagesize=\"" + PageIdConst.getPageSize("Sys:system_label_manage_list", this.user.getUID(), "Doc") + "\" tabletype=\"" + str9 + "\"><sql backfields=\"*\" sqlwhere=\"" + Util.toHtmlForSplitPage(str6) + "\"  sqlform=\"" + Util.toHtmlForSplitPage(str7) + "\" sqlorderby=\"ff.id\"  sqlprimarykey=\"ff.id\" sqlsortway=\"desc\"  sqldistinct=\"true\" />" + str8 + "<head><col width=\"15%\" text=\"ID\" column=\"id\"  orderkey=\"ff.id\" hide=\"false\"/><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(433, this.user.getLanguage()) + "\" column=\"indexdesc\" orderkey=\"indexdesc\" hide=\"false\"/>";
/*  99 */     if (recordSet.next()) {
/* 100 */       str3 = str3 + "<col width=\"20%\"  text=\"" + recordSet.getString("language") + "\" column=\"cname\" orderkey=\"cname\" hide=\"false\"/>";
/*     */     }
/* 102 */     if (recordSet.next()) {
/* 103 */       str3 = str3 + "<col width=\"20%\"  text=\"" + recordSet.getString("language") + "\" column=\"ename\" orderkey=\"ename\" hide=\"false\"/>";
/*     */     }
/* 105 */     if (recordSet.next()) {
/* 106 */       str3 = str3 + "<col width=\"20%\"  text=\"" + recordSet.getString("language") + "\" column=\"twname\" orderkey=\"twname\" hide=\"false\"/>";
/*     */     }
/* 108 */     if (recordSet.next()) {
/* 109 */       str3 = str3 + "<col width=\"20%\"  text=\"" + recordSet.getString("language") + "\" column=\"elname\" orderkey=\"elname\" hide=\"false\"/>";
/*     */     }
/* 111 */     if (recordSet.next()) {
/* 112 */       str3 = str3 + "<col width=\"20%\"  text=\"" + recordSet.getString("language") + "\" column=\"flname\" orderkey=\"flname\" hide=\"false\"/>";
/*     */     }
/* 114 */     str3 = str3 + "</head></table>";
/*     */ 
/*     */     
/* 117 */     String str10 = str2 + "_" + Util.getEncrypt(Util.getRandom());
/* 118 */     Util_TableMap.setVal(str10, str3);
/* 119 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/* 120 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str10);
/* 121 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 127 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 128 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 129 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 130 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 131 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 32011, "id"));
/* 132 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 81711, "indexdesc", true));
/* 133 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/LabelBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */