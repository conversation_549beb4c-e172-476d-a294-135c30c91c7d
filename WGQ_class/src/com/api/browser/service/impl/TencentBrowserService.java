/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TencentBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  31 */     String str1 = Util.null2String(paramMap.get("englishname"));
/*  32 */     String str2 = Util.null2String(paramMap.get("chinesename"));
/*     */     
/*  34 */     String str3 = "where 1 = 1 ";
/*  35 */     String str4 = "";
/*  36 */     String str5 = "";
/*  37 */     String str6 = "";
/*  38 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  39 */     SplitTableBean splitTableBean = null;
/*  40 */     str4 = "id,englishname,chinesename";
/*  41 */     str5 = "from qqmailotherparams";
/*  42 */     str6 = "id";
/*     */     
/*  44 */     if (!str1.equals("")) {
/*  45 */       str3 = str3 + " and englishname like '%" + str1 + "%' ";
/*     */     }
/*  47 */     if (!str2.equals("")) {
/*  48 */       str3 = str3 + " and chinesename like '%" + str2 + "%' ";
/*     */     }
/*  50 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  51 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15767, this.user.getLanguage()), "englishname", "englishname", 1)).setIsInputCol(BoolAttr.TRUE));
/*  52 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "chinesename", "chinesename"));
/*     */     
/*  54 */     splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/*  55 */     splitTableBean.setSqlsortway("ASC");
/*  56 */     splitTableBean.setSqlisdistinct("true");
/*  57 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  58 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  63 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  64 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  65 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  66 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  67 */     if ("278".equals(this.browserType)) {
/*  68 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 805, "jobgroupid", "281"));
/*  69 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 15855, "jobactivitieid", "282"));
/*     */     } 
/*  71 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15767, "jobtitlename", true));
/*  72 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "jobtitlemark"));
/*  73 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  78 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  79 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  80 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  81 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/*  82 */     RecordSet recordSet = new RecordSet();
/*  83 */     String str2 = " select * from qqmailotherparams where id in (" + str1 + ") ";
/*  84 */     recordSet.executeSql(str2);
/*  85 */     while (recordSet.next()) {
/*  86 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  87 */       hashMap1.put("id", recordSet.getString("id"));
/*  88 */       hashMap1.put("englishname", Util.null2String(recordSet.getString("englishname")));
/*  89 */       hashMap1.put("chinesename", Util.null2String(recordSet.getString("chinesename")));
/*     */       
/*  91 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/*  94 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  95 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  96 */     arrayList1.add(new ListHeadBean("englishname", "", 1, BoolAttr.TRUE));
/*  97 */     arrayList1.add(new ListHeadBean("chinesename", ""));
/*     */     
/*  99 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 100 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 101 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 102 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/TencentBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */