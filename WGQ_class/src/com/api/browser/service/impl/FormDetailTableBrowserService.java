/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.engine.workflow.biz.DetailOrderBiz;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FormDetailTableBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 27 */     int i = Util.getIntValue(Util.null2String(paramMap.get("formId")));
/* 28 */     int j = Util.getIntValue(Util.null2String(paramMap.get("isBill")));
/*    */     
/* 30 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 31 */     arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 32 */     ListHeadBean listHeadBean = (new ListHeadBean("tablename", SystemEnv.getHtmlLabelName(30671, this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE);
/* 33 */     listHeadBean.setShowType(1);
/* 34 */     arrayList.add(listHeadBean);
/* 35 */     arrayList.add(new ListHeadBean("tableLabel", SystemEnv.getHtmlLabelName(30674, this.user.getLanguage())));
/* 36 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*    */     
/* 38 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 39 */     RecordSet recordSet = new RecordSet();
/* 40 */     if (j == 0) {
/* 41 */       recordSet.executeQuery("select distinct groupid from workflow_formfield where formid = ?  and isdetail = '1' order by groupid ", new Object[] { Integer.valueOf(i) });
/* 42 */       while (recordSet.next()) {
/* 43 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 44 */         int k = recordSet.getInt("groupid") + 1;
/* 45 */         hashMap1.put("id", Integer.valueOf(k));
/* 46 */         hashMap1.put("tablename", "workflow_formdetail" + k);
/* 47 */         hashMap1.put("tableLabel", SystemEnv.getHtmlLabelName(17463, this.user.getLanguage()) + k);
/* 48 */         arrayList1.add(hashMap1);
/*    */       } 
/*    */     } else {
/* 51 */       DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/* 52 */       Map map = detailOrderBiz.getDetailOrderMap(i);
/* 53 */       recordSet.executeQuery("select tablename,orderid from workflow_billdetailtable where billid = ? order by orderid", new Object[] { Integer.valueOf(i) });
/* 54 */       while (recordSet.next()) {
/* 55 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 56 */         String str1 = recordSet.getString("tablename");
/* 57 */         String str2 = recordSet.getString("orderid");
/* 58 */         hashMap1.put("id", str1);
/* 59 */         hashMap1.put("tablename", str1);
/*    */         
/* 61 */         String str3 = map.containsKey(str1) ? (new StringBuilder()).append(map.get(str1)).append("").toString() : str2;
/* 62 */         hashMap1.put("tableLabel", SystemEnv.getHtmlLabelName(17463, this.user.getLanguage()) + str3);
/* 63 */         arrayList1.add(hashMap1);
/*    */       } 
/*    */     } 
/* 66 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList1);
/* 67 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 68 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FormDetailTableBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */