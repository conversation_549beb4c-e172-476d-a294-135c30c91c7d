/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.cube.cache.CustomTreeComInfo;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.tree.CustomTreeData;
/*     */ import weaver.formmode.tree.CustomTreeUtil;
/*     */ import weaver.formmode.tree.TreeNode;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class FormmodeTreeBrowserService extends BrowserService {
/*  22 */   private FormmodeLog formmodeLog = new FormmodeLog();
/*  23 */   private FormmodeBrowserService formmodeBrowserService = new FormmodeBrowserService();
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*     */     Map<String, Object> map;
/*  26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  27 */     String str1 = Util.null2String(paramMap.get("searchbrowserid"));
/*  28 */     String str2 = Util.null2String(paramMap.get("cube_treeid"));
/*  29 */     String str3 = Util.null2String(paramMap.get("isshowall"));
/*  30 */     String str4 = Util.null2String(paramMap.get("checkexcludeids"));
/*  31 */     List<String> list = Arrays.asList(str4.split(","));
/*  32 */     boolean bool1 = false;
/*  33 */     if (str2.indexOf("_searchType") > -1) {
/*  34 */       str2 = str2.split("_searchType")[0];
/*  35 */       bool1 = true;
/*     */     } 
/*  37 */     String str5 = Util.null2String(paramMap.get("list"));
/*  38 */     String str6 = Util.null2String(paramMap.get("isshowview"));
/*     */     
/*  40 */     if ("1".equals(str5)) {
/*  41 */       if (!"".equals(str1) && !"0".equals(str1)) {
/*     */ 
/*     */ 
/*     */         
/*  45 */         RecordSet recordSet1 = new RecordSet();
/*  46 */         byte b = 0;
/*  47 */         String str15 = "select d1.id from mode_customtree t1 left JOIN mode_customtreedetail d1 on t1.id=d1.mainid where t1.id=?";
/*  48 */         recordSet1.executeQuery(str15, new Object[] { str2 });
/*  49 */         String str16 = str2;
/*  50 */         while (recordSet1.next()) {
/*  51 */           str2 = Util.null2o(recordSet1.getString("id"));
/*  52 */           b++;
/*     */         } 
/*  54 */         if (b >= 2) {
/*  55 */           return getTreeListData(paramMap);
/*     */         }
/*  57 */         str15 = "select showname from MODE_BROWSER where customid=?";
/*  58 */         this.formmodeBrowserService.setUser(this.user);
/*  59 */         String str17 = "";
/*  60 */         recordSet1.executeQuery(str15, new Object[] { str1 });
/*  61 */         if (recordSet1.next()) {
/*  62 */           str17 = Util.null2String(recordSet1.getString("showname"));
/*     */         }
/*  64 */         paramMap.put("type", str17);
/*  65 */         paramMap.put("treeid", str2);
/*     */         try {
/*  67 */           map = this.formmodeBrowserService.getBrowserData(paramMap);
/*     */           
/*  69 */           List list1 = (List)map.get(BrowserConstant.BROWSER_RESULT_DATA);
/*  70 */           for (Map map1 : list1) {
/*  71 */             for (Map.Entry entry : map1.entrySet()) {
/*  72 */               String str = (String)entry.getValue();
/*  73 */               if (!str.equals("") && str.indexOf("</a>") > 0) {
/*  74 */                 entry.setValue(((String)entry.getValue()).replaceAll("<a.*?>", "").replaceAll("</a>", ""));
/*     */               }
/*     */             } 
/*     */           } 
/*  78 */           setIconSetting(map, str16);
/*  79 */           map.put(BrowserConstant.BROWSER_RESULT_DATA, list1);
/*  80 */           return map;
/*  81 */         } catch (Exception exception) {
/*  82 */           writeLog(exception);
/*  83 */           setIconSetting(map, str16);
/*  84 */           return map;
/*     */         } 
/*     */       } 
/*     */       
/*  88 */       return getTreeListData(paramMap);
/*     */     } 
/*  90 */     String str7 = Util.null2String(paramMap.get("id"));
/*  91 */     String str8 = "";
/*  92 */     if ("".equals(str7)) {
/*  93 */       str8 = "true";
/*     */     } else {
/*  95 */       str8 = "false";
/*     */     } 
/*     */     
/*  98 */     String str9 = "SELECT a.isonlyleaf,a.isselsub,showlevel,levelsplitchar FROM mode_customtree a where id=" + str2;
/*  99 */     RecordSet recordSet = new RecordSet();
/* 100 */     recordSet.execute(str9);
/* 101 */     String str10 = "";
/* 102 */     String str11 = "";
/* 103 */     boolean bool = false;
/* 104 */     String str12 = "";
/* 105 */     if (recordSet.next()) {
/* 106 */       str11 = recordSet.getString("isonlyleaf");
/* 107 */       str10 = recordSet.getString("isselsub");
/* 108 */       bool = "1".equals(recordSet.getString("showlevel"));
/* 109 */       str12 = recordSet.getString("levelsplitchar");
/*     */     } 
/*     */     
/* 112 */     CustomTreeData customTreeData = new CustomTreeData();
/* 113 */     customTreeData.setUser(this.user);
/* 114 */     customTreeData.setIsSearch(bool1);
/* 115 */     customTreeData.setIsshowall(str3);
/* 116 */     customTreeData.setShowalllevel(bool);
/* 117 */     customTreeData.setLevelsplitchar(str12);
/* 118 */     String str13 = Util.null2String(paramMap.get("selectids"));
/* 119 */     if (!"".equals(str13)) {
/* 120 */       str13 = "," + str13 + ",";
/*     */     }
/* 122 */     String str14 = Util.null2String(paramMap.get("treerootnode"));
/*     */     
/* 124 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 125 */     boolean bool2 = true;
/*     */     try {
/* 127 */       String str15 = "_";
/* 128 */       List<TreeNode> list1 = new ArrayList();
/* 129 */       if (str7.equals("") || str7.equals("0")) {
/* 130 */         str7 = "0" + str15 + "0";
/*     */       }
/* 132 */       String[] arrayOfString = str7.split(str15, 2);
/* 133 */       String str16 = arrayOfString[0];
/* 134 */       String str17 = arrayOfString[1];
/* 135 */       String str18 = Util.null2String(paramMap.get("currenttime"));
/* 136 */       Map map1 = paramMap.containsKey("consearch") ? dealFormParams2(paramMap, str18, false) : dealFormParams(paramMap, str18);
/* 137 */       customTreeData.setAllField(map1);
/* 138 */       customTreeData.setIsshowview(str6);
/* 139 */       customTreeData.setTreerootnode(str14);
/* 140 */       customTreeData.setParams(paramMap);
/* 141 */       if (str8.equals("true")) {
/* 142 */         List list2 = customTreeData.getRootNodeData(str2);
/*     */       } else {
/* 144 */         list1 = customTreeData.getCurrentNodeData(str2, str16, str17);
/*     */       } 
/* 146 */       if (str8.equals("true") && list1.size() > 0 && "".equals(((TreeNode)list1.get(0)).getName())) {
/* 147 */         list1 = customTreeData.getCurrentNodeData(str2, str16, "0_0");
/* 148 */         str8 = "false";
/*     */       } 
/* 150 */       if ("1".equals(str10)) {
/* 151 */         bool2 = false;
/*     */       }
/* 153 */       for (byte b = 0; b < list1.size(); b++) {
/* 154 */         TreeNode treeNode = list1.get(b);
/* 155 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 156 */         browserTreeNode.setId(treeNode.getId());
/* 157 */         browserTreeNode.setName(treeNode.getName());
/* 158 */         browserTreeNode.setTitle(treeNode.getName());
/* 159 */         browserTreeNode.setLinkUrl(isReturnLinkUrl(treeNode.getId()) ? ("/formmode/search/CustomSearchOpenTree.jsp?pid=" + treeNode.getId()) : "");
/* 160 */         browserTreeNode.setIcon(treeNode.getIcon());
/* 161 */         browserTreeNode.setIsImgIcon(false);
/* 162 */         browserTreeNode.setOrgWholePathspan(treeNode.getOrgWholePathspan());
/* 163 */         if (!"".equals(treeNode.getIcon())) {
/* 164 */           browserTreeNode.setIsImgIcon(true);
/*     */         }
/* 166 */         if (str8.equals("true")) {
/* 167 */           browserTreeNode.setType("0");
/* 168 */           browserTreeNode.setPid("");
/* 169 */           browserTreeNode.setCanClick(false);
/*     */         } else {
/* 171 */           String str = "2";
/* 172 */           if ("true".equals(treeNode.getIsParent())) {
/* 173 */             str = "2";
/*     */           } else {
/* 175 */             str = "3";
/*     */           } 
/* 177 */           browserTreeNode.setType(str);
/* 178 */           browserTreeNode.setPid(str7);
/* 179 */           browserTreeNode.setCanClick(!("1".equals(str11) && "true".equals(treeNode.getIsParent())));
/*     */         } 
/* 181 */         browserTreeNode.setPid(str7);
/* 182 */         browserTreeNode.setIsParent("true".equals(treeNode.getIsParent()));
/* 183 */         boolean bool3 = false;
/* 184 */         if (str13.indexOf("," + treeNode.getId() + ",") != -1) {
/* 185 */           bool3 = true;
/*     */         }
/* 187 */         browserTreeNode.setSelected(bool3);
/* 188 */         browserTreeNode.setCheckStrictly(bool2);
/* 189 */         if (list.contains(browserTreeNode.getId()) && browserTreeNode.getCanClick() == true) {
/* 190 */           browserTreeNode.setCanClick(false);
/*     */         }
/* 192 */         arrayList.add(browserTreeNode);
/*     */       } 
/* 194 */     } catch (Exception exception) {
/* 195 */       this.formmodeLog.writeLog(exception);
/*     */     } 
/* 197 */     map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 198 */     map.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 199 */     map.put("checkStrictly", Boolean.valueOf(bool2));
/* 200 */     setIconSetting(map, str2);
/* 201 */     return map;
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean isReturnLinkUrl(String paramString) {
/* 206 */     boolean bool = false;
/* 207 */     String str1 = paramString.split("_", 2)[0];
/* 208 */     String str2 = paramString.split("_", 2)[1];
/* 209 */     String str3 = "select mainid from mode_customtreedetail where id=" + StringHelper.empty2Null(str1);
/* 210 */     int i = 0;
/* 211 */     CustomTreeUtil customTreeUtil = new CustomTreeUtil();
/* 212 */     RecordSet recordSet = new RecordSet();
/* 213 */     recordSet.executeSql(str3);
/* 214 */     while (recordSet.next()) {
/* 215 */       i = Util.getIntValue(recordSet.getString("mainid"), 0);
/*     */     }
/* 217 */     String str4 = customTreeUtil.getRelateHrefAddress(i, str1, str2);
/* 218 */     if (!str4.equals("")) {
/* 219 */       bool = true;
/*     */     } else {
/* 221 */       bool = false;
/*     */     } 
/* 223 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, Object> getTreeListData(Map<String, Object> paramMap) {
/* 228 */     String str1 = Util.null2String(paramMap.get("cube_treeid"));
/* 229 */     if (str1.indexOf("_searchType") > -1) {
/* 230 */       str1 = str1.split("_searchType")[0];
/*     */     }
/* 232 */     String str2 = Util.null2String(paramMap.get("searchbrowserid"));
/*     */     
/* 234 */     String str3 = Util.null2String(paramMap.get("name"));
/* 235 */     String str4 = Util.null2String(paramMap.get("src"));
/* 236 */     String str5 = Util.null2String(paramMap.get("excludeId"));
/* 237 */     String str6 = str2;
/*     */     
/* 239 */     int i = Util.getIntValue(Util.null2String(paramMap.get("pagesize")), 10);
/*     */     
/* 241 */     if (paramMap.containsKey("min") && paramMap.containsKey("max")) {
/* 242 */       int j = Util.getIntValue(Util.null2String(paramMap.get("min")), 0);
/* 243 */       int k = Util.getIntValue(Util.null2String(paramMap.get("max")), 0);
/* 244 */       if (j > 0 && k > j) {
/* 245 */         i = k - j + 1;
/*     */       }
/*     */     } 
/*     */     
/* 249 */     if (i < 1) i = 10;
/*     */ 
/*     */ 
/*     */     
/* 253 */     String str7 = "  a.id,a.showname,a.nodename,a.nodeid ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 263 */     String str8 = getSqlFrom(str1);
/*     */     
/* 265 */     String str9 = " from (" + str8 + ") a ";
/* 266 */     String str10 = " where 1=1 ";
/* 267 */     if (!str3.equals("")) {
/* 268 */       str10 = str10 + " and a.showname like '%" + str3 + "%'";
/*     */     }
/*     */     
/* 271 */     String str11 = "id";
/* 272 */     String str12 = "";
/* 273 */     if (str4.equalsIgnoreCase("dest")) {
/* 274 */       if ("".equals(str6)) {
/* 275 */         str6 = "0";
/*     */       }
/* 277 */       String[] arrayOfString = str6.split(",");
/* 278 */       String str = "";
/* 279 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 280 */         str = str + "'" + arrayOfString[b] + "'";
/* 281 */         if (b < arrayOfString.length - 1) {
/* 282 */           str = str + ",";
/*     */         }
/*     */       } 
/* 285 */       str12 = str12 + " and a.id in (" + str + ")";
/* 286 */     } else if (str4.equalsIgnoreCase("src")) {
/* 287 */       if (str5.length() == 0) str5 = str6; 
/* 288 */       if (str5.length() > 0) {
/* 289 */         String[] arrayOfString = str5.split(",");
/* 290 */         String str = "";
/* 291 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 292 */           str = str + "'" + arrayOfString[b] + "'";
/* 293 */           if (b < arrayOfString.length - 1) {
/* 294 */             str = str + ",";
/*     */           }
/*     */         } 
/* 297 */         str12 = str12 + " and a.id not in (" + str + ")";
/*     */       } 
/*     */     } 
/* 300 */     str10 = str10 + str12;
/* 301 */     String str13 = "nodeid,id";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 345 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 346 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 347 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 348 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "showname", "showname", 1)).setIsInputCol(BoolAttr.TRUE));
/* 349 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str9, str10, str13, str11, arrayList);
/* 350 */     String str14 = getDsnameWithTree(str1);
/* 351 */     if (!StringHelper.isEmpty(str14)) {
/* 352 */       splitTableBean.setPoolname(str14);
/*     */     }
/* 354 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 355 */     setIconSetting((Map)hashMap, str1);
/* 356 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDsnameWithTree(String paramString) {
/* 368 */     String str1 = "select * from mode_customtreedetail where mainid=" + paramString + " order by showorder";
/* 369 */     RecordSet recordSet = new RecordSet();
/* 370 */     recordSet.execute(str1);
/* 371 */     String str2 = "";
/*     */     
/* 373 */     CustomTreeData customTreeData = new CustomTreeData();
/* 374 */     if (recordSet.next()) {
/* 375 */       String str = recordSet.getString("id");
/* 376 */       str2 = customTreeData.getVdatasourceByNodeId(str);
/*     */     } 
/* 378 */     if (StringHelper.isEmpty(str2) || "$ECOLOGY_SYS_LOCAL_POOLNAME".equals(str2)) {
/* 379 */       str2 = "";
/*     */     }
/* 381 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 388 */     String str1 = Util.null2String(paramMap.get("treeid"));
/* 389 */     RecordSet recordSet = new RecordSet();
/* 390 */     String str2 = "select isshowsearchtab,searchbrowserid from mode_customtree where id =?";
/* 391 */     int i = 0;
/* 392 */     int j = 0;
/* 393 */     recordSet.executeQuery(str2, new Object[] { str1 });
/* 394 */     if (recordSet.next()) {
/* 395 */       i = Util.getIntValue(recordSet.getString("isshowsearchtab"), 0);
/* 396 */       j = Util.getIntValue(recordSet.getString("searchbrowserid"), 0);
/*     */     } 
/* 398 */     if (i > 0 && j > 0) {
/* 399 */       this.formmodeBrowserService.setUser(this.user);
/* 400 */       str2 = "select showname from MODE_BROWSER where customid=?";
/* 401 */       String str = "";
/* 402 */       recordSet.executeQuery(str2, new Object[] { Integer.valueOf(j) });
/* 403 */       if (recordSet.next()) {
/* 404 */         str = Util.null2String(recordSet.getString("showname"));
/*     */       }
/* 406 */       paramMap.put("type", str);
/*     */       try {
/* 408 */         return this.formmodeBrowserService.getBrowserConditionInfo(paramMap);
/* 409 */       } catch (Exception exception) {
/* 410 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 413 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 414 */     if (i > 0 && j <= 0) {
/* 415 */       ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 416 */       hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 417 */       ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 418 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30828, "name", true));
/*     */     } 
/* 420 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*     */     Map<String, Object> map;
/* 425 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 426 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 427 */     String str2 = Util.null2String(paramMap.get("cube_treeid"));
/* 428 */     String str3 = Util.null2String(paramMap.get("isshowsearchtab"));
/* 429 */     String str4 = Util.null2String(paramMap.get("searchbrowserid"));
/* 430 */     String str5 = Util.null2String(paramMap.get("browsertype"));
/* 431 */     RecordSet recordSet1 = new RecordSet();
/* 432 */     boolean bool = false;
/* 433 */     recordSet1.executeQuery("select isonlyleaf from mode_customtree where id=?", new Object[] { str2 });
/* 434 */     if (recordSet1.next() && 
/* 435 */       recordSet1.getInt("isonlyleaf") == 1) {
/* 436 */       bool = true;
/*     */     }
/*     */     
/* 439 */     Map<String, Boolean> map1 = isParentnodeId(str1, str2);
/* 440 */     if ("".equals(str1)) {
/* 441 */       return (Map)hashMap;
/*     */     }
/* 443 */     if (!"0".equals(str3) && !"".equals(str4) && "".equals(str5)) {
/* 444 */       String str = "select showname from MODE_BROWSER where customid=?";
/* 445 */       RecordSet recordSet = new RecordSet();
/* 446 */       recordSet.executeQuery(str, new Object[] { str4 });
/* 447 */       if (recordSet.next()) {
/* 448 */         str5 = Util.null2String(recordSet.getString("showname"));
/*     */       }
/*     */     } 
/* 451 */     if (!"".equals(str5) && !"0".equals(str4) && !"".equals(str4)) {
/* 452 */       this.formmodeBrowserService.setUser(this.user);
/* 453 */       String str10 = "select id from mode_customtreedetail where mainid=?";
/* 454 */       RecordSet recordSet = new RecordSet();
/* 455 */       recordSet.executeQuery(str10, new Object[] { str2 });
/* 456 */       if (recordSet.next()) {
/* 457 */         str2 = Util.null2String(recordSet.getString("id"));
/*     */       }
/* 459 */       String str11 = str1;
/* 460 */       str1 = str1.replace(",0_0", "");
/* 461 */       if (!"".equals(str2)) {
/* 462 */         str1 = str1.replaceAll(str2 + "_", "");
/*     */       }
/* 464 */       paramMap.put("type", str5);
/* 465 */       paramMap.put("treeid", str2);
/* 466 */       paramMap.put(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS, str1);
/*     */       try {
/* 468 */         map = this.formmodeBrowserService.getMultBrowserDestData(paramMap);
/* 469 */         ArrayList<Map> arrayList2 = new ArrayList();
/*     */         
/* 471 */         List list = (List)map.get(BrowserConstant.BROWSER_RESULT_DATA);
/* 472 */         for (String str : str11.split(",")) {
/* 473 */           boolean bool2 = ((Boolean)map1.get(str)).booleanValue();
/* 474 */           if (bool) {
/* 475 */             if (!bool2) {
/* 476 */               for (Map map2 : list) {
/* 477 */                 for (Map.Entry entry : map2.entrySet()) {
/* 478 */                   String str12 = (String)entry.getKey();
/* 479 */                   String str13 = (String)entry.getValue();
/* 480 */                   if (str12.equals(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME) && str13
/* 481 */                     .equals(str)) {
/* 482 */                     arrayList2.add(map2);
/*     */                   }
/* 484 */                   if (!str13.equals("") && str13.indexOf("</a>") > 0) {
/* 485 */                     entry.setValue(((String)entry.getValue()).replaceAll("<a.*?>", "").replaceAll("</a>", ""));
/*     */                   }
/*     */                 } 
/*     */               } 
/*     */             }
/*     */           } else {
/* 491 */             for (Map map2 : list) {
/* 492 */               for (Map.Entry entry : map2.entrySet()) {
/* 493 */                 String str12 = (String)entry.getKey();
/* 494 */                 String str13 = (String)entry.getValue();
/* 495 */                 if (str12.equals(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME) && str13
/* 496 */                   .equals(str)) {
/* 497 */                   arrayList2.add(map2);
/*     */                 }
/* 499 */                 if (!str13.equals("") && str13.indexOf("</a>") > 0) {
/* 500 */                   entry.setValue(((String)entry.getValue()).replaceAll("<a.*?>", "").replaceAll("</a>", ""));
/*     */                 }
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 507 */         map.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList2);
/* 508 */         return map;
/* 509 */       } catch (Exception exception) {
/* 510 */         writeLog(exception);
/* 511 */         return map;
/*     */       } 
/*     */     } 
/* 514 */     String str6 = "";
/* 515 */     String[] arrayOfString = Util.splitString(str1, ",");
/* 516 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 517 */       str6 = str6 + "'" + arrayOfString[b] + "'";
/* 518 */       if (b < arrayOfString.length - 1) {
/* 519 */         str6 = str6 + ",";
/*     */       }
/*     */     } 
/* 522 */     String str7 = getSqlFrom(str2);
/* 523 */     String str8 = " where a.id in (" + str6 + ")";
/* 524 */     RecordSet recordSet2 = new RecordSet();
/* 525 */     CustomTreeComInfo customTreeComInfo = new CustomTreeComInfo();
/* 526 */     boolean bool1 = "1".equals(customTreeComInfo.get(CustomTreeComInfo.showlevel, str2));
/* 527 */     CustomTreeUtil customTreeUtil = new CustomTreeUtil(bool1, customTreeComInfo.get(CustomTreeComInfo.levelsplitchar, str2));
/* 528 */     String str9 = getDsnameWithTree(str2);
/* 529 */     if (!StringHelper.isEmpty(str9)) {
/* 530 */       recordSet2.executeSql("select a.id,a.showname,a.nodename,a.nodeid from (" + str7 + ") a " + str8, str9);
/*     */     } else {
/* 532 */       recordSet2.executeSql("select a.id,a.showname,a.nodename,a.nodeid from (" + str7 + ") a " + str8);
/*     */     } 
/* 534 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 535 */     while (recordSet2.next()) {
/* 536 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 537 */       String str10 = recordSet2.getString(1);
/* 538 */       String str11 = CubeCipherUitl.decrypt(recordSet2.getString(2));
/* 539 */       if (bool) {
/* 540 */         boolean bool2 = ((Boolean)map1.get(recordSet2.getString(1))).booleanValue();
/* 541 */         if (!bool2) {
/* 542 */           hashMap1.put("id", str10);
/* 543 */           hashMap1.put("showname", str11);
/* 544 */           if (bool1) {
/* 545 */             String str = customTreeUtil.getAllLevelName(str10, str11);
/* 546 */             hashMap1.put("orgWholePathspan", str);
/*     */           } 
/*     */         } 
/*     */       } else {
/* 550 */         hashMap1.put("id", str10);
/* 551 */         hashMap1.put("showname", str11);
/* 552 */         if (bool1) {
/* 553 */           String str = customTreeUtil.getAllLevelName(str10, str11);
/* 554 */           hashMap1.put("orgWholePathspan", str);
/*     */         } 
/*     */       } 
/* 557 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 560 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 561 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 562 */     arrayList1.add(new ListHeadBean("showname", "", 1, BoolAttr.TRUE));
/*     */     
/* 564 */     map.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 565 */     map.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 566 */     map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 567 */     return map;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Boolean> isParentnodeId(String paramString1, String paramString2) {
/* 572 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 573 */     String[] arrayOfString = paramString1.split(",");
/*     */     
/* 575 */     CustomTreeData customTreeData = new CustomTreeData();
/* 576 */     customTreeData.setUser(this.user);
/* 577 */     ArrayList arrayList = new ArrayList();
/* 578 */     for (String str1 : arrayOfString) {
/* 579 */       String[] arrayOfString1 = str1.split("_");
/* 580 */       String str2 = arrayOfString1[0];
/* 581 */       String str3 = arrayOfString1[1];
/* 582 */       List list = customTreeData.getCurrentNodeData(paramString2, str2, str3);
/* 583 */       if (list.size() == 0) {
/* 584 */         hashMap.put(str1, Boolean.valueOf(false));
/*     */       } else {
/* 586 */         hashMap.put(str1, Boolean.valueOf(true));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 598 */     return (Map)hashMap;
/*     */   }
/*     */   private String getSqlFrom(String paramString) {
/* 601 */     RecordSet recordSet1 = new RecordSet();
/* 602 */     String str1 = "select * from mode_customtreedetail where mainid=" + paramString + " order by showorder";
/* 603 */     recordSet1.execute(str1);
/*     */     
/* 605 */     CustomTreeData customTreeData = new CustomTreeData();
/* 606 */     String str2 = "";
/* 607 */     String str3 = "";
/* 608 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 610 */     if (recordSet1.next()) {
/*     */ 
/*     */       
/* 613 */       String str = getDsnameWithTree(paramString);
/* 614 */       if (str.equals("")) {
/* 615 */         str2 = recordSet2.getDBType();
/*     */       } else {
/* 617 */         str3 = str;
/* 618 */         str2 = recordSet2.getDBTypeByPoolName(str3);
/*     */       } 
/*     */     } 
/*     */     
/* 622 */     String str4 = "";
/* 623 */     if (str2.equalsIgnoreCase("sqlserver")) {
/* 624 */       str4 = "+";
/*     */     } else {
/* 626 */       str4 = "||";
/*     */     } 
/*     */     
/* 629 */     String str5 = "";
/* 630 */     recordSet1.beforFirst();
/* 631 */     while (recordSet1.next()) {
/* 632 */       String str6 = recordSet1.getString("tablekey");
/* 633 */       String str7 = recordSet1.getString("showfield");
/* 634 */       String str8 = recordSet1.getString("tablename");
/* 635 */       String str9 = Util.null2String(recordSet1.getString("sourcefrom"));
/* 636 */       String str10 = Util.null2String(recordSet1.getString("sourceid"));
/* 637 */       str8 = VSqlUtil.getRealTableForTree(str9, str10, str8);
/* 638 */       String str11 = recordSet1.getString("id");
/* 639 */       String str12 = recordSet1.getString("nodename");
/* 640 */       String str13 = recordSet1.getString("datacondition");
/*     */       
/* 642 */       String str14 = str6;
/* 643 */       if (str2.equalsIgnoreCase("sqlserver")) {
/* 644 */         str14 = "convert(varchar(4000)," + str6 + ")";
/* 645 */       } else if (str2.equalsIgnoreCase("mysql")) {
/* 646 */         str14 = "convert(" + str6 + ",CHAR)";
/* 647 */       } else if (str2.equalsIgnoreCase("postgresql")) {
/* 648 */         str14 = "cast(" + str6 + " as varchar)";
/*     */       } 
/* 650 */       String str15 = "select '" + str11 + "_'" + str4 + str14 + " as id," + str6 + " as objid," + str7 + " as showname,'" + str12 + "' as nodename, '" + str11 + "' as nodeid from " + str8;
/* 651 */       if ("mysql".equals(str2)) {
/* 652 */         str15 = "select concat('" + str11 + "_'," + str14 + ") as id," + str6 + " as objid," + str7 + " as showname,'" + str12 + "' as nodename, '" + str11 + "' as nodeid from " + str8;
/* 653 */       } else if (str2.equalsIgnoreCase("oracle")) {
/* 654 */         str15 = "select '" + str11 + "_'" + str4 + str14 + " as id," + str6 + "||'' as objid," + str7 + " as showname,'" + str12 + "' as nodename, '" + str11 + "' as nodeid from " + str8;
/* 655 */       } else if (str2.equalsIgnoreCase("postgresql")) {
/* 656 */         str15 = "select '" + str11 + "_'" + str4 + str14 + " as id,cast(" + str6 + " as varchar) as objid," + str7 + " as showname,'" + str12 + "' as nodename, '" + str11 + "' as nodeid from " + str8;
/*     */       } 
/*     */ 
/*     */       
/* 660 */       if (!StringHelper.isEmpty(str13)) {
/* 661 */         customTreeData.setUser(this.user);
/* 662 */         str13 = customTreeData.replaceParam(str13);
/* 663 */         if (str13.indexOf("$") > -1) {
/* 664 */           int i = str13.indexOf("$");
/* 665 */           int j = str13.indexOf("$", i + 1);
/* 666 */           if (j > -1) {
/* 667 */             str13 = " 1=1 ";
/*     */           }
/*     */         } 
/* 670 */         str15 = str15 + " where 1=1 and (" + str13 + ")";
/*     */       } 
/* 672 */       if (str5.equals("")) {
/* 673 */         str5 = str15; continue;
/*     */       } 
/* 675 */       str5 = str5 + " union all " + str15;
/*     */     } 
/*     */     
/* 678 */     return str5;
/*     */   }
/*     */   public Map dealFormParams2(Map<String, Object> paramMap, String paramString, boolean paramBoolean) {
/* 681 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 682 */     if (null != paramMap && paramMap.size() > 0) {
/* 683 */       StringBuffer stringBuffer = new StringBuffer();
/* 684 */       Set<String> set = paramMap.keySet();
/* 685 */       for (String str : set) {
/*     */         
/* 687 */         String[] arrayOfString = str.split("_");
/*     */         
/* 689 */         if (arrayOfString.length > 1 && str.startsWith("con_")) {
/* 690 */           int i = str.lastIndexOf("_");
/* 691 */           String str1 = arrayOfString[arrayOfString.length - 1];
/* 692 */           stringBuffer.append(str1);
/* 693 */           stringBuffer.append(",");
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 698 */       if (stringBuffer.length() > 0) {
/* 699 */         String str = "select t.id,t.fieldname,t.fieldhtmltype,t.type,t.viewtype,t.detailtable from workflow_billfield t where t.id in(" + stringBuffer.deleteCharAt(stringBuffer.length() - 1) + ")";
/* 700 */         RecordSet recordSet = new RecordSet();
/* 701 */         recordSet.executeQuery(str, new Object[0]);
/* 702 */         while (recordSet.next()) {
/* 703 */           String str1 = recordSet.getString("fieldname");
/* 704 */           String str2 = recordSet.getString("fieldhtmltype");
/* 705 */           String str3 = recordSet.getString("type");
/* 706 */           String str4 = recordSet.getString("viewtype");
/* 707 */           String str5 = Util.null2String(paramMap.get("con_" + recordSet.getString("id")));
/* 708 */           str5 = returnSpecialChar(str5);
/* 709 */           if ("1".equals(str2) && !"1".equals(str3) && 
/* 710 */             str5.trim().equals(",")) {
/* 711 */             str5 = "";
/*     */           }
/*     */           
/* 714 */           if ("1".equals(str4)) {
/* 715 */             hashMap.put("$" + Util.null2String(recordSet.getString("detailtable")) + "_" + str1 + "$", str5); continue;
/*     */           } 
/* 717 */           hashMap.put("$" + str1 + "$", str5);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 722 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map dealFormParams(Map<String, Object> paramMap, String paramString) {
/* 731 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 732 */     if (null != paramMap && paramMap.size() > 0) {
/* 733 */       Set<String> set = paramMap.keySet();
/* 734 */       for (String str : set) {
/*     */         
/* 736 */         int i = str.indexOf("_" + paramString);
/* 737 */         if (i != -1) {
/* 738 */           String str1 = str.substring(0, i);
/* 739 */           String str2 = Util.null2String(paramMap.get(str));
/* 740 */           str2 = returnSpecialChar(str2);
/* 741 */           if ("".equals(str2)) {
/* 742 */             str2 = "''";
/*     */           }
/* 744 */           hashMap.put("$" + str1 + "$", str2);
/*     */         } 
/*     */       } 
/*     */     } 
/* 748 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String returnSpecialChar(String paramString) {
/* 758 */     paramString = paramString.replaceAll("@#add#@", "+");
/* 759 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setIconSetting(Map<String, Object> paramMap, String paramString) {
/* 767 */     RecordSet recordSet = new RecordSet();
/* 768 */     String str1 = "select id,icon,iconColor,iconBg from modetreefield where id in (select appid from mode_customtree where id=?)";
/* 769 */     recordSet.executeQuery(str1, new Object[] { paramString });
/* 770 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 771 */     String str2 = "";
/* 772 */     String str3 = "";
/* 773 */     String str4 = "";
/* 774 */     if (recordSet.next()) {
/* 775 */       str2 = Util.null2String(recordSet.getString("icon"));
/* 776 */       str3 = Util.null2String(recordSet.getString("iconColor"));
/* 777 */       str4 = Util.null2String(recordSet.getString("iconBg"));
/*     */     } 
/* 779 */     hashMap.put("icon", "".equals(str2) ? "icon-coms-ModelingEngine" : str2);
/* 780 */     hashMap.put("fontColor", "".equals(str3) ? "#fff" : str3);
/* 781 */     hashMap.put("bgColor", "".equals(str4) ? "#96358a" : str4);
/* 782 */     paramMap.put("iconSetting", hashMap);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FormmodeTreeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */