/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContractTypeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"contractdesc\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*    */   
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 52 */     String str1 = Util.null2String(paramMap.get("fullname"));
/* 53 */     String str2 = Util.null2String(paramMap.get("description"));
/* 54 */     String str3 = " ";
/* 55 */     if (!str1.equals("")) {
/* 56 */       str3 = str3 + " and name like '%";
/* 57 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 58 */       str3 = str3 + "%'";
/*    */     } 
/* 60 */     if (!str2.equals("")) {
/* 61 */       str3 = str3 + " and contractdesc like '%";
/* 62 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 63 */       str3 = str3 + "%'";
/*    */     } 
/*    */     
/* 66 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*    */     
/* 68 */     String str4 = " id , name ,contractdesc ";
/* 69 */     String str5 = "id";
/* 70 */     String str6 = "CRM_ContractType";
/*    */     
/* 72 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 73 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 74 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 75 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "contractdesc", "contractdesc"));
/*    */     
/* 77 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str6, str3, str5, "id", arrayList);
/* 78 */     splitTableBean.setSqlsortway("ASC");
/*    */     
/*    */     try {
/* 81 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 82 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"contractdesc\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 83 */     } catch (Exception exception) {
/* 84 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 87 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 88 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 93 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 94 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 95 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 96 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname", true));
/* 97 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 98 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 99 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ContractTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */