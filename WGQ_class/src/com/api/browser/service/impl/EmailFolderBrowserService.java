/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.DBUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.SplitPageParaBean;
/*     */ import weaver.general.SplitPageUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EmailFolderBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  43 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*  44 */     int i = 0;
/*     */     
/*     */     try {
/*  47 */       String str1 = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("q")), "UTF-8");
/*  48 */       String str2 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageSize")), "10");
/*  49 */       String str3 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageNum")), "1");
/*  50 */       String str4 = Util.null2String(paramHttpServletRequest.getParameter("menuType"));
/*     */       
/*  52 */       if (!str1.isEmpty()) {
/*  53 */         String str5 = SystemEnv.getHtmlLabelName(19816, this.user.getLanguage());
/*  54 */         String str6 = SystemEnv.getHtmlLabelName(2040, this.user.getLanguage());
/*  55 */         String str7 = SystemEnv.getHtmlLabelName(2039, this.user.getLanguage());
/*  56 */         String str8 = SystemEnv.getHtmlLabelName(19558, this.user.getLanguage());
/*     */         
/*  58 */         RecordSet recordSet = new RecordSet();
/*  59 */         String str9 = " ";
/*     */         
/*  61 */         if ("oracle".equals(recordSet.getDBType())) {
/*     */ 
/*     */           
/*  64 */           str9 = "(SELECT 0 id ,-3 orderid, '" + str5 + "' name from dual UNION SELECT -1,-2 orderid,'" + str8 + "' from dual UNION SELECT -2,-1 orderid, '" + str7 + "' from dual  UNION SELECT -3,0 orderid,'" + str6 + "' from dual    UNION SELECT id ,id as orderid, folderName as name  FROM MailInboxFolder WHERE userId = " + this.user.getUID() + " ) t";
/*     */         }
/*     */         else {
/*     */           
/*  68 */           str9 = "(SELECT 0 AS id ,-3 as orderid, '" + str5 + "' AS name  UNION SELECT -1,-2 as orderid,'" + str8 + "' UNION SELECT -2,-1 as orderid,'" + str7 + "'  UNION SELECT -3,0 as orderid,  '" + str6 + "'  UNION SELECT id,id as orderid , folderName as name   FROM MailInboxFolder WHERE userId = " + this.user.getUID() + " ) t";
/*     */         } 
/*  70 */         String str10 = " name like '%" + str1 + "%' ";
/*     */         
/*  72 */         SplitPageParaBean splitPageParaBean = new SplitPageParaBean();
/*  73 */         SplitPageUtil splitPageUtil = new SplitPageUtil();
/*  74 */         splitPageParaBean.setSqlFrom(str9);
/*  75 */         splitPageParaBean.setBackFields("id,name");
/*  76 */         splitPageParaBean.setPrimaryKey("id");
/*     */         
/*  78 */         splitPageParaBean.getClass(); splitPageParaBean.setSortWay(0);
/*  79 */         splitPageParaBean.setSqlWhere(str10);
/*  80 */         splitPageUtil.setSpp(splitPageParaBean);
/*  81 */         recordSet = splitPageUtil.getCurrentPageRs(Integer.valueOf(str3).intValue(), Integer.valueOf(str2).intValue());
/*  82 */         i = splitPageUtil.getRecordCount();
/*     */         
/*  84 */         HashSet<String> hashSet = new HashSet();
/*  85 */         while (recordSet.next()) {
/*  86 */           String str = Util.null2String(recordSet.getString("id"));
/*  87 */           if (StringUtils.isNotEmpty(str) && !hashSet.contains(str)) {
/*  88 */             hashSet.add(str);
/*  89 */             arrayList.add(getSearchObj(str, recordSet.getString("name")));
/*     */           } 
/*     */         } 
/*     */       } 
/*  93 */     } catch (Exception exception) {
/*  94 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  97 */     hashMap.put("count", Integer.valueOf(i));
/*  98 */     hashMap.put("datas", arrayList);
/*     */     
/* 100 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private JSONObject getSearchObj(String paramString1, String paramString2) {
/* 104 */     JSONObject jSONObject = new JSONObject();
/* 105 */     jSONObject.put("id", paramString1);
/* 106 */     jSONObject.put("name", paramString2);
/* 107 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 120 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 121 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 123 */     String str1 = Util.null2String(paramMap.get("name"));
/* 124 */     String str2 = Util.null2String(paramMap.get("menuType"));
/*     */     
/* 126 */     String str3 = SystemEnv.getHtmlLabelName(19816, this.user.getLanguage());
/* 127 */     String str4 = SystemEnv.getHtmlLabelName(2040, this.user.getLanguage());
/* 128 */     String str5 = SystemEnv.getHtmlLabelName(2039, this.user.getLanguage());
/* 129 */     String str6 = SystemEnv.getHtmlLabelName(19558, this.user.getLanguage());
/*     */     
/* 131 */     String str7 = " 1=1 ";
/*     */     
/* 133 */     if (!str1.equals("")) {
/* 134 */       str7 = str7 + " and name like '%" + str1 + "%'";
/*     */     }
/*     */     
/* 137 */     String str8 = "  id ,  name";
/* 138 */     String str9 = " ( SELECT id , folderName as name FROM MailInboxFolder WHERE userId = " + this.user.getUID() + " ) t";
/*     */     
/* 140 */     if ("oracle".equals(recordSet.getDBType())) {
/*     */ 
/*     */       
/* 143 */       str9 = "(SELECT 0 id ,-3 orderid, '" + str3 + "' name from dual UNION SELECT -1,-2 orderid,'" + str6 + "' from dual UNION SELECT -2,-1 orderid, '" + str5 + "' from dual  UNION SELECT -3,0 orderid,'" + str4 + "' from dual    UNION SELECT id ,id as orderid, folderName as name  FROM MailInboxFolder WHERE userId = " + this.user.getUID() + " ) t";
/*     */     }
/*     */     else {
/*     */       
/* 147 */       str9 = "(SELECT 0 AS id ,-3 as orderid, '" + str3 + "' AS name  UNION SELECT -1,-2 as orderid,'" + str6 + "' UNION SELECT -2,-1 as orderid,'" + str5 + "'  UNION SELECT -3,0 as orderid,  '" + str4 + "'  UNION SELECT id ,id as orderid, folderName as name   FROM MailInboxFolder WHERE userId = " + this.user.getUID() + " ) t";
/*     */     } 
/* 149 */     String str10 = "orderid";
/*     */     
/* 151 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 152 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 153 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(517746, this.user.getLanguage()), "name", "name"));
/*     */     
/* 155 */     SplitTableBean splitTableBean = new SplitTableBean(str8, str9, str7, str10, "id", "asc", arrayList);
/* 156 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 157 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 162 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 163 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 164 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 165 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 526358, "name", true));
/* 166 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 167 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 173 */     String str = Util.null2String(paramMap.get("selectids"));
/*     */     
/* 175 */     if (!"".equals(str)) {
/* 176 */       String str1 = SystemEnv.getHtmlLabelName(19816, this.user.getLanguage());
/* 177 */       String str2 = SystemEnv.getHtmlLabelName(2040, this.user.getLanguage());
/* 178 */       String str3 = SystemEnv.getHtmlLabelName(2039, this.user.getLanguage());
/* 179 */       String str4 = SystemEnv.getHtmlLabelName(19558, this.user.getLanguage());
/*     */       
/* 181 */       RecordSet recordSet = new RecordSet();
/* 182 */       ArrayList arrayList = new ArrayList();
/* 183 */       String str5 = "";
/* 184 */       if ("oracle".equals(recordSet.getDBType())) {
/*     */ 
/*     */         
/* 187 */         str5 = "(SELECT 0 id ,-3 orderid, '" + str1 + "' name from dual UNION SELECT -1,-2 orderid,'" + str4 + "' from dual UNION SELECT -2,-1 orderid, '" + str3 + "' from dual  UNION SELECT -3,0 orderid,'" + str2 + "' from dual    UNION SELECT id ,id as orderid, folderName as name  FROM MailInboxFolder WHERE userId = " + this.user.getUID() + " ) t";
/*     */       }
/*     */       else {
/*     */         
/* 191 */         str5 = "(SELECT 0 AS id ,-3 as orderid, '" + str1 + "' AS name  UNION SELECT -1,-2 as orderid,'" + str4 + "' UNION SELECT -2,-1 as orderid,'" + str3 + "'  UNION SELECT -3,0 as orderid,  '" + str2 + "'  UNION SELECT id ,id as orderid , folderName as name  FROM MailInboxFolder WHERE userId = " + this.user.getUID() + " ) t";
/*     */       } 
/* 193 */       recordSet.executeQuery("select  id , name ,orderid from " + str5 + " where  id in (" + DBUtil.getParamReplace(str) + ") order by orderid asc", new Object[] { DBUtil.trasToList(arrayList, new Object[] { str }) });
/* 194 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 195 */       while (recordSet.next()) {
/* 196 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 197 */         hashMap1.put("id", recordSet.getString("id"));
/* 198 */         hashMap1.put("idspan", hashMap1.get("id"));
/* 199 */         hashMap1.put("name", recordSet.getString("name"));
/* 200 */         hashMap1.put("namespan", hashMap1.get("name"));
/* 201 */         hashMap1.put("randomFieldId", recordSet.getString("id"));
/* 202 */         hashMap1.put("randomFieldIdspan", "");
/* 203 */         arrayList1.add(hashMap1);
/*     */       } 
/* 205 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 206 */       hashMap.put("datas", arrayList1);
/* 207 */       return (Map)hashMap;
/*     */     } 
/* 209 */     return new HashMap<>();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EmailFolderBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */