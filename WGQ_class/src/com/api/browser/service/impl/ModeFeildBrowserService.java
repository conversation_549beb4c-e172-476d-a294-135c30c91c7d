/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.formmode.cache.ModeComInfo;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModeFeildBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */     String str1 = Util.null2String(paramMap.get("fieldHtmlType"));
/* 29 */     String str2 = Util.null2String(paramMap.get("fieldType"));
/* 30 */     String str3 = Util.null2String(paramMap.get("formId"));
/* 31 */     String str4 = Util.null2String(paramMap.get("modeId"));
/* 32 */     if (str3 == null || "".equals(str3)) {
/* 33 */       ModeComInfo modeComInfo = new ModeComInfo();
/* 34 */       str3 = modeComInfo.getFormId(str4);
/*    */     } 
/* 36 */     if (str3 == null || "".equals(str3)) {
/* 37 */       return (Map)hashMap;
/*    */     }
/* 39 */     String str5 = " where 1 = 1 and t1.billid = " + str3;
/* 40 */     String str6 = " t1.id,t1.fieldname,t2.labelname  ";
/* 41 */     String str7 = " workflow_billfield t1 left join htmllabelinfo t2 on t1.fieldlabel = t2.indexid and t2.languageid = " + this.user.getLanguage();
/* 42 */     if (str1 != null && !"".equals(str1)) {
/* 43 */       str5 = str5 + " and t1.fieldHtmlType =" + str1;
/*    */     }
/* 45 */     if (str2 != null && !"".equals(str2)) {
/* 46 */       str5 = str5 + " and t1.type =" + str2;
/*    */     }
/* 48 */     String str8 = " t1.dsporder,t1.id ";
/*    */     
/* 50 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 51 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 52 */     arrayList.add((new SplitTableColBean("50%", "" + SystemEnv.getHtmlLabelName(685, ThreadVarLanguage.getLang()) + "", "labelname", "labelname")).setIsInputCol(BoolAttr.TRUE));
/* 53 */     arrayList.add(new SplitTableColBean("50%", "" + SystemEnv.getHtmlLabelName(15026, ThreadVarLanguage.getLang()) + "", "fieldname", "fieldname"));
/*    */ 
/*    */     
/* 56 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str5, str8, "t1.id", arrayList);
/* 57 */     splitTableBean.setSqlsortway("ASC");
/* 58 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 59 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeFeildBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */