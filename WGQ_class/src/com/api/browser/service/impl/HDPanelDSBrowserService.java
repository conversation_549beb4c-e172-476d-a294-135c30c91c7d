/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.general.Util;
/*     */ import weaver.general.browserData.BrowserManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HDPanelDSBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  36 */     String str1 = Util.null2String(paramMap.get("name"));
/*  37 */     String str2 = Util.null2String(paramMap.get("remark"));
/*  38 */     String str3 = " where 1 = 1 ";
/*  39 */     String str4 = Util.null2String(SystemEnv.getHtmlLabelName(2211, this.user.getLanguage()));
/*  40 */     String str5 = Util.null2String(SystemEnv.getHtmlLabelName(504532, this.user.getLanguage()));
/*  41 */     if (!"".equals(str1)) {
/*  42 */       str3 = str3 + " and ((a.name like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' and a.issystem<>1 ) or ('" + str4 + "' like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' and a.issystem=1)) ";
/*     */     }
/*  44 */     if (!"".equals(str2)) {
/*  45 */       str3 = str3 + " and ((a.remark like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' and a.issystem<>1 ) or ('" + str5 + "' like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' and a.issystem=1)) ";
/*     */     }
/*     */ 
/*     */     
/*  49 */     String str6 = "a.id,a.name,a.remark,a.issystem";
/*  50 */     String str7 = "DP_Source a ";
/*  51 */     String str8 = "a.id ";
/*     */     
/*  53 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  54 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  55 */     SplitTableColBean splitTableColBean = new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", "com.engine.portal.cmd.hdpanelelement.util.HDPanelUtil.getHDPanelDSName", "column:issystem+" + this.user.getLanguage());
/*  56 */     splitTableColBean.setIsInputCol(BoolAttr.TRUE);
/*  57 */     arrayList.add(splitTableColBean);
/*  58 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(85, this.user.getLanguage()), "remark", "", "com.engine.portal.cmd.hdpanelelement.util.HDPanelUtil.getHDPanelDSName", "column:issystem+" + this.user.getLanguage()));
/*     */     
/*  60 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str3, str8, "a.id", arrayList);
/*  61 */     splitTableBean.setSqlsortway("ASC");
/*  62 */     splitTableBean.setSqlisdistinct("false");
/*  63 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  65 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  77 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  78 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  79 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  80 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "195", "name", true));
/*  81 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "85", "remark"));
/*  82 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  94 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  96 */     String str1 = "DP_Source a";
/*     */     
/*  98 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  99 */     String str3 = Util.null2String(SystemEnv.getHtmlLabelName(2211, this.user.getLanguage()));
/* 100 */     String str4 = "1=2";
/* 101 */     if (!"".equals(str2)) {
/* 102 */       str4 = str4 + " or ((a.name like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' and a.issystem<>1 ) or ('" + str3 + "' like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' and a.issystem=1)) ";
/*     */     }
/*     */     
/* 105 */     BrowserManager browserManager = new BrowserManager();
/* 106 */     browserManager.setType("HDPanelDS");
/* 107 */     browserManager.setOrderKey("id");
/* 108 */     String str5 = browserManager.getResult(paramHttpServletRequest, "id,name,issystem", str1, str4, 30);
/* 109 */     JSONArray jSONArray = JSONArray.fromObject(str5);
/* 110 */     if (null != jSONArray)
/*     */     {
/* 112 */       for (byte b = 0; b < jSONArray.size(); b++) {
/* 113 */         JSONObject jSONObject = (JSONObject)jSONArray.get(b);
/* 114 */         if (null != jSONObject && "1".equals(jSONObject.getString("issystem")) && "2211".equals(jSONObject.getString("name"))) {
/* 115 */           jSONObject.put("name", str3);
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     }
/* 120 */     str5 = jSONArray.toString();
/* 121 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, str5);
/* 122 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HDPanelDSBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */