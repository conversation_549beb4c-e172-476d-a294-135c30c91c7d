/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.BrowserTreeNode;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AlbumBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 26 */     String str1 = Util.null2s(Util.null2String(paramMap.get("id")), "0");
/*    */     
/* 28 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*    */     
/* 30 */     RecordSet recordSet = new RecordSet();
/*    */ 
/*    */     
/* 33 */     String str2 = "select * from hrmsubcompany where  (canceled=0 or canceled is null) and supsubcomid=" + str1 + "   order by supsubcomid asc,id asc";
/* 34 */     recordSet.execute(str2);
/* 35 */     while (recordSet.next()) {
/* 36 */       String str3 = recordSet.getString("id");
/* 37 */       int i = Util.getIntValue(recordSet.getString("supsubcomid"), 0);
/* 38 */       if (!("" + i).equals(str1)) {
/*    */         continue;
/*    */       }
/* 41 */       String str4 = recordSet.getString("subcompanyname");
/* 42 */       boolean bool = hasChild(str3, "1");
/* 43 */       arrayList.add(new BrowserTreeNode(str3, str4, i + "", bool, true));
/*    */     } 
/*    */     
/* 46 */     str2 = "select * from  AlbumPhotos where  isFolder='1' and parentid=" + str1 + " order by subcompanyid asc,orderNum desc,id desc";
/* 47 */     recordSet.execute(str2);
/* 48 */     while (recordSet.next()) {
/* 49 */       String str3 = recordSet.getString("id");
/* 50 */       int i = Util.getIntValue(recordSet.getString("parentid"), 0);
/* 51 */       String str4 = recordSet.getString("photoname");
/* 52 */       boolean bool = hasChild(str3, "2");
/* 53 */       arrayList.add(new BrowserTreeNode(str3, str4, i + "", bool, true));
/*    */     } 
/*    */     
/* 56 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 57 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 58 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private boolean hasChild(String paramString1, String paramString2) throws Exception {
/* 68 */     boolean bool = false;
/* 69 */     RecordSet recordSet = new RecordSet();
/* 70 */     String str = "select * from hrmsubcompany where  (canceled=0 or canceled is null) and supsubcomid = " + paramString1;
/* 71 */     recordSet.execute(str);
/* 72 */     if (recordSet.next()) {
/* 73 */       bool = true;
/*    */     }
/* 75 */     if (!bool) {
/* 76 */       str = "select * from  AlbumPhotos where  isFolder='1' and parentid = " + paramString1;
/* 77 */       recordSet.execute(str);
/* 78 */       if (recordSet.next()) {
/* 79 */         bool = true;
/*    */       }
/*    */     } 
/* 82 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AlbumBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */