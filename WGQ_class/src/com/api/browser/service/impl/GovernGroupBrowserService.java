/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class GovernGroupBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 21 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 22 */     String str1 = Util.null2String(paramMap.get("name"));
/* 23 */     int i = this.user.getLanguage();
/* 24 */     String str2 = " t1.type = 0 and t1.id !=0 ";
/* 25 */     if (!str1.equals("")) {
/* 26 */       str2 = str2 + " and t1.name like '%";
/* 27 */       str2 = str2 + Util.fromScreen2(str1, i);
/* 28 */       str2 = str2 + "%'";
/*    */     } 
/*    */     
/* 31 */     String str3 = " t1.id ";
/* 32 */     String str4 = " t1.id,t1.name ";
/* 33 */     String str5 = " govern_category t1 ";
/* 34 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 35 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 36 */     SplitTableColBean splitTableColBean = (new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(389700, this.user.getLanguage()), "name", "t1.name", "com.engine.govern.biz.CategoryTransMethod.getCategoryNameStr", Util.null2String(Integer.valueOf(i)))).setIsInputCol(BoolAttr.TRUE);
/* 37 */     arrayList.add(splitTableColBean);
/*    */     
/* 39 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str2, str3, "id", arrayList);
/* 40 */     splitTableBean.setSqlsortway("ASC");
/* 41 */     splitTableBean.setSqlisdistinct("true");
/* 42 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 43 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 56 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 57 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 58 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 59 */     ConditionType conditionType = ConditionType.INPUT;
/* 60 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(conditionType, 0, "name", true);
/* 61 */     searchConditionItem.setLabel(SystemEnv.getHtmlLabelName(389700, Util.getIntValue(this.user.getLanguage())));
/* 62 */     arrayList.add(searchConditionItem);
/* 63 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/GovernGroupBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */