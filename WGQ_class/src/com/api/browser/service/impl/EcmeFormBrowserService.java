/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EcmeFormBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     String str1 = Util.null2String(paramMap.get("tablelabel")).trim();
/* 32 */     String str2 = " where 1 = 1 ";
/* 33 */     if (!str1.equals("")) {
/* 34 */       str2 = str2 + " and t1.tablelabel like '%" + str1 + "%'";
/*    */     }
/* 36 */     String str3 = " t1.id,t1.tablename,t1.tablelabel ";
/* 37 */     String str4 = " ecme_tableinfo t1 ";
/* 38 */     String str5 = " t1.id ";
/*    */     
/* 40 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 41 */     arrayList.add(new SplitTableColBean("true", "id"));
/*    */     
/* 43 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15451, this.user.getLanguage()), "tablelabel", "tablelabel")).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 45 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15026, this.user.getLanguage()), "tablename", "tablename"));
/*    */     
/* 47 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "t1.id", arrayList);
/* 48 */     splitTableBean.setSqlsortway("ASC");
/* 49 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 50 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 56 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 57 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 59 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15451, "tablelabel").setIsQuickSearch(true));
/* 60 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 61 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 66 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 67 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 68 */     String str2 = "select t1.id,t1.tablename,t1.tablelabel from ecme_tableinfo t1 where t1.tablelabel like '%" + str1 + "%' order by t1.id ";
/* 69 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 70 */     RecordSet recordSet = new RecordSet();
/* 71 */     recordSet.executeQuery(str2, new Object[0]);
/* 72 */     while (recordSet.next()) {
/* 73 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 74 */       hashMap1.put("id", recordSet.getString("id"));
/* 75 */       hashMap1.put("name", recordSet.getString("tablelabel"));
/* 76 */       hashMap1.put("tablename", recordSet.getString("tablename"));
/* 77 */       hashMap1.put("tablelabelname", recordSet.getString("tablelabel"));
/* 78 */       arrayList.add(hashMap1);
/*    */     } 
/* 80 */     hashMap.put("datas", arrayList);
/*    */     
/* 82 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 83 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EcmeFormBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */