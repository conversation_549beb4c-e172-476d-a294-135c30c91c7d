/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionGroup;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.cloudstore.eccom.result.WeaResultMsg;
/*    */ import com.engine.systeminfo.util.AppManageTransmethod;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AppModuleBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 33 */     String str1 = " t1.id,t1.name,t1.description ";
/* 34 */     String str2 = "ecology_biz_app_module t1,(SELECT DISTINCT(t2. MODULE) FROM ecology_biz_app_type t2 WHERE 1=1 ";
/* 35 */     String str3 = " t1.id = t3.module ";
/* 36 */     String str4 = "t1.id";
/* 37 */     String str5 = "t1.id";
/* 38 */     String str6 = "asc";
/* 39 */     String str7 = "" + this.user.getLanguage();
/*    */     
/* 41 */     String str8 = Util.null2String(paramMap.get("appmodule_browser_condition"));
/* 42 */     if (StringUtils.isNotBlank(str8)) {
/* 43 */       str3 = str3 + " and description like '%" + str8 + "%'";
/*    */     }
/* 45 */     str2 = str2 + " and (" + Util.getSubINClause(StringUtils.join(AppManageTransmethod.checkUnstandard(), ","), "t2.id", "in") + ") ) t3";
/*    */     
/* 47 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 48 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 49 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()), "name", "name", "com.engine.systeminfo.util.AppManageTransmethod.getModule", str7)).setIsInputCol(BoolAttr.TRUE));
/* 50 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description"));
/*    */     
/* 52 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, str4, str5, str6, arrayList);
/* 53 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 54 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 60 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 61 */     RecordSet recordSet = new RecordSet();
/* 62 */     String str2 = "SELECT t1.id,t1.name,t1.description FROM ecology_biz_app_module t1,(SELECT DISTINCT(t2. MODULE) FROM ecology_biz_app_type t2 WHERE 1 = 1 ";
/* 63 */     str2 = str2 + " and (" + Util.getSubINClause(StringUtils.join(AppManageTransmethod.checkUnstandard(), ","), "t2.id", "in") + ") ) t3 WHERE t1.id = t3.module and t1.description like ?";
/* 64 */     recordSet.executeQuery(str2, new Object[] { "%" + str1 + "%" });
/* 65 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 66 */     while (recordSet.next()) {
/* 67 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 68 */       hashMap1.put("id", recordSet.getString("id"));
/* 69 */       hashMap1.put("name", SystemEnv.getHtmlLabelName(recordSet.getInt("name"), this.user.getLanguage()));
/* 70 */       arrayList.add(hashMap1);
/*    */     } 
/* 72 */     hashMap.put("datas", arrayList);
/* 73 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 79 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/* 80 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 81 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/* 82 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*    */ 
/*    */     
/* 85 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 28485, "appmodule_browser_condition");
/* 86 */     arrayList1.add(searchConditionItem);
/*    */     
/* 88 */     arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(15774, Util.getIntValue(this.user.getLanguage())), true, arrayList1));
/* 89 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 90 */     return weaResultMsg.getResultMapAll();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AppModuleBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */