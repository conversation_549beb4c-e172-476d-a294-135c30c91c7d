/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.maintenance.FnaCostCenter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CostCenterBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"code\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  82 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  84 */     if (this.user == null) {
/*  85 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  86 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  89 */     int i = Util.getIntValue(Util.null2String(paramMap.get("list")), 0);
/*     */ 
/*     */     
/*     */     try {
/*  93 */       if (i == 1) {
/*  94 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  95 */         SplitTableBean splitTableBean = getTableList(paramMap);
/*  96 */         hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */       } else {
/*  98 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  99 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 100 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 101 */         arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 102 */         ListHeadBean listHeadBean = (new ListHeadBean("name", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE);
/* 103 */         listHeadBean.setShowType(1);
/* 104 */         arrayList.add(listHeadBean);
/* 105 */         arrayList.add(new ListHeadBean("code", SystemEnv.getHtmlLabelName(18428, this.user.getLanguage())));
/* 106 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/* 107 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } 
/* 109 */     } catch (Exception exception) {
/* 110 */       (new BaseBean()).writeLog(exception);
/*     */     } 
/*     */     
/* 113 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 126 */     if (this.user == null) {
/* 127 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 128 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 131 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 132 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 134 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 515, "fccname"));
/* 135 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "fcccode"));
/*     */     
/* 137 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) throws Exception {
/* 151 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*     */     
/* 153 */     RecordSet recordSet = new RecordSet();
/* 154 */     FnaCommon fnaCommon = new FnaCommon();
/* 155 */     FnaCostCenter fnaCostCenter = new FnaCostCenter();
/*     */     
/* 157 */     String str1 = Util.null2String(paramMap.get("id"));
/* 158 */     String str2 = Util.null2String(paramMap.get("wfid"));
/* 159 */     String str3 = Util.null2String(paramMap.get("fieldid"));
/* 160 */     String str4 = Util.null2String(paramMap.get("isFromMode"));
/* 161 */     int i = Util.getIntValue(Util.null2String(paramMap.get("modeId")), -1);
/* 162 */     if ("1".equals(str4) && i > 0) {
/* 163 */       str2 = String.valueOf(-1 * i);
/*     */     }
/*     */     
/* 166 */     boolean bool = FnaCostCenter.isBxlc(str2);
/*     */     
/* 168 */     int j = Util.getIntValue(Util.null2String(paramMap.get("wf_beagenter")), 0);
/* 169 */     List list = new ArrayList();
/* 170 */     if (j > 0) {
/* 171 */       User user = new User(j);
/* 172 */       List list1 = FnaCostCenter.getFccList(user);
/*     */     } else {
/* 174 */       list = FnaCostCenter.getFccList(this.user);
/*     */     } 
/*     */     
/* 177 */     if ("".equals(str1)) {
/* 178 */       str1 = "0";
/*     */     }
/*     */     
/* 181 */     if ("".equals(str1)) {
/* 182 */       String str5 = "0";
/* 183 */       String str6 = SystemEnv.getHtmlLabelName(515, this.user.getLanguage());
/* 184 */       String str7 = "0";
/*     */       
/* 186 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 187 */       browserTreeNode.setId(str5);
/* 188 */       browserTreeNode.setName(str6);
/* 189 */       browserTreeNode.setType(str7);
/* 190 */       browserTreeNode.setIsParent(true);
/* 191 */       arrayList.add(browserTreeNode);
/*     */     } else {
/* 193 */       RecordSet recordSet1 = new RecordSet();
/*     */ 
/*     */       
/* 196 */       ArrayList<String> arrayList1 = new ArrayList();
/*     */       
/* 198 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 199 */       recordSet1.executeQuery(" select * from fnacostcenter ", new Object[0]);
/* 200 */       while (recordSet1.next()) {
/* 201 */         int k = Util.getIntValue(recordSet1.getString("type"));
/* 202 */         String str5 = Util.null2String(recordSet1.getString("id"));
/* 203 */         String str6 = Util.null2String(recordSet1.getString("supFccId"));
/* 204 */         if (k == 0) {
/* 205 */           arrayList1.add(str5); continue;
/* 206 */         }  if (k == 1) {
/* 207 */           hashMap1.put(str5, str6);
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 212 */       List list1 = fnaCommon.getWfBrowdefList(str2, str3, "251");
/* 213 */       ArrayList<String> arrayList2 = new ArrayList();
/* 214 */       ArrayList<String> arrayList3 = new ArrayList();
/* 215 */       for (String str5 : list1) {
/* 216 */         if (arrayList1.contains(str5)) {
/* 217 */           arrayList3.add(str5); continue;
/*     */         } 
/* 219 */         arrayList2.add(str5);
/*     */       } 
/*     */ 
/*     */       
/* 223 */       HashSet<String> hashSet1 = new HashSet();
/* 224 */       HashSet<String> hashSet2 = new HashSet();
/* 225 */       if (arrayList3.size() > 0)
/*     */       {
/* 227 */         fnaCostCenter.getAllSubCostcenterType(arrayList3, hashSet1, hashSet2);
/*     */       }
/*     */ 
/*     */       
/* 231 */       ArrayList<String> arrayList4 = new ArrayList();
/* 232 */       for (String str5 : arrayList2) {
/* 233 */         String str6 = (String)hashMap1.get(str5);
/* 234 */         if (!arrayList4.contains(str6)) {
/* 235 */           arrayList4.add(str6);
/*     */         }
/*     */       } 
/*     */       
/* 239 */       HashSet hashSet3 = new HashSet();
/* 240 */       HashSet hashSet4 = new HashSet();
/* 241 */       if (arrayList4.size() > 0)
/*     */       {
/* 243 */         fnaCostCenter.getAllSubCostcenterType(arrayList4, hashSet3, hashSet4, 2);
/*     */       }
/*     */ 
/*     */       
/* 247 */       for (String str5 : hashSet3) {
/* 248 */         hashSet1.add(str5);
/*     */       }
/*     */ 
/*     */       
/* 252 */       for (String str5 : arrayList2) {
/* 253 */         hashSet2.add(str5);
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 258 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 259 */       recordSet.executeQuery(" select id, supFccId from FnaCostCenter ", new Object[0]);
/* 260 */       while (recordSet.next()) {
/* 261 */         String str5 = Util.null2String(recordSet.getString("supFccId"));
/* 262 */         if (!"0".equals(str5)) {
/* 263 */           String str6 = (String)hashMap2.get(str5);
/* 264 */           if (str6 != null) {
/* 265 */             int k = Integer.parseInt(str6) + 1;
/* 266 */             hashMap2.put(str5, String.valueOf(k)); continue;
/*     */           } 
/* 268 */           hashMap2.put(str5, "1");
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 274 */       String str = "select a.id, a.type, a.name, a.code, a.Archive, a.displayOrder from FnaCostCenter a  where a.supFccId = " + Util.getIntValue(str1) + " ORDER BY a.type, a.displayOrder, a.code, a.name, a.id ";
/*     */ 
/*     */       
/* 277 */       recordSet.executeQuery(str, new Object[0]);
/* 278 */       while (recordSet.next()) {
/* 279 */         String str5 = recordSet.getString("id");
/* 280 */         int k = Util.getIntValue(recordSet.getString("type"));
/* 281 */         String str6 = recordSet.getString("name");
/*     */         
/* 283 */         int m = Util.getIntValue(recordSet.getString("Archive"), 0);
/*     */         
/* 285 */         String str7 = str6;
/* 286 */         if (m == 1) {
/* 287 */           str7 = str7 + "(" + SystemEnv.getHtmlLabelName(22205, this.user.getLanguage()) + ")";
/*     */           
/*     */           continue;
/*     */         } 
/*     */         
/* 292 */         if (list1.size() > 0 && !hashSet1.contains(str5) && !hashSet2.contains(str5)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 297 */         if (bool && k == 1 && !list.contains(str5)) {
/*     */           continue;
/*     */         }
/*     */         
/* 301 */         String str8 = "/images/treeimages/home16_wev8.gif";
/* 302 */         if (k == 1) {
/* 303 */           str8 = "/images/treeimages/dept16_wev8.gif";
/*     */         }
/*     */         
/* 306 */         boolean bool1 = true;
/* 307 */         if (k == 1) {
/* 308 */           bool1 = false;
/*     */         }
/* 310 */         else if (hashMap2.get(str5) != null) {
/* 311 */           bool1 = true;
/*     */         } else {
/* 313 */           bool1 = false;
/*     */         } 
/*     */ 
/*     */         
/* 317 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 318 */         browserTreeNode.setId(str5);
/* 319 */         browserTreeNode.setName(str7);
/* 320 */         browserTreeNode.setIsParent(bool1);
/* 321 */         browserTreeNode.setType(String.valueOf(k));
/* 322 */         browserTreeNode.setIcon(str8);
/* 323 */         browserTreeNode.setCanClick((k == 1));
/* 324 */         arrayList.add(browserTreeNode);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 329 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 340 */     FnaCommon fnaCommon = new FnaCommon();
/* 341 */     FnaCostCenter fnaCostCenter = new FnaCostCenter();
/*     */     
/* 343 */     String str1 = Util.null2String(paramMap.get("tabid"));
/* 344 */     if (str1.equals("")) {
/* 345 */       str1 = "0";
/*     */     }
/*     */     
/* 348 */     String str2 = Util.null2String(paramMap.get("fccname"));
/* 349 */     String str3 = Util.null2String(paramMap.get("fcccode"));
/* 350 */     String str4 = Util.null2String(paramMap.get("wfid"));
/* 351 */     String str5 = Util.null2String(paramMap.get("fieldid"));
/* 352 */     String str6 = Util.null2String(paramMap.get("name"));
/* 353 */     int i = Util.getIntValue(Util.null2String(paramMap.get("fccGroupId")), -1);
/* 354 */     String str7 = Util.null2String(paramMap.get("isFromMode"));
/* 355 */     int j = Util.getIntValue(Util.null2String(paramMap.get("modeId")), -1);
/* 356 */     if ("1".equals(str7) && j > 0) {
/* 357 */       str4 = String.valueOf(-1 * j);
/*     */     }
/* 359 */     String str8 = "6";
/*     */     
/* 361 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/* 364 */     ArrayList<String> arrayList1 = new ArrayList();
/* 365 */     recordSet.executeQuery(" select * from fnacostcenter where type = 0 ", new Object[0]);
/* 366 */     while (recordSet.next()) {
/* 367 */       String str = Util.null2String(recordSet.getString("id"));
/* 368 */       arrayList1.add(str);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 374 */     List list = fnaCommon.getWfBrowdefList(str4, str5, "251");
/*     */     
/* 376 */     ArrayList<String> arrayList2 = new ArrayList();
/* 377 */     ArrayList<String> arrayList3 = new ArrayList();
/* 378 */     for (String str : list) {
/* 379 */       if (arrayList1.contains(str)) {
/* 380 */         arrayList3.add(str); continue;
/*     */       } 
/* 382 */       arrayList2.add(str);
/*     */     } 
/*     */ 
/*     */     
/* 386 */     HashSet hashSet1 = new HashSet();
/* 387 */     HashSet hashSet2 = new HashSet();
/*     */     
/* 389 */     if (arrayList3.size() > 0) {
/* 390 */       fnaCostCenter.getAllSubCostcenterType(arrayList3, hashSet1, hashSet2, 1);
/*     */     }
/*     */     
/* 393 */     String str9 = " a.id,a.name,a.code,a.displayOrder ";
/* 394 */     String str10 = " FnaCostCenter a";
/* 395 */     String str11 = " where (a.Archive is null or a.Archive = 0) and a.type = 1 ";
/*     */ 
/*     */     
/* 398 */     boolean bool = FnaCostCenter.isBxlc(str4);
/* 399 */     if (bool && this.user != null && this.user.getUID() != 1) {
/*     */       
/* 401 */       int k = Util.getIntValue(Util.null2String(paramMap.get("wf_beagenter")), 0);
/* 402 */       String str = "";
/* 403 */       if (k > 0) {
/* 404 */         User user = new User(k);
/* 405 */         str = FnaCostCenter.getFccFilterRule(user, "a");
/*     */       } else {
/* 407 */         str = FnaCostCenter.getFccFilterRule(this.user, "a");
/*     */       } 
/*     */       
/* 410 */       if (!"".equals(str)) {
/* 411 */         str11 = str11 + " and ( " + str + " ) ";
/*     */       }
/*     */     } 
/*     */     
/* 415 */     if (i > -1) {
/* 416 */       str11 = str11 + " and a.supFccId = " + i + " ";
/*     */     }
/* 418 */     if (!"".equals(str2)) {
/* 419 */       str11 = str11 + " and a.name like '%" + StringEscapeUtils.escapeSql(str2) + "%' ";
/*     */     }
/* 421 */     if (!"".equals(str6)) {
/* 422 */       str11 = str11 + " and a.name like '%" + StringEscapeUtils.escapeSql(str6) + "%' ";
/*     */     }
/* 424 */     if (!"".equals(str3)) {
/* 425 */       str11 = str11 + " and a.code like '%" + StringEscapeUtils.escapeSql(str3) + "%' ";
/*     */     }
/*     */ 
/*     */     
/* 429 */     String str12 = "";
/* 430 */     if (hashSet1.size() > 0) {
/* 431 */       String str = "";
/* 432 */       for (String str16 : hashSet1) {
/* 433 */         if (!"".equals(str)) {
/* 434 */           str = str + ",";
/*     */         }
/* 436 */         str = str + str16;
/*     */       } 
/* 438 */       str12 = " a.supFccId in (" + str + ") ";
/*     */     } 
/*     */ 
/*     */     
/* 442 */     String str13 = "";
/* 443 */     if (arrayList2.size() > 0) {
/* 444 */       String str = "";
/* 445 */       for (String str16 : arrayList2) {
/* 446 */         if (!"".equals(str)) {
/* 447 */           str = str + ",";
/*     */         }
/* 449 */         str = str + str16;
/*     */       } 
/* 451 */       str13 = " a.id in (" + str + ") ";
/*     */     } 
/*     */     
/* 454 */     if (!"".equals(str12) && !"".equals(str13)) {
/* 455 */       str11 = str11 + " and ( " + str12;
/* 456 */       str11 = str11 + " or " + str13 + " ) ";
/* 457 */     } else if (!"".equals(str12)) {
/* 458 */       str11 = str11 + " and " + str12;
/* 459 */     } else if (!"".equals(str13)) {
/* 460 */       str11 = str11 + " and " + str13;
/*     */     } 
/*     */     
/* 463 */     String str14 = " a.displayOrder,a.code,a.name ";
/* 464 */     String str15 = "a.id";
/*     */     
/* 466 */     (new BaseBean()).writeLog("111---sql: select " + str9 + str10 + str11);
/*     */     
/* 468 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 469 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 470 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", 1));
/* 471 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage()), "code", "code", "weaver.fna.general.FnaCommon.escapeHtml", 0));
/*     */ 
/*     */     
/* 474 */     SplitTableBean splitTableBean = new SplitTableBean("FccBrowserList", "none", str8, "FccBrowserList", str9, str10, str11, str14, str15, "ASC", arrayList);
/*     */     
/* 476 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 478 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 480 */       SplitMobileTemplateBean splitMobileTemplateBean = new SplitMobileTemplateBean();
/* 481 */       splitMobileTemplateBean.addJsonTemplate("json", JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"code\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 482 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 483 */     } catch (Exception exception) {
/* 484 */       exception.printStackTrace();
/*     */     } 
/* 486 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 492 */     String str1 = Util.null2String(paramMap.get("wfid"));
/*     */     
/* 494 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 495 */     String str2 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 496 */     if ("".equals(str2)) return (Map)hashMap; 
/* 497 */     String str3 = "select a.id, a.name, a.code from FnaCostCenter a where  a.id in (" + str2 + ")";
/*     */ 
/*     */     
/* 500 */     boolean bool = FnaCostCenter.isBxlc(str1);
/* 501 */     if (bool && this.user != null && this.user.getUID() != 1) {
/*     */       
/* 503 */       String str = FnaCostCenter.getFccFilterRule(this.user, "a");
/* 504 */       if (!"".equals(str)) {
/* 505 */         str3 = str3 + " and ( " + str + " ) ";
/*     */       }
/*     */     } 
/*     */     
/* 509 */     RecordSet recordSet = new RecordSet();
/* 510 */     recordSet.executeQuery(str3, new Object[0]);
/* 511 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 512 */     while (recordSet.next()) {
/* 513 */       String str4 = recordSet.getString("id");
/* 514 */       String str5 = recordSet.getString("name");
/* 515 */       String str6 = recordSet.getString("code");
/*     */       
/* 517 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 518 */       hashMap1.put("id", str4);
/* 519 */       hashMap1.put("name", str5);
/* 520 */       hashMap1.put("code", str6);
/* 521 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 524 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 525 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 526 */     ListHeadBean listHeadBean = (new ListHeadBean("name", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE);
/* 527 */     listHeadBean.setShowType(1);
/* 528 */     arrayList1.add(listHeadBean);
/* 529 */     arrayList1.add(new ListHeadBean("code", SystemEnv.getHtmlLabelName(18428, this.user.getLanguage())));
/*     */     
/* 531 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 532 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str2, "id"));
/* 533 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 534 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CostCenterBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */