/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BearerMultiBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  42 */     if (this.user == null) {
/*  43 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  44 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  47 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  48 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  50 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name").setIsQuickSearch(true));
/*  51 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "codeName"));
/*  52 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  54 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  70 */     if (this.user == null) {
/*  71 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  72 */       return (Map)hashMap;
/*     */     } 
/*  74 */     String str1 = Util.null2String(paramMap.get("accountId"));
/*  75 */     RecordSet recordSet = new RecordSet();
/*  76 */     if ("".equals(str1)) {
/*  77 */       recordSet.executeQuery(" select accountId from FnaAccountUser where userId = ? ", new Object[] { Integer.valueOf(this.user.getUID()) });
/*  78 */       if (recordSet.next()) {
/*  79 */         str1 = Util.null2String(recordSet.getString("accountId"));
/*     */       }
/*     */     } 
/*  82 */     String str2 = "";
/*  83 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(0) });
/*  84 */     if (recordSet.next()) {
/*  85 */       str2 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/*  87 */     paramMap.put("tableName", str2);
/*     */     
/*  89 */     int i = Util.getIntValue(Util.null2String(paramMap.get("qryType")), 0);
/*     */     
/*  91 */     if (i == 1) {
/*  92 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  93 */       SplitTableBean splitTableBean = getTableList(paramMap);
/*  94 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } else {
/*  96 */       String str = Util.null2String((String)paramMap.get("id"), "0");
/*  97 */       if ("0".equals(str)) {
/*  98 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  99 */         arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 100 */         arrayList.add(new ListHeadBean("budgetBearerName", "", 1, BoolAttr.TRUE));
/* 101 */         arrayList.add(new ListHeadBean("codeName", ""));
/* 102 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */         
/* 104 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 105 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 106 */         if (list.size() == 0) {
/* 107 */           BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 108 */           browserTreeNode.setId("-1");
/* 109 */           browserTreeNode.setName(SystemEnv.getHtmlLabelName(387641, this.user.getLanguage()));
/* 110 */           browserTreeNode.setPid("0");
/* 111 */           browserTreeNode.setParent(true);
/* 112 */           browserTreeNode.setType("0");
/* 113 */           browserTreeNode.setCanClick(false);
/* 114 */           browserTreeNode.setIcon("icon-coms-LargeArea");
/*     */           
/* 116 */           list.add(browserTreeNode);
/*     */         } 
/*     */         
/* 119 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } else {
/* 121 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 122 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 123 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 128 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 139 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 141 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 142 */     String str2 = Util.null2String(paramMap.get("alllevel"));
/* 143 */     String str3 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 144 */     String[] arrayOfString = str3.split(",");
/*     */     
/* 146 */     RecordSet recordSet = new RecordSet();
/* 147 */     String str4 = "";
/* 148 */     if ("".equals(str1)) {
/* 149 */       recordSet.executeQuery(" select accountId from FnaAccountUser where userId = ? ", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 150 */       if (recordSet.next()) {
/* 151 */         str1 = Util.null2String(recordSet.getString("accountId"));
/*     */       }
/*     */     } 
/* 154 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(0) });
/* 155 */     if (recordSet.next()) {
/* 156 */       str4 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/*     */     
/* 159 */     if (!"".equals(str4)) {
/* 160 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */       
/* 162 */       StringBuffer stringBuffer = new StringBuffer();
/* 163 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 164 */         if (stringBuffer.length() > 0) {
/* 165 */           stringBuffer.append(",");
/*     */         }
/* 167 */         stringBuffer.append("'").append(arrayOfString[b]).append("'");
/*     */       } 
/*     */       
/* 170 */       if ("1".equals(str2)) {
/* 171 */         ArrayList<String> arrayList2 = new ArrayList();
/*     */         
/* 173 */         String str = " select a.autocode from " + str4 + " a where (isArchive <> 1  or isArchive is null)  and id in ( " + stringBuffer.toString() + ")";
/* 174 */         recordSet.executeQuery(str, new Object[0]);
/* 175 */         while (recordSet.next()) {
/* 176 */           String str5 = Util.null2String(recordSet.getString("autocode"));
/* 177 */           arrayList2.add(str5);
/*     */         } 
/*     */         
/* 180 */         StringBuffer stringBuffer1 = new StringBuffer();
/* 181 */         stringBuffer1.append(" select a.id,a.budgetBearerName,a.codeName from " + str4 + " a ");
/* 182 */         stringBuffer1.append(" where (1=2 ");
/* 183 */         for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 184 */           stringBuffer1.append(" or a.autocode like '" + (String)arrayList2.get(b1) + "%' ");
/*     */         }
/* 186 */         stringBuffer1.append(" ) ");
/* 187 */         stringBuffer1.append(" and (isArchive <> 1  or isArchive is null) order by bearerlevel,autocode ");
/*     */         
/* 189 */         str3 = "";
/* 190 */         recordSet.executeQuery(stringBuffer1.toString(), new Object[0]);
/* 191 */         while (recordSet.next()) {
/* 192 */           String str5 = Util.null2String(recordSet.getString("id"));
/* 193 */           String str6 = Util.null2String(recordSet.getString("budgetBearerName"));
/* 194 */           String str7 = Util.null2String(recordSet.getString("codeName"));
/*     */           
/* 196 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 197 */           hashMap1.put("id", str5);
/* 198 */           hashMap1.put("budgetBearerName", str6);
/* 199 */           hashMap1.put("codeName", str7);
/* 200 */           arrayList.add(hashMap1);
/*     */           
/* 202 */           str3 = str3 + str5 + ",";
/*     */         } 
/*     */         
/* 205 */         if (!"".equals(str3)) {
/* 206 */           str3 = str3.substring(0, str3.length() - 1);
/*     */         }
/*     */       } else {
/* 209 */         StringBuffer stringBuffer1 = new StringBuffer();
/* 210 */         stringBuffer1.append(" select a.id,a.budgetBearerName,a.codeName from " + str4 + " a ");
/* 211 */         stringBuffer1.append(" where (isArchive <> 1  or isArchive is null)  ");
/* 212 */         stringBuffer1.append(" and id in (").append(stringBuffer.toString()).append(") ");
/*     */         
/* 214 */         recordSet.executeQuery(stringBuffer1.toString(), new Object[0]);
/* 215 */         while (recordSet.next()) {
/* 216 */           String str5 = Util.null2String(recordSet.getString("id"));
/* 217 */           String str6 = Util.null2String(recordSet.getString("budgetBearerName"));
/* 218 */           String str7 = Util.null2String(recordSet.getString("codeName"));
/*     */           
/* 220 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 221 */           hashMap1.put("id", str5);
/* 222 */           hashMap1.put("budgetBearerName", str6);
/* 223 */           hashMap1.put("codeName", str7);
/* 224 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/*     */       
/* 228 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 229 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 230 */       arrayList1.add(new ListHeadBean("budgetBearerName", "", 1, BoolAttr.TRUE));
/* 231 */       arrayList1.add(new ListHeadBean("codeName", ""));
/*     */       
/* 233 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 234 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str3, "id"));
/* 235 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/*     */     
/* 238 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 249 */     String str1 = Util.null2String(paramMap.get("name"));
/* 250 */     String str2 = Util.null2String(paramMap.get("accountId"));
/* 251 */     String str3 = Util.null2String(paramMap.get("codeName"));
/* 252 */     String str4 = "";
/*     */     
/* 254 */     RecordSet recordSet = new RecordSet();
/* 255 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str2, Integer.valueOf(0) });
/* 256 */     if (recordSet.next()) {
/* 257 */       str4 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/* 259 */     String str5 = "10";
/*     */     
/* 261 */     String str6 = " a.id,a.budgetBearerName,a.codeName,a.bearerLevel,a.autoCode ";
/* 262 */     String str7 = " " + str4 + "  a";
/* 263 */     String str8 = " where (isArchive <> 1  or isArchive is null)  ";
/*     */     
/* 265 */     if (!"".equals(str1)) {
/* 266 */       str8 = str8 + " and a.budgetBearerName like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/* 268 */     if (!"".equals(str3)) {
/* 269 */       str8 = str8 + " and a.codeName like '%" + StringEscapeUtils.escapeSql(str3) + "%' ";
/*     */     }
/*     */     
/* 272 */     String str9 = "a.bearerLevel,a.autoCode";
/* 273 */     String str10 = "a.id";
/*     */     
/* 275 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 276 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 277 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "budgetBearerName", "budgetBearerName")).setIsInputCol(BoolAttr.TRUE));
/* 278 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage()), "codeName", "codeName", "weaver.fna.general.FnaCommon.escapeHtml"));
/*     */ 
/*     */     
/* 281 */     SplitTableBean splitTableBean = new SplitTableBean("bearerMultiBrowserList", "none", str5, "bearerMultiBrowserList", str6, str7, str8, str9, str10, "ASC", arrayList);
/*     */     
/* 283 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 285 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) {
/* 296 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 297 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 298 */     String str2 = "";
/*     */     
/* 300 */     RecordSet recordSet1 = new RecordSet();
/* 301 */     recordSet1.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(0) });
/* 302 */     if (recordSet1.next()) {
/* 303 */       str2 = Util.null2String(recordSet1.getString("tableName"));
/*     */     }
/*     */     
/* 306 */     RecordSet recordSet2 = new RecordSet();
/* 307 */     RecordSet recordSet3 = new RecordSet();
/* 308 */     String str3 = Util.null2String(paramMap.get("id"));
/* 309 */     String str4 = "";
/* 310 */     recordSet1.executeQuery("select * from " + str2 + " where id=?", new Object[] { str3 });
/* 311 */     if (recordSet1.next()) {
/* 312 */       str4 = Util.null2String(recordSet1.getString("autoCode"));
/*     */     }
/* 314 */     String str5 = "";
/* 315 */     if ("".equals(str4)) {
/* 316 */       str5 = " select id,budgetBearerName,autoCode from " + str2 + " where (isArchive <> 1  or isArchive is null) and autoCode like '" + str4 + "_%' and bearerLevel = 1";
/*     */     } else {
/* 318 */       recordSet2.executeQuery("select id,bearerLevel from " + str2 + " where (isArchive <> 1  or isArchive is null) and autoCode = ?", new Object[] { str4 });
/* 319 */       int i = 0;
/* 320 */       if (recordSet2.next()) {
/* 321 */         i = Util.getIntValue(recordSet2.getString("bearerLevel"));
/*     */       }
/* 323 */       str5 = " select id,budgetBearerName,autoCode from " + str2 + " where (isArchive <> 1  or isArchive is null) and autoCode like '" + str4 + "_%' and bearerLevel = " + (i + 1);
/*     */     } 
/* 325 */     str5 = str5 + " order by bearerLevel,autoCode,displayOrder, budgetBearerName";
/* 326 */     String str6 = "/images/treeimages/home16_wev8.gif";
/* 327 */     recordSet2.executeQuery(str5, new Object[0]);
/* 328 */     while (recordSet2.next()) {
/* 329 */       String str7 = recordSet2.getString("id");
/* 330 */       String str8 = recordSet2.getString("budgetBearerName");
/* 331 */       String str9 = recordSet2.getString("autoCode");
/* 332 */       boolean bool = true;
/* 333 */       recordSet3.executeQuery(" select count(*) cnt from " + str2 + "  where (isArchive <> 1  or isArchive is null) and autoCode like '" + str9 + "_%'", new Object[0]);
/* 334 */       if (recordSet3.next() && recordSet3.getInt("cnt") > 0) {
/* 335 */         bool = true;
/*     */       } else {
/* 337 */         bool = false;
/*     */       } 
/*     */       
/* 340 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 341 */       browserTreeNode.setId(str7);
/* 342 */       browserTreeNode.setName(str8);
/* 343 */       browserTreeNode.setIsParent(bool);
/* 344 */       browserTreeNode.setIcon(str6);
/* 345 */       browserTreeNode.setCanClick(true);
/* 346 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 348 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BearerMultiBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */