/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.homepage.HomepageUtil;
/*     */ import weaver.homepage.cominfo.HomepageBaseLayoutCominfo;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.page.PageCominfo;
/*     */ import weaver.page.PageUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ElementBrowserService
/*     */   extends BrowserService
/*     */ {
/*  42 */   private PageCominfo pc = new PageCominfo();
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  45 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  46 */     String str1 = Util.null2String(paramMap.get("sourcetype"));
/*  47 */     String str2 = Util.null2String(paramMap.get("pageType"));
/*  48 */     String str3 = " ";
/*  49 */     if ("1".equals(str2)) {
/*  50 */       str3 = " and creatortype=0 and subcompanyid=-1 and isredirecturl!=1 ";
/*     */     } else {
/*  52 */       str3 = " and subcompanyid>0 and isredirecturl!=1 ";
/*     */     } 
/*  54 */     if ("1".equals(str1)) {
/*  55 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  56 */       HomepageUtil homepageUtil = new HomepageUtil();
/*  57 */       PageUtil pageUtil = new PageUtil();
/*  58 */       RecordSet recordSet = new RecordSet();
/*  59 */       ArrayList<String> arrayList = new ArrayList();
/*  60 */       ArrayList<Integer> arrayList1 = new ArrayList();
/*  61 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  62 */       String str4 = Util.null2String(paramMap.get("hpid"));
/*  63 */       String str5 = Util.null2String(paramMap.get("title"));
/*  64 */       String str6 = Util.null2String(paramMap.get("ebasename"));
/*  65 */       if (!"".equals(str4)) {
/*  66 */         String str = "select id,infoname,subcompanyid from hpinfo h where id=" + str4 + " and infoname is not null " + str3 + " and isuse=1 order by pid,ordernum1,id";
/*  67 */         if (this.user.getUID() == 1) {
/*  68 */           str = "select id,infoname,subcompanyid from hpinfo h where id=" + str4 + " and infoname is not null " + str3 + " and isuse=1 order by  pid,ordernum1,id";
/*     */         }
/*  70 */         recordSet.executeQuery(str, new Object[0]);
/*  71 */         while (recordSet.next()) {
/*  72 */           arrayList.add(recordSet.getString("id"));
/*  73 */           arrayList1.add(Integer.valueOf(Util.getIntValue(recordSet.getString("subcompanyid"), -1)));
/*  74 */           hashMap2.put(recordSet.getString("id"), recordSet.getString("infoname"));
/*     */         } 
/*     */       } else {
/*  77 */         String str = "select id,infoname,subcompanyid from hpinfo h where infoname is not null " + str3 + "   and isuse=1  and id in (" + homepageUtil.getShareHomapage(this.user) + ") order by pid,ordernum1,id";
/*  78 */         if (this.user.getUID() == 1) {
/*  79 */           str = "select id,infoname,subcompanyid from hpinfo h where infoname is not null  " + str3 + "  and isuse=1 order by  pid,ordernum1,id";
/*     */         }
/*  81 */         recordSet.executeQuery(str, new Object[0]);
/*  82 */         while (recordSet.next()) {
/*  83 */           arrayList.add(recordSet.getString("id"));
/*  84 */           arrayList1.add(Integer.valueOf(Util.getIntValue(recordSet.getString("subcompanyid"), -1)));
/*  85 */           hashMap2.put(recordSet.getString("id"), recordSet.getString("infoname"));
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*  90 */       boolean bool = true;
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  95 */       if (!HrmUserVarify.checkUserRight("homepage:Maint", this.user) && !pageUtil.getUserMaintHpidListPublic(this.user.getUID()).contains(str4)) {
/*  96 */         bool = false;
/*     */       }
/*     */       
/*  99 */       PageCominfo pageCominfo = new PageCominfo();
/* 100 */       hashMap1.put("hasRight", Boolean.valueOf(bool));
/* 101 */       String str7 = "";
/* 102 */       if (bool) {
/*     */         
/* 104 */         HomepageBaseLayoutCominfo homepageBaseLayoutCominfo = new HomepageBaseLayoutCominfo();
/* 105 */         String str = "";
/* 106 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 107 */           String str11 = arrayList.get(b);
/* 108 */           int i = ((Integer)arrayList1.get(b)).intValue();
/* 109 */           String str12 = pageCominfo.getLayoutid(str11);
/*     */ 
/*     */           
/* 112 */           str = homepageBaseLayoutCominfo.getAllowArea(str12);
/* 113 */           String str13 = "";
/* 114 */           ArrayList<String> arrayList3 = Util.TokenizerString(str, ","); int j;
/* 115 */           for (j = 0; j < arrayList3.size(); ) { str13 = str13 + " areaflag='" + arrayList3.get(j) + "' or"; j++; }
/* 116 */            if (!str13.equals("")) {
/* 117 */             str13 = str13.substring(0, str13.length() - 2);
/* 118 */             str13 = " and (" + str13 + ")";
/*     */           } 
/*     */           
/* 121 */           j = pageUtil.getHpUserId(str11, "" + i, this.user);
/* 122 */           int k = pageUtil.getHpUserType(str11, "" + i, this.user);
/* 123 */           if (pageCominfo.getSubcompanyid(str11).equals("-1") && pageCominfo.getCreatortype(str11).equals("0")) {
/* 124 */             j = 1;
/* 125 */             k = 0;
/*     */           } 
/* 127 */           if (Util.getIntValue(str11) < 0) {
/* 128 */             j = 1;
/* 129 */             k = 0;
/*     */           } 
/* 131 */           int m = j;
/* 132 */           int n = k;
/* 133 */           if (pageCominfo.getIsLocked(str11).equals("1")) {
/* 134 */             m = Util.getIntValue(pageCominfo.getCreatorid(str11));
/* 135 */             n = Util.getIntValue(pageCominfo.getCreatortype(str11));
/*     */           } 
/* 137 */           String str14 = "select areaElements from hplayout where hpid=" + str11 + " and userid=" + m + " and usertype=" + n + str13;
/* 138 */           recordSet.executeQuery(str14, new Object[0]);
/* 139 */           while (recordSet.next()) {
/* 140 */             str7 = str7 + Util.null2String(recordSet.getString("areaElements"));
/*     */           }
/*     */         } 
/*     */       } 
/* 144 */       String str8 = " where he.isuse = '1'";
/* 145 */       if (!"".equals(str7)) {
/* 146 */         str7 = str7.substring(0, str7.length() - 1);
/* 147 */         str8 = str8 + " and he.id in (" + str7 + ")";
/*     */       } 
/* 149 */       if (!"".equals(str5))
/* 150 */         str8 = str8 + " and he.title like '%" + str5 + "%'"; 
/* 151 */       if (!"".equals(str6))
/* 152 */         str8 = str8 + " and hbe.title like '%" + str6 + "%'"; 
/* 153 */       String str9 = " ";
/* 154 */       if (recordSet.getDBType().equals("sqlserver")) {
/* 155 */         str9 = " 'element' + convert(varchar(100),he.id) as id, he.hpid as otherinfo, he.title, hbe.title as ebasename ";
/*     */       } else {
/* 157 */         str9 = " CONCAT('element',he.id) AS id, he.hpid as otherinfo, he.title, hbe.title as ebasename ";
/*     */       } 
/* 159 */       String str10 = " hpElement he join hpbaseElement hbe on he.ebaseid = hbe.id";
/* 160 */       ArrayList<SplitTableColBean> arrayList2 = new ArrayList();
/* 161 */       arrayList2.add(new SplitTableColBean("true", "id"));
/* 162 */       arrayList2.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(229, this.user.getLanguage()), "title", "he.title", 1)).setIsInputCol(BoolAttr.TRUE));
/* 163 */       arrayList2.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(382251, this.user.getLanguage()), "ebasename", "hbe.title", "com.api.browser.service.impl.ElementBrowserService.getEbaseName", this.user.getLanguage() + ""));
/* 164 */       arrayList2.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(382252, this.user.getLanguage()), "otherinfo", "he.hpid", "com.api.browser.service.impl.ElementBrowserService.getInfoName", this.user.getLanguage() + ""));
/* 165 */       SplitTableBean splitTableBean = new SplitTableBean(str9, str10, str8, "he.id", "he.id", arrayList2);
/* 166 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } else {
/* 168 */       String str4 = Util.null2String(paramMap.get("templatetitle"));
/* 169 */       String str5 = Util.null2String(paramMap.get("templatedesc"));
/* 170 */       String str6 = Util.null2String(paramMap.get("ebasename"));
/* 171 */       RecordSet recordSet = new RecordSet();
/* 172 */       String str7 = " where 1=1";
/* 173 */       if (!"".equals(str4)) str7 = str7 + " and het.templatetitle like '%" + str4 + "%'"; 
/* 174 */       if (!"".equals(str5)) str7 = str7 + " and het.templatedesc like '%" + str5 + "%'"; 
/* 175 */       if (!"".equals(str6)) str7 = str7 + " and hbe.title like '%" + str6 + "%'"; 
/* 176 */       String str8 = " ";
/* 177 */       if (recordSet.getDBType().equals("sqlserver")) {
/* 178 */         str8 = " 'template' + convert(varchar(100),het.id) as id, het.templatetitle as title, hbe.title as ebasename, het.templatedesc as otherinfo";
/*     */       } else {
/* 180 */         str8 = " CONCAT('template',het.id) as id, het.templatetitle as title, hbe.title as ebasename, het.templatedesc as otherinfo";
/*     */       } 
/* 182 */       String str9 = " hpElementTemplate het left join hpbaseElement hbe on het.ebaseid = hbe.id ";
/* 183 */       recordSet.executeQuery("select count(het.id) as total from  " + str9 + str7, new Object[0]);
/* 184 */       ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 185 */       arrayList.add(new SplitTableColBean("true", "id"));
/* 186 */       arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(229, this.user.getLanguage()), "title", "het.templatetitle", 1)).setIsInputCol(BoolAttr.TRUE));
/* 187 */       arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(382251, this.user.getLanguage()), "ebasename", "hbe.title", "com.api.browser.service.impl.ElementBrowserService.getEbaseName", this.user.getLanguage() + ""));
/* 188 */       arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "otherinfo", "het.templatedesc", "com.api.browser.service.impl.ElementBrowserService.getTemplateDesc", this.user.getLanguage() + ""));
/* 189 */       SplitTableBean splitTableBean = new SplitTableBean(str8, str9, str7, "het.id", "het.id", arrayList);
/* 190 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } 
/* 192 */     return (Map)hashMap;
/*     */   }
/*     */   public String getInfoName(String paramString1, String paramString2) {
/* 195 */     return SystemEnv.getHtmlLabelName(382252, Util.getIntValue(paramString2)) + "：" + this.pc.getInfoname(paramString1);
/*     */   }
/*     */   public String getEbaseName(String paramString1, String paramString2) {
/* 198 */     return SystemEnv.getHtmlLabelName(382251, Util.getIntValue(paramString2)) + "：" + paramString1;
/*     */   }
/*     */   public String getTemplateDesc(String paramString1, String paramString2) {
/* 201 */     return SystemEnv.getHtmlLabelName(433, Util.getIntValue(paramString2)) + "：" + paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 212 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 213 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 214 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 215 */     String str1 = Util.null2String((String)paramMap.get("sourcetype"), "1");
/* 216 */     String str2 = Util.null2String(paramMap.get("pageType"));
/* 217 */     String str3 = " ";
/* 218 */     if ("1".equals(str2)) {
/* 219 */       str3 = " and creatortype=0 and subcompanyid=-1 and isredirecturl!=1 ";
/*     */     } else {
/* 221 */       str3 = " and subcompanyid>0 and isredirecturl!=1 ";
/*     */     } 
/* 223 */     if ("1".equals(str1) || "".equals(str1)) {
/* 224 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "title", true));
/* 225 */       arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 382252, "hpid", getPortalList(str3)));
/* 226 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 382251, "ebasename"));
/* 227 */     } else if ("2".equals(str1)) {
/* 228 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "templatetitle", true));
/* 229 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "templatedesc"));
/* 230 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 382251, "ebasename"));
/*     */     } 
/* 232 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 233 */     return (Map)hashMap;
/*     */   }
/*     */   public List<SearchConditionOption> getPortalList(String paramString) {
/* 236 */     HomepageUtil homepageUtil = new HomepageUtil();
/* 237 */     RecordSet recordSet = new RecordSet();
/* 238 */     String str = "select id,infoname from hpinfo h where infoname is not null " + paramString + "   and isuse=1  and id in (" + homepageUtil.getShareHomapage(this.user) + ") order by pid,ordernum1,id";
/* 239 */     if (this.user.getUID() == 1) {
/* 240 */       str = "select id,infoname from hpinfo h where infoname is not null " + paramString + "  and isuse=1 order by  pid,ordernum1,id";
/*     */     }
/* 242 */     recordSet.executeQuery(str, new Object[0]);
/* 243 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 244 */     arrayList.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/* 245 */     while (recordSet.next()) {
/* 246 */       arrayList.add(new SearchConditionOption(recordSet.getString("id"), recordSet.getString("infoname"), false));
/*     */     }
/* 248 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 258 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 259 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 260 */     ArrayList<String> arrayList = Util.TokenizerString(str1, ",");
/* 261 */     String str2 = "";
/* 262 */     String str3 = "";
/* 263 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 264 */       String str = arrayList.get(b);
/* 265 */       if (str.indexOf("element") != -1) str2 = str2 + str.replace("element", "") + ","; 
/* 266 */       if (str.indexOf("template") != -1) str3 = str3 + str.replace("template", "") + ","; 
/*     */     } 
/* 268 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 269 */     if (!"".equals(str2)) {
/* 270 */       str2 = str2.substring(0, str2.length() - 1);
/* 271 */       RecordSet recordSet = new RecordSet();
/* 272 */       recordSet.execute("select he.id, he.hpid, he.title as title, hbe.title as ebasename from hpElement he join hpbaseElement hbe on he.ebaseid = hbe.id where he.id in (" + str2 + ")");
/* 273 */       while (recordSet.next()) {
/* 274 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 275 */         hashMap1.put("id", "element" + recordSet.getString("id"));
/* 276 */         hashMap1.put("title", recordSet.getString("title"));
/* 277 */         hashMap1.put("otherinfo", SystemEnv.getHtmlLabelName(382252, this.user.getLanguage()) + "：" + this.pc.getInfoname(recordSet.getString("hpid")));
/* 278 */         hashMap1.put("ebasename", SystemEnv.getHtmlLabelName(382251, this.user.getLanguage()) + "：" + recordSet.getString("ebasename"));
/* 279 */         arrayList1.add(hashMap1);
/*     */       } 
/*     */     } 
/* 282 */     if (!"".equals(str3)) {
/* 283 */       str3 = str3.substring(0, str3.length() - 1);
/* 284 */       RecordSet recordSet = new RecordSet();
/* 285 */       recordSet.execute("select het.id, het.templatetitle, hbe.title as ebasename, het.templatedesc from  hpElementTemplate het left join hpbaseElement hbe on het.ebaseid = hbe.id where het.id in (" + str3 + ")");
/* 286 */       while (recordSet.next()) {
/* 287 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 288 */         hashMap1.put("id", "template" + recordSet.getString("id"));
/* 289 */         hashMap1.put("title", recordSet.getString("templatetitle"));
/* 290 */         hashMap1.put("otherinfo", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()) + "：" + recordSet.getString("templatedesc"));
/* 291 */         hashMap1.put("ebasename", SystemEnv.getHtmlLabelName(382251, this.user.getLanguage()) + "：" + recordSet.getString("ebasename"));
/* 292 */         arrayList1.add(hashMap1);
/*     */       } 
/*     */     } 
/* 295 */     ArrayList<ListHeadBean> arrayList2 = new ArrayList();
/* 296 */     arrayList2.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 297 */     arrayList2.add(new ListHeadBean("title", SystemEnv.getHtmlLabelName(229, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 298 */     arrayList2.add(new ListHeadBean("otherinfo", SystemEnv.getHtmlLabelName(382252, this.user.getLanguage())));
/* 299 */     arrayList2.add(new ListHeadBean("ebasename", SystemEnv.getHtmlLabelName(382251, this.user.getLanguage())));
/* 300 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList2);
/* 301 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList1, str1, "id"));
/* 302 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 303 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ElementBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */