/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerSizeService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  55 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  56 */     String str2 = Util.null2String(paramMap.get("fullname"));
/*  57 */     String str3 = Util.null2String(paramMap.get("description"));
/*  58 */     String str4 = " ";
/*  59 */     boolean bool = false;
/*  60 */     if (!str1.equals("") && 
/*  61 */       !bool) {
/*  62 */       bool = true;
/*  63 */       str4 = str4 + str1;
/*     */     } 
/*     */     
/*  66 */     if (!str2.equals("")) {
/*  67 */       if (!bool) {
/*  68 */         bool = true;
/*  69 */         str4 = str4 + " where fullname like '%";
/*  70 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  71 */         str4 = str4 + "%'";
/*     */       } else {
/*  73 */         str4 = str4 + " and fullname like '%";
/*  74 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  75 */         str4 = str4 + "%'";
/*     */       } 
/*     */     }
/*  78 */     if (!str3.equals("")) {
/*  79 */       if (!bool) {
/*  80 */         bool = true;
/*  81 */         str4 = str4 + " where description like '%";
/*  82 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/*  83 */         str4 = str4 + "%'";
/*     */       } else {
/*  85 */         str4 = str4 + " and description like '%";
/*  86 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/*  87 */         str4 = str4 + "%'";
/*     */       } 
/*     */     }
/*     */     
/*  91 */     String str5 = "id,fullname,description";
/*  92 */     String str6 = "CRM_CustomerSize";
/*  93 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  94 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/*  95 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname")).setIsInputCol(BoolAttr.TRUE));
/*  96 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*     */     
/*  98 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "orderkey", "id", "asc", arrayList);
/*     */     
/*     */     try {
/* 101 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 102 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 103 */     } catch (Exception exception) {
/* 104 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 107 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 108 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 113 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 114 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 115 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 116 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname", true));
/* 117 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description", false));
/* 118 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 119 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CustomerSizeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */