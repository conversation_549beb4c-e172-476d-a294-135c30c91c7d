/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.outter.OutterDisplayHelper;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OutterBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     Logger logger = LoggerFactory.getLogger(OutterBrowserService.class);
/* 29 */     OutterDisplayHelper outterDisplayHelper = new OutterDisplayHelper();
/* 30 */     String str1 = outterDisplayHelper.getShareOutterSql(this.user);
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str2 = Util.null2String(paramMap.get("sysid"));
/* 33 */     String str3 = Util.null2String(paramMap.get("name"));
/* 34 */     String str4 = Util.null2String(paramMap.get("paramFlag"));
/* 35 */     String str5 = " where EXISTS (select 1 from (" + str1 + ") b where a.sysid=b.sysid ) and (a.logintype=1 or a.logintype=2) ";
/* 36 */     if (!"".equals(str2)) str5 = str5 + " and a.sysid=" + str2; 
/* 37 */     if (!"".equals(str3)) str5 = str5 + " and a.name like '%" + str3 + "%' "; 
/* 38 */     if ("".equals(str4)) {
/* 39 */       str5 = str5 + " and a.logintype='1' ";
/*    */     } else {
/* 41 */       str5 = str5 + " and a.logintype='2' ";
/*    */     } 
/* 43 */     String str6 = " ";
/* 44 */     str6 = " a.id as id,a.sysid as sysid,a.name as name,a.sysid as asysid ";
/* 45 */     String str7 = " from outter_sys a ";
/* 46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 47 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 48 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "sysid", "sysid")).setIsInputCol(BoolAttr.TRUE));
/*    */ 
/*    */     
/* 51 */     if ("".equals(str4)) {
/* 52 */       arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name"));
/*    */     } else {
/* 54 */       arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(10000228, this.user.getLanguage()), "asysid", "asysid", "weaver.general.PCAndAppTransMethod.getParams"));
/*    */     } 
/* 56 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str5, "id", "id", arrayList);
/* 57 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 58 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 69 */     String str = Util.null2String(paramMap.get("paramFlag"));
/* 70 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 71 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 72 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 73 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 84, "sysid"));
/* 74 */     if ("".equals(str)) {
/* 75 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name"));
/*    */     }
/* 77 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 78 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OutterBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */