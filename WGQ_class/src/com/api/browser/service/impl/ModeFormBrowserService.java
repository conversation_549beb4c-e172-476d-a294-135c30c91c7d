/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.cube.biz.SqlHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.dao.AppInfoDao;
/*     */ import weaver.formmode.dao.FormInfoDao;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ModeFormBrowserService
/*     */   extends BrowserService
/*     */ {
/*  35 */   private ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  40 */     String str1 = Util.null2String(paramMap.get("formname")).trim();
/*  41 */     String str2 = Util.null2String(paramMap.get("formtype"));
/*  42 */     String str3 = " where 1 = 1 ";
/*     */     
/*  44 */     String str4 = getNoRightWhere();
/*  45 */     FormInfoDao formInfoDao = new FormInfoDao();
/*  46 */     List<? extends CharSequence> list = formInfoDao.getNoRightFormNames(this.user.getUID());
/*  47 */     String str5 = "";
/*  48 */     if (list.size() > 0) {
/*  49 */       str5 = " and upper(t1.tablename) not in ('" + String.join("','", list) + "') ";
/*     */     }
/*  51 */     str3 = str3 + str4 + str5;
/*  52 */     if (!str1.equals(""))
/*  53 */       str3 = str3 + " and t2.labelname like '%" + str1 + "%'"; 
/*  54 */     if (str2.equals("1")) {
/*  55 */       str3 = str3 + " and t3.isvirtualform = 1";
/*  56 */     } else if (str2.equals("2")) {
/*  57 */       str3 = str3 + " and (t3.isvirtualform is null or t3.isvirtualform != 1)";
/*     */     } 
/*     */     
/*  60 */     if (this.manageDetachComInfo.isUseFmManageDetach() && this.user.getUID() != 1) {
/*  61 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  62 */       int[] arrayOfInt1 = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEFORM:ALL", 0);
/*  63 */       int[] arrayOfInt2 = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FormManage:All", 0);
/*  64 */       int[] arrayOfInt3 = new int[arrayOfInt1.length + arrayOfInt2.length];
/*  65 */       byte b = 0;
/*  66 */       for (int i : arrayOfInt1) {
/*  67 */         arrayOfInt3[b] = i;
/*  68 */         b++;
/*     */       } 
/*  70 */       for (int i : arrayOfInt2) {
/*  71 */         arrayOfInt3[b] = i;
/*  72 */         b++;
/*     */       } 
/*  74 */       if (arrayOfInt3.length == 0) {
/*  75 */         str3 = str3 + " and 1=2 ";
/*     */       } else {
/*  77 */         str3 = str3 + " and " + SqlHelper.SplitSqlInCondition("t1.subCompanyId3", arrayOfInt3);
/*     */       } 
/*     */     } 
/*     */     
/*  81 */     String str6 = " t1.id,t1.tablename,t2.labelname, t1.formdes, t3.isvirtualform ";
/*  82 */     String str7 = " workflow_bill t1 left join htmllabelinfo t2 on t1.namelabel = t2.indexid and t2.languageid = " + this.user.getLanguage() + " left join modeformextend t3 on t1.id = t3.formid ";
/*     */     
/*  84 */     String str8 = " t1.dsporder,t1.id ";
/*     */     
/*  86 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  87 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  88 */     arrayList.add(new SplitTableColBean("true", "isvirtualform"));
/*     */     
/*  90 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15451, this.user.getLanguage()), "labelname", "labelname", "com.api.browser.service.impl.ModeFormBrowserService.getLabelName", "column:isvirtualform"))
/*  91 */         .setIsInputCol(BoolAttr.TRUE));
/*     */     
/*  93 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15026, this.user.getLanguage()), "tablename", "tablename", "weaver.formmode.virtualform.VirtualFormHandler.getRealFromName"));
/*     */     
/*  95 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15452, this.user.getLanguage()), "formdes", "formdes"));
/*     */     
/*  97 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str3, str8, "t1.id", arrayList);
/*  98 */     splitTableBean.setSqlsortway("ASC");
/*  99 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 100 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNoRightWhere() {
/* 108 */     String str1 = "";
/* 109 */     String str2 = "";
/* 110 */     AppInfoDao appInfoDao = new AppInfoDao();
/* 111 */     List<? extends CharSequence> list = appInfoDao.getNoRightAppids(this.user.getUID());
/* 112 */     ArrayList<String> arrayList = new ArrayList();
/* 113 */     if (list.size() > 0) {
/* 114 */       str2 = "    in (" + String.join(",", list) + ") ";
/* 115 */       String str = "select formid from appforminfo where appid" + str2;
/* 116 */       str = str + " UNION select b.formid from workflow_bill a left join modeinfo b on a.id=b.formid where b.modetype " + str2;
/* 117 */       str = str + " UNION select formid from ModeFormExtend where appid" + str2;
/* 118 */       RecordSet recordSet = new RecordSet();
/* 119 */       recordSet.executeQuery(str, new Object[0]);
/* 120 */       while (recordSet.next()) {
/* 121 */         arrayList.add(Util.null2String(recordSet.getString("formid")));
/*     */       }
/*     */     } 
/* 124 */     if (arrayList.size() > 0) {
/* 125 */       str1 = " and t1.id not in (" + String.join(",", (Iterable)arrayList) + ") ";
/*     */     }
/* 127 */     return str1;
/*     */   }
/*     */   
/*     */   public String getLabelName(String paramString1, String paramString2) {
/* 131 */     if ("1".equals(paramString2)) {
/* 132 */       return "<span style='position:relative;'>" + paramString1 + "<span class='cube-virtual-form-flag' >V</span></span>";
/*     */     }
/* 134 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 140 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 141 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 142 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 143 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15451, "formname").setIsQuickSearch(true));
/*     */     
/* 145 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 18411, "formtype");
/* 146 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 147 */     arrayList1.add(new SearchConditionOption("", ""));
/* 148 */     String str = Util.null2String(paramMap.get("formtype"));
/* 149 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(33885, this.user.getLanguage())));
/* 150 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(33886, this.user.getLanguage())));
/* 151 */     searchConditionItem.setOptions(arrayList1);
/* 152 */     arrayList.add(searchConditionItem);
/*     */     
/* 154 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 155 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 160 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 161 */     String str1 = paramHttpServletRequest.getParameter("q");
/*     */     
/* 163 */     String str2 = getNoRightWhere();
/* 164 */     FormInfoDao formInfoDao = new FormInfoDao();
/* 165 */     List<? extends CharSequence> list = formInfoDao.getNoRightFormNames(this.user.getUID());
/* 166 */     String str3 = "";
/* 167 */     if (list.size() > 0) {
/* 168 */       str3 = " and upper(t1.tablename) not in ('" + String.join("','", list) + "') ";
/*     */     }
/* 170 */     String str4 = "";
/* 171 */     if (this.manageDetachComInfo.isUseFmManageDetach() && this.user.getUID() != 1) {
/* 172 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 173 */       int[] arrayOfInt1 = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEFORM:ALL", 0);
/* 174 */       int[] arrayOfInt2 = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FormManage:All", 0);
/* 175 */       int[] arrayOfInt3 = new int[arrayOfInt1.length + arrayOfInt2.length];
/* 176 */       byte b = 0;
/* 177 */       for (int i : arrayOfInt1) {
/* 178 */         arrayOfInt3[b] = i;
/* 179 */         b++;
/*     */       } 
/* 181 */       for (int i : arrayOfInt2) {
/* 182 */         arrayOfInt3[b] = i;
/* 183 */         b++;
/*     */       } 
/* 185 */       if (arrayOfInt3.length == 0) {
/* 186 */         str4 = str4 + " and 1=2 ";
/*     */       } else {
/* 188 */         str4 = str4 + " and " + SqlHelper.SplitSqlInCondition("t1.subCompanyId3", arrayOfInt3);
/*     */       } 
/*     */     } 
/* 191 */     String str5 = "select t1.id,t1.tablename,t2.labelname, t1.formdes, t3.isvirtualform from  workflow_bill t1 left join htmllabelinfo t2 on t1.namelabel = t2.indexid and t2.languageid = " + this.user.getLanguage() + " left join modeformextend t3 on t1.id = t3.formid  where t2.labelname like '%" + str1 + "%'" + str2 + str3 + str4 + " order by t1.dsporder,t1.id";
/*     */     
/* 193 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 194 */     RecordSet recordSet = new RecordSet();
/* 195 */     recordSet.executeQuery(str5, new Object[0]);
/* 196 */     while (recordSet.next()) {
/* 197 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 198 */       hashMap1.put("id", recordSet.getString("id"));
/* 199 */       hashMap1.put("name", recordSet.getString("labelname"));
/* 200 */       hashMap1.put("isvirtualform", Util.null2s(recordSet.getString("isvirtualform"), "0"));
/* 201 */       hashMap1.put("tablename", recordSet.getString("tablename"));
/* 202 */       hashMap1.put("tablelabelname", recordSet.getString("labelname"));
/* 203 */       arrayList.add(hashMap1);
/*     */     } 
/* 205 */     hashMap.put("datas", arrayList);
/*     */     
/* 207 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 208 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeFormBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */