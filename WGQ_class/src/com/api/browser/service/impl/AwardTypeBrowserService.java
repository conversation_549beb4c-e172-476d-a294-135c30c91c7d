/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SearchConditionOption;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AwardTypeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 34 */     String str1 = Util.null2String(paramMap.get("awardtype"));
/* 35 */     String str2 = Util.null2String(paramMap.get("name"));
/* 36 */     String str3 = Util.null2String(paramMap.get("description"));
/*    */     
/* 38 */     String str4 = " ";
/* 39 */     if (!str1.equals("")) {
/* 40 */       str4 = str4 + " and awardtype = '" + str1 + "' ";
/*    */     }
/*    */     
/* 43 */     if (!str2.equals("")) {
/* 44 */       str4 = str4 + " and name like '%";
/* 45 */       str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/* 46 */       str4 = str4 + "%'";
/*    */     } 
/*    */     
/* 49 */     if (!str3.equals("")) {
/* 50 */       str4 = str4 + " and description like '%";
/* 51 */       str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/* 52 */       str4 = str4 + "%'";
/*    */     } 
/* 54 */     str4 = SqlUtils.replaceFirstAnd(str4);
/*    */     
/* 56 */     String str5 = " id,name,awardtype,description ";
/* 57 */     String str6 = " HrmAwardType  ";
/*    */     
/* 59 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 60 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 61 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 62 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "awardtype", "awardtype", "com.api.browser.service.impl.AwardTypeBrowserService.getAwardtypeLabel", String.valueOf(this.user.getLanguage())));
/* 63 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(15667, this.user.getLanguage()), "description", "description"));
/*    */     
/* 65 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", arrayList);
/* 66 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 67 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAwardtypeLabel(String paramString1, String paramString2) {
/* 77 */     if ("0".equals(paramString1))
/* 78 */       return SystemEnv.getHtmlLabelName(809, Util.getIntValue(paramString2, 7)); 
/* 79 */     if ("1".equals(paramString1))
/* 80 */       return SystemEnv.getHtmlLabelName(810, Util.getIntValue(paramString2, 7)); 
/* 81 */     return "";
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 87 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 88 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 89 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 90 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 91 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(809, this.user.getLanguage())));
/* 92 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(810, this.user.getLanguage())));
/* 93 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 63, "awardtype", arrayList1));
/*    */     
/* 95 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true));
/* 96 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15667, "description"));
/*    */     
/* 98 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AwardTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */