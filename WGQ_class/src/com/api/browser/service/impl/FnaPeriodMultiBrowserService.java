/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaPeriodMultiBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     if (this.user == null) {
/*  41 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  42 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  45 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  46 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  48 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name").setIsQuickSearch(true));
/*  49 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "codeName"));
/*  50 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  52 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  66 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  68 */     if (this.user == null) {
/*  69 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  70 */       return (Map)hashMap;
/*     */     } 
/*  72 */     RecordSet recordSet = new RecordSet();
/*  73 */     String str1 = Util.null2String(paramMap.get("accountId"));
/*  74 */     if ("".equals(str1)) {
/*     */       
/*  76 */       recordSet.executeQuery(" select accountId from FnaAccountUser where userId = ? ", new Object[] { Integer.valueOf(this.user.getUID()) });
/*  77 */       if (recordSet.next()) {
/*  78 */         str1 = Util.null2String(recordSet.getString("accountId"));
/*     */       }
/*     */     } 
/*  81 */     String str2 = "";
/*  82 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(3) });
/*  83 */     if (recordSet.next()) {
/*  84 */       str2 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/*  86 */     paramMap.put("tableName", str2);
/*     */     
/*  88 */     int i = Util.getIntValue(Util.null2String(paramMap.get("qryType")), 0);
/*     */     
/*  90 */     if (i == 1) {
/*  91 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  92 */       SplitTableBean splitTableBean = getTableList(paramMap);
/*  93 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } else {
/*  95 */       String str = Util.null2String((String)paramMap.get("id"), "0");
/*  96 */       if ("0".equals(str)) {
/*  97 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  98 */         arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  99 */         arrayList.add(new ListHeadBean("periodName", "", 1, BoolAttr.TRUE));
/* 100 */         arrayList.add(new ListHeadBean("codeName", ""));
/* 101 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */         
/* 103 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 104 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 105 */         if (list.size() == 0) {
/* 106 */           BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 107 */           browserTreeNode.setId("-1");
/* 108 */           browserTreeNode.setName(SystemEnv.getHtmlLabelName(15388, this.user.getLanguage()));
/* 109 */           browserTreeNode.setPid("0");
/* 110 */           browserTreeNode.setParent(true);
/* 111 */           browserTreeNode.setType("0");
/* 112 */           browserTreeNode.setCanClick(false);
/* 113 */           browserTreeNode.setIcon("icon-coms-LargeArea");
/*     */           
/* 115 */           list.add(browserTreeNode);
/*     */         } 
/*     */         
/* 118 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } else {
/* 120 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 121 */         List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 122 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 127 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 138 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/* 141 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 142 */     String str2 = Util.null2String(paramMap.get("alllevel"));
/* 143 */     String str3 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 144 */     String[] arrayOfString = str3.split(",");
/*     */     
/* 146 */     RecordSet recordSet = new RecordSet();
/* 147 */     String str4 = "";
/* 148 */     if ("".equals(str1)) {
/* 149 */       recordSet.executeQuery(" select accountId from FnaAccountUser where userId = ? ", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 150 */       if (recordSet.next()) {
/* 151 */         str1 = Util.null2String(recordSet.getString("accountId"));
/*     */       }
/*     */     } 
/* 154 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(3) });
/* 155 */     if (recordSet.next()) {
/* 156 */       str4 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/*     */     
/* 159 */     if (!"".equals(str4)) {
/* 160 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */       
/* 162 */       StringBuffer stringBuffer = new StringBuffer();
/* 163 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 164 */         if (stringBuffer.length() > 0) {
/* 165 */           stringBuffer.append(",");
/*     */         }
/* 167 */         stringBuffer.append("'").append(arrayOfString[b]).append("'");
/*     */       } 
/*     */       
/* 170 */       if ("1".equals(str2)) {
/* 171 */         ArrayList<String> arrayList2 = new ArrayList();
/*     */         
/* 173 */         String str = " select a.autocode from " + str4 + " a where (isArchive <> 1  or isArchive is null)  and id in ( " + stringBuffer.toString() + ")";
/* 174 */         recordSet.executeQuery(str, new Object[0]);
/* 175 */         while (recordSet.next()) {
/* 176 */           String str5 = Util.null2String(recordSet.getString("autocode"));
/* 177 */           arrayList2.add(str5);
/*     */         } 
/*     */         
/* 180 */         StringBuffer stringBuffer1 = new StringBuffer();
/* 181 */         stringBuffer1.append(" select a.id,a.periodName,a.codeName from " + str4 + " a ");
/* 182 */         stringBuffer1.append(" where (1=2 ");
/* 183 */         for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 184 */           stringBuffer1.append(" or a.autocode like '" + (String)arrayList2.get(b1) + "%' ");
/*     */         }
/* 186 */         stringBuffer1.append(" ) ");
/* 187 */         stringBuffer1.append(" and (isArchive <> 1  or isArchive is null) order by periodlevel,beginDate,autocode ");
/*     */         
/* 189 */         str3 = "";
/* 190 */         recordSet.executeQuery(stringBuffer1.toString(), new Object[0]);
/* 191 */         while (recordSet.next()) {
/* 192 */           String str5 = Util.null2String(recordSet.getString("id"));
/* 193 */           String str6 = Util.null2String(recordSet.getString("periodName"));
/* 194 */           String str7 = Util.null2String(recordSet.getString("codeName"));
/*     */           
/* 196 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 197 */           hashMap1.put("id", str5);
/* 198 */           hashMap1.put("periodName", str6);
/* 199 */           hashMap1.put("codeName", str7);
/* 200 */           arrayList.add(hashMap1);
/*     */           
/* 202 */           str3 = str3 + str5 + ",";
/*     */         } 
/*     */         
/* 205 */         if (!"".equals(str3)) {
/* 206 */           str3 = str3.substring(0, str3.length() - 1);
/*     */         }
/*     */       } else {
/* 209 */         StringBuffer stringBuffer1 = new StringBuffer();
/* 210 */         stringBuffer1.append(" select a.id,a.periodName,a.codeName from " + str4 + " a ");
/* 211 */         stringBuffer1.append(" where (isArchive <> 1  or isArchive is null)  ");
/* 212 */         stringBuffer1.append(" and id in (").append(stringBuffer.toString()).append(")");
/*     */         
/* 214 */         recordSet.executeQuery(stringBuffer1.toString(), new Object[0]);
/* 215 */         while (recordSet.next()) {
/* 216 */           String str5 = Util.null2String(recordSet.getString("id"));
/* 217 */           String str6 = Util.null2String(recordSet.getString("periodName"));
/* 218 */           String str7 = Util.null2String(recordSet.getString("codeName"));
/*     */           
/* 220 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 221 */           hashMap1.put("id", str5);
/* 222 */           hashMap1.put("periodName", str6);
/* 223 */           hashMap1.put("codeName", str7);
/* 224 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/*     */       
/* 228 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 229 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 230 */       arrayList1.add(new ListHeadBean("periodName", "", 1, BoolAttr.TRUE));
/* 231 */       arrayList1.add(new ListHeadBean("codeName", ""));
/*     */       
/* 233 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 234 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str3, "id"));
/* 235 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/*     */ 
/*     */     
/* 239 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 250 */     String str1 = Util.null2String(paramMap.get("name"));
/* 251 */     String str2 = Util.null2String(paramMap.get("accountId"));
/* 252 */     String str3 = Util.null2String(paramMap.get("codeName"));
/* 253 */     String str4 = "";
/*     */     
/* 255 */     RecordSet recordSet = new RecordSet();
/* 256 */     recordSet.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str2, Integer.valueOf(3) });
/* 257 */     if (recordSet.next()) {
/* 258 */       str4 = Util.null2String(recordSet.getString("tableName"));
/*     */     }
/* 260 */     String str5 = "10";
/*     */     
/* 262 */     String str6 = " a.id,a.periodName,a.codeName ";
/* 263 */     String str7 = " " + str4 + "  a";
/* 264 */     String str8 = " where (a.isArchive <> 1  or a.isArchive is null)  ";
/*     */     
/* 266 */     if (!"".equals(str1)) {
/* 267 */       str8 = str8 + " and a.periodName like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/* 269 */     if (!"".equals(str3)) {
/* 270 */       str8 = str8 + " and a.codeName like '%" + StringEscapeUtils.escapeSql(str3) + "%' ";
/*     */     }
/*     */     
/* 273 */     String str9 = " a.periodLevel,a.beginDate ";
/* 274 */     String str10 = "a.id";
/*     */     
/* 276 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 277 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 278 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "periodName", "periodName")).setIsInputCol(BoolAttr.TRUE));
/* 279 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage()), "codeName", "codeName", "weaver.fna.general.FnaCommon.escapeHtml"));
/*     */ 
/*     */     
/* 282 */     return new SplitTableBean("periodMultiBrowserList", "none", str5, "periodMultiBrowserList", str6, str7, str8, str9, str10, "ASC", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) {
/* 295 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 296 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 297 */     String str2 = "";
/*     */     
/* 299 */     RecordSet recordSet1 = new RecordSet();
/* 300 */     recordSet1.executeQuery(" select * from FnaAccountDtl where accountId = ? and tableType = ? ", new Object[] { str1, Integer.valueOf(3) });
/* 301 */     if (recordSet1.next()) {
/* 302 */       str2 = Util.null2String(recordSet1.getString("tableName"));
/*     */     }
/*     */     
/* 305 */     RecordSet recordSet2 = new RecordSet();
/* 306 */     RecordSet recordSet3 = new RecordSet();
/* 307 */     String str3 = Util.null2String(paramMap.get("id"));
/* 308 */     String str4 = "";
/* 309 */     recordSet1.executeQuery("select * from " + str2 + " where id=?", new Object[] { str3 });
/* 310 */     if (recordSet1.next()) {
/* 311 */       str4 = Util.null2String(recordSet1.getString("autoCode"));
/*     */     }
/* 313 */     String str5 = "";
/* 314 */     if ("".equals(str4)) {
/* 315 */       str5 = " select id,periodName,autoCode from " + str2 + " where (isArchive <> 1  or isArchive is null) and autoCode like '" + str4 + "_%' and periodLevel = 1";
/*     */     } else {
/* 317 */       recordSet2.executeQuery("select id,periodLevel from " + str2 + " where (isArchive <> 1  or isArchive is null) and autoCode = ?", new Object[] { str4 });
/* 318 */       int i = 0;
/* 319 */       if (recordSet2.next()) {
/* 320 */         i = Util.getIntValue(recordSet2.getString("periodLevel"));
/*     */       }
/* 322 */       str5 = " select id,periodName,autoCode from " + str2 + " where (isArchive <> 1  or isArchive is null) and autoCode like '" + str4 + "_%' and periodLevel = " + (i + 1);
/*     */     } 
/* 324 */     str5 = str5 + " ORDER BY periodLevel,beginDate";
/* 325 */     recordSet2.executeQuery(str5, new Object[0]);
/* 326 */     String str6 = "/images/treeimages/home16_wev8.gif";
/* 327 */     while (recordSet2.next()) {
/* 328 */       String str7 = recordSet2.getString("id");
/* 329 */       String str8 = recordSet2.getString("periodName");
/* 330 */       String str9 = recordSet2.getString("autoCode");
/* 331 */       boolean bool = true;
/* 332 */       recordSet3.executeQuery(" select count(*) cnt from " + str2 + "  where (isArchive <> 1  or isArchive is null) and autoCode like '" + str9 + "_%'", new Object[0]);
/* 333 */       if (recordSet3.next() && recordSet3.getInt("cnt") > 0) {
/* 334 */         bool = true;
/*     */       } else {
/* 336 */         bool = false;
/*     */       } 
/*     */       
/* 339 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 340 */       browserTreeNode.setId(str7);
/* 341 */       browserTreeNode.setName(str8);
/* 342 */       browserTreeNode.setIsParent(bool);
/* 343 */       browserTreeNode.setIcon(str6);
/* 344 */       browserTreeNode.setCanClick(true);
/* 345 */       arrayList.add(browserTreeNode);
/*     */     } 
/* 347 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaPeriodMultiBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */