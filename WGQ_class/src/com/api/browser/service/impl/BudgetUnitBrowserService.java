/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.CompanyComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BudgetUnitBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  56 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  57 */     if (this.user == null) {
/*  58 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  59 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  62 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  63 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  64 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "orgName").setIsQuickSearch(true));
/*  65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "orgCode"));
/*  66 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  68 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  79 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  80 */     if (this.user == null) {
/*  81 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  82 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  85 */     int i = Util.getIntValue(Util.null2String(paramMap.get("orgType")), 0);
/*  86 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  87 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  89 */     String[] arrayOfString = str.split(",");
/*  90 */     if (i == 0) {
/*  91 */       CompanyComInfo companyComInfo = new CompanyComInfo();
/*  92 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  93 */         String str1 = companyComInfo.getCompanyname(arrayOfString[b]);
/*  94 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  95 */         hashMap1.put("id", Util.null2String(arrayOfString[b]));
/*  96 */         hashMap1.put("orgName", Util.null2String(str1));
/*  97 */         hashMap1.put("orgCode", "");
/*  98 */         arrayList.add(hashMap1);
/*     */       } 
/* 100 */     } else if (i == 1) {
/* 101 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 102 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 103 */         String str1 = subCompanyComInfo.getSubCompanyCode(arrayOfString[b]);
/* 104 */         String str2 = subCompanyComInfo.getSubCompanyname(arrayOfString[b]);
/* 105 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 106 */         hashMap1.put("id", Util.null2String(arrayOfString[b]));
/* 107 */         hashMap1.put("orgName", Util.null2String(str2));
/* 108 */         hashMap1.put("orgCode", Util.null2String(str1));
/* 109 */         arrayList.add(hashMap1);
/*     */       } 
/* 111 */     } else if (i == 2) {
/* 112 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 113 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 114 */         String str1 = departmentComInfo.getDepartmentCode(arrayOfString[b]);
/* 115 */         String str2 = departmentComInfo.getDepartmentName(arrayOfString[b]);
/* 116 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 117 */         hashMap1.put("id", Util.null2String(arrayOfString[b]));
/* 118 */         hashMap1.put("orgName", Util.null2String(str2));
/* 119 */         hashMap1.put("orgCode", Util.null2String(str1));
/* 120 */         arrayList.add(hashMap1);
/*     */       } 
/* 122 */     } else if (i == 3) {
/* 123 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 124 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 125 */         String str1 = resourceComInfo.getWorkcode(arrayOfString[b]);
/* 126 */         String str2 = resourceComInfo.getLastname(arrayOfString[b]);
/* 127 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 128 */         hashMap1.put("id", Util.null2String(arrayOfString[b]));
/* 129 */         hashMap1.put("orgName", Util.null2String(str2));
/* 130 */         hashMap1.put("orgCode", Util.null2String(str1));
/* 131 */         arrayList.add(hashMap1);
/*     */       } 
/* 133 */     } else if (i == 18004) {
/* 134 */       StringBuffer stringBuffer = new StringBuffer();
/* 135 */       stringBuffer.append(" select fc.id id, fc.name name, fc.code code from FnaCostCenter fc ");
/* 136 */       stringBuffer.append(" where fc.id in (" + str + ") ");
/* 137 */       RecordSet recordSet = new RecordSet();
/* 138 */       recordSet.executeSql(stringBuffer.toString());
/* 139 */       while (recordSet.next()) {
/* 140 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 141 */         String str1 = Util.null2String(recordSet.getString("id"));
/* 142 */         String str2 = Util.null2String(recordSet.getString("name"));
/* 143 */         String str3 = Util.null2String(recordSet.getString("code"));
/* 144 */         hashMap1.put("id", str1);
/* 145 */         hashMap1.put("orgName", str2);
/* 146 */         hashMap1.put("orgCode", str3);
/* 147 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/*     */     
/* 151 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 152 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 153 */     arrayList1.add(new ListHeadBean("orgName", "", 1, BoolAttr.TRUE));
/* 154 */     arrayList1.add(new ListHeadBean("orgCode", ""));
/*     */     
/* 156 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 157 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 158 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 160 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 166 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 167 */     if (this.user == null) {
/* 168 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 169 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 172 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 173 */     arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 174 */     arrayList.add(new ListHeadBean("orgName", "", 1, BoolAttr.TRUE));
/* 175 */     arrayList.add(new ListHeadBean("orgCode", ""));
/* 176 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */     
/* 178 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 179 */     int i = Util.getIntValue(Util.null2String(paramMap.get("qryType")), 0);
/* 180 */     int j = Util.getIntValue(Util.null2String(paramMap.get("orgType")), 0);
/*     */     
/* 182 */     String str1 = Util.null2String(paramMap.get("orgId"));
/* 183 */     String str2 = Util.null2String(paramMap.get("orgName"));
/* 184 */     String str3 = Util.null2String(paramMap.get("orgCode"));
/*     */     
/* 186 */     RecordSet recordSet = new RecordSet();
/* 187 */     if (j == 0) {
/* 188 */       StringBuffer stringBuffer = new StringBuffer();
/* 189 */       stringBuffer.append(" select hc.id id, hc.companyname name from HrmCompany hc ");
/* 190 */       stringBuffer.append(" where hc.id in (" + str1 + ") ");
/* 191 */       stringBuffer.append(" and hc.companyname like '%" + str2 + "%' ");
/* 192 */       recordSet.executeSql(stringBuffer.toString());
/* 193 */       while (recordSet.next()) {
/* 194 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 195 */         hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 196 */         hashMap1.put("orgName", Util.null2String(recordSet.getString("name")));
/* 197 */         hashMap1.put("orgCode", "");
/* 198 */         arrayList1.add(hashMap1);
/*     */       } 
/* 200 */     } else if (j == 1) {
/* 201 */       StringBuffer stringBuffer = new StringBuffer();
/* 202 */       stringBuffer.append(" select hs.id id, hs.subcompanyname name, hs.subcompanycode code from HrmSubCompany hs ");
/* 203 */       stringBuffer.append(" where hs.id in (" + str1 + ") ");
/* 204 */       stringBuffer.append(" and hs.subcompanyname like '%" + str2 + "%' ");
/* 205 */       stringBuffer.append(" and case when hs.subcompanycode is null then '' else hs.subcompanycode end like '%" + str3 + "%' ");
/* 206 */       recordSet.executeSql(stringBuffer.toString());
/* 207 */       commonData(recordSet, (List)arrayList1);
/* 208 */     } else if (j == 2) {
/* 209 */       StringBuffer stringBuffer = new StringBuffer();
/* 210 */       stringBuffer.append(" select hd.id id, hd.departmentname name, hd.departmentcode code from HrmDepartment hd ");
/* 211 */       stringBuffer.append(" where hd.id in (" + str1 + ") ");
/* 212 */       stringBuffer.append(" and hd.departmentname like '%" + str2 + "%' ");
/* 213 */       stringBuffer.append(" and case when hd.departmentcode is null then '' else hd.departmentcode end like '%" + str3 + "%' ");
/* 214 */       recordSet.executeSql(stringBuffer.toString());
/* 215 */       commonData(recordSet, (List)arrayList1);
/* 216 */     } else if (j == 3) {
/* 217 */       StringBuffer stringBuffer = new StringBuffer();
/* 218 */       stringBuffer.append(" select hr.id id, hr.lastname name, hr.workcode code from HrmResource hr ");
/* 219 */       stringBuffer.append(" where hr.id in (" + str1 + ") ");
/* 220 */       stringBuffer.append(" and hr.lastname like '%" + str2 + "%' ");
/* 221 */       stringBuffer.append(" and case when hr.workcode is null then '' else hr.workcode end like '%" + str3 + "%' ");
/* 222 */       recordSet.executeSql(stringBuffer.toString());
/* 223 */       commonData(recordSet, (List)arrayList1);
/* 224 */     } else if (j == 18004) {
/* 225 */       StringBuffer stringBuffer = new StringBuffer();
/* 226 */       stringBuffer.append(" select fc.id id, fc.name name, fc.code code from FnaCostCenter fc ");
/* 227 */       stringBuffer.append(" where fc.id in (" + str1 + ") ");
/* 228 */       stringBuffer.append(" and fc.name like '%" + str2 + "%' ");
/* 229 */       stringBuffer.append(" and case when fc.code is null then '' else fc.code end like '%" + str3 + "%' ");
/* 230 */       recordSet.executeSql(stringBuffer.toString());
/* 231 */       commonData(recordSet, (List)arrayList1);
/*     */     } 
/*     */     
/* 234 */     if (i == 1) {
/* 235 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList1);
/* 236 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/* 238 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void commonData(RecordSet paramRecordSet, List<Map<String, Object>> paramList) throws Exception {
/* 247 */     while (paramRecordSet.next()) {
/* 248 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 249 */       hashMap.put("id", Util.null2String(paramRecordSet.getString("id")));
/* 250 */       hashMap.put("orgName", Util.null2String(paramRecordSet.getString("name")));
/* 251 */       hashMap.put("orgCode", Util.null2String(paramRecordSet.getString("code")));
/* 252 */       paramList.add(hashMap);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BudgetUnitBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */