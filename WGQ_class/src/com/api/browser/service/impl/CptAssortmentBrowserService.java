/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptAssortmentBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  30 */     String str1 = Util.null2s(Util.null2String(paramMap.get("id")), "0");
/*     */     
/*  32 */     String str2 = Util.null2s(Util.null2String(paramMap.get("onlyendnode")), "n");
/*  33 */     String str3 = Util.null2String(paramMap.get("showcptcount"));
/*     */ 
/*     */     
/*  36 */     RecordSet recordSet = new RecordSet();
/*  37 */     int i = this.user.getUserSubCompany1();
/*  38 */     int j = this.user.getUID();
/*  39 */     recordSet.execute("select cptdetachable from SystemSet");
/*  40 */     int k = 0;
/*  41 */     if (recordSet.next()) {
/*  42 */       k = recordSet.getInt("cptdetachable");
/*     */     }
/*  44 */     String str4 = "";
/*  45 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/*  46 */       str4 = "Capital:Maintenance";
/*     */     }
/*  48 */     ArrayList<String> arrayList = new ArrayList();
/*  49 */     if (k == 1 && j != 1) {
/*  50 */       recordSet.executeProc("HrmRoleSR_SeByURId", "" + this.user.getUID() + Util.getSeparator() + str4);
/*  51 */       while (recordSet.next()) {
/*  52 */         String str = recordSet.getString("subcompanyid");
/*  53 */         arrayList.add(str.trim());
/*     */       } 
/*     */     } 
/*     */     
/*  57 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  58 */     capitalAssortmentComInfo.setTofirstRow();
/*  59 */     ArrayList<BrowserTreeNode> arrayList1 = new ArrayList();
/*  60 */     while (capitalAssortmentComInfo.next()) {
/*  61 */       String str5 = Util.null2String(capitalAssortmentComInfo.getSupAssortmentId(), "0");
/*  62 */       int m = Util.getIntValue(Util.null2String(capitalAssortmentComInfo.getSubcompanyid1()), 0);
/*     */       
/*  64 */       if (!str5.equals(str1)) {
/*     */         continue;
/*     */       }
/*     */       
/*  68 */       if (k == 1 && j != 1 && (
/*  69 */         (arrayList.size() == 0) ? (
/*  70 */         i != m) : 
/*     */ 
/*     */ 
/*     */         
/*  74 */         !arrayList.contains(m + ""))) {
/*     */         continue;
/*     */       }
/*     */ 
/*     */ 
/*     */       
/*  80 */       String str6 = capitalAssortmentComInfo.getAssortmentId();
/*  81 */       String str7 = capitalAssortmentComInfo.getAssortmentName();
/*  82 */       String str8 = capitalAssortmentComInfo.getCapitalCount();
/*  83 */       boolean bool1 = ("y".equals(str3.toLowerCase()) && Integer.parseInt(str8) > 0) ? true : false;
/*  84 */       String str9 = bool1 ? (str7 + " (" + str8 + ")") : str7;
/*  85 */       boolean bool = hasChild(str6);
/*  86 */       boolean bool2 = "y".equals(str2.toLowerCase()) ? (!bool ? true : false) : true;
/*  87 */       arrayList1.add(new BrowserTreeNode(str6, str9, str1, bool, bool2));
/*     */     } 
/*  89 */     ArrayList<ListHeadBean> arrayList2 = new ArrayList();
/*  90 */     arrayList2.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  91 */     arrayList2.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(33439, this.user.getLanguage()), 1, BoolAttr.TRUE));
/*     */     
/*  93 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList2);
/*  94 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  95 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList1);
/*  96 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean hasChild(String paramString) throws Exception {
/* 106 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 107 */     capitalAssortmentComInfo.setTofirstRow();
/* 108 */     while (capitalAssortmentComInfo.next()) {
/* 109 */       if (capitalAssortmentComInfo.getSupAssortmentId().equals(paramString))
/* 110 */         return true; 
/*     */     } 
/* 112 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 122 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 123 */     RecordSet recordSet = new RecordSet();
/* 124 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 125 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 126 */     String str2 = Util.null2String(paramMap.get("alllevel"));
/* 127 */     String[] arrayOfString = str1.split(",");
/* 128 */     ArrayList<String> arrayList = new ArrayList();
/* 129 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 130 */       String str = arrayOfString[b];
/* 131 */       if (!arrayList.contains(str)) {
/* 132 */         arrayList.add(str);
/*     */       }
/*     */       
/* 135 */       if ("1".equals(str2)) {
/* 136 */         String str4 = capitalAssortmentComInfo.getSupAssortmentStr(str);
/* 137 */         String str5 = "select id from CptCapitalAssortment where supassortmentstr LIKE '" + str4 + str + "|%' order by id ";
/* 138 */         recordSet.execute(str5);
/* 139 */         while (recordSet.next()) {
/* 140 */           String str6 = recordSet.getString(1);
/* 141 */           if (!arrayList.contains(str6)) {
/* 142 */             arrayList.add(str6);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 148 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 149 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 150 */     StringBuffer stringBuffer = new StringBuffer();
/* 151 */     for (String str : arrayList) {
/* 152 */       hashMap2 = new HashMap<>();
/* 153 */       hashMap2.put("id", Util.null2String(str));
/* 154 */       hashMap2.put("name", Util.null2String(capitalAssortmentComInfo.getAssortmentName(str)));
/* 155 */       arrayList1.add(hashMap2);
/* 156 */       stringBuffer.append(str).append(",");
/*     */     } 
/* 158 */     String str3 = stringBuffer.toString();
/* 159 */     if ("".equals(str3)) {
/* 160 */       str3 = str3.substring(0, str3.length() - 1);
/*     */     }
/*     */     
/* 163 */     ArrayList<ListHeadBean> arrayList2 = new ArrayList();
/* 164 */     arrayList2.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 165 */     arrayList2.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(33439, this.user.getLanguage()), 1, BoolAttr.TRUE));
/*     */     
/* 167 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList2);
/* 168 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList1, str3, "id"));
/* 169 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 170 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CptAssortmentBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */