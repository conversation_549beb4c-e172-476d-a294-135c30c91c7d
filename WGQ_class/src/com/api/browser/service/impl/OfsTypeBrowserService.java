/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.integration.util.CommonService;
/*     */ import com.engine.integration.util.PageUidFactory;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OfsTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  40 */     String str1 = Util.null2String(paramMap.get("sysid"));
/*  41 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/*  42 */     String str3 = Util.null2String(paramMap.get("receivewfdata"));
/*     */     
/*  44 */     String str4 = " where cancel = 0 ";
/*  45 */     if (!"".equals(str1)) {
/*  46 */       str4 = str4 + " and sysid ='" + str1 + "'";
/*     */     }
/*  48 */     if (!"".equals(str2)) {
/*  49 */       str4 = str4 + " and workflowname like '%" + str2 + "%'";
/*     */     }
/*  51 */     if (!"".equals(str3)) {
/*  52 */       str4 = str4 + " and receivewfdata =" + str3;
/*     */     }
/*  54 */     String str5 = " * ";
/*  55 */     String str6 = "10";
/*  56 */     String str7 = "ofs_workflow";
/*  57 */     String str8 = " ofs_workflow ";
/*     */ 
/*     */     
/*  60 */     String str9 = PageUidFactory.getPageUid("intergration_browser_ofsType");
/*  61 */     String str10 = str9;
/*  62 */     String str11 = PageIdConst.getPageSize(str10, this.user.getUID());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  74 */     String str12 = "<table instanceid=\"ofs_workflowTable\" tabletype=\"checkbox\" pagesize=\"" + str6 + "\" > <checkboxpopedom    popedompara=\"column:workflowid\" showmethod=\"weaver.general.SplitPageTransmethod.getCheckBox\" /> <sql backfields=\"" + str5 + "\" sqlform=\"" + str8 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str4) + "\"  sqlorderby=\"sysid,workflowid\"  sqlprimarykey=\"workflowid\" sqlsortway=\"desc\" sqlisdistinct=\"true\" />       <head>           <col hide=\"true\" width=\"35%\" text=\"" + SystemEnv.getHtmlLabelName(16579, this.user.getLanguage()) + "\"  column=\"workflowid\" orderkey=\"workflowid\"  isPrimarykey='true' />           <col hide=\"false\" width=\"35%\" text=\"" + SystemEnv.getHtmlLabelName(16579, this.user.getLanguage()) + "\"  column=\"workflowname\" orderkey=\"workflowname\" isInputCol='true' />           <col hide=\"false\" width=\"35%\" text=\"" + SystemEnv.getHtmlLabelName(31694, this.user.getLanguage()) + "\"  column=\"sysid\" orderkey=\"sysid\" transmethod=\"weaver.ofs.util.OfsDataParse.getOfsInfoName\" />           <col hide=\"false\" width=\"30%\" text=\"" + SystemEnv.getHtmlLabelName(10000227, Util.getIntValue(this.user.getLanguage())) + "\"  column=\"receivewfdata\" orderkey=\"receivewfdata\" otherpara=\"" + this.user.getLanguage() + "\" transmethod=\"weaver.ofs.util.OfsDataParse.getOpenName\"/>       </head></table>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  79 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  80 */     String str13 = str9 + "_" + Util.getEncrypt(Util.getRandom());
/*  81 */     Util_TableMap.setVal(str13, str12);
/*  82 */     hashMap2.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  83 */     hashMap2.put(BrowserConstant.BROWSER_RESULT_DATA, str13);
/*     */     
/*  85 */     hashMap1.putAll(hashMap2);
/*     */     
/*  87 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  93 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  94 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  95 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  96 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 16579, "workflowname", true));
/*  97 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, "18526,18015", "receivewfdata");
/*  98 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  99 */     arrayList1.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/* 100 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(163, this.user.getLanguage())));
/* 101 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(161, this.user.getLanguage())));
/* 102 */     (new CommonService()).setFirstOptions(arrayList1);
/* 103 */     searchConditionItem.setOptions(arrayList1);
/* 104 */     arrayList.add(searchConditionItem);
/* 105 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 112 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 113 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, null);
/* 114 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 115 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 116 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OfsTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */