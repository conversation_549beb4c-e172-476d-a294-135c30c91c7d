/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BudgetDimensionBrowserService
/*     */   extends BrowserService
/*     */ {
/*  31 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     if (this.user == null) {
/*  41 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  42 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  45 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  46 */     SplitTableBean splitTableBean = getTableList(paramMap);
/*  47 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  49 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  60 */     if (this.user == null) {
/*  61 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  62 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  65 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  66 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  68 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name"));
/*  69 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 389137, "dimensionType"));
/*     */     
/*  71 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  73 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/*  83 */     String str1 = "";
/*  84 */     RecordSet recordSet = new RecordSet();
/*  85 */     String str2 = Util.null2String(paramMap.get("name"));
/*  86 */     String str3 = Util.null2String(paramMap.get("accountId"));
/*  87 */     String str4 = Util.null2String(paramMap.get("selfId"));
/*  88 */     String str5 = Util.null2String(paramMap.get("selfDetailId"));
/*     */     
/*  90 */     StringBuffer stringBuffer = new StringBuffer();
/*  91 */     String str6 = "select dimensionID from FnaDataSetDetail where dasetID=? and id<>?";
/*  92 */     recordSet.executeQuery(str6, new Object[] { str4, str5 });
/*  93 */     while (recordSet.next()) {
/*  94 */       stringBuffer.append("'" + recordSet.getString("dimensionID") + "',");
/*     */     }
/*  96 */     stringBuffer.append("'-1'");
/*  97 */     (new BaseBean()).writeLog("dimensionIDs===" + stringBuffer);
/*  98 */     String str7 = "select tableName from FnaAccountDtl where accountId=? and tableType=?";
/*  99 */     recordSet.executeQuery(str7, new Object[] { str3, Integer.valueOf(2) });
/* 100 */     if (recordSet.next()) {
/* 101 */       str1 = recordSet.getString("tableName");
/*     */     }
/*     */     
/* 104 */     String str8 = " a.id id,t.typeName type,a.dimensionName name ";
/* 105 */     String str9 = str1 + " a,FnaDimensionType t";
/* 106 */     String str10 = " where 1=1 and a.typeId=t.id";
/*     */     
/* 108 */     if (!str3.equals("")) {
/* 109 */       str10 = str10 + " and (a.id != '" + str3 + "') ";
/*     */     }
/* 111 */     if (!"".equals(str2)) {
/* 112 */       str10 = str10 + " and (a.dimensionName like '%" + StringEscapeUtils.escapeSql(str2) + "%'  or t.typeName like '%" + StringEscapeUtils.escapeSql(str2) + "%')";
/*     */     }
/*     */     
/* 115 */     if (!"".equals(stringBuffer)) {
/* 116 */       str10 = str10 + " and a.id not in (" + stringBuffer + ")";
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 124 */     String str11 = " a.dimensionName,t.typeName ";
/* 125 */     String str12 = "a.id";
/* 126 */     this.bb.writeLog("查询列表sql===select" + str8 + " from " + str9 + str10 + " order by" + str11);
/* 127 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 128 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 129 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name"));
/* 130 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(389137, this.user.getLanguage()), "type", "type"));
/* 131 */     arrayList.add(new SplitTableColBean("true", "name"));
/*     */     
/* 133 */     SplitTableBean splitTableBean = new SplitTableBean("Fna:fnaBudgetDimensionInner", "none", PageIdConst.getPageSize("Fna:fnaBudgetDimensionInner", this.user.getUID(), "Fna"), "Fna:fnaBudgetDimensionInner", str8, str9, str10, str11, str12, "ASC", arrayList);
/*     */     
/* 135 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 137 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 150 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 151 */     RecordSet recordSet = new RecordSet();
/* 152 */     LinkedList<HashMap<Object, Object>> linkedList = new LinkedList();
/* 153 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 154 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("accountId"));
/*     */     
/* 156 */     StringBuffer stringBuffer = new StringBuffer();
/* 157 */     String str3 = "select dimensionID from FnaDataSetDetail";
/* 158 */     recordSet.executeQuery(str3, new Object[0]);
/* 159 */     while (recordSet.next()) {
/* 160 */       stringBuffer.append("'" + recordSet.getString("dimensionID") + "',");
/*     */     }
/* 162 */     stringBuffer.append("'-1'");
/*     */     
/* 164 */     String str4 = "";
/* 165 */     String str5 = "select tableName from FnaAccountDtl where accountId=? and tableType=?";
/* 166 */     recordSet.executeQuery(str5, new Object[] { str2, Integer.valueOf(2) });
/* 167 */     if (recordSet.next()) {
/* 168 */       str4 = recordSet.getString("tableName");
/*     */     }
/*     */     
/* 171 */     String str6 = "select a.id, a.dimensionName, t.typeName from  " + str4 + " a,FnaDimensionType t  where t.id=a.typeId and  a.dimensionName like '%" + str1 + "%'";
/*     */     
/* 173 */     if (!"".equals(stringBuffer)) {
/* 174 */       str6 = str6 + "and a.id not in(" + stringBuffer + ")";
/*     */     }
/* 176 */     str6 = str6 + " ORDER BY a.dimensionName, a.id ";
/*     */ 
/*     */     
/* 179 */     this.bb.writeLog("dimensionName===" + str1);
/* 180 */     this.bb.writeLog("accountId===" + str2);
/* 181 */     this.bb.writeLog("tablename===" + str4);
/* 182 */     this.bb.writeLog("浏览按钮联想sql===" + str6);
/* 183 */     recordSet.executeQuery(str6, new Object[0]);
/*     */     
/* 185 */     while (recordSet.next()) {
/* 186 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 187 */       String str7 = recordSet.getString("id");
/* 188 */       String str8 = recordSet.getString("dimensionName");
/*     */       
/* 190 */       hashMap1.put("id", str7);
/* 191 */       hashMap1.put("name", str8);
/* 192 */       linkedList.add(hashMap1);
/*     */     } 
/* 194 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, linkedList);
/*     */     
/* 196 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BudgetDimensionBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */