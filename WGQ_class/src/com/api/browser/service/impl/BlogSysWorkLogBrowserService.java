/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ public class BlogSysWorkLogBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  30 */     String str1 = Util.null2String(paramMap.get("type"));
/*  31 */     String str2 = Util.null2String(paramMap.get("typeid"));
/*  32 */     String str3 = "t1.id , t1.modulename as name";
/*  33 */     String str4 = " blog_sysworklog_config t1";
/*  34 */     String str5 = "resourceType = " + str1 + " and resourceid =" + str2;
/*  35 */     String str6 = "t1.id";
/*     */     
/*  37 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  38 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  39 */     arrayList.add(new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(1278, this.user.getLanguage()), "name", "name", 1));
/*  40 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str5, str6, "t1.id", arrayList);
/*  41 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  42 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLableName(String paramString) {
/*  50 */     return SystemEnv.getHtmlLabelName(Util.getIntValue(paramString), this.user.getLanguage());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  58 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  59 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("type"));
/*  60 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("typeid"));
/*  61 */     String str4 = "select id,lableid from blog_sysworklog_config where modulename like '%" + str1 + "%' and resourceType = ? and resourceid = ?";
/*  62 */     RecordSet recordSet = new RecordSet();
/*  63 */     recordSet.executeQuery(str4, new Object[] { str2, str3 });
/*  64 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  65 */     while (recordSet.next()) {
/*  66 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  67 */       hashMap1.put("id", recordSet.getString("id"));
/*  68 */       hashMap1.put("name", SystemEnv.getHtmlLabelName(recordSet.getInt("lableid"), this.user.getLanguage()));
/*  69 */       arrayList.add(hashMap1);
/*     */     } 
/*  71 */     hashMap.put("datas", arrayList);
/*  72 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  77 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  78 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  79 */     if ("".equals(str1)) return (Map)hashMap; 
/*  80 */     RecordSet recordSet = new RecordSet();
/*  81 */     String str2 = Util.null2String(paramMap.get("type"));
/*  82 */     String str3 = Util.null2String(paramMap.get("typeid"));
/*  83 */     String str4 = "select id,lableid from blog_sysworklog_config where id in (" + str1 + ") and resourceType = ? and resourceid = ?";
/*  84 */     recordSet.executeQuery(str4, new Object[] { str2, str3 });
/*  85 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  87 */     while (recordSet.next()) {
/*  88 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  89 */       hashMap1.put("id", recordSet.getString("id"));
/*  90 */       hashMap1.put("name", SystemEnv.getHtmlLabelName(recordSet.getInt("lableid"), this.user.getLanguage()));
/*  91 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/*  94 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  95 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  96 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(1278, this.user.getLanguage()), 1, BoolAttr.TRUE));
/*  97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  98 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/*  99 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 100 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 105 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 106 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 107 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 108 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 32011, "id", true));
/* 109 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 28485, "name", true));
/* 110 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 111 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BlogSysWorkLogBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */