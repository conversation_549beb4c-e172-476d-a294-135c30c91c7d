/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmContractBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 33 */     String str1 = Util.null2String(paramMap.get("contractname"));
/* 34 */     String str2 = "id, contractname, contractman, contracttypeid, contractstartdate, contractenddate";
/* 35 */     String str3 = "from (select t.id, t.contractname, t.contractman, t.contracttypeid, t.contractstartdate, t.contractenddate from HrmContract t) t";
/* 36 */     String str4 = " where 1=1 ";
/* 37 */     if (str1.length() > 0) {
/* 38 */       str4 = str4 + " and contractname like '%" + str1 + "%'";
/*    */     }
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 44 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 45 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 46 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15142, this.user.getLanguage()), "contractname", null)).setIsInputCol(BoolAttr.TRUE));
/* 47 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15776, this.user.getLanguage()), "contractman", "contractman", "weaver.hrm.resource.ResourceComInfo.getLastname"));
/* 48 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(6158, this.user.getLanguage()), "contracttypeid", "contracttypeid", "weaver.hrm.contract.ContractTypeComInfo.getContractTypename"));
/* 49 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(1970, this.user.getLanguage()), "contractstartdate", null));
/* 50 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15236, this.user.getLanguage()), "contractenddate", null));
/* 51 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, Util.toHtmlForSplitPage(str4), "id", "id", arrayList);
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 59 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 60 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 61 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15142, "contractname", true));
/* 62 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 63 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmContractBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */