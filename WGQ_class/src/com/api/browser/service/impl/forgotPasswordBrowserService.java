/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class forgotPasswordBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  32 */     String str1 = Util.null2String(paramMap.get("forgottypename"));
/*  33 */     String str2 = Util.null2String(paramMap.get("id"));
/*  34 */     String str3 = "where 1 = 1";
/*     */     
/*  36 */     if (str1.length() > 0) {
/*  37 */       str3 = str3 + " and forgottypename like '%" + str1 + "%' ";
/*     */     }
/*     */     
/*  40 */     String str4 = " a.id,a.forgottype,b.labelname as forgottypename ";
/*  41 */     String str5 = " hrm_forgotPassword a , htmllabelinfo b ";
/*  42 */     str3 = str3 + " and a.forgottypelabel = b.indexid and b.languageid=" + this.user.getLanguage();
/*     */ 
/*     */     
/*  45 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  46 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  47 */     arrayList.add(new SplitTableColBean("true", "forgottype"));
/*  48 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(125976, this.user.getLanguage()), "forgottypename", "forgottypename", 1)).setIsInputCol(BoolAttr.TRUE));
/*     */     
/*  50 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, "id", "id", arrayList);
/*  51 */     splitTableBean.setSqlsortway("ASC");
/*  52 */     splitTableBean.setSqlisdistinct("true");
/*  53 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  54 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  60 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  61 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  62 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 125976, "forgottypename", true));
/*  63 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  64 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  70 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  71 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  72 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/*  73 */     String str2 = "";
/*  74 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  76 */     if (str1.length() > 0) {
/*  77 */       String[] arrayOfString = str1.split(",");
/*  78 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  79 */         str2 = "select a.id,a.forgottype,b.labelname as forgottypename from hrm_forgotPassword a , htmllabelinfo b where a.forgottypelabel = b.indexid and b.languageid= " + this.user.getLanguage() + " and id in ( " + arrayOfString[b] + ")";
/*  80 */         recordSet.execute(str2);
/*  81 */         if (recordSet.next()) {
/*  82 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  83 */           hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/*  84 */           hashMap1.put("forgottype", Util.null2String(recordSet.getString("forgottype")));
/*  85 */           hashMap1.put("forgottypename", Util.null2String(recordSet.getString("forgottypename")));
/*  86 */           arrayList.add(hashMap1);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  92 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  93 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  94 */     arrayList1.add(new ListHeadBean("forgottype", BoolAttr.TRUE));
/*  95 */     arrayList1.add((new ListHeadBean("forgottypename", "")).setIsInputCol(BoolAttr.TRUE));
/*     */     
/*  97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  98 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  99 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 100 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/forgotPasswordBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */