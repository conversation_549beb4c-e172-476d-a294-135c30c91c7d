/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerTypeService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  54 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  61 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  62 */     String str2 = Util.null2String(paramMap.get("fullname"));
/*  63 */     String str3 = Util.null2String(paramMap.get("description"));
/*  64 */     String str4 = " ";
/*  65 */     boolean bool = false;
/*  66 */     if (!str1.equals("") && 
/*  67 */       !bool) {
/*  68 */       bool = true;
/*  69 */       str4 = str4 + str1;
/*     */     } 
/*     */     
/*  72 */     if (!str2.equals("")) {
/*  73 */       if (!bool) {
/*  74 */         bool = true;
/*  75 */         str4 = str4 + " where fullname like '%";
/*  76 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  77 */         str4 = str4 + "%'";
/*     */       } else {
/*  79 */         str4 = str4 + " and fullname like '%";
/*  80 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  81 */         str4 = str4 + "%'";
/*     */       } 
/*     */     }
/*  84 */     if (!str3.equals("")) {
/*  85 */       if (!bool) {
/*  86 */         bool = true;
/*  87 */         str4 = str4 + " where description like '%";
/*  88 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/*  89 */         str4 = str4 + "%'";
/*     */       } else {
/*  91 */         str4 = str4 + " and description like '%";
/*  92 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/*  93 */         str4 = str4 + "%'";
/*     */       } 
/*     */     }
/*     */     
/*  97 */     String str5 = "id,fullname,description";
/*  98 */     String str6 = "CRM_CustomerType";
/*  99 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 100 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 101 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "fullname", "fullname", 1)).setIsInputCol(BoolAttr.TRUE));
/* 102 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*     */     
/* 104 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "orderkey", "id", "asc", arrayList);
/*     */     
/*     */     try {
/* 107 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 108 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"fullname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"description\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 109 */     } catch (Exception exception) {
/* 110 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 113 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 114 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 119 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 120 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 121 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 122 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "fullname", true));
/* 123 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description", false));
/* 124 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 125 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CustomerTypeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */