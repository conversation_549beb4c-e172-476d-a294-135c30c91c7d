/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.general.SplitPageParaBean;
/*     */ import weaver.general.SplitPageUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EmailOuterInputBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  48 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  50 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*  51 */     int i = 0;
/*     */     
/*     */     try {
/*  54 */       String str1 = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("q")), "UTF-8");
/*  55 */       String str2 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageSize")), "40");
/*  56 */       String str3 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageNum")), "1");
/*     */       
/*  58 */       if (!str1.isEmpty()) {
/*  59 */         RecordSet recordSet = new RecordSet();
/*  60 */         boolean bool = "oracle".equals(recordSet.getOrgindbtype());
/*     */         
/*  62 */         StringBuffer stringBuffer = new StringBuffer();
/*  63 */         if (bool) {
/*  64 */           stringBuffer.append("select distinct mailUserName as name, mailaddress as email from mailUserAddress where userId=" + this.user.getUID() + " and ( lower(mailaddress) like '%" + str1.toLowerCase() + "%' or lower(mailUserName) like '%" + str1.toLowerCase() + "%' )");
/*     */         } else {
/*  66 */           stringBuffer.append("select distinct mailUserName as name, mailaddress as email from mailUserAddress where userId=" + this.user.getUID() + " and ( mailaddress like '%" + str1 + "%' or mailUserName like '%" + str1 + "%' )");
/*     */         } 
/*  68 */         stringBuffer.append(" union ");
/*     */         
/*  70 */         String str4 = " #### like '%" + str1 + "%'";
/*  71 */         if (bool) {
/*  72 */           str4 = " lower(####) like '%" + str1.toLowerCase() + "%'";
/*     */         }
/*     */ 
/*     */         
/*  76 */         String str5 = "";
/*  77 */         AppDetachComInfo appDetachComInfo = new AppDetachComInfo();
/*  78 */         if (appDetachComInfo.isUseAppDetach()) {
/*  79 */           String str7 = appDetachComInfo.getScopeSqlByHrmResourceSearch(this.user.getUID() + "", false, "resource_hr");
/*  80 */           String str8 = (str7 != null && !"".equals(str7)) ? (" and " + str7) : "";
/*  81 */           str5 = str5 + str8;
/*     */         } 
/*  83 */         stringBuffer.append(" select distinct lastname as name, email from HrmResource hr where status in (0,1,2,3) and (" + ("oracle".equals(recordSet.getOrgindbtype()) ? "email is not null" : "email is not null and  email !=''") + ") and (" + str4.replaceAll("####", "lastname") + " or " + str4.replaceAll("####", "pinyinlastname") + " or " + str4.replaceAll("####", "email") + ")  " + str5);
/*     */         
/*  85 */         stringBuffer.append(" union ");
/*     */ 
/*     */         
/*  88 */         recordSet.executeQuery("select id from HrmRoleMembers where  roleid = 8 and rolelevel = 2 and resourceid = ?", new Object[] { Integer.valueOf(this.user.getUID()) });
/*  89 */         if (recordSet.next()) {
/*  90 */           stringBuffer.append(" select distinct name,email from CRM_CustomerInfo where (deleted=0 or deleted is null)  and  email is not null and (" + str4.replaceAll("####", "name") + " or " + str4.replaceAll("####", "email") + ") ");
/*     */         } else {
/*  92 */           CrmShareBase crmShareBase = new CrmShareBase();
/*  93 */           String str = crmShareBase.getTempTable(this.user.getUID() + "");
/*  94 */           stringBuffer.append(" select distinct t1.name,t1.email  from CRM_CustomerInfo t1 left join " + str + " t2 on t1.id = t2.relateditemid  where t1.id = t2.relateditemid and (t1.deleted=0 or t1.deleted is null) and (" + (
/*     */               
/*  96 */               "oracle".equals(recordSet.getOrgindbtype()) ? "t1.email is not null" : "t1.email is not null and t1.email !=''") + ") and " + str4.replaceAll("####", "t1.name") + " ");
/*     */         } 
/*     */         
/*  99 */         String str6 = "";
/* 100 */         if ("oracle".equals(recordSet.getDBType())) {
/*     */           
/* 102 */           str6 = "email is not null ";
/*     */         } else {
/*     */           
/* 105 */           str6 = "email is not null and  email !=''";
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 110 */         SplitPageParaBean splitPageParaBean = new SplitPageParaBean();
/* 111 */         SplitPageUtil splitPageUtil = new SplitPageUtil();
/* 112 */         splitPageParaBean.setSqlFrom("(" + stringBuffer.toString() + ") a");
/* 113 */         splitPageParaBean.setBackFields("name, email");
/* 114 */         splitPageParaBean.setPrimaryKey("email");
/* 115 */         splitPageParaBean.setSqlWhere(str6);
/* 116 */         splitPageUtil.setSpp(splitPageParaBean);
/* 117 */         recordSet = splitPageUtil.getCurrentPageRs(Integer.valueOf(str3).intValue(), Integer.valueOf(str2).intValue());
/* 118 */         i = splitPageUtil.getRecordCount();
/*     */         
/* 120 */         HashSet<String> hashSet = new HashSet();
/* 121 */         while (recordSet.next()) {
/* 122 */           String str = Util.null2String(recordSet.getString("email"));
/* 123 */           if (StringUtils.isNotEmpty(str) && !hashSet.contains(str)) {
/* 124 */             hashSet.add(str);
/* 125 */             arrayList.add(getSearchObj(str, recordSet.getString("name")));
/*     */           } 
/*     */         } 
/*     */       } 
/* 129 */     } catch (Exception exception) {
/* 130 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 133 */     hashMap.put("count", Integer.valueOf(i));
/* 134 */     hashMap.put("datas", arrayList);
/*     */ 
/*     */ 
/*     */     
/* 138 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private JSONObject getSearchObj(String paramString1, String paramString2) {
/* 142 */     JSONObject jSONObject = new JSONObject();
/* 143 */     jSONObject.put("id", paramString1);
/* 144 */     jSONObject.put("name", paramString2 + "&lt;" + ' ' + paramString1 + ' ' + "&gt;");
/* 145 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 158 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 159 */     RecordSet recordSet = new RecordSet();
/* 160 */     String str1 = "";
/* 161 */     if ("oracle".equals(recordSet.getOrgindbtype())) {
/* 162 */       str1 = " email is not null ";
/*     */     } else {
/* 164 */       str1 = " email is not null and  email !=''";
/*     */     } 
/*     */     
/* 167 */     String str2 = Util.null2String(paramMap.get("email"));
/* 168 */     if (!str2.equals("")) {
/* 169 */       str1 = str1 + " and t.email like '%" + str2 + "%'";
/*     */     }
/* 171 */     String str3 = Util.null2String(paramMap.get("name"));
/* 172 */     if (!str3.equals("")) {
/* 173 */       str1 = str1 + " and (t.name like '%" + str3 + "%' or t.email like '%" + str3 + "%')";
/*     */     }
/*     */     
/* 176 */     String str4 = " name , email as id";
/* 177 */     if ("oracle".equals(recordSet.getDBType())) {
/* 178 */       str4 = " name || '&lt;' ||' '|| email||' ' ||'&gt;' as name , email as id ,'resource' type";
/* 179 */     } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 180 */       str4 = " name +'&lt;'+' '+email+' '+'&gt;' as name , email as id ,'resource' type";
/* 181 */     } else if ("mysql".equals(recordSet.getDBType())) {
/* 182 */       str4 = " CONCAT(name,'&lt;',' ',email,' ','&gt;') as name , email as id ,'resource' type";
/*     */     } else {
/* 184 */       str4 = " name || '&lt;' ||' '|| email||' ' ||'&gt;' as name , email as id ,'resource' type";
/*     */     } 
/*     */     
/* 187 */     StringBuffer stringBuffer = new StringBuffer();
/* 188 */     stringBuffer.append("select distinct mailUserName as name, mailaddress as email from mailUserAddress where userId=" + this.user.getUID());
/* 189 */     stringBuffer.append(" union ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 203 */     recordSet.executeQuery("select id from HrmRoleMembers where  roleid = 8 and rolelevel = 2 and resourceid = ?", new Object[] { Integer.valueOf(this.user.getUID()) });
/* 204 */     if (recordSet.next()) {
/* 205 */       stringBuffer.append(" select distinct name,email from CRM_CustomerInfo where (deleted=0 or deleted is null)  and  email is not null ");
/*     */     } else {
/* 207 */       CrmShareBase crmShareBase = new CrmShareBase();
/* 208 */       String str = crmShareBase.getTempTable(this.user.getUID() + "");
/* 209 */       stringBuffer.append(" select distinct t1.name,t1.email  from CRM_CustomerInfo t1 left join " + str + " t2 on t1.id = t2.relateditemid  where t1.id = t2.relateditemid and (t1.deleted=0 or t1.deleted is null) and t1.email is not null ");
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 215 */     String str5 = "(" + stringBuffer.toString() + ") t";
/*     */     
/* 217 */     String str6 = "name";
/*     */ 
/*     */     
/* 220 */     List list = Util_MobileData.createList("col1", "col1_row1", "name", "", "col2_row1", "id", "");
/* 221 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createJsonTemplateBean("theme_default", list);
/*     */     
/* 223 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 224 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */     
/* 226 */     arrayList.add(new SplitTableColBean("true", "name"));
/* 227 */     arrayList.add(new SplitTableColBean("true", "type"));
/*     */     
/* 229 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "name", "name"));
/*     */ 
/*     */     
/* 232 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str1, str6, "id", arrayList);
/* 233 */     splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/* 234 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */ 
/*     */     
/* 237 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 242 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 243 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 244 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 245 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 413, "name", true));
/* 246 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 21896, "email"));
/* 247 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 248 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EmailOuterInputBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */