/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.fnaMulDimensions.entity.FnaBrowserTreeNodeBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.maintenance.CostCenterComInfo;
/*     */ import weaver.fna.maintenance.FnaCostCenter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MultiCostCenterBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  60 */     if (this.user == null) {
/*  61 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  62 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  65 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  66 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/*  68 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 515, "fccname").setIsQuickSearch(true));
/*  69 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1321, "fcccode"));
/*     */     
/*  71 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/*  73 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  87 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  89 */     if (this.user == null) {
/*  90 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  91 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  94 */     int i = Util.getIntValue(Util.null2String(paramMap.get("qryType")), 0);
/*     */     
/*     */     try {
/*  97 */       if (i == 1) {
/*  98 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  99 */         SplitTableBean splitTableBean = getTableList(paramMap);
/* 100 */         hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */       } else {
/* 102 */         int j = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/* 103 */         if (j == 0) {
/* 104 */           ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 105 */           arrayList.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 106 */           arrayList.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/* 107 */           arrayList.add(new ListHeadBean("code", ""));
/* 108 */           hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */           
/* 110 */           hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 111 */           List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 112 */           if (list.size() == 0) {
/* 113 */             BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 114 */             browserTreeNode.setId("-1");
/* 115 */             browserTreeNode.setName(SystemEnv.getHtmlLabelName(515, this.user.getLanguage()));
/* 116 */             browserTreeNode.setPid("0");
/* 117 */             browserTreeNode.setParent(true);
/* 118 */             browserTreeNode.setType("0");
/* 119 */             browserTreeNode.setCanClick(false);
/* 120 */             browserTreeNode.setIcon("icon-coms-LargeArea");
/*     */             
/* 122 */             list.add(browserTreeNode);
/*     */           } 
/*     */           
/* 125 */           hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */         } else {
/* 127 */           hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 128 */           List<BrowserTreeNode> list = getTreeNodeInfo(paramMap);
/* 129 */           hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */         } 
/*     */       } 
/* 132 */     } catch (Exception exception) {
/* 133 */       (new BaseBean()).writeLog(exception);
/*     */     } 
/*     */     
/* 136 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 147 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 149 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 150 */     String str2 = Util.null2String(paramMap.get("alllevel"));
/* 151 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 153 */     CostCenterComInfo costCenterComInfo = new CostCenterComInfo();
/* 154 */     if ("1".equals(str2)) {
/* 155 */       List<String> list = CostCenterComInfo.recursiveSubordinate(str1);
/*     */       
/* 157 */       str1 = "";
/* 158 */       int i = list.size();
/* 159 */       for (byte b = 0; b < i; b++) {
/* 160 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 161 */         String str = list.get(b);
/* 162 */         hashMap1.put("id", str);
/* 163 */         hashMap1.put("name", costCenterComInfo.getName(str));
/* 164 */         hashMap1.put("code", costCenterComInfo.getCode(str));
/* 165 */         arrayList.add(hashMap1);
/*     */         
/* 167 */         str1 = str1 + str + ",";
/*     */       } 
/*     */       
/* 170 */       if (!"".equals(str1)) {
/* 171 */         str1 = str1.substring(0, str1.length() - 1);
/*     */       }
/*     */     } else {
/* 174 */       String[] arrayOfString = str1.split(",");
/* 175 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 176 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 177 */         String str = arrayOfString[b];
/* 178 */         hashMap1.put("id", str);
/* 179 */         hashMap1.put("name", costCenterComInfo.getName(str));
/* 180 */         hashMap1.put("code", costCenterComInfo.getCode(str));
/* 181 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/*     */     
/* 185 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 186 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 187 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/* 188 */     arrayList1.add(new ListHeadBean("code", ""));
/*     */     
/* 190 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 191 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 192 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 194 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/* 206 */     String str1 = Util.null2String(paramMap.get("fccname")).trim();
/* 207 */     String str2 = Util.null2String(paramMap.get("fcccode")).trim();
/*     */     
/* 209 */     int i = Util.getIntValue(Util.null2String(paramMap.get("fccGroupId")), -1);
/*     */     
/* 211 */     String str3 = "10";
/*     */     
/* 213 */     String str4 = " a.id,a.name,a.code,a.displayOrder ";
/* 214 */     String str5 = " FnaCostCenter a";
/* 215 */     String str6 = " where (a.Archive is null or a.Archive = 0) and a.type = 1 ";
/* 216 */     if (i > -1) {
/* 217 */       str6 = str6 + " and a.supFccId = " + i + " ";
/*     */     }
/* 219 */     if (!"".equals(str1)) {
/* 220 */       str6 = str6 + " and a.name like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/* 222 */     if (!"".equals(str2)) {
/* 223 */       str6 = str6 + " and a.code like '%" + StringEscapeUtils.escapeSql(str2) + "%' ";
/*     */     }
/*     */     
/* 226 */     String str7 = " a.displayOrder,a.code,a.name ";
/* 227 */     String str8 = "a.id";
/*     */     
/* 229 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 230 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 231 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/* 232 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(1321, this.user.getLanguage()), "code", "code", "weaver.fna.general.FnaCommon.escapeHtml"));
/*     */ 
/*     */     
/* 235 */     SplitTableBean splitTableBean = new SplitTableBean("FccBrowserList", "none", str3, "FccBrowserList", str4, str5, str6, str7, str8, "ASC", arrayList);
/*     */     
/* 237 */     splitTableBean.setSqlisdistinct("true");
/*     */     
/* 239 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<BrowserTreeNode> getTreeNodeInfo(Map<String, Object> paramMap) {
/* 250 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*     */     
/* 252 */     RecordSet recordSet = new RecordSet();
/* 253 */     FnaCommon fnaCommon = new FnaCommon();
/* 254 */     FnaCostCenter fnaCostCenter = new FnaCostCenter();
/*     */     
/* 256 */     String str1 = Util.null2String(paramMap.get("id"));
/* 257 */     String str2 = Util.null2String(paramMap.get("wfid"));
/* 258 */     String str3 = Util.null2String(paramMap.get("fieldid"));
/*     */     
/* 260 */     if ("".equals(str1)) {
/* 261 */       str1 = "0";
/*     */     }
/*     */     
/* 264 */     if ("".equals(str1)) {
/* 265 */       String str4 = "0";
/* 266 */       String str5 = SystemEnv.getHtmlLabelName(515, this.user.getLanguage());
/* 267 */       String str6 = "0";
/*     */       
/* 269 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/* 270 */       browserTreeNode.setId(str4);
/* 271 */       browserTreeNode.setName(str5);
/* 272 */       browserTreeNode.setType(str6);
/* 273 */       browserTreeNode.setIsParent(true);
/* 274 */       arrayList.add(browserTreeNode);
/*     */     }
/*     */     else {
/*     */       
/* 278 */       List list = fnaCommon.getWfBrowdefList(str2, str3, "251");
/* 279 */       HashSet hashSet1 = new HashSet();
/* 280 */       HashSet hashSet2 = new HashSet();
/* 281 */       if (list.size() > 0) {
/* 282 */         fnaCostCenter.getAllSubCostcenterType(list, hashSet1, hashSet2);
/*     */       }
/*     */ 
/*     */       
/* 286 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 287 */       recordSet.executeQuery(" select id, supFccId from FnaCostCenter ", new Object[0]);
/* 288 */       while (recordSet.next()) {
/* 289 */         String str4 = Util.null2String(recordSet.getString("supFccId"));
/* 290 */         if (!"0".equals(str4)) {
/* 291 */           String str5 = (String)hashMap.get(str4);
/* 292 */           if (str5 != null) {
/* 293 */             int i = Integer.parseInt(str5) + 1;
/* 294 */             hashMap.put(str4, String.valueOf(i)); continue;
/*     */           } 
/* 296 */           hashMap.put(str4, "1");
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 302 */       String str = "select a.id, a.type, a.name, a.code, a.Archive, a.supFccId, a.displayOrder from FnaCostCenter a  where a.supFccId = " + Util.getIntValue(str1) + " ORDER BY a.type, a.displayOrder, a.code, a.name, a.id ";
/*     */       
/* 304 */       recordSet.executeQuery(str, new Object[0]);
/* 305 */       while (recordSet.next()) {
/* 306 */         String str4 = recordSet.getString("id");
/* 307 */         int i = Util.getIntValue(recordSet.getString("type"));
/*     */         
/* 309 */         String str5 = recordSet.getString("name");
/*     */         
/* 311 */         int j = Util.getIntValue(recordSet.getString("Archive"), 0);
/*     */         
/* 313 */         String str6 = str5;
/* 314 */         if (j == 1) {
/* 315 */           str6 = str6 + "(" + SystemEnv.getHtmlLabelName(22205, this.user.getLanguage()) + ")";
/*     */           
/*     */           continue;
/*     */         } 
/* 319 */         if (list.size() > 0 && !hashSet1.contains(str4) && !hashSet2.contains(str4)) {
/*     */           continue;
/*     */         }
/*     */         
/* 323 */         String str7 = "icon-coms-LargeArea";
/* 324 */         if (i == 1) {
/* 325 */           str7 = "icon-coms-Branch";
/*     */         }
/*     */         
/* 328 */         boolean bool = true;
/* 329 */         if (i == 1) {
/* 330 */           bool = false;
/*     */         }
/* 332 */         else if (hashMap.get(str4) != null) {
/* 333 */           bool = true;
/*     */         } else {
/* 335 */           bool = false;
/*     */         } 
/*     */ 
/*     */         
/* 339 */         FnaBrowserTreeNodeBean fnaBrowserTreeNodeBean = new FnaBrowserTreeNodeBean();
/* 340 */         fnaBrowserTreeNodeBean.setId(str4);
/* 341 */         fnaBrowserTreeNodeBean.setPid(str1);
/* 342 */         fnaBrowserTreeNodeBean.setName(str6);
/* 343 */         fnaBrowserTreeNodeBean.setIsParent(bool);
/* 344 */         fnaBrowserTreeNodeBean.setType(String.valueOf(i));
/* 345 */         fnaBrowserTreeNodeBean.setIcon(str7);
/* 346 */         fnaBrowserTreeNodeBean.setCanClick((i == 1));
/*     */         
/* 348 */         arrayList.add(fnaBrowserTreeNodeBean);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 353 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MultiCostCenterBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */