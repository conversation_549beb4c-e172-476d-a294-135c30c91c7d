/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.blog.util.BlogCommonUtils;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.blog.BlogDao;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BlogTemplateBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  34 */     String str1 = "";
/*  35 */     String str2 = Util.null2String(paramMap.get("tempName"));
/*  36 */     if (!str2.equals("")) {
/*  37 */       str1 = str1 + " and t1.tempName like '%" + str2 + "%'";
/*     */     }
/*  39 */     String str3 = Util.null2String(paramMap.get("tempDesc"));
/*  40 */     if (!str3.equals("")) {
/*  41 */       str1 = str1 + " and t1.tempDesc like '%" + str3 + "%'";
/*     */     }
/*  43 */     String str4 = Util.null2String(paramMap.get("isEcme"));
/*     */     
/*  45 */     if (!str4.equals("")) {
/*  46 */       str1 = str1 + " and t1.isEcme = " + str4;
/*     */     }
/*     */ 
/*     */     
/*  50 */     BlogDao blogDao = new BlogDao();
/*     */     
/*  52 */     String str5 = "id , tempName ,tempDesc,isecme as contentType,isecme, isUsed ,userId,tempContent as name,isSystem,case isSystem when '1' then '" + SystemEnv.getHtmlLabelName(83158, this.user.getLanguage()) + "' else '" + SystemEnv.getHtmlLabelName(83159, this.user.getLanguage()) + "' END isSystem_str , case isecme when 1 then '" + SystemEnv.getHtmlLabelName(10004829, this.user.getLanguage()) + "' else '" + SystemEnv.getHtmlLabelName(386758, this.user.getLanguage()) + "' end isEcme_str";
/*  53 */     String str6 = "from blog_template t1 left join " + blogDao.getTemplateTable(this.user.getUID() + "") + "t2 on t1.id = t2.tempid";
/*  54 */     str1 = "((t1.id = t2.tempid and isUsed = 1) or (isSystem = 0 and userId = '" + this.user.getUID() + "'))" + str1;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  61 */     String str7 = "isSystem , id";
/*     */ 
/*     */     
/*  64 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  65 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  70 */     arrayList.add(new SplitTableColBean("true", "name"));
/*  71 */     arrayList.add(new SplitTableColBean("true", "contentType"));
/*  72 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "tempName", "tempName"));
/*  73 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18627, this.user.getLanguage()), "tempDesc", "tempDesc"));
/*  74 */     arrayList.add(new SplitTableColBean("18%", SystemEnv.getHtmlLabelName(524504, this.user.getLanguage()), "isEcme_str", "isEcme_str"));
/*  75 */     arrayList.add(new SplitTableColBean("18%", SystemEnv.getHtmlLabelName(20622, this.user.getLanguage()), "isSystem_str", "isSystem_str"));
/*     */     
/*  77 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str5, str6, str1, str7, "id", arrayList)));
/*  78 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  83 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  84 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  85 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  86 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18151, "tempName", true));
/*  87 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18627, "tempDesc"));
/*     */     
/*  89 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  91 */     recordSet.executeQuery("select isenableecme from blog_sysSetting where resourcetype =? and resourceid = ? ", new Object[] { Integer.valueOf(0), Integer.valueOf(0) });
/*     */     
/*  93 */     recordSet.next();
/*     */     
/*  95 */     if (!recordSet.getString("isenableecme").equals("0")) {
/*  96 */       arrayList.add(new SearchConditionItem(ConditionType.SELECT, SystemEnv.getHtmlLabelName(524504, this.user.getLanguage()), "", new String[] { "isEcme" }, BlogCommonUtils.getBlogTemplateContentOption(this.user.getLanguage()), 6, 18, null));
/*     */     }
/*     */ 
/*     */     
/* 100 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 101 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BlogTemplateBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */