/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomQueryTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  38 */     String str1 = Util.null2String(paramMap.get("typename"));
/*  39 */     String str2 = Util.null2String(paramMap.get("typenamemark"));
/*  40 */     String str3 = " ";
/*  41 */     if (!str1.equals("")) {
/*  42 */       str3 = str3 + " and typename like '%";
/*  43 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/*  44 */       str3 = str3 + "%'";
/*     */     } 
/*  46 */     if (!str2.equals("")) {
/*  47 */       str3 = str3 + " where typenamemark like '%";
/*  48 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/*  49 */       str3 = str3 + "%'";
/*     */     } 
/*     */     
/*  52 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*     */ 
/*     */     
/*  55 */     String str4 = "id,typename,typenamemark,showorder";
/*  56 */     String str5 = " workflow_customQuerytype ";
/*  57 */     String str6 = "showorder";
/*     */     
/*  59 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  60 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  61 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15520, this.user.getLanguage()), "typename", null, 1)).setIsInputCol(BoolAttr.TRUE));
/*  62 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15521, this.user.getLanguage()), "typenamemark", null, 0));
/*     */     
/*  64 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/*  65 */     splitTableBean.setSqlsortway("ASC");
/*  66 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  67 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  72 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  73 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  74 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  75 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  76 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15520, "typename", true));
/*  77 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15521, "typenamemark"));
/*  78 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  83 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  84 */     RecordSet recordSet = new RecordSet();
/*  85 */     String str2 = "where 1=1 ";
/*  86 */     if (!"".equals(str1)) {
/*  87 */       str2 = str2 + " and (typename like '%" + str1 + "%' ";
/*  88 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/*  90 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/*  91 */           str2 = str2 + " or f_GetPy(typename) like '%" + str1.toUpperCase() + "%'";
/*  92 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/*  93 */           str2 = str2 + " or [dbo].f_GetPy(typename) like '%" + str1.toUpperCase() + "%'";
/*  94 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/*  95 */           str2 = str2 + " or getpinyin(typename) like '%" + str1.toUpperCase() + "%'";
/*     */         }  } 
/*  97 */       str2 = str2 + ")";
/*     */     } 
/*  99 */     recordSet.executeQuery("select id,typename from WORKFLOW_CUSTOMQUERYTYPE " + str2 + " order by showorder", new Object[0]);
/* 100 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 101 */     while (recordSet.next()) {
/* 102 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 103 */       hashMap1.put("id", recordSet.getString("id"));
/* 104 */       hashMap1.put("name", recordSet.getString("typename"));
/* 105 */       arrayList.add(hashMap1);
/*     */     } 
/* 107 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 108 */     hashMap.put("datas", arrayList);
/* 109 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CustomQueryTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */