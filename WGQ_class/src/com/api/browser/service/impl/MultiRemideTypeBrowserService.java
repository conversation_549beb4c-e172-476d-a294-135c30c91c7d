/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.meeting.MeetingBrowser;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class MultiRemideTypeBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  25 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  26 */     Map map = MeetingBrowser.getRemindMap();
/*  27 */     Iterator<String> iterator = map.keySet().iterator();
/*  28 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  29 */     List list = null;
/*  30 */     if (!"".equals(str)) {
/*  31 */       list = Util.splitString2List(str, ",");
/*     */     }
/*  33 */     while (iterator.hasNext()) {
/*  34 */       String str1 = iterator.next();
/*  35 */       if (list != null && list.size() > 0 && !list.contains(str1)) {
/*     */         continue;
/*     */       }
/*  38 */       String str2 = MeetingBrowser.getRemindName(Util.getIntValue(str1), this.user.getLanguage());
/*  39 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  40 */       arrayList.add(hashMap1);
/*  41 */       hashMap1.put("id", str1);
/*  42 */       hashMap1.put("name", str2);
/*  43 */       hashMap1.put("randomFieldId", str1);
/*     */     } 
/*     */     
/*  46 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  47 */     arrayList1.add(new ListHeadBean(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, BoolAttr.TRUE));
/*  48 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  49 */     ListHeadBean listHeadBean = (new ListHeadBean("name", SystemEnv.getHtmlLabelName(18713, this.user.getLanguage()), 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT);
/*  50 */     listHeadBean.setDisplay(BoolAttr.FALSE);
/*  51 */     arrayList1.add(listHeadBean);
/*  52 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  53 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  54 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */ 
/*     */     
/*  57 */     hashMap.put("mobileshowtype", MobileShowTypeAttr.ListView);
/*  58 */     hashMap.put("mobileshowtemplate", MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/*     */     
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/*  68 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/*  69 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.name");
/*  70 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  80 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  81 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  82 */     if ("".equals(str)) return (Map)hashMap; 
/*  83 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  84 */     Map map = MeetingBrowser.getRemindMap();
/*  85 */     Iterator<String> iterator = map.keySet().iterator();
/*  86 */     if (!"".equals(str)) {
/*  87 */       List list = Util.splitString2List(str, ",");
/*  88 */       while (iterator.hasNext()) {
/*  89 */         String str1 = iterator.next();
/*  90 */         if (list != null && list.size() > 0 && !list.contains(str1)) {
/*     */           continue;
/*     */         }
/*  93 */         String str2 = MeetingBrowser.getRemindName(Util.getIntValue(str1), this.user.getLanguage());
/*  94 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  95 */         arrayList.add(hashMap1);
/*  96 */         hashMap1.put("id", str1);
/*  97 */         hashMap1.put("name", str2);
/*  98 */         hashMap1.put(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, str1);
/*     */       } 
/*     */       
/* 101 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*     */       
/* 103 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 104 */       arrayList1.add(new ListHeadBean(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, BoolAttr.TRUE));
/* 105 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 106 */       ListHeadBean listHeadBean = (new ListHeadBean("name", SystemEnv.getHtmlLabelName(18713, this.user.getLanguage()), 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT);
/* 107 */       listHeadBean.setDisplay(BoolAttr.FALSE);
/* 108 */       arrayList1.add(listHeadBean);
/* 109 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 110 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/* 112 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MultiRemideTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */