/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocTemplateService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  63 */     String str1 = Util.null2String(paramMap.get("doctype"));
/*  64 */     if (str1 != null && "".equals(str1)) { str1 = "0"; }
/*  65 */     else if (str1 != null && !"".equals(str1))
/*  66 */     { if (str1.equals(".htm")) { str1 = "0"; }
/*  67 */       else if (str1.equals(".doc")) { str1 = "2"; }
/*  68 */       else if (str1.equals(".xls")) { str1 = "3"; }
/*  69 */       else if (str1.equals(".wps")) { str1 = "4"; }
/*     */        }
/*  71 */      RecordSet recordSet = new RecordSet();
/*  72 */     String str2 = "id , mouldname ";
/*  73 */     String str3 = " DocMouldFile ";
/*  74 */     String str4 = " where 1=1 ";
/*  75 */     String str5 = " id ";
/*     */     
/*  77 */     str4 = str4 + " and mouldtype=" + str1;
/*     */     
/*  79 */     if (recordSet.getDBType().equals("postgresql")) {
/*  80 */       str4 = str4 + " and (templateid is null or templateid = 0)";
/*     */     } else {
/*  82 */       str4 = str4 + " and (templateid is null or templateid = '' or templateid = 0) ";
/*     */     } 
/*     */ 
/*     */     
/*  86 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  87 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  88 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id", 1)).setIsInputCol(BoolAttr.TRUE));
/*  89 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "mouldname", "mouldname")).setIsInputCol(BoolAttr.TRUE));
/*     */ 
/*     */     
/*  92 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, "id", arrayList);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 100 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 101 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 106 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 107 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 108 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("doctype"));
/* 109 */     if (str2 != null && "".equals(str2)) { str2 = "0"; }
/* 110 */     else if (str2 != null && !"".equals(str2))
/* 111 */     { if (str2.equals(".htm")) { str2 = "0"; }
/* 112 */       else if (str2.equals(".doc")) { str2 = "2"; }
/* 113 */       else if (str2.equals(".xls")) { str2 = "3"; }
/* 114 */       else if (str2.equals(".wps")) { str2 = "4"; }
/*     */        }
/* 116 */      String str3 = "select id,mouldname,mouldType from DocMouldFile WHERE ID not IN (Select TEMPLETDOCID From HrmContractTemplet)   ";
/*     */     
/* 118 */     if (!str1.isEmpty()) {
/* 119 */       str1 = str1.replace("'", "''");
/* 120 */       str3 = str3 + " and mouldname like '%" + str1 + "%'";
/*     */     } 
/* 122 */     if (str2 != null && !"".equals(str2)) {
/* 123 */       str3 = str3 + " and mouldtype = ?";
/*     */     }
/*     */     
/* 126 */     str3 = str3 + " order by id desc";
/* 127 */     RecordSet recordSet = new RecordSet();
/* 128 */     if (str2 != null && !"".equals(str2)) {
/* 129 */       recordSet.executeQuery(str3, new Object[] { str2 });
/*     */     } else {
/* 131 */       recordSet.executeQuery(str3, new Object[0]);
/*     */     } 
/* 133 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 134 */     while (recordSet.next()) {
/* 135 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 136 */       hashMap1.put("id", recordSet.getString("id"));
/* 137 */       hashMap1.put("name", recordSet.getString("mouldname"));
/* 138 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 141 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 142 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DocTemplateService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */