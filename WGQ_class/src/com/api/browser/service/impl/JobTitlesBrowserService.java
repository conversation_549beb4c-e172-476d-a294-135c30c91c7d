/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JobTitlesBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  38 */     String str1 = Util.null2String(paramMap.get("jobtitlemark"));
/*  39 */     String str2 = Util.null2String(paramMap.get("jobtitlename"));
/*  40 */     String str3 = Util.null2String(paramMap.get("jobtitlecode"));
/*  41 */     String str4 = Util.null2String(paramMap.get("jobactivityname"));
/*  42 */     String str5 = "where 1 = 1 ";
/*  43 */     String str6 = "";
/*  44 */     String str7 = "";
/*  45 */     String str8 = "";
/*  46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  47 */     SplitTableBean splitTableBean = null;
/*  48 */     if ("278".equals(this.browserType)) {
/*  49 */       str6 = "c.id,c.jobtitlemark,c.jobtitlename,a.jobgroupname,a.jobgroupremark,b.jobactivitymark";
/*  50 */       str7 = "from HrmJobTitles c left join HrmJobActivities b on b.id = c.jobactivityid left join HrmJobGroups a on a.id = b.jobgroupid";
/*  51 */       str8 = "c.id";
/*  52 */       str5 = " where (c.canceled is null or c.canceled <> 1)";
/*  53 */       String str9 = Util.null2String(paramMap.get("jobgroupid"));
/*  54 */       String str10 = Util.null2String(paramMap.get("jobactivitieid"));
/*  55 */       if (!str9.equals("")) {
/*  56 */         str5 = str5 + " and a.id =" + str9;
/*     */       }
/*  58 */       if (!str10.equals("")) {
/*  59 */         str5 = str5 + " and b.id= " + str10;
/*     */       }
/*  61 */       if (!str1.equals("")) {
/*  62 */         str5 = str5 + " and c.jobtitlemark like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */       }
/*  64 */       if (!str2.equals("")) {
/*  65 */         str5 = str5 + " and c.jobtitlename like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */       }
/*  67 */       if (!str3.equals("")) {
/*  68 */         str5 = str5 + " and c.jobtitlecode like '%" + Util.fromScreen2(str3, this.user.getLanguage()) + "%' ";
/*     */       }
/*     */       
/*  71 */       arrayList.add(new SplitTableColBean("true", "id"));
/*  72 */       arrayList.add((new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(6086, this.user.getLanguage()), "jobtitlemark", "jobtitlemark", 1)).setIsInputCol(BoolAttr.TRUE));
/*  73 */       arrayList.add(new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(1915, this.user.getLanguage()), "jobactivitymark", "jobactivitymark"));
/*  74 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(805, this.user.getLanguage()), "jobgroupremark", "jobgroupremark"));
/*  75 */       splitTableBean = new SplitTableBean(str6, str7, str5, str8, "c.id", arrayList);
/*     */     } else {
/*  77 */       str6 = " a.id as id,a.jobtitlemark as jobtitlemark,a.jobtitlename as jobtitlename, b.jobactivityname as jobactivityname, b.jobactivitymark as jobactivitymark , a.jobtitlecode as jobtitlecode";
/*  78 */       str7 = " from HrmJobTitles a left join HrmJobActivities b on a.jobactivityid=b.id ";
/*  79 */       str8 = " a.id ";
/*  80 */       str5 = str5 + " and (a.canceled is null or a.canceled <> 1)";
/*  81 */       if (!"".equals(str1)) {
/*  82 */         str5 = str5 + " and a.jobtitlemark like '%" + str1 + "%'";
/*     */       }
/*     */       
/*  85 */       if (!"".equals(str2)) {
/*  86 */         str5 = str5 + " and a.jobtitlename like '%" + str2 + "%'";
/*     */       }
/*     */       
/*  89 */       if (!"".equals(str3)) {
/*  90 */         str5 = str5 + " and a.jobtitlecode like '%" + str3 + "%'";
/*     */       }
/*     */       
/*  93 */       if (!"".equals(str4)) {
/*  94 */         str5 = str5 + " and jobactivityname like '%" + str4 + "%'";
/*     */       }
/*  96 */       arrayList.add(new SplitTableColBean("true", "id"));
/*  97 */       arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15767, this.user.getLanguage()), "jobtitlename", "jobtitlename"));
/*  98 */       arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "jobtitlemark", "jobtitlemark")).setIsInputCol(BoolAttr.TRUE));
/*  99 */       arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(524944, this.user.getLanguage()), "jobtitlecode", "jobtitlecode"));
/* 100 */       arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(1915, this.user.getLanguage()), "jobactivitymark", "jobactivitymark"));
/* 101 */       splitTableBean = new SplitTableBean(str6, str7, str5, str8, "a.id", arrayList);
/*     */     } 
/*     */     
/* 104 */     splitTableBean.setSqlsortway("ASC");
/* 105 */     splitTableBean.setSqlisdistinct("true");
/* 106 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 107 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 112 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 113 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 114 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 115 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 116 */     if ("278".equals(this.browserType)) {
/* 117 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 805, "jobgroupid", "281"));
/* 118 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 15855, "jobactivitieid", "282"));
/*     */     } 
/* 120 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15767, "jobtitlename"));
/* 121 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "jobtitlemark", true));
/* 122 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 524944, "jobtitlecode"));
/* 123 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 128 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 129 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 130 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 131 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 132 */     RecordSet recordSet = new RecordSet();
/* 133 */     String str2 = " select c.id,c.jobtitlemark,c.jobtitlename,a.jobgroupname,a.jobgroupremark,b.jobactivitymark  from HrmJobTitles c left join HrmJobActivities b on b.id = c.jobactivityid left join HrmJobGroups a on a.id = b.jobgroupid  where c.id in (" + str1 + ") order by c.jobtitlemark ";
/*     */ 
/*     */ 
/*     */     
/* 137 */     recordSet.executeSql(str2);
/* 138 */     while (recordSet.next()) {
/* 139 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 140 */       hashMap1.put("id", recordSet.getString("id"));
/* 141 */       hashMap1.put("jobtitlemark", Util.null2String(recordSet.getString("jobtitlemark")));
/* 142 */       hashMap1.put("jobactivitymark", Util.null2String(recordSet.getString("jobactivitymark")));
/* 143 */       hashMap1.put("jobgroupremark", Util.null2String(recordSet.getString("jobgroupremark")));
/* 144 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 147 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 148 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 149 */     arrayList1.add(new ListHeadBean("jobtitlemark", "", 1, BoolAttr.TRUE));
/* 150 */     arrayList1.add(new ListHeadBean("jobactivitymark", ""));
/* 151 */     arrayList1.add(new ListHeadBean("jobgroupremark", ""));
/*     */     
/* 153 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 154 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 155 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 156 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 161 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 162 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 163 */     ArrayList<String> arrayList = new ArrayList();
/* 164 */     String str2 = "";
/* 165 */     if (str1.length() > 0) str2 = "%" + str1 + "%"; 
/* 166 */     RecordSet recordSet = new RecordSet();
/* 167 */     String str3 = "where 1 = 1 ";
/* 168 */     String str4 = " a.id as id,a.jobtitlemark as jobtitlemark,a.jobtitlename as jobtitlename,a.jobtitlecode as jobtitlecode,b.jobactivityname as jobactivityname, b.jobactivitymark as jobactivitymark ";
/* 169 */     String str5 = " from HrmJobTitles a left join HrmJobActivities b on a.jobactivityid=b.id ";
/* 170 */     String str6 = " order by a.id ";
/* 171 */     str3 = str3 + " and (a.canceled is null or a.canceled <> 1)";
/* 172 */     if (!"".equals(str2)) {
/* 173 */       str3 = str3 + " and (a.jobtitlemark like ? or a.jobtitlename like ? or a.ecology_pinyin_search like ? )";
/* 174 */       arrayList.add(str2);
/* 175 */       arrayList.add(str2);
/* 176 */       arrayList.add(str2);
/*     */     } 
/* 178 */     String str7 = "select " + str4 + str5 + str3 + str6;
/* 179 */     recordSet.executeQuery(str7, new Object[] { arrayList });
/* 180 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 181 */     while (recordSet.next()) {
/* 182 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 183 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 184 */       String str8 = Util.null2String(recordSet.getString("jobtitlemark"));
/* 185 */       String str9 = Util.null2String(recordSet.getString("jobtitlecode"));
/* 186 */       String str10 = str8 + "|" + str9 + "|" + Util.null2String(recordSet.getString("jobactivitymark"));
/* 187 */       hashMap1.put("name", str8);
/* 188 */       hashMap1.put("title", str10);
/* 189 */       arrayList1.add(hashMap1);
/*     */     } 
/* 191 */     hashMap.put("datas", arrayList1);
/* 192 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/JobTitlesBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */