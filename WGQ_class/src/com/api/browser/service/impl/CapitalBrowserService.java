/*      */ package com.api.browser.service.impl;
/*      */ import com.alibaba.fastjson.JSON;
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.browser.bean.BrowserBean;
/*      */ import com.api.browser.bean.ListHeadBean;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SearchConditionOption;
/*      */ import com.api.browser.bean.SplitTableBean;
/*      */ import com.api.browser.bean.SplitTableColBean;
/*      */ import com.api.browser.service.BrowserService;
/*      */ import com.api.browser.util.BoolAttr;
/*      */ import com.api.browser.util.BrowserBaseUtil;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.BrowserInitUtil;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.api.browser.util.MobileShowTypeAttr;
/*      */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*      */ import com.cloudstore.dev.api.util.Util_MobileData;
/*      */ import com.engine.cpt.util.CapitalTransMethod;
/*      */ import com.engine.workflow.biz.wfPathAdvanceSet.BrowserDataDefinitionBiz;
/*      */ import com.weaver.formmodel.util.DateHelper;
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.Collections;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import org.json.JSONObject;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*      */ import weaver.cpt.util.CommonShareManager;
/*      */ import weaver.cpt.util.CptWfUtil;
/*      */ import weaver.filter.XssUtil;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.general.browserData.BrowserManager;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.proj.util.SQLUtil;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*      */ import weaver.workflow.browserdatadefinition.ConditionField;
/*      */ 
/*      */ public class CapitalBrowserService extends BrowserService {
/*      */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*   55 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*   57 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), -1);
/*   58 */     int j = Util.getIntValue(Util.null2String(paramMap.get("fieldid")), -1);
/*   59 */     int k = Util.getIntValue(Util.null2String(paramMap.get("viewtype")), -1);
/*      */     
/*   61 */     String str = Util.null2String(paramMap.get("datatype"));
/*   62 */     if ("tree".equals(str)) {
/*   63 */       BrowserDataDefinitionBiz.getDataDefinitionParams(paramMap, this.browserType, this.user);
/*   64 */       String str1 = Util.null2String(paramMap.get("capitalgroupid"));
/*   65 */       ArrayList arrayList = StringHelper.string2ArrayList2(str1, ",");
/*   66 */       HashSet<String> hashSet = new HashSet();
/*   67 */       hashSet.add(str1);
/*   68 */       CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*      */       
/*   70 */       String str2 = "";
/*   71 */       List<ConditionField> list = ConditionField.readAll(i, j, k);
/*   72 */       for (byte b = 0; b < list.size(); b++) {
/*   73 */         ConditionField conditionField = list.get(b);
/*   74 */         String str5 = conditionField.getFieldName();
/*   75 */         if ("capitalgroupid".equalsIgnoreCase(str5)) {
/*   76 */           str2 = conditionField.getValueType();
/*      */           break;
/*      */         } 
/*      */       } 
/*   80 */       for (Object object : arrayList) {
/*   81 */         String str5 = object.toString();
/*   82 */         hashSet.add(str5);
/*      */         
/*   84 */         String str6 = capitalAssortmentComInfo.getSupAssortmentStr(str5);
/*   85 */         String[] arrayOfString = str6.split("\\|");
/*   86 */         for (byte b1 = 1; b1 < arrayOfString.length; b1++) {
/*   87 */           hashSet.add(arrayOfString[b1]);
/*      */         }
/*      */       } 
/*      */ 
/*      */       
/*   92 */       String str3 = Util.null2s(Util.null2String(paramMap.get("id")), "0");
/*      */ 
/*      */       
/*   95 */       RecordSet recordSet = new RecordSet();
/*   96 */       int m = this.user.getUserSubCompany1();
/*   97 */       int n = this.user.getUID();
/*   98 */       recordSet.execute("select cptdetachable from SystemSet");
/*   99 */       int i1 = 0;
/*  100 */       if (recordSet.next()) {
/*  101 */         i1 = recordSet.getInt("cptdetachable");
/*      */       }
/*  103 */       String str4 = "";
/*  104 */       if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/*  105 */         str4 = "Capital:Maintenance";
/*      */       }
/*  107 */       ArrayList<String> arrayList1 = new ArrayList();
/*  108 */       if (i1 == 1 && n != 1) {
/*  109 */         recordSet.executeProc("HrmRoleSR_SeByURId", "" + this.user.getUID() + Util.getSeparator() + str4);
/*  110 */         while (recordSet.next()) {
/*  111 */           String str5 = recordSet.getString("subcompanyid");
/*  112 */           arrayList1.add(str5.trim());
/*      */         } 
/*      */       } 
/*      */       
/*  116 */       capitalAssortmentComInfo.setTofirstRow();
/*  117 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*  118 */       while (capitalAssortmentComInfo.next()) {
/*  119 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  120 */         String str5 = Util.null2String(capitalAssortmentComInfo.getSupAssortmentId(), "0");
/*  121 */         int i2 = Util.getIntValue(Util.null2String(capitalAssortmentComInfo.getSubcompanyid1()), 0);
/*  122 */         if (!str5.equals(str3)) {
/*      */           continue;
/*      */         }
/*      */         
/*  126 */         if (i1 == 1 && n != 1 && (
/*  127 */           (arrayList1.size() == 0) ? (
/*  128 */           m != i2) : 
/*      */ 
/*      */ 
/*      */           
/*  132 */           !arrayList1.contains(i2 + ""))) {
/*      */           continue;
/*      */         }
/*      */ 
/*      */         
/*  137 */         String str6 = capitalAssortmentComInfo.getAssortmentId();
/*  138 */         String str7 = capitalAssortmentComInfo.getAssortmentName();
/*  139 */         if (!"".equals(str1) && !hashSet.contains(str6)) {
/*      */           continue;
/*      */         }
/*  142 */         boolean bool = hasChild(str6);
/*  143 */         hashMap1.put("domid", "sel_" + str6);
/*  144 */         hashMap1.put("haschild", Boolean.valueOf(bool));
/*  145 */         hashMap1.put("isLeaf", Boolean.valueOf(!bool));
/*  146 */         hashMap1.put("isopen", Boolean.valueOf(false));
/*  147 */         hashMap1.put("key", str6);
/*  148 */         hashMap1.put("name", str7);
/*  149 */         arrayList2.add(hashMap1);
/*      */       } 
/*  151 */       hashMap.put("datas", arrayList2);
/*      */     } else {
/*      */       
/*  154 */       CptWfUtil cptWfUtil = new CptWfUtil();
/*  155 */       String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  156 */       String str2 = Util.null2String(paramMap.get("cptstateid"));
/*  157 */       String str3 = Util.null2String(paramMap.get("cptsptcount"));
/*  158 */       String str4 = Util.null2String(paramMap.get("isdata"));
/*  159 */       String str5 = Util.null2String(paramMap.get("mtype"));
/*  160 */       int m = Util.getIntValue(Util.null2String(paramMap.get("isMultBrow")), 0);
/*      */       
/*  162 */       String str6 = Util.null2s(Util.null2String(paramMap.get("inculdeNumZero")), "1");
/*  163 */       String str7 = "0";
/*  164 */       String str8 = Util.null2String(paramMap.get("billid"));
/*  165 */       String str9 = Util.null2String(paramMap.get("wfid"));
/*  166 */       int n = Util.getIntValue(Util.null2String(paramMap.get("requestid")), -1);
/*  167 */       RecordSet recordSet1 = new RecordSet();
/*  168 */       recordSet1.execute("select * from cpt_barcodesettings");
/*  169 */       recordSet1.next();
/*  170 */       String str10 = Util.null2String(recordSet1.getString("userfilter"));
/*  171 */       String str11 = Util.null2String(recordSet1.getString("useHrm"));
/*  172 */       String str12 = Util.null2String(recordSet1.getString("useHrms"));
/*  173 */       boolean bool1 = "0".equals(str10);
/*  174 */       boolean bool2 = "1".equals(str11);
/*  175 */       String str13 = Util.null2String(str12);
/*  176 */       ArrayList arrayList = Util.TokenizerString(str13, ",");
/*      */ 
/*      */       
/*  179 */       if (str1.indexOf("isdata") != -1) {
/*  180 */         if (str1.substring(str1.indexOf("isdata='") + 8, str1.indexOf("isdata='") + 9).equals("2")) {
/*  181 */           str7 = "1";
/*  182 */         } else if (str1.substring(str1.indexOf("isdata=") + 7, str1.indexOf("isdata=") + 8).equals("2")) {
/*  183 */           str7 = "1";
/*      */         }
/*      */       
/*      */       }
/*  187 */       else if (str4.equals("") || str4.equals("2")) {
/*  188 */         str7 = "1";
/*      */       } 
/*      */ 
/*      */       
/*  192 */       if (!str8.equals("") && str7.equals("1")) {
/*  193 */         int i4 = Util.getIntValue(str8);
/*  194 */         switch (i4) {
/*      */           case 19:
/*  196 */             str1 = " where isdata='2'  ";
/*  197 */             str2 = "1";
/*  198 */             str6 = "0";
/*      */             break;
/*      */           case 220:
/*  201 */             str1 = " where isdata='2'  ";
/*  202 */             str3 = "1";
/*  203 */             str2 = "1";
/*  204 */             str6 = "0";
/*      */             break;
/*      */           case 18:
/*  207 */             str1 = " where isdata='2'  ";
/*  208 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, "move");
/*  209 */             str2 = "1,2";
/*  210 */             str6 = "0";
/*      */             break;
/*      */           case 224:
/*  213 */             str1 = " where isdata='2'  ";
/*  214 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, "back");
/*      */             
/*  216 */             str2 = "4,2,3";
/*  217 */             str6 = "0";
/*      */             break;
/*      */           case 201:
/*  220 */             str1 = " where isdata='2'  ";
/*  221 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, "discard");
/*  222 */             str2 = "1,2,3,4";
/*  223 */             str6 = "0";
/*      */             break;
/*      */           case 222:
/*  226 */             str1 = " where isdata='2'  ";
/*  227 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, "mend");
/*  228 */             str3 = "1";
/*  229 */             str2 = "1,2,3";
/*  230 */             str6 = "0";
/*      */             break;
/*      */           case 221:
/*  233 */             str1 = " where isdata='2'  ";
/*  234 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, "loss");
/*  235 */             str2 = "1,2,3,4";
/*  236 */             str6 = "0";
/*      */             break;
/*      */         } 
/*      */       } 
/*  240 */       String str14 = "";
/*  241 */       if (!"".equals(str9) && str7.equals("1")) {
/*  242 */         str14 = cptWfUtil.getWftype(str9);
/*  243 */         if (!"".equals(str14)) {
/*  244 */           if ("fetch".equalsIgnoreCase(str14)) {
/*  245 */             str2 = "1";
/*  246 */           } else if ("lend".equalsIgnoreCase(str14)) {
/*  247 */             str2 = "1";
/*  248 */             str3 = "1";
/*  249 */           } else if ("move".equalsIgnoreCase(str14)) {
/*  250 */             str2 = "1,2";
/*  251 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, str14);
/*  252 */           } else if ("back".equalsIgnoreCase(str14)) {
/*  253 */             str2 = "2,3,4";
/*  254 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, str14);
/*  255 */           } else if ("discard".equalsIgnoreCase(str14)) {
/*  256 */             str2 = "1,2,3,4";
/*  257 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, str14);
/*  258 */           } else if ("mend".equalsIgnoreCase(str14)) {
/*  259 */             str2 = "1,2,3";
/*  260 */             str3 = "1";
/*  261 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, str14);
/*  262 */           } else if ("loss".equalsIgnoreCase(str14)) {
/*  263 */             str2 = "1,2,3,4";
/*  264 */             str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, str14);
/*      */           } 
/*  266 */           str6 = "0";
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  271 */       if (!"".equals(str5) && (
/*  272 */         "cptmove".equalsIgnoreCase(str5) || "cptback".equalsIgnoreCase(str5) || "cptdiscard".equalsIgnoreCase(str5) || "cptmend".equalsIgnoreCase(str5) || "cptloss".equalsIgnoreCase(str5))) {
/*  273 */         str1 = str1 + getResourceSql(str1, bool1, bool2, arrayList, str5);
/*      */       }
/*      */ 
/*      */ 
/*      */       
/*  278 */       recordSet1.execute("select cptdetachable from SystemSet");
/*  279 */       int i1 = 0;
/*  280 */       if (recordSet1.next()) {
/*  281 */         i1 = recordSet1.getInt("cptdetachable");
/*      */       }
/*      */       
/*  284 */       int i2 = this.user.getUserSubCompany1();
/*  285 */       int i3 = this.user.getUID();
/*  286 */       char c = Util.getSeparator();
/*      */ 
/*      */       
/*  289 */       String str15 = " ";
/*  290 */       String str16 = str1;
/*  291 */       String str17 = "";
/*  292 */       if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/*  293 */         str17 = "Capital:Maintenance";
/*      */       }
/*  295 */       String str18 = "";
/*      */       
/*  297 */       boolean bool = false;
/*  298 */       byte b1 = 0;
/*  299 */       if (!str1.equals("")) {
/*  300 */         if (!bool) {
/*  301 */           bool = true;
/*  302 */           str15 = str15 + str16;
/*      */         } 
/*  304 */         if (str1.indexOf("isdata") != -1) {
/*  305 */           String str28 = str1.substring(str1.indexOf("isdata") + 1);
/*  306 */           int i4 = str28.indexOf("'1'");
/*  307 */           int i5 = str28.indexOf("'2'");
/*  308 */           int i6 = str28.indexOf("1");
/*  309 */           int i7 = str28.indexOf("2");
/*  310 */           if (i4 == -1 && i5 > -1) {
/*  311 */             b1 = 2;
/*  312 */           } else if (i4 > -1 && i5 == -1) {
/*  313 */             b1 = 1;
/*  314 */           } else if (i6 == -1 && i7 > -1) {
/*  315 */             b1 = 2;
/*  316 */           } else if (i6 > -1 && i7 == -1) {
/*  317 */             b1 = 1;
/*      */           }
/*      */         
/*  320 */         } else if (!bool) {
/*  321 */           bool = true;
/*  322 */           str15 = str15 + " where isdata = '2' ";
/*      */         } else {
/*  324 */           str15 = str15 + " and isdata = '2' ";
/*      */         }
/*      */       
/*      */       }
/*  328 */       else if (!bool) {
/*  329 */         bool = true;
/*  330 */         str15 = str15 + " where isdata = '" + ("0".equals(str7) ? "1" : "2") + "' ";
/*      */       } else {
/*  332 */         str15 = str15 + " and isdata = '" + ("0".equals(str7) ? "1" : "2") + "' ";
/*      */       } 
/*      */       
/*  335 */       if (b1 == 0) {
/*  336 */         b1 = 2;
/*      */       }
/*  338 */       if (!"1".equals(str7)) {
/*  339 */         str15 = str15 + " and (t1.cancelled is null or t1.cancelled=0)";
/*      */       }
/*  341 */       if (!str2.equals("")) {
/*  342 */         str15 = str15 + " and stateid in (";
/*  343 */         str15 = str15 + Util.fromScreen2(str2, this.user.getLanguage());
/*  344 */         str15 = str15 + ") ";
/*      */       } 
/*  346 */       if (!str3.equals("")) {
/*  347 */         str15 = str15 + " and sptcount = '";
/*  348 */         str15 = str15 + Util.fromScreen2(str3, this.user.getLanguage());
/*  349 */         str15 = str15 + "'";
/*      */       } 
/*      */ 
/*      */       
/*  353 */       if (i1 == 1 && i3 != 1) {
/*  354 */         if (b1 == 2) {
/*  355 */           String str28 = "";
/*  356 */           recordSet1.executeProc("HrmRoleSR_SeByURId", "" + i3 + c + str17);
/*  357 */           while (recordSet1.next()) {
/*  358 */             str18 = recordSet1.getString("subcompanyid");
/*  359 */             str28 = str28 + ", " + str18;
/*      */           } 
/*  361 */           if (!"".equals(str28)) {
/*  362 */             str28 = str28.substring(1);
/*  363 */             str15 = str15 + " and blongsubcompany in (" + str28 + ") ";
/*      */           } else {
/*  365 */             str15 = str15 + " and blongsubcompany in (" + i2 + ") ";
/*      */           } 
/*  367 */         } else if (b1 == 1) {
/*  368 */           CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  369 */           int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), str17);
/*  370 */           String str28 = "";
/*  371 */           for (byte b = 0; b < arrayOfInt.length; b++) {
/*  372 */             if (arrayOfInt[b] > 0) {
/*  373 */               str28 = str28 + (str28.equals("") ? "" : ",") + arrayOfInt[b];
/*      */             }
/*      */           } 
/*  376 */           if (str28.equals(""))
/*  377 */             str28 = this.user.getUserSubCompany1() + ""; 
/*  378 */           if (!"".equals(str28)) {
/*  379 */             str15 = str15 + " and blongsubcompany in (" + str28 + ") ";
/*      */           } else {
/*  381 */             str15 = str15 + " and blongsubcompany in (" + str28 + ") ";
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  423 */       String str19 = ",";
/*  424 */       if (!"1".equals(str7)) {
/*  425 */         str19 = str19 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,alertnum,warehouse,";
/*  426 */       } else if (!(new CapitalTransMethod()).IsWareHouseOpen()) {
/*  427 */         str19 = str19 + "warehouse,";
/*      */       } 
/*      */       
/*  430 */       RecordSet recordSet2 = new RecordSet();
/*  431 */       StringBuffer stringBuffer = new StringBuffer();
/*  432 */       String str20 = "select t1.*,t2.fieldname,t2.fieldlabel,t2.fieldhtmltype,t2.type,t2.issystem from cpt_browdef t1,cptDefineField t2 where t1.iscondition=1 and t1.fieldid=t2.id and t2.isopen=1 order by t1.displayorder";
/*  433 */       recordSet2.execute(str20);
/*  434 */       while (recordSet2.next()) {
/*  435 */         String str28 = Util.null2String(recordSet2.getString("fieldname"));
/*  436 */         String str29 = str28;
/*  437 */         int i4 = Util.getIntValue(recordSet2.getString("fieldhtmltype"), 0);
/*  438 */         String str30 = Util.null2String(recordSet2.getString("type"));
/*  439 */         if (str19.contains("," + str28 + ","))
/*      */           continue; 
/*  441 */         if ((i4 == 2 && "2".equals(str30)) || i4 == 6 || i4 == 7) {
/*      */           continue;
/*      */         }
/*  444 */         String str31 = Util.null2String(recordSet2.getString("fieldid"));
/*      */         
/*  446 */         String str32 = Util.null2String(paramMap.get(str28));
/*  447 */         String str33 = "";
/*  448 */         String str34 = "";
/*  449 */         int i5 = Util.getIntValue(recordSet2.getString("issystem"), 0);
/*  450 */         if (i5 != 1) {
/*  451 */           str29 = "field" + str31;
/*  452 */           str32 = Util.null2String(paramMap.get(str29));
/*      */         } 
/*  454 */         if (i4 == 3 && str30.equals("2")) {
/*  455 */           str32 = Util.null2String(paramMap.get(str29 + "_select"));
/*  456 */           str33 = Util.null2String(paramMap.get(str29 + "_start"));
/*  457 */           str34 = Util.null2String(paramMap.get(str29 + "_end"));
/*      */         } 
/*      */ 
/*      */         
/*  461 */         String str35 = "";
/*  462 */         if (i4 == 1 && ("2".equals(str30) || "3".equals(str30))) {
/*  463 */           str35 = Util.null2String(paramMap.get(str29 + "_1"));
/*      */         }
/*      */         
/*  466 */         if (!"".equals(str32)) {
/*  467 */           if (i4 == 3) {
/*  468 */             if (str30.equals("2")) {
/*  469 */               String str36 = "";
/*  470 */               String str37 = "";
/*  471 */               if (str30.equals("2")) {
/*  472 */                 Map<String, String> map = getDateRangeByDateField(str32, str33, str34);
/*  473 */                 str36 = map.get("startdate");
/*  474 */                 str37 = map.get("enddate");
/*  475 */                 if ("".equals(str36) && "".equals(str37)) {
/*      */                   continue;
/*      */                 }
/*      */               } 
/*  479 */               if (!str36.equals("")) {
/*  480 */                 stringBuffer.append("and (t1." + str28);
/*  481 */                 stringBuffer.append(" >='" + str36 + "' ");
/*      */               } 
/*  483 */               if (!str37.equals("")) {
/*  484 */                 stringBuffer.append("and t1." + str28);
/*  485 */                 stringBuffer.append(" <='" + str37 + "' ");
/*      */               } 
/*  487 */               if (!str36.equals("")) {
/*  488 */                 stringBuffer.append(") ");
/*      */               }
/*  490 */             } else if ("25".equals(str30) && str28.equals("capitalgroupid")) {
/*      */               
/*  492 */               stringBuffer.append(" and (t1.capitalgroupid in(" + str32 + ") ");
/*  493 */               String[] arrayOfString = str32.split(",");
/*  494 */               for (String str36 : arrayOfString) {
/*  495 */                 stringBuffer.append(" or t1.capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + str36 + "|%') ");
/*      */               }
/*  497 */               stringBuffer.append(")");
/*      */             }
/*  499 */             else if (str28.equalsIgnoreCase("departmentid") || str28.equalsIgnoreCase("blongdepartment") || str28.equalsIgnoreCase("blongsubcompany") || str28.equalsIgnoreCase("resourceid")) {
/*  500 */               stringBuffer.append(" and t1." + str28 + " in(" + str32 + ")  ");
/*      */             } else {
/*  502 */               boolean bool3 = "true".equalsIgnoreCase(BrowserManager.browIsSingle("" + str30));
/*  503 */               if (bool3) {
/*  504 */                 stringBuffer.append(" and t1." + str28 + " ='" + str32 + "'  ");
/*      */               } else {
/*  506 */                 String str36 = recordSet2.getDBType();
/*  507 */                 if ("oracle".equalsIgnoreCase(str36)) {
/*  508 */                   stringBuffer.append(SQLUtil.filteSql(recordSet2.getDBType(), " and ','+t1." + str28 + "+',' like '%," + str32 + ",%'  "));
/*  509 */                 } else if ("mysql".equalsIgnoreCase(str36)) {
/*  510 */                   stringBuffer.append(" and concat(',',convert(t1." + str28 + " , char(2000)),',') like '%," + str32 + ",%'  ");
/*      */                 }
/*  512 */                 else if ("postgresql".equalsIgnoreCase(str36)) {
/*  513 */                   stringBuffer.append(SQLUtil.filteSql(recordSet2.getDBType(), " and ','+t1." + str28 + "+',' like '%," + str32 + ",%'  "));
/*      */                 } else {
/*      */                   
/*  516 */                   stringBuffer.append(" and ','+convert(varchar(2000),t1." + str28 + ")+',' like '%," + str32 + ",%'  ");
/*      */                 }
/*      */               
/*      */               } 
/*      */             } 
/*  521 */           } else if (i4 == 4) {
/*  522 */             if ("sptcount".equals(str28)) {
/*      */               
/*  524 */               if (str32.equals("1")) {
/*  525 */                 stringBuffer.append(" and t1.sptcount='" + str32 + "'  ");
/*  526 */               } else if (str32.equals("0")) {
/*  527 */                 stringBuffer.append(" and ( t1.sptcount is null or  t1.sptcount = '' or t1.sptcount != '1' ) ");
/*      */               }
/*      */             
/*  530 */             } else if ("1".equals(str32)) {
/*  531 */               stringBuffer.append(" and t1." + str28 + " ='" + str32 + "'  ");
/*      */             }
/*      */           
/*  534 */           } else if (i4 == 5) {
/*  535 */             stringBuffer.append(" and t1." + str28 + " ='" + str32 + "'  ");
/*      */           }
/*  537 */           else if (i4 == 1 && ("2".equals(str30) || "3".equals(str30))) {
/*  538 */             if ("capitalnum".equalsIgnoreCase(str28)) {
/*  539 */               if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  540 */                 stringBuffer.append(" and (nvl(capitalnum,0)-nvl(frozennum,0)) >= " + str32);
/*  541 */               } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/*  542 */                 stringBuffer.append(" and (ifnull(capitalnum,0)-ifnull(frozennum,0)) >=" + str32);
/*      */               } else {
/*  544 */                 stringBuffer.append(" and (isnull(capitalnum,0)-isnull(frozennum,0)) >=" + str32);
/*      */               } 
/*      */             } else {
/*  547 */               stringBuffer.append(" and t1." + str28 + ">= " + str32);
/*      */             } 
/*  549 */           } else if (i4 == 2) {
/*  550 */             str32 = Util.fromScreen(Util.null2String(str32), this.user.getLanguage());
/*  551 */             stringBuffer.append(" and t1." + str28 + " like'%" + str32 + "%'  ");
/*      */           } else {
/*  553 */             stringBuffer.append(" and t1." + str28 + " like'%" + str32 + "%'  ");
/*      */           } 
/*      */         }
/*      */ 
/*      */ 
/*      */         
/*  559 */         if (i4 == 1 && ("2".equals(str30) || "3".equals(str30))) {
/*  560 */           if ("capitalnum".equalsIgnoreCase(str28)) {
/*  561 */             if (!"".equals(str35)) {
/*  562 */               if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  563 */                 stringBuffer.append(" and (nvl(capitalnum,0)-nvl(frozennum,0)) <= " + str35); continue;
/*  564 */               }  if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/*  565 */                 stringBuffer.append(" and (ifnull(capitalnum,0)-ifnull(frozennum,0)) <=" + str35); continue;
/*      */               } 
/*  567 */               stringBuffer.append(" and (isnull(capitalnum,0)-isnull(frozennum,0)) <=" + str35);
/*      */             } 
/*      */             continue;
/*      */           } 
/*  571 */           if (!"".equals(str35)) {
/*  572 */             stringBuffer.append(" and t1." + str28 + "<= " + str35);
/*      */           }
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  579 */       String str21 = "";
/*  580 */       String str22 = "";
/*  581 */       List<ConditionField> list = ConditionField.readAll(i, j, k);
/*  582 */       for (byte b2 = 0; b2 < list.size(); b2++) {
/*  583 */         ConditionField conditionField = list.get(b2);
/*  584 */         String str28 = conditionField.getFieldName();
/*  585 */         if ("capitalgroupid".equalsIgnoreCase(str28)) {
/*  586 */           str21 = conditionField.getValue();
/*  587 */           str22 = conditionField.getValueType();
/*      */           
/*      */           break;
/*      */         } 
/*      */       } 
/*  592 */       if (str22.equals("3")) {
/*  593 */         str21 = Util.null2String(paramMap.get("field" + str21));
/*      */       }
/*  595 */       if (!str21.equals("")) {
/*  596 */         stringBuffer.append(" and (t1.capitalgroupid in(" + str21 + ") ");
/*  597 */         String[] arrayOfString = str21.split(",");
/*  598 */         for (String str28 : arrayOfString) {
/*  599 */           stringBuffer.append(" or t1.capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + str28 + "|%') ");
/*      */         }
/*  601 */         stringBuffer.append(")");
/*      */       } 
/*      */       
/*  604 */       if (!"".equals(Util.null2String(paramMap.get("datatype")))) {
/*  605 */         stringBuffer.append(" and t1.datatype= " + Util.null2String(paramMap.get("datatype")) + " ");
/*      */       }
/*      */       
/*  608 */       if (stringBuffer.length() > 5) {
/*  609 */         str15 = str15 + stringBuffer.toString();
/*      */       }
/*      */       
/*  612 */       if ("1".equals(str7) && !this.user.getLoginid().equalsIgnoreCase("sysadmin")) {
/*  613 */         CommonShareManager commonShareManager = new CommonShareManager();
/*  614 */         commonShareManager.setAliasTableName("t2");
/*  615 */         str15 = str15 + commonShareManager.getAssortmentSqlWhere(this.user);
/*      */       } 
/*      */       
/*  618 */       String str23 = " t1.id ";
/*  619 */       String str24 = " t1.*,";
/*  620 */       if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  621 */         str24 = str24 + "(nvl(capitalnum,0)-nvl(frozennum,0)) cptnum ";
/*  622 */       } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/*  623 */         str24 = str24 + "(ifnull(capitalnum,0)-ifnull(frozennum,0)) cptnum ";
/*      */       } else {
/*  625 */         str24 = str24 + "(isnull(capitalnum,0)-isnull(frozennum,0)) cptnum ";
/*      */       } 
/*  627 */       String str25 = " CptCapital t1 ";
/*      */ 
/*      */       
/*  630 */       String str26 = "";
/*  631 */       if (!"1".equals(str6) && 2 == b1) {
/*  632 */         str26 = " and  ";
/*  633 */         if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  634 */           str26 = str26 + "  (nvl(capitalnum,0)-nvl(frozennum,0))>0 ";
/*  635 */         } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/*  636 */           str26 = str26 + "  (ifnull(capitalnum,0)-ifnull(frozennum,0))>0 ";
/*      */         } else {
/*  638 */           str26 = str26 + "  (isnull(capitalnum,0)-isnull(frozennum,0))>0 ";
/*      */         } 
/*      */       } 
/*  641 */       if (n > 0 && "1".equals(str7)) {
/*  642 */         String str28 = "select t1.currentnodetype,t1.workflowid,t2.formid from workflow_requestbase t1,workflow_base t2 where t1.workflowid=t2.id and t1.requestid=" + n;
/*  643 */         int i4 = 0;
/*  644 */         int i5 = 0;
/*  645 */         int i6 = 0;
/*      */         
/*  647 */         recordSet1.execute(str28);
/*  648 */         while (recordSet1.next()) {
/*  649 */           i4 = recordSet1.getInt("formid");
/*  650 */           i5 = recordSet1.getInt("workflowid");
/*  651 */           i6 = recordSet1.getInt("currentnodetype");
/*      */         } 
/*  653 */         if (!"apply".equals(str14) && !"applyuse".equals(str14) && 
/*  654 */           i6 > 0 && i6 < 3) {
/*  655 */           CptWfUtil cptWfUtil1 = new CptWfUtil();
/*  656 */           JSONObject jSONObject = cptWfUtil1.getCptwfInfo("" + i5);
/*  657 */           if (jSONObject.length() > 0) {
/*  658 */             String str29 = "formtable_main_" + -i4;
/*  659 */             recordSet1.execute("select tablename from workflow_bill where id=" + i4);
/*  660 */             while (recordSet1.next()) {
/*  661 */               str29 = recordSet1.getString("tablename");
/*      */             }
/*  663 */             String str30 = str29;
/*  664 */             String str31 = jSONObject.getString("zcname");
/*  665 */             String str32 = jSONObject.getString("slname");
/*  666 */             int i7 = Util.getIntValue("" + jSONObject.getInt("zctype"), 0);
/*  667 */             if (i7 == 1) {
/*  668 */               str30 = str30 + "_dt1";
/*  669 */             } else if (i7 == 2) {
/*  670 */               str30 = str30 + "_dt2";
/*  671 */             } else if (i7 == 3) {
/*  672 */               str30 = str30 + "_dt3";
/*  673 */             } else if (i7 == 4) {
/*  674 */               str30 = str30 + "_dt4";
/*      */             } 
/*  676 */             String str33 = "";
/*  677 */             if (!str30.equals(str29)) {
/*  678 */               str33 = " select d." + str31 + " as currentzcid,sum(d." + str32 + ") as currentreqnum from " + str29 + " m ," + str30 + " d where d.mainid=m.id and m.requestid=" + n + " group by d." + str31 + " ";
/*      */             } else {
/*      */               
/*  681 */               str33 = "select m." + str31 + " as currentzcid,sum(m." + str32 + ") as currentreqnum from " + str29 + " m  where  m.requestid=" + n + " group by m." + str31 + " ";
/*      */             } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  688 */             if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  689 */               str24 = " t2.currentreqnum,t1.*,(nvl(capitalnum,0)-nvl(frozennum,0)+nvl(currentreqnum,0)) cptnum ";
/*      */             }
/*  691 */             else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/*  692 */               str24 = " t2.currentreqnum,t1.*,(ifnull(capitalnum,0)-ifnull(frozennum,0)+ifnull(currentreqnum,0)) cptnum ";
/*      */             
/*      */             }
/*  695 */             else if ("postgresql".equalsIgnoreCase(recordSet1.getDBType())) {
/*      */               
/*  697 */               str24 = " t2.currentreqnum,t1.*,(coalesce(capitalnum,0)-coalesce(frozennum,0)+coalesce(currentreqnum,0)) cptnum ";
/*      */             }
/*      */             else {
/*      */               
/*  701 */               str24 = " t2.currentreqnum,t1.*,(isnull(capitalnum,0)-isnull(frozennum,0)+isnull(currentreqnum,0)) cptnum ";
/*      */             } 
/*      */             
/*  704 */             str25 = " CptCapital t1 left outer join (" + str33 + ") t2 on t2.currentzcid=t1.id ";
/*      */             
/*  706 */             if (!bool) {
/*  707 */               bool = true;
/*  708 */               str26 = " where ";
/*      */             } else {
/*  710 */               str26 = " and  ";
/*      */             } 
/*  712 */             if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  713 */               str26 = str26 + "  (nvl(capitalnum,0)-nvl(frozennum,0)+nvl(currentreqnum,0))>0 ";
/*  714 */             } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/*  715 */               str26 = str26 + "  (ifnull(capitalnum,0)-ifnull(frozennum,0)+ifnull(currentreqnum,0))>0 ";
/*      */             } else {
/*  717 */               str26 = str26 + "  (isnull(capitalnum,0)-isnull(frozennum,0)+isnull(currentreqnum,0))>0 ";
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  726 */       String str27 = "select t1.*,t2.fieldname,t2.fieldlabel,t2.fieldhtmltype,t2.type from cpt_browdef t1,cptDefineField t2 where t1.istitle=1 and t1.fieldid=t2.id and t2.isopen=1  order by t1.displayorder";
/*  727 */       recordSet1.execute(str27);
/*      */       
/*  729 */       if (str5.equals("cptchange") || str14.equals("change")) {
/*  730 */         if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  731 */           str15 = str15 + " and ((sptcount=1 and nvl(frozennum,0)=0) or sptcount<>1)";
/*  732 */         } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/*  733 */           str15 = str15 + " and ((sptcount=1 and ifnull(frozennum,0)=0) or sptcount<>1)";
/*      */         } else {
/*  735 */           str15 = str15 + " and ((sptcount=1 and isnull(frozennum,0)=0) or sptcount<>1)";
/*      */         } 
/*      */       } else {
/*  738 */         str15 = str15 + str26;
/*      */       } 
/*      */       
/*  741 */       ArrayList<SplitTableColBean> arrayList1 = new ArrayList();
/*      */ 
/*      */       
/*  744 */       if (m == 1) {
/*  745 */         if (str7.equals("1")) {
/*  746 */           arrayList1.add(new SplitTableColBean("true", "id"));
/*  747 */           arrayList1.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/*  748 */           SplitTableColBean splitTableColBean = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(714, this.user.getLanguage()), "mark");
/*  749 */           splitTableColBean.setTablename("CptCapital");
/*  750 */           arrayList1.add(splitTableColBean);
/*  751 */           splitTableColBean = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(904, this.user.getLanguage()), "capitalspec");
/*  752 */           splitTableColBean.setTablename("CptCapital");
/*  753 */           arrayList1.add(splitTableColBean);
/*  754 */           splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(1331, this.user.getLanguage()), "cptnum");
/*  755 */           splitTableColBean.setTablename("CptCapital");
/*  756 */           arrayList1.add(splitTableColBean);
/*      */         } else {
/*  758 */           arrayList1.add(new SplitTableColBean("true", "id"));
/*  759 */           arrayList1.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/*  760 */           SplitTableColBean splitTableColBean = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(714, this.user.getLanguage()), "mark");
/*  761 */           splitTableColBean.setTablename("CptCapital");
/*  762 */           arrayList1.add(splitTableColBean);
/*  763 */           splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(904, this.user.getLanguage()), "capitalspec");
/*  764 */           splitTableColBean.setTablename("CptCapital");
/*      */         } 
/*      */       } else {
/*  767 */         SplitTableColBean splitTableColBean = new SplitTableColBean("true", "id");
/*  768 */         splitTableColBean.setTransmethod("weaver.cpt.util.CapitalTransUtil.getBrowserRetInfo");
/*  769 */         splitTableColBean.setOtherpara(str7);
/*  770 */         arrayList1.add(splitTableColBean);
/*  771 */         CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  772 */         while (recordSet1.next()) {
/*  773 */           String str28 = recordSet1.getString("fieldname");
/*  774 */           if (str19.contains("," + str28 + ","))
/*      */             continue; 
/*  776 */           int i4 = recordSet1.getInt("fieldid");
/*  777 */           int i5 = recordSet1.getInt("fieldlabel");
/*  778 */           int i6 = recordSet1.getInt("fieldhtmltype");
/*  779 */           int i7 = recordSet1.getInt("type");
/*  780 */           if ("resourceid".equalsIgnoreCase(str28) && !"1".equals(str7)) {
/*  781 */             i5 = 1507;
/*      */           }
/*      */           
/*  784 */           String str29 = this.user.getLanguage() + "+" + i4 + "+" + i6 + "+" + i7 + "+" + str28;
/*      */           
/*  786 */           if ("capitalnum".equalsIgnoreCase(str28)) {
/*  787 */             str28 = "cptnum";
/*      */           }
/*      */           
/*  790 */           if (i6 == 2) {
/*  791 */             arrayList1.add(new SplitTableColBean("5%", SystemEnv.getHtmlLabelName(i5, this.user.getLanguage()), str28, null, "com.api.cpt.util.FieldInfoManager.getFieldvalue", str29));
/*      */           } else {
/*  793 */             arrayList1.add(new SplitTableColBean("5%", SystemEnv.getHtmlLabelName(i5, this.user.getLanguage()), str28, "".equals(CapitalTransMethod.getIsOrder(str28)) ? null : str28, "com.api.cpt.util.FieldInfoManager.getFieldvalue", str29));
/*      */           } 
/*      */           
/*  796 */           if (str7.equals("1") && "blongsubcompany".equalsIgnoreCase(str28)) {
/*  797 */             str29 = this.user.getLanguage() + "+-9999+3+179";
/*  798 */             arrayList1.add(new SplitTableColBean("5%", SystemEnv.getHtmlLabelName(1509, this.user.getLanguage()), "datatype", "datatype", "com.api.cpt.util.FieldInfoManager.getFieldvalue", str29));
/*      */           } 
/*      */         } 
/*      */       } 
/*  802 */       SplitTableBean splitTableBean = new SplitTableBean(str24, str25, str15, str23, "t1.id", arrayList1);
/*  803 */       splitTableBean.setSqlsortway("ASC");
/*  804 */       if (this.isMobile) {
/*  805 */         CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  806 */         boolean bool3 = capitalTransMethod.IsWareHouseOpen();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  824 */         String str28 = "<div class='template-wrapper' style='margin-left: 15px;min-width:230px;-webkit-box-pack: start;-webkit-justify-content: flex-start;-ms-flex-pack: start;justify-content: flex-start;align-items: stretch;'>\t<div class='template-main-box' style='width: 100%;padding-bottom: 5px;'>\t\t<p class='template-row template-title' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 14px;color: #333;font-weight: 700;'>#{name}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(714, this.user.getLanguage()) + "：#{mark}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;float:right'>" + SystemEnv.getHtmlLabelName(602, this.user.getLanguage()) + "：#{stateid}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(904, this.user.getLanguage()) + "：#{capitalspec}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(1363, this.user.getLanguage()) + "：#{sptcount}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(1331, this.user.getLanguage()) + "：#{cptnum}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(1508, this.user.getLanguage()) + "：#{resourceidspan}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(21030, this.user.getLanguage()) + "：#{departmentidspan}</span>\t\t</p>\t</div></div>";
/*      */ 
/*      */ 
/*      */         
/*  828 */         if (bool3)
/*      */         {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  848 */           str28 = "<div class='template-wrapper' style='margin-left: 15px;min-width:230px;-webkit-box-pack: start;-webkit-justify-content: flex-start;-ms-flex-pack: start;justify-content: flex-start;align-items: stretch;'>\t<div class='template-main-box' style='width: 100%;padding-bottom: 5px;'>\t\t<p class='template-row template-title' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 14px;color: #333;font-weight: 700;'>#{name}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(714, this.user.getLanguage()) + "：#{mark}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;float:right'>" + SystemEnv.getHtmlLabelName(602, this.user.getLanguage()) + "：#{stateid}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(904, this.user.getLanguage()) + "：#{capitalspec}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(1363, this.user.getLanguage()) + "：#{sptcount}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(1331, this.user.getLanguage()) + "：#{cptnum}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(1508, this.user.getLanguage()) + "：#{resourceidspan}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(21030, this.user.getLanguage()) + "：#{departmentidspan}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(711, this.user.getLanguage()) + "：#{warehousespan}</span>\t\t</p>\t</div></div>";
/*      */         }
/*      */ 
/*      */ 
/*      */         
/*  853 */         if (!"1".equals(str7))
/*      */         {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  868 */           str28 = "<div class='template-wrapper' style='margin-left: 15px;min-width:230px;width: 100%;-webkit-box-pack: start;-webkit-justify-content: flex-start;-ms-flex-pack: start;justify-content: flex-start;align-items: stretch;'>\t<div class='template-main-box' style='width: 100%;padding-bottom: 5px;'>\t\t<p class='template-row template-title' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 14px;color: #333;font-weight: 700;'>#{name}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(714, this.user.getLanguage()) + "：#{mark}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(904, this.user.getLanguage()) + "：#{capitalspec}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px'>" + SystemEnv.getHtmlLabelName(1363, this.user.getLanguage()) + "：#{sptcount}</span>\t\t</p>\t\t<p class='template-row' style='margin: 0;padding: 0;'>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(1507, this.user.getLanguage()) + "：#{resourceidspan}</span>\t\t\t<span style='font-size: 12px;color: #acacac;word-break: break-all;white-space: nowrap;margin-right: 5px;'>" + SystemEnv.getHtmlLabelName(19799, this.user.getLanguage()) + "：#{blongsubcompanyspan}</span>\t\t</p>\t</div></div>";
/*      */         }
/*      */ 
/*      */ 
/*      */         
/*  873 */         arrayList1 = new ArrayList<>();
/*  874 */         arrayList1.add(new SplitTableColBean("true", "id"));
/*  875 */         arrayList1.add(new SplitTableColBean("true", "name"));
/*  876 */         arrayList1.add(new SplitTableColBean("true", "mark"));
/*  877 */         arrayList1.add(new SplitTableColBean("true", "capitalspec"));
/*  878 */         arrayList1.add(new SplitTableColBean("cptnum", "com.engine.cpt.util.CapitalTransMethod.getDesensAndDecryptData", "capitalnum", 0));
/*  879 */         arrayList1.add(new SplitTableColBean("sptcount", "com.api.cpt.mobile.util.CapitalTransUtil.getSptcountState", "" + this.user.getLanguage() + "", 0));
/*  880 */         arrayList1.add(new SplitTableColBean("resourceid", "com.api.cpt.mobile.util.CapitalTransUtil.getResourcename", "", 0));
/*  881 */         arrayList1.add(new SplitTableColBean("departmentid", "com.api.cpt.mobile.util.CapitalTransUtil.getDepartmentname", "", 0));
/*  882 */         if (bool3) {
/*  883 */           arrayList1.add(new SplitTableColBean("warehouse", "com.api.cpt.mobile.util.CapitalTransUtil.getWareHouseName", "", 0));
/*      */         }
/*  885 */         arrayList1.add(new SplitTableColBean("blongsubcompany", "com.api.cpt.mobile.util.CapitalTransUtil.getSubCompanyname", "", 0));
/*  886 */         arrayList1.add(new SplitTableColBean("stateid", "com.api.cpt.mobile.util.CapitalTransUtil.getBrowserShowName", "243+ ", 0));
/*  887 */         splitTableBean = new SplitTableBean(str24, str25, str15, str23, "t1.id", arrayList1);
/*  888 */         splitTableBean.setSqlsortway("ASC");
/*  889 */         SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createStringTemplateBean("template", str28);
/*  890 */         splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*  891 */         splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/*      */       } 
/*  893 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*      */     } 
/*  895 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  905 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  906 */     RecordSet recordSet = new RecordSet();
/*  907 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  908 */     String[] arrayOfString = str.split(",");
/*  909 */     StringBuffer stringBuffer1 = new StringBuffer();
/*  910 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  911 */       stringBuffer1.append("'").append(arrayOfString[b]).append("',");
/*      */     }
/*  913 */     stringBuffer1.append("'-1'");
/*  914 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/*  916 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  917 */     stringBuffer2.append(" select a.id,a.name,a.mark from CptCapital a ");
/*  918 */     stringBuffer2.append(" where id in (").append(stringBuffer1.toString()).append(")");
/*      */     
/*  920 */     recordSet.executeQuery(stringBuffer2.toString(), new Object[0]);
/*  921 */     while (recordSet.next()) {
/*  922 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  923 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/*  924 */       hashMap1.put("name", Util.null2String(recordSet.getString("name")));
/*  925 */       hashMap1.put("mark", Util.null2String(recordSet.getString("mark")));
/*  926 */       arrayList.add(hashMap1);
/*      */     } 
/*  928 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  929 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  930 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(1268, this.user.getLanguage()), 1, BoolAttr.TRUE));
/*  931 */     arrayList1.add(new ListHeadBean("mark", SystemEnv.getHtmlLabelName(714, this.user.getLanguage())));
/*  932 */     arrayList1.add(new ListHeadBean("capitalspec", SystemEnv.getHtmlLabelName(904, this.user.getLanguage())));
/*      */     
/*  934 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  935 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/*  936 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  937 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  942 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/*  943 */     String str2 = Util.null2String(paramMap.get("isdata"));
/*  944 */     String str3 = "0";
/*      */     
/*  946 */     if (str1.indexOf("isdata") != -1) {
/*  947 */       if (str1.substring(str1.indexOf("isdata='") + 8, str1.indexOf("isdata='") + 9).equals("2")) {
/*  948 */         str3 = "1";
/*  949 */       } else if (str1.substring(str1.indexOf("isdata=") + 7, str1.indexOf("isdata=") + 8).equals("2")) {
/*  950 */         str3 = "1";
/*      */       }
/*      */     
/*      */     }
/*  954 */     else if (str2.equals("") || str2.equals("2")) {
/*  955 */       str3 = "1";
/*      */     } 
/*      */     
/*  958 */     RecordSet recordSet1 = new RecordSet();
/*  959 */     RecordSet recordSet2 = new RecordSet();
/*  960 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  961 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  962 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*      */     
/*  964 */     String str4 = "select t1.*,t2.* from cpt_browdef t1,cptDefineField t2 where t1.iscondition=1 and t1.fieldid=t2.id and t2.isopen=1 order by t1.displayorder";
/*  965 */     recordSet1.execute(str4);
/*  966 */     String str5 = ",";
/*      */     
/*  968 */     SearchConditionItem searchConditionItem = null;
/*      */     
/*  970 */     if (!"1".equals(str3)) {
/*  971 */       str5 = str5 + "isinner,barcode,fnamark,stateid,blongdepartment,departmentid,capitalnum,startdate,enddate,manudate,stockindate,location,selectdate,contractno,invoice,deprestartdate,usedyear,currentprice,alertnum,warehouse,";
/*      */     }
/*  973 */     else if (!(new CapitalTransMethod()).IsWareHouseOpen()) {
/*  974 */       str5 = str5 + "warehouse,";
/*      */     } 
/*      */ 
/*      */     
/*  978 */     while (recordSet1.next()) {
/*  979 */       String str6 = recordSet1.getString("fieldname");
/*  980 */       if (str5.contains("," + str6 + ","))
/*      */         continue; 
/*  982 */       String str7 = Util.null2String(recordSet1.getString("id"));
/*  983 */       String str8 = Util.null2String(recordSet1.getString("fielddbtype"));
/*  984 */       String str9 = Util.null2String(recordSet1.getString("fieldhtmltype"));
/*  985 */       String str10 = Util.null2String(recordSet1.getString("type"));
/*  986 */       int i = Util.getIntValue(recordSet1.getString("fieldlabel"));
/*  987 */       String str11 = Util.getIntValue(recordSet1.getString("issystem"), 0) + "";
/*      */       
/*  989 */       if ("resourceid".equalsIgnoreCase(str6) && !"1".equals(str3)) {
/*  990 */         i = 1507;
/*      */       }
/*      */       
/*  993 */       String str12 = (1 == Util.getIntValue(str11)) ? str6 : ("field" + str7);
/*      */       
/*  995 */       ConditionType conditionType = null;
/*  996 */       if ("1".equals(str9)) {
/*  997 */         conditionType = ConditionType.INPUT;
/*  998 */         if ("name".equals(str6)) {
/*  999 */           arrayList.add(conditionFactory.createCondition(conditionType, i, str12, true)); continue;
/*      */         } 
/* 1001 */         if ("2".equals(str10) || "3".equals(str10)) {
/* 1002 */           conditionType = ConditionType.SCOPE;
/* 1003 */           String[] arrayOfString = { str12, str12 + "_1" };
/* 1004 */           arrayList.add(conditionFactory.createCondition(conditionType, i, arrayOfString)); continue;
/*      */         } 
/* 1006 */         arrayList.add(conditionFactory.createCondition(conditionType, i, str12));
/*      */         
/*      */         continue;
/*      */       } 
/* 1010 */       if ("2".equals(str9)) {
/* 1011 */         conditionType = ConditionType.TEXTAREA;
/* 1012 */         arrayList.add(conditionFactory.createCondition(conditionType, i, str12)); continue;
/* 1013 */       }  if ("3".equals(str9)) {
/*      */         
/* 1015 */         if (str6.equalsIgnoreCase("departmentid") || str6.equalsIgnoreCase("blongdepartment") || str6.equalsIgnoreCase("blongsubcompany") || str6.equalsIgnoreCase("resourceid")) {
/* 1016 */           String str = str10;
/* 1017 */           if ("1".equals(str10)) {
/* 1018 */             str = "17";
/* 1019 */           } else if ("4".equals(str10)) {
/* 1020 */             str = "57";
/* 1021 */           } else if ("164".equals(str10)) {
/* 1022 */             str = "194";
/*      */           } 
/* 1024 */           SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, i, str12, str);
/* 1025 */           arrayList.add(searchConditionItem2);
/*      */           
/*      */           continue;
/*      */         } 
/* 1029 */         if ("2".equals(str10)) {
/* 1030 */           conditionType = ConditionType.DATE;
/* 1031 */           SearchConditionItem searchConditionItem2 = new SearchConditionItem();
/* 1032 */           String[] arrayOfString = { str12 + "_select", str12 + "_start", str12 + "_end" };
/* 1033 */           searchConditionItem2 = conditionFactory.createCondition(conditionType, i, arrayOfString);
/* 1034 */           List<SearchConditionOption> list = getDateSelectFromTo(this.user.getLanguage());
/* 1035 */           searchConditionItem2.setOptions(list);
/* 1036 */           arrayList.add(searchConditionItem2); continue;
/* 1037 */         }  if (str10.equals("19")) {
/* 1038 */           conditionType = ConditionType.TIMEPICKER;
/* 1039 */           arrayList.add(conditionFactory.createCondition(conditionType, i, str12)); continue;
/* 1040 */         }  if (str10.equals("402")) {
/* 1041 */           searchConditionItem = conditionFactory.createCondition(ConditionType.DATEPICKER, i, str12, str10);
/* 1042 */           if (!this.isMobile) {
/* 1043 */             searchConditionItem.setFormat("yyyy");
/* 1044 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1045 */             hashMap1.put("format", "yyyy");
/* 1046 */             searchConditionItem.setOtherParams(hashMap1);
/*      */           } 
/* 1048 */           searchConditionItem.setMode("year");
/* 1049 */           searchConditionItem.setShowTime(false);
/* 1050 */           arrayList.add(searchConditionItem); continue;
/* 1051 */         }  if (str10.equals("403")) {
/* 1052 */           searchConditionItem = conditionFactory.createCondition(ConditionType.DATEPICKER, i, str12, str10);
/* 1053 */           if (!this.isMobile) {
/* 1054 */             searchConditionItem.setFormat("yyyy-MM");
/* 1055 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1056 */             hashMap1.put("format", "yyyy-MM");
/* 1057 */             searchConditionItem.setOtherParams(hashMap1);
/*      */           } 
/* 1059 */           searchConditionItem.setMode("month");
/* 1060 */           searchConditionItem.setShowTime(false);
/* 1061 */           arrayList.add(searchConditionItem); continue;
/*      */         } 
/* 1063 */         conditionType = ConditionType.BROWSER;
/* 1064 */         SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(conditionType, i, str12, str10);
/* 1065 */         if ("161".equals(str10) || "162".equals(str10) || "256".equals(str10) || "257".equals(str10)) {
/* 1066 */           BrowserBean browserBean = searchConditionItem1.getBrowserConditionParam();
/* 1067 */           browserBean.getDestDataParams().put("type", str8);
/*      */           
/* 1069 */           String str = str8;
/* 1070 */           if ("161".equals(str10) || "162".equals(str10)) {
/*      */             try {
/* 1072 */               Browser browser = (Browser)StaticObj.getServiceByFullname(str, Browser.class);
/* 1073 */               ArrayList arrayList1 = new ArrayList();
/* 1074 */               Map<String, String> map1 = browserBean.getDataParams();
/* 1075 */               map1.put("type", str);
/* 1076 */               map1.put("currenttime", Long.valueOf(System.currentTimeMillis()));
/*      */               
/* 1078 */               Map<String, String> map2 = browserBean.getCompleteParams();
/* 1079 */               map2.put("fielddbtype", str);
/* 1080 */               map2.put("type", str10);
/*      */               
/* 1082 */               Map<String, String> map3 = browserBean.getConditionDataParams();
/* 1083 */               map3.put("type", str);
/*      */               
/* 1085 */               browserBean.setLinkUrl(browser.getHref());
/* 1086 */               browserBean.setRelateFieldid(arrayList1);
/* 1087 */               browserBean.setHasAdvanceSerach(!"1".equals(Util.null2String(browser.getShowtree())));
/* 1088 */               browserBean.setTitle(browser.getName());
/* 1089 */             } catch (Exception exception) {
/* 1090 */               exception.printStackTrace();
/*      */             } 
/*      */           } else {
/* 1093 */             BrowserInitUtil browserInitUtil = new BrowserInitUtil();
/* 1094 */             browserBean.getDataParams().put("type", str);
/* 1095 */             browserBean.setType(String.valueOf(str10));
/* 1096 */             browserInitUtil.initBrowser(browserBean, this.user.getLanguage());
/*      */           }
/*      */         
/*      */         }
/* 1100 */         else if ("7".equals(str10) && 1 == Util.getIntValue(str11)) {
/* 1101 */           BrowserBean browserBean = searchConditionItem1.getBrowserConditionParam();
/* 1102 */           Map<String, String> map1 = browserBean.getDataParams();
/* 1103 */           XssUtil xssUtil = new XssUtil();
/* 1104 */           map1.put("sqlwhere", xssUtil.put("where t1.type=2"));
/* 1105 */           Map<String, String> map2 = browserBean.getCompleteParams();
/* 1106 */           map2.put("sqlwhere", xssUtil.put("where t1.type=2"));
/*      */         } 
/* 1108 */         if (str6.equals("capitalgroupid")) {
/* 1109 */           BrowserBean browserBean = searchConditionItem1.getBrowserConditionParam();
/*      */           
/* 1111 */           Map<String, Character> map1 = browserBean.getDataParams();
/* 1112 */           map1.put("onlyendnode", Character.valueOf('n'));
/* 1113 */           Map<String, Character> map2 = browserBean.getCompleteParams();
/* 1114 */           map2.put("onlyendnode", Character.valueOf('n'));
/* 1115 */           Map<String, Character> map3 = browserBean.getConditionDataParams();
/* 1116 */           map3.put("onlyendnode", Character.valueOf('n'));
/* 1117 */           browserBean.setIsSingle(false);
/*      */         } 
/* 1119 */         arrayList.add(searchConditionItem1); continue;
/*      */       } 
/* 1121 */       if ("4".equals(str9)) {
/*      */         
/* 1123 */         if ("sptcount".equals(str6)) {
/*      */           
/* 1125 */           conditionType = ConditionType.SELECT;
/* 1126 */           ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*      */           
/* 1128 */           SearchConditionOption searchConditionOption = new SearchConditionOption();
/* 1129 */           searchConditionOption.setKey("");
/* 1130 */           searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(332, Util.getIntValue(this.user.getLanguage())));
/* 1131 */           searchConditionOption.setSelected(false);
/* 1132 */           arrayList1.add(searchConditionOption);
/*      */           
/* 1134 */           searchConditionOption = new SearchConditionOption();
/* 1135 */           searchConditionOption.setKey("1");
/* 1136 */           searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(1363, this.user.getLanguage()));
/* 1137 */           searchConditionOption.setSelected(false);
/* 1138 */           arrayList1.add(searchConditionOption);
/*      */           
/* 1140 */           searchConditionOption = new SearchConditionOption();
/* 1141 */           searchConditionOption.setKey("0");
/* 1142 */           searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(125023, this.user.getLanguage()));
/* 1143 */           searchConditionOption.setSelected(false);
/* 1144 */           arrayList1.add(searchConditionOption);
/* 1145 */           SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(conditionType, i, str6, arrayList1);
/* 1146 */           arrayList.add(searchConditionItem1); continue;
/*      */         } 
/* 1148 */         conditionType = ConditionType.CHECKBOX;
/* 1149 */         arrayList.add(conditionFactory.createCondition(conditionType, i, str12)); continue;
/*      */       } 
/* 1151 */       if ("5".equals(str9)) {
/*      */         
/* 1153 */         String str13 = "";
/* 1154 */         String str14 = "";
/* 1155 */         recordSet2.executeQuery("select iscommon,cid from cptDefineField where id=?", new Object[] { str7 });
/* 1156 */         if (recordSet2.next()) {
/* 1157 */           str13 = Util.null2String(recordSet2.getString("iscommon"));
/* 1158 */           str14 = Util.null2String(recordSet2.getString("cid"));
/*      */         } 
/* 1160 */         conditionType = ConditionType.SELECT;
/* 1161 */         ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 1162 */         SearchConditionOption searchConditionOption = new SearchConditionOption();
/* 1163 */         searchConditionOption.setKey("");
/* 1164 */         searchConditionOption.setShowname(SystemEnv.getHtmlLabelName(332, this.user.getLanguage()));
/* 1165 */         searchConditionOption.setSelected(true);
/* 1166 */         arrayList1.add(searchConditionOption);
/* 1167 */         if (str13.equals("1")) {
/* 1168 */           ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 1169 */           recordSet2.executeQuery("select id,name,defaultvalue,disorder from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str14 });
/* 1170 */           byte b = 0;
/* 1171 */           while (recordSet2.next()) {
/* 1172 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1173 */             hashMap1.put("key", Integer.valueOf(b));
/* 1174 */             hashMap1.put("showname", Util.null2String(recordSet2.getString("name")));
/* 1175 */             hashMap1.put("selected", Boolean.valueOf(false));
/* 1176 */             hashMap1.put("disorder", Integer.valueOf(recordSet2.getInt("disorder")));
/* 1177 */             arrayList2.add(hashMap1);
/* 1178 */             b++;
/*      */           } 
/* 1180 */           Collections.sort(arrayList2, (paramMap1, paramMap2) -> ((Integer)paramMap1.get("disorder")).intValue() - ((Integer)paramMap2.get("disorder")).intValue());
/* 1181 */           for (Map<Object, Object> map : arrayList2) {
/* 1182 */             searchConditionOption = new SearchConditionOption();
/* 1183 */             searchConditionOption.setKey((new StringBuilder()).append(map.get("key")).append("").toString());
/* 1184 */             searchConditionOption.setShowname((new StringBuilder()).append(map.get("showname")).append("").toString());
/* 1185 */             searchConditionOption.setSelected(((Boolean)map.get("selected")).booleanValue());
/* 1186 */             arrayList1.add(searchConditionOption);
/*      */           } 
/*      */         } else {
/* 1189 */           byte b = 2;
/* 1190 */           recordSet2.executeProc("cpt_selectitembyid_new", "" + str7 + b + '\001');
/* 1191 */           while (recordSet2.next()) {
/* 1192 */             String str15 = Util.null2String(recordSet2.getString("selectvalue"));
/* 1193 */             String str16 = Util.toScreen(recordSet2.getString("selectname"), this.user.getLanguage());
/* 1194 */             String str17 = Util.null2String(recordSet2.getString("selectlabel"));
/*      */             
/* 1196 */             if (!"".equals(str17)) {
/* 1197 */               str16 = SystemEnv.getHtmlLabelNames(str17, this.user.getLanguage());
/*      */             }
/* 1199 */             searchConditionOption = new SearchConditionOption();
/* 1200 */             searchConditionOption.setKey(str15);
/* 1201 */             searchConditionOption.setShowname(str16);
/* 1202 */             searchConditionOption.setSelected(false);
/* 1203 */             arrayList1.add(searchConditionOption);
/*      */           } 
/*      */         } 
/* 1206 */         arrayList.add(conditionFactory.createCondition(conditionType, i, str12, arrayList1));
/*      */       } 
/*      */     } 
/* 1209 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 1210 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 1215 */     String str1 = "";
/* 1216 */     BrowserManager browserManager = new BrowserManager();
/* 1217 */     RecordSet recordSet1 = new RecordSet();
/* 1218 */     RecordSet recordSet2 = new RecordSet();
/* 1219 */     CommonShareManager commonShareManager = new CommonShareManager();
/* 1220 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 1221 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*      */     
/* 1223 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("sqlwhere"));
/* 1224 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("billid"));
/* 1225 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("isdata"), 2);
/* 1226 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("cptstateid"));
/* 1227 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("cptuse"));
/* 1228 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("cptsptcount"));
/* 1229 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("wfid"));
/* 1230 */     String str8 = Util.null2s(paramHttpServletRequest.getParameter("inculdeNumZero"), "1");
/* 1231 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("mtype"));
/* 1232 */     recordSet1.execute("select * from cpt_barcodesettings");
/* 1233 */     recordSet1.next();
/* 1234 */     String str10 = Util.null2String(recordSet1.getString("userfilter"));
/* 1235 */     String str11 = Util.null2String(recordSet1.getString("useHrm"));
/* 1236 */     String str12 = Util.null2String(recordSet1.getString("useHrms"));
/* 1237 */     boolean bool1 = "0".equals(str10);
/* 1238 */     boolean bool2 = "1".equals(str11);
/* 1239 */     String str13 = Util.null2String(str12);
/* 1240 */     ArrayList arrayList = Util.TokenizerString(str13, ",");
/* 1241 */     int j = Util.getIntValue(str3);
/* 1242 */     String str14 = "";
/*      */     
/* 1244 */     if (str2.indexOf("isdata") != -1) {
/* 1245 */       if (str2.substring(str2.indexOf("isdata='") + 8, str2.indexOf("isdata='") + 9).equals("2")) {
/* 1246 */         i = 2;
/* 1247 */       } else if (str2.substring(str2.indexOf("isdata=") + 7, str2.indexOf("isdata=") + 8).equals("2")) {
/* 1248 */         i = 2;
/* 1249 */       } else if (str2.substring(str2.indexOf("isdata='") + 8, str2.indexOf("isdata='") + 9).equals("1")) {
/* 1250 */         i = 1;
/* 1251 */       } else if (str2.substring(str2.indexOf("isdata=") + 7, str2.indexOf("isdata=") + 8).equals("1")) {
/* 1252 */         i = 1;
/*      */       } 
/*      */     }
/* 1255 */     str14 = " isdata='" + i + "'";
/* 1256 */     if (!str3.equals("") && i == 2)
/*      */     {
/* 1258 */       switch (j) {
/*      */         case 19:
/* 1260 */           str4 = "1";
/* 1261 */           str8 = "0";
/*      */           break;
/*      */         case 220:
/* 1264 */           str6 = "1";
/* 1265 */           str4 = "1";
/* 1266 */           str8 = "0";
/*      */           break;
/*      */         case 18:
/* 1269 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, "move");
/* 1270 */           str4 = "1,2";
/* 1271 */           str8 = "0";
/*      */           break;
/*      */         case 224:
/* 1274 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, "back");
/* 1275 */           str4 = "2,3,4";
/* 1276 */           str8 = "0";
/*      */           break;
/*      */         case 201:
/* 1279 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, "discard");
/* 1280 */           str4 = "1,2,3,4";
/* 1281 */           str8 = "0";
/*      */           break;
/*      */         case 222:
/* 1284 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, "mend");
/* 1285 */           str6 = "1";
/* 1286 */           str4 = "1,2,3";
/* 1287 */           str8 = "0";
/*      */           break;
/*      */         case 221:
/* 1290 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, "loss");
/* 1291 */           str4 = "1,2,3,4";
/* 1292 */           str8 = "0";
/*      */           break;
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/* 1300 */     String str15 = " CptCapital t1 ";
/* 1301 */     String str16 = " t1.id,t1.name";
/*      */ 
/*      */     
/* 1304 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("reqid"), 0);
/* 1305 */     String str17 = "";
/*      */     
/* 1307 */     if (k > 0 && i == 2) {
/* 1308 */       String str = "select t1.currentnodetype,t1.workflowid,t2.formid from workflow_requestbase t1,workflow_base t2 where t1.workflowid=t2.id and t1.requestid=" + k;
/* 1309 */       int i5 = 0;
/* 1310 */       int i6 = 0;
/* 1311 */       recordSet1.execute(str);
/* 1312 */       while (recordSet1.next()) {
/* 1313 */         i5 = recordSet1.getInt("formid");
/* 1314 */         i6 = recordSet1.getInt("currentnodetype");
/*      */       } 
/* 1316 */       if (i6 > 0 && i6 < 3) {
/* 1317 */         String str22 = "";
/* 1318 */         if (i5 == 18 || i5 == 19 || i5 == 201 || i5 == 221) {
/* 1319 */           if (i5 == 221) {
/* 1320 */             str22 = "select d.lossCpt as as currentzcid,sum(d.losscount) as currentreqnum from bill_cptloss d where d.requestid=" + k + " group by d.lossCpt ";
/* 1321 */           } else if (i5 == 18) {
/* 1322 */             str22 = "select d.capitalid as as currentzcid,sum(d.number_n) as currentreqnum from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and m.requestid=" + k + " group by d.capitalid ";
/*      */           }
/* 1324 */           else if (i5 == 201) {
/* 1325 */             str22 = "select d.capitalid as as currentzcid,sum(d.numbers) as currentreqnum from bill_Discard_Detail d where d.detailrequestid=" + k + " group by d.capitalid ";
/*      */           }
/* 1327 */           else if (i5 == 19) {
/* 1328 */             str22 = "SELECT d.capitalid as as currentzcid,sum(d.number_n) as currentreqnum FROM bill_CptFetchDetail d ,bill_CptFetchMain m WHERE d.cptfetchid =m.id AND m.requestid = " + k + " group by d.capitalid ";
/*      */           }
/*      */         
/*      */         } else {
/*      */           
/* 1333 */           CptWfUtil cptWfUtil = new CptWfUtil();
/* 1334 */           JSONObject jSONObject = cptWfUtil.getCptwfInfo("" + str7);
/* 1335 */           if (jSONObject.length() > 0) {
/* 1336 */             String str23 = "formtable_main_" + -i5;
/* 1337 */             recordSet1.execute("select tablename from workflow_bill where id=" + i5);
/* 1338 */             while (recordSet1.next()) {
/* 1339 */               str23 = recordSet1.getString("tablename");
/*      */             }
/* 1341 */             String str24 = str23;
/* 1342 */             String str25 = jSONObject.getString("zcname");
/* 1343 */             String str26 = jSONObject.getString("slname");
/* 1344 */             int i7 = Util.getIntValue("" + jSONObject.getInt("zctype"), 0);
/* 1345 */             if (i7 == 1) {
/* 1346 */               str24 = str24 + "_dt1";
/* 1347 */             } else if (i7 == 2) {
/* 1348 */               str24 = str24 + "_dt2";
/* 1349 */             } else if (i7 == 3) {
/* 1350 */               str24 = str24 + "_dt3";
/* 1351 */             } else if (i7 == 4) {
/* 1352 */               str24 = str24 + "_dt4";
/*      */             } 
/*      */             
/* 1355 */             if (!str24.equals(str23)) {
/* 1356 */               str22 = " select d." + str25 + " as currentzcid,sum(d." + str26 + ") as currentreqnum from " + str23 + " m ," + str24 + " d where d.mainid=m.id and m.requestid=" + k + " group by d." + str25 + " ";
/*      */             } else {
/*      */               
/* 1359 */               str22 = "select m." + str25 + " as currentzcid,sum(m." + str26 + ") as currentreqnum from " + str23 + " m  where  m.requestid=" + k + " group by m." + str25 + " ";
/*      */             } 
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1365 */         if (!"".equals(str22)) {
/* 1366 */           str15 = " CptCapital t1 left outer join (" + str22 + ") tt2 on tt2.currentzcid=t1.id ";
/*      */         }
/*      */         
/* 1369 */         if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 1370 */           str17 = " and  (nvl(capitalnum,0)-nvl(frozennum,0)+nvl(currentreqnum,0))>0 ";
/* 1371 */         } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 1372 */           str17 = " and  (ifnull(capitalnum,0)-ifnull(frozennum,0)+ifnull(currentreqnum,0))>0 ";
/*      */         } else {
/* 1374 */           str17 = " and  (isnull(capitalnum,0)-isnull(frozennum,0)+isnull(currentreqnum,0))>0 ";
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1385 */     if (!"".equals(str9) && (
/* 1386 */       "cptmove".equalsIgnoreCase(str9) || "cptback".equalsIgnoreCase(str9) || "cptdiscard".equalsIgnoreCase(str9) || "cptloss".equalsIgnoreCase(str9) || "cptmend".equalsIgnoreCase(str9))) {
/* 1387 */       str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, str9);
/*      */     }
/*      */     
/* 1390 */     String str18 = "";
/* 1391 */     if (!"".equals(str7) && i == 2) {
/* 1392 */       CptWfUtil cptWfUtil = new CptWfUtil();
/* 1393 */       str18 = cptWfUtil.getWftype(str7);
/* 1394 */       if (!"".equals(str18)) {
/* 1395 */         if ("fetch".equalsIgnoreCase(str18) || j == 19) {
/* 1396 */           str5 = "1";
/* 1397 */           str4 = "1";
/* 1398 */         } else if ("lend".equalsIgnoreCase(str18) || j == 220) {
/* 1399 */           str4 = "1";
/* 1400 */           str6 = "1";
/* 1401 */         } else if ("move".equalsIgnoreCase(str18) || j == 18) {
/* 1402 */           str4 = "1,2";
/* 1403 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, str18);
/* 1404 */         } else if ("back".equalsIgnoreCase(str18) || j == 224) {
/* 1405 */           str4 = "2,3,4";
/* 1406 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, str18);
/* 1407 */         } else if ("discard".equalsIgnoreCase(str18) || j == 201) {
/* 1408 */           str4 = "1,2,3,4";
/* 1409 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, str18);
/* 1410 */         } else if ("mend".equalsIgnoreCase(str18) || j == 222) {
/* 1411 */           str4 = "1,2,3";
/* 1412 */           str6 = "1";
/* 1413 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, str18);
/* 1414 */         } else if ("loss".equalsIgnoreCase(str18) || j == 221) {
/* 1415 */           str4 = "1,2,3,4";
/* 1416 */           str14 = str14 + getResourceSql(str14, bool1, bool2, arrayList, str18);
/*      */         } 
/* 1418 */         str8 = "0";
/*      */       } 
/*      */     } 
/*      */     
/* 1422 */     if (!"1".equals(str8) && 2 == i) {
/* 1423 */       if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 1424 */         str17 = " and (nvl(capitalnum,0)-nvl(frozennum,0))>0 ";
/* 1425 */       } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 1426 */         str17 = " and (ifnull(capitalnum,0)-ifnull(frozennum,0))>0 ";
/*      */       } else {
/* 1428 */         str17 = " and (isnull(capitalnum,0)-isnull(frozennum,0))>0 ";
/*      */       } 
/*      */     }
/*      */     
/* 1432 */     if (i == 2) {
/* 1433 */       if (!this.user.getLoginid().equalsIgnoreCase("sysadmin")) {
/* 1434 */         str14 = str14 + commonShareManager.getAssortmentSqlWhere(this.user);
/*      */       }
/* 1436 */       if ("1".equals(str5)) {
/* 1437 */         str14 = str14 + " and stateid=1 ";
/* 1438 */       } else if (!"".equals(str4)) {
/* 1439 */         str14 = str14 + " and stateid in(" + str4 + ") ";
/*      */       } 
/* 1441 */       if ("1".equals(str6)) {
/* 1442 */         str14 = str14 + " and sptcount='1' ";
/*      */       }
/*      */     } 
/* 1445 */     if (str9.equals("cptchange") || str18.equals("change")) {
/* 1446 */       if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 1447 */         str14 = str14 + " and ((sptcount=1 and nvl(frozennum,0)=0) or sptcount<>1)";
/* 1448 */       } else if ("mysql".equalsIgnoreCase(recordSet1.getDBType())) {
/* 1449 */         str14 = str14 + " and ((sptcount=1 and ifnull(frozennum,0)=0) or sptcount<>1)";
/*      */       } else {
/* 1451 */         str14 = str14 + " and ((sptcount=1 and isnull(frozennum,0)=0) or sptcount<>1)";
/*      */       } 
/*      */     } else {
/* 1454 */       str14 = str14 + str17;
/*      */     } 
/* 1456 */     if (i == 1) {
/* 1457 */       str14 = str14 + " and (t1.cancelled is null or t1.cancelled=0)";
/*      */     }
/*      */ 
/*      */     
/* 1461 */     recordSet1.execute("select cptdetachable from SystemSet");
/* 1462 */     int m = 0;
/* 1463 */     if (recordSet1.next()) {
/* 1464 */       m = recordSet1.getInt("cptdetachable");
/*      */     }
/* 1466 */     int n = this.user.getUserSubCompany1();
/* 1467 */     int i1 = this.user.getUID();
/* 1468 */     char c = Util.getSeparator();
/* 1469 */     String str19 = "";
/* 1470 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", this.user)) {
/* 1471 */       str19 = "Capital:Maintenance";
/*      */     }
/* 1473 */     String str20 = "";
/*      */     
/* 1475 */     if (m == 1 && i1 != 1) {
/* 1476 */       if (i == 2) {
/* 1477 */         String str = "";
/* 1478 */         recordSet2.executeProc("HrmRoleSR_SeByURId", "" + i1 + c + str19);
/* 1479 */         while (recordSet2.next()) {
/* 1480 */           str20 = recordSet2.getString("subcompanyid");
/* 1481 */           str = str + ", " + str20;
/*      */         } 
/* 1483 */         if (!"".equals(str)) {
/* 1484 */           str = str.substring(1);
/* 1485 */           str14 = str14 + " and blongsubcompany in (" + str + ") ";
/*      */         } else {
/* 1487 */           str14 = str14 + " and blongsubcompany in (" + n + ") ";
/*      */         } 
/* 1489 */       } else if (i == 1) {
/* 1490 */         int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), str19);
/* 1491 */         String str = "";
/* 1492 */         for (byte b1 = 0; b1 < arrayOfInt.length; b1++) {
/* 1493 */           if (arrayOfInt[b1] > 0) {
/* 1494 */             str = str + (str.equals("") ? "" : ",") + arrayOfInt[b1];
/*      */           }
/*      */         } 
/* 1497 */         if (str.equals(""))
/* 1498 */           str = this.user.getUserSubCompany1() + ""; 
/* 1499 */         if (!"".equals(str)) {
/* 1500 */           str14 = str14 + " and blongsubcompany in (" + str + ") ";
/*      */         } else {
/* 1502 */           str14 = str14 + " and blongsubcompany in (" + str + ") ";
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 1509 */     int i2 = Util.getIntValue(paramHttpServletRequest.getParameter("wfid"), -1);
/* 1510 */     int i3 = Util.getIntValue(paramHttpServletRequest.getParameter("fieldid"), -1);
/* 1511 */     int i4 = Util.getIntValue(paramHttpServletRequest.getParameter("viewtype"), -1);
/* 1512 */     List<?> list = null;
/* 1513 */     if (paramHttpServletRequest.getParameter("wfid") != null && (list = ConditionField.readAll(i2, i3, i4)).size() > 0) {
/* 1514 */       for (byte b1 = 0; b1 < list.size(); b1++) {
/* 1515 */         ConditionField conditionField = (ConditionField)list.get(b1);
/* 1516 */         String str22 = conditionField.getFieldName();
/* 1517 */         String str23 = conditionField.getValue();
/* 1518 */         if ("departmentid".equalsIgnoreCase(str22)) {
/* 1519 */           String str = conditionField.getValueType();
/* 1520 */           if ("1".equals(str)) {
/* 1521 */             str23 = resourceComInfo.getDepartmentID("" + this.user.getUID());
/* 1522 */             if (!"".equals(str23)) {
/* 1523 */               str14 = str14 + " and t1.departmentid='" + str23 + "' ";
/*      */             }
/*      */           } 
/* 1526 */         } else if ("blongdepartment".equalsIgnoreCase(str22)) {
/* 1527 */           String str = conditionField.getValueType();
/* 1528 */           if ("1".equals(str)) {
/* 1529 */             str23 = resourceComInfo.getDepartmentID("" + this.user.getUID());
/* 1530 */             if (!"".equals(str23)) {
/* 1531 */               str14 = str14 + " and t1.blongdepartment='" + str23 + "' ";
/*      */             }
/*      */           } 
/* 1534 */         } else if ("blongsubcompany".equalsIgnoreCase(str22)) {
/* 1535 */           String str = conditionField.getValueType();
/* 1536 */           if ("1".equals(str)) {
/* 1537 */             str23 = resourceComInfo.getSubCompanyID("" + this.user.getUID());
/* 1538 */             if (!"".equals(str23)) {
/* 1539 */               str14 = str14 + " and t1.blongsubcompany='" + str23 + "' ";
/*      */             }
/*      */           } 
/* 1542 */         } else if ("resourceid".equalsIgnoreCase(str22)) {
/* 1543 */           String str = conditionField.getValueType();
/* 1544 */           if ("1".equals(str)) {
/* 1545 */             str23 = "" + this.user.getUID();
/* 1546 */             if (!"".equals(str23)) {
/* 1547 */               str14 = str14 + " and t1.resourceid='" + str23 + "' ";
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     }
/*      */     
/* 1554 */     String str21 = "name,fnamark,mark,departmentid,capitalspec,blongdepartment,blongsubcompany,resourceid,capitalgroupid,capitaltypeid,warehouse";
/* 1555 */     String[] arrayOfString = str21.split(",");
/* 1556 */     for (String str22 : arrayOfString) {
/* 1557 */       String str23 = Util.null2String(this.dataDefinitionParams.get(str22));
/* 1558 */       if (!str23.equals("")) {
/* 1559 */         if (str22.equals("name") || str22.equals("fnamark") || str22.equals("mark") || str22.equals("capitalspec")) {
/* 1560 */           str14 = str14 + " and t1." + str22 + " like '%" + str23 + "%' ";
/*      */         }
/* 1562 */         else if (str22.equals("capitalgroupid")) {
/* 1563 */           str14 = str14 + " and (t1.capitalgroupid in(" + str23 + ") ";
/* 1564 */           String[] arrayOfString1 = str23.split(",");
/* 1565 */           for (String str : arrayOfString1) {
/* 1566 */             str14 = str14 + " or t1.capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + str + "|%')";
/*      */           }
/* 1568 */           str14 = str14 + ")";
/*      */         } else {
/* 1570 */           str14 = str14 + " and t1." + str22 + " in(" + str23 + ") ";
/*      */         } 
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/* 1576 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1577 */     str1 = browserManager.getResult(paramHttpServletRequest, str16, str15, str14, 1000);
/* 1578 */     CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 1579 */     JSONArray jSONArray = (JSONArray)JSON.parse(str1);
/* 1580 */     for (byte b = 0; b < jSONArray.size(); b++) {
/* 1581 */       JSONObject jSONObject = (JSONObject)jSONArray.get(b);
/* 1582 */       String str = jSONObject.getString("id");
/* 1583 */       jSONObject.put("title", jSONObject.get("name") + "&nbsp;|&nbsp;" + capitalComInfo.getMark(str) + "&nbsp;|&nbsp;" + capitalComInfo.getCapitalspec(str));
/*      */     } 
/* 1585 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray);
/* 1586 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> getDateRangeByDateField(String paramString1, String paramString2, String paramString3) {
/* 1598 */     Calendar calendar = Calendar.getInstance();
/* 1599 */     Date date = calendar.getTime();
/* 1600 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1601 */     String str1 = "";
/* 1602 */     String str2 = "";
/* 1603 */     if ("1".equals(paramString1)) {
/* 1604 */       str1 = DateHelper.getCurrentDate();
/* 1605 */       str2 = DateHelper.getCurrentDate();
/* 1606 */     } else if ("2".equals(paramString1)) {
/* 1607 */       calendar.set(7, 1);
/* 1608 */       date = calendar.getTime();
/* 1609 */       str1 = DateHelper.convertDateIntoYYYYMMDDStr(date);
/*      */       
/* 1611 */       calendar.set(7, 7);
/* 1612 */       date = calendar.getTime();
/* 1613 */       str2 = DateHelper.convertDateIntoYYYYMMDDStr(date);
/* 1614 */     } else if ("3".equals(paramString1)) {
/* 1615 */       str1 = DateHelper.getFirstDayOfMonthWeek(date);
/* 1616 */       str2 = DateHelper.getLastDayOfMonthWeek(date);
/* 1617 */     } else if ("4".equals(paramString1)) {
/* 1618 */       str1 = getQuarterStart();
/* 1619 */       str2 = getQuarterEnd();
/* 1620 */     } else if ("5".equals(paramString1)) {
/* 1621 */       int i = Util.getIntValue(DateHelper.getCurrentYear());
/* 1622 */       str1 = getFirstDayOfYear(i);
/* 1623 */       str2 = getLastDayOfYear(i);
/* 1624 */     } else if ("6".equals(paramString1)) {
/* 1625 */       str1 = paramString2;
/* 1626 */       str2 = paramString3;
/* 1627 */     } else if ("7".equals(paramString1)) {
/* 1628 */       calendar.add(2, -1);
/* 1629 */       date = calendar.getTime();
/* 1630 */       str1 = DateHelper.getFirstDayOfMonthWeek(date);
/* 1631 */       str2 = DateHelper.getLastDayOfMonthWeek(date);
/* 1632 */     } else if ("8".equals(paramString1)) {
/* 1633 */       int i = Util.getIntValue(DateHelper.getCurrentYear()) - 1;
/* 1634 */       str1 = getFirstDayOfYear(i);
/* 1635 */       str2 = getLastDayOfYear(i);
/*      */     } 
/* 1637 */     hashMap.put("startdate", str1);
/* 1638 */     hashMap.put("enddate", str2);
/* 1639 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getFirstDayOfYear(int paramInt) {
/* 1648 */     Calendar calendar = Calendar.getInstance();
/* 1649 */     calendar.clear();
/* 1650 */     calendar.set(1, paramInt);
/* 1651 */     Date date = calendar.getTime();
/* 1652 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getLastDayOfYear(int paramInt) {
/* 1662 */     Calendar calendar = Calendar.getInstance();
/* 1663 */     calendar.clear();
/* 1664 */     calendar.set(1, paramInt);
/* 1665 */     calendar.roll(6, -1);
/* 1666 */     Date date = calendar.getTime();
/* 1667 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getQuarterStart() {
/* 1676 */     Calendar calendar = Calendar.getInstance();
/* 1677 */     int i = calendar.get(2) + 1;
/* 1678 */     if (i >= 1 && i <= 3) {
/* 1679 */       calendar.set(2, 0);
/* 1680 */     } else if (i >= 4 && i <= 6) {
/* 1681 */       calendar.set(2, 3);
/* 1682 */     } else if (i >= 7 && i <= 9) {
/* 1683 */       calendar.set(2, 6);
/* 1684 */     } else if (i >= 10 && i <= 12) {
/* 1685 */       calendar.set(2, 9);
/* 1686 */     }  calendar.set(5, 1);
/* 1687 */     Date date = calendar.getTime();
/* 1688 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getQuarterEnd() {
/* 1697 */     Calendar calendar = Calendar.getInstance();
/* 1698 */     int i = calendar.get(2) + 1;
/* 1699 */     if (i >= 1 && i <= 3) {
/* 1700 */       calendar.set(2, 2);
/* 1701 */       calendar.set(5, 31);
/* 1702 */     } else if (i >= 4 && i <= 6) {
/* 1703 */       calendar.set(2, 5);
/* 1704 */       calendar.set(5, 30);
/* 1705 */     } else if (i >= 7 && i <= 9) {
/* 1706 */       calendar.set(2, 8);
/* 1707 */       calendar.set(5, 30);
/* 1708 */     } else if (i >= 10 && i <= 12) {
/* 1709 */       calendar.set(2, 11);
/* 1710 */       calendar.set(5, 31);
/*      */     } 
/* 1712 */     Date date = calendar.getTime();
/* 1713 */     return DateHelper.convertDateIntoYYYYMMDDStr(date);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<SearchConditionOption> getDateSelectFromTo(int paramInt) {
/* 1721 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 1722 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, paramInt), true));
/* 1723 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, paramInt)));
/* 1724 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, paramInt)));
/* 1725 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, paramInt)));
/* 1726 */     arrayList.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(27347, paramInt)));
/* 1727 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, paramInt)));
/* 1728 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, paramInt)));
/* 1729 */     arrayList.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(81716, paramInt)));
/* 1730 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, paramInt)));
/* 1731 */     return arrayList;
/*      */   }
/*      */   
/*      */   public String getResourceSql(String paramString1, boolean paramBoolean1, boolean paramBoolean2, ArrayList paramArrayList, String paramString2) {
/* 1735 */     RecordSet recordSet = new RecordSet();
/* 1736 */     String str = "";
/* 1737 */     CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/* 1738 */     boolean bool = capitalTransMethod.IsWareHouseOpen();
/* 1739 */     if (!this.user.getLoginid().equalsIgnoreCase("sysadmin")) {
/* 1740 */       if ("cptmove".equalsIgnoreCase(paramString2) || "move".equalsIgnoreCase(paramString2)) {
/* 1741 */         String str1 = "";
/* 1742 */         if (bool) {
/* 1743 */           if (recordSet.getDBType().equals("oracle")) {
/* 1744 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where ','||manager||',' like '%," + this.user.getUID() + ",%') and sptcount=0 and isdata=2) ";
/* 1745 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 1746 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where concat(',',manager,',') like '%," + this.user.getUID() + ",%') and sptcount=0 and isdata=2) ";
/*      */           }
/* 1748 */           else if (recordSet.getDBType().equals("postgresql")) {
/* 1749 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where ','||manager||',' like '%," + this.user.getUID() + ",%') and sptcount=0 and isdata=2) ";
/*      */           } else {
/*      */             
/* 1752 */             str1 = " or id in(select id from CptCapital where warehouse in (select id from CptCapitalWareHouse where ','+manager+',' like '%," + this.user.getUID() + ",%') and sptcount=0 and isdata=2) ";
/*      */           } 
/*      */         }
/*      */         
/* 1756 */         if (paramBoolean1) {
/* 1757 */           if (paramString1.length() > 0) {
/* 1758 */             str = str + " and ((resourceid='" + this.user.getUID() + "' and stateid=2) " + str1 + " ) ";
/*      */           } else {
/* 1760 */             str = str + " where ((resourceid='" + this.user.getUID() + "' and stateid=2) " + str1 + " ) ";
/*      */           } 
/* 1762 */         } else if (paramBoolean2 && !paramArrayList.contains(this.user.getUID() + "")) {
/* 1763 */           if (paramString1.length() > 0) {
/* 1764 */             str = str + " and ((resourceid='" + this.user.getUID() + "' and stateid=2) " + str1 + " ) ";
/*      */           } else {
/* 1766 */             str = str + " where ((resourceid='" + this.user.getUID() + "' and stateid=2) " + str1 + " ) ";
/*      */           }
/*      */         
/* 1769 */         } else if (paramString1.length() > 0) {
/* 1770 */           str = str + " and (stateid=2 " + str1 + " ) ";
/*      */         } else {
/* 1772 */           str = str + " where (stateid=2 " + str1 + " ) ";
/*      */         }
/*      */       
/*      */       }
/* 1776 */       else if (paramBoolean1) {
/* 1777 */         if (paramString1.length() > 0) {
/* 1778 */           str = str + " and resourceid='" + this.user.getUID() + "'";
/*      */         } else {
/* 1780 */           str = str + " where resourceid='" + this.user.getUID() + "'";
/*      */         } 
/* 1782 */       } else if (paramBoolean2 && !paramArrayList.contains(this.user.getUID() + "")) {
/* 1783 */         if (paramString1.length() > 0) {
/* 1784 */           str = str + " and resourceid='" + this.user.getUID() + "'";
/*      */         } else {
/* 1786 */           str = str + " where resourceid='" + this.user.getUID() + "'";
/*      */         }
/*      */       
/*      */       }
/*      */     
/* 1791 */     } else if ("cptmove".equalsIgnoreCase(paramString2) || "move".equalsIgnoreCase(paramString2)) {
/* 1792 */       String str1 = "";
/* 1793 */       if (bool) {
/* 1794 */         str1 = " or id in(select id from CptCapital where sptcount=0 and isdata=2) ";
/*      */       }
/* 1796 */       if (paramString1.length() > 0) {
/* 1797 */         str = str + " and (stateid=2 " + str1 + " ) ";
/*      */       } else {
/* 1799 */         str = str + " where (stateid=2 " + str1 + " ) ";
/*      */       } 
/*      */     } 
/*      */     
/* 1803 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean hasChild(String paramString) throws Exception {
/* 1813 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 1814 */     capitalAssortmentComInfo.setTofirstRow();
/* 1815 */     while (capitalAssortmentComInfo.next()) {
/* 1816 */       if (capitalAssortmentComInfo.getSupAssortmentId().equals(paramString))
/* 1817 */         return true; 
/*      */     } 
/* 1819 */     return false;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CapitalBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */