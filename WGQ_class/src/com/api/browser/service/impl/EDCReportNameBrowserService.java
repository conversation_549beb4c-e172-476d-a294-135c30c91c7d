/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EDCReportNameBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  34 */     String str1 = Util.null2String(paramMap.get("name")).trim();
/*  35 */     String str2 = " where 1 = 1 ";
/*  36 */     if (!str1.equals("")) {
/*  37 */       str2 = str2 + " and t1.name like '%" + str1 + "%'";
/*     */     }
/*  39 */     String str3 = " t1.uuid id,t1.name";
/*  40 */     String str4 = " edc_reportsheet t1 ";
/*  41 */     String str5 = " t1.uuid ";
/*     */     
/*  43 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  44 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */ 
/*     */ 
/*     */     
/*  48 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(517062, this.user.getLanguage()), "name", "name", 1));
/*     */     
/*  50 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "t1.uuid", arrayList);
/*  51 */     splitTableBean.setSqlsortway("ASC");
/*  52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  53 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getLabelName(String paramString1, String paramString2) {
/*  57 */     if ("1".equals(paramString2)) {
/*  58 */       return "<span style='position:relative;'>" + paramString1 + "<span class='cube-virtual-form-flag' >V</span></span>";
/*     */     }
/*  60 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  66 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  67 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  71 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 517062, "name", true);
/*  72 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/*  74 */     arrayList1.add(new SearchConditionOption("1", "" + SystemEnv.getHtmlLabelName(15517, ThreadVarLanguage.getLang()) + ""));
/*  75 */     searchConditionItem.setOptions(arrayList1);
/*  76 */     arrayList.add(searchConditionItem);
/*     */     
/*  78 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  79 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  85 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  86 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  87 */     String str2 = "";
/*  88 */     String str3 = "SELECT t1.uuid id,t1.name FROM edc_app t1   where t1.name like '%" + str1 + "%' " + str2 + "order by t1.id";
/*     */     
/*  90 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  91 */     RecordSet recordSet = new RecordSet();
/*  92 */     recordSet.executeQuery(str3, new Object[0]);
/*  93 */     while (recordSet.next()) {
/*  94 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  95 */       hashMap1.put("id", recordSet.getString("id"));
/*  96 */       hashMap1.put("name", recordSet.getString("name"));
/*  97 */       arrayList.add(hashMap1);
/*     */     } 
/*  99 */     hashMap.put("datas", arrayList);
/*     */     
/* 101 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 102 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EDCReportNameBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */