/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.workflow.biz.requestForm.TestWorkflowCheckBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.meeting.MeetingShareUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MeetingRoomBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  40 */     int i = Util.getIntValue(Util.null2String(paramMap.get("forall")), 0);
/*  41 */     String str1 = Util.null2String(paramMap.get("fullname"));
/*  42 */     String str2 = Util.null2String(paramMap.get("description"));
/*  43 */     int j = Util.getIntValue(Util.null2String(paramMap.get("SelectSubCompany")), -1);
/*  44 */     String str3 = Util.null2String(paramMap.get("wfTestStr"));
/*  45 */     String str4 = Util.null2String(paramMap.get("mrtype"));
/*  46 */     String str5 = Util.null2String(paramMap.get("isfromScreen"));
/*  47 */     String str6 = " where 1 = 1 ";
/*     */     
/*  49 */     if ("1".equals(str5)) {
/*  50 */       str6 = str6 + " and a.screenShowType>0 ";
/*     */     }
/*     */ 
/*     */     
/*  54 */     String str7 = Util.null2String(paramMap.get("sqlwhere"));
/*  55 */     if (!"".equals(str7)) {
/*  56 */       str6 = str6 + str7;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  68 */     boolean bool = true;
/*     */     
/*  70 */     if (bool == true && j > 0) {
/*  71 */       str6 = str6 + " and a.subcompanyid = " + j;
/*     */     }
/*     */     
/*  74 */     if (!str1.equals("")) {
/*  75 */       str6 = str6 + " and a.name like '%";
/*  76 */       str6 = str6 + Util.fromScreen2(str1, this.user.getLanguage());
/*  77 */       str6 = str6 + "%'";
/*     */     } 
/*  79 */     if (!str2.equals("")) {
/*  80 */       str6 = str6 + " and a.roomdesc like '%";
/*  81 */       str6 = str6 + Util.fromScreen2(str2, this.user.getLanguage());
/*  82 */       str6 = str6 + "%'";
/*     */     } 
/*  84 */     if (!"".equals(str4)) {
/*  85 */       str6 = str6 + " and mrtype ='" + str4 + "'";
/*     */     }
/*     */     
/*  88 */     if (i != 1) {
/*  89 */       TestWorkflowCheckBiz testWorkflowCheckBiz = new TestWorkflowCheckBiz();
/*  90 */       String[] arrayOfString = testWorkflowCheckBiz.decodeTestStr(str3);
/*  91 */       User user = new User();
/*  92 */       if (arrayOfString.length == 3 && "true".equals(arrayOfString[0])) {
/*  93 */         int k = Util.getIntValue(arrayOfString[1]);
/*  94 */         int m = Util.getIntValue(arrayOfString[2], 0);
/*  95 */         user = MeetingShareUtil.generateUserObj(k, m);
/*  96 */         str6 = str6 + MeetingShareUtil.getRoomShareSql(user);
/*     */       } else {
/*  98 */         str6 = str6 + MeetingShareUtil.getRoomShareSql(this.user);
/*     */       } 
/*     */       
/* 101 */       str6 = str6 + " and (a.status=1 or a.status is null )";
/*     */     } 
/*     */     
/* 104 */     String str8 = " a.id,a.name,a.subcompanyid,a.roomdesc,a.mrtype ";
/* 105 */     String str9 = " MeetingRoom a ";
/* 106 */     String str10 = "a.dsporder,a.name";
/*     */     
/* 108 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 109 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 110 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(31232, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/* 111 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(383904, this.user.getLanguage()), "mrtype", "mrtype", "com.api.meeting.util.MeetingTransMethod.getMeetingRoomTypeShowStr")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/* 112 */     if (bool == true)
/* 113 */       arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "com.api.meeting.util.MeetingTransMethod.getMeetingSubCompany")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL)); 
/* 114 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "roomdesc", "roomdesc")).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */     
/* 116 */     SplitTableBean splitTableBean = new SplitTableBean(str8, str9, str6, str10, "a.id", arrayList);
/* 117 */     splitTableBean.setSqlsortway("ASC");
/*     */     
/* 119 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 121 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig(bool)));
/* 122 */     } catch (Exception exception) {
/* 123 */       exception.printStackTrace();
/*     */     } 
/* 125 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 126 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig(int paramInt) {
/* 134 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 135 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.name");
/* 136 */     if (paramInt == 1) {
/* 137 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.subcompanyid");
/* 138 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row3.mrtype");
/* 139 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row4.roomdesc");
/*     */     } else {
/* 141 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.mrtype");
/* 142 */       MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row3.roomdesc");
/*     */     } 
/* 144 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 154 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 155 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 156 */     arrayList.add(new SearchConditionOption("", ""));
/* 157 */     RecordSet recordSet = new RecordSet();
/* 158 */     recordSet.execute("select * from MeetingRoom_type order by dsporder");
/* 159 */     while (recordSet.next()) {
/* 160 */       arrayList.add(new SearchConditionOption(recordSet.getString("id"), recordSet.getString("name")));
/*     */     }
/*     */     
/* 163 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 164 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 165 */     arrayList1.add(conditionFactory.createCondition(ConditionType.INPUT, 31232, "fullname", true));
/* 166 */     arrayList1.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 167 */     arrayList1.add(conditionFactory.createCondition(ConditionType.BROWSER, 17868, "SelectSubCompany", "164"));
/* 168 */     arrayList1.add(conditionFactory.createCondition(ConditionType.SELECT, 383904, "mrtype", arrayList));
/* 169 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 170 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MeetingRoomBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */