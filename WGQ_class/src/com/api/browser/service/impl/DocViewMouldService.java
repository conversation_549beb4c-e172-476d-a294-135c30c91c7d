/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocViewMouldService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  91 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  92 */     String str1 = Util.null2String(paramMap.get("doctype"));
/*  93 */     if (str1 != null && "".equals(str1)) { str1 = "0"; }
/*  94 */     else if (str1 != null && !"".equals(str1))
/*  95 */     { if (str1.equals(".htm")) { str1 = "0"; }
/*  96 */       else if (str1.equals(".doc")) { str1 = "2"; }
/*  97 */       else if (str1.equals(".xls")) { str1 = "3"; }
/*  98 */       else if (str1.equals(".wps")) { str1 = "4"; }
/*     */        }
/*     */     
/* 101 */     String str2 = "id , mouldname";
/* 102 */     String str3 = " DocMould ";
/* 103 */     String str4 = " where 1=1 ";
/* 104 */     String str5 = " id ";
/*     */     
/* 106 */     str4 = str4 + " and mouldtype=" + str1;
/*     */ 
/*     */     
/* 109 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 110 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 111 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id", 1)).setIsInputCol(BoolAttr.TRUE));
/* 112 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "mouldname", "mouldname")).setIsInputCol(BoolAttr.TRUE));
/*     */ 
/*     */     
/* 115 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, "id", arrayList);
/* 116 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 117 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 122 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 123 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 124 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("doctype"));
/* 125 */     if (str2 != null && "".equals(str2)) { str2 = "0"; }
/* 126 */     else if (str2 != null && !"".equals(str2))
/* 127 */     { if (str2.equals(".htm")) { str2 = "0"; }
/* 128 */       else if (str2.equals(".doc")) { str2 = "2"; }
/* 129 */       else if (str2.equals(".xls")) { str2 = "3"; }
/* 130 */       else if (str2.equals(".wps")) { str2 = "4"; }
/*     */        }
/* 132 */      String str3 = "select id,mouldname,isuserdefault,mouldtype,systemtype from DocMould where issysdefault='0'  ";
/*     */     
/* 134 */     if (!str1.isEmpty()) {
/* 135 */       str1 = str1.replace("'", "''");
/* 136 */       str3 = str3 + " and mouldname like '%" + str1 + "%'";
/*     */     } 
/* 138 */     if (str2 != null && !"".equals(str2)) {
/* 139 */       str3 = str3 + " and mouldtype = ? ";
/*     */     }
/*     */     
/* 142 */     str3 = str3 + " order by id desc";
/* 143 */     RecordSet recordSet = new RecordSet();
/* 144 */     if (str2 != null && !"".equals(str2)) {
/* 145 */       recordSet.executeQuery(str3, new Object[] { str2 });
/*     */     } else {
/* 147 */       recordSet.executeQuery(str3, new Object[0]);
/*     */     } 
/* 149 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 150 */     while (recordSet.next()) {
/* 151 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 152 */       hashMap1.put("id", recordSet.getString("id"));
/* 153 */       hashMap1.put("name", recordSet.getString("mouldname"));
/* 154 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 157 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 158 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DocViewMouldService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */