/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.voting.util.VotingDetach;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class VotingMouldService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     String str1 = "id,subject,descr,createrid";
/*  41 */     String str2 = " where istemplate = '1'";
/*  42 */     String str3 = "voting";
/*  43 */     String str4 = "id";
/*     */     
/*  45 */     String str5 = Util.null2String(paramMap.get("subject"));
/*  46 */     if (!str5.isEmpty()) {
/*  47 */       str5 = str5.replace("'", "''");
/*  48 */       str2 = str2 + " and subject like '%" + str5 + "%'";
/*     */     } 
/*     */     
/*  51 */     String str6 = Util.null2String(paramMap.get("descr"));
/*  52 */     if (!str6.isEmpty()) {
/*  53 */       str6 = str6.replace("'", "''");
/*  54 */       str2 = str2 + " and descr like '%" + str6 + "%'";
/*     */     } 
/*     */     
/*  57 */     String str7 = Util.null2String(paramMap.get("createrid"));
/*  58 */     if (!str7.isEmpty()) {
/*  59 */       str2 = str2 + " and createrid = " + str7;
/*     */     }
/*     */     
/*  62 */     boolean bool = VotingDetach.isDetach();
/*  63 */     if (bool) {
/*  64 */       String str = Util.null2String(paramMap.get("subcompanyid"));
/*  65 */       if (!str.isEmpty()) {
/*  66 */         str2 = str2 + " and subcompanyid=" + str;
/*  67 */       } else if (this.user.getUID() != 1) {
/*  68 */         String str8 = Util.null2String(VotingDetach.getSubcompanyids(this.user));
/*  69 */         str8 = str8.startsWith(",") ? str8.substring(1) : str8;
/*  70 */         str8 = str8.endsWith(",") ? str8.substring(0, str8.length() - 1) : str8;
/*  71 */         if (str8.isEmpty()) {
/*  72 */           str2 = str2 + " and 1 <> 1 ";
/*     */         } else {
/*  74 */           str2 = str2 + " and (" + Util.getSubINClause(str8, "subcompanyid", "in") + ")";
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  79 */     str2 = Util.toHtmlForSplitPage(str2);
/*  80 */     str3 = Util.toHtmlForSplitPage(str3);
/*     */     
/*  82 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  83 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  84 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(229, this.user.getLanguage()), "subject", "subject", 1)).setIsInputCol(BoolAttr.TRUE));
/*  85 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "descr", "descr"));
/*  86 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(882, this.user.getLanguage()), "createrid", "createrid", "weaver.splitepage.transform.SptmForDoc.getHrmname", 0));
/*     */     
/*  88 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str3, str2, str4, "id", arrayList);
/*  89 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  90 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  99 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 100 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 101 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 103 */     if (!str.isEmpty()) {
/* 104 */       RecordSet recordSet = new RecordSet();
/* 105 */       String str1 = str.contains(",") ? (" in (" + str + ")") : ("=" + str);
/* 106 */       recordSet.executeQuery("select id,subject,descr from voting where id " + str1 + " order by id desc", new Object[0]);
/* 107 */       while (recordSet.next()) {
/* 108 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 109 */         hashMap1.put("id", recordSet.getString("id"));
/* 110 */         hashMap1.put("name", recordSet.getString("subject"));
/* 111 */         hashMap1.put("desc", recordSet.getString("desc"));
/* 112 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/* 115 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 116 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 117 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/* 118 */     arrayList1.add(new ListHeadBean("desc", "", 1, BoolAttr.FALSE));
/*     */     
/* 120 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 121 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 122 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 123 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 129 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 131 */     String str = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 132 */     RecordSet recordSet = new RecordSet();
/* 133 */     recordSet.executeQuery("select id,subject,descr from voting where subject like ? order by id desc", new Object[] { str });
/* 134 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 135 */     while (recordSet.next()) {
/* 136 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 137 */       hashMap1.put("id", recordSet.getString("id"));
/* 138 */       hashMap1.put("name", recordSet.getString("subject"));
/* 139 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 142 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 143 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 151 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 152 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 153 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 154 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 229, "subject", true));
/* 155 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "descr"));
/* 156 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 882, "createrid", "1"));
/* 157 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 158 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/VotingMouldService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */