/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.fnaMulDimensions.entity.FnaBrowserTreeNodeBean;
/*     */ import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
/*     */ import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaApprovalTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  37 */     if (this.user == null) {
/*  38 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  39 */       return (Map)hashMap;
/*     */     } 
/*  41 */     String str = Util.null2String(paramMap.get("list"));
/*  42 */     if (!"1".equals(str)) {
/*  43 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  44 */       SplitTableBean splitTableBean = getTableList(paramMap);
/*     */       
/*  46 */       if (splitTableBean == null) {
/*  47 */         ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  48 */         arrayList.add(new ListHeadBean("approvaTypelName", SystemEnv.getHtmlLabelName(195, this.user.getLanguage())));
/*  49 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  50 */         hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */       } else {
/*  52 */         hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */       } 
/*     */     } else {
/*     */       
/*  56 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  57 */       List<FnaBrowserTreeNodeBean> list = getTreeNodeInfo(paramMap);
/*  58 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*     */     } 
/*     */     
/*  61 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/*  72 */     String str1 = Util.null2String(paramMap.get("accountId"));
/*  73 */     String str2 = Util.null2String(paramMap.get("approvaTypelstatus"));
/*  74 */     String str3 = (new BudgetApprovalUtil()).getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE.intValue());
/*  75 */     if ("".equals(str3)) {
/*  76 */       return null;
/*     */     }
/*     */     
/*  79 */     String str4 = " approvalVersGroupId id,approvaTypelName,displayOrder ";
/*  80 */     String str5 = str3 + "  ";
/*  81 */     String str6 = " where 1=1 ";
/*  82 */     if (!"".equals(str2)) {
/*  83 */       str6 = str6 + " and approvaTypelstatus = " + StringEscapeUtils.escapeSql(str2);
/*     */     }
/*  85 */     String str7 = " displayOrder,approvalVersGroupId,approvaTypelName ";
/*  86 */     String str8 = " id ";
/*  87 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  88 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  89 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "approvaTypelName", "approvaTypelName")).setIsInputCol(BoolAttr.TRUE).setShowType(1));
/*  90 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str6, str7, str8, "ASC", arrayList);
/*  91 */     splitTableBean.setSqlisdistinct("true");
/*  92 */     return splitTableBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 102 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 103 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 105 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 106 */     String str2 = (new BudgetApprovalUtil()).getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE.intValue());
/* 107 */     String str3 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*     */     
/* 109 */     if (!"".equals(str2)) {
/* 110 */       String[] arrayOfString = str3.split(",");
/* 111 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 112 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 113 */         stringBuffer1.append("'").append(arrayOfString[b]).append("',");
/*     */       }
/* 115 */       stringBuffer1.append("'-1'");
/* 116 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 117 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 118 */       StringBuffer stringBuffer2 = new StringBuffer();
/* 119 */       stringBuffer2.append(" select approvalVersGroupId as id,approvaTypelName from " + str2 + " a ");
/* 120 */       stringBuffer2.append(" where 1=1  ");
/* 121 */       stringBuffer2.append(" and approvalVersGroupId in (").append(stringBuffer1.toString()).append(")");
/*     */       
/* 123 */       recordSet.executeQuery(stringBuffer2.toString(), new Object[0]);
/* 124 */       while (recordSet.next()) {
/* 125 */         String str4 = Util.null2String(recordSet.getString("id"));
/* 126 */         String str5 = Util.null2String(recordSet.getString("approvaTypelName"));
/*     */         
/* 128 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 129 */         hashMap2.put("id", str4);
/* 130 */         hashMap2.put("approvaTypelName", str5);
/* 131 */         hashMap1.put(str4, hashMap2);
/*     */       } 
/*     */       
/* 134 */       for (String str : arrayOfString) {
/* 135 */         Map map = (Map)hashMap1.get(str);
/* 136 */         if (map != null && map.size() > 0) {
/* 137 */           String str4 = Util.null2String((String)map.get("id"));
/* 138 */           String str5 = Util.null2String((String)map.get("approvaTypelName"));
/*     */           
/* 140 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 141 */           hashMap2.put("id", str4);
/* 142 */           hashMap2.put("approvaTypelName", str5);
/* 143 */           arrayList.add(hashMap2);
/*     */         } 
/*     */       } 
/*     */       
/* 147 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 148 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 149 */       arrayList1.add(new ListHeadBean("approvaTypelName", "", 1, BoolAttr.TRUE));
/*     */       
/* 151 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 152 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str3, "id"));
/* 153 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/*     */ 
/*     */     
/* 157 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<FnaBrowserTreeNodeBean> getTreeNodeInfo(Map<String, Object> paramMap) throws Exception {
/* 168 */     ArrayList<FnaBrowserTreeNodeBean> arrayList = new ArrayList();
/* 169 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 171 */     String str1 = Util.null2String(paramMap.get("accountId"));
/* 172 */     if ("".equals(str1)) {
/* 173 */       return null;
/*     */     }
/*     */     
/* 176 */     String str2 = Util.null2String(paramMap.get("approvaTypelstatus"));
/* 177 */     String str3 = (new BudgetApprovalUtil()).getTableName(str1, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE.intValue());
/* 178 */     String str4 = "select approvalVersGroupId as id,approvaTypelName as name from " + str3 + " where 1=1 ";
/* 179 */     if (!"".equals(str2)) {
/* 180 */       str4 = str4 + " and approvaTypelstatus = " + StringEscapeUtils.escapeSql(str2);
/*     */     }
/* 182 */     str4 = str4 + " group by approvalVersGroupId,approvaTypelName,displayOrder order by displayOrder ";
/* 183 */     recordSet.executeQuery(str4, new Object[0]);
/* 184 */     while (recordSet.next()) {
/* 185 */       String str5 = Util.null2String(recordSet.getString("id"));
/* 186 */       String str6 = "";
/*     */       
/* 188 */       String str7 = Util.null2String(recordSet.getString("name"));
/*     */       
/* 190 */       boolean bool = false;
/* 191 */       if ("".equals(str1)) {
/* 192 */         bool = true;
/*     */       }
/*     */       
/* 195 */       FnaBrowserTreeNodeBean fnaBrowserTreeNodeBean = new FnaBrowserTreeNodeBean();
/* 196 */       fnaBrowserTreeNodeBean.setId(str5);
/* 197 */       fnaBrowserTreeNodeBean.setName(str7);
/* 198 */       fnaBrowserTreeNodeBean.setPid(str1);
/* 199 */       fnaBrowserTreeNodeBean.setParent(bool);
/* 200 */       fnaBrowserTreeNodeBean.setCanClick(!bool);
/* 201 */       fnaBrowserTreeNodeBean.setOrgWholePathspan(str6);
/* 202 */       arrayList.add(fnaBrowserTreeNodeBean);
/*     */     } 
/* 204 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaApprovalTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */