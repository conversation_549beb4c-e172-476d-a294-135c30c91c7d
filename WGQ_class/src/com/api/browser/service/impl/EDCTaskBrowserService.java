/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.edc.util.EDCUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EDCTaskBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  34 */     String str1 = Util.null2String(paramMap.get("name")).trim();
/*  35 */     String str2 = Util.null2String(paramMap.get("appid"));
/*  36 */     String str3 = " where 1 = 1 ";
/*  37 */     if (!str1.equals("")) {
/*  38 */       str3 = str3 + " and t1.name like '%" + str1 + "%'";
/*     */     }
/*  40 */     if (!EDCUtil.isEmpty(str2))
/*  41 */       str3 = str3 + " and t1.appid = " + str2; 
/*  42 */     String str4 = " t1.id,t1.name";
/*  43 */     String str5 = " edc_task t1 ";
/*  44 */     String str6 = " t1.id ";
/*     */     
/*  46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  47 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */ 
/*     */ 
/*     */     
/*  51 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(502853, this.user.getLanguage()), "name", "name", 1));
/*     */     
/*  53 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "t1.id", arrayList);
/*  54 */     splitTableBean.setSqlsortway("ASC");
/*  55 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  56 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getLabelName(String paramString1, String paramString2) {
/*  60 */     if ("1".equals(paramString2)) {
/*  61 */       return "<span style='position:relative;'>" + paramString1 + "<span class='cube-virtual-form-flag' >V</span></span>";
/*     */     }
/*  63 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  69 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  70 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  74 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 502853, "name", true);
/*  75 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/*  77 */     arrayList1.add(new SearchConditionOption("1", "" + SystemEnv.getHtmlLabelName(1352, ThreadVarLanguage.getLang()) + ""));
/*  78 */     searchConditionItem.setOptions(arrayList1);
/*  79 */     arrayList.add(searchConditionItem);
/*     */     
/*  81 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  82 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  88 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  89 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  90 */     String str2 = paramHttpServletRequest.getParameter("appid");
/*  91 */     String str3 = "";
/*  92 */     if (EDCUtil.isNotEmpty(str2)) {
/*  93 */       str3 = "and t1.appid = " + str2;
/*     */     }
/*  95 */     String str4 = "SELECT t1.id ,t1.name FROM edc_task t1   where t1.name like '%" + str1 + "%' " + str3 + "order by t1.id";
/*     */     
/*  97 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  98 */     RecordSet recordSet = new RecordSet();
/*  99 */     recordSet.executeQuery(str4, new Object[0]);
/* 100 */     while (recordSet.next()) {
/* 101 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 102 */       hashMap1.put("id", recordSet.getString("id"));
/* 103 */       hashMap1.put("name", recordSet.getString("name"));
/* 104 */       arrayList.add(hashMap1);
/*     */     } 
/* 106 */     hashMap.put("datas", arrayList);
/*     */     
/* 108 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 109 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EDCTaskBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */