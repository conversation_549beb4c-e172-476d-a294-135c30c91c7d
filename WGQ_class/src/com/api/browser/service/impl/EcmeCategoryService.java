/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.ecme.util.EcmeTransMethod;
/*    */ import com.api.formmode.page.util.Util;
/*    */ import java.net.URLDecoder;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.ConnectionPool;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class EcmeCategoryService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 29 */     String str1 = (String)paramMap.get("feaid");
/* 30 */     String str2 = Util.null2String(paramMap.get("name"));
/* 31 */     RecordSet recordSet = new RecordSet();
/* 32 */     EcmeTransMethod ecmeTransMethod = new EcmeTransMethod();
/* 33 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 34 */     String str3 = "";
/* 35 */     if (!"".equals(str2)) {
/* 36 */       str3 = str3 + " and  name like '%" + str2 + "%'";
/*    */     }
/* 38 */     recordSet.executeQuery("select * from ecme_categoryinfo where modeid=(select modeid from ecme_feainfo where id=?) " + str3, new Object[] { str1 });
/* 39 */     while (recordSet.next()) {
/* 40 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 41 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 42 */       String str = Util.null2String(recordSet.getString("name"));
/* 43 */       str = ecmeTransMethod.convertLanguage(str, this.user.getLanguage() + "");
/* 44 */       hashMap1.put("name", str);
/* 45 */       arrayList.add(hashMap1);
/*    */     } 
/* 47 */     hashMap.put("datas", arrayList);
/*    */     
/* 49 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 50 */     arrayList1.add((new ListHeadBean("id", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()) + "ID")).setIsPrimarykey(BoolAttr.TRUE));
/* 51 */     arrayList1.add((new ListHeadBean("name", SystemEnv.getHtmlLabelName(15795, this.user.getLanguage()))).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 53 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 54 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 55 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 56 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 61 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 62 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 63 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15795, "name", true));
/* 64 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 65 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 70 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 71 */     RecordSet recordSet = new RecordSet();
/* 72 */     EcmeTransMethod ecmeTransMethod = new EcmeTransMethod();
/* 73 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("id"));
/* 74 */     String str2 = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("q")), "UTF-8").trim();
/* 75 */     String str3 = " ecme_categoryinfo t";
/* 76 */     String str4 = " where t.modeid= (select modeid from ecme_feainfo where id=" + str1 + ")";
/* 77 */     if (!"".equals(str2)) {
/* 78 */       str4 = str4 + " and (t.name like '%" + str2 + "%' ";
/* 79 */       if (!ConnectionPool.getInstance().isNewDB())
/*    */       {
/* 81 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 82 */           str4 = str4 + " or f_GetPy(t.name) like '%" + str2.toUpperCase() + "%'";
/* 83 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 84 */           str4 = str4 + " or [dbo].f_GetPy(t.name) like '%" + str2.toUpperCase() + "%'";
/* 85 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 86 */           str4 = str4 + " or getpinyin(t.name) like '%" + str2.toUpperCase() + "%'";
/*    */         }  } 
/* 88 */       str4 = str4 + ")";
/*    */     } 
/* 90 */     recordSet.executeQuery("select t.id,t.name from " + str3 + str4 + " order by t.id", new Object[0]);
/* 91 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 92 */     while (recordSet.next()) {
/* 93 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 94 */       hashMap1.put("id", recordSet.getString("id"));
/* 95 */       hashMap1.put("name", ecmeTransMethod.convertLanguage(recordSet.getString("name"), this.user.getLanguage() + ""));
/* 96 */       arrayList.add(hashMap1);
/*    */     } 
/* 98 */     hashMap.put("datas", arrayList);
/* 99 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EcmeCategoryService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */