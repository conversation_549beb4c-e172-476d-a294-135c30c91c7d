/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class HrmCareerPlanBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 24 */     String str1 = Util.null2String(paramMap.get("name"));
/*    */     
/* 26 */     String str2 = " id,topic as name,principalid,informmanid,startdate,advice ";
/* 27 */     String str3 = " from HrmCareerPlan ";
/* 28 */     String str4 = " where 1=1 ";
/*    */     
/* 30 */     if (!str1.equals("")) {
/* 31 */       str4 = str4 + " and topic like '%" + str1 + "%' ";
/*    */     }
/* 33 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 34 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 35 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(344, this.user.getLanguage()), "name", null)).setIsInputCol(BoolAttr.TRUE));
/* 36 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(2097, this.user.getLanguage()), "principalid", null, "weaver.hrm.resource.ResourceComInfo.getResourcename"));
/* 37 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15669, this.user.getLanguage()), "informmanid", null, "weaver.hrm.resource.ResourceComInfo.getResourcename"));
/* 38 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15668, this.user.getLanguage()), "startdate", null));
/*    */     
/* 40 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, Util.toHtmlForSplitPage(str4), "id", "id", arrayList);
/* 41 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 42 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 46 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 47 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 48 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 49 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 344, "name", true));
/* 50 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 51 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmCareerPlanBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */