/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.DBUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.SplitPageParaBean;
/*     */ import weaver.general.SplitPageUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EmailLabelBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  42 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*  43 */     int i = 0;
/*     */     
/*     */     try {
/*  46 */       String str1 = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("q")), "UTF-8");
/*  47 */       String str2 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageSize")), "10");
/*  48 */       String str3 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("pageNum")), "1");
/*     */       
/*  50 */       if (!str1.isEmpty()) {
/*  51 */         RecordSet recordSet = new RecordSet();
/*     */         
/*  53 */         String str4 = "select * from email_label";
/*     */         
/*  55 */         String str5 = " name like '%" + str1 + "%' and accountid = " + this.user.getUID();
/*     */         
/*  57 */         SplitPageParaBean splitPageParaBean = new SplitPageParaBean();
/*  58 */         SplitPageUtil splitPageUtil = new SplitPageUtil();
/*  59 */         splitPageParaBean.setSqlFrom("(" + str4.toString() + ") a");
/*  60 */         splitPageParaBean.setBackFields("id,name");
/*  61 */         splitPageParaBean.setPrimaryKey("id");
/*  62 */         splitPageParaBean.setSqlOrderBy("id");
/*  63 */         splitPageParaBean.getClass(); splitPageParaBean.setSortWay(1);
/*  64 */         splitPageParaBean.setSqlWhere(str5);
/*  65 */         splitPageUtil.setSpp(splitPageParaBean);
/*  66 */         recordSet = splitPageUtil.getCurrentPageRs(Integer.valueOf(str3).intValue(), Integer.valueOf(str2).intValue());
/*  67 */         i = splitPageUtil.getRecordCount();
/*     */         
/*  69 */         HashSet<String> hashSet = new HashSet();
/*  70 */         while (recordSet.next()) {
/*  71 */           String str = Util.null2String(recordSet.getString("id"));
/*  72 */           if (StringUtils.isNotEmpty(str) && !hashSet.contains(str)) {
/*  73 */             hashSet.add(str);
/*  74 */             arrayList.add(getSearchObj(str, recordSet.getString("name")));
/*     */           } 
/*     */         } 
/*     */       } 
/*  78 */     } catch (Exception exception) {
/*  79 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  82 */     hashMap.put("count", Integer.valueOf(i));
/*  83 */     hashMap.put("datas", arrayList);
/*     */     
/*  85 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private JSONObject getSearchObj(String paramString1, String paramString2) {
/*  89 */     JSONObject jSONObject = new JSONObject();
/*  90 */     jSONObject.put("id", paramString1);
/*  91 */     jSONObject.put("name", paramString2);
/*  92 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 105 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 106 */     RecordSet recordSet = new RecordSet();
/* 107 */     String str1 = " 1=1 and accountid = " + this.user.getUID();
/*     */     
/* 109 */     String str2 = Util.null2String(paramMap.get("name"));
/* 110 */     if (!str2.equals("")) {
/* 111 */       str1 = str1 + " and name like '%" + str2 + "%'";
/*     */     }
/*     */     
/* 114 */     String str3 = "  id ,  name";
/* 115 */     String str4 = "select * from email_label";
/* 116 */     String str5 = "(" + str4.toString() + ") t";
/*     */     
/* 118 */     String str6 = "id";
/*     */     
/* 120 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 121 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 122 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(526358, this.user.getLanguage()), "name", "name"));
/*     */     
/* 124 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str5, str1, str6, "id", arrayList);
/* 125 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 126 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 131 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 132 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 133 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 134 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 526358, "name", true));
/* 135 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 136 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 142 */     String str = Util.null2String(paramMap.get("selectids"));
/*     */     
/* 144 */     if (!"".equals(str)) {
/* 145 */       RecordSet recordSet = new RecordSet();
/* 146 */       ArrayList arrayList = new ArrayList();
/* 147 */       recordSet.executeQuery("select  id , name from email_label where accountid = " + this.user.getUID() + " and id in (" + DBUtil.getParamReplace(str) + ") order by id asc", new Object[] { DBUtil.trasToList(arrayList, new Object[] { str }) });
/* 148 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 149 */       while (recordSet.next()) {
/* 150 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 151 */         hashMap1.put("id", recordSet.getString("id"));
/* 152 */         hashMap1.put("idspan", hashMap1.get("id"));
/* 153 */         hashMap1.put("name", recordSet.getString("name"));
/* 154 */         hashMap1.put("namespan", hashMap1.get("name"));
/* 155 */         hashMap1.put("randomFieldId", recordSet.getString("id"));
/* 156 */         hashMap1.put("randomFieldIdspan", "");
/* 157 */         arrayList1.add(hashMap1);
/*     */       } 
/* 159 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 160 */       hashMap.put("datas", arrayList1);
/* 161 */       return (Map)hashMap;
/*     */     } 
/* 163 */     return new HashMap<>();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EmailLabelBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */