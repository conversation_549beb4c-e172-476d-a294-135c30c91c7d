/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.fnaMulDimensions.util.BudgetApprovalUtil;
/*     */ import com.engine.fnaMulDimensions.util.DimensionUtil;
/*     */ import com.engine.fnaMulDimensions.util.FnaWorkbookTemplateCheckUtil;
/*     */ import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaMultiDimTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  44 */     if (this.user == null) {
/*  45 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  46 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  49 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  50 */     SplitTableBean splitTableBean = getTableList(paramMap);
/*  51 */     if (splitTableBean == null) {
/*  52 */       ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  53 */       arrayList.add(new ListHeadBean("approvalName", SystemEnv.getHtmlLabelName(504430, this.user.getLanguage())));
/*     */ 
/*     */       
/*  56 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  57 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */     } else {
/*  59 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } 
/*     */     
/*  62 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/*  73 */     String str1 = Util.null2String(paramMap.get("typeName"));
/*     */     
/*  75 */     BudgetApprovalUtil budgetApprovalUtil = new BudgetApprovalUtil();
/*  76 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  78 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  79 */     recordSet.executeQuery(" select * from FnaDimensionType ", new Object[0]);
/*  80 */     while (recordSet.next()) {
/*  81 */       String str9 = Util.null2String(recordSet.getString("id"));
/*  82 */       String str10 = Util.null2String(recordSet.getString("typeName"));
/*  83 */       hashMap.put(str9, str10);
/*     */     } 
/*     */     
/*  86 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  88 */     recordSet.executeQuery("select * from FnaAccountInfo where accountNumber>=0 and isArchive != 1 ", new Object[0]);
/*  89 */     while (recordSet.next()) {
/*  90 */       String str = Util.null2String(recordSet.getString("id"));
/*  91 */       arrayList.add(str);
/*     */     } 
/*     */     
/*  94 */     FnaWorkbookTemplateCheckUtil fnaWorkbookTemplateCheckUtil = new FnaWorkbookTemplateCheckUtil();
/*  95 */     HashSet<String> hashSet = new HashSet();
/*  96 */     for (String str9 : arrayList) {
/*  97 */       String str10 = budgetApprovalUtil.getTableName(str9, FnaAccTypeConstant.BUDGET_APPROVAL.intValue());
/*  98 */       String str11 = budgetApprovalUtil.getTableName(str9, FnaAccTypeConstant.BUDGET_APPROVAL_TYPE.intValue());
/*  99 */       String str12 = budgetApprovalUtil.getTableName(str9, FnaAccTypeConstant.BUDGET_TEMPLATE.intValue());
/* 100 */       if ("".equals(str10) || "".equals(str12)) {
/*     */         continue;
/*     */       }
/*     */       
/* 104 */       recordSet.executeQuery("select a.id from " + str12 + " a  join " + str10 + " b on b.id = a.approvalId  join " + str11 + " c on c.id = b.approvalTypeId  where a.accountId = ?  and b.nodeStatus = 1 and c.approvaTypelstatus = 1", new Object[] { str9 });
/*     */ 
/*     */ 
/*     */       
/* 108 */       ArrayList<String> arrayList2 = new ArrayList();
/* 109 */       while (recordSet.next()) {
/* 110 */         String str = Util.null2String(recordSet.getString("id"));
/* 111 */         arrayList2.add(str);
/*     */       } 
/*     */       
/* 114 */       for (String str13 : arrayList2) {
/* 115 */         String str14 = fnaWorkbookTemplateCheckUtil.getTypesIdByDataJson(str13, str9);
/*     */         
/* 117 */         String str15 = DimensionUtil.rankTypes(str14);
/* 118 */         hashSet.add(str15);
/*     */       } 
/*     */     } 
/* 121 */     StringBuffer stringBuffer = new StringBuffer();
/* 122 */     for (String str : hashSet) {
/* 123 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 124 */       for (String str9 : str.split(",")) {
/* 125 */         String str10 = (String)hashMap.get(str9);
/* 126 */         if (stringBuffer1.length() > 0) {
/* 127 */           stringBuffer1.append(",");
/*     */         }
/* 129 */         stringBuffer1.append(str10);
/*     */       } 
/* 131 */       if (stringBuffer.length() > 0) {
/* 132 */         stringBuffer.append(" union all ");
/*     */       }
/* 134 */       stringBuffer.append("select '").append(str).append("'").append(" id, ").append(" '").append(stringBuffer1).append("' typeName ");
/* 135 */       if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "dm".equalsIgnoreCase(recordSet.getOrgindbtype()) || "st".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/* 136 */         stringBuffer.append(" from dual ");
/*     */       }
/*     */     } 
/*     */     
/* 140 */     String str2 = "(" + stringBuffer.toString() + ")";
/* 141 */     if ("mysql".equalsIgnoreCase(recordSet.getDBType()) || "sqlserver".equalsIgnoreCase(recordSet.getDBType()) || "postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 142 */       str2 = str2 + " as a1";
/*     */     }
/* 144 */     if (str2.equals("()") || str2.trim().equals("() as a1")) {
/* 145 */       return null;
/*     */     }
/*     */     
/* 148 */     String str3 = "10";
/* 149 */     String str4 = " id,typeName  ";
/* 150 */     String str5 = " from " + str2;
/* 151 */     String str6 = " where 1=1 ";
/*     */     
/* 153 */     if (!"".equals(str1)) {
/* 154 */       str6 = str6 + " and typeName like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/*     */     
/* 157 */     String str7 = "id";
/* 158 */     String str8 = "id";
/*     */     
/* 160 */     ArrayList<SplitTableColBean> arrayList1 = new ArrayList();
/* 161 */     arrayList1.add(new SplitTableColBean("true", "id"));
/* 162 */     arrayList1.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(504430, this.user.getLanguage()), "typeName", "typeName")).setIsInputCol(BoolAttr.TRUE));
/*     */     
/* 164 */     return new SplitTableBean("FnaMulDimTypeBrowserList", "none", str3, "FnaMulDimTypeBrowserList", str4, str5, str6, str7, str8, "ASC", arrayList1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaMultiDimTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */