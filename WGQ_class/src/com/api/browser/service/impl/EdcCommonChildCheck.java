/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EdcCommonChildCheck
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 21 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 23 */     String str1 = Util.null2String(paramMap.get("publicCheckIds"));
/*    */     
/* 25 */     String str2 = " mode_selectitempage t ";
/* 26 */     String str3 = " t.id  in (" + str1 + ") and EXISTS (SELECT 1 FROM mode_selectitempagedetail dt WHERE t.id=dt.mainid and dt.pid!=0)";
/*    */     
/* 28 */     String str4 = " * ";
/*    */     
/* 30 */     String str5 = " id ";
/* 31 */     String str6 = "desc";
/* 32 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 33 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 34 */     arrayList.add((new SplitTableColBean("80%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "selectitemname", "name", "", 1)).setIsInputCol(BoolAttr.TRUE));
/* 35 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str2, str3, str5, "id", str6, arrayList);
/* 36 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 37 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EdcCommonChildCheck.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */