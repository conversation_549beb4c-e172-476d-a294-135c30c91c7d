/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EDCAnalysisGroupBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  34 */     String str1 = Util.null2String(paramMap.get("name")).trim();
/*  35 */     String str2 = "where t1.id not in (select sourceid from edc_appsresource where sourcetype = 'analysis') ";
/*  36 */     String str3 = "";
/*  37 */     String str4 = "";
/*  38 */     String str5 = "";
/*     */     
/*  40 */     RecordSet recordSet = new RecordSet();
/*  41 */     String str6 = recordSet.getDBType();
/*  42 */     if (!str1.equals(""))
/*  43 */       str2 = str2 + " and t1.name like '%" + str1 + "%' "; 
/*  44 */     str3 = " t1.id ,t1.name ";
/*     */     
/*  46 */     str4 = " ( select uuid id, name from edc_reportDsGroup  union select 'default' id, '默认分组' name " + ("sqlserver".equals(str6) ? "" : "from dual") + " ) t1 ";
/*  47 */     str5 = " t1.name ";
/*     */ 
/*     */ 
/*     */     
/*  51 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  52 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */ 
/*     */ 
/*     */     
/*  56 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(514583, this.user.getLanguage()), "name", "name", "com.api.browser.service.impl.EDCAnalysisGroupBrowserService.getGroupName", "", 1));
/*     */ 
/*     */     
/*  59 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "t1.name", arrayList);
/*     */     
/*  61 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  62 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getGroupName(String paramString1, String paramString2) {
/*  67 */     return Util.formatMultiLang(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  73 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  74 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  78 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 514583, "name", true);
/*  79 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/*  81 */     arrayList1.add(new SearchConditionOption("1", "" + SystemEnv.getHtmlLabelName(514582, ThreadVarLanguage.getLang()) + ""));
/*  82 */     searchConditionItem.setOptions(arrayList1);
/*  83 */     arrayList.add(searchConditionItem);
/*     */     
/*  85 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  86 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  93 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  94 */     String str2 = "";
/*  95 */     String str3 = (new RecordSet()).getDBType();
/*     */     
/*  97 */     String str4 = "( select uuid id, name + '(报表)' name from edc_reportDsGroup) t1 union select 'default' id, '默认分组' name " + ("sqlserver".equals(str3) ? "" : "from dual") + "  where t1.name like '%" + str1 + "%' " + str2 + "order by t1.name";
/*     */     
/*  99 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 100 */     RecordSet recordSet = new RecordSet();
/* 101 */     recordSet.executeQuery(str4, new Object[0]);
/* 102 */     while (recordSet.next()) {
/* 103 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 104 */       hashMap1.put("id", recordSet.getString("id"));
/* 105 */       hashMap1.put("name", recordSet.getString("name"));
/* 106 */       arrayList.add(hashMap1);
/*     */     } 
/* 108 */     hashMap.put("datas", arrayList);
/*     */     
/* 110 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 111 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EDCAnalysisGroupBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */