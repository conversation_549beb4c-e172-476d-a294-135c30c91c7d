/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.MobileShowTypeAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomerValueBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"levelvalue\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*    */   
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 52 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 54 */     String str1 = Util.null2String(paramMap.get("name"));
/*    */     
/* 56 */     String str2 = "id,name,levelvalue";
/* 57 */     String str3 = "CRM_Evaluation_Level";
/* 58 */     String str4 = " ";
/* 59 */     if (!"".equals(str1)) {
/* 60 */       str4 = str4 + " name like '%" + str1 + "%'";
/*    */     }
/* 62 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 63 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/* 64 */     arrayList.add(new SplitTableColBean("55%", SystemEnv.getHtmlLabelName(84276, this.user.getLanguage()), "name", "name"));
/* 65 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(6072, this.user.getLanguage()), "levevalue", "levevalue"));
/* 66 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, "orderkey", "id", "asc", arrayList);
/*    */     
/*    */     try {
/* 69 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 70 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"name\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"levelvalue\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 71 */     } catch (Exception exception) {
/* 72 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 75 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 76 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 81 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 82 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 83 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 84 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "name", true));
/*    */     
/* 86 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 87 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CustomerValueBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */