/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.BrowserTreeNode;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LgcAssortmentBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 24 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 25 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 26 */     if (this.user == null) {
/* 27 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 28 */       return (Map)hashMap;
/*    */     } 
/* 30 */     String str = Util.null2String(paramMap.get("id"));
/* 31 */     List<BrowserTreeNode> list = getTreeNodeInfo(str);
/* 32 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/* 33 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public List<BrowserTreeNode> getTreeNodeInfo(String paramString) {
/* 41 */     paramString = "".equals(paramString) ? "0" : paramString;
/* 42 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/* 43 */     RecordSet recordSet = new RecordSet();
/* 44 */     recordSet.executeSql(" select * from LgcAssetAssortment where supassortmentid =" + paramString);
/* 45 */     while (recordSet.next()) {
/* 46 */       String str1 = recordSet.getString("id");
/* 47 */       String str2 = recordSet.getString("assortmentname");
/* 48 */       String str3 = recordSet.getString("supassortmentid");
/* 49 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode(str1, str2, str3, hasChild(str1), true);
/* 50 */       arrayList.add(browserTreeNode);
/*    */     } 
/*    */     
/* 53 */     return arrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private boolean hasChild(String paramString) {
/* 61 */     RecordSet recordSet = new RecordSet();
/* 62 */     recordSet.executeSql(" select count(0) c from LgcAssetAssortment where supassortmentid=" + paramString);
/* 63 */     return (recordSet.next() && recordSet.getInt("c") > 0);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/LgcAssortmentBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */