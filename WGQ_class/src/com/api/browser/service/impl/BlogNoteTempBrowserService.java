/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.blog.BlogDao;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BlogNoteTempBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 31 */     String str1 = "";
/* 32 */     String str2 = Util.null2String(paramMap.get("tempName"));
/* 33 */     if (!str2.equals("")) {
/* 34 */       str1 = str1 + " and t1.tempName like '%" + str2 + "%'";
/*    */     }
/* 36 */     String str3 = Util.null2String(paramMap.get("tempDesc"));
/* 37 */     if (!str3.equals("")) {
/* 38 */       str1 = str1 + " and t1.tempDesc like '%" + str3 + "%'";
/*    */     }
/* 40 */     String str4 = Util.null2String(paramMap.get("isEcme"));
/*    */     
/* 42 */     if (!str4.equals("")) {
/* 43 */       str1 = str1 + " and t1.isEcme = " + str4;
/*    */     }
/*    */ 
/*    */     
/* 47 */     BlogDao blogDao = new BlogDao();
/*    */     
/* 49 */     String str5 = "id , tempName ,tempDesc,isecme as contentType,isecme, isUsed ,userId,tempContent as name,isSystem,case isSystem when '1' then '" + SystemEnv.getHtmlLabelName(83158, this.user.getLanguage()) + "' else '" + SystemEnv.getHtmlLabelName(83159, this.user.getLanguage()) + "' END isSystem_str , case isecme when '1' then '" + SystemEnv.getHtmlLabelName(10004829, this.user.getLanguage()) + "' else '" + SystemEnv.getHtmlLabelName(386758, this.user.getLanguage()) + "' end isEcme_str";
/* 50 */     String str6 = "from blog_template t1 left join " + blogDao.getTemplateTable(this.user.getUID() + "") + "t2 on t1.id = t2.tempid";
/* 51 */     str1 = "((t1.id = t2.tempid and isUsed = 1) or (isSystem = 0 and userId = '" + this.user.getUID() + "'))" + str1;
/*    */ 
/*    */     
/* 54 */     str1 = str1 + " and isecme = 0";
/*    */ 
/*    */     
/* 57 */     String str7 = "isSystem , id";
/*    */     
/* 59 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 60 */     arrayList.add(new SplitTableColBean("true", "id"));
/*    */ 
/*    */     
/* 63 */     arrayList.add(new SplitTableColBean("true", "name"));
/* 64 */     arrayList.add(new SplitTableColBean("true", "contentType"));
/* 65 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "tempName", "tempName"));
/* 66 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18627, this.user.getLanguage()), "tempDesc", "tempDesc"));
/* 67 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(20622, this.user.getLanguage()), "isSystem_str", "isSystem_str"));
/*    */     
/* 69 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str5, str6, str1, str7, "id", arrayList)));
/* 70 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 76 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 77 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 78 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18151, "tempName", true));
/* 79 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18627, "tempDesc"));
/* 80 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 81 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BlogNoteTempBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */