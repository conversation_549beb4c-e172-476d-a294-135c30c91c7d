/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.workflow.biz.newRule.RuleSetBiz;
/*     */ import com.engine.workflow.util.NewRuleListUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowNewRuleBrowserImpl
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  34 */     String str1 = Util.null2String(paramMap.get("rulename"));
/*  35 */     String str2 = Util.null2String(paramMap.get("rulePassedTip"));
/*  36 */     String str3 = Util.null2String(paramMap.get("ruleNoPassTip"));
/*  37 */     String str4 = Util.null2String(paramMap.get("noPassProcessType"));
/*     */ 
/*     */     
/*  40 */     String str5 = " source = 0 ";
/*  41 */     if (!str1.equals("")) str5 = str5 + " and rulename like '%" + str1 + "%'";
/*     */     
/*  43 */     if (!"".equals(str2)) {
/*  44 */       str5 = str5 + " and rulePassedTip like '%" + str2 + "%' ";
/*     */     }
/*     */     
/*  47 */     if (!"".equals(str3)) {
/*  48 */       str5 = str5 + " and ruleNoPassTip like '%" + str3 + "%' ";
/*     */     }
/*     */     
/*  51 */     if (!"-1".equals(str4) && !"".equals(str4)) {
/*  52 */       str5 = str5 + " and noPassProcessType = '" + str4 + "' ";
/*     */     }
/*     */     
/*  55 */     String str6 = " ruleid,rulename,condit,rulePassedTip,ruleNoPassTip,noPassProcessType ";
/*  56 */     String str7 = " newrule_base ";
/*  57 */     String str8 = " ruleid ";
/*     */     
/*  59 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  60 */     arrayList.add(new SplitTableColBean("true", "ruleid"));
/*  61 */     arrayList.add((new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(19829, this.user.getLanguage()), "rulename", null, 1)).setIsInputCol(BoolAttr.TRUE));
/*  62 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15364, this.user.getLanguage()), "condit", null, RuleSetBiz.class.getName() + ".getRuleTip", "column:ruleid+" + this.user.getUID(), 0));
/*  63 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(529731, this.user.getLanguage()), "rulePassedTip", null, RuleSetBiz.class.getName() + ".getRuleTip", "column:ruleid+" + this.user.getUID(), 0));
/*  64 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(525500, this.user.getLanguage()), "ruleNoPassTip", null, RuleSetBiz.class.getName() + ".getRuleTip", "column:ruleid+" + this.user.getUID(), 0));
/*  65 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(525501, this.user.getLanguage()), "noPassProcessType", null, NewRuleListUtil.class.getName() + ".getProcessType", this.user.getLanguage() + ""));
/*     */     
/*  67 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str5, str8, "ruleid", arrayList);
/*  68 */     splitTableBean.setSqlsortway("ASC");
/*  69 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  70 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  76 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  77 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  78 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  79 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 19829, "rulename", true);
/*  80 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 529731, "rulePassedTip");
/*  81 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.INPUT, 525500, "ruleNoPassTip");
/*  82 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.SELECT, 525501, "noPassProcessType");
/*  83 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  84 */     arrayList1.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/*  85 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(32853, this.user.getLanguage()), false));
/*  86 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(32852, this.user.getLanguage()), false));
/*  87 */     searchConditionItem4.setOptions(arrayList1);
/*     */     
/*  89 */     arrayList.add(searchConditionItem1);
/*  90 */     arrayList.add(searchConditionItem2);
/*  91 */     arrayList.add(searchConditionItem3);
/*  92 */     arrayList.add(searchConditionItem4);
/*     */     
/*  94 */     for (SearchConditionItem searchConditionItem : arrayList) {
/*  95 */       searchConditionItem.setLabelcol(10);
/*  96 */       searchConditionItem.setFieldcol(14);
/*     */     } 
/*     */     
/*  99 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 104 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 105 */     RecordSet recordSet = new RecordSet();
/* 106 */     String str2 = "where 1=1 ";
/* 107 */     if (!"".equals(str1)) {
/* 108 */       str2 = str2 + " and (rulename like '%" + str1 + "%' ";
/* 109 */       if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 110 */         str2 = str2 + " or f_GetPy(rulename) like '%" + str1.toUpperCase() + "%'";
/* 111 */       } else if ("sqlserver".equals(recordSet.getDBType())) {
/* 112 */         str2 = str2 + " or [dbo].f_GetPy(rulename) like '%" + str1.toUpperCase() + "%'";
/*     */       }
/* 114 */       else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 115 */         str2 = str2 + " or getpinyin(rulename) like '%" + str1.toUpperCase() + "%'";
/*     */       } 
/* 117 */       str2 = str2 + ")";
/*     */     } 
/* 119 */     recordSet.executeQuery("select ruleid,rulename,condit from newrule_base " + str2 + " order by ruleid", new Object[0]);
/* 120 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 121 */     while (recordSet.next()) {
/* 122 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 123 */       hashMap1.put("id", recordSet.getString("ruleid"));
/* 124 */       hashMap1.put("name", recordSet.getString("rulename"));
/* 125 */       arrayList.add(hashMap1);
/*     */     } 
/* 127 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 128 */     hashMap.put("datas", arrayList);
/* 129 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowNewRuleBrowserImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */