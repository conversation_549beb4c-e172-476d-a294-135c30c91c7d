/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.workflow.entity.browser.WfBrowserTreeNode;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkTypeComInfo;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ 
/*     */ public class WfNodeBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  29 */     WorkflowBrowserServiceAssist workflowBrowserServiceAssist = new WorkflowBrowserServiceAssist(paramMap, this.user);
/*     */     
/*  31 */     String str1 = Util.null2String(paramMap.get("showtype"));
/*  32 */     String str2 = Util.null2String(paramMap.get("isflowDoc"));
/*  33 */     if ("listview".equals(str1)) {
/*     */       
/*  35 */       String str3 = " workflowid,activeid,workflowname,typeid,typename,id,nodename,istemplate,dsporder1,dsporder,subcompanyid ";
/*  36 */       String str4 = " a.id as workflowid,(case a.isvalid when '3' then a.activeversionid else a.id end) as activeid , a.workflowname, b.id as typeid , b.typename, wn.id, wn.nodename, (case when a.istemplate is null then '0' when a.istemplate = '' then '0' else a.istemplate end) as istemplate,b.dsporder dsporder1,( case when a.dsporder is null then 0 else a.dsporder end) as dsporder,a.subcompanyid";
/*  37 */       String str5 = " from workflow_base a left join workflow_type b on a.workflowtype = b.id left join workflow_flownode wf on a.id = wf.workflowid left join workflow_nodebase wn on wf.nodeid = wn.id ";
/*     */       
/*  39 */       String str6 = handledBrowserSql(paramMap);
/*  40 */       String str7 = " dsporder1 asc,typeid asc, istemplate asc,dsporder asc, workflowname asc,a.id desc ,wf.nodeorder asc, wf.nodetype asc, wf.nodeid asc";
/*     */ 
/*     */       
/*  43 */       String str8 = workflowBrowserServiceAssist.getOthetSqlWhere();
/*     */       
/*  45 */       String str9 = Util.null2String(paramMap.get("showHistory"));
/*  46 */       if (!"".equals(str8)) {
/*  47 */         if (!"2".equals(str9)) {
/*  48 */           str5 = " from (select " + str4 + " " + str5 + " " + str6 + ") table_a ";
/*  49 */           str6 = " where exists(select 1 from workflow_base where id = table_a.activeid and " + str8 + ") ";
/*  50 */           str4 = str3;
/*     */         } else {
/*  52 */           str6 = str6 + " and " + str8;
/*     */         } 
/*     */       }
/*  55 */       ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  56 */       SplitTableColBean splitTableColBean1 = new SplitTableColBean("true", "id");
/*  57 */       splitTableColBean1.setIsPrimarykey(BoolAttr.TRUE);
/*  58 */       arrayList.add(splitTableColBean1);
/*     */       
/*  60 */       SplitTableColBean splitTableColBean2 = (new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()), "nodename", null, 1)).setIsInputCol(BoolAttr.TRUE);
/*  61 */       splitTableColBean2.setTransmethod(getClass().getName() + ".nodenameTrans");
/*  62 */       splitTableColBean2.setOtherpara(this.user.getLanguage() + "");
/*  63 */       SplitTableColBean splitTableColBean3 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), "workflowname", null, 0);
/*  64 */       splitTableColBean3.setTransmethod(getClass().getName() + ".workflownameTrans");
/*  65 */       splitTableColBean3.setOtherpara(this.user.getLanguage() + "");
/*  66 */       SplitTableColBean splitTableColBean4 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15795, this.user.getLanguage()), "typename", null, 0);
/*  67 */       splitTableColBean4.setTransmethod(getClass().getName() + ".typenameTrans");
/*  68 */       splitTableColBean4.setOtherpara(this.user.getLanguage() + "");
/*  69 */       arrayList.add(splitTableColBean2);
/*  70 */       arrayList.add(splitTableColBean3);
/*  71 */       arrayList.add(splitTableColBean4);
/*  72 */       SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str6, str7, "nodeid", arrayList);
/*  73 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  74 */     } else if ("typeview".equals(str1)) {
/*  75 */       String str3 = Util.null2String(paramMap.get("type"));
/*  76 */       String str4 = Util.null2String(paramMap.get("isLoadAll"));
/*  77 */       boolean bool = "1".equals(Util.null2String(paramMap.get("canClickWfType")));
/*  78 */       List<WfBrowserTreeNode> list = new ArrayList();
/*  79 */       String str5 = Util.null2String(paramMap.get("id"));
/*  80 */       String str6 = handledBrowserSql(paramMap);
/*  81 */       if ("1".equals(str2)) {
/*  82 */         str6 = str6 + " and a.isworkflowdoc = 1 and a.officaltype > 0  ";
/*     */       }
/*  84 */       if ("".equals(str3)) {
/*  85 */         List<WfBrowserTreeNode> list1 = workflowBrowserServiceAssist.getWfTypes(str6, str4, bool, false, true);
/*  86 */       } else if ("wftype".equals(str3)) {
/*  87 */         List<WfBrowserTreeNode> list1 = workflowBrowserServiceAssist.getWorkflows(str6, str4, str5, true);
/*  88 */       } else if ("wf".equals(str3)) {
/*  89 */         list = workflowBrowserServiceAssist.getWorkflowNodes(str4, str5, str6);
/*     */       } 
/*  91 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*  92 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  93 */       hashMap.put(BrowserConstant.BROWSER_RESULT_IS_ALL, Boolean.valueOf(false));
/*     */     } 
/*     */     
/*  96 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 105 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 106 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 107 */     if ("".equals(str1)) return (Map)hashMap; 
/* 108 */     String str2 = Util.null2String(paramMap.get("types"));
/* 109 */     if (!"".equals(str2) && (str2.contains("wftype") || str2.contains("wf") || str2.contains("node"))) {
/* 110 */       str1 = handledDestIds(str2, paramMap);
/*     */     }
/*     */     
/* 113 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 114 */     WorkTypeComInfo workTypeComInfo = new WorkTypeComInfo();
/* 115 */     String str3 = String.valueOf(this.user.getLanguage());
/* 116 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 117 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 118 */     if (!"".equals(str1)) {
/* 119 */       String str = "select wn.id , wn.nodename, wf.workflowid  from workflow_nodebase wn join workflow_flownode wf on wn.id = wf.nodeid  and " + Util.getSubINClause(str1, "wn.id", "in");
/* 120 */       RecordSet recordSet = new RecordSet();
/* 121 */       recordSet.executeQuery(str, new Object[0]);
/* 122 */       while (recordSet.next()) {
/* 123 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 124 */         String str4 = recordSet.getString("id");
/* 125 */         String str5 = Util.formatMultiLang(recordSet.getString("nodename"), str3);
/* 126 */         String str6 = recordSet.getString("workflowid");
/* 127 */         if (!"1".equals(workflowAllComInfo.getIsValid(str6))) {
/*     */           continue;
/*     */         }
/*     */         
/* 131 */         String str7 = Util.formatMultiLang(workflowAllComInfo.getWorkflowname(str6), str3);
/* 132 */         String str8 = Util.formatMultiLang(workTypeComInfo.getWorkTypename(workflowAllComInfo.getWorkflowtype(str6)), str3);
/* 133 */         hashMap1.put("id", str4);
/* 134 */         hashMap1.put("nodename", str5);
/* 135 */         hashMap1.put("workflowname", str7);
/* 136 */         hashMap1.put("typename", str8);
/* 137 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/* 140 */     ListHeadBean listHeadBean = new ListHeadBean("id", BoolAttr.TRUE);
/* 141 */     listHeadBean.setIsPrimarykey(BoolAttr.TRUE);
/* 142 */     arrayList1.add(listHeadBean);
/* 143 */     arrayList1.add(new ListHeadBean("nodename", "", 1, BoolAttr.TRUE));
/* 144 */     arrayList1.add(new ListHeadBean("workflowname", "", 0));
/* 145 */     arrayList1.add(new ListHeadBean("typename", "", 0));
/* 146 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 147 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 148 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 149 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String handledDestIds(String paramString, Map<String, Object> paramMap) {
/* 158 */     String str1 = "";
/* 159 */     StringBuilder stringBuilder = new StringBuilder();
/* 160 */     String str2 = Util.null2String(paramMap.get("isTemplate"));
/* 161 */     String str3 = Util.null2String(paramMap.get("showHistory"));
/*     */     
/* 163 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfstatus")), 1);
/*     */     
/* 165 */     String str4 = Util.null2String(paramMap.get("nodename"));
/* 166 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodetype")), 0);
/*     */     try {
/* 168 */       str1 = handledBrowserSql(paramMap);
/* 169 */     } catch (Exception exception) {
/* 170 */       exception.printStackTrace();
/*     */     } 
/* 172 */     String[] arrayOfString = paramString.split(",");
/* 173 */     WorkflowBrowserServiceAssist workflowBrowserServiceAssist = new WorkflowBrowserServiceAssist(paramMap, this.user);
/* 174 */     for (String str : arrayOfString) {
/* 175 */       String[] arrayOfString1 = str.split("\\|");
/* 176 */       if ("wftype".equals(arrayOfString1[0])) {
/* 177 */         HashMap<String, List<String>> hashMap = workflowBrowserServiceAssist.getWfidAndType(str1 + " and b.id = '" + arrayOfString1[1] + "'", str2, str3, i, true);
/* 178 */         List list = hashMap.get("wfids");
/* 179 */         String str5 = "";
/* 180 */         for (String str6 : list) {
/* 181 */           str5 = str5 + "," + str6;
/*     */         }
/* 183 */         if (!"".equals(str5)) {
/* 184 */           String str6 = workflowBrowserServiceAssist.getNodeidsByWfid(str5.substring(1), str4, j);
/* 185 */           for (String str7 : Util.TokenizerString2(str6, ",")) {
/* 186 */             if (!("," + stringBuilder.toString() + ",").contains("," + str7 + ",")) {
/* 187 */               stringBuilder.append(",").append(str7);
/*     */             }
/*     */           } 
/*     */         } 
/* 191 */       } else if ("wf".equals(arrayOfString1[0])) {
/* 192 */         String str5 = workflowBrowserServiceAssist.getNodeidsByWfid(arrayOfString1[1], str4, j);
/* 193 */         for (String str6 : Util.TokenizerString2(str5, ",")) {
/* 194 */           if (!("," + stringBuilder.toString() + ",").contains("," + str6 + ",")) {
/* 195 */             stringBuilder.append(",").append(str6);
/*     */           }
/*     */         } 
/* 198 */       } else if ("node".equals(arrayOfString1[0]) && 
/* 199 */         !("," + stringBuilder.toString() + ",").contains("," + arrayOfString1[1] + ",")) {
/* 200 */         stringBuilder.append(",").append(arrayOfString1[1]);
/*     */       } 
/*     */     } 
/*     */     
/* 204 */     if (stringBuilder.length() > 0) {
/* 205 */       return stringBuilder.substring(1);
/*     */     }
/* 207 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 216 */     String str1 = getBrowserAutoCompleteSqlWhere(paramHttpServletRequest, paramHttpServletResponse);
/* 217 */     Map map = ParamUtil.request2Map(paramHttpServletRequest);
/* 218 */     String str2 = Util.null2String(map.get("isflowDoc"));
/* 219 */     if ("1".equals(str2)) {
/* 220 */       str1 = str1 + " and a.isworkflowdoc = 1 and a.officaltype > 0  ";
/*     */     }
/* 222 */     String str3 = " a.id as workflowid,(case a.isvalid when '3' then a.activeversionid else a.id end) as activeid , a.workflowname, b.id as typeid , b.typename, wn.id as nodeid, wn.nodename, (case when a.istemplate is null then '0' when a.istemplate = '' then '0' else a.istemplate end) as istemplate,b.dsporder dsporder1,( case when a.dsporder is null then 0 else a.dsporder end) as dsporder,a.subcompanyid";
/* 223 */     String str4 = " from workflow_base a left join workflow_type b on a.workflowtype = b.id left join workflow_flownode wf on a.id = wf.workflowid left join workflow_nodebase wn on wf.nodeid = wn.id ";
/* 224 */     String str5 = " dsporder1 asc,typeid asc, istemplate asc,dsporder asc, workflowname asc,a.id desc ";
/* 225 */     String str6 = String.valueOf(this.user.getLanguage());
/* 226 */     RecordSet recordSet = new RecordSet();
/* 227 */     String str7 = "select " + str3 + " " + str4 + " " + str1 + " order by " + str5;
/* 228 */     recordSet.executeQuery(str7, new Object[0]);
/* 229 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 230 */     while (recordSet.next()) {
/* 231 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 232 */       String str8 = recordSet.getString("nodeid");
/* 233 */       String str9 = Util.formatMultiLang(recordSet.getString("nodename"), str6);
/* 234 */       String str10 = Util.formatMultiLang(recordSet.getString("workflowname"), str6);
/* 235 */       String str11 = Util.formatMultiLang(recordSet.getString("typename"), str6);
/* 236 */       hashMap1.put("id", str8);
/* 237 */       hashMap1.put("name", str9);
/* 238 */       hashMap1.put("workflowname", str10);
/* 239 */       String str12 = str9;
/* 240 */       if (!"".equals(str10)) {
/* 241 */         str12 = str12 + " | " + str10;
/*     */       }
/* 243 */       if (!"".equals(str11)) {
/* 244 */         str12 = str12 + " | " + str11;
/*     */       }
/* 246 */       hashMap1.put("title", str12);
/* 247 */       arrayList.add(hashMap1);
/*     */     } 
/* 249 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 250 */     hashMap.put("datas", arrayList);
/* 251 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getBrowserAutoCompleteSqlWhere(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 258 */     WorkflowBrowserService workflowBrowserService = new WorkflowBrowserService();
/* 259 */     List<String> list = workflowBrowserService.getBrowserAutoCompleteSqlWhere(paramHttpServletRequest, paramHttpServletResponse);
/* 260 */     return ((String)list.get(0)).replace("workflowname", "nodename");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 269 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 270 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 271 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 272 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 274 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 15070, "nodename", true);
/*     */     
/* 276 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 125749, "workflowid", "-99991");
/*     */     
/* 278 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, 33806, "typeid", "wftype");
/*     */     
/* 280 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 281 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.SELECT, 15536, "nodetype", arrayList1);
/*     */     
/* 283 */     arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/* 284 */     arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(125, this.user.getLanguage())));
/* 285 */     arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(30835, this.user.getLanguage())));
/* 286 */     arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(553, this.user.getLanguage())));
/* 287 */     arrayList1.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(529067, this.user.getLanguage())));
/* 288 */     arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(529068, this.user.getLanguage())));
/* 289 */     arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(251, this.user.getLanguage())));
/*     */     
/* 291 */     arrayList.add(searchConditionItem1);
/* 292 */     arrayList.add(searchConditionItem2);
/* 293 */     arrayList.add(searchConditionItem3);
/* 294 */     arrayList.add(searchConditionItem4);
/* 295 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String handledBrowserSql(Map<String, Object> paramMap) throws Exception {
/* 302 */     String str1 = Util.null2String(paramMap.get("from"));
/* 303 */     WorkflowBrowserService workflowBrowserService = new WorkflowBrowserService();
/* 304 */     String str2 = workflowBrowserService.getBrowserSqlFrom(paramMap, str1);
/* 305 */     String str3 = "";
/* 306 */     String str4 = Util.null2String(paramMap.get("nodename"));
/* 307 */     String str5 = Util.null2String(paramMap.get("workflowid"));
/* 308 */     String str6 = Util.null2String(paramMap.get("typeid"));
/* 309 */     int i = Util.getIntValue(Util.null2String(paramMap.get("nodetype")), 0);
/* 310 */     if (!"".equals(str4)) {
/* 311 */       RecordSet recordSet = new RecordSet();
/* 312 */       if ("oracle".equals(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 313 */         str3 = str3 + " and upper(wn.nodename) like '%" + Util.fromScreen2(str4, this.user.getLanguage()).toUpperCase() + "%' ";
/*     */       } else {
/* 315 */         str3 = str3 + " and wn.nodename like '%" + Util.fromScreen2(str4, this.user.getLanguage()) + "%' ";
/*     */       } 
/*     */     } 
/* 318 */     if (!"".equals(str5)) {
/* 319 */       str3 = str3 + " and a.id = '" + str5 + "' ";
/*     */     }
/* 321 */     if (!"".equals(str6)) {
/* 322 */       str3 = str3 + " and b.id = '" + str6 + "' ";
/*     */     }
/*     */     
/* 325 */     if (i != 4) {
/* 326 */       str3 = str3 + " and wf.nodetype = '" + i + "' ";
/*     */     }
/* 328 */     return str2 + str3;
/*     */   }
/*     */ 
/*     */   
/*     */   public String nodenameTrans(String paramString1, String paramString2) {
/* 333 */     return Util.formatMultiLang(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public String workflownameTrans(String paramString1, String paramString2) {
/* 337 */     return Util.formatMultiLang(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public String typenameTrans(String paramString1, String paramString2) {
/* 341 */     return Util.formatMultiLang(paramString1, paramString2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WfNodeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */