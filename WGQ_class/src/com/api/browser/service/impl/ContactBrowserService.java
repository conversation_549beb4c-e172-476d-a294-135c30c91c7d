/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserAutoCompleteService;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.crm.service.CustomerService;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ContactBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"firstname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"jobtitle\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"customername\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     String str1 = Util.null2String(paramMap.get("lastname"));
/*  70 */     String str2 = Util.null2String(paramMap.get("firstname"));
/*  71 */     String str3 = Util.null2String(paramMap.get("customer"));
/*  72 */     String str4 = Util.null2String(paramMap.get("customerid"));
/*  73 */     CrmShareBase crmShareBase = new CrmShareBase();
/*  74 */     String str5 = crmShareBase.getTempTable("" + this.user.getUID());
/*  75 */     String str6 = " where 1=1 ";
/*     */     
/*  77 */     CustomerService customerService = new CustomerService();
/*  78 */     if (CustomerService.isPro) {
/*  79 */       str6 = str6 + "AND (t1.status <> 0 OR t1.status IS NULL)";
/*     */     }
/*     */     
/*  82 */     if (!str4.equals("")) {
/*  83 */       str6 = str6 + " and customerid =" + str4;
/*     */     }
/*  85 */     if (!str1.equals("")) {
/*  86 */       str6 = str6 + " and lastname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */     }
/*  88 */     if (!str2.equals("")) {
/*  89 */       str6 = str6 + " and firstname like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */     }
/*  91 */     if (!str3.equals("")) {
/*  92 */       str6 = str6 + " and name like '%" + Util.fromScreen2(str3, this.user.getLanguage()) + "%' ";
/*     */     }
/*     */     
/*  95 */     String str7 = "t1.id,t1.firstname,t1.jobtitle,t1.customerid,t2.name as customername";
/*     */ 
/*     */     
/*  98 */     str6 = str6 + " and t1.customerid = t2.id and t2.deleted=0 ";
/*     */     
/* 100 */     if (!crmShareBase.isCrmManager(this.user.getUID() + "") && !crmShareBase.isSystemManager(this.user.getUID() + "")) {
/* 101 */       str6 = str6 + " and exists(select 1 from " + str5 + "t where t2.id=t.relateditemid) ";
/*     */     }
/*     */     
/* 104 */     String str8 = "t1.fullname";
/*     */ 
/*     */ 
/*     */     
/* 108 */     String str9 = "CRM_CustomerContacter t1 left join CRM_CustomerInfo t2 on t1.customerid = t2.id ";
/*     */     
/* 110 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 111 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 112 */     arrayList.add(new SplitTableColBean("true", "customerid"));
/*     */     
/* 114 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(460, this.user.getLanguage()), "firstname", "firstname", 1);
/* 115 */     splitTableColBean1.setIsInputCol(BoolAttr.TRUE);
/* 116 */     splitTableColBean1.setTablename("CRM_CustomerContacter");
/* 117 */     arrayList.add(splitTableColBean1);
/*     */     
/* 119 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(6086, this.user.getLanguage()), "jobtitle", "jobtitle");
/* 120 */     splitTableColBean2.setTablename("CRM_CustomerContacter");
/* 121 */     arrayList.add(splitTableColBean2);
/*     */     
/* 123 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(136, this.user.getLanguage()), "customername", "customername");
/* 124 */     splitTableColBean3.setTablename("CRM_CustomerInfo");
/* 125 */     arrayList.add(splitTableColBean3);
/*     */     
/* 127 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str9, str6, str8, "t1.id", arrayList);
/*     */     
/*     */     try {
/* 130 */       splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 131 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"firstname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"jobtitle\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"customername\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/* 132 */     } catch (Exception exception) {
/* 133 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 137 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 138 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 143 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 144 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 145 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 146 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 413, "firstname", true));
/* 147 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 475, "lastname"));
/* 148 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 136, "customer"));
/*     */     
/* 150 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 151 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 157 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 158 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 159 */     if ("".equals(str1)) return (Map)hashMap; 
/* 160 */     String str2 = "select a.id,a.firstname,a.jobtitle,a.customerid from CRM_CustomerContacter a where a.id in (" + str1 + ")";
/* 161 */     RecordSet recordSet = new RecordSet();
/* 162 */     recordSet.executeQuery(str2, new Object[0]);
/* 163 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 164 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 165 */     while (recordSet.next()) {
/* 166 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 167 */       hashMap1.put("id", recordSet.getString(1));
/* 168 */       hashMap1.put("firstname", recordSet.getString(2));
/* 169 */       hashMap1.put("jobtitle", recordSet.getString(3));
/* 170 */       hashMap1.put("customername", customerInfoComInfo.getCustomerInfoname(recordSet.getString(4)));
/*     */       
/* 172 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 175 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 176 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 177 */     arrayList1.add(new ListHeadBean("firstname", "", 1, BoolAttr.TRUE));
/* 178 */     arrayList1.add(new ListHeadBean("jobtitle", SystemEnv.getHtmlLabelName(6086, this.user.getLanguage())));
/* 179 */     arrayList1.add(new ListHeadBean("customername", SystemEnv.getHtmlLabelName(136, this.user.getLanguage())));
/*     */     
/* 181 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 182 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 183 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 184 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 189 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 190 */     BrowserAutoCompleteService browserAutoCompleteService = new BrowserAutoCompleteService();
/* 191 */     CrmShareBase crmShareBase = new CrmShareBase();
/* 192 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 193 */     String str1 = browserAutoCompleteService.getCompleteData(this.browserType, this.user, paramHttpServletRequest, paramHttpServletResponse);
/* 194 */     String str2 = crmShareBase.getTempTable("" + this.user.getUID());
/*     */     
/* 196 */     RecordSet recordSet = new RecordSet();
/* 197 */     JSONArray jSONArray1 = JSONArray.parseArray(str1);
/* 198 */     String str3 = "";
/* 199 */     JSONArray jSONArray2 = new JSONArray();
/* 200 */     for (byte b = 0; b < jSONArray1.size(); b++) {
/* 201 */       JSONObject jSONObject = jSONArray1.getJSONObject(b);
/* 202 */       String str4 = (String)jSONObject.get("id");
/* 203 */       String str5 = (String)jSONObject.get("name");
/* 204 */       String str6 = "select id,firstname,jobtitle,customerid from CRM_CustomerContacter where id=?";
/* 205 */       recordSet.executeQuery(str6, new Object[] { str4 });
/* 206 */       if (recordSet.next()) {
/* 207 */         String str7 = recordSet.getString("jobtitle");
/* 208 */         String str8 = customerInfoComInfo.getCustomerInfoname(recordSet.getString("customerid"));
/* 209 */         if ("".equals(str7) && !"".equals(str8)) {
/* 210 */           str3 = str4 + "&nbsp;|&nbsp;" + str5 + "&nbsp;|&nbsp;" + str8;
/*     */         }
/* 212 */         if (!"".equals(str7) && "".equals(str8)) {
/* 213 */           str3 = str4 + "&nbsp;|&nbsp;" + str5 + "&nbsp;|&nbsp;" + str7;
/*     */         }
/* 215 */         if (!"".equals(str7) && !"".equals(str8)) {
/* 216 */           str3 = str4 + "&nbsp;|&nbsp;" + str5 + "&nbsp;|&nbsp;" + str7 + "&nbsp;|&nbsp;" + str8;
/*     */         }
/* 218 */         if ("".equals(str7) && "".equals(str8)) {
/* 219 */           str3 = str4 + "&nbsp;|&nbsp;" + str5;
/*     */         }
/*     */         
/* 222 */         jSONObject.put("title", str3);
/* 223 */         jSONArray2.add(jSONObject);
/*     */       } 
/*     */     } 
/* 226 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray2.toString());
/*     */     
/* 228 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ContactBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */