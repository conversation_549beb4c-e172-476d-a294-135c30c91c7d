/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.menuconfig.MenuUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SystemMenuBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  22 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  23 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  25 */     String str1 = Util.null2String(paramMap.get("menuType"));
/*  26 */     int i = Util.getIntValue(Util.null2String(paramMap.get("resourceId")), 1);
/*  27 */     int j = Util.getIntValue(Util.null2String(paramMap.get("resourceType")), 1);
/*  28 */     int k = this.user.getLanguage();
/*     */     
/*  30 */     int m = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/*  31 */     String str2 = Util.null2String(paramMap.get("ids"));
/*  32 */     List<String> list = Arrays.asList(Util.TokenizerString2("".equals(str2) ? "0" : str2, ","));
/*     */ 
/*     */     
/*  35 */     BaseBean baseBean = new BaseBean();
/*  36 */     ArrayList<String> arrayList1 = new ArrayList();
/*  37 */     if ("left".equals(str1)) {
/*  38 */       arrayList1.addAll(Arrays.asList(Util.TokenizerString2(Util.null2String(baseBean.getPropValue("shieldmenu", "e9menu.frontmenu")), ",")));
/*  39 */       arrayList1.add("115");
/*  40 */       arrayList1.add("119");
/*  41 */     } else if ("top".equals(str1)) {
/*  42 */       arrayList1.addAll(Arrays.asList(Util.TokenizerString2(Util.null2String(baseBean.getPropValue("shieldmenu", "e9menu.backmenu")), ",")));
/*  43 */       arrayList1.add("1");
/*  44 */       arrayList1.add("10");
/*  45 */       arrayList1.add("19");
/*  46 */       arrayList1.add("26");
/*  47 */       arrayList1.add("27");
/*     */     } 
/*     */     
/*  50 */     MenuUtil menuUtil = new MenuUtil(str1, j, i, k);
/*  51 */     menuUtil.setUser(this.user);
/*     */     
/*  53 */     RecordSet recordSet = new RecordSet();
/*  54 */     if (m == 0) {
/*  55 */       recordSet = menuUtil.getAllMenuRs(1, "visible");
/*     */     } else {
/*  57 */       recordSet = menuUtil.getMenuRs(m, "visible");
/*     */     } 
/*     */     
/*  60 */     while (recordSet.next()) {
/*  61 */       String str3 = recordSet.getString("infoId");
/*     */       
/*  63 */       if (list.contains(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/*  67 */       if (arrayList1.contains(str3)) {
/*     */         continue;
/*     */       }
/*  70 */       arrayList1.add(str3);
/*     */ 
/*     */       
/*  73 */       int n = recordSet.getInt("labelId");
/*  74 */       boolean bool1 = (recordSet.getInt("useCustomName") == 1) ? true : false;
/*  75 */       String str4 = recordSet.getString("customName");
/*  76 */       String str5 = recordSet.getString("customName_e");
/*  77 */       String str6 = recordSet.getString("customName_t");
/*  78 */       boolean bool2 = (recordSet.getInt("infoUseCustomName") == 1) ? true : false;
/*  79 */       String str7 = recordSet.getString("infoCustomName");
/*  80 */       String str8 = recordSet.getString("infoCustomName_e");
/*  81 */       String str9 = recordSet.getString("infoCustomName_t");
/*  82 */       String str10 = menuUtil.getMenuText(n, bool1, str4, str5, str6, bool2, str7, str8, str9, k);
/*     */       
/*  84 */       int i1 = recordSet.getInt("allParent");
/*  85 */       boolean bool3 = (i1 > 0) ? true : false;
/*  86 */       boolean bool4 = !bool3 ? true : false;
/*     */       
/*  88 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  89 */       hashMap1.put("key", str3);
/*  90 */       hashMap1.put("id", str3);
/*  91 */       hashMap1.put("name", str10);
/*  92 */       hashMap1.put("isParent", Boolean.valueOf(bool3));
/*  93 */       hashMap1.put("isLeaf", Boolean.valueOf(bool4));
/*  94 */       hashMap1.put("canClick", Boolean.valueOf(true));
/*  95 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/*  98 */     hashMap.put("datas", arrayList);
/*  99 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*     */     
/* 101 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SystemMenuBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */