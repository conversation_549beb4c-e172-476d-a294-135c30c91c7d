/*      */ package com.api.browser.service.impl;
/*      */ import com.api.browser.bean.BrowserTreeNode;
/*      */ import com.api.browser.bean.ListHeadBean;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SearchConditionOption;
/*      */ import com.api.browser.bean.SplitTableBean;
/*      */ import com.api.browser.bean.SplitTableColBean;
/*      */ import com.api.browser.util.BoolAttr;
/*      */ import com.api.browser.util.BrowserBaseUtil;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.BrowserDataType;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*      */ import com.cloudstore.dev.api.util.Util_MobileData;
/*      */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*      */ import com.engine.encrypt.biz.WfEncryptBiz;
/*      */ import com.engine.workflow.biz.excelDesign.MainSubWfSignBiz;
/*      */ import com.engine.workflow.entity.browser.WfBrowserTreeNode;
/*      */ import com.engine.workflow.util.MonitorUtil;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.form.FormComInfo;
/*      */ import weaver.workflow.request.todo.OfsSettingObject;
/*      */ import weaver.workflow.request.todo.RequestUtil;
/*      */ import weaver.workflow.workflow.BillComInfo;
/*      */ import weaver.workflow.workflow.WorkTypeComInfo;
/*      */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*      */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*      */ import weaver.workflow.workflow.WorkflowVersion;
/*      */ 
/*      */ public class WorkflowBrowserService extends BrowserService {
/*   44 */   public final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"workflowname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"typename\"                    }                 ],                \"key\": \"col1_row2\"            },            {                \"configs\": [                    {                        \"key\": \"workflowdesc\"                    }                 ],                \"key\": \"col1_row3\"            }        ],        \"key\": \"col1\"    }]";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*   78 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*   79 */     String str1 = Util.null2String(paramMap.get("isWfTree"));
/*   80 */     String str2 = Util.null2String(paramMap.get("from"));
/*   81 */     boolean bool = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/*   82 */     String str3 = Util.null2String(paramMap.get("showtype"));
/*   83 */     if ("1".equals(str1) && "".equals(str3)) {
/*   84 */       String str4 = Util.null2String(paramMap.get("containsOfs"));
/*   85 */       String str5 = Util.null2String(paramMap.get("viewtype"));
/*   86 */       String str6 = Util.null2String(paramMap.get("roottitle"));
/*   87 */       String str7 = Util.null2String(paramMap.get("typeids"));
/*   88 */       String str8 = Util.null2String(paramMap.get("workflowids"));
/*   89 */       String str9 = Util.null2String(paramMap.get("nodeids"));
/*   90 */       String str10 = Util.null2String(paramMap.get("isLoadAll"));
/*   91 */       String str11 = Util.null2String(Util.null2String(paramMap.get("type")));
/*   92 */       String str12 = Util.null2String(paramMap.get("needNode"));
/*   93 */       boolean bool1 = "1".equals(Util.null2String(paramMap.get("canClickWfType")));
/*      */       
/*   95 */       if ("".equals(str6)) {
/*   96 */         str6 = SystemEnv.getHtmlLabelName(34067, this.user.getLanguage());
/*      */       }
/*   98 */       boolean bool2 = "1".equals(Util.null2String(paramMap.get("needCustomQuery")));
/*   99 */       if (bool2) {
/*  100 */         String str = Util.null2String(paramMap.get("id"));
/*  101 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode("-1", str6, "", true);
/*  102 */         browserTreeNode.setSubs(getWfTreeNodes(paramMap));
/*  103 */         hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, browserTreeNode);
/*  104 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  105 */         hashMap.put(BrowserConstant.BROWSER_RESULT_IS_ALL, Boolean.valueOf(true));
/*      */       } else {
/*  107 */         String str = Util.null2String(paramMap.get("id"));
/*  108 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode("-1", str6, "", true);
/*  109 */         List<BrowserTreeNode> list = new ArrayList();
/*      */         
/*  111 */         RequestUtil requestUtil = new RequestUtil();
/*  112 */         OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  113 */         boolean bool3 = (ofsSettingObject.getIsuse() == 1) ? true : false;
/*  114 */         boolean bool4 = ofsSettingObject.getShowdone().equals("1");
/*  115 */         if ("1".equals(str4) && ("1".equals(str5) || ("2".equals(str5) && bool4) || "4".equals(str5) || "10".equals(str5) || ("3".equals(str5) && bool4) || "5".equals(str5) || "11".equals(str5) || "14".equals(str5))) {
/*  116 */           List<BrowserTreeNode> list1 = getWfTreeData(str7, str8, str9, str10, str11, str12, str, bool1, "1");
/*      */         } else {
/*  118 */           list = getWfTreeData(str7, str8, str9, str10, str11, str12, str, bool1);
/*      */         } 
/*  120 */         if ("".equals(str11)) {
/*  121 */           browserTreeNode.setSubs(list);
/*  122 */           hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, browserTreeNode);
/*      */         } else {
/*  124 */           hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*      */         } 
/*  126 */         hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  127 */         hashMap.put(BrowserConstant.BROWSER_RESULT_IS_ALL, Boolean.valueOf(false));
/*      */       } 
/*  129 */     } else if ("".equals(str3) || "listview".equals(str3)) {
/*  130 */       RequestUtil requestUtil = new RequestUtil();
/*  131 */       OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  132 */       String str4 = getBrowserSqlFrom(paramMap, str2);
/*  133 */       String str5 = Util.null2String(paramMap.get("showSubCompany"));
/*  134 */       String str6 = "id, activeid, workflowname,workflowdesc, formid,isbill,typename,typeid,istemplate,dsporder1,dsporder,subcompanyid,showorder ";
/*  135 */       String str7 = "a.id,(case a.isvalid when '3' then a.activeversionid else a.id end) as activeid, a.workflowname,a.workflowdesc,a.formid,a.isbill,b.typename,b.id as typeid,(case when a.istemplate is null then '0' when a.istemplate = '' then '0' else a.istemplate end) as istemplate,b.dsporder dsporder1,( case when a.dsporder is null then 0 else a.dsporder end) as dsporder,a.subcompanyid, 0 as showorder";
/*  136 */       String str8 = "from workflow_base a,workflow_type b";
/*  137 */       String str9 = " dsporder1 asc,typeid asc, istemplate asc,dsporder asc,showorder asc, workflowname asc,id desc";
/*      */ 
/*      */       
/*  140 */       String str10 = "2".equals(ofsSettingObject.getShowsysname()) ? " os.sysfullname " : " os.sysshortname ";
/*  141 */       String str11 = "workflowid ,workflowid as activeid, workflowname , '' as workflowdesc, ow.sysid ,'ofs' isbill," + str10 + " as typename, os.sysid as typeid, '1' istemplate ," + 2147483647 + " dsporder1 , " + 2147483647 + " dsporder , " + 2147483647 + "  subcompanyid, os.showorder";
/*      */       
/*  143 */       String str12 = " from ofs_workflow ow join ofs_sysinfo os on ow.sysid = os.sysid  ";
/*  144 */       String str13 = " where 1=1 and ow.cancel = '0' and os.cancel = '0' ";
/*      */ 
/*      */       
/*  147 */       String str14 = Util.null2String(paramMap.get("containsOfs"));
/*  148 */       String str15 = Util.null2String(paramMap.get("viewtype"));
/*  149 */       boolean bool1 = ofsSettingObject.getShowdone().equals("1");
/*  150 */       if (ofsSettingObject.getIsuse() == 1 && ("1".equals(paramMap.get("isMobileWorkflow")) || WorkflowBrowserServiceAssist.isNeedOsForPortal(str14, str15))) {
/*  151 */         String str = null;
/*  152 */         String[] arrayOfString = str4.split("and");
/*  153 */         if (arrayOfString.length > 3) {
/*  154 */           for (byte b = 3; b < arrayOfString.length; b++) {
/*  155 */             if (arrayOfString[b].trim().contains("workflowname")) {
/*  156 */               str13 = str13 + " and " + arrayOfString[b];
/*  157 */             } else if (arrayOfString[b].trim().contains("workflowtype")) {
/*  158 */               if (arrayOfString[b].indexOf("-") != -1) {
/*  159 */                 str = arrayOfString[b].replaceFirst("workflowtype", "ow.sysid");
/*  160 */                 str13 = str13 + " and " + str;
/*      */               } else {
/*  162 */                 str13 = str13 + " and 1=2";
/*      */               } 
/*  164 */             } else if (arrayOfString[b].trim().contains("workflowdesc")) {
/*      */               
/*  166 */               if ("1".equals(ofsSettingObject.getShowsysname())) {
/*  167 */                 str = arrayOfString[b].replaceFirst("workflowdesc", "sysshortname");
/*  168 */                 str13 = str13 + " and " + str;
/*  169 */               } else if ("2".equals(ofsSettingObject.getShowsysname())) {
/*  170 */                 str = arrayOfString[b].replaceFirst("workflowdesc", "sysfullname");
/*  171 */                 str13 = str13 + " and " + str;
/*      */               } else {
/*  173 */                 str13 = str13 + " and 1=2";
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         }
/*      */       } else {
/*  179 */         str13 = str13 + " and 1=2 ";
/*      */       } 
/*      */       
/*  182 */       String str16 = Util.null2String(paramMap.get("showHistory"));
/*  183 */       WorkflowBrowserServiceAssist workflowBrowserServiceAssist = new WorkflowBrowserServiceAssist(paramMap, this.user);
/*  184 */       String str17 = workflowBrowserServiceAssist.getOthetSqlWhere();
/*  185 */       if (!"".equals(str17)) {
/*  186 */         if (!"2".equals(str16)) {
/*  187 */           str8 = " from (select " + str7 + " " + str8 + " " + str4 + ") table_a ";
/*  188 */           str4 = " where exists(select 1 from workflow_base where id = table_a.activeid and " + str17 + ") ";
/*  189 */           str7 = str6;
/*      */         } else {
/*  191 */           str4 = str4 + " and " + str17;
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  196 */       str8 = "( select " + str7 + " " + str8 + "  " + str4 + "   union all select  " + str11 + "  " + str12 + " " + str13 + ") tx ";
/*      */       
/*  198 */       str6 = "id,workflowname,workflowdesc,formid,isbill,typename,typeid,istemplate,dsporder1,dsporder,subcompanyid";
/*  199 */       str4 = "";
/*      */ 
/*      */       
/*  202 */       ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  203 */       arrayList.add(new SplitTableColBean("true", "id"));
/*  204 */       String str18 = Util.null2String(paramMap.get("templateImport"));
/*  205 */       String str19 = Util.null2String(paramMap.get("isInitLayout"));
/*      */       
/*  207 */       if (!"statisticItem".equals(Util.null2String(paramMap.get("search"))) && !"customQuery".equals(Util.null2String(paramMap.get("search"))) && !"1".equals(str18) && !"1".equals(str19)) {
/*  208 */         arrayList.add(new SplitTableColBean("true", "formid"));
/*      */       }
/*  210 */       arrayList.add(new SplitTableColBean("true", "isbill"));
/*  211 */       if ("customQuery".equals(Util.null2String(paramMap.get("search")))) {
/*  212 */         arrayList.add(new SplitTableColBean("formid", getClass().getName() + ".getFormName", "column:isBill+" + this.user.getLanguage(), 2));
/*      */       }
/*  214 */       if ("statisticItem".equals(Util.null2String(paramMap.get("search")))) {
/*  215 */         SplitTableColBean splitTableColBean = new SplitTableColBean("true", "formid");
/*  216 */         splitTableColBean.setTransmethod(getClass().getName() + ".getFormName");
/*  217 */         splitTableColBean.setOtherpara("column:isBill+" + this.user.getLanguage());
/*  218 */         splitTableColBean.setTransMethodForce("true");
/*  219 */         arrayList.add(splitTableColBean);
/*      */       } 
/*  221 */       if ("1".equals(str18)) {
/*  222 */         SplitTableColBean splitTableColBean1 = (new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(517314, Util.getIntValue(this.user.getLanguage())), "workflowname", null, 1)).setIsInputCol(BoolAttr.TRUE);
/*  223 */         splitTableColBean1.setHide("true");
/*  224 */         splitTableColBean1.setKey("wfName1");
/*  225 */         arrayList.add(splitTableColBean1);
/*  226 */         SplitTableColBean splitTableColBean2 = new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(517314, Util.getIntValue(this.user.getLanguage())), "workflowname", null, 1);
/*  227 */         splitTableColBean2.setTransmethod(getClass().getName() + ".workflowNameTrans");
/*  228 */         splitTableColBean2.setOtherpara("column:id+" + this.user.getLanguage());
/*  229 */         splitTableColBean2.setKey("wfName2");
/*  230 */         arrayList.add(splitTableColBean2);
/*      */         
/*  232 */         arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(33806, this.user.getLanguage()), "typename", null, 0));
/*  233 */         arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15600, this.user.getLanguage()), "formid", "formid", getClass().getName() + ".getFormName", "column:isBill+" + this.user.getLanguage()));
/*  234 */         arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(385207, this.user.getLanguage()), "istemplate", "istemplate", getClass().getName() + ".isTemplateTrans", "" + this.user.getLanguage()));
/*      */         
/*  236 */         if (bool == true && "1".equals(str5)) {
/*  237 */           arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname"));
/*      */         }
/*  239 */       } else if ("1".equals(str19)) {
/*      */         
/*  241 */         String str = Util.null2String(paramMap.get("ismult"));
/*  242 */         if ("1".equals(str)) {
/*      */           
/*  244 */           SplitTableColBean splitTableColBean1 = (new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(81651, Util.getIntValue(this.user.getLanguage())), "workflowname", null, 1)).setIsInputCol(BoolAttr.TRUE);
/*  245 */           arrayList.add(splitTableColBean1);
/*      */           
/*  247 */           arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(33806, this.user.getLanguage()), "typename", null, 0));
/*  248 */           SplitTableColBean splitTableColBean2 = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81710, this.user.getLanguage()), "workflowdesc", null, 0);
/*      */           
/*  250 */           if (!this.isMobile) {
/*  251 */             splitTableColBean2.setTransmethod(getClass().getName() + ".workflowDescTrans");
/*      */           }
/*  253 */           arrayList.add(splitTableColBean2);
/*      */         } else {
/*      */           
/*  256 */           SplitTableColBean splitTableColBean1 = (new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(81651, Util.getIntValue(this.user.getLanguage())), "workflowname", null, 1)).setIsInputCol(BoolAttr.TRUE);
/*  257 */           splitTableColBean1.setHide("true");
/*  258 */           splitTableColBean1.setKey("wfName1");
/*  259 */           arrayList.add(splitTableColBean1);
/*  260 */           SplitTableColBean splitTableColBean2 = new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(81651, Util.getIntValue(this.user.getLanguage())), "workflowname", null, 1);
/*  261 */           splitTableColBean2.setTransmethod(getClass().getName() + ".workflowNameTrans");
/*  262 */           splitTableColBean2.setOtherpara("column:id+" + this.user.getLanguage());
/*  263 */           splitTableColBean2.setKey("wfName2");
/*  264 */           arrayList.add(splitTableColBean2);
/*      */           
/*  266 */           arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(15600, this.user.getLanguage()), "formid", "formid", getClass().getName() + ".getFormName", "column:isBill+" + this.user.getLanguage()));
/*      */           
/*  268 */           arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(33806, this.user.getLanguage()), "typename", null, 0));
/*      */           
/*  270 */           arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(81710, this.user.getLanguage()), "workflowdesc", null, 0));
/*      */           
/*  272 */           arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(15513, this.user.getLanguage()), "dsporder", "dsporder", 0));
/*      */         } 
/*      */       } else {
/*      */         
/*  276 */         SplitTableColBean splitTableColBean1 = (new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(81651, this.user.getLanguage()), "workflowname", null, 1)).setIsInputCol(BoolAttr.TRUE);
/*  277 */         if (1 == Util.getIntValue(Util.null2String(paramMap.get("isReportForm")), 0)) {
/*  278 */           splitTableColBean1.setTransmethod(getClass().getName() + ".workflowNameTrans");
/*  279 */           splitTableColBean1.setOtherpara("column:id+" + this.user.getLanguage());
/*      */         } 
/*  281 */         if ("1,3".equals(Util.null2s(Util.null2String(paramMap.get("isvalid")), "1"))) {
/*  282 */           splitTableColBean1.setTransmethod(getClass().getName() + ".workflowNameTrans");
/*  283 */           splitTableColBean1.setOtherpara("column:id+" + this.user.getLanguage());
/*      */         } 
/*  285 */         arrayList.add(splitTableColBean1);
/*  286 */         SplitTableColBean splitTableColBean2 = new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(33806, this.user.getLanguage()), "typename", null, 0);
/*  287 */         if ("1".equals(paramMap.get("isMobileWorkflow"))) {
/*  288 */           splitTableColBean2.setTransmethod(getClass().getName() + ".typeNameTrans");
/*  289 */           splitTableColBean2.setOtherpara("column:typeid+" + this.user.getLanguage());
/*      */         } 
/*  291 */         arrayList.add(splitTableColBean2);
/*  292 */         SplitTableColBean splitTableColBean3 = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81710, this.user.getLanguage()), "workflowdesc", null, 0);
/*      */         
/*  294 */         if (!this.isMobile) {
/*  295 */           splitTableColBean3.setTransmethod(getClass().getName() + ".workflowDescTrans");
/*      */         }
/*  297 */         arrayList.add(splitTableColBean3);
/*      */ 
/*      */         
/*  300 */         if (bool == true && "1".equals(str5)) {
/*  301 */           arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname"));
/*      */         }
/*      */       } 
/*  304 */       SplitTableBean splitTableBean = new SplitTableBean(str6, str8, str4, str9, "a.id", arrayList);
/*  305 */       boolean bool2 = "1".equals(Util.null2String(paramMap.get("ismobile")));
/*  306 */       if (bool2 && !"1".equals(str18)) {
/*  307 */         List list = Util_MobileData.createList("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"workflowname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"typename\"                    }                 ],                \"key\": \"col1_row2\"            },            {                \"configs\": [                    {                        \"key\": \"workflowdesc\"                    }                 ],                \"key\": \"col1_row3\"            }        ],        \"key\": \"col1\"    }]");
/*  308 */         SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createJsonTemplateBean("theme_default", list);
/*  309 */         splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/*      */       } 
/*  311 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  312 */     } else if ("typeview".equals(str3)) {
/*  313 */       String str4 = Util.null2String(Util.null2String(paramMap.get("type")));
/*  314 */       String str5 = Util.null2String(paramMap.get("isLoadAll"));
/*  315 */       boolean bool1 = "1".equals(Util.null2String(paramMap.get("canClickWfType")));
/*  316 */       RequestUtil requestUtil = new RequestUtil();
/*  317 */       OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*      */       
/*  319 */       boolean bool2 = (1 == ofsSettingObject.getIsuse()) ? true : false;
/*  320 */       String str6 = Util.null2String(paramMap.get("viewtype"));
/*  321 */       String str7 = Util.null2String(paramMap.get("containsOfs"));
/*  322 */       boolean bool3 = ((bool2 && "1".equals(paramMap.get("isMobileWorkflow"))) || WorkflowBrowserServiceAssist.isNeedOsForPortal(str7, str6)) ? true : false;
/*  323 */       List<WfBrowserTreeNode> list = new ArrayList();
/*  324 */       String str8 = Util.null2String(paramMap.get("id"));
/*  325 */       String str9 = getBrowserSqlFrom(paramMap, str2);
/*  326 */       WorkflowBrowserServiceAssist workflowBrowserServiceAssist = new WorkflowBrowserServiceAssist(paramMap, this.user);
/*  327 */       if ("".equals(str4)) {
/*  328 */         List<WfBrowserTreeNode> list1 = workflowBrowserServiceAssist.getWfTypes(str9, str5, bool1, bool3, false);
/*  329 */       } else if ("wftype".equals(str4)) {
/*  330 */         list = workflowBrowserServiceAssist.getWorkflows(str9, str5, str8, false);
/*      */       } 
/*  332 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*  333 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  334 */       hashMap.put(BrowserConstant.BROWSER_RESULT_IS_ALL, Boolean.valueOf(false));
/*      */     } 
/*  336 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  341 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  342 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  343 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  344 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  345 */     String str1 = Util.null2String(paramMap.get("templateImport"));
/*  346 */     if ("1".equals(str1)) {
/*  347 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "385207,195", "workflowname", true));
/*      */     } else {
/*  349 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 81651, "workflowname", true));
/*      */     } 
/*      */ 
/*      */     
/*  353 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.BROWSER, 33806, "typeid", "wftype");
/*  354 */     searchConditionItem1.getBrowserConditionParam().setIcon("icon-coms-workflow");
/*  355 */     searchConditionItem1.getBrowserConditionParam().setIconBgcolor("#0079DE");
/*      */     
/*  357 */     if ("1".equals(paramMap.get("isMobileWorkflow"))) {
/*  358 */       searchConditionItem1.getBrowserConditionParam().getDataParams().put("isMobileWorkflowType", "1");
/*  359 */       searchConditionItem1.getBrowserConditionParam().getConditionDataParams().put("isMobileWorkflowType", "1");
/*      */     } 
/*      */     
/*  362 */     arrayList.add(searchConditionItem1);
/*      */ 
/*      */     
/*  365 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 81710, "workflowdesc");
/*  366 */     arrayList.add(searchConditionItem2);
/*      */ 
/*      */     
/*  369 */     if ("1".equals(str1)) {
/*      */       
/*  371 */       SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 385207, "isTemplate");
/*  372 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*  373 */       arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), false));
/*  374 */       arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(33658, this.user.getLanguage()), true));
/*  375 */       arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(34067, this.user.getLanguage()), false));
/*  376 */       searchConditionItem3.setOptions(arrayList1);
/*  377 */       searchConditionItem3.setSelectWidth("40%");
/*      */       
/*  379 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  380 */       SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.SELECT, 19061, "wfstatus");
/*  381 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*  382 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), false));
/*  383 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(2246, this.user.getLanguage()) + "/" + SystemEnv.getHtmlLabelName(25496, this.user.getLanguage()), true));
/*  384 */       arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(2246, this.user.getLanguage()), false));
/*  385 */       arrayList2.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(25496, this.user.getLanguage()), false));
/*  386 */       arrayList2.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(2245, this.user.getLanguage()), false));
/*  387 */       searchConditionItem4.setOptions(arrayList2);
/*  388 */       hashMap1.put("2", searchConditionItem4);
/*  389 */       searchConditionItem4.setValue("1");
/*      */       
/*  391 */       searchConditionItem3.setSelectLinkageDatas(hashMap1);
/*  392 */       arrayList.add(searchConditionItem3);
/*      */       
/*  394 */       SearchConditionItem searchConditionItem5 = conditionFactory.createCondition(ConditionType.SELECT, 18411, "formType");
/*  395 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*  396 */       arrayList3.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/*  397 */       arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(125026, this.user.getLanguage()), false));
/*  398 */       arrayList3.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(125027, this.user.getLanguage()), false));
/*  399 */       searchConditionItem5.setOptions(arrayList3);
/*  400 */       arrayList.add(searchConditionItem5);
/*      */ 
/*      */       
/*  403 */       SearchConditionItem searchConditionItem6 = conditionFactory.createCondition(ConditionType.SELECT, "18499,18500", "showHistory");
/*  404 */       ArrayList<SearchConditionOption> arrayList4 = new ArrayList();
/*  405 */       arrayList4.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(89, this.user.getLanguage()), false));
/*  406 */       arrayList4.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(23857, this.user.getLanguage()), true));
/*  407 */       searchConditionItem6.setOptions(arrayList4);
/*  408 */       arrayList.add(searchConditionItem6);
/*      */     } 
/*      */ 
/*      */     
/*  412 */     boolean bool = (new ManageDetachComInfo()).isUseWfManageDetach();
/*      */     
/*  414 */     String str2 = Util.null2s(Util.null2String(paramMap.get("org")), "0");
/*  415 */     if (bool && "1".equals(str2)) {
/*  416 */       String str = Util.null2String(paramMap.get("rightStr"));
/*  417 */       if (!"".equals(str)) {
/*  418 */         SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subcompanyid", "169");
/*  419 */         searchConditionItem.getBrowserConditionParam().setTitle(SystemEnv.getHtmlLabelName(141, this.user.getLanguage()));
/*  420 */         searchConditionItem.getBrowserConditionParam().getDataParams().put("rightStr", str);
/*  421 */         searchConditionItem.getBrowserConditionParam().getCompleteParams().put("rightStr", str);
/*  422 */         searchConditionItem.getBrowserConditionParam().getConditionDataParams().put("rightStr", str);
/*  423 */         arrayList.add(searchConditionItem);
/*      */       } 
/*      */     } 
/*      */     
/*  427 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  439 */     List<String> list = getBrowserAutoCompleteSqlWhere(paramHttpServletRequest, paramHttpServletResponse);
/*  440 */     String str1 = list.get(0);
/*  441 */     String str2 = list.get(1);
/*  442 */     RequestUtil requestUtil = new RequestUtil();
/*  443 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("isMobileWorkflow"));
/*  444 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("viewtype"));
/*  445 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("containsOfs"));
/*  446 */     boolean bool1 = (("1".equalsIgnoreCase(str3) || WorkflowBrowserServiceAssist.isNeedOsForPortal(str5, str4)) && requestUtil.getOfsSetting().getIsuse() == 1) ? true : false;
/*  447 */     RecordSet recordSet = new RecordSet();
/*  448 */     if (bool1) {
/*  449 */       String str = "select * from ( select id ,workflowname,workflowtype,formid,isbill,subcompanyid from workflow_base a  " + str1 + " union all select workflowid,workflowname,sysid, -1 as formid, '-1' as isbill, -1 as subcompanyid from ofs_workflow where " + str2 + ") temp_w order by workflowname ,id";
/*      */       
/*  451 */       recordSet.executeQuery(str, new Object[0]);
/*      */     } else {
/*  453 */       recordSet.executeQuery("select id,workflowname,workflowtype,formid,isbill,subcompanyid from workflow_base a " + str1 + " order by workflowname,id", new Object[0]);
/*      */     } 
/*  455 */     boolean bool2 = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/*  456 */     boolean bool3 = (bool2 == true && Util.null2String(paramHttpServletRequest.getParameter("showSubCompany")).equals("1")) ? true : false;
/*  457 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  458 */     WorkTypeComInfo workTypeComInfo = new WorkTypeComInfo();
/*  459 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  460 */     while (recordSet.next()) {
/*  461 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  462 */       String str6 = recordSet.getString("workflowname");
/*  463 */       String str7 = recordSet.getString("workflowtype");
/*  464 */       hashMap1.put("id", recordSet.getString("id"));
/*  465 */       hashMap1.put("name", str6);
/*  466 */       String str8 = recordSet.getString("formid");
/*  467 */       String str9 = recordSet.getString("isbill");
/*  468 */       hashMap1.put("formid", str8);
/*  469 */       hashMap1.put("isbill", str9);
/*  470 */       String str10 = recordSet.getString("subcompanyid");
/*  471 */       String str11 = str6;
/*  472 */       String str12 = workTypeComInfo.getWorkTypename(str7);
/*  473 */       if (str7.contains("-")) {
/*  474 */         str12 = requestUtil.getSysname(str7, "1");
/*  475 */         if ("2".equals(requestUtil.getOfsSetting().getShowsysname())) {
/*  476 */           str12 = requestUtil.getSysname(str7, "2");
/*      */         }
/*      */       } 
/*      */       
/*  480 */       if ("customQuery".equals(Util.null2String(paramHttpServletRequest.getParameter("complete"))) || "statisticItem".equals(Util.null2String(paramHttpServletRequest.getParameter("search")))) {
/*  481 */         hashMap1.put("formidspan", getFormName(str8, str9 + "+" + this.user.getLanguage()));
/*      */       }
/*  483 */       if (bool3 && !"".equals(str10)) {
/*  484 */         hashMap1.put("title", str6 + "|" + subCompanyComInfo.getSubCompanyname(str10));
/*  485 */         str11 = str11 + "|" + subCompanyComInfo.getSubCompanyname(str10);
/*      */       } 
/*  487 */       if (!"".equals(str12)) {
/*  488 */         hashMap1.put("title", str11 + " | " + str12);
/*      */       }
/*  490 */       arrayList.add(hashMap1);
/*      */     } 
/*  492 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  493 */     hashMap.put("datas", arrayList);
/*  494 */     return (Map)hashMap;
/*      */   }
/*      */   
/*      */   public List<String> getBrowserAutoCompleteSqlWhere(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  498 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("typeid")), 0);
/*  499 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("formid")), 0);
/*  500 */     int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("isbill")), -1);
/*  501 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("from"));
/*  502 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("isMobileWorkflow"));
/*  503 */     RequestUtil requestUtil = new RequestUtil();
/*  504 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("viewtype"));
/*  505 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("containsOfs"));
/*  506 */     boolean bool = (("1".equalsIgnoreCase(str2) || WorkflowBrowserServiceAssist.isNeedOsForPortal(str4, str3)) && requestUtil.getOfsSetting().getIsuse() == 1) ? true : false;
/*  507 */     String str5 = " 1=1 ";
/*  508 */     String str6 = Util.null2String(Util.null2String(paramHttpServletRequest.getParameter("wfRanges")));
/*  509 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  510 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("templateImport"));
/*  511 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("financialProof"));
/*  512 */     String str10 = Util.null2String(paramHttpServletRequest.getParameter("subCompanyListStr"));
/*      */     
/*  514 */     String str11 = Util.null2String(paramHttpServletRequest.getParameter("isCustomize"));
/*      */     
/*  516 */     String str12 = Util.null2s(Util.null2String(paramHttpServletRequest.getParameter("isvalid")), "1");
/*  517 */     if (str12.length() == 1) {
/*  518 */       str12 = "'" + str12 + "'";
/*      */     } else {
/*  520 */       String[] arrayOfString = str12.split(",");
/*  521 */       str12 = "";
/*  522 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  523 */         str12 = str12 + "'" + arrayOfString[b] + "',";
/*      */       }
/*  525 */       str12 = str12.substring(0, str12.length() - 1);
/*      */     } 
/*  527 */     RecordSet recordSet = new RecordSet();
/*  528 */     String str13 = "";
/*  529 */     if ("1".equals(str8)) {
/*  530 */       str13 = " where istemplate = '1' ";
/*      */     } else {
/*  532 */       str13 = " where (istemplate != '1' or istemplate is null) and isvalid in(" + str12 + ") ";
/*      */     } 
/*  534 */     if (i != 0)
/*  535 */       str13 = str13 + " and workflowtype='" + i + "' "; 
/*  536 */     if (j != 0 && k != -1)
/*  537 */       str13 = str13 + " and formid='" + j + "' and isbill='" + k + "' "; 
/*  538 */     if (!"".equals(str6)) {
/*  539 */       String str = WorkflowVersion.getAllVersionStringByWFIDs(str6);
/*  540 */       if (!"".equals(str))
/*  541 */         str13 = str13 + " and (" + Util.getSubINClause(str, "id", "in") + ") "; 
/*      */     } 
/*  543 */     if ("1".equals(str11)) {
/*  544 */       str13 = str13 + " and formid<0 and isbill=1 ";
/*      */     }
/*  546 */     String str14 = Util.null2String(paramHttpServletRequest.getParameter("sqlwhere"));
/*  547 */     if (!str14.equals("")) {
/*  548 */       str13 = str13 + str14;
/*      */     }
/*  550 */     if (!"".equals(str7)) {
/*  551 */       if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/*  552 */         str13 = str13 + " and (upper(workflowname) like '%" + str7.toUpperCase() + "%' ";
/*  553 */         str13 = str13 + " or upper(f_GetPy(workflowname)) like '%" + str7.toUpperCase() + "%'";
/*  554 */         if (bool) {
/*  555 */           str5 = str5 + " and (upper(workflowname) like '%" + str7.toUpperCase() + "%' ";
/*  556 */           str5 = str5 + "  or upper(f_GetPy(workflowname)) like '%" + str7.toUpperCase() + "%' ";
/*      */         } 
/*  558 */       } else if ("sqlserver".equals(recordSet.getDBType())) {
/*  559 */         str13 = str13 + " and (workflowname like '%" + str7 + "%' ";
/*  560 */         str13 = str13 + " or [dbo].f_GetPy(workflowname) like '%" + str7.toUpperCase() + "%'";
/*  561 */         if (bool) {
/*  562 */           str5 = str5 + " and (workflowname like '%" + str7 + "%' ";
/*  563 */           str5 = str5 + "  or [dbo].f_GetPy(workflowname) like '%" + str7.toUpperCase() + "%' ";
/*      */         } 
/*      */       } else {
/*  566 */         str13 = str13 + " and (workflowname like '%" + str7 + "%' ";
/*  567 */         if (bool) {
/*  568 */           str5 = str5 + "  and (workflowname like '%" + str7 + "%' ";
/*      */         }
/*      */       } 
/*  571 */       str13 = str13 + ")";
/*  572 */       if (bool) {
/*  573 */         str5 = str5 + ")";
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  578 */     if ("1".equals(str9) && this.user.getUID() != 1) {
/*  579 */       str13 = str13 + " and subcompanyid in (" + str10 + ") ";
/*      */     }
/*      */ 
/*      */     
/*  583 */     if ("monitor".equals(Util.null2String(paramHttpServletRequest.getParameter("complete")))) {
/*  584 */       str13 = str13 + MonitorUtil.getMonitorWorkflowCompleteSqlWhere(this.user);
/*      */     }
/*      */ 
/*      */     
/*  588 */     if ("monitorSet".equals(Util.null2String(paramHttpServletRequest.getParameter("complete")))) {
/*  589 */       str13 = str13 + MonitorUtil.getMonitorSetWorkflowSearchSqlWhere(this.user, Util.null2String(paramHttpServletRequest.getParameter("infoid")), "complete");
/*      */     }
/*      */     
/*  592 */     if ("mainSubChoose".equals(Util.null2String(paramHttpServletRequest.getParameter("search")))) {
/*  593 */       int m = Util.getIntValue(paramHttpServletRequest.getParameter("chooseType") + "");
/*  594 */       int n = Util.getIntValue(paramHttpServletRequest.getParameter("basewfid") + "");
/*  595 */       MainSubWfSignBiz mainSubWfSignBiz = new MainSubWfSignBiz();
/*  596 */       str13 = str13 + mainSubWfSignBiz.buildFilterSqlStr("a.id", n, m);
/*      */     } 
/*      */     
/*  599 */     boolean bool1 = (new ManageDetachComInfo()).isUseWfManageDetach();
/*      */     
/*  601 */     String str15 = Util.null2String(paramHttpServletRequest.getParameter("isInitLayout"));
/*  602 */     if (("pathImport".equals(Util.null2String(paramHttpServletRequest.getParameter("pathImportType"))) || "1".equals(str15)) && bool1) {
/*  603 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  604 */       String str = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", 0);
/*  605 */       str13 = str13 + " and (" + Util.getSubINClause(getPathImportRightIds(str), "id", "in") + ") ";
/*      */     } 
/*      */     
/*  608 */     if ("reportSetting".equals(str1) && (new ManageDetachComInfo()).isUseWfManageDetach()) {
/*      */       
/*  610 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  611 */       String str = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowReportManage:All", -1);
/*  612 */       if (!str.equals("")) {
/*  613 */         String str18 = Util.getSubINClause(str, "subcompanyid", "in");
/*  614 */         if (!str18.equals("")) {
/*  615 */           str13 = str13 + " and " + str18;
/*      */         }
/*      */       } else {
/*  618 */         str13 = str13 + " and 1=2";
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  623 */     String str16 = Util.null2String(paramHttpServletRequest.getParameter("isOdoc"));
/*  624 */     if ("1".equals(str16)) {
/*  625 */       str13 = str13 + " and a.officaltype>0 and a.id in( select c.workflowid from workflow_createdoc c where c.status=1 ) ";
/*  626 */       if (j != 0) {
/*  627 */         str13 = str13 + " and a.formid=" + j + " ";
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  632 */     String str17 = Util.null2String(paramHttpServletRequest.getParameter("cpttype"));
/*  633 */     if (!"".equals(str17)) {
/*  634 */       String str = Util.null2String(paramHttpServletRequest.getParameter("cptwfids"));
/*  635 */       str13 = str13 + "and isbill=1 and exists ( select 1 from workflow_billfield t2 where t2.billid=a.formid and t2.fieldhtmltype=3 and t2.type= " + str17 + " and a.formid not in(14,18,19,201,220,221,222,224) ) and a.id not in(-1" + str + ")";
/*      */     } 
/*      */ 
/*      */     
/*  639 */     if ("1".equals((new WorkflowConfigComInfo()).getValue("workflow_browserData_detachable")) && !"monitor".equals(Util.null2String(paramHttpServletRequest.getParameter("complete"))) && !"monitorSet".equals(Util.null2String(paramHttpServletRequest.getParameter("complete")))) {
/*  640 */       str13 = str13 + getDetachableCondition("a.");
/*      */     }
/*  642 */     return Arrays.asList(new String[] { str13, str5 });
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSystemname(String paramString1, String paramString2) {
/*  653 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*  654 */     String str1 = arrayOfString[0];
/*  655 */     String str2 = arrayOfString[1];
/*  656 */     int i = Util.getIntValue(paramString1, 0);
/*  657 */     String str3 = "";
/*  658 */     if (i < 0) {
/*  659 */       RecordSet recordSet = new RecordSet();
/*  660 */       recordSet.executeSql("select sysshortname,sysfullname from ofs_sysinfo where sysid=" + i);
/*  661 */       if (recordSet.next()) {
/*  662 */         if (str1.equals("1")) {
/*  663 */           str3 = recordSet.getString(1);
/*  664 */         } else if (str1.equals("2")) {
/*  665 */           str3 = recordSet.getString(2);
/*      */         } else {
/*  667 */           str3 = "";
/*      */         } 
/*      */       }
/*      */     } else {
/*  671 */       str3 = str2;
/*      */     } 
/*  673 */     return str3;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getWorkflowTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  684 */     return (Map)new HashMap<>();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  690 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  691 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  692 */     boolean bool = "1".equals(paramMap.get("needHandleType"));
/*  693 */     if ("".equals(str1)) return (Map)hashMap;
/*      */     
/*  695 */     String str2 = Util.null2String(paramMap.get("isMobileWorkflow"));
/*  696 */     String str3 = Util.null2String(paramMap.get("viewtype"));
/*  697 */     String str4 = Util.null2String(paramMap.get("containsOfs"));
/*      */     
/*  699 */     RequestUtil requestUtil = new RequestUtil();
/*  700 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  701 */     boolean bool1 = (("1".equals(str2) || WorkflowBrowserServiceAssist.isNeedOsForPortal(str4, str3)) && ofsSettingObject.getIsuse() == 1) ? true : false;
/*      */     
/*  703 */     String str5 = Util.null2String(paramMap.get("types"));
/*  704 */     if (bool && !"".equals(str5)) {
/*  705 */       String str = handleSelectids(str5, paramMap);
/*  706 */       if (!"".equals(str)) {
/*  707 */         str1 = str;
/*      */       }
/*      */     } 
/*  710 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  711 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  712 */     RecordSet recordSet = new RecordSet();
/*  713 */     String str6 = String.valueOf(this.user.getLanguage());
/*      */     
/*  715 */     String str7 = "";
/*  716 */     String str8 = "";
/*  717 */     if (str1.length() > 0) {
/*  718 */       str7 = str7 + Util.getSubINClause(str1, "a.id", "in");
/*  719 */       str8 = str8 + Util.getSubINClause(str1, "workflowid", "in");
/*      */     } 
/*  721 */     String str9 = "select a.id,a.workflowname,b.typename,b.id as typeid,a.formid,a.isbill,a.workflowdesc from workflow_base a,workflow_type b where a.workflowtype=b.id and ( " + str7 + " )";
/*  722 */     if (bool1) {
/*  723 */       str9 = str9 + " union all select ow.workflowid,ow.workflowname,os.sysshortname as typename,ow.sysid as typeid,ow.sysid as formid,'ofs' isbill,'' as workflowdesc from ofs_workflow ow join ofs_sysinfo os on ow.sysid = os.sysid where (" + str8 + ")";
/*      */     }
/*  725 */     recordSet.executeQuery(str9, new Object[0]);
/*  726 */     String str10 = "";
/*  727 */     while (recordSet.next()) {
/*  728 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  729 */       hashMap1.put("id", recordSet.getString(1));
/*  730 */       str10 = recordSet.getString("workflowdesc");
/*  731 */       String str11 = Util.formatMultiLang(Util.null2String(recordSet.getString(2)), str6);
/*  732 */       String str12 = Util.formatMultiLang(Util.null2String(recordSet.getString(3)), str6);
/*  733 */       String str13 = Util.null2String(recordSet.getString("typeid"));
/*  734 */       String str14 = str11 + "|" + str12;
/*      */       
/*  736 */       if (!"".equals(str10)) {
/*  737 */         str10 = "<br>" + str10;
/*      */       }
/*  739 */       hashMap1.put("workflowname", str11);
/*  740 */       hashMap1.put("workflowdesc", str10);
/*  741 */       if ("1".equals(str2) && ofsSettingObject.getIsuse() == 1) {
/*  742 */         str12 = typeNameTrans(str12, str13 + "+" + this.user.getLanguage());
/*      */       }
/*  744 */       hashMap1.put("typename", str12);
/*  745 */       String str15 = recordSet.getString("formid");
/*  746 */       String str16 = recordSet.getString("isbill");
/*  747 */       hashMap1.put("formid", str15);
/*  748 */       hashMap1.put("isbill", str16);
/*      */       
/*  750 */       if ("customQuery".equals(Util.null2String(paramMap.get("search")))) {
/*  751 */         hashMap1.put("formidspan", getFormName(str15, str16 + "+" + this.user.getLanguage()));
/*      */       }
/*  753 */       arrayList1.add(hashMap1);
/*      */     } 
/*  755 */     arrayList.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  756 */     arrayList.add(new ListHeadBean("workflowname", "", 1, BoolAttr.TRUE));
/*  757 */     arrayList.add(new ListHeadBean("typename", "", 0));
/*  758 */     arrayList.add(new ListHeadBean("workflowdesc", "", 0));
/*      */     
/*  760 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*  761 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList1, str1, "id"));
/*  762 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  763 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<BrowserTreeNode> getWfTreeData(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, boolean paramBoolean) {
/*  777 */     List<String> list1 = Util.splitString2List(paramString1, ",");
/*  778 */     List<String> list2 = Util.splitString2List(paramString2, ",");
/*  779 */     if ("".equals(paramString5))
/*  780 */       return getWfTypes(list1, list2, paramString4, paramString6, paramBoolean, "0"); 
/*  781 */     if ("wftype".equals(paramString5))
/*  782 */       return getWorkflowsByTypeid(paramString7, list2, paramString6, "0", getWfInfo().get(0), getWfInfo().get(1)); 
/*  783 */     if ("wf".equals(paramString5) && "1".equals(paramString6)) {
/*  784 */       List<String> list = Util.splitString2List(paramString3, ",");
/*  785 */       return getWfNodes(paramString7, list);
/*      */     } 
/*  787 */     return new ArrayList<>();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<BrowserTreeNode> getWfTreeData(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, boolean paramBoolean, String paramString8) {
/*  800 */     List<String> list1 = Util.splitString2List(paramString1, ",");
/*  801 */     List<String> list2 = Util.splitString2List(paramString2, ",");
/*  802 */     if ("".equals(paramString5))
/*  803 */       return getWfTypes(list1, list2, paramString4, paramString6, paramBoolean, paramString8); 
/*  804 */     if ("wftype".equals(paramString5))
/*  805 */       return getWorkflowsByTypeid(paramString7, list2, paramString6, paramString8, (Map<String, String>)null, (Map<String, String>)null); 
/*  806 */     if ("wf".equals(paramString5) && "1".equals(paramString6)) {
/*  807 */       List<String> list = Util.splitString2List(paramString3, ",");
/*  808 */       return getWfNodes(paramString7, list);
/*      */     } 
/*  810 */     return new ArrayList<>();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getWfTypes(List<String> paramList1, List<String> paramList2, String paramString1, String paramString2, boolean paramBoolean, String paramString3) {
/*  824 */     WorkTypeComInfo workTypeComInfo = new WorkTypeComInfo();
/*  825 */     workTypeComInfo.setTofirstRow();
/*  826 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*  827 */     Map<String, String> map1 = getWfInfo().get(0);
/*  828 */     Map<String, String> map2 = getWfInfo().get(1);
/*      */     
/*  830 */     while (workTypeComInfo.next()) {
/*  831 */       String str1 = workTypeComInfo.getWorkTypeid();
/*  832 */       String str2 = workTypeComInfo.getWorkTypename();
/*  833 */       List<BrowserTreeNode> list = getWorkflowsByTypeid(str1, paramList2, paramString2, "0", map1, map2);
/*  834 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode(str1, str2, "-1", (list.size() > 0), list);
/*  835 */       browserTreeNode.setType("wftype");
/*  836 */       browserTreeNode.setCanClick(paramBoolean);
/*  837 */       browserTreeNode.setSelected(paramList1.contains(str1));
/*  838 */       arrayList.add(browserTreeNode);
/*      */     } 
/*  840 */     RequestUtil requestUtil = new RequestUtil();
/*  841 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  842 */     boolean bool = (ofsSettingObject.getIsuse() == 1) ? true : false;
/*  843 */     if (bool && "1".equals(paramString3)) {
/*  844 */       RecordSet recordSet = new RecordSet();
/*  845 */       recordSet.executeQuery("select sysid,sysshortname from ofs_sysinfo", new Object[0]);
/*  846 */       while (recordSet.next()) {
/*  847 */         String str1 = Util.null2String(recordSet.getString("sysid"));
/*  848 */         String str2 = Util.null2String(recordSet.getString("sysshortname"));
/*  849 */         List<BrowserTreeNode> list = getWorkflowsByTypeid(str1, paramList2, paramString2, paramString3, (Map<String, String>)null, (Map<String, String>)null);
/*  850 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode(str1, str2, "-1", (list.size() > 0), list);
/*  851 */         browserTreeNode.setType("wftype");
/*  852 */         browserTreeNode.setCanClick(paramBoolean);
/*  853 */         browserTreeNode.setSelected(paramList1.contains(str1));
/*  854 */         arrayList.add(browserTreeNode);
/*      */       } 
/*      */     } 
/*  857 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<BrowserTreeNode> getWorkflowsByTypeid(String paramString1, List<String> paramList, String paramString2, String paramString3, Map<String, String> paramMap1, Map<String, String> paramMap2) {
/*  870 */     WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  871 */     workflowAllComInfo.setTofirstRow();
/*  872 */     ArrayList<String> arrayList = new ArrayList();
/*  873 */     ArrayList<BrowserTreeNode> arrayList1 = new ArrayList();
/*      */     
/*  875 */     if (!"1".equals(paramString3))
/*      */     {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  886 */       while (workflowAllComInfo.next()) {
/*      */         
/*  888 */         String str = workflowAllComInfo.getWorkflowid();
/*  889 */         str = ("".equals(paramMap1.get(str)) || "0".equals(paramMap1.get(str))) ? str : paramMap1.get(str);
/*  890 */         if (paramString1.equals(workflowAllComInfo.getWorkflowtype()) && "1".equals(paramMap2.get(str)) && arrayList.indexOf(str) == -1) {
/*  891 */           arrayList.add(str);
/*  892 */           String str1 = workflowAllComInfo.getWorkflowname(str);
/*  893 */           BrowserTreeNode browserTreeNode = new BrowserTreeNode(str, str1, paramString1, "1".equals(paramString2));
/*  894 */           browserTreeNode.setType("wf");
/*  895 */           browserTreeNode.setCanClick(true);
/*  896 */           browserTreeNode.setSelected(paramList.contains(str));
/*  897 */           arrayList1.add(browserTreeNode);
/*      */         } 
/*      */       } 
/*      */     }
/*  901 */     RequestUtil requestUtil = new RequestUtil();
/*  902 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  903 */     boolean bool = (ofsSettingObject.getIsuse() == 1) ? true : false;
/*  904 */     if (bool && "1".equals(paramString3)) {
/*  905 */       RecordSet recordSet = new RecordSet();
/*  906 */       recordSet.executeQuery("select workflowid,workflowname from ofs_workflow where sysid=?", new Object[] { paramString1 });
/*  907 */       while (recordSet.next()) {
/*  908 */         String str1 = Util.null2String(recordSet.getString("workflowid"));
/*  909 */         String str2 = Util.null2String(recordSet.getString("workflowname"));
/*  910 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode(str1, str2, paramString1, "1".equals(paramString2));
/*      */         
/*  912 */         browserTreeNode.setType("wf");
/*  913 */         browserTreeNode.setCanClick(true);
/*  914 */         browserTreeNode.setSelected(paramList.contains(str1));
/*  915 */         arrayList1.add(browserTreeNode);
/*      */       } 
/*      */     } 
/*  918 */     return arrayList1;
/*      */   }
/*      */   
/*      */   private List<BrowserTreeNode> getWfNodes(String paramString, List<String> paramList) {
/*  922 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*  923 */     String str = "select a.id,a.nodename from workflow_nodebase a left join workflow_flownode b on a.id = b.nodeid  where b.workflowid = ? order by b.nodeorder";
/*  924 */     RecordSet recordSet = new RecordSet();
/*  925 */     recordSet.executeQuery(str, new Object[] { paramString });
/*  926 */     while (recordSet.next()) {
/*  927 */       String str1 = recordSet.getString("id");
/*  928 */       String str2 = recordSet.getString("nodename");
/*  929 */       BrowserTreeNode browserTreeNode = new BrowserTreeNode(str1, str2, paramString, false);
/*  930 */       browserTreeNode.setType("node");
/*  931 */       browserTreeNode.setCanClick(true);
/*  932 */       browserTreeNode.setSelected(paramList.contains(str1));
/*  933 */       arrayList.add(browserTreeNode);
/*      */     } 
/*  935 */     return arrayList;
/*      */   }
/*      */   
/*      */   private List<BrowserTreeNode> getWfTreeNodes(Map<String, Object> paramMap) {
/*  939 */     boolean bool = "1".equals(Util.null2String(paramMap.get("canClickWfType")));
/*  940 */     String str1 = Util.null2String(paramMap.get("typeids"));
/*  941 */     String str2 = Util.null2String(paramMap.get("workflowids"));
/*  942 */     List list1 = Util.splitString2List(str1, ",");
/*  943 */     List list2 = Util.splitString2List(str2, ",");
/*      */     
/*  945 */     String str3 = "select a.id,a.workflowtype,a.workflowname,a.workflowdesc,a.formid,a.isbill,b.typename,a.istemplate ";
/*  946 */     String str4 = "from workflow_base a,workflow_type b ";
/*  947 */     String str5 = getQuerySqlWhere(paramMap);
/*      */ 
/*      */     
/*  950 */     String str6 = Util.null2String(paramMap.get("isOdoc"));
/*  951 */     if (str6.equals("true")) {
/*  952 */       str5 = str5 + " and a.id in ( select c.workflowid from workflow_createdoc c left join workflow_base d on c.workflowid=d.id where c.status=1 and (d.officaltype<1 or d.officaltype='' or d.officaltype is null)) ";
/*      */     }
/*  954 */     String str7 = "order by b.dsporder,a.dsporder,a.id ";
/*  955 */     String str8 = str3 + str4 + str5 + str7;
/*  956 */     RecordSet recordSet = new RecordSet();
/*  957 */     recordSet.executeQuery(str8, new Object[0]);
/*  958 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  959 */     while (recordSet.next()) {
/*  960 */       String str9 = recordSet.getString("workflowtype");
/*  961 */       BrowserTreeNode browserTreeNode1 = (BrowserTreeNode)linkedHashMap.get(str9);
/*  962 */       if (browserTreeNode1 == null) {
/*  963 */         String str = Util.null2String(recordSet.getString("typename"));
/*  964 */         browserTreeNode1 = new BrowserTreeNode(str9, str, "-1", true);
/*  965 */         browserTreeNode1.setType("wftype");
/*  966 */         browserTreeNode1.setCanClick(bool);
/*  967 */         browserTreeNode1.setSelected(list1.contains(str9));
/*  968 */         browserTreeNode1.setSubs(new ArrayList());
/*  969 */         linkedHashMap.put(str9, browserTreeNode1);
/*      */       } 
/*  971 */       List<BrowserTreeNode> list = browserTreeNode1.getSubs();
/*  972 */       String str10 = recordSet.getString("id");
/*  973 */       String str11 = recordSet.getString("workflowname");
/*  974 */       BrowserTreeNode browserTreeNode2 = new BrowserTreeNode(str10, str11, str9, false);
/*  975 */       browserTreeNode2.setType("wf");
/*  976 */       browserTreeNode2.setCanClick(true);
/*  977 */       browserTreeNode2.setSelected(list2.contains(str10));
/*      */       
/*  979 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  980 */       hashMap.put("formid", Integer.valueOf(recordSet.getInt("formid")));
/*  981 */       hashMap.put("isbill", recordSet.getString("isbill"));
/*  982 */       browserTreeNode2.setProp(hashMap);
/*  983 */       list.add(browserTreeNode2);
/*      */     } 
/*  985 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*  986 */     Iterator iterator = linkedHashMap.keySet().iterator();
/*  987 */     while (iterator.hasNext()) {
/*  988 */       arrayList.add(linkedHashMap.get(iterator.next()));
/*      */     }
/*  990 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFormName(String paramString1, String paramString2) throws Exception {
/* 1003 */     FormComInfo formComInfo = new FormComInfo();
/* 1004 */     BillComInfo billComInfo = new BillComInfo();
/* 1005 */     String str1 = "";
/* 1006 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1007 */     String str2 = Util.null2String(arrayOfString[0]);
/* 1008 */     int i = Util.getIntValue(arrayOfString[1], 7);
/* 1009 */     if ("0".equals(str2)) {
/* 1010 */       str1 = formComInfo.getFormname(paramString1);
/* 1011 */     } else if ("1".equals(str2)) {
/* 1012 */       str1 = SystemEnv.getHtmlLabelName(Util.getIntValue(billComInfo.getBillLabel(paramString1)), i);
/*      */     } 
/* 1014 */     str1 = Util.null2String(str1);
/* 1015 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String workflowNameTrans(String paramString1, String paramString2) {
/* 1026 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1027 */     String str = Util.null2String(arrayOfString[0]);
/* 1028 */     int i = Util.getIntValue(arrayOfString[1], 7);
/*      */     
/* 1030 */     WorkflowVersion workflowVersion = new WorkflowVersion(str);
/* 1031 */     int j = workflowVersion.getVersionID();
/* 1032 */     if (!workflowVersion.isActive()) {
/* 1033 */       paramString1 = paramString1 + "<span style=\"color: #999999\"> 【" + SystemEnv.getHtmlLabelName(18500, i) + "V" + j + "】</span>";
/*      */     }
/* 1035 */     return paramString1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String typeNameTrans(String paramString1, String paramString2) {
/* 1045 */     RequestUtil requestUtil = new RequestUtil();
/* 1046 */     boolean bool = (requestUtil.getOfsSetting().getIsuse() == 1) ? true : false;
/* 1047 */     String str = "";
/* 1048 */     if (bool) {
/* 1049 */       String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1050 */       String str1 = Util.null2String(arrayOfString[0]);
/* 1051 */       String str2 = Util.null2String(arrayOfString[1]);
/* 1052 */       String str3 = requestUtil.getOfsSetting().getShowsysname();
/* 1053 */       if (!str1.contains("-")) {
/* 1054 */         if ("1".equals(str3)) {
/* 1055 */           str = requestUtil.getOfsSetting().getOashortname();
/* 1056 */         } else if ("2".equals(str3)) {
/* 1057 */           str = requestUtil.getOfsSetting().getOafullname();
/*      */         } 
/* 1059 */         if (!"".equals(str)) {
/* 1060 */           paramString1 = "[" + Util.formatMultiLang(str, str2) + "]" + Util.formatMultiLang(paramString1, str2);
/*      */         }
/*      */       } else {
/* 1063 */         paramString1 = requestUtil.getSysname(str1, "1");
/* 1064 */         if ("2".equals(str3)) {
/* 1065 */           paramString1 = requestUtil.getSysname(str1, "2");
/*      */         }
/* 1067 */         if (!"".equals(paramString1) && ("1".equals(str3) || "2".equals(str3))) {
/* 1068 */           paramString1 = "[" + Util.formatMultiLang(paramString1, str2) + "]";
/*      */         }
/*      */       } 
/*      */     } 
/* 1072 */     return paramString1;
/*      */   }
/*      */ 
/*      */   
/*      */   public String workflowDescTrans(String paramString) {
/* 1077 */     if (!"".equals(paramString)) {
/* 1078 */       paramString = "<hr style=\"border-width:0;\">" + paramString;
/*      */     }
/* 1080 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String isTemplateTrans(String paramString1, String paramString2) {
/* 1091 */     int i = Util.getIntValue(paramString2);
/* 1092 */     String str = SystemEnv.getHtmlLabelName(34067, i);
/* 1093 */     if ("1".equals(paramString1)) {
/* 1094 */       str = SystemEnv.getHtmlLabelName(33658, i);
/*      */     }
/* 1096 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPathImportRightIds(String paramString) {
/* 1104 */     String str1 = "";
/* 1105 */     String str2 = "select id from workflow_base where 1=1 and (" + Util.getSubINClause(paramString, "subcompanyid", "in") + ") ";
/* 1106 */     RecordSet recordSet1 = new RecordSet();
/* 1107 */     recordSet1.executeQuery(str2, new Object[0]);
/* 1108 */     while (recordSet1.next()) {
/* 1109 */       String str = Util.null2String(recordSet1.getString("id"));
/* 1110 */       if (!str1.equals("")) {
/* 1111 */         str1 = str1 + ",";
/*      */       }
/* 1113 */       str1 = str1 + str;
/*      */     } 
/*      */     
/* 1116 */     RecordSet recordSet2 = new RecordSet();
/* 1117 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1118 */     String str3 = hrmCommonServiceImpl.getRoleIds(this.user.getUID());
/* 1119 */     String str4 = "select id,activeVersionID from workflow_base where id in (select distinct dirid from wfAccessControlList where userid = ? or roleid in (" + str3 + "))";
/* 1120 */     recordSet2.executeQuery(str4, new Object[] { Integer.valueOf(this.user.getUID()) });
/* 1121 */     while (recordSet2.next()) {
/* 1122 */       String str5 = "";
/* 1123 */       String str6 = Util.null2String(recordSet2.getString("id"));
/* 1124 */       String str7 = Util.null2String(recordSet2.getString("activeVersionID"));
/* 1125 */       if (str7.equals("")) {
/* 1126 */         str5 = str6;
/*      */       } else {
/* 1128 */         str5 = str7;
/*      */       } 
/* 1130 */       if (str1.indexOf(str5) == -1) {
/* 1131 */         str1 = str1 + "," + str5;
/*      */       }
/*      */     } 
/*      */     
/* 1135 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMaintainRightIds(String paramString) {
/* 1142 */     String str1 = "select id,activeVersionID from workflow_base where 1=1 and (" + Util.getSubINClause(paramString, "subcompanyid", "in") + ") ";
/* 1143 */     RecordSet recordSet = new RecordSet();
/* 1144 */     recordSet.executeQuery(str1, new Object[0]);
/* 1145 */     String str2 = "";
/* 1146 */     while (recordSet.next()) {
/* 1147 */       String str3 = "";
/* 1148 */       String str4 = Util.null2String(recordSet.getString("id"));
/* 1149 */       String str5 = Util.null2String(recordSet.getString("activeVersionID"));
/* 1150 */       if (str5.equals("")) {
/* 1151 */         str3 = str4;
/*      */       } else {
/* 1153 */         str3 = str5;
/*      */       } 
/* 1155 */       if (!str2.equals("")) {
/* 1156 */         str2 = str2 + ",";
/*      */       }
/* 1158 */       str2 = str2 + str3;
/*      */     } 
/* 1160 */     return str2;
/*      */   }
/*      */   
/*      */   private String getQuerySqlWhere(Map<String, Object> paramMap) {
/* 1164 */     String str1 = Util.null2String(paramMap.get("propertyOfApproveWorkFlow"));
/* 1165 */     String str2 = Util.null2String(paramMap.get("sqlwhere"));
/*      */     
/* 1167 */     String str3 = Util.null2String(paramMap.get("cpttype"));
/* 1168 */     if (!"".equals(str3)) {
/* 1169 */       String str = Util.null2String(paramMap.get("cptwfids"));
/* 1170 */       str2 = "and isbill=1 and exists ( select 1 from workflow_billfield t2 where t2.billid=a.formid and t2.fieldhtmltype=3 and t2.type= " + str3 + " and formid not in(14,18,19,201,220,221,222,224) ) and a.id not in(-1" + str + ")";
/*      */     } 
/*      */ 
/*      */     
/* 1174 */     String str4 = Util.null2String(paramMap.get("workflowname"));
/* 1175 */     int i = Util.getIntValue(Util.null2String(paramMap.get("workflowid")), 0);
/* 1176 */     String str5 = Util.null2String(paramMap.get("workflowdesc"));
/* 1177 */     int j = Util.getIntValue(Util.null2String(paramMap.get("typeid")), 0);
/* 1178 */     boolean bool1 = false;
/* 1179 */     if (1 == Util.getIntValue(Util.null2String(paramMap.get("isReportForm")), 0))
/* 1180 */       bool1 = true; 
/* 1181 */     int k = Util.getIntValue(Util.null2String(paramMap.get("formid")), 0);
/* 1182 */     int m = Util.getIntValue(Util.null2String(paramMap.get("isbill")), -1);
/* 1183 */     String str6 = Util.null2String(paramMap.get("wfRanges"));
/*      */     
/* 1185 */     String str7 = null;
/* 1186 */     if (bool1) {
/* 1187 */       str7 = Util.null2s(Util.null2String(paramMap.get("isvalid")), "1,3");
/*      */     } else {
/* 1189 */       str7 = Util.null2s(Util.null2String(paramMap.get("isvalid")), "1");
/*      */     } 
/* 1191 */     if (str7.length() == 1) {
/* 1192 */       str7 = "'" + str7 + "'";
/*      */     } else {
/* 1194 */       String[] arrayOfString = str7.split(",");
/* 1195 */       str7 = "";
/* 1196 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 1197 */         str7 = str7 + "'" + arrayOfString[b] + "',";
/*      */       }
/* 1199 */       str7 = str7.substring(0, str7.length() - 1);
/*      */     } 
/* 1201 */     String str8 = Util.null2String(paramMap.get("subcompanyid"));
/* 1202 */     String str9 = Util.null2String(paramMap.get("isTemplate"));
/* 1203 */     String str10 = Util.null2String(paramMap.get("templateImport"));
/* 1204 */     String str11 = Util.null2String(paramMap.get("noIncludeWfids"));
/*      */     
/* 1206 */     String str12 = "";
/*      */     
/* 1208 */     if ("1".equals(str10)) {
/* 1209 */       str12 = " where a.workflowtype=b.id ";
/* 1210 */       if ("1".equals(str9)) {
/* 1211 */         str12 = str12 + " and istemplate='1' ";
/* 1212 */       } else if ("2".equals(str9)) {
/* 1213 */         str12 = str12 + " and (istemplate != '1' or istemplate is null) ";
/*      */       } 
/*      */       
/* 1216 */       String str18 = Util.null2String(paramMap.get("formType"));
/* 1217 */       if ("1".equals(str18)) {
/* 1218 */         str12 = str12 + " and ((formid < 0 and isbill = 1) or isbill = 0) ";
/* 1219 */       } else if ("2".equals(str18)) {
/* 1220 */         str12 = str12 + " and formid > 0 and isbill = 1 ";
/*      */       } 
/*      */       
/* 1223 */       String str19 = Util.null2String(paramMap.get("showHistory"));
/*      */       
/* 1225 */       if (!"1".equals(str9) && "2".equals(str19)) {
/* 1226 */         str12 = str12 + " and isvalid != '3' ";
/*      */       }
/*      */     }
/* 1229 */     else if ("1".equals(str9)) {
/* 1230 */       str12 = " where a.workflowtype=b.id and istemplate='1' ";
/*      */     } else {
/* 1232 */       str12 = " where a.workflowtype=b.id and (istemplate != '1' or istemplate is null) and isvalid in(" + str7 + ") ";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1237 */     if ("htmllayoutchoose".equals(Util.null2String(paramMap.get("from")))) {
/* 1238 */       str12 = " where 1=1 ";
/*      */     }
/* 1240 */     if (!str2.equals("")) {
/* 1241 */       str12 = str12 + str2;
/*      */     }
/* 1243 */     if (j != 0) {
/* 1244 */       str12 = str12 + " and workflowtype='" + j + "' ";
/*      */     }
/* 1246 */     if (k != 0 && m != -1) {
/* 1247 */       str12 = str12 + " and formid='" + k + "' and isbill='" + m + "' ";
/*      */     }
/*      */     
/* 1250 */     String str13 = Util.null2String(paramMap.get("isCustomize"));
/* 1251 */     if ("1".equals(str13)) {
/* 1252 */       str12 = str12 + " and formid<0 and isbill=1 ";
/*      */     }
/*      */     
/* 1255 */     if (i > 0 && bool1) {
/* 1256 */       WorkflowVersion workflowVersion = new WorkflowVersion("" + i);
/* 1257 */       List list = workflowVersion.getAllVersionList();
/* 1258 */       if (list.size() > 1) {
/* 1259 */         String str = "";
/* 1260 */         for (Map map : list) {
/* 1261 */           str = str + (String)map.get("id") + ",";
/*      */         }
/* 1263 */         str12 = str12 + " and a.id in ( " + str.substring(0, str.length() - 1) + ")";
/*      */       } else {
/* 1265 */         str12 = str12 + " and a.id = " + i;
/*      */       } 
/*      */     } 
/* 1268 */     if (i > 0 && !bool1) {
/* 1269 */       str12 = str12 + " and a.id  = " + i;
/*      */     }
/*      */     
/* 1272 */     if (!"".equals(str11)) {
/* 1273 */       str12 = str12 + " and a.id not in (" + str11 + ") ";
/*      */     }
/*      */     
/* 1276 */     if (!"".equals(str6)) {
/* 1277 */       String str = WorkflowVersion.getAllVersionStringByWFIDs(str6);
/* 1278 */       if (!"".equals(str))
/* 1279 */         str12 = str12 + " and (" + Util.getSubINClause(str, "a.id", "in") + ") "; 
/*      */     } 
/* 1281 */     if (!"".equals(str4)) {
/* 1282 */       RecordSet recordSet = new RecordSet();
/* 1283 */       if ("oracle".equals(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 1284 */         str12 = str12 + " and upper(workflowname) like '%" + Util.fromScreen2(str4, this.user.getLanguage()).toUpperCase() + "%' ";
/*      */       } else {
/* 1286 */         str12 = str12 + " and workflowname like '%" + Util.fromScreen2(str4, this.user.getLanguage()) + "%' ";
/*      */       } 
/*      */     } 
/* 1289 */     if (!"".equals(str5)) {
/* 1290 */       str12 = str12 + " and workflowdesc like '%" + Util.fromScreen2(str5, this.user.getLanguage()) + "%' ";
/*      */     }
/*      */     
/* 1293 */     if (!"".equals(str8)) {
/* 1294 */       str12 = str12 + " and a.subcompanyid = " + str8;
/*      */     }
/* 1296 */     if ("contract".equals(str1)) {
/* 1297 */       str12 = str12 + " and formid = 49 and isbill = 1 ";
/*      */     }
/*      */ 
/*      */     
/* 1301 */     if ("monitor".equals(Util.null2String(paramMap.get("search")))) {
/* 1302 */       str12 = str12 + MonitorUtil.getMonitorWorkflowSearchSqlWhere(this.user);
/*      */     }
/*      */ 
/*      */     
/* 1306 */     if ("monitorSet".equals(Util.null2String(paramMap.get("search")))) {
/* 1307 */       str12 = str12 + MonitorUtil.getMonitorSetWorkflowSearchSqlWhere(this.user, Util.null2String(paramMap.get("infoid")), "search");
/*      */     }
/*      */     
/* 1310 */     if ("mainSubChoose".equals(Util.null2String(paramMap.get("search")))) {
/* 1311 */       int n = Util.getIntValue((new StringBuilder()).append(paramMap.get("chooseType")).append("").toString());
/* 1312 */       int i1 = Util.getIntValue((new StringBuilder()).append(paramMap.get("basewfid")).append("").toString());
/* 1313 */       MainSubWfSignBiz mainSubWfSignBiz = new MainSubWfSignBiz();
/* 1314 */       str12 = str12 + mainSubWfSignBiz.buildFilterSqlStr("a.id", i1, n);
/*      */     } 
/*      */ 
/*      */     
/* 1318 */     String str14 = Util.null2String(paramMap.get("isEncryptShared"));
/* 1319 */     boolean bool2 = (WfEncryptBiz.isOpenShareEncryptSet("WORKFLOW") && WfEncryptBiz.isOpenShareEncryptDetailSet("WORKFLOW", "SHAREBASE")) ? true : false;
/* 1320 */     if (str14.equals("1") && bool2) {
/* 1321 */       str12 = str12 + " and (isencryptshare ='0' or isencryptshare is null) ";
/*      */     }
/*      */     
/* 1324 */     boolean bool = (new ManageDetachComInfo()).isUseWfManageDetach();
/* 1325 */     String str15 = Util.null2String(paramMap.get("pathImportType"));
/* 1326 */     String str16 = Util.null2String(paramMap.get("isInitLayout"));
/*      */     
/* 1328 */     String str17 = Util.null2String(paramMap.get("wfMaintainRight"));
/*      */     
/*      */     try {
/* 1331 */       if (("pathImport".equals(str15) || "1".equals(str16)) && bool && this.user.getUID() != 1) {
/* 1332 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1333 */         String str = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", 0);
/* 1334 */         str12 = str12 + " and (" + Util.getSubINClause(getPathImportRightIds(str), "a.id", "in") + ") ";
/*      */       } 
/* 1336 */       if ("1".equals(str17) && bool) {
/* 1337 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1338 */         String str = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", 0);
/* 1339 */         str12 = str12 + " and (" + Util.getSubINClause(getMaintainRightIds(str), "a.id", "in") + ") ";
/*      */       } 
/* 1341 */     } catch (Exception exception) {
/* 1342 */       exception.printStackTrace();
/*      */     } 
/* 1344 */     if ("1".equals((new WorkflowConfigComInfo()).getValue("workflow_browserData_detachable")) && !"monitor".equals(Util.null2String(paramMap.get("search"))) && !"monitorSet".equals(Util.null2String(paramMap.get("search")))) {
/* 1345 */       str12 = str12 + getDetachableCondition("a.");
/*      */     }
/* 1347 */     return str12;
/*      */   }
/*      */   
/*      */   private List<Map<String, String>> getWfInfo() {
/* 1351 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1352 */     RecordSet recordSet = new RecordSet();
/* 1353 */     recordSet.executeQuery("select id,activeversionid,isvalid from workflow_base", new Object[0]);
/* 1354 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1355 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1356 */     String str = "";
/* 1357 */     while (recordSet.next()) {
/* 1358 */       str = Util.null2String(recordSet.getString("id"));
/* 1359 */       hashMap1.put(str, Util.null2String(recordSet.getString("activeversionid")));
/* 1360 */       hashMap2.put(str, Util.null2String(recordSet.getString("isvalid")));
/*      */     } 
/* 1362 */     arrayList.add(hashMap1);
/* 1363 */     arrayList.add(hashMap2);
/* 1364 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getBrowserSqlFrom(Map<String, Object> paramMap, String paramString) throws Exception {
/* 1369 */     String str1 = getQuerySqlWhere(paramMap);
/*      */     
/* 1371 */     String str2 = Util.null2String(paramMap.get("isOdoc"));
/* 1372 */     String str3 = Util.null2String(paramMap.get("showSubCompany"));
/* 1373 */     int i = Util.getIntValue(Util.null2String(paramMap.get("formid")).toString(), 0);
/* 1374 */     if (str2.equals("true")) {
/* 1375 */       str1 = str1 + " and a.id in ( select c.workflowid from workflow_createdoc c left join workflow_base d on c.workflowid=d.id where c.status=1 and (d.officaltype<1 or d.officaltype='' or d.officaltype is null)) ";
/*      */     }
/*      */     
/* 1378 */     if ("1".equals(str2)) {
/* 1379 */       str1 = str1 + " and a.officaltype>0 and a.id in( select c.workflowid from workflow_createdoc c where c.status=1 ) ";
/* 1380 */       if (i != 0) {
/* 1381 */         str1 = str1 + " and a.formid=" + i + " ";
/*      */       }
/*      */     } 
/*      */     
/* 1385 */     if ("reportSetting".equals(paramString) && (new ManageDetachComInfo()).isUseWfManageDetach()) {
/*      */       
/* 1387 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1388 */       String str = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowReportManage:All", -1);
/* 1389 */       if (!str.equals("")) {
/* 1390 */         String str4 = Util.getSubINClause(str, "subcompanyid", "in");
/* 1391 */         if (!str4.equals("")) {
/* 1392 */           str1 = str1 + " and " + str4;
/*      */         }
/*      */       } else {
/* 1395 */         str1 = str1 + " and 1=2";
/*      */       } 
/*      */     } 
/* 1398 */     return str1;
/*      */   }
/*      */ 
/*      */   
/*      */   private String handleSelectids(String paramString, Map<String, Object> paramMap) {
/* 1403 */     WorkflowBrowserServiceAssist workflowBrowserServiceAssist = new WorkflowBrowserServiceAssist(paramMap, this.user);
/* 1404 */     String str1 = "";
/* 1405 */     String str2 = Util.null2String(paramMap.get("isTemplate"));
/* 1406 */     String str3 = Util.null2String(paramMap.get("showHistory"));
/* 1407 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfstatus")), 1);
/*      */     try {
/* 1409 */       str1 = getBrowserSqlFrom(paramMap, Util.null2String(paramMap.get("from")));
/* 1410 */     } catch (Exception exception) {
/* 1411 */       exception.printStackTrace();
/*      */     } 
/* 1413 */     if ("".equals(paramString)) {
/* 1414 */       return "";
/*      */     }
/* 1416 */     RecordSet recordSet = new RecordSet();
/* 1417 */     String str4 = "";
/* 1418 */     for (String str : paramString.split(",")) {
/* 1419 */       String[] arrayOfString = str.split("\\|");
/* 1420 */       if ("wftype".equals(arrayOfString[0])) {
/* 1421 */         List<String> list = new ArrayList();
/* 1422 */         if (!arrayOfString[1].contains("-")) {
/* 1423 */           HashMap<String, List<String>> hashMap = workflowBrowserServiceAssist.getWfidAndType(str1 + " and workflowtype = '" + arrayOfString[1] + "'", str2, str3, i);
/* 1424 */           list = hashMap.get("wfids");
/*      */         } else {
/* 1426 */           String str5 = workflowBrowserServiceAssist.getOsWorkflowSqlByParams(Util.null2String(paramMap.get("workflowname")), Util.null2String(paramMap.get("workflowdesc")), arrayOfString[1]);
/* 1427 */           recordSet.executeQuery(str5, new Object[0]);
/* 1428 */           while (recordSet.next()) {
/* 1429 */             list.add(recordSet.getString("workflowid"));
/*      */           }
/*      */         } 
/* 1432 */         for (String str5 : list) {
/* 1433 */           if (!("," + str4 + ",").contains("," + str5 + ",")) {
/* 1434 */             str4 = str4 + "," + str5;
/*      */           }
/*      */         } 
/* 1437 */       } else if ("wf".equals(arrayOfString[0]) && 
/* 1438 */         !("," + str4 + ",").contains("," + arrayOfString[1] + ",")) {
/* 1439 */         str4 = str4 + "," + arrayOfString[1];
/*      */       } 
/*      */     } 
/*      */     
/* 1443 */     if (str4.length() > 0) {
/* 1444 */       str4 = str4.substring(1);
/*      */     }
/* 1446 */     return str4;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getDetachableCondition(String paramString) {
/* 1455 */     String str = "";
/* 1456 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/* 1457 */     if (manageDetachComInfo.isUseWfManageDetach()) {
/*      */       try {
/* 1459 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1460 */         String str1 = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", -1);
/* 1461 */         String str2 = (new WfRightManager()).getAllWfTypeIds(this.user.getUID());
/* 1462 */         String str3 = this.user.getUserSubCompany1() + "";
/*      */         
/* 1464 */         if (!"".equals(str3) && !"".equals(str1)) {
/* 1465 */           str3 = str3 + "," + str1;
/* 1466 */         } else if (!"".equals(str1)) {
/* 1467 */           str3 = str1;
/*      */         } 
/* 1469 */         if ("".equals(str3)) {
/* 1470 */           str3 = "0";
/*      */         }
/*      */         
/* 1473 */         str = str + " and (1=2 ";
/* 1474 */         if (!"".equals(str3)) {
/* 1475 */           str = str + " or " + Util.getSubINClause(str3, paramString + "subcompanyid", "in");
/*      */         }
/* 1477 */         if (!"".equals(str2)) {
/* 1478 */           str = str + " or " + Util.getSubINClause(str2, paramString + "id", "in");
/*      */         }
/* 1480 */         str = str + ")";
/* 1481 */         return str;
/* 1482 */       } catch (Exception exception) {
/* 1483 */         exception.printStackTrace();
/* 1484 */         writeLog("路径浏览按钮分权数据获取：user:" + this.user.getUID() + ";e:" + exception.getMessage());
/* 1485 */         return "";
/*      */       } 
/*      */     }
/* 1488 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */