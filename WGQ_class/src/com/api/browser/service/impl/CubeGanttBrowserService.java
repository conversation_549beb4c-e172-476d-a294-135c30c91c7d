/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.formmode.service.CommonConstant;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CubeGanttBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */     String str1 = Util.null2String(paramMap.get("modeid"));
/* 29 */     String str2 = Util.null2String(paramMap.get("name"));
/* 30 */     String str3 = " where reffield in (select id from workflow_billfield where fielddbtype in (select " + CommonConstant.getConcatSql(new String[] { "'browser.'", "showname" }) + " from mode_browser where customid in (select id from mode_custombrowser where modeid='" + str1 + "'))) ";
/* 31 */     if (!str2.equals("")) {
/* 32 */       str3 = str3 + " and upper(ganttname) like upper('%" + str2 + "%')";
/*    */     }
/* 34 */     String str4 = " id,ganttname ";
/* 35 */     String str5 = " mode_ganttSet ";
/* 36 */     String str6 = " id ";
/* 37 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 38 */     arrayList.add(new SplitTableColBean("30%", "ID", "id", "id"));
/*    */     
/* 40 */     arrayList.add((new SplitTableColBean("70%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "ganttname", "ganttname")).setIsInputCol(BoolAttr.TRUE));
/* 41 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/* 42 */     splitTableBean.setSqlsortway("ASC");
/* 43 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 44 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 49 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 50 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 52 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 195, "name");
/* 53 */     arrayList.add(searchConditionItem);
/* 54 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 55 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CubeGanttBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */