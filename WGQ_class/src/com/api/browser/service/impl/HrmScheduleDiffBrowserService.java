/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleDiffBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 29 */     String str1 = Util.null2String(paramMap.get("diffname"));
/* 30 */     String str2 = Util.null2String(paramMap.get("diffdesc"));
/* 31 */     String str3 = Util.null2String(paramMap.get("workflowid"));
/* 32 */     String str4 = Util.null2String(paramMap.get("salaryitem"));
/* 33 */     String str5 = Util.null2String(paramMap.get("difftype"));
/* 34 */     String str6 = Util.null2String(paramMap.get("difftime"));
/* 35 */     String str7 = Util.null2String(paramMap.get("salaryable"));
/* 36 */     String str8 = Util.null2String(paramMap.get("counttype"));
/*    */     
/* 38 */     String str9 = " ";
/* 39 */     if (!str1.equals("")) {
/* 40 */       str9 = str9 + " and diffname like '%";
/* 41 */       str9 = str9 + Util.fromScreen2(str1, this.user.getLanguage());
/* 42 */       str9 = str9 + "%'";
/*    */     } 
/* 44 */     if (!str2.equals("")) {
/* 45 */       str9 = str9 + " and diffdesc like '%";
/* 46 */       str9 = str9 + Util.fromScreen2(str2, this.user.getLanguage());
/* 47 */       str9 = str9 + "%'";
/*    */     } 
/* 49 */     if (!str3.equals("") && !str3.equals("0")) {
/* 50 */       str9 = str9 + " and workflowid =";
/* 51 */       str9 = str9 + Util.fromScreen2(str3, this.user.getLanguage());
/* 52 */       str9 = str9 + " ";
/*    */     } 
/* 54 */     if (!str4.equals("")) {
/* 55 */       str9 = str9 + " and salaryitem =";
/* 56 */       str9 = str9 + Util.fromScreen2(str4, this.user.getLanguage());
/* 57 */       str9 = str9 + " ";
/*    */     } 
/* 59 */     if (!str5.equals("")) {
/* 60 */       str9 = str9 + " and difftype =" + Util.fromScreen2(str5, this.user.getLanguage());
/*    */     }
/* 62 */     if (!str6.equals("")) {
/* 63 */       str9 = str9 + " and difftime =" + Util.fromScreen2(str6, this.user.getLanguage());
/*    */     }
/* 65 */     if (!str7.equals("")) {
/* 66 */       str9 = str9 + " and salaryable =" + Util.fromScreen2(str7, this.user.getLanguage());
/*    */     }
/*    */     
/* 69 */     if (!str8.equals("")) {
/* 70 */       str9 = str9 + " and counttype =" + Util.fromScreen2(str8, this.user.getLanguage());
/*    */     }
/*    */ 
/*    */     
/* 74 */     String str10 = " id ,diffname ";
/* 75 */     String str11 = " HrmScheduleDiff ";
/*    */     
/* 77 */     str9 = SqlUtils.replaceFirstAnd(str9);
/* 78 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 79 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 80 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "diffname", "diffname")).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 82 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, str9, "id", "id", arrayList);
/* 83 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 84 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmScheduleDiffBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */