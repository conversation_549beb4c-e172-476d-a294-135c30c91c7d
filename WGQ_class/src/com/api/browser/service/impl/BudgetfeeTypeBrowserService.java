/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.formmode.cache.ModeComInfo;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import com.engine.fnaMulDimensions.entity.FnaBrowserTreeNodeBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.maintenance.BudgetfeeTypeComInfo;
/*     */ import weaver.fna.maintenance.FnaSystemSetComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BudgetfeeTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*  57 */   public String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"firstName\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"secondName\"                    }                 ],                \"key\": \"col1_row2\"            },            {                \"configs\": [                    {                        \"key\": \"thirdName\"                    }                 ],                \"key\": \"col1_row3\"            }        ],        \"key\": \"col1\"    }]";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 100 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 102 */     if (this.user == null) {
/* 103 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 104 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*     */     try {
/* 108 */       putData(paramMap, (Map)hashMap);
/* 109 */     } catch (Exception exception) {
/* 110 */       (new BaseBean()).writeLog(exception);
/*     */     } 
/*     */     
/* 113 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 125 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 127 */     if (this.user == null) {
/* 128 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 129 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 132 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 133 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 135 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1462, "name", true));
/* 136 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*     */     
/* 138 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void putData(Map<String, Object> paramMap1, Map<String, Object> paramMap2) throws Exception {
/* 151 */     RecordSet recordSet1 = new RecordSet();
/* 152 */     RecordSet recordSet2 = new RecordSet();
/* 153 */     RecordSet recordSet3 = new RecordSet();
/* 154 */     Integer integer = Integer.valueOf(Util.getIntValue((String)paramMap1.get("ismobile")));
/* 155 */     int i = Util.getIntValue(Util.null2String(paramMap1.get("list")), 0);
/* 156 */     String str1 = Util.null2String(paramMap1.get("displayarchive"));
/*     */     
/* 158 */     int j = Util.getIntValue(Util.null2String(paramMap1.get("orgType")), -1);
/* 159 */     int k = Util.getIntValue(Util.null2String(paramMap1.get("orgId")), -1);
/* 160 */     int m = Util.getIntValue(Util.null2String(paramMap1.get("wfid")), -1);
/* 161 */     String str2 = Util.null2String(paramMap1.get("isFromMode"));
/* 162 */     int n = Util.getIntValue(Util.null2String(paramMap1.get("modeId")), -1);
/* 163 */     if ("1".equals(str2) && n > -1) {
/* 164 */       m = -1 * n;
/*     */     }
/* 166 */     int i1 = Util.getIntValue((String)paramMap1.get("fieldid"), -1);
/* 167 */     int i2 = Util.getIntValue((String)paramMap1.get("billid"), -1);
/* 168 */     int i3 = Util.getIntValue(Util.null2String(paramMap1.get("orgType2")), -1);
/* 169 */     int i4 = Util.getIntValue(Util.null2String(paramMap1.get("orgId2")), -1);
/* 170 */     String str3 = Util.null2String(paramMap1.get("occurdate"));
/* 171 */     short s1 = -1;
/* 172 */     short s2 = -1;
/*     */     
/* 174 */     if (j == 0) {
/* 175 */       s1 = 3;
/* 176 */     } else if (j == 1) {
/* 177 */       s1 = 2;
/* 178 */     } else if (j == 2) {
/* 179 */       s1 = 1;
/* 180 */     } else if (j == 3) {
/* 181 */       s1 = 18004;
/*     */     } 
/*     */     
/* 184 */     if (i3 == 0) {
/* 185 */       s2 = 3;
/* 186 */     } else if (i3 == 1) {
/* 187 */       s2 = 2;
/* 188 */     } else if (i3 == 2) {
/* 189 */       s2 = 1;
/* 190 */     } else if (i3 == 3) {
/* 191 */       s2 = 18004;
/*     */     } 
/*     */     
/* 194 */     FnaSystemSetComInfo fnaSystemSetComInfo = new FnaSystemSetComInfo();
/* 195 */     int i5 = Util.getIntValue(fnaSystemSetComInfo.get_enableDispalyAll());
/* 196 */     String str4 = Util.null2String(fnaSystemSetComInfo.get_separator()).trim();
/* 197 */     boolean bool1 = (1 == Util.getIntValue(fnaSystemSetComInfo.get_subjectFilter())) ? true : false;
/* 198 */     int i6 = Util.getIntValue(fnaSystemSetComInfo.get_optionalSubject(), 0);
/*     */     
/* 200 */     int i7 = 0;
/* 201 */     int i8 = 0;
/* 202 */     if ("1".equals(str2)) {
/* 203 */       ModeComInfo modeComInfo = new ModeComInfo();
/* 204 */       i8 = Util.getIntValue(modeComInfo.getFormId(String.valueOf(n)));
/*     */     }
/* 206 */     else if (m > 0) {
/* 207 */       recordSet1.executeQuery("select * from workflow_base where id = " + m, new Object[0]);
/* 208 */       if (recordSet1.next()) {
/* 209 */         i7 = recordSet1.getInt("isbill");
/* 210 */         i8 = recordSet1.getInt("formid");
/*     */       } 
/*     */     } 
/*     */     
/* 214 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 215 */     String str5 = FnaCommon.getFnaWfFieldInfo4Expense(m, hashMap);
/*     */     
/* 217 */     byte b = -1;
/* 218 */     if ("fnaFeeWf".equals(str5) || "change".equals(str5) || "share".equals(str5)) {
/* 219 */       b = 1;
/*     */     }
/*     */     
/* 222 */     BudgetfeeTypeComInfo budgetfeeTypeComInfo = new BudgetfeeTypeComInfo();
/* 223 */     if ("1".equals(str2)) {
/*     */       
/* 225 */       if (n != -1 && i1 == -1 && i2 != -1) {
/* 226 */         recordSet1.executeQuery("select * from workflow_billfield where (fieldname = 'subject' or fieldname = 'feetypeid') and billid = ? ", new Object[] { Integer.valueOf(i2) });
/* 227 */         if (recordSet1.next()) {
/* 228 */           i1 = recordSet1.getInt("id");
/*     */         }
/*     */       }
/*     */     
/*     */     }
/* 233 */     else if (m != -1 && i1 == -1 && i2 != -1) {
/* 234 */       recordSet1.executeQuery("select * from workflow_billfield where (fieldname = 'subject' or fieldname = 'feetypeid') and billid = ? ", new Object[] { Integer.valueOf(i2) });
/* 235 */       if (recordSet1.next()) {
/* 236 */         i1 = recordSet1.getInt("id");
/*     */       }
/*     */     } 
/*     */     
/* 240 */     boolean bool2 = false;
/* 241 */     if (m > 0) {
/* 242 */       String str = "select count(*) cnt from fnaFeeWfInfo where workflowid = " + m;
/* 243 */       recordSet3.executeQuery(str, new Object[0]);
/* 244 */       bool2 = (recordSet3.next() && recordSet3.getInt("cnt") > 0) ? true : false;
/*     */     } 
/*     */ 
/*     */     
/* 248 */     ArrayList arrayList1 = new ArrayList();
/* 249 */     ArrayList arrayList2 = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 253 */     budgetfeeTypeComInfo.getWfBrowdefListByFeelevel(m + "", i1 + "", "22", arrayList1);
/* 254 */     boolean bool3 = true;
/* 255 */     if (arrayList1.size() == 0) {
/* 256 */       bool3 = false;
/*     */     }
/* 258 */     if (bool3) {
/* 259 */       budgetfeeTypeComInfo.getWfBrowdefListByFeelevel_canSelect(m + "", i1 + "", "22", arrayList2);
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 277 */     short s3 = s1;
/* 278 */     int i9 = k;
/* 279 */     if (s1 == 3) {
/* 280 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 281 */       s3 = 2;
/* 282 */       i9 = Util.getIntValue(resourceComInfo.getDepartmentID(k + ""));
/*     */     } 
/*     */     
/* 285 */     short s4 = s2;
/* 286 */     int i10 = i4;
/* 287 */     if (s2 == 3) {
/* 288 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 289 */       s4 = 2;
/* 290 */       i10 = Util.getIntValue(resourceComInfo.getDepartmentID(i4 + ""));
/*     */     } 
/*     */     
/* 293 */     boolean bool4 = false;
/* 294 */     if (s1 == -1 && k == -1 && "".equals(str3)) {
/* 295 */       bool4 = true;
/*     */     }
/* 297 */     int i11 = Util.getIntValue(fnaSystemSetComInfo.get_budgetAccountFilter(), 0);
/* 298 */     boolean bool5 = (i11 == 1) ? true : false;
/* 299 */     List list = new ArrayList();
/* 300 */     if (bool5 && ("fnaFeeWf".equals(str5) || "share".equals(str5))) {
/* 301 */       list = budgetfeeTypeComInfo.getBudgeted_Subjects(s1, k, str3);
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 309 */     ArrayList<String> arrayList = new ArrayList();
/* 310 */     recordSet1.executeQuery(" select fieldName from FnaBrowserSetting where browserType = 22 and fieldEnable = 1 order by displayOrder ", new Object[0]);
/* 311 */     while (recordSet1.next()) {
/* 312 */       String str = Util.null2String(recordSet1.getString("fieldName"));
/* 313 */       arrayList.add(str);
/*     */     } 
/*     */     
/* 316 */     if (i == 1) {
/*     */       
/* 318 */       String str6 = Util.null2String(paramMap1.get("name"));
/*     */       
/* 320 */       String str7 = " 1=1 ";
/* 321 */       if ("change".equals(str5) || (i7 == 1 && i8 == 159)) {
/* 322 */         str7 = " a.isEditFeeType = 1 ";
/*     */       }
/* 324 */       else if (i6 == 1) {
/* 325 */         str7 = " not EXISTS (select 1 from FnaBudgetfeeType a1 where a1.supsubject = a.id) ";
/*     */       } 
/*     */ 
/*     */       
/* 329 */       String str8 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 335 */       str8 = "select a.id, a.name, a.tipName, a.codeName, a.codeName2, a.subjectNote, a.description, b.orderId, a.feeperiod, a.Archive, a.isEditFeeType, a.isEditFeeTypeId, a.supsubject  from FnaBudgetfeeType a  join FnaBudgetfeeTypeUsed b on a.id = b.subjectId  where " + str7 + " and a.isEditFeeTypeId > 0  and b.userId = " + this.user.getUID() + "  ORDER BY a.feeperiod, b.orderId desc, a.feelevel, a.displayOrder, a.codeName, a.name, a.id ";
/*     */ 
/*     */       
/* 338 */       if (!"".equals(str6))
/*     */       {
/*     */         
/* 341 */         str8 = "select a.id, a.name, a.tipName, a.codeName, a.codeName2, a.subjectNote, a.description, a.feelevel, a.feeperiod, a.Archive, a.isEditFeeType, a.isEditFeeTypeId, a.supsubject from FnaBudgetfeeType a  where " + str7 + " and (a.codeName like '%" + StringEscapeUtils.escapeSql(str6) + "%' or a.name like '%" + StringEscapeUtils.escapeSql(str6) + "%')  and a.isEditFeeTypeId > 0  ORDER BY a.feelevel, a.displayOrder, a.codeName, a.name, a.id ";
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 349 */       recordSet2.executeQuery(str8, new Object[0]);
/*     */       
/* 351 */       ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/* 352 */       StringBuffer stringBuffer = new StringBuffer("");
/* 353 */       while (recordSet2.next()) {
/* 354 */         String str9 = Util.null2String(recordSet2.getString("id"));
/* 355 */         String str10 = Util.null2String(recordSet2.getString("name"));
/* 356 */         String str11 = Util.null2String(recordSet2.getString("codeName"));
/* 357 */         String str12 = Util.null2String(recordSet2.getString("codeName2"));
/* 358 */         String str13 = Util.null2String(recordSet2.getString("subjectNote"));
/* 359 */         String str14 = Util.null2String(recordSet2.getString("description"));
/* 360 */         String str15 = Util.null2String(recordSet2.getString("supsubject"));
/* 361 */         boolean bool = (recordSet2.getInt("Archive") == 1) ? true : false;
/* 362 */         int i12 = Util.getIntValue(recordSet2.getString("isEditFeeType"));
/*     */ 
/*     */         
/* 365 */         if (bool && !"1".equals(str1)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 370 */         if (bool3 && !arrayList2.contains(str9)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 375 */         if (("change".equals(str5) || (i7 == 1 && i8 == 159)) && i12 != 1) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 380 */         if (!bool4 && 
/* 381 */           bool5 && ("fnaFeeWf".equals(str5) || "share".equals(str5)) && 
/*     */           
/* 383 */           !list.contains(str9)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */ 
/*     */         
/* 389 */         if (bool2 && b == 1 && bool1 && s3 > 0 && i9 > 0) {
/* 390 */           boolean bool6 = budgetfeeTypeComInfo.checkRuleSetRight(s3, i9, Util.getIntValue(str9));
/* 391 */           if (!bool6) {
/*     */             continue;
/*     */           }
/*     */         } 
/*     */         
/* 396 */         if (bool2 && b == 1 && bool1 && s4 > 0 && i10 > 0) {
/* 397 */           boolean bool6 = budgetfeeTypeComInfo.checkRuleSetRight(s4, i10, Util.getIntValue(str9));
/* 398 */           if (!bool6) {
/*     */             continue;
/*     */           }
/*     */         } 
/*     */         
/* 403 */         String str16 = str10;
/* 404 */         String str17 = str10;
/* 405 */         if (i5 == 1) {
/* 406 */           str16 = budgetfeeTypeComInfo.getSubjectFullName(str9, str4);
/*     */         }
/* 408 */         if ("".equals(str15.trim())) {
/* 409 */           str17 = "";
/*     */         } else {
/* 411 */           str17 = budgetfeeTypeComInfo.getSubjectFullName(str15, str4);
/*     */         } 
/*     */ 
/*     */         
/* 415 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 416 */         hashMap1.put("id", str9);
/* 417 */         hashMap1.put("name", str10);
/* 418 */         hashMap1.put("codeName", str11);
/* 419 */         hashMap1.put("codeName2", str12);
/* 420 */         hashMap1.put("subjectNote", str13);
/* 421 */         hashMap1.put("description", str14);
/* 422 */         hashMap1.put("fullName", str16);
/* 423 */         hashMap1.put("highName", str17);
/* 424 */         stringBuffer.append(str9 + ",");
/* 425 */         arrayList3.add(hashMap1);
/*     */       } 
/*     */       
/* 428 */       if (integer.intValue() == 1) {
/*     */         
/* 430 */         stringBuffer.append("-1");
/* 431 */         String str9 = " a.id,a.name,a.codeName,a.codeName2,a.subjectNote,a.supsubject,a.description ";
/* 432 */         String str10 = "FnaBudgetfeeType a  ";
/* 433 */         String str11 = "where a.id in(" + StringEscapeUtils.escapeSql(stringBuffer.toString()) + ") ";
/* 434 */         String str12 = " a.id ";
/* 435 */         ArrayList<SplitTableColBean> arrayList4 = new ArrayList();
/* 436 */         arrayList4.add((new SplitTableColBean("true", "id")).setBelong(BelongAttr.PCMOBILE));
/* 437 */         arrayList4.add((new SplitTableColBean("true", "name")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE));
/*     */         
/* 439 */         for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/* 440 */           String str13 = arrayList.get(b1);
/* 441 */           SplitTableColBean splitTableColBean = new SplitTableColBean();
/* 442 */           String str14 = "";
/* 443 */           if ("subjectName".equals(str13)) {
/*     */             
/* 445 */             splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15409, this.user.getLanguage()), "name", "name", "weaver.fna.general.FnaSplitPageTransmethod.getSubjectNameforMobile", this.user.getLanguage() + "+" + b1, 1);
/* 446 */             str14 = "name";
/* 447 */           } else if ("subjectCode".equals(str13)) {
/*     */             
/* 449 */             splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(21108, this.user.getLanguage()), "codeName", "codeName", "weaver.fna.general.FnaSplitPageTransmethod.getCodeNameforMobile", this.user.getLanguage() + "+" + b1, 1);
/* 450 */             str14 = "codeName";
/* 451 */           } else if ("accountingCode".equals(str13)) {
/*     */             
/* 453 */             splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(132177, this.user.getLanguage()), "codeName2", "codeName2", "weaver.fna.general.FnaSplitPageTransmethod.getCodeName2forMobile", this.user.getLanguage() + "+" + b1, 1);
/* 454 */             str14 = "codeName2";
/* 455 */           } else if ("note".equals(str13)) {
/*     */             
/* 457 */             splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(454, this.user.getLanguage()), "subjectNote", "subjectNote", "weaver.fna.general.FnaSplitPageTransmethod.getSubjectNoteforMobile", this.user.getLanguage() + "+" + b1, 1);
/* 458 */             str14 = "subjectNote";
/* 459 */           } else if ("higherSubjects".equals(str13)) {
/*     */             
/* 461 */             splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18428, this.user.getLanguage()), "supsubject", "supsubject", "weaver.fna.general.FnaSplitPageTransmethod.getSupsubjectNameforMobile", this.user.getLanguage() + "+" + b1, 1);
/* 462 */             str14 = "supsubject";
/* 463 */           } else if ("description".equals(str13)) {
/*     */             
/* 465 */             splitTableColBean = new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description", "weaver.fna.general.FnaSplitPageTransmethod.getDescriptionforMobile", this.user.getLanguage() + "+" + b1, 1);
/* 466 */             str14 = "description";
/*     */           } 
/*     */           
/* 469 */           splitTableColBean.setBelong(BelongAttr.PCMOBILE);
/* 470 */           if (b1 != 0) {
/* 471 */             splitTableColBean.setMobileviewtype(MobileViewTypeAttr.DETAIL);
/*     */           }
/* 473 */           arrayList4.add(splitTableColBean);
/*     */           
/* 475 */           if (b1 == 0) {
/* 476 */             this.JSON_CONFIG = this.JSON_CONFIG.replace("firstName", str14);
/* 477 */           } else if (b1 == 1) {
/* 478 */             this.JSON_CONFIG = this.JSON_CONFIG.replace("secondName", str14);
/* 479 */           } else if (b1 == 2) {
/* 480 */             this.JSON_CONFIG = this.JSON_CONFIG.replace("thirdName", str14);
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 486 */         SplitTableBean splitTableBean = new SplitTableBean(str9, str10, str11, str12, "id", arrayList4);
/* 487 */         splitTableBean.setSqlisdistinct("true");
/*     */         
/* 489 */         List list1 = Util_MobileData.createList(this.JSON_CONFIG);
/* 490 */         SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createJsonTemplateBean("theme_default", list1);
/*     */         
/* 492 */         splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/* 493 */         splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/*     */         
/* 495 */         paramMap2.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */       } else {
/* 497 */         ArrayList<ListHeadBean> arrayList4 = new ArrayList();
/* 498 */         arrayList4.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 499 */         arrayList4.add((new ListHeadBean("fullName", BoolAttr.TRUE)).setIsInputCol(BoolAttr.TRUE));
/*     */         
/* 501 */         for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/* 502 */           String str = arrayList.get(b1);
/* 503 */           if ("subjectName".equals(str)) {
/* 504 */             arrayList4.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(15409, this.user.getLanguage()), 1));
/* 505 */           } else if ("subjectCode".equals(str)) {
/* 506 */             arrayList4.add(new ListHeadBean("codeName", SystemEnv.getHtmlLabelName(21108, this.user.getLanguage())));
/* 507 */           } else if ("accountingCode".equals(str)) {
/* 508 */             arrayList4.add(new ListHeadBean("codeName2", SystemEnv.getHtmlLabelName(132177, this.user.getLanguage())));
/* 509 */           } else if ("note".equals(str)) {
/* 510 */             arrayList4.add(new ListHeadBean("subjectNote", SystemEnv.getHtmlLabelName(454, this.user.getLanguage())));
/* 511 */           } else if ("higherSubjects".equals(str)) {
/* 512 */             arrayList4.add(new ListHeadBean("highName", SystemEnv.getHtmlLabelName(18428, this.user.getLanguage())));
/* 513 */           } else if ("description".equals(str)) {
/* 514 */             arrayList4.add(new ListHeadBean("description", SystemEnv.getHtmlLabelName(433, this.user.getLanguage())));
/*     */           } 
/*     */         } 
/*     */         
/* 518 */         paramMap2.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList4);
/* 519 */         paramMap2.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList3);
/* 520 */         paramMap2.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */       }
/*     */     
/*     */     } else {
/*     */       
/* 525 */       ArrayList<FnaBrowserTreeNodeBean> arrayList3 = new ArrayList();
/*     */       
/* 527 */       String str6 = Util.null2String(paramMap1.get("id"));
/*     */       
/* 529 */       if ("".equals(str6)) {
/* 530 */         str6 = "0";
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 536 */       int i12 = Util.getIntValue(str6);
/*     */       
/* 538 */       String str7 = "select a.id, a.name, a.tipName, a.codeName, a.codeName2, a.subjectNote, a.description, a.feelevel, a.feeperiod, a.Archive, a.isEditFeeType, a.isEditFeeTypeId from FnaBudgetfeeType a  where a.supsubject = " + i12 + "  ORDER BY a.feeperiod,a.displayOrder,a.codeName, a.name, a.id ";
/*     */ 
/*     */ 
/*     */       
/* 542 */       recordSet2.executeQuery(str7, new Object[0]);
/* 543 */       while (recordSet2.next()) {
/* 544 */         String str8 = recordSet2.getString("id");
/* 545 */         String str9 = recordSet2.getString("name");
/* 546 */         String str10 = Util.null2String(recordSet2.getString("tipName"));
/* 547 */         int i13 = Util.getIntValue(recordSet2.getString("feelevel"));
/* 548 */         boolean bool6 = (recordSet2.getInt("Archive") == 1) ? true : false;
/* 549 */         int i14 = Util.getIntValue(recordSet2.getString("isEditFeeTypeId"));
/* 550 */         int i15 = Util.getIntValue(recordSet2.getString("isEditFeeType"));
/* 551 */         String str11 = Util.null2String(recordSet2.getString("codeName"));
/* 552 */         String str12 = Util.null2String(recordSet2.getString("codeName2"));
/* 553 */         String str13 = Util.null2String(recordSet2.getString("subjectNote"));
/* 554 */         String str14 = Util.null2String(recordSet2.getString("description"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 560 */         if (bool6 && !"1".equals(str1)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 565 */         if (bool3 && !arrayList1.contains(str8)) {
/*     */           continue;
/*     */         }
/*     */         
/* 569 */         boolean bool7 = (i14 > 0) ? true : false;
/* 570 */         if (bool3 && !arrayList2.contains(str8)) {
/* 571 */           bool7 = false;
/*     */         }
/*     */         
/* 574 */         if (bool7 == true)
/*     */         {
/* 576 */           if ("change".equals(str5) || (i7 == 1 && i8 == 159)) {
/* 577 */             if (i15 != 1) {
/* 578 */               bool7 = false;
/*     */             }
/* 580 */             if (i14 > 0 && Util.getIntValue(str8) != i14) {
/*     */               continue;
/*     */             }
/*     */           } 
/*     */         }
/*     */ 
/*     */ 
/*     */         
/* 588 */         if (bool2 && b == 1 && bool1 && s3 > 0 && i9 > 0) {
/* 589 */           boolean bool = budgetfeeTypeComInfo.checkRuleSetRight(s3, i9, Util.getIntValue(str8));
/* 590 */           if (!bool) {
/*     */             continue;
/*     */           }
/*     */         } 
/*     */         
/* 595 */         if (bool2 && b == 1 && bool1 && s4 > 0 && i10 > 0) {
/* 596 */           boolean bool = budgetfeeTypeComInfo.checkRuleSetRight(s4, i10, Util.getIntValue(str8));
/* 597 */           if (!bool) {
/*     */             continue;
/*     */           }
/*     */         } 
/*     */ 
/*     */         
/* 603 */         if (!bool4 && 
/* 604 */           bool5 && ("fnaFeeWf".equals(str5) || "share".equals(str5)) && 
/*     */           
/* 606 */           !list.contains(str8)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 614 */         String str15 = str9;
/* 615 */         if (i5 == 1) {
/* 616 */           str15 = budgetfeeTypeComInfo.getSubjectFullName(str8, str4);
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 624 */         boolean bool8 = true;
/* 625 */         if (("change".equals(str5) || (i7 == 1 && i8 == 159)) && bool7 == true) {
/*     */           
/* 627 */           bool8 = false;
/*     */         } else {
/* 629 */           String str = "select count(*) cnt from FnaBudgetfeeType a where a.supsubject = " + Util.getIntValue(str8);
/* 630 */           recordSet3.executeQuery(str, new Object[0]);
/* 631 */           if (recordSet3.next() && recordSet3.getInt("cnt") > 0) {
/* 632 */             bool8 = true;
/* 633 */             if (i6 == 1) {
/* 634 */               bool7 = false;
/*     */             }
/*     */           } else {
/* 637 */             bool8 = false;
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 643 */         FnaBrowserTreeNodeBean fnaBrowserTreeNodeBean = new FnaBrowserTreeNodeBean();
/* 644 */         fnaBrowserTreeNodeBean.setId(str8);
/* 645 */         fnaBrowserTreeNodeBean.setName(str9);
/* 646 */         fnaBrowserTreeNodeBean.setPid(str6);
/* 647 */         fnaBrowserTreeNodeBean.setParent(bool8);
/* 648 */         fnaBrowserTreeNodeBean.setType(String.valueOf(i13));
/* 649 */         fnaBrowserTreeNodeBean.setCanClick((bool7 == true));
/* 650 */         fnaBrowserTreeNodeBean.setFullName(str15);
/* 651 */         fnaBrowserTreeNodeBean.setOrgWholePathspan(str15);
/*     */ 
/*     */ 
/*     */         
/* 655 */         ArrayList<String> arrayList4 = new ArrayList();
/* 656 */         String str16 = "";
/* 657 */         for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/* 658 */           String str = arrayList.get(b1);
/*     */           
/* 660 */           if ("subjectName".equals(str)) {
/* 661 */             if (!"".equals(str9)) {
/* 662 */               if (b1 != 0) {
/* 663 */                 String str17 = SystemEnv.getHtmlLabelName(15409, this.user.getLanguage()) + ":" + str9;
/* 664 */                 str16 = str16 + str17 + " ";
/* 665 */                 fnaBrowserTreeNodeBean.setSubjectName(str17);
/*     */               } else {
/* 667 */                 fnaBrowserTreeNodeBean.setSubjectName(str9);
/*     */               } 
/*     */             }
/* 670 */           } else if ("subjectCode".equals(str)) {
/* 671 */             if (!"".equals(str11)) {
/* 672 */               if (b1 != 0) {
/* 673 */                 String str17 = SystemEnv.getHtmlLabelName(21108, this.user.getLanguage()) + ":" + str11;
/* 674 */                 str16 = str16 + str17 + " ";
/* 675 */                 fnaBrowserTreeNodeBean.setSubjectCode(str17);
/*     */               } else {
/* 677 */                 fnaBrowserTreeNodeBean.setSubjectCode(str11);
/*     */               } 
/*     */             }
/* 680 */           } else if ("accountingCode".equals(str)) {
/* 681 */             if (!"".equals(str12)) {
/* 682 */               if (b1 != 0) {
/* 683 */                 String str17 = SystemEnv.getHtmlLabelName(132177, this.user.getLanguage()) + ":" + str12;
/* 684 */                 str16 = str16 + str17 + " ";
/* 685 */                 fnaBrowserTreeNodeBean.setAccountingCode(str17);
/*     */               } else {
/* 687 */                 fnaBrowserTreeNodeBean.setAccountingCode(str12);
/*     */               } 
/*     */             }
/* 690 */           } else if ("note".equals(str)) {
/* 691 */             if (!"".equals(str13)) {
/* 692 */               if (b1 != 0) {
/* 693 */                 String str17 = SystemEnv.getHtmlLabelName(454, this.user.getLanguage()) + ":" + str13;
/* 694 */                 str16 = str16 + str17 + " ";
/* 695 */                 fnaBrowserTreeNodeBean.setNote(str17);
/*     */               } else {
/* 697 */                 fnaBrowserTreeNodeBean.setNote(str13);
/*     */               } 
/*     */             }
/* 700 */           } else if ("description".equals(str) && 
/* 701 */             !"".equals(str14)) {
/* 702 */             if (b1 != 0) {
/* 703 */               String str17 = SystemEnv.getHtmlLabelName(433, this.user.getLanguage()) + ":" + str14;
/* 704 */               str16 = str16 + str17 + " ";
/* 705 */               fnaBrowserTreeNodeBean.setDescription(str17);
/*     */             } else {
/* 707 */               fnaBrowserTreeNodeBean.setDescription(str14);
/*     */             } 
/*     */           } 
/*     */ 
/*     */           
/* 712 */           if (!"higherSubjects".equals(str)) {
/* 713 */             arrayList4.add(str);
/*     */           }
/*     */         } 
/*     */         
/* 717 */         fnaBrowserTreeNodeBean.setDisplayKeys(arrayList4);
/* 718 */         if ("".equals(str10)) {
/* 719 */           str10 = str16;
/*     */         }
/* 721 */         fnaBrowserTreeNodeBean.setTitle(str10);
/*     */ 
/*     */         
/* 724 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 725 */         hashMap1.put("fullName", str15);
/* 726 */         hashMap1.put("isEditFeeTypeId", Integer.valueOf(i14));
/* 727 */         fnaBrowserTreeNodeBean.setProp(hashMap1);
/*     */         
/* 729 */         arrayList3.add(fnaBrowserTreeNodeBean);
/*     */       } 
/* 731 */       paramMap2.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/* 732 */       paramMap2.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList3);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/BudgetfeeTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */