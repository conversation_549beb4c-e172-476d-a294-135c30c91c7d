/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.train.TrainLayoutComInfo;
/*     */ import weaver.hrm.train.TrainPlanComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmTrainPlanBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  36 */     String str1 = Util.null2String(paramMap.get("planname"));
/*  37 */     String str2 = Util.null2String(paramMap.get("layoutid"));
/*  38 */     String str3 = Util.null2String(paramMap.get("planaim"));
/*  39 */     String str4 = Util.null2String(paramMap.get("plancontent"));
/*  40 */     String str5 = " ";
/*  41 */     if (!str1.equals("")) {
/*  42 */       str5 = str5 + " and planname like '%";
/*  43 */       str5 = str5 + Util.fromScreen2(str1, this.user.getLanguage());
/*  44 */       str5 = str5 + "%'";
/*     */     } 
/*  46 */     if (!str2.equals("")) {
/*  47 */       str5 = str5 + " and layoutid =";
/*  48 */       str5 = str5 + Util.fromScreen2(str2, this.user.getLanguage());
/*  49 */       str5 = str5 + " ";
/*     */     } 
/*  51 */     if (!str3.equals("")) {
/*  52 */       str5 = str5 + " and planaim like '%";
/*  53 */       str5 = str5 + Util.fromScreen2(str3, this.user.getLanguage());
/*  54 */       str5 = str5 + "%'";
/*     */     } 
/*  56 */     if (!str4.equals("")) {
/*  57 */       str5 = str5 + " and plancontent like '%";
/*  58 */       str5 = str5 + Util.fromScreen2(str4, this.user.getLanguage());
/*  59 */       str5 = str5 + "%'";
/*     */     } 
/*  61 */     str5 = SqlUtils.replaceFirstAnd(str5);
/*  62 */     RecordSet recordSet = new RecordSet();
/*  63 */     recordSet.execute("select id,planname,layoutid,plancontent,planaim from HrmTrainPlan " + str5);
/*  64 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  65 */     TrainPlanComInfo trainPlanComInfo = new TrainPlanComInfo();
/*  66 */     TrainLayoutComInfo trainLayoutComInfo = new TrainLayoutComInfo();
/*  67 */     while (recordSet.next()) {
/*  68 */       String str = recordSet.getString("id");
/*  69 */       boolean bool = trainPlanComInfo.isViewer(str, "" + this.user.getUID());
/*  70 */       if (HrmUserVarify.checkUserRight("HrmTrainLayoutEdit:Edit", this.user)) {
/*  71 */         bool = true;
/*     */       }
/*  73 */       if (!bool) {
/*     */         continue;
/*     */       }
/*     */       
/*  77 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  78 */       hashMap1.put("id", str);
/*  79 */       hashMap1.put("planname", Util.null2String(recordSet.getString("planname")));
/*  80 */       hashMap1.put("layoutname", trainLayoutComInfo.getLayoutname(recordSet.getString("layoutid")));
/*  81 */       hashMap1.put("plancontent", Util.null2String(recordSet.getString("plancontent")));
/*  82 */       hashMap1.put("planaim", Util.null2String(recordSet.getString("planaim")));
/*  83 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/*  86 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  87 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  88 */     arrayList1.add((new ListHeadBean("planname", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "15%")).setIsInputCol(BoolAttr.TRUE));
/*  89 */     arrayList1.add(new ListHeadBean("layoutname", SystemEnv.getHtmlLabelName(6101, this.user.getLanguage()), "15%"));
/*  90 */     arrayList1.add(new ListHeadBean("plancontent", SystemEnv.getHtmlLabelName(345, this.user.getLanguage()), "35%"));
/*  91 */     arrayList1.add(new ListHeadBean("planaim", SystemEnv.getHtmlLabelName(16142, this.user.getLanguage()), "35%"));
/*     */     
/*  93 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  94 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  95 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  96 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 101 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 102 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 103 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 104 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 106 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "planname", true));
/*     */     
/* 108 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 345, "plancontent"));
/* 109 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 16142, "planaim"));
/* 110 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmTrainPlanBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */