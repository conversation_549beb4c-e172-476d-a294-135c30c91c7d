/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.e9.controller.base.FnaInvoiceLedgerController;
/*     */ import weaver.fna.invoice.Constants;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MultiInvoiceBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[  {    \"key\": \"col1\",    \"configs\": [      {        \"key\": \"col1_row1\",        \"configs\": [          {            \"key\": \"invoiceNumber\"          },          {            \"key\": \"invoiceCode\",            \"style\": {              \"float\": \"right\"            }          }        ]      },      {        \"key\": \"col1_row2\",        \"configs\": [          {            \"key\": \"taxIncludedPrice\"          },          {            \"key\": \"invoiceTypeName\",            \"style\": {              \"float\": \"right\"            }          }        ]      }      {        \"key\": \"col1_row3\",        \"configs\": [          {            \"key\": \"billingdate\"          },        ]      }    ]  }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  98 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  99 */     if (this.user == null) {
/* 100 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 101 */       return (Map)hashMap;
/*     */     } 
/* 103 */     String str1 = Util.null2String(paramMap.get("createdatestart")).trim();
/* 104 */     String str2 = Util.null2String(paramMap.get("createdateend")).trim();
/* 105 */     String str3 = Util.null2String(paramMap.get("invoiceNumber"));
/* 106 */     String str4 = Util.null2String(paramMap.get("seller"));
/*     */ 
/*     */     
/* 109 */     FnaInvoiceLedgerController fnaInvoiceLedgerController = FnaInvoiceLedgerController.getInstance();
/* 110 */     String str5 = fnaInvoiceLedgerController.getCaseWhenSql4InvoiceTypeList("invoiceTypeName", "a.invoiceType", this.user.getLanguage()) + " ";
/*     */ 
/*     */     
/* 113 */     String str6 = " a.*, " + fnaInvoiceLedgerController.getCaseWhenSql4InvoiceTypeList("invoiceTypeName", "a.invoiceType", this.user.getLanguage()) + " ";
/* 114 */     String str7 = " from FnaInvoiceLedger a ";
/*     */     
/* 116 */     StringBuffer stringBuffer = new StringBuffer(" where 1=1 and (checkStatus = 1  or checkStatus = 2)    ");
/* 117 */     if (!"".equals(str2)) {
/* 118 */       stringBuffer.append(" and a.billingDate <= '").append(StringEscapeUtils.escapeSql(str2)).append("' ");
/*     */     }
/* 120 */     if (!"".equals(str1)) {
/* 121 */       stringBuffer.append(" and a.billingDate >= '").append(StringEscapeUtils.escapeSql(str1)).append("' ");
/*     */     }
/* 123 */     if (!"".equals(str3)) {
/* 124 */       stringBuffer.append(" and a.invoiceNumber like '%").append(StringEscapeUtils.escapeSql(str3.trim())).append("%'");
/*     */     }
/* 126 */     if (!"".equals(str4)) {
/* 127 */       stringBuffer.append(" and a.seller like '%").append(StringEscapeUtils.escapeSql(str4.trim())).append("%'");
/*     */     }
/* 129 */     stringBuffer.append(" and (a.userid_new = ").append(this.user.getUID()).append(" or a.id in ( select invoiceId from fnaInvoiceSharer where sharer = ").append(this.user.getUID()).append(") ").append(") ");
/* 130 */     stringBuffer.append(" and a.status = '0' ");
/* 131 */     String str8 = " a.id ";
/*     */ 
/*     */     
/* 134 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 135 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 136 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(900, this.user.getLanguage()), "invoiceNumber", "invoiceNumber", 1)).setIsInputCol(BoolAttr.TRUE));
/* 137 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(17213, this.user.getLanguage()), "invoiceCode", "invoiceCode"));
/* 138 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(17213, this.user.getLanguage()), "invoiceTypeName", "invoiceTypeName"));
/* 139 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(17213, this.user.getLanguage()), "billingdate", "billingdate"));
/* 140 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(17213, this.user.getLanguage()), "taxIncludedPrice", "taxIncludedPrice"));
/* 141 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, stringBuffer.toString(), str8, "a.id", "DESC", arrayList);
/*     */ 
/*     */ 
/*     */     
/* 145 */     List list = Util_MobileData.createList("[  {    \"key\": \"col1\",    \"configs\": [      {        \"key\": \"col1_row1\",        \"configs\": [          {            \"key\": \"invoiceNumber\"          },          {            \"key\": \"invoiceCode\",            \"style\": {              \"float\": \"right\"            }          }        ]      },      {        \"key\": \"col1_row2\",        \"configs\": [          {            \"key\": \"taxIncludedPrice\"          },          {            \"key\": \"invoiceTypeName\",            \"style\": {              \"float\": \"right\"            }          }        ]      }      {        \"key\": \"col1_row3\",        \"configs\": [          {            \"key\": \"billingdate\"          },        ]      }    ]  }]");
/* 146 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createJsonTemplateBean("theme_default", list);
/* 147 */     splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/*     */     
/* 149 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 150 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 160 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 161 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 162 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 163 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 900, "invoiceNumber").setIsQuickSearch(true));
/* 164 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 165 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 175 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 177 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*     */     
/* 179 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 181 */     StringBuffer stringBuffer = new StringBuffer();
/* 182 */     stringBuffer.append(" select a.* from FnaInvoiceLedger a ");
/* 183 */     stringBuffer.append(" where a.id in (").append(str).append(")");
/* 184 */     stringBuffer.append(" order by a.id desc ");
/*     */     
/* 186 */     RecordSet recordSet = new RecordSet();
/* 187 */     recordSet.execute(stringBuffer.toString());
/* 188 */     while (recordSet.next()) {
/* 189 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 190 */       hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 191 */       hashMap1.put("invoiceNumber", Util.null2String(recordSet.getString("invoiceNumber")));
/* 192 */       hashMap1.put("invoiceCode", Util.null2String(recordSet.getString("invoiceCode")));
/* 193 */       hashMap1.put("invoiceTypeName", Constants.INVOICETYPE.get(Util.null2String(recordSet.getString("invoicetype"))));
/* 194 */       hashMap1.put("billingdate", Util.null2String(recordSet.getString("billingdate")));
/* 195 */       hashMap1.put("taxIncludedPrice", Util.null2String(recordSet.getString("taxIncludedPrice")));
/* 196 */       arrayList.add(hashMap1);
/*     */     } 
/* 198 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 199 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 200 */     arrayList1.add(new ListHeadBean("invoiceNumber", "", 1, BoolAttr.TRUE));
/* 201 */     arrayList1.add(new ListHeadBean("invoiceCode", ""));
/* 202 */     arrayList1.add(new ListHeadBean("invoiceTypeName", ""));
/* 203 */     arrayList1.add(new ListHeadBean("billingdate", ""));
/* 204 */     arrayList1.add(new ListHeadBean("taxIncludedPrice", ""));
/*     */     
/* 206 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 207 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 208 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 210 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MultiInvoiceBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */