/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SearchConditionOption;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.DBUtil;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.cowork.CoMainTypeComInfo;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class MutiCoworkTypeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */     String str1 = Util.null2String(paramMap.get("typename"));
/* 29 */     String str2 = Util.null2String(paramMap.get("departmentid"));
/* 30 */     String str3 = " * ";
/* 31 */     String str4 = " from cowork_types ";
/* 32 */     String str5 = " where 1=1 ";
/* 33 */     if (!"".equals(str1)) {
/* 34 */       str5 = str5 + " and typename like '%" + str1 + "%'";
/*    */     }
/* 36 */     if (!"".equals(str2)) {
/* 37 */       str5 = str5 + " and departmentid=" + str2 + "";
/*    */     }
/* 39 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 40 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 41 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(83265, this.user.getLanguage()), "typename", null)).setIsInputCol(BoolAttr.TRUE));
/* 42 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(83266, this.user.getLanguage()), "departmentid", null, "weaver.cowork.CoMainTypeComInfo.getCoMainTypename"));
/*    */     
/* 44 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, Util.toHtmlForSplitPage(str5), "id", "id", arrayList);
/* 45 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 46 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 51 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 52 */     CoMainTypeComInfo coMainTypeComInfo = new CoMainTypeComInfo();
/* 53 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 54 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 55 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 83265, "typename", true));
/* 56 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 57 */     coMainTypeComInfo.setTofirstRow();
/* 58 */     while (coMainTypeComInfo.next()) {
/* 59 */       arrayList1.add(new SearchConditionOption(coMainTypeComInfo.getCoMainTypeid(), coMainTypeComInfo.getCoMainTypename()));
/*    */     }
/* 61 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 83266, "departmentid", arrayList1));
/* 62 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 63 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 68 */     String str = Util.null2String(paramMap.get("selectids"));
/*    */     
/* 70 */     if (!"".equals(str)) {
/* 71 */       RecordSet recordSet = new RecordSet();
/* 72 */       ArrayList arrayList = new ArrayList();
/* 73 */       recordSet.executeQuery("select  ct.id ,ct.typename as name,ct.departmentid,cm.typename  from cowork_types ct,cowork_maintypes cm where ct.departmentid=cm.id and ct.id in (" + DBUtil.getParamReplace(str) + ") order by id", new Object[] { DBUtil.trasToList(arrayList, new Object[] { str }) });
/* 74 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 75 */       while (recordSet.next()) {
/* 76 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 77 */         hashMap1.put("id", recordSet.getString("id"));
/* 78 */         hashMap1.put("idspan", hashMap1.get("id"));
/* 79 */         hashMap1.put("typename", recordSet.getString("name") + " " + recordSet.getString("typename"));
/* 80 */         hashMap1.put("typenamespan", hashMap1.get("typename"));
/* 81 */         hashMap1.put("randomFieldId", recordSet.getString("id"));
/* 82 */         hashMap1.put("randomFieldIdspan", "");
/* 83 */         arrayList1.add(hashMap1);
/*    */       } 
/* 85 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 86 */       hashMap.put("datas", arrayList1);
/* 87 */       return (Map)hashMap;
/*    */     } 
/* 89 */     return new HashMap<>();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MutiCoworkTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */