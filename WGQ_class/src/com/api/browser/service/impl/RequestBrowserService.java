/*      */ package com.api.browser.service.impl;
/*      */ import com.alibaba.fastjson.JSON;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SearchConditionOption;
/*      */ import com.api.browser.bean.SplitTableBean;
/*      */ import com.api.browser.bean.SplitTableColBean;
/*      */ import com.api.browser.service.BrowserService;
/*      */ import com.api.browser.util.BelongAttr;
/*      */ import com.api.browser.util.BoolAttr;
/*      */ import com.api.browser.util.BrowserBaseUtil;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.api.browser.util.MobileShowTypeAttr;
/*      */ import com.api.browser.util.MobileViewTypeAttr;
/*      */ import com.api.browser.util.SplitTableUtil;
/*      */ import com.api.workflow.util.ServiceUtil;
/*      */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*      */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*      */ import com.engine.workflow.biz.RequestQuickSearchBiz;
/*      */ import com.engine.workflow.biz.requestForm.RequestSecLevelBiz;
/*      */ import java.text.ParseException;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.regex.Pattern;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.fna.maintenance.FnaSplitTableByYearComInfo;
/*      */ import weaver.fullsearch.util.SearchBrowserUtils;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.setting.HrmUserSettingComInfo;
/*      */ import weaver.workflow.request.RequestBaseUtil;
/*      */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*      */ import weaver.workflow.workflow.WorkflowComInfo;
/*      */ import weaver.workflow.workflow.WorkflowVersion;
/*      */ 
/*      */ public class RequestBrowserService extends BrowserService {
/*   48 */   public static final Pattern COLLECTION_REQUEST_ID_PATTERN = Pattern.compile("requestid=(\\d+)");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static final String SEC_LEVEL = "secretLevel";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"requestname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"createdate\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"creater\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static final String JSON_CONFIG_2 = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"requestnamenew\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"createdate\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"creater\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  116 */     List<String> list = fnaUtils(paramMap);
/*  117 */     String str1 = list.get(1);
/*      */     
/*  119 */     boolean bool = validQuickSearch(paramMap);
/*  120 */     if (bool && "".equals(str1)) {
/*      */       
/*  122 */       if ("171".equals(this.browserType)) paramMap.put("currentnodetype", Integer.valueOf(3)); 
/*  123 */       eSearchParamsTrans(paramMap);
/*  124 */       return (new RequestQuickSearchBiz()).getRequestList(paramMap, this.user);
/*      */     } 
/*  126 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  127 */     String str2 = " t.* ";
/*  128 */     String str3 = "createdate , createtime ";
/*  129 */     String str4 = Util.null2String(paramMap.get("importWfBrow"));
/*  130 */     String str5 = getFromSql(paramMap, list);
/*      */     
/*  132 */     (new BaseBean()).writeLog("111--sql:select" + str2 + str5);
/*      */ 
/*      */     
/*  135 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isFormEMShare")), -1);
/*  136 */     if (i == 1) {
/*  137 */       getTableString4EMShare((Map)hashMap, this.user, str2, str5, str3);
/*      */     } else {
/*  139 */       getTableString((Map)hashMap, this.user, str2, str5, str3, str4, Util.getIntValue(Util.null2String(paramMap.get("ismobile"))));
/*      */     } 
/*  141 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<String> fnaUtils(Map<String, Object> paramMap) {
/*  152 */     String str1 = Util.null2String(paramMap.get("currworkflowid"));
/*  153 */     if ("".equals(str1)) {
/*  154 */       str1 = Util.null2String(paramMap.get("workflowid"));
/*      */     }
/*      */ 
/*      */     
/*  158 */     StringBuilder stringBuilder1 = new StringBuilder();
/*  159 */     StringBuilder stringBuilder2 = new StringBuilder();
/*      */ 
/*      */     
/*  162 */     RecordSet recordSet = new RecordSet();
/*  163 */     String str2 = Util.null2String(paramMap.get("fieldid"));
/*  164 */     if (!"".equals(str2)) {
/*  165 */       String[] arrayOfString = str2.split("_");
/*  166 */       str2 = arrayOfString[0];
/*      */     } 
/*  168 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")));
/*  169 */     int j = Util.getIntValue(Util.null2String(paramMap.get("fieldid")));
/*  170 */     int k = Util.getIntValue(Util.null2String(paramMap.get("__requestid")));
/*  171 */     int m = Util.getIntValue(Util.null2String(paramMap.get("fna_wfid")));
/*  172 */     int n = Util.getIntValue(Util.null2String(paramMap.get("fna_fieldid")));
/*  173 */     if (i > 0 && m <= 0) {
/*  174 */       m = i;
/*      */     }
/*  176 */     if (Util.getIntValue(str1) > 0 && m <= 0) {
/*  177 */       m = Util.getIntValue(str1);
/*      */     }
/*  179 */     if (j > 0 && n <= 0) {
/*  180 */       n = j;
/*      */     }
/*  182 */     if (Util.getIntValue(str2) > 0 && n <= 0) {
/*  183 */       n = Util.getIntValue(str2);
/*      */     }
/*  185 */     boolean bool1 = false;
/*  186 */     HashMap hashMap = FnaCommon.getIsEnableFnaWfHm(m);
/*  187 */     bool1 = "true".equals(hashMap.get("isEnableFnaWfE8"));
/*      */ 
/*      */ 
/*      */     
/*  191 */     if (!bool1) {
/*  192 */       HashMap hashMap1 = FnaCommon.getIsEnableFnaRepaymentWfHm(m);
/*  193 */       bool1 = "true".equals(hashMap1.get("isEnableFnaRepaymentWf"));
/*      */     } 
/*      */ 
/*      */     
/*  197 */     boolean bool2 = false;
/*  198 */     boolean bool = false;
/*  199 */     boolean bool3 = false;
/*  200 */     if (bool1) {
/*  201 */       bool2 = FnaCommon.checkFnaWfFieldFnaType(m, n, 2, 0, "fnaFeeWf");
/*  202 */       bool = (FnaCommon.checkFnaWfFieldFnaType(m, n, 1, 2, "repayment") || FnaCommon.checkFnaWfFieldFnaType(m, n, 1, 2, "fnaFeeWf")) ? true : false;
/*  203 */       bool3 = FnaCommon.checkFnaWfFieldFnaType(m, n, 1, 4, "fnaFeeWf");
/*      */     } 
/*  205 */     int i1 = Util.getIntValue(Util.null2String(paramMap.get("borrowType")), -1);
/*  206 */     int i2 = 0;
/*  207 */     int i3 = 0;
/*      */ 
/*      */     
/*  210 */     if (bool2) {
/*      */       
/*  212 */       String str3 = "";
/*  213 */       recordSet.executeQuery(" select workflowid from fnaFeeWfInfo where enable = 1 and fnaWfType = 'fnaFeeWf' ", new Object[0]);
/*  214 */       while (recordSet.next()) {
/*  215 */         String str = Util.null2String(recordSet.getString("workflowid"));
/*  216 */         if (!"".equals(str3)) {
/*  217 */           str3 = str3 + ",";
/*      */         }
/*  219 */         str3 = str3 + str;
/*      */       } 
/*  221 */       String str4 = WorkflowVersion.getAllVersionStringByWFIDs(str3);
/*  222 */       if ("".equals(str4)) {
/*  223 */         str4 = "-1";
/*      */       }
/*      */       
/*  226 */       String str5 = "ISNULL";
/*      */       
/*  228 */       if ("oracle".equals(recordSet.getDBType())) {
/*  229 */         str5 = "NVL";
/*      */       }
/*  231 */       else if ("mysql".equals(recordSet.getDBType())) {
/*  232 */         str5 = "IFNULL";
/*  233 */       } else if ("postgresql".equals(recordSet.getDBType())) {
/*  234 */         str5 = "isnull";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  240 */       ArrayList<String> arrayList = new ArrayList();
/*  241 */       FnaSplitTableByYearComInfo.getFnaTableNameList(FnaTableNameEnum.FnaExpenseInfo, "1000-01-01", "9999-12-31", arrayList);
/*  242 */       stringBuilder1.append("(");
/*  243 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  244 */         String str = arrayList.get(b);
/*      */         
/*  246 */         if (b > 0)
/*      */         {
/*  248 */           stringBuilder1.append(" union all ");
/*      */         }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  255 */         stringBuilder1.append(" ( select requestid from ").append(str).append(" where budgetperiodslist is not null and sourceRequestid <> ").append(k);
/*  256 */         stringBuilder1.append(" and status = 0  GROUP BY organizationid, organizationtype, subject, budgetperiods, budgetperiodslist,requestid HAVING SUM(")
/*  257 */           .append(str5).append("(amount,0.00)) > 0.00 ) ");
/*      */       } 
/*      */ 
/*      */       
/*  261 */       stringBuilder1.append(") fei");
/*  262 */       stringBuilder2.append(" and fei.requestid = workflow_requestbase.requestid ");
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  267 */       stringBuilder2.append(" and workflow_requestbase.currentnodetype = 3 and workflow_requestbase.workflowid in (").append(str4).append(") ");
/*      */     }
/*  269 */     else if (bool) {
/*      */ 
/*      */       
/*  272 */       int i4 = this.user.getUID();
/*  273 */       i2 = Util.getIntValue(Util.null2String(paramMap.get("main_fieldIdSqr_controlBorrowingWf")), -1);
/*  274 */       if (i2 == 1) {
/*  275 */         i3 = Util.getIntValue(Util.null2String(paramMap.get("main_fieldIdSqr_val")), -1);
/*  276 */         i4 = i3;
/*      */       } 
/*  278 */       String str = "";
/*  279 */       if (i1 == -1 || i1 == 0) {
/*  280 */         str = str + " (fbi1.borrowType0 = 0 and fbi1.applicantid = " + i4 + ") \n";
/*      */       }
/*  282 */       if (i1 == -1) {
/*  283 */         str = str + " or \n";
/*      */       }
/*  285 */       if (i1 == -1 || i1 == 1) {
/*  286 */         str = str + " (fbi1.borrowType1 = 1) \n";
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  307 */       stringBuilder1.append(" (select fbi.borrowRequestId, fbi.borrowRequestIdDtlId,SUM(fbi.amountBorrow * fbi.borrowDirection) sum_amountBorrow,MAX(CASE WHEN fbi.recordType = 'borrow' THEN fbi.applicantid ELSE 0 END) applicantid,")
/*  308 */         .append("MAX(CASE WHEN fbi.recordType = 'borrow' THEN fbi.departmentid ELSE 0 END) departmentid,MAX(CASE WHEN fbi.recordType = 'borrow' THEN fbi.subcompanyid1 ELSE 0 END) subcompanyid1,")
/*  309 */         .append("MAX(CASE WHEN (fbi.borrowType is null or fbi.borrowType <> 1) THEN 0 ELSE -99999 END) borrowType0,MAX(CASE WHEN fbi.borrowType = 1 THEN 1 ELSE -99999 END) borrowType1 from FnaBorrowInfo fbi ")
/*  310 */         .append(" where fbi.requestid <> ").append(k).append(" GROUP BY fbi.borrowRequestId, fbi.borrowRequestIdDtlId) fbi1 ");
/*      */       
/*  312 */       stringBuilder2.append(" and fbi1.sum_amountBorrow > 0 and fbi1.borrowRequestId = workflow_requestbase.requestId and (" + str + ") ");
/*  313 */     } else if (bool3) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  319 */       stringBuilder1.append(" (select fbi.advanceRequestId, fbi.advanceRequestIdDtlId, SUM(fbi.amountAdvance * fbi.advanceDirection) sum_amountAdvance from FnaAdvanceInfo fbi where fbi.requestid <> ")
/*  320 */         .append(k).append(" GROUP BY fbi.advanceRequestId, fbi.advanceRequestIdDtlId) fbi1 ");
/*      */       
/*  322 */       stringBuilder2.append(" and fbi1.sum_amountAdvance > 0 and fbi1.advanceRequestId = workflow_requestbase.requestId ");
/*      */     } 
/*      */ 
/*      */     
/*  326 */     return Arrays.asList(new String[] { stringBuilder1.toString(), stringBuilder2.toString() });
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getTableString4EMShare(Map<String, Object> paramMap, User paramUser, String paramString1, String paramString2, String paramString3) {
/*  338 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  339 */     arrayList.add((new SplitTableColBean("true", "id")).setBelong(BelongAttr.PCMOBILE));
/*  340 */     arrayList.add((new SplitTableColBean("50%", 
/*  341 */           SystemEnv.getHtmlLabelName(26876, paramUser.getLanguage()), "requestnamenew", "requestnamenew", "com.api.browser.service.impl.RequestBrowserService.getRequestNameWithOutHtml", "column:id+" + paramUser
/*  342 */           .getLanguage() + "+column:requestname", 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE));
/*  343 */     arrayList.add((new SplitTableColBean("25%", 
/*  344 */           SystemEnv.getHtmlLabelName(882, paramUser.getLanguage()), "creater", "creater", "weaver.workflow.request.RequestBrowser.getWfCreaterName", "column:creatertype+" + paramUser
/*      */ 
/*      */           
/*  347 */           .getLanguage())).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  348 */     arrayList.add((new SplitTableColBean("25%", 
/*  349 */           SystemEnv.getHtmlLabelName(1339, paramUser.getLanguage()), "createdate", "createdate", "com.api.browser.service.impl.RequestBrowserService.getCreateTime", "column:createtime"))
/*      */ 
/*      */         
/*  352 */         .setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  353 */     SplitTableBean splitTableBean = new SplitTableBean(paramString1, paramString2, "", paramString3, "id", arrayList);
/*  354 */     splitTableBean.setSqlisdistinct("true");
/*      */ 
/*      */     
/*  357 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*      */     try {
/*  359 */       splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"requestnamenew\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"createdate\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"creater\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  360 */     } catch (Exception exception) {
/*  361 */       exception.printStackTrace();
/*      */     } 
/*  363 */     paramMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*      */   }
/*      */   
/*      */   private void getTableString(Map<String, Object> paramMap, User paramUser, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/*  367 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  368 */     arrayList.add((new SplitTableColBean("true", "id")).setBelong(BelongAttr.PCMOBILE));
/*  369 */     arrayList.add((new SplitTableColBean("true", "requestname")).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE));
/*  370 */     if ("1".equals(paramString4)) {
/*  371 */       arrayList.add(new SplitTableColBean("50%", 
/*  372 */             SystemEnv.getHtmlLabelName(26876, paramUser.getLanguage()), "requestname", "requestname", "com.api.browser.service.impl.RequestBrowserService.getRequestnameWithLink", "column:id+" + paramUser
/*  373 */             .getLanguage() + "+column:requestname", 1));
/*  374 */       arrayList.add(new SplitTableColBean("25%", 
/*  375 */             SystemEnv.getHtmlLabelName(882, paramUser.getLanguage()), "creater", "creater", "weaver.general.WorkFlowTransMethod.getWFSearchResultName", "column:creatertype"));
/*      */     }
/*      */     else {
/*      */       
/*  379 */       arrayList.add((new SplitTableColBean("50%", 
/*  380 */             SystemEnv.getHtmlLabelName(26876, paramUser.getLanguage()), "requestnamenew", "requestnamenew", "com.api.browser.service.impl.RequestBrowserService.getRequestname", "column:id+" + paramUser
/*      */ 
/*      */             
/*  383 */             .getLanguage() + "+column:requestname", 1)).setBelong(BelongAttr.PCMOBILE));
/*  384 */       arrayList.add((new SplitTableColBean("25%", 
/*  385 */             SystemEnv.getHtmlLabelName(882, paramUser.getLanguage()), "creater", "creater", "weaver.workflow.request.RequestBrowser.getWfCreaterName", "column:creatertype+" + paramUser
/*      */ 
/*      */             
/*  388 */             .getLanguage())).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*      */     } 
/*      */     
/*  391 */     arrayList.add((new SplitTableColBean("25%", 
/*  392 */           SystemEnv.getHtmlLabelName(1339, paramUser.getLanguage()), "createdate", "createdate", "com.api.browser.service.impl.RequestBrowserService.getCreateTime", "column:createtime"))
/*      */ 
/*      */         
/*  395 */         .setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*  396 */     SplitTableBean splitTableBean = new SplitTableBean(paramString1, paramString2, "", paramString3, "id", arrayList);
/*  397 */     splitTableBean.setSqlisdistinct("true");
/*      */ 
/*      */ 
/*      */     
/*  401 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*      */     try {
/*  403 */       if ("1".equals(paramString4)) {
/*  404 */         splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"requestname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"createdate\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"creater\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*      */       } else {
/*  406 */         splitTableBean.createMobileTemplate(JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"requestnamenew\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"createdate\"                    },                    {                        \"style\": {                            \"float\": \"right\"                        },                        \"key\": \"creater\"                    }                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*      */       } 
/*  408 */     } catch (Exception exception) {
/*  409 */       exception.printStackTrace();
/*      */     } 
/*  411 */     paramMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  416 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  417 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  418 */     if ("".equals(str1)) return (Map)hashMap; 
/*  419 */     String str2 = "select requestid,creatertype,requestname, requestnamenew,creater,createdate,createtime from workflow_requestbase where requestid in (" + str1 + ")";
/*  420 */     RecordSet recordSet = new RecordSet();
/*  421 */     recordSet.executeSql(str2);
/*  422 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  423 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  424 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  425 */     while (recordSet.next()) {
/*  426 */       String str3 = Util.null2String(recordSet.getString("requestid"));
/*  427 */       String str4 = Util.null2String(recordSet.getString("creater"));
/*  428 */       String str5 = Util.null2String(recordSet.getString("createdate"));
/*  429 */       String str6 = Util.null2String(recordSet.getString("createtime"));
/*  430 */       String str7 = Util.null2String(recordSet.getString("requestname"));
/*  431 */       String str8 = getRequestname("", str3 + "+" + this.user.getLanguage() + "+" + str7);
/*      */ 
/*      */       
/*  434 */       String str9 = Util.null2String(recordSet.getString("createtype"));
/*  435 */       if ("1".equals(str9)) {
/*  436 */         str4 = customerInfoComInfo.getCustomerInfoname(str4);
/*      */       } else {
/*  438 */         str4 = resourceComInfo.getResourcename(str4);
/*      */       } 
/*      */       
/*  441 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  442 */       hashMap1.put("id", str3);
/*  443 */       hashMap1.put("requestname", str7);
/*  444 */       hashMap1.put("requestnamespan", str7);
/*  445 */       hashMap1.put("requestnamenew", str8);
/*  446 */       hashMap1.put("creater", str4);
/*  447 */       hashMap1.put("createdate", str5 + " " + str6);
/*  448 */       arrayList.add(hashMap1);
/*      */     } 
/*      */ 
/*      */     
/*  452 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/*  453 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  454 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  459 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  460 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  461 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  462 */     ConditionFactory conditionFactory = new ConditionFactory(this.user, !"16".equals(this.browserType));
/*      */     
/*  464 */     String str1 = Util.null2String(paramMap.get("importWfBrow"));
/*  465 */     if ("1".equals(str1)) {
/*  466 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 26876, "requestname", true));
/*  467 */       arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 714, "requestmark"));
/*      */       
/*  469 */       boolean bool2 = ("1".equals(Util.null2String(paramMap.get("ismobile"))) && "1".equals(Util.null2String(paramMap.get("newReq")))) ? true : false;
/*  470 */       if (!bool2) {
/*  471 */         arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 882, "creater", "17"));
/*      */       }
/*  473 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 19225, "department", "57"));
/*      */       
/*  475 */       String[] arrayOfString = { "createdatetype", "createdatestart", "createdateend" };
/*  476 */       SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.DATE, 722, arrayOfString);
/*  477 */       searchConditionItem3.setColSpan(1);
/*  478 */       List list = BrowserBaseUtil.getDateSelectOption(this.user.getLanguage(), true, true);
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  483 */       searchConditionItem3.setLabelcol(3);
/*  484 */       searchConditionItem3.setFieldcol(21);
/*  485 */       searchConditionItem3.setOptions(list);
/*  486 */       arrayList.add(searchConditionItem3);
/*  487 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 782, "prjids", "135"));
/*      */       
/*  489 */       WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  490 */       SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.BROWSER, 259, "workflowid", "-99991", 32770);
/*  491 */       String str = Util.null2String(paramMap.get("workflowid"));
/*  492 */       List<HashMap<Object, Object>> list1 = searchConditionItem4.getBrowserConditionParam().getReplaceDatas();
/*  493 */       if (list1 == null) list1 = new ArrayList(); 
/*  494 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  495 */       hashMap1.put("id", str);
/*  496 */       hashMap1.put("name", workflowAllComInfo.getWorkflowname(str));
/*  497 */       list1.add(hashMap1);
/*  498 */       searchConditionItem4.getBrowserConditionParam().setReplaceDatas(list1);
/*  499 */       arrayList.add(searchConditionItem4);
/*      */       
/*  501 */       Map<String, String> map1 = searchConditionItem4.getBrowserConditionParam().getDataParams();
/*  502 */       map1.put("formid", workflowAllComInfo.getFormId(str));
/*  503 */       map1.put("isbill", workflowAllComInfo.getIsBill(str));
/*      */       
/*  505 */       Map<String, String> map2 = searchConditionItem4.getBrowserConditionParam().getCompleteParams();
/*  506 */       map2.putAll(map1);
/*      */ 
/*      */       
/*  509 */       arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 783, "crmids", "18"));
/*      */       
/*  511 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*  512 */       arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), bool2));
/*  513 */       arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(18800, this.user.getLanguage()), !bool2));
/*  514 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(17999, this.user.getLanguage())));
/*  515 */       arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 15112, "status", arrayList2));
/*  516 */       return (Map)hashMap;
/*      */     } 
/*  518 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 26876, "requestname", true));
/*  519 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.BROWSER, 33806, "workflowid", "-99991", 32770);
/*  520 */     searchConditionItem1.getBrowserConditionParam().setIsSingle(false);
/*  521 */     arrayList.add(searchConditionItem1);
/*  522 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 714, "requestmark"));
/*  523 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 882, "creater", "17"));
/*  524 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 19225, "department", "57"));
/*  525 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 22788, "subid", "194"));
/*      */     
/*  527 */     String[] arrayOfString1 = { "createdatetype", "createdatestart", "createdateend" };
/*  528 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.DATE, 722, arrayOfString1);
/*  529 */     searchConditionItem2.setColSpan(1);
/*  530 */     searchConditionItem2.setLabelcol("16".equals(this.browserType) ? 3 : 8);
/*  531 */     searchConditionItem2.setFieldcol("16".equals(this.browserType) ? 21 : 16);
/*  532 */     searchConditionItem2.setOptions(BrowserBaseUtil.getDateSelectOption(this.user.getLanguage(), true, true));
/*  533 */     arrayList.add(searchConditionItem2);
/*      */     
/*  535 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 782, "prjids", "135"));
/*  536 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 783, "crmids", "18"));
/*      */ 
/*      */     
/*  539 */     if (!"171".equals(this.browserType)) {
/*  540 */       ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*  541 */       arrayList2.add(new SearchConditionOption("", ""));
/*  542 */       arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(17999, this.user.getLanguage())));
/*  543 */       arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(18800, this.user.getLanguage())));
/*  544 */       arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 15112, "status", arrayList2));
/*      */     } 
/*      */ 
/*      */     
/*  548 */     String str2 = Util.null2String((new BaseBean()).getPropValue("wfdateduring", "wfdateduring"));
/*  549 */     String[] arrayOfString2 = Util.TokenizerString2(str2, ",");
/*  550 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*      */     
/*  552 */     SearchBrowserUtils searchBrowserUtils = new SearchBrowserUtils();
/*  553 */     boolean bool = SearchBrowserUtils.quickSearchValidate("WFSEARCH", this.user.getLanguage() + "");
/*  554 */     if (bool) bool = validQuickSearch(paramMap); 
/*  555 */     boolean bool1 = !bool ? true : false;
/*  556 */     for (String str : arrayOfString2) {
/*  557 */       if (!"".equals(Util.null2String(str))) {
/*  558 */         arrayList1.add(new SearchConditionOption(str, SystemEnv.getHtmlLabelName(24515, this.user.getLanguage()) + str + SystemEnv.getHtmlLabelName(26301, this.user.getLanguage()), bool1));
/*  559 */         if (bool1) bool1 = false; 
/*      */       } 
/*  561 */     }  arrayList1.add(new SearchConditionOption("38", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), bool));
/*      */     
/*  563 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 31787, "date2during", arrayList1));
/*  564 */     return (Map)hashMap;
/*      */   }
/*      */   
/*      */   public String getRequestnameWithLink(String paramString1, String paramString2) {
/*  568 */     int i = Util.getIntValue(paramString2.substring(0, paramString2.indexOf("+")));
/*  569 */     String str = getRequestname(paramString1, paramString2);
/*  570 */     str = "<a style = \"text-decoration: none\" href=\"javaScript:openFullWindowHaveBar('/workflow/request/ViewRequestForwardSPA.jsp?requestid=" + i + "')\">" + str + "</a>";
/*  571 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getRequestname(String paramString1, String paramString2) {
/*  576 */     int i = Util.getIntValue(paramString2.substring(0, paramString2.indexOf("+")));
/*  577 */     paramString2 = paramString2.substring(paramString2.indexOf("+") + 1);
/*  578 */     int j = Util.getIntValue(paramString2.substring(0, paramString2.indexOf("+")));
/*  579 */     String str = paramString2.substring(paramString2.indexOf("+") + 1);
/*  580 */     RecordSet recordSet = new RecordSet();
/*  581 */     recordSet.executeQuery("select id,formid,isbill from workflow_base t1 where exists (select 1 from workflow_requestbase t2 where t2.workflowid  = t1.id and t2.requestid = ? )", new Object[] { Integer.valueOf(i) });
/*  582 */     int k = 0;
/*  583 */     int m = 0;
/*  584 */     int n = 0;
/*  585 */     if (recordSet.next()) {
/*  586 */       k = recordSet.getInt(1);
/*  587 */       m = recordSet.getInt(2);
/*  588 */       n = recordSet.getInt(3);
/*      */     } 
/*      */     
/*  591 */     RequestBaseUtil requestBaseUtil = new RequestBaseUtil();
/*  592 */     str = requestBaseUtil.formatRequestname(str, k + "", i + "", n, m, j);
/*      */     
/*  594 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getRequestNameWithOutHtml(String paramString1, String paramString2) {
/*  604 */     String str = getRequestname(paramString1, paramString2);
/*  605 */     return Util.delHtmlWithSpace(str);
/*      */   }
/*      */   
/*      */   public String getWfOnlyViewLink(String paramString1, String paramString2) {
/*  609 */     RecordSet recordSet = new RecordSet();
/*  610 */     recordSet.executeQuery("select id,formid,isbill from workflow_base t1 where exists (select 1 from workflow_requestbase t2 where t2.workflowid  = t1.id and t2.requestid = ? )", new Object[] { paramString2 });
/*  611 */     int i = 0;
/*  612 */     int j = 0;
/*  613 */     int k = 0;
/*  614 */     if (recordSet.next()) {
/*  615 */       i = recordSet.getInt(1);
/*  616 */       j = recordSet.getInt(2);
/*  617 */       k = recordSet.getInt(3);
/*      */     } 
/*  619 */     boolean bool = (null == this.user) ? true : this.user.getLanguage();
/*  620 */     RequestBaseUtil requestBaseUtil = new RequestBaseUtil();
/*  621 */     paramString1 = requestBaseUtil.formatRequestname(paramString1, i + "", paramString2 + "", k, j, bool);
/*  622 */     return "<a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequestForwardSPA.jsp?isonlyview=1&requestid=" + paramString2 + "')>" + paramString1 + "</a>";
/*      */   }
/*      */   
/*      */   public String getCreateTime(String paramString1, String paramString2) {
/*  626 */     return paramString1 + " " + paramString2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean validQuickSearch(Map<String, Object> paramMap) {
/*  636 */     ArrayList<String> arrayList = new ArrayList();
/*  637 */     arrayList.add("formid");
/*  638 */     arrayList.add("importWfBrow");
/*  639 */     arrayList.add("collReq");
/*  640 */     arrayList.add("spellSqlWhere");
/*  641 */     Iterator<String> iterator = paramMap.keySet().iterator();
/*  642 */     while (iterator.hasNext()) {
/*  643 */       String str = iterator.next();
/*  644 */       if (arrayList.contains(str)) {
/*  645 */         return false;
/*      */       }
/*  647 */       if (!"fieldid".equalsIgnoreCase(str) && str.indexOf("field") > -1) {
/*  648 */         return false;
/*      */       }
/*  650 */       if ("date2during".equals(str)) {
/*  651 */         String str1 = Util.null2String(paramMap.get("date2during"));
/*  652 */         if (!"38".equals(str1) && !"".equals(str1)) {
/*  653 */           return false;
/*      */         }
/*      */       } 
/*  656 */       if ("secretLevel".equals(str) && HrmClassifiedProtectionBiz.isOpenClassification() && !SearchBrowserUtils.isSupportSecretLevel()) {
/*  657 */         return false;
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  662 */     SearchBrowserUtils searchBrowserUtils = new SearchBrowserUtils();
/*  663 */     return SearchBrowserUtils.quickSearchValidate("WFSEARCH", this.user.getLanguage() + "");
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  668 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  670 */     List<String> list = fnaUtils(this.dataDefinitionParams);
/*  671 */     String str1 = list.get(1);
/*      */     
/*  673 */     boolean bool = validQuickSearch(this.dataDefinitionParams);
/*  674 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  675 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  676 */     this.dataDefinitionParams.put("requestname", str2);
/*  677 */     if (bool && "".equals(str1)) {
/*      */       
/*  679 */       if ("171".equals(this.browserType)) this.dataDefinitionParams.put("currentnodetype", Integer.valueOf(3)); 
/*  680 */       Map<String, Object> map = reSetDataDefinitionParams();
/*  681 */       List list1 = (new RequestQuickSearchBiz()).getRequestData(this.user, map, 30);
/*  682 */       if (list1 != null && list1.size() > 0) {
/*  683 */         for (Map map1 : list1) {
/*  684 */           Map map2 = map1;
/*  685 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  686 */           hashMap1.put(BrowserConstant.BROWSER_AUTO_COMPLETE_PRIMARY_KEY, map2.get("id"));
/*  687 */           hashMap1.put(BrowserConstant.BROWSER_AUTO_COMPLETE_SHOW_NAME, map2.get("requestname"));
/*  688 */           arrayList.add(hashMap1);
/*      */         } 
/*      */       }
/*      */     } else {
/*  692 */       String str3 = "t.id,t.requestname,t.createdate,t.createtime";
/*  693 */       String str4 = " createdate desc, createtime desc";
/*  694 */       String str5 = getFromSql(this.dataDefinitionParams, list);
/*  695 */       String str6 = SqlUtils.getPageSql(str3, str5, str4, 0, 30);
/*  696 */       RecordSet recordSet = new RecordSet();
/*  697 */       recordSet.executeQuery(str6, new Object[0]);
/*  698 */       while (recordSet.next()) {
/*  699 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  700 */         hashMap1.put(BrowserConstant.BROWSER_AUTO_COMPLETE_PRIMARY_KEY, recordSet.getString("id"));
/*  701 */         hashMap1.put(BrowserConstant.BROWSER_AUTO_COMPLETE_SHOW_NAME, recordSet.getString("requestname"));
/*  702 */         arrayList.add(hashMap1);
/*      */       } 
/*      */     } 
/*  705 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  706 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> reSetDataDefinitionParams() {
/*  713 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  714 */     for (Map.Entry entry : this.dataDefinitionParams.entrySet()) {
/*  715 */       if (!"".equals(Util.null2String(entry.getValue()))) {
/*  716 */         hashMap.put(entry.getKey(), entry.getValue());
/*      */       }
/*      */     } 
/*  719 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private String getFromSql(Map<String, Object> paramMap, List<String> paramList) {
/*  725 */     String str1 = paramList.get(0);
/*  726 */     String str2 = paramList.get(1);
/*      */     
/*  728 */     StringBuilder stringBuilder = new StringBuilder();
/*  729 */     stringBuilder.append(" where 1=1 ").append(Util.null2String(paramMap.get("spellSqlWhere")));
/*      */     
/*  731 */     String str3 = Util.null2String(paramMap.get("importWfBrow"));
/*  732 */     String str4 = String.valueOf(this.user.getUID());
/*  733 */     int i = this.user.getUID();
/*  734 */     RecordSet recordSet = new RecordSet();
/*  735 */     HrmUserSettingComInfo hrmUserSettingComInfo = new HrmUserSettingComInfo();
/*  736 */     String str5 = hrmUserSettingComInfo.getBelongtoshow(str4 + "");
/*  737 */     String str6 = "0";
/*  738 */     if ("2".equals(this.user.getLogintype())) str6 = "1"; 
/*  739 */     int j = Util.getIntValue(Util.null2String(paramMap.get("formid")), 0);
/*  740 */     int k = Util.getIntValue(Util.null2String(paramMap.get("isbill")), -1);
/*  741 */     if (j != 0 && k != -1) {
/*  742 */       stringBuilder.append(" and workflow_base.formid = ").append(j).append(" and workflow_base.isbill = ").append(k);
/*      */     }
/*  744 */     stringBuilder.append(str2);
/*  745 */     String str7 = Util.null2String(paramMap.get("requestname"));
/*  746 */     String str8 = Util.null2String(paramMap.get("secretLevel"));
/*  747 */     String str9 = Util.null2String(paramMap.get("creater"));
/*  748 */     String str10 = Util.null2String(paramMap.get("requestmark"));
/*  749 */     String str11 = Util.null2String(paramMap.get("prjids"));
/*  750 */     String str12 = Util.null2String(paramMap.get("crmids"));
/*  751 */     String str13 = Util.null2String(paramMap.get("workflowid"));
/*  752 */     String str14 = Util.null2String(paramMap.get("department"));
/*  753 */     String str15 = Util.null2String(paramMap.get("status"));
/*  754 */     String str16 = Util.null2String(paramMap.get("subid"));
/*  755 */     int m = 0;
/*  756 */     String str17 = Util.null2String(recordSet.getPropValue("wfdateduring", "wfdateduring"));
/*  757 */     String[] arrayOfString = Util.TokenizerString2(str17, ",");
/*  758 */     if (arrayOfString.length > 0) {
/*  759 */       m = Util.getIntValue(arrayOfString[0], 0);
/*      */     }
/*  761 */     if (m < 0 || m > 36) {
/*  762 */       m = 0;
/*      */     }
/*  764 */     int n = Util.getIntValue(Util.null2String(paramMap.get("date2during")), m);
/*  765 */     if (!"".equals(str7)) {
/*  766 */       String str = Util.fromScreen2(str7, this.user.getLanguage());
/*  767 */       str = str.replace("/", "//");
/*      */       
/*  769 */       if (!"oracle".equals(recordSet.getDBType())) {
/*  770 */         str = str.replace("[", "/[");
/*  771 */         str = str.replace("]", "/]");
/*      */       } 
/*      */       
/*  774 */       str = str.replace("_", "/_");
/*  775 */       str = str.replace("%", "/%");
/*      */       
/*  777 */       if (Util.isEnableMultiLang()) {
/*  778 */         if ("sqlserver".equals(recordSet.getDBType())) {
/*  779 */           stringBuilder.append(" and dbo.convToMultiLang(requestnamenew," + this.user.getLanguage() + ") like '%").append(str).append("%'");
/*      */         } else {
/*  781 */           stringBuilder.append(" and convToMultiLang(requestnamenew," + this.user.getLanguage() + ") like '%").append(str).append("%'");
/*      */         } 
/*      */       } else {
/*  784 */         stringBuilder.append(" and requestnamenew like '%").append(str).append("%'");
/*      */       } 
/*      */       
/*  787 */       stringBuilder.append(" escape '/' ");
/*      */     } 
/*  789 */     if (!"".equals(str13) && !"0".equals(str13)) {
/*  790 */       stringBuilder.append(" and workflow_requestbase.workflowid in (").append(WorkflowVersion.getAllVersionStringByWFIDs(str13)).append(")");
/*      */     }
/*  792 */     if (!"".equals(str8) && HrmClassifiedProtectionBiz.isOpenClassification()) {
/*  793 */       stringBuilder.append(" and workflow_requestbase.seclevel >= " + str8 + " ");
/*      */     }
/*  795 */     if (!"".equals(str10)) {
/*  796 */       stringBuilder.append(" and requestmark like '%").append(str10).append("%'");
/*      */     }
/*  798 */     if ("1".equals(str3)) {
/*  799 */       String str = Util.null2String(recordSet.getPropValue("workflowbase", "importWfOnlyYouCreateWfids"));
/*      */       
/*  801 */       boolean bool1 = false;
/*  802 */       if (!"".equals(str)) {
/*  803 */         String str19 = WorkflowVersion.getAllVersionStringByWFIDs(str);
/*  804 */         WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  805 */         List list = Util.splitString2List(str19, ",");
/*  806 */         for (String str20 : list) {
/*  807 */           if (Util.getIntValue(workflowAllComInfo.getFormId(str20)) == j && Util.getIntValue(workflowAllComInfo.getIsBill(str20)) == k) {
/*  808 */             bool1 = true;
/*      */             break;
/*      */           } 
/*      */         } 
/*      */       } 
/*  813 */       if (bool1) {
/*  814 */         stringBuilder.append(" and creater = ").append(this.user.getUID()).append(" and usertype=0 ");
/*      */       } else {
/*  816 */         stringBuilder.append(" and workflow_currentoperator.userid  = ").append(this.user.getUID()).append(" and creatertype=" + str6 + " ");
/*      */       } 
/*      */     } 
/*  819 */     if (!"".equals(str9))
/*      */     {
/*  821 */       stringBuilder.append(" and creater in (").append(str9).append(") and creatertype=" + str6 + " ");
/*      */     }
/*  823 */     if (!"".equals(str14) && !"0".equals(str14)) {
/*  824 */       stringBuilder.append(" and workflow_requestbase.creater in (select id from hrmresource where departmentid in (").append(str14).append("))");
/*      */     }
/*  826 */     if (!"".equals(str16) && !"0".equals(str16)) {
/*  827 */       stringBuilder.append(" and workflow_requestbase.creater in (select id from hrmresource where subcompanyid1 in (").append(str16).append("))");
/*      */     }
/*      */     try {
/*  830 */       stringBuilder.append(AdvanceSerarchUtil.handDateCondition("createdatetype", "createdatestart", "createdateend", "createdate", paramMap));
/*  831 */     } catch (ParseException parseException) {
/*  832 */       parseException.printStackTrace();
/*      */     } 
/*  834 */     if (!"".equals(str11) && !"0".equals(str11)) {
/*  835 */       String[] arrayOfString1 = str11.split(",");
/*  836 */       if (arrayOfString1.length > 0) {
/*  837 */         stringBuilder.append(" AND (");
/*  838 */         if ("oracle".equals(recordSet.getDBType())) {
/*  839 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  840 */             if (b > 0) {
/*  841 */               stringBuilder.append(" OR ");
/*      */             }
/*  843 */             stringBuilder.append("(concat(concat(',' , To_char(workflow_requestbase.prjids)) , ',') LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           } 
/*  845 */         } else if ("mysql".equals(recordSet.getDBType())) {
/*  846 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  847 */             if (b > 0) {
/*  848 */               stringBuilder.append(" OR ");
/*      */             }
/*  850 */             stringBuilder.append("(concat(',' , workflow_requestbase.prjids , ',') LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           }
/*      */         
/*  853 */         } else if ("postgresql".equals(recordSet.getDBType())) {
/*  854 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  855 */             if (b > 0) {
/*  856 */               stringBuilder.append(" OR ");
/*      */             }
/*  858 */             stringBuilder.append("(concat(concat(',' , To_char(workflow_requestbase.prjids)) , ',') LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           } 
/*      */         } else {
/*      */           
/*  862 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  863 */             if (b > 0) {
/*  864 */               stringBuilder.append(" OR ");
/*      */             }
/*  866 */             stringBuilder.append("(',' + CONVERT(varchar,workflow_requestbase.prjids) + ',' LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           } 
/*      */         } 
/*  869 */         stringBuilder.append(") ");
/*      */       } 
/*      */     } 
/*  872 */     if (!"".equals(str12) && !"0".equals(str12)) {
/*  873 */       String[] arrayOfString1 = str12.split(",");
/*  874 */       if (arrayOfString1.length > 0) {
/*  875 */         stringBuilder.append(" AND (");
/*  876 */         if ("oracle".equals(recordSet.getDBType())) {
/*  877 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  878 */             if (b > 0) {
/*  879 */               stringBuilder.append(" OR ");
/*      */             }
/*  881 */             stringBuilder.append("(concat(concat(',' , To_char(workflow_requestbase.crmids)) , ',') LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           } 
/*  883 */         } else if ("mysql".equals(recordSet.getDBType())) {
/*  884 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  885 */             if (b > 0) {
/*  886 */               stringBuilder.append(" OR ");
/*      */             }
/*  888 */             stringBuilder.append("(concat(',' , workflow_requestbase.crmids , ',') LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           }
/*      */         
/*  891 */         } else if ("postgresql".equals(recordSet.getDBType())) {
/*  892 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  893 */             if (b > 0) {
/*  894 */               stringBuilder.append(" OR ");
/*      */             }
/*  896 */             stringBuilder.append("(concat(concat(',' , To_char(workflow_requestbase.crmids)) , ',') LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           } 
/*      */         } else {
/*      */           
/*  900 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  901 */             if (b > 0) {
/*  902 */               stringBuilder.append(" OR ");
/*      */             }
/*  904 */             stringBuilder.append("(',' + CONVERT(varchar,workflow_requestbase.crmids) + ',' LIKE '%," + arrayOfString1[b] + ",%')");
/*      */           } 
/*      */         } 
/*  907 */         stringBuilder.append(") ");
/*      */       } 
/*      */     } 
/*  910 */     if ("1".equals(str15)) {
/*  911 */       stringBuilder.append(" and currentnodetype < 3 ");
/*  912 */     } else if ("2".equals(str15)) {
/*  913 */       stringBuilder.append(" and currentnodetype = 3 ");
/*      */     } 
/*  915 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  916 */     if (!"1".equals(str3)) {
/*  917 */       stringBuilder.append(workflowComInfo.getDateDuringSql(n));
/*      */     }
/*  919 */     if (" where 1=1 ".equals(stringBuilder)) {
/*  920 */       stringBuilder.append(" and workflow_requestbase.requestid <> 0");
/*      */     }
/*  922 */     if ("oracle".equals(recordSet.getDBType())) {
/*  923 */       stringBuilder.append(" and (nvl(workflow_requestbase.currentstatus,-1) = -1 or (nvl(workflow_requestbase.currentstatus,-1)=0 and workflow_requestbase.creater=" + this.user.getUID() + "))");
/*  924 */     } else if ("mysql".equals(recordSet.getDBType())) {
/*  925 */       stringBuilder.append(" and (ifnull(workflow_requestbase.currentstatus,-1) = -1 or (ifnull(workflow_requestbase.currentstatus,-1)=0 and workflow_requestbase.creater=" + this.user.getUID() + "))");
/*      */     } else {
/*  927 */       stringBuilder.append(" and (isnull(workflow_requestbase.currentstatus,-1) = -1 or (isnull(workflow_requestbase.currentstatus,-1)=0 and workflow_requestbase.creater=" + this.user.getUID() + "))");
/*      */     } 
/*      */     
/*  930 */     boolean bool = "1".equals(Util.null2String(paramMap.get("collReq")));
/*  931 */     if (bool) {
/*  932 */       stringBuilder.append(" and exists (select 1 from SysFavourite t1 where t1.favouriteobjid  = workflow_requestbase.requestid and t1.favouritetype = 2 and t1.resourceid = " + this.user.getUID() + ") ");
/*      */     }
/*      */     
/*  935 */     int i1 = Util.getIntValue(Util.null2String(paramMap.get("isFormEMShare")), -1);
/*  936 */     if (i1 == 1) {
/*  937 */       String str19 = ServiceUtil.getDBJudgeNullFun(recordSet.getDBType()) + "(workflow_base.isencryptshare, '0')";
/*  938 */       String str20 = ServiceUtil.getDBJudgeNullFun(recordSet.getDBType()) + "(workflow_base.encryptrange, '0')";
/*  939 */       stringBuilder.append(" and (workflow_base.isencryptshare = '' or " + str19 + "='0' or (" + str19 + "='1' and " + str20 + "='1'))");
/*      */     } 
/*      */     
/*  942 */     String str18 = "";
/*  943 */     str1 = StringUtils.isEmpty(str1) ? "" : ("," + str1);
/*  944 */     if ("1".equals(str5)) {
/*  945 */       if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("db2"))
/*      */       {
/*      */         
/*  948 */         str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid in (" + i + ") and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */       
/*      */       }
/*  951 */       else if (recordSet.getDBType().equals("mysql"))
/*      */       {
/*      */         
/*  954 */         str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid in (" + i + ") and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */       
/*      */       }
/*  957 */       else if (recordSet.getDBType().equals("postgresql"))
/*      */       {
/*      */         
/*  960 */         str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid in (" + i + ") and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */       
/*      */       }
/*      */       else
/*      */       {
/*      */         
/*  966 */         str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid in (" + i + ") and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */       
/*      */       }
/*      */     
/*      */     }
/*  971 */     else if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("db2")) {
/*      */ 
/*      */       
/*  974 */       str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid=" + i + " and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */     
/*      */     }
/*  977 */     else if (recordSet.getDBType().equals("mysql")) {
/*      */ 
/*      */       
/*  980 */       str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid=" + i + " and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */     
/*      */     }
/*  983 */     else if (recordSet.getDBType().equals("postgresql")) {
/*      */ 
/*      */       
/*  986 */       str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid=" + i + " and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */       
/*  992 */       str18 = " from ( select distinct workflow_requestbase.requestid as id,requestnamenew,requestname,creater,creatertype,createdate,createtime from workflow_requestbase , workflow_currentoperator , workflow_base" + str1 + stringBuilder.toString() + " and workflow_currentoperator.requestid = workflow_requestbase.requestid and workflow_currentoperator.userid=" + i + " and workflow_currentoperator.usertype=" + str6 + " and workflow_requestbase.workflowid = workflow_base.id and (workflow_base.isvalid='1' or workflow_base.isvalid='3')  ) t ";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  997 */     return str18;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void eSearchParamsTrans(Map<String, Object> paramMap) {
/* 1010 */     if (paramMap.containsKey("secretLevel")) {
/* 1011 */       String str = Util.null2String(paramMap.get("secretLevel"));
/* 1012 */       paramMap.put("secretLevel", (new RequestSecLevelBiz()).getFullSecLevel(Util.getIntValue(str)));
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/RequestBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */