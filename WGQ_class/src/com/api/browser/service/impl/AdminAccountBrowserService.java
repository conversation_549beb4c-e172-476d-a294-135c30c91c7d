/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AdminAccountBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  32 */     String str1 = Util.null2String(paramMap.get("lastname"));
/*  33 */     String str2 = Util.null2String(paramMap.get("description"));
/*  34 */     String str3 = Util.null2String(paramMap.get("isFromErrorRemind"));
/*     */     
/*  36 */     String str4 = Util.null2String(paramMap.get("fromStr"));
/*  37 */     String str5 = "";
/*  38 */     if (!str1.equals("")) {
/*  39 */       str5 = str5 + " and lastname like '%";
/*  40 */       str5 = str5 + Util.fromScreen2(str1, 7);
/*  41 */       str5 = str5 + "%'";
/*     */     } 
/*  43 */     if (!str2.equals("")) {
/*  44 */       str5 = str5 + " and description like '%";
/*  45 */       str5 = str5 + Util.fromScreen2(str2, 7);
/*  46 */       str5 = str5 + "%'";
/*     */     } 
/*  48 */     if (str4.equalsIgnoreCase("roleMembers") || !HrmSanyuanAdminBiz.getSanyuanAble()) {
/*  49 */       str5 = str5 + " and (sanyuanType is null or sanyuanType not in (1,2,3)) ";
/*     */     }
/*  51 */     if (this.user.getUID() != 1) {
/*  52 */       str5 = str5 + " and (id=" + this.user.getUID() + " or creator=" + this.user.getUID() + ") ";
/*     */     }
/*  54 */     if (!(new ManageDetachComInfo()).getDetachable().equals("1"))
/*     */     {
/*     */       
/*  57 */       str5 = str5 + " and id = 1 ";
/*     */     }
/*     */     
/*  60 */     str5 = SqlUtils.replaceFirstAnd(str5);
/*  61 */     if (str3.equals("1")) {
/*  62 */       str5 = "";
/*     */     }
/*     */     
/*  65 */     String str6 = " id ,lastname,description ";
/*  66 */     String str7 = " HrmResourceManager ";
/*     */     
/*  68 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  69 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  70 */     arrayList.add((new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "lastname", "lastname", 1)).setIsInputCol(BoolAttr.TRUE));
/*  71 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*     */     
/*  73 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str5, "id", "id", arrayList);
/*  74 */     splitTableBean.setSqlsortway("ASC");
/*  75 */     splitTableBean.setSqlisdistinct("true");
/*  76 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  77 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  82 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  83 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  84 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  85 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  86 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "lastname", true));
/*  87 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/*  88 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  95 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  96 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  97 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  98 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/*  99 */     RecordSet recordSet = new RecordSet();
/* 100 */     String str2 = "select  id ,lastname,description from HrmResourceManager where id in (" + str1 + ")";
/* 101 */     recordSet.executeQuery(str2, new Object[0]);
/* 102 */     while (recordSet.next()) {
/* 103 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 104 */       hashMap1.put("id", recordSet.getString("id"));
/* 105 */       hashMap1.put("lastname", Util.null2String(recordSet.getString("lastname")));
/* 106 */       hashMap1.put("description", Util.null2String(recordSet.getString("description")));
/* 107 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 110 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 111 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 112 */     arrayList1.add(new ListHeadBean("lastname", "", 1, BoolAttr.TRUE));
/* 113 */     arrayList1.add(new ListHeadBean("description", "", 1, BoolAttr.TRUE));
/*     */     
/* 115 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 116 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 117 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 118 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AdminAccountBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */