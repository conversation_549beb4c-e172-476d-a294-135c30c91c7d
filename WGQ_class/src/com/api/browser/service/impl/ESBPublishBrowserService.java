/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ESBPublishBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     String str1 = Util.null2String(paramMap.get("publishid"));
/* 31 */     String str2 = Util.null2String(paramMap.get("publishname"));
/*    */ 
/*    */     
/* 34 */     String str3 = "PUBLISHID,PUBLISHNAME,STATE,VERSION,CREATEDATE,CREATETIME";
/* 35 */     String str4 = " ESB_PUBLISH ";
/*    */     
/* 37 */     String str5 = " where STATE = '1' ";
/*    */     
/* 39 */     if (!str1.isEmpty()) {
/* 40 */       str5 = str5 + " and PUBLISHID like '%" + str1 + "%' ";
/*    */     }
/* 42 */     if (!str2.isEmpty()) {
/* 43 */       str5 = str5 + " and PUBLISHNAME like '%" + str2 + "%'";
/*    */     }
/* 45 */     String str6 = " CREATEDATE DESC, CREATETIME DESC ";
/* 46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 47 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "publishid", "publishid")).setShowType(1).setIsInputCol(BoolAttr.TRUE));
/* 48 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "publishname", "publishname")).setShowType(0));
/* 49 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(567, this.user.getLanguage()), "version", "version")).setShowType(0));
/*    */     
/* 51 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str5, str6, "PUBLISHID", "desc", arrayList);
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 61 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 62 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 63 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 84, "publishid", true));
/* 64 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "publishname", false));
/*    */     
/* 66 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 67 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ESBPublishBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */