/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ModeTabsBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  32 */     String str1 = Util.null2String(paramMap.get("tabname"));
/*  33 */     String str2 = Util.null2String(paramMap.get("modeid"));
/*  34 */     String str3 = "where 1 = 1 ";
/*  35 */     String str4 = "";
/*  36 */     String str5 = "";
/*  37 */     String str6 = "";
/*  38 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  39 */     SplitTableBean splitTableBean = null;
/*  40 */     if ("modeTab".equals(this.browserType)) {
/*  41 */       str4 = "id,tabname,orderid";
/*  42 */       str5 = " from modeTabs_" + str2;
/*  43 */       str6 = "orderid";
/*  44 */       str3 = " where 1=1 ";
/*  45 */       String str8 = Util.null2String(paramMap.get("id"));
/*  46 */       String str9 = Util.null2String(paramMap.get("jobactivitieid"));
/*  47 */       if (!str8.equals("")) {
/*  48 */         str3 = str3 + " and id =" + str8;
/*     */       }
/*  50 */       if (!str9.equals("")) {
/*  51 */         str3 = str3 + " and b.id= " + str9;
/*     */       }
/*  53 */       if (!str1.equals("")) {
/*  54 */         str3 = str3 + " and tabname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */       }
/*  56 */       arrayList.add(new SplitTableColBean("true", "id"));
/*  57 */       arrayList.add((new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(81323, this.user.getLanguage()), "tabname", "tabname", 1)).setIsInputCol(BoolAttr.TRUE));
/*  58 */       splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/*     */     } 
/*  60 */     splitTableBean.setDatasource("com.engine.cube.cmd.browser.ModeTabBrowserData.getTabData");
/*  61 */     String str7 = "select " + str4 + str5 + str3;
/*  62 */     splitTableBean.setSourceparams("sql:" + Util.toHtmlForSplitPage(str7) + "+tabname:" + str1);
/*  63 */     splitTableBean.setSqlisdistinct("true");
/*  64 */     splitTableBean.setInstanceid("TabTable");
/*  65 */     splitTableBean.setPageUID("Tab123456");
/*  66 */     splitTableBean.setPagesize("10");
/*  67 */     splitTableBean.setTableType("none");
/*  68 */     splitTableBean.setSqlsortway("DESC");
/*  69 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  70 */     hashMap.put("sessionkey", hashMap.get(BrowserConstant.BROWSER_RESULT_DATA));
/*  71 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  76 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  77 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  78 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  79 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  80 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 81323, "tabname", true));
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  88 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  89 */     String str2 = Util.null2String(paramMap.get("modeid"));
/*  90 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/*  91 */     RecordSet recordSet = new RecordSet();
/*  92 */     String str3 = " select id,tabname  from modeTabs_" + str2 + " where id in (" + str1 + ") order by orderid ";
/*     */ 
/*     */ 
/*     */     
/*  96 */     recordSet.executeQuery(str3, new Object[0]);
/*  97 */     if (str1.contains("-1")) {
/*  98 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  99 */       hashMap1.put("id", "-1");
/* 100 */       hashMap1.put("tabname", "" + SystemEnv.getHtmlLabelName(503109, ThreadVarLanguage.getLang()) + "");
/* 101 */       arrayList.add(hashMap1);
/*     */     } 
/* 103 */     while (recordSet.next()) {
/* 104 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 105 */       hashMap1.put("id", recordSet.getString("id"));
/* 106 */       hashMap1.put("tabname", Util.null2String(recordSet.getString("tabname")));
/* 107 */       arrayList.add(hashMap1);
/*     */     } 
/* 109 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 110 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 111 */     arrayList1.add(new ListHeadBean("tabname", "", 1, BoolAttr.TRUE));
/* 112 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 113 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 114 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 115 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 120 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 121 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 122 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("modeid"));
/* 123 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 124 */     if (StringHelper.isEmpty(str1)) return (Map)hashMap; 
/* 125 */     RecordSet recordSet = new RecordSet();
/* 126 */     String str3 = " select id,tabname  from modeTabs_" + str1 + " where tabname like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' order by orderid ";
/* 127 */     recordSet.executeQuery(str3, new Object[0]);
/* 128 */     while (recordSet.next()) {
/* 129 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 130 */       hashMap1.put("id", recordSet.getString("id"));
/* 131 */       hashMap1.put("name", Util.null2String(recordSet.getString("tabname")));
/* 132 */       hashMap1.put("tabname", Util.null2String(recordSet.getString("tabname")));
/* 133 */       arrayList.add(hashMap1);
/*     */     } 
/* 135 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 136 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeTabsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */