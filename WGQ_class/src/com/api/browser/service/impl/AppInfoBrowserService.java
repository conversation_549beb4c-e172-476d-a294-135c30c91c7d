/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import com.engine.systeminfo.util.AppManageTransmethod;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class AppInfoBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  27 */     ArrayList arrayList = new ArrayList();
/*  28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  29 */     String str1 = "t1.id, t1.appname, t1.apptype, t1.cus_homeurl, t1.img_url, t1.description, t2.homeurl, t3.name as moduleLabel, t2.homeurl as appurl";
/*  30 */     RecordSet recordSet = new RecordSet();
/*  31 */     if (recordSet.getDBType().equalsIgnoreCase("sqlserver")) {
/*  32 */       str1 = "t1.id, t1.appname, t1.apptype, t1.cus_homeurl, t1.img_url, t1.description, t2.homeurl, t3.name as moduleLabel, t2.homeurl as appurl";
/*     */     }
/*  34 */     String str2 = " ecology_biz_app t1 left join ecology_biz_app_type t2 on t1.apptype=t2.id left join ecology_biz_app_module t3 on t2.module=t3.id";
/*     */     
/*  36 */     String str3 = " t1.status='y'";
/*  37 */     str3 = str3 + " and (" + Util.getSubINClause(StringUtils.join(AppManageTransmethod.checkUnstandard(), ","), "t2.id", "in") + ")";
/*  38 */     String str4 = "t1.id";
/*  39 */     String str5 = "id";
/*  40 */     String str6 = "asc";
/*  41 */     String str7 = String.valueOf(this.user.getLanguage());
/*     */ 
/*     */     
/*  44 */     String str8 = Util.null2String(paramMap.get("appname"));
/*  45 */     String str9 = Util.null2String(paramMap.get("description"));
/*  46 */     String str10 = Util.null2String(paramMap.get("module"));
/*  47 */     if (StringUtils.isNotBlank(str8)) {
/*  48 */       str3 = str3 + " and t1.appname like '%" + str8 + "%'";
/*     */     }
/*  50 */     if (StringUtils.isNotBlank(str9)) {
/*  51 */       str3 = str3 + " and t1.description like '%" + str9 + "%'";
/*     */     }
/*  53 */     if (StringUtils.isNotBlank(str10)) {
/*  54 */       str3 = str3 + " and t3.id='" + str10 + "'";
/*     */     }
/*     */     
/*  57 */     ArrayList<SplitTableColBean> arrayList1 = new ArrayList();
/*  58 */     SplitTableColBean splitTableColBean1 = new SplitTableColBean("true", "id");
/*  59 */     SplitTableColBean splitTableColBean2 = new SplitTableColBean("true", "apptype");
/*  60 */     SplitTableColBean splitTableColBean3 = new SplitTableColBean("true", "cus_homeurl");
/*  61 */     SplitTableColBean splitTableColBean4 = new SplitTableColBean("true", "appurl");
/*  62 */     SplitTableColBean splitTableColBean5 = new SplitTableColBean("0%", "img_url", "img_url", null, "com.engine.systeminfo.util.TransMethod_AppPage.getUploadDatas");
/*  63 */     SplitTableColBean splitTableColBean6 = new SplitTableColBean("0%", "homeurl", "homeurl", null, "com.engine.systeminfo.util.TransMethod_AppPage.getHomeurl", "column:id+column:apptype+column:homeurl+column:cus_homeurl");
/*  64 */     SplitTableColBean splitTableColBean7 = (new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(381923, this.user.getLanguage()), "appname", "appname")).setIsInputCol(BoolAttr.TRUE);
/*  65 */     SplitTableColBean splitTableColBean8 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()), "moduleLabel", "moduleLabel", "com.engine.systeminfo.util.AppManageTransmethod.getModule", str7);
/*  66 */     splitTableColBean7.setShowType(1);
/*  67 */     splitTableColBean8.setShowType(0);
/*  68 */     splitTableColBean6.setTransMethodForce("true");
/*  69 */     splitTableColBean5.setTransMethodForce("true");
/*  70 */     splitTableColBean6.setDisplay("false");
/*  71 */     splitTableColBean5.setDisplay("false");
/*  72 */     splitTableColBean6.setHide("true");
/*  73 */     splitTableColBean5.setHide("true");
/*     */     
/*  75 */     arrayList1.add(splitTableColBean1);
/*  76 */     arrayList1.add(splitTableColBean2);
/*  77 */     arrayList1.add(splitTableColBean3);
/*  78 */     arrayList1.add(splitTableColBean4);
/*  79 */     arrayList1.add(splitTableColBean6);
/*  80 */     arrayList1.add(splitTableColBean5);
/*  81 */     arrayList1.add(splitTableColBean7);
/*  82 */     arrayList1.add(splitTableColBean8);
/*  83 */     arrayList1.add(new SplitTableColBean("true", "description"));
/*     */     
/*  85 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, str4, str5, str6, arrayList1);
/*  86 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  87 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  93 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/*  94 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  95 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/*  96 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/*  99 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 195, "appname", true);
/* 100 */     arrayList1.add(searchConditionItem1);
/*     */ 
/*     */     
/* 103 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 33522, "module", "appModule");
/* 104 */     arrayList1.add(searchConditionItem2);
/*     */ 
/*     */     
/* 107 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.INPUT, 381882, "description");
/* 108 */     arrayList1.add(searchConditionItem3);
/*     */     
/* 110 */     arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(383122, Util.getIntValue(this.user.getLanguage())), true, arrayList1));
/* 111 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 112 */     return weaResultMsg.getResultMapAll();
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 117 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 118 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 119 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 120 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 121 */     String str2 = Util.null2String(paramMap.get("appname"));
/* 122 */     RecordSet recordSet = new RecordSet();
/* 123 */     String str3 = "select t1.id,t1.appname,t3.name as moduleLabel,t3.description from ecology_biz_app t1 left join ecology_biz_app_type t2 on t1.apptype=t2.id left join ecology_biz_app_module t3 on t2.module=t3.id where 1=1 and id in (" + str1 + ")";
/* 124 */     if (StringUtils.isNotBlank(str2)) {
/* 125 */       str3 = str3 + " and appname like '%" + str2 + "%'";
/*     */     }
/* 127 */     recordSet.executeQuery(str3, new Object[0]);
/* 128 */     while (recordSet.next()) {
/* 129 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 130 */       String str4 = Util.null2String(recordSet.getString("id"));
/* 131 */       String str5 = Util.null2String(recordSet.getString("appname"));
/* 132 */       String str6 = Util.null2String(recordSet.getString("moduleLabel"));
/* 133 */       hashMap1.put("id", str4);
/* 134 */       hashMap1.put("appname", str5);
/* 135 */       hashMap1.put("module", SystemEnv.getHtmlLabelName(Integer.parseInt(str6), this.user.getLanguage()));
/* 136 */       arrayList.add(hashMap1);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 147 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 148 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 149 */     arrayList1.add(new ListHeadBean("appname", "", 1, BoolAttr.TRUE));
/* 150 */     arrayList1.add(new ListHeadBean("module", ""));
/*     */     
/* 152 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 153 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 154 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 155 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AppInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */