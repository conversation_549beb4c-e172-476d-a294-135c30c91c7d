/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class HrmTrainTypeBrowserService
/*    */   extends BrowserService {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 21 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 22 */     String str1 = Util.null2String(paramMap.get("name"));
/* 23 */     String str2 = Util.null2String(paramMap.get("description"));
/*    */     
/* 25 */     String str3 = "id,name,description";
/* 26 */     String str4 = " from HrmTrainType ";
/* 27 */     String str5 = " where 1=1 ";
/* 28 */     if (!str1.equals("")) {
/* 29 */       str5 = str5 + " and name like '%";
/* 30 */       str5 = str5 + Util.fromScreen2(str1, this.user.getLanguage());
/* 31 */       str5 = str5 + "%'";
/*    */     } 
/* 33 */     if (!str2.equals("")) {
/* 34 */       str5 = str5 + " and description like '%";
/* 35 */       str5 = str5 + Util.fromScreen2(str2, this.user.getLanguage());
/* 36 */       str5 = str5 + "%'";
/*    */     } 
/* 38 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 39 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 40 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", null)).setIsInputCol(BoolAttr.TRUE));
/* 41 */     arrayList.add(new SplitTableColBean("70%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", null));
/*    */     
/* 43 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, Util.toHtmlForSplitPage(str5), "id", "id", arrayList);
/* 44 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 45 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 50 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 51 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 52 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 53 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true));
/* 54 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description", false));
/* 55 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 56 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmTrainTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */