/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.workflow.constant.FieldHtmlType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.docs.CustomDictManager;
/*     */ import weaver.general.KnowledgeTransMethod;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocCustomFieldService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) {
/*  42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  44 */     String str1 = Util.null2String(paramMap.get("scope"));
/*  45 */     if (str1.equals("")) str1 = "DocCustomFieldBySecCategory"; 
/*  46 */     String str2 = "";
/*  47 */     RecordSet recordSet1 = new RecordSet();
/*  48 */     KnowledgeTransMethod knowledgeTransMethod = new KnowledgeTransMethod();
/*     */     
/*  50 */     String str3 = Util.null2String(paramMap.get("fieldlabel"));
/*  51 */     String str4 = Util.null2String(paramMap.get("fieldname"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  62 */     String str5 = Util.null2String(paramMap.get("type"));
/*  63 */     String str6 = Util.null2String(paramMap.get("fieldhtmltype"));
/*  64 */     String str7 = Util.null2s((String)paramMap.get("categoryid"), "");
/*  65 */     String str8 = Util.null2s((String)paramMap.get("limitTxt"), "");
/*     */     
/*  67 */     String str9 = "where (scope='" + str1 + "' or id in(select fieldid from cus_formfield where scope = '" + str1 + "'))";
/*  68 */     if (!str7.isEmpty()) {
/*  69 */       str9 = "where id in (select fieldid from cus_formfield where scopeid = " + str7 + ") ";
/*     */     }
/*  71 */     boolean bool1 = true;
/*     */     
/*  73 */     if (!str3.equals("")) {
/*  74 */       if (!bool1) {
/*  75 */         bool1 = true;
/*  76 */         str9 = str9 + " where  fieldlabel like '%";
/*  77 */         str9 = str9 + str3;
/*  78 */         str9 = str9 + "%'";
/*     */       } else {
/*     */         
/*  81 */         str9 = str9 + " and fieldlabel like '%";
/*  82 */         str9 = str9 + str3;
/*  83 */         str9 = str9 + "%'";
/*     */       } 
/*     */     }
/*  86 */     if (!"".equals(str4)) {
/*  87 */       if (!bool1) {
/*  88 */         bool1 = true;
/*  89 */         str9 = str9 + " where fieldname like '%";
/*  90 */         str9 = str9 + Util.fromScreen2(str4, this.user.getLanguage());
/*  91 */         str9 = str9 + "%'";
/*     */       } else {
/*     */         
/*  94 */         str9 = str9 + " and fieldname like '%";
/*  95 */         str9 = str9 + Util.fromScreen2(str4, this.user.getLanguage());
/*  96 */         str9 = str9 + "%'";
/*     */       } 
/*     */     }
/*     */     
/* 100 */     if (!"".equals(str6)) {
/* 101 */       if (!bool1) {
/* 102 */         bool1 = true;
/* 103 */         str9 = str9 + " where  fieldhtmltype = '";
/* 104 */         str9 = str9 + str6;
/* 105 */         str9 = str9 + "'";
/*     */       } else {
/*     */         
/* 108 */         str9 = str9 + " and fieldhtmltype = '";
/* 109 */         str9 = str9 + str6;
/* 110 */         str9 = str9 + "'";
/*     */       } 
/*     */     }
/*     */     
/* 114 */     if (!"".equals(str5)) {
/* 115 */       if (!bool1) {
/* 116 */         bool1 = true;
/* 117 */         str9 = str9 + " where  type = '";
/* 118 */         str9 = str9 + str5;
/* 119 */         str9 = str9 + "'";
/*     */       } else {
/*     */         
/* 122 */         str9 = str9 + " and type = '";
/* 123 */         str9 = str9 + str5;
/* 124 */         str9 = str9 + "'";
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 129 */     boolean bool2 = false;
/*     */     
/* 131 */     int i = Util.getIntValue(Util.null2String(paramMap.get("pageSize")), 10);
/* 132 */     int j = Util.getIntValue(Util.null2String(paramMap.get("currentPage")), 1);
/* 133 */     int k = Util.getIntValue(Util.null2String(paramMap.get("min")), 1);
/*     */ 
/*     */ 
/*     */     
/* 137 */     j = k / i + 1;
/*     */     
/* 139 */     String str10 = "select * from cus_formdict " + str9;
/* 140 */     recordSet1.execute(str10);
/* 141 */     int m = recordSet1.getCounts();
/*     */ 
/*     */     
/* 144 */     RecordSet recordSet2 = (new CustomDictManager()).getSelectResult(str9, j, i);
/* 145 */     JSONArray jSONArray = new JSONArray();
/* 146 */     RecordSet recordSet3 = new RecordSet();
/* 147 */     String str11 = "select fieldhtmltype,fieldname from cus_formdict where id = ? and scope = ?";
/* 148 */     byte b = 0;
/* 149 */     while (recordSet2.next()) {
/* 150 */       JSONObject jSONObject = new JSONObject();
/* 151 */       jSONObject.put("id", recordSet2.getString("id"));
/* 152 */       str2 = Util.null2String(recordSet2.getString("fieldlabel"));
/* 153 */       if (str2.equals("")) str2 = "field" + recordSet2.getString("id"); 
/* 154 */       jSONObject.put("fieldlabel", str2);
/* 155 */       jSONObject.put("type", knowledgeTransMethod.getCusFieldType(recordSet2.getString("fieldhtmltype"), recordSet2.getString("type") + "+" + this.user.getLanguage()));
/* 156 */       if ("1".equals(str8)) {
/* 157 */         recordSet3.executeQuery(str11, new Object[] { recordSet2.getString("id"), "DocCustomFieldBySecCategory" });
/* 158 */         if (recordSet3.next()) {
/* 159 */           String str = Util.null2String(recordSet3.getString("fieldhtmltype"));
/* 160 */           if ("1".equals(str) || "2".equals(str)) {
/* 161 */             jSONArray.add(jSONObject); continue;
/*     */           } 
/* 163 */           b++;
/*     */         } 
/*     */         continue;
/*     */       } 
/* 167 */       jSONArray.add(jSONObject);
/*     */     } 
/*     */ 
/*     */     
/* 171 */     recordSet2.writeLog(">>>>>>>>>111 categoryid=" + str7 + "  RecordSetCounts=" + m + "   limitCount=" + b + "  perpage=" + i);
/* 172 */     m -= b;
/* 173 */     int n = m / i;
/*     */     
/* 175 */     if (n % i > 0 || n == 0) {
/* 176 */       n++;
/*     */     }
/* 178 */     recordSet2.writeLog(">>>>>>>>>2222 categoryid=" + str7 + "  RecordSetCounts=" + m + "   limitCount=" + b + "  perpage=" + i + "  totalPage=" + n);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 183 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 184 */     arrayList.add((new ListHeadBean("id", BoolAttr.FALSE)).setIsPrimarykey(BoolAttr.TRUE));
/* 185 */     arrayList.add((new ListHeadBean("fieldlabel", "", 1)).setIsInputCol(BoolAttr.TRUE));
/* 186 */     arrayList.add(new ListHeadBean("type", "", 0));
/*     */     
/* 188 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/* 189 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray);
/* 190 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA_O.getTypeid()));
/* 191 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TOTAL, Integer.valueOf(m));
/* 192 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CURRENT_PAGE, Integer.valueOf(n));
/* 193 */     hashMap.put(BrowserConstant.BROWSER_RESULT_PAGESIZE, Integer.valueOf(i));
/* 194 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 199 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 200 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 201 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 202 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30828, "fieldlabel", true));
/* 203 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 23241, "fieldname"));
/*     */ 
/*     */     
/* 206 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 687, "fieldhtmltype");
/* 207 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 208 */     arrayList1.add(new SearchConditionOption("", ""));
/* 209 */     arrayList1.add(new SearchConditionOption(FieldHtmlType.SINGLELINE.getIdStr(), FieldHtmlType.SINGLELINE.getLabelName(this.user.getLanguage())));
/* 210 */     arrayList1.add(new SearchConditionOption(FieldHtmlType.MUTILALINE.getIdStr(), FieldHtmlType.MUTILALINE.getLabelName(this.user.getLanguage())));
/* 211 */     arrayList1.add(new SearchConditionOption(FieldHtmlType.BROWSER.getIdStr(), FieldHtmlType.BROWSER.getLabelName(this.user.getLanguage())));
/* 212 */     arrayList1.add(new SearchConditionOption(FieldHtmlType.CHECK.getIdStr(), FieldHtmlType.CHECK.getLabelName(this.user.getLanguage())));
/* 213 */     arrayList1.add(new SearchConditionOption(FieldHtmlType.SELECT.getIdStr(), FieldHtmlType.SELECT.getLabelName(this.user.getLanguage())));
/* 214 */     searchConditionItem1.setOptions(arrayList1);
/*     */     
/* 216 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */     
/* 219 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, "84113", "type");
/* 220 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 221 */     arrayList2.add(new SearchConditionOption("", ""));
/* 222 */     arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(608, this.user.getLanguage())));
/* 223 */     arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(696, this.user.getLanguage())));
/* 224 */     arrayList2.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(697, this.user.getLanguage())));
/* 225 */     searchConditionItem2.setOptions(arrayList2);
/* 226 */     hashMap2.put(FieldHtmlType.SINGLELINE.getIdStr(), searchConditionItem2);
/*     */ 
/*     */     
/* 229 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.SELECT, "84113", "type");
/* 230 */     searchConditionItem3.setOptions(getBrowserLinkOptions());
/* 231 */     hashMap2.put(FieldHtmlType.BROWSER.getIdStr(), searchConditionItem3);
/*     */ 
/*     */ 
/*     */     
/* 235 */     searchConditionItem1.setSelectLinkageDatas(hashMap2);
/*     */     
/* 237 */     arrayList.add(searchConditionItem1);
/*     */     
/* 239 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 240 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */   
/*     */   private List<SearchConditionOption> getBrowserLinkOptions() {
/* 245 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 246 */     arrayList.add(new SearchConditionOption("", ""));
/*     */     
/* 248 */     String str1 = "10, 11, 64, 6, 56, 5, 3, 26,235,242,243,246,224,225,14,15,267,261,258,264,265,33,266";
/*     */     
/* 250 */     String str2 = "select w.typeid as groupid,w.id as itemid,w.labelid as itemlabel,w.orderid as orderid from  workflow_browserurl w left join HtmlLabelInfo h on w.labelid=h.indexid where h.languageid=" + this.user.getLanguage() + " and w.id not in (" + str1 + ") and w.browserurl is not null and w.useable = 1 and w.browserurl is not null order by groupid,orderid asc";
/*     */     
/* 252 */     RecordSet recordSet = new RecordSet();
/* 253 */     recordSet.execute(str2);
/*     */     
/* 255 */     while (recordSet.next()) {
/* 256 */       String str = recordSet.getString("itemid");
/* 257 */       int i = Util.getIntValue(recordSet.getString("itemlabel"), 0);
/*     */       
/* 259 */       arrayList.add(new SearchConditionOption(str, SystemEnv.getHtmlLabelName(i, this.user.getLanguage())));
/*     */     } 
/*     */     
/* 262 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 267 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 268 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 269 */     if ("".equals(str1)) return (Map)hashMap; 
/* 270 */     String str2 = "select id,fieldlabel,fieldhtmltype,type from cus_formdict where id in (" + str1 + ") order by id";
/* 271 */     RecordSet recordSet = new RecordSet();
/* 272 */     recordSet.executeSql(str2);
/* 273 */     JSONArray jSONArray = new JSONArray();
/* 274 */     KnowledgeTransMethod knowledgeTransMethod = new KnowledgeTransMethod();
/* 275 */     while (recordSet.next()) {
/* 276 */       JSONObject jSONObject = new JSONObject();
/* 277 */       jSONObject.put("id", recordSet.getString("id"));
/* 278 */       String str = Util.null2String(recordSet.getString("fieldlabel"));
/* 279 */       if (str.equals("")) str = "field" + recordSet.getString("id"); 
/* 280 */       jSONObject.put("fieldlabel", str);
/* 281 */       jSONObject.put("type", knowledgeTransMethod.getCusFieldType(recordSet.getString("fieldhtmltype"), recordSet.getString("type") + "+" + this.user.getLanguage()));
/* 282 */       jSONArray.add(jSONObject);
/*     */     } 
/*     */     
/* 285 */     ArrayList<ListHeadBean> arrayList = new ArrayList();
/* 286 */     arrayList.add((new ListHeadBean("id", BoolAttr.FALSE)).setIsPrimarykey(BoolAttr.TRUE));
/* 287 */     arrayList.add(new ListHeadBean("fieldlabel", "", 1));
/* 288 */     arrayList.add(new ListHeadBean("type", "", 0));
/*     */     
/* 290 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/* 291 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray);
/* 292 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 293 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/DocCustomFieldService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */