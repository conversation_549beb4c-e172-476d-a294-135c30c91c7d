/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.workrelate.util.TaskUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class WorkrelateTaskBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  35 */     String str1 = Util.null2String(paramMap.get("secretLevel"));
/*  36 */     String str2 = Util.null2String(paramMap.get("isparent"));
/*  37 */     String str3 = Util.null2String(paramMap.get("taskname"));
/*  38 */     String str4 = Util.null2String(paramMap.get("begindate"));
/*  39 */     String str5 = Util.null2String(paramMap.get("enddate"));
/*  40 */     String str6 = Util.null2String(paramMap.get("principalid"));
/*  41 */     String str7 = Util.null2String(paramMap.get("parentid"));
/*  42 */     String str8 = Util.null2String(paramMap.get("parentids"));
/*  43 */     String str9 = Util.null2String(paramMap.get("sqlwhere"));
/*  44 */     if ("".equals(str9)) {
/*  45 */       str9 = str9 + " where (t1.deleted=0 or t1.deleted is null) ";
/*     */     } else {
/*  47 */       str9 = str9 + " (t1.deleted=0 or t1.deleted is null) ";
/*     */     } 
/*     */     
/*  50 */     if (!str1.equals("")) {
/*  51 */       if ("1".equals(str2)) {
/*  52 */         str9 = str9 + " and (t1.secretlev <= " + str1 + " or t1.secretlev is null) ";
/*     */       } else {
/*  54 */         str9 = str9 + " and (t1.secretlev >= " + str1 + " or t1.secretlev is null) ";
/*     */       } 
/*     */     }
/*  57 */     if (!str3.equals("")) {
/*  58 */       str9 = str9 + " and t1.name like '%" + Util.fromScreen2(str3, this.user.getLanguage()) + "%' ";
/*     */     }
/*  60 */     if (!str4.equals("")) {
/*  61 */       str9 = str9 + " and t1.begindate >= '" + str4 + "'";
/*     */     }
/*  63 */     if (!str5.equals("")) {
/*  64 */       str9 = str9 + " and t1.enddate <= '" + str5 + "'";
/*     */     }
/*  66 */     if (!"".equals(str8)) {
/*  67 */       str9 = str9 + " and t1.parentid=" + str8;
/*     */     }
/*  69 */     if (!str6.equals("")) {
/*  70 */       str9 = str9 + " and t1.principalid='" + str6 + "'";
/*     */     }
/*  72 */     if (!str7.equals("")) {
/*  73 */       str9 = str9 + " and t1.id not in (select id from TM_TaskInfo t2 where t2.parentid = '" + str7 + "') and t1.id <> " + str7;
/*     */       
/*  75 */       str9 = str9 + " and ((t1.principalid=" + this.user.getUID() + " and t1.status not in(4,5)) or t1.creater=" + this.user.getUID() + " or exists (select 1 from TM_TaskPartner tp where tp.taskid=t1.id and tp.partnerid=" + this.user.getUID() + " and t1.status not in(4,5)))";
/*     */     
/*     */     }
/*     */     else {
/*     */       
/*  80 */       str9 = str9 + " and ((t1.principalid=" + this.user.getUID() + " and t1.status not in(4,5)) or t1.creater=" + this.user.getUID() + " or exists (select 1 from TM_TaskPartner tp where tp.taskid=t1.id and tp.partnerid=" + this.user.getUID() + " and t1.status not in(4,5)) or exists (select 1 from TM_TaskSharer ts where ts.taskid=t1.id and ts.sharerid=" + this.user.getUID() + " and t1.status not in(4,5))";
/*  81 */       if ("1".equals(TaskUtil.getBaseSetTask("ismanagerview", "1")))
/*     */       {
/*  83 */         str9 = str9 + " or exists (select 1 from HrmResource hrm where (hrm.id=t1.principalid or hrm.id=t1.creater) and hrm.managerstr like '%," + this.user.getUID() + ",%' and t1.status not in(4,5)) or exists (select 1 from HrmResource hrm,TM_TaskPartner tp where tp.taskid=t1.id and hrm.id=tp.partnerid and hrm.managerstr like '%," + this.user.getUID() + ",%' and t1.status not in(4,5))";
/*     */       }
/*  85 */       str9 = str9 + ")";
/*     */     } 
/*  87 */     String str10 = "t1.id,t1.name taskname,t1.principalid,t1.begindate,t1.enddate";
/*  88 */     String str11 = " TM_TaskInfo t1 " + str9 + " ";
/*     */     
/*  90 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  91 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  92 */     arrayList.add((new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(1352, this.user.getLanguage()), "taskname", "id", 1)).setIsInputCol(BoolAttr.TRUE));
/*  93 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15285, this.user.getLanguage()), "principalid", "principalid", "weaver.hrm.resource.ResourceComInfo.getResourcename", 0));
/*  94 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(740, this.user.getLanguage()), "begindate", "begindate", 2));
/*  95 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(741, this.user.getLanguage()), "enddate", "enddate", 0));
/*     */     
/*  97 */     SplitTableBean splitTableBean = new SplitTableBean(str10, str11, "", "t1.id", "t1.id", arrayList);
/*     */     
/*  99 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 101 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/* 102 */     } catch (Exception exception) {
/* 103 */       exception.printStackTrace();
/*     */     } 
/* 105 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 106 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 113 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 114 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.taskname");
/* 115 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.principalid");
/* 116 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 117 */     hashMap.put("marginLeft", "20px");
/* 118 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.enddate", hashMap);
/* 119 */     return arrayList;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 123 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 124 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 125 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 126 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 1352, "taskname", true));
/* 127 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 15285, "principalid", "1"));
/* 128 */     arrayList.add(conditionFactory.createCondition(ConditionType.DATEPICKER, 740, "begindate"));
/* 129 */     arrayList.add(conditionFactory.createCondition(ConditionType.DATEPICKER, 741, "enddate"));
/* 130 */     BrowserBean browserBean = new BrowserBean();
/* 131 */     browserBean.setType("wrltsk");
/* 132 */     browserBean.setIcon("icon-coms-implement");
/* 133 */     browserBean.setIconBgcolor("rgb(150, 53, 138)");
/* 134 */     browserBean.setTitle(SystemEnv.getHtmlLabelName(23785, this.user.getLanguage()));
/* 135 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 23785, "parentids", browserBean));
/* 136 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 137 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 141 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 142 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 143 */     String str2 = paramHttpServletRequest.getParameter("secretLevel");
/* 144 */     String str3 = paramHttpServletRequest.getParameter("isparent");
/*     */     
/* 146 */     RecordSet recordSet = new RecordSet();
/* 147 */     String str4 = " where (t1.deleted=0 or t1.deleted is null) ";
/* 148 */     if (str2 != null) {
/* 149 */       if ("".equals(str2)) {
/* 150 */         return (Map)hashMap;
/*     */       }
/* 152 */       if ("1".equals(str3)) {
/* 153 */         str4 = str4 + " and (t1.secretlev <= " + str2 + " or t1.secretlev is null) ";
/*     */       } else {
/* 155 */         str4 = str4 + " and (t1.secretlev >= " + str2 + " or t1.secretlev is null) ";
/*     */       } 
/*     */     } 
/* 158 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("sqlwhere"));
/* 159 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("parentid"));
/* 160 */     if (!str5.equals("")) {
/* 161 */       str4 = str4 + str5;
/*     */     }
/* 163 */     if (!"".equals(str1)) {
/* 164 */       str4 = str4 + " and name like '%" + str1 + "%' ";
/*     */     }
/* 166 */     if (!str6.equals("")) {
/* 167 */       str4 = str4 + " and t1.id not in (select id from TM_TaskInfo t2 where t2.parentid = '" + str6 + "') and t1.id <> " + str6;
/*     */       
/* 169 */       str4 = str4 + " and ((t1.principalid=" + this.user.getUID() + " and t1.status not in(4,5)) or t1.creater=" + this.user.getUID() + " or exists (select 1 from TM_TaskPartner tp where tp.taskid=t1.id and tp.partnerid=" + this.user.getUID() + " and t1.status not in(4,5)))";
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 174 */       str4 = str4 + " and ((t1.principalid=" + this.user.getUID() + " and t1.status not in(4,5)) or t1.creater=" + this.user.getUID() + " or exists (select 1 from TM_TaskPartner tp where tp.taskid=t1.id and tp.partnerid=" + this.user.getUID() + " and t1.status not in(4,5)) or exists (select 1 from TM_TaskSharer ts where ts.taskid=t1.id and ts.sharerid=" + this.user.getUID() + " and t1.status not in(4,5))";
/* 175 */       if ("1".equals(TaskUtil.getBaseSetTask("ismanagerview", "1")))
/*     */       {
/* 177 */         str4 = str4 + " or exists (select 1 from HrmResource hrm where (hrm.id=t1.principalid or hrm.id=t1.creater) and hrm.managerstr like '%," + this.user.getUID() + ",%' and t1.status not in(4,5)) or exists (select 1 from HrmResource hrm,TM_TaskPartner tp where tp.taskid=t1.id and hrm.id=tp.partnerid and hrm.managerstr like '%," + this.user.getUID() + ",%' and t1.status not in(4,5))";
/*     */       }
/* 179 */       str4 = str4 + ")";
/*     */     } 
/* 181 */     recordSet.executeQuery("select t1.id, t1.name from TM_TaskInfo t1 " + str4 + " order by id desc", new Object[0]);
/* 182 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 183 */     while (recordSet.next()) {
/* 184 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 185 */       hashMap1.put("id", recordSet.getString("id"));
/* 186 */       hashMap1.put("name", recordSet.getString("name"));
/* 187 */       arrayList.add(hashMap1);
/*     */     } 
/* 189 */     hashMap.put("datas", arrayList);
/* 190 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 194 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 195 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 196 */     if ("".equals(str)) return (Map)hashMap; 
/* 197 */     RecordSet recordSet = new RecordSet();
/* 198 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 199 */     recordSet.execute("select tt1.id, tt1.name taskname,tt1.begindate,tt1.enddate,tt1.principalid from TM_TaskInfo tt1  where tt1.id in (" + str + ") and (tt1.deleted=0 or tt1.deleted is null)");
/* 200 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 201 */     while (recordSet.next()) {
/* 202 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 203 */       hashMap1.put("id", recordSet.getString("id"));
/* 204 */       hashMap1.put("taskname", recordSet.getString("taskname"));
/* 205 */       hashMap1.put("enddate", recordSet.getString("enddate"));
/* 206 */       hashMap1.put("principalid", resourceComInfo.getLastname(recordSet.getString("principalid")));
/* 207 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 210 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 211 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 212 */     arrayList1.add(new ListHeadBean("taskname", SystemEnv.getHtmlLabelName(1352, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 213 */     arrayList1.add(new ListHeadBean("principalid", SystemEnv.getHtmlLabelName(2097, this.user.getLanguage()), 0, BoolAttr.FALSE));
/* 214 */     arrayList1.add(new ListHeadBean("enddate", SystemEnv.getHtmlLabelName(741, this.user.getLanguage()), 0, BoolAttr.FALSE));
/*     */     
/* 216 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 217 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 218 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 219 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkrelateTaskBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */