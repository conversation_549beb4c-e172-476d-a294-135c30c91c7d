/*      */ package com.api.browser.service.impl;
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.browser.bean.BrowserTreeNode;
/*      */ import com.api.browser.bean.ListHeadBean;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SearchConditionOption;
/*      */ import com.api.browser.bean.SplitTableColBean;
/*      */ import com.api.browser.util.BoolAttr;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.BrowserTreeNodeIcon;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.engine.common.service.impl.WorkflowCommonServiceImpl;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.Util;
/*      */ import weaver.general.browserData.BrowserManager;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.appdetach.AppDetachComInfo;
/*      */ import weaver.hrm.common.Tools;
/*      */ import weaver.hrm.company.CompanyComInfo;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.companyvirtual.CompanyVirtualComInfo;
/*      */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*      */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*      */ import weaver.hrm.resource.MutilResourceBrowser;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*      */ import weaver.systeminfo.systemright.OrganizationUtil;
/*      */ 
/*      */ public class ResourceBrowserDecService extends BrowserService {
/*   42 */   private AppDetachComInfo adci = null;
/*      */   private boolean isDetachBrower = false;
/*   44 */   private Map<String, Object> params = null;
/*   45 */   private int[] subcomids = null;
/*   46 */   private int[] subcomids1 = null;
/*   47 */   private ArrayList UserDepts = new ArrayList();
/*   48 */   private ArrayList UserDepts1 = new ArrayList();
/*   49 */   private List<String> dataRanageSubCompanyIds = null;
/*   50 */   private List<String> dataRanageSuperSubCompanyIds = null;
/*   51 */   private List<String> dataRanageDepartmentIds = null;
/*   52 */   private List<String> dataRanageSuperDepartmentIds = null;
/*   53 */   private List<String> dataRanageAllSubCompanyIds = new ArrayList<>();
/*   54 */   private List<String> dataRanageAllDepartmentIds = new ArrayList<>();
/*   55 */   private List textheightLists = new ArrayList();
/*   56 */   private List<String> resourceids = new ArrayList<>();
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*   60 */     this.params = paramMap;
/*   61 */     this.isDetachBrower = true;
/*   62 */     getSubCompanyTreeListByDecRight();
/*      */     
/*   64 */     WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/*   65 */     Map<String, Object> map = workflowCommonServiceImpl.getDataDefinitionDataRanageSet(paramMap, this.user, Util.getIntValue(this.browserType));
/*   66 */     this.dataRanageSubCompanyIds = (List<String>)map.get("subCompanyIds");
/*   67 */     this.dataRanageSuperSubCompanyIds = (List<String>)map.get("superSubCompanyIds");
/*   68 */     this.dataRanageDepartmentIds = (List<String>)map.get("departmentIds");
/*   69 */     this.dataRanageSuperDepartmentIds = (List<String>)map.get("superDeptIds");
/*   70 */     this.resourceids = new ArrayList<>();
/*      */     
/*   72 */     if (this.dataRanageSubCompanyIds != null && !this.dataRanageSubCompanyIds.isEmpty()) {
/*   73 */       this.dataRanageAllSubCompanyIds.addAll(this.dataRanageSubCompanyIds);
/*      */     }
/*   75 */     if (this.dataRanageSuperSubCompanyIds != null && !this.dataRanageSuperSubCompanyIds.isEmpty()) {
/*   76 */       this.dataRanageAllSubCompanyIds.addAll(this.dataRanageSuperSubCompanyIds);
/*      */     }
/*   78 */     if (this.dataRanageDepartmentIds != null && !this.dataRanageDepartmentIds.isEmpty()) {
/*   79 */       this.dataRanageAllDepartmentIds.addAll(this.dataRanageDepartmentIds);
/*      */     }
/*   81 */     if (this.dataRanageSuperDepartmentIds != null && !this.dataRanageSuperDepartmentIds.isEmpty()) {
/*   82 */       this.dataRanageAllDepartmentIds.addAll(this.dataRanageSuperDepartmentIds);
/*      */     }
/*   84 */     generateRange(map);
/*   85 */     String str1 = Util.null2String(paramMap.get("cmd"));
/*   86 */     if ("list".equals(str1))
/*   87 */       return getOrgListData(paramMap); 
/*   88 */     if ("v2grouptree".equals(str1)) {
/*   89 */       return getGroupData(paramMap);
/*      */     }
/*      */     
/*   92 */     String str2 = Util.null2String(paramMap.get("id"));
/*   93 */     if ("".equals(str2)) {
/*   94 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       
/*   96 */       String str3 = Util.null2String(paramMap.get("virtualCompanyid"));
/*   97 */       if (str3.length() == 0) {
/*   98 */         str3 = Util.null2String(paramMap.get("virtualtype"));
/*      */       }
/*  100 */       if (str3.equals("1")) {
/*  101 */         str3 = "";
/*      */       }
/*      */       
/*  104 */       boolean bool1 = isDepartmentBrowser();
/*      */       
/*  106 */       boolean bool2 = "1".equals(Util.null2String(paramMap.get("isLoadAllSub")));
/*      */       
/*  108 */       CompanyComInfo companyComInfo = null;
/*  109 */       CompanyVirtualComInfo companyVirtualComInfo = null;
/*      */       
/*      */       try {
/*  112 */         companyComInfo = new CompanyComInfo();
/*  113 */         companyVirtualComInfo = new CompanyVirtualComInfo();
/*  114 */         companyVirtualComInfo.setUser(this.user);
/*  115 */       } catch (Exception exception) {}
/*      */ 
/*      */       
/*  118 */       ArrayList<OrgBean> arrayList = new ArrayList();
/*      */ 
/*      */       
/*  121 */       if ("".equals(str3) && companyVirtualComInfo.getCompanyNum() > 0) {
/*  122 */         OrgBean orgBean1 = null;
/*  123 */         if (companyComInfo.getCompanyNum() > 0) {
/*  124 */           companyComInfo.setTofirstRow();
/*  125 */           while (companyComInfo.next()) {
/*  126 */             orgBean1 = new OrgBean();
/*  127 */             orgBean1.setCompanyid(companyComInfo.getCompanyid());
/*  128 */             orgBean1.setName(companyComInfo.getCompanyname());
/*  129 */             orgBean1.setIsVirtual("0");
/*  130 */             arrayList.add(orgBean1);
/*      */           } 
/*      */         } 
/*      */         
/*  134 */         companyVirtualComInfo.setTofirstRow();
/*  135 */         while (companyVirtualComInfo.next()) {
/*  136 */           orgBean1 = new OrgBean();
/*  137 */           orgBean1.setCompanyid(companyVirtualComInfo.getCompanyid());
/*  138 */           orgBean1.setName(companyVirtualComInfo.getVirtualType());
/*  139 */           orgBean1.setIsVirtual("1");
/*  140 */           arrayList.add(orgBean1);
/*      */         } 
/*      */       } 
/*  143 */       if (this.isDetachBrower) arrayList.clear(); 
/*  144 */       hashMap.put("companys", arrayList);
/*  145 */       String str4 = "";
/*  146 */       if ("".equals(str3)) {
/*  147 */         str4 = companyComInfo.getCompanyname("1");
/*      */       } else {
/*  149 */         str4 = companyVirtualComInfo.getVirtualType(str3);
/*  150 */       }  OrgBean orgBean = new OrgBean();
/*  151 */       orgBean.setIcon(BrowserTreeNodeIcon.COMPANY_ICON.toString());
/*  152 */       if ("".equals(str3)) {
/*  153 */         orgBean.setId("0");
/*  154 */         orgBean.setCompanyid("1");
/*  155 */         orgBean.setName(str4);
/*  156 */         orgBean.setType("0");
/*  157 */         orgBean.setIsVirtual("0");
/*      */ 
/*      */         
/*  160 */         loadSubCompanys(orgBean, bool1, bool2, this.user);
/*      */       } else {
/*      */         
/*  163 */         orgBean.setId("0");
/*  164 */         orgBean.setCompanyid(str3);
/*  165 */         orgBean.setName(str4);
/*  166 */         orgBean.setType("0");
/*  167 */         orgBean.setIsVirtual("1");
/*  168 */         loadVirtualSubCompanyInfo(orgBean, bool1, bool2, this.user);
/*      */       } 
/*      */       
/*  171 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  172 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, orgBean);
/*      */       
/*  174 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  175 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  176 */       arrayList1.add(new ListHeadBean("lastname", "", 1, BoolAttr.TRUE));
/*  177 */       arrayList1.add(new ListHeadBean("jobtitlename", "", 1));
/*  178 */       arrayList1.add(new ListHeadBean("getHrmMobileSignInInfodepartmentname", ""));
/*  179 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  180 */       return (Map)hashMap;
/*      */     } 
/*  182 */     return getTreeNodeData(paramMap);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  188 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  190 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  191 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  192 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  193 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("virtualtype"));
/*      */     
/*  195 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("sqlwhere"));
/*      */     
/*  197 */     WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/*  198 */     Map<String, String> map = ParamUtil.request2Map(paramHttpServletRequest);
/*  199 */     map.put("tableAlias", "t1");
/*  200 */     Map map1 = workflowCommonServiceImpl.getDataDefinitionDataRanageSet(map, user, 1);
/*  201 */     if (Util.null2String(map1.get("sqlWhere")).length() > 0) {
/*  202 */       if (str2.length() > 0)
/*  203 */         str2 = str2 + "and"; 
/*  204 */       str2 = str2 + Util.null2String(map1.get("sqlWhere"));
/*      */     } 
/*      */     
/*  207 */     if (str2.equals("")) {
/*  208 */       str2 = "t1.departmentid = t2.id";
/*      */     } else {
/*  210 */       str2 = str2 + " and t1.departmentid = t2.id";
/*      */     } 
/*  212 */     if (Util.getIntValue(str1) < -1) {
/*  213 */       str2 = str2 + " and t1.virtualtype = " + str1;
/*      */     }
/*      */     
/*  216 */     if (str2.indexOf("status") == -1)
/*      */     {
/*      */ 
/*      */       
/*  220 */       if (str2.equals("")) {
/*  221 */         str2 = str2 + " (t1.status = 0 or t1.status = 1 or t1.status = 2 or t1.status = 3) ";
/*      */       } else {
/*  223 */         str2 = str2 + " and (t1.status = 0 or t1.status = 1 or t1.status = 2 or t1.status = 3) ";
/*      */       } 
/*      */     }
/*      */     
/*  227 */     HttpSession httpSession = paramHttpServletRequest.getSession();
/*  228 */     if (this.browserType.equals("165") || this.browserType.equals("166")) {
/*  229 */       int i = Util.getIntValue((String)httpSession.getAttribute("beagenter_" + user.getUID()));
/*  230 */       if (i <= 0) {
/*  231 */         i = user.getUID();
/*      */       }
/*  233 */       int j = Util.getIntValue(paramHttpServletRequest.getParameter("fieldid"));
/*  234 */       int k = Util.getIntValue(paramHttpServletRequest.getParameter("viewtype"));
/*  235 */       int m = Util.getIntValue(paramHttpServletRequest.getParameter("isbill"), 1);
/*  236 */       if (j != -1) {
/*  237 */         CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  238 */         checkSubCompanyRight.setDetachable(1);
/*  239 */         checkSubCompanyRight.setIsbill(m);
/*  240 */         checkSubCompanyRight.setFieldid(j);
/*  241 */         checkSubCompanyRight.setIsdetail(k);
/*  242 */         boolean bool1 = checkSubCompanyRight.getDecentralizationAttr(i, "Resources:decentralization", j, k, m);
/*  243 */         boolean bool2 = checkSubCompanyRight.getIsall();
/*  244 */         String str5 = Util.null2String(checkSubCompanyRight.getDepartmentids());
/*  245 */         String str6 = Util.null2String(checkSubCompanyRight.getSubcompanyids());
/*  246 */         if (!bool2) {
/*  247 */           if (bool1) {
/*  248 */             if (str5.length() > 0 && !str5.equals("0")) {
/*  249 */               str2 = str2 + " and t1.departmentid in(" + str5 + ")";
/*      */             }
/*      */           }
/*  252 */           else if (str6.length() > 0 && !str6.equals("0")) {
/*  253 */             str2 = str2 + " and t1.subcompanyid1 in(" + str6 + ")";
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  260 */     AppDetachComInfo appDetachComInfo = new AppDetachComInfo();
/*  261 */     String str3 = appDetachComInfo.getScopeSqlByHrmResourceSearch(String.valueOf(user.getUID()), true, "resource_t1");
/*  262 */     str2 = str2 + ((str3 == null || str3.length() == 0) ? "" : (" and " + str3));
/*      */     
/*  264 */     BrowserManager browserManager = new BrowserManager();
/*  265 */     browserManager.setType(this.browserType);
/*  266 */     browserManager.setOrderKey("t1.dsporder");
/*  267 */     browserManager.setOrderWay("asc");
/*  268 */     String str4 = "";
/*  269 */     if (Util.getIntValue(str1) < -1) {
/*  270 */       str4 = browserManager.getResult(paramHttpServletRequest, "t1.id,lastname,departmentname", "HrmResourcevirtualview t1,hrmdepartmentvirtual t2", str2, 30, "t1");
/*      */     } else {
/*  272 */       str4 = browserManager.getResult(paramHttpServletRequest, "t1.id,lastname,departmentname", "hrmresource t1,hrmdepartment t2", str2, 30, "t1");
/*      */     } 
/*      */     
/*  275 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  276 */     JSONArray jSONArray = (JSONArray)JSON.parse(str4);
/*  277 */     for (byte b = 0; b < jSONArray.size(); b++) {
/*  278 */       JSONObject jSONObject = (JSONObject)jSONArray.get(b);
/*  279 */       String str5 = jSONObject.getString("id");
/*  280 */       jSONObject.put("lastname", resourceComInfo.getLastname(str5));
/*  281 */       jSONObject.put("jobtitlename", MutilResourceBrowser.getJobTitlesname(str5));
/*  282 */       jSONObject.put("icon", resourceComInfo.getMessagerUrls(str5));
/*  283 */       jSONObject.put("requestParams", resourceComInfo.getUserIconInfoStr(str5, user));
/*  284 */       jSONObject.put("isImgIcon", Boolean.valueOf(true));
/*  285 */       jSONObject.put("type", "resource");
/*  286 */       jSONObject.put("departmentname", departmentComInfo.getDepartmentmark(resourceComInfo.getDepartmentID(str5)));
/*  287 */       String str6 = departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID(str5));
/*  288 */       String str7 = subCompanyComInfo.getSupsubcomid(str6);
/*  289 */       jSONObject.put("subcompanyname", subCompanyComInfo.getSubcompanyname(str6));
/*  290 */       jSONObject.put("supsubcompanyname", subCompanyComInfo.getSubcompanyname(str7));
/*  291 */       jSONObject.put("title", jSONObject.get("lastname") + "|" + jSONObject.get("departmentname"));
/*      */     } 
/*  293 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, jSONArray);
/*  294 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void loadSubCompanys(OrgBean paramOrgBean, boolean paramBoolean1, boolean paramBoolean2, User paramUser) {
/*  306 */     SubCompanyComInfo subCompanyComInfo = null;
/*      */     try {
/*  308 */       subCompanyComInfo = new SubCompanyComInfo();
/*  309 */     } catch (Exception exception) {
/*  310 */       exception.printStackTrace();
/*      */     } 
/*  312 */     subCompanyComInfo.setTofirstRow();
/*      */     
/*  314 */     if (paramBoolean1)
/*      */     {
/*  316 */       loadSubDepartments(paramOrgBean, (OrgBean)null, paramBoolean2);
/*      */     }
/*      */     
/*  319 */     List<OrgBean> list = null;
/*  320 */     if (paramOrgBean.getSubs() != null) {
/*  321 */       list = paramOrgBean.getSubs();
/*      */     } else {
/*  323 */       list = new ArrayList();
/*      */     } 
/*      */     
/*  326 */     while (subCompanyComInfo.next()) {
/*  327 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*      */       
/*  329 */       if ("1".equals(subCompanyComInfo.getCompanyiscanceled())) {
/*      */         continue;
/*      */       }
/*  332 */       if (str1.equals(""))
/*  333 */         str1 = "0"; 
/*  334 */       if (!str1.equals(paramOrgBean.getId())) {
/*      */         continue;
/*      */       }
/*  337 */       String str2 = subCompanyComInfo.getSubCompanyid();
/*      */       
/*  339 */       if (this.dataRanageAllSubCompanyIds != null && !this.dataRanageAllSubCompanyIds.isEmpty() && 
/*  340 */         !this.dataRanageAllSubCompanyIds.contains(str2)) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */       
/*  345 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*      */       
/*  347 */       if (!checkDetach("com", str2)) {
/*      */         continue;
/*      */       }
/*      */       
/*  351 */       OrgBean orgBean = new OrgBean();
/*  352 */       orgBean.setId(str2);
/*  353 */       orgBean.setName(str3);
/*  354 */       orgBean.setPid(paramOrgBean.getId());
/*  355 */       orgBean.setType("1");
/*  356 */       orgBean.setIsVirtual("0");
/*  357 */       orgBean.setCanClick(!paramBoolean1);
/*  358 */       orgBean.setIcon(BrowserTreeNodeIcon.SUBCOMPANY_ICON.toString());
/*  359 */       if (!paramBoolean1) {
/*  360 */         orgBean.setShadowInfo(getAllParentsOrg(str2, "0"));
/*      */       }
/*      */       
/*  363 */       if (this.isDetachBrower) {
/*  364 */         boolean bool = false;
/*  365 */         for (byte b = 0; b < this.subcomids1.length; b++) {
/*  366 */           if (str2.equals(String.valueOf(this.subcomids1[b]))) {
/*  367 */             bool = true;
/*      */           }
/*      */         } 
/*      */         
/*  371 */         if (this.dataRanageSubCompanyIds != null && this.dataRanageSubCompanyIds.size() > 0 && this.dataRanageSubCompanyIds.contains(str2)) {
/*  372 */           bool = true;
/*      */         }
/*      */         
/*  375 */         if (!paramBoolean1) {
/*  376 */           orgBean.setCanClick(bool);
/*      */         }
/*      */         
/*  379 */         if (!bool) {
/*  380 */           validOrgIsParent(orgBean, paramBoolean1, paramUser);
/*  381 */           if (!orgBean.getIsParent()) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */       } 
/*  386 */       list.add(orgBean);
/*      */       
/*  388 */       paramOrgBean.setIsParent(true);
/*      */ 
/*      */       
/*  391 */       if (paramBoolean2) {
/*  392 */         loadSubCompanys(orgBean, paramBoolean1, paramBoolean2, paramUser);
/*      */         continue;
/*      */       } 
/*  395 */       validOrgIsParent(orgBean, paramBoolean1, paramUser);
/*      */     } 
/*      */     
/*  398 */     paramOrgBean.setSubs(list);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void validOrgIsParent(OrgBean paramOrgBean, boolean paramBoolean, User paramUser) {
/*  409 */     SubCompanyComInfo subCompanyComInfo = null;
/*      */     try {
/*  411 */       subCompanyComInfo = new SubCompanyComInfo();
/*  412 */     } catch (Exception exception) {
/*  413 */       exception.printStackTrace();
/*      */     } 
/*  415 */     subCompanyComInfo.setTofirstRow();
/*  416 */     if (paramBoolean) {
/*  417 */       validOrgIsParent(paramOrgBean, (OrgBean)null);
/*      */     }
/*      */     
/*  420 */     while (subCompanyComInfo.next()) {
/*  421 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*  422 */       if (str1.equals(""))
/*  423 */         str1 = "0"; 
/*  424 */       if (!str1.equals(paramOrgBean.getId())) {
/*      */         continue;
/*      */       }
/*  427 */       if ("1".equals(subCompanyComInfo.getCompanyiscanceled())) {
/*      */         continue;
/*      */       }
/*      */       
/*  431 */       String str2 = subCompanyComInfo.getSubCompanyid();
/*  432 */       if (!checkDetach("com", str2)) {
/*      */         continue;
/*      */       }
/*  435 */       paramOrgBean.setIsParent(true);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void validOrgIsParent(OrgBean paramOrgBean1, OrgBean paramOrgBean2) {
/*  447 */     DepartmentComInfo departmentComInfo = null;
/*      */     try {
/*  449 */       departmentComInfo = new DepartmentComInfo();
/*  450 */     } catch (Exception exception) {
/*  451 */       exception.printStackTrace();
/*      */     } 
/*  453 */     departmentComInfo.setTofirstRow();
/*  454 */     String str1 = (paramOrgBean2 == null) ? "0" : paramOrgBean2.getId();
/*  455 */     String str2 = paramOrgBean1.getId();
/*  456 */     if ("".equals(str2)) {
/*  457 */       str2 = departmentComInfo.getSubcompanyid1(str1);
/*      */     }
/*      */     
/*  460 */     while (departmentComInfo.next()) {
/*  461 */       String str3 = departmentComInfo.getDepartmentsupdepid();
/*  462 */       if (str1.equals("0") && str3.equals("")) {
/*  463 */         str3 = "0";
/*      */       }
/*  465 */       if (!departmentComInfo.getSubcompanyid1().equals(str2) || (!str3.equals(str1) && (departmentComInfo.getSubcompanyid1(str3).equals(str2) || 
/*  466 */         !str1.equals("0")))) {
/*      */         continue;
/*      */       }
/*  469 */       if ("1".equals(departmentComInfo.getDeparmentcanceled())) {
/*      */         continue;
/*      */       }
/*      */       
/*  473 */       String str4 = departmentComInfo.getDepartmentid();
/*  474 */       if (!checkDetach("dept", str4)) {
/*      */         continue;
/*      */       }
/*  477 */       paramOrgBean1.setIsParent(true);
/*  478 */       if (paramOrgBean2 != null) {
/*  479 */         paramOrgBean2.setIsParent(true);
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void loadSubDepartments(OrgBean paramOrgBean1, OrgBean paramOrgBean2, boolean paramBoolean) {
/*  493 */     DepartmentComInfo departmentComInfo = null;
/*      */     try {
/*  495 */       departmentComInfo = new DepartmentComInfo();
/*  496 */     } catch (Exception exception) {
/*  497 */       exception.printStackTrace();
/*      */     } 
/*  499 */     String str1 = (paramOrgBean2 == null) ? "0" : paramOrgBean2.getId();
/*      */     
/*  501 */     String str2 = Util.null2String(paramOrgBean1.getId());
/*  502 */     if ("".equals(str2)) {
/*  503 */       str2 = departmentComInfo.getSubcompanyid1(str1);
/*      */     }
/*  505 */     String str3 = (paramOrgBean2 == null) ? paramOrgBean1.getId() : paramOrgBean2.getId();
/*      */     
/*  507 */     List<BrowserTreeNode> list = null;
/*  508 */     if (paramOrgBean2 == null && paramOrgBean1.getSubs() != null) {
/*  509 */       list = paramOrgBean1.getSubs();
/*      */     } else {
/*  511 */       list = new ArrayList();
/*      */     } 
/*      */     
/*  514 */     if (paramOrgBean2 != null)
/*      */     {
/*  516 */       loadDeptResources(list, paramOrgBean2, paramBoolean);
/*      */     }
/*      */     
/*  519 */     departmentComInfo.setTofirstRow();
/*  520 */     while (departmentComInfo.next()) {
/*  521 */       String str4 = departmentComInfo.getDepartmentsupdepid();
/*  522 */       if ("1".equals(departmentComInfo.getDeparmentcanceled())) {
/*      */         continue;
/*      */       }
/*  525 */       if (str1.equals("0") && str4.equals("")) {
/*  526 */         str4 = "0";
/*      */       }
/*  528 */       if (!departmentComInfo.getSubcompanyid1().equals(str2) || (!str4.equals(str1) && (departmentComInfo.getSubcompanyid1(str4).equals(str2) || 
/*  529 */         !str1.equals("0")))) {
/*      */         continue;
/*      */       }
/*  532 */       String str5 = departmentComInfo.getDepartmentid();
/*      */       
/*  534 */       if (this.dataRanageAllDepartmentIds != null && !this.dataRanageAllDepartmentIds.isEmpty() && 
/*  535 */         !this.dataRanageAllDepartmentIds.contains(str5)) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */       
/*  540 */       String str6 = departmentComInfo.getDepartmentmark();
/*  541 */       if (!checkDetach("dept", str5)) {
/*      */         continue;
/*      */       }
/*      */       
/*  545 */       OrgBean orgBean = new OrgBean();
/*  546 */       orgBean.setId(str5);
/*  547 */       orgBean.setPid(str3);
/*  548 */       orgBean.setName(str6);
/*  549 */       orgBean.setType("2");
/*  550 */       orgBean.setIsVirtual("0");
/*  551 */       orgBean.setShadowInfo(getAllParentsOrg(str5, "1"));
/*  552 */       orgBean.setPsubcompanyid(paramOrgBean1.getId());
/*  553 */       orgBean.setIcon(BrowserTreeNodeIcon.DEPARTMENT_ICON.toString());
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  558 */       if (this.dataRanageDepartmentIds != null) {
/*  559 */         boolean bool = false;
/*  560 */         if (this.dataRanageDepartmentIds.size() == 0 || this.dataRanageDepartmentIds.contains(str5)) {
/*  561 */           bool = true;
/*      */         }
/*  563 */         orgBean.setCanClick(bool);
/*      */       } 
/*      */       
/*  566 */       if (this.isDetachBrower) {
/*  567 */         boolean bool = false;
/*  568 */         if (this.UserDepts1.size() == 0 || this.UserDepts.contains(str5)) {
/*  569 */           bool = true;
/*      */ 
/*      */ 
/*      */           
/*  573 */           orgBean.setCanClick(bool);
/*      */         } else {
/*      */           continue;
/*      */         } 
/*  577 */       }  orgBean.setCanClick(false);
/*  578 */       orgBean.setIsParent(true);
/*      */       
/*  580 */       paramOrgBean1.setIsParent(true);
/*  581 */       list.add(orgBean);
/*      */       
/*  583 */       if (paramBoolean) {
/*  584 */         loadSubDepartments(paramOrgBean1, orgBean, paramBoolean);
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  590 */     if (paramOrgBean2 == null) {
/*  591 */       paramOrgBean1.setSubs(list);
/*      */     } else {
/*      */       
/*  594 */       paramOrgBean2.setSubs(list);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void loadDeptResources(List<BrowserTreeNode> paramList, OrgBean paramOrgBean, boolean paramBoolean) {
/*  606 */     ResourceComInfo resourceComInfo = null;
/*  607 */     DepartmentComInfo departmentComInfo = null;
/*      */     try {
/*  609 */       departmentComInfo = new DepartmentComInfo();
/*  610 */       resourceComInfo = new ResourceComInfo();
/*  611 */     } catch (Exception exception) {
/*  612 */       exception.printStackTrace();
/*      */     } 
/*  614 */     if (paramOrgBean == null)
/*  615 */       return;  String str = paramOrgBean.getId();
/*  616 */     if (this.isDetachBrower) {
/*  617 */       boolean bool = false;
/*  618 */       if (this.UserDepts1.size() > 0 && !this.UserDepts1.contains(str)) {
/*      */         return;
/*      */       }
/*      */     } 
/*  622 */     resourceComInfo.setTofirstRow();
/*  623 */     while (resourceComInfo.next()) {
/*  624 */       String str1 = resourceComInfo.getDepartmentID();
/*  625 */       if (str.length() == 0) {
/*      */         break;
/*      */       }
/*  628 */       if (str.equals(str1) && (
/*  629 */         "0".equals(resourceComInfo.getStatus()) || "1".equals(resourceComInfo.getStatus()) || "2".equals(resourceComInfo.getStatus()) || "3".equals(resourceComInfo.getStatus()))) {
/*      */         
/*  631 */         String str2 = resourceComInfo.getResourceid();
/*      */         
/*  633 */         if (this.resourceids.size() > 0 && !this.resourceids.contains(str2))
/*      */           continue; 
/*  635 */         String str3 = resourceComInfo.getLastname();
/*  636 */         String str4 = resourceComInfo.getMessagerUrls();
/*      */         
/*  638 */         OrgBean orgBean = new OrgBean();
/*  639 */         orgBean.setId(str2);
/*  640 */         orgBean.setPid(str);
/*  641 */         orgBean.setName(str3);
/*  642 */         orgBean.setType("3");
/*  643 */         orgBean.setIsVirtual("0");
/*      */         
/*  645 */         orgBean.setCanClick(true);
/*      */         
/*  647 */         orgBean.setPsubcompanyid(departmentComInfo.getSubcompanyid1(str));
/*  648 */         orgBean.setRequestParams(resourceComInfo.getUserIconInfoStr(str2, this.user));
/*  649 */         orgBean.setIcon(str4);
/*  650 */         orgBean.setIsImgIcon(false);
/*  651 */         orgBean.setCanClick(true);
/*      */         
/*  653 */         paramList.add(orgBean);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void loadVirtualSubCompanyInfo(OrgBean paramOrgBean, boolean paramBoolean1, boolean paramBoolean2, User paramUser) {
/*  667 */     SubCompanyVirtualComInfo subCompanyVirtualComInfo = null;
/*      */     try {
/*  669 */       subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/*  670 */       if ("".equals(Util.null2String(paramOrgBean.getCompanyid()))) {
/*  671 */         paramOrgBean.setCompanyid(subCompanyVirtualComInfo.getCompanyid(paramOrgBean.getId()));
/*      */       }
/*  673 */     } catch (Exception exception) {
/*  674 */       exception.printStackTrace();
/*      */     } 
/*  676 */     subCompanyVirtualComInfo.setTofirstRow();
/*      */ 
/*      */     
/*  679 */     if (paramBoolean1) {
/*  680 */       loadVirtualSubDepartments(paramOrgBean, (OrgBean)null, paramBoolean2);
/*      */     }
/*      */     
/*  683 */     List<OrgBean> list = null;
/*  684 */     if (paramOrgBean.getSubs() != null) {
/*  685 */       list = paramOrgBean.getSubs();
/*      */     } else {
/*  687 */       list = new ArrayList();
/*      */     } 
/*      */     
/*  690 */     while (subCompanyVirtualComInfo.next()) {
/*  691 */       String str1 = subCompanyVirtualComInfo.getSubCompanyid();
/*  692 */       String str2 = subCompanyVirtualComInfo.getSupsubcomid();
/*  693 */       String str3 = subCompanyVirtualComInfo.getCompanyid();
/*      */       
/*  695 */       if ("1".equals(subCompanyVirtualComInfo.getCompanyiscanceled())) {
/*      */         continue;
/*      */       }
/*  698 */       if (!str3.equals(paramOrgBean.getCompanyid()))
/*      */         continue; 
/*  700 */       if (str2.equals(""))
/*  701 */         str2 = "0"; 
/*  702 */       if (!str2.equals(paramOrgBean.getId())) {
/*      */         continue;
/*      */       }
/*  705 */       String str4 = subCompanyVirtualComInfo.getSubCompanyname();
/*      */       
/*  707 */       OrgBean orgBean = new OrgBean();
/*  708 */       orgBean.setId(str1);
/*  709 */       orgBean.setPid(paramOrgBean.getId());
/*  710 */       orgBean.setName(str4);
/*  711 */       orgBean.setCompanyid(paramOrgBean.getCompanyid());
/*  712 */       orgBean.setType("1");
/*  713 */       orgBean.setIsVirtual("1");
/*  714 */       orgBean.setCanClick(!paramBoolean1);
/*  715 */       orgBean.setIcon(BrowserTreeNodeIcon.SUBCOMPANY_ICON.toString());
/*  716 */       if (!paramBoolean1) {
/*  717 */         orgBean.setShadowInfo(getAllParentsOrg(str1, "0+" + paramOrgBean.getCompanyid()));
/*      */       }
/*  719 */       paramOrgBean.setIsParent(true);
/*      */       
/*  721 */       list.add(orgBean);
/*      */       
/*  723 */       if (paramBoolean2) {
/*  724 */         loadVirtualSubCompanyInfo(orgBean, paramBoolean1, paramBoolean2, paramUser); continue;
/*      */       } 
/*  726 */       validVirtualOrgIsParent(orgBean, paramBoolean1, paramUser);
/*      */     } 
/*      */     
/*  729 */     paramOrgBean.setSubs(list);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void loadVirtualSubDepartments(OrgBean paramOrgBean1, OrgBean paramOrgBean2, boolean paramBoolean) {
/*  740 */     DepartmentVirtualComInfo departmentVirtualComInfo = null;
/*      */     try {
/*  742 */       departmentVirtualComInfo = new DepartmentVirtualComInfo();
/*  743 */     } catch (Exception exception) {
/*  744 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*  747 */     String str1 = (paramOrgBean2 == null) ? "0" : paramOrgBean2.getId();
/*  748 */     String str2 = Util.null2String(paramOrgBean1.getId());
/*  749 */     if ("".equals(str2)) {
/*  750 */       str2 = departmentVirtualComInfo.getSubcompanyid1(str1);
/*      */     }
/*      */     
/*  753 */     String str3 = (paramOrgBean2 == null) ? paramOrgBean1.getId() : paramOrgBean2.getId();
/*  754 */     List<OrgBean> list = null;
/*  755 */     if (paramOrgBean2 == null && paramOrgBean1.getSubs() != null) {
/*  756 */       list = paramOrgBean1.getSubs();
/*      */     } else {
/*  758 */       list = new ArrayList();
/*      */     } 
/*  760 */     departmentVirtualComInfo.setTofirstRow();
/*  761 */     while (departmentVirtualComInfo.next()) {
/*  762 */       if ("1".equals(departmentVirtualComInfo.getDeparmentcanceled())) {
/*      */         continue;
/*      */       }
/*  765 */       if (str1.equals(departmentVirtualComInfo.getDepartmentid()))
/*      */         continue; 
/*  767 */       String str4 = departmentVirtualComInfo.getDepartmentsupdepid();
/*  768 */       if (str1.equals("0") && str4.equals(""))
/*  769 */         str4 = "0"; 
/*  770 */       if (!departmentVirtualComInfo.getSubcompanyid1().equals(str2) || (!str4.equals(str1) && (departmentVirtualComInfo.getSubcompanyid1(str4).equals(str2) || !str1.equals("0")))) {
/*      */         continue;
/*      */       }
/*  773 */       String str5 = departmentVirtualComInfo.getDepartmentid();
/*  774 */       String str6 = departmentVirtualComInfo.getDepartmentmark();
/*      */       
/*  776 */       OrgBean orgBean = new OrgBean();
/*  777 */       orgBean.setId(str5);
/*  778 */       orgBean.setPid(str3);
/*  779 */       orgBean.setName(str6);
/*  780 */       orgBean.setType("2");
/*  781 */       orgBean.setIsVirtual("1");
/*  782 */       orgBean.setCanClick(true);
/*  783 */       orgBean.setPsubcompanyid(paramOrgBean1.getId());
/*  784 */       orgBean.setShadowInfo(getAllParentsOrg(str5, "0+" + paramOrgBean1.getCompanyid()));
/*  785 */       orgBean.setIcon(BrowserTreeNodeIcon.DEPARTMENT_ICON.toString());
/*  786 */       paramOrgBean1.setIsParent(true);
/*  787 */       if (paramOrgBean2 != null) {
/*  788 */         paramOrgBean2.setIsParent(true);
/*      */       }
/*      */       
/*  791 */       list.add(orgBean);
/*      */       
/*  793 */       if (paramBoolean) {
/*  794 */         loadVirtualSubDepartments(paramOrgBean1, orgBean, paramBoolean); continue;
/*      */       } 
/*  796 */       validVirtualOrgIsParent(paramOrgBean1, orgBean);
/*      */     } 
/*      */ 
/*      */     
/*  800 */     if (paramOrgBean2 == null) {
/*  801 */       paramOrgBean1.setSubs(list);
/*      */     } else {
/*  803 */       paramOrgBean2.setSubs(list);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void validVirtualOrgIsParent(OrgBean paramOrgBean, boolean paramBoolean, User paramUser) {
/*  813 */     SubCompanyVirtualComInfo subCompanyVirtualComInfo = null;
/*      */     try {
/*  815 */       subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/*  816 */       if ("".equals(Util.null2String(paramOrgBean.getCompanyid()))) {
/*  817 */         paramOrgBean.setCompanyid(subCompanyVirtualComInfo.getCompanyid(paramOrgBean.getId()));
/*      */       }
/*  819 */     } catch (Exception exception) {
/*  820 */       exception.printStackTrace();
/*      */     } 
/*  822 */     subCompanyVirtualComInfo.setTofirstRow();
/*      */ 
/*      */     
/*  825 */     if (paramBoolean) {
/*  826 */       validVirtualOrgIsParent(paramOrgBean, (OrgBean)null);
/*      */     }
/*  828 */     while (subCompanyVirtualComInfo.next()) {
/*  829 */       String str1 = subCompanyVirtualComInfo.getSubCompanyid();
/*  830 */       String str2 = subCompanyVirtualComInfo.getSupsubcomid();
/*  831 */       String str3 = subCompanyVirtualComInfo.getCompanyid();
/*      */       
/*  833 */       if (!str3.equals(paramOrgBean.getCompanyid()))
/*      */         continue; 
/*  835 */       if (str2.equals(""))
/*  836 */         str2 = "0"; 
/*  837 */       if (!str2.equals(paramOrgBean.getId())) {
/*      */         continue;
/*      */       }
/*  840 */       paramOrgBean.setIsParent(true);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void validVirtualOrgIsParent(OrgBean paramOrgBean1, OrgBean paramOrgBean2) {
/*  850 */     DepartmentVirtualComInfo departmentVirtualComInfo = null;
/*      */     try {
/*  852 */       departmentVirtualComInfo = new DepartmentVirtualComInfo();
/*  853 */     } catch (Exception exception) {
/*  854 */       exception.printStackTrace();
/*      */     } 
/*  856 */     departmentVirtualComInfo.setTofirstRow();
/*      */     
/*  858 */     String str1 = (paramOrgBean2 == null) ? "0" : paramOrgBean2.getId();
/*  859 */     String str2 = paramOrgBean1.getId();
/*      */     
/*  861 */     while (departmentVirtualComInfo.next()) {
/*  862 */       if (str1.equals(departmentVirtualComInfo.getDepartmentid()))
/*      */         continue; 
/*  864 */       String str = departmentVirtualComInfo.getDepartmentsupdepid();
/*  865 */       if (str1.equals("0") && str.equals(""))
/*  866 */         str = "0"; 
/*  867 */       if (!departmentVirtualComInfo.getSubcompanyid1().equals(str2) || (!str.equals(str1) && (departmentVirtualComInfo.getSubcompanyid1(str).equals(str2) || !str1.equals("0")))) {
/*      */         continue;
/*      */       }
/*  870 */       paramOrgBean1.setIsParent(true);
/*  871 */       if (paramOrgBean2 != null) {
/*  872 */         paramOrgBean2.setIsParent(true);
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getTreeNodeData(Map<String, Object> paramMap) {
/*  888 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  890 */     String str1 = Util.null2String(paramMap.get("type"));
/*  891 */     String str2 = Util.null2String(paramMap.get("id"));
/*  892 */     String str3 = Util.null2String(paramMap.get("psubcompanyid"));
/*  893 */     String str4 = Util.null2String(paramMap.get("isVirtual"));
/*      */     
/*  895 */     boolean bool = isDepartmentBrowser();
/*  896 */     OrgBean orgBean = new OrgBean();
/*  897 */     List list = null;
/*  898 */     if ("0".equals(str1)) {
/*  899 */       String str = Util.null2String(paramMap.get("companyid"));
/*  900 */       orgBean.setId(str2);
/*  901 */       orgBean.setCompanyid(str);
/*      */       
/*  903 */       if ("0".equals(str4)) {
/*  904 */         loadSubCompanys(orgBean, bool, false, this.user);
/*      */       } else {
/*  906 */         loadVirtualSubCompanyInfo(orgBean, bool, false, this.user);
/*      */       } 
/*  908 */       list = orgBean.getSubs();
/*  909 */     } else if ("1".equals(str1)) {
/*  910 */       orgBean.setId(str2);
/*  911 */       orgBean.setType(str1);
/*  912 */       orgBean.setIsVirtual(str4);
/*  913 */       if ("0".equals(str4)) {
/*  914 */         loadSubCompanys(orgBean, bool, false, this.user);
/*      */       } else {
/*  916 */         loadVirtualSubCompanyInfo(orgBean, bool, false, this.user);
/*      */       } 
/*  918 */       list = orgBean.getSubs();
/*  919 */     } else if ("2".equals(str1)) {
/*  920 */       orgBean.setId(str3);
/*      */       
/*  922 */       OrgBean orgBean1 = new OrgBean();
/*  923 */       orgBean1.setId(str2);
/*  924 */       orgBean1.setIsVirtual(str4);
/*  925 */       if ("0".equals(str4)) {
/*  926 */         loadSubDepartments(orgBean, orgBean1, false);
/*      */       } else {
/*  928 */         loadVirtualSubDepartments(orgBean, orgBean1, false);
/*      */       } 
/*  930 */       list = orgBean1.getSubs();
/*      */     } 
/*      */     
/*  933 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  934 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, list);
/*  935 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   private Map<String, Object> getOrgListData(Map<String, Object> paramMap) {
/*  940 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  941 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  942 */     String str2 = "";
/*  943 */     String str3 = "";
/*  944 */     String str4 = " where 1 = 1 ";
/*  945 */     String str5 = "";
/*  946 */     String str6 = "id";
/*      */ 
/*      */     
/*  949 */     String str7 = "";
/*  950 */     WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/*      */ 
/*      */     
/*  953 */     Map map = workflowCommonServiceImpl.getDataDefinitionDataRanageSet(paramMap, this.user, 1);
/*  954 */     if (Util.null2String(map.get("sqlWhere")).length() > 0) {
/*  955 */       str7 = str7 + Util.null2String(map.get("sqlWhere"));
/*      */     }
/*      */     
/*  958 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  959 */     arrayList.add(new SplitTableColBean("true", "id"));
/*      */     
/*  961 */     str2 = "id,lastname, jobtitle as jobtitlename, departmentid as departmentname";
/*  962 */     str3 = "HrmResource";
/*  963 */     str5 = "id";
/*      */     
/*  965 */     String str8 = Util.null2String(paramMap.get("lastname"));
/*  966 */     String str9 = Util.null2String(paramMap.get("virtualtype"));
/*  967 */     String str10 = Util.null2String(paramMap.get("status"));
/*  968 */     String str11 = Util.null2String(paramMap.get("subcompanyid"));
/*  969 */     String str12 = Util.null2String(paramMap.get("departmentid"));
/*  970 */     String str13 = Util.null2String(paramMap.get("jobtitle"));
/*  971 */     String str14 = Util.null2String(paramMap.get("roleid"));
/*      */     
/*  973 */     if (!"".equals(str8)) {
/*  974 */       str4 = str4 + " and( lastname like '%" + Util.fromScreen2(str8, this.user.getLanguage()) + "%' or pinyinlastname like '%" + Util.fromScreen2(str8, this.user.getLanguage()) + "%' or workcode like '%" + str8 + "%' or mobile like '%" + str8 + "%') ";
/*      */     }
/*      */     
/*  977 */     if (!"".equals(str10)) {
/*  978 */       if ("8".equals(str10)) {
/*  979 */         str4 = str4 + " and (status =0 or status = 1 or status = 2 or status = 3) ";
/*  980 */       } else if (!"9".equals(str10)) {
/*  981 */         str4 = str4 + " and status in (" + str10 + ")";
/*      */       } 
/*      */     }
/*      */     
/*  985 */     if (!"".equals(str11)) {
/*  986 */       str4 = str4 + " and subcompanyid1 = " + str11;
/*      */     }
/*      */     
/*  989 */     if (!"".equals(str12)) {
/*  990 */       str4 = str4 + " and departmentid = " + str12;
/*      */     }
/*      */     
/*  993 */     if (!"".equals(str13)) {
/*  994 */       str4 = str4 + " and jobtitle in ( select id from hrmjobtitles where jobtitlename like '%" + str13 + "%' )";
/*      */     }
/*      */     
/*  997 */     if (!str14.equals("")) {
/*  998 */       str4 = str4 + " and id in (select t1.ResourceID from hrmrolemembers t1,hrmroles t2 where t1.roleid = t2.ID and t2.ID=" + str14 + " ) ";
/*      */     }
/*      */     
/* 1001 */     if (!"".equals(str7)) {
/* 1002 */       str4 = str4 + str7;
/*      */     }
/*      */     
/* 1005 */     if (!"".equals(str1)) {
/* 1006 */       str4 = str4 + "(" + Util.getSubINClause(str1, "id", " not in ") + ")";
/*      */     }
/*      */     
/* 1009 */     String str15 = "";
/* 1010 */     if (this.isDetachBrower) {
/* 1011 */       str15 = ""; byte b;
/* 1012 */       for (b = 0; b < this.UserDepts1.size(); b++) {
/* 1013 */         if (str15.length() > 0) str15 = str15 + ","; 
/* 1014 */         str15 = str15 + this.UserDepts1.get(b);
/*      */       } 
/* 1016 */       if (!"".equals(str15)) {
/* 1017 */         str4 = str4 + " and (" + Tools.getOracleSQLIn(str15, "departmentid") + ")";
/*      */       }
/*      */       
/* 1020 */       str15 = "";
/* 1021 */       for (b = 0; b < this.subcomids1.length; b++) {
/* 1022 */         if (str15.length() > 0) str15 = str15 + ","; 
/* 1023 */         str15 = str15 + this.subcomids1[b];
/*      */       } 
/* 1025 */       if (!"".equals(str15)) {
/* 1026 */         str4 = str4 + " and (" + Tools.getOracleSQLIn(str15, "subcompanyid1") + ")";
/*      */       }
/*      */       
/* 1029 */       if (this.UserDepts1.size() == 0 && this.subcomids1.length == 0) {
/* 1030 */         str4 = str4 + " and 1=2 ";
/*      */       }
/*      */       
/* 1033 */       boolean bool = (new ManageDetachComInfo()).isUseHrmManageDetach();
/* 1034 */       if (!bool) {
/* 1035 */         String str = Util.null2String(this.params.get("rightStr"));
/* 1036 */         if (str.length() > 0) {
/* 1037 */           String str16 = HrmUserVarify.getRightLevel(str, this.user);
/* 1038 */           int i = this.user.getUserDepartment();
/* 1039 */           int j = this.user.getUserSubCompany1();
/* 1040 */           if (!str16.equals("2"))
/*      */           {
/* 1042 */             if (str16.equals("1")) {
/* 1043 */               str4 = str4 + " and subcompanyid1=" + j;
/* 1044 */             } else if (str16.equals("0")) {
/* 1045 */               str4 = str4 + " and id=" + i;
/*      */             } 
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/* 1051 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(413, this.user.getLanguage()), "lastname", "lastname", 1)).setIsInputCol(BoolAttr.TRUE));
/* 1052 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(6086, this.user.getLanguage()), "jobtitlename", null, "weaver.hrm.job.JobTitlesComInfo.getJobTitlesname", 1));
/* 1053 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(141, this.user.getLanguage()), "departmentname", null, "weaver.hrm.company.DepartmentComInfo.getDepartmentmark"));
/*      */     
/* 1055 */     hashMap.putAll(SplitTableUtil.makeListDataResult(new SplitTableBean(str2, str3, str4, str5, str6, arrayList)));
/* 1056 */     return (Map)hashMap;
/*      */   }
/*      */   
/*      */   public String getAllParentsOrg(String paramString1, String paramString2) {
/* 1060 */     String[] arrayOfString = paramString2.split("\\+");
/* 1061 */     boolean bool = "1".equals(arrayOfString[0]);
/* 1062 */     boolean bool1 = !"".equals((arrayOfString.length > 1) ? Util.null2String(arrayOfString[1]) : "") ? true : false;
/* 1063 */     String str = getPName("", paramString1, bool, bool1);
/* 1064 */     if (bool1) {
/* 1065 */       CompanyVirtualComInfo companyVirtualComInfo = new CompanyVirtualComInfo();
/* 1066 */       str = ("".equals(str) ? "" : (str + "/")) + companyVirtualComInfo.getVirtualType(Util.null2String(arrayOfString[1]));
/*      */     } else {
/* 1068 */       CompanyComInfo companyComInfo = new CompanyComInfo();
/* 1069 */       str = ("".equals(str) ? "" : (str + "/")) + companyComInfo.getCompanyname("1");
/*      */     } 
/* 1071 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getPName(String paramString1, String paramString2, boolean paramBoolean1, boolean paramBoolean2) {
/* 1076 */     if ("0".equals(paramString2) || "".equals(paramString2)) return paramString1; 
/* 1077 */     String str1 = "";
/* 1078 */     String str2 = "";
/* 1079 */     if (paramBoolean1) {
/* 1080 */       if (paramBoolean2) {
/* 1081 */         DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/* 1082 */         str1 = departmentVirtualComInfo.getDepartmentmark(paramString2);
/* 1083 */         paramBoolean1 = !"0".equals(Util.null2s(departmentVirtualComInfo.getDepartmentsupdepid(paramString2), "0"));
/* 1084 */         str2 = paramBoolean1 ? departmentVirtualComInfo.getDepartmentsupdepid(paramString2) : departmentVirtualComInfo.getSubcompanyid1(paramString2);
/*      */       } else {
/* 1086 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1087 */         str1 = departmentComInfo.getDepartmentmark(paramString2);
/* 1088 */         paramBoolean1 = !"0".equals(Util.null2s(departmentComInfo.getDepartmentsupdepid(paramString2), "0"));
/* 1089 */         str2 = paramBoolean1 ? departmentComInfo.getDepartmentsupdepid(paramString2) : departmentComInfo.getSubcompanyid1(paramString2);
/*      */       }
/*      */     
/* 1092 */     } else if (paramBoolean2) {
/* 1093 */       SubCompanyVirtualComInfo subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/* 1094 */       str1 = subCompanyVirtualComInfo.getSubCompanyname(paramString2);
/* 1095 */       str2 = Util.null2String(subCompanyVirtualComInfo.getSupsubcomid(paramString2));
/*      */     } else {
/* 1097 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1098 */       str1 = subCompanyComInfo.getSubCompanyname(paramString2);
/* 1099 */       str2 = Util.null2String(subCompanyComInfo.getSupsubcomid(paramString2));
/*      */     } 
/*      */     
/* 1102 */     paramString1 = ("".equals(paramString1) ? "" : (paramString1 + "/")) + str1;
/* 1103 */     return getPName(paramString1, str2, paramBoolean1, paramBoolean2);
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 1108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1109 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 1110 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 1111 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*      */     
/* 1113 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 413, "lastname", true));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1131 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 1132 */     arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 602, "status", arrayList1));
/* 1133 */     String str = Util.null2String(paramMap.get("changeType"));
/* 1134 */     if (str.length() == 0) {
/*      */       
/* 1136 */       RemindSettings remindSettings = (new ChgPasswdReminder()).getRemindSettings();
/* 1137 */       String str1 = Util.null2String(remindSettings.getCheckUnJob(), "0");
/* 1138 */       if ("1".equals(str1)) {
/* 1139 */         if (HrmUserVarify.checkUserRight("hrm:departureView", this.user)) {
/* 1140 */           arrayList1.add(new SearchConditionOption("9", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/*      */         }
/*      */       } else {
/* 1143 */         arrayList1.add(new SearchConditionOption("9", SystemEnv.getHtmlLabelName(332, this.user.getLanguage())));
/*      */       } 
/* 1145 */       arrayList1.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(1831, this.user.getLanguage()), true));
/* 1146 */       arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(15710, this.user.getLanguage())));
/* 1147 */       arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15711, this.user.getLanguage())));
/* 1148 */       arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(480, this.user.getLanguage())));
/* 1149 */       arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15844, this.user.getLanguage())));
/* 1150 */       if ("1".equals(str1)) {
/* 1151 */         if (HrmUserVarify.checkUserRight("hrm:departureView", this.user)) {
/* 1152 */           arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(6094, this.user.getLanguage())));
/* 1153 */           arrayList1.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(6091, this.user.getLanguage())));
/* 1154 */           arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(6092, this.user.getLanguage())));
/* 1155 */           arrayList1.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(2245, this.user.getLanguage())));
/*      */         } 
/*      */       } else {
/* 1158 */         arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(6094, this.user.getLanguage())));
/* 1159 */         arrayList1.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(6091, this.user.getLanguage())));
/* 1160 */         arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(6092, this.user.getLanguage())));
/* 1161 */         arrayList1.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(2245, this.user.getLanguage())));
/*      */       } 
/*      */     } else {
/* 1164 */       arrayList1.add(new SearchConditionOption("9", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/* 1165 */       if (str.equals("hrmtry")) {
/* 1166 */         arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(480, this.user.getLanguage())));
/* 1167 */       } else if (str.equals("retire")) {
/* 1168 */         arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15711, this.user.getLanguage())));
/* 1169 */       } else if (str.equals("rehire")) {
/* 1170 */         arrayList1.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(6094, this.user.getLanguage())));
/* 1171 */         arrayList1.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(6091, this.user.getLanguage())));
/* 1172 */         arrayList1.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(6092, this.user.getLanguage())));
/* 1173 */         arrayList1.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(2245, this.user.getLanguage())));
/* 1174 */       } else if (str.equals("redeploy") || str.equals("fire") || str.equals("extend")) {
/* 1175 */         arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(15710, this.user.getLanguage())));
/* 1176 */         arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15711, this.user.getLanguage())));
/* 1177 */         arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(480, this.user.getLanguage())));
/* 1178 */         arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15844, this.user.getLanguage())));
/* 1179 */       } else if (str.equals("hire")) {
/* 1180 */         arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(15710, this.user.getLanguage())));
/* 1181 */         arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(480, this.user.getLanguage())));
/* 1182 */         arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15844, this.user.getLanguage())));
/* 1183 */       } else if (str.equals("dismiss")) {
/* 1184 */         arrayList1.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(1831, this.user.getLanguage())));
/* 1185 */         arrayList1.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(15710, this.user.getLanguage())));
/* 1186 */         arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15711, this.user.getLanguage())));
/* 1187 */         arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(480, this.user.getLanguage())));
/* 1188 */         arrayList1.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15844, this.user.getLanguage())));
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1193 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 141, "subcompanyid", "194"));
/*      */     
/* 1195 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 124, "departmentid", "57"));
/*      */     
/* 1197 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 6086, "jobtitle"));
/*      */     
/* 1199 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 122, "roleid", "267"));
/* 1200 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 1205 */     this.isDetachBrower = true;
/* 1206 */     this.params = paramMap;
/* 1207 */     getSubCompanyTreeListByDecRight();
/* 1208 */     MutilResourceBrowser mutilResourceBrowser = new MutilResourceBrowser();
/* 1209 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1210 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 1211 */     String str2 = Util.null2String(paramMap.get("types"));
/* 1212 */     String[] arrayOfString = str2.split(",");
/* 1213 */     String str3 = (Util.null2String(paramMap.get("alllevel")).length() == 0) ? "0" : Util.null2String(paramMap.get("alllevel"));
/*      */     
/* 1215 */     String str4 = Util.null2String(paramMap.get("lastname"));
/* 1216 */     String str5 = Util.null2String(paramMap.get("virtualtype"));
/* 1217 */     String str6 = Util.null2String(paramMap.get("status"));
/* 1218 */     String str7 = Util.null2String(paramMap.get("subcompanyid"));
/* 1219 */     String str8 = Util.null2String(paramMap.get("departmentid"));
/* 1220 */     String str9 = Util.null2String(paramMap.get("jobtitle"));
/* 1221 */     String str10 = Util.null2String(paramMap.get("roleid"));
/*      */ 
/*      */     
/* 1224 */     String str11 = "";
/* 1225 */     WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/* 1226 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*      */ 
/*      */     
/* 1229 */     Map map = workflowCommonServiceImpl.getDataDefinitionDataRanageSet(paramMap, this.user, 1);
/* 1230 */     if (Util.null2String(map.get("sqlWhere")).length() > 0) {
/* 1231 */       str11 = str11 + Util.null2String(map.get("sqlWhere"));
/*      */     }
/*      */     
/* 1234 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1235 */     String str12 = "select ";
/* 1236 */     String str13 = "";
/* 1237 */     str12 = str12 + "id,lastname, departmentid ";
/* 1238 */     str12 = str12 + " from HrmResource";
/* 1239 */     str12 = str12 + " where 1=1 ";
/*      */     
/* 1241 */     if (!"".equals(str2)) {
/* 1242 */       ArrayList<String> arrayList2 = new ArrayList();
/* 1243 */       for (String str14 : arrayOfString) {
/* 1244 */         String str15 = "";
/* 1245 */         String[] arrayOfString1 = str14.split("\\|");
/* 1246 */         if (arrayOfString1.length > 1) {
/* 1247 */           String str16 = arrayOfString1[0];
/* 1248 */           String str17 = arrayOfString1[1];
/* 1249 */           if (str16.equals("subcom") || str16.equals("dept") || str16.equals("com")) {
/*      */             
/* 1251 */             String str18 = str16 + "_" + str17;
/* 1252 */             if (Integer.parseInt(str17) < 0) {
/*      */               
/* 1254 */               str15 = mutilResourceBrowser.getComDeptResourceVirtualIds(str18, str3, "0", this.user, str11);
/*      */             } else {
/* 1256 */               str15 = mutilResourceBrowser.getComDeptResourceIds(str18, str3, "0", this.user, str11);
/*      */             } 
/* 1258 */           } else if (str16.equals("group")) {
/* 1259 */             if (str17.startsWith("-")) {
/*      */               
/* 1261 */               str15 = str17;
/*      */             } else {
/* 1263 */               str15 = mutilResourceBrowser.getGroupResourceIds(str17, "0", this.user, str11);
/*      */             } 
/*      */           } 
/*      */           
/* 1267 */           String[] arrayOfString2 = Util.TokenizerString2(str15, ",");
/* 1268 */           for (String str18 : arrayOfString2) {
/* 1269 */             if (!"".equals(Util.null2String(str18)))
/*      */             {
/* 1271 */               arrayList2.add(str18);
/*      */             }
/*      */           } 
/* 1274 */         } else if (!"".equals(Util.null2String(str14))) {
/*      */           
/* 1276 */           String[] arrayOfString2 = Util.TokenizerString2(str14, ",");
/* 1277 */           for (String str16 : arrayOfString2) {
/* 1278 */             if (!"".equals(Util.null2String(str16)))
/*      */             {
/* 1280 */               arrayList2.add(str16); } 
/*      */           } 
/*      */         } 
/*      */       } 
/* 1284 */       if (arrayList2.size() > 0) {
/* 1285 */         str1 = String.join(",", (Iterable)arrayList2);
/* 1286 */         str12 = str12 + " and " + Util.getSubINClause(str1, "id", "in");
/*      */       } 
/* 1288 */       String str = "";
/* 1289 */       if (this.isDetachBrower) {
/* 1290 */         str = ""; byte b;
/* 1291 */         for (b = 0; b < this.UserDepts1.size(); b++) {
/* 1292 */           if (str.length() > 0) str = str + ","; 
/* 1293 */           str = str + this.UserDepts1.get(b);
/*      */         } 
/* 1295 */         if (!"".equals(str)) {
/* 1296 */           str12 = str12 + " and (" + Tools.getOracleSQLIn(str, "departmentid") + ")";
/*      */         }
/*      */         
/* 1299 */         str = "";
/* 1300 */         for (b = 0; b < this.subcomids1.length; b++) {
/* 1301 */           if (str.length() > 0) str = str + ","; 
/* 1302 */           str = str + this.subcomids1[b];
/*      */         } 
/* 1304 */         if (!"".equals(str)) {
/* 1305 */           str12 = str12 + " and (" + Tools.getOracleSQLIn(str, "subcompanyid1") + ")";
/*      */         }
/*      */         
/* 1308 */         if (this.UserDepts1.size() == 0 && this.subcomids1.length == 0) {
/* 1309 */           str12 = str12 + " and 1=2 ";
/*      */         }
/*      */         
/* 1312 */         boolean bool = (new ManageDetachComInfo()).isUseHrmManageDetach();
/* 1313 */         if (!bool) {
/* 1314 */           String str14 = Util.null2String(this.params.get("rightStr"));
/* 1315 */           if (str14.length() > 0) {
/* 1316 */             String str15 = HrmUserVarify.getRightLevel(str14, this.user);
/* 1317 */             int i = this.user.getUserDepartment();
/* 1318 */             int j = this.user.getUserSubCompany1();
/* 1319 */             if (!str15.equals("2"))
/*      */             {
/* 1321 */               if (str15.equals("1")) {
/* 1322 */                 str12 = str12 + " and subcompanyid1=" + j;
/* 1323 */               } else if (str15.equals("0")) {
/* 1324 */                 str12 = str12 + " and id=" + i;
/*      */               }  } 
/*      */           } 
/*      */         } 
/*      */       } 
/* 1329 */     } else if (str1.length() > 0) {
/* 1330 */       str12 = str12 + " and " + Util.getSubINClause(str1, "id", "in");
/* 1331 */     } else if (str1.length() <= 0) {
/* 1332 */       str12 = str12 + " and 1=2 ";
/*      */     } 
/* 1334 */     if (str11.length() > 0) {
/* 1335 */       str12 = str12 + str11;
/*      */     }
/*      */     
/* 1338 */     RecordSet recordSet = new RecordSet();
/* 1339 */     recordSet.executeSql(str12);
/* 1340 */     while (recordSet.next()) {
/* 1341 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1342 */       arrayList.add(hashMap1);
/* 1343 */       String str = Util.null2String(recordSet.getString("id"));
/* 1344 */       hashMap1.put("id", str);
/* 1345 */       hashMap1.put("lastname", recordSet.getString("lastname"));
/* 1346 */       hashMap1.put("jobtitlename", MutilResourceBrowser.getJobTitlesname(str, this.user));
/* 1347 */       hashMap1.put("departmentname", departmentComInfo.getDepartmentmark(recordSet.getString("departmentid")));
/*      */     } 
/* 1349 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 1350 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 1351 */     arrayList1.add(new ListHeadBean("lastname", SystemEnv.getHtmlLabelName(413, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 1352 */     arrayList1.add(new ListHeadBean("jobtitlename", "", 1));
/* 1353 */     arrayList1.add(new ListHeadBean("departmentname", ""));
/*      */     
/* 1355 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 1356 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 1357 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 1358 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isDepartmentBrowser() {
/* 1367 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getSubCompanyTreeListByDecRight() throws Exception {
/* 1375 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 1376 */     String str1 = Util.null2String(this.params.get("rightStr"));
/* 1377 */     if ("".equals(str1)) {
/* 1378 */       str1 = "Resources:decentralization";
/*      */     }
/* 1380 */     int i = Util.getIntValue(Util.null2String(this.params.get("beagenter_" + this.user.getUID())), 0);
/* 1381 */     if (i <= 0) {
/* 1382 */       i = this.user.getUID();
/*      */     }
/* 1384 */     int j = Util.getIntValue(Util.null2String(this.params.get("viewtype")), 0);
/* 1385 */     int k = Util.getIntValue(Util.null2String(this.params.get("isbill")), 0);
/* 1386 */     int m = Util.getIntValue(Util.null2String(this.params.get("fieldid")), 0);
/* 1387 */     boolean bool = checkSubCompanyRight.getDecentralizationAttr(i, str1, m, j, k);
/*      */ 
/*      */     
/* 1390 */     String str2 = Util.null2String(this.params.get("isruledesign"));
/* 1391 */     boolean bool1 = false;
/* 1392 */     String str3 = "select * from HrmResourceManager where id = " + i;
/* 1393 */     RecordSet recordSet = new RecordSet();
/* 1394 */     recordSet.executeSql(str3);
/* 1395 */     if (recordSet.next()) {
/* 1396 */       bool1 = true;
/*      */     }
/*      */     
/* 1399 */     if (bool1 && "true".equals(str2)) {
/* 1400 */       i = 1;
/*      */     }
/* 1402 */     if (m <= 0) {
/* 1403 */       this.UserDepts = checkSubCompanyRight.getDepartmentPathByDec(this.user.getUID(), bool);
/* 1404 */       this.subcomids = checkSubCompanyRight.getSubComPathByDecUserRightId(this.user.getUID(), str1, 0);
/* 1405 */       this.subcomids1 = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), str1, 0);
/*      */       
/* 1407 */       boolean bool2 = (new ManageDetachComInfo()).isUseHrmManageDetach();
/* 1408 */       if (!bool2 && 
/* 1409 */         str1.length() > 0) {
/* 1410 */         String str = HrmUserVarify.getRightLevel(str1, this.user);
/* 1411 */         int n = this.user.getUserDepartment();
/* 1412 */         if (str.equals("0")) {
/* 1413 */           this.UserDepts1.add("" + n);
/*      */         }
/*      */       } 
/*      */     } else {
/*      */       
/* 1418 */       OrganizationUtil organizationUtil = new OrganizationUtil();
/*      */       
/* 1420 */       organizationUtil.selectData(this.user.getUID(), m + "", j, k, str1);
/* 1421 */       this.textheightLists = organizationUtil.getTextheightLists();
/* 1422 */       ArrayList<String> arrayList1 = organizationUtil.getSubcomList();
/* 1423 */       ArrayList arrayList = organizationUtil.getDepatList();
/* 1424 */       ArrayList<String> arrayList2 = organizationUtil.getNecessarySupcomList();
/* 1425 */       this.UserDepts = organizationUtil.getNecessarySupDepatList();
/*      */       
/* 1427 */       this.subcomids1 = new int[arrayList1.size()]; byte b;
/* 1428 */       for (b = 0; b < arrayList1.size(); b++) {
/* 1429 */         String str = arrayList1.get(b);
/* 1430 */         this.subcomids1[b] = Util.getIntValue(str);
/* 1431 */         if (!arrayList2.contains(str)) {
/* 1432 */           arrayList2.add(str);
/*      */         }
/*      */       } 
/*      */       
/* 1436 */       this.subcomids = new int[arrayList2.size()];
/* 1437 */       for (b = 0; b < arrayList2.size(); b++) {
/* 1438 */         this.subcomids[b] = Util.getIntValue((String)arrayList2.get(b));
/*      */       }
/*      */       
/* 1441 */       this.UserDepts1 = arrayList;
/* 1442 */       for (b = 0; b < arrayList.size(); b++) {
/* 1443 */         String str = (new StringBuilder()).append(arrayList.get(b)).append("").toString();
/* 1444 */         if (!this.UserDepts.contains(str)) {
/* 1445 */           this.UserDepts.add(str);
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   private boolean checkDetachDel(String paramString1, String paramString2) {
/* 1475 */     boolean bool = true;
/* 1476 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1477 */     if (paramString1.equals("com")) {
/*      */       
/* 1479 */       if (this.dataRanageAllSubCompanyIds != null && !this.dataRanageAllSubCompanyIds.isEmpty()) {
/* 1480 */         bool = false;
/* 1481 */         if (this.dataRanageAllSubCompanyIds.contains(paramString2)) {
/* 1482 */           bool = true;
/*      */         }
/*      */       } 
/*      */       
/* 1486 */       if (this.isDetachBrower) {
/* 1487 */         bool = false;
/* 1488 */         for (byte b = 0; b < this.subcomids.length; b++) {
/* 1489 */           if (paramString2.equals(String.valueOf(this.subcomids[b]))) {
/* 1490 */             bool = true;
/*      */             break;
/*      */           } 
/*      */         } 
/*      */       } else {
/* 1495 */         if (this.adci == null) {
/* 1496 */           this.adci = new AppDetachComInfo(this.user);
/*      */         }
/* 1498 */         if (this.adci.isUseAppDetach()) {
/* 1499 */           if (this.adci.checkUserAppDetach(paramString2, "2") == 0) {
/* 1500 */             bool = false;
/*      */           } else {
/* 1502 */             bool = true;
/*      */           } 
/*      */         }
/*      */       } 
/* 1506 */     } else if (paramString1.equals("dept")) {
/* 1507 */       String str = departmentComInfo.getSubcompanyid1(paramString2);
/*      */       
/* 1509 */       if (this.dataRanageAllDepartmentIds != null && !this.dataRanageAllDepartmentIds.isEmpty()) {
/* 1510 */         bool = false;
/* 1511 */         if (this.dataRanageAllDepartmentIds.contains(paramString2)) {
/* 1512 */           bool = true;
/*      */         }
/* 1514 */         else if (this.dataRanageSubCompanyIds.contains(str)) {
/* 1515 */           bool = true;
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1520 */       if (this.isDetachBrower) {
/* 1521 */         bool = false;
/* 1522 */         if (this.UserDepts.size() > 0 && this.UserDepts.indexOf(paramString2) != -1) {
/* 1523 */           bool = true;
/* 1524 */         } else if (this.subcomids1.length > 0) {
/* 1525 */           for (byte b = 0; b < this.subcomids1.length; b++) {
/* 1526 */             if (str.equals(String.valueOf(this.subcomids1[b]))) {
/* 1527 */               bool = true;
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } else {
/* 1532 */         if (this.adci == null) {
/* 1533 */           this.adci = new AppDetachComInfo(this.user);
/*      */         }
/* 1535 */         if (this.adci.isUseAppDetach()) {
/* 1536 */           if (this.adci.checkUserAppDetach(paramString2, "3") == 0) {
/* 1537 */             bool = false;
/*      */           } else {
/* 1539 */             bool = true;
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/* 1544 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean checkDetach(String paramString1, String paramString2) {
/* 1555 */     boolean bool = true;
/* 1556 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1557 */     if (paramString1.equals("com")) {
/*      */       
/* 1559 */       if (this.dataRanageAllSubCompanyIds != null && !this.dataRanageAllSubCompanyIds.isEmpty()) {
/* 1560 */         bool = false;
/* 1561 */         if (this.dataRanageAllSubCompanyIds.contains(paramString2)) {
/* 1562 */           bool = true;
/*      */         }
/*      */       } 
/*      */       
/* 1566 */       if (this.isDetachBrower) {
/* 1567 */         bool = false;
/* 1568 */         for (byte b = 0; b < this.subcomids.length; b++) {
/* 1569 */           if (paramString2.equals(String.valueOf(this.subcomids[b]))) {
/* 1570 */             bool = true;
/*      */             break;
/*      */           } 
/*      */         } 
/*      */       } else {
/* 1575 */         if (this.adci == null) {
/* 1576 */           this.adci = new AppDetachComInfo(this.user);
/*      */         }
/* 1578 */         if (this.adci.isUseAppDetach()) {
/* 1579 */           if (this.adci.checkUserAppDetach(paramString2, "2") == 0) {
/* 1580 */             bool = false;
/*      */           } else {
/* 1582 */             bool = true;
/*      */           } 
/*      */         }
/*      */       } 
/* 1586 */     } else if (paramString1.equals("dept")) {
/* 1587 */       String str = departmentComInfo.getSubcompanyid1(paramString2);
/*      */       
/* 1589 */       if (this.dataRanageAllDepartmentIds != null && !this.dataRanageAllDepartmentIds.isEmpty()) {
/* 1590 */         bool = false;
/* 1591 */         if (this.dataRanageAllDepartmentIds.contains(paramString2)) {
/* 1592 */           bool = true;
/*      */         }
/* 1594 */         else if (this.dataRanageAllSubCompanyIds.contains(str)) {
/* 1595 */           bool = true;
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1600 */       if (this.isDetachBrower) {
/* 1601 */         bool = false;
/* 1602 */         if (this.UserDepts.size() > 0 && this.UserDepts.indexOf(paramString2) != -1) {
/* 1603 */           bool = true;
/* 1604 */         } else if (this.subcomids1.length > 0) {
/* 1605 */           for (byte b = 0; b < this.subcomids1.length; b++) {
/* 1606 */             if (str.equals(String.valueOf(this.subcomids1[b]))) {
/* 1607 */               bool = true;
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/* 1613 */     return bool;
/*      */   }
/*      */   
/*      */   private void generateRange(Map<String, Object> paramMap) {
/*      */     try {
/* 1618 */       String str = Util.null2String(paramMap.get("sqlWhere"));
/* 1619 */       if (str.length() > 0) {
/* 1620 */         SubCompanyVirtualComInfo subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/* 1621 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1622 */         DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/* 1623 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*      */         
/* 1625 */         RecordSet recordSet = new RecordSet();
/* 1626 */         HashSet<String> hashSet1 = new HashSet();
/* 1627 */         HashSet<String> hashSet2 = new HashSet();
/* 1628 */         HashSet<String> hashSet3 = new HashSet();
/* 1629 */         HashSet<String> hashSet4 = new HashSet();
/*      */         
/* 1631 */         String str1 = "select id, subcompanyid1, departmentid from HRMRESOURCEALLVIEW where 1 = 1" + str;
/* 1632 */         recordSet.executeQuery(str1, new Object[0]);
/* 1633 */         while (recordSet.next()) {
/* 1634 */           String str2 = Util.null2String(recordSet.getString("id"));
/* 1635 */           String str3 = Util.null2String(recordSet.getString("subcompanyid1"));
/* 1636 */           String str4 = Util.null2String(recordSet.getString("departmentid"));
/* 1637 */           if (!"".equals(str2))
/* 1638 */             this.resourceids.add(str2); 
/* 1639 */           if (!"".equals(str3)) {
/* 1640 */             hashSet1.add(str3);
/*      */           }
/* 1642 */           if (!"".equals(str3)) {
/* 1643 */             hashSet2.add(str4);
/*      */           }
/*      */         } 
/*      */         
/* 1647 */         Iterator<String> iterator = hashSet1.iterator();
/* 1648 */         while (iterator.hasNext()) {
/* 1649 */           String str2 = "";
/* 1650 */           String str3 = iterator.next();
/* 1651 */           hashSet3.add(str3);
/* 1652 */           if (Integer.parseInt(str3) < 0) {
/* 1653 */             str2 = subCompanyVirtualComInfo.getAllSupCompany(str3);
/*      */           } else {
/* 1655 */             str2 = subCompanyComInfo.getAllSupCompany(str3);
/*      */           } 
/* 1657 */           String[] arrayOfString = Util.splitString(str2, ",");
/* 1658 */           for (String str4 : arrayOfString) {
/* 1659 */             hashSet3.add(str4);
/*      */           }
/*      */         } 
/*      */         
/* 1663 */         iterator = hashSet3.iterator();
/* 1664 */         while (iterator.hasNext()) {
/* 1665 */           String str2 = iterator.next();
/* 1666 */           if (!"".equals(str2))
/* 1667 */             this.dataRanageAllSubCompanyIds.add(str2); 
/*      */         } 
/* 1669 */         if (hashSet3.size() == 0) {
/* 1670 */           this.dataRanageAllSubCompanyIds.add("");
/*      */         }
/* 1672 */         iterator = hashSet2.iterator();
/* 1673 */         while (iterator.hasNext()) {
/* 1674 */           String str2 = "";
/* 1675 */           String str3 = iterator.next();
/* 1676 */           hashSet4.add(str3);
/* 1677 */           if (Integer.parseInt(str3) < 0) {
/* 1678 */             str2 = departmentVirtualComInfo.getAllSupDepartment(str3);
/*      */           } else {
/* 1680 */             str2 = departmentComInfo.getAllSupDepartment(str3);
/*      */           } 
/* 1682 */           String[] arrayOfString = Util.splitString(str2, ",");
/* 1683 */           for (String str4 : arrayOfString) {
/* 1684 */             hashSet4.add(str4);
/*      */           }
/*      */         } 
/*      */         
/* 1688 */         iterator = hashSet4.iterator();
/* 1689 */         while (iterator.hasNext()) {
/* 1690 */           String str2 = iterator.next();
/* 1691 */           if (!"".equals(str2))
/* 1692 */             this.dataRanageAllDepartmentIds.add(str2); 
/*      */         } 
/* 1694 */         if (hashSet4.size() == 0)
/* 1695 */           this.dataRanageAllDepartmentIds.add(""); 
/*      */       } 
/* 1697 */     } catch (Exception exception) {
/* 1698 */       exception.printStackTrace();
/* 1699 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */   private class OrgBean extends BrowserTreeNode { private String companyid;
/*      */     private String isVirtual;
/*      */     private String psubcompanyid;
/*      */     private String requestParams;
/*      */     
/*      */     private OrgBean() {}
/*      */     
/*      */     public String getCompanyid() {
/* 1710 */       return this.companyid;
/*      */     }
/*      */     
/*      */     public void setCompanyid(String param1String) {
/* 1714 */       this.companyid = param1String;
/*      */     }
/*      */ 
/*      */     
/*      */     public String getIsVirtual() {
/* 1719 */       return this.isVirtual;
/*      */     }
/*      */     
/*      */     public void setIsVirtual(String param1String) {
/* 1723 */       this.isVirtual = param1String;
/*      */     }
/*      */     
/*      */     public String getPsubcompanyid() {
/* 1727 */       return this.psubcompanyid;
/*      */     }
/*      */     
/*      */     public void setPsubcompanyid(String param1String) {
/* 1731 */       this.psubcompanyid = param1String;
/*      */     }
/*      */     
/*      */     public String getRequestParams() {
/* 1735 */       return this.requestParams;
/*      */     }
/*      */     
/*      */     public void setRequestParams(String param1String) {
/* 1739 */       this.requestParams = param1String;
/*      */     } }
/*      */   
/*      */   private Map<String, Object> getGroupData(Map<String, Object> paramMap) {
/*      */     Map<String, Object> map;
/* 1744 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/* 1746 */       String str1 = Util.null2String(paramMap.get("cmd"));
/* 1747 */       String str2 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 1748 */       String str3 = " ";
/*      */ 
/*      */       
/* 1751 */       String str4 = "";
/* 1752 */       WorkflowCommonServiceImpl workflowCommonServiceImpl = new WorkflowCommonServiceImpl();
/* 1753 */       Map map1 = workflowCommonServiceImpl.getDataDefinitionDataRanageSet(paramMap, this.user, 1);
/* 1754 */       if (Util.null2String(map1.get("sqlWhere")).length() > 0) {
/* 1755 */         str4 = str4 + Util.null2String(map1.get("sqlWhere"));
/*      */       }
/*      */       
/* 1758 */       if (!"".equals(str4)) {
/* 1759 */         str3 = str3 + str4;
/*      */       }
/*      */       
/* 1762 */       if (!"".equals(str2)) {
/* 1763 */         str3 = str3 + "(" + Util.getSubINClause(str2, "hr.id", " not in ") + ")";
/*      */       }
/*      */       
/* 1766 */       String str5 = "";
/* 1767 */       if (this.isDetachBrower) {
/* 1768 */         str5 = ""; byte b;
/* 1769 */         for (b = 0; b < this.UserDepts1.size(); b++) {
/* 1770 */           if (str5.length() > 0) str5 = str5 + ","; 
/* 1771 */           str5 = str5 + this.UserDepts1.get(b);
/*      */         } 
/* 1773 */         if (!"".equals(str5)) {
/* 1774 */           str3 = str3 + " and (" + Tools.getOracleSQLIn(str5, "hr.departmentid") + ")";
/*      */         }
/*      */         
/* 1777 */         str5 = "";
/* 1778 */         for (b = 0; b < this.subcomids1.length; b++) {
/* 1779 */           if (str5.length() > 0) str5 = str5 + ","; 
/* 1780 */           str5 = str5 + this.subcomids1[b];
/*      */         } 
/* 1782 */         if (!"".equals(str5)) {
/* 1783 */           str3 = str3 + " and (" + Tools.getOracleSQLIn(str5, "hr.subcompanyid1") + ")";
/*      */         }
/*      */         
/* 1786 */         if (this.UserDepts1.size() == 0 && this.subcomids1.length == 0) {
/* 1787 */           str3 = str3 + " and 1=2 ";
/*      */         }
/*      */         
/* 1790 */         boolean bool = (new ManageDetachComInfo()).isUseHrmManageDetach();
/* 1791 */         if (!bool) {
/* 1792 */           String str = Util.null2String(this.params.get("rightStr"));
/* 1793 */           if (str.length() > 0) {
/* 1794 */             String str6 = HrmUserVarify.getRightLevel(str, this.user);
/* 1795 */             int i = this.user.getUserDepartment();
/* 1796 */             int j = this.user.getUserSubCompany1();
/* 1797 */             if (!str6.equals("2"))
/*      */             {
/* 1799 */               if (str6.equals("1")) {
/* 1800 */                 str3 = str3 + " and hr.subcompanyid1=" + j;
/* 1801 */               } else if (str6.equals("0")) {
/* 1802 */                 str3 = str3 + " and hr.departmentid=" + i;
/*      */               }  } 
/*      */           } 
/*      */         } 
/*      */       } 
/* 1807 */       ResourceBrowserService resourceBrowserService = new ResourceBrowserService();
/* 1808 */       resourceBrowserService.setUser(this.user);
/* 1809 */       paramMap.put("from", "ResourceBrowserDecService");
/* 1810 */       paramMap.put("sqlwhere", str3);
/* 1811 */       map = resourceBrowserService.getBrowserData(paramMap);
/* 1812 */       if ("v2grouptree".equals(str1)) {
/* 1813 */         map.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*      */       }
/* 1815 */     } catch (Exception exception) {
/* 1816 */       writeLog(exception);
/*      */     } 
/* 1818 */     return map;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ResourceBrowserDecService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */