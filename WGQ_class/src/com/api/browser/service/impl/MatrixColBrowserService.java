/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixinfoComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.chain.MatrixCondition;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.matrix.MatrixManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MatrixColBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  36 */     String str1 = Util.null2String(paramMap.get("matrixid"));
/*  37 */     String str2 = Util.null2String(paramMap.get("displayname"));
/*  38 */     String str3 = "where 1 = 1 and fieldtype=1 ";
/*  39 */     if (str1.length() == 0) {
/*  40 */       return (Map)hashMap;
/*     */     }
/*  42 */     String str4 = "";
/*  43 */     int i = 0;
/*  44 */     MatrixinfoComInfo matrixinfoComInfo = new MatrixinfoComInfo();
/*  45 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  46 */     boolean bool = manageDetachComInfo.isUseHrmManageDetach();
/*  47 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  48 */     String str5 = matrixinfoComInfo.getIssystem(str1);
/*  49 */     MatrixManager matrixManager = new MatrixManager();
/*  50 */     MatrixCondition matrixCondition = matrixManager.getMatrixConditionByMaintUser(this.user.getUID() + "", str1);
/*  51 */     if (bool) {
/*  52 */       String str = matrixinfoComInfo.getSubcompanyid(str1);
/*  53 */       if (str.length() == 0 || Util.getIntValue(str, -1) < 1) {
/*  54 */         str = manageDetachComInfo.getHrmdftsubcomid();
/*     */       }
/*  56 */       i = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "Matrix:Maint", Util.getIntValue(str, -1));
/*     */       
/*  58 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "Matrix:Maint");
/*  59 */       int j = -1;
/*  60 */       for (byte b = 0; arrayOfInt != null && b < arrayOfInt.length; b++) {
/*  61 */         int k = arrayOfInt[b];
/*  62 */         int m = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "Matrix:Maint", k);
/*  63 */         if (m > j)
/*     */         {
/*  65 */           j = m;
/*     */         }
/*     */       } 
/*  68 */       if ("1".equalsIgnoreCase(str5) || "2".equalsIgnoreCase(str5)) {
/*  69 */         i = j;
/*     */       }
/*     */     } 
/*  72 */     if (matrixCondition != null && matrixCondition.getRightLevel() > 1 && !"1".equalsIgnoreCase(str5) && !"2".equalsIgnoreCase(str5)) {
/*  73 */       i = 1;
/*     */     }
/*  75 */     if (matrixCondition != null) {
/*  76 */       str4 = matrixCondition.getColids();
/*  77 */       if (str4.length() > 0)
/*     */       {
/*  79 */         if (i > 0)
/*     */         {
/*  81 */           str4 = "";
/*     */         }
/*     */       }
/*     */     } 
/*  85 */     RecordSet recordSet = new RecordSet();
/*  86 */     String str6 = " * ";
/*  87 */     String str7 = " MATRIXFIELDINFO ";
/*  88 */     str3 = str3 + " and matrixid = " + str1;
/*  89 */     if (str4.length() > 0) {
/*  90 */       str3 = str3 + " and " + Util.getSubINClause(str4, "id", "in");
/*     */     }
/*  92 */     if (str2.length() > 0) {
/*  93 */       str3 = str3 + " and displayname like '%" + str2 + "%'";
/*     */     }
/*     */     
/*  96 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  97 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  98 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(15930, this.user.getLanguage()), "displayname", "displayname", 1)).setIsInputCol(BoolAttr.TRUE));
/*  99 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str3, "id", "id", arrayList);
/* 100 */     splitTableBean.setSqlsortway("ASC");
/* 101 */     splitTableBean.setSqlisdistinct("true");
/* 102 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 103 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 109 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 110 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 111 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 112 */     if (this.user != null && !"".equals(str)) {
/* 113 */       String str1 = "";
/* 114 */       RecordSet recordSet = new RecordSet();
/* 115 */       recordSet.executeQuery(str1, new Object[0]);
/* 116 */       if (str.length() > 0) {
/* 117 */         String[] arrayOfString = str.split(",");
/*     */         
/* 119 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 120 */           str1 = "select * from MATRIXFIELDINFO where fieldtype=1  and id in ( " + arrayOfString[b] + ")";
/* 121 */           recordSet.execute(str1);
/* 122 */           if (recordSet.next()) {
/* 123 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 124 */             hashMap1.put("id", Util.null2String(recordSet.getString("id")));
/* 125 */             hashMap1.put("displayname", Util.null2String(recordSet.getString("displayname")));
/* 126 */             arrayList.add(hashMap1);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/* 131 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 132 */       arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 133 */       arrayList1.add((new ListHeadBean("displayname", "")).setIsInputCol(BoolAttr.TRUE));
/* 134 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 135 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 136 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 137 */       return (Map)hashMap;
/*     */     } 
/* 139 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 146 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 147 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 148 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 149 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15930, "displayname", true));
/* 150 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 151 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MatrixColBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */