/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaMutilTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  49 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  51 */     if (this.user == null) {
/*  52 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  53 */       return (Map)hashMap;
/*     */     } 
/*  55 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  56 */     SplitTableBean splitTableBean = getTableList(paramMap);
/*  57 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  59 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) throws Exception {
/*  69 */     String str1 = Util.null2String(paramMap.get("name"));
/*  70 */     String str2 = Util.null2String(paramMap.get("sqlwhere"));
/*  71 */     String str3 = Util.null2String(paramMap.get("dimensionTableName"));
/*  72 */     String str4 = "10";
/*  73 */     String str5 = " id,typeName ";
/*  74 */     String str6 = " FnaDimensionType ";
/*  75 */     String str7 = " where 1=1  and typeName<>' ' and typeName is not null";
/*     */     
/*  77 */     if (!"".equals(str1)) {
/*  78 */       str7 = str7 + " and typeName like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/*  80 */     if (!"".equals(str2)) {
/*  81 */       str7 = str7 + str2;
/*     */     }
/*  83 */     if (!"".equals(str3)) {
/*  84 */       str7 = str7 + " and id in( select typeId from " + str3 + " )";
/*     */     }
/*     */     
/*  87 */     String str8 = "";
/*  88 */     String str9 = "";
/*     */     
/*  90 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  91 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  92 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "typeName", "typeName")).setIsInputCol(BoolAttr.TRUE).setShowType(1));
/*     */     
/*  94 */     return new SplitTableBean("fnaMultiTypeBrowserList", "none", str4, "fnaMultiTypeBrowserList", str5, str6, str7, str8, str9, "ASC", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */     RecordSet recordSet = new RecordSet();
/* 110 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 111 */     String str2 = Util.null2String(paramMap.get("sqlwhere"));
/*     */     
/* 113 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 114 */     StringBuffer stringBuffer = new StringBuffer();
/* 115 */     stringBuffer.append(" select id,typeName from FnaDimensionType  ");
/* 116 */     stringBuffer.append(" where 1=1  ");
/* 117 */     stringBuffer.append(" and id in (").append(str1).append(") ");
/*     */     
/* 119 */     if (!"".equals(str2)) {
/* 120 */       stringBuffer.append(str2);
/*     */     }
/*     */     
/* 123 */     recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/* 124 */     while (recordSet.next()) {
/* 125 */       String str3 = Util.null2String(recordSet.getString("id"));
/* 126 */       String str4 = Util.null2String(recordSet.getString("typeName"));
/*     */       
/* 128 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 129 */       hashMap1.put("id", str3);
/* 130 */       hashMap1.put("typeName", str4);
/* 131 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 134 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 135 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 136 */     arrayList1.add(new ListHeadBean("typeName", "", 1, BoolAttr.TRUE));
/*     */     
/* 138 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 139 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 140 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 142 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaMutilTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */