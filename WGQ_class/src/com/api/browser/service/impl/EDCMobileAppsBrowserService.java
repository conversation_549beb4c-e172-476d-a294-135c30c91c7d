/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EDCMobileAppsBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  34 */     String str1 = Util.null2String(paramMap.get("appname")).trim();
/*  35 */     String str2 = " where isdelete = 0  and t1.id not in (select sourceid from edc_appsresource where sourcetype = 'mobile') ";
/*  36 */     if (!str1.equals("")) {
/*  37 */       str2 = str2 + " and t1.appname like '%" + str1 + "%' ";
/*     */     }
/*  39 */     String str3 = " t1.id,t1.appname";
/*  40 */     String str4 = " MobileAppBaseInfo t1 ";
/*  41 */     String str5 = " t1.showorder asc,t1.id desc ";
/*     */     
/*  43 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  44 */     arrayList.add(new SplitTableColBean("true", "id"));
/*     */ 
/*     */ 
/*     */     
/*  48 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(510564, this.user.getLanguage()), "id", "id", 1));
/*  49 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(513356, this.user.getLanguage()), "appname", "appname", 1));
/*     */     
/*  51 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "t1.id", arrayList);
/*     */     
/*  53 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  54 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getLabelName(String paramString1, String paramString2) {
/*  58 */     if ("1".equals(paramString2)) {
/*  59 */       return "<span style='position:relative;'>" + paramString1 + "<span class='cube-virtual-form-flag' >V</span></span>";
/*     */     }
/*  61 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  67 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  68 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  72 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 513356, "appname", true);
/*  73 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */     
/*  75 */     arrayList1.add(new SearchConditionOption("1", "" + SystemEnv.getHtmlLabelName(381923, ThreadVarLanguage.getLang()) + ""));
/*  76 */     searchConditionItem.setOptions(arrayList1);
/*  77 */     arrayList.add(searchConditionItem);
/*     */     
/*  79 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  80 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  88 */     String str2 = "";
/*  89 */     String str3 = "SELECT t1.id ,t1.appname FROM MobileAppBaseInfo t1   where t1.appname like '%" + str1 + "%' " + str2 + "order by t1.showorder asc,t1.id desc";
/*     */     
/*  91 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  92 */     RecordSet recordSet = new RecordSet();
/*  93 */     recordSet.executeQuery(str3, new Object[0]);
/*  94 */     while (recordSet.next()) {
/*  95 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  96 */       hashMap1.put("id", recordSet.getString("id"));
/*  97 */       hashMap1.put("name", recordSet.getString("appname"));
/*  98 */       arrayList.add(hashMap1);
/*     */     } 
/* 100 */     hashMap.put("datas", arrayList);
/*     */     
/* 102 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 103 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EDCMobileAppsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */