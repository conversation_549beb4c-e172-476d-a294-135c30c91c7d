/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WhiteListBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 32 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 33 */     String str1 = Util.null2String(paramMap.get("exclude_"));
/* 34 */     String str2 = "WHERE 1 = 1 ";
/* 35 */     String str3 = "";
/* 36 */     String str4 = "";
/* 37 */     String str5 = "";
/* 38 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 39 */     SplitTableBean splitTableBean = null;
/* 40 */     str3 = "id,exclude_,type_,desc_,orderNum";
/* 41 */     str4 = " FROM Int_WhiteList";
/* 42 */     str5 = "orderNum";
/* 43 */     if (!"".equals(str1)) {
/* 44 */       str2 = str2 + " AND exclude_ like '%" + str1 + "%' ";
/*    */     }
/* 46 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 47 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(513385, this.user.getLanguage()), "exclude_", "exclude_", 1)).setIsInputCol(BoolAttr.TRUE));
/* 48 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(85, this.user.getLanguage()), "desc_", "desc_"));
/* 49 */     splitTableBean = new SplitTableBean(str3, str4, str2, str5, "id", arrayList);
/* 50 */     splitTableBean.setSqlsortway("ASC");
/* 51 */     splitTableBean.setSqlisdistinct("true");
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 59 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 60 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 61 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 62 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 513385, "exclude_", true));
/* 63 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 69 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 70 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 71 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 72 */     RecordSet recordSet = new RecordSet();
/* 73 */     String str2 = " SELECT * FROM Int_WhiteList WHERE id in (" + str1 + ") ORDER BY orderNum";
/* 74 */     recordSet.executeQuery(str2, new Object[0]);
/* 75 */     while (recordSet.next()) {
/* 76 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 77 */       hashMap1.put("id", recordSet.getString("id"));
/* 78 */       hashMap1.put("exclude_", Util.null2String(recordSet.getString("exclude_")));
/* 79 */       hashMap1.put("desc_", Util.null2String(recordSet.getString("desc_")));
/* 80 */       arrayList.add(hashMap1);
/*    */     } 
/*    */     
/* 83 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 84 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 85 */     arrayList1.add(new ListHeadBean("exclude_", "", 1, BoolAttr.TRUE));
/* 86 */     arrayList1.add(new ListHeadBean("desc_", ""));
/*    */     
/* 88 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 89 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 90 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 91 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WhiteListBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */