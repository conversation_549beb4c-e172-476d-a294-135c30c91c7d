/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionGroup;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.cloudstore.eccom.result.WeaResultMsg;
/*    */ import com.engine.systeminfo.constant.AppManageConstant;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class AppPageBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 28 */     String str1 = "t1.id , t1.name , t1.title , t1.description";
/* 29 */     String str2 = " ecology_biz_app_page t1";
/* 30 */     String str3 = " status='" + AppManageConstant.YES_STATUS + "'";
/* 31 */     String str4 = "t1.id";
/* 32 */     String str5 = "t1.id";
/* 33 */     String str6 = "asc";
/*    */     
/* 35 */     String str7 = Util.null2String(paramMap.get("id"));
/* 36 */     String str8 = Util.null2String(paramMap.get("name"));
/* 37 */     String str9 = Util.null2String(paramMap.get("title"));
/* 38 */     String str10 = Util.null2String(paramMap.get("description"));
/* 39 */     if (StringUtils.isNotBlank(str7)) {
/* 40 */       str3 = str3 + " and t1.id like '%" + str7 + "%'";
/*    */     }
/* 42 */     if (StringUtils.isNotBlank(str8)) {
/* 43 */       str3 = str3 + " and t1.name like '%" + str8 + "%'";
/*    */     }
/* 45 */     if (StringUtils.isNotBlank(str9)) {
/* 46 */       str3 = str3 + " and t1.title like '%" + str9 + "%'";
/*    */     }
/* 48 */     if (StringUtils.isNotBlank(str10)) {
/* 49 */       str3 = str3 + " and t1.description like '%" + str10 + "%'";
/*    */     }
/* 51 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 52 */     arrayList.add(new SplitTableColBean("20%", "ID", "id", "id"));
/* 53 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(33439, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 54 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(344, this.user.getLanguage()), "title", "title"));
/* 55 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*    */     
/* 57 */     SplitTableBean splitTableBean = new SplitTableBean(str1, str2, str3, str4, str5, str6, arrayList);
/* 58 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 59 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 64 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/* 65 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 66 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/* 67 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*    */ 
/*    */     
/* 70 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT, 32011, "id");
/* 71 */     arrayList1.add(searchConditionItem1);
/*    */ 
/*    */     
/* 74 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.INPUT, 33439, "name", true);
/* 75 */     arrayList1.add(searchConditionItem2);
/*    */ 
/*    */     
/* 78 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.INPUT, 344, "title");
/* 79 */     arrayList1.add(searchConditionItem3);
/*    */ 
/*    */     
/* 82 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.INPUT, 433, "description");
/* 83 */     arrayList1.add(searchConditionItem4);
/*    */     
/* 85 */     arrayList.add(new SearchConditionGroup("", true, arrayList1));
/* 86 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 87 */     return weaResultMsg.getResultMapAll();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AppPageBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */