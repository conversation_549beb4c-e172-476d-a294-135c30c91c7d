/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.formmode.virtualform.VirtualFormHandler;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModePageExpandBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 29 */     String str1 = Util.null2String(paramMap.get("expendname"));
/* 30 */     int i = Util.getIntValue(Util.null2String(paramMap.get("customsearchid")), 0);
/*    */     
/* 32 */     int j = 0;
/* 33 */     int k = 0;
/* 34 */     if (i > 0) {
/* 35 */       RecordSet recordSet1 = new RecordSet();
/* 36 */       String str = "select modeid,formid from mode_customsearch where id = " + i;
/* 37 */       recordSet1.executeSql(str);
/* 38 */       while (recordSet1.next()) {
/* 39 */         j = Util.getIntValue(recordSet1.getString("modeid"), 0);
/* 40 */         k = Util.getIntValue(recordSet1.getString("formid"), 0);
/*    */       } 
/*    */     } 
/*    */     
/* 44 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 46 */     String str2 = "a.id,a.modeid,a.expendname,a.expendname as name,a.showtype,a.hrefid,a.hreftype,a.hreftarget,a.opentype,a.isshow,a.showorder,a.isbatch,a.issystem,a.issystemflag,b.modename ";
/* 47 */     String str3 = "from mode_pageexpand a,modeinfo b ";
/* 48 */     String str4 = " where a.modeid = b.id ";
/* 49 */     str4 = str4 + " and a.modeid = '" + j + "'";
/* 50 */     str4 = str4 + " and a.isbatch = 0 and (a.issystemflag not in (1,2,10,17,6,8,13) or a.issystemflag is null)";
/* 51 */     str4 = str4 + " and (a.issystem = 1 or (a.issystem = 0 and a.viewpage = 1))";
/*    */     
/* 53 */     if (!str1.equals("")) {
/* 54 */       str4 = str4 + " and a.expendname like '%" + str1 + "%' ";
/*    */     }
/* 56 */     if (VirtualFormHandler.isVirtualForm(k)) {
/* 57 */       str4 = str4 + " and (a.issystemflag not in (3,4,5,9,100,8)  or a.issystemflag is null)";
/*    */     }
/* 59 */     String str5 = " ";
/*    */ 
/*    */     
/* 62 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 63 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 64 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(30170, this.user.getLanguage()), "expendname", "expendname", "weaver.formmode.interfaces.InterfaceTransmethod.getExpandNameWithoutUrl", "column:id+column:issystem+column:issystemflag+" + this.user.getLanguage()));
/* 65 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(81468, this.user.getLanguage()), "issystem", "issystem", "weaver.formmode.interfaces.InterfaceTransmethod.getExpandType", "column:issystem+" + this.user.getLanguage()));
/* 66 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(81469, this.user.getLanguage()), "isbatch", "isbatch", "weaver.formmode.interfaces.InterfaceTransmethod.getIsBatch", "column:isbatch+" + this.user.getLanguage()));
/* 67 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(30173, this.user.getLanguage()), "opentype", "opentype", "weaver.formmode.interfaces.InterfaceTransmethod.getOpenType", "column:opentype+" + this.user.getLanguage()));
/* 68 */     arrayList.add((new SplitTableColBean("true", "name")).setIsInputCol(BoolAttr.TRUE));
/*    */     
/* 70 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, "a.id", arrayList);
/* 71 */     splitTableBean.setSqlsortway("ASC");
/* 72 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 73 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 79 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 80 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 81 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 82 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 30170, "expendname");
/* 83 */     arrayList.add(searchConditionItem.setIsQuickSearch(true));
/* 84 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 85 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModePageExpandBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */