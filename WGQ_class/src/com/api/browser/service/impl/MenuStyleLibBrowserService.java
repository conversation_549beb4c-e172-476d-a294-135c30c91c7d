/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MenuStyleLibBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = Util.null2String(paramMap.get("menustyletype"));
/* 33 */     String str2 = Util.null2String(paramMap.get("menustylename"));
/*    */     
/* 35 */     String str3 = " styleid,menustylename,menustyledesc ";
/* 36 */     String str4 = " hpMenuStyle ";
/* 37 */     String str5 = " where 1=1 ";
/* 38 */     if (!"".equals(str1)) {
/* 39 */       str5 = str5 + " and menustyletype='" + str1 + "' ";
/*    */     }
/* 41 */     if (!"".equals(str2)) {
/* 42 */       str5 = str5 + " and menustylename like '%" + str2 + "%' ";
/*    */     }
/* 44 */     String str6 = " menustylelastdate,menustylelasttime ";
/*    */     
/* 46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 47 */     arrayList.add(new SplitTableColBean("true", "styleid"));
/* 48 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(22009, this.user.getLanguage()), "menustylename", "menustylename")).setIsInputCol(BoolAttr.TRUE));
/* 49 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "menustyledesc", "menustyledesc"));
/*    */     
/* 51 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str5, str6, "styleid", "desc", arrayList);
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 60 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 61 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 62 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 22009, "menustylename", true));
/*    */     
/* 64 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 65 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MenuStyleLibBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */