/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.cube.biz.SqlHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.service.CommonConstant;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ public class ModeSearchBrowserService
/*     */   extends BrowserService
/*     */ {
/*  28 */   private ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  29 */   private boolean isUseFmManageDetach = this.manageDetachComInfo
/*  30 */     .isUseFmManageDetach();
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  36 */     String str1 = Util.null2String(paramMap.get("customname"));
/*  37 */     String str2 = Util.null2String(paramMap.get("modeid"));
/*  38 */     String str3 = Util.null2String(paramMap.get("comeFrom"));
/*     */ 
/*     */     
/*  41 */     String str4 = CommonConstant.DB_ISNULL_FUN;
/*  42 */     String str5 = " 1=1 ";
/*     */     
/*  44 */     String str6 = "";
/*  45 */     RecordSet recordSet = new RecordSet();
/*  46 */     if ("centerquery".equals(str3)) {
/*  47 */       if (recordSet.getDBType().toLowerCase().contains("postgresql")) {
/*  48 */         str6 = " and " + CommonConstant.DB_ISNULL_FUN + "(secondPassword,'0') <> '1'";
/*     */       } else {
/*  50 */         str6 = " and " + CommonConstant.DB_ISNULL_FUN + "(secondPassword,0) <> 1";
/*     */       } 
/*     */     }
/*     */     
/*  54 */     String str7 = " a.id,a.modeid,b.modename,a.customname,a.customdesc ";
/*  55 */     String str8 = " from (select mcs.id,mcs.modeid,mcs.customname,mcs.customdesc from mode_customsearch mcs left join modeTreeField mtf on mcs.appid=mtf.id where " + str4 + "(mtf.isdelete,0)<>1  " + str6 + " ) a  left join (select id,modename from modeinfo ) b on a.modeid=b.id ";
/*     */     
/*  57 */     if (this.isUseFmManageDetach && !"".equals(str2) && !"0".equals(str2)) {
/*  58 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  59 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEAPP:ALL", 0);
/*  60 */       if (arrayOfInt.length == 0) {
/*  61 */         str5 = str5 + " and 1=2 ";
/*     */       } else {
/*  63 */         str5 = str5 + " and " + SqlHelper.SplitSqlInCondition("c.subCompanyId", arrayOfInt);
/*     */       } 
/*     */     } 
/*     */     
/*  67 */     String str9 = " ";
/*  68 */     if (!str1.equals("")) {
/*  69 */       str5 = str5 + " and a.customname like '%" + str1 + "%'";
/*     */     }
/*  71 */     if (!str2.equals("") && !str2.equals("0")) {
/*  72 */       str5 = str5 + " and a.modeid = '" + str2 + "'";
/*     */     }
/*     */ 
/*     */     
/*  76 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  77 */     arrayList.add((new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id")).setIsPrimarykey(BoolAttr.TRUE));
/*  78 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(20773, this.user.getLanguage()), "customname", "customname")).setIsInputCol(BoolAttr.TRUE));
/*  79 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(19049, this.user.getLanguage()), "modename", "modename"));
/*  80 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(10000226, Util.getIntValue(this.user.getLanguage())), "customdesc", "customdesc"));
/*  81 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str8, str5, str9, "a.id", arrayList);
/*     */     
/*  83 */     splitTableBean.setSqlsortway("DESC");
/*  84 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     
/*  86 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  92 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  93 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  94 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  95 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 20773, "customname").setIsQuickSearch(true));
/*  96 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, "19049", "modeid", "modeInfo", 30177));
/*  97 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  98 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 103 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 104 */     String str1 = paramHttpServletRequest.getParameter("q");
/*     */     
/* 106 */     String str2 = "";
/* 107 */     String str3 = " a.id,a.modeid,(select modename from modeinfo where id=a.modeid ) as modename,a.customname,a.customdesc ";
/* 108 */     String str4 = " from mode_customsearch a,modeinfo b,modeTreeField c ";
/* 109 */     if (this.isUseFmManageDetach) {
/* 110 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 111 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEAPP:ALL", 0);
/* 112 */       if (arrayOfInt.length == 0) {
/* 113 */         str2 = str2 + " and 1=2 ";
/*     */       } else {
/* 115 */         str2 = str2 + " and " + SqlHelper.SplitSqlInCondition("c.subCompanyId", arrayOfInt);
/*     */       } 
/*     */     } 
/* 118 */     String str5 = " ";
/* 119 */     RecordSet recordSet = new RecordSet();
/* 120 */     if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 121 */       str2 = " b.modetype=c.id and nvl(c.isdelete,0)<>1" + str2;
/* 122 */     } else if (recordSet.getDBType().equalsIgnoreCase("sqlserver")) {
/* 123 */       str2 = " b.modetype=c.id and isnull(c.isdelete,0)<>1" + str2;
/* 124 */     } else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 125 */       str2 = " b.modetype=c.id and COALESCE(c.isdelete,0)<>1" + str2;
/*     */     } else {
/* 127 */       str2 = " b.modetype=c.id and IFNULL(c.isdelete,0)<>1" + str2;
/*     */     } 
/* 129 */     if (!str1.equals("")) {
/* 130 */       str2 = str2 + " and a.customname like '%" + str1 + "%'";
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 135 */     String str6 = "select distinct" + str3 + str4 + " where " + str2;
/*     */     
/* 137 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 138 */     recordSet.executeQuery(str6, new Object[0]);
/* 139 */     while (recordSet.next()) {
/* 140 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 141 */       hashMap1.put("id", recordSet.getString("id"));
/* 142 */       hashMap1.put("name", recordSet.getString("customname"));
/* 143 */       hashMap1.put("modeid", recordSet.getString("modeid"));
/* 144 */       hashMap1.put("modename", recordSet.getString("modename"));
/* 145 */       arrayList.add(hashMap1);
/*     */     } 
/* 147 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 148 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeSearchBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */