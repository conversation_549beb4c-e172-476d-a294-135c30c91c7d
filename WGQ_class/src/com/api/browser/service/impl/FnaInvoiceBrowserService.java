/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import com.cloudstore.dev.api.util.Util_MobileData;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.fna.e9.controller.base.FnaInvoiceLedgerController;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaInvoiceBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[  {    \"key\": \"col1\",    \"configs\": [      {        \"key\": \"col1_row1\",        \"configs\": [          {            \"key\": \"invoiceNumber\"          },          {            \"key\": \"invoiceCode\",            \"style\": {              \"float\": \"right\"            }          }        ]      },      {        \"key\": \"col1_row2\",        \"configs\": [          {            \"key\": \"taxIncludedPrice\"          },          {            \"key\": \"invoiceTypeName\",            \"style\": {              \"float\": \"right\"            }          }        ]      }      {        \"key\": \"col1_row3\",        \"configs\": [          {            \"key\": \"billingDate\"          },        ]      }      {        \"key\": \"col1_row4\",        \"configs\": [          {            \"key\": \"seller\"          },        ]      }    ]  }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  98 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  99 */     if (this.user == null) {
/* 100 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/* 101 */       return (Map)hashMap;
/*     */     } 
/* 103 */     String str1 = Util.null2String(paramMap.get("createdatestart")).trim();
/* 104 */     String str2 = Util.null2String(paramMap.get("createdateend")).trim();
/* 105 */     String str3 = Util.null2String(paramMap.get("invoiceNumber"));
/* 106 */     String str4 = Util.null2String(paramMap.get("seller"));
/*     */     
/* 108 */     FnaInvoiceLedgerController fnaInvoiceLedgerController = FnaInvoiceLedgerController.getInstance();
/*     */     
/* 110 */     String str5 = " a.*," + fnaInvoiceLedgerController.getCaseWhenSql4InvoiceTypeList("invoiceTypeName", "a.invoiceType", this.user.getLanguage()) + " ";
/* 111 */     String str6 = " from FnaInvoiceLedger a ";
/*     */     
/* 113 */     StringBuffer stringBuffer = new StringBuffer(" where 1=1 and  (checkStatus = 1  or checkStatus = 2) ");
/* 114 */     if (!"".equals(str2)) {
/* 115 */       stringBuffer.append(" and a.billingDate <= '").append(StringEscapeUtils.escapeSql(str2)).append("' ");
/*     */     }
/* 117 */     if (!"".equals(str1)) {
/* 118 */       stringBuffer.append(" and a.billingDate >= '").append(StringEscapeUtils.escapeSql(str1)).append("' ");
/*     */     }
/* 120 */     if (!"".equals(str3)) {
/* 121 */       stringBuffer.append(" and a.invoiceNumber like '%").append(StringEscapeUtils.escapeSql(str3.trim())).append("%'");
/*     */     }
/* 123 */     if (!"".equals(str4)) {
/* 124 */       stringBuffer.append(" and a.seller like '%").append(StringEscapeUtils.escapeSql(str4.trim())).append("%'");
/*     */     }
/* 126 */     stringBuffer.append(" and (a.userid_new = ").append(this.user.getUID()).append(" or a.id in ( select invoiceId from fnaInvoiceSharer where sharer = ").append(this.user.getUID()).append(") ").append(") ");
/* 127 */     stringBuffer.append(" and a.status = '0' ");
/* 128 */     String str7 = " a.id  ";
/*     */ 
/*     */     
/* 131 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 132 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 133 */     arrayList.add((new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(900, this.user.getLanguage()), "invoiceNumber", "invoiceNumber")).setIsInputCol(BoolAttr.TRUE));
/* 134 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(131489, this.user.getLanguage()), "invoiceTypeName", "invoiceTypeName"));
/* 135 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(17213, this.user.getLanguage()), "billingDate", "billingDate"));
/* 136 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(131490, this.user.getLanguage()), "seller", "seller"));
/* 137 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(131491, this.user.getLanguage()), "purchaser", "purchaser"));
/* 138 */     arrayList.add(new SplitTableColBean("10%", SystemEnv.getHtmlLabelName(131496, this.user.getLanguage()), "taxIncludedPrice", "taxIncludedPrice"));
/* 139 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, stringBuffer.toString(), str7, "a.id", "DESC", arrayList);
/*     */ 
/*     */     
/* 142 */     List list = Util_MobileData.createList("[  {    \"key\": \"col1\",    \"configs\": [      {        \"key\": \"col1_row1\",        \"configs\": [          {            \"key\": \"invoiceNumber\"          },          {            \"key\": \"invoiceCode\",            \"style\": {              \"float\": \"right\"            }          }        ]      },      {        \"key\": \"col1_row2\",        \"configs\": [          {            \"key\": \"taxIncludedPrice\"          },          {            \"key\": \"invoiceTypeName\",            \"style\": {              \"float\": \"right\"            }          }        ]      }      {        \"key\": \"col1_row3\",        \"configs\": [          {            \"key\": \"billingDate\"          },        ]      }      {        \"key\": \"col1_row4\",        \"configs\": [          {            \"key\": \"seller\"          },        ]      }    ]  }]");
/* 143 */     SplitMobileTemplateBean splitMobileTemplateBean = Util_MobileData.createJsonTemplateBean("theme_default", list);
/* 144 */     splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/*     */     
/* 146 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 147 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 157 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 158 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 159 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 167 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 900, "invoiceNumber", true));
/* 168 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 131490, "seller", false));
/* 169 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 170 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaInvoiceBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */