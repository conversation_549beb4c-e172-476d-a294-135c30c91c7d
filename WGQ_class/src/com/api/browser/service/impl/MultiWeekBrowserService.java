/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.meeting.MeetingBrowser;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class MultiWeekBrowserService
/*     */   extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  27 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  28 */     Map map = MeetingBrowser.getWeekMap();
/*  29 */     Iterator<String> iterator = map.keySet().iterator();
/*  30 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  31 */     List list = null;
/*  32 */     if (!"".equals(str)) {
/*  33 */       list = Util.splitString2List(str, ",");
/*     */     }
/*  35 */     while (iterator.hasNext()) {
/*  36 */       String str1 = iterator.next();
/*  37 */       if (list != null && list.size() > 0 && !list.contains(str1)) {
/*     */         continue;
/*     */       }
/*  40 */       String str2 = MeetingBrowser.getWeekName(Util.getIntValue(str1), this.user.getLanguage());
/*  41 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  42 */       arrayList.add(hashMap1);
/*  43 */       hashMap1.put("id", str1);
/*  44 */       hashMap1.put("name", str2);
/*  45 */       hashMap1.put(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, str1);
/*     */     } 
/*     */     
/*  48 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  49 */     arrayList1.add(new ListHeadBean(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, BoolAttr.TRUE));
/*  50 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/*  51 */     ListHeadBean listHeadBean = (new ListHeadBean("name", SystemEnv.getHtmlLabelName(18518, this.user.getLanguage()), 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT);
/*  52 */     listHeadBean.setDisplay(BoolAttr.FALSE);
/*  53 */     arrayList1.add(listHeadBean);
/*  54 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  55 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  56 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */ 
/*     */     
/*  59 */     hashMap.put("mobileshowtype", MobileShowTypeAttr.ListView);
/*  60 */     hashMap.put("mobileshowtemplate", MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/*     */     
/*  62 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/*  71 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/*  72 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.name");
/*  73 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  83 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  84 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  85 */     if ("".equals(str)) return (Map)hashMap; 
/*  86 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  87 */     Map map = MeetingBrowser.getWeekMap();
/*  88 */     Iterator<String> iterator = map.keySet().iterator();
/*  89 */     if (!"".equals(str)) {
/*  90 */       List list = Util.splitString2List(str, ",");
/*  91 */       while (iterator.hasNext()) {
/*  92 */         String str1 = iterator.next();
/*  93 */         if (list != null && list.size() > 0 && !list.contains(str1)) {
/*     */           continue;
/*     */         }
/*  96 */         String str2 = MeetingBrowser.getWeekName(Util.getIntValue(str1), this.user.getLanguage());
/*  97 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  98 */         arrayList.add(hashMap1);
/*  99 */         hashMap1.put("id", str1);
/* 100 */         hashMap1.put("name", str2);
/* 101 */         hashMap1.put(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, str1);
/*     */       } 
/* 103 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*     */       
/* 105 */       ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 106 */       arrayList1.add(new ListHeadBean(BrowserConstant.BROWSER_LIST_CHECKBOX_FIELDNAME, BoolAttr.TRUE));
/* 107 */       arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 108 */       ListHeadBean listHeadBean = (new ListHeadBean("name", SystemEnv.getHtmlLabelName(18518, this.user.getLanguage()), 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT);
/* 109 */       listHeadBean.setDisplay(BoolAttr.FALSE);
/* 110 */       arrayList1.add(listHeadBean);
/* 111 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 112 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     } 
/* 114 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MultiWeekBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */