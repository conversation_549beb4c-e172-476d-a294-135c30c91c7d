/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.company.SubCompanyComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SalaryItemBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = Util.null2String(paramMap.get("sqlWhere")).trim();
/* 33 */     if (str1.length() <= 0) {
/* 34 */       str1 = " where 1=1 ";
/*    */     }
/* 36 */     String str2 = Util.null2String(paramMap.get("isRightBrowser")).trim();
/* 37 */     String str3 = Util.null2String(paramMap.get("itemname")).trim();
/* 38 */     String str4 = Util.null2String(paramMap.get("itemcode")).trim();
/*    */     
/* 40 */     if ("1".equals(str2)) {
/* 41 */       String str8 = Util.null2String(paramMap.get("subcompanyid")).trim();
/* 42 */       int i = Util.getIntValue((String)paramMap.get("applyscope"), 0);
/* 43 */       String str9 = (new SubCompanyComInfo()).getAllSupCompany(str8);
/* 44 */       if (i == 0) {
/* 45 */         str1 = " where itemtype<9 and applyscope=0 ";
/* 46 */       } else if (i == 1) {
/* 47 */         if (str9.endsWith(",")) {
/* 48 */           str9 = str9.substring(0, str9.length() - 1);
/* 49 */           str1 = " where itemtype<9 and applyscope=0 or (applyscope>0 and subcompanyid=" + str8 + ") or (applyscope=2 and (" + Util.getSubINClause(str9, "subcompanyid", "in") + ")) ";
/*    */         } else {
/* 51 */           str1 = " where itemtype<9 and applyscope=0 or (applyscope>0 and subcompanyid=" + str8 + ") ";
/*    */         } 
/* 53 */       } else if (i == 2) {
/* 54 */         if (str9.endsWith(",")) {
/* 55 */           str9 = str9.substring(0, str9.length() - 1);
/* 56 */           str1 = " where itemtype<9 and applyscope=0 or (applyscope=2 and (" + Util.getSubINClause(str9 + "," + str8, "subcompanyid", "in") + ")) ";
/*    */         } else {
/* 58 */           str1 = " where itemtype<9 and applyscope=0 or (applyscope=2 and subcompanyid=" + str8 + ") ";
/*    */         } 
/*    */       } 
/*    */     } 
/*    */     
/* 63 */     String str5 = "id,itemname,itemcode,itemtype";
/* 64 */     String str6 = "HrmSalaryItem";
/* 65 */     String str7 = "id";
/* 66 */     if (str3.length() > 0) {
/* 67 */       str1 = str1 + " and itemname like '%" + str3 + "%' ";
/*    */     }
/* 69 */     if (str4.length() > 0) {
/* 70 */       str1 = str1 + " and itemcode like '%" + str4 + "%' ";
/*    */     }
/*    */     
/* 73 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 74 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 75 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "itemname", "itemname")).setIsInputCol(BoolAttr.TRUE));
/* 76 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(590, this.user.getLanguage()), "itemcode", "itemcode"));
/* 77 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(63, this.user.getLanguage()), "itemtype", "itemtype", "com.engine.hrm.util.HrmTransMethod.getItemTypeName", "" + this.user.getLanguage()));
/*    */     
/* 79 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str1, str7, "id", arrayList);
/* 80 */     splitTableBean.setSqlsortway("ASC");
/* 81 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 82 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 87 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 88 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 89 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 90 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "itemname", true));
/* 91 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 590, "itemcode"));
/* 92 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 93 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SalaryItemBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */