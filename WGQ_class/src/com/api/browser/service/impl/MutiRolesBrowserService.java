/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MutiRolesBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  40 */     String str1 = Util.null2String(paramMap.get("rolesname"));
/*  41 */     String str2 = Util.null2String(paramMap.get("rolesmark"));
/*  42 */     String str3 = Util.null2String(paramMap.get("hrm_id"));
/*  43 */     String str4 = Util.null2String(paramMap.get("canShowSanyuan"));
/*  44 */     String str5 = Util.null2String(paramMap.get("sqlwhere"));
/*  45 */     boolean bool1 = HrmSanyuanAdminBiz.getSanyuanAble();
/*  46 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/*  47 */     boolean bool2 = manageDetachComInfo.isUseHrmManageDetach();
/*  48 */     String str6 = "";
/*  49 */     if (!"".equals(str3) && !"1".equals(str3) && bool2) {
/*  50 */       str6 = str6 + " where exists (select distinct subcompanyid from SysRoleSubcomRight where subcompanyid = a.Subcompanyid and rightlevel>0 and  exists (select roleid from hrmrolemembers where roleid = SysRoleSubcomRight.Roleid and resourceid = " + str3 + "))";
/*     */     } else {
/*  52 */       str6 = str6 + " where 1=1 ";
/*     */     } 
/*     */     
/*  55 */     if (str1.length() > 0) {
/*  56 */       str6 = str6 + " and a.rolesname like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */     }
/*  58 */     if (str2.length() > 0) {
/*  59 */       str6 = str6 + " and a.rolesmark like '%" + Util.fromScreen2(str2, this.user.getLanguage()) + "%' ";
/*     */     }
/*  61 */     if (!bool1 || (!str4.equals("") && str4.equals("false"))) {
/*  62 */       str6 = str6 + " and (a.sanyuanType is null or a.sanyuanType not in (1,2,3)) ";
/*     */     }
/*  64 */     if (str5.length() > 0) {
/*  65 */       str6 = str6 + " and " + str5;
/*     */     }
/*  67 */     String str7 = "a.id,a.rolesname,a.rolesmark";
/*  68 */     String str8 = "HrmRoles a";
/*     */     
/*  70 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  71 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  72 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(15068, this.user.getLanguage()), "rolesmark", "rolesmark", 1)).setIsInputCol(BoolAttr.TRUE));
/*  73 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(25734, this.user.getLanguage()), "rolesname", "rolesname"));
/*     */     
/*  75 */     SplitTableBean splitTableBean = new SplitTableBean(str7, str8, str6, "a.id", "a.id", arrayList);
/*  76 */     splitTableBean.setSqlsortway("ASC");
/*  77 */     splitTableBean.setSqlisdistinct("true");
/*  78 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  79 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  84 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  85 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  86 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  87 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "rolesmark", true));
/*  88 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 85, "rolesname"));
/*  89 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  90 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  95 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  96 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  97 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  98 */     if (this.user == null || "".equals(str)) return (Map)hashMap;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 109 */     RolesComInfo rolesComInfo = new RolesComInfo();
/* 110 */     String[] arrayOfString = Util.splitString(str, ",");
/* 111 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 112 */       String str1 = Util.null2String(arrayOfString[b]);
/* 113 */       if (str1.length() != 0) {
/* 114 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 115 */         hashMap1.put("id", str1);
/* 116 */         hashMap1.put("rolesname", Util.null2String(rolesComInfo.getRolesname(str1)));
/* 117 */         hashMap1.put("rolesmark", Util.null2String(rolesComInfo.getRolesRemark(str1)));
/* 118 */         arrayList.add(hashMap1);
/*     */       } 
/*     */     } 
/* 121 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 122 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 123 */     arrayList1.add(new ListHeadBean("rolesmark", "", 1, BoolAttr.TRUE));
/* 124 */     arrayList1.add(new ListHeadBean("rolesname", ""));
/*     */     
/* 126 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 127 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 128 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 129 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MutiRolesBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */