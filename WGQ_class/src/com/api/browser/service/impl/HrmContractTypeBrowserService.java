/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*    */ 
/*    */ 
/*    */ public class HrmContractTypeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 26 */     String str1 = Util.null2String(paramMap.get("contractTemId"));
/* 27 */     String str2 = Util.null2String(paramMap.get("contractTypeName"));
/* 28 */     String str3 = Util.null2String(paramMap.get("subcompanyId"));
/*    */     
/* 30 */     String str4 = " type.id,type.typename,type.saveurl,type.contracttempletid,type.ishirecontract,type.remindaheaddate,template.templetname,template.templetdocid ";
/* 31 */     String str5 = " from HrmContractType type,HrmContractTemplet template ";
/* 32 */     String str6 = " where 1=1 and type.contracttempletid = template.ID ";
/*    */     
/* 34 */     if (!str1.equals("")) {
/* 35 */       str6 = str6 + " and template.id=" + str1;
/*    */     }
/* 37 */     if (!str2.equals("")) {
/* 38 */       str6 = str6 + " and type.typename like '%" + str2 + "%' ";
/*    */     }
/* 40 */     ManageDetachComInfo manageDetachComInfo = new ManageDetachComInfo();
/* 41 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 42 */     boolean bool = manageDetachComInfo.isUseHrmManageDetach();
/* 43 */     String str7 = manageDetachComInfo.getHrmdftsubcomid();
/* 44 */     if (bool) {
/* 45 */       if (str3.equals("") || str3.equals("-1")) {
/* 46 */         int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "HrmContractTypeAdd:Add");
/* 47 */         String str = "";
/* 48 */         int i = arrayOfInt.length / 1000 + 1;
/* 49 */         for (byte b = 1; b <= i; b++) {
/* 50 */           str = "";
/* 51 */           for (int j = (b - 1) * 1000; arrayOfInt != null && j < b * 1000 && j < arrayOfInt.length; j++) {
/* 52 */             if (str.length() > 0) {
/* 53 */               str = str + ",";
/*    */             }
/* 55 */             str = str + arrayOfInt[j];
/*    */           } 
/* 57 */           if (str.equals("")) {
/* 58 */             str = manageDetachComInfo.getHrmdftsubcomid();
/*    */           }
/* 60 */           if (str.length() > 0) {
/* 61 */             if (b == 1) {
/* 62 */               str6 = str6 + " and (type.subcompanyid in (" + str + ") ";
/*    */             } else {
/* 64 */               str6 = str6 + "  or type.subcompanyid in (" + str + ") ";
/*    */             } 
/* 66 */             if (b == i) {
/* 67 */               str6 = str6 + ")";
/*    */             }
/*    */           } 
/*    */         } 
/*    */       } else {
/* 72 */         str6 = str6 + " and type.subcompanyid = " + str3;
/*    */       } 
/*    */     }
/* 75 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 76 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 77 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(6158, this.user.getLanguage()), "typename", null)).setIsInputCol(BoolAttr.TRUE));
/* 78 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(15786, this.user.getLanguage()), "templetname", null));
/*    */     
/* 80 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, Util.toHtmlForSplitPage(str6), "id", "id", arrayList);
/* 81 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 82 */     return (Map)hashMap;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 87 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 88 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 89 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "contractTypeName", true));
/* 90 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 64, "contractTemId", "304"));
/* 91 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 92 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmContractTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */