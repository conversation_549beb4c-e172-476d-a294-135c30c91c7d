/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.DBUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class EcodeBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  29 */     RecordSet recordSet = new RecordSet();
/*  30 */     String str1 = recordSet.getDBType();
/*  31 */     String str2 = null;
/*  32 */     if ("sqlserver".equals(str1)) {
/*  33 */       str2 = "id,name,flownum,createdate + createtime as cretime,modifydate + modifytime as modtime";
/*     */     } else {
/*  35 */       str2 = "id,name,flownum,concat(concat(createdate,' '),createtime) as cretime,concat(concat(modifydate,' '),modifytime) as modtime ";
/*     */     } 
/*  37 */     String str3 = " cloudstore_codefolder";
/*  38 */     String str4 = "deleted = 0 and parentid = 'root' and status = 'released'";
/*  39 */     String str5 = "modtime";
/*  40 */     String str6 = "id";
/*  41 */     String str7 = "desc";
/*  42 */     String str8 = String.valueOf(this.user.getLanguage());
/*     */     
/*  44 */     String str9 = Util.null2String(paramMap.get("name"));
/*  45 */     if (StringUtils.isNotBlank(str9)) {
/*  46 */       str4 = str4 + " and name like '%" + str9 + "%'";
/*     */     }
/*  48 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  49 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  50 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(388667, this.user.getLanguage()), "name", "", "com.api.browser.service.impl.EcodeBrowserService.flowName", "column:flownum", 1)).setIsInputCol(BoolAttr.TRUE));
/*  51 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()), "modtime", "modtime", 0));
/*  52 */     arrayList.add(new SplitTableColBean("true", "cretime"));
/*  53 */     arrayList.add(new SplitTableColBean("true", "flownum"));
/*  54 */     arrayList.add(new SplitTableColBean("true", "status"));
/*  55 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str4, str5, str6, str7, arrayList);
/*  56 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  57 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  63 */     String str1 = paramHttpServletRequest.getParameter("q");
/*  64 */     RecordSet recordSet = new RecordSet();
/*  65 */     String str2 = recordSet.getDBType();
/*  66 */     String str3 = null;
/*  67 */     boolean bool = "sqlserver".equals(str2);
/*  68 */     if (StringUtils.isNotBlank(str1)) {
/*  69 */       if (bool) {
/*  70 */         str3 = "select id,name,flownum,createdate + createtime as cretime,modifydate + modifytime as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' and name like '%" + str1 + "%' order by modifydate desc,modifytime desc,id desc";
/*     */       } else {
/*  72 */         str3 = "select id,name,flownum,concat(concat(createdate,' '),createtime) as cretime,concat(concat(modifydate,' '),modifytime) as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' and name like '%" + str1 + "%' order by modifydate desc,modifytime desc,id desc";
/*     */       } 
/*  74 */       recordSet.executeQuery(str3, new Object[0]);
/*     */     } else {
/*  76 */       if (bool) {
/*  77 */         str3 = "select id,name,flownum,createdate + createtime as cretime,modifydate + modifytime as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' order by modifydate desc,modifytime desc,id desc";
/*     */       } else {
/*  79 */         str3 = "select id,name,flownum,concat(concat(createdate,' '),createtime) as cretime,concat(concat(modifydate,' '),modifytime) as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' order by modifydate desc,modifytime desc,id desc";
/*     */       } 
/*  81 */       recordSet.executeQuery(str3, new Object[0]);
/*     */     } 
/*  83 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  84 */     while (recordSet.next()) {
/*  85 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  86 */       hashMap1.put("id", recordSet.getString("id"));
/*  87 */       hashMap1.put("name", flowName(recordSet.getString("name"), String.valueOf(recordSet.getInt("flownum"))));
/*  88 */       hashMap1.put("cretime", recordSet.getString("cretime"));
/*  89 */       hashMap1.put("modtime", recordSet.getString("modtime"));
/*  90 */       arrayList.add(hashMap1);
/*     */     } 
/*  92 */     hashMap.put("datas", arrayList);
/*  93 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  99 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/* 100 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 101 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/* 102 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*     */     
/* 104 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true);
/* 105 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 111 */     arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(15774, Util.getIntValue(this.user.getLanguage())), true, arrayList1));
/* 112 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/* 113 */     return weaResultMsg.getResultMapAll();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 119 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 120 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 121 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 122 */     if (this.user == null || "".equals(str1))
/* 123 */       return (Map)hashMap; 
/* 124 */     ArrayList arrayList1 = new ArrayList();
/* 125 */     Object[] arrayOfObject = DBUtil.transListIn(str1, arrayList1);
/* 126 */     String str2 = Util.null2String(paramMap.get("name"));
/*     */     
/* 128 */     RecordSet recordSet = new RecordSet();
/* 129 */     String str3 = recordSet.getDBType();
/* 130 */     String str4 = null;
/* 131 */     boolean bool = "sqlserver".equals(str3);
/* 132 */     if (StringUtils.isNotBlank(str2)) {
/* 133 */       if (bool) {
/* 134 */         str4 = "select id,name,flownum,createdate + createtime as cretime,modifydate + modifytime as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' and id in (" + arrayOfObject[0] + ") and name like '%" + str2 + "%' order by modifydate desc,modifytime desc,id desc";
/*     */       } else {
/* 136 */         str4 = "select id,name,flownum,concat(concat(createdate,' '),createtime) as cretime,concat(concat(modifydate,' '),modifytime) as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' and id in (" + arrayOfObject[0] + ") and name like '%" + str2 + "%' order by modifydate desc,modifytime desc,id desc";
/*     */       } 
/* 138 */       recordSet.executeQuery(str4, new Object[] { arrayList1 });
/*     */     } else {
/* 140 */       if (bool) {
/* 141 */         str4 = "select id,name,flownum,createdate + createtime as cretime,modifydate + modifytime as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' and id in (" + arrayOfObject[0] + ") order by modifydate desc,modifytime desc,id desc";
/*     */       } else {
/* 143 */         str4 = "select id,name,flownum,concat(concat(createdate,' '),createtime) as cretime,concat(concat(modifydate,' '),modifytime) as modtime from cloudstore_codefolder where deleted = 0 and parentid = 'root' and status = 'released' and id in (" + arrayOfObject[0] + ") order by modifydate desc,modifytime desc,id desc";
/*     */       } 
/* 145 */       recordSet.executeQuery(str4, new Object[] { arrayList1 });
/*     */     } 
/*     */     
/* 148 */     while (recordSet.next()) {
/* 149 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 150 */       hashMap1.put("id", recordSet.getString("id"));
/* 151 */       hashMap1.put("name", flowName(recordSet.getString("name"), String.valueOf(recordSet.getInt("flownum"))));
/* 152 */       hashMap1.put("cretime", recordSet.getString("cretime"));
/* 153 */       hashMap1.put("modtime", recordSet.getString("modtime"));
/* 154 */       arrayList.add(hashMap1);
/*     */     } 
/* 156 */     ArrayList<SplitTableColBean> arrayList2 = new ArrayList();
/* 157 */     arrayList2.add(new SplitTableColBean("true", "id"));
/* 158 */     arrayList2.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(388667, this.user.getLanguage()), "name", "", "com.api.browser.service.impl.EcodeBrowserService.flowName", "column:flownum", 1)).setIsInputCol(BoolAttr.TRUE));
/* 159 */     arrayList2.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(33522, this.user.getLanguage()), "modtime", "modtime", 0));
/* 160 */     arrayList2.add(new SplitTableColBean("true", "cretime"));
/* 161 */     arrayList2.add(new SplitTableColBean("true", "flownum"));
/* 162 */     arrayList2.add(new SplitTableColBean("true", "status"));
/*     */     
/* 164 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList2);
/* 165 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 166 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 168 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public static String flowName(String paramString1, String paramString2) {
/* 172 */     int i = Util.getIntValue(paramString2, 0);
/* 173 */     if (i <= 0) {
/* 174 */       return paramString1;
/*     */     }
/* 176 */     String[] arrayOfString = paramString1.split("\\.");
/* 177 */     if (arrayOfString.length > 1) {
/* 178 */       StringBuilder stringBuilder = new StringBuilder();
/* 179 */       stringBuilder.append(arrayOfString[0]).append(i).append(".").append(arrayOfString[1]);
/* 180 */       paramString1 = stringBuilder.toString();
/*     */     } else {
/* 182 */       paramString1 = paramString1 + i;
/*     */     } 
/* 184 */     return paramString1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/EcodeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */