/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.formmode.page.util.Util;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SimpleBudgetBrowserService
/*     */   extends BrowserService
/*     */ {
/*  33 */   public static int MONTH_TOTAL_BUDGET = 501395;
/*     */ 
/*     */   
/*  36 */   public static int MONTH_AVAILABLE_BUDGET = 501396;
/*     */ 
/*     */   
/*  39 */   public static int QUARTER_TOTAL_BUDGET = 501397;
/*     */ 
/*     */   
/*  42 */   public static int QUARTER_AVAILABLE_BUDGET = 501398;
/*     */ 
/*     */   
/*  45 */   public static int YEAR_TOTAL_BUDGET = 501399;
/*     */ 
/*     */   
/*  48 */   public static int YEAR_AVAILABLE_BUDGET = 501400;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  54 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  56 */     if (this.user == null) {
/*  57 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  58 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  61 */     int[] arrayOfInt = { YEAR_TOTAL_BUDGET, YEAR_AVAILABLE_BUDGET };
/*     */     
/*  63 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  64 */     for (byte b = 0; b < arrayOfInt.length; b++) {
/*  65 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  66 */       hashMap1.put("id", String.valueOf(arrayOfInt[b]));
/*  67 */       hashMap1.put("name", SystemEnv.getHtmlLabelName(arrayOfInt[b], this.user.getLanguage()));
/*  68 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/*  71 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  72 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  73 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(33045, this.user.getLanguage()), 1));
/*     */     
/*  75 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/*  76 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  77 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/*  79 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  90 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  92 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*     */     
/*  94 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  96 */     String[] arrayOfString = str.split(",");
/*  97 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  98 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  99 */       hashMap1.put("id", String.valueOf(Integer.parseInt(arrayOfString[b])));
/* 100 */       hashMap1.put("name", SystemEnv.getHtmlLabelName(Integer.parseInt(arrayOfString[b]), this.user.getLanguage()));
/* 101 */       arrayList.add(hashMap1);
/*     */     } 
/*     */ 
/*     */     
/* 105 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 106 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 107 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/*     */ 
/*     */     
/* 110 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 111 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 112 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 114 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SimpleBudgetBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */