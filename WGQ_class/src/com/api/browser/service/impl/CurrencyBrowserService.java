/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileTemplateBean;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CurrencyBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public static final String JSON_CONFIG = "[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"currencyname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"currencydesc\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]";
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  59 */     String str1 = Util.null2String(paramMap.get("currencyname")).trim();
/*  60 */     String str2 = Util.null2String(paramMap.get("currencydesc")).trim();
/*     */     
/*  62 */     String str3 = " where activable = '1'  ";
/*  63 */     if (!str1.equals("")) {
/*  64 */       str3 = str3 + " and currencyname like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/*  66 */     if (!str2.equals("")) {
/*  67 */       str3 = str3 + " and currencydesc like '%" + StringEscapeUtils.escapeSql(str2) + "%' ";
/*     */     }
/*     */     
/*  70 */     String str4 = " id,currencyname,currencydesc ";
/*  71 */     String str5 = " FnaCurrency  ";
/*  72 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  73 */     arrayList.add(new SplitTableColBean("hide", "id"));
/*  74 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "currencyname", "currencyname")).setIsInputCol(BoolAttr.TRUE));
/*  75 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "currencydesc", "currencydesc"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  85 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, "id", "id", "asc", arrayList);
/*  86 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/*  88 */       SplitMobileTemplateBean splitMobileTemplateBean = new SplitMobileTemplateBean();
/*  89 */       splitMobileTemplateBean.addJsonTemplate("json", JSON.parseArray("[    {        \"configs\": [            {                \"configs\": [                    {                        \"key\": \"currencyname\"                    }                ],                \"key\": \"col1_row1\"            },            {                \"configs\": [                    {                        \"key\": \"currencydesc\"                    },                ],                \"key\": \"col1_row2\"            }        ],        \"key\": \"col1\"    }]", SplitMobileDataBean.class));
/*  90 */       splitTableBean.createMobileTemplate(splitMobileTemplateBean);
/*  91 */     } catch (Exception exception) {
/*  92 */       exception.printStackTrace();
/*     */     } 
/*  94 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  95 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 100 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 101 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 102 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 103 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "currencyname", true));
/* 104 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "currencydesc"));
/* 105 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 106 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CurrencyBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */