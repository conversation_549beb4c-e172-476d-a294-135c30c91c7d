/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.page.PageCominfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AppSelectTreeService
/*    */   extends BrowserService
/*    */ {
/* 26 */   private PageCominfo pc = new PageCominfo();
/* 27 */   String menutype = "";
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = (Util.null2String((String)paramMap.get("id")) == "") ? "0" : Util.null2String((String)paramMap.get("id"));
/* 33 */     String str2 = (Util.null2String((String)paramMap.get("level")) == "") ? "0" : Util.null2String((String)paramMap.get("level"));
/*    */     
/* 35 */     String str3 = "select * from ECOLOGY_BIZ_APP_PAGE";
/* 36 */     String str4 = "select * from ECOLOGY_BIZ_APP_GROUP where pageid=?";
/* 37 */     String str5 = "select a.*,b.APPNAME as name from ECOLOGY_BIZ_APP_GROUP_DETAIL a  left join ECOLOGY_BIZ_APP b on a.APP_ID=b.ID where MODULE_GROUP=?";
/*    */ 
/*    */     
/* 40 */     boolean bool = false;
/* 41 */     RecordSet recordSet = new RecordSet();
/* 42 */     if (str2.equals("0"))
/*    */     {
/* 44 */       recordSet.executeQuery(str3, new Object[0]);
/*    */     }
/*    */     
/* 47 */     if (str2.equals("1"))
/*    */     {
/* 49 */       recordSet.executeQuery(str4, new Object[] { str1 });
/*    */     }
/*    */     
/* 52 */     if (str2.equals("2")) {
/*    */       
/* 54 */       recordSet.executeQuery(str5, new Object[] { str1 });
/* 55 */       bool = true;
/*    */     } 
/*    */     
/* 58 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*    */     
/* 60 */     while (recordSet.next()) {
/* 61 */       String str6 = recordSet.getString("id");
/* 62 */       String str7 = recordSet.getString("name");
/*    */       
/* 64 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*    */       
/* 66 */       hashMap1.put("key", str6);
/* 67 */       hashMap1.put("id", str6);
/* 68 */       hashMap1.put("name", str7);
/* 69 */       hashMap1.put("isLeaf", Boolean.valueOf(bool));
/*    */       
/* 71 */       arrayList.add(hashMap1);
/* 72 */       hashMap.put("datas", arrayList);
/* 73 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*    */     } 
/*    */     
/* 76 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 87 */     return (Map)new HashMap<>();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/AppSelectTreeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */