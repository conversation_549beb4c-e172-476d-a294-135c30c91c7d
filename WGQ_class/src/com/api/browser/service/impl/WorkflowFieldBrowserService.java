/*      */ package com.api.browser.service.impl;
/*      */ import com.api.browser.bean.ListHeadBean;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SearchConditionOption;
/*      */ import com.api.browser.bean.SplitTableBean;
/*      */ import com.api.browser.bean.SplitTableColBean;
/*      */ import com.api.browser.util.BoolAttr;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.api.browser.util.SqlUtils;
/*      */ import com.api.formmode.cache.ModeComInfo;
/*      */ import com.engine.workflow.biz.DetailOrderBiz;
/*      */ import com.engine.workflow.entity.SystemFieldInfoEntity;
/*      */ import com.engine.workflow.util.LinkAgeViewAttrUtil;
/*      */ import com.engine.workflow.util.SystemFieldUtil;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.LabelUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ public class WorkflowFieldBrowserService extends BrowserService {
/*      */   public static String getQueryFormFieldSQL(int paramInt1, int paramInt2) {
/*   31 */     return getQueryFormFieldSQL(paramInt1, paramInt2, null);
/*      */   }
/*      */   
/*      */   public static String getQueryFormFieldSQL(int paramInt1, int paramInt2, User paramUser) {
/*   35 */     StringBuffer stringBuffer = new StringBuffer();
/*   36 */     if (paramInt2 == 0) {
/*   37 */       stringBuffer.append(" select workflow_formfield.fieldid        as id,                                              \n");
/*   38 */       stringBuffer.append("         fieldname                        as name,                                            \n");
/*   39 */       stringBuffer.append("         workflow_fieldlable.fieldlable   as label,                                           \n");
/*   40 */       stringBuffer.append("         workflow_formfield.fieldorder    as dsporder,                                        \n");
/*   41 */       stringBuffer.append("         workflow_formdict.fielddbtype    as dbtype,                                          \n");
/*   42 */       stringBuffer.append("         workflow_formdict.fieldhtmltype  as httype,                                          \n");
/*   43 */       stringBuffer.append("         workflow_formdict.type           as type,                                            \n");
/*   44 */       stringBuffer.append("         '0'                              as viewtype,                                        \n");
/*   45 */       stringBuffer.append("         ''                               as detailtable,                                     \n");
/*   46 */       stringBuffer.append("         workflow_formfield.groupid       as groupid,                                         \n");
/*   47 */       stringBuffer.append(" 0 as tableOrder, \n");
/*   48 */       stringBuffer.append("         'workflow_form'                  as maintablename                                    \n");
/*   49 */       stringBuffer.append("    from workflow_formfield                                                                   \n");
/*   50 */       stringBuffer.append("    left join workflow_formdict on workflow_formdict.id = workflow_formfield.fieldid          \n");
/*   51 */       stringBuffer.append("    left join workflow_fieldlable on workflow_fieldlable.formid = workflow_formfield.formid   \n");
/*   52 */       stringBuffer.append("    and workflow_fieldlable.fieldid = workflow_formfield.fieldid                              \n");
/*   53 */       if (paramUser != null) {
/*   54 */         stringBuffer.append("     and workflow_fieldlable.langurageid = " + paramUser.getLanguage() + "                         \n");
/*      */       } else {
/*   56 */         stringBuffer.append("     and workflow_fieldlable.isdefault = 1                                                \n");
/*      */       } 
/*   58 */       stringBuffer.append("     where workflow_formfield.formid = " + paramInt1 + "                                         \n");
/*   59 */       stringBuffer.append("     and (workflow_formfield.isdetail != '1' or workflow_formfield.isdetail is null)          \n");
/*   60 */       stringBuffer.append("  union                                                                                       \n");
/*   61 */       stringBuffer.append("  select workflow_formfield.fieldid               as id,                                      \n");
/*   62 */       stringBuffer.append("         fieldname                                as name,                                    \n");
/*   63 */       stringBuffer.append("         workflow_fieldlable.fieldlable           as label,                                   \n");
/*   64 */       stringBuffer.append("         workflow_formfield.fieldorder + 100      as dsporder,                                \n");
/*   65 */       stringBuffer.append("         workflow_formdictdetail.fielddbtype      as dbtype,                                  \n");
/*   66 */       stringBuffer.append("         workflow_formdictdetail.fieldhtmltype    as httype,                                  \n");
/*   67 */       stringBuffer.append(" ( case when workflow_formdictdetail.fieldhtmltype = '5' and workflow_formdictdetail.type = '0' then 1 else workflow_formdictdetail.type end ) as type,                                    \n");
/*   68 */       stringBuffer.append("         '1'                                      as viewtype,                                \n");
/*   69 */       stringBuffer.append("         'workflow_formdetail'                    as detailtable,                             \n");
/*   70 */       stringBuffer.append("         workflow_formfield.groupid               as groupid,                                 \n");
/*   71 */       stringBuffer.append("( case when workflow_formfield.groupid is null then 0 else (workflow_formfield.groupid + 1) end )AS tableOrder,     \n");
/*   72 */       stringBuffer.append("         ''                                       as maintablename                            \n");
/*   73 */       stringBuffer.append("    from workflow_formfield                                                                   \n");
/*   74 */       stringBuffer.append("    left join workflow_formdictdetail on workflow_formdictdetail.id = workflow_formfield.fieldid                    \n");
/*   75 */       stringBuffer.append("    left join workflow_fieldlable on  workflow_fieldlable.formid = workflow_formfield.formid                        \n");
/*   76 */       if (paramUser != null) {
/*   77 */         stringBuffer.append("     and workflow_fieldlable.langurageid = " + paramUser.getLanguage() + "                          \n");
/*      */       } else {
/*   79 */         stringBuffer.append("     and workflow_fieldlable.isdefault = 1                                                \n");
/*      */       } 
/*   81 */       stringBuffer.append("     and workflow_fieldlable.fieldid = workflow_formfield.fieldid                             \n");
/*   82 */       stringBuffer.append("     where workflow_formfield.formid =" + paramInt1 + "                                            \n");
/*   83 */       stringBuffer.append("     and (workflow_formfield.isdetail = '1' or workflow_formfield.isdetail is not null)       \n");
/*   84 */     } else if (paramInt2 == 1) {
/*   85 */       stringBuffer.append("    select wfbf.id            as id,                                                      \n");
/*   86 */       stringBuffer.append("           wfbf.fieldname     as name,                                                    \n");
/*   87 */       stringBuffer.append("           wfbf.fieldlabel    as label,                                                   \n");
/*   88 */       stringBuffer.append("           wfbf.fielddbtype   as dbtype,                                                  \n");
/*   89 */       stringBuffer.append("           wfbf.fieldhtmltype as httype,                                                  \n");
/*   90 */       stringBuffer.append(" ( case when wfbf.fieldhtmltype = '5' and wfbf.type = '0' then 1 else wfbf.type end )  as type,  \n");
/*   91 */       stringBuffer.append("           wfbf.dsporder      as dsporder,                                                \n");
/*   92 */       stringBuffer.append("           wfbf.viewtype      as viewtype,                                                \n");
/*   93 */       stringBuffer.append("           wfbf.detailtable   as detailtable,                                             \n");
/*   94 */       stringBuffer.append("           wfb.tablename      as maintablename,                                           \n");
/*   95 */       stringBuffer.append("          (case when wbd.orderid is null then 0 else wbd.orderid end) as tableOrder       \n");
/*   96 */       stringBuffer.append("      from workflow_billfield wfbf                                                        \n");
/*   97 */       stringBuffer.append("      left join workflow_bill wfb on wfbf.billid  = wfb.id                                \n");
/*   98 */       stringBuffer.append("      left join workflow_billdetailtable wbd on wfbf.detailtable = wbd.tablename          \n");
/*   99 */       stringBuffer.append("     where wfbf.billid = " + paramInt1 + "                                                   \n");
/*      */     } 
/*  101 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  106 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  107 */     String str1 = Util.null2String(paramMap.get("workflowId"));
/*  108 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isBill")), -1);
/*  109 */     int j = Util.getIntValue(Util.null2String(paramMap.get("formId")), -1);
/*  110 */     String str2 = Util.null2String(paramMap.get("htType"));
/*  111 */     String str3 = Util.null2String(paramMap.get("type"));
/*  112 */     String str4 = Util.null2String(paramMap.get("needFieldTypes"));
/*  113 */     String str5 = Util.null2String(paramMap.get("isDetail"));
/*  114 */     String str6 = Util.null2String(paramMap.get("tableIndex"));
/*  115 */     String str7 = Util.null2String(paramMap.get("fieldName"));
/*  116 */     String str8 = Util.null2String(paramMap.get("systemFieldType"));
/*  117 */     int k = Util.getIntValue(Util.null2String(paramMap.get("tableType")), 0);
/*  118 */     String str9 = Util.null2String(paramMap.get("tableNum"));
/*  119 */     String str10 = Util.null2String(paramMap.get("detailTableName"));
/*  120 */     String str11 = Util.null2String(paramMap.get("tableNumSearch"));
/*  121 */     String str12 = Util.null2String(paramMap.get("showFieldname"));
/*  122 */     String str13 = Util.null2String(paramMap.get("requestType"));
/*  123 */     String str14 = Util.null2String(paramMap.get("isNeedTrans"));
/*  124 */     String str15 = Util.null2String(paramMap.get("isNeedType"));
/*  125 */     String str16 = Util.null2String(paramMap.get("isFromRuleDesign"));
/*  126 */     String str17 = Util.null2String(paramMap.get("isConformRulecheck"));
/*  127 */     String str18 = Util.null2String(paramMap.get("isFormDocCompare"));
/*  128 */     String str19 = Util.null2String(paramMap.get("noNeedField"));
/*  129 */     String str20 = Util.null2String(paramMap.get("noNeedLocationField"));
/*  130 */     int m = Util.getIntValue(Util.null2String(paramMap.get("showDetailId")), 0);
/*  131 */     int n = Util.getIntValue(Util.null2String(paramMap.get("modeId")));
/*  132 */     String str21 = Util.null2o(Util.null2String(paramMap.get("isFromMode")));
/*  133 */     String str22 = Util.null2o(Util.null2String(paramMap.get("isOdoc")));
/*  134 */     if ("1".equals(str22)) {
/*  135 */       str18 = "1";
/*  136 */       str12 = "1";
/*  137 */       str13 = "1";
/*      */     } 
/*  139 */     String str23 = Util.null2o(Util.null2String(paramMap.get("isFromDataSecurity")));
/*  140 */     if (i == 0) {
/*  141 */       fixOldFormFieldData(j);
/*      */     }
/*      */ 
/*      */     
/*  145 */     if (!"".equals(str10)) {
/*  146 */       ArrayList<String> arrayList1 = new ArrayList();
/*  147 */       DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/*  148 */       List<String> list = Arrays.asList(str10.split(","));
/*  149 */       for (String str : list) {
/*  150 */         arrayList1.add(detailOrderBiz.getDetailOrderByTableName(str) + "");
/*      */       }
/*      */       
/*  153 */       if ("0".equals(str9))
/*  154 */         arrayList1.add("0"); 
/*  155 */       str9 = String.join(",", (Iterable)arrayList1);
/*      */     } 
/*      */     
/*  158 */     if (k == 1) {
/*  159 */       str5 = "0";
/*  160 */     } else if (k == 2) {
/*  161 */       str5 = "1";
/*      */     } 
/*      */     
/*  164 */     if ("1".equals(str21)) {
/*  165 */       ModeComInfo modeComInfo = new ModeComInfo();
/*  166 */       i = 1;
/*  167 */       j = Util.getIntValue(modeComInfo.getFormId(n + ""));
/*      */     }
/*  169 */     else if (i == -1 || j == -1) {
/*  170 */       RecordSet recordSet = new RecordSet();
/*  171 */       String str = "SELECT formid , isbill FROM workflow_base WHERE id = ?";
/*  172 */       recordSet.executeQuery(str, new Object[] { str1 });
/*  173 */       if (recordSet.next()) {
/*  174 */         i = recordSet.getInt("isBill");
/*  175 */         j = recordSet.getInt("formId");
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  180 */     String str24 = " where 1 = 1 ";
/*      */ 
/*      */     
/*  183 */     if (!"".equals(str11) && !"all".equals(str11)) {
/*      */ 
/*      */ 
/*      */       
/*  187 */       DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/*  188 */       Map map = detailOrderBiz.getDetailMap(j);
/*  189 */       String str36 = (String)map.get(Integer.valueOf(Integer.parseInt(str11)));
/*      */       
/*  191 */       String str37 = "select orderid from workflow_billdetailtable where tablename = '" + str36 + "'";
/*  192 */       RecordSet recordSet = new RecordSet();
/*  193 */       recordSet.executeSql(str37);
/*  194 */       if (recordSet.next()) {
/*  195 */         str11 = String.valueOf(recordSet.getInt("orderid"));
/*      */       }
/*      */       
/*  198 */       str24 = str24 + " and tableOrder = " + str11;
/*  199 */     } else if ("1".equals(str16) && !"all".equals(str11)) {
/*  200 */       str24 = str24 + " and viewtype = 0 ";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  205 */     String str25 = "";
/*  206 */     String str26 = "";
/*  207 */     if (i == 0) {
/*  208 */       str25 = " * ";
/*  209 */       str26 = " ( select id,name,label,label as showname, 0 as labelindex, dbtype, httype,type,dsporder,viewtype, groupid as tableindex,tableOrder, maintablename ";
/*  210 */       if ("overtime".equals(str8))
/*  211 */         str26 = str26 + ",'' as toFieldTip "; 
/*  212 */       str26 = str26 + " from ( " + getQueryFormFieldSQL(j, i, this.user) + " ) a ";
/*      */     } else {
/*  214 */       str25 = " * ";
/*  215 */       str26 = " (select a.id as id,b.labelname as label,b.labelname as showname, a.label as labelindex, name,dbtype,httype,type,dsporder,viewtype, detailtable as tableindex,tableOrder, maintablename,enc.isencrypt as isencrypt ";
/*  216 */       if ("overtime".equals(str8)) {
/*  217 */         str26 = str26 + ",'' as toFieldTip ";
/*      */       }
/*  219 */       str26 = str26 + " from ( " + getQueryFormFieldSQL(j, i, this.user) + " ) a  left join HTMLLABELInfo b on b.indexid = a.label  and b.languageid =   " + this.user.getLanguage() + " left join enc_field_config_info enc on (case when a.viewtype='1' then a.detailtable else a.maintablename end) = enc.tablename and a.name = enc.fieldname ";
/*      */     } 
/*      */ 
/*      */     
/*  223 */     String str27 = Util.null2String(paramMap.get("fromBatchSet"));
/*  224 */     str26 = "1".equals(str27) ? "" : str26;
/*      */     
/*  226 */     if (!"".equals(str8)) {
/*  227 */       List list = SystemFieldUtil.getSystemFields(str8, this.user, paramMap);
/*  228 */       for (SystemFieldInfoEntity systemFieldInfoEntity : list) {
/*  229 */         str26 = str26 + getUnionSql(systemFieldInfoEntity, i, str8);
/*      */       }
/*      */     } 
/*      */     
/*  233 */     if (str26.startsWith(" UNION") && "1".equals(str27)) {
/*  234 */       str26 = str26.replaceFirst("UNION", "(");
/*      */     }
/*      */     
/*  237 */     str26 = str26 + " ) t1 ";
/*      */     
/*  239 */     if ("1".equals(str23)) {
/*  240 */       str24 = str24 + " and (httype = 1  or  httype = 2)  ";
/*      */     }
/*      */     
/*  243 */     if (!"".equals(str2)) {
/*  244 */       str24 = str24 + " and httype in (" + str2 + ") ";
/*      */     }
/*      */     
/*  247 */     if (!"".equals(str3)) {
/*  248 */       str24 = str24 + " and type in (" + str3 + ") ";
/*      */     }
/*      */     
/*  251 */     if (!"".equals(str4)) {
/*  252 */       str24 = str24 + " and ( ";
/*  253 */       String[] arrayOfString = str4.split(",");
/*  254 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  255 */         String str36 = arrayOfString[b].split("_")[0];
/*  256 */         String str37 = arrayOfString[b].split("_")[1];
/*  257 */         if (b != 0) {
/*  258 */           str24 = str24 + " or ";
/*      */         }
/*  260 */         str24 = str24 + " (httype = " + str36 + " and type = " + str37 + ")";
/*      */       } 
/*  262 */       str24 = str24 + " ) ";
/*      */     } 
/*      */     
/*  265 */     if (!"".equals(str5)) {
/*  266 */       if ("1".equals(str5)) {
/*  267 */         str24 = str24 + " and ( viewtype = '1' ";
/*      */       } else {
/*  269 */         str24 = str24 + " and ( viewtype = '0' ";
/*      */       } 
/*  271 */       if (!"".equals(str8)) {
/*  272 */         str24 = str24 + " or viewtype = '-10' ";
/*      */       }
/*  274 */       str24 = str24 + " ) ";
/*      */     } 
/*      */ 
/*      */     
/*  278 */     if (!"".equals(str16) || !"".equals(str17)) {
/*  279 */       str24 = str24 + " and httype!=6 and not(httype=2 and type=2) and httype<>7 and not(httype=3 and type=141) ";
/*  280 */       if (i == 1) {
/*  281 */         str24 = str24 + " and (isencrypt is null or isencrypt = 0) ";
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  286 */     if (!"".equals(str18)) {
/*  287 */       str24 = str24 + " and (httype=6 and type=1 or httype=3 and type=9)  ";
/*      */     }
/*      */ 
/*      */     
/*  291 */     String str28 = Util.null2String(paramMap.get("nodeId"));
/*  292 */     String str29 = Util.null2String(paramMap.get("notInclude"));
/*  293 */     if (!str29.equals("")) {
/*  294 */       str24 = str24 + " and id not in( " + str29 + ") ";
/*      */     }
/*  296 */     if (!str28.equals("")) {
/*  297 */       str24 = str24 + LinkAgeViewAttrUtil.getSqlWhere(paramMap, this.user);
/*      */     }
/*      */ 
/*      */     
/*  301 */     if (!str9.equals("")) {
/*  302 */       ArrayList<String> arrayList1 = Util.TokenizerString(str9, ",");
/*  303 */       str24 = str24 + " and ( 1= 2 ";
/*      */       
/*  305 */       if (!"".equals(str8)) {
/*  306 */         str24 = str24 + " or viewtype = '-10' ";
/*  307 */         if ("wfToWorkplan".equals(str8)) {
/*  308 */           str24 = str24 + " or viewtype = '-2' ";
/*      */         }
/*      */       } 
/*  311 */       if (arrayList1.contains("0")) {
/*  312 */         str24 = str24 + " or viewtype = '0' ";
/*  313 */         arrayList1.remove("0");
/*      */       } 
/*      */       
/*  316 */       if (arrayList1.size() > 0) {
/*  317 */         if (i == 0) {
/*  318 */           String str = "";
/*  319 */           for (Object object : arrayList1) {
/*  320 */             String str36 = object.toString();
/*  321 */             if (Util.getIntValue(object.toString(), 0) > 0) {
/*  322 */               str36 = (Util.getIntValue(object.toString(), 0) - 1) + "";
/*      */             }
/*  324 */             str = str + "," + str36;
/*      */           } 
/*  326 */           if (!"".equals(str)) {
/*  327 */             str24 = str24 + " or tableIndex in( " + str.substring(1) + ") ";
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           }
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  343 */           String str = "";
/*  344 */           for (byte b = 0; b < arrayList1.size(); b++) {
/*  345 */             String str36 = arrayList1.get(b);
/*  346 */             DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/*  347 */             Map map = detailOrderBiz.getDetailMap(j);
/*  348 */             str = str + ",'" + (String)map.get(Integer.valueOf(Integer.parseInt(str36))) + "'";
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  355 */           if (!"".equals(str)) {
/*  356 */             str = str.substring(1);
/*  357 */             str24 = str24 + " or tableIndex in(" + str + ") ";
/*      */           } 
/*      */         } 
/*      */       }
/*  361 */       str24 = str24 + ")";
/*      */     } 
/*      */ 
/*      */     
/*  365 */     if (!"".equals(str6)) {
/*  366 */       if ("main".equals(str6)) {
/*  367 */         if (i == 0) {
/*  368 */           str24 = str24 + " and maintablename = 'workflow_form' ";
/*      */         } else {
/*  370 */           str24 = str24 + " and viewtype = '0' ";
/*      */         } 
/*      */       } else {
/*  373 */         str24 = str24 + " and tableindex = '" + str6 + "' ";
/*      */       } 
/*      */     }
/*      */     
/*  377 */     if (!"".equals(str19)) {
/*  378 */       str24 = str24 + " and id not in (" + str19 + ") ";
/*      */     }
/*      */     
/*  381 */     if ("1".equals(str20)) {
/*  382 */       str24 = str24 + " and (httype <> 9 or type <> 1) ";
/*      */     }
/*      */     
/*  385 */     String str30 = " where 1 = 1 ";
/*  386 */     if (!"".equals(str7)) {
/*  387 */       str30 = str30 + " and label like '%" + str7 + "%' ";
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  394 */     String str31 = " tableOrder,dsporder ";
/*      */     
/*  396 */     str24 = SqlUtils.replaceFirstAnd(str24);
/*      */     
/*  398 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  399 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  400 */     arrayList.add(new SplitTableColBean("true", "tableorder"));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  412 */     if (str15.equals("1")) {
/*  413 */       arrayList.add(new SplitTableColBean("true", "viewtype"));
/*  414 */       arrayList.add(new SplitTableColBean("true", "httype"));
/*  415 */       arrayList.add(new SplitTableColBean("true", "type"));
/*  416 */       arrayList.add(new SplitTableColBean("true", "name"));
/*      */     } 
/*      */     
/*  419 */     String str32 = "50%";
/*  420 */     String str33 = "50%";
/*  421 */     String str34 = SystemEnv.getHtmlLabelName(685, this.user.getLanguage());
/*  422 */     String str35 = SystemEnv.getHtmlLabelName("1".equals(str16) ? 17997 : 26734, this.user.getLanguage());
/*  423 */     if ("1".equals(str12)) {
/*  424 */       str32 = "30%";
/*  425 */       str33 = "40%";
/*  426 */       str34 = SystemEnv.getHtmlLabelName(15456, this.user.getLanguage());
/*  427 */       str35 = SystemEnv.getHtmlLabelName(17997, this.user.getLanguage());
/*  428 */       arrayList.add(new SplitTableColBean("true", "viewtype"));
/*  429 */       arrayList.add(new SplitTableColBean("true", "httype"));
/*  430 */       arrayList.add(new SplitTableColBean("true", "type"));
/*  431 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName("1".equals(str18) ? 124937 : 685, this.user.getLanguage()), "name", "10"));
/*      */     } 
/*      */     
/*  434 */     if (i == 0) {
/*  435 */       String str = getClass().getName() + ".tableTypeTransmethod";
/*  436 */       if (str14.equals("1")) {
/*  437 */         String str36 = getClass().getName() + ".doFieldNameTrans";
/*  438 */         String str37 = "column:viewtype+column:tableindex+" + i + "+" + this.user.getLanguage() + "+" + j + "+column:maintablename";
/*      */         
/*  440 */         SplitTableColBean splitTableColBean1 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "showname", null, str36, str37, 1);
/*  441 */         splitTableColBean1.setIsInputCol(BoolAttr.TRUE);
/*  442 */         splitTableColBean1.setHide("true");
/*  443 */         splitTableColBean1.setTransMethodForce("true");
/*  444 */         splitTableColBean1.setShowType(1);
/*  445 */         arrayList.add(splitTableColBean1);
/*      */         
/*  447 */         arrayList.add(new SplitTableColBean(str32, str34, "label", null, 1));
/*  448 */         arrayList.add(new SplitTableColBean(str33, str35, "tableindex", null, str, "" + this.user.getLanguage() + "+column:maintablename+column:viewtype", 0));
/*      */       } else {
/*  450 */         arrayList.add((new SplitTableColBean(str32, str34, "label", null, 1)).setIsInputCol(BoolAttr.TRUE));
/*  451 */         arrayList.add(new SplitTableColBean(str33, str35, "tableindex", null, str, "" + this.user.getLanguage() + "+column:maintablename+column:viewtype", 0));
/*      */       } 
/*      */     } else {
/*  454 */       String str36 = getClass().getName() + ".detailTableTrans";
/*  455 */       String str37 = getClass().getName() + ".getShowName";
/*  456 */       if (str14.equals("1")) {
/*  457 */         String str38 = getClass().getName() + ".doFieldNameTrans";
/*  458 */         String str39 = "column:viewtype+column:tableindex+" + i + "+" + this.user.getLanguage() + "+" + j + "+column:maintablename+column:labelindex";
/*  459 */         SplitTableColBean splitTableColBean1 = new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "showname", null, str38, str39, 1);
/*  460 */         splitTableColBean1.setIsInputCol(BoolAttr.TRUE);
/*  461 */         splitTableColBean1.setHide("true");
/*  462 */         splitTableColBean1.setTransMethodForce("true");
/*  463 */         splitTableColBean1.setShowType(1);
/*  464 */         arrayList.add(splitTableColBean1);
/*  465 */         arrayList.add(new SplitTableColBean(str32, str34, "label", null, str37, "column:labelindex", 1));
/*  466 */         arrayList.add(new SplitTableColBean(str33, str35, "tableindex", null, str36, "" + this.user.getLanguage() + "+column:viewtype++column:maintablename", 0));
/*      */       } else {
/*  468 */         arrayList.add((new SplitTableColBean(str32, str34, "label", null, str37, "column:labelindex", 1)).setIsInputCol(BoolAttr.TRUE));
/*  469 */         arrayList.add(new SplitTableColBean(str33, str35, "tableindex", null, str36, "" + this.user.getLanguage() + "+column:viewtype++column:maintablename+0+" + str16, 0));
/*      */       } 
/*      */     } 
/*      */     
/*  473 */     if ("1".equals(str13)) {
/*  474 */       arrayList.add(new SplitTableColBean(str33, SystemEnv.getHtmlLabelName(687, this.user.getLanguage()), "httype", null, "com.engine.workflow.biz.FormFieldTransMethod.fieldTypeTransmethod", "+column:type+" + this.user.getLanguage(), 0));
/*      */     }
/*  476 */     if ("1".equals(str18)) {
/*  477 */       arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(128719, this.user.getLanguage()), "dsporder", null));
/*      */     }
/*  479 */     SplitTableColBean splitTableColBean = new SplitTableColBean(str33, "", "dbtype", null, 2);
/*  480 */     splitTableColBean.setHide("true");
/*  481 */     arrayList.add(splitTableColBean);
/*      */     
/*  483 */     if ("overtime".equals(str8))
/*  484 */       arrayList.add(new SplitTableColBean("true", "toFieldTip")); 
/*  485 */     SplitTableBean splitTableBean = new SplitTableBean();
/*  486 */     splitTableBean.setBackfields("*");
/*  487 */     StringBuilder stringBuilder = new StringBuilder();
/*  488 */     if (m == 1 && !"".equals(str9)) {
/*  489 */       ArrayList arrayList1 = Util.TokenizerString(str9, ",");
/*  490 */       for (Object object : arrayList1) {
/*  491 */         if ("0".equals(object))
/*  492 */           continue;  String str = SystemEnv.getHtmlLabelName(515309, this.user.getLanguage());
/*  493 */         stringBuilder.append(" select -1 as id,'")
/*  494 */           .append(str)
/*  495 */           .append("' as label ,'")
/*  496 */           .append(str)
/*  497 */           .append("' as showname, 0 as labelindex, 'id' as name,'int'as dbtype,null as httype,null as type,null as dsporder,1 as viewtype,'_dt")
/*  498 */           .append(object).append("' as tableindex,").append(object).append(" as tableOrder,null as maintablename ");
/*  499 */         if (i == 1) stringBuilder.append(" ,0 as isencrypt "); 
/*  500 */         stringBuilder.append(" from workflow_billfield  union ");
/*      */       } 
/*      */     } 
/*  503 */     splitTableBean.setSqlform("(" + stringBuilder.toString() + " select " + str25 + " from " + str26 + " " + str24 + ") t2 ");
/*  504 */     splitTableBean.setSqlwhere(str30);
/*  505 */     splitTableBean.setSqlprimarykey("id");
/*  506 */     splitTableBean.setCols(arrayList);
/*  507 */     splitTableBean.setSqlorderby(str31);
/*  508 */     splitTableBean.setSqlsortway("ASC");
/*  509 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  510 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  515 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  516 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  518 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("workflowId"));
/*  519 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("isBill")), -1);
/*  520 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("formId")), -1);
/*  521 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("htType"));
/*  522 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("type"));
/*  523 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("needFieldTypes"));
/*  524 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("isDetail"));
/*  525 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("tableIndex"));
/*  526 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("isNeedType"));
/*  527 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("systemFieldType"));
/*  528 */     String str10 = Util.null2String(paramHttpServletRequest.getParameter("tableNum"));
/*  529 */     String str11 = Util.null2String(paramHttpServletRequest.getParameter("detailTableName"));
/*  530 */     String str12 = Util.null2String(paramHttpServletRequest.getParameter("isFormDocCompare"));
/*  531 */     String str13 = Util.null2String(paramHttpServletRequest.getParameter("isFromRuleDesign"));
/*  532 */     String str14 = Util.null2String(paramHttpServletRequest.getParameter("isConformRulecheck"));
/*  533 */     String str15 = Util.null2String(paramHttpServletRequest.getParameter("noNeedField"));
/*  534 */     String str16 = Util.null2String(paramHttpServletRequest.getParameter("noNeedLocationField"));
/*  535 */     String str17 = Util.null2o(Util.null2String(paramHttpServletRequest.getParameter("isFromMode")));
/*  536 */     int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("modeId")));
/*  537 */     String str18 = Util.null2o(Util.null2String(paramHttpServletRequest.getParameter("isFromDataSecurity")));
/*      */     
/*  539 */     if ("1".equals(str17)) {
/*  540 */       if (i == -1 || j == -1) {
/*  541 */         ModeComInfo modeComInfo = new ModeComInfo();
/*  542 */         i = 1;
/*  543 */         j = Util.getIntValue(modeComInfo.getFormId(k + ""));
/*      */       }
/*      */     
/*  546 */     } else if (i == -1 || j == -1) {
/*  547 */       String str = "SELECT formid , isbill FROM workflow_base WHERE id = ?";
/*  548 */       recordSet.executeQuery(str, new Object[] { str2 });
/*  549 */       if (recordSet.next()) {
/*  550 */         i = recordSet.getInt("isBill");
/*  551 */         j = recordSet.getInt("formId");
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  557 */     if (!"".equals(str11)) {
/*  558 */       ArrayList<String> arrayList1 = new ArrayList();
/*  559 */       DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/*  560 */       List<String> list = Arrays.asList(str11.split(","));
/*  561 */       for (String str : list) {
/*  562 */         arrayList1.add(detailOrderBiz.getDetailOrderByTableName(str) + "");
/*      */       }
/*      */       
/*  565 */       if ("0".equals(str10))
/*  566 */         arrayList1.add("0"); 
/*  567 */       str10 = String.join(",", (Iterable)arrayList1);
/*      */     } 
/*  569 */     String str19 = " where 1 = 1 ";
/*      */ 
/*      */ 
/*      */     
/*  573 */     String str20 = "";
/*  574 */     String str21 = "";
/*  575 */     if (i == 0) {
/*  576 */       str20 = " * ";
/*  577 */       str21 = " ( select id,name,label,label as showname,0 as labelindex,dbtype, httype,type,dsporder, viewtype, groupid as tableindex,tableOrder, maintablename ";
/*  578 */       if ("overtime".equals(str9))
/*  579 */         str21 = str21 + ",'' as toFieldTip "; 
/*  580 */       str21 = str21 + " from ( " + getQueryFormFieldSQL(j, i, this.user) + " ) a ";
/*      */     } else {
/*  582 */       str20 = " * ";
/*  583 */       str21 = " (select a.id as id,b.indexdesc as label, b.indexdesc as showname,a.label as labelindex, name,dbtype,httype,type,dsporder,viewtype, detailtable as tableindex,tableOrder, maintablename,enc.isencrypt as isencrypt";
/*  584 */       if ("overtime".equals(str9))
/*  585 */         str21 = str21 + ",'' as toFieldTip "; 
/*  586 */       str21 = str21 + " from ( " + getQueryFormFieldSQL(j, i, this.user) + " ) a left join HTMLLABELINDEX b on a.label = b.id  left join enc_field_config_info enc on (case when a.viewtype='1' then a.detailtable else a.maintablename end) = enc.tablename and a.name = enc.fieldname ";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  591 */     String str22 = Util.null2String(paramHttpServletRequest.getParameter("fromBatchSet"));
/*  592 */     str21 = "1".equals(str22) ? "" : str21;
/*      */     
/*  594 */     if (!"".equals(str9)) {
/*  595 */       List list = SystemFieldUtil.getSystemFields(str9, this.user, ParamUtil.request2Map(paramHttpServletRequest));
/*  596 */       for (SystemFieldInfoEntity systemFieldInfoEntity : list) {
/*  597 */         str21 = str21 + getUnionSql(systemFieldInfoEntity, i, str9);
/*      */       }
/*      */     } 
/*      */     
/*  601 */     if (str21.startsWith(" UNION") && "1".equals(str22)) {
/*  602 */       str21 = str21.replaceFirst("UNION", "(");
/*      */     }
/*      */     
/*  605 */     str21 = str21 + " ) t1 ";
/*      */     
/*  607 */     if ("1".equals(str18)) {
/*  608 */       str19 = str19 + " and (httype = 1  or  httype = 2) ";
/*      */     }
/*      */     
/*  611 */     if (!"".equals(str3)) {
/*  612 */       str19 = str19 + " and httype in (" + str3 + ") ";
/*      */     }
/*      */     
/*  615 */     if (!"".equals(str4)) {
/*  616 */       str19 = str19 + " and type in (" + str4 + ") ";
/*      */     }
/*      */     
/*  619 */     if (!"".equals(str5)) {
/*  620 */       str19 = str19 + " and ( ";
/*  621 */       String[] arrayOfString = str5.split(",");
/*  622 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  623 */         String str29 = arrayOfString[b].split("_")[0];
/*  624 */         String str30 = arrayOfString[b].split("_")[1];
/*  625 */         if (b != 0) {
/*  626 */           str19 = str19 + " or ";
/*      */         }
/*  628 */         str19 = str19 + " (httype = " + str29 + " and type = " + str30 + ")";
/*      */       } 
/*  630 */       str19 = str19 + " ) ";
/*      */     } 
/*      */     
/*  633 */     if (!"".equals(str6)) {
/*  634 */       if ("1".equals(str6)) {
/*  635 */         str19 = str19 + " and ( viewtype = '1' ";
/*      */       } else {
/*  637 */         str19 = str19 + " and ( viewtype = '0' ";
/*      */       } 
/*  639 */       if (!"".equals(str9)) {
/*  640 */         str19 = str19 + " or viewtype = '-10' ";
/*  641 */         if ("wfToWorkplan".equals(str9)) {
/*  642 */           str19 = str19 + " or viewtype = '-2' ";
/*      */         }
/*      */       } 
/*  645 */       str19 = str19 + " ) ";
/*      */     } 
/*      */ 
/*      */     
/*  649 */     if (!"".equals(str13) || !"".equals(str14)) {
/*  650 */       str19 = str19 + " and httype!=6 and not(httype=2 and type=2) and httype<>7 and not(httype=3 and type=141) ";
/*  651 */       if (i == 1) {
/*  652 */         str19 = str19 + " and (isencrypt is null or isencrypt = 0) ";
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  657 */     if (!"".equals(str12)) {
/*  658 */       str19 = str19 + " and (httype=6 and type=1 or httype=3 and type=9)  ";
/*      */     }
/*      */     
/*  661 */     if ("1".equals(str16)) {
/*  662 */       str19 = str19 + " and (httype <> 9 or type <> 1) ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  667 */     String str23 = Util.null2String(paramHttpServletRequest.getParameter("nodeId"));
/*  668 */     String str24 = Util.null2String(paramHttpServletRequest.getParameter("notInclude"));
/*  669 */     if (!str24.equals("")) {
/*  670 */       str19 = str19 + " and id not in( " + str24 + ") ";
/*      */     }
/*  672 */     if (!str23.equals("")) {
/*  673 */       str19 = str19 + LinkAgeViewAttrUtil.getSqlWhere(ParamUtil.request2Map(paramHttpServletRequest), this.user);
/*      */     }
/*      */ 
/*      */     
/*  677 */     if (!str10.equals("")) {
/*  678 */       str19 = str19 + " and tableOrder in( " + str10 + " ) ";
/*      */     }
/*      */     
/*  681 */     if (!"".equals(str7)) {
/*  682 */       if ("main".equals(str7)) {
/*  683 */         if (i == 0) {
/*  684 */           str19 = str19 + " and maintablename = 'workflow_form' ";
/*      */         } else {
/*  686 */           str19 = str19 + " and viewtype = '0' ";
/*      */         } 
/*      */       } else {
/*  689 */         str19 = str19 + " and tableIndex = '" + str7 + "' ";
/*      */       } 
/*      */     }
/*      */     
/*  693 */     if (!"".equals(str15)) {
/*  694 */       str19 = str19 + " and id not in (" + str15 + ") ";
/*      */     }
/*      */ 
/*      */     
/*  698 */     String str25 = " tableOrder,dsporder ";
/*      */     
/*  700 */     str19 = SqlUtils.replaceFirstAnd(str19);
/*      */     
/*  702 */     String str26 = "label";
/*  703 */     String str27 = " where 1=1 ";
/*  704 */     if (!"".equals(str1)) {
/*  705 */       str27 = str27 + " and (" + str26 + " like '%" + str1 + "%' ";
/*  706 */       if (!ConnectionPool.getInstance().isNewDB())
/*      */       {
/*  708 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/*  709 */           str27 = str27 + " or f_GetPy(" + str26 + ") like '%" + str1.toUpperCase() + "%'";
/*  710 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/*  711 */           str27 = str27 + " or [dbo].f_GetPy(" + str26 + ") like '%" + str1.toUpperCase() + "%'";
/*  712 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/*  713 */           str27 = str27 + " or getpinyin(" + str26 + ") like '%" + str1.toUpperCase() + "%'";
/*      */         }  } 
/*  715 */       str27 = str27 + ")";
/*      */     } 
/*  717 */     recordSet.executeQuery("select * from (select " + str20 + " from " + str21 + str19 + ") t2 " + str27 + " order by " + str25, new Object[0]);
/*  718 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  719 */     String str28 = Util.null2String(paramHttpServletRequest.getParameter("isNeedTrans"));
/*  720 */     while (recordSet.next()) {
/*  721 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  722 */       hashMap1.put("id", recordSet.getString("id"));
/*  723 */       hashMap1.put("tableindex", recordSet.getString("tableindex"));
/*  724 */       hashMap1.put("tableorder", recordSet.getString("tableOrder"));
/*  725 */       hashMap1.put("dbtype", recordSet.getString("dbtype"));
/*  726 */       String str = recordSet.getString(str26);
/*  727 */       if (str28.equals("1")) {
/*  728 */         String str29 = recordSet.getString("viewtype");
/*  729 */         String str30 = "";
/*  730 */         str30 = recordSet.getString("tableindex");
/*  731 */         String str31 = str29 + "+" + str30 + "+" + i + "+" + this.user.getLanguage() + "+" + j + "+" + recordSet.getString("maintablename");
/*  732 */         str = doFieldNameTrans(str, str31);
/*      */       } 
/*  734 */       hashMap1.put("name", str);
/*  735 */       if ("1".equals(str8)) {
/*  736 */         hashMap1.put("viewtype", recordSet.getString("viewtype"));
/*  737 */         hashMap1.put("httype", recordSet.getString("httype"));
/*  738 */         hashMap1.put("type", recordSet.getString("type"));
/*      */       } 
/*  740 */       arrayList.add(hashMap1);
/*      */     } 
/*  742 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  743 */     hashMap.put("datas", arrayList);
/*  744 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  749 */     int i = Util.getIntValue(Util.null2String(paramMap.get("workflowId")), -1);
/*  750 */     int j = Util.getIntValue(Util.null2String(paramMap.get("isBill")), -1);
/*  751 */     int k = Util.getIntValue(Util.null2String(paramMap.get("formId")), -1);
/*  752 */     String str1 = Util.null2String(paramMap.get("noNeedTableNumSearch"));
/*  753 */     String str2 = Util.null2String(paramMap.get("systemFieldType"));
/*  754 */     String str3 = Util.null2String(paramMap.get("isFromWfcustitle"));
/*  755 */     String str4 = Util.null2String(paramMap.get("isFromRuleDesign"));
/*  756 */     String str5 = Util.null2String(paramMap.get("showFieldname"));
/*  757 */     RecordSet recordSet = new RecordSet();
/*  758 */     if (j == -1 || k == -1) {
/*  759 */       String str = "SELECT formid , isbill FROM workflow_base WHERE id = ?";
/*  760 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(i) });
/*  761 */       if (recordSet.next()) {
/*  762 */         j = recordSet.getInt("isBill");
/*  763 */         k = recordSet.getInt("formId");
/*      */       } 
/*      */     } 
/*  766 */     String str6 = Util.null2String(paramMap.get("tableNum"));
/*  767 */     String str7 = Util.null2String(paramMap.get("detailTableName"));
/*      */     
/*  769 */     String str8 = "";
/*      */ 
/*      */     
/*  772 */     if (!"".equals(str7)) {
/*  773 */       ArrayList<String> arrayList3 = new ArrayList();
/*  774 */       DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/*  775 */       List<String> list = Arrays.asList(str7.split(","));
/*  776 */       for (String str : list) {
/*  777 */         arrayList3.add(detailOrderBiz.getDetailOrderByTableName(str) + "");
/*      */       }
/*      */       
/*  780 */       if ("0".equals(str6))
/*  781 */         arrayList3.add("0"); 
/*  782 */       str6 = String.join(",", (Iterable)arrayList3);
/*      */     } 
/*      */     
/*  785 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  786 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  787 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  788 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  789 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "1".equals(str5) ? 15456 : 685, "fieldName", true));
/*      */ 
/*      */     
/*  792 */     ArrayList<String> arrayList1 = new ArrayList();
/*      */ 
/*      */ 
/*      */     
/*  796 */     ArrayList<String> arrayList2 = new ArrayList();
/*      */     
/*  798 */     if ("1".equals(str3)) {
/*  799 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 63, "tableNumSearch");
/*  800 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*  801 */       arrayList3.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), true));
/*  802 */       arrayList3.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(468, this.user.getLanguage()), false));
/*  803 */       arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()), false));
/*  804 */       searchConditionItem.setOptions(arrayList3);
/*  805 */       arrayList.add(searchConditionItem);
/*      */     } else {
/*  807 */       SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 26734, "tableNumSearch");
/*  808 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*      */       
/*  810 */       searchConditionItem.setLabelcol(7);
/*  811 */       searchConditionItem.setFieldcol(17);
/*  812 */       if ("1".equals(str4)) {
/*  813 */         str8 = "0";
/*  814 */         searchConditionItem.setLabel(SystemEnv.getHtmlLabelName(686, this.user.getLanguage()));
/*      */       } 
/*      */       
/*  817 */       arrayList3.add(new SearchConditionOption("all", SystemEnv.getHtmlLabelName(332, this.user.getLanguage()), str8.equals("")));
/*  818 */       if (!"".equals(str2)) {
/*  819 */         arrayList3.add(new SearchConditionOption("-1", SystemEnv.getHtmlLabelName(468, this.user.getLanguage()), str8.equals("-1")));
/*  820 */         if ("1".equals(str4)) {
/*  821 */           ((SearchConditionOption)arrayList3.get(arrayList3.size() - 1)).setShowname(SystemEnv.getHtmlLabelName(28415, this.user.getLanguage()));
/*      */         }
/*      */       } 
/*      */       
/*  825 */       if (!"".equals(str6)) {
/*  826 */         arrayList1.addAll(Util.TokenizerString(str6, ","));
/*      */       } else {
/*  828 */         String str = "";
/*  829 */         arrayList1.add("0");
/*      */ 
/*      */         
/*  832 */         arrayList2.add("0");
/*  833 */         if (j == 0) {
/*  834 */           StringBuffer stringBuffer = new StringBuffer();
/*  835 */           stringBuffer.append("  select distinct workflow_formfield.groupid               as tableindex,                         \n");
/*  836 */           stringBuffer.append(" (case when workflow_formfield.groupid is null then 0 else (workflow_formfield.groupid + 1) end ) AS tableOrder \n");
/*  837 */           stringBuffer.append("    from workflow_formfield, workflow_formdictdetail, workflow_fieldlable                     \n");
/*  838 */           stringBuffer.append("   where workflow_fieldlable.formid = workflow_formfield.formid                               \n");
/*  839 */           stringBuffer.append("     and workflow_fieldlable.isdefault = 1                                                    \n");
/*  840 */           stringBuffer.append("     and workflow_fieldlable.fieldid = workflow_formfield.fieldid                             \n");
/*  841 */           stringBuffer.append("     and workflow_formdictdetail.id = workflow_formfield.fieldid                              \n");
/*  842 */           stringBuffer.append("     and workflow_formfield.formid =" + k + "                                            \n");
/*  843 */           stringBuffer.append("     and (workflow_formfield.isdetail = '1' or workflow_formfield.isdetail is not null)       \n");
/*  844 */           stringBuffer.append("     order by  tableOrder                                                                        \n");
/*  845 */           str = stringBuffer.toString();
/*      */         } else {
/*  847 */           str = "SELECT (case when orderid is null then 0 else orderid end) as tableOrder FROM Workflow_billdetailtable where billid = " + k + " order by tableOrder";
/*      */         } 
/*  849 */         recordSet.execute(str);
/*      */ 
/*      */         
/*  852 */         byte b1 = 1;
/*  853 */         while (recordSet.next()) {
/*  854 */           String str9 = recordSet.getString("tableOrder");
/*  855 */           if ("0".equals(str9)) {
/*  856 */             arrayList1.add(str9);
/*      */           } else {
/*  858 */             arrayList1.add("" + b1);
/*      */           } 
/*  860 */           b1++;
/*  861 */           arrayList2.add(str9);
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  870 */       byte b = 1;
/*  871 */       for (String str : arrayList1) {
/*  872 */         if ("0".equals(str)) {
/*  873 */           arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(21778, this.user.getLanguage()), str8.equals("0")));
/*  874 */           if ("1".equals(str4)) {
/*  875 */             ((SearchConditionOption)arrayList3.get(arrayList3.size() - 1)).setShowname(SystemEnv.getHtmlLabelName(21740, this.user.getLanguage()));
/*      */           }
/*      */           
/*      */           continue;
/*      */         } 
/*      */         
/*  881 */         if (arrayList2.size() == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  890 */           arrayList3.add(new SearchConditionOption(str, SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + str, str8.equals(str)));
/*      */           continue;
/*      */         } 
/*  893 */         arrayList3.add(new SearchConditionOption(String.valueOf(b), SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b, str8.equals(Integer.valueOf(b))));
/*  894 */         b++;
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  900 */       searchConditionItem.setOptions(arrayList3);
/*  901 */       if (!"1".equals(str1)) {
/*  902 */         arrayList.add(searchConditionItem);
/*      */       }
/*      */     } 
/*  905 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  911 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  912 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  913 */     String str2 = Util.null2String(paramMap.get("workflowId"));
/*  914 */     int i = Util.getIntValue(Util.null2String(paramMap.get("isBill")), -1);
/*  915 */     int j = Util.getIntValue(Util.null2String(paramMap.get("formId")), -1);
/*  916 */     String str3 = Util.null2String(Util.null2String(paramMap.get("nodeId")));
/*  917 */     String str4 = Util.null2String(paramMap.get("systemFieldType"));
/*  918 */     if (i == -1 || j == -1) {
/*  919 */       RecordSet recordSet1 = new RecordSet();
/*  920 */       String str = "SELECT formid , isbill FROM workflow_base WHERE id = ?";
/*  921 */       recordSet1.executeQuery(str, new Object[] { str2 });
/*  922 */       if (recordSet1.next()) {
/*  923 */         i = recordSet1.getInt("isBill");
/*  924 */         j = recordSet1.getInt("formId");
/*      */       } 
/*      */     } 
/*      */     
/*  928 */     if (!str3.equals("")) {
/*  929 */       str1 = LinkAgeViewAttrUtil.getSelectids(paramMap, this.user);
/*      */     }
/*      */     
/*  932 */     if ("".equals(str1)) {
/*  933 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, new ArrayList());
/*  934 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  935 */       return (Map)hashMap;
/*      */     } 
/*  937 */     String str5 = "";
/*  938 */     if (i == 0) {
/*  939 */       str5 = str5 + " select id, name,label,dsporder,dbtype, httype,type, viewtype,detailtable, groupid , maintablename from ";
/*      */     } else {
/*  941 */       str5 = str5 + " select id, name,label,dbtype,httype,type,dsporder,viewtype, detailtable, maintablename from ";
/*      */     } 
/*  943 */     str5 = str5 + " ( " + getQueryFormFieldSQL(j, i, this.user) + " ) a where id in (" + str1 + ")";
/*  944 */     if (i == 0) {
/*  945 */       str5 = str5 + " order by groupid,dsporder ";
/*      */     } else {
/*  947 */       str5 = str5 + " order by detailtable,dsporder ";
/*      */     } 
/*  949 */     RecordSet recordSet = new RecordSet();
/*  950 */     recordSet.executeQuery(str5, new Object[0]);
/*  951 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  952 */     while (recordSet.next()) {
/*  953 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  954 */       hashMap1.put("id", recordSet.getString("id"));
/*  955 */       if (i == 0) {
/*  956 */         String str = recordSet.getString("viewtype") + "+" + recordSet.getString("groupid") + "+" + i + "+" + this.user.getLanguage() + "+" + j + "+" + recordSet.getString("maintablename");
/*  957 */         hashMap1.put("showname", doFieldNameTrans(recordSet.getString("label"), str));
/*  958 */         hashMap1.put("label", recordSet.getString("label"));
/*  959 */         hashMap1.put("tableindexspan", tableTypeTransmethod(recordSet.getString("groupid"), this.user.getLanguage() + "+" + recordSet.getString("maintablename") + "+" + recordSet.getString("viewtype")));
/*      */       } else {
/*  961 */         String str = recordSet.getString("viewtype") + "+" + recordSet.getString("detailtable") + "+" + i + "+" + this.user.getLanguage() + "+" + j + "+" + recordSet.getString("maintablename");
/*  962 */         hashMap1.put("showname", doFieldNameTrans(SystemEnv.getHtmlLabelName(recordSet.getInt("label"), this.user.getLanguage()), str));
/*  963 */         hashMap1.put("label", labelNameTransmethod(recordSet.getString("label"), this.user.getLanguage() + ""));
/*  964 */         hashMap1.put("tableindexspan", detailTableTrans(recordSet.getString("detailtable"), this.user.getLanguage() + "+" + recordSet.getString("viewtype") + "+" + recordSet.getString("maintablename")));
/*      */       } 
/*  966 */       arrayList.add(hashMap1);
/*      */     } 
/*  968 */     if (!"".equals(str4)) {
/*  969 */       List list = SystemFieldUtil.getSystemFields(str4, this.user, paramMap);
/*  970 */       for (SystemFieldInfoEntity systemFieldInfoEntity : list) {
/*  971 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  972 */         hashMap1.put("id", systemFieldInfoEntity.getId() + "");
/*  973 */         hashMap1.put("showname", systemFieldInfoEntity.getMaintablename() + "." + systemFieldInfoEntity.getLabel());
/*  974 */         hashMap1.put("label", systemFieldInfoEntity.getLabel());
/*  975 */         hashMap1.put("tableindexspan", systemFieldInfoEntity.getMaintablename());
/*  976 */         arrayList.add(hashMap1);
/*      */       } 
/*      */     } 
/*      */     
/*  980 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*  981 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/*  982 */     arrayList1.add(new ListHeadBean("fieldName", "", 1));
/*  983 */     arrayList1.add(new ListHeadBean("tableType", "", 0));
/*      */     
/*  985 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/*  986 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  987 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public String tableTypeTransmethod(String paramString1, String paramString2) {
/*  992 */     String str = "";
/*  993 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  994 */     int i = Util.getIntValue(arrayOfString[0]);
/*  995 */     int j = Util.getIntValue(arrayOfString[2]);
/*  996 */     if (paramString1 == null || "".equals(paramString1) || j == 0) {
/*  997 */       str = SystemEnv.getHtmlLabelName(21778, i);
/*  998 */     } else if (Util.getIntValue(paramString1) < 0) {
/*  999 */       String str1 = arrayOfString[1];
/* 1000 */       str = str1;
/*      */     } else {
/* 1002 */       int k = Util.getIntValue(paramString1);
/* 1003 */       str = SystemEnv.getHtmlLabelName(19325, i) + (k + 1);
/*      */     } 
/* 1005 */     return str;
/*      */   }
/*      */   
/*      */   public String labelNameTransmethod(String paramString1, String paramString2) {
/* 1009 */     String str = "";
/* 1010 */     int i = Util.getIntValue(paramString2);
/* 1011 */     int j = Util.getIntValue(paramString1);
/* 1012 */     str = SystemEnv.getHtmlLabelName(j, i);
/* 1013 */     return str;
/*      */   }
/*      */   
/*      */   public String detailTableTrans(String paramString1, String paramString2) {
/* 1017 */     String str1 = "";
/* 1018 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1019 */     int i = Util.getIntValue(arrayOfString[0]);
/* 1020 */     int j = Util.getIntValue(arrayOfString[1]);
/* 1021 */     boolean bool = false;
/* 1022 */     String str2 = "0";
/* 1023 */     int k = 0;
/*      */ 
/*      */ 
/*      */     
/* 1027 */     String str3 = "select billid from workflow_billdetailtable where tablename =?";
/* 1028 */     RecordSet recordSet = new RecordSet();
/* 1029 */     recordSet.executeQuery(str3, new Object[] { paramString1 });
/* 1030 */     DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/* 1031 */     Map map = new HashMap<>();
/* 1032 */     while (recordSet.next()) {
/* 1033 */       map = detailOrderBiz.getDetailOrderMap(recordSet.getInt("billid"));
/*      */     }
/*      */     
/* 1036 */     if (arrayOfString.length > 3) {
/* 1037 */       bool = "portal".equals(Util.null2String(arrayOfString[2]));
/*      */     }
/*      */     
/* 1040 */     if (arrayOfString.length == 4) {
/* 1041 */       k = Util.getIntValue(arrayOfString[3], 0);
/*      */     }
/*      */     
/* 1044 */     if (arrayOfString.length == 5) {
/* 1045 */       str2 = Util.null2String(arrayOfString[4]);
/*      */     }
/*      */     
/* 1048 */     if (j == 0) {
/* 1049 */       str1 = SystemEnv.getHtmlLabelName(21778, i);
/* 1050 */       if ("1".equals(str2)) {
/* 1051 */         str1 = SystemEnv.getHtmlLabelName(18020, i);
/*      */       }
/* 1053 */       if (bool && k < 0) {
/* 1054 */         str1 = str1 + "（" + SystemEnv.getHtmlLabelName(28415, i) + "）";
/*      */       }
/* 1056 */     } else if (j < 0) {
/* 1057 */       String str = arrayOfString[2];
/* 1058 */       str1 = str;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/* 1065 */     else if (paramString1.startsWith("_dt")) {
/* 1066 */       int m = Util.getIntValue(paramString1.substring(3));
/* 1067 */       str1 = SystemEnv.getHtmlLabelName(19325, i) + m;
/*      */     } else {
/* 1069 */       int m = ((Integer)map.get(paramString1)).intValue();
/* 1070 */       str1 = SystemEnv.getHtmlLabelName(19325, i) + m;
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1087 */     return str1;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getOrderidByTablename(String paramString) {
/* 1092 */     RecordSet recordSet = new RecordSet();
/* 1093 */     String str = "select orderid from workflow_billdetailtable where tablename = ?";
/* 1094 */     recordSet.executeQuery(str, new Object[] { paramString });
/* 1095 */     if (recordSet.next()) {
/* 1096 */       return Util.null2String(recordSet.getString(1));
/*      */     }
/* 1098 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getShowName(String paramString1, String paramString2) {
/* 1108 */     if ("".equals(paramString1)) {
/* 1109 */       return LabelUtil.getMultiLangLabel(paramString2);
/*      */     }
/* 1111 */     return paramString1;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String doFieldNameTrans(String paramString1, String paramString2) {
/* 1117 */     String str1 = "";
/* 1118 */     String[] arrayOfString = paramString2.split("\\+");
/*      */     
/* 1120 */     if (arrayOfString.length >= 7 && "".equals(paramString1)) {
/* 1121 */       String str = arrayOfString[6];
/* 1122 */       paramString1 = LabelUtil.getMultiLangLabel(str);
/*      */     } 
/* 1124 */     String str2 = arrayOfString[0];
/* 1125 */     String str3 = arrayOfString[1];
/* 1126 */     String str4 = arrayOfString[2];
/* 1127 */     int i = Util.getIntValue(arrayOfString[3]);
/* 1128 */     String str5 = arrayOfString[4];
/*      */ 
/*      */ 
/*      */     
/* 1132 */     DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/* 1133 */     Map map = detailOrderBiz.getDetailOrderMap(Integer.parseInt(str5));
/*      */     
/* 1135 */     if (Util.getIntValue(str2) < 0) {
/* 1136 */       String str = arrayOfString[5];
/* 1137 */       str1 = str + "." + paramString1;
/* 1138 */     } else if (str2.equals("0")) {
/* 1139 */       str1 = SystemEnv.getHtmlLabelName(21778, i) + "." + paramString1;
/*      */     }
/* 1141 */     else if (str4.equals("0")) {
/* 1142 */       str1 = SystemEnv.getHtmlLabelName(19325, i) + (Util.getIntValue(str3) + 1) + "." + paramString1;
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */       
/* 1149 */       int j = ((Integer)map.get(str3)).intValue();
/* 1150 */       str1 = SystemEnv.getHtmlLabelName(19325, i) + j + "." + paramString1;
/*      */     } 
/*      */ 
/*      */     
/* 1154 */     return str1;
/*      */   }
/*      */   
/*      */   private String getUnionSql(SystemFieldInfoEntity paramSystemFieldInfoEntity, int paramInt, String paramString) {
/* 1158 */     RecordSet recordSet = new RecordSet();
/* 1159 */     String str1 = recordSet.getDBType();
/* 1160 */     String str2 = "";
/*      */ 
/*      */     
/* 1163 */     paramSystemFieldInfoEntity.setFieldname(Util.null2String(paramSystemFieldInfoEntity.getFieldname()).replace("'", "''"));
/* 1164 */     paramSystemFieldInfoEntity.setLabel(Util.null2String(paramSystemFieldInfoEntity.getLabel()).replace("'", "''"));
/*      */     
/* 1166 */     if (paramInt == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1180 */       str2 = " UNION SELECT " + paramSystemFieldInfoEntity.getId() + " AS ID,'" + Util.null2String(paramSystemFieldInfoEntity.getFieldname()) + "' AS NAME,'" + paramSystemFieldInfoEntity.getLabel() + "' AS label,'" + paramSystemFieldInfoEntity.getLabel() + "' AS showname, 0 AS labelindex, '" + paramSystemFieldInfoEntity.getDbType() + "' AS dbtype,'" + paramSystemFieldInfoEntity.getHttpType() + "' AS httype," + paramSystemFieldInfoEntity.getType() + " AS TYPE," + paramSystemFieldInfoEntity.getDsporder() + " AS dsporder,'" + paramSystemFieldInfoEntity.getViewtype() + "' AS viewtype,-1 AS tableindex," + paramSystemFieldInfoEntity.getTableOrder() + " AS tableOrder, '" + paramSystemFieldInfoEntity.getMaintablename() + "' AS maintablename ";
/* 1181 */       if ("overtime".equals(paramString)) {
/* 1182 */         str2 = str2 + ",'" + paramSystemFieldInfoEntity.getToFieldTip() + "' as toFieldTip";
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1197 */       str2 = " UNION SELECT " + paramSystemFieldInfoEntity.getId() + " AS ID,'" + paramSystemFieldInfoEntity.getLabel() + "' AS label,'" + paramSystemFieldInfoEntity.getLabel() + "' AS showname, 0 AS labelindex, '" + Util.null2String(paramSystemFieldInfoEntity.getFieldname()) + "' AS NAME,'" + paramSystemFieldInfoEntity.getDbType() + "' AS dbtype,'" + paramSystemFieldInfoEntity.getHttpType() + "' AS httype," + paramSystemFieldInfoEntity.getType() + " AS TYPE," + paramSystemFieldInfoEntity.getDsporder() + " AS dsporder," + paramSystemFieldInfoEntity.getViewtype() + " AS viewtype,'-1' AS tableindex," + paramSystemFieldInfoEntity.getTableOrder() + " AS tableOrder, '" + paramSystemFieldInfoEntity.getMaintablename() + "' AS maintablename, 0 AS isencrypt ";
/*      */       
/* 1199 */       if ("overtime".equals(paramString))
/* 1200 */         str2 = str2 + ",'" + paramSystemFieldInfoEntity.getToFieldTip() + "' as toFieldTip"; 
/*      */     } 
/* 1202 */     if ("oracle".equals(str1)) {
/* 1203 */       str2 = str2 + " FROM DUAL ";
/*      */     }
/* 1205 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void fixOldFormFieldData(int paramInt) {
/* 1213 */     RecordSet recordSet = new RecordSet();
/* 1214 */     recordSet.executeUpdate("update workflow_formfield set groupid = null where isdetail is null and formid = ?", new Object[] { Integer.valueOf(paramInt) });
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getNewGroupid(String paramString, int paramInt) {
/* 1220 */     RecordSet recordSet = new RecordSet();
/* 1221 */     String str1 = "";
/* 1222 */     String str2 = "";
/*      */     
/* 1224 */     if (recordSet.getOrgindbtype().toLowerCase().equals("gs")) {
/* 1225 */       str2 = "SELECT t.tablename FROM (select rid, orderid,tablename  from (SELECT row_number() over(order by orderid) as rid,tablename, orderid  FROM Workflow_billdetailtable  WHERE billid = " + paramString + " order by orderid) t1) t  WHERE t.rid = " + paramInt;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/* 1232 */     else if (recordSet.getDBType().equals("oracle")) {
/* 1233 */       str2 = " SELECT t.tablename FROM (select rownum as rid,orderid,t1.tablename from (SELECT tablename, orderid FROM Workflow_billdetailtable  WHERE billid = " + paramString + " order by orderid) t1) t WHERE t.rid=" + paramInt;
/* 1234 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 1235 */       str2 = "select t2.tablename,convert(rid,SIGNED) as rid  from (SELECT (@rowNum := @rowNum + 1) as rid, t1.orderid,t1.tablename       from (SELECT (@rowNum := 0) as rid,tablename, orderid             FROM Workflow_billdetailtable             WHERE billid = " + paramString + "            order by orderid) t1) t2  where t2.rid = " + paramInt;
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */ 
/*      */       
/* 1243 */       str2 = "SELECT t.tablename FROM (SELECT ROW_NUMBER() OVER (ORDER BY ORDERid) AS rid ,tablename,orderid FROM Workflow_billdetailtable WHERE billid=" + paramString + " ) t WHERE t.rid=" + paramInt;
/*      */     } 
/* 1245 */     recordSet.executeSql(str2);
/* 1246 */     if (recordSet.next()) {
/* 1247 */       str1 = recordSet.getString(1);
/*      */     }
/*      */     
/* 1250 */     return str1;
/*      */   }
/*      */ 
/*      */   
/*      */   public String orderidTrans(String paramString1, String paramString2) {
/* 1255 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*      */     
/* 1257 */     int i = Util.getIntValue(arrayOfString[0]);
/* 1258 */     String str1 = arrayOfString[1];
/* 1259 */     int j = Util.getIntValue(arrayOfString[2]);
/*      */     
/* 1261 */     String str2 = "";
/*      */     
/* 1263 */     if (paramString1.equals("") || paramString1.equals("0"))
/* 1264 */       return paramString1; 
/* 1265 */     if (i == 0) {
/* 1266 */       return paramString1;
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1278 */     DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/* 1279 */     Map map = detailOrderBiz.getDetailOrderMap(j);
/* 1280 */     str2 = String.valueOf(map.get(str1));
/* 1281 */     paramString1 = str2;
/* 1282 */     return paramString1;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkflowFieldBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */