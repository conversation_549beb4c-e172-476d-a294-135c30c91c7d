/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SearchConditionOption;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.engine.cube.biz.SqlHelper;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModeTreeBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = Util.null2String(paramMap.get("treename"));
/* 33 */     String str2 = Util.null2String(paramMap.get("appid"));
/*    */ 
/*    */     
/* 36 */     String str3 = " where a.appid=b.id and a.showtype=1 and b.isdelete=0 ";
/* 37 */     String str4 = " a.id,a.treename,b.id as appid,b.treeFieldName as appname ";
/* 38 */     String str5 = " from mode_customtree a,modeTreeField b ";
/*    */     
/* 40 */     if (!str1.equals("")) {
/* 41 */       str3 = str3 + " and a.treename like '%" + str1 + "%'";
/*    */     }
/* 43 */     if (!str2.equals("")) {
/* 44 */       str3 = str3 + " and b.id=" + str2 + "";
/*    */     }
/* 46 */     String str6 = " a.id ";
/*    */     
/* 48 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 49 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 50 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(30209, this.user.getLanguage()), "treename", "treename")).setIsInputCol(BoolAttr.TRUE));
/* 51 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(82186, this.user.getLanguage()), "appname", "appname"));
/*    */ 
/*    */     
/* 54 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "a.id", arrayList);
/* 55 */     splitTableBean.setSqlsortway("ASC");
/* 56 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 62 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 63 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 64 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 30209, "treename").setIsQuickSearch(true));
/* 66 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.SELECT, 82186, "appid");
/* 67 */     ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 68 */     arrayList1.add(new SearchConditionOption("", ""));
/* 69 */     String str = "select * from modeTreeField where (isdelete is null or isdelete=0 ) ";
/* 70 */     RecordSet recordSet = new RecordSet();
/* 71 */     if ((new ManageDetachComInfo()).isUseFmManageDetach()) {
/* 72 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 73 */       int[] arrayOfInt = checkSubCompanyRight.getSubComByUserRightId(this.user.getUID(), "FORMMODEAPP:ALL", 0);
/* 74 */       if (arrayOfInt.length == 0) {
/* 75 */         str = str + " and 1=2 ";
/*    */       } else {
/* 77 */         str = str + " and " + SqlHelper.SplitSqlInCondition("subCompanyId", arrayOfInt);
/*    */       } 
/*    */     } 
/* 80 */     str = str + " order  by showOrder asc ";
/* 81 */     recordSet.executeSql(str);
/* 82 */     while (recordSet.next()) {
/* 83 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 84 */       String str2 = Util.null2String(recordSet.getString("treeFieldName"));
/* 85 */       arrayList1.add(new SearchConditionOption(str1, str2));
/*    */     } 
/* 87 */     searchConditionItem.setOptions(arrayList1);
/* 88 */     arrayList.add(searchConditionItem);
/* 89 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 90 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeTreeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */