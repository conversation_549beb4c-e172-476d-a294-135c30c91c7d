/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ScheduleDeviceBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 30 */     RecordSet recordSet = new RecordSet();
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 32 */     String str1 = Util.null2String(paramMap.get("devicename"));
/* 33 */     String str2 = Util.null2String(paramMap.get("devicecode"));
/* 34 */     String str3 = Util.null2String(paramMap.get("subcompanyid"));
/* 35 */     String str4 = " where 1=1 ";
/*    */     
/* 37 */     if (!str3.equals("")) {
/* 38 */       str4 = str4 + " and (subcompanyids is null OR subcompanyids = '' ";
/* 39 */       if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("db2") || recordSet.getDBType().equals("mysql")) {
/* 40 */         str4 = str4 + " or concat(concat(',',subcompanyids),',') like '%," + str3 + ",%'";
/*    */       }
/* 42 */       else if (recordSet.getDBType().equals("postgresql")) {
/* 43 */         str4 = str4 + " or ','||subcompanyids||',' like '%," + str3 + ",%'";
/*    */       } else {
/* 45 */         str4 = str4 + " or ','+subcompanyids+',' like '%," + str3 + ",%'";
/*    */       } 
/* 47 */       str4 = str4 + ") ";
/*    */     } 
/*    */     
/* 50 */     if (!str1.equals("")) {
/* 51 */       str4 = str4 + " and devicename like '%";
/* 52 */       str4 = str4 + str1;
/* 53 */       str4 = str4 + "%'";
/*    */     } 
/* 55 */     if (!str2.equals("")) {
/* 56 */       str4 = str4 + " and devicecode like '%";
/* 57 */       str4 = str4 + str2;
/* 58 */       str4 = str4 + "%'";
/*    */     } 
/* 60 */     String str5 = "id,devicename,devicecode";
/* 61 */     String str6 = "kq_schedule_device";
/*    */     
/* 63 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 64 */     arrayList.add(new SplitTableColBean("hide", "id"));
/* 65 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(512449, this.user.getLanguage()), "devicename", "devicename")).setIsInputCol(BoolAttr.TRUE));
/* 66 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(512448, this.user.getLanguage()), "devicecode", "devicecode"));
/*    */     
/* 68 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", arrayList);
/* 69 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 70 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 75 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 76 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 77 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 78 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 79 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 512449, "devicename", true));
/* 80 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 512448, "devicecode", true));
/*    */     
/* 82 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ScheduleDeviceBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */