/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.api.browser.util.SqlUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestOperationTypeService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  35 */     String str1 = Util.null2String(paramMap.get("name"));
/*  36 */     String str2 = " where b.languageid = " + this.user.getLanguage();
/*  37 */     if (!"".equals(str1)) {
/*  38 */       str2 = str2 + " and b.labelname like '%";
/*  39 */       str2 = str2 + Util.fromScreen2(str1, this.user.getLanguage());
/*  40 */       str2 = str2 + "%'";
/*     */     } 
/*     */     
/*  43 */     String str3 = " typeorder ";
/*  44 */     String str4 = " id,type,b.labelname,typeorder ";
/*  45 */     String str5 = " from workflow_operationtype a left join htmllabelinfo b on a.label = b.indexid ";
/*     */     
/*  47 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  48 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  49 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(81454, this.user.getLanguage()), "labelname", "labelname", 1)).setIsInputCol(BoolAttr.TRUE));
/*     */     
/*  51 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str2, str3, "id", arrayList);
/*     */     
/*  53 */     splitTableBean.setSqlsortway("ASC");
/*  54 */     splitTableBean.setSqlisdistinct("true");
/*  55 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  56 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  61 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  62 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  63 */     if ("".equals(str1)) return (Map)hashMap; 
/*  64 */     String str2 = "select a.id,b.labelname from workflow_operationtype a left join htmllabelinfo b on a.label = b.indexid where a.id in (" + str1 + ") and b.languageid = " + this.user.getLanguage();
/*  65 */     RecordSet recordSet = new RecordSet();
/*  66 */     recordSet.executeSql(str2);
/*  67 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  68 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  69 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  70 */     while (recordSet.next()) {
/*  71 */       String str3 = Util.null2String(recordSet.getString("id"));
/*  72 */       String str4 = Util.null2String(recordSet.getString("labelname"));
/*     */       
/*  74 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  75 */       hashMap1.put("id", str3);
/*  76 */       hashMap1.put("labelname", str4);
/*  77 */       arrayList.add(hashMap1);
/*     */     } 
/*  79 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/*  80 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  86 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  88 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  89 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 81454, "name", true));
/*  90 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  91 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  97 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  98 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  99 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 100 */     String str2 = "id,type,b.labelname,typeorder ";
/* 101 */     String str3 = " typeorder";
/* 102 */     String str4 = " from workflow_operationtype a left join htmllabelinfo b on a.label = b.indexid where b.labelname like '%" + str1 + "%' and b.languageid = " + this.user.getLanguage();
/* 103 */     String str5 = SqlUtils.getPageSql(str2, str4, str3, 0, 30);
/* 104 */     RecordSet recordSet = new RecordSet();
/* 105 */     recordSet.executeQuery(str5, new Object[0]);
/* 106 */     while (recordSet.next()) {
/* 107 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 108 */       hashMap1.put(BrowserConstant.BROWSER_AUTO_COMPLETE_PRIMARY_KEY, recordSet.getString("id"));
/* 109 */       hashMap1.put(BrowserConstant.BROWSER_AUTO_COMPLETE_SHOW_NAME, recordSet.getString("labelname"));
/* 110 */       arrayList.add(hashMap1);
/*     */     } 
/* 112 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 113 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/RequestOperationTypeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */