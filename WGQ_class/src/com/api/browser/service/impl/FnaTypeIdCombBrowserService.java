/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaTypeIdCombBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */ 
/*     */     
/*  40 */     RecordSet recordSet1 = new RecordSet();
/*  41 */     RecordSet recordSet2 = new RecordSet();
/*  42 */     StringBuffer stringBuffer = new StringBuffer();
/*  43 */     recordSet1.executeQuery(" select * from FnaAccountDtl where tableType = 12 ", new Object[0]);
/*  44 */     int i = recordSet1.getCounts();
/*  45 */     byte b = 0;
/*  46 */     while (recordSet1.next()) {
/*  47 */       String str = Util.null2String(recordSet1.getString("tableName"));
/*     */       
/*  49 */       stringBuffer.append(" select distinct a.numberCode, ");
/*  50 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 1) then a.dimensionTypeId else 0 end) dtId1,  ");
/*  51 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 2) then a.dimensionTypeId else 0 end) dtId2, ");
/*  52 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 3) then a.dimensionTypeId else 0 end) dtId3, ");
/*  53 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 4) then a.dimensionTypeId else 0 end) dtId4, ");
/*  54 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 5) then a.dimensionTypeId else 0 end) dtId5, ");
/*  55 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 6) then a.dimensionTypeId else 0 end) dtId6, ");
/*  56 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 7) then a.dimensionTypeId else 0 end) dtId7, ");
/*  57 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 8) then a.dimensionTypeId else 0 end) dtId8, ");
/*  58 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 9) then a.dimensionTypeId else 0 end) dtId9, ");
/*  59 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 10) then a.dimensionTypeId else 0 end) dtId10, ");
/*  60 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 11) then a.dimensionTypeId else 0 end) dtId11, ");
/*  61 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 12) then a.dimensionTypeId else 0 end) dtId12, ");
/*  62 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 13) then a.dimensionTypeId else 0 end) dtId13, ");
/*  63 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 14) then a.dimensionTypeId else 0 end) dtId14, ");
/*  64 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 15) then a.dimensionTypeId else 0 end) dtId15, ");
/*  65 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 16) then a.dimensionTypeId else 0 end) dtId16, ");
/*  66 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 17) then a.dimensionTypeId else 0 end) dtId17, ");
/*  67 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 18) then a.dimensionTypeId else 0 end) dtId18, ");
/*  68 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 19) then a.dimensionTypeId else 0 end) dtId19, ");
/*  69 */       stringBuffer.append(" sum(case when (a.dimensionTypeId = 20) then a.dimensionTypeId else 0 end) dtId20, ");
/*  70 */       stringBuffer.append(" count(*) cnt ");
/*  71 */       stringBuffer.append(" from ").append(str).append(" a ");
/*  72 */       stringBuffer.append(" group by a.numberCode ");
/*     */       
/*  74 */       if (b != i - 1) {
/*  75 */         stringBuffer.append(" union ");
/*     */       }
/*  77 */       b++;
/*     */     } 
/*     */     
/*  80 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  81 */     recordSet1.executeQuery(" select * from FnaDimensionType ", new Object[0]);
/*  82 */     while (recordSet1.next()) {
/*  83 */       String str1 = Util.null2String(recordSet1.getString("id"));
/*  84 */       String str2 = Util.null2String(recordSet1.getString("typeName"));
/*  85 */       hashMap2.put(str1, str2);
/*     */     } 
/*     */     
/*  88 */     int[] arrayOfInt = new int[20];
/*     */     
/*  90 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  91 */     recordSet2.executeQuery(stringBuffer.toString(), new Object[0]);
/*  92 */     while (recordSet2.next()) {
/*  93 */       arrayOfInt[0] = Util.getIntValue(recordSet2.getString("dtId1"), 0);
/*  94 */       arrayOfInt[1] = Util.getIntValue(recordSet2.getString("dtId2"), 0);
/*  95 */       arrayOfInt[2] = Util.getIntValue(recordSet2.getString("dtId3"), 0);
/*  96 */       arrayOfInt[3] = Util.getIntValue(recordSet2.getString("dtId4"), 0);
/*  97 */       arrayOfInt[4] = Util.getIntValue(recordSet2.getString("dtId5"), 0);
/*  98 */       arrayOfInt[5] = Util.getIntValue(recordSet2.getString("dtId6"), 0);
/*  99 */       arrayOfInt[6] = Util.getIntValue(recordSet2.getString("dtId7"), 0);
/* 100 */       arrayOfInt[7] = Util.getIntValue(recordSet2.getString("dtId8"), 0);
/* 101 */       arrayOfInt[8] = Util.getIntValue(recordSet2.getString("dtId9"), 0);
/* 102 */       arrayOfInt[9] = Util.getIntValue(recordSet2.getString("dtId10"), 0);
/* 103 */       arrayOfInt[10] = Util.getIntValue(recordSet2.getString("dtId11"), 0);
/* 104 */       arrayOfInt[11] = Util.getIntValue(recordSet2.getString("dtId12"), 0);
/* 105 */       arrayOfInt[12] = Util.getIntValue(recordSet2.getString("dtId13"), 0);
/* 106 */       arrayOfInt[13] = Util.getIntValue(recordSet2.getString("dtId14"), 0);
/* 107 */       arrayOfInt[14] = Util.getIntValue(recordSet2.getString("dtId15"), 0);
/* 108 */       arrayOfInt[15] = Util.getIntValue(recordSet2.getString("dtId16"), 0);
/* 109 */       arrayOfInt[16] = Util.getIntValue(recordSet2.getString("dtId17"), 0);
/* 110 */       arrayOfInt[17] = Util.getIntValue(recordSet2.getString("dtId18"), 0);
/* 111 */       arrayOfInt[18] = Util.getIntValue(recordSet2.getString("dtId19"), 0);
/* 112 */       arrayOfInt[19] = Util.getIntValue(recordSet2.getString("dtId20"), 0);
/*     */       
/* 114 */       String str1 = "";
/* 115 */       String str2 = "";
/* 116 */       for (byte b1 = 0; b1 < arrayOfInt.length; b1++) {
/* 117 */         if (arrayOfInt[b1] != 0) {
/* 118 */           str1 = str1 + arrayOfInt[b1] + ",";
/* 119 */           str2 = str2 + (String)hashMap2.get(String.valueOf(arrayOfInt[b1])) + ",";
/*     */         } 
/*     */       } 
/*     */       
/* 123 */       str1 = str1.substring(0, str1.length() - 1);
/* 124 */       str2 = str2.substring(0, str2.length() - 1);
/*     */       
/* 126 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 127 */       hashMap.put("id", str1);
/* 128 */       hashMap.put("name", str2);
/* 129 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 132 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 133 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 134 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(504430, this.user.getLanguage()), 1));
/*     */     
/* 136 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 137 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 138 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*     */     
/* 140 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaTypeIdCombBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */