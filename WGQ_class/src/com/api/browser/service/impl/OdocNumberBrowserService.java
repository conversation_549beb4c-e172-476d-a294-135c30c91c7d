/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionGroup;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.cloudstore.eccom.result.WeaResultMsg;
/*     */ import com.engine.odoc.util.OdocNumberManageUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ public class OdocNumberBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  35 */     boolean bool = "workflow".equals(Util.null2String(paramMap.get("fromModule")));
/*  36 */     String str1 = "isopen=1 ";
/*  37 */     str1 = str1 + Util.null2String(paramMap.get("sqlwhere"));
/*  38 */     String str2 = " id,typeName as name,prefix,yearPre,years,yearEnd,serialPre,serial,currentSerial,serialEnd,digit,fillZero,showYear";
/*  39 */     int i = Util.getIntValue(Util.null2String(paramMap.get("requestid")));
/*  40 */     int j = Util.getIntValue(Util.null2String(paramMap.get("workflowId")));
/*  41 */     if (j < 1) {
/*  42 */       j = Util.getIntValue(Util.null2String(paramMap.get("wfid")));
/*  43 */       if (j < 1) {
/*  44 */         j = Util.getIntValue(Util.null2String(paramMap.get("workflowid")));
/*     */       }
/*     */     } 
/*     */     
/*  48 */     if (j > 1) {
/*  49 */       str1 = str1 + " and " + OdocNumberManageUtil.getOdocNumberBrowserSqlWhere(j, bool, "id", "in");
/*     */     }
/*  51 */     String str3 = Util.null2String(paramMap.get("typeName"));
/*  52 */     String str4 = Util.null2String(paramMap.get("name"));
/*  53 */     if (!"".equals(str3)) {
/*  54 */       str1 = str1 + " and typeName like '%" + str3 + "%'";
/*     */     }
/*  56 */     if (!"".equals(str4)) {
/*  57 */       str1 = str1 + " and typeName like '%" + str4 + "%'";
/*     */     }
/*  59 */     String str5 = "odoc_numberManage";
/*  60 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  61 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  62 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(531665, this.user.getLanguage()), "name", "name", 1)).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/*  63 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(23690, this.user.getLanguage()), "preview", "preview", "com.engine.odoc.util.OdocNumberManageUtil.getPreviewStr", "column:prefix+column:yearPre+column:yearEnd+column:serialPre+column:serial+column:serialEnd+column:digit+column:fillZero+column:currentSerial+column:showYear+column:years", 0))
/*     */ 
/*     */         
/*  66 */         .setMobileviewtype(MobileViewTypeAttr.DETAIL));
/*     */     
/*  68 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str5, str1, "id", "id", "asc", arrayList);
/*     */     
/*  70 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/*  72 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/*  73 */     } catch (Exception exception) {
/*  74 */       exception.printStackTrace();
/*     */     } 
/*  76 */     splitTableBean.setSqlisdistinct("true");
/*  77 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  78 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  83 */     WeaResultMsg weaResultMsg = new WeaResultMsg(true);
/*  84 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  85 */     ArrayList<SearchConditionGroup> arrayList = new ArrayList();
/*  86 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/*  89 */     SearchConditionItem searchConditionItem = conditionFactory.createCondition(ConditionType.INPUT, 531665, "typeName", true);
/*  90 */     searchConditionItem.setPlaceholder(SystemEnv.getHtmlLabelName(532001, this.user.getLanguage()));
/*  91 */     arrayList1.add(searchConditionItem);
/*     */     
/*  93 */     arrayList.add(new SearchConditionGroup(SystemEnv.getHtmlLabelName(383122, Util.getIntValue(this.user.getLanguage())), true, arrayList1));
/*  94 */     weaResultMsg.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList1);
/*  95 */     return weaResultMsg.getResultMapAll();
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 100 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 101 */     OdocNumberManageUtil odocNumberManageUtil = new OdocNumberManageUtil();
/* 102 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 103 */     String str2 = Util.null2String(paramMap.get("isGeneratePrintMould"));
/* 104 */     if ("".equals(str1)) return (Map)hashMap; 
/* 105 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 106 */     RecordSet recordSet = new RecordSet();
/* 107 */     recordSet.executeQuery("select id,typeName,prefix,yearPre,years,yearEnd,serialPre,currentSerial,serialEnd,digit,fillZero,showYear from odoc_numberManage where id in (" + str1 + ")", new Object[0]);
/* 108 */     while (recordSet.next()) {
/* 109 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 110 */       hashMap1.put("id", recordSet.getString("id"));
/* 111 */       hashMap1.put("name", recordSet.getString("typeName"));
/* 112 */       String str3 = Util.null2String(recordSet.getString("prefix"));
/* 113 */       String str4 = Util.null2String(recordSet.getString("yearPre"));
/* 114 */       String str5 = Util.null2String(recordSet.getString("yearEnd"));
/* 115 */       String str6 = Util.null2String(recordSet.getString("serialPre"));
/* 116 */       String str7 = Util.null2String(recordSet.getString("serial"));
/* 117 */       int i = Util.getIntValue(Util.null2String(recordSet.getString("currentSerial")), 1);
/* 118 */       String str8 = Util.null2String(recordSet.getString("serialEnd"));
/* 119 */       int j = Util.getIntValue(Util.null2String(recordSet.getString("digit")), 0);
/* 120 */       int k = Util.getIntValue(Util.null2String(recordSet.getString("fillZero")), 0);
/* 121 */       int m = Util.getIntValue(Util.null2String(recordSet.getString("showYear")), 0);
/* 122 */       String str9 = Util.null2String(recordSet.getString("years"));
/* 123 */       String str10 = str3 + "+" + str4 + "+" + str5 + "+" + str6 + "+" + str7 + "+" + str8 + "+" + j + "+" + k + "+" + i + "+" + m + "+" + str9;
/* 124 */       hashMap1.put("preview", odocNumberManageUtil.getPreviewStr("", str10));
/* 125 */       arrayList.add(hashMap1);
/*     */     } 
/* 127 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 128 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 129 */     arrayList1.add(new ListHeadBean("typeName", "", 1, BoolAttr.TRUE));
/*     */     
/* 131 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 132 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 133 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 134 */     return (Map)hashMap;
/*     */   }
/* 136 */   private BaseBean logger = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getDetachableSqlWhere(int paramInt) {
/* 145 */     String str = " 1 = 1 ";
/* 146 */     if (paramInt > 0) {
/* 147 */       CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 148 */       int i = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "WorkflowManage:All", paramInt);
/* 149 */       if (i > -1) {
/* 150 */         str = str + " and subCompanyId = " + paramInt + " ";
/*     */       } else {
/* 152 */         str = str + " and 1 = 2 ";
/*     */       } 
/*     */     } else {
/*     */       try {
/* 156 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 157 */         String str1 = subCompanyComInfo.getRightSubCompany(this.user.getUID(), "WorkflowManage:All", -1);
/* 158 */         if (!str1.equals("")) {
/* 159 */           str = str + " and (" + Util.getSubINClause(str1, "subCompanyId", "in") + ") ";
/*     */         } else {
/* 161 */           str = str + " and 1 = 2 ";
/*     */         } 
/* 163 */       } catch (Exception exception) {
/* 164 */         this.logger.writeLog(exception);
/* 165 */         str = " 1 != 1 ";
/*     */       } 
/*     */     } 
/* 168 */     return str;
/*     */   }
/*     */   
/*     */   public String getHaveRightNumberIdS(int paramInt1, int paramInt2) {
/* 172 */     String str1 = " and 1!=1 ";
/* 173 */     RecordSet recordSet = new RecordSet();
/* 174 */     String str2 = "";
/* 175 */     recordSet.executeQuery("select id,subcompanyid,subDivisionCanUse,otherSubType,otherSubCompanyIds,coverSub from odoc_numberManage", new Object[0]);
/* 176 */     while (recordSet.next()) {
/* 177 */       int i = Util.getIntValue(Util.null2String(recordSet.getString("id")));
/* 178 */       int j = Util.getIntValue(Util.null2String(recordSet.getString("subcompanyid")));
/* 179 */       int k = Util.getIntValue(Util.null2String(recordSet.getString("subDivisionCanUse")));
/* 180 */       int m = Util.getIntValue(Util.null2String(recordSet.getString("otherSubType")));
/* 181 */       String str = Util.null2String(recordSet.getString("otherSubCompanyIds"));
/* 182 */       int n = Util.getIntValue(Util.null2String(recordSet.getString("coverSub")));
/* 183 */       boolean bool = false;
/* 184 */       if (j == paramInt2 || m == 1) {
/* 185 */         bool = true;
/*     */       } else {
/* 187 */         if (k == 1) {
/* 188 */           bool = containSubcompayid(j + "", paramInt2 + "");
/*     */         }
/* 190 */         if (!bool && 
/* 191 */           m == 2) {
/* 192 */           if (str == paramInt2 + "" || str.startsWith(paramInt2 + ",") || str.endsWith("," + paramInt2) || str.indexOf("," + paramInt2 + ",") >= 0) {
/* 193 */             bool = true;
/* 194 */           } else if (n == 1) {
/* 195 */             String[] arrayOfString = str.split(",");
/* 196 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 197 */               String str3 = arrayOfString[b];
/* 198 */               if (!"".equals(str3)) {
/* 199 */                 bool = containSubcompayid(str3, paramInt2 + "");
/* 200 */                 if (bool) {
/*     */                   break;
/*     */                 }
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 210 */       if (bool) {
/* 211 */         str2 = str2 + "," + i;
/*     */       }
/*     */     } 
/* 214 */     if (!"".equals(str2)) {
/* 215 */       str2 = str2.substring(1);
/* 216 */       str1 = " and " + Util.getSubINClause(str2, "id", "in");
/*     */     } 
/* 218 */     return str1;
/*     */   }
/*     */   
/*     */   public boolean containSubcompayid(String paramString1, String paramString2) {
/* 222 */     boolean bool = false;
/* 223 */     String str = SubCompanyComInfo.getAllChildSubcompanyId(paramString1, "");
/* 224 */     String[] arrayOfString = str.split(",");
/* 225 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 226 */       if (arrayOfString[b].equals(paramString2 + "")) {
/* 227 */         bool = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 231 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 239 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 240 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.name");
/* 241 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.preview");
/* 242 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OdocNumberBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */