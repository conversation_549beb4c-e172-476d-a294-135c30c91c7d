/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.BrowserTreeNode;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.engine.cube.cmd.app.GetAppListCmd;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.formmode.log.FormmodeLog;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class ModeAppTreeBrowserService
/*     */   extends BrowserService
/*     */ {
/*  20 */   private FormmodeLog formmodeLog = new FormmodeLog();
/*     */   
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  24 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/*  25 */     int j = Util.getIntValue(Util.null2String(paramMap.get("subCompanyId")), -1);
/*  26 */     String str = Util.null2String(paramMap.get("appid"));
/*  27 */     ArrayList<BrowserTreeNode> arrayList = new ArrayList();
/*  28 */     boolean bool = true;
/*     */     try {
/*  30 */       GetAppListCmd getAppListCmd = new GetAppListCmd(paramMap, this.user);
/*  31 */       if (j == 0) {
/*  32 */         j = -1;
/*     */       }
/*  34 */       List<Map> list = getAppListCmd.getAppInfos1(j);
/*  35 */       ArrayList<Map> arrayList1 = new ArrayList(); byte b;
/*  36 */       for (b = 0; b < list.size(); b++) {
/*  37 */         Map map = list.get(b);
/*  38 */         if (Util.null2String(map.get("superFieldid")).equals(Util.null2String(Integer.valueOf(i))) && !Util.null2String(map.get("id")).equals(str)) {
/*  39 */           arrayList1.add(list.get(b));
/*     */         }
/*     */       } 
/*  42 */       getAppListCmd.sortList(arrayList1);
/*     */       
/*  44 */       for (b = 0; b < arrayList1.size(); b++) {
/*  45 */         Map map = arrayList1.get(b);
/*  46 */         BrowserTreeNode browserTreeNode = new BrowserTreeNode();
/*  47 */         String str1 = Util.null2String(map.get("id"));
/*  48 */         int k = Util.getIntValue(Util.null2String(map.get("childappcount")), 0);
/*  49 */         browserTreeNode.setId(str1);
/*  50 */         browserTreeNode.setName(Util.null2String(map.get("treeFieldName")));
/*  51 */         browserTreeNode.setTitle(Util.null2String(map.get("treeFieldDesc")));
/*  52 */         browserTreeNode.setLinkUrl("");
/*  53 */         browserTreeNode.setIcon("");
/*  54 */         browserTreeNode.setIsImgIcon(false);
/*  55 */         browserTreeNode.setCanClick(true);
/*  56 */         String str2 = "2";
/*  57 */         browserTreeNode.setType(str2);
/*  58 */         browserTreeNode.setPid(Util.null2String(Integer.valueOf(i)));
/*  59 */         if (k > 0) {
/*  60 */           browserTreeNode.setIsParent(true);
/*     */         } else {
/*  62 */           browserTreeNode.setIsParent(false);
/*     */         } 
/*  64 */         browserTreeNode.setSelected(false);
/*  65 */         browserTreeNode.setCheckStrictly(bool);
/*  66 */         arrayList.add(browserTreeNode);
/*     */       } 
/*  68 */     } catch (Exception exception) {
/*  69 */       this.formmodeLog.writeLog(exception);
/*     */     } 
/*  71 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.TREE_DATA.getTypeid()));
/*  72 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/*  73 */     hashMap.put("checkStrictly", Boolean.valueOf(bool));
/*  74 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  79 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  80 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  81 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("appid"));
/*  82 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("subCompanyId")), -1);
/*  83 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  84 */     hashMap2.put("funType", paramHttpServletRequest.getParameter("funType"));
/*  85 */     ArrayList arrayList = new ArrayList();
/*     */     try {
/*  87 */       GetAppListCmd getAppListCmd = new GetAppListCmd(hashMap2, this.user);
/*  88 */       List<Map> list = getAppListCmd.getAppInfosCompleteSearch(i, str1);
/*  89 */       for (byte b = 0; b < list.size(); b++) {
/*  90 */         Map map = list.get(b);
/*  91 */         if (!Util.null2String(map.get("id")).equals(str2)) {
/*  92 */           arrayList.add(list.get(b));
/*     */         }
/*     */       } 
/*  95 */       getAppListCmd.sortList(arrayList);
/*  96 */     } catch (Exception exception) {
/*  97 */       this.formmodeLog.writeLog(exception);
/*     */     } 
/*     */     
/* 100 */     hashMap1.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 101 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/ModeAppTreeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */