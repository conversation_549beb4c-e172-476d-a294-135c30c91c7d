/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LocationBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  30 */     String str1 = Util.null2String(paramMap.get("locationname"));
/*  31 */     String str2 = Util.null2String(paramMap.get("locationdesc"));
/*  32 */     String str3 = Util.null2String(paramMap.get("address"));
/*  33 */     String str4 = " where 1 = 1 ";
/*  34 */     if (!str1.equals("")) {
/*  35 */       str4 = str4 + " and locationname like '%";
/*  36 */       str4 = str4 + Util.fromScreen2(str1, this.user.getLanguage());
/*  37 */       str4 = str4 + "%'";
/*     */     } 
/*  39 */     if (!str2.equals("")) {
/*  40 */       str4 = str4 + " and locationdesc like '%";
/*  41 */       str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/*  42 */       str4 = str4 + "%'";
/*     */     } 
/*  44 */     if (!str3.equals("")) {
/*  45 */       str4 = str4 + " and (address1 like '%";
/*  46 */       str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/*  47 */       str4 = str4 + "%')";
/*     */     } 
/*     */     
/*  50 */     String str5 = " id ,locationname,locationdesc,address1 ";
/*  51 */     String str6 = " HrmLocations ";
/*  52 */     String str7 = " showOrder,id ";
/*     */     
/*  54 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  55 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(84, this.user.getLanguage()), "id", "id"));
/*  56 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "locationname", "locationname")).setIsInputCol(BoolAttr.TRUE));
/*  57 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(15767, this.user.getLanguage()), "locationdesc", "locationdesc"));
/*  58 */     arrayList.add(new SplitTableColBean("35%", SystemEnv.getHtmlLabelName(110, this.user.getLanguage()), "address1", "address1"));
/*     */     
/*  60 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, str7, "id", arrayList);
/*  61 */     splitTableBean.setSqlsortway("ASC");
/*  62 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  63 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  70 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  71 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  72 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 399, "locationname", true));
/*  73 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 15767, "locationdesc"));
/*  74 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 110, "address"));
/*  75 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  76 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  81 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  82 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  83 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  84 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/*  85 */     RecordSet recordSet = new RecordSet();
/*  86 */     String str2 = "select id,locationname,locationdesc from HrmLocations where id in (" + str1 + ")";
/*  87 */     recordSet.executeQuery(str2, new Object[0]);
/*  88 */     while (recordSet.next()) {
/*  89 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  90 */       hashMap1.put("id", recordSet.getString("id"));
/*  91 */       hashMap1.put("locationname", Util.null2String(recordSet.getString("locationname")));
/*  92 */       hashMap1.put("locationdesc", Util.null2String(recordSet.getString("locationdesc")));
/*  93 */       arrayList.add(hashMap1);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 107 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 108 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 109 */     arrayList1.add(new ListHeadBean("locationname", "", 1, BoolAttr.TRUE));
/* 110 */     arrayList1.add(new ListHeadBean("locationdesc", ""));
/*     */     
/* 112 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 113 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 114 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 115 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/LocationBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */