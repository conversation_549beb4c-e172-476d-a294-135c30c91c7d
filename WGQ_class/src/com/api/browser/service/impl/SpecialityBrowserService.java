/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.ListHeadBean;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.BrowserDataType;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import com.api.browser.util.SqlUtils;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SpecialityBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     String str1 = Util.null2String(paramMap.get("name"));
/* 31 */     String str2 = Util.null2String(paramMap.get("description"));
/* 32 */     String str3 = " ";
/* 33 */     if (!str1.equals("")) {
/* 34 */       str3 = str3 + " and name like '%";
/* 35 */       str3 = str3 + Util.fromScreen2(str1, 7);
/* 36 */       str3 = str3 + "%'";
/*    */     } 
/* 38 */     if (!str2.equals("")) {
/* 39 */       str3 = str3 + " and description like '%";
/* 40 */       str3 = str3 + Util.fromScreen2(str2, 7);
/* 41 */       str3 = str3 + "%'";
/*    */     } 
/* 43 */     str3 = SqlUtils.replaceFirstAnd(str3);
/*    */     
/* 45 */     String str4 = " id ,name,description ";
/* 46 */     String str5 = " HrmSpeciality ";
/*    */     
/* 48 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 49 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 50 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "name", "name")).setIsInputCol(BoolAttr.TRUE));
/* 51 */     arrayList.add(new SplitTableColBean("70%", SystemEnv.getHtmlLabelName(433, this.user.getLanguage()), "description", "description"));
/*    */     
/* 53 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, "id", "id", arrayList);
/* 54 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 55 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 61 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 62 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 63 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 64 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "name", true));
/* 65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 433, "description"));
/* 66 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 72 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 73 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 74 */     if (this.user == null || "".equals(str1)) return (Map)hashMap; 
/* 75 */     RecordSet recordSet = new RecordSet();
/* 76 */     String str2 = "select id,name,description from HrmSpeciality where id in (" + str1 + ")";
/* 77 */     recordSet.executeQuery(str2, new Object[0]);
/* 78 */     while (recordSet.next()) {
/* 79 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 80 */       hashMap1.put("id", recordSet.getString("id"));
/* 81 */       hashMap1.put("name", Util.null2String(recordSet.getString("name")));
/* 82 */       hashMap1.put("description", Util.null2String(recordSet.getString("description")));
/* 83 */       arrayList.add(hashMap1);
/*    */     } 
/*    */     
/* 86 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 87 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 88 */     arrayList1.add(new ListHeadBean("name", "", 1, BoolAttr.TRUE));
/* 89 */     arrayList1.add(new ListHeadBean("description", ""));
/*    */     
/* 91 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 92 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, arrayList);
/* 93 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 94 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/SpecialityBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */