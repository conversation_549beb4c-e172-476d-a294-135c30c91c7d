/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MatrixFieldInfoBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  37 */     String str1 = Util.null2String(paramMap.get("displayname"));
/*  38 */     int i = Util.getIntValue(Util.null2String(paramMap.get("matrixid")), -1);
/*     */ 
/*     */     
/*  41 */     String str2 = "where fieldtype='1' and matrixid = " + i + " ";
/*     */     
/*  43 */     if (!str1.equals("")) {
/*  44 */       str2 = str2 + " and displayname like '%";
/*  45 */       str2 = str2 + Util.fromScreen2(str1, this.user.getLanguage());
/*  46 */       str2 = str2 + "%'";
/*     */     } 
/*     */ 
/*     */     
/*  50 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  51 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  52 */     arrayList.add((new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "displayname", "", 1)).setIsInputCol(BoolAttr.TRUE));
/*     */ 
/*     */     
/*  55 */     String str3 = "id,displayname";
/*  56 */     String str4 = "MatrixFieldInfo";
/*  57 */     String str5 = "id";
/*  58 */     SplitTableBean splitTableBean = new SplitTableBean(str3, str4, str2, str5, "id", "asc", arrayList);
/*  59 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  60 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  66 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  67 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  68 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  69 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  70 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 685, "displayname", true));
/*  71 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  76 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  77 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("matrixid"));
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     String str3 = " where fieldtype='1' and matrixid = " + str2 + " ";
/*  80 */     if (!"".equals(str1)) {
/*  81 */       str3 = str3 + " and (displayname like '%" + str1 + "%' ";
/*  82 */       if (!ConnectionPool.getInstance().isNewDB())
/*     */       {
/*  84 */         if ("oracle".equalsIgnoreCase(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/*  85 */           str3 = str3 + " or f_GetPy(displayname) like '%" + str1.toUpperCase() + "%'";
/*  86 */         } else if ("sqlserver".equals(recordSet.getDBType())) {
/*  87 */           str3 = str3 + " or [dbo].f_GetPy(displayname) like '%" + str1.toUpperCase() + "%'";
/*  88 */         } else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/*  89 */           str3 = str3 + " or getpinyin(displayname) like '%" + str1.toUpperCase() + "%'";
/*     */         }  } 
/*  91 */       str3 = str3 + ")";
/*     */     } 
/*  93 */     recordSet.executeQuery("select id,displayname from MatrixFieldInfo " + str3 + " order by id", new Object[0]);
/*  94 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  95 */     while (recordSet.next()) {
/*  96 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  97 */       hashMap1.put("id", recordSet.getString("id"));
/*  98 */       hashMap1.put("name", recordSet.getString("displayname"));
/*  99 */       arrayList.add(hashMap1);
/*     */     } 
/* 101 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 102 */     hashMap.put("datas", arrayList);
/* 103 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MatrixFieldInfoBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */