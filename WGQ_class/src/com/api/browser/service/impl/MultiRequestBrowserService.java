/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.service.BrowserService;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.fna.budget.FnaTableNameEnum;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.maintenance.FnaSplitTableByYearComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.workflow.request.MailAndMessage;
/*     */ import weaver.workflow.request.RequestBaseUtil;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MultiRequestBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  33 */     String str1 = Util.null2String(paramMap.get("f_weaver_belongto_userid"));
/*  34 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  36 */     String str2 = String.valueOf(this.user.getUID());
/*  37 */     if (str1 != null && !str1.equals(str2) && !"".equals(str1)) {
/*  38 */       str2 = str1;
/*     */     }
/*  40 */     RecordSet recordSet1 = new RecordSet();
/*  41 */     RecordSet recordSet2 = new RecordSet();
/*  42 */     if (this.user == null) {
/*  43 */       return (Map)hashMap1;
/*     */     }
/*  45 */     int i = this.user.getLanguage();
/*  46 */     String str3 = Util.null2String(paramMap.get("systemIds"));
/*  47 */     if (str3.startsWith(",")) {
/*  48 */       str3 = str3.substring(1);
/*     */     }
/*  50 */     String str4 = Util.null2String(paramMap.get("src"));
/*     */ 
/*     */     
/*  53 */     String str5 = "";
/*  54 */     int j = Util.getIntValue(Util.null2String(paramMap.get("__requestid")));
/*  55 */     int k = Util.getIntValue(Util.null2String(paramMap.get("fna_wfid")));
/*  56 */     int m = Util.getIntValue(Util.null2String(paramMap.get("fna_fieldid")));
/*  57 */     boolean bool1 = false;
/*  58 */     HashMap hashMap = FnaCommon.getIsEnableFnaWfHm(k);
/*  59 */     bool1 = "true".equals(hashMap.get("isEnableFnaWfE8"));
/*  60 */     if (!bool1) {
/*  61 */       HashMap hashMap3 = FnaCommon.getIsEnableFnaRepaymentWfHm(k);
/*  62 */       bool1 = "true".equals(hashMap3.get("isEnableFnaRepaymentWf"));
/*     */     } 
/*  64 */     boolean bool2 = false;
/*     */     
/*  66 */     if (bool1) {
/*  67 */       bool2 = FnaCommon.checkFnaWfFieldFnaType(k, m, 2, 0, "fnaFeeWf");
/*     */     }
/*  69 */     if (bool2) {
/*     */       
/*  71 */       String str26 = "";
/*  72 */       recordSet1.executeQuery(" select workflowid from fnaFeeWfInfo where enable = 1 and fnaWfType = 'fnaFeeWf' ", new Object[0]);
/*  73 */       while (recordSet1.next()) {
/*  74 */         String str = Util.null2String(recordSet1.getString("workflowid"));
/*  75 */         if (!"".equals(str26)) {
/*  76 */           str26 = str26 + ",";
/*     */         }
/*  78 */         str26 = str26 + str;
/*     */       } 
/*  80 */       String str27 = WorkflowVersion.getAllVersionStringByWFIDs(str26);
/*  81 */       if ("".equals(str27)) {
/*  82 */         str27 = "-1";
/*     */       }
/*     */ 
/*     */       
/*  86 */       String str28 = "ISNULL";
/*  87 */       if ("oracle".equals(recordSet1.getDBType())) {
/*  88 */         str28 = "NVL";
/*     */       }
/*     */ 
/*     */       
/*  92 */       str5 = str5 + " and ( ";
/*     */       
/*  94 */       ArrayList<String> arrayList1 = new ArrayList();
/*  95 */       FnaSplitTableByYearComInfo.getFnaTableNameList(FnaTableNameEnum.FnaExpenseInfo, "1000-01-01", "9999-12-31", arrayList1);
/*  96 */       for (byte b = 0; b < arrayList1.size(); b++) {
/*  97 */         String str = arrayList1.get(b);
/*     */         
/*  99 */         if (b > 0) {
/* 100 */           str5 = str5 + " or ";
/*     */         }
/* 102 */         str5 = str5 + " exists (  select 1  from " + str + " fei  where fei.budgetperiodslist is not null  and fei.sourceRequestid <> " + j + "  and fei.status = 0  and fei.requestid = a.requestid  GROUP BY fei.organizationid, fei.organizationtype, fei.subject, fei.budgetperiods, fei.budgetperiodslist   HAVING SUM(" + str28 + "(fei.amount, 0.00)) > 0.00  )  ";
/*     */       } 
/*     */ 
/*     */       
/* 106 */       str5 = str5 + " ) ";
/*     */       
/* 108 */       str5 = str5 + " and a.currentnodetype = 3 ";
/* 109 */       str5 = str5 + " and a.workflowid in (" + str27 + ") ";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 114 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 115 */     HashMap<Object, Object> hashMap2 = null;
/* 116 */     int n = -1;
/* 117 */     int i1 = -1;
/* 118 */     if (str3.trim().startsWith(",")) {
/* 119 */       str3 = str3.substring(1);
/*     */     }
/* 121 */     String str6 = Util.null2String(paramMap.get("excludeId"));
/* 122 */     if (str6.startsWith(",")) {
/* 123 */       str6 = str6.substring(1);
/*     */     }
/* 125 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 126 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 127 */     RequestBaseUtil requestBaseUtil = new RequestBaseUtil();
/* 128 */     if ("dest".equalsIgnoreCase(str4)) {
/* 129 */       if (!"".equals(str3)) {
/* 130 */         String str = "select requestid,creatertype,requestname, requestnamenew,creater,createdate,createtime from workflow_requestbase where requestid in (" + str3 + ")";
/* 131 */         recordSet1.executeSql(str);
/* 132 */         while (recordSet1.next()) {
/* 133 */           String str26 = Util.null2String(recordSet1.getString("requestid"));
/* 134 */           String str27 = Util.null2String(recordSet1.getString("creater"));
/* 135 */           String str28 = Util.null2String(recordSet1.getString("createdate"));
/* 136 */           String str29 = Util.null2String(recordSet1.getString("createtime"));
/* 137 */           String str30 = Util.null2String(recordSet1.getString("requestname"));
/* 138 */           String str31 = Util.null2String(recordSet1.getString("requestnamenew"));
/*     */           
/* 140 */           String str32 = "";
/* 141 */           recordSet2.execute("select workflowid from workflow_requestbase where requestid = " + str26);
/* 142 */           if (recordSet2.next()) {
/* 143 */             str32 = Util.null2String(recordSet2.getString("workflowid"));
/*     */           }
/*     */ 
/*     */           
/* 147 */           recordSet2.execute("select formid,isbill from workflow_base where id=" + str32);
/* 148 */           if (recordSet2.next()) {
/* 149 */             n = recordSet2.getInt(1);
/* 150 */             i1 = recordSet2.getInt(2);
/*     */           } 
/*     */           
/* 153 */           str30 = requestBaseUtil.formatRequestname(str30, str32 + "", str26, i1, n, i);
/*     */           
/* 155 */           String str33 = Util.null2String(recordSet1.getString("createtype"));
/* 156 */           if ("1".equals(str33)) {
/* 157 */             str27 = customerInfoComInfo.getCustomerInfoname(str27);
/*     */           } else {
/* 159 */             str27 = resourceComInfo.getResourcename(str27);
/*     */           } 
/* 161 */           hashMap2 = new HashMap<>();
/* 162 */           hashMap2.put("requestid", str26);
/* 163 */           hashMap2.put("requestname", str30);
/* 164 */           hashMap2.put("creater", str27);
/* 165 */           hashMap2.put("createtime", str28 + " " + str29);
/* 166 */           arrayList.add(hashMap2);
/*     */         } 
/*     */       } 
/* 169 */       hashMap1.put("currentPage", Integer.valueOf(1));
/* 170 */       hashMap1.put("totalPage", Integer.valueOf(1));
/* 171 */       hashMap1.put("mapList", arrayList);
/* 172 */       return (Map)hashMap1;
/*     */     } 
/* 174 */     String str7 = Util.null2String(paramMap.get("createdatestart"));
/* 175 */     String str8 = Util.null2String(paramMap.get("createdateend"));
/* 176 */     String str9 = Util.null2String(paramMap.get("requestmark"));
/* 177 */     String str10 = Util.null2String(paramMap.get("prjids"));
/* 178 */     String str11 = Util.null2String(paramMap.get("crmids"));
/* 179 */     String str12 = Util.null2String(paramMap.get("isfrom"));
/* 180 */     String str13 = Util.null2String(paramMap.get("workflowid"));
/* 181 */     if (str13.equals("") || str13.equals("0")) {
/* 182 */       str13 = Util.null2String(paramMap.get("flowReport_flowId"));
/*     */     }
/*     */     
/* 185 */     String str14 = Util.null2String(paramMap.get("currworkflowid"));
/*     */     
/* 187 */     String str15 = Util.null2String(paramMap.get("department"));
/* 188 */     String str16 = Util.null2String(paramMap.get("subid"));
/* 189 */     if (str15.startsWith(",")) {
/* 190 */       str15 = str15.substring(1);
/*     */     }
/* 192 */     String str17 = Util.null2String(paramMap.get("fieldid"));
/* 193 */     String str18 = "";
/* 194 */     int i2 = 0;
/* 195 */     i2 = Util.getIntValue(Util.null2String(paramMap.get("date2during")), 0);
/* 196 */     String str19 = "select gdtype,jsqjtype from workflow_rquestBrowseFunction WHERE workflowid=" + str14 + " AND fieldid=" + str17;
/* 197 */     recordSet1.executeSql(str19);
/* 198 */     if (recordSet1.next()) {
/* 199 */       str18 = recordSet1.getString(1);
/* 200 */       if (i2 == 0)
/* 201 */         i2 = recordSet1.getInt(2); 
/*     */     } 
/* 203 */     String str20 = Util.null2String(paramMap.get("status"));
/* 204 */     if ("".equals(str20)) {
/* 205 */       str20 = str18;
/*     */     }
/* 207 */     String str21 = Util.null2String(paramMap.get("requestname"));
/* 208 */     String str22 = Util.null2String(paramMap.get("creater"));
/* 209 */     if (str22.startsWith(",")) {
/* 210 */       str22 = str22.substring(1);
/*     */     }
/*     */     
/* 213 */     String str23 = "0";
/*     */     
/* 215 */     if (this.user.getLogintype().equals("2")) {
/* 216 */       str23 = "1";
/*     */     }
/* 218 */     if (str22.trim().startsWith(",")) {
/* 219 */       str22 = str22.substring(1);
/*     */     }
/* 221 */     StringBuffer stringBuffer = new StringBuffer();
/* 222 */     if (!str21.equals("")) {
/* 223 */       stringBuffer.append(" and a.requestnamenew like '%" + Util.fromScreen2(str21, this.user.getLanguage()) + "%' ");
/*     */     }
/* 225 */     if (!str22.equals("")) {
/* 226 */       stringBuffer.append(" and a.creater in (" + str22 + ") and a.creatertype=0 ");
/*     */     }
/*     */     
/* 229 */     if (!str7.equals("")) {
/* 230 */       stringBuffer.append(" and a.createdate >='" + str7 + "' ");
/*     */     }
/*     */     
/* 233 */     if (!str8.equals("")) {
/* 234 */       stringBuffer.append(" and a.createdate <='" + str8 + "' ");
/*     */     }
/*     */     
/* 237 */     if (str20.equals("1")) {
/* 238 */       stringBuffer.append(" and a.currentnodetype < 3 ");
/*     */     }
/*     */     
/* 241 */     if (str20.equals("2")) {
/* 242 */       stringBuffer.append(" and a.currentnodetype = 3 ");
/*     */     }
/*     */     
/* 245 */     if (!str13.equals("") && !str13.equals("0")) {
/* 246 */       if (!str13.equals("-999")) {
/* 247 */         stringBuffer.append(" and a.workflowid in ( " + WorkflowVersion.getAllVersionStringByWFIDs(str13) + ")");
/*     */       } else {
/* 249 */         stringBuffer.append(" and a.workflowid in (" + str13 + ")");
/*     */       } 
/*     */     }
/*     */     
/* 253 */     if (!str15.equals("") && !str15.equals("0")) {
/* 254 */       stringBuffer.append(" and a.creater in (select id from hrmresource where departmentid in (" + str15 + "))");
/*     */     }
/*     */     
/* 257 */     if (!"".equals(str16) && !"0".equals(str16) && !"flowrpt".equals(str12)) {
/* 258 */       stringBuffer.append(" and a.creater in (select id from hrmresource where subcompanyid1 in (" + str16 + "))");
/*     */     }
/*     */     
/* 261 */     if (!"".equals(str10) && !"0".equals(str10)) {
/* 262 */       String[] arrayOfString = str10.split(",");
/* 263 */       if (arrayOfString.length > 0) {
/* 264 */         stringBuffer.append(" AND (");
/* 265 */         if ("oracle".equals(recordSet1.getDBType())) {
/* 266 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 267 */             if (b > 0) {
/* 268 */               stringBuffer.append(" OR ");
/*     */             }
/* 270 */             stringBuffer.append("(concat(concat(',' , To_char(a.prjids)) , ',') LIKE '%," + arrayOfString[b] + ",%')");
/*     */           } 
/* 272 */         } else if ("mysql".equals(recordSet1.getDBType())) {
/* 273 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 274 */             if (b > 0) {
/* 275 */               stringBuffer.append(" OR ");
/*     */             }
/* 277 */             stringBuffer.append("(concat(',' , CONVERT(a.prjids , char) , ',') LIKE '%," + arrayOfString[b] + ",%')");
/*     */           }
/*     */         
/* 280 */         } else if ("postgresql".equals(recordSet1.getDBType())) {
/* 281 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 282 */             if (b > 0) {
/* 283 */               stringBuffer.append(" OR ");
/*     */             }
/* 285 */             stringBuffer.append("(concat(concat(',' , To_char(a.prjids)) , ',') LIKE '%," + arrayOfString[b] + ",%')");
/*     */           } 
/*     */         } else {
/*     */           
/* 289 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 290 */             if (b > 0) {
/* 291 */               stringBuffer.append(" OR ");
/*     */             }
/* 293 */             stringBuffer.append("(',' + CONVERT(varchar,a.prjids) + ',' LIKE '%," + arrayOfString[b] + ",%')");
/*     */           } 
/*     */         } 
/* 296 */         stringBuffer.append(") ");
/*     */       } 
/*     */     } 
/*     */     
/* 300 */     if (!"".equals(str11) && !"0".equals(str11)) {
/* 301 */       String[] arrayOfString = str11.split(",");
/* 302 */       if (arrayOfString.length > 0) {
/* 303 */         stringBuffer.append(" AND (");
/* 304 */         if ("oracle".equals(recordSet1.getDBType())) {
/* 305 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 306 */             if (b > 0) {
/* 307 */               stringBuffer.append(" OR ");
/*     */             }
/* 309 */             stringBuffer.append("(concat(concat(',' , To_char(a.crmids)) , ',') LIKE '%," + arrayOfString[b] + ",%')");
/*     */           } 
/* 311 */         } else if ("mysql".equals(recordSet1.getDBType())) {
/* 312 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 313 */             if (b > 0) {
/* 314 */               stringBuffer.append(" OR ");
/*     */             }
/* 316 */             stringBuffer.append("(concat(',' , CONVERT(a.crmids, char) , ',') LIKE '%," + arrayOfString[b] + ",%')");
/*     */           }
/*     */         
/* 319 */         } else if ("postgresql".equals(recordSet1.getDBType())) {
/* 320 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 321 */             if (b > 0) {
/* 322 */               stringBuffer.append(" OR ");
/*     */             }
/* 324 */             stringBuffer.append("(concat(concat(',' , To_char(a.crmids)) , ',') LIKE '%," + arrayOfString[b] + ",%')");
/*     */           } 
/*     */         } else {
/*     */           
/* 328 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 329 */             if (b > 0) {
/* 330 */               stringBuffer.append(" OR ");
/*     */             }
/* 332 */             stringBuffer.append("(',' + CONVERT(varchar,a.crmids) + ',' LIKE '%," + arrayOfString[b] + ",%')");
/*     */           } 
/*     */         } 
/* 335 */         stringBuffer.append(") ");
/*     */       } 
/*     */     } 
/*     */     
/* 339 */     if (!str9.equals("")) {
/* 340 */       stringBuffer.append(" and a.requestmark like '%" + str9 + "%' ");
/*     */     }
/*     */     
/* 343 */     if (stringBuffer.equals("")) {
/* 344 */       stringBuffer.append(" and a.requestid <> 0 ");
/*     */     }
/* 346 */     if (recordSet1.getDBType().equals("oracle")) {
/* 347 */       if (!"flowrpt".equals(str12)) {
/* 348 */         stringBuffer.append(" and (nvl(a.currentstatus,-1) = -1 or (nvl(a.currentstatus,-1)=0 and a.creater=" + this.user.getUID() + ")) ");
/*     */       }
/*     */     }
/* 351 */     else if (!"flowrpt".equals(str12)) {
/* 352 */       stringBuffer.append(" and (isnull(a.currentstatus,-1) = -1 or (isnull(a.currentstatus,-1)=0 and a.creater=" + this.user.getUID() + ")) ");
/*     */     } 
/*     */     
/* 355 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 356 */     stringBuffer.append(workflowComInfo.getDateDuringSql(i2));
/*     */     
/* 358 */     stringBuffer.append(" and b.requestid = a.requestid ");
/* 359 */     if (!"flowrpt".equals(str12)) {
/* 360 */       stringBuffer.append(" and b.userid in (" + str2 + ")");
/*     */     }
/* 362 */     stringBuffer.append(" and b.usertype=" + str23 + " and a.workflowid = c.id and c.isvalid in ('1','3') and (c.istemplate is null or c.istemplate<>'1')");
/*     */     
/* 364 */     stringBuffer.append(" and islasttimes=1 ");
/*     */     
/* 366 */     stringBuffer.append(str5);
/*     */     
/* 368 */     String str24 = " order by createdate desc, createtime desc";
/* 369 */     String str25 = "";
/* 370 */     int i3 = Util.getIntValue(Util.null2String(paramMap.get("pageSize")), 10);
/* 371 */     int i4 = Util.getIntValue(Util.null2String(paramMap.get("currentPage")), 1);
/*     */     
/* 373 */     if (recordSet1.getDBType().equals("oracle")) {
/*     */       
/* 375 */       str25 = "select * from (select row_number() over(order by createdate desc, createtime desc) rn,a.requestid,a.requestname,a.creater,a.createdate,a.createtime,a.creatertype from workflow_requestbase a, workflow_currentoperator b, workflow_base c  where 1=1 " + stringBuffer.toString().replaceAll("receivedate", "createdate") + " ) where rn > " + ((i4 - 1) * i3) + " and rn <=" + (i4 * i3) + str24;
/*     */     }
/*     */     else {
/*     */       
/* 379 */       str25 = "select top " + i3 + " a.requestid,a.requestname,a.creater,a.createdate,a.createtime,a.creatertype from workflow_requestbase a, workflow_currentoperator b, workflow_base c  where a.requestid not in (select top " + ((i4 - 1) * i3) + " a.requestid from workflow_requestbase a where 1=1 " + stringBuffer.toString().replaceAll("receivedate", "createdate") + str24 + ")" + stringBuffer.toString().replaceAll("receivedate", "createdate") + str24;
/*     */     } 
/*     */     
/* 382 */     recordSet1.executeSql(str25);
/* 383 */     MailAndMessage mailAndMessage = new MailAndMessage();
/* 384 */     while (recordSet1.next()) {
/* 385 */       String str26 = Util.null2String(recordSet1.getString("requestid"));
/* 386 */       String str27 = Util.null2String(recordSet1.getString("creater"));
/* 387 */       String str28 = Util.null2String(recordSet1.getString("createdate"));
/* 388 */       String str29 = Util.null2String(recordSet1.getString("createtime"));
/* 389 */       String str30 = Util.null2String(recordSet1.getString("requestname"));
/* 390 */       String str31 = Util.null2String(recordSet1.getString("creatertype"));
/* 391 */       recordSet2.execute("select workflowid from workflow_requestbase where requestid = " + str26);
/* 392 */       if (recordSet2.next()) {
/* 393 */         str13 = Util.null2String(recordSet1.getString("workflowid"));
/*     */       }
/*     */ 
/*     */       
/* 397 */       recordSet2.execute("select formid,isbill from workflow_base where id=" + str13);
/* 398 */       if (recordSet2.next()) {
/* 399 */         n = recordSet2.getInt(1);
/* 400 */         i1 = recordSet2.getInt(2);
/*     */       } 
/*     */       
/* 403 */       String str32 = mailAndMessage.getTitle(Util.getIntValue(str26, -1), Util.getIntValue(str13, -1), n, this.user.getLanguage(), i1);
/* 404 */       if (!str32.equals(""))
/* 405 */         str30 = str30 + "<B>(" + str32 + ")</B>"; 
/* 406 */       if ("1".equals(str31)) {
/* 407 */         str27 = customerInfoComInfo.getCustomerInfoname(str27);
/*     */       } else {
/* 409 */         str27 = resourceComInfo.getResourcename(str27);
/*     */       } 
/* 411 */       hashMap2 = new HashMap<>();
/* 412 */       hashMap2.put("requestid", str26);
/* 413 */       hashMap2.put("requestname", str30);
/* 414 */       hashMap2.put("creater", str27);
/* 415 */       hashMap2.put("createtime", str28 + " " + str29);
/* 416 */       arrayList.add(hashMap2);
/*     */     } 
/* 418 */     hashMap1.put("currentPage", Integer.valueOf(i4));
/* 419 */     hashMap1.put("datas", arrayList);
/* 420 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/MultiRequestBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */