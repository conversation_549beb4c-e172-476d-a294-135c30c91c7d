/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ public class HrmTrainLayoutBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 24 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 25 */     String str1 = Util.null2String(paramMap.get("trainLayoutName"));
/* 26 */     String str2 = Util.null2String(paramMap.get("trainLayoutContent"));
/* 27 */     String str3 = Util.null2String(paramMap.get("trainLayoutAim"));
/*    */     
/* 29 */     String str4 = " HrmTrainLayout.id,layoutname,layoutcontent,layoutaim ";
/* 30 */     String str5 = " from HrmTrainLayout,HrmTrainType ";
/* 31 */     String str6 = " where HrmTrainLayout.typeid=HrmTrainType.id ";
/* 32 */     if (!str1.equals("")) {
/* 33 */       str6 = str6 + " and layoutname like '%";
/* 34 */       str6 = str6 + Util.fromScreen2(str1, this.user.getLanguage());
/* 35 */       str6 = str6 + "%'";
/*    */     } 
/* 37 */     if (!str2.equals("")) {
/* 38 */       str6 = str6 + " and layoutcontent like '%";
/* 39 */       str6 = str6 + Util.fromScreen2(str2, this.user.getLanguage());
/* 40 */       str6 = str6 + "%'";
/*    */     } 
/* 42 */     if (!str3.equals("")) {
/* 43 */       str6 = str6 + " and layoutaim like '%";
/* 44 */       str6 = str6 + Util.fromScreen2(str3, this.user.getLanguage());
/* 45 */       str6 = str6 + "%'";
/*    */     } 
/*    */     
/* 48 */     RecordSet recordSet = new RecordSet();
/* 49 */     if (recordSet.getDBType().equalsIgnoreCase("Oracle")) {
/* 50 */       str6 = str6 + " and ','||HrmTrainType.typeoperator||',' like '%," + this.user.getUID() + ",%' ";
/* 51 */     } else if (recordSet.getDBType().equalsIgnoreCase("mysql")) {
/* 52 */       str6 = str6 + " and CONCAT(',',HrmTrainType.typeoperator,',') like '%," + this.user.getUID() + ",%' ";
/*    */     }
/* 54 */     else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 55 */       str6 = str6 + " and ','||HrmTrainType.typeoperator||',' like '%," + this.user.getUID() + ",%' ";
/*    */     } else {
/*    */       
/* 58 */       str6 = str6 + " and ','+HrmTrainType.typeoperator+',' like '%," + this.user.getUID() + ",%' ";
/*    */     } 
/* 60 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 61 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 62 */     arrayList.add((new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "layoutname", null)).setIsInputCol(BoolAttr.TRUE));
/* 63 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(345, this.user.getLanguage()), "layoutcontent", null));
/* 64 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(16142, this.user.getLanguage()), "layoutaim", null));
/*    */     
/* 66 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, Util.toHtmlForSplitPage(str6), "id", "id", arrayList);
/* 67 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 68 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 73 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 74 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 75 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 76 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "trainLayoutName", true));
/* 77 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 345, "trainLayoutContent", false));
/* 78 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 16142, "trainLayoutAim", false));
/* 79 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 80 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/HrmTrainLayoutBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */