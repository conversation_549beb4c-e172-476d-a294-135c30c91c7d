/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BelongAttr;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.MobileViewTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.meeting.MeetingUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkPlanTypeBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  46 */     String str1 = Util.null2String(paramMap.get("workplantypename"));
/*  47 */     String str2 = "";
/*  48 */     String str3 = "";
/*  49 */     String str4 = "";
/*  50 */     String str5 = " where 1=1 ";
/*  51 */     if (!"".equalsIgnoreCase(str1)) {
/*  52 */       str5 = str5 + " and " + MeetingUtil.getMultiLangSql("workplantypename", this.user.getLanguage() + "") + " like '%" + str1 + "%' ";
/*     */     }
/*     */     
/*  55 */     str2 = " workPlanTypeID as id,workplantypename ";
/*  56 */     str3 = " WorkPlanType ";
/*  57 */     str4 = "displayorder, workPlanTypeID ";
/*  58 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  59 */     arrayList.add((new SplitTableColBean("true", "id")).setIsPrimarykey(BoolAttr.TRUE));
/*  60 */     arrayList.add((new SplitTableColBean("100%", SystemEnv.getHtmlLabelName(16094, this.user.getLanguage()), "workplantypename", "workplantypename", 1)).setIsInputCol(BoolAttr.TRUE).setBelong(BelongAttr.PCMOBILE).setMobileviewtype(MobileViewTypeAttr.HIGHLIGHT));
/*     */     
/*  62 */     SplitTableBean splitTableBean = new SplitTableBean(str2, str3, str5, str4, "id", arrayList);
/*  63 */     splitTableBean.setSqlsortway("ASC");
/*     */ 
/*     */     
/*  66 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     
/*     */     try {
/*  69 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(MobileJsonConfigUtil.addKey(null, "col1.col1_row1.workplantypename")));
/*  70 */     } catch (Exception exception) {
/*  71 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  74 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  75 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  85 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  87 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  88 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  89 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 19774, "workplantypename", true));
/*  90 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  91 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  98 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  99 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 100 */     if ("".equals(str)) return (Map)hashMap; 
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     recordSet.executeSql("select * from WorkPlanType where workPlanTypeID in (" + str + ") order by displayorder, workPlanTypeID asc");
/* 103 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 104 */     while (recordSet.next()) {
/* 105 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 107 */       hashMap1.put("id", recordSet.getString("workPlanTypeID"));
/* 108 */       hashMap1.put("idspan", recordSet.getString("workPlanTypeID"));
/* 109 */       hashMap1.put("randomFieldId", recordSet.getString("workPlanTypeID"));
/* 110 */       hashMap1.put("randomFieldIdspan", recordSet.getString("workPlanTypeID"));
/* 111 */       hashMap1.put("workplantypename", recordSet.getString("workplantypename"));
/* 112 */       hashMap1.put("workplantypenamespan", recordSet.getString("workplantypename"));
/* 113 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 116 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/*     */     
/* 118 */     arrayList1.add((new ListHeadBean("id", BoolAttr.TRUE)).setIsPrimarykey(BoolAttr.TRUE));
/* 119 */     arrayList1.add(new ListHeadBean("workplantypename", SystemEnv.getHtmlLabelName(16094, this.user.getLanguage()), 1, BoolAttr.TRUE));
/*     */     
/* 121 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 122 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 123 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 124 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 129 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 130 */     String str1 = paramHttpServletRequest.getParameter("q");
/* 131 */     RecordSet recordSet = new RecordSet();
/* 132 */     String str2 = "select workplantypeid,workplantypename from WorkPlanType  where ";
/* 133 */     str2 = str2 + MeetingUtil.getMultiLangSql("workplantypename", this.user.getLanguage() + "") + " like '%" + str1 + "%' ";
/* 134 */     recordSet.executeQuery(str2, new Object[0]);
/* 135 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 136 */     while (recordSet.next()) {
/* 137 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 138 */       hashMap1.put("id", recordSet.getString(1));
/* 139 */       hashMap1.put("name", recordSet.getString(2));
/* 140 */       arrayList.add(hashMap1);
/*     */     } 
/* 142 */     hashMap.put("datas", arrayList);
/* 143 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkPlanTypeBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */