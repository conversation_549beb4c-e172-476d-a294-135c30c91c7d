/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CountryBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 35 */     String str1 = Util.null2String(paramMap.get("sqlwhere"));
/* 36 */     String str2 = Util.null2String(paramMap.get("name"));
/* 37 */     String str3 = Util.null2String(paramMap.get("countrydesc"));
/* 38 */     String str4 = " ";
/* 39 */     boolean bool = false;
/* 40 */     if (!str1.equals("") && 
/* 41 */       !bool) {
/* 42 */       bool = true;
/* 43 */       str4 = str4 + str1;
/*    */     } 
/*    */     
/* 46 */     if (bool == true) {
/* 47 */       str4 = str4 + " and (canceled is null or canceled = 0) ";
/*    */     } else {
/* 49 */       bool = true;
/* 50 */       str4 = str4 + " where (canceled is null or canceled = 0) ";
/*    */     } 
/* 52 */     if (!str2.equals("")) {
/* 53 */       if (!bool) {
/* 54 */         bool = true;
/* 55 */         str4 = str4 + " where countryname like '%";
/* 56 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/* 57 */         str4 = str4 + "%'";
/*    */       } else {
/* 59 */         str4 = str4 + " and countryname like '%";
/* 60 */         str4 = str4 + Util.fromScreen2(str2, this.user.getLanguage());
/* 61 */         str4 = str4 + "%'";
/*    */       } 
/*    */     }
/* 64 */     if (!str3.equals("")) {
/* 65 */       if (!bool) {
/* 66 */         bool = true;
/* 67 */         str4 = str4 + " where countrydesc like '%";
/* 68 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/* 69 */         str4 = str4 + "%'";
/*    */       } else {
/* 71 */         str4 = str4 + " and countrydesc like '%";
/* 72 */         str4 = str4 + Util.fromScreen2(str3, this.user.getLanguage());
/* 73 */         str4 = str4 + "%'";
/*    */       } 
/*    */     }
/* 76 */     String str5 = "id,countryname,countrydesc";
/* 77 */     String str6 = "HrmCountry";
/*    */     
/* 79 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 80 */     arrayList.add(new SplitTableColBean("hide", "id"));
/* 81 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(399, this.user.getLanguage()), "countryname", "countryname")).setIsInputCol(BoolAttr.TRUE));
/* 82 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(377, this.user.getLanguage()), "countrydesc", "countrydesc"));
/*    */     
/* 84 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str4, "id", "id", arrayList);
/* 85 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 86 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/CountryBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */