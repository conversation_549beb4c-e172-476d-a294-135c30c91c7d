/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class KnowledgePointBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  33 */     String str1 = Util.null2String(paramMap.get("name"));
/*  34 */     String str2 = Util.null2String(paramMap.get("createrId"));
/*  35 */     String str3 = Util.null2String(paramMap.get("sqlwhere"));
/*  36 */     if ("".equals(str3)) {
/*  37 */       str3 = str3 + " where t1.deleted=0 ";
/*     */     } else {
/*  39 */       str3 = str3 + " t1.deleted=0 ";
/*     */     } 
/*  41 */     if (!str1.equals("")) {
/*  42 */       str3 = str3 + " and t1.name like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */     }
/*  44 */     if (!str2.equals("")) {
/*  45 */       str3 = str3 + " and t1.createrId in( " + str2 + ")";
/*     */     }
/*  47 */     String str4 = "t1.id,t1.name,t1.createrId,t1.createDate ";
/*  48 */     String str5 = " KT_Knowledge t1 " + str3 + " ";
/*     */     
/*  50 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*  51 */     arrayList.add(new SplitTableColBean("true", "id"));
/*  52 */     arrayList.add((new SplitTableColBean("45%", SystemEnv.getHtmlLabelName(27915, this.user.getLanguage()), "name", "name", 1)).setIsInputCol(BoolAttr.TRUE));
/*  53 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(882, this.user.getLanguage()), "createrId", "createrId", "weaver.hrm.resource.ResourceComInfo.getResourcename", 0));
/*  54 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(1339, this.user.getLanguage()), "createDate", "createDate", 2));
/*     */     
/*  56 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, "", "t1.id", "t1.id", arrayList);
/*  57 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*  58 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/*  63 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  64 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/*  65 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  66 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 27915, "name", true));
/*  67 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 882, "createrId", "17"));
/*  68 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/*  69 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  73 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*  74 */     RecordSet recordSet = new RecordSet();
/*  75 */     String str2 = " where t1.deleted=0 ";
/*  76 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("sqlwhere"));
/*  77 */     if (!str3.equals("")) {
/*  78 */       str2 = str2 + str3;
/*     */     }
/*  80 */     if (!"".equals(str1)) {
/*  81 */       str2 = str2 + " and name like '%" + str1 + "%' ";
/*     */     }
/*  83 */     recordSet.executeQuery("select t1.id, t1.name from KT_Knowledge t1 " + str2 + " order by id desc", new Object[0]);
/*  84 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  85 */     while (recordSet.next()) {
/*  86 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  87 */       hashMap1.put("id", recordSet.getString("id"));
/*  88 */       hashMap1.put("name", recordSet.getString("name"));
/*  89 */       arrayList.add(hashMap1);
/*     */     } 
/*  91 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  92 */     hashMap.put("datas", arrayList);
/*  93 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/*  97 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  98 */     String str = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/*  99 */     if ("".equals(str)) return (Map)hashMap; 
/* 100 */     RecordSet recordSet = new RecordSet();
/* 101 */     recordSet.execute("select tt1.id, tt1.name,tt1.createrId,tt1.createDate from KT_Knowledge tt1  where tt1.id in (" + str + ") and tt1.deleted=0");
/* 102 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 103 */     while (recordSet.next()) {
/* 104 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 105 */       hashMap1.put("id", recordSet.getString("id"));
/* 106 */       hashMap1.put("name", recordSet.getString("name"));
/* 107 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 110 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 111 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 112 */     arrayList1.add(new ListHeadBean("name", SystemEnv.getHtmlLabelName(27915, this.user.getLanguage()), 1, BoolAttr.TRUE));
/*     */     
/* 114 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 115 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str, "id"));
/* 116 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 117 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/KnowledgePointBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */