/*     */ package com.api.browser.service.impl;
/*     */ 
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaDataSetBrowserService
/*     */   extends BrowserService
/*     */ {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  43 */     if (this.user == null) {
/*  44 */       hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, null);
/*  45 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  48 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_SPLIT_DATA.getTypeid()));
/*  49 */     SplitTableBean splitTableBean = getTableList(paramMap);
/*  50 */     if (splitTableBean == null) {
/*  51 */       ArrayList<ListHeadBean> arrayList = new ArrayList();
/*  52 */       arrayList.add(new ListHeadBean("datasetName", SystemEnv.getHtmlLabelName(388995, this.user.getLanguage())));
/*  53 */       arrayList.add(new ListHeadBean("datasourcename", SystemEnv.getHtmlLabelName(18076, this.user.getLanguage())));
/*  54 */       hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/*  55 */       hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList);
/*     */     } else {
/*  57 */       hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*     */     } 
/*     */ 
/*     */     
/*  61 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SplitTableBean getTableList(Map<String, Object> paramMap) {
/*  74 */     String str1 = Util.null2String(paramMap.get("datasetName"));
/*  75 */     String str2 = Util.null2String(paramMap.get("accountId"));
/*  76 */     String str3 = Util.null2String(paramMap.get("preType"));
/*  77 */     String str4 = "10";
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     StringBuffer stringBuffer = new StringBuffer();
/*  80 */     if (!"".equals(str2)) {
/*  81 */       recordSet.executeQuery("select id from FnaMulDatasSet where datasetType = 0 and accountId <> ? ", new Object[] { str2 });
/*  82 */       while (recordSet.next()) {
/*  83 */         String str = Util.null2String(recordSet.getString("id"));
/*  84 */         if (stringBuffer.length() > 0) {
/*  85 */           stringBuffer.append(",");
/*     */         }
/*  87 */         stringBuffer.append("'").append(str).append("'");
/*     */       } 
/*     */     } 
/*     */     
/*  91 */     if ("synMode".equals(str3)) {
/*  92 */       recordSet.executeQuery("select a.mainId from FnaDsPlaceHolder a join FnaMulDatasSet b on a.mainId = b.id where b.datasetType = 1 ", new Object[0]);
/*  93 */       while (recordSet.next()) {
/*  94 */         String str = Util.null2String(recordSet.getString("mainId"));
/*  95 */         if (stringBuffer.length() > 0) {
/*  96 */           stringBuffer.append(",");
/*     */         }
/*  98 */         stringBuffer.append("'").append(str).append("'");
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 105 */     String str5 = " a.id,a.datasetName,a.datasourcename,a.displayOrder, a.accountId,b.accountName,a.sourceSql,a.approvalId,  case when(datasetType=1) then '" + SystemEnv.getHtmlLabelName(126462, this.user.getLanguage()) + "' else '" + SystemEnv.getHtmlLabelName(389012, this.user.getLanguage()) + "' end datasetType ";
/* 106 */     String str6 = " FnaMulDatasSet a   left join FnaAccountInfo b on a.accountId=b.id";
/* 107 */     String str7 = " where 1=1 ";
/*     */     
/* 109 */     if (!"".equals(str1)) {
/* 110 */       str7 = str7 + " and a.datasetName like '%" + StringEscapeUtils.escapeSql(str1) + "%' ";
/*     */     }
/*     */     
/* 113 */     if (stringBuffer.length() > 0) {
/* 114 */       str7 = str7 + " and a.id not in (" + stringBuffer.toString() + ")";
/*     */     }
/* 116 */     if ("synMode".equals(str3)) {
/* 117 */       str7 = str7 + " and datasetType = 1 ";
/*     */     }
/*     */     
/* 120 */     String str8 = "a.displayOrder,a.datasetName,a.id";
/* 121 */     String str9 = "a.id";
/*     */     
/* 123 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 124 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 125 */     arrayList.add((new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(388995, this.user.getLanguage()), "datasetName", "datasetName")).setIsInputCol(BoolAttr.TRUE));
/* 126 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(389006, this.user.getLanguage()), "datasetType", "datasetType"));
/* 127 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(507130, this.user.getLanguage()), "accountName", "accountName", "com.engine.fnaMulDimensions.util.FnaMulDatasSetUtil.getAccountNameOrlocalName", "column:datasourcename"));
/*     */     
/* 129 */     arrayList.add(new SplitTableColBean("25%", SystemEnv.getHtmlLabelName(507131, this.user.getLanguage()), "sourceSql", "sourceSql", "com.engine.fnaMulDimensions.util.FnaMulDatasSetUtil.getApprovalOrSql", "column:approvalId+column:accountId"));
/*     */     
/* 131 */     return new SplitTableBean("FnadataSetbrowserList", "none", str4, "FnadataSetbrowserList", str5, str6, str7, str8, str9, "ASC", arrayList);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/FnaDataSetBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */