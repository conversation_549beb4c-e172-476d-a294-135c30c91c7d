/*     */ package com.api.browser.service.impl;
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.ListHeadBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.bean.SplitTableBean;
/*     */ import com.api.browser.bean.SplitTableColBean;
/*     */ import com.api.browser.service.BrowserService;
/*     */ import com.api.browser.util.BoolAttr;
/*     */ import com.api.browser.util.BrowserBaseUtil;
/*     */ import com.api.browser.util.BrowserConstant;
/*     */ import com.api.browser.util.BrowserDataType;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.browser.util.MobileJsonConfigUtil;
/*     */ import com.api.browser.util.MobileShowTypeAttr;
/*     */ import com.api.browser.util.SplitTableUtil;
/*     */ import com.cloudstore.dev.api.bean.SplitMobileDataBean;
/*     */ import com.engine.workrelate.constant.GoalCommonTypeEnum;
/*     */ import com.engine.workrelate.util.GoalUtil;
/*     */ import com.engine.workrelate.util.WorkrelateUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class WorkrelateGoalBrowserService extends BrowserService {
/*     */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/*  36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  38 */     String str1 = Util.null2String(paramMap.get("goalname"));
/*  39 */     String str2 = Util.null2String(paramMap.get("enddate"));
/*  40 */     String str3 = Util.null2String(paramMap.get("principalid"));
/*  41 */     String str4 = Util.null2String(paramMap.get("parentid"));
/*  42 */     String str5 = Util.null2String(paramMap.get("parentids"));
/*  43 */     String str6 = Util.null2String(paramMap.get("sqlwhere"));
/*  44 */     String str7 = Util.null2String(paramMap.get("goalType"));
/*  45 */     String str8 = Util.null2String(paramMap.get("typeId"));
/*  46 */     String str9 = Util.null2String(paramMap.get("orgId"));
/*  47 */     String str10 = Util.null2String(paramMap.get("secretLevel"));
/*  48 */     String str11 = Util.null2String(paramMap.get("isParent"));
/*  49 */     String str12 = Util.null2String(paramMap.get("checkId"));
/*  50 */     String str13 = Util.null2String(paramMap.get("isOrgGoal"));
/*  51 */     if (StringUtils.equals(str13, "true") && StringUtils.equals(str7, "1") && StringUtils.isNotBlank(str12)) {
/*  52 */       str7 = "2";
/*  53 */       Map map = GoalUtil.getOrgMap(str12);
/*  54 */       str8 = (String)map.get("typeId");
/*  55 */       str9 = (String)map.get("orgId");
/*     */     } 
/*  57 */     String str14 = GoalUtil.getTableName(str4, str7);
/*  58 */     WorkrelateUtil workrelateUtil = new WorkrelateUtil();
/*  59 */     if ("".equals(str6)) {
/*  60 */       str6 = str6 + " where (t1.deleted=0 or t1.deleted is null) ";
/*     */     } else {
/*  62 */       str6 = str6 + " (t1.deleted=0 or t1.deleted is null) ";
/*     */     } 
/*  64 */     if (!str10.equals("")) {
/*  65 */       if (StringUtils.equals("1", str11)) {
/*  66 */         str6 = str6 + " and (t1.secretlev <= " + str10 + " or t1.secretlev is null) ";
/*     */       } else {
/*  68 */         str6 = str6 + " and (t1.secretlev >= " + str10 + " or t1.secretlev is null) ";
/*     */       } 
/*     */     }
/*  71 */     if (!str1.equals("")) {
/*  72 */       str6 = str6 + " and t1.name like '%" + Util.fromScreen2(str1, this.user.getLanguage()) + "%' ";
/*     */     }
/*  74 */     if (!str2.equals("")) {
/*  75 */       str6 = str6 + " and t1.enddate <= '" + str2 + "'";
/*     */     }
/*  77 */     if (!str5.equals("")) {
/*  78 */       str6 = str6 + " and t1.parentid='" + str5 + "'";
/*     */     }
/*  80 */     if (!str3.equals("")) {
/*  81 */       str6 = str6 + " and t1.principalid='" + str3 + "'";
/*     */     }
/*     */     
/*  84 */     if (StringUtils.isNotBlank(str4) && !StringUtils.equals("0", str4)) {
/*  85 */       str6 = str6 + " and t1.id not in (select id from " + str14 + " t2 where t2.parentid = '" + str4 + "') and t1.id <> " + str4;
/*     */     }
/*     */     
/*  88 */     if (StringUtils.isNotBlank(str4) && StringUtils.equals("0", str4) && 
/*  89 */       StringUtils.isNotBlank(str8) && StringUtils.equals(str14, GoalCommonTypeEnum.orgtable.getCode())) {
/*  90 */       str6 = str6 + getOrgSqlWhere(Util.getIntValue(str8, 1));
/*     */     }
/*  92 */     int i = ((Integer)GoalUtil.getGoalMaint("" + this.user.getUID()).get("right")).intValue();
/*  93 */     if (StringUtils.equals(str14, GoalCommonTypeEnum.persontable.getCode())) {
/*  94 */       if (i != 2) {
/*  95 */         if (!str4.equals(""))
/*     */         {
/*  97 */           str6 = str6 + " and (t1.principalid=" + this.user.getUID() + " or t1.creater=" + this.user.getUID() + " or exists (select 1 from GM_GoalPartner tp where tp.goalid=t1.id and tp.partnerid=" + this.user.getUID() + "))";
/*     */ 
/*     */         
/*     */         }
/*     */         else
/*     */         {
/*     */ 
/*     */           
/* 105 */           str6 = str6 + " and (t1.principalid=" + this.user.getUID() + " or t1.creater=" + this.user.getUID() + " or exists (select 1 from GM_GoalPartner tp where tp.goalid=t1.id and tp.partnerid=" + this.user.getUID() + ") or exists (select 1 from GM_GoalSharer ts where ts.goalid=t1.id and ts.sharerid=" + this.user.getUID() + ") or exists (select 1 from HrmResource hrm where (hrm.id=t1.principalid or hrm.id=t1.creater) and hrm.managerstr like '%," + this.user.getUID() + ",%') or exists (select 1 from HrmResource hrm,GM_GoalPartner tp where tp.goalid=t1.id and hrm.id=tp.partnerid and hrm.managerstr like '%," + this.user.getUID() + ",%') or exists(" + workrelateUtil.getWorkrelateShareSql(this.user.getUID() + "", "1", "1") + " and t1.id = ws.bizid ))";
/*     */         }
/*     */       
/*     */       }
/*     */     }
/* 110 */     else if (i != 2) {
/* 111 */       str6 = str6 + "and exists (" + workrelateUtil.getWorkrelateShareSql("" + this.user.getUID(), "1", "1", str8, str9) + " and t1.id = ws.bizid)";
/*     */     } 
/*     */     
/* 114 */     String str15 = "t1.id,t1.name goalname,t1.principalid,t1.begindate,t1.enddate";
/*     */ 
/*     */ 
/*     */     
/* 118 */     String str16 = str14 + " t1 " + str6 + " ";
/*     */     
/* 120 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 121 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 122 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(18238, this.user.getLanguage()), "goalname", "id", 1)).setIsInputCol(BoolAttr.TRUE));
/* 123 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(2097, this.user.getLanguage()), "principalid", "principalid", "weaver.hrm.resource.ResourceComInfo.getResourcename", 0));
/* 124 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(741, this.user.getLanguage()), "enddate", "enddate", 0));
/*     */     
/* 126 */     SplitTableBean splitTableBean = new SplitTableBean(str15, str16, "", "t1.id", "t1.id", arrayList);
/*     */     
/* 128 */     splitTableBean.setMobileshowtype(MobileShowTypeAttr.ListView);
/*     */     try {
/* 130 */       splitTableBean.createMobileTemplate(MobileJsonConfigUtil.getSplitMobileTemplateBean(getJonsConfig()));
/* 131 */     } catch (Exception exception) {
/* 132 */       exception.printStackTrace();
/*     */     } 
/* 134 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 135 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<SplitMobileDataBean> getJonsConfig() {
/* 142 */     ArrayList<SplitMobileDataBean> arrayList = new ArrayList();
/* 143 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row1.goalname");
/* 144 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.principalid");
/* 145 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 146 */     hashMap.put("marginLeft", "20px");
/* 147 */     MobileJsonConfigUtil.addKey(arrayList, "col1.col1_row2.enddate", hashMap);
/* 148 */     return arrayList;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 152 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 153 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 154 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 155 */     String str = Util.null2String(paramMap.get("showType"));
/*     */     
/* 157 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18238, "goalname", true));
/* 158 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 2097, "principalid", "1"));
/*     */     
/* 160 */     BrowserBean browserBean = new BrowserBean();
/* 161 */     browserBean.setType("wrlgoal");
/* 162 */     browserBean.setIcon("icon-coms-implement");
/* 163 */     browserBean.setIconBgcolor("rgb(150, 53, 138)");
/* 164 */     browserBean.setTitle(SystemEnv.getHtmlLabelName(19709, this.user.getLanguage()));
/* 165 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 19709, "parentids", browserBean));
/* 166 */     arrayList.add(conditionFactory.createCondition(ConditionType.DATEPICKER, 741, "enddate"));
/*     */     
/* 168 */     if (StringUtils.isBlank(str) || StringUtils.equals("0", str)) {
/* 169 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/* 170 */       arrayList1.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(526298, this.user.getLanguage()), true));
/* 171 */       arrayList1.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(506155, this.user.getLanguage())));
/* 172 */       arrayList.add(conditionFactory.createCondition(ConditionType.SELECT, 125569, "goalType", arrayList1));
/*     */     } 
/*     */     
/* 175 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 176 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 180 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 181 */     RecordSet recordSet = new RecordSet();
/* 182 */     String str2 = " where (t1.deleted=0 or t1.deleted is null) ";
/* 183 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("sqlwhere"));
/* 184 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("parentid"));
/* 185 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("goalType"));
/* 186 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("secretLevel"));
/* 187 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("isParent"));
/* 188 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("checkId"));
/* 189 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("isOrgGoal"));
/* 190 */     if (StringUtils.equals(str9, "true") && StringUtils.equals(str5, "1") && StringUtils.isNotBlank(str8)) {
/* 191 */       str5 = "2";
/*     */     }
/* 193 */     String str10 = GoalUtil.getTableName(str4, str5);
/* 194 */     if (!str3.equals("")) {
/* 195 */       str2 = str2 + str3;
/*     */     }
/* 197 */     if (!str6.equals("")) {
/* 198 */       if (StringUtils.equals("1", str7)) {
/* 199 */         str2 = str2 + " and (t1.secretlev <= " + str6 + " or t1.secretlev is null) ";
/*     */       } else {
/* 201 */         str2 = str2 + " and (t1.secretlev >= " + str6 + " or t1.secretlev is null) ";
/*     */       } 
/*     */     }
/* 204 */     if (!"".equals(str1)) {
/* 205 */       str2 = str2 + " and name like '%" + str1 + "%' ";
/*     */     }
/* 207 */     if (!str4.equals("")) {
/* 208 */       str2 = str2 + " and t1.id not in (select id from " + str10 + " t2 where t2.parentid = '" + str4 + "') and t1.id <> " + str4;
/*     */     }
/* 210 */     if (((Integer)GoalUtil.getGoalMaint("" + this.user.getUID()).get("right")).intValue() != 2) {
/* 211 */       if (!str4.equals("")) {
/*     */         
/* 213 */         str2 = str2 + " and (t1.principalid=" + this.user.getUID() + " or t1.creater=" + this.user.getUID() + " or exists (select 1 from GM_GoalPartner tp where tp.goalid=t1.id and tp.partnerid=" + this.user.getUID() + "))";
/*     */ 
/*     */       
/*     */       }
/*     */       else {
/*     */ 
/*     */         
/* 220 */         str2 = str2 + " and (t1.principalid=" + this.user.getUID() + " or t1.creater=" + this.user.getUID() + " or exists (select 1 from GM_GoalPartner tp where tp.goalid=t1.id and tp.partnerid=" + this.user.getUID() + ") or exists (select 1 from GM_GoalSharer ts where ts.goalid=t1.id and ts.sharerid=" + this.user.getUID() + ") or exists (select 1 from HrmResource hrm where (hrm.id=t1.principalid or hrm.id=t1.creater) and hrm.managerstr like '%," + this.user.getUID() + ",%') or exists (select 1 from HrmResource hrm,GM_GoalPartner tp where tp.goalid=t1.id and hrm.id=tp.partnerid and hrm.managerstr like '%," + this.user.getUID() + ",%'))";
/*     */       } 
/*     */     }
/*     */     
/* 224 */     recordSet.executeQuery("select t1.id, t1.name from " + str10 + " t1 " + str2 + " order by id desc", new Object[0]);
/* 225 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 226 */     while (recordSet.next()) {
/* 227 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 228 */       hashMap1.put("id", recordSet.getString("id"));
/* 229 */       hashMap1.put("name", recordSet.getString("name"));
/* 230 */       arrayList.add(hashMap1);
/*     */     } 
/* 232 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 233 */     hashMap.put("datas", arrayList);
/* 234 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getMultBrowserDestData(Map<String, Object> paramMap) throws Exception {
/* 238 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 239 */     String str1 = Util.null2String(paramMap.get(BrowserConstant.BROWSER_MULT_DEST_SELECTIDS));
/* 240 */     if ("".equals(str1)) return (Map)hashMap; 
/* 241 */     RecordSet recordSet = new RecordSet();
/* 242 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 243 */     String str2 = Util.null2String(paramMap.get("parentid"));
/* 244 */     String str3 = Util.null2String(paramMap.get("goalType"));
/* 245 */     String str4 = GoalUtil.getTableName(str2, str3);
/* 246 */     recordSet.execute("select tt1.id, tt1.name goalname,tt1.begindate,tt1.enddate,tt1.principalid from " + str4 + " tt1  where tt1.id in (" + str1 + ") and (tt1.deleted=0 or tt1.deleted is null)");
/* 247 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 248 */     while (recordSet.next()) {
/* 249 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 250 */       hashMap1.put("id", recordSet.getString("id"));
/* 251 */       hashMap1.put("goalname", recordSet.getString("goalname"));
/* 252 */       hashMap1.put("enddate", recordSet.getString("enddate"));
/* 253 */       hashMap1.put("principalid", resourceComInfo.getLastname(recordSet.getString("principalid")));
/* 254 */       arrayList.add(hashMap1);
/*     */     } 
/*     */     
/* 257 */     ArrayList<ListHeadBean> arrayList1 = new ArrayList();
/* 258 */     arrayList1.add(new ListHeadBean("id", BoolAttr.TRUE));
/* 259 */     arrayList1.add(new ListHeadBean("goalname", SystemEnv.getHtmlLabelName(18238, this.user.getLanguage()), 1, BoolAttr.TRUE));
/* 260 */     arrayList1.add(new ListHeadBean("principalid", SystemEnv.getHtmlLabelName(2097, this.user.getLanguage()), 0, BoolAttr.FALSE));
/* 261 */     arrayList1.add(new ListHeadBean("enddate", SystemEnv.getHtmlLabelName(741, this.user.getLanguage()), 0, BoolAttr.FALSE));
/*     */     
/* 263 */     hashMap.put(BrowserConstant.BROWSER_RESULT_COLUMN, arrayList1);
/* 264 */     hashMap.put(BrowserConstant.BROWSER_RESULT_DATA, BrowserBaseUtil.sortDatas(arrayList, str1, "id"));
/* 265 */     hashMap.put(BrowserConstant.BROWSER_RESULT_TYPE, Integer.valueOf(BrowserDataType.LIST_ALL_DATA.getTypeid()));
/* 266 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getOrgSqlWhere(int paramInt) {
/* 275 */     String str = "";
/* 276 */     if (paramInt == 1) {
/* 277 */       str = " and t1.typeid = 1 ";
/* 278 */     } else if (paramInt == 2) {
/* 279 */       str = " and (t1.typeid = 1 or t1.typeid = 2)";
/*     */     } else {
/* 281 */       str = " and (t1.typeid = 1 or t1.typeid = 2 or t1.typeid = 3)";
/*     */     } 
/* 283 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/WorkrelateGoalBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */