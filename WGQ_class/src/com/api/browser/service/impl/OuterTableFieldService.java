/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OuterTableFieldService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 35 */     String str1 = Util.null2String(paramMap.get("outTableName"));
/* 36 */     String str2 = Util.null2String(paramMap.get("datasourceName"));
/*    */     
/* 38 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 39 */     String str3 = " o.object_id = c.object_id and c.system_type_id = t.system_type_id  and o.name = 'E20190321153813_dt1' ";
/* 40 */     String str4 = "c.name AS id,t.name as value,c.max_length as length";
/* 41 */     String str5 = "sys.tables AS o , sys.columns AS c, sys.types AS t";
/* 42 */     String str6 = "c.column_id";
/* 43 */     String str7 = "select " + str4 + "from " + str5 + str3;
/* 44 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 45 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(685, this.user.getLanguage()), "id", "id")).setIsInputCol(BoolAttr.TRUE));
/* 46 */     arrayList.add(new SplitTableColBean("60%", SystemEnv.getHtmlLabelName(686, this.user.getLanguage()), "value", "value"));
/*    */     
/* 48 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/* 49 */     String str8 = "com.api.workflow.util.OutDataSource.getDataSource";
/*    */     
/* 51 */     splitTableBean.setDatasource(str8);
/* 52 */     splitTableBean.setSourceparams("datasourceName:" + str2 + "+tablename:" + str1);
/* 53 */     splitTableBean.setSqlsortway("ASC");
/* 54 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/*    */     
/* 56 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/OuterTableFieldService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */