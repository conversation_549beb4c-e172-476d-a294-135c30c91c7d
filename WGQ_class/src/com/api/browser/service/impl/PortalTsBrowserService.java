/*    */ package com.api.browser.service.impl;
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class PortalTsBrowserService extends BrowserService {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 22 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 23 */     String str1 = Util.null2String(paramMap.get("layoutType"));
/* 24 */     String str2 = Util.null2String(paramMap.get("id"));
/* 25 */     String str3 = Util.null2String(paramMap.get("layoutName"));
/* 26 */     String str4 = Util.null2String(paramMap.get("layoutTitle"));
/*    */     
/* 28 */     String str5 = " id,layout_name,layout_title ";
/* 29 */     String str6 = " hp_grid_layout ";
/* 30 */     String str7 = " where (layout_delete is null or layout_delete = '' or layout_delete = '0') ";
/* 31 */     if (!"".equals(str1)) {
/* 32 */       str7 = str7 + " and layout_type ='" + str1 + "' ";
/*    */     }
/* 34 */     if (!"".equals(str2)) {
/* 35 */       str7 = str7 + " and id =" + str2 + " ";
/*    */     }
/* 37 */     if (!"".equals(str3)) {
/* 38 */       str7 = str7 + " and layout_name like '%" + str3 + "%' ";
/*    */     }
/* 40 */     if (!"".equals(str4)) {
/* 41 */       str7 = str7 + " and layout_title like '%" + str4 + "%' ";
/*    */     }
/* 43 */     String str8 = " id ";
/*    */ 
/*    */     
/* 46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 47 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 48 */     arrayList.add(new SplitTableColBean("20%", SystemEnv.getHtmlLabelName(32011, this.user.getLanguage()), "id", "id"));
/* 49 */     arrayList.add((new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(18151, this.user.getLanguage()), "layout_name", "layout_name")).setIsInputCol(BoolAttr.TRUE));
/* 50 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(18795, this.user.getLanguage()), "layout_title", "layout_title"));
/*    */     
/* 52 */     SplitTableBean splitTableBean = new SplitTableBean(str5, str6, str7, str8, "id", "desc", arrayList);
/* 53 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 54 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 59 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 61 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 62 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 63 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18151, "layoutName", true));
/* 64 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 18795, "layoutTitle", false));
/* 65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 32011, "id", false));
/*    */     
/* 67 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 68 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> browserAutoComplete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 73 */     RecordSet recordSet = new RecordSet();
/* 74 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 75 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 76 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("layoutType"));
/* 77 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/* 78 */     recordSet.executeQuery("select id,layout_name from hp_grid_layout where (layout_delete is null or layout_delete = '' or layout_delete = '0') and layout_type = ? and layout_name like '%" + str2 + "%' ", new Object[] { str1 });
/* 79 */     while (recordSet.next()) {
/* 80 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 81 */       String str3 = recordSet.getString("id");
/* 82 */       String str4 = recordSet.getString("layout_name");
/* 83 */       hashMap1.put("id", str3);
/* 84 */       hashMap1.put("name", str4);
/* 85 */       arrayList.add(hashMap1);
/*    */     } 
/* 87 */     hashMap.put("datas", arrayList);
/* 88 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/PortalTsBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */