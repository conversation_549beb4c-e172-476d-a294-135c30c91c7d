/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PageBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */     String str1 = Util.null2String(paramMap.get("id"));
/* 31 */     String str2 = Util.null2String(paramMap.get("infoname"));
/* 32 */     String str3 = Util.null2String(paramMap.get("subcompanyid"));
/* 33 */     String str4 = Util.null2String(paramMap.get("hpcreatorid"));
/*    */     
/* 35 */     RecordSet recordSet = new RecordSet();
/* 36 */     String str5 = " where 1=1";
/* 37 */     if (!"".equals(str1)) str5 = str5 + " and id=" + str1; 
/* 38 */     if (!"".equals(str3)) str5 = str5 + " and subcompanyid=" + str3; 
/* 39 */     if (!"".equals(str4)) str5 = str5 + " and hpcreatorid=" + str4; 
/* 40 */     if (!"".equals(str2)) str5 = str5 + " and infoname like '%" + str2 + "%'"; 
/* 41 */     String str6 = " ";
/* 42 */     str6 = "id,infoname,subcompanyid,isuse,islocked,creatorid,hpcreatorid,hplanuageid,hplastdate,isRedirectUrl ";
/*    */     
/* 44 */     String str7 = " from hpinfo ";
/*    */     
/* 46 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 47 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 48 */     arrayList.add((new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "infoname", "infoname", 1)).setIsInputCol(BoolAttr.TRUE));
/* 49 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()), "subcompanyid", "subcompanyid", "weaver.hrm.company.SubCompanyComInfo.getSubCompanyname"));
/* 50 */     arrayList.add(new SplitTableColBean("40%", SystemEnv.getHtmlLabelName(882, this.user.getLanguage()), "hpcreatorid", "hpcreatorid", "com.api.portal.backend.util.SptmForHomepage.getPortalCreator"));
/* 51 */     SplitTableBean splitTableBean = new SplitTableBean(str6, str7, str5, "id", "id", arrayList);
/* 52 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 64 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 65 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 66 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 67 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "infoname", true));
/* 68 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 17868, "subcompanyid", "164"));
/* 69 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, "", "id"));
/* 70 */     arrayList.add(conditionFactory.createCondition(ConditionType.BROWSER, 882, "hpcreatorid", "1"));
/* 71 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 72 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/PageBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */