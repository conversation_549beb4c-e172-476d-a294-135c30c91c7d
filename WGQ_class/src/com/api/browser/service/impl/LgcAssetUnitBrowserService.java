/*    */ package com.api.browser.service.impl;
/*    */ 
/*    */ import com.api.browser.bean.SearchConditionItem;
/*    */ import com.api.browser.bean.SplitTableBean;
/*    */ import com.api.browser.bean.SplitTableColBean;
/*    */ import com.api.browser.service.BrowserService;
/*    */ import com.api.browser.util.BoolAttr;
/*    */ import com.api.browser.util.BrowserConstant;
/*    */ import com.api.browser.util.ConditionFactory;
/*    */ import com.api.browser.util.ConditionType;
/*    */ import com.api.browser.util.SplitTableUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LgcAssetUnitBrowserService
/*    */   extends BrowserService
/*    */ {
/*    */   public Map<String, Object> getBrowserData(Map<String, Object> paramMap) throws Exception {
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     String str1 = Util.null2String(paramMap.get("unitname"));
/* 32 */     String str2 = Util.null2String(paramMap.get("unitdesc"));
/* 33 */     String str3 = " where 1 = 1 ";
/* 34 */     if (!str1.equals("")) {
/* 35 */       str3 = str3 + " and unitname like '%";
/* 36 */       str3 = str3 + Util.fromScreen2(str1, this.user.getLanguage());
/* 37 */       str3 = str3 + "%'";
/*    */     } 
/* 39 */     if (!str2.equals("")) {
/* 40 */       str3 = str3 + " and unitdesc like '%";
/* 41 */       str3 = str3 + Util.fromScreen2(str2, this.user.getLanguage());
/* 42 */       str3 = str3 + "%'";
/*    */     } 
/* 44 */     String str4 = " id,unitname,unitdesc ";
/* 45 */     String str5 = "LgcAssetUnit";
/* 46 */     String str6 = " id ";
/*    */     
/* 48 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/* 49 */     arrayList.add(new SplitTableColBean("true", "id"));
/* 50 */     arrayList.add((new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(195, this.user.getLanguage()), "unitname", "unitname")).setIsInputCol(BoolAttr.TRUE));
/* 51 */     arrayList.add(new SplitTableColBean("50%", SystemEnv.getHtmlLabelName(85, this.user.getLanguage()), "unitdesc", "unitdesc"));
/*    */ 
/*    */     
/* 54 */     SplitTableBean splitTableBean = new SplitTableBean(str4, str5, str3, str6, "id", arrayList);
/* 55 */     splitTableBean.setSqlsortway("ASC");
/* 56 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getBrowserConditionInfo(Map<String, Object> paramMap) throws Exception {
/* 62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 63 */     ArrayList<SearchConditionItem> arrayList = new ArrayList();
/* 64 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 65 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 195, "unitname", true));
/* 66 */     arrayList.add(conditionFactory.createCondition(ConditionType.INPUT, 85, "unitdesc"));
/* 67 */     hashMap.put(BrowserConstant.BROWSER_RESULT_CONDITIONS, arrayList);
/* 68 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/browser/service/impl/LgcAssetUnitBrowserService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */