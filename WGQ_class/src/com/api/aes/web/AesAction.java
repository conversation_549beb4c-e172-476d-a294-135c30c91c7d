/*    */ package com.api.aes.web;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.Produces;
/*    */ import javax.ws.rs.core.Context;
/*    */ import net.sf.json.JSONObject;
/*    */ import weaver.aes.AES;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Path("/aes")
/*    */ public class AesAction
/*    */ {
/*    */   @Path("/aesInfo")
/*    */   @POST
/*    */   @Produces({"text/plain"})
/*    */   public String getLicenseInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 36 */     JSONObject jSONObject = new JSONObject();
/* 37 */     AES aES = new AES();
/* 38 */     jSONObject.put("aesStatus", Boolean.valueOf(aES.isEnable()));
/* 39 */     jSONObject.put("urlPattern", aES.getUrlPattern());
/* 40 */     return jSONObject.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/aes/web/AesAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */