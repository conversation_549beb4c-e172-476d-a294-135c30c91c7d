/*     */ package com.api.car.service;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.car.util.CarUtil;
/*     */ import com.api.meeting.util.FieldUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.car.CarTypeComInfo;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarBackgroundService
/*     */   extends BaseBean
/*     */ {
/*     */   private User user;
/*  29 */   private int language = 7;
/*     */   
/*     */   private HttpServletRequest request;
/*     */   private HttpServletResponse response;
/*     */   
/*     */   public CarBackgroundService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  35 */     init(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void init(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  45 */     this.request = paramHttpServletRequest;
/*  46 */     this.response = paramHttpServletResponse;
/*  47 */     this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  48 */     if (this.user != null) {
/*  49 */       this.language = this.user.getLanguage();
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarApplySetting() {
/*  58 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  59 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*  60 */     int i = 0;
/*  61 */     int j = 0;
/*  62 */     byte b = 2;
/*  63 */     int k = 0;
/*  64 */     int m = 23;
/*  65 */     int n = 1;
/*  66 */     int i1 = 0;
/*  67 */     String str1 = "E3F6D8";
/*  68 */     String str2 = "FB3D17";
/*  69 */     RecordSet recordSet = new RecordSet();
/*  70 */     String str3 = "select isremind,remindtype,timeRangeStart,timeRangeEnd,dspUnit,usedColor,conflictedColor,isshare from mode_carremindset";
/*  71 */     recordSet.execute(str3);
/*  72 */     if (recordSet.next()) {
/*  73 */       i = recordSet.getInt("isremind");
/*  74 */       j = recordSet.getInt("remindtype");
/*  75 */       k = Util.getIntValue(recordSet.getString("timeRangeStart"), 0);
/*  76 */       m = Util.getIntValue(recordSet.getString("timeRangeEnd"), 23);
/*  77 */       n = Util.getIntValue(recordSet.getString("dspUnit"), 1);
/*  78 */       str1 = Util.null2String(recordSet.getString("usedColor"));
/*  79 */       str2 = Util.null2String(recordSet.getString("conflictedColor"));
/*  80 */       i1 = Util.getIntValue(recordSet.getString("isshare"), 0);
/*     */     } 
/*  82 */     hashMap1.put("isremind", Integer.valueOf(i));
/*  83 */     hashMap1.put("remindtype", Integer.valueOf(j));
/*  84 */     hashMap1.put("isshare", Integer.valueOf(i1));
/*  85 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  86 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  87 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*  88 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(10000229, this.language));
/*  89 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*     */     
/*  91 */     SearchConditionItem searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(ConditionType.SWITCH, 128287, "isremind"), 6, 12);
/*  92 */     arrayList1.add(searchConditionItem);
/*     */     
/*  94 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*  95 */     arrayList2.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(128289, this.user.getLanguage())));
/*  96 */     arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(128290, this.user.getLanguage())));
/*  97 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(ConditionType.SELECT, 128288, "remindtype", arrayList2), 6, 12);
/*  98 */     arrayList1.add(searchConditionItem);
/*  99 */     hashMap2.put("items", arrayList1);
/* 100 */     arrayList.add(hashMap2);
/*     */     
/* 102 */     hashMap2 = new HashMap<>();
/* 103 */     ArrayList<Map> arrayList3 = new ArrayList();
/* 104 */     arrayList3.add(FieldUtil.getFormItemForPeriod(new String[] { "timeRangeStart", "timeRangeEnd" }, SystemEnv.getHtmlLabelName(124949, this.user.getLanguage()), new int[] { k, m }, new int[] { 0, 23 }));
/* 105 */     arrayList2 = new ArrayList<>();
/* 106 */     arrayList2.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(124951, this.language)));
/* 107 */     arrayList2.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(124952, this.language)));
/* 108 */     arrayList3.add(FieldUtil.getFormItemForSelect("dspUnit", SystemEnv.getHtmlLabelName(124950, this.user.getLanguage()), "" + n, b, 3, arrayList2));
/* 109 */     arrayList3.add(FieldUtil.getFormItemForColorPicker("usedColor", SystemEnv.getHtmlLabelName(19097, this.user.getLanguage()), "#" + str1));
/* 110 */     arrayList3.add(FieldUtil.getFormItemForColorPicker("conflictedColor", SystemEnv.getHtmlLabelName(386006, this.user.getLanguage()), "#" + str2));
/* 111 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(10000230, Util.getIntValue(this.user.getLanguage())));
/* 112 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/* 113 */     hashMap2.put("items", arrayList3);
/* 114 */     arrayList.add(hashMap2);
/*     */     
/* 116 */     hashMap2 = new HashMap<>();
/* 117 */     arrayList1 = new ArrayList<>();
/* 118 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(501229, this.language));
/* 119 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*     */     
/* 121 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(ConditionType.SWITCH, 82752, "isshare"), 6, 12);
/* 122 */     arrayList1.add(searchConditionItem);
/* 123 */     hashMap2.put("items", arrayList1);
/* 124 */     arrayList.add(hashMap2);
/*     */ 
/*     */     
/* 127 */     hashMap1.put("conditioninfo", arrayList);
/* 128 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarType() {
/* 136 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 137 */     String str1 = "Car1234567890";
/* 138 */     String str2 = str1 + "_" + Util.getEncrypt(Util.getRandom());
/* 139 */     byte b = 10;
/* 140 */     String str3 = " id,name,description,usefee ";
/* 141 */     String str4 = " from cartype ";
/* 142 */     String str5 = "";
/* 143 */     String str6 = Util.null2String(this.request.getParameter("searchName"));
/* 144 */     if (!str6.equals("")) str5 = " where name like '%" + str6 + "%'"; 
/* 145 */     String str7 = " id ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 156 */     String str8 = "<table  pageUid=\"" + str1 + "\" instanceid=\"CarTable\" tabletype=\"none\" pagesize=\"" + b + "\" ><sql backfields=\"" + str3 + "\" sqlform=\"" + str4 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str5) + "\"  sqlorderby=\"" + str7 + "\"  sqlprimarykey=\"id\" sqlsortway=\"desc\" sqlisdistinct=\"true\"/>\t <head>\t\t<col width=\"17%\"  text=\"" + SystemEnv.getHtmlLabelName(195, this.user.getLanguage()) + "\"  linkvaluecolumn=\"id\" column=\"name\" />\t\t<col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(433, this.user.getLanguage()) + "\" column=\"description\"/>\t\t<col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(1491, this.user.getLanguage()) + "\" column=\"usefee\"/>\t </head>\t <operates width=\"15%\"><popedom transmethod=\"\" otherpara=\"\" ></popedom> <operate href=\"javascript:doEdit()\"  text=\"" + SystemEnv.getHtmlLabelName(93, this.user.getLanguage()) + "\" target=\"_self\" index=\"0\"/><operate href=\"javascript:doDel()\"  text=\"" + SystemEnv.getHtmlLabelName(91, this.user.getLanguage()) + "\"  index=\"1\"/>";
/* 157 */     str8 = str8 + "</operates></table>";
/* 158 */     Util_TableMap.setVal(str2, str8);
/* 159 */     hashMap.put("sessionkey", str2);
/* 160 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> saveOrUpdateCarType() {
/* 168 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 169 */     String str1 = Util.null2String(this.request.getParameter("id"));
/* 170 */     String str2 = Util.null2String(this.request.getParameter("name"));
/* 171 */     String str3 = Util.null2String(this.request.getParameter("description"));
/* 172 */     String str4 = Util.null2String(this.request.getParameter("usefee"));
/* 173 */     RecordSet recordSet = new RecordSet();
/* 174 */     boolean bool = false;
/* 175 */     char c = Util.getSeparator();
/* 176 */     if (Util.null2String(str1).equals("")) {
/* 177 */       String str = str2 + c + str3 + c + str4;
/* 178 */       bool = recordSet.executeProc("CarType_Insert", str);
/*     */     } else {
/* 180 */       String str = str1 + c + str2 + c + str3 + c + str4;
/* 181 */       bool = recordSet.executeProc("CarType_Update", str);
/*     */     } 
/* 183 */     if (bool) {
/* 184 */       hashMap.put("status", "1");
/*     */     }
/* 186 */     CarTypeComInfo carTypeComInfo = new CarTypeComInfo();
/* 187 */     carTypeComInfo.removeCarTypeCache();
/* 188 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> deleteCarType() {
/* 196 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 197 */     String str1 = Util.null2String(this.request.getParameter("id"));
/* 198 */     RecordSet recordSet = new RecordSet();
/* 199 */     boolean bool = false;
/* 200 */     int i = 0;
/*     */     
/* 202 */     recordSet.execute("select count(1) from CarInfo where carType=" + str1);
/* 203 */     if (recordSet.next()) {
/* 204 */       i = recordSet.getInt(1);
/* 205 */       if (i > 0) {
/* 206 */         hashMap.put("status", "-1");
/* 207 */         hashMap.put("error", SystemEnv.getHtmlLabelName(21018, this.user.getLanguage()));
/* 208 */         return (Map)hashMap;
/*     */       } 
/*     */     } 
/*     */     
/* 212 */     String str2 = str1;
/* 213 */     bool = recordSet.executeProc("CarType_Delete", str2);
/* 214 */     if (bool) {
/* 215 */       hashMap.put("status", "1");
/*     */     }
/* 217 */     CarTypeComInfo carTypeComInfo = new CarTypeComInfo();
/* 218 */     carTypeComInfo.removeCarTypeCache();
/* 219 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/service/CarBackgroundService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */