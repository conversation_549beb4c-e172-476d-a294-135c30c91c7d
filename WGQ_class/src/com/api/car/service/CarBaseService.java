/*     */ package com.api.car.service;
/*     */ 
/*     */ import com.api.browser.bean.BrowserBean;
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.impl.FormmodeBrowserService;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.car.util.CarSetInfo;
/*     */ import com.api.car.util.CarUtil;
/*     */ import com.api.meeting.util.PageUidFactory;
/*     */ import com.api.workplan.util.WorkPlanSearchConditionUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ import weaver.workflow.form.FormManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarBaseService
/*     */   extends BaseBean
/*     */ {
/*     */   private User user;
/*  42 */   private int language = 7;
/*     */   
/*     */   private HttpServletRequest request;
/*     */   private HttpServletResponse response;
/*     */   
/*     */   public CarBaseService() {}
/*     */   
/*     */   public CarBaseService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  50 */     init(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void init(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  60 */     this.request = paramHttpServletRequest;
/*  61 */     this.response = paramHttpServletResponse;
/*  62 */     this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  63 */     if (this.user != null) {
/*  64 */       this.language = this.user.getLanguage();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarList(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  70 */     int i = 0;
/*  71 */     int j = this.user.getUID();
/*  72 */     String str1 = " where 1=1";
/*  73 */     int k = 0;
/*  74 */     RecordSet recordSet1 = new RecordSet();
/*  75 */     recordSet1.execute("select carsdetachable from SystemSet");
/*  76 */     int m = 0;
/*  77 */     if (recordSet1.next()) {
/*  78 */       m = recordSet1.getInt(1);
/*     */     }
/*  80 */     i = Util.getIntValue(paramHttpServletRequest.getParameter("subCompanyId"), 0);
/*  81 */     RecordSet recordSet2 = new RecordSet();
/*  82 */     String str2 = "";
/*  83 */     if (m == 1) {
/*     */       
/*  85 */       if (j != 1) {
/*  86 */         String str24 = "";
/*  87 */         String str25 = "";
/*  88 */         char c = Util.getSeparator();
/*  89 */         recordSet2.executeProc("HrmRoleSR_SeByURId", "" + j + c + "Car:Maintenance");
/*  90 */         CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  91 */         while (recordSet2.next()) {
/*  92 */           str25 = recordSet2.getString("subcompanyid");
/*  93 */           k = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "Car:Maintenance", Util.getIntValue(str25, -1));
/*  94 */           if (k == -1) {
/*     */             continue;
/*     */           }
/*  97 */           str2 = str2 + str25 + ":" + k + ",";
/*  98 */           str24 = str24 + ", " + str25;
/*     */         } 
/* 100 */         str2 = str2.replaceAll(",$", "");
/* 101 */         if (!"".equals(str24)) {
/* 102 */           str24 = str24.substring(1);
/* 103 */           str1 = str1 + " and subcompanyid in (" + str24 + ") ";
/*     */         } else {
/* 105 */           str1 = str1 + " and subcompanyid=" + this.user.getUserSubCompany1();
/*     */         } 
/* 107 */         if (i == 0) {
/* 108 */           k = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "Car:Maintenance", 0);
/*     */         } else {
/* 110 */           k = checkSubCompanyRight.ChkComRightByUserRightCompanyId(this.user.getUID(), "Car:Maintenance", i);
/*     */         } 
/*     */       } else {
/* 113 */         k = 2;
/*     */       }
/*     */     
/* 116 */     } else if (j == 1 || HrmUserVarify.checkUserRight("Car:Maintenance", this.user)) {
/* 117 */       k = 2;
/*     */     } 
/*     */     
/* 120 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 121 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("carNo"));
/* 122 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("flowTitle2"));
/* 123 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("carType"));
/* 124 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("factoryNo"));
/* 125 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("driver"));
/* 126 */     FormmodeBrowserService formmodeBrowserService = new FormmodeBrowserService();
/* 127 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("buydate_select"));
/* 128 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("buydate_start"));
/* 129 */     String str10 = Util.null2String(paramHttpServletRequest.getParameter("buydate_end"));
/* 130 */     String str11 = Util.null2String(paramHttpServletRequest.getParameter("carstatus"));
/* 131 */     Map map = FormmodeBrowserService.getDateRangeByDateField(str8, str9, str10);
/* 132 */     str9 = (String)map.get("startdate");
/* 133 */     str10 = (String)map.get("enddate");
/* 134 */     String str12 = Util.null2String(paramHttpServletRequest.getParameter("ismanager"));
/* 135 */     if (!"".equals(str11) && "depositlist".equals(str11)) {
/* 136 */       str1 = str1 + " and deposit=1 ";
/*     */     } else {
/* 138 */       str1 = str1 + " and (deposit<>'1' or deposit is null) ";
/*     */     } 
/* 140 */     if (!str3.equals("")) {
/* 141 */       str1 = str1 + " and carNo like '%" + str3 + "%'";
/*     */     }
/* 143 */     else if (!"".equals(str4)) {
/* 144 */       str1 = str1 + " and carNo like '%" + str4 + "%'";
/*     */     } 
/*     */     
/* 147 */     if (!str5.equals("")) {
/* 148 */       str1 = str1 + " and carType=" + str5 + "";
/*     */     }
/* 150 */     if (!str7.equals("")) {
/* 151 */       str1 = str1 + " and driver ='" + str7 + "'";
/*     */     }
/* 153 */     if (!str6.equals("")) {
/* 154 */       str1 = str1 + " and factoryNo like '%" + str6 + "%'";
/*     */     }
/* 156 */     if (!str9.equals("")) {
/* 157 */       str1 = str1 + " and buyDate >= '" + str9 + "'";
/*     */     }
/* 159 */     if (!str10.equals("")) {
/* 160 */       str1 = str1 + " and buyDate <= '" + str10 + "'";
/*     */     }
/* 162 */     if (i > 0) {
/* 163 */       if (this.user.getUID() == 1) {
/* 164 */         if (i > 0) {
/* 165 */           str1 = str1 + " and subCompanyId=" + i;
/*     */         } else {
/* 167 */           str1 = str1 + " and subCompanyId>=0";
/*     */         }
/*     */       
/*     */       }
/* 171 */       else if (i > 0) {
/* 172 */         str1 = str1 + " and subCompanyId=" + i;
/*     */       } else {
/* 174 */         str1 = str1 + " and subCompanyId>0";
/*     */       } 
/*     */     }
/*     */     
/* 178 */     int n = Util.getIntValue(paramHttpServletRequest.getParameter("pagenum"), 1);
/* 179 */     byte b = 10;
/*     */     
/* 181 */     String str13 = "id, factoryNo, carNo, carType, driver, buyDate,deposit,subcompanyid,showorder ";
/* 182 */     String str14 = " from CarInfo a";
/* 183 */     String str15 = str1;
/*     */     
/* 185 */     String str16 = "showorder asc,id desc";
/* 186 */     CarSetInfo carSetInfo = new CarSetInfo();
/* 187 */     String str17 = "column:userid";
/* 188 */     String str18 = "column:viewtype+column:isremark+column:isprocessed+column:nodeid+column:workflowid+";
/* 189 */     String str19 = this.user.getUID() + "_" + Character.MIN_VALUE;
/* 190 */     String str20 = "column:deposit";
/* 191 */     String str21 = "1234567890";
/*     */     
/* 193 */     String str22 = " <table pageUid=\"" + str21 + "\" instanceid=\"CarTable\" tabletype=\"none\" pagesize=\"" + b + "\" >\t<sql backfields=\"" + str13 + "\" sqlform=\"" + str14 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str15) + "\"  sqlorderby=\"" + str16 + "\"  sqlprimarykey=\"id\" sqlsortway=\"desc\" sqlisdistinct=\"true\"/>";
/*     */     
/* 195 */     str22 = str22 + "<operates>";
/* 196 */     str22 = str22 + "<popedom transmethod=\"com.api.car.util.CarUtil.getCarOperateBtns\" otherpara=\"column:subCompanyId+" + carSetInfo.getIsShare() + "+" + k + "+" + str2 + "\"  otherpara2=\"" + str20 + "\"></popedom> ";
/* 197 */     str22 = str22 + " <operate href=\"javascript:doEdit();\"  text=\"" + SystemEnv.getHtmlLabelName(93, this.user.getLanguage()) + "\"  index=\"0\"/>";
/* 198 */     str22 = str22 + "<operate  href=\"javascript:doDel();\"  text=\"" + SystemEnv.getHtmlLabelName(91, this.user.getLanguage()) + "\"  index=\"1\"/>";
/* 199 */     str22 = str22 + "<operate  href=\"javascript:doDeposit();\"  text=\"" + SystemEnv.getHtmlLabelName(22151, this.user.getLanguage()) + "\"  index=\"2\"/>";
/* 200 */     str22 = str22 + "<operate  href=\"javascript:doDeposit();\"  text=\"" + SystemEnv.getHtmlLabelName(22152, this.user.getLanguage()) + "\"  index=\"3\"/>";
/* 201 */     if (carSetInfo.getIsShare() == 1) {
/* 202 */       str22 = str22 + "<operate  href=\"javascript:doShare();\"  text=\"" + SystemEnv.getHtmlLabelName(82752, this.user.getLanguage()) + "\"  index=\"4\"/>";
/*     */     }
/* 204 */     str22 = str22 + "</operates>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 220 */     str22 = str22 + "\t<head>\t<col width=\"17%\"  text=\"" + SystemEnv.getHtmlLabelName(20318, this.user.getLanguage()) + "\" column=\"factoryNo\" orderkey=\"factoryNo\" /><col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(20319, this.user.getLanguage()) + "\" column=\"carNo\"  orderkey=\"carNo\" linkvaluecolumn=\"id\" linkkey=\"id\" href=\"javascript:showDialog2('/car/CarInfoViewTab.jsp?id={0}&amp;fg=1&amp;flag=1&amp;dialog=1')\" target=\"_self\"/><col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(63, this.user.getLanguage()) + "\" column=\"carType\" orderkey=\"carType\" transmethod=\"weaver.car.CarTypeComInfo.getCarTypename\" /><col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(17649, this.user.getLanguage()) + "\" column=\"driver\"  orderkey=\"driver\" transmethod=\"weaver.hrm.resource.ResourceComInfo.getResourcename\" /><col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(16914, this.user.getLanguage()) + "\" column=\"buyDate\"  orderkey=\"buyDate\" /><col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()) + "\" column=\"subCompanyId\"  orderkey=\"subCompanyId\" otherpara=\"" + this.user.getLanguage() + "\" transmethod=\"com.api.car.util.CarUtil.getSubCompanyname\" /><col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(15513, this.user.getLanguage()) + "\" column=\"showorder\"  orderkey=\"showorder\" /><col width=\"0%\" display='false'  column=\"deposit\"  orderkey=\"deposit\" /></head> </table>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 225 */     String str23 = str21 + "_" + Util.getEncrypt(Util.getRandom());
/* 226 */     Util_TableMap.setVal(str23, str22);
/* 227 */     hashMap.put("sessionkey", str23);
/* 228 */     hashMap.put("operatelevel", Integer.valueOf(k));
/* 229 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public ArrayList<String> getHrmGroupBaseOperate(String paramString1, String paramString2, String paramString3) {
/* 234 */     ArrayList<String> arrayList = new ArrayList();
/* 235 */     arrayList.add("true");
/* 236 */     if (paramString3.equals("0") || paramString2.equals("true")) {
/* 237 */       arrayList.add("true");
/*     */     } else {
/* 239 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 242 */     if (paramString3.equals("1") && paramString2.equals("true")) {
/* 243 */       arrayList.add("true");
/*     */     } else {
/* 245 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 248 */     arrayList.add(paramString2);
/*     */     
/* 250 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarTypeListData() {
/* 261 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 262 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     try {
/* 264 */       String str = "SELECT a.id,a.name,a.description,a.usefee FROM CarType a ORDER  BY id desc";
/* 265 */       RecordSet recordSet = new RecordSet();
/* 266 */       recordSet.execute(str);
/* 267 */       while (recordSet.next()) {
/* 268 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 269 */         hashMap1.put("id", recordSet.getString("id"));
/* 270 */         hashMap1.put("name", recordSet.getString("name"));
/* 271 */         hashMap1.put("description", recordSet.getString("description"));
/* 272 */         hashMap1.put("usefee", recordSet.getString("usefee"));
/* 273 */         arrayList.add(hashMap1);
/*     */       } 
/* 275 */       hashMap.put("carTypeListData", arrayList);
/* 276 */     } catch (Exception exception) {
/* 277 */       writeLog(exception);
/*     */     } 
/* 279 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarFormField() {
/* 288 */     RecordSet recordSet = new RecordSet();
/* 289 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/* 292 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 296 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 297 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 298 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(1361, this.language));
/* 299 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*     */     
/* 301 */     recordSet.execute("select carsdetachable from SystemSet");
/* 302 */     int i = 0;
/* 303 */     if (recordSet.next()) {
/* 304 */       i = recordSet.getInt(1);
/*     */     }
/*     */     
/* 307 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 309 */     ConditionType conditionType = ConditionType.INPUT;
/* 310 */     SearchConditionItem searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 20319, "carNo", true), 6, 12);
/* 311 */     searchConditionItem.setViewAttr(3);
/* 312 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 315 */     conditionType = ConditionType.BROWSER;
/* 316 */     BrowserBean browserBean = new BrowserBean("169", SystemEnv.getHtmlLabelName(17868, this.language));
/* 317 */     browserBean.getDataParams().put("rightStr", "Car:Maintenance");
/* 318 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 17868, "subCompanyId", browserBean), 6, 12);
/* 319 */     searchConditionItem.setViewAttr((i == 1) ? 3 : 2);
/* 320 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 323 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 324 */     arrayList2.add(new SearchConditionOption("", ""));
/* 325 */     recordSet.executeProc("CarType_Select", "");
/* 326 */     while (recordSet.next()) {
/* 327 */       String str1 = recordSet.getString("id");
/* 328 */       String str2 = recordSet.getString("name");
/* 329 */       arrayList2.add(new SearchConditionOption(str1, str2));
/*     */     } 
/* 331 */     conditionType = ConditionType.SELECT;
/* 332 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 17630, "carType", arrayList2), 6, 12);
/* 333 */     searchConditionItem.setViewAttr(3);
/* 334 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 337 */     conditionType = ConditionType.INPUTNUMBER;
/* 338 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 1491, "usefee"), 6, 12);
/* 339 */     searchConditionItem.setViewAttr(3);
/* 340 */     searchConditionItem.setPrecision(2);
/* 341 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 344 */     conditionType = ConditionType.INPUT;
/* 345 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 20318, "factoryNo"), 6, 12);
/* 346 */     searchConditionItem.setViewAttr(3);
/* 347 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 350 */     conditionType = ConditionType.INPUTNUMBER;
/* 351 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 20320, "price"), 6, 12);
/* 352 */     searchConditionItem.setViewAttr(2);
/* 353 */     searchConditionItem.setPrecision(2);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 362 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 365 */     conditionType = ConditionType.DATEPICKER;
/* 366 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 16914, "buyDate"), 6, 12);
/* 367 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 370 */     conditionType = ConditionType.INPUT;
/* 371 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 20322, "engineNo"), 6, 9);
/* 372 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 375 */     conditionType = ConditionType.BROWSER;
/* 376 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 17649, "driver", "1"), 6, 12);
/* 377 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 380 */     conditionType = ConditionType.INPUT;
/* 381 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 454, "remark"), 6, 12);
/* 382 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 385 */     conditionType = ConditionType.INPUTNUMBER;
/* 386 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 15513, "showorder"), 6, 12);
/* 387 */     searchConditionItem.setViewAttr(3);
/* 388 */     searchConditionItem.setValue(Integer.valueOf(1));
/* 389 */     searchConditionItem.setPrecision(2);
/* 390 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 393 */     conditionType = ConditionType.SWITCH;
/* 394 */     searchConditionItem = CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 383297, "deposit"), 6, 12);
/* 395 */     arrayList1.add(searchConditionItem);
/*     */ 
/*     */     
/* 398 */     hashMap2.put("items", arrayList1);
/* 399 */     arrayList.add(hashMap2);
/*     */     
/* 401 */     CarSetInfo carSetInfo = new CarSetInfo();
/* 402 */     hashMap1.put("conditioninfo", arrayList);
/* 403 */     hashMap1.put("status", "1");
/* 404 */     hashMap1.put("isshare", Integer.valueOf(carSetInfo.getIsShare()));
/* 405 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarUseSingtonList() {
/* 413 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 414 */     RecordSet recordSet1 = new RecordSet();
/* 415 */     RecordSet recordSet2 = new RecordSet();
/* 416 */     FormManager formManager = new FormManager();
/* 417 */     String str1 = "";
/* 418 */     if (recordSet1.getDBType().equals("oracle")) {
/* 419 */       str1 = str1 + "(select id,requestid,to_number(carId) as carId,to_number(driver) as driver,to_number(userid) as userid,startdate,starttime,enddate,endtime,cancel from CarUseApprove";
/*     */     } else {
/* 421 */       str1 = str1 + "(select id,requestid,carId,driver,userid,startdate,starttime,enddate,endtime,cancel from CarUseApprove";
/*     */     } 
/* 423 */     recordSet1.executeSql("select id,formid from carbasic where formid!=163 and isuse = 1");
/* 424 */     while (recordSet1.next()) {
/* 425 */       String str9 = recordSet1.getString("id");
/* 426 */       String str10 = recordSet1.getString("formid");
/* 427 */       String str11 = formManager.getTablename(str10);
/* 428 */       str1 = str1 + " union all select id,requestid,";
/* 429 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 430 */       recordSet2.executeSql("select carfieldid,modefieldid,fieldname from mode_carrelatemode c,workflow_billfield b where c.modefieldid=b.id and mainid=" + str9);
/* 431 */       while (recordSet2.next()) {
/* 432 */         String str12 = recordSet2.getString("carfieldid");
/* 433 */         String str13 = recordSet2.getString("modefieldid");
/* 434 */         String str14 = recordSet2.getString("fieldname");
/* 435 */         hashMap1.put(str12, str14);
/*     */       } 
/* 437 */       if (recordSet1.getDBType().equals("oracle")) {
/* 438 */         str1 = str1 + "to_number(" + Util.null2s(Util.null2String(hashMap1.get("627")), "0") + ") as carId,";
/* 439 */         str1 = str1 + "to_number(" + Util.null2s(Util.null2String(hashMap1.get("628")), "0") + ") as driver,";
/* 440 */         str1 = str1 + "to_number(" + Util.null2s(Util.null2String(hashMap1.get("629")), "0") + ") as userid,";
/* 441 */       } else if (recordSet1.getDBType().equals("mysql")) {
/* 442 */         str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("627")), "0") + " as carId,";
/* 443 */         str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("628")), "0") + " as driver,";
/* 444 */         str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("629")), "0") + " as userid,";
/*     */       }
/* 446 */       else if (recordSet1.getDBType().equals("postgresql")) {
/* 447 */         str1 = str1 + "to_number(" + Util.null2s(Util.null2String(hashMap1.get("627")), "0") + ") as carId,";
/* 448 */         str1 = str1 + "to_number(" + Util.null2s(Util.null2String(hashMap1.get("628")), "0") + ") as driver,";
/* 449 */         str1 = str1 + "to_number(" + Util.null2s(Util.null2String(hashMap1.get("629")), "0") + ") as userid,";
/*     */       } else {
/*     */         
/* 452 */         str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("627")), "0") + " as carId,";
/* 453 */         str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("628")), "0") + " as driver,";
/* 454 */         str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("629")), "0") + " as userid,";
/*     */       } 
/*     */       
/* 457 */       str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("634")), "''") + " as startDate,";
/* 458 */       str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("635")), "''") + " as startTime,";
/* 459 */       str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("636")), "''") + " as endDate,";
/* 460 */       str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("637")), "''") + " as endTime,";
/* 461 */       str1 = str1 + Util.null2s(Util.null2String(hashMap1.get("639")), "'0'") + " as cancel";
/* 462 */       str1 = str1 + " from " + str11;
/*     */     } 
/* 464 */     str1 = str1 + ")";
/*     */     
/* 466 */     String str2 = Util.null2String(this.request.getParameter("carid"));
/* 467 */     byte b = 10;
/* 468 */     String str3 = "t1.*";
/* 469 */     String str4 = " from " + str1 + " t1,workflow_requestbase t2";
/* 470 */     String str5 = "CarUseSinton";
/* 471 */     String str6 = "where t1.carId = " + str2 + " and t1.requestid=t2.requestid and t2.workflowid not in (select workflowid from carbasic where isuse=0) and t2.currentnodetype<>0 ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 485 */     String str7 = "<table  pageUid=\"" + str5 + "\" instanceid=\"CarUseSintonTable\" pagesize=\"" + b + "\" tabletype=\"none\"><sql backfields=\"" + str3 + "\" sqlform=\"" + str4 + "\" sqlprimarykey=\"uuid\" sqlsortway=\"Desc\" sqlisdistinct=\"true\" sqlwhere=\"" + Util.toHtmlForSplitPage(str6) + "\"/><head><col width=\"15%\"  text=\"" + SystemEnv.getHtmlLabelName(21028, this.user.getLanguage()) + "\" column=\"carId\"  transmethod=\"weaver.car.CarInfoComInfo.getCarNo\" /><col width=\"0%\" hide =\"true\"  text=\"\" column=\"uuid\"  transmethod=\"com.api.car.util.CarUtil.getUUid\" /><col width=\"30%\"  text=\"" + SystemEnv.getHtmlLabelName(648, this.user.getLanguage()) + "\" column=\"requestname\"  otherpara=\"column:requestid\" transmethod=\"com.api.car.util.CarUtil.getRequestName\" /><col width=\"15%\"  text=\"" + SystemEnv.getHtmlLabelName(602, this.user.getLanguage()) + "\" column=\"requestid\"  transmethod=\"weaver.workflow.request.RequestComInfo.getRequestStatus\"/><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(742, this.user.getLanguage()) + "\"  column=\"startDate\" otherpara=\"column:startTime\"  transmethod=\"weaver.car.CarDateTimeUtil.getLocaleDate\"/><col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(743, this.user.getLanguage()) + "\"  column=\"endDate\"  otherpara=\"column:endTime\" transmethod=\"weaver.car.CarDateTimeUtil.getLocaleDate\"/></head></table>";
/*     */ 
/*     */     
/* 488 */     String str8 = str5 + "_" + Util.getEncrypt(Util.getRandom());
/* 489 */     Util_TableMap.setVal(str8, str7);
/* 490 */     hashMap.put("sessionkey", str8);
/*     */     
/* 492 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarShareFields(Map<String, Object> paramMap) {
/* 501 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 502 */     int i = this.user.getLanguage();
/* 503 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 504 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/* 505 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT_INTERVAL, 683, new String[] { "seclevel", "seclevelMax" });
/* 506 */     searchConditionItem1.setValue(new String[] { "10", "100" });
/* 507 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/* 508 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(179, i), true));
/* 509 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(124, i)));
/* 510 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(141, i)));
/* 511 */     boolean bool = (new ManageDetachComInfo()).appDetachDisableAll(this.user);
/* 512 */     if (!bool) {
/* 513 */       arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(1340, i)));
/*     */     }
/* 515 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(122, i)));
/* 516 */     arrayList.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(6086, i)));
/*     */     
/* 518 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 519 */     arrayList1.add(conditionFactory.createCondition(ConditionType.SELECT, 19117, "permissiontype", arrayList));
/* 520 */     arrayList1.add(hashMap2);
/*     */ 
/*     */     
/* 523 */     ArrayList<ArrayList<SearchConditionItem>> arrayList2 = new ArrayList();
/* 524 */     ArrayList<SearchConditionItem> arrayList3 = new ArrayList();
/* 525 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 106, "departmentid", "57");
/* 526 */     BrowserBean browserBean = searchConditionItem2.getBrowserConditionParam();
/* 527 */     browserBean.setViewAttr(3);
/* 528 */     searchConditionItem2.setBrowserConditionParam(browserBean);
/* 529 */     searchConditionItem2.setViewAttr(3);
/* 530 */     arrayList3.add(searchConditionItem2);
/* 531 */     arrayList2.add(arrayList3);
/* 532 */     arrayList3 = new ArrayList<>();
/* 533 */     searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT_INTERVAL, 683, new String[] { "seclevel", "seclevelMax" });
/* 534 */     searchConditionItem1.setValue(new String[] { "0", "100" });
/* 535 */     arrayList3.add(searchConditionItem1);
/* 536 */     arrayList2.add(arrayList3);
/* 537 */     hashMap2.put("1", arrayList2);
/*     */ 
/*     */     
/* 540 */     arrayList2 = new ArrayList<>();
/* 541 */     arrayList3 = new ArrayList<>();
/* 542 */     searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 106, "subids", "194");
/* 543 */     browserBean = searchConditionItem2.getBrowserConditionParam();
/* 544 */     browserBean.setViewAttr(3);
/* 545 */     searchConditionItem2.setBrowserConditionParam(browserBean);
/* 546 */     searchConditionItem2.setViewAttr(3);
/* 547 */     arrayList3.add(searchConditionItem2);
/* 548 */     arrayList2.add(arrayList3);
/* 549 */     arrayList3 = new ArrayList<>();
/* 550 */     searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT_INTERVAL, 683, new String[] { "seclevel", "seclevelMax" });
/* 551 */     searchConditionItem1.setValue(new String[] { "0", "100" });
/* 552 */     arrayList3.add(searchConditionItem1);
/* 553 */     arrayList2.add(arrayList3);
/* 554 */     hashMap2.put("6", arrayList2);
/*     */ 
/*     */     
/* 557 */     arrayList2 = new ArrayList<>();
/* 558 */     arrayList3 = new ArrayList<>();
/* 559 */     searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT_INTERVAL, 683, new String[] { "seclevel", "seclevelMax" });
/* 560 */     searchConditionItem1.setValue(new String[] { "0", "100" });
/* 561 */     arrayList3.add(searchConditionItem1);
/* 562 */     arrayList2.add(arrayList3);
/* 563 */     hashMap2.put("3", arrayList2);
/*     */ 
/*     */     
/* 566 */     arrayList2 = new ArrayList<>();
/* 567 */     arrayList3 = new ArrayList<>();
/* 568 */     searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 106, "userid", "17");
/* 569 */     browserBean = searchConditionItem2.getBrowserConditionParam();
/* 570 */     browserBean.setViewAttr(3);
/* 571 */     searchConditionItem2.setBrowserConditionParam(browserBean);
/* 572 */     searchConditionItem2.setViewAttr(3);
/* 573 */     arrayList3.add(searchConditionItem2);
/* 574 */     arrayList2.add(arrayList3);
/* 575 */     hashMap2.put("5", arrayList2);
/*     */ 
/*     */     
/* 578 */     arrayList2 = new ArrayList<>();
/* 579 */     arrayList3 = new ArrayList<>();
/* 580 */     searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 106, "roleid", "65");
/* 581 */     browserBean = searchConditionItem2.getBrowserConditionParam();
/* 582 */     browserBean.setViewAttr(3);
/* 583 */     searchConditionItem2.setBrowserConditionParam(browserBean);
/* 584 */     searchConditionItem2.setViewAttr(3);
/* 585 */     arrayList3.add(searchConditionItem2);
/* 586 */     arrayList3.add(conditionFactory.createCondition(ConditionType.SELECT, 139, "rolelevel", WorkPlanSearchConditionUtil.getRoleLevelOption(i)));
/* 587 */     arrayList2.add(arrayList3);
/* 588 */     arrayList3 = new ArrayList<>();
/* 589 */     searchConditionItem1 = conditionFactory.createCondition(ConditionType.INPUT_INTERVAL, 683, new String[] { "seclevel", "seclevelMax" });
/* 590 */     searchConditionItem1.setValue(new String[] { "0", "100" });
/* 591 */     arrayList3.add(searchConditionItem1);
/* 592 */     arrayList2.add(arrayList3);
/* 593 */     hashMap2.put("2", arrayList2);
/*     */ 
/*     */     
/* 596 */     arrayList2 = new ArrayList<>();
/* 597 */     arrayList3 = new ArrayList<>();
/*     */     
/* 599 */     searchConditionItem2 = conditionFactory.createCondition(ConditionType.BROWSER, 106, "jobid", "278");
/* 600 */     browserBean = searchConditionItem2.getBrowserConditionParam();
/* 601 */     browserBean.setViewAttr(3);
/* 602 */     searchConditionItem2.setBrowserConditionParam(browserBean);
/* 603 */     searchConditionItem2.setViewAttr(3);
/* 604 */     arrayList3.add(searchConditionItem2);
/*     */     
/* 606 */     arrayList2.add(arrayList3);
/* 607 */     arrayList3 = new ArrayList<>();
/* 608 */     searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT_LINKAGE, 28169, "joblevel", WorkPlanSearchConditionUtil.getJobLevelOption(i));
/* 609 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 610 */     SearchConditionItem searchConditionItem3 = conditionFactory.createCondition(ConditionType.BROWSER, 19437, "sublevelids", "164");
/* 611 */     searchConditionItem3.setKey("1");
/* 612 */     browserBean = searchConditionItem3.getBrowserConditionParam();
/* 613 */     browserBean.setViewAttr(3);
/* 614 */     searchConditionItem3.setBrowserConditionParam(browserBean);
/* 615 */     searchConditionItem3.setViewAttr(3);
/* 616 */     SearchConditionItem searchConditionItem4 = conditionFactory.createCondition(ConditionType.BROWSER, 19438, "deplevelids", "4");
/* 617 */     searchConditionItem4.setKey("2");
/* 618 */     browserBean = searchConditionItem4.getBrowserConditionParam();
/* 619 */     browserBean.setViewAttr(3);
/* 620 */     searchConditionItem4.setBrowserConditionParam(browserBean);
/* 621 */     searchConditionItem4.setViewAttr(3);
/* 622 */     hashMap3.put("1", searchConditionItem3);
/* 623 */     hashMap3.put("2", searchConditionItem4);
/* 624 */     searchConditionItem2.setSelectLinkageDatas(hashMap3);
/* 625 */     arrayList3.add(searchConditionItem2);
/* 626 */     arrayList2.add(arrayList3);
/* 627 */     hashMap2.put("8", arrayList2);
/*     */     
/* 629 */     hashMap1.put("fields", arrayList1);
/* 630 */     return (Map)hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getShareList(Map<String, Object> paramMap) {
/* 639 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 640 */     String str1 = Util.null2String(paramMap.get("id"));
/* 641 */     String str2 = Util.null2String(paramMap.get("isview"));
/* 642 */     String str3 = PageUidFactory.getPageUid("roomShareList");
/* 643 */     String str4 = str3;
/* 644 */     String str5 = PageIdConst.getPageSize(str4, this.user.getUID());
/* 645 */     String str6 = " id ";
/* 646 */     String str7 = "";
/* 647 */     String str8 = " carid = " + str1;
/*     */     
/* 649 */     String str9 = "column:departmentid+column:subcompanyid+column:userid+column:roleid+column:rolelevel+" + this.user.getLanguage() + "+column:jobtitleid+column:joblevel+column:joblevelvalue";
/*     */     
/* 651 */     String str10 = "column:deptlevel+column:deptlevelMax+column:sublevel+column:sublevelMax+column:seclevel+column:seclevelMax+column:roleseclevel+column:roleseclevelMax";
/*     */     
/* 653 */     String str11 = " id,carid,permissiontype,permissiontype as permissiontype2 ,permissiontype as permissiontype3 ,departmentid,deptlevel,subcompanyid,sublevel,seclevel,userid,seclevelMax,deptlevelMax,sublevelMax,roleid,rolelevel,roleseclevel,roleseclevelMax,jobtitleid,joblevel,joblevelvalue ";
/* 654 */     String str12 = " car_share ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 662 */     str7 = " <table instanceid=\"\" pageId=\"" + str3 + "\" pageUid=\"" + str3 + "\" tabletype=\"checkbox\"  pagesize=\"" + str5 + "\" > <checkboxpopedom  id=\"checkbox\" popedompara=\"1\" showmethod=\"weaver.meeting.Maint.MeetingTransMethod.getCheckbox\"  />       <sql backfields=\"" + str11 + "\" sqlform=\"" + str12 + "\"  sqlwhere=\"" + Util.toHtmlForSplitPage(str8) + "\"  sqlorderby=\"" + str6 + "\"  sqlprimarykey=\"id\" sqlsortway=\"ASC\" sqlisdistinct=\"true\" />       <head>\t\t\t<col hide=\"true\" text=\"\" column=\"id\" orderkey=\"id\" />           <col width=\"30%\"  text=\"" + SystemEnv.getHtmlLabelName(21956, this.user.getLanguage()) + "\" column=\"permissiontype\" orderkey=\"permissiontype\" otherpara=\"" + this.user.getLanguage() + "\" transmethod=\"weaver.meeting.Maint.MeetingTransMethod.getMeetingPermissiontype\" />           <col width=\"40%\"  text=\"" + SystemEnv.getHtmlLabelName(106, this.user.getLanguage()) + "\" column=\"permissiontype2\" orderkey=\"permissiontype\" otherpara=\"" + str9 + "\" transmethod=\"weaver.meeting.Maint.MeetingTransMethod.getMeetingPermissionObj\" />           <col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(683, this.user.getLanguage()) + "\" column=\"permissiontype3\" orderkey=\"permissiontype\" otherpara=\"" + str10 + "\" transmethod=\"weaver.meeting.Maint.MeetingTransMethod.getMeetingPermissionlevel\" />       </head>";
/*     */     
/* 664 */     if (!"true".equals(str2))
/*     */     {
/*     */ 
/*     */       
/* 668 */       str7 = str7 + "\t   <operates>\t\t<popedom column=\"id\" transmethod=\"com.api.car.util.CarUtil.checkCarShareOperate\"></popedom> \t\t<operate href=\"javascript:onDel();\" text=\"" + SystemEnv.getHtmlLabelName(91, this.user.getLanguage()) + "\" target=\"_self\" index=\"0\"/>\t\t</operates>";
/*     */     }
/*     */     
/* 671 */     str7 = str7 + " </table>";
/* 672 */     String str13 = str3 + "_" + Util.getEncrypt(Util.getRandom());
/* 673 */     Util_TableMap.setVal(str13, str7);
/* 674 */     hashMap.put("sessionkey", str13);
/* 675 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> saveShare(Map<String, Object> paramMap) {
/* 685 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 686 */     String str1 = Util.null2String(paramMap.get("id"));
/* 687 */     RecordSet recordSet = new RecordSet();
/* 688 */     String str2 = "";
/* 689 */     String str3 = Util.null2String(paramMap.get("permissiontype"));
/* 690 */     if ("1".equals(str3)) {
/* 691 */       String[] arrayOfString = Util.TokenizerString2(
/* 692 */           Util.null2String(paramMap.get("departmentid")), ",");
/* 693 */       int i = Util.getIntValue(
/* 694 */           Util.null2String(paramMap.get("seclevel")), 0);
/* 695 */       int j = Util.getIntValue(
/* 696 */           Util.null2String(paramMap.get("seclevelMax")), 100);
/* 697 */       if (arrayOfString.length > 0) {
/* 698 */         for (String str : arrayOfString) {
/* 699 */           str2 = "select carid,departmentid from car_share where carid =" + str1 + " and departmentid = " + str;
/*     */           
/* 701 */           recordSet.execute(str2);
/* 702 */           if (!recordSet.next()) {
/* 703 */             str2 = " insert into car_share (carid,permissiontype,departmentid,deptlevel,deptlevelMax) values (" + str1 + "," + str3 + "," + str + "," + i + "," + j + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 711 */             recordSet.execute(str2);
/*     */           } else {
/* 713 */             str2 = " update car_share set deptlevel = " + i + ",deptlevelMax=" + j + " where carid=" + str1 + " and departmentid = " + str;
/*     */ 
/*     */ 
/*     */             
/* 717 */             recordSet.execute(str2);
/*     */           } 
/*     */         } 
/*     */       }
/* 721 */     } else if ("2".equals(str3)) {
/* 722 */       String[] arrayOfString = Util.TokenizerString2(
/* 723 */           Util.null2String(paramMap.get("roleid")), ",");
/* 724 */       int i = Util.getIntValue(
/* 725 */           Util.null2String(paramMap.get("rolelevel")), 0);
/* 726 */       int j = Util.getIntValue(
/* 727 */           Util.null2String(paramMap.get("seclevel")), 0);
/* 728 */       int k = Util.getIntValue(
/* 729 */           Util.null2String(paramMap.get("seclevelMax")), 100);
/* 730 */       if (arrayOfString.length > 0) {
/* 731 */         for (String str : arrayOfString) {
/* 732 */           str2 = "select carid,roleid from car_share where carid =" + str1 + " and roleid = " + str;
/*     */           
/* 734 */           recordSet.execute(str2);
/* 735 */           if (!recordSet.next()) {
/* 736 */             str2 = " insert into car_share (carid,permissiontype,roleid,roleseclevel,roleseclevelMax,rolelevel) values (" + str1 + "," + str3 + "," + str + "," + j + "," + k + "," + i + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 748 */             recordSet.execute(str2);
/*     */           } else {
/* 750 */             str2 = " update car_share set roleseclevel = " + j + ",roleseclevelMax=" + k + ",rolelevel=" + i + " where carid=" + str1 + " and roleid = " + str;
/*     */ 
/*     */ 
/*     */             
/* 754 */             recordSet.execute(str2);
/*     */           } 
/*     */         } 
/*     */       }
/* 758 */     } else if ("3".equals(str3)) {
/* 759 */       int i = Util.getIntValue(
/* 760 */           Util.null2String(paramMap.get("seclevel")), 0);
/* 761 */       int j = Util.getIntValue(
/* 762 */           Util.null2String(paramMap.get("seclevelMax")), 100);
/* 763 */       str2 = "select carid,permissiontype from car_share where carid =" + str1 + " and permissiontype=" + str3;
/*     */       
/* 765 */       recordSet.execute(str2);
/* 766 */       if (!recordSet.next()) {
/* 767 */         str2 = " insert into car_share (carid,permissiontype,seclevel,seclevelMax) values (" + str1 + "," + str3 + "," + i + "," + j + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 775 */         recordSet.execute(str2);
/*     */       } else {
/* 777 */         str2 = " update car_share set seclevel = " + i + ",seclevelMax=" + j + " where carid=" + str1 + " and permissiontype = " + str3;
/*     */ 
/*     */         
/* 780 */         recordSet.execute(str2);
/*     */       } 
/* 782 */     } else if ("5".equals(str3)) {
/* 783 */       String[] arrayOfString = Util.TokenizerString2(
/* 784 */           Util.null2String(paramMap.get("userid")), ",");
/* 785 */       if (arrayOfString.length > 0) {
/* 786 */         for (String str : arrayOfString) {
/* 787 */           str2 = "select carid,userid from car_share where carid =" + str1 + " and userid = " + str;
/*     */           
/* 789 */           recordSet.execute(str2);
/* 790 */           if (!recordSet.next()) {
/* 791 */             str2 = " insert into car_share (carid,permissiontype,userid) values (" + str1 + "," + str3 + "," + str + ")";
/*     */             
/* 793 */             recordSet.execute(str2);
/*     */           } 
/*     */         } 
/*     */       }
/* 797 */     } else if ("6".equals(str3)) {
/* 798 */       String[] arrayOfString = Util.TokenizerString2(
/* 799 */           Util.null2String(paramMap.get("subids")), ",");
/* 800 */       int i = Util.getIntValue(
/* 801 */           Util.null2String(paramMap.get("seclevel")), 0);
/* 802 */       int j = Util.getIntValue(
/* 803 */           Util.null2String(paramMap.get("seclevelMax")), 100);
/* 804 */       if (arrayOfString.length > 0) {
/* 805 */         for (String str : arrayOfString) {
/* 806 */           str2 = "select carid,subcompanyid from car_share where carid =" + str1 + " and subcompanyid = " + str;
/*     */           
/* 808 */           recordSet.execute(str2);
/* 809 */           if (!recordSet.next()) {
/* 810 */             str2 = " insert into car_share (carid,permissiontype,subcompanyid,sublevel,sublevelMax) values (" + str1 + "," + str3 + "," + str + "," + i + "," + j + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 818 */             recordSet.execute(str2);
/*     */           } else {
/* 820 */             str2 = " update car_share set sublevel = " + i + ",sublevelMax=" + j + " where carid=" + str1 + " and subcompanyid = " + str;
/*     */ 
/*     */ 
/*     */             
/* 824 */             recordSet.execute(str2);
/*     */           }
/*     */         
/*     */         }
/*     */       
/*     */       }
/* 830 */     } else if ("8".equals(str3)) {
/* 831 */       String[] arrayOfString = Util.TokenizerString2(
/* 832 */           Util.null2String(paramMap.get("jobid")), ",");
/* 833 */       int i = Util.getIntValue(
/* 834 */           Util.null2String(paramMap.get("joblevel")), 0);
/* 835 */       String str = "";
/* 836 */       if (i == 1) {
/* 837 */         str = Util.null2String(paramMap.get("sublevelids"));
/* 838 */       } else if (i == 2) {
/* 839 */         str = Util.null2String(paramMap.get("deplevelids"));
/*     */       } 
/* 841 */       if (arrayOfString.length > 0) {
/* 842 */         for (String str4 : arrayOfString) {
/* 843 */           str2 = "select mtid from car_share where carid =" + str1 + " and jobtitleid = " + str4;
/*     */           
/* 845 */           recordSet.execute(str2);
/* 846 */           if (!recordSet.next()) {
/* 847 */             str2 = " insert into car_share (carid,permissiontype,jobtitleid,joblevel,joblevelvalue) values (" + str1 + "," + str3 + "," + str4 + "," + i + ",'" + str + "')";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 855 */             recordSet.execute(str2);
/*     */           } else {
/* 857 */             str2 = " update car_share set joblevel = " + i + ",joblevelvalue=" + str + " where carid=" + str1 + " and jobtitleid = " + str4;
/*     */ 
/*     */ 
/*     */             
/* 861 */             recordSet.execute(str2);
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/* 866 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> delShare(Map<String, Object> paramMap) {
/* 875 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 876 */     String str1 = Util.null2String(paramMap.get("carid"));
/* 877 */     String str2 = Util.null2String(paramMap.get("ids"));
/* 878 */     RecordSet recordSet = new RecordSet();
/* 879 */     if (!"".equals(str2)) {
/* 880 */       String str = "delete from car_share where id in (" + str2 + ") ";
/* 881 */       recordSet.execute(str);
/*     */     } 
/* 883 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/service/CarBaseService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */