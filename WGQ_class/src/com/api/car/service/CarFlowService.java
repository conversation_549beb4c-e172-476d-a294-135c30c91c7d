/*     */ package com.api.car.service;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.impl.FormmodeBrowserService;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.car.util.CarUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarFlowService
/*     */   extends BaseBean
/*     */ {
/*     */   private User user;
/*  33 */   private int language = 7;
/*     */   
/*     */   private HttpServletRequest request;
/*     */   private HttpServletResponse response;
/*     */   
/*     */   public CarFlowService() {}
/*     */   
/*     */   public CarFlowService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  41 */     init(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void init(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  51 */     this.request = paramHttpServletRequest;
/*  52 */     this.response = paramHttpServletResponse;
/*  53 */     this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  54 */     if (this.user != null) {
/*  55 */       this.language = this.user.getLanguage();
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarFlowList(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  67 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  68 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  70 */     String str1 = "";
/*  71 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("requestname"));
/*  72 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("workflowid"));
/*  73 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("creater"));
/*  74 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("wfcode"));
/*  75 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("requestlevel"));
/*     */     
/*  77 */     FormmodeBrowserService formmodeBrowserService = new FormmodeBrowserService();
/*  78 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("createdate_select"));
/*  79 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("createdate_start"));
/*  80 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("createdate_end"));
/*  81 */     Map map = FormmodeBrowserService.getDateRangeByDateField(str7, str8, str9);
/*  82 */     str8 = (String)map.get("startdate");
/*  83 */     str9 = (String)map.get("enddate");
/*     */     
/*  85 */     String str10 = "";
/*     */     
/*  87 */     if (!StringHelper.isEmpty(str2)) {
/*  88 */       str1 = str1 + " AND t1.requestname LIKE '%" + str2 + "%' ";
/*     */     }
/*     */     
/*  91 */     if (!StringHelper.isEmpty(str3)) {
/*  92 */       str1 = str1 + " AND t1.workflowid=" + str3 + " ";
/*     */     }
/*     */     
/*  95 */     if (!StringHelper.isEmpty(str4)) {
/*  96 */       str1 = str1 + " AND t1.creater=" + str4 + " ";
/*     */     }
/*     */     
/*  99 */     if (!StringHelper.isEmpty(str5)) {
/* 100 */       str1 = str1 + " AND t1.requestmark LIKE '%" + str5 + "%' ";
/*     */     }
/*     */     
/* 103 */     if (!StringHelper.isEmpty(str6)) {
/* 104 */       str1 = str1 + " AND t1.requestlevel=" + str6 + " ";
/*     */     }
/*     */     
/* 107 */     if (!str8.equals("")) {
/* 108 */       str1 = str1 + " AND t1.createdate >= '" + str8 + "'";
/*     */     }
/* 110 */     if (!str9.equals("")) {
/* 111 */       str1 = str1 + " AND t1.createdate <= '" + str9 + "'";
/*     */     }
/*     */     
/* 114 */     byte b = 10;
/* 115 */     String str11 = " requestid,requestmark,createdate, createtime,creater, creatertype, workflowid, requestname, requestnamenew, status,requestlevel,currentnodeid,viewtype,userid,receivedate,receivetime,isremark,nodeid,agentorbyagentid,agenttype,isprocessed ,systype,workflowtype";
/* 116 */     String str12 = "" + this.user.getLogintype();
/* 117 */     boolean bool = false;
/* 118 */     if (str12.equals("2")) {
/* 119 */       bool = true;
/*     */     }
/* 121 */     String str13 = "";
/* 122 */     if (recordSet.getDBType().equals("oracle")) {
/* 123 */       str13 = "nvl";
/* 124 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 125 */       str13 = "ifnull";
/*     */     }
/* 127 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 128 */       str13 = "isnull";
/*     */     } else {
/*     */       
/* 131 */       str13 = "isnull";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 153 */     String str14 = "  ( SELECT t1.requestid, t1.requestmark, t1.createdate, t1.createtime, t1.creater , t1.creatertype, t1.workflowid, t1.requestname, t1.requestnamenew, t1.status , t1.requestlevel, t1.currentnodeid, t2.viewtype, t2.userid, t2.receivedate , t2.receivetime, t2.isremark, t2.nodeid, t2.agentorbyagentid, t2.agenttype , t2.isprocessed, '0' AS systype, t2.workflowtype FROM workflow_requestbase t1, workflow_currentoperator t2 WHERE (t1.deleted <> 1 \tOR t1.deleted IS NULL) AND t1.requestid = t2.requestid AND t2.userid = " + this.user.getUID() + " AND t2.usertype = " + bool + " AND (t1.deleted = 0 OR t1.deleted IS NULL) " + str1 + " AND t1.workflowid IN  ( SELECT id FROM workflow_base WHERE (formid = 163 OR formid IN ( SELECT formid FROM carbasic WHERE isuse = 1 )) AND id NOT IN ( SELECT workflowid \tFROM carbasic WHERE isuse = 0 ) AND isbill = '1' ) AND ((t2.isremark = 0 AND (t2.takisremark IS NULL OR t2.takisremark = 0)) OR t2.isremark IN ('1', '5', '8', '9', '7')) AND t2.islasttimes = 1  AND (" + str13 + "(t1.currentstatus,-1) = -1 or (" + str13 + "(t1.currentstatus,-1)=0 and t1.creater in (" + this.user.getUID() + ")))  AND t1.workflowid IN ( SELECT id FROM workflow_base WHERE isvalid = '1' OR isvalid = '3' ) ) t1";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 158 */     String str15 = "receivedate , receivetime";
/* 159 */     String str16 = "";
/* 160 */     String str17 = "column:userid";
/* 161 */     String str18 = "column:viewtype+column:isremark+column:isprocessed+column:nodeid+column:workflowid+" + str16;
/* 162 */     String str19 = this.user.getUID() + "_" + bool;
/*     */     
/* 164 */     String str20 = "carFlowList";
/* 165 */     String str21 = " <table pageUid=\"" + str20 + "\" instanceid=\"workflowRequestListTable\" tabletype=\"none\" pageId=\"" + str20 + "\" cssHandler=\"com.weaver.cssRenderHandler.request.CheckboxColorRender\" pagesize=\"" + b + "\" >";
/* 166 */     str21 = str21 + " <sql backfields=\"" + str11 + "\" sqlform=\"" + Util.toHtmlForSplitPage(str14) + "\" sqlwhere=\"\"  sqlorderby=\"" + str15 + "\"  sqlprimarykey=\"requestid\" sqlsortway=\"Desc\" sqlisdistinct=\"false\" />";
/* 167 */     str21 = str21 + "<operates>";
/* 168 */     str21 = str21 + " <popedom async=\"false\" transmethod=\"weaver.general.WorkFlowTransMethod.getWFSearchResultOperation\" otherpara=\"" + str18 + "\" otherpara2=\"" + str19 + "\" ></popedom> ";
/* 169 */     str21 = str21 + "<operate href=\"javascript:doPrint();\" otherpara=\"" + str17 + "\" text=\"" + SystemEnv.getHtmlLabelName(257, this.user.getLanguage()) + "\" index=\"2\"/>";
/* 170 */     str21 = str21 + "</operates>";
/* 171 */     str21 = str21 + "<head>";
/*     */ 
/*     */ 
/*     */     
/* 175 */     String str22 = this.user.getLanguage() + "+" + this.user.getUID() + "+column:userid";
/*     */     
/* 177 */     str21 = str21 + " <col width=\"10%\" display=\"true\"   text=\"" + SystemEnv.getHtmlLabelName(19502, this.user.getLanguage()) + "\" column=\"requestmark\" />";
/*     */ 
/*     */     
/* 180 */     str21 = str21 + " <col width=\"19%\" display=\"true\"  text=\"" + SystemEnv.getHtmlLabelName(1334, this.user.getLanguage()) + "\" column=\"requestname\" orderkey=\"requestname\"  transmethod=\"com.api.car.util.CarUtil.getCarFlowName\" otherpara=\"column:requestid\"/>";
/*     */ 
/*     */     
/* 183 */     str21 = str21 + " <col width=\"10%\"  display=\"true\"  text=\"" + SystemEnv.getHtmlLabelName(259, this.user.getLanguage()) + "\" column=\"workflowid\" orderkey=\"t1.workflowid\" transmethod=\"weaver.general.WorkFlowTransMethod.getWorkflowname\" />";
/*     */ 
/*     */     
/* 186 */     str21 = str21 + " <col width=\"6%\" display=\"true\"  text=\"" + SystemEnv.getHtmlLabelName(882, this.user.getLanguage()) + "\" column=\"creater\" orderkey=\"creater\"  otherpara=\"column:creatertype\" transmethod=\"weaver.general.WorkFlowTransMethod.getWFSearchResultName\" />";
/*     */ 
/*     */ 
/*     */     
/* 190 */     str21 = str21 + "<col display=\"true\" width=\"10%\"  text=\"" + SystemEnv.getHtmlLabelName(722, this.user.getLanguage()) + "\" column=\"createdate\" orderkey=\"t1.createdate,t1.createtime\" otherpara=\"column:createtime\" transmethod=\"weaver.general.WorkFlowTransMethod.getWFSearchResultCreateTime\" />";
/*     */ 
/*     */     
/* 193 */     str21 = str21 + " <col width=\"8%\" display=\"true\"   text=\"" + SystemEnv.getHtmlLabelName(18564, this.user.getLanguage()) + "\" column=\"currentnodeid\" otherpara=\"column:requestid\" orderkey=\"t1.currentnodeid\" transmethod=\"weaver.general.WorkFlowTransMethod.getCurrentNode\"/>";
/*     */ 
/*     */     
/* 196 */     str21 = str21 + " <col width=\"15%\" display=\"true\"   text=\"" + SystemEnv.getHtmlLabelName(16354, this.user.getLanguage()) + "\" column=\"requestid\"  otherpara=\"" + str22 + "\" transmethod=\"weaver.general.WorkFlowTransMethod.getUnOperators\"/>";
/*     */ 
/*     */     
/* 199 */     str21 = str21 + " <col width=\"10%\" display=\"true\"   text=\"" + SystemEnv.getHtmlLabelName(15534, this.user.getLanguage()) + "\" column=\"requestlevel\" otherpara=\"" + this.user.getLanguage() + "\" transmethod=\"weaver.general.WorkFlowTransMethod.getWFSearchResultUrgencyDegree\"/>";
/* 200 */     str21 = str21 + "</head></table>";
/*     */     
/* 202 */     String str23 = str20 + "_" + Util.getEncrypt(Util.getRandom());
/* 203 */     Util_TableMap.setVal(str23, str21);
/* 204 */     hashMap.put("sessionkey", str23);
/*     */     
/* 206 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarFlowListCondition() {
/* 215 */     RecordSet recordSet = new RecordSet();
/* 216 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/* 219 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 223 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 224 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 225 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(20331, this.language));
/* 226 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*     */ 
/*     */     
/* 229 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 231 */     ConditionType conditionType = ConditionType.INPUT;
/* 232 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 1334, "requestname", true)));
/*     */ 
/*     */ 
/*     */     
/* 236 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 237 */     arrayList2.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.language), true));
/* 238 */     String str1 = " SELECT id,workflowname FROM workflow_base  WHERE (formid = 163 OR formid IN ( SELECT formid FROM carbasic WHERE isuse = 1 ))  AND id NOT IN ( SELECT workflowid \tFROM carbasic WHERE isuse = 0 )  AND isbill = '1' ";
/* 239 */     recordSet.execute(str1);
/* 240 */     while (recordSet.next()) {
/* 241 */       String str3 = recordSet.getString("id");
/* 242 */       String str4 = recordSet.getString("workflowname");
/* 243 */       arrayList2.add(new SearchConditionOption(str3, str4));
/*     */     } 
/* 245 */     conditionType = ConditionType.SELECT;
/* 246 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 259, "workflowid", arrayList2)));
/*     */ 
/*     */     
/* 249 */     conditionType = ConditionType.BROWSER;
/* 250 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 882, "creater", "1")));
/*     */ 
/*     */     
/* 253 */     conditionType = ConditionType.DATE;
/* 254 */     String str2 = "createdate";
/* 255 */     String[] arrayOfString = { str2 + "_select", str2 + "_start", str2 + "_end" };
/* 256 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 722, arrayOfString)));
/*     */ 
/*     */     
/* 259 */     conditionType = ConditionType.INPUT;
/* 260 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 19502, "wfcode", false)));
/*     */ 
/*     */     
/* 263 */     ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 264 */     arrayList3.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.language), true));
/* 265 */     arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(225, this.language)));
/* 266 */     arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15533, this.language)));
/* 267 */     arrayList3.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(2087, this.language)));
/* 268 */     conditionType = ConditionType.SELECT;
/* 269 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 15534, "requestlevel", arrayList3)));
/*     */ 
/*     */ 
/*     */     
/* 273 */     hashMap2.put("items", arrayList1);
/* 274 */     arrayList.add(hashMap2);
/*     */ 
/*     */     
/* 277 */     hashMap1.put("conditioninfo", arrayList);
/* 278 */     hashMap1.put("status", "1");
/* 279 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/service/CarFlowService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */