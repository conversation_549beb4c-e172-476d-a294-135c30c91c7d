/*     */ package com.api.car.service;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.service.impl.FormmodeBrowserService;
/*     */ import com.api.browser.util.ConditionFactory;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.api.car.util.CarSetInfo;
/*     */ import com.api.car.util.CarUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarSearchService
/*     */   extends BaseBean
/*     */ {
/*     */   private User user;
/*  33 */   private int language = 7;
/*     */   
/*     */   private HttpServletRequest request;
/*     */   private HttpServletResponse response;
/*     */   
/*     */   public CarSearchService() {}
/*     */   
/*     */   public CarSearchService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  41 */     init(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void init(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  51 */     this.request = paramHttpServletRequest;
/*  52 */     this.response = paramHttpServletResponse;
/*  53 */     this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  54 */     if (this.user != null) {
/*  55 */       this.language = this.user.getLanguage();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarSearchList(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  61 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  62 */     RecordSet recordSet1 = new RecordSet();
/*  63 */     RecordSet recordSet2 = new RecordSet();
/*  64 */     int i = this.user.getUID();
/*  65 */     int j = this.user.getUserSubCompany1();
/*  66 */     String str1 = " where 1=1";
/*  67 */     CarSetInfo carSetInfo = new CarSetInfo();
/*  68 */     if (carSetInfo.getIsShare() != 1) {
/*  69 */       recordSet1.execute("select carsdetachable from SystemSet");
/*  70 */       int m = 0;
/*  71 */       if (recordSet1.next()) {
/*  72 */         m = recordSet1.getInt(1);
/*     */       }
/*     */       
/*  75 */       if (m == 1) {
/*  76 */         if (!"".equals(Util.null2String(paramHttpServletRequest.getParameter("subCompanyId")))) {
/*  77 */           j = Util.getIntValue(paramHttpServletRequest.getParameter("subCompanyId"));
/*     */         }
/*  79 */         if (i != 1) {
/*  80 */           str1 = str1 + CarUtil.getDetachSql(this.user);
/*     */         }
/*     */       } else {
/*  83 */         j = -1;
/*     */       } 
/*     */     } 
/*  86 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("carNo"));
/*  87 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("flowTitle2"));
/*  88 */     String[] arrayOfString = paramHttpServletRequest.getParameterValues("carType");
/*  89 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("factoryNo"));
/*  90 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("driver"));
/*  91 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("subCompanyId"));
/*     */     
/*  93 */     FormmodeBrowserService formmodeBrowserService = new FormmodeBrowserService();
/*  94 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("buydate_select"));
/*  95 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("buydate_start"));
/*  96 */     String str9 = Util.null2String(paramHttpServletRequest.getParameter("buydate_end"));
/*  97 */     Map map = FormmodeBrowserService.getDateRangeByDateField(str7, str8, str9);
/*  98 */     str8 = (String)map.get("startdate");
/*  99 */     str9 = (String)map.get("enddate");
/*     */     
/* 101 */     String str10 = "";
/*     */     
/* 103 */     if (!str2.equals("")) {
/* 104 */       str1 = str1 + " and carNo like '%" + str2 + "%'";
/*     */     }
/* 106 */     else if (!"".equals(str3)) {
/* 107 */       str1 = str1 + " and carNo like '%" + str3 + "%'";
/*     */     } 
/*     */     
/* 110 */     if (arrayOfString != null && arrayOfString.length > 0) {
/* 111 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 112 */         String str = arrayOfString[b1];
/* 113 */         if (!StringHelper.isEmpty(str)) {
/* 114 */           str10 = str10 + "," + str;
/*     */         }
/*     */       } 
/* 117 */       if (!StringHelper.isEmpty(str10)) {
/* 118 */         str10 = str10.substring(1);
/* 119 */         str1 = str1 + " and carType in (" + str10 + ")";
/*     */       } 
/*     */     } 
/* 122 */     if (!str4.equals("")) {
/* 123 */       str1 = str1 + " and factoryNo like '%" + str4 + "%'";
/*     */     }
/* 125 */     if (!str8.equals("")) {
/* 126 */       str1 = str1 + " and buyDate >= '" + str8 + "'";
/*     */     }
/* 128 */     if (!str9.equals("")) {
/* 129 */       str1 = str1 + " and buyDate <= '" + str9 + "'";
/*     */     }
/* 131 */     if (!str5.equals("")) {
/* 132 */       str1 = str1 + " and driver ='" + str5 + "'";
/*     */     }
/* 134 */     if (!str6.equals("")) {
/* 135 */       str1 = str1 + " and subcompanyid ='" + str6 + "'";
/*     */     }
/* 137 */     str1 = str1 + CarUtil.getCarShareSql(this.user);
/* 138 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("pagenum"), 1);
/* 139 */     byte b = 10;
/*     */     
/* 141 */     String str11 = "id, factoryNo, carNo, carType, driver, buyDate,subCompanyId,showorder";
/* 142 */     String str12 = "from CarInfo a";
/* 143 */     String str13 = str1;
/* 144 */     String str14 = "showorder asc,id desc";
/* 145 */     String str15 = "";
/* 146 */     String str16 = "Car123456";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 160 */     str15 = " <table pageUid=\"" + str16 + "\" instanceid=\"CarTable\" tabletype=\"none\" pagesize=\"" + b + "\" >\t\t<sql backfields=\"" + str11 + "\" sqlform=\"" + str12 + "\" sqlwhere=\"" + Util.toHtmlForSplitPage(str13) + "\"  sqlorderby=\"" + str14 + "\"  sqlprimarykey=\"id\" sqlsortway=\"desc\" sqlisdistinct=\"true\"/>\t\t<head>\t\t\t<col width=\"17%\"  text=\"" + SystemEnv.getHtmlLabelName(20318, this.user.getLanguage()) + "\" column=\"factoryNo\" orderkey=\"factoryNo\" />\t\t\t<col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(20319, this.user.getLanguage()) + "\" column=\"carNo\"  orderkey=\"carNo\" />\t\t\t<col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(63, this.user.getLanguage()) + "\" column=\"carType\" orderkey=\"carType\" transmethod=\"weaver.car.CarTypeComInfo.getCarTypename\" />\t\t\t<col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(17649, this.user.getLanguage()) + "\" column=\"driver\"  orderkey=\"driver\" transmethod=\"weaver.hrm.resource.ResourceComInfo.getResourcename\" />\t\t\t<col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(17868, this.user.getLanguage()) + "\" column=\"subCompanyId\"  orderkey=\"subCompanyId\" otherpara=\"" + this.user.getLanguage() + "\" transmethod=\"com.api.car.util.CarUtil.getSubCompanyname\" />\t\t\t<col width=\"17%\"   text=\"" + SystemEnv.getHtmlLabelName(16914, this.user.getLanguage()) + "\" column=\"buyDate\"  orderkey=\"buyDate\" />\t\t</head></table>";
/*     */ 
/*     */ 
/*     */     
/* 164 */     String str17 = str16 + "_" + Util.getEncrypt(Util.getRandom());
/* 165 */     Util_TableMap.setVal(str17, str15);
/* 166 */     hashMap.put("sessionkey", str17);
/* 167 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getCarSearchListCondition() {
/* 175 */     RecordSet recordSet = new RecordSet();
/* 176 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/* 179 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 183 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 184 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/* 185 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(20331, this.language));
/* 186 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*     */ 
/*     */     
/* 189 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*     */     
/* 191 */     ConditionType conditionType = ConditionType.INPUT;
/* 192 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 20319, "carNo", true)));
/*     */ 
/*     */     
/* 195 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 196 */     arrayList2.add(new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, this.language), true));
/* 197 */     recordSet.executeProc("CarType_Select", "");
/* 198 */     while (recordSet.next()) {
/* 199 */       String str1 = recordSet.getString("id");
/* 200 */       String str2 = recordSet.getString("name");
/* 201 */       arrayList2.add(new SearchConditionOption(str1, str2));
/*     */     } 
/* 203 */     conditionType = ConditionType.SELECT;
/* 204 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 17630, "carType", arrayList2)));
/*     */ 
/*     */     
/* 207 */     conditionType = ConditionType.INPUT;
/* 208 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 20318, "factoryNo", false)));
/*     */ 
/*     */     
/* 211 */     conditionType = ConditionType.DATE;
/* 212 */     String str = "buydate";
/* 213 */     String[] arrayOfString = { str + "_select", str + "_start", str + "_end" };
/* 214 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 16914, arrayOfString)));
/*     */ 
/*     */     
/* 217 */     conditionType = ConditionType.BROWSER;
/* 218 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 17649, "driver", "1")));
/*     */ 
/*     */     
/* 221 */     conditionType = ConditionType.BROWSER;
/* 222 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 17868, "subCompanyId", "164")));
/*     */ 
/*     */     
/* 225 */     hashMap2.put("items", arrayList1);
/* 226 */     arrayList.add(hashMap2);
/*     */ 
/*     */     
/* 229 */     hashMap1.put("conditioninfo", arrayList);
/* 230 */     hashMap1.put("status", "1");
/* 231 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/service/CarSearchService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */