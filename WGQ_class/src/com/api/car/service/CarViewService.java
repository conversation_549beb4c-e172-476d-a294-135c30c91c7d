/*      */ package com.api.car.service;
/*      */ 
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SplitTableBean;
/*      */ import com.api.browser.bean.SplitTableColBean;
/*      */ import com.api.browser.util.BrowserConstant;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import com.api.browser.util.SplitTableUtil;
/*      */ import com.api.car.util.CarSetInfo;
/*      */ import com.api.car.util.CarUtil;
/*      */ import com.weaver.formmodel.util.DateHelper;
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import weaver.car.CarDateTimeUtil;
/*      */ import weaver.car.CarInfoComInfo;
/*      */ import weaver.car.CarInfoReport;
/*      */ import weaver.car.CarTypeComInfo;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.dateformat.DateTransformer;
/*      */ import weaver.dateformat.UnifiedConversionInterface;
/*      */ import weaver.formmode.exttools.impexp.common.CodeUtils;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.meeting.Maint.MeetingSetInfo;
/*      */ import weaver.meeting.MeetingShareUtil;
/*      */ import weaver.meeting.defined.MeetingFieldComInfo;
/*      */ import weaver.share.ShareManager;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*      */ import weaver.workflow.form.FormManager;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CarViewService
/*      */   extends BaseBean
/*      */ {
/*      */   private User user;
/*   59 */   private int language = 7;
/*      */   
/*      */   private HttpServletRequest request;
/*      */   private HttpServletResponse response;
/*      */   
/*      */   public CarViewService() {}
/*      */   
/*      */   public CarViewService(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*   67 */     init(paramHttpServletRequest, paramHttpServletResponse);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void init(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*   77 */     this.request = paramHttpServletRequest;
/*   78 */     this.response = paramHttpServletResponse;
/*   79 */     this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*   80 */     if (this.user != null) {
/*   81 */       this.language = this.user.getLanguage();
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getCarViewSearchListCondition() {
/*   91 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */ 
/*      */     
/*   94 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */ 
/*      */ 
/*      */     
/*   98 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*   99 */     ArrayList<SearchConditionItem> arrayList1 = new ArrayList();
/*  100 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(20331, this.language));
/*  101 */     hashMap2.put("defaultshow", Boolean.valueOf(true));
/*      */ 
/*      */     
/*  104 */     ConditionFactory conditionFactory = new ConditionFactory(this.user);
/*      */     
/*  106 */     ConditionType conditionType = ConditionType.INPUT;
/*  107 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 20319, "carNo", true)));
/*      */ 
/*      */     
/*  110 */     conditionType = ConditionType.BROWSER;
/*  111 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 17868, "subId", "164")));
/*      */ 
/*      */     
/*  114 */     conditionType = ConditionType.INPUT;
/*  115 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(conditionType, 17666, "bywhat", false);
/*  116 */     searchConditionItem1.setValue("4");
/*  117 */     arrayList1.add(CarUtil.castSearchConditionItem(searchConditionItem1));
/*      */ 
/*      */     
/*  120 */     conditionType = ConditionType.INPUT;
/*  121 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(conditionType, 15625, "currentdate", false);
/*  122 */     String str = DateHelper.getCurrentDate();
/*  123 */     searchConditionItem2.setValue(str);
/*  124 */     arrayList1.add(CarUtil.castSearchConditionItem(searchConditionItem2));
/*      */ 
/*      */     
/*  127 */     conditionType = ConditionType.INPUT;
/*  128 */     arrayList1.add(CarUtil.castSearchConditionItem(conditionFactory.createCondition(conditionType, 920, "carId", false)));
/*      */ 
/*      */     
/*  131 */     hashMap2.put("items", arrayList1);
/*  132 */     arrayList.add(hashMap2);
/*      */ 
/*      */     
/*  135 */     hashMap1.put("conditioninfo", arrayList);
/*  136 */     hashMap1.put("status", "1");
/*  137 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCarReportData(User paramUser, int paramInt1, int paramInt2, String paramString1, String paramString2) throws Exception {
/*  152 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  153 */     CarSetInfo carSetInfo1 = new CarSetInfo();
/*  154 */     String str1 = carSetInfo1.getUsedColor();
/*  155 */     String str2 = carSetInfo1.getAgreementColor();
/*  156 */     String str3 = carSetInfo1.getConflictedColor();
/*  157 */     String str4 = carSetInfo1.getUsedColorFont();
/*  158 */     String str5 = carSetInfo1.getAgreementColorFont();
/*  159 */     String str6 = carSetInfo1.getConflictedColorFont();
/*      */     
/*  161 */     CarInfoComInfo carInfoComInfo = new CarInfoComInfo();
/*      */     
/*  163 */     if (str1.equals("")) str1 = "E3F6D8"; 
/*  164 */     if (str2.equals("")) str2 = "FFE4C4"; 
/*  165 */     if (str3.equals("")) str3 = "FBDFEB";
/*      */     
/*  167 */     int i = carSetInfo1.getDspUnit();
/*  168 */     Calendar calendar1 = Calendar.getInstance();
/*  169 */     Calendar calendar2 = Calendar.getInstance();
/*  170 */     Calendar calendar3 = Calendar.getInstance();
/*      */     
/*  172 */     if (paramString2.equals("")) {
/*  173 */       String[] arrayOfString = (new DateTransformer()).getLocaleDateAndTime(DateHelper.getCurDateTime());
/*  174 */       paramString2 = arrayOfString[0];
/*      */     } 
/*      */     
/*  177 */     if (!paramString2.equals("")) {
/*  178 */       int i1 = Util.getIntValue(paramString2.substring(0, 4));
/*  179 */       int i2 = Util.getIntValue(paramString2.substring(5, 7)) - 1;
/*  180 */       int i3 = Util.getIntValue(paramString2.substring(8, 10));
/*  181 */       calendar1.set(i1, i2, i3);
/*      */     } 
/*      */     
/*  184 */     int j = calendar1.get(1);
/*  185 */     int k = calendar1.get(2);
/*  186 */     int m = calendar1.get(5);
/*      */ 
/*      */     
/*  189 */     j = calendar1.get(1);
/*  190 */     k = calendar1.get(2) + 1;
/*  191 */     m = calendar1.get(5);
/*  192 */     if (paramInt1 == 2) {
/*  193 */       paramString2 = Util.add0(j, 4) + "-" + Util.add0(k, 2);
/*      */     } else {
/*  195 */       paramString2 = Util.add0(j, 4) + "-" + Util.add0(k, 2) + "-" + Util.add0(m, 2);
/*      */     } 
/*  197 */     calendar2.set(j, k - 1, m);
/*  198 */     calendar3.set(j, k - 1, m);
/*  199 */     Calendar calendar4 = Calendar.getInstance();
/*  200 */     calendar4.set(j, k - 1, m);
/*  201 */     calendar4.add(2, 1);
/*  202 */     calendar4.set(5, 1);
/*  203 */     calendar4.add(5, -1);
/*  204 */     int n = calendar4.get(5);
/*  205 */     switch (paramInt1) {
/*      */       case 2:
/*  207 */         calendar1.add(2, 1);
/*      */         break;
/*      */       case 3:
/*  210 */         calendar1.add(3, 1);
/*      */         break;
/*      */       case 4:
/*  213 */         calendar1.add(5, 1);
/*      */         break;
/*      */     } 
/*      */     
/*  217 */     j = calendar1.get(1);
/*  218 */     k = calendar1.get(2) + 1;
/*  219 */     m = calendar1.get(5);
/*  220 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  221 */     CarInfoReport carInfoReport = new CarInfoReport();
/*  222 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  230 */     ArrayList<String> arrayList1 = new ArrayList();
/*  231 */     ArrayList<String> arrayList2 = new ArrayList();
/*      */     
/*  233 */     RecordSet recordSet1 = new RecordSet();
/*  234 */     RecordSet recordSet2 = new RecordSet();
/*  235 */     byte b = 2;
/*  236 */     String str7 = "";
/*      */     
/*  238 */     CarSetInfo carSetInfo2 = new CarSetInfo();
/*  239 */     if (carSetInfo2.getIsShare() != 1) {
/*  240 */       recordSet1.execute("select carsdetachable from SystemSet");
/*  241 */       int i1 = 0;
/*  242 */       if (recordSet1.next()) {
/*  243 */         i1 = recordSet1.getInt(1);
/*      */       }
/*  245 */       if (i1 == 1) {
/*  246 */         if (!"".equals(Util.null2String(this.request.getParameter("subids")))) {
/*  247 */           paramInt2 = Util.getIntValue(this.request.getParameter("subids"));
/*      */         }
/*      */         
/*  250 */         if (paramUser.getUID() != 1) {
/*  251 */           String str9 = "";
/*  252 */           String str10 = "";
/*  253 */           recordSet2.executeProc("HrmRoleSR_SeByURId", "" + paramUser.getUID() + b + "Car:Maintenance");
/*  254 */           int i2 = -1;
/*  255 */           CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  256 */           while (recordSet2.next()) {
/*  257 */             str10 = Util.null2String(recordSet2.getString("subcompanyid"));
/*  258 */             i2 = checkSubCompanyRight.ChkComRightByUserRightCompanyId(paramUser.getUID(), "Car:Maintenance", Util.getIntValue(str10, -1));
/*  259 */             if (i2 == -1) {
/*      */               continue;
/*      */             }
/*  262 */             str9 = str9 + ", " + str10;
/*      */           } 
/*  264 */           if (!"".equals(str9)) {
/*  265 */             str9 = str9.substring(1);
/*  266 */             str7 = str7 + " and subcompanyid in (" + str9 + ") ";
/*      */           } else {
/*  268 */             str7 = str7 + " and subcompanyid=" + paramUser.getUserSubCompany1();
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  275 */     if (!"".equals(paramString1.trim())) {
/*  276 */       str7 = str7 + " and c.carNo like '%" + paramString1 + "%' ";
/*      */     }
/*  278 */     if (paramInt2 > 0) {
/*  279 */       if (paramUser.getUID() == 1) {
/*  280 */         if (paramInt2 > 0) {
/*  281 */           str7 = str7 + " and subCompanyId=" + paramInt2;
/*      */         } else {
/*  283 */           str7 = str7 + " and subCompanyId>=0";
/*      */         }
/*      */       
/*      */       }
/*  287 */       else if (paramInt2 > 0) {
/*      */         
/*  289 */         str7 = str7 + " and subCompanyId=" + paramInt2;
/*      */       } else {
/*  291 */         str7 = str7 + " and subCompanyId>0";
/*      */       } 
/*      */     }
/*      */     
/*  295 */     str7 = str7 + CarUtil.getCarShareSql(paramUser).replace("a.", "c.");
/*  296 */     String str8 = "select id,carno from CarInfo c where (deposit<>'1' or deposit is null) " + str7 + " order by showorder asc,id desc";
/*  297 */     recordSet1.execute(str8);
/*  298 */     while (recordSet1.next()) {
/*  299 */       String str9 = recordSet1.getString("id");
/*  300 */       String str10 = recordSet1.getString("carno");
/*  301 */       String str11 = "";
/*  302 */       arrayList1.add(str9);
/*  303 */       arrayList2.add(str10);
/*  304 */       List list = getCarTitle("" + str9, carInfoComInfo, paramUser);
/*  305 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  306 */       hashMap.put("id", str9);
/*  307 */       hashMap.put("name", str10);
/*  308 */       hashMap.put("title", list);
/*  309 */       arrayList.add(hashMap);
/*      */     } 
/*      */ 
/*      */     
/*  313 */     if (paramInt1 == 2) {
/*  314 */       ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/*  315 */       for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  316 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  317 */         String str9 = arrayList1.get(b1);
/*  318 */         String str10 = arrayList2.get(b1);
/*  319 */         if (paramInt1 == 3) {
/*  320 */           String str = CarUtil.getFirstDayOfWeek(paramString2);
/*  321 */           hashMap2 = carInfoReport.getMapping(str, paramInt1, arrayList1.get(b1));
/*      */         } else {
/*  323 */           hashMap2 = carInfoReport.getMapping(paramString2, paramInt1, arrayList1.get(b1));
/*      */         } 
/*  325 */         HashMap hashMap3 = (HashMap)hashMap2.get(arrayList1.get(b1));
/*  326 */         ArrayList arrayList4 = (ArrayList)hashMap3.get("ids");
/*      */ 
/*      */         
/*  329 */         ArrayList<String> arrayList5 = (ArrayList)hashMap3.get("startDates");
/*  330 */         ArrayList<String> arrayList6 = (ArrayList)hashMap3.get("startTimes");
/*  331 */         ArrayList<String> arrayList7 = (ArrayList)hashMap3.get("drivers");
/*  332 */         ArrayList<String> arrayList8 = (ArrayList)hashMap3.get("userids");
/*  333 */         ArrayList<String> arrayList9 = (ArrayList)hashMap3.get("endTimes");
/*  334 */         ArrayList<String> arrayList10 = (ArrayList)hashMap3.get("endDates");
/*  335 */         ArrayList<String> arrayList11 = (ArrayList)hashMap3.get("cancels");
/*      */         
/*  337 */         ArrayList<HashMap<Object, Object>> arrayList12 = new ArrayList();
/*  338 */         for (byte b2 = 0; b2 < n; b2++) {
/*      */           
/*  340 */           HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  341 */           String str11 = "";
/*  342 */           String str12 = "";
/*  343 */           ArrayList<List> arrayList13 = new ArrayList();
/*  344 */           byte b3 = 0;
/*  345 */           String str13 = paramString2 + "-" + Util.add0(b2 + 1, 2);
/*  346 */           String str14 = getDayOccupied(str13, arrayList5, arrayList6, arrayList10, arrayList9, arrayList11);
/*      */           
/*  348 */           ArrayList<HashMap<Object, Object>> arrayList14 = new ArrayList();
/*  349 */           for (byte b4 = 0; b4 < arrayList4.size(); b4++) {
/*      */             
/*  351 */             String str15 = arrayList7.get(b4);
/*  352 */             String str16 = arrayList8.get(b4);
/*  353 */             String str17 = arrayList6.get(b4);
/*  354 */             String str18 = arrayList9.get(b4);
/*  355 */             String str19 = arrayList5.get(b4);
/*  356 */             String str20 = arrayList10.get(b4);
/*  357 */             String str21 = arrayList11.get(b4);
/*  358 */             if (!str21.equals("1")) {
/*      */               
/*  360 */               String str22 = CarDateTimeUtil.getLocaleDate(str19, str17);
/*  361 */               String str23 = CarDateTimeUtil.getShortLocaleTime(str19, str17);
/*  362 */               String str24 = CarDateTimeUtil.getLocaleDate(str20, str18);
/*  363 */               String str25 = CarDateTimeUtil.getShortLocaleTime(str20, str18);
/*      */               
/*  365 */               if (str13.compareTo(str22) >= 0 && str13.compareTo(str24) <= 0) {
/*      */                 
/*  367 */                 b3++;
/*      */                 
/*  369 */                 arrayList13.add(getCarUseTitle(carInfoComInfo.getCarNo("" + str9), str15, str16, str22, str24, str23, str25, paramUser));
/*  370 */                 HashMap<Object, Object> hashMap5 = new HashMap<>();
/*  371 */                 hashMap5.put("id", arrayList4.get(b4));
/*  372 */                 hashMap5.put("name", carInfoComInfo.getCarNo("" + str9));
/*  373 */                 arrayList14.add(hashMap5);
/*      */               } 
/*      */             } 
/*  376 */           }  if ("2".equals(str14)) {
/*      */             
/*  378 */             str11 = "#" + str3;
/*  379 */             str12 = str6;
/*      */           }
/*  381 */           else if ("1".equals(str14)) {
/*      */             
/*  383 */             str11 = "#" + str1;
/*  384 */             str12 = str4;
/*      */           } 
/*  386 */           if (arrayList14.size() > 0) {
/*  387 */             hashMap4.put("date", str13);
/*  388 */             hashMap4.put("fontcolor", str12);
/*  389 */             hashMap4.put("bgcolor", str11);
/*  390 */             hashMap4.put("meetings", arrayList14);
/*  391 */             hashMap4.put("title", arrayList13);
/*  392 */             hashMap4.put("content", Integer.valueOf(b3));
/*  393 */             arrayList12.add(hashMap4);
/*      */           } 
/*      */         } 
/*  396 */         if (arrayList12.size() > 0) {
/*  397 */           HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  398 */           hashMap4.put("carid", str9);
/*  399 */           hashMap4.put("info", arrayList12);
/*  400 */           arrayList3.add(hashMap4);
/*      */         } 
/*      */       } 
/*  403 */       hashMap1.put("datas", arrayList3);
/*  404 */     } else if (paramInt1 == 3) {
/*  405 */       ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/*  406 */       for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  407 */         String str9 = arrayList1.get(b1);
/*  408 */         String str10 = arrayList2.get(b1);
/*  409 */         if (paramInt1 == 3) {
/*  410 */           String str = CarUtil.getFirstDayOfWeek(paramString2);
/*  411 */           hashMap2 = carInfoReport.getMapping(str, paramInt1, arrayList1.get(b1));
/*      */         } else {
/*  413 */           hashMap2 = carInfoReport.getMapping(paramString2, paramInt1, arrayList1.get(b1));
/*      */         } 
/*  415 */         HashMap hashMap = (HashMap)hashMap2.get(arrayList1.get(b1));
/*  416 */         ArrayList arrayList4 = (ArrayList)hashMap.get("ids");
/*  417 */         ArrayList<String> arrayList5 = (ArrayList)hashMap.get("drivers");
/*  418 */         ArrayList<String> arrayList6 = (ArrayList)hashMap.get("userids");
/*  419 */         ArrayList<String> arrayList7 = (ArrayList)hashMap.get("startTimes");
/*  420 */         ArrayList<String> arrayList8 = (ArrayList)hashMap.get("endTimes");
/*  421 */         ArrayList<String> arrayList9 = (ArrayList)hashMap.get("startDates");
/*  422 */         ArrayList<String> arrayList10 = (ArrayList)hashMap.get("endDates");
/*  423 */         ArrayList<String> arrayList11 = (ArrayList)hashMap.get("cancels");
/*      */         
/*  425 */         String str11 = CarUtil.getFirstDayOfWeek(paramString2);
/*  426 */         ArrayList<HashMap<Object, Object>> arrayList12 = new ArrayList();
/*  427 */         for (byte b2 = -1; b2 < 6; b2++) {
/*      */           
/*  429 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  430 */           String str12 = "";
/*  431 */           String str13 = "";
/*  432 */           ArrayList<List> arrayList13 = new ArrayList();
/*  433 */           byte b3 = 0;
/*  434 */           String str14 = TimeUtil.dateAdd(str11, b2);
/*      */           
/*  436 */           String str15 = getDayOccupied(str14, arrayList9, arrayList7, arrayList10, arrayList8, arrayList11);
/*      */           
/*  438 */           ArrayList<HashMap<Object, Object>> arrayList14 = new ArrayList();
/*  439 */           for (byte b4 = 0; b4 < arrayList4.size(); b4++) {
/*      */             
/*  441 */             String str16 = arrayList5.get(b4);
/*  442 */             String str17 = arrayList6.get(b4);
/*  443 */             String str18 = arrayList7.get(b4);
/*  444 */             String str19 = arrayList8.get(b4);
/*  445 */             String str20 = arrayList9.get(b4);
/*  446 */             String str21 = arrayList10.get(b4);
/*  447 */             String str22 = arrayList11.get(b4);
/*      */             
/*  449 */             String str23 = CarDateTimeUtil.getLocaleDate(str20, str18);
/*  450 */             String str24 = CarDateTimeUtil.getShortLocaleTime(str20, str18);
/*  451 */             String str25 = CarDateTimeUtil.getLocaleDate(str21, str19);
/*  452 */             String str26 = CarDateTimeUtil.getShortLocaleTime(str21, str19);
/*      */             
/*  454 */             if (!str22.equals("1") && 
/*  455 */               str14.compareTo(str23) >= 0 && str14.compareTo(str25) <= 0) {
/*      */               
/*  457 */               b3++;
/*  458 */               arrayList13.add(getCarUseTitle(carInfoComInfo.getCarNo("" + str9), str16, str17, str23, str25, str24, str26, paramUser));
/*  459 */               HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  460 */               hashMap4.put("id", arrayList4.get(b4));
/*  461 */               hashMap4.put("name", carInfoComInfo.getCarNo("" + str9));
/*  462 */               arrayList14.add(hashMap4);
/*      */             } 
/*      */           } 
/*  465 */           if ("2".equals(str15)) {
/*      */             
/*  467 */             str12 = "#" + str3;
/*  468 */             str13 = str6;
/*      */           }
/*  470 */           else if ("1".equals(str15)) {
/*      */             
/*  472 */             str12 = "#" + str1;
/*  473 */             str13 = str4;
/*      */           } 
/*  475 */           if (arrayList14.size() > 0) {
/*  476 */             hashMap3.put("date", str14);
/*  477 */             hashMap3.put("fontcolor", str13);
/*  478 */             hashMap3.put("bgcolor", str12);
/*  479 */             hashMap3.put("meetings", arrayList14);
/*  480 */             hashMap3.put("title", arrayList13);
/*  481 */             hashMap3.put("content", Integer.valueOf(b3));
/*  482 */             arrayList12.add(hashMap3);
/*      */           } 
/*      */         } 
/*  485 */         if (arrayList12.size() > 0) {
/*  486 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  487 */           hashMap3.put("carid", str9);
/*  488 */           hashMap3.put("info", arrayList12);
/*  489 */           arrayList3.add(hashMap3);
/*      */         } 
/*      */       } 
/*  492 */       hashMap1.put("datas", arrayList3);
/*  493 */     } else if (paramInt1 == 4) {
/*  494 */       ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/*  495 */       for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  496 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  497 */         String str9 = arrayList1.get(b1);
/*  498 */         String str10 = arrayList2.get(b1);
/*  499 */         if (paramInt1 == 3) {
/*  500 */           String str = CarUtil.getFirstDayOfWeek(paramString2);
/*  501 */           hashMap2 = carInfoReport.getMapping(str, paramInt1, arrayList1.get(b1));
/*      */         } else {
/*  503 */           hashMap2 = carInfoReport.getMapping(paramString2, paramInt1, arrayList1.get(b1));
/*      */         } 
/*  505 */         HashMap hashMap3 = (HashMap)hashMap2.get(arrayList1.get(b1));
/*      */         
/*  507 */         ArrayList arrayList4 = (ArrayList)hashMap3.get("ids");
/*  508 */         ArrayList<String> arrayList5 = (ArrayList)hashMap3.get("drivers");
/*  509 */         ArrayList<String> arrayList6 = (ArrayList)hashMap3.get("userids");
/*  510 */         ArrayList<String> arrayList7 = (ArrayList)hashMap3.get("startTimes");
/*  511 */         ArrayList<String> arrayList8 = (ArrayList)hashMap3.get("endTimes");
/*  512 */         ArrayList<String> arrayList9 = (ArrayList)hashMap3.get("startDates");
/*  513 */         ArrayList<String> arrayList10 = (ArrayList)hashMap3.get("endDates");
/*  514 */         ArrayList<String> arrayList11 = (ArrayList)hashMap3.get("cancels");
/*      */         
/*  516 */         ArrayList<HashMap<Object, Object>> arrayList12 = new ArrayList();
/*  517 */         for (int i1 = carSetInfo1.getTimeRangeStart() * i; i1 < (carSetInfo1.getTimeRangeEnd() + 1) * i; i1++) {
/*      */           
/*  519 */           HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  520 */           String str11 = "";
/*  521 */           String str12 = "";
/*  522 */           ArrayList<List> arrayList13 = new ArrayList();
/*      */           
/*  524 */           String str13 = (i == 1) ? (Util.add0(i1, 2) + ":00") : CarUtil.getTimesBg(i1 - 1, i);
/*      */ 
/*      */           
/*  527 */           String str14 = paramString2 + " " + ((i == 1) ? (Util.add0(i1, 2) + ":00") : CarUtil.getTimesBg(i1 - 1, i));
/*  528 */           String str15 = paramString2 + " " + ((i == 1) ? (Util.add0(i1, 2) + ":59") : CarUtil.getTimesEd(i1, i));
/*  529 */           String str16 = getHourOccupied(paramString2, "" + i1, arrayList9, arrayList7, arrayList10, arrayList8, arrayList11, i);
/*      */ 
/*      */           
/*  532 */           byte b2 = 0;
/*  533 */           ArrayList<HashMap<Object, Object>> arrayList14 = new ArrayList();
/*  534 */           for (byte b3 = 0; b3 < arrayList4.size(); b3++) {
/*      */             
/*  536 */             String str17 = arrayList5.get(b3);
/*  537 */             String str18 = arrayList6.get(b3);
/*  538 */             String str19 = arrayList7.get(b3);
/*  539 */             String str20 = arrayList8.get(b3);
/*  540 */             String str21 = arrayList9.get(b3);
/*  541 */             String str22 = arrayList10.get(b3);
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  546 */             String str23 = CarDateTimeUtil.getLocaleDateTime(str21, str19);
/*  547 */             String str24 = CarDateTimeUtil.getLocaleDateTime(str22, str20);
/*  548 */             String str25 = CarDateTimeUtil.getLocaleDate(str21, str19);
/*  549 */             String str26 = CarDateTimeUtil.getShortLocaleTime(str21, str19);
/*  550 */             String str27 = CarDateTimeUtil.getLocaleDate(str22, str20);
/*  551 */             String str28 = CarDateTimeUtil.getShortLocaleTime(str22, str20);
/*      */             
/*  553 */             String str29 = arrayList11.get(b3);
/*  554 */             if (!str29.equals("1") && 
/*  555 */               str15.compareTo(str23) >= 0 && str14.compareTo(str24) <= 0) {
/*      */               
/*  557 */               b2++;
/*      */               
/*  559 */               arrayList13.add(getCarUseTitle(carInfoComInfo.getCarNo("" + str9), str17, str18, str25, str27, str26, str28, paramUser));
/*  560 */               HashMap<Object, Object> hashMap5 = new HashMap<>();
/*  561 */               hashMap5.put("id", arrayList4.get(b3));
/*  562 */               hashMap5.put("name", carInfoComInfo.getCarNo("" + str9));
/*  563 */               arrayList14.add(hashMap5);
/*      */             } 
/*      */           } 
/*  566 */           if ("2".equals(str16)) {
/*      */             
/*  568 */             str11 = "#" + str3;
/*  569 */             str12 = str6;
/*      */           }
/*  571 */           else if ("1".equals(str16)) {
/*      */             
/*  573 */             str11 = "#" + str1;
/*  574 */             str12 = str4;
/*      */           } 
/*  576 */           if (arrayList14.size() > 0) {
/*  577 */             hashMap4.put("time", str13);
/*  578 */             hashMap4.put("fontcolor", str12);
/*  579 */             hashMap4.put("bgcolor", str11);
/*  580 */             hashMap4.put("meetings", arrayList14);
/*  581 */             hashMap4.put("title", arrayList13);
/*  582 */             hashMap4.put("content", Integer.valueOf(b2));
/*  583 */             arrayList12.add(hashMap4);
/*      */           } 
/*      */         } 
/*  586 */         if (arrayList12.size() > 0) {
/*  587 */           HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  588 */           hashMap4.put("carid", str9);
/*  589 */           hashMap4.put("info", arrayList12);
/*  590 */           arrayList3.add(hashMap4);
/*      */         } 
/*      */       } 
/*  593 */       hashMap1.put("datas", arrayList3);
/*  594 */       hashMap1.put("dspUnit", Integer.valueOf(i));
/*      */     } 
/*  596 */     hashMap1.put("bywhat", Integer.valueOf(paramInt1));
/*  597 */     hashMap1.put("cars", arrayList);
/*  598 */     return JSONObject.toJSONString(hashMap1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List getCarTitle(String paramString, CarInfoComInfo paramCarInfoComInfo, User paramUser) throws Exception {
/*  610 */     CarTypeComInfo carTypeComInfo = new CarTypeComInfo();
/*  611 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  612 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/*  614 */     String str1 = paramCarInfoComInfo.getCarNo("" + paramString);
/*  615 */     String str2 = paramCarInfoComInfo.getCarType("" + paramString);
/*  616 */     String str3 = "";
/*  617 */     if (!StringHelper.isEmpty(str2)) {
/*  618 */       str3 = carTypeComInfo.getCarTypename(str2);
/*      */     }
/*  620 */     String str4 = paramCarInfoComInfo.getFactoryNo("" + paramString);
/*  621 */     String str5 = paramCarInfoComInfo.getUsefee("" + paramString);
/*  622 */     String str6 = paramCarInfoComInfo.getSubcompanyid("" + paramString);
/*  623 */     String str7 = "";
/*  624 */     if (!StringHelper.isEmpty(str6)) {
/*  625 */       str7 = Util.toScreen(subCompanyComInfo.getSubCompanyname(str6), paramUser.getLanguage());
/*      */     }
/*  627 */     if (!StringHelper.isEmpty(str7)) {
/*  628 */       str7 = Util.formatMultiLang(str7, paramUser.getLanguage() + "");
/*      */     }
/*      */     
/*  631 */     arrayList.add(SystemEnv.getHtmlLabelName(20319, paramUser.getLanguage()) + "：" + str1);
/*  632 */     arrayList.add(SystemEnv.getHtmlLabelName(17868, paramUser.getLanguage()) + "：" + str7);
/*  633 */     arrayList.add(SystemEnv.getHtmlLabelName(17630, paramUser.getLanguage()) + "：" + str3);
/*  634 */     arrayList.add(SystemEnv.getHtmlLabelName(1491, paramUser.getLanguage()) + "：" + str5);
/*  635 */     arrayList.add(SystemEnv.getHtmlLabelName(20318, paramUser.getLanguage()) + "：" + str4);
/*      */     
/*  637 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDayOccupied(String paramString, List<String> paramList1, List<String> paramList2, List<String> paramList3, List<String> paramList4, List<String> paramList5) {
/*  652 */     String[] arrayOfString = new String[1440]; byte b;
/*  653 */     for (b = 0; b < paramList1.size(); b++) {
/*      */       
/*  655 */       String str1 = paramList1.get(b);
/*  656 */       String str2 = paramList2.get(b);
/*  657 */       String str3 = paramList3.get(b);
/*  658 */       String str4 = paramList4.get(b);
/*  659 */       String str5 = paramList5.get(b);
/*  660 */       str1 = CarDateTimeUtil.getLocaleDate(str1, str2);
/*  661 */       str3 = CarDateTimeUtil.getLocaleDate(str3, str4);
/*  662 */       if (!"1".equals(str5) && str1.compareTo(paramString) <= 0 && paramString.compareTo(str3) <= 0) {
/*      */         
/*  664 */         if (str1.compareTo(paramString) < 0)
/*      */         {
/*  666 */           str2 = "00:00";
/*      */         }
/*  668 */         if (paramString.compareTo(str3) < 0)
/*      */         {
/*  670 */           str4 = "23:59";
/*      */         }
/*  672 */         int i = CarUtil.getMinuteOfDay(str2) + 1;
/*  673 */         int j = CarUtil.getMinuteOfDay(str4);
/*  674 */         while (i < j) {
/*      */           
/*  676 */           if ("1".equals(arrayOfString[i]))
/*      */           {
/*  678 */             return "2";
/*      */           }
/*      */ 
/*      */           
/*  682 */           arrayOfString[i] = "1";
/*      */           
/*  684 */           i++;
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  689 */     for (b = 0; b < '֠'; b++) {
/*      */       
/*  691 */       if ("1".equals(arrayOfString[b]))
/*      */       {
/*  693 */         return "1";
/*      */       }
/*      */     } 
/*  696 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getHourOccupied(String paramString1, String paramString2, List<String> paramList1, List<String> paramList2, List<String> paramList3, List<String> paramList4, List<String> paramList5, int paramInt) {
/*  714 */     String[] arrayOfString = new String[1440];
/*  715 */     String str1 = (paramInt == 1) ? (((paramString2.length() == 1) ? ("0" + paramString2) : paramString2) + ":00") : CarUtil.getTimesBg(Util.getIntValue(paramString2) - 1, paramInt);
/*  716 */     String str2 = (paramInt == 1) ? (((paramString2.length() == 1) ? ("0" + paramString2) : paramString2) + ":59") : CarUtil.getTimesEd(Util.getIntValue(paramString2), paramInt);
/*      */     byte b;
/*  718 */     for (b = 0; b < paramList1.size(); b++) {
/*      */       
/*  720 */       String str3 = paramList1.get(b);
/*  721 */       String str4 = paramList2.get(b);
/*  722 */       String str5 = paramList3.get(b);
/*  723 */       String str6 = paramList4.get(b);
/*  724 */       String str7 = paramList5.get(b);
/*      */       
/*  726 */       str3 = CarDateTimeUtil.getLocaleDate(str3, str4);
/*  727 */       str4 = CarDateTimeUtil.getLocaleTime(str3, str4);
/*  728 */       str5 = CarDateTimeUtil.getLocaleDate(str5, str6);
/*  729 */       str6 = CarDateTimeUtil.getLocaleTime(str5, str6);
/*      */ 
/*      */ 
/*      */       
/*  733 */       if (!"1".equals(str7) && (str3
/*  734 */         .compareTo(paramString1) < 0 || (str3.compareTo(paramString1) == 0 && str4.compareTo(str2) <= 0)) && (paramString1
/*  735 */         .compareTo(str5) < 0 || (paramString1.compareTo(str5) == 0 && str1.compareTo(str6) <= 0))) {
/*      */ 
/*      */         
/*  738 */         if (str3.compareTo(paramString1) < 0 || str4.compareTo(str1) < 0)
/*      */         {
/*  740 */           str4 = str1;
/*      */         }
/*  742 */         if (paramString1.compareTo(str5) < 0 || str2.compareTo(str6) <= 0)
/*      */         {
/*  744 */           str6 = str2;
/*      */         }
/*      */         
/*  747 */         int i = CarUtil.getMinuteOfDay(str4) + 1;
/*  748 */         int j = CarUtil.getMinuteOfDay(str6);
/*  749 */         while (i < j) {
/*      */           
/*  751 */           if ("1".equals(arrayOfString[i]))
/*      */           {
/*  753 */             return "2";
/*      */           }
/*      */ 
/*      */           
/*  757 */           arrayOfString[i] = "1";
/*      */           
/*  759 */           i++;
/*      */         } 
/*      */       } 
/*      */     } 
/*  763 */     for (b = 0; b < '֠'; b++) {
/*      */       
/*  765 */       if ("1".equals(arrayOfString[b]))
/*      */       {
/*  767 */         return "1";
/*      */       }
/*      */     } 
/*  770 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List getCarUseTitle(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, User paramUser) throws Exception {
/*  789 */     String str1 = "";
/*  790 */     MeetingFieldComInfo meetingFieldComInfo = new MeetingFieldComInfo();
/*  791 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  792 */     ArrayList<String> arrayList = new ArrayList();
/*  793 */     arrayList.add(SystemEnv.getHtmlLabelName(20319, paramUser.getLanguage()) + "：" + paramString1);
/*  794 */     arrayList.add(SystemEnv.getHtmlLabelName(17649, paramUser.getLanguage()) + "：" + resourceComInfo.getResourcename(paramString2));
/*  795 */     arrayList.add(SystemEnv.getHtmlLabelName(17670, paramUser.getLanguage()) + "：" + resourceComInfo.getResourcename(paramString3));
/*      */ 
/*      */     
/*  798 */     String str2 = paramString4 + " " + paramString6;
/*  799 */     String str3 = paramString5 + " " + paramString7;
/*  800 */     arrayList.add(SystemEnv.getHtmlLabelName(83825, paramUser.getLanguage()) + "：" + str2);
/*  801 */     arrayList.add(SystemEnv.getHtmlLabelName(83826, paramUser.getLanguage()) + "：" + str3);
/*  802 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCarReportList(User paramUser, int paramInt1, int paramInt2, String paramString1, String paramString2, int paramInt3) {
/*      */     byte b;
/*  817 */     String str1 = this.user.getUID() + "";
/*  818 */     String str2 = MeetingShareUtil.getAllUser(this.user);
/*  819 */     MeetingSetInfo meetingSetInfo = new MeetingSetInfo();
/*  820 */     MeetingFieldComInfo meetingFieldComInfo = new MeetingFieldComInfo();
/*  821 */     boolean bool = HrmUserVarify.checkUserRight("Car:Maintenance", this.user);
/*  822 */     String str3 = "0";
/*  823 */     if (bool) {
/*  824 */       str3 = "1";
/*      */     }
/*  826 */     Calendar calendar1 = Calendar.getInstance();
/*  827 */     Calendar calendar2 = Calendar.getInstance();
/*  828 */     Calendar calendar3 = Calendar.getInstance();
/*      */     
/*  830 */     if (paramString2.equals("")) {
/*  831 */       String[] arrayOfString = (new DateTransformer()).getLocaleDateAndTime(DateHelper.getCurDateTime());
/*  832 */       paramString2 = arrayOfString[0];
/*      */     } 
/*      */     
/*  835 */     if (!paramString2.equals("")) {
/*  836 */       int i2 = Util.getIntValue(paramString2.substring(0, 4));
/*  837 */       int i3 = Util.getIntValue(paramString2.substring(5, 7)) - 1;
/*  838 */       int i4 = Util.getIntValue(paramString2.substring(8, 10));
/*  839 */       calendar1.set(i2, i3, i4);
/*      */     } 
/*      */     
/*  842 */     int i = calendar1.get(1);
/*  843 */     int j = calendar1.get(2);
/*  844 */     int k = calendar1.get(5);
/*      */ 
/*      */     
/*  847 */     i = calendar1.get(1);
/*  848 */     j = calendar1.get(2) + 1;
/*  849 */     k = calendar1.get(5);
/*  850 */     if (paramInt1 == 2) {
/*  851 */       paramString2 = Util.add0(i, 4) + "-" + Util.add0(j, 2);
/*      */     } else {
/*  853 */       paramString2 = Util.add0(i, 4) + "-" + Util.add0(j, 2) + "-" + Util.add0(k, 2);
/*      */     } 
/*      */ 
/*      */     
/*  857 */     RecordSet recordSet1 = new RecordSet();
/*  858 */     RecordSet recordSet2 = new RecordSet();
/*      */     
/*  860 */     recordSet1.execute("select carsdetachable from SystemSet");
/*  861 */     int m = 0;
/*  862 */     if (recordSet1.next()) {
/*  863 */       m = recordSet1.getInt(1);
/*      */     }
/*  865 */     String str4 = "";
/*  866 */     CarSetInfo carSetInfo = new CarSetInfo();
/*  867 */     if (carSetInfo.getIsShare() != 1) {
/*  868 */       if (m == 1) {
/*  869 */         if (!"".equals(Util.null2String(this.request.getParameter("subids")))) {
/*  870 */           paramInt2 = Util.getIntValue(this.request.getParameter("subids"));
/*      */         }
/*  872 */         if (this.user.getUID() != 1) {
/*  873 */           str4 = str4 + CarUtil.getDetachSql(this.user);
/*      */         }
/*      */       } else {
/*  876 */         paramInt2 = -1;
/*      */       } 
/*      */     }
/*      */     
/*  880 */     if (!"".equals(paramString1.trim())) {
/*  881 */       str4 = str4 + " and c1.carNo like '%" + paramString1 + "%' ";
/*      */     }
/*      */     
/*  884 */     if (paramInt3 != 0) {
/*  885 */       str4 = str4 + " and c1.id = " + paramInt3;
/*      */     }
/*      */ 
/*      */     
/*  889 */     if (paramInt2 > 0) {
/*  890 */       str4 = str4 + "and c1.subCompanyId = " + paramInt2;
/*      */     }
/*      */ 
/*      */     
/*  894 */     int n = meetingSetInfo.getContacterPrm();
/*  895 */     int i1 = meetingSetInfo.getCreaterPrm();
/*      */     
/*  897 */     String str5 = "";
/*  898 */     String str6 = paramString2;
/*  899 */     UnifiedConversionInterface unifiedConversionInterface = new UnifiedConversionInterface();
/*  900 */     switch (paramInt1) {
/*      */       case 2:
/*  902 */         str5 = " and  (('" + str6 + "' between SUBSTRING(c2.startdate,1,7) and SUBSTRING(c2.enddate,1,7)) or (SUBSTRING(c2.startdate,1,7)='" + str6 + "' and (c2.enddate='' or c2.enddate is null))) ";
/*      */         break;
/*      */       
/*      */       case 3:
/*  906 */         str7 = CarUtil.getFirstDayOfWeek(paramString2);
/*  907 */         str5 = " and  (";
/*  908 */         for (b = -1; b < 6; b++) {
/*  909 */           String str = TimeUtil.dateAdd(str7, b);
/*  910 */           str5 = str5 + "(('" + str + "' between c2.startdate and c2.enddate) or (c2.startdate='" + str + "' and (c2.enddate='' or c2.enddate is null)))or";
/*      */         } 
/*  912 */         str5 = str5.substring(0, str5.length() - 2);
/*  913 */         str5 = str5 + ")  ";
/*      */         break;
/*      */       case 4:
/*  916 */         if (unifiedConversionInterface.getTimeZoneStatus()) {
/*  917 */           String str16 = (new DateTransformer()).getServerDate(paramString2, "00:00:00");
/*  918 */           String str17 = (new DateTransformer()).getServerTime(paramString2, "00:00:00");
/*  919 */           String str18 = (new DateTransformer()).getServerDate(paramString2, "23:59:59");
/*  920 */           String str19 = (new DateTransformer()).getServerTime(paramString2, "23:59:59");
/*  921 */           if (recordSet1.getDBType().equals("oracle")) {
/*  922 */             str5 = str5 + " and ((c2.startDate ||' '||c2.startTime >= '" + str16 + " " + str17 + "' AND c2.startDate ||' '||c2.startTime <= '" + str18 + " " + str19 + "') OR (c2.startDate ||' '||c2.startTime <= '" + str16 + " " + str17 + "' AND c2.endDate||' '||c2.endTime >= '" + str18 + " " + str19 + "') OR (c2.endDate||' '||c2.endTime >= '" + str16 + " " + str17 + "' AND c2.endDate||' '||c2.endTime <= '" + str18 + " " + str19 + "') )";
/*      */             break;
/*      */           } 
/*  925 */           if (recordSet1.getDBType().equals("mysql")) {
/*  926 */             str5 = str5 + " and ((concat(c2.startDate,' ',c2.startTime) >= concat('" + str16 + "',' ','" + str17 + "') AND concat(c2.startDate,' ',c2.startTime) <= concat('" + str18 + "',' ','" + str19 + "')) OR (concat(c2.startDate ,' ',c2.startTime) <= concat('" + str16 + "',' ','" + str17 + "') AND concat(c2.endDate,' ',c2.endTime) >= concat('" + str18 + "',' ','" + str19 + "')) OR (concat(c2.endDate,' ',c2.endTime) >= concat('" + str16 + "',' ','" + str17 + "') AND concat(c2.endDate,' ',c2.endTime) <= concat('" + str18 + "',' ','" + str19 + "')))";
/*      */             
/*      */             break;
/*      */           } 
/*  930 */           if (recordSet1.getDBType().equals("postgresql")) {
/*  931 */             str5 = str5 + " and ((c2.startDate ||' '||c2.startTime >= '" + str16 + " " + str17 + "' AND c2.startDate ||' '||c2.startTime <= '" + str18 + " " + str19 + "') OR (c2.startDate ||' '||c2.startTime <= '" + str16 + " " + str17 + "' AND c2.endDate||' '||c2.endTime >= '" + str18 + " " + str19 + "') OR (c2.endDate||' '||c2.endTime >= '" + str16 + " " + str17 + "' AND c2.endDate||' '||c2.endTime <= '" + str18 + " " + str19 + "') )";
/*      */             
/*      */             break;
/*      */           } 
/*      */           
/*  936 */           str5 = str5 + " and ((c2.startDate +' '+c2.startTime >= '" + str16 + " " + str17 + "' AND c2.startDate +' '+c2.startTime <= '" + str18 + " " + str19 + "') OR (c2.startDate +' '+c2.startTime <= '" + str16 + " " + str17 + "' AND c2.endDate+' '+c2.endTime >= '" + str18 + " " + str19 + "') OR (c2.endDate+' '+c2.endTime >= '" + str16 + " " + str17 + "' AND c2.endDate+' '+c2.endTime <= '" + str18 + " " + str19 + "'))";
/*      */           
/*      */           break;
/*      */         } 
/*      */         
/*  941 */         str5 = "  and (('" + paramString2 + "'  between c2.startdate and c2.enddate) or (c2.startdate='" + paramString2 + "' and (c2.enddate='' or c2.enddate is null)))";
/*      */         break;
/*      */     } 
/*      */     
/*  945 */     if (recordSet1.getDBType().equals("oracle")) {
/*  946 */       str5 = Util.StringReplace(str5, "SUBSTRING", "substr");
/*      */     }
/*  948 */     str5 = str5 + str4;
/*  949 */     String str7 = "";
/*  950 */     if (recordSet1.getDBType().equals("oracle")) {
/*  951 */       str7 = str7 + "(select id,requestid,to_number(carId) as carId,to_number(driver) as driver,to_number(userid) as userid,startdate,starttime,enddate,endtime,cancel,'CarUseApprove' as tablename,'cancel' as fieldname from CarUseApprove";
/*      */     }
/*  953 */     else if (recordSet1.getDBType().equals("mysql")) {
/*  954 */       str7 = str7 + "(select id,requestid,carId,driver,userid,startdate,starttime,enddate,endtime,cancel,'CarUseApprove' as tablename,'cancel' as fieldname from CarUseApprove";
/*      */     }
/*  956 */     else if (recordSet1.getDBType().equals("postgresql")) {
/*  957 */       str7 = str7 + "(select id,requestid,to_number(carId) as carId,to_number(driver) as driver,to_number(userid) as userid,startdate,starttime,enddate,endtime,cancel,'CarUseApprove' as tablename,'cancel' as fieldname from CarUseApprove";
/*      */     } else {
/*      */       
/*  960 */       str7 = str7 + "(select id,requestid,carId,driver,userid,startdate,starttime,enddate,endtime,cancel,'CarUseApprove' as tablename,'cancel' as fieldname from CarUseApprove";
/*      */     } 
/*  962 */     recordSet1.execute("select id,formid,workflowid from carbasic where formid!=163 and isuse = 1  and workflowid in (select id from workflow_base)");
/*  963 */     FormManager formManager = new FormManager();
/*  964 */     while (recordSet1.next()) {
/*  965 */       String str16 = recordSet1.getString("id");
/*  966 */       String str17 = recordSet1.getString("formid");
/*  967 */       String str18 = formManager.getTablename(str17);
/*  968 */       if (str18 == null || "".equals(str18))
/*  969 */         continue;  String str19 = recordSet1.getString("workflowid");
/*  970 */       str7 = str7 + " union all select id,requestid,";
/*  971 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  972 */       recordSet2.execute("select carfieldid,modefieldid,fieldname from mode_carrelatemode c,workflow_billfield b where c.modefieldid=b.id and mainid=" + str16);
/*  973 */       while (recordSet2.next()) {
/*  974 */         String str20 = recordSet2.getString("carfieldid");
/*  975 */         String str21 = recordSet2.getString("modefieldid");
/*  976 */         String str22 = recordSet2.getString("fieldname");
/*  977 */         hashMap1.put(str20, str22);
/*      */       } 
/*  979 */       str7 = str7 + CarUtil.addSql(hashMap1) + ",";
/*  980 */       str7 = str7 + "'" + str18 + "' as tablename,";
/*  981 */       str7 = str7 + "'" + Util.null2String(hashMap1.get("639")) + "' as fieldname";
/*  982 */       str7 = str7 + " from " + str18;
/*  983 */       str7 = str7 + " where (select max(workflowid) from workflow_requestbase where requestid=" + str18 + ".requestid)=" + str19;
/*      */     } 
/*  985 */     str7 = str7 + ")";
/*      */     
/*  987 */     String str8 = "c1.id,c2.id aid,c1.carNo,c2.driver,c2.userid,c2.startdate,c2.starttime,c2.enddate,c2.endtime,c3.requestid,c3.requestname,c.id as tid, c.name as typename,c3.currentnodetype,c2.cancel,c2.tablename,c2.fieldname,c3.workflowid,c3.creater,c3.requestnamenew ";
/*      */     
/*  989 */     String str9 = "  Carinfo c1 left join " + str7 + " c2 on c2.carId = c1.id left join workflow_requestbase c3 on c2.requestid=c3.requestid  left join CarType c on c1.cartype = c.id ";
/*      */     
/*  991 */     String str10 = " where c3.currentnodetype<>0 and (deposit<>'1' or deposit is null)  and c3.workflowid not in (select workflowid from carbasic where isuse=0)" + str5;
/*      */     
/*  993 */     String str11 = " c2.startdate ,c2.starttime , c1.id";
/*  994 */     String str12 = "";
/*  995 */     String str13 = "0";
/*      */     
/*  997 */     String str14 = "column:requestid+" + this.user.getLanguage() + "+" + str3 + "+column:tablename+column:fieldname";
/*  998 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  999 */     str10 = str10 + CarUtil.getCarShareSql(this.user).replace("a.", "c1.");
/* 1000 */     String str15 = "select " + str8 + "from " + str9 + str10;
/* 1001 */     ArrayList<SplitTableColBean> arrayList = new ArrayList();
/*      */     
/* 1003 */     arrayList.add(new SplitTableColBean("12%", SystemEnv.getHtmlLabelName(21028, this.user.getLanguage()), "carNo", "carNo"));
/*      */     
/* 1005 */     arrayList.add(new SplitTableColBean("30%", SystemEnv.getHtmlLabelName(82288, this.user.getLanguage()), "requestname", "requestname"));
/*      */     
/* 1007 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(17670, this.user.getLanguage()), "userid", "userid"));
/*      */     
/* 1009 */     arrayList.add(new SplitTableColBean("15%", SystemEnv.getHtmlLabelName(602, this.user.getLanguage()), "requestid", "requestid"));
/*      */     
/* 1011 */     arrayList.add(new SplitTableColBean("14%", SystemEnv.getHtmlLabelName(742, this.user.getLanguage()), "startdate", "startdate"));
/*      */     
/* 1013 */     arrayList.add(new SplitTableColBean("14%", SystemEnv.getHtmlLabelName(743, this.user.getLanguage()), "enddate", "enddate"));
/*      */     
/* 1015 */     arrayList.add(new SplitTableColBean("12%", "", "aid"));
/* 1016 */     SplitTableBean splitTableBean = new SplitTableBean(str8, str9, str10, str11, "c1.id", arrayList);
/* 1017 */     splitTableBean.setDatasource("com.api.car.util.CarDataSource.getCarReportListData");
/* 1018 */     splitTableBean.setSourceparams("sql:" + CodeUtils.getBase64(Util.toHtmlForSplitPage(str15)) + "+cancelString:" + str3);
/* 1019 */     splitTableBean.setInstanceid("carTable");
/* 1020 */     splitTableBean.setPageUID("Car123456");
/* 1021 */     splitTableBean.setPagesize("10");
/* 1022 */     splitTableBean.setTableType("none");
/* 1023 */     splitTableBean.setSqlsortway("DESC");
/* 1024 */     hashMap.putAll(SplitTableUtil.makeListDataResult(splitTableBean));
/* 1025 */     hashMap.put("sessionkey", hashMap.get(BrowserConstant.BROWSER_RESULT_DATA));
/* 1026 */     return JSONObject.toJSONString(hashMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCarFlowCreateData(User paramUser) throws Exception {
/* 1038 */     int i = paramUser.getUID();
/* 1039 */     String str1 = paramUser.getLogintype();
/* 1040 */     boolean bool = false;
/* 1041 */     if (str1.equals("2")) {
/* 1042 */       bool = true;
/*      */     }
/*      */     
/* 1045 */     ArrayList<String> arrayList1 = new ArrayList();
/* 1046 */     ArrayList<String> arrayList2 = new ArrayList();
/*      */     
/* 1048 */     RecordSet recordSet = new RecordSet();
/* 1049 */     ShareManager shareManager = new ShareManager();
/* 1050 */     String str2 = shareManager.getWfShareSqlWhere(paramUser, "t1");
/* 1051 */     String str3 = "select distinct t2.workflowtype,t3.typename from ShareInnerWfCreate t1,workflow_base t2,workflow_type t3 where t2.workflowtype = t3.id and t1.workflowid=t2.id and (t2.formid=163 or t2.formid in (select formid from carbasic)) and t1.workflowid  in (select workflowid from carbasic where isuse=1) and t2.isbill=1 and t2.isvalid='1' and t1.usertype = " + bool + " and " + str2;
/* 1052 */     recordSet.execute(str3);
/* 1053 */     while (recordSet.next()) {
/* 1054 */       arrayList1.add(recordSet.getString("workflowtype"));
/* 1055 */       arrayList2.add(recordSet.getString("typename"));
/*      */     } 
/*      */     
/* 1058 */     ArrayList arrayList3 = new ArrayList();
/* 1059 */     ArrayList arrayList4 = new ArrayList();
/* 1060 */     str3 = "select distinct t2.id as workflowid,t2.workflowname,workflowtype from ShareInnerWfCreate t1,workflow_base t2 where t1.workflowid=t2.id and (t2.formid=163 or t2.formid in (select formid from carbasic)) and t1.workflowid  in (select workflowid from carbasic where isuse=1) and t2.isbill=1 and t2.isvalid='1' and t1.usertype = " + bool + " and " + str2;
/* 1061 */     recordSet.execute(str3);
/* 1062 */     JSONArray jSONArray = new JSONArray();
/* 1063 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 1064 */       String str4 = Util.null2String(arrayList1.get(b));
/* 1065 */       String str5 = Util.null2String(arrayList2.get(b));
/* 1066 */       recordSet.beforFirst();
/* 1067 */       JSONArray jSONArray1 = new JSONArray();
/* 1068 */       while (recordSet.next()) {
/* 1069 */         String str6 = Util.null2String(recordSet.getString("workflowid"));
/* 1070 */         String str7 = Util.null2String(recordSet.getString("workflowname"));
/* 1071 */         String str8 = Util.null2String(recordSet.getString("workflowtype"));
/* 1072 */         if (str8.equals(str4)) {
/* 1073 */           JSONObject jSONObject1 = new JSONObject();
/* 1074 */           jSONObject1.put("workflowid", str6);
/* 1075 */           jSONObject1.put("workflowname", str7);
/* 1076 */           jSONArray1.add(jSONObject1);
/*      */         } 
/*      */       } 
/* 1079 */       JSONObject jSONObject = new JSONObject();
/* 1080 */       jSONObject.put("workflowtypeid", str4);
/* 1081 */       jSONObject.put("workflowtypename", str5);
/* 1082 */       jSONObject.put("flows", jSONArray1);
/* 1083 */       jSONArray.add(jSONObject);
/*      */     } 
/*      */     
/* 1086 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1087 */     hashMap.put("datas", jSONArray);
/* 1088 */     hashMap.put("count", Integer.valueOf(recordSet.getCounts()));
/* 1089 */     return JSONObject.toJSONString(hashMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCarFlowField() {
/* 1097 */     String str1 = Util.null2String(this.request.getParameter("workflowid"));
/* 1098 */     String str2 = "select distinct carfieldid,modefieldid from carbasic car,workflow_billfield bill,mode_carrelatemode m where  bill.billid=car.formid and modefieldid=bill.id and car.workflowid=" + str1;
/*      */ 
/*      */     
/* 1101 */     RecordSet recordSet = new RecordSet();
/* 1102 */     recordSet.execute(str2);
/* 1103 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1104 */     hashMap.put("carId", "field627");
/* 1105 */     hashMap.put("startDate", "field634");
/* 1106 */     hashMap.put("startTime", "field635");
/* 1107 */     hashMap.put("endDate", "field636");
/* 1108 */     hashMap.put("endTime", "field637");
/* 1109 */     while (recordSet.next()) {
/* 1110 */       String str3 = Util.null2String(recordSet.getString("carfieldid"));
/* 1111 */       String str4 = Util.null2String(recordSet.getString("modefieldid"));
/* 1112 */       if ("627".equals(str3)) {
/* 1113 */         hashMap.put("carId", "field" + str4); continue;
/* 1114 */       }  if ("634".equals(str3)) {
/* 1115 */         hashMap.put("startDate", "field" + str4); continue;
/* 1116 */       }  if ("635".equals(str3)) {
/* 1117 */         hashMap.put("startTime", "field" + str4); continue;
/* 1118 */       }  if ("636".equals(str3)) {
/* 1119 */         hashMap.put("endDate", "field" + str4); continue;
/* 1120 */       }  if ("637".equals(str3)) {
/* 1121 */         hashMap.put("endTime", "field" + str4);
/*      */       }
/*      */     } 
/* 1124 */     return JSONObject.toJSONString(hashMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCarReportBaseData(User paramUser) throws Exception {
/* 1133 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1134 */     CarSetInfo carSetInfo = new CarSetInfo();
/* 1135 */     String str1 = carSetInfo.getUsedColor();
/* 1136 */     String str2 = carSetInfo.getAgreementColor();
/* 1137 */     String str3 = carSetInfo.getConflictedColor();
/* 1138 */     int i = carSetInfo.getTimeRangeStart();
/* 1139 */     int j = carSetInfo.getTimeRangeEnd();
/* 1140 */     int k = carSetInfo.getIsRemind();
/* 1141 */     int m = carSetInfo.getRemindType();
/* 1142 */     if (str1.equals("")) str1 = "E3F6D8"; 
/* 1143 */     if (str2.equals("")) str2 = "FFE4C4"; 
/* 1144 */     if (str3.equals("")) str3 = "FBDFEB"; 
/* 1145 */     String str4 = "";
/* 1146 */     RecordSet recordSet = new RecordSet();
/* 1147 */     String str5 = Util.null2String(Integer.valueOf(paramUser.getUserSubCompany1()));
/* 1148 */     recordSet.executeSql("SELECT subcompanydesc FROM HrmSubCompany WHERE id = " + str5);
/* 1149 */     if (recordSet.next()) {
/* 1150 */       if ("1".equals(str5)) {
/* 1151 */         str4 = SystemEnv.getHtmlLabelName(140, paramUser.getLanguage()) + " : " + Util.null2String(recordSet.getString("subcompanydesc"));
/*      */       } else {
/* 1153 */         str4 = SystemEnv.getHtmlLabelName(141, paramUser.getLanguage()) + " : " + Util.null2String(recordSet.getString("subcompanydesc"));
/*      */       } 
/*      */     }
/* 1156 */     if ("0".equals(str5)) {
/* 1157 */       recordSet.executeSql("SELECT companyname FROM HrmCompany WHERE id = 1");
/* 1158 */       if (recordSet.next()) {
/* 1159 */         str4 = SystemEnv.getHtmlLabelName(140, paramUser.getLanguage()) + " : " + Util.null2String(recordSet.getString("companyname"));
/*      */       }
/*      */     } 
/* 1162 */     hashMap.put("usedColor", "#" + str1);
/* 1163 */     hashMap.put("agreementColor", "#" + str2);
/* 1164 */     hashMap.put("conflictedColor", "#" + str3);
/* 1165 */     hashMap.put("timestart", Integer.valueOf(i));
/* 1166 */     hashMap.put("timeend", Integer.valueOf(j));
/* 1167 */     hashMap.put("subname", str4);
/* 1168 */     if (k == 1) {
/* 1169 */       if (m == 1) {
/* 1170 */         hashMap.put("roomConflictType", Integer.valueOf(1));
/*      */       } else {
/* 1172 */         hashMap.put("roomConflictType", Integer.valueOf(2));
/*      */       } 
/*      */     } else {
/* 1175 */       hashMap.put("roomConflictType", Integer.valueOf(0));
/*      */     } 
/* 1177 */     return JSONObject.toJSONString(hashMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String doCancel(User paramUser) throws Exception {
/* 1186 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1187 */     String str1 = Util.null2String(this.request.getParameter("id"));
/* 1188 */     String str2 = Util.null2String(this.request.getParameter("tablename"));
/* 1189 */     String str3 = Util.null2String(this.request.getParameter("cancelname"));
/* 1190 */     String str4 = "update " + str2 + " set " + str3 + "='1' where id=" + str1;
/* 1191 */     RecordSet recordSet = new RecordSet();
/* 1192 */     boolean bool = recordSet.executeSql(str4);
/* 1193 */     if (bool) {
/* 1194 */       hashMap.put("status", "1");
/*      */     } else {
/* 1196 */       hashMap.put("status", "0");
/*      */     } 
/* 1198 */     return JSONObject.toJSONString(hashMap);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCarServerDateTime() throws Exception {
/* 1207 */     String str1 = Util.null2String(this.request.getParameter("startDate"));
/* 1208 */     String str2 = Util.null2String(this.request.getParameter("startTime"));
/* 1209 */     String str3 = Util.null2String(this.request.getParameter("endDate"));
/* 1210 */     String str4 = Util.null2String(this.request.getParameter("endTime"));
/* 1211 */     str1 = CarDateTimeUtil.getServerDate(str1, str2);
/* 1212 */     str2 = CarDateTimeUtil.getShortServerTime(str1, str2);
/* 1213 */     str3 = CarDateTimeUtil.getServerDate(str3, str4);
/* 1214 */     str4 = CarDateTimeUtil.getShortServerTime(str3, str4);
/* 1215 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1216 */     hashMap.put("startDate", str1);
/* 1217 */     hashMap.put("startTime", str2);
/* 1218 */     hashMap.put("endDate", str3);
/* 1219 */     hashMap.put("endTime", str4);
/* 1220 */     return JSONObject.toJSONString(hashMap);
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/service/CarViewService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */