/*    */ package com.api.car.service;
/*    */ 
/*    */ import com.engine.workflow.biz.systemBill.BillActionCfg;
/*    */ import com.engine.workflow.biz.systemBill.SystemBill;
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ public class SystemBill163Impl
/*    */   implements SystemBill {
/*    */   public boolean judgeIsValid() {
/* 10 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getIncludePage() {
/* 15 */     return GCONST.getContextPath() + "/car/template/CarSubmitRequestJs.jsp";
/*    */   }
/*    */ 
/*    */   
/*    */   public BillActionCfg registerActionInfo() {
/* 20 */     return new BillActionCfg();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/service/SystemBill163Impl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */