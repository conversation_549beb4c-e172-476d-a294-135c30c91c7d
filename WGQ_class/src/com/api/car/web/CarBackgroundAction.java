/*     */ package com.api.car.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.car.service.CarBackgroundService;
/*     */ import com.api.car.util.CarSetInfo;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/car/background")
/*     */ public class CarBackgroundAction
/*     */   extends BaseBean
/*     */ {
/*     */   private boolean checkRight(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  37 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  38 */     if (!HrmUserVarify.checkUserRight("Car:Maintenance", user)) {
/*  39 */       return false;
/*     */     }
/*  41 */     return true;
/*     */   }
/*     */   
/*  44 */   private Map<String, Object> noRightMap = new HashMap<String, Object>()
/*     */     {
/*     */     
/*     */     };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCarApplySetting")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarApplySetting(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  59 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/*  60 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/*  61 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/*  63 */     Map<String, String> map = (Map)new HashMap<>();
/*  64 */     String str = "";
/*     */     
/*     */     try {
/*  67 */       map = (new CarBackgroundService(paramHttpServletRequest, paramHttpServletResponse)).getCarApplySetting();
/*  68 */     } catch (Exception exception) {
/*  69 */       writeLog(exception);
/*  70 */       map.put("status", "0");
/*  71 */       str = "catch exception : " + exception.getMessage();
/*  72 */       map.put("error", str);
/*     */     } 
/*  74 */     map.put("isRight", Integer.valueOf(1));
/*  75 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/saveCarApplySetting")
/*     */   @Produces({"text/plain"})
/*     */   public String saveCarApplySetting(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  86 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/*  87 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/*  88 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/*  90 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  91 */     String str = "";
/*     */     
/*     */     try {
/*  94 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("isremind"));
/*     */       
/*  96 */       String str2 = Util.null2String(paramHttpServletRequest.getParameter("isshare"));
/*  97 */       String str3 = Util.null2String(paramHttpServletRequest
/*  98 */           .getParameter("remindtype"));
/*  99 */       int i = Util.getIntValue(paramHttpServletRequest.getParameter("timeRangeStart"), 0);
/* 100 */       int j = Util.getIntValue(paramHttpServletRequest.getParameter("timeRangeEnd"), 23);
/* 101 */       int k = Util.getIntValue(paramHttpServletRequest.getParameter("dspUnit"), 1);
/* 102 */       String str4 = Util.null2String(paramHttpServletRequest.getParameter("usedColor")).replace("#", "");
/* 103 */       String str5 = Util.null2String(paramHttpServletRequest.getParameter("conflictedColor")).replace("#", "");
/* 104 */       RecordSet recordSet = new RecordSet();
/* 105 */       boolean bool = false;
/* 106 */       String str6 = "select * from mode_carremindset";
/* 107 */       recordSet.executeQuery(str6, new Object[0]);
/* 108 */       if (recordSet.getCounts() <= 0) {
/* 109 */         str6 = "insert into mode_carremindset(isremind,remindtype,timeRangeStart,timeRangeEnd,dspUnit,usedColor,conflictedColor,isshare,isopenshare) values (?,?,?,?,?,?,?,?,0)";
/* 110 */         bool = recordSet.executeUpdate(str6, new Object[] { str1, str3, Integer.valueOf(i), Integer.valueOf(j), Integer.valueOf(k), str4, str5, str2 });
/*     */       } else {
/* 112 */         str6 = "update mode_carremindset set isremind=?,remindtype=?,timeRangeStart=?,timeRangeEnd=?,dspUnit=?,usedColor=?,conflictedColor=?,isshare=?";
/* 113 */         bool = recordSet.executeUpdate(str6, new Object[] { str1, str3, Integer.valueOf(i), Integer.valueOf(j), Integer.valueOf(k), str4, str5, str2 });
/*     */       } 
/* 115 */       if (bool) {
/* 116 */         hashMap.put("status", "1");
/* 117 */         if ("1".equals(str2)) {
/* 118 */           insertShare(recordSet);
/*     */         }
/* 120 */         (new CarSetInfo()).removeCarSetInfoCache();
/*     */       } else {
/* 122 */         str = "" + SystemEnv.getHtmlLabelName(10004395, ThreadVarLanguage.getLang()) + "";
/* 123 */         throw new Exception(str);
/*     */       } 
/* 125 */     } catch (Exception exception) {
/* 126 */       writeLog(exception);
/* 127 */       hashMap.put("status", "0");
/* 128 */       if (StringHelper.isEmpty(str)) {
/* 129 */         str = "catch exception : " + exception.getMessage();
/*     */       }
/* 131 */       hashMap.put("error", str);
/*     */     } 
/* 133 */     hashMap.put("isRight", Integer.valueOf(1));
/* 134 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCarType")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarType(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 145 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 146 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/* 147 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/* 149 */     Map<String, String> map = (Map)new HashMap<>();
/* 150 */     String str = "";
/*     */     try {
/* 152 */       map = (new CarBackgroundService(paramHttpServletRequest, paramHttpServletResponse)).getCarType();
/* 153 */     } catch (Exception exception) {
/* 154 */       writeLog(exception);
/* 155 */       map.put("status", "0");
/* 156 */       str = "catch exception : " + exception.getMessage();
/* 157 */       map.put("error", str);
/*     */     } 
/* 159 */     map.put("isRight", Integer.valueOf(1));
/* 160 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/saveOrUpdateCarType")
/*     */   @Produces({"text/plain"})
/*     */   public String saveOrUpdateCarType(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 171 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 172 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/* 173 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/* 175 */     Map<String, String> map = (Map)new HashMap<>();
/* 176 */     String str = "";
/*     */     try {
/* 178 */       map = (new CarBackgroundService(paramHttpServletRequest, paramHttpServletResponse)).saveOrUpdateCarType();
/* 179 */     } catch (Exception exception) {
/* 180 */       writeLog(exception);
/* 181 */       map.put("status", "0");
/* 182 */       str = "catch exception : " + exception.getMessage();
/* 183 */       map.put("error", str);
/*     */     } 
/* 185 */     map.put("isRight", Integer.valueOf(1));
/* 186 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/deleteCarType")
/*     */   @Produces({"text/plain"})
/*     */   public String deleteCarType(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 197 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 198 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/* 199 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/* 201 */     Map<String, String> map = (Map)new HashMap<>();
/* 202 */     String str = "";
/*     */     try {
/* 204 */       map = (new CarBackgroundService(paramHttpServletRequest, paramHttpServletResponse)).deleteCarType();
/* 205 */     } catch (Exception exception) {
/* 206 */       writeLog(exception);
/* 207 */       map.put("status", "0");
/* 208 */       str = "catch exception : " + exception.getMessage();
/* 209 */       map.put("error", str);
/*     */     } 
/* 211 */     map.put("isRight", Integer.valueOf(1));
/* 212 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void insertShare(RecordSet paramRecordSet) {
/* 220 */     boolean bool = ((new CarSetInfo()).getIsOpenShare() == 0) ? true : false;
/* 221 */     if (bool) {
/* 222 */       paramRecordSet.execute("select carsdetachable from SystemSet");
/* 223 */       int i = 0;
/* 224 */       if (paramRecordSet.next()) {
/* 225 */         i = paramRecordSet.getInt(1);
/*     */       }
/* 227 */       String str = "select id,subcompanyid from carinfo ";
/* 228 */       paramRecordSet.executeQuery(str, new Object[0]);
/* 229 */       RecordSet recordSet = new RecordSet();
/* 230 */       while (paramRecordSet.next()) {
/* 231 */         String str1 = Util.null2String(paramRecordSet.getString("id"));
/* 232 */         String str2 = Util.null2String(paramRecordSet.getString("subcompanyid"));
/* 233 */         if (!"".equals(str1)) {
/* 234 */           if (i == 1) {
/* 235 */             str = "insert into car_share(carid,permissiontype,subcompanyid,sublevel,sublevelmax) values(?,6,?,0,100)";
/* 236 */             recordSet.executeUpdate(str, new Object[] { str1, str2 }); continue;
/*     */           } 
/* 238 */           str = "insert into car_share(carid,permissiontype,seclevel,seclevelmax) values(?,3,0,100)";
/* 239 */           recordSet.executeUpdate(str, new Object[] { str1 });
/*     */         } 
/*     */       } 
/*     */       
/* 243 */       str = "update mode_carremindset set isopenshare=1";
/* 244 */       recordSet.executeUpdate(str, new Object[0]);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/web/CarBackgroundAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */