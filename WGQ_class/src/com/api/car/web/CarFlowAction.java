/*    */ package com.api.car.web;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.api.car.service.CarFlowService;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.ws.rs.GET;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.Produces;
/*    */ import javax.ws.rs.core.Context;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Path("/car/flow")
/*    */ public class CarFlowAction
/*    */   extends BaseBean
/*    */ {
/*    */   @GET
/*    */   @Path("/getCarFlowList")
/*    */   @Produces({"text/plain"})
/*    */   public String getCarFlowList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 30 */     return carFlowList(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @POST
/*    */   @Path("/getCarFlowList")
/*    */   @Produces({"text/plain"})
/*    */   public String carFlowList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*    */     Map<String, String> map;
/* 41 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 42 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 44 */       map = (new CarFlowService(paramHttpServletRequest, paramHttpServletResponse)).getCarFlowList(paramHttpServletRequest, paramHttpServletResponse);
/* 45 */       map.put("status", "1");
/* 46 */     } catch (Exception exception) {
/* 47 */       writeLog(exception);
/* 48 */       map.put("status", "0");
/* 49 */       map.put("error", "catch exception : " + exception.getMessage());
/*    */     } 
/* 51 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @GET
/*    */   @Path("/getCarFlowListCondition")
/*    */   @Produces({"text/plain"})
/*    */   public String getCarFlowListCondition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 64 */     Map<String, String> map = (Map)new HashMap<>();
/*    */     try {
/* 66 */       map = (new CarFlowService(paramHttpServletRequest, paramHttpServletResponse)).getCarFlowListCondition();
/* 67 */     } catch (Exception exception) {
/* 68 */       writeLog(exception);
/* 69 */       map.put("status", "0");
/* 70 */       map.put("error", "catch exception : " + exception.getMessage());
/*    */     } 
/* 72 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/web/CarFlowAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */