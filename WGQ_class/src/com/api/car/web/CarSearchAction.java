/*    */ package com.api.car.web;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.api.car.service.CarSearchService;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.ws.rs.GET;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.Produces;
/*    */ import javax.ws.rs.core.Context;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Path("/car/search")
/*    */ public class CarSearchAction
/*    */   extends BaseBean
/*    */ {
/*    */   @GET
/*    */   @Path("/getCarSearchList")
/*    */   @Produces({"text/plain"})
/*    */   public String getCarSearchList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 31 */     return carSearchList(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @POST
/*    */   @Path("/getCarSearchList")
/*    */   @Produces({"text/plain"})
/*    */   public String carSearchList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*    */     Map<String, String> map;
/* 42 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 43 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 45 */       map = (new CarSearchService(paramHttpServletRequest, paramHttpServletResponse)).getCarSearchList(paramHttpServletRequest, paramHttpServletResponse);
/* 46 */       map.put("status", "1");
/* 47 */     } catch (Exception exception) {
/* 48 */       writeLog(exception);
/* 49 */       map.put("status", "0");
/* 50 */       map.put("error", "catch exception : " + exception.getMessage());
/*    */     } 
/* 52 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @GET
/*    */   @Path("/getCarSearchListCondition")
/*    */   @Produces({"text/plain"})
/*    */   public String getCarSearchListCondition(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 66 */     Map<String, String> map = (Map)new HashMap<>();
/*    */     try {
/* 68 */       map = (new CarSearchService(paramHttpServletRequest, paramHttpServletResponse)).getCarSearchListCondition();
/* 69 */     } catch (Exception exception) {
/* 70 */       writeLog(exception);
/* 71 */       map.put("status", "0");
/* 72 */       map.put("error", "catch exception : " + exception.getMessage());
/*    */     } 
/* 74 */     return JSONObject.toJSONString(map);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/web/CarSearchAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */