/*     */ package com.api.car.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.alibaba.fastjson.serializer.SerializerFeature;
/*     */ import com.api.car.service.CarBaseService;
/*     */ import com.api.car.util.CarSetInfo;
/*     */ import com.api.car.util.CarUtil;
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.car.CarInfoComInfo;
/*     */ import weaver.car.CarTypeComInfo;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/car/base")
/*     */ public class CarBaseAction
/*     */   extends BaseBean
/*     */ {
/*     */   @GET
/*     */   @Path("/getCarList")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  46 */     return carList(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getCarList")
/*     */   @Produces({"text/plain"})
/*     */   public String carList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map<String, String> map;
/*  56 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/*  57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  59 */       map = (new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).getCarList(paramHttpServletRequest, paramHttpServletResponse);
/*  60 */       map.put("status", "1");
/*  61 */     } catch (Exception exception) {
/*  62 */       writeLog(exception);
/*  63 */       map.put("status", "0");
/*  64 */       map.put("error", "catch exception : " + exception.getMessage());
/*     */     } 
/*  66 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCarTypeListData")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarTypeListData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  82 */     return carTypeListData(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getCarTypeListData")
/*     */   @Produces({"text/plain"})
/*     */   public String carTypeListData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map<String, String> map;
/*  92 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/*  93 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  95 */       map = (new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).getCarTypeListData();
/*  96 */       map.put("status", "1");
/*  97 */     } catch (Exception exception) {
/*  98 */       writeLog(exception);
/*  99 */       map.put("status", "0");
/* 100 */       map.put("error", "catch exception : " + exception.getMessage());
/*     */     } 
/* 102 */     return JSONObject.toJSONString(map, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/delCarinfo")
/*     */   @Produces({"text/plain"})
/*     */   public String delCarinfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 114 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 115 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 117 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 118 */       if (!HrmUserVarify.checkUserRight("Car:Maintenance", user)) {
/* 119 */         throw new Exception("没有车辆管理权限");
/*     */       }
/* 121 */       String str = Util.null2String(paramHttpServletRequest.getParameter("id"));
/* 122 */       if (!str.equals("")) {
/* 123 */         String str1 = "delete from CarInfo where id=" + str;
/* 124 */         RecordSet recordSet = new RecordSet();
/* 125 */         recordSet.execute(str1);
/* 126 */         str1 = "delete from car_share where carid=?";
/* 127 */         recordSet.executeUpdate(str1, new Object[] { str });
/*     */       } 
/* 129 */       hashMap.put("status", "1");
/* 130 */     } catch (Exception exception) {
/* 131 */       writeLog(exception);
/* 132 */       hashMap.put("status", "0");
/* 133 */       hashMap.put("error", "catch exception : " + exception.getMessage());
/*     */     } 
/* 135 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCarInfo")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarInfo(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 146 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 147 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 149 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 150 */       String str = Util.null2String(paramHttpServletRequest.getParameter("id"));
/* 151 */       boolean bool = false;
/* 152 */       if (!str.equals("")) {
/* 153 */         String str1 = "select * from CarInfo where id=" + str;
/* 154 */         RecordSet recordSet = new RecordSet();
/* 155 */         recordSet.execute(str1);
/* 156 */         if (recordSet.next()) {
/* 157 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 158 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 159 */           CarTypeComInfo carTypeComInfo = new CarTypeComInfo();
/*     */           
/* 161 */           String str2 = recordSet.getString("carNo");
/* 162 */           String str3 = recordSet.getString("carType");
/* 163 */           String str4 = "";
/* 164 */           if (!StringHelper.isEmpty(str3)) {
/* 165 */             str4 = carTypeComInfo.getCarTypename(str3);
/*     */           }
/* 167 */           String str5 = recordSet.getString("usefee");
/* 168 */           String str6 = recordSet.getString("factoryNo");
/* 169 */           String str7 = recordSet.getString("price");
/* 170 */           String str8 = recordSet.getString("buyDate");
/* 171 */           String str9 = recordSet.getString("engineNo");
/* 172 */           String str10 = recordSet.getString("driver");
/* 173 */           String str11 = "";
/* 174 */           String str12 = recordSet.getString("showorder");
/* 175 */           String str13 = recordSet.getString("deposit");
/* 176 */           if (!StringHelper.isEmpty(str10)) {
/* 177 */             str11 = resourceComInfo.getResourcename(str10);
/*     */           }
/* 179 */           String str14 = recordSet.getString("remark");
/* 180 */           String str15 = recordSet.getString("subCompanyId");
/* 181 */           String str16 = "";
/* 182 */           if (!StringHelper.isEmpty(str15)) {
/* 183 */             str16 = Util.toScreen(subCompanyComInfo.getSubCompanyname(str15), user.getLanguage());
/*     */           }
/* 185 */           if (!StringHelper.isEmpty(str16)) {
/* 186 */             str16 = Util.formatMultiLang(str16, user.getLanguage() + "");
/*     */           }
/* 188 */           bool = true;
/*     */           
/* 190 */           hashMap.put("id", str);
/* 191 */           hashMap.put("carNo", str2);
/* 192 */           hashMap.put("carType", str3);
/* 193 */           hashMap.put("carTypeName", str4);
/* 194 */           hashMap.put("usefee", str5);
/* 195 */           hashMap.put("factoryNo", str6);
/* 196 */           hashMap.put("price", str7);
/* 197 */           hashMap.put("buyDate", str8);
/* 198 */           hashMap.put("engineNo", str9);
/* 199 */           hashMap.put("driver", str10);
/* 200 */           hashMap.put("driverName", str11);
/* 201 */           hashMap.put("remark", str14);
/* 202 */           hashMap.put("showorder", Double.valueOf(Util.getDoubleValue(str12, 1.0D)));
/* 203 */           hashMap.put("subCompanyId", str15);
/* 204 */           hashMap.put("subCompanyIdName", str16);
/* 205 */           hashMap.put("deposit", "1".equals(str13) ? "1" : "0");
/*     */         } 
/*     */       } 
/* 208 */       hashMap.put("isshare", Integer.valueOf((new CarSetInfo()).getIsShare()));
/* 209 */       if (bool) {
/* 210 */         hashMap.put("status", "1");
/*     */       } else {
/* 212 */         hashMap.put("status", "0");
/* 213 */         throw new Exception("未能查找到车辆信息");
/*     */       } 
/* 215 */     } catch (Exception exception) {
/* 216 */       writeLog(exception);
/* 217 */       hashMap.put("status", "0");
/* 218 */       hashMap.put("error", "catch exception : " + exception.getMessage());
/*     */     } 
/* 220 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCarFormField")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarFormField(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 234 */     Map<String, String> map = (Map)new HashMap<>();
/*     */     try {
/* 236 */       map = (new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).getCarFormField();
/* 237 */     } catch (Exception exception) {
/* 238 */       writeLog(exception);
/* 239 */       map.put("status", "0");
/* 240 */       map.put("error", "catch exception : " + exception.getMessage());
/*     */     } 
/* 242 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/saveorupdateCar")
/*     */   @Produces({"text/plain"})
/*     */   public String saveorupdateCar(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 254 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 255 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 256 */     String str = "";
/*     */     try {
/* 258 */       User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 259 */       if (!HrmUserVarify.checkUserRight("Car:Maintenance", user)) {
/* 260 */         str = "" + SystemEnv.getHtmlLabelName(10004394, ThreadVarLanguage.getLang()) + "";
/* 261 */         throw new Exception(str);
/*     */       } 
/* 263 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("operation"));
/* 264 */       String str2 = Util.fromScreen(paramHttpServletRequest.getParameter("id"), user.getLanguage());
/* 265 */       hashMap.put("carid", str2);
/* 266 */       String str3 = Util.fromScreen(paramHttpServletRequest.getParameter("carNo"), user.getLanguage());
/* 267 */       String str4 = Util.fromScreen(paramHttpServletRequest.getParameter("carType"), user.getLanguage());
/* 268 */       String str5 = Util.null2String(paramHttpServletRequest.getParameter("usefee"));
/* 269 */       String str6 = Util.fromScreen(paramHttpServletRequest.getParameter("factoryNo"), user.getLanguage());
/* 270 */       String str7 = Util.fromScreen(paramHttpServletRequest.getParameter("price"), user.getLanguage());
/* 271 */       String str8 = Util.fromScreen(paramHttpServletRequest.getParameter("buyDate"), user.getLanguage());
/* 272 */       String str9 = Util.fromScreen(paramHttpServletRequest.getParameter("engineNo"), user.getLanguage());
/* 273 */       String str10 = Util.fromScreen(paramHttpServletRequest.getParameter("driver"), user.getLanguage());
/* 274 */       String str11 = Util.fromScreen(paramHttpServletRequest.getParameter("remark"), user.getLanguage());
/* 275 */       String str12 = Util.fromScreen(paramHttpServletRequest.getParameter("subCompanyId"), user.getLanguage());
/* 276 */       String str13 = Util.null2String(paramHttpServletRequest.getParameter("showorder"));
/* 277 */       String str14 = Util.fromScreen(paramHttpServletRequest.getParameter("deposit"), user.getLanguage());
/* 278 */       RecordSet recordSet = new RecordSet();
/* 279 */       boolean bool = false;
/* 280 */       if (str1.equals("add")) {
/* 281 */         String str15 = "insert into CarInfo(";
/* 282 */         String str16 = "values(";
/* 283 */         if (!str3.equals("")) {
/* 284 */           str15 = str15 + "carNo";
/* 285 */           str16 = str16 + "'" + str3 + "'";
/*     */         } 
/* 287 */         if (!str4.equals("")) {
/* 288 */           str15 = str15 + ",carType";
/* 289 */           str16 = str16 + "," + str4 + "";
/*     */         } 
/* 291 */         if (!"".equals(str5)) {
/* 292 */           str15 = str15 + ",usefee";
/* 293 */           str16 = str16 + "," + str5 + "";
/*     */         } 
/* 295 */         if (!str6.equals("")) {
/* 296 */           str15 = str15 + ",factoryNo";
/* 297 */           str16 = str16 + ",'" + str6 + "'";
/*     */         } 
/* 299 */         if (!str7.equals("")) {
/* 300 */           str15 = str15 + ",price";
/* 301 */           str16 = str16 + "," + str7 + "";
/*     */         } 
/* 303 */         if (!str8.equals("")) {
/* 304 */           str15 = str15 + ",buyDate";
/* 305 */           str16 = str16 + ",'" + str8 + "'";
/*     */         } 
/* 307 */         if (!str9.equals("")) {
/* 308 */           str15 = str15 + ",engineNo";
/* 309 */           str16 = str16 + ",'" + str9 + "'";
/*     */         } 
/* 311 */         if (!str10.equals("")) {
/* 312 */           str15 = str15 + ",driver";
/* 313 */           str16 = str16 + "," + str10 + "";
/*     */         } 
/* 315 */         if (!str11.equals("")) {
/* 316 */           str15 = str15 + ",remark";
/* 317 */           str16 = str16 + ",'" + str11 + "'";
/*     */         } 
/* 319 */         if (!str12.equals("")) {
/* 320 */           str15 = str15 + ",subCompanyId";
/* 321 */           str16 = str16 + "," + str12 + "";
/*     */         } 
/* 323 */         if (!"".equals(str13)) {
/* 324 */           str15 = str15 + ",showorder";
/* 325 */           str16 = str16 + "," + str13 + "";
/*     */         } 
/* 327 */         str15 = str15 + ",deposit";
/* 328 */         str16 = str16 + "," + ("".equals(str14) ? "0" : str14);
/* 329 */         String str17 = str15 + ") " + str16 + ")";
/* 330 */         bool = recordSet.execute(str17);
/* 331 */         str17 = "select max(id) carid from CarInfo ";
/* 332 */         recordSet.executeQuery(str17, new Object[0]);
/* 333 */         if (recordSet.next()) {
/* 334 */           str2 = Util.null2String(recordSet.getString("carid"));
/* 335 */           hashMap.put("carid", str2);
/* 336 */           CarUtil.insertShare(str2, str12);
/*     */         } 
/* 338 */       } else if (str1.equals("edit")) {
/* 339 */         if (!str2.equals("")) {
/* 340 */           String str15 = "update CarInfo set";
/* 341 */           if (!str3.equals("")) {
/* 342 */             str15 = str15 + " carNo='" + str3 + "'";
/*     */           }
/* 344 */           if (!str4.equals("")) {
/* 345 */             str15 = str15 + ",carType=" + str4 + "";
/*     */           }
/* 347 */           if (!"".equals(str5)) {
/* 348 */             str15 = str15 + ",usefee=" + str5 + "";
/*     */           }
/* 350 */           if (!str6.equals("")) {
/* 351 */             str15 = str15 + ",factoryNo='" + str6 + "'";
/*     */           }
/* 353 */           if (!str10.equals("")) {
/* 354 */             str15 = str15 + ",driver='" + str10 + "'";
/*     */           } else {
/* 356 */             str15 = str15 + ",driver=null";
/*     */           } 
/* 358 */           if (!str12.equals("")) {
/* 359 */             str15 = str15 + ",subCompanyId='" + str12 + "'";
/*     */           } else {
/* 361 */             str15 = str15 + ",subCompanyId=null";
/*     */           } 
/* 363 */           if (str7.equals("")) str7 = "null"; 
/* 364 */           str15 = str15 + ",price=" + str7 + "";
/* 365 */           str15 = str15 + ",showorder=" + str13 + "";
/* 366 */           str15 = str15 + ",buyDate='" + str8 + "'";
/* 367 */           str15 = str15 + ",engineNo='" + str9 + "'";
/* 368 */           str15 = str15 + ",remark='" + str11 + "'";
/* 369 */           str15 = str15 + ",deposit='" + ("1".equals(str14) ? "1" : "0") + "'";
/* 370 */           str15 = str15 + " where id=" + str2;
/* 371 */           bool = recordSet.execute(str15);
/*     */         } 
/* 373 */       } else if (str1.equals("deposit")) {
/* 374 */         String str15 = "update carinfo set deposit=? where id=?";
/* 375 */         bool = recordSet.executeUpdate(str15, new Object[] { "1".equals(str14) ? "0" : "1", str2 });
/*     */       } 
/* 377 */       if (bool) {
/* 378 */         hashMap.put("status", "1");
/* 379 */         CarInfoComInfo carInfoComInfo = new CarInfoComInfo();
/* 380 */         carInfoComInfo.removeCarInfoComInfoCache();
/*     */       } else {
/* 382 */         str = "" + SystemEnv.getHtmlLabelName(10004395, ThreadVarLanguage.getLang()) + "";
/* 383 */         throw new Exception(str);
/*     */       }
/*     */     
/* 386 */     } catch (Exception exception) {
/* 387 */       writeLog(exception);
/* 388 */       hashMap.put("status", "0");
/* 389 */       if (StringHelper.isEmpty(str)) {
/* 390 */         str = "catch exception : " + exception.getMessage();
/*     */       }
/* 392 */       hashMap.put("error", str);
/*     */     } 
/* 394 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCarUseSingtonList")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarUseSingtonList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*     */     Map<String, String> map;
/* 405 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 406 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 408 */       map = (new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).getCarUseSingtonList();
/* 409 */       map.put("status", "1");
/* 410 */     } catch (Exception exception) {
/* 411 */       writeLog(exception);
/* 412 */       map.put("status", "0");
/* 413 */       map.put("error", "catch exception : " + exception.getMessage());
/*     */     } 
/* 415 */     return JSONObject.toJSONString(map, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getCarShareFields")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarShareFields(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 429 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 431 */       hashMap.putAll((new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).getCarShareFields(ParamUtil.request2Map(paramHttpServletRequest)));
/* 432 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 433 */     } catch (Exception exception) {
/* 434 */       exception.printStackTrace();
/* 435 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 436 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 438 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getShareList")
/*     */   @Produces({"text/plain"})
/*     */   public String getShareList(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 452 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 454 */       hashMap.putAll((new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).getShareList(ParamUtil.request2Map(paramHttpServletRequest)));
/* 455 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 456 */     } catch (Exception exception) {
/* 457 */       exception.printStackTrace();
/* 458 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 459 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 461 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/saveShare")
/*     */   @Produces({"text/plain"})
/*     */   public String saveShare(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 475 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 477 */       hashMap.putAll((new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).saveShare(ParamUtil.request2Map(paramHttpServletRequest)));
/* 478 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 479 */     } catch (Exception exception) {
/* 480 */       exception.printStackTrace();
/* 481 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 482 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 484 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/delShare")
/*     */   @Produces({"text/plain"})
/*     */   public String delShare(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) throws Exception {
/* 498 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 500 */       hashMap.putAll((new CarBaseService(paramHttpServletRequest, paramHttpServletResponse)).delShare(ParamUtil.request2Map(paramHttpServletRequest)));
/* 501 */       hashMap.put("api_status", Boolean.valueOf(true));
/* 502 */     } catch (Exception exception) {
/* 503 */       exception.printStackTrace();
/* 504 */       hashMap.put("api_status", Boolean.valueOf(false));
/* 505 */       hashMap.put("api_errormsg", "catch exception : " + exception.getMessage());
/*     */     } 
/* 507 */     return JSONObject.toJSONString(hashMap, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/web/CarBaseAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */