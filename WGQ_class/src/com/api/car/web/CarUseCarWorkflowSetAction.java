/*     */ package com.api.car.web;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.alibaba.fastjson.serializer.SerializerFeature;
/*     */ import com.api.car.service.CarUseCarWorkflowSetService;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.ws.rs.GET;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import javax.ws.rs.core.Context;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Path("/car/UseCarSet")
/*     */ public class CarUseCarWorkflowSetAction
/*     */   extends BaseBean
/*     */ {
/*     */   private boolean checkRight(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  34 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  35 */     if (!HrmUserVarify.checkUserRight("Car:Maintenance", user)) {
/*  36 */       return false;
/*     */     }
/*  38 */     return true;
/*     */   }
/*     */   
/*  41 */   private Map<String, Object> noRightMap = new HashMap<String, Object>()
/*     */     {
/*     */     
/*     */     };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/getUseCarWorkflowSetList")
/*     */   @Produces({"text/plain"})
/*     */   public String getCarApplySetting(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  56 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/*  57 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/*  58 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/*  60 */     Map<String, String> map = (Map)new HashMap<>();
/*  61 */     String str = "";
/*     */     try {
/*  63 */       map = (new CarUseCarWorkflowSetService(paramHttpServletRequest, paramHttpServletResponse)).getUseCarWorkflowSetList();
/*  64 */     } catch (Exception exception) {
/*  65 */       writeLog(exception);
/*  66 */       map.put("status", "0");
/*  67 */       str = "catch exception : " + exception.getMessage();
/*  68 */       map.put("error", str);
/*     */     } 
/*  70 */     map.put("isRight", Integer.valueOf(1));
/*  71 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getCreateUseCarWorkflowSetInitData")
/*     */   @Produces({"text/plain"})
/*     */   public String getCreateUseCarWorkflowSetInitData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/*  84 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/*  85 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/*  86 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/*  88 */     Map<String, String> map = (Map)new HashMap<>();
/*  89 */     String str = "";
/*     */     try {
/*  91 */       map = (new CarUseCarWorkflowSetService(paramHttpServletRequest, paramHttpServletResponse)).getCreateUseCarWorkflowSetInitData();
/*  92 */     } catch (Exception exception) {
/*  93 */       writeLog(exception);
/*  94 */       map.put("status", "0");
/*  95 */       str = "catch exception : " + exception.getMessage();
/*  96 */       map.put("error", str);
/*     */     } 
/*  98 */     map.put("isRight", Integer.valueOf(1));
/*  99 */     return JSONObject.toJSONString(map, new SerializerFeature[] { SerializerFeature.DisableCircularReferenceDetect });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/submitCreateUseCarWorkflowSet")
/*     */   @Produces({"text/plain"})
/*     */   public String saveCreateUseCarWorkflowSet(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 110 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 111 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/* 112 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/* 114 */     Map<String, String> map = (Map)new HashMap<>();
/* 115 */     String str = "";
/*     */     try {
/* 117 */       map = (new CarUseCarWorkflowSetService(paramHttpServletRequest, paramHttpServletResponse)).saveCreateUseCarWorkflowSet();
/* 118 */     } catch (Exception exception) {
/* 119 */       writeLog(exception);
/* 120 */       map.put("status", "0");
/* 121 */       str = "catch exception : " + exception.getMessage();
/* 122 */       map.put("error", str);
/*     */     } 
/* 124 */     map.put("isRight", Integer.valueOf(1));
/* 125 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getAddUseCarWorkflowSetInitData")
/*     */   @Produces({"text/plain"})
/*     */   public String getAddUseCarWorkflowSetInitData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 136 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 137 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/* 138 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/* 140 */     Map<String, String> map = (Map)new HashMap<>();
/* 141 */     String str = "";
/*     */     try {
/* 143 */       map = (new CarUseCarWorkflowSetService(paramHttpServletRequest, paramHttpServletResponse)).getAddUseCarWorkflowSetInitData();
/* 144 */     } catch (Exception exception) {
/* 145 */       writeLog(exception);
/* 146 */       map.put("status", "0");
/* 147 */       str = "catch exception : " + exception.getMessage();
/* 148 */       map.put("error", str);
/*     */     } 
/* 150 */     map.put("isRight", Integer.valueOf(1));
/* 151 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getEditUseCarWorkflowSetInitData")
/*     */   @Produces({"text/plain"})
/*     */   public String getEditUseCarWorkflowSetInitData(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 162 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 163 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/* 164 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/* 166 */     Map<String, String> map = (Map)new HashMap<>();
/* 167 */     String str = "";
/*     */     try {
/* 169 */       map = (new CarUseCarWorkflowSetService(paramHttpServletRequest, paramHttpServletResponse)).getAddUseCarWorkflowSetInitData();
/* 170 */       map = (new CarUseCarWorkflowSetService(paramHttpServletRequest, paramHttpServletResponse)).getCarRelateModeInfo(map);
/* 171 */     } catch (Exception exception) {
/* 172 */       writeLog(exception);
/* 173 */       map.put("status", "0");
/* 174 */       str = "catch exception : " + exception.getMessage();
/* 175 */       map.put("error", str);
/*     */     } 
/* 177 */     map.put("isRight", Integer.valueOf(1));
/*     */     
/* 179 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @GET
/*     */   @Path("/getWorkflowDetachable")
/*     */   @Produces({"text/plain"})
/*     */   public String getWorkflowDetachable(@Context HttpServletRequest paramHttpServletRequest, @Context HttpServletResponse paramHttpServletResponse) {
/* 190 */     paramHttpServletResponse.setContentType("application/x-www-form-urlencoded; charset=utf-8");
/* 191 */     if (!checkRight(paramHttpServletRequest, paramHttpServletResponse)) {
/* 192 */       return JSONObject.toJSONString(this.noRightMap);
/*     */     }
/* 194 */     Map<String, String> map = (Map)new HashMap<>();
/* 195 */     String str = "";
/*     */     try {
/* 197 */       map = (new CarUseCarWorkflowSetService(paramHttpServletRequest, paramHttpServletResponse)).getWorkflowDetachable();
/* 198 */     } catch (Exception exception) {
/* 199 */       writeLog(exception);
/* 200 */       map.put("status", "0");
/* 201 */       str = "catch exception : " + exception.getMessage();
/* 202 */       map.put("error", str);
/*     */     } 
/* 204 */     return JSONObject.toJSONString(map);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/web/CarUseCarWorkflowSetAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */