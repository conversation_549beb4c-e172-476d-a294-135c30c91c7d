/*     */ package com.api.car.util;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarSetInfo
/*     */   extends BaseBean
/*     */ {
/*  19 */   private StaticObj staticobj = null;
/*     */   
/*     */   int id;
/*     */   
/*     */   int timeRangeStart;
/*     */   
/*     */   int timeRangeEnd;
/*     */   
/*  27 */   int dspUnit = 1;
/*     */   
/*  29 */   String usedColor = "";
/*     */   
/*  31 */   String agreementColor = "";
/*     */   
/*  33 */   String conflictedColor = "";
/*     */   
/*  35 */   String usedColorFont = "";
/*     */   
/*  37 */   String agreementColorFont = "";
/*     */   
/*  39 */   String conflictedColorFont = "";
/*     */ 
/*     */   
/*     */   int isRemind;
/*     */ 
/*     */   
/*     */   int remindType;
/*     */ 
/*     */   
/*     */   int isShare;
/*     */ 
/*     */   
/*     */   int isOpenShare;
/*     */ 
/*     */   
/*     */   public CarSetInfo() {
/*  55 */     this.staticobj = StaticObj.getInstance();
/*  56 */     if (this.staticobj.getObject("carsetinfo") == null) init(); 
/*  57 */     this.id = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "id")));
/*  58 */     this.timeRangeStart = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "timeRangeStart")));
/*  59 */     this.timeRangeEnd = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "timeRangeEnd")));
/*  60 */     this.dspUnit = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "dspUnit")));
/*  61 */     this.usedColor = Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "usedColor"));
/*  62 */     this.agreementColor = Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "agreementColor"));
/*  63 */     this.conflictedColor = Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "conflictedColor"));
/*  64 */     this.usedColorFont = Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "usedColorFont"));
/*  65 */     this.agreementColorFont = Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "agreementColorFont"));
/*  66 */     this.conflictedColorFont = Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "conflictedColorFont"));
/*  67 */     this.isRemind = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "isRemind")));
/*  68 */     this.remindType = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "remindType")));
/*  69 */     this.isShare = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "isShare")));
/*  70 */     this.isOpenShare = Util.getIntValue(Util.null2String(this.staticobj.getRecordFromObj("carsetinfo", "isOpenShare")));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init() {
/*     */     try {
/*  78 */       RecordSet recordSet = new RecordSet();
/*  79 */       recordSet.execute("select * from mode_carremindset order by id");
/*  80 */       if (recordSet.next()) {
/*  81 */         this.staticobj.putRecordToObj("carsetinfo", "id", Integer.valueOf(Util.getIntValue(recordSet.getString("id"))));
/*  82 */         this.staticobj.putRecordToObj("carsetinfo", "timeRangeStart", Integer.valueOf(Util.getIntValue(recordSet.getString("timeRangeStart"), 0)));
/*  83 */         this.staticobj.putRecordToObj("carsetinfo", "timeRangeEnd", Integer.valueOf(Util.getIntValue(recordSet.getString("timeRangeEnd"), 23)));
/*  84 */         this.staticobj.putRecordToObj("carsetinfo", "dspUnit", Integer.valueOf(Util.getIntValue(recordSet.getString("dspUnit"), 1)));
/*  85 */         this.staticobj.putRecordToObj("carsetinfo", "usedColor", Util.null2String(recordSet.getString("usedColor")));
/*  86 */         this.staticobj.putRecordToObj("carsetinfo", "agreementColor", Util.null2String(recordSet.getString("agreementColor")));
/*  87 */         this.staticobj.putRecordToObj("carsetinfo", "conflictedColor", Util.null2String(recordSet.getString("conflictedColor")));
/*  88 */         this.staticobj.putRecordToObj("carsetinfo", "usedColorFont", Util.null2String(recordSet.getString("usedColorFont")));
/*  89 */         this.staticobj.putRecordToObj("carsetinfo", "agreementColorFont", Util.null2String(recordSet.getString("agreementColorFont")));
/*  90 */         this.staticobj.putRecordToObj("carsetinfo", "conflictedColorFont", Util.null2String(recordSet.getString("conflictedColorFont")));
/*  91 */         this.staticobj.putRecordToObj("carsetinfo", "isRemind", Integer.valueOf(Util.getIntValue(recordSet.getString("isremind"), 1)));
/*  92 */         this.staticobj.putRecordToObj("carsetinfo", "remindType", Integer.valueOf(Util.getIntValue(recordSet.getString("remindtype"), 1)));
/*  93 */         this.staticobj.putRecordToObj("carsetinfo", "isShare", Integer.valueOf(Util.getIntValue(recordSet.getString("isshare"), 0)));
/*  94 */         this.staticobj.putRecordToObj("carsetinfo", "isOpenShare", Integer.valueOf(Util.getIntValue(recordSet.getString("isopenshare"), 0)));
/*     */       } 
/*  96 */     } catch (Exception exception) {
/*  97 */       (new BaseBean()).writeLog("车辆配置信息初始化失败...." + exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void removeCarSetInfoCache() {
/* 102 */     this.staticobj.removeObject("carsetinfo");
/*     */   }
/*     */   
/*     */   public int getId() {
/* 106 */     return this.id;
/*     */   }
/*     */   
/*     */   public int getTimeRangeStart() {
/* 110 */     return this.timeRangeStart;
/*     */   }
/*     */   
/*     */   public int getTimeRangeEnd() {
/* 114 */     return this.timeRangeEnd;
/*     */   }
/*     */   
/*     */   public int getDspUnit() {
/* 118 */     return this.dspUnit;
/*     */   }
/*     */   
/*     */   public String getUsedColor() {
/* 122 */     return this.usedColor;
/*     */   }
/*     */   
/*     */   public String getAgreementColor() {
/* 126 */     return this.agreementColor;
/*     */   }
/*     */   
/*     */   public String getConflictedColor() {
/* 130 */     return this.conflictedColor;
/*     */   }
/*     */   
/*     */   public String getUsedColorFont() {
/* 134 */     return this.usedColorFont;
/*     */   }
/*     */   
/*     */   public String getAgreementColorFont() {
/* 138 */     return this.agreementColorFont;
/*     */   }
/*     */   
/*     */   public String getConflictedColorFont() {
/* 142 */     return this.conflictedColorFont;
/*     */   }
/*     */   
/*     */   public int getIsRemind() {
/* 146 */     return this.isRemind;
/*     */   }
/*     */   
/*     */   public int getRemindType() {
/* 150 */     return this.remindType;
/*     */   }
/*     */   
/*     */   public int getIsShare() {
/* 154 */     return this.isShare;
/*     */   }
/*     */   
/*     */   public int getIsOpenShare() {
/* 158 */     return this.isOpenShare;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/util/CarSetInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */