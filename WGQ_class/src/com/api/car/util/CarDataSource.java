/*     */ package com.api.car.util;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.car.Maint.CarTransMethod;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.exttools.impexp.common.CodeUtils;
/*     */ import weaver.formmode.service.CommonConstant;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.request.RequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarDataSource
/*     */ {
/*     */   public List<Map<String, String>> getCarReportListData(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  25 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  26 */     String str1 = Util.null2String(paramMap.get("sql"));
/*  27 */     str1 = CodeUtils.getFromBase64(str1);
/*  28 */     String str2 = Util.null2String(paramMap.get("cancelString"));
/*  29 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("sortParams"));
/*  30 */     if (null != str3 && !"[]".equals(str3) && !"".equals(str3)) {
/*  31 */       String str = getOrderStr(str3);
/*  32 */       if (!"".equals(str)) {
/*  33 */         str = str.replaceAll(",$", "");
/*  34 */         str1 = "select * from (" + str1 + " )temp order by " + str;
/*     */       } 
/*     */     } 
/*  37 */     str1 = Util.toSqlForSplitPage(str1);
/*  38 */     (new BaseBean()).writeLog("getCarReportListData:" + str1);
/*  39 */     RecordSet recordSet = new RecordSet();
/*  40 */     recordSet.execute(str1);
/*  41 */     CarTransMethod carTransMethod = new CarTransMethod();
/*  42 */     RequestComInfo requestComInfo = new RequestComInfo();
/*  43 */     while (recordSet.next()) {
/*  44 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  45 */       String str4 = recordSet.getString("carNo");
/*  46 */       String str5 = recordSet.getString("id");
/*  47 */       String str6 = recordSet.getString("requestid");
/*  48 */       String str7 = recordSet.getString("requestname");
/*  49 */       String str8 = recordSet.getString("workflowid");
/*  50 */       String str9 = recordSet.getString("viewtype");
/*  51 */       String str10 = recordSet.getString("nodeid");
/*  52 */       String str11 = recordSet.getString("isremark");
/*  53 */       String str12 = recordSet.getString("agentorbyagentid");
/*  54 */       String str13 = recordSet.getString("agenttype");
/*  55 */       String str14 = recordSet.getString("isprocessed");
/*  56 */       String str15 = recordSet.getString("userid");
/*  57 */       String str16 = recordSet.getString("creater");
/*     */       
/*  59 */       String str17 = recordSet.getString("startdate");
/*  60 */       String str18 = recordSet.getString("starttime");
/*  61 */       String str19 = recordSet.getString("enddate");
/*  62 */       String str20 = recordSet.getString("endtime");
/*  63 */       String str21 = recordSet.getString("aid");
/*  64 */       String str22 = recordSet.getString("tablename");
/*  65 */       String str23 = recordSet.getString("fieldname");
/*  66 */       str23 = "".equals(str23) ? " " : str23;
/*  67 */       String str24 = "";
/*  68 */       String str25 = "0";
/*  69 */       StringBuilder stringBuilder = new StringBuilder();
/*  70 */       stringBuilder.append(str6).append("+");
/*  71 */       stringBuilder.append(str8).append("+");
/*  72 */       stringBuilder.append(str9).append("+");
/*  73 */       stringBuilder.append(str25).append("+");
/*  74 */       stringBuilder.append(paramUser.getLanguage()).append("+");
/*  75 */       stringBuilder.append(str10).append("+");
/*  76 */       stringBuilder.append(str11).append("+");
/*  77 */       stringBuilder.append(paramUser.getUID()).append("+");
/*  78 */       stringBuilder.append(str12).append("+");
/*  79 */       stringBuilder.append(str13).append("+");
/*  80 */       stringBuilder.append(str14).append("+");
/*  81 */       stringBuilder.append(str15).append("+");
/*  82 */       stringBuilder.append(str24).append("+");
/*  83 */       stringBuilder.append(str16).append("+");
/*  84 */       String str26 = "<a href='" + GCONST.getContextPath() + "/workflow/request/ViewRequestForwardSPA.jsp?ismonitor=1&requestid=" + str6 + "' target='_blank'>" + str7 + "</a>";
/*  85 */       String str27 = carTransMethod.getUsername(str15);
/*  86 */       String str28 = requestComInfo.getRequestStatus(str6);
/*     */ 
/*     */       
/*  89 */       String str29 = str17 + " " + str18;
/*  90 */       String str30 = str19 + " " + str20;
/*  91 */       String str31 = str6 + "+" + paramUser.getLanguage() + "+" + str2 + "+" + str22 + "+" + str23;
/*  92 */       String str32 = carTransMethod.getCarStatus(str21, str31);
/*  93 */       hashMap.put("carNo", str4);
/*  94 */       hashMap.put("carid", str5);
/*  95 */       hashMap.put("requestname", str26);
/*  96 */       hashMap.put("userid", str27);
/*  97 */       hashMap.put("requestid", str28);
/*  98 */       hashMap.put("startdate", str29);
/*  99 */       hashMap.put("enddate", str30);
/* 100 */       hashMap.put("aid", str32);
/* 101 */       arrayList.add(hashMap);
/*     */     } 
/* 103 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private String getOrderStr(String paramString) {
/* 107 */     String str = "";
/* 108 */     JSONArray jSONArray = JSONArray.parseArray(paramString);
/* 109 */     for (byte b = 0; b < jSONArray.size(); b++) {
/* 110 */       String[] arrayOfString = jSONArray.getJSONObject(b).getString("orderkey").split(",");
/* 111 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 112 */         if (null != arrayOfString[b1]) {
/*     */ 
/*     */           
/* 115 */           String str1 = arrayOfString[b1].trim();
/* 116 */           if ("enddate".equalsIgnoreCase(str1)) {
/* 117 */             str1 = CommonConstant.getConcatSql(new String[] { "enddate", "' '", "endtime" });
/* 118 */           } else if ("startdate".equalsIgnoreCase(str1)) {
/* 119 */             str1 = CommonConstant.getConcatSql(new String[] { "startdate", "' '", "starttime" });
/*     */           } 
/*     */ 
/*     */           
/* 123 */           str = str + str1 + "  " + (!"".equals(jSONArray.getJSONObject(b).getString("sortOrder")) ? jSONArray.getJSONObject(b).getString("sortOrder").replace("end", "") : "") + ",";
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 129 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/util/CarDataSource.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */