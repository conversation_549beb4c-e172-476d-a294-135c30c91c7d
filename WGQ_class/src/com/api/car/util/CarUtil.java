/*     */ package com.api.car.util;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionItem;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import com.weaver.general.TimeUtil;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import weaver.WorkPlan.MutilUserUtil;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarUtil
/*     */ {
/*     */   public static SearchConditionItem castSearchConditionItem(SearchConditionItem paramSearchConditionItem) {
/*  31 */     paramSearchConditionItem.setLabelcol(6);
/*  32 */     paramSearchConditionItem.setFieldcol(18);
/*  33 */     return paramSearchConditionItem;
/*     */   }
/*     */   
/*     */   public static SearchConditionItem castSearchConditionItem(SearchConditionItem paramSearchConditionItem, int paramInt1, int paramInt2) {
/*  37 */     paramSearchConditionItem.setLabelcol(paramInt1);
/*  38 */     paramSearchConditionItem.setFieldcol(paramInt2);
/*  39 */     return paramSearchConditionItem;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<SearchConditionOption> getDateSelectOption(int paramInt, boolean paramBoolean1, boolean paramBoolean2) {
/*  51 */     ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*  52 */     arrayList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, paramInt), true));
/*  53 */     arrayList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(15537, paramInt)));
/*  54 */     arrayList.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(15539, paramInt)));
/*  55 */     arrayList.add(new SearchConditionOption("3", SystemEnv.getHtmlLabelName(15541, paramInt)));
/*  56 */     if (paramBoolean1) {
/*  57 */       arrayList.add(new SearchConditionOption("7", SystemEnv.getHtmlLabelName(27347, paramInt)));
/*     */     }
/*  59 */     arrayList.add(new SearchConditionOption("4", SystemEnv.getHtmlLabelName(21904, paramInt)));
/*  60 */     arrayList.add(new SearchConditionOption("5", SystemEnv.getHtmlLabelName(15384, paramInt)));
/*  61 */     if (paramBoolean2) {
/*  62 */       arrayList.add(new SearchConditionOption("8", SystemEnv.getHtmlLabelName(81716, paramInt)));
/*     */     }
/*  64 */     arrayList.add(new SearchConditionOption("6", SystemEnv.getHtmlLabelName(32530, paramInt)));
/*  65 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getTimesBg(int paramInt1, int paramInt2) {
/*  75 */     int i = (paramInt1 + 1) * 60 / paramInt2;
/*  76 */     int j = i / 60;
/*  77 */     int k = i % 60;
/*  78 */     return ((j > 9) ? ("" + j) : ("0" + j)) + ":" + ((k > 9) ? ("" + k) : ("0" + k));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getTimesEd(int paramInt1, int paramInt2) {
/*  88 */     int i = (paramInt1 + 1) * 60 / paramInt2;
/*  89 */     int j = i / 60;
/*  90 */     int k = i % 60;
/*  91 */     if (k == 0) {
/*  92 */       k = 59;
/*  93 */       j--;
/*     */     } else {
/*  95 */       k--;
/*     */     } 
/*  97 */     return ((j > 9) ? ("" + j) : ("0" + j)) + ":" + ((k > 9) ? ("" + k) : ("0" + k));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getMinuteOfDay(String paramString) {
/* 109 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, ":");
/* 110 */     return Integer.parseInt(arrayList.get(0)) * 60 + Integer.parseInt(arrayList.get(1));
/*     */   }
/*     */   
/*     */   public ArrayList<String> getCarOperateBtns(String paramString1, String paramString2, String paramString3) {
/* 114 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString2, "+");
/* 115 */     String str1 = arrayList1.get(0);
/* 116 */     String str2 = arrayList1.get(1);
/* 117 */     String str3 = arrayList1.get(2);
/* 118 */     String str4 = "";
/* 119 */     if (arrayList1.size() > 3) {
/* 120 */       str4 = arrayList1.get(3);
/*     */     }
/* 122 */     String str5 = "-1";
/* 123 */     if (!"".equals(str4)) {
/* 124 */       for (String str8 : str4.split(",")) {
/* 125 */         String[] arrayOfString = str8.split(":");
/* 126 */         String str9 = arrayOfString[0];
/* 127 */         String str10 = arrayOfString[1];
/* 128 */         if (str9.equals(str1)) {
/* 129 */           str5 = str10;
/*     */         }
/*     */       } 
/*     */     } else {
/* 133 */       str5 = str3;
/*     */     } 
/* 135 */     ArrayList<String> arrayList2 = new ArrayList();
/* 136 */     String str6 = "false";
/* 137 */     if (Util.getIntValue(str5) > 0) {
/* 138 */       str6 = "true";
/*     */     }
/* 140 */     arrayList2.add(str6);
/*     */     
/* 142 */     String str7 = "false";
/* 143 */     if (Util.getIntValue(str5) > 1) {
/* 144 */       str7 = "true";
/*     */     }
/* 146 */     arrayList2.add(str7);
/* 147 */     if (Util.getIntValue(str5) > 0) {
/* 148 */       if ("1".equals(paramString3)) {
/* 149 */         arrayList2.add("false");
/* 150 */         arrayList2.add("true");
/*     */       } else {
/* 152 */         arrayList2.add("true");
/* 153 */         arrayList2.add("false");
/*     */       } 
/*     */     }
/* 156 */     if ("1".equals(str2)) {
/* 157 */       arrayList2.add(str6);
/*     */     }
/* 159 */     return arrayList2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCarDeposit(String paramString1, String paramString2) {
/* 169 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/* 170 */     String str1 = arrayList.get(0);
/* 171 */     String str2 = arrayList.get(1);
/* 172 */     String str3 = arrayList.get(2);
/* 173 */     str2 = "1".equals(str2) ? ("(" + SystemEnv.getHtmlLabelName(22205, Integer.parseInt(str3)) + ")") : "";
/* 174 */     return str1 + str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCompanyname(String paramString1, String paramString2) {
/* 184 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 185 */     return Util.formatMultiLang(subCompanyComInfo.getSubCompanyname(paramString1), paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestName(String paramString1, String paramString2) {
/* 196 */     if (paramString2 == null || paramString2.trim().equals(""))
/* 197 */       return ""; 
/* 198 */     RecordSet recordSet = new RecordSet();
/* 199 */     recordSet.executeProc("workflow_Requestbase_SByID", paramString2);
/* 200 */     if (recordSet.next()) return recordSet.getString("requestname"); 
/* 201 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUUid(String paramString) {
/* 211 */     return UUID.randomUUID().toString().replace("-", "").toLowerCase();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUsename(String paramString1, String paramString2) {
/* 222 */     int i = Util.getIntValue(paramString2, 7);
/* 223 */     if ("1".equals(paramString1) || "".equals(Util.null2String(paramString1))) {
/* 224 */       return SystemEnv.getHtmlLabelName(31676, i);
/*     */     }
/* 226 */     return SystemEnv.getHtmlLabelName(18096, i);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getDetachSql(User paramUser) {
/* 232 */     String str1 = "";
/* 233 */     RecordSet recordSet = new RecordSet();
/* 234 */     String str2 = "";
/* 235 */     String str3 = "";
/* 236 */     char c = Util.getSeparator();
/* 237 */     recordSet.executeProc("HrmRoleSR_SeByURId", "" + paramUser.getUID() + c + "Car:Maintenance");
/* 238 */     int i = -1;
/* 239 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 240 */     while (recordSet.next()) {
/* 241 */       str3 = Util.null2String(recordSet.getString("subcompanyid"));
/* 242 */       i = checkSubCompanyRight.ChkComRightByUserRightCompanyId(paramUser.getUID(), "Car:Maintenance", Util.getIntValue(str3, -1));
/* 243 */       if (i == -1) {
/*     */         continue;
/*     */       }
/* 246 */       str2 = str2 + ", " + str3;
/*     */     } 
/* 248 */     if (!"".equals(str2)) {
/* 249 */       str2 = str2.substring(1);
/* 250 */       str1 = str1 + " and subcompanyid in (" + str2 + ") ";
/*     */     } else {
/* 252 */       str1 = str1 + " and subcompanyid=" + paramUser.getUserSubCompany1() + " ";
/*     */     } 
/* 254 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getCarFlowName(String paramString1, String paramString2) {
/* 263 */     return "<a href='" + GCONST.getContextPath() + "/workflow/request/ViewRequest.jsp?requestid=" + paramString2 + "&isovertime=0' target='_newworks'>" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> checkCarShareOperate(String paramString) {
/* 274 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 276 */     arrayList.add("true");
/* 277 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void insertShare(String paramString1, String paramString2) {
/* 286 */     if ((new CarSetInfo()).getIsOpenShare() == 1) {
/* 287 */       RecordSet recordSet = new RecordSet();
/* 288 */       recordSet.execute("select carsdetachable from SystemSet");
/* 289 */       int i = 0;
/* 290 */       if (recordSet.next()) {
/* 291 */         i = recordSet.getInt(1);
/*     */       }
/* 293 */       if (i == 1) {
/* 294 */         String str = "insert into car_share(carid,permissiontype,subcompanyid,sublevel,sublevelmax) values(?,6,?,0,100)";
/* 295 */         recordSet.executeUpdate(str, new Object[] { paramString1, paramString2 });
/*     */       } else {
/* 297 */         String str = "insert into car_share(carid,permissiontype,seclevel,seclevelmax) values(?,3,0,100)";
/* 298 */         recordSet.executeUpdate(str, new Object[] { paramString1 });
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getCarShareSql(User paramUser) {
/* 310 */     CarSetInfo carSetInfo = new CarSetInfo();
/* 311 */     if (carSetInfo.getIsShare() != 1) {
/* 312 */       return " ";
/*     */     }
/*     */     
/* 315 */     ArrayList<User> arrayList = new ArrayList();
/* 316 */     arrayList.add(paramUser);
/* 317 */     if (MutilUserUtil.isShowBelongto(paramUser)) {
/* 318 */       List<? extends User> list = User.getBelongtoUsersByUserId(paramUser.getUID());
/* 319 */       if (list != null) arrayList.addAll(list); 
/*     */     } 
/* 321 */     User user = null;
/* 322 */     ResourceVirtualComInfo resourceVirtualComInfo = null;
/*     */     try {
/* 324 */       resourceVirtualComInfo = new ResourceVirtualComInfo();
/* 325 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/* 329 */     StringBuffer stringBuffer = new StringBuffer();
/* 330 */     stringBuffer.append(" and exists (");
/* 331 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 332 */       user = arrayList.get(b);
/* 333 */       String str1 = user.getUserDepartment() + "";
/* 334 */       String str2 = user.getUserSubCompany1() + "," + user.getUserSubCompany2() + "," + user.getUserSubCompany3() + "," + user.getUserSubCompany4();
/* 335 */       if (resourceVirtualComInfo != null) {
/* 336 */         String str3 = resourceVirtualComInfo.getDepartmentids(user.getUID() + "");
/* 337 */         if (!"".equals(str3)) {
/* 338 */           str1 = str1 + "," + str3;
/*     */         }
/* 340 */         String str4 = resourceVirtualComInfo.getSubcompanyids(user.getUID() + "");
/* 341 */         if (!"".equals(str4)) {
/* 342 */           str2 = str2 + "," + str4;
/*     */         }
/*     */       } 
/* 345 */       if (str1.endsWith(",")) {
/* 346 */         str1 = str1.substring(0, str1.length() - 1);
/*     */       }
/* 348 */       if (str2.endsWith(",")) {
/* 349 */         str2 = str2.substring(0, str2.length() - 1);
/*     */       }
/* 351 */       stringBuffer.append((b == 0) ? "" : "  UNION ALL  ");
/* 352 */       stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid and b.departmentid in (" + str1 + ") and b.deptlevel <= " + user.getSeclevel() + " AND b.deptlevelMax >= " + user.getSeclevel() + " AND b.permissiontype = 1 ");
/* 353 */       stringBuffer.append(" UNION ALL ");
/* 354 */       stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid and b.subcompanyid in (" + str2 + ") and b.sublevel <= " + user.getSeclevel() + " and b.sublevelMax >= " + user.getSeclevel() + " AND b.permissiontype = 6 ");
/* 355 */       stringBuffer.append(" UNION ALL ");
/* 356 */       stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid and b.seclevel <= " + user.getSeclevel() + " and b.seclevelMax >= " + user.getSeclevel() + " AND b.permissiontype = 3 ");
/* 357 */       stringBuffer.append(" UNION ALL ");
/* 358 */       stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid AND b.userid = " + user.getUID() + " AND b.permissiontype = 5 ");
/*     */       
/* 360 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 361 */       List<Map> list = hrmCommonServiceImpl.getRoleInfo(user.getUID());
/* 362 */       for (byte b1 = 0; b1 < list.size(); b1++) {
/* 363 */         Map map = list.get(b1);
/* 364 */         int i = Util.getIntValue((String)map.get("roleid"), -1);
/* 365 */         int j = Util.getIntValue((String)map.get("rolelevel"), 0);
/* 366 */         stringBuffer.append(" UNION ALL ");
/* 367 */         stringBuffer.append(" SELECT 1 FROM car_share b WHERE a.id = b.carid AND b.roleid =" + i + " AND " + j + " >= b.rolelevel AND b.roleseclevel <= " + user.getSeclevel() + " and b.roleseclevelMax >= " + user.getSeclevel() + " AND b.permissiontype = 2  ");
/*     */       } 
/* 369 */       stringBuffer.append(" UNION ALL ");
/* 370 */       if (StringUtil.isEmpty(user.getJobtitle())) {
/* 371 */         stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid AND b.jobtitleid is null and b.joblevel=0 AND b.permissiontype = 8 ");
/*     */       } else {
/* 373 */         stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid AND b.jobtitleid = '" + user.getJobtitle() + "' and b.joblevel=0 AND b.permissiontype = 8 ");
/*     */       } 
/*     */       
/* 376 */       String[] arrayOfString1 = str2.split(",");
/* 377 */       String[] arrayOfString2 = str1.split(","); byte b2;
/* 378 */       for (b2 = 0; b2 < arrayOfString1.length; b2++) {
/* 379 */         if (!"".equals(arrayOfString1[b2])) {
/* 380 */           stringBuffer.append(" UNION ALL ");
/* 381 */           if (StringUtil.isEmpty(user.getJobtitle())) {
/* 382 */             stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid AND b.jobtitleid is null AND (b.joblevelvalue = '" + arrayOfString1[b2] + "' or b.joblevelvalue like '%," + arrayOfString1[b2] + ",%' or b.joblevelvalue like '%," + arrayOfString1[b2] + "' or b.joblevelvalue like '" + arrayOfString1[b2] + ",%') and b.joblevel=1 AND b.permissiontype = 8 ");
/*     */           } else {
/* 384 */             stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid AND b.jobtitleid = '" + user.getJobtitle() + "' AND (b.joblevelvalue = '" + arrayOfString1[b2] + "' or b.joblevelvalue like '%," + arrayOfString1[b2] + ",%' or b.joblevelvalue like '%," + arrayOfString1[b2] + "' or b.joblevelvalue like '" + arrayOfString1[b2] + ",%') and b.joblevel=1 AND b.permissiontype = 8 ");
/*     */           } 
/*     */         } 
/*     */       } 
/* 388 */       for (b2 = 0; b2 < arrayOfString2.length; b2++) {
/* 389 */         if (!"".equals(arrayOfString2[b2])) {
/* 390 */           stringBuffer.append(" UNION ALL ");
/* 391 */           if (StringUtil.isEmpty(user.getJobtitle())) {
/* 392 */             stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid AND b.jobtitleid is null and (b.joblevelvalue = '" + arrayOfString2[b2] + "' or b.joblevelvalue like '%," + arrayOfString2[b2] + ",%' or b.joblevelvalue like '%," + arrayOfString2[b2] + "' or b.joblevelvalue like '" + arrayOfString2[b2] + ",%') and b.joblevel=2 AND b.permissiontype = 8 ");
/*     */           } else {
/* 394 */             stringBuffer.append(" SELECT 1 FROM car_share b where a.id = b.carid AND b.jobtitleid = '" + user.getJobtitle() + "' and (b.joblevelvalue = '" + arrayOfString2[b2] + "' or b.joblevelvalue like '%," + arrayOfString2[b2] + ",%' or b.joblevelvalue like '%," + arrayOfString2[b2] + "' or b.joblevelvalue like '" + arrayOfString2[b2] + ",%') and b.joblevel=2 AND b.permissiontype = 8 ");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 399 */     stringBuffer.append(") ");
/* 400 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFirstDayOfWeek(String paramString) {
/* 409 */     String str = paramString;
/*     */     try {
/* 411 */       SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
/* 412 */       Date date = simpleDateFormat1.parse(paramString);
/* 413 */       SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("EEEE");
/* 414 */       String str1 = simpleDateFormat2.format(date);
/* 415 */       if (SystemEnv.getHtmlLabelName(398, 7).equals(str1)) {
/* 416 */         paramString = TimeUtil.dateAdd(paramString, 1);
/*     */       }
/* 418 */       str = DateUtil.getFirstDayOfWeek(paramString);
/* 419 */     } catch (Exception exception) {
/* 420 */       (new BaseBean()).writeLog(exception);
/*     */     } 
/* 422 */     return str;
/*     */   }
/*     */   
/*     */   public static String addSql(Map paramMap) {
/* 426 */     String str1 = "";
/* 427 */     RecordSet recordSet = new RecordSet();
/* 428 */     String str2 = Util.null2s(Util.null2String(paramMap.get("627")), "0");
/* 429 */     String str3 = Util.null2s(Util.null2String(paramMap.get("628")), "0");
/* 430 */     String str4 = Util.null2s(Util.null2String(paramMap.get("629")), "0");
/* 431 */     if (recordSet.getDBType().equals("oracle")) {
/* 432 */       str1 = str1 + "to_number(decode(" + str2 + ",'',null," + str2 + ")) as carId,";
/* 433 */       str1 = str1 + "to_number(decode(" + str3 + ",'',null," + str3 + ")) as driver,";
/* 434 */       str1 = str1 + "to_number(decode(" + str4 + ",'',null," + str4 + ")) as userid,";
/* 435 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 436 */       str1 = str1 + Util.null2s(Util.null2String(paramMap.get("627")), "0") + " as carId,";
/* 437 */       str1 = str1 + Util.null2s(Util.null2String(paramMap.get("628")), "0") + " as driver,";
/* 438 */       str1 = str1 + Util.null2s(Util.null2String(paramMap.get("629")), "0") + " as userid,";
/*     */     } else {
/* 440 */       str1 = str1 + Util.null2s(Util.null2String(paramMap.get("627")), "0") + " as carId,";
/* 441 */       str1 = str1 + Util.null2s(Util.null2String(paramMap.get("628")), "0") + " as driver,";
/* 442 */       str1 = str1 + Util.null2s(Util.null2String(paramMap.get("629")), "0") + " as userid,";
/*     */     } 
/*     */     
/* 445 */     str1 = str1 + Util.null2s(Util.null2String(paramMap.get("634")), "''") + " as startDate,";
/* 446 */     str1 = str1 + Util.null2s(Util.null2String(paramMap.get("635")), "''") + " as startTime,";
/* 447 */     str1 = str1 + Util.null2s(Util.null2String(paramMap.get("636")), "''") + " as endDate,";
/* 448 */     str1 = str1 + Util.null2s(Util.null2String(paramMap.get("637")), "''") + " as endTime,";
/* 449 */     str1 = str1 + Util.null2s(Util.null2String(paramMap.get("639")), "'0'") + " as cancel";
/* 450 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/com/api/car/util/CarUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */