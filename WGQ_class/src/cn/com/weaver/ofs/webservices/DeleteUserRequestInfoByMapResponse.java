/*     */ package cn.com.weaver.ofs.webservices;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.lang.reflect.Array;
/*     */ import java.util.Arrays;
/*     */ import javax.xml.namespace.QName;
/*     */ import org.apache.axis.description.ElementDesc;
/*     */ import org.apache.axis.description.FieldDesc;
/*     */ import org.apache.axis.description.TypeDesc;
/*     */ import org.apache.axis.encoding.Deserializer;
/*     */ import org.apache.axis.encoding.Serializer;
/*     */ import org.apache.axis.encoding.ser.BeanDeserializer;
/*     */ import org.apache.axis.encoding.ser.BeanSerializer;
/*     */ 
/*     */ public class DeleteUserRequestInfoByMapResponse
/*     */   implements Serializable {
/*     */   public DeleteUserRequestInfoByMapResponse(AnyType2AnyTypeMapEntry[] out) {
/*  18 */     this.out = out;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private AnyType2AnyTypeMapEntry[] out;
/*     */ 
/*     */ 
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] getOut() {
/*  28 */     return this.out;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOut(AnyType2AnyTypeMapEntry[] out) {
/*  38 */     this.out = out;
/*     */   }
/*     */   
/*  41 */   private Object __equalsCalc = null;
/*     */   public synchronized boolean equals(Object obj) {
/*  43 */     if (!(obj instanceof DeleteUserRequestInfoByMapResponse)) return false; 
/*  44 */     DeleteUserRequestInfoByMapResponse other = (DeleteUserRequestInfoByMapResponse)obj;
/*  45 */     if (obj == null) return false; 
/*  46 */     if (this == obj) return true; 
/*  47 */     if (this.__equalsCalc != null) {
/*  48 */       return (this.__equalsCalc == obj);
/*     */     }
/*  50 */     this.__equalsCalc = obj;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  55 */     boolean _equals = ((this.out == null && other.getOut() == null) || (this.out != null && Arrays.equals((Object[])this.out, (Object[])other.getOut())));
/*  56 */     this.__equalsCalc = null;
/*  57 */     return _equals;
/*     */   }
/*     */   private boolean __hashCodeCalc = false;
/*     */   
/*     */   public synchronized int hashCode() {
/*  62 */     if (this.__hashCodeCalc) {
/*  63 */       return 0;
/*     */     }
/*  65 */     this.__hashCodeCalc = true;
/*  66 */     int _hashCode = 1;
/*  67 */     if (getOut() != null) {
/*  68 */       int i = 0;
/*  69 */       for (; i < Array.getLength(getOut()); 
/*  70 */         i++) {
/*  71 */         Object obj = Array.get(getOut(), i);
/*  72 */         if (obj != null && 
/*  73 */           !obj.getClass().isArray()) {
/*  74 */           _hashCode += obj.hashCode();
/*     */         }
/*     */       } 
/*     */     } 
/*  78 */     this.__hashCodeCalc = false;
/*  79 */     return _hashCode;
/*     */   }
/*     */ 
/*     */   
/*  83 */   private static TypeDesc typeDesc = new TypeDesc(DeleteUserRequestInfoByMapResponse.class, true);
/*     */ 
/*     */   
/*     */   static {
/*  87 */     typeDesc.setXmlType(new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByMapResponse"));
/*  88 */     ElementDesc elemField = new ElementDesc();
/*  89 */     elemField.setFieldName("out");
/*  90 */     elemField.setXmlName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  91 */     elemField.setXmlType(new QName("webservices.ofs.weaver.com.cn", ">anyType2anyTypeMap>entry"));
/*  92 */     elemField.setNillable(true);
/*  93 */     elemField.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  94 */     typeDesc.addFieldDesc((FieldDesc)elemField);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static TypeDesc getTypeDesc() {
/* 101 */     return typeDesc;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Serializer getSerializer(String mechType, Class _javaType, QName _xmlType) {
/* 111 */     return (Serializer)new BeanSerializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Deserializer getDeserializer(String mechType, Class _javaType, QName _xmlType) {
/* 123 */     return (Deserializer)new BeanDeserializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */   
/*     */   public DeleteUserRequestInfoByMapResponse() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/DeleteUserRequestInfoByMapResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */