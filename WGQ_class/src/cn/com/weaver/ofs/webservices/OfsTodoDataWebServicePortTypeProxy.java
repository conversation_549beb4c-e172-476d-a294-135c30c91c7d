/*     */ package cn.com.weaver.ofs.webservices;
/*     */ 
/*     */ public class OfsTodoDataWebServicePortTypeProxy implements OfsTodoDataWebServicePortType {
/*   4 */   private String _endpoint = null;
/*   5 */   private OfsTodoDataWebServicePortType ofsTodoDataWebServicePortType = null;
/*     */   
/*     */   public OfsTodoDataWebServicePortTypeProxy() {
/*   8 */     _initOfsTodoDataWebServicePortTypeProxy();
/*     */   }
/*     */   
/*     */   public OfsTodoDataWebServicePortTypeProxy(String endpoint) {
/*  12 */     this._endpoint = endpoint;
/*  13 */     _initOfsTodoDataWebServicePortTypeProxy();
/*     */   }
/*     */   
/*     */   private void _initOfsTodoDataWebServicePortTypeProxy() {
/*     */     try {
/*  18 */       this.ofsTodoDataWebServicePortType = (new OfsTodoDataWebServiceLocator()).getOfsTodoDataWebServiceHttpPort();
/*  19 */       if (this.ofsTodoDataWebServicePortType != null) {
/*  20 */         if (this._endpoint != null) {
/*  21 */           ((Stub)this.ofsTodoDataWebServicePortType)._setProperty("javax.xml.rpc.service.endpoint.address", this._endpoint);
/*     */         } else {
/*  23 */           this._endpoint = (String)((Stub)this.ofsTodoDataWebServicePortType)._getProperty("javax.xml.rpc.service.endpoint.address");
/*     */         }
/*     */       
/*     */       }
/*  27 */     } catch (ServiceException serviceException) {}
/*     */   }
/*     */   
/*     */   public String getEndpoint() {
/*  31 */     return this._endpoint;
/*     */   }
/*     */   
/*     */   public void setEndpoint(String endpoint) {
/*  35 */     this._endpoint = endpoint;
/*  36 */     if (this.ofsTodoDataWebServicePortType != null) {
/*  37 */       ((Stub)this.ofsTodoDataWebServicePortType)._setProperty("javax.xml.rpc.service.endpoint.address", this._endpoint);
/*     */     }
/*     */   }
/*     */   
/*     */   public OfsTodoDataWebServicePortType getOfsTodoDataWebServicePortType() {
/*  42 */     if (this.ofsTodoDataWebServicePortType == null)
/*  43 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  44 */     return this.ofsTodoDataWebServicePortType;
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] processOverRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  48 */     if (this.ofsTodoDataWebServicePortType == null)
/*  49 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  50 */     return this.ofsTodoDataWebServicePortType.processOverRequestByMap(in0);
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] receiveCCRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  54 */     if (this.ofsTodoDataWebServicePortType == null)
/*  55 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  56 */     return this.ofsTodoDataWebServicePortType.receiveCCRequestByMap(in0);
/*     */   }
/*     */   
/*     */   public String processOverRequestByJson(String in0) throws RemoteException {
/*  60 */     if (this.ofsTodoDataWebServicePortType == null)
/*  61 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  62 */     return this.ofsTodoDataWebServicePortType.processOverRequestByJson(in0);
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] receiveRequestInfoByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  66 */     if (this.ofsTodoDataWebServicePortType == null)
/*  67 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  68 */     return this.ofsTodoDataWebServicePortType.receiveRequestInfoByMap(in0);
/*     */   }
/*     */   
/*     */   public String receiveRequestInfoByXml(String in0) throws RemoteException {
/*  72 */     if (this.ofsTodoDataWebServicePortType == null)
/*  73 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  74 */     return this.ofsTodoDataWebServicePortType.receiveRequestInfoByXml(in0);
/*     */   }
/*     */   
/*     */   public String receiveTodoRequestByXml(String in0) throws RemoteException {
/*  78 */     if (this.ofsTodoDataWebServicePortType == null)
/*  79 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  80 */     return this.ofsTodoDataWebServicePortType.receiveTodoRequestByXml(in0);
/*     */   }
/*     */   
/*     */   public String deleteRequestInfoByXML(String in0) throws RemoteException {
/*  84 */     if (this.ofsTodoDataWebServicePortType == null)
/*  85 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  86 */     return this.ofsTodoDataWebServicePortType.deleteRequestInfoByXML(in0);
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] receiveTodoRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  90 */     if (this.ofsTodoDataWebServicePortType == null)
/*  91 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  92 */     return this.ofsTodoDataWebServicePortType.receiveTodoRequestByMap(in0);
/*     */   }
/*     */   
/*     */   public String deleteUserRequestInfoByJson(String in0) throws RemoteException {
/*  96 */     if (this.ofsTodoDataWebServicePortType == null)
/*  97 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/*  98 */     return this.ofsTodoDataWebServicePortType.deleteUserRequestInfoByJson(in0);
/*     */   }
/*     */   
/*     */   public String processDoneRequestByJson(String in0) throws RemoteException {
/* 102 */     if (this.ofsTodoDataWebServicePortType == null)
/* 103 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 104 */     return this.ofsTodoDataWebServicePortType.processDoneRequestByJson(in0);
/*     */   }
/*     */   
/*     */   public String processOverRequestByXml(String in0) throws RemoteException {
/* 108 */     if (this.ofsTodoDataWebServicePortType == null)
/* 109 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 110 */     return this.ofsTodoDataWebServicePortType.processOverRequestByXml(in0);
/*     */   }
/*     */   
/*     */   public String receiveCCRequestByXml(String in0) throws RemoteException {
/* 114 */     if (this.ofsTodoDataWebServicePortType == null)
/* 115 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 116 */     return this.ofsTodoDataWebServicePortType.receiveCCRequestByXml(in0);
/*     */   }
/*     */   
/*     */   public String receiveRequestInfoByJson(String in0) throws RemoteException {
/* 120 */     if (this.ofsTodoDataWebServicePortType == null)
/* 121 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 122 */     return this.ofsTodoDataWebServicePortType.receiveRequestInfoByJson(in0);
/*     */   }
/*     */   
/*     */   public String deleteRequestInfoByJson(String in0) throws RemoteException {
/* 126 */     if (this.ofsTodoDataWebServicePortType == null)
/* 127 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 128 */     return this.ofsTodoDataWebServicePortType.deleteRequestInfoByJson(in0);
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] deleteRequestInfoByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/* 132 */     if (this.ofsTodoDataWebServicePortType == null)
/* 133 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 134 */     return this.ofsTodoDataWebServicePortType.deleteRequestInfoByMap(in0);
/*     */   }
/*     */   
/*     */   public String receiveCCRequestByJson(String in0) throws RemoteException {
/* 138 */     if (this.ofsTodoDataWebServicePortType == null)
/* 139 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 140 */     return this.ofsTodoDataWebServicePortType.receiveCCRequestByJson(in0);
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] deleteUserRequestInfoByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/* 144 */     if (this.ofsTodoDataWebServicePortType == null)
/* 145 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 146 */     return this.ofsTodoDataWebServicePortType.deleteUserRequestInfoByMap(in0);
/*     */   }
/*     */   
/*     */   public String processDoneRequestByXml(String in0) throws RemoteException {
/* 150 */     if (this.ofsTodoDataWebServicePortType == null)
/* 151 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 152 */     return this.ofsTodoDataWebServicePortType.processDoneRequestByXml(in0);
/*     */   }
/*     */   
/*     */   public String receiveTodoRequestByJson(String in0) throws RemoteException {
/* 156 */     if (this.ofsTodoDataWebServicePortType == null)
/* 157 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 158 */     return this.ofsTodoDataWebServicePortType.receiveTodoRequestByJson(in0);
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] processDoneRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/* 162 */     if (this.ofsTodoDataWebServicePortType == null)
/* 163 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 164 */     return this.ofsTodoDataWebServicePortType.processDoneRequestByMap(in0);
/*     */   }
/*     */   
/*     */   public String deleteUserRequestInfoByXML(String in0) throws RemoteException {
/* 168 */     if (this.ofsTodoDataWebServicePortType == null)
/* 169 */       _initOfsTodoDataWebServicePortTypeProxy(); 
/* 170 */     return this.ofsTodoDataWebServicePortType.deleteUserRequestInfoByXML(in0);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/OfsTodoDataWebServicePortTypeProxy.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */