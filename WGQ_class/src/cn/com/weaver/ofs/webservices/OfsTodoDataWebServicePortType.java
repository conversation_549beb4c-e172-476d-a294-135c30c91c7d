package cn.com.weaver.ofs.webservices;

import java.rmi.Remote;
import java.rmi.RemoteException;

public interface OfsTodoDataWebServicePortType extends Remote {
  AnyType2AnyTypeMapEntry[] processOverRequestByMap(AnyType2AnyTypeMapEntry[] paramArrayOfAnyType2AnyTypeMapEntry) throws RemoteException;
  
  AnyType2AnyTypeMapEntry[] receiveCCRequestByMap(AnyType2AnyTypeMapEntry[] paramArrayOfAnyType2AnyTypeMapEntry) throws RemoteException;
  
  String processOverRequestByJson(String paramString) throws RemoteException;
  
  AnyType2AnyTypeMapEntry[] receiveRequestInfoByMap(AnyType2AnyTypeMapEntry[] paramArrayOfAnyType2AnyTypeMapEntry) throws RemoteException;
  
  String receiveRequestInfoByXml(String paramString) throws RemoteException;
  
  String receiveTodoRequestByXml(String paramString) throws RemoteException;
  
  String deleteRequestInfoByXML(String paramString) throws RemoteException;
  
  AnyType2AnyTypeMapEntry[] receiveTodoRequestByMap(AnyType2AnyTypeMapEntry[] paramArrayOfAnyType2AnyTypeMapEntry) throws RemoteException;
  
  String deleteUserRequestInfoByJson(String paramString) throws RemoteException;
  
  String processDoneRequestByJson(String paramString) throws RemoteException;
  
  String processOverRequestByXml(String paramString) throws RemoteException;
  
  String receiveCCRequestByXml(String paramString) throws RemoteException;
  
  String receiveRequestInfoByJson(String paramString) throws RemoteException;
  
  String deleteRequestInfoByJson(String paramString) throws RemoteException;
  
  AnyType2AnyTypeMapEntry[] deleteRequestInfoByMap(AnyType2AnyTypeMapEntry[] paramArrayOfAnyType2AnyTypeMapEntry) throws RemoteException;
  
  String receiveCCRequestByJson(String paramString) throws RemoteException;
  
  AnyType2AnyTypeMapEntry[] deleteUserRequestInfoByMap(AnyType2AnyTypeMapEntry[] paramArrayOfAnyType2AnyTypeMapEntry) throws RemoteException;
  
  String processDoneRequestByXml(String paramString) throws RemoteException;
  
  String receiveTodoRequestByJson(String paramString) throws RemoteException;
  
  AnyType2AnyTypeMapEntry[] processDoneRequestByMap(AnyType2AnyTypeMapEntry[] paramArrayOfAnyType2AnyTypeMapEntry) throws RemoteException;
  
  String deleteUserRequestInfoByXML(String paramString) throws RemoteException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/OfsTodoDataWebServicePortType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */