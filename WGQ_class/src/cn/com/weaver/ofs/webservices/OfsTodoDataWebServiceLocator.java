/*     */ package cn.com.weaver.ofs.webservices;
/*     */ 
/*     */ import java.net.MalformedURLException;
/*     */ import java.net.URL;
/*     */ import java.rmi.Remote;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import javax.xml.namespace.QName;
/*     */ import javax.xml.rpc.ServiceException;
/*     */ import org.apache.axis.AxisFault;
/*     */ import org.apache.axis.EngineConfiguration;
/*     */ import org.apache.axis.client.Service;
/*     */ import org.apache.axis.client.Stub;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ public class OfsTodoDataWebServiceLocator extends Service implements OfsTodoDataWebService {
/*     */   public OfsTodoDataWebServiceLocator() {
/*  18 */     BaseBean bean = new BaseBean();
/*  19 */     String oaip = bean.getPropValue("ofs", "oaip");
/*  20 */     this.OfsTodoDataWebServiceHttpPort_address = "http://" + oaip + "/services/OfsTodoDataWebService";
/*     */   }
/*     */   private String OfsTodoDataWebServiceHttpPort_address;
/*     */   public OfsTodoDataWebServiceLocator(EngineConfiguration config) {
/*  24 */     super(config);
/*     */   }
/*     */   
/*     */   public OfsTodoDataWebServiceLocator(String wsdlLoc, QName sName) throws ServiceException {
/*  28 */     super(wsdlLoc, sName);
/*     */   }
/*     */   
/*     */   public String getOfsTodoDataWebServiceHttpPortAddress() {
/*  32 */     return this.OfsTodoDataWebServiceHttpPort_address;
/*     */   }
/*     */ 
/*     */   
/*  36 */   private String OfsTodoDataWebServiceHttpPortWSDDServiceName = "OfsTodoDataWebServiceHttpPort";
/*     */   
/*     */   public String getOfsTodoDataWebServiceHttpPortWSDDServiceName() {
/*  39 */     return this.OfsTodoDataWebServiceHttpPortWSDDServiceName;
/*     */   }
/*     */   
/*     */   public void setOfsTodoDataWebServiceHttpPortWSDDServiceName(String name) {
/*  43 */     this.OfsTodoDataWebServiceHttpPortWSDDServiceName = name;
/*     */   }
/*     */   
/*     */   public OfsTodoDataWebServicePortType getOfsTodoDataWebServiceHttpPort() throws ServiceException {
/*     */     URL endpoint;
/*     */     try {
/*  49 */       endpoint = new URL(this.OfsTodoDataWebServiceHttpPort_address);
/*     */     }
/*  51 */     catch (MalformedURLException e) {
/*  52 */       throw new ServiceException(e);
/*     */     } 
/*  54 */     return getOfsTodoDataWebServiceHttpPort(endpoint);
/*     */   }
/*     */   
/*     */   public OfsTodoDataWebServicePortType getOfsTodoDataWebServiceHttpPort(URL portAddress) throws ServiceException {
/*     */     try {
/*  59 */       OfsTodoDataWebServiceHttpBindingStub _stub = new OfsTodoDataWebServiceHttpBindingStub(portAddress, this);
/*  60 */       _stub.setPortName(getOfsTodoDataWebServiceHttpPortWSDDServiceName());
/*  61 */       return _stub;
/*     */     }
/*  63 */     catch (AxisFault e) {
/*  64 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setOfsTodoDataWebServiceHttpPortEndpointAddress(String address) {
/*  69 */     this.OfsTodoDataWebServiceHttpPort_address = address;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Remote getPort(Class<?> serviceEndpointInterface) throws ServiceException {
/*     */     try {
/*  79 */       if (OfsTodoDataWebServicePortType.class.isAssignableFrom(serviceEndpointInterface)) {
/*  80 */         OfsTodoDataWebServiceHttpBindingStub _stub = new OfsTodoDataWebServiceHttpBindingStub(new URL(this.OfsTodoDataWebServiceHttpPort_address), this);
/*  81 */         _stub.setPortName(getOfsTodoDataWebServiceHttpPortWSDDServiceName());
/*  82 */         return _stub;
/*     */       }
/*     */     
/*  85 */     } catch (Throwable t) {
/*  86 */       throw new ServiceException(t);
/*     */     } 
/*  88 */     throw new ServiceException("There is no stub implementation for the interface:  " + ((serviceEndpointInterface == null) ? "null" : serviceEndpointInterface.getName()));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Remote getPort(QName portName, Class serviceEndpointInterface) throws ServiceException {
/*  97 */     if (portName == null) {
/*  98 */       return getPort(serviceEndpointInterface);
/*     */     }
/* 100 */     String inputPortName = portName.getLocalPart();
/* 101 */     if ("OfsTodoDataWebServiceHttpPort".equals(inputPortName)) {
/* 102 */       return getOfsTodoDataWebServiceHttpPort();
/*     */     }
/*     */     
/* 105 */     Remote _stub = getPort(serviceEndpointInterface);
/* 106 */     ((Stub)_stub).setPortName(portName);
/* 107 */     return _stub;
/*     */   }
/*     */ 
/*     */   
/*     */   public QName getServiceName() {
/* 112 */     return new QName("webservices.ofs.weaver.com.cn", "OfsTodoDataWebService");
/*     */   }
/*     */   
/* 115 */   private HashSet ports = null;
/*     */   
/*     */   public Iterator getPorts() {
/* 118 */     if (this.ports == null) {
/* 119 */       this.ports = new HashSet();
/* 120 */       this.ports.add(new QName("webservices.ofs.weaver.com.cn", "OfsTodoDataWebServiceHttpPort"));
/*     */     } 
/* 122 */     return this.ports.iterator();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEndpointAddress(String portName, String address) throws ServiceException {
/* 130 */     if ("OfsTodoDataWebServiceHttpPort".equals(portName)) {
/* 131 */       setOfsTodoDataWebServiceHttpPortEndpointAddress(address);
/*     */     }
/*     */     else {
/*     */       
/* 135 */       throw new ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEndpointAddress(QName portName, String address) throws ServiceException {
/* 143 */     setEndpointAddress(portName.getLocalPart(), address);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/OfsTodoDataWebServiceLocator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */