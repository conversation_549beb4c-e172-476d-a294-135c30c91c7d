/*     */ package cn.com.weaver.ofs.webservices;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.lang.reflect.Array;
/*     */ import java.util.Arrays;
/*     */ import javax.xml.namespace.QName;
/*     */ import org.apache.axis.description.ElementDesc;
/*     */ import org.apache.axis.description.FieldDesc;
/*     */ import org.apache.axis.description.TypeDesc;
/*     */ import org.apache.axis.encoding.Deserializer;
/*     */ import org.apache.axis.encoding.Serializer;
/*     */ import org.apache.axis.encoding.ser.BeanDeserializer;
/*     */ import org.apache.axis.encoding.ser.BeanSerializer;
/*     */ 
/*     */ public class DeleteUserRequestInfoByMap
/*     */   implements Serializable {
/*     */   public DeleteUserRequestInfoByMap(AnyType2AnyTypeMapEntry[] in0) {
/*  18 */     this.in0 = in0;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private AnyType2AnyTypeMapEntry[] in0;
/*     */ 
/*     */ 
/*     */   
/*     */   public AnyType2AnyTypeMapEntry[] getIn0() {
/*  28 */     return this.in0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIn0(AnyType2AnyTypeMapEntry[] in0) {
/*  38 */     this.in0 = in0;
/*     */   }
/*     */   
/*  41 */   private Object __equalsCalc = null;
/*     */   public synchronized boolean equals(Object obj) {
/*  43 */     if (!(obj instanceof DeleteUserRequestInfoByMap)) return false; 
/*  44 */     DeleteUserRequestInfoByMap other = (DeleteUserRequestInfoByMap)obj;
/*  45 */     if (obj == null) return false; 
/*  46 */     if (this == obj) return true; 
/*  47 */     if (this.__equalsCalc != null) {
/*  48 */       return (this.__equalsCalc == obj);
/*     */     }
/*  50 */     this.__equalsCalc = obj;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  55 */     boolean _equals = ((this.in0 == null && other.getIn0() == null) || (this.in0 != null && Arrays.equals((Object[])this.in0, (Object[])other.getIn0())));
/*  56 */     this.__equalsCalc = null;
/*  57 */     return _equals;
/*     */   }
/*     */   private boolean __hashCodeCalc = false;
/*     */   
/*     */   public synchronized int hashCode() {
/*  62 */     if (this.__hashCodeCalc) {
/*  63 */       return 0;
/*     */     }
/*  65 */     this.__hashCodeCalc = true;
/*  66 */     int _hashCode = 1;
/*  67 */     if (getIn0() != null) {
/*  68 */       int i = 0;
/*  69 */       for (; i < Array.getLength(getIn0()); 
/*  70 */         i++) {
/*  71 */         Object obj = Array.get(getIn0(), i);
/*  72 */         if (obj != null && 
/*  73 */           !obj.getClass().isArray()) {
/*  74 */           _hashCode += obj.hashCode();
/*     */         }
/*     */       } 
/*     */     } 
/*  78 */     this.__hashCodeCalc = false;
/*  79 */     return _hashCode;
/*     */   }
/*     */ 
/*     */   
/*  83 */   private static TypeDesc typeDesc = new TypeDesc(DeleteUserRequestInfoByMap.class, true);
/*     */ 
/*     */   
/*     */   static {
/*  87 */     typeDesc.setXmlType(new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByMap"));
/*  88 */     ElementDesc elemField = new ElementDesc();
/*  89 */     elemField.setFieldName("in0");
/*  90 */     elemField.setXmlName(new QName("webservices.ofs.weaver.com.cn", "in0"));
/*  91 */     elemField.setXmlType(new QName("webservices.ofs.weaver.com.cn", ">anyType2anyTypeMap>entry"));
/*  92 */     elemField.setNillable(true);
/*  93 */     elemField.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  94 */     typeDesc.addFieldDesc((FieldDesc)elemField);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static TypeDesc getTypeDesc() {
/* 101 */     return typeDesc;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Serializer getSerializer(String mechType, Class _javaType, QName _xmlType) {
/* 111 */     return (Serializer)new BeanSerializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Deserializer getDeserializer(String mechType, Class _javaType, QName _xmlType) {
/* 123 */     return (Deserializer)new BeanDeserializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */   
/*     */   public DeleteUserRequestInfoByMap() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/DeleteUserRequestInfoByMap.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */