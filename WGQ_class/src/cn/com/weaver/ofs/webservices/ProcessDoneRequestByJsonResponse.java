/*     */ package cn.com.weaver.ofs.webservices;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import javax.xml.namespace.QName;
/*     */ import org.apache.axis.description.ElementDesc;
/*     */ import org.apache.axis.description.FieldDesc;
/*     */ import org.apache.axis.description.TypeDesc;
/*     */ import org.apache.axis.encoding.Deserializer;
/*     */ import org.apache.axis.encoding.Serializer;
/*     */ import org.apache.axis.encoding.ser.BeanDeserializer;
/*     */ import org.apache.axis.encoding.ser.BeanSerializer;
/*     */ 
/*     */ public class ProcessDoneRequestByJsonResponse
/*     */   implements Serializable {
/*     */   private String out;
/*     */   
/*     */   public ProcessDoneRequestByJsonResponse(String out) {
/*  18 */     this.out = out;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOut() {
/*  28 */     return this.out;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOut(String out) {
/*  38 */     this.out = out;
/*     */   }
/*     */   
/*  41 */   private Object __equalsCalc = null;
/*     */   public synchronized boolean equals(Object obj) {
/*  43 */     if (!(obj instanceof ProcessDoneRequestByJsonResponse)) return false; 
/*  44 */     ProcessDoneRequestByJsonResponse other = (ProcessDoneRequestByJsonResponse)obj;
/*  45 */     if (obj == null) return false; 
/*  46 */     if (this == obj) return true; 
/*  47 */     if (this.__equalsCalc != null) {
/*  48 */       return (this.__equalsCalc == obj);
/*     */     }
/*  50 */     this.__equalsCalc = obj;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  55 */     boolean _equals = ((this.out == null && other.getOut() == null) || (this.out != null && this.out.equals(other.getOut())));
/*  56 */     this.__equalsCalc = null;
/*  57 */     return _equals;
/*     */   }
/*     */   private boolean __hashCodeCalc = false;
/*     */   
/*     */   public synchronized int hashCode() {
/*  62 */     if (this.__hashCodeCalc) {
/*  63 */       return 0;
/*     */     }
/*  65 */     this.__hashCodeCalc = true;
/*  66 */     int _hashCode = 1;
/*  67 */     if (getOut() != null) {
/*  68 */       _hashCode += getOut().hashCode();
/*     */     }
/*  70 */     this.__hashCodeCalc = false;
/*  71 */     return _hashCode;
/*     */   }
/*     */ 
/*     */   
/*  75 */   private static TypeDesc typeDesc = new TypeDesc(ProcessDoneRequestByJsonResponse.class, true);
/*     */ 
/*     */   
/*     */   static {
/*  79 */     typeDesc.setXmlType(new QName("webservices.ofs.weaver.com.cn", ">processDoneRequestByJsonResponse"));
/*  80 */     ElementDesc elemField = new ElementDesc();
/*  81 */     elemField.setFieldName("out");
/*  82 */     elemField.setXmlName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  83 */     elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  84 */     elemField.setNillable(true);
/*  85 */     typeDesc.addFieldDesc((FieldDesc)elemField);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static TypeDesc getTypeDesc() {
/*  92 */     return typeDesc;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Serializer getSerializer(String mechType, Class _javaType, QName _xmlType) {
/* 102 */     return (Serializer)new BeanSerializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Deserializer getDeserializer(String mechType, Class _javaType, QName _xmlType) {
/* 114 */     return (Deserializer)new BeanDeserializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */   
/*     */   public ProcessDoneRequestByJsonResponse() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/ProcessDoneRequestByJsonResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */