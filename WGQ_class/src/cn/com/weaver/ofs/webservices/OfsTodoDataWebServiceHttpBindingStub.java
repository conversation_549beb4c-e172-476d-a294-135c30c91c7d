/*      */ package cn.com.weaver.ofs.webservices;
/*      */ import javax.xml.namespace.QName;
/*      */ import org.apache.axis.AxisFault;
/*      */ import org.apache.axis.client.Call;
/*      */ import org.apache.axis.description.OperationDesc;
/*      */ import org.apache.axis.description.ParameterDesc;
/*      */ import org.apache.axis.encoding.ser.BeanDeserializerFactory;
/*      */ import org.apache.axis.encoding.ser.BeanSerializerFactory;
/*      */ 
/*      */ public class OfsTodoDataWebServiceHttpBindingStub extends Stub implements OfsTodoDataWebServicePortType {
/*   11 */   private Vector cachedSerClasses = new Vector();
/*   12 */   private Vector cachedSerQNames = new Vector();
/*   13 */   private Vector cachedSerFactories = new Vector();
/*   14 */   private Vector cachedDeserFactories = new Vector();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   19 */   static OperationDesc[] _operations = new OperationDesc[21]; static {
/*   20 */     _initOperationDesc1();
/*   21 */     _initOperationDesc2();
/*   22 */     _initOperationDesc3();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private static void _initOperationDesc1() {
/*   28 */     OperationDesc oper = new OperationDesc();
/*   29 */     oper.setName("processOverRequestByMap");
/*   30 */     ParameterDesc param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"), AnyType2AnyTypeMapEntry[].class, false, false);
/*   31 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*   32 */     param.setNillable(true);
/*   33 */     oper.addParameter(param);
/*   34 */     oper.setReturnType(new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"));
/*   35 */     oper.setReturnClass(AnyType2AnyTypeMapEntry[].class);
/*   36 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*   37 */     param = oper.getReturnParamDesc();
/*   38 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*   39 */     oper.setStyle(Style.WRAPPED);
/*   40 */     oper.setUse(Use.LITERAL);
/*   41 */     _operations[0] = oper;
/*      */     
/*   43 */     oper = new OperationDesc();
/*   44 */     oper.setName("receiveCCRequestByMap");
/*   45 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"), AnyType2AnyTypeMapEntry[].class, false, false);
/*   46 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*   47 */     param.setNillable(true);
/*   48 */     oper.addParameter(param);
/*   49 */     oper.setReturnType(new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"));
/*   50 */     oper.setReturnClass(AnyType2AnyTypeMapEntry[].class);
/*   51 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*   52 */     param = oper.getReturnParamDesc();
/*   53 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*   54 */     oper.setStyle(Style.WRAPPED);
/*   55 */     oper.setUse(Use.LITERAL);
/*   56 */     _operations[1] = oper;
/*      */     
/*   58 */     oper = new OperationDesc();
/*   59 */     oper.setName("processOverRequestByJson");
/*   60 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*   61 */     param.setNillable(true);
/*   62 */     oper.addParameter(param);
/*   63 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*   64 */     oper.setReturnClass(String.class);
/*   65 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*   66 */     oper.setStyle(Style.WRAPPED);
/*   67 */     oper.setUse(Use.LITERAL);
/*   68 */     _operations[2] = oper;
/*      */     
/*   70 */     oper = new OperationDesc();
/*   71 */     oper.setName("receiveRequestInfoByMap");
/*   72 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"), AnyType2AnyTypeMapEntry[].class, false, false);
/*   73 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*   74 */     param.setNillable(true);
/*   75 */     oper.addParameter(param);
/*   76 */     oper.setReturnType(new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"));
/*   77 */     oper.setReturnClass(AnyType2AnyTypeMapEntry[].class);
/*   78 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*   79 */     param = oper.getReturnParamDesc();
/*   80 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*   81 */     oper.setStyle(Style.WRAPPED);
/*   82 */     oper.setUse(Use.LITERAL);
/*   83 */     _operations[3] = oper;
/*      */     
/*   85 */     oper = new OperationDesc();
/*   86 */     oper.setName("receiveRequestInfoByXml");
/*   87 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*   88 */     param.setNillable(true);
/*   89 */     oper.addParameter(param);
/*   90 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*   91 */     oper.setReturnClass(String.class);
/*   92 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*   93 */     oper.setStyle(Style.WRAPPED);
/*   94 */     oper.setUse(Use.LITERAL);
/*   95 */     _operations[4] = oper;
/*      */     
/*   97 */     oper = new OperationDesc();
/*   98 */     oper.setName("receiveTodoRequestByXml");
/*   99 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  100 */     param.setNillable(true);
/*  101 */     oper.addParameter(param);
/*  102 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  103 */     oper.setReturnClass(String.class);
/*  104 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  105 */     oper.setStyle(Style.WRAPPED);
/*  106 */     oper.setUse(Use.LITERAL);
/*  107 */     _operations[5] = oper;
/*      */     
/*  109 */     oper = new OperationDesc();
/*  110 */     oper.setName("deleteRequestInfoByXML");
/*  111 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  112 */     param.setNillable(true);
/*  113 */     oper.addParameter(param);
/*  114 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  115 */     oper.setReturnClass(String.class);
/*  116 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  117 */     oper.setStyle(Style.WRAPPED);
/*  118 */     oper.setUse(Use.LITERAL);
/*  119 */     _operations[6] = oper;
/*      */     
/*  121 */     oper = new OperationDesc();
/*  122 */     oper.setName("receiveTodoRequestByMap");
/*  123 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"), AnyType2AnyTypeMapEntry[].class, false, false);
/*  124 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  125 */     param.setNillable(true);
/*  126 */     oper.addParameter(param);
/*  127 */     oper.setReturnType(new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"));
/*  128 */     oper.setReturnClass(AnyType2AnyTypeMapEntry[].class);
/*  129 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  130 */     param = oper.getReturnParamDesc();
/*  131 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  132 */     oper.setStyle(Style.WRAPPED);
/*  133 */     oper.setUse(Use.LITERAL);
/*  134 */     _operations[7] = oper;
/*      */     
/*  136 */     oper = new OperationDesc();
/*  137 */     oper.setName("deleteUserRequestInfoByJson");
/*  138 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  139 */     param.setNillable(true);
/*  140 */     oper.addParameter(param);
/*  141 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  142 */     oper.setReturnClass(String.class);
/*  143 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  144 */     oper.setStyle(Style.WRAPPED);
/*  145 */     oper.setUse(Use.LITERAL);
/*  146 */     _operations[8] = oper;
/*      */     
/*  148 */     oper = new OperationDesc();
/*  149 */     oper.setName("processDoneRequestByJson");
/*  150 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  151 */     param.setNillable(true);
/*  152 */     oper.addParameter(param);
/*  153 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  154 */     oper.setReturnClass(String.class);
/*  155 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  156 */     oper.setStyle(Style.WRAPPED);
/*  157 */     oper.setUse(Use.LITERAL);
/*  158 */     _operations[9] = oper;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static void _initOperationDesc2() {
/*  165 */     OperationDesc oper = new OperationDesc();
/*  166 */     oper.setName("processOverRequestByXml");
/*  167 */     ParameterDesc param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  168 */     param.setNillable(true);
/*  169 */     oper.addParameter(param);
/*  170 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  171 */     oper.setReturnClass(String.class);
/*  172 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  173 */     oper.setStyle(Style.WRAPPED);
/*  174 */     oper.setUse(Use.LITERAL);
/*  175 */     _operations[10] = oper;
/*      */     
/*  177 */     oper = new OperationDesc();
/*  178 */     oper.setName("receiveCCRequestByXml");
/*  179 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  180 */     param.setNillable(true);
/*  181 */     oper.addParameter(param);
/*  182 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  183 */     oper.setReturnClass(String.class);
/*  184 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  185 */     oper.setStyle(Style.WRAPPED);
/*  186 */     oper.setUse(Use.LITERAL);
/*  187 */     _operations[11] = oper;
/*      */     
/*  189 */     oper = new OperationDesc();
/*  190 */     oper.setName("receiveRequestInfoByJson");
/*  191 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  192 */     param.setNillable(true);
/*  193 */     oper.addParameter(param);
/*  194 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  195 */     oper.setReturnClass(String.class);
/*  196 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  197 */     oper.setStyle(Style.WRAPPED);
/*  198 */     oper.setUse(Use.LITERAL);
/*  199 */     _operations[12] = oper;
/*      */     
/*  201 */     oper = new OperationDesc();
/*  202 */     oper.setName("deleteRequestInfoByJson");
/*  203 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  204 */     param.setNillable(true);
/*  205 */     oper.addParameter(param);
/*  206 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  207 */     oper.setReturnClass(String.class);
/*  208 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  209 */     oper.setStyle(Style.WRAPPED);
/*  210 */     oper.setUse(Use.LITERAL);
/*  211 */     _operations[13] = oper;
/*      */     
/*  213 */     oper = new OperationDesc();
/*  214 */     oper.setName("deleteRequestInfoByMap");
/*  215 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"), AnyType2AnyTypeMapEntry[].class, false, false);
/*  216 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  217 */     param.setNillable(true);
/*  218 */     oper.addParameter(param);
/*  219 */     oper.setReturnType(new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"));
/*  220 */     oper.setReturnClass(AnyType2AnyTypeMapEntry[].class);
/*  221 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  222 */     param = oper.getReturnParamDesc();
/*  223 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  224 */     oper.setStyle(Style.WRAPPED);
/*  225 */     oper.setUse(Use.LITERAL);
/*  226 */     _operations[14] = oper;
/*      */     
/*  228 */     oper = new OperationDesc();
/*  229 */     oper.setName("receiveCCRequestByJson");
/*  230 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  231 */     param.setNillable(true);
/*  232 */     oper.addParameter(param);
/*  233 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  234 */     oper.setReturnClass(String.class);
/*  235 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  236 */     oper.setStyle(Style.WRAPPED);
/*  237 */     oper.setUse(Use.LITERAL);
/*  238 */     _operations[15] = oper;
/*      */     
/*  240 */     oper = new OperationDesc();
/*  241 */     oper.setName("deleteUserRequestInfoByMap");
/*  242 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"), AnyType2AnyTypeMapEntry[].class, false, false);
/*  243 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  244 */     param.setNillable(true);
/*  245 */     oper.addParameter(param);
/*  246 */     oper.setReturnType(new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"));
/*  247 */     oper.setReturnClass(AnyType2AnyTypeMapEntry[].class);
/*  248 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  249 */     param = oper.getReturnParamDesc();
/*  250 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  251 */     oper.setStyle(Style.WRAPPED);
/*  252 */     oper.setUse(Use.LITERAL);
/*  253 */     _operations[16] = oper;
/*      */     
/*  255 */     oper = new OperationDesc();
/*  256 */     oper.setName("processDoneRequestByXml");
/*  257 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  258 */     param.setNillable(true);
/*  259 */     oper.addParameter(param);
/*  260 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  261 */     oper.setReturnClass(String.class);
/*  262 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  263 */     oper.setStyle(Style.WRAPPED);
/*  264 */     oper.setUse(Use.LITERAL);
/*  265 */     _operations[17] = oper;
/*      */     
/*  267 */     oper = new OperationDesc();
/*  268 */     oper.setName("receiveTodoRequestByJson");
/*  269 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  270 */     param.setNillable(true);
/*  271 */     oper.addParameter(param);
/*  272 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  273 */     oper.setReturnClass(String.class);
/*  274 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  275 */     oper.setStyle(Style.WRAPPED);
/*  276 */     oper.setUse(Use.LITERAL);
/*  277 */     _operations[18] = oper;
/*      */     
/*  279 */     oper = new OperationDesc();
/*  280 */     oper.setName("processDoneRequestByMap");
/*  281 */     param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"), AnyType2AnyTypeMapEntry[].class, false, false);
/*  282 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  283 */     param.setNillable(true);
/*  284 */     oper.addParameter(param);
/*  285 */     oper.setReturnType(new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap"));
/*  286 */     oper.setReturnClass(AnyType2AnyTypeMapEntry[].class);
/*  287 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  288 */     param = oper.getReturnParamDesc();
/*  289 */     param.setItemQName(new QName("webservices.ofs.weaver.com.cn", "entry"));
/*  290 */     oper.setStyle(Style.WRAPPED);
/*  291 */     oper.setUse(Use.LITERAL);
/*  292 */     _operations[19] = oper;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static void _initOperationDesc3() {
/*  299 */     OperationDesc oper = new OperationDesc();
/*  300 */     oper.setName("deleteUserRequestInfoByXML");
/*  301 */     ParameterDesc param = new ParameterDesc(new QName("webservices.ofs.weaver.com.cn", "in0"), (byte)1, new QName("http://www.w3.org/2001/XMLSchema", "string"), String.class, false, false);
/*  302 */     param.setNillable(true);
/*  303 */     oper.addParameter(param);
/*  304 */     oper.setReturnType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
/*  305 */     oper.setReturnClass(String.class);
/*  306 */     oper.setReturnQName(new QName("webservices.ofs.weaver.com.cn", "out"));
/*  307 */     oper.setStyle(Style.WRAPPED);
/*  308 */     oper.setUse(Use.LITERAL);
/*  309 */     _operations[20] = oper;
/*      */   }
/*      */ 
/*      */   
/*      */   public OfsTodoDataWebServiceHttpBindingStub() throws AxisFault {
/*  314 */     this((Service)null);
/*      */   }
/*      */   
/*      */   public OfsTodoDataWebServiceHttpBindingStub(URL endpointURL, Service service) throws AxisFault {
/*  318 */     this(service);
/*  319 */     this.cachedEndpoint = endpointURL;
/*      */   }
/*      */   
/*      */   public OfsTodoDataWebServiceHttpBindingStub(Service service) throws AxisFault {
/*  323 */     if (service == null) {
/*  324 */       this.service = (Service)new Service();
/*      */     } else {
/*  326 */       this.service = service;
/*      */     } 
/*  328 */     ((Service)this.service).setTypeMappingVersion("1.2");
/*      */ 
/*      */ 
/*      */     
/*  332 */     Class<BeanSerializerFactory> beansf = BeanSerializerFactory.class;
/*  333 */     Class<BeanDeserializerFactory> beandf = BeanDeserializerFactory.class;
/*  334 */     Class<EnumSerializerFactory> enumsf = EnumSerializerFactory.class;
/*  335 */     Class<EnumDeserializerFactory> enumdf = EnumDeserializerFactory.class;
/*  336 */     Class<ArraySerializerFactory> arraysf = ArraySerializerFactory.class;
/*  337 */     Class<ArrayDeserializerFactory> arraydf = ArrayDeserializerFactory.class;
/*  338 */     Class<SimpleSerializerFactory> simplesf = SimpleSerializerFactory.class;
/*  339 */     Class<SimpleDeserializerFactory> simpledf = SimpleDeserializerFactory.class;
/*  340 */     Class<SimpleListSerializerFactory> simplelistsf = SimpleListSerializerFactory.class;
/*  341 */     Class<SimpleListDeserializerFactory> simplelistdf = SimpleListDeserializerFactory.class;
/*  342 */     QName qName = new QName("webservices.ofs.weaver.com.cn", ">anyType2anyTypeMap>entry");
/*  343 */     this.cachedSerQNames.add(qName);
/*  344 */     Class<AnyType2AnyTypeMapEntry> cls = AnyType2AnyTypeMapEntry.class;
/*  345 */     this.cachedSerClasses.add(cls);
/*  346 */     this.cachedSerFactories.add(beansf);
/*  347 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  349 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteRequestInfoByJson");
/*  350 */     this.cachedSerQNames.add(qName);
/*  351 */     Class<DeleteRequestInfoByJson> clazz42 = DeleteRequestInfoByJson.class;
/*  352 */     this.cachedSerClasses.add(clazz42);
/*  353 */     this.cachedSerFactories.add(beansf);
/*  354 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  356 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteRequestInfoByJsonResponse");
/*  357 */     this.cachedSerQNames.add(qName);
/*  358 */     Class<DeleteRequestInfoByJsonResponse> clazz41 = DeleteRequestInfoByJsonResponse.class;
/*  359 */     this.cachedSerClasses.add(clazz41);
/*  360 */     this.cachedSerFactories.add(beansf);
/*  361 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  363 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteRequestInfoByMap");
/*  364 */     this.cachedSerQNames.add(qName);
/*  365 */     Class<DeleteRequestInfoByMap> clazz40 = DeleteRequestInfoByMap.class;
/*  366 */     this.cachedSerClasses.add(clazz40);
/*  367 */     this.cachedSerFactories.add(beansf);
/*  368 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  370 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteRequestInfoByMapResponse");
/*  371 */     this.cachedSerQNames.add(qName);
/*  372 */     Class<DeleteRequestInfoByMapResponse> clazz39 = DeleteRequestInfoByMapResponse.class;
/*  373 */     this.cachedSerClasses.add(clazz39);
/*  374 */     this.cachedSerFactories.add(beansf);
/*  375 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  377 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteRequestInfoByXML");
/*  378 */     this.cachedSerQNames.add(qName);
/*  379 */     Class<DeleteRequestInfoByXML> clazz38 = DeleteRequestInfoByXML.class;
/*  380 */     this.cachedSerClasses.add(clazz38);
/*  381 */     this.cachedSerFactories.add(beansf);
/*  382 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  384 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteRequestInfoByXMLResponse");
/*  385 */     this.cachedSerQNames.add(qName);
/*  386 */     Class<DeleteRequestInfoByXMLResponse> clazz37 = DeleteRequestInfoByXMLResponse.class;
/*  387 */     this.cachedSerClasses.add(clazz37);
/*  388 */     this.cachedSerFactories.add(beansf);
/*  389 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  391 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByJson");
/*  392 */     this.cachedSerQNames.add(qName);
/*  393 */     Class<DeleteUserRequestInfoByJson> clazz36 = DeleteUserRequestInfoByJson.class;
/*  394 */     this.cachedSerClasses.add(clazz36);
/*  395 */     this.cachedSerFactories.add(beansf);
/*  396 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  398 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByJsonResponse");
/*  399 */     this.cachedSerQNames.add(qName);
/*  400 */     Class<DeleteUserRequestInfoByJsonResponse> clazz35 = DeleteUserRequestInfoByJsonResponse.class;
/*  401 */     this.cachedSerClasses.add(clazz35);
/*  402 */     this.cachedSerFactories.add(beansf);
/*  403 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  405 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByMap");
/*  406 */     this.cachedSerQNames.add(qName);
/*  407 */     Class<DeleteUserRequestInfoByMap> clazz34 = DeleteUserRequestInfoByMap.class;
/*  408 */     this.cachedSerClasses.add(clazz34);
/*  409 */     this.cachedSerFactories.add(beansf);
/*  410 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  412 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByMapResponse");
/*  413 */     this.cachedSerQNames.add(qName);
/*  414 */     Class<DeleteUserRequestInfoByMapResponse> clazz33 = DeleteUserRequestInfoByMapResponse.class;
/*  415 */     this.cachedSerClasses.add(clazz33);
/*  416 */     this.cachedSerFactories.add(beansf);
/*  417 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  419 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByXML");
/*  420 */     this.cachedSerQNames.add(qName);
/*  421 */     Class<DeleteUserRequestInfoByXML> clazz32 = DeleteUserRequestInfoByXML.class;
/*  422 */     this.cachedSerClasses.add(clazz32);
/*  423 */     this.cachedSerFactories.add(beansf);
/*  424 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  426 */     qName = new QName("webservices.ofs.weaver.com.cn", ">deleteUserRequestInfoByXMLResponse");
/*  427 */     this.cachedSerQNames.add(qName);
/*  428 */     Class<DeleteUserRequestInfoByXMLResponse> clazz31 = DeleteUserRequestInfoByXMLResponse.class;
/*  429 */     this.cachedSerClasses.add(clazz31);
/*  430 */     this.cachedSerFactories.add(beansf);
/*  431 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  433 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processDoneRequestByJson");
/*  434 */     this.cachedSerQNames.add(qName);
/*  435 */     Class<ProcessDoneRequestByJson> clazz30 = ProcessDoneRequestByJson.class;
/*  436 */     this.cachedSerClasses.add(clazz30);
/*  437 */     this.cachedSerFactories.add(beansf);
/*  438 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  440 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processDoneRequestByJsonResponse");
/*  441 */     this.cachedSerQNames.add(qName);
/*  442 */     Class<ProcessDoneRequestByJsonResponse> clazz29 = ProcessDoneRequestByJsonResponse.class;
/*  443 */     this.cachedSerClasses.add(clazz29);
/*  444 */     this.cachedSerFactories.add(beansf);
/*  445 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  447 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processDoneRequestByMap");
/*  448 */     this.cachedSerQNames.add(qName);
/*  449 */     Class<ProcessDoneRequestByMap> clazz28 = ProcessDoneRequestByMap.class;
/*  450 */     this.cachedSerClasses.add(clazz28);
/*  451 */     this.cachedSerFactories.add(beansf);
/*  452 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  454 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processDoneRequestByMapResponse");
/*  455 */     this.cachedSerQNames.add(qName);
/*  456 */     Class<ProcessDoneRequestByMapResponse> clazz27 = ProcessDoneRequestByMapResponse.class;
/*  457 */     this.cachedSerClasses.add(clazz27);
/*  458 */     this.cachedSerFactories.add(beansf);
/*  459 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  461 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processDoneRequestByXml");
/*  462 */     this.cachedSerQNames.add(qName);
/*  463 */     Class<ProcessDoneRequestByXml> clazz26 = ProcessDoneRequestByXml.class;
/*  464 */     this.cachedSerClasses.add(clazz26);
/*  465 */     this.cachedSerFactories.add(beansf);
/*  466 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  468 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processDoneRequestByXmlResponse");
/*  469 */     this.cachedSerQNames.add(qName);
/*  470 */     Class<ProcessDoneRequestByXmlResponse> clazz25 = ProcessDoneRequestByXmlResponse.class;
/*  471 */     this.cachedSerClasses.add(clazz25);
/*  472 */     this.cachedSerFactories.add(beansf);
/*  473 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  475 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processOverRequestByJson");
/*  476 */     this.cachedSerQNames.add(qName);
/*  477 */     Class<ProcessOverRequestByJson> clazz24 = ProcessOverRequestByJson.class;
/*  478 */     this.cachedSerClasses.add(clazz24);
/*  479 */     this.cachedSerFactories.add(beansf);
/*  480 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  482 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processOverRequestByJsonResponse");
/*  483 */     this.cachedSerQNames.add(qName);
/*  484 */     Class<ProcessOverRequestByJsonResponse> clazz23 = ProcessOverRequestByJsonResponse.class;
/*  485 */     this.cachedSerClasses.add(clazz23);
/*  486 */     this.cachedSerFactories.add(beansf);
/*  487 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  489 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processOverRequestByMap");
/*  490 */     this.cachedSerQNames.add(qName);
/*  491 */     Class<ProcessOverRequestByMap> clazz22 = ProcessOverRequestByMap.class;
/*  492 */     this.cachedSerClasses.add(clazz22);
/*  493 */     this.cachedSerFactories.add(beansf);
/*  494 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  496 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processOverRequestByMapResponse");
/*  497 */     this.cachedSerQNames.add(qName);
/*  498 */     Class<ProcessOverRequestByMapResponse> clazz21 = ProcessOverRequestByMapResponse.class;
/*  499 */     this.cachedSerClasses.add(clazz21);
/*  500 */     this.cachedSerFactories.add(beansf);
/*  501 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  503 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processOverRequestByXml");
/*  504 */     this.cachedSerQNames.add(qName);
/*  505 */     Class<ProcessOverRequestByXml> clazz20 = ProcessOverRequestByXml.class;
/*  506 */     this.cachedSerClasses.add(clazz20);
/*  507 */     this.cachedSerFactories.add(beansf);
/*  508 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  510 */     qName = new QName("webservices.ofs.weaver.com.cn", ">processOverRequestByXmlResponse");
/*  511 */     this.cachedSerQNames.add(qName);
/*  512 */     Class<ProcessOverRequestByXmlResponse> clazz19 = ProcessOverRequestByXmlResponse.class;
/*  513 */     this.cachedSerClasses.add(clazz19);
/*  514 */     this.cachedSerFactories.add(beansf);
/*  515 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  517 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveCCRequestByJson");
/*  518 */     this.cachedSerQNames.add(qName);
/*  519 */     Class<ReceiveCCRequestByJson> clazz18 = ReceiveCCRequestByJson.class;
/*  520 */     this.cachedSerClasses.add(clazz18);
/*  521 */     this.cachedSerFactories.add(beansf);
/*  522 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  524 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveCCRequestByJsonResponse");
/*  525 */     this.cachedSerQNames.add(qName);
/*  526 */     Class<ReceiveCCRequestByJsonResponse> clazz17 = ReceiveCCRequestByJsonResponse.class;
/*  527 */     this.cachedSerClasses.add(clazz17);
/*  528 */     this.cachedSerFactories.add(beansf);
/*  529 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  531 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveCCRequestByMap");
/*  532 */     this.cachedSerQNames.add(qName);
/*  533 */     Class<ReceiveCCRequestByMap> clazz16 = ReceiveCCRequestByMap.class;
/*  534 */     this.cachedSerClasses.add(clazz16);
/*  535 */     this.cachedSerFactories.add(beansf);
/*  536 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  538 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveCCRequestByMapResponse");
/*  539 */     this.cachedSerQNames.add(qName);
/*  540 */     Class<ReceiveCCRequestByMapResponse> clazz15 = ReceiveCCRequestByMapResponse.class;
/*  541 */     this.cachedSerClasses.add(clazz15);
/*  542 */     this.cachedSerFactories.add(beansf);
/*  543 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  545 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveCCRequestByXml");
/*  546 */     this.cachedSerQNames.add(qName);
/*  547 */     Class<ReceiveCCRequestByXml> clazz14 = ReceiveCCRequestByXml.class;
/*  548 */     this.cachedSerClasses.add(clazz14);
/*  549 */     this.cachedSerFactories.add(beansf);
/*  550 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  552 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveCCRequestByXmlResponse");
/*  553 */     this.cachedSerQNames.add(qName);
/*  554 */     Class<ReceiveCCRequestByXmlResponse> clazz13 = ReceiveCCRequestByXmlResponse.class;
/*  555 */     this.cachedSerClasses.add(clazz13);
/*  556 */     this.cachedSerFactories.add(beansf);
/*  557 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  559 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveRequestInfoByJson");
/*  560 */     this.cachedSerQNames.add(qName);
/*  561 */     Class<ReceiveRequestInfoByJson> clazz12 = ReceiveRequestInfoByJson.class;
/*  562 */     this.cachedSerClasses.add(clazz12);
/*  563 */     this.cachedSerFactories.add(beansf);
/*  564 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  566 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveRequestInfoByJsonResponse");
/*  567 */     this.cachedSerQNames.add(qName);
/*  568 */     Class<ReceiveRequestInfoByJsonResponse> clazz11 = ReceiveRequestInfoByJsonResponse.class;
/*  569 */     this.cachedSerClasses.add(clazz11);
/*  570 */     this.cachedSerFactories.add(beansf);
/*  571 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  573 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveRequestInfoByMap");
/*  574 */     this.cachedSerQNames.add(qName);
/*  575 */     Class<ReceiveRequestInfoByMap> clazz10 = ReceiveRequestInfoByMap.class;
/*  576 */     this.cachedSerClasses.add(clazz10);
/*  577 */     this.cachedSerFactories.add(beansf);
/*  578 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  580 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveRequestInfoByMapResponse");
/*  581 */     this.cachedSerQNames.add(qName);
/*  582 */     Class<ReceiveRequestInfoByMapResponse> clazz9 = ReceiveRequestInfoByMapResponse.class;
/*  583 */     this.cachedSerClasses.add(clazz9);
/*  584 */     this.cachedSerFactories.add(beansf);
/*  585 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  587 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveRequestInfoByXml");
/*  588 */     this.cachedSerQNames.add(qName);
/*  589 */     Class<ReceiveRequestInfoByXml> clazz8 = ReceiveRequestInfoByXml.class;
/*  590 */     this.cachedSerClasses.add(clazz8);
/*  591 */     this.cachedSerFactories.add(beansf);
/*  592 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  594 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveRequestInfoByXmlResponse");
/*  595 */     this.cachedSerQNames.add(qName);
/*  596 */     Class<ReceiveRequestInfoByXmlResponse> clazz7 = ReceiveRequestInfoByXmlResponse.class;
/*  597 */     this.cachedSerClasses.add(clazz7);
/*  598 */     this.cachedSerFactories.add(beansf);
/*  599 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  601 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveTodoRequestByJson");
/*  602 */     this.cachedSerQNames.add(qName);
/*  603 */     Class<ReceiveTodoRequestByJson> clazz6 = ReceiveTodoRequestByJson.class;
/*  604 */     this.cachedSerClasses.add(clazz6);
/*  605 */     this.cachedSerFactories.add(beansf);
/*  606 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  608 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveTodoRequestByJsonResponse");
/*  609 */     this.cachedSerQNames.add(qName);
/*  610 */     Class<ReceiveTodoRequestByJsonResponse> clazz5 = ReceiveTodoRequestByJsonResponse.class;
/*  611 */     this.cachedSerClasses.add(clazz5);
/*  612 */     this.cachedSerFactories.add(beansf);
/*  613 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  615 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveTodoRequestByMap");
/*  616 */     this.cachedSerQNames.add(qName);
/*  617 */     Class<ReceiveTodoRequestByMap> clazz4 = ReceiveTodoRequestByMap.class;
/*  618 */     this.cachedSerClasses.add(clazz4);
/*  619 */     this.cachedSerFactories.add(beansf);
/*  620 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  622 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveTodoRequestByMapResponse");
/*  623 */     this.cachedSerQNames.add(qName);
/*  624 */     Class<ReceiveTodoRequestByMapResponse> clazz3 = ReceiveTodoRequestByMapResponse.class;
/*  625 */     this.cachedSerClasses.add(clazz3);
/*  626 */     this.cachedSerFactories.add(beansf);
/*  627 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  629 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveTodoRequestByXml");
/*  630 */     this.cachedSerQNames.add(qName);
/*  631 */     Class<ReceiveTodoRequestByXml> clazz2 = ReceiveTodoRequestByXml.class;
/*  632 */     this.cachedSerClasses.add(clazz2);
/*  633 */     this.cachedSerFactories.add(beansf);
/*  634 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  636 */     qName = new QName("webservices.ofs.weaver.com.cn", ">receiveTodoRequestByXmlResponse");
/*  637 */     this.cachedSerQNames.add(qName);
/*  638 */     Class<ReceiveTodoRequestByXmlResponse> clazz1 = ReceiveTodoRequestByXmlResponse.class;
/*  639 */     this.cachedSerClasses.add(clazz1);
/*  640 */     this.cachedSerFactories.add(beansf);
/*  641 */     this.cachedDeserFactories.add(beandf);
/*      */     
/*  643 */     qName = new QName("webservices.ofs.weaver.com.cn", "anyType2anyTypeMap");
/*  644 */     this.cachedSerQNames.add(qName);
/*  645 */     Class<AnyType2AnyTypeMapEntry[]> clazz = AnyType2AnyTypeMapEntry[].class;
/*  646 */     this.cachedSerClasses.add(clazz);
/*  647 */     qName = new QName("webservices.ofs.weaver.com.cn", ">anyType2anyTypeMap>entry");
/*  648 */     QName qName2 = new QName("webservices.ofs.weaver.com.cn", "entry");
/*  649 */     this.cachedSerFactories.add(new ArraySerializerFactory(qName, qName2));
/*  650 */     this.cachedDeserFactories.add(new ArrayDeserializerFactory());
/*      */   }
/*      */ 
/*      */   
/*      */   protected Call createCall() throws RemoteException {
/*      */     try {
/*  656 */       Call _call = _createCall();
/*  657 */       if (this.maintainSessionSet) {
/*  658 */         _call.setMaintainSession(this.maintainSession);
/*      */       }
/*  660 */       if (this.cachedUsername != null) {
/*  661 */         _call.setUsername(this.cachedUsername);
/*      */       }
/*  663 */       if (this.cachedPassword != null) {
/*  664 */         _call.setPassword(this.cachedPassword);
/*      */       }
/*  666 */       if (this.cachedEndpoint != null) {
/*  667 */         _call.setTargetEndpointAddress(this.cachedEndpoint);
/*      */       }
/*  669 */       if (this.cachedTimeout != null) {
/*  670 */         _call.setTimeout(this.cachedTimeout);
/*      */       }
/*  672 */       if (this.cachedPortName != null) {
/*  673 */         _call.setPortName(this.cachedPortName);
/*      */       }
/*  675 */       Enumeration<String> keys = this.cachedProperties.keys();
/*  676 */       while (keys.hasMoreElements()) {
/*  677 */         String key = keys.nextElement();
/*  678 */         _call.setProperty(key, this.cachedProperties.get(key));
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  685 */       synchronized (this) {
/*  686 */         if (firstCall()) {
/*      */           
/*  688 */           _call.setEncodingStyle(null);
/*  689 */           for (int i = 0; i < this.cachedSerFactories.size(); i++) {
/*  690 */             Class cls = this.cachedSerClasses.get(i);
/*      */             
/*  692 */             QName qName = this.cachedSerQNames.get(i);
/*  693 */             Object x = this.cachedSerFactories.get(i);
/*  694 */             if (x instanceof Class) {
/*      */               
/*  696 */               Class sf = this.cachedSerFactories.get(i);
/*      */               
/*  698 */               Class df = this.cachedDeserFactories.get(i);
/*  699 */               _call.registerTypeMapping(cls, qName, sf, df, false);
/*      */             }
/*  701 */             else if (x instanceof javax.xml.rpc.encoding.SerializerFactory) {
/*      */               
/*  703 */               SerializerFactory sf = this.cachedSerFactories.get(i);
/*      */               
/*  705 */               DeserializerFactory df = this.cachedDeserFactories.get(i);
/*  706 */               _call.registerTypeMapping(cls, qName, sf, df, false);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*  711 */       return _call;
/*      */     }
/*  713 */     catch (Throwable _t) {
/*  714 */       throw new AxisFault("Failure trying to get the Call object", _t);
/*      */     } 
/*      */   }
/*      */   
/*      */   public AnyType2AnyTypeMapEntry[] processOverRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  719 */     if (this.cachedEndpoint == null) {
/*  720 */       throw new NoEndPointException();
/*      */     }
/*  722 */     Call _call = createCall();
/*  723 */     _call.setOperation(_operations[0]);
/*  724 */     _call.setUseSOAPAction(true);
/*  725 */     _call.setSOAPActionURI("");
/*  726 */     _call.setEncodingStyle(null);
/*  727 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  728 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  729 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  730 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "processOverRequestByMap"));
/*      */     
/*  732 */     setRequestHeaders(_call);
/*  733 */     setAttachments(_call); try {
/*  734 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  736 */       if (_resp instanceof RemoteException) {
/*  737 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  740 */       extractAttachments(_call);
/*      */       try {
/*  742 */         return (AnyType2AnyTypeMapEntry[])_resp;
/*  743 */       } catch (Exception _exception) {
/*  744 */         return (AnyType2AnyTypeMapEntry[])JavaUtils.convert(_resp, AnyType2AnyTypeMapEntry[].class);
/*      */       }
/*      */     
/*  747 */     } catch (AxisFault axisFaultException) {
/*  748 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public AnyType2AnyTypeMapEntry[] receiveCCRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  753 */     if (this.cachedEndpoint == null) {
/*  754 */       throw new NoEndPointException();
/*      */     }
/*  756 */     Call _call = createCall();
/*  757 */     _call.setOperation(_operations[1]);
/*  758 */     _call.setUseSOAPAction(true);
/*  759 */     _call.setSOAPActionURI("");
/*  760 */     _call.setEncodingStyle(null);
/*  761 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  762 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  763 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  764 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveCCRequestByMap"));
/*      */     
/*  766 */     setRequestHeaders(_call);
/*  767 */     setAttachments(_call); try {
/*  768 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  770 */       if (_resp instanceof RemoteException) {
/*  771 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  774 */       extractAttachments(_call);
/*      */       try {
/*  776 */         return (AnyType2AnyTypeMapEntry[])_resp;
/*  777 */       } catch (Exception _exception) {
/*  778 */         return (AnyType2AnyTypeMapEntry[])JavaUtils.convert(_resp, AnyType2AnyTypeMapEntry[].class);
/*      */       }
/*      */     
/*  781 */     } catch (AxisFault axisFaultException) {
/*  782 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String processOverRequestByJson(String in0) throws RemoteException {
/*  787 */     if (this.cachedEndpoint == null) {
/*  788 */       throw new NoEndPointException();
/*      */     }
/*  790 */     Call _call = createCall();
/*  791 */     _call.setOperation(_operations[2]);
/*  792 */     _call.setUseSOAPAction(true);
/*  793 */     _call.setSOAPActionURI("");
/*  794 */     _call.setEncodingStyle(null);
/*  795 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  796 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  797 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  798 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "processOverRequestByJson"));
/*      */     
/*  800 */     setRequestHeaders(_call);
/*  801 */     setAttachments(_call); try {
/*  802 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  804 */       if (_resp instanceof RemoteException) {
/*  805 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  808 */       extractAttachments(_call);
/*      */       try {
/*  810 */         return (String)_resp;
/*  811 */       } catch (Exception _exception) {
/*  812 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/*  815 */     } catch (AxisFault axisFaultException) {
/*  816 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public AnyType2AnyTypeMapEntry[] receiveRequestInfoByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  821 */     if (this.cachedEndpoint == null) {
/*  822 */       throw new NoEndPointException();
/*      */     }
/*  824 */     Call _call = createCall();
/*  825 */     _call.setOperation(_operations[3]);
/*  826 */     _call.setUseSOAPAction(true);
/*  827 */     _call.setSOAPActionURI("");
/*  828 */     _call.setEncodingStyle(null);
/*  829 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  830 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  831 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  832 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveRequestInfoByMap"));
/*      */     
/*  834 */     setRequestHeaders(_call);
/*  835 */     setAttachments(_call); try {
/*  836 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  838 */       if (_resp instanceof RemoteException) {
/*  839 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  842 */       extractAttachments(_call);
/*      */       try {
/*  844 */         return (AnyType2AnyTypeMapEntry[])_resp;
/*  845 */       } catch (Exception _exception) {
/*  846 */         return (AnyType2AnyTypeMapEntry[])JavaUtils.convert(_resp, AnyType2AnyTypeMapEntry[].class);
/*      */       }
/*      */     
/*  849 */     } catch (AxisFault axisFaultException) {
/*  850 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String receiveRequestInfoByXml(String in0) throws RemoteException {
/*  855 */     if (this.cachedEndpoint == null) {
/*  856 */       throw new NoEndPointException();
/*      */     }
/*  858 */     Call _call = createCall();
/*  859 */     _call.setOperation(_operations[4]);
/*  860 */     _call.setUseSOAPAction(true);
/*  861 */     _call.setSOAPActionURI("");
/*  862 */     _call.setEncodingStyle(null);
/*  863 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  864 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  865 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  866 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveRequestInfoByXml"));
/*      */     
/*  868 */     setRequestHeaders(_call);
/*  869 */     setAttachments(_call); try {
/*  870 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  872 */       if (_resp instanceof RemoteException) {
/*  873 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  876 */       extractAttachments(_call);
/*      */       try {
/*  878 */         return (String)_resp;
/*  879 */       } catch (Exception _exception) {
/*  880 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/*  883 */     } catch (AxisFault axisFaultException) {
/*  884 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String receiveTodoRequestByXml(String in0) throws RemoteException {
/*  889 */     if (this.cachedEndpoint == null) {
/*  890 */       throw new NoEndPointException();
/*      */     }
/*  892 */     Call _call = createCall();
/*  893 */     _call.setOperation(_operations[5]);
/*  894 */     _call.setUseSOAPAction(true);
/*  895 */     _call.setSOAPActionURI("");
/*  896 */     _call.setEncodingStyle(null);
/*  897 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  898 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  899 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  900 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveTodoRequestByXml"));
/*      */     
/*  902 */     setRequestHeaders(_call);
/*  903 */     setAttachments(_call); try {
/*  904 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  906 */       if (_resp instanceof RemoteException) {
/*  907 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  910 */       extractAttachments(_call);
/*      */       try {
/*  912 */         return (String)_resp;
/*  913 */       } catch (Exception _exception) {
/*  914 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/*  917 */     } catch (AxisFault axisFaultException) {
/*  918 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String deleteRequestInfoByXML(String in0) throws RemoteException {
/*  923 */     if (this.cachedEndpoint == null) {
/*  924 */       throw new NoEndPointException();
/*      */     }
/*  926 */     Call _call = createCall();
/*  927 */     _call.setOperation(_operations[6]);
/*  928 */     _call.setUseSOAPAction(true);
/*  929 */     _call.setSOAPActionURI("");
/*  930 */     _call.setEncodingStyle(null);
/*  931 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  932 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  933 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  934 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "deleteRequestInfoByXML"));
/*      */     
/*  936 */     setRequestHeaders(_call);
/*  937 */     setAttachments(_call); try {
/*  938 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  940 */       if (_resp instanceof RemoteException) {
/*  941 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  944 */       extractAttachments(_call);
/*      */       try {
/*  946 */         return (String)_resp;
/*  947 */       } catch (Exception _exception) {
/*  948 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/*  951 */     } catch (AxisFault axisFaultException) {
/*  952 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public AnyType2AnyTypeMapEntry[] receiveTodoRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/*  957 */     if (this.cachedEndpoint == null) {
/*  958 */       throw new NoEndPointException();
/*      */     }
/*  960 */     Call _call = createCall();
/*  961 */     _call.setOperation(_operations[7]);
/*  962 */     _call.setUseSOAPAction(true);
/*  963 */     _call.setSOAPActionURI("");
/*  964 */     _call.setEncodingStyle(null);
/*  965 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/*  966 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/*  967 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/*  968 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveTodoRequestByMap"));
/*      */     
/*  970 */     setRequestHeaders(_call);
/*  971 */     setAttachments(_call); try {
/*  972 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/*  974 */       if (_resp instanceof RemoteException) {
/*  975 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/*  978 */       extractAttachments(_call);
/*      */       try {
/*  980 */         return (AnyType2AnyTypeMapEntry[])_resp;
/*  981 */       } catch (Exception _exception) {
/*  982 */         return (AnyType2AnyTypeMapEntry[])JavaUtils.convert(_resp, AnyType2AnyTypeMapEntry[].class);
/*      */       }
/*      */     
/*  985 */     } catch (AxisFault axisFaultException) {
/*  986 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String deleteUserRequestInfoByJson(String in0) throws RemoteException {
/*  991 */     if (this.cachedEndpoint == null) {
/*  992 */       throw new NoEndPointException();
/*      */     }
/*  994 */     Call _call = createCall();
/*  995 */     _call.setOperation(_operations[8]);
/*  996 */     _call.setUseSOAPAction(true);
/*  997 */     _call.setSOAPActionURI("");
/*  998 */     _call.setEncodingStyle(null);
/*  999 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1000 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1001 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1002 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "deleteUserRequestInfoByJson"));
/*      */     
/* 1004 */     setRequestHeaders(_call);
/* 1005 */     setAttachments(_call); try {
/* 1006 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1008 */       if (_resp instanceof RemoteException) {
/* 1009 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1012 */       extractAttachments(_call);
/*      */       try {
/* 1014 */         return (String)_resp;
/* 1015 */       } catch (Exception _exception) {
/* 1016 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1019 */     } catch (AxisFault axisFaultException) {
/* 1020 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String processDoneRequestByJson(String in0) throws RemoteException {
/* 1025 */     if (this.cachedEndpoint == null) {
/* 1026 */       throw new NoEndPointException();
/*      */     }
/* 1028 */     Call _call = createCall();
/* 1029 */     _call.setOperation(_operations[9]);
/* 1030 */     _call.setUseSOAPAction(true);
/* 1031 */     _call.setSOAPActionURI("");
/* 1032 */     _call.setEncodingStyle(null);
/* 1033 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1034 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1035 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1036 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "processDoneRequestByJson"));
/*      */     
/* 1038 */     setRequestHeaders(_call);
/* 1039 */     setAttachments(_call); try {
/* 1040 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1042 */       if (_resp instanceof RemoteException) {
/* 1043 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1046 */       extractAttachments(_call);
/*      */       try {
/* 1048 */         return (String)_resp;
/* 1049 */       } catch (Exception _exception) {
/* 1050 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1053 */     } catch (AxisFault axisFaultException) {
/* 1054 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String processOverRequestByXml(String in0) throws RemoteException {
/* 1059 */     if (this.cachedEndpoint == null) {
/* 1060 */       throw new NoEndPointException();
/*      */     }
/* 1062 */     Call _call = createCall();
/* 1063 */     _call.setOperation(_operations[10]);
/* 1064 */     _call.setUseSOAPAction(true);
/* 1065 */     _call.setSOAPActionURI("");
/* 1066 */     _call.setEncodingStyle(null);
/* 1067 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1068 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1069 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1070 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "processOverRequestByXml"));
/*      */     
/* 1072 */     setRequestHeaders(_call);
/* 1073 */     setAttachments(_call); try {
/* 1074 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1076 */       if (_resp instanceof RemoteException) {
/* 1077 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1080 */       extractAttachments(_call);
/*      */       try {
/* 1082 */         return (String)_resp;
/* 1083 */       } catch (Exception _exception) {
/* 1084 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1087 */     } catch (AxisFault axisFaultException) {
/* 1088 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String receiveCCRequestByXml(String in0) throws RemoteException {
/* 1093 */     if (this.cachedEndpoint == null) {
/* 1094 */       throw new NoEndPointException();
/*      */     }
/* 1096 */     Call _call = createCall();
/* 1097 */     _call.setOperation(_operations[11]);
/* 1098 */     _call.setUseSOAPAction(true);
/* 1099 */     _call.setSOAPActionURI("");
/* 1100 */     _call.setEncodingStyle(null);
/* 1101 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1102 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1103 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1104 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveCCRequestByXml"));
/*      */     
/* 1106 */     setRequestHeaders(_call);
/* 1107 */     setAttachments(_call); try {
/* 1108 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1110 */       if (_resp instanceof RemoteException) {
/* 1111 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1114 */       extractAttachments(_call);
/*      */       try {
/* 1116 */         return (String)_resp;
/* 1117 */       } catch (Exception _exception) {
/* 1118 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1121 */     } catch (AxisFault axisFaultException) {
/* 1122 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String receiveRequestInfoByJson(String in0) throws RemoteException {
/* 1127 */     if (this.cachedEndpoint == null) {
/* 1128 */       throw new NoEndPointException();
/*      */     }
/* 1130 */     Call _call = createCall();
/* 1131 */     _call.setOperation(_operations[12]);
/* 1132 */     _call.setUseSOAPAction(true);
/* 1133 */     _call.setSOAPActionURI("");
/* 1134 */     _call.setEncodingStyle(null);
/* 1135 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1136 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1137 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1138 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveRequestInfoByJson"));
/*      */     
/* 1140 */     setRequestHeaders(_call);
/* 1141 */     setAttachments(_call); try {
/* 1142 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1144 */       if (_resp instanceof RemoteException) {
/* 1145 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1148 */       extractAttachments(_call);
/*      */       try {
/* 1150 */         return (String)_resp;
/* 1151 */       } catch (Exception _exception) {
/* 1152 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1155 */     } catch (AxisFault axisFaultException) {
/* 1156 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String deleteRequestInfoByJson(String in0) throws RemoteException {
/* 1161 */     if (this.cachedEndpoint == null) {
/* 1162 */       throw new NoEndPointException();
/*      */     }
/* 1164 */     Call _call = createCall();
/* 1165 */     _call.setOperation(_operations[13]);
/* 1166 */     _call.setUseSOAPAction(true);
/* 1167 */     _call.setSOAPActionURI("");
/* 1168 */     _call.setEncodingStyle(null);
/* 1169 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1170 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1171 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1172 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "deleteRequestInfoByJson"));
/*      */     
/* 1174 */     setRequestHeaders(_call);
/* 1175 */     setAttachments(_call); try {
/* 1176 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1178 */       if (_resp instanceof RemoteException) {
/* 1179 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1182 */       extractAttachments(_call);
/*      */       try {
/* 1184 */         return (String)_resp;
/* 1185 */       } catch (Exception _exception) {
/* 1186 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1189 */     } catch (AxisFault axisFaultException) {
/* 1190 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public AnyType2AnyTypeMapEntry[] deleteRequestInfoByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/* 1195 */     if (this.cachedEndpoint == null) {
/* 1196 */       throw new NoEndPointException();
/*      */     }
/* 1198 */     Call _call = createCall();
/* 1199 */     _call.setOperation(_operations[14]);
/* 1200 */     _call.setUseSOAPAction(true);
/* 1201 */     _call.setSOAPActionURI("");
/* 1202 */     _call.setEncodingStyle(null);
/* 1203 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1204 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1205 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1206 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "deleteRequestInfoByMap"));
/*      */     
/* 1208 */     setRequestHeaders(_call);
/* 1209 */     setAttachments(_call); try {
/* 1210 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1212 */       if (_resp instanceof RemoteException) {
/* 1213 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1216 */       extractAttachments(_call);
/*      */       try {
/* 1218 */         return (AnyType2AnyTypeMapEntry[])_resp;
/* 1219 */       } catch (Exception _exception) {
/* 1220 */         return (AnyType2AnyTypeMapEntry[])JavaUtils.convert(_resp, AnyType2AnyTypeMapEntry[].class);
/*      */       }
/*      */     
/* 1223 */     } catch (AxisFault axisFaultException) {
/* 1224 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String receiveCCRequestByJson(String in0) throws RemoteException {
/* 1229 */     if (this.cachedEndpoint == null) {
/* 1230 */       throw new NoEndPointException();
/*      */     }
/* 1232 */     Call _call = createCall();
/* 1233 */     _call.setOperation(_operations[15]);
/* 1234 */     _call.setUseSOAPAction(true);
/* 1235 */     _call.setSOAPActionURI("");
/* 1236 */     _call.setEncodingStyle(null);
/* 1237 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1238 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1239 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1240 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveCCRequestByJson"));
/*      */     
/* 1242 */     setRequestHeaders(_call);
/* 1243 */     setAttachments(_call); try {
/* 1244 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1246 */       if (_resp instanceof RemoteException) {
/* 1247 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1250 */       extractAttachments(_call);
/*      */       try {
/* 1252 */         return (String)_resp;
/* 1253 */       } catch (Exception _exception) {
/* 1254 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1257 */     } catch (AxisFault axisFaultException) {
/* 1258 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public AnyType2AnyTypeMapEntry[] deleteUserRequestInfoByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/* 1263 */     if (this.cachedEndpoint == null) {
/* 1264 */       throw new NoEndPointException();
/*      */     }
/* 1266 */     Call _call = createCall();
/* 1267 */     _call.setOperation(_operations[16]);
/* 1268 */     _call.setUseSOAPAction(true);
/* 1269 */     _call.setSOAPActionURI("");
/* 1270 */     _call.setEncodingStyle(null);
/* 1271 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1272 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1273 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1274 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "deleteUserRequestInfoByMap"));
/*      */     
/* 1276 */     setRequestHeaders(_call);
/* 1277 */     setAttachments(_call); try {
/* 1278 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1280 */       if (_resp instanceof RemoteException) {
/* 1281 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1284 */       extractAttachments(_call);
/*      */       try {
/* 1286 */         return (AnyType2AnyTypeMapEntry[])_resp;
/* 1287 */       } catch (Exception _exception) {
/* 1288 */         return (AnyType2AnyTypeMapEntry[])JavaUtils.convert(_resp, AnyType2AnyTypeMapEntry[].class);
/*      */       }
/*      */     
/* 1291 */     } catch (AxisFault axisFaultException) {
/* 1292 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String processDoneRequestByXml(String in0) throws RemoteException {
/* 1297 */     if (this.cachedEndpoint == null) {
/* 1298 */       throw new NoEndPointException();
/*      */     }
/* 1300 */     Call _call = createCall();
/* 1301 */     _call.setOperation(_operations[17]);
/* 1302 */     _call.setUseSOAPAction(true);
/* 1303 */     _call.setSOAPActionURI("");
/* 1304 */     _call.setEncodingStyle(null);
/* 1305 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1306 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1307 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1308 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "processDoneRequestByXml"));
/*      */     
/* 1310 */     setRequestHeaders(_call);
/* 1311 */     setAttachments(_call); try {
/* 1312 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1314 */       if (_resp instanceof RemoteException) {
/* 1315 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1318 */       extractAttachments(_call);
/*      */       try {
/* 1320 */         return (String)_resp;
/* 1321 */       } catch (Exception _exception) {
/* 1322 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1325 */     } catch (AxisFault axisFaultException) {
/* 1326 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String receiveTodoRequestByJson(String in0) throws RemoteException {
/* 1331 */     if (this.cachedEndpoint == null) {
/* 1332 */       throw new NoEndPointException();
/*      */     }
/* 1334 */     Call _call = createCall();
/* 1335 */     _call.setOperation(_operations[18]);
/* 1336 */     _call.setUseSOAPAction(true);
/* 1337 */     _call.setSOAPActionURI("");
/* 1338 */     _call.setEncodingStyle(null);
/* 1339 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1340 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1341 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1342 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "receiveTodoRequestByJson"));
/*      */     
/* 1344 */     setRequestHeaders(_call);
/* 1345 */     setAttachments(_call); try {
/* 1346 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1348 */       if (_resp instanceof RemoteException) {
/* 1349 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1352 */       extractAttachments(_call);
/*      */       try {
/* 1354 */         return (String)_resp;
/* 1355 */       } catch (Exception _exception) {
/* 1356 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1359 */     } catch (AxisFault axisFaultException) {
/* 1360 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public AnyType2AnyTypeMapEntry[] processDoneRequestByMap(AnyType2AnyTypeMapEntry[] in0) throws RemoteException {
/* 1365 */     if (this.cachedEndpoint == null) {
/* 1366 */       throw new NoEndPointException();
/*      */     }
/* 1368 */     Call _call = createCall();
/* 1369 */     _call.setOperation(_operations[19]);
/* 1370 */     _call.setUseSOAPAction(true);
/* 1371 */     _call.setSOAPActionURI("");
/* 1372 */     _call.setEncodingStyle(null);
/* 1373 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1374 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1375 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1376 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "processDoneRequestByMap"));
/*      */     
/* 1378 */     setRequestHeaders(_call);
/* 1379 */     setAttachments(_call); try {
/* 1380 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1382 */       if (_resp instanceof RemoteException) {
/* 1383 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1386 */       extractAttachments(_call);
/*      */       try {
/* 1388 */         return (AnyType2AnyTypeMapEntry[])_resp;
/* 1389 */       } catch (Exception _exception) {
/* 1390 */         return (AnyType2AnyTypeMapEntry[])JavaUtils.convert(_resp, AnyType2AnyTypeMapEntry[].class);
/*      */       }
/*      */     
/* 1393 */     } catch (AxisFault axisFaultException) {
/* 1394 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String deleteUserRequestInfoByXML(String in0) throws RemoteException {
/* 1399 */     if (this.cachedEndpoint == null) {
/* 1400 */       throw new NoEndPointException();
/*      */     }
/* 1402 */     Call _call = createCall();
/* 1403 */     _call.setOperation(_operations[20]);
/* 1404 */     _call.setUseSOAPAction(true);
/* 1405 */     _call.setSOAPActionURI("");
/* 1406 */     _call.setEncodingStyle(null);
/* 1407 */     _call.setProperty("sendXsiTypes", Boolean.FALSE);
/* 1408 */     _call.setProperty("sendMultiRefs", Boolean.FALSE);
/* 1409 */     _call.setSOAPVersion((SOAPConstants)SOAPConstants.SOAP11_CONSTANTS);
/* 1410 */     _call.setOperationName(new QName("webservices.ofs.weaver.com.cn", "deleteUserRequestInfoByXML"));
/*      */     
/* 1412 */     setRequestHeaders(_call);
/* 1413 */     setAttachments(_call); try {
/* 1414 */       Object _resp = _call.invoke(new Object[] { in0 });
/*      */       
/* 1416 */       if (_resp instanceof RemoteException) {
/* 1417 */         throw (RemoteException)_resp;
/*      */       }
/*      */       
/* 1420 */       extractAttachments(_call);
/*      */       try {
/* 1422 */         return (String)_resp;
/* 1423 */       } catch (Exception _exception) {
/* 1424 */         return (String)JavaUtils.convert(_resp, String.class);
/*      */       }
/*      */     
/* 1427 */     } catch (AxisFault axisFaultException) {
/* 1428 */       throw axisFaultException;
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/OfsTodoDataWebServiceHttpBindingStub.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */