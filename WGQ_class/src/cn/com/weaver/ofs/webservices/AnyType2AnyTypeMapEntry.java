/*     */ package cn.com.weaver.ofs.webservices;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import javax.xml.namespace.QName;
/*     */ import org.apache.axis.description.ElementDesc;
/*     */ import org.apache.axis.description.FieldDesc;
/*     */ import org.apache.axis.description.TypeDesc;
/*     */ import org.apache.axis.encoding.Deserializer;
/*     */ import org.apache.axis.encoding.Serializer;
/*     */ import org.apache.axis.encoding.ser.BeanDeserializer;
/*     */ import org.apache.axis.encoding.ser.BeanSerializer;
/*     */ 
/*     */ 
/*     */ public class AnyType2AnyTypeMapEntry
/*     */   implements Serializable
/*     */ {
/*     */   private Object key;
/*     */   private Object value;
/*     */   
/*     */   public AnyType2AnyTypeMapEntry(Object key, Object value) {
/*  21 */     this.key = key;
/*  22 */     this.value = value;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object getKey() {
/*  32 */     return this.key;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setKey(Object key) {
/*  42 */     this.key = key;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object getValue() {
/*  52 */     return this.value;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setValue(Object value) {
/*  62 */     this.value = value;
/*     */   }
/*     */   
/*  65 */   private Object __equalsCalc = null;
/*     */   public synchronized boolean equals(Object obj) {
/*  67 */     if (!(obj instanceof AnyType2AnyTypeMapEntry)) return false; 
/*  68 */     AnyType2AnyTypeMapEntry other = (AnyType2AnyTypeMapEntry)obj;
/*  69 */     if (obj == null) return false; 
/*  70 */     if (this == obj) return true; 
/*  71 */     if (this.__equalsCalc != null) {
/*  72 */       return (this.__equalsCalc == obj);
/*     */     }
/*  74 */     this.__equalsCalc = obj;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  82 */     boolean _equals = (((this.key == null && other.getKey() == null) || (this.key != null && this.key.equals(other.getKey()))) && ((this.value == null && other.getValue() == null) || (this.value != null && this.value.equals(other.getValue()))));
/*  83 */     this.__equalsCalc = null;
/*  84 */     return _equals;
/*     */   }
/*     */   private boolean __hashCodeCalc = false;
/*     */   
/*     */   public synchronized int hashCode() {
/*  89 */     if (this.__hashCodeCalc) {
/*  90 */       return 0;
/*     */     }
/*  92 */     this.__hashCodeCalc = true;
/*  93 */     int _hashCode = 1;
/*  94 */     if (getKey() != null) {
/*  95 */       _hashCode += getKey().hashCode();
/*     */     }
/*  97 */     if (getValue() != null) {
/*  98 */       _hashCode += getValue().hashCode();
/*     */     }
/* 100 */     this.__hashCodeCalc = false;
/* 101 */     return _hashCode;
/*     */   }
/*     */ 
/*     */   
/* 105 */   private static TypeDesc typeDesc = new TypeDesc(AnyType2AnyTypeMapEntry.class, true);
/*     */ 
/*     */   
/*     */   static {
/* 109 */     typeDesc.setXmlType(new QName("webservices.ofs.weaver.com.cn", ">anyType2anyTypeMap>entry"));
/* 110 */     ElementDesc elemField = new ElementDesc();
/* 111 */     elemField.setFieldName("key");
/* 112 */     elemField.setXmlName(new QName("webservices.ofs.weaver.com.cn", "key"));
/* 113 */     elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "anyType"));
/* 114 */     elemField.setMinOccurs(0);
/* 115 */     elemField.setNillable(false);
/* 116 */     typeDesc.addFieldDesc((FieldDesc)elemField);
/* 117 */     elemField = new ElementDesc();
/* 118 */     elemField.setFieldName("value");
/* 119 */     elemField.setXmlName(new QName("webservices.ofs.weaver.com.cn", "value"));
/* 120 */     elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "anyType"));
/* 121 */     elemField.setMinOccurs(0);
/* 122 */     elemField.setNillable(false);
/* 123 */     typeDesc.addFieldDesc((FieldDesc)elemField);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static TypeDesc getTypeDesc() {
/* 130 */     return typeDesc;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Serializer getSerializer(String mechType, Class _javaType, QName _xmlType) {
/* 140 */     return (Serializer)new BeanSerializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Deserializer getDeserializer(String mechType, Class _javaType, QName _xmlType) {
/* 152 */     return (Deserializer)new BeanDeserializer(_javaType, _xmlType, typeDesc);
/*     */   }
/*     */   
/*     */   public AnyType2AnyTypeMapEntry() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/AnyType2AnyTypeMapEntry.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */