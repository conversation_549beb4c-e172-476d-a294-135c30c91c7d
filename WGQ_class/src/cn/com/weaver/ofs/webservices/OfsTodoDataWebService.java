package cn.com.weaver.ofs.webservices;

import java.net.URL;
import javax.xml.rpc.Service;
import javax.xml.rpc.ServiceException;

public interface OfsTodoDataWebService extends Service {
  String getOfsTodoDataWebServiceHttpPortAddress();
  
  OfsTodoDataWebServicePortType getOfsTodoDataWebServiceHttpPort() throws ServiceException;
  
  OfsTodoDataWebServicePortType getOfsTodoDataWebServiceHttpPort(URL paramURL) throws ServiceException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/cn/com/weaver/ofs/webservices/OfsTodoDataWebService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */