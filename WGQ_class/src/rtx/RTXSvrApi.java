/*     */ package rtx;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RTXSvrApi
/*     */ {
/*  20 */   static int PRO_ADDUSER = 1;
/*  21 */   static int PRO_DELUSER = 2;
/*  22 */   static int PRO_SETUSERSMPLINFO = 3;
/*  23 */   static int PRO_GETUSER = 4;
/*  24 */   static int PRO_SETUSER = 5;
/*  25 */   static int PRO_GETUSERSMPLINFO = 6;
/*  26 */   static int PRO_SETUSERPRIVILEGE = 7;
/*  27 */   static int PRO_IFEXIST = 8;
/*  28 */   static int PRO_TRANUSER = 9;
/*     */ 
/*     */   
/*  31 */   static int PRO_ADDDEPT = 257;
/*  32 */   static int PRO_DELDEPT = 258;
/*  33 */   static int PRO_SETDEPT = 259;
/*  34 */   static int PRO_GETCHILDDEPT = 260;
/*  35 */   static int PRO_GETDEPTALLUSER = 261;
/*  36 */   static int PRO_SETDEPTPRIVILEGE = 262;
/*  37 */   static int PRO_GETDEPTSMPLINFO = 263;
/*     */   
/*  39 */   static int PRO_SMS_LOGON = 4096;
/*  40 */   static int PRO_SMS_SEND = 4097;
/*  41 */   static int PRO_SMS_NICKLIST = 4098;
/*  42 */   static int PRO_SMS_FUNCLIST = 4099;
/*  43 */   static int PRO_SMS_CHECK = 4100;
/*     */   
/*  45 */   static int PRO_SYS_USERLOGIN = 8192;
/*  46 */   static int PRO_SYS_GETUSERSTATUS = 8193;
/*  47 */   static int PRO_SYS_SENDIM = 8194;
/*  48 */   static int PRO_SYS_USERLOGINVERIFY = 8195;
/*     */   
/*  50 */   static int PRO_EXT_NOTIFY = 8448;
/*     */ 
/*     */   
/*  53 */   static String OBJNAME_RTXEXT = "EXTTOOLS";
/*  54 */   static String OBJNAME_RTXSYS = "SYSTOOLS";
/*  55 */   static String OBJNAME_DEPTMANAGER = "DEPTMANAGER";
/*  56 */   static String OBJNAME_USERMANAGER = "USERMANAGER";
/*  57 */   static String OBJNAME_SMSMANAGER = "SMSOBJECT";
/*     */ 
/*     */   
/*  60 */   static String KEY_TYPE = "TYPE";
/*     */   
/*  62 */   static String KEY_USERID = "USERID";
/*  63 */   static String KEY_USERNAME = "USERNAME";
/*  64 */   static String KEY_UIN = "UIN";
/*  65 */   static String KEY_NICK = "NICK";
/*  66 */   static String KEY_MOBILE = "MOBILE";
/*  67 */   static String KEY_OUTERUIN = "OUTERUIN";
/*  68 */   static String KEY_LASTMODIFYTIME = "LASTMODIFYTIME";
/*  69 */   static String KEY_FACE = "FACE";
/*  70 */   static String KEY_PASSWORD = "PWD";
/*     */   
/*  72 */   static String KEY_AGE = "AGE";
/*  73 */   static String KEY_GENDER = "GENDER";
/*  74 */   static String KEY_BIRTHDAY = "BIRTHDAY";
/*  75 */   static String KEY_BLOODTYPE = "BLOODTYPE";
/*  76 */   static String KEY_CONSTELLATION = "CONSTELLATION";
/*  77 */   static String KEY_COLLAGE = "COLLAGE";
/*  78 */   static String KEY_HOMEPAGE = "HOMEPAGE";
/*  79 */   static String KEY_EMAIL = "EMAIL";
/*  80 */   static String KEY_PHONE = "PHONE";
/*  81 */   static String KEY_FAX = "FAX";
/*  82 */   static String KEY_ADDRESS = "ADDRESS";
/*  83 */   static String KEY_POSTCODE = "POSTCODE";
/*  84 */   static String KEY_COUNTRY = "COUNTRY";
/*  85 */   static String KEY_PROVINCE = "PROVINCE";
/*  86 */   static String KEY_CITY = "CITY";
/*  87 */   static String KEY_MEMO = "MEMO";
/*  88 */   static String KEY_MOBILETYPE = "MOBILETYPE";
/*  89 */   static String KEY_AUTHTYPE = "AUTHTYPE";
/*  90 */   static String KEY_POSITION = "POSITION";
/*  91 */   static String KEY_OPENGSMINFO = "OPENGSMINFO";
/*  92 */   static String KEY_OPENCONTACTINFO = "OPENCONTACTINFO";
/*  93 */   static String KEY_PUBOUTUIN = "PUBOUTUIN";
/*  94 */   static String KEY_PUBOUTNICK = "PUBOUTNICK";
/*  95 */   static String KEY_PUBOUTNAME = "PUBOUTNAME";
/*  96 */   static String KEY_PUBOUTDEPT = "PUBOUTDEPT";
/*  97 */   static String KEY_PUBOUTPOSITION = "PUBOUTPOSITION";
/*  98 */   static String KEY_PUBOUTINFO = "PUBOUTINFO";
/*  99 */   static String KEY_OUTERPUBLISH = "OUTERPUBLISH";
/*     */   
/* 101 */   static String KEY_LDAPID = "LDAPID";
/* 102 */   static String KEY_DEPTID = "DEPTID";
/* 103 */   static String KEY_PDEPTID = "PDEPTID";
/* 104 */   static String KEY_SORTID = "SORTID";
/* 105 */   static String KEY_NAME = "NAME";
/* 106 */   static String KEY_INFO = "INFO";
/* 107 */   static String KEY_COMPLETEDELBS = "COMPLETEDELBS";
/*     */ 
/*     */   
/* 110 */   static String KEY_DENY = "DENY";
/* 111 */   static String KEY_ALLOW = "ALLOW";
/*     */   
/* 113 */   static String KEY_SESSIONKEY = "SESSIONKEY";
/*     */ 
/*     */   
/* 116 */   static String KEY_SENDER = "SENDER";
/* 117 */   static String KEY_FUNNO = "FUNCNO";
/* 118 */   static String KEY_RECEIVER = "RECEIVER";
/* 119 */   static String KEY_RECEIVERUIN = "RECEIVERUIN";
/* 120 */   static String KEY_SMS = "SMS";
/* 121 */   static String KEY_CUT = "CUT";
/* 122 */   static String KEY_DELFLAG = "DELFLAG";
/*     */ 
/*     */ 
/*     */   
/* 126 */   static String KEY_RECVUSERS = "RECVUSERS";
/* 127 */   static String KEY_IMMSG = "IMMSG";
/*     */   
/* 129 */   static String KEY_MSGID = "MSGID";
/* 130 */   static String KEY_MSGINFO = "MSGINFO";
/* 131 */   static String KEY_ASSISTANTTYPE = "ASSTYPE";
/* 132 */   static String KEY_TITLE = "TITLE";
/*     */ 
/*     */ 
/*     */   
/* 136 */   static String KEY_RESULT_INCODE = "INNERCODE";
/* 137 */   static String KEY_RESULT_CODE = "CODE";
/* 138 */   static String KEY_RESULT_TYPE = "TYPE";
/* 139 */   static String KEY_RESULT_NAME = "NAME";
/* 140 */   static String KEY_RESULT_VALUE = "VALUE";
/* 141 */   static String KEY_RESULT_VARIANT = "VARIANT";
/*     */ 
/*     */   
/*     */   static {
/* 145 */     System.loadLibrary("SDKAPIJava");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String GetPropertyItemName(int paramInt) {
/* 201 */     return GetProperty(paramInt, KEY_RESULT_NAME);
/*     */   }
/*     */   
/*     */   public String GetPropertyItemValue(int paramInt) {
/* 205 */     return GetProperty(paramInt, KEY_RESULT_VALUE);
/*     */   }
/*     */   
/*     */   public int GetResultInnerCode(int paramInt) {
/* 209 */     String str = GetProperty(paramInt, KEY_RESULT_INCODE);
/* 210 */     return Integer.parseInt(str);
/*     */   }
/*     */   
/*     */   public int GetResultCode(int paramInt) {
/* 214 */     String str = GetProperty(paramInt, KEY_RESULT_CODE);
/* 215 */     return Integer.parseInt(str);
/*     */   }
/*     */   
/*     */   public int GetResultType(int paramInt) {
/* 219 */     String str = GetProperty(paramInt, KEY_RESULT_TYPE);
/* 220 */     return Integer.parseInt(str);
/*     */   }
/*     */   
/*     */   public native boolean Init();
/*     */   
/*     */   public native void UnInit();
/*     */   
/*     */   public native String GetError(int paramInt);
/*     */   
/*     */   public native String GetVersion();
/*     */   
/*     */   public native int GetNewObject(String paramString);
/*     */   
/*     */   public native String GetObjectName(int paramInt);
/*     */   
/*     */   public native int SetObjectName(int paramInt, String paramString);
/*     */   
/*     */   public native int GetNewPropertys();
/*     */   
/*     */   public native int IsHandle(int paramInt);
/*     */   
/*     */   public native int AddRefHandle(int paramInt);
/*     */   
/*     */   public native int ReleaseHandle(int paramInt);
/*     */   
/*     */   public native int AddProperty(int paramInt, String paramString1, String paramString2);
/*     */   
/*     */   public native int ClearProperty(int paramInt1, int paramInt2);
/*     */   
/*     */   public native int RemoveProperty(int paramInt, String paramString);
/*     */   
/*     */   public native String GetProperty(int paramInt, String paramString);
/*     */   
/*     */   public native int SetServerIP(int paramInt, String paramString);
/*     */   
/*     */   public native String GetServerIP(int paramInt);
/*     */   
/*     */   public native int GetServerPort(int paramInt);
/*     */   
/*     */   public native int SetServerPort(int paramInt1, int paramInt2);
/*     */   
/*     */   public native int GetPropertysCount(int paramInt);
/*     */   
/*     */   public native int GetPropertysItem(int paramInt1, int paramInt2);
/*     */   
/*     */   public native int Call(int paramInt1, int paramInt2, int paramInt3);
/*     */   
/*     */   public native int GetResultPropertys(int paramInt);
/*     */   
/*     */   public native int GetResultInt(int paramInt);
/*     */   
/*     */   public native String GetResultString(int paramInt);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/rtx/RTXSvrApi.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */