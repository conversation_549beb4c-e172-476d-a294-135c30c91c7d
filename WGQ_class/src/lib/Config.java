/*     */ package lib;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.DataInputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.apache.pdfbox.pdmodel.PDDocument;
/*     */ import org.apache.regexp.RE;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Config
/*     */ {
/*  24 */   protected static JArray config = null;
/*  25 */   private static String ROOT = System.getProperty("user.home");
/*  26 */   protected static String OS = System.getProperty("os.name");
/*  27 */   protected static String DIRECTORY_SEPARATOR = null;
/*  28 */   protected static int len = 0;
/*  29 */   protected static String doc_url = "http://flexpaper.devaldi.com/docs_php.jsp";
/*     */   public Config() {
/*  31 */     if (DIRECTORY_SEPARATOR == null)
/*  32 */       DIRECTORY_SEPARATOR = System.getProperty("file.separator"); 
/*  33 */     if (isChange()) {
/*  34 */       config = parse_ini_file(getConfigFilename());
/*     */     }
/*     */   }
/*     */   
/*     */   public boolean isChange() {
/*  39 */     File file = new File(getConfigFilename());
/*  40 */     if (!file.isFile() || !file.canRead()) {
/*  41 */       config = null;
/*  42 */       return false;
/*     */     } 
/*  44 */     if (file.length() == len)
/*  45 */       return false; 
/*  46 */     len = (int)file.length();
/*  47 */     return true;
/*     */   }
/*     */   
/*     */   public boolean isWin() {
/*  51 */     if (OS.contains("Win"))
/*  52 */       return true; 
/*  53 */     return false;
/*     */   }
/*     */   
/*     */   public String getConfig(String paramString) {
/*  57 */     paramString = paramString.trim();
/*  58 */     if (paramString == null || paramString.length() <= 0 || config == null)
/*  59 */       return null; 
/*  60 */     return config.get(paramString);
/*     */   }
/*     */   
/*     */   public String getConfig(String paramString1, String paramString2) {
/*  64 */     paramString1 = paramString1.trim();
/*  65 */     if (paramString1 == null || paramString1.length() <= 0 || config == null)
/*  66 */       return paramString2; 
/*  67 */     return config.get(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public JArray getConfigs() {
/*  71 */     if (config == null) return null; 
/*  72 */     return config.clone();
/*     */   }
/*     */   
/*     */   public String getDocUrl() {
/*  76 */     return "<br/><br/>Click <a href='" + doc_url + "'>here</a> for more information on configuring FlexPaper with JSP";
/*     */   }
/*     */   
/*     */   public String getConfigFilename() {
/*  80 */     String str = ROOT + DIRECTORY_SEPARATOR + "jspConfig" + DIRECTORY_SEPARATOR;
/*  81 */     File file = new File(str);
/*  82 */     if (!file.isDirectory())
/*  83 */       file.mkdirs(); 
/*  84 */     return str + "jspconfig.ini";
/*     */   }
/*     */   
/*     */   public boolean saveConfig(JArray paramJArray) {
/*  88 */     if (!write_ini(paramJArray, getConfigFilename()))
/*  89 */       return false; 
/*  90 */     config = paramJArray;
/*  91 */     return true;
/*     */   }
/*     */   
/*     */   public int is_writable(String paramString) {
/*  95 */     File file = new File(paramString.trim());
/*  96 */     if (!file.isDirectory() || !file.canWrite())
/*  97 */       return 0; 
/*  98 */     return 1;
/*     */   }
/*     */   
/*     */   public void mkdir(String paramString) {
/* 102 */     paramString = paramString.trim();
/* 103 */     File file = new File(paramString);
/* 104 */     if (!file.isDirectory())
/* 105 */       file.mkdirs(); 
/*     */   }
/*     */   public boolean write_ini(JArray paramJArray, String paramString) {
/* 108 */     BufferedWriter bufferedWriter = null;
/* 109 */     FileWriter fileWriter = null;
/* 110 */     byte b = 30;
/*     */     try {
/* 112 */       fileWriter = new FileWriter(paramString);
/* 113 */       bufferedWriter = new BufferedWriter(fileWriter);
/* 114 */       for (byte b1 = 0; b1 < paramJArray.len(); b1++) {
/* 115 */         String str1 = paramJArray.getIndex(b1);
/* 116 */         bufferedWriter.write(str1);
/* 117 */         String str2 = paramJArray.get(b1).trim();
/* 118 */         if (str2.length() > 0) {
/* 119 */           for (byte b2 = 0; b2 < b - str1.length(); b2++)
/* 120 */             bufferedWriter.write(" "); 
/* 121 */           bufferedWriter.write("= ");
/* 122 */           bufferedWriter.write(str2);
/*     */         } 
/* 124 */         bufferedWriter.write("\r\n");
/*     */       } 
/* 126 */       bufferedWriter.close();
/* 127 */       fileWriter.close();
/* 128 */     } catch (Exception exception) {
/*     */       try {
/* 130 */         if (fileWriter != null)
/* 131 */           fileWriter.close(); 
/* 132 */         if (bufferedWriter != null)
/* 133 */           bufferedWriter.close(); 
/* 134 */       } catch (IOException iOException) {}
/*     */       
/* 136 */       exception.printStackTrace();
/* 137 */       return false;
/*     */     } 
/* 139 */     return true;
/*     */   }
/*     */   
/*     */   public JArray newConfig(String paramString) {
/* 143 */     JArray jArray = new JArray();
/* 144 */     jArray.add("[admin]", "");
/* 145 */     jArray.add("username", "ok");
/* 146 */     jArray.add("password", "ok");
/* 147 */     jArray.add("[requirements]", "");
/* 148 */     jArray.add("test_pdf2swf", "true");
/* 149 */     jArray.add("test_pdf2json", "true");
/* 150 */     jArray.add("licensekey", "");
/* 151 */     jArray.add("[general]", "");
/* 152 */     jArray.add("allowcache", "true");
/* 153 */     jArray.add("splitmode", "false");
/* 154 */     jArray.add("path.pdf", "C:\\pdf\\");
/* 155 */     jArray.add("path.swf", "C:\\docs\\");
/* 156 */     jArray.add("renderingorder.primary", "flash");
/* 157 */     jArray.add("renderingorder.secondary", "html");
/* 158 */     jArray.add("[external commands]", "");
/* 159 */     jArray.add("cmd.conversion.singledoc", "\"pdf2swf.exe\" \"{path.pdf}{pdffile}\" -o \"{path.swf}{pdffile}.swf\" -f -T 9 -t -s storeallcharacters -s linknameurl");
/* 160 */     jArray.add("cmd.conversion.splitpages", "\"pdf2swf.exe\" \"{path.pdf}{pdffile}\" -o \"{path.swf}{pdffile}_%.swf\" -f -T 9 -t -s storeallcharacters -s linknameurl");
/* 161 */     jArray.add("cmd.conversion.renderpage", "\"swfrender.exe\" \"{path.swf}{swffile}\" -p {page} -o \"{path.swf}{pdffile}_{page}_wev8.png\" -X 1024 -s keepaspectratio");
/* 162 */     jArray.add("cmd.conversion.rendersplitpage", "\"swfrender.exe\" \"{path.swf}{swffile}\" -o \"{path.swf}{pdffile}_{page}_wev8.png\" -X 1024 -s keepaspectratio");
/* 163 */     jArray.add("cmd.conversion.jsonfile", "\"pdf2json.exe\" \"{path.pdf}{pdffile}\" -enc UTF-8 -compress \"{path.swf}{pdffile}_wev8.js\"");
/* 164 */     jArray.add("cmd.conversion.splitjsonfile", "\"pdf2json.exe\" \"{path.pdf}{pdffile}\" -enc UTF-8 -compress -split 10 \"{path.swf}{pdffile}_%_wev8.js\"");
/* 165 */     jArray.add("cmd.searching.extracttext", "\"swfstrings.exe\" \"{swffile}\"");
/* 166 */     jArray.add("cmd.query.swfwidth", "\"swfdump.exe\" \"{swffile}\" -X");
/* 167 */     jArray.add("cmd.query.swfheight", "\"swfdump.exe\" \"{swffile}\" -Y");
/* 168 */     if (!write_ini(jArray, paramString))
/* 169 */       return null; 
/* 170 */     return jArray;
/*     */   }
/*     */   
/*     */   public JArray parse_ini_file(String paramString) {
/* 174 */     File file = new File(paramString);
/* 175 */     if (!file.isFile() || !file.canRead())
/* 176 */       return null; 
/* 177 */     JArray jArray = new JArray();
/*     */     try {
/* 179 */       FileInputStream fileInputStream = new FileInputStream(paramString);
/* 180 */       DataInputStream dataInputStream = new DataInputStream(fileInputStream);
/* 181 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(dataInputStream));
/*     */       String str;
/* 183 */       while ((str = bufferedReader.readLine()) != null) {
/* 184 */         String[] arrayOfString = str.split("=");
/* 185 */         String str1 = arrayOfString[0].trim();
/* 186 */         String str2 = null;
/* 187 */         if (arrayOfString.length > 1) {
/* 188 */           str2 = arrayOfString[1].trim();
/*     */         }
/* 190 */         jArray.add(str1, str2);
/*     */       } 
/* 192 */       dataInputStream.close();
/* 193 */       fileInputStream.close();
/* 194 */     } catch (Exception exception) {
/* 195 */       exception.printStackTrace();
/*     */     } 
/* 197 */     return jArray;
/*     */   }
/*     */   
/*     */   public boolean exec(String paramString) {
/* 201 */     boolean bool = true;
/*     */ 
/*     */     
/*     */     try {
/* 205 */       ArrayList<String> arrayList = commandLineAsList(paramString);
/*     */       
/* 207 */       ProcessBuilder processBuilder = new ProcessBuilder(arrayList);
/* 208 */       processBuilder.redirectErrorStream(true);
/* 209 */       Process process = processBuilder.start();
/*     */ 
/*     */       
/* 212 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
/*     */       String str;
/* 214 */       while ((str = bufferedReader.readLine()) != null) {
/* 215 */         if (str.toLowerCase().startsWith("warning"))
/*     */           continue; 
/* 217 */         if (str.toLowerCase().startsWith("error")) {
/*     */           
/* 219 */           bool = false; continue;
/* 220 */         }  if (str.toLowerCase().startsWith("fatal"))
/*     */         {
/* 222 */           bool = false;
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/*     */       try {
/* 228 */         process.waitFor();
/* 229 */       } catch (InterruptedException interruptedException) {
/* 230 */         return false;
/*     */       } 
/* 232 */     } catch (IOException iOException) {
/* 233 */       iOException.printStackTrace();
/* 234 */       return false;
/*     */     } 
/* 236 */     return bool;
/*     */   }
/*     */   
/*     */   public String separate(String paramString) {
/* 240 */     if (isWin()) {
/* 241 */       return (paramString.trim() + "\\").replace("\\\\", "\\");
/*     */     }
/* 243 */     return (paramString.trim() + "/").replace("//", "/");
/*     */   }
/*     */   
/*     */   public String execs(String paramString) {
/* 247 */     String str = "";
/*     */     try {
/* 249 */       ArrayList<String> arrayList = commandLineAsList(paramString);
/*     */       
/* 251 */       ProcessBuilder processBuilder = new ProcessBuilder(arrayList);
/* 252 */       processBuilder.redirectErrorStream(true);
/* 253 */       Process process = processBuilder.start();
/*     */ 
/*     */       
/* 256 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
/*     */       String str1;
/* 258 */       while ((str1 = bufferedReader.readLine()) != null) {
/* 259 */         str = str + "\t" + str1;
/*     */       }
/*     */       try {
/* 262 */         process.waitFor();
/* 263 */       } catch (InterruptedException interruptedException) {
/* 264 */         return null;
/*     */       } 
/* 266 */     } catch (IOException iOException) {
/* 267 */       iOException.printStackTrace();
/* 268 */       return null;
/*     */     } 
/* 270 */     return str;
/*     */   }
/*     */   
/*     */   public static ArrayList commandLineAsList(String paramString) {
/* 274 */     ArrayList<String> arrayList = new ArrayList();
/* 275 */     String str = "";
/* 276 */     boolean bool = false;
/*     */     
/* 278 */     for (byte b = 0; b < paramString.length(); b++) {
/* 279 */       char c = paramString.charAt(b);
/*     */       
/* 281 */       if (!bool && (c == ' ' || c == '\t')) {
/* 282 */         if (str.length() > 0) {
/* 283 */           arrayList.add(str);
/* 284 */           str = "";
/*     */         } 
/*     */       } else {
/* 287 */         if (c == '"') {
/* 288 */           bool = !bool ? true : false;
/*     */         }
/*     */         
/* 291 */         str = str + c;
/*     */       } 
/* 293 */     }  if (str.length() > 0) {
/* 294 */       arrayList.add(str);
/*     */     }
/*     */     
/* 297 */     return arrayList;
/*     */   }
/*     */   
/*     */   public void DeleteFiles(String paramString) {
/* 301 */     paramString = separate(paramString);
/* 302 */     File file = new File(paramString);
/* 303 */     if (!file.isDirectory())
/*     */       return; 
/* 305 */     String[] arrayOfString = file.list();
/* 306 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 307 */       File file1 = new File(paramString + arrayOfString[b]);
/* 308 */       file1.delete();
/*     */     } 
/*     */   }
/*     */   
/*     */   public ArrayList<String> DirectoryFiles(String paramString) {
/* 313 */     ArrayList<String> arrayList = new ArrayList();
/* 314 */     paramString = separate(paramString);
/* 315 */     File file = new File(paramString);
/* 316 */     if (!file.isDirectory() && !file.mkdirs()) {
/* 317 */       return null;
/*     */     }
/* 319 */     String[] arrayOfString = file.list();
/* 320 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 321 */       File file1 = new File(paramString + arrayOfString[b]);
/* 322 */       if (file1.isFile()) {
/* 323 */         arrayList.add(arrayOfString[b]);
/*     */       }
/*     */     } 
/* 326 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getSizeString(long paramLong) {
/* 330 */     String str = " byte";
/* 331 */     if (paramLong > 1023L) {
/* 332 */       str = " KB";
/* 333 */       paramLong /= 1024L;
/* 334 */       if (paramLong > 1023L) {
/* 335 */         str = " MB";
/* 336 */         paramLong /= 1024L;
/* 337 */         if (paramLong > 1023L) {
/* 338 */           paramLong /= 1024L;
/* 339 */           str = " GB";
/*     */         } 
/*     */       } 
/*     */     } 
/* 343 */     return paramLong + str;
/*     */   }
/*     */   
/*     */   public long FileSize(String paramString1, String paramString2) {
/* 347 */     paramString1 = separate(paramString1);
/* 348 */     File file = new File(paramString1 + paramString2);
/* 349 */     if (!file.isFile())
/* 350 */       return 0L; 
/* 351 */     return file.length();
/*     */   }
/*     */   
/*     */   public long FileSize(String paramString) {
/* 355 */     File file = new File(paramString);
/* 356 */     if (!file.isFile())
/* 357 */       return 0L; 
/* 358 */     return file.length();
/*     */   }
/*     */   
/*     */   public void DeleteFiles(String[] paramArrayOfString, String paramString) {
/* 362 */     paramString = separate(paramString);
/* 363 */     for (byte b = 0; b < paramArrayOfString.length; b++) {
/* 364 */       File file = new File(paramString + paramArrayOfString[b]);
/* 365 */       if (file.exists()) {
/* 366 */         file.delete();
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setConfig(String paramString1, String paramString2) {
/* 372 */     if (paramString1 == null || paramString1.trim().length() == 0)
/*     */       return; 
/* 374 */     config.set(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public boolean is_dir(String paramString) {
/* 378 */     if (paramString == null || paramString.length() == 0)
/* 379 */       return false; 
/* 380 */     File file = new File(paramString);
/* 381 */     if (file.isDirectory())
/* 382 */       return true; 
/* 383 */     return false;
/*     */   }
/*     */   
/*     */   public boolean file_exists(String paramString) {
/* 387 */     if (paramString == null || paramString.length() == 0)
/* 388 */       return false; 
/* 389 */     File file = new File(paramString);
/* 390 */     if (file.exists())
/* 391 */       return true; 
/* 392 */     return false;
/*     */   }
/*     */   
/*     */   public byte[] file_get_contents(String paramString) {
/* 396 */     byte[] arrayOfByte = { 0 };
/* 397 */     if (paramString == null || paramString == "")
/* 398 */       return arrayOfByte; 
/*     */     try {
/* 400 */       File file = new File(paramString);
/* 401 */       if (!file.isFile() || !file.canRead())
/* 402 */         return arrayOfByte; 
/* 403 */       FileInputStream fileInputStream = new FileInputStream(paramString);
/* 404 */       arrayOfByte = new byte[(int)file.length()];
/* 405 */       fileInputStream.read(arrayOfByte);
/* 406 */       fileInputStream.close();
/* 407 */     } catch (Exception exception) {
/* 408 */       exception.printStackTrace();
/*     */     } 
/* 410 */     return arrayOfByte;
/*     */   }
/*     */   
/*     */   public String file_get_content(String paramString) {
/* 414 */     String str = "";
/* 415 */     if (paramString == null || paramString == "")
/* 416 */       return null; 
/*     */     try {
/* 418 */       File file = new File(paramString);
/* 419 */       if (!file.isFile() || !file.canRead())
/* 420 */         return null; 
/* 421 */       FileInputStream fileInputStream = new FileInputStream(paramString);
/* 422 */       byte[] arrayOfByte = new byte[(int)file.length()];
/* 423 */       fileInputStream.read(arrayOfByte);
/* 424 */       fileInputStream.close();
/* 425 */       str = new String(arrayOfByte, "iso8859-1");
/* 426 */     } catch (Exception exception) {
/* 427 */       exception.printStackTrace();
/*     */     } 
/* 429 */     return str;
/*     */   }
/*     */   
/*     */   public boolean file_write_contents(String paramString1, String paramString2) {
/*     */     try {
/* 434 */       FileOutputStream fileOutputStream = new FileOutputStream(paramString1);
/* 435 */       byte[] arrayOfByte = paramString2.getBytes("iso8859-1");
/* 436 */       fileOutputStream.write(arrayOfByte);
/* 437 */       fileOutputStream.close();
/* 438 */     } catch (Exception exception) {
/* 439 */       exception.printStackTrace();
/* 440 */       return false;
/*     */     } 
/* 442 */     return true;
/*     */   }
/*     */   
/*     */   public int getTotalPage(String paramString) {
/* 446 */     int i = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 452 */       PDDocument pDDocument = PDDocument.load(new File(paramString));
/* 453 */       i = pDDocument.getDocumentCatalog().getPages().getCount();
/* 454 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 497 */     return i;
/*     */   }
/*     */   
/*     */   public ArrayList<String> glob(String paramString1, String paramString2) {
/* 501 */     paramString1 = separate(paramString1);
/* 502 */     ArrayList<String> arrayList = new ArrayList();
/* 503 */     File file = new File(paramString1);
/* 504 */     if (!file.isDirectory())
/* 505 */       return arrayList; 
/* 506 */     String[] arrayOfString = file.list();
/* 507 */     paramString2 = paramString2.replace(" ", "*");
/* 508 */     paramString2 = paramString2.replace(".", "[.]");
/* 509 */     paramString2 = paramString2.replace("*", "(.*)");
/* 510 */     Pattern pattern = Pattern.compile(paramString2);
/* 511 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 512 */       Matcher matcher = pattern.matcher(arrayOfString[b]);
/* 513 */       if (matcher.matches()) {
/* 514 */         arrayList.add(paramString1 + arrayOfString[b]);
/*     */       }
/*     */     } 
/* 517 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String strip_non_numerics(String paramString) {
/* 521 */     echo("***Config \tstrip_non_numerices  string = " + paramString);
/* 522 */     String str = "[\\D]";
/*     */     try {
/* 524 */       RE rE = new RE(str);
/* 525 */       paramString = rE.subst(paramString, "");
/* 526 */     } catch (Exception exception) {}
/* 527 */     return paramString;
/*     */   }
/*     */   
/*     */   public void echo(Object paramObject) {
/* 531 */     (new BaseBean()).writeLog(paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) throws Exception {
/* 541 */     Config config = new Config();
/* 542 */     int i = config.getTotalPage("D:/pdfFiles/201308/K/1377075718851.pdf");
/*     */ 
/*     */     
/* 545 */     String str = "D:/pdfFiles/201308/K/1377075718851.pdf";
/* 546 */     str = "D:/pdfFiles/201308/D/1377077126751.pdf";
/*     */ 
/*     */ 
/*     */     
/* 550 */     PDDocument pDDocument = PDDocument.load(new File(str));
/* 551 */     int j = pDDocument.getDocumentCatalog().getPages().getCount();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/lib/Config.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */