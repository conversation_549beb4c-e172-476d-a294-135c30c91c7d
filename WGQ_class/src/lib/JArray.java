/*     */ package lib;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ 
/*     */ public class JArray {
/*   6 */   private ArrayList<String> index = null;
/*   7 */   private ArrayList<String> data = null;
/*     */   
/*     */   public JArray() {
/*  10 */     this.index = new ArrayList<String>();
/*  11 */     this.data = new ArrayList<String>();
/*     */   }
/*     */   
/*     */   public JArray(String paramString) {
/*  15 */     set(makeArray(paramString));
/*     */   }
/*     */ 
/*     */   
/*     */   public JArray(ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2) {
/*  20 */     this.index = (ArrayList<String>)paramArrayList1.clone();
/*  21 */     this.data = (ArrayList<String>)paramArrayList2.clone();
/*     */   }
/*     */   
/*     */   public JArray clone() {
/*  25 */     return new JArray(this.index, this.data);
/*     */   }
/*     */   
/*     */   public void set(JArray paramJArray) {
/*  29 */     if (paramJArray == null)
/*  30 */       paramJArray = new JArray(); 
/*  31 */     this.index = paramJArray.index;
/*  32 */     this.data = paramJArray.data;
/*     */   }
/*     */   
/*     */   public int len() {
/*  36 */     return this.index.size();
/*     */   }
/*     */   
/*     */   public String getIndex(int paramInt) {
/*  40 */     return this.index.get(paramInt);
/*     */   }
/*     */   
/*     */   public String get(int paramInt) {
/*  44 */     return this.data.get(paramInt);
/*     */   }
/*     */   
/*     */   public void add(String paramString1, String paramString2) {
/*  48 */     this.index.add(paramString1);
/*  49 */     this.data.add(paramString2);
/*     */   }
/*     */   
/*     */   public void set(String paramString1, String paramString2) {
/*  53 */     int i = this.index.indexOf(paramString1);
/*  54 */     if (i < 0) {
/*  55 */       add(paramString1, paramString2);
/*     */       return;
/*     */     } 
/*  58 */     this.data.set(i, paramString2);
/*     */   }
/*     */   
/*     */   public String get(String paramString) {
/*  62 */     int i = this.index.indexOf(paramString);
/*  63 */     if (i < 0)
/*  64 */       return null; 
/*  65 */     return this.data.get(i);
/*     */   }
/*     */   
/*     */   public String get(String paramString1, String paramString2) {
/*  69 */     int i = this.index.indexOf(paramString1);
/*  70 */     if (i < 0 || this.data.get(i) == null) {
/*  71 */       return paramString2;
/*     */     }
/*  73 */     return this.data.get(i);
/*     */   }
/*     */   
/*     */   public void del(String paramString) {
/*  77 */     int i = this.index.indexOf(paramString);
/*  78 */     if (i > -1) {
/*  79 */       this.index.remove(i);
/*  80 */       this.data.remove(i);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public String makeString() {
/*  86 */     String str = "{";
/*  87 */     for (byte b = 0; b < this.index.size(); b++) {
/*  88 */       String str1 = this.index.get(b);
/*  89 */       str = str + '"' + str1 + "\":\"" + get(str1) + "\",";
/*     */     } 
/*  91 */     str = str.substring(0, (str.lastIndexOf(",") < 0) ? str.length() : str.lastIndexOf(",")) + "}";
/*  92 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public JArray makeArray(String paramString) {
/*  97 */     if (paramString == null)
/*  98 */       return null; 
/*  99 */     paramString = paramString.trim();
/* 100 */     boolean bool = (paramString.indexOf("{") >= 0) ? paramString.indexOf("{") : ((paramString.indexOf("[") >= 0) ? paramString.indexOf("[") : true);
/* 101 */     if (bool)
/* 102 */       return null; 
/* 103 */     paramString = paramString.substring(bool);
/* 104 */     if (paramString.length() < 3)
/* 105 */       return null; 
/* 106 */     return getJString(paramString);
/*     */   }
/*     */   
/*     */   public JArray getJString(String paramString) {
/* 110 */     paramString = paramString.trim();
/* 111 */     if (paramString.length() == 0)
/* 112 */       return null; 
/* 113 */     boolean bool1 = false;
/* 114 */     boolean bool2 = false;
/* 115 */     boolean bool3 = false;
/* 116 */     boolean bool4 = false;
/* 117 */     char[] arrayOfChar = paramString.toCharArray();
/* 118 */     String str1 = "";
/* 119 */     String str2 = "";
/* 120 */     JArray jArray = new JArray();
/* 121 */     for (byte b = 0; b < arrayOfChar.length; b++) {
/* 122 */       if (!bool3 && !bool2) {
/* 123 */         if (arrayOfChar[b] == '{' || arrayOfChar[b] == '[') {
/* 124 */           bool1 = true;
/* 125 */           str2 = "";
/* 126 */           str1 = "";
/* 127 */           bool4 = false; continue;
/*     */         } 
/* 129 */         if (arrayOfChar[b] == '}' || arrayOfChar[b] == ']') {
/* 130 */           str1 = str1.trim();
/* 131 */           if (str1.length() != 0) {
/* 132 */             jArray.add(str1, str2);
/*     */           }
/* 134 */           return jArray;
/* 135 */         }  if (arrayOfChar[b] == ':') {
/* 136 */           bool4 = true;
/* 137 */           bool1 = true; continue;
/*     */         } 
/* 139 */         if (arrayOfChar[b] == ',') {
/* 140 */           str1 = str1.trim();
/* 141 */           if (str1.length() != 0) {
/* 142 */             jArray.add(str1, str2);
/*     */           }
/* 144 */           str2 = "";
/* 145 */           str1 = "";
/* 146 */           bool4 = false;
/* 147 */           bool1 = true;
/*     */ 
/*     */           
/*     */           continue;
/*     */         } 
/* 152 */       } else if ((bool3 && arrayOfChar[b] == '\'') || (bool2 && arrayOfChar[b] == '"')) {
/* 153 */         bool3 = false;
/* 154 */         bool2 = false;
/*     */         
/*     */         continue;
/*     */       } 
/* 158 */       if (bool1) {
/* 159 */         if (arrayOfChar[b] == '"') {
/* 160 */           bool2 = true;
/* 161 */           bool1 = false;
/*     */           continue;
/*     */         } 
/* 164 */         if (arrayOfChar[b] == '\'') {
/* 165 */           bool3 = true;
/* 166 */           bool1 = false;
/*     */           continue;
/*     */         } 
/*     */       } 
/* 170 */       if (!bool4) {
/* 171 */         str1 = str1 + arrayOfChar[b];
/*     */       } else {
/* 173 */         str2 = str2 + arrayOfChar[b];
/*     */       }  continue;
/* 175 */     }  return jArray;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/lib/JArray.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */