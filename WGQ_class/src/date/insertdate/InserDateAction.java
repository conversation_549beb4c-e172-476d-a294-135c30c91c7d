/*     */ package date.insertdate;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.setup.ModeRightInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ 
/*     */ public class InserDateAction
/*     */   extends BaseBean
/*     */   implements Action
/*     */ {
/*     */   public String execute(RequestInfo requestInfo) {
/*  18 */     String retStr = "1";
/*  19 */     int startyear = 2020;
/*  20 */     int startmonth = 0;
/*  21 */     int startday = 1;
/*     */     
/*  23 */     int endyear = 2020;
/*  24 */     int endmonth = 11;
/*  25 */     int endtday = 31;
/*  26 */     SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
/*  27 */     Calendar cal = Calendar.getInstance();
/*  28 */     cal.set(1, startyear);
/*  29 */     cal.set(2, startmonth);
/*  30 */     cal.set(5, startday);
/*     */     
/*  32 */     Calendar ca2 = Calendar.getInstance();
/*  33 */     ca2.set(1, endyear);
/*  34 */     ca2.set(2, endmonth);
/*  35 */     ca2.set(5, endtday);
/*  36 */     long start = cal.getTimeInMillis();
/*  37 */     long end = ca2.getTimeInMillis();
/*  38 */     long jinlv = 86400000L;
/*  39 */     Date date = new Date();
/*     */     
/*  41 */     Calendar cal3 = Calendar.getInstance();
/*  42 */     String sql = "";
/*  43 */     String rq = "";
/*  44 */     int xq = 0;
/*  45 */     int sftstx = -1;
/*  46 */     RecordSet rs = new RecordSet(); long i;
/*  47 */     for (i = start; i <= end; i += jinlv) {
/*  48 */       date.setTime(i);
/*  49 */       rq = sdf.format(date);
/*  50 */       cal3.setTime(date);
/*  51 */       int week = cal3.get(7);
/*  52 */       switch (week) {
/*     */         case 1:
/*  54 */           xq = 0;
/*  55 */           sftstx = 1;
/*     */           break;
/*     */         case 2:
/*  58 */           xq = 1;
/*  59 */           sftstx = 0;
/*     */           break;
/*     */         case 3:
/*  62 */           xq = 2;
/*  63 */           sftstx = 0;
/*     */           break;
/*     */         case 4:
/*  66 */           xq = 3;
/*  67 */           sftstx = 0;
/*     */           break;
/*     */         case 5:
/*  70 */           xq = 4;
/*  71 */           sftstx = 0;
/*     */           break;
/*     */         case 6:
/*  74 */           xq = 5;
/*  75 */           sftstx = 1;
/*     */           break;
/*     */         case 7:
/*  78 */           xq = 6;
/*  79 */           sftstx = 1;
/*     */           break;
/*     */       } 
/*  82 */       sql = "insert into uf_ycglzb (formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,rq,xq,sftstx) values(10001,1,0,to_char(sysdate,'yyyy-mm-dd'),to_char(sysdate,'hh24:mi:ss'),'" + rq + "'," + xq + "," + sftstx + ")";
/*     */       
/*  84 */       rs.execute(sql);
/*  85 */       writeLog("uf_ycglzb sql====" + sql);
/*     */     } 
/*     */     
/*  88 */     String billsql = "select id,modedatacreater,formmodeid from uf_ycglzb ";
/*  89 */     rs.execute(billsql);
/*     */     try {
/*  91 */       while (rs.next()) {
/*  92 */         int billid = Util.getIntValue(rs.getString("id"));
/*  93 */         int modedatacreater = Util.getIntValue(rs.getString("modedatacreater"));
/*  94 */         int formmodeid = Util.getIntValue(rs.getString("formmodeid"));
/*  95 */         ModeRightInfo ModeRightInfo = new ModeRightInfo();
/*  96 */         ModeRightInfo.rebuildModeDataShareByEdit(modedatacreater, formmodeid, billid);
/*     */       } 
/*  98 */     } catch (Exception e) {
/*  99 */       retStr = "0";
/* 100 */       writeLog("权限重构失败-------uf_ycglzb----" + e);
/*     */     } 
/* 102 */     return retStr;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/date/insertdate/InserDateAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */