/*     */ package jcifs.http;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import java.security.Principal;
/*     */ import java.util.Enumeration;
/*     */ import javax.servlet.Filter;
/*     */ import javax.servlet.FilterChain;
/*     */ import javax.servlet.FilterConfig;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import jcifs.Config;
/*     */ import jcifs.UniAddress;
/*     */ import jcifs.smb.NtlmChallenge;
/*     */ import jcifs.smb.NtlmPasswordAuthentication;
/*     */ import jcifs.smb.SmbAuthException;
/*     */ import jcifs.smb.SmbSession;
/*     */ import jcifs.util.Base64;
/*     */ import jcifs.util.Hexdump;
/*     */ import jcifs.util.LogStream;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.util.ConstantsUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class NtlmHttpFilter
/*     */   implements Filter
/*     */ {
/*  56 */   private static LogStream log = LogStream.getInstance();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  61 */   private static Logger newlog = LoggerFactory.getLogger(NtlmHttpFilter.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String defaultDomain;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String domainController;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean loadBalance;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean enableBasic;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean insecureBasic;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String realm;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init(FilterConfig paramFilterConfig) throws ServletException {
/* 103 */     Config.setProperty("jcifs.smb.client.soTimeout", "1800000");
/* 104 */     Config.setProperty("jcifs.netbios.cachePolicy", "1200");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 110 */     Config.setProperty("jcifs.smb.lmCompatibility", "0");
/* 111 */     Config.setProperty("jcifs.smb.client.useExtendedSecurity", "false");
/*     */     
/* 113 */     Enumeration<String> enumeration = paramFilterConfig.getInitParameterNames();
/* 114 */     while (enumeration.hasMoreElements()) {
/* 115 */       String str = enumeration.nextElement();
/* 116 */       if (str.startsWith("jcifs.")) {
/* 117 */         Config.setProperty(str, paramFilterConfig.getInitParameter(str));
/*     */       }
/*     */     } 
/* 120 */     this.defaultDomain = Config.getProperty("jcifs.smb.client.domain");
/* 121 */     this.domainController = Config.getProperty("jcifs.http.domainController");
/* 122 */     if (this.domainController == null) {
/* 123 */       this.domainController = this.defaultDomain;
/* 124 */       this.loadBalance = Config.getBoolean("jcifs.http.loadBalance", true);
/*     */     } 
/* 126 */     this
/* 127 */       .enableBasic = Boolean.valueOf(Config.getProperty("jcifs.http.enableBasic")).booleanValue();
/* 128 */     this
/* 129 */       .insecureBasic = Boolean.valueOf(Config.getProperty("jcifs.http.insecureBasic")).booleanValue();
/* 130 */     this.realm = Config.getProperty("jcifs.http.basicRealm");
/* 131 */     if (this.realm == null) this.realm = "jCIFS"; 
/*     */     int i;
/* 133 */     if ((i = Config.getInt("jcifs.util.loglevel", -1)) != -1) {
/* 134 */       LogStream.setLevel(i);
/*     */     }
/* 136 */     if (LogStream.level > 2) {
/*     */       try {
/* 138 */         Config.store((OutputStream)log, "JCIFS PROPERTIES");
/* 139 */       } catch (IOException iOException) {}
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void destroy() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/* 163 */     HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/* 164 */     HttpServletResponse httpServletResponse = (HttpServletResponse)paramServletResponse;
/*     */     
/*     */     try {
/*     */       NtlmPasswordAuthentication ntlmPasswordAuthentication;
/* 168 */       if ((ntlmPasswordAuthentication = negotiate(httpServletRequest, httpServletResponse, false)) == null) {
/*     */         return;
/*     */       }
/*     */       
/* 172 */       paramFilterChain.doFilter((ServletRequest)new NtlmHttpServletRequest(httpServletRequest, (Principal)ntlmPasswordAuthentication), paramServletResponse);
/*     */     }
/* 174 */     catch (Exception exception) {
/* 175 */       newlog.error("NtlmHttpFilter : " + exception);
/* 176 */       if (exception.getMessage().indexOf("The parameter is incorrect.") > -1) {
/* 177 */         String str = ConstantsUtil.INTEGRATION_LDAP_DOWNLOAD;
/* 178 */         newlog.error("NtlmHttpFilter : The parameter is incorrect. Response send redirect to : " + str);
/* 179 */         httpServletResponse.sendRedirect(str);
/*     */         return;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected NtlmPasswordAuthentication negotiate(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, boolean paramBoolean) throws IOException, ServletException {
/* 201 */     NtlmPasswordAuthentication ntlmPasswordAuthentication = null;
/* 202 */     String str = paramHttpServletRequest.getHeader("Authorization");
/* 203 */     boolean bool = (this.enableBasic && (this.insecureBasic || paramHttpServletRequest.isSecure())) ? true : false;
/*     */     
/* 205 */     if (str != null && (str.startsWith("NTLM ") || (bool && str
/* 206 */       .startsWith("Basic ")))) {
/* 207 */       UniAddress uniAddress; if (str.startsWith("NTLM ")) {
/* 208 */         byte[] arrayOfByte; HttpSession httpSession = paramHttpServletRequest.getSession();
/*     */ 
/*     */         
/* 211 */         if (this.loadBalance) {
/* 212 */           NtlmChallenge ntlmChallenge = (NtlmChallenge)httpSession.getAttribute("NtlmHttpChal");
/* 213 */           if (ntlmChallenge == null) {
/* 214 */             ntlmChallenge = SmbSession.getChallengeForDomain();
/* 215 */             httpSession.setAttribute("NtlmHttpChal", ntlmChallenge);
/*     */           } 
/* 217 */           uniAddress = ntlmChallenge.dc;
/* 218 */           arrayOfByte = ntlmChallenge.challenge;
/*     */         } else {
/* 220 */           uniAddress = UniAddress.getByName(this.domainController, true);
/* 221 */           arrayOfByte = SmbSession.getChallenge(uniAddress);
/*     */         } 
/*     */         
/* 224 */         if ((ntlmPasswordAuthentication = NtlmSsp.authenticate(paramHttpServletRequest, paramHttpServletResponse, arrayOfByte)) == null) {
/* 225 */           return null;
/*     */         }
/*     */         
/* 228 */         httpSession.removeAttribute("NtlmHttpChal");
/*     */       } else {
/* 230 */         String str1 = new String(Base64.decode(str.substring(6)), "US-ASCII");
/*     */         
/* 232 */         int i = str1.indexOf(':');
/* 233 */         String str2 = (i != -1) ? str1.substring(0, i) : str1;
/* 234 */         String str3 = (i != -1) ? str1.substring(i + 1) : "";
/*     */         
/* 236 */         i = str2.indexOf('\\');
/* 237 */         if (i == -1) i = str2.indexOf('/'); 
/* 238 */         String str4 = (i != -1) ? str2.substring(0, i) : this.defaultDomain;
/*     */         
/* 240 */         str2 = (i != -1) ? str2.substring(i + 1) : str2;
/* 241 */         ntlmPasswordAuthentication = new NtlmPasswordAuthentication(str4, str2, str3);
/* 242 */         uniAddress = UniAddress.getByName(this.domainController, true);
/*     */       } 
/*     */       
/*     */       try {
/* 246 */         SmbSession.logon(uniAddress, ntlmPasswordAuthentication);
/*     */         
/* 248 */         if (LogStream.level > 2) {
/* 249 */           log.println("NtlmHttpFilter: " + ntlmPasswordAuthentication + " successfully authenticated against " + uniAddress);
/*     */         }
/*     */       }
/* 252 */       catch (SmbAuthException smbAuthException) {
/* 253 */         if (LogStream.level > 1) {
/* 254 */           log.println("NtlmHttpFilter: " + ntlmPasswordAuthentication.getName() + ": 0x" + 
/* 255 */               Hexdump.toHexString(smbAuthException.getNtStatus(), 8) + ": " + smbAuthException);
/*     */         }
/*     */         
/* 258 */         if (smbAuthException.getNtStatus() == -1073741819) {
/*     */ 
/*     */ 
/*     */           
/* 262 */           HttpSession httpSession = paramHttpServletRequest.getSession(false);
/* 263 */           if (httpSession != null) {
/* 264 */             httpSession.removeAttribute("NtlmHttpAuth");
/*     */           }
/*     */         } 
/* 267 */         paramHttpServletResponse.setHeader("WWW-Authenticate", "NTLM");
/* 268 */         if (bool) {
/* 269 */           paramHttpServletResponse.addHeader("WWW-Authenticate", "Basic realm=\"" + this.realm + "\"");
/*     */         }
/*     */         
/* 272 */         paramHttpServletResponse.getOutputStream().print("<script language=javascript>window.location.href='<%=weaver.general.GCONST.getContextPath()%>/login/Login.jsp?logintype=1&message=60';</script>");
/*     */ 
/*     */ 
/*     */         
/* 276 */         return null;
/*     */       } 
/* 278 */       paramHttpServletRequest.getSession().setAttribute("NtlmHttpAuth", ntlmPasswordAuthentication);
/*     */     }
/* 280 */     else if (!paramBoolean) {
/* 281 */       HttpSession httpSession = paramHttpServletRequest.getSession(false);
/* 282 */       if (httpSession == null || (
/* 283 */         ntlmPasswordAuthentication = (NtlmPasswordAuthentication)httpSession.getAttribute("NtlmHttpAuth")) == null) {
/* 284 */         paramHttpServletResponse.setHeader("WWW-Authenticate", "NTLM");
/* 285 */         if (bool) {
/* 286 */           paramHttpServletResponse.addHeader("WWW-Authenticate", "Basic realm=\"" + this.realm + "\"");
/*     */         }
/*     */         
/* 289 */         paramHttpServletResponse.setStatus(401);
/* 290 */         paramHttpServletResponse.setContentLength(0);
/* 291 */         paramHttpServletResponse.flushBuffer();
/* 292 */         return null;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 297 */     return ntlmPasswordAuthentication;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFilterConfig(FilterConfig paramFilterConfig) {
/*     */     try {
/* 306 */       init(paramFilterConfig);
/* 307 */     } catch (Exception exception) {
/* 308 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FilterConfig getFilterConfig() {
/* 317 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/jcifs/http/NtlmHttpFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */