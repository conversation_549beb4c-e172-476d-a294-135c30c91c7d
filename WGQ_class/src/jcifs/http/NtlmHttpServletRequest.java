/*    */ package jcifs.http;
/*    */ 
/*    */ import java.security.Principal;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletRequestWrapper;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class NtlmHttpServletRequest
/*    */   extends HttpServletRequestWrapper
/*    */ {
/*    */   Principal principal;
/*    */   
/*    */   NtlmHttpServletRequest(HttpServletRequest paramHttpServletRequest, Principal paramPrincipal) {
/* 42 */     super(paramHttpServletRequest);
/* 43 */     this.principal = paramPrincipal;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getRemoteUser() {
/* 51 */     return this.principal.getName();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Principal getUserPrincipal() {
/* 59 */     return this.principal;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getAuthType() {
/* 67 */     return "NTLM";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/jcifs/http/NtlmHttpServletRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */