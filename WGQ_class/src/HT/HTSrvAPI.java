/*     */ package HT;
/*     */ 
/*     */ import java.security.DigestException;
/*     */ import java.security.MessageDigest;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.DESedeKeySpec;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HTSrvAPI
/*     */ {
/*     */   public String HTSrvSHA1(String paramString, int paramInt) {
/*  25 */     String str = "";
/*     */     
/*     */     try {
/*  28 */       byte[] arrayOfByte = new byte[20];
/*  29 */       MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
/*  30 */       messageDigest.update(paramString.getBytes(), 0, paramInt);
/*  31 */       messageDigest.digest(arrayOfByte, 0, 20);
/*  32 */       str = ByteToString(arrayOfByte, 20);
/*     */     }
/*  34 */     catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/*     */       
/*  36 */       noSuchAlgorithmException.printStackTrace();
/*     */     }
/*  38 */     catch (DigestException digestException) {
/*     */       
/*  40 */       digestException.printStackTrace();
/*     */     } 
/*     */     
/*  43 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String HTSrvCrypt(int paramInt1, String paramString1, int paramInt2, String paramString2) {
/*  60 */     byte[] arrayOfByte1 = new byte[24];
/*     */     
/*  62 */     String str = paramString1 + paramString1.substring(0, 16);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  67 */     byte[] arrayOfByte2 = new byte[24];
/*  68 */     arrayOfByte2 = StrToByte(str);
/*     */     
/*  70 */     byte[] arrayOfByte3 = StrToByte(paramString2);
/*     */     
/*     */     try {
/*  73 */       boolean bool = true;
/*  74 */       DESedeKeySpec dESedeKeySpec = new DESedeKeySpec(arrayOfByte2);
/*  75 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DESede");
/*  76 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESedeKeySpec);
/*  77 */       Cipher cipher = Cipher.getInstance("DESede");
/*  78 */       cipher.init(bool, secretKey);
/*  79 */       arrayOfByte1 = cipher.doFinal(arrayOfByte3);
/*     */     }
/*  81 */     catch (Exception exception) {
/*  82 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/*  86 */     return ByteToString(arrayOfByte1, 24);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String ByteToString(byte[] paramArrayOfbyte, int paramInt) {
/*  95 */     if (paramArrayOfbyte == null) {
/*  96 */       return "";
/*     */     }
/*  98 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 100 */     byte[] arrayOfByte = paramArrayOfbyte;
/*     */     
/* 102 */     for (byte b = 0; b < paramInt; b++) {
/*     */       
/* 104 */       int i = arrayOfByte[b] >> 4 & 0xF;
/* 105 */       stringBuffer.append("0123456789ABCDEF00000".charAt(i));
/* 106 */       i = arrayOfByte[b] & 0xF;
/* 107 */       stringBuffer.append("0123456789ABCDEF00000".charAt(i));
/*     */     } 
/*     */     
/* 110 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public byte[] StrToByte(String paramString) {
/* 178 */     int i = paramString.length();
/* 179 */     byte[] arrayOfByte = new byte[i / 2];
/* 180 */     char[] arrayOfChar = new char[i];
/*     */     
/* 182 */     for (byte b2 = 0; b2 < i; b2++) {
/* 183 */       arrayOfChar[b2] = paramString.charAt(b2);
/*     */     }
/* 185 */     for (byte b1 = 0; b1 < i; b1++) {
/* 186 */       byte b = (byte)arrayOfChar[b1];
/* 187 */       if (b1 % 2 == 0) {
/* 188 */         if (b >= 48 && b <= 57) {
/* 189 */           arrayOfByte[b1 / 2] = (byte)(b - 48 << 4);
/* 190 */         } else if (b >= 97 && b <= 102) {
/* 191 */           arrayOfByte[b1 / 2] = (byte)(b - 87 << 4);
/* 192 */         } else if (b >= 65 && b <= 70) {
/* 193 */           arrayOfByte[b1 / 2] = (byte)(b - 55 << 4);
/*     */         
/*     */         }
/*     */       
/*     */       }
/* 198 */       else if (b >= 48 && b <= 57) {
/* 199 */         arrayOfByte[b1 / 2] = (byte)(arrayOfByte[b1 / 2] | b - 48);
/* 200 */       } else if (b >= 97 && b <= 102) {
/* 201 */         arrayOfByte[b1 / 2] = (byte)(arrayOfByte[b1 / 2] | b - 87);
/* 202 */       } else if (b >= 65 && b <= 70) {
/* 203 */         arrayOfByte[b1 / 2] = (byte)(arrayOfByte[b1 / 2] | b - 55);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 209 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String HexToStr(byte[] paramArrayOfbyte, int paramInt) {
/* 214 */     char[] arrayOfChar = new char[2 * paramInt + 1];
/*     */     
/* 216 */     for (byte b = 0; b < paramInt; b++) {
/* 217 */       if ((paramArrayOfbyte[b] & 0xF0) >> 4 >= 0 && (paramArrayOfbyte[b] & 0xF0) >> 4 <= 9) {
/* 218 */         arrayOfChar[2 * b] = (char)(((paramArrayOfbyte[b] & 0xF0) >> 4) + 48);
/* 219 */       } else if ((paramArrayOfbyte[b] & 0xF0) >> 4 >= 10 && (paramArrayOfbyte[b] & 0xF0) >> 4 <= 16) {
/* 220 */         arrayOfChar[2 * b] = (char)(((paramArrayOfbyte[b] & 0xF0) >> 4) + 55);
/*     */       } 
/*     */       
/* 223 */       if ((paramArrayOfbyte[b] & 0xF) >= 0 && (paramArrayOfbyte[b] & 0xF) <= 9) {
/* 224 */         arrayOfChar[2 * b + 1] = (char)((paramArrayOfbyte[b] & 0xF) + 48);
/* 225 */       } else if ((paramArrayOfbyte[b] & 0xF) >= 10 && (paramArrayOfbyte[b] & 0xF) <= 16) {
/* 226 */         arrayOfChar[2 * b + 1] = (char)((paramArrayOfbyte[b] & 0xF) + 55);
/*     */       } 
/*     */     } 
/* 229 */     return new String(arrayOfChar);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/HT/HTSrvAPI.class
 * Java compiler version: 2 (46.0)
 * JD-Core Version:       1.1.3
 */