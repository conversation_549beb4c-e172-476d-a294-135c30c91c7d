/*    */ package weaver.addressbook;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.company.DepartmentComInfo;
/*    */ import weaver.hrm.company.SubCompanyComInfo;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ public class AddressBookUtil
/*    */   extends BaseBean {
/*    */   public String getAddressBookUserInfo(String paramString) {
/* 11 */     String str = "";
/*    */     try {
/* 13 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 14 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 15 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*    */       
/* 17 */       str = "<table><tr><td rowspan=\"2\"><img src=\"" + resourceComInfo.getMessagerUrls(paramString) + "\"></td>";
/* 18 */       str = str + "<td>";
/* 19 */       str = str + resourceComInfo.getLastname(paramString) + "&nbsp;" + subCompanyComInfo.getSubCompanyname(resourceComInfo.getSubCompanyID(paramString)) + "&nbsp;";
/* 20 */       str = str + "</td></tr>";
/* 21 */       str = str + "<tr><td>";
/* 22 */       str = str + departmentComInfo.getDepartmentname(resourceComInfo.getDepartmentID(paramString)) + "&nbsp;";
/* 23 */       str = str + resourceComInfo.getMobile(paramString) + "&nbsp;";
/* 24 */       str = str + resourceComInfo.getEmail(paramString) + "&nbsp;";
/* 25 */       str = str + "</td></tr></table>";
/*    */     }
/* 27 */     catch (Exception exception) {
/* 28 */       exception.printStackTrace();
/* 29 */       writeLog(exception);
/*    */     } 
/* 31 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/addressbook/AddressBookUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */