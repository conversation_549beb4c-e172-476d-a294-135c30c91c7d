/*    */ package weaver.integration.thirdsdk.alymail.util;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ALYMailParam
/*    */ {
/*    */   public static String getALMailSignParam(Map<String, Object> paramMap, String paramString1, String paramString2) {
/* 25 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 26 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 27 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 28 */     hashMap1.put("accessToken", paramString1);
/* 29 */     hashMap1.put("accessTarget", paramString2);
/* 30 */     hashMap3.put("access", hashMap1);
/* 31 */     paramMap.remove("canceled");
/* 32 */     paramMap.remove("id");
/* 33 */     paramMap.remove("supsubcomid");
/* 34 */     paramMap.remove("supdepid");
/* 35 */     paramMap.remove("subcompanyid1");
/* 36 */     paramMap.remove("showorder");
/* 37 */     for (String str : paramMap.keySet()) {
/* 38 */       hashMap2.put(str, paramMap.get(str));
/*    */     }
/* 40 */     hashMap3.put("param", hashMap2);
/* 41 */     return JSON.toJSONString(hashMap3);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/alymail/util/ALYMailParam.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */