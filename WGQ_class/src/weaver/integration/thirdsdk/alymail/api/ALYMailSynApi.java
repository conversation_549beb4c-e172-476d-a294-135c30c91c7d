/*     */ package weaver.integration.thirdsdk.alymail.api;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.hrm.util.face.HrmFaceCheckManager;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.framework.data.record.SimpleRecordData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ 
/*     */ 
/*     */ public class ALYMailSynApi
/*     */ {
/*  24 */   private Logger newlog = LoggerFactory.getLogger(ALYMailSynApi.class);
/*  25 */   private static ALYMailSynApi instance = null;
/*  26 */   private static User user = null;
/*  27 */   private static String accessTarget = null;
/*  28 */   private static String accessCode = null;
/*  29 */   private static String accessPassword = null;
/*  30 */   private static String mailMasterId = null;
/*  31 */   private static String bindField = null;
/*  32 */   private MailDao mailDao = new MailDao();
/*     */ 
/*     */   
/*     */   private ALYMailSynApi(User paramUser) {
/*  36 */     this; user = paramUser;
/*     */   }
/*     */   
/*     */   public static synchronized ALYMailSynApi getInstance(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/*  40 */     if (instance == null) {
/*  41 */       synchronized (ALYMailSynApi.class) {
/*  42 */         if (instance == null) {
/*  43 */           instance = new ALYMailSynApi(paramUser);
/*     */         }
/*     */       } 
/*     */     }
/*  47 */     doInit(paramMap1, paramMap2);
/*  48 */     return instance;
/*     */   }
/*     */   
/*     */   private static void doInit(Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/*  52 */     for (String str : paramMap2.keySet()) {
/*  53 */       if ("alcode".equals(str)) {
/*  54 */         accessCode = paramMap2.get(str).toString(); continue;
/*  55 */       }  if ("alpassword".equals(str)) {
/*  56 */         accessPassword = paramMap2.get(str).toString(); continue;
/*  57 */       }  if ("aldomain".equals(str)) {
/*  58 */         accessTarget = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  64 */     mailMasterId = Util.null2String(paramMap1.get("id"));
/*  65 */     bindField = Util.null2String(paramMap1.get("bindfield"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized JSONObject initOrgAndUser(User paramUser, String paramString) throws Exception {
/*  89 */     JSONObject jSONObject = new JSONObject();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*  95 */       this.newlog.error("------------------------------初始化OA组织架构和人员到阿里云邮箱开始------------------------------");
/*  96 */       String str = ALYMailApi.getRootDepartmentId(paramString, accessTarget);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 107 */       syncSubcompany(paramString, paramUser, str);
/*     */       
/* 109 */       syncDepartment(paramString, paramUser);
/*     */       
/* 111 */       syncUser(paramString, paramUser);
/*     */       
/* 113 */       this.newlog.error("------------------------------初始化OA组织架构和人员到阿里云邮箱结束------------------------------");
/* 114 */       return null;
/* 115 */     } catch (Exception exception) {
/* 116 */       this.newlog.error("OA组织架构和人员初始化到阿里云邮箱，出现异常：", exception);
/* 117 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private synchronized void syncSubcompany(String paramString1, User paramUser, String paramString2) {
/* 127 */     List<Map<String, Object>> list = HrmFaceCheckManager.syncBatch("0", HrmFaceCheckManager.getOaSubcompany(), true);
/* 128 */     if (list != null && list.size() > 0) {
/* 129 */       syncSubcompanyByList(list, paramString1, paramUser, paramString2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void syncSubcompanyByList(List<Map<String, Object>> paramList, String paramString1, User paramUser, String paramString2) {
/* 140 */     if (paramList != null && paramList.size() > 0) {
/* 141 */       RecordSet recordSet1 = new RecordSet();
/* 142 */       RecordSet recordSet2 = new RecordSet();
/* 143 */       for (Map<String, Object> map : paramList) {
/* 144 */         List<Map<String, Object>> list = (List)map.get("children");
/* 145 */         SimpleRecordData simpleRecordData = (SimpleRecordData)map.get("simpleRecordData");
/* 146 */         String str1 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("canceled")).getFieldValue());
/* 147 */         String str2 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("id")).getFieldValue());
/* 148 */         String str3 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("subcompanyname")).getFieldValue());
/* 149 */         String str4 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("supsubcomid")).getFieldValue());
/* 150 */         int i = Util.getIntValue(Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("showorder")).getFieldValue()), 0);
/* 151 */         if (str4 == null || "".equals(str4)) {
/* 152 */           str4 = "0";
/*     */         }
/*     */         
/* 155 */         if (i < 0) {
/* 156 */           i = 0;
/*     */         }
/*     */         
/* 159 */         String str5 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 160 */         recordSet1.executeQuery(str5, new Object[] { str2, mailMasterId });
/* 161 */         boolean bool = recordSet1.next();
/* 162 */         JSONObject jSONObject = new JSONObject();
/* 163 */         if (bool) {
/*     */           
/* 165 */           String str7 = recordSet1.getString("mailid");
/* 166 */           String str8 = recordSet1.getString("mailpid");
/*     */           
/* 168 */           boolean bool1 = false;
/* 169 */           List list1 = ALYMailApi.getAllALYDeptids(paramString1, accessTarget);
/* 170 */           if (list1.contains(str7)) {
/* 171 */             bool1 = true;
/*     */           }
/* 173 */           if (bool1) {
/* 174 */             if (!"1".equals(str1)) {
/* 175 */               JSONObject jSONObject1 = new JSONObject();
/* 176 */               String str9 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 177 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 178 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 179 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 180 */               if ("0".equals(str4)) {
/* 181 */                 str8 = paramString2;
/*     */               } else {
/* 183 */                 recordSet2.executeQuery(str9, new Object[] { str4, mailMasterId });
/* 184 */                 if (recordSet2.next()) {
/* 185 */                   str8 = recordSet2.getString("mailid");
/*     */                 }
/*     */               } 
/* 188 */               hashMap1.put("accessToken", paramString1);
/* 189 */               hashMap1.put("accessTarget", accessTarget);
/* 190 */               hashMap3.put("access", hashMap1);
/* 191 */               hashMap2.put("departmentId", str7);
/* 192 */               hashMap2.put("parentId", str8);
/* 193 */               hashMap2.put("name", str3);
/* 194 */               hashMap3.put("param", hashMap2);
/* 195 */               String str10 = JSON.toJSONString(hashMap3);
/* 196 */               jSONObject = ALYMailApi.updateDepartment(str10);
/* 197 */               jSONObject.put("operateType", "4");
/* 198 */               String str11 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 199 */               if ("100".equals(str11)) {
/* 200 */                 recordSet2.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str3, str8, str7, mailMasterId });
/*     */               }
/*     */             } else {
/*     */               
/* 204 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 205 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 206 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 207 */               hashMap1.put("accessToken", paramString1);
/* 208 */               hashMap1.put("accessTarget", accessTarget);
/* 209 */               hashMap3.put("access", hashMap1);
/* 210 */               hashMap2.put("departmentId", str7);
/* 211 */               hashMap3.put("param", hashMap2);
/* 212 */               String str9 = JSON.toJSONString(hashMap3);
/* 213 */               jSONObject = ALYMailApi.deleteDepartment(str9);
/* 214 */               jSONObject.put("operateType", "4");
/* 215 */               String str10 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 216 */               if ("100".equals(str10)) {
/* 217 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str7, mailMasterId });
/*     */               }
/*     */             } 
/*     */           } else {
/*     */             
/* 222 */             String str9 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 223 */             recordSet2.executeQuery(str9, new Object[] { str4, mailMasterId });
/* 224 */             if (recordSet2.next()) {
/* 225 */               str8 = recordSet2.getString("mailid");
/*     */             }
/* 227 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 228 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 229 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 230 */             hashMap1.put("accessToken", paramString1);
/* 231 */             hashMap1.put("accessTarget", accessTarget);
/* 232 */             hashMap3.put("access", hashMap1);
/* 233 */             hashMap2.put("parentId", str8);
/* 234 */             hashMap2.put("name", str3);
/* 235 */             hashMap3.put("param", hashMap2);
/* 236 */             String str10 = JSON.toJSONString(hashMap3);
/* 237 */             jSONObject = ALYMailApi.createDepartment(str10);
/* 238 */             jSONObject.put("operateType", "4");
/* 239 */             String str11 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 240 */             String str12 = (String)jSONObject.getJSONObject("data").get("departmentId");
/* 241 */             if ("100".equals(str11)) {
/* 242 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str12, str8, str2, "1", str3, mailMasterId });
/*     */             
/*     */             }
/*     */           }
/*     */         
/*     */         }
/* 248 */         else if (!"1".equals(str1)) {
/*     */           
/* 250 */           String str7 = paramString2;
/* 251 */           String str8 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 252 */           recordSet2.executeQuery(str8, new Object[] { str4, mailMasterId });
/* 253 */           if (recordSet2.next()) {
/* 254 */             str7 = recordSet2.getString("mailid");
/*     */           } else {
/* 256 */             str7 = paramString2;
/*     */           } 
/* 258 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 259 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 260 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 261 */           hashMap1.put("accessToken", paramString1);
/* 262 */           hashMap1.put("accessTarget", accessTarget);
/* 263 */           hashMap3.put("access", hashMap1);
/* 264 */           hashMap2.put("parentId", str7);
/* 265 */           hashMap2.put("name", str3);
/* 266 */           hashMap3.put("param", hashMap2);
/* 267 */           String str9 = JSON.toJSONString(hashMap3);
/* 268 */           jSONObject = ALYMailApi.createDepartment(str9);
/* 269 */           jSONObject.put("operateType", "4");
/* 270 */           String str10 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 271 */           String str11 = (String)jSONObject.getJSONObject("data").get("departmentId");
/* 272 */           if ("100".equals(str10)) {
/* 273 */             recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str11, str7, str2, "1", str3, mailMasterId });
/*     */           }
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */         
/* 280 */         String str6 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 300 */         if (jSONObject != null && "100".equals(jSONObject.getJSONObject("status").get("statusCode"))) {
/* 301 */           recordSet2.executeUpdate(str6, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "1", jSONObject.getString("operateType"), "1", "", mailMasterId });
/* 302 */           this.newlog.error("分部：(" + str3 + ")，上级分部：" + str4 + "，同步成功！");
/* 303 */         } else if (jSONObject != null) {
/* 304 */           recordSet2.executeUpdate(str6, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "1", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getJSONObject("status").get("statusCode")), mailMasterId });
/* 305 */           this.newlog.error("分部：(" + str3 + ")，上级分部：" + str4 + "，同步失败，code=" + (String)jSONObject.getJSONObject("status").get("statusCode") + "，msg=" + (String)jSONObject.getJSONObject("status").get("statusMsg"));
/*     */         } 
/*     */         
/* 308 */         if (list != null && list.size() > 0) {
/* 309 */           syncSubcompanyByList(list, paramString1, paramUser, paramString2);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private synchronized void syncDepartment(String paramString, User paramUser) {
/* 323 */     List<Map<String, Object>> list = HrmFaceCheckManager.syncBatch("0", HrmFaceCheckManager.getOaDepartment(), true);
/* 324 */     if (list != null && list.size() > 0) {
/* 325 */       syncDepartmentByList(list, paramString, paramUser);
/*     */     }
/*     */   }
/*     */   
/*     */   private void syncDepartmentByList(List<Map<String, Object>> paramList, String paramString, User paramUser) {
/* 330 */     if (paramList != null && paramList.size() > 0) {
/* 331 */       RecordSet recordSet1 = new RecordSet();
/* 332 */       RecordSet recordSet2 = new RecordSet();
/* 333 */       for (Map<String, Object> map : paramList) {
/* 334 */         List<Map<String, Object>> list = (List)map.get("children");
/* 335 */         SimpleRecordData simpleRecordData = (SimpleRecordData)map.get("simpleRecordData");
/* 336 */         String str1 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("canceled")).getFieldValue());
/* 337 */         String str2 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("id")).getFieldValue());
/* 338 */         String str3 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("departmentname")).getFieldValue());
/* 339 */         String str4 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("subcompanyid1")).getFieldValue());
/* 340 */         String str5 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("supdepid")).getFieldValue());
/* 341 */         int i = Util.getIntValue(Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("showorder")).getFieldValue()), 0);
/* 342 */         if (str5 == null || "".equals(str5)) {
/* 343 */           str5 = "0";
/*     */         }
/* 345 */         if (i < 0) {
/* 346 */           i = 0;
/*     */         }
/* 348 */         String str6 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 349 */         recordSet1.executeQuery(str6, new Object[] { str2, mailMasterId });
/* 350 */         boolean bool = recordSet1.next();
/* 351 */         JSONObject jSONObject = new JSONObject();
/* 352 */         if (bool) {
/*     */           
/* 354 */           String str8 = recordSet1.getString("mailid");
/* 355 */           String str9 = recordSet1.getString("mailpid");
/*     */           
/* 357 */           boolean bool1 = false;
/* 358 */           List list1 = ALYMailApi.getAllALYDeptids(paramString, accessTarget);
/* 359 */           if (list1.contains(str8)) {
/* 360 */             bool1 = true;
/*     */           }
/* 362 */           if (bool1) {
/* 363 */             if (!"1".equals(str1)) {
/* 364 */               JSONObject jSONObject1 = new JSONObject();
/* 365 */               String str10 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 366 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 367 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 368 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 369 */               recordSet2.executeQuery(str10, new Object[] { str5, mailMasterId });
/* 370 */               if (recordSet2.next()) {
/* 371 */                 str9 = recordSet2.getString("mailid");
/*     */               } else {
/* 373 */                 str10 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 374 */                 recordSet2.executeQuery(str10, new Object[] { str4, mailMasterId });
/* 375 */                 if (recordSet2.next()) {
/* 376 */                   str9 = recordSet2.getString("mailid");
/*     */                 }
/*     */               } 
/* 379 */               hashMap1.put("accessToken", paramString);
/* 380 */               hashMap1.put("accessTarget", accessTarget);
/* 381 */               hashMap3.put("access", hashMap1);
/* 382 */               hashMap2.put("departmentId", str8);
/* 383 */               hashMap2.put("parentId", str9);
/* 384 */               hashMap2.put("name", str3);
/* 385 */               hashMap3.put("param", hashMap2);
/* 386 */               String str11 = JSON.toJSONString(hashMap3);
/* 387 */               jSONObject = ALYMailApi.updateDepartment(str11);
/* 388 */               jSONObject.put("operateType", "4");
/* 389 */               String str12 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 390 */               if ("100".equals(str12)) {
/* 391 */                 recordSet2.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str3, str9, str8, mailMasterId });
/*     */               }
/*     */             } else {
/*     */               
/* 395 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 396 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 397 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 398 */               hashMap1.put("accessToken", paramString);
/* 399 */               hashMap1.put("accessTarget", accessTarget);
/* 400 */               hashMap3.put("access", hashMap1);
/* 401 */               hashMap2.put("departmentId", str8);
/* 402 */               hashMap3.put("param", hashMap2);
/* 403 */               String str10 = JSON.toJSONString(hashMap3);
/* 404 */               jSONObject = ALYMailApi.deleteDepartment(str10);
/* 405 */               jSONObject.put("operateType", "4");
/* 406 */               String str11 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 407 */               if ("100".equals(str11)) {
/* 408 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str8, mailMasterId });
/*     */               }
/*     */             } 
/*     */           } else {
/*     */             
/* 413 */             String str10 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 414 */             recordSet2.executeQuery(str10, new Object[] { str5, mailMasterId });
/* 415 */             if (recordSet2.next()) {
/* 416 */               str9 = recordSet2.getString("mailid");
/*     */             } else {
/* 418 */               str10 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 419 */               recordSet2.executeQuery(str10, new Object[] { str4, mailMasterId });
/* 420 */               if (recordSet2.next()) {
/* 421 */                 str9 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/* 424 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 425 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 426 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 427 */             hashMap1.put("accessToken", paramString);
/* 428 */             hashMap1.put("accessTarget", accessTarget);
/* 429 */             hashMap3.put("access", hashMap1);
/* 430 */             hashMap2.put("parentId", str9);
/* 431 */             hashMap2.put("name", str3);
/* 432 */             hashMap3.put("param", hashMap2);
/* 433 */             String str11 = JSON.toJSONString(hashMap3);
/* 434 */             jSONObject = ALYMailApi.createDepartment(str11);
/* 435 */             jSONObject.put("operateType", "4");
/* 436 */             String str12 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 437 */             String str13 = (String)jSONObject.getJSONObject("data").get("departmentId");
/* 438 */             if ("100".equals(str12)) {
/* 439 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str13, str9, str2, "2", str3, mailMasterId });
/*     */             
/*     */             }
/*     */           }
/*     */         
/*     */         }
/* 445 */         else if (!"1".equals(str1)) {
/*     */           
/* 447 */           String str8 = "";
/* 448 */           String str9 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 449 */           recordSet2.executeQuery(str9, new Object[] { str5, mailMasterId });
/* 450 */           if (recordSet2.next()) {
/* 451 */             str8 = recordSet2.getString("mailid");
/*     */           } else {
/* 453 */             str9 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 454 */             recordSet2.executeQuery(str9, new Object[] { str4, mailMasterId });
/* 455 */             if (recordSet2.next()) {
/* 456 */               str8 = recordSet2.getString("mailid");
/*     */             }
/*     */           } 
/* 459 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 460 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 461 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 462 */           hashMap1.put("accessToken", paramString);
/* 463 */           hashMap1.put("accessTarget", accessTarget);
/* 464 */           hashMap3.put("access", hashMap1);
/* 465 */           hashMap2.put("parentId", str8);
/* 466 */           hashMap2.put("name", str3);
/* 467 */           hashMap3.put("param", hashMap2);
/* 468 */           String str10 = JSON.toJSONString(hashMap3);
/* 469 */           jSONObject = ALYMailApi.createDepartment(str10);
/* 470 */           jSONObject.put("operateType", "4");
/* 471 */           String str11 = (String)jSONObject.getJSONObject("status").get("statusCode");
/* 472 */           String str12 = (String)jSONObject.getJSONObject("data").get("departmentId");
/* 473 */           if ("100".equals(str11)) {
/* 474 */             recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str12, str8, str2, "2", str3, mailMasterId });
/*     */           }
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */         
/* 481 */         String str7 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 502 */         if (jSONObject != null && "100".equals(jSONObject.getJSONObject("status").get("statusCode"))) {
/* 503 */           recordSet2.executeUpdate(str7, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "2", jSONObject.getString("operateType"), "1", "", mailMasterId });
/* 504 */           this.newlog.error("部门：(" + str3 + ")，上级部门：" + str5 + "，同步成功！");
/* 505 */         } else if (jSONObject != null) {
/* 506 */           recordSet2.executeUpdate(str7, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "2", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getJSONObject("status").get("statusCode")), mailMasterId });
/* 507 */           this.newlog.error("部门：(" + str3 + ")，上级部门：" + str5 + "，同步失败，code=" + (String)jSONObject.getJSONObject("status").get("statusCode") + "，msg=" + (String)jSONObject.getJSONObject("status").get("statusMsg"));
/*     */         } 
/*     */         
/* 510 */         if (list != null && list.size() > 0) {
/* 511 */           syncDepartmentByList(list, paramString, paramUser);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private synchronized void syncUser(String paramString, User paramUser) {
/* 524 */     List<Map<String, Object>> list = HrmFaceCheckManager.syncBatch("0", HrmFaceCheckManager.getOaResource(), true);
/* 525 */     if (list != null && list.size() > 0) {
/* 526 */       syncUserByList(list, paramString, paramUser);
/*     */     }
/*     */   }
/*     */   
/*     */   private void syncUserByList(List<Map<String, Object>> paramList, String paramString, User paramUser) {
/* 531 */     if (paramList != null && paramList.size() > 0) {
/* 532 */       RecordSet recordSet = new RecordSet();
/* 533 */       for (Map<String, Object> map : paramList) {
/* 534 */         List<Map<String, Object>> list = (List)map.get("children");
/* 535 */         SimpleRecordData simpleRecordData = (SimpleRecordData)map.get("simpleRecordData");
/*     */         try {
/* 537 */           String str1 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("id")).getFieldValue());
/* 538 */           String str2 = this.mailDao.getBindFieldValue(bindField, str1 + "");
/* 539 */           String str3 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("loginid")).getFieldValue());
/*     */           
/* 541 */           String str4 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("lastname")).getFieldValue());
/*     */ 
/*     */           
/* 544 */           String str5 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("telephone")).getFieldValue());
/* 545 */           String str6 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("mobile")).getFieldValue());
/* 546 */           String str7 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("email")).getFieldValue());
/* 547 */           String str8 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("departmentid")).getFieldValue());
/* 548 */           String str9 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("status")).getFieldValue());
/*     */           
/* 550 */           String str10 = getRandomPassword(18);
/* 551 */           JSONObject jSONObject = new JSONObject();
/* 552 */           if ("".equals(str2)) {
/* 553 */             this.newlog.error(str4 + "：在OA中没有" + bindField + "字段不同步");
/*     */             
/*     */             continue;
/*     */           } 
/* 557 */           boolean bool = ALYMailApi.checkUser(str2, paramString, accessTarget);
/* 558 */           if (bool) {
/*     */             
/* 560 */             if (str9.equals("0") || str9.equals("1") || str9.equals("2") || str9.equals("3")) {
/* 561 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 562 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */               
/* 564 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 565 */               ArrayList arrayList = new ArrayList();
/* 566 */               hashMap1.put("accessToken", paramString);
/* 567 */               hashMap1.put("accessTarget", str2);
/* 568 */               hashMap3.put("access", hashMap1);
/* 569 */               hashMap2.put("name", str4);
/*     */ 
/*     */ 
/*     */               
/* 573 */               hashMap2.put("departmentId", getALYDeptId(str8));
/* 574 */               hashMap2.put("mobilePhone", str6);
/* 575 */               hashMap2.put("workPhone", str5);
/*     */ 
/*     */               
/* 578 */               hashMap3.put("param", hashMap2);
/* 579 */               String str = JSON.toJSONString(hashMap3);
/* 580 */               jSONObject = ALYMailApi.updateHrmresource(str);
/* 581 */               jSONObject.put("operateType", "4");
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/*     */               continue;
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */ 
/*     */ 
/*     */ 
/*     */           
/*     */           }
/* 603 */           else if (str9.equals("0") || str9.equals("1") || str9.equals("2") || str9.equals("3")) {
/*     */             
/* 605 */             if (str2.indexOf("@") > -1) {
/* 606 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 607 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 608 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 609 */               HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 610 */               ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 611 */               hashMap1.put("accessToken", paramString);
/* 612 */               hashMap1.put("accessTarget", accessTarget);
/* 613 */               hashMap4.put("access", hashMap1);
/* 614 */               hashMap2.put("name", str4);
/* 615 */               hashMap2.put("passwd", str10);
/* 616 */               hashMap2.put("email", str2);
/*     */               
/* 618 */               hashMap2.put("departmentId", getALYDeptId(str8));
/* 619 */               hashMap2.put("mobilePhone", str6);
/* 620 */               hashMap2.put("workPhone", str5);
/* 621 */               arrayList.add(hashMap2);
/* 622 */               hashMap3.put("accounts", arrayList);
/* 623 */               hashMap4.put("param", hashMap3);
/* 624 */               String str = JSON.toJSONString(hashMap4);
/* 625 */               jSONObject = ALYMailApi.createHrmresource(str);
/* 626 */               jSONObject.put("operateType", "4");
/* 627 */               if (jSONObject != null && "100".equals(jSONObject.getJSONObject("status").get("statusCode"))) {
/*     */                 
/* 629 */                 String str12 = "INSERT INTO MailDefaultPwd\n        ( userid ,\n          loginid ,\n          lastname ,\n          defaultpwd ,\n          createtime,\n          mailmasterid\n        )\nVALUES  ( ? ,\n          ? ,\n          ? ,\n          ? ,\n          ? ,\n          ?  \n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 644 */                 recordSet.executeUpdate(str12, new Object[] { str1, str3, str4, str10, TimeUtil.getCurrentTimeString(), mailMasterId });
/*     */               } 
/*     */             } else {
/* 647 */               this.newlog.error("人员：ID=" + str1 + "，loginid=" + str3 + "，" + bindField + "=" + str2 + "，所属部门：" + str8 + "，邮箱地址错误！");
/*     */               
/*     */               continue;
/*     */             } 
/*     */           } else {
/*     */             continue;
/*     */           } 
/*     */           
/* 655 */           String str11 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 675 */           if (jSONObject != null && "100".equals(jSONObject.getJSONObject("status").get("statusCode"))) {
/* 676 */             recordSet.executeUpdate(str11, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "3", jSONObject.getString("operateType"), "1", "", mailMasterId });
/* 677 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str3 + "，" + bindField + "=" + str2 + "，所属部门：" + str8 + "，同步成功！");
/* 678 */           } else if (jSONObject != null) {
/* 679 */             recordSet.executeUpdate(str11, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "3", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getJSONObject("status").get("statusCode")), mailMasterId });
/* 680 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str3 + "，" + bindField + "=" + str2 + "，所属部门：" + str8 + "，同步失败，code=" + (String)jSONObject.getJSONObject("status").get("statusCode") + "，msg=" + (String)jSONObject.getJSONObject("status").get("statusMsg"));
/*     */           } 
/* 682 */         } catch (Exception exception) {
/* 683 */           this.newlog.error("阿里云邮箱初始化人员时异常！", exception);
/*     */         } 
/*     */         
/* 686 */         if (list != null && list.size() > 0) {
/* 687 */           syncUserByList(list, paramString, paramUser);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private String getALYDeptId(String paramString) {
/* 694 */     RecordSet recordSet = new RecordSet();
/* 695 */     recordSet.executeQuery("SELECT mailid FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?", new Object[] { paramString, mailMasterId });
/* 696 */     if (recordSet.next()) {
/* 697 */       return recordSet.getString("mailid");
/*     */     }
/* 699 */     return "1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getPsw(int paramInt) {
/* 746 */     String str1 = null;
/*     */     
/* 748 */     String str2 = "0123456789abcdefghijklABCDEFGHIJKL!@#$";
/* 749 */     boolean bool = false;
/*     */     
/* 751 */     while (!bool) {
/*     */       
/* 753 */       str1 = "";
/*     */       
/* 755 */       byte b1 = 0, b2 = 0, b3 = 0, b4 = 0;
/* 756 */       for (byte b5 = 0; b5 < paramInt; b5++) {
/* 757 */         int i = (int)(Math.random() * str2.length());
/* 758 */         str1 = str1 + str2.charAt(i);
/* 759 */         if (0 <= i && i <= 9) {
/* 760 */           b1++;
/*     */         }
/* 762 */         if (10 <= i && i <= 35) {
/* 763 */           b2++;
/*     */         }
/* 765 */         if (36 <= i && i <= 61) {
/* 766 */           b3++;
/*     */         }
/* 768 */         if (62 <= i && i < str2.length()) {
/* 769 */           b4++;
/*     */         }
/*     */       } 
/*     */       
/* 773 */       bool = ((b1 * b2 * b3 != 0 && b4 == 0) || (b1 * b2 * b4 != 0 && b3 == 0) || (b1 * b3 * b4 != 0 && b2 == 0) || (b2 * b3 * b4 != 0 && b1 == 0)) ? true : false;
/*     */     } 
/* 775 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRandomPassword(int paramInt) {
/* 783 */     char[] arrayOfChar = new char[paramInt];
/* 784 */     byte b = 0;
/* 785 */     while (b < paramInt) {
/* 786 */       int i = (int)(Math.random() * 3.0D);
/* 787 */       if (i == 0) {
/* 788 */         arrayOfChar[b] = (char)(int)(65.0D + Math.random() * 26.0D);
/* 789 */       } else if (i == 1) {
/* 790 */         arrayOfChar[b] = (char)(int)(97.0D + Math.random() * 26.0D);
/*     */       } else {
/* 792 */         arrayOfChar[b] = (char)(int)(48.0D + Math.random() * 10.0D);
/* 793 */       }  b++;
/*     */     } 
/* 795 */     return new String(arrayOfChar);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 802 */     System.out.println(getPsw(10));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/alymail/api/ALYMailSynApi.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */