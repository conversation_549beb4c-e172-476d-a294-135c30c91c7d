/*     */ package weaver.integration.thirdsdk.alymail.api;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.util.HttpsUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ALYMailApi
/*     */ {
/*  20 */   private static Logger newlog = LoggerFactory.getLogger(ALYMailApi.class);
/*     */   
/*  22 */   private static String ALMAIL_URL = "https://alimailws.alibaba-inc.com/alimailws";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isOpenALYMail() {
/*  32 */     RecordSet recordSet = new RecordSet();
/*  33 */     recordSet.executeQuery("SELECT * FROM ALYMailSetting WHERE isuse='1'", new Object[0]);
/*  34 */     if (recordSet.next()) {
/*  35 */       return true;
/*     */     }
/*  37 */     return false;
/*     */   }
/*     */   
/*     */   public static String getRootDepartmentId(String paramString1, String paramString2) {
/*  41 */     String str1 = ALMAIL_URL + "/ud/getDomainInfo";
/*  42 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  43 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  44 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  45 */     hashMap1.put("accessToken", paramString1);
/*  46 */     hashMap1.put("accessTarget", paramString2);
/*  47 */     hashMap3.put("access", hashMap1);
/*  48 */     hashMap2.put("fields", new ArrayList());
/*  49 */     hashMap3.put("param", hashMap2);
/*  50 */     String str2 = JSON.toJSONString(hashMap3);
/*  51 */     String str3 = "";
/*     */     try {
/*  53 */       String str4 = HttpsUtil.httpsRequest(str1, "POST", str2);
/*  54 */       newlog.error("获取顶级得到的结果为：" + str4);
/*  55 */       JSONObject jSONObject = JSONObject.parseObject(str4);
/*  56 */       String str5 = (String)jSONObject.getJSONObject("status").get("statusCode");
/*  57 */       if ("100".equals(str5)) {
/*  58 */         str3 = (String)jSONObject.getJSONObject("data").get("rootDepartmentId");
/*     */       }
/*  60 */     } catch (Exception exception) {
/*  61 */       newlog.error("获取顶级部门id出错，", exception);
/*     */     } 
/*  63 */     return str3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject getToken(String paramString1, String paramString2) {
/*  71 */     String str1 = ALMAIL_URL + "/control/wsLogin";
/*  72 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  73 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  74 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  75 */     hashMap1.put("accessToken", "");
/*  76 */     hashMap1.put("accessTarget", "");
/*  77 */     hashMap3.put("access", hashMap1);
/*  78 */     hashMap2.put("accessCode", paramString1);
/*  79 */     hashMap2.put("accessPassword", paramString2);
/*  80 */     hashMap3.put("param", hashMap2);
/*  81 */     String str2 = JSON.toJSONString(hashMap3);
/*  82 */     String str3 = HttpsUtil.httpsRequest(str1, "POST", str2);
/*  83 */     newlog.error("获取token的url得到的结果为：" + str3);
/*  84 */     return JSONObject.parseObject(str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject getDomain(String paramString1, String paramString2) {
/*  92 */     String str1 = ALMAIL_URL + "/ud/getDomainInfo";
/*  93 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  94 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  95 */     ArrayList arrayList = new ArrayList();
/*  96 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  97 */     hashMap1.put("accessToken", paramString1);
/*  98 */     hashMap1.put("accessTarget", paramString2);
/*  99 */     hashMap3.put("access", hashMap1);
/* 100 */     hashMap2.put("fields", arrayList);
/* 101 */     hashMap3.put("param", hashMap2);
/* 102 */     String str2 = JSON.toJSONString(hashMap3);
/* 103 */     String str3 = HttpsUtil.httpsRequest(str1, "POST", str2);
/* 104 */     newlog.error("获取域名的url得到的结果为：" + str3);
/* 105 */     return JSONObject.parseObject(str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List getAllALYDeptids(String paramString1, String paramString2) {
/* 118 */     ArrayList<String> arrayList = new ArrayList();
/* 119 */     String str = ALMAIL_URL + "/ud/getDepartmentList";
/* 120 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 121 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 122 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 123 */     hashMap1.put("accessToken", paramString1);
/* 124 */     hashMap1.put("accessTarget", paramString2);
/* 125 */     hashMap3.put("access", hashMap1);
/* 126 */     hashMap3.put("param", hashMap2);
/*     */     try {
/* 128 */       String str1 = JSON.toJSONString(hashMap3);
/* 129 */       String str2 = HttpsUtil.httpsRequest(str, "POST", str1);
/* 130 */       newlog.error("获取阿里云邮箱的部门列表结果：" + str2);
/* 131 */       JSONObject jSONObject1 = JSONObject.parseObject(str2);
/* 132 */       JSONObject jSONObject2 = jSONObject1.getJSONObject("data");
/* 133 */       JSONArray jSONArray = jSONObject2.getJSONArray("dataList");
/* 134 */       for (byte b = 0; b < jSONArray.size(); b++) {
/* 135 */         JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 136 */         String str3 = jSONObject.getString("departmentId");
/* 137 */         arrayList.add(str3);
/*     */       } 
/* 139 */     } catch (Exception exception) {
/* 140 */       newlog.error("获取部门id列表出错", exception);
/*     */     } 
/* 142 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject createDepartment(String paramString) {
/* 152 */     newlog.error("创建部门传参：" + paramString);
/* 153 */     String str1 = ALMAIL_URL + "/ud/createDepartment";
/* 154 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString);
/* 155 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 156 */     newlog.error("创建部门返回数据：" + str2);
/* 157 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateDepartment(String paramString) {
/* 168 */     newlog.error("更新部门传参：" + paramString);
/* 169 */     String str1 = ALMAIL_URL + "/ud/updateDepartment";
/* 170 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString);
/* 171 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 172 */     newlog.error("更新部门返回数据：" + str2);
/* 173 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject deleteDepartment(String paramString) {
/* 184 */     newlog.error("删除部门传参：" + paramString);
/* 185 */     String str1 = ALMAIL_URL + "/ud/removeDepartment";
/* 186 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString);
/* 187 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 188 */     newlog.error("删除部门返回数据：" + str2);
/* 189 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean checkUser(String paramString1, String paramString2, String paramString3) {
/* 201 */     String str = ALMAIL_URL + "/ud/getAccountsInfo";
/* 202 */     boolean bool = false;
/* 203 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 204 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 205 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 206 */     hashMap1.put("accessToken", paramString2);
/* 207 */     hashMap1.put("accessTarget", paramString3);
/* 208 */     hashMap3.put("access", hashMap1);
/* 209 */     ArrayList<String> arrayList = new ArrayList();
/* 210 */     ArrayList arrayList1 = new ArrayList();
/* 211 */     arrayList.add(paramString1);
/* 212 */     hashMap2.put("emails", arrayList);
/* 213 */     hashMap2.put("fields", arrayList1);
/* 214 */     hashMap3.put("param", hashMap2);
/*     */     try {
/* 216 */       String str1 = JSON.toJSONString(hashMap3);
/* 217 */       newlog.error("获取用户是否存在的传参===" + str1);
/* 218 */       String str2 = HttpsUtil.httpsRequest(str, "POST", str1);
/* 219 */       newlog.error("获取用户是否存在的结果===" + str2);
/* 220 */       JSONObject jSONObject1 = JSONObject.parseObject(str2);
/* 221 */       String str3 = jSONObject1.getJSONObject("status").getString("statusCode");
/* 222 */       JSONObject jSONObject2 = jSONObject1.getJSONObject("data");
/* 223 */       JSONArray jSONArray = jSONObject2.getJSONArray("dataList");
/*     */       
/* 225 */       if (jSONArray.size() > 0) {
/* 226 */         bool = true;
/*     */       }
/* 228 */     } catch (Exception exception) {
/* 229 */       newlog.error("判断人员是否存在出错", exception);
/*     */     } 
/* 231 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject createHrmresource(String paramString) {
/* 241 */     newlog.error("创建人员传参：" + paramString);
/* 242 */     String str1 = ALMAIL_URL + "/ud/createAccounts";
/* 243 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString);
/* 244 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 245 */     newlog.error("创建人员返回数据：" + str2);
/* 246 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateHrmresource(String paramString) {
/* 257 */     newlog.error("更新人员传参：" + paramString);
/* 258 */     String str1 = ALMAIL_URL + "/ud/updateAccountInfo";
/* 259 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString);
/* 260 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 261 */     newlog.error("更新人员返回数据：" + str2);
/* 262 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject deleteHrmresource(String paramString) {
/* 273 */     newlog.error("冻结人员传参：" + paramString);
/* 274 */     String str1 = ALMAIL_URL + "/ud/updateAccountsStatus";
/* 275 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString);
/* 276 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 277 */     newlog.error("冻结人员返回数据：" + str2);
/* 278 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject changeUserPsw(String paramString) {
/* 284 */     newlog.error("更改人员密码传参：" + paramString);
/* 285 */     String str1 = ALMAIL_URL + "/ud/updateAccountsPassword";
/* 286 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString);
/* 287 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 288 */     newlog.error("更改人员密码返回数据：" + str2);
/* 289 */     return jSONObject;
/*     */   }
/*     */ 
/*     */   
/*     */   public static JSONObject getMailUnread(String paramString1, String paramString2, String paramString3) {
/* 294 */     String str1 = ALMAIL_URL + "/mbox/queryMailListByChats";
/* 295 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 296 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 297 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 298 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 299 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 300 */     HashMap<Object, Object> hashMap6 = new HashMap<>();
/* 301 */     ArrayList<String> arrayList = new ArrayList();
/* 302 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 303 */     hashMap2.put("accessToken", paramString2);
/* 304 */     hashMap2.put("accessTarget", paramString3);
/* 305 */     hashMap1.put("access", hashMap2);
/* 306 */     hashMap3.put("folderId", "");
/* 307 */     hashMap3.put("offset", "0");
/* 308 */     arrayList.add("tag");
/* 309 */     arrayList.add("from");
/* 310 */     arrayList.add("to");
/* 311 */     arrayList.add("date");
/* 312 */     arrayList.add("subject");
/* 313 */     arrayList.add("priority");
/* 314 */     arrayList.add("hasAttachment");
/* 315 */     arrayList.add("mailId");
/* 316 */     arrayList.add("status");
/* 317 */     arrayList.add("read");
/* 318 */     arrayList.add("extend");
/* 319 */     arrayList.add("mailSize");
/* 320 */     hashMap3.put("fields", arrayList);
/* 321 */     hashMap4.put("field", "subject");
/* 322 */     hashMap4.put("order", "[asc]");
/* 323 */     hashMap5.put("field", "from");
/* 324 */     hashMap5.put("order", "[asc]");
/* 325 */     hashMap6.put("field", "date");
/* 326 */     hashMap6.put("order", "[asc]");
/* 327 */     arrayList1.add(hashMap4);
/* 328 */     arrayList1.add(hashMap5);
/* 329 */     arrayList1.add(hashMap6);
/* 330 */     hashMap3.put("sortFields", arrayList1);
/* 331 */     hashMap3.put("emailAddress", paramString1);
/* 332 */     hashMap3.put("condition", "[unread]");
/* 333 */     hashMap3.put("length", "199");
/* 334 */     hashMap1.put("param", hashMap3);
/* 335 */     String str2 = JSON.toJSONString(hashMap1);
/* 336 */     String str3 = HttpsUtil.httpsRequest(str1, "POST", str2);
/* 337 */     return JSONObject.parseObject(str3);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/alymail/api/ALYMailApi.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */