/*     */ package weaver.integration.thirdsdk.alymail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.thirdsdk.alymail.api.ALYMailApi;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmResourceSubscriber4ALY
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  27 */     List<Map<String, Object>> list = getAccessToken();
/*  28 */     if (list != null && list.size() > 0 && paramList != null) {
/*  29 */       String str1 = "";
/*  30 */       String str2 = "";
/*  31 */       String str3 = "";
/*  32 */       String str4 = "";
/*  33 */       String str5 = "";
/*  34 */       String str6 = "";
/*  35 */       String str7 = "";
/*  36 */       String str8 = "";
/*  37 */       String str9 = "";
/*  38 */       String str10 = getRandomPassword(18);
/*  39 */       MailDao mailDao = new MailDao();
/*  40 */       for (FieldData fieldData : paramList) {
/*  41 */         Object object = fieldData.getFieldValue();
/*  42 */         String str = fieldData.getFieldName();
/*     */         
/*  44 */         if ("loginid".equalsIgnoreCase(str)) {
/*  45 */           str2 = object.toString(); continue;
/*  46 */         }  if ("id".equalsIgnoreCase(str)) {
/*  47 */           str1 = object.toString(); continue;
/*  48 */         }  if ("lastname".equalsIgnoreCase(str)) {
/*  49 */           str3 = object.toString(); continue;
/*  50 */         }  if ("telephone".equalsIgnoreCase(str)) {
/*  51 */           str4 = object.toString(); continue;
/*  52 */         }  if ("mobile".equalsIgnoreCase(str)) {
/*  53 */           str9 = object.toString(); continue;
/*  54 */         }  if ("email".equalsIgnoreCase(str)) {
/*  55 */           str6 = object.toString(); continue;
/*  56 */         }  if ("departmentid".equalsIgnoreCase(str)) {
/*  57 */           str7 = object.toString(); continue;
/*  58 */         }  if ("status".equalsIgnoreCase(str)) {
/*  59 */           str8 = object.toString(); continue;
/*  60 */         }  if ("workcode".equalsIgnoreCase(str)) {
/*  61 */           str5 = object.toString();
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/*  66 */       for (Map<String, Object> map : list) {
/*  67 */         RecordSet recordSet1 = new RecordSet();
/*  68 */         RecordSet recordSet2 = new RecordSet();
/*  69 */         String str11 = Util.null2String(map.get("id"));
/*  70 */         String str12 = Util.null2String(map.get("accessToken"));
/*  71 */         String str13 = Util.null2String(map.get("accessTarget"));
/*  72 */         String str14 = Util.null2String(map.get("bindField"));
/*  73 */         String str15 = mailDao.getBindFieldValue(str14, str1);
/*  74 */         if ("".equals(str15)) {
/*  75 */           return null;
/*     */         }
/*  77 */         String str16 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  97 */         boolean bool = ALYMailApi.checkUser(str15, str12, str13);
/*     */         
/*  99 */         JSONObject jSONObject = new JSONObject();
/* 100 */         if (bool) {
/*     */           
/* 102 */           if (str8.equals("0") || str8.equals("1") || str8.equals("2") || str8.equals("3")) {
/* 103 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 104 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */             
/* 106 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 107 */             ArrayList arrayList = new ArrayList();
/* 108 */             hashMap1.put("accessToken", str12);
/* 109 */             hashMap1.put("accessTarget", str15);
/* 110 */             hashMap3.put("access", hashMap1);
/* 111 */             hashMap2.put("name", str3);
/*     */ 
/*     */ 
/*     */             
/* 115 */             hashMap2.put("departmentId", getALYDeptId(str7, str11));
/* 116 */             hashMap2.put("workPhone", str4);
/* 117 */             hashMap2.put("mobilePhone", str9);
/* 118 */             hashMap2.put("employeeNo", str5);
/*     */ 
/*     */             
/* 121 */             hashMap3.put("param", hashMap2);
/* 122 */             String str17 = JSON.toJSONString(hashMap3);
/*     */             
/* 124 */             jSONObject = ALYMailApi.updateHrmresource(str17);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 147 */             HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 148 */             HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 149 */             HashMap<Object, Object> hashMap6 = new HashMap<>();
/* 150 */             HashMap<Object, Object> hashMap7 = new HashMap<>();
/* 151 */             ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 152 */             hashMap4.put("accessToken", str12);
/* 153 */             hashMap4.put("accessTarget", str13);
/* 154 */             hashMap7.put("access", hashMap4);
/* 155 */             hashMap5.put("email", str15);
/* 156 */             hashMap5.put("status", "0");
/* 157 */             arrayList1.add(hashMap5);
/* 158 */             hashMap6.put("accounts", arrayList1);
/* 159 */             hashMap7.put("param", hashMap6);
/* 160 */             String str18 = JSON.toJSONString(hashMap7);
/* 161 */             jSONObject = ALYMailApi.deleteHrmresource(str18);
/* 162 */             jSONObject.put("operateType", "2");
/*     */           } else {
/*     */             
/* 165 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 166 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 167 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 168 */             HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 169 */             ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 170 */             hashMap1.put("accessToken", str12);
/* 171 */             hashMap1.put("accessTarget", str13);
/* 172 */             hashMap4.put("access", hashMap1);
/* 173 */             hashMap2.put("email", str15);
/* 174 */             hashMap2.put("status", "1");
/* 175 */             arrayList.add(hashMap2);
/* 176 */             hashMap3.put("accounts", arrayList);
/* 177 */             hashMap4.put("param", hashMap3);
/* 178 */             String str = JSON.toJSONString(hashMap4);
/* 179 */             jSONObject = ALYMailApi.deleteHrmresource(str);
/* 180 */             jSONObject.put("operateType", "5");
/*     */           }
/*     */         
/* 183 */         } else if (str8.equals("0") || str8.equals("1") || str8.equals("2") || str8.equals("3")) {
/*     */           
/* 185 */           if (str15.indexOf("@") > -1) {
/* 186 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 187 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 188 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 189 */             HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 190 */             ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 191 */             hashMap1.put("accessToken", str12);
/* 192 */             hashMap1.put("accessTarget", str13);
/* 193 */             hashMap4.put("access", hashMap1);
/* 194 */             hashMap2.put("name", str3);
/*     */ 
/*     */ 
/*     */             
/* 198 */             hashMap2.put("passwd", str10);
/* 199 */             hashMap2.put("email", str15);
/*     */             
/* 201 */             hashMap2.put("departmentId", getALYDeptId(str7, str11));
/* 202 */             hashMap2.put("workPhone", str4);
/* 203 */             hashMap2.put("mobilePhone", str9);
/* 204 */             hashMap2.put("employeeNo", str5);
/* 205 */             arrayList.add(hashMap2);
/* 206 */             hashMap3.put("accounts", arrayList);
/* 207 */             hashMap4.put("param", hashMap3);
/* 208 */             String str = JSON.toJSONString(hashMap4);
/* 209 */             jSONObject = ALYMailApi.createHrmresource(str);
/*     */             
/* 211 */             if (jSONObject != null && "100".equals(Util.null2String(jSONObject.getJSONObject("status").get("statusCode")))) {
/*     */               
/* 213 */               String str17 = "INSERT INTO MailDefaultPwd\n        ( userid ,\n          loginid ,\n          lastname ,\n          defaultpwd ,\n          createtime,\n          mailmasterid\n        )\nVALUES  ( ? ,\n          ? ,\n          ? ,\n          ? ,\n          ? ,\n          ?  \n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 228 */               recordSet2.executeUpdate(str17, new Object[] { str1, str2, str3, str10, TimeUtil.getCurrentTimeString(), str11 });
/*     */             } 
/*     */             
/* 231 */             jSONObject.put("operateType", "1");
/*     */           } else {
/* 233 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，" + str14 + "=" + str15 + "，所属部门：" + str7 + "，邮箱地址错误！");
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 239 */         if (jSONObject != null && "100".equals(Util.null2String(jSONObject.getJSONObject("status").get("statusCode")))) {
/* 240 */           recordSet2.executeUpdate(str16, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "1", "", str11 });
/* 241 */           this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，" + str14 + "=" + str15 + "，所属部门：" + str7 + "，同步成功！"); continue;
/* 242 */         }  if (jSONObject != null) {
/* 243 */           recordSet2.executeUpdate(str16, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(Util.null2String(jSONObject.getJSONObject("status").get("statusCode"))), str11 });
/* 244 */           this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，" + str14 + "=" + str15 + "，所属部门：" + str7 + "，同步失败，code=" + Util.null2String(jSONObject.getJSONObject("status").get("statusCode")) + "，msg=" + Util.null2String(jSONObject.getJSONObject("status").get("statusMsg")));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 249 */     return null;
/*     */   }
/*     */   
/* 252 */   private Logger newlog = LoggerFactory.getLogger(HrmResourceSubscriber4ALY.class);
/*     */   
/*     */   private String getALYDeptId(String paramString1, String paramString2) {
/* 255 */     RecordSet recordSet = new RecordSet();
/* 256 */     recordSet.executeQuery("SELECT mailid FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?", new Object[] { paramString1, paramString2 });
/* 257 */     if (recordSet.next()) {
/* 258 */       return recordSet.getString("mailid");
/*     */     }
/* 260 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getAccessToken() {
/* 266 */     RecordSet recordSet = new RecordSet();
/* 267 */     MailDao mailDao = new MailDao();
/* 268 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 269 */     recordSet.executeQuery("select * from mail_master where mailtype = 1", new Object[0]);
/* 270 */     while (recordSet.next()) {
/* 271 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 272 */       String str2 = Util.null2String(recordSet.getString("isuse"));
/* 273 */       String str3 = Util.null2String(recordSet.getString("issync"));
/*     */       
/* 275 */       if (!"1".equals(str2)) {
/*     */         continue;
/*     */       }
/* 278 */       if (!"1".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/* 282 */       Map map1 = mailDao.getMailMaster(str1);
/* 283 */       Map map2 = mailDao.getMailDetail(str1);
/* 284 */       JSONObject jSONObject1 = ALYMailApi.getToken(Util.null2String(map2.get("alcode")), Util.null2String(map2.get("alpassword")));
/* 285 */       JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/* 286 */       JSONObject jSONObject3 = jSONObject1.getJSONObject("data");
/* 287 */       if (jSONObject3.containsKey("accessToken") && "100".equals(jSONObject2.getString("statusCode"))) {
/*     */         
/* 289 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 290 */         String str = jSONObject3.getString("accessToken");
/* 291 */         hashMap.put("accessToken", str);
/* 292 */         hashMap.put("accessTarget", Util.null2String(map2.get("aldomain")));
/*     */         
/* 294 */         hashMap.put("bindField", Util.null2String(map1.get("bindfield")));
/*     */         
/* 296 */         hashMap.put("id", str1);
/* 297 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 300 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public boolean synHrmresourcePwd(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 304 */     if ("".equals(paramString3)) {
/* 305 */       return false;
/*     */     }
/* 307 */     boolean bool = false;
/*     */     try {
/* 309 */       JSONObject jSONObject = new JSONObject();
/* 310 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 311 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 312 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 313 */       HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 314 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 315 */       hashMap1.put("accessToken", paramString2);
/* 316 */       hashMap1.put("accessTarget", paramString5);
/* 317 */       hashMap4.put("access", hashMap1);
/* 318 */       hashMap2.put("password", paramString4);
/* 319 */       hashMap2.put("email ", paramString3);
/* 320 */       arrayList.add(hashMap2);
/* 321 */       hashMap3.put("accounts", arrayList);
/* 322 */       hashMap4.put("param", hashMap3);
/* 323 */       String str = JSON.toJSONString(hashMap4);
/* 324 */       jSONObject = ALYMailApi.changeUserPsw(str);
/* 325 */       if (jSONObject != null && "100".equals(Util.null2String(jSONObject.getJSONObject("status").get("statusCode")))) {
/* 326 */         bool = true;
/* 327 */         this.newlog.error("人员：ID=" + paramString1 + "，email=" + paramString3 + "，同步成功！");
/* 328 */       } else if (jSONObject != null) {
/* 329 */         this.newlog.error("人员：ID=" + paramString1 + "，email=" + paramString3 + "，同步失败，code=" + Util.null2String(jSONObject.getJSONObject("status").get("statusCode")) + "，msg=" + Util.null2String(jSONObject.getJSONObject("status").get("statusMsg")));
/*     */       } 
/* 331 */     } catch (Exception exception) {
/* 332 */       this.newlog.error("阿里云邮箱修改人员密码时异常！", exception);
/*     */     } 
/* 334 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getPsw(int paramInt) {
/* 344 */     String str1 = null;
/*     */     
/* 346 */     String str2 = "0123456789abcdefghijklABCDEFGHIJKL!@#$";
/* 347 */     boolean bool = false;
/*     */     
/* 349 */     while (!bool) {
/*     */       
/* 351 */       str1 = "";
/*     */       
/* 353 */       byte b1 = 0, b2 = 0, b3 = 0, b4 = 0;
/* 354 */       for (byte b5 = 0; b5 < paramInt; b5++) {
/* 355 */         int i = (int)(Math.random() * str2.length());
/* 356 */         str1 = str1 + str2.charAt(i);
/* 357 */         if (0 <= i && i <= 9) {
/* 358 */           b1++;
/*     */         }
/* 360 */         if (10 <= i && i <= 35) {
/* 361 */           b2++;
/*     */         }
/* 363 */         if (36 <= i && i <= 61) {
/* 364 */           b3++;
/*     */         }
/* 366 */         if (62 <= i && i < str2.length()) {
/* 367 */           b4++;
/*     */         }
/*     */       } 
/*     */       
/* 371 */       bool = ((b1 * b2 * b3 != 0 && b4 == 0) || (b1 * b2 * b4 != 0 && b3 == 0) || (b1 * b3 * b4 != 0 && b2 == 0) || (b2 * b3 * b4 != 0 && b1 == 0)) ? true : false;
/*     */     } 
/* 373 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRandomPassword(int paramInt) {
/* 382 */     char[] arrayOfChar = new char[paramInt];
/* 383 */     byte b = 0;
/* 384 */     while (b < paramInt) {
/* 385 */       int i = (int)(Math.random() * 3.0D);
/* 386 */       if (i == 0) {
/* 387 */         arrayOfChar[b] = (char)(int)(65.0D + Math.random() * 26.0D);
/* 388 */       } else if (i == 1) {
/* 389 */         arrayOfChar[b] = (char)(int)(97.0D + Math.random() * 26.0D);
/*     */       } else {
/* 391 */         arrayOfChar[b] = (char)(int)(48.0D + Math.random() * 10.0D);
/* 392 */       }  b++;
/*     */     } 
/* 394 */     return new String(arrayOfChar);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/alymail/biz/HrmResourceSubscriber4ALY.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */