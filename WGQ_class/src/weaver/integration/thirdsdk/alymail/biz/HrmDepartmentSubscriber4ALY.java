/*     */ package weaver.integration.thirdsdk.alymail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.thirdsdk.alymail.api.ALYMailApi;
/*     */ import weaver.integration.thirdsdk.alymail.util.ALYMailParam;
/*     */ import weaver.integration.thirdsdk.mailutil.MailRealFieldAndValue;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmDepartmentSubscriber4ALY
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  27 */     List<Map<String, Object>> list = getAccessToken();
/*  28 */     if (list != null && list.size() > 0 && paramList != null) {
/*     */       
/*  30 */       Map<String, String> map = MailRealFieldAndValue.getFieldAndValue(paramList);
/*     */       
/*  32 */       String str = Util.null2String(map.get("id"));
/*     */       
/*  34 */       for (Map<String, Object> map1 : list) {
/*  35 */         RecordSet recordSet1 = new RecordSet();
/*  36 */         RecordSet recordSet2 = new RecordSet();
/*  37 */         String str1 = Util.null2String(map1.get("id"));
/*  38 */         String str2 = Util.null2String(map1.get("accessToken"));
/*  39 */         String str3 = Util.null2String(map1.get("accessTarget"));
/*     */ 
/*     */         
/*  42 */         String str4 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  61 */         String str5 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/*  62 */         recordSet1.executeQuery(str5, new Object[] { str, str1 });
/*  63 */         boolean bool = recordSet1.next();
/*  64 */         JSONObject jSONObject = new JSONObject();
/*  65 */         if (bool) {
/*     */           
/*  67 */           String str6 = recordSet1.getString("mailid");
/*  68 */           String str7 = recordSet1.getString("mailpid");
/*     */           
/*  70 */           boolean bool1 = false;
/*  71 */           List list1 = ALYMailApi.getAllALYDeptids(str2, str3);
/*  72 */           if (list1.contains(str6)) {
/*  73 */             bool1 = true;
/*     */           }
/*  75 */           if (bool1) {
/*  76 */             if ("delete".equalsIgnoreCase(paramString)) {
/*  77 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  78 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  79 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  80 */               hashMap1.put("accessToken", str2);
/*  81 */               hashMap1.put("accessTarget", str3);
/*  82 */               hashMap3.put("access", hashMap1);
/*  83 */               hashMap2.put("departmentId", str6);
/*  84 */               hashMap3.put("param", hashMap2);
/*  85 */               String str8 = JSON.toJSONString(hashMap3);
/*  86 */               jSONObject = ALYMailApi.deleteDepartment(str8);
/*  87 */               jSONObject.put("operateType", "3");
/*  88 */               String str9 = (String)jSONObject.getJSONObject("status").get("statusCode");
/*  89 */               if ("100".equals(str9)) {
/*  90 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str6, str1 });
/*     */               }
/*     */             }
/*  93 */             else if (!"1".equals(Util.null2String(map.get("canceled")))) {
/*  94 */               JSONObject jSONObject1 = new JSONObject();
/*  95 */               String str8 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/*     */               
/*  97 */               recordSet2.executeQuery(str8, new Object[] { Util.null2String(map.get("supdepid")), str1 });
/*  98 */               if (recordSet2.next()) {
/*  99 */                 str7 = recordSet2.getString("mailid");
/*     */               } else {
/* 101 */                 str8 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? and mailmasterid=?";
/* 102 */                 recordSet2.executeQuery(str8, new Object[] { Util.null2String(map.get("subcompanyid1")), str1 });
/*     */                 
/* 104 */                 if (recordSet2.next()) {
/* 105 */                   str7 = recordSet2.getString("mailid");
/*     */                 }
/*     */               } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 118 */               map.put("departmentId", str6);
/* 119 */               map.put("parentId", str7);
/* 120 */               String str9 = ALYMailParam.getALMailSignParam(map, str2, str3);
/* 121 */               jSONObject = ALYMailApi.updateDepartment(str9);
/* 122 */               jSONObject.put("operateType", "2");
/* 123 */               String str10 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 124 */               if ("100".equals(str10)) {
/* 125 */                 recordSet2.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { Util.null2String(map.get("name")), str7, str6, str1 });
/*     */               }
/*     */             } else {
/*     */               
/* 129 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 130 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 131 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 132 */               hashMap1.put("accessToken", str2);
/* 133 */               hashMap1.put("accessTarget", str3);
/* 134 */               hashMap3.put("access", hashMap1);
/* 135 */               hashMap2.put("departmentId", str6);
/* 136 */               hashMap3.put("param", hashMap2);
/* 137 */               String str8 = JSON.toJSONString(hashMap3);
/* 138 */               jSONObject = ALYMailApi.deleteDepartment(str8);
/* 139 */               jSONObject.put("operateType", "3");
/* 140 */               String str9 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 141 */               if ("100".equals(str9)) {
/* 142 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str6, str1 });
/*     */               }
/*     */             } 
/*     */           } else {
/* 146 */             if ("delete".equalsIgnoreCase(paramString)) {
/* 147 */               return null;
/*     */             }
/*     */             
/* 150 */             String str8 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/* 151 */             recordSet2.executeQuery(str8, new Object[] { Util.null2String(map.get("supdepid")), str1 });
/* 152 */             if (recordSet2.next()) {
/* 153 */               str7 = recordSet2.getString("mailid");
/*     */             } else {
/* 155 */               str8 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? and mailmasterid=?";
/* 156 */               recordSet2.executeQuery(str8, new Object[] { Util.null2String(map.get("subcompanyid1")), str1 });
/* 157 */               if (recordSet2.next()) {
/* 158 */                 str7 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 171 */             map.put("parentId", str7);
/* 172 */             String str9 = ALYMailParam.getALMailSignParam(map, str2, str3);
/* 173 */             jSONObject = ALYMailApi.createDepartment(str9);
/* 174 */             jSONObject.put("operateType", "1");
/* 175 */             String str10 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 176 */             String str11 = Util.null2String(jSONObject.getJSONObject("data").get("departmentId"));
/* 177 */             if ("100".equals(str10)) {
/* 178 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str11, str7, str, "2", Util.null2String(map.get("name")), str1 });
/*     */             }
/*     */           } 
/*     */         } else {
/*     */           
/* 183 */           if ("delete".equalsIgnoreCase(paramString)) {
/* 184 */             return null;
/*     */           }
/*     */           
/* 187 */           if (!"1".equals(Util.null2String(map.get("canceled")))) {
/*     */             
/* 189 */             String str6 = "";
/* 190 */             String str7 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/* 191 */             recordSet2.executeQuery(str7, new Object[] { Util.null2String(map.get("supdepid")), str1 });
/* 192 */             if (recordSet2.next()) {
/* 193 */               str6 = recordSet2.getString("mailid");
/*     */             } else {
/* 195 */               str7 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? and mailmasterid=?";
/* 196 */               recordSet2.executeQuery(str7, new Object[] { Util.null2String(map.get("subcompanyid1")), str1 });
/* 197 */               if (recordSet2.next()) {
/* 198 */                 str6 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 211 */             map.put("parentId", str6);
/* 212 */             String str8 = ALYMailParam.getALMailSignParam(map, str2, str3);
/* 213 */             jSONObject = ALYMailApi.createDepartment(str8);
/* 214 */             jSONObject.put("operateType", "1");
/* 215 */             String str9 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 216 */             String str10 = Util.null2String(jSONObject.getJSONObject("data").get("departmentId"));
/* 217 */             if ("100".equals(str9)) {
/* 218 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str10, str6, str, "2", Util.null2String(map.get("name")), str1 });
/*     */             }
/*     */           } 
/*     */         } 
/* 222 */         if (jSONObject != null && "100".equals(Util.null2String(jSONObject.getJSONObject("status").get("statusCode")))) {
/* 223 */           recordSet2.executeUpdate(str4, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("name")), "2", jSONObject.getString("operateType"), "1", "", str1 });
/* 224 */           this.newlog.error("部门：(" + Util.null2String(map.get("name")) + ")，上级部门：" + Util.null2String(map.get("supdepid")) + "，同步成功！"); continue;
/* 225 */         }  if (jSONObject != null) {
/* 226 */           recordSet2.executeUpdate(str4, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("name")), "2", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(Util.null2String(jSONObject.getJSONObject("status").get("statusCode"))), str1 });
/* 227 */           this.newlog.error("部门：(" + Util.null2String(map.get("name")) + ")，上级部门：" + Util.null2String(map.get("supdepid")) + "，同步失败，code=" + Util.null2String(jSONObject.getJSONObject("status").get("statusCode")) + "，msg=" + Util.null2String(jSONObject.getJSONObject("status").get("statusMsg")));
/*     */         } 
/*     */       } 
/*     */     } 
/* 231 */     return null;
/*     */   }
/*     */   
/* 234 */   private Logger newlog = LoggerFactory.getLogger(HrmDepartmentSubscriber4ALY.class);
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getAccessToken() {
/* 239 */     RecordSet recordSet = new RecordSet();
/* 240 */     MailDao mailDao = new MailDao();
/* 241 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 242 */     recordSet.executeQuery("select * from mail_master where mailtype = 1", new Object[0]);
/* 243 */     while (recordSet.next()) {
/* 244 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 245 */       String str2 = Util.null2String(recordSet.getString("isuse"));
/* 246 */       String str3 = Util.null2String(recordSet.getString("issync"));
/*     */       
/* 248 */       if (!"1".equals(str2)) {
/*     */         continue;
/*     */       }
/* 251 */       if (!"1".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/* 255 */       Map map1 = mailDao.getMailMaster(str1);
/* 256 */       Map map2 = mailDao.getMailDetail(str1);
/* 257 */       JSONObject jSONObject1 = ALYMailApi.getToken(Util.null2String(map2.get("alcode")), Util.null2String(map2.get("alpassword")));
/* 258 */       JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/* 259 */       JSONObject jSONObject3 = jSONObject1.getJSONObject("data");
/* 260 */       if (jSONObject3.containsKey("accessToken") && "100".equals(jSONObject2.getString("statusCode"))) {
/*     */         
/* 262 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 263 */         String str = jSONObject3.getString("accessToken");
/* 264 */         hashMap.put("accessToken", str);
/* 265 */         hashMap.put("accessTarget", Util.null2String(map2.get("aldomain")));
/*     */         
/* 267 */         hashMap.put("bindField", Util.null2String(map1.get("bindfield")));
/*     */         
/* 269 */         hashMap.put("id", str1);
/* 270 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 273 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/alymail/biz/HrmDepartmentSubscriber4ALY.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */