/*     */ package weaver.integration.thirdsdk.alymail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.thirdsdk.alymail.api.ALYMailApi;
/*     */ import weaver.integration.thirdsdk.alymail.util.ALYMailParam;
/*     */ import weaver.integration.thirdsdk.mailutil.MailRealFieldAndValue;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSubcompanySubscriber4ALY
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  27 */     List<Map<String, Object>> list = getAccessToken();
/*  28 */     if (list != null && list.size() > 0 && paramList != null) {
/*     */       
/*  30 */       Map<String, String> map = MailRealFieldAndValue.getFieldAndValue(paramList);
/*     */ 
/*     */       
/*  33 */       String str = Util.null2String(map.get("id"));
/*     */       
/*  35 */       for (Map<String, Object> map1 : list) {
/*  36 */         RecordSet recordSet1 = new RecordSet();
/*  37 */         RecordSet recordSet2 = new RecordSet();
/*  38 */         String str1 = Util.null2String(map1.get("id"));
/*  39 */         String str2 = Util.null2String(map1.get("accessToken"));
/*  40 */         String str3 = Util.null2String(map1.get("accessTarget"));
/*     */         
/*  42 */         String str4 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  62 */         String str5 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/*  63 */         recordSet1.executeQuery(str5, new Object[] { str, str1 });
/*  64 */         boolean bool = recordSet1.next();
/*  65 */         JSONObject jSONObject = new JSONObject();
/*  66 */         if (bool) {
/*     */           
/*  68 */           String str6 = recordSet1.getString("mailid");
/*  69 */           String str7 = recordSet1.getString("mailpid");
/*     */           
/*  71 */           boolean bool1 = false;
/*  72 */           List list1 = ALYMailApi.getAllALYDeptids(str2, str3);
/*  73 */           if (list1.contains(str6)) {
/*  74 */             bool1 = true;
/*     */           }
/*  76 */           if (bool1) {
/*  77 */             if ("delete".equalsIgnoreCase(paramString)) {
/*  78 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  79 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  80 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  81 */               hashMap1.put("accessToken", str2);
/*  82 */               hashMap1.put("accessTarget", str3);
/*  83 */               hashMap3.put("access", hashMap1);
/*  84 */               hashMap2.put("departmentId", str6);
/*  85 */               hashMap3.put("param", hashMap2);
/*  86 */               String str8 = JSON.toJSONString(hashMap3);
/*  87 */               jSONObject = ALYMailApi.deleteDepartment(str8);
/*  88 */               jSONObject.put("operateType", "3");
/*  89 */               String str9 = (String)jSONObject.getJSONObject("status").get("statusCode");
/*  90 */               if ("100".equals(str9)) {
/*  91 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str6, str1 });
/*     */               }
/*     */             }
/*  94 */             else if (!"1".equals(Util.null2String(map.get("canceled")))) {
/*  95 */               JSONObject jSONObject1 = new JSONObject();
/*  96 */               String str8 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/*  97 */               recordSet2.executeQuery(str8, new Object[] { Util.null2String(map.get("supsubcomid")), str1 });
/*  98 */               if (recordSet2.next()) {
/*  99 */                 str7 = recordSet2.getString("mailid");
/*     */               }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 109 */               map.put("departmentId", str6);
/* 110 */               map.put("parentId", str7);
/* 111 */               String str9 = ALYMailParam.getALMailSignParam(map, str2, str3);
/* 112 */               jSONObject = ALYMailApi.updateDepartment(str9);
/* 113 */               jSONObject.put("operateType", "2");
/* 114 */               String str10 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 115 */               if ("100".equals(str10)) {
/* 116 */                 recordSet2.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { Util.null2String(map.get("name")), str7, str6, str1 });
/*     */               }
/*     */             } else {
/*     */               
/* 120 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 121 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 122 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 123 */               hashMap1.put("accessToken", str2);
/* 124 */               hashMap1.put("accessTarget", str3);
/* 125 */               hashMap3.put("access", hashMap1);
/* 126 */               hashMap2.put("departmentId", str6);
/* 127 */               hashMap3.put("param", hashMap2);
/* 128 */               String str8 = JSON.toJSONString(hashMap3);
/* 129 */               jSONObject = ALYMailApi.deleteDepartment(str8);
/* 130 */               jSONObject.put("operateType", "2");
/* 131 */               String str9 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 132 */               if ("100".equals(str9)) {
/* 133 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str6, str1 });
/*     */               }
/*     */             } 
/*     */           } else {
/*     */             
/* 138 */             if ("delete".equalsIgnoreCase(paramString)) {
/* 139 */               return null;
/*     */             }
/* 141 */             String str8 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 142 */             if ("0".equals(Util.null2String(map.get("supsubcomid")))) {
/* 143 */               str7 = ALYMailApi.getRootDepartmentId(str2, str3);
/*     */             } else {
/* 145 */               recordSet2.executeQuery(str8, new Object[] { Util.null2String(map.get("supsubcomid")), str1 });
/* 146 */               if (recordSet2.next()) {
/* 147 */                 str7 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 161 */             map.put("parentId", str7);
/* 162 */             String str9 = ALYMailParam.getALMailSignParam(map, str2, str3);
/* 163 */             jSONObject = ALYMailApi.createDepartment(str9);
/* 164 */             jSONObject.put("operateType", "1");
/* 165 */             String str10 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 166 */             String str11 = Util.null2String(jSONObject.getJSONObject("data").get("departmentId"));
/* 167 */             if ("100".equals(str10)) {
/* 168 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str11, str7, str, "1", Util.null2String(map.get("name")), str1 });
/*     */             }
/*     */           } 
/*     */         } else {
/*     */           
/* 173 */           if ("delete".equalsIgnoreCase(paramString)) {
/* 174 */             return null;
/*     */           }
/*     */           
/* 177 */           if (!"1".equals(Util.null2String(map.get("canceled")))) {
/*     */             
/* 179 */             String str6 = ALYMailApi.getRootDepartmentId(str2, str3);
/* 180 */             String str7 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 181 */             recordSet2.executeQuery(str7, new Object[] { Util.null2String(map.get("supsubcomid")), str1 });
/* 182 */             if (recordSet2.next()) {
/* 183 */               str6 = recordSet2.getString("mailid");
/*     */             }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 192 */             map.put("parentId", str6);
/* 193 */             String str8 = ALYMailParam.getALMailSignParam(map, str2, str3);
/* 194 */             jSONObject = ALYMailApi.createDepartment(str8);
/* 195 */             jSONObject.put("operateType", "1");
/* 196 */             String str9 = Util.null2String(jSONObject.getJSONObject("status").get("statusCode"));
/* 197 */             String str10 = Util.null2String(jSONObject.getJSONObject("data").get("departmentId"));
/* 198 */             if ("100".equals(str9))
/*     */             {
/* 200 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str10, str6, str, "1", Util.null2String(map.get("name")), str1 });
/*     */             }
/*     */           } 
/*     */         } 
/* 204 */         if (jSONObject != null && "100".equals(Util.null2String(jSONObject.getJSONObject("status").get("statusCode")))) {
/* 205 */           recordSet2.executeUpdate(str4, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("name")), "1", jSONObject.getString("operateType"), "1", "", str1 });
/* 206 */           this.newlog.error("分部：(" + Util.null2String(map.get("name")) + ")，上级分部：" + Util.null2String(map.get("supsubcomid")) + "，同步成功！"); continue;
/* 207 */         }  if (jSONObject != null) {
/* 208 */           recordSet2.executeUpdate(str4, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("name")), "1", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(Util.null2String(jSONObject.getJSONObject("status").get("statusCode"))), str1 });
/* 209 */           this.newlog.error("分部：(" + Util.null2String(map.get("name")) + ")，上级分部：" + Util.null2String(map.get("supsubcomid")) + "，同步失败，code=" + Util.null2String(jSONObject.getJSONObject("status").get("statusCode")) + "，msg=" + Util.null2String(jSONObject.getJSONObject("status").get("statusMsg")));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 214 */     return null;
/*     */   }
/*     */   
/* 217 */   private Logger newlog = LoggerFactory.getLogger(HrmSubcompanySubscriber4ALY.class);
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getAccessToken() {
/* 222 */     RecordSet recordSet = new RecordSet();
/* 223 */     MailDao mailDao = new MailDao();
/* 224 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 225 */     recordSet.executeQuery("select * from mail_master where mailtype = 1", new Object[0]);
/* 226 */     while (recordSet.next()) {
/* 227 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 228 */       String str2 = Util.null2String(recordSet.getString("isuse"));
/* 229 */       String str3 = Util.null2String(recordSet.getString("issync"));
/*     */       
/* 231 */       if (!"1".equals(str2)) {
/*     */         continue;
/*     */       }
/* 234 */       if (!"1".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/* 238 */       Map map1 = mailDao.getMailMaster(str1);
/* 239 */       Map map2 = mailDao.getMailDetail(str1);
/* 240 */       String str4 = "";
/* 241 */       String str5 = "";
/* 242 */       String str6 = "";
/* 243 */       for (String str : map2.keySet()) {
/* 244 */         if ("alcode".equals(str)) {
/* 245 */           str4 = Util.null2String(map2.get(str)); continue;
/* 246 */         }  if ("alpassword".equals(str)) {
/* 247 */           str5 = Util.null2String(map2.get(str)); continue;
/* 248 */         }  if ("aldomain".equals(str)) {
/* 249 */           str6 = Util.null2String(map2.get(str));
/*     */         }
/*     */       } 
/* 252 */       JSONObject jSONObject1 = ALYMailApi.getToken(str4, str5);
/* 253 */       JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/* 254 */       JSONObject jSONObject3 = jSONObject1.getJSONObject("data");
/* 255 */       if (jSONObject3.containsKey("accessToken") && "100".equals(jSONObject2.getString("statusCode"))) {
/*     */         
/* 257 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 258 */         String str = jSONObject3.getString("accessToken");
/*     */         
/* 260 */         hashMap.put("accessToken", str);
/* 261 */         hashMap.put("accessTarget", str6);
/*     */         
/* 263 */         hashMap.put("bindField", Util.null2String(map1.get("bindfield")));
/*     */         
/* 265 */         hashMap.put("id", str1);
/* 266 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 269 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/alymail/biz/HrmSubcompanySubscriber4ALY.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */