/*     */ package weaver.integration.thirdsdk.wymail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.mail.WYUtil.rsa.RSASignatureToQiye;
/*     */ import weaver.integration.thirdsdk.mailutil.MailRealFieldAndValue;
/*     */ import weaver.integration.thirdsdk.wymail.api.WYMailApi;
/*     */ import weaver.integration.thirdsdk.wymail.util.WYMailParam;
/*     */ import weaver.integration.util.SignUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmDepartmentSubscriber4WY
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  29 */     List<Map<String, Object>> list = getSynMes();
/*  30 */     if (list != null && list.size() > 0 && paramList != null) {
/*  31 */       Map<String, String> map = MailRealFieldAndValue.getFieldAndValue(paramList);
/*     */       
/*  33 */       String str = Util.null2String(map.get("id"));
/*  34 */       for (Map<String, Object> map1 : list) {
/*  35 */         String str1 = System.currentTimeMillis() + "";
/*  36 */         String str2 = Util.null2String(map1.get("id"));
/*  37 */         String str3 = Util.null2String(map1.get("email163_domain"));
/*  38 */         String str4 = Util.null2String(map1.get("email163_product"));
/*  39 */         String str5 = Util.null2String(map1.get("email163_key"));
/*  40 */         String str6 = Util.null2String(map1.get("email163_url"));
/*     */         
/*  42 */         RecordSet recordSet1 = new RecordSet();
/*  43 */         RecordSet recordSet2 = new RecordSet();
/*     */         
/*  45 */         String str7 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  64 */         String str8 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/*  65 */         recordSet1.executeQuery(str8, new Object[] { str, str2 });
/*  66 */         boolean bool = recordSet1.next();
/*  67 */         JSONObject jSONObject = new JSONObject();
/*  68 */         if (bool) {
/*     */           
/*  70 */           String str9 = recordSet1.getString("mailid");
/*  71 */           String str10 = recordSet1.getString("mailpid");
/*     */           
/*  73 */           boolean bool1 = false;
/*  74 */           List list1 = WYMailApi.getAllALYDeptids(str3, str4, str5, str6);
/*  75 */           if (list1.contains(str9)) {
/*  76 */             bool1 = true;
/*     */           }
/*  78 */           if (bool1) {
/*  79 */             if ("delete".equals(paramString)) {
/*     */               
/*  81 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  82 */               hashMap.put("domain", str3);
/*  83 */               hashMap.put("product", str4);
/*  84 */               hashMap.put("time", str1);
/*  85 */               hashMap.put("unit_id", str9);
/*  86 */               String str11 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/*  93 */               String str12 = RSASignatureToQiye.generateSigature(str5, str11);
/*  94 */               String str13 = str11 + "&sign=" + str12;
/*  95 */               jSONObject = WYMailApi.deleteDepartment(str13, str6);
/*  96 */               jSONObject.put("operateType", "3");
/*  97 */               if (jSONObject.getBoolean("suc").booleanValue())
/*     */               {
/*  99 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str9, str2 });
/*     */               }
/*     */             }
/* 102 */             else if (!"1".equals(Util.null2String(map.get("canceled")))) {
/* 103 */               JSONObject jSONObject1 = new JSONObject();
/* 104 */               String str11 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/* 105 */               recordSet2.executeQuery(str11, new Object[] { Util.null2String(map.get("supdepid")), str2 });
/* 106 */               String str12 = "";
/* 107 */               if (recordSet2.next()) {
/* 108 */                 str12 = recordSet2.getString("mailid");
/*     */               } else {
/* 110 */                 str11 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? and mailmasterid=?";
/* 111 */                 recordSet2.executeQuery(str11, new Object[] { Util.null2String(map.get("subcompanyid1")), str2 });
/* 112 */                 if (recordSet2.next()) {
/* 113 */                   str12 = recordSet2.getString("mailid");
/*     */                 }
/*     */               } 
/* 116 */               if (!str12.equals(str10)) {
/* 117 */                 HashMap<Object, Object> hashMap = new HashMap<>();
/* 118 */                 hashMap.put("domain", str3);
/* 119 */                 hashMap.put("new_parent_id", str12);
/* 120 */                 hashMap.put("product", str4);
/* 121 */                 hashMap.put("time", str1);
/* 122 */                 hashMap.put("unit_id", str9);
/* 123 */                 String str17 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 130 */                 String str18 = RSASignatureToQiye.generateSigature(str5, str17);
/* 131 */                 String str19 = str17 + "&sign=" + str18;
/* 132 */                 jSONObject = WYMailApi.updateDepartmentSupid(str19, str6);
/* 133 */                 if (jSONObject.getBoolean("suc").booleanValue())
/*     */                 {
/* 135 */                   recordSet2.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { Util.null2String(map.get("unit_name")), str12, str9, str2 });
/*     */                 }
/*     */               } 
/*     */               
/* 139 */               map.put("unit_id", str9);
/* 140 */               String str13 = WYMailParam.getWYMailSignParam(map, str3, str4, str1, 1);
/* 141 */               String str14 = WYMailParam.getWYMailSignParam(map, str3, str4, str1, 0);
/*     */               
/* 143 */               String str15 = RSASignatureToQiye.generateSigature(str5, str13);
/* 144 */               String str16 = str14 + "&sign=" + str15;
/* 145 */               jSONObject = WYMailApi.updateDepartment(str16, str6);
/* 146 */               jSONObject.put("operateType", "2");
/* 147 */               if (jSONObject.getBoolean("suc").booleanValue()) {
/* 148 */                 recordSet2.executeUpdate("update mail_dep_map set name=? where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { Util.null2String(map.get("unit_name")), str9, str2 });
/*     */               }
/*     */             } else {
/*     */               
/* 152 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 153 */               hashMap.put("domain", str3);
/* 154 */               hashMap.put("product", str4);
/* 155 */               hashMap.put("time", str1);
/* 156 */               hashMap.put("unit_id", str9);
/* 157 */               String str11 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 164 */               String str12 = RSASignatureToQiye.generateSigature(str5, str11);
/* 165 */               String str13 = str11 + "&sign=" + str12;
/* 166 */               jSONObject = WYMailApi.deleteDepartment(str13, str6);
/* 167 */               jSONObject.put("operateType", "3");
/* 168 */               if (jSONObject.getBoolean("suc").booleanValue())
/*     */               {
/* 170 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str9, str2 });
/*     */               }
/*     */             } 
/*     */           } else {
/* 174 */             if ("delete".equals(paramString)) {
/* 175 */               return null;
/*     */             }
/*     */             
/* 178 */             String str11 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/* 179 */             recordSet2.executeQuery(str11, new Object[] { Util.null2String(map.get("supdepid")), str2 });
/* 180 */             if (recordSet2.next()) {
/* 181 */               str10 = recordSet2.getString("mailid");
/*     */             } else {
/* 183 */               str11 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? and mailmasterid=?";
/* 184 */               recordSet2.executeQuery(str11, new Object[] { Util.null2String(map.get("subcompanyid1")), str2 });
/* 185 */               if (recordSet2.next()) {
/* 186 */                 str10 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/* 189 */             map.put("unit_id", str9);
/* 190 */             map.put("parent_id", str10);
/* 191 */             String str12 = WYMailParam.getWYMailSignParam(map, str3, str4, str1, 1);
/*     */             
/* 193 */             String str13 = RSASignatureToQiye.generateSigature(str5, str12);
/*     */ 
/*     */             
/* 196 */             String str14 = WYMailParam.getWYMailSignParam(map, str3, str4, str1, 0);
/* 197 */             String str15 = str14 + "&sign=" + str13;
/* 198 */             jSONObject = WYMailApi.createDepartment(str15, str6);
/* 199 */             jSONObject.put("operateType", "1");
/* 200 */             if (jSONObject.getBoolean("suc").booleanValue()) {
/* 201 */               String str16 = jSONObject.getJSONObject("con").getString("unit_id");
/*     */               
/* 203 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str16, str10, str, "2", Util.null2String(map.get("unit_name")), str2 });
/*     */             } 
/*     */           } 
/*     */         } else {
/*     */           
/* 208 */           if ("delete".equals(paramString)) {
/* 209 */             return null;
/*     */           }
/* 211 */           if (!"1".equals(Util.null2String(map.get("canceled")))) {
/*     */             
/* 213 */             String str9 = "";
/* 214 */             String str10 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?";
/* 215 */             recordSet2.executeQuery(str10, new Object[] { Util.null2String(map.get("supdepid")), str2 });
/* 216 */             if (recordSet2.next()) {
/* 217 */               str9 = recordSet2.getString("mailid");
/*     */             } else {
/* 219 */               str10 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? and mailmasterid=?";
/* 220 */               recordSet2.executeQuery(str10, new Object[] { Util.null2String(map.get("subcompanyid1")), str2 });
/* 221 */               if (recordSet2.next()) {
/* 222 */                 str9 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/* 225 */             map.put("parent_id", str9);
/* 226 */             String str11 = WYMailParam.getWYMailSignParam(map, str3, str4, str1, 1);
/*     */             
/* 228 */             String str12 = RSASignatureToQiye.generateSigature(str5, str11);
/*     */ 
/*     */             
/* 231 */             String str13 = WYMailParam.getWYMailSignParam(map, str3, str4, str1, 0);
/* 232 */             String str14 = str13 + "&sign=" + str12;
/* 233 */             jSONObject = WYMailApi.createDepartment(str14, str6);
/* 234 */             jSONObject.put("operateType", "1");
/* 235 */             if (jSONObject.getBoolean("suc").booleanValue()) {
/* 236 */               String str15 = jSONObject.getJSONObject("con").getString("unit_id");
/*     */               
/* 238 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str15, str9, str, "2", Util.null2String(map.get("unit_name")), str2 });
/*     */             } 
/*     */           } 
/*     */         } 
/* 242 */         if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/* 243 */           recordSet2.executeUpdate(str7, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("unit_name")), "2", jSONObject.getString("operateType"), "1", "", str2 });
/* 244 */           this.newlog.error("部门：(" + Util.null2String(map.get("unit_name")) + ")，上级部门：" + Util.null2String(map.get("supdeptid")) + "，同步成功！"); continue;
/* 245 */         }  if (jSONObject != null) {
/* 246 */           recordSet2.executeUpdate(str7, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("unit_name")), "2", jSONObject.getString("operateType"), "0", jSONObject.getString("error_code"), str2 });
/* 247 */           this.newlog.error("分部：(" + Util.null2String(map.get("unit_name")) + ")，上级分部：" + Util.null2String(map.get("supdeptid")) + "，同步失败，code=" + jSONObject.getString("error_code") + "，msg=" + jSONObject.getString("error_code"));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 252 */     return null;
/*     */   }
/*     */   
/* 255 */   private Logger newlog = LoggerFactory.getLogger(HrmDepartmentSubscriber4WY.class);
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getSynMes() {
/* 260 */     RecordSet recordSet = new RecordSet();
/* 261 */     MailDao mailDao = new MailDao();
/* 262 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 263 */     recordSet.executeQuery("select * from mail_master where mailtype = 2", new Object[0]);
/* 264 */     while (recordSet.next()) {
/* 265 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 266 */       String str2 = Util.null2String(recordSet.getString("isuse"));
/* 267 */       String str3 = Util.null2String(recordSet.getString("issync"));
/* 268 */       if (!"1".equals(str2)) {
/*     */         continue;
/*     */       }
/* 271 */       if (!"1".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/* 275 */       Map map1 = mailDao.getMailMaster(str1);
/* 276 */       Map map2 = mailDao.getMailDetail(str1);
/* 277 */       String str4 = "";
/* 278 */       String str5 = "";
/* 279 */       String str6 = "";
/* 280 */       String str7 = "";
/* 281 */       String str8 = "";
/* 282 */       for (String str : map2.keySet()) {
/* 283 */         if ("email163_domain".equals(str)) {
/* 284 */           str4 = Util.null2String(map2.get(str).toString()); continue;
/* 285 */         }  if ("email163_product".equals(str)) {
/* 286 */           str5 = Util.null2String(map2.get(str).toString()); continue;
/* 287 */         }  if ("email163_key".equals(str)) {
/* 288 */           str6 = Util.null2String(map2.get(str).toString()); continue;
/* 289 */         }  if ("email163_url".equals(str)) {
/* 290 */           str7 = Util.null2String(map2.get(str).toString()); continue;
/* 291 */         }  if ("email163_topdeptid".equals(str)) {
/* 292 */           str8 = Util.null2String(map2.get(str).toString());
/*     */         }
/*     */       } 
/* 295 */       JSONObject jSONObject = WYMailApi.testWY(str4, str5, str6, str7);
/* 296 */       if (jSONObject.getBoolean("suc").booleanValue()) {
/*     */ 
/*     */         
/* 299 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         
/* 301 */         hashMap.put("bindField", Util.null2String(map1.get("bindfield")));
/*     */         
/* 303 */         hashMap.put("id", str1);
/*     */         
/* 305 */         hashMap.put("email163_domain", str4);
/* 306 */         hashMap.put("email163_product", str5);
/* 307 */         hashMap.put("email163_key", str6);
/* 308 */         hashMap.put("email163_topdeptid", str8);
/* 309 */         hashMap.put("email163_url", str7);
/* 310 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 313 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private String doEncode(String paramString) {
/* 317 */     String str = "";
/*     */     try {
/* 319 */       str = URLEncoder.encode(paramString, "utf-8");
/* 320 */     } catch (Exception exception) {
/* 321 */       exception.printStackTrace();
/* 322 */       this.newlog.error(paramString + "转码失败：", exception);
/*     */     } 
/* 324 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/wymail/biz/HrmDepartmentSubscriber4WY.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */