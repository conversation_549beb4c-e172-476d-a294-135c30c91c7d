/*     */ package weaver.integration.thirdsdk.wymail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.mail.WYUtil.rsa.RSASignatureToQiye;
/*     */ import weaver.integration.thirdsdk.mailutil.MailRealFieldAndValue;
/*     */ import weaver.integration.thirdsdk.wymail.api.WYMailApi;
/*     */ import weaver.integration.thirdsdk.wymail.util.WYMailParam;
/*     */ import weaver.integration.util.SignUtil;
/*     */ 
/*     */ 
/*     */ public class HrmSubcompanySubscriber4WY
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  26 */     List<Map<String, Object>> list = getSynMes();
/*     */ 
/*     */     
/*  29 */     if (list != null && list.size() > 0 && paramList != null) {
/*  30 */       Map<String, String> map = MailRealFieldAndValue.getFieldAndValue(paramList);
/*     */       
/*  32 */       String str = Util.null2String(map.get("id"));
/*     */ 
/*     */       
/*  35 */       for (Map<String, Object> map1 : list) {
/*  36 */         RecordSet recordSet1 = new RecordSet();
/*  37 */         RecordSet recordSet2 = new RecordSet();
/*  38 */         String str1 = Util.null2String(map1.get("id"));
/*  39 */         String str2 = Util.null2String(map1.get("email163_domain"));
/*  40 */         String str3 = Util.null2String(map1.get("email163_product"));
/*  41 */         String str4 = Util.null2String(map1.get("email163_key"));
/*  42 */         String str5 = Util.null2String(map1.get("email163_topdeptid"));
/*  43 */         String str6 = Util.null2String(map1.get("email163_url"));
/*  44 */         String str7 = System.currentTimeMillis() + "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  59 */         String str8 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  79 */         String str9 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/*     */ 
/*     */         
/*  82 */         recordSet2.executeQuery(str9, new Object[] { str, str1 });
/*  83 */         boolean bool = recordSet2.next();
/*  84 */         JSONObject jSONObject = new JSONObject();
/*  85 */         if (bool) {
/*     */           
/*  87 */           String str10 = recordSet2.getString("mailid");
/*  88 */           String str11 = recordSet2.getString("mailpid");
/*     */           
/*  90 */           boolean bool1 = false;
/*  91 */           List list1 = WYMailApi.getAllALYDeptids(str2, str3, str4, str6);
/*     */           
/*  93 */           if (list1.contains(str10)) {
/*  94 */             bool1 = true;
/*     */           }
/*     */           
/*  97 */           if (bool1) {
/*  98 */             if ("delete".equals(paramString)) {
/*  99 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 100 */               hashMap.put("domain", str2);
/* 101 */               hashMap.put("product", str3);
/* 102 */               hashMap.put("time", str7);
/* 103 */               hashMap.put("unit_id", str10);
/* 104 */               String str12 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 111 */               String str13 = RSASignatureToQiye.generateSigature(str4, str12);
/* 112 */               String str14 = str12 + "&sign=" + str13;
/* 113 */               jSONObject = WYMailApi.deleteDepartment(str14, str6);
/* 114 */               jSONObject.put("operateType", "3");
/* 115 */               if (jSONObject.getBoolean("suc").booleanValue())
/*     */               {
/* 117 */                 recordSet1.executeUpdate("delete from mail_dep_map where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str10, str1 });
/*     */               }
/*     */             }
/* 120 */             else if (!"1".equals(Util.null2String(map.get("canceled")))) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 134 */               String str12 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 135 */               String str13 = "";
/* 136 */               if ("0".equals(Util.null2String(map.get("supsubcomid")))) {
/* 137 */                 str13 = str5;
/*     */               } else {
/* 139 */                 recordSet1.executeQuery(str12, new Object[] { Util.null2String(map.get("supsubcomid")), str1 });
/* 140 */                 if (recordSet1.next()) {
/* 141 */                   str13 = recordSet1.getString("mailid");
/*     */                 }
/*     */               } 
/* 144 */               if (!str13.equals(str11)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 151 */                 HashMap<Object, Object> hashMap = new HashMap<>();
/* 152 */                 hashMap.put("domain", str2);
/* 153 */                 hashMap.put("new_parent_id", str13);
/* 154 */                 hashMap.put("product", str3);
/* 155 */                 hashMap.put("time", str7);
/* 156 */                 hashMap.put("unit_id", str10);
/* 157 */                 String str18 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 164 */                 String str19 = RSASignatureToQiye.generateSigature(str4, str18);
/* 165 */                 String str20 = str18 + "&sign=" + str19;
/* 166 */                 jSONObject = WYMailApi.updateDepartmentSupid(str20, str6);
/* 167 */                 if (jSONObject.getBoolean("suc").booleanValue())
/*     */                 {
/* 169 */                   recordSet1.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { Util.null2String(map.get("unit_name")), str13, str10, str1 });
/*     */                 }
/*     */               } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 181 */               map.put("unit_id", str10);
/* 182 */               String str14 = WYMailParam.getWYMailSignParam(map, str2, str3, str7, 1);
/* 183 */               String str15 = WYMailParam.getWYMailSignParam(map, str2, str3, str7, 0);
/*     */               
/* 185 */               String str16 = RSASignatureToQiye.generateSigature(str4, str14);
/* 186 */               String str17 = str15 + "&sign=" + str16;
/* 187 */               jSONObject = WYMailApi.updateDepartment(str17, str6);
/* 188 */               jSONObject.put("operateType", "2");
/* 189 */               if (jSONObject.getBoolean("suc").booleanValue())
/*     */               {
/* 191 */                 recordSet1.executeUpdate("update mail_dep_map set name=? where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { Util.null2String(map.get("unit_name")), str10, str1 });
/*     */ 
/*     */               
/*     */               }
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */               
/* 200 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 201 */               hashMap.put("domain", str2);
/* 202 */               hashMap.put("product", str3);
/* 203 */               hashMap.put("time", str7);
/* 204 */               hashMap.put("unit_id", str10);
/* 205 */               String str12 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 212 */               String str13 = RSASignatureToQiye.generateSigature(str4, str12);
/* 213 */               String str14 = str12 + "&sign=" + str13;
/* 214 */               jSONObject = WYMailApi.deleteDepartment(str14, str6);
/* 215 */               jSONObject.put("operateType", "3");
/* 216 */               if (jSONObject.getBoolean("suc").booleanValue())
/*     */               {
/* 218 */                 recordSet1.executeUpdate("delete from mail_dep_map where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str10, str1 });
/*     */               }
/*     */             } 
/*     */           } else {
/* 222 */             if ("delete".equals(paramString)) {
/* 223 */               return null;
/*     */             }
/*     */             
/* 226 */             String str12 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 227 */             if ("0".equals(Util.null2String(map.get("supsubcomid")))) {
/* 228 */               str11 = str5;
/*     */             } else {
/* 230 */               recordSet1.executeQuery(str12, new Object[] { Util.null2String(map.get("supsubcomid")), str1 });
/* 231 */               if (recordSet1.next()) {
/* 232 */                 str11 = recordSet1.getString("wyid");
/*     */               }
/*     */             } 
/* 235 */             map.put("parent_id", str11);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 251 */             String str13 = WYMailParam.getWYMailSignParam(map, str2, str3, str7, 1);
/*     */             
/* 253 */             String str14 = RSASignatureToQiye.generateSigature(str4, str13);
/*     */ 
/*     */             
/* 256 */             String str15 = WYMailParam.getWYMailSignParam(map, str2, str3, str7, 0);
/* 257 */             String str16 = str15 + "&sign=" + str14;
/* 258 */             jSONObject = WYMailApi.createDepartment(str16, str6);
/* 259 */             jSONObject.put("operateType", "1");
/* 260 */             if (jSONObject.getBoolean("suc").booleanValue()) {
/* 261 */               String str17 = jSONObject.getJSONObject("con").getString("unit_id");
/*     */               
/* 263 */               recordSet1.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str17, str11, str, "1", Util.null2String(map.get("unit_name")), str1 });
/*     */             } 
/*     */           } 
/*     */         } else {
/* 267 */           if ("delete".equals(paramString)) {
/* 268 */             return null;
/*     */           }
/*     */           
/* 271 */           if (!"1".equals(Util.null2String(map.get("canceled")))) {
/*     */             
/* 273 */             String str10 = str5;
/* 274 */             String str11 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 275 */             recordSet1.executeQuery(str11, new Object[] { Util.null2String(map.get("supsubcomid")), str1 });
/* 276 */             if (recordSet1.next()) {
/* 277 */               str10 = recordSet1.getString("mailid");
/*     */             }
/* 279 */             map.put("parent_id", str10);
/* 280 */             String str12 = WYMailParam.getWYMailSignParam(map, str2, str3, str7, 1);
/*     */             
/* 282 */             String str13 = RSASignatureToQiye.generateSigature(str4, str12);
/*     */ 
/*     */             
/* 285 */             String str14 = WYMailParam.getWYMailSignParam(map, str2, str3, str7, 0);
/* 286 */             String str15 = str14 + "&sign=" + str13;
/* 287 */             jSONObject = WYMailApi.createDepartment(str15, str6);
/* 288 */             jSONObject.put("operateType", "1");
/* 289 */             if (jSONObject.getBoolean("suc").booleanValue()) {
/* 290 */               String str16 = jSONObject.getJSONObject("con").getString("unit_id");
/*     */               
/* 292 */               recordSet1.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str16, str10, str, "1", Util.null2String(map.get("unit_name")), str1 });
/*     */             } 
/*     */           } 
/*     */         } 
/* 296 */         if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/* 297 */           recordSet1.executeUpdate(str8, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("unit_name")), "1", jSONObject.getString("operateType"), "1", "", str1 });
/* 298 */           this.newlog.error("分部：(" + Util.null2String(map.get("unit_name")) + ")，上级分部：" + Util.null2String(map.get("supsubcomid")) + "，同步成功！"); continue;
/* 299 */         }  if (jSONObject != null) {
/* 300 */           recordSet1.executeUpdate(str8, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), Util.null2String(map.get("unit_name")), "1", jSONObject.getString("operateType"), "0", jSONObject.getString("error_code"), str1 });
/* 301 */           this.newlog.error("分部：(" + Util.null2String(map.get("unit_name")) + ")，上级分部：" + Util.null2String(map.get("supsubcomid")) + "，同步失败，code=" + jSONObject.getString("error_code") + "，msg=" + jSONObject.getString("error_code"));
/*     */         } 
/*     */       } 
/*     */     } 
/* 305 */     return null;
/*     */   }
/*     */   
/* 308 */   private Logger newlog = LoggerFactory.getLogger(HrmSubcompanySubscriber4WY.class);
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getSynMes() {
/* 313 */     RecordSet recordSet = new RecordSet();
/* 314 */     MailDao mailDao = new MailDao();
/* 315 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 316 */     recordSet.executeQuery("select * from mail_master where mailtype = 2", new Object[0]);
/* 317 */     while (recordSet.next()) {
/* 318 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 319 */       String str2 = Util.null2String(recordSet.getString("isuse"));
/* 320 */       String str3 = Util.null2String(recordSet.getString("issync"));
/* 321 */       if (!"1".equals(str2)) {
/*     */         continue;
/*     */       }
/* 324 */       if (!"1".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/* 328 */       Map map1 = mailDao.getMailMaster(str1);
/* 329 */       Map map2 = mailDao.getMailDetail(str1);
/* 330 */       String str4 = "";
/* 331 */       String str5 = "";
/* 332 */       String str6 = "";
/* 333 */       String str7 = "";
/* 334 */       String str8 = "";
/* 335 */       for (String str : map2.keySet()) {
/* 336 */         if ("email163_domain".equals(str)) {
/* 337 */           str4 = Util.null2String(map2.get(str).toString()); continue;
/* 338 */         }  if ("email163_product".equals(str)) {
/* 339 */           str5 = Util.null2String(map2.get(str).toString()); continue;
/* 340 */         }  if ("email163_key".equals(str)) {
/* 341 */           str6 = Util.null2String(map2.get(str).toString()); continue;
/* 342 */         }  if ("email163_url".equals(str)) {
/* 343 */           str7 = Util.null2String(map2.get(str).toString()); continue;
/* 344 */         }  if ("email163_topdeptid".equals(str)) {
/* 345 */           str8 = Util.null2String(map2.get(str).toString());
/*     */         }
/*     */       } 
/* 348 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 349 */       hashMap.put("bindField", Util.null2String(map1.get("bindfield")));
/*     */       
/* 351 */       hashMap.put("id", str1);
/*     */       
/* 353 */       hashMap.put("email163_domain", str4);
/* 354 */       hashMap.put("email163_product", str5);
/* 355 */       hashMap.put("email163_key", str6);
/* 356 */       hashMap.put("email163_topdeptid", str8);
/* 357 */       hashMap.put("email163_url", str7);
/* 358 */       arrayList.add(hashMap);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 376 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 382 */     TreeMap<Object, Object> treeMap = new TreeMap<>();
/* 383 */     treeMap.put("sd", "ds");
/* 384 */     treeMap.put("sv", Integer.valueOf(1));
/* 385 */     treeMap.put("ed", Boolean.valueOf(true));
/* 386 */     treeMap.put("nf", "dsbv");
/* 387 */     treeMap.put("a", "dvs");
/* 388 */     for (String str : treeMap.keySet()) {
/* 389 */       System.out.println("key===" + str);
/* 390 */       System.out.println("value====" + treeMap.get(str));
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/wymail/biz/HrmSubcompanySubscriber4WY.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */