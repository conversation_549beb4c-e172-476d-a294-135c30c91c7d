/*     */ package weaver.integration.thirdsdk.wymail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.mail.WYUtil.rsa.RSASignatureToQiye;
/*     */ import weaver.integration.thirdsdk.wymail.api.WYMailApi;
/*     */ import weaver.integration.util.SignUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmResourceSubscriber4WY
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  30 */     List<Map<String, Object>> list = getSynMes();
/*  31 */     if (list != null && list.size() > 0 && paramList != null) {
/*  32 */       String str1 = "";
/*  33 */       String str2 = "";
/*  34 */       String str3 = "";
/*  35 */       String str4 = "";
/*     */       
/*  37 */       String str5 = "";
/*  38 */       String str6 = "";
/*  39 */       String str7 = "";
/*  40 */       String str8 = "";
/*  41 */       String str9 = getRandomPassword(16);
/*  42 */       String str10 = "";
/*  43 */       String str11 = "";
/*  44 */       MailDao mailDao = new MailDao();
/*  45 */       for (FieldData fieldData : paramList) {
/*  46 */         Object object = fieldData.getFieldValue();
/*  47 */         String str = fieldData.getFieldName();
/*     */         
/*  49 */         if ("loginid".equalsIgnoreCase(str)) {
/*  50 */           str2 = object.toString(); continue;
/*  51 */         }  if ("id".equalsIgnoreCase(str)) {
/*  52 */           str1 = object.toString(); continue;
/*  53 */         }  if ("lastname".equalsIgnoreCase(str)) {
/*  54 */           str3 = object.toString(); continue;
/*  55 */         }  if ("telephone".equalsIgnoreCase(str)) {
/*  56 */           str4 = object.toString();
/*     */           continue;
/*     */         } 
/*  59 */         if ("email".equalsIgnoreCase(str)) {
/*  60 */           str5 = object.toString(); continue;
/*  61 */         }  if ("departmentid".equalsIgnoreCase(str)) {
/*  62 */           str7 = object.toString(); continue;
/*  63 */         }  if ("status".equalsIgnoreCase(str)) {
/*  64 */           str8 = object.toString();
/*     */           continue;
/*     */         } 
/*  67 */         if ("mobile".equalsIgnoreCase(str)) {
/*  68 */           str10 = object.toString(); continue;
/*  69 */         }  if ("sex".equalsIgnoreCase(str)) {
/*  70 */           str11 = object.toString();
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/*  75 */       for (Map<String, Object> map : list) {
/*  76 */         String str12 = System.currentTimeMillis() + "";
/*  77 */         String str13 = Util.null2String(map.get("id"));
/*  78 */         String str14 = Util.null2String(map.get("email163_domain"));
/*  79 */         String str15 = Util.null2String(map.get("email163_product"));
/*  80 */         String str16 = Util.null2String(map.get("email163_key"));
/*     */         
/*  82 */         String str17 = Util.null2String(map.get("email163_url"));
/*  83 */         String str18 = Util.null2String(map.get("bindField"));
/*  84 */         String str19 = mailDao.getBindFieldValue(str18, str1);
/*  85 */         RecordSet recordSet = new RecordSet();
/*  86 */         String str20 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 105 */         JSONObject jSONObject = new JSONObject();
/* 106 */         if ("".equals(str19) || str19.indexOf("@") <= -1 || !str14.equals(str19.substring(str19.indexOf("@") + 1, str19.length()))) {
/* 107 */           this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，" + str18 + "=" + str19 + "，所属部门：" + str7 + "，邮箱地址错误！");
/* 108 */           return null;
/*     */         } 
/* 110 */         str6 = str19.substring(0, str19.indexOf("@"));
/*     */ 
/*     */         
/* 113 */         Map map1 = WYMailApi.checkUser(str6, str14, str15, str16, str17);
/* 114 */         if (((Boolean)map1.get("checkUser")).booleanValue()) {
/*     */           
/* 116 */           if (str8.equals("0") || str8.equals("1") || str8.equals("2") || str8.equals("3")) {
/* 117 */             if ("0".equals(Util.null2String(map1.get("status")))) {
/*     */               
/* 119 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 120 */               hashMap1.put("account_name", str6);
/* 121 */               hashMap1.put("domain", str14);
/* 122 */               hashMap1.put("product", str15);
/* 123 */               hashMap1.put("time", str12);
/* 124 */               String str30 = SignUtil.linkParameters(hashMap1);
/*     */ 
/*     */               
/* 127 */               String str31 = RSASignatureToQiye.generateSigature(str16, str30);
/* 128 */               String str32 = str30 + "&sign=" + str31;
/* 129 */               WYMailApi.hfHrmresource(str32, str17);
/*     */             } 
/* 131 */             String str21 = Util.null2String(map1.get("wyUserDept"));
/* 132 */             String str22 = getWYDeptId(str7, str13);
/*     */             
/* 134 */             if (!str21.equals(str22)) {
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 139 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 140 */               hashMap1.put("account_name", str6);
/* 141 */               hashMap1.put("domain", str14);
/* 142 */               hashMap1.put("product", str15);
/* 143 */               hashMap1.put("time", str12);
/* 144 */               hashMap1.put("unit_id", str22);
/* 145 */               String str30 = SignUtil.linkParameters(hashMap1);
/*     */ 
/*     */ 
/*     */               
/* 149 */               String str31 = RSASignatureToQiye.generateSigature(str16, str30);
/* 150 */               String str32 = str30 + "&sign=" + str31;
/* 151 */               WYMailApi.updateHrmToDept(str32, str17);
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 158 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 159 */             hashMap.put("account_name", str6);
/* 160 */             hashMap.put("domain", str14);
/* 161 */             hashMap.put("mobile", str10);
/* 162 */             hashMap.put("nickname", str3);
/* 163 */             hashMap.put("tel", str4);
/* 164 */             hashMap.put("product", str15);
/* 165 */             hashMap.put("time", str12);
/* 166 */             hashMap.put("gender", str11);
/* 167 */             String str23 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 174 */             String str24 = RSASignatureToQiye.generateSigature(str16, str23);
/* 175 */             System.out.println(str24);
/*     */ 
/*     */             
/* 178 */             String str25 = "account_name=" + str6 + "&domain=" + str14 + "&gender=" + str11 + "&mobile=" + str10 + "&nickname=" + doEncode(str3) + "&tel=" + str4 + "&product=" + str15 + "&time=" + str12;
/*     */ 
/*     */             
/* 181 */             String str26 = str25 + "&sign=" + str24;
/* 182 */             jSONObject = WYMailApi.updateHrmresource(str26, str17);
/*     */ 
/*     */             
/* 185 */             String str27 = "account_name=" + str6 + "&domain=" + str14 + "&product=" + str15 + "&time=" + str12;
/*     */             
/* 187 */             String str28 = RSASignatureToQiye.generateSigature(str16, str27);
/* 188 */             String str29 = str27 + "&sign=" + str28;
/* 189 */             jSONObject = WYMailApi.hfHrmresource(str29, str17);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 209 */             jSONObject.put("operateType", "2");
/*     */           } else {
/*     */             
/* 212 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 213 */             hashMap.put("account_name", str6);
/* 214 */             hashMap.put("domain", str14);
/* 215 */             hashMap.put("product", str15);
/* 216 */             hashMap.put("time", str12);
/* 217 */             String str21 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */             
/* 220 */             String str22 = RSASignatureToQiye.generateSigature(str16, str21);
/* 221 */             String str23 = str21 + "&sign=" + str22;
/* 222 */             jSONObject = WYMailApi.jyHrmresource(str23, str17);
/* 223 */             jSONObject.put("operateType", "5");
/*     */           }
/*     */         
/* 226 */         } else if (str8.equals("0") || str8.equals("1") || str8.equals("2") || str8.equals("3")) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 235 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 236 */           hashMap.put("account_name", str6);
/* 237 */           hashMap.put("addr_right", "1");
/* 238 */           hashMap.put("addr_visible", "1");
/* 239 */           hashMap.put("domain", str14);
/* 240 */           hashMap.put("gender", str11);
/* 241 */           hashMap.put("mobile", str10);
/* 242 */           hashMap.put("nickname", str3);
/* 243 */           hashMap.put("pass_type", "0");
/* 244 */           hashMap.put("passchange_req", "0");
/* 245 */           hashMap.put("password", str9);
/* 246 */           hashMap.put("product", str15);
/* 247 */           hashMap.put("tel", str4);
/* 248 */           hashMap.put("time", str12);
/* 249 */           hashMap.put("unit_id", getWYDeptId(str7, str13));
/* 250 */           String str21 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 257 */           String str22 = RSASignatureToQiye.generateSigature(str16, str21);
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 262 */           String str23 = "account_name=" + str6 + "&addr_right=1&addr_visible=1&domain=" + str14 + "&gender=" + str11 + "&mobile=" + str10 + "&nickname=" + doEncode(str3) + "&pass_type=0&passchange_req=0&password=" + str9 + "&tel=" + str4 + "&product=" + str15 + "&time=" + str12 + "&unit_id=" + getWYDeptId(str7, str13);
/* 263 */           String str24 = str23 + "&sign=" + str22;
/* 264 */           jSONObject = WYMailApi.createHrmresource(str24, str17);
/* 265 */           if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/*     */             
/* 267 */             String str = "INSERT INTO MailDefaultPwd\n        ( userid ,\n          loginid ,\n          lastname ,\n          defaultpwd ,\n          createtime,\n          mailmasterid\n        )\nVALUES  ( ? ,\n          ? ,\n          ? ,\n          ? ,\n          ? ,\n          ?  \n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 282 */             recordSet.executeUpdate(str, new Object[] { str1, str2, str3, str9, TimeUtil.getCurrentTimeString(), str13 });
/*     */           } 
/* 284 */           jSONObject.put("operateType", "1");
/*     */         } 
/*     */ 
/*     */         
/* 288 */         if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/* 289 */           recordSet.executeUpdate(str20, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "1", "", str13 });
/* 290 */           this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，" + str18 + "=" + str19 + "，所属部门：" + str7 + "，同步成功！"); continue;
/* 291 */         }  if (jSONObject != null) {
/* 292 */           recordSet.executeUpdate(str20, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "0", jSONObject.getString("error_code"), str13 });
/* 293 */           this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，" + str18 + "=" + str19 + "，所属部门：" + str7 + "，同步失败，code=" + jSONObject.getString("error_code") + "，msg=" + jSONObject.getString("error_code"));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 298 */     return null;
/*     */   }
/*     */   
/* 301 */   private Logger newlog = LoggerFactory.getLogger(HrmResourceSubscriber4WY.class);
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getSynMes() {
/* 306 */     RecordSet recordSet = new RecordSet();
/* 307 */     MailDao mailDao = new MailDao();
/* 308 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 309 */     recordSet.executeQuery("select * from mail_master where mailtype = 2", new Object[0]);
/* 310 */     while (recordSet.next()) {
/* 311 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 312 */       String str2 = Util.null2String(recordSet.getString("isuse"));
/* 313 */       String str3 = Util.null2String(recordSet.getString("issync"));
/* 314 */       if (!"1".equals(str2)) {
/*     */         continue;
/*     */       }
/* 317 */       if (!"1".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */       
/* 321 */       Map map1 = mailDao.getMailMaster(str1);
/* 322 */       Map map2 = mailDao.getMailDetail(str1);
/* 323 */       String str4 = "";
/* 324 */       String str5 = "";
/* 325 */       String str6 = "";
/* 326 */       String str7 = "";
/* 327 */       String str8 = "";
/* 328 */       for (String str : map2.keySet()) {
/* 329 */         if ("email163_domain".equals(str)) {
/* 330 */           str4 = Util.null2String(map2.get(str).toString()); continue;
/* 331 */         }  if ("email163_product".equals(str)) {
/* 332 */           str5 = Util.null2String(map2.get(str).toString()); continue;
/* 333 */         }  if ("email163_key".equals(str)) {
/* 334 */           str6 = Util.null2String(map2.get(str).toString()); continue;
/* 335 */         }  if ("email163_url".equals(str)) {
/* 336 */           str7 = Util.null2String(map2.get(str).toString()); continue;
/* 337 */         }  if ("email163_topdeptid".equals(str)) {
/* 338 */           str8 = Util.null2String(map2.get(str).toString());
/*     */         }
/*     */       } 
/* 341 */       JSONObject jSONObject = WYMailApi.testWY(str4, str5, str6, str7);
/* 342 */       if (jSONObject.getBoolean("suc").booleanValue()) {
/*     */ 
/*     */         
/* 345 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         
/* 347 */         hashMap.put("bindField", Util.null2String(map1.get("bindfield")));
/*     */         
/* 349 */         hashMap.put("id", str1);
/*     */         
/* 351 */         hashMap.put("email163_domain", str4);
/* 352 */         hashMap.put("email163_product", str5);
/* 353 */         hashMap.put("email163_key", str6);
/* 354 */         hashMap.put("email163_topdeptid", str8);
/* 355 */         hashMap.put("email163_url", str7);
/* 356 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 359 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private String doEncode(String paramString) {
/* 363 */     String str = "";
/*     */     try {
/* 365 */       str = URLEncoder.encode(paramString, "utf-8");
/* 366 */     } catch (Exception exception) {
/* 367 */       exception.printStackTrace();
/* 368 */       this.newlog.error(paramString + "转码失败：", exception);
/*     */     } 
/* 370 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRandomPassword(int paramInt) {
/* 379 */     char[] arrayOfChar = new char[paramInt];
/* 380 */     byte b = 0;
/* 381 */     while (b < paramInt) {
/* 382 */       int i = (int)(Math.random() * 3.0D);
/* 383 */       if (i == 0) {
/* 384 */         arrayOfChar[b] = (char)(int)(65.0D + Math.random() * 26.0D);
/* 385 */       } else if (i == 1) {
/* 386 */         arrayOfChar[b] = (char)(int)(97.0D + Math.random() * 26.0D);
/*     */       } else {
/* 388 */         arrayOfChar[b] = (char)(int)(48.0D + Math.random() * 10.0D);
/* 389 */       }  b++;
/*     */     } 
/* 391 */     return new String(arrayOfChar);
/*     */   }
/*     */ 
/*     */   
/*     */   private String getWYDeptId(String paramString1, String paramString2) {
/* 396 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 398 */     recordSet.executeQuery("SELECT mailid FROM mail_dep_map WHERE weavertype='2' AND weaverid=? and mailmasterid=?", new Object[] { paramString1, paramString2 });
/* 399 */     if (recordSet.next()) {
/* 400 */       return recordSet.getString("mailid");
/*     */     }
/* 402 */     return "1";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/wymail/biz/HrmResourceSubscriber4WY.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */