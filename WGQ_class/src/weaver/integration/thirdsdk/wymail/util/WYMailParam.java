/*    */ package weaver.integration.thirdsdk.wymail.util;
/*    */ 
/*    */ import java.net.URLEncoder;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WYMailParam
/*    */ {
/*    */   public static String getWYMailSignParam(Map<String, Object> paramMap, String paramString1, String paramString2, String paramString3, int paramInt) {
/* 23 */     paramMap.put("domain", paramString1);
/* 24 */     paramMap.put("product", paramString2);
/* 25 */     paramMap.put("time", paramString3);
/* 26 */     paramMap.remove("canceled");
/* 27 */     paramMap.remove("id");
/* 28 */     paramMap.remove("supsubcomid");
/* 29 */     paramMap.remove("supdepid");
/* 30 */     paramMap.remove("subcompanyid1");
/* 31 */     paramMap.remove("showorder");
/* 32 */     String str = "";
/* 33 */     if (paramInt == 1) {
/* 34 */       for (String str1 : paramMap.keySet()) {
/* 35 */         str = str + str1 + "=" + paramMap.get(str1) + "&";
/*    */       }
/*    */     } else {
/* 38 */       for (String str1 : paramMap.keySet()) {
/* 39 */         String str2 = Util.null2String(paramMap.get(str1));
/* 40 */         if (checkStrHasChinese(str2))
/*    */         {
/* 42 */           str2 = doEncode(str2);
/*    */         }
/*    */         
/* 45 */         str = str + str1 + "=" + str2 + "&";
/*    */       } 
/*    */     } 
/* 48 */     if (!"".equals(str)) {
/* 49 */       str = str.substring(0, str.length() - 1);
/*    */     }
/* 51 */     return str;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean checkStrHasChinese(String paramString) {
/* 58 */     for (char c : paramString.toCharArray()) {
/* 59 */       if (c >= '一' && c <= '龥') {
/* 60 */         return true;
/*    */       }
/*    */     } 
/* 63 */     return false;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private static String doEncode(String paramString) {
/* 72 */     String str = "";
/*    */     try {
/* 74 */       str = URLEncoder.encode(paramString, "utf-8");
/* 75 */     } catch (Exception exception) {
/* 76 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 79 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/wymail/util/WYMailParam.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */