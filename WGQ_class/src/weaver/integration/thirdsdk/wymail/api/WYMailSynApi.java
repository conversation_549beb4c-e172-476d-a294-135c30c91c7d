/*     */ package weaver.integration.thirdsdk.wymail.api;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.hrm.util.face.HrmFaceCheckManager;
/*     */ import com.engine.integration.dao.MailDao;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.framework.data.record.SimpleRecordData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.mail.WYUtil.rsa.RSASignatureToQiye;
/*     */ import weaver.integration.util.SignUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WYMailSynApi
/*     */ {
/*  26 */   private Logger newlog = LoggerFactory.getLogger(WYMailSynApi.class);
/*  27 */   private static WYMailSynApi instance = null;
/*  28 */   private static User user = null;
/*  29 */   private static String wyDoamin = null;
/*  30 */   private static String wyProduct = null;
/*  31 */   private static String wyPriKey = null;
/*  32 */   private static String wyUrl = null;
/*  33 */   private static String wyTopDeptid = null;
/*  34 */   private static String mailMasterId = null;
/*  35 */   private static String bindField = null;
/*  36 */   private MailDao mailDao = new MailDao();
/*     */   
/*     */   private WYMailSynApi(User paramUser) {
/*  39 */     this; user = paramUser;
/*     */   }
/*     */   
/*     */   public static synchronized WYMailSynApi getInstance(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/*  43 */     if (instance == null) {
/*  44 */       synchronized (WYMailSynApi.class) {
/*  45 */         if (instance == null) {
/*  46 */           instance = new WYMailSynApi(paramUser);
/*     */         }
/*     */       } 
/*     */     }
/*  50 */     doInit(paramMap1, paramMap2);
/*  51 */     return instance;
/*     */   }
/*     */   
/*     */   private static void doInit(Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/*  55 */     for (String str : paramMap2.keySet()) {
/*  56 */       if ("email163_domain".equals(str)) {
/*  57 */         wyDoamin = Util.null2String(paramMap2.get(str)); continue;
/*  58 */       }  if ("email163_product".equals(str)) {
/*  59 */         wyProduct = Util.null2String(paramMap2.get(str)); continue;
/*  60 */       }  if ("email163_key".equals(str)) {
/*  61 */         wyPriKey = Util.null2String(paramMap2.get(str)); continue;
/*  62 */       }  if ("email163_url".equals(str)) {
/*  63 */         wyUrl = Util.null2String(paramMap2.get(str)); continue;
/*  64 */       }  if ("email163_topdeptid".equals(str)) {
/*  65 */         wyTopDeptid = Util.null2String(paramMap2.get(str));
/*     */       }
/*     */     } 
/*  68 */     mailMasterId = Util.null2String(paramMap1.get("id"));
/*  69 */     bindField = Util.null2String(paramMap1.get("bindfield"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String doEncode(String paramString) {
/*  76 */     String str = "";
/*     */     try {
/*  78 */       str = URLEncoder.encode(paramString, "utf-8");
/*  79 */     } catch (Exception exception) {
/*  80 */       exception.printStackTrace();
/*  81 */       this.newlog.error(paramString + "转码失败：", exception);
/*     */     } 
/*  83 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized JSONObject initOrgAndUser(User paramUser) throws Exception {
/*  93 */     JSONObject jSONObject = new JSONObject();
/*     */     
/*     */     try {
/*  96 */       this.newlog.error("------------------------------初始化OA组织架构和人员到网易邮箱开始------------------------------");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 107 */       syncSubcompany(paramUser);
/*     */       
/* 109 */       syncDepartment(paramUser);
/*     */       
/* 111 */       syncUser(paramUser);
/*     */       
/* 113 */       this.newlog.error("------------------------------初始化OA组织架构和人员到网易邮箱结束------------------------------");
/* 114 */       return null;
/* 115 */     } catch (Exception exception) {
/* 116 */       this.newlog.error("OA组织架构和人员初始化到网易邮箱，出现异常：", exception);
/* 117 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private synchronized void syncSubcompany(User paramUser) {
/* 126 */     List<Map<String, Object>> list = HrmFaceCheckManager.syncBatch("0", HrmFaceCheckManager.getOaSubcompany(), true);
/* 127 */     if (list != null && list.size() > 0) {
/* 128 */       syncSubcompanyByList(list, paramUser);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void syncSubcompanyByList(List<Map<String, Object>> paramList, User paramUser) {
/* 138 */     if (paramList != null && paramList.size() > 0) {
/*     */       
/* 140 */       RecordSet recordSet1 = new RecordSet();
/* 141 */       RecordSet recordSet2 = new RecordSet();
/* 142 */       for (Map<String, Object> map : paramList) {
/* 143 */         String str1 = System.currentTimeMillis() + "";
/* 144 */         List<Map<String, Object>> list = (List)map.get("children");
/* 145 */         SimpleRecordData simpleRecordData = (SimpleRecordData)map.get("simpleRecordData");
/* 146 */         String str2 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("canceled")).getFieldValue());
/* 147 */         String str3 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("id")).getFieldValue());
/* 148 */         String str4 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("subcompanyname")).getFieldValue());
/* 149 */         String str5 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("supsubcomid")).getFieldValue());
/* 150 */         int i = Util.getIntValue(Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("showorder")).getFieldValue()), 0);
/* 151 */         if (str5 == null || "".equals(str5)) {
/* 152 */           str5 = "0";
/*     */         }
/*     */         
/* 155 */         if (i < 0) {
/* 156 */           i = 0;
/*     */         }
/*     */         
/* 159 */         String str6 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 160 */         recordSet1.executeQuery(str6, new Object[] { str3, mailMasterId });
/* 161 */         boolean bool = recordSet1.next();
/* 162 */         JSONObject jSONObject = new JSONObject();
/* 163 */         if (bool) {
/*     */           
/* 165 */           String str8 = recordSet1.getString("mailid");
/* 166 */           String str9 = recordSet1.getString("mailpid");
/*     */           
/* 168 */           boolean bool1 = false;
/* 169 */           List list1 = WYMailApi.getAllALYDeptids(wyDoamin, wyProduct, wyPriKey, wyUrl);
/* 170 */           if (list1.contains(str8)) {
/* 171 */             bool1 = true;
/*     */           }
/* 173 */           if (bool1) {
/* 174 */             if (!"1".equals(str2)) {
/*     */               
/* 176 */               JSONObject jSONObject1 = new JSONObject();
/* 177 */               String str10 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 178 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 179 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 180 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 181 */               String str11 = "";
/* 182 */               if ("0".equals(str5)) {
/* 183 */                 str11 = wyTopDeptid;
/*     */               } else {
/* 185 */                 recordSet2.executeQuery(str10, new Object[] { str5, mailMasterId });
/* 186 */                 if (recordSet2.next()) {
/* 187 */                   str11 = recordSet2.getString("mailid");
/*     */                 }
/*     */               } 
/* 190 */               if (!str11.equals(str9)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 196 */                 HashMap<Object, Object> hashMap = new HashMap<>();
/* 197 */                 hashMap.put("domain", wyDoamin);
/* 198 */                 hashMap.put("new_parent_id", str11);
/* 199 */                 hashMap.put("product", wyProduct);
/* 200 */                 hashMap.put("time", str1);
/* 201 */                 hashMap.put("unit_id", str8);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 207 */                 String str16 = SignUtil.linkParameters(hashMap);
/*     */                 
/* 209 */                 String str17 = RSASignatureToQiye.generateSigature(wyPriKey, str16);
/* 210 */                 String str18 = str16 + "&sign=" + str17;
/* 211 */                 jSONObject = WYMailApi.updateDepartmentSupid(str18, wyUrl);
/* 212 */                 if (jSONObject.getBoolean("suc").booleanValue())
/*     */                 {
/* 214 */                   recordSet2.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str4, str11, str8, mailMasterId });
/*     */                 }
/*     */               } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 226 */               HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 227 */               hashMap4.put("domain", wyDoamin);
/* 228 */               hashMap4.put("product", wyProduct);
/* 229 */               hashMap4.put("time", str1);
/* 230 */               hashMap4.put("unit_desc", str4);
/* 231 */               hashMap4.put("unit_id", str8);
/* 232 */               hashMap4.put("unit_name", str4);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 239 */               String str12 = SignUtil.linkParameters(hashMap4);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 245 */               String str13 = "domain=" + wyDoamin + "&product=" + wyProduct + "&time=" + str1 + "&unit_desc=" + doEncode(str4) + "&unit_id=" + str8 + "&unit_name=" + doEncode(str4);
/*     */               
/* 247 */               String str14 = RSASignatureToQiye.generateSigature(wyPriKey, str12);
/* 248 */               String str15 = str13 + "&sign=" + str14;
/* 249 */               jSONObject = WYMailApi.updateDepartment(str15, wyUrl);
/* 250 */               jSONObject.put("operateType", "4");
/* 251 */               if (jSONObject.getBoolean("suc").booleanValue()) {
/* 252 */                 recordSet2.executeUpdate("update mail_dep_map set name=? where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str4, str8, mailMasterId });
/*     */               
/*     */               }
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */               
/* 260 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 261 */               hashMap.put("domain", wyDoamin);
/* 262 */               hashMap.put("product", wyProduct);
/* 263 */               hashMap.put("time", str1);
/* 264 */               hashMap.put("unit_id", str8);
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 269 */               String str10 = SignUtil.linkParameters(hashMap);
/*     */               
/* 271 */               String str11 = RSASignatureToQiye.generateSigature(wyPriKey, str10);
/* 272 */               String str12 = str10 + "&sign=" + str11;
/* 273 */               jSONObject = WYMailApi.deleteDepartment(str12, wyUrl);
/* 274 */               jSONObject.put("operateType", "4");
/* 275 */               if (jSONObject.getBoolean("suc").booleanValue())
/*     */               {
/* 277 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='1' and mailid=? and mailmasterid=?", new Object[] { str8, mailMasterId });
/*     */               }
/*     */             } 
/*     */           } else {
/*     */             
/* 282 */             String str10 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 283 */             if ("0".equals(str5)) {
/* 284 */               str9 = wyTopDeptid;
/*     */             } else {
/* 286 */               recordSet2.executeQuery(str10, new Object[] { str5, mailMasterId });
/* 287 */               if (recordSet2.next()) {
/* 288 */                 str9 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 304 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 305 */             hashMap.put("domain", wyDoamin);
/* 306 */             hashMap.put("parent_id", str9);
/* 307 */             hashMap.put("product", wyProduct);
/* 308 */             hashMap.put("time", str1);
/* 309 */             hashMap.put("unit_desc", str4);
/* 310 */             hashMap.put("unit_name", str4);
/*     */ 
/*     */             
/* 313 */             String str11 = SignUtil.linkParameters(hashMap);
/*     */             
/* 315 */             String str12 = RSASignatureToQiye.generateSigature(wyPriKey, str11);
/*     */             
/* 317 */             String str13 = "domain=" + wyDoamin + "&parent_id=" + str9 + "&product=" + wyProduct + "&time=" + str1 + "&unit_desc=" + doEncode(str4) + "&unit_name=" + doEncode(str4);
/* 318 */             String str14 = str13 + "&sign=" + str12;
/* 319 */             jSONObject = WYMailApi.createDepartment(str14, wyUrl);
/* 320 */             jSONObject.put("operateType", "4");
/* 321 */             if (jSONObject.getBoolean("suc").booleanValue()) {
/* 322 */               String str = jSONObject.getJSONObject("con").getString("unit_id");
/*     */               
/* 324 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str, str9, str3, "1", str4, mailMasterId });
/*     */             }
/*     */           
/*     */           }
/*     */         
/* 329 */         } else if (!"1".equals(str2)) {
/*     */           
/* 331 */           String str8 = wyTopDeptid;
/* 332 */           String str9 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 333 */           recordSet2.executeQuery(str9, new Object[] { str5, mailMasterId });
/* 334 */           if (recordSet2.next()) {
/* 335 */             str8 = recordSet2.getString("mailid");
/*     */           }
/* 337 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 338 */           hashMap.put("domain", wyDoamin);
/* 339 */           hashMap.put("parent_id", str8);
/* 340 */           hashMap.put("product", wyProduct);
/* 341 */           hashMap.put("time", str1);
/* 342 */           hashMap.put("unit_desc", str4);
/* 343 */           hashMap.put("unit_name", str4);
/*     */ 
/*     */           
/* 346 */           String str10 = SignUtil.linkParameters(hashMap);
/*     */           
/* 348 */           String str11 = RSASignatureToQiye.generateSigature(wyPriKey, str10);
/*     */           
/* 350 */           String str12 = "domain=" + wyDoamin + "&parent_id=" + str8 + "&product=" + wyProduct + "&time=" + str1 + "&unit_desc=" + doEncode(str4) + "&unit_name=" + doEncode(str4);
/* 351 */           String str13 = str12 + "&sign=" + str11;
/* 352 */           jSONObject = WYMailApi.createDepartment(str13, wyUrl);
/* 353 */           jSONObject.put("operateType", "4");
/* 354 */           if (jSONObject.getBoolean("suc").booleanValue()) {
/* 355 */             String str = jSONObject.getJSONObject("con").getString("unit_id");
/*     */             
/* 357 */             recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str, str8, str3, "1", str4, mailMasterId });
/*     */           } 
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */         
/* 364 */         String str7 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 384 */         if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/* 385 */           recordSet2.executeUpdate(str7, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "1", jSONObject.getString("operateType"), "1", "", mailMasterId });
/* 386 */           this.newlog.error("分部：(" + str4 + ")，上级分部：" + str5 + "，同步成功！");
/* 387 */         } else if (jSONObject != null) {
/* 388 */           recordSet2.executeUpdate(str7, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "1", jSONObject.getString("operateType"), "0", jSONObject.getString("error_code"), mailMasterId });
/* 389 */           this.newlog.error("分部：(" + str4 + ")，上级分部：" + str5 + "，同步失败，code=" + jSONObject.getString("error_code") + "，msg=" + jSONObject.getString("error_code"));
/*     */         } 
/*     */         
/* 392 */         if (list != null && list.size() > 0) {
/* 393 */           syncSubcompanyByList(list, paramUser);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private synchronized void syncDepartment(User paramUser) {
/* 405 */     List<Map<String, Object>> list = HrmFaceCheckManager.syncBatch("0", HrmFaceCheckManager.getOaDepartment(), true);
/* 406 */     if (list != null && list.size() > 0) {
/* 407 */       syncDepartmentByList(list, paramUser);
/*     */     }
/*     */   }
/*     */   
/*     */   private void syncDepartmentByList(List<Map<String, Object>> paramList, User paramUser) {
/* 412 */     if (paramList != null && paramList.size() > 0) {
/* 413 */       RecordSet recordSet1 = new RecordSet();
/* 414 */       RecordSet recordSet2 = new RecordSet();
/* 415 */       for (Map<String, Object> map : paramList) {
/* 416 */         String str1 = System.currentTimeMillis() + "";
/* 417 */         List<Map<String, Object>> list = (List)map.get("children");
/* 418 */         SimpleRecordData simpleRecordData = (SimpleRecordData)map.get("simpleRecordData");
/* 419 */         String str2 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("canceled")).getFieldValue());
/* 420 */         String str3 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("id")).getFieldValue());
/* 421 */         String str4 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("departmentname")).getFieldValue());
/* 422 */         String str5 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("subcompanyid1")).getFieldValue());
/* 423 */         String str6 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("supdepid")).getFieldValue());
/* 424 */         int i = Util.getIntValue(Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("showorder")).getFieldValue()), 0);
/* 425 */         if (str6 == null || "".equals(str6)) {
/* 426 */           str6 = "0";
/*     */         }
/* 428 */         if (i < 0) {
/* 429 */           i = 0;
/*     */         }
/* 431 */         String str7 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 432 */         recordSet1.executeQuery(str7, new Object[] { str3, mailMasterId });
/* 433 */         boolean bool = recordSet1.next();
/* 434 */         JSONObject jSONObject = new JSONObject();
/* 435 */         if (bool) {
/*     */           
/* 437 */           String str9 = recordSet1.getString("mailid");
/* 438 */           String str10 = recordSet1.getString("mailpid");
/*     */           
/* 440 */           boolean bool1 = false;
/* 441 */           List list1 = WYMailApi.getAllALYDeptids(wyDoamin, wyProduct, wyPriKey, wyUrl);
/* 442 */           if (list1.contains(str9)) {
/* 443 */             bool1 = true;
/*     */           }
/* 445 */           if (bool1) {
/* 446 */             if (!"1".equals(str2)) {
/* 447 */               JSONObject jSONObject1 = new JSONObject();
/* 448 */               String str11 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 449 */               recordSet2.executeQuery(str11, new Object[] { str6, mailMasterId });
/* 450 */               String str12 = "";
/* 451 */               if (recordSet2.next()) {
/* 452 */                 str12 = recordSet2.getString("mailid");
/*     */               } else {
/* 454 */                 str11 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 455 */                 recordSet2.executeQuery(str11, new Object[] { str5, mailMasterId });
/* 456 */                 if (recordSet2.next()) {
/* 457 */                   str12 = recordSet2.getString("mailid");
/*     */                 }
/*     */               } 
/* 460 */               if (!str12.equals(str10)) {
/* 461 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 462 */                 hashMap1.put("domain", wyDoamin);
/* 463 */                 hashMap1.put("new_parent_id", str12);
/* 464 */                 hashMap1.put("product", wyProduct);
/* 465 */                 hashMap1.put("time", str1);
/* 466 */                 hashMap1.put("unit_id", str9);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 472 */                 String str17 = SignUtil.linkParameters(hashMap1);
/*     */                 
/* 474 */                 String str18 = RSASignatureToQiye.generateSigature(wyPriKey, str17);
/* 475 */                 String str19 = str17 + "&sign=" + str18;
/* 476 */                 jSONObject = WYMailApi.updateDepartmentSupid(str19, wyUrl);
/* 477 */                 if (jSONObject.getBoolean("suc").booleanValue())
/*     */                 {
/* 479 */                   recordSet2.executeUpdate("update mail_dep_map set name=?,mailpid=? where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str4, str12, str9, mailMasterId });
/*     */                 }
/*     */               } 
/*     */               
/* 483 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 484 */               hashMap.put("domain", wyDoamin);
/* 485 */               hashMap.put("product", wyProduct);
/* 486 */               hashMap.put("time", str1);
/* 487 */               hashMap.put("unit_desc", str4);
/* 488 */               hashMap.put("unit_id", str9);
/* 489 */               hashMap.put("unit_name", str4);
/* 490 */               String str13 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 502 */               String str14 = "domain=" + wyDoamin + "&product=" + wyProduct + "&time=" + str1 + "&unit_desc=" + doEncode(str4) + "&unit_id=" + str9 + "&unit_name=" + doEncode(str4);
/*     */               
/* 504 */               String str15 = RSASignatureToQiye.generateSigature(wyPriKey, str13);
/* 505 */               String str16 = str14 + "&sign=" + str15;
/* 506 */               jSONObject = WYMailApi.updateDepartment(str16, wyUrl);
/* 507 */               jSONObject.put("operateType", "4");
/* 508 */               if (jSONObject.getBoolean("suc").booleanValue()) {
/* 509 */                 recordSet2.executeUpdate("update mail_dep_map set name=? where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str4, str9, mailMasterId });
/*     */               }
/*     */             } else {
/*     */               
/* 513 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 514 */               hashMap.put("", wyDoamin);
/* 515 */               hashMap.put("", wyProduct);
/* 516 */               hashMap.put("", str1);
/* 517 */               hashMap.put("unit_id", str9);
/* 518 */               String str11 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 525 */               String str12 = RSASignatureToQiye.generateSigature(wyPriKey, str11);
/* 526 */               String str13 = str11 + "&sign=" + str12;
/* 527 */               jSONObject = WYMailApi.deleteDepartment(str13, wyUrl);
/* 528 */               jSONObject.put("operateType", "4");
/* 529 */               if (jSONObject.getBoolean("suc").booleanValue())
/*     */               {
/* 531 */                 recordSet2.executeUpdate("delete from mail_dep_map where weavertype='2' and mailid=? and mailmasterid=?", new Object[] { str9, mailMasterId });
/*     */               }
/*     */             } 
/*     */           } else {
/*     */             
/* 536 */             String str11 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 537 */             recordSet2.executeQuery(str11, new Object[] { str6, mailMasterId });
/* 538 */             if (recordSet2.next()) {
/* 539 */               str10 = recordSet2.getString("mailid");
/*     */             } else {
/* 541 */               str11 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 542 */               recordSet2.executeQuery(str11, new Object[] { str5, mailMasterId });
/* 543 */               if (recordSet2.next()) {
/* 544 */                 str10 = recordSet2.getString("mailid");
/*     */               }
/*     */             } 
/* 547 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 548 */             hashMap.put("domain", wyDoamin);
/* 549 */             hashMap.put("parent_id", str10);
/* 550 */             hashMap.put("product", wyProduct);
/* 551 */             hashMap.put("time", str1);
/* 552 */             hashMap.put("unit_desc", str4);
/* 553 */             hashMap.put("unit_name", str4);
/* 554 */             String str12 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */             
/* 558 */             String str13 = RSASignatureToQiye.generateSigature(wyPriKey, str12);
/*     */             
/* 560 */             String str14 = "domain=" + wyDoamin + "&parent_id=" + str10 + "&product=" + wyProduct + "&time=" + str1 + "&unit_desc=" + doEncode(str4) + "&unit_name=" + doEncode(str4);
/* 561 */             String str15 = str14 + "&sign=" + str13;
/* 562 */             jSONObject = WYMailApi.createDepartment(str15, wyUrl);
/* 563 */             jSONObject.put("operateType", "4");
/* 564 */             if (jSONObject.getBoolean("suc").booleanValue()) {
/* 565 */               String str = jSONObject.getJSONObject("con").getString("unit_id");
/*     */               
/* 567 */               recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str, str10, str3, "2", str4, mailMasterId });
/*     */             }
/*     */           
/*     */           }
/*     */         
/* 572 */         } else if (!"1".equals(str2)) {
/*     */           
/* 574 */           String str9 = "";
/* 575 */           String str10 = "SELECT * FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?";
/* 576 */           recordSet2.executeQuery(str10, new Object[] { str6, mailMasterId });
/* 577 */           if (recordSet2.next()) {
/* 578 */             str9 = recordSet2.getString("mailid");
/*     */           } else {
/* 580 */             str10 = "SELECT * FROM mail_dep_map WHERE weavertype='1' AND weaverid=? AND mailmasterid=?";
/* 581 */             recordSet2.executeQuery(str10, new Object[] { str5, mailMasterId });
/* 582 */             if (recordSet2.next()) {
/* 583 */               str9 = recordSet2.getString("mailid");
/*     */             }
/*     */           } 
/* 586 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 587 */           hashMap.put("domain", wyDoamin);
/* 588 */           hashMap.put("parent_id", str9);
/* 589 */           hashMap.put("product", wyProduct);
/* 590 */           hashMap.put("time", str1);
/* 591 */           hashMap.put("unit_desc", str4);
/* 592 */           hashMap.put("unit_name", str4);
/* 593 */           String str11 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */           
/* 597 */           String str12 = RSASignatureToQiye.generateSigature(wyPriKey, str11);
/*     */           
/* 599 */           String str13 = "domain=" + wyDoamin + "&parent_id=" + str9 + "&product=" + wyProduct + "&time=" + str1 + "&unit_desc=" + doEncode(str4) + "&unit_name=" + doEncode(str4);
/* 600 */           String str14 = str13 + "&sign=" + str12;
/* 601 */           jSONObject = WYMailApi.createDepartment(str14, wyUrl);
/* 602 */           jSONObject.put("operateType", "4");
/* 603 */           if (jSONObject.getBoolean("suc").booleanValue()) {
/* 604 */             String str = jSONObject.getJSONObject("con").getString("unit_id");
/*     */             
/* 606 */             recordSet2.executeUpdate("insert into mail_dep_map(mailid,mailpid,weaverid,weavertype,name,mailmasterid) values (?,?,?,?,?,?)", new Object[] { str, str9, str3, "2", str4, mailMasterId });
/*     */           } 
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */         
/* 613 */         String str8 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 633 */         if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/* 634 */           recordSet2.executeUpdate(str8, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "2", jSONObject.getString("operateType"), "1", "", mailMasterId });
/* 635 */           this.newlog.error("部门：(" + str4 + ")，上级部门：" + str6 + "，同步成功！");
/* 636 */         } else if (jSONObject != null) {
/* 637 */           recordSet2.executeUpdate(str8, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "2", jSONObject.getString("operateType"), "0", jSONObject.getString("error_code"), mailMasterId });
/* 638 */           this.newlog.error("分部：(" + str4 + ")，上级分部：" + str6 + "，同步失败，code=" + jSONObject.getString("error_code") + "，msg=" + jSONObject.getString("error_code"));
/*     */         } 
/*     */         
/* 641 */         if (list != null && list.size() > 0) {
/* 642 */           syncDepartmentByList(list, paramUser);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private synchronized void syncUser(User paramUser) {
/* 654 */     List<Map<String, Object>> list = HrmFaceCheckManager.syncBatch("0", HrmFaceCheckManager.getOaResource(), true);
/* 655 */     if (list != null && list.size() > 0) {
/* 656 */       syncUserByList(list, paramUser);
/*     */     }
/*     */   }
/*     */   
/*     */   private void syncUserByList(List<Map<String, Object>> paramList, User paramUser) {
/* 661 */     if (paramList != null && paramList.size() > 0) {
/* 662 */       RecordSet recordSet = new RecordSet();
/* 663 */       for (Map<String, Object> map : paramList) {
/* 664 */         String str = System.currentTimeMillis() + "";
/* 665 */         List<Map<String, Object>> list = (List)map.get("children");
/* 666 */         SimpleRecordData simpleRecordData = (SimpleRecordData)map.get("simpleRecordData");
/*     */         try {
/* 668 */           String str1 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("id")).getFieldValue());
/* 669 */           String str2 = this.mailDao.getBindFieldValue(bindField, str1 + "");
/* 670 */           String str3 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("loginid")).getFieldValue());
/*     */           
/* 672 */           String str4 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("lastname")).getFieldValue());
/* 673 */           String str5 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("sex")).getFieldValue());
/*     */           
/* 675 */           String str6 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("telephone")).getFieldValue());
/* 676 */           String str7 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("mobile")).getFieldValue());
/* 677 */           String str8 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("email")).getFieldValue());
/* 678 */           String str9 = "";
/* 679 */           String str10 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("departmentid")).getFieldValue());
/* 680 */           String str11 = Util.null2String(((FieldData)simpleRecordData.getRecordData().getFieldDataMap().get("status")).getFieldValue());
/*     */           
/* 682 */           String str12 = getRandomPassword(16);
/*     */           
/* 684 */           JSONObject jSONObject = new JSONObject();
/* 685 */           if ("".equals(str2) || str2.indexOf("@") <= -1 || !wyDoamin.equals(str2.substring(str2.indexOf("@") + 1, str2.length()))) {
/* 686 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str3 + "，" + bindField + "=" + str2 + "，所属部门：" + str10 + "，邮箱地址错误！");
/*     */             continue;
/*     */           } 
/* 689 */           str9 = str2.substring(0, str2.indexOf("@"));
/*     */ 
/*     */           
/* 692 */           Map<String, Object> map1 = WYMailApi.checkUser(str9, wyDoamin, wyProduct, wyPriKey, wyUrl);
/* 693 */           if (((Boolean)map1.get("checkUser")).booleanValue()) {
/*     */             
/* 695 */             if (str11.equals("0") || str11.equals("1") || str11.equals("2") || str11.equals("3")) {
/* 696 */               String str14 = Util.null2String(map1.get("wyUserDept"));
/* 697 */               if ("0".equals(Util.null2String(map1.get("status")))) {
/*     */                 
/* 699 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 700 */                 hashMap1.put("account_name", str9);
/* 701 */                 hashMap1.put("domain", wyDoamin);
/* 702 */                 hashMap1.put("product", wyProduct);
/* 703 */                 hashMap1.put("time", str);
/* 704 */                 String str20 = SignUtil.linkParameters(hashMap1);
/*     */ 
/*     */                 
/* 707 */                 String str21 = RSASignatureToQiye.generateSigature(wyPriKey, str20);
/* 708 */                 String str22 = str20 + "&sign=" + str21;
/* 709 */                 WYMailApi.hfHrmresource(str22, wyUrl);
/*     */               } 
/* 711 */               String str15 = getWYDeptId(str10);
/*     */               
/* 713 */               if (!str14.equals(str15)) {
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 718 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 719 */                 hashMap1.put("account_name", str9);
/* 720 */                 hashMap1.put("domain", wyDoamin);
/* 721 */                 hashMap1.put("product", wyProduct);
/* 722 */                 hashMap1.put("time", str);
/* 723 */                 hashMap1.put("unit_id", str15);
/* 724 */                 String str20 = SignUtil.linkParameters(hashMap1);
/*     */ 
/*     */ 
/*     */                 
/* 728 */                 String str21 = RSASignatureToQiye.generateSigature(wyPriKey, str20);
/* 729 */                 String str22 = str20 + "&sign=" + str21;
/* 730 */                 WYMailApi.updateHrmToDept(str22, wyUrl);
/*     */               } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 737 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 738 */               hashMap.put("account_name", str9);
/* 739 */               hashMap.put("domain", wyDoamin);
/* 740 */               hashMap.put("gender", str5);
/* 741 */               hashMap.put("mobile", str7);
/* 742 */               hashMap.put("nickname", str4);
/* 743 */               hashMap.put("tel", str6);
/* 744 */               hashMap.put("product", wyProduct);
/* 745 */               hashMap.put("time", str);
/* 746 */               String str16 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 753 */               String str17 = RSASignatureToQiye.generateSigature(wyPriKey, str16);
/* 754 */               System.out.println(str17);
/*     */ 
/*     */               
/* 757 */               String str18 = "account_name=" + str9 + "&domain=" + wyDoamin + "&gender=" + str5 + "&mobile=" + str7 + "&nickname=" + doEncode(str4) + "&tel=" + str6 + "&product=" + wyProduct + "&time=" + str;
/*     */ 
/*     */               
/* 760 */               String str19 = str18 + "&sign=" + str17;
/* 761 */               jSONObject = WYMailApi.updateHrmresource(str19, wyUrl);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 774 */               jSONObject.put("operateType", "4");
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */               
/*     */               continue;
/*     */             
/*     */             }
/*     */           
/*     */           }
/* 785 */           else if (str11.equals("0") || str11.equals("1") || str11.equals("2") || str11.equals("3")) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 800 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 801 */             hashMap.put("account_name", str9);
/* 802 */             hashMap.put("addr_right", "1");
/* 803 */             hashMap.put("addr_visible", "1");
/* 804 */             hashMap.put("domain", wyDoamin);
/* 805 */             hashMap.put("gender", str5);
/* 806 */             hashMap.put("mobile", str7);
/* 807 */             hashMap.put("nickname", str4);
/* 808 */             hashMap.put("pass_type", "0");
/* 809 */             hashMap.put("passchange_req", "0");
/* 810 */             hashMap.put("password", str12);
/* 811 */             hashMap.put("product", wyProduct);
/* 812 */             hashMap.put("tel", str6);
/* 813 */             hashMap.put("time", str);
/* 814 */             hashMap.put("unit_id", getWYDeptId(str10));
/* 815 */             String str14 = SignUtil.linkParameters(hashMap);
/*     */ 
/*     */             
/* 818 */             String str15 = RSASignatureToQiye.generateSigature(wyPriKey, str14);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 824 */             String str16 = str14 + "&sign=" + str15;
/* 825 */             jSONObject = WYMailApi.createHrmresource(str16, wyUrl);
/* 826 */             if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/*     */               
/* 828 */               String str17 = "INSERT INTO MailDefaultPwd\n        ( userid ,\n          loginid ,\n          lastname ,\n          defaultpwd ,\n          createtime,\n          mailmasterid\n        )\nVALUES  ( ? ,\n          ? ,\n          ? ,\n          ? ,\n          ? ,\n          ?  \n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 843 */               recordSet.executeUpdate(str17, new Object[] { str1, str3, str4, str12, TimeUtil.getCurrentTimeString(), mailMasterId });
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 848 */             jSONObject.put("operateType", "4");
/*     */           } else {
/*     */             continue;
/*     */           } 
/*     */ 
/*     */           
/* 854 */           String str13 = "INSERT INTO integrationmailog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary,\n          mailmasterid\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 874 */           if (jSONObject != null && jSONObject.getBoolean("suc").booleanValue()) {
/* 875 */             recordSet.executeUpdate(str13, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "3", jSONObject.getString("operateType"), "1", "", mailMasterId });
/* 876 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str3 + "，email=" + str8 + "，所属部门：" + str10 + "，同步成功！");
/* 877 */           } else if (jSONObject != null) {
/* 878 */             recordSet.executeUpdate(str13, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str4, "3", jSONObject.getString("operateType"), "0", jSONObject.getString("error_code"), mailMasterId });
/* 879 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str3 + "，email=" + str8 + "，所属部门：" + str10 + "，同步失败，code=" + jSONObject.getString("error_code") + "，msg=" + jSONObject.getString("error_code"));
/*     */           } 
/* 881 */         } catch (Exception exception) {
/* 882 */           this.newlog.error("网易邮箱初始化人员时异常！", exception);
/*     */         } 
/*     */         
/* 885 */         if (list != null && list.size() > 0) {
/* 886 */           syncUserByList(list, paramUser);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private String getWYDeptId(String paramString) {
/* 893 */     RecordSet recordSet = new RecordSet();
/* 894 */     recordSet.executeQuery("SELECT mailid FROM mail_dep_map WHERE weavertype='2' AND weaverid=? AND mailmasterid=?", new Object[] { paramString, mailMasterId });
/* 895 */     if (recordSet.next()) {
/* 896 */       return recordSet.getString("mailid");
/*     */     }
/* 898 */     return "1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getPsw(int paramInt) {
/* 908 */     String str1 = null;
/*     */     
/* 910 */     String str2 = "0123456789abcdefghijklABCDEFGHIJKL!@#$";
/* 911 */     boolean bool = false;
/*     */     
/* 913 */     while (!bool) {
/*     */       
/* 915 */       str1 = "";
/*     */       
/* 917 */       byte b1 = 0, b2 = 0, b3 = 0, b4 = 0;
/* 918 */       for (byte b5 = 0; b5 < paramInt; b5++) {
/* 919 */         int i = (int)(Math.random() * str2.length());
/* 920 */         str1 = str1 + str2.charAt(i);
/* 921 */         if (0 <= i && i <= 9) {
/* 922 */           b1++;
/*     */         }
/* 924 */         if (10 <= i && i <= 35) {
/* 925 */           b2++;
/*     */         }
/* 927 */         if (36 <= i && i <= 61) {
/* 928 */           b3++;
/*     */         }
/* 930 */         if (62 <= i && i < str2.length()) {
/* 931 */           b4++;
/*     */         }
/*     */       } 
/*     */       
/* 935 */       bool = ((b1 * b2 * b3 != 0 && b4 == 0) || (b1 * b2 * b4 != 0 && b3 == 0) || (b1 * b3 * b4 != 0 && b2 == 0) || (b2 * b3 * b4 != 0 && b1 == 0)) ? true : false;
/*     */     } 
/* 937 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRandomPassword(int paramInt) {
/* 945 */     char[] arrayOfChar = new char[paramInt];
/* 946 */     byte b = 0;
/* 947 */     while (b < paramInt) {
/* 948 */       int i = (int)(Math.random() * 3.0D);
/* 949 */       if (i == 0) {
/* 950 */         arrayOfChar[b] = (char)(int)(65.0D + Math.random() * 26.0D);
/* 951 */       } else if (i == 1) {
/* 952 */         arrayOfChar[b] = (char)(int)(97.0D + Math.random() * 26.0D);
/*     */       } else {
/* 954 */         arrayOfChar[b] = (char)(int)(48.0D + Math.random() * 10.0D);
/* 955 */       }  b++;
/*     */     } 
/* 957 */     return new String(arrayOfChar);
/*     */   }
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 962 */     String str1 = "{\n\t\"status\": {\n\t\t\"statusMsg\": \"xx\",\n\t\t\"statusCode\": \"xx\"\n\t},\n\t\"data\": {\n\t\t\"accessToken\": \"xx\"\n\t}\n}";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 972 */     JSONObject jSONObject1 = JSONObject.parseObject(str1);
/* 973 */     JSONObject jSONObject2 = jSONObject1.getJSONObject("data");
/* 974 */     String str2 = jSONObject2.getString("accessToken");
/* 975 */     System.out.println("map1===" + str2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/wymail/api/WYMailSynApi.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */