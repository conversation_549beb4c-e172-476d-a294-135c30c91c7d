/*     */ package weaver.integration.thirdsdk.coremail.biz;
/*     */ 
/*     */ import com.weaver.integration.ldap.util.AuthenticUtil;
/*     */ import java.net.Socket;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import tebie.applib.api.APIContext;
/*     */ import tebie.applib.api.IClient;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.MD5;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.email.CoreMailUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmResourceSubscriber4CoreMail
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  32 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  34 */     if (paramList != null) {
/*  35 */       String str1 = "";
/*  36 */       String str2 = "";
/*  37 */       String str3 = "";
/*  38 */       String str4 = "";
/*  39 */       String str5 = "";
/*  40 */       String str6 = "";
/*  41 */       String str7 = "";
/*  42 */       String str8 = "";
/*  43 */       String str9 = "";
/*  44 */       String str10 = "";
/*  45 */       String str11 = "";
/*  46 */       String str12 = "";
/*  47 */       for (FieldData fieldData : paramList) {
/*  48 */         Object object = fieldData.getFieldValue();
/*  49 */         String str = fieldData.getFieldName();
/*     */         
/*  51 */         if ("loginid".equalsIgnoreCase(str)) {
/*  52 */           str2 = object.toString(); continue;
/*  53 */         }  if ("id".equalsIgnoreCase(str)) {
/*  54 */           str1 = object.toString(); continue;
/*  55 */         }  if ("lastname".equalsIgnoreCase(str)) {
/*  56 */           str3 = object.toString(); continue;
/*  57 */         }  if ("sex".equalsIgnoreCase(str)) {
/*  58 */           str4 = object.toString(); continue;
/*  59 */         }  if ("birthday".equalsIgnoreCase(str)) {
/*  60 */           str5 = object.toString(); continue;
/*  61 */         }  if ("telephone".equalsIgnoreCase(str)) {
/*  62 */           str6 = object.toString(); continue;
/*  63 */         }  if ("mobile".equalsIgnoreCase(str)) {
/*  64 */           str7 = object.toString(); continue;
/*  65 */         }  if ("email".equalsIgnoreCase(str)) {
/*  66 */           str8 = object.toString(); continue;
/*  67 */         }  if ("departmentid".equalsIgnoreCase(str)) {
/*  68 */           str9 = object.toString(); continue;
/*  69 */         }  if ("subcompanyid1".equalsIgnoreCase(str)) {
/*  70 */           str10 = object.toString(); continue;
/*  71 */         }  if ("status".equalsIgnoreCase(str)) {
/*  72 */           str11 = object.toString(); continue;
/*  73 */         }  if ("password".equalsIgnoreCase(str)) {
/*  74 */           AuthenticUtil authenticUtil = new AuthenticUtil();
/*  75 */           Map map = authenticUtil.isAuthenticFromAD(str2);
/*  76 */           if (((Boolean)map.get("result")).booleanValue()) {
/*  77 */             str12 = Util.null2String(Prop.getPropValue("coremail", "DEFAULTPASSWORD"));
/*  78 */             if (!"".equals(str12)) {
/*  79 */               MD5 mD5 = new MD5();
/*  80 */               str12 = "{enc2}" + mD5.getMD5ofStr(str12).toLowerCase() + "&encrypted=1";
/*     */             }  continue;
/*     */           } 
/*  83 */           if (object != null) {
/*  84 */             MD5 mD5 = new MD5();
/*  85 */             str12 = object.toString();
/*  86 */             str12 = "{enc2}" + mD5.getMD5ofStr(str12).toLowerCase() + "&encrypted=1";
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*     */       try {
/*  93 */         synUser(str11, str10, str1, str2, str3, str4, str5, str6, str7, str8, str9, str12);
/*  94 */       } catch (Exception exception) {
/*  95 */         this.newlog.error("实时同步人员架构到coremail报错", exception);
/*     */       } 
/*     */     } 
/*  98 */     return null;
/*     */   }
/*     */   
/* 101 */   private Logger newlog = LoggerFactory.getLogger(HrmResourceSubscriber4CoreMail.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean synUser(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10, String paramString11, String paramString12) throws Exception {
/* 111 */     boolean bool = false;
/* 112 */     Socket socket = null;
/* 113 */     IClient iClient = null;
/* 114 */     APIContext aPIContext = null;
/* 115 */     CoreMailUtil coreMailUtil = new CoreMailUtil();
/*     */     
/* 117 */     RecordSet recordSet = new RecordSet();
/* 118 */     String str = "select * from coremailsetting";
/* 119 */     recordSet.executeSql(str);
/* 120 */     while (recordSet.next()) {
/* 121 */       String str1 = Util.null2String(recordSet.getString("bindsubcompany"));
/* 122 */       String str2 = Util.null2String(recordSet.getString("isuse"));
/* 123 */       String str3 = Util.null2String(recordSet.getString("issync"));
/* 124 */       String str4 = Util.null2String(recordSet.getString("systemaddress"));
/* 125 */       int i = Util.getIntValue(recordSet.getString("emailPort"), 6195);
/* 126 */       String str5 = Util.null2String(recordSet.getString("orgid"));
/* 127 */       String str6 = Util.null2String(recordSet.getString("providerid"));
/* 128 */       String str7 = Util.null2String(recordSet.getString("basedomain"));
/* 129 */       String str8 = Util.null2String(recordSet.getString("bindfield"));
/* 130 */       String str9 = "";
/* 131 */       if ("loginid".equalsIgnoreCase(str8)) {
/* 132 */         str9 = paramString4;
/* 133 */       } else if ("lastname".equalsIgnoreCase(str8)) {
/* 134 */         str9 = paramString5;
/* 135 */       } else if ("telephone".equalsIgnoreCase(str8)) {
/* 136 */         str9 = paramString8;
/* 137 */       } else if ("mobile".equalsIgnoreCase(str8)) {
/* 138 */         str9 = paramString9;
/* 139 */       } else if ("email".equalsIgnoreCase(str8)) {
/* 140 */         str9 = paramString10;
/*     */       } 
/* 142 */       if ("0".equals(str2) || !str9.contains(str7)) {
/*     */         continue;
/*     */       }
/* 145 */       if ("0".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */ 
/*     */       
/* 150 */       if (!"".equals(str1)) {
/* 151 */         String[] arrayOfString = str1.split(",");
/* 152 */         List<String> list = Arrays.asList(arrayOfString);
/* 153 */         if (!list.contains(paramString2)) {
/*     */           continue;
/*     */         }
/*     */       } 
/*     */       
/* 158 */       String str10 = "";
/*     */       try {
/* 160 */         socket = new Socket(str4, i);
/* 161 */         iClient = APIContext.getClient(socket);
/*     */         
/* 163 */         if ("0".equals(paramString1) || "1".equals(paramString1) || "2".equals(paramString1) || "3".equals(paramString1)) {
/* 164 */           if (userExist(str9, str4, i)) {
/* 165 */             if ("".equals(paramString12)) {
/* 166 */               aPIContext = iClient.changeAttrs(str9, "nick_name=" + paramString4 + "&true_name=" + paramString5 + "&gender=" + paramString6 + "&birthday=" + paramString7 + "&company_phone=" + paramString8 + "&mobile_number=" + paramString9 + "&user_status=0&org_unit_id=" + paramString11 + "&smsaddr=" + paramString3);
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 176 */               aPIContext = iClient.changeAttrs(str9, "password=" + paramString12 + "&nick_name=" + paramString4 + "&true_name=" + paramString5 + "&gender=" + paramString6 + "&birthday=" + paramString7 + "&company_phone=" + paramString8 + "&mobile_number=" + paramString9 + "&user_status=0&org_unit_id=" + paramString11 + "&smsaddr=" + paramString3);
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 186 */             str10 = "2";
/*     */           } else {
/* 188 */             aPIContext = iClient.createUser(str6, str5, str9.substring(0, str9.indexOf("@")), "domain_name=" + str7 + "&user_status=0&cos_id=1&org_unit_id=" + paramString11);
/*     */ 
/*     */             
/* 191 */             if ("".equals(paramString12)) {
/* 192 */               aPIContext = iClient.changeAttrs(str9, "nick_name=" + paramString4 + "&true_name=" + paramString5 + "&gender=" + paramString6 + "&birthday=" + paramString7 + "&company_phone=" + paramString8 + "&mobile_number=" + paramString9 + "&user_status=0&org_unit_id=" + paramString11 + "&smsaddr=" + paramString3);
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 202 */               aPIContext = iClient.changeAttrs(str9, "password=" + paramString12 + "&nick_name=" + paramString4 + "&true_name=" + paramString5 + "&gender=" + paramString6 + "&birthday=" + paramString7 + "&company_phone=" + paramString8 + "&mobile_number=" + paramString9 + "&user_status=0&org_unit_id=" + paramString11 + "&smsaddr=" + paramString3);
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 213 */             str10 = "1";
/*     */           } 
/*     */           
/* 216 */           if (aPIContext.getRetCode() == 0) {
/* 217 */             coreMailUtil.insertCoreMailSynLog("3", paramString5, str10, "1", "");
/*     */             
/* 219 */             this.newlog.error("实时同步人员到CoreMail邮件系统成功：ID=" + paramString3 + "，" + str8 + "=" + str9 + "，所属部门：" + paramString11 + "，code=" + aPIContext
/* 220 */                 .getRetCode());
/* 221 */             bool = true;
/*     */           } else {
/* 223 */             coreMailUtil.insertCoreMailSynLog("3", paramString5, str10, "2", aPIContext.getErrorInfo());
/*     */             
/* 225 */             this.newlog.error("实时同步人员到CoreMail邮件系统失败：ID=" + paramString3 + "，" + str8 + "=" + str9 + "，所属部门：" + paramString11 + "，code=" + aPIContext
/* 226 */                 .getRetCode() + "，msg=" + aPIContext.getErrorInfo());
/* 227 */             bool = false;
/*     */           } 
/*     */         } else {
/* 230 */           synLeaveUser(paramString3, str8, str9, paramString5, str4, i, str7);
/*     */         }
/*     */       
/* 233 */       } catch (Exception exception) {
/* 234 */         this.newlog.error("实时同步人员到CoreMail邮件系统出现异常：", exception);
/* 235 */         bool = false;
/*     */       } finally {
/* 237 */         if (iClient != null) {
/* 238 */           iClient.close();
/*     */         }
/* 240 */         if (socket != null) {
/* 241 */           socket.close();
/*     */         }
/*     */       } 
/* 244 */       return bool;
/*     */     } 
/* 246 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean userExist(String paramString1, String paramString2, int paramInt) throws Exception {
/* 256 */     boolean bool = false;
/* 257 */     Socket socket = null;
/* 258 */     IClient iClient = null;
/* 259 */     APIContext aPIContext = null;
/*     */     
/*     */     try {
/* 262 */       socket = new Socket(paramString2, paramInt);
/* 263 */       iClient = APIContext.getClient(socket);
/* 264 */       aPIContext = iClient.userExist(paramString1);
/* 265 */       if (aPIContext.getRetCode() == 0) {
/* 266 */         bool = true;
/*     */       }
/* 268 */       this.newlog.error("检测CoreMail邮箱账号是否存在：" + paramString1 + "，getRetCode()=" + aPIContext.getRetCode());
/* 269 */     } catch (Exception exception) {
/* 270 */       this.newlog.error("检测CoreMail邮箱账号是否存在出现异常：", exception);
/*     */     } finally {
/* 272 */       iClient.close();
/* 273 */       socket.close();
/*     */     } 
/* 275 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean synLeaveUser(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, int paramInt, String paramString6) throws Exception {
/* 284 */     boolean bool = false;
/* 285 */     CoreMailUtil coreMailUtil = new CoreMailUtil();
/* 286 */     Socket socket = null;
/* 287 */     IClient iClient = null;
/* 288 */     APIContext aPIContext = null;
/*     */     
/*     */     try {
/* 291 */       socket = new Socket(paramString5, paramInt);
/* 292 */       iClient = APIContext.getClient(socket);
/*     */       
/* 294 */       if (userExist(paramString3, paramString5, paramInt))
/*     */       {
/* 296 */         aPIContext = iClient.changeAttrs(paramString3, "user_status=1");
/*     */       }
/*     */       
/* 299 */       if (aPIContext.getRetCode() == 0) {
/* 300 */         coreMailUtil.insertCoreMailSynLog("3", paramString4, "3", "1", "");
/*     */         
/* 302 */         this.newlog.error("OA人员离职，冻结CoreMail邮件系统账号成功，ID=" + paramString1 + "，" + paramString2 + "=" + paramString3 + "，code=" + aPIContext
/* 303 */             .getRetCode());
/* 304 */         bool = true;
/*     */       } else {
/* 306 */         coreMailUtil.insertCoreMailSynLog("3", paramString4, "3", "2", aPIContext.getErrorInfo());
/*     */         
/* 308 */         this.newlog.error("OA人员离职，冻结CoreMail邮件系统账号失败，ID=" + paramString1 + "，" + paramString2 + "=" + paramString3 + "，code=" + aPIContext
/* 309 */             .getRetCode() + "，msg=" + aPIContext.getErrorInfo());
/* 310 */         bool = false;
/*     */       }
/*     */     
/* 313 */     } catch (Exception exception) {
/* 314 */       this.newlog.error("OA人员离职，冻结CoreMail邮件系统账号出现异常：", exception);
/* 315 */       bool = false;
/*     */     } finally {
/* 317 */       if (iClient != null) {
/* 318 */         iClient.close();
/*     */       }
/* 320 */       if (socket != null) {
/* 321 */         socket.close();
/*     */       }
/*     */     } 
/* 324 */     return bool;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 328 */     byte b = 0;
/* 329 */     while (b < 5) {
/* 330 */       if (b == 2);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/coremail/biz/HrmResourceSubscriber4CoreMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */