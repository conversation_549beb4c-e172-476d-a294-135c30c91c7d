/*     */ package weaver.integration.thirdsdk.coremail.biz;
/*     */ 
/*     */ import java.net.Socket;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import tebie.applib.api.APIContext;
/*     */ import tebie.applib.api.IClient;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.email.CoreMailUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmDepartmentSubscriber4CoreMail
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  27 */     if (paramList != null) {
/*  28 */       String str1 = "";
/*  29 */       String str2 = "";
/*  30 */       String str3 = "";
/*  31 */       String str4 = "";
/*  32 */       String str5 = "";
/*  33 */       boolean bool = false;
/*  34 */       for (FieldData fieldData : paramList) {
/*  35 */         Object object = fieldData.getFieldValue();
/*  36 */         String str = fieldData.getFieldName();
/*  37 */         if ("departmentname".equalsIgnoreCase(str)) {
/*  38 */           str3 = object.toString(); continue;
/*  39 */         }  if ("subcompanyid1".equalsIgnoreCase(str)) {
/*  40 */           str4 = object.toString();
/*     */         }
/*     */       } 
/*     */       
/*  44 */       for (FieldData fieldData : paramList) {
/*  45 */         Object object = fieldData.getFieldValue();
/*  46 */         String str = fieldData.getFieldName();
/*     */         
/*  48 */         if ("canceled".equalsIgnoreCase(str)) {
/*  49 */           str1 = object.toString(); continue;
/*  50 */         }  if ("id".equalsIgnoreCase(str)) {
/*  51 */           str2 = object.toString(); continue;
/*  52 */         }  if ("supdepid".equalsIgnoreCase(str)) {
/*  53 */           str5 = object.toString();
/*  54 */           if (Integer.parseInt(str5) == 0) {
/*  55 */             str5 = "parent_org_unit_id=com_" + str4 + "&org_unit_name=" + str3; continue;
/*     */           } 
/*  57 */           str5 = "parent_org_unit_id=" + str5 + "&org_unit_name=" + str3;
/*     */         } 
/*     */       } 
/*     */       
/*     */       try {
/*  62 */         synOrg(str4, str2, str3, str5, str1);
/*  63 */       } catch (Exception exception) {
/*  64 */         this.newlog.error("实时同步部门架构到coremail报错", exception);
/*     */       } 
/*     */     } 
/*     */     
/*  68 */     return null;
/*     */   }
/*     */   
/*  71 */   private Logger newlog = LoggerFactory.getLogger(HrmDepartmentSubscriber4CoreMail.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean synOrg(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) throws Exception {
/*  82 */     RecordSet recordSet1 = new RecordSet();
/*  83 */     RecordSet recordSet2 = new RecordSet();
/*  84 */     String str = "select * from coremailsetting";
/*  85 */     recordSet1.executeSql(str);
/*  86 */     while (recordSet1.next()) {
/*  87 */       String str1 = Util.null2String(recordSet1.getString("bindsubcompany"));
/*  88 */       String str2 = Util.null2String(recordSet1.getString("isuse"));
/*  89 */       String str3 = Util.null2String(recordSet1.getString("issync"));
/*  90 */       String str4 = Util.null2String(recordSet1.getString("systemaddress"));
/*  91 */       int i = Util.getIntValue(recordSet1.getString("emailPort"), 6195);
/*  92 */       String str5 = Util.null2String(recordSet1.getString("orgid"));
/*  93 */       String str6 = Util.null2String(recordSet1.getString("providerid"));
/*  94 */       String str7 = Util.null2String(recordSet1.getString("domain"));
/*  95 */       boolean bool = true;
/*  96 */       Socket socket = null;
/*  97 */       IClient iClient = null;
/*  98 */       APIContext aPIContext1 = null;
/*  99 */       APIContext aPIContext2 = null;
/* 100 */       CoreMailUtil coreMailUtil = new CoreMailUtil();
/*     */       
/* 102 */       if ("0".equals(str2)) {
/*     */         continue;
/*     */       }
/* 105 */       if ("0".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */ 
/*     */       
/* 110 */       if (!"".equals(str1)) {
/* 111 */         String[] arrayOfString = str1.split(",");
/* 112 */         List<String> list = Arrays.asList(arrayOfString);
/* 113 */         if (!list.contains(paramString1)) {
/*     */           continue;
/*     */         }
/*     */       } 
/*     */       
/* 118 */       String str8 = (paramString2.indexOf("com_") > -1) ? "1" : "2";
/* 119 */       String str9 = "";
/*     */       
/*     */       try {
/* 122 */         socket = new Socket(str4, i);
/* 123 */         iClient = APIContext.getClient(socket);
/*     */ 
/*     */         
/* 126 */         recordSet2.executeSql("select * from hrmdepartment where id=" + paramString2);
/* 127 */         if (recordSet2.next()) {
/*     */ 
/*     */           
/* 130 */           aPIContext2 = iClient.getUnitAttrs(str5, paramString2, "org_unit_name=");
/* 131 */           if (aPIContext2.getRetCode() == 0) {
/* 132 */             if (!"1".equals(paramString5)) {
/* 133 */               aPIContext1 = iClient.setUnitAttrs(str5, paramString2, paramString4);
/* 134 */               str9 = "2";
/*     */             } else {
/* 136 */               aPIContext1 = iClient.delUnit(str5, paramString2);
/* 137 */               str9 = "3";
/*     */             }
/*     */           
/* 140 */           } else if (!"1".equals(paramString5)) {
/* 141 */             aPIContext1 = iClient.addUnit(str5, paramString2, paramString4);
/* 142 */             str9 = "1";
/*     */           }
/*     */         
/*     */         }
/*     */         else {
/*     */           
/* 148 */           aPIContext2 = iClient.getUnitAttrs(str5, paramString2, "org_unit_name=");
/* 149 */           if (aPIContext2.getRetCode() == 0) {
/* 150 */             aPIContext1 = iClient.delUnit(str5, paramString2);
/* 151 */             str9 = "3";
/*     */           } 
/*     */         } 
/*     */         
/* 155 */         if (aPIContext1.getRetCode() == 0) {
/* 156 */           coreMailUtil.insertCoreMailSynLog(str8, paramString3, str9, "1", "");
/*     */           
/* 158 */           this.newlog.error("实时同步分部或部门到CoreMail邮件系统成功，ID=" + paramString2 + "，更新信息：" + paramString4 + "，code=" + aPIContext1
/* 159 */               .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/* 160 */           bool = true;
/*     */         } else {
/* 162 */           coreMailUtil.insertCoreMailSynLog(str8, paramString3, str9, "2", aPIContext1.getErrorInfo());
/*     */           
/* 164 */           this.newlog.error("实时同步分部或部门到CoreMail邮件系统失败，ID=" + paramString2 + "，更新信息：" + paramString4 + "，code=" + aPIContext1
/* 165 */               .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/* 166 */           bool = false;
/*     */         } 
/* 168 */       } catch (Exception exception) {
/* 169 */         this.newlog.error("实时同步分部或部门到CoreMail邮件系统出现异常：", exception);
/* 170 */         bool = false;
/*     */       } finally {
/* 172 */         if (iClient != null) {
/* 173 */           iClient.close();
/*     */         }
/* 175 */         if (socket != null) {
/* 176 */           socket.close();
/*     */         }
/*     */       } 
/* 179 */       return bool;
/*     */     } 
/* 181 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/coremail/biz/HrmDepartmentSubscriber4CoreMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */