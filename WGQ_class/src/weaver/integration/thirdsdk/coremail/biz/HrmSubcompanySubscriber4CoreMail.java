/*     */ package weaver.integration.thirdsdk.coremail.biz;
/*     */ 
/*     */ import java.net.Socket;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import tebie.applib.api.APIContext;
/*     */ import tebie.applib.api.IClient;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.email.CoreMailUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSubcompanySubscriber4CoreMail
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  21 */     if (paramList != null) {
/*  22 */       String str1 = "";
/*  23 */       String str2 = "";
/*  24 */       String str3 = "";
/*  25 */       String str4 = "";
/*  26 */       String str5 = "";
/*  27 */       for (FieldData fieldData : paramList) {
/*  28 */         Object object = fieldData.getFieldValue();
/*  29 */         String str = fieldData.getFieldName();
/*  30 */         if ("subcompanyname".equalsIgnoreCase(str)) {
/*  31 */           str3 = object.toString();
/*     */           break;
/*     */         } 
/*     */       } 
/*  35 */       for (FieldData fieldData : paramList) {
/*  36 */         Object object = fieldData.getFieldValue();
/*  37 */         String str = fieldData.getFieldName();
/*     */         
/*  39 */         if ("canceled".equalsIgnoreCase(str)) {
/*  40 */           str1 = object.toString(); continue;
/*  41 */         }  if ("id".equalsIgnoreCase(str)) {
/*  42 */           str5 = object.toString();
/*  43 */           str2 = "com_" + object.toString(); continue;
/*  44 */         }  if ("supsubcomid".equalsIgnoreCase(str)) {
/*  45 */           str4 = object.toString();
/*  46 */           if (Integer.parseInt(str4) > 0) {
/*  47 */             str4 = "parent_org_unit_id=com_" + str4 + "&org_unit_name=" + str3; continue;
/*     */           } 
/*  49 */           str4 = "org_unit_name=" + str3;
/*     */         } 
/*     */       } 
/*     */       
/*     */       try {
/*  54 */         synOrg(str5, str2, str3, str4, str1);
/*  55 */       } catch (Exception exception) {
/*  56 */         this.newlog.error("实时同步分部架构到coremail报错", exception);
/*     */       } 
/*     */     } 
/*  59 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean synOrg(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) throws Exception {
/*  71 */     RecordSet recordSet1 = new RecordSet();
/*  72 */     RecordSet recordSet2 = new RecordSet();
/*  73 */     String str = "select * from coremailsetting";
/*  74 */     recordSet1.executeSql(str);
/*  75 */     while (recordSet1.next()) {
/*  76 */       String str1 = Util.null2String(recordSet1.getString("bindsubcompany"));
/*  77 */       String str2 = Util.null2String(recordSet1.getString("isuse"));
/*  78 */       String str3 = Util.null2String(recordSet1.getString("issync"));
/*  79 */       String str4 = Util.null2String(recordSet1.getString("systemaddress"));
/*  80 */       int i = Util.getIntValue(recordSet1.getString("emailPort"), 6195);
/*  81 */       String str5 = Util.null2String(recordSet1.getString("orgid"));
/*  82 */       String str6 = Util.null2String(recordSet1.getString("providerid"));
/*  83 */       String str7 = Util.null2String(recordSet1.getString("domain"));
/*  84 */       boolean bool = true;
/*  85 */       Socket socket = null;
/*  86 */       IClient iClient = null;
/*  87 */       APIContext aPIContext1 = null;
/*  88 */       APIContext aPIContext2 = null;
/*  89 */       CoreMailUtil coreMailUtil = new CoreMailUtil();
/*     */       
/*  91 */       if ("0".equals(str2)) {
/*     */         continue;
/*     */       }
/*  94 */       if ("0".equals(str3)) {
/*     */         continue;
/*     */       }
/*     */ 
/*     */       
/*  99 */       if (!"".equals(str1)) {
/* 100 */         String[] arrayOfString = str1.split(",");
/* 101 */         List<String> list = Arrays.asList(arrayOfString);
/* 102 */         if (!list.contains(paramString1)) {
/*     */           continue;
/*     */         }
/*     */       } 
/*     */       
/* 107 */       String str8 = (paramString2.indexOf("com_") > -1) ? "1" : "2";
/* 108 */       String str9 = "";
/*     */       
/*     */       try {
/* 111 */         socket = new Socket(str4, i);
/* 112 */         iClient = APIContext.getClient(socket);
/*     */         
/* 114 */         recordSet2.executeSql("select * from hrmsubcompany where id=" + paramString1);
/* 115 */         if (recordSet2.next()) {
/*     */           
/* 117 */           aPIContext2 = iClient.getUnitAttrs(str5, paramString2, "org_unit_name=");
/* 118 */           if (aPIContext2.getRetCode() == 0) {
/* 119 */             if (!"1".equals(paramString5)) {
/* 120 */               aPIContext1 = iClient.setUnitAttrs(str5, paramString2, paramString4);
/* 121 */               str9 = "2";
/*     */             } else {
/* 123 */               aPIContext1 = iClient.delUnit(str5, paramString2);
/* 124 */               str9 = "3";
/*     */             }
/*     */           
/* 127 */           } else if (!"1".equals(paramString5)) {
/* 128 */             aPIContext1 = iClient.addUnit(str5, paramString2, paramString4);
/* 129 */             str9 = "1";
/*     */           }
/*     */         
/*     */         } else {
/*     */           
/* 134 */           aPIContext2 = iClient.getUnitAttrs(str5, paramString2, "org_unit_name=");
/* 135 */           if (aPIContext2.getRetCode() == 0) {
/* 136 */             aPIContext1 = iClient.delUnit(str5, paramString2);
/* 137 */             str9 = "3";
/*     */           } 
/*     */         } 
/*     */         
/* 141 */         if (aPIContext1.getRetCode() == 0) {
/* 142 */           coreMailUtil.insertCoreMailSynLog(str8, paramString3, str9, "1", "");
/*     */           
/* 144 */           this.newlog.error("实时同步分部或部门到CoreMail邮件系统成功，ID=" + paramString2 + "，更新信息：" + paramString4 + "，code=" + aPIContext1
/* 145 */               .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/* 146 */           bool = true;
/*     */         } else {
/* 148 */           coreMailUtil.insertCoreMailSynLog(str8, paramString3, str9, "2", aPIContext1.getErrorInfo());
/*     */           
/* 150 */           this.newlog.error("实时同步分部或部门到CoreMail邮件系统失败，ID=" + paramString2 + "，更新信息：" + paramString4 + "，code=" + aPIContext1
/* 151 */               .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/* 152 */           bool = false;
/*     */         } 
/* 154 */       } catch (Exception exception) {
/* 155 */         this.newlog.error("实时同步分部或部门到CoreMail邮件系统出现异常：", exception);
/* 156 */         bool = false;
/*     */       } finally {
/* 158 */         if (iClient != null) {
/* 159 */           iClient.close();
/*     */         }
/* 161 */         if (socket != null) {
/* 162 */           socket.close();
/*     */         }
/*     */       } 
/* 165 */       return bool;
/*     */     } 
/* 167 */     return false;
/*     */   }
/*     */   
/* 170 */   private Logger newlog = LoggerFactory.getLogger(HrmSubcompanySubscriber4CoreMail.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/coremail/biz/HrmSubcompanySubscriber4CoreMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */