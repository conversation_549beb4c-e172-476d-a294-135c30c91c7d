/*    */ package weaver.integration.thirdsdk.mailutil;
/*    */ 
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.TreeMap;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ 
/*    */ public class MailRealFieldAndValue {
/*    */   public static Map<String, Object> getFieldAndValue(List<FieldData> paramList) {
/* 10 */     TreeMap<Object, Object> treeMap = new TreeMap<>();
/* 11 */     for (FieldData fieldData : paramList) {
/* 12 */       treeMap.put(fieldData.getFieldName(), fieldData.getFieldValue());
/*    */     }
/*    */     
/* 15 */     return (Map)treeMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/mailutil/MailRealFieldAndValue.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */