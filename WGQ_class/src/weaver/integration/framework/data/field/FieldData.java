/*    */ package weaver.integration.framework.data.field;
/*    */ 
/*    */ 
/*    */ public class FieldData
/*    */ {
/*    */   private String fieldName;
/*    */   private String fieldLabel;
/*    */   private String fieldType;
/*    */   private Object fieldValue;
/*    */   
/*    */   public FieldData() {}
/*    */   
/*    */   public FieldData(String paramString1, String paramString2, String paramString3, Object paramObject) {
/* 14 */     this.fieldName = paramString1;
/* 15 */     this.fieldLabel = paramString2;
/* 16 */     this.fieldType = paramString3;
/* 17 */     this.fieldValue = paramObject;
/*    */   }
/*    */   
/*    */   public String getFieldName() {
/* 21 */     return this.fieldName;
/*    */   }
/*    */   public void setFieldName(String paramString) {
/* 24 */     this.fieldName = paramString;
/*    */   }
/*    */   public String getFieldLabel() {
/* 27 */     return this.fieldLabel;
/*    */   }
/*    */   public void setFieldLabel(String paramString) {
/* 30 */     this.fieldLabel = paramString;
/*    */   }
/*    */   public String getFieldType() {
/* 33 */     return this.fieldType;
/*    */   }
/*    */   public void setFieldType(String paramString) {
/* 36 */     this.fieldType = paramString;
/*    */   }
/*    */   public Object getFieldValue() {
/* 39 */     return this.fieldValue;
/*    */   }
/*    */   public void setFieldValue(Object paramObject) {
/* 42 */     this.fieldValue = paramObject;
/*    */   }
/*    */   
/*    */   public String toString() {
/* 46 */     return "{" + 
/* 47 */       "\"fieldName\":\"" + 
/* 48 */       this.fieldName + "\"," + "\"fieldLabel\":\"" + 
/* 49 */       this.fieldLabel + "\"," + "\"fieldType\":\"" + 
/* 50 */       this.fieldType + "\"," + "\"fieldValue\":\"" + 
/* 51 */       this.fieldValue + "\"" + "}";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/field/FieldData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */