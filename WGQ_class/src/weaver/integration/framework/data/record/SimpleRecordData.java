/*    */ package weaver.integration.framework.data.record;
/*    */ 
/*    */ 
/*    */ public class SimpleRecordData
/*    */ {
/*    */   private String action;
/*    */   private CommonRecordData recordData;
/*    */   
/*    */   public SimpleRecordData() {}
/*    */   
/*    */   public SimpleRecordData(String paramString, CommonRecordData paramCommonRecordData) {
/* 12 */     this.action = paramString;
/* 13 */     this.recordData = paramCommonRecordData;
/*    */   }
/*    */   
/*    */   public CommonRecordData getRecordData() {
/* 17 */     return this.recordData;
/*    */   }
/*    */   
/*    */   public void setRecordData(CommonRecordData paramCommonRecordData) {
/* 21 */     this.recordData = paramCommonRecordData;
/*    */   }
/*    */   
/*    */   public void setAction(String paramString) {
/* 25 */     this.action = paramString;
/*    */   }
/*    */   
/*    */   public String getAction() {
/* 29 */     return this.action;
/*    */   }
/*    */   
/*    */   public String toString() {
/* 33 */     return "{" + 
/* 34 */       "\"action\":\"" + 
/* 35 */       this.action + "\"," + "\"data\":" + 
/* 36 */       this.recordData + "}";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/record/SimpleRecordData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */