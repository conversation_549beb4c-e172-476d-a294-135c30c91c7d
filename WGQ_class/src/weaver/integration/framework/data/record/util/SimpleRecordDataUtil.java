/*    */ package weaver.integration.framework.data.record.util;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.integration.framework.data.record.CommonRecordData;
/*    */ import weaver.integration.framework.data.record.SimpleRecordData;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SimpleRecordDataUtil
/*    */ {
/*    */   public static SimpleRecordData washData(SimpleRecordData paramSimpleRecordData, List<String> paramList) {
/* 14 */     long l1 = System.currentTimeMillis();
/* 15 */     if (paramList == null || paramList.size() == 0) {
/* 16 */       return paramSimpleRecordData;
/*    */     }
/*    */     
/* 19 */     StringBuilder stringBuilder = new StringBuilder(",");
/* 20 */     if (paramList != null) {
/* 21 */       for (String str1 : paramList) {
/* 22 */         stringBuilder.append(str1).append(",");
/*    */       }
/*    */     }
/* 25 */     String str = stringBuilder.toString();
/*    */     
/* 27 */     Map map = paramSimpleRecordData.getRecordData().getFieldDataMap();
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 30 */     for (Map.Entry entry : map.entrySet()) {
/* 31 */       if (str.indexOf("," + (String)entry.getKey() + ",") > -1) {
/* 32 */         hashMap.put(entry.getKey(), entry.getValue());
/*    */       }
/*    */     } 
/*    */     
/* 36 */     SimpleRecordData simpleRecordData = new SimpleRecordData();
/*    */     
/* 38 */     simpleRecordData.setAction(paramSimpleRecordData.getAction());
/*    */     
/* 40 */     CommonRecordData commonRecordData = new CommonRecordData();
/* 41 */     commonRecordData.setFieldDataMap(hashMap);
/* 42 */     simpleRecordData.setRecordData(commonRecordData);
/*    */     
/* 44 */     long l2 = System.currentTimeMillis();
/*    */     
/* 46 */     return simpleRecordData;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/record/util/SimpleRecordDataUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */