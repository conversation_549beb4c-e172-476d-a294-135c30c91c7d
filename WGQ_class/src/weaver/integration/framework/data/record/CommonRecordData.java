/*    */ package weaver.integration.framework.data.record;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ 
/*    */ public class CommonRecordData
/*    */ {
/*    */   private String tableName;
/*    */   private Map<String, FieldData> fieldDataMap;
/*    */   
/*    */   public CommonRecordData() {
/* 13 */     this.fieldDataMap = new HashMap<>();
/*    */   }
/*    */   
/*    */   public CommonRecordData(String paramString, Map<String, FieldData> paramMap) {
/* 17 */     setTableName(paramString);
/* 18 */     setFieldDataMap(paramMap);
/*    */   }
/*    */   
/*    */   public CommonRecordData(Map<String, FieldData> paramMap) {
/* 22 */     setFieldDataMap(paramMap);
/*    */   }
/*    */   
/*    */   public String getTableName() {
/* 26 */     return this.tableName;
/*    */   }
/*    */   
/*    */   public void setTableName(String paramString) {
/* 30 */     this.tableName = paramString;
/*    */   }
/*    */   
/*    */   public Map<String, FieldData> getFieldDataMap() {
/* 34 */     return this.fieldDataMap;
/*    */   }
/*    */   
/*    */   public void setFieldDataMap(Map<String, FieldData> paramMap) {
/* 38 */     this.fieldDataMap = paramMap;
/*    */   }
/*    */   
/*    */   public boolean setField(String paramString, FieldData paramFieldData) {
/* 42 */     this.fieldDataMap.put(paramString, paramFieldData);
/* 43 */     return true;
/*    */   }
/*    */   
/*    */   public boolean removeField(String paramString) {
/* 47 */     this.fieldDataMap.remove(paramString);
/* 48 */     return true;
/*    */   }
/*    */   
/*    */   public String toString() {
/* 52 */     StringBuilder stringBuilder = new StringBuilder();
/* 53 */     for (Map.Entry<String, FieldData> entry : this.fieldDataMap.entrySet()) {
/* 54 */       stringBuilder.append(entry.getValue()).append(",");
/*    */     }
/* 56 */     stringBuilder = new StringBuilder(stringBuilder.substring(0, stringBuilder.length() - 1));
/*    */     
/* 58 */     return "{" + 
/* 59 */       "\"tableName\":\"" + 
/* 60 */       this.tableName + "\"," + "\"data\":{" + 
/* 61 */       stringBuilder + 
/* 62 */       "}" + 
/* 63 */       "}";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/record/CommonRecordData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */