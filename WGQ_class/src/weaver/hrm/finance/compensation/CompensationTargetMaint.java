/*     */ package weaver.hrm.finance.compensation;
/*     */ 
/*     */ import java.io.FileInputStream;
/*     */ import java.math.BigDecimal;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.SQLException;
/*     */ import java.text.DateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Vector;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDateUtil;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*     */ import org.apache.poi.ss.usermodel.Cell;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.WeaverConnection;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ public class CompensationTargetMaint
/*     */   extends BaseBean
/*     */ {
/*  33 */   private ArrayList targetlist = null;
/*  34 */   private ArrayList targetnamelist = null;
/*  35 */   private Vector msg1 = new Vector();
/*  36 */   private Vector msg2 = new Vector();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Vector getMsg1() {
/*  45 */     return this.msg1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Vector getMsg2() {
/*  53 */     return this.msg2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getTargetlist() {
/*  61 */     return this.targetlist;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getTargetnamelist() {
/*  69 */     return this.targetnamelist;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getDepartmentTarget(int paramInt1, int paramInt2, int paramInt3, String paramString, int paramInt4, boolean paramBoolean) {
/*  82 */     this.targetlist = new ArrayList();
/*  83 */     this.targetnamelist = new ArrayList();
/*  84 */     RecordSet recordSet1 = new RecordSet();
/*  85 */     RecordSet recordSet2 = new RecordSet();
/*     */     try {
/*  87 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  88 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  89 */       String str1 = subCompanyComInfo.getRightSubCompany(paramInt3, paramString, paramInt4);
/*  90 */       ArrayList arrayList = Util.TokenizerString(str1, ",");
/*  91 */       String str2 = subCompanyComInfo.getRightSubCompanyStr("" + paramInt1, arrayList);
/*  92 */       String str3 = "";
/*  93 */       if (str2.trim().equals("")) {
/*  94 */         str3 = "" + paramInt1;
/*     */       } else {
/*  96 */         str3 = str2 + "," + paramInt1;
/*     */       } 
/*  98 */       recordSet1.executeSql("select id,TargetName,subcompanyid,AreaType from HRM_CompensationTargetSet order by showorder asc,id asc");
/*  99 */       while (recordSet1.next()) {
/* 100 */         String str4 = recordSet1.getString("id");
/* 101 */         String str5 = recordSet1.getString("TargetName");
/* 102 */         int i = recordSet1.getInt("subcompanyid");
/* 103 */         int j = recordSet1.getInt("AreaType");
/* 104 */         if (j == 0) {
/* 105 */           this.targetlist.add(str4);
/* 106 */           this.targetnamelist.add(str5); continue;
/* 107 */         }  if (j == 1) {
/* 108 */           if (paramInt1 == i) {
/* 109 */             if (!paramBoolean && paramInt2 < 1) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 115 */               this.targetlist.add(str4);
/* 116 */               this.targetnamelist.add(str5); continue;
/*     */             } 
/* 118 */             this.targetlist.add(str4);
/* 119 */             this.targetnamelist.add(str5);
/*     */           }  continue;
/*     */         } 
/* 122 */         if (j == 2) {
/* 123 */           ArrayList<String> arrayList1 = new ArrayList();
/* 124 */           ArrayList<String> arrayList2 = new ArrayList();
/* 125 */           arrayList1.add(i + "");
/* 126 */           arrayList2.add(paramInt1 + "");
/* 127 */           subCompanyComInfo.getSubCompanyLists("" + i, arrayList1);
/* 128 */           subCompanyComInfo.getSubCompanyLists("" + paramInt1, arrayList2);
/* 129 */           for (byte b = 0; b < arrayList2.size(); b++) {
/* 130 */             if (arrayList1.indexOf(arrayList2.get(b)) > -1) {
/* 131 */               this.targetlist.add(str4);
/* 132 */               this.targetnamelist.add(str5); break;
/*     */             } 
/*     */           }  continue;
/*     */         } 
/* 136 */         if (j == 3) {
/* 137 */           recordSet2.executeSql("select companyordeptid from HRM_ComTargetSetDetail where Targetid=" + str4 + " and companyordeptid=" + paramInt1);
/* 138 */           if (recordSet2.next()) {
/* 139 */             this.targetlist.add(str4);
/* 140 */             this.targetnamelist.add(str5);
/*     */           }  continue;
/* 142 */         }  if (j == 4) {
/* 143 */           if (paramBoolean && paramInt2 < 1) {
/* 144 */             String str = "";
/* 145 */             departmentComInfo.setTofirstRow();
/* 146 */             while (departmentComInfo.next()) {
/* 147 */               if (("," + str3 + ",").indexOf("," + departmentComInfo.getSubcompanyid1() + ",") > -1) {
/* 148 */                 str = str + departmentComInfo.getDepartmentid() + ",";
/*     */               }
/*     */             } 
/* 151 */             if (str.length() > 0) str = str.substring(0, str.length() - 1); 
/* 152 */             recordSet2.executeSql("select companyordeptid from HRM_ComTargetSetDetail where Targetid=" + str4 + " and companyordeptid in(" + str + ")");
/* 153 */             if (recordSet2.next()) {
/* 154 */               this.targetlist.add(str4);
/* 155 */               this.targetnamelist.add(str5);
/*     */             }  continue;
/*     */           } 
/* 158 */           recordSet2.executeSql("select companyordeptid from HRM_ComTargetSetDetail where Targetid=" + str4 + " and companyordeptid=" + paramInt2);
/* 159 */           if (recordSet2.next()) {
/* 160 */             this.targetlist.add(str4);
/* 161 */             this.targetnamelist.add(str5);
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/* 166 */     } catch (Exception exception) {
/* 167 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMemo(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 180 */     String str1 = "";
/* 181 */     RecordSet recordSet = new RecordSet();
/* 182 */     String str2 = "select memo from HRM_CompensationTargetInfo where CompensationYear=" + paramInt4 + " and CompensationMonth=" + paramInt5 + " and Userid=" + paramInt3;
/* 183 */     recordSet.executeSql(str2);
/* 184 */     if (recordSet.next()) {
/* 185 */       str1 = recordSet.getString("memo");
/*     */     }
/* 187 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getTarget(String paramString, ArrayList<String> paramArrayList) {
/* 197 */     ArrayList<String> arrayList = new ArrayList();
/* 198 */     if (paramArrayList != null) {
/* 199 */       String str = "";
/* 200 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/* 201 */         if (str.equals("")) {
/* 202 */           str = paramArrayList.get(b);
/*     */         } else {
/* 204 */           str = str + "," + paramArrayList.get(b);
/*     */         } 
/* 206 */         arrayList.add("0");
/*     */       } 
/* 208 */       if (paramString != null && !paramString.trim().equals("") && str.length() > 0) {
/* 209 */         RecordSet recordSet = new RecordSet();
/* 210 */         String str1 = "select Targetid,Target from HRM_CompensationTargetDetail where CompensationTargetid=" + paramString + " and Targetid in(" + str + ")";
/* 211 */         recordSet.executeSql(str1);
/* 212 */         while (recordSet.next()) {
/* 213 */           String str2 = recordSet.getString("Targetid");
/* 214 */           String str3 = recordSet.getString("Target");
/* 215 */           int i = paramArrayList.indexOf(str2);
/* 216 */           if (i != -1) arrayList.set(i, str3); 
/*     */         } 
/*     */       } 
/*     */     } 
/* 220 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getCellValue(HSSFCell paramHSSFCell, int paramInt, String paramString1, String paramString2) {
/* 229 */     String str = "";
/*     */     try {
/* 231 */       switch (paramHSSFCell.getCellType()) {
/*     */         
/*     */         case NUMERIC:
/* 234 */           if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/* 235 */             str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString(); break;
/*     */           } 
/* 237 */           str = String.valueOf(paramHSSFCell.getNumericCellValue());
/*     */           break;
/*     */         
/*     */         case STRING:
/* 241 */           str = paramHSSFCell.getStringCellValue();
/*     */           break;
/*     */         case FORMULA:
/* 244 */           str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString();
/*     */           break;
/*     */       } 
/*     */ 
/*     */     
/* 249 */     } catch (Exception exception) {
/* 250 */       writeLog(exception);
/*     */     } 
/* 252 */     if (paramInt == 1 && str.equals("")) {
/* 253 */       this.msg1.add(paramString1);
/* 254 */       this.msg2.add(paramString2);
/*     */     } 
/* 256 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void ExcelToDB(String paramString, int paramInt1, int paramInt2, int paramInt3, int paramInt4, ArrayList<String> paramArrayList, int paramInt5, int paramInt6) {
/* 270 */     ConnStatement connStatement = new ConnStatement();
/* 271 */     ConnectionPool connectionPool = ConnectionPool.getInstance();
/* 272 */     WeaverConnection weaverConnection = connectionPool.getConnection();
/* 273 */     PreparedStatement preparedStatement = null;
/*     */     try {
/* 275 */       preparedStatement = weaverConnection.prepareStatement("INSERT INTO HRM_CompensationTargetDetail(CompensationTargetid,Targetid,Target) values(?,?,?)");
/* 276 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 277 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 278 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 279 */       String str1 = "";
/* 280 */       String str2 = "delete from HRM_CompensationTargetDetail where CompensationTargetid in(select id from HRM_CompensationTargetInfo where CompensationYear=" + paramInt3 + " and CompensationMonth=" + paramInt4;
/* 281 */       String str3 = "delete from HRM_CompensationTargetInfo where CompensationYear=" + paramInt3 + " and CompensationMonth=" + paramInt4;
/* 282 */       if (paramInt1 > 0) {
/* 283 */         String str = subCompanyComInfo.getRightSubCompany(paramInt6, "Compensation:Maintenance", 0);
/* 284 */         ArrayList arrayList = Util.TokenizerString(str, ",");
/* 285 */         str1 = subCompanyComInfo.getRightSubCompanyStr1("" + paramInt1, arrayList);
/*     */       } 
/*     */       
/* 288 */       if (paramInt2 > 0) {
/* 289 */         str2 = str2 + " and departmentid=" + paramInt2 + ")";
/* 290 */         str3 = str3 + " and departmentid=" + paramInt2;
/*     */       } else {
/* 292 */         str2 = str2 + " and subcompanyid in(" + str1 + "))";
/* 293 */         str3 = str3 + " and subcompanyid in(" + str1 + ")";
/*     */       } 
/* 295 */       connStatement.setStatementSql(str2);
/* 296 */       connStatement.executeUpdate();
/* 297 */       connStatement.setStatementSql(str3);
/* 298 */       connStatement.executeUpdate();
/* 299 */       FileInputStream fileInputStream = new FileInputStream(paramString);
/* 300 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/* 301 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/* 302 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/* 304 */       fileInputStream.close();
/* 305 */       HSSFRow hSSFRow = null;
/* 306 */       String str4 = "";
/* 307 */       String str5 = "";
/*     */       
/* 309 */       int i = hSSFSheet.getLastRowNum();
/*     */       
/* 311 */       byte b1 = 0;
/* 312 */       for (byte b2 = 1; b2 < i + 1; b2++) {
/* 313 */         hSSFRow = hSSFSheet.getRow(b2);
/* 314 */         HSSFCell hSSFCell1 = null;
/* 315 */         HSSFCell hSSFCell2 = null;
/* 316 */         HSSFCell hSSFCell3 = null;
/* 317 */         ArrayList<String> arrayList = new ArrayList();
/*     */         
/* 319 */         hSSFCell1 = hSSFRow.getCell(0);
/* 320 */         str4 = getCellValue(hSSFCell1, 1, (b2 + 1) + "", "1");
/*     */         
/* 322 */         if (!str4.equals("")) {
/*     */           try {
/* 324 */             str4 = (new BigDecimal(str4)).setScale(0, 4).intValue() + "";
/* 325 */           } catch (Exception exception) {
/* 326 */             this.msg1.add((b2 + 1) + "");
/* 327 */             this.msg2.add("1");
/*     */           } 
/*     */           
/* 330 */           if (paramInt2 < 1 && paramInt1 > 0 && ("," + str1 + ",").indexOf("," + departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID(str4)) + ",") < 0) {
/* 331 */             this.msg1.add((b2 + 1) + "");
/* 332 */             this.msg2.add("1");
/*     */           
/*     */           }
/* 335 */           else if (paramInt2 > 0 && !resourceComInfo.getDepartmentID(str4).equals("" + paramInt2)) {
/* 336 */             this.msg1.add((b2 + 1) + "");
/* 337 */             this.msg2.add("1");
/*     */           } else {
/*     */             int j;
/*     */ 
/*     */             
/* 342 */             for (j = 0; j < paramArrayList.size(); j++) {
/* 343 */               hSSFCell2 = hSSFRow.getCell((short)(j + 3));
/* 344 */               String str = getCellValue(hSSFCell2, 0, (b2 + 1) + "", (j + 4) + "");
/*     */               
/*     */               try {
/* 347 */                 str = (new BigDecimal(str.trim())).setScale(2, 4).toString();
/* 348 */               } catch (Exception exception) {
/* 349 */                 str = "0";
/*     */               } 
/* 351 */               arrayList.add(str);
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 356 */             hSSFCell3 = hSSFRow.getCell((short)(paramArrayList.size() + 3));
/* 357 */             str5 = getCellValue(hSSFCell3, 0, (b2 + 1) + "", "" + (paramArrayList.size() + 4));
/*     */ 
/*     */ 
/*     */             
/* 361 */             str2 = "INSERT INTO HRM_CompensationTargetInfo(subcompanyid,departmentid,CompensationYear,CompensationMonth,Userid,memo) values(?,?,?,?,?,?)";
/* 362 */             connStatement.setStatementSql(str2);
/* 363 */             connStatement.setInt(1, Util.getIntValue(departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID(str4))));
/* 364 */             connStatement.setInt(2, Util.getIntValue(resourceComInfo.getDepartmentID(str4)));
/* 365 */             connStatement.setInt(3, paramInt3);
/* 366 */             connStatement.setInt(4, paramInt4);
/* 367 */             connStatement.setInt(5, Integer.parseInt(str4));
/* 368 */             connStatement.setString(6, str5);
/* 369 */             connStatement.executeUpdate();
/* 370 */             str2 = "select id from HRM_CompensationTargetInfo where CompensationYear=" + paramInt3 + " and CompensationMonth=" + paramInt4 + " and Userid=" + Util.getIntValue(str4);
/* 371 */             connStatement.setStatementSql(str2);
/* 372 */             connStatement.executeQuery();
/* 373 */             if (connStatement.next()) {
/* 374 */               j = connStatement.getInt(1);
/* 375 */               for (byte b = 0; b < paramArrayList.size(); b++) {
/* 376 */                 String str = arrayList.get(b);
/* 377 */                 int k = Util.getIntValue(paramArrayList.get(b));
/* 378 */                 preparedStatement.setInt(1, j);
/* 379 */                 preparedStatement.setInt(2, k);
/* 380 */                 preparedStatement.setString(3, str);
/* 381 */                 preparedStatement.addBatch();
/* 382 */                 b1++;
/* 383 */                 if (b1 == 'Ϩ')
/* 384 */                 { preparedStatement.executeBatch();
/* 385 */                   weaverConnection.commit();
/* 386 */                   preparedStatement.clearBatch();
/* 387 */                   b1 = 0; } 
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/* 392 */       }  preparedStatement.executeBatch();
/* 393 */       weaverConnection.commit();
/* 394 */     } catch (Exception exception) {
/* 395 */       writeLog(exception);
/* 396 */       exception.printStackTrace();
/*     */     } finally {
/* 398 */       connStatement.close();
/*     */       try {
/* 400 */         if (preparedStatement != null) {
/* 401 */           preparedStatement.close();
/*     */         }
/* 403 */       } catch (SQLException sQLException) {
/* 404 */         writeLog(">>>>关闭stmt出错");
/* 405 */         writeLog(sQLException);
/*     */       } 
/*     */       try {
/* 408 */         if (weaverConnection != null && !weaverConnection.isClosed()) {
/* 409 */           weaverConnection.close();
/*     */         }
/* 411 */       } catch (SQLException sQLException) {
/* 412 */         writeLog(">>>>关闭conn出错");
/* 413 */         writeLog(sQLException);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public void ExcelToDB(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, ArrayList<String> paramArrayList, int paramInt6, int paramInt7) {
/* 419 */     ConnStatement connStatement = new ConnStatement();
/* 420 */     ConnectionPool connectionPool = ConnectionPool.getInstance();
/* 421 */     WeaverConnection weaverConnection = connectionPool.getConnection();
/* 422 */     PreparedStatement preparedStatement = null;
/*     */     try {
/* 424 */       preparedStatement = weaverConnection.prepareStatement("INSERT INTO HRM_CompensationTargetDetail(CompensationTargetid,Targetid,Target) values(?,?,?)");
/* 425 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 426 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 427 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 428 */       String str1 = "";
/* 429 */       String str2 = "delete from HRM_CompensationTargetDetail where CompensationTargetid in(select id from HRM_CompensationTargetInfo where CompensationYear=" + paramInt4 + " and CompensationMonth=" + paramInt5;
/* 430 */       String str3 = "delete from HRM_CompensationTargetInfo where CompensationYear=" + paramInt4 + " and CompensationMonth=" + paramInt5;
/* 431 */       if (paramInt2 > 0) {
/* 432 */         String str = subCompanyComInfo.getRightSubCompany(paramInt7, "Compensation:Maintenance", 0);
/* 433 */         ArrayList arrayList = Util.TokenizerString(str, ",");
/* 434 */         str1 = subCompanyComInfo.getRightSubCompanyStr1("" + paramInt2, arrayList);
/*     */       } 
/*     */       
/* 437 */       if (paramInt3 > 0) {
/* 438 */         str2 = str2 + " and departmentid=" + paramInt3 + ")";
/* 439 */         str3 = str3 + " and departmentid=" + paramInt3;
/*     */       } else {
/* 441 */         str2 = str2 + " and subcompanyid in(" + str1 + "))";
/* 442 */         str3 = str3 + " and subcompanyid in(" + str1 + ")";
/*     */       } 
/* 444 */       connStatement.setStatementSql(str2);
/* 445 */       connStatement.executeUpdate();
/* 446 */       connStatement.setStatementSql(str3);
/* 447 */       connStatement.executeUpdate();
/* 448 */       ImageFileManager imageFileManager = new ImageFileManager();
/* 449 */       imageFileManager.getImageFileInfoById(paramInt1);
/* 450 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(new POIFSFileSystem(imageFileManager.getInputStream()));
/* 451 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/* 453 */       HSSFRow hSSFRow = null;
/* 454 */       String str4 = "";
/* 455 */       String str5 = "";
/*     */       
/* 457 */       int i = hSSFSheet.getLastRowNum();
/* 458 */       byte b1 = 0;
/* 459 */       for (byte b2 = 1; b2 < i + 1; b2++) {
/* 460 */         hSSFRow = hSSFSheet.getRow(b2);
/* 461 */         HSSFCell hSSFCell1 = null;
/* 462 */         HSSFCell hSSFCell2 = null;
/* 463 */         HSSFCell hSSFCell3 = null;
/* 464 */         ArrayList<String> arrayList = new ArrayList();
/* 465 */         hSSFCell1 = hSSFRow.getCell(0);
/* 466 */         str4 = getCellValue(hSSFCell1, 1, (b2 + 1) + "", "1").trim();
/* 467 */         if (!str4.equals("")) {
/*     */           try {
/* 469 */             str4 = (new BigDecimal(str4.trim())).setScale(0, 4).intValue() + "";
/* 470 */           } catch (Exception exception) {
/* 471 */             this.msg1.add((b2 + 1) + "");
/* 472 */             this.msg2.add("1");
/*     */           } 
/*     */           
/* 475 */           if (paramInt3 < 1 && paramInt2 > 0 && ("," + str1 + ",").indexOf("," + departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID(str4)) + ",") < 0) {
/* 476 */             this.msg1.add((b2 + 1) + "");
/* 477 */             this.msg2.add("1");
/*     */           
/*     */           }
/* 480 */           else if (paramInt3 > 0 && !resourceComInfo.getDepartmentID(str4).equals("" + paramInt3)) {
/* 481 */             this.msg1.add((b2 + 1) + "");
/* 482 */             this.msg2.add("1");
/*     */           } else {
/*     */             int j;
/* 485 */             for (j = 0; j < paramArrayList.size(); j++) {
/* 486 */               hSSFCell2 = hSSFRow.getCell((short)(j + 3));
/* 487 */               String str = getCellValue(hSSFCell2, 0, (b2 + 1) + "", (j + 4) + "");
/*     */               try {
/* 489 */                 str = (new BigDecimal(str.trim())).setScale(2, 4).toString();
/* 490 */               } catch (Exception exception) {
/* 491 */                 str = "0";
/*     */               } 
/* 493 */               arrayList.add(str);
/*     */             } 
/*     */             
/* 496 */             hSSFCell3 = hSSFRow.getCell((short)(paramArrayList.size() + 3));
/* 497 */             str5 = getCellValue(hSSFCell3, 0, (b2 + 1) + "", "" + (paramArrayList.size() + 4));
/*     */ 
/*     */             
/* 500 */             str2 = "INSERT INTO HRM_CompensationTargetInfo(subcompanyid,departmentid,CompensationYear,CompensationMonth,Userid,memo) values(?,?,?,?,?,?)";
/* 501 */             connStatement.setStatementSql(str2);
/* 502 */             connStatement.setInt(1, Util.getIntValue(departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID(str4))));
/* 503 */             connStatement.setInt(2, Util.getIntValue(resourceComInfo.getDepartmentID(str4)));
/* 504 */             connStatement.setInt(3, paramInt4);
/* 505 */             connStatement.setInt(4, paramInt5);
/* 506 */             connStatement.setInt(5, Integer.parseInt(str4));
/* 507 */             connStatement.setString(6, str5);
/* 508 */             connStatement.executeUpdate();
/* 509 */             str2 = "select id from HRM_CompensationTargetInfo where CompensationYear=" + paramInt4 + " and CompensationMonth=" + paramInt5 + " and Userid=" + Util.getIntValue(str4);
/* 510 */             connStatement.setStatementSql(str2);
/* 511 */             connStatement.executeQuery();
/* 512 */             if (connStatement.next()) {
/* 513 */               j = connStatement.getInt(1);
/* 514 */               for (byte b = 0; b < paramArrayList.size(); b++) {
/* 515 */                 String str = arrayList.get(b);
/* 516 */                 int k = Util.getIntValue(paramArrayList.get(b));
/* 517 */                 preparedStatement.setInt(1, j);
/* 518 */                 preparedStatement.setInt(2, k);
/* 519 */                 preparedStatement.setString(3, str);
/* 520 */                 preparedStatement.addBatch();
/* 521 */                 b1++;
/* 522 */                 if (b1 == 'Ϩ')
/* 523 */                 { preparedStatement.executeBatch();
/* 524 */                   weaverConnection.commit();
/* 525 */                   preparedStatement.clearBatch();
/* 526 */                   b1 = 0; } 
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/* 531 */       }  preparedStatement.executeBatch();
/* 532 */       weaverConnection.commit();
/* 533 */     } catch (Exception exception) {
/* 534 */       writeLog(exception);
/* 535 */       exception.printStackTrace();
/*     */     } finally {
/* 537 */       connStatement.close();
/*     */       try {
/* 539 */         if (preparedStatement != null) {
/* 540 */           preparedStatement.close();
/*     */         }
/* 542 */       } catch (SQLException sQLException) {
/* 543 */         writeLog(">>>>关闭stmt出错");
/* 544 */         writeLog(sQLException);
/*     */       } 
/*     */       try {
/* 547 */         if (weaverConnection != null && !weaverConnection.isClosed()) {
/* 548 */           weaverConnection.close();
/*     */         }
/* 550 */       } catch (SQLException sQLException) {
/* 551 */         writeLog(">>>>关闭conn出错");
/* 552 */         writeLog(sQLException);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/finance/compensation/CompensationTargetMaint.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */