/*     */ package weaver.hrm.finance;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class BankComInfo
/*     */   extends CacheBase
/*     */ {
/*  12 */   protected static String TABLE_NAME = "HrmBank";
/*     */   
/*  14 */   protected static String TABLE_WHERE = null;
/*     */   
/*  16 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  19 */   protected static String PK_NAME = "id";
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "bankname")
/*     */   protected static int bankname;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "bankdesc")
/*     */   protected static int bankdesc;
/*     */ 
/*     */ 
/*     */   
/*     */   public int getBankNum() {
/*  33 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  42 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBankid() {
/*  50 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBankname() {
/*  58 */     return (String)getRowValue(bankname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBankname(String paramString) {
/*  68 */     return (String)getValue(bankname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBankdesc() {
/*  76 */     return (String)getRowValue(bankdesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBankdesc(String paramString) {
/*  86 */     return (String)getValue(bankdesc, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCheckstr() {
/*  94 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCheckstr(String paramString) {
/* 104 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeBankCache() {
/* 111 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/finance/BankComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */