/*      */ package weaver.hrm.finance;
/*      */ 
/*      */ import com.engine.common.biz.EncryptConfigBiz;
/*      */ import com.engine.kq.util.KQDurationCalculatorUtil;
/*      */ import com.engine.personalIncomeTax.biz.CostBiz;
/*      */ import com.googlecode.aviator.AviatorEvaluator;
/*      */ import java.math.BigDecimal;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Hashtable;
/*      */ import java.util.List;
/*      */ import java.util.Locale;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.common.database.dialect.DialectUtil;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.job.JobTitlesComInfo;
/*      */ import weaver.hrm.location.LocationComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.hrm.schedule.HrmKqSystemComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class SalaryManager
/*      */   extends BaseBean
/*      */ {
/*   38 */   private RecordSet rs = null;
/*   39 */   private ResourceComInfo resourcecominfo = null;
/*   40 */   private JobTitlesComInfo jobtitlecominfo = null;
/*      */   
/*   42 */   private String theresourceid = null;
/*   43 */   private String theitemid = null;
/*   44 */   private String theitemtype = null;
/*   45 */   private String thecityid = null;
/*   46 */   private String thepayid = null;
/*      */ 
/*      */   
/*   49 */   ArrayList resourceitems = null;
/*   50 */   ArrayList amounts = null;
/*   51 */   ArrayList itemids = null;
/*   52 */   ArrayList itemcodes = null;
/*   53 */   ArrayList itemtypes = null;
/*   54 */   ArrayList iteminfos = null;
/*   55 */   ArrayList taxcitys = null;
/*   56 */   ArrayList taxinfos = null;
/*   57 */   ArrayList welfareratecitys = null;
/*   58 */   ArrayList welfarerateinfos = null;
/*      */ 
/*      */   
/*   61 */   ArrayList diffids = null;
/*   62 */   ArrayList salaryables = null;
/*   63 */   ArrayList counttypes = null;
/*   64 */   ArrayList salaryitems = null;
/*   65 */   ArrayList countnums = null;
/*   66 */   ArrayList mindifftimes = null;
/*   67 */   ArrayList timecounttypes = null;
/*      */ 
/*      */   
/*   70 */   ArrayList itemshifts = null;
/*   71 */   ArrayList shiftpays = null;
/*   72 */   ArrayList resourceshifts = null;
/*   73 */   ArrayList shiftcounts = null;
/*      */ 
/*      */   
/*   76 */   ArrayList itemresorces = null;
/*   77 */   ArrayList resourcetpays = null;
/*   78 */   ArrayList resourceworktimes = null;
/*   79 */   ArrayList timecounts = null;
/*      */ 
/*      */   
/*   82 */   ArrayList resourcediffids = null;
/*   83 */   ArrayList resourcediffinfos = null;
/*      */   
/*      */   private boolean hasnohrmsalaryinfo = false;
/*      */   
/*   87 */   private String theseldate = "";
/*   88 */   private String theselbegindate = "";
/*   89 */   private String theselenddate = "";
/*      */   
/*   91 */   private ConnStatement statement = null;
/*      */ 
/*      */   
/*      */   public SalaryManager() {
/*   95 */     this.rs = new RecordSet();
/*   96 */     this.statement = new ConnStatement();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourceid(String paramString) {
/*  106 */     this.theresourceid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setItemid(String paramString) {
/*  115 */     this.theitemid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setItemtype(String paramString) {
/*  124 */     this.theitemtype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCityid(String paramString) {
/*  133 */     this.thecityid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourceitems(ArrayList paramArrayList) {
/*  142 */     this.resourceitems = paramArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAmounts(ArrayList paramArrayList) {
/*  151 */     this.amounts = paramArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initItemSalary(String paramString) {
/*  160 */     Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/*  161 */     Hashtable<Object, Object> hashtable2 = new Hashtable<>();
/*  162 */     ArrayList<String> arrayList = null;
/*      */     
/*      */     try {
/*  165 */       this.resourcecominfo = new ResourceComInfo();
/*  166 */       this.jobtitlecominfo = new JobTitlesComInfo();
/*  167 */     } catch (Exception exception) {}
/*      */ 
/*      */ 
/*      */     
/*  171 */     this.rs.executeSql("delete from HrmSalaryPersonality where itemid = " + paramString);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  181 */     boolean bool = true;
/*  182 */     this.rs.executeSql(" select * from HrmSalaryRank where itemid = " + paramString);
/*  183 */     while (this.rs.next()) {
/*  184 */       bool = false;
/*  185 */       String str1 = "" + Util.getIntValue(this.rs.getString("jobactivityid"), 0);
/*  186 */       String str2 = "" + Util.getIntValue(this.rs.getString("jobid"), 0);
/*  187 */       String str3 = "" + Util.getIntValue(this.rs.getString("joblevelfrom"), 0);
/*  188 */       String str4 = "" + Util.getIntValue(this.rs.getString("joblevelto"), 0);
/*  189 */       String str5 = "" + Util.getDoubleValue(this.rs.getString("amount"), 0.0D);
/*      */ 
/*      */       
/*  192 */       if (hashtable2.get(str1 + "_" + str2) == null) {
/*  193 */         arrayList = new ArrayList();
/*  194 */         arrayList.add(str3 + "_" + str4 + "_" + str5);
/*  195 */         hashtable2.put(str1 + "_" + str2, arrayList); continue;
/*      */       } 
/*  197 */       arrayList = (ArrayList<String>)hashtable2.get(str1 + "_" + str2);
/*  198 */       arrayList.add(str3 + "_" + str4 + "_" + str5);
/*      */     } 
/*      */ 
/*      */     
/*  202 */     if (bool)
/*  203 */       return;  while (this.resourcecominfo.next()) {
/*      */       
/*  205 */       String str1 = this.resourcecominfo.getResourceid();
/*  206 */       String str2 = "" + Util.getIntValue(this.resourcecominfo.getJobTitle(), 0);
/*  207 */       String str3 = "" + Util.getIntValue(this.jobtitlecominfo.getJobactivityid(str2), 0);
/*  208 */       String str4 = "" + Util.getIntValue(EncryptConfigBiz.getDecryptData(this.resourcecominfo.getJoblevel()), 0);
/*  209 */       String str5 = "";
/*  210 */       boolean bool1 = false;
/*      */ 
/*      */       
/*  213 */       String str6 = (String)hashtable1.get(str1);
/*  214 */       if (str6 != null) {
/*  215 */         str5 = "" + Util.getDoubleValue(str6, 0.0D);
/*  216 */         bool1 = true;
/*      */       } 
/*      */       
/*  219 */       if (!bool1 && hashtable2.get(str3 + "_" + str2) != null) {
/*  220 */         arrayList = (ArrayList<String>)hashtable2.get(str3 + "_" + str2);
/*  221 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  222 */           String str = arrayList.get(b);
/*  223 */           String[] arrayOfString = Util.TokenizerString2(str, "_");
/*  224 */           int i = Util.getIntValue(arrayOfString[0], 0);
/*  225 */           int j = Util.getIntValue(arrayOfString[1], 0);
/*  226 */           int k = Util.getIntValue(str4, 0);
/*      */           
/*  228 */           if (k >= i && k <= j) {
/*  229 */             str5 = "" + Util.getDoubleValue(arrayOfString[2], 0.0D);
/*  230 */             bool1 = true;
/*      */             break;
/*      */           } 
/*      */         } 
/*  234 */       } else if (!bool1 && hashtable2.get(str3 + "_0") != null) {
/*  235 */         arrayList = (ArrayList<String>)hashtable2.get(str3 + "_0");
/*  236 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  237 */           String str = arrayList.get(b);
/*  238 */           String[] arrayOfString = Util.TokenizerString2(str, "_");
/*  239 */           int i = Util.getIntValue(arrayOfString[0], 0);
/*  240 */           int j = Util.getIntValue(arrayOfString[1], 0);
/*  241 */           int k = Util.getIntValue(str4, 0);
/*      */           
/*  243 */           if (k >= i && k <= j) {
/*  244 */             str5 = "" + Util.getDoubleValue(arrayOfString[2], 0.0D);
/*  245 */             bool1 = true;
/*      */             break;
/*      */           } 
/*      */         } 
/*  249 */       } else if (!bool1 && hashtable2.get("0_" + str2) != null) {
/*  250 */         arrayList = (ArrayList<String>)hashtable2.get("0_" + str2);
/*  251 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  252 */           String str = arrayList.get(b);
/*  253 */           String[] arrayOfString = Util.TokenizerString2(str, "_");
/*  254 */           int i = Util.getIntValue(arrayOfString[0], 0);
/*  255 */           int j = Util.getIntValue(arrayOfString[1], 0);
/*  256 */           int k = Util.getIntValue(str4, 0);
/*      */           
/*  258 */           if (k >= i && k <= j) {
/*  259 */             str5 = "" + Util.getDoubleValue(arrayOfString[2], 0.0D);
/*  260 */             bool1 = true;
/*      */             break;
/*      */           } 
/*      */         } 
/*  264 */       } else if (!bool1 && hashtable2.get("0_0") != null) {
/*  265 */         arrayList = (ArrayList<String>)hashtable2.get("0_0");
/*  266 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  267 */           String str = arrayList.get(b);
/*  268 */           String[] arrayOfString = Util.TokenizerString2(str, "_");
/*  269 */           int i = Util.getIntValue(arrayOfString[0], 0);
/*  270 */           int j = Util.getIntValue(arrayOfString[1], 0);
/*  271 */           int k = Util.getIntValue(str4, 0);
/*      */           
/*  273 */           if (k >= i && k <= j) {
/*  274 */             str5 = "" + Util.getDoubleValue(arrayOfString[2], 0.0D);
/*  275 */             bool1 = true;
/*      */             
/*      */             break;
/*      */           } 
/*      */         } 
/*      */       } 
/*  281 */       if (bool1) {
/*  282 */         this.rs.executeUpdate("update HrmSalaryResourcePay set isBatch=1 where itemid=" + paramString + " and resourceid=" + str1, new Object[0]);
/*  283 */         this.rs.executeSql("insert into HrmSalaryPersonality (itemid, hrmid, salary)  values (" + paramString + "," + str1 + "," + str5 + ")");
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initResourceSalary(String paramString) {
/*      */     try {
/*  297 */       this.resourcecominfo = new ResourceComInfo();
/*  298 */       this.jobtitlecominfo = new JobTitlesComInfo();
/*  299 */     } catch (Exception exception) {}
/*      */ 
/*      */     
/*  302 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  304 */     String str1 = "" + Util.getIntValue(this.resourcecominfo.getJobTitle(paramString), 0);
/*  305 */     String str2 = "" + Util.getIntValue(this.jobtitlecominfo.getJobactivityid(str1), 0);
/*  306 */     String str3 = "" + Util.getIntValue(EncryptConfigBiz.getDecryptData(this.resourcecominfo.getJoblevel(paramString)), 0);
/*  307 */     String str4 = "";
/*  308 */     boolean bool = false;
/*      */     
/*  310 */     this.rs.executeSql(" delete from HrmSalaryPersonality where hrmid = " + paramString);
/*      */     
/*  312 */     this.rs.executeSql(" select a.* from HrmSalaryRank a,  HrmSalaryItem b  where a.itemid = b.id and ( b.itemtype = '1' or b.itemtype = '2') order by a.itemid ");
/*      */ 
/*      */     
/*  315 */     while (this.rs.next()) {
/*  316 */       String str5 = "" + Util.getIntValue(this.rs.getString("jobactivityid"), 0);
/*  317 */       String str6 = "" + Util.getIntValue(this.rs.getString("itemid"), 0);
/*  318 */       String str7 = "" + Util.getIntValue(this.rs.getString("jobid"), 0);
/*  319 */       String str8 = "" + Util.getIntValue(this.rs.getString("joblevelfrom"), 0);
/*  320 */       String str9 = "" + Util.getIntValue(this.rs.getString("joblevelto"), 0);
/*  321 */       String str10 = "" + Util.getDoubleValue(this.rs.getString("amount"), 0.0D);
/*  322 */       if (str9.equals("0")) str9 = "9999";
/*      */       
/*  324 */       if (str6.equals(str4) && bool)
/*  325 */         continue;  if (!str6.equals(str4)) {
/*  326 */         str4 = str6;
/*  327 */         bool = false;
/*      */       } 
/*      */ 
/*      */       
/*  331 */       recordSet.executeSql(" select * from HrmSalaryResourcePay where itemid = " + str6 + " and resourceid = " + paramString);
/*  332 */       if (recordSet.next()) {
/*  333 */         String str = "" + Util.getDoubleValue(recordSet.getString("resourcepay"), 0.0D);
/*  334 */         recordSet.executeSql(" insert into HrmSalaryPersonality (itemid, hrmid, salary)  values (" + str6 + "," + paramString + "," + str + ")");
/*      */         
/*  336 */         bool = true;
/*      */         
/*      */         continue;
/*      */       } 
/*  340 */       if ((!str5.equals(str2) && !str5.equals("0")) || (
/*  341 */         !str7.equals(str1) && !str7.equals("0"))) {
/*      */         continue;
/*      */       }
/*  344 */       int i = Util.getIntValue(str8, 0);
/*  345 */       int j = Util.getIntValue(str9, 99999);
/*  346 */       int k = Util.getIntValue(str3, 0);
/*      */       
/*  348 */       if (k >= i && k <= j) {
/*  349 */         this.rs.executeSql(" insert into HrmSalaryPersonality (itemid, hrmid, salary)  values (" + str6 + "," + paramString + "," + str10 + ")");
/*      */         
/*  351 */         bool = true;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initResourceSalaryInfo(boolean paramBoolean) {
/*  362 */     this.hasnohrmsalaryinfo = paramBoolean;
/*      */     
/*  364 */     this.itemids = new ArrayList();
/*  365 */     this.itemcodes = new ArrayList();
/*  366 */     this.itemtypes = new ArrayList();
/*  367 */     this.iteminfos = new ArrayList();
/*  368 */     this.taxcitys = new ArrayList();
/*  369 */     this.taxinfos = new ArrayList();
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  374 */     this.diffids = new ArrayList();
/*  375 */     this.salaryables = new ArrayList();
/*  376 */     this.counttypes = new ArrayList();
/*  377 */     this.salaryitems = new ArrayList();
/*  378 */     this.countnums = new ArrayList();
/*  379 */     this.mindifftimes = new ArrayList();
/*  380 */     this.timecounttypes = new ArrayList();
/*      */ 
/*      */     
/*  383 */     this.itemshifts = new ArrayList();
/*  384 */     this.shiftpays = new ArrayList();
/*      */ 
/*      */     
/*  387 */     this.resourceshifts = new ArrayList();
/*  388 */     this.shiftcounts = new ArrayList();
/*      */ 
/*      */     
/*  391 */     this.itemresorces = new ArrayList();
/*  392 */     this.resourcetpays = new ArrayList();
/*      */ 
/*      */     
/*  395 */     this.resourceworktimes = new ArrayList();
/*  396 */     this.timecounts = new ArrayList();
/*      */ 
/*      */     
/*  399 */     this.resourcediffids = new ArrayList();
/*  400 */     this.resourcediffinfos = new ArrayList();
/*      */     
/*  402 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  404 */     this.rs.executeSql(" select * from HrmSalaryItem");
/*  405 */     while (this.rs.next()) {
/*  406 */       String str1 = Util.null2String(this.rs.getString("id"));
/*  407 */       String str2 = Util.null2String(this.rs.getString("itemcode"));
/*  408 */       String str3 = Util.null2String(this.rs.getString("itemtype"));
/*  409 */       String str4 = "" + Util.getDoubleValue(this.rs.getString("personwelfarerate"), 0.0D);
/*  410 */       String str5 = "" + Util.getDoubleValue(this.rs.getString("companywelfarerate"), 0.0D);
/*  411 */       String str6 = "" + Util.getIntValue(this.rs.getString("taxrelateitem"), 0);
/*  412 */       String str7 = Util.null2String(this.rs.getString("amountecp"));
/*      */       
/*  414 */       this.itemids.add(str1);
/*  415 */       this.itemcodes.add(str2);
/*  416 */       this.itemtypes.add(str3);
/*      */       
/*  418 */       if (str3.equals("1") || str3.equals("7") || str3.equals("8")) { this.iteminfos.add(""); continue; }
/*  419 */        if (str3.equals("2")) {
/*  420 */         this.itemids.add(str1 + "_1");
/*  421 */         this.itemids.add(str1 + "_2");
/*  422 */         this.itemcodes.add(str2 + "_1");
/*  423 */         this.itemcodes.add(str2 + "_2");
/*  424 */         this.itemtypes.add(str3);
/*  425 */         this.itemtypes.add(str3);
/*  426 */         this.iteminfos.add(str4 + "_" + str5);
/*  427 */         this.iteminfos.add("");
/*  428 */         this.iteminfos.add(""); continue;
/*  429 */       }  if (str3.equals("3")) { this.iteminfos.add(str6); continue; }
/*  430 */        if (str3.equals("4")) { this.iteminfos.add(str7); continue; }
/*  431 */        if (str3.equals("5") || str3.equals("6")) {
/*  432 */         String str8 = "";
/*  433 */         recordSet.executeSql(" select diffid from HrmSalarySchedule where itemid = " + str1);
/*  434 */         while (recordSet.next()) {
/*  435 */           String str9 = Util.null2String(recordSet.getString("diffid"));
/*  436 */           if (str8.equals("")) { str8 = str9; continue; }
/*  437 */            str8 = str8 + "," + str9;
/*      */         } 
/*  439 */         this.iteminfos.add(str8);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  444 */     if (paramBoolean) {
/*  445 */       this.resourceitems = new ArrayList();
/*  446 */       this.amounts = new ArrayList();
/*  447 */       this.rs.executeSql(" select * from HrmSalaryPersonality ");
/*  448 */       while (this.rs.next()) {
/*  449 */         String str1 = Util.null2String(this.rs.getString("itemid"));
/*  450 */         String str2 = Util.null2String(this.rs.getString("hrmid"));
/*  451 */         String str3 = "" + Util.getDoubleValue(this.rs.getString("salary"), 0.0D);
/*  452 */         this.resourceitems.add(str2 + "_" + str1);
/*  453 */         this.amounts.add(str3);
/*      */       } 
/*      */       
/*  456 */       this.welfareratecitys = new ArrayList();
/*  457 */       this.welfarerateinfos = new ArrayList();
/*      */       
/*  459 */       this.rs.executeSql(" select cityid , itemid, personwelfarerate, companywelfarerate  from HrmSalaryWelfarerate ");
/*      */       
/*  461 */       while (this.rs.next()) {
/*  462 */         String str1 = Util.null2String(this.rs.getString("itemid"));
/*  463 */         String str2 = "" + Util.getIntValue(this.rs.getString("cityid"), 0);
/*  464 */         String str3 = "" + Util.getDoubleValue(this.rs.getString("personwelfarerate"), 0.0D);
/*  465 */         String str4 = "" + Util.getDoubleValue(this.rs.getString("companywelfarerate"), 0.0D);
/*  466 */         this.welfareratecitys.add(str1 + "_" + str2);
/*  467 */         this.welfarerateinfos.add(str3 + "_" + str4);
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  473 */       if (!this.theseldate.equals("")) {
/*      */ 
/*      */         
/*  476 */         this.rs.executeSql(" select * from HrmScheduleDiff ");
/*  477 */         while (this.rs.next()) {
/*  478 */           String str1 = Util.null2String(this.rs.getString("id"));
/*  479 */           String str2 = Util.null2String(this.rs.getString("salaryable"));
/*  480 */           String str3 = Util.null2String(this.rs.getString("counttype"));
/*  481 */           String str4 = Util.null2String(this.rs.getString("salaryitem"));
/*  482 */           String str5 = "" + Util.getDoubleValue(this.rs.getString("countnum"), 0.0D);
/*  483 */           String str6 = "" + Util.getIntValue(this.rs.getString("mindifftime"), 0);
/*  484 */           String str7 = Util.null2String(this.rs.getString("workflowid"));
/*      */           
/*  486 */           this.diffids.add(str1);
/*  487 */           this.salaryables.add(str2);
/*  488 */           this.counttypes.add(str3);
/*  489 */           this.salaryitems.add(str4);
/*  490 */           this.countnums.add(str5);
/*  491 */           this.mindifftimes.add(str6);
/*  492 */           this.timecounttypes.add(str7);
/*      */         } 
/*      */ 
/*      */         
/*  496 */         this.rs.executeSql(" select * from HrmSalaryShiftPay ");
/*  497 */         while (this.rs.next()) {
/*  498 */           String str1 = Util.null2String(this.rs.getString("itemid"));
/*  499 */           String str2 = Util.null2String(this.rs.getString("shiftid"));
/*  500 */           String str3 = "" + Util.getDoubleValue(this.rs.getString("shiftpay"), 0.0D);
/*      */           
/*  502 */           this.itemshifts.add(str1 + "_" + str2);
/*  503 */           this.shiftpays.add(str3);
/*      */         } 
/*      */ 
/*      */         
/*  507 */         this.rs.executeSql(" select * from HrmWorkTimeCount where workdate = '" + this.theseldate + "' ");
/*  508 */         while (this.rs.next()) {
/*  509 */           String str1 = Util.null2String(this.rs.getString("resourceid"));
/*  510 */           String str2 = Util.null2String(this.rs.getString("shiftid"));
/*  511 */           String str3 = "" + Util.getIntValue(this.rs.getString("workcount"), 0);
/*      */           
/*  513 */           this.resourceshifts.add(str1 + "_" + str2);
/*  514 */           this.shiftcounts.add(str3);
/*      */         } 
/*      */ 
/*      */         
/*  518 */         this.rs.executeSql(" select * from HrmSalaryResourcePay ");
/*  519 */         while (this.rs.next()) {
/*  520 */           String str1 = Util.null2String(this.rs.getString("itemid"));
/*  521 */           String str2 = Util.null2String(this.rs.getString("resourceid"));
/*  522 */           String str3 = "" + Util.getDoubleValue(this.rs.getString("resourcepay"), 0.0D);
/*      */           
/*  524 */           this.itemresorces.add(str1 + "_" + str2);
/*  525 */           this.resourcetpays.add(str3);
/*      */         } 
/*      */ 
/*      */         
/*  529 */         this.rs.executeSql(" select sum(workcount) as workcount , resourceid from HrmWorkTimeCount where workdate = '" + this.theseldate + "' group by resourceid ");
/*  530 */         while (this.rs.next()) {
/*  531 */           String str1 = Util.null2String(this.rs.getString("resourceid"));
/*  532 */           String str2 = "" + Util.getIntValue(this.rs.getString("workcount"), 0);
/*      */           
/*  534 */           this.resourceworktimes.add(str1);
/*  535 */           this.timecounts.add(str2);
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  540 */         this.rs.executeSql(" select id , resourceid , diffid , realdifftime,  realcarddifftime , difftype , startdate , enddate from HrmScheduleMaintance where startdate >= '" + this.theselbegindate + "' and enddate <= '" + this.theselenddate + "' ");
/*  541 */         while (this.rs.next()) {
/*  542 */           String str1 = Util.null2String(this.rs.getString("diffid"));
/*  543 */           int i = this.diffids.indexOf(str1);
/*  544 */           if (i == -1)
/*      */             continue; 
/*  546 */           String str2 = this.salaryables.get(i);
/*  547 */           if (str2.equals(""))
/*      */             continue; 
/*  549 */           String str3 = Util.null2String(this.rs.getString("resourceid"));
/*  550 */           String[] arrayOfString = new String[6];
/*      */           
/*  552 */           arrayOfString[0] = Util.null2String(this.rs.getString("id"));
/*  553 */           arrayOfString[1] = "" + Util.getIntValue(this.rs.getString("realdifftime"), 0);
/*  554 */           arrayOfString[2] = "" + Util.getIntValue(this.rs.getString("realcarddifftime"), 0);
/*  555 */           arrayOfString[3] = "" + Util.getIntValue(this.rs.getString("difftype"), 0);
/*  556 */           arrayOfString[4] = Util.null2String(this.rs.getString("startdate"));
/*  557 */           arrayOfString[5] = Util.null2String(this.rs.getString("enddate"));
/*      */           
/*  559 */           int j = this.resourcediffids.indexOf(str3 + "_" + str1);
/*  560 */           if (j == -1) {
/*  561 */             ArrayList<String[]> arrayList2 = new ArrayList();
/*  562 */             arrayList2.add(arrayOfString);
/*  563 */             this.resourcediffids.add(str3 + "_" + str1);
/*  564 */             this.resourcediffinfos.add(arrayList2); continue;
/*      */           } 
/*  566 */           ArrayList<String[]> arrayList1 = this.resourcediffinfos.get(j);
/*  567 */           arrayList1.add(arrayOfString);
/*  568 */           this.resourcediffinfos.set(j, arrayList1);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  575 */     this.rs.executeSql(" select a.id , a.itemid, a.cityid, a.taxbenchmark, b.ranklow, b.rankhigh, b.taxrate  from HrmSalaryTaxbench a, HrmSalaryTaxrate b  where a.id = b.benchid order by a.id , b.ranklow ");
/*      */ 
/*      */ 
/*      */     
/*  579 */     String str = "";
/*  580 */     ArrayList<String> arrayList = null;
/*  581 */     while (this.rs.next()) {
/*  582 */       String str1 = Util.null2String(this.rs.getString("id"));
/*  583 */       String str2 = Util.null2String(this.rs.getString("itemid"));
/*  584 */       String str3 = "" + Util.getIntValue(this.rs.getString("cityid"), 0);
/*  585 */       String str4 = "" + Util.getIntValue(this.rs.getString("taxbenchmark"), 0);
/*  586 */       String str5 = "" + Util.getIntValue(this.rs.getString("ranklow"), 0);
/*  587 */       String str6 = "" + Util.getIntValue(this.rs.getString("rankhigh"), 0);
/*  588 */       String str7 = "" + Util.getIntValue(this.rs.getString("taxrate"), 0);
/*  589 */       if (str6.equals("0")) str6 = "99999999";
/*      */       
/*  591 */       if (!str1.equals(str)) {
/*  592 */         arrayList = new ArrayList();
/*  593 */         arrayList.add(str4);
/*  594 */         this.taxcitys.add(str2 + "_" + str3);
/*  595 */         this.taxinfos.add(arrayList);
/*  596 */         str = str1;
/*      */       } 
/*  598 */       arrayList.add(str5 + "_" + str6 + "_" + str7);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initResourcePay(String paramString1, String paramString2, String paramString3, int paramInt) {
/*  606 */     char c = Util.getSeparator();
/*  607 */     this.theseldate = paramString1;
/*  608 */     this.theselbegindate = paramString2;
/*  609 */     this.theselenddate = paramString3;
/*      */     
/*  611 */     String str = paramString1 + c + "0";
/*      */     
/*  613 */     if (paramInt != 0) {
/*  614 */       this.thepayid = "" + paramInt;
/*  615 */       this.rs.executeSql(" delete from HrmSalaryPaydetail where payid = " + paramInt);
/*      */       
/*  617 */       this.rs.executeSql(" delete from HrmSalaryDiffDetail where payid = " + paramInt);
/*      */     } else {
/*      */       
/*  620 */       this.rs.executeProc("HrmSalaryPay_Insert", str);
/*  621 */       if (this.rs.next()) paramInt = Util.getIntValue(this.rs.getString(1), 0); 
/*  622 */       if (paramInt == 0)
/*  623 */         return;  this.thepayid = "" + paramInt;
/*      */     } 
/*      */     
/*  626 */     ArrayList<String> arrayList1 = new ArrayList();
/*  627 */     ArrayList<String> arrayList2 = new ArrayList();
/*  628 */     this.rs.executeSql("select id, itemtype from HrmSalaryItem");
/*  629 */     while (this.rs.next()) {
/*  630 */       arrayList1.add(Util.null2String(this.rs.getString("id")));
/*  631 */       arrayList2.add(Util.null2String(this.rs.getString("itemtype")));
/*      */     } 
/*      */     
/*  634 */     initResourceSalaryInfo(true);
/*      */     
/*  636 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  638 */     if (recordSet.getDBType().equals("oracle")) {
/*  639 */       recordSet.executeSql(" select a.id , b.locationcity  from HrmResource a , HrmLocations b\twhere a.locationid = b.id and a.status !='4' and a.status !='7'  order by a.departmentid , a.id ");
/*  640 */     } else if (this.rs.getDBType().equals("db2")) {
/*  641 */       recordSet.executeSql(" select a.id , b.locationcity  from HrmResource a left join HrmLocations b   on a.locationid = b.id where a.status !=4 and a.status !=7  order by a.departmentid , a.id ");
/*      */     
/*      */     }
/*      */     else {
/*      */       
/*  646 */       recordSet.executeSql(" select a.id , b.locationcity  from HrmResource a left join HrmLocations b   on a.locationid = b.id where a.status !='4' and a.status !='7'  order by a.departmentid , a.id ");
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  651 */     while (recordSet.next()) {
/*  652 */       String str1 = Util.null2String(recordSet.getString("id"));
/*  653 */       String str2 = Util.null2String(recordSet.getString("locationcity"));
/*      */       
/*  655 */       setResourceid(str1);
/*  656 */       setCityid(str2);
/*      */       
/*  658 */       for (byte b = 0; b < arrayList1.size(); b++) {
/*  659 */         String str3 = arrayList1.get(b);
/*  660 */         String str4 = arrayList2.get(b);
/*  661 */         setItemid(str3);
/*  662 */         setItemtype(str4);
/*      */         
/*  664 */         if (!str4.equals("2")) {
/*  665 */           double d = getItemSalary();
/*  666 */           str = "" + paramInt + c + str3 + c + str1 + c + d;
/*  667 */           recordSet.executeProc("HrmSalaryPaydetail_Insert", str);
/*      */         } else {
/*  669 */           double d1 = getItemPersonSalary();
/*  670 */           str = "" + paramInt + c + str3 + "_1" + c + str1 + c + d1;
/*  671 */           recordSet.executeProc("HrmSalaryPaydetail_Insert", str);
/*      */           
/*  673 */           double d2 = getItemCompanySalary();
/*  674 */           str = "" + paramInt + c + str3 + "_2" + c + str1 + c + d2;
/*  675 */           recordSet.executeProc("HrmSalaryPaydetail_Insert", str);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */   
/*      */   public double getItemSalary() {
/*  682 */     return getItemSalary(this.theitemid, 0);
/*      */   }
/*      */   
/*      */   public double getItemPersonSalary() {
/*  686 */     return getItemSalary(this.theitemid + "_1", 0);
/*      */   }
/*      */   
/*      */   public double getItemCompanySalary() {
/*  690 */     return getItemSalary(this.theitemid + "_2", 0);
/*      */   }
/*      */ 
/*      */   
/*      */   private double getItemSalary(String paramString, int paramInt) {
/*  695 */     int i = this.itemids.indexOf(paramString);
/*  696 */     if (i == -1) return 0.0D;
/*      */ 
/*      */     
/*  699 */     int j = this.resourceitems.indexOf(this.theresourceid + "_" + paramString);
/*  700 */     if (j != -1) return Util.getDoubleValue(this.amounts.get(j), 0.0D);
/*      */ 
/*      */     
/*  703 */     String str = this.itemtypes.get(i);
/*      */     
/*  705 */     if (str.equals("1")) {
/*  706 */       this.resourceitems.add(this.theresourceid + "_" + paramString);
/*  707 */       this.amounts.add("0");
/*  708 */       return 0.0D;
/*      */     } 
/*      */ 
/*      */     
/*  712 */     if (str.equals("2")) {
/*  713 */       int k = paramString.indexOf("_");
/*  714 */       if (k == -1 || !this.hasnohrmsalaryinfo) {
/*  715 */         this.resourceitems.add(this.theresourceid + "_" + paramString);
/*  716 */         this.amounts.add("0");
/*  717 */         return 0.0D;
/*      */       } 
/*  719 */       if (paramInt++ > 10) return 0.0D; 
/*  720 */       String str1 = paramString.substring(0, k);
/*  721 */       double d = getItemSalary(str1, paramInt);
/*  722 */       getSalaryWelfarerate(str1, paramInt, d);
/*  723 */       return getItemSalary(paramString, paramInt);
/*      */     } 
/*      */ 
/*      */     
/*  727 */     if (paramInt++ > 10) return 0.0D;
/*      */     
/*  729 */     if (str.equals("3")) {
/*  730 */       double d = getSalaryTax(paramString, paramInt, i);
/*  731 */       this.resourceitems.add(this.theresourceid + "_" + paramString);
/*  732 */       this.amounts.add("" + d);
/*  733 */       return d;
/*      */     } 
/*      */     
/*  736 */     if (str.equals("4")) {
/*  737 */       double d = getSalarySum(paramString, paramInt, i);
/*  738 */       this.resourceitems.add(this.theresourceid + "_" + paramString);
/*  739 */       this.amounts.add("" + d);
/*  740 */       return d;
/*      */     } 
/*      */     
/*  743 */     if (str.equals("5") || str.equals("6")) {
/*  744 */       double d = getSalaryDiff(paramString, paramInt, i);
/*  745 */       this.resourceitems.add(this.theresourceid + "_" + paramString);
/*  746 */       this.amounts.add("" + d);
/*  747 */       return d;
/*      */     } 
/*      */     
/*  750 */     if (str.equals("7")) {
/*  751 */       double d = getSalaryShift(paramString, paramInt);
/*  752 */       this.resourceitems.add(this.theresourceid + "_" + paramString);
/*  753 */       this.amounts.add("" + d);
/*  754 */       return d;
/*      */     } 
/*      */     
/*  757 */     if (str.equals("8")) {
/*  758 */       double d = getSalaryResource(paramString, paramInt);
/*  759 */       this.resourceitems.add(this.theresourceid + "_" + paramString);
/*  760 */       this.amounts.add("" + d);
/*  761 */       return d;
/*      */     } 
/*      */     
/*  764 */     return 0.0D;
/*      */   }
/*      */ 
/*      */   
/*      */   private void getSalaryWelfarerate(String paramString, int paramInt, double paramDouble) {
/*  769 */     if (paramDouble == 0.0D) {
/*  770 */       this.resourceitems.add(this.theresourceid + "_" + paramString + "_1");
/*  771 */       this.amounts.add("0");
/*  772 */       this.resourceitems.add(this.theresourceid + "_" + paramString + "_2");
/*  773 */       this.amounts.add("0");
/*      */       
/*      */       return;
/*      */     } 
/*  777 */     int i = this.welfareratecitys.indexOf(paramString + "_" + this.thecityid);
/*  778 */     if (i == -1) i = this.welfareratecitys.indexOf(paramString + "_0"); 
/*  779 */     if (i == -1) {
/*  780 */       this.resourceitems.add(this.theresourceid + "_" + paramString + "_1");
/*  781 */       this.amounts.add("0");
/*  782 */       this.resourceitems.add(this.theresourceid + "_" + paramString + "_2");
/*  783 */       this.amounts.add("0");
/*      */       
/*      */       return;
/*      */     } 
/*  787 */     String str = this.welfarerateinfos.get(i);
/*  788 */     String[] arrayOfString = Util.TokenizerString2(str, "_");
/*  789 */     BigDecimal bigDecimal1 = new BigDecimal(arrayOfString[0]);
/*  790 */     BigDecimal bigDecimal2 = new BigDecimal(arrayOfString[1]);
/*  791 */     BigDecimal bigDecimal3 = new BigDecimal(paramDouble);
/*  792 */     BigDecimal bigDecimal4 = bigDecimal3.multiply(bigDecimal1).divide(new BigDecimal(100), 2, 5);
/*  793 */     BigDecimal bigDecimal5 = bigDecimal3.multiply(bigDecimal2).divide(new BigDecimal(100), 2, 5);
/*  794 */     this.resourceitems.add(this.theresourceid + "_" + paramString + "_1");
/*  795 */     this.amounts.add("" + bigDecimal4.doubleValue());
/*  796 */     this.resourceitems.add(this.theresourceid + "_" + paramString + "_2");
/*  797 */     this.amounts.add("" + bigDecimal5.doubleValue());
/*      */   }
/*      */ 
/*      */   
/*      */   private double getSalaryTax(String paramString, int paramInt1, int paramInt2) {
/*  802 */     String str = this.iteminfos.get(paramInt2);
/*  803 */     double d1 = getItemSalary(str, paramInt1);
/*  804 */     if (d1 == 0.0D) return 0.0D;
/*      */     
/*  806 */     int i = this.taxcitys.indexOf(paramString + "_" + this.thecityid);
/*  807 */     if (i == -1) i = this.taxcitys.indexOf(paramString + "_0"); 
/*  808 */     if (i == -1) return 0.0D;
/*      */     
/*  810 */     ArrayList<String> arrayList = this.taxinfos.get(i);
/*  811 */     double d2 = Util.getDoubleValue(arrayList.get(0), 0.0D);
/*      */     
/*  813 */     double d3 = d1 - d2;
/*  814 */     if (d3 <= 0.0D) return 0.0D;
/*      */     
/*  816 */     BigDecimal bigDecimal = new BigDecimal(0);
/*  817 */     for (byte b = 1; b < arrayList.size(); b++) {
/*  818 */       String str1 = arrayList.get(b);
/*  819 */       String[] arrayOfString = Util.TokenizerString2(str1, "_");
/*  820 */       double d4 = Util.getDoubleValue(arrayOfString[0], 0.0D);
/*  821 */       double d5 = Util.getDoubleValue(arrayOfString[1], 9999999.0D);
/*  822 */       double d6 = Util.getDoubleValue(arrayOfString[2], 0.0D);
/*      */       
/*  824 */       if (d3 > d5) {
/*  825 */         double d = d5 - d4;
/*  826 */         BigDecimal bigDecimal1 = new BigDecimal(d);
/*  827 */         BigDecimal bigDecimal2 = new BigDecimal(d6);
/*  828 */         BigDecimal bigDecimal3 = bigDecimal1.multiply(bigDecimal2).divide(new BigDecimal(100), 2, 5);
/*  829 */         bigDecimal = bigDecimal.add(bigDecimal3);
/*      */       } else {
/*  831 */         double d = d3 - d4;
/*  832 */         BigDecimal bigDecimal1 = new BigDecimal(d);
/*  833 */         BigDecimal bigDecimal2 = new BigDecimal(d6);
/*  834 */         BigDecimal bigDecimal3 = bigDecimal1.multiply(bigDecimal2).divide(new BigDecimal(100), 2, 5);
/*  835 */         bigDecimal = bigDecimal.add(bigDecimal3);
/*      */         
/*      */         break;
/*      */       } 
/*      */     } 
/*  840 */     return bigDecimal.doubleValue();
/*      */   }
/*      */ 
/*      */   
/*      */   private double getSalaryDiff(String paramString, int paramInt1, int paramInt2) {
/*  845 */     if (this.theseldate.equals("")) return 0.0D;
/*      */     
/*  847 */     String str = this.iteminfos.get(paramInt2);
/*  848 */     if (str.equals("")) return 0.0D;
/*      */     
/*  850 */     double d1 = 0.0D;
/*      */     
/*  852 */     HrmKqSystemComInfo hrmKqSystemComInfo = new HrmKqSystemComInfo();
/*  853 */     int i = Util.getIntValue(hrmKqSystemComInfo.getAvgworkhour(), 172);
/*      */     
/*  855 */     double d2 = i * 60.0D;
/*      */     
/*  857 */     ArrayList<String> arrayList = Util.TokenizerString(str, ",");
/*      */     
/*  859 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  860 */       String str1 = arrayList.get(b);
/*      */       
/*  862 */       int j = this.resourcediffids.indexOf(this.theresourceid + "_" + str1);
/*  863 */       if (j != -1) {
/*      */         
/*  865 */         ArrayList<String[]> arrayList1 = this.resourcediffinfos.get(j);
/*      */         
/*  867 */         for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  868 */           String[] arrayOfString = arrayList1.get(b1);
/*      */           
/*  870 */           String str2 = arrayOfString[0];
/*  871 */           int k = Util.getIntValue(arrayOfString[1], 0);
/*  872 */           int m = Util.getIntValue(arrayOfString[2], 0);
/*  873 */           String str3 = arrayOfString[3];
/*  874 */           String str4 = arrayOfString[4];
/*  875 */           String str5 = arrayOfString[5];
/*      */           
/*  877 */           int n = 0;
/*  878 */           double d3 = 0.0D;
/*  879 */           int i1 = this.diffids.indexOf(str1);
/*  880 */           String str6 = this.counttypes.get(i1);
/*  881 */           String str7 = this.salaryitems.get(i1);
/*  882 */           double d4 = Util.getDoubleValue(this.countnums.get(i1), 0.0D);
/*  883 */           int i2 = Util.getIntValue(this.mindifftimes.get(i1), 0);
/*  884 */           int i3 = Util.getIntValue(this.timecounttypes.get(i1));
/*      */ 
/*      */ 
/*      */           
/*  888 */           switch (i3) {
/*      */             case 1:
/*  890 */               n = k;
/*      */               break;
/*      */             case 2:
/*  893 */               n = m;
/*      */               break;
/*      */             case 3:
/*  896 */               n = Math.max(k, m);
/*      */               break;
/*      */             case 4:
/*  899 */               n = Math.min(k, m);
/*      */               break;
/*      */           } 
/*      */ 
/*      */           
/*  904 */           if (!str3.equals("0") && 
/*  905 */             i2 != 0) {
/*  906 */             int i4 = n / i2;
/*  907 */             if (i4 * i2 == n) { n = n; }
/*  908 */             else { n = (i4 + 1) * i2; }
/*      */           
/*      */           } 
/*      */ 
/*      */           
/*  913 */           if (n > 0) {
/*      */ 
/*      */             
/*  916 */             if (str6.equals("1"))
/*  917 */             { if (i2 != 0)
/*  918 */               { d3 = n * 1.0D / i2 * 1.0D * d4; }
/*  919 */               else { d3 = d4; }
/*      */                }
/*  921 */             else { double d = getItemSalary(str7, paramInt1);
/*  922 */               d3 = n * d * d4 * 1.0D / d2; }
/*      */ 
/*      */ 
/*      */             
/*  926 */             if (d3 != 0.0D)
/*  927 */             { d1 += d3;
/*      */               
/*  929 */               char c = Util.getSeparator();
/*  930 */               String str8 = paramString + c + this.theresourceid + c + this.thepayid + c + str2 + c + str1 + c + str4 + c + str5 + c + "" + n + c + "" + d3;
/*      */               
/*  932 */               this.rs.executeProc("HrmSalaryDiffDetail_Insert", str8); } 
/*      */           } 
/*      */         } 
/*      */       } 
/*  936 */     }  BigDecimal bigDecimal = new BigDecimal(d1);
/*  937 */     bigDecimal = bigDecimal.divide(new BigDecimal(1), 2, 5);
/*      */     
/*  939 */     return bigDecimal.doubleValue();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private double getSalaryShift(String paramString, int paramInt) {
/*  945 */     if (this.theseldate.equals("")) return 0.0D;
/*      */     
/*  947 */     double d = 0.0D;
/*      */     
/*  949 */     for (byte b = 0; b < this.itemshifts.size(); b++) {
/*  950 */       String str = this.itemshifts.get(b);
/*  951 */       if (str.indexOf(paramString) == 0) {
/*  952 */         double d1 = Util.getDoubleValue(this.shiftpays.get(b), 0.0D);
/*      */         
/*  954 */         String str1 = str.substring(str.indexOf("_") + 1, str.length());
/*  955 */         int i = this.resourceshifts.indexOf(this.theresourceid + "_" + str1);
/*  956 */         if (i != -1) {
/*  957 */           int j = Util.getIntValue(this.shiftcounts.get(i), 0);
/*  958 */           d += j * d1;
/*      */         } 
/*      */       } 
/*      */     } 
/*  962 */     return d;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private double getSalaryResource(String paramString, int paramInt) {
/*  968 */     if (this.theseldate.equals("")) return 0.0D;
/*      */     
/*  970 */     double d1 = 0.0D;
/*      */     
/*  972 */     double d2 = 0.0D;
/*  973 */     int i = this.itemresorces.indexOf(paramString + "_" + this.theresourceid);
/*  974 */     if (i != -1) d2 = Util.getDoubleValue(this.resourcetpays.get(i), 0.0D);
/*      */     
/*  976 */     int j = 0;
/*  977 */     int k = this.resourceworktimes.indexOf(this.theresourceid);
/*  978 */     if (k != -1) {
/*  979 */       j = Util.getIntValue(this.timecounts.get(k), 0);
/*      */     }
/*  981 */     d1 = j * d2;
/*      */     
/*  983 */     return d1;
/*      */   }
/*      */ 
/*      */   
/*      */   private double getSalarySum(String paramString, int paramInt1, int paramInt2) {
/*      */     try {
/*  989 */       String str1 = this.iteminfos.get(paramInt2);
/*  990 */       String str2 = "";
/*  991 */       String str3 = "";
/*  992 */       while ((str2 = findSumItem(str1)) != null) {
/*  993 */         int i = this.itemcodes.indexOf(str2);
/*  994 */         double d = 0.0D;
/*  995 */         if (i != -1) {
/*  996 */           String str = this.itemids.get(i);
/*  997 */           d = getItemSalary(str, paramInt1);
/*      */         } 
/*  999 */         str1 = Util.StringReplaceOnce(str1, "$" + str2, "" + d);
/*      */       } 
/*      */       
/* 1002 */       if (str1.equals("")) str1 = "0";
/*      */ 
/*      */       
/* 1005 */       if (this.statement.getDBType().equals("oracle")) { str3 = " select " + str1 + " from dual "; }
/* 1006 */       else if (this.statement.getDBType().equals("db2")) { str3 = " select " + str1 + " from SYSIBM.SYSDUMMY1 "; }
/* 1007 */       else { str3 = " select " + str1; }
/* 1008 */        this.statement.setStatementSql(str3);
/* 1009 */       this.statement.executeQuery();
/* 1010 */       if (this.statement.next()) {
/* 1011 */         double d = Util.getDoubleValue(this.statement.getString(1), 0.0D);
/* 1012 */         BigDecimal bigDecimal = (new BigDecimal(d)).divide(new BigDecimal(1), 2, 5);
/* 1013 */         return bigDecimal.doubleValue();
/* 1014 */       }  return 0.0D;
/* 1015 */     } catch (Exception exception) {
/* 1016 */       return 0.0D;
/*      */     } 
/*      */   }
/*      */   
/*      */   private String findSumItem(String paramString) {
/* 1021 */     int i = paramString.indexOf("$");
/* 1022 */     if (i < 0) return null;
/*      */     
/* 1024 */     String str = "";
/* 1025 */     char c = '\002';
/* 1026 */     i++;
/*      */     
/* 1028 */     if (i < paramString.length()) c = paramString.charAt(i);
/*      */     
/* 1030 */     while (Character.isLetterOrDigit(c) || c == '_') {
/* 1031 */       str = str + "" + c;
/* 1032 */       i++;
/* 1033 */       if (i < paramString.length()) c = paramString.charAt(i);
/*      */     
/*      */     } 
/*      */     
/* 1037 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getScheduleMonth(String paramString1, int paramInt, String paramString2) {
/* 1047 */     String str = "0";
/*      */     try {
/* 1049 */       String str1 = paramString2.substring(0, 4);
/* 1050 */       String str2 = paramString2.substring(5);
/* 1051 */       String str3 = "select sum(hours) as schedulemonth from hrmschedulemonth where hrmid=" + paramInt + " and difftype=" + paramString1 + " and theyear='" + str1 + "' and themonth='" + str2 + "'";
/* 1052 */       this.statement.setStatementSql(str3);
/* 1053 */       this.statement.executeQuery();
/* 1054 */       if (this.statement.next()) str = "" + Util.getFloatValue(this.statement.getString("schedulemonth"), 0.0F); 
/* 1055 */     } catch (Exception exception) {}
/*      */     
/* 1057 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getKqData(String paramString1, int paramInt, String paramString2, String paramString3) {
/* 1071 */     String str = "0";
/*      */     try {
/* 1073 */       String str1 = "kq_format_total";
/* 1074 */       String str2 = paramString2.substring(0, 4);
/* 1075 */       String str3 = String.valueOf(Util.getIntValue(paramString2.substring(5)));
/* 1076 */       String str4 = "kqdate";
/* 1077 */       String str5 = "kqdate";
/* 1078 */       String str6 = paramString1.toLowerCase(Locale.ROOT);
/* 1079 */       String str7 = "";
/* 1080 */       if (paramString1.startsWith("leave")) {
/* 1081 */         str1 = "kq_flow_split_leave";
/* 1082 */         str4 = "fromdatedb";
/* 1083 */         str5 = "todatedb";
/* 1084 */         str6 = "durationdb";
/* 1085 */         str7 = " and newleavetype = " + paramString1.substring("leave".length());
/* 1086 */       } else if (paramString1.startsWith("overtime")) {
/* 1087 */         str1 = "KQ_FLOW_OVERTIME";
/* 1088 */         str4 = "fromdatedb";
/* 1089 */         str5 = "todatedb";
/* 1090 */         str6 = "duration_min";
/* 1091 */         str7 = " and paidleaveenable = " + paramString1.charAt("overtime".length()) + " and changetype = " + paramString1.charAt("overtime".length() + 1);
/*      */       } 
/* 1093 */       String str8 = "substr(" + str4 + ", 0, 4)";
/* 1094 */       String str9 = "substr(" + str4 + ", 6, 2)";
/* 1095 */       String str10 = "substr(" + str5 + ", 0, 4)";
/* 1096 */       String str11 = "substr(" + str5 + ", 6, 2)";
/*      */       
/* 1098 */       if ("oracle".equalsIgnoreCase(this.rs.getDBType())) {
/* 1099 */         str8 = "substr(" + str4 + ", 0, 4)";
/* 1100 */         str9 = "substr(" + str4 + ", 6, 2)";
/* 1101 */         str10 = "substr(" + str5 + ", 0, 4)";
/* 1102 */         str11 = "substr(" + str5 + ", 6, 2)";
/* 1103 */       } else if ("mysql".equalsIgnoreCase(this.rs.getDBType())) {
/* 1104 */         str8 = "SUBSTRING(" + str4 + ", 1, 4)";
/* 1105 */         str9 = "SUBSTRING(" + str4 + ", 6, 2)";
/* 1106 */         str10 = "SUBSTRING(" + str5 + ", 1, 4)";
/* 1107 */         str11 = "SUBSTRING(" + str5 + ", 6, 2)";
/*      */       } 
/* 1109 */       String str12 = "";
/*      */       
/* 1111 */       if (paramString3.equals("4"))
/* 1112 */         str12 = "select sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str9 + " = " + str3 + " and " + str10 + " = " + str2 + " and " + str11 + " = " + str3 + str7; 
/* 1113 */       if (paramString3.equals("3")) {
/* 1114 */         if (Util.getIntValue(str3) > 0 && Util.getIntValue(str3) < 4)
/* 1115 */           str12 = "select sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str10 + " = " + str2 + " and " + str11 + "<4" + str7; 
/* 1116 */         if (Util.getIntValue(str3) > 3 && Util.getIntValue(str3) < 7)
/* 1117 */           str12 = "select sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str10 + " = " + str2 + " and " + str9 + ">3 and " + str11 + "<7" + str7; 
/* 1118 */         if (Util.getIntValue(str3) > 6 && Util.getIntValue(str3) < 10)
/* 1119 */           str12 = "select sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str10 + " = " + str2 + " and " + str9 + ">6 and " + str11 + "<10" + str7; 
/* 1120 */         if (Util.getIntValue(str3) > 9)
/* 1121 */           str12 = "select  sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str10 + " = " + str2 + " and " + str9 + ">9" + str7; 
/*      */       } 
/* 1123 */       if (paramString3.equals("2"))
/* 1124 */         if (Util.getIntValue(str3) < 7) {
/* 1125 */           str12 = "select sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str10 + " = " + str2 + " and " + str11 + "<7" + str7;
/*      */         } else {
/* 1127 */           str12 = "select sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str10 + " = " + str2 + " and " + str9 + ">6" + str7;
/*      */         }  
/* 1129 */       if (paramString3.equals("1"))
/* 1130 */         str12 = "select sum(" + str6 + ") as target from " + str1 + " where resourceid=" + paramInt + " and " + str8 + " = " + str2 + " and " + str10 + " = " + str2 + str7; 
/* 1131 */       this.statement.setStatementSql(str12);
/* 1132 */       this.statement.executeQuery();
/* 1133 */       if (this.statement.next()) {
/* 1134 */         if (str6.endsWith("mins")) {
/*      */           
/* 1136 */           str = "" + KQDurationCalculatorUtil.getDurationRound("" + (Util.getDoubleValue(this.statement.getString("target"), 0.0D) / 60.0D));
/*      */         } else {
/* 1138 */           str = "" + Util.getDoubleValue(this.statement.getString("target"), 0.0D);
/*      */         } 
/*      */       }
/* 1141 */     } catch (Exception exception) {}
/*      */ 
/*      */     
/* 1144 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getCompensationTarget(String paramString1, int paramInt, String paramString2, String paramString3) {
/* 1157 */     String str = "0";
/*      */     try {
/* 1159 */       String str1 = paramString2.substring(0, 4);
/* 1160 */       String str2 = String.valueOf(Util.getIntValue(paramString2.substring(5)));
/* 1161 */       String str3 = "";
/*      */       
/* 1163 */       if (paramString3.equals("4")) {
/* 1164 */         str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + " and a.compensationMonth=" + str2;
/*      */       }
/* 1166 */       if (paramString3.equals("3")) {
/* 1167 */         if (Util.getIntValue(str2) > 0 && Util.getIntValue(str2) < 4)
/* 1168 */           str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + " and a.compensationMonth<4"; 
/* 1169 */         if (Util.getIntValue(str2) > 3 && Util.getIntValue(str2) < 7)
/* 1170 */           str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + " a.compensationMonth>3 and a.compensationMonth<7"; 
/* 1171 */         if (Util.getIntValue(str2) > 6 && Util.getIntValue(str2) < 10)
/* 1172 */           str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + " a.compensationMonth>6 and a.compensationMonth<10"; 
/* 1173 */         if (Util.getIntValue(str2) > 9)
/* 1174 */           str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + " a.compensationMonth>9"; 
/*      */       } 
/* 1176 */       if (paramString3.equals("2"))
/* 1177 */         if (Util.getIntValue(str2) < 7) {
/* 1178 */           str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + " and a.compensationMonth>0 and a.compensationMonth<7";
/*      */         } else {
/* 1180 */           str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + " and a.compensationMonth>6";
/*      */         }  
/* 1182 */       if (paramString3.equals("1"))
/* 1183 */         str3 = "select sum(target) as target from HRM_CompensationTargetInfo a,HRM_CompensationTargetDetail b where a.id=b.compensationTargetid and a.userid=" + paramInt + " and b.targetid=" + paramString1 + " and a.compensationyear=" + str1 + ""; 
/* 1184 */       this.statement.setStatementSql(str3);
/* 1185 */       this.statement.executeQuery();
/* 1186 */       if (this.statement.next()) str = "" + Util.getFloatValue(this.statement.getString("target"), 0.0F); 
/* 1187 */     } catch (Exception exception) {}
/*      */     
/* 1189 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getPiece(int paramInt, String paramString) {
/* 1200 */     String str = "0";
/*      */     try {
/* 1202 */       String str1 = paramString.substring(0, 4);
/* 1203 */       String str2 = String.valueOf(Util.getIntValue(paramString.substring(5)));
/* 1204 */       String str3 = "select sum(a.pieceNum*b.price) as piece from HRM_PieceRateInfo a,HRM_PieceRateSetting b,hrmresource c where a.pieceRateNo=b.pieceRateNo and a.usercode=c.workcode and c.id=" + paramInt + " and pieceyear=" + str1 + " and piecemonth=" + str2 + " and b.subcompanyid=c.subcompanyid1";
/* 1205 */       this.statement.setStatementSql(str3);
/* 1206 */       this.statement.executeQuery();
/* 1207 */       if (this.statement.next()) str = "" + Util.getFloatValue(this.statement.getString("piece"), 0.0F); 
/* 1208 */     } catch (Exception exception) {}
/*      */     
/* 1210 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getlastMonthSalary(String paramString1, int paramInt, String paramString2, String paramString3) {
/* 1222 */     RecordSet recordSet = new RecordSet();
/* 1223 */     String str = "0";
/* 1224 */     if (!"12".equals(paramString2.substring(5)) || !"1".equals(paramString3)) {
/* 1225 */       if (paramString1.equals("-1")) {
/*      */         try {
/* 1227 */           str = CostBiz.getInstance().getTotalCost("" + paramInt, paramString2);
/* 1228 */         } catch (Exception exception) {
/* 1229 */           exception.printStackTrace();
/*      */         } 
/*      */       } else {
/* 1232 */         recordSet.executeQuery("select t2.salary from HrmSalaryPay t1,HrmSalaryPaydetail t2 where t1.id=t2.payid and t2.itemid=? and t2.hrmid=? and t1.paydate=?", new Object[] { paramString1, Integer.valueOf(paramInt), paramString2 });
/* 1233 */         if (recordSet.next()) {
/* 1234 */           str = "" + Util.getFloatValue(recordSet.getString("salary"), 0.0F);
/*      */         }
/*      */       } 
/*      */     }
/* 1238 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseCondition(int paramInt, String paramString1, String paramString2) {
/*      */     try {
/* 1253 */       if (paramString2.equals("")) {
/* 1254 */         return "true";
/*      */       }
/* 1256 */       String str1 = Util.match(paramString2, "@[^@]*@");
/* 1257 */       while (!str1.equals("")) {
/* 1258 */         String str4 = str1.substring(1, str1.lastIndexOf("@"));
/* 1259 */         String str5 = getScheduleMonth(str4, paramInt, paramString1);
/* 1260 */         paramString2 = Util.replace(paramString2, "@[^@]*@", str5, 1);
/* 1261 */         str1 = Util.match(paramString2, "@[^@]*@");
/*      */       } 
/*      */       
/* 1264 */       String str2 = Util.match(paramString2, "\\$[^\\$]*\\$");
/* 1265 */       while (!str2.equals("")) {
/* 1266 */         String str4 = str2.substring(1, str2.lastIndexOf("$"));
/* 1267 */         String str5 = str4.substring(0, str4.indexOf("("));
/* 1268 */         String str6 = str4.substring(str4.indexOf("(") + 1, str4.indexOf(")"));
/* 1269 */         String str7 = getCompensationTarget(str5, paramInt, paramString1, str6);
/* 1270 */         paramString2 = Util.replace(paramString2, "\\$[^\\$]*\\$", str7, 1);
/* 1271 */         str2 = Util.match(paramString2, "\\$[^\\$]*\\$");
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1276 */       String str3 = Util.match(paramString2, "\\~[^\\~]*\\~");
/* 1277 */       while (!str3.equals("")) {
/* 1278 */         String str4 = str3.substring(1, str3.lastIndexOf("~"));
/* 1279 */         String str5 = str4.substring(0, str4.indexOf("("));
/* 1280 */         String str6 = str4.substring(str4.indexOf("(") + 1, str4.indexOf(")"));
/* 1281 */         String str7 = getKqData(str5, paramInt, paramString1, str6);
/* 1282 */         paramString2 = Util.replace(paramString2, "\\~[^\\~]*\\~", str7, 1);
/* 1283 */         str3 = Util.match(paramString2, "\\~[^\\~]*\\~");
/*      */       } 
/*      */       
/* 1286 */       return paramString2;
/* 1287 */     } catch (Exception exception) {
/*      */       
/* 1289 */       return "";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean assertCondition(String paramString) {
/*      */     try {
/* 1311 */       return ((Boolean)AviatorEvaluator.execute(paramString)).booleanValue();
/* 1312 */     } catch (Exception exception) {
/*      */       
/* 1314 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String eval(String paramString) {
/*      */     try {
/* 1337 */       String str = Util.null2String(AviatorEvaluator.execute(paramString));
/* 1338 */       if (str.equals("")) str = "0"; 
/* 1339 */       return str;
/* 1340 */     } catch (Exception exception) {
/*      */       
/* 1342 */       return "0";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseFormula(int paramInt1, int paramInt2, String paramString1, String paramString2, List paramList) {
/*      */     try {
/* 1359 */       String str1 = Util.match(paramString2, "#[^#]*#");
/* 1360 */       while (!str1.equals("")) {
/*      */         
/* 1362 */         String str6 = str1.substring(1, str1.lastIndexOf("#"));
/* 1363 */         if (paramList.contains(str6)) {
/* 1364 */           writeLog("Dead loop detected in formula:" + paramString2);
/* 1365 */           return "0";
/*      */         } 
/* 1367 */         String str7 = "0";
/* 1368 */         if (str6.indexOf("_") > 0) {
/* 1369 */           int i = Util.getIntValue(str6.substring(0, str6.indexOf("_")));
/* 1370 */           int j = Util.getIntValue(str6.substring(str6.indexOf("_") + 1));
/* 1371 */           str7 = getWelfareTypeSalary(paramInt1, i, paramInt2, j, paramList);
/*      */         
/*      */         }
/* 1374 */         else if (str6.equals("-1")) {
/* 1375 */           str7 = CostBiz.getInstance().getTotalCost("" + paramInt1, paramString1);
/*      */         } else {
/* 1377 */           str7 = runFunction(Util.getIntValue(str6), paramInt1, paramInt2, paramList);
/*      */         } 
/*      */         
/* 1380 */         paramString2 = Util.replace(paramString2, "#[^#]*#", str7, 1);
/* 1381 */         str1 = Util.match(paramString2, "#[^#]*#");
/*      */       } 
/*      */       
/* 1384 */       String str2 = Util.match(paramString2, "@[^@]*@");
/* 1385 */       while (!str2.equals("")) {
/*      */         
/* 1387 */         String str6 = str2.substring(1, str2.lastIndexOf("@"));
/* 1388 */         String str7 = getScheduleMonth(str6, paramInt1, paramString1);
/* 1389 */         paramString2 = Util.replace(paramString2, "@[^@]*@", str7, 1);
/* 1390 */         str2 = Util.match(paramString2, "@[^@]*@");
/*      */       } 
/*      */       
/* 1393 */       String str3 = Util.match(paramString2, "\\$[^\\$]*\\$");
/* 1394 */       while (!str3.equals("")) {
/* 1395 */         String str6 = str3.substring(1, str3.lastIndexOf("$"));
/* 1396 */         String str7 = str6.substring(0, str6.indexOf("("));
/* 1397 */         String str8 = str6.substring(str6.indexOf("(") + 1, str6.indexOf(")"));
/* 1398 */         String str9 = getCompensationTarget(str7, paramInt1, paramString1, str8);
/* 1399 */         paramString2 = Util.replace(paramString2, "\\$[^\\$]*\\$", str9, 1);
/* 1400 */         str3 = Util.match(paramString2, "\\$[^\\$]*\\$");
/*      */       } 
/*      */       
/* 1403 */       if (Util.null2String(paramString1).trim().length() == 7) {
/* 1404 */         String str6 = TimeUtil.dateAdd(paramString1 + "-01", -1).substring(0, 7);
/* 1405 */         String str7 = Util.match(paramString2, "\\|[^\\|]*\\|");
/* 1406 */         while (!str7.equals("")) {
/* 1407 */           String str8 = Util.null2String(str7.substring(1, str7.lastIndexOf("|"))).trim();
/* 1408 */           String str9 = Util.null2String(str8.substring(0, str8.indexOf("["))).trim();
/* 1409 */           String str10 = Util.null2String(str8.substring(str8.indexOf("[") + 1, str8.indexOf("]"))).trim();
/* 1410 */           String str11 = getlastMonthSalary(str9, paramInt1, str6, str10);
/* 1411 */           paramString2 = Util.replace(paramString2, "\\|[^\\|]*\\|", str11, 1);
/* 1412 */           str7 = Util.match(paramString2, "\\|[^\\|]*\\|");
/*      */         } 
/*      */       } 
/*      */       
/* 1416 */       String str4 = Util.match(paramString2, "\\^");
/* 1417 */       if (!str4.equals("")) {
/* 1418 */         str4 = getPiece(paramInt1, paramString1);
/* 1419 */         paramString2 = Util.replace(paramString2, "\\^", str4, 0);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1424 */       String str5 = Util.match(paramString2, "\\~[^\\~]*\\~");
/* 1425 */       while (!str5.equals("")) {
/* 1426 */         String str6 = str5.substring(1, str5.lastIndexOf("~"));
/* 1427 */         String str7 = str6.substring(0, str6.indexOf("("));
/* 1428 */         String str8 = str6.substring(str6.indexOf("(") + 1, str6.indexOf(")"));
/* 1429 */         String str9 = getKqData(str7, paramInt1, paramString1, str8);
/* 1430 */         paramString2 = Util.replace(paramString2, "\\~[^\\~]*\\~", str9, 1);
/* 1431 */         str5 = Util.match(paramString2, "\\~[^\\~]*\\~");
/*      */       } 
/* 1433 */       return paramString2;
/* 1434 */     } catch (Exception exception) {
/*      */       
/* 1436 */       return "0";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCalTypeSalary(int paramInt1, int paramInt2, int paramInt3, List<String> paramList) {
/* 1449 */     String str = "0";
/*      */     try {
/* 1451 */       String str1 = "select salary from HrmSalarypaydetail where payid=" + paramInt3 + " and hrmid=" + paramInt1 + " and itemid='" + paramInt2 + "'";
/* 1452 */       this.statement.setStatementSql(str1);
/* 1453 */       this.statement.executeQuery();
/* 1454 */       if (this.statement.next()) {
/* 1455 */         str = this.statement.getString("salary");
/* 1456 */         return str;
/*      */       } 
/*      */       
/* 1459 */       if (!paramList.contains("" + paramInt2)) paramList.add("" + paramInt2); 
/* 1460 */       str1 = "select * from hrmsalarypay where id=" + paramInt3;
/* 1461 */       String str2 = "";
/* 1462 */       this.statement.setStatementSql(str1);
/* 1463 */       this.statement.executeQuery();
/* 1464 */       if (this.statement.next()) {
/* 1465 */         str2 = this.statement.getString("paydate");
/*      */       } else {
/* 1467 */         return "0";
/* 1468 */       }  ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1469 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1470 */       SalaryComInfo salaryComInfo = new SalaryComInfo();
/* 1471 */       String str3 = resourceComInfo.getDepartmentID("" + paramInt1);
/* 1472 */       String str4 = departmentComInfo.getSubcompanyid1(str3);
/* 1473 */       String str5 = str2.substring(5);
/* 1474 */       if (str5.equals("01") || str5.equals("02") || str5.equals("04") || str5.equals("05") || str5.equals("07") || str5.equals("08") || str5.equals("10") || str5.equals("11")) {
/* 1475 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str3 + ") or (a.scopetype=2 and b.objectid=" + str4 + ") or a.scopetype=0) and c.timescope=4 order by a.scopetype desc";
/*      */       
/*      */       }
/* 1478 */       else if (str5.equals("03") || str5.equals("09")) {
/* 1479 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str3 + ") or (a.scopetype=2 and b.objectid=" + str4 + ") or a.scopetype=0) and c.timescope>2 order by c.timescope,a.scopetype desc";
/*      */       
/*      */       }
/* 1482 */       else if (str5.equals("06")) {
/* 1483 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str3 + ") or (a.scopetype=2 and b.objectid=" + str4 + ") or a.scopetype=0) and c.timescope>1 order by c.timescope,a.scopetype desc";
/*      */       }
/*      */       else {
/*      */         
/* 1487 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str3 + ") or (a.scopetype=2 and b.objectid=" + str4 + ") or a.scopetype=0) and c.timescope>0 order by c.timescope,a.scopetype desc";
/*      */       } 
/*      */       
/* 1490 */       this.statement.setStatementSql(str1);
/* 1491 */       this.statement.executeQuery();
/* 1492 */       ArrayList<String> arrayList1 = new ArrayList();
/* 1493 */       ArrayList<String> arrayList2 = new ArrayList();
/* 1494 */       ArrayList<String> arrayList3 = new ArrayList();
/* 1495 */       ArrayList<String> arrayList4 = new ArrayList();
/*      */       
/* 1497 */       while (this.statement.next()) {
/* 1498 */         arrayList1.add(this.statement.getString("condition"));
/* 1499 */         arrayList2.add(this.statement.getString("conditiondsp"));
/* 1500 */         arrayList3.add(this.statement.getString("formular"));
/* 1501 */         arrayList4.add(this.statement.getString("formulardsp"));
/*      */       } 
/* 1503 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 1504 */         String str6 = parseCondition(paramInt1, str2, arrayList1.get(b));
/* 1505 */         if (assertCondition(str6)) {
/*      */           
/* 1507 */           String str7 = parseFormula(paramInt1, paramInt3, str2, arrayList3.get(b), paramList);
/*      */           
/* 1509 */           str = eval(str7);
/* 1510 */           if (Util.getDoubleValue(str, 0.0D) < 0.0D && "0".equals(salaryComInfo.getIsNegativeNum("" + paramInt2))) {
/* 1511 */             str = "0";
/*      */           }
/* 1513 */           Insertpaydetail("" + paramInt2, paramInt1, paramInt3, str, str6, str7, arrayList2.get(b), arrayList4.get(b));
/* 1514 */           return str;
/*      */         } 
/*      */       } 
/* 1517 */     } catch (Exception exception) {}
/*      */     
/* 1519 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWelfareTypeSalary(int paramInt1, int paramInt2, int paramInt3, int paramInt4, List<String> paramList) {
/* 1531 */     String str = "0";
/*      */     try {
/* 1533 */       String str1 = "select salary from HrmSalarypaydetail where payid=" + paramInt3 + " and hrmid=" + paramInt1 + " and itemid='" + paramInt2 + "_" + paramInt4 + "'";
/* 1534 */       this.statement.setStatementSql(str1);
/* 1535 */       this.statement.executeQuery();
/* 1536 */       if (this.statement.next()) {
/* 1537 */         str = this.statement.getString("salary");
/* 1538 */         return str;
/*      */       } 
/*      */ 
/*      */       
/* 1542 */       if (!paramList.contains("" + paramInt2 + "_" + paramInt4)) paramList.add("" + paramInt2 + "_" + paramInt4); 
/* 1543 */       str1 = "select * from hrmsalarypay where id=" + paramInt3;
/* 1544 */       String str2 = "";
/* 1545 */       this.statement.setStatementSql(str1);
/* 1546 */       this.statement.executeQuery();
/* 1547 */       if (this.statement.next()) {
/* 1548 */         str2 = this.statement.getString("paydate");
/*      */       } else {
/* 1550 */         return "0";
/*      */       } 
/* 1552 */       str1 = "select * from hrmsalaryitem where id=" + paramInt2;
/* 1553 */       this.statement.setStatementSql(str1);
/* 1554 */       this.statement.executeQuery();
/* 1555 */       if (!this.statement.next())
/* 1556 */         return "0"; 
/* 1557 */       String str3 = this.statement.getString("calMode");
/* 1558 */       String str4 = this.statement.getString("personalPercent");
/* 1559 */       String str5 = this.statement.getString("companyPercent");
/*      */       
/* 1561 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1562 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1563 */       SalaryComInfo salaryComInfo = new SalaryComInfo();
/*      */       
/* 1565 */       String str6 = resourceComInfo.getDepartmentID("" + paramInt1);
/* 1566 */       String str7 = departmentComInfo.getSubcompanyid1(str6);
/* 1567 */       String str8 = str2.substring(5);
/* 1568 */       if (str8.equals("01") || str8.equals("02") || str8.equals("04") || str8.equals("05") || str8.equals("07") || str8.equals("08") || str8.equals("10") || str8.equals("11")) {
/* 1569 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str6 + ") or (a.scopetype=2 and b.objectid=" + str7 + ") or a.scopetype=0) and c.timescope=4 order by a.scopetype desc";
/*      */       
/*      */       }
/* 1572 */       else if (str8.equals("03") || str8.equals("09")) {
/* 1573 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str6 + ") or (a.scopetype=2 and b.objectid=" + str7 + ") or a.scopetype=0) and c.timescope>2 order by c.timescope,a.scopetype desc";
/*      */       
/*      */       }
/* 1576 */       else if (str8.equals("06")) {
/* 1577 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str6 + ") or (a.scopetype=2 and b.objectid=" + str7 + ") or a.scopetype=0) and c.timescope>1 order by c.timescope,a.scopetype desc";
/*      */       }
/*      */       else {
/*      */         
/* 1581 */         str1 = "select  a.itemid,a.scopetype,b.objectid,c.timescope,c.condition,c.formular,c.conditiondsp,c.formulardsp from HrmSalaryCalBench a, HrmSalaryCalScope b, HrmSalaryCalRate c where a.id=b.benchid and a.id=c.benchid  and a.itemid=" + paramInt2 + " and ((a.scopetype=4 and b.objectid=" + paramInt1 + ") or (a.scopetype=3 and b.objectid=" + str6 + ") or (a.scopetype=2 and b.objectid=" + str7 + ") or a.scopetype=0) and c.timescope>0 order by c.timescope,a.scopetype desc";
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1586 */       this.statement.setStatementSql(str1);
/* 1587 */       this.statement.executeQuery();
/* 1588 */       ArrayList<String> arrayList1 = new ArrayList();
/* 1589 */       ArrayList<String> arrayList2 = new ArrayList();
/* 1590 */       ArrayList<String> arrayList3 = new ArrayList();
/* 1591 */       ArrayList<String> arrayList4 = new ArrayList();
/*      */       
/* 1593 */       while (this.statement.next()) {
/* 1594 */         arrayList1.add(this.statement.getString("condition"));
/* 1595 */         arrayList2.add(this.statement.getString("conditiondsp"));
/* 1596 */         arrayList3.add(this.statement.getString("formular"));
/* 1597 */         arrayList4.add(this.statement.getString("formulardsp"));
/*      */       } 
/* 1599 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 1600 */         String str9 = parseCondition(paramInt1, str2, arrayList1.get(b));
/* 1601 */         if (assertCondition(str9)) {
/* 1602 */           String str10 = parseFormula(paramInt1, paramInt3, str2, arrayList3.get(b), paramList);
/* 1603 */           if (str3.equals("1")) {
/* 1604 */             if (paramInt4 == 1) {
/* 1605 */               str10 = "(" + str10 + ")*" + str4 + "/100";
/* 1606 */               str = eval(str10);
/* 1607 */               if (Util.getDoubleValue(str, 0.0D) < 0.0D && "0".equals(salaryComInfo.getIsNegativeNum("" + paramInt2))) {
/* 1608 */                 str = "0";
/*      */               }
/* 1610 */               Insertpaydetail("" + paramInt2 + "_" + paramInt4, paramInt1, paramInt3, str, str9, str10, arrayList2.get(b), arrayList4.get(b));
/* 1611 */               return str;
/* 1612 */             }  if (paramInt4 == 2) {
/* 1613 */               str10 = "(" + str10 + ")*" + str5 + "/100";
/* 1614 */               str = eval(str10);
/* 1615 */               if (Util.getDoubleValue(str, 0.0D) < 0.0D && "0".equals(salaryComInfo.getIsNegativeNum("" + paramInt2))) {
/* 1616 */                 str = "0";
/*      */               }
/* 1618 */               Insertpaydetail("" + paramInt2 + "_" + paramInt4, paramInt1, paramInt3, str, str9, str10, arrayList2.get(b), arrayList4.get(b));
/* 1619 */               return str;
/*      */             } 
/* 1621 */             return "0";
/* 1622 */           }  if (str3.equals("2")) {
/* 1623 */             if (paramInt4 == 1) {
/* 1624 */               String[] arrayOfString1 = Util.TokenizerString2(str10, ";");
/* 1625 */               String[] arrayOfString2 = Util.TokenizerString2(arrayList4.get(b), ";");
/* 1626 */               if (arrayOfString1.length == 2) {
/* 1627 */                 str = eval(arrayOfString1[0]);
/* 1628 */                 if (Util.getDoubleValue(str, 0.0D) < 0.0D && "0".equals(salaryComInfo.getIsNegativeNum("" + paramInt2))) {
/* 1629 */                   str = "0";
/*      */                 }
/* 1631 */                 Insertpaydetail("" + paramInt2 + "_" + paramInt4, paramInt1, paramInt3, str, str9, arrayOfString1[0], arrayList2.get(b), arrayOfString2[0]);
/* 1632 */                 return str;
/*      */               } 
/* 1634 */               return "0";
/* 1635 */             }  if (paramInt4 == 2) {
/* 1636 */               String[] arrayOfString1 = Util.TokenizerString2(str10, ";");
/* 1637 */               String[] arrayOfString2 = Util.TokenizerString2(arrayList4.get(b), ";");
/* 1638 */               if (arrayOfString1.length == 2) {
/* 1639 */                 str = eval(arrayOfString1[1]);
/* 1640 */                 if (Util.getDoubleValue(str, 0.0D) < 0.0D && "0".equals(salaryComInfo.getIsNegativeNum("" + paramInt2))) {
/* 1641 */                   str = "0";
/*      */                 }
/* 1643 */                 Insertpaydetail("" + paramInt2 + "_" + paramInt4, paramInt1, paramInt3, str, str9, arrayOfString1[1], arrayList2.get(b), arrayOfString2[1]);
/* 1644 */                 return str;
/*      */               } 
/* 1646 */               return "0";
/*      */             } 
/* 1648 */             return "0";
/*      */           } 
/* 1650 */           return "0";
/*      */         } 
/*      */       } 
/* 1653 */     } catch (Exception exception) {}
/*      */     
/* 1655 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPayTypeSalary(int paramInt1, int paramInt2, int paramInt3) {
/* 1668 */     String str = "0";
/*      */     try {
/* 1670 */       this.resourcecominfo = new ResourceComInfo();
/* 1671 */       String str1 = this.resourcecominfo.getDepartmentID("" + paramInt1);
/* 1672 */       String str2 = "select salary from HrmSalarypaydetail where payid=" + paramInt3 + " and hrmid=" + paramInt1 + " and itemid='" + paramInt2 + "' and departmentid=" + str1;
/* 1673 */       this.statement.setStatementSql(str2);
/* 1674 */       this.statement.executeQuery();
/* 1675 */       if (this.statement.next()) {
/* 1676 */         str = this.statement.getString("salary");
/*      */       } else {
/* 1678 */         str2 = " select resourcepay from HrmSalaryResourcePay where (isbatch is null or isbatch=0) and itemid = " + paramInt2 + " and resourceid = " + paramInt1;
/* 1679 */         this.statement.setStatementSql(str2);
/* 1680 */         this.statement.executeQuery();
/* 1681 */         if (this.statement.next()) {
/* 1682 */           str = this.statement.getString("resourcepay");
/*      */         } else {
/* 1684 */           str = getRankSalary(paramInt1, paramInt2);
/*      */         } 
/*      */         
/* 1687 */         Insertpaydetail("" + paramInt2, paramInt1, paramInt3, str);
/*      */       } 
/* 1689 */     } catch (Exception exception) {}
/*      */     
/* 1691 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTaxTypeSalary(int paramInt1, int paramInt2, int paramInt3, List<String> paramList) {
/* 1703 */     String str = "0";
/*      */     try {
/* 1705 */       String str1 = "select salary from HrmSalarypaydetail where payid=" + paramInt3 + " and hrmid=" + paramInt1 + " and itemid='" + paramInt2 + "'";
/* 1706 */       this.statement.setStatementSql(str1);
/* 1707 */       this.statement.executeQuery();
/* 1708 */       if (this.statement.next()) {
/* 1709 */         str = this.statement.getString("salary");
/* 1710 */         return str;
/*      */       } 
/*      */ 
/*      */       
/* 1714 */       String str2 = "0";
/* 1715 */       str1 = "select taxrelateitem from HrmSalaryItem where id=" + paramInt2;
/* 1716 */       this.statement.setStatementSql(str1);
/* 1717 */       this.statement.executeQuery();
/* 1718 */       if (this.statement.next()) {
/* 1719 */         int i = this.statement.getInt("taxrelateitem");
/* 1720 */         paramList.add("" + paramInt2);
/* 1721 */         str2 = runFunction(i, paramInt1, paramInt3, paramList);
/* 1722 */         str = getTaxSalary(paramInt1, paramInt2, str2);
/*      */         
/* 1724 */         Insertpaydetail("" + paramInt2, paramInt1, paramInt3, str);
/* 1725 */         return str;
/*      */       } 
/* 1727 */     } catch (Exception exception) {}
/*      */     
/* 1729 */     return str;
/*      */   }
/*      */   
/*      */   public String getCumulativeTypeSalary(int paramInt1, int paramInt2, int paramInt3, List<String> paramList) {
/* 1733 */     String str = "0";
/*      */     try {
/* 1735 */       String str1 = "select salary from HrmSalarypaydetail where payid=" + paramInt3 + " and hrmid=" + paramInt1 + " and itemid='" + paramInt2 + "'";
/* 1736 */       this.statement.setStatementSql(str1);
/* 1737 */       this.statement.executeQuery();
/* 1738 */       if (this.statement.next()) {
/* 1739 */         str = this.statement.getString("salary");
/* 1740 */         return str;
/*      */       } 
/*      */       
/* 1743 */       str1 = "select * from hrmsalarypay where id=" + paramInt3;
/* 1744 */       String str2 = "";
/* 1745 */       this.statement.setStatementSql(str1);
/* 1746 */       this.statement.executeQuery();
/* 1747 */       if (this.statement.next()) {
/* 1748 */         str2 = this.statement.getString("paydate");
/*      */       } else {
/* 1750 */         return "0";
/*      */       } 
/*      */ 
/*      */       
/* 1754 */       String str3 = "0";
/* 1755 */       String str4 = "0";
/* 1756 */       String str5 = "0";
/* 1757 */       str1 = "select taxrelateitem,zxfjkcxm from HrmSalaryItem where id=" + paramInt2;
/* 1758 */       this.statement.setStatementSql(str1);
/* 1759 */       this.statement.executeQuery();
/* 1760 */       if (this.statement.next()) {
/* 1761 */         int i = this.statement.getInt("taxrelateitem");
/* 1762 */         int j = Util.getIntValue(this.statement.getString("zxfjkcxm"));
/* 1763 */         paramList.add("" + paramInt2);
/* 1764 */         str3 = runFunction(i, paramInt1, paramInt3, paramList);
/* 1765 */         str4 = runFunction(j, paramInt1, paramInt3, paramList);
/* 1766 */         str = getCumulativeTaxSalary(paramInt1, paramInt2, str3, str4, str2);
/*      */         
/* 1768 */         Insertpaydetail("" + paramInt2, paramInt1, paramInt3, str);
/* 1769 */         return str;
/*      */       } 
/* 1771 */     } catch (Exception exception) {}
/*      */     
/* 1773 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getScheduleAddTypeSalary(int paramInt1, int paramInt2, int paramInt3) {
/* 1785 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getScheduleDecTypeSalary(int paramInt1, int paramInt2, int paramInt3) {
/* 1798 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubsidyTypeSalary(int paramInt1, int paramInt2, int paramInt3) {
/* 1811 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean Insertpaydetail(String paramString1, int paramInt1, int paramInt2, String paramString2) {
/*      */     try {
/* 1826 */       this.resourcecominfo = new ResourceComInfo();
/* 1827 */       if (paramString2.equals("")) {
/* 1828 */         paramString2 = "0";
/*      */       }
/* 1830 */       String str = "insert into HrmSalarypaydetail(payid,itemid,hrmid,salary,departmentid,status,sent) values(" + paramInt2 + ",'" + paramString1 + "'," + paramInt1 + "," + paramString2 + "," + this.resourcecominfo.getDepartmentID("" + paramInt1) + ",0,0)";
/* 1831 */       this.statement.setStatementSql(str);
/*      */       
/* 1833 */       return (this.statement.executeUpdate() > 0);
/* 1834 */     } catch (Exception exception) {
/* 1835 */       writeLog(exception);
/*      */       
/* 1837 */       return false;
/*      */     } 
/*      */   }
/*      */   public boolean Insertpaydetail(String paramString1, int paramInt1, int paramInt2, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/*      */     try {
/* 1842 */       this.resourcecominfo = new ResourceComInfo();
/* 1843 */       String str1 = "condition";
/* 1844 */       if (DialectUtil.isMySql()) {
/* 1845 */         str1 = "`condition`";
/*      */       }
/* 1847 */       String str2 = "insert into HrmSalarypaydetail(payid,itemid,hrmid,salary,departmentid,status,sent," + str1 + ",formular,conditiondsp,formulardsp) values(" + paramInt2 + ",'" + paramString1 + "'," + paramInt1 + "," + paramString2 + "," + this.resourcecominfo.getDepartmentID("" + paramInt1) + ",0,0,'" + paramString3 + "','" + paramString4 + "','" + paramString5 + "','" + paramString6 + "')";
/*      */       
/* 1849 */       this.statement.setStatementSql(str2);
/* 1850 */       return (this.statement.executeUpdate() > 0);
/* 1851 */     } catch (Exception exception) {
/* 1852 */       writeLog(exception);
/*      */       
/* 1854 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getRankSalary(int paramInt1, int paramInt2) {
/* 1865 */     String str = "0";
/*      */     try {
/* 1867 */       this.resourcecominfo = new ResourceComInfo();
/* 1868 */       JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 1869 */       String str1 = "select amount from HrmSalaryRank where itemid=" + paramInt2 + " and (1=1 ";
/*      */       
/* 1871 */       str1 = str1 + " and (jobid=" + this.resourcecominfo.getJobTitle("" + paramInt1) + " or jobid=0)";
/* 1872 */       if (!this.resourcecominfo.getJobTitle("" + paramInt1).equals("") && !jobTitlesComInfo.getJobactivityid(this.resourcecominfo.getJobTitle("" + paramInt1)).equals("")) {
/* 1873 */         str1 = str1 + " and (jobactivityid=" + jobTitlesComInfo.getJobactivityid(this.resourcecominfo.getJobTitle("" + paramInt1)) + " or jobactivityid=0)";
/*      */       }
/* 1875 */       String str2 = "" + Util.getIntValue(EncryptConfigBiz.getDecryptData(this.resourcecominfo.getJoblevel("" + paramInt1)), 0);
/*      */       
/* 1877 */       str1 = str1 + " and (joblevelfrom<=" + str2;
/* 1878 */       str1 = str1 + " and joblevelto>=" + str2;
/* 1879 */       str1 = str1 + " )";
/*      */       
/* 1881 */       str1 = str1 + " ) order by jobid desc,jobactivityid desc,id desc";
/*      */       
/* 1883 */       this.statement.setStatementSql(str1);
/* 1884 */       this.statement.executeQuery();
/* 1885 */       if (this.statement.next()) {
/* 1886 */         str = this.statement.getString("amount");
/*      */       }
/* 1888 */     } catch (Exception exception) {}
/*      */ 
/*      */     
/* 1891 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTaxSalary(int paramInt1, int paramInt2, String paramString) {
/* 1903 */     ConnStatement connStatement = new ConnStatement();
/* 1904 */     String str = "0";
/*      */     try {
/* 1906 */       this.resourcecominfo = new ResourceComInfo();
/* 1907 */       LocationComInfo locationComInfo = new LocationComInfo();
/* 1908 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1914 */       String str1 = "select b.taxrate,a.taxbenchmark,b.subtractnum from HrmSalaryTaxbench a,HrmSalaryTaxrate b,HrmSalaryTaxscope c where a.id=b.benchid and a.id=c.benchid and a.itemid=" + paramInt2 + " and a.taxbenchmark<=" + paramString + " and ((b.ranklow<" + paramString + "-a.taxbenchmark and b.rankhigh>=" + paramString + "-a.taxbenchmark) or (b.ranklow<" + paramString + "-a.taxbenchmark and b.rankhigh=0)) and (c.scopetype=0 or (c.scopetype=1 and c.objectid='" + locationComInfo.getLocationcity(this.resourcecominfo.getLocationid("" + paramInt1)) + "') or (c.scopetype=2 and c.objectid='" + departmentComInfo.getSubcompanyid1(this.resourcecominfo.getDepartmentID("" + paramInt1)) + "') or (c.scopetype=3 and c.objectid='" + this.resourcecominfo.getDepartmentID("" + paramInt1) + "') or (c.scopetype=4 and c.objectid='" + paramInt1 + "')) order by c.scopetype desc";
/*      */ 
/*      */       
/* 1917 */       connStatement.setStatementSql(str1);
/* 1918 */       connStatement.executeQuery();
/* 1919 */       if (connStatement.next()) {
/* 1920 */         str = (new BigDecimal(connStatement.getString("taxrate"))).multiply((new BigDecimal(paramString)).subtract(new BigDecimal(connStatement.getString("taxbenchmark")))).divide(new BigDecimal(100), 2, 4).subtract(new BigDecimal(Util.getIntValue(connStatement.getString("subtractnum")))).toString();
/*      */       }
/* 1922 */     } catch (Exception exception) {
/*      */     
/*      */     } finally {
/* 1925 */       connStatement.close();
/*      */     } 
/* 1927 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCumulativeTaxSalary(int paramInt1, int paramInt2, String paramString1, String paramString2, String paramString3) {
/* 1940 */     ConnStatement connStatement = new ConnStatement();
/* 1941 */     String str = "0";
/*      */     try {
/* 1943 */       this.resourcecominfo = new ResourceComInfo();
/* 1944 */       LocationComInfo locationComInfo = new LocationComInfo();
/* 1945 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*      */       
/* 1947 */       int i = 0;
/* 1948 */       String str1 = this.resourcecominfo.getCompanyStartDate("" + paramInt1);
/* 1949 */       if (!str1.equals("")) {
/* 1950 */         int j = Util.getIntValue(str1.substring(0, 4));
/* 1951 */         int k = Util.getIntValue(str1.substring(5, 7));
/* 1952 */         int m = Util.getIntValue(paramString3.substring(0, 4));
/* 1953 */         int n = Util.getIntValue(paramString3.substring(5));
/* 1954 */         if (j < m) {
/* 1955 */           i = n;
/* 1956 */         } else if (j == m) {
/* 1957 */           if (n < k) {
/* 1958 */             return "0";
/*      */           }
/* 1960 */           i = n - k + 1;
/*      */         } else {
/*      */           
/* 1963 */           return "0";
/*      */         } 
/*      */       } else {
/* 1966 */         return "0";
/*      */       } 
/*      */       
/* 1969 */       String str2 = "";
/* 1970 */       String str3 = "select * from HrmSalaryTaxbench where itemid=" + paramInt2;
/* 1971 */       connStatement.setStatementSql(str3);
/* 1972 */       connStatement.executeQuery();
/* 1973 */       if (connStatement.next()) {
/* 1974 */         str2 = connStatement.getString("taxbenchmark");
/*      */       }
/*      */       
/* 1977 */       paramString1 = (new BigDecimal(paramString1)).subtract(new BigDecimal(paramString2)).subtract((new BigDecimal(str2)).multiply(new BigDecimal(i))).toString();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1984 */       str3 = "select b.taxrate,a.taxbenchmark,b.subtractnum from HrmSalaryTaxbench a,HrmSalaryTaxrate b,HrmSalaryTaxscope c where a.id=b.benchid and a.id=c.benchid and a.itemid=" + paramInt2 + " and ((b.ranklow<" + paramString1 + " and b.rankhigh>=" + paramString1 + ")  or (b.ranklow<" + paramString1 + " and b.rankhigh=0)) and (c.scopetype=0 or (c.scopetype=1 and c.objectid='" + locationComInfo.getLocationcity(this.resourcecominfo.getLocationid("" + paramInt1)) + "') or (c.scopetype=2 and c.objectid='" + departmentComInfo.getSubcompanyid1(this.resourcecominfo.getDepartmentID("" + paramInt1)) + "') or (c.scopetype=3 and c.objectid='" + this.resourcecominfo.getDepartmentID("" + paramInt1) + "') or (c.scopetype=4 and c.objectid='" + paramInt1 + "')) order by c.scopetype desc";
/*      */ 
/*      */       
/* 1987 */       connStatement.setStatementSql(str3);
/* 1988 */       connStatement.executeQuery();
/* 1989 */       if (connStatement.next()) {
/* 1990 */         str = (new BigDecimal(connStatement.getString("taxrate"))).multiply(new BigDecimal(paramString1)).divide(new BigDecimal(100), 2, 4).subtract(new BigDecimal(Util.getIntValue(connStatement.getString("subtractnum")))).toString();
/*      */       }
/* 1992 */     } catch (Exception exception) {
/*      */     
/*      */     } finally {
/* 1995 */       connStatement.close();
/*      */     } 
/* 1997 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String runFunction(int paramInt1, int paramInt2, int paramInt3, List paramList) {
/* 2010 */     String str = "0";
/*      */     try {
/* 2012 */       SalaryComInfo salaryComInfo = new SalaryComInfo();
/* 2013 */       String str1 = salaryComInfo.getSalaryItemtype("" + paramInt1);
/* 2014 */       if (str1.equals("1")) {
/* 2015 */         str = getPayTypeSalary(paramInt2, paramInt1, paramInt3);
/* 2016 */       } else if (str1.equals("3")) {
/* 2017 */         str = getTaxTypeSalary(paramInt2, paramInt1, paramInt3, paramList);
/* 2018 */       } else if (str1.equals("4")) {
/* 2019 */         str = getCalTypeSalary(paramInt2, paramInt1, paramInt3, paramList);
/* 2020 */       } else if (str1.equals("5")) {
/* 2021 */         str = getScheduleAddTypeSalary(paramInt2, paramInt1, paramInt3);
/* 2022 */       } else if (str1.equals("6")) {
/* 2023 */         str = getScheduleDecTypeSalary(paramInt2, paramInt1, paramInt3);
/* 2024 */       } else if (str1.equals("7")) {
/* 2025 */         str = getSubsidyTypeSalary(paramInt2, paramInt1, paramInt3);
/* 2026 */       } else if (str1.equals("9")) {
/* 2027 */         ArrayList arrayList = (ArrayList)((ArrayList)paramList).clone();
/* 2028 */         str = getWelfareTypeSalary(paramInt2, paramInt1, paramInt3, 1, paramList);
/* 2029 */         str = getWelfareTypeSalary(paramInt2, paramInt1, paramInt3, 2, arrayList);
/* 2030 */       } else if (str1.equals("11")) {
/* 2031 */         str = getCumulativeTypeSalary(paramInt2, paramInt1, paramInt3, paramList);
/*      */       } 
/* 2033 */     } catch (Exception exception) {
/* 2034 */       exception.printStackTrace();
/*      */     } 
/* 2036 */     return str;
/*      */   }
/*      */   
/*      */   public String runFunction(int paramInt1, int paramInt2, int paramInt3) {
/* 2040 */     return runFunction(paramInt1, paramInt2, paramInt3, new ArrayList());
/*      */   }
/*      */ 
/*      */   
/*      */   public void clearExistItem() {}
/*      */ 
/*      */   
/*      */   public void colseconnect() {
/* 2048 */     if (this.statement != null) {
/* 2049 */       this.statement.close();
/*      */     }
/* 2051 */     this.rs = null;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/finance/SalaryManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */