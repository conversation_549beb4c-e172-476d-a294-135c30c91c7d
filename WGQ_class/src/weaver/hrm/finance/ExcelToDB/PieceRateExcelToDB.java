/*     */ package weaver.hrm.finance.ExcelToDB;
/*     */ 
/*     */ import java.io.FileInputStream;
/*     */ import java.math.BigDecimal;
/*     */ import java.text.DateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Vector;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDateUtil;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*     */ import org.apache.poi.ss.usermodel.Cell;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class PieceRateExcelToDB
/*     */   extends BaseBean
/*     */ {
/*  23 */   private Vector msg1 = new Vector();
/*  24 */   private Vector msg2 = new Vector();
/*  25 */   private ArrayList crmidlist = new ArrayList();
/*  26 */   private ArrayList crmnamelist = new ArrayList();
/*  27 */   private String msg3 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Vector getMsg1() {
/*  33 */     return this.msg1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Vector getMsg2() {
/*  41 */     return this.msg2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMsg3() {
/*  49 */     return this.msg3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getCellValue(HSSFCell paramHSSFCell, int paramInt, String paramString1, String paramString2, boolean paramBoolean) {
/*  62 */     String str = "";
/*     */     try {
/*  64 */       switch (paramHSSFCell.getCellType()) {
/*     */         
/*     */         case NUMERIC:
/*  67 */           if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/*  68 */             str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString(); break;
/*     */           } 
/*  70 */           if (paramBoolean) {
/*  71 */             double d = paramHSSFCell.getNumericCellValue();
/*  72 */             int i = (new BigDecimal(d)).intValue();
/*  73 */             if (d != i) {
/*  74 */               str = String.valueOf(d); break;
/*     */             } 
/*  76 */             str = String.valueOf(i);
/*     */             break;
/*     */           } 
/*  79 */           str = String.valueOf(paramHSSFCell.getNumericCellValue());
/*     */           break;
/*     */ 
/*     */         
/*     */         case STRING:
/*  84 */           str = paramHSSFCell.getStringCellValue();
/*     */           break;
/*     */         case FORMULA:
/*  87 */           str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString();
/*     */           break;
/*     */       } 
/*     */ 
/*     */     
/*  92 */     } catch (Exception exception) {
/*  93 */       writeLog(exception);
/*     */     } 
/*  95 */     if (paramInt == 1 && str.equals("")) {
/*  96 */       this.msg1.add(paramString1);
/*  97 */       this.msg2.add(paramString2);
/*     */     } 
/*  99 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void ExcelToDB1(String paramString, int paramInt1, int paramInt2) {
/* 109 */     ConnStatement connStatement = new ConnStatement();
/*     */     
/*     */     try {
/* 112 */       connStatement.setStatementSql("delete from HRM_PieceRateSetting where subcompanyid=" + paramInt1);
/* 113 */       connStatement.executeUpdate();
/* 114 */       FileInputStream fileInputStream = new FileInputStream(paramString);
/* 115 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/* 116 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/* 117 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/* 119 */       fileInputStream.close();
/* 120 */       HSSFRow hSSFRow = null;
/* 121 */       String str1 = "";
/* 122 */       String str2 = "";
/* 123 */       String str3 = "";
/* 124 */       String str4 = "";
/* 125 */       String str5 = "";
/*     */       
/* 127 */       int i = hSSFSheet.getLastRowNum();
/*     */       
/* 129 */       for (byte b = 1; b < i + 1; b++) {
/* 130 */         hSSFRow = hSSFSheet.getRow(b);
/* 131 */         HSSFCell hSSFCell1 = null;
/* 132 */         HSSFCell hSSFCell2 = null;
/* 133 */         HSSFCell hSSFCell3 = null;
/* 134 */         HSSFCell hSSFCell4 = null;
/* 135 */         HSSFCell hSSFCell5 = null;
/*     */ 
/*     */         
/* 138 */         hSSFCell1 = hSSFRow.getCell(0);
/* 139 */         str1 = getCellValue(hSSFCell1, 1, (b + 1) + "", "1", true);
/*     */         
/* 141 */         if (!str1.equals("")) {
/*     */ 
/*     */           
/* 144 */           hSSFCell2 = hSSFRow.getCell(1);
/* 145 */           str2 = getCellValue(hSSFCell2, 0, (b + 1) + "", "2", true);
/*     */ 
/*     */ 
/*     */           
/* 149 */           hSSFCell3 = hSSFRow.getCell(2);
/* 150 */           str3 = getCellValue(hSSFCell3, 0, (b + 1) + "", "3", true);
/*     */ 
/*     */           
/* 153 */           hSSFCell4 = hSSFRow.getCell(3);
/* 154 */           str4 = getCellValue(hSSFCell4, 0, (b + 1) + "", "4", false);
/*     */           
/*     */           try {
/* 157 */             str4 = (new BigDecimal(str4)).setScale(2, 4).toString() + "";
/* 158 */           } catch (Exception exception) {
/* 159 */             str4 = "0";
/*     */           } 
/*     */ 
/*     */ 
/*     */           
/* 164 */           hSSFCell5 = hSSFRow.getCell(4);
/* 165 */           str5 = getCellValue(hSSFCell5, 0, (b + 1) + "", "5", true);
/*     */ 
/*     */ 
/*     */           
/* 169 */           String str6 = "INSERT INTO HRM_PieceRateSetting(subcompanyid,PieceRateNo,PieceRateName,workingpro,price,memo) values(?,?,?,?,?,?)";
/* 170 */           connStatement.setStatementSql(str6);
/* 171 */           connStatement.setInt(1, paramInt1);
/* 172 */           connStatement.setString(2, Util.fromScreen3(str1, paramInt2));
/* 173 */           connStatement.setString(3, Util.fromScreen3(str2, paramInt2));
/* 174 */           connStatement.setString(4, Util.fromScreen3(str3, paramInt2));
/* 175 */           connStatement.setFloat(5, Util.getFloatValue(str4, 0.0F));
/* 176 */           connStatement.setString(6, Util.fromScreen3(str5, paramInt2));
/* 177 */           connStatement.executeUpdate();
/* 178 */           String str7 = "";
/*     */           
/* 180 */           str6 = "select PieceRateNo from (select count(PieceRateNo) nums,PieceRateNo from HRM_PieceRateSetting where subcompanyid=" + paramInt1 + " group by PieceRateNo) a where a.nums>1";
/* 181 */           connStatement.setStatementSql(str6);
/* 182 */           connStatement.executeQuery();
/* 183 */           while (connStatement.next()) {
/* 184 */             str7 = str7 + "'" + connStatement.getString("PieceRateNo") + "',";
/* 185 */             this.msg3 += connStatement.getString("PieceRateNo") + ",";
/*     */           } 
/* 187 */           if (!this.msg3.trim().equals(""))
/* 188 */           { str7 = str7.substring(0, str7.length() - 1);
/* 189 */             this.msg3 = this.msg3.substring(0, this.msg3.length() - 1);
/*     */             
/* 191 */             str6 = "delete from HRM_PieceRateSetting where subcompanyid=" + paramInt1 + " and piecerateno in(" + str7 + ") and id<(select max(id) from HRM_PieceRateSetting where subcompanyid=" + paramInt1 + " and piecerateno in(" + str7 + "))";
/* 192 */             connStatement.setStatementSql(str6);
/* 193 */             connStatement.executeUpdate(); } 
/*     */         } 
/*     */       } 
/* 196 */     } catch (Exception exception) {
/* 197 */       writeLog(exception);
/* 198 */       exception.printStackTrace();
/*     */     } finally {
/* 200 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void ExcelToDB2(String paramString, int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 213 */     ConnStatement connStatement = new ConnStatement();
/*     */     
/*     */     try {
/* 216 */       connStatement.setStatementSql("delete from HRM_PieceRateInfo where subcompanyid=" + paramInt1 + " and PieceYear=" + paramInt2 + " and PieceMonth=" + paramInt3);
/* 217 */       connStatement.executeUpdate();
/*     */       
/* 219 */       connStatement.setStatementSql("select workcode,departmentid from HrmResource where subcompanyid1=" + paramInt1 + " group by workcode,departmentid");
/* 220 */       connStatement.executeQuery();
/* 221 */       ArrayList<String> arrayList1 = new ArrayList();
/* 222 */       ArrayList<String> arrayList2 = new ArrayList();
/* 223 */       while (connStatement.next()) {
/* 224 */         arrayList1.add(connStatement.getString("workcode"));
/* 225 */         arrayList2.add(connStatement.getString("departmentid"));
/*     */       } 
/*     */       
/* 228 */       connStatement.setStatementSql("select PieceRateNo from HRM_PieceRateSetting where subcompanyid=" + paramInt1);
/* 229 */       connStatement.executeQuery();
/* 230 */       ArrayList<String> arrayList3 = new ArrayList();
/* 231 */       while (connStatement.next()) {
/* 232 */         arrayList3.add(connStatement.getString("PieceRateNo"));
/*     */       }
/* 234 */       FileInputStream fileInputStream = new FileInputStream(paramString);
/* 235 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/* 236 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/* 237 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/* 239 */       fileInputStream.close();
/* 240 */       HSSFRow hSSFRow = null;
/* 241 */       String str1 = "";
/* 242 */       String str2 = "";
/* 243 */       String str3 = "";
/* 244 */       String str4 = "";
/* 245 */       String str5 = "";
/*     */       
/* 247 */       int i = hSSFSheet.getLastRowNum();
/*     */       
/* 249 */       for (byte b = 1; b < i + 1; b++) {
/* 250 */         hSSFRow = hSSFSheet.getRow(b);
/* 251 */         HSSFCell hSSFCell1 = null;
/* 252 */         HSSFCell hSSFCell2 = null;
/* 253 */         HSSFCell hSSFCell3 = null;
/* 254 */         HSSFCell hSSFCell4 = null;
/* 255 */         HSSFCell hSSFCell5 = null;
/* 256 */         int j = -1;
/*     */ 
/*     */         
/* 259 */         hSSFCell1 = hSSFRow.getCell(0);
/* 260 */         str2 = getCellValue(hSSFCell1, 1, (b + 1) + "", "1", true);
/*     */         
/* 262 */         if (!str2.equals(""))
/* 263 */         { int k = arrayList1.indexOf(Util.fromScreen3(str2, paramInt4).trim());
/* 264 */           if (k < 0)
/* 265 */           { this.msg1.add((b + 1) + "");
/* 266 */             this.msg2.add("1"); }
/*     */           else
/*     */           
/* 269 */           { j = Util.getIntValue(arrayList2.get(k));
/*     */ 
/*     */ 
/*     */             
/* 273 */             hSSFCell2 = hSSFRow.getCell(1);
/* 274 */             str1 = getCellValue(hSSFCell2, 1, (b + 1) + "", "2", true);
/*     */             
/* 276 */             if (!str1.equals(""))
/* 277 */             { k = arrayList3.indexOf(Util.fromScreen3(str1, paramInt4).trim());
/* 278 */               if (k < 0)
/* 279 */               { this.msg1.add((b + 1) + "");
/* 280 */                 this.msg2.add("2");
/*     */                  }
/*     */               
/*     */               else
/*     */               
/* 285 */               { hSSFCell3 = hSSFRow.getCell(2);
/* 286 */                 str4 = getCellValue(hSSFCell3, 0, (b + 1) + "", "3", false);
/*     */                 try {
/* 288 */                   str4 = (new BigDecimal(str4)).setScale(2, 4).toString() + "";
/* 289 */                 } catch (Exception exception) {
/* 290 */                   str4 = "0";
/*     */                 } 
/*     */ 
/*     */                 
/* 294 */                 hSSFCell4 = hSSFRow.getCell(3);
/* 295 */                 str3 = getCellValue(hSSFCell4, 0, (b + 1) + "", "4", true);
/*     */                 
/* 297 */                 if (str3.length() > 10)
/* 298 */                 { this.msg1.add((b + 1) + "");
/* 299 */                   this.msg2.add("4");
/*     */                    }
/*     */                 
/*     */                 else
/*     */                 
/* 304 */                 { hSSFCell5 = hSSFRow.getCell(4);
/* 305 */                   str5 = getCellValue(hSSFCell5, 0, (b + 1) + "", "5", true);
/*     */ 
/*     */ 
/*     */                   
/* 309 */                   String str = "INSERT INTO HRM_PieceRateInfo(subcompanyid,departmentid,PieceYear,PieceMonth,UserCode,PieceRateNo,PieceRateDate,PieceNum,memo) values(?,?,?,?,?,?,?,?,?)";
/* 310 */                   connStatement.setStatementSql(str);
/* 311 */                   connStatement.setInt(1, paramInt1);
/* 312 */                   connStatement.setInt(2, j);
/* 313 */                   connStatement.setInt(3, paramInt2);
/* 314 */                   connStatement.setInt(4, paramInt3);
/* 315 */                   connStatement.setString(5, Util.fromScreen3(str2, paramInt4));
/* 316 */                   connStatement.setString(6, Util.fromScreen3(str1, paramInt4));
/* 317 */                   connStatement.setString(7, Util.fromScreen3(str3, paramInt4));
/* 318 */                   connStatement.setFloat(8, Util.getFloatValue(str4, 0.0F));
/* 319 */                   connStatement.setString(9, Util.fromScreen3(str5, paramInt4));
/* 320 */                   connStatement.executeUpdate(); }  }  }  }  } 
/*     */       } 
/* 322 */     } catch (Exception exception) {
/* 323 */       writeLog(exception);
/* 324 */       exception.printStackTrace();
/*     */     } finally {
/* 326 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/finance/ExcelToDB/PieceRateExcelToDB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */