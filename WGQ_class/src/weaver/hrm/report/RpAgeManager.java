/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpAgeManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private SysMaintenanceLog log;
/*     */   private String resourcetype1;
/*     */   private String resourcetype2;
/*     */   private String resourcetype3;
/*     */   private String resourcetype4;
/*     */   private int departmentid;
/*     */   
/*     */   public RpAgeManager() {
/*  32 */     resetParameter();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetParameter() {
/*  39 */     this.resourcetype1 = "";
/*  40 */     this.resourcetype2 = "";
/*  41 */     this.resourcetype3 = "";
/*  42 */     this.resourcetype4 = "";
/*  43 */     this.departmentid = -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype1(String paramString) {
/*  51 */     this.resourcetype1 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype2(String paramString) {
/*  59 */     this.resourcetype2 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype3(String paramString) {
/*  67 */     this.resourcetype3 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype4(String paramString) {
/*  75 */     this.resourcetype4 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDepartmentid(int paramInt) {
/*  83 */     this.departmentid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getYearorder() throws Exception {
/*  92 */     return this.statement.getInt("yearorder");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAgenum() throws Exception {
/* 101 */     return this.statement.getInt("num");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectRpAge() throws Exception {
/* 109 */     this.statement = new RecordSet();
/*     */     try {
/* 111 */       Calendar calendar = Calendar.getInstance();
/* 112 */       int i = calendar.getTime().getYear() + 1900;
/* 113 */       String str1 = "";
/* 114 */       boolean bool = false;
/* 115 */       if (this.departmentid == 0) { str1 = ""; }
/* 116 */       else { str1 = "where departmentid=" + this.departmentid; }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 125 */       if (!this.resourcetype1.equals("")) {
/* 126 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype1; }
/* 127 */         else { str1 = str1 + " and resourcetype in('" + this.resourcetype1; }
/* 128 */          bool = true;
/*     */       } 
/* 130 */       if (!this.resourcetype2.equals("")) {
/* 131 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype2; }
/*     */         
/* 133 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype2; }
/* 134 */         else { str1 = str1 + "','" + this.resourcetype2; }
/*     */         
/* 136 */         bool = true;
/*     */       } 
/* 138 */       if (!this.resourcetype3.equals("")) {
/* 139 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype3; }
/*     */         
/* 141 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype3; }
/* 142 */         else { str1 = str1 + "','" + this.resourcetype3; }
/*     */         
/* 144 */         bool = true;
/*     */       } 
/* 146 */       if (!this.resourcetype4.equals("")) {
/* 147 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype4; }
/*     */         
/* 149 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype4; }
/* 150 */         else { str1 = str1 + "','" + this.resourcetype4; }
/*     */         
/* 152 */         bool = true;
/*     */       } 
/* 154 */       if (bool == true) str1 = str1 + "' )"; 
/* 155 */       String str2 = "select count(id) num,(" + i + "-left(birthday,4))/5 yearorder from hrmresource ";
/* 156 */       str2 = str2 + str1;
/* 157 */       str2 = str2 + " group by (" + i + "-left(birthday,4))/5 order by yearorder";
/* 158 */       this.statement.executeSql(str2);
/*     */     }
/* 160 */     catch (Exception exception) {
/* 161 */       writeLog(exception);
/* 162 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/* 172 */     return this.statement.next();
/*     */   }
/*     */   
/*     */   public void closeStatement() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpAgeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */