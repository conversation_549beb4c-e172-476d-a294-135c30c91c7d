/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.LogMan;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.job.EducationLevelComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.job.UseKindComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UseDemandManager
/*     */ {
/*  30 */   RecordSet rs = new RecordSet();
/*  31 */   Hashtable show = new Hashtable<Object, Object>();
/*  32 */   LogMan lm = LogMan.getInstance();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getResultByContent(int paramInt, String paramString) {
/*  41 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*  42 */     if (paramInt == 1) {
/*  43 */       hashtable = getResultByDep(paramString);
/*     */     }
/*  45 */     if (paramInt == 2) {
/*  46 */       hashtable = getResultByJobTitle(paramString);
/*     */     }
/*  48 */     if (paramInt == 3) {
/*  49 */       hashtable = getResultByUseKind(paramString);
/*     */     }
/*  51 */     if (paramInt == 4) {
/*  52 */       hashtable = getResultByEduLevel(paramString);
/*     */     }
/*  54 */     return hashtable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getMonResultByContent(int paramInt, String paramString1, String paramString2) {
/*  65 */     ArrayList arrayList = new ArrayList();
/*  66 */     if (paramInt == 1) {
/*  67 */       arrayList = getMonResultByDep(paramString1, paramString2);
/*     */     }
/*  69 */     if (paramInt == 2) {
/*  70 */       arrayList = getMonResultByJobTitle(paramString1, paramString2);
/*     */     }
/*  72 */     if (paramInt == 3) {
/*  73 */       arrayList = getMonResultByUseKind(paramString1, paramString2);
/*     */     }
/*  75 */     if (paramInt == 4) {
/*  76 */       arrayList = getMonResultByEduLevel(paramString1, paramString2);
/*     */     }
/*  78 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private Hashtable getResultByDep(String paramString) {
/*  83 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*     */     try {
/*  85 */       RecordSet recordSet = new RecordSet();
/*  86 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  87 */       String str = "select distinct(demanddep) from HrmUseDemand,HrmDepartment where demanddep=HrmDepartment.id " + paramString + " order by demanddep ";
/*  88 */       this.rs.executeSql(str);
/*  89 */       while (this.rs.next()) {
/*  90 */         int i = Util.getIntValue(this.rs.getString("demanddep"));
/*  91 */         String str1 = departmentComInfo.getDepartmentname("" + i);
/*  92 */         this.show.put("" + i, str1);
/*  93 */         int j = 0;
/*  94 */         String str2 = "select demandnum from HrmUseDemand where demanddep = " + i + paramString;
/*  95 */         recordSet.executeSql(str2);
/*  96 */         while (recordSet.next()) {
/*  97 */           int k = Util.getIntValue(recordSet.getString("demandnum"), 0);
/*  98 */           j += k;
/*     */         } 
/* 100 */         hashtable.put("" + i, "" + j);
/*     */       } 
/* 102 */     } catch (Exception exception) {
/* 103 */       this.lm.writeLog(exception);
/*     */     } 
/* 105 */     return hashtable;
/*     */   }
/*     */   
/*     */   private ArrayList getMonResultByDep(String paramString1, String paramString2) {
/* 109 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/* 111 */       for (byte b = 1; b < 13; b++) {
/* 112 */         int i = 0;
/* 113 */         String str1 = "" + paramString1 + "" + Util.add0(b, 2) + "-01";
/* 114 */         String str2 = "" + paramString1 + "" + Util.add0(b, 2) + "-31";
/* 115 */         String str3 = " and demandregdate>='" + str1 + "' and demandregdate <='" + str2 + "' ";
/* 116 */         String str4 = "select demandnum from HrmUseDemand where demanddep = " + paramString2 + str3;
/* 117 */         this.rs.executeSql(str4);
/* 118 */         while (this.rs.next()) {
/* 119 */           int j = Util.getIntValue(this.rs.getString("demandnum"), 0);
/* 120 */           i += j;
/*     */         } 
/* 122 */         arrayList.add("" + i);
/*     */       } 
/* 124 */     } catch (Exception exception) {
/* 125 */       this.lm.writeLog(exception);
/*     */     } 
/* 127 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private Hashtable getResultByJobTitle(String paramString) {
/* 132 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*     */     try {
/* 134 */       RecordSet recordSet = new RecordSet();
/* 135 */       JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 136 */       String str = "select distinct(demandjobtitle) from HrmUseDemand,HrmJobTitles where demandjobtitle=HrmJobTitles.id " + paramString + " order by demandjobtitle ";
/* 137 */       this.rs.executeSql(str);
/* 138 */       while (this.rs.next()) {
/* 139 */         int i = Util.getIntValue(this.rs.getString("demandjobtitle"));
/* 140 */         String str1 = jobTitlesComInfo.getJobTitlesname("" + i);
/* 141 */         this.show.put("" + i, str1);
/* 142 */         int j = 0;
/* 143 */         String str2 = "select demandnum from HrmUseDemand where demandjobtitle = " + i + paramString;
/* 144 */         recordSet.executeSql(str2);
/* 145 */         while (recordSet.next()) {
/* 146 */           int k = Util.getIntValue(recordSet.getString("demandnum"), 0);
/* 147 */           j += k;
/*     */         } 
/* 149 */         hashtable.put("" + i, "" + j);
/*     */       } 
/* 151 */     } catch (Exception exception) {
/* 152 */       this.lm.writeLog(exception);
/*     */     } 
/* 154 */     return hashtable;
/*     */   }
/*     */   
/*     */   private ArrayList getMonResultByJobTitle(String paramString1, String paramString2) {
/* 158 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/* 160 */       for (byte b = 1; b < 13; b++) {
/* 161 */         int i = 0;
/* 162 */         String str1 = "" + paramString1 + "" + Util.add0(b, 2) + "-01";
/* 163 */         String str2 = "" + paramString1 + "" + Util.add0(b, 2) + "-31";
/* 164 */         String str3 = " and demandregdate>='" + str1 + "' and demandregdate <='" + str2 + "' ";
/* 165 */         String str4 = "select demandnum from HrmUseDemand where demandjobtitle = " + paramString2 + str3;
/* 166 */         this.rs.executeSql(str4);
/* 167 */         while (this.rs.next()) {
/* 168 */           int j = Util.getIntValue(this.rs.getString("demandnum"), 0);
/* 169 */           i += j;
/*     */         } 
/* 171 */         arrayList.add("" + i);
/*     */       } 
/* 173 */     } catch (Exception exception) {
/* 174 */       this.lm.writeLog(exception);
/*     */     } 
/* 176 */     return arrayList;
/*     */   }
/*     */   
/*     */   private Hashtable getResultByUseKind(String paramString) {
/* 180 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*     */     try {
/* 182 */       RecordSet recordSet = new RecordSet();
/* 183 */       UseKindComInfo useKindComInfo = new UseKindComInfo();
/* 184 */       String str = "select distinct(demandkind) from HrmUseDemand,HrmUseKind where demandkind = HrmUseKind.id " + paramString + " order by demandkind ";
/* 185 */       this.rs.executeSql(str);
/* 186 */       while (this.rs.next()) {
/* 187 */         int i = Util.getIntValue(this.rs.getString("demandkind"));
/* 188 */         String str1 = useKindComInfo.getUseKindname("" + i);
/* 189 */         this.show.put("" + i, str1);
/* 190 */         int j = 0;
/* 191 */         String str2 = "select demandnum from HrmUseDemand where demandkind = " + i + paramString;
/* 192 */         recordSet.executeSql(str2);
/* 193 */         while (recordSet.next()) {
/* 194 */           int k = Util.getIntValue(recordSet.getString("demandnum"), 0);
/* 195 */           j += k;
/*     */         } 
/* 197 */         hashtable.put("" + i, "" + j);
/*     */       } 
/* 199 */     } catch (Exception exception) {
/* 200 */       this.lm.writeLog(exception);
/*     */     } 
/* 202 */     return hashtable;
/*     */   }
/*     */   
/*     */   private ArrayList getMonResultByUseKind(String paramString1, String paramString2) {
/* 206 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/* 208 */       for (byte b = 1; b < 13; b++) {
/* 209 */         int i = 0;
/* 210 */         String str1 = "" + paramString1 + "" + Util.add0(b, 2) + "-01";
/* 211 */         String str2 = "" + paramString1 + "" + Util.add0(b, 2) + "-31";
/* 212 */         String str3 = " and demandregdate>='" + str1 + "' and demandregdate <='" + str2 + "' ";
/* 213 */         String str4 = "select demandnum from HrmUseDemand where demandkind = " + paramString2 + str3;
/* 214 */         this.rs.executeSql(str4);
/* 215 */         while (this.rs.next()) {
/* 216 */           int j = Util.getIntValue(this.rs.getString("demandnum"), 0);
/* 217 */           i += j;
/*     */         } 
/* 219 */         arrayList.add("" + i);
/*     */       } 
/* 221 */     } catch (Exception exception) {
/* 222 */       this.lm.writeLog(exception);
/*     */     } 
/* 224 */     return arrayList;
/*     */   }
/*     */   
/*     */   private Hashtable getResultByEduLevel(String paramString) {
/* 228 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*     */     try {
/* 230 */       RecordSet recordSet = new RecordSet();
/* 231 */       EducationLevelComInfo educationLevelComInfo = new EducationLevelComInfo();
/* 232 */       String str = "select distinct(leastedulevel) from HrmUseDemand,HrmEducationLevel where  leastedulevel = HrmEducationLevel.id" + paramString + " order by leastedulevel ";
/* 233 */       this.rs.executeSql(str);
/* 234 */       while (this.rs.next()) {
/* 235 */         int i = Util.getIntValue(this.rs.getString("leastedulevel"));
/* 236 */         String str1 = educationLevelComInfo.getEducationLevelname("" + i);
/* 237 */         this.show.put("" + i, str1);
/* 238 */         int j = 0;
/* 239 */         String str2 = "select demandnum from HrmUseDemand where leastedulevel = " + i + paramString;
/* 240 */         recordSet.executeSql(str2);
/* 241 */         while (recordSet.next()) {
/* 242 */           int k = Util.getIntValue(recordSet.getString("demandnum"), 0);
/* 243 */           j += k;
/*     */         } 
/* 245 */         hashtable.put("" + i, "" + j);
/*     */       } 
/* 247 */     } catch (Exception exception) {
/* 248 */       this.lm.writeLog(exception);
/*     */     } 
/* 250 */     return hashtable;
/*     */   }
/*     */   
/*     */   private ArrayList getMonResultByEduLevel(String paramString1, String paramString2) {
/* 254 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/* 256 */       for (byte b = 1; b < 13; b++) {
/* 257 */         int i = 0;
/* 258 */         String str1 = "" + paramString1 + "" + Util.add0(b, 2) + "-01";
/* 259 */         String str2 = "" + paramString1 + "" + Util.add0(b, 2) + "-31";
/* 260 */         String str3 = " and demandregdate>='" + str1 + "' and demandregdate <='" + str2 + "' ";
/* 261 */         String str4 = "select demandnum from HrmUseDemand where leastedulevel = " + paramString2 + str3;
/* 262 */         this.rs.executeSql(str4);
/* 263 */         while (this.rs.next()) {
/* 264 */           int j = Util.getIntValue(this.rs.getString("demandnum"), 0);
/* 265 */           i += j;
/*     */         } 
/* 267 */         arrayList.add("" + i);
/*     */       } 
/* 269 */     } catch (Exception exception) {
/* 270 */       this.lm.writeLog(exception);
/*     */     } 
/* 272 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getShow() {
/* 280 */     return this.show;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/UseDemandManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */