/*    */ package weaver.hrm.report.domain;
/*    */ 
/*    */ import java.util.Date;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmRpSubTemplate
/*    */ {
/*    */   private int id;
/*    */   private String name;
/*    */   private int author;
/*    */   private Date createDate;
/*    */   private String scope;
/*    */   private int delflag;
/*    */   
/*    */   public HrmRpSubTemplate() {}
/*    */   
/*    */   public HrmRpSubTemplate(boolean paramBoolean) {
/* 32 */     if (paramBoolean) {
/* 33 */       init();
/*    */     }
/*    */   }
/*    */   
/*    */   public void init() {
/* 38 */     this.id = 0;
/* 39 */     this.name = "";
/* 40 */     this.author = 0;
/* 41 */     this.createDate = new Date();
/* 42 */     this.scope = "";
/* 43 */     this.delflag = 0;
/*    */   }
/*    */   
/*    */   public void setId(int paramInt) {
/* 47 */     this.id = paramInt;
/*    */   }
/*    */   
/*    */   public int getId() {
/* 51 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setName(String paramString) {
/* 55 */     this.name = paramString;
/*    */   }
/*    */   
/*    */   public String getName() {
/* 59 */     return this.name;
/*    */   }
/*    */   
/*    */   public void setAuthor(int paramInt) {
/* 63 */     this.author = paramInt;
/*    */   }
/*    */   
/*    */   public int getAuthor() {
/* 67 */     return this.author;
/*    */   }
/*    */   
/*    */   public void setCreateDate(Date paramDate) {
/* 71 */     this.createDate = paramDate;
/*    */   }
/*    */   
/*    */   public Date getCreateDate() {
/* 75 */     return this.createDate;
/*    */   }
/*    */   
/*    */   public void setScope(String paramString) {
/* 79 */     this.scope = paramString;
/*    */   }
/*    */   
/*    */   public String getScope() {
/* 83 */     return this.scope;
/*    */   }
/*    */   
/*    */   public void setDelflag(int paramInt) {
/* 87 */     this.delflag = paramInt;
/*    */   }
/*    */   
/*    */   public int getDelflag() {
/* 91 */     return this.delflag;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/domain/HrmRpSubTemplate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */