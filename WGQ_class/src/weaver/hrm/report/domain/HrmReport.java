/*     */ package weaver.hrm.report.domain;
/*     */ 
/*     */ import weaver.common.StringUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmReport
/*     */ {
/*     */   private int resourceId;
/*     */   private String fromDate;
/*     */   private String fromTime;
/*     */   private String toDate;
/*     */   private String toTime;
/*     */   private String resId;
/*     */   private String status;
/*     */   private int subCompanyId;
/*     */   private int departmentId;
/*     */   private int otype;
/*     */   private int newLeaveType;
/*     */   
/*     */   public int getResourceId() {
/*  34 */     return this.resourceId;
/*     */   }
/*     */   
/*     */   public void setResourceId(int paramInt) {
/*  38 */     this.resourceId = paramInt;
/*  39 */     this.resId = String.valueOf(paramInt);
/*     */   }
/*     */   
/*     */   public String getFromDate() {
/*  43 */     return this.fromDate;
/*     */   }
/*     */   
/*     */   public void setFromDate(String paramString) {
/*  47 */     this.fromDate = paramString;
/*     */   }
/*     */   
/*     */   public String getFromTime() {
/*  51 */     return this.fromTime;
/*     */   }
/*     */   
/*     */   public void setFromTime(String paramString) {
/*  55 */     this.fromTime = paramString;
/*     */   }
/*     */   
/*     */   public String getToDate() {
/*  59 */     return this.toDate;
/*     */   }
/*     */   
/*     */   public void setToDate(String paramString) {
/*  63 */     this.toDate = paramString;
/*     */   }
/*     */   
/*     */   public String getToTime() {
/*  67 */     return this.toTime;
/*     */   }
/*     */   
/*     */   public void setToTime(String paramString) {
/*  71 */     this.toTime = paramString;
/*     */   }
/*     */   
/*     */   public String getFullFromTime() {
/*  75 */     return this.fromDate + " " + this.fromTime;
/*     */   }
/*     */   
/*     */   public String getFullToTime() {
/*  79 */     return this.toDate + " " + this.toTime;
/*     */   }
/*     */   
/*     */   public int getDepartmentId() {
/*  83 */     return this.departmentId;
/*     */   }
/*     */   
/*     */   public void setDepartmentId(int paramInt) {
/*  87 */     this.departmentId = paramInt;
/*     */   }
/*     */   
/*     */   public String getResId() {
/*  91 */     return this.resId;
/*     */   }
/*     */   
/*     */   public void setResId(String paramString) {
/*  95 */     this.resId = paramString;
/*  96 */     this.resourceId = StringUtil.parseToInt(paramString);
/*     */   }
/*     */   
/*     */   public String getStatus() {
/* 100 */     return this.status;
/*     */   }
/*     */   
/*     */   public void setStatus(String paramString) {
/* 104 */     this.status = paramString;
/*     */   }
/*     */   
/*     */   public int getSubCompanyId() {
/* 108 */     return this.subCompanyId;
/*     */   }
/*     */   
/*     */   public void setSubCompanyId(int paramInt) {
/* 112 */     this.subCompanyId = paramInt;
/*     */   }
/*     */   
/*     */   public int getNewLeaveType() {
/* 116 */     return this.newLeaveType;
/*     */   }
/*     */   
/*     */   public void setNewLeaveType(int paramInt) {
/* 120 */     this.newLeaveType = paramInt;
/*     */   }
/*     */   
/*     */   public int getOtype() {
/* 124 */     return this.otype;
/*     */   }
/*     */   
/*     */   public void setOtype(int paramInt) {
/* 128 */     this.otype = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/domain/HrmReport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */