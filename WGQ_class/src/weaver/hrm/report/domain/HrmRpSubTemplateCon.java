/*     */ package weaver.hrm.report.domain;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmRpSubTemplateCon
/*     */ {
/*     */   private Integer id;
/*     */   private Integer templateId;
/*     */   private String colName;
/*     */   private String conHtmltype;
/*     */   private String conType;
/*     */   private String conOpt;
/*     */   private String conValue;
/*     */   private String conOpt1;
/*     */   private String conValue1;
/*     */   
/*     */   public HrmRpSubTemplateCon() {
/*  29 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmRpSubTemplateCon(boolean paramBoolean) {
/*  33 */     if (paramBoolean) {
/*  34 */       init();
/*     */     }
/*     */   }
/*     */   
/*     */   public void init() {
/*  39 */     this.id = Integer.valueOf(0);
/*  40 */     this.templateId = Integer.valueOf(0);
/*  41 */     this.colName = "";
/*  42 */     this.conHtmltype = "";
/*  43 */     this.conType = "";
/*  44 */     this.conOpt = "";
/*  45 */     this.conValue = "";
/*  46 */     this.conOpt1 = "";
/*  47 */     this.conValue1 = "";
/*     */   }
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  51 */     this.id = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getId() {
/*  55 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setTemplateId(Integer paramInteger) {
/*  59 */     this.templateId = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getTemplateId() {
/*  63 */     return this.templateId;
/*     */   }
/*     */   
/*     */   public void setColName(String paramString) {
/*  67 */     this.colName = paramString;
/*     */   }
/*     */   
/*     */   public String getColName() {
/*  71 */     return this.colName;
/*     */   }
/*     */   
/*     */   public void setConHtmltype(String paramString) {
/*  75 */     this.conHtmltype = paramString;
/*     */   }
/*     */   
/*     */   public String getConHtmltype() {
/*  79 */     return this.conHtmltype;
/*     */   }
/*     */   
/*     */   public void setConType(String paramString) {
/*  83 */     this.conType = paramString;
/*     */   }
/*     */   
/*     */   public String getConType() {
/*  87 */     return this.conType;
/*     */   }
/*     */   
/*     */   public void setConOpt(String paramString) {
/*  91 */     this.conOpt = paramString;
/*     */   }
/*     */   
/*     */   public String getConOpt() {
/*  95 */     return this.conOpt;
/*     */   }
/*     */   
/*     */   public void setConValue(String paramString) {
/*  99 */     this.conValue = paramString;
/*     */   }
/*     */   
/*     */   public String getConValue() {
/* 103 */     return this.conValue;
/*     */   }
/*     */   
/*     */   public void setConOpt1(String paramString) {
/* 107 */     this.conOpt1 = paramString;
/*     */   }
/*     */   
/*     */   public String getConOpt1() {
/* 111 */     return this.conOpt1;
/*     */   }
/*     */   
/*     */   public void setConValue1(String paramString) {
/* 115 */     this.conValue1 = paramString;
/*     */   }
/*     */   
/*     */   public String getConValue1() {
/* 119 */     return this.conValue1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/domain/HrmRpSubTemplateCon.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */