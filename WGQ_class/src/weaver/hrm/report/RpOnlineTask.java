/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.TimerTask;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.LogMan;
/*     */ import weaver.hrm.online.HrmUserOnlineMap;
/*     */ import weaver.hrm.online.IPUtil;
/*     */ import weaver.login.LicenseCheckLogin;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpOnlineTask
/*     */   extends TimerTask
/*     */ {
/*  35 */   LogMan lm = LogMan.getInstance();
/*  36 */   int onlineHrm = 0;
/*  37 */   int onlineAllHrm = 0;
/*  38 */   int avgNum = 0;
/*  39 */   int times = 1;
/*  40 */   String serverip = "";
/*     */ 
/*     */   
/*     */   public void run() {
/*     */     try {
/*  45 */       LicenseCheckLogin licenseCheckLogin = new LicenseCheckLogin();
/*  46 */       licenseCheckLogin.checkOnlineUser();
/*     */       
/*  48 */       HrmUserOnlineMap hrmUserOnlineMap = HrmUserOnlineMap.getInstance();
/*  49 */       this.onlineHrm = hrmUserOnlineMap.getCurrentOnlineCount();
/*  50 */       this.onlineAllHrm = hrmUserOnlineMap.getUserOnlineCount();
/*  51 */       this.serverip = IPUtil.getLocalIp();
/*  52 */       String str1 = " and serverip = '" + this.serverip + "' ";
/*     */       
/*  54 */       SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
/*  55 */       SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("HH:mm");
/*     */       
/*  57 */       Date date = new Date();
/*  58 */       String str2 = simpleDateFormat1.format(date);
/*  59 */       String str3 = simpleDateFormat2.format(date);
/*  60 */       Calendar calendar = Calendar.getInstance();
/*  61 */       int i = calendar.get(11);
/*  62 */       int j = calendar.get(1);
/*  63 */       int k = calendar.get(2) + 1;
/*     */ 
/*     */ 
/*     */       
/*  67 */       String str4 = "INSERT INTO HrmOnlineCount(online_date,online_time,online_year,online_month,online_num,serverip) VALUES('" + str2 + "','" + str3 + "'," + j + "," + k + "," + this.onlineHrm + ",'" + this.serverip + "')";
/*     */       
/*  69 */       RecordSet recordSet = new RecordSet();
/*  70 */       recordSet.execute(str4);
/*     */       
/*  72 */       str4 = "INSERT INTO HrmOnlineCount(online_date,online_time,online_year,online_month,online_num,serverip) VALUES('" + str2 + "','" + str3 + "'," + j + "," + k + "," + this.onlineAllHrm + ",'ALL')";
/*     */       
/*  74 */       recordSet.execute(str4);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  80 */       String str5 = ((i < 10) ? ("0" + i) : (String)Integer.valueOf(i)) + ":00";
/*  81 */       String str6 = ((i < 10) ? ("0" + i) : (String)Integer.valueOf(i)) + ":59";
/*  82 */       str4 = "select avg(online_num) as avg_num  from HrmOnlineCount where online_date='" + str2 + "' and online_time>='" + str5 + "' and online_time<='" + str6 + "'" + str1;
/*  83 */       recordSet.execute(str4);
/*  84 */       if (recordSet.next()) {
/*  85 */         this.avgNum = (int)recordSet.getDouble("avg_num");
/*     */       }
/*     */       
/*  88 */       str4 = "select id from HrmOnlineAvg where online_date='" + str2 + "' and point_time=" + i + str1;
/*  89 */       recordSet.execute(str4);
/*  90 */       if (recordSet.next()) {
/*  91 */         str4 = "update HrmOnlineAvg set online_num=" + this.avgNum + " where online_date='" + str2 + "' and point_time=" + i + str1;
/*     */       } else {
/*  93 */         str4 = "insert into HrmOnlineAvg(online_date,online_year,online_month,point_time,online_num,serverip) values('" + str2 + "'," + j + "," + k + "," + i + "," + this.avgNum + ",'" + this.serverip + "')";
/*     */       } 
/*     */       
/*  96 */       recordSet.execute(str4);
/*     */       
/*  98 */       str4 = "select avg(online_num) as avg_num  from HrmOnlineCount where online_date='" + str2 + "' and online_time>='" + str5 + "' and online_time<='" + str6 + "' and serverip='ALL' ";
/*  99 */       recordSet.execute(str4);
/* 100 */       if (recordSet.next()) {
/* 101 */         this.avgNum = (int)recordSet.getDouble("avg_num");
/*     */       }
/*     */       
/* 104 */       str4 = "select id from HrmOnlineAvg where online_date='" + str2 + "' and point_time=" + i + " and serverip='ALL' ";
/* 105 */       recordSet.execute(str4);
/* 106 */       if (recordSet.next()) {
/* 107 */         str4 = "update HrmOnlineAvg set online_num=" + this.avgNum + " where online_date='" + str2 + "' and point_time=" + i + " and serverip='ALL' ";
/*     */       } else {
/* 109 */         str4 = "insert into HrmOnlineAvg(online_date,online_year,online_month,point_time,online_num,serverip) values('" + str2 + "'," + j + "," + k + "," + i + "," + this.avgNum + ",'ALL')";
/*     */       } 
/*     */       
/* 112 */       recordSet.execute(str4);
/*     */ 
/*     */ 
/*     */       
/* 116 */       str4 = "select count(*) as avg_num  from HrmRefuseCount where refuse_date='" + str2 + "' and refuse_hour=" + i;
/* 117 */       recordSet.execute(str4);
/* 118 */       if (recordSet.next()) {
/* 119 */         this.avgNum = (int)recordSet.getDouble("avg_num");
/*     */       }
/* 121 */       str4 = "select id from HrmRefuseAvg where refuse_date='" + str2 + "' and refuse_hour=" + i;
/* 122 */       recordSet.execute(str4);
/* 123 */       if (recordSet.next()) {
/* 124 */         str4 = "update HrmRefuseAvg set refuse_num=" + this.avgNum + " where refuse_date='" + str2 + "' and refuse_hour=" + i;
/*     */       } else {
/* 126 */         str4 = "insert into HrmRefuseAvg(refuse_date,refuse_year,refuse_month,refuse_hour,refuse_num) values('" + str2 + "'," + j + "," + k + "," + i + "," + this.avgNum + ")";
/*     */       } 
/*     */       
/* 129 */       recordSet.execute(str4);
/*     */ 
/*     */     
/*     */     }
/* 133 */     catch (Exception exception) {
/* 134 */       this.lm.writeLog(exception);
/* 135 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpOnlineTask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */