/*    */ package weaver.hrm.report;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.systeminfo.SysMaintenanceLog;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RpTrainPeoNumByDepManager
/*    */   extends BaseBean
/*    */ {
/*    */   private RecordSet statement;
/*    */   private SysMaintenanceLog log;
/*    */   private String startdate;
/*    */   private String enddate;
/*    */   private String direction;
/*    */   private int departmentid;
/*    */   
/*    */   public RpTrainPeoNumByDepManager() {
/* 24 */     resetParameter();
/*    */   }
/*    */   
/*    */   public void resetParameter() {
/* 28 */     this.startdate = "";
/* 29 */     this.enddate = "";
/* 30 */     this.departmentid = -1;
/* 31 */     this.direction = "";
/*    */   }
/*    */   
/*    */   public void setStartdate(String paramString) {
/* 35 */     this.startdate = paramString;
/*    */   }
/*    */   
/*    */   public void setEnddate(String paramString) {
/* 39 */     this.enddate = paramString;
/*    */   }
/*    */   
/*    */   public void setDepartmentid(int paramInt) {
/* 43 */     this.departmentid = paramInt;
/*    */   }
/*    */   
/*    */   public void setDirection(String paramString) {
/* 47 */     this.direction = paramString;
/*    */   }
/*    */   
/*    */   public int getTrainPeoNumTotal() throws Exception {
/* 51 */     return this.statement.getInt("totalnum");
/*    */   }
/*    */   
/*    */   public int getTrainTypeid() throws Exception {
/* 55 */     return this.statement.getInt("traintype");
/*    */   }
/*    */   
/*    */   public void selectRpTrainPeoNumByDep() throws Exception {
/* 59 */     this.statement = new RecordSet();
/*    */     try {
/* 61 */       String str1 = "";
/* 62 */       if (this.departmentid == 0) { str1 = ""; }
/* 63 */       else { str1 = " where resourceid in(select id from HrmResource where departmentid=" + this.departmentid + ")"; }
/* 64 */        if (!this.startdate.equals(""))
/* 65 */         if (str1.equals("")) { str1 = " where trainstartdate >='" + this.startdate + "'"; }
/* 66 */         else { str1 = str1 + " and trainstartdate >='" + this.startdate + "'"; }
/*    */          
/* 68 */       if (!this.enddate.equals("")) {
/* 69 */         if (str1.equals("")) { str1 = " where trainenddate <='" + this.enddate + "'"; }
/*    */         else
/* 71 */         { str1 = str1 + " and trainenddate <='" + this.enddate + "'"; }
/*    */       
/*    */       }
/*    */       
/* 75 */       String str2 = "";
/* 76 */       if (this.direction.equals("1")) {
/* 77 */         str2 = "select count(resourceid) totalnum,traintype from HrmTrainRecord";
/*    */       } else {
/*    */         
/* 80 */         str2 = "select count(distinct(resourceid)) totalnum,traintype from HrmTrainRecord";
/*    */       } 
/* 82 */       str2 = str2 + str1;
/* 83 */       str2 = str2 + " group by traintype";
/* 84 */       this.statement.executeSql(str2);
/* 85 */       writeLog(str2);
/*    */     
/*    */     }
/* 88 */     catch (Exception exception) {
/* 89 */       writeLog(exception);
/* 90 */       throw exception;
/*    */     } 
/*    */   }
/*    */   
/*    */   public boolean next() throws Exception {
/* 95 */     return this.statement.next();
/*    */   }
/*    */   
/*    */   public void closeStatement() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpTrainPeoNumByDepManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */