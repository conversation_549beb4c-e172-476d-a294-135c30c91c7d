/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpJobManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private SysMaintenanceLog log;
/*     */   private String resourcetype1;
/*     */   private String resourcetype2;
/*     */   private String resourcetype3;
/*     */   private String resourcetype4;
/*     */   private int departmentid;
/*     */   private String action;
/*     */   private int actionid;
/*     */   
/*     */   public RpJobManager() {
/*  34 */     resetParameter();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetParameter() {
/*  41 */     this.resourcetype1 = "";
/*  42 */     this.resourcetype2 = "";
/*  43 */     this.resourcetype3 = "";
/*  44 */     this.resourcetype4 = "";
/*  45 */     this.departmentid = -1;
/*  46 */     this.action = "";
/*  47 */     this.actionid = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype1(String paramString) {
/*  55 */     this.resourcetype1 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype2(String paramString) {
/*  62 */     this.resourcetype2 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype3(String paramString) {
/*  69 */     this.resourcetype3 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype4(String paramString) {
/*  76 */     this.resourcetype4 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDepartmentid(int paramInt) {
/*  83 */     this.departmentid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAction(String paramString) {
/*  91 */     this.action = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionid(int paramInt) {
/*  99 */     this.actionid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResultid() throws Exception {
/* 108 */     return this.statement.getInt("resultid");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResultcount() throws Exception {
/* 117 */     return this.statement.getInt("num");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectRpJob() throws Exception {
/* 125 */     this.statement = new RecordSet();
/*     */     try {
/* 127 */       String str1 = "";
/* 128 */       boolean bool = false;
/* 129 */       if (this.departmentid == 0) { str1 = ""; }
/* 130 */       else { str1 = "where departmentid=" + this.departmentid; }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 139 */       if (!this.resourcetype1.equals("")) {
/* 140 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype1; }
/* 141 */         else { str1 = str1 + " and resourcetype in('" + this.resourcetype1; }
/* 142 */          bool = true;
/*     */       } 
/* 144 */       if (!this.resourcetype2.equals("")) {
/* 145 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype2; }
/*     */         
/* 147 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype2; }
/* 148 */         else { str1 = str1 + "','" + this.resourcetype2; }
/*     */         
/* 150 */         bool = true;
/*     */       } 
/* 152 */       if (!this.resourcetype3.equals("")) {
/* 153 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype3; }
/*     */         
/* 155 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype3; }
/* 156 */         else { str1 = str1 + "','" + this.resourcetype3; }
/*     */         
/* 158 */         bool = true;
/*     */       } 
/* 160 */       if (!this.resourcetype4.equals("")) {
/* 161 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype4; }
/*     */         
/* 163 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype4; }
/* 164 */         else { str1 = str1 + "','" + this.resourcetype4; }
/*     */         
/* 166 */         bool = true;
/*     */       } 
/* 168 */       if (bool == true) str1 = str1 + "' )"; 
/* 169 */       String str2 = "";
/* 170 */       if (this.action.equals("jobgroup"))
/* 171 */         str2 = "select count(id) num,jobgroup resultid from hrmresource " + str1 + " group by jobgroup order by num desc"; 
/* 172 */       if (this.action.equals("jobactivity")) {
/* 173 */         str2 = "select count(id) num,jobactivity resultid from hrmresource ";
/* 174 */         if (str1.equals("")) { str1 = "where jobactivity in( select id from hrmjobactivities where jobgroupid=" + this.actionid + ")"; }
/* 175 */         else { str1 = str1 + " and jobactivity in( select id from hrmjobactivities where jobgroupid=" + this.actionid + ")"; }
/* 176 */          str2 = str2 + str1 + " group by jobactivity order by num desc";
/*     */       } 
/* 178 */       if (this.action.equals("jobtitle")) {
/* 179 */         str2 = "select count(id) num,jobtitle resultid from hrmresource ";
/* 180 */         if (str1.equals("")) { str1 = "where jobtitle in( select id from hrmjobtitles where jobactivityid=" + this.actionid + ")"; }
/* 181 */         else { str1 = str1 + " and jobtitle in( select id from hrmjobtitles where jobactivityid=" + this.actionid + ")"; }
/* 182 */          str2 = str2 + str1 + " group by jobtitle order by num desc";
/*     */       } 
/* 184 */       this.statement.executeSql(str2);
/*     */     }
/* 186 */     catch (Exception exception) {
/* 187 */       writeLog(exception);
/* 188 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/* 198 */     return this.statement.next();
/*     */   }
/*     */   
/*     */   public void closeStatement() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpJobManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */