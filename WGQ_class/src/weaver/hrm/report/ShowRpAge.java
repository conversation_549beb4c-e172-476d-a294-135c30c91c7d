/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import com.sun.image.codec.jpeg.JPEGCodec;
/*     */ import com.sun.image.codec.jpeg.JPEGEncodeParam;
/*     */ import com.sun.image.codec.jpeg.JPEGImageEncoder;
/*     */ import java.awt.BasicStroke;
/*     */ import java.awt.Color;
/*     */ import java.awt.Graphics2D;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ShowRpAge
/*     */   extends HttpServlet
/*     */ {
/*  29 */   private int width = 320;
/*  30 */   private int height = 320;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  42 */     String str = Util.null2String(paramHttpServletRequest.getParameter("departmentid"));
/*     */ 
/*     */     
/*     */     try {
/*  46 */       ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*  47 */       BufferedOutputStream bufferedOutputStream = new BufferedOutputStream((OutputStream)servletOutputStream);
/*     */       
/*  49 */       BufferedImage bufferedImage = new BufferedImage(this.width, this.height, 1);
/*     */       
/*  51 */       JPEGEncodeParam jPEGEncodeParam = null;
/*  52 */       JPEGImageEncoder jPEGImageEncoder = JPEGCodec.createJPEGEncoder(bufferedOutputStream);
/*     */       
/*  54 */       jPEGEncodeParam = jPEGImageEncoder.getDefaultJPEGEncodeParam(bufferedImage);
/*  55 */       jPEGEncodeParam.setQuality(0.9F, true);
/*  56 */       jPEGImageEncoder.setJPEGEncodeParam(jPEGEncodeParam);
/*     */       
/*  58 */       Graphics2D graphics2D = bufferedImage.createGraphics();
/*     */       
/*  60 */       graphics2D.setColor(Color.white);
/*  61 */       graphics2D.fillRect(0, 0, this.width, this.height);
/*     */       
/*  63 */       byte b1 = 10;
/*  64 */       char c = 'Ĭ';
/*  65 */       byte b2 = 10;
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  70 */       RpAgeManager rpAgeManager = new RpAgeManager();
/*     */       
/*  72 */       ArrayList<String> arrayList1 = new ArrayList();
/*  73 */       ArrayList<String> arrayList2 = new ArrayList();
/*  74 */       rpAgeManager.resetParameter();
/*  75 */       rpAgeManager.setDepartmentid(Util.getIntValue(str, 0));
/*  76 */       rpAgeManager.selectRpAge();
/*  77 */       while (rpAgeManager.next()) {
/*  78 */         arrayList1.add(rpAgeManager.getAgenum() + "");
/*  79 */         arrayList2.add(rpAgeManager.getYearorder() + "");
/*     */       } 
/*  81 */       rpAgeManager.closeStatement();
/*     */       
/*  83 */       int i = 0;
/*  84 */       ArrayList<String> arrayList3 = new ArrayList();
/*  85 */       ArrayList<String> arrayList4 = new ArrayList();
/*  86 */       ArrayList<Color> arrayList = new ArrayList();
/*     */       
/*  88 */       int[] arrayOfInt = new int[7]; byte b3;
/*  89 */       for (b3 = 0; b3 < arrayList1.size(); b3++) {
/*  90 */         int k = Util.getIntValue(arrayList2.get(b3), 0);
/*  91 */         int m = Util.getIntValue(arrayList1.get(b3), 0);
/*  92 */         if (k < 5)
/*  93 */           arrayOfInt[0] = arrayOfInt[0] + m; 
/*  94 */         if (k == 5)
/*  95 */           arrayOfInt[1] = arrayOfInt[1] + m; 
/*  96 */         if (k == 6)
/*  97 */           arrayOfInt[2] = arrayOfInt[2] + m; 
/*  98 */         if (k == 7)
/*  99 */           arrayOfInt[3] = arrayOfInt[3] + m; 
/* 100 */         if (k == 8)
/* 101 */           arrayOfInt[4] = arrayOfInt[4] + m; 
/* 102 */         if (k == 9)
/* 103 */           arrayOfInt[5] = arrayOfInt[5] + m; 
/* 104 */         if (k > 9)
/* 105 */           arrayOfInt[6] = arrayOfInt[6] + m; 
/* 106 */         i += m;
/*     */       } 
/* 108 */       arrayList3.add("<25");
/* 109 */       arrayList3.add("25-29");
/* 110 */       arrayList3.add("30-34");
/* 111 */       arrayList3.add("35-39");
/* 112 */       arrayList3.add("40-44");
/* 113 */       arrayList3.add("45-49");
/* 114 */       arrayList3.add(">49");
/*     */       
/* 116 */       arrayList.add(Color.red);
/* 117 */       arrayList.add(Color.lightGray);
/* 118 */       arrayList.add(Color.cyan);
/* 119 */       arrayList.add(Color.magenta);
/* 120 */       arrayList.add(Color.pink);
/* 121 */       arrayList.add(Color.yellow);
/* 122 */       arrayList.add(Color.green);
/*     */       
/* 124 */       for (b3 = 0; b3 < arrayOfInt.length; b3++) {
/* 125 */         int k = (int)(arrayOfInt[b3] * 360.0F / i);
/* 126 */         arrayList4.add("" + k);
/*     */       } 
/* 128 */       BasicStroke basicStroke = new BasicStroke(0.0F);
/* 129 */       graphics2D.setStroke(basicStroke);
/* 130 */       int j = 0;
/* 131 */       for (byte b4 = 0; b4 < arrayList3.size(); b4++) {
/* 132 */         int k = Util.getIntValue("" + arrayList4.get(b4), 0);
/* 133 */         if (k != 0) {
/*     */           
/* 135 */           graphics2D.setColor(arrayList.get(b4));
/* 136 */           graphics2D.fillArc(b1, b2, c, c, j, k);
/*     */ 
/*     */           
/* 139 */           int m = (int)(Math.cos(Math.toRadians(((k + j + j) / 2))) * (2 * c / 5));
/* 140 */           int n = (int)(Math.sin(Math.toRadians(((k + j + j) / 2))) * (2 * c / 5));
/*     */           
/* 142 */           graphics2D.setColor(Color.black);
/* 143 */           graphics2D.drawString("" + arrayList3.get(b4) + "(" + arrayOfInt[b4] + ")", c / 2 + m - 10, c / 2 - n);
/* 144 */           j += k;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 155 */       graphics2D.dispose();
/* 156 */       jPEGImageEncoder.encode(bufferedImage);
/*     */ 
/*     */       
/* 159 */       servletOutputStream.flush();
/* 160 */       bufferedOutputStream.close();
/* 161 */       servletOutputStream.close();
/*     */ 
/*     */     
/*     */     }
/* 165 */     catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/ShowRpAge.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */