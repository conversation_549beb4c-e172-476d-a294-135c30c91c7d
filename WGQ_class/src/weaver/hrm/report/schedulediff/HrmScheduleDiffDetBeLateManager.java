/*     */ package weaver.hrm.report.schedulediff;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.ScheduleApplicationRule;
/*     */ import weaver.hrm.attendance.manager.HrmScheduleApplicationRuleManager;
/*     */ import weaver.hrm.report.manager.HrmReportManager;
/*     */ import weaver.hrm.schedule.domain.HrmSchedule;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffDetBeLateManager
/*     */   extends HrmReportManager
/*     */ {
/*     */   public HrmScheduleDiffDetBeLateManager() {
/*  33 */     this((User)null);
/*     */   }
/*     */   
/*     */   public HrmScheduleDiffDetBeLateManager(User paramUser) {
/*  37 */     super(paramUser, HrmReportManager.SignType.BE_LATE);
/*     */   }
/*     */   
/*     */   public List getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  41 */     setLanId((paramUser == null) ? 7 : paramUser.getLanguage());
/*     */     
/*  43 */     String str1 = StringUtil.vString(paramMap.get("isRule"));
/*  44 */     String str2 = StringUtil.vString(paramMap.get("ruleid"));
/*  45 */     HrmScheduleApplicationRuleManager hrmScheduleApplicationRuleManager = new HrmScheduleApplicationRuleManager();
/*     */     
/*  47 */     List list1 = new ArrayList();
/*  48 */     List list2 = getScheduleList(paramMap);
/*     */     
/*  50 */     if ("1".equals(str1)) {
/*  51 */       ScheduleApplicationRule scheduleApplicationRule = (ScheduleApplicationRule)hrmScheduleApplicationRuleManager.get(str2);
/*  52 */       for (Map map : list2) {
/*  53 */         String str = StringUtil.vString(map.get("resourceId"));
/*  54 */         int i = StringUtil.parseToInt((String)this.indexMap.get(str), -1);
/*  55 */         if (i >= 0) {
/*  56 */           Map map1 = this.personList.get(i);
/*  57 */           String str3 = StringUtil.vString(map.get(HrmReportManager.SignType.BE_LATE + "_realHours"));
/*     */           
/*  59 */           double d = StringUtil.parseToDouble(str3);
/*  60 */           boolean bool = checkIsinRule(map1, scheduleApplicationRule, (long)(d * 60.0D));
/*  61 */           if (bool) {
/*  62 */             list1.add(map);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } else {
/*  67 */       list1 = list2;
/*     */     } 
/*     */     
/*  70 */     return list1;
/*     */   }
/*     */ 
/*     */   
/*     */   protected List<Map<String, Object>> getScheduleList(List<Map<String, Object>> paramList, Map<String, Map<String, List<HrmSchedule>>> paramMap) {
/*  75 */     List<Map<String, Object>> list = getSignInList();
/*  76 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/*  77 */     if (paramList != null) {
/*  78 */       for (Map<String, Object> map : paramList) {
/*  79 */         if (notEquals(map, list))
/*  80 */           continue;  arrayList.add(map);
/*     */       } 
/*     */     }
/*  83 */     return arrayList;
/*     */   }
/*     */   
/*     */   private boolean notEquals(Map<String, Object> paramMap, List<Map<String, Object>> paramList) {
/*  87 */     BaseBean baseBean = new BaseBean();
/*  88 */     boolean bool = false;
/*  89 */     if (paramList != null) {
/*  90 */       boolean bool1 = Boolean.parseBoolean(StringUtil.vString(paramMap.get("isSchedulePerson")));
/*  91 */       int i = StringUtil.parseToInt(String.valueOf(paramMap.get("resourceId")), 0);
/*  92 */       int j = StringUtil.parseToInt(String.valueOf(paramMap.get("signType")), 0);
/*  93 */       String str1 = StringUtil.vString(paramMap.get("signDate"));
/*  94 */       String str2 = StringUtil.vString(paramMap.get("signTime"));
/*  95 */       for (Map<String, Object> map : paramList) {
/*     */         
/*  97 */         if (StringUtil.parseToInt(String.valueOf(map.get("resourceId")), 0) == i && 
/*  98 */           StringUtil.vString(map.get("signDate")).equals(str1)) {
/*  99 */           if (bool1) {
/* 100 */             List list1 = (List)paramMap.get("detailWorkTimes");
/* 101 */             List list2 = (List)paramMap.get("detailSignTimes");
/* 102 */             boolean bool2 = false;
/* 103 */             for (byte b = 0; b < list1.size(); b++) {
/*     */               
/* 105 */               String str3 = str1 + " " + str2;
/*     */               
/* 107 */               String str4 = str1 + " " + StringUtil.vString(map.get("signTime"));
/* 108 */               String str5 = str1 + " " + ((String[])list2.get(b))[0] + ":00";
/* 109 */               String str6 = str1 + " " + ((String[])list2.get(b))[1] + ":00";
/* 110 */               boolean bool3 = DateUtil.isInDateRange(str3, str5, str6);
/* 111 */               boolean bool4 = DateUtil.isInDateRange(str4, str5, str6);
/*     */               
/* 113 */               if (bool3 && bool4) {
/* 114 */                 if (!StringUtil.vString(map.get("signTime")).equals(str2)) {
/* 115 */                   bool2 = true;
/*     */                 }
/*     */                 break;
/*     */               } 
/*     */             } 
/* 120 */             if (bool2) {
/* 121 */               bool = true;
/*     */               break;
/*     */             } 
/*     */             continue;
/*     */           } 
/* 126 */           if (j == 2) {
/* 127 */             baseBean.writeLog("===[HrmScheduleDiffDetBeLateManager].resourceId=" + i + ",signType=" + j + ",signTime=" + str2 + ",m.get(signTime)=" + StringUtil.vString(map.get("signTime")));
/* 128 */             int k = str2.compareTo(StringUtil.vString(map.get("signStartTime")));
/* 129 */             baseBean.writeLog("===[HrmScheduleDiffDetBeLateManager].flag=" + k);
/* 130 */             if (str2.compareTo(StringUtil.vString(map.get("signStartTime"))) < 0) {
/* 131 */               if (!StringUtil.vString(map.get("signTime")).equals(str2) && StringUtil.vString(map.get("signTime")).compareTo(StringUtil.vString(map.get("signStartTime"))) < 0) {
/* 132 */                 bool = true; break;
/*     */               } 
/*     */               continue;
/*     */             } 
/* 136 */             if (!StringUtil.vString(map.get("signTime")).equals(str2) && StringUtil.vString(map.get("signTime")).compareTo(StringUtil.vString(map.get("signStartTime"))) > 0) {
/* 137 */               bool = true; break;
/*     */             } 
/*     */             continue;
/*     */           } 
/* 141 */           if (j == 0) {
/* 142 */             bool = false; continue;
/*     */           } 
/* 144 */           if (!StringUtil.vString(map.get("signTime")).equals(str2)) {
/* 145 */             bool = true;
/*     */           }
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 153 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getSignInList() {
/* 158 */     HrmScheduleDiffDetSignInManager hrmScheduleDiffDetSignInManager = new HrmScheduleDiffDetSignInManager();
/* 159 */     hrmScheduleDiffDetSignInManager.setPersonList(this.personList);
/* 160 */     hrmScheduleDiffDetSignInManager.setIndexMap(this.indexMap);
/* 161 */     return hrmScheduleDiffDetSignInManager.getScheduleList(this.bean);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffDetBeLateManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */