/*     */ package weaver.hrm.report.schedulediff;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*     */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*     */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*     */ import weaver.hrm.report.domain.HrmReport;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffOtherManager
/*     */   extends BaseManager<HrmReport>
/*     */ {
/*  28 */   private int languageId = 7;
/*  29 */   private HrmLeaveTypeColorManager typeManager = null;
/*  30 */   private List<HrmLeaveTypeColor> leaveTypeList = null;
/*  31 */   private HrmScheduleManager manager = null;
/*  32 */   protected HrmLeaveTypeColorManager leaveTypeColorManager = null;
/*     */   
/*     */   public HrmScheduleDiffOtherManager() {
/*  35 */     RecordSet recordSet = new RecordSet();
/*  36 */     this.typeManager = new HrmLeaveTypeColorManager();
/*  37 */     this.manager = new HrmScheduleManager();
/*  38 */     this.manager.setForSchedule(true);
/*  39 */     this.leaveTypeColorManager = new HrmLeaveTypeColorManager();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void initLeaveTypeList(int paramInt) {
/*  47 */     if (paramInt == 0) this.leaveTypeList = this.typeManager.find("[map]subcompanyid:0;"); 
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  51 */     String str1 = paramMap.get("fromDate");
/*  52 */     String str2 = paramMap.get("toDate");
/*  53 */     int i = StringUtil.parseToInt(paramMap.get("subCompanyId"));
/*  54 */     int j = StringUtil.parseToInt(paramMap.get("departmentId"));
/*  55 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  56 */     String str4 = StringUtil.vString(paramMap.get("status"));
/*  57 */     int k = StringUtil.parseToInt(paramMap.get("type"), 4);
/*  58 */     boolean bool1 = Boolean.valueOf(StringUtil.vString(paramMap.get("arg1"))).booleanValue();
/*  59 */     boolean bool2 = Boolean.valueOf(StringUtil.vString(paramMap.get("arg2"))).booleanValue();
/*  60 */     String str5 = StringUtil.vString(paramMap.get("leaveType"));
/*  61 */     return getScheduleList(paramUser, str1, str2, i, j, str3, k, bool1, bool2, str5, str4);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, String paramString4) {
/*  65 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, false, false, paramString4);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, int paramInt4) {
/*  69 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, false, false, paramInt4, "");
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, boolean paramBoolean1, boolean paramBoolean2, String paramString4) {
/*  73 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, paramBoolean1, paramBoolean2, 0, paramString4);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, boolean paramBoolean1, boolean paramBoolean2, String paramString4, String paramString5) {
/*  77 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, paramBoolean1, paramBoolean2, 0, paramString4, paramString5);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, boolean paramBoolean1, boolean paramBoolean2, int paramInt4, String paramString4) {
/*  81 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, paramBoolean1, paramBoolean2, paramInt4, "", paramString4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, boolean paramBoolean1, boolean paramBoolean2, int paramInt4, String paramString4, String paramString5) {
/* 100 */     initLeaveTypeList(paramInt3);
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     if (paramUser != null) this.languageId = paramUser.getLanguage(); 
/* 103 */     boolean bool = (paramInt4 > 0 && paramInt3 == 0) ? true : false;
/* 104 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 105 */     String str1 = "fromDate", str2 = "fromTime", str3 = "toDate", str4 = "toTime";
/* 106 */     switch (paramInt3) {
/*     */       case 0:
/* 108 */         if (paramInt4 > 0) hashMap1.put("newLeaveType", " and t.newLeaveType <= " + paramInt4); 
/* 109 */         if (StringUtil.isNotNull(paramString4)) hashMap1.put("newLeaveType", " and t.newLeaveType = " + StringUtil.parseToInt(paramString4, 0)); 
/*     */         break;
/*     */       case 3:
/* 112 */         str1 = "fromdate";
/* 113 */         str2 = "fromtime";
/* 114 */         str3 = "tilldate";
/* 115 */         str4 = "tilltime";
/* 116 */         hashMap1.put("oType", " and t.otype in ('0', '1')");
/*     */         break;
/*     */     } 
/* 119 */     String str5 = " and (t." + str1 + " between '" + paramString1 + "' and '" + paramString2 + "' or t." + str3 + " between '" + paramString1 + "' and '" + paramString2 + "' or '" + paramString1 + "' between t." + str1 + " and t." + str3 + " or '" + paramString2 + "' between t." + str1 + " and t." + str3 + ")";
/* 120 */     if (StringUtil.isNotNull(paramString1) && StringUtil.isNotNull(paramString2)) { hashMap1.put("fromDate", str5); }
/* 121 */     else if (StringUtil.isNotNull(paramString1)) { hashMap1.put("fromDate", " and (t." + str1 + " >= '" + paramString1 + "' or '" + paramString1 + "' between t." + str1 + " and t." + str3 + ")"); }
/* 122 */     else if (StringUtil.isNotNull(paramString2)) { hashMap1.put("toDate", " and (t." + str3 + " <= '" + paramString2 + "' or '" + paramString2 + "' between t." + str1 + " and t." + str3 + ")"); }
/* 123 */      if (paramInt2 > 0) hashMap1.put("departmentId", " and t.resourceId in  (select id from hrmresource where departmentid = " + paramInt2 + ")"); 
/* 124 */     if (paramInt1 > 0) hashMap1.put("subCompanyId", " and t.resourceId in  (select id from hrmresource where subcompanyid1 = " + paramInt1 + ")"); 
/* 125 */     if (StringUtil.isNotNull(paramString3)) hashMap1.put("resourceId", " and t.resourceId in (" + paramString3 + ")"); 
/* 126 */     if (StringUtil.isNotNull(paramString5) && !paramString5.equals("8") && !paramString5.equals("9")) {
/* 127 */       hashMap1.put("status", " and c.status=" + paramString5);
/* 128 */     } else if (paramString5.equals("8") || paramString5.equals("")) {
/* 129 */       hashMap1.put("status", " and c.status in (0,1,2,3)");
/*     */     } 
/* 131 */     String str6 = (new HrmAttProcSetManager()).getSQLByField006(paramInt3, hashMap1, paramBoolean1, paramBoolean2);
/* 132 */     recordSet.executeSql(str6);
/* 133 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 134 */     HashMap<Object, Object> hashMap2 = null;
/* 135 */     String str7 = "", str8 = "", str9 = "", str10 = "", str11 = "", str12 = "", str13 = "", str14 = "", str15 = "";
/* 136 */     if (bool) {
/* 137 */       String str = "";
/* 138 */       hashMap2 = new HashMap<>();
/* 139 */       while (recordSet.next()) {
/* 140 */         str = StringUtil.vString(recordSet.getString("newLeaveType"));
/* 141 */         if (!hashMap2.containsKey(str)) hashMap2.put(str, str); 
/*     */       } 
/* 143 */       arrayList.add(hashMap2);
/*     */     } else {
/* 145 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 146 */       while (recordSet.next()) {
/* 147 */         hashMap2 = new HashMap<>();
/* 148 */         str7 = StringUtil.vString(recordSet.getString("requestid"));
/* 149 */         hashMap2.put("requestId", str7);
/* 150 */         str8 = StringUtil.vString(recordSet.getString("requestname"));
/* 151 */         str11 = StringUtil.vString(recordSet.getString("requestname"));
/* 152 */         str9 = "<a href=javaScript:openFullWindowHaveBarForWFList('/workflow/request/ViewRequestForwardSPA.jsp?requestid=" + str7 + "&isovertime=0'," + str7 + ");>" + str8 + "</a>";
/* 153 */         hashMap2.put("requestName", str8);
/* 154 */         hashMap2.put("requestName4E9", str9);
/* 155 */         str10 = StringUtil.vString(recordSet.getString("resourceId"));
/* 156 */         hashMap2.put("resourceId", str10);
/* 157 */         hashMap2.put("workcode", StringUtil.vString(recordSet.getString("workcode")));
/* 158 */         hashMap2.put("resourceName", StringUtil.vString(recordSet.getString("lastname")));
/* 159 */         hashMap2.put("departmentName", StringUtil.vString(recordSet.getString("departmentname")));
/* 160 */         str12 = StringUtil.vString(recordSet.getString(str1));
/* 161 */         str13 = StringUtil.vString(recordSet.getString(str2));
/* 162 */         hashMap2.put("fromDate", str12);
/* 163 */         hashMap2.put("fromTime", str13);
/* 164 */         str14 = StringUtil.vString(recordSet.getString(str3));
/* 165 */         str15 = StringUtil.vString(recordSet.getString(str4));
/* 166 */         hashMap2.put("toDate", str14);
/* 167 */         hashMap2.put("toTime", str15);
/* 168 */         hashMap2.put("startTime", str12 + "/" + str13);
/* 169 */         hashMap2.put("endTime", str14 + "/" + str15);
/* 170 */         hashMap2.put("status", StringUtil.vString(recordSet.getString("status")));
/* 171 */         User user = null;
/* 172 */         if (hashMap.containsKey(str10)) {
/* 173 */           user = (User)hashMap.get(str10);
/*     */         } else {
/* 175 */           user = this.manager.getUser(StringUtil.parseToInt(str10));
/* 176 */           hashMap.put(str10, user);
/*     */         } 
/* 178 */         if (paramInt3 == 0) {
/* 179 */           this.leaveTypeColorManager = (this.leaveTypeColorManager == null) ? new HrmLeaveTypeColorManager() : this.leaveTypeColorManager;
/* 180 */           HrmLeaveTypeColor hrmLeaveTypeColor = (HrmLeaveTypeColor)this.leaveTypeColorManager.get(this.leaveTypeColorManager.getMapParam("field004:" + recordSet.getString("newLeaveType")));
/* 181 */           hrmLeaveTypeColor = (hrmLeaveTypeColor == null) ? new HrmLeaveTypeColor() : hrmLeaveTypeColor;
/* 182 */           this.manager.setIsCalWorkDay(hrmLeaveTypeColor.getIsCalWorkDay().intValue());
/* 183 */           this.manager.setRelateweekday(hrmLeaveTypeColor.getRelateweekday().intValue());
/* 184 */           hashMap2.put("leaveDays", this.manager.getTotalWorkDays(str12, str13, str14, str15, paramInt1, user, true));
/* 185 */           hashMap2.put("WFleaveDays", StringUtil.vString(recordSet.getString("leaveDays")));
/* 186 */           hashMap2.put("newLeaveType", recordSet.getString("newLeaveType"));
/* 187 */           initLeave((Map)hashMap2, recordSet.getInt("newLeaveType"));
/* 188 */         } else if (paramInt3 == 1 || paramInt3 == 2) {
/* 189 */           this.manager.setIsCalWorkDay(1);
/* 190 */           this.manager.setRelateweekday(2);
/* 191 */           this.manager.setScheduleUnit(0);
/* 192 */           hashMap2.put("days", this.manager.getTotalWorkDays(str12, str13, str14, str15, paramInt1, user, true));
/* 193 */         } else if (paramInt3 == 3 || paramInt3 == 4) {
/* 194 */           this.manager.setIsCalWorkDay(1);
/* 195 */           this.manager.setRelateweekday(2);
/* 196 */           this.manager.setScheduleUnit(0);
/* 197 */           str11 = StringUtil.vString(recordSet.getString("requestname"));
/* 198 */           str8 = "<a href=javaScript:openFullWindowHaveBarForWFList('/workflow/request/ViewRequest.jsp?requestid=" + str7 + "&isovertime=0'," + str7 + ");doReadIt(" + str7 + ",\"\",this);>" + str11 + "</a>";
/* 199 */           hashMap2.put("requestName", str8);
/* 200 */           hashMap2.put("outName", str11);
/* 201 */           if (paramInt3 == 3) {
/* 202 */             String str = this.manager.getTotalRestHours(str12, str13, str14, str15, paramInt1, user, true);
/* 203 */             hashMap2.put("days", String.valueOf(StringUtil.round(StringUtil.parseToDouble(str, 0.0D), 2)));
/* 204 */           } else if (paramInt3 == 4) {
/* 205 */             hashMap2.put("days", this.manager.getTotalWorkDays(str12, str13, str14, str15, paramInt1, user, true));
/*     */           } 
/*     */         } 
/* 208 */         arrayList.add(hashMap2);
/*     */       } 
/*     */     } 
/* 211 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private void initLeave(Map<String, String> paramMap, int paramInt) {
/* 215 */     String str = "";
/* 216 */     if (this.leaveTypeList != null)
/* 217 */       for (HrmLeaveTypeColor hrmLeaveTypeColor : this.leaveTypeList) {
/* 218 */         if (hrmLeaveTypeColor.getField004().intValue() == paramInt) {
/* 219 */           switch (this.languageId) {
/*     */             case 7:
/* 221 */               str = hrmLeaveTypeColor.getField007();
/*     */               break;
/*     */             case 8:
/* 224 */               str = hrmLeaveTypeColor.getField008();
/*     */               break;
/*     */             case 9:
/* 227 */               str = hrmLeaveTypeColor.getField009();
/*     */               break;
/*     */           } 
/* 230 */           str = StringUtil.vString(str, hrmLeaveTypeColor.getField001());
/* 231 */           paramMap.put("leaveType", str);
/* 232 */           paramMap.put("leaveColor", hrmLeaveTypeColor.getColor());
/*     */           return;
/*     */         } 
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffOtherManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */