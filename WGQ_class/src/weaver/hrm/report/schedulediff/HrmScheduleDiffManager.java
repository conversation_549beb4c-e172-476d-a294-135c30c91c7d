/*     */ package weaver.hrm.report.schedulediff;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmAttFlowType;
/*     */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*     */ import weaver.hrm.attendance.domain.ScheduleApplicationRule;
/*     */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*     */ import weaver.hrm.attendance.manager.HrmScheduleApplicationRuleManager;
/*     */ import weaver.hrm.report.domain.HrmReport;
/*     */ import weaver.hrm.report.manager.HrmReportManager;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffManager
/*     */   extends HrmReportManager
/*     */ {
/*  33 */   private HrmReportManager reportManager = null;
/*     */   
/*     */   public HrmScheduleDiffManager() {
/*  36 */     this((User)null);
/*     */   }
/*     */   
/*     */   public HrmScheduleDiffManager(User paramUser) {
/*  40 */     super(paramUser, HrmReportManager.SignType.DIFF_REPORT);
/*     */   }
/*     */   
/*     */   public int getSubCompanyId() {
/*  44 */     return this.bean.getSubCompanyId();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getTotalWorkingDays(String paramString1, String paramString2) {
/*  51 */     if (StringUtil.isNull(new String[] { paramString1, paramString2 }) || paramString1.compareTo(paramString2) > 0) return 0; 
/*  52 */     Map map = this.manager.isWorkday(paramString1, paramString2, this.manager.getSubCompanyId());
/*  53 */     boolean bool = false;
/*  54 */     byte b = 0;
/*  55 */     for (String str = paramString1; !bool; ) {
/*  56 */       if (str.equals(paramString2)) bool = true;
/*     */       
/*  58 */       Boolean bool1 = (Boolean)map.get(str);
/*  59 */       if (bool1 != null && bool1.booleanValue()) b++;
/*     */       
/*  61 */       str = DateUtil.addDate(str, 1);
/*     */     } 
/*  63 */     return b;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void updateData() {
/*  68 */     updateDataOfBeLate();
/*     */     
/*  70 */     updateDataOfLeaveEarly();
/*     */     
/*  72 */     updateDataOfAbsentFromWork();
/*     */     
/*  74 */     updateDataOfNoSign();
/*     */     
/*  76 */     updateDataForAttFlow(this.leaveMap, HrmAttFlowType.LEAVE);
/*     */     
/*  78 */     updateDataForAttFlow(this.evectionMap, HrmAttFlowType.EVECTION);
/*     */     
/*  80 */     updateDataForAttFlow(this.outMap, HrmAttFlowType.OUT);
/*     */     
/*  82 */     updateDataForAttFlow(this.otherMap, HrmAttFlowType.OTHER);
/*     */     
/*  84 */     updateDataForAttFlow(this.overTimeMap, HrmAttFlowType.OVERTIME);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfBeLate() {
/*  91 */     setReportManager(new HrmScheduleDiffDetBeLateManager());
/*  92 */     List<Map<String, Object>> list = getReportList();
/*  93 */     HrmScheduleApplicationRuleManager hrmScheduleApplicationRuleManager = new HrmScheduleApplicationRuleManager();
/*  94 */     List<ScheduleApplicationRule> list1 = hrmScheduleApplicationRuleManager.find("[map]sharetype:1");
/*  95 */     for (Map<String, Object> map : list) {
/*  96 */       String str = StringUtil.vString(map.get("resourceId"));
/*  97 */       int i = StringUtil.parseToInt((String)this.indexMap.get(str), -1);
/*  98 */       if (i >= 0) {
/*  99 */         Map<String, Object> map1 = this.personList.get(i);
/*     */         
/* 101 */         String str1 = StringUtil.vString(map.get("signTime"));
/* 102 */         String str2 = StringUtil.vString(map.get(HrmReportManager.SignType.BE_LATE + "_realHours"));
/*     */         
/* 104 */         double d = StringUtil.parseToDouble(str2);
/*     */         
/* 106 */         putScheduleRuleValue(map1, list1, (long)(d * 60.0D), "beLate");
/* 107 */         resetValue(map1, "beLate");
/*     */         
/* 109 */         if (str1.compareTo("09:00:00") < 0) {
/* 110 */           resetValue(map1, "beLateA"); continue;
/*     */         } 
/* 112 */         resetValue(map1, "beLateB");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void putScheduleRuleValue(Map<String, Object> paramMap, List<ScheduleApplicationRule> paramList, long paramLong, String paramString) {
/* 126 */     for (ScheduleApplicationRule scheduleApplicationRule : paramList) {
/* 127 */       String str = StringUtil.vString(scheduleApplicationRule.getId());
/*     */ 
/*     */       
/* 130 */       long l1 = StringUtil.parseToLong(scheduleApplicationRule.getSeclevel()) * 60L;
/*     */       
/* 132 */       long l2 = StringUtil.parseToLong(scheduleApplicationRule.getSeclevelend()) * 60L + 59L;
/* 133 */       if (paramLong >= l1 && paramLong <= l2) {
/* 134 */         resetValue(paramMap, paramString + str);
/*     */         break;
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void resetValue(Map<String, Object> paramMap, String paramString) {
/* 141 */     resetValue(paramMap, paramString, 1);
/*     */   }
/*     */   
/*     */   private void resetValue(Map<String, Object> paramMap, String paramString, int paramInt) {
/* 145 */     int i = StringUtil.parseToInt((String)paramMap.get(paramString), 0);
/* 146 */     i += paramInt;
/* 147 */     paramMap.put(paramString, String.valueOf(i));
/*     */   }
/*     */   
/*     */   private void resetValue(Map<String, Object> paramMap, String paramString, double paramDouble) {
/* 151 */     double d = StringUtil.parseToDouble((String)paramMap.get(paramString), 0.0D);
/* 152 */     d += paramDouble;
/* 153 */     paramMap.put(paramString, String.valueOf(StringUtil.round(d)));
/*     */   }
/*     */   
/*     */   private void setReportManager(HrmReportManager paramHrmReportManager) {
/* 157 */     paramHrmReportManager.setUser(this.user);
/* 158 */     paramHrmReportManager.setPersonList(this.personList);
/* 159 */     paramHrmReportManager.setIndexMap(this.indexMap);
/* 160 */     this.reportManager = paramHrmReportManager;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getReportList() {
/* 169 */     return (this.reportManager == null) ? new ArrayList<>() : this.reportManager.getScheduleList(this.bean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfLeaveEarly() {
/* 176 */     setReportManager(new HrmScheduleDiffDetLeaveEarlyManager());
/* 177 */     List<Map<String, Object>> list = getReportList();
/* 178 */     HrmScheduleApplicationRuleManager hrmScheduleApplicationRuleManager = new HrmScheduleApplicationRuleManager();
/* 179 */     List<ScheduleApplicationRule> list1 = hrmScheduleApplicationRuleManager.find("[map]sharetype:2");
/* 180 */     for (Map<String, Object> map : list) {
/* 181 */       String str = StringUtil.vString(map.get("resourceId"));
/* 182 */       int i = StringUtil.parseToInt((String)this.indexMap.get(str), -1);
/* 183 */       if (i >= 0) {
/* 184 */         String str1 = StringUtil.vString(map.get(HrmReportManager.SignType.LEAVE_EARLY + "_realHours"));
/*     */         
/* 186 */         double d = StringUtil.parseToDouble(str1);
/* 187 */         Map<String, Object> map1 = this.personList.get(i);
/*     */         
/* 189 */         putScheduleRuleValue(map1, list1, (long)(d * 60.0D), "leaveEarly");
/* 190 */         resetValue(map1, "leaveEarly");
/*     */         
/* 192 */         if (StringUtil.vString(map.get("signTime")).compareTo("17:00:00") > 0) {
/* 193 */           resetValue(map1, "leaveEarlyA"); continue;
/*     */         } 
/* 195 */         resetValue(map1, "leaveEarlyB");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfAbsentFromWork() {
/* 206 */     setReportManager(new HrmScheduleDiffDetAbsentFromWorkManager());
/* 207 */     List<Map<String, Object>> list = getReportList();
/* 208 */     for (Map<String, Object> map : list) {
/* 209 */       String str = StringUtil.vString(map.get("resourceId"));
/* 210 */       List list1 = (List)map.get("fDates");
/* 211 */       List list2 = (List)map.get("sDates");
/*     */       
/* 213 */       int i = StringUtil.parseToInt((String)this.indexMap.get(str), -1);
/* 214 */       if (i >= 0) {
/* 215 */         Map<String, Object> map1 = this.personList.get(i);
/* 216 */         double d = StringUtil.parseToDouble(map.get(HrmReportManager.SignType.ABSENT + "_realHours"));
/* 217 */         resetValue(map1, "absentFromWork");
/* 218 */         if (d > 0.0D) {
/* 219 */           resetValue(map1, "absentFromWorkHours", d);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfNoSign() {
/* 229 */     setReportManager(new HrmScheduleDiffDetNoSignManager());
/* 230 */     List<Map<String, Object>> list = getReportList();
/* 231 */     for (Map<String, Object> map : list) {
/* 232 */       String str = StringUtil.vString(map.get("resourceId"));
/* 233 */       int i = StringUtil.parseToInt((String)this.indexMap.get(str), -1);
/* 234 */       if (i >= 0) resetValue(this.personList.get(i), "noSign");
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private HrmScheduleSetDetail getDetailBean(List<HrmScheduleSetDetail> paramList, String paramString) {
/* 245 */     HrmScheduleSetDetail hrmScheduleSetDetail = null;
/* 246 */     for (HrmScheduleSetDetail hrmScheduleSetDetail1 : paramList) {
/* 247 */       if (hrmScheduleSetDetail1.getField003().equals(paramString)) {
/* 248 */         hrmScheduleSetDetail = hrmScheduleSetDetail1;
/*     */         break;
/*     */       } 
/*     */     } 
/* 252 */     return hrmScheduleSetDetail;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataForAttFlow(Map<String, List<HrmReport>> paramMap, HrmAttFlowType paramHrmAttFlowType) {
/* 262 */     String str1 = this.bean.getFromDate();
/* 263 */     String str2 = this.bean.getToDate();
/* 264 */     Iterator<Map.Entry> iterator = paramMap.entrySet().iterator();
/* 265 */     while (iterator.hasNext()) {
/* 266 */       Map.Entry entry = iterator.next();
/* 267 */       String str3 = StringUtil.vString(entry.getKey());
/* 268 */       List list = (List)entry.getValue();
/* 269 */       int i = StringUtil.parseToInt((String)this.indexMap.get(str3), -1);
/* 270 */       if (i < 0 || list == null)
/*     */         continue; 
/* 272 */       Map<String, Object> map = this.personList.get(i);
/* 273 */       List<HrmScheduleSetDetail> list1 = (List)map.get("detailList");
/* 274 */       if (list1 == null)
/* 275 */         continue;  HrmScheduleSetDetail hrmScheduleSetDetail = null;
/* 276 */       String str4 = "", str5 = "", str6 = "", str7 = "";
/* 277 */       for (HrmReport hrmReport : list) {
/* 278 */         str4 = hrmReport.getFromDate();
/* 279 */         str5 = hrmReport.getFromTime();
/* 280 */         str6 = hrmReport.getToDate();
/* 281 */         str7 = hrmReport.getToTime();
/* 282 */         if (str4.compareTo(str1) < 0) {
/* 283 */           str4 = str1;
/* 284 */           str5 = "00:00";
/*     */         } 
/* 286 */         if (str6.compareTo(str2) > 0) {
/* 287 */           String str = "";
/* 288 */           if ((hrmScheduleSetDetail = getDetailBean(list1, str4)) != null) {
/* 289 */             List list2 = this.manager.timeToList(hrmScheduleSetDetail.getWorkTime());
/* 290 */             for (String[] arrayOfString : list2) {
/* 291 */               if (arrayOfString[0].compareTo(arrayOfString[1]) > 0) {
/* 292 */                 str = (str7.compareTo(arrayOfString[1]) > 0) ? arrayOfString[1] : str7;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 297 */           str6 = DateUtil.addDate(str2, 1);
/* 298 */           str7 = StringUtil.isNull(str) ? "00:00" : str;
/*     */         } 
/* 300 */         putAttFlowValue(paramHrmAttFlowType, hrmReport, map, str4, str5, str6, str7);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void putAttFlowValue(HrmAttFlowType paramHrmAttFlowType, HrmReport paramHrmReport, Map<String, Object> paramMap, String paramString1, String paramString2, String paramString3, String paramString4) {
/* 316 */     String str1 = paramHrmAttFlowType.getName();
/* 317 */     String str2 = "";
/* 318 */     int i = this.bean.getSubCompanyId();
/* 319 */     User user = this.manager.getUser(paramHrmReport.getResourceId());
/* 320 */     switch (paramHrmAttFlowType.getType()) {
/*     */       case 3:
/* 322 */         str2 = this.manager.getTotalRestHours(paramString1, paramString2, paramString3, paramString4, i, user);
/* 323 */         str1 = str1 + StringUtil.vString(Integer.valueOf(paramHrmReport.getOtype()));
/*     */         break;
/*     */       case 0:
/* 326 */         str1 = str1 + StringUtil.vString(Integer.valueOf(paramHrmReport.getNewLeaveType()));
/*     */       default:
/* 328 */         if (paramHrmAttFlowType.getType() == 0) {
/* 329 */           this.leaveTypeColorManager = (this.leaveTypeColorManager == null) ? new HrmLeaveTypeColorManager() : this.leaveTypeColorManager;
/* 330 */           HrmLeaveTypeColor hrmLeaveTypeColor = (HrmLeaveTypeColor)this.leaveTypeColorManager.get(this.leaveTypeColorManager.getMapParam("field004:" + paramHrmReport.getNewLeaveType()));
/* 331 */           hrmLeaveTypeColor = (hrmLeaveTypeColor == null) ? new HrmLeaveTypeColor() : hrmLeaveTypeColor;
/* 332 */           this.manager.setIsCalWorkDay(hrmLeaveTypeColor.getIsCalWorkDay().intValue());
/* 333 */           this.manager.setRelateweekday(hrmLeaveTypeColor.getRelateweekday().intValue());
/*     */         } else {
/* 335 */           this.manager.setIsCalWorkDay(1);
/* 336 */           this.manager.setRelateweekday(2);
/* 337 */           this.manager.setScheduleUnit(0);
/*     */         } 
/* 339 */         str2 = this.manager.getTotalWorkDays(paramString1, paramString2, paramString3, paramString4, i, user);
/*     */         break;
/*     */     } 
/* 342 */     resetValue(paramMap, str1, StringUtil.parseToDouble(str2, 0.0D));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */