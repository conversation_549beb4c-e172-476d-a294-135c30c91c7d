/*    */ package weaver.hrm.report.schedulediff;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Iterator;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.common.DateUtil;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.report.manager.HrmReportManager;
/*    */ import weaver.hrm.schedule.domain.HrmSchedule;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleDate;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleDiffDetAbsentFromWorkManager
/*    */   extends HrmReportManager
/*    */ {
/*    */   public HrmScheduleDiffDetAbsentFromWorkManager() {
/* 32 */     this((User)null);
/*    */   }
/*    */   
/*    */   public HrmScheduleDiffDetAbsentFromWorkManager(User paramUser) {
/* 36 */     super(paramUser, HrmReportManager.SignType.ABSENT);
/*    */   }
/*    */   
/*    */   public List getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 40 */     setLanId((paramUser == null) ? 7 : paramUser.getLanguage());
/* 41 */     return getScheduleList(paramMap);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   protected List<Map<String, Object>> getScheduleList(List<Map<String, Object>> paramList, Map<String, Map<String, List<HrmSchedule>>> paramMap) {
/* 49 */     if (paramList == null) {
/* 50 */       paramList = new ArrayList<>();
/*    */     } else {
/* 52 */       paramList.clear();
/*    */     } 
/* 54 */     Map map = null;
/* 55 */     for (Map map1 : this.personList) {
/* 56 */       if ((map = (Map)map1.get("dateMap")) == null)
/* 57 */         continue;  String str = StringUtil.vString(map1.get("resourceId"));
/* 58 */       Map<String, List<HrmSchedule>> map2 = paramMap.get(str);
/* 59 */       HrmScheduleDate hrmScheduleDate = null;
/* 60 */       Iterator<Map.Entry> iterator = map.entrySet().iterator();
/* 61 */       while (iterator.hasNext()) {
/* 62 */         Map.Entry entry = iterator.next();
/* 63 */         String str1 = StringUtil.vString(entry.getKey());
/* 64 */         if ((hrmScheduleDate = (HrmScheduleDate)entry.getValue()) == null || !hrmScheduleDate.isWorkDay())
/* 65 */           continue;  HrmSchedule hrmSchedule = new HrmSchedule();
/* 66 */         hrmSchedule.setSignDate(str1);
/* 67 */         initHrmSchedule(map1, hrmSchedule);
/*    */         
/* 69 */         addValue(paramList, getListByCurrentDate(map2, str1, hrmScheduleDate), hrmSchedule, hrmScheduleDate);
/*    */       } 
/*    */     } 
/* 72 */     return paramList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private List<HrmSchedule> getListByCurrentDate(Map<String, List<HrmSchedule>> paramMap, String paramString, HrmScheduleDate paramHrmScheduleDate) {
/* 83 */     if (paramMap == null) return new ArrayList<>();
/*    */     
/* 85 */     List<HrmSchedule> list = paramMap.get(paramString);
/* 86 */     boolean bool = false;
/* 87 */     if (paramHrmScheduleDate.isSchedulePerson()) {
/* 88 */       String[] arrayOfString = this.manager.getAcrossDateTime(paramHrmScheduleDate.getDBean().getSignTime());
/* 89 */       if (arrayOfString != null && arrayOfString.length == 2) {
/* 90 */         bool = true;
/*    */       }
/*    */     } 
/* 93 */     String str = DateUtil.addDate(paramString, 1);
/* 94 */     list = (list == null) ? new ArrayList() : list;
/* 95 */     if (bool) {
/* 96 */       List list1 = paramMap.get(str);
/* 97 */       if (list1 != null) list.addAll(list1); 
/*    */     } 
/* 99 */     return list;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffDetAbsentFromWorkManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */