/*     */ package weaver.hrm.report.schedulediff;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.report.manager.HrmReportManager;
/*     */ import weaver.hrm.schedule.domain.HrmSchedule;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleDate;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffDetNoSignManager
/*     */   extends HrmReportManager
/*     */ {
/*     */   public HrmScheduleDiffDetNoSignManager() {
/*  32 */     this((User)null);
/*     */   }
/*     */   
/*     */   public HrmScheduleDiffDetNoSignManager(User paramUser) {
/*  36 */     super(paramUser, HrmReportManager.SignType.NO_SIGN);
/*     */   }
/*     */   
/*     */   public List getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  40 */     setLanId((paramUser == null) ? 7 : paramUser.getLanguage());
/*  41 */     return getScheduleList(paramMap);
/*     */   }
/*     */ 
/*     */   
/*     */   protected List<Map<String, Object>> getScheduleList(List<Map<String, Object>> paramList, Map<String, Map<String, List<HrmSchedule>>> paramMap) {
/*  46 */     if (paramList == null) {
/*  47 */       paramList = new ArrayList<>();
/*     */     } else {
/*  49 */       paramList.clear();
/*     */     } 
/*     */     
/*  52 */     List<Map<String, Object>> list = getSignOutList();
/*  53 */     Map map = null;
/*  54 */     for (Map map1 : this.personList) {
/*  55 */       if ((map = (Map)map1.get("dateMap")) == null)
/*  56 */         continue;  String str1 = StringUtil.vString(map1.get("resourceId"));
/*  57 */       Map map2 = paramMap.get(str1);
/*  58 */       HrmScheduleDate hrmScheduleDate = null;
/*  59 */       Iterator<Map.Entry> iterator = map.entrySet().iterator();
/*  60 */       String str2 = "";
/*  61 */       while (iterator.hasNext()) {
/*  62 */         Map.Entry entry = iterator.next();
/*  63 */         String str = StringUtil.vString(entry.getKey());
/*  64 */         if ((hrmScheduleDate = (HrmScheduleDate)entry.getValue()) == null || !hrmScheduleDate.isWorkDay())
/*  65 */           continue;  str2 = DateUtil.addDate(str, 1);
/*     */         
/*  67 */         HrmSchedule hrmSchedule = new HrmSchedule();
/*  68 */         hrmSchedule.setSignDate(str);
/*  69 */         initHrmSchedule(map1, hrmSchedule);
/*     */         
/*  71 */         List list1 = null, list2 = null;
/*  72 */         if (map2 != null) {
/*  73 */           list1 = (List)map2.get(str);
/*  74 */           list2 = (List)map2.get(str2);
/*  75 */           if (list1 == null) list1 = new ArrayList(); 
/*  76 */           if (list2 == null) list2 = new ArrayList(); 
/*  77 */           list1.addAll(list2);
/*     */         } 
/*     */         
/*  80 */         addValue(paramList, list1, getListByCurrentDate(str1, list, str, hrmScheduleDate), hrmSchedule, hrmScheduleDate);
/*     */       } 
/*     */     } 
/*  83 */     return paramList;
/*     */   }
/*     */   
/*     */   private List getSignOutList() {
/*  87 */     HrmScheduleDiffDetSignOutManager hrmScheduleDiffDetSignOutManager = new HrmScheduleDiffDetSignOutManager(this.user);
/*  88 */     hrmScheduleDiffDetSignOutManager.setPersonList(this.personList);
/*  89 */     hrmScheduleDiffDetSignOutManager.setIndexMap(this.indexMap);
/*  90 */     return hrmScheduleDiffDetSignOutManager.getScheduleList(this.bean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getListByCurrentDate(String paramString1, List<Map<String, Object>> paramList, String paramString2, HrmScheduleDate paramHrmScheduleDate) {
/* 102 */     boolean bool = false;
/* 103 */     String str1 = "";
/* 104 */     if (paramHrmScheduleDate.isSchedulePerson()) {
/* 105 */       String[] arrayOfString = this.manager.getAcrossDateTime(paramHrmScheduleDate.getDBean().getSignTime());
/* 106 */       if (arrayOfString != null && arrayOfString.length == 2) {
/* 107 */         bool = true;
/* 108 */         str1 = arrayOfString[1] + ":00";
/*     */       } 
/*     */     } 
/* 111 */     String str2 = "", str3 = "", str4 = "", str5 = DateUtil.addDate(paramString2, 1);
/* 112 */     ArrayList<Map> arrayList = new ArrayList();
/* 113 */     for (Map<String, Object> map : paramList) {
/* 114 */       str4 = StringUtil.vString(map.get("resourceId"));
/* 115 */       str2 = StringUtil.vString(map.get("signDate"));
/* 116 */       str3 = StringUtil.vString(map.get("signTime"));
/* 117 */       if (paramString1.equals(StringUtil.vString(map.get("resourceId"))) && (
/* 118 */         paramString2.equals(str2) || (bool && str5.equals(str2) && str3.compareTo(str1) <= 0))) {
/* 119 */         arrayList.add(map);
/*     */       }
/*     */     } 
/* 122 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffDetNoSignManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */