/*    */ package weaver.hrm.report.schedulediff;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.attendance.domain.ScheduleApplicationRule;
/*    */ import weaver.hrm.attendance.manager.HrmScheduleApplicationRuleManager;
/*    */ import weaver.hrm.report.manager.HrmReportManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleDiffDetLeaveEarlyManager
/*    */   extends HrmReportManager
/*    */ {
/*    */   public HrmScheduleDiffDetLeaveEarlyManager() {
/* 30 */     this((User)null);
/*    */   }
/*    */   
/*    */   public HrmScheduleDiffDetLeaveEarlyManager(User paramUser) {
/* 34 */     super(paramUser, HrmReportManager.SignType.LEAVE_EARLY);
/*    */   }
/*    */   
/*    */   public List getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 38 */     setLanId((paramUser == null) ? 7 : paramUser.getLanguage());
/* 39 */     String str1 = StringUtil.vString(paramMap.get("isRule"));
/* 40 */     String str2 = StringUtil.vString(paramMap.get("ruleid"));
/* 41 */     HrmScheduleApplicationRuleManager hrmScheduleApplicationRuleManager = new HrmScheduleApplicationRuleManager();
/*    */     
/* 43 */     List list1 = new ArrayList();
/* 44 */     List list2 = getScheduleList(paramMap);
/*    */     
/* 46 */     if ("1".equals(str1)) {
/* 47 */       ScheduleApplicationRule scheduleApplicationRule = (ScheduleApplicationRule)hrmScheduleApplicationRuleManager.get(str2);
/* 48 */       for (Map map : list2) {
/* 49 */         String str = StringUtil.vString(map.get("resourceId"));
/* 50 */         int i = StringUtil.parseToInt((String)this.indexMap.get(str), -1);
/* 51 */         if (i >= 0) {
/* 52 */           boolean bool = false;
/* 53 */           Map map1 = this.personList.get(i);
/* 54 */           String str3 = StringUtil.vString(map.get(HrmReportManager.SignType.LEAVE_EARLY + "_realHours"));
/*    */           
/* 56 */           double d = StringUtil.parseToDouble(str3);
/* 57 */           bool = checkIsinRule(map1, scheduleApplicationRule, (long)(d * 60.0D));
/* 58 */           if (bool) {
/* 59 */             list1.add(map);
/*    */           }
/*    */         } 
/*    */       } 
/*    */     } else {
/* 64 */       list1 = list2;
/*    */     } 
/* 66 */     return list1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffDetLeaveEarlyManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */