/*    */ package weaver.hrm.report.schedulediff;
/*    */ 
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.report.manager.HrmReportManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleDiffDetSignOutManager
/*    */   extends HrmReportManager
/*    */ {
/*    */   public HrmScheduleDiffDetSignOutManager() {
/* 24 */     this((User)null);
/*    */   }
/*    */   
/*    */   public HrmScheduleDiffDetSignOutManager(User paramUser) {
/* 28 */     super(paramUser, HrmReportManager.SignType.SIGN_OUT);
/*    */   }
/*    */   
/*    */   public List getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 32 */     setLanId((paramUser == null) ? 7 : paramUser.getLanguage());
/* 33 */     return getScheduleList(paramMap);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffDetSignOutManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */