/*     */ package weaver.hrm.report.schedulediff;
/*     */ 
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffUtil
/*     */   extends BaseBean
/*     */ {
/*     */   private User user;
/*  29 */   private int scale = 2;
/*     */   
/*  31 */   private HrmScheduleManager manager = null;
/*     */   private boolean isForSchedule = false;
/*     */   
/*     */   public HrmScheduleDiffUtil() {
/*  35 */     this(null);
/*     */   }
/*     */   
/*     */   public HrmScheduleDiffUtil(User paramUser) {
/*  39 */     this.manager = new HrmScheduleManager();
/*  40 */     setUser(paramUser);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  49 */     this.user = paramUser;
/*  50 */     if (this.manager == null) {
/*  51 */       this.manager = new HrmScheduleManager();
/*     */     }
/*  53 */     if (this.manager.getUser() == null) {
/*  54 */       this.manager.setUser(paramUser);
/*     */     }
/*     */   }
/*     */   
/*     */   public int getFielddbtype2() {
/*  59 */     return this.scale;
/*     */   }
/*     */   
/*     */   public void setFielddbtype2(int paramInt) {
/*  63 */     this.scale = paramInt;
/*     */   }
/*     */   public boolean isForSchedule() {
/*  66 */     return this.isForSchedule;
/*     */   }
/*     */   
/*     */   public void setForSchedule(boolean paramBoolean) {
/*  70 */     this.isForSchedule = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTotalWorkingDays(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/*  82 */     return getTotalWorkingDays(paramString1, paramString2, paramString3, paramString4, paramInt, this.user, true);
/*     */   }
/*     */   
/*     */   public String getTotalWorkingDays(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt1, int paramInt2, int paramInt3) {
/*  86 */     this.manager.setIsCalWorkDay(paramInt2);
/*  87 */     this.manager.setRelateweekday(paramInt3);
/*  88 */     return getTotalWorkingDays(paramString1, paramString2, paramString3, paramString4, paramInt1, this.user, true);
/*     */   }
/*     */   
/*  91 */   private static ThreadLocal<Boolean> isFromFlowLocal = new ThreadLocal<>();
/*     */   
/*     */   public static boolean isFromFlow() {
/*  94 */     return Boolean.TRUE.equals(isFromFlowLocal.get());
/*     */   }
/*     */   
/*     */   public String getTotalWorkingDays(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, User paramUser, boolean paramBoolean) {
/*  98 */     this.manager.setScale(this.scale);
/*  99 */     if (!isForSchedule()) {
/* 100 */       this.manager.setScheduleUnit(0);
/*     */     }
/* 102 */     isFromFlowLocal.set(Boolean.valueOf(true));
/* 103 */     String str = this.manager.getTotalWorkDays(paramString1, paramString2, paramString3, paramString4, paramInt, paramUser, paramBoolean);
/* 104 */     isFromFlowLocal.remove();
/* 105 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTotalWorkingHours(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/* 118 */     return getTotalWorkingHours(paramString1, paramString2, paramString3, paramString4, paramInt, this.user, true);
/*     */   }
/*     */   
/*     */   public String getTotalWorkingHours(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, User paramUser, boolean paramBoolean) {
/* 122 */     this.manager.setScale(this.scale);
/* 123 */     if (!isForSchedule()) {
/* 124 */       this.manager.setScheduleUnit(0);
/*     */     }
/* 126 */     return this.manager.getTotalWorkHours(paramString1, paramString2, paramString3, paramString4, paramInt, paramUser, paramBoolean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTotalNotWorkHours(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/* 139 */     return getTotalNotWorkHours(paramString1, paramString2, paramString3, paramString4, paramInt, this.user, true);
/*     */   }
/*     */   
/*     */   public String getTotalNotWorkHours(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, User paramUser, boolean paramBoolean) {
/* 143 */     this.manager.setScale(this.scale);
/* 144 */     return this.manager.getTotalRestHours(paramString1, paramString2, paramString3, paramString4, paramInt, paramUser, paramBoolean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBillSelectName(int paramInt1, String paramString, int paramInt2) {
/* 160 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select selectName").append(" from workflow_billfield a,workflow_SelectItem b").append(" where a.id = b.fieldId and a.billId = ").append(paramInt1).append(" and a.fieldName = '").append(paramString).append("'").append(" and b.selectValue = ").append(paramInt2);
/* 161 */     RecordSet recordSet = new RecordSet();
/* 162 */     recordSet.executeSql(stringBuffer.toString());
/* 163 */     return recordSet.next() ? StringUtil.vString(recordSet.getString("selectName")) : "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getIsWorkday(String paramString) {
/* 172 */     int i = 0;
/* 173 */     String str = "";
/* 174 */     if (this.user != null) {
/* 175 */       i = this.user.getUserSubCompany1();
/* 176 */       str = this.user.getCountryid();
/*     */     } 
/* 178 */     return getIsWorkday(paramString, i, str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getIsWorkday(String paramString1, int paramInt, String paramString2) {
/* 186 */     return this.manager.isWorkday(paramString1, paramInt, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getOnDutyAndOffDutyTimeMap(String paramString, int paramInt) {
/* 195 */     return this.manager.getOnDutyAndOffDutyTimeMap(paramString, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getStatusNameOfHrmResource(String paramString) {
/* 203 */     return getStatusName(paramString, (this.user != null) ? this.user.getLanguage() : 7);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getStatusName(String paramString1, String paramString2) {
/* 211 */     String str = ""; 
/* 212 */     try { str = (new ResourceComInfo()).getStatus(paramString1); } catch (Exception exception) {}
/* 213 */     return getStatusName(str, StringUtil.parseToInt(paramString2, 7));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getStatusName(String paramString, int paramInt) {
/* 223 */     String str = "";
/* 224 */     if (StringUtil.isNotNull(paramString)) {
/* 225 */       if (paramString.equals("0")) {
/* 226 */         str = SystemEnv.getHtmlLabelName(15710, paramInt);
/* 227 */       } else if (paramString.equals("1")) {
/* 228 */         str = SystemEnv.getHtmlLabelName(15711, paramInt);
/* 229 */       } else if (paramString.equals("2")) {
/* 230 */         str = SystemEnv.getHtmlLabelName(480, paramInt);
/* 231 */       } else if (paramString.equals("3")) {
/* 232 */         str = SystemEnv.getHtmlLabelName(15844, paramInt);
/* 233 */       } else if (paramString.equals("4")) {
/* 234 */         str = SystemEnv.getHtmlLabelName(6094, paramInt);
/* 235 */       } else if (paramString.equals("5")) {
/* 236 */         str = SystemEnv.getHtmlLabelName(6091, paramInt);
/* 237 */       } else if (paramString.equals("6")) {
/* 238 */         str = SystemEnv.getHtmlLabelName(6092, paramInt);
/* 239 */       } else if (paramString.equals("7")) {
/* 240 */         str = SystemEnv.getHtmlLabelName(2245, paramInt);
/* 241 */       } else if (paramString.equals("10")) {
/* 242 */         str = SystemEnv.getHtmlLabelName(1831, paramInt);
/*     */       } 
/*     */     }
/* 245 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String[] getSignInfo(User paramUser) {
/* 253 */     return (new HrmScheduleManager()).getSignInfo(paramUser);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */