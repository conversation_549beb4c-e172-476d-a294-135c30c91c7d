/*    */ package weaver.hrm.report.schedulediff;
/*    */ 
/*    */ import java.util.Comparator;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleDiffListComparator
/*    */   implements Comparator
/*    */ {
/*    */   public int compare(Object paramObject1, Object paramObject2) {
/* 40 */     byte b = 0;
/*    */     
/* 42 */     Map map1 = (Map)paramObject1;
/* 43 */     Map map2 = (Map)paramObject2;
/*    */     
/* 45 */     int i = Util.getIntValue((String)map1.get("subCompanyId"), -1);
/* 46 */     int j = Util.getIntValue((String)map2.get("subCompanyId"), -1);
/* 47 */     if (i > j) {
/* 48 */       b = 1;
/* 49 */       return b;
/* 50 */     }  if (i < j) {
/* 51 */       b = -1;
/* 52 */       return b;
/*    */     } 
/*    */     
/* 55 */     int k = Util.getIntValue((String)map1.get("departmentId"), -1);
/* 56 */     int m = Util.getIntValue((String)map2.get("departmentId"), -1);
/* 57 */     if (k > m) {
/* 58 */       b = 1;
/* 59 */       return b;
/* 60 */     }  if (k < m) {
/* 61 */       b = -1;
/* 62 */       return b;
/*    */     } 
/*    */     
/* 65 */     int n = Util.getIntValue((String)map1.get("resourceId"), -1);
/* 66 */     int i1 = Util.getIntValue((String)map2.get("resourceId"), -1);
/* 67 */     if (n > i1) {
/* 68 */       b = 1;
/* 69 */       return b;
/* 70 */     }  if (n < i1) {
/* 71 */       b = -1;
/* 72 */       return b;
/*    */     } 
/*    */     
/* 75 */     String str1 = Util.null2String((String)map1.get("signDate"));
/* 76 */     String str2 = Util.null2String((String)map2.get("signDate"));
/* 77 */     if (str1.compareTo(str2) > 0) {
/* 78 */       b = 1;
/* 79 */       return b;
/* 80 */     }  if (str1.compareTo(str2) < 0) {
/* 81 */       b = -1;
/* 82 */       return b;
/*    */     } 
/*    */ 
/*    */     
/* 86 */     int i2 = Util.getIntValue((String)map1.get("signId"), -1);
/* 87 */     int i3 = Util.getIntValue((String)map2.get("signId"), -1);
/* 88 */     if (i2 > i3) {
/* 89 */       b = 1;
/* 90 */       return b;
/* 91 */     }  if (i2 < i3) {
/* 92 */       b = -1;
/* 93 */       return b;
/*    */     } 
/*    */     
/* 96 */     return b;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleDiffListComparator.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */