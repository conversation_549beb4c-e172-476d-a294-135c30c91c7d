/*     */ package weaver.hrm.report.schedulediff;
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*     */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class HrmScheduleReportData extends BaseBean {
/*     */   private void initSchedule(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  16 */     RecordSet recordSet = new RecordSet();
/*  17 */     DecimalFormat decimalFormat = new DecimalFormat("0.00");
/*  18 */     StringUtil stringUtil = new StringUtil();
/*  19 */     HrmLeaveTypeColorManager hrmLeaveTypeColorManager = new HrmLeaveTypeColorManager();
/*  20 */     List<HrmLeaveTypeColor> list = hrmLeaveTypeColorManager.find("[map]subcompanyid:0;field003:1");
/*  21 */     byte b1 = (list == null) ? 0 : list.size();
/*  22 */     HrmLeaveTypeColor hrmLeaveTypeColor = null;
/*  23 */     HrmScheduleDiffManager hrmScheduleDiffManager = new HrmScheduleDiffManager();
/*  24 */     List<Map> list1 = hrmScheduleDiffManager.getScheduleList(paramString1, paramString2, paramInt1, paramInt2, paramString3, "");
/*  25 */     Map map = null;
/*     */     
/*  27 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  28 */     HashMap<Object, Object> hashMap = null;
/*  29 */     for (byte b2 = 0; b2 < list1.size(); b2++) {
/*  30 */       map = list1.get(b2);
/*  31 */       String str = (String)map.get("resourceId");
/*  32 */       hashMap = new HashMap<>();
/*  33 */       hashMap.put("resourceId", str);
/*  34 */       hashMap.put("resourceName", map.get("resourceName")); int i;
/*  35 */       for (i = 0; i < b1; i++) {
/*  36 */         hrmLeaveTypeColor = list.get(i);
/*  37 */         if (hrmLeaveTypeColor.getField004().intValue() == 1) {
/*  38 */           hashMap.put("leave1", StringUtil.vString(map.get("leave" + hrmLeaveTypeColor.getField004())));
/*  39 */         } else if (hrmLeaveTypeColor.getField004().intValue() == 2) {
/*  40 */           hashMap.put("leave2", StringUtil.vString(map.get("leave" + hrmLeaveTypeColor.getField004())));
/*  41 */         } else if (hrmLeaveTypeColor.getField004().intValue() == -6) {
/*  42 */           hashMap.put("leave6", StringUtil.vString(map.get("leave" + hrmLeaveTypeColor.getField004())));
/*     */         } 
/*     */       } 
/*  45 */       hashMap.put("beLate", StringUtil.vString(map.get("beLate")));
/*  46 */       hashMap.put("leaveEarly", StringUtil.vString(map.get("leaveEarly")));
/*  47 */       i = 0;
/*  48 */       int j = 0;
/*  49 */       recordSet.executeSql("SELECT COUNT(distinct signdate) FROM HrmScheduleSign WHERE signType=1 and signdate>='" + paramString1 + "' and signdate<='" + paramString2 + "' AND userId=" + str);
/*  50 */       if (recordSet.next()) {
/*  51 */         i = recordSet.getInt(1);
/*     */       }
/*  53 */       if (Double.parseDouble((String)map.get("workDays")) == 0.0D) {
/*  54 */         hashMap.put("workDaysper", "0.00");
/*     */       } else {
/*  56 */         hashMap.put("workDaysper", "" + decimalFormat.format(i / Double.parseDouble((String)map.get("workDays"))));
/*     */       } 
/*  58 */       hashMap.put("signnum", "" + i);
/*  59 */       recordSet.executeSql("SELECT COUNT(distinct signdate) FROM HrmScheduleSign WHERE signType=1 AND signFrom='mobile' and signdate>='" + paramString1 + "' and signdate<='" + paramString2 + "' AND userId=" + str);
/*  60 */       if (recordSet.next()) {
/*  61 */         j = recordSet.getInt(1);
/*     */       }
/*  63 */       hashMap.put("mobilesignnum", "" + j);
/*  64 */       if (i == 0) {
/*  65 */         hashMap.put("mobilesignper", "0.00");
/*     */       } else {
/*  67 */         hashMap.put("mobilesignper", "" + decimalFormat.format(j / i));
/*     */       } 
/*  69 */       arrayList.add(hashMap);
/*     */     } 
/*     */ 
/*     */     
/*  73 */     String str1 = "leave2,leave6,leave1,beLate,leaveEarly,workDaysper,signnum,mobilesignnum,mobilesignper";
/*  74 */     String str2 = "" + SystemEnv.getHtmlLabelName(10003660, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(19517, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003661, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(33899, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003662, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003663, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003664, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003665, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003666, ThreadVarLanguage.getLang()) + "";
/*  75 */     String[] arrayOfString1 = str1.split(",");
/*  76 */     String[] arrayOfString2 = str2.split(",");
/*  77 */     for (Map<Object, Object> map1 : arrayList) {
/*  78 */       paramString3 = (String)map1.get("resourceId");
/*  79 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/*  80 */         String str3 = (String)map1.get(arrayOfString1[b]);
/*  81 */         if (str3.length() == 0) str3 = "0"; 
/*  82 */         String str4 = " insert into nzhg2017review (userid, moduleid, prjid, prjtextvalue, value) values(" + paramString3 + ",'1'," + (b + 1) + ",'" + arrayOfString2[b] + "'," + str3 + ") ";
/*     */         
/*  84 */         recordSet.executeSql(str4);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initData(User paramUser, String paramString) {
/*  95 */     RecordSet recordSet = new RecordSet();
/*  96 */     if (!HrmUserVarify.checkUserRight("BohaiInsuranceScheduleReport:View", paramUser)) {
/*  97 */       writeLog("对不起！您暂无考勤统计权限");
/*     */       
/*     */       return;
/*     */     } 
/* 101 */     Date date = DateUtil.parseToDate(paramString + "-01-01");
/* 102 */     String str1 = DateUtil.getFirstDayOfYear(date);
/* 103 */     String str2 = DateUtil.getLastDayOfYear(date);
/*     */     
/* 105 */     recordSet.executeSql("delete from nzhg2017review where moduleid =1 ");
/* 106 */     recordSet.executeSql("select id from hrmsubcompany where (canceled=0 or canceled  IS NULL)");
/* 107 */     while (recordSet.next()) {
/* 108 */       int i = recordSet.getInt("id");
/* 109 */       initSchedule(str1, str2, i, 0, "");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff/HrmScheduleReportData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */