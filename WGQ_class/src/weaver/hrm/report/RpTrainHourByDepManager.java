/*    */ package weaver.hrm.report;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.systeminfo.SysMaintenanceLog;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RpTrainHourByDepManager
/*    */   extends BaseBean
/*    */ {
/*    */   private RecordSet statement;
/*    */   private SysMaintenanceLog log;
/*    */   private String startdate;
/*    */   private String enddate;
/*    */   private int departmentid;
/*    */   private int resourceid;
/*    */   
/*    */   public RpTrainHourByDepManager() {
/* 24 */     resetParameter();
/*    */   }
/*    */   
/*    */   public void resetParameter() {
/* 28 */     this.startdate = "";
/* 29 */     this.enddate = "";
/* 30 */     this.departmentid = -1;
/* 31 */     this.resourceid = -1;
/*    */   }
/*    */   
/*    */   public void setStartdate(String paramString) {
/* 35 */     this.startdate = paramString;
/*    */   }
/*    */   
/*    */   public void setEnddate(String paramString) {
/* 39 */     this.enddate = paramString;
/*    */   }
/*    */   
/*    */   public void setDepartmentid(int paramInt) {
/* 43 */     this.departmentid = paramInt;
/*    */   }
/*    */   
/*    */   public void setResourceid(int paramInt) {
/* 47 */     this.resourceid = paramInt;
/*    */   }
/*    */   
/*    */   public int getTrainHourSum() throws Exception {
/* 51 */     return this.statement.getInt("totalhours");
/*    */   }
/*    */   
/*    */   public int getTrainTypeid() throws Exception {
/* 55 */     return this.statement.getInt("traintype");
/*    */   }
/*    */   
/*    */   public void selectRpTrainHourByDep() throws Exception {
/* 59 */     this.statement = new RecordSet();
/*    */     try {
/* 61 */       String str1 = "";
/*    */       
/* 63 */       if (this.departmentid != 0) {
/* 64 */         str1 = " where resourceid in(select id from HrmResource where departmentid=" + this.departmentid + ")";
/*    */       }
/* 66 */       else if (this.resourceid != 0) {
/* 67 */         str1 = " where resourceid =" + this.resourceid;
/*    */       } else {
/*    */         
/* 70 */         str1 = "";
/*    */       } 
/*    */       
/* 73 */       if (!this.startdate.equals(""))
/* 74 */         if (str1.equals("")) { str1 = " where trainstartdate >='" + this.startdate + "'"; }
/* 75 */         else { str1 = str1 + " and trainstartdate >='" + this.startdate + "'"; }
/*    */          
/* 77 */       if (!this.enddate.equals("")) {
/* 78 */         if (str1.equals("")) { str1 = " where trainenddate <='" + this.enddate + "'"; }
/*    */         else
/* 80 */         { str1 = str1 + " and trainenddate <='" + this.enddate + "'"; }
/*    */       
/*    */       }
/*    */       
/* 84 */       String str2 = "select sum(trainhour) totalhours,traintype from HrmTrainRecord";
/* 85 */       str2 = str2 + str1;
/* 86 */       str2 = str2 + " group by traintype";
/* 87 */       this.statement.executeSql(str2);
/* 88 */       writeLog(str2);
/*    */     
/*    */     }
/* 91 */     catch (Exception exception) {
/* 92 */       writeLog(exception);
/* 93 */       throw exception;
/*    */     } 
/*    */   }
/*    */   
/*    */   public boolean next() throws Exception {
/* 98 */     return this.statement.next();
/*    */   }
/*    */   
/*    */   public void closeStatement() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpTrainHourByDepManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */