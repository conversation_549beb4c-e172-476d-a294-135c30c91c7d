/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpSeclevelManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private SysMaintenanceLog log;
/*     */   private String resourcetype1;
/*     */   private String resourcetype2;
/*     */   private String resourcetype3;
/*     */   private String resourcetype4;
/*     */   private int departmentid;
/*     */   private int activestatus;
/*     */   
/*     */   public RpSeclevelManager() {
/*  33 */     resetParameter();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetParameter() {
/*  40 */     this.resourcetype1 = "";
/*  41 */     this.resourcetype2 = "";
/*  42 */     this.resourcetype3 = "";
/*  43 */     this.resourcetype4 = "";
/*  44 */     this.departmentid = -1;
/*  45 */     this.activestatus = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype1(String paramString) {
/*  53 */     this.resourcetype1 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype2(String paramString) {
/*  60 */     this.resourcetype2 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype3(String paramString) {
/*  68 */     this.resourcetype3 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype4(String paramString) {
/*  75 */     this.resourcetype4 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDepartmentid(int paramInt) {
/*  82 */     this.departmentid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActivestatus(int paramInt) {
/*  90 */     this.activestatus = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResultcount() throws Exception {
/*  99 */     return this.statement.getInt("num");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResultid() throws Exception {
/* 108 */     return this.statement.getInt("resultid");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResulttype() throws Exception {
/* 117 */     return this.statement.getString("resourcetype");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectRpSeclevel() throws Exception {
/* 125 */     this.statement = new RecordSet();
/*     */     try {
/* 127 */       Calendar calendar = Calendar.getInstance();
/* 128 */       String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */       
/* 131 */       String str2 = "";
/* 132 */       boolean bool = false;
/* 133 */       if (this.departmentid == 0) { str2 = ""; }
/* 134 */       else { str2 = "where departmentid=" + this.departmentid; }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 143 */       if (!this.resourcetype1.equals("")) {
/* 144 */         if (str2.equals("")) { str2 = "where resourcetype in('" + this.resourcetype1; }
/* 145 */         else { str2 = str2 + " and resourcetype in('" + this.resourcetype1; }
/* 146 */          bool = true;
/*     */       } 
/* 148 */       if (!this.resourcetype2.equals("")) {
/* 149 */         if (str2.equals("")) { str2 = "where resourcetype in('" + this.resourcetype2; }
/*     */         
/* 151 */         else if (!bool) { str2 = str2 + " and resourcetype in('" + this.resourcetype2; }
/* 152 */         else { str2 = str2 + "','" + this.resourcetype2; }
/*     */         
/* 154 */         bool = true;
/*     */       } 
/* 156 */       if (!this.resourcetype3.equals("")) {
/* 157 */         if (str2.equals("")) { str2 = "where resourcetype in('" + this.resourcetype3; }
/*     */         
/* 159 */         else if (!bool) { str2 = str2 + " and resourcetype in('" + this.resourcetype3; }
/* 160 */         else { str2 = str2 + "','" + this.resourcetype3; }
/*     */         
/* 162 */         bool = true;
/*     */       } 
/* 164 */       if (!this.resourcetype4.equals("")) {
/* 165 */         if (str2.equals("")) { str2 = "where resourcetype in('" + this.resourcetype4; }
/*     */         
/* 167 */         else if (!bool) { str2 = str2 + " and resourcetype in('" + this.resourcetype4; }
/* 168 */         else { str2 = str2 + "','" + this.resourcetype4; }
/*     */         
/* 170 */         bool = true;
/*     */       } 
/* 172 */       if (bool == true) str2 = str2 + "' )"; 
/* 173 */       if (this.activestatus == 1)
/* 174 */         if (str2.equals("")) { str2 = "where (('" + str1 + "'>startdate or (startdate='' or startdate is  null)) and ('" + str1 + "'<enddate or (enddate='' or enddate is  null)))"; }
/* 175 */         else { str2 = str2 + " and (('" + str1 + "'>startdate or (startdate='' or startdate is  null)) and ('" + str1 + "'<enddate or (enddate='' or enddate is  null)))"; }
/*     */          
/* 177 */       if (this.activestatus == 2)
/* 178 */         if (str2.equals("")) { str2 = "where (('" + str1 + "'<startdate and (startdate<>'' and startdate is not null)) or ('" + str1 + "'>enddate and (enddate<>'' and enddate is not null)))"; }
/* 179 */         else { str2 = str2 + " and (('" + str1 + "'<startdate and (startdate<>'' and startdate is not null)) or ('" + str1 + "'>enddate and (enddate<>'' and enddate is not null)))"; }
/*     */          
/* 181 */       String str3 = "select count(id) num,seclevel resultid ,resourcetype from hrmresource ";
/* 182 */       str3 = str3 + str2;
/* 183 */       str3 = str3 + " group by seclevel,resourcetype order by seclevel,num desc";
/* 184 */       this.statement.executeSql(str3);
/*     */     }
/* 186 */     catch (Exception exception) {
/* 187 */       writeLog(exception);
/* 188 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/* 198 */     return this.statement.next();
/*     */   }
/*     */   
/*     */   public void closeStatement() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpSeclevelManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */