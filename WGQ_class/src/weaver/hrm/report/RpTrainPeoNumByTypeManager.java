/*    */ package weaver.hrm.report;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.systeminfo.SysMaintenanceLog;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RpTrainPeoNumByTypeManager
/*    */   extends BaseBean
/*    */ {
/*    */   private RecordSet statement;
/*    */   private SysMaintenanceLog log;
/*    */   private String startdate;
/*    */   private String enddate;
/*    */   private String direction;
/*    */   private int traintypeid;
/*    */   
/*    */   public RpTrainPeoNumByTypeManager() {
/* 24 */     resetParameter();
/*    */   }
/*    */   
/*    */   public void resetParameter() {
/* 28 */     this.startdate = "";
/* 29 */     this.enddate = "";
/* 30 */     this.traintypeid = -1;
/* 31 */     this.direction = "";
/*    */   }
/*    */   
/*    */   public void setStartdate(String paramString) {
/* 35 */     this.startdate = paramString;
/*    */   }
/*    */   
/*    */   public void setEnddate(String paramString) {
/* 39 */     this.enddate = paramString;
/*    */   }
/*    */   
/*    */   public void setDirection(String paramString) {
/* 43 */     this.direction = paramString;
/*    */   }
/*    */   
/*    */   public void setTrainTypeid(int paramInt) {
/* 47 */     this.traintypeid = paramInt;
/*    */   }
/*    */   
/*    */   public int getTrainPeoNumTotal() throws Exception {
/* 51 */     return this.statement.getInt("totalnum");
/*    */   }
/*    */   
/*    */   public int getDepartmentid() throws Exception {
/* 55 */     return this.statement.getInt("departmentid");
/*    */   }
/*    */   
/*    */   public void selectRpTrainPeoNumByType() throws Exception {
/* 59 */     this.statement = new RecordSet();
/*    */     try {
/* 61 */       String str1 = "";
/* 62 */       if (this.traintypeid == 0) { str1 = ""; }
/* 63 */       else { str1 = " and a.traintype=" + this.traintypeid; }
/* 64 */        if (!this.startdate.equals("")) {
/* 65 */         str1 = str1 + " and a.trainstartdate >='" + this.startdate + "'";
/*    */       }
/* 67 */       if (!this.enddate.equals("")) {
/* 68 */         str1 = str1 + " and a.trainenddate <='" + this.enddate + "'";
/*    */       }
/*    */       
/* 71 */       String str2 = "";
/* 72 */       if (this.direction.equals("1")) {
/* 73 */         str2 = "select count(a.resourceid) totalnum,b.departmentid from HrmTrainRecord a,HrmResource b where a.resourceid = b.id";
/*    */       } else {
/*    */         
/* 76 */         str2 = "select count(distinct(a.resourceid)) totalnum,b.departmentid from HrmTrainRecord a,HrmResource b where a.resourceid = b.id";
/*    */       } 
/* 78 */       str2 = str2 + str1;
/* 79 */       str2 = str2 + " group by b.departmentid";
/* 80 */       this.statement.executeSql(str2);
/* 81 */       writeLog(str2);
/*    */     
/*    */     }
/* 84 */     catch (Exception exception) {
/* 85 */       writeLog(exception);
/* 86 */       throw exception;
/*    */     } 
/*    */   }
/*    */   
/*    */   public boolean next() throws Exception {
/* 91 */     return this.statement.next();
/*    */   }
/*    */   
/*    */   public void closeStatement() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpTrainPeoNumByTypeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */