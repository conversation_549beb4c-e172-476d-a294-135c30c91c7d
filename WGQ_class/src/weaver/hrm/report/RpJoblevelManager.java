/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpJoblevelManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private SysMaintenanceLog log;
/*     */   private String resourcetype1;
/*     */   private String resourcetype2;
/*     */   private String resourcetype3;
/*     */   private String resourcetype4;
/*     */   private int departmentid;
/*     */   
/*     */   public RpJoblevelManager() {
/*  32 */     resetParameter();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetParameter() {
/*  39 */     this.resourcetype1 = "";
/*  40 */     this.resourcetype2 = "";
/*  41 */     this.resourcetype3 = "";
/*  42 */     this.resourcetype4 = "";
/*  43 */     this.departmentid = -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype1(String paramString) {
/*  51 */     this.resourcetype1 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype2(String paramString) {
/*  58 */     this.resourcetype2 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype3(String paramString) {
/*  65 */     this.resourcetype3 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setResourcetype4(String paramString) {
/*  72 */     this.resourcetype4 = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDepartmentid(int paramInt) {
/*  79 */     this.departmentid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResultcount() throws Exception {
/*  88 */     return this.statement.getInt("num");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResultid() throws Exception {
/*  97 */     return this.statement.getInt("resultid");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResulttype() throws Exception {
/* 106 */     return this.statement.getString("resourcetype");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectRpJoblevel() throws Exception {
/* 114 */     this.statement = new RecordSet();
/*     */     try {
/* 116 */       String str1 = "";
/* 117 */       boolean bool = false;
/* 118 */       if (this.departmentid == 0) { str1 = ""; }
/* 119 */       else { str1 = "where departmentid=" + this.departmentid; }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 128 */       if (!this.resourcetype1.equals("")) {
/* 129 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype1; }
/* 130 */         else { str1 = str1 + " and resourcetype in('" + this.resourcetype1; }
/* 131 */          bool = true;
/*     */       } 
/* 133 */       if (!this.resourcetype2.equals("")) {
/* 134 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype2; }
/*     */         
/* 136 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype2; }
/* 137 */         else { str1 = str1 + "','" + this.resourcetype2; }
/*     */         
/* 139 */         bool = true;
/*     */       } 
/* 141 */       if (!this.resourcetype3.equals("")) {
/* 142 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype3; }
/*     */         
/* 144 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype3; }
/* 145 */         else { str1 = str1 + "','" + this.resourcetype3; }
/*     */         
/* 147 */         bool = true;
/*     */       } 
/* 149 */       if (!this.resourcetype4.equals("")) {
/* 150 */         if (str1.equals("")) { str1 = "where resourcetype in('" + this.resourcetype4; }
/*     */         
/* 152 */         else if (!bool) { str1 = str1 + " and resourcetype in('" + this.resourcetype4; }
/* 153 */         else { str1 = str1 + "','" + this.resourcetype4; }
/*     */         
/* 155 */         bool = true;
/*     */       } 
/* 157 */       if (bool == true) str1 = str1 + "' )"; 
/* 158 */       String str2 = "select count(id) num,joblevel resultid ,resourcetype from hrmresource ";
/* 159 */       str2 = str2 + str1;
/* 160 */       str2 = str2 + " group by joblevel,resourcetype order by joblevel,num desc";
/* 161 */       this.statement.executeSql(str2);
/*     */     }
/* 163 */     catch (Exception exception) {
/* 164 */       writeLog(exception);
/* 165 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/* 175 */     return this.statement.next();
/*     */   }
/*     */   
/*     */   public void closeStatement() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpJoblevelManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */