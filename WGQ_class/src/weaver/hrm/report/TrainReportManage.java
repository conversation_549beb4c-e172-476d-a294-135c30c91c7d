/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.LogMan;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TrainReportManage
/*     */ {
/*  14 */   LogMan lm = LogMan.getInstance();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getAttendRate(String paramString) {
/*  22 */     float f = 0.0F;
/*     */     try {
/*  24 */       RecordSet recordSet = new RecordSet();
/*  25 */       String str1 = "select count(*) from HrmTrainActor where traindayid in(select id from HrmTrainDay where trainid =" + paramString + ")";
/*  26 */       recordSet.executeSql(str1);
/*  27 */       recordSet.next();
/*  28 */       int i = recordSet.getInt(1);
/*  29 */       if (i == 0) {
/*  30 */         return 0.0F;
/*     */       }
/*  32 */       String str2 = "select count(*) from HrmTrainActor where traindayid in(select id from HrmTrainDay where trainid =" + paramString + ") and isattend = 1";
/*  33 */       recordSet.executeSql(str2);
/*  34 */       recordSet.next();
/*  35 */       int j = recordSet.getInt(1);
/*  36 */       f = getRate(j, i);
/*     */     }
/*  38 */     catch (Exception exception) {}
/*     */     
/*  40 */     return f;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getLayoutAttendRate(String paramString) {
/*  49 */     float f = 0.0F;
/*     */     try {
/*  51 */       RecordSet recordSet = new RecordSet();
/*  52 */       String str1 = "select count(*) from HrmTrainActor where traindayid in(select id from HrmTrainDay where trainid in (select id from HrmTrain where planid in( select id from HrmTrainPlan where layoutid=" + paramString + ")))";
/*  53 */       recordSet.executeSql(str1);
/*  54 */       recordSet.next();
/*  55 */       int i = recordSet.getInt(1);
/*  56 */       if (i == 0) {
/*  57 */         return 0.0F;
/*     */       }
/*  59 */       String str2 = "select count(*) from HrmTrainActor where traindayid in(select id from HrmTrainDay where trainid in (select id from HrmTrain where planid in( select id from HrmTrainPlan where layoutid =" + paramString + "))) and isattend = 1";
/*  60 */       recordSet.executeSql(str2);
/*  61 */       recordSet.next();
/*  62 */       int j = recordSet.getInt(1);
/*  63 */       f = getRate(j, i);
/*     */     }
/*  65 */     catch (Exception exception) {}
/*     */     
/*  67 */     return f;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getTestInfo(String paramString, int paramInt) {
/*     */     try {
/*  78 */       RecordSet recordSet = new RecordSet();
/*  79 */       String str = "select count(id) from HrmTrainTest where trainid = " + paramString + " and result = " + paramInt;
/*  80 */       recordSet.executeSql(str);
/*  81 */       recordSet.next();
/*  82 */       int i = recordSet.getInt(1);
/*  83 */       int j = getTestNum(paramString);
/*  84 */       if (j == 0) {
/*  85 */         return 0.0F;
/*     */       }
/*     */       
/*  88 */       return getRate(i, j);
/*  89 */     } catch (Exception exception) {
/*  90 */       this.lm.writeLog(exception);
/*     */       
/*  92 */       return 0.0F;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getAssessInfo(String paramString, int paramInt) {
/*     */     try {
/* 103 */       RecordSet recordSet = new RecordSet();
/* 104 */       String str = "select count(id) from HrmTrainAssess where trainid = " + paramString + " and implement = " + paramInt;
/* 105 */       recordSet.executeSql(str);
/* 106 */       recordSet.next();
/* 107 */       int i = recordSet.getInt(1);
/* 108 */       int j = getAssessNum(paramString);
/* 109 */       if (j == 0) {
/* 110 */         return 0.0F;
/*     */       }
/* 112 */       return getRate(i, j);
/* 113 */     } catch (Exception exception) {
/* 114 */       this.lm.writeLog(exception);
/*     */       
/* 116 */       return 0.0F;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getResourceAssess(int paramInt) {
/*     */     try {
/* 126 */       RecordSet recordSet = new RecordSet();
/* 127 */       String str = "select count(id)";
/* 128 */     } catch (Exception exception) {
/* 129 */       this.lm.writeLog(exception);
/*     */     } 
/* 131 */     return 0.0F;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getResourceAssess(String paramString, int paramInt) {
/*     */     try {
/* 142 */       RecordSet recordSet = new RecordSet();
/* 143 */       String str = "select count(id) from HrmTrainAssess where trainid in( select id from HrmTrain where resource_n =" + paramString + ") and implement = " + paramInt;
/* 144 */       recordSet.executeSql(str);
/* 145 */       recordSet.next();
/* 146 */       int i = recordSet.getInt(1);
/* 147 */       int j = getResourceAssessNum(paramString);
/* 148 */       if (j == 0) {
/* 149 */         return 0.0F;
/*     */       }
/* 151 */       return getRate(i, j);
/* 152 */     } catch (Exception exception) {
/* 153 */       this.lm.writeLog(exception);
/*     */       
/* 155 */       return 0.0F;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getLayoutAssess(String paramString, int paramInt) {
/*     */     try {
/* 166 */       RecordSet recordSet = new RecordSet();
/* 167 */       String str = "select count(id) from HrmTrainLayoutAssess where layoutid =" + paramString + " and implement = " + paramInt;
/* 168 */       recordSet.executeSql(str);
/* 169 */       recordSet.next();
/* 170 */       int i = recordSet.getInt(1);
/* 171 */       int j = getLayoutAssessNum(paramString);
/* 172 */       if (j == 0) {
/* 173 */         return 0.0F;
/*     */       }
/* 175 */       return getRate(i, j);
/* 176 */     } catch (Exception exception) {
/* 177 */       this.lm.writeLog(exception);
/*     */       
/* 179 */       return 0.0F;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getLayoutAssessByTrain(String paramString, int paramInt) {
/*     */     try {
/* 190 */       RecordSet recordSet = new RecordSet();
/* 191 */       String str = "select count(id) from HrmTrainAssess where trainid in ( select id from HrmTrain where planid in ( select id from HrmTrainPlan where layoutid =" + paramString + "))  and implement = " + paramInt;
/* 192 */       recordSet.executeSql(str);
/* 193 */       recordSet.next();
/* 194 */       int i = recordSet.getInt(1);
/* 195 */       int j = getLayoutAssessNumByTrain(paramString);
/* 196 */       if (j == 0) {
/* 197 */         return 0.0F;
/*     */       }
/* 199 */       return getRate(i, j);
/* 200 */     } catch (Exception exception) {
/* 201 */       this.lm.writeLog(exception);
/*     */       
/* 203 */       return 0.0F;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getLayoutTestByTrain(String paramString, int paramInt) {
/*     */     try {
/* 214 */       RecordSet recordSet = new RecordSet();
/* 215 */       String str = "select count(id) from HrmTrainTest where trainid in ( select id from HrmTrain where planid in ( select id from HrmTrainPlan where layoutid =" + paramString + "))  and result = " + paramInt;
/* 216 */       recordSet.executeSql(str);
/* 217 */       recordSet.next();
/* 218 */       int i = recordSet.getInt(1);
/* 219 */       int j = getLayoutTestNumByTrain(paramString);
/* 220 */       if (j == 0) {
/* 221 */         return 0.0F;
/*     */       }
/* 223 */       return getRate(i, j);
/* 224 */     } catch (Exception exception) {
/* 225 */       this.lm.writeLog(exception);
/*     */       
/* 227 */       return 0.0F;
/*     */     } 
/*     */   }
/*     */   private int getTestNum(String paramString) {
/*     */     try {
/* 232 */       RecordSet recordSet = new RecordSet();
/* 233 */       String str = "select count(id) from HrmTrainTest where trainid = " + paramString;
/* 234 */       recordSet.executeSql(str);
/* 235 */       recordSet.next();
/* 236 */       return recordSet.getInt(1);
/*     */     }
/* 238 */     catch (Exception exception) {
/* 239 */       this.lm.writeLog(exception);
/*     */       
/* 241 */       return 0;
/*     */     } 
/*     */   }
/*     */   private int getAssessNum(String paramString) {
/*     */     try {
/* 246 */       RecordSet recordSet = new RecordSet();
/* 247 */       String str = "select count(id) from HrmTrainAssess where trainid = " + paramString;
/* 248 */       recordSet.executeSql(str);
/* 249 */       recordSet.next();
/* 250 */       return recordSet.getInt(1);
/*     */     }
/* 252 */     catch (Exception exception) {
/* 253 */       this.lm.writeLog(exception);
/*     */       
/* 255 */       return 0;
/*     */     } 
/*     */   }
/*     */   private int getResourceAssessNum(String paramString) {
/*     */     try {
/* 260 */       RecordSet recordSet = new RecordSet();
/* 261 */       String str = "select count(id) from HrmTrainAssess where trainid in ( select id from HrmTrain where resource_n=" + paramString + ")";
/* 262 */       recordSet.executeSql(str);
/* 263 */       recordSet.next();
/* 264 */       return recordSet.getInt(1);
/*     */     }
/* 266 */     catch (Exception exception) {
/* 267 */       this.lm.writeLog(exception);
/*     */       
/* 269 */       return 0;
/*     */     } 
/*     */   }
/*     */   private int getLayoutAssessNum(String paramString) {
/*     */     try {
/* 274 */       RecordSet recordSet = new RecordSet();
/* 275 */       String str = "select count(id) from HrmTrainLayoutAssess where layoutid =" + paramString;
/* 276 */       recordSet.executeSql(str);
/* 277 */       recordSet.next();
/* 278 */       return recordSet.getInt(1);
/*     */     }
/* 280 */     catch (Exception exception) {
/* 281 */       this.lm.writeLog(exception);
/*     */       
/* 283 */       return 0;
/*     */     } 
/*     */   }
/*     */   private int getLayoutAssessNumByTrain(String paramString) {
/*     */     try {
/* 288 */       RecordSet recordSet = new RecordSet();
/* 289 */       String str = "select count(id) from HrmTrainAssess where trainid in ( select id from HrmTrain where planid in ( select id from HrmTrainPlan where layoutid =" + paramString + "))";
/* 290 */       recordSet.executeSql(str);
/* 291 */       recordSet.next();
/* 292 */       return recordSet.getInt(1);
/*     */     }
/* 294 */     catch (Exception exception) {
/* 295 */       this.lm.writeLog(exception);
/*     */       
/* 297 */       return 0;
/*     */     } 
/*     */   }
/*     */   private int getLayoutTestNumByTrain(String paramString) {
/*     */     try {
/* 302 */       RecordSet recordSet = new RecordSet();
/* 303 */       String str = "select count(id) from HrmTrainTest where trainid in ( select id from HrmTrain where planid in ( select id from HrmTrainPlan where layoutid =" + paramString + "))";
/* 304 */       recordSet.executeSql(str);
/* 305 */       recordSet.next();
/* 306 */       return recordSet.getInt(1);
/*     */     }
/* 308 */     catch (Exception exception) {
/* 309 */       this.lm.writeLog(exception);
/*     */       
/* 311 */       return 0;
/*     */     } 
/*     */   }
/*     */   
/*     */   private float getRate(int paramInt1, int paramInt2) {
/* 316 */     double d = (paramInt1 / paramInt2);
/* 317 */     float f = (float)((int)(d * 100.0D) / 100.0D);
/* 318 */     f *= 100.0F;
/*     */     
/* 320 */     String str1 = "" + f;
/* 321 */     int i = str1.indexOf(".");
/* 322 */     String str2 = str1.substring(i);
/* 323 */     while (str2.length() <= 2) {
/* 324 */       str1 = str1 + Character.MIN_VALUE;
/* 325 */       str2 = str2 + Character.MIN_VALUE;
/*     */     } 
/* 327 */     str1 = str1.substring(0, i + 3);
/* 328 */     return Float.parseFloat(str1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/TrainReportManage.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */