/*    */ package weaver.hrm.report.manager;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.appdetach.AppDetachComInfo;
/*    */ import weaver.hrm.job.EducationLevelComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmEducationLevelRpManager
/*    */   extends ReportManager
/*    */   implements IReport
/*    */ {
/*    */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 24 */     String str1 = paramMap.get("fromdate");
/* 25 */     String str2 = paramMap.get("enddate");
/* 26 */     String str3 = paramMap.get("department");
/* 27 */     String str4 = paramMap.get("location");
/* 28 */     String str5 = paramMap.get("status");
/*    */     
/* 30 */     int i = 0;
/* 31 */     String str6 = "";
/*    */     
/* 33 */     if (str5.equals("")) {
/* 34 */       str5 = "8";
/*    */     }
/*    */     
/* 37 */     if (!str1.equals("")) {
/* 38 */       str6 = str6 + " and startdate>='" + str1 + "'";
/*    */     }
/* 40 */     if (!str2.equals("")) {
/* 41 */       str6 = str6 + " and (startdate<='" + str2 + "' or startdate is null)";
/*    */     }
/*    */     
/* 44 */     if (!str4.equals("")) {
/* 45 */       str6 = str6 + " and locationid =" + str4;
/*    */     }
/* 47 */     if (!str3.equals("")) {
/* 48 */       str6 = str6 + " and departmentid =" + str3;
/*    */     }
/*    */     
/* 51 */     if (!str5.equals("") && !str5.equals("9")) {
/* 52 */       if (str5.equals("8")) {
/* 53 */         str6 = str6 + " and status <= 3";
/*    */       } else {
/* 55 */         str6 = str6 + " and status =" + str5;
/*    */       } 
/*    */     }
/*    */     
/* 59 */     String str7 = AppDetachComInfo.getInnerResourceSql("t1");
/*    */     
/* 61 */     str6 = str6 + " and " + str7;
/*    */     
/* 63 */     String str8 = "";
/* 64 */     String str9 = " (accounttype=0 or accounttype is null) ";
/* 65 */     if (str6.equals("")) {
/* 66 */       str8 = " select 0  resultid ,count(*) resultcount from HrmResource t1 where (educationlevel <2 or educationlevel is null) and " + str9 + " union\tselect educationlevel resultid  ,count(*) resultcount from HrmResource t1 where educationlevel > 1 and " + str9 + " group by  educationlevel ";
/*    */     } else {
/* 68 */       str8 = " select 0  resultid ,count(*) resultcount from HrmResource t1 where (educationlevel <2 or educationlevel is null) and " + str9 + " " + str6 + " union\tselect educationlevel resultid  ,count(*) resultcount from HrmResource t1 where educationlevel > 1 and " + str9 + " " + str6 + " group by  educationlevel ";
/*    */     } 
/* 70 */     this.rs.executeSql(str8);
/*    */     
/* 72 */     while (this.rs.next()) {
/* 73 */       int j = this.rs.getInt(2);
/* 74 */       i += j;
/*    */     } 
/* 76 */     this.rs.beforFirst();
/* 77 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 78 */     HashMap<Object, Object> hashMap = null;
/* 79 */     if (i != 0) {
/* 80 */       while (this.rs.next()) {
/* 81 */         String str10 = this.rs.getString(1);
/* 82 */         if (str10.equals("0")) str10 = "1"; 
/* 83 */         int j = this.rs.getInt(2);
/*    */         
/* 85 */         hashMap = new HashMap<>();
/* 86 */         String str11 = "";
/*    */         try {
/* 88 */           str11 = Util.toScreen((new EducationLevelComInfo()).getEducationLevelname(str10), paramUser.getLanguage());
/* 89 */         } catch (Exception exception) {
/* 90 */           exception.printStackTrace();
/*    */         } 
/* 92 */         hashMap.put("title", str11);
/* 93 */         hashMap.put("result", String.valueOf(j));
/* 94 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 95 */         hashMap.put("total", String.valueOf(i));
/* 96 */         arrayList.add(hashMap);
/*    */       } 
/*    */     }
/* 99 */     return (List)arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmEducationLevelRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */