/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSexRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  26 */     String str1 = paramMap.get("fromdate");
/*  27 */     String str2 = paramMap.get("enddate");
/*  28 */     String str3 = paramMap.get("department");
/*  29 */     String str4 = paramMap.get("location");
/*  30 */     String str5 = paramMap.get("status");
/*     */     
/*  32 */     int i = 0;
/*  33 */     String str6 = "";
/*     */     
/*  35 */     if (str5.equals("")) {
/*  36 */       str5 = "8";
/*     */     }
/*     */     
/*  39 */     if (!str1.equals("")) {
/*  40 */       str6 = str6 + " and startdate>='" + str1 + "'";
/*     */     }
/*  42 */     if (!str2.equals("")) {
/*  43 */       str6 = str6 + " and (startdate<='" + str2 + "' or startdate is null)";
/*     */     }
/*     */     
/*  46 */     if (!str4.equals("")) {
/*  47 */       str6 = str6 + " and locationid =" + str4;
/*     */     }
/*  49 */     if (!str3.equals("")) {
/*  50 */       str6 = str6 + " and departmentid =" + str3;
/*     */     }
/*     */     
/*  53 */     if (!str5.equals("") && !str5.equals("9")) {
/*  54 */       if (str5.equals("8")) {
/*  55 */         str6 = str6 + " and status <= 3";
/*     */       } else {
/*  57 */         str6 = str6 + " and status =" + str5;
/*     */       } 
/*     */     }
/*     */     
/*  61 */     String str7 = AppDetachComInfo.getInnerResourceSql();
/*     */     
/*  63 */     str6 = str6 + " and " + str7;
/*     */     
/*  65 */     String str8 = "";
/*  66 */     String str9 = "";
/*  67 */     if (str6.equals("")) {
/*  68 */       str9 = "select count(*)  from HrmResource  where (accounttype is null or accounttype=0) and id>2 ";
/*     */     } else {
/*  70 */       str9 = "select count(*)  from HrmResource where (accounttype is null or accounttype=0) and id>2 " + str6;
/*     */     } 
/*  72 */     this.rs.executeSql(str9);
/*  73 */     this.rs.next();
/*  74 */     i = this.rs.getInt(1);
/*     */     
/*  76 */     String str10 = "";
/*  77 */     String str11 = "";
/*  78 */     int j = 0;
/*  79 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  80 */     HashMap<Object, Object> hashMap = null;
/*     */     
/*  82 */     str8 = "select  COUNT(*)   resultcount , sex from HrmResource   where  (accounttype is null or accounttype=0) and id>2 " + str6 + " group by sex ";
/*     */     
/*  84 */     this.rs.executeSql(str8);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  92 */     int k = 0;
/*     */     
/*  94 */     while (this.rs.next()) {
/*  95 */       j = Util.getIntValue(this.rs.getString(1), 0);
/*  96 */       str10 = Util.null2String(this.rs.getString(2));
/*     */       
/*  98 */       if (!str10.equals("1") && !str10.equals("0")) {
/*     */         
/* 100 */         k += j;
/*     */         continue;
/*     */       } 
/* 103 */       if (j == 0)
/* 104 */         continue;  if (str10.equals("0")) {
/* 105 */         str11 = SystemEnv.getHtmlLabelName(417, paramUser.getLanguage());
/*     */       } else {
/*     */         
/* 108 */         str11 = SystemEnv.getHtmlLabelName(418, paramUser.getLanguage());
/*     */       } 
/*     */       
/* 111 */       hashMap = new HashMap<>();
/* 112 */       hashMap.put("title", str11);
/* 113 */       hashMap.put("result", String.valueOf(j));
/* 114 */       hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 115 */       hashMap.put("total", String.valueOf(i));
/* 116 */       arrayList.add(hashMap);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 122 */     if (k != 0) {
/* 123 */       str11 = SystemEnv.getHtmlLabelName(15808, paramUser.getLanguage());
/* 124 */       hashMap = new HashMap<>();
/* 125 */       hashMap.put("title", str11);
/* 126 */       hashMap.put("result", String.valueOf(k));
/* 127 */       hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(k), String.valueOf(i)));
/* 128 */       hashMap.put("total", String.valueOf(i));
/* 129 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 132 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmSexRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */