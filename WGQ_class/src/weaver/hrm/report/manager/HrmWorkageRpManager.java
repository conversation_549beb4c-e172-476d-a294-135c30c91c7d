/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmWorkageRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  26 */     String str1 = paramMap.get("department");
/*  27 */     String str2 = paramMap.get("location");
/*  28 */     String str3 = paramMap.get("status");
/*  29 */     String str4 = paramMap.get("area");
/*     */     
/*  31 */     int i = 0;
/*  32 */     String str5 = "";
/*  33 */     String str6 = "";
/*     */     
/*  35 */     if (str4.equals("")) {
/*  36 */       str4 = "10";
/*     */     }
/*  38 */     if (str3.equals("")) {
/*  39 */       str3 = "8";
/*     */     }
/*     */     
/*  42 */     if (!str2.equals("")) {
/*  43 */       str5 = str5 + " and locationid =" + str2;
/*  44 */       if (str6.equals("")) {
/*  45 */         str6 = " where locationid =" + str2;
/*     */       } else {
/*     */         
/*  48 */         str6 = str6 + " and locationid =" + str2;
/*     */       } 
/*     */     } 
/*  51 */     if (!str1.equals("")) {
/*  52 */       str5 = str5 + " and departmentid =" + str1;
/*  53 */       if (str6.equals("")) {
/*  54 */         str6 = " where departmentid =" + str1;
/*     */       } else {
/*     */         
/*  57 */         str6 = str6 + " and departmentid =" + str1;
/*     */       } 
/*     */     } 
/*     */     
/*  61 */     if (!str3.equals("") && !str3.equals("9")) {
/*  62 */       if (str3.equals("8")) {
/*  63 */         str5 = str5 + " and status <= 3";
/*  64 */         if (str6.equals("")) {
/*  65 */           str6 = " where status <= 3";
/*     */         } else {
/*  67 */           str6 = str6 + " and status <= 3";
/*     */         } 
/*     */       } else {
/*  70 */         str5 = str5 + " and status =" + str3;
/*     */         
/*  72 */         if (str6.equals("")) {
/*  73 */           str6 = " where status =" + str3;
/*     */         } else {
/*  75 */           str6 = str6 + " and status =" + str3;
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*  80 */     String str7 = AppDetachComInfo.getInnerResourceSql();
/*     */     
/*  82 */     str5 = str5 + " and " + str7;
/*  83 */     if (str6.equals("")) {
/*  84 */       str6 = " where " + str7;
/*     */     } else {
/*  86 */       str6 = str6 + " and " + str7;
/*     */     } 
/*     */     
/*  89 */     String str8 = "";
/*  90 */     String str9 = "";
/*  91 */     int j = 0;
/*  92 */     if (str6.equals("")) {
/*  93 */       str9 = "select count(*)  from HrmResource where accounttype is null or accounttype=0 ";
/*     */     } else {
/*  95 */       str9 = "select count(*)  from HrmResource " + str6 + " and (accounttype is null or accounttype=0)";
/*     */     } 
/*  97 */     this.rs.executeSql(str9);
/*  98 */     this.rs.next();
/*  99 */     i = this.rs.getInt(1);
/*     */ 
/*     */     
/* 102 */     if (str5.equals("")) {
/* 103 */       str8 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and (startdate is null or startdate ='')";
/*     */     } else {
/* 105 */       str8 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and (startdate is null or startdate ='')" + str5;
/*     */     } 
/* 107 */     this.rs.executeSql(str8);
/* 108 */     this.rs.next();
/* 109 */     j = this.rs.getInt(1);
/*     */     
/* 111 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 112 */     HashMap<Object, Object> hashMap = null;
/* 113 */     if (j != 0) {
/* 114 */       hashMap = new HashMap<>();
/* 115 */       hashMap.put("title", SystemEnv.getHtmlLabelName(15863, paramUser.getLanguage()));
/* 116 */       hashMap.put("result", String.valueOf(j));
/* 117 */       hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 118 */       hashMap.put("total", String.valueOf(i));
/* 119 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 122 */     Calendar calendar = Calendar.getInstance();
/* 123 */     int k = Util.getIntValue(Util.add0(calendar.get(1), 4));
/*     */     
/* 125 */     if (this.rs.getDBType().equals("oracle")) {
/* 126 */       str8 = "select min(startdate) from HrmResource where  (accounttype is null or accounttype=0) and startdate is not null ";
/*     */     } else {
/* 128 */       str8 = "select min(startdate) from HrmResource where  (accounttype is null or accounttype=0) and startdate is not null and startdate !=''";
/*     */     } 
/* 130 */     this.rs.executeSql(str8);
/* 131 */     this.rs.next();
/* 132 */     String str10 = this.rs.getString(1);
/* 133 */     if (!str10.equals("") && str10.length() >= 4) {
/*     */       
/* 135 */       int m = Util.getIntValue(str10.substring(0, 4));
/* 136 */       int n = k - m + 1;
/* 137 */       int i1 = Util.getIntValue(str4);
/* 138 */       int i2 = ((i1 == 0) ? 0 : (n / i1)) + 1;
/* 139 */       String str11 = "";
/* 140 */       String str12 = "";
/*     */       
/* 142 */       for (byte b = 0; b < i2; b++) {
/* 143 */         int i3 = b * i1;
/* 144 */         int i4 = (b + 1) * i1;
/*     */ 
/*     */         
/* 147 */         str11 = (k - i1) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */         
/* 150 */         str12 = k + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */         
/* 152 */         if (this.rs.getDBType().equals("oracle")) {
/* 153 */           if (str5.equals("")) {
/* 154 */             str8 = "select count(*) resultcount from HrmResource where  (accounttype is null or accounttype=0) and startdate >'" + str11 + "'  and startdate <='" + str12 + "'  and startdate is not null ";
/*     */           } else {
/* 156 */             str8 = "select count(*) resultcount from HrmResource where  (accounttype is null or accounttype=0) and startdate >'" + str11 + "'  and startdate <='" + str12 + "'  and startdate is not null " + str5;
/*     */           }
/*     */         
/* 159 */         } else if (str5.equals("")) {
/* 160 */           str8 = "select count(*) resultcount from HrmResource where  (accounttype is null or accounttype=0) and startdate >'" + str11 + "'  and startdate <='" + str12 + "' and startdate<>'' and startdate is not null ";
/*     */         } else {
/* 162 */           str8 = "select count(*) resultcount from HrmResource where  (accounttype is null or accounttype=0) and startdate >'" + str11 + "'  and startdate <='" + str12 + "' and startdate<>'' and startdate is not null " + str5;
/*     */         } 
/*     */ 
/*     */         
/* 166 */         this.rs.executeSql(str8);
/* 167 */         this.rs.next();
/* 168 */         j = this.rs.getInt(1);
/*     */         
/* 170 */         if (j != 0) {
/* 171 */           hashMap = new HashMap<>();
/* 172 */           hashMap.put("title", i3 + "-" + i4 + " " + SystemEnv.getHtmlLabelName(445, paramUser.getLanguage()));
/* 173 */           hashMap.put("result", String.valueOf(j));
/* 174 */           hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 175 */           hashMap.put("total", String.valueOf(i));
/* 176 */           arrayList.add(hashMap);
/*     */         } 
/* 178 */         k -= i1;
/*     */       } 
/*     */     } 
/* 181 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmWorkageRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */