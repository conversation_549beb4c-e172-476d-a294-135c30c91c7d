/*    */ package weaver.hrm.report.manager;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Enumeration;
/*    */ import java.util.HashMap;
/*    */ import java.util.Hashtable;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmRpContractTimeManager
/*    */   extends ReportManager
/*    */   implements IReport
/*    */ {
/*    */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 24 */     String str1 = paramMap.get("from");
/* 25 */     String str2 = paramMap.get("year");
/* 26 */     String str3 = paramMap.get("type");
/* 27 */     String str4 = paramMap.get("subcompanyid1");
/*    */     
/* 29 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 30 */     HashMap<Object, Object> hashMap = null;
/*    */     
/* 32 */     String str5 = "";
/* 33 */     String str6 = "select id,typename from HrmContractType";
/* 34 */     this.rs.executeSql(str6);
/* 35 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/* 36 */     while (this.rs.next()) {
/* 37 */       hashtable.put(new Integer(this.rs.getInt("id")), this.rs.getString("typename"));
/*    */     }
/* 39 */     String str7 = "";
/* 40 */     if (!str4.equals("") && str1.equals("")) {
/* 41 */       str7 = str7 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str4 + ")";
/*    */     }
/* 43 */     if (str1.equals("time")) {
/* 44 */       str4 = String.valueOf(paramUser.getUserSubCompany1());
/* 45 */       str7 = str7 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str4 + ")";
/*    */     } 
/* 47 */     if (str3.equals("")) {
/* 48 */       Enumeration<Integer> enumeration = hashtable.keys();
/* 49 */       while (enumeration.hasMoreElements()) {
/* 50 */         Integer integer = enumeration.nextElement();
/* 51 */         String str = (String)hashtable.get(integer);
/* 52 */         hashMap = new HashMap<Object, Object>();
/* 53 */         hashMap.put("title", str);
/* 54 */         for (byte b = 1; b < 13; b++) {
/* 55 */           String str8 = "" + str2 + "-" + Util.add0(b, 2) + "-01";
/* 56 */           String str9 = "" + str2 + "-" + Util.add0(b, 2) + "-31";
/* 57 */           str5 = " and (contractstartdate >='" + str8 + "' and contractstartdate <= '" + str9 + "')";
/* 58 */           str5 = str5 + " and t1.contracttypeid=" + integer;
/* 59 */           str6 = "select count(t1.id) resultcount from HrmContract t1 left join HrmResource t2 on t1.contractman = t2.id where 3 = 3 " + str5 + str7;
/* 60 */           this.rs.executeSql(str6);
/* 61 */           this.rs.next();
/* 62 */           String str10 = "" + this.rs.getInt(1);
/* 63 */           hashMap.put("result" + b, str10);
/* 64 */           hashMap.put("name", str10);
/*    */         } 
/* 66 */         arrayList.add(hashMap);
/*    */       } 
/*    */     } else {
/* 69 */       hashMap = new HashMap<Object, Object>();
/* 70 */       hashMap.put("title", String.valueOf(str2));
/* 71 */       for (byte b = 1; b < 13; b++) {
/* 72 */         String str8 = "" + str2 + "-" + Util.add0(b, 2) + "-01";
/* 73 */         String str9 = "" + str2 + "-" + Util.add0(b, 2) + "-31";
/* 74 */         str5 = " and (contractstartdate >='" + str8 + "' and contractstartdate <= '" + str9 + "')";
/* 75 */         str5 = str5 + " and t1.contracttypeid=" + str3;
/* 76 */         str6 = "select count(t1.id) resultcount from HrmContract t1 left join HrmResource t2 on t1.contractman = t2.id where 3 = 3 " + str5 + str7;
/* 77 */         this.rs.executeSql(str6);
/* 78 */         this.rs.next();
/* 79 */         String str10 = "" + this.rs.getInt(1);
/* 80 */         String str11 = "" + b + Util.toScreen("月", paramUser.getLanguage(), "0");
/* 81 */         hashMap.put("result" + b, str10);
/* 82 */         hashMap.put("title" + b, str11);
/*    */       } 
/* 84 */       arrayList.add(hashMap);
/*    */     } 
/* 86 */     return (List)arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmRpContractTimeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */