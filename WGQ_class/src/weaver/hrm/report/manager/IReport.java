package weaver.hrm.report.manager;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import weaver.hrm.User;

public interface IReport {
  List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/IReport.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */