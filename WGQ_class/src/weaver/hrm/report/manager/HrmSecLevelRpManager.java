/*    */ package weaver.hrm.report.manager;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.appdetach.AppDetachComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSecLevelRpManager
/*    */   extends ReportManager
/*    */   implements IReport
/*    */ {
/*    */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 24 */     String str1 = paramMap.get("fromdate");
/* 25 */     String str2 = paramMap.get("enddate");
/* 26 */     String str3 = paramMap.get("department");
/* 27 */     String str4 = paramMap.get("location");
/* 28 */     String str5 = paramMap.get("status");
/*    */     
/* 30 */     int i = 0;
/* 31 */     String str6 = "";
/*    */     
/* 33 */     if (str5.equals("")) {
/* 34 */       str5 = "8";
/*    */     }
/*    */     
/* 37 */     if (!str1.equals("")) {
/* 38 */       str6 = str6 + " and startdate>='" + str1 + "'";
/*    */     }
/* 40 */     if (!str2.equals("")) {
/* 41 */       str6 = str6 + " and (startdate<='" + str2 + "' or startdate is null)";
/*    */     }
/* 43 */     if (!str4.equals("")) {
/* 44 */       str6 = str6 + " and locationid =" + str4;
/*    */     }
/* 46 */     if (!str3.equals("")) {
/* 47 */       str6 = str6 + " and departmentid =" + str3;
/*    */     }
/* 49 */     if (!str5.equals("") && !str5.equals("9")) {
/* 50 */       if (str5.equals("8")) {
/* 51 */         str6 = str6 + " and status <= 3";
/*    */       } else {
/* 53 */         str6 = str6 + " and status =" + str5;
/*    */       } 
/*    */     }
/*    */     
/* 57 */     String str7 = AppDetachComInfo.getInnerResourceSql();
/*    */     
/* 59 */     str6 = str6 + " and " + str7;
/*    */     
/* 61 */     String str8 = "";
/* 62 */     String str9 = "";
/* 63 */     if (str6.equals("")) {
/* 64 */       str9 = "select count(*)  from HrmResource  where seclevel is not null";
/*    */     } else {
/* 66 */       str9 = "select count(*)  from HrmResource  where seclevel is not null" + str6;
/*    */     } 
/* 68 */     this.rs.executeSql(str9);
/* 69 */     this.rs.next();
/* 70 */     i = this.rs.getInt(1);
/*    */     
/* 72 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 73 */     HashMap<Object, Object> hashMap = null;
/*    */     
/* 75 */     if (i != 0) {
/* 76 */       if (str6.equals("")) {
/* 77 */         str8 = "select  COUNT(*)   resultcount,seclevel from HrmResource   where seclevel is not null group by seclevel order by seclevel";
/*    */       } else {
/* 79 */         str8 = "select  COUNT(*)   resultcount,seclevel from HrmResource   where seclevel is not null " + str6 + " group by seclevel order by seclevel";
/*    */       } 
/* 81 */       this.rs.executeSql(str8);
/* 82 */       this.rs.first();
/*    */       do {
/* 84 */         int j = this.rs.getInt(1);
/* 85 */         String str = this.rs.getString(2);
/*    */         
/* 87 */         if (j == 0)
/* 88 */           continue;  hashMap = new HashMap<Object, Object>();
/* 89 */         hashMap.put("title", str + " " + SystemEnv.getHtmlLabelName(15872, paramUser.getLanguage()));
/* 90 */         hashMap.put("result", String.valueOf(j));
/* 91 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 92 */         hashMap.put("total", String.valueOf(i));
/* 93 */         arrayList.add(hashMap);
/*    */       }
/* 95 */       while (this.rs.next());
/*    */     } 
/* 97 */     return (List)arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmSecLevelRpManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */