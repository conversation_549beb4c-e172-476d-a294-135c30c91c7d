/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.common.database.dialect.DbDialectFactory;
/*     */ import weaver.hrm.common.database.dialect.DialectUtil;
/*     */ import weaver.hrm.job.JobCallComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmJobCallRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  28 */     String str1 = paramMap.get("fromdate");
/*  29 */     String str2 = paramMap.get("enddate");
/*  30 */     String str3 = paramMap.get("location");
/*  31 */     String str4 = paramMap.get("status");
/*     */     
/*  33 */     int i = 0;
/*  34 */     int j = 0;
/*  35 */     String str5 = "";
/*     */     
/*  37 */     if (!str1.equals("")) {
/*  38 */       str5 = str5 + " and t1.startdate>='" + str1 + "'";
/*     */     }
/*  40 */     if (!str2.equals("")) {
/*  41 */       str5 = str5 + " and (t1.startdate<='" + str2 + "' or t1.startdate is null)";
/*     */     }
/*     */     
/*  44 */     if (!str3.equals("")) {
/*  45 */       str5 = str5 + " and t1.locationid =" + str3;
/*     */     }
/*  47 */     if (!str4.equals("") && !str4.equals("9")) {
/*  48 */       if (str4.equals("8")) {
/*  49 */         str5 = str5 + " and t1.status <= 3";
/*     */       } else {
/*  51 */         str5 = str5 + " and t1.status =" + str4;
/*     */       } 
/*     */     }
/*     */     
/*  55 */     String str6 = AppDetachComInfo.getInnerResourceSql("t1");
/*     */     
/*  57 */     str5 = str5 + " and " + str6;
/*     */     
/*  59 */     String str7 = "select count(id) from HrmResource t1 where (t1.accounttype is null or t1.accounttype=0) and 1=1" + str5;
/*  60 */     this.rs.executeSql(str7);
/*  61 */     this.rs.next();
/*  62 */     i = this.rs.getInt(1);
/*     */     
/*  64 */     String str8 = "";
/*  65 */     String str9 = "";
/*  66 */     if (this.rs.getDBType().equals("oracle")) {
/*     */       
/*  68 */       str9 = "nvl(t1.jobcall,0)";
/*     */     }
/*  70 */     else if (this.rs.getDBType().equals("db2")) {
/*     */       
/*  72 */       str9 = "coalesce(t1.jobcall,0)";
/*  73 */     } else if (DialectUtil.isMySql(this.rs.getDBType())) {
/*  74 */       str9 = DbDialectFactory.get(this.rs.getDBType()).isNull("t1.jobcall", Integer.valueOf(0));
/*     */     } else {
/*  76 */       str9 = "ISNULL(t1.jobcall,0)";
/*     */     } 
/*  78 */     if (str5.equals("")) {
/*     */       
/*  80 */       str8 = "select " + str9 + " jobcall,COUNT(distinct t1.id) resultcount from HrmResource  t1 where (t1.accounttype is null or t1.accounttype=0) group by t1.jobcall";
/*     */     } else {
/*     */       
/*  83 */       str8 = "select " + str9 + " jobcall,COUNT(distinct t1.id) resultcount from HrmResource  t1 where (t1.accounttype is null or t1.accounttype=0) " + str5 + " group by t1.jobcall";
/*  84 */     }  str8 = "select a.jobcall, sum(a.resultcount) resultcount from (" + str8 + ") a group by a.jobcall";
/*  85 */     this.rs.executeSql(str8);
/*     */     
/*  87 */     while (this.rs.next()) {
/*  88 */       int k = this.rs.getInt(2);
/*  89 */       j += k;
/*     */     } 
/*  91 */     this.rs.beforFirst();
/*  92 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  93 */     HashMap<Object, Object> hashMap = null;
/*  94 */     if (i != 0) {
/*  95 */       while (this.rs.next()) {
/*  96 */         String str10 = this.rs.getString(1);
/*  97 */         int k = this.rs.getInt(2);
/*     */         
/*  99 */         hashMap = new HashMap<>();
/* 100 */         String str11 = "";
/* 101 */         if ("0".equals(str10)) {
/* 102 */           str11 = SystemEnv.getHtmlLabelName(15863, paramUser.getLanguage());
/*     */         } else {
/*     */           try {
/* 105 */             str11 = Util.toScreen((new JobCallComInfo()).getJobCallname(str10), paramUser.getLanguage());
/* 106 */           } catch (Exception exception) {
/* 107 */             exception.printStackTrace();
/*     */           } 
/*     */         } 
/* 110 */         hashMap.put("title", str11);
/* 111 */         hashMap.put("result", String.valueOf(k));
/* 112 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(k), String.valueOf(i)));
/* 113 */         hashMap.put("total", String.valueOf(i));
/* 114 */         arrayList.add(hashMap);
/*     */       } 
/*     */     }
/* 117 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmJobCallRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */