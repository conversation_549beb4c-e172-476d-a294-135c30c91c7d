/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.contract.ContractTypeComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmRpContractTypeManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  23 */     String str1 = paramMap.get("fromdate");
/*  24 */     String str2 = paramMap.get("enddate");
/*  25 */     String str3 = paramMap.get("fromTodate");
/*  26 */     String str4 = paramMap.get("endTodate");
/*  27 */     String str5 = paramMap.get("from");
/*  28 */     String str6 = paramMap.get("subcompanyid1");
/*  29 */     String str7 = paramMap.get("depid");
/*     */     
/*  31 */     int i = 0;
/*  32 */     float f = 0.0F;
/*  33 */     String str8 = "";
/*     */     
/*  35 */     if (!str1.equals("")) {
/*  36 */       str8 = str8 + " and t1.contractstartdate>='" + str1 + "'";
/*     */     }
/*  38 */     if (!str2.equals("")) {
/*  39 */       str8 = str8 + " and (t1.contractstartdate<='" + str2 + "' or t1.contractstartdate is null)";
/*     */     }
/*  41 */     if (!str3.equals("")) {
/*  42 */       str8 = str8 + " and t1.contractenddate>='" + str3 + "'";
/*     */     }
/*  44 */     if (!str4.equals("")) {
/*  45 */       if (this.rs.getDBType().equals("oracle")) {
/*  46 */         str8 = str8 + " and (t1.contractenddate<='" + str4 + "' and t1.contractenddate is not null)";
/*     */       } else {
/*  48 */         str8 = str8 + " and (t1.contractenddate<='" + str4 + "' and t1.contractenddate is not null and t1.contractenddate <> '')";
/*     */       } 
/*     */     }
/*  51 */     if (!str7.equals("")) {
/*  52 */       str8 = str8 + " and t1.contractman= t2.id and t2.departmentid = " + str7 + "";
/*     */     }
/*  54 */     if (!str6.equals("")) {
/*  55 */       str8 = str8 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str6 + ")";
/*     */     }
/*  57 */     if (str5.equals("type")) {
/*  58 */       str6 = String.valueOf(paramUser.getUserSubCompany1());
/*  59 */       str8 = str8 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str6 + ")";
/*     */     } 
/*  61 */     String str9 = "select count(t1.id) from HrmContract t1,HrmResource t2 where (t2.accounttype is null or t2.accounttype=0) and t1.contractman = t2.id " + str8;
/*  62 */     this.rs.executeSql(str9);
/*  63 */     this.rs.next();
/*  64 */     i = this.rs.getInt(1);
/*     */     
/*  66 */     String str10 = "select t3.id resultid, count(t1.id) resultcount from HrmContract t1,HrmResource t2,HrmContractType t3 where (t2.accounttype is null or t2.accounttype=0) and t1.contractman = t2.id and  t1.contracttypeid = t3.id " + str8 + " group by t3.id";
/*  67 */     this.rs.executeSql(str10);
/*     */     
/*  69 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  70 */     HashMap<Object, Object> hashMap = null;
/*     */     
/*  72 */     this.rs.first();
/*  73 */     int j = 0;
/*  74 */     if (i != 0) {
/*     */       do {
/*  76 */         String str11 = Tools.vString(this.rs.getString(1));
/*  77 */         int k = this.rs.getInt(2);
/*  78 */         if (k < 0) {
/*  79 */           k = 0;
/*     */         }
/*  81 */         j += k;
/*  82 */         f = k * 100.0F / i;
/*  83 */         f = (int)(f * 100.0F) / 100.0F;
/*     */         
/*  85 */         if (!Tools.isNotNull(str11))
/*  86 */           continue;  hashMap = new HashMap<Object, Object>();
/*  87 */         String str12 = "";
/*     */         try {
/*  89 */           str12 = Util.toScreen((new ContractTypeComInfo()).getContractTypename(str11), paramUser.getLanguage());
/*  90 */         } catch (Exception exception) {
/*  91 */           exception.printStackTrace();
/*     */         } 
/*     */         
/*  94 */         hashMap.put("title", "<a href=\"/hrm/contract/contracttype/HrmContractTypeEdit.jsp?isdialog=1&id=" + str11 + "\">" + str12 + "</a>");
/*  95 */         hashMap.put("result", String.valueOf(k));
/*  96 */         hashMap.put("result_url", "<a href=\"HrmRpContractDetail.jsp?isdialog=1&typeid=" + str11 + "\">" + String.valueOf(k) + "</a>");
/*  97 */         hashMap.put("percent", String.valueOf(f));
/*  98 */         hashMap.put("total", String.valueOf(i));
/*  99 */         arrayList.add(hashMap);
/*     */       
/*     */       }
/* 102 */       while (this.rs.next());
/*     */     }
/* 104 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmRpContractTypeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */