/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmRpContractManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  25 */     String str1 = Util.null2String(paramMap.get("fromdate"));
/*  26 */     String str2 = Util.null2String(paramMap.get("enddate"));
/*  27 */     String str3 = Util.null2String(paramMap.get("fromTodate"));
/*  28 */     String str4 = Util.null2String(paramMap.get("endTodate"));
/*  29 */     String str5 = Util.null2String(paramMap.get("from"));
/*  30 */     String str6 = Util.null2String(paramMap.get("subcompanyid1"));
/*  31 */     String str7 = Util.null2String(paramMap.get("departmentid"));
/*  32 */     String str8 = Util.null2String(paramMap.get("departmentname"));
/*  33 */     String str9 = Util.null2String(paramMap.get("workstatus"));
/*     */ 
/*     */ 
/*     */     
/*  37 */     int i = 0;
/*  38 */     float f = 0.0F;
/*  39 */     String str10 = "";
/*     */     
/*  41 */     if (!str1.equals("")) {
/*  42 */       str10 = str10 + " and t1.contractstartdate>='" + str1 + "'";
/*     */     }
/*  44 */     if (!str2.equals("")) {
/*  45 */       str10 = str10 + " and (t1.contractstartdate<='" + str2 + "' or t1.contractstartdate is null)";
/*     */     }
/*  47 */     if (!str3.equals("")) {
/*  48 */       str10 = str10 + " and t1.contractenddate>='" + str3 + "'";
/*     */     }
/*  50 */     if (!str4.equals("")) {
/*  51 */       if (this.rs.getDBType().equals("oracle")) {
/*  52 */         str10 = str10 + " and (t1.contractenddate<='" + str4 + "' and t1.contractenddate is not null)";
/*     */       } else {
/*  54 */         str10 = str10 + " and (t1.contractenddate<='" + str4 + "' and t1.contractenddate is not null and t1.contractenddate <> '')";
/*     */       } 
/*     */     }
/*     */     
/*  58 */     if (!str9.equals("") && !str9.equals("9")) {
/*  59 */       if (str9.equals("8")) {
/*  60 */         str10 = str10 + " and t2.status <= 3  ";
/*     */       } else {
/*  62 */         str10 = str10 + " and t2.status =" + str9 + " ";
/*     */       } 
/*     */     }
/*     */     
/*  66 */     if (!str8.equals("")) {
/*  67 */       str10 = str10 + " and t3.departmentname  like '%" + str8 + "%' ";
/*     */     }
/*  69 */     if (!str7.equals("")) {
/*  70 */       str10 = str10 + " and t3.id in    (" + str7 + ")";
/*     */     }
/*  72 */     if (!str6.equals("") && str5.equals("")) {
/*  73 */       str10 = str10 + " and t2.id in (select id from HrmResource where subcompanyid1 in   (" + str6 + "))";
/*  74 */     } else if (str6.equals("") && str5.equals("contract")) {
/*  75 */       str6 = String.valueOf(paramUser.getUserSubCompany1());
/*  76 */       str10 = str10 + " and t2.id in (select id from HrmResource where subcompanyid1 in  (" + str6 + ")) ";
/*  77 */     } else if (str5.equals("contract")) {
/*  78 */       str10 = str10 + " and t2.id in (select id from HrmResource where subcompanyid1 in  (" + str6 + ")) ";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  84 */     String str11 = "select count(t1.id) from HrmContract t1,HrmResource t2,HrmDepartment t3  where (t2.accounttype is null or t2.accounttype=0) and t1.contractman = t2.id and t2.departmentid = t3.id " + str10;
/*  85 */     this.rs.executeSql(str11);
/*  86 */     this.rs.next();
/*  87 */     i = this.rs.getInt(1);
/*     */     
/*  89 */     String str12 = "select t3.id resultid, count(t1.id) resultcount from HrmContract t1,HrmResource t2,HrmDepartment t3 where (t2.accounttype is null or t2.accounttype=0) and t1.contractman = t2.id and t2.departmentid = t3.id " + str10 + " group by t3.id order by resultcount desc";
/*  90 */     this.rs.executeSql(str12);
/*  91 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  92 */     HashMap<Object, Object> hashMap = null;
/*     */     
/*  94 */     this.rs.first();
/*     */     
/*  96 */     int j = 0;
/*  97 */     if (i != 0) {
/*     */       do {
/*  99 */         String str13 = Tools.vString(this.rs.getString(1));
/* 100 */         int k = this.rs.getInt(2);
/* 101 */         if (k < 0) {
/* 102 */           k = 0;
/*     */         }
/* 104 */         j += k;
/* 105 */         f = k * 100.0F / i;
/* 106 */         f = (int)(f * 100.0F) / 100.0F;
/*     */         
/* 108 */         if (!Tools.isNotNull(str13))
/* 109 */           continue;  hashMap = new HashMap<>();
/* 110 */         String str14 = "";
/*     */         try {
/* 112 */           str14 = Util.toScreen((new DepartmentComInfo()).getDepartmentname(str13), paramUser.getLanguage());
/* 113 */         } catch (Exception exception) {
/* 114 */           exception.printStackTrace();
/*     */         } 
/*     */         
/* 117 */         hashMap.put("deptid", str13);
/* 118 */         hashMap.put("deptname", str14);
/* 119 */         hashMap.put("title", "<a href=\"/hrm/company/HrmDepartmentDsp.jsp?id=" + str13 + "\" target=\"_fullwindow\">" + str14 + "</a>");
/* 120 */         hashMap.put("resultTitle", "<a href=\"/hrm/contract/contract/HrmContract.jsp?isshow=1&subcompanyid1=&departmentid=" + str13 + "\" target=\"_fullwindow\">" + k + "</a>");
/* 121 */         hashMap.put("result", String.valueOf(k));
/* 122 */         hashMap.put("percent", String.valueOf(f));
/* 123 */         hashMap.put("total", String.valueOf(i));
/* 124 */         arrayList.add(hashMap);
/*     */       
/*     */       }
/* 127 */       while (this.rs.next());
/*     */     }
/* 129 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public String getResultStr(String paramString1, String paramString2) {
/* 133 */     return paramString2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmRpContractManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */