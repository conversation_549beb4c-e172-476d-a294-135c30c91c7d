/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.job.UseKindComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmUsekindRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  26 */     String str1 = paramMap.get("fromdate");
/*  27 */     String str2 = paramMap.get("enddate");
/*  28 */     String str3 = paramMap.get("department");
/*  29 */     String str4 = paramMap.get("location");
/*  30 */     String str5 = paramMap.get("status");
/*     */     
/*  32 */     int i = 0;
/*  33 */     String str6 = "";
/*  34 */     String str7 = "";
/*     */     
/*  36 */     if (str5.equals("")) {
/*  37 */       str5 = "8";
/*     */     }
/*     */     
/*  40 */     if (!str1.equals("")) {
/*  41 */       str6 = str6 + " and t1.startdate>='" + str1 + "'";
/*  42 */       if (str7.equals("")) {
/*  43 */         str7 = " where startdate>='" + str1 + "'";
/*     */       } else {
/*     */         
/*  46 */         str7 = str7 + " and startdate>='" + str1 + "'";
/*     */       } 
/*     */     } 
/*  49 */     if (!str2.equals("")) {
/*  50 */       str6 = str6 + " and (t1.startdate<='" + str2 + "' or t1.startdate is null)";
/*  51 */       if (str7.equals("")) {
/*  52 */         str7 = " where (startdate<='" + str2 + "' or startdate is null)";
/*     */       } else {
/*     */         
/*  55 */         str7 = str7 + " and (startdate<='" + str2 + "' or startdate is null)";
/*     */       } 
/*     */     } 
/*     */     
/*  59 */     if (!str4.equals("")) {
/*  60 */       str6 = str6 + " and t1.locationid =" + str4;
/*  61 */       if (str7.equals("")) {
/*  62 */         str7 = " where locationid =" + str4;
/*     */       } else {
/*     */         
/*  65 */         str7 = str7 + " and locationid =" + str4;
/*     */       } 
/*     */     } 
/*  68 */     if (!str3.equals("")) {
/*  69 */       str6 = str6 + " and t1.departmentid =" + str3;
/*  70 */       if (str7.equals("")) {
/*  71 */         str7 = " where departmentid =" + str3;
/*     */       } else {
/*     */         
/*  74 */         str7 = str7 + " and departmentid =" + str3;
/*     */       } 
/*     */     } 
/*     */     
/*  78 */     if (!str5.equals("") && !str5.equals("9")) {
/*  79 */       if (str5.equals("8")) {
/*  80 */         str6 = str6 + " and t1.status <= 3";
/*  81 */         if (str7.equals("")) {
/*  82 */           str7 = " where status <= 3";
/*     */         } else {
/*  84 */           str7 = str7 + " and status <= 3";
/*     */         } 
/*     */       } else {
/*  87 */         str6 = str6 + " and t1.status =" + str5;
/*     */         
/*  89 */         if (str7.equals("")) {
/*  90 */           str7 = " where status =" + str5;
/*     */         } else {
/*  92 */           str7 = str7 + " and status =" + str5;
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*  97 */     String str8 = AppDetachComInfo.getInnerResourceSql("t1");
/*     */     
/*  99 */     str6 = str6 + " and " + str8;
/* 100 */     str8 = AppDetachComInfo.getInnerResourceSql();
/* 101 */     if (str7.equals("")) {
/* 102 */       str7 = " where " + str8;
/*     */     } else {
/* 104 */       str7 = str7 + " and " + str8;
/*     */     } 
/*     */     
/* 107 */     String str9 = "";
/* 108 */     String str10 = "";
/* 109 */     if (str7.equals("")) {
/* 110 */       str10 = "select count(*)  from HrmResource where accounttype is null or accounttype=0 ";
/*     */     } else {
/* 112 */       str10 = "select count(*)  from HrmResource " + str7 + " and (accounttype is null or accounttype=0)";
/*     */     } 
/* 114 */     this.rs.executeSql(str10);
/* 115 */     this.rs.next();
/* 116 */     i = this.rs.getInt(1);
/*     */     
/* 118 */     if (str6.equals("")) {
/* 119 */       str9 = "select count(*) resultcount from HrmResource t1 where (t1.accounttype is null or t1.accounttype=0) and t1.usekind is null or t1.usekind ='' or t1.usekind='0'";
/*     */     } else {
/* 121 */       str9 = "select count(*) resultcount from HrmResource t1 where (t1.accounttype is null or t1.accounttype=0) and (t1.usekind is null or t1.usekind ='' or t1.usekind='0')" + str6;
/*     */     } 
/* 123 */     this.rs.executeSql(str9);
/* 124 */     this.rs.next();
/* 125 */     int j = this.rs.getInt(1);
/*     */     
/* 127 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 128 */     HashMap<Object, Object> hashMap = null;
/* 129 */     if (j != 0) {
/* 130 */       hashMap = new HashMap<Object, Object>();
/* 131 */       hashMap.put("title", SystemEnv.getHtmlLabelName(15863, paramUser.getLanguage()));
/* 132 */       hashMap.put("result", String.valueOf(j));
/* 133 */       hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 134 */       hashMap.put("total", String.valueOf(i));
/* 135 */       arrayList.add(hashMap);
/*     */     } 
/* 137 */     if (i != 0) {
/* 138 */       if (str6.equals("")) {
/* 139 */         str9 = "select  COUNT(*)   resultcount,t2.id usekindid from HrmResource t1 ,HrmUseKind t2  where (t1.accounttype is null or t1.accounttype=0) and t1.usekind = t2.id group by t2.id order by t2.id ";
/*     */       } else {
/* 141 */         str9 = "select  COUNT(*)   resultcount,t2.id usekindid from HrmResource t1 ,HrmUseKind t2  where (t1.accounttype is null or t1.accounttype=0) and t1.usekind = t2.id " + str6 + " group by t2.id order by t2.id ";
/*     */       } 
/* 143 */       this.rs.executeSql(str9);
/*     */       
/* 145 */       while (this.rs.next()) {
/* 146 */         j = this.rs.getInt(1);
/* 147 */         String str = this.rs.getString(2);
/* 148 */         if (j != 0) {
/* 149 */           hashMap = new HashMap<Object, Object>();
/* 150 */           String str11 = "";
/*     */           try {
/* 152 */             str11 = Util.toScreen((new UseKindComInfo()).getUseKindname(str), paramUser.getLanguage());
/* 153 */           } catch (Exception exception) {
/* 154 */             exception.printStackTrace();
/*     */           } 
/* 156 */           hashMap.put("title", str11);
/* 157 */           hashMap.put("result", String.valueOf(j));
/* 158 */           hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 159 */           hashMap.put("total", String.valueOf(i));
/* 160 */           arrayList.add(hashMap);
/*     */         } 
/*     */       } 
/*     */     } 
/* 164 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmUsekindRpManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */