/*      */ package weaver.hrm.report.manager;
/*      */ 
/*      */ import com.engine.kq.wfset.util.KQ122Util;
/*      */ import com.engine.kq.wfset.util.KQSignUtil;
/*      */ import java.text.ParseException;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import weaver.common.DateUtil;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.framework.BaseDao;
/*      */ import weaver.framework.BaseManager;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.attendance.domain.HrmScheduleApplication;
/*      */ import weaver.hrm.attendance.domain.ScheduleApplicationRule;
/*      */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*      */ import weaver.hrm.attendance.manager.HrmScheduleApplicationManager;
/*      */ import weaver.hrm.report.dao.HrmReportDao;
/*      */ import weaver.hrm.report.domain.HrmReport;
/*      */ import weaver.hrm.schedule.domain.HrmSchedule;
/*      */ import weaver.hrm.schedule.domain.HrmScheduleDate;
/*      */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*      */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public abstract class HrmReportManager
/*      */   extends BaseManager<HrmReport>
/*      */ {
/*      */   protected boolean sortForResult;
/*      */   protected List<Map<String, Object>> personList;
/*      */   protected Map<String, String> indexMap;
/*      */   protected Map<String, List<HrmReport>> leaveMap;
/*      */   protected Map<String, List<HrmReport>> evectionMap;
/*      */   protected Map<String, List<HrmReport>> outMap;
/*      */   protected Map<String, List<HrmReport>> otherMap;
/*      */   protected Map<String, List<HrmReport>> overTimeMap;
/*      */   protected HrmReport bean;
/*      */   private boolean needInit = true;
/*   63 */   private HrmReportDao dao = null;
/*      */   
/*   65 */   protected SignType type = null;
/*      */   
/*      */   private static final String BEGIN_SIGN_TIME = "00:00";
/*      */   
/*      */   private static final String END_SIGN_TIME = "23:59";
/*      */   
/*      */   private int lanId;
/*      */   
/*      */   private String showInfo;
/*      */   
/*   75 */   protected long time = 0L;
/*      */   
/*   77 */   protected HrmScheduleManager manager = null;
/*      */   
/*      */   private boolean isDiffReport = false;
/*      */   
/*   81 */   protected Map<String, Map<String, HrmSchedule>> pSchedules = null;
/*      */   
/*   83 */   private String currentDate = "";
/*      */   
/*   85 */   private String nextDate = "";
/*      */   
/*   87 */   private int scheduleUnit = 0;
/*      */   
/*   89 */   protected HrmLeaveTypeColorManager leaveTypeColorManager = null;
/*      */   
/*   91 */   private KQ122Util kq122Util = new KQ122Util();
/*      */   private boolean isKQOpen = false;
/*      */   
/*      */   public enum SignType
/*      */   {
/*   96 */     SIGN_IN("signIn"),
/*   97 */     SIGN_OUT("signOut"),
/*   98 */     BE_LATE("beLate"),
/*   99 */     LEAVE_EARLY("leaveEarly"),
/*  100 */     NO_SIGN("noSign"),
/*  101 */     ABSENT("absent"),
/*  102 */     DIFF_REPORT("diffReport");
/*      */     
/*      */     public String name;
/*      */     
/*      */     SignType(String param1String1) {
/*  107 */       this.name = param1String1;
/*      */     }
/*      */     
/*      */     public String getName() {
/*  111 */       return this.name;
/*      */     }
/*      */   }
/*      */   
/*      */   public enum NoSignType {
/*  116 */     SING_IN, SING_OUT, SING_TIME;
/*      */   }
/*      */   
/*      */   public HrmReportManager(User paramUser, SignType paramSignType) {
/*  120 */     setSignType(paramSignType);
/*  121 */     setUser(paramUser);
/*  122 */     setSortForResult(true);
/*  123 */     this.bean = new HrmReport();
/*  124 */     this.dao = new HrmReportDao();
/*  125 */     setDao((BaseDao)this.dao);
/*  126 */     this.personList = new ArrayList<>();
/*  127 */     this.indexMap = new HashMap<>();
/*  128 */     this.lanId = (paramUser == null) ? 7 : paramUser.getLanguage();
/*  129 */     this.showInfo = SystemEnv.getHtmlLabelName(10000168, Util.getIntValue(this.lanId));
/*  130 */     this.scheduleUnit = getUnit();
/*  131 */     this.leaveTypeColorManager = new HrmLeaveTypeColorManager();
/*  132 */     this.isKQOpen = this.kq122Util.is122Open();
/*      */   }
/*      */   
/*      */   protected void setSignType(SignType paramSignType) {
/*  136 */     this.type = paramSignType;
/*  137 */     init();
/*      */   }
/*      */   
/*      */   private void init() {
/*  141 */     this.time = System.currentTimeMillis();
/*  142 */     this.currentDate = DateUtil.getCurrentDate();
/*  143 */     this.nextDate = DateUtil.addDate(this.currentDate, 1);
/*  144 */     this.pSchedules = new HashMap<>();
/*  145 */     switch (this.type) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       case null:
/*  159 */         this.isDiffReport = true;
/*      */         break;
/*      */     } 
/*      */   }
/*      */   
/*      */   public void setUser(User paramUser) {
/*  165 */     super.setUser(paramUser);
/*  166 */     this.manager = new HrmScheduleManager(paramUser);
/*  167 */     this.manager.setForSchedule(true);
/*      */   }
/*      */   
/*      */   public void setSortForResult(boolean paramBoolean) {
/*  171 */     this.sortForResult = paramBoolean;
/*      */   }
/*      */   
/*      */   public void setIndexMap(Map<String, String> paramMap) {
/*  175 */     this.indexMap = paramMap;
/*      */   }
/*      */   
/*      */   public Map<String, String> getIndexMap() {
/*  179 */     return this.indexMap;
/*      */   }
/*      */   
/*      */   public void setPersonList(List<Map<String, Object>> paramList) {
/*  183 */     this.personList = paramList;
/*      */   }
/*      */   
/*      */   public List<Map<String, Object>> getPersonList() {
/*  187 */     return this.personList;
/*      */   }
/*      */   
/*      */   public void setBean(HrmReport paramHrmReport) {
/*  191 */     this.bean = paramHrmReport;
/*      */   }
/*      */   
/*      */   public HrmReport getBean() {
/*  195 */     return this.bean;
/*      */   }
/*      */   
/*      */   protected boolean checkParam(String paramString1, String paramString2) {
/*  199 */     return (StringUtil.isNull(new String[] { paramString1, paramString2 }) || paramString1
/*  200 */       .compareTo(paramString2) > 0 || this.personList == null || this.personList
/*      */       
/*  202 */       .size() == 0 || this.indexMap == null || this.indexMap
/*      */       
/*  204 */       .isEmpty());
/*      */   }
/*      */   
/*      */   private void initBean(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, String paramString4) {
/*  208 */     this.bean.setFromDate(StringUtil.vString(paramString1));
/*  209 */     this.bean.setToDate(StringUtil.vString(paramString2));
/*  210 */     this.bean.setSubCompanyId(paramInt1);
/*  211 */     this.bean.setDepartmentId(paramInt2);
/*  212 */     this.bean.setResId(StringUtil.vString(paramString3));
/*  213 */     this.bean.setStatus(StringUtil.vString(paramString4));
/*      */     
/*  215 */     if (this.needInit) initReport();
/*      */   
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void initReport() {
/*  223 */     Map map = this.manager.getDatePersonInfo(this.bean.getFromDate(), this.bean.getToDate(), this.bean.getSubCompanyId(), this.bean.getDepartmentId(), this.bean.getResId(), this.bean.getStatus());
/*  224 */     this.personList = (List<Map<String, Object>>)map.get("personList");
/*  225 */     this.indexMap = (Map<String, String>)map.get("indexMap");
/*  226 */     this.leaveMap = initMap((Map)map.get("leaveMap"));
/*  227 */     this.evectionMap = initMap((Map)map.get("evectionMap"));
/*  228 */     this.outMap = initMap((Map)map.get("outMap"));
/*  229 */     this.otherMap = initMap((Map)map.get("otherMap"));
/*  230 */     if (this.isDiffReport)
/*      */     {
/*  232 */       this.overTimeMap = this.manager.getOverTimeMap(this.bean.getFromDate(), this.bean.getToDate(), this.bean.getSubCompanyId(), this.bean.getDepartmentId(), this.bean.getResId());
/*      */     }
/*  234 */     this.bean.setSubCompanyId(StringUtil.parseToInt((String)map.get("subCompanyId")));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<HrmSchedule> getScheduleSignList() {
/*  242 */     return this.dao.getScheduleSignList(this.bean, getSignType());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, HrmScheduleDate> getPersonDateMap(String paramString) {
/*  252 */     Map<String, HrmScheduleDate> map = null;
/*  253 */     boolean bool = (this.indexMap == null) ? true : StringUtil.parseToInt(this.indexMap.get(paramString));
/*  254 */     if (bool) {
/*  255 */       Map map1 = (this.personList == null) ? null : this.personList.get(bool);
/*  256 */       map = (map1 == null) ? null : (Map)map1.get("dateMap");
/*      */     } 
/*  258 */     return map;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void toMapOfRule(Map<String, Object> paramMap) {
/*      */     boolean bool;
/*  266 */     ArrayList<?> arrayList = new ArrayList();
/*  267 */     ArrayList arrayList1 = new ArrayList();
/*  268 */     List list1 = (List)paramMap.get("fDates");
/*  269 */     List list2 = (List)paramMap.get("sDates");
/*      */     
/*  271 */     if (list1 == null && list2 == null) {
/*      */       return;
/*      */     }
/*  274 */     if (list1 != null) {
/*  275 */       arrayList.addAll(list1);
/*      */     }
/*  277 */     if (list2 != null) {
/*  278 */       arrayList1.addAll(list2);
/*      */     }
/*  280 */     String str = (String)paramMap.get("signTime");
/*  281 */     double d1 = 0.0D;
/*  282 */     double d2 = 0.0D;
/*  283 */     if (arrayList != null && arrayList1 != null && arrayList.size() > 0 && arrayList1.size() > 0) {
/*  284 */       arrayList1.removeAll(arrayList);
/*      */       
/*  286 */       d1 = arrayList1.size();
/*  287 */       d2 = d1 / 60.0D;
/*  288 */     } else if (arrayList1 != null && arrayList1.size() > 0) {
/*  289 */       d1 = arrayList1.size();
/*  290 */       d2 = d1 / 60.0D;
/*      */     } 
/*  292 */     switch (this.type) {
/*      */       case SING_TIME:
/*  294 */         bool = false;
/*  295 */         if (StringUtil.isNotNull(str) && 
/*  296 */           str.length() >= 8) {
/*  297 */           bool = str.substring(6, 8).equals("00");
/*      */         }
/*      */         
/*  300 */         paramMap.put(SignType.BE_LATE + "_realHours", Double.valueOf(bool ? d1 : (d1 - 1.0D)));
/*      */         break;
/*      */       case null:
/*  303 */         paramMap.put(SignType.ABSENT + "_realHours", Double.valueOf(StringUtil.round(d2, 1)));
/*      */         break;
/*      */       case null:
/*  306 */         paramMap.put(SignType.LEAVE_EARLY + "_realHours", Double.valueOf(d1));
/*      */         break;
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   private Map<String, Object> toMap(HrmSchedule paramHrmSchedule) {
/*  313 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  314 */     hashMap.put("subCompanyId", String.valueOf(paramHrmSchedule.getSubCompanyId()));
/*  315 */     hashMap.put("departmentId", String.valueOf(paramHrmSchedule.getDepartmentId()));
/*  316 */     hashMap.put("departmentName", paramHrmSchedule.getDepartmentName());
/*  317 */     hashMap.put("resourceId", String.valueOf(paramHrmSchedule.getResourceId()));
/*  318 */     hashMap.put("resourceName", paramHrmSchedule.getResourceName());
/*  319 */     hashMap.put("statusName", getStatusName(paramHrmSchedule.getStatus()));
/*  320 */     hashMap.put("status", paramHrmSchedule.getStatus());
/*  321 */     hashMap.put("workcode", paramHrmSchedule.getWorkcode());
/*  322 */     hashMap.put("currentDate", paramHrmSchedule.getSignDate());
/*  323 */     hashMap.put("signId", String.valueOf(paramHrmSchedule.getId()));
/*  324 */     hashMap.put("signDate", paramHrmSchedule.getSignDate());
/*  325 */     hashMap.put("oldSignDate", paramHrmSchedule.getOldSignDate());
/*  326 */     hashMap.put("signTime", paramHrmSchedule.getSignTime());
/*  327 */     hashMap.put("clientAddress", paramHrmSchedule.getClientAddress());
/*  328 */     hashMap.put("scheduleName", paramHrmSchedule.get$ScheduleName());
/*  329 */     hashMap.put("signType", paramHrmSchedule.get$SignType());
/*  330 */     hashMap.put("signStartTime", paramHrmSchedule.get$SignStartTime());
/*  331 */     hashMap.put("_addr", paramHrmSchedule.getAddr());
/*  332 */     hashMap.put("longitude", paramHrmSchedule.getLongitude());
/*  333 */     hashMap.put("latitude", paramHrmSchedule.getLatitude());
/*  334 */     hashMap.put("mfer", this.type.getName());
/*  335 */     hashMap.put("fDates", paramHrmSchedule.getFDates());
/*  336 */     hashMap.put("sDates", paramHrmSchedule.getSDates());
/*  337 */     hashMap.put("isSchedulePerson", Boolean.valueOf(paramHrmSchedule.isSchedulePerson()));
/*  338 */     hashMap.put("detailWorkTimes", paramHrmSchedule.getWorkTimes());
/*  339 */     hashMap.put("detailSignTimes", paramHrmSchedule.getSignTimes());
/*      */     
/*  341 */     toMapOfRule((Map)hashMap);
/*      */     
/*  343 */     String str1 = "";
/*  344 */     String str2 = "";
/*  345 */     if (StringUtil.isNull(paramHrmSchedule.getSignFrom()) || paramHrmSchedule.getSignFrom().equalsIgnoreCase("pc")) {
/*  346 */       str2 = "";
/*      */     }
/*  348 */     else if (StringUtil.isNull(new String[] { paramHrmSchedule.getLongitude(), paramHrmSchedule.getLatitude() })) {
/*  349 */       str2 = "";
/*      */     } else {
/*  351 */       str2 = StringUtil.vString(paramHrmSchedule.getAddr(), this.showInfo);
/*      */     } 
/*      */     
/*  354 */     if (StringUtil.isNotNull(str2))
/*      */     {
/*  356 */       str1 = str2;
/*      */     }
/*  358 */     hashMap.put("addr", str1);
/*  359 */     hashMap.put("addrDetail", str2.equals(this.showInfo) ? (paramHrmSchedule.getLongitude() + "," + paramHrmSchedule.getLatitude()) : str2);
/*  360 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getStatusName(String paramString) {
/*  369 */     String str = "";
/*  370 */     if (StringUtil.isNotNull(paramString)) {
/*  371 */       if (paramString.equals("0")) {
/*  372 */         str = SystemEnv.getHtmlLabelName(15710, this.lanId);
/*  373 */       } else if (paramString.equals("1")) {
/*  374 */         str = SystemEnv.getHtmlLabelName(15711, this.lanId);
/*  375 */       } else if (paramString.equals("2")) {
/*  376 */         str = SystemEnv.getHtmlLabelName(480, this.lanId);
/*  377 */       } else if (paramString.equals("3")) {
/*  378 */         str = SystemEnv.getHtmlLabelName(15844, this.lanId);
/*  379 */       } else if (paramString.equals("4")) {
/*  380 */         str = SystemEnv.getHtmlLabelName(6094, this.lanId);
/*  381 */       } else if (paramString.equals("5")) {
/*  382 */         str = SystemEnv.getHtmlLabelName(6091, this.lanId);
/*  383 */       } else if (paramString.equals("6")) {
/*  384 */         str = SystemEnv.getHtmlLabelName(6092, this.lanId);
/*  385 */       } else if (paramString.equals("7")) {
/*  386 */         str = SystemEnv.getHtmlLabelName(2245, this.lanId);
/*  387 */       } else if (paramString.equals("10")) {
/*  388 */         str = SystemEnv.getHtmlLabelName(1831, this.lanId);
/*      */       } 
/*      */     }
/*  391 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected void updateData() {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean timeIsIn(String paramString1, String paramString2, String paramString3) {
/*  406 */     String str1 = this.currentDate + " " + paramString1;
/*  407 */     String str2 = this.currentDate + " " + paramString2;
/*  408 */     boolean bool = false;
/*  409 */     if (paramString2.compareTo(paramString3) > 0) {
/*  410 */       str1 = ((paramString1.compareTo(paramString2) < 0) ? this.nextDate : this.currentDate) + " " + paramString1;
/*  411 */       bool = (DateUtil.isInDateRange(str1, str2, this.currentDate + " 23:59:59") || DateUtil.isInDateRange(str1, this.nextDate + " 00:00:00", this.nextDate + " " + paramString3));
/*      */     } else {
/*  413 */       bool = DateUtil.isInDateRange(str1, str2, this.currentDate + " " + paramString3);
/*      */     } 
/*  415 */     return bool;
/*      */   }
/*      */   
/*      */   protected void initHrmSchedule(Map<String, Object> paramMap, HrmSchedule paramHrmSchedule) {
/*  419 */     paramHrmSchedule.setResourceId(StringUtil.parseToInt((String)paramMap.get("resourceId")));
/*  420 */     paramHrmSchedule.setResourceName(StringUtil.vString(paramMap.get("resourceName")));
/*  421 */     paramHrmSchedule.setDepartmentId(StringUtil.parseToInt((String)paramMap.get("departmentId")));
/*  422 */     paramHrmSchedule.setDepartmentName(StringUtil.vString(paramMap.get("departmentName")));
/*  423 */     paramHrmSchedule.setSubCompanyId(StringUtil.parseToInt((String)paramMap.get("subCompanyId")));
/*  424 */     paramHrmSchedule.setStatus(StringUtil.vString(paramMap.get("pStatus")));
/*  425 */     paramHrmSchedule.setWorkcode(StringUtil.vString(paramMap.get("workcode")));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initHrmSchedule(HrmScheduleDate paramHrmScheduleDate, HrmSchedule paramHrmSchedule) {
/*  434 */     if (paramHrmScheduleDate.isSchedulePerson()) { initHrmScheduleByDdBean(paramHrmSchedule, paramHrmScheduleDate.getDBean()); }
/*  435 */     else { initHrmScheduleByDsBean(paramHrmSchedule, paramHrmScheduleDate.getSBean()); }
/*      */   
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void initHrmScheduleByDdBean(HrmSchedule paramHrmSchedule, HrmScheduleSetDetail paramHrmScheduleSetDetail) {
/*  444 */     List<String[]> list1 = null, list2 = null, list3 = null;
/*  445 */     if (this.manager != null) {
/*  446 */       list2 = this.manager.timeToList(paramHrmScheduleSetDetail.getSignTime());
/*  447 */       list1 = this.manager.timeToList(paramHrmScheduleSetDetail.getWorkTime());
/*  448 */       list3 = this.manager.timeToList(paramHrmScheduleSetDetail.getRestTime());
/*      */     } 
/*  450 */     paramHrmSchedule.setSchedulePerson(true);
/*  451 */     paramHrmSchedule.setDsSignType("");
/*  452 */     initHrmSchedule(paramHrmSchedule, paramHrmScheduleSetDetail.getField001Name(), (String)null, (String)null, list1, list2, list3);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void initHrmScheduleByDsBean(HrmSchedule paramHrmSchedule1, HrmSchedule paramHrmSchedule2) {
/*  461 */     ArrayList<String[]> arrayList1 = null, arrayList2 = null;
/*  462 */     arrayList1 = new ArrayList();
/*  463 */     arrayList2 = new ArrayList();
/*  464 */     if (paramHrmSchedule2 == null) {
/*      */       return;
/*      */     }
/*  467 */     if (paramHrmSchedule2.getSignType().equals("2")) {
/*  468 */       switch (this.type) {
/*      */         case SING_IN:
/*  470 */           arrayList2.add(new String[] { "00:00", paramHrmSchedule2.getSignStartTime() });
/*  471 */           arrayList2.add(new String[] { paramHrmSchedule2.getSignStartTime(), "23:59" });
/*      */           break;
/*      */         case null:
/*  474 */           arrayList2.add(new String[] { "00:00", paramHrmSchedule2.getOffDutyTimeAM() });
/*  475 */           arrayList2.add(new String[] { paramHrmSchedule2.getSignStartTime(), "23:59" });
/*      */           break;
/*      */         case SING_TIME:
/*  478 */           arrayList2.add(new String[] { paramHrmSchedule2.getOnDutyTimeAM(), paramHrmSchedule2.getOffDutyTimeAM() });
/*  479 */           arrayList2.add(new String[] { paramHrmSchedule2.getOnDutyTimePM(), paramHrmSchedule2.getOffDutyTimePM() });
/*      */           break;
/*      */         case null:
/*  482 */           arrayList2.add(new String[] { "00:00", paramHrmSchedule2.getOnDutyTimePM() });
/*  483 */           arrayList2.add(new String[] { paramHrmSchedule2.getSignStartTime(), "23:59" });
/*      */           break;
/*      */         case SING_OUT:
/*      */         case null:
/*  487 */           arrayList2.add(new String[] { paramHrmSchedule2.getOffDutyTimeAM(), paramHrmSchedule2.getOnDutyTimePM() });
/*  488 */           arrayList2.add(new String[] { paramHrmSchedule2.getOffDutyTimePM(), "23:59" });
/*      */           break;
/*      */       } 
/*  491 */       arrayList1.add(new String[] { paramHrmSchedule2.getOnDutyTimeAM(), paramHrmSchedule2.getOffDutyTimeAM() });
/*  492 */       arrayList1.add(new String[] { paramHrmSchedule2.getOnDutyTimePM(), paramHrmSchedule2.getOffDutyTimePM() });
/*      */     } else {
/*  494 */       arrayList2.add(new String[] { "00:00", "23:59" });
/*  495 */       arrayList1.add(new String[] { paramHrmSchedule2.getOnDutyTimeAM(), paramHrmSchedule2.getOffDutyTimePM() });
/*      */     } 
/*  497 */     paramHrmSchedule1.setSchedulePerson(false);
/*  498 */     paramHrmSchedule1.setDsSignType(paramHrmSchedule2.getSignType());
/*  499 */     paramHrmSchedule1.setOnDutyTimeAM(paramHrmSchedule2.getOnDutyTimeAM());
/*  500 */     paramHrmSchedule1.setOnDutyTimePM(paramHrmSchedule2.getOnDutyTimePM());
/*  501 */     paramHrmSchedule1.setOffDutyTimeAM(paramHrmSchedule2.getOffDutyTimeAM());
/*  502 */     paramHrmSchedule1.setOffDutyTimePM(paramHrmSchedule2.getOffDutyTimePM());
/*  503 */     initHrmSchedule(paramHrmSchedule1, (String)null, paramHrmSchedule2.getSignType(), paramHrmSchedule2.getSignStartTime(), (List<String[]>)arrayList1, (List<String[]>)arrayList2, (List<String[]>)null);
/*      */   }
/*      */   
/*      */   private void initHrmSchedule(HrmSchedule paramHrmSchedule, String paramString1, String paramString2, String paramString3, List<String[]> paramList1, List<String[]> paramList2, List<String[]> paramList3) {
/*  507 */     paramHrmSchedule.set$ScheduleName(StringUtil.vString(paramString1));
/*  508 */     paramHrmSchedule.set$ScheduleTitle(StringUtil.vString(paramString1));
/*  509 */     paramHrmSchedule.set$SignType(StringUtil.vString(paramString2));
/*  510 */     paramHrmSchedule.set$SignStartTime(StringUtil.vString(paramString3));
/*  511 */     paramHrmSchedule.setWorkTimes(paramList1);
/*  512 */     paramHrmSchedule.setSignTimes(paramList2);
/*  513 */     paramHrmSchedule.setRestTimes(paramList3);
/*      */     
/*  515 */     String str = String.valueOf(paramHrmSchedule.getResourceId());
/*  516 */     Map<Object, Object> map = (Map)this.pSchedules.get(str);
/*  517 */     if (map == null) map = new HashMap<>(); 
/*  518 */     map.put(paramHrmSchedule.getSignDate(), paramHrmSchedule);
/*  519 */     this.pSchedules.put(str, map);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void checkScheduleList(List<HrmSchedule> paramList) {
/*      */     byte b;
/*  527 */     ArrayList<HrmSchedule> arrayList = new ArrayList();
/*  528 */     switch (this.type) {
/*      */       case SING_TIME:
/*      */       case null:
/*  531 */         for (b = 0; b < paramList.size(); b++) {
/*  532 */           HrmSchedule hrmSchedule = paramList.get(b);
/*  533 */           boolean bool = isAllowAdd(hrmSchedule); boolean bool1 = !isOffset(hrmSchedule) ? true : false;
/*  534 */           if (bool && bool1) {
/*  535 */             arrayList.add(hrmSchedule);
/*      */           }
/*      */         } 
/*  538 */         paramList.clear();
/*  539 */         paramList.addAll(arrayList);
/*      */         break;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<Map<String, Object>> toScheduleList(Map<String, Map<String, List<HrmSchedule>>> paramMap) {
/*  551 */     ArrayList<Map<String, Object>> arrayList = new ArrayList();
/*  552 */     Iterator<Map.Entry> iterator = paramMap.entrySet().iterator();
/*  553 */     while (iterator.hasNext()) {
/*  554 */       Map.Entry entry = iterator.next();
/*  555 */       Iterator<Map.Entry> iterator1 = ((Map)entry.getValue()).entrySet().iterator();
/*  556 */       while (iterator1.hasNext()) {
/*  557 */         Map.Entry entry1 = iterator1.next();
/*  558 */         List<HrmSchedule> list = (List)entry1.getValue();
/*  559 */         if (list == null)
/*      */           continue; 
/*  561 */         checkScheduleList(list);
/*      */         
/*  563 */         for (HrmSchedule hrmSchedule : list) {
/*  564 */           arrayList.add(toMap(hrmSchedule));
/*      */         }
/*      */       } 
/*      */     } 
/*  568 */     runTime();
/*  569 */     return getScheduleList(arrayList, paramMap);
/*      */   }
/*      */ 
/*      */   
/*      */   private void runTime() {}
/*      */ 
/*      */   
/*      */   public List getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  577 */     return null;
/*      */   }
/*      */ 
/*      */   
/*      */   protected List<Map<String, Object>> getScheduleList(Map<String, String> paramMap) {
/*  582 */     String str1 = StringUtil.vString(paramMap.get("fromDate"));
/*  583 */     String str2 = StringUtil.vString(paramMap.get("toDate"));
/*  584 */     int i = StringUtil.parseToInt(paramMap.get("subCompanyId"));
/*  585 */     int j = StringUtil.parseToInt(paramMap.get("departmentId"));
/*  586 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  587 */     String str4 = StringUtil.vString(paramMap.get("status"));
/*  588 */     return getScheduleList(str1, str2, i, j, str3, str4);
/*      */   }
/*      */   
/*      */   protected List<Map<String, Object>> getScheduleList(List<Map<String, Object>> paramList, Map<String, Map<String, List<HrmSchedule>>> paramMap) {
/*  592 */     return paramList;
/*      */   }
/*      */   
/*      */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) {
/*  596 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, String.valueOf(paramInt3), "");
/*      */   }
/*      */   
/*      */   public List getScheduleList(HrmReport paramHrmReport) {
/*  600 */     setBean(paramHrmReport);
/*  601 */     return (paramHrmReport == null) ? new ArrayList() : getScheduleList(paramHrmReport.getFromDate(), paramHrmReport.getToDate(), paramHrmReport.getSubCompanyId(), paramHrmReport.getDepartmentId(), paramHrmReport.getResId(), paramHrmReport.getStatus());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  614 */     if (this.isKQOpen) {
/*  615 */       if (this.type == SignType.SIGN_IN)
/*  616 */         return KQSignUtil.getKQSignData(SignType.SIGN_IN, paramString3, paramString1, paramString2); 
/*  617 */       if (this.type == SignType.SIGN_OUT) {
/*  618 */         return KQSignUtil.getKQSignData(SignType.SIGN_OUT, paramString3, paramString1, paramString2);
/*      */       }
/*  620 */       return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, paramString3, "");
/*      */     } 
/*      */     
/*  623 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, paramString3, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, String paramString4) {
/*  636 */     initBean(paramString1, paramString2, paramInt1, paramInt2, paramString3, paramString4);
/*  637 */     if (checkParam(paramString1, paramString2)) return new ArrayList();
/*      */     
/*  639 */     if (this.type == SignType.DIFF_REPORT) {
/*  640 */       updateData();
/*  641 */       runTime();
/*  642 */       return this.personList;
/*      */     } 
/*  644 */     String str1 = "", str2 = "", str3 = "";
/*  645 */     HrmScheduleDate hrmScheduleDate = null;
/*  646 */     Map<String, HrmScheduleDate> map = null;
/*      */     
/*  648 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  649 */     List<HrmSchedule> list = getScheduleSignList();
/*  650 */     for (HrmSchedule hrmSchedule : list) {
/*  651 */       str1 = hrmSchedule.getSignDate();
/*  652 */       str2 = hrmSchedule.getSignTime();
/*  653 */       str3 = String.valueOf(hrmSchedule.getResourceId());
/*  654 */       if ((map = getPersonDateMap(str3)) == null)
/*  655 */         continue;  String str4 = str1, str5 = DateUtil.addDate(str1, -1);
/*  656 */       if ((hrmScheduleDate = map.get(str1)) == null || (!hrmScheduleDate.isWorkDay() && hrmScheduleDate.isSchedulePerson())) {
/*  657 */         boolean bool = true;
/*  658 */         if (hrmScheduleDate != null && hrmScheduleDate.isSchedulePerson()) {
/*  659 */           HrmScheduleDate hrmScheduleDate1 = map.get(str5);
/*  660 */           if (hrmScheduleDate1 != null && hrmScheduleDate1.isWorkDay()) {
/*  661 */             hrmScheduleDate = hrmScheduleDate1;
/*  662 */             HrmScheduleSetDetail hrmScheduleSetDetail = hrmScheduleDate.getDBean();
/*  663 */             if (hrmScheduleSetDetail != null) {
/*  664 */               String[] arrayOfString1 = this.manager.getAcrossDateTime(hrmScheduleSetDetail.getWorkTime());
/*  665 */               String[] arrayOfString2 = this.manager.getAcrossDateTime(hrmScheduleSetDetail.getSignTime());
/*  666 */               if (arrayOfString2 != null && arrayOfString2.length == 2 && arrayOfString1 != null && arrayOfString1.length == 2) {
/*  667 */                 String str6 = str1 + " " + str2;
/*  668 */                 String str7 = str5 + " " + arrayOfString2[0] + ":00";
/*  669 */                 String str8 = str1 + " " + arrayOfString2[1] + ":00";
/*  670 */                 String str9 = str5 + " " + arrayOfString1[0] + ":00";
/*  671 */                 String str10 = str1 + " " + arrayOfString1[1] + ":00";
/*  672 */                 switch (this.type) {
/*      */                   case SING_IN:
/*      */                   case null:
/*  675 */                     bool = (str6.compareTo(str7) >= 0 || str6.compareTo(str10) < 0) ? false : true;
/*      */                     break;
/*      */                   case SING_OUT:
/*  678 */                     bool = (str6.compareTo(str9) >= 0 || str6.compareTo(str8) < 0) ? false : true;
/*      */                     break;
/*      */                   case null:
/*  681 */                     bool = (str6.compareTo(str7) >= 0 || str6.compareTo(str8) < 0) ? false : true;
/*      */                     break;
/*      */                   case SING_TIME:
/*  684 */                     bool = (str6.compareTo(str9) > 0 || str6.compareTo(str10) < 0) ? false : true;
/*      */                     break;
/*      */                   case null:
/*  687 */                     bool = (str6.compareTo(str9) >= 0 || str6.compareTo(str8) < 0) ? false : true;
/*      */                     break;
/*      */                 } 
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*  694 */         if (bool)
/*  695 */           continue;  str4 = str5;
/*      */       } else {
/*  697 */         boolean bool = false;
/*  698 */         if (hrmScheduleDate.isSchedulePerson()) {
/*  699 */           List list2 = this.manager.timeToList(hrmScheduleDate.getDBean().getWorkTime());
/*  700 */           List list3 = this.manager.timeToList(hrmScheduleDate.getDBean().getSignTime());
/*  701 */           String str6 = (list2.size() > 0) ? ((String[])list2.get(0))[0] : "";
/*  702 */           String str7 = (list3.size() > 0) ? ((String[])list3.get(0))[0] : "";
/*  703 */           if (StringUtil.isNotNull(str6)) {
/*  704 */             switch (this.type) {
/*      */               case SING_IN:
/*      */               case SING_TIME:
/*      */               case null:
/*      */               case null:
/*  709 */                 if (str2.compareTo(str7 + ":00") < 0) {
/*  710 */                   str4 = str5;
/*      */                 }
/*      */                 break;
/*      */               case SING_OUT:
/*      */               case null:
/*  715 */                 if (str2.compareTo(str6 + ":00") < 0) {
/*  716 */                   str4 = str5;
/*      */                 }
/*      */                 break;
/*      */             } 
/*      */           }
/*      */         } 
/*  722 */         if (bool || str4.compareTo(paramString1) < 0)
/*      */           continue; 
/*  724 */       }  hrmSchedule.setTempSignDate(str4);
/*      */       
/*  726 */       Map<Object, Object> map1 = linkedHashMap.containsKey(str3) ? (Map)linkedHashMap.get(str3) : null;
/*  727 */       List<HrmSchedule> list1 = null;
/*  728 */       if (map1 == null) {
/*  729 */         map1 = new LinkedHashMap<>();
/*      */       } else {
/*  731 */         list1 = (List)map1.get(str4);
/*      */       } 
/*  733 */       if (list1 == null) list1 = new ArrayList(); 
/*  734 */       initHrmSchedule(hrmScheduleDate, hrmSchedule);
/*  735 */       handle(hrmSchedule, list1);
/*  736 */       map1.put(str4, list1);
/*  737 */       linkedHashMap.put(str3, map1);
/*      */     } 
/*  739 */     return toScheduleList((Map)linkedHashMap);
/*      */   }
/*      */   
/*      */   private void resetName(HrmSchedule paramHrmSchedule, String[] paramArrayOfString) {
/*  743 */     String str1 = StringUtil.vString(paramHrmSchedule.get$ScheduleTitle());
/*  744 */     String str2 = paramArrayOfString[0] + "-" + paramArrayOfString[1];
/*  745 */     if (StringUtil.isNull(str1)) {
/*  746 */       str1 = str1 + str2;
/*      */     } else {
/*  748 */       str1 = str1 + bracket(str2);
/*      */     } 
/*  750 */     paramHrmSchedule.set$ScheduleName(str1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void handle(HrmSchedule paramHrmSchedule, List<HrmSchedule> paramList) {
/*  759 */     List<String[]> list1 = paramHrmSchedule.getWorkTimes(), list2 = paramHrmSchedule.getSignTimes();
/*  760 */     String str1 = (paramHrmSchedule == null) ? this.currentDate : paramHrmSchedule.getSignDate();
/*  761 */     String str2 = DateUtil.addDate(str1, 1);
/*  762 */     String str3 = "", str4 = "", str5 = "";
/*  763 */     for (byte b = 0; b < list1.size(); b++) {
/*  764 */       String[] arrayOfString1 = list2.get(b);
/*  765 */       String[] arrayOfString2 = list1.get(b);
/*  766 */       String[] arrayOfString3 = getTimeRange(arrayOfString1, arrayOfString2, NoSignType.SING_IN);
/*      */       
/*  768 */       str3 = paramHrmSchedule.getOldSignDate() + " " + paramHrmSchedule.getSignTime();
/*  769 */       str4 = str1 + " " + arrayOfString3[0];
/*  770 */       str5 = ((arrayOfString3[0].compareTo(arrayOfString3[1]) > 0) ? str2 : str1) + " " + arrayOfString3[1];
/*      */       
/*  772 */       if (DateUtil.isInDateRange(str3, str4, str5)) {
/*  773 */         resetName(paramHrmSchedule, arrayOfString2);
/*  774 */         if (paramList.size() == 0) {
/*  775 */           paramList.add(paramHrmSchedule); break;
/*      */         } 
/*  777 */         HrmSchedule hrmSchedule = null;
/*  778 */         byte b1 = -1;
/*  779 */         for (byte b2 = 0; b2 < paramList.size(); b2++) {
/*  780 */           HrmSchedule hrmSchedule1 = paramList.get(b2);
/*  781 */           str3 = hrmSchedule1.getOldSignDate() + " " + hrmSchedule1.getSignTime();
/*  782 */           if (DateUtil.isInDateRange(str3, str4, str5)) {
/*  783 */             hrmSchedule = hrmSchedule1;
/*  784 */             b1 = b2;
/*      */             break;
/*      */           } 
/*      */         } 
/*  788 */         if (hrmSchedule == null || b1 < 0) {
/*  789 */           paramList.add(paramHrmSchedule); break;
/*  790 */         }  if (allowChange(paramHrmSchedule, hrmSchedule)) {
/*  791 */           paramList.set(b1, paramHrmSchedule);
/*      */         }
/*      */         break;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected String[] getWorkTime(HrmSchedule paramHrmSchedule) {
/*  805 */     String[] arrayOfString = null;
/*  806 */     List<String[]> list1 = paramHrmSchedule.getWorkTimes(), list2 = paramHrmSchedule.getSignTimes();
/*  807 */     for (byte b = 0; b < list1.size(); b++) {
/*  808 */       String[] arrayOfString1 = list2.get(b);
/*  809 */       String[] arrayOfString2 = list1.get(b);
/*  810 */       String[] arrayOfString3 = getTimeRange(arrayOfString1, arrayOfString2, NoSignType.SING_TIME);
/*  811 */       if (timeIsIn(paramHrmSchedule.getSignTime(), arrayOfString3[0], arrayOfString3[1])) {
/*  812 */         arrayOfString = arrayOfString2;
/*      */         
/*      */         break;
/*      */       } 
/*      */     } 
/*  817 */     return arrayOfString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isAllowAdd(HrmSchedule paramHrmSchedule) {
/*  826 */     String[] arrayOfString = getWorkTime(paramHrmSchedule);
/*  827 */     return (arrayOfString != null && arrayOfString.length == 2) ? allowAdd(paramHrmSchedule.getSignTime(), arrayOfString[0], arrayOfString[1]) : false;
/*      */   }
/*      */   
/*      */   protected void addValue(List<Map<String, Object>> paramList, List<HrmSchedule> paramList1, HrmSchedule paramHrmSchedule, HrmScheduleDate paramHrmScheduleDate) {
/*  831 */     addValue(paramList, paramList1, paramHrmSchedule, paramHrmScheduleDate, true);
/*      */   }
/*      */   
/*      */   protected void addValue(List<Map<String, Object>> paramList, List<HrmSchedule> paramList1, HrmSchedule paramHrmSchedule, HrmScheduleDate paramHrmScheduleDate, boolean paramBoolean) {
/*  835 */     addValue(paramList, paramList1, (List<Map<String, Object>>)null, paramHrmSchedule, paramHrmScheduleDate, paramBoolean);
/*      */   }
/*      */   
/*      */   protected void addValue(List<Map<String, Object>> paramList1, List<HrmSchedule> paramList, List<Map<String, Object>> paramList2, HrmSchedule paramHrmSchedule, HrmScheduleDate paramHrmScheduleDate) {
/*  839 */     addValue(paramList1, paramList, paramList2, paramHrmSchedule, paramHrmScheduleDate, true);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected void addValue(List<Map<String, Object>> paramList1, List<HrmSchedule> paramList, List<Map<String, Object>> paramList2, HrmSchedule paramHrmSchedule, HrmScheduleDate paramHrmScheduleDate, boolean paramBoolean) {
/*  852 */     if (paramBoolean) initHrmSchedule(paramHrmScheduleDate, paramHrmSchedule); 
/*  853 */     List<String[]> list1 = paramHrmSchedule.getWorkTimes();
/*  854 */     List<String[]> list2 = paramHrmSchedule.getSignTimes();
/*  855 */     byte b1 = (list1 == null) ? 0 : list1.size();
/*  856 */     for (byte b2 = 0; b2 < b1; b2++) {
/*  857 */       String[] arrayOfString1 = list1.get(b2);
/*  858 */       String[] arrayOfString2 = getTimeRange(list2.get(b2), arrayOfString1, NoSignType.SING_IN);
/*  859 */       String[] arrayOfString3 = getTimeRange(list2.get(b2), arrayOfString1, NoSignType.SING_OUT);
/*  860 */       boolean bool1 = !checkScheduleIsIn(paramHrmSchedule, paramList, paramList2, arrayOfString3, arrayOfString2) ? true : false, bool2 = !isOffset(paramHrmSchedule, arrayOfString1) ? true : false;
/*  861 */       if (bool1 && bool2) {
/*  862 */         resetName(paramHrmSchedule, arrayOfString1);
/*  863 */         paramList1.add(toMap(paramHrmSchedule));
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean checkScheduleIsIn(HrmSchedule paramHrmSchedule, List<HrmSchedule> paramList, List<Map<String, Object>> paramList1, String[] paramArrayOfString1, String[] paramArrayOfString2) {
/*  878 */     boolean bool = false;
/*  879 */     String str1 = (paramHrmSchedule == null) ? this.currentDate : paramHrmSchedule.getSignDate();
/*  880 */     String str2 = DateUtil.addDate(str1, 1);
/*  881 */     String str3 = "", str4 = "", str5 = "";
/*  882 */     if (paramList != null) {
/*  883 */       for (HrmSchedule hrmSchedule : paramList) {
/*  884 */         str3 = hrmSchedule.getOldSignDate() + " " + hrmSchedule.getSignTime();
/*  885 */         str4 = str1 + " " + paramArrayOfString2[0];
/*  886 */         str5 = ((paramArrayOfString2[0].compareTo(paramArrayOfString2[1]) > 0) ? str2 : str1) + " " + paramArrayOfString2[1];
/*  887 */         if (DateUtil.isInDateRange(str3, str4, str5)) {
/*  888 */           bool = true;
/*      */           break;
/*      */         } 
/*      */       } 
/*      */     }
/*  893 */     switch (this.type) {
/*      */       case null:
/*  895 */         if (bool) {
/*  896 */           bool = false;
/*  897 */           if (paramList1 != null)
/*  898 */             for (Map<String, Object> map : paramList1) {
/*  899 */               if (!StringUtil.vString(Integer.valueOf(paramHrmSchedule.getResourceId())).equals(StringUtil.vString(map.get("resourceId"))))
/*  900 */                 continue;  str3 = StringUtil.vString(map.get("oldSignDate")) + " " + StringUtil.vString(map.get("signTime"));
/*  901 */               str4 = str1 + " " + paramArrayOfString1[0];
/*  902 */               str5 = ((paramArrayOfString1[0].compareTo(paramArrayOfString1[1]) > 0) ? str2 : str1) + " " + paramArrayOfString1[1];
/*  903 */               if (DateUtil.isInDateRange(str3, str4, str5)) {
/*  904 */                 bool = true;
/*      */                 break;
/*      */               } 
/*      */             }  
/*      */           break;
/*      */         } 
/*  910 */         bool = true;
/*      */         break;
/*      */     } 
/*      */     
/*  914 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String[] getTimeRange(String[] paramArrayOfString1, String[] paramArrayOfString2, NoSignType paramNoSignType) {
/*  925 */     String[] arrayOfString = new String[2];
/*  926 */     switch (this.type) {
/*      */       case SING_IN:
/*  928 */         arrayOfString[0] = paramArrayOfString1[0];
/*  929 */         arrayOfString[1] = "".equals(paramArrayOfString2[1]) ? paramArrayOfString1[1] : paramArrayOfString1[1];
/*      */         break;
/*      */       case null:
/*  932 */         arrayOfString[0] = paramArrayOfString1[0];
/*  933 */         arrayOfString[1] = paramArrayOfString2[1];
/*      */         break;
/*      */       case SING_TIME:
/*  936 */         arrayOfString[0] = paramArrayOfString2[0];
/*  937 */         arrayOfString[1] = paramArrayOfString2[1];
/*      */         break;
/*      */       case null:
/*  940 */         switch (paramNoSignType) {
/*      */           case SING_IN:
/*  942 */             arrayOfString[0] = paramArrayOfString1[0];
/*  943 */             arrayOfString[1] = paramArrayOfString2[1];
/*      */             break;
/*      */           case SING_OUT:
/*  946 */             arrayOfString[0] = paramArrayOfString2[0];
/*  947 */             arrayOfString[1] = paramArrayOfString1[1];
/*      */             break;
/*      */           case SING_TIME:
/*  950 */             arrayOfString[0] = paramArrayOfString1[0];
/*  951 */             arrayOfString[1] = paramArrayOfString1[1];
/*      */             break;
/*      */         } 
/*      */         break;
/*      */       case SING_OUT:
/*  956 */         arrayOfString[0] = "".equals(paramArrayOfString2[0]) ? paramArrayOfString1[0] : paramArrayOfString2[0];
/*  957 */         arrayOfString[1] = paramArrayOfString1[1];
/*      */         break;
/*      */       case null:
/*  960 */         arrayOfString[0] = paramArrayOfString2[0];
/*  961 */         arrayOfString[1] = paramArrayOfString1[1];
/*      */         break;
/*      */     } 
/*  964 */     if (StringUtil.isNotNull(new String[] { arrayOfString[0], arrayOfString[1] })) {
/*  965 */       arrayOfString[0] = getFullTime(arrayOfString[0], false);
/*  966 */       arrayOfString[1] = getFullTime(arrayOfString[1], (this.type != SignType.SIGN_IN && this.type != SignType.BE_LATE));
/*      */     } 
/*  968 */     return arrayOfString;
/*      */   }
/*      */   
/*      */   private String getFullTime(String paramString, boolean paramBoolean) {
/*  972 */     paramString = StringUtil.vString(paramString);
/*  973 */     if (paramString.length() == 5) paramString = paramString + ":" + (paramBoolean ? "59" : "00"); 
/*  974 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean allowAdd(String paramString1, String paramString2, String paramString3) {
/*  985 */     paramString2 = getFullTime(paramString2, false);
/*  986 */     paramString3 = getFullTime(paramString3, (this.type != SignType.SIGN_IN && this.type != SignType.BE_LATE));
/*  987 */     boolean bool = false;
/*  988 */     switch (this.type) {
/*      */       case SING_TIME:
/*  990 */         if (paramString2.compareTo(paramString3) > 0) {
/*  991 */           bool = (paramString1.compareTo(paramString2) > 0 || paramString1.compareTo(paramString3) < 0) ? true : false; break;
/*      */         } 
/*  993 */         bool = (paramString1.compareTo(paramString2) > 0) ? true : false;
/*      */         break;
/*      */       
/*      */       case null:
/*  997 */         if (paramString2.compareTo(paramString3) > 0) {
/*  998 */           bool = (paramString1.compareTo(paramString3) < 0 || paramString1.compareTo(paramString2) > 0) ? true : false; break;
/*      */         } 
/* 1000 */         bool = (paramString1.compareTo(paramString3) < 0) ? true : false;
/*      */         break;
/*      */     } 
/*      */     
/* 1004 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean allowChange(HrmSchedule paramHrmSchedule1, HrmSchedule paramHrmSchedule2) {
/* 1014 */     boolean bool = false;
/* 1015 */     switch (this.type) {
/*      */       case SING_IN:
/*      */       case SING_TIME:
/*      */       case null:
/*      */       case null:
/* 1020 */         bool = (paramHrmSchedule1.compareTo(paramHrmSchedule2) < 0) ? true : false;
/*      */         break;
/*      */       case SING_OUT:
/*      */       case null:
/* 1024 */         bool = (paramHrmSchedule1.compareTo(paramHrmSchedule2) > 0) ? true : false;
/*      */         break;
/*      */     } 
/* 1027 */     return bool;
/*      */   }
/*      */   
/*      */   private int getSignType() {
/* 1031 */     byte b = 0;
/* 1032 */     switch (this.type) {
/*      */       case SING_IN:
/*      */       case SING_TIME:
/*      */       case null:
/*      */       case null:
/* 1037 */         b = 1;
/*      */         break;
/*      */       case SING_OUT:
/*      */       case null:
/* 1041 */         b = 2;
/*      */         break;
/*      */     } 
/* 1044 */     return b;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected String getNextMin(String paramString) {
/* 1053 */     String str = "";
/* 1054 */     if (StringUtil.isNotNull(paramString)) {
/* 1055 */       if (paramString.length() >= 19) {
/* 1056 */         paramString = paramString.substring(17).equals("00") ? paramString : DateUtil.addSecond(paramString, 60);
/*      */       }
/* 1058 */       if (paramString.length() >= 16) {
/* 1059 */         str = paramString.substring(11, 16);
/*      */       } else {
/* 1061 */         str = paramString;
/*      */       } 
/*      */     } 
/* 1064 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   protected String getLastMin(String paramString) {
/* 1073 */     String str = "";
/* 1074 */     if (StringUtil.isNotNull(paramString)) {
/* 1075 */       if (paramString.length() >= 17) {
/* 1076 */         str = paramString.substring(11, 16);
/*      */       } else {
/* 1078 */         str = paramString;
/*      */       } 
/*      */     }
/* 1081 */     return str;
/*      */   }
/*      */   
/*      */   private boolean isOffset(HrmSchedule paramHrmSchedule) {
/* 1085 */     return isOffset(paramHrmSchedule, getWorkTime(paramHrmSchedule));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isOffset(HrmSchedule paramHrmSchedule, String[] paramArrayOfString) {
/* 1095 */     boolean bool = false;
/* 1096 */     if (paramArrayOfString != null && paramArrayOfString.length == 2) {
/* 1097 */       String str1 = paramHrmSchedule.getSignDate();
/* 1098 */       String str2 = paramHrmSchedule.getSignTime();
/* 1099 */       if (str2.compareTo(paramArrayOfString[1]) > 0 && (paramArrayOfString[0]
/* 1100 */         .compareTo(paramArrayOfString[1]) <= 0 || paramArrayOfString[1].equals("00:00") || str2.compareTo(paramArrayOfString[0]) < 0)) {
/* 1101 */         str2 = paramArrayOfString[1];
/*      */       } else {
/* 1103 */         switch (this.type) {
/*      */           case SING_TIME:
/*      */           case null:
/*      */           case null:
/* 1107 */             str2 = getNextMin(paramHrmSchedule.getSignFullTime());
/*      */             break;
/*      */           case null:
/* 1110 */             str2 = getLastMin(paramHrmSchedule.getSignFullTime());
/*      */             break;
/*      */         } 
/* 1113 */         int i = StringUtil.vString(str2).indexOf(" ");
/* 1114 */         if (i != -1) str2 = str2.split(" ")[1]; 
/*      */       } 
/* 1116 */       String str3 = "", str4 = "";
/* 1117 */       switch (this.type) {
/*      */         case SING_TIME:
/* 1119 */           str3 = paramArrayOfString[0];
/* 1120 */           str4 = str2;
/*      */           break;
/*      */         case null:
/* 1123 */           str3 = str2;
/* 1124 */           str4 = paramArrayOfString[1];
/*      */           break;
/*      */         default:
/* 1127 */           str3 = paramArrayOfString[0];
/* 1128 */           str4 = paramArrayOfString[1];
/*      */           break;
/*      */       } 
/* 1131 */       String str5 = "", str6 = "";
/* 1132 */       if (str3.compareTo(str4) > 0) {
/* 1133 */         str6 = DateUtil.addDate(str1, 1) + " " + str4;
/*      */       } else {
/* 1135 */         str6 = str1 + " " + str4;
/*      */       } 
/* 1137 */       str5 = str1 + " " + str3;
/* 1138 */       String[] arrayOfString1 = new String[0];
/* 1139 */       if (!paramHrmSchedule.isSchedulePerson() && !paramHrmSchedule.getDsSignType().equals("2")) {
/* 1140 */         ArrayList<String> arrayList = new ArrayList();
/* 1141 */         arrayList.addAll(Arrays.asList(DateUtil.getDateMinutes(str5, str6)));
/* 1142 */         arrayOfString1 = (String[])arrayList.toArray((Object[])new String[arrayList.size()]);
/* 1143 */         String[] arrayOfString = DateUtil.getDateMinutes(str1 + " " + paramHrmSchedule.getOffDutyTimeAM(), str1 + " " + paramHrmSchedule.getOnDutyTimePM());
/* 1144 */         arrayList.clear();
/* 1145 */         for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/* 1146 */           boolean bool1 = false;
/* 1147 */           for (byte b2 = 0; b2 < arrayOfString.length; b2++) {
/* 1148 */             if (arrayOfString1[b1].equals(arrayOfString[b2])) {
/* 1149 */               bool1 = true;
/*      */               break;
/*      */             } 
/*      */           } 
/* 1153 */           if (!bool1) arrayList.add(arrayOfString1[b1]); 
/*      */         } 
/* 1155 */         arrayOfString1 = arrayList.<String>toArray(new String[arrayList.size()]);
/*      */       }
/* 1157 */       else if (paramHrmSchedule.isSchedulePerson()) {
/* 1158 */         List list = paramHrmSchedule.getRestTimes();
/* 1159 */         ArrayList arrayList = new ArrayList();
/* 1160 */         for (String[] arrayOfString : list) {
/* 1161 */           if (arrayOfString.length != 2 || StringUtil.isNull(new String[] { arrayOfString[0], arrayOfString[1] }))
/* 1162 */             continue;  String str7 = str1;
/* 1163 */           String str8 = str7;
/* 1164 */           if (arrayOfString[0].compareTo(arrayOfString[1]) > 0) str8 = DateUtil.addDate(str1, 1); 
/* 1165 */           arrayList.addAll(Arrays.asList(DateUtil.getDateMinutes(str7 + " " + arrayOfString[0], str8 + " " + arrayOfString[1])));
/*      */         } 
/* 1167 */         ArrayList<String> arrayList1 = new ArrayList();
/* 1168 */         arrayOfString1 = DateUtil.getDateMinutes(str5, str6);
/* 1169 */         for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/* 1170 */           boolean bool1 = false;
/* 1171 */           for (String str : arrayList) {
/* 1172 */             if (arrayOfString1[b1].equals(str)) {
/* 1173 */               bool1 = true;
/*      */               break;
/*      */             } 
/*      */           } 
/* 1177 */           if (!bool1) arrayList1.add(arrayOfString1[b1]); 
/*      */         } 
/* 1179 */         arrayOfString1 = arrayList1.<String>toArray(new String[arrayList1.size()]);
/*      */       } else {
/* 1181 */         arrayOfString1 = DateUtil.getDateMinutes(str5, str6);
/*      */       } 
/*      */       
/* 1184 */       String[] arrayOfString2 = new String[arrayOfString1.length];
/* 1185 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/* 1186 */         arrayOfString2[b] = arrayOfString1[b].split(" ")[1];
/*      */       }
/* 1188 */       paramHrmSchedule.fDates = new ArrayList();
/* 1189 */       paramHrmSchedule.sDates = new ArrayList();
/* 1190 */       String[] arrayOfString3 = toArray(getFlowTime(String.valueOf(paramHrmSchedule.getResourceId()), str1, new String[] { str3, str4 }));
/* 1191 */       List<String> list1 = Arrays.asList(arrayOfString3);
/* 1192 */       List<String> list2 = Arrays.asList(arrayOfString2);
/* 1193 */       if (arrayOfString2.length <= arrayOfString3.length) bool = list1.containsAll(list2); 
/* 1194 */       if (this.type == SignType.ABSENT || this.type == SignType.BE_LATE || this.type == SignType.LEAVE_EARLY) {
/* 1195 */         ArrayList<String> arrayList1 = new ArrayList<>(list1);
/* 1196 */         ArrayList<String> arrayList2 = new ArrayList<>(list2);
/* 1197 */         paramHrmSchedule.fDates = arrayList1;
/* 1198 */         paramHrmSchedule.sDates = arrayList2;
/*      */       } 
/*      */     } 
/* 1201 */     return bool;
/*      */   }
/*      */   
/*      */   protected long totalTime(String paramString1, String paramString2, String[] paramArrayOfString) {
/* 1205 */     long l = totalTime(paramString1, paramString2, paramArrayOfString, this.leaveMap);
/* 1206 */     l += totalTime(paramString1, paramString2, paramArrayOfString, this.evectionMap);
/* 1207 */     l += totalTime(paramString1, paramString2, paramArrayOfString, this.outMap);
/* 1208 */     l += totalTime(paramString1, paramString2, paramArrayOfString, this.otherMap);
/* 1209 */     return l;
/*      */   }
/*      */   
/*      */   private long totalTime(String paramString1, String paramString2, String[] paramArrayOfString, Map<String, List<HrmReport>> paramMap) {
/* 1213 */     String str1 = "", str2 = "", str3 = "", str4 = "";
/* 1214 */     long l = 0L;
/* 1215 */     List list = (paramMap == null) ? null : paramMap.get(paramString1);
/* 1216 */     if (list != null) {
/* 1217 */       for (HrmReport hrmReport : list) {
/* 1218 */         str1 = hrmReport.getFromDate();
/* 1219 */         str2 = hrmReport.getFromTime();
/* 1220 */         str3 = hrmReport.getToDate();
/* 1221 */         str4 = hrmReport.getToTime();
/* 1222 */         if (str1.compareTo(paramString2) > 0 || str3.compareTo(paramString2) < 0)
/*      */           continue; 
/* 1224 */         if (str1.compareTo(paramString2) < 0)
/* 1225 */         { str1 = paramString2;
/* 1226 */           str2 = paramArrayOfString[0]; }
/* 1227 */         else { if (str2.compareTo(paramArrayOfString[1]) > 0)
/*      */             continue; 
/* 1229 */           if (str2.compareTo(paramArrayOfString[0]) < 0)
/* 1230 */             str2 = paramArrayOfString[0];  }
/*      */         
/* 1232 */         if (str3.compareTo(paramString2) > 0)
/* 1233 */         { str3 = paramString2;
/* 1234 */           str4 = paramArrayOfString[1]; }
/* 1235 */         else { if (str4.compareTo(paramArrayOfString[0]) <= 0)
/*      */             continue; 
/* 1237 */           if (str4.compareTo(paramArrayOfString[1]) > 0)
/* 1238 */             str4 = paramArrayOfString[1];  }
/*      */         
/* 1240 */         l += totalTime(str1, str2, str3, str4);
/*      */       } 
/*      */     }
/* 1243 */     return l;
/*      */   }
/*      */   
/*      */   protected Map<String, String> getFlowTime(String paramString1, String paramString2, String[] paramArrayOfString) {
/* 1247 */     Map<String, String> map = getFlowTime(paramString1, paramString2, paramArrayOfString, this.leaveMap, true);
/* 1248 */     map.putAll(getFlowTime(paramString1, paramString2, paramArrayOfString, this.evectionMap));
/* 1249 */     map.putAll(getFlowTime(paramString1, paramString2, paramArrayOfString, this.outMap));
/* 1250 */     map.putAll(getFlowTime(paramString1, paramString2, paramArrayOfString, this.otherMap));
/* 1251 */     return map;
/*      */   }
/*      */ 
/*      */   
/*      */   private String[] toArray(Map<String, String> paramMap) {
/* 1256 */     String[] arrayOfString = new String[(paramMap == null) ? 0 : paramMap.size()];
/* 1257 */     if (paramMap != null) {
/* 1258 */       Iterator<Map.Entry> iterator = paramMap.entrySet().iterator();
/* 1259 */       Map.Entry entry = null;
/* 1260 */       byte b = 0;
/* 1261 */       while (iterator.hasNext()) {
/* 1262 */         entry = iterator.next();
/* 1263 */         arrayOfString[b++] = ((String)entry.getValue()).split(" ")[1];
/*      */       } 
/*      */     } 
/* 1266 */     return arrayOfString;
/*      */   }
/*      */   
/*      */   private Map<String, String> getFlowTime(String paramString1, String paramString2, String[] paramArrayOfString, Map<String, List<HrmReport>> paramMap) {
/* 1270 */     return getFlowTime(paramString1, paramString2, paramArrayOfString, paramMap, false);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, String> getFlowTime(String paramString1, String paramString2, String[] paramArrayOfString, Map<String, List<HrmReport>> paramMap, boolean paramBoolean) {
/* 1282 */     String str1 = "", str2 = "", str3 = "", str4 = "", str5 = "", str6 = "";
/* 1283 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 1284 */     List list = (paramMap == null) ? null : paramMap.get(paramString1);
/*      */     
/* 1286 */     HrmSchedule hrmSchedule = new HrmSchedule();
/* 1287 */     int i = StringUtil.parseToInt(this.indexMap.get(paramString1), -1);
/* 1288 */     String str7 = "";
/*      */ 
/*      */     
/* 1291 */     if (list != null) {
/* 1292 */       str5 = DateUtil.addDate(paramString2, 1);
/* 1293 */       str6 = DateUtil.addDate(paramString2, -1);
/* 1294 */       boolean bool = (paramArrayOfString != null && paramArrayOfString.length == 2 && paramArrayOfString[0].compareTo(paramArrayOfString[1]) > 0 && !paramArrayOfString[1].equals("00:00")) ? true : false;
/* 1295 */       for (HrmReport hrmReport : list) {
/* 1296 */         str1 = hrmReport.getFromDate();
/* 1297 */         str2 = hrmReport.getFromTime();
/* 1298 */         str3 = hrmReport.getToDate();
/* 1299 */         str4 = hrmReport.getToTime();
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1304 */         if (paramBoolean) {
/* 1305 */           if (this.scheduleUnit == 1) {
/*      */             
/* 1307 */             if (i >= 0) {
/* 1308 */               Map map = this.personList.get(i);
/* 1309 */               if ((HrmScheduleDate)((Map)map.get("dateMap")).get(paramString2) != null) {
/* 1310 */                 hrmSchedule = ((HrmScheduleDate)((Map)map.get("dateMap")).get(paramString2)).getSBean();
/* 1311 */                 if (hrmSchedule != null && !hrmSchedule.isEmpty()) {
/* 1312 */                   str7 = getUnitType(hrmSchedule, str2, str4).toLowerCase();
/* 1313 */                   if ("am".equalsIgnoreCase(str7)) {
/* 1314 */                     str2 = hrmSchedule.getOnDutyTimeAM();
/* 1315 */                     str4 = hrmSchedule.getOffDutyTimeAM();
/* 1316 */                   } else if ("pm".equalsIgnoreCase(str7)) {
/* 1317 */                     str2 = hrmSchedule.getOnDutyTimePM();
/* 1318 */                     str4 = hrmSchedule.getOffDutyTimePM();
/* 1319 */                   } else if ("all".equalsIgnoreCase(str7)) {
/* 1320 */                     str2 = hrmSchedule.getOnDutyTimeAM();
/* 1321 */                     str4 = hrmSchedule.getOffDutyTimePM();
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */             } 
/* 1326 */           } else if (this.scheduleUnit == 2) {
/* 1327 */             str2 = paramArrayOfString[0];
/* 1328 */             str4 = paramArrayOfString[1];
/*      */           } 
/*      */         }
/* 1331 */         if (paramArrayOfString[1].equals("00:00")) {
/* 1332 */           SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*      */           try {
/* 1334 */             Date date1 = simpleDateFormat.parse(str1 + " " + str2);
/* 1335 */             Date date2 = simpleDateFormat.parse(str3 + " " + str4);
/* 1336 */             Date date3 = simpleDateFormat.parse(paramString2 + " " + paramArrayOfString[0]);
/* 1337 */             Date date4 = simpleDateFormat.parse(str5 + " " + paramArrayOfString[1]);
/* 1338 */             if (date2.before(date3))
/*      */               continue; 
/* 1340 */             if (date4.before(date1)) {
/*      */               continue;
/*      */             }
/* 1343 */             if (date1.before(date3)) {
/* 1344 */               str1 = simpleDateFormat.format(date3).split(" ")[0];
/* 1345 */               str2 = simpleDateFormat.format(date3).split(" ")[1];
/*      */             } else {
/* 1347 */               str1 = simpleDateFormat.format(date1).split(" ")[0];
/* 1348 */               str2 = simpleDateFormat.format(date1).split(" ")[1];
/*      */             } 
/* 1350 */             if (date2.before(date4)) {
/* 1351 */               str3 = simpleDateFormat.format(date2).split(" ")[0];
/* 1352 */               str4 = simpleDateFormat.format(date2).split(" ")[1];
/*      */             } else {
/* 1354 */               str3 = simpleDateFormat.format(date4).split(" ")[0];
/* 1355 */               str4 = simpleDateFormat.format(date4).split(" ")[1];
/*      */             }
/*      */           
/* 1358 */           } catch (ParseException parseException) {
/* 1359 */             parseException.printStackTrace();
/*      */           } 
/*      */         } else {
/* 1362 */           if (bool ? ((
/* 1363 */             str1.compareTo(paramString2) > 0 && (!str1.equals(str5) || (str1.equals(str5) && str2.compareTo(paramArrayOfString[1]) > 0))) || str3.compareTo(str6) < 0) : (
/*      */ 
/*      */             
/* 1366 */             str1.compareTo(paramString2) > 0 || str3.compareTo(paramString2) < 0)) {
/*      */             continue;
/*      */           }
/* 1369 */           if (str1.compareTo(paramString2) < 0)
/* 1370 */           { if (bool) {
/* 1371 */               if (str1.compareTo(str6) < 0) {
/* 1372 */                 str1 = paramString2;
/* 1373 */                 str2 = paramArrayOfString[0];
/* 1374 */               } else if (str1.equals(str6) && 
/* 1375 */                 str2.compareTo(paramArrayOfString[0]) < 0 && str2.compareTo(paramArrayOfString[1]) > 0) {
/* 1376 */                 str2 = paramArrayOfString[0];
/*      */               } 
/*      */             } else {
/*      */               
/* 1380 */               str1 = paramString2;
/* 1381 */               str2 = paramArrayOfString[0];
/*      */             }  }
/* 1383 */           else { if (str2.compareTo(paramArrayOfString[1]) > 0 && paramArrayOfString[0].compareTo(paramArrayOfString[1]) < 0)
/*      */               continue; 
/* 1385 */             if (str2.compareTo(paramArrayOfString[0]) < 0)
/* 1386 */               if (bool) {
/* 1387 */                 if (str2.compareTo(paramArrayOfString[1]) > 0) {
/* 1388 */                   str2 = paramArrayOfString[0];
/*      */                 }
/*      */               } else {
/* 1391 */                 str2 = paramArrayOfString[0];
/*      */               }   }
/*      */           
/* 1394 */           if (str3.compareTo(paramString2) > 0) {
/* 1395 */             if (bool) {
/* 1396 */               if (str3.compareTo(str5) > 0) {
/* 1397 */                 str3 = str5;
/* 1398 */                 str4 = paramArrayOfString[1];
/* 1399 */               } else if (str4.compareTo(paramArrayOfString[0]) < 0 && str4.compareTo(paramArrayOfString[1]) > 0) {
/* 1400 */                 str4 = paramArrayOfString[1];
/*      */               } 
/*      */             } else {
/* 1403 */               str3 = paramString2;
/* 1404 */               str4 = paramArrayOfString[1];
/*      */             } 
/* 1406 */           } else if (str4.compareTo(paramArrayOfString[0]) <= 0) {
/* 1407 */             if (bool) {
/* 1408 */               if (str4.compareTo(paramArrayOfString[1]) > 0 && str4.compareTo(paramArrayOfString[0]) <= 0) {
/* 1409 */                 str4 = paramArrayOfString[1];
/*      */               }
/*      */             } else {
/*      */               continue;
/*      */             } 
/* 1414 */           } else if (str4.compareTo(paramArrayOfString[1]) > 0 && 
/* 1415 */             !bool) {
/* 1416 */             str4 = paramArrayOfString[1];
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1421 */         String[] arrayOfString = DateUtil.getDateMinutes(str1 + " " + str2, str3 + " " + str4);
/* 1422 */         for (String str : arrayOfString) linkedHashMap.put(str, str); 
/*      */       } 
/*      */     } 
/* 1425 */     return (Map)linkedHashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private int getUnit() {
/* 1433 */     HrmScheduleApplicationManager hrmScheduleApplicationManager = new HrmScheduleApplicationManager();
/* 1434 */     HrmScheduleApplication hrmScheduleApplication = new HrmScheduleApplication();
/* 1435 */     hrmScheduleApplication = (HrmScheduleApplication)hrmScheduleApplicationManager.get(hrmScheduleApplicationManager.getMapParam("type:0"));
/* 1436 */     hrmScheduleApplication = (hrmScheduleApplication == null) ? new HrmScheduleApplication() : hrmScheduleApplication;
/*      */     
/* 1438 */     return hrmScheduleApplication.getUnit().intValue();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getUnitType(HrmSchedule paramHrmSchedule, String paramString1, String paramString2) {
/* 1453 */     String str1 = "";
/* 1454 */     String str2 = paramHrmSchedule.getOnDutyTimeAM();
/* 1455 */     String str3 = paramHrmSchedule.getOffDutyTimeAM();
/* 1456 */     String str4 = paramHrmSchedule.getOnDutyTimePM();
/* 1457 */     String str5 = paramHrmSchedule.getOffDutyTimePM();
/*      */     
/* 1459 */     String str6 = "";
/*      */ 
/*      */     
/* 1462 */     if (paramString1.compareTo(str2) <= 0) {
/* 1463 */       if (paramString2.compareTo(str2) > 0)
/*      */       {
/* 1465 */         if (paramString2.compareTo(str2) > 0 && paramString2.compareTo(str4) < 0) {
/*      */           
/* 1467 */           str6 = "am";
/* 1468 */         } else if (paramString2.compareTo(str4) >= 0) {
/*      */           
/* 1470 */           str6 = "all";
/*      */         }  } 
/* 1472 */     } else if (paramString1.compareTo(str2) > 0 && paramString1.compareTo(str3) <= 0) {
/*      */       
/* 1474 */       if (paramString2.compareTo(str2) > 0)
/*      */       {
/* 1476 */         if (paramString2.compareTo(str2) > 0 && paramString2.compareTo(str4) < 0) {
/*      */           
/* 1478 */           str6 = "am";
/* 1479 */         } else if (paramString2.compareTo(str4) >= 0) {
/*      */           
/* 1481 */           str6 = "all";
/*      */         }  } 
/* 1483 */     } else if (paramString1.compareTo(str3) > 0 && paramString1.compareTo(str4) < 0) {
/*      */       
/* 1485 */       if (paramString2.compareTo(str2) > 0)
/*      */       {
/* 1487 */         if (paramString2.compareTo(str2) <= 0 || paramString2.compareTo(str4) >= 0)
/*      */         {
/* 1489 */           if (paramString2.compareTo(str4) >= 0)
/*      */           {
/* 1491 */             str6 = "pm"; }  } 
/*      */       }
/* 1493 */     } else if (paramString1.compareTo(str4) >= 0 && paramString1.compareTo(str5) <= 0) {
/*      */       
/* 1495 */       if (paramString2.compareTo(str2) > 0)
/*      */       {
/* 1497 */         if (paramString2.compareTo(str2) <= 0 || paramString2.compareTo(str4) >= 0)
/*      */         {
/* 1499 */           if (paramString2.compareTo(str4) >= 0)
/*      */           {
/* 1501 */             str6 = "pm"; }  } 
/*      */       }
/* 1503 */     } else if (paramString1.compareTo(str5) > 0) {
/*      */     
/*      */     } 
/*      */     
/* 1507 */     return str6;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkIsinRule(Map<String, Object> paramMap, ScheduleApplicationRule paramScheduleApplicationRule, long paramLong) {
/* 1518 */     boolean bool = false;
/* 1519 */     String str = StringUtil.vString(paramScheduleApplicationRule.getId());
/*      */ 
/*      */     
/* 1522 */     long l1 = StringUtil.parseToLong(paramScheduleApplicationRule.getSeclevel()) * 60L;
/*      */     
/* 1524 */     long l2 = StringUtil.parseToLong(paramScheduleApplicationRule.getSeclevelend()) * 60L + 59L;
/* 1525 */     if (paramLong >= l1 && paramLong <= l2) {
/* 1526 */       bool = true;
/*      */     }
/* 1528 */     return bool;
/*      */   }
/*      */   
/*      */   public int getLanId() {
/* 1532 */     return this.lanId;
/*      */   }
/*      */   
/*      */   public void setLanId(int paramInt) {
/* 1536 */     this.lanId = paramInt;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmReportManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */