/*    */ package weaver.hrm.report.manager;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.appdetach.AppDetachComInfo;
/*    */ import weaver.hrm.common.SplitPageTagFormat;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmStatusRpManager
/*    */   extends ReportManager
/*    */   implements IReport
/*    */ {
/*    */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 24 */     String str1 = paramMap.get("fromdate");
/* 25 */     String str2 = paramMap.get("enddate");
/* 26 */     String str3 = paramMap.get("department");
/* 27 */     String str4 = paramMap.get("location");
/*    */     
/* 29 */     String str5 = "";
/*    */     
/* 31 */     if (!str1.equals("")) {
/* 32 */       str5 = str5 + " and startdate>='" + str1 + "'";
/*    */     }
/*    */     
/* 35 */     if (!str2.equals("")) {
/* 36 */       str5 = str5 + " and (startdate<='" + str2 + "' or startdate is null)";
/*    */     }
/*    */ 
/*    */     
/* 40 */     if (!str4.equals("")) {
/* 41 */       str5 = str5 + " and locationid =" + str4;
/*    */     }
/*    */     
/* 44 */     if (!str3.equals("")) {
/* 45 */       str5 = str5 + " and departmentid =" + str3;
/*    */     }
/*    */ 
/*    */     
/* 49 */     String str6 = AppDetachComInfo.getInnerResourceSql();
/*    */     
/* 51 */     str5 = str5 + " and " + str6;
/*    */     
/* 53 */     String str7 = "select count(*)  from HrmResource where (accounttype is null or accounttype=0) and status is not null " + str5;
/* 54 */     this.rs.executeSql(str7);
/* 55 */     boolean bool = this.rs.next() ? this.rs.getInt(1) : false;
/*    */     
/* 57 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 58 */     HashMap<Object, Object> hashMap = null;
/* 59 */     str7 = "select status,count(id) resultcount from HrmResource where (accounttype is null or accounttype=0) and status is not null ";
/* 60 */     if (!str5.equals("")) {
/* 61 */       str7 = str7 + str5;
/*    */     }
/* 63 */     str7 = str7 + " group by status";
/*    */     
/* 65 */     this.rs.executeSql(str7);
/* 66 */     if (bool) {
/* 67 */       while (this.rs.next()) {
/* 68 */         String str = StringUtil.vString(this.rs.getString(1));
/* 69 */         int i = this.rs.getInt(2);
/*    */         
/* 71 */         hashMap = new HashMap<>();
/* 72 */         hashMap.put("title", (new SplitPageTagFormat()).colFormat(str, "{cmd:array[" + paramUser.getLanguage() + ";0=15710,1=15711,2=480,3=15844,4=6094,5=6091,6=6092,7=2245,8=1831,9=332]}"));
/* 73 */         hashMap.put("result", String.valueOf(i));
/* 74 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(i), String.valueOf(bool)));
/* 75 */         hashMap.put("total", String.valueOf(bool));
/* 76 */         arrayList.add(hashMap);
/*    */       } 
/*    */     }
/*    */     
/* 80 */     return (List)arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmStatusRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */