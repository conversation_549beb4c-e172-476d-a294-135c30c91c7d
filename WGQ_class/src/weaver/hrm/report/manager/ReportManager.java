/*    */ package weaver.hrm.report.manager;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ReportManager
/*    */ {
/* 11 */   protected RecordSet rs = null;
/*    */   
/* 13 */   protected RecordSet _rs = null;
/*    */   
/*    */   public ReportManager() {
/* 16 */     this.rs = new RecordSet();
/* 17 */     this._rs = new RecordSet();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/ReportManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */