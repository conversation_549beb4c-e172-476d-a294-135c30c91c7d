/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.job.JobActivitiesComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmJobActivityRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  25 */     String str1 = Util.null2String(paramMap.get("fromdate"));
/*  26 */     String str2 = Util.null2String(paramMap.get("enddate"));
/*  27 */     String str3 = Util.null2String(paramMap.get("location"));
/*  28 */     String str4 = Util.null2String(paramMap.get("status"));
/*  29 */     String str5 = Util.null2String(paramMap.get("department"));
/*  30 */     String str6 = Util.null2String(paramMap.get("subcompany"));
/*  31 */     String str7 = Util.null2String(paramMap.get("jobActivityMark"));
/*     */     
/*  33 */     int i = 0;
/*  34 */     int j = 0;
/*  35 */     String str8 = "";
/*     */     
/*  37 */     if (!str1.equals("")) {
/*  38 */       str8 = str8 + " and t1.startdate>='" + str1 + "'";
/*     */     }
/*  40 */     if (!str2.equals("")) {
/*  41 */       str8 = str8 + " and (t1.startdate<='" + str2 + "' or t1.startdate is null)";
/*     */     }
/*     */     
/*  44 */     if (!str3.equals("")) {
/*  45 */       str8 = str8 + " and t1.locationid =" + str3;
/*     */     }
/*     */     
/*  48 */     if (!str5.equals("")) {
/*  49 */       str8 = str8 + " and t1.departmentid  in (" + str5 + ")";
/*     */     }
/*     */     
/*  52 */     if (!str6.equals("")) {
/*  53 */       str8 = str8 + " and t1.subcompanyid1  in (" + str6 + ") ";
/*     */     }
/*     */     
/*  56 */     if (!str7.equals("")) {
/*  57 */       str8 = str8 + " and t3.jobActivityMark like '%" + str7 + "%'";
/*     */     }
/*     */     
/*  60 */     if (!str4.equals("") && !str4.equals("9")) {
/*  61 */       if (str4.equals("8")) {
/*  62 */         str8 = str8 + " and t1.status <= 3";
/*     */       } else {
/*  64 */         str8 = str8 + " and t1.status =" + str4;
/*     */       } 
/*     */     }
/*     */     
/*  68 */     String str9 = AppDetachComInfo.getInnerResourceSql("t1");
/*     */     
/*  70 */     str8 = str8 + " and " + str9;
/*     */     
/*  72 */     String str10 = "select count(t1.id) from HrmResource t1  ,HrmJobTitles  t2,HrmJobActivities t3  where   (t1.accounttype is null or t1.accounttype=0) and t1.jobtitle=t2.id and t2.jobactivityid = t3.id   and 1=1" + str8;
/*  73 */     this.rs.executeSql(str10);
/*  74 */     this.rs.next();
/*  75 */     i = this.rs.getInt(1);
/*  76 */     String str11 = "";
/*  77 */     if (str8.equals("")) {
/*  78 */       str11 = "select t3.id   resultid,COUNT(t1.id)   resultcount    from HrmResource  t1,HrmJobTitles  t2,HrmJobActivities t3 where (t1.accounttype is null or t1.accounttype=0) and t1.jobtitle=t2.id and t2.jobactivityid = t3.id group by t3.id order by resultcount desc";
/*     */     } else {
/*     */       
/*  81 */       str11 = "select t3.id   resultid,COUNT(t1.id)   resultcount    from HrmResource  t1,HrmJobTitles  t2, HrmJobActivities t3 where (t1.accounttype is null or t1.accounttype=0) and t1.jobtitle=t2.id and t2.jobactivityid = t3.id " + str8 + " group by t3.id order by resultcount  desc";
/*     */     } 
/*  83 */     this.rs.executeSql(str11);
/*  84 */     while (this.rs.next()) {
/*  85 */       int k = this.rs.getInt(2);
/*  86 */       j += k;
/*     */     } 
/*     */     
/*  89 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  90 */     HashMap<Object, Object> hashMap = null;
/*     */     
/*  92 */     if (i != 0 && this.rs.first()) {
/*  93 */       JobActivitiesComInfo jobActivitiesComInfo = new JobActivitiesComInfo();
/*     */       do {
/*  95 */         String str12 = this.rs.getString(1);
/*  96 */         int k = this.rs.getInt(2);
/*  97 */         hashMap = new HashMap<>();
/*  98 */         String str13 = "";
/*     */         try {
/* 100 */           str13 = Util.toScreen(jobActivitiesComInfo.getJobActivitiesmarks(str12), paramUser.getLanguage());
/* 101 */           str13 = str13 + "-";
/* 102 */           str13 = str13 + Util.toScreen(jobActivitiesComInfo.getJobActivitiesname(str12), paramUser.getLanguage());
/* 103 */         } catch (Exception exception) {
/* 104 */           exception.printStackTrace();
/*     */         } 
/* 106 */         hashMap.put("title", str13);
/* 107 */         hashMap.put("mark", Util.toScreen(jobActivitiesComInfo.getJobActivitiesmarks(str12), paramUser.getLanguage()));
/* 108 */         hashMap.put("name", Util.toScreen(jobActivitiesComInfo.getJobActivitiesname(str12), paramUser.getLanguage()));
/* 109 */         hashMap.put("result", String.valueOf(k));
/* 110 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(k), String.valueOf(i)));
/* 111 */         hashMap.put("total", String.valueOf(i));
/* 112 */         arrayList.add(hashMap);
/* 113 */       } while (this.rs.next());
/*     */     } 
/* 115 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmJobActivityRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */