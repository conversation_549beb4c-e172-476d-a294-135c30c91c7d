/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.contract.ContractTypeComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmContractReportManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  27 */     String str1 = paramMap.get("from");
/*  28 */     String str2 = paramMap.get("year");
/*  29 */     String str3 = paramMap.get("subcompanyid1");
/*  30 */     String str4 = paramMap.get("showpage");
/*  31 */     str4 = (str4.length() == 0) ? "1" : str4;
/*     */     
/*  33 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/*  34 */     HashMap<Object, Object> hashMap = null;
/*  35 */     String str5 = "";
/*     */     
/*  37 */     Hashtable<Object, Object> hashtable1 = new Hashtable<Object, Object>();
/*  38 */     Hashtable<Object, Object> hashtable2 = new Hashtable<Object, Object>();
/*  39 */     if (str4.equals("3")) {
/*  40 */       str5 = "select id,typename from HrmContractType order by id";
/*  41 */       this._rs.executeSql(str5);
/*  42 */       byte b = 0;
/*  43 */       while (this._rs.next()) {
/*  44 */         hashtable1.put(new Integer(b), this._rs.getString("id"));
/*  45 */         hashtable2.put(new Integer(b), this._rs.getString("typename"));
/*  46 */         b++;
/*     */       } 
/*     */     } 
/*     */     
/*  50 */     String str6 = "";
/*  51 */     if (str1.equals("report")) {
/*  52 */       str6 = str6 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str3 + ")";
/*     */     }
/*  54 */     if (!str3.equals("") && !str1.equals("report")) {
/*  55 */       str6 = str6 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str3 + ")";
/*     */     }
/*     */     
/*  58 */     if (str4.equals("1")) {
/*  59 */       str5 = "select distinct(t2.departmentid) resultid from HrmContract t1,HrmResource t2 where (t2.accounttype is null or t2.accounttype=0) " + str6;
/*  60 */     } else if (str4.equals("2")) {
/*  61 */       str5 = "select distinct(t2.id) resultid from HrmContract t1,HrmContractType t2 where t1.contracttypeid = t2.id ";
/*  62 */     } else if (str4.equals("3")) {
/*  63 */       str5 = "select distinct(t2.departmentid) resultid from HrmContract t1,HrmResource t2 where 1=1" + str6;
/*     */     } else {
/*  65 */       return arrayList;
/*     */     } 
/*  67 */     this._rs.executeSql(str5);
/*     */     
/*  69 */     while (this._rs.next()) {
/*  70 */       hashMap = new HashMap<Object, Object>();
/*  71 */       String str7 = Tools.vString(this._rs.getString(1));
/*  72 */       String str8 = "";
/*     */       try {
/*  74 */         if (str4.equals("1") || str4.equals("3")) {
/*  75 */           str8 = Util.toScreen((new DepartmentComInfo()).getDepartmentname(str7), paramUser.getLanguage());
/*  76 */         } else if (str4.equals("2")) {
/*  77 */           str8 = Util.toScreen((new ContractTypeComInfo()).getContractTypename(str7), paramUser.getLanguage());
/*     */         } 
/*  79 */       } catch (Exception exception) {
/*  80 */         exception.printStackTrace();
/*     */       } 
/*  82 */       if (str7.length() == 0) {
/*     */         continue;
/*     */       }
/*  85 */       hashMap.put("title", str8);
/*     */       
/*  87 */       if (str4.equals("3")) {
/*  88 */         Enumeration<Integer> enumeration = hashtable1.keys();
/*  89 */         while (enumeration.hasMoreElements()) {
/*  90 */           Integer integer = enumeration.nextElement();
/*  91 */           String str9 = (String)hashtable1.get(integer);
/*  92 */           str6 = " and contracttypeid =  " + str9;
/*  93 */           String str10 = "" + str2 + "-01-01";
/*  94 */           String str11 = "" + str2 + "-12-31";
/*  95 */           str6 = str6 + " and (contractstartdate >='" + str10 + "' and contractstartdate <= '" + str11 + "')";
/*  96 */           str5 = "select count(t1.id) resultcount from HrmContract t1,HrmResource t2 where t1.contractman = t2.id and t2.departmentid = " + str7 + str6;
/*  97 */           this.rs.executeSql(str5);
/*  98 */           this.rs.next();
/*  99 */           hashMap.put("resultcount" + str9, String.valueOf(this.rs.getInt(1)));
/*     */         } 
/*     */       } else {
/* 102 */         for (byte b = 1; b < 13; b++) {
/* 103 */           String str9 = "" + str2 + "-" + Util.add0(b, 2) + "-01";
/* 104 */           String str10 = "" + str2 + "-" + Util.add0(b, 2) + "-31";
/* 105 */           str6 = " and (contractstartdate >='" + str9 + "' and contractstartdate <= '" + str10 + "')";
/*     */           
/* 107 */           if (str4.equals("1")) {
/* 108 */             if (str1.equals("report")) {
/* 109 */               str6 = str6 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str3 + ")";
/*     */             }
/* 111 */             if (!str3.equals("") && !str1.equals("report")) {
/* 112 */               str6 = str6 + " and t2.id in (select id from HrmResource where subcompanyid1 =  " + str3 + ")";
/*     */             }
/* 114 */             str5 = "select count(t1.id) resultcount from HrmContract t1,HrmResource t2 where (t2.accounttype is null or t2.accounttype=0) and t1.contractman = t2.id and t2.departmentid = " + str7 + str6;
/* 115 */           } else if (str4.equals("2")) {
/* 116 */             str5 = "select count(t1.id) resultcount from HrmContract t1 where t1.contracttypeid =  " + str7 + str6;
/*     */           } 
/* 118 */           this.rs.executeSql(str5);
/* 119 */           this.rs.next();
/*     */           
/* 121 */           String str11 = "" + this.rs.getInt(1);
/* 122 */           hashMap.put("result" + b, String.valueOf(str11));
/*     */         } 
/*     */       } 
/* 125 */       arrayList.add(hashMap);
/*     */     } 
/* 127 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmContractReportManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */