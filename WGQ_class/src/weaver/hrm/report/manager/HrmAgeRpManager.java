/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.common.database.dialect.DbDialectFactory;
/*     */ import weaver.hrm.common.database.dialect.DialectUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmAgeRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  27 */     String str1 = paramMap.get("fromdate");
/*  28 */     String str2 = paramMap.get("enddate");
/*  29 */     String str3 = paramMap.get("department");
/*  30 */     String str4 = paramMap.get("location");
/*  31 */     String str5 = paramMap.get("status");
/*  32 */     String str6 = paramMap.get("area");
/*     */     
/*  34 */     int i = 0;
/*  35 */     String str7 = "";
/*  36 */     String str8 = "";
/*     */     
/*  38 */     if (str6.equals("")) {
/*  39 */       str6 = "10";
/*     */     }
/*  41 */     if (str5.equals("")) {
/*  42 */       str5 = "8";
/*     */     }
/*     */     
/*  45 */     if (!str1.equals("")) {
/*  46 */       str7 = str7 + " and startdate>='" + str1 + "'";
/*  47 */       if (str8.equals("")) {
/*  48 */         str8 = " where startdate>='" + str1 + "'";
/*     */       } else {
/*     */         
/*  51 */         str8 = str8 + " and startdate>='" + str1 + "'";
/*     */       } 
/*     */     } 
/*  54 */     if (!str2.equals("")) {
/*  55 */       str7 = str7 + " and (startdate<='" + str2 + "' or startdate is null)";
/*  56 */       if (str8.equals("")) {
/*  57 */         str8 = " where (startdate<='" + str2 + "' or startdate is null)";
/*     */       } else {
/*     */         
/*  60 */         str8 = str8 + " and (startdate<='" + str2 + "' or startdate is null)";
/*     */       } 
/*     */     } 
/*     */     
/*  64 */     if (!str4.equals("")) {
/*  65 */       str7 = str7 + " and locationid =" + str4;
/*  66 */       if (str8.equals("")) {
/*  67 */         str8 = " where locationid =" + str4;
/*     */       } else {
/*     */         
/*  70 */         str8 = str8 + " and locationid =" + str4;
/*     */       } 
/*     */     } 
/*  73 */     if (!str3.equals("")) {
/*  74 */       str7 = str7 + " and departmentid =" + str3;
/*  75 */       if (str8.equals("")) {
/*  76 */         str8 = " where departmentid =" + str3;
/*     */       } else {
/*     */         
/*  79 */         str8 = str8 + " and departmentid =" + str3;
/*     */       } 
/*     */     } 
/*     */     
/*  83 */     if (!str5.equals("") && !str5.equals("9")) {
/*  84 */       if (str5.equals("8")) {
/*  85 */         str7 = str7 + " and status <= 3";
/*  86 */         if (str8.equals("")) {
/*  87 */           str8 = " where status <= 3";
/*     */         } else {
/*  89 */           str8 = str8 + " and status <= 3";
/*     */         } 
/*     */       } else {
/*  92 */         str7 = str7 + " and status =" + str5;
/*     */         
/*  94 */         if (str8.equals("")) {
/*  95 */           str8 = " where status =" + str5;
/*     */         } else {
/*  97 */           str8 = str8 + " and status =" + str5;
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/* 102 */     String str9 = AppDetachComInfo.getInnerResourceSql();
/*     */     
/* 104 */     str7 = str7 + " and " + str9;
/* 105 */     if (str8.equals("")) {
/* 106 */       str8 = " where " + str9;
/*     */     } else {
/* 108 */       str8 = str8 + " and " + str9;
/*     */     } 
/*     */     
/* 111 */     String str10 = "";
/* 112 */     String str11 = "";
/* 113 */     int j = 0;
/* 114 */     if (str8.equals("")) {
/* 115 */       str11 = "select count(*)  from HrmResource where accounttype is null or accounttype=0 ";
/*     */     } else {
/* 117 */       str11 = "select count(*)  from HrmResource " + str8 + " and (accounttype is null or accounttype=0)";
/*     */     } 
/* 119 */     this.rs.executeSql(str11);
/* 120 */     this.rs.next();
/* 121 */     i = this.rs.getInt(1);
/*     */ 
/*     */     
/* 124 */     if (str7.equals("")) {
/* 125 */       str10 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and birthday is null or birthday =''";
/*     */     } else {
/* 127 */       str10 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and (birthday is null or birthday ='')" + str7;
/*     */     } 
/* 129 */     this.rs.executeSql(str10);
/* 130 */     this.rs.next();
/* 131 */     j = this.rs.getInt(1);
/*     */     
/* 133 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 134 */     HashMap<Object, Object> hashMap = null;
/* 135 */     if (j != 0) {
/* 136 */       hashMap = new HashMap<>();
/* 137 */       hashMap.put("title", SystemEnv.getHtmlLabelName(15863, paramUser.getLanguage()));
/* 138 */       hashMap.put("result", String.valueOf(j));
/* 139 */       hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 140 */       hashMap.put("total", String.valueOf(i));
/* 141 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 144 */     Calendar calendar = Calendar.getInstance();
/* 145 */     int k = Util.getIntValue(Util.add0(calendar.get(1), 4));
/*     */     
/* 147 */     if (this.rs.getDBType().equals("oracle")) {
/* 148 */       str10 = "select min(birthday) from HrmResource where (accounttype is null or accounttype=0) and  birthday is not null and length(ltrim(rtrim(birthday)))=10 ";
/* 149 */     } else if (this.rs.getDBType().equals("db2")) {
/* 150 */       str10 = "select min(birthday) from HrmResource where birthday !='' and length(ltrim(rtrim(birthday)))=10 ";
/* 151 */     } else if (DialectUtil.isMySql(this.rs.getDBType())) {
/*     */ 
/*     */       
/* 154 */       str10 = "select min(birthday) from HrmResource where (accounttype is null or accounttype=0) and birthday is not null and birthday !='' and " + DbDialectFactory.get(this.rs.getDBType()).charLengthSql("ltrim(rtrim(birthday))") + "=10 ";
/*     */     } else {
/* 156 */       str10 = "select min(birthday) from HrmResource where (accounttype is null or accounttype=0) and birthday is not null and birthday !='' and len(ltrim(rtrim(birthday)))=10 ";
/*     */     } 
/*     */     
/* 159 */     this.rs.executeSql(str10);
/* 160 */     this.rs.next();
/* 161 */     String str12 = this.rs.getString(1);
/*     */     
/* 163 */     if (!str12.equals("")) {
/* 164 */       int m = Util.getIntValue(str12.substring(0, 4));
/* 165 */       int n = k - m + 1;
/* 166 */       int i1 = Util.getIntValue(str6);
/* 167 */       int i2 = n / i1 + 1;
/* 168 */       String str13 = "";
/* 169 */       String str14 = "";
/*     */       
/* 171 */       for (byte b = 0; b < i2; b++) {
/* 172 */         int i3 = b * i1;
/* 173 */         int i4 = (b + 1) * i1 - 1;
/*     */ 
/*     */         
/* 176 */         str13 = (k - i1) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */         
/* 179 */         str14 = k + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */         
/* 181 */         if (this.rs.getDBType().equals("oracle")) {
/* 182 */           if (str7.equals("")) {
/* 183 */             str10 = "select count(*) resultcount from HrmResource  where (accounttype is null or accounttype=0) and birthday >='" + str13 + "'  and birthday <='" + str14 + "' and birthday is not null ";
/*     */           } else {
/* 185 */             str10 = "select count(*) resultcount from HrmResource  where (accounttype is null or accounttype=0) and birthday >='" + str13 + "'  and birthday <='" + str14 + "'  and birthday is not null " + str7;
/*     */           } 
/* 187 */         } else if (this.rs.getDBType().equals("db2")) {
/* 188 */           if (str7.equals("")) {
/* 189 */             str10 = "select count(*) resultcount from HrmResource where birthday >='" + str13 + "'  and birthday <='" + str14 + "' and birthday<>''  ";
/*     */           } else {
/* 191 */             str10 = "select count(*) resultcount from HrmResource where birthday >='" + str13 + "'  and birthday <='" + str14 + "' and birthday<>''  " + str7;
/*     */           }
/*     */         
/* 194 */         } else if (str7.equals("")) {
/* 195 */           str10 = "select count(*) resultcount from HrmResource  where (accounttype is null or accounttype=0) and birthday >='" + str13 + "'  and birthday <='" + str14 + "' and birthday<>'' and birthday is not null ";
/*     */         } else {
/* 197 */           str10 = "select count(*) resultcount from HrmResource  where (accounttype is null or accounttype=0) and birthday >='" + str13 + "'  and birthday <='" + str14 + "' and birthday<>'' and birthday is not null " + str7;
/*     */         } 
/*     */         
/* 200 */         this.rs.executeSql(str10);
/* 201 */         this.rs.next();
/* 202 */         j = this.rs.getInt(1);
/*     */         
/* 204 */         if (j != 0) {
/* 205 */           hashMap = new HashMap<>();
/* 206 */           hashMap.put("title", i3 + "-" + i4 + " " + SystemEnv.getHtmlLabelName(15864, paramUser.getLanguage()));
/* 207 */           hashMap.put("result", String.valueOf(j));
/* 208 */           hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 209 */           hashMap.put("total", String.valueOf(i));
/* 210 */           arrayList.add(hashMap);
/*     */         } 
/* 212 */         k -= i1;
/*     */       } 
/*     */     } 
/* 215 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmAgeRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */