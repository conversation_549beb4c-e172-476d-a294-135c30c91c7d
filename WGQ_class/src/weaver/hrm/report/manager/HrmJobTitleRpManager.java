/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmJobTitleRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  25 */     String str1 = Util.null2String(paramMap.get("fromdate"));
/*  26 */     String str2 = Util.null2String(paramMap.get("enddate"));
/*  27 */     String str3 = Util.null2String(paramMap.get("location"));
/*  28 */     String str4 = Util.null2String(paramMap.get("status"));
/*  29 */     String str5 = Util.null2String(paramMap.get("department"));
/*  30 */     String str6 = Util.null2String(paramMap.get("subcompany"));
/*  31 */     String str7 = Util.null2String(paramMap.get("jobtitle"));
/*     */ 
/*     */     
/*  34 */     int i = 0;
/*  35 */     int j = 0;
/*  36 */     String str8 = "";
/*     */     
/*  38 */     if (!str1.equals("")) {
/*  39 */       str8 = str8 + " and t1.startdate>='" + str1 + "'";
/*     */     }
/*  41 */     if (!str2.equals("")) {
/*  42 */       str8 = str8 + " and (t1.startdate<='" + str2 + "' or t1.startdate is null)";
/*     */     }
/*     */     
/*  45 */     if (!str5.equals("")) {
/*  46 */       str8 = str8 + " and t1.departmentid  in (" + str5 + ")";
/*     */     }
/*     */     
/*  49 */     if (!str7.equals("")) {
/*  50 */       str8 = str8 + " and t2.jobtitlemark like '%" + str7 + "%'";
/*     */     }
/*     */     
/*  53 */     if (!str6.equals("")) {
/*  54 */       str8 = str8 + " and t1.subcompanyid1  in (" + str6 + ") ";
/*     */     }
/*     */     
/*  57 */     if (!str3.equals("")) {
/*  58 */       str8 = str8 + " and t1.locationid =" + str3;
/*     */     }
/*  60 */     if (!str4.equals("") && !str4.equals("9")) {
/*  61 */       if (str4.equals("8")) {
/*  62 */         str8 = str8 + " and t1.status <= 3";
/*     */       } else {
/*  64 */         str8 = str8 + " and t1.status =" + str4;
/*     */       } 
/*     */     }
/*     */     
/*  68 */     String str9 = AppDetachComInfo.getInnerResourceSql("t1");
/*     */     
/*  70 */     str8 = str8 + " and " + str9;
/*     */     
/*  72 */     String str10 = "select count(t1.id) from HrmResource t1,HrmJobTitles  t2  where (t1.accounttype is null or t1.accounttype=0) and  t1.jobtitle=t2.id   and 1=1" + str8;
/*  73 */     this.rs.executeSql(str10);
/*  74 */     this.rs.next();
/*  75 */     i = this.rs.getInt(1);
/*     */     
/*  77 */     String str11 = "";
/*  78 */     if (str8.equals("")) {
/*  79 */       str11 = "select t1.jobtitle   resultid,COUNT(t1.id)   resultcount from HrmResource  t1,HrmJobTitles  t2 where (t1.accounttype is null or t1.accounttype=0) and t1.jobtitle=t2.id  group by t1.jobtitle order by resultcount  desc";
/*     */     } else {
/*     */       
/*  82 */       str11 = "select t1.jobtitle   resultid,COUNT(t1.id)   resultcount from HrmResource  t1,HrmJobTitles  t2 where (t1.accounttype is null or t1.accounttype=0) and t1.jobtitle=t2.id " + str8 + " group by t1.jobtitle order by resultcount desc";
/*  83 */     }  this.rs.executeSql(str11);
/*     */     
/*  85 */     while (this.rs.next()) {
/*  86 */       int k = this.rs.getInt(2);
/*  87 */       j += k;
/*     */     } 
/*     */     
/*  90 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  91 */     HashMap<Object, Object> hashMap = null;
/*  92 */     if (i != 0 && this.rs.first()) {
/*  93 */       JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/*  94 */       String str = "";
/*     */       do {
/*  96 */         String str12 = this.rs.getString(1);
/*  97 */         int k = this.rs.getInt(2);
/*  98 */         hashMap = new HashMap<>();
/*     */         try {
/* 100 */           str = Util.toScreen(jobTitlesComInfo.getJobTitlesmark(str12), paramUser.getLanguage());
/* 101 */           str = str + "-";
/* 102 */           str = str + Util.toScreen(jobTitlesComInfo.getJobTitlesname(str12), paramUser.getLanguage());
/* 103 */         } catch (Exception exception) {
/* 104 */           exception.printStackTrace();
/*     */         } 
/* 106 */         hashMap.put("title", str);
/* 107 */         hashMap.put("name", Util.toScreen(jobTitlesComInfo.getJobTitlesname(str12), paramUser.getLanguage()));
/* 108 */         hashMap.put("result", String.valueOf(k));
/* 109 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(k), String.valueOf(i)));
/* 110 */         hashMap.put("total", String.valueOf(i));
/* 111 */         arrayList.add(hashMap);
/* 112 */       } while (this.rs.next());
/*     */     } 
/* 114 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmJobTitleRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */