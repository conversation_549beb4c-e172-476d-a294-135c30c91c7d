/*    */ package weaver.hrm.report.manager;
/*    */ 
/*    */ import weaver.framework.BaseDao;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.report.dao.HrmRpSubTemplateConDao;
/*    */ import weaver.hrm.report.domain.HrmRpSubTemplateCon;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmRpSubTemplateConManager
/*    */   extends BaseManager<HrmRpSubTemplateCon>
/*    */ {
/* 14 */   private HrmRpSubTemplateConDao dao = null;
/*    */   
/*    */   public HrmRpSubTemplateConManager() {
/* 17 */     this.dao = new HrmRpSubTemplateConDao();
/* 18 */     setDao((BaseDao)this.dao);
/*    */   }
/*    */   
/*    */   public void deleteCon(Comparable paramComparable) {
/* 22 */     this.dao.deleteConByTemplateId(paramComparable);
/*    */   }
/*    */   
/*    */   public void saveCon(int paramInt, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7) {
/* 26 */     HrmRpSubTemplateCon hrmRpSubTemplateCon = new HrmRpSubTemplateCon();
/* 27 */     hrmRpSubTemplateCon.setTemplateId(Integer.valueOf(paramInt));
/* 28 */     hrmRpSubTemplateCon.setColName(paramString1);
/* 29 */     hrmRpSubTemplateCon.setConHtmltype(paramString2);
/* 30 */     hrmRpSubTemplateCon.setConType(paramString3);
/* 31 */     hrmRpSubTemplateCon.setConOpt(paramString4);
/* 32 */     hrmRpSubTemplateCon.setConValue(paramString5);
/* 33 */     hrmRpSubTemplateCon.setConOpt1(paramString6);
/* 34 */     hrmRpSubTemplateCon.setConValue1(paramString7);
/* 35 */     insert(hrmRpSubTemplateCon);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmRpSubTemplateConManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */