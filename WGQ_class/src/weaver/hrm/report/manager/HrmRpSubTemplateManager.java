/*    */ package weaver.hrm.report.manager;
/*    */ 
/*    */ import weaver.framework.BaseDao;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.report.dao.HrmRpSubTemplateDao;
/*    */ import weaver.hrm.report.domain.HrmRpSubTemplate;
/*    */ 
/*    */ public class HrmRpSubTemplateManager
/*    */   extends BaseManager<HrmRpSubTemplate>
/*    */ {
/* 11 */   private HrmRpSubTemplateDao dao = null;
/*    */   
/*    */   public HrmRpSubTemplateManager() {
/* 14 */     this.dao = new HrmRpSubTemplateDao();
/* 15 */     setDao((BaseDao)this.dao);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmRpSubTemplateManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */