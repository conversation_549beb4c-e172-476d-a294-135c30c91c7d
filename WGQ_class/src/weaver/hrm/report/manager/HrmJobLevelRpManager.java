/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmJobLevelRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  25 */     String str1 = paramMap.get("fromdate");
/*  26 */     String str2 = paramMap.get("enddate");
/*  27 */     String str3 = paramMap.get("department");
/*  28 */     String str4 = paramMap.get("location");
/*  29 */     String str5 = paramMap.get("status");
/*  30 */     String str6 = paramMap.get("area");
/*     */     
/*  32 */     boolean bool = false;
/*  33 */     int i = 0;
/*     */     
/*  35 */     String str7 = "";
/*  36 */     String str8 = "";
/*     */     
/*  38 */     if (str6.equals("")) {
/*  39 */       str6 = "5";
/*     */     }
/*  41 */     if (str5.equals("")) {
/*  42 */       str5 = "8";
/*     */     }
/*     */     
/*  45 */     if (!str1.equals("")) {
/*  46 */       str7 = str7 + " and startdate>='" + str1 + "'";
/*  47 */       if (str8.equals("")) {
/*  48 */         str8 = " where startdate>='" + str1 + "'";
/*     */       } else {
/*     */         
/*  51 */         str8 = str8 + " and startdate>='" + str1 + "'";
/*     */       } 
/*     */     } 
/*  54 */     if (!str2.equals("")) {
/*  55 */       str7 = str7 + " and (startdate<='" + str2 + "' or startdate is null)";
/*  56 */       if (str8.equals("")) {
/*  57 */         str8 = " where (startdate<='" + str2 + "' or startdate is null)";
/*     */       } else {
/*     */         
/*  60 */         str8 = str8 + " and (startdate<='" + str2 + "' or startdate is null)";
/*     */       } 
/*     */     } 
/*     */     
/*  64 */     if (!str4.equals("")) {
/*  65 */       str7 = str7 + " and locationid =" + str4;
/*  66 */       if (str8.equals("")) {
/*  67 */         str8 = " where locationid =" + str4;
/*     */       } else {
/*     */         
/*  70 */         str8 = str8 + " and locationid =" + str4;
/*     */       } 
/*     */     } 
/*  73 */     if (!str3.equals("")) {
/*  74 */       str7 = str7 + " and departmentid =" + str3;
/*  75 */       if (str8.equals("")) {
/*  76 */         str8 = " where departmentid =" + str3;
/*     */       } else {
/*     */         
/*  79 */         str8 = str8 + " and departmentid =" + str3;
/*     */       } 
/*     */     } 
/*     */     
/*  83 */     if (!str5.equals("") && !str5.equals("9")) {
/*  84 */       if (str5.equals("8")) {
/*  85 */         str7 = str7 + " and status <= 3";
/*  86 */         if (str8.equals("")) {
/*  87 */           str8 = " where status <= 3";
/*     */         } else {
/*  89 */           str8 = str8 + " and status <= 3";
/*     */         } 
/*     */       } else {
/*  92 */         str7 = str7 + " and status =" + str5;
/*     */         
/*  94 */         if (str8.equals("")) {
/*  95 */           str8 = " where status =" + str5;
/*     */         } else {
/*  97 */           str8 = str8 + " and status =" + str5;
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/* 102 */     String str9 = AppDetachComInfo.getInnerResourceSql();
/*     */     
/* 104 */     str7 = str7 + " and " + str9;
/* 105 */     if (str8.equals("")) {
/* 106 */       str8 = " where " + str9;
/*     */     } else {
/* 108 */       str8 = str8 + " and " + str9;
/*     */     } 
/*     */     
/* 111 */     String str10 = "";
/* 112 */     String str11 = "";
/* 113 */     int j = 0;
/* 114 */     if (str8.equals("")) {
/* 115 */       str11 = "select count(*)  from HrmResource where accounttype is null or accounttype=0 ";
/*     */     } else {
/* 117 */       str11 = "select count(*)  from HrmResource " + str8 + " and (accounttype is null or accounttype=0)";
/*     */     } 
/* 119 */     this.rs.executeSql(str11);
/*     */     
/* 121 */     this.rs.next();
/* 122 */     i = this.rs.getInt(1);
/*     */ 
/*     */     
/* 125 */     if (str7.equals("")) {
/* 126 */       str10 = "select count(*) resultcount from HrmResource  where (joblevel = 0 or joblevel is null) and (accounttype is null or accounttype=0)";
/*     */     } else {
/* 128 */       str10 = "select count(*) resultcount from HrmResource where (joblevel = 0 or joblevel is null) and (accounttype is null or accounttype=0) " + str7;
/*     */     } 
/* 130 */     this.rs.executeSql(str10);
/* 131 */     this.rs.next();
/* 132 */     j = this.rs.getInt(1);
/*     */     
/* 134 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 135 */     HashMap<Object, Object> hashMap = null;
/* 136 */     if (j != 0) {
/* 137 */       hashMap = new HashMap<Object, Object>();
/* 138 */       hashMap.put("title", "0 " + SystemEnv.getHtmlLabelName(15872, paramUser.getLanguage()));
/* 139 */       hashMap.put("result", String.valueOf(j));
/* 140 */       hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 141 */       hashMap.put("total", String.valueOf(i));
/* 142 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 145 */     str10 = "select max(joblevel) from HrmResource where joblevel>0";
/* 146 */     this.rs.executeSql(str10);
/* 147 */     this.rs.next();
/* 148 */     int k = this.rs.getInt(1);
/*     */     
/* 150 */     int m = Util.getIntValue(str6);
/* 151 */     int n = ((m == 0) ? 0 : (k / m)) + 1;
/* 152 */     bool = true;
/*     */     
/* 154 */     for (byte b = 0; b < n; b++) {
/* 155 */       int i1 = b * m + 1;
/* 156 */       int i2 = (b + 1) * m;
/*     */       
/* 158 */       if (str7.equals("")) {
/* 159 */         str10 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and joblevel >=" + i1 + "  and joblevel <=" + i2;
/*     */       } else {
/* 161 */         str10 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and joblevel >=" + i1 + "  and joblevel <=" + i2 + str7;
/*     */       } 
/* 163 */       this.rs.executeSql(str10);
/* 164 */       this.rs.next();
/* 165 */       j = this.rs.getInt(1);
/*     */       
/* 167 */       if (j != 0) {
/* 168 */         hashMap = new HashMap<Object, Object>();
/* 169 */         String str = (m == 1) ? (i1 + " " + SystemEnv.getHtmlLabelName(15872, paramUser.getLanguage())) : (i1 + "-" + i2 + " " + SystemEnv.getHtmlLabelName(15872, paramUser.getLanguage()));
/* 170 */         hashMap.put("title", str);
/* 171 */         hashMap.put("result", String.valueOf(j));
/* 172 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 173 */         hashMap.put("total", String.valueOf(i));
/* 174 */         arrayList.add(hashMap);
/*     */       } 
/* 176 */       if (!bool) { bool = true; }
/* 177 */       else { bool = false; }
/*     */     
/* 179 */     }  return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmJobLevelRpManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */