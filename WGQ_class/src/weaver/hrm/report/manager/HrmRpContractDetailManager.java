/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.contract.ContractTypeComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmRpContractDetailManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  27 */     String str1 = Util.null2String(paramMap.get("fromdate"));
/*  28 */     String str2 = Util.null2String(paramMap.get("enddate"));
/*  29 */     String str3 = Util.null2String(paramMap.get("fromTodate"));
/*  30 */     String str4 = Util.null2String(paramMap.get("endTodate"));
/*  31 */     String str5 = Util.null2String(paramMap.get("from"));
/*  32 */     String str6 = Util.null2String(paramMap.get("subcompanyid1"));
/*  33 */     String str7 = Util.null2String(paramMap.get("typeid"));
/*  34 */     String str8 = Util.null2String(paramMap.get("department"));
/*  35 */     String str9 = Util.null2String(paramMap.get("typepar"));
/*  36 */     String str10 = Util.null2String(paramMap.get("lastname"));
/*  37 */     String str11 = Util.null2String(paramMap.get("jobid"));
/*  38 */     String str12 = Util.null2String(paramMap.get("workstatus"));
/*     */     
/*  40 */     int i = 0;
/*  41 */     String str13 = "";
/*     */     
/*  43 */     if (!str1.equals("")) {
/*  44 */       str13 = str13 + " and t1.contractstartdate>='" + str1 + "'";
/*     */     }
/*  46 */     if (!str2.equals("")) {
/*  47 */       str13 = str13 + " and (t1.contractstartdate<='" + str2 + "' or t1.contractstartdate is null)";
/*     */     }
/*  49 */     if (!str3.equals("")) {
/*  50 */       str13 = str13 + " and t1.contractenddate>='" + str3 + "'";
/*     */     }
/*  52 */     if (!str4.equals("")) {
/*  53 */       if (this.rs.getDBType().equals("oracle")) {
/*  54 */         str13 = str13 + " and (t1.contractenddate<='" + str4 + "' and t1.contractenddate is not null)";
/*     */       } else {
/*  56 */         str13 = str13 + " and (t1.contractenddate<='" + str4 + "' and t1.contractenddate is not null and t1.contractenddate <> '')";
/*     */       } 
/*     */     }
/*  59 */     if (!str9.equals("")) {
/*  60 */       str13 = str13 + " and t2.jobtitle in (select id from HrmJobTitles where jobtitlename like '%" + str9 + "%')";
/*     */     }
/*  62 */     if (!str8.equals("")) {
/*  63 */       str13 = str13 + "  and t2.departmentid in  (" + str8 + ") ";
/*     */     }
/*  65 */     if (!str7.equals("")) {
/*  66 */       str13 = str13 + " and t1.contracttypeid=" + str7 + " ";
/*     */     }
/*  68 */     if (!str11.equals("")) {
/*  69 */       str13 = str13 + "  and t2.jobtitle = " + str11 + " ";
/*     */     }
/*  71 */     if (!str10.equals("")) {
/*  72 */       str13 = str13 + "  and t2.lastname like '%" + str10 + "%' ";
/*     */     }
/*     */     
/*  75 */     if (!str12.equals("") && !str12.equals("9")) {
/*  76 */       if (str12.equals("8")) {
/*  77 */         str13 = str13 + " and t2.status <= 3  ";
/*     */       } else {
/*  79 */         str13 = str13 + " and t2.status =" + str12 + " ";
/*     */       } 
/*     */     }
/*     */     
/*  83 */     if (!str6.equals("") && !str5.equals("detail")) {
/*  84 */       str13 = str13 + " and t2.id in (select id from HrmResource where subcompanyid1 in (" + str6 + ")) ";
/*     */     }
/*  86 */     if (str5.equals("detail"))
/*     */     {
/*  88 */       str13 = str13 + " and t2.id in (select id from HrmResource where subcompanyid1 in (" + str6 + ")) ";
/*     */     }
/*  90 */     String str14 = "";
/*  91 */     str14 = "select count(t1.id) from HrmContract t1,HrmResource t2 where (t2.accounttype is null or t2.accounttype=0) and t1.contractman=t2.id " + str13;
/*  92 */     this.rs.executeSql(str14);
/*  93 */     this.rs.next();
/*  94 */     i = this.rs.getInt(1);
/*     */     
/*  96 */     String str15 = "";
/*  97 */     str15 = "select t1.*,departmentid,jobtitle from HrmContract t1,HrmResource t2 where (t2.accounttype is null or t2.accounttype=0) and t1.contractman=t2.id " + str13 + " order by contractstartdate desc";
/*  98 */     this.rs.executeSql(str15);
/*  99 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 100 */     HashMap<Object, Object> hashMap = null;
/*     */     
/* 102 */     if (i != 0) {
/* 103 */       while (this.rs.next()) {
/* 104 */         hashMap = new HashMap<>();
/*     */         
/* 106 */         String str16 = Util.null2String(this.rs.getString("departmentid"));
/* 107 */         String str17 = Util.toScreen(this.rs.getString("contractstartdate"), paramUser.getLanguage());
/* 108 */         String str18 = Util.toScreen(this.rs.getString("contractenddate"), paramUser.getLanguage());
/*     */         
/* 110 */         String str19 = "";
/* 111 */         String str20 = "";
/* 112 */         String str21 = "";
/* 113 */         String str22 = "";
/*     */         try {
/* 115 */           str19 = Util.toScreen((new ResourceComInfo()).getResourcename(this.rs.getString("contractman")), paramUser.getLanguage());
/* 116 */           str20 = Util.toScreen((new ContractTypeComInfo()).getContractTypename(this.rs.getString("contracttypeid")), paramUser.getLanguage());
/* 117 */           str21 = Util.toScreen((new DepartmentComInfo()).getDepartmentname(str16), paramUser.getLanguage());
/* 118 */           str22 = Util.toScreen((new JobTitlesComInfo()).getJobTitlesname(this.rs.getString("jobtitle")), paramUser.getLanguage());
/* 119 */         } catch (Exception exception) {
/* 120 */           exception.printStackTrace();
/*     */         } 
/* 122 */         hashMap.put("deptname", str21);
/* 123 */         hashMap.put("jobname", str22);
/* 124 */         hashMap.put("typename", str20);
/* 125 */         hashMap.put("hrmname", str19);
/* 126 */         hashMap.put("deptid", str16);
/* 127 */         hashMap.put("jobid", Util.null2String(this.rs.getString("jobtitle")));
/* 128 */         hashMap.put("typeid", Util.null2String(this.rs.getString("contracttypeid")));
/* 129 */         hashMap.put("userid", Util.null2String(this.rs.getString("contractman")));
/*     */         
/* 131 */         hashMap.put("resourcename", "<a href=\"/hrm/resource/HrmResource.jsp?id=" + Util.null2String(this.rs.getString("contractman")) + "\" target=\"_fullwindow\">" + str19 + "</a>");
/* 132 */         hashMap.put("contracttypename", "<a href=\"/hrm/contract/contracttype/HrmContractTypeEditDo.jsp?isreport=1&id=" + Util.null2String(this.rs.getString("contracttypeid")) + "\" target=\"_fullwindow\">" + str20 + "</a>");
/* 133 */         hashMap.put("departnemtname", "<a href=\"/hrm/company/HrmDepartmentDsp.jsp?id=" + str16 + "\" target=\"_fullwindow\">" + str21 + "</a>");
/* 134 */         hashMap.put("jobtitlename", "<a href=\"/hrm/HrmDialogTab.jsp?_fromURL=HrmJobTitlesEditNotShowBtn&id=" + Util.null2String(this.rs.getString("jobtitle")) + "\" target=\"_fullwindow\">" + str22 + "</a>");
/* 135 */         hashMap.put("contractstartdate", str17);
/* 136 */         hashMap.put("contractenddate", str18);
/* 137 */         arrayList.add(hashMap);
/*     */       } 
/*     */     }
/* 140 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmRpContractDetailManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */