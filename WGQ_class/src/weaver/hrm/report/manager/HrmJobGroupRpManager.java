/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.job.JobGroupsComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmJobGroupRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  25 */     String str1 = Util.null2String(paramMap.get("fromdate"));
/*  26 */     String str2 = Util.null2String(paramMap.get("enddate"));
/*  27 */     String str3 = Util.null2String(paramMap.get("location"));
/*  28 */     String str4 = Util.null2String(paramMap.get("status"));
/*  29 */     String str5 = Util.null2String(paramMap.get("department"));
/*  30 */     String str6 = Util.null2String(paramMap.get("subcompany"));
/*  31 */     String str7 = Util.null2String(paramMap.get("jobGroupMark"));
/*     */     
/*  33 */     int i = 0;
/*  34 */     int j = 0;
/*  35 */     String str8 = "";
/*     */     
/*  37 */     if (!str1.equals("")) {
/*  38 */       str8 = str8 + " and t1.startdate>='" + str1 + "'";
/*     */     }
/*  40 */     if (!str2.equals("")) {
/*  41 */       str8 = str8 + " and (t1.startdate<='" + str2 + "' or t1.startdate is null)";
/*     */     }
/*  43 */     if (!str7.equals("")) {
/*  44 */       str8 = str8 + " and t4.jobGroupRemark like  '%" + str7 + "%'";
/*     */     }
/*     */     
/*  47 */     if (!str5.equals("")) {
/*  48 */       str8 = str8 + " and t1.departmentid  in (" + str5 + ")";
/*     */     }
/*     */     
/*  51 */     if (!str6.equals("")) {
/*  52 */       str8 = str8 + " and t1.subcompanyid1  in (" + str6 + ") ";
/*     */     }
/*     */     
/*  55 */     if (!str3.equals("")) {
/*  56 */       str8 = str8 + " and t1.locationid =" + str3;
/*     */     }
/*  58 */     if (!str4.equals("") && !str4.equals("9")) {
/*  59 */       if (str4.equals("8")) {
/*  60 */         str8 = str8 + " and t1.status <= 3";
/*     */       } else {
/*  62 */         str8 = str8 + " and t1.status =" + str4;
/*     */       } 
/*     */     }
/*     */     
/*  66 */     String str9 = AppDetachComInfo.getInnerResourceSql("t1");
/*     */     
/*  68 */     str8 = str8 + " and " + str9;
/*     */     
/*  70 */     String str10 = "select count(t1.id) from HrmResource  t1,HrmJobTitles  t2,HrmJobActivities t3,HrmJobGroups t4  where (t1.accounttype is null or t1.accounttype=0)  and t1.jobtitle=t2.id and t2.jobactivityid = t3.id and t3.jobgroupid = t4.id  and 1=1" + str8;
/*  71 */     this.rs.executeSql(str10);
/*  72 */     this.rs.next();
/*  73 */     i = this.rs.getInt(1);
/*     */     
/*  75 */     String str11 = "";
/*  76 */     if (str8.equals("")) {
/*  77 */       str11 = "select t4.id   resultid,COUNT(t1.id)   resultcount from HrmResource  t1,HrmJobTitles  t2,HrmJobActivities t3,HrmJobGroups t4 where (t1.accounttype is null or t1.accounttype=0) and t1.jobtitle=t2.id and t2.jobactivityid = t3.id and t3.jobgroupid = t4.id group by t4.id order by resultcount  desc";
/*     */     } else {
/*     */       
/*  80 */       str11 = "select t4.id   resultid,COUNT(t1.id)   resultcount from HrmResource  t1,HrmJobTitles  t2, HrmJobActivities t3,HrmJobGroups t4 where (t1.accounttype is null or t1.accounttype=0) and t1.jobtitle=t2.id and t2.jobactivityid = t3.id and t3.jobgroupid = t4.id " + str8 + " group by t4.id order by resultcount  desc";
/*  81 */     }  this.rs.executeSql(str11);
/*     */     
/*  83 */     while (this.rs.next()) {
/*  84 */       int k = this.rs.getInt(2);
/*  85 */       j += k;
/*     */     } 
/*     */     
/*  88 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  89 */     HashMap<Object, Object> hashMap = null;
/*  90 */     JobGroupsComInfo jobGroupsComInfo = new JobGroupsComInfo();
/*  91 */     if (i != 0 && this.rs.first()) {
/*     */       do {
/*  93 */         String str12 = this.rs.getString(1);
/*  94 */         int k = this.rs.getInt(2);
/*     */         
/*  96 */         hashMap = new HashMap<>();
/*  97 */         String str13 = "";
/*     */         try {
/*  99 */           str13 = Util.toScreen(jobGroupsComInfo.getJobGroupsremarks(str12), paramUser.getLanguage());
/* 100 */           str13 = str13 + "-";
/* 101 */           str13 = str13 + Util.toScreen(jobGroupsComInfo.getJobGroupsname(str12), paramUser.getLanguage());
/* 102 */         } catch (Exception exception) {
/* 103 */           exception.printStackTrace();
/*     */         } 
/* 105 */         hashMap.put("title", str13);
/* 106 */         hashMap.put("mark", Util.toScreen(jobGroupsComInfo.getJobGroupsremarks(str12), paramUser.getLanguage()));
/* 107 */         hashMap.put("name", Util.toScreen(jobGroupsComInfo.getJobGroupsname(str12), paramUser.getLanguage()));
/* 108 */         hashMap.put("result", String.valueOf(k));
/* 109 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(k), String.valueOf(i)));
/* 110 */         hashMap.put("total", String.valueOf(i));
/* 111 */         arrayList.add(hashMap);
/* 112 */       } while (this.rs.next());
/*     */     }
/* 114 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmJobGroupRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */