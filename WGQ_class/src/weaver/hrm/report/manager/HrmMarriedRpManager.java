/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.common.SplitPageTagFormat;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmMarriedRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  25 */     String str1 = paramMap.get("fromdate");
/*  26 */     String str2 = paramMap.get("enddate");
/*  27 */     String str3 = paramMap.get("department");
/*  28 */     String str4 = paramMap.get("location");
/*  29 */     String str5 = paramMap.get("status");
/*     */     
/*  31 */     int i = 0;
/*  32 */     String str6 = "";
/*  33 */     String str7 = "";
/*     */     
/*  35 */     if (str5.equals("")) {
/*  36 */       str5 = "8";
/*     */     }
/*     */     
/*  39 */     if (!str1.equals("")) {
/*  40 */       str6 = str6 + " and startdate>='" + str1 + "'";
/*  41 */       if (str7.equals("")) {
/*  42 */         str7 = " where startdate>='" + str1 + "'";
/*     */       } else {
/*     */         
/*  45 */         str7 = str7 + " and startdate>='" + str1 + "'";
/*     */       } 
/*     */     } 
/*  48 */     if (!str2.equals("")) {
/*  49 */       str6 = str6 + " and (startdate<='" + str2 + "' or startdate is null)";
/*  50 */       if (str7.equals("")) {
/*  51 */         str7 = " where (startdate<='" + str2 + "' or startdate is null)";
/*     */       } else {
/*     */         
/*  54 */         str7 = str7 + " and (startdate<='" + str2 + "' or startdate is null)";
/*     */       } 
/*     */     } 
/*     */     
/*  58 */     if (!str4.equals("")) {
/*  59 */       str6 = str6 + " and locationid =" + str4;
/*  60 */       if (str7.equals("")) {
/*  61 */         str7 = " where locationid =" + str4;
/*     */       } else {
/*     */         
/*  64 */         str7 = str7 + " and locationid =" + str4;
/*     */       } 
/*     */     } 
/*  67 */     if (!str3.equals("")) {
/*  68 */       str6 = str6 + " and departmentid =" + str3;
/*  69 */       if (str7.equals("")) {
/*  70 */         str7 = " where departmentid =" + str3;
/*     */       } else {
/*     */         
/*  73 */         str7 = str7 + " and departmentid =" + str3;
/*     */       } 
/*     */     } 
/*     */     
/*  77 */     if (!str5.equals("") && !str5.equals("9")) {
/*  78 */       if (str5.equals("8")) {
/*  79 */         str6 = str6 + " and status <= 3";
/*  80 */         if (str7.equals("")) {
/*  81 */           str7 = " where status <= 3";
/*     */         } else {
/*  83 */           str7 = str7 + " and status <= 3";
/*     */         } 
/*     */       } else {
/*  86 */         str6 = str6 + " and status =" + str5;
/*     */         
/*  88 */         if (str7.equals("")) {
/*  89 */           str7 = " where status =" + str5;
/*     */         } else {
/*  91 */           str7 = str7 + " and status =" + str5;
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*  96 */     String str8 = AppDetachComInfo.getInnerResourceSql();
/*     */     
/*  98 */     str6 = str6 + " and " + str8;
/*     */     
/* 100 */     String str9 = "";
/* 101 */     String str10 = "";
/* 102 */     if (str7.equals("")) {
/* 103 */       str10 = "select count(*)  from HrmResource where accounttype is null or accounttype=0 ";
/*     */     } else {
/* 105 */       str10 = "select count(*)  from HrmResource " + str7 + " and (accounttype is null or accounttype=0)";
/*     */     } 
/* 107 */     this.rs.executeSql(str10);
/* 108 */     this.rs.next();
/* 109 */     i = this.rs.getInt(1);
/*     */     
/* 111 */     if (str6.equals("")) {
/* 112 */       str9 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and maritalstatus is null or maritalstatus =''";
/*     */     } else {
/* 114 */       str9 = "select count(*) resultcount from HrmResource where (accounttype is null or accounttype=0) and (maritalstatus is null or maritalstatus ='')" + str6;
/*     */     } 
/* 116 */     this.rs.executeSql(str9);
/* 117 */     this.rs.next();
/* 118 */     int j = this.rs.getInt(1);
/*     */     
/* 120 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 121 */     HashMap<Object, Object> hashMap = null;
/* 122 */     if (j != 0) {
/* 123 */       hashMap = new HashMap<>();
/* 124 */       hashMap.put("title", SystemEnv.getHtmlLabelName(15863, paramUser.getLanguage()));
/* 125 */       hashMap.put("result", String.valueOf(j));
/* 126 */       hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 127 */       hashMap.put("total", String.valueOf(i));
/* 128 */       arrayList.add(hashMap);
/*     */     } 
/* 130 */     if (i != 0) {
/* 131 */       for (byte b = 0; b < 3; b++) {
/* 132 */         String str = "";
/* 133 */         if (b == 0) { str = "0"; }
/* 134 */         else if (b == 1) { str = "1"; }
/* 135 */         else if (b == 2) { str = "2"; }
/*     */         
/* 137 */         if (str6.equals("")) {
/* 138 */           str9 = "select  COUNT(*)   resultcount from HrmResource   where (accounttype is null or accounttype=0) and maritalstatus = '" + str + "'";
/*     */         } else {
/* 140 */           str9 = "select  COUNT(*)   resultcount from HrmResource   where (accounttype is null or accounttype=0) and maritalstatus = '" + str + "'" + str6;
/*     */         } 
/* 142 */         this.rs.executeSql(str9);
/* 143 */         this.rs.next();
/*     */         
/* 145 */         j = this.rs.getInt(1);
/*     */         
/* 147 */         if (j != 0) {
/* 148 */           hashMap = new HashMap<>();
/* 149 */           hashMap.put("title", (new SplitPageTagFormat()).colFormat(str, "{cmd:array[" + paramUser.getLanguage() + ";0=470,1=471,2=472]}"));
/* 150 */           hashMap.put("result", String.valueOf(j));
/* 151 */           hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 152 */           hashMap.put("total", String.valueOf(i));
/* 153 */           arrayList.add(hashMap);
/*     */         } 
/*     */       } 
/*     */     }
/* 157 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmMarriedRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */