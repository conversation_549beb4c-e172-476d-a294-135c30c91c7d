/*     */ package weaver.hrm.report.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmDepartmentRpManager
/*     */   extends ReportManager
/*     */   implements IReport
/*     */ {
/*     */   public List<Map<String, String>> getResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  26 */     String str1 = Util.null2String(paramMap.get("fromdate"));
/*  27 */     String str2 = Util.null2String(paramMap.get("enddate"));
/*  28 */     String str3 = Util.null2String(paramMap.get("location"));
/*  29 */     String str4 = Util.null2String(paramMap.get("status"));
/*  30 */     String str5 = Util.null2String(paramMap.get("department"));
/*  31 */     String str6 = Util.null2String(paramMap.get("subcompany"));
/*  32 */     String str7 = Util.null2String(paramMap.get("departmentmark"));
/*     */     
/*  34 */     int i = 0;
/*  35 */     String str8 = "";
/*     */     
/*  37 */     if (!str1.equals("")) {
/*  38 */       str8 = str8 + " and t1.startdate>='" + str1 + "'";
/*     */     }
/*  40 */     if (!str2.equals("")) {
/*  41 */       str8 = str8 + " and (t1.startdate<='" + str2 + "' or t1.startdate is null)";
/*     */     }
/*     */     
/*  44 */     if (!str7.equals("")) {
/*  45 */       str8 = str8 + " and t2.departmentmark  like  '%" + str7 + "%'";
/*     */     }
/*     */     
/*  48 */     if (!str5.equals("")) {
/*  49 */       str8 = str8 + " and t1.departmentid in (" + str5 + ")";
/*     */     }
/*     */     
/*  52 */     if (!str6.equals("")) {
/*  53 */       str8 = str8 + " and t1.subcompanyid1 in (" + str6 + ")";
/*     */     }
/*     */     
/*  56 */     if (!str3.equals("")) {
/*  57 */       str8 = str8 + " and t1.locationid =" + str3;
/*     */     }
/*  59 */     if (!str4.equals("") && !str4.equals("9")) {
/*  60 */       if (str4.equals("8")) {
/*  61 */         str8 = str8 + " and t1.status <= 3";
/*     */       } else {
/*  63 */         str8 = str8 + " and t1.status =" + str4;
/*     */       } 
/*     */     }
/*  66 */     String str9 = AppDetachComInfo.getInnerResourceSql("t1");
/*     */     
/*  68 */     str8 = str8 + " and " + str9;
/*     */     
/*  70 */     String str10 = "";
/*  71 */     if (str8.equals("")) {
/*  72 */       str10 = "select t1.subcompanyid1, t1.departmentid, COUNT(t1.id)   resultcount from HrmResource  t1,HrmDepartment  t2 where (t1.accounttype is null or t1.accounttype=0) and t1.departmentid=t2.id  group by t1.subcompanyid1, t1.departmentid order by resultcount desc";
/*     */     } else {
/*     */       
/*  75 */       str10 = "select t1.subcompanyid1, t1.departmentid, COUNT(t1.id)   resultcount from HrmResource  t1,HrmDepartment  t2 where (t1.accounttype is null or t1.accounttype=0) and t1.departmentid=t2.id " + str8 + " group by t1.subcompanyid1, t1.departmentid order by resultcount desc";
/*     */     } 
/*  77 */     this.rs.executeSql(str10);
/*     */     
/*  79 */     while (this.rs.next()) {
/*  80 */       int j = this.rs.getInt("resultcount");
/*  81 */       i += j;
/*     */     } 
/*  83 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  84 */     HashMap<Object, Object> hashMap = null;
/*     */     
/*  86 */     if (i != 0 && this.rs.first()) {
/*  87 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  88 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  89 */       String str11 = "";
/*  90 */       String str12 = "";
/*     */       do {
/*  92 */         String str13 = this.rs.getString("subcompanyid1");
/*  93 */         String str14 = this.rs.getString("departmentid");
/*  94 */         int j = this.rs.getInt("resultcount");
/*  95 */         hashMap = new HashMap<>();
/*     */         try {
/*  97 */           str11 = Util.toScreen(subCompanyComInfo.getSubCompanyname(str13), paramUser.getLanguage());
/*  98 */           str12 = Util.toScreen(departmentComInfo.getDepartmentname(str14), paramUser.getLanguage());
/*  99 */         } catch (Exception exception) {
/* 100 */           exception.printStackTrace();
/*     */         } 
/* 102 */         hashMap.put("subcompanyname", str11);
/* 103 */         hashMap.put("departmentname", str12);
/* 104 */         hashMap.put("departmentid", str14);
/* 105 */         hashMap.put("result", String.valueOf(j));
/* 106 */         hashMap.put("percent", StringUtil.formatDoubleValue(String.valueOf(j), String.valueOf(i)));
/* 107 */         hashMap.put("total", String.valueOf(i));
/* 108 */         arrayList.add(hashMap);
/* 109 */       } while (this.rs.next());
/*     */     } 
/* 111 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/manager/HrmDepartmentRpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */