/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpTrainHourByTypeManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private SysMaintenanceLog log;
/*     */   private String startdate;
/*     */   private String enddate;
/*     */   private int indepartmentid;
/*     */   private int traintypeid;
/*     */   
/*     */   public RpTrainHourByTypeManager() {
/*  24 */     resetParameter();
/*     */   }
/*     */   
/*     */   public void resetParameter() {
/*  28 */     this.startdate = "";
/*  29 */     this.enddate = "";
/*  30 */     this.traintypeid = -1;
/*  31 */     this.indepartmentid = 0;
/*     */   }
/*     */   
/*     */   public void setStartdate(String paramString) {
/*  35 */     this.startdate = paramString;
/*     */   }
/*     */   
/*     */   public void setEnddate(String paramString) {
/*  39 */     this.enddate = paramString;
/*     */   }
/*     */   
/*     */   public void setTrainTypeid(int paramInt) {
/*  43 */     this.traintypeid = paramInt;
/*     */   }
/*     */   
/*     */   public void setInDepartmentid(int paramInt) {
/*  47 */     this.indepartmentid = paramInt;
/*  48 */     writeLog("" + this.indepartmentid);
/*     */   }
/*     */   
/*     */   public int getTrainHourSum() throws Exception {
/*  52 */     return this.statement.getInt("totalhours");
/*     */   }
/*     */   
/*     */   public int getDepartmentid() throws Exception {
/*  56 */     return this.statement.getInt("departmentid");
/*     */   }
/*     */   
/*     */   public int getResourceid() throws Exception {
/*  60 */     return this.statement.getInt("resourceid");
/*     */   }
/*     */   
/*     */   private void selectRpTrainHourByType1() throws Exception {
/*  64 */     this.statement = new RecordSet();
/*     */     try {
/*  66 */       String str1 = "";
/*  67 */       if (this.traintypeid == 0) { str1 = ""; }
/*  68 */       else { str1 = " and a.traintype=" + this.traintypeid; }
/*  69 */        if (!this.startdate.equals("")) {
/*  70 */         str1 = str1 + " and a.trainstartdate >='" + this.startdate + "'";
/*     */       }
/*  72 */       if (!this.enddate.equals("")) {
/*  73 */         str1 = str1 + " and a.trainenddate <='" + this.enddate + "'";
/*     */       }
/*     */       
/*  76 */       String str2 = "select sum(a.trainhour) totalhours,b.departmentid from HrmTrainRecord a,HrmResource b where a.resourceid = b.id";
/*  77 */       str2 = str2 + str1;
/*  78 */       str2 = str2 + " group by b.departmentid";
/*  79 */       this.statement.executeSql(str2);
/*  80 */       writeLog(str2);
/*     */     }
/*  82 */     catch (Exception exception) {
/*  83 */       writeLog(exception);
/*  84 */       throw exception;
/*     */     } 
/*     */   }
/*     */   
/*     */   private void selectRpTrainHourByType2() throws Exception {
/*  89 */     this.statement = new RecordSet();
/*     */     try {
/*  91 */       String str1 = "";
/*  92 */       if (this.traintypeid == 0) { str1 = ""; }
/*  93 */       else { str1 = " and a.traintype=" + this.traintypeid; }
/*  94 */        if (!this.startdate.equals("")) {
/*  95 */         str1 = str1 + " and a.trainstartdate >='" + this.startdate + "'";
/*     */       }
/*  97 */       if (!this.enddate.equals("")) {
/*  98 */         str1 = str1 + " and a.trainenddate <='" + this.enddate + "'";
/*     */       }
/*     */       
/* 101 */       String str2 = "select sum(a.trainhour) totalhours,a.resourceid from HrmTrainRecord a,HrmResource b where\ta.resourceid  = b.id";
/* 102 */       str2 = str2 + str1;
/* 103 */       str2 = str2 + " and a.resourceid in(select id from HrmResource where departmentid = " + this.indepartmentid + ")";
/* 104 */       str2 = str2 + " group by a.resourceid";
/* 105 */       this.statement.executeSql(str2);
/* 106 */       writeLog(str2);
/*     */     
/*     */     }
/* 109 */     catch (Exception exception) {
/* 110 */       writeLog(exception);
/* 111 */       throw exception;
/*     */     } 
/*     */   }
/*     */   
/*     */   public void selectRpTrainHourByType() throws Exception {
/* 116 */     if (this.indepartmentid != 0) {
/*     */       
/* 118 */       selectRpTrainHourByType2();
/*     */     } else {
/*     */       
/* 121 */       selectRpTrainHourByType1();
/*     */     } 
/*     */   }
/*     */   
/*     */   public boolean next() throws Exception {
/* 126 */     return this.statement.next();
/*     */   }
/*     */   
/*     */   public void closeStatement() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpTrainHourByTypeManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */