/*    */ package weaver.hrm.report.dao;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.framework.BaseConnection;
/*    */ import weaver.framework.BaseDao;
/*    */ import weaver.hrm.report.domain.HrmReport;
/*    */ import weaver.hrm.schedule.domain.HrmSchedule;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmReportDao
/*    */   extends BaseConnection
/*    */   implements BaseDao<HrmReport>
/*    */ {
/*    */   public void delete(Comparable paramComparable) {}
/*    */   
/*    */   public List<HrmReport> find(Map<String, Comparable> paramMap) {
/* 26 */     return null;
/*    */   }
/*    */   
/*    */   public HrmReport get(Comparable paramComparable) {
/* 30 */     return null;
/*    */   }
/*    */   
/*    */   public Comparable insert(HrmReport paramHrmReport) {
/* 34 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public void update(HrmReport paramHrmReport) {}
/*    */ 
/*    */   
/*    */   public List<HrmSchedule> getScheduleSignList(HrmReport paramHrmReport, int paramInt) {
/* 42 */     if (paramHrmReport == null || StringUtil.isNull(new String[] { paramHrmReport.getFromDate(), paramHrmReport.getToDate() })) return new ArrayList<>();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 49 */     StringBuffer stringBuffer = (new StringBuffer("select sign.id,sign.userId,sign.userType,sign.signType,sign.signDate,sign.signTime,")).append("sign.clientAddress,sign.isInCom,sign.signFrom,sign.longitude,sign.latitude,").append("sign.addr,res.subCompanyId1 as subCompanyId,res.departmentId,dep.departmentName,").append("res.id as resourceId,res.lastName as resourceName,res.status,res.workcode from HrmScheduleSign sign").append(" left join HrmResource res on sign.userId = res.id left join HrmDepartment dep on res.departmentid = dep.id").append(" where sign.signDate between '").append(paramHrmReport.getFromDate()).append("' and '").append(paramHrmReport.getToDate()).append("'");
/* 50 */     if (StringUtil.isNotNull(paramHrmReport.getResId())) {
/* 51 */       stringBuffer.append(" and sign.userId in (").append(paramHrmReport.getResId()).append(")");
/*    */     }
/* 53 */     if (paramHrmReport.getDepartmentId() > 0) {
/* 54 */       stringBuffer.append(" and res.departmentId = ").append(paramHrmReport.getDepartmentId());
/*    */     }
/* 56 */     if (paramHrmReport.getSubCompanyId() > 0) {
/* 57 */       stringBuffer.append(" and res.subcompanyid1 = ").append(paramHrmReport.getSubCompanyId());
/*    */     }
/* 59 */     if (StringUtil.isNotNull(paramHrmReport.getStatus()) && !paramHrmReport.getStatus().equals("8") && !paramHrmReport.getStatus().equals("9")) {
/* 60 */       stringBuffer.append(" and res.status=").append(paramHrmReport.getStatus());
/* 61 */     } else if (paramHrmReport.getStatus().equals("8") || paramHrmReport.getStatus().equals("")) {
/* 62 */       stringBuffer.append(" and res.status in (0,1,2,3)");
/*    */     } 
/* 64 */     stringBuffer.append(" and sign.signType = '").append(paramInt).append("'")
/*    */ 
/*    */ 
/*    */       
/* 68 */       .append(" and sign.isInCom = '1' and sign.userType = '1'")
/* 69 */       .append(" order by res.subCompanyId1,res.departmentId,sign.userId,sign.signDate,sign.signTime");
/* 70 */     this.rs.executeSql(stringBuffer.toString());
/*    */     
/* 72 */     ArrayList<HrmSchedule> arrayList = new ArrayList();
/* 73 */     HrmSchedule hrmSchedule = null;
/* 74 */     while (this.rs.next()) {
/* 75 */       hrmSchedule = new HrmSchedule();
/* 76 */       hrmSchedule.setId(Integer.valueOf(this.rs.getInt("id")));
/* 77 */       hrmSchedule.setUserId(this.rs.getInt("userId"));
/* 78 */       hrmSchedule.setUserType(this.rs.getString("userType"));
/* 79 */       hrmSchedule.setSignType(this.rs.getString("signType"));
/* 80 */       hrmSchedule.setSignDate(this.rs.getString("signDate"));
/* 81 */       hrmSchedule.setSignTime(this.rs.getString("signTime"));
/* 82 */       hrmSchedule.setWorkcode(this.rs.getString("workcode"));
/* 83 */       hrmSchedule.setClientAddress(this.rs.getString("clientAddress"));
/* 84 */       hrmSchedule.setIsInCom(this.rs.getString("isInCom"));
/* 85 */       hrmSchedule.setSignFrom(this.rs.getString("signFrom"));
/* 86 */       hrmSchedule.setLongitude(this.rs.getString("longitude"));
/* 87 */       hrmSchedule.setLatitude(this.rs.getString("latitude"));
/* 88 */       hrmSchedule.setAddr(this.rs.getString("addr"));
/* 89 */       hrmSchedule.setSubCompanyId(this.rs.getInt("subCompanyId"));
/* 90 */       hrmSchedule.setDepartmentId(this.rs.getInt("departmentId"));
/* 91 */       hrmSchedule.setResourceId(this.rs.getInt("resourceId"));
/* 92 */       hrmSchedule.setResourceName(this.rs.getString("resourceName"));
/* 93 */       hrmSchedule.setDepartmentName(this.rs.getString("departmentName"));
/* 94 */       hrmSchedule.setStatus(this.rs.getString("status"));
/* 95 */       arrayList.add(hrmSchedule);
/*    */     } 
/* 97 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/dao/HrmReportDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */