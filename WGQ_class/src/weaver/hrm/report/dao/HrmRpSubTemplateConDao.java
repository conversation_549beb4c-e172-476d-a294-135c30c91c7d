/*     */ package weaver.hrm.report.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.report.domain.HrmRpSubTemplateCon;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmRpSubTemplateConDao
/*     */   implements BaseDao<HrmRpSubTemplateCon>
/*     */ {
/*  23 */   private RecordSet rs = new RecordSet();
/*     */ 
/*     */   
/*     */   public Comparable insert(HrmRpSubTemplateCon paramHrmRpSubTemplateCon) {
/*  27 */     if (paramHrmRpSubTemplateCon == null) {
/*  28 */       return Integer.valueOf(-1);
/*     */     }
/*  30 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_rp_sub_template_con (template_id,col_name,con_htmltype,con_type,con_opt,con_value,").append(" con_opt1,con_value1 )").append(" values(" + paramHrmRpSubTemplateCon.getTemplateId() + ",'" + paramHrmRpSubTemplateCon.getColName() + "','" + paramHrmRpSubTemplateCon.getConHtmltype() + "','" + paramHrmRpSubTemplateCon.getConType() + "',").append(" '" + paramHrmRpSubTemplateCon.getConOpt() + "','" + paramHrmRpSubTemplateCon.getConValue() + "','" + paramHrmRpSubTemplateCon.getConOpt1() + "','" + paramHrmRpSubTemplateCon.getConValue1() + "' )");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  35 */     this.rs.executeSql(stringBuffer.toString());
/*  36 */     return Integer.valueOf(1);
/*     */   }
/*     */   
/*     */   public void update(HrmRpSubTemplateCon paramHrmRpSubTemplateCon) {
/*  40 */     if (paramHrmRpSubTemplateCon == null) {
/*     */       return;
/*     */     }
/*  43 */     StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_rp_sub_template_con t set").append(" t.template_id = " + paramHrmRpSubTemplateCon.getTemplateId() + ",t.col_name = '" + paramHrmRpSubTemplateCon.getColName() + "',t.con_htmltype = '" + paramHrmRpSubTemplateCon.getConHtmltype() + "',").append(" t.con_type = '" + paramHrmRpSubTemplateCon.getConType() + "',t.con_opt = '" + paramHrmRpSubTemplateCon.getConOpt() + "',t.con_value = '" + paramHrmRpSubTemplateCon.getConValue() + "',").append(" t.con_opt1 = '" + paramHrmRpSubTemplateCon.getConOpt1() + "',t.con_value1 = '" + paramHrmRpSubTemplateCon.getConValue1() + "'").append(" where t.id = " + paramHrmRpSubTemplateCon.getId() + "");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  49 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */   
/*     */   public List<HrmRpSubTemplateCon> find(Map<String, Comparable> paramMap) {
/*  53 */     ArrayList<HrmRpSubTemplateCon> arrayList = new ArrayList();
/*  54 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.template_id,t.col_name,t.con_htmltype,t.con_type,t.con_opt,t.con_value,").append(" t.con_opt1,t.con_value1").append(" from hrm_rp_sub_template_con t").append(" where  1 = 1");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  59 */     if (paramMap != null) {
/*  60 */       if (paramMap.containsKey("id")) {
/*  61 */         stringBuffer.append(" and t.id = ").append(Tools.vString(paramMap.get("id")));
/*     */       }
/*  63 */       if (paramMap.containsKey("begin_id")) {
/*  64 */         stringBuffer.append(" and t.id >= ").append(Tools.vString(paramMap.get("begin_id")));
/*     */       }
/*  66 */       if (paramMap.containsKey("end_id")) {
/*  67 */         stringBuffer.append(" and t.id < ").append(Tools.vString(paramMap.get("end_id")));
/*     */       }
/*  69 */       if (paramMap.containsKey("sql_id")) {
/*  70 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_id")));
/*     */       }
/*  72 */       if (paramMap.containsKey("templateId")) {
/*  73 */         stringBuffer.append(" and t.template_id = ").append(Tools.vString(paramMap.get("templateId")));
/*     */       }
/*  75 */       if (paramMap.containsKey("begin_templateId")) {
/*  76 */         stringBuffer.append(" and t.template_id >= ").append(Tools.vString(paramMap.get("begin_templateId")));
/*     */       }
/*  78 */       if (paramMap.containsKey("end_templateId")) {
/*  79 */         stringBuffer.append(" and t.template_id < ").append(Tools.vString(paramMap.get("end_templateId")));
/*     */       }
/*  81 */       if (paramMap.containsKey("sql_templateId")) {
/*  82 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_templateId")));
/*     */       }
/*  84 */       if (paramMap.containsKey("colName")) {
/*  85 */         stringBuffer.append(" and t.col_name = '").append(Tools.vString(paramMap.get("colName"))).append("'");
/*     */       }
/*  87 */       if (paramMap.containsKey("like_colName")) {
/*  88 */         stringBuffer.append(" and t.col_name like '%").append(Tools.vString(paramMap.get("like_colName"))).append("%'");
/*     */       }
/*  90 */       if (paramMap.containsKey("sql_colName")) {
/*  91 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_colName")));
/*     */       }
/*  93 */       if (paramMap.containsKey("conHtmltype")) {
/*  94 */         stringBuffer.append(" and t.con_htmltype = '").append(Tools.vString(paramMap.get("conHtmltype"))).append("'");
/*     */       }
/*  96 */       if (paramMap.containsKey("like_conHtmltype")) {
/*  97 */         stringBuffer.append(" and t.con_htmltype like '%").append(Tools.vString(paramMap.get("like_conHtmltype"))).append("%'");
/*     */       }
/*  99 */       if (paramMap.containsKey("sql_conHtmltype")) {
/* 100 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_conHtmltype")));
/*     */       }
/* 102 */       if (paramMap.containsKey("conType")) {
/* 103 */         stringBuffer.append(" and t.con_type = '").append(Tools.vString(paramMap.get("conType"))).append("'");
/*     */       }
/* 105 */       if (paramMap.containsKey("like_conType")) {
/* 106 */         stringBuffer.append(" and t.con_type like '%").append(Tools.vString(paramMap.get("like_conType"))).append("%'");
/*     */       }
/* 108 */       if (paramMap.containsKey("sql_conType")) {
/* 109 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_conType")));
/*     */       }
/* 111 */       if (paramMap.containsKey("conOpt")) {
/* 112 */         stringBuffer.append(" and t.con_opt = '").append(Tools.vString(paramMap.get("conOpt"))).append("'");
/*     */       }
/* 114 */       if (paramMap.containsKey("like_conOpt")) {
/* 115 */         stringBuffer.append(" and t.con_opt like '%").append(Tools.vString(paramMap.get("like_conOpt"))).append("%'");
/*     */       }
/* 117 */       if (paramMap.containsKey("sql_conOpt")) {
/* 118 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_conOpt")));
/*     */       }
/* 120 */       if (paramMap.containsKey("conValue")) {
/* 121 */         stringBuffer.append(" and t.con_value = '").append(Tools.vString(paramMap.get("conValue"))).append("'");
/*     */       }
/* 123 */       if (paramMap.containsKey("like_conValue")) {
/* 124 */         stringBuffer.append(" and t.con_value like '%").append(Tools.vString(paramMap.get("like_conValue"))).append("%'");
/*     */       }
/* 126 */       if (paramMap.containsKey("sql_conValue")) {
/* 127 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_conValue")));
/*     */       }
/* 129 */       if (paramMap.containsKey("conOpt1")) {
/* 130 */         stringBuffer.append(" and t.con_opt1 = '").append(Tools.vString(paramMap.get("conOpt1"))).append("'");
/*     */       }
/* 132 */       if (paramMap.containsKey("like_conOpt1")) {
/* 133 */         stringBuffer.append(" and t.con_opt1 like '%").append(Tools.vString(paramMap.get("like_conOpt1"))).append("%'");
/*     */       }
/* 135 */       if (paramMap.containsKey("sql_conOpt1")) {
/* 136 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_conOpt1")));
/*     */       }
/* 138 */       if (paramMap.containsKey("conValue1")) {
/* 139 */         stringBuffer.append(" and t.con_value1 = '").append(Tools.vString(paramMap.get("conValue1"))).append("'");
/*     */       }
/* 141 */       if (paramMap.containsKey("like_conValue1")) {
/* 142 */         stringBuffer.append(" and t.con_value1 like '%").append(Tools.vString(paramMap.get("like_conValue1"))).append("%'");
/*     */       }
/* 144 */       if (paramMap.containsKey("sql_conValue1")) {
/* 145 */         stringBuffer.append(" " + Tools.vString(paramMap.get("sql_conValue1")));
/*     */       }
/* 147 */       if (paramMap.containsKey("mfsql")) {
/* 148 */         stringBuffer.append(" " + Tools.vString(paramMap.get("mfsql")));
/*     */       }
/* 150 */       if (paramMap.containsKey("sqlorderby")) {
/* 151 */         stringBuffer.append(" order by " + Tools.vString(paramMap.get("sqlorderby")));
/*     */       } else {
/* 153 */         stringBuffer.append(" order by t.id ").append((Tools.vString(paramMap.get("sqlsortway")).length() > 0) ? Tools.vString(paramMap.get("sqlsortway")) : "desc");
/*     */       } 
/*     */     } 
/* 156 */     this.rs.executeSql(stringBuffer.toString());
/* 157 */     HrmRpSubTemplateCon hrmRpSubTemplateCon = null;
/* 158 */     while (this.rs.next()) {
/* 159 */       hrmRpSubTemplateCon = new HrmRpSubTemplateCon();
/* 160 */       hrmRpSubTemplateCon.setId(Integer.valueOf(Tools.parseToInt(this.rs.getString("id"))));
/* 161 */       hrmRpSubTemplateCon.setTemplateId(Integer.valueOf(Tools.parseToInt(this.rs.getString("template_id"))));
/* 162 */       hrmRpSubTemplateCon.setColName(Tools.vString(this.rs.getString("col_name")));
/* 163 */       hrmRpSubTemplateCon.setConHtmltype(Tools.vString(this.rs.getString("con_htmltype")));
/* 164 */       hrmRpSubTemplateCon.setConType(Tools.vString(this.rs.getString("con_type")));
/* 165 */       hrmRpSubTemplateCon.setConOpt(Tools.vString(this.rs.getString("con_opt")));
/* 166 */       hrmRpSubTemplateCon.setConValue(Tools.vString(this.rs.getString("con_value")));
/* 167 */       hrmRpSubTemplateCon.setConOpt1(Tools.vString(this.rs.getString("con_opt1")));
/* 168 */       hrmRpSubTemplateCon.setConValue1(Tools.vString(this.rs.getString("con_value1")));
/* 169 */       arrayList.add(hrmRpSubTemplateCon);
/*     */     } 
/* 171 */     return arrayList;
/*     */   }
/*     */   
/*     */   public HrmRpSubTemplateCon get(Comparable paramComparable) {
/* 175 */     HrmRpSubTemplateCon hrmRpSubTemplateCon = null;
/* 176 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 177 */     hashMap.put("id", paramComparable);
/* 178 */     List<HrmRpSubTemplateCon> list = find((Map)hashMap);
/* 179 */     if (list != null && list.size() > 0) {
/* 180 */       hrmRpSubTemplateCon = list.get(0);
/*     */     }
/* 182 */     return hrmRpSubTemplateCon;
/*     */   }
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/* 186 */     this.rs.executeSql("delete from hrm_rp_sub_template_con where id in ( " + paramComparable + " )");
/*     */   }
/*     */   
/*     */   public void deleteConByTemplateId(Comparable paramComparable) {
/* 190 */     this.rs.executeSql("delete from hrm_rp_sub_template_con where template_id in ( " + paramComparable + " )");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/dao/HrmRpSubTemplateConDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */