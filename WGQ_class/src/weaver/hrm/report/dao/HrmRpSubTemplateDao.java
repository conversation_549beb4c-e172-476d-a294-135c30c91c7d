/*     */ package weaver.hrm.report.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.common.database.dialect.DbDialectFactory;
/*     */ import weaver.hrm.common.database.dialect.DialectUtil;
/*     */ import weaver.hrm.report.domain.HrmRpSubTemplate;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmRpSubTemplateDao
/*     */   implements BaseDao<HrmRpSubTemplate>
/*     */ {
/*  24 */   private RecordSet rs = new RecordSet();
/*     */ 
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/*  28 */     this.rs.executeSql("DELETE FROM HRM_RP_SUB_TEMPLATE WHERE ID IN (" + paramComparable + ")");
/*     */   }
/*     */   
/*     */   public List<HrmRpSubTemplate> find(Map<String, Comparable> paramMap) {
/*  32 */     return find(paramMap, null);
/*     */   }
/*     */   
/*     */   private List<HrmRpSubTemplate> find(Map<String, Comparable> paramMap, String paramString) {
/*  36 */     ArrayList<HrmRpSubTemplate> arrayList = new ArrayList();
/*     */     
/*  38 */     StringBuffer stringBuffer = new StringBuffer(" SELECT ID,NAME,AUTHOR");
/*  39 */     if ("oracle".equals(this.rs.getDBType())) {
/*  40 */       stringBuffer.append(",CREATE_DATE+0 CREATE_DATE,SCOPE,DELFLAG ");
/*     */     } else {
/*  42 */       stringBuffer.append(",CREATE_DATE,SCOPE,DELFLAG ");
/*     */     } 
/*  44 */     stringBuffer.append("FROM HRM_RP_SUB_TEMPLATE WHERE DELFLAG = 0 ");
/*  45 */     if (Tools.isNotNull(paramString)) {
/*  46 */       stringBuffer.append(paramString);
/*  47 */     } else if (paramMap != null) {
/*  48 */       if (paramMap.containsKey("name")) {
/*  49 */         stringBuffer.append(" AND NAME = '").append(Tools.vString(paramMap.get("name"))).append("'");
/*     */       }
/*  51 */       if (paramMap.containsKey("like_name")) {
/*  52 */         stringBuffer.append(" AND NAME LIKE '%").append(Tools.vString(paramMap.get("name"))).append("%'");
/*     */       }
/*  54 */       if (paramMap.containsKey("author")) {
/*  55 */         stringBuffer.append(" AND AUTHOR = ").append(Tools.vString(paramMap.get("author")));
/*     */       }
/*  57 */       if (paramMap.containsKey("scope")) {
/*  58 */         stringBuffer.append(" AND SCOPE = '").append(Tools.vString(paramMap.get("scope"))).append("'");
/*     */       }
/*  60 */       if (paramMap.containsKey("like_scope")) {
/*  61 */         stringBuffer.append(" AND SCOPE LIKE '%").append(Tools.vString(paramMap.get("scope"))).append("%'");
/*     */       }
/*     */     } 
/*  64 */     this.rs.executeSql(stringBuffer.toString());
/*  65 */     while (this.rs.next()) {
/*  66 */       HrmRpSubTemplate hrmRpSubTemplate = new HrmRpSubTemplate();
/*  67 */       hrmRpSubTemplate.setId(this.rs.getInt("ID"));
/*  68 */       hrmRpSubTemplate.setName(this.rs.getString("NAME"));
/*  69 */       hrmRpSubTemplate.setAuthor(this.rs.getInt("AUTHOR"));
/*  70 */       hrmRpSubTemplate.setCreateDate(DateUtil.parseToDate(this.rs.getString("CREATE_DATE")));
/*  71 */       hrmRpSubTemplate.setScope(this.rs.getString("SCOPE"));
/*  72 */       hrmRpSubTemplate.setDelflag(this.rs.getInt("DELFLAG"));
/*  73 */       arrayList.add(hrmRpSubTemplate);
/*     */     } 
/*  75 */     return arrayList;
/*     */   }
/*     */   
/*     */   public HrmRpSubTemplate get(Comparable paramComparable) {
/*  79 */     HrmRpSubTemplate hrmRpSubTemplate = null;
/*  80 */     StringBuffer stringBuffer = (new StringBuffer(" AND ID = ")).append(paramComparable);
/*  81 */     List<HrmRpSubTemplate> list = find(null, stringBuffer.toString());
/*  82 */     if (list != null && list.size() > 0) {
/*  83 */       hrmRpSubTemplate = list.get(0);
/*     */     }
/*  85 */     return hrmRpSubTemplate;
/*     */   }
/*     */   
/*     */   public Comparable insert(HrmRpSubTemplate paramHrmRpSubTemplate) {
/*  89 */     if (paramHrmRpSubTemplate == null || Tools.isNull(paramHrmRpSubTemplate.getName())) {
/*  90 */       return Integer.valueOf(-1);
/*     */     }
/*  92 */     String str = "'" + Tools.getDateTime(paramHrmRpSubTemplate.getCreateDate()) + "'";
/*  93 */     if ("oracle".equals(this.rs.getDBType())) {
/*  94 */       str = "to_timestamp(" + str + ",'yyyy-mm-dd hh24:mi')";
/*  95 */     } else if (DialectUtil.isMySql(this.rs.getDBType())) {
/*  96 */       str = DbDialectFactory.get(this.rs.getDBType()).strToDateSql(str, "'%Y-%m-%d %H:%i'");
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 104 */     StringBuffer stringBuffer = (new StringBuffer()).append("INSERT INTO HRM_RP_SUB_TEMPLATE(NAME,AUTHOR,CREATE_DATE,SCOPE,DELFLAG) VALUES(").append("'").append(paramHrmRpSubTemplate.getName()).append("',").append(paramHrmRpSubTemplate.getAuthor()).append(",").append(str).append(",").append("'").append(paramHrmRpSubTemplate.getScope()).append("',").append(paramHrmRpSubTemplate.getDelflag()).append(")");
/* 105 */     this.rs.executeSql(stringBuffer.toString());
/* 106 */     int i = -1;
/* 107 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 108 */     hashMap.put("name", paramHrmRpSubTemplate.getName());
/* 109 */     hashMap.put("author", Integer.valueOf(paramHrmRpSubTemplate.getAuthor()));
/* 110 */     hashMap.put("scope", paramHrmRpSubTemplate.getScope());
/* 111 */     List<HrmRpSubTemplate> list = find((Map)hashMap);
/* 112 */     if (list != null && list.size() > 0) {
/* 113 */       i = ((HrmRpSubTemplate)list.get(0)).getId();
/*     */     }
/* 115 */     return Integer.valueOf(i);
/*     */   }
/*     */   
/*     */   public void update(HrmRpSubTemplate paramHrmRpSubTemplate) {
/* 119 */     if (paramHrmRpSubTemplate == null || paramHrmRpSubTemplate.getId() <= 0) {
/*     */       return;
/*     */     }
/* 122 */     String str = "'" + Tools.getDateTime(paramHrmRpSubTemplate.getCreateDate()) + "'";
/* 123 */     if ("oracle".equals(this.rs.getDBType())) {
/* 124 */       str = "to_timestamp(" + str + ",'yyyy-mm-dd hh24:mi')";
/* 125 */     } else if (DialectUtil.isMySql(this.rs.getDBType())) {
/* 126 */       str = DbDialectFactory.get(this.rs.getDBType()).strToDateSql(str, "'%Y-%m-%d %H:%i'");
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 134 */     StringBuffer stringBuffer = (new StringBuffer()).append("UPDATE HRM_RP_SUB_TEMPLATE SET NAME = '").append(paramHrmRpSubTemplate.getName()).append("',").append("AUTHOR = ").append(paramHrmRpSubTemplate.getAuthor()).append(",").append(str).append(",").append("SCOPE = '").append(paramHrmRpSubTemplate.getScope()).append("',").append("DELFLAG = ").append(paramHrmRpSubTemplate.getDelflag()).append(" WHERE ID = ").append(paramHrmRpSubTemplate.getId());
/* 135 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/dao/HrmRpSubTemplateDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */