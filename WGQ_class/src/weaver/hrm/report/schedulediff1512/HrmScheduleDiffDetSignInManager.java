/*     */ package weaver.hrm.report.schedulediff1512;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmMFScheduleDiff;
/*     */ import weaver.hrm.attendance.manager.HrmMFScheduleDiffManager;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffListComparator;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffDetSignInManager
/*     */   extends BaseBean
/*     */ {
/*  41 */   private User user = null;
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean sortForResult = true;
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  50 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSortForResult(boolean paramBoolean) {
/*  57 */     this.sortForResult = paramBoolean;
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  61 */     String str1 = paramMap.get("fromDate");
/*  62 */     String str2 = paramMap.get("toDate");
/*  63 */     int i = Util.getIntValue(paramMap.get("subCompanyId"), -1);
/*  64 */     int j = Util.getIntValue(paramMap.get("departmentId"), -1);
/*  65 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  66 */     return getScheduleList(str1, str2, i, j, str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) {
/*  81 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, String.valueOf(paramInt3));
/*     */   }
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  85 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  86 */     HashMap<Object, Object> hashMap = null;
/*     */ 
/*     */     
/*  89 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/*  90 */       .trim().equals("") || paramString1
/*  91 */       .compareTo(paramString2) > 0) {
/*  92 */       return arrayList;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/*  97 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  98 */       RecordSet recordSet = new RecordSet();
/*  99 */       StringBuffer stringBuffer = null;
/*     */       
/* 101 */       String str1 = "";
/* 102 */       String str2 = "";
/* 103 */       String str3 = "";
/* 104 */       String str4 = "";
/* 105 */       String str5 = "";
/* 106 */       String str6 = "";
/* 107 */       String str7 = "";
/* 108 */       String str8 = "";
/* 109 */       String str9 = "";
/* 110 */       String str10 = "";
/* 111 */       String str11 = "";
/*     */       
/* 113 */       Map map = null;
/* 114 */       String str12 = null;
/* 115 */       String str13 = "";
/* 116 */       String str14 = "";
/* 117 */       String str15 = null;
/* 118 */       String str16 = "1";
/* 119 */       String str17 = "";
/* 120 */       String str18 = recordSet.getDBType();
/*     */       
/* 122 */       String str19 = "";
/* 123 */       String str20 = "";
/* 124 */       boolean bool = false;
/* 125 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 126 */       hrmScheduleDiffUtil.setUser(this.user);
/*     */       
/* 128 */       HrmMFScheduleDiffManager hrmMFScheduleDiffManager = new HrmMFScheduleDiffManager();
/* 129 */       HrmMFScheduleDiff hrmMFScheduleDiff = null;
/*     */       
/* 131 */       for (str19 = paramString1; !bool; ) {
/*     */         
/* 133 */         if (str19.equals(paramString2)) {
/* 134 */           bool = true;
/*     */         }
/* 136 */         map = hrmScheduleDiffUtil.getOnDutyAndOffDutyTimeMap(str19, paramInt1);
/*     */         
/* 138 */         str12 = Util.null2String((String)map.get("onDutyTimeAM"));
/* 139 */         str13 = StringUtil.vString(map.get("offDutyTimeAM"));
/* 140 */         str14 = StringUtil.vString(map.get("onDutyTimePM"));
/* 141 */         str15 = Util.null2String((String)map.get("offDutyTimePM"));
/* 142 */         str16 = StringUtil.vString(map.get("signType"), "1");
/* 143 */         str17 = StringUtil.vString(map.get("signStartTime"));
/* 144 */         hrmMFScheduleDiff = new HrmMFScheduleDiff();
/* 145 */         hrmMFScheduleDiff.setClassName("HrmScheduleDiffDetSignInManager");
/* 146 */         hrmMFScheduleDiff.setCurrentDate(str19);
/* 147 */         hrmMFScheduleDiff.setDepartmentId(paramInt2);
/* 148 */         hrmMFScheduleDiff.setOffDutyTimeAM(str13);
/* 149 */         hrmMFScheduleDiff.setOffDutyTimePM(str15);
/* 150 */         hrmMFScheduleDiff.setOnDutyTimeAM(str12);
/* 151 */         hrmMFScheduleDiff.setOnDutyTimePM(str14);
/* 152 */         hrmMFScheduleDiff.setResourceId(paramString3);
/* 153 */         hrmMFScheduleDiff.setSignStartTime(str17);
/* 154 */         hrmMFScheduleDiff.setSignType(str16);
/* 155 */         hrmMFScheduleDiff.setSortForResult(this.sortForResult);
/* 156 */         hrmMFScheduleDiff.setSubCompanyId(paramInt1);
/* 157 */         hrmMFScheduleDiff.setSqlType(str18);
/* 158 */         hrmMFScheduleDiffManager.setBean(hrmMFScheduleDiff);
/*     */         
/* 160 */         stringBuffer = new StringBuffer();
/* 161 */         if (hrmMFScheduleDiffManager.isSecSign()) {
/* 162 */           stringBuffer.append(hrmMFScheduleDiffManager.getSQL());
/*     */         } else {
/* 164 */           stringBuffer.append(" select a.*,b.clientAddress,b.signFrom,b.LONGITUDE,b.LATITUDE,b.ADDR from (select b.subCompanyId1 as subCompanyId,b.departmentId,b.id as resourceId,b.lastName as resourceName,b.status,min(a.id) as signId ,a.signDate,min(a.signTime) as signTime ")
/* 165 */             .append("   from HrmScheduleSign a,HrmResource b ")
/* 166 */             .append("  where a.userId=b.id ")
/* 167 */             .append("    and a.signDate='").append(str19).append("'")
/* 168 */             .append("    and a.signType='1' ")
/* 169 */             .append("    and a.isInCom='1' ")
/* 170 */             .append("    and a.userType='1' ");
/*     */           
/* 172 */           if (paramInt1 > 0) {
/* 173 */             stringBuffer.append(" and  b.subCompanyId1=").append(paramInt1);
/*     */           }
/*     */           
/* 176 */           if (paramInt2 > 0) {
/* 177 */             stringBuffer.append(" and  b.departmentId=").append(paramInt2);
/*     */           }
/*     */           
/* 180 */           if (paramString3.length() > 0) {
/* 181 */             stringBuffer.append(" and  b.id in (").append(paramString3).append(")");
/*     */           }
/* 183 */           stringBuffer.append("    and b.status in(0,1,2,3) ");
/*     */           
/* 185 */           if ("oracle".equals(recordSet.getDBType())) {
/* 186 */             stringBuffer.append(" and b.loginid is not null ");
/*     */           } else {
/* 188 */             stringBuffer.append(" and b.loginid is not null and b.loginid<>'' ");
/*     */           } 
/*     */           
/* 191 */           stringBuffer.append("  group by b.subCompanyId1 ,b.departmentId,b.id ,b.lastName ,b.status ,a.signDate");
/* 192 */           stringBuffer.append(") a left join HrmScheduleSign b on a.resourceId = b.userId and a.signDate = b.signDate and a.signTime = b.signTime ");
/* 193 */           if (this.sortForResult) {
/* 194 */             stringBuffer.append("  order by a.subCompanyId asc,a.departmentId asc,a.resourceId asc,a.signDate  asc ");
/*     */           }
/*     */         } 
/*     */ 
/*     */         
/* 199 */         recordSet.executeSql(stringBuffer.toString());
/* 200 */         while (recordSet.next()) {
/* 201 */           str1 = Util.null2String(recordSet.getString("subCompanyId"));
/* 202 */           str2 = Util.null2String(recordSet.getString("departmentId"));
/* 203 */           str3 = departmentComInfo.getDepartmentname(str2);
/* 204 */           str4 = Util.null2String(recordSet.getString("resourceId"));
/* 205 */           str5 = Util.null2String(recordSet.getString("resourceName"));
/* 206 */           str6 = Util.null2String(recordSet.getString("status"));
/* 207 */           str7 = hrmScheduleDiffUtil.getStatusNameOfHrmResource(str6);
/* 208 */           str8 = Util.null2String(recordSet.getString("signId"));
/* 209 */           str9 = Util.null2String(recordSet.getString("signDate"));
/* 210 */           str10 = Util.null2String(recordSet.getString("signTime"));
/* 211 */           str11 = Util.null2String(recordSet.getString("clientAddress"));
/* 212 */           String str21 = "";
/* 213 */           String str22 = "";
/* 214 */           if (StringUtil.isNull(recordSet.getString("signFrom")) || StringUtil.vString(recordSet.getString("signFrom")).equalsIgnoreCase("pc")) {
/* 215 */             str22 = "";
/*     */           }
/* 217 */           else if (StringUtil.isNull(recordSet.getString("LONGITUDE")) || StringUtil.isNull(recordSet.getString("LATITUDE"))) {
/* 218 */             str22 = "";
/*     */           } else {
/* 220 */             str22 = StringUtil.vString(recordSet.getString("ADDR"), "" + SystemEnv.getHtmlLabelName(10000168, ThreadVarLanguage.getLang()) + "");
/*     */           } 
/*     */           
/* 223 */           if (StringUtil.isNotNull(str22)) {
/* 224 */             str21 = "<a href=\"javascript:void(0);\" onclick=\"openMap('" + StringUtil.vString(recordSet.getString("LONGITUDE")) + "','" + StringUtil.vString(recordSet.getString("LATITUDE")) + "','" + "1de5f5f7f410712e1280aaf08d2065f1" + "','" + (str22.equals("查看信息") ? "null" : str22) + "');\">" + str22 + "</a>";
/*     */           }
/* 226 */           hashMap = new HashMap<>();
/* 227 */           hashMap.put("subCompanyId", str1);
/* 228 */           hashMap.put("departmentId", str2);
/* 229 */           hashMap.put("departmentName", str3);
/* 230 */           hashMap.put("resourceId", str4);
/* 231 */           hashMap.put("resourceName", str5);
/* 232 */           hashMap.put("statusName", str7);
/* 233 */           hashMap.put("currentDate", str19);
/* 234 */           hashMap.put("signId", str8);
/* 235 */           hashMap.put("signDate", str9);
/* 236 */           hashMap.put("signTime", str10);
/* 237 */           hashMap.put("clientAddress", str11);
/* 238 */           hashMap.put("signType", str16);
/* 239 */           hashMap.put("signStartTime", str17);
/* 240 */           hashMap.put("addr", str21);
/* 241 */           hashMap.put("_addr", StringUtil.vString(recordSet.getString("ADDR")));
/* 242 */           hashMap.put("longitude", StringUtil.vString(recordSet.getString("LONGITUDE")));
/* 243 */           hashMap.put("latitude", StringUtil.vString(recordSet.getString("LATITUDE")));
/* 244 */           hashMap.put("mfer", "signIn");
/* 245 */           hashMap.put("addrDetail", str22.equals("查看信息") ? (StringUtil.vString(recordSet.getString("LONGITUDE")) + "," + StringUtil.vString(recordSet.getString("LATITUDE"))) : str22);
/* 246 */           arrayList.add(hashMap);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 251 */         str20 = TimeUtil.dateAdd(str19, 1);
/* 252 */         str19 = str20;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 257 */       if (this.sortForResult && arrayList.size() >= 2) {
/* 258 */         Collections.sort(arrayList, (Comparator<? super HashMap<Object, Object>>)new HrmScheduleDiffListComparator());
/*     */       }
/*     */       
/* 261 */       return arrayList;
/* 262 */     } catch (Exception exception) {
/* 263 */       return arrayList;
/*     */     } finally {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffDetSignInManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */