/*     */ package weaver.hrm.report.schedulediff1512;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmMFScheduleDiff;
/*     */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*     */ import weaver.hrm.attendance.manager.HrmMFScheduleDiffManager;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffListComparator;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffDetBeLateManager
/*     */   extends BaseBean
/*     */ {
/*  44 */   private User user = null;
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean sortForResult = true;
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  53 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSortForResult(boolean paramBoolean) {
/*  60 */     this.sortForResult = paramBoolean;
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  64 */     String str1 = paramMap.get("fromDate");
/*  65 */     String str2 = paramMap.get("toDate");
/*  66 */     int i = Util.getIntValue(paramMap.get("subCompanyId"), -1);
/*  67 */     int j = Util.getIntValue(paramMap.get("departmentId"), -1);
/*  68 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  69 */     return getScheduleList(str1, str2, i, j, str3);
/*     */   }
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) {
/*  73 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, String.valueOf(paramInt3));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  89 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  90 */     HashMap<Object, Object> hashMap = null;
/*     */     
/*  92 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/*  93 */       .trim().equals("") || paramString1
/*  94 */       .compareTo(paramString2) > 0) {
/*  95 */       return arrayList;
/*     */     }
/*     */     
/*     */     try {
/*  99 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 100 */       RecordSet recordSet = new RecordSet();
/* 101 */       StringBuffer stringBuffer = null;
/*     */       
/* 103 */       String str1 = "";
/* 104 */       String str2 = "";
/* 105 */       String str3 = "";
/* 106 */       String str4 = "";
/* 107 */       String str5 = "";
/* 108 */       String str6 = "";
/* 109 */       String str7 = "";
/* 110 */       String str8 = "";
/* 111 */       String str9 = "";
/* 112 */       String str10 = "";
/* 113 */       String str11 = "";
/*     */       
/* 115 */       Map map = null;
/* 116 */       String str12 = null;
/* 117 */       String str13 = "";
/* 118 */       String str14 = "";
/* 119 */       String str15 = null;
/* 120 */       String str16 = "1";
/* 121 */       String str17 = "";
/* 122 */       String str18 = recordSet.getDBType();
/* 123 */       boolean bool1 = false;
/*     */       
/* 125 */       String str19 = "";
/* 126 */       String str20 = "";
/* 127 */       boolean bool = false;
/* 128 */       boolean bool2 = true;
/* 129 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 130 */       hrmScheduleDiffUtil.setUser(this.user);
/*     */       
/* 132 */       HrmMFScheduleDiffManager hrmMFScheduleDiffManager = new HrmMFScheduleDiffManager();
/* 133 */       HrmMFScheduleDiff hrmMFScheduleDiff = null;
/* 134 */       for (str19 = paramString1; !bool; ) {
/*     */         
/* 136 */         if (str19.equals(paramString2)) {
/* 137 */           bool = true;
/*     */         }
/*     */         
/* 140 */         bool2 = hrmScheduleDiffUtil.getIsWorkday(str19, paramInt1, "");
/* 141 */         if (!bool2) {
/*     */           
/* 143 */           str20 = TimeUtil.dateAdd(str19, 1);
/* 144 */           str19 = str20;
/*     */           
/*     */           continue;
/*     */         } 
/*     */         
/* 149 */         map = hrmScheduleDiffUtil.getOnDutyAndOffDutyTimeMap(str19, paramInt1);
/*     */         
/* 151 */         str12 = Util.null2String((String)map.get("onDutyTimeAM"));
/* 152 */         str13 = StringUtil.vString(map.get("offDutyTimeAM"));
/* 153 */         str14 = StringUtil.vString(map.get("onDutyTimePM"));
/* 154 */         str15 = Util.null2String((String)map.get("offDutyTimePM"));
/* 155 */         str16 = StringUtil.vString(map.get("signType"), "1");
/* 156 */         str17 = StringUtil.vString(map.get("signStartTime"));
/* 157 */         hrmMFScheduleDiff = new HrmMFScheduleDiff();
/* 158 */         hrmMFScheduleDiff.setClassName("HrmScheduleDiffDetBeLateManager");
/* 159 */         hrmMFScheduleDiff.setCurrentDate(str19);
/* 160 */         hrmMFScheduleDiff.setDepartmentId(paramInt2);
/* 161 */         hrmMFScheduleDiff.setOffDutyTimeAM(str13);
/* 162 */         hrmMFScheduleDiff.setOffDutyTimePM(str15);
/* 163 */         hrmMFScheduleDiff.setOnDutyTimeAM(str12);
/* 164 */         hrmMFScheduleDiff.setOnDutyTimePM(str14);
/* 165 */         hrmMFScheduleDiff.setResourceId(paramString3);
/* 166 */         hrmMFScheduleDiff.setSignStartTime(str17);
/* 167 */         hrmMFScheduleDiff.setSignType(str16);
/* 168 */         hrmMFScheduleDiff.setSortForResult(this.sortForResult);
/* 169 */         hrmMFScheduleDiff.setSubCompanyId(paramInt1);
/* 170 */         hrmMFScheduleDiff.setSqlType(str18);
/* 171 */         hrmMFScheduleDiffManager.setBean(hrmMFScheduleDiff);
/*     */         
/* 173 */         stringBuffer = new StringBuffer();
/* 174 */         if (hrmMFScheduleDiffManager.isSecSign()) {
/* 175 */           stringBuffer.append(hrmMFScheduleDiffManager.getSQL());
/*     */         } else {
/*     */           
/* 178 */           stringBuffer.append(" select a.*,b.clientAddress,b.signFrom,b.LONGITUDE,b.LATITUDE,b.ADDR from( ");
/* 179 */           if ("oracle".equals(recordSet.getDBType())) {
/* 180 */             stringBuffer.append(" select b.subCompanyId1 as subCompanyId,b.departmentId,b.id as resourceId,b.lastName as resourceName,b.status,min(a.id) as signId ,a.signDate,min(a.signTime) as signTime ");
/*     */           } else {
/* 182 */             stringBuffer.append(" select top 1000000 b.subCompanyId1 as subCompanyId,b.departmentId,b.id as resourceId,b.lastName as resourceName,b.status,min(a.id) as signId ,a.signDate,min(a.signTime) as signTime ");
/*     */           } 
/* 184 */           stringBuffer
/* 185 */             .append("   from HrmScheduleSign a,HrmResource b ")
/* 186 */             .append("  where a.userId=b.id ")
/* 187 */             .append("    and a.signDate='").append(str19).append("'")
/* 188 */             .append("    and a.signType='1' ")
/* 189 */             .append("    and a.isInCom='1' ")
/* 190 */             .append("    and a.userType='1' ")
/*     */             
/* 192 */             .append("    and a.signTime<'").append(str15).append(":00' ");
/*     */           
/* 194 */           if (paramInt1 > 0) {
/* 195 */             stringBuffer.append(" and  b.subCompanyId1=").append(paramInt1);
/*     */           }
/*     */           
/* 198 */           if (paramInt2 > 0) {
/* 199 */             stringBuffer.append(" and  b.departmentId=").append(paramInt2);
/*     */           }
/*     */           
/* 202 */           if (paramString3.length() > 0) {
/* 203 */             stringBuffer.append(" and  b.id in (").append(paramString3).append(")");
/*     */           }
/* 205 */           stringBuffer.append("    and b.status in(0,1,2,3) ");
/*     */           
/* 207 */           if ("oracle".equals(recordSet.getDBType())) {
/* 208 */             stringBuffer.append(" and b.loginid is not null ");
/*     */           } else {
/* 210 */             stringBuffer.append(" and b.loginid is not null and b.loginid<>'' ");
/*     */           } 
/*     */           
/* 213 */           stringBuffer.append("  group by b.subCompanyId1 ,b.departmentId,b.id ,b.lastName ,b.status ,a.signDate");
/* 214 */           stringBuffer.append(" ) a left join HrmScheduleSign b on a.resourceId = b.userId and a.signDate = b.signDate and a.signTime = b.signTime where a.signTime>'").append(str12).append(":00' ");
/* 215 */           if (this.sortForResult) {
/* 216 */             stringBuffer.append("  order by a.subCompanyId asc,a.departmentId asc,a.resourceId asc,a.signDate  asc ");
/*     */           }
/*     */         } 
/*     */         
/* 220 */         recordSet.executeSql(stringBuffer.toString());
/* 221 */         while (recordSet.next()) {
/* 222 */           str1 = Util.null2String(recordSet.getString("subCompanyId"));
/* 223 */           str2 = Util.null2String(recordSet.getString("departmentId"));
/* 224 */           str3 = departmentComInfo.getDepartmentname(str2);
/* 225 */           str4 = Util.null2String(recordSet.getString("resourceId"));
/* 226 */           str5 = Util.null2String(recordSet.getString("resourceName"));
/* 227 */           str6 = Util.null2String(recordSet.getString("status"));
/* 228 */           str7 = hrmScheduleDiffUtil.getStatusNameOfHrmResource(str6);
/* 229 */           str8 = Util.null2String(recordSet.getString("signId"));
/* 230 */           str9 = Util.null2String(recordSet.getString("signDate"));
/* 231 */           str10 = Util.null2String(recordSet.getString("signTime"));
/* 232 */           str11 = Util.null2String(recordSet.getString("clientAddress"));
/* 233 */           String str21 = "";
/* 234 */           String str22 = "";
/* 235 */           if (StringUtil.isNull(recordSet.getString("signFrom")) || StringUtil.vString(recordSet.getString("signFrom")).equalsIgnoreCase("pc")) {
/* 236 */             str22 = "";
/*     */           }
/* 238 */           else if (StringUtil.isNull(recordSet.getString("LONGITUDE")) || StringUtil.isNull(recordSet.getString("LATITUDE"))) {
/* 239 */             str22 = "";
/*     */           } else {
/* 241 */             str22 = StringUtil.vString(recordSet.getString("ADDR"), "" + SystemEnv.getHtmlLabelName(10000168, ThreadVarLanguage.getLang()) + "");
/*     */           } 
/*     */           
/* 244 */           if (StringUtil.isNotNull(str22)) {
/* 245 */             str21 = "<a href=\"javascript:void(0);\" onclick=\"openMap('" + StringUtil.vString(recordSet.getString("LONGITUDE")) + "','" + StringUtil.vString(recordSet.getString("LATITUDE")) + "','" + "1de5f5f7f410712e1280aaf08d2065f1" + "','" + (str22.equals("查看信息") ? "null" : str22) + "');\">" + str22 + "</a>";
/*     */           }
/*     */           
/* 248 */           String str23 = str12;
/* 249 */           if (hrmMFScheduleDiffManager.isSecSign() && str10.compareTo(str14) > 0) {
/* 250 */             str23 = str14;
/*     */           }
/* 252 */           String str24 = str10;
/* 253 */           if (!hrmMFScheduleDiffManager.isSecSign() && str10.compareTo(str14) <= 0 && str10.compareTo(str13) >= 0) {
/* 254 */             str24 = str13 + ":00";
/*     */           }
/* 256 */           bool1 = getHasApproved(Util.getIntValue(str4, -1), str19, str23, str24);
/* 257 */           if (!bool1) {
/* 258 */             hashMap = new HashMap<>();
/* 259 */             hashMap.put("subCompanyId", str1);
/* 260 */             hashMap.put("departmentId", str2);
/* 261 */             hashMap.put("departmentName", str3);
/* 262 */             hashMap.put("resourceId", str4);
/* 263 */             hashMap.put("resourceName", str5);
/* 264 */             hashMap.put("statusName", str7);
/* 265 */             hashMap.put("currentDate", str19);
/* 266 */             hashMap.put("signId", str8);
/* 267 */             hashMap.put("signDate", str9);
/* 268 */             hashMap.put("signTime", str10);
/* 269 */             hashMap.put("clientAddress", str11);
/* 270 */             hashMap.put("signType", str16);
/* 271 */             hashMap.put("signStartTime", str17);
/* 272 */             hashMap.put("addr", str21);
/* 273 */             hashMap.put("_addr", StringUtil.vString(recordSet.getString("ADDR")));
/* 274 */             hashMap.put("longitude", StringUtil.vString(recordSet.getString("LONGITUDE")));
/* 275 */             hashMap.put("latitude", StringUtil.vString(recordSet.getString("LATITUDE")));
/* 276 */             hashMap.put("mfer", "beLate");
/* 277 */             hashMap.put("addrDetail", str22.equals("查看信息") ? (StringUtil.vString(recordSet.getString("LONGITUDE")) + "," + StringUtil.vString(recordSet.getString("LATITUDE"))) : str22);
/* 278 */             arrayList.add(hashMap);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 283 */         str20 = TimeUtil.dateAdd(str19, 1);
/* 284 */         str19 = str20;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 289 */       if (this.sortForResult && arrayList.size() >= 2) {
/* 290 */         Collections.sort(arrayList, (Comparator<? super HashMap<Object, Object>>)new HrmScheduleDiffListComparator());
/*     */       }
/*     */       
/* 293 */       return arrayList;
/* 294 */     } catch (Exception exception) {
/* 295 */       return arrayList;
/*     */     } finally {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean getHasApproved(int paramInt, String paramString1, String paramString2, String paramString3) {
/* 311 */     String str1 = getNextMinOfSignTime(paramString1 + " " + paramString3);
/* 312 */     RecordSet recordSet = new RecordSet();
/* 313 */     String str2 = "";
/* 314 */     String str3 = "";
/* 315 */     if (recordSet.getDBType().equals("oracle")) {
/* 316 */       str2 = " and concat(concat(t.fromDate,' '), t.fromTime) <= '" + paramString1 + " " + paramString2 + "'";
/* 317 */       str3 = " and concat(concat(t.toDate,' '), t.toTime) >= '" + str1 + "'";
/*     */     } else {
/* 319 */       str2 = " and t.fromDate+' '+t.fromTime <= '" + paramString1 + " " + paramString2 + "'";
/* 320 */       str3 = " and t.toDate+' '+t.toTime >= '" + str1 + "'";
/*     */     } 
/* 322 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 323 */     hashMap.put("fromDate", str2);
/* 324 */     hashMap.put("toDate", str3);
/* 325 */     hashMap.put("resourceId", " and t.resourceId = " + paramInt);
/* 326 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(0, hashMap));
/* 327 */     if (recordSet.next()) return true;
/*     */     
/* 329 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(1, hashMap));
/* 330 */     if (recordSet.next()) return true;
/*     */     
/* 332 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(2, hashMap));
/* 333 */     if (recordSet.next()) return true;
/*     */     
/* 335 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(4, hashMap));
/* 336 */     if (recordSet.next()) return true;
/*     */     
/* 338 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getNextMinOfSignTime(String paramString) {
/* 353 */     String str1 = "";
/*     */     
/* 355 */     if (paramString == null || paramString.trim().equals("") || paramString
/* 356 */       .length() < 19) {
/* 357 */       return str1;
/*     */     }
/*     */     
/* 360 */     String str2 = paramString.substring(17);
/*     */     
/* 362 */     if (str2.equals("00")) {
/* 363 */       str1 = paramString.substring(0, 16);
/*     */     } else {
/* 365 */       paramString = TimeUtil.timeAdd(paramString, 60);
/* 366 */       str1 = paramString.substring(0, 16);
/*     */     } 
/*     */     
/* 369 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffDetBeLateManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */