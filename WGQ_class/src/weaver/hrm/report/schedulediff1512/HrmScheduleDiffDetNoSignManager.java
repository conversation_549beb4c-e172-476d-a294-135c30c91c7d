/*     */ package weaver.hrm.report.schedulediff1512;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmMFScheduleDiff;
/*     */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*     */ import weaver.hrm.attendance.manager.HrmMFScheduleDiffManager;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffListComparator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffDetNoSignManager
/*     */   extends BaseBean
/*     */ {
/*  44 */   private User user = null;
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean sortForResult = true;
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  53 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSortForResult(boolean paramBoolean) {
/*  61 */     this.sortForResult = paramBoolean;
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  65 */     String str1 = paramMap.get("fromDate");
/*  66 */     String str2 = paramMap.get("toDate");
/*  67 */     int i = Util.getIntValue(paramMap.get("subCompanyId"), -1);
/*  68 */     int j = Util.getIntValue(paramMap.get("departmentId"), -1);
/*  69 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  70 */     return getScheduleList(str1, str2, i, j, str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) {
/*  85 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, String.valueOf(paramInt3));
/*     */   }
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  89 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  90 */     HashMap<Object, Object> hashMap = null;
/*     */ 
/*     */     
/*  93 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/*  94 */       .trim().equals("") || paramString1
/*  95 */       .compareTo(paramString2) > 0) {
/*  96 */       return arrayList;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 101 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 102 */       RecordSet recordSet = new RecordSet();
/* 103 */       StringBuffer stringBuffer = null;
/*     */       
/* 105 */       String str1 = "";
/* 106 */       String str2 = "";
/* 107 */       String str3 = "";
/* 108 */       String str4 = "";
/* 109 */       String str5 = "";
/* 110 */       String str6 = "";
/* 111 */       String str7 = "";
/* 112 */       String str8 = "";
/* 113 */       String str9 = "";
/* 114 */       String str10 = "";
/*     */       
/* 116 */       Map map = null;
/* 117 */       String str11 = null;
/* 118 */       String str12 = "";
/* 119 */       String str13 = "";
/* 120 */       String str14 = null;
/* 121 */       String str15 = "1";
/* 122 */       String str16 = "";
/* 123 */       String str17 = recordSet.getDBType();
/* 124 */       boolean bool1 = false;
/*     */       
/* 126 */       String str18 = "";
/* 127 */       String str19 = "";
/* 128 */       boolean bool = false;
/* 129 */       boolean bool2 = true;
/* 130 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 131 */       hrmScheduleDiffUtil.setUser(this.user);
/*     */       
/* 133 */       HrmMFScheduleDiffManager hrmMFScheduleDiffManager = new HrmMFScheduleDiffManager();
/* 134 */       HrmMFScheduleDiff hrmMFScheduleDiff = null;
/* 135 */       for (str18 = paramString1; !bool; ) {
/*     */         
/* 137 */         if (str18.equals(paramString2)) {
/* 138 */           bool = true;
/*     */         }
/*     */         
/* 141 */         bool2 = hrmScheduleDiffUtil.getIsWorkday(str18, paramInt1, "");
/*     */         
/* 143 */         if (!bool2) {
/*     */           
/* 145 */           str19 = TimeUtil.dateAdd(str18, 1);
/* 146 */           str18 = str19;
/*     */           
/*     */           continue;
/*     */         } 
/*     */         
/* 151 */         map = hrmScheduleDiffUtil.getOnDutyAndOffDutyTimeMap(str18, paramInt1);
/*     */         
/* 153 */         str11 = Util.null2String((String)map.get("onDutyTimeAM"));
/* 154 */         str12 = StringUtil.vString(map.get("offDutyTimeAM"));
/* 155 */         str13 = StringUtil.vString(map.get("onDutyTimePM"));
/* 156 */         str14 = Util.null2String((String)map.get("offDutyTimePM"));
/* 157 */         str15 = StringUtil.vString(map.get("signType"), "1");
/* 158 */         str16 = StringUtil.vString(map.get("signStartTime"));
/* 159 */         hrmMFScheduleDiff = new HrmMFScheduleDiff();
/* 160 */         hrmMFScheduleDiff.setClassName("HrmScheduleDiffDetNoSignManager");
/* 161 */         hrmMFScheduleDiff.setCurrentDate(str18);
/* 162 */         hrmMFScheduleDiff.setDepartmentId(paramInt2);
/* 163 */         hrmMFScheduleDiff.setOffDutyTimeAM(str12);
/* 164 */         hrmMFScheduleDiff.setOffDutyTimePM(str14);
/* 165 */         hrmMFScheduleDiff.setOnDutyTimeAM(str11);
/* 166 */         hrmMFScheduleDiff.setOnDutyTimePM(str13);
/* 167 */         hrmMFScheduleDiff.setResourceId(paramString3);
/* 168 */         hrmMFScheduleDiff.setSignStartTime(str16);
/* 169 */         hrmMFScheduleDiff.setSignType(str15);
/* 170 */         hrmMFScheduleDiff.setSortForResult(this.sortForResult);
/* 171 */         hrmMFScheduleDiff.setSubCompanyId(paramInt1);
/* 172 */         hrmMFScheduleDiff.setSqlType(str17);
/* 173 */         hrmMFScheduleDiffManager.setBean(hrmMFScheduleDiff);
/*     */         
/* 175 */         stringBuffer = new StringBuffer();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 211 */         if (hrmMFScheduleDiffManager.isSecSign()) {
/* 212 */           stringBuffer.append(hrmMFScheduleDiffManager.getSQL());
/*     */         } else {
/* 214 */           stringBuffer.append(" select * from( ");
/* 215 */           if ("oracle".equals(recordSet.getDBType())) {
/* 216 */             stringBuffer.append(" select b.subCompanyId1 as subCompanyId,b.departmentId,b.id as resourceId,b.lastName as resourceName,b.status,min(a.id) as signId ,a.signDate,min(a.signTime) as signTime ");
/*     */           } else {
/* 218 */             stringBuffer.append(" select top 1000000 b.subCompanyId1 as subCompanyId,b.departmentId,b.id as resourceId,b.lastName as resourceName,b.status,min(a.id) as signId ,a.signDate,min(a.signTime) as signTime ");
/*     */           } 
/* 220 */           stringBuffer
/* 221 */             .append("   from HrmScheduleSign a,HrmResource b ")
/* 222 */             .append("  where a.userId=b.id ")
/* 223 */             .append("    and a.signDate='").append(str18).append("'")
/* 224 */             .append("    and a.signType='1' ")
/* 225 */             .append("    and a.isInCom='1' ")
/* 226 */             .append("    and a.userType='1' ");
/*     */           
/* 228 */           if (paramInt1 > 0) {
/* 229 */             stringBuffer.append(" and  b.subCompanyId1=").append(paramInt1);
/*     */           }
/*     */           
/* 232 */           if (paramInt2 > 0) {
/* 233 */             stringBuffer.append(" and  b.departmentId=").append(paramInt2);
/*     */           }
/*     */           
/* 236 */           if (paramString3.length() > 0) {
/* 237 */             stringBuffer.append(" and  b.id in (").append(paramString3).append(")");
/*     */           }
/* 239 */           stringBuffer.append("    and b.status in(0,1,2,3) ");
/*     */           
/* 241 */           if ("oracle".equals(recordSet.getDBType())) {
/* 242 */             stringBuffer.append(" and b.loginid is not null ");
/*     */           } else {
/* 244 */             stringBuffer.append(" and b.loginid is not null and b.loginid<>'' ");
/*     */           } 
/*     */           
/* 247 */           stringBuffer.append("  group by b.subCompanyId1 ,b.departmentId,b.id ,b.lastName ,b.status ,a.signDate");
/* 248 */           if (this.sortForResult) {
/* 249 */             stringBuffer.append("  order by b.subCompanyId1 asc,b.departmentId asc,b.id asc ");
/*     */           }
/* 251 */           stringBuffer.append(" )TempTable where signTime<'").append(str14).append(":00' ")
/* 252 */             .append("    and not exists( ")
/* 253 */             .append("       select 1 ")
/* 254 */             .append("       from HrmScheduleSign ")
/* 255 */             .append("      where signDate='").append(str18).append("' ")
/* 256 */             .append("        and signType='2' ")
/* 257 */             .append("        and userType='1' ")
/* 258 */             .append("        and isInCom='1' ")
/*     */             
/* 260 */             .append("        and userId=TempTable.resourceId ")
/* 261 */             .append("   ) ");
/*     */         } 
/*     */ 
/*     */         
/* 265 */         recordSet.executeSql(stringBuffer.toString());
/* 266 */         while (recordSet.next()) {
/* 267 */           str1 = Util.null2String(recordSet.getString("subCompanyId"));
/* 268 */           str2 = Util.null2String(recordSet.getString("departmentId"));
/* 269 */           str3 = departmentComInfo.getDepartmentname(str2);
/* 270 */           str4 = Util.null2String(recordSet.getString("resourceId"));
/* 271 */           str5 = Util.null2String(recordSet.getString("resourceName"));
/* 272 */           str6 = Util.null2String(recordSet.getString("status"));
/* 273 */           str7 = hrmScheduleDiffUtil.getStatusNameOfHrmResource(str6);
/* 274 */           str8 = Util.null2String(recordSet.getString("signId"));
/* 275 */           str9 = Util.null2String(recordSet.getString("signDate"));
/* 276 */           str10 = Util.null2String(recordSet.getString("signTime"));
/*     */           
/* 278 */           String str = str14;
/* 279 */           if (hrmMFScheduleDiffManager.isSecSign() && str10.compareTo(str13) < 0) {
/* 280 */             str = str16;
/*     */           }
/* 282 */           bool1 = getHasApproved(Util.getIntValue(str4, -1), str18, str10, str);
/* 283 */           if (!bool1) {
/* 284 */             hashMap = new HashMap<>();
/* 285 */             hashMap.put("subCompanyId", str1);
/* 286 */             hashMap.put("departmentId", str2);
/* 287 */             hashMap.put("departmentName", str3);
/* 288 */             hashMap.put("resourceId", str4);
/* 289 */             hashMap.put("resourceName", str5);
/* 290 */             hashMap.put("statusName", str7);
/* 291 */             hashMap.put("currentDate", str18);
/* 292 */             hashMap.put("signId", str8);
/* 293 */             hashMap.put("signType", str15);
/* 294 */             hashMap.put("signStartTime", str16);
/* 295 */             hashMap.put("signTime", str10);
/* 296 */             hashMap.put("mfer", "noSign");
/* 297 */             arrayList.add(hashMap);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 302 */         str19 = TimeUtil.dateAdd(str18, 1);
/* 303 */         str18 = str19;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 308 */       if (this.sortForResult && arrayList.size() >= 2) {
/* 309 */         Collections.sort(arrayList, (Comparator<? super HashMap<Object, Object>>)new HrmScheduleDiffListComparator());
/*     */       }
/*     */       
/* 312 */       return arrayList;
/* 313 */     } catch (Exception exception) {
/* 314 */       return arrayList;
/*     */     } finally {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean getHasApproved(int paramInt, String paramString1, String paramString2, String paramString3) {
/* 331 */     RecordSet recordSet = new RecordSet();
/* 332 */     String str1 = "0";
/*     */     
/* 334 */     String str2 = "";
/* 335 */     int i = TimeUtil.dateWeekday(paramString1);
/* 336 */     switch (i) {
/*     */       case 0:
/* 338 */         str2 = "sunstarttime1";
/*     */         break;
/*     */       case 1:
/* 341 */         str2 = "monstarttime1";
/*     */         break;
/*     */       case 2:
/* 344 */         str2 = "tuestarttime1";
/*     */         break;
/*     */       case 3:
/* 347 */         str2 = "wedstarttime1";
/*     */         break;
/*     */       case 4:
/* 350 */         str2 = "thustarttime1";
/*     */         break;
/*     */       case 5:
/* 353 */         str2 = "fristarttime1";
/*     */         break;
/*     */       case 6:
/* 356 */         str2 = "satstarttime1";
/*     */         break;
/*     */     } 
/*     */     
/* 360 */     recordSet.executeSql("select subcompanyid1 from hrmresource where id=" + paramInt);
/* 361 */     if (recordSet.next()) str1 = recordSet.getString("subcompanyid1");
/*     */     
/* 363 */     String str3 = "select " + str2 + " from HrmSchedule  where validedatefrom <='" + paramString1 + "' and validedateto>='" + paramString1 + "' and relatedid=" + str1;
/* 364 */     recordSet.execute(str3);
/* 365 */     if (recordSet.next()) {
/* 366 */       String str = Util.null2String(recordSet.getString(1));
/* 367 */       if (!"".equals(str)) str = str + ":00"; 
/* 368 */       int j = str.compareTo(paramString2);
/* 369 */       if (j >= 0) paramString2 = str; 
/*     */     } else {
/* 371 */       str3 = "select " + str2 + " from HrmSchedule  where validedatefrom <='" + paramString1 + "' and validedateto>='" + paramString1 + "' and relatedid=0";
/* 372 */       recordSet.execute(str3);
/* 373 */       if (recordSet.next()) {
/* 374 */         String str = Util.null2String(recordSet.getString(1));
/* 375 */         if (!"".equals(str)) str = str + ":00"; 
/* 376 */         int j = str.compareTo(paramString2);
/* 377 */         if (j >= 0) paramString2 = str;
/*     */       
/*     */       } 
/*     */     } 
/*     */     
/* 382 */     String str4 = getLastMinOfSignTime(paramString1 + " " + paramString2);
/* 383 */     String str5 = "";
/* 384 */     String str6 = "";
/* 385 */     if (recordSet.getDBType().equals("oracle")) {
/* 386 */       str5 = " and concat(concat(t.fromDate,' '), t.fromTime) <= '" + str4 + "'";
/* 387 */       str6 = " and concat(concat(t.toDate,' '), t.toTime) >= '" + paramString1 + " " + paramString3 + "'";
/*     */     } else {
/* 389 */       str5 = " and t.fromDate+' '+t.fromTime <= '" + str4 + "'";
/* 390 */       str6 = " and t.toDate+' '+t.toTime >= '" + paramString1 + " " + paramString3 + "'";
/*     */     } 
/* 392 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 393 */     hashMap.put("fromDate", str5);
/* 394 */     hashMap.put("toDate", str6);
/* 395 */     hashMap.put("resourceId", " and t.resourceId = " + paramInt);
/* 396 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(0, hashMap));
/* 397 */     if (recordSet.next()) return true;
/*     */     
/* 399 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(1, hashMap));
/* 400 */     if (recordSet.next()) return true;
/*     */     
/* 402 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(2, hashMap));
/* 403 */     if (recordSet.next()) return true;
/*     */     
/* 405 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(4, hashMap));
/* 406 */     if (recordSet.next()) return true;
/*     */     
/* 408 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getLastMinOfSignTime(String paramString) {
/* 423 */     String str = "";
/*     */     
/* 425 */     if (paramString == null || paramString.trim().equals("") || paramString
/* 426 */       .length() < 19) {
/* 427 */       return str;
/*     */     }
/*     */     
/* 430 */     str = paramString.substring(0, 16);
/*     */ 
/*     */     
/* 433 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffDetNoSignManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */