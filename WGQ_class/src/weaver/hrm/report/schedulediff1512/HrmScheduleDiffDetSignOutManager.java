/*     */ package weaver.hrm.report.schedulediff1512;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmMFScheduleDiff;
/*     */ import weaver.hrm.attendance.manager.HrmMFScheduleDiffManager;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffListComparator;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffDetSignOutManager
/*     */   extends BaseBean
/*     */ {
/*  42 */   private User user = null;
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean sortForResult = true;
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  51 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSortForResult(boolean paramBoolean) {
/*  59 */     this.sortForResult = paramBoolean;
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  63 */     String str1 = paramMap.get("fromDate");
/*  64 */     String str2 = paramMap.get("toDate");
/*  65 */     int i = Util.getIntValue(paramMap.get("subCompanyId"), -1);
/*  66 */     int j = Util.getIntValue(paramMap.get("departmentId"), -1);
/*  67 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  68 */     return getScheduleList(str1, str2, i, j, str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) {
/*  83 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, String.valueOf(paramInt3));
/*     */   }
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  87 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  88 */     HashMap<Object, Object> hashMap = null;
/*     */ 
/*     */     
/*  91 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/*  92 */       .trim().equals("") || paramString1
/*  93 */       .compareTo(paramString2) > 0) {
/*  94 */       return arrayList;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/*  99 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 100 */       RecordSet recordSet = new RecordSet();
/* 101 */       StringBuffer stringBuffer = null;
/*     */       
/* 103 */       String str1 = "";
/* 104 */       String str2 = "";
/* 105 */       String str3 = "";
/* 106 */       String str4 = "";
/* 107 */       String str5 = "";
/* 108 */       String str6 = "";
/* 109 */       String str7 = "";
/* 110 */       String str8 = "";
/* 111 */       String str9 = "";
/* 112 */       String str10 = "";
/* 113 */       String str11 = "";
/*     */       
/* 115 */       Map map = null;
/* 116 */       String str12 = null;
/* 117 */       String str13 = "";
/* 118 */       String str14 = "";
/* 119 */       String str15 = null;
/* 120 */       String str16 = "1";
/* 121 */       String str17 = "";
/* 122 */       String str18 = recordSet.getDBType();
/*     */       
/* 124 */       String str19 = "";
/* 125 */       String str20 = "";
/* 126 */       boolean bool = false;
/* 127 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 128 */       hrmScheduleDiffUtil.setUser(this.user);
/*     */       
/* 130 */       HrmMFScheduleDiffManager hrmMFScheduleDiffManager = new HrmMFScheduleDiffManager();
/* 131 */       HrmMFScheduleDiff hrmMFScheduleDiff = null;
/*     */       
/* 133 */       for (str19 = paramString1; !bool; ) {
/*     */         
/* 135 */         if (str19.equals(paramString2)) {
/* 136 */           bool = true;
/*     */         }
/* 138 */         map = hrmScheduleDiffUtil.getOnDutyAndOffDutyTimeMap(str19, paramInt1);
/*     */         
/* 140 */         str12 = Util.null2String((String)map.get("onDutyTimeAM"));
/* 141 */         str13 = StringUtil.vString(map.get("offDutyTimeAM"));
/* 142 */         str14 = StringUtil.vString(map.get("onDutyTimePM"));
/* 143 */         str15 = Util.null2String((String)map.get("offDutyTimePM"));
/* 144 */         str16 = StringUtil.vString(map.get("signType"), "1");
/* 145 */         str17 = StringUtil.vString(map.get("signStartTime"));
/* 146 */         hrmMFScheduleDiff = new HrmMFScheduleDiff();
/* 147 */         hrmMFScheduleDiff.setClassName("HrmScheduleDiffDetSignOutManager");
/* 148 */         hrmMFScheduleDiff.setCurrentDate(str19);
/* 149 */         hrmMFScheduleDiff.setDepartmentId(paramInt2);
/* 150 */         hrmMFScheduleDiff.setOffDutyTimeAM(str13);
/* 151 */         hrmMFScheduleDiff.setOffDutyTimePM(str15);
/* 152 */         hrmMFScheduleDiff.setOnDutyTimeAM(str12);
/* 153 */         hrmMFScheduleDiff.setOnDutyTimePM(str14);
/* 154 */         hrmMFScheduleDiff.setResourceId(paramString3);
/* 155 */         hrmMFScheduleDiff.setSignStartTime(str17);
/* 156 */         hrmMFScheduleDiff.setSignType(str16);
/* 157 */         hrmMFScheduleDiff.setSortForResult(this.sortForResult);
/* 158 */         hrmMFScheduleDiff.setSubCompanyId(paramInt1);
/* 159 */         hrmMFScheduleDiff.setSqlType(str18);
/* 160 */         hrmMFScheduleDiffManager.setBean(hrmMFScheduleDiff);
/*     */         
/* 162 */         stringBuffer = new StringBuffer();
/* 163 */         if (hrmMFScheduleDiffManager.isSecSign()) {
/* 164 */           stringBuffer.append(hrmMFScheduleDiffManager.getSQL());
/*     */         } else {
/* 166 */           stringBuffer.append("  select a.*,b.clientAddress,b.signFrom,b.LONGITUDE,b.LATITUDE,b.ADDR from (select b.subCompanyId1 as subCompanyId,b.departmentId,b.id as resourceId,b.lastName as resourceName,b.status,max(a.id) as signId ,a.signDate,max(a.signTime) as signTime ")
/* 167 */             .append("   from HrmScheduleSign a,HrmResource b ")
/* 168 */             .append("  where a.userId=b.id ")
/* 169 */             .append("    and a.signDate='").append(str19).append("'")
/* 170 */             .append("    and a.signType='2' ")
/* 171 */             .append("    and a.isInCom='1' ")
/* 172 */             .append("    and a.userType='1' ");
/*     */           
/* 174 */           if (paramInt1 > 0) {
/* 175 */             stringBuffer.append(" and  b.subCompanyId1=").append(paramInt1);
/*     */           }
/*     */           
/* 178 */           if (paramInt2 > 0) {
/* 179 */             stringBuffer.append(" and  b.departmentId=").append(paramInt2);
/*     */           }
/*     */           
/* 182 */           if (paramString3.length() > 0) {
/* 183 */             stringBuffer.append(" and  b.id in (").append(paramString3).append(")");
/*     */           }
/* 185 */           stringBuffer.append("    and b.status in(0,1,2,3) ");
/*     */           
/* 187 */           if ("oracle".equals(recordSet.getDBType())) {
/* 188 */             stringBuffer.append(" and b.loginid is not null ");
/*     */           } else {
/* 190 */             stringBuffer.append(" and b.loginid is not null and b.loginid<>'' ");
/*     */           } 
/*     */           
/* 193 */           stringBuffer.append("  group by b.subCompanyId1 ,b.departmentId,b.id ,b.lastName ,b.status ,a.signDate");
/* 194 */           stringBuffer.append(") a left join HrmScheduleSign b on a.resourceId = b.userId and a.signDate = b.signDate and a.signTime = b.signTime ");
/* 195 */           if (this.sortForResult) {
/* 196 */             stringBuffer.append("  order by a.subCompanyId asc,a.departmentId asc,a.resourceId asc,a.signDate  asc ");
/*     */           }
/*     */         } 
/*     */ 
/*     */         
/* 201 */         recordSet.executeSql(stringBuffer.toString());
/* 202 */         while (recordSet.next()) {
/* 203 */           str1 = Util.null2String(recordSet.getString("subCompanyId"));
/* 204 */           str2 = Util.null2String(recordSet.getString("departmentId"));
/* 205 */           str3 = departmentComInfo.getDepartmentname(str2);
/* 206 */           str4 = Util.null2String(recordSet.getString("resourceId"));
/* 207 */           str5 = Util.null2String(recordSet.getString("resourceName"));
/* 208 */           str6 = Util.null2String(recordSet.getString("status"));
/* 209 */           str7 = hrmScheduleDiffUtil.getStatusNameOfHrmResource(str6);
/* 210 */           str8 = Util.null2String(recordSet.getString("signId"));
/* 211 */           str9 = Util.null2String(recordSet.getString("signDate"));
/* 212 */           str10 = Util.null2String(recordSet.getString("signTime"));
/* 213 */           str11 = Util.null2String(recordSet.getString("clientAddress"));
/* 214 */           String str21 = "";
/* 215 */           String str22 = "";
/* 216 */           if (StringUtil.isNull(recordSet.getString("signFrom")) || StringUtil.vString(recordSet.getString("signFrom")).equalsIgnoreCase("pc")) {
/* 217 */             str22 = "";
/*     */           }
/* 219 */           else if (StringUtil.isNull(recordSet.getString("LONGITUDE")) || StringUtil.isNull(recordSet.getString("LATITUDE"))) {
/* 220 */             str22 = "";
/*     */           } else {
/* 222 */             str22 = StringUtil.vString(recordSet.getString("ADDR"), "" + SystemEnv.getHtmlLabelName(10000168, ThreadVarLanguage.getLang()) + "");
/*     */           } 
/*     */           
/* 225 */           if (StringUtil.isNotNull(str22)) {
/* 226 */             str21 = "<a href=\"javascript:void(0);\" onclick=\"openMap('" + StringUtil.vString(recordSet.getString("LONGITUDE")) + "','" + StringUtil.vString(recordSet.getString("LATITUDE")) + "','" + "1de5f5f7f410712e1280aaf08d2065f1" + "','" + (str22.equals("查看信息") ? "null" : str22) + "');\">" + str22 + "</a>";
/*     */           }
/* 228 */           hashMap = new HashMap<>();
/* 229 */           hashMap.put("subCompanyId", str1);
/* 230 */           hashMap.put("departmentId", str2);
/* 231 */           hashMap.put("departmentName", str3);
/* 232 */           hashMap.put("resourceId", str4);
/* 233 */           hashMap.put("resourceName", str5);
/* 234 */           hashMap.put("statusName", str7);
/* 235 */           hashMap.put("currentDate", str19);
/* 236 */           hashMap.put("signId", str8);
/* 237 */           hashMap.put("signDate", str9);
/* 238 */           hashMap.put("signTime", str10);
/* 239 */           hashMap.put("clientAddress", str11);
/* 240 */           hashMap.put("signType", str16);
/* 241 */           hashMap.put("signStartTime", str17);
/* 242 */           hashMap.put("addr", str21);
/* 243 */           hashMap.put("_addr", StringUtil.vString(recordSet.getString("ADDR")));
/* 244 */           hashMap.put("longitude", StringUtil.vString(recordSet.getString("LONGITUDE")));
/* 245 */           hashMap.put("latitude", StringUtil.vString(recordSet.getString("LATITUDE")));
/* 246 */           hashMap.put("mfer", "signOut");
/* 247 */           hashMap.put("addrDetail", str22.equals("查看信息") ? (StringUtil.vString(recordSet.getString("LONGITUDE")) + "," + StringUtil.vString(recordSet.getString("LATITUDE"))) : str22);
/* 248 */           arrayList.add(hashMap);
/*     */         } 
/*     */ 
/*     */         
/* 252 */         str20 = TimeUtil.dateAdd(str19, 1);
/* 253 */         str19 = str20;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 258 */       if (this.sortForResult && arrayList.size() >= 2) {
/* 259 */         Collections.sort(arrayList, (Comparator<? super HashMap<Object, Object>>)new HrmScheduleDiffListComparator());
/*     */       }
/*     */       
/* 262 */       return arrayList;
/* 263 */     } catch (Exception exception) {
/* 264 */       return arrayList;
/*     */     } finally {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffDetSignOutManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */