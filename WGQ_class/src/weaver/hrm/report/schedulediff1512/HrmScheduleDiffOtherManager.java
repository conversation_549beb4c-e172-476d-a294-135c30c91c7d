/*     */ package weaver.hrm.report.schedulediff1512;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*     */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*     */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffOtherManager
/*     */   extends BaseBean
/*     */ {
/*  25 */   private RecordSet rs = null;
/*  26 */   private int languageId = 7;
/*  27 */   private HrmScheduleDiffUtil diffUtil = null;
/*  28 */   private HrmLeaveTypeColorManager typeManager = null;
/*  29 */   private List<HrmLeaveTypeColor> leaveTypeList = null;
/*     */   
/*     */   public HrmScheduleDiffOtherManager() {
/*  32 */     this.rs = new RecordSet();
/*  33 */     this.diffUtil = new HrmScheduleDiffUtil();
/*  34 */     this.typeManager = new HrmLeaveTypeColorManager();
/*     */   }
/*     */   
/*     */   private void initLeaveTypeList(int paramInt) {
/*  38 */     if (paramInt == 0) this.leaveTypeList = this.typeManager.find("[map]subcompanyid:0;"); 
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  42 */     String str1 = paramMap.get("fromDate");
/*  43 */     String str2 = paramMap.get("toDate");
/*  44 */     int i = StringUtil.parseToInt(paramMap.get("subCompanyId"));
/*  45 */     int j = StringUtil.parseToInt(paramMap.get("departmentId"));
/*  46 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  47 */     int k = StringUtil.parseToInt(paramMap.get("type"), 4);
/*  48 */     boolean bool1 = Boolean.valueOf(StringUtil.vString(paramMap.get("arg1"))).booleanValue();
/*  49 */     boolean bool2 = Boolean.valueOf(StringUtil.vString(paramMap.get("arg2"))).booleanValue();
/*  50 */     return getScheduleList(paramUser, str1, str2, i, j, str3, k, bool1, bool2);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3) {
/*  54 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, false, false);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, int paramInt4) {
/*  58 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, false, false, paramInt4);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, boolean paramBoolean1, boolean paramBoolean2) {
/*  62 */     return getScheduleList(paramUser, paramString1, paramString2, paramInt1, paramInt2, paramString3, paramInt3, paramBoolean1, paramBoolean2, 0);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, boolean paramBoolean1, boolean paramBoolean2, int paramInt4) {
/*  66 */     this.diffUtil.setUser(paramUser);
/*  67 */     initLeaveTypeList(paramInt3);
/*  68 */     if (paramUser != null) this.languageId = paramUser.getLanguage(); 
/*  69 */     boolean bool = (paramInt4 > 0 && paramInt3 == 0) ? true : false;
/*  70 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  71 */     String str1 = "fromDate", str2 = "fromTime", str3 = "toDate", str4 = "toTime";
/*  72 */     switch (paramInt3) {
/*     */       case 0:
/*  74 */         if (paramInt4 > 0) hashMap1.put("newLeaveType", " and t.newLeaveType between 1 and " + paramInt4); 
/*     */         break;
/*     */       case 3:
/*  77 */         str1 = "fromdate";
/*  78 */         str2 = "fromtime";
/*  79 */         str3 = "tilldate";
/*  80 */         str4 = "tilltime";
/*  81 */         hashMap1.put("oType", " and t.otype = '0'");
/*     */         break;
/*     */     } 
/*  84 */     String str5 = " and (t." + str1 + " between '" + paramString1 + "' and '" + paramString2 + "' or t." + str3 + " between '" + paramString1 + "' and '" + paramString2 + "')";
/*  85 */     if (StringUtil.isNotNull(paramString1) && StringUtil.isNotNull(paramString2)) { hashMap1.put("fromDate", str5); }
/*  86 */     else if (StringUtil.isNotNull(paramString1)) { hashMap1.put("fromDate", " and t." + str1 + " >= '" + paramString1 + "'"); }
/*  87 */     else if (StringUtil.isNotNull(paramString2)) { hashMap1.put("toDate", " and t." + str3 + " <= '" + paramString2 + "'"); }
/*  88 */      if (paramInt2 > 0) hashMap1.put("departmentId", " and t.departmentId = " + paramInt2); 
/*  89 */     if (paramInt1 > 0) hashMap1.put("subCompanyId", " and t.departmentId in (select id from hrmdepartment where subcompanyid1 = " + paramInt1 + ")"); 
/*  90 */     if (StringUtil.isNotNull(paramString3)) hashMap1.put("resourceId", " and t.resourceId in (" + paramString3 + ")"); 
/*  91 */     this.rs.executeSql((new HrmAttProcSetManager()).getSQLByField006(paramInt3, hashMap1, paramBoolean1, paramBoolean2));
/*  92 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  93 */     HashMap<Object, Object> hashMap2 = null;
/*  94 */     String str6 = "", str7 = "", str8 = "", str9 = "", str10 = "", str11 = "", str12 = "";
/*  95 */     if (bool) {
/*  96 */       String str = "";
/*  97 */       hashMap2 = new HashMap<>();
/*  98 */       while (this.rs.next()) {
/*  99 */         str = StringUtil.vString(this.rs.getString("newLeaveType"));
/* 100 */         if (!hashMap2.containsKey(str)) hashMap2.put(str, str); 
/*     */       } 
/* 102 */       arrayList.add(hashMap2);
/*     */     } else {
/* 104 */       while (this.rs.next()) {
/* 105 */         hashMap2 = new HashMap<>();
/* 106 */         str6 = StringUtil.vString(this.rs.getString("requestid"));
/* 107 */         hashMap2.put("requestId", str6);
/* 108 */         hashMap2.put("resourceId", StringUtil.vString(this.rs.getString("resourceId")));
/* 109 */         hashMap2.put("resourceName", StringUtil.vString(this.rs.getString("lastname")));
/* 110 */         hashMap2.put("departmentName", StringUtil.vString(this.rs.getString("departmentname")));
/* 111 */         str9 = StringUtil.vString(this.rs.getString(str1));
/* 112 */         str10 = StringUtil.vString(this.rs.getString(str2));
/* 113 */         hashMap2.put("fromDate", str9);
/* 114 */         hashMap2.put("fromTime", str10);
/* 115 */         str11 = StringUtil.vString(this.rs.getString(str3));
/* 116 */         str12 = StringUtil.vString(this.rs.getString(str4));
/* 117 */         hashMap2.put("toDate", str11);
/* 118 */         hashMap2.put("toTime", str12);
/* 119 */         hashMap2.put("startTime", str9 + "/" + str10);
/* 120 */         hashMap2.put("endTime", str11 + "/" + str12);
/* 121 */         hashMap2.put("status", StringUtil.vString(this.rs.getString("status")));
/* 122 */         if (paramInt3 == 0) {
/* 123 */           hashMap2.put("leaveDays", this.diffUtil.getTotalWorkingDays(str9, str10, str11, str12, paramInt1));
/* 124 */           hashMap2.put("newLeaveType", this.rs.getString("newLeaveType"));
/* 125 */           initLeave((Map)hashMap2, this.rs.getInt("newLeaveType"));
/* 126 */         } else if (paramInt3 == 1 || paramInt3 == 2) {
/* 127 */           hashMap2.put("days", this.diffUtil.getTotalWorkingDays(str9, str10, str11, str12, paramInt1));
/* 128 */         } else if (paramInt3 == 3 || paramInt3 == 4) {
/* 129 */           str8 = StringUtil.vString(this.rs.getString("requestname"));
/* 130 */           str7 = "<a href=javaScript:openFullWindowHaveBarForWFList('/workflow/request/ViewRequest.jsp?requestid=" + str6 + "&isovertime=0'," + str6 + ");doReadIt(" + str6 + ",\"\",this);>" + str8 + "</a>";
/* 131 */           hashMap2.put("requestName", str7);
/* 132 */           hashMap2.put("outName", str8);
/*     */         } 
/* 134 */         arrayList.add(hashMap2);
/*     */       } 
/*     */     } 
/* 137 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private void initLeave(Map<String, String> paramMap, int paramInt) {
/* 141 */     String str = "";
/* 142 */     if (this.leaveTypeList != null)
/* 143 */       for (HrmLeaveTypeColor hrmLeaveTypeColor : this.leaveTypeList) {
/* 144 */         if (hrmLeaveTypeColor.getField004().intValue() == paramInt) {
/* 145 */           switch (this.languageId) {
/*     */             case 7:
/* 147 */               str = hrmLeaveTypeColor.getField007();
/*     */               break;
/*     */             case 8:
/* 150 */               str = hrmLeaveTypeColor.getField008();
/*     */               break;
/*     */             case 9:
/* 153 */               str = hrmLeaveTypeColor.getField009();
/*     */               break;
/*     */           } 
/* 156 */           str = StringUtil.vString(str, hrmLeaveTypeColor.getField001());
/* 157 */           paramMap.put("leaveType", str);
/* 158 */           paramMap.put("leaveColor", hrmLeaveTypeColor.getColor());
/*     */           return;
/*     */         } 
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffOtherManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */