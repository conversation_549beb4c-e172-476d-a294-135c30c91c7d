/*      */ package weaver.hrm.report.schedulediff1512;
/*      */ 
/*      */ import java.math.BigDecimal;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.HashMap;
/*      */ import java.util.Map;
/*      */ import weaver.common.DateUtil;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.common.Tools;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class HrmScheduleDiffUtil
/*      */   extends BaseBean
/*      */ {
/*   38 */   private BigDecimal workHoursPerDayDefault = new BigDecimal(7.167D);
/*      */   
/*   40 */   private String onDutyTimeAMDefault = "08:30";
/*      */   
/*   42 */   private String offDutyTimeAMDefault = "11:40";
/*      */   
/*   44 */   private String onDutyTimePMDefault = "13:30";
/*      */   
/*   46 */   private String offDutyTimePMDefault = "17:30";
/*      */   private User user;
/*      */   
/*      */   public HrmScheduleDiffUtil() {
/*   50 */     this.user = null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setUser(User paramUser) {
/*   58 */     this.user = paramUser;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTotalWorkingDays(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/*   79 */     String str = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   90 */     if (paramString1 == null || paramString1.trim().equals("") || paramString3 == null || paramString3
/*   91 */       .trim().equals("") || paramString1
/*   92 */       .compareTo(paramString3) > 0 || (paramString1.equals(paramString3) && paramString2.compareTo(paramString4) > 0)) {
/*   93 */       return str;
/*      */     }
/*      */ 
/*      */     
/*   97 */     if (paramString2 == null || paramString2.trim().equals("")) {
/*   98 */       Map map = getOnDutyAndOffDutyTimeMap(paramString1, paramInt);
/*   99 */       String str1 = StringUtil.vString((String)map.get("onDutyTimeAM"));
/*  100 */       paramString2 = str1;
/*      */     } 
/*      */     
/*  103 */     if (paramString4 == null || paramString4.trim().equals("")) {
/*  104 */       Map map = getOnDutyAndOffDutyTimeMap(paramString3, paramInt);
/*  105 */       String str1 = StringUtil.vString((String)map.get("offDutyTimePM"));
/*  106 */       paramString4 = str1;
/*      */     } 
/*      */ 
/*      */     
/*  110 */     if (paramString2 == null || paramString2.trim().equals("") || paramString4 == null || paramString4
/*  111 */       .trim().equals("")) {
/*  112 */       return str;
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  117 */     BigDecimal bigDecimal1 = new BigDecimal(0.0D);
/*  118 */     BigDecimal bigDecimal2 = new BigDecimal(0.0D);
/*      */ 
/*      */     
/*      */     try {
/*  122 */       String str1 = "";
/*  123 */       String str2 = "";
/*  124 */       boolean bool = false;
/*  125 */       boolean bool1 = true;
/*      */       
/*  127 */       Map map = null;
/*  128 */       String str3 = "";
/*  129 */       String str4 = "";
/*  130 */       String str5 = "";
/*  131 */       String str6 = "";
/*      */       
/*  133 */       long l = 0L;
/*      */       
/*  135 */       for (str1 = paramString1; !bool; ) {
/*      */         
/*  137 */         bigDecimal1 = new BigDecimal(0.0D);
/*      */         
/*  139 */         if (str1.equals(paramString3)) {
/*  140 */           bool = true;
/*      */         }
/*      */         
/*  143 */         bool1 = getIsWorkday(str1, paramInt, "");
/*      */         
/*  145 */         if (!bool1) {
/*      */           
/*  147 */           str2 = TimeUtil.dateAdd(str1, 1);
/*  148 */           str1 = str2;
/*      */           
/*      */           continue;
/*      */         } 
/*      */         
/*  153 */         map = getOnDutyAndOffDutyTimeMap(str1, paramInt);
/*      */         
/*  155 */         str3 = StringUtil.vString((String)map.get("onDutyTimeAM"));
/*  156 */         str4 = StringUtil.vString((String)map.get("offDutyTimeAM"));
/*  157 */         str5 = StringUtil.vString((String)map.get("onDutyTimePM"));
/*  158 */         str6 = StringUtil.vString((String)map.get("offDutyTimePM"));
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  163 */         if (str1.equals(paramString1) && str1.equals(paramString3)) {
/*      */           
/*  165 */           if (paramString2.compareTo(str3) < 0) {
/*      */             
/*  167 */             if (paramString4.compareTo(str3) >= 0)
/*      */             {
/*      */               
/*  170 */               if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  171 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + paramString4 + ":00");
/*  172 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               
/*      */               }
/*  175 */               else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  176 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00");
/*  177 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               
/*      */               }
/*  180 */               else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*      */                 
/*  182 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  183 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               
/*      */               }
/*      */               else {
/*      */                 
/*  188 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  189 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               }
/*      */             
/*      */             }
/*  193 */           } else if (paramString2.compareTo(str3) >= 0 && paramString2.compareTo(str4) <= 0) {
/*      */ 
/*      */             
/*  196 */             if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  197 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  198 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  201 */             else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  202 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00");
/*  203 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  206 */             else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*      */               
/*  208 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  209 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*      */             else {
/*      */               
/*  214 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  215 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*      */           
/*      */           }
/*  219 */           else if (paramString2.compareTo(str4) > 0 && paramString2.compareTo(str5) < 0) {
/*      */ 
/*      */             
/*  222 */             if (paramString4.compareTo(str4) <= 0 || paramString4.compareTo(str5) >= 0)
/*      */             {
/*      */               
/*  225 */               if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  226 */                 l = TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  227 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               }
/*      */               else {
/*      */                 
/*  231 */                 l = TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  232 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               } 
/*      */             }
/*  235 */           } else if (paramString2.compareTo(str5) >= 0 && paramString2.compareTo(str6) <= 0) {
/*      */ 
/*      */             
/*  238 */             if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  239 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  240 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*      */             else {
/*      */               
/*  244 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str6 + ":00");
/*  245 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*      */ 
/*      */           
/*      */           }
/*      */ 
/*      */         
/*      */         }
/*  254 */         else if (str1.equals(paramString1) && !str1.equals(paramString3)) {
/*      */ 
/*      */           
/*  257 */           if (paramString2.compareTo(str3) < 0)
/*      */           {
/*  259 */             l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  260 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           
/*      */           }
/*  263 */           else if (paramString2.compareTo(str3) >= 0 && paramString2.compareTo(str4) <= 0)
/*      */           {
/*      */             
/*  266 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  267 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */ 
/*      */           
/*      */           }
/*  271 */           else if (paramString2.compareTo(str4) > 0 && paramString2.compareTo(str5) < 0)
/*      */           {
/*  273 */             l = TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  274 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           
/*      */           }
/*  277 */           else if (paramString2.compareTo(str5) >= 0 && paramString2.compareTo(str6) <= 0)
/*      */           {
/*  279 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str6 + ":00");
/*  280 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */ 
/*      */           
/*      */           }
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*  288 */         else if (!str1.equals(paramString1) && str1.equals(paramString3)) {
/*      */ 
/*      */           
/*  291 */           if (paramString4.compareTo(str3) >= 0)
/*      */           {
/*      */             
/*  294 */             if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  295 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + paramString4 + ":00");
/*  296 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  299 */             else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  300 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00");
/*  301 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  304 */             else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*      */               
/*  306 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  307 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*      */             else {
/*      */               
/*  312 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  313 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             } 
/*      */           }
/*  316 */         } else if (!str1.equals(paramString1) && !str1.equals(paramString3)) {
/*      */ 
/*      */           
/*  319 */           l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  320 */           bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */         } 
/*      */         
/*  323 */         BigDecimal bigDecimal = getWorkHoursPerDay(map);
/*  324 */         bigDecimal2 = bigDecimal2.add(bigDecimal1.divide(bigDecimal, 2, 4));
/*      */ 
/*      */         
/*  327 */         str2 = TimeUtil.dateAdd(str1, 1);
/*  328 */         str1 = str2;
/*      */       } 
/*      */       
/*  331 */       str = bigDecimal2.toString();
/*  332 */       return str;
/*  333 */     } catch (Exception exception) {
/*  334 */       return str;
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTotalWorkingHours(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/*  341 */     if (paramString1 == null || paramString1.trim().equals("") || paramString3 == null || paramString3
/*  342 */       .trim().equals("") || paramString1
/*  343 */       .compareTo(paramString3) > 0 || (paramString1.equals(paramString3) && paramString2.compareTo(paramString4) > 0)) {
/*  344 */       return "";
/*      */     }
/*      */ 
/*      */     
/*  348 */     if (paramString2 == null || paramString2.trim().equals("")) {
/*  349 */       Map map = getOnDutyAndOffDutyTimeMap(paramString1, paramInt);
/*  350 */       String str = StringUtil.vString((String)map.get("onDutyTimeAM"));
/*  351 */       paramString2 = str;
/*      */     } 
/*      */     
/*  354 */     if (paramString4 == null || paramString4.trim().equals("")) {
/*  355 */       Map map = getOnDutyAndOffDutyTimeMap(paramString3, paramInt);
/*  356 */       String str = StringUtil.vString((String)map.get("offDutyTimePM"));
/*  357 */       paramString4 = str;
/*      */     } 
/*      */ 
/*      */     
/*  361 */     if (paramString2 == null || paramString2.trim().equals("") || paramString4 == null || paramString4
/*  362 */       .trim().equals("")) {
/*  363 */       return "";
/*      */     }
/*      */     
/*  366 */     BigDecimal bigDecimal1 = new BigDecimal(0.0D);
/*  367 */     BigDecimal bigDecimal2 = new BigDecimal(0.0D);
/*      */     try {
/*  369 */       String str1 = "";
/*  370 */       String str2 = "";
/*  371 */       boolean bool = false;
/*  372 */       boolean bool1 = true;
/*      */       
/*  374 */       Map map = null;
/*  375 */       String str3 = "";
/*  376 */       String str4 = "";
/*  377 */       String str5 = "";
/*  378 */       String str6 = "";
/*      */       
/*  380 */       long l = 0L;
/*      */       
/*  382 */       for (str1 = paramString1; !bool; ) {
/*      */         
/*  384 */         bigDecimal1 = new BigDecimal(0.0D);
/*      */         
/*  386 */         if (str1.equals(paramString3)) {
/*  387 */           bool = true;
/*      */         }
/*      */         
/*  390 */         bool1 = getIsWorkday(str1, paramInt, "");
/*      */         
/*  392 */         if (!bool1) {
/*      */           
/*  394 */           str2 = TimeUtil.dateAdd(str1, 1);
/*  395 */           str1 = str2;
/*      */           
/*      */           continue;
/*      */         } 
/*      */         
/*  400 */         map = getOnDutyAndOffDutyTimeMap(str1, paramInt);
/*      */         
/*  402 */         str3 = StringUtil.vString((String)map.get("onDutyTimeAM"));
/*  403 */         str4 = StringUtil.vString((String)map.get("offDutyTimeAM"));
/*  404 */         str5 = StringUtil.vString((String)map.get("onDutyTimePM"));
/*  405 */         str6 = StringUtil.vString((String)map.get("offDutyTimePM"));
/*      */ 
/*      */         
/*  408 */         if (str1.equals(paramString1) && str1.equals(paramString3)) {
/*      */           
/*  410 */           if (paramString2.compareTo(str3) < 0) {
/*      */             
/*  412 */             if (paramString4.compareTo(str3) >= 0)
/*      */             {
/*      */               
/*  415 */               if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  416 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + paramString4 + ":00");
/*  417 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               
/*      */               }
/*  420 */               else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  421 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00");
/*  422 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               
/*      */               }
/*  425 */               else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*      */                 
/*  427 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  428 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               
/*      */               }
/*      */               else {
/*      */                 
/*  433 */                 l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  434 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               }
/*      */             
/*      */             }
/*  438 */           } else if (paramString2.compareTo(str3) >= 0 && paramString2.compareTo(str4) <= 0) {
/*      */ 
/*      */             
/*  441 */             if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  442 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  443 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  446 */             else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  447 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00");
/*  448 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  451 */             else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*      */               
/*  453 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  454 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*      */             else {
/*      */               
/*  459 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  460 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*      */           
/*      */           }
/*  464 */           else if (paramString2.compareTo(str4) > 0 && paramString2.compareTo(str5) < 0) {
/*      */ 
/*      */             
/*  467 */             if (paramString4.compareTo(str4) <= 0 || paramString4.compareTo(str5) >= 0)
/*      */             {
/*      */               
/*  470 */               if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  471 */                 l = TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  472 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               }
/*      */               else {
/*      */                 
/*  476 */                 l = TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  477 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               } 
/*      */             }
/*  480 */           } else if (paramString2.compareTo(str5) >= 0 && paramString2.compareTo(str6) <= 0) {
/*      */ 
/*      */             
/*  483 */             if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  484 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  485 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*      */             else {
/*      */               
/*  489 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str6 + ":00");
/*  490 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*      */ 
/*      */           
/*      */           }
/*      */ 
/*      */         
/*      */         }
/*  499 */         else if (str1.equals(paramString1) && !str1.equals(paramString3)) {
/*      */ 
/*      */           
/*  502 */           if (paramString2.compareTo(str3) < 0)
/*      */           {
/*  504 */             l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  505 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           
/*      */           }
/*  508 */           else if (paramString2.compareTo(str3) >= 0 && paramString2.compareTo(str4) <= 0)
/*      */           {
/*      */             
/*  511 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  512 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */ 
/*      */           
/*      */           }
/*  516 */           else if (paramString2.compareTo(str4) > 0 && paramString2.compareTo(str5) < 0)
/*      */           {
/*  518 */             l = TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  519 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           
/*      */           }
/*  522 */           else if (paramString2.compareTo(str5) >= 0 && paramString2.compareTo(str6) <= 0)
/*      */           {
/*  524 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str6 + ":00");
/*  525 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */ 
/*      */           
/*      */           }
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*  533 */         else if (!str1.equals(paramString1) && str1.equals(paramString3)) {
/*      */ 
/*      */           
/*  536 */           if (paramString4.compareTo(str3) >= 0)
/*      */           {
/*      */             
/*  539 */             if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  540 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + paramString4 + ":00");
/*  541 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  544 */             else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  545 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00");
/*  546 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*  549 */             else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*      */               
/*  551 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + paramString4 + ":00");
/*  552 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             
/*      */             }
/*      */             else {
/*      */               
/*  557 */               l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  558 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             } 
/*      */           }
/*  561 */         } else if (!str1.equals(paramString1) && !str1.equals(paramString3)) {
/*      */ 
/*      */           
/*  564 */           l = TimeUtil.timeInterval(str1 + " " + str3 + ":00", str1 + " " + str4 + ":00") + TimeUtil.timeInterval(str1 + " " + str5 + ":00", str1 + " " + str6 + ":00");
/*  565 */           bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */         } 
/*  567 */         bigDecimal2 = bigDecimal2.add(bigDecimal1);
/*      */         
/*  569 */         str2 = TimeUtil.dateAdd(str1, 1);
/*  570 */         str1 = str2;
/*      */       } 
/*      */       
/*  573 */       return bigDecimal2.toString();
/*  574 */     } catch (Exception exception) {
/*  575 */       return "";
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTotalNotWorkHours(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/*  582 */     if (paramString1 == null || paramString1.trim().equals("") || paramString3 == null || paramString3
/*  583 */       .trim().equals("") || paramString1
/*  584 */       .compareTo(paramString3) > 0 || (paramString1.equals(paramString3) && paramString2.compareTo(paramString4) > 0)) {
/*  585 */       return "";
/*      */     }
/*      */ 
/*      */     
/*  589 */     if (paramString2 == null || paramString2.trim().equals("")) {
/*  590 */       Map map = getOnDutyAndOffDutyTimeMap(paramString1, paramInt);
/*  591 */       String str = StringUtil.vString((String)map.get("onDutyTimeAM"));
/*  592 */       paramString2 = str;
/*      */     } 
/*      */     
/*  595 */     if (paramString4 == null || paramString4.trim().equals("")) {
/*  596 */       Map map = getOnDutyAndOffDutyTimeMap(paramString3, paramInt);
/*  597 */       String str = StringUtil.vString((String)map.get("offDutyTimePM"));
/*  598 */       paramString4 = str;
/*      */     } 
/*      */ 
/*      */     
/*  602 */     if (paramString2 == null || paramString2.trim().equals("") || paramString4 == null || paramString4
/*  603 */       .trim().equals("")) {
/*  604 */       return "";
/*      */     }
/*  606 */     BigDecimal bigDecimal1 = new BigDecimal(0.0D);
/*  607 */     BigDecimal bigDecimal2 = new BigDecimal(0.0D);
/*      */     try {
/*  609 */       String str1 = "";
/*  610 */       String str2 = "";
/*  611 */       boolean bool = false;
/*  612 */       boolean bool1 = true;
/*      */       
/*  614 */       Map map = null;
/*  615 */       String str3 = "";
/*  616 */       String str4 = "";
/*  617 */       String str5 = "";
/*  618 */       String str6 = "";
/*      */       
/*  620 */       for (str1 = paramString1; !bool; ) {
/*      */         
/*  622 */         bigDecimal1 = new BigDecimal(0.0D);
/*      */         
/*  624 */         if (str1.equals(paramString3)) {
/*  625 */           bool = true;
/*      */         }
/*      */         
/*  628 */         long l = 0L;
/*      */         
/*  630 */         bool1 = getIsWorkday(str1, paramInt, "");
/*  631 */         if (!bool1) {
/*  632 */           if (str1.equals(paramString1) && str1.equals(paramString3)) {
/*  633 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  634 */           } else if (str1.equals(paramString1) && !str1.equals(paramString3)) {
/*  635 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " 23:59:59") + 1L;
/*  636 */           } else if (!str1.equals(paramString1) && str1.equals(paramString3)) {
/*  637 */             l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " " + paramString4 + ":00") + 1L;
/*  638 */           } else if (!str1.equals(paramString1) && !str1.equals(paramString3)) {
/*  639 */             l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " 23:59:59") + 1L;
/*      */           } 
/*  641 */           bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           
/*  643 */           bigDecimal2 = bigDecimal2.add(bigDecimal1);
/*      */           
/*  645 */           str2 = TimeUtil.dateAdd(str1, 1);
/*  646 */           str1 = str2;
/*      */           
/*      */           continue;
/*      */         } 
/*  650 */         map = getOnDutyAndOffDutyTimeMap(str1, paramInt);
/*      */         
/*  652 */         str3 = StringUtil.vString((String)map.get("onDutyTimeAM"));
/*  653 */         str4 = StringUtil.vString((String)map.get("offDutyTimeAM"));
/*  654 */         str5 = StringUtil.vString((String)map.get("onDutyTimePM"));
/*  655 */         str6 = StringUtil.vString((String)map.get("offDutyTimePM"));
/*      */ 
/*      */         
/*  658 */         if (str1.equals(paramString1) && str1.equals(paramString3)) {
/*      */           
/*  660 */           if (paramString2.compareTo(str3) < 0) {
/*      */             
/*  662 */             if (paramString4.compareTo(str3) < 0) {
/*  663 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  664 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*  666 */             else if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  667 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str3 + ":00");
/*  668 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*  670 */             else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  671 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str3 + ":00");
/*  672 */               l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + paramString4 + ":00");
/*  673 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*  675 */             else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  676 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str3 + ":00");
/*  677 */               l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  678 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             } else {
/*      */               
/*  681 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str3 + ":00");
/*  682 */               l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  683 */               l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " " + paramString4 + ":00");
/*  684 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*      */           
/*  687 */           } else if (paramString2.compareTo(str3) >= 0 && paramString2.compareTo(str4) <= 0) {
/*      */             
/*  689 */             if (paramString4.compareTo(str3) < 0 || paramString4.compareTo(str4) > 0)
/*      */             {
/*      */               
/*  692 */               if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  693 */                 l = TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + paramString4 + ":00");
/*  694 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               }
/*  696 */               else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  697 */                 l = TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  698 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               } else {
/*      */                 
/*  701 */                 l = TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  702 */                 l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " " + paramString4 + ":00");
/*  703 */                 bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */               } 
/*      */             }
/*  706 */           } else if (paramString2.compareTo(str4) > 0 && paramString2.compareTo(str5) < 0) {
/*      */             
/*  708 */             if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  709 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  710 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*  712 */             else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  713 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str5 + ":00");
/*  714 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             } else {
/*      */               
/*  717 */               l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str5 + ":00");
/*  718 */               l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " " + paramString4 + ":00");
/*  719 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*      */           
/*  722 */           } else if (paramString2.compareTo(str5) >= 0 && paramString2.compareTo(str6) <= 0) {
/*      */             
/*  724 */             if (paramString4.compareTo(str5) < 0 || paramString4.compareTo(str6) > 0)
/*      */             {
/*      */ 
/*      */               
/*  728 */               l = TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " " + paramString4 + ":00");
/*  729 */               bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */             }
/*      */           
/*      */           } else {
/*      */             
/*  734 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + paramString4 + ":00");
/*  735 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*      */         
/*  738 */         } else if (str1.equals(paramString1) && !str1.equals(paramString3)) {
/*      */           
/*  740 */           if (paramString2.compareTo(str3) < 0) {
/*  741 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str3 + ":00");
/*  742 */             l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  743 */             l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " 23:59:59") + 1L;
/*  744 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*  746 */           else if (paramString2.compareTo(str3) >= 0 && paramString2.compareTo(str4) <= 0) {
/*  747 */             l = TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  748 */             l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " 23:59:59") + 1L;
/*  749 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*  751 */           else if (paramString2.compareTo(str4) > 0 && paramString2.compareTo(str5) < 0) {
/*  752 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " " + str5 + ":00");
/*  753 */             l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " 23:59:59") + 1L;
/*  754 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*  756 */           else if (paramString2.compareTo(str5) >= 0 && paramString2.compareTo(str6) <= 0) {
/*  757 */             l = TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " 23:59:59") + 1L;
/*  758 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           } else {
/*      */             
/*  761 */             l = TimeUtil.timeInterval(str1 + " " + paramString2 + ":00", str1 + " 23:59:59") + 1L;
/*  762 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*      */         
/*  765 */         } else if (!str1.equals(paramString1) && str1.equals(paramString3)) {
/*      */           
/*  767 */           if (paramString4.compareTo(str3) < 0) {
/*  768 */             l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " " + paramString4 + ":00");
/*  769 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*  771 */           else if (paramString4.compareTo(str3) >= 0 && paramString4.compareTo(str4) <= 0) {
/*  772 */             l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " " + str3 + ":00");
/*  773 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*  775 */           else if (paramString4.compareTo(str4) > 0 && paramString4.compareTo(str5) < 0) {
/*  776 */             l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " " + str3 + ":00");
/*  777 */             l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + paramString4 + ":00");
/*  778 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*  780 */           else if (paramString4.compareTo(str5) >= 0 && paramString4.compareTo(str6) <= 0) {
/*  781 */             l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " " + str3 + ":00");
/*  782 */             l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  783 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           } else {
/*      */             
/*  786 */             l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " " + str3 + ":00");
/*  787 */             l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  788 */             l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " " + paramString4 + ":00");
/*  789 */             bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */           }
/*      */         
/*  792 */         } else if (!str1.equals(paramString1) && !str1.equals(paramString3)) {
/*  793 */           l = TimeUtil.timeInterval(str1 + " 00:00:00", str1 + " " + str3 + ":00");
/*  794 */           l += TimeUtil.timeInterval(str1 + " " + str4 + ":00", str1 + " " + str5 + ":00");
/*  795 */           l += TimeUtil.timeInterval(str1 + " " + str6 + ":00", str1 + " 23:59:59") + 1L;
/*  796 */           bigDecimal1 = addTotalWorkingHoursByTimeInterval(bigDecimal1, l);
/*      */         } 
/*  798 */         bigDecimal2 = bigDecimal2.add(bigDecimal1);
/*      */         
/*  800 */         str2 = TimeUtil.dateAdd(str1, 1);
/*  801 */         str1 = str2;
/*      */       } 
/*      */       
/*  804 */       return bigDecimal2.toString();
/*  805 */     } catch (Exception exception) {
/*  806 */       return "";
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBillSelectName(int paramInt1, String paramString, int paramInt2) {
/*  822 */     String str = "";
/*      */     
/*  824 */     StringBuffer stringBuffer = new StringBuffer();
/*  825 */     stringBuffer.append(" select selectName ")
/*  826 */       .append("   from workflow_billfield a,workflow_SelectItem b ")
/*  827 */       .append("  where a.id=b.fieldId ")
/*  828 */       .append("    and a.billId=").append(paramInt1)
/*  829 */       .append("    and a.fieldName='").append(paramString).append("' ")
/*  830 */       .append("    and b.selectValue=").append(paramInt2);
/*      */     
/*  832 */     RecordSet recordSet = new RecordSet();
/*  833 */     recordSet.executeSql(stringBuffer.toString());
/*  834 */     if (recordSet.next()) {
/*  835 */       str = StringUtil.vString(recordSet.getString("selectName"));
/*      */     }
/*      */     
/*  838 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean getIsWorkday(String paramString) {
/*  849 */     boolean bool = true;
/*      */     
/*  851 */     int i = 0;
/*  852 */     String str = "";
/*  853 */     if (this.user != null) {
/*  854 */       i = Util.getIntValue("" + this.user.getUserSubCompany1(), 0);
/*      */     }
/*  856 */     if (this.user != null) {
/*  857 */       str = this.user.getCountryid();
/*      */     }
/*  859 */     bool = getIsWorkday(paramString, i, str);
/*  860 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean getIsWorkday(String paramString1, int paramInt, String paramString2) {
/*  873 */     boolean bool = true;
/*      */ 
/*      */     
/*      */     try {
/*  877 */       if (paramInt <= 0 && 
/*  878 */         this.user != null) {
/*  879 */         paramInt = Util.getIntValue("" + this.user.getUserSubCompany1(), 0);
/*      */       }
/*      */       
/*  882 */       if ((paramString2 == null || paramString2.trim().equals("")) && 
/*  883 */         this.user != null && this.user.getCountryid() != null && !this.user.getCountryid().equals("")) {
/*  884 */         paramString2 = this.user.getCountryid();
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  890 */       StringBuffer stringBuffer = new StringBuffer();
/*  891 */       stringBuffer.append(" select 1 ")
/*  892 */         .append("   from HrmPubHoliday ")
/*  893 */         .append("  where holidayDate='").append(paramString1).append("' ")
/*  894 */         .append("    and changeType=1 ");
/*      */       
/*  896 */       if (paramString2 != null && !paramString2.trim().equals("")) {
/*  897 */         stringBuffer.append(" and countryId=").append(paramString2);
/*      */       }
/*  899 */       RecordSet recordSet = new RecordSet();
/*  900 */       recordSet.executeSql(stringBuffer.toString());
/*  901 */       if (recordSet.next()) {
/*  902 */         bool = false;
/*  903 */         return bool;
/*      */       } 
/*      */ 
/*      */       
/*  907 */       stringBuffer = new StringBuffer();
/*  908 */       stringBuffer.append(" select 1 ")
/*  909 */         .append("   from HrmPubHoliday ")
/*  910 */         .append("  where holidayDate='").append(paramString1).append("' ")
/*  911 */         .append("    and changeType=3 ");
/*      */       
/*  913 */       if (paramString2 != null && !paramString2.trim().equals("")) {
/*  914 */         stringBuffer.append(" and countryId=").append(paramString2);
/*      */       }
/*  916 */       recordSet.executeSql(stringBuffer.toString());
/*  917 */       if (recordSet.next()) {
/*  918 */         bool = false;
/*  919 */         return bool;
/*      */       } 
/*      */ 
/*      */       
/*  923 */       stringBuffer = new StringBuffer();
/*  924 */       stringBuffer.append(" select 1 ")
/*  925 */         .append("   from HrmPubHoliday ")
/*  926 */         .append("  where holidayDate='").append(paramString1).append("' ")
/*  927 */         .append("    and changeType=2 ");
/*      */       
/*  929 */       if (paramString2 != null && !paramString2.trim().equals("")) {
/*  930 */         stringBuffer.append(" and countryId=").append(paramString2);
/*      */       }
/*  932 */       recordSet.executeSql(stringBuffer.toString());
/*  933 */       if (recordSet.next()) {
/*  934 */         bool = true;
/*  935 */         return bool;
/*      */       } 
/*      */       
/*  938 */       int i = TimeUtil.dateWeekday(paramString1);
/*  939 */       String str1 = "";
/*  940 */       String str2 = "";
/*  941 */       stringBuffer = new StringBuffer();
/*  942 */       stringBuffer.append(" select ");
/*  943 */       if (i == 1) {
/*  944 */         str1 = "monstarttime1";
/*  945 */       } else if (i == 2) {
/*  946 */         str1 = "tuestarttime1";
/*  947 */       } else if (i == 3) {
/*  948 */         str1 = "wedstarttime1";
/*  949 */       } else if (i == 4) {
/*  950 */         str1 = "thustarttime1";
/*  951 */       } else if (i == 5) {
/*  952 */         str1 = "fristarttime1";
/*  953 */       } else if (i == 6) {
/*  954 */         str1 = "satstarttime1";
/*  955 */       } else if (i == 0) {
/*  956 */         str1 = "sunstarttime1";
/*      */       } 
/*      */       
/*  959 */       stringBuffer.append(str1 + " from HrmSchedule where scheduletype = '4' and relatedid = " + paramInt);
/*  960 */       if ("oracle".equals(recordSet.getDBType()) || "db2".equals(recordSet.getDBType())) {
/*  961 */         stringBuffer.append(" and to_date(validedatefrom,'yyyy-mm-dd') <= to_date('" + paramString1 + "','yyyy-mm-dd') and to_date(validedateto,'yyyy-mm-dd') >= to_date('" + paramString1 + "','yyyy-mm-dd') ");
/*      */       } else {
/*  963 */         stringBuffer.append(" and validedatefrom <= '" + paramString1 + "' and validedateto >= '" + paramString1 + "' ");
/*      */       } 
/*  965 */       recordSet.executeSql(stringBuffer.toString());
/*  966 */       if (recordSet.getCounts() == 0) {
/*  967 */         stringBuffer = new StringBuffer();
/*  968 */         stringBuffer.append(" select " + str1);
/*  969 */         stringBuffer.append(" from HrmSchedule where scheduletype = '3' and relatedid = 0 ");
/*  970 */         if ("oracle".equals(recordSet.getDBType()) || "db2".equals(recordSet.getDBType())) {
/*  971 */           stringBuffer.append(" and to_date(validedatefrom,'yyyy-mm-dd') <= to_date('" + paramString1 + "','yyyy-mm-dd') and to_date(validedateto,'yyyy-mm-dd') >= to_date('" + paramString1 + "','yyyy-mm-dd') ");
/*      */         } else {
/*  973 */           stringBuffer.append(" and validedatefrom <= '" + paramString1 + "' and validedateto >= '" + paramString1 + "' ");
/*      */         } 
/*  975 */         recordSet.executeSql(stringBuffer.toString());
/*  976 */         recordSet.next();
/*  977 */         str2 = recordSet.getString(1).trim();
/*  978 */         if ("".equals(str2)) {
/*  979 */           bool = false;
/*  980 */           return bool;
/*      */         } 
/*      */       } else {
/*  983 */         recordSet.next();
/*  984 */         str2 = recordSet.getString(1).trim();
/*  985 */         if ("".equals(str2)) {
/*  986 */           bool = false;
/*  987 */           return bool;
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1008 */       return bool;
/* 1009 */     } catch (Exception exception) {
/* 1010 */       return bool;
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map getOnDutyAndOffDutyTimeMap(String paramString, int paramInt) {
/* 1108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/* 1110 */       String str1 = "";
/* 1111 */       String str2 = "";
/* 1112 */       String str3 = "";
/* 1113 */       String str4 = "";
/* 1114 */       String str5 = "1";
/* 1115 */       String str6 = "";
/*      */       
/* 1117 */       StringBuffer stringBuffer = new StringBuffer();
/* 1118 */       int i = TimeUtil.dateWeekday(paramString);
/*      */       
/* 1120 */       RecordSet recordSet = new RecordSet();
/*      */       
/* 1122 */       stringBuffer.append(" select relateWeekDay ")
/* 1123 */         .append("   from HrmPubHoliday ")
/* 1124 */         .append("  where holidayDate='").append(paramString).append("' ")
/* 1125 */         .append("    and changeType=2 ");
/*      */       
/* 1127 */       if (this.user != null && this.user.getCountryid() != null && !this.user.getCountryid().equals("")) {
/* 1128 */         stringBuffer.append(" and countryId=").append(this.user.getCountryid());
/*      */       }
/*      */       
/* 1131 */       recordSet.executeSql(stringBuffer.toString());
/* 1132 */       if (recordSet.next()) {
/* 1133 */         i = Util.getIntValue(recordSet.getString("relateWeekDay"), 1) - 1;
/*      */       }
/*      */       
/* 1136 */       stringBuffer = new StringBuffer();
/* 1137 */       if (i == 0) {
/* 1138 */         stringBuffer.append(" select sunStartTime1 as onDutyTimeAM,sunEndTime1 as offDutyTimeAM,sunStartTime2 as onDutyTimePM ,sunEndTime2 as offDutyTimePM ");
/* 1139 */       } else if (i == 1) {
/* 1140 */         stringBuffer.append(" select monStartTime1 as onDutyTimeAM,monEndTime1 as offDutyTimeAM,monStartTime2 as onDutyTimePM ,monEndTime2 as offDutyTimePM ");
/* 1141 */       } else if (i == 2) {
/* 1142 */         stringBuffer.append(" select tueStartTime1 as onDutyTimeAM,tueEndTime1 as offDutyTimeAM,tueStartTime2 as onDutyTimePM ,tueEndTime2 as offDutyTimePM ");
/* 1143 */       } else if (i == 3) {
/* 1144 */         stringBuffer.append(" select wedStartTime1 as onDutyTimeAM,wedEndTime1 as offDutyTimeAM,wedStartTime2 as onDutyTimePM ,wedEndTime2 as offDutyTimePM ");
/* 1145 */       } else if (i == 4) {
/* 1146 */         stringBuffer.append(" select thuStartTime1 as onDutyTimeAM,thuEndTime1 as offDutyTimeAM,thuStartTime2 as onDutyTimePM ,thuEndTime2 as offDutyTimePM ");
/* 1147 */       } else if (i == 5) {
/* 1148 */         stringBuffer.append(" select friStartTime1 as onDutyTimeAM,friEndTime1 as offDutyTimeAM,friStartTime2 as onDutyTimePM ,friEndTime2 as offDutyTimePM ");
/*      */       } else {
/* 1150 */         stringBuffer.append(" select satStartTime1 as onDutyTimeAM,satEndTime1 as offDutyTimeAM,satStartTime2 as onDutyTimePM ,satEndTime2 as offDutyTimePM ");
/*      */       } 
/*      */       
/* 1153 */       stringBuffer.append(",sign_type as signType,sign_start_time as signStartTime  from HrmSchedule ")
/* 1154 */         .append(" where valideDateFrom<='").append(paramString).append("'")
/* 1155 */         .append("   and valideDateTo>='").append(paramString).append("'")
/* 1156 */         .append("   and (scheduleType='3' ")
/* 1157 */         .append("        or (scheduleType='4' and relatedId=").append(paramInt).append(")")
/* 1158 */         .append("        ) ")
/* 1159 */         .append(" order by scheduleType desc,relatedId asc,id desc ");
/*      */ 
/*      */       
/* 1162 */       recordSet.executeSql(stringBuffer.toString());
/* 1163 */       if (recordSet.next()) {
/* 1164 */         str1 = StringUtil.vString(recordSet.getString("onDutyTimeAM"));
/* 1165 */         str2 = StringUtil.vString(recordSet.getString("offDutyTimeAM"));
/* 1166 */         str3 = StringUtil.vString(recordSet.getString("onDutyTimePM"));
/* 1167 */         str4 = StringUtil.vString(recordSet.getString("offDutyTimePM"));
/* 1168 */         str5 = StringUtil.vString(recordSet.getString("signType"), "1");
/* 1169 */         str6 = StringUtil.vString(recordSet.getString("signStartTime"));
/*      */       } 
/*      */       
/* 1172 */       if (str1 == null || str1.equals("")) {
/* 1173 */         str1 = this.onDutyTimeAMDefault;
/*      */       }
/* 1175 */       if (str2 == null || str2.equals("")) {
/* 1176 */         str2 = this.offDutyTimeAMDefault;
/*      */       }
/* 1178 */       if (str3 == null || str3.equals("")) {
/* 1179 */         str3 = this.onDutyTimePMDefault;
/*      */       }
/* 1181 */       if (str4 == null || str4.equals("")) {
/* 1182 */         str4 = this.offDutyTimePMDefault;
/*      */       }
/*      */       
/* 1185 */       hashMap.put("onDutyTimeAM", str1);
/* 1186 */       hashMap.put("offDutyTimeAM", str2);
/* 1187 */       hashMap.put("onDutyTimePM", str3);
/* 1188 */       hashMap.put("offDutyTimePM", str4);
/* 1189 */       hashMap.put("signType", str5);
/* 1190 */       hashMap.put("signStartTime", str6);
/*      */       
/* 1192 */       return hashMap;
/* 1193 */     } catch (Exception exception) {
/* 1194 */       return hashMap;
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStatusNameOfHrmResource(String paramString) {
/* 1206 */     String str = "";
/*      */     
/* 1208 */     if (paramString == null || paramString.trim().equals("")) {
/* 1209 */       return str;
/*      */     }
/*      */     
/* 1212 */     int i = 7;
/* 1213 */     if (this.user != null) {
/* 1214 */       i = this.user.getLanguage();
/*      */     }
/*      */     
/* 1217 */     if (paramString.equals("0")) {
/* 1218 */       str = SystemEnv.getHtmlLabelName(15710, i);
/* 1219 */     } else if (paramString.equals("1")) {
/* 1220 */       str = SystemEnv.getHtmlLabelName(15711, i);
/* 1221 */     } else if (paramString.equals("2")) {
/* 1222 */       str = SystemEnv.getHtmlLabelName(480, i);
/* 1223 */     } else if (paramString.equals("3")) {
/* 1224 */       str = SystemEnv.getHtmlLabelName(15844, i);
/* 1225 */     } else if (paramString.equals("4")) {
/* 1226 */       str = SystemEnv.getHtmlLabelName(6094, i);
/* 1227 */     } else if (paramString.equals("5")) {
/* 1228 */       str = SystemEnv.getHtmlLabelName(6091, i);
/* 1229 */     } else if (paramString.equals("6")) {
/* 1230 */       str = SystemEnv.getHtmlLabelName(6092, i);
/* 1231 */     } else if (paramString.equals("7")) {
/* 1232 */       str = SystemEnv.getHtmlLabelName(2245, i);
/* 1233 */     } else if (paramString.equals("10")) {
/* 1234 */       str = SystemEnv.getHtmlLabelName(1831, i);
/*      */     } 
/*      */ 
/*      */     
/* 1238 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStatusName(String paramString1, String paramString2) {
/* 1248 */     String str = "";
/*      */     
/* 1250 */     ResourceComInfo resourceComInfo = null;
/*      */     
/*      */     try {
/* 1253 */       resourceComInfo = new ResourceComInfo();
/* 1254 */       String str1 = resourceComInfo.getStatus(paramString1);
/*      */       
/* 1256 */       if (str1 == null || str1.trim().equals("")) {
/* 1257 */         return str;
/*      */       }
/*      */       
/* 1260 */       int i = Integer.valueOf(paramString2).intValue();
/*      */       
/* 1262 */       if (str1.equals("0")) {
/* 1263 */         str = SystemEnv.getHtmlLabelName(15710, i);
/* 1264 */       } else if (str1.equals("1")) {
/* 1265 */         str = SystemEnv.getHtmlLabelName(15711, i);
/* 1266 */       } else if (str1.equals("2")) {
/* 1267 */         str = SystemEnv.getHtmlLabelName(480, i);
/* 1268 */       } else if (str1.equals("3")) {
/* 1269 */         str = SystemEnv.getHtmlLabelName(15844, i);
/* 1270 */       } else if (str1.equals("4")) {
/* 1271 */         str = SystemEnv.getHtmlLabelName(6094, i);
/* 1272 */       } else if (str1.equals("5")) {
/* 1273 */         str = SystemEnv.getHtmlLabelName(6091, i);
/* 1274 */       } else if (str1.equals("6")) {
/* 1275 */         str = SystemEnv.getHtmlLabelName(6092, i);
/* 1276 */       } else if (str1.equals("7")) {
/* 1277 */         str = SystemEnv.getHtmlLabelName(2245, i);
/* 1278 */       } else if (str1.equals("10")) {
/* 1279 */         str = SystemEnv.getHtmlLabelName(1831, i);
/*      */       } 
/* 1281 */     } catch (Exception exception) {
/* 1282 */       return "";
/*      */     } 
/*      */     
/* 1285 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BigDecimal addTotalWorkingHoursByTimeInterval(BigDecimal paramBigDecimal, long paramLong) {
/* 1296 */     BigDecimal bigDecimal = new BigDecimal(0.0D);
/*      */     
/*      */     try {
/* 1299 */       bigDecimal = paramBigDecimal.add((new BigDecimal(paramLong)).divide(new BigDecimal(3600), 4, 4));
/* 1300 */       return bigDecimal;
/* 1301 */     } catch (Exception exception) {
/* 1302 */       return bigDecimal;
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BigDecimal getWorkHoursPerDay(String paramString, int paramInt) {
/* 1315 */     BigDecimal bigDecimal = this.workHoursPerDayDefault;
/*      */ 
/*      */     
/*      */     try {
/* 1319 */       String str1 = "";
/* 1320 */       String str2 = "";
/* 1321 */       String str3 = "";
/* 1322 */       String str4 = "";
/*      */       
/* 1324 */       StringBuffer stringBuffer = new StringBuffer();
/* 1325 */       stringBuffer.append(" select wedStartTime1 as onDutyTimeAM,wedEndTime1 as offDutyTimeAM,wedStartTime2 as onDutyTimePM ,wedEndTime2 as offDutyTimePM ")
/* 1326 */         .append("  from HrmSchedule ")
/* 1327 */         .append(" where valideDateFrom<='").append(paramString).append("'")
/* 1328 */         .append("   and valideDateTo>='").append(paramString).append("'")
/* 1329 */         .append(" where  (scheduleType='3' ")
/* 1330 */         .append("        or (scheduleType='4' and relatedId=").append(paramInt).append(")")
/* 1331 */         .append("        ) ")
/* 1332 */         .append(" order by scheduleType desc,relatedId asc,id desc ");
/*      */ 
/*      */       
/* 1335 */       RecordSet recordSet = new RecordSet();
/* 1336 */       recordSet.executeSql(stringBuffer.toString());
/* 1337 */       if (recordSet.next()) {
/* 1338 */         str1 = StringUtil.vString(recordSet.getString("onDutyTimeAM"));
/* 1339 */         str2 = StringUtil.vString(recordSet.getString("offDutyTimeAM"));
/* 1340 */         str3 = StringUtil.vString(recordSet.getString("onDutyTimePM"));
/* 1341 */         str4 = StringUtil.vString(recordSet.getString("offDutyTimePM"));
/*      */       } 
/*      */       
/* 1344 */       if (str1 == null || str1.equals("")) {
/* 1345 */         str1 = this.onDutyTimeAMDefault;
/*      */       }
/* 1347 */       if (str2 == null || str2.equals("")) {
/* 1348 */         str2 = this.offDutyTimeAMDefault;
/*      */       }
/* 1350 */       if (str3 == null || str3.equals("")) {
/* 1351 */         str3 = this.onDutyTimePMDefault;
/*      */       }
/* 1353 */       if (str4 == null || str4.equals("")) {
/* 1354 */         str4 = this.offDutyTimePMDefault;
/*      */       }
/*      */ 
/*      */       
/* 1358 */       String str5 = Util.addTime(Util.subTime(str2, str1), Util.subTime(str4, str3));
/* 1359 */       ArrayList<String> arrayList = Util.TokenizerString(str5, ":");
/* 1360 */       int i = Util.getIntValue(arrayList.get(0), 0);
/* 1361 */       int j = Util.getIntValue(arrayList.get(1), 1);
/* 1362 */       double d = 1.0D * j / 60.0D + i;
/* 1363 */       bigDecimal = new BigDecimal(d);
/*      */       
/* 1365 */       return bigDecimal;
/* 1366 */     } catch (Exception exception) {
/* 1367 */       return bigDecimal;
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BigDecimal getWorkHoursPerDay() {
/* 1378 */     BigDecimal bigDecimal = this.workHoursPerDayDefault;
/*      */ 
/*      */     
/*      */     try {
/* 1382 */       String str1 = "";
/* 1383 */       String str2 = "";
/* 1384 */       String str3 = "";
/* 1385 */       String str4 = "";
/*      */       
/* 1387 */       StringBuffer stringBuffer = new StringBuffer();
/* 1388 */       stringBuffer.append(" select wedStartTime1 as onDutyTimeAM,wedEndTime1 as offDutyTimeAM,wedStartTime2 as onDutyTimePM ,wedEndTime2 as offDutyTimePM ")
/* 1389 */         .append("  from HrmSchedule ")
/* 1390 */         .append(" where  scheduleType='3' ")
/* 1391 */         .append(" order by relatedId asc,id desc ");
/*      */ 
/*      */       
/* 1394 */       RecordSet recordSet = new RecordSet();
/* 1395 */       recordSet.executeSql(stringBuffer.toString());
/* 1396 */       if (recordSet.next()) {
/* 1397 */         str1 = StringUtil.vString(recordSet.getString("onDutyTimeAM"));
/* 1398 */         str2 = StringUtil.vString(recordSet.getString("offDutyTimeAM"));
/* 1399 */         str3 = StringUtil.vString(recordSet.getString("onDutyTimePM"));
/* 1400 */         str4 = StringUtil.vString(recordSet.getString("offDutyTimePM"));
/*      */       } 
/*      */       
/* 1403 */       if (str1 == null || str1.equals("")) {
/* 1404 */         str1 = this.onDutyTimeAMDefault;
/*      */       }
/* 1406 */       if (str2 == null || str2.equals("")) {
/* 1407 */         str2 = this.offDutyTimeAMDefault;
/*      */       }
/* 1409 */       if (str3 == null || str3.equals("")) {
/* 1410 */         str3 = this.onDutyTimePMDefault;
/*      */       }
/* 1412 */       if (str4 == null || str4.equals("")) {
/* 1413 */         str4 = this.offDutyTimePMDefault;
/*      */       }
/*      */ 
/*      */       
/* 1417 */       String str5 = Util.addTime(Util.subTime(str2, str1), Util.subTime(str4, str3));
/* 1418 */       ArrayList<String> arrayList = Util.TokenizerString(str5, ":");
/* 1419 */       int i = Util.getIntValue(arrayList.get(0), 0);
/* 1420 */       int j = Util.getIntValue(arrayList.get(1), 1);
/* 1421 */       double d = 1.0D * j / 60.0D + i;
/* 1422 */       bigDecimal = new BigDecimal(d);
/*      */       
/* 1424 */       return bigDecimal;
/* 1425 */     } catch (Exception exception) {
/* 1426 */       return bigDecimal;
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private BigDecimal getWorkHoursPerDay(Map paramMap) {
/* 1438 */     BigDecimal bigDecimal = this.workHoursPerDayDefault;
/*      */ 
/*      */     
/*      */     try {
/* 1442 */       String str1 = StringUtil.vString((String)paramMap.get("onDutyTimeAM"));
/* 1443 */       String str2 = StringUtil.vString((String)paramMap.get("offDutyTimeAM"));
/* 1444 */       String str3 = StringUtil.vString((String)paramMap.get("onDutyTimePM"));
/* 1445 */       String str4 = StringUtil.vString((String)paramMap.get("offDutyTimePM"));
/*      */       
/* 1447 */       if (str1 == null || str1.equals("")) {
/* 1448 */         str1 = this.onDutyTimeAMDefault;
/*      */       }
/* 1450 */       if (str2 == null || str2.equals("")) {
/* 1451 */         str2 = this.offDutyTimeAMDefault;
/*      */       }
/* 1453 */       if (str3 == null || str3.equals("")) {
/* 1454 */         str3 = this.onDutyTimePMDefault;
/*      */       }
/* 1456 */       if (str4 == null || str4.equals("")) {
/* 1457 */         str4 = this.offDutyTimePMDefault;
/*      */       }
/*      */ 
/*      */       
/* 1461 */       String str5 = Util.addTime(Util.subTime(str2, str1), Util.subTime(str4, str3));
/* 1462 */       ArrayList<String> arrayList = Util.TokenizerString(str5, ":");
/* 1463 */       int i = Util.getIntValue(arrayList.get(0), 0);
/* 1464 */       int j = Util.getIntValue(arrayList.get(1), 1);
/* 1465 */       double d = 1.0D * j / 60.0D + i;
/* 1466 */       bigDecimal = new BigDecimal(d);
/*      */       
/* 1468 */       return bigDecimal;
/* 1469 */     } catch (Exception exception) {
/* 1470 */       return bigDecimal;
/*      */     } finally {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String[] getSignInfo(User paramUser) {
/* 1481 */     String[] arrayOfString = { "", "", "", "", "" };
/* 1482 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1484 */     String str1 = TimeUtil.getCurrentTimeString();
/* 1485 */     String str2 = TimeUtil.getCurrentDateString() + " 00:00:00";
/* 1486 */     String str3 = TimeUtil.getCurrentDateString();
/* 1487 */     long l = TimeUtil.timeInterval(str1, str2);
/*      */     
/* 1489 */     boolean bool1 = false;
/* 1490 */     boolean bool2 = false;
/* 1491 */     boolean bool = false;
/*      */     
/* 1493 */     recordSet.executeSql("select loginid from hrmresourcemanager where loginid = '" + paramUser.getLoginid() + "'");
/* 1494 */     if (recordSet.next()) {
/* 1495 */       bool2 = true;
/*      */     }
/*      */     
/* 1498 */     if (!bool2) {
/* 1499 */       recordSet.executeSql("select loginid from hrmresource where loginid = '" + paramUser.getLoginid() + "'");
/* 1500 */       if (recordSet.next()) {
/* 1501 */         bool2 = false;
/*      */       } else {
/* 1503 */         bool2 = true;
/*      */       } 
/*      */     } 
/*      */     
/* 1507 */     String str4 = "0";
/* 1508 */     String str5 = "1";
/* 1509 */     String str6 = "";
/* 1510 */     recordSet.executeSql("select needsign,onlyworkday, signTimeScope from hrmkqsystemSet ");
/* 1511 */     if (recordSet.next()) {
/* 1512 */       str4 = "" + recordSet.getInt("needsign");
/* 1513 */       str5 = "" + recordSet.getInt("onlyworkday");
/* 1514 */       str6 = "" + recordSet.getString("signTimeScope");
/*      */     } 
/*      */ 
/*      */     
/* 1518 */     String str7 = "1";
/* 1519 */     String str8 = "1";
/* 1520 */     String str9 = "1";
/* 1521 */     String str10 = "";
/*      */ 
/*      */     
/* 1524 */     HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 1525 */     hrmScheduleDiffUtil.setUser(paramUser);
/* 1526 */     bool = hrmScheduleDiffUtil.getIsWorkday(str3);
/* 1527 */     if (str5.equals("0")) bool = true;
/*      */     
/* 1529 */     if (str4.equals("1") && bool && !bool2 && l < 0L) {
/* 1530 */       Calendar calendar = Calendar.getInstance();
/* 1531 */       String str13 = Util.add0(calendar.get(1), 4);
/* 1532 */       String str14 = Util.add0(calendar.get(2) + 1, 2);
/* 1533 */       String str15 = Util.add0(calendar.get(5), 2);
/*      */       
/* 1535 */       String str16 = str13 + "-" + str14 + "-" + str15;
/* 1536 */       Map map = hrmScheduleDiffUtil.getOnDutyAndOffDutyTimeMap(str16, paramUser.getUserSubCompany1());
/* 1537 */       if (map != null) {
/* 1538 */         if (map.containsKey("signType")) str9 = StringUtil.vString(map.get("signType"), "1"); 
/* 1539 */         if (map.containsKey("signStartTime")) str10 = StringUtil.vString(map.get("signStartTime")); 
/*      */       } 
/* 1541 */       String str17 = "SELECT 1 FROM HrmScheduleSign where userId=" + paramUser.getUID() + " and  userType='" + paramUser.getLogintype() + "' and signDate='" + str16 + "' and signType='1'  and isInCom='1'";
/* 1542 */       if (str9.equals("2") && str10.length() > 0) {
/* 1543 */         Calendar calendar1 = DateUtil.getCalendar(DateUtil.getCurrentDate() + " " + str10 + ":00");
/* 1544 */         if (DateUtil.getCalendar().after(calendar1)) {
/* 1545 */           str17 = str17 + " and signTime >= '" + str10 + ":00'";
/*      */         }
/*      */       } 
/* 1548 */       recordSet.executeSql(str17);
/* 1549 */       if (recordSet.next()) {
/* 1550 */         str7 = "2";
/*      */       }
/* 1552 */       str8 = str7;
/* 1553 */       bool1 = true;
/*      */     } else {
/* 1555 */       bool1 = false;
/*      */     } 
/*      */ 
/*      */     
/* 1559 */     String str11 = null;
/* 1560 */     String str12 = null;
/* 1561 */     if (str6.length() > 0) {
/* 1562 */       String[] arrayOfString1 = str6.split("-");
/* 1563 */       str11 = Tools.getCurrentDate() + " " + arrayOfString1[0] + ":00";
/* 1564 */       str12 = Tools.getCurrentDate() + " " + arrayOfString1[1] + ":00";
/*      */ 
/*      */       
/* 1567 */       if (!str8.equals("2")) {
/*      */ 
/*      */         
/* 1570 */         if (TimeUtil.timeInterval(str11, str1) < 0L) bool1 = false; 
/* 1571 */         if (TimeUtil.timeInterval(str1, str12) < 0L) bool1 = false;
/*      */       
/*      */       } 
/*      */     } 
/* 1575 */     arrayOfString[0] = bool1 + "";
/* 1576 */     arrayOfString[1] = str8;
/* 1577 */     arrayOfString[2] = str7;
/* 1578 */     arrayOfString[3] = str9;
/* 1579 */     arrayOfString[4] = str10;
/* 1580 */     return arrayOfString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */