/*     */ package weaver.hrm.report.schedulediff1512;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmMFScheduleDiff;
/*     */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*     */ import weaver.hrm.attendance.manager.HrmMFScheduleDiffManager;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.report.domain.HrmReport;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffListComparator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffDetAbsentFromWorkManager
/*     */   extends BaseBean
/*     */ {
/*  50 */   private User user = null;
/*     */   private boolean sortForResult = true;
/*  52 */   private List<HrmReport> list000 = new ArrayList<>();
/*  53 */   private List<HrmReport> list001 = new ArrayList<>();
/*  54 */   private List<HrmReport> list002 = new ArrayList<>();
/*  55 */   private List<HrmReport> list004 = new ArrayList<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  63 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSortForResult(boolean paramBoolean) {
/*  71 */     this.sortForResult = paramBoolean;
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getScheduleList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  75 */     String str1 = paramMap.get("fromDate");
/*  76 */     String str2 = paramMap.get("toDate");
/*  77 */     int i = Util.getIntValue(paramMap.get("subCompanyId"), -1);
/*  78 */     int j = Util.getIntValue(paramMap.get("departmentId"), -1);
/*  79 */     String str3 = StringUtil.vString(paramMap.get("resourceId"));
/*  80 */     return getScheduleList(str1, str2, i, j, str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) {
/*  95 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, String.valueOf(paramInt3));
/*     */   }
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  99 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 100 */     HashMap<Object, Object> hashMap = null;
/*     */ 
/*     */     
/* 103 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/* 104 */       .trim().equals("") || paramString1
/* 105 */       .compareTo(paramString2) > 0) {
/* 106 */       return arrayList;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 111 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 112 */       RecordSet recordSet = new RecordSet();
/* 113 */       StringBuffer stringBuffer = null;
/*     */       
/* 115 */       String str1 = "";
/* 116 */       String str2 = "";
/* 117 */       String str3 = "";
/* 118 */       String str4 = "";
/* 119 */       String str5 = "";
/* 120 */       String str6 = "";
/* 121 */       String str7 = "";
/* 122 */       String str8 = "";
/*     */       
/* 124 */       Map map = null;
/* 125 */       String str9 = null;
/* 126 */       String str10 = "";
/* 127 */       String str11 = "";
/* 128 */       String str12 = null;
/* 129 */       String str13 = "1";
/* 130 */       String str14 = "";
/* 131 */       String str15 = recordSet.getDBType();
/* 132 */       boolean bool1 = false;
/*     */       
/* 134 */       String str16 = "";
/* 135 */       String str17 = "";
/* 136 */       boolean bool = false;
/* 137 */       boolean bool2 = true;
/* 138 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 139 */       hrmScheduleDiffUtil.setUser(this.user);
/*     */       
/* 141 */       HrmMFScheduleDiffManager hrmMFScheduleDiffManager = new HrmMFScheduleDiffManager();
/* 142 */       HrmMFScheduleDiff hrmMFScheduleDiff = null;
/*     */       
/* 144 */       this.list000 = getDataMap(paramString1, paramString2, paramString3, 0);
/* 145 */       this.list001 = getDataMap(paramString1, paramString2, paramString3, 1);
/* 146 */       this.list002 = getDataMap(paramString1, paramString2, paramString3, 2);
/* 147 */       this.list004 = getDataMap(paramString1, paramString2, paramString3, 4);
/*     */       
/* 149 */       String str18 = DateUtil.getCurrentDate();
/* 150 */       for (str16 = paramString1; !bool; ) {
/*     */         
/* 152 */         if (str16.equals(paramString2)) {
/* 153 */           bool = true;
/* 154 */         } else if (str16.equals(str18)) {
/* 155 */           bool = true;
/*     */         } 
/*     */         
/* 158 */         bool2 = hrmScheduleDiffUtil.getIsWorkday(str16, paramInt1, "");
/*     */         
/* 160 */         if (!bool2) {
/*     */           
/* 162 */           str17 = TimeUtil.dateAdd(str16, 1);
/* 163 */           str16 = str17;
/*     */           
/*     */           continue;
/*     */         } 
/*     */         
/* 168 */         map = hrmScheduleDiffUtil.getOnDutyAndOffDutyTimeMap(str16, paramInt1);
/*     */         
/* 170 */         str9 = StringUtil.vString((String)map.get("onDutyTimeAM"));
/* 171 */         str10 = StringUtil.vString(map.get("offDutyTimeAM"));
/* 172 */         str11 = StringUtil.vString(map.get("onDutyTimePM"));
/* 173 */         str12 = StringUtil.vString((String)map.get("offDutyTimePM"));
/* 174 */         str13 = StringUtil.vString(map.get("signType"), "1");
/* 175 */         str14 = StringUtil.vString(map.get("signStartTime"));
/* 176 */         hrmMFScheduleDiff = new HrmMFScheduleDiff();
/* 177 */         hrmMFScheduleDiff.setClassName("HrmScheduleDiffDetAbsentFromWorkManager");
/* 178 */         hrmMFScheduleDiff.setCurrentDate(str16);
/* 179 */         hrmMFScheduleDiff.setDepartmentId(paramInt2);
/* 180 */         hrmMFScheduleDiff.setOffDutyTimeAM(str10);
/* 181 */         hrmMFScheduleDiff.setOffDutyTimePM(str12);
/* 182 */         hrmMFScheduleDiff.setOnDutyTimeAM(str9);
/* 183 */         hrmMFScheduleDiff.setOnDutyTimePM(str11);
/* 184 */         hrmMFScheduleDiff.setResourceId(paramString3);
/* 185 */         hrmMFScheduleDiff.setSignStartTime(str14);
/* 186 */         hrmMFScheduleDiff.setSignType(str13);
/* 187 */         hrmMFScheduleDiff.setSortForResult(this.sortForResult);
/* 188 */         hrmMFScheduleDiff.setSubCompanyId(paramInt1);
/* 189 */         hrmMFScheduleDiff.setSqlType(str15);
/* 190 */         hrmMFScheduleDiffManager.setBean(hrmMFScheduleDiff);
/*     */ 
/*     */         
/* 193 */         stringBuffer = new StringBuffer();
/* 194 */         if (hrmMFScheduleDiffManager.isSecSign()) {
/* 195 */           stringBuffer.append(hrmMFScheduleDiffManager.getSQL());
/*     */         } else {
/* 197 */           stringBuffer.append(" select subCompanyId1 as subCompanyId,departmentId,id as resourceId,lastName as resourceName,status,id as signId ")
/* 198 */             .append("   from HrmResource ")
/* 199 */             .append("  where status in(0,1,2,3) ");
/*     */           
/* 201 */           if ("oracle".equals(recordSet.getDBType())) {
/* 202 */             stringBuffer.append(" and loginid is not null ");
/*     */           } else {
/* 204 */             stringBuffer.append(" and loginid is not null and loginid<>'' ");
/*     */           } 
/* 206 */           stringBuffer
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 219 */             .append("     and( ")
/* 220 */             .append("          not exists( ")
/* 221 */             .append("              select 1 ")
/* 222 */             .append("               from HrmScheduleSign ")
/* 223 */             .append("              where signDate='").append(str16).append("' ")
/* 224 */             .append("                and signType='1' ")
/* 225 */             .append("                and userType='1' ")
/* 226 */             .append("                and isInCom='1' ")
/* 227 */             .append("                and userId=HrmResource.id  ")
/* 228 */             .append("          ) ")
/* 229 */             .append("         or exists( ")
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 238 */             .append("             select 1 ")
/* 239 */             .append("               from ( ")
/* 240 */             .append("                   select userId,min(signTime) as signTime ")
/* 241 */             .append("                     from HrmScheduleSign ")
/* 242 */             .append("                    where signDate='").append(str16).append("' ")
/* 243 */             .append("                      and signType='1' ")
/* 244 */             .append("                      and userType='1' ")
/* 245 */             .append("                      and isInCom='1' ")
/* 246 */             .append("                     group by userId ")
/* 247 */             .append("               )TempTable ")
/* 248 */             .append("              where signTime>='").append(str12).append(":00' ")
/* 249 */             .append("                and userId=HrmResource.id ")
/* 250 */             .append("         ) ")
/* 251 */             .append("         or exists( ")
/* 252 */             .append("             select 1 ")
/* 253 */             .append("               from ( ")
/* 254 */             .append("                   select userId,max(signTime) as signTime ")
/* 255 */             .append("                     from HrmScheduleSign ")
/* 256 */             .append("                    where signDate='").append(str16).append("' ")
/* 257 */             .append("                      and signType='2' ")
/* 258 */             .append("                      and userType='1' ")
/* 259 */             .append("                      and isInCom='1' ")
/* 260 */             .append("                     group by userId ")
/* 261 */             .append("               )TempTable ")
/* 262 */             .append("              where signTime<='").append(str9).append(":00' ")
/* 263 */             .append("                and userId=HrmResource.id ")
/* 264 */             .append("         ) ")
/* 265 */             .append(" )\t ");
/*     */ 
/*     */           
/* 268 */           if (paramInt1 > 0) {
/* 269 */             stringBuffer.append(" and  subCompanyId1=").append(paramInt1);
/*     */           }
/*     */           
/* 272 */           if (paramInt2 > 0) {
/* 273 */             stringBuffer.append(" and  departmentId=").append(paramInt2);
/*     */           }
/*     */           
/* 276 */           if (paramString3.length() > 0) {
/* 277 */             stringBuffer.append(" and  id in (").append(paramString3).append(")");
/*     */           }
/* 279 */           if (this.sortForResult) {
/* 280 */             stringBuffer.append("  order by subCompanyId1 asc,departmentId asc,id asc ");
/*     */           }
/*     */         } 
/*     */ 
/*     */         
/* 285 */         recordSet.executeSql(stringBuffer.toString());
/* 286 */         while (recordSet.next()) {
/* 287 */           str1 = StringUtil.vString(recordSet.getString("subCompanyId"));
/* 288 */           str2 = StringUtil.vString(recordSet.getString("departmentId"));
/* 289 */           str3 = departmentComInfo.getDepartmentname(str2);
/* 290 */           str4 = StringUtil.vString(recordSet.getString("resourceId"));
/* 291 */           str5 = StringUtil.vString(recordSet.getString("resourceName"));
/* 292 */           str6 = StringUtil.vString(recordSet.getString("status"));
/* 293 */           str7 = hrmScheduleDiffUtil.getStatusNameOfHrmResource(str6);
/* 294 */           str8 = StringUtil.vString(recordSet.getString("signId"));
/*     */           
/* 296 */           String str19 = str9;
/* 297 */           String str20 = str12;
/* 298 */           if (hrmMFScheduleDiffManager.isSecSign()) {
/* 299 */             String str = StringUtil.vString(recordSet.getString("tp"), "AM");
/* 300 */             if (str.equals("AM")) {
/* 301 */               str19 = str9;
/* 302 */               str20 = str10;
/*     */             } else {
/* 304 */               str19 = str11;
/* 305 */               str20 = str12;
/*     */             } 
/*     */           } 
/* 308 */           bool1 = getHasApproved(Util.getIntValue(str4, -1), str16, str19, str20, Util.getIntValue(str1));
/* 309 */           if (!bool1) {
/* 310 */             hashMap = new HashMap<>();
/* 311 */             hashMap.put("subCompanyId", str1);
/* 312 */             hashMap.put("departmentId", str2);
/* 313 */             hashMap.put("departmentName", str3);
/* 314 */             hashMap.put("resourceId", str4);
/* 315 */             hashMap.put("resourceName", str5);
/* 316 */             hashMap.put("statusName", str7);
/* 317 */             hashMap.put("currentDate", str16);
/* 318 */             hashMap.put("signId", str8);
/* 319 */             hashMap.put("signType", str13);
/* 320 */             hashMap.put("signStartTime", str14);
/* 321 */             hashMap.put("mfer", "absent");
/* 322 */             arrayList.add(hashMap);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 327 */         str17 = TimeUtil.dateAdd(str16, 1);
/* 328 */         str16 = str17;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 333 */       if (this.sortForResult && arrayList.size() >= 2) {
/* 334 */         Collections.sort(arrayList, (Comparator<? super HashMap<Object, Object>>)new HrmScheduleDiffListComparator());
/*     */       }
/*     */       
/* 337 */       return arrayList;
/* 338 */     } catch (Exception exception) {
/* 339 */       return arrayList;
/*     */     } finally {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmReport> getDataMap(String paramString1, String paramString2, String paramString3, int paramInt) {
/* 347 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 348 */     hashMap.put("fromDate", " and ((t.fromDate between '" + paramString1 + "' and '" + paramString2 + "') or (t.fromDate<='" + paramString1 + "' and t.toDate>='" + paramString1 + "') or (t.fromDate<='" + paramString2 + "' and t.toDate>='" + paramString2 + "'))");
/* 349 */     if (StringUtil.isNotNull(paramString3)) hashMap.put("resourceId", " and t.resourceId = " + paramString3); 
/* 350 */     RecordSet recordSet = new RecordSet();
/* 351 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(paramInt, hashMap));
/* 352 */     ArrayList<HrmReport> arrayList = new ArrayList();
/* 353 */     HrmReport hrmReport = null;
/* 354 */     while (recordSet.next()) {
/* 355 */       hrmReport = new HrmReport();
/* 356 */       hrmReport.setResourceId(recordSet.getInt("resourceId"));
/* 357 */       hrmReport.setFromDate(StringUtil.vString(recordSet.getString("fromDate")));
/* 358 */       hrmReport.setFromTime(StringUtil.vString(recordSet.getString("fromTime")));
/* 359 */       hrmReport.setToDate(StringUtil.vString(recordSet.getString("toDate")));
/* 360 */       hrmReport.setToTime(StringUtil.vString(recordSet.getString("toTime")));
/* 361 */       arrayList.add(hrmReport);
/*     */     } 
/* 363 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean getHasApproved(int paramInt1, String paramString1, String paramString2, String paramString3, int paramInt2) {
/* 378 */     boolean bool = false;
/* 379 */     if (getResult(paramInt1, paramString1, paramString2, paramString3, 0)) { bool = true; }
/* 380 */     else if (getResult(paramInt1, paramString1, paramString2, paramString3, 1)) { bool = true; }
/* 381 */     else if (getResult(paramInt1, paramString1, paramString2, paramString3, 2)) { bool = true; }
/* 382 */     else if (getResult(paramInt1, paramString1, paramString2, paramString3, 4)) { bool = true; }
/* 383 */      return bool;
/*     */   }
/*     */   
/*     */   private boolean getResult(int paramInt1, String paramString1, String paramString2, String paramString3, int paramInt2) {
/* 387 */     List<HrmReport> list = null;
/* 388 */     switch (paramInt2) {
/*     */       case 0:
/* 390 */         list = this.list000;
/*     */         break;
/*     */       case 1:
/* 393 */         list = this.list001;
/*     */         break;
/*     */       case 2:
/* 396 */         list = this.list002;
/*     */         break;
/*     */       case 4:
/* 399 */         list = this.list004;
/*     */         break;
/*     */     } 
/* 402 */     String str1 = "", str2 = "", str3 = "", str4 = "";
/* 403 */     for (HrmReport hrmReport : list) {
/* 404 */       if (hrmReport.getResourceId() != paramInt1 || hrmReport
/* 405 */         .getFullFromTime().compareTo(paramString1 + " " + paramString3) > 0 || hrmReport
/* 406 */         .getFullToTime().compareTo(paramString1 + " " + paramString2) < 0)
/* 407 */         continue;  str1 = hrmReport.getFromDate();
/* 408 */       str2 = hrmReport.getFromTime();
/* 409 */       str3 = hrmReport.getToDate();
/* 410 */       str4 = hrmReport.getToTime();
/* 411 */       if (str1.compareTo(paramString1) < 0) {
/* 412 */         str2 = paramString2;
/* 413 */         if (str3.compareTo(paramString1) > 0) {
/* 414 */           return true;
/*     */         }
/* 416 */         int k = str4.compareTo(paramString3);
/* 417 */         if (k >= 0) {
/* 418 */           return true;
/*     */         }
/*     */         continue;
/*     */       } 
/* 422 */       if (str3.compareTo(paramString1) > 0) {
/* 423 */         str4 = paramString3;
/* 424 */         int k = str2.compareTo(paramString2);
/* 425 */         if (k <= 0)
/* 426 */           return true; 
/*     */         continue;
/*     */       } 
/* 429 */       int i = str2.compareTo(paramString2);
/* 430 */       int j = str4.compareTo(paramString3);
/* 431 */       if (i <= 0 && j >= 0) {
/* 432 */         return true;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 437 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffDetAbsentFromWorkManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */