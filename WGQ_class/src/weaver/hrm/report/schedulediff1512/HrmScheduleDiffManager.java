/*     */ package weaver.hrm.report.schedulediff1512;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*     */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*     */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDiffManager
/*     */   extends BaseBean
/*     */ {
/*  32 */   private String criticalOfAandBForAM = "09:00:00";
/*  33 */   private String criticalOfAandBForPM = "17:00:00";
/*     */   
/*     */   private User user;
/*     */   
/*     */   public HrmScheduleDiffManager() {
/*  38 */     this.user = null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  46 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getTotalWorkingDays(String paramString1, String paramString2) {
/*  57 */     byte b = 0;
/*     */ 
/*     */     
/*  60 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/*  61 */       .trim().equals("") || paramString1
/*  62 */       .compareTo(paramString2) > 0) {
/*  63 */       return b;
/*     */     }
/*     */     
/*  66 */     String str1 = "";
/*  67 */     String str2 = "";
/*  68 */     boolean bool = false;
/*  69 */     boolean bool1 = true;
/*  70 */     HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/*  71 */     hrmScheduleDiffUtil.setUser(this.user);
/*     */     
/*  73 */     for (str1 = paramString1; !bool; ) {
/*     */       
/*  75 */       if (str1.equals(paramString2)) {
/*  76 */         bool = true;
/*     */       }
/*     */       
/*  79 */       bool1 = hrmScheduleDiffUtil.getIsWorkday(str1);
/*     */       
/*  81 */       if (!bool1) {
/*     */         
/*  83 */         str2 = TimeUtil.dateAdd(str1, 1);
/*  84 */         str1 = str2;
/*     */ 
/*     */         
/*     */         continue;
/*     */       } 
/*     */       
/*  90 */       str2 = TimeUtil.dateAdd(str1, 1);
/*  91 */       str1 = str2;
/*  92 */       b++;
/*     */     } 
/*     */     
/*  95 */     return b;
/*     */   }
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) {
/*  99 */     return getScheduleList(paramString1, paramString2, paramInt1, paramInt2, String.valueOf(paramInt3));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getScheduleList(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 114 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 115 */     HashMap<Object, Object> hashMap1 = null;
/* 116 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 118 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 120 */     StringBuffer stringBuffer = new StringBuffer();
/* 121 */     stringBuffer.append(" select id,lastName,departmentId ")
/* 122 */       .append("   from HrmResource ")
/* 123 */       .append("  where status in(0,1,2,3) ");
/*     */     
/* 125 */     if ("oracle".equals(recordSet.getDBType())) {
/* 126 */       stringBuffer.append(" and loginid is not null ");
/*     */     } else {
/* 128 */       stringBuffer.append(" and loginid is not null and loginid<>'' ");
/*     */     } 
/*     */     
/* 131 */     if (paramInt1 > 0) {
/* 132 */       stringBuffer.append(" and  subCompanyId1=").append(paramInt1);
/*     */     }
/*     */     
/* 135 */     if (paramInt2 > 0) {
/* 136 */       stringBuffer.append(" and  departmentId=").append(paramInt2);
/*     */     }
/*     */     
/* 139 */     if (paramString3.length() > 0) {
/* 140 */       stringBuffer.append(" and  id in (").append(paramString3).append(")");
/*     */     }
/*     */     
/* 143 */     stringBuffer.append("  order by subCompanyId1 asc,departmentId asc,id asc ");
/*     */ 
/*     */     
/*     */     try {
/* 147 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*     */       
/* 149 */       int i = 0;
/* 150 */       String str1 = "";
/* 151 */       int j = 0;
/* 152 */       String str2 = "";
/*     */       
/* 154 */       byte b = 0;
/*     */       
/* 156 */       recordSet.executeSql(stringBuffer.toString());
/* 157 */       while (recordSet.next()) {
/* 158 */         i = recordSet.getInt("departmentId");
/* 159 */         str1 = departmentComInfo.getDepartmentname("" + i);
/* 160 */         j = recordSet.getInt("id");
/* 161 */         str2 = StringUtil.vString(recordSet.getString("lastName"));
/*     */         
/* 163 */         hashMap1 = new HashMap<>();
/* 164 */         hashMap1.put("departmentName", str1);
/* 165 */         hashMap1.put("resourceName", str2);
/* 166 */         hashMap1.put("resourceId", String.valueOf(j));
/*     */         
/* 168 */         hashMap2.put(String.valueOf(j), String.valueOf(b));
/* 169 */         arrayList.add(hashMap1);
/* 170 */         b++;
/*     */       } 
/*     */ 
/*     */       
/* 174 */       updateDataOfBeLate(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */ 
/*     */       
/* 177 */       updateDataOfLeaveEarly(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */ 
/*     */       
/* 180 */       updateDataOfAbsentFromWork(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */ 
/*     */       
/* 183 */       updateDataOfNoSign(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */ 
/*     */       
/* 186 */       updateDataForAttFlow(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3, 0, "leave");
/*     */ 
/*     */       
/* 189 */       updateDataForAttFlow(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3, 1, "evection");
/*     */ 
/*     */       
/* 192 */       updateDataForAttFlow(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3, 2, "outDays");
/*     */ 
/*     */       
/* 195 */       updateDataForAttFlow(arrayList, hashMap2, paramString1, paramString2, paramInt1, paramInt2, paramString3, 3, "overDays");
/*     */       
/* 197 */       return arrayList;
/* 198 */     } catch (Exception exception) {
/* 199 */       return arrayList;
/*     */     } finally {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfBeLate(List<Map> paramList, Map paramMap, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 219 */     HrmScheduleDiffDetBeLateManager hrmScheduleDiffDetBeLateManager = new HrmScheduleDiffDetBeLateManager();
/* 220 */     hrmScheduleDiffDetBeLateManager.setUser(this.user);
/* 221 */     hrmScheduleDiffDetBeLateManager.setSortForResult(false);
/* 222 */     List<Map> list = hrmScheduleDiffDetBeLateManager.getScheduleList(paramString1, paramString2, paramInt1, paramInt2, paramString3);
/* 223 */     Map map = null;
/*     */     
/* 225 */     String str1 = "";
/* 226 */     String str2 = "";
/* 227 */     int i = -1;
/*     */     
/* 229 */     Map<String, String> map1 = null;
/* 230 */     String str3 = "";
/* 231 */     String str4 = "";
/* 232 */     for (byte b = 0; b < list.size(); b++) {
/* 233 */       map = list.get(b);
/* 234 */       str1 = StringUtil.vString((String)map.get("resourceId"));
/* 235 */       str2 = StringUtil.vString((String)map.get("signTime"));
/*     */       
/* 237 */       i = Util.getIntValue((String)paramMap.get(str1), -1);
/* 238 */       if (i >= 0) {
/* 239 */         map1 = paramList.get(i);
/*     */         
/* 241 */         if (str2.compareTo(this.criticalOfAandBForAM) < 0) {
/* 242 */           str3 = (String)map1.get("beLateA");
/* 243 */           str3 = String.valueOf(Util.getIntValue(str3, 0) + 1);
/* 244 */           map1.put("beLateA", str3);
/*     */         } else {
/* 246 */           str4 = map1.get("beLateB");
/* 247 */           str4 = String.valueOf(Util.getIntValue(str4, 0) + 1);
/* 248 */           map1.put("beLateB", str4);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfLeaveEarly(List<Map> paramList, Map paramMap, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 269 */     HrmScheduleDiffDetLeaveEarlyManager hrmScheduleDiffDetLeaveEarlyManager = new HrmScheduleDiffDetLeaveEarlyManager();
/* 270 */     hrmScheduleDiffDetLeaveEarlyManager.setUser(this.user);
/* 271 */     hrmScheduleDiffDetLeaveEarlyManager.setSortForResult(false);
/* 272 */     List<Map> list = hrmScheduleDiffDetLeaveEarlyManager.getScheduleList(paramString1, paramString2, paramInt1, paramInt2, paramString3);
/* 273 */     Map map = null;
/*     */     
/* 275 */     String str1 = "";
/* 276 */     String str2 = "";
/* 277 */     int i = -1;
/*     */     
/* 279 */     Map<String, String> map1 = null;
/* 280 */     String str3 = "";
/* 281 */     String str4 = "";
/*     */     
/* 283 */     for (byte b = 0; b < list.size(); b++) {
/* 284 */       map = list.get(b);
/* 285 */       str1 = StringUtil.vString((String)map.get("resourceId"));
/* 286 */       str2 = StringUtil.vString((String)map.get("signTime"));
/*     */       
/* 288 */       i = Util.getIntValue((String)paramMap.get(str1), -1);
/* 289 */       if (i >= 0) {
/* 290 */         map1 = paramList.get(i);
/*     */         
/* 292 */         if (str2.compareTo(this.criticalOfAandBForPM) > 0) {
/* 293 */           str3 = (String)map1.get("leaveEarlyA");
/* 294 */           str3 = String.valueOf(Util.getIntValue(str3, 0) + 1);
/* 295 */           map1.put("leaveEarlyA", str3);
/*     */         } else {
/* 297 */           str4 = map1.get("leaveEarlyB");
/* 298 */           str4 = String.valueOf(Util.getIntValue(str4, 0) + 1);
/* 299 */           map1.put("leaveEarlyB", str4);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfAbsentFromWork(List<Map> paramList, Map paramMap, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 320 */     HrmScheduleDiffDetAbsentFromWorkManager hrmScheduleDiffDetAbsentFromWorkManager = new HrmScheduleDiffDetAbsentFromWorkManager();
/* 321 */     hrmScheduleDiffDetAbsentFromWorkManager.setUser(this.user);
/* 322 */     hrmScheduleDiffDetAbsentFromWorkManager.setSortForResult(false);
/* 323 */     List<Map> list = hrmScheduleDiffDetAbsentFromWorkManager.getScheduleList(paramString1, paramString2, paramInt1, paramInt2, paramString3);
/* 324 */     Map map = null;
/*     */     
/* 326 */     String str1 = "";
/* 327 */     String str2 = "";
/* 328 */     int i = -1;
/*     */     
/* 330 */     Map<String, String> map1 = null;
/* 331 */     String str3 = "";
/*     */     
/* 333 */     for (byte b = 0; b < list.size(); b++) {
/* 334 */       map = list.get(b);
/* 335 */       str1 = StringUtil.vString((String)map.get("resourceId"));
/* 336 */       str2 = StringUtil.vString(map.get("signType"), "1");
/* 337 */       i = Util.getIntValue((String)paramMap.get(str1), -1);
/* 338 */       if (i >= 0) {
/* 339 */         map1 = paramList.get(i);
/* 340 */         str3 = (String)map1.get("absentFromWork");
/*     */         
/* 342 */         str3 = String.valueOf(Util.getFloatValue(str3, 0.0F) + (str2.equals("2") ? 0.5D : 1.0D));
/* 343 */         map1.put("absentFromWork", str3);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateDataOfNoSign(List<Map> paramList, Map paramMap, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 362 */     HrmScheduleDiffDetNoSignManager hrmScheduleDiffDetNoSignManager = new HrmScheduleDiffDetNoSignManager();
/* 363 */     hrmScheduleDiffDetNoSignManager.setUser(this.user);
/* 364 */     hrmScheduleDiffDetNoSignManager.setSortForResult(false);
/* 365 */     List<Map> list = hrmScheduleDiffDetNoSignManager.getScheduleList(paramString1, paramString2, paramInt1, paramInt2, paramString3);
/* 366 */     Map map = null;
/*     */     
/* 368 */     String str1 = "";
/* 369 */     int i = -1;
/*     */     
/* 371 */     Map<String, String> map1 = null;
/* 372 */     String str2 = "";
/*     */     
/* 374 */     for (byte b = 0; b < list.size(); b++) {
/* 375 */       map = list.get(b);
/* 376 */       str1 = StringUtil.vString((String)map.get("resourceId"));
/*     */       
/* 378 */       i = Util.getIntValue((String)paramMap.get(str1), -1);
/* 379 */       if (i >= 0) {
/* 380 */         map1 = paramList.get(i);
/* 381 */         str2 = (String)map1.get("noSign");
/* 382 */         str2 = String.valueOf(Util.getIntValue(str2, 0) + 1);
/* 383 */         map1.put("noSign", str2);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void updateDataForAttFlow(List<Map> paramList, Map paramMap, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3, String paramString4) {
/* 389 */     HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 390 */     hrmScheduleDiffUtil.setUser(this.user);
/*     */     
/* 392 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 393 */     String str1 = "fromDate", str2 = "fromTime", str3 = "toDate", str4 = "toTime";
/* 394 */     switch (paramInt3) {
/*     */       case 3:
/* 396 */         str1 = "fromdate";
/* 397 */         str2 = "fromtime";
/* 398 */         str3 = "tilldate";
/* 399 */         str4 = "tilltime";
/* 400 */         hashMap.put("oType", " and t.otype = '0'");
/*     */         break;
/*     */     } 
/* 403 */     String str5 = " and (t." + str1 + " between '" + paramString1 + "' and '" + paramString2 + "' or t." + str3 + " between '" + paramString1 + "' and '" + paramString2 + "')";
/* 404 */     if (StringUtil.isNotNull(paramString1) && StringUtil.isNotNull(paramString2)) { hashMap.put("fromDate", str5); }
/* 405 */     else if (StringUtil.isNotNull(paramString1)) { hashMap.put("fromDate", " and t." + str1 + " >= '" + paramString1 + "'"); }
/* 406 */     else if (StringUtil.isNotNull(paramString2)) { hashMap.put("toDate", " and t." + str3 + " <= '" + paramString2 + "'"); }
/* 407 */      if (paramInt2 != 0) hashMap.put("departmentId", " and t.departmentId = " + paramInt2); 
/* 408 */     if (paramInt1 > 0) hashMap.put("subCompanyId", " and t.departmentId in (select id from hrmdepartment where subcompanyid1 = " + paramInt1 + ")"); 
/* 409 */     if (StringUtil.isNotNull(paramString3)) hashMap.put("resourceId", " and t.resourceId in (" + paramString3 + ")"); 
/* 410 */     RecordSet recordSet = new RecordSet();
/* 411 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(paramInt3, hashMap));
/* 412 */     Map<String, String> map = null;
/* 413 */     int i = -1;
/* 414 */     int j = 0;
/* 415 */     List list = (paramInt3 == 0) ? (new HrmLeaveTypeColorManager()).find("[map]subcompanyid:0;field002:1;field003:1") : null;
/* 416 */     String str6 = "", str7 = "", str8 = "";
/* 417 */     String str9 = "", str10 = "", str11 = "", str12 = "";
/* 418 */     while (recordSet.next()) {
/* 419 */       str6 = StringUtil.vString(recordSet.getString("resourceId"));
/* 420 */       i = StringUtil.parseToInt((String)paramMap.get(str6), -1);
/* 421 */       if (i < 0)
/*     */         continue; 
/* 423 */       str9 = StringUtil.vString(recordSet.getString(str1));
/* 424 */       str10 = StringUtil.vString(recordSet.getString(str2));
/* 425 */       str11 = StringUtil.vString(recordSet.getString(str3));
/* 426 */       str12 = StringUtil.vString(recordSet.getString(str4));
/* 427 */       if (paramInt3 == 0) j = StringUtil.parseToInt(recordSet.getString("newLeaveType"));
/*     */       
/* 429 */       if (str9.compareTo(paramString1) < 0) {
/* 430 */         str9 = paramString1;
/* 431 */         str10 = "00:00";
/*     */       } 
/*     */       
/* 434 */       if (str11.compareTo(paramString2) > 0) {
/* 435 */         str11 = paramString2;
/* 436 */         str12 = "23:59";
/*     */       } 
/*     */       
/* 439 */       if (paramInt3 == 3) {
/* 440 */         str8 = hrmScheduleDiffUtil.getTotalNotWorkHours(str9, str10, str11, str12, paramInt1);
/*     */       } else {
/* 442 */         str8 = hrmScheduleDiffUtil.getTotalWorkingDays(str9, str10, str11, str12, paramInt1);
/*     */       } 
/* 444 */       map = paramList.get(i);
/* 445 */       if (paramInt3 == 0) {
/* 446 */         if (list != null) {
/* 447 */           for (HrmLeaveTypeColor hrmLeaveTypeColor : list) {
/* 448 */             if (j == hrmLeaveTypeColor.getField004().intValue()) {
/* 449 */               str7 = StringUtil.vString(map.get("leave" + j));
/* 450 */               str7 = Util.getPointValue(Util.round(String.valueOf(Util.getDoubleValue(str7, 0.0D) + Util.getDoubleValue(str8, 0.0D)), 2));
/* 451 */               map.put("leave" + j, str7);
/*     */             } 
/*     */           } 
/*     */         }
/*     */         continue;
/*     */       } 
/* 457 */       str7 = StringUtil.vString(map.get(paramString4));
/* 458 */       str7 = Util.getPointValue(Util.round(String.valueOf(Util.getDoubleValue(str7, 0.0D) + Util.getDoubleValue(str8, 0.0D)), 2));
/* 459 */       map.put(paramString4, str7);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/schedulediff1512/HrmScheduleDiffManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */