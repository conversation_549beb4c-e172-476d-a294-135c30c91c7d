/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.StringTokenizer;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.docs.CustomFieldManager;
/*     */ import weaver.file.LogMan;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.CostCenterComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.finance.BankComInfo;
/*     */ import weaver.hrm.job.EducationLevelComInfo;
/*     */ import weaver.hrm.job.JobActivitiesComInfo;
/*     */ import weaver.hrm.job.JobCallComInfo;
/*     */ import weaver.hrm.job.JobGroupsComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.job.UseKindComInfo;
/*     */ import weaver.hrm.location.LocationComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpResourceDefine
/*     */ {
/*  41 */   RecordSet rs = new RecordSet();
/*  42 */   ArrayList al = new ArrayList();
/*  43 */   LogMan lm = LogMan.getInstance();
/*  44 */   int userlanguage = 7;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getShowOrder(int paramInt) {
/*  52 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  54 */     boolean bool = false;
/*  55 */     String str = "select colname,showorder from HrmResourceRpDefine where resourceid = " + paramInt + " order by id";
/*  56 */     this.rs.executeSql(str);
/*  57 */     while (this.rs.next()) {
/*  58 */       bool = true;
/*  59 */       hashMap.put(this.rs.getString("colname"), this.rs.getString("showorder"));
/*     */     } 
/*  61 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getHeader(String paramString) {
/*     */     try {
/*  71 */       ArrayList<String> arrayList = new ArrayList();
/*  72 */       String str = "select colname,header from HrmResourceRpDefine where showorder > 0 and resourceid = " + paramString + " order by showorder";
/*  73 */       this.rs.executeSql(str);
/*  74 */       while (this.rs.next()) {
/*  75 */         this.al.add(this.rs.getString(1));
/*  76 */         arrayList.add(this.rs.getString(2));
/*     */       } 
/*  78 */       return arrayList;
/*  79 */     } catch (Exception exception) {
/*  80 */       this.lm.writeLog(exception);
/*     */       
/*  82 */       return new ArrayList();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getContent(String paramString) {
/*     */     try {
/*  92 */       ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*  93 */       String str = "select * from HrmResource " + paramString;
/*  94 */       this.rs.executeSql(str);
/*  95 */       CustomFieldManager customFieldManager = new CustomFieldManager("HrmCustomFieldByInfoType", 1);
/*  96 */       customFieldManager.getCustomFields();
/*  97 */       while (this.rs.next()) {
/*  98 */         int i = Util.getIntValue(this.rs.getString("id"));
/*  99 */         ArrayList<String> arrayList1 = new ArrayList();
/* 100 */         for (byte b = 0; b < this.al.size(); b++) {
/* 101 */           String str1 = this.al.get(b);
/* 102 */           String str2 = "";
/* 103 */           int j = getDirectClass(str1);
/* 104 */           if (j == 0) {
/* 105 */             str2 = Util.null2String(this.rs.getString(str1));
/*     */           }
/* 107 */           if (j == 1) {
/* 108 */             String str3 = Util.null2String(this.rs.getString(str1));
/* 109 */             str2 = getContent(str1, str3);
/*     */           } 
/* 111 */           if (j == 2) {
/* 112 */             String str3 = this.rs.getString("id");
/* 113 */             str2 = getContentById(str1, str3);
/*     */           } 
/* 115 */           if (j == 3) {
/* 116 */             customFieldManager.getCustomFields(Util.getIntValue(str1.substring(5), 0));
/* 117 */             customFieldManager.next();
/* 118 */             String str3 = String.valueOf(customFieldManager.getId());
/* 119 */             String str4 = String.valueOf(customFieldManager.getHtmlType());
/* 120 */             String str5 = String.valueOf(customFieldManager.getType());
/* 121 */             String str6 = Util.null2String(this.rs.getString(str1));
/* 122 */             BrowserComInfo browserComInfo = new BrowserComInfo();
/* 123 */             RecordSet recordSet = new RecordSet();
/* 124 */             if (str4.equals("1") || str4.equals("2")) {
/* 125 */               str2 = str6;
/* 126 */             } else if (str4.equals("3")) {
/*     */               
/* 128 */               String str7 = browserComInfo.getBrowserurl(str5);
/* 129 */               String str8 = browserComInfo.getLinkurl(str5);
/* 130 */               String str9 = "";
/*     */ 
/*     */               
/* 133 */               if (str5.equals("2") || str5.equals("19")) {
/* 134 */                 str2 = str6;
/* 135 */               } else if (!str6.equals("")) {
/* 136 */                 String str10 = browserComInfo.getBrowsertablename(str5);
/* 137 */                 String str11 = browserComInfo.getBrowsercolumname(str5);
/* 138 */                 String str12 = browserComInfo.getBrowserkeycolumname(str5);
/* 139 */                 str = "";
/*     */                 
/* 141 */                 HashMap<Object, Object> hashMap = new HashMap<>();
/*     */                 
/* 143 */                 if (str5.equals("17") || str5.equals("18") || str5.equals("27") || str5.equals("37") || str5.equals("56") || str5.equals("57") || str5.equals("65")) {
/* 144 */                   str = "select " + str12 + "," + str11 + " from " + str10 + " where " + str12 + " in( " + str6 + ")";
/*     */                 } else {
/* 146 */                   str = "select " + str12 + "," + str11 + " from " + str10 + " where " + str12 + "=" + str6;
/*     */                 } 
/*     */                 
/* 149 */                 recordSet.executeSql(str);
/* 150 */                 while (recordSet.next()) {
/* 151 */                   str9 = Util.null2String(recordSet.getString(1));
/*     */                   
/* 153 */                   String str14 = recordSet.getString(2);
/* 154 */                   if (!str8.equals("")) {
/*     */                     
/* 156 */                     hashMap.put(String.valueOf(str9), "<a href='" + str8 + str9 + "'>" + str14 + "</a> ");
/*     */                     continue;
/*     */                   } 
/* 159 */                   hashMap.put(String.valueOf(str9), str14);
/*     */                 } 
/*     */                 
/* 162 */                 StringTokenizer stringTokenizer = new StringTokenizer(str6, ",");
/* 163 */                 String str13 = "";
/* 164 */                 while (stringTokenizer.hasMoreTokens()) {
/* 165 */                   str13 = stringTokenizer.nextToken();
/*     */                   
/* 167 */                   if (str13.length() > 0 && hashMap.get(str13) != null) {
/* 168 */                     str2 = str2 + hashMap.get(str13);
/*     */                   
/*     */                   }
/*     */                 }
/*     */               
/*     */               }
/*     */             
/*     */             }
/* 176 */             else if (customFieldManager.getHtmlType().equals("4")) {
/* 177 */               str2 = this.rs.getString(str1).equals("1") ? ("<input type=checkbox value=1 name=" + str1 + " checked>") : ("<input type=checkbox value=1 name=" + str1 + " disabled >");
/* 178 */             } else if (customFieldManager.getHtmlType().equals("5")) {
/* 179 */               customFieldManager.getSelectItem(customFieldManager.getId());
/* 180 */               while (customFieldManager.nextSelect()) {
/* 181 */                 if (customFieldManager.getSelectValue().equals(this.rs.getString(str1))) {
/* 182 */                   str2 = customFieldManager.getSelectName();
/*     */                   
/*     */                   break;
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/* 189 */           arrayList1.add(str2);
/*     */         } 
/* 191 */         arrayList.add(arrayList1);
/*     */       } 
/* 193 */       return arrayList;
/* 194 */     } catch (Exception exception) {
/* 195 */       this.lm.writeLog(exception);
/*     */       
/* 197 */       return new ArrayList();
/*     */     } 
/*     */   }
/*     */   private int getDirectClass(String paramString) {
/* 201 */     if (paramString.equals("jobgroup") || paramString.equals("jobactivity") || paramString.equals("roles") || paramString.equals("age")) {
/* 202 */       return 2;
/*     */     }
/* 204 */     if (paramString.equals("healthinfo") || paramString.equals("maritalstatus") || paramString.equals("sex") || paramString.equals("departmentid") || paramString.equals("subcompanyid1") || paramString.equals("costcenterid") || paramString.equals("status") || paramString.equals("jobtitle") || paramString.equals("jobcall") || paramString.equals("managerid") || paramString.equals("assistantid") || paramString.equals("educationlevel") || paramString.equals("usekind") || paramString.equals("islabouunion") || paramString.equals("bankid1") || paramString.equals("locationid")) {
/* 205 */       return 1;
/*     */     }
/* 207 */     if (paramString.indexOf("field") > -1 && 
/* 208 */       !paramString.equals("datefield1") && !paramString.equals("datefield2") && !paramString.equals("datefield3") && !paramString.equals("datefield4") && !paramString.equals("datefield5") && !paramString.equals("numberfield1") && !paramString.equals("numberfield2") && !paramString.equals("numberfield3") && !paramString.equals("numberfield4") && !paramString.equals("numberfield5") && !paramString.equals("textfield1") && !paramString.equals("textfield2") && !paramString.equals("textfield3") && !paramString.equals("textfield4") && !paramString.equals("textfield5") && !paramString.equals("tinyintfield1") && !paramString.equals("tinyintfield2") && !paramString.equals("tinyintfield3") && !paramString.equals("tinyintfield4") && !paramString.equals("tinyintfield5")) {
/* 209 */       return 3;
/*     */     }
/*     */     
/* 212 */     return 0;
/*     */   }
/*     */   
/*     */   private String getContent(String paramString1, String paramString2) {
/* 216 */     String str = "";
/*     */     try {
/* 218 */       if (paramString1.equals("sex")) {
/* 219 */         if (paramString2.equals("0")) {
/* 220 */           str = SystemEnv.getHtmlLabelName(28473, this.userlanguage);
/*     */         } else {
/* 222 */           str = SystemEnv.getHtmlLabelName(28474, this.userlanguage);
/*     */         } 
/* 224 */         return str;
/*     */       } 
/* 226 */       if (paramString1.equals("maritalstatus")) {
/* 227 */         if (paramString2.equals("0")) {
/* 228 */           str = SystemEnv.getHtmlLabelName(470, this.userlanguage);
/*     */         }
/* 230 */         else if (paramString2.equals("1")) {
/* 231 */           str = SystemEnv.getHtmlLabelName(471, this.userlanguage);
/*     */         }
/* 233 */         else if (paramString2.equals("2")) {
/* 234 */           str = SystemEnv.getHtmlLabelName(472, this.userlanguage);
/*     */         } 
/* 236 */         return str;
/*     */       } 
/* 238 */       if (paramString1.equals("departmentid")) {
/* 239 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 240 */         str = departmentComInfo.getDepartmentname(paramString2);
/* 241 */         return str;
/*     */       } 
/* 243 */       if (paramString1.equals("subcompanyid1")) {
/* 244 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 245 */         str = subCompanyComInfo.getSubCompanyname(paramString2);
/* 246 */         return str;
/*     */       } 
/* 248 */       if (paramString1.equals("costcenterid")) {
/* 249 */         CostCenterComInfo costCenterComInfo = new CostCenterComInfo();
/* 250 */         str = costCenterComInfo.getCostCentername(paramString2);
/* 251 */         return str;
/*     */       } 
/* 253 */       if (paramString1.equals("status")) {
/* 254 */         if (paramString2.equals("0")) {
/* 255 */           str = SystemEnv.getHtmlLabelName(15710, this.userlanguage);
/*     */         }
/* 257 */         if (paramString2.equals("1")) {
/* 258 */           str = SystemEnv.getHtmlLabelName(15711, this.userlanguage);
/*     */         }
/* 260 */         if (paramString2.equals("2")) {
/* 261 */           str = SystemEnv.getHtmlLabelName(480, this.userlanguage);
/*     */         }
/* 263 */         if (paramString2.equals("3")) {
/* 264 */           str = SystemEnv.getHtmlLabelName(15844, this.userlanguage);
/*     */         }
/* 266 */         if (paramString2.equals("4")) {
/* 267 */           str = SystemEnv.getHtmlLabelName(6094, this.userlanguage);
/*     */         }
/* 269 */         if (paramString2.equals("5")) {
/* 270 */           str = SystemEnv.getHtmlLabelName(6091, this.userlanguage);
/*     */         }
/* 272 */         if (paramString2.equals("6")) {
/* 273 */           str = SystemEnv.getHtmlLabelName(6092, this.userlanguage);
/*     */         }
/* 275 */         if (paramString2.equals("7")) {
/* 276 */           str = SystemEnv.getHtmlLabelName(2245, this.userlanguage);
/*     */         }
/* 278 */         return str;
/*     */       } 
/* 280 */       if (paramString1.equals("healthinfo")) {
/* 281 */         if (paramString2.equals("0")) {
/* 282 */           str = SystemEnv.getHtmlLabelName(824, this.userlanguage);
/*     */         }
/* 284 */         if (paramString2.equals("1")) {
/* 285 */           str = SystemEnv.getHtmlLabelName(821, this.userlanguage);
/*     */         }
/* 287 */         if (paramString2.equals("2")) {
/* 288 */           str = SystemEnv.getHtmlLabelName(154, this.userlanguage);
/*     */         }
/* 290 */         if (paramString2.equals("3")) {
/* 291 */           str = SystemEnv.getHtmlLabelName(463, this.userlanguage);
/*     */         }
/*     */         
/* 294 */         return str;
/*     */       } 
/* 296 */       if (paramString1.equals("jobtitle")) {
/* 297 */         JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 298 */         str = jobTitlesComInfo.getJobTitlesname(paramString2);
/* 299 */         return str;
/*     */       } 
/* 301 */       if (paramString1.equals("jobcall")) {
/* 302 */         JobCallComInfo jobCallComInfo = new JobCallComInfo();
/* 303 */         str = jobCallComInfo.getJobCallname(paramString2);
/* 304 */         return str;
/*     */       } 
/* 306 */       if (paramString1.equals("managerid") || paramString1.equals("assistantid")) {
/* 307 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 308 */         str = resourceComInfo.getResourcename(paramString2);
/* 309 */         return str;
/*     */       } 
/* 311 */       if (paramString1.equals("educationlevel")) {
/* 312 */         EducationLevelComInfo educationLevelComInfo = new EducationLevelComInfo();
/* 313 */         str = educationLevelComInfo.getEducationLevelname(paramString2);
/* 314 */         return str;
/*     */       } 
/* 316 */       if (paramString1.equals("usekind")) {
/* 317 */         UseKindComInfo useKindComInfo = new UseKindComInfo();
/* 318 */         str = useKindComInfo.getUseKindname(paramString2);
/* 319 */         return str;
/*     */       } 
/* 321 */       if (paramString1.equals("islabouunion")) {
/* 322 */         if (paramString2.equals("1")) {
/* 323 */           str = SystemEnv.getHtmlLabelName(163, this.userlanguage);
/*     */         } else {
/* 325 */           str = SystemEnv.getHtmlLabelName(161, this.userlanguage);
/*     */         } 
/* 327 */         return str;
/*     */       } 
/* 329 */       if (paramString1.equals("bankid1")) {
/* 330 */         BankComInfo bankComInfo = new BankComInfo();
/* 331 */         str = bankComInfo.getBankname(paramString2);
/* 332 */         return str;
/*     */       } 
/* 334 */       if (paramString1.equals("locationid")) {
/* 335 */         LocationComInfo locationComInfo = new LocationComInfo();
/* 336 */         str = locationComInfo.getLocationname(paramString2);
/* 337 */         return str;
/*     */       } 
/* 339 */     } catch (Exception exception) {
/* 340 */       this.lm.writeLog(exception);
/*     */     } 
/* 342 */     return str;
/*     */   }
/*     */   
/*     */   private String getContentById(String paramString1, String paramString2) {
/*     */     try {
/* 347 */       String str = "";
/* 348 */       RecordSet recordSet = new RecordSet();
/* 349 */       if (paramString1.equals("jobgroup")) {
/* 350 */         String str1 = "select jobtitle from HrmResource where id = " + paramString2;
/* 351 */         recordSet.executeSql(str1);
/* 352 */         recordSet.next();
/* 353 */         String str2 = Util.null2String(recordSet.getString(1));
/* 354 */         JobActivitiesComInfo jobActivitiesComInfo = new JobActivitiesComInfo();
/* 355 */         JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 356 */         JobGroupsComInfo jobGroupsComInfo = new JobGroupsComInfo();
/* 357 */         String str3 = jobTitlesComInfo.getJobactivityid(str2);
/* 358 */         String str4 = jobActivitiesComInfo.getJobgroupid(str3);
/* 359 */         str = jobGroupsComInfo.getJobGroupsname(str4);
/* 360 */         return str;
/*     */       } 
/* 362 */       if (paramString1.equals("jobactivity")) {
/* 363 */         String str1 = "select jobtitle from HrmResource where id = " + paramString2;
/* 364 */         recordSet.executeSql(str1);
/* 365 */         recordSet.next();
/* 366 */         String str2 = Util.null2String(recordSet.getString(1));
/* 367 */         JobActivitiesComInfo jobActivitiesComInfo = new JobActivitiesComInfo();
/* 368 */         JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 369 */         String str3 = jobTitlesComInfo.getJobactivityid(str2);
/* 370 */         str = jobActivitiesComInfo.getJobActivitiesname(str3);
/* 371 */         return str;
/*     */       } 
/* 373 */       if (paramString1.equals("age")) {
/* 374 */         Calendar calendar = Calendar.getInstance();
/* 375 */         String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/* 376 */         String str2 = "select birthday from HrmResource where id = " + paramString2;
/* 377 */         recordSet.executeSql(str2);
/* 378 */         recordSet.next();
/* 379 */         String str3 = Util.null2String(recordSet.getString(1));
/* 380 */         str = "" + Util.yearDiff(str3, str1);
/* 381 */         return str;
/*     */       } 
/* 383 */       if (paramString1.equals("roles")) {
/*     */         
/* 385 */         String str1 = " SELECT resourceid, roleid , rolelevel FROM (  SELECT a.id AS resourceid, b.roleid , b.rolelevel FROM HrmResource a, HrmRoleMembers b  WHERE (a.id=b.resourceid and b.resourcetype=1)  UNION ALL  SELECT a.id AS resourceid, b.roleid , b.rolelevel FROM HrmResourceManager a, HrmRoleMembers b  WHERE (a.id=b.resourceid and b.resourcetype IN(7,8))  UNION ALL  SELECT a.id AS resourceid, b.roleid , b.rolelevel FROM HrmResource a, HrmRoleMembers b  WHERE (a.subcompanyid1 = b.resourceid AND a.seclevel>=b.seclevelfrom AND a.seclevel<=b.seclevelto AND b.resourcetype=2)  UNION ALL  SELECT a.id AS resourceid, b.roleid , b.rolelevel FROM HrmResource a, HrmRoleMembers b  WHERE (a.departmentid = b.resourceid AND a.seclevel>=b.seclevelfrom AND a.seclevel<=b.seclevelto AND b.resourcetype=3)  UNION ALL  SELECT a.id AS resourceid, b.roleid , b.rolelevel FROM HrmResource a, HrmRoleMembers b  WHERE  (a.jobtitle = b.resourceid AND b.resourcetype=5 AND (b.jobtitlelevel=1 OR (b.jobtitlelevel=2 AND a.subcompanyid1 IN(b.subdepid)) OR (b.jobtitlelevel=3 AND a.departmentid IN(b.subdepid))))) t where resourceid = " + paramString2;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 401 */         this.rs.executeSql(str1);
/* 402 */         while (recordSet.next()) {
/* 403 */           RolesComInfo rolesComInfo = new RolesComInfo();
/* 404 */           String str2 = rolesComInfo.getRolesname(recordSet.getString("roleid"));
/* 405 */           String str3 = recordSet.getString("rolelevel");
/* 406 */           String str4 = "";
/* 407 */           if (str3.equals("2")) str4 = SystemEnv.getHtmlLabelName(140, this.userlanguage); 
/* 408 */           if (str3.equals("1")) str4 = SystemEnv.getHtmlLabelName(141, this.userlanguage); 
/* 409 */           if (str3.equals("0")) str4 = SystemEnv.getHtmlLabelName(124, this.userlanguage); 
/* 410 */           str = str + str2 + "-" + str4 + ",";
/*     */         } 
/* 412 */         if (!str.equals("")) str = str.substring(0, str.length() - 1); 
/* 413 */         return str;
/*     */       } 
/* 415 */     } catch (Exception exception) {
/* 416 */       this.lm.writeLog(exception);
/*     */     } 
/* 418 */     return "";
/*     */   }
/*     */   public int getUserlanguage() {
/* 421 */     return this.userlanguage;
/*     */   }
/*     */   
/*     */   public void setUserlanguage(int paramInt) {
/* 425 */     this.userlanguage = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpResourceDefine.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */