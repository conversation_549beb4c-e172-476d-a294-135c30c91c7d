/*     */ package weaver.hrm.report;
/*     */ 
/*     */ import java.util.Enumeration;
/*     */ import java.util.Hashtable;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.LogMan;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.career.CareerPlanComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RpCareerApplyManager
/*     */   extends BaseBean
/*     */ {
/*  31 */   LogMan lm = LogMan.getInstance();
/*  32 */   RecordSet rs = new RecordSet();
/*  33 */   Hashtable show = new Hashtable<>();
/*  34 */   Hashtable header = new Hashtable<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getResultByContent(int paramInt1, String paramString, int paramInt2, boolean paramBoolean, User paramUser) {
/*  45 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  46 */     if (paramInt1 == 1) {
/*  47 */       hashtable = getResultByJobTitle(paramString, paramBoolean);
/*     */     }
/*  49 */     if (paramInt1 == 2) {
/*  50 */       hashtable = getResultByCategory(paramString, paramBoolean, paramUser);
/*     */     }
/*  52 */     if (paramInt1 == 3) {
/*  53 */       hashtable = getResultByEduLevel(paramString, paramBoolean);
/*     */     }
/*  55 */     if (paramInt1 == 4) {
/*  56 */       hashtable = getResultBySalary(paramString, paramInt2, paramBoolean);
/*     */     }
/*  58 */     if (paramInt1 == 5) {
/*  59 */       hashtable = getResultByWorkTime(paramString, paramInt2, paramBoolean);
/*     */     }
/*  61 */     return hashtable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getResultByColRow(int paramInt1, int paramInt2, String paramString, int paramInt3, int paramInt4, User paramUser) {
/*  74 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  75 */     if (paramInt2 == 1) {
/*  76 */       if (paramInt1 == 1) {
/*  77 */         return getJobTitleByCategory(paramString, paramUser);
/*     */       }
/*  79 */       if (paramInt1 == 2) {
/*  80 */         return getJobTitleByEduLevel(paramString);
/*     */       }
/*  82 */       if (paramInt1 == 3) {
/*  83 */         return getJobTitleBySalary(paramString, paramInt3);
/*     */       }
/*  85 */       if (paramInt1 == 4) {
/*  86 */         return getJobTitleByWorkTime(paramString, paramInt3);
/*     */       }
/*     */     } 
/*  89 */     if (paramInt2 == 2) {
/*  90 */       if (paramInt1 == 1) {
/*  91 */         return getPlanByCategory(paramString, paramUser);
/*     */       }
/*  93 */       if (paramInt1 == 2) {
/*  94 */         return getPlanByEduLevel(paramString);
/*     */       }
/*  96 */       if (paramInt1 == 3) {
/*  97 */         return getPlanBySalary(paramString, paramInt3);
/*     */       }
/*  99 */       if (paramInt1 == 4) {
/* 100 */         return getPlanByWorkTime(paramString, paramInt3);
/*     */       }
/*     */     } 
/* 103 */     if (paramInt2 == 3) {
/* 104 */       if (paramInt1 == 1) {
/* 105 */         return getMonByCategory(paramString, paramInt4, paramUser);
/*     */       }
/* 107 */       if (paramInt1 == 2) {
/* 108 */         return getMonByEduLevel(paramString, paramInt4);
/*     */       }
/* 110 */       if (paramInt1 == 3) {
/* 111 */         return getMonBySalary(paramString, paramInt3, paramInt4);
/*     */       }
/* 113 */       if (paramInt1 == 4) {
/* 114 */         return getMonByWorkTime(paramString, paramInt3, paramInt4);
/*     */       }
/*     */     } 
/* 117 */     return hashtable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Hashtable getResultByJobTitle(String paramString, boolean paramBoolean) {
/* 127 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 129 */       RecordSet recordSet = new RecordSet();
/* 130 */       String str = "select distinct(jobtitle),jobtitlename from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmJobTitles where t2.applyid = t1.id and jobtitle=HrmJobTitles.id " + paramString + " order by jobtitle ";
/* 131 */       if (paramBoolean) {
/* 132 */         str = "select distinct(jobtitle),jobtitlename from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmJobTitles,HrmCareerInvite t3 where t2.applyid = t1.id and jobtitle=HrmJobTitles.id " + paramString + " order by jobtitle ";
/*     */       }
/* 134 */       this.rs.executeSql(str);
/* 135 */       while (this.rs.next()) {
/* 136 */         int i = Util.getIntValue(this.rs.getString("jobtitle"));
/* 137 */         String str1 = Util.null2String(this.rs.getString("jobtitlename"));
/* 138 */         recordSet.executeSql("SELECT b.id, b.jobtitlename FROM HrmCareerInvite a, HrmJobTitles b where a.careername=b.id and a.id=" + i);
/* 139 */         recordSet.next();
/* 140 */         int j = recordSet.getInt(1);
/* 141 */         str1 = Util.null2String(recordSet.getString("jobtitlename"));
/* 142 */         this.show.put(new Integer(j), str1);
/* 143 */         String str2 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and jobtitle = " + i + paramString;
/* 144 */         if (paramBoolean) {
/* 145 */           str2 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 ,HrmCareerInvite t3 where t2.applyid = t1.id and jobtitle = " + i + paramString;
/*     */         }
/* 147 */         recordSet.executeSql(str2);
/* 148 */         recordSet.next();
/* 149 */         int k = recordSet.getInt(1);
/*     */         
/* 151 */         hashtable.put(new Integer(j), "" + k);
/*     */       } 
/* 153 */     } catch (Exception exception) {
/* 154 */       writeLog(exception);
/*     */     } 
/* 156 */     return hashtable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Hashtable getResultByCategory(String paramString, boolean paramBoolean, User paramUser) {
/* 166 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 167 */     this.show.put(new Integer(0), SystemEnv.getHtmlLabelName(134, paramUser.getLanguage()));
/* 168 */     this.show.put(new Integer(1), SystemEnv.getHtmlLabelName(1830, paramUser.getLanguage()));
/* 169 */     this.show.put(new Integer(2), SystemEnv.getHtmlLabelName(1831, paramUser.getLanguage()));
/* 170 */     this.show.put(new Integer(3), SystemEnv.getHtmlLabelName(1832, paramUser.getLanguage()));
/* 171 */     this.show.put(new Integer(4), SystemEnv.getHtmlLabelName(811, paramUser.getLanguage()));
/*     */     try {
/* 173 */       String str = "";
/* 174 */       str = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString;
/* 175 */       if (paramBoolean) {
/* 176 */         str = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id " + paramString;
/*     */       }
/* 178 */       this.rs.executeSql(str);
/* 179 */       this.rs.next();
/* 180 */       int i = this.rs.getInt(1);
/* 181 */       int j = 0;
/* 182 */       for (byte b = 0; b < 4; b++) {
/* 183 */         String str1 = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and category = '" + b + "' " + paramString;
/* 184 */         if (paramBoolean) {
/* 185 */           str1 = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3  where t2.applyid = t1.id and category = '" + b + "' " + paramString;
/*     */         }
/* 187 */         this.rs.executeSql(str1);
/* 188 */         this.rs.next();
/* 189 */         int k = this.rs.getInt(1);
/* 190 */         j += k;
/* 191 */         hashtable.put(new Integer(b), "" + k);
/*     */       } 
/* 193 */       hashtable.put(new Integer(4), "" + (i - j));
/* 194 */       return hashtable;
/* 195 */     } catch (Exception exception) {
/* 196 */       writeLog(exception);
/*     */       
/* 198 */       return hashtable;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Hashtable getResultByEduLevel(String paramString, boolean paramBoolean) {
/* 208 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 210 */       RecordSet recordSet = new RecordSet();
/* 211 */       String str = "select distinct(educationlevel),name from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmEducationLevel where t2.applyid = t1.id and educationlevel=HrmEducationLevel.id " + paramString + " order by educationlevel ";
/* 212 */       if (paramBoolean) {
/* 213 */         str = "select distinct(educationlevel),name from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmEducationLevel,HrmCareerInvite t3 where t2.applyid = t1.id and  educationlevel=HrmEducationLevel.id " + paramString + " order by educationlevel ";
/*     */       }
/* 215 */       this.rs.executeSql(str);
/* 216 */       while (this.rs.next()) {
/* 217 */         int i = Util.getIntValue(this.rs.getString("educationlevel"));
/* 218 */         String str1 = Util.null2String(this.rs.getString("name"));
/* 219 */         this.show.put(new Integer(i), str1);
/* 220 */         String str2 = "select count(t1.id) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and  educationlevel = " + i + paramString;
/* 221 */         if (paramBoolean) {
/* 222 */           str2 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id and educationlevel = " + i + paramString;
/*     */         }
/* 224 */         recordSet.executeSql(str2);
/* 225 */         recordSet.next();
/* 226 */         int j = recordSet.getInt(1);
/* 227 */         hashtable.put(new Integer(i), "" + j);
/*     */       } 
/* 229 */       return hashtable;
/* 230 */     } catch (Exception exception) {
/* 231 */       writeLog(exception);
/*     */       
/* 233 */       return hashtable;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Hashtable getResultBySalary(String paramString, int paramInt, boolean paramBoolean) {
/* 244 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 246 */       if (paramInt == 0) {
/* 247 */         return hashtable;
/*     */       }
/* 249 */       int i = 0;
/* 250 */       String str = "select max(salaryneed) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString;
/* 251 */       if (paramBoolean) {
/* 252 */         str = "select max(salaryneed) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id " + paramString;
/*     */       }
/* 254 */       this.rs.executeSql(str);
/* 255 */       this.rs.next();
/* 256 */       i = this.rs.getInt(1);
/* 257 */       byte b = 0; int j;
/* 258 */       for (j = 0; j <= i; j += paramInt) {
/* 259 */         String str1 = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and salaryneed >=" + j + " and salaryneed<" + (j + paramInt) + paramString + " ";
/* 260 */         if (paramBoolean) {
/* 261 */           str1 = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id and salaryneed >=" + j + " and salaryneed<" + (j + paramInt) + paramString + " ";
/*     */         }
/* 263 */         this.rs.executeSql(str1);
/* 264 */         this.rs.next();
/* 265 */         int k = this.rs.getInt(1);
/* 266 */         if (k != 0) {
/* 267 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt) + Util.toScreen("元", 7, "0"));
/* 268 */           hashtable.put(new Integer(b), "" + k);
/* 269 */           b++;
/*     */         } 
/* 271 */       }  return hashtable;
/* 272 */     } catch (Exception exception) {
/* 273 */       writeLog(exception);
/*     */       
/* 275 */       return hashtable;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Hashtable getResultByWorkTime(String paramString, int paramInt, boolean paramBoolean) {
/* 285 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 287 */       if (paramInt == 0) {
/* 288 */         return hashtable;
/*     */       }
/* 290 */       int i = 0;
/* 291 */       String str = "select max(worktime) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString;
/* 292 */       if (paramBoolean) {
/* 293 */         str = "select max(worktime) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id " + paramString;
/*     */       }
/* 295 */       this.rs.executeSql(str);
/* 296 */       this.rs.next();
/* 297 */       i = this.rs.getInt(1);
/* 298 */       byte b = 0; int j;
/* 299 */       for (j = 0; j <= i; j += paramInt) {
/* 300 */         String str1 = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and worktime >=" + j + " and worktime<" + (j + paramInt) + paramString + " ";
/* 301 */         if (paramBoolean) {
/* 302 */           str1 = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id and worktime >=" + j + " and worktime<" + (j + paramInt) + paramString + " ";
/*     */         }
/* 304 */         this.rs.executeSql(str1);
/* 305 */         this.rs.next();
/* 306 */         int k = this.rs.getInt(1);
/* 307 */         if (k != 0) {
/* 308 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt) + Util.toScreen("年", 7, "0"));
/* 309 */           hashtable.put(new Integer(b), "" + k);
/* 310 */           b++;
/*     */         } 
/* 312 */       }  return hashtable;
/* 313 */     } catch (Exception exception) {
/* 314 */       writeLog(exception);
/*     */       
/* 316 */       return hashtable;
/*     */     } 
/*     */   }
/*     */   private Hashtable getJobTitleByCategory(String paramString, User paramUser) {
/* 320 */     this.show.put(new Integer(0), SystemEnv.getHtmlLabelName(134, paramUser.getLanguage()));
/* 321 */     this.show.put(new Integer(1), SystemEnv.getHtmlLabelName(1830, paramUser.getLanguage()));
/* 322 */     this.show.put(new Integer(2), SystemEnv.getHtmlLabelName(1831, paramUser.getLanguage()));
/* 323 */     this.show.put(new Integer(3), SystemEnv.getHtmlLabelName(1832, paramUser.getLanguage()));
/* 324 */     this.show.put(new Integer(4), SystemEnv.getHtmlLabelName(811, paramUser.getLanguage()));
/* 325 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 327 */       RecordSet recordSet1 = new RecordSet();
/* 328 */       RecordSet recordSet2 = new RecordSet();
/* 329 */       String str = "select distinct(jobtitle),jobtitlename from HrmCareerApply t1,HrmJobTitles where jobtitle = HrmJobTitles.id " + paramString;
/* 330 */       recordSet1.executeSql(str);
/* 331 */       while (recordSet1.next()) {
/* 332 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 333 */         String str1 = recordSet1.getString("jobtitle");
/*     */         
/* 335 */         recordSet2.executeSql("SELECT b.id, b.jobtitlename FROM HrmCareerInvite a, HrmJobTitles b where a.careername=b.id and a.id=" + str1);
/* 336 */         recordSet2.next();
/* 337 */         int i = recordSet2.getInt(1);
/* 338 */         String str2 = Util.null2String(recordSet2.getString("jobtitlename"));
/*     */         
/* 340 */         this.header.put(Integer.valueOf(i), str2);
/* 341 */         String str3 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id  and jobtitle = " + str1 + paramString;
/* 342 */         this.rs.executeSql(str3);
/* 343 */         this.rs.next();
/* 344 */         int j = this.rs.getInt(1);
/* 345 */         int k = 0;
/* 346 */         for (byte b = 0; b < 4; b++) {
/* 347 */           String str4 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and t2.category = " + b + " and jobtitle = " + str1 + paramString;
/* 348 */           this.rs.executeSql(str4);
/* 349 */           this.rs.next();
/* 350 */           int m = this.rs.getInt(1);
/* 351 */           k += m;
/* 352 */           hashtable1.put(new Integer(b), "" + m);
/*     */         } 
/* 354 */         hashtable1.put(new Integer(4), "" + (j - k));
/* 355 */         hashtable.put(Integer.valueOf(i), hashtable1);
/*     */       } 
/* 357 */       return hashtable;
/* 358 */     } catch (Exception exception) {
/* 359 */       writeLog(exception);
/*     */       
/* 361 */       return hashtable;
/*     */     } 
/*     */   }
/*     */   private Hashtable getJobTitleByEduLevel(String paramString) {
/* 365 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 367 */       RecordSet recordSet = new RecordSet();
/* 368 */       String str1 = "select distinct(educationlevel),name from HrmEducationLevel, HrmCareerApply t1 where t1.educationlevel = HrmEducationLevel.id " + paramString;
/* 369 */       this.rs.executeSql(str1);
/* 370 */       while (this.rs.next()) {
/* 371 */         this.show.put(Integer.valueOf(this.rs.getString("educationlevel")), Util.null2String(this.rs.getString("name")));
/*     */       }
/* 373 */       this.show.put(new Integer(0), Util.toScreen("" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "", 7, "0"));
/* 374 */       String str2 = "select distinct(jobtitle),jobtitlename from HrmCareerApply t1,HrmJobTitles where jobtitle = HrmJobTitles.id " + paramString;
/* 375 */       recordSet.executeSql(str2);
/* 376 */       while (recordSet.next()) {
/* 377 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 378 */         String str = recordSet.getString("jobtitle");
/* 379 */         this.header.put(Integer.valueOf(str), recordSet.getString("jobtitlename"));
/* 380 */         Enumeration<Integer> enumeration = this.show.keys();
/* 381 */         while (enumeration.hasMoreElements()) {
/* 382 */           Integer integer = enumeration.nextElement();
/* 383 */           String str3 = "select count(t1.id) from HrmCareerApply t1 where educationlevel = " + integer + " and jobtitle = " + str + paramString;
/* 384 */           this.rs.executeSql(str3);
/* 385 */           this.rs.next();
/* 386 */           int i = this.rs.getInt(1);
/* 387 */           hashtable1.put(integer, "" + i);
/*     */         } 
/* 389 */         hashtable.put(Integer.valueOf(str), hashtable1);
/*     */       } 
/* 391 */       return hashtable;
/* 392 */     } catch (Exception exception) {
/* 393 */       writeLog(exception);
/*     */       
/* 395 */       return hashtable;
/*     */     } 
/*     */   }
/*     */   private Hashtable getJobTitleBySalary(String paramString, int paramInt) {
/* 399 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 401 */       if (paramInt == 0) {
/* 402 */         return hashtable;
/*     */       }
/* 404 */       RecordSet recordSet = new RecordSet();
/* 405 */       int i = 0;
/* 406 */       String str1 = "select max(salaryneed) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString;
/* 407 */       this.rs.executeSql(str1);
/* 408 */       this.rs.next();
/* 409 */       i = this.rs.getInt(1);
/* 410 */       byte b = 0; int j;
/* 411 */       for (j = 0; j <= i; j += paramInt) {
/* 412 */         String str = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and salaryneed >=" + j + " and salaryneed<" + (j + paramInt) + paramString + " ";
/* 413 */         this.rs.executeSql(str);
/* 414 */         this.rs.next();
/* 415 */         int k = this.rs.getInt(1);
/* 416 */         if (k != 0) {
/* 417 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt) + Util.toScreen("元", 7, "0"));
/* 418 */           b++;
/*     */         } 
/* 420 */       }  String str2 = "select distinct(jobtitle),jobtitlename from HrmCareerApply t1,HrmJobTitles where jobtitle = HrmJobTitles.id " + paramString;
/* 421 */       recordSet.executeSql(str2);
/* 422 */       while (recordSet.next()) {
/* 423 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 424 */         String str = recordSet.getString("jobtitle");
/* 425 */         this.header.put(Integer.valueOf(str), recordSet.getString("jobtitlename"));
/* 426 */         Enumeration<Integer> enumeration = this.show.keys();
/* 427 */         while (enumeration.hasMoreElements()) {
/* 428 */           Integer integer = enumeration.nextElement();
/* 429 */           String str3 = (String)this.show.get(integer);
/* 430 */           int k = str3.indexOf("-");
/* 431 */           int m = Util.getIntValue(str3.substring(0, k));
/* 432 */           int n = Util.getIntValue(str3.substring(k + 1, str3.length() - 2));
/* 433 */           String str4 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and jobtitle=" + str + " and salaryneed>=" + m + " and salaryneed <=" + n + paramString;
/* 434 */           this.rs.executeSql(str4);
/* 435 */           this.rs.next();
/* 436 */           int i1 = this.rs.getInt(1);
/* 437 */           hashtable1.put(integer, "" + i1);
/*     */         } 
/* 439 */         hashtable.put(Integer.valueOf(str), hashtable1);
/*     */       } 
/* 441 */       return hashtable;
/* 442 */     } catch (Exception exception) {
/* 443 */       writeLog(exception);
/*     */       
/* 445 */       return hashtable;
/*     */     } 
/*     */   } private Hashtable getJobTitleByWorkTime(String paramString, int paramInt) {
/* 448 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 450 */       if (paramInt == 0) {
/* 451 */         return hashtable;
/*     */       }
/* 453 */       RecordSet recordSet = new RecordSet();
/* 454 */       int i = 0;
/* 455 */       String str1 = "select max(worktime) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString;
/* 456 */       this.rs.executeSql(str1);
/* 457 */       this.rs.next();
/* 458 */       i = this.rs.getInt(1);
/* 459 */       byte b = 0; int j;
/* 460 */       for (j = 0; j <= i; j += paramInt) {
/* 461 */         String str = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and worktime >=" + j + " and worktime<" + (j + paramInt) + paramString + " ";
/* 462 */         this.rs.executeSql(str);
/* 463 */         this.rs.next();
/* 464 */         int k = this.rs.getInt(1);
/* 465 */         if (k != 0) {
/* 466 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt) + Util.toScreen("年", 7, "0"));
/* 467 */           b++;
/*     */         } 
/* 469 */       }  String str2 = "select distinct(jobtitle),jobtitlename from HrmCareerApply t1,HrmJobTitles where jobtitle = HrmJobTitles.id " + paramString;
/* 470 */       recordSet.executeSql(str2);
/* 471 */       while (recordSet.next()) {
/* 472 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 473 */         String str = recordSet.getString("jobtitle");
/* 474 */         this.header.put(Integer.valueOf(str), recordSet.getString("jobtitlename"));
/* 475 */         Enumeration<Integer> enumeration = this.show.keys();
/* 476 */         while (enumeration.hasMoreElements()) {
/* 477 */           Integer integer = enumeration.nextElement();
/* 478 */           String str3 = (String)this.show.get(integer);
/* 479 */           int k = str3.indexOf("-");
/* 480 */           int m = Util.getIntValue(str3.substring(0, k));
/* 481 */           int n = Util.getIntValue(str3.substring(k + 1, str3.length() - 2));
/* 482 */           String str4 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and jobtitle=" + str + " and worktime>=" + m + " and worktime <=" + n + paramString;
/* 483 */           this.rs.executeSql(str4);
/* 484 */           this.rs.next();
/* 485 */           int i1 = this.rs.getInt(1);
/* 486 */           hashtable1.put(integer, "" + i1);
/*     */         } 
/* 488 */         hashtable.put(Integer.valueOf(str), hashtable1);
/*     */       } 
/* 490 */       return hashtable;
/* 491 */     } catch (Exception exception) {
/* 492 */       writeLog(exception);
/*     */       
/* 494 */       return hashtable;
/*     */     } 
/*     */   } private Hashtable getPlanByCategory(String paramString, User paramUser) {
/* 497 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 498 */     this.show.put(new Integer(0), SystemEnv.getHtmlLabelName(134, paramUser.getLanguage()));
/* 499 */     this.show.put(new Integer(1), SystemEnv.getHtmlLabelName(1830, paramUser.getLanguage()));
/* 500 */     this.show.put(new Integer(2), SystemEnv.getHtmlLabelName(1831, paramUser.getLanguage()));
/* 501 */     this.show.put(new Integer(3), SystemEnv.getHtmlLabelName(1832, paramUser.getLanguage()));
/* 502 */     this.show.put(new Integer(4), SystemEnv.getHtmlLabelName(811, paramUser.getLanguage()));
/*     */     try {
/* 504 */       RecordSet recordSet = new RecordSet();
/* 505 */       CareerPlanComInfo careerPlanComInfo = new CareerPlanComInfo();
/* 506 */       String str = "select distinct(careerplanid) from HrmCareerApply t1,HrmCareerInvite t3 where t1.careerinviteid = t3.id " + paramString;
/* 507 */       writeLog(str);
/* 508 */       recordSet.executeSql(str);
/* 509 */       while (recordSet.next()) {
/* 510 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 511 */         String str1 = recordSet.getString(1);
/* 512 */         this.header.put(Integer.valueOf(str1), careerPlanComInfo.getCareerPlantopic(str1));
/* 513 */         String str2 = "select count(t1.id) from HrmCareerApply t1,HrmCareerInvite t3 where t1.careerinviteid = t3.id and t3.careerplanid = " + str1 + paramString;
/* 514 */         this.rs.executeSql(str2);
/* 515 */         this.rs.next();
/* 516 */         int i = this.rs.getInt(1);
/* 517 */         int j = 0;
/* 518 */         for (byte b = 0; b < 4; b++) {
/* 519 */           String str3 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t1.careerinviteid = t3.id and t3.careerplanid = " + str1 + " and t2.applyid = t1.id and t2.category = " + b + paramString;
/* 520 */           this.rs.executeSql(str3);
/* 521 */           this.rs.next();
/* 522 */           int k = this.rs.getInt(1);
/* 523 */           j += k;
/* 524 */           hashtable1.put(new Integer(b), "" + k);
/*     */         } 
/* 526 */         hashtable1.put(new Integer(4), "" + (i - j));
/* 527 */         hashtable.put(Integer.valueOf(str1), hashtable1);
/*     */       } 
/* 529 */       return hashtable;
/* 530 */     } catch (Exception exception) {
/* 531 */       writeLog(exception);
/*     */       
/* 533 */       return hashtable;
/*     */     } 
/*     */   } private Hashtable getPlanByEduLevel(String paramString) {
/* 536 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 538 */       RecordSet recordSet = new RecordSet();
/* 539 */       CareerPlanComInfo careerPlanComInfo = new CareerPlanComInfo();
/* 540 */       String str1 = "select distinct(educationlevel),name from HrmEducationLevel, HrmCareerApply t1 where t1.educationlevel = HrmEducationLevel.id " + paramString;
/* 541 */       this.rs.executeSql(str1);
/* 542 */       while (this.rs.next()) {
/* 543 */         this.show.put(Integer.valueOf(this.rs.getString("educationlevel")), Util.null2String(this.rs.getString("name")));
/*     */       }
/* 545 */       this.show.put(new Integer(0), Util.toScreen("" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "", 7, "0"));
/* 546 */       String str2 = "select distinct(careerplanid) from HrmCareerApply t1,HrmCareerInvite t3 where t1.careerinviteid = t3.id " + paramString;
/* 547 */       recordSet.executeSql(str2);
/* 548 */       while (recordSet.next()) {
/* 549 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 550 */         String str = recordSet.getString(1);
/* 551 */         this.header.put(Integer.valueOf(str), careerPlanComInfo.getCareerPlantopic(str));
/* 552 */         Enumeration<Integer> enumeration = this.show.keys();
/* 553 */         while (enumeration.hasMoreElements()) {
/* 554 */           Integer integer = enumeration.nextElement();
/* 555 */           String str3 = "select count(t1.id) from HrmCareerApply t1,HrmCareerInvite t2 where t1.careerinviteid = t2.id and t2.careerplanid = " + str + " and educationlevel = " + integer + paramString;
/* 556 */           this.rs.executeSql(str3);
/* 557 */           this.rs.next();
/* 558 */           int i = this.rs.getInt(1);
/* 559 */           hashtable1.put(integer, "" + i);
/*     */         } 
/* 561 */         hashtable.put(Integer.valueOf(str), hashtable1);
/*     */       } 
/* 563 */       return hashtable;
/* 564 */     } catch (Exception exception) {
/* 565 */       writeLog(exception);
/*     */       
/* 567 */       return hashtable;
/*     */     } 
/*     */   } private Hashtable getPlanBySalary(String paramString, int paramInt) {
/* 570 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 572 */       if (paramInt == 0) {
/* 573 */         return hashtable;
/*     */       }
/* 575 */       RecordSet recordSet = new RecordSet();
/* 576 */       CareerPlanComInfo careerPlanComInfo = new CareerPlanComInfo();
/* 577 */       int i = 0;
/* 578 */       String str1 = "select max(salaryneed) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString;
/* 579 */       this.rs.executeSql(str1);
/* 580 */       this.rs.next();
/* 581 */       i = this.rs.getInt(1);
/* 582 */       byte b = 0; int j;
/* 583 */       for (j = 0; j <= i; j += paramInt) {
/* 584 */         String str = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and salaryneed >=" + j + " and salaryneed<" + (j + paramInt) + paramString + " ";
/* 585 */         this.rs.executeSql(str);
/* 586 */         this.rs.next();
/* 587 */         int k = this.rs.getInt(1);
/* 588 */         if (k != 0) {
/* 589 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt) + Util.toScreen("元", 7, "0"));
/* 590 */           b++;
/*     */         } 
/* 592 */       }  String str2 = "select distinct(careerplanid) from HrmCareerApply t1,HrmCareerInvite t3 where t1.careerinviteid = t3.id " + paramString;
/* 593 */       recordSet.executeSql(str2);
/* 594 */       while (recordSet.next()) {
/* 595 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 596 */         String str = recordSet.getString(1);
/* 597 */         this.header.put(Integer.valueOf(str), careerPlanComInfo.getCareerPlantopic(str));
/* 598 */         Enumeration<Integer> enumeration = this.show.keys();
/* 599 */         while (enumeration.hasMoreElements()) {
/* 600 */           Integer integer = enumeration.nextElement();
/* 601 */           String str3 = (String)this.show.get(integer);
/* 602 */           int k = str3.indexOf("-");
/* 603 */           int m = Util.getIntValue(str3.substring(0, k));
/* 604 */           int n = Util.getIntValue(str3.substring(k + 1, str3.length() - 2));
/* 605 */           String str4 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id and t1.careerinviteid= t3.id and t3.careerplanid = " + str + " and salaryneed>=" + m + " and salaryneed <=" + n + paramString;
/* 606 */           this.rs.executeSql(str4);
/* 607 */           this.rs.next();
/* 608 */           int i1 = this.rs.getInt(1);
/* 609 */           hashtable1.put(integer, "" + i1);
/*     */         } 
/* 611 */         hashtable.put(Integer.valueOf(str), hashtable1);
/*     */       } 
/* 613 */       return hashtable;
/* 614 */     } catch (Exception exception) {
/* 615 */       writeLog(exception);
/*     */       
/* 617 */       return hashtable;
/*     */     } 
/*     */   } private Hashtable getPlanByWorkTime(String paramString, int paramInt) {
/* 620 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 622 */       if (paramInt == 0) {
/* 623 */         return hashtable;
/*     */       }
/* 625 */       RecordSet recordSet = new RecordSet();
/* 626 */       CareerPlanComInfo careerPlanComInfo = new CareerPlanComInfo();
/* 627 */       int i = 0;
/* 628 */       String str1 = "select max(worktime) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString;
/* 629 */       this.rs.executeSql(str1);
/* 630 */       this.rs.next();
/* 631 */       i = this.rs.getInt(1);
/* 632 */       byte b = 0; int j;
/* 633 */       for (j = 0; j <= i; j += paramInt) {
/* 634 */         String str = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and worktime >=" + j + " and worktime<" + (j + paramInt) + paramString + " ";
/* 635 */         this.rs.executeSql(str);
/* 636 */         this.rs.next();
/* 637 */         int k = this.rs.getInt(1);
/* 638 */         if (k != 0) {
/* 639 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt) + Util.toScreen("年", 7, "0"));
/* 640 */           b++;
/*     */         } 
/* 642 */       }  String str2 = "select distinct(careerplanid) from HrmCareerApply t1,HrmCareerInvite t3 where t1.careerinviteid = t3.id " + paramString;
/* 643 */       recordSet.executeSql(str2);
/* 644 */       while (recordSet.next()) {
/* 645 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 646 */         String str = recordSet.getString(1);
/* 647 */         this.header.put(Integer.valueOf(str), careerPlanComInfo.getCareerPlantopic(str));
/* 648 */         Enumeration<Integer> enumeration = this.show.keys();
/* 649 */         while (enumeration.hasMoreElements()) {
/* 650 */           Integer integer = enumeration.nextElement();
/* 651 */           String str3 = (String)this.show.get(integer);
/* 652 */           int k = str3.indexOf("-");
/* 653 */           int m = Util.getIntValue(str3.substring(0, k));
/* 654 */           int n = Util.getIntValue(str3.substring(k + 1, str3.length() - 2));
/* 655 */           String str4 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2,HrmCareerInvite t3 where t2.applyid = t1.id and t1.careerinviteid= t3.id and t3.careerplanid = " + str + " and worktime>=" + m + " and worktime <=" + n + paramString;
/* 656 */           this.rs.executeSql(str4);
/* 657 */           this.rs.next();
/* 658 */           int i1 = this.rs.getInt(1);
/* 659 */           hashtable1.put(integer, "" + i1);
/*     */         } 
/* 661 */         hashtable.put(Integer.valueOf(str), hashtable1);
/*     */       } 
/* 663 */       return hashtable;
/* 664 */     } catch (Exception exception) {
/* 665 */       writeLog(exception);
/*     */       
/* 667 */       return hashtable;
/*     */     } 
/*     */   }
/*     */   private Hashtable getMonByCategory(String paramString, int paramInt, User paramUser) {
/* 671 */     this.show.put(new Integer(0), SystemEnv.getHtmlLabelName(134, paramUser.getLanguage()));
/* 672 */     this.show.put(new Integer(1), SystemEnv.getHtmlLabelName(1830, paramUser.getLanguage()));
/* 673 */     this.show.put(new Integer(2), SystemEnv.getHtmlLabelName(1831, paramUser.getLanguage()));
/* 674 */     this.show.put(new Integer(3), SystemEnv.getHtmlLabelName(1832, paramUser.getLanguage()));
/* 675 */     this.show.put(new Integer(4), SystemEnv.getHtmlLabelName(811, paramUser.getLanguage()));
/* 676 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 678 */       for (byte b = 1; b < 13; b++) {
/* 679 */         this.header.put(new Integer(b), Util.toScreen("" + b + "月", 7, "0"));
/* 680 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 681 */         String str1 = "" + Util.add0(paramInt, 4) + "-" + Util.add0(b, 2) + "-01";
/* 682 */         String str2 = "" + Util.add0(paramInt, 4) + "-" + Util.add0(b, 2) + "-31";
/* 683 */         String str3 = " and createdate >='" + str1 + "' and createdate <='" + str2 + "'";
/* 684 */         String str4 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id  " + paramString + str3;
/* 685 */         this.rs.executeSql(str4);
/* 686 */         this.rs.next();
/* 687 */         int i = this.rs.getInt(1);
/* 688 */         int j = 0;
/* 689 */         for (byte b1 = 0; b1 < 4; b1++) {
/* 690 */           String str = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and t2.category = " + b1 + paramString + str3;
/* 691 */           this.rs.executeSql(str);
/* 692 */           this.rs.next();
/* 693 */           int k = this.rs.getInt(1);
/* 694 */           j += k;
/* 695 */           hashtable1.put(new Integer(b1), "" + k);
/*     */         } 
/* 697 */         hashtable1.put(new Integer(4), "" + (i - j));
/* 698 */         hashtable.put(new Integer(b), hashtable1);
/*     */       } 
/* 700 */       return hashtable;
/* 701 */     } catch (Exception exception) {
/* 702 */       writeLog(exception);
/*     */       
/* 704 */       return hashtable;
/*     */     } 
/*     */   }
/*     */   private Hashtable getMonByEduLevel(String paramString, int paramInt) {
/* 708 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 710 */       RecordSet recordSet = new RecordSet();
/* 711 */       String str = "select distinct(educationlevel),name from HrmEducationLevel, HrmCareerApply t1 where t1.educationlevel = HrmEducationLevel.id " + paramString;
/* 712 */       this.rs.executeSql(str);
/* 713 */       while (this.rs.next()) {
/* 714 */         this.show.put(Integer.valueOf(this.rs.getString("educationlevel")), Util.null2String(this.rs.getString("name")));
/*     */       }
/* 716 */       this.show.put(new Integer(0), Util.toScreen("" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "", 7, "0"));
/* 717 */       for (byte b = 1; b < 13; b++) {
/* 718 */         this.header.put(new Integer(b), Util.toScreen("" + b + "月", 7, "0"));
/* 719 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 720 */         String str1 = "" + Util.add0(paramInt, 4) + "-" + Util.add0(b, 2) + "-01";
/* 721 */         String str2 = "" + Util.add0(paramInt, 4) + "-" + Util.add0(b, 2) + "-31";
/* 722 */         String str3 = " and createdate >='" + str1 + "' and createdate <='" + str2 + "'";
/* 723 */         Enumeration<Integer> enumeration = this.show.keys();
/* 724 */         while (enumeration.hasMoreElements()) {
/* 725 */           Integer integer = enumeration.nextElement();
/* 726 */           String str4 = "select count(t1.id) from HrmCareerApply t1 where educationlevel = " + integer + paramString + str3;
/* 727 */           this.rs.executeSql(str4);
/* 728 */           this.rs.next();
/* 729 */           int i = this.rs.getInt(1);
/* 730 */           hashtable1.put(integer, "" + i);
/*     */         } 
/* 732 */         hashtable.put(new Integer(b), hashtable1);
/*     */       } 
/* 734 */       return hashtable;
/* 735 */     } catch (Exception exception) {
/* 736 */       writeLog(exception);
/*     */       
/* 738 */       return hashtable;
/*     */     } 
/*     */   }
/*     */   private Hashtable getMonBySalary(String paramString, int paramInt1, int paramInt2) {
/* 742 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 744 */       if (paramInt1 == 0) {
/* 745 */         return hashtable;
/*     */       }
/* 747 */       int i = 0;
/* 748 */       String str1 = Util.add0(paramInt2, 4) + "-01-01";
/* 749 */       String str2 = Util.add0(paramInt2, 4) + "-12-31";
/* 750 */       String str3 = " and createdate>='" + str1 + "' and createdate<='" + str2 + "'";
/* 751 */       String str4 = "select max(salaryneed) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString + str3;
/* 752 */       this.rs.executeSql(str4);
/* 753 */       this.rs.next();
/* 754 */       i = this.rs.getInt(1);
/* 755 */       byte b = 0; int j;
/* 756 */       for (j = 0; j <= i; j += paramInt1) {
/* 757 */         String str = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and salaryneed >=" + j + " and salaryneed<" + (j + paramInt1) + paramString + " ";
/* 758 */         this.rs.executeSql(str);
/* 759 */         this.rs.next();
/* 760 */         int k = this.rs.getInt(1);
/* 761 */         if (k != 0) {
/* 762 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt1) + Util.toScreen("元", 7, "0"));
/* 763 */           b++;
/*     */         } 
/* 765 */       }  for (j = 1; j < 13; j++) {
/* 766 */         this.header.put(new Integer(j), Util.toScreen("" + j + "月", 7, "0"));
/* 767 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 768 */         String str5 = "" + Util.add0(paramInt2, 4) + "-" + Util.add0(j, 2) + "-01";
/* 769 */         String str6 = "" + Util.add0(paramInt2, 4) + "-" + Util.add0(j, 2) + "-31";
/* 770 */         String str7 = " and createdate >='" + str5 + "' and createdate <='" + str6 + "'";
/* 771 */         Enumeration<Integer> enumeration = this.show.keys();
/* 772 */         while (enumeration.hasMoreElements()) {
/* 773 */           Integer integer = enumeration.nextElement();
/* 774 */           String str8 = (String)this.show.get(integer);
/* 775 */           int k = str8.indexOf("-");
/* 776 */           int m = Util.getIntValue(str8.substring(0, k));
/* 777 */           int n = Util.getIntValue(str8.substring(k + 1, str8.length() - 2));
/* 778 */           String str9 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id  and salaryneed>=" + m + " and salaryneed <=" + n + paramString + str7;
/* 779 */           this.rs.executeSql(str9);
/* 780 */           this.rs.next();
/* 781 */           int i1 = this.rs.getInt(1);
/* 782 */           hashtable1.put(integer, "" + i1);
/*     */         } 
/* 784 */         hashtable.put(new Integer(j), hashtable1);
/*     */       } 
/* 786 */       return hashtable;
/* 787 */     } catch (Exception exception) {
/* 788 */       writeLog(exception);
/*     */       
/* 790 */       return hashtable;
/*     */     } 
/*     */   } private Hashtable getMonByWorkTime(String paramString, int paramInt1, int paramInt2) {
/* 793 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     try {
/* 795 */       if (paramInt1 == 0) {
/* 796 */         return hashtable;
/*     */       }
/* 798 */       int i = 0;
/* 799 */       String str1 = Util.add0(paramInt2, 4) + "-01-01";
/* 800 */       String str2 = Util.add0(paramInt2, 4) + "-12-31";
/* 801 */       String str3 = " and createdate>='" + str1 + "' and createdate<='" + str2 + "'";
/* 802 */       String str4 = "select max(worktime) from HrmCareerApply t1, HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id " + paramString + str3;
/* 803 */       this.rs.executeSql(str4);
/* 804 */       this.rs.next();
/* 805 */       i = this.rs.getInt(1);
/* 806 */       byte b = 0; int j;
/* 807 */       for (j = 0; j <= i; j += paramInt1) {
/* 808 */         String str = "select count(applyid) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id and worktime >=" + j + " and worktime<" + (j + paramInt1) + paramString + " ";
/* 809 */         this.rs.executeSql(str);
/* 810 */         this.rs.next();
/* 811 */         int k = this.rs.getInt(1);
/* 812 */         if (k != 0) {
/* 813 */           this.show.put(new Integer(b), "" + j + "-" + (j + paramInt1) + Util.toScreen("年", 7, "0"));
/* 814 */           b++;
/*     */         } 
/* 816 */       }  for (j = 1; j < 13; j++) {
/* 817 */         this.header.put(new Integer(j), Util.toScreen("" + j + "月", 7, "0"));
/* 818 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 819 */         String str5 = "" + Util.add0(paramInt2, 4) + "-" + Util.add0(j, 2) + "-01";
/* 820 */         String str6 = "" + Util.add0(paramInt2, 4) + "-" + Util.add0(j, 2) + "-31";
/* 821 */         String str7 = " and createdate >='" + str5 + "' and createdate <='" + str6 + "'";
/* 822 */         Enumeration<Integer> enumeration = this.show.keys();
/* 823 */         while (enumeration.hasMoreElements()) {
/* 824 */           Integer integer = enumeration.nextElement();
/* 825 */           String str8 = (String)this.show.get(integer);
/* 826 */           int k = str8.indexOf("-");
/* 827 */           int m = Util.getIntValue(str8.substring(0, k));
/* 828 */           int n = Util.getIntValue(str8.substring(k + 1, str8.length() - 2));
/* 829 */           String str9 = "select count(t1.id) from HrmCareerApply t1,HrmCareerApplyOtherInfo t2 where t2.applyid = t1.id  and worktime>=" + m + " and worktime <=" + n + paramString + str7;
/* 830 */           this.rs.executeSql(str9);
/* 831 */           this.rs.next();
/* 832 */           int i1 = this.rs.getInt(1);
/* 833 */           hashtable1.put(integer, "" + i1);
/*     */         } 
/* 835 */         hashtable.put(new Integer(j), hashtable1);
/*     */       } 
/* 837 */       return hashtable;
/* 838 */     } catch (Exception exception) {
/* 839 */       writeLog(exception);
/*     */       
/* 841 */       return hashtable;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getShow() {
/* 849 */     return this.show;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getHeader() {
/* 857 */     return this.header;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/report/RpCareerApplyManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */