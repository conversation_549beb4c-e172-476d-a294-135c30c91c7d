/*     */ package weaver.hrm.location;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LocationComInfo
/*     */   extends CacheBase
/*     */ {
/*  15 */   protected static String TABLE_NAME = "HrmLocations";
/*     */   
/*  17 */   protected static String TABLE_WHERE = null;
/*     */   
/*  19 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  22 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "locationname")
/*     */   protected static int locationname;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "countryid")
/*     */   protected static int countryid;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "locationcity")
/*     */   protected static int locationcity;
/*     */ 
/*     */ 
/*     */   
/*     */   public int getLocationNum() {
/*  39 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  48 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocationid() {
/*  56 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocationname() {
/*  64 */     return (String)getRowValue(locationname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocationname(String paramString) {
/*  74 */     return (String)getValue(locationname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocationcountry() {
/*  82 */     return (String)getRowValue(countryid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocationcountry(String paramString) {
/*  92 */     return (String)getValue(countryid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocationcity() {
/* 100 */     return (String)getRowValue(locationcity);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocationcity(String paramString) {
/* 110 */     return (String)getValue(locationcity, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeLocationCache() {
/* 117 */     removeCache();
/*     */   }
/*     */   
/*     */   public ArrayList<String> getLocationOperate(String paramString1, String paramString2, String paramString3) {
/* 121 */     ArrayList<String> arrayList = new ArrayList();
/* 122 */     String str1 = paramString3.split(":")[0];
/* 123 */     String str2 = paramString3.split(":")[1];
/* 124 */     if (paramString2.equals("true")) {
/* 125 */       arrayList.add("true");
/*     */     } else {
/* 127 */       arrayList.add("false");
/*     */     } 
/* 129 */     if (str1.equals("true")) {
/* 130 */       arrayList.add(getLocationCheckbox(paramString1));
/*     */     } else {
/* 132 */       arrayList.add("false");
/*     */     } 
/* 134 */     if (str2.equals("true")) {
/* 135 */       arrayList.add("true");
/*     */     } else {
/* 137 */       arrayList.add("false");
/*     */     } 
/* 139 */     arrayList.add("true");
/* 140 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getLocationCheckbox(String paramString) {
/* 144 */     RecordSet recordSet = new RecordSet();
/* 145 */     recordSet.executeSql("select * from Hrmresource where locationid = " + paramString);
/* 146 */     if (recordSet.next()) {
/* 147 */       return "false";
/*     */     }
/* 149 */     return "true";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/location/LocationComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */