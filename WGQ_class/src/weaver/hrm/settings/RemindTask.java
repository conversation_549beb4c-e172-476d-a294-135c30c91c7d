/*    */ package weaver.hrm.settings;
/*    */ 
/*    */ import java.util.TimerTask;
/*    */ import weaver.file.LogMan;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RemindTask
/*    */   extends TimerTask
/*    */ {
/* 32 */   String mode = Prop.getPropValue(GCONST.getConfigFile(), "authentic");
/* 33 */   String type = Prop.getPropValue(GCONST.getConfigFile(), "ldap.type");
/* 34 */   LogMan lm = LogMan.getInstance();
/*    */   
/*    */   public void run() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/settings/RemindTask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */