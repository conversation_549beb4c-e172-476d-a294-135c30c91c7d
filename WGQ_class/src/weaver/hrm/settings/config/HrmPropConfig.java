/*   */ package weaver.hrm.settings.config;
/*   */ 
/*   */ import weaver.file.Prop;
/*   */ import weaver.general.Util;
/*   */ 
/*   */ public class HrmPropConfig
/*   */ {
/*   */   public static boolean isLoginRSAOpen() {
/* 9 */     return "1".equals(Util.null2String(Prop.getPropValue("openRSA", "openLoginRSA")));
/*   */   }
/*   */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/settings/config/HrmPropConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */