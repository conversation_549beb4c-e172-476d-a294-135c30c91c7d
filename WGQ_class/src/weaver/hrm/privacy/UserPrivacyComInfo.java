/*     */ package weaver.hrm.privacy;
/*     */ 
/*     */ import com.engine.common.biz.EncryptConfigBiz;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ public class UserPrivacyComInfo
/*     */   extends CacheBase
/*     */ {
/*  16 */   protected static String TABLE_NAME = "userprivacysetting";
/*     */   
/*  18 */   protected static String TABLE_WHERE = null;
/*     */   
/*  20 */   protected static String TABLE_ORDER = "userid";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  26 */   protected static String PK_NAME = "combinedid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "userid")
/*     */   protected static int userid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "ptype")
/*     */   protected static int ptype;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "pvalue")
/*     */   protected static int pvalue;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getPrivacyNum() {
/*  52 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  61 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserPrivacyid() {
/*  69 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getUserid() {
/*  74 */     return (String)getRowValue(0);
/*     */   }
/*     */   public String getUserid(String paramString) {
/*  77 */     return (String)getValue(userid, paramString);
/*     */   }
/*     */   
/*     */   public String getPtype() {
/*  81 */     return (String)getRowValue(0);
/*     */   }
/*     */   
/*     */   public String getPtype(String paramString) {
/*  85 */     return (String)getValue(ptype, paramString);
/*     */   }
/*     */   
/*     */   public String getPvalue() {
/*  89 */     return (String)getRowValue(0);
/*     */   }
/*     */   public String getPvalue(String paramString) {
/*  92 */     return (String)getValue(pvalue, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean autoInitIfNotFound() {
/*  99 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeUserPrivacyCache() {
/* 106 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShow(String paramString1, User paramUser, String paramString2, String paramString3, String paramString4) {
/* 119 */     String str = paramString3;
/*     */     try {
/* 121 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 122 */       boolean bool = true;
/* 123 */       int i = 0;
/* 124 */       int j = 0, k = 0;
/* 125 */       i = Util.getIntValue(paramString4, 0);
/*     */       
/* 127 */       j = Util.getIntValue(resourceComInfo.getSeclevel(paramString1), 0);
/*     */ 
/*     */       
/* 130 */       if (Integer.parseInt(paramString1) == paramUser.getUID()) {
/* 131 */         str = paramString3;
/* 132 */         return str;
/*     */       } 
/*     */       
/* 135 */       if (i == 2) {
/*     */         
/* 137 */         bool = false;
/* 138 */       } else if (i == 3) {
/*     */         
/* 140 */         k = Util.getIntValue(resourceComInfo.getSeclevel("" + paramUser.getUID()), 0);
/* 141 */         if (j > k) bool = false;
/*     */       
/*     */       } 
/*     */       
/* 145 */       if (!bool && paramString3.length() > 0) {
/* 146 */         str = formatShow(paramString2, paramString3);
/*     */       }
/* 148 */     } catch (Exception exception) {}
/*     */     
/* 150 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String formatShow(String paramString1, String paramString2) {
/* 160 */     if (paramString2.startsWith("desensitization__")) {
/* 161 */       paramString2 = EncryptConfigBiz.getDecryptData(paramString2);
/*     */     }
/* 163 */     String str = paramString2;
/* 164 */     if ("email".equals(paramString1)) {
/* 165 */       String str1 = str.substring(0, str.indexOf("@"));
/* 166 */       String str2 = str.substring(str.indexOf("@"));
/* 167 */       String str3 = "***************************************************************************************";
/* 168 */       int i = str1.length();
/* 169 */       int j = i / 2;
/* 170 */       str1 = str1.substring(0, j);
/* 171 */       str1 = str1 + str3;
/* 172 */       str1 = str1.substring(0, i);
/* 173 */       str = str1 + str2;
/*     */     } else {
/* 175 */       String str1 = "***************************************************************************************";
/* 176 */       int i = paramString2.length();
/* 177 */       int j = i / 2;
/* 178 */       str = paramString2.substring(0, j);
/* 179 */       str = str + str1;
/* 180 */       str = str.substring(0, i);
/*     */     } 
/* 182 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/privacy/UserPrivacyComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */