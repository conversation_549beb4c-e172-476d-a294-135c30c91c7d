/*     */ package weaver.hrm.privacy;
/*     */ 
/*     */ import com.engine.common.biz.EncryptConfigBiz;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PrivacyComInfo
/*     */   extends CacheBase
/*     */ {
/*  23 */   private Map<String, String> mapShowSets = null;
/*  24 */   private Map<String, String> mapShowTypes = null;
/*  25 */   private Map<String, String> mapShowTypeDefaults = null;
/*     */   
/*  27 */   protected static String TABLE_NAME = "hrmsynprivacysetting";
/*     */   
/*  29 */   protected static String TABLE_WHERE = null;
/*     */   
/*  31 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  34 */   protected static String PK_NAME = "id";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "fieldid")
/*     */   protected static int fieldid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "fieldname")
/*     */   protected static int fieldname;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "fieldlabel")
/*     */   protected static int fieldlabel;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "showTypeDefault")
/*     */   protected static int showTypeDefault;
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "showSet")
/*     */   protected static int showSet;
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "showType")
/*     */   protected static int showType;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PrivacyComInfo() {
/*  74 */     loadPrivacy();
/*     */   }
/*     */   
/*     */   private void loadPrivacy() {
/*  78 */     this.mapShowSets = new HashMap<>();
/*  79 */     this.mapShowTypes = new HashMap<>();
/*  80 */     this.mapShowTypeDefaults = new HashMap<>();
/*  81 */     while (next()) {
/*  82 */       this.mapShowSets.put(getFieldname(), getShowSet());
/*  83 */       this.mapShowTypes.put(getFieldname(), getShowType());
/*  84 */       this.mapShowTypeDefaults.put(getFieldname(), getShowTypeDefault());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getPrivacyNum() {
/*  94 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/* 103 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPrivacyid() {
/* 111 */     return (String)getRowValue(0);
/*     */   }
/*     */   
/*     */   public String getFieldid() {
/* 115 */     return (String)getRowValue(fieldid);
/*     */   }
/*     */   
/*     */   public String getFieldid(String paramString) {
/* 119 */     return (String)getValue(fieldid, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldname() {
/* 123 */     return (String)getRowValue(fieldname);
/*     */   }
/*     */   
/*     */   public String getFieldname(String paramString) {
/* 127 */     return (String)getValue(fieldname, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldlabel() {
/* 131 */     return (String)getRowValue(fieldlabel);
/*     */   }
/*     */   
/*     */   public String getFieldlabel(String paramString) {
/* 135 */     return (String)getValue(fieldlabel, paramString);
/*     */   }
/*     */   
/*     */   public String getShowTypeDefault() {
/* 139 */     return (String)getRowValue(showTypeDefault);
/*     */   }
/*     */   
/*     */   public String getShowTypeDefault(String paramString) {
/* 143 */     return (String)getValue(showTypeDefault, paramString);
/*     */   }
/*     */   
/*     */   public String getShowSet() {
/* 147 */     return (String)getRowValue(showSet);
/*     */   }
/*     */   
/*     */   public String getShowSet(String paramString) {
/* 151 */     return (String)getValue(showSet, paramString);
/*     */   }
/*     */   
/*     */   public String getShowType() {
/* 155 */     return (String)getRowValue(showType);
/*     */   }
/*     */   
/*     */   public String getShowType(String paramString) {
/* 159 */     return (String)getValue(showType, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removePrivacyCache() {
/* 166 */     removeCache();
/*     */   }
/*     */   
/*     */   public Map<String, String> getMapShowSets() {
/* 170 */     return this.mapShowSets;
/*     */   }
/*     */   
/*     */   public Map<String, String> getMapShowTypes() {
/* 174 */     return this.mapShowTypes;
/*     */   }
/*     */   
/*     */   public Map<String, String> getMapShowTypeDefaults() {
/* 178 */     return this.mapShowTypeDefaults;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShow(String paramString1, User paramUser, String paramString2, String paramString3, String paramString4) {
/* 191 */     String str = paramString3;
/*     */     try {
/* 193 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 194 */       boolean bool = true;
/* 195 */       int i = 0;
/* 196 */       int j = 0, k = 0;
/* 197 */       i = Util.getIntValue(paramString4, 0);
/*     */       
/* 199 */       j = Util.getIntValue(resourceComInfo.getSeclevel(paramString1), 0);
/*     */ 
/*     */       
/* 202 */       if (Integer.parseInt(paramString1) == paramUser.getUID()) {
/* 203 */         str = paramString3;
/* 204 */         return str;
/*     */       } 
/*     */       
/* 207 */       if (i == 2) {
/*     */         
/* 209 */         bool = false;
/* 210 */       } else if (i == 3) {
/*     */         
/* 212 */         k = Util.getIntValue(resourceComInfo.getSeclevel("" + paramUser.getUID()), 0);
/* 213 */         if (j > k) bool = false;
/*     */       
/*     */       } 
/*     */ 
/*     */       
/* 218 */       if (!bool && paramString3.length() > 0) {
/* 219 */         str = formatShow(paramString2, paramString3);
/*     */       }
/* 221 */     } catch (Exception exception) {}
/*     */     
/* 223 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String formatShow(String paramString1, String paramString2) {
/* 233 */     if (paramString2.startsWith("desensitization__")) {
/* 234 */       paramString2 = EncryptConfigBiz.getDecryptData(paramString2);
/*     */     }
/* 236 */     String str = paramString2;
/* 237 */     if ("email".equals(paramString1)) {
/* 238 */       String str1 = str.substring(0, str.indexOf("@"));
/* 239 */       String str2 = str.substring(str.indexOf("@"));
/* 240 */       String str3 = "***************************************************************************************";
/* 241 */       int i = str1.length();
/* 242 */       int j = i / 2;
/* 243 */       str1 = str1.substring(0, j);
/* 244 */       str1 = str1 + str3;
/* 245 */       str1 = str1.substring(0, i);
/* 246 */       str = str1 + str2;
/*     */     } else {
/* 248 */       String str1 = "***************************************************************************************";
/* 249 */       int i = paramString2.length();
/* 250 */       int j = i / 2;
/* 251 */       str = paramString2.substring(0, j);
/* 252 */       str = str + str1;
/* 253 */       str = str.substring(0, i);
/*     */     } 
/* 255 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSearchContent(String paramString1, String paramString2) {
/* 266 */     String str = paramString1;
/*     */     try {
/* 268 */       UserPrivacyComInfo userPrivacyComInfo = new UserPrivacyComInfo();
/* 269 */       User user = new User();
/* 270 */       String str1 = Util.splitString(paramString2, "+")[0];
/* 271 */       String str2 = Util.splitString(paramString2, "+")[1];
/* 272 */       String str3 = Util.splitString(paramString2, "+")[2];
/* 273 */       user.setUid(Integer.parseInt(str2));
/* 274 */       if (str2.equals("1")) user.setLoginid("sysadmin");
/*     */       
/* 276 */       if (this.mapShowSets != null && this.mapShowSets.get(str3) != null) {
/* 277 */         String str4 = Util.null2String(this.mapShowSets.get(str3));
/* 278 */         String str5 = Util.null2String(this.mapShowTypeDefaults.get(str3));
/* 279 */         if (str4.equals("1")) {
/* 280 */           String str6 = str1 + "__" + str3;
/* 281 */           String str7 = Util.null2String(userPrivacyComInfo.getPvalue(str6));
/* 282 */           if (str7.length() > 0) {
/* 283 */             str = userPrivacyComInfo.getShow(str1, user, str3, paramString1, str7);
/*     */           } else {
/* 285 */             str = getShow(str1, user, str3, paramString1, str5);
/*     */           } 
/*     */         } else {
/* 288 */           str = getShow(str1, user, str3, paramString1, str5);
/*     */         } 
/*     */       } 
/* 291 */     } catch (Exception exception) {
/* 292 */       exception.printStackTrace();
/*     */     } 
/* 294 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/privacy/PrivacyComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */