/*    */ package weaver.hrm.privacy;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PrivacyUtil
/*    */ {
/*    */   public static String checkByPrivacy(String paramString1, User paramUser, String paramString2, String paramString3, Map<String, String> paramMap1, Map<String, String> paramMap2, PrivacyComInfo paramPrivacyComInfo) {
/* 22 */     UserPrivacyComInfo userPrivacyComInfo = new UserPrivacyComInfo();
/* 23 */     if (paramString3.length() > 0 && 
/* 24 */       paramMap1 != null && paramMap1.get(paramString2) != null) {
/* 25 */       String str1 = Util.null2String(paramMap1.get(paramString2));
/* 26 */       String str2 = Util.null2String(paramMap2.get(paramString2));
/* 27 */       if (str1.equals("1")) {
/* 28 */         String str3 = paramString1 + "__" + paramString2;
/* 29 */         String str4 = Util.null2String(userPrivacyComInfo.getPvalue(str3));
/* 30 */         if (str4.length() > 0) {
/* 31 */           paramString3 = userPrivacyComInfo.getShow(paramString1, paramUser, paramString2, paramString3, str4);
/*    */         } else {
/* 33 */           paramString3 = paramPrivacyComInfo.getShow(paramString1, paramUser, paramString2, paramString3, str2);
/*    */         } 
/*    */       } else {
/* 36 */         paramString3 = paramPrivacyComInfo.getShow(paramString1, paramUser, paramString2, paramString3, str2);
/*    */       } 
/*    */     } 
/*    */     
/* 40 */     return paramString3;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/privacy/PrivacyUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */