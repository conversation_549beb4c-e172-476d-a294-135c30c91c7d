/*    */ package weaver.hrm.privacy;
/*    */ 
/*    */ public enum PrivacyBaseFieldEnum {
/*  4 */   MOBILE("mobile"),
/*  5 */   TELEPHONE("telephone"),
/*  6 */   MOBILECALL("mobilecall"),
/*  7 */   FAX("fax"),
/*  8 */   EMAIL("email");
/*    */   
/*    */   private String fieldname;
/*    */   
/*    */   PrivacyBaseFieldEnum(String paramString1) {
/* 13 */     this.fieldname = paramString1;
/*    */   }
/*    */   
/*    */   public String getFieldname() {
/* 17 */     return this.fieldname;
/*    */   }
/*    */   
/*    */   public void setFieldname(String paramString) {
/* 21 */     this.fieldname = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/privacy/PrivacyBaseFieldEnum.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */