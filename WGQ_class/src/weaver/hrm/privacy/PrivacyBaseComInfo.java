/*     */ package weaver.hrm.privacy;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PrivacyBaseComInfo
/*     */   extends CacheBase
/*     */ {
/*  15 */   private Map<String, String> mapShowSets = null;
/*  16 */   private Map<String, String> mapShowTypes = null;
/*  17 */   private Map<String, String> mapShowTypeDefaults = null;
/*     */   
/*  19 */   protected static String TABLE_NAME = "hrmprivacysetting";
/*     */   
/*  21 */   protected static String TABLE_WHERE = null;
/*     */   
/*  23 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  26 */   protected static String PK_NAME = "id";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "fieldid")
/*     */   protected static int fieldid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "fieldname")
/*     */   protected static int fieldname;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "fieldlabel")
/*     */   protected static int fieldlabel;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "showTypeDefault")
/*     */   protected static int showTypeDefault;
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "showSet")
/*     */   protected static int showSet;
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "showType")
/*     */   protected static int showType;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PrivacyBaseComInfo() {
/*  66 */     loadPrivacy();
/*     */   }
/*     */   
/*     */   private void loadPrivacy() {
/*  70 */     this.mapShowSets = new HashMap<>();
/*  71 */     this.mapShowTypes = new HashMap<>();
/*  72 */     this.mapShowTypeDefaults = new HashMap<>();
/*  73 */     while (next()) {
/*  74 */       this.mapShowSets.put(getFieldname(), getShowSet());
/*  75 */       this.mapShowTypes.put(getFieldname(), getShowType());
/*  76 */       this.mapShowTypeDefaults.put(getFieldname(), getShowTypeDefault());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getPrivacyNum() {
/*  86 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  95 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPrivacyid() {
/* 103 */     return (String)getRowValue(0);
/*     */   }
/*     */   
/*     */   public String getFieldid() {
/* 107 */     return (String)getRowValue(fieldid);
/*     */   }
/*     */   
/*     */   public String getFieldid(String paramString) {
/* 111 */     return (String)getValue(fieldid, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldname() {
/* 115 */     return (String)getRowValue(fieldname);
/*     */   }
/*     */   
/*     */   public String getFieldname(String paramString) {
/* 119 */     return (String)getValue(fieldname, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldlabel() {
/* 123 */     return (String)getRowValue(fieldlabel);
/*     */   }
/*     */   
/*     */   public String getFieldlabel(String paramString) {
/* 127 */     return (String)getValue(fieldlabel, paramString);
/*     */   }
/*     */   
/*     */   public String getShowTypeDefault() {
/* 131 */     return (String)getRowValue(showTypeDefault);
/*     */   }
/*     */   
/*     */   public String getShowTypeDefault(String paramString) {
/* 135 */     return (String)getValue(showTypeDefault, paramString);
/*     */   }
/*     */   
/*     */   public String getShowSet() {
/* 139 */     return (String)getRowValue(showSet);
/*     */   }
/*     */   
/*     */   public String getShowSet(String paramString) {
/* 143 */     return (String)getValue(showSet, paramString);
/*     */   }
/*     */   
/*     */   public String getShowType() {
/* 147 */     return (String)getRowValue(showType);
/*     */   }
/*     */   
/*     */   public String getShowType(String paramString) {
/* 151 */     return (String)getValue(showType, paramString);
/*     */   }
/*     */   
/*     */   public Map<String, String> getMapShowSets() {
/* 155 */     return this.mapShowSets;
/*     */   }
/*     */   
/*     */   public Map<String, String> getMapShowTypes() {
/* 159 */     return this.mapShowTypes;
/*     */   }
/*     */   
/*     */   public Map<String, String> getMapShowTypeDefaults() {
/* 163 */     return this.mapShowTypeDefaults;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removePrivacyCache() {
/* 170 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/privacy/PrivacyBaseComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */