/*     */ package weaver.hrm.company;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ 
/*     */ 
/*     */ public class CostcenterMainComInfo
/*     */   extends BaseBean
/*     */ {
/*  12 */   private ArrayList ids = null;
/*  13 */   private ArrayList names = null;
/*  14 */   private ArrayList descs = null;
/*  15 */   private StaticObj staticobj = null;
/*     */   
/*  17 */   private int current_index = -1;
/*  18 */   private int array_size = 0;
/*  19 */   private static Object lock = new Object();
/*     */   
/*     */   public CostcenterMainComInfo() throws Exception {
/*  22 */     this.staticobj = StaticObj.getInstance();
/*  23 */     getCostcenterMainInfo();
/*  24 */     this.array_size = this.ids.size();
/*     */   }
/*     */   
/*     */   private void getCostcenterMainInfo() throws Exception {
/*  28 */     synchronized (lock) {
/*  29 */       if (this.staticobj.getObject("CostcenterMainInfo") == null)
/*  30 */         setCostcenterMainInfo(); 
/*  31 */       this.ids = (ArrayList)this.staticobj.getRecordFromObj("CostcenterMainInfo", "ids");
/*  32 */       this.names = (ArrayList)this.staticobj.getRecordFromObj("CostcenterMainInfo", "names");
/*  33 */       this.descs = (ArrayList)this.staticobj.getRecordFromObj("CostcenterMainInfo", "descs");
/*  34 */       if (this.ids == null) setCostcenterMainInfo(); 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setCostcenterMainInfo() throws Exception {
/*  39 */     if (this.ids != null) {
/*  40 */       this.ids.clear();
/*     */     } else {
/*  42 */       this.ids = new ArrayList();
/*  43 */     }  if (this.names != null) {
/*  44 */       this.names.clear();
/*     */     } else {
/*  46 */       this.names = new ArrayList();
/*  47 */     }  if (this.descs != null) {
/*  48 */       this.descs.clear();
/*     */     } else {
/*  50 */       this.descs = new ArrayList();
/*     */     } 
/*  52 */     RecordSet recordSet = new RecordSet();
/*  53 */     recordSet.executeProc("HrmCostcenterMainCategory_S", "");
/*     */     try {
/*  55 */       while (recordSet.next()) {
/*  56 */         this.ids.add(recordSet.getString("id"));
/*  57 */         this.names.add(recordSet.getString("ccmaincategoryname"));
/*  58 */         this.descs.add(recordSet.getString("ccmaincategorydesc"));
/*     */       }
/*     */     
/*  61 */     } catch (Exception exception) {
/*  62 */       writeLog(exception);
/*  63 */       throw exception;
/*     */     } 
/*  65 */     this.staticobj.putRecordToObj("CostcenterMainInfo", "ids", this.ids);
/*  66 */     this.staticobj.putRecordToObj("CostcenterMainInfo", "names", this.names);
/*  67 */     this.staticobj.putRecordToObj("CostcenterMainInfo", "descs", this.descs);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getCostcenterMainNum() {
/*  72 */     return this.array_size;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  77 */     if (this.current_index + 1 < this.array_size) {
/*  78 */       this.current_index++;
/*  79 */       return true;
/*     */     } 
/*     */     
/*  82 */     this.current_index = -1;
/*  83 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  88 */     while (this.current_index + 1 < this.array_size) {
/*  89 */       this.current_index++;
/*     */     }
/*     */     
/*  92 */     if (this.current_index + 1 >= this.array_size) {
/*  93 */       this.current_index = -1;
/*  94 */       return false;
/*     */     } 
/*     */ 
/*     */     
/*  98 */     this.current_index++;
/*  99 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/* 104 */     this.current_index = -1;
/*     */   }
/*     */   
/*     */   public String getCostcenterMainid() {
/* 108 */     return this.ids.get(this.current_index);
/*     */   }
/*     */   
/*     */   public String getCostcenterMainname() {
/* 112 */     return this.names.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCostcenterMainname(String paramString) {
/* 117 */     int i = this.ids.indexOf(paramString);
/* 118 */     if (i != -1) {
/* 119 */       return this.names.get(i);
/*     */     }
/* 121 */     return "";
/*     */   }
/*     */   
/*     */   public String getCostcenterMaindesc() {
/* 125 */     return this.descs.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCostcenterMaindesc(String paramString) {
/* 130 */     int i = this.ids.indexOf(paramString);
/* 131 */     if (i != -1) {
/* 132 */       return this.descs.get(i);
/*     */     }
/* 134 */     return "";
/*     */   }
/*     */   
/*     */   public void removeCostcenterMainCache() {
/* 138 */     this.staticobj.removeObject("CostcenterMainInfo");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/company/CostcenterMainComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */