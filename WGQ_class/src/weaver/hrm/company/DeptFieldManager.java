/*     */ package weaver.hrm.company;
/*     */ 
/*     */ import com.weaver.integration.util.IntegratedSapUtil;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DeptFieldManager
/*     */   extends BaseBean
/*     */ {
/*  29 */   private String action = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void reset() {
/*  51 */     this.action = "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAction(String paramString) {
/*  63 */     this.action = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized int getNewIndexId(RecordSetTrans paramRecordSetTrans) {
/*  72 */     int i = -1;
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*  77 */       paramRecordSetTrans.executeSql("select min(id) as id from HtmlLabelIndex");
/*  78 */       if (paramRecordSetTrans.next()) {
/*  79 */         i = paramRecordSetTrans.getInt("id") - 1;
/*  80 */         if (i > -2) i = -2; 
/*     */       } 
/*  82 */     } catch (Exception exception) {
/*  83 */       i = -1;
/*     */     } 
/*  85 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getItemFieldTypeSelectForAddMainRow(User paramUser) {
/*  96 */     String str1 = "";
/*     */     
/*  98 */     if (paramUser == null) {
/*  99 */       return str1;
/*     */     }
/* 101 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/* 102 */     String str2 = "3";
/*     */     
/* 104 */     str1 = str1 + "<input type='hidden' name='itemFieldType_\" + rowindex + \"' value='3'>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 116 */     String str3 = "style='display:none'";
/* 117 */     if ("3".equals(str2)) {
/* 118 */       str3 = "style='display:inline'";
/*     */     }
/* 120 */     String str4 = IntegratedSapUtil.getIsOpenEcology70Sap();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 125 */     str1 = str1 + "<div id=div3_\" + rowindex + \" " + str3 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "&nbsp;<select class='InputStyle' name='broswerType_\" + rowindex + \"' onChange='onChangBroswerType(\"  + rowindex +  \")' style='width: 100px'>";
/*     */     
/* 127 */     while (browserComInfo.next()) {
/* 128 */       if (browserComInfo.getBrowserurl().equals("") || (
/* 129 */         !"1".equals(browserComInfo.getBrowserid()) && !"17".equals(browserComInfo.getBrowserid()))) {
/*     */         continue;
/*     */       }
/* 132 */       str1 = str1 + "<option value='" + browserComInfo.getBrowserid() + "'>" + SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo.getBrowserlabelid(), 0), paramUser.getLanguage()) + "</option>";
/*     */     } 
/* 134 */     str1 = str1 + "</select></div>";
/*     */ 
/*     */     
/* 137 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getSelectedForItemFieldType(String paramString1, String paramString2) {
/* 152 */     String str = "";
/*     */     
/* 154 */     if (paramString1 == null || paramString2 == null) {
/* 155 */       return str;
/*     */     }
/*     */     
/* 158 */     if (paramString1.equals(paramString2)) {
/* 159 */       str = "selected";
/*     */     }
/*     */     
/* 162 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/company/DeptFieldManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */