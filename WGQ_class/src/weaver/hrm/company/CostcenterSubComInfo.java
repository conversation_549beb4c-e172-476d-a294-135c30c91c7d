/*     */ package weaver.hrm.company;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ 
/*     */ 
/*     */ public class CostcenterSubComInfo
/*     */   extends BaseBean
/*     */ {
/*  12 */   private ArrayList ids = null;
/*  13 */   private ArrayList names = null;
/*  14 */   private ArrayList descs = null;
/*  15 */   private ArrayList ccmaincategoryid = null;
/*  16 */   private ArrayList isdefaults = null;
/*  17 */   private StaticObj staticobj = null;
/*     */   
/*  19 */   private int current_index = -1;
/*  20 */   private int array_size = 0;
/*  21 */   private static Object lock = new Object();
/*     */   
/*     */   public CostcenterSubComInfo() throws Exception {
/*  24 */     this.staticobj = StaticObj.getInstance();
/*  25 */     getCostcenterSubInfo();
/*  26 */     this.array_size = this.ids.size();
/*     */   }
/*     */   
/*     */   private void getCostcenterSubInfo() throws Exception {
/*  30 */     synchronized (lock) {
/*  31 */       if (this.staticobj.getObject("CostcenterSubInfo") == null)
/*  32 */         setCostcenterSubInfo(); 
/*  33 */       this.ids = (ArrayList)this.staticobj.getRecordFromObj("CostcenterSubInfo", "ids");
/*  34 */       this.names = (ArrayList)this.staticobj.getRecordFromObj("CostcenterSubInfo", "names");
/*  35 */       this.descs = (ArrayList)this.staticobj.getRecordFromObj("CostcenterSubInfo", "descs");
/*  36 */       this.ccmaincategoryid = (ArrayList)this.staticobj.getRecordFromObj("CostcenterSubInfo", "ccmaincategoryid");
/*  37 */       this.isdefaults = (ArrayList)this.staticobj.getRecordFromObj("CostcenterSubInfo", "isdefaults");
/*  38 */       if (this.ids == null) setCostcenterSubInfo(); 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setCostcenterSubInfo() throws Exception {
/*  43 */     if (this.ids != null) {
/*  44 */       this.ids.clear();
/*     */     } else {
/*  46 */       this.ids = new ArrayList();
/*  47 */     }  if (this.names != null) {
/*  48 */       this.names.clear();
/*     */     } else {
/*  50 */       this.names = new ArrayList();
/*  51 */     }  if (this.descs != null) {
/*  52 */       this.descs.clear();
/*     */     } else {
/*  54 */       this.descs = new ArrayList();
/*  55 */     }  if (this.ccmaincategoryid != null) {
/*  56 */       this.ccmaincategoryid.clear();
/*     */     } else {
/*  58 */       this.ccmaincategoryid = new ArrayList();
/*  59 */     }  if (this.isdefaults != null) {
/*  60 */       this.isdefaults.clear();
/*     */     } else {
/*  62 */       this.isdefaults = new ArrayList();
/*     */     } 
/*  64 */     RecordSet recordSet = new RecordSet();
/*  65 */     recordSet.executeProc("HrmCostcenterSubCategory_S", "");
/*     */     
/*     */     try {
/*  68 */       while (recordSet.next()) {
/*  69 */         this.ids.add(recordSet.getString(1));
/*  70 */         this.names.add(recordSet.getString(2));
/*  71 */         this.descs.add(recordSet.getString(3));
/*  72 */         this.ccmaincategoryid.add(recordSet.getString(4));
/*  73 */         this.isdefaults.add(recordSet.getString(5));
/*     */       }
/*     */     
/*  76 */     } catch (Exception exception) {
/*  77 */       writeLog(exception);
/*  78 */       throw exception;
/*     */     } 
/*  80 */     this.staticobj.putRecordToObj("CostcenterSubInfo", "ids", this.ids);
/*  81 */     this.staticobj.putRecordToObj("CostcenterSubInfo", "names", this.names);
/*  82 */     this.staticobj.putRecordToObj("CostcenterSubInfo", "descs", this.descs);
/*  83 */     this.staticobj.putRecordToObj("CostcenterSubInfo", "ccmaincategoryid", this.ccmaincategoryid);
/*  84 */     this.staticobj.putRecordToObj("CostcenterSubInfo", "isdefaults", this.isdefaults);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getCompanyNum() {
/*  89 */     return this.array_size;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  94 */     if (this.current_index + 1 < this.array_size) {
/*  95 */       this.current_index++;
/*  96 */       return true;
/*     */     } 
/*     */     
/*  99 */     this.current_index = -1;
/* 100 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/* 105 */     while (this.current_index + 1 < this.array_size) {
/* 106 */       this.current_index++;
/*     */     }
/*     */     
/* 109 */     if (this.current_index + 1 >= this.array_size) {
/* 110 */       this.current_index = -1;
/* 111 */       return false;
/*     */     } 
/*     */ 
/*     */     
/* 115 */     this.current_index++;
/* 116 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/* 121 */     this.current_index = -1;
/*     */   }
/*     */   
/*     */   public String getCostcenterSubid() {
/* 125 */     return this.ids.get(this.current_index);
/*     */   }
/*     */   
/*     */   public String getCostcenterSubname() {
/* 129 */     return this.names.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCostcenterSubname(String paramString) {
/* 134 */     int i = this.ids.indexOf(paramString);
/* 135 */     if (i != -1) {
/* 136 */       return this.names.get(i);
/*     */     }
/* 138 */     return "";
/*     */   }
/*     */   public String getCostcenterSubdesc() {
/* 141 */     return this.descs.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCostcenterSubdesc(String paramString) {
/* 146 */     int i = this.ids.indexOf(paramString);
/* 147 */     if (i != -1) {
/* 148 */       return this.descs.get(i);
/*     */     }
/* 150 */     return "";
/*     */   }
/*     */   public String getMaincategoryid() {
/* 153 */     return this.ccmaincategoryid.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getMaincategoryid(String paramString) {
/* 158 */     int i = this.ids.indexOf(paramString);
/* 159 */     if (i != -1) {
/* 160 */       return this.ccmaincategoryid.get(i);
/*     */     }
/* 162 */     return "";
/*     */   }
/*     */   
/*     */   public String getIsdefault() {
/* 166 */     return this.isdefaults.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getIsdefault(String paramString) {
/* 171 */     int i = this.ids.indexOf(paramString);
/* 172 */     if (i != -1) {
/* 173 */       return this.isdefaults.get(i);
/*     */     }
/* 175 */     return "";
/*     */   }
/*     */   public void removeCompanyCache() {
/* 178 */     this.staticobj.removeObject("CostcenterSubInfo");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/company/CostcenterSubComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */