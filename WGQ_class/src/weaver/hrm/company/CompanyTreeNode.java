/*    */ package weaver.hrm.company;
/*    */ 
/*    */ import weaver.common.util.taglib.TreeNode;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CompanyTreeNode
/*    */   extends TreeNode
/*    */ {
/*    */   private String id;
/*    */   private String type;
/*    */   
/*    */   public String getId() {
/* 26 */     return this.id;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setId(String paramString) {
/* 34 */     this.id = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getType() {
/* 41 */     return this.type;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setType(String paramString) {
/* 49 */     this.type = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean equals(Object paramObject) {
/* 58 */     if (getId().equals(((CompanyTreeNode)paramObject).getId()) && getType().equals(((CompanyTreeNode)paramObject).getType()))
/*    */     {
/* 60 */       return true;
/*    */     }
/* 62 */     return false;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 67 */     int i = 1;
/*    */     
/* 69 */     i = i * 31 + ((this.type == null) ? 0 : this.type.hashCode());
/* 70 */     i = i * 31 + ((this.id == null) ? 0 : this.id.hashCode());
/* 71 */     i = i * 31 + ((getTitle() == null) ? 0 : getTitle().hashCode());
/* 72 */     i = i * 31 + ((getNodeName() == null) ? 0 : getNodeName().hashCode());
/* 73 */     i = i * 31 + ((getLevel() == null) ? 0 : getLevel().hashCode());
/* 74 */     return i;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/company/CompanyTreeNode.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */