/*     */ package weaver.hrm.company;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ 
/*     */ 
/*     */ public class CostCenterComInfo
/*     */   extends BaseBean
/*     */ {
/*  12 */   private ArrayList ids = null;
/*  13 */   private ArrayList names = null;
/*  14 */   private ArrayList marks = null;
/*  15 */   private ArrayList activables = null;
/*  16 */   private ArrayList departmentid = null;
/*  17 */   private ArrayList ccsubcategory1s = null;
/*  18 */   private ArrayList ccsubcategory2s = null;
/*  19 */   private ArrayList ccsubcategory3s = null;
/*  20 */   private ArrayList ccsubcategory4s = null;
/*  21 */   private StaticObj staticobj = null;
/*     */   
/*  23 */   private int current_index = -1;
/*  24 */   private int array_size = 0;
/*  25 */   private static Object lock = new Object();
/*     */   
/*     */   public CostCenterComInfo() throws Exception {
/*  28 */     this.staticobj = StaticObj.getInstance();
/*  29 */     getCostCenterInfo();
/*  30 */     this.array_size = this.ids.size();
/*     */   }
/*     */   
/*     */   private void getCostCenterInfo() throws Exception {
/*  34 */     synchronized (lock) {
/*  35 */       if (this.staticobj.getObject("CostCenterInfo") == null)
/*  36 */         setCostCenterInfo(); 
/*  37 */       this.ids = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "ids");
/*  38 */       this.names = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "names");
/*  39 */       this.marks = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "marks");
/*  40 */       this.activables = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "activables");
/*  41 */       this.departmentid = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "departmentid");
/*  42 */       this.ccsubcategory1s = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "ccsubcategory1s");
/*  43 */       this.ccsubcategory2s = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "ccsubcategory2s");
/*  44 */       this.ccsubcategory3s = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "ccsubcategory3s");
/*  45 */       this.ccsubcategory4s = (ArrayList)this.staticobj.getRecordFromObj("CostCenterInfo", "ccsubcategory4s");
/*  46 */       if (this.ids == null) setCostCenterInfo(); 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setCostCenterInfo() throws Exception {
/*  51 */     if (this.ids != null) {
/*  52 */       this.ids.clear();
/*     */     } else {
/*  54 */       this.ids = new ArrayList();
/*  55 */     }  if (this.names != null) {
/*  56 */       this.names.clear();
/*     */     } else {
/*  58 */       this.names = new ArrayList();
/*  59 */     }  if (this.marks != null) {
/*  60 */       this.marks.clear();
/*     */     } else {
/*  62 */       this.marks = new ArrayList();
/*  63 */     }  if (this.activables != null) {
/*  64 */       this.activables.clear();
/*     */     } else {
/*  66 */       this.activables = new ArrayList();
/*  67 */     }  if (this.departmentid != null) {
/*  68 */       this.departmentid.clear();
/*     */     } else {
/*  70 */       this.departmentid = new ArrayList();
/*  71 */     }  if (this.ccsubcategory1s != null) {
/*  72 */       this.ccsubcategory1s.clear();
/*     */     } else {
/*  74 */       this.ccsubcategory1s = new ArrayList();
/*  75 */     }  if (this.ccsubcategory2s != null) {
/*  76 */       this.ccsubcategory2s.clear();
/*     */     } else {
/*  78 */       this.ccsubcategory2s = new ArrayList();
/*  79 */     }  if (this.ccsubcategory3s != null) {
/*  80 */       this.ccsubcategory3s.clear();
/*     */     } else {
/*  82 */       this.ccsubcategory3s = new ArrayList();
/*  83 */     }  if (this.ccsubcategory4s != null) {
/*  84 */       this.ccsubcategory4s.clear();
/*     */     } else {
/*  86 */       this.ccsubcategory4s = new ArrayList();
/*     */     } 
/*  88 */     RecordSet recordSet = new RecordSet();
/*  89 */     recordSet.executeProc("HrmCostCenter_Select", "");
/*     */     
/*     */     try {
/*  92 */       while (recordSet.next()) {
/*  93 */         this.ids.add(recordSet.getString(1));
/*  94 */         this.names.add(recordSet.getString(3));
/*  95 */         this.marks.add(recordSet.getString(2));
/*  96 */         this.departmentid.add(recordSet.getString(5));
/*  97 */         this.ccsubcategory1s.add(recordSet.getString(6));
/*     */       }
/*     */     
/* 100 */     } catch (Exception exception) {
/* 101 */       writeLog(exception);
/* 102 */       throw exception;
/*     */     } 
/* 104 */     this.staticobj.putRecordToObj("CostCenterInfo", "ids", this.ids);
/* 105 */     this.staticobj.putRecordToObj("CostCenterInfo", "names", this.names);
/* 106 */     this.staticobj.putRecordToObj("CostCenterInfo", "marks", this.marks);
/* 107 */     this.staticobj.putRecordToObj("CostCenterInfo", "departmentid", this.departmentid);
/* 108 */     this.staticobj.putRecordToObj("CostCenterInfo", "ccsubcategory1s", this.ccsubcategory1s);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getCompanyNum() {
/* 113 */     return this.array_size;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 118 */     if (this.current_index + 1 < this.array_size) {
/* 119 */       this.current_index++;
/* 120 */       return true;
/*     */     } 
/*     */     
/* 123 */     this.current_index = -1;
/* 124 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/* 129 */     while (this.current_index + 1 < this.array_size) {
/* 130 */       this.current_index++;
/*     */     }
/*     */     
/* 133 */     if (this.current_index + 1 >= this.array_size) {
/* 134 */       this.current_index = -1;
/* 135 */       return false;
/*     */     } 
/*     */ 
/*     */     
/* 139 */     this.current_index++;
/* 140 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/* 145 */     this.current_index = -1;
/*     */   }
/*     */   
/*     */   public String getCostCenterid() {
/* 149 */     return this.ids.get(this.current_index);
/*     */   }
/*     */   
/*     */   public String getCostCentername() {
/* 153 */     return this.names.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCostCentername(String paramString) {
/* 158 */     int i = this.ids.indexOf(paramString);
/* 159 */     if (i != -1) {
/* 160 */       return this.names.get(i);
/*     */     }
/* 162 */     return "";
/*     */   }
/*     */   public String getCostCentermark() {
/* 165 */     return this.marks.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCostCentermark(String paramString) {
/* 170 */     int i = this.ids.indexOf(paramString);
/* 171 */     if (i != -1) {
/* 172 */       return this.marks.get(i);
/*     */     }
/* 174 */     return "";
/*     */   }
/*     */   public String getDepartmentid() {
/* 177 */     return this.departmentid.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDepartmentid(String paramString) {
/* 182 */     int i = this.ids.indexOf(paramString);
/* 183 */     if (i != -1) {
/* 184 */       return this.departmentid.get(i);
/*     */     }
/* 186 */     return "";
/*     */   }
/*     */   public String getActivable() {
/* 189 */     return this.activables.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getActivable(String paramString) {
/* 194 */     int i = this.ids.indexOf(paramString);
/* 195 */     if (i != -1) {
/* 196 */       return this.activables.get(i);
/*     */     }
/* 198 */     return "";
/*     */   }
/*     */   
/*     */   public String getSubcategoryid1() {
/* 202 */     return this.ccsubcategory1s.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSubcategoryid1(String paramString) {
/* 207 */     int i = this.ids.indexOf(paramString);
/* 208 */     if (i != -1) {
/* 209 */       return this.ccsubcategory1s.get(i);
/*     */     }
/* 211 */     return "";
/*     */   }
/*     */   public String getSubcategoryid2() {
/* 214 */     return this.ccsubcategory2s.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSubcategoryid2(String paramString) {
/* 219 */     int i = this.ids.indexOf(paramString);
/* 220 */     if (i != -1) {
/* 221 */       return this.ccsubcategory2s.get(i);
/*     */     }
/* 223 */     return "";
/*     */   }
/*     */   public String getSubcategoryid3() {
/* 226 */     return this.ccsubcategory3s.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSubcategoryid3(String paramString) {
/* 231 */     int i = this.ids.indexOf(paramString);
/* 232 */     if (i != -1) {
/* 233 */       return this.ccsubcategory3s.get(i);
/*     */     }
/* 235 */     return "";
/*     */   }
/*     */   public String getSubcategoryid4() {
/* 238 */     return this.ccsubcategory4s.get(this.current_index);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSubcategoryid4(String paramString) {
/* 243 */     int i = this.ids.indexOf(paramString);
/* 244 */     if (i != -1) {
/* 245 */       return this.ccsubcategory4s.get(i);
/*     */     }
/* 247 */     return "";
/*     */   }
/*     */   public void removeCompanyCache() {
/* 250 */     this.staticobj.removeObject("CostCenterInfo");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/company/CostCenterComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */