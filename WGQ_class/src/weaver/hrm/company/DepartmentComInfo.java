/*     */ package weaver.hrm.company;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.StringTokenizer;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.cachecenter.bean.LoadComInfo;
/*     */ import weaver.hrm.cachecenter.bean.SuperDepartmentComInfo;
/*     */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*     */ import weaver.hrm.login.cache.DepartmentDoaminComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class DepartmentComInfo
/*     */   extends CacheBase
/*     */ {
/*  29 */   protected static String TABLE_NAME = "HrmDepartment";
/*     */ 
/*     */ 
/*     */   
/*  33 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */   
/*  37 */   protected static String TABLE_ORDER = "subcompanyid1 asc , supdepid asc , showorder asc, departmentname asc ";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  40 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn(name = "departmentname")
/*     */   protected static int name;
/*     */   @CacheColumn(name = "departmentmark")
/*     */   protected static int mark;
/*     */   @CacheColumn(name = "supdepid")
/*     */   protected static int supdepid;
/*     */   @CacheColumn(name = "allsupdepid")
/*     */   protected static int allsupdepid;
/*     */   @CacheColumn(name = "subcompanyid1")
/*     */   protected static int subcompanyid1;
/*     */   @CacheColumn(name = "showorder")
/*     */   protected static int showorder;
/*     */   @CacheColumn(name = "canceled")
/*     */   protected static int canceled;
/*     */   @CacheColumn(name = "coadjutant")
/*     */   protected static int coadjutant;
/*     */   @CacheColumn(name = "departmentcode")
/*     */   private static int departmentcode;
/*  60 */   private static ConcurrentHashMap<String, String> id2Idx = new ConcurrentHashMap<>();
/*  61 */   private static ConcurrentHashMap<String, String> idx2Id = new ConcurrentHashMap<>();
/*  62 */   private static ConcurrentHashMap<String, String> id2ParentIds = new ConcurrentHashMap<>();
/*  63 */   private static ConcurrentHashMap<String, String> id2ChildIds = new ConcurrentHashMap<>();
/*     */ 
/*     */ 
/*     */   
/*     */   public CacheMap initCache() throws Exception {
/*  68 */     CacheMap cacheMap = createCacheMap();
/*  69 */     byte b = 0;
/*  70 */     id2Idx.clear();
/*  71 */     idx2Id.clear();
/*  72 */     RecordSet recordSet = new RecordSet();
/*  73 */     recordSet.executeProc("HrmDepartment_Select", "");
/*  74 */     while (recordSet.next()) {
/*  75 */       String str = Util.null2String(recordSet.getString(PK_NAME));
/*  76 */       CacheItem cacheItem = createCacheItem();
/*  77 */       parseResultSetToCacheItem(recordSet, cacheItem);
/*     */ 
/*     */       
/*  80 */       modifyCacheItem(str, cacheItem);
/*  81 */       cacheMap.put(str, cacheItem);
/*  82 */       id2Idx.put(recordSet.getString("id"), "" + b);
/*  83 */       idx2Id.put("" + b, recordSet.getString("id"));
/*  84 */       b++;
/*     */     } 
/*  86 */     return cacheMap;
/*     */   }
/*     */   
/*     */   public CacheItem initCache(String paramString) {
/*  90 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCompanyNum() {
/*  99 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentid() {
/* 108 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentid(int paramInt) {
/* 118 */     return idx2Id.get("" + paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentCode() {
/* 127 */     return (String)getRowValue(departmentcode);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentCode(String paramString) {
/* 137 */     if (Util.getIntValue(paramString) < 0) {
/*     */       
/*     */       try {
/* 140 */         DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/* 141 */         return departmentVirtualComInfo.getDepartmentCode(paramString);
/* 142 */       } catch (Exception exception) {
/* 143 */         writeLog(exception);
/*     */       } 
/*     */     }
/* 146 */     return (String)getValue(departmentcode, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIdIndexKey(String paramString) {
/* 156 */     return Util.getIntValue(id2Idx.get(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentname() {
/* 165 */     return (String)getRowValue(name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentname(String paramString) {
/* 175 */     if (Util.getIntValue(paramString) < 0) {
/*     */       
/*     */       try {
/* 178 */         DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/* 179 */         return departmentVirtualComInfo.getDepartmentname(paramString);
/* 180 */       } catch (Exception exception) {
/* 181 */         writeLog(exception);
/*     */       } 
/*     */     }
/* 184 */     return (String)getValue(name, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentnameToLink(String paramString) {
/* 194 */     return "<a href=\"/hrm/company/HrmDepartmentDsp.jsp?id=" + paramString + "\" target=\"_new\"  >" + getDepartmentname(paramString) + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentmark() {
/* 203 */     return (String)getRowValue(mark);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentmark(String paramString) {
/* 213 */     if (Util.getIntValue(paramString) < 0) {
/*     */       
/*     */       try {
/* 216 */         DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/* 217 */         return departmentVirtualComInfo.getDepartmentmark(paramString);
/* 218 */       } catch (Exception exception) {
/* 219 */         writeLog(exception);
/*     */       } 
/*     */     }
/*     */     
/* 223 */     return (String)getValue(mark, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentsupdepid() {
/* 232 */     return (String)getRowValue(supdepid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentsupdepid(String paramString) {
/* 242 */     return (String)getValue(supdepid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoadjutant() {
/* 251 */     return (String)getRowValue(coadjutant);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoadjutant(String paramString) {
/* 261 */     return (String)getValue(coadjutant, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllsupdepid() {
/* 270 */     return (String)getRowValue(allsupdepid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllsupdepid(String paramString) {
/* 280 */     return (String)getValue(allsupdepid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShoworder() {
/* 289 */     return (String)getRowValue(showorder);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShoworder(String paramString) {
/* 299 */     return (String)getValue(showorder, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubcompanyid1() {
/* 308 */     return (String)getRowValue(subcompanyid1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubcompanyid1(String paramString) {
/* 318 */     return (String)getValue(subcompanyid1, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDeparmentcanceled() {
/* 327 */     return (String)getRowValue(canceled);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDeparmentcanceled(String paramString) {
/* 337 */     return (String)getValue(canceled, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCompanyCache() {
/* 345 */     (new SuperDepartmentComInfo()).removeCache();
/* 346 */     removeCache();
/* 347 */     id2ChildIds.clear();
/* 348 */     id2ParentIds.clear();
/* 349 */     (new DepartmentDoaminComInfo()).removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllSupDepId(int paramInt) {
/* 359 */     RecordSet recordSet = new RecordSet();
/* 360 */     String str1 = "";
/* 361 */     int i = 0;
/* 362 */     String str2 = "select supdepid from HrmDepartment where id = " + paramInt;
/* 363 */     recordSet.executeSql(str2);
/* 364 */     while (recordSet.next()) {
/* 365 */       i = Util.getIntValue(recordSet.getString("supdepid"), 0);
/*     */     }
/* 367 */     str2 = "select allsupdepid from HrmDepartment where id = " + i;
/* 368 */     recordSet.executeSql(str2);
/* 369 */     while (recordSet.next()) {
/* 370 */       str1 = recordSet.getString("allsupdepid");
/*     */     }
/* 372 */     str1 = str1 + i + ",";
/*     */     
/* 374 */     return str1;
/*     */   }
/*     */   
/*     */   public int getLevelByDepId(String paramString) {
/* 378 */     RecordSet recordSet = new RecordSet();
/* 379 */     byte b1 = 0;
/* 380 */     byte b2 = 0;
/* 381 */     int i = 0;
/* 382 */     String str1 = "0";
/* 383 */     String str2 = "0";
/* 384 */     String str3 = "select subcompanyid1,supdepid from HrmDepartment where id = " + paramString;
/* 385 */     recordSet.executeSql(str3);
/* 386 */     if (recordSet.next()) {
/* 387 */       str1 = Util.null2String(recordSet.getString(2));
/* 388 */       str2 = Util.null2String(recordSet.getString(1));
/* 389 */       b1++;
/* 390 */       b2++;
/* 391 */       if ("".equals(str1))
/* 392 */         str1 = "0"; 
/* 393 */       if ("".equals(str2))
/* 394 */         str2 = "0"; 
/*     */     } 
/* 396 */     while (!"0".equals(str1)) {
/* 397 */       str3 = "select supdepid from HrmDepartment where id = " + str1;
/* 398 */       recordSet.executeSql(str3);
/* 399 */       if (recordSet.next()) {
/* 400 */         str1 = Util.null2String(recordSet.getString(1));
/* 401 */         b1++;
/* 402 */         if ("".equals(str1)) {
/* 403 */           str1 = "0";
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 409 */     while (!"0".equals(str2)) {
/* 410 */       str3 = "select supsubcomid from HrmSubCompany where id = " + str2;
/* 411 */       recordSet.executeSql(str3);
/* 412 */       if (recordSet.next()) {
/* 413 */         str2 = Util.null2String(recordSet.getString(1));
/* 414 */         b2++;
/* 415 */         if ("".equals(str2)) {
/* 416 */           str2 = "0";
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 422 */     i = b1 + b2;
/* 423 */     return i;
/*     */   }
/*     */   
/*     */   public String getDeptnames(String paramString) {
/* 427 */     String str = "";
/* 428 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 429 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 430 */       str = str + getDepartmentname(arrayOfString[b]) + ",";
/*     */     }
/* 432 */     if (!"".equals(str)) str = str.substring(0, str.length() - 1); 
/* 433 */     return str;
/*     */   }
/*     */   
/*     */   public ArrayList getAllChildDeptByDepId(ArrayList<String> paramArrayList, String paramString) {
/* 437 */     RecordSet recordSet = new RecordSet();
/* 438 */     recordSet.executeSql("select id from HrmDepartment where supdepid=" + paramString);
/* 439 */     while (recordSet.next()) {
/* 440 */       String str = Util.null2String(recordSet.getString(1));
/* 441 */       if ("".equals(str)) {
/*     */         continue;
/*     */       }
/* 444 */       paramArrayList.add(str);
/* 445 */       getAllChildDeptByDepId(paramArrayList, str);
/*     */     } 
/* 447 */     return paramArrayList;
/*     */   }
/*     */   
/*     */   public String getChildDeptId(String paramString) {
/* 451 */     String str = null;
/* 452 */     RecordSet recordSet = new RecordSet();
/* 453 */     recordSet.executeQuery("select id from HrmDepartment where supdepid = ?", new Object[] { paramString });
/* 454 */     while (recordSet.next()) {
/* 455 */       str = Util.null2String(recordSet.getString(1));
/*     */     }
/* 457 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAllParentDepartIdbak(String paramString1, String paramString2) throws Exception {
/* 468 */     RecordSet recordSet = new RecordSet();
/* 469 */     recordSet.executeSql("select supdepid from HrmDepartment where (canceled IS NULL OR canceled !='1') and supdepid !=0 and id = " + paramString1);
/* 470 */     while (recordSet.next()) {
/* 471 */       paramString2 = paramString2 + "," + recordSet.getString(1);
/* 472 */       paramString2 = getAllParentDepartIdbak(recordSet.getString(1), paramString2);
/*     */     } 
/* 474 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllSupDepartment(String paramString) throws Exception {
/* 485 */     if (id2ParentIds.containsKey(paramString)) {
/* 486 */       return id2ParentIds.get(paramString);
/*     */     }
/* 488 */     String str = getAllSupDepartment(paramString, new DepartmentComInfo(), 1);
/* 489 */     id2ParentIds.put(paramString, str);
/* 490 */     return str;
/*     */   }
/*     */   
/*     */   private String getAllSupDepartment(String paramString, DepartmentComInfo paramDepartmentComInfo, int paramInt) throws Exception {
/* 494 */     String str1 = "";
/* 495 */     if (paramDepartmentComInfo == null) {
/* 496 */       paramDepartmentComInfo = new DepartmentComInfo();
/*     */     }
/* 498 */     String str2 = paramDepartmentComInfo.getDepartmentsupdepid(paramString);
/* 499 */     if (str2 == null || str2.equals("") || str2.equals("0") || str2.equals(paramString) || ("," + str1)
/* 500 */       .indexOf("," + str2 + ",") > -1 || paramInt > 10000)
/* 501 */       return str1; 
/* 502 */     str1 = str1 + str2 + ",";
/* 503 */     paramInt++;
/* 504 */     str1 = str1 + getAllSupDepartment(str2, paramDepartmentComInfo, paramInt);
/* 505 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAllParentDepartId(String paramString1, String paramString2) throws Exception {
/*     */     try {
/* 534 */       String str = (new DepartmentComInfo()).getAllSupDepartment(paramString1);
/* 535 */       if (str.length() > 0) {
/* 536 */         paramString2 = paramString2 + "," + str;
/* 537 */         if (paramString2.endsWith(",")) paramString2 = paramString2.substring(0, paramString2.lastIndexOf(",")); 
/*     */       } 
/* 539 */     } catch (Exception exception) {
/* 540 */       (new BaseBean()).writeLog("SubCompanyComInfo>>>getAllParentSubcompanyId" + exception);
/*     */     } 
/* 542 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAllChildDepartIdbak(String paramString1, String paramString2) throws Exception {
/* 553 */     RecordSet recordSet = new RecordSet();
/* 554 */     recordSet.executeSql("select id from HrmDepartment where (canceled IS NULL OR canceled !='1') and id <>  " + paramString1 + " and supdepid = " + paramString1);
/* 555 */     while (recordSet.next()) {
/* 556 */       paramString2 = paramString2 + "," + recordSet.getString(1);
/* 557 */       paramString2 = getAllChildDepartIdbak(recordSet.getString(1), paramString2);
/*     */     } 
/* 559 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAllChildDepartId(String paramString1, String paramString2) throws Exception {
/* 569 */     return getAllChildDepartId(paramString1, paramString2, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAllChildDepartId(String paramString1, String paramString2, boolean paramBoolean) throws Exception {
/* 581 */     SuperDepartmentComInfo superDepartmentComInfo = (SuperDepartmentComInfo)LoadComInfo.getInstance(SuperDepartmentComInfo.class);
/* 582 */     StringBuffer stringBuffer1 = new StringBuffer();
/*     */     
/* 584 */     String str1 = superDepartmentComInfo.getAllDownIdsBySuperId(paramString1);
/*     */     
/* 586 */     if (!StringUtils.isBlank(str1)) {
/* 587 */       if (stringBuffer1.length() > 0) stringBuffer1.append(","); 
/* 588 */       stringBuffer1.append(str1);
/*     */     } 
/* 590 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 591 */     if (!paramBoolean && stringBuffer1.length() > 0) {
/* 592 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 593 */       StringTokenizer stringTokenizer = new StringTokenizer(stringBuffer1.toString(), ",");
/* 594 */       while (stringTokenizer.hasMoreTokens()) {
/* 595 */         String str3 = stringTokenizer.nextToken();
/* 596 */         String str4 = departmentComInfo.getDeparmentcanceled(str3);
/* 597 */         if ("1".equalsIgnoreCase(str4))
/* 598 */           continue;  if (stringBuffer2.length() > 0) stringBuffer2.append(","); 
/* 599 */         stringBuffer2.append(str3);
/*     */       } 
/*     */     } else {
/* 602 */       stringBuffer2 = stringBuffer1;
/*     */     } 
/*     */     
/* 605 */     String str2 = stringBuffer2.toString();
/*     */     
/* 607 */     if (StringUtils.isNotBlank(str2)) {
/* 608 */       paramString2 = paramString2 + "," + str2;
/*     */     }
/* 610 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllParentDepartmentBlankNames(String paramString1, String paramString2) throws Exception {
/* 624 */     String str1 = "";
/* 625 */     String str2 = "";
/* 626 */     String str3 = "";
/* 627 */     String str4 = getSubcompanyid1(paramString1);
/* 628 */     if (str4.length() == 0) str4 = paramString2; 
/* 629 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 630 */     String str5 = "";
/* 631 */     str5 = SubCompanyComInfo.getAllParentSubcompanyId(str4, str5);
/* 632 */     str5 = str4 + str5;
/* 633 */     String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 634 */     for (int i = arrayOfString.length - 1; i >= 0; i--) {
/* 635 */       if (Util.null2String(subCompanyComInfo.getSubcompanyname(arrayOfString[i])).length() != 0) {
/* 636 */         if (str2.length() > 0) str2 = str2 + ">"; 
/* 637 */         str2 = str2 + subCompanyComInfo.getSubcompanyname(arrayOfString[i]);
/*     */       } 
/*     */     } 
/* 640 */     String str6 = "";
/* 641 */     str6 = getAllParentDepartId(paramString1, str6);
/* 642 */     str6 = paramString1 + str6;
/* 643 */     arrayOfString = Util.TokenizerString2(str6, ",");
/* 644 */     for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 645 */       if (Util.null2String(getDepartmentname(arrayOfString[j])).length() != 0) {
/* 646 */         if (str3.length() > 0) str3 = str3 + ">"; 
/* 647 */         str3 = str3 + getDepartmentname(arrayOfString[j]);
/*     */       } 
/* 649 */     }  if (str2.length() > 0 && str3.length() > 0) {
/* 650 */       str1 = str2 + "||" + str3;
/*     */     }
/*     */     
/* 653 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllParentDepartmentBlankNames(String paramString1, String paramString2, String paramString3) throws Exception {
/* 665 */     String str1 = "";
/* 666 */     String str2 = "";
/* 667 */     String str3 = "";
/* 668 */     String str4 = getSubcompanyid1(paramString1);
/* 669 */     if (str4.length() == 0) str4 = paramString2; 
/* 670 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 671 */     String str5 = "";
/* 672 */     str5 = SubCompanyComInfo.getAllParentSubcompanyId(str4, str5);
/* 673 */     str5 = str4 + str5;
/* 674 */     String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 675 */     for (int i = arrayOfString.length - 1; i >= 0; i--) {
/* 676 */       if (Util.null2String(subCompanyComInfo.getSubcompanyname(arrayOfString[i])).length() != 0) {
/* 677 */         if (str2.length() > 0) str2 = str2 + paramString3; 
/* 678 */         str2 = str2 + subCompanyComInfo.getSubcompanyname(arrayOfString[i]);
/*     */       } 
/*     */     } 
/* 681 */     String str6 = "";
/* 682 */     str6 = getAllParentDepartId(paramString1, str6);
/* 683 */     str6 = paramString1 + str6;
/* 684 */     arrayOfString = Util.TokenizerString2(str6, ",");
/* 685 */     for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 686 */       if (Util.null2String(getDepartmentname(arrayOfString[j])).length() != 0) {
/* 687 */         if (str3.length() > 0) str3 = str3 + paramString3; 
/* 688 */         str3 = str3 + getDepartmentname(arrayOfString[j]);
/*     */       } 
/* 690 */     }  if (str2.length() > 0 && str3.length() > 0) {
/* 691 */       str1 = str2 + paramString3 + str3;
/*     */     }
/*     */     
/* 694 */     return str1;
/*     */   }
/*     */   
/*     */   public String getAllParentDepartmentBlankNames(String paramString1, String paramString2, String paramString3, String paramString4, boolean paramBoolean) throws Exception {
/* 698 */     ArrayList<String> arrayList = new ArrayList();
/* 699 */     String str1 = getSubcompanyid1(paramString1);
/* 700 */     if (str1.length() == 0) str1 = paramString2; 
/* 701 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 702 */     String str2 = "";
/* 703 */     str2 = SubCompanyComInfo.getAllParentSubcompanyId(str1, str2);
/* 704 */     str2 = str1 + str2;
/* 705 */     String[] arrayOfString = Util.TokenizerString2(str2, ",");
/* 706 */     for (int i = arrayOfString.length - 1; i >= 0; i--) {
/* 707 */       if (Util.null2String(subCompanyComInfo.getSubcompanyname(arrayOfString[i])).length() != 0) {
/* 708 */         arrayList.add(subCompanyComInfo.getSubcompanyname(arrayOfString[i]));
/*     */       }
/*     */     } 
/* 711 */     String str3 = "";
/* 712 */     str3 = getAllParentDepartId(paramString1, str3);
/* 713 */     str3 = paramString1 + str3;
/* 714 */     arrayOfString = Util.TokenizerString2(str3, ",");
/* 715 */     for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 716 */       if (Util.null2String(getDepartmentname(arrayOfString[j])).length() != 0 && (paramBoolean || !arrayOfString[j].equals(paramString1)))
/* 717 */         arrayList.add(getDepartmentname(arrayOfString[j])); 
/*     */     } 
/* 719 */     if ("".equals(paramString4) || "0".equals(paramString4)) {
/* 720 */       return String.join(paramString3, (Iterable)arrayList);
/*     */     }
/* 722 */     Collections.reverse(arrayList);
/* 723 */     return String.join(paramString3, (Iterable)arrayList);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getAllParentDepartmentNames(String paramString1, String paramString2) throws Exception {
/* 728 */     String str1 = "";
/* 729 */     String str2 = getSubcompanyid1(paramString1);
/* 730 */     if (str2.length() == 0) str2 = paramString2; 
/* 731 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 732 */     String str3 = "";
/* 733 */     str3 = SubCompanyComInfo.getAllParentSubcompanyId(str2, str3);
/* 734 */     str3 = str2 + str3;
/* 735 */     String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 736 */     for (int i = arrayOfString.length - 1; i >= 0; i--) {
/* 737 */       if (Util.null2String(subCompanyComInfo.getSubCompanydesc(arrayOfString[i])).length() != 0) {
/* 738 */         if (str1.length() > 0) str1 = str1 + "<span style=\"color:#B8B8B8;\">/</span>";
/*     */         
/* 740 */         str1 = str1 + "<a href=\"javascript:void(0)\"  onclick=\"javascript:viewSubCompany(" + arrayOfString[i] + ");\" >" + subCompanyComInfo.getSubCompanydesc(arrayOfString[i]) + "</a>";
/*     */       } 
/*     */     } 
/* 743 */     String str4 = "";
/* 744 */     str4 = getAllParentDepartId(paramString1, str4);
/* 745 */     str4 = paramString1 + str4;
/* 746 */     arrayOfString = Util.TokenizerString2(str4, ",");
/* 747 */     for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 748 */       if (Util.null2String(getDepartmentname(arrayOfString[j])).length() != 0) {
/* 749 */         if (str1.length() > 0) str1 = str1 + "<span style=\"color:#B8B8B8;\">/</span>";
/*     */         
/* 751 */         str1 = str1 + "<a href=\"javascript:void(0)\"  onclick=\"javascript:viewDepartment(" + arrayOfString[j] + ");\" >" + getDepartmentname(arrayOfString[j]) + "</a>";
/*     */       } 
/*     */     } 
/* 754 */     return str1;
/*     */   }
/*     */   
/*     */   public String getAllParentDepartmentMarks(String paramString1, String paramString2) throws Exception {
/* 758 */     String str1 = "";
/* 759 */     String str2 = getSubcompanyid1(paramString1);
/* 760 */     if (str2.length() == 0) str2 = paramString2; 
/* 761 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 762 */     String str3 = "";
/* 763 */     str3 = SubCompanyComInfo.getAllParentSubcompanyId(str2, str3);
/* 764 */     str3 = str2 + str3;
/* 765 */     String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 766 */     for (int i = arrayOfString.length - 1; i >= 0; i--) {
/* 767 */       if (Util.null2String(subCompanyComInfo.getSubcompanyname(arrayOfString[i])).length() != 0) {
/* 768 */         if (str1.length() > 0) str1 = str1 + "<span style=\"color:#B8B8B8;\">/</span>";
/*     */         
/* 770 */         str1 = str1 + "<a href=\"javascript:void(0)\"  onclick=\"javascript:viewSubCompany(" + arrayOfString[i] + ");\" >" + subCompanyComInfo.getSubcompanyname(arrayOfString[i]) + "</a>";
/*     */       } 
/*     */     } 
/* 773 */     String str4 = "";
/* 774 */     str4 = getAllParentDepartId(paramString1, str4);
/* 775 */     str4 = paramString1 + str4;
/* 776 */     arrayOfString = Util.TokenizerString2(str4, ",");
/* 777 */     for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 778 */       if (Util.null2String(getDepartmentmark(arrayOfString[j])).length() != 0) {
/* 779 */         if (str1.length() > 0) str1 = str1 + "<span style=\"color:#B8B8B8;\">/</span>";
/*     */         
/* 781 */         str1 = str1 + "<a href=\"javascript:void(0)\"  onclick=\"javascript:viewDepartment(" + arrayOfString[j] + ");\" >" + getDepartmentmark(arrayOfString[j]) + "</a>";
/*     */       } 
/*     */     } 
/* 784 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentRealPath(String paramString) throws Exception {
/* 795 */     String str = "";
/*     */     try {
/* 797 */       String str1 = "";
/* 798 */       str1 = getAllParentDepartId(paramString, str1);
/* 799 */       str1 = paramString + str1;
/* 800 */       String[] arrayOfString = Util.TokenizerString2(str1, ",");
/* 801 */       for (int i = arrayOfString.length - 1; i >= 0; i--) {
/* 802 */         if (Util.null2String(getDepartmentname(arrayOfString[i])).length() != 0)
/* 803 */         { if (str.length() > 0) str = str + ">"; 
/* 804 */           str = str + getDepartmentname(arrayOfString[i]); } 
/*     */       } 
/* 806 */     } catch (Exception exception) {
/* 807 */       (new BaseBean()).writeLog("DepartmentComInfo>>>getDepartmentRealPath" + exception);
/*     */     } 
/* 809 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentRealPath(String paramString1, String paramString2, String paramString3) throws Exception {
/* 821 */     String str = "";
/* 822 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/* 824 */       String str1 = "";
/* 825 */       str1 = getAllParentDepartId(paramString1, str1);
/* 826 */       str1 = paramString1 + str1;
/* 827 */       String[] arrayOfString = Util.TokenizerString2(str1, ",");
/* 828 */       for (String str2 : arrayOfString) {
/* 829 */         String str3 = getDepartmentmark(str2);
/* 830 */         if (!"".equals(str3)) {
/* 831 */           arrayList.add(str3);
/*     */         }
/*     */       } 
/* 834 */       if ("0".equals(paramString3))
/* 835 */         Collections.reverse(arrayList); 
/* 836 */     } catch (Exception exception) {
/* 837 */       (new BaseBean()).writeLog("DepartmentComInfo>>>getDepartmentRealPath" + exception);
/*     */     } 
/* 839 */     return String.join(paramString2, (Iterable)arrayList);
/*     */   }
/*     */   
/*     */   public String getDepartmentRealPath(String paramString1, String paramString2, String paramString3, boolean paramBoolean) throws Exception {
/* 843 */     String str = "";
/* 844 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/* 846 */       String str1 = "";
/* 847 */       str1 = getAllParentDepartId(paramString1, str1);
/* 848 */       str1 = paramString1 + str1;
/* 849 */       String[] arrayOfString = Util.TokenizerString2(str1, ",");
/* 850 */       for (String str2 : arrayOfString) {
/* 851 */         if (paramBoolean || !str2.equals(paramString1)) {
/*     */           
/* 853 */           String str3 = getDepartmentname(str2);
/* 854 */           if (!"".equals(str3))
/* 855 */             arrayList.add(str3); 
/*     */         } 
/*     */       } 
/* 858 */       if ("0".equals(paramString3))
/* 859 */         Collections.reverse(arrayList); 
/* 860 */     } catch (Exception exception) {
/* 861 */       (new BaseBean()).writeLog("DepartmentComInfo>>>getDepartmentRealPath" + exception);
/*     */     } 
/* 863 */     return String.join(paramString2, (Iterable)arrayList);
/*     */   }
/*     */   
/*     */   public String getDepartmentNames(String paramString) {
/* 867 */     String str = "";
/* 868 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 869 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 870 */       str = str + getDepartmentname(arrayOfString[b]) + ",";
/*     */     }
/* 872 */     if (!"".equals(str)) str = str.substring(0, str.length() - 1); 
/* 873 */     return str;
/*     */   }
/*     */   
/*     */   public String getDepartmentName(String paramString) throws Exception {
/* 877 */     String str1 = getDepartmentmark(paramString);
/* 878 */     String str2 = Util.null2String(getDeparmentcanceled(paramString));
/* 879 */     if ("1".equals(str2)) {
/* 880 */       str1 = str1 + "<span><font color=\"red\">(" + SystemEnv.getHtmlLabelName(22205, ThreadVarLanguage.getLang()) + ")</font></span>";
/*     */     }
/*     */     
/* 883 */     return str1;
/*     */   }
/*     */   
/*     */   public ArrayList<String> getDepartmentOperate(String paramString1, String paramString2, String paramString3) {
/* 887 */     ArrayList<String> arrayList = new ArrayList();
/* 888 */     String str1 = paramString3.split(":")[0];
/* 889 */     String str2 = paramString3.split(":")[0];
/* 890 */     String str3 = paramString3.split(":")[1];
/*     */     
/* 892 */     if (paramString2.equals("true")) {
/* 893 */       arrayList.add("true");
/*     */     } else {
/* 895 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 898 */     if (str1.equals("true")) {
/* 899 */       arrayList.add(getDepartmentCanCancel(paramString1));
/*     */     } else {
/* 901 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 904 */     if (str2.equals("true")) {
/* 905 */       arrayList.add(getDepartmentCanIsCancel(paramString1));
/*     */     } else {
/* 907 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 910 */     if (paramString2.equals("true")) {
/* 911 */       arrayList.add("true");
/* 912 */       arrayList.add("true");
/* 913 */       arrayList.add("true");
/*     */     } 
/*     */     
/* 916 */     if (str3.equals("true")) {
/* 917 */       arrayList.add("true");
/*     */     } else {
/* 919 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 922 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDepartmentCanCancel(String paramString) {
/* 926 */     RecordSet recordSet = new RecordSet();
/* 927 */     String str = "";
/* 928 */     recordSet.executeSql("select canceled from HrmDepartment where id=" + paramString);
/* 929 */     if (recordSet.next()) {
/* 930 */       str = Util.null2String(recordSet.getString("canceled"));
/*     */     }
/* 932 */     if (str.equals("0") || str.equals("")) {
/* 933 */       return "true";
/*     */     }
/* 935 */     return "false";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDepartmentCanIsCancel(String paramString) {
/* 940 */     RecordSet recordSet = new RecordSet();
/* 941 */     String str = "";
/* 942 */     recordSet.executeSql("select canceled from HrmDepartment where id=" + paramString);
/* 943 */     if (recordSet.next()) {
/* 944 */       str = Util.null2String(recordSet.getString("canceled"));
/*     */     }
/* 946 */     if (str.equals("1")) {
/* 947 */       return "true";
/*     */     }
/* 949 */     return "false";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDepartmentCheckbox(String paramString) {
/* 954 */     RecordSet recordSet = new RecordSet();
/* 955 */     recordSet.executeSql("select * from hrmresource where departmentid= " + paramString);
/* 956 */     if (recordSet.next()) {
/* 957 */       return "false";
/*     */     }
/* 959 */     return "true";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDepartmentsForEdit(String paramString) throws Exception {
/* 964 */     JSONArray jSONArray = new JSONArray();
/* 965 */     String str = "";
/* 966 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 967 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 968 */     for (byte b = 0; arrayOfString != null && b < arrayOfString.length; b++) {
/* 969 */       String str1 = arrayOfString[b];
/* 970 */       str = departmentComInfo.getDepartmentmark(str1) + "&nbsp;";
/* 971 */       JSONObject jSONObject = new JSONObject();
/* 972 */       jSONObject.put("browserValue", str1);
/* 973 */       jSONObject.put("browserSpanValue", str);
/* 974 */       jSONArray.add(jSONObject);
/*     */     } 
/* 976 */     return jSONArray.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/company/DepartmentComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */