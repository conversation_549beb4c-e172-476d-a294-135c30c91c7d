/*    */ package weaver.hrm.company;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CompanyComInfo
/*    */   extends CacheBase
/*    */ {
/* 13 */   protected static String TABLE_NAME = "hrmcompany";
/*    */   
/* 15 */   protected static String TABLE_WHERE = null;
/*    */   
/* 17 */   protected static String TABLE_ORDER = "id";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 20 */   protected static String PK_NAME = "id";
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @CacheColumn(name = "companyname")
/*    */   protected static int name;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getCompanyNum() {
/* 33 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public boolean next(String paramString) {
/* 43 */     return false;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCompanyid() {
/* 51 */     return (String)getRowValue(0);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCompanyname() {
/* 59 */     return (String)getRowValue(name);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCompanyname(String paramString) {
/* 69 */     return (String)getValue(name, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeCompanyCache() {
/* 76 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/company/CompanyComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */