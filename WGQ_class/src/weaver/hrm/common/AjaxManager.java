/*     */ package weaver.hrm.common;
/*     */ 
/*     */ import com.api.hrm.util.ServiceUtil;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.ServletContext;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import ln.LN;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.MessageUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.manager.HrmPaidLeaveTimeManager;
/*     */ import weaver.hrm.authority.domain.HrmTransferSet;
/*     */ import weaver.hrm.authority.manager.HrmPostManager;
/*     */ import weaver.hrm.authority.manager.HrmResourceManager;
/*     */ import weaver.hrm.authority.manager.HrmTransferLogManager;
/*     */ import weaver.hrm.authority.manager.HrmTransferSetManager;
/*     */ import weaver.hrm.autotask.domain.HrmUsbAutoDate;
/*     */ import weaver.hrm.autotask.manager.HrmUsbAutoDateManager;
/*     */ import weaver.hrm.career.manager.HrmCareerApplyManager;
/*     */ import weaver.hrm.chart.domain.HrmChartSet;
/*     */ import weaver.hrm.chart.domain.HrmCompanyVirtual;
/*     */ import weaver.hrm.chart.manager.HrmChartSetManager;
/*     */ import weaver.hrm.chart.manager.HrmCompanyVirtualManager;
/*     */ import weaver.hrm.chart.manager.OrgChartManager;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.online.HrmUserOnlineMap;
/*     */ import weaver.hrm.passwordprotection.domain.HrmResource;
/*     */ import weaver.hrm.passwordprotection.manager.HrmPasswordProtectionQuestionManager;
/*     */ import weaver.hrm.passwordprotection.manager.HrmPasswordProtectionSetManager;
/*     */ import weaver.hrm.passwordprotection.manager.HrmResourceManager;
/*     */ import weaver.hrm.passwordprotection.manager.HrmResourceManagerManager;
/*     */ import weaver.hrm.report.manager.HrmRpSubTemplateManager;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsDetail;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleWorktime;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*     */ import weaver.hrm.schedule.manager.HrmSchedulePersonnelManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsDetailManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleWorktimeManager;
/*     */ import weaver.login.LicenseCheckLogin;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class AjaxManager
/*     */ {
/*  72 */   private static final RecordSet RS = new RecordSet();
/*     */   
/*  74 */   private static StaticObj staticobj = StaticObj.getInstance();
/*     */   public static String getData(HttpServletRequest paramHttpServletRequest) {
/*  76 */     return getData(paramHttpServletRequest, (ServletContext)null);
/*     */   }
/*     */   
/*     */   public static String getData(HttpServletRequest paramHttpServletRequest, ServletContext paramServletContext) {
/*  80 */     return getData("", paramHttpServletRequest, paramServletContext);
/*     */   }
/*     */   
/*     */   public static String getData(String paramString, HttpServletRequest paramHttpServletRequest, ServletContext paramServletContext) {
/*  84 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/*  85 */     return StringUtil.isNull(str) ? "" : proc(paramString, str, paramHttpServletRequest, paramServletContext);
/*     */   }
/*     */   
/*     */   public static String getData(String paramString1, String paramString2) {
/*  89 */     String str1 = "";
/*  90 */     paramString1 = StringUtil.vString(paramString1);
/*  91 */     paramString2 = StringUtil.vString(paramString2);
/*  92 */     String[] arrayOfString = paramString2.split(";");
/*  93 */     if (arrayOfString == null || arrayOfString.length != 2) return ""; 
/*  94 */     String str2 = StringUtil.vString(arrayOfString[0]);
/*  95 */     String str3 = StringUtil.vString(arrayOfString[1]);
/*  96 */     if (str2.equals("getHrmChoiceImage")) {
/*  97 */       String[] arrayOfString1 = StringUtil.split(str3, "+");
/*  98 */       StringBuffer stringBuffer = new StringBuffer();
/*  99 */       for (String str : arrayOfString1) {
/* 100 */         stringBuffer.append(str);
/*     */       }
/* 102 */       String[] arrayOfString2 = stringBuffer.toString().split(",");
/* 103 */       if (arrayOfString2.length == 2) {
/* 104 */         String str = DateUtil.getCurrentDate();
/* 105 */         if ((str.compareTo(arrayOfString1[0]) >= 0 || StringUtil.isNull(arrayOfString1[0])) && (str.compareTo(arrayOfString1[1]) <= 0 || StringUtil.isNull(arrayOfString1[1]))) {
/* 106 */           str1 = "<img src='/images/BacoCheck.gif'>";
/*     */         }
/*     */       } 
/* 109 */       if (HrmUserVarify.isUserOnline(paramString1)) {
/* 110 */         str1 = str1 + "<img src='/images/State_LoggedOn.gif'>";
/*     */       }
/* 112 */     } else if (str2.equals("getTResourceName")) {
/* 113 */       RS.executeSql("select 1 from HrmResourceManager where loginid = '" + paramString1 + "'");
/* 114 */       if (RS.next()) str1 = "HrmResourceManager";
/*     */       
/* 116 */       str1 = Tools.vString(str1, str3);
/* 117 */     } else if (str2.equals("getAccountType")) {
/* 118 */       RS.executeSql("select accounttype from HrmResource where id = " + paramString1);
/* 119 */       if (RS.next()) str1 = RS.getString(1);
/*     */       
/* 121 */       str1 = Tools.vString(str1, str3);
/* 122 */     } else if (str2.equals("getLnScCount")) {
/* 123 */       str1 = getLnScResult(str3);
/* 124 */     } else if (str2.equals("getSchedulePersonSql")) {
/* 125 */       HrmSchedulePersonnelManager hrmSchedulePersonnelManager = new HrmSchedulePersonnelManager();
/* 126 */       str1 = " and hr.id in (" + hrmSchedulePersonnelManager.getPersonIds() + ")";
/*     */     } 
/* 128 */     return str1;
/*     */   }
/*     */ 
/*     */   
/* 132 */   public static Map<String, Long> timeMap = null;
/*     */   
/*     */   private static String proc(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, ServletContext paramServletContext) {
/* 135 */     StringBuffer stringBuffer = new StringBuffer();
/* 136 */     JSONObject jSONObject = new JSONObject();
/* 137 */     String str1 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("id"));
/* 138 */     String str2 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg"));
/* 139 */     User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/* 140 */     if (paramString2.equalsIgnoreCase("getUseDemand"))
/* 141 */     { stringBuffer.append(getUseDemand(str1)); }
/* 142 */     else if (paramString2.equalsIgnoreCase("getPlanIdByApplyId"))
/* 143 */     { HrmCareerApplyManager hrmCareerApplyManager = new HrmCareerApplyManager();
/* 144 */       stringBuffer.append(hrmCareerApplyManager.findPlanIdByApplyId(str1)); }
/* 145 */     else if (paramString2.equalsIgnoreCase("HrmResourceMultiSelect"))
/* 146 */     { String str = "";
/* 147 */       if (paramString1.equalsIgnoreCase("g_d")) {
/* 148 */         str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("departmentid"));
/* 149 */         stringBuffer.append(getAllDeptId(str, str));
/* 150 */       } else if (paramString1.equalsIgnoreCase("g_s")) {
/* 151 */         str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("subcompanyid"));
/* 152 */         stringBuffer.append(getAllSubId(str, str));
/*     */       }  }
/* 154 */     else if (paramString2.equalsIgnoreCase("getSelectAllId"))
/* 155 */     { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("sql"));
/* 156 */       String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("type"));
/* 157 */       stringBuffer.append(getSelectAllIds(str3, str4)); }
/* 158 */     else if (paramString2.equals("checkHrmReportTemplateName"))
/* 159 */     { String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("name"));
/* 160 */       if (str.length() == 0) {
/* 161 */         stringBuffer.append("-1");
/*     */       } else {
/* 163 */         HrmRpSubTemplateManager hrmRpSubTemplateManager = new HrmRpSubTemplateManager();
/* 164 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 165 */         hashMap.put("name", str);
/* 166 */         hashMap.put("author", StringUtil.getURLDecode(paramHttpServletRequest.getParameter("author")));
/* 167 */         List list = hrmRpSubTemplateManager.find(hashMap);
/* 168 */         stringBuffer.append((list == null || list.size() == 0) ? "0" : "1");
/*     */       }  }
/* 170 */     else if (paramString2.equals("cehckScheduleSignDataSourceSet"))
/* 171 */     { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("fromdate"));
/* 172 */       String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("enddate"));
/* 173 */       stringBuffer.append(String.valueOf((DateUtil.compDate(str3, str4) >= 0))); }
/* 174 */     else if (paramString2.equalsIgnoreCase("getHrmAward"))
/* 175 */     { stringBuffer.append(getHrmAward(str1)); }
/* 176 */     else if (paramString2.equalsIgnoreCase("checkLoginId"))
/* 177 */     { String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("resourceid"));
/* 178 */       stringBuffer.append(checkLoginId(str1, str)); }
/* 179 */     else if (paramString2.equalsIgnoreCase("jsonRemove"))
/* 180 */     { String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("jsonSql"));
/* 181 */       MJson mJson = new MJson(str, true);
/* 182 */       mJson.removeArrayValue(str1);
/* 183 */       stringBuffer.append(StringUtil.getURLEncode(mJson.toString())); }
/* 184 */     else if (paramString2.equalsIgnoreCase("getTransferData"))
/* 185 */     { if (HrmUserVarify.checkUserRight("HrmRrightTransfer:Tran", user) || HrmUserVarify.checkUserRight("HrmRrightAuthority:search", user)) {
/* 186 */         String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("isAll"));
/* 187 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("fromid"));
/* 188 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("idStr"));
/* 189 */         String str6 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("key"));
/* 190 */         String str7 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("jsonSql"));
/* 191 */         MJson mJson = new MJson(str7, true);
/* 192 */         String str8 = "", str9 = "";
/* 193 */         while (mJson.next()) {
/* 194 */           str8 = mJson.getKey();
/* 195 */           if (str8.equalsIgnoreCase(str6)) {
/* 196 */             str9 = mJson.getValue();
/*     */             break;
/*     */           } 
/*     */         } 
/* 200 */         if (str3.equals("0") && StringUtil.isNull(str5) && StringUtil.isNotNull(str9)) {
/* 201 */           RS.executeSql(str9);
/* 202 */           for (; RS.next(); str5 = str5 + StringUtil.vString(RS.getString("id")) + ",");
/* 203 */           if (str5.endsWith(",")) str5 = str5.substring(0, str5.length() - 1); 
/*     */         } 
/* 205 */         if (str3.equals("1")) str5 = ""; 
/* 206 */         if (str1.equalsIgnoreCase("T203")) {
/* 207 */           HrmResourceManager hrmResourceManager = new HrmResourceManager();
/* 208 */           stringBuffer.append(hrmResourceManager.getAllNum("department", str1, str4, str5));
/* 209 */         } else if (str1.equalsIgnoreCase("C302")) {
/* 210 */           HrmPostManager hrmPostManager = new HrmPostManager();
/* 211 */           stringBuffer.append(hrmPostManager.getAllNum("subcompany", str1, str4, str5));
/*     */         } 
/*     */       }  }
/* 214 */     else if (paramString2.equalsIgnoreCase("checkFromid"))
/* 215 */     { HashMap<Object, Object> hashMap = new HashMap<>();
/* 216 */       hashMap.put("fromid", str1);
/* 217 */       hashMap.put("pStatus", "0");
/* 218 */       List list = (new HrmTransferLogManager()).find(hashMap);
/* 219 */       stringBuffer.append((list == null || list.size() == 0) ? "0" : "1"); }
/* 220 */     else if (paramString2.equalsIgnoreCase("networkIsConnection"))
/* 221 */     { stringBuffer.append(StringUtil.networkIsConnection()); }
/* 222 */     else if (paramString2.equalsIgnoreCase("getData"))
/* 223 */     { String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("jsonSql"));
/* 224 */       if (StringUtil.isNotNull(str)) {
/*     */         try {
/* 226 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 227 */           hashMap.put("jsonSql", ExtUtil.vString(str));
/* 228 */           List list = (new HrmTransferSetManager()).find(hashMap);
/* 229 */           if (list != null) {
/* 230 */             for (HrmTransferSet hrmTransferSet : list) {
/* 231 */               stringBuffer.append(hrmTransferSet.getName()).append(";").append(";;;");
/*     */             }
/*     */           }
/* 234 */         } catch (Exception exception) {
/* 235 */           stringBuffer.append(exception.toString());
/*     */         } 
/*     */       } }
/* 238 */     else if (paramString2.equalsIgnoreCase("ppset"))
/* 239 */     { HrmPasswordProtectionSetManager hrmPasswordProtectionSetManager = new HrmPasswordProtectionSetManager();
/* 240 */       String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("checked"));
/* 241 */       hrmPasswordProtectionSetManager.set(StringUtil.parseToLong(str1), Boolean.valueOf(str).booleanValue()); }
/* 242 */     else if (paramString2.equalsIgnoreCase("verifyPswd"))
/* 243 */     { String str = Util.getEncrypt(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("pswd")));
/* 244 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 245 */       hashMap.put("id", str1);
/* 246 */       hashMap.put("password", str);
/* 247 */       boolean bool = ((new HrmResourceManager()).get(hashMap) != null) ? true : false;
/* 248 */       if (!bool) {
/* 249 */         bool = ((new HrmResourceManagerManager()).get(hashMap) != null) ? true : false;
/*     */       }
/*     */       try {
/* 252 */         jSONObject.put("result", String.valueOf(bool));
/* 253 */       } catch (JSONException jSONException) {}
/* 254 */       stringBuffer.append(jSONObject.toString()); }
/* 255 */     else { if (paramString2.equalsIgnoreCase("insertQuestion"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 300 */       if (paramString2.equalsIgnoreCase("userOffline"))
/* 301 */       { String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("uid"));
/* 302 */         (new LicenseCheckLogin()).userOffline(str);
/* 303 */         Map map = (paramServletContext == null) ? null : (Map)paramServletContext.getAttribute("userSessions");
/* 304 */         List<HttpSession> list = (map != null && map.containsKey(str)) ? (List)map.get(str) : null;
/* 305 */         byte b1 = (list == null) ? 0 : list.size();
/* 306 */         for (byte b2 = 0; b2 < b1; b2++) { 
/* 307 */           try { ((HttpSession)list.get(b2)).setAttribute("userOffline", "1"); } catch (IllegalStateException illegalStateException) {} }
/*     */ 
/*     */         
/* 310 */         HrmUserOnlineMap.getInstance().userOffline(str); }
/*     */       
/* 312 */       else if (paramString2.equalsIgnoreCase("isOffline"))
/* 313 */       { stringBuffer.append("1"); }
/* 314 */       else if (paramString2.equalsIgnoreCase("forgotPasswordCheck"))
/* 315 */       { String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("loginid"));
/* 316 */         stringBuffer.append(checkLoginId(str, null, true)); }
/* 317 */       else if (paramString2.equalsIgnoreCase("sendSMS"))
/* 318 */       { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("receiver"));
/* 319 */         if (StringUtil.isNotNull(str3)) str3 = StringUtil.decode(str3); 
/* 320 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("content"));
/* 321 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("loginid"));
/* 322 */         String str6 = "";
/* 323 */         boolean bool = false;
/* 324 */         HrmPasswordProtectionSetManager hrmPasswordProtectionSetManager = new HrmPasswordProtectionSetManager();
/* 325 */         if (StringUtil.isNotNull(str1) && !str1.equals("0")) {
/* 326 */           str6 = hrmPasswordProtectionSetManager.getRandomPassword();
/* 327 */           str4 = StringUtil.replace(Constants.PASSWORD_MESSSAGE, "{pswd}", str6);
/* 328 */           bool = true;
/*     */         } 
/* 330 */         boolean bool1 = MessageUtil.sendSMS(str3, str4);
/* 331 */         if (bool1 && bool) hrmPasswordProtectionSetManager.changePassword(str1, str5, str6); 
/* 332 */         stringBuffer.append(bool1); }
/* 333 */       else if (paramString2.equalsIgnoreCase("sendEmail"))
/* 334 */       { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("receiver"));
/* 335 */         if (StringUtil.isNotNull(str3)) str3 = StringUtil.decode(str3); 
/* 336 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("subject"));
/* 337 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("content"));
/* 338 */         String str6 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("loginid"));
/* 339 */         if (StringUtil.isNull(str4)) str4 = "E-cology" + SystemEnv.getHtmlLabelName(83510, ThreadVarLanguage.getLang()) + ""; 
/* 340 */         String str7 = "";
/* 341 */         boolean bool = false;
/* 342 */         HrmPasswordProtectionSetManager hrmPasswordProtectionSetManager = new HrmPasswordProtectionSetManager();
/* 343 */         if (StringUtil.isNotNull(str1) && !str1.equals("0")) {
/* 344 */           str7 = hrmPasswordProtectionSetManager.getRandomPassword();
/* 345 */           str5 = StringUtil.replace(Constants.PASSWORD_MESSSAGE, "{pswd}", str7);
/* 346 */           bool = true;
/*     */         } 
/* 348 */         boolean bool1 = MessageUtil.sendEmail(str3, str4, str5);
/* 349 */         if (bool1 && bool) hrmPasswordProtectionSetManager.changePassword(str1, str6, str7); 
/* 350 */         stringBuffer.append(bool1); }
/* 351 */       else if (paramString2.equalsIgnoreCase("verifyQuestion"))
/* 352 */       { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("loginid"));
/* 353 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("qid"));
/* 354 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("answer"));
/* 355 */         HrmPasswordProtectionQuestionManager hrmPasswordProtectionQuestionManager = new HrmPasswordProtectionQuestionManager();
/* 356 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 357 */         hashMap.put("sql_userId", "and t.user_id in (select id from " + getData(str3, "getTResourceName;HrmResource") + " where loginid = '" + str3 + "') ");
/* 358 */         hashMap.put("id", str4);
/* 359 */         hashMap.put("answer", str5);
/* 360 */         List list = hrmPasswordProtectionQuestionManager.find(hashMap);
/* 361 */         stringBuffer.append((list != null && list.size() > 0)); }
/* 362 */       else if (paramString2.equalsIgnoreCase("saveNewPassword"))
/* 363 */       { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("loginid"));
/* 364 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("newpswd"));
/* 365 */         (new HrmPasswordProtectionSetManager()).changePassword(str1, str3, str4); }
/* 366 */       else if (paramString2.equalsIgnoreCase("saveUsbAutoDate"))
/* 367 */       { HrmUsbAutoDateManager hrmUsbAutoDateManager = new HrmUsbAutoDateManager();
/* 368 */         long l = StringUtil.parseToLong(paramHttpServletRequest.getParameter("arg0"));
/* 369 */         boolean bool = Boolean.valueOf(StringUtil.vString(paramHttpServletRequest.getParameter("arg1"))).booleanValue();
/* 370 */         String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg2"));
/* 371 */         int i = StringUtil.parseToInt(paramHttpServletRequest.getParameter("arg3"));
/*     */         
/* 373 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 374 */         hashMap.put("userId", Long.valueOf(l));
/*     */         
/* 376 */         boolean bool1 = false;
/* 377 */         HrmUsbAutoDate hrmUsbAutoDate = (HrmUsbAutoDate)hrmUsbAutoDateManager.get(hashMap);
/* 378 */         if (hrmUsbAutoDate == null) {
/* 379 */           bool1 = true;
/* 380 */           hrmUsbAutoDate = new HrmUsbAutoDate();
/* 381 */           hrmUsbAutoDate.setUserId(Long.valueOf(l));
/*     */         } 
/* 383 */         hrmUsbAutoDate.setNeedAuto(Integer.valueOf(bool ? 1 : 0));
/* 384 */         hrmUsbAutoDate.setEnableDate(str);
/* 385 */         hrmUsbAutoDate.setEnableUsbType(Integer.valueOf(i));
/*     */         
/* 387 */         if (bool1) { hrmUsbAutoDateManager.insert(hrmUsbAutoDate); }
/* 388 */         else { hrmUsbAutoDateManager.update(hrmUsbAutoDate); }  }
/* 389 */       else if (paramString2.equalsIgnoreCase("saveAdminUsbSet"))
/* 390 */       { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg0"));
/* 391 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg1"));
/* 392 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg2"));
/* 393 */         String str6 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg3"));
/* 394 */         String str7 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg4"));
/* 395 */         String str8 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg5"));
/*     */         
/* 397 */         RS.executeSql("UPDATE HRMRESOURCEMANAGER SET" + " userUsbType = '" + 
/*     */             
/* 399 */             str4 + "'," + " usbstate = '" + 
/* 400 */             str5 + "'," + " mobile = '" + 
/* 401 */             str6 + "'," + " serial = '" + 
/* 402 */             str7 + "'," + " tokenKey = '" + 
/* 403 */             str8 + "'" + " where id = " + 
/* 404 */             str3);
/*     */          }
/*     */       
/* 407 */       else if (paramString2.equalsIgnoreCase("savect"))
/* 408 */       { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg0"));
/* 409 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg1"));
/* 410 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg2"));
/* 411 */         String str6 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg3"));
/* 412 */         HrmChartSetManager hrmChartSetManager = new HrmChartSetManager();
/* 413 */         HrmChartSet hrmChartSet = (HrmChartSet)hrmChartSetManager.get(str3);
/* 414 */         boolean bool = false;
/* 415 */         if (hrmChartSet == null || hrmChartSet.getId().intValue() <= 0) {
/* 416 */           hrmChartSet = new HrmChartSet();
/* 417 */           hrmChartSet.setIsSys(Integer.valueOf(1));
/* 418 */           hrmChartSet.setAuthor(Integer.valueOf(1));
/* 419 */           bool = true;
/*     */         } 
/* 421 */         hrmChartSet.setShowType(Integer.valueOf(StringUtil.parseToInt(str4, 1)));
/* 422 */         hrmChartSet.setShowNum(Integer.valueOf(StringUtil.parseToInt(str5, 1)));
/* 423 */         hrmChartSet.setShowMode(str6);
/* 424 */         if (bool) {
/* 425 */           hrmChartSetManager.insert(hrmChartSet);
/*     */         } else {
/* 427 */           hrmChartSetManager.update(hrmChartSet);
/*     */         }  }
/* 429 */       else if (paramString2.equalsIgnoreCase("initChart"))
/* 430 */       { String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg0"));
/* 431 */         int i = StringUtil.parseToInt(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg1")), 1);
/* 432 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg2"));
/* 433 */         boolean bool = Boolean.valueOf(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg3"))).booleanValue();
/* 434 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg4"));
/* 435 */         String str6 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg5"));
/* 436 */         String str7 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg6"));
/* 437 */         String str8 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg7"));
/* 438 */         String str9 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg8"));
/* 439 */         String str10 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg9"));
/* 440 */         String str11 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg10"));
/* 441 */         String str12 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg11"));
/*     */         
/* 443 */         HrmCompanyVirtualManager hrmCompanyVirtualManager = new HrmCompanyVirtualManager();
/*     */         
/* 445 */         HrmCompanyVirtual hrmCompanyVirtual = null;
/* 446 */         if (!bool) {
/* 447 */           hrmCompanyVirtual = (HrmCompanyVirtual)hrmCompanyVirtualManager.get(str3);
/* 448 */           str4 = (hrmCompanyVirtual != null) ? hrmCompanyVirtual.getVirtualtype() : "";
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 512 */         (new ServiceUtil()).initOrgLevel();
/*     */         
/* 514 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 515 */         hashMap1.put("status", str5);
/* 516 */         hashMap1.put("docStatus", str6);
/* 517 */         hashMap1.put("customerType", str7);
/* 518 */         hashMap1.put("customerStatus", str8);
/* 519 */         hashMap1.put("workType", str9);
/* 520 */         hashMap1.put("projectStatus", str10);
/*     */         
/* 522 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 523 */         hashMap2.put("cmd", str11);
/* 524 */         hashMap2.put("sorgid", str3);
/*     */         
/* 526 */         OrgChartManager orgChartManager = new OrgChartManager();
/* 527 */         orgChartManager.setShownum(i);
/* 528 */         orgChartManager.init(user, paramHttpServletRequest, hashMap1, str12, bool, hrmCompanyVirtual, hashMap2);
/* 529 */         String str13 = String.valueOf(orgChartManager.getCompanyResourceCount());
/* 530 */         String str14 = str4 + SystemEnv.getHtmlLabelNames(orgChartManager.getLabelIndex() + ",523", user.getLanguage()) + str13;
/*     */         
/* 532 */         String str15 = "/hrm/HrmTab.jsp?_fromURL=HrmResourceSearchResult&from=hrmorg&virtualtype=" + str3;
/* 533 */         StringBuffer stringBuffer1 = new StringBuffer("[");
/* 534 */         stringBuffer1.append("{\"id\":\"0\", \"pid\":\"\", \"type\":\"company\", \"name\":\"").append(StringUtil.vString(str4, 12)).append("\", \"title\":\"").append(str4).append("\", \"num\":\"" + str13 + "\", \"nTitle\":\"" + str14 + "\", \"oDisplay\":\"none\", \"subRCount\":\"0\", \"subTitle\":\"\", \"cOnclick\":\"" + str15 + "\", \"sOnclick\":\"\"}").append(orgChartManager.getData("0"));
/* 535 */         stringBuffer1.append("]");
/* 536 */         stringBuffer.append(stringBuffer1.toString()); }
/* 537 */       else if (paramString2.equals("getSubcompanyLimitUsers"))
/* 538 */       { String str = "0";
/*     */         try {
/* 540 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 541 */           str = StringUtil.vString(subCompanyComInfo.getSupsubcomid(str2), "0");
/* 542 */         } catch (Exception exception) {}
/* 543 */         int i = 0, j = 0, k = 0, m = 0, n = 0, i1 = 0;
/* 544 */         LN lN = new LN();
/* 545 */         lN.CkHrmnum();
/* 546 */         i1 = StringUtil.parseToInt(lN.getHrmnum(), 0);
/* 547 */         HrmResourceManager hrmResourceManager = new HrmResourceManager();
/* 548 */         if (str.equals("0")) {
/* 549 */           j = i1;
/*     */         } else {
/* 551 */           j = hrmResourceManager.getSupNum(str2);
/*     */         } 
/* 553 */         k = hrmResourceManager.getAllNum(str2);
/* 554 */         Map map = hrmResourceManager.getSubNum(str2);
/* 555 */         if (map.containsKey("num")) i = ((Integer)map.get("num")).intValue(); 
/* 556 */         if (map.containsKey("selfNum")) n = ((Integer)map.get("selfNum")).intValue(); 
/* 557 */         if (map.containsKey("subNum")) m = ((Integer)map.get("subNum")).intValue(); 
/* 558 */         stringBuffer.append("[{")
/* 559 */           .append("supid:'").append(str).append("',")
/* 560 */           .append("num:'").append(i).append("',")
/* 561 */           .append("lNum:'").append(i1).append("',")
/* 562 */           .append("selfNum:'").append(n).append("',")
/* 563 */           .append("supNum:'").append(j).append("',")
/* 564 */           .append("allNum:'").append(k).append("',")
/* 565 */           .append("subNum:'").append(m).append("'")
/* 566 */           .append("}]"); }
/* 567 */       else if (paramString2.equalsIgnoreCase("noMore"))
/* 568 */       { stringBuffer.append((new HrmResourceManager()).noMore(str2)); }
/* 569 */       else if (paramString2.equalsIgnoreCase("checkNewDeptUsers"))
/* 570 */       { String str3 = "";
/* 571 */         String str4 = "";
/* 572 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("currentUserId"));
/*     */         try {
/* 574 */           str3 = (new DepartmentComInfo()).getSubcompanyid1(str2);
/* 575 */           str4 = (new ResourceComInfo()).getSubCompanyID(str5);
/* 576 */         } catch (Exception exception) {}
/*     */         
/* 578 */         if (str4.equals(str3)) {
/* 579 */           stringBuffer.append(false);
/*     */         } else {
/* 581 */           stringBuffer.append((new HrmResourceManager()).noMore(str3));
/*     */         }  }
/* 583 */       else if (paramString2.equalsIgnoreCase("getLnScCount"))
/* 584 */       { stringBuffer.append(getLnScResult(str2)); }
/* 585 */       else if (paramString2.equalsIgnoreCase("checkBeforeShowMobileSignData"))
/* 586 */       { StringBuffer stringBuffer1 = new StringBuffer();
/* 587 */         List<String> list1 = Arrays.asList((new HrmResourceManager()).getSubResourceIds(str2).split(","));
/* 588 */         List<String> list2 = Arrays.asList(str1.split(","));
/* 589 */         ResourceComInfo resourceComInfo = null; 
/* 590 */         try { resourceComInfo = new ResourceComInfo(); } catch (Exception exception) {}
/* 591 */         if (list2 != null && list2.size() > 0) {
/* 592 */           for (String str : list2) {
/* 593 */             if (str.equals(str2) || list1.indexOf(str) != -1)
/* 594 */               continue;  stringBuffer1.append(StringUtil.isNull(stringBuffer1.toString()) ? "" : "，").append((resourceComInfo == null) ? "" : resourceComInfo.getResourcename(str));
/*     */           } 
/*     */         }
/* 597 */         stringBuffer.append("[{")
/* 598 */           .append("isShow:'").append(StringUtil.isNull(stringBuffer1.toString()) ? "true" : "false").append("',")
/* 599 */           .append("absensemessage:'").append(StringUtil.isNull(stringBuffer1.toString()) ? "" : ("" + SystemEnv.getHtmlLabelName(10003636, ThreadVarLanguage.getLang()) + "" + stringBuffer1.toString() + "】" + SystemEnv.getHtmlLabelName(10003637, ThreadVarLanguage.getLang()) + "")).append("',")
/* 600 */           .append("message:'").append(StringUtil.isNull(stringBuffer1.toString()) ? "" : ("" + SystemEnv.getHtmlLabelName(10003636, ThreadVarLanguage.getLang()) + "" + stringBuffer1.toString() + "】" + SystemEnv.getHtmlLabelName(10003638, ThreadVarLanguage.getLang()) + "")).append("'")
/* 601 */           .append("}]"); }
/* 602 */       else if (paramString2.equalsIgnoreCase("compareTime"))
/* 603 */       { stringBuffer.append((new HrmScheduleManager(paramHttpServletRequest, null, user)).getSignType()); }
/* 604 */       else if (paramString2.endsWith("getResourcePaidLeaveDays"))
/* 605 */       { double d = 0.0D;
/* 606 */         if (StringUtil.isNotNull(str2)) {
/* 607 */           d = (new HrmPaidLeaveTimeManager()).getCurrentPaidLeaveDaysByUser(String.valueOf(str2));
/*     */         }
/* 609 */         stringBuffer.append(String.valueOf(d)); }
/* 610 */       else if (paramString2.equalsIgnoreCase("getFormInfo"))
/*     */       
/*     */       { 
/*     */         try {
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 618 */           StringBuffer stringBuffer1 = (new StringBuffer("select id,formName as name from WorkFlow_FormBase t where 1 = 1 ")).append(" and t.id =(select formid from workflow_base where id = ").append(str2).append(")").append(" union all ").append("select t.id, t2.labelname as name from workflow_bill t").append(" left join htmllabelinfo t2 on t.namelabel = t2.indexid where t.id = (select formid from workflow_base where id = ").append(str2).append(") and t2.languageid = ").append(user.getLanguage());
/* 619 */           RS.executeSql(stringBuffer1.toString());
/* 620 */           if (RS.next()) {
/* 621 */             jSONObject.put("id", RS.getString("id"));
/* 622 */             jSONObject.put("name", RS.getString("name"));
/*     */           } 
/* 624 */         } catch (Exception exception) {}
/*     */         
/* 626 */         stringBuffer.append(jSONObject.toString()); }
/* 627 */       else if (paramString2.equalsIgnoreCase("checkDetailWorkTime"))
/* 628 */       { String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("arg1"));
/* 629 */         HrmScheduleWorktimeManager hrmScheduleWorktimeManager = new HrmScheduleWorktimeManager();
/* 630 */         List list = hrmScheduleWorktimeManager.find("[map]sql_id:and t.id in(" + StringUtil.replace(str, ";", ",") + ");delflag:0");
/* 631 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 632 */         int i = 0; byte b = 0;
/* 633 */         StringBuffer stringBuffer1 = new StringBuffer(), stringBuffer2 = new StringBuffer();
/* 634 */         for (HrmScheduleWorktime hrmScheduleWorktime : list) {
/* 635 */           if (hrmScheduleWorktime.getField002().compareTo(hrmScheduleWorktime.getField003()) > 0 && !hrmScheduleWorktime.getField003().equals("00:00")) {
/* 636 */             stringBuffer2.append((stringBuffer2.length() == 0) ? "" : ",").append(hrmScheduleWorktime.getField002()).append("-00:00");
/* 637 */             stringBuffer1.append((stringBuffer1.length() == 0) ? "" : ",").append("00:00-").append(hrmScheduleWorktime.getField003());
/*     */           } else {
/* 639 */             stringBuffer2.append((stringBuffer2.length() == 0) ? "" : ",").append(hrmScheduleWorktime.getTime());
/*     */           } 
/* 641 */           i += DateUtil.totalTime(hrmScheduleWorktime.getField002(), hrmScheduleWorktime.getField003(), hashMap);
/*     */         } 
/* 643 */         int j = StringUtil.parseToInt(paramHttpServletRequest.getParameter("type"));
/* 644 */         if (!(b = (i == hashMap.size()) ? 0 : 1)) {
/* 645 */           HrmScheduleShiftsDetailManager hrmScheduleShiftsDetailManager = new HrmScheduleShiftsDetailManager();
/* 646 */           List list1 = hrmScheduleShiftsDetailManager.find("[map]field001:" + str2 + ";sql_id:and t.id != " + str1);
/* 647 */           if (j == 0 || j == 1) {
/* 648 */             HrmScheduleShiftsDetail hrmScheduleShiftsDetail = new HrmScheduleShiftsDetail();
/* 649 */             hrmScheduleShiftsDetailManager.initBean(paramHttpServletRequest, hrmScheduleShiftsDetail);
/* 650 */             for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail1 : list1) {
/* 651 */               if (hrmScheduleShiftsDetailManager.check(j, hrmScheduleShiftsDetail1, hrmScheduleShiftsDetail)) {
/* 652 */                 b = 3; break;
/*     */               } 
/* 654 */               if (hrmScheduleShiftsDetailManager.checkAcrossTime(j, hrmScheduleShiftsDetail1, hrmScheduleShiftsDetail, stringBuffer1.toString(), stringBuffer2.toString())) {
/* 655 */                 b = 2;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } else {
/* 660 */             StringBuffer stringBuffer3 = new StringBuffer(str);
/* 661 */             for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : list1) stringBuffer3.append(";").append(hrmScheduleShiftsDetail.getField002()); 
/* 662 */             b = hrmScheduleWorktimeManager.isOverlap(stringBuffer3.toString()) ? 2 : b;
/*     */           } 
/*     */         }  
/* 665 */         try { jSONObject.put("result", String.valueOf(b)); } catch (JSONException jSONException) {}
/* 666 */         stringBuffer.append(jSONObject.toString()); }
/* 667 */       else if (paramString2.equalsIgnoreCase("checkDetailDate"))
/* 668 */       { boolean bool = true;
/* 669 */         int i = StringUtil.parseToInt(paramHttpServletRequest.getParameter("type"));
/* 670 */         if (i == 0 || i == 1) {
/* 671 */           HrmScheduleShiftsDetailManager hrmScheduleShiftsDetailManager = new HrmScheduleShiftsDetailManager();
/* 672 */           List list = hrmScheduleShiftsDetailManager.find("[map]field001:" + str2 + ";sql_id:and t.id != " + str1);
/* 673 */           HrmScheduleShiftsDetail hrmScheduleShiftsDetail = new HrmScheduleShiftsDetail();
/* 674 */           hrmScheduleShiftsDetailManager.initBean(paramHttpServletRequest, hrmScheduleShiftsDetail);
/* 675 */           for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail1 : list) {
/* 676 */             if (hrmScheduleShiftsDetailManager.check(i, hrmScheduleShiftsDetail1, hrmScheduleShiftsDetail)) {
/* 677 */               bool = false; break;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 682 */         try { jSONObject.put("result", bool ? "true" : "false"); } catch (JSONException jSONException) {}
/* 683 */         stringBuffer.append(jSONObject.toString()); }
/* 684 */       else if (paramString2.equalsIgnoreCase("getScheduleShiftsSetDetailSize")) { 
/* 685 */         try { jSONObject.put("result", (new HrmScheduleShiftsDetailManager()).count("field001:" + str1 + ";delflag:0")); } catch (JSONException jSONException) {}
/* 686 */         stringBuffer.append(jSONObject.toString()); }
/*     */        }
/* 688 */      return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getUseDemand(String paramString) {
/* 695 */     StringBuffer stringBuffer1 = (new StringBuffer("select d.id,d.demandjobtitle,j.jobtitlename,d.demandkind,k.name as useKindName, ")).append("d.leastedulevel,l.name as levelName,d.demandnum,d.demandregdate,d.otherrequest,d.status from HrmUseDemand d ").append("left join HrmJobTitles j on d.demandjobtitle = j.id left join HrmUseKind k on d.demandkind = k.id ").append("left join HrmEducationLevel l on d.leastedulevel = l.id where d.id = ").append(paramString);
/* 696 */     RS.executeSql(stringBuffer1.toString());
/*     */     
/* 698 */     StringBuffer stringBuffer2 = new StringBuffer("[");
/* 699 */     if (RS.next()) {
/* 700 */       stringBuffer2.append("{")
/* 701 */         .append("id:'").append(StringUtil.vString(RS.getString("id"))).append("',")
/* 702 */         .append("demandjobtitle:'").append(StringUtil.vString(RS.getString("demandjobtitle"))).append("',")
/* 703 */         .append("jobtitlename:'").append(StringUtil.vString(RS.getString("jobtitlename"))).append("',")
/* 704 */         .append("demandkind:'").append(StringUtil.vString(RS.getString("demandkind"))).append("',")
/* 705 */         .append("useKindName:'").append(StringUtil.vString(RS.getString("useKindName"))).append("',")
/* 706 */         .append("leastedulevel:'").append(StringUtil.vString(RS.getString("leastedulevel"))).append("',")
/* 707 */         .append("levelName:'").append(StringUtil.vString(RS.getString("levelName"))).append("',")
/* 708 */         .append("demandnum:'").append(StringUtil.vString(RS.getString("demandnum"))).append("',")
/* 709 */         .append("demandregdate:'").append(StringUtil.vString(RS.getString("demandregdate"))).append("',")
/* 710 */         .append("otherrequest:'").append(StringUtil.getURLEncode(RS.getString("otherrequest"))).append("',")
/* 711 */         .append("status:'").append(StringUtil.vString(RS.getString("status"))).append("'")
/* 712 */         .append("}");
/*     */     }
/* 714 */     stringBuffer2.append("]");
/* 715 */     return stringBuffer2.toString();
/*     */   }
/*     */   
/*     */   private static String getAllSubId(String paramString1, String paramString2) {
/* 719 */     if (paramString1.length() == 0) {
/* 720 */       return "";
/*     */     }
/*     */     
/* 723 */     StringBuffer stringBuffer = (new StringBuffer()).append("select id from HrmSubCompany where id <> ").append(paramString1).append(" and supsubcomid = ").append(paramString1);
/* 724 */     RS.executeSql(stringBuffer.toString());
/*     */     
/* 726 */     while (RS.next()) {
/* 727 */       String str = StringUtil.vString(RS.getString("id"));
/* 728 */       paramString2 = paramString2 + "," + str;
/* 729 */       paramString2 = getAllSubId(str, paramString2);
/*     */     } 
/* 731 */     return paramString2;
/*     */   }
/*     */   
/*     */   private static String getAllDeptId(String paramString1, String paramString2) {
/* 735 */     if (paramString1.length() == 0) {
/* 736 */       return "";
/*     */     }
/*     */     
/* 739 */     StringBuffer stringBuffer = (new StringBuffer()).append("select id from HrmDepartment where id <> ").append(paramString1).append(" and supdepid = ").append(paramString1);
/* 740 */     RS.executeSql(stringBuffer.toString());
/*     */     
/* 742 */     while (RS.next()) {
/* 743 */       String str = StringUtil.vString(RS.getString("id"));
/* 744 */       paramString2 = paramString2 + "," + str;
/* 745 */       paramString2 = getAllDeptId(str, paramString2);
/*     */     } 
/* 747 */     return paramString2;
/*     */   }
/*     */   
/*     */   private static String getSelectAllIds(String paramString1, String paramString2) {
/* 751 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getHrmAward(String paramString) {
/* 769 */     RS.executeSql("select id,description,transact from HrmAwardType where id = " + paramString);
/*     */     
/* 771 */     StringBuffer stringBuffer = new StringBuffer("[");
/* 772 */     if (RS.next()) {
/* 773 */       stringBuffer.append("{")
/* 774 */         .append("id:'").append(StringUtil.vString(RS.getString("id"))).append("',")
/* 775 */         .append("description:'").append(StringUtil.vString(RS.getString("description"))).append("',")
/* 776 */         .append("transact:'").append(StringUtil.vString(RS.getString("transact"))).append("'")
/* 777 */         .append("}");
/*     */     }
/* 779 */     stringBuffer.append("]");
/* 780 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private static String checkLoginId(String paramString1, String paramString2) {
/* 784 */     return checkLoginId(paramString1, paramString2, false);
/*     */   }
/*     */ 
/*     */   
/*     */   private static String checkLoginId(String paramString1, String paramString2, boolean paramBoolean) {
/* 789 */     StringBuffer stringBuffer1 = (new StringBuffer("select id,lastname,loginid,{fEmail},mobile,(select COUNT(id) from hrm_protection_question where user_id = {tName}.id and delflag = 0) as qCount from {tName} where loginid = '")).append(StringUtil.vString(paramString1)).append("' ");
/* 790 */     if (StringUtil.isNotNull(paramString2)) {
/* 791 */       stringBuffer1.append(" and id != ").append(paramString2);
/*     */     }
/* 793 */     String str = StringUtil.replace(stringBuffer1.toString(), "{fEmail}", "email");
/* 794 */     str = StringUtil.replace(str, "{tName}", "HrmResource");
/* 795 */     RS.executeSql(str + (paramBoolean ? " and (accounttype = 0 or accounttype is null) " : ""));
/* 796 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 797 */     HrmResource hrmResource = new HrmResource();
/* 798 */     int i = 0;
/* 799 */     if (RS.next()) {
/* 800 */       hrmResource.setId(Integer.valueOf(RS.getInt(1)));
/* 801 */       hrmResource.setLastname(StringUtil.vString(RS.getString(2)));
/* 802 */       hrmResource.setLoginid(StringUtil.vString(RS.getString(3)));
/* 803 */       hrmResource.setEmail(StringUtil.vString(RS.getString(4)));
/* 804 */       hrmResource.setMobile(StringUtil.vString(RS.getString(5)));
/* 805 */       i = RS.getInt("qCount");
/*     */     } 
/* 807 */     if (hrmResource.getId().intValue() == 0) {
/* 808 */       str = StringUtil.replace(stringBuffer1.toString(), "{fEmail}", "'' as email");
/* 809 */       str = StringUtil.replace(str, "{tName}", "HrmResourceManager");
/* 810 */       RS.executeSql(str);
/* 811 */       if (RS.next()) {
/* 812 */         hrmResource.setId(Integer.valueOf(RS.getInt(1)));
/* 813 */         hrmResource.setLastname(StringUtil.vString(RS.getString(2)));
/* 814 */         hrmResource.setLoginid(StringUtil.vString(RS.getString(3)));
/* 815 */         hrmResource.setEmail(StringUtil.vString(RS.getString(4)));
/* 816 */         hrmResource.setMobile(StringUtil.vString(RS.getString(5)));
/* 817 */         i = RS.getInt("qCount");
/*     */       } 
/*     */     } 
/* 820 */     if (paramBoolean) {
/* 821 */       JSONObject jSONObject = new JSONObject();
/*     */       try {
/* 823 */         jSONObject.put("qCount", String.valueOf(i));
/* 824 */         jSONObject.put("id", hrmResource.getId());
/* 825 */         String str1 = hrmResource.getMobile();
/* 826 */         if (str1.length() - 4 > 0) {
/* 827 */           str1 = str1.substring(0, str1.length() - 4);
/*     */         }
/* 829 */         str1 = str1 + "****";
/* 830 */         jSONObject.put("mobile", StringUtil.isNull(hrmResource.getMobile()) ? "" : str1);
/* 831 */         jSONObject.put("field001", StringUtil.isNull(hrmResource.getMobile()) ? "" : StringUtil.encode(hrmResource.getMobile()));
/*     */         
/* 833 */         String str2 = "****";
/* 834 */         String[] arrayOfString = hrmResource.getEmail().split("@");
/* 835 */         if (arrayOfString.length == 2) {
/* 836 */           str2 = arrayOfString[0];
/*     */           
/* 838 */           if (str2.length() - 4 > 0) {
/* 839 */             str2 = str2.substring(0, str2.length() - 4);
/*     */           }
/* 841 */           str2 = str2 + "****";
/* 842 */           str2 = str2 + "@" + arrayOfString[1];
/*     */         } 
/* 844 */         jSONObject.put("email", StringUtil.isNull(hrmResource.getEmail()) ? "" : str2);
/* 845 */         jSONObject.put("field002", StringUtil.isNull(hrmResource.getEmail()) ? "" : StringUtil.encode(hrmResource.getEmail()));
/* 846 */       } catch (JSONException jSONException) {}
/*     */       
/* 848 */       stringBuffer2.append(jSONObject.toString());
/*     */     } else {
/* 850 */       stringBuffer2.append((hrmResource.getId().intValue() != 0) ? "1" : "0");
/*     */     } 
/* 852 */     return stringBuffer2.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getLnScResult(String paramString) {
/* 858 */     int i = 1;
/* 859 */     int j = 0;
/* 860 */     LN lN = new LN();
/* 861 */     lN.InLicense();
/* 862 */     i = StringUtil.parseToInt(lN.getScType(), 1);
/* 863 */     j = StringUtil.parseToInt(lN.getScCount(), 0);
/* 864 */     j = (i == 0) ? ((j < 0) ? 0 : j) : 0;
/*     */     
/* 866 */     String str = "";
/* 867 */     if (paramString.equals("ct")) {
/* 868 */       str = String.valueOf(j);
/* 869 */     } else if (paramString.equals("mf")) {
/* 870 */       int k = 0;
/* 871 */       RS.executeSql("select COUNT(id) from HrmSubCompany where supsubcomid = 0 and (canceled is null or canceled != '1')");
/* 872 */       if (RS.next())
/* 873 */         k = RS.getInt(1); 
/* 874 */       str = String.valueOf((j == 0 || k < j));
/*     */     } 
/* 876 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/AjaxManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */