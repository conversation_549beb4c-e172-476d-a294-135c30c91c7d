/*    */ package weaver.hrm.common.database.dialect;
/*    */ 
/*    */ import weaver.hrm.common.database.dialect.constract.DateFormatStyle;
/*    */ 
/*    */ public class OracleDialectSql
/*    */   extends GeneralDbDialectSql {
/*  7 */   private static OracleDialectSql dialectSql = new OracleDialectSql();
/*    */   
/*    */   public <T> String isNull(String paramString, T paramT) {
/* 10 */     String str = (paramT instanceof String) ? ("'" + paramT + "'") : (new StringBuilder()).append(paramT).append("").toString();
/* 11 */     return String.format("nvl(%s,%s)", new Object[] { paramString, str });
/*    */   }
/*    */   
/*    */   public static IDbDialectSql getInstance() {
/* 15 */     return dialectSql;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNullCondition(String paramString) {
/* 20 */     return String.format("%s is null", new Object[] { paramString });
/*    */   }
/*    */ 
/*    */   
/*    */   public String strToDateSql(String paramString, DateFormatStyle paramDateFormatStyle) {
/* 25 */     switch (paramDateFormatStyle) {
/*    */       case FULL_DATE:
/* 27 */         return String.format("to_date(%s,'yyyy-MM-dd HH24:mi:ss')", new Object[] { paramString });
/*    */     } 
/* 29 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public String strToDateSql(String paramString1, String paramString2) {
/* 34 */     return String.format("to_date(%s,'%s')", new Object[] { paramString1, paramString2 });
/*    */   }
/*    */ 
/*    */   
/*    */   public String dateToStrSql(String paramString, DateFormatStyle paramDateFormatStyle) {
/* 39 */     switch (paramDateFormatStyle) {
/*    */       case FULL_DATE:
/* 41 */         return String.format("to_char(%s,'yyyy-MM-dd HH24:mi:ss')", new Object[] { paramString });
/*    */     } 
/* 43 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/database/dialect/OracleDialectSql.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */