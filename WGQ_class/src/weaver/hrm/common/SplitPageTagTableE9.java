/*     */ package weaver.hrm.common;
/*     */ 
/*     */ import com.api.hrm.util.PageUidFactory;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SplitPageTagTableE9
/*     */ {
/*  20 */   private String pageId = "";
/*     */   
/*  22 */   private User user = null;
/*     */   
/*     */   private boolean showPopedom = true;
/*     */   
/*  26 */   private String popedompara = "";
/*     */   
/*  28 */   private List<Operate> operateList = null;
/*     */   
/*  30 */   private List<Column> columnList = null;
/*     */   
/*  32 */   private Map<String, String> tableAttr = null;
/*     */   
/*  34 */   private Map<String, String> sqlAttr = null;
/*     */   
/*     */   private String tableType;
/*     */   
/*     */   public SplitPageTagTableE9(User paramUser) {
/*  39 */     this("", paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTableE9(String paramString, User paramUser) {
/*  43 */     this(paramString, true, paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTableE9(String paramString1, String paramString2, User paramUser) {
/*  47 */     this(paramString1, paramString2, true, paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTableE9(String paramString, boolean paramBoolean, User paramUser) {
/*  51 */     this(paramString, "checkbox", paramBoolean, paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTableE9(String paramString1, String paramString2, boolean paramBoolean, User paramUser) {
/*  55 */     this.pageId = paramString1;
/*  56 */     this.user = paramUser;
/*  57 */     this.tableType = paramString2;
/*  58 */     this.operateList = new ArrayList<>();
/*  59 */     this.columnList = new ArrayList<>();
/*  60 */     this.tableAttr = new HashMap<>();
/*  61 */     this.sqlAttr = new HashMap<>();
/*     */     
/*  63 */     init();
/*     */   }
/*     */   
/*     */   private void init() {
/*  67 */     addAttribute("pagesize", PageIdConst.getPageSize(this.pageId, this.user.getUID(), "Hrm"));
/*  68 */     addAttribute("pageUid", PageUidFactory.getHrmPageUid(this.pageId));
/*  69 */     addAttribute("tabletype", StringUtil.vString(this.tableType, "checkbox"));
/*  70 */     addAttribute("needPage", "true");
/*  71 */     addAttribute("datasource", "");
/*  72 */     addAttribute("sourceparams", "");
/*     */     
/*  74 */     addSqlAttribute("sqlsortway", "asc");
/*  75 */     addSqlAttribute("sqlisdistinct", "true");
/*  76 */     addSqlAttribute("sqlprimarykey", "id");
/*  77 */     addSqlAttribute("sumColumns", "");
/*  78 */     addSqlAttribute("showCountColumn", "");
/*  79 */     addSqlAttribute("showPageCount", "");
/*  80 */     addSqlAttribute("decimalFormat", "");
/*     */   }
/*     */   
/*     */   public void addOperate(String paramString1, String paramString2) {
/*  84 */     addOperate(paramString1, paramString2, "true");
/*     */   }
/*     */   
/*     */   public void addOperate(String paramString1, String paramString2, String paramString3) {
/*  88 */     addOperate(paramString1, paramString2, paramString3, "");
/*     */   }
/*     */   
/*     */   public void addOperate(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  92 */     int i = this.operateList.size();
/*  93 */     this.operateList.add(new Operate(paramString1, paramString2, paramString3, i, paramString4));
/*     */   }
/*     */   
/*     */   public void addAttribute(String paramString1, String paramString2) {
/*  97 */     this.tableAttr.put(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public void setSql(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 101 */     addSqlAttribute("backfields", paramString1);
/* 102 */     addSqlAttribute("sqlform", paramString2);
/* 103 */     addSqlAttribute("sqlwhere", paramString3);
/* 104 */     addSqlAttribute("sqlorderby", paramString4);
/*     */   }
/*     */   
/*     */   public void setSql(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 108 */     setSql(paramString1, paramString2, paramString3, paramString4);
/* 109 */     addSqlAttribute("sqlsortway", paramString5);
/*     */   }
/*     */   
/*     */   public void setData(String paramString1, String paramString2) {
/* 113 */     setData(paramString1, paramString2, "none");
/*     */   }
/*     */ 
/*     */   
/*     */   public void setData(String paramString1, String paramString2, String paramString3) {
/* 118 */     addAttribute("datasource", paramString1);
/* 119 */     addAttribute("sourceparams", paramString2);
/* 120 */     this.tableType = paramString3;
/* 121 */     addAttribute("tabletype", paramString3);
/* 122 */     setShowPopedom(false);
/* 123 */     setSql("*", "temp", "", "id");
/*     */   }
/*     */   
/*     */   public String getSql() {
/* 127 */     String str1 = StringUtil.vString(this.sqlAttr.get("backfields"));
/* 128 */     String str2 = StringUtil.vString(this.sqlAttr.get("sqlform"));
/* 129 */     String str3 = StringUtil.vString(this.sqlAttr.get("sqlwhere"));
/* 130 */     String str4 = StringUtil.vString(this.sqlAttr.get("sqlorderby"));
/* 131 */     String str5 = StringUtil.vString(this.sqlAttr.get("sqlsortway"));
/* 132 */     if (str4.length() > 0) {
/* 133 */       String[] arrayOfString = str4.split(",");
/* 134 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 135 */       byte b = 0;
/* 136 */       for (String str : arrayOfString) stringBuffer1.append(str).append(" ").append(str5).append((b++ != arrayOfString.length - 1) ? "," : ""); 
/* 137 */       str4 = stringBuffer1.toString();
/*     */     } 
/*     */     
/* 140 */     StringBuffer stringBuffer = (new StringBuffer(str1.toLowerCase().startsWith("select") ? "" : " select ")).append(str1).append(str2.toLowerCase().startsWith("from") ? " " : " from ").append(str2);
/* 141 */     if (str3.length() != 0) stringBuffer.append(str3.toLowerCase().startsWith("where") ? " " : " where ").append(str3); 
/* 142 */     stringBuffer.append(str4.toLowerCase().startsWith("order") ? " " : " order by ").append(str4);
/* 143 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public void setPrimaryKey(String paramString) {
/* 147 */     addSqlAttribute("sqlprimarykey", paramString);
/*     */   }
/*     */   
/*     */   public void addSqlAttribute(String paramString1, String paramString2) {
/* 151 */     this.sqlAttr.put(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public void setShowPopedom(boolean paramBoolean) {
/* 155 */     this.showPopedom = paramBoolean;
/*     */   }
/*     */   
/*     */   public void setPopedompara(String paramString) {
/* 159 */     this.popedompara = paramString;
/*     */   }
/*     */   
/*     */   public void addHideCol(Comparable paramComparable, String paramString1, String paramString2) {
/* 163 */     Column column = new Column("0", paramString1, paramString2, "", "", "");
/* 164 */     column.hide = "true";
/* 165 */     this.columnList.add(column);
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2) {
/* 169 */     addCol(paramComparable, paramString1, paramString2, paramString2);
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3) {
/* 173 */     addCol(paramComparable, paramString1, paramString2, paramString3, "", "");
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3, String paramString4) {
/* 177 */     addCol(paramComparable, paramString1, paramString2, paramString3, paramString4, "");
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 181 */     this.columnList.add(new Column((paramComparable instanceof Integer) ? String.valueOf(paramComparable + "%") : String.valueOf(paramComparable), paramString1, paramString2, paramString3, paramString4, paramString5));
/*     */   }
/*     */   
/*     */   public void addFormatCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3) {
/* 185 */     addFormatCol(paramComparable, paramString1, paramString2, paramString2, paramString3);
/*     */   }
/*     */   
/*     */   public void addFormatCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3, String paramString4) {
/* 189 */     addCol(paramComparable, paramString1, paramString2, paramString3, "weaver.hrm.common.SplitPageTagFormat.colFormat", paramString4);
/*     */   }
/*     */   
/*     */   public String toString() {
/* 193 */     StringBuffer stringBuffer = new StringBuffer();
/* 194 */     stringBuffer.append("<table");
/* 195 */     Iterator<Map.Entry> iterator = this.tableAttr.entrySet().iterator();
/* 196 */     while (iterator.hasNext()) {
/* 197 */       Map.Entry entry = iterator.next();
/* 198 */       String str1 = (String)entry.getKey();
/* 199 */       String str2 = (String)entry.getValue();
/* 200 */       if (StringUtil.isNull(str2))
/*     */         continue; 
/* 202 */       stringBuffer.append(" ").append(str1).append("=\"" + str2 + "\"");
/*     */     } 
/* 204 */     stringBuffer.append(">");
/* 205 */     if (this.tableType.equalsIgnoreCase("thumbnail")) {
/* 206 */       stringBuffer.append("<browser imgurl=\"/weaver/weaver.common.Ctrl?arg0=weaver.framework.BaseController\" linkkey=\"thumbnailFilePath\" linkvaluecolumn=\"filePath\" path=\"\" />");
/*     */     }
/* 208 */     if (this.showPopedom) {
/* 209 */       stringBuffer.append("<checkboxpopedom showmethod=\"weaver.hrm.common.SplitPageTagOperate.getBasicCheckbox\"  tabletype=\"" + this.tableType + "\"  popedompara=\"" + this.popedompara + "\"/>");
/*     */     }
/* 211 */     stringBuffer.append("<sql");
/* 212 */     iterator = this.sqlAttr.entrySet().iterator();
/* 213 */     while (iterator.hasNext()) {
/* 214 */       Map.Entry entry = iterator.next();
/* 215 */       String str1 = (String)entry.getKey();
/* 216 */       String str2 = (String)entry.getValue();
/* 217 */       if (StringUtil.isNull(str2) && !str1.equals("sqlwhere"))
/*     */         continue; 
/* 219 */       stringBuffer.append(" ").append(str1).append("=\"" + StringUtil.toHtml(str2) + "\"");
/*     */     } 
/* 221 */     stringBuffer.append("/>");
/* 222 */     if (this.operateList.size() > 0) {
/* 223 */       stringBuffer.append("<operates width=\"20%\">");
/* 224 */       if (this.showPopedom) {
/* 225 */         stringBuffer.append("<popedom transmethod=\"weaver.hrm.common.SplitPageTagOperate.getBasicOperate\" otherpara=\"");
/* 226 */         byte b = 0;
/* 227 */         for (Operate operate : this.operateList) {
/* 228 */           stringBuffer.append(operate.check).append((b == this.operateList.size() - 1) ? "" : ":");
/* 229 */           b++;
/*     */         } 
/* 231 */         stringBuffer.append("\"></popedom>");
/*     */       } 
/* 233 */       for (Operate operate : this.operateList) {
/* 234 */         stringBuffer.append("<operate href=\"").append(operate.href).append("\"");
/* 235 */         if (StringUtil.isNotNull(operate.otherParam)) stringBuffer.append(" otherpara=\"").append(operate.otherParam).append("\""); 
/* 236 */         stringBuffer.append(" text=\"").append(operate.text).append("\" index=\"").append(operate.index).append("\"/> ");
/*     */       } 
/* 238 */       stringBuffer.append("</operates>");
/*     */     } 
/* 240 */     stringBuffer.append("<head>");
/* 241 */     for (Column column : this.columnList) {
/* 242 */       stringBuffer.append("<col width=\"").append(column.width).append("\" text=\"")
/* 243 */         .append(column.text).append("\" column=\"").append(column.column).append("\"");
/* 244 */       if (StringUtil.isNotNull(column.hide)) stringBuffer.append(" hide=\"").append(column.hide).append("\""); 
/* 245 */       if (StringUtil.isNotNull(column.orderKey)) stringBuffer.append(" orderkey=\"").append(column.orderKey).append("\""); 
/* 246 */       if (StringUtil.isNotNull(column.transMethod)) stringBuffer.append(" transmethod=\"").append(column.transMethod).append("\""); 
/* 247 */       if (StringUtil.isNotNull(column.otherPara)) stringBuffer.append(" otherpara=\"").append(column.otherPara).append("\""); 
/* 248 */       stringBuffer.append("/>");
/*     */     } 
/* 250 */     stringBuffer.append("</head></table>");
/* 251 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private class Operate {
/*     */     String text;
/*     */     String href;
/*     */     int index;
/*     */     String check;
/*     */     String otherParam;
/*     */     
/*     */     public Operate(String param1String1, String param1String2, String param1String3, int param1Int, String param1String4) {
/* 262 */       this.text = param1String1;
/* 263 */       this.href = param1String2;
/* 264 */       this.check = param1String3;
/* 265 */       this.index = param1Int;
/* 266 */       this.otherParam = param1String4;
/*     */     }
/*     */   }
/*     */   
/*     */   private class Column {
/*     */     String width;
/*     */     String text;
/*     */     String column;
/*     */     String orderKey;
/*     */     String transMethod;
/*     */     String otherPara;
/*     */     String hide;
/*     */     
/*     */     public Column(String param1String1, String param1String2, String param1String3, String param1String4, String param1String5, String param1String6) {
/* 280 */       this.width = param1String1;
/* 281 */       this.text = param1String2;
/* 282 */       this.column = param1String3;
/* 283 */       this.orderKey = param1String4;
/* 284 */       this.transMethod = param1String5;
/* 285 */       this.otherPara = param1String6;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/SplitPageTagTableE9.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */