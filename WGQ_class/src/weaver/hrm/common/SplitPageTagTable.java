/*     */ package weaver.hrm.common;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.jsp.JspWriter;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.PageIdConst;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SplitPageTagTable
/*     */ {
/*  21 */   private String pageId = "";
/*     */   
/*     */   private boolean isShowCol;
/*     */   
/*  25 */   private JspWriter out = null;
/*     */   
/*  27 */   private User user = null;
/*     */   
/*     */   private boolean showPopedom = true;
/*     */   
/*  31 */   private String popedompara = "";
/*     */   
/*  33 */   private List<Operate> operateList = null;
/*     */   
/*  35 */   private List<Column> columnList = null;
/*     */   
/*  37 */   private Map<String, String> tableAttr = null;
/*     */   
/*  39 */   private Map<String, String> sqlAttr = null;
/*     */   
/*     */   private String tableType;
/*     */   
/*     */   public SplitPageTagTable(JspWriter paramJspWriter, User paramUser) {
/*  44 */     this("", paramJspWriter, paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTable(String paramString, JspWriter paramJspWriter, User paramUser) {
/*  48 */     this(paramString, true, paramJspWriter, paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTable(String paramString1, String paramString2, JspWriter paramJspWriter, User paramUser) {
/*  52 */     this(paramString1, paramString2, true, paramJspWriter, paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTable(String paramString, boolean paramBoolean, JspWriter paramJspWriter, User paramUser) {
/*  56 */     this(paramString, "checkbox", paramBoolean, paramJspWriter, paramUser);
/*     */   }
/*     */   
/*     */   public SplitPageTagTable(String paramString1, String paramString2, boolean paramBoolean, JspWriter paramJspWriter, User paramUser) {
/*  60 */     this.pageId = paramString1;
/*  61 */     this.isShowCol = paramBoolean;
/*  62 */     this.out = paramJspWriter;
/*  63 */     this.user = paramUser;
/*  64 */     this.tableType = paramString2;
/*  65 */     this.operateList = new ArrayList<Operate>();
/*  66 */     this.columnList = new ArrayList<Column>();
/*  67 */     this.tableAttr = new HashMap<String, String>();
/*  68 */     this.sqlAttr = new HashMap<String, String>();
/*     */     
/*  70 */     init();
/*     */   }
/*     */   
/*     */   private void init() {
/*  74 */     addAttribute("pagesize", PageIdConst.getPageSize(this.pageId, this.user.getUID(), "Hrm"));
/*  75 */     addAttribute("tabletype", StringUtil.vString(this.tableType, "checkbox"));
/*  76 */     addAttribute("needPage", "true");
/*  77 */     addAttribute("datasource", "");
/*  78 */     addAttribute("sourceparams", "");
/*     */     
/*  80 */     addSqlAttribute("sqlsortway", "asc");
/*  81 */     addSqlAttribute("sqlisdistinct", "true");
/*  82 */     addSqlAttribute("sqlprimarykey", "id");
/*  83 */     addSqlAttribute("sumColumns", "");
/*  84 */     addSqlAttribute("showCountColumn", "");
/*  85 */     addSqlAttribute("showPageCount", "");
/*  86 */     addSqlAttribute("decimalFormat", "");
/*     */     
/*  88 */     if (StringUtil.isNotNull(this.pageId)) {
/*  89 */       addAttribute("pageId", this.pageId); 
/*  90 */       try { this.out.print("<input type=\"hidden\" name=\"pageId\" id=\"pageId\" value=\"" + this.pageId + "\" _showCol=\"" + String.valueOf(this.isShowCol) + "\"/>"); } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */   
/*     */   public void addOperate(String paramString1, String paramString2) {
/*  95 */     addOperate(paramString1, paramString2, "true");
/*     */   }
/*     */   
/*     */   public void addOperate(String paramString1, String paramString2, String paramString3) {
/*  99 */     addOperate(paramString1, paramString2, paramString3, "");
/*     */   }
/*     */   
/*     */   public void addOperate(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 103 */     int i = this.operateList.size();
/* 104 */     this.operateList.add(new Operate(paramString1, paramString2, paramString3, i, paramString4));
/*     */   }
/*     */   
/*     */   public void addAttribute(String paramString1, String paramString2) {
/* 108 */     this.tableAttr.put(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public void setSql(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 112 */     addSqlAttribute("backfields", paramString1);
/* 113 */     addSqlAttribute("sqlform", paramString2);
/* 114 */     addSqlAttribute("sqlwhere", paramString3);
/* 115 */     addSqlAttribute("sqlorderby", paramString4);
/*     */   }
/*     */   
/*     */   public void setSql(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 119 */     setSql(paramString1, paramString2, paramString3, paramString4);
/* 120 */     addSqlAttribute("sqlsortway", paramString5);
/*     */   }
/*     */   
/*     */   public void setData(String paramString1, String paramString2) {
/* 124 */     setData(paramString1, paramString2, "none");
/*     */   }
/*     */ 
/*     */   
/*     */   public void setData(String paramString1, String paramString2, String paramString3) {
/* 129 */     addAttribute("datasource", paramString1);
/* 130 */     addAttribute("sourceparams", paramString2);
/* 131 */     this.tableType = paramString3;
/* 132 */     addAttribute("tabletype", paramString3);
/* 133 */     setShowPopedom(false);
/* 134 */     setSql("*", "temp", "", "id");
/*     */   }
/*     */   
/*     */   public String getSql() {
/* 138 */     String str1 = StringUtil.vString(this.sqlAttr.get("backfields"));
/* 139 */     String str2 = StringUtil.vString(this.sqlAttr.get("sqlform"));
/* 140 */     String str3 = StringUtil.vString(this.sqlAttr.get("sqlwhere"));
/* 141 */     String str4 = StringUtil.vString(this.sqlAttr.get("sqlorderby"));
/* 142 */     String str5 = StringUtil.vString(this.sqlAttr.get("sqlsortway"));
/* 143 */     if (str4.length() > 0) {
/* 144 */       String[] arrayOfString = str4.split(",");
/* 145 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 146 */       byte b = 0;
/* 147 */       for (String str : arrayOfString) stringBuffer1.append(str).append(" ").append(str5).append((b++ != arrayOfString.length - 1) ? "," : ""); 
/* 148 */       str4 = stringBuffer1.toString();
/*     */     } 
/* 150 */     StringBuffer stringBuffer = (new StringBuffer(str1.toLowerCase().startsWith("select") ? "" : " select ")).append(str1).append(str2.toLowerCase().startsWith("from") ? " " : " from ").append(str2);
/*     */     
/* 152 */     if (str3.length() != 0) stringBuffer.append(str3.toLowerCase().startsWith("where") ? " " : " where ").append(str3); 
/* 153 */     stringBuffer.append(str4.toLowerCase().startsWith("order") ? " " : " order by ").append(str4);
/* 154 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public void setPrimaryKey(String paramString) {
/* 158 */     addSqlAttribute("sqlprimarykey", paramString);
/*     */   }
/*     */   
/*     */   public void addSqlAttribute(String paramString1, String paramString2) {
/* 162 */     this.sqlAttr.put(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public void setShowPopedom(boolean paramBoolean) {
/* 166 */     this.showPopedom = paramBoolean;
/*     */   }
/*     */   
/*     */   public void setPopedompara(String paramString) {
/* 170 */     this.popedompara = paramString;
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2) {
/* 174 */     addCol(paramComparable, paramString1, paramString2, paramString2);
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3) {
/* 178 */     addCol(paramComparable, paramString1, paramString2, paramString3, "", "");
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3, String paramString4) {
/* 182 */     addCol(paramComparable, paramString1, paramString2, paramString3, paramString4, "");
/*     */   }
/*     */   
/*     */   public void addCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 186 */     this.columnList.add(new Column((paramComparable instanceof Integer) ? String.valueOf(paramComparable + "%") : String.valueOf(paramComparable), paramString1, paramString2, paramString3, paramString4, paramString5));
/*     */   }
/*     */   
/*     */   public void addFormatCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3) {
/* 190 */     addFormatCol(paramComparable, paramString1, paramString2, paramString2, paramString3);
/*     */   }
/*     */   
/*     */   public void addFormatCol(Comparable paramComparable, String paramString1, String paramString2, String paramString3, String paramString4) {
/* 194 */     addCol(paramComparable, paramString1, paramString2, paramString3, "weaver.hrm.common.SplitPageTagFormat.colFormat", paramString4);
/*     */   }
/*     */   
/*     */   public String toString() {
/* 198 */     StringBuffer stringBuffer = new StringBuffer();
/* 199 */     stringBuffer.append("<table");
/* 200 */     Iterator<Map.Entry> iterator = this.tableAttr.entrySet().iterator();
/* 201 */     while (iterator.hasNext()) {
/* 202 */       Map.Entry entry = iterator.next();
/* 203 */       String str1 = (String)entry.getKey();
/* 204 */       String str2 = (String)entry.getValue();
/* 205 */       if (StringUtil.isNull(str2))
/*     */         continue; 
/* 207 */       stringBuffer.append(" ").append(str1).append("=\"" + str2 + "\"");
/*     */     } 
/* 209 */     stringBuffer.append(">");
/* 210 */     if (this.tableType.equalsIgnoreCase("thumbnail")) {
/* 211 */       stringBuffer.append("<browser imgurl=\"/weaver/weaver.common.Ctrl?arg0=weaver.framework.BaseController\" linkkey=\"thumbnailFilePath\" linkvaluecolumn=\"filePath\" path=\"\" />");
/*     */     }
/* 213 */     if (this.showPopedom) {
/* 214 */       stringBuffer.append("<checkboxpopedom showmethod=\"weaver.hrm.common.SplitPageTagOperate.getBasicCheckbox\"  id=\"checkbox\"  popedompara=\"" + this.popedompara + "\"/>");
/*     */     }
/* 216 */     stringBuffer.append("<sql");
/* 217 */     iterator = this.sqlAttr.entrySet().iterator();
/* 218 */     while (iterator.hasNext()) {
/* 219 */       Map.Entry entry = iterator.next();
/* 220 */       String str1 = (String)entry.getKey();
/* 221 */       String str2 = (String)entry.getValue();
/* 222 */       if (StringUtil.isNull(str2) && !str1.equals("sqlwhere"))
/*     */         continue; 
/* 224 */       stringBuffer.append(" ").append(str1).append("=\"" + StringUtil.toHtml(str2) + "\"");
/*     */     } 
/* 226 */     stringBuffer.append("/>");
/* 227 */     if (this.operateList.size() > 0) {
/* 228 */       stringBuffer.append("<operates width=\"20%\">");
/* 229 */       if (this.showPopedom) {
/* 230 */         stringBuffer.append("<popedom transmethod=\"weaver.hrm.common.SplitPageTagOperate.getBasicOperate\" otherpara=\"");
/* 231 */         byte b = 0;
/* 232 */         for (Operate operate : this.operateList) {
/* 233 */           stringBuffer.append(operate.check).append((b == this.operateList.size() - 1) ? "" : ":");
/* 234 */           b++;
/*     */         } 
/* 236 */         stringBuffer.append("\"></popedom>");
/*     */       } 
/* 238 */       for (Operate operate : this.operateList) {
/* 239 */         stringBuffer.append("<operate href=\"").append(operate.href).append("\"");
/* 240 */         if (StringUtil.isNotNull(operate.otherParam)) stringBuffer.append(" otherpara=\"").append(operate.otherParam).append("\""); 
/* 241 */         stringBuffer.append(" text=\"").append(operate.text).append("\" index=\"").append(operate.index).append("\"/> ");
/*     */       } 
/* 243 */       stringBuffer.append("</operates>");
/*     */     } 
/* 245 */     stringBuffer.append("<head>");
/* 246 */     for (Column column : this.columnList) {
/* 247 */       stringBuffer.append("<col width=\"").append(column.width).append("\" text=\"").append(column.text).append("\" column=\"").append(column.column).append("\"");
/*     */       
/* 249 */       if (StringUtil.isNotNull(column.orderKey)) stringBuffer.append(" orderkey=\"").append(column.orderKey).append("\""); 
/* 250 */       if (StringUtil.isNotNull(column.transMethod)) stringBuffer.append(" transmethod=\"").append(column.transMethod).append("\""); 
/* 251 */       if (StringUtil.isNotNull(column.otherPara)) stringBuffer.append(" otherpara=\"").append(column.otherPara).append("\""); 
/* 252 */       stringBuffer.append("/>");
/*     */     } 
/* 254 */     stringBuffer.append("</head></table>");
/* 255 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private class Operate {
/*     */     String text;
/*     */     String href;
/*     */     int index;
/*     */     String check;
/*     */     String otherParam;
/*     */     
/*     */     public Operate(String param1String1, String param1String2, String param1String3, int param1Int, String param1String4) {
/* 266 */       this.text = param1String1;
/* 267 */       this.href = param1String2;
/* 268 */       this.check = param1String3;
/* 269 */       this.index = param1Int;
/* 270 */       this.otherParam = param1String4;
/*     */     }
/*     */   }
/*     */   
/*     */   private class Column {
/*     */     String width;
/*     */     String text;
/*     */     String column;
/*     */     String orderKey;
/*     */     String transMethod;
/*     */     String otherPara;
/*     */     
/*     */     public Column(String param1String1, String param1String2, String param1String3, String param1String4, String param1String5, String param1String6) {
/* 283 */       this.width = param1String1;
/* 284 */       this.text = param1String2;
/* 285 */       this.column = param1String3;
/* 286 */       this.orderKey = param1String4;
/* 287 */       this.transMethod = param1String5;
/* 288 */       this.otherPara = param1String6;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/SplitPageTagTable.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */