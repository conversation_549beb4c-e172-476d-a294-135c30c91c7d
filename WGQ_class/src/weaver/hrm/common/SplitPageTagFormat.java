/*     */ package weaver.hrm.common;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class SplitPageTagFormat
/*     */ {
/*  14 */   private String param = "";
/*     */   
/*  16 */   private String data = "";
/*     */   
/*  18 */   private String oldData = "";
/*     */   
/*  20 */   private String result = "";
/*     */   
/*  22 */   private String style = "";
/*     */   
/*  24 */   private String format = "";
/*     */   
/*     */   private boolean doFormat = false;
/*     */   
/*  28 */   private String[] formatCMD = new String[] { "maxsize", "dateformat" };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String colFormat(String paramString1, String paramString2) {
/*  36 */     this.param = StringUtil.vString(paramString1);
/*  37 */     this.oldData = this.data = StringUtil.vString(paramString2);
/*  38 */     init();
/*  39 */     parse();
/*  40 */     formatValue();
/*  41 */     setStyleValue();
/*  42 */     return this.result;
/*     */   }
/*     */   
/*     */   private void init() {
/*  46 */     String[] arrayOfString = StringUtil.split(this.data, "+");
/*  47 */     StringBuilder stringBuilder = new StringBuilder();
/*  48 */     for (String str : arrayOfString) {
/*  49 */       stringBuilder.append(str);
/*     */     }
/*  51 */     this.data = stringBuilder.toString();
/*     */     
/*  53 */     if (StringUtil.contains(this.data, "{cmd:clean}")) {
/*  54 */       this.data = StringUtil.replace(this.data, "{cmd:clean}", "");
/*     */     } else {
/*  56 */       this.result += this.param;
/*     */     } 
/*     */     
/*  59 */     if (StringUtil.contains(this.data, "{cmd:format(\\[.+\\])}")) {
/*  60 */       this.format = getCmdValue(this.data, "{cmd:format(\\[.+\\])}");
/*  61 */       this.doFormat = true;
/*     */     } 
/*     */     
/*  64 */     if (StringUtil.contains(this.data, "{cmd:style(\\[.+\\])}")) {
/*  65 */       this.style = getCmdValue(this.data, "{cmd:style(\\[.+\\])}");
/*     */     }
/*     */   }
/*     */   
/*     */   private void parse() {
/*  70 */     if (StringUtil.contains(this.data, "{cmd:append(\\[.+\\])}")) {
/*  71 */       this.data = getCmdValue(this.data, "{cmd:append(\\[.+\\])}");
/*  72 */       parseAppend();
/*  73 */     } else if (StringUtil.contains(this.data, "{cmd:class(\\[.+\\])}")) {
/*  74 */       this.data = getCmdValue(this.data, "{cmd:class(\\[.+\\])}");
/*  75 */       parseClass();
/*  76 */     } else if (StringUtil.contains(this.data, "{cmd:array(\\[.+\\])}")) {
/*  77 */       this.data = getCmdValue(this.data, "{cmd:array(\\[.+\\])}");
/*  78 */       parseArray();
/*  79 */     } else if (StringUtil.contains(this.data, "{cmd:json(\\[.+\\])}")) {
/*  80 */       this.data = getCmdValue(this.oldData, "{cmd:json(\\[.+\\])}", true);
/*  81 */       parseJson();
/*  82 */     } else if (StringUtil.contains(this.data, "{cmd:img(\\[.+\\])}")) {
/*  83 */       this.data = getCmdValue(this.data, "{cmd:img(\\[.+\\])}");
/*  84 */       this.doFormat = false;
/*  85 */       parseImg();
/*  86 */     } else if (StringUtil.contains(this.data, "{cmd:link(\\[.+\\])}")) {
/*  87 */       this.data = getCmdValue(this.data, "{cmd:link(\\[.+\\])}");
/*  88 */       parseLink();
/*     */     }
/*  90 */     else if (!this.doFormat) {
/*  91 */       parseAppend();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void parseImg() {
/*  97 */     String str = "";
/*  98 */     if (isExpression(this.data)) {
/*  99 */       str = str + getExpressionValue(this.data);
/* 100 */     } else if (this.data.indexOf("=") != -1) {
/* 101 */       str = str + getArrayValue(this.param, this.data);
/*     */     } else {
/* 103 */       str = str + this.data;
/*     */     } 
/* 105 */     if (StringUtil.isNotNull(str)) {
/* 106 */       str = "<img src = '" + str + "' />";
/*     */     }
/* 108 */     this.result = str;
/*     */   }
/*     */   
/*     */   private void parseClass() {
/* 112 */     this.result = getClassValue(this.data);
/*     */   }
/*     */   
/*     */   private String getClassValue(String paramString) {
/* 116 */     String[] arrayOfString = paramString.split("\\(");
/* 117 */     String str1 = "";
/* 118 */     String str2 = "";
/* 119 */     String str3 = StringUtil.vString(arrayOfString[0]);
/* 120 */     int i = str3.lastIndexOf(".");
/* 121 */     if (i == -1) {
/* 122 */       throw new RuntimeException("format error:" + paramString);
/*     */     }
/* 124 */     str1 = str3.substring(0, i);
/* 125 */     str2 = str3.substring(i + ".".length());
/*     */     
/* 127 */     ArrayList<String> arrayList = new ArrayList();
/* 128 */     String str4 = StringUtil.vString(arrayOfString[1]);
/* 129 */     if (!str4.endsWith(")")) {
/* 130 */       throw new RuntimeException("format error:" + paramString);
/*     */     }
/* 132 */     str4 = str4.substring(0, str4.length() - 1);
/* 133 */     if (str4.length() > 0) {
/* 134 */       String[] arrayOfString1 = str4.split(",");
/* 135 */       for (String str : arrayOfString1) {
/* 136 */         str = StringUtil.vString(str);
/* 137 */         if (str.length() > 0 && isExpression(str)) {
/* 138 */           str = getExpressionValue(str);
/*     */         }
/* 140 */         arrayList.add(str);
/*     */       } 
/*     */     } 
/* 143 */     return StringUtil.doInvoke(str1, str2, arrayList);
/*     */   }
/*     */   
/*     */   private void parseAppend() {
/* 147 */     if (isExpression(this.data)) {
/* 148 */       this.result += getExpressionValue(this.data);
/*     */     } else {
/* 150 */       String str1 = "getValue(";
/* 151 */       String str2 = ")";
/* 152 */       int i = this.data.indexOf(str1);
/* 153 */       int j = this.data.indexOf(str2);
/* 154 */       if (i != -1 && j != -1) {
/* 155 */         this.result += this.data.substring(0, i);
/* 156 */         this.data = this.data.substring(i + str1.length(), j);
/* 157 */         String[] arrayOfString = StringUtil.split(this.data, ";");
/* 158 */         if (arrayOfString.length == 3) {
/* 159 */           String str3 = StringUtil.vString(arrayOfString[0]);
/* 160 */           boolean bool = (!StringUtil.isNull(arrayOfString[1]) && !StringUtil.vString(arrayOfString[1]).equals("-1")) ? true : false;
/* 161 */           int k = StringUtil.parseToInt(arrayOfString[1], 7);
/* 162 */           String str4 = arrayOfString[2];
/* 163 */           String str5 = getArrayValue(str3, str4);
/* 164 */           this.result += bool ? getHtmlLabelValue(str5, k) : str5;
/*     */         } 
/* 166 */         if (this.data.length() >= j + str2.length()) {
/* 167 */           this.result += this.data.substring(j + str2.length());
/*     */         }
/*     */       } else {
/* 170 */         this.result += this.data;
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void parseArray() {
/* 176 */     this.result = getArrayValue(this.data);
/*     */   }
/*     */   
/*     */   private String getArrayValue(String paramString) {
/* 180 */     String str1 = paramString;
/* 181 */     String[] arrayOfString = StringUtil.split(str1, ";");
/* 182 */     boolean bool = false;
/* 183 */     int i = 0;
/* 184 */     if (arrayOfString.length == 2) {
/* 185 */       bool = (!StringUtil.isNull(arrayOfString[0]) && !StringUtil.vString(arrayOfString[0]).equals("-1")) ? true : false;
/* 186 */       i = StringUtil.parseToInt(arrayOfString[0], 7);
/* 187 */       str1 = arrayOfString[1];
/*     */     } 
/* 189 */     String str2 = getArrayValue(this.param, str1);
/* 190 */     return bool ? getHtmlLabelValue(str2, i) : str2;
/*     */   }
/*     */   
/*     */   private void parseLink() {
/* 194 */     String[] arrayOfString1 = null;
/* 195 */     String str = this.data;
/* 196 */     if (this.data.indexOf("|") != -1) {
/* 197 */       arrayOfString1 = StringUtil.split(this.data, "|");
/* 198 */       if (arrayOfString1.length != 2)
/*     */         return; 
/* 200 */       String str1 = StringUtil.vString(arrayOfString1[0]);
/* 201 */       String str2 = "";
/* 202 */       if (str1.startsWith("class:")) {
/* 203 */         str2 = str1.substring("class:".length());
/* 204 */         this.param = getClassValue(str2);
/* 205 */       } else if (str1.startsWith("array:")) {
/* 206 */         str2 = str1.substring("array:".length());
/* 207 */         this.param = getArrayValue(str2);
/*     */       } 
/* 209 */       str = arrayOfString1[1];
/*     */     } 
/* 211 */     arrayOfString1 = StringUtil.split(str, ";");
/* 212 */     if (arrayOfString1.length != 2)
/*     */       return; 
/* 214 */     String[] arrayOfString2 = arrayOfString1[1].split("___");
/* 215 */     StringBuffer stringBuffer = (new StringBuffer("<a href=\"")).append(arrayOfString1[0]).append("(");
/* 216 */     for (byte b = 0; b < arrayOfString2.length; b++) {
/* 217 */       stringBuffer.append("'").append(arrayOfString2[b]).append("'");
/* 218 */       if (b == arrayOfString2.length - 1)
/* 219 */         break;  stringBuffer.append(",");
/*     */     } 
/* 221 */     stringBuffer.append(")\">").append(this.param).append("</a>");
/* 222 */     this.result = stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private void parseJson() {
/* 226 */     String[] arrayOfString1 = StringUtil.split(this.data, ";");
/* 227 */     if (arrayOfString1.length != 3) {
/* 228 */       throw new RuntimeException("format error:" + this.data);
/*     */     }
/* 230 */     String str1 = StringUtil.vString(arrayOfString1[0]);
/* 231 */     boolean bool = false;
/* 232 */     if (str1.indexOf("[") != -1) {
/* 233 */       String[] arrayOfString = str1.split("\\\\[");
/* 234 */       str1 = arrayOfString[0];
/* 235 */       if (!arrayOfString[1].endsWith("]")) {
/* 236 */         throw new RuntimeException("format error:" + str1);
/*     */       }
/* 238 */       bool = arrayOfString[1].substring(0, arrayOfString[1].length() - 1).equalsIgnoreCase("y");
/*     */     } 
/* 240 */     String[] arrayOfString2 = StringUtil.split(arrayOfString1[1], "+");
/* 241 */     StringBuilder stringBuilder = new StringBuilder();
/* 242 */     for (String str : arrayOfString2) {
/* 243 */       str = StringUtil.vString(str);
/* 244 */       stringBuilder.append(str.equalsIgnoreCase("and") ? "&" : str);
/*     */     } 
/* 246 */     String[] arrayOfString3 = stringBuilder.toString().split("&");
/* 247 */     if (arrayOfString3 == null || arrayOfString3.length == 0) {
/*     */       return;
/*     */     }
/* 250 */     String str2 = "";
/* 251 */     MJson mJson = new MJson(arrayOfString1[2]);
/* 252 */     while (mJson.next()) {
/* 253 */       byte b = 0;
/* 254 */       for (String str : arrayOfString3) {
/* 255 */         String[] arrayOfString = str.split("\\=");
/* 256 */         if (arrayOfString != null && arrayOfString.length == 2) {
/*     */ 
/*     */           
/* 259 */           String str3 = StringUtil.vString(mJson.getString(arrayOfString[0]));
/* 260 */           String str4 = arrayOfString[1];
/* 261 */           if (bool) {
/* 262 */             str3 = str3.toLowerCase();
/* 263 */             str4 = str4.toLowerCase();
/*     */           } 
/* 265 */           if (str3.equals(str4))
/* 266 */             b++; 
/*     */         } 
/*     */       } 
/* 269 */       if (b == arrayOfString3.length) {
/* 270 */         str2 = StringUtil.vString(mJson.getString(str1));
/*     */         break;
/*     */       } 
/*     */     } 
/* 274 */     this.result = str2;
/*     */   }
/*     */   
/*     */   private boolean isExpression(String paramString) {
/* 278 */     boolean bool1 = (paramString.indexOf("?") != -1) ? true : false;
/* 279 */     boolean bool2 = (paramString.indexOf(":") != -1) ? true : false;
/* 280 */     return (bool1 && bool2);
/*     */   }
/*     */   
/*     */   private String getExpressionValue(String paramString) {
/* 284 */     String[] arrayOfString1 = paramString.split("\\?");
/* 285 */     String[] arrayOfString2 = arrayOfString1[1].split("\\:");
/* 286 */     String str = StringUtil.vString(StringUtil.replace(arrayOfString1[0], "null", ""));
/* 287 */     return StringUtil.vString((str.equals("==") || StringUtil.compare(str)) ? arrayOfString2[0] : arrayOfString2[1]);
/*     */   }
/*     */   
/*     */   private String getHtmlLabelValue(String paramString, int paramInt) {
/* 291 */     String str = "";
/* 292 */     paramString = StringUtil.replace(paramString, "and", ",");
/* 293 */     if (paramString.indexOf(",") == -1) {
/* 294 */       str = SystemEnv.getHtmlLabelName(StringUtil.parseToInt(paramString), paramInt);
/*     */     } else {
/* 296 */       str = SystemEnv.getHtmlLabelNames(paramString, paramInt);
/*     */     } 
/* 298 */     return str;
/*     */   }
/*     */   
/*     */   private void formatValue() {
/* 302 */     if (!this.doFormat || StringUtil.isNull(this.format) || StringUtil.isNull(this.result)) {
/*     */       return;
/*     */     }
/* 305 */     String[] arrayOfString = StringUtil.split(this.format, ";");
/* 306 */     for (String str : arrayOfString) {
/* 307 */       String[] arrayOfString1 = str.split("\\:");
/* 308 */       if (arrayOfString1 != null && arrayOfString1.length == 2) {
/*     */         int i;
/*     */         
/* 311 */         String str1 = StringUtil.vString(arrayOfString1[0]);
/* 312 */         String str2 = StringUtil.vString(arrayOfString1[1]);
/* 313 */         switch (getFormatCmdIndex(str1)) {
/*     */ 
/*     */           
/*     */           case 0:
/* 317 */             i = StringUtil.parseToInt(str2);
/* 318 */             if (i > 0 && this.result.length() > i) {
/* 319 */               this.result = this.result.substring(0, i);
/* 320 */               this.result += "...";
/*     */             } 
/*     */             break;
/*     */           case 1:
/* 324 */             this.result = DateUtil.formatDate(this.result, str2);
/*     */             break;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */   private void setStyleValue() {
/* 331 */     if (StringUtil.isNull(this.style)) {
/*     */       return;
/*     */     }
/* 334 */     String str = "";
/* 335 */     if (isExpression(this.style)) {
/* 336 */       str = getExpressionValue(this.style);
/* 337 */       str = StringUtil.replace(str, "-", ":");
/* 338 */     } else if (this.style.indexOf("=") != -1) {
/* 339 */       str = getArrayValue(this.param, this.style);
/*     */     } else {
/* 341 */       str = this.style;
/*     */     } 
/* 343 */     if (StringUtil.isNotNull(str)) {
/* 344 */       StringBuilder stringBuilder = new StringBuilder();
/* 345 */       stringBuilder.append("<span style=\"").append(str).append("\">").append(this.result).append("</span>");
/* 346 */       this.result = stringBuilder.toString();
/*     */     } 
/*     */   }
/*     */   
/*     */   private String getArrayValue(String paramString1, String paramString2) {
/* 351 */     String str1 = "";
/* 352 */     String[] arrayOfString = paramString2.split(",");
/* 353 */     String str2 = "";
/* 354 */     byte b = 0;
/* 355 */     for (String str : arrayOfString) {
/* 356 */       String[] arrayOfString1 = str.split("\\=");
/* 357 */       if (arrayOfString1 != null && arrayOfString1.length == 2) {
/*     */ 
/*     */         
/* 360 */         if (arrayOfString1[0].equals(paramString1)) {
/* 361 */           str1 = arrayOfString1[1];
/*     */           break;
/*     */         } 
/* 364 */         if (arrayOfString1[0].equalsIgnoreCase("default")) {
/* 365 */           str2 = arrayOfString1[1];
/*     */         }
/* 367 */         b++;
/*     */       } 
/*     */     } 
/* 370 */     if (str2.length() > 0 && b == arrayOfString.length) {
/* 371 */       str1 = str2;
/*     */     }
/* 373 */     return StringUtil.vString(str1);
/*     */   }
/*     */   
/*     */   private String getCmdValue(String paramString1, String paramString2) {
/* 377 */     return getCmdValue(paramString1, paramString2, false);
/*     */   }
/*     */   
/*     */   private String getCmdValue(String paramString1, String paramString2, boolean paramBoolean) {
/* 381 */     paramString1 = StringUtil.getMatchResult(paramString1, paramString2);
/* 382 */     paramString1 = StringUtil.removeTag(paramString1, "[", "]", paramBoolean);
/* 383 */     return paramString1;
/*     */   }
/*     */   
/*     */   private int getFormatCmdIndex(String paramString) {
/* 387 */     byte b = -1;
/* 388 */     for (byte b1 = 0; b1 < this.formatCMD.length; b1++) {
/* 389 */       if (this.formatCMD[b1].equalsIgnoreCase(paramString)) {
/* 390 */         b = b1;
/*     */         break;
/*     */       } 
/*     */     } 
/* 394 */     return b;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/SplitPageTagFormat.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */