/*    */ package weaver.hrm.common;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class SplitPageTagOperate
/*    */ {
/*    */   public static ArrayList<String> getBasicOperate(String paramString1, String paramString2) {
/* 18 */     ArrayList<String> arrayList = new ArrayList();
/* 19 */     String[] arrayOfString = paramString2.split(":");
/* 20 */     for (byte b = 0; b < ((arrayOfString == null) ? 0 : arrayOfString.length); b++) {
/* 21 */       arrayList.add(String.valueOf(getOperateValue(arrayOfString[b])));
/*    */     }
/* 23 */     return arrayList;
/*    */   }
/*    */   
/*    */   public static ArrayList<String> getBasicOperate2(String paramString1, String paramString2) {
/* 27 */     ArrayList<String> arrayList = new ArrayList();
/* 28 */     String[] arrayOfString = paramString2.split(":");
/* 29 */     for (byte b = 0; b < ((arrayOfString == null) ? 0 : arrayOfString.length); b++) {
/* 30 */       if (b == 1 && Util.null2String(arrayOfString[4]).indexOf("isIE") > -1) {
/* 31 */         if (arrayOfString[5].equals("+0+=0")) {
/* 32 */           arrayList.add("true");
/*    */         }
/* 34 */         else if (arrayOfString[4].equals("isIE=true")) {
/*    */           
/* 36 */           arrayList.add("true");
/*    */         } else {
/* 38 */           arrayList.add("false");
/*    */         }
/*    */       
/*    */       } else {
/*    */         
/* 43 */         arrayList.add(String.valueOf(getOperateValue(arrayOfString[b])));
/*    */       } 
/*    */     } 
/* 46 */     return arrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getBasicCheckbox(String paramString) {
/* 54 */     return String.valueOf(getOperateValue(paramString));
/*    */   }
/*    */   
/*    */   private static boolean getOperateValue(String paramString) {
/* 58 */     String str = Tools.replace(paramString, "+", "");
/* 59 */     str = Tools.replace(str, "and", "&&");
/* 60 */     str = Tools.replace(str, "or", "||");
/* 61 */     int i = str.indexOf("[");
/* 62 */     int j = str.indexOf("]");
/* 63 */     if (i != -1 && j != -1) {
/* 64 */       return PluginOperate.check(str.substring(i + "[".length(), j));
/*    */     }
/* 66 */     return Tools.compare(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/SplitPageTagOperate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */