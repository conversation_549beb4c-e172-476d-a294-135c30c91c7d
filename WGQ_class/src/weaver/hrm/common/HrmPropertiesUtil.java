/*    */ package weaver.hrm.common;
/*    */ 
/*    */ import weaver.common.EnumUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmPropertiesUtil
/*    */ {
/* 14 */   private EnumUtil util = null;
/*    */   
/*    */   public HrmPropertiesUtil() {
/* 17 */     this.util = new EnumUtil("hrm_mfstyle.properties");
/*    */   }
/*    */   
/*    */   public String getValue(String paramString) {
/* 21 */     return this.util.getProperty(paramString);
/*    */   }
/*    */   
/*    */   public void updateValue(String paramString1, String paramString2) {
/* 25 */     this.util.updateValue(paramString1, paramString2);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/HrmPropertiesUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */