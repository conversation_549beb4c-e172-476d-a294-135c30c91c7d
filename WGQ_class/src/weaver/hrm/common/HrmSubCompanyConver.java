/*    */ package weaver.hrm.common;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.lang3.StringUtils;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.webservice.exception.HrmWebServiceException;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSubCompanyConver
/*    */ {
/*    */   public static String getIdFromJsonValue(String paramString) {
/* 15 */     if (StringUtils.isBlank(paramString)) return null; 
/* 16 */     if (!HrmValueConver.isJsonValue(paramString)) return null; 
/* 17 */     Map<String, Object> map = HrmValueConver.getJsonMapByValue(paramString);
/*    */     
/* 19 */     StringBuffer stringBuffer = new StringBuffer();
/* 20 */     ArrayList<Object> arrayList = new ArrayList();
/* 21 */     for (Map.Entry<String, Object> entry : map.entrySet()) {
/* 22 */       String str = (String)entry.getKey();
/* 23 */       Object object = entry.getValue();
/* 24 */       if (object == null || StringUtils.isBlank(object.toString())) {
/*    */         continue;
/*    */       }
/* 27 */       if (stringBuffer.length() > 0) stringBuffer.append(" and "); 
/* 28 */       stringBuffer.append(str).append("=?");
/* 29 */       arrayList.add(object);
/*    */     } 
/*    */     
/* 32 */     if (arrayList.size() == 0) {
/* 33 */       throw new HrmWebServiceException("-1", "数据不可以为空：" + paramString);
/*    */     }
/*    */     
/* 36 */     RecordSet recordSet = new RecordSet();
/* 37 */     recordSet.executeQuery(String.format("select id from hrmsubcompany where %s ", new Object[] { stringBuffer.toString() }), new Object[] { arrayList });
/* 38 */     if (recordSet.getCounts() > 1 || recordSet.getCounts() == 0) {
/* 39 */       throw new HrmWebServiceException("-1", "数据不唯一或找不到：" + paramString);
/*    */     }
/*    */     
/* 42 */     recordSet.next();
/* 43 */     return Util.null2String(recordSet.getString("id"));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/common/HrmSubCompanyConver.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */