/*     */ package weaver.hrm.synorg;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.file.ExcelParse;
/*     */ import weaver.file.FileUploadToPath;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.common.database.dialect.DialectUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class SynOrganization
/*     */   extends BaseBean
/*     */ {
/*  19 */   private String currentdate = "";
/*  20 */   private String currenttime = "";
/*  21 */   private ArrayList errorRow = new ArrayList();
/*     */   
/*     */   public ArrayList getErrorRow() {
/*  24 */     return this.errorRow;
/*     */   }
/*     */   
/*     */   public void setErrorRow(ArrayList paramArrayList) {
/*  28 */     this.errorRow = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String ImportData(FileUploadToPath paramFileUploadToPath, User paramUser) {
/*  43 */     String str1 = "";
/*  44 */     String str2 = "";
/*  45 */     RecordSet recordSet = new RecordSet();
/*  46 */     boolean bool = recordSet.getDBType().equals("oracle");
/*     */ 
/*     */     
/*  49 */     String str3 = paramFileUploadToPath.uploadFiles("excelfile");
/*  50 */     ExcelParse excelParse = new ExcelParse();
/*  51 */     excelParse.init(str3);
/*  52 */     byte b = 1;
/*  53 */     boolean bool1 = false;
/*  54 */     boolean bool2 = false;
/*     */     
/*  56 */     str2 = "select id,tablename,datakey from Syn_Organization order by id asc";
/*  57 */     recordSet.executeSql(str2);
/*  58 */     while (recordSet.next()) {
/*  59 */       int i = 0;
/*  60 */       RecordSet recordSet1 = new RecordSet();
/*  61 */       String str4 = Util.null2String(recordSet.getString("tablename")).toLowerCase();
/*  62 */       String str5 = Util.null2String(recordSet.getString("datakey")).toLowerCase();
/*  63 */       if (bool) {
/*  64 */         str2 = "select COLUMN_NAME name,data_type type from user_tab_columns where upper(table_name)=upper('" + str4 + "') ORDER BY COLUMN_ID asc";
/*  65 */       } else if (DialectUtil.isMySql(recordSet.getDBType())) {
/*  66 */         str2 = "select column_name name,data_type type from information_schema.columns where upper(table_name)=upper('" + str4 + "') ORDER BY ordinal_position asc";
/*     */       }
/*  68 */       else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/*  69 */         str2 = "select column_name as name,data_type as type from information_schema.columns where upper(table_name)=upper('" + str4 + "') ORDER BY ordinal_position asc";
/*     */       } else {
/*     */         
/*  72 */         str2 = "select c.name name,t.name type from sysobjects o,syscolumns c,systypes t where c.xtype=t.xusertype and o.id=c.id and upper(o.name)=upper('" + str4 + "') order by c.colid asc";
/*     */       } 
/*  74 */       recordSet1.executeSql(str2);
/*  75 */       String[] arrayOfString1 = new String[recordSet1.getCounts()];
/*  76 */       String[] arrayOfString2 = new String[recordSet1.getCounts()];
/*  77 */       byte b1 = 0;
/*  78 */       while (recordSet1.next()) {
/*  79 */         String str8 = Util.null2String(recordSet1.getString("name")).toLowerCase();
/*  80 */         String str9 = Util.null2String(recordSet1.getString("type")).toLowerCase();
/*  81 */         arrayOfString1[b1] = str8;
/*  82 */         arrayOfString2[b1++] = str9;
/*     */       } 
/*     */ 
/*     */       
/*  86 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */       if (!str5.equals("")) {
/*  88 */         str2 = "select " + str5 + " from " + str4 + " order by " + str5 + " asc";
/*  89 */         recordSet1.executeSql(str2);
/*  90 */         while (recordSet1.next()) {
/*  91 */           String str = Util.null2String(recordSet1.getString(str5));
/*  92 */           hashMap.put(str, str);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*  97 */       String str6 = disabledTableObject(str4, bool);
/*  98 */       String str7 = enabledTableObject(str4, bool);
/*     */       
/* 100 */       RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 101 */       recordSetTrans.setAutoCommit(false);
/* 102 */       recordSetTrans.setChecksql(false);
/*     */       
/*     */       try {
/* 105 */         boolean bool3 = true;
/* 106 */         byte b2 = 2;
/* 107 */         while (bool3) {
/* 108 */           StringBuffer stringBuffer1 = new StringBuffer();
/* 109 */           StringBuffer stringBuffer2 = new StringBuffer();
/* 110 */           StringBuffer stringBuffer3 = new StringBuffer();
/* 111 */           StringBuffer stringBuffer4 = new StringBuffer();
/*     */           
/* 113 */           stringBuffer1.append(" insert into " + str4 + " (");
/* 114 */           stringBuffer2.append(" values (");
/* 115 */           stringBuffer3.append(" update " + str4 + " set ");
/*     */           
/* 117 */           boolean bool4 = true;
/* 118 */           boolean bool5 = false;
/* 119 */           String str = "";
/*     */ 
/*     */           
/* 122 */           for (byte b3 = 0; b3 < arrayOfString1.length; b3++) {
/* 123 */             String str8 = arrayOfString1[b3];
/* 124 */             String str9 = arrayOfString2[b3];
/*     */             
/* 126 */             int j = b3 + 1;
/* 127 */             String str10 = Util.null2String(excelParse.getValue("" + b, "" + b2, "" + j)).trim();
/* 128 */             if (str8.equalsIgnoreCase(str5)) {
/* 129 */               if (hashMap.containsKey(str10)) {
/* 130 */                 bool5 = true;
/* 131 */                 hashMap.put(str10, str10);
/* 132 */                 if (Util.getIntValue(str10) > i) {
/* 133 */                   i = Util.getIntValue(str10);
/*     */                 }
/*     */               } 
/* 136 */               str = str10;
/* 137 */               stringBuffer4.append(" where " + str5 + " = '" + str10 + "'");
/*     */             } 
/*     */ 
/*     */             
/* 141 */             if (!str10.equals("") && !str8.equalsIgnoreCase(str5)) {
/* 142 */               str10 = str10.replace("'", "''");
/* 143 */               stringBuffer1.append(" " + str8 + ",");
/* 144 */               stringBuffer2.append(" '" + str10 + "',");
/* 145 */               stringBuffer3.append(" " + str8 + " = '" + str10 + "',");
/* 146 */               bool4 = false;
/*     */             } 
/*     */           } 
/*     */           
/* 150 */           if (bool4) {
/* 151 */             bool3 = false;
/*     */           } else {
/* 153 */             String str8 = stringBuffer1.toString();
/* 154 */             str8 = str8 + str5 + ")";
/* 155 */             str8 = str8 + " " + stringBuffer2.toString();
/* 156 */             str8 = str8 + str + ")";
/*     */             
/* 158 */             String str9 = stringBuffer3.toString();
/* 159 */             str9 = str9.substring(0, str9.length() - 1);
/* 160 */             str9 = str9 + " " + stringBuffer4.toString();
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 165 */             boolean bool6 = false;
/* 166 */             if (bool5) {
/* 167 */               bool6 = recordSetTrans.executeSql(str9);
/*     */             }
/* 169 */             else if (str4.equalsIgnoreCase("hrmresource")) {
/* 170 */               bool6 = recordSetTrans.executeSql(str8);
/*     */             }
/* 172 */             else if (bool) {
/* 173 */               str6 = str6.replace(";", "");
/* 174 */               str7 = str7.replace(";", "");
/* 175 */               bool6 = recordSetTrans.executeSql(str6);
/* 176 */               bool6 = recordSetTrans.executeSql(str8);
/* 177 */               bool6 = recordSetTrans.executeSql(str7);
/*     */             } else {
/* 179 */               str8 = str6 + str8 + ";" + str7;
/* 180 */               bool6 = recordSetTrans.executeSql(str8);
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 185 */             if (bool6) {
/* 186 */               bool1 = true;
/*     */             } else {
/* 188 */               bool2 = true;
/*     */             } 
/*     */           } 
/* 191 */           b2++;
/*     */         } 
/*     */ 
/*     */         
/* 195 */         modifyMaxResourceid(str4, i, bool, recordSetTrans);
/*     */         
/* 197 */         recordSetTrans.commit();
/* 198 */       } catch (Exception exception) {
/* 199 */         bool2 = true;
/* 200 */         recordSetTrans.rollback();
/* 201 */         writeLog(exception);
/*     */       } 
/*     */       
/* 204 */       b++;
/*     */     } 
/*     */     
/* 207 */     if (bool1 && bool2) {
/* 208 */       str1 = SystemEnv.getHtmlLabelName(31247, paramUser.getLanguage());
/* 209 */     } else if (bool1 && !bool2) {
/* 210 */       str1 = SystemEnv.getHtmlLabelName(28450, paramUser.getLanguage());
/*     */     } else {
/* 212 */       str1 = SystemEnv.getHtmlLabelName(31246, paramUser.getLanguage());
/*     */     } 
/*     */     
/* 215 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String disabledTableObject(String paramString, boolean paramBoolean) {
/* 227 */     String str = "";
/* 228 */     if (paramBoolean && !ConnectionPool.getInstance().getOrgindbtype().equalsIgnoreCase("jc")) {
/* 229 */       if (paramString.equalsIgnoreCase("HrmSubCompany")) {
/* 230 */         str = "ALTER TRIGGER HrmSubCompany_Trigger DISABLE;";
/* 231 */       } else if (paramString.equalsIgnoreCase("HrmDepartment")) {
/* 232 */         str = "ALTER TRIGGER HrmDepartment_Trigger DISABLE;";
/* 233 */       } else if (paramString.equalsIgnoreCase("HrmJobTitles")) {
/* 234 */         str = "ALTER TRIGGER HrmJobTitles_Trigger DISABLE;";
/* 235 */       } else if (paramString.equalsIgnoreCase("HrmRoles")) {
/* 236 */         str = "ALTER TRIGGER HrmRoles_Trigger DISABLE;";
/* 237 */       } else if (paramString.equalsIgnoreCase("HrmRoleMembers")) {
/* 238 */         str = "ALTER TRIGGER HrmRoleMembers_Trigger DISABLE;";
/*     */       } 
/*     */     } else {
/* 241 */       str = "set IDENTITY_INSERT " + paramString + " on;";
/*     */     } 
/* 243 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String enabledTableObject(String paramString, boolean paramBoolean) {
/* 255 */     String str = "";
/* 256 */     if (paramBoolean && !"jc".equalsIgnoreCase(ConnectionPool.getInstance().getOrgindbtype())) {
/* 257 */       if (paramString.equalsIgnoreCase("HrmSubCompany")) {
/* 258 */         str = "ALTER TRIGGER HrmSubCompany_Trigger ENABLE;";
/* 259 */       } else if (paramString.equalsIgnoreCase("HrmDepartment")) {
/* 260 */         str = "ALTER TRIGGER HrmDepartment_Trigger ENABLE;";
/* 261 */       } else if (paramString.equalsIgnoreCase("HrmJobTitles")) {
/* 262 */         str = "ALTER TRIGGER HrmJobTitles_Trigger ENABLE;";
/* 263 */       } else if (paramString.equalsIgnoreCase("HrmRoles")) {
/* 264 */         str = "ALTER TRIGGER HrmRoles_Trigger ENABLE;";
/* 265 */       } else if (paramString.equalsIgnoreCase("HrmRoleMembers")) {
/* 266 */         str = "ALTER TRIGGER HrmRoleMembers_Trigger ENABLE;";
/*     */       } 
/*     */     } else {
/* 269 */       str = "set IDENTITY_INSERT " + paramString + " off;";
/*     */     } 
/* 271 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void modifyMaxResourceid(String paramString, int paramInt, boolean paramBoolean, RecordSetTrans paramRecordSetTrans) throws Exception {
/* 286 */     RecordSet recordSet = new RecordSet();
/* 287 */     String str = "";
/*     */     
/* 289 */     if (paramString.equalsIgnoreCase("hrmresource")) {
/* 290 */       int i = 0;
/* 291 */       str = "select currentid from SequenceIndex where upper(indexdesc) = upper('resourceid')";
/* 292 */       recordSet.executeSql(str);
/* 293 */       while (recordSet.next()) {
/* 294 */         i = recordSet.getInt("currentid");
/*     */       }
/*     */       
/* 297 */       if (paramInt >= i) {
/* 298 */         str = "update SequenceIndex set currentid = " + (paramInt + 1) + " where upper(indexdesc) = upper('resourceid')";
/* 299 */         recordSet.executeSql(str);
/*     */       } 
/*     */     } 
/* 302 */     if (paramBoolean) {
/* 303 */       String str1 = "";
/* 304 */       if (!paramString.equalsIgnoreCase("")) {
/* 305 */         if (paramString.equalsIgnoreCase("HrmSubCompany")) {
/* 306 */           str1 = "HrmSubCompany_id";
/* 307 */         } else if (paramString.equalsIgnoreCase("HrmDepartment")) {
/* 308 */           str1 = "HrmDepartment_id";
/* 309 */         } else if (paramString.equalsIgnoreCase("HrmJobTitles")) {
/* 310 */           str1 = "HrmJobTitles_id";
/* 311 */         } else if (paramString.equalsIgnoreCase("HrmRoles")) {
/* 312 */           str1 = "HrmRoles_id";
/* 313 */         } else if (paramString.equalsIgnoreCase("HrmRoleMembers")) {
/* 314 */           str1 = "HrmRoleMembers_id";
/*     */         } 
/*     */         
/* 317 */         if (!str1.equals("")) {
/* 318 */           modifySequence(str1, paramInt, paramRecordSetTrans);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void modifySequence(String paramString, int paramInt, RecordSetTrans paramRecordSetTrans) throws Exception {
/* 331 */     int i = 1;
/* 332 */     String str1 = "select " + paramString + ".nextval from dual";
/* 333 */     paramRecordSetTrans.executeSql(str1);
/* 334 */     while (paramRecordSetTrans.next()) {
/* 335 */       i = paramRecordSetTrans.getInt(1);
/*     */     }
/*     */     
/* 338 */     if (paramInt > i) {
/* 339 */       i = paramInt + 1;
/*     */     }
/*     */     
/* 342 */     str1 = "drop sequence " + paramString;
/* 343 */     paramRecordSetTrans.executeSql(str1);
/*     */     
/* 345 */     String str2 = "";
/* 346 */     if ("jc".equalsIgnoreCase(ConnectionPool.getInstance().getOrgindbtype())) {
/* 347 */       str2 = " MAXVALUE 9223372036854775807 ";
/*     */     } else {
/* 349 */       str2 = " nomaxvalue ";
/*     */     } 
/*     */     
/* 352 */     str1 = "create sequence " + paramString + " start with " + i + " increment by 1 " + str2 + " nocycle nocache";
/* 353 */     paramRecordSetTrans.executeSql(str1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/synorg/SynOrganization.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */