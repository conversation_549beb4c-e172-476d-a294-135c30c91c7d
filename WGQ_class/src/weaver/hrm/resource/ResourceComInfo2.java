/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ResourceComInfo2
/*     */   extends CacheBase
/*     */ {
/*  33 */   protected static String TABLE_NAME = "hrmresource";
/*     */   
/*  35 */   protected static String TABLE_WHERE = null;
/*     */   
/*  37 */   protected static String TABLE_ORDER = "dsporder asc,id asc";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  40 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn(name = "loginid")
/*     */   protected static int loginId;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int sex;
/*     */   
/*     */   @CacheColumn(name = "lastname")
/*     */   protected static int lastName;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int email;
/*     */   
/*     */   @CacheColumn(isVirtual = true)
/*     */   protected static int virtualColumn1;
/*     */   
/*     */   public CacheMap initCache() throws Exception {
/*  58 */     CacheMap cacheMap = super.initCache();
/*     */     
/*  60 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  62 */     recordSet.executeSql("select id,loginid,loginid,lastname,lastname from HrmResourceManager");
/*  63 */     while (recordSet.next()) {
/*  64 */       String str = Util.null2String(recordSet.getString(PK_NAME));
/*  65 */       CacheItem cacheItem = createCacheItem();
/*  66 */       parseResultSetToCacheItem(recordSet, cacheItem);
/*     */ 
/*     */       
/*  69 */       modifyCacheItem(str, cacheItem);
/*  70 */       cacheMap.put(str, cacheItem);
/*     */     } 
/*     */ 
/*     */     
/*  74 */     StaticObj.getInstance().putObject("PluginResourceComInfoUpdate", "1");
/*  75 */     return cacheMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void modifyCacheItem(String paramString, CacheItem paramCacheItem) {
/*  80 */     String str = (String)paramCacheItem.get(sex);
/*     */     
/*  82 */     paramCacheItem.set(sex, "0".equals(str) ? "男" : ("1".equals(str) ? "女" : "未知"));
/*     */     
/*  84 */     paramCacheItem.set(virtualColumn1, paramCacheItem.get(0) + "_" + paramCacheItem.get(lastName) + "_test_virtual");
/*     */   }
/*     */   
/*     */   public CacheItem initCache(String paramString) {
/*  88 */     CacheItem cacheItem = super.initCache(paramString);
/*  89 */     StaticObj.getInstance().putObject("PluginResourceComInfoUpdate", "1");
/*  90 */     return cacheItem;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResourceNum() {
/*  98 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourceid() {
/* 106 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourcename() {
/* 114 */     return (String)getRowValue(lastName);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFirstname() {
/* 122 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLoginID() {
/* 130 */     return (String)getRowValue(loginId);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLastname() {
/* 138 */     return (String)getRowValue(lastName);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSex() {
/* 146 */     return (String)getRowValue(sex);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEmail() {
/* 154 */     return (String)getRowValue(email);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourcename(String paramString) {
/* 168 */     return (String)getValue(lastName, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFirstname(String paramString) {
/* 178 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLoginID(String paramString) {
/* 187 */     return (String)getValue(loginId, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLastname(String paramString) {
/* 196 */     return (String)getValue(lastName, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSexs(String paramString) {
/* 205 */     return (String)getValue(sex, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEmail(String paramString) {
/* 214 */     return (String)getValue(email, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLastnameByEmail(String paramString) {
/* 225 */     RecordSet recordSet = new RecordSet();
/* 226 */     String str = getColumnName(lastName);
/* 227 */     recordSet.executeSql("select " + str + " from hrmresource where " + str + "='" + paramString + "'");
/* 228 */     if (recordSet.next()) {
/* 229 */       return recordSet.getString(str);
/*     */     }
/* 231 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateResourceInfoCache(String paramString) {
/* 241 */     updateCache(paramString);
/* 242 */     StaticObj.getInstance().putObject("PluginResourceComInfoUpdate", "1");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteResourceInfoCache(String paramString) {
/* 250 */     deleteCache(paramString);
/* 251 */     StaticObj.getInstance().putObject("PluginResourceComInfoUpdate", "1");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeResourceCache() {
/* 258 */     removeCache();
/* 259 */     StaticObj.getInstance().putObject("PluginResourceComInfoUpdate", "1");
/*     */   }
/*     */ 
/*     */   
/*     */   public String getVirtualColumn1() {
/* 264 */     return (String)getRowValue(virtualColumn1);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getVirtualColumn1(String paramString) {
/* 269 */     return (String)getValue(virtualColumn1, paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/ResourceComInfo2.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */