/*     */ package weaver.hrm.resource;
/*     */ import java.util.ArrayList;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.appdetach.AppDetachComInfo;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class MutilResourceBrowser {
/*  21 */   public String mode = Prop.getPropValue(GCONST.getConfigFile(), "authentic");
/*  22 */   public AppDetachComInfo adci = null;
/*     */   
/*     */   public MutilResourceBrowser() {
/*  25 */     this.adci = new AppDetachComInfo();
/*     */   }
/*     */   
/*     */   public String getSelectedidsNum(String paramString1, String paramString2, String paramString3, User paramUser, String paramString4) throws Exception {
/*  29 */     String str = getExcludeSqlWhere(paramString1, paramString2, paramString3, paramUser, paramString4);
/*  30 */     if (Util.null2String(str).length() == 0) return "0"; 
/*  31 */     return "" + (Util.TokenizerString2(str, ",")).length;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getExcludeSqlWhere(String paramString1, String paramString2, String paramString3, User paramUser, String paramString4) throws Exception {
/*  44 */     if (Util.null2String(paramString1).length() == 0) return ""; 
/*  45 */     if (paramString1.startsWith(",")) {
/*  46 */       paramString1 = paramString1.substring(1);
/*     */     }
/*  48 */     String[] arrayOfString1 = paramString1.split(",");
/*  49 */     String str = "";
/*  50 */     ArrayList<String> arrayList = new ArrayList();
/*  51 */     for (byte b = 0; arrayOfString1 != null && b < arrayOfString1.length; b++) {
/*  52 */       String[] arrayOfString = Util.TokenizerString2(arrayOfString1[b], "_");
/*  53 */       if (arrayOfString.length == 1) {
/*     */         
/*  55 */         if (str.length() > 0) str = str + ","; 
/*  56 */         str = str + arrayOfString[0];
/*     */       } else {
/*  58 */         String str1 = arrayOfString[0];
/*  59 */         String str2 = arrayOfString[1];
/*  60 */         if (Integer.parseInt(str2) < -1) {
/*     */           
/*  62 */           if (str.length() > 0) str = str + ","; 
/*  63 */           str = str + getComDeptResourceVirtualIds(arrayOfString1[b], paramString2, paramString3, paramUser, paramString4);
/*     */         }
/*  65 */         else if (str1.startsWith("group")) {
/*  66 */           if (str.length() > 0) str = str + ","; 
/*  67 */           str = str + getGroupResourceIds(str2, paramString3, paramUser, paramString4);
/*     */         } else {
/*  69 */           if (str.length() > 0) str = str + ","; 
/*  70 */           str = str + getComDeptResourceIds(arrayOfString1[b], paramString2, paramString3, paramUser, paramString4);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  76 */     String[] arrayOfString2 = str.split(",");
/*  77 */     str = "";
/*  78 */     for (String str1 : arrayOfString2) {
/*  79 */       if (!arrayList.contains(str1)) {
/*  80 */         arrayList.add(str1);
/*  81 */         if (str.length() > 0) str = str + ","; 
/*  82 */         str = str + str1;
/*     */       } 
/*     */     } 
/*  85 */     return str;
/*     */   }
/*     */   
/*     */   public int getGroupResourceNum(String paramString1, String paramString2, User paramUser, String paramString3) throws Exception {
/*  89 */     int i = 0;
/*  90 */     String str = getGroupResourceIds(paramString1, paramString2, paramUser, paramString3);
/*  91 */     if (str.length() > 0) {
/*  92 */       i = (Util.TokenizerString2(str, ",")).length;
/*     */     }
/*  94 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGroupResourceIds(String paramString1, String paramString2, User paramUser, String paramString3) throws Exception {
/* 106 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 108 */     if (paramString3.length() > 0) {
/* 109 */       if (paramString3.trim().startsWith("and id in"))
/* 110 */         paramString3 = paramString3.replaceFirst("id in", "hr.id in"); 
/* 111 */       if (paramString3.trim().startsWith("and")) {
/* 112 */         paramString3 = " " + paramString3;
/*     */       } else {
/* 114 */         paramString3 = " and " + paramString3;
/*     */       } 
/*     */     } 
/*     */     
/* 118 */     if (this.adci.isUseAppDetach()) {
/* 119 */       String str4 = this.adci.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/* 120 */       String str5 = (str4 != null && !"".equals(str4)) ? (" and " + str4) : "";
/* 121 */       paramString3 = paramString3 + str5;
/*     */     } 
/* 123 */     String str1 = "select hr.id, lastname, hr.pinyinlastname, hr.subcompanyid1, hr.jobtitle  from hrmresource hr, HrmGroupMembers t2  where hr.id= userid and groupid in (" + paramString1 + ")";
/*     */ 
/*     */     
/* 126 */     paramString3 = paramString3 + " and hr.status in (0,1,2,3)";
/*     */     
/* 128 */     String str2 = "";
/* 129 */     if (!paramString2.equals("1")) {
/* 130 */       if (this.mode == null || !this.mode.equals("ldap")) {
/* 131 */         str2 = " and hr.loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and hr.loginid<>'' ");
/*     */       } else {
/*     */         
/* 134 */         str2 = " and hr.loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and hr.loginid<>'' ");
/*     */       } 
/*     */     }
/*     */     
/* 138 */     paramString3 = paramString3 + (paramString2.equals("1") ? "" : str2);
/* 139 */     if (paramString3.length() > 0) str1 = str1 + paramString3; 
/* 140 */     String str3 = "";
/* 141 */     str1 = str1 + " order by t2.dsporder";
/* 142 */     recordSet.executeSql(str1);
/* 143 */     while (recordSet.next()) {
/* 144 */       String str = recordSet.getString("id");
/*     */       
/* 146 */       if (str3.length() > 0) str3 = str3 + ","; 
/* 147 */       str3 = str3 + str;
/*     */     } 
/* 149 */     return str3;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONArray getGroupResource(String paramString1, String paramString2, String paramString3, String paramString4, User paramUser, String paramString5) throws Exception {
/* 154 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 155 */     JSONArray jSONArray = new JSONArray();
/* 156 */     RecordSet recordSet = new RecordSet();
/* 157 */     String str = "";
/*     */     
/* 159 */     if (paramString1.equals("-1")) {
/*     */       
/* 161 */       paramString1 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 171 */       str = " select distinct t1.id,t1.name from HrmGroup t1 , HrmGroupShare t2  where t1.id=t2.groupid and (t2.userid=" + paramUser.getUID() + " or (t2.departmentid=" + paramUser.getUserDepartment() + " and t2.seclevel<=" + Util.getIntValue(paramUser.getSeclevel()) + " and t2.seclevelto>=" + Util.getIntValue(paramUser.getSeclevel()) + ")  or (t2.subcompanyid=" + paramUser.getUserSubCompany1() + " and t2.seclevel<=" + Util.getIntValue(paramUser.getSeclevel()) + " and t2.seclevelto>=" + Util.getIntValue(paramUser.getSeclevel()) + ")  or (t2.foralluser=1 and t2.seclevel<=" + Util.getIntValue(paramUser.getSeclevel()) + " and t2.seclevelto>=" + Util.getIntValue(paramUser.getSeclevel()) + ")) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + paramUser.getUID() + " AND t3.resourcetype in(1,7,8))) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + paramUser.getUserSubCompany1() + " AND t3.resourcetype = 2 AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " >= t3.seclevelfrom AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " <= t3.seclevelto)) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + paramUser.getUserDepartment() + " AND t3.resourcetype = 3 AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " >= t3.seclevelfrom AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " <= t3.seclevelto)) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + Util.getIntValue(paramUser.getJobtitle(), 0) + " AND t3.resourcetype = 5 AND ( t3.jobtitlelevel = 1 OR ( t3.jobtitlelevel = 2 AND t3.subdepid = " + paramUser.getUserSubCompany1() + " ) OR ( t3.jobtitlelevel = 3 AND t3.subdepid = " + paramUser.getUserDepartment() + " )))) union select distinct t1.id,t1.name from HrmGroup t1,HrmGroupShare t2 , HrmJobTitles t3  where (t1.id = t2.groupid AND t2.jobtitleid = t3.id and t3.id='" + paramUser.getJobtitle() + "'  and (t2.jobtitlelevel=0 OR (t2.jobtitlelevel=1 AND t2.scopeid like '%," + paramUser.getUserDepartment() + ",%') or(t2.jobtitlelevel=2 AND t2.scopeid like '%," + paramUser.getUserSubCompany1() + ",%'))) ";
/* 172 */       recordSet.executeSql(str);
/* 173 */       while (recordSet.next()) {
/* 174 */         if (paramString1.length() > 0) paramString1 = paramString1 + ","; 
/* 175 */         paramString1 = paramString1 + recordSet.getString("id");
/*     */       } 
/*     */       
/* 178 */       str = "select id,name from HrmGroup where owner=" + paramUser.getUID() + " and type=0 ";
/* 179 */       recordSet.executeSql(str);
/* 180 */       while (recordSet.next()) {
/* 181 */         if (paramString1.length() > 0) paramString1 = paramString1 + ","; 
/* 182 */         paramString1 = paramString1 + recordSet.getString("id");
/*     */       } 
/* 184 */     } else if (paramString1.equals("-2")) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 194 */       str = " select distinct t1.id,t1.name from HrmGroup t1 , HrmGroupShare t2  where t1.id=t2.groupid and (t2.userid=" + paramUser.getUID() + " or (t2.departmentid=" + paramUser.getUserDepartment() + " and t2.seclevel<=" + Util.getIntValue(paramUser.getSeclevel()) + " and t2.seclevelto>=" + Util.getIntValue(paramUser.getSeclevel()) + ")  or (t2.subcompanyid=" + paramUser.getUserSubCompany1() + " and t2.seclevel<=" + Util.getIntValue(paramUser.getSeclevel()) + " and t2.seclevelto>=" + Util.getIntValue(paramUser.getSeclevel()) + ")  or (t2.foralluser=1 and t2.seclevel<=" + Util.getIntValue(paramUser.getSeclevel()) + " and t2.seclevelto>=" + Util.getIntValue(paramUser.getSeclevel()) + ")) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + paramUser.getUID() + " AND t3.resourcetype in(1,7,8))) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + paramUser.getUserSubCompany1() + " AND t3.resourcetype = 2 AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " >= t3.seclevelfrom AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " <= t3.seclevelto)) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + paramUser.getUserDepartment() + " AND t3.resourcetype = 3 AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " >= t3.seclevelfrom AND " + Util.getIntValue(paramUser.getSeclevel(), 0) + " <= t3.seclevelto)) UNION SELECT DISTINCT t1.id ,t1.name FROM HrmGroup t1 ,HrmGroupShare t2 , HrmRoleMembers t3 WHERE ( t1.type = 1 AND t2.groupid = t1.id AND t2.roleid = t3.roleid AND t2.rolelevel <= t3.rolelevel AND (t3.resourceid = " + Util.getIntValue(paramUser.getJobtitle(), 0) + " AND t3.resourcetype = 5 AND ( t3.jobtitlelevel = 1 OR ( t3.jobtitlelevel = 2 AND t3.subdepid = " + paramUser.getUserSubCompany1() + " ) OR ( t3.jobtitlelevel = 3 AND t3.subdepid = " + paramUser.getUserDepartment() + " )))) union select distinct t1.id,t1.name from HrmGroup t1,HrmGroupShare t2 , HrmJobTitles t3  where (t1.id = t2.groupid AND t2.jobtitleid = t3.id and t3.id='" + paramUser.getJobtitle() + "'  and (t2.jobtitlelevel=0 OR (t2.jobtitlelevel=1 AND t2.scopeid like '%," + paramUser.getUserDepartment() + ",%') or(t2.jobtitlelevel=2 AND t2.scopeid like '%," + paramUser.getUserSubCompany1() + ",%'))) ";
/* 195 */       recordSet.executeSql(str);
/* 196 */       while (recordSet.next()) {
/* 197 */         if (paramString1.length() > 0) paramString1 = paramString1 + ","; 
/* 198 */         paramString1 = paramString1 + recordSet.getString("id");
/*     */       } 
/* 200 */     } else if (paramString1.equals("-3")) {
/* 201 */       str = "select id,name from HrmGroup where owner=" + paramUser.getUID() + " and type=0 ";
/* 202 */       recordSet.executeSql(str);
/* 203 */       while (recordSet.next()) {
/* 204 */         if (paramString1.length() > 0) paramString1 = paramString1 + ","; 
/* 205 */         paramString1 = paramString1 + recordSet.getString("id");
/*     */       } 
/*     */     } 
/*     */     
/* 209 */     if (paramString5.length() > 0) paramString5 = " and " + paramString5;
/*     */     
/* 211 */     if (this.adci.isUseAppDetach()) {
/* 212 */       String str1 = this.adci.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/* 213 */       String str2 = (str1 != null && !"".equals(str1)) ? (" and " + str1) : "";
/* 214 */       paramString5 = paramString5 + str2;
/*     */     } 
/*     */     
/* 217 */     str = "select hr.id, lastname, hr.pinyinlastname, hr.subcompanyid1, hr.jobtitle  from hrmresource hr, HrmGroupMembers t2  where hr.id= userid and groupid in (" + paramString1 + ")";
/*     */ 
/*     */     
/* 220 */     if (paramString5.length() > 0) str = str + paramString5; 
/* 221 */     if (paramString4.length() > 0) str = str + " and userid not in (" + paramString4 + ")"; 
/* 222 */     str = str + " order by hr.dsporder";
/*     */     
/* 224 */     recordSet.executeSql(str);
/* 225 */     while (recordSet.next()) {
/* 226 */       String str1 = recordSet.getString("id");
/*     */       
/* 228 */       if (paramString4.length() > 0) paramString4 = paramString4 + ","; 
/* 229 */       paramString4 = paramString4 + str1;
/* 230 */       String str2 = recordSet.getString("lastname");
/* 231 */       String str3 = recordSet.getString("pinyinlastname");
/* 232 */       String str4 = recordSet.getString("jobtitle");
/* 233 */       str4 = getJobTitlesname(str1, paramUser);
/* 234 */       str2 = "<span id='pinyinlastname' style='display:none'>" + str3 + "</span><span id='lastname'>" + str2 + "</span><span id='jobtitlename' style='color:#929390;margin-left:15px;margin-right:2px;'>" + str4 + "</span>";
/*     */       
/* 236 */       JSONObject jSONObject = new JSONObject();
/* 237 */       jSONObject.put("messagerurl", resourceComInfo.getMessagerUrls(str1));
/* 238 */       jSONObject.put("id", str1);
/* 239 */       jSONObject.put("nodeid", "resource_" + str1);
/* 240 */       jSONObject.put("type", "resource");
/* 241 */       jSONObject.put("lastname", recordSet.getString("lastname"));
/* 242 */       jSONObject.put("pinyinlastname", recordSet.getString("pinyinlastname"));
/* 243 */       jSONObject.put("jobtitlename", getJobTitlesname(str1, paramUser));
/* 244 */       jSONArray.add(jSONObject);
/*     */     } 
/* 246 */     return jSONArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getComDeptResourceNum(String paramString1, String paramString2, String paramString3, User paramUser, String paramString4) throws Exception {
/* 259 */     int i = 0;
/* 260 */     String[] arrayOfString = Util.TokenizerString2(paramString1, "_");
/* 261 */     String str = "";
/* 262 */     if (Integer.parseInt(arrayOfString[1]) < 0) {
/*     */       
/* 264 */       str = getComDeptResourceVirtualIds(paramString1, paramString2, paramString3, paramUser, paramString4);
/*     */     } else {
/* 266 */       str = getComDeptResourceIds(paramString1, paramString2, paramString3, paramUser, paramString4);
/*     */     } 
/* 268 */     if (str.length() > 0) {
/* 269 */       i = (Util.TokenizerString2(str, ",")).length;
/*     */     }
/* 271 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getComDeptResourceIds(String paramString1, String paramString2, String paramString3, User paramUser, String paramString4) throws Exception {
/* 284 */     RecordSet recordSet = new RecordSet();
/* 285 */     ArrayList arrayList = new ArrayList();
/* 286 */     String[] arrayOfString = Util.TokenizerString2(paramString1, "_");
/* 287 */     String str1 = "";
/*     */     
/* 289 */     String str2 = arrayOfString[0];
/* 290 */     String str3 = arrayOfString[1];
/* 291 */     String str4 = " select hr.id as id, lastname, pinyinlastname, departmentid, subcompanyid1, jobtitle  from hrmresource hr where 1= 1 ";
/*     */     
/* 293 */     if (paramString4.length() > 0 && 
/* 294 */       !paramString4.trim().startsWith("and")) paramString4 = "and" + paramString4;
/*     */ 
/*     */     
/* 297 */     paramString4 = paramString4 + " and hr.status in (0,1,2,3)";
/* 298 */     if (!str2.equals("com"))
/*     */     {
/* 300 */       if (str2.equals("subcom")) {
/*     */         
/* 302 */         if (paramString2.equals("1")) str3 = SubCompanyComInfo.getAllChildSubcompanyId(str3, str3); 
/* 303 */         if (str3.length() > 0) {
/* 304 */           paramString4 = paramString4 + " and " + Tools.getOracleSQLIn(str3, "subcompanyid1");
/*     */         }
/* 306 */       } else if (str2.equals("dept")) {
/*     */         
/* 308 */         if (paramString2.equals("1")) str3 = DepartmentComInfo.getAllChildDepartId(str3, str3); 
/* 309 */         if (str3.length() > 0) {
/* 310 */           paramString4 = paramString4 + " and " + Tools.getOracleSQLIn(str3, "departmentid");
/*     */         }
/*     */       } 
/*     */     }
/*     */     
/* 315 */     String str5 = "";
/* 316 */     if (!paramString3.equals("1")) {
/* 317 */       if (this.mode == null || !this.mode.equals("ldap")) {
/*     */         
/* 319 */         str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */       } else {
/*     */         
/* 322 */         str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */       } 
/*     */     }
/*     */     
/* 326 */     paramString4 = paramString4 + (paramString3.equals("1") ? "" : str5);
/*     */ 
/*     */ 
/*     */     
/* 330 */     if (this.adci.isUseAppDetach()) {
/* 331 */       String str6 = this.adci.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/* 332 */       String str7 = (str6 != null && !"".equals(str6)) ? (" and " + str6) : "";
/* 333 */       paramString4 = paramString4 + str7;
/*     */     } 
/* 335 */     if (paramString4.length() > 0) str4 = str4 + paramString4; 
/* 336 */     str4 = str4 + " order by dsporder,lastname ";
/* 337 */     recordSet.executeSql(str4);
/* 338 */     while (recordSet.next()) {
/* 339 */       str3 = recordSet.getString("id");
/*     */ 
/*     */       
/* 342 */       if (str1.length() > 0) str1 = str1 + ","; 
/* 343 */       str1 = str1 + str3;
/*     */     } 
/*     */     
/* 346 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray getComDeptResource(String paramString1, String paramString2, String paramString3, String paramString4, User paramUser, String paramString5) throws Exception {
/* 360 */     RecordSet recordSet = new RecordSet();
/* 361 */     JSONArray jSONArray = new JSONArray();
/* 362 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 363 */     String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/*     */     
/* 365 */     for (String str1 : arrayOfString) {
/* 366 */       String str2 = Util.TokenizerString2(str1, "_")[0];
/* 367 */       String str3 = Util.TokenizerString2(str1, "_")[1];
/* 368 */       String str4 = " select hr.id as id, lastname, pinyinlastname, departmentid, subcompanyid1, jobtitle, loginid, account  from hrmresource hr where 1=1 ";
/*     */       
/* 370 */       if (paramString5.length() > 0) paramString5 = " and " + paramString5; 
/* 371 */       paramString5 = paramString5 + " and hr.status in (0,1,2,3)";
/* 372 */       if (!str2.equals("com"))
/*     */       {
/* 374 */         if (str2.equals("subcom")) {
/*     */           
/* 376 */           if (paramString2.equals("1")) str3 = SubCompanyComInfo.getAllChildSubcompanyId(str3, str3);
/*     */           
/* 378 */           if (str3 != null) str3.replaceFirst("^[ ]*[,]", ""); 
/* 379 */           paramString5 = paramString5 + " and " + Tools.getOracleSQLIn(str3, "subcompanyid1");
/* 380 */         } else if (str2.equals("dept")) {
/*     */           
/* 382 */           if (paramString2.equals("1")) str3 = DepartmentComInfo.getAllChildDepartId(str3, str3);
/*     */           
/* 384 */           if (str3 != null) str3.replaceFirst("^[ ]*[,]", ""); 
/* 385 */           paramString5 = paramString5 + " and " + Tools.getOracleSQLIn(str3, "departmentid");
/* 386 */         } else if (str2.equals("manager")) {
/* 387 */           if (paramString2.equals("1")) {
/*     */             
/* 389 */             paramString5 = paramString5 + " and hr.managerstr like '%," + paramUser.getUID() + ",%'";
/*     */           } else {
/* 391 */             paramString5 = paramString5 + " and managerid = " + paramUser.getUID();
/*     */           } 
/*     */         } 
/*     */       }
/* 395 */       paramString4 = getExcludeSqlWhere(paramString4, paramString2, paramString3, paramUser, paramString5);
/* 396 */       if (paramString4.length() > 0)
/*     */       {
/* 398 */         paramString5 = paramString5 + " and " + Tools.getOracleSQLNotIn(paramString4, "hr.id");
/*     */       }
/*     */ 
/*     */       
/* 402 */       String str5 = "";
/* 403 */       if (!paramString3.equals("1")) {
/* 404 */         if (this.mode == null || !this.mode.equals("ldap")) {
/* 405 */           str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */         } else {
/*     */           
/* 408 */           str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */         } 
/*     */       }
/*     */       
/* 412 */       paramString5 = paramString5 + (paramString3.equals("1") ? "" : str5);
/*     */ 
/*     */ 
/*     */       
/* 416 */       if (this.adci.isUseAppDetach()) {
/* 417 */         String str6 = this.adci.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/* 418 */         String str7 = (str6 != null && !"".equals(str6)) ? (" and " + str6) : "";
/* 419 */         paramString5 = paramString5 + str7;
/*     */       } 
/* 421 */       if (paramString5.length() > 0) str4 = str4 + paramString5;
/*     */       
/* 423 */       str4 = str4 + " order by dsporder,lastname ";
/*     */       
/* 425 */       recordSet.executeSql(str4);
/* 426 */       while (recordSet.next()) {
/* 427 */         str3 = recordSet.getString("id");
/*     */         
/* 429 */         if (paramString4.length() > 0) paramString4 = paramString4 + ","; 
/* 430 */         paramString4 = paramString4 + str3;
/*     */         
/* 432 */         JSONObject jSONObject = new JSONObject();
/* 433 */         jSONObject.put("messagerurl", resourceComInfo.getMessagerUrls(str3));
/* 434 */         jSONObject.put("id", str3);
/* 435 */         jSONObject.put("nodeid", "resource_" + str3);
/* 436 */         jSONObject.put("type", "resource");
/* 437 */         jSONObject.put("lastname", recordSet.getString("lastname"));
/* 438 */         jSONObject.put("pinyinlastname", recordSet.getString("pinyinlastname"));
/* 439 */         jSONObject.put("jobtitlename", getJobTitlesname(str3));
/* 440 */         jSONArray.add(jSONObject);
/*     */       } 
/*     */     } 
/* 443 */     return jSONArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getComDeptResourceVirtualNum(String paramString1, String paramString2, String paramString3, User paramUser, String paramString4) throws Exception {
/* 456 */     int i = 0;
/* 457 */     String str = getComDeptResourceIds(paramString1, paramString2, paramString3, paramUser, paramString4);
/* 458 */     if (str.length() > 0) {
/* 459 */       i = (Util.TokenizerString2(str, ",")).length;
/*     */     }
/* 461 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray getComDeptResourceVirtual(String paramString1, String paramString2, String paramString3, String paramString4, User paramUser, String paramString5) throws Exception {
/* 475 */     RecordSet recordSet = new RecordSet();
/* 476 */     JSONArray jSONArray = new JSONArray();
/* 477 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 478 */     String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/*     */     
/* 480 */     for (String str1 : arrayOfString) {
/* 481 */       String str2 = Util.TokenizerString2(str1, "_")[0];
/* 482 */       String str3 = Util.TokenizerString2(str1, "_")[1];
/* 483 */       String str4 = " select hr.id as id, lastname, pinyinlastname, departmentid, subcompanyid1, jobtitle, dsporder  from HrmResourceVirtualView hr where 1= 1 ";
/*     */       
/* 485 */       if (paramString5.length() > 0) paramString5 = " and " + paramString5; 
/* 486 */       paramString5 = paramString5 + " and hr.status in (0,1,2,3)";
/* 487 */       if (str2.equals("com")) {
/*     */         
/* 489 */         paramString5 = paramString5 + " and hr.virtualtype = " + str3;
/* 490 */       } else if (str2.equals("subcom")) {
/*     */         
/* 492 */         if (paramString2.equals("1")) str3 = SubCompanyVirtualComInfo.getAllChildSubcompanyId(str3, str3); 
/* 493 */         paramString5 = paramString5 + " and hr.subcompanyid1 in (" + str3 + ") ";
/* 494 */       } else if (str2.equals("dept")) {
/*     */         
/* 496 */         if (paramString2.equals("1")) str3 = DepartmentVirtualComInfo.getAllChildDepartId(str3, str3); 
/* 497 */         paramString5 = paramString5 + " and hr.departmentid in(" + str3 + ") ";
/* 498 */       } else if (str2.equals("manager")) {
/*     */         
/* 500 */         if (paramString2.equals("1")) {
/* 501 */           paramString5 = paramString5 + " and hr.managerstr like '%," + paramUser.getUID() + ",%' AND hr.virtualtype =" + str3;
/*     */         } else {
/*     */           
/* 504 */           paramString5 = paramString5 + " and hr.managerid= " + paramUser.getUID() + " AND hr.virtualtype =" + str3;
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 509 */       paramString4 = getExcludeSqlWhere(paramString4, paramString2, paramString3, paramUser, paramString5);
/* 510 */       if (paramString4.length() > 0)
/*     */       {
/* 512 */         paramString5 = paramString5 + " and " + Tools.getOracleSQLNotIn(paramString4, "hr.id");
/*     */       }
/*     */ 
/*     */       
/* 516 */       String str5 = "";
/* 517 */       if (!paramString3.equals("1")) {
/* 518 */         if (this.mode == null || !this.mode.equals("ldap")) {
/* 519 */           str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */         } else {
/*     */           
/* 522 */           str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */         } 
/*     */       }
/*     */       
/* 526 */       paramString5 = paramString5 + (paramString3.equals("1") ? "" : str5);
/*     */ 
/*     */ 
/*     */       
/* 530 */       if (this.adci.isUseAppDetach()) {
/* 531 */         String str6 = this.adci.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/* 532 */         String str7 = (str6 != null && !"".equals(str6)) ? (" and " + str6) : "";
/* 533 */         paramString5 = paramString5 + str7;
/*     */       } 
/* 535 */       if (paramString5.length() > 0) str4 = str4 + paramString5;
/*     */       
/* 537 */       str4 = str4 + " order by dsporder,lastname ";
/*     */       
/* 539 */       recordSet.executeSql(str4);
/* 540 */       while (recordSet.next()) {
/* 541 */         str3 = recordSet.getString("id");
/*     */         
/* 543 */         if (paramString4.length() > 0) paramString4 = paramString4 + ","; 
/* 544 */         paramString4 = paramString4 + str3;
/*     */         
/* 546 */         JSONObject jSONObject = new JSONObject();
/* 547 */         jSONObject.put("messagerurl", resourceComInfo.getMessagerUrls(str3));
/* 548 */         jSONObject.put("id", str3);
/* 549 */         jSONObject.put("nodeid", "resource_" + str3);
/* 550 */         jSONObject.put("type", "resource");
/* 551 */         jSONObject.put("lastname", recordSet.getString("lastname"));
/* 552 */         jSONObject.put("pinyinlastname", recordSet.getString("pinyinlastname"));
/* 553 */         jSONObject.put("jobtitlename", getJobTitlesname(str3, paramUser));
/* 554 */         jSONArray.add(jSONObject);
/*     */       } 
/*     */     } 
/* 557 */     return jSONArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getComDeptResourceVirtualIds(String paramString1, String paramString2, String paramString3, User paramUser, String paramString4) throws Exception {
/* 571 */     RecordSet recordSet = new RecordSet();
/* 572 */     String str = "";
/* 573 */     String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/*     */     
/* 575 */     for (String str1 : arrayOfString) {
/* 576 */       String str2 = Util.TokenizerString2(str1, "_")[0];
/* 577 */       String str3 = Util.TokenizerString2(str1, "_")[1];
/* 578 */       String str4 = " select hr.id as id, lastname, pinyinlastname, departmentid, subcompanyid1, jobtitle, dsporder  from hrmresource hr where 1= 1 ";
/*     */       
/* 580 */       if (paramString4.length() > 0) {
/* 581 */         if (!paramString4.trim().startsWith("and")) paramString4 = paramString4 + "and"; 
/* 582 */         paramString4 = paramString4 + paramString4;
/*     */       } 
/* 584 */       paramString4 = paramString4 + " and hr.status in (0,1,2,3)";
/* 585 */       if (str2.equals("com")) {
/*     */         
/* 587 */         str4 = str4 + "SELECT t3.resourceid FROM  HrmSubCompanyVirtual t1 , HrmDepartmentVirtual t2, HrmResourcevirtual t3 WHERE t1.id=t2.subcompanyid1 AND t2.id=t3.departmentid AND t1.companyid = " + str3;
/*     */       
/*     */       }
/* 590 */       else if (str2.equals("subcom")) {
/*     */         
/* 592 */         if (paramString2.equals("1")) str3 = SubCompanyVirtualComInfo.getAllChildSubcompanyId(str3, str3); 
/* 593 */         paramString4 = paramString4 + " and hr.id in (SELECT t3.resourceid FROM  HrmSubCompanyVirtual t1 , HrmDepartmentVirtual t2, HrmResourcevirtual t3  WHERE t1.id=t2.subcompanyid1 AND t2.id=t3.departmentid   AND t2.subcompanyid1 in (" + str3 + ")) ";
/*     */       
/*     */       }
/* 596 */       else if (str2.equals("dept")) {
/*     */         
/* 598 */         if (paramString2.equals("1")) str3 = DepartmentVirtualComInfo.getAllChildDepartId(str3, str3); 
/* 599 */         paramString4 = paramString4 + " and hr.id in (SELECT t3.resourceid FROM  HrmSubCompanyVirtual t1 , HrmDepartmentVirtual t2, HrmResourcevirtual t3  WHERE t1.id=t2.subcompanyid1 AND t2.id=t3.departmentid  AND t2.id in (" + str3 + ")) ";
/*     */       
/*     */       }
/* 602 */       else if (str2.equals("manager")) {
/*     */         
/* 604 */         if (paramString2.equals("1")) {
/*     */           
/* 606 */           paramString4 = paramString4 + " and hr.id in (SELECT t3.resourceid FROM  HrmSubCompanyVirtual t1 , HrmDepartmentVirtual t2, HrmResourcevirtual t3  WHERE t1.id=t2.subcompanyid1 AND t2.id=t3.departmentid AND t3.managerstr like '%," + paramUser.getUID() + ",%' AND t1.companyid =" + str3 + " )";
/*     */         }
/*     */         else {
/*     */           
/* 610 */           paramString4 = paramString4 + " and hr.id in (SELECT t3.resourceid FROM  HrmSubCompanyVirtual t1 , HrmDepartmentVirtual t2, HrmResourcevirtual t3  WHERE t1.id=t2.subcompanyid1 AND t2.id=t3.departmentid AND t3.managerid= " + paramUser.getUID() + " AND t1.companyid =" + str3 + " )";
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 616 */       String str5 = "";
/* 617 */       if (!paramString3.equals("1")) {
/* 618 */         if (this.mode == null || !this.mode.equals("ldap")) {
/* 619 */           str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */         } else {
/*     */           
/* 622 */           str5 = " and loginid is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and loginid<>'' ");
/*     */         } 
/*     */       }
/*     */       
/* 626 */       paramString4 = paramString4 + (paramString3.equals("1") ? "" : str5);
/*     */ 
/*     */ 
/*     */       
/* 630 */       if (this.adci.isUseAppDetach()) {
/* 631 */         String str6 = this.adci.getScopeSqlByHrmResourceSearch(paramUser.getUID() + "", true, "resource_hr");
/* 632 */         String str7 = (str6 != null && !"".equals(str6)) ? (" and " + str6) : "";
/* 633 */         paramString4 = paramString4 + str7;
/*     */       } 
/* 635 */       if (paramString4.length() > 0) str4 = str4 + paramString4;
/*     */       
/* 637 */       str4 = str4 + " order by dsporder,lastname ";
/*     */       
/* 639 */       recordSet.executeSql(str4);
/* 640 */       while (recordSet.next()) {
/* 641 */         str3 = recordSet.getString("id");
/*     */         
/* 643 */         if (str.length() > 0) str = str + ","; 
/* 644 */         str = str + str3;
/*     */       } 
/*     */     } 
/* 647 */     return str;
/*     */   }
/*     */   
/*     */   public static String getJobTitlesname(String paramString) throws Exception {
/* 651 */     return getJobTitlesname(paramString, null);
/*     */   }
/*     */   
/*     */   public static String getJobTitlesname(String paramString, User paramUser) throws Exception {
/* 655 */     JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 656 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 657 */     String str1 = resourceComInfo.getJobTitle(paramString);
/* 658 */     String str2 = resourceComInfo.getLoginID(paramString);
/*     */     
/* 660 */     String str3 = jobTitlesComInfo.getJobTitlesname(str1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 671 */     if (Util.null2String(str2).length() == 0) {
/* 672 */       if (paramUser == null) {
/* 673 */         str3 = "(" + SystemEnv.getHtmlLabelName(10003669, ThreadVarLanguage.getLang()) + ")";
/*     */       } else {
/* 675 */         str3 = "(" + SystemEnv.getHtmlLabelName(81672, Util.getIntValue(paramUser.getLanguage())) + ")";
/*     */       } 
/*     */     }
/* 678 */     return str3;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/MutilResourceBrowser.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */