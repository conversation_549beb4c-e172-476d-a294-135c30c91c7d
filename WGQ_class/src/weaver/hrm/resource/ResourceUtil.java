/*    */ package weaver.hrm.resource;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.company.DepartmentComInfo;
/*    */ 
/*    */ public class ResourceUtil
/*    */   extends BaseBean
/*    */ {
/* 10 */   private ResourceComInfo ResourceComInfo = null;
/* 11 */   private DepartmentComInfo DepartmentComInfo = null;
/*    */   public ResourceUtil() {
/*    */     try {
/* 14 */       this.ResourceComInfo = new ResourceComInfo();
/* 15 */       this.DepartmentComInfo = new DepartmentComInfo();
/* 16 */     } catch (Exception exception) {
/*    */       
/* 18 */       writeLog(exception);
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getHrmShowNameHref(String paramString) {
/* 29 */     String str = "";
/* 30 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 31 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 32 */       String str1 = Util.null2String(arrayOfString[b]);
/* 33 */       if (!str1.equals("")) {
/* 34 */         str = str + "<a href='javaScript:openhrm(" + str1 + ");' onclick='pointerXY(event);'>" + this.ResourceComInfo.getResourcename(str1) + "</a>&nbsp";
/*    */       }
/*    */     } 
/* 37 */     return str;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getHrmShowName(String paramString) {
/* 48 */     String str = "";
/* 49 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 50 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 51 */       String str1 = Util.null2String(arrayOfString[b]);
/* 52 */       if (!str1.equals("")) {
/* 53 */         str = str + "," + this.ResourceComInfo.getResourcename(str1);
/*    */       }
/*    */     } 
/* 56 */     str = str.replaceFirst(",", "");
/* 57 */     return str;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getHrmShowDepartmentID(String paramString) {
/* 67 */     String str = "";
/* 68 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 69 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 70 */       String str1 = Util.null2String(arrayOfString[b]);
/* 71 */       if (!str1.equals("")) {
/* 72 */         str = str + "," + this.ResourceComInfo.getDepartmentID(str1);
/*    */       }
/*    */     } 
/* 75 */     str = str.replaceFirst(",", "");
/* 76 */     return str;
/*    */   }
/*    */   
/*    */   public String getHrmDepartmentName(String paramString) {
/* 80 */     String str = "";
/* 81 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 82 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 83 */       String str1 = Util.null2String(arrayOfString[b]);
/* 84 */       if (!str1.equals("")) {
/* 85 */         str = str + "," + this.DepartmentComInfo.getDepartmentname(str1);
/*    */       }
/*    */     } 
/* 88 */     str = str.replaceFirst(",", "");
/* 89 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/ResourceUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */