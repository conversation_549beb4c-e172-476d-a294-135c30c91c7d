/*     */ package weaver.hrm.resource.browser;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TreeHelper
/*     */   extends SubCompanyComInfo
/*     */ {
/*  16 */   public int[] subcomids = null;
/*  17 */   public int[] subcomids1 = null;
/*  18 */   public int[] subcomids2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getSubCompanyTreeListByEditRight(int paramInt, String paramString) throws Exception {
/*  28 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/*  29 */     this.subcomids = checkSubCompanyRight.getSubComPathByUserRightId(paramInt, paramString, 1);
/*  30 */     this.subcomids1 = checkSubCompanyRight.getSubComByUserRightId(paramInt, paramString);
/*  31 */     this.subcomids2 = checkSubCompanyRight.getSubComByUserEditRightId(paramInt, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getDepartTreeList(TreeNode paramTreeNode1, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, TreeNode paramTreeNode2, ArrayList paramArrayList) throws Exception {
/*  49 */     int i = paramInt1;
/*  50 */     i++;
/*     */ 
/*     */     
/*  53 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  54 */     departmentComInfo.setTofirstRow();
/*     */ 
/*     */     
/*  57 */     while ((departmentComInfo.next() & ((i < paramInt2) ? 1 : 0)) != 0) {
/*  58 */       if (paramString2.equals(departmentComInfo.getDepartmentid()))
/*     */         continue; 
/*  60 */       String str1 = departmentComInfo.getDepartmentsupdepid();
/*  61 */       String str2 = departmentComInfo.getDeparmentcanceled();
/*  62 */       if (paramString2.equals("0") && str1.equals(""))
/*  63 */         str1 = "0"; 
/*  64 */       if (!departmentComInfo.getSubcompanyid1().equals(paramString1) || (!str1.equals(paramString2) && (departmentComInfo.getSubcompanyid1(str1).equals(paramString1) || !paramString2.equals("0"))))
/*     */         continue; 
/*  66 */       String str3 = departmentComInfo.getDepartmentid();
/*  67 */       String str4 = departmentComInfo.getDepartmentname();
/*     */ 
/*     */       
/*  70 */       TreeNode treeNode = new TreeNode();
/*  71 */       treeNode.setTitle(str4);
/*  72 */       treeNode.setNodeId("dept_" + paramString1 + "_" + str3);
/*     */       
/*  74 */       treeNode.setIcon("/images/treeimages/subCopany_Colse_wev8.gif");
/*  75 */       if (i == paramInt2 - 1)
/*     */       {
/*     */         
/*  78 */         if (hasChild("dept", str3))
/*     */         {
/*  80 */           if (paramString3.equals("resourceHire")) {
/*  81 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/hire/ResourceMultiXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  82 */           } else if (paramString3.equals("resourceExtend")) {
/*  83 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/hire/ResourceMultiXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  84 */           } else if (paramString3.equals("resourceFire")) {
/*  85 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/fire/ResourceMultiXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  86 */           } else if (paramString3.equals("resourceRehire")) {
/*  87 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/rehire/ResourceMultiXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  88 */           } else if (paramString3.equals("resourceRetire")) {
/*  89 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/retire/ResourceMultiXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  90 */           } else if (paramString3.equals("resourceTry")) {
/*  91 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/try/ResourceMultiXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  92 */           } else if (paramString3.equals("resourceRedeploy")) {
/*  93 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/redeploy/ResourceSingleXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  94 */           } else if (paramString3.equals("resourceDismissMulti")) {
/*  95 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/dismiss/ResourceMultiXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*  96 */           } else if (paramString3.equals("resourceDismissSingle")) {
/*  97 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/dismiss/ResourceSingleXML.jsp?type=dept&id=" + str3 + "&level=" + i + "&subid=" + paramString1 + "&nodeid=" + treeNode.getNodeId());
/*     */           } 
/*     */         }
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 104 */       if (paramString3.equals("resourceHire") || paramString3.equals("resourceExtend") || paramString3.equals("resourceFire") || paramString3.equals("resourceRehire") || paramString3.equals("resourceRetire") || paramString3.equals("resourceTry") || paramString3.equals("resourceRedeploy") || paramString3.equals("resourceDismissMulti") || paramString3.equals("resourceDismissSingle")) {
/* 105 */         treeNode.setHref("javascript:setDepartment('" + treeNode.getNodeId() + "')");
/* 106 */         treeNode.setTarget("_self");
/*     */       } 
/*     */ 
/*     */       
/* 110 */       if (paramTreeNode2 != null && (paramTreeNode2.getTreeNode()).length > 0 && paramTreeNode2.equals(treeNode))
/* 111 */         treeNode = paramTreeNode2; 
/* 112 */       if ("0".equals(str2) || "".equals(str2)) {
/* 113 */         paramTreeNode1.addTreeNode(treeNode);
/*     */       }
/* 115 */       if (paramArrayList != null) {
/* 116 */         paramArrayList.remove(treeNode);
/*     */       }
/*     */       
/* 119 */       getDepartTreeList(treeNode, paramString1, str3, i, paramInt2, paramString3, paramTreeNode2, paramArrayList);
/*     */     } 
/*     */     
/* 122 */     return paramTreeNode1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getSubCompanyTreeListByEditRight(TreeNode paramTreeNode1, String paramString1, int paramInt1, int paramInt2, boolean paramBoolean, String paramString2, TreeNode paramTreeNode2, ArrayList paramArrayList) throws Exception {
/* 141 */     int i = paramInt1;
/*     */ 
/*     */     
/* 144 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 145 */     subCompanyComInfo.setTofirstRow();
/*     */     
/* 147 */     i++;
/*     */     
/* 149 */     boolean bool = false;
/* 150 */     for (byte b = 0; b < this.subcomids1.length; b++) {
/* 151 */       if (paramString1.equals(String.valueOf(this.subcomids1[b])))
/* 152 */         bool = true; 
/*     */     } 
/* 154 */     if (paramBoolean && bool) {
/* 155 */       getDepartTreeList(paramTreeNode1, paramString1, "0", i - 1, paramInt2, paramString2, paramTreeNode2, paramArrayList);
/*     */     }
/*     */ 
/*     */     
/* 159 */     while (subCompanyComInfo.next() && i < paramInt2) {
/* 160 */       String str1 = subCompanyComInfo.getSupsubcomid();
/* 161 */       if (str1.equals(""))
/* 162 */         str1 = "0"; 
/* 163 */       if (!str1.equals(paramString1))
/*     */         continue; 
/* 165 */       String str2 = subCompanyComInfo.getSubCompanyid();
/* 166 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*     */ 
/*     */       
/* 169 */       boolean bool1 = false;
/* 170 */       for (byte b1 = 0; b1 < this.subcomids.length; b1++) {
/* 171 */         if (str2.equals(String.valueOf(this.subcomids[b1])))
/* 172 */           bool1 = true; 
/*     */       } 
/* 174 */       if (!bool1)
/*     */         continue; 
/* 176 */       TreeNode treeNode = new TreeNode();
/* 177 */       treeNode.setTitle(str3);
/* 178 */       treeNode.setNodeId("com_" + str2);
/* 179 */       treeNode.setIcon("/images/treeimages/Home_wev8.gif");
/*     */       
/* 181 */       if (i == paramInt2 - 1)
/*     */       {
/*     */         
/* 184 */         if (hasChild("com", str2)) {
/* 185 */           if (paramString2.equals("resourceHire")) {
/* 186 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/hire/ResourceMultiXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 187 */           } else if (paramString2.equals("resourceExtend")) {
/* 188 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/extend/ResourceMultiXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 189 */           } else if (paramString2.equals("resourceFire")) {
/* 190 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/fire/ResourceMultiXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 191 */           } else if (paramString2.equals("resourceRehire")) {
/* 192 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/rehire/ResourceMultiXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 193 */           } else if (paramString2.equals("resourceRetire")) {
/* 194 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/retire/ResourceMultiXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 195 */           } else if (paramString2.equals("resourceTry")) {
/* 196 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/try/ResourceMultiXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 197 */           } else if (paramString2.equals("resourceRedeploy")) {
/* 198 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/redeploy/ResourceSingleXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 199 */           } else if (paramString2.equals("resourceDismissMulti")) {
/* 200 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/dismiss/ResourceMultiXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/* 201 */           } else if (paramString2.equals("resourceDismissSingle")) {
/* 202 */             treeNode.setNodeXmlSrc("/hrm/resource/browser/dismiss/ResourceSingleXML.jsp?type=com&id=" + str2 + "&level=" + i + "&nodeid=" + treeNode.getNodeId());
/*     */           } 
/*     */         }
/*     */       }
/* 206 */       boolean bool2 = false;
/* 207 */       for (byte b2 = 0; b2 < this.subcomids2.length; b2++) {
/* 208 */         if (str2.equals(String.valueOf(this.subcomids2[b2]))) {
/* 209 */           bool2 = true;
/*     */         }
/*     */       } 
/*     */       
/* 213 */       if (paramString2.equals("resourceHire") || paramString2.equals("resourceExtend") || paramString2.equals("resourceFire") || paramString2.equals("resourceRehire") || paramString2.equals("resourceRetire") || paramString2.equals("resourceTry") || paramString2.equals("resourceRedeploy") || paramString2.equals("resourceDismissMulti") || paramString2.equals("resourceDismissSingle")) {
/* 214 */         treeNode.setHref("javascript:setSubcompany('" + treeNode.getNodeId() + "')");
/* 215 */         treeNode.setTarget("_self");
/*     */       } 
/*     */       
/* 218 */       if (paramTreeNode2 != null && (paramTreeNode2.getTreeNode()).length > 0 && paramTreeNode2.equals(treeNode))
/* 219 */         treeNode = paramTreeNode2; 
/* 220 */       paramTreeNode1.addTreeNode(treeNode);
/* 221 */       if (paramArrayList != null)
/* 222 */         paramArrayList.remove(treeNode); 
/* 223 */       getSubCompanyTreeListByEditRight(treeNode, str2, i, paramInt2, paramBoolean, paramString2, paramTreeNode2, paramArrayList);
/*     */     } 
/*     */ 
/*     */     
/* 227 */     return paramTreeNode1;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean hasChild(String paramString1, String paramString2) throws Exception {
/* 232 */     if (paramString1.equals("com")) {
/* 233 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 234 */       subCompanyComInfo.setTofirstRow();
/* 235 */       while (subCompanyComInfo.next()) {
/* 236 */         if (subCompanyComInfo.getSupsubcomid().equals(paramString2)) return true; 
/*     */       } 
/* 238 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 239 */       departmentComInfo.setTofirstRow();
/* 240 */       while (departmentComInfo.next()) {
/* 241 */         String str = departmentComInfo.getDepartmentsupdepid();
/*     */ 
/*     */         
/* 244 */         if (departmentComInfo.getSubcompanyid1().equals(paramString2)) return true;
/*     */       
/*     */       }
/*     */     
/* 248 */     } else if (paramString1.equals("dept")) {
/* 249 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 250 */       departmentComInfo.setTofirstRow();
/* 251 */       while (departmentComInfo.next())
/*     */       {
/* 253 */         if (departmentComInfo.getSubcompanyid1().equals(departmentComInfo.getSubcompanyid1(paramString2)) && departmentComInfo.getDepartmentsupdepid().equals(paramString2)) {
/* 254 */           return true;
/*     */         }
/*     */       }
/*     */     
/* 258 */     } else if (paramString1.equals("com1")) {
/* 259 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 260 */       subCompanyComInfo.setTofirstRow();
/* 261 */       while (subCompanyComInfo.next()) {
/* 262 */         String str = subCompanyComInfo.getSubCompanyid();
/* 263 */         if (subCompanyComInfo.getSupsubcomid().equals(paramString2)) return true; 
/*     */       } 
/*     */     } else {
/* 266 */       return false;
/* 267 */     }  return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/browser/TreeHelper.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */