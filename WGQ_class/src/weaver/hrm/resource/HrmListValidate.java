/*    */ package weaver.hrm.resource;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ public class HrmListValidate
/*    */   extends BaseBean
/*    */ {
/* 12 */   private ArrayList ids = null;
/* 13 */   private ArrayList names = null;
/* 14 */   private ArrayList validates = null;
/* 15 */   private RecordSet rt = null;
/*    */ 
/*    */ 
/*    */   
/*    */   public HrmListValidate() throws Exception {
/* 20 */     getHrmListValidate();
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   private void getHrmListValidate() throws Exception {
/* 26 */     setHrmListValidate();
/*    */   }
/*    */   
/*    */   private void setHrmListValidate() throws Exception {
/* 30 */     if (this.ids != null) {
/* 31 */       this.ids.clear();
/*    */     } else {
/* 33 */       this.ids = new ArrayList();
/*    */     } 
/* 35 */     if (this.names != null) {
/* 36 */       this.names.clear();
/*    */     } else {
/* 38 */       this.names = new ArrayList();
/*    */     } 
/* 40 */     if (this.validates != null) {
/* 41 */       this.validates.clear();
/*    */     } else {
/* 43 */       this.validates = new ArrayList();
/*    */     } 
/* 45 */     RecordSet recordSet = new RecordSet();
/*    */     try {
/* 47 */       recordSet.executeProc("HrmList_SelectAll", "");
/* 48 */       while (recordSet.next()) {
/* 49 */         this.ids.add(Util.null2String(recordSet.getString("id")));
/* 50 */         this.names.add(Util.null2String(recordSet.getString("name")));
/* 51 */         this.validates.add(Util.null2String(recordSet.getString("validate_n")));
/*    */       }
/*    */     
/*    */     }
/* 55 */     catch (Exception exception) {
/* 56 */       writeLog(exception);
/* 57 */       throw exception;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isValidate(int paramInt) {
/* 70 */     int i = this.ids.indexOf(paramInt + "");
/* 71 */     if (i != -1 && ((String)this.validates.get(i)).trim().equals("1")) {
/* 72 */       return true;
/*    */     }
/* 74 */     return false;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getListName(String paramString) {
/* 84 */     int i = this.ids.indexOf(paramString);
/* 85 */     if (i != -1) {
/* 86 */       return ((String)this.names.get(i)).trim();
/*    */     }
/* 88 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/HrmListValidate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */