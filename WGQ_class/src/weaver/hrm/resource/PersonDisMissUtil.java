/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.sql.Timestamp;
/*     */ import java.util.Date;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.interfaces.hrm.HrmServiceManager;
/*     */ import weaver.rtx.OrganisationComRunnable;
/*     */ import weaver.system.SysRemindWorkflow;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PersonDisMissUtil
/*     */ {
/*  22 */   private Log log = LogFactory.getLog(PersonDisMissUtil.class.getName());
/*     */   public void synDismiss(String paramString1, String paramString2) {
/*  24 */     this.log.error("****************禁用账号做离职操作开始***************");
/*  25 */     RecordSet recordSet = new RecordSet();
/*  26 */     String str1 = "5";
/*  27 */     String str2 = "";
/*  28 */     char c = Util.getSeparator();
/*  29 */     String str3 = "AD禁用，做离职操作";
/*  30 */     String str4 = "1";
/*  31 */     String str5 = "";
/*  32 */     String str6 = "";
/*  33 */     Date date = new Date();
/*  34 */     long l = date.getTime();
/*  35 */     Timestamp timestamp = new Timestamp(l);
/*  36 */     String str7 = timestamp.toString().substring(0, 4) + "-" + timestamp.toString().substring(5, 7) + "-" + timestamp.toString().substring(8, 10);
/*  37 */     String str8 = "";
/*  38 */     int i = 7;
/*  39 */     String str9 = "select * from hrmresource where loginid='" + paramString1 + "'";
/*  40 */     recordSet.executeSql(str9);
/*  41 */     this.log.error("查询OA人员数据sql====>" + str9);
/*  42 */     if (recordSet.next()) {
/*  43 */       str8 = recordSet.getString("id");
/*  44 */       String str10 = recordSet.getString("lastname");
/*  45 */       String str11 = recordSet.getString("jobtitle");
/*  46 */       i = Integer.parseInt(recordSet.getString("systemlanguage"));
/*  47 */       str2 = "" + str8 + c + paramString2 + c + str3 + c + "" + c + str4 + c + str11 + c + str1 + c + str4;
/*  48 */       this.log.error("参数para====>" + str2);
/*  49 */       recordSet.executeProc("HrmResource_Dismiss", str2);
/*  50 */       String str12 = SystemEnv.getHtmlLabelName(16123, i);
/*  51 */       str12 = str12 + ":" + str10;
/*  52 */       str5 = SystemEnv.getHtmlLabelName(16123, i);
/*  53 */       str5 = str5 + ":" + str10;
/*  54 */       str5 = str5 + "-" + str10;
/*  55 */       str5 = str5 + "-" + str7;
/*  56 */       str6 = "<a href=/hrm/resource/HrmResource.jsp?id=" + str8 + ">" + Util.fromScreen2(str12, i) + "</a><br>" + SystemEnv.getHtmlLabelName(454, i) + ":" + str3;
/*  57 */       this.log.error("参数remark====>" + str6);
/*  58 */       SysRemindWorkflow sysRemindWorkflow = new SysRemindWorkflow();
/*     */       try {
/*  60 */         sysRemindWorkflow.setPrjSysRemind(str5, 0, 1, str4, str6);
/*  61 */       } catch (Exception exception) {
/*  62 */         this.log.error("生成提醒工作流发生异常====>:" + exception.getMessage());
/*  63 */         exception.printStackTrace();
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/*  68 */       recordSet.executeSql("delete from hrmrolemembers where resourceid=" + str8);
/*     */       
/*  70 */       recordSet.executeSql("delete from PluginLicenseUser where plugintype='mobile' and sharetype='0' and sharevalue='" + str8 + "'");
/*     */       
/*  72 */       str9 = "update HrmResource set status =5, loginid='',password='' ,account='' where id = " + str8;
/*  73 */       recordSet.executeSql(str9);
/*  74 */       str9 = "delete hrmgroupmembers where userid=" + str8;
/*  75 */       recordSet.executeSql(str9);
/*  76 */       str9 = "select max(id) from HrmStatusHistory";
/*  77 */       recordSet.executeSql(str9);
/*  78 */       recordSet.next();
/*  79 */       str9 = "update HrmStatusHistory set isdispose = 1 where id=" + recordSet.getInt(1);
/*  80 */       recordSet.executeSql(str9);
/*     */       
/*  82 */       (new Thread((Runnable)new OrganisationComRunnable("user", "dismiss", str8 + "-" + paramString1))).start();
/*     */ 
/*     */       
/*  85 */       HrmServiceManager hrmServiceManager = new HrmServiceManager();
/*  86 */       hrmServiceManager.SynInstantHrmResource(str8, "3");
/*     */ 
/*     */ 
/*     */       
/*  90 */       recordSet.executeSql("select id,readers from cowork_items where coworkers like '%" + str8 + "%' and readers not like '" + str8 + "%'");
/*  91 */       while (recordSet.next()) {
/*  92 */         String str13 = Util.null2String(recordSet.getString(1));
/*  93 */         String str14 = Util.null2String(recordSet.getString(2));
/*  94 */         if (!str14.equals("")) { str14 = str14 + str8 + ","; }
/*  95 */         else { str14 = "," + str8 + ","; }
/*  96 */          recordSet.executeSql("update cowork_items set readers='" + str14 + "' where id=" + str13);
/*     */       } 
/*     */       
/*  99 */       this.log.error("****************禁用账号做离职操作结束***************");
/*     */     } else {
/*     */       
/* 102 */       this.log.error("****************禁用账号:" + paramString1 + "在OA系统中未找到，不做离职处理操作***************");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/PersonDisMissUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */