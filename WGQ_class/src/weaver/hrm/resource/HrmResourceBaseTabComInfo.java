/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONObject;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmResourceBaseTabComInfo
/*     */   extends CacheBase
/*     */ {
/*  21 */   private DecimalFormat df = new DecimalFormat("00000.00");
/*     */   
/*  23 */   protected static String TABLE_NAME = "HrmResourceBaseTab";
/*     */   
/*  25 */   protected static String TABLE_WHERE = null;
/*     */   
/*  27 */   protected static String TABLE_ORDER = "dsporder";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  30 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn(name = "groupname")
/*     */   protected static int field_name;
/*     */   
/*     */   @CacheColumn(name = "grouplabel")
/*     */   protected static int label;
/*     */   
/*     */   @CacheColumn(name = "dsporder")
/*     */   protected static int dsporder;
/*     */   @CacheColumn(name = "isopen")
/*     */   protected static int isopen;
/*     */   @CacheColumn(name = "ismand")
/*     */   protected static int ismand;
/*     */   @CacheColumn(name = "isused")
/*     */   private static int isused;
/*     */   @CacheColumn(name = "issystem")
/*     */   protected static int issystem;
/*     */   @CacheColumn(name = "linkurl")
/*     */   protected static int linkurl;
/*     */   @CacheColumn(name = "tabnum")
/*     */   protected static int tabnum;
/*  52 */   private static TreeMap<String, JSONObject> usedFieldMap = new TreeMap<>();
/*  53 */   private static TreeMap<String, JSONObject> mandFieldMap = new TreeMap<>();
/*  54 */   private static TreeMap<String, JSONObject> openFieldMap = new TreeMap<>();
/*     */ 
/*     */   
/*     */   protected CacheMap initCache() throws Exception {
/*  58 */     CacheMap cacheMap = createCacheMap();
/*  59 */     RecordSet recordSet = new RecordSet();
/*  60 */     openFieldMap.clear();
/*  61 */     mandFieldMap.clear();
/*  62 */     usedFieldMap.clear();
/*     */     
/*  64 */     String str = " select * from HrmResourceBaseTab order by dsporder ";
/*  65 */     recordSet.execute(str);
/*  66 */     while (recordSet.next()) {
/*  67 */       CacheItem cacheItem = createCacheItem();
/*     */       
/*  69 */       String str1 = Util.null2String(recordSet.getString("id"));
/*  70 */       cacheItem.set(0, str1);
/*  71 */       cacheItem.set(field_name, Util.null2String(recordSet.getString("groupname")));
/*  72 */       cacheItem.set(label, Util.null2String(recordSet.getString("grouplabel")));
/*  73 */       cacheItem.set(dsporder, Util.null2String(recordSet.getString("dsporder")));
/*  74 */       cacheItem.set(isopen, Util.null2String(recordSet.getString("isopen")));
/*  75 */       cacheItem.set(ismand, Util.null2String(recordSet.getString("ismand")));
/*  76 */       cacheItem.set(isused, Util.null2String(recordSet.getString("isused")));
/*  77 */       cacheItem.set(issystem, Util.null2String(recordSet.getString("issystem")));
/*  78 */       cacheItem.set(linkurl, Util.null2String(recordSet.getString("linkurl")));
/*  79 */       cacheItem.set(tabnum, Util.null2String(recordSet.getString("tabnum")));
/*     */ 
/*     */       
/*  82 */       JSONObject jSONObject = new JSONObject();
/*  83 */       jSONObject.put("id", str1);
/*  84 */       jSONObject.put("fieldname", Util.null2String(recordSet.getString("groupname")));
/*  85 */       jSONObject.put("fieldlabel", Util.null2String(recordSet.getString("grouplabel")));
/*  86 */       jSONObject.put("dsporder", Util.null2String(recordSet.getString("dsporder")));
/*  87 */       jSONObject.put("isopen", Util.null2String(recordSet.getString("isopen")));
/*  88 */       jSONObject.put("ismand", Util.null2String(recordSet.getString("ismand")));
/*  89 */       jSONObject.put("isused", Util.null2String(recordSet.getString("isused")));
/*  90 */       jSONObject.put("issystem", Util.null2String(recordSet.getString("issystem")));
/*  91 */       jSONObject.put("linkurl", Util.null2String(recordSet.getString("linkurl")));
/*  92 */       jSONObject.put("tabnum", Util.null2String(recordSet.getString("tabnum")));
/*     */       
/*  94 */       if ("1".equals(Integer.valueOf(isopen))) {
/*  95 */         openFieldMap.put(this.df.format(Util.getDoubleValue(recordSet.getString("dsporder"))), jSONObject);
/*     */       }
/*  97 */       if ("1".equals(Integer.valueOf(ismand))) {
/*  98 */         mandFieldMap.put(this.df.format(Util.getDoubleValue(recordSet.getString("dsporder"))), jSONObject);
/*     */       }
/* 100 */       if ("1".equals(Integer.valueOf(isused))) {
/* 101 */         usedFieldMap.put(this.df.format(Util.getDoubleValue(recordSet.getString("dsporder"))), jSONObject);
/*     */       }
/*     */       
/* 104 */       modifyCacheItem(str1, cacheItem);
/* 105 */       cacheMap.put(str1, cacheItem);
/*     */     } 
/* 107 */     return cacheMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected CacheItem initCache(String paramString) {
/* 112 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getGroupNum() {
/* 122 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/* 135 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGroupid() {
/* 145 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getGroupName() {
/* 150 */     return (String)getRowValue(field_name);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getGroupName(String paramString) {
/* 155 */     return (String)getRowValue(field_name);
/*     */   }
/*     */   public String getTabnum() {
/* 158 */     return (String)getRowValue(tabnum);
/*     */   }
/*     */   public String getTabnum(String paramString) {
/* 161 */     return (String)getValue(tabnum, paramString);
/*     */   }
/*     */   public String getLabel() {
/* 164 */     return (String)getRowValue(label);
/*     */   }
/*     */   public String getLabel(String paramString) {
/* 167 */     return (String)getValue(label, paramString);
/*     */   }
/*     */   public String getDsporder() {
/* 170 */     return (String)getRowValue(dsporder);
/*     */   }
/*     */   public String getDsporder(String paramString) {
/* 173 */     return (String)getValue(dsporder, paramString);
/*     */   }
/*     */   public String getIsopen() {
/* 176 */     return (String)getRowValue(isopen);
/*     */   }
/*     */   public String getIsopen(String paramString) {
/* 179 */     return (String)getValue(isopen, paramString);
/*     */   }
/*     */   public String getIsmand() {
/* 182 */     return (String)getRowValue(ismand);
/*     */   }
/*     */   public String getIsmand(String paramString) {
/* 185 */     return (String)getValue(ismand, paramString);
/*     */   }
/*     */   public String getIsused() {
/* 188 */     return (String)getRowValue(isused);
/*     */   }
/*     */   public String getIsused(String paramString) {
/* 191 */     return (String)getValue(isused, paramString);
/*     */   }
/*     */   public String getIsSystem() {
/* 194 */     return (String)getRowValue(issystem);
/*     */   }
/*     */   public String getIsSystem(String paramString) {
/* 197 */     return (String)getValue(issystem, paramString);
/*     */   }
/*     */   public String getLinkurl() {
/* 200 */     return (String)getRowValue(linkurl);
/*     */   }
/*     */   public String getLinkurl(String paramString) {
/* 203 */     return (String)getValue(linkurl, paramString);
/*     */   }
/*     */   
/*     */   public void removeCache() {
/* 207 */     super.removeCache();
/*     */   }
/*     */   
/*     */   public TreeMap<String, JSONObject> getUsedFieldMap() {
/* 211 */     return usedFieldMap;
/*     */   }
/*     */   public TreeMap<String, JSONObject> getOpenFieldMap() {
/* 214 */     return openFieldMap;
/*     */   }
/*     */   public TreeMap<String, JSONObject> getMandFieldMap() {
/* 217 */     return mandFieldMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/HrmResourceBaseTabComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */