/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AllManagers
/*     */   extends BaseBean
/*     */ {
/*  21 */   private int currentindex = -1;
/*  22 */   private int recordercount = 0;
/*     */   private ArrayList allmanagerids;
/*     */   
/*     */   public AllManagers() {
/*  26 */     this.allmanagerids = new ArrayList();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getAll(String paramString) throws Exception {
/*  35 */     String str = paramString;
/*  36 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*     */     
/*  38 */     this.allmanagerids.clear();
/*  39 */     this.currentindex = -1;
/*  40 */     this.recordercount = 0;
/*     */     
/*  42 */     byte b = 0;
/*     */     
/*  44 */     while (b <= 50) {
/*  45 */       String str1 = resourceComInfo.getManagerID(str);
/*  46 */       if (str1.equals(str) || str1.equals("0") || str1.equals("")) {
/*     */         break;
/*     */       }
/*     */       
/*  50 */       if (this.allmanagerids.indexOf(str1) >= 0)
/*  51 */         break;  String str2 = resourceComInfo.getStatus(str1);
/*  52 */       if (str2.equals("0") || str2.equals("1") || str2.equals("2") || str2.equals("3")) {
/*  53 */         this.allmanagerids.add(str1);
/*  54 */         this.recordercount++;
/*     */       } 
/*  56 */       str = str1;
/*     */       
/*  58 */       b++;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllManagerstr(String paramString) {
/*  69 */     String str = "0";
/*     */     try {
/*  71 */       getAll(paramString);
/*  72 */     } catch (Exception exception) {}
/*     */     
/*  74 */     for (byte b = 0; b < this.allmanagerids.size(); b++) {
/*  75 */       if (str.equals("0")) {
/*  76 */         str = this.allmanagerids.get(b);
/*     */       } else {
/*  78 */         str = str + "," + this.allmanagerids.get(b);
/*     */       } 
/*     */     } 
/*  81 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAllManagerNum() {
/*  90 */     return this.recordercount;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  99 */     if (this.currentindex + 1 < this.recordercount) {
/* 100 */       this.currentindex++;
/* 101 */       return true;
/*     */     } 
/*     */     
/* 104 */     this.currentindex = -1;
/* 105 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getManagerID() {
/* 110 */     return this.allmanagerids.get(this.currentindex);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/AllManagers.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */