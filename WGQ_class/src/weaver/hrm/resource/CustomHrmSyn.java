/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.hrm.DepartmentBean;
/*     */ import weaver.interfaces.hrm.HrmServiceManager;
/*     */ import weaver.interfaces.hrm.HrmSynService;
/*     */ import weaver.interfaces.hrm.JobTitleBean;
/*     */ import weaver.interfaces.hrm.OrgXmlBean;
/*     */ import weaver.interfaces.hrm.ParseXml;
/*     */ import weaver.interfaces.hrm.SubCompanyBean;
/*     */ import weaver.interfaces.hrm.UserBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomHrmSyn
/*     */   extends BaseBean
/*     */ {
/*  38 */   private Logger newlog = LoggerFactory.getLogger(CustomHrmSyn.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap syn() {
/*  45 */     return syn("0");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap syn(String paramString) {
/*  54 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  56 */       HrmSynService hrmSynService = (HrmSynService)StaticObj.getService("hrmsyn", "hrmsyncustom", HrmSynService.class);
/*  57 */       hrmSynService.removeSynResult();
/*  58 */       HrmServiceManager hrmServiceManager = new HrmServiceManager();
/*     */ 
/*     */       
/*  61 */       if ("0".equals(paramString)) {
/*  62 */         String str1 = Util.null2String(hrmSynService.SynTimingToOASubCompany());
/*  63 */         if (!"".equals(str1)) {
/*  64 */           SynSubCompany(str1);
/*     */         }
/*     */         
/*  67 */         String str2 = Util.null2String(hrmSynService.SynTimingToOADepartment());
/*  68 */         if (!"".equals(str2)) {
/*  69 */           SynDepartment(str2);
/*     */         }
/*  71 */         String str3 = Util.null2String(hrmSynService.SynTimingToOAJobtitle());
/*  72 */         if (!"".equals(str3)) {
/*  73 */           SynJobtitle(str3);
/*     */         }
/*  75 */         String str4 = Util.null2String(hrmSynService.SynTimingToOAHrmResource());
/*  76 */         if (!"".equals(str4)) {
/*  77 */           SynHrmResource(str4);
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/*  82 */       if ("1".equals(paramString)) {
/*  83 */         String str = Util.null2String(hrmSynService.SynTimingToOASubCompany());
/*  84 */         if (!"".equals(str)) {
/*  85 */           SynSubCompany(str);
/*     */         }
/*     */       } 
/*     */       
/*  89 */       if ("2".equals(paramString)) {
/*  90 */         String str = Util.null2String(hrmSynService.SynTimingToOADepartment());
/*  91 */         if (!"".equals(str)) {
/*  92 */           SynDepartment(str);
/*     */         }
/*     */       } 
/*     */       
/*  96 */       if ("3".equals(paramString)) {
/*  97 */         String str = Util.null2String(hrmSynService.SynTimingToOAJobtitle());
/*  98 */         if (!"".equals(str)) {
/*  99 */           SynJobtitle(str);
/*     */         }
/*     */       } 
/*     */       
/* 103 */       if ("4".equals(paramString)) {
/* 104 */         String str = Util.null2String(hrmSynService.SynTimingToOAHrmResource());
/* 105 */         if (!"".equals(str)) {
/* 106 */           SynHrmResource(str);
/*     */         }
/*     */       } 
/*     */       
/* 110 */       SubCompanyBean[] arrayOfSubCompanyBean = hrmServiceManager.getHrmSubcompanyInfo();
/* 111 */       hrmSynService.SynTimingFromOASubCompany(arrayOfSubCompanyBean);
/*     */       
/* 113 */       DepartmentBean[] arrayOfDepartmentBean = hrmServiceManager.getHrmDepartmentInfo();
/* 114 */       hrmSynService.SynTimingFromOADepartment(arrayOfDepartmentBean);
/*     */       
/* 116 */       JobTitleBean[] arrayOfJobTitleBean = hrmServiceManager.getHrmJobTitleInfo();
/* 117 */       hrmSynService.SynTimingFromOAJobtitle(arrayOfJobTitleBean);
/*     */       
/* 119 */       UserBean[] arrayOfUserBean = hrmServiceManager.getHrmUserInfo();
/* 120 */       hrmSynService.SynTimingFromOAHrmResource(arrayOfUserBean);
/*     */       
/* 122 */       hashMap = hrmSynService.getSynResult();
/* 123 */     } catch (Exception exception) {
/* 124 */       this.newlog.error(exception);
/* 125 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 128 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SynSubCompany(String paramString) throws Exception {
/* 137 */     if (!Util.null2String(paramString).equals("")) {
/* 138 */       ParseXml parseXml = new ParseXml();
/*     */       
/* 140 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 142 */       ArrayList arrayList1 = new ArrayList();
/*     */       
/* 144 */       ArrayList arrayList2 = new ArrayList();
/*     */       
/* 146 */       ArrayList arrayList3 = new ArrayList();
/* 147 */       HrmServiceManager hrmServiceManager = new HrmServiceManager();
/*     */       
/*     */       try {
/* 150 */         String str = "";
/* 151 */         parseXml.parseOrg(paramString);
/* 152 */         hashMap = parseXml.getH_orgInfo();
/* 153 */         List<String> list1 = parseXml.getH_addOrg();
/* 154 */         List<String> list2 = parseXml.getH_updateOrg();
/* 155 */         List<String> list3 = parseXml.getH_delOrg(); byte b;
/* 156 */         for (b = 0; b < list1.size(); b++) {
/* 157 */           str = list1.get(b);
/* 158 */           OrgXmlBean orgXmlBean = (OrgXmlBean)hashMap.get(str);
/* 159 */           hrmServiceManager.addSubCompany(orgXmlBean);
/*     */         } 
/*     */         
/* 162 */         for (b = 0; b < list2.size(); b++) {
/* 163 */           str = list2.get(b);
/* 164 */           OrgXmlBean orgXmlBean = (OrgXmlBean)hashMap.get(str);
/* 165 */           hrmServiceManager.editSubCompany(orgXmlBean);
/*     */         } 
/*     */         
/* 168 */         for (b = 0; b < list3.size(); b++) {
/* 169 */           str = list3.get(b);
/* 170 */           OrgXmlBean orgXmlBean = (OrgXmlBean)hashMap.get(str);
/* 171 */           hrmServiceManager.editSubCompany(orgXmlBean);
/*     */         } 
/*     */         
/*     */         try {
/* 175 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 176 */           subCompanyComInfo.removeCompanyCache();
/* 177 */         } catch (Exception exception) {
/* 178 */           this.newlog.error(exception);
/* 179 */           exception.printStackTrace();
/*     */         } 
/* 181 */       } catch (Exception exception) {
/* 182 */         this.newlog.error(exception);
/* 183 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SynDepartment(String paramString) throws Exception {
/* 194 */     if (!Util.null2String(paramString).equals("")) {
/* 195 */       ParseXml parseXml = new ParseXml();
/*     */       
/* 197 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 199 */       ArrayList arrayList1 = new ArrayList();
/*     */       
/* 201 */       ArrayList arrayList2 = new ArrayList();
/*     */       
/* 203 */       ArrayList arrayList3 = new ArrayList();
/* 204 */       HrmServiceManager hrmServiceManager = new HrmServiceManager();
/*     */       
/*     */       try {
/* 207 */         String str = "";
/* 208 */         parseXml.parseOrg(paramString);
/* 209 */         hashMap = parseXml.getH_orgInfo();
/* 210 */         List<String> list1 = parseXml.getH_addOrg();
/* 211 */         List<String> list2 = parseXml.getH_updateOrg();
/* 212 */         List<String> list3 = parseXml.getH_delOrg(); byte b;
/* 213 */         for (b = 0; b < list1.size(); b++) {
/* 214 */           str = list1.get(b);
/* 215 */           OrgXmlBean orgXmlBean = (OrgXmlBean)hashMap.get(str);
/* 216 */           hrmServiceManager.addDepartment(orgXmlBean);
/*     */         } 
/*     */         
/* 219 */         for (b = 0; b < list2.size(); b++) {
/* 220 */           str = list2.get(b);
/* 221 */           OrgXmlBean orgXmlBean = (OrgXmlBean)hashMap.get(str);
/* 222 */           hrmServiceManager.editDepartment(orgXmlBean);
/*     */         } 
/*     */         
/* 225 */         for (b = 0; b < list3.size(); b++) {
/* 226 */           str = list3.get(b);
/* 227 */           OrgXmlBean orgXmlBean = (OrgXmlBean)hashMap.get(str);
/* 228 */           hrmServiceManager.delDepartment(orgXmlBean);
/*     */         } 
/*     */         
/*     */         try {
/* 232 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 233 */           departmentComInfo.removeCompanyCache();
/* 234 */         } catch (Exception exception) {
/* 235 */           this.newlog.error(exception);
/* 236 */           exception.printStackTrace();
/*     */         } 
/* 238 */       } catch (Exception exception) {
/* 239 */         this.newlog.error(exception);
/* 240 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SynJobtitle(String paramString) throws Exception {
/* 251 */     if (!Util.null2String(paramString).equals("")) {
/*     */       
/* 253 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 255 */       ArrayList arrayList1 = new ArrayList();
/*     */       
/* 257 */       ArrayList arrayList2 = new ArrayList();
/*     */       
/* 259 */       ArrayList arrayList3 = new ArrayList();
/* 260 */       ParseXml parseXml = new ParseXml();
/* 261 */       HrmServiceManager hrmServiceManager = new HrmServiceManager();
/*     */       
/*     */       try {
/* 264 */         String str = "";
/* 265 */         parseXml.parseJobTitle(paramString);
/* 266 */         hashMap = parseXml.getH_orgInfo();
/* 267 */         List<String> list1 = parseXml.getH_addOrg();
/* 268 */         List<String> list2 = parseXml.getH_updateOrg();
/* 269 */         List<String> list3 = parseXml.getH_delOrg(); byte b;
/* 270 */         for (b = 0; b < list1.size(); b++) {
/* 271 */           str = list1.get(b);
/* 272 */           JobTitleBean jobTitleBean = (JobTitleBean)hashMap.get(str);
/* 273 */           hrmServiceManager.addJobTitle(jobTitleBean);
/*     */         } 
/*     */         
/* 276 */         for (b = 0; b < list2.size(); b++) {
/* 277 */           str = list2.get(b);
/* 278 */           JobTitleBean jobTitleBean = (JobTitleBean)hashMap.get(str);
/* 279 */           hrmServiceManager.editJobTitle(jobTitleBean);
/*     */         } 
/*     */         
/* 282 */         for (b = 0; b < list3.size(); b++) {
/* 283 */           str = list3.get(b);
/* 284 */           JobTitleBean jobTitleBean = (JobTitleBean)hashMap.get(str);
/* 285 */           hrmServiceManager.delJobTitle(jobTitleBean);
/*     */         } 
/*     */         
/*     */         try {
/* 289 */           JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 290 */           jobTitlesComInfo.removeJobTitlesCache();
/* 291 */         } catch (Exception exception) {
/* 292 */           this.newlog.error(exception);
/* 293 */           exception.printStackTrace();
/*     */         } 
/* 295 */       } catch (Exception exception) {
/* 296 */         this.newlog.error(exception);
/* 297 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SynHrmResource(String paramString) throws Exception {
/* 308 */     if (!Util.null2String(paramString).equals("")) {
/*     */       
/* 310 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 312 */       ArrayList arrayList1 = new ArrayList();
/*     */       
/* 314 */       ArrayList arrayList2 = new ArrayList();
/*     */       
/* 316 */       ArrayList arrayList3 = new ArrayList();
/* 317 */       ParseXml parseXml = new ParseXml();
/* 318 */       HrmServiceManager hrmServiceManager = new HrmServiceManager();
/*     */       try {
/* 320 */         String str = "";
/* 321 */         parseXml.parseHrmResource(paramString);
/* 322 */         hashMap = parseXml.getH_orgInfo();
/* 323 */         List<String> list1 = parseXml.getH_addOrg();
/* 324 */         List<String> list2 = parseXml.getH_updateOrg(); byte b;
/* 325 */         for (b = 0; b < list1.size(); b++) {
/* 326 */           str = list1.get(b);
/* 327 */           UserBean userBean = (UserBean)hashMap.get(str);
/* 328 */           hrmServiceManager.synHrmResource(userBean);
/*     */         } 
/*     */         
/* 331 */         for (b = 0; b < list2.size(); b++) {
/* 332 */           str = list2.get(b);
/* 333 */           UserBean userBean = (UserBean)hashMap.get(str);
/* 334 */           hrmServiceManager.synHrmResource(userBean);
/*     */         } 
/* 336 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 337 */         resourceComInfo.removeResourceCache();
/* 338 */       } catch (Exception exception) {
/* 339 */         this.newlog.error(exception);
/* 340 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/CustomHrmSyn.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */