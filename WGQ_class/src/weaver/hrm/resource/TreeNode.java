/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TreeNode
/*     */ {
/*     */   private String checked;
/*  34 */   private ArrayList<TreeNode> children = new ArrayList<TreeNode>(); private String chkDisabled; private String click; private String halfCheck; private String icon;
/*     */   private String iconClose;
/*     */   private String iconOpen;
/*     */   private String iconSkin;
/*     */   private String isHidden;
/*     */   private String isParent;
/*     */   
/*     */   public void AddChildren(TreeNode paramTreeNode) {
/*  42 */     this.children.add(paramTreeNode);
/*     */   }
/*     */   private String name; private String nocheck; private String open; private String target; private String type; private String url; private String id; private String nodeid; private String pid;
/*     */   public String getChecked() {
/*  46 */     return this.checked;
/*     */   }
/*     */   
/*     */   public void setChecked(String paramString) {
/*  50 */     this.checked = paramString;
/*     */   }
/*     */   
/*     */   public ArrayList<TreeNode> getChildren() {
/*  54 */     return this.children;
/*     */   }
/*     */   
/*     */   public void setChildren(ArrayList<TreeNode> paramArrayList) {
/*  58 */     this.children = paramArrayList;
/*     */   }
/*     */   
/*     */   public String getChkDisabled() {
/*  62 */     return this.chkDisabled;
/*     */   }
/*     */   
/*     */   public void setChkDisabled(String paramString) {
/*  66 */     this.chkDisabled = paramString;
/*     */   }
/*     */   
/*     */   public String getClick() {
/*  70 */     return this.click;
/*     */   }
/*     */   
/*     */   public void setClick(String paramString) {
/*  74 */     this.click = paramString;
/*     */   }
/*     */   
/*     */   public String getHalfCheck() {
/*  78 */     return this.halfCheck;
/*     */   }
/*     */   
/*     */   public void setHalfCheck(String paramString) {
/*  82 */     this.halfCheck = paramString;
/*     */   }
/*     */   
/*     */   public String getIcon() {
/*  86 */     return this.icon;
/*     */   }
/*     */   
/*     */   public void setIcon(String paramString) {
/*  90 */     this.icon = paramString;
/*     */   }
/*     */   
/*     */   public String getIconClose() {
/*  94 */     return this.iconClose;
/*     */   }
/*     */   
/*     */   public void setIconClose(String paramString) {
/*  98 */     this.iconClose = paramString;
/*     */   }
/*     */   
/*     */   public String getIconOpen() {
/* 102 */     return this.iconOpen;
/*     */   }
/*     */   
/*     */   public void setIconOpen(String paramString) {
/* 106 */     this.iconOpen = paramString;
/*     */   }
/*     */   
/*     */   public String getIconSkin() {
/* 110 */     return this.iconSkin;
/*     */   }
/*     */   
/*     */   public void setIconSkin(String paramString) {
/* 114 */     this.iconSkin = paramString;
/*     */   }
/*     */   
/*     */   public String getIsHidden() {
/* 118 */     return this.isHidden;
/*     */   }
/*     */   
/*     */   public void setIsHidden(String paramString) {
/* 122 */     this.isHidden = paramString;
/*     */   }
/*     */   
/*     */   public String getIsParent() {
/* 126 */     return this.isParent;
/*     */   }
/*     */   
/*     */   public void setIsParent(String paramString) {
/* 130 */     this.isParent = paramString;
/*     */   }
/*     */   
/*     */   public String getName() {
/* 134 */     return this.name;
/*     */   }
/*     */   
/*     */   public void setName(String paramString) {
/* 138 */     this.name = paramString;
/*     */   }
/*     */   
/*     */   public String getNocheck() {
/* 142 */     return this.nocheck;
/*     */   }
/*     */   
/*     */   public void setNocheck(String paramString) {
/* 146 */     this.nocheck = paramString;
/*     */   }
/*     */   
/*     */   public String getOpen() {
/* 150 */     return this.open;
/*     */   }
/*     */   
/*     */   public void setOpen(String paramString) {
/* 154 */     this.open = paramString;
/*     */   }
/*     */   
/*     */   public String getTarget() {
/* 158 */     return this.target;
/*     */   }
/*     */   
/*     */   public void setTarget(String paramString) {
/* 162 */     this.target = paramString;
/*     */   }
/*     */   
/*     */   public String getUrl() {
/* 166 */     return this.url;
/*     */   }
/*     */   
/*     */   public void setUrl(String paramString) {
/* 170 */     this.url = paramString;
/*     */   }
/*     */   
/*     */   public String getId() {
/* 174 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(String paramString) {
/* 178 */     this.id = paramString;
/*     */   }
/*     */   
/*     */   public String getPid() {
/* 182 */     return this.pid;
/*     */   }
/*     */   
/*     */   public void setPid(String paramString) {
/* 186 */     this.pid = paramString;
/*     */   }
/*     */   
/*     */   public String getType() {
/* 190 */     return this.type;
/*     */   }
/*     */   
/*     */   public void setType(String paramString) {
/* 194 */     this.type = paramString;
/*     */   }
/*     */   
/*     */   public String getNodeid() {
/* 198 */     return this.nodeid;
/*     */   }
/*     */   
/*     */   public void setNodeid(String paramString) {
/* 202 */     this.nodeid = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/TreeNode.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */