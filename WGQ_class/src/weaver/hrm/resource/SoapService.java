/*     */ package weaver.hrm.resource;
/*     */ import java.io.StringReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.xml.parsers.DocumentBuilder;
/*     */ import javax.xml.parsers.DocumentBuilderFactory;
/*     */ import javax.xml.xpath.XPath;
/*     */ import javax.xml.xpath.XPathConstants;
/*     */ import javax.xml.xpath.XPathExpression;
/*     */ import javax.xml.xpath.XPathFactory;
/*     */ import org.apache.axiom.om.OMAbstractFactory;
/*     */ import org.apache.axiom.om.OMElement;
/*     */ import org.apache.axiom.om.OMFactory;
/*     */ import org.apache.axiom.om.OMNamespace;
/*     */ import org.apache.axiom.om.OMNode;
/*     */ import org.apache.axis2.AxisFault;
/*     */ import org.apache.axis2.addressing.EndpointReference;
/*     */ import org.apache.axis2.client.Options;
/*     */ import org.apache.axis2.client.ServiceClient;
/*     */ import org.w3c.dom.Document;
/*     */ import org.w3c.dom.Node;
/*     */ import org.w3c.dom.NodeList;
/*     */ import org.xml.sax.InputSource;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ public class SoapService extends BaseBean {
/*  36 */   private static Logger lm = LoggerFactory.getLogger(SoapService.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String serviceSend(String paramString1, String paramString2, Map paramMap1, Map paramMap2) {
/*  49 */     return serviceSend(paramString1, "", paramString2, paramMap1, paramMap2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String serviceSend(String paramString1, String paramString2, String paramString3, Map paramMap1, Map paramMap2) {
/*  63 */     if (!"".equals(paramString1) && !"".equals(paramString3)) {
/*  64 */       return (String)sendAxis2Receive(paramString1, paramString2, paramString3, paramMap1, paramMap2);
/*     */     }
/*  66 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String serviceSend(String paramString1, String paramString2, Map paramMap, String paramString3) {
/*  79 */     return serviceSend(paramString1, "", paramString2, paramMap, paramString3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String serviceSend(String paramString1, String paramString2, String paramString3, Map paramMap, String paramString4) {
/*  94 */     if (!"".equals(paramString1) && !"".equals(paramString3)) {
/*  95 */       Map map1 = (Map)paramMap.get("type");
/*  96 */       Map map2 = (Map)paramMap.get("value");
/*  97 */       Map map3 = (Map)paramMap.get("array");
/*  98 */       return (String)sendAxis2Receive(paramString1, paramString2, paramString3, map1, map2);
/*     */     } 
/* 100 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static Object sendAxis2Receive(String paramString1, String paramString2, String paramString3, Map paramMap1, Map paramMap2) {
/* 115 */     System.setProperty("javax.xml.stream.XMLInputFactory", "com.sun.xml.internal.stream.XMLInputFactoryImpl");
/* 116 */     ServiceClient serviceClient = null;
/*     */     
/*     */     try {
/* 119 */       Options options = new Options();
/*     */       
/* 121 */       if (paramString1.indexOf("?wsdl") > -1) {
/* 122 */         paramString1 = paramString1.substring(0, paramString1.indexOf("?wsdl"));
/*     */       }
/* 124 */       EndpointReference endpointReference = new EndpointReference(paramString1);
/* 125 */       options.setTo(endpointReference);
/*     */       
/* 127 */       serviceClient = new ServiceClient();
/* 128 */       serviceClient.setOptions(options);
/* 129 */       OMFactory oMFactory = OMAbstractFactory.getOMFactory();
/* 130 */       OMNamespace oMNamespace = null;
/*     */       
/* 132 */       if (paramString2 != null && !"".equals(paramString2)) {
/* 133 */         String str = paramString2;
/* 134 */         if (paramString2.lastIndexOf("/") != paramString2.length() - 1) {
/* 135 */           str = str + "/";
/*     */         }
/* 137 */         options.setAction(str + paramString3);
/* 138 */         oMNamespace = oMFactory.createOMNamespace(str, "");
/*     */       } else {
/* 140 */         options.setAction(paramString3);
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 145 */       OMElement oMElement1 = oMFactory.createOMElement(paramString3, oMNamespace);
/* 146 */       if (paramMap1 != null && paramMap1.size() > 0) {
/* 147 */         Set set = paramMap1.keySet();
/* 148 */         for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/* 149 */           String str1 = Util.null2String(iterator.next());
/* 150 */           String str2 = Util.null2String((String)paramMap1.get(str1));
/* 151 */           String str3 = Util.null2String((String)paramMap2.get(str1));
/* 152 */           OMElement oMElement = oMFactory.createOMElement(str1, oMNamespace);
/* 153 */           oMElement.setText(str3);
/* 154 */           oMElement1.addChild((OMNode)oMElement);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 159 */       OMElement oMElement2 = serviceClient.sendReceive(oMElement1);
/* 160 */       if (oMElement2 != null) {
/* 161 */         String str = oMElement2.getFirstElement().getText();
/*     */         
/* 163 */         lm.info("result is " + str);
/* 164 */         return str;
/*     */       } 
/* 166 */       lm.error("result is null!!!");
/*     */ 
/*     */       
/* 169 */       return "";
/* 170 */     } catch (Exception exception) {
/* 171 */       exception.printStackTrace();
/* 172 */       lm.error("=============error occured!!!" + exception.getMessage());
/*     */     } finally {
/* 174 */       if (serviceClient != null) {
/*     */         try {
/* 176 */           serviceClient.cleanupTransport();
/* 177 */         } catch (AxisFault axisFault) {
/* 178 */           axisFault.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/* 182 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List parseServiceResult(String paramString1, String paramString2) {
/* 193 */     List list = null;
/*     */ 
/*     */     
/*     */     try {
/* 197 */       DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
/* 198 */       documentBuilderFactory.setNamespaceAware(true);
/* 199 */       SecurityMethodUtil.setDBFFeature(documentBuilderFactory);
/* 200 */       DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
/* 201 */       StringReader stringReader = new StringReader(paramString1);
/*     */       
/* 203 */       InputSource inputSource = new InputSource(stringReader);
/* 204 */       Document document = documentBuilder.parse(inputSource);
/*     */       
/* 206 */       XPathFactory xPathFactory = XPathFactory.newInstance();
/* 207 */       XPath xPath = xPathFactory.newXPath();
/* 208 */       if (!"".equals(paramString2)) {
/* 209 */         list = getElementByName(xPath, document, paramString2);
/*     */       }
/*     */     }
/* 212 */     catch (Exception exception) {
/* 213 */       exception.printStackTrace();
/* 214 */       lm.info(exception.toString());
/*     */     } 
/* 216 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static List getElementByName(XPath paramXPath, Document paramDocument, String paramString) throws Exception {
/* 230 */     ArrayList<String> arrayList = new ArrayList();
/* 231 */     String str = "";
/* 232 */     str = paramString;
/* 233 */     XPathExpression xPathExpression = paramXPath.compile(str);
/* 234 */     Object object = xPathExpression.evaluate(paramDocument, XPathConstants.NODESET);
/* 235 */     NodeList nodeList = (NodeList)object;
/* 236 */     for (byte b = 0; b < nodeList.getLength(); b++) {
/* 237 */       Node node = nodeList.item(b);
/* 238 */       arrayList.add(node.getTextContent());
/*     */     } 
/* 240 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String unicode2String(String paramString) {
/* 250 */     StringBuffer stringBuffer = new StringBuffer();
/* 251 */     String[] arrayOfString = paramString.split("\\\\u");
/*     */     
/* 253 */     for (byte b = 1; b < arrayOfString.length; b++) {
/*     */       
/* 255 */       int i = Integer.parseInt(arrayOfString[b], 16);
/*     */       
/* 257 */       stringBuffer.append((char)i);
/*     */     } 
/*     */     
/* 260 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String unicodeStr2String(String paramString) {
/* 270 */     int i = paramString.length();
/* 271 */     int j = 0;
/*     */     
/* 273 */     String str = "\\\\u[a-f0-9A-F]{1,4}";
/* 274 */     Pattern pattern = Pattern.compile(str);
/* 275 */     Matcher matcher = pattern.matcher(paramString);
/* 276 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 278 */     while (matcher.find()) {
/* 279 */       String str1 = matcher.group();
/* 280 */       String str2 = unicode2String(str1);
/*     */ 
/*     */       
/* 283 */       int k = matcher.start();
/*     */       
/* 285 */       stringBuffer.append(paramString.substring(j, k));
/* 286 */       stringBuffer.append(str2);
/* 287 */       j = k + str1.length();
/*     */     } 
/* 289 */     stringBuffer.append(paramString.substring(j, i));
/* 290 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/SoapService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */