/*      */ package weaver.hrm.resource;
/*      */ 
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.system.language.biz.TransLatorComInfo;
/*      */ import com.engine.common.biz.EncryptConfigBiz;
/*      */ import com.engine.encrypt.biz.DecryptResourceComInfo;
/*      */ import com.engine.encrypt.biz.EncryptFieldViewScopeConfigComInfo;
/*      */ import com.engine.kq.biz.KQGroupMemberComInfo;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import net.sf.json.JSONArray;
/*      */ import org.apache.commons.lang3.StringUtils;
/*      */ import weaver.cache.CacheBase;
/*      */ import weaver.cache.CacheColumn;
/*      */ import weaver.cache.CacheColumnType;
/*      */ import weaver.cache.CacheItem;
/*      */ import weaver.cache.CacheMap;
/*      */ import weaver.cache.PKColumn;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.file.LogMan;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarManager;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.cachecenter.bean.KVResourceComInfo;
/*      */ import weaver.hrm.cachecenter.bean.RolemembersComInfo;
/*      */ import weaver.hrm.common.Tools;
/*      */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*      */ import weaver.hrm.login.cache.ResourceDoaminComInfo;
/*      */ import weaver.hrm.privacy.PrivacyComInfo;
/*      */ import weaver.hrm.privacy.UserPrivacyComInfo;
/*      */ import weaver.hrm.tools.Time;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.setting.HrmUserIconSettingComInfo;
/*      */ import weaver.systeminfo.setting.HrmUserSettingComInfo;
/*      */ import weaver.systeminfo.systemright.CheckUserRight;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ResourceComInfo
/*      */   extends CacheBase
/*      */ {
/*   52 */   String sql = "";
/*   53 */   LogMan lm = LogMan.getInstance();
/*      */ 
/*      */   
/*   56 */   protected static String TABLE_NAME = "HrmResource";
/*      */ 
/*      */ 
/*      */   
/*   60 */   protected static String TABLE_WHERE = null;
/*      */ 
/*      */ 
/*      */   
/*   64 */   protected static String TABLE_ORDER = "dsporder asc,id asc";
/*      */   
/*      */   @PKColumn(type = CacheColumnType.NUMBER)
/*   67 */   protected static String PK_NAME = "id";
/*      */   
/*      */   @CacheColumn(name = "loginid")
/*      */   protected static int loginid;
/*      */   
/*      */   @CacheColumn(name = "workcode")
/*      */   protected static int workcode;
/*      */   
/*      */   @CacheColumn(name = "lastname")
/*      */   protected static int lastname;
/*      */   
/*      */   @CacheColumn(name = "pinyinlastname")
/*      */   protected static int pinyinlastname;
/*      */   
/*      */   @CacheColumn(name = "sex")
/*      */   protected static int sex;
/*      */   @CacheColumn(name = "email")
/*      */   protected static int email;
/*      */   @CacheColumn(name = "resourcetype")
/*      */   protected static int resourcetype;
/*      */   @CacheColumn(name = "locationid")
/*      */   protected static int locationid;
/*      */   @CacheColumn(name = "departmentid")
/*      */   protected static int departmentid;
/*      */   @CacheColumn(name = "subcompanyid1")
/*      */   protected static int subcompanyid;
/*      */   @CacheColumn(name = "costcenterid")
/*      */   protected static int costcenterid;
/*      */   @CacheColumn(name = "jobtitle")
/*      */   protected static int jobtitleid;
/*      */   @CacheColumn(name = "managerid")
/*      */   protected static int managerid;
/*      */   @CacheColumn(name = "assistantid")
/*      */   protected static int assistantid;
/*      */   @CacheColumn(name = "joblevel")
/*      */   protected static int joblevel;
/*      */   @CacheColumn(name = "seclevel")
/*      */   protected static int seclevel;
/*      */   @CacheColumn(name = "status")
/*      */   protected static int status;
/*      */   @CacheColumn(name = "account")
/*      */   protected static int account;
/*      */   @CacheColumn(name = "mobile")
/*      */   protected static int mobile;
/*      */   @CacheColumn(name = "mobileshowtype")
/*      */   protected static int mobileshowtype;
/*      */   @CacheColumn(name = "password")
/*      */   protected static int pwd;
/*      */   @CacheColumn(name = "systemLanguage")
/*      */   protected static int systemLanguage;
/*      */   @CacheColumn(name = "telephone")
/*      */   protected static int telephone;
/*      */   @CacheColumn(name = "managerstr")
/*      */   protected static int managerstr;
/*      */   @CacheColumn(name = "messagerurl")
/*      */   protected static int messagerurl;
/*      */   @CacheColumn(name = "accounttype")
/*      */   protected static int accounttype;
/*      */   @CacheColumn(name = "belongto")
/*      */   protected static int belongto;
/*      */   @CacheColumn(name = "createdate")
/*      */   protected static int createdate;
/*      */   @CacheColumn(name = "resourceimageid")
/*      */   protected static int resourceimageid;
/*      */   @CacheColumn(name = "classification")
/*      */   protected static int classification;
/*      */   @CacheColumn(name = "encKey")
/*      */   protected static int encKey;
/*      */   @CacheColumn(name = "crc")
/*      */   protected static int crc;
/*      */   @CacheColumn(name = "companyStartDate")
/*      */   protected static int companyStartDate;
/*      */   @CacheColumn(name = "workStartDate")
/*      */   protected static int workStartDate;
/*      */   @CacheColumn(name = "enddate")
/*      */   protected static int enddate;
/*      */   @CacheColumn(name = "jobcall")
/*      */   protected static int jobCall;
/*      */   @CacheColumn(name = "mobilecall")
/*      */   protected static int mobileCall;
/*      */   @CacheColumn(name = "fax")
/*      */   protected static int fax;
/*      */   @CacheColumn(name = "jobactivitydesc")
/*      */   protected static int jobactivitydesc;
/*      */   @CacheColumn(name = "workroom")
/*      */   protected static int workroom;
/*      */   @CacheColumn(name = "dsporder")
/*      */   protected static int dsporder;
/*      */   @CacheColumn(name = "workyear")
/*      */   protected static int workyear;
/*      */   @CacheColumn(name = "companyworkyear")
/*      */   protected static int companyworkyear;
/*      */   @CacheColumn(name = "birthday")
/*      */   protected static int birthday;
/*      */   @CacheColumn(name = "educationlevel")
/*      */   protected static int educationlevel;
/*      */   @CacheColumn(name = "isAdmin")
/*      */   protected static int isAdmin;
/*      */   
/*      */   public String getWorkYear() {
/*  167 */     return ((String)getRowValue(workyear)).trim();
/*      */   }
/*      */   
/*      */   public String getWorkYear(String paramString) {
/*  171 */     return ((String)getValue(workyear, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getCompanyWorkYear() {
/*  175 */     return ((String)getRowValue(companyworkyear)).trim();
/*      */   }
/*      */   
/*      */   public String getCompanyWorkYear(String paramString) {
/*  179 */     return ((String)getValue(companyworkyear, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getBirthday() {
/*  183 */     return ((String)getRowValue(birthday)).trim();
/*      */   }
/*      */   
/*      */   public String getBirthday(String paramString) {
/*  187 */     return ((String)getValue(birthday, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getEducationlevel() {
/*  191 */     return ((String)getRowValue(educationlevel)).trim();
/*      */   }
/*      */   
/*      */   public String getEducationlevel(String paramString) {
/*  195 */     return ((String)getValue(educationlevel, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ResourceComInfo() throws Exception {
/*  205 */     ThreadVarManager.setHasMultiFilter(Boolean.valueOf(true));
/*      */   }
/*      */   
/*      */   public ResourceComInfo(boolean paramBoolean) {
/*      */     try {
/*  210 */       ThreadVarManager.setHasMultiFilter(Boolean.valueOf(true));
/*  211 */     } catch (Exception exception) {
/*  212 */       throw new RuntimeException(exception);
/*      */     } 
/*      */   }
/*      */   public CacheMap initCache() throws Exception {
/*  216 */     CacheMap cacheMap = createCacheMap();
/*      */     
/*  218 */     RecordSet recordSet = new RecordSet();
/*  219 */     String str = " select a.*, 0 as isAdmin from hrmresource a order by a.dsporder asc, a.id asc";
/*  220 */     recordSet.executeSql(str);
/*  221 */     while (recordSet.next()) {
/*  222 */       String str1 = Util.null2String(recordSet.getString(PK_NAME));
/*  223 */       CacheItem cacheItem = createCacheItem();
/*  224 */       parseResultSetToCacheItem(recordSet, cacheItem, "hrmresource");
/*      */       
/*  226 */       modifyCacheItem(str1, cacheItem);
/*  227 */       cacheMap.put(str1, cacheItem);
/*      */     } 
/*  229 */     return cacheMap;
/*      */   }
/*      */   
/*      */   public CacheItem initCache(String paramString) {
/*  233 */     if (Util.getIntValue(paramString) <= 0) {
/*  234 */       return null;
/*      */     }
/*      */     
/*  237 */     boolean bool = false;
/*  238 */     CacheItem cacheItem = null;
/*  239 */     RecordSet recordSet = new RecordSet();
/*  240 */     String str = " select a.*, 0 as isAdmin from hrmresource a where id=" + paramString + " order by a.dsporder asc, a.id asc";
/*  241 */     recordSet.executeSql(str);
/*  242 */     if (recordSet.next()) {
/*  243 */       cacheItem = createCacheItem();
/*  244 */       parseResultSetToCacheItem(recordSet, cacheItem, "hrmresource");
/*  245 */       modifyCacheItem(paramString, cacheItem);
/*  246 */       bool = true;
/*      */     } 
/*      */     
/*  249 */     if (!bool) {
/*  250 */       str = "SELECT id,loginid,'' AS workcode,lastname,'' as pinyinlastname ,'' AS sex, '' AS email,'' AS resourcetype,0 AS locationid,0 AS departmentid,0 AS subcompanyid1, 0 AS costcenterid,0 AS jobtitle,0 AS managerid,0 AS assistantid,0 AS joblevel,seclevel,status,'' AS account,mobile,password,systemLanguage, '' AS telephone,'' AS managerstr,'' AS messagerurl , id AS dsporder,0 AS accounttype, 0 AS belongto, 0 as mobileshowtype,creater,created,modified,modifier,tokenKey, '' AS createdate ,0 AS resourceimageid,9999 AS classification,'' AS encKey,'' AS crc,'' as enddate,1 as isAdmin FROM HrmResourceManager where id=" + paramString;
/*  251 */       recordSet.executeSql(str);
/*  252 */       if (recordSet.next()) {
/*  253 */         cacheItem = createCacheItem();
/*  254 */         parseResultSetToCacheItem(recordSet, cacheItem, "HrmResourceManager");
/*  255 */         modifyCacheItem(paramString, cacheItem);
/*      */       } 
/*      */     } 
/*  258 */     return cacheItem;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getResourceNum() {
/*  267 */     return size();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean next() {
/*  276 */     while (super.next()) {
/*  277 */       if ("sysadmin".equals(getRowValue(loginid))) {
/*      */         continue;
/*      */       }
/*  280 */       return true;
/*      */     } 
/*  282 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourceid() {
/*  292 */     return (String)getRowValue(0);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourcename() {
/*  301 */     return ((String)getRowValue(lastname)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFirstname() {
/*  310 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLoginID() {
/*  319 */     return ((String)getRowValue(loginid)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWorkcode() {
/*  328 */     return ((String)getRowValue(workcode)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPWD() {
/*  337 */     return ((String)getRowValue(pwd)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLastname() {
/*  346 */     return ((String)getRowValue(lastname)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPinyinlastname() {
/*  355 */     return ((String)getRowValue(pinyinlastname)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSex() {
/*  365 */     return ((String)getRowValue(sex)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEmail() {
/*  374 */     return ((String)getRowValue(email)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourcetype() {
/*  383 */     return ((String)getRowValue(resourcetype)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLocation() {
/*  392 */     return ((String)getRowValue(locationid)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepartmentID() {
/*  405 */     return ("" + Util.getIntValue((String)getRowValue(departmentid), 0)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubCompanyID() {
/*  414 */     return ("" + Util.getIntValue((String)getRowValue(subcompanyid), 0)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCostcenterID() {
/*  423 */     return ("" + Util.getIntValue((String)getRowValue(costcenterid), 0)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJobTitle() {
/*  432 */     return ((String)getRowValue(jobtitleid)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManagerID() {
/*  441 */     return ((String)getRowValue(managerid)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManagersIDs() {
/*  450 */     return ((String)getRowValue(managerstr)).trim();
/*      */   }
/*      */   
/*      */   public String getMessagerUrls() {
/*  454 */     String str = ((String)getRowValue(messagerurl)).trim();
/*      */     
/*  456 */     if ("".equals(str))
/*  457 */       if (((String)getRowValue(sex)).trim().equals("1")) {
/*  458 */         str = "/messager/images/icon_w_wev8.jpg";
/*      */       } else {
/*  460 */         str = "/messager/images/icon_m_wev8.jpg";
/*      */       }  
/*  462 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManagersIDs(String paramString) {
/*  472 */     return ((String)getValue(managerstr, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getMessagerUrls(String paramString) {
/*  476 */     String str = ((String)getValue(messagerurl, paramString)).trim();
/*  477 */     if ("".equals(str)) {
/*  478 */       int i = Util.getIntValue(getResourceimageid(paramString));
/*  479 */       if (i > 0) {
/*  480 */         str = "/weaver/weaver.file.FileDownload?fileid=" + i;
/*      */       }
/*  482 */       else if (((String)getValue(sex, paramString)).trim().equals("1")) {
/*  483 */         str = "/messager/images/icon_w_wev8.jpg";
/*      */       } else {
/*  485 */         str = "/messager/images/icon_m_wev8.jpg";
/*      */       } 
/*      */     } 
/*  488 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getUserIconInfoStr(String paramString, User paramUser) {
/*  493 */     return JSONObject.toJSONString(getUserIconInfo(paramString, paramUser));
/*      */   }
/*      */   
/*      */   public Map<String, Object> getUserIconInfo(String paramString, User paramUser) {
/*  497 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/*  499 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  500 */       HrmUserIconSettingComInfo hrmUserIconSettingComInfo = new HrmUserIconSettingComInfo();
/*  501 */       boolean bool = false;
/*  502 */       String str1 = hrmUserIconSettingComInfo.getHeadformat();
/*  503 */       String str2 = "";
/*  504 */       String str3 = "";
/*  505 */       String str4 = "";
/*  506 */       String str5 = "";
/*  507 */       String str6 = "";
/*  508 */       String str7 = "";
/*  509 */       String str8 = "";
/*  510 */       String str9 = "";
/*  511 */       str4 = resourceComInfo.getMessagerUrls(paramString);
/*  512 */       str5 = StringUtil.vString(resourceComInfo.getSexs(paramString).trim(), "0");
/*  513 */       str6 = Util.null2String(Util.formatMultiLang(resourceComInfo.getLastname(paramString), paramUser.getLanguage() + ""));
/*  514 */       str7 = User.getLastname(str6);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  520 */       if (str4.indexOf("icon_w_wev8.jpg") > -1 || str4.indexOf("icon_m_wev8.jpg") > -1 || str4.indexOf("dummyContact.png") > -1) {
/*  521 */         str4 = "";
/*      */       }
/*  523 */       if (!paramString.equals("-99")) {
/*  524 */         bool = true;
/*  525 */         if (str5.equals("1")) {
/*  526 */           str2 = hrmUserIconSettingComInfo.getFheadbackcolor();
/*  527 */           str3 = hrmUserIconSettingComInfo.getFheadfontcolor();
/*  528 */           str8 = hrmUserIconSettingComInfo.getFmessagerurl();
/*      */         } else {
/*  530 */           str2 = hrmUserIconSettingComInfo.getMheadbackcolor();
/*  531 */           str3 = hrmUserIconSettingComInfo.getMheadfontcolor();
/*  532 */           str8 = hrmUserIconSettingComInfo.getMmessagerurl();
/*      */         } 
/*      */       } else {
/*      */         
/*  536 */         bool = true;
/*  537 */         str5 = "0";
/*  538 */         str2 = hrmUserIconSettingComInfo.getMheadbackcolor();
/*  539 */         str3 = hrmUserIconSettingComInfo.getMheadfontcolor();
/*  540 */         str6 = Util.null2String(SystemEnv.getHtmlLabelName(18611, paramUser.getLanguage()));
/*  541 */         str7 = User.getLastname(str6, Util.getIntValue(str1));
/*  542 */         str8 = "/cloudstore/resource/pc/com/images/anomous.png";
/*      */       } 
/*  544 */       if ("2".equals(str1)) {
/*  545 */         str1 = "0";
/*  546 */         if (str4.length() == 0) {
/*  547 */           str4 = str8;
/*      */         }
/*  549 */         if (str5.equals("1")) {
/*  550 */           str9 = hrmUserIconSettingComInfo.getFmessagerurl();
/*      */         } else {
/*  552 */           str9 = hrmUserIconSettingComInfo.getMmessagerurl();
/*      */         }
/*      */       
/*  555 */       } else if ("3".equals(str1)) {
/*  556 */         str1 = "2";
/*      */       } 
/*      */       
/*  559 */       hashMap.put("isDefault", Boolean.valueOf(bool));
/*  560 */       hashMap.put("headformat", str1);
/*  561 */       hashMap.put("gender", str5);
/*  562 */       hashMap.put("background", str2);
/*  563 */       hashMap.put("fontcolor", str3);
/*  564 */       hashMap.put("lastname", str6);
/*  565 */       hashMap.put("shortname", str7);
/*  566 */       hashMap.put("messagerurl", str4);
/*  567 */       hashMap.put("defaultmessagerurl", str9);
/*  568 */     } catch (Exception exception) {
/*  569 */       writeLog(exception);
/*      */     } 
/*  571 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAssistantID() {
/*  580 */     return ((String)getRowValue(assistantid)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJoblevel() {
/*  589 */     return ((String)getRowValue(joblevel)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevel() {
/*  600 */     String str = ((String)getRowValue(seclevel)).trim();
/*  601 */     if (str == null || str.trim().equals("")) {
/*  602 */       str = "0";
/*      */     }
/*  604 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevelTwo() {
/*  614 */     return ((String)getRowValue(seclevel)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStatus() {
/*  625 */     return ((String)getRowValue(status)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAccount() {
/*  636 */     return ((String)getRowValue(account)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMobile() {
/*  647 */     return ((String)getRowValue(mobile)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String getMobileshowtype() {
/*  659 */     return ((String)getRowValue(mobileshowtype)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSystemLanguage() {
/*  670 */     return User.getUserLang(Util.getIntValue(getResourceid())) + "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTelephone() {
/*  680 */     return ((String)getRowValue(telephone)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourcename(String paramString) {
/*  690 */     return StringUtil.replace(((String)getValue(lastname, paramString)).trim(), ",", "，");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String filterResourcename(String paramString1, String paramString2) {
/*  700 */     if ("".equals(paramString2) || "3".equals(paramString2))
/*  701 */       return StringUtil.replace(((String)getValue(lastname, paramString1)).trim(), ",", "，"); 
/*  702 */     int i = 0;
/*  703 */     switch (paramString2) {
/*      */       case "1":
/*  705 */         i = workcode;
/*      */         break;
/*      */       case "2":
/*  708 */         i = loginid;
/*      */         break;
/*      */     } 
/*  711 */     return StringUtil.replace(((String)getValue(i, paramString1)).trim(), ",", "，");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFirstname(String paramString) {
/*  721 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLoginID(String paramString) {
/*  731 */     return ((String)getValue(loginid, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWorkcode(String paramString) {
/*  741 */     return ((String)getValue(workcode, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLastname(String paramString) {
/*  751 */     return ((String)getValue(lastname, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPinyinlastname(String paramString) {
/*  762 */     return ((String)getValue(pinyinlastname, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPWD(String paramString) {
/*  772 */     return ((String)getValue(pwd, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSexs(String paramString) {
/*  782 */     return ((String)getValue(sex, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEmail(String paramString) {
/*  792 */     return ((String)getValue(email, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLastnameByEmail(String paramString) {
/*  803 */     RecordSet recordSet = new RecordSet();
/*  804 */     String str = "";
/*  805 */     recordSet.executeSql("select lastname from hrmresource where email='" + paramString + "'");
/*  806 */     if (recordSet.next()) {
/*  807 */       str = Util.null2String(recordSet.getString("lastname"));
/*      */     }
/*  809 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourcetype(String paramString) {
/*  819 */     return ((String)getValue(resourcetype, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLocationid(String paramString) {
/*  829 */     return ((String)getValue(locationid, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepartmentID(String paramString) {
/*  848 */     return ("" + Util.getIntValue((String)getValue(departmentid, paramString), 0)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubCompanyID(String paramString) {
/*  858 */     return ("" + Util.getIntValue((String)getValue(subcompanyid, paramString), 0)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCostcenterID(String paramString) {
/*  868 */     return ("" + Util.getIntValue((String)getValue(costcenterid, paramString), 0)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJobTitle(String paramString) {
/*  878 */     return ((String)getValue(jobtitleid, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManagerID(String paramString) {
/*  888 */     return ((String)getValue(managerid, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAssistantID(String paramString) {
/*  898 */     return ((String)getValue(assistantid, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJoblevel(String paramString) {
/*  908 */     return ((String)getValue(joblevel, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevel(String paramString) {
/*  918 */     String str = ((String)getValue(seclevel, paramString)).trim();
/*  919 */     if (str == null || str.trim().equals("")) {
/*  920 */       str = "0";
/*      */     }
/*  922 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevelTwo(String paramString) {
/*  932 */     return ((String)getValue(seclevel, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStatus(String paramString) {
/*  942 */     return ((String)getValue(status, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAccount(String paramString) {
/*  952 */     return ((String)getValue(account, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMobile(String paramString) {
/*  962 */     return ((String)getValue(mobile, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String getMobileshowtype(String paramString) {
/*  974 */     return ((String)getValue(mobileshowtype, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSystemLanguage(String paramString) {
/*  984 */     return User.getUserLang(Util.getIntValue(paramString)) + "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTelephone(String paramString) {
/*  996 */     return ((String)getValue(telephone, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getAccountType(String paramString) {
/* 1000 */     return ((String)getValue(accounttype, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getBelongTo(String paramString) {
/* 1004 */     return ((String)getValue(belongto, paramString)).trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMulResourcename(String paramString) {
/* 1014 */     String str1 = "";
/* 1015 */     String str2 = "";
/* 1016 */     paramString = paramString + ",";
/* 1017 */     for (byte b = 0; b < paramString.length(); b++) {
/* 1018 */       if (paramString.charAt(b) != ',') {
/* 1019 */         str2 = str2 + paramString.charAt(b);
/*      */       } else {
/* 1021 */         str1 = str1 + " " + getResourcename(str2);
/* 1022 */         str2 = "";
/*      */       } 
/*      */     } 
/* 1025 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMulResourcename1(String paramString) {
/* 1035 */     return getMulResourcename1(paramString, "&nbsp;&nbsp;");
/*      */   }
/*      */   
/*      */   public String getMulResourcename1(String paramString1, String paramString2) {
/* 1039 */     String str1 = "";
/* 1040 */     String str2 = "";
/* 1041 */     paramString1 = paramString1 + ",";
/* 1042 */     for (byte b = 0; b < paramString1.length(); b++) {
/* 1043 */       if (paramString1.charAt(b) != ',') {
/* 1044 */         str2 = str2 + paramString1.charAt(b);
/*      */       }
/* 1046 */       else if (!"".equals(str2)) {
/* 1047 */         if (str1.equals("")) {
/* 1048 */           str1 = "<a href=\"javascript:openhrm('" + str2 + "');\" onclick='pointerXY(event);'>" + getResourcename(str2) + "</a>";
/*      */         } else {
/* 1050 */           str1 = str1 + paramString2 + "<a href=\"javascript:openhrm('" + str2 + "');\" onclick='pointerXY(event);'>" + getResourcename(str2) + "</a>";
/*      */         } 
/* 1052 */         str2 = "";
/*      */       } 
/*      */     } 
/* 1055 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMulResourcename2(String paramString) {
/* 1065 */     String str1 = "";
/* 1066 */     String str2 = "";
/* 1067 */     paramString = paramString + ",";
/* 1068 */     for (byte b = 0; b < paramString.length(); b++) {
/* 1069 */       if (paramString.charAt(b) != ',') {
/* 1070 */         str2 = str2 + paramString.charAt(b);
/*      */       }
/* 1072 */       else if (!"".equals(str2)) {
/* 1073 */         str1 = str1 + "<a href='javascript:void(0)' onclick=openFullWindowForXtable('/hrm/resource/HrmResource.jsp?id=" + str2 + "')>" + getResourcename(str2) + "</a>&nbsp;";
/* 1074 */         str2 = "";
/*      */       } 
/*      */     } 
/* 1077 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isManager(int paramInt, String paramString) {
/* 1088 */     RecordSet recordSet = new RecordSet();
/* 1089 */     this.sql = "select managerstr from HrmResource where id = '" + paramString + "'";
/* 1090 */     recordSet.executeSql(this.sql);
/* 1091 */     while (recordSet.next()) {
/* 1092 */       String str1 = Util.null2String(recordSet.getString("managerstr"));
/* 1093 */       String str2 = "";
/* 1094 */       str1 = str1 + ",";
/* 1095 */       for (byte b = 0; b < str1.length(); b++) {
/* 1096 */         if (str1.charAt(b) != ',') {
/* 1097 */           str2 = str2 + str1.charAt(b);
/*      */         } else {
/* 1099 */           if (str2.equals("" + paramInt)) {
/* 1100 */             return true;
/*      */           }
/* 1102 */           str2 = "";
/*      */         } 
/*      */       } 
/*      */     } 
/* 1106 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isSysInfoView(int paramInt, String paramString) {
/* 1117 */     RecordSet recordSet = new RecordSet();
/* 1118 */     this.sql = "select hrmid from HrmInfoMaintenance where id = 1";
/* 1119 */     recordSet.executeSql(this.sql);
/* 1120 */     int i = 0;
/* 1121 */     while (recordSet.next()) {
/* 1122 */       i = Util.getIntValue(recordSet.getString("hrmid"));
/*      */     }
/* 1124 */     if (paramInt == i) {
/* 1125 */       return true;
/*      */     }
/* 1127 */     if (isNewResource(paramInt) && isSuperviser(paramInt, paramString)) {
/* 1128 */       return true;
/*      */     }
/* 1130 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isSysInfoView2(int paramInt, String paramString) {
/* 1141 */     RecordSet recordSet = new RecordSet();
/* 1142 */     this.sql = "select hrmid,hrmids from HrmInfoMaintenance where id = 1";
/* 1143 */     recordSet.executeSql(this.sql);
/* 1144 */     int i = 0;
/* 1145 */     String str = "";
/* 1146 */     while (recordSet.next()) {
/* 1147 */       i = Util.getIntValue(recordSet.getString("hrmid"));
/* 1148 */       str = Util.null2String(recordSet.getString("hrmids"));
/*      */     } 
/* 1150 */     if (paramInt == i) {
/* 1151 */       return true;
/*      */     }
/* 1153 */     String[] arrayOfString = str.split(",");
/* 1154 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1155 */       if (arrayOfString[b].equals(paramInt + "")) {
/* 1156 */         return true;
/*      */       }
/*      */     } 
/* 1159 */     if (isNewResource(paramInt) && isSuperviser(paramInt, paramString)) {
/* 1160 */       return true;
/*      */     }
/* 1162 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isFinInfoView(int paramInt, String paramString) {
/* 1174 */     RecordSet recordSet = new RecordSet();
/* 1175 */     this.sql = "select hrmid from HrmInfoMaintenance where id = 2";
/* 1176 */     recordSet.executeSql(this.sql);
/* 1177 */     int i = 0;
/* 1178 */     while (recordSet.next()) {
/* 1179 */       i = Util.getIntValue(recordSet.getString("hrmid"));
/*      */     }
/* 1181 */     if (paramInt == i) {
/* 1182 */       return true;
/*      */     }
/* 1184 */     if (isNewResource(paramInt) && isSuperviser(paramInt, paramString)) {
/* 1185 */       return true;
/*      */     }
/* 1187 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isFinInfoView2(int paramInt, String paramString) {
/* 1200 */     RecordSet recordSet = new RecordSet();
/* 1201 */     this.sql = "select hrmid,hrmids from HrmInfoMaintenance where id = 2";
/* 1202 */     recordSet.executeSql(this.sql);
/* 1203 */     int i = 0;
/* 1204 */     String str = "";
/* 1205 */     while (recordSet.next()) {
/* 1206 */       i = Util.getIntValue(recordSet.getString("hrmid"));
/* 1207 */       str = Util.null2String(recordSet.getString("hrmids"));
/*      */     } 
/* 1209 */     if (paramInt == i) {
/* 1210 */       return true;
/*      */     }
/* 1212 */     String[] arrayOfString = str.split(",");
/* 1213 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1214 */       if (arrayOfString[b].equals(paramInt + "")) {
/* 1215 */         return true;
/*      */       }
/*      */     } 
/* 1218 */     if (isNewResource(paramInt) && isSuperviser(paramInt, paramString)) {
/* 1219 */       return true;
/*      */     }
/* 1221 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isCapInfoView(int paramInt, String paramString) {
/* 1233 */     RecordSet recordSet = new RecordSet();
/* 1234 */     this.sql = "select hrmid from HrmInfoMaintenance where id = 3";
/* 1235 */     recordSet.executeSql(this.sql);
/* 1236 */     int i = 0;
/* 1237 */     while (recordSet.next()) {
/* 1238 */       i = Util.getIntValue(recordSet.getString("hrmid"));
/*      */     }
/* 1240 */     if (paramInt == i) {
/* 1241 */       return true;
/*      */     }
/* 1243 */     if (isNewResource(paramInt) && isSuperviser(paramInt, paramString)) {
/* 1244 */       return true;
/*      */     }
/* 1246 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isCapInfoView2(int paramInt, String paramString) {
/* 1259 */     RecordSet recordSet = new RecordSet();
/* 1260 */     this.sql = "select hrmid,hrmids from HrmInfoMaintenance where id = 3";
/* 1261 */     recordSet.executeSql(this.sql);
/* 1262 */     int i = 0;
/* 1263 */     String str = "";
/* 1264 */     while (recordSet.next()) {
/* 1265 */       i = Util.getIntValue(recordSet.getString("hrmid"));
/* 1266 */       str = Util.null2String(recordSet.getString("hrmids"));
/*      */     } 
/* 1268 */     if (paramInt == i) {
/* 1269 */       return true;
/*      */     }
/* 1271 */     String[] arrayOfString = str.split(",");
/* 1272 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1273 */       if (arrayOfString[b].equals(paramInt + "")) {
/* 1274 */         return true;
/*      */       }
/*      */     } 
/* 1277 */     if (isNewResource(paramInt) && isSuperviser(paramInt, paramString)) {
/* 1278 */       return true;
/*      */     }
/* 1280 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isSuperviser(int paramInt, String paramString) {
/* 1292 */     RecordSet recordSet = new RecordSet();
/* 1293 */     this.sql = "select hrmid from HrmInfoMaintenance where id = 10";
/* 1294 */     recordSet.executeSql(this.sql);
/* 1295 */     int i = 0;
/* 1296 */     while (recordSet.next()) {
/* 1297 */       i = Util.getIntValue(recordSet.getString("hrmid"));
/*      */     }
/* 1299 */     if (paramInt == i) {
/* 1300 */       return true;
/*      */     }
/* 1302 */     if (isCreaterOfResource(paramInt, paramString)) return true; 
/* 1303 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isCreaterOfResource(int paramInt, String paramString) {
/* 1314 */     RecordSet recordSet = new RecordSet();
/* 1315 */     this.sql = "select createrid from HrmResource where id = '" + paramString + "'";
/* 1316 */     recordSet.executeSql(this.sql);
/* 1317 */     int i = 0;
/* 1318 */     while (recordSet.next()) {
/* 1319 */       i = Util.getIntValue(recordSet.getString("createrid"));
/*      */     }
/* 1321 */     if (paramInt == i) {
/* 1322 */       return true;
/*      */     }
/* 1324 */     return false;
/*      */   }
/*      */   
/*      */   public boolean isFinish(String paramString) {
/* 1328 */     RecordSet recordSet = new RecordSet();
/* 1329 */     this.sql = "select * from HrmInfoStatus where itemid < 4 and hrmid = '" + paramString + "'";
/* 1330 */     recordSet.executeSql(this.sql);
/* 1331 */     while (recordSet.next()) {
/* 1332 */       String str = recordSet.getString("status");
/* 1333 */       if (str.equals("0")) {
/* 1334 */         return false;
/*      */       }
/*      */     } 
/* 1337 */     return true;
/*      */   }
/*      */   
/*      */   public boolean isNewResource(int paramInt) {
/* 1341 */     RecordSet recordSet = new RecordSet();
/* 1342 */     this.sql = "select status from HrmInfoStatus where hrmid='" + paramInt + "'";
/* 1343 */     recordSet.executeSql(this.sql);
/* 1344 */     while (recordSet.next()) {
/* 1345 */       String str = recordSet.getString("status");
/* 1346 */       if (str.equals("0")) {
/* 1347 */         return true;
/*      */       }
/*      */     } 
/* 1350 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getAbsenceDayTotal(String paramString1, String paramString2) {
/* 1361 */     String str = paramString2;
/* 1362 */     int i = 0;
/*      */     try {
/* 1364 */       RecordSet recordSet = new RecordSet();
/* 1365 */       this.sql = "select totalday from HrmScheduleMaintance where resourceid ='" + paramString1 + "' and diffid = " + str;
/* 1366 */       recordSet.executeSql(this.sql);
/* 1367 */       while (recordSet.next()) {
/* 1368 */         i += recordSet.getInt(1);
/*      */       }
/* 1370 */     } catch (Exception exception) {
/* 1371 */       this.lm.writeLog(exception);
/*      */     } 
/* 1373 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getAbsenceDayTotal(String paramString) {
/* 1383 */     int i = 0;
/*      */     try {
/* 1385 */       RecordSet recordSet = new RecordSet();
/* 1386 */       this.sql = "select totalday from HrmScheduleMaintance where resourceid ='" + paramString + "'";
/* 1387 */       recordSet.executeSql(this.sql);
/* 1388 */       while (recordSet.next()) {
/* 1389 */         i += recordSet.getInt(1);
/*      */       }
/* 1391 */     } catch (Exception exception) {
/* 1392 */       this.lm.writeLog(exception);
/*      */     } 
/* 1394 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private int subHour(String paramString1, String paramString2) {
/* 1405 */     if (paramString1.equals(paramString2)) {
/* 1406 */       return 0;
/*      */     }
/* 1408 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString1, ":");
/* 1409 */     ArrayList<String> arrayList2 = Util.TokenizerString(paramString2, ":");
/* 1410 */     int i = Util.getIntValue(arrayList1.get(0));
/* 1411 */     int j = Util.getIntValue(arrayList2.get(0));
/* 1412 */     return j - i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private int subMinute(String paramString1, String paramString2) {
/* 1423 */     if (paramString1.equals(paramString2)) {
/* 1424 */       return 0;
/*      */     }
/* 1426 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString1, ":");
/* 1427 */     ArrayList<String> arrayList2 = Util.TokenizerString(paramString2, ":");
/* 1428 */     int i = Util.getIntValue(arrayList1.get(1));
/* 1429 */     int j = Util.getIntValue(arrayList2.get(1));
/* 1430 */     return j - i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isWorkDay(String paramString1, String paramString2) {
/* 1441 */     Time time = new Time();
/* 1442 */     return time.isWorkDay(paramString1, Util.getIntValue(paramString2));
/*      */   }
/*      */   
/*      */   public void addResourceInfoCache(String paramString) {
/* 1446 */     addCache(paramString);
/* 1447 */     (new DecryptResourceComInfo()).addCache(paramString);
/* 1448 */     (new EncryptFieldViewScopeConfigComInfo()).removeCache();
/* 1449 */     (new KVResourceComInfo()).removeCache();
/* 1450 */     (new TransLatorComInfo()).removeCache();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void removeResourceCache() {
/* 1457 */     removeCache();
/*      */     try {
/* 1459 */       (new DecryptResourceComInfo()).removeCache();
/* 1460 */       (new ResourceBelongtoComInfo()).removeResourceBelongtoCache();
/* 1461 */       (new HrmUserSettingComInfo()).removeHrmUserSettingComInfoCache();
/* 1462 */       (new ResourceVirtualComInfo()).removeResourceVirtualCache();
/* 1463 */       (new User()).removeUserCache();
/* 1464 */       (new KQGroupMemberComInfo()).removeCache();
/* 1465 */       (new EncryptFieldViewScopeConfigComInfo()).removeCache();
/* 1466 */       (new KVResourceComInfo()).removeCache();
/* 1467 */       (new TransLatorComInfo()).removeCache();
/* 1468 */       (new ResourceDoaminComInfo()).removeCache();
/* 1469 */     } catch (Exception exception) {
/* 1470 */       writeLog(exception);
/*      */     } 
/*      */     try {
/* 1473 */       CheckUserRight checkUserRight = new CheckUserRight();
/* 1474 */       checkUserRight.removeMemberRoleCache();
/* 1475 */       (new RolemembersComInfo()).removeCache();
/* 1476 */     } catch (Exception exception) {
/* 1477 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateResourceInfoCache(String paramString) {
/* 1487 */     boolean bool = false;
/* 1488 */     String str = "";
/* 1489 */     if (Util.getIntValue(getAccountType(paramString)) > 0 && Util.getIntValue(getBelongTo(paramString)) > 0) {
/* 1490 */       bool = true;
/* 1491 */       str = getBelongTo(paramString);
/*      */     } 
/* 1493 */     updateCache(paramString);
/* 1494 */     if (!bool && Util.getIntValue(getAccountType(paramString)) > 0 && Util.getIntValue(getBelongTo(paramString)) > 0) {
/* 1495 */       bool = true;
/* 1496 */       str = getBelongTo(paramString);
/*      */     } 
/* 1498 */     if (bool) {
/*      */       try {
/* 1500 */         (new ResourceBelongtoComInfo()).updateCache(str);
/* 1501 */         HrmUserSettingComInfo hrmUserSettingComInfo = new HrmUserSettingComInfo();
/* 1502 */         String str1 = hrmUserSettingComInfo.getId(str);
/* 1503 */         hrmUserSettingComInfo.updateCache(str1);
/* 1504 */         (new KQGroupMemberComInfo()).updateCache(str1);
/* 1505 */       } catch (Exception exception) {
/* 1506 */         writeLog(exception);
/*      */       } 
/*      */     }
/*      */     try {
/* 1510 */       (new DecryptResourceComInfo()).updateCache(paramString);
/* 1511 */       (new ResourceBelongtoComInfo()).updateCache(paramString);
/* 1512 */       HrmUserSettingComInfo hrmUserSettingComInfo = new HrmUserSettingComInfo();
/* 1513 */       String str1 = hrmUserSettingComInfo.getId("" + paramString);
/* 1514 */       hrmUserSettingComInfo.updateCache(str1);
/* 1515 */       (new ResourceVirtualComInfo()).removeResourceVirtualCache();
/* 1516 */       (new User()).updateCache(paramString);
/* 1517 */       (new KQGroupMemberComInfo()).removeCache();
/* 1518 */       (new EncryptFieldViewScopeConfigComInfo()).removeCache();
/* 1519 */       (new TransLatorComInfo()).removeCache();
/* 1520 */     } catch (Exception exception) {
/* 1521 */       writeLog(exception);
/*      */     } 
/*      */     
/* 1524 */     (new KVResourceComInfo()).removeCache();
/* 1525 */     (new ResourceDoaminComInfo()).removeCache();
/*      */     try {
/* 1527 */       CheckUserRight checkUserRight = new CheckUserRight();
/* 1528 */       checkUserRight.removeMemberRoleCache();
/* 1529 */       (new RolemembersComInfo()).removeCache();
/* 1530 */     } catch (Exception exception) {
/* 1531 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void deleteResourceInfoCache(String paramString) {
/* 1541 */     boolean bool = false;
/* 1542 */     String str = "";
/* 1543 */     if (Util.getIntValue(getAccountType(paramString)) > 0 && Util.getIntValue(getBelongTo(paramString)) > 0) {
/* 1544 */       bool = true;
/* 1545 */       str = getBelongTo(paramString);
/*      */     } 
/* 1547 */     deleteCache(paramString);
/* 1548 */     (new DecryptResourceComInfo()).deleteCache(paramString);
/* 1549 */     if (!bool && Util.getIntValue(getAccountType(paramString)) > 0 && Util.getIntValue(getBelongTo(paramString)) > 0) {
/* 1550 */       bool = true;
/* 1551 */       str = getBelongTo(paramString);
/*      */     } 
/* 1553 */     if (bool) {
/*      */       try {
/* 1555 */         (new ResourceBelongtoComInfo()).deleteCache(str);
/* 1556 */         HrmUserSettingComInfo hrmUserSettingComInfo = new HrmUserSettingComInfo();
/* 1557 */         String str1 = hrmUserSettingComInfo.getId(str);
/* 1558 */         hrmUserSettingComInfo.deleteCache(str1);
/* 1559 */       } catch (Exception exception) {
/* 1560 */         writeLog(exception);
/*      */       } 
/*      */     }
/*      */     try {
/* 1564 */       (new ResourceBelongtoComInfo()).deleteCache(paramString);
/* 1565 */       HrmUserSettingComInfo hrmUserSettingComInfo = new HrmUserSettingComInfo();
/* 1566 */       String str1 = hrmUserSettingComInfo.getId("" + paramString);
/* 1567 */       hrmUserSettingComInfo.deleteCache(str1);
/* 1568 */     } catch (Exception exception) {
/* 1569 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getClientDetailModifier(String paramString1, String paramString2, String paramString3) {
/* 1636 */     if (paramString1 == null || paramString1.trim().equals("") || (
/* 1637 */       !"1".equals(paramString2) && !"2".equals(paramString2)))
/*      */     {
/* 1639 */       return "";
/*      */     }
/*      */     
/* 1642 */     RecordSet recordSet = new RecordSet();
/* 1643 */     String str = "";
/* 1644 */     if ("1".equals(paramString2))
/* 1645 */       return getResourcename(paramString1); 
/* 1646 */     if ("2".equals(paramString2)) {
/* 1647 */       str = "select name from CRM_CustomerInfo where id ='" + paramString1 + "'";
/*      */ 
/*      */ 
/*      */       
/* 1651 */       recordSet.executeSql(str);
/* 1652 */       if (recordSet.next()) {
/* 1653 */         return Util.null2String(recordSet.getString("name"));
/*      */       }
/*      */     } 
/*      */     
/* 1657 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isLowLevel(int paramInt1, int paramInt2) {
/* 1670 */     boolean bool = false;
/* 1671 */     RecordSet recordSet = new RecordSet();
/* 1672 */     recordSet.executeSql("select  count(id) from HrmResource where managerid=" + paramInt2 + " and id=" + paramInt1);
/* 1673 */     if (recordSet.next()) {
/* 1674 */       int i = recordSet.getInt(1);
/* 1675 */       bool = (i == 1) ? true : false;
/*      */     } 
/* 1677 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManagers(String paramString) {
/* 1687 */     String str = "";
/* 1688 */     RecordSet recordSet = new RecordSet();
/* 1689 */     recordSet.executeSql("select managerstr from HrmResource where id=" + paramString);
/* 1690 */     if (recordSet.next()) {
/* 1691 */       str = Util.null2String(recordSet.getString(1));
/*      */     }
/* 1693 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String isOnline(String paramString) {
/* 1703 */     if (HrmUserVarify.isUserOnline(paramString)) {
/* 1704 */       return "<img src='" + GCONST.getContextPath() + "/images/State_LoggedOn_wev8.gif'/>";
/*      */     }
/* 1706 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String accounttype(String paramString) {
/* 1716 */     if (Util.null2String(paramString).equals("") || Util.null2String(paramString).equals("0")) {
/* 1717 */       return "<img src='/hrm/images/accounttype0_wev8.png' style='margin-left: -5px;'/>";
/*      */     }
/* 1719 */     return "<img src='/hrm/images/accounttype1_wev8.png' style='margin-left: -5px;'/>";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap getUnderlining() {
/* 1728 */     if (underlinings == null) {
/* 1729 */       underlinings = getUnderliningCache();
/*      */     }
/* 1731 */     return underlinings;
/*      */   }
/*      */   
/* 1734 */   public static HashMap underlinings = null;
/*      */   
/*      */   public HashMap getUnderliningCache() {
/* 1737 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/* 1739 */       setTofirstRow();
/* 1740 */       while (next()) {
/* 1741 */         String str1 = getResourceid();
/* 1742 */         String str2 = getManagersIDs();
/* 1743 */         String[] arrayOfString = Util.TokenizerString2(str2, ",");
/* 1744 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 1745 */           if (!arrayOfString[b].equals(""))
/* 1746 */             if (hashMap.containsKey(arrayOfString[b])) {
/* 1747 */               String str = (String)hashMap.get(arrayOfString[b]);
/* 1748 */               if (("," + str + ",").indexOf("," + str1 + ",") == -1)
/*      */               {
/* 1750 */                 hashMap.put(arrayOfString[b], str + "," + str1); } 
/*      */             } else {
/* 1752 */               hashMap.put(arrayOfString[b], "," + str1);
/*      */             }  
/*      */         } 
/*      */       } 
/* 1756 */     } catch (Exception exception) {
/* 1757 */       this.lm.writeLog(exception);
/*      */     } 
/* 1759 */     return hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAllManagerByUserId(String paramString) {
/* 1769 */     String str = Util.null2String(getManagersIDs(paramString));
/* 1770 */     if (str.length() == 0) return ""; 
/* 1771 */     if (str.startsWith(",")) str = str.substring(1, str.length()); 
/* 1772 */     if (str.endsWith(",")) str = str.substring(0, str.length() - 1); 
/* 1773 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getUnderliningByUserId(String paramString) {
/* 1783 */     String str = "";
/* 1784 */     RecordSet recordSet = new RecordSet();
/* 1785 */     recordSet.executeSql("select id from HrmResource where managerstr like '%," + paramString + ",%'");
/* 1786 */     while (recordSet.next()) {
/* 1787 */       if (str.length() > 0) str = str + ","; 
/* 1788 */       str = str + Util.null2String(recordSet.getString("id"));
/*      */     } 
/* 1790 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getUserIdByLoginId(String paramString) {
/* 1795 */     String str = "";
/* 1796 */     RecordSet recordSet = new RecordSet();
/* 1797 */     recordSet.executeSql("select id from HrmResource where loginid='" + paramString + "'");
/* 1798 */     if (recordSet.next()) {
/* 1799 */       str = Util.null2String(recordSet.getString(1));
/*      */     }
/* 1801 */     return str;
/*      */   }
/*      */   
/*      */   public String getMutiResourceLink(String paramString) throws Exception {
/* 1805 */     String str1 = "";
/* 1806 */     String str2 = "";
/* 1807 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1808 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 1809 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 1810 */       str2 = arrayList.get(b);
/* 1811 */       str1 = str1 + "<a href=javascript:openFullWindowForXtable('/hrm/resource/HrmResource.jsp?id=" + str2 + "')> " + resourceComInfo.getResourcename(str2) + "</a> ";
/*      */     } 
/* 1813 */     return str1;
/*      */   }
/*      */   
/*      */   public String getLastnameAllStatus(String paramString) {
/* 1817 */     String str = "";
/* 1818 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 1819 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1820 */       str = str + getLastname(arrayOfString[b]) + ",";
/*      */     }
/* 1822 */     if (!"".equals(str)) str = str.substring(0, str.length() - 1); 
/* 1823 */     return str;
/*      */   }
/*      */   
/*      */   public String getLastnames(String paramString) {
/* 1827 */     String str = "";
/* 1828 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 1829 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1830 */       String str1 = getStatus(arrayOfString[b]);
/* 1831 */       if (str1.equals("0") || str1.equals("1") || str1.equals("2") || str1.equals("3") || str1.equals("5"))
/* 1832 */         str = str + getLastname(arrayOfString[b]) + ","; 
/*      */     } 
/* 1834 */     if (!"".equals(str)) str = str.substring(0, str.length() - 1); 
/* 1835 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getLastnamesForEdit(String paramString) throws Exception {
/* 1840 */     JSONArray jSONArray = new JSONArray();
/* 1841 */     String str = "";
/* 1842 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1843 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 1844 */     for (byte b = 0; arrayOfString != null && b < arrayOfString.length; b++) {
/* 1845 */       String str1 = arrayOfString[b];
/*      */       
/* 1847 */       str = "<a href=\"javaScript:openFullWindowHaveBar('/hrm/resource/HrmResource.jsp?id=" + str1 + "')\">" + resourceComInfo.getResourcename(str1) + "</a> &nbsp;";
/* 1848 */       JSONObject jSONObject = new JSONObject();
/* 1849 */       jSONObject.put("browserValue", str1);
/* 1850 */       jSONObject.put("browserSpanValue", str);
/* 1851 */       jSONArray.add(jSONObject);
/*      */     } 
/* 1853 */     return jSONArray.toString();
/*      */   }
/*      */ 
/*      */   
/*      */   public String getLastnamesForSimpleHrm(String paramString) throws Exception {
/* 1858 */     JSONArray jSONArray = new JSONArray();
/* 1859 */     String str = "";
/* 1860 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1861 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 1862 */     for (byte b = 0; arrayOfString != null && b < arrayOfString.length; b++) {
/* 1863 */       String str1 = Util.null2String(arrayOfString[b]);
/* 1864 */       if (str1.equals("0")) str1 = "";
/*      */       
/* 1866 */       str = "<a onclick=\"pointerXY(event)\" href='javaScript:openhrm(" + str1 + ");'>" + resourceComInfo.getResourcename(str1) + "</a> &nbsp;";
/* 1867 */       JSONObject jSONObject = new JSONObject();
/* 1868 */       jSONObject.put("browserValue", str1);
/* 1869 */       jSONObject.put("browserSpanValue", str);
/* 1870 */       jSONArray.add(jSONObject);
/*      */     } 
/* 1872 */     return jSONArray.toString();
/*      */   }
/*      */ 
/*      */   
/*      */   public String getSexName(String paramString) {
/* 1877 */     return getSexName(paramString, "7");
/*      */   }
/*      */ 
/*      */   
/*      */   public String getSexName(String paramString1, String paramString2) {
/* 1882 */     int i = StringUtil.parseToInt(paramString2, 7);
/* 1883 */     if (Util.null2String(paramString1).equals("0"))
/* 1884 */       return SystemEnv.getHtmlLabelName(28473, i); 
/* 1885 */     if (Util.null2String(paramString1).equals("1")) {
/* 1886 */       return SystemEnv.getHtmlLabelName(28474, i);
/*      */     }
/* 1888 */     return "";
/*      */   }
/*      */   
/*      */   public String getMobileShow1(String paramString1, String paramString2) {
/* 1892 */     User user = new User();
/* 1893 */     String str1 = paramString2.split("\\+")[0];
/* 1894 */     String str2 = paramString2.split("\\+")[1];
/* 1895 */     user.setUid(Integer.parseInt(str2));
/* 1896 */     if (str2.equals("1")) user.setLoginid("sysadmin"); 
/* 1897 */     return getMobileShow(String.valueOf(str1), user);
/*      */   }
/*      */   
/*      */   public String getMobileShow(String paramString1, String paramString2) {
/* 1901 */     User user = new User();
/* 1902 */     user.setUid(Integer.parseInt(paramString2));
/* 1903 */     if (paramString2.equals("1")) user.setLoginid("sysadmin"); 
/* 1904 */     return getMobileShow(String.valueOf(paramString1), user);
/*      */   }
/*      */   
/* 1907 */   private static final Pattern p = Pattern.compile("^(\\d{3})(\\d{4})(\\d{4})$");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String formatMobile(String paramString) {
/* 1916 */     if (StringUtils.isBlank(paramString)) return ""; 
/* 1917 */     Matcher matcher = p.matcher(paramString);
/* 1918 */     if (matcher.find())
/*      */     {
/* 1920 */       return matcher.group(1).concat("-")
/* 1921 */         .concat(matcher.group(2))
/* 1922 */         .concat("-")
/* 1923 */         .concat(matcher.group(3));
/*      */     }
/*      */ 
/*      */     
/* 1927 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean showCommonPrivacy(String paramString1, String paramString2, String paramString3) throws Exception {
/* 1938 */     boolean bool = true;
/*      */     
/* 1940 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1941 */     int i = 0, j = 0;
/* 1942 */     int k = Util.getIntValue(paramString1, 0);
/*      */     
/* 1944 */     i = Util.getIntValue(resourceComInfo.getSeclevel(paramString2), 0);
/*      */ 
/*      */     
/* 1947 */     if (paramString2.equalsIgnoreCase(paramString3)) {
/* 1948 */       return bool;
/*      */     }
/*      */     
/* 1951 */     if (k == 2) {
/*      */       
/* 1953 */       bool = false;
/* 1954 */     } else if (k == 3) {
/*      */       
/* 1956 */       j = Util.getIntValue(resourceComInfo.getSeclevel(paramString3), 0);
/* 1957 */       if (i > j) bool = false;
/*      */     
/*      */     } 
/*      */ 
/*      */     
/* 1962 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMobileShow(String paramString, User paramUser) {
/* 1973 */     int i = paramUser.getUID();
/* 1974 */     String str1 = Util.null2String(getMobile(paramString));
/* 1975 */     boolean bool = true;
/*      */     
/* 1977 */     String str2 = "mobile";
/* 1978 */     UserPrivacyComInfo userPrivacyComInfo = new UserPrivacyComInfo();
/* 1979 */     PrivacyComInfo privacyComInfo = new PrivacyComInfo();
/* 1980 */     Map map1 = privacyComInfo.getMapShowSets();
/* 1981 */     Map map2 = privacyComInfo.getMapShowTypeDefaults();
/*      */ 
/*      */     
/*      */     try {
/* 1985 */       if (map1 != null && map1.get(str2) != null) {
/* 1986 */         String str3 = Util.null2String((String)map1.get(str2));
/* 1987 */         String str4 = Util.null2String((String)map2.get(str2));
/*      */         
/* 1989 */         if (str3.equals("1")) {
/* 1990 */           String str5 = paramString + "__" + str2;
/* 1991 */           String str6 = Util.null2String(userPrivacyComInfo.getPvalue(str5));
/*      */           
/* 1993 */           if (str6.length() > 0) {
/* 1994 */             bool = showCommonPrivacy(str6, "" + paramString, "" + i);
/*      */           } else {
/* 1996 */             bool = showCommonPrivacy(str4, "" + paramString, "" + i);
/*      */           } 
/*      */         } else {
/* 1999 */           bool = showCommonPrivacy(str4, "" + paramString, "" + i);
/*      */         } 
/*      */       } 
/* 2002 */     } catch (Exception exception) {
/* 2003 */       exception.printStackTrace();
/* 2004 */       writeLog(exception);
/*      */     } 
/*      */     
/* 2007 */     if (!bool && str1.length() > 0) {
/* 2008 */       if (str1.startsWith("desensitization__")) {
/* 2009 */         str1 = EncryptConfigBiz.getDecryptData(str1);
/*      */       }
/*      */       
/* 2012 */       if (str1.length() <= 4) {
/* 2013 */         str1 = "****";
/*      */       }
/* 2015 */       str1 = str1.substring(0, str1.length() - 4) + "****";
/*      */     } 
/* 2017 */     return formatMobile(str1);
/*      */   }
/*      */   
/*      */   public ArrayList<String> getResourceOperate(String paramString1, String paramString2, String paramString3) {
/* 2021 */     ArrayList<String> arrayList = new ArrayList();
/* 2022 */     String str = paramString3.split(":")[0];
/*      */     
/* 2024 */     if (paramString2.equals("true")) {
/* 2025 */       arrayList.add("true");
/*      */     } else {
/* 2027 */       arrayList.add("false");
/*      */     } 
/*      */     
/* 2030 */     if (str.equals("true")) {
/* 2031 */       arrayList.add("true");
/*      */     } else {
/* 2033 */       arrayList.add("false");
/*      */     } 
/* 2035 */     return arrayList;
/*      */   }
/*      */   
/*      */   public static String getStatusName(String paramString1, String paramString2) {
/* 2039 */     String str = "";
/* 2040 */     int i = Util.getIntValue(paramString1);
/* 2041 */     int j = Util.getIntValue(paramString2);
/* 2042 */     switch (i)
/*      */     { case 0:
/* 2044 */         str = SystemEnv.getHtmlLabelName(15710, j);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 2074 */         return str;case 1: str = SystemEnv.getHtmlLabelName(15711, j); return str;case 2: str = SystemEnv.getHtmlLabelName(480, j); return str;case 3: str = SystemEnv.getHtmlLabelName(15844, j); return str;case 4: str = SystemEnv.getHtmlLabelName(6094, j); return str;case 5: str = SystemEnv.getHtmlLabelName(6091, j); return str;case 6: str = SystemEnv.getHtmlLabelName(6092, j); return str;case 7: str = SystemEnv.getHtmlLabelName(2245, j); return str;case 9: str = SystemEnv.getHtmlLabelName(332, j); return str; }  str = SystemEnv.getHtmlLabelName(1831, j); return str;
/*      */   }
/*      */   
/*      */   public static String getStatusName(int paramInt, User paramUser) {
/* 2078 */     return getStatusName("" + paramInt, "" + paramUser.getLanguage());
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCreatedate(String paramString) {
/* 2084 */     return ((String)getValue(createdate, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getResourceimageid(String paramString) {
/* 2088 */     return ((String)getValue(resourceimageid, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getClassification() {
/* 2092 */     return (String)getRowValue(classification);
/*      */   }
/*      */   
/*      */   public String getClassification(String paramString) {
/* 2096 */     return (String)getValue(classification, paramString);
/*      */   }
/*      */   
/*      */   public String getEncKey() {
/* 2100 */     return (String)getRowValue(encKey);
/*      */   }
/*      */   
/*      */   public String getEncKey(String paramString) {
/* 2104 */     return (String)getValue(encKey, paramString);
/*      */   }
/*      */   
/*      */   public String getCrc() {
/* 2108 */     return (String)getRowValue(crc);
/*      */   }
/*      */   
/*      */   public String getCrc(String paramString) {
/* 2112 */     return (String)getValue(crc, paramString);
/*      */   }
/*      */   
/*      */   public String getCompanyStartDate() {
/* 2116 */     return (String)getRowValue(companyStartDate);
/*      */   }
/*      */   
/*      */   public String getCompanyStartDate(String paramString) {
/* 2120 */     return (String)getValue(companyStartDate, paramString);
/*      */   }
/*      */   
/*      */   public List<Map> getResource(String paramString, List<? extends CharSequence> paramList) {
/* 2124 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 2125 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2130 */     String str = "select id, " + String.join(",", paramList) + ", dsporder from HrmResource where " + Tools.getOracleSQLIn(paramString, "id");
/*      */     try {
/* 2132 */       RecordSet recordSet = new RecordSet();
/* 2133 */       recordSet.executeQuery(str, new Object[0]);
/* 2134 */       String[] arrayOfString = recordSet.getColumnName();
/* 2135 */       while (recordSet.next()) {
/* 2136 */         hashMap = new HashMap<>();
/* 2137 */         for (String str1 : arrayOfString) {
/* 2138 */           hashMap.put(str1.toLowerCase(), recordSet.getString(str1));
/*      */         }
/* 2140 */         arrayList.add(hashMap);
/*      */       } 
/* 2142 */     } catch (Exception exception) {}
/*      */ 
/*      */     
/* 2145 */     return (List)arrayList;
/*      */   }
/*      */   
/*      */   public String getWorkStartDate() {
/* 2149 */     return ((String)getRowValue(workStartDate)).trim();
/*      */   }
/*      */   
/*      */   public String getWorkStartDate(String paramString) {
/* 2153 */     return ((String)getValue(workStartDate, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getEndDate() {
/* 2157 */     return ((String)getRowValue(enddate)).trim();
/*      */   }
/*      */   
/*      */   public String getEndDate(String paramString) {
/* 2161 */     return ((String)getValue(enddate, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getJobCall() {
/* 2165 */     return ((String)getRowValue(jobCall)).trim();
/*      */   }
/*      */   
/*      */   public String getJobCall(String paramString) {
/* 2169 */     return ((String)getValue(jobCall, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getMobileCall() {
/* 2173 */     return ((String)getRowValue(mobileCall)).trim();
/*      */   }
/*      */   
/*      */   public String getMobileCall(String paramString) {
/* 2177 */     return ((String)getValue(mobileCall, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getFax() {
/* 2181 */     return ((String)getRowValue(fax)).trim();
/*      */   }
/*      */   
/*      */   public String getFax(String paramString) {
/* 2185 */     return ((String)getValue(fax, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getJobActivityDesc() {
/* 2189 */     return ((String)getRowValue(jobactivitydesc)).trim();
/*      */   }
/*      */   
/*      */   public String getJobActivityDesc(String paramString) {
/* 2193 */     return ((String)getValue(jobactivitydesc, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getWorkroom() {
/* 2197 */     return ((String)getRowValue(workroom)).trim();
/*      */   }
/*      */   
/*      */   public String getWorkroom(String paramString) {
/* 2201 */     return ((String)getValue(workroom, paramString)).trim();
/*      */   }
/*      */   
/*      */   public String getDsporder() {
/* 2205 */     return ((String)getRowValue(dsporder)).trim();
/*      */   }
/*      */   
/*      */   public String getDsporder(String paramString) {
/* 2209 */     return ((String)getValue(dsporder, paramString)).trim();
/*      */   }
/*      */   
/*      */   public boolean isAdmin() {
/* 2213 */     String str = ((String)getRowValue(isAdmin)).trim();
/* 2214 */     if ("1".equals(str)) {
/* 2215 */       return true;
/*      */     }
/* 2217 */     return false;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean isAdmin(String paramString) {
/* 2222 */     String str = ((String)getValue(isAdmin, paramString)).trim();
/* 2223 */     if ("1".equals(str)) {
/* 2224 */       return true;
/*      */     }
/* 2226 */     return false;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/ResourceComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */