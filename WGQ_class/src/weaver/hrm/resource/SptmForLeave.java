/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.schedule.HrmAnnualManagement;
/*     */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SptmForLeave
/*     */ {
/*     */   public String getLeaveType(String paramString1, String paramString2) {
/*  16 */     RecordSet recordSet = new RecordSet();
/*  17 */     String str1 = "";
/*  18 */     String str2 = "";
/*  19 */     String str3 = "";
/*  20 */     String str4 = "select * from workflow_billfield where billid = 180 and (fieldname = 'leaveType' or fieldname = 'otherLeaveType')";
/*  21 */     recordSet.executeSql(str4);
/*  22 */     while (recordSet.next()) {
/*  23 */       if (recordSet.getString("fieldname").toLowerCase().equals("leavetype")) str1 = recordSet.getString("id"); 
/*  24 */       if (recordSet.getString("fieldname").toLowerCase().equals("otherleavetype")) str2 = recordSet.getString("id");
/*     */     
/*     */     } 
/*  27 */     if (Util.getIntValue(paramString1, 0) < 4) {
/*  28 */       str4 = "select * from workflow_SelectItem where fieldid = " + str1 + " and selectvalue = " + paramString1;
/*  29 */       recordSet.executeSql(str4);
/*  30 */       if (recordSet.next()) str3 = Util.null2String(recordSet.getString("selectname")); 
/*     */     } else {
/*  32 */       str4 = "select * from workflow_SelectItem where fieldid = " + str2 + " and selectvalue = " + paramString2;
/*  33 */       recordSet.executeSql(str4);
/*  34 */       if (recordSet.next()) str3 = Util.null2String(recordSet.getString("selectname"));
/*     */     
/*     */     } 
/*  37 */     return str3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLeaveTypeColor(String paramString1, String paramString2) {
/*  47 */     RecordSet recordSet = new RecordSet();
/*  48 */     String str1 = "";
/*  49 */     String str2 = "";
/*  50 */     String str3 = "";
/*  51 */     String str4 = Util.TokenizerString2(paramString2, "+")[0];
/*  52 */     String str5 = Util.TokenizerString2(paramString2, "+")[1];
/*  53 */     String str6 = "FF0000";
/*  54 */     String str7 = "";
/*  55 */     HrmAnnualManagement hrmAnnualManagement = new HrmAnnualManagement();
/*     */     try {
/*  57 */       str5 = HrmAnnualManagement.getLeaveColor(str5);
/*  58 */     } catch (Exception exception) {
/*  59 */       str5 = "";
/*     */     } 
/*  61 */     String str8 = "select * from workflow_billfield where billid = 180 and (fieldname = 'leaveType' or fieldname = 'otherLeaveType')";
/*  62 */     recordSet.executeSql(str8);
/*  63 */     while (recordSet.next()) {
/*  64 */       if (recordSet.getString("fieldname").toLowerCase().equals("leavetype")) str1 = recordSet.getString("id"); 
/*  65 */       if (recordSet.getString("fieldname").toLowerCase().equals("otherleavetype")) str2 = recordSet.getString("id");
/*     */     
/*     */     } 
/*  68 */     if (Util.getIntValue(paramString1, 0) < 4) {
/*  69 */       str8 = "select * from workflow_SelectItem where fieldid = " + str1 + " and selectvalue = " + paramString1;
/*  70 */       recordSet.executeSql(str8);
/*  71 */       if (recordSet.next()) str3 = Util.null2String(recordSet.getString("id")); 
/*     */     } else {
/*  73 */       str8 = "select * from workflow_SelectItem where fieldid = " + str2 + " and selectvalue = " + str4;
/*  74 */       recordSet.executeSql(str8);
/*  75 */       if (recordSet.next()) str3 = Util.null2String(recordSet.getString("id"));
/*     */     
/*     */     } 
/*  78 */     if (!str5.equals("")) {
/*  79 */       str8 = "select * from hrmleavetypecolor where itemid = " + str3 + " and subcompanyid = " + str5;
/*  80 */       recordSet.executeSql(str8);
/*  81 */       if (recordSet.next()) str6 = recordSet.getString("color");
/*     */     
/*     */     } 
/*  84 */     if (Util.null2String(str6).equals("")) str6 = "FF0000"; 
/*  85 */     str7 = "<span style='width:20px;background-color:" + str6 + "'>&nbsp;&nbsp;&nbsp;&nbsp;</span>";
/*  86 */     return str7;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTime(String paramString1, String paramString2) {
/*  97 */     return paramString1 + "　" + paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestName(String paramString) throws Exception {
/* 107 */     WorkflowRequestComInfo workflowRequestComInfo = new WorkflowRequestComInfo();
/* 108 */     String str = workflowRequestComInfo.getRequestName(paramString);
/* 109 */     str = "<a href=javascript:openFullWindowHaveBar('/workflow/request/ViewRequest.jsp?requestid=" + paramString + "') target='_self'>" + str + "</a>";
/* 110 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLeaveTypeColor(String paramString1, String paramString2, String paramString3) {
/* 121 */     RecordSet recordSet = new RecordSet();
/* 122 */     String str1 = "";
/* 123 */     String str2 = "";
/* 124 */     String str3 = "";
/*     */     
/* 126 */     String str4 = "FF0000";
/*     */     
/* 128 */     HrmAnnualManagement hrmAnnualManagement = new HrmAnnualManagement();
/*     */     try {
/* 130 */       paramString3 = HrmAnnualManagement.getLeaveColor(paramString3);
/* 131 */     } catch (Exception exception) {
/* 132 */       paramString3 = "";
/*     */     } 
/* 134 */     String str5 = "select * from workflow_billfield where billid = 180 and (fieldname = 'leaveType' or fieldname = 'otherLeaveType')";
/* 135 */     recordSet.executeSql(str5);
/* 136 */     while (recordSet.next()) {
/* 137 */       if (recordSet.getString("fieldname").toLowerCase().equals("leavetype")) str1 = recordSet.getString("id"); 
/* 138 */       if (recordSet.getString("fieldname").toLowerCase().equals("otherleavetype")) str2 = recordSet.getString("id");
/*     */     
/*     */     } 
/* 141 */     if (Util.getIntValue(paramString1, 0) < 4) {
/* 142 */       str5 = "select * from workflow_SelectItem where fieldid = " + str1 + " and selectvalue = " + paramString1;
/* 143 */       recordSet.executeSql(str5);
/* 144 */       if (recordSet.next()) str3 = Util.null2String(recordSet.getString("id")); 
/*     */     } else {
/* 146 */       str5 = "select * from workflow_SelectItem where fieldid = " + str2 + " and selectvalue = " + paramString2;
/* 147 */       recordSet.executeSql(str5);
/* 148 */       if (recordSet.next()) str3 = Util.null2String(recordSet.getString("id"));
/*     */     
/*     */     } 
/* 151 */     if (!paramString3.equals("")) {
/* 152 */       str5 = "select * from hrmleavetypecolor where itemid = " + str3 + " and subcompanyid = " + paramString3;
/* 153 */       recordSet.executeSql(str5);
/* 154 */       if (recordSet.next()) str4 = recordSet.getString("color"); 
/*     */     } 
/* 156 */     if (Util.null2String(str4).equals("")) str4 = "FF0000";
/*     */     
/* 158 */     return str4;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/SptmForLeave.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */