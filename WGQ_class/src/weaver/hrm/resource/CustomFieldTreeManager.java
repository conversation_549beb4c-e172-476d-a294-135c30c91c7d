/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import com.engine.common.entity.EncryptFieldEntity;
/*     */ import com.engine.encrypt.biz.EncryptFieldConfigComInfo;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.docs.CustomFieldManager;
/*     */ import weaver.encrypt.EncryptUtil;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.definedfield.HrmFieldGroupComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomFieldTreeManager
/*     */ {
/*  31 */   private RecordSet rs = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isE9 = false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int addTreeField(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2) {
/*  43 */     RecordSet recordSet = new RecordSet();
/*  44 */     int i = 0;
/*  45 */     String str = "select max(id) from cus_treeform";
/*  46 */     recordSet.executeSql(str);
/*  47 */     if (recordSet.next()) {
/*  48 */       i = Util.getIntValue(recordSet.getString(1), 0);
/*     */     }
/*  50 */     str = "insert into cus_treeform(scope, formlabel, id, parentid, viewtype, scopeorder) values('" + paramString1 + "','" + paramString2 + "'," + (i + 1) + "," + paramInt1 + ",'" + paramString3 + "'," + paramInt2 + ")";
/*     */     
/*  52 */     recordSet.executeSql(str);
/*  53 */     return i + 1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void editTreeField(int paramInt1, String paramString1, String paramString2, String paramString3, int paramInt2, int paramInt3) {
/*  66 */     RecordSet recordSet = new RecordSet();
/*  67 */     String str = "update cus_treeform set formlabel='" + paramString2 + "', viewtype='" + paramString3 + "', scopeorder=" + paramInt3 + " where scope='" + paramString1 + "' and id=" + paramInt1;
/*     */     
/*  69 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteTreeField(int paramInt, String paramString) {
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     String str = "delete from cus_treeform where scope='" + paramString + "' and id=" + paramInt;
/*  80 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCustomData(String paramString, int paramInt1, HttpServletRequest paramHttpServletRequest, int paramInt2) {
/*  91 */     String str1 = paramString;
/*  92 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/*  93 */       str1 = "HrmCustomFieldByInfoType"; 
/*  94 */     RecordSet recordSet = new RecordSet();
/*  95 */     CustomFieldManager customFieldManager = new CustomFieldManager(str1, paramInt1);
/*  96 */     customFieldManager.getCustomFields();
/*  97 */     String str2 = "insert into cus_fielddata";
/*  98 */     String str3 = "";
/*  99 */     String str4 = "";
/* 100 */     while (customFieldManager.next()) {
/* 101 */       str3 = str3 + ",field" + customFieldManager.getId();
/* 102 */       if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 103 */         .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 104 */         str4 = str4 + ",'" + Util.null2String(paramHttpServletRequest.getParameter("customfield" + customFieldManager.getId())) + "'"; continue;
/*     */       } 
/* 106 */       if (Util.null2String(paramHttpServletRequest.getParameter("customfield" + customFieldManager.getId())).equals("")) {
/* 107 */         str4 = str4 + ",null"; continue;
/*     */       } 
/* 109 */       str4 = str4 + "," + Util.null2String(paramHttpServletRequest.getParameter("customfield" + customFieldManager.getId()));
/*     */     } 
/*     */ 
/*     */     
/* 113 */     if (!str3.equals("")) {
/* 114 */       str3 = str3.substring(1);
/* 115 */       str4 = str4.substring(1);
/* 116 */       if (str4.contains("\n")) str4 = str4.replaceAll("\r\n", "<br/>"); 
/* 117 */       str2 = str2 + "(scope,scopeid,id," + str3 + ") values('" + paramString + "'," + paramInt1 + "," + paramInt2 + "," + str4 + ")";
/* 118 */       RecordSet recordSet1 = new RecordSet();
/* 119 */       recordSet1.executeSql("select id from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2);
/* 120 */       if (!recordSet1.next())
/*     */       {
/*     */         
/* 123 */         recordSet.executeSql(str2);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCustomData(String paramString, int paramInt1, FileUpload paramFileUpload, int paramInt2) {
/* 137 */     String str1 = paramString;
/* 138 */     EncryptUtil encryptUtil = new EncryptUtil();
/* 139 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 140 */       str1 = "HrmCustomFieldByInfoType"; 
/* 141 */     RecordSet recordSet = new RecordSet();
/* 142 */     CustomFieldManager customFieldManager = new CustomFieldManager(str1, paramInt1);
/* 143 */     customFieldManager.getCustomFields();
/* 144 */     String str2 = "insert into cus_fielddata";
/* 145 */     String str3 = "";
/* 146 */     String str4 = "";
/* 147 */     while (customFieldManager.next()) {
/* 148 */       String str5 = "field" + customFieldManager.getId();
/* 149 */       str3 = str3 + ",field" + customFieldManager.getId();
/* 150 */       String str6 = Util.null2String(paramFileUpload.getParameter("customfield" + customFieldManager.getId()));
/* 151 */       EncryptFieldEntity encryptFieldEntity = (new EncryptFieldConfigComInfo()).getFieldEncryptConfig("cus_fielddata", str5, "HrmCustomFieldByInfoType", "" + paramInt1);
/* 152 */       if (encryptFieldEntity != null && encryptFieldEntity.getIsEncrypt().equals("1"))
/*     */       {
/* 154 */         str6 = Util.null2String(encryptUtil.encryt("cus_fielddata", str5, paramString, "" + paramInt1, str6, str6));
/*     */       }
/* 156 */       if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 157 */         .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 158 */         str4 = str4 + ",'" + str6 + "'"; continue;
/*     */       } 
/* 160 */       if (Util.null2String(paramFileUpload.getParameter("customfield" + customFieldManager.getId())).equals("")) {
/* 161 */         str4 = str4 + ",null"; continue;
/*     */       } 
/* 163 */       str4 = str4 + "," + str6;
/*     */     } 
/*     */ 
/*     */     
/* 167 */     if (!str3.equals("")) {
/* 168 */       str3 = str3.substring(1);
/* 169 */       str4 = str4.substring(1);
/*     */       
/* 171 */       str2 = str2 + "(scope,scopeid,id," + str3 + ") values('" + paramString + "'," + paramInt1 + "," + paramInt2 + "," + str4 + ")";
/*     */       
/* 173 */       RecordSet recordSet1 = new RecordSet();
/* 174 */       recordSet1.executeQuery("select id from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2, new Object[0]);
/* 175 */       if (!recordSet1.next())
/*     */       {
/*     */         
/* 178 */         recordSet.executeUpdate(str2, new Object[0]);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCustomDataE9Add(String paramString, int paramInt1, FileUpload paramFileUpload, int paramInt2) {
/* 192 */     String str1 = paramString;
/* 193 */     String str2 = "" + paramInt1;
/* 194 */     if (paramInt1 == -1) str2 = "0"; 
/* 195 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 196 */       str1 = "HrmCustomFieldByInfoType"; 
/* 197 */     RecordSet recordSet = new RecordSet();
/* 198 */     CustomFieldManager customFieldManager = new CustomFieldManager(str1, paramInt1);
/* 199 */     customFieldManager.getCustomFields();
/* 200 */     String str3 = "insert into cus_fielddata";
/* 201 */     String str4 = "";
/* 202 */     String str5 = "";
/* 203 */     while (customFieldManager.next()) {
/* 204 */       str4 = str4 + ",field" + customFieldManager.getId();
/* 205 */       if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 206 */         .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 207 */         str5 = str5 + ",'" + Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId())) + "'"; continue;
/*     */       } 
/* 209 */       if (Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId())).equals("")) {
/* 210 */         str5 = str5 + ",null"; continue;
/*     */       } 
/* 212 */       str5 = str5 + "," + Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId()));
/*     */     } 
/*     */ 
/*     */     
/* 216 */     if (!str4.equals("")) {
/* 217 */       str4 = str4.substring(1);
/* 218 */       str5 = str5.substring(1);
/*     */       
/* 220 */       str3 = str3 + "(scope,scopeid,id," + str4 + ") values('" + paramString + "'," + paramInt1 + "," + paramInt2 + "," + str5 + ")";
/*     */       
/* 222 */       RecordSet recordSet1 = new RecordSet();
/* 223 */       recordSet1.executeSql("select id from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2);
/* 224 */       if (!recordSet1.next())
/*     */       {
/*     */         
/* 227 */         recordSet.executeSql(str3);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void editCustomData(String paramString, int paramInt1, HttpServletRequest paramHttpServletRequest, int paramInt2) {
/* 241 */     String str = paramString;
/* 242 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 243 */       str = "HrmCustomFieldByInfoType"; 
/* 244 */     RecordSet recordSet = new RecordSet();
/* 245 */     recordSet.executeSql("select id from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2);
/* 246 */     if (recordSet.next()) {
/* 247 */       CustomFieldManager customFieldManager = new CustomFieldManager(str, paramInt1);
/* 248 */       customFieldManager.getCustomFields();
/* 249 */       String str1 = "update cus_fielddata set ";
/* 250 */       String str2 = "";
/* 251 */       while (customFieldManager.next()) {
/* 252 */         if (!customFieldManager.isUse())
/* 253 */           continue;  str2 = str2 + ",field" + customFieldManager.getId() + "=";
/* 254 */         if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 255 */           .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 256 */           str2 = str2 + "'" + Util.null2String(paramHttpServletRequest.getParameter("customfield" + customFieldManager.getId())) + "'"; continue;
/*     */         } 
/* 258 */         if (Util.null2String(paramHttpServletRequest.getParameter("customfield" + customFieldManager.getId())).equals("")) {
/* 259 */           str2 = str2 + "null"; continue;
/*     */         } 
/* 261 */         str2 = str2 + Util.null2String(paramHttpServletRequest.getParameter("customfield" + customFieldManager.getId()));
/*     */       } 
/*     */ 
/*     */       
/* 265 */       if (!str2.equals("")) {
/* 266 */         str2 = str2.substring(1);
/* 267 */         if (str2.contains("\n")) str2 = str2.replaceAll("\r\n", "<br/>"); 
/* 268 */         str1 = str1 + str2 + " where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2;
/* 269 */         recordSet.executeSql(str1);
/*     */       } 
/*     */     } else {
/* 272 */       addCustomData(paramString, paramInt1, paramHttpServletRequest, paramInt2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void editCustomData(String paramString, int paramInt1, FileUpload paramFileUpload, int paramInt2) {
/* 285 */     String str = paramString;
/* 286 */     HrmFieldGroupComInfo hrmFieldGroupComInfo = new HrmFieldGroupComInfo();
/* 287 */     EncryptUtil encryptUtil = new EncryptUtil();
/* 288 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 289 */       str = "HrmCustomFieldByInfoType"; 
/* 290 */     RecordSet recordSet = new RecordSet();
/* 291 */     recordSet.executeSql("select id from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2);
/* 292 */     if (recordSet.next()) {
/* 293 */       CustomFieldManager customFieldManager = new CustomFieldManager(str, paramInt1);
/* 294 */       customFieldManager.getCustomFields();
/* 295 */       String str1 = "update cus_fielddata set ";
/* 296 */       String str2 = "";
/* 297 */       while (customFieldManager.next()) {
/* 298 */         if (!customFieldManager.isUse() || 
/* 299 */           !hrmFieldGroupComInfo.getIsShow("" + customFieldManager.getGroupId()).equals("1"))
/*     */           continue; 
/* 301 */         String str3 = "field" + customFieldManager.getId();
/* 302 */         String str4 = Util.null2String(paramFileUpload.getParameter("customfield" + customFieldManager.getId()));
/* 303 */         EncryptFieldEntity encryptFieldEntity = (new EncryptFieldConfigComInfo()).getFieldEncryptConfig("cus_fielddata", str3, "HrmCustomFieldByInfoType", "" + paramInt1);
/* 304 */         if (encryptFieldEntity != null && encryptFieldEntity.getIsEncrypt().equals("1"))
/*     */         {
/* 306 */           str4 = Util.null2String(encryptUtil.encryt("cus_fielddata", str3, paramString, "" + paramInt1, str4, str4));
/*     */         }
/* 308 */         str2 = str2 + "," + str3 + "=";
/* 309 */         if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 310 */           .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 311 */           str2 = str2 + "'" + str4 + "'"; continue;
/*     */         } 
/* 313 */         if (str4.equals("")) {
/* 314 */           str2 = str2 + "null"; continue;
/*     */         } 
/* 316 */         str2 = str2 + str4;
/*     */       } 
/*     */ 
/*     */       
/* 320 */       if (!str2.equals("")) {
/* 321 */         str2 = str2.substring(1);
/*     */         
/* 323 */         str1 = str1 + str2 + " where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2;
/* 324 */         recordSet.setNoAutoEncrypt(true);
/* 325 */         recordSet.executeUpdate(str1, new Object[0]);
/*     */       } 
/*     */     } else {
/* 328 */       addCustomData(paramString, paramInt1, paramFileUpload, paramInt2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void editCustomDataE9Add(String paramString, int paramInt1, FileUpload paramFileUpload, int paramInt2) {
/* 341 */     String str1 = paramString;
/* 342 */     String str2 = "" + paramInt1;
/* 343 */     if (paramInt1 == -1) str2 = "0";
/*     */     
/* 345 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 346 */       str1 = "HrmCustomFieldByInfoType"; 
/* 347 */     RecordSet recordSet = new RecordSet();
/* 348 */     recordSet.executeSql("select id from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2);
/* 349 */     if (recordSet.next()) {
/* 350 */       CustomFieldManager customFieldManager = new CustomFieldManager(str1, paramInt1);
/* 351 */       customFieldManager.getCustomFields();
/* 352 */       String str3 = "update cus_fielddata set ";
/* 353 */       String str4 = "";
/* 354 */       while (customFieldManager.next()) {
/* 355 */         if (!customFieldManager.isUse())
/* 356 */           continue;  str4 = str4 + ",field" + customFieldManager.getId() + "=";
/* 357 */         if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 358 */           .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 359 */           str4 = str4 + "'" + Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId())) + "'"; continue;
/*     */         } 
/* 361 */         if (Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId())).equals("")) {
/* 362 */           str4 = str4 + "null"; continue;
/*     */         } 
/* 364 */         str4 = str4 + Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId()));
/*     */       } 
/*     */ 
/*     */       
/* 368 */       if (!str4.equals("")) {
/* 369 */         str4 = str4.substring(1);
/*     */         
/* 371 */         str3 = str3 + str4 + " where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2;
/* 372 */         recordSet.executeSql(str3);
/*     */       } 
/*     */     } else {
/* 375 */       addCustomDataE9Add(paramString, paramInt1, paramFileUpload, paramInt2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCustomData(String paramString, int paramInt1, int paramInt2) {
/* 387 */     RecordSet recordSet = new RecordSet();
/* 388 */     recordSet.executeSql("delete from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getMutiCustomData(String paramString, int paramInt1, int paramInt2) {
/* 398 */     String str1 = paramString;
/* 399 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 400 */       str1 = "HrmCustomFieldByInfoType"; 
/* 401 */     if (this.rs == null) {
/* 402 */       this.rs = new RecordSet();
/*     */     }
/* 404 */     RecordSet recordSet = new RecordSet();
/* 405 */     String str2 = "";
/* 406 */     recordSet.executeSql("select fieldid from cus_formfield where scope='" + str1 + "' and scopeid=" + paramInt1);
/* 407 */     while (recordSet.next()) {
/* 408 */       str2 = str2 + ",field" + recordSet.getString("fieldid");
/*     */     }
/*     */     
/* 411 */     if (!str2.equals("")) {
/* 412 */       str2 = str2.substring(1);
/* 413 */       this.rs.executeSql("select " + str2 + " from cus_fielddata where scope='" + paramString + "' and scopeid=" + paramInt1 + " and id=" + paramInt2 + " order by seqorder");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean nextMutiData() {
/* 422 */     return this.rs.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMutiData(String paramString) {
/* 431 */     return this.rs.getString(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void editMutiCustomData(String paramString, int paramInt1, FileUpload paramFileUpload, int paramInt2) {
/* 442 */     String str1 = paramString;
/* 443 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 444 */       str1 = "HrmCustomFieldByInfoType"; 
/* 445 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 447 */     String str2 = "insert into cus_fielddata";
/* 448 */     String str3 = "";
/* 449 */     String str4 = "";
/*     */ 
/*     */     
/* 452 */     recordSet.executeSql("select id from cus_treeform where parentid=" + paramInt1);
/* 453 */     int i = 0;
/* 454 */     boolean bool = true;
/* 455 */     String str5 = "";
/* 456 */     while (recordSet.next()) {
/* 457 */       i = recordSet.getInt("id");
/* 458 */       recordSet.executeSql("delete from cus_fielddata where scope='" + paramString + "' and scopeid=" + i + " and id=" + paramInt2);
/* 459 */       CustomFieldManager customFieldManager = new CustomFieldManager(str1, i);
/* 460 */       customFieldManager.getCustomFields();
/* 461 */       str3 = "";
/* 462 */       while (customFieldManager.next()) {
/* 463 */         str3 = str3 + ",field" + customFieldManager.getId();
/*     */       }
/* 465 */       if (!str3.equals("")) {
/* 466 */         str3 = str3.substring(1);
/*     */       }
/* 468 */       int j = Util.getIntValue(paramFileUpload.getParameter("nodesnum_" + i), 0);
/* 469 */       for (byte b = 0; b < j; b++) {
/* 470 */         customFieldManager.beforeFirst();
/* 471 */         str4 = "";
/* 472 */         bool = true;
/*     */         
/* 474 */         while (customFieldManager.next()) {
/* 475 */           str5 = Util.null2String(paramFileUpload.getParameter("customfield" + customFieldManager.getId() + "_" + i + "_" + b));
/* 476 */           if (customFieldManager.getHtmlType().equals("4")) {
/* 477 */             if (this.isE9) {
/* 478 */               if (str5.equals("true")) {
/* 479 */                 str5 = "1";
/*     */               }
/* 481 */               if (str5.equals("false")) {
/* 482 */                 str5 = "";
/*     */               }
/*     */             } else {
/* 485 */               str5 = Util.null2String(paramFileUpload.getParameter("customfield" + customFieldManager.getId() + "_" + i + "_" + b + "0"));
/*     */             } 
/*     */           }
/*     */           
/* 489 */           if (!str5.equals("")) {
/* 490 */             bool = false;
/*     */           }
/* 492 */           if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 493 */             .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 494 */             str4 = str4 + ",'" + str5 + "'"; continue;
/*     */           } 
/* 496 */           if (str5.equals("")) {
/* 497 */             str4 = str4 + ",null"; continue;
/*     */           } 
/* 499 */           str4 = str4 + "," + str5;
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 504 */         if (!bool && 
/* 505 */           !str4.equals(""))
/*     */         {
/* 507 */           recordSet.executeSql(str2 + "(scope,scopeid,id," + str3 + ") values('" + paramString + "'," + i + "," + paramInt2 + "," + str4.substring(1) + ")");
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void editMutiCustomDataeE9Add(String paramString, int paramInt1, FileUpload paramFileUpload, int paramInt2) {
/* 522 */     String str1 = paramString;
/* 523 */     String str2 = "" + paramInt1;
/* 524 */     if (paramInt1 == -1) str2 = "0"; 
/* 525 */     if (paramString.equals("CareerCustomFieldByInfoType"))
/* 526 */       str1 = "HrmCustomFieldByInfoType"; 
/* 527 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 529 */     String str3 = "insert into cus_fielddata";
/* 530 */     String str4 = "";
/* 531 */     String str5 = "";
/*     */ 
/*     */     
/* 534 */     recordSet.executeSql("select id from cus_treeform where parentid=" + paramInt1);
/* 535 */     int i = 0;
/* 536 */     boolean bool = true;
/* 537 */     String str6 = "";
/* 538 */     while (recordSet.next()) {
/* 539 */       i = recordSet.getInt("id");
/* 540 */       recordSet.executeSql("delete from cus_fielddata where scope='" + paramString + "' and scopeid=" + i + " and id=" + paramInt2);
/* 541 */       CustomFieldManager customFieldManager = new CustomFieldManager(str1, i);
/* 542 */       customFieldManager.getCustomFields();
/* 543 */       str4 = "";
/* 544 */       while (customFieldManager.next()) {
/* 545 */         str4 = str4 + ",field" + customFieldManager.getId();
/*     */       }
/* 547 */       if (!str4.equals("")) {
/* 548 */         str4 = str4.substring(1);
/*     */       }
/* 550 */       int j = Util.getIntValue(paramFileUpload.getParameter("nodesnum_" + i), 0);
/* 551 */       for (byte b = 0; b < j; b++) {
/* 552 */         customFieldManager.beforeFirst();
/* 553 */         str5 = "";
/* 554 */         bool = true;
/*     */         
/* 556 */         while (customFieldManager.next()) {
/* 557 */           str6 = Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId() + "_" + i + "_" + b));
/* 558 */           if (customFieldManager.getHtmlType().equals("4")) str6 = Util.null2String(paramFileUpload.getParameter("customfield_" + str2 + "_" + customFieldManager.getId() + "_" + i + "_" + b + "0")); 
/* 559 */           if (!str6.equals("")) {
/* 560 */             bool = false;
/*     */           }
/* 562 */           if (customFieldManager.getFieldDbType().startsWith("text") || customFieldManager.getFieldDbType().startsWith("char") || customFieldManager
/* 563 */             .getFieldDbType().startsWith("varchar") || customFieldManager.getType() == 161 || customFieldManager.getType() == 162 || customFieldManager.getType() == 257 || customFieldManager.getType() == 256) {
/* 564 */             str5 = str5 + ",'" + str6 + "'"; continue;
/*     */           } 
/* 566 */           if (str6.equals("")) {
/* 567 */             str5 = str5 + ",null"; continue;
/*     */           } 
/* 569 */           str5 = str5 + "," + str6;
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 574 */         if (!bool && 
/* 575 */           !str5.equals("")) {
/* 576 */           recordSet.executeSql(str3 + "(scope,scopeid,id," + str4 + ") values('" + paramString + "'," + i + "," + paramInt2 + "," + str5.substring(1) + ")");
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean getIsE9() {
/* 584 */     return this.isE9;
/*     */   }
/*     */   
/*     */   public void setIsE9(boolean paramBoolean) {
/* 588 */     this.isE9 = paramBoolean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/CustomFieldTreeManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */