/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AllSubordinate
/*     */   extends BaseBean
/*     */ {
/*  22 */   private int currentindex = -1;
/*  23 */   private int recordercount = 0;
/*     */ 
/*     */ 
/*     */   
/*     */   private ArrayList allsubordinates;
/*     */ 
/*     */ 
/*     */   
/*     */   private List<String> lstDirectSup;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getAll(String paramString) throws Exception {
/*  37 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  39 */     recordSet.executeProc("HrmResource_SelectTheSub", paramString);
/*  40 */     while (recordSet.next()) {
/*  41 */       if (this.allsubordinates.indexOf(Util.null2String(recordSet.getString(1))) != -1)
/*  42 */         continue;  this.allsubordinates.add(Util.null2String(recordSet.getString(1)));
/*  43 */       this.recordercount++;
/*  44 */       getAll(Util.null2String(recordSet.getString(1)));
/*     */     } 
/*     */   }
/*     */   
/*  48 */   public AllSubordinate() { this.lstDirectSup = new ArrayList<String>();
/*     */     this.allsubordinates = new ArrayList(); } public void getAllDiectSub(String paramString) {
/*  50 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  52 */     recordSet.executeProc("HrmResource_SelectTheSub", paramString);
/*  53 */     ArrayList<String> arrayList = new ArrayList();
/*  54 */     while (recordSet.next()) {
/*  55 */       arrayList.add(Util.null2String(recordSet.getString(1)));
/*     */     }
/*     */     
/*  58 */     if (arrayList.size() != 0) {
/*     */       
/*  60 */       if (this.lstDirectSup.indexOf(paramString) != -1) {
/*  61 */         this.lstDirectSup.add(paramString);
/*     */       }
/*  63 */       for (String str : arrayList) {
/*     */         
/*  65 */         if (!str.equals(paramString)) {
/*  66 */           getAllDiectSub(str);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public List<String> getDirectSup() {
/*  73 */     return this.lstDirectSup;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllSub(String paramString) throws Exception {
/*  84 */     getAll(paramString);
/*  85 */     String str = "0";
/*  86 */     for (byte b = 0; b < this.allsubordinates.size(); b++) {
/*  87 */       if (str.equals("0")) {
/*  88 */         str = this.allsubordinates.get(b);
/*     */       } else {
/*  90 */         str = str + "," + this.allsubordinates.get(b);
/*     */       } 
/*     */     } 
/*  93 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAllSubordinateNum() {
/* 103 */     return this.recordercount;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 112 */     if (this.currentindex + 1 < this.recordercount) {
/* 113 */       this.currentindex++;
/* 114 */       return true;
/*     */     } 
/*     */     
/* 117 */     this.currentindex = -1;
/* 118 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubordinateID() {
/* 127 */     return this.allsubordinates.get(this.currentindex);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/AllSubordinate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */