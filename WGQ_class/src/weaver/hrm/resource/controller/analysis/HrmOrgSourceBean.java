/*    */ package weaver.hrm.resource.controller.analysis;
/*    */ 
/*    */ import weaver.hrm.cachecenter.entry.HrmTypeStyle;
/*    */ import weaver.hrm.cachecenter.entry.OrgtypeInfo;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ public class HrmOrgSourceBean
/*    */   extends HrmOrgBean {
/*  9 */   protected ResourceComInfo resourceComInfo = new ResourceComInfo(true); public boolean checkContain(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/*    */     boolean bool1;
/*    */     String str5, str6;
/* 12 */     Boolean bool = Boolean.valueOf(false);
/* 13 */     String str1 = this.resourceComInfo.getSubCompanyID(paramString);
/* 14 */     String str2 = this.resourceComInfo.getSeclevel(paramString);
/* 15 */     String str3 = this.resourceComInfo.getDepartmentID(paramString);
/* 16 */     String str4 = this.resourceComInfo.getJobTitle(paramString);
/*    */ 
/*    */     
/* 19 */     switch (paramOrgtypeInfo.getType()) {
/*    */       case SUBCOMPANY:
/* 21 */         bool = Boolean.valueOf((checkSimpleSubcompany(paramOrgtypeInfo, str1) && 
/* 22 */             checkSeclevel(paramOrgtypeInfo, str2)));
/*    */         break;
/*    */       case DEPARTMENT:
/* 25 */         bool = Boolean.valueOf((checkSimpleDepartment(paramOrgtypeInfo, str3) && 
/* 26 */             checkSeclevel(paramOrgtypeInfo, str2)));
/*    */         break;
/*    */       case RESOURCE:
/* 29 */         bool = Boolean.valueOf(checkSimpleResource(paramOrgtypeInfo, paramString));
/*    */         break;
/*    */       case ROLE:
/* 32 */         bool = Boolean.valueOf((checkSimpleRole(paramOrgtypeInfo, paramString) && 
/* 33 */             checkSeclevel(paramOrgtypeInfo, str2)));
/*    */         break;
/*    */       case JOBTITLE:
/* 36 */         bool1 = true;
/* 37 */         str5 = paramOrgtypeInfo.getJoblevel();
/* 38 */         str6 = paramOrgtypeInfo.getJobobjid();
/* 39 */         if ("0".equals(str5)) {
/* 40 */           bool1 = checkSimpleDepartment(OrgtypeInfo.simpleWrap(str6, HrmTypeStyle.DEPARTMENT), str3);
/*    */         }
/* 42 */         else if ("1".equals(str5)) {
/* 43 */           bool1 = checkSimpleSubcompany(OrgtypeInfo.simpleWrap(str6, HrmTypeStyle.SUBCOMPANY), str1);
/*    */         } 
/*    */ 
/*    */         
/* 47 */         bool = Boolean.valueOf((bool1 && 
/* 48 */             checkSimpleJob(paramOrgtypeInfo, str4)));
/*    */         break;
/*    */       case ALL:
/* 51 */         bool = Boolean.valueOf(checkSeclevel(paramOrgtypeInfo, str2));
/*    */         break;
/*    */     } 
/* 54 */     return bool.booleanValue() ? paramOrgtypeInfo.getIsIn() : (!paramOrgtypeInfo.getIsIn());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/controller/analysis/HrmOrgSourceBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */