/*     */ package weaver.hrm.resource.controller.analysis;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.cachecenter.entry.HrmTypeStyle;
/*     */ import weaver.hrm.cachecenter.entry.OrgtypeInfo;
/*     */ import weaver.hrm.cachecenter.entry.SqlConstStyle;
/*     */ import weaver.hrm.cachecenter.util.HrmOrgUtil;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmOrgForwardBean
/*     */   extends HrmOrgBean
/*     */ {
/*     */   private List<OrgtypeInfo> list;
/*  24 */   private List<OrgtypeInfo> positiveList = new ArrayList<>();
/*  25 */   private List<OrgtypeInfo> reverseList = new ArrayList<>();
/*     */   
/*     */   private boolean isNeed = false;
/*     */   
/*  29 */   private Set<String> viewDeptSet = new HashSet<>();
/*     */   
/*  31 */   private Set<String> viewSubCompanySet = new HashSet<>();
/*     */   
/*  33 */   protected ResourceComInfo resourceComInfo = new ResourceComInfo(true);
/*     */   
/*     */   private boolean isInited = false;
/*     */   
/*     */   private User user;
/*     */   
/*     */   public HrmOrgForwardBean(List<OrgtypeInfo> paramList, User paramUser) {
/*  40 */     this(paramList, true, paramUser);
/*     */   }
/*     */   
/*     */   public HrmOrgForwardBean(List<OrgtypeInfo> paramList, boolean paramBoolean, User paramUser) {
/*  44 */     this.list = paramList;
/*  45 */     this.isNeed = (this.list != null);
/*  46 */     this.user = paramUser;
/*     */     
/*  48 */     if (this.isNeed && this.list.size() > 0) {
/*  49 */       for (OrgtypeInfo orgtypeInfo : this.list) {
/*  50 */         if (orgtypeInfo.getIsIn()) {
/*  51 */           this.positiveList.add(orgtypeInfo); continue;
/*     */         } 
/*  53 */         this.reverseList.add(orgtypeInfo);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*  58 */     checkInited(paramBoolean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void checkInited(boolean paramBoolean) {
/*  65 */     if (!this.isInited && 
/*  66 */       this.isNeed && this.list.size() > 0 && 
/*  67 */       paramBoolean) init();
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void init() {
/*  74 */     this.isInited = true;
/*  75 */     this.resourceComInfo.setTofirstRow();
/*  76 */     while (this.resourceComInfo.next()) {
/*  77 */       String str1 = this.resourceComInfo.getDepartmentID();
/*  78 */       String str2 = this.resourceComInfo.getResourceid();
/*     */       
/*  80 */       if (this.viewDeptSet.contains(str1))
/*     */         continue; 
/*  82 */       if (assertPull(str2)) {
/*  83 */         pull();
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   private boolean assertPull(String paramString) {
/*  89 */     Boolean bool1 = null;
/*  90 */     Boolean bool2 = null;
/*     */     
/*  92 */     for (OrgtypeInfo orgtypeInfo : this.list) {
/*  93 */       if (isPositivePass(bool2, orgtypeInfo))
/*     */         continue; 
/*  95 */       if (isReversePass(bool1, orgtypeInfo))
/*     */         break; 
/*  97 */       boolean bool = checkOne(orgtypeInfo, paramString);
/*  98 */       if (orgtypeInfo.getIsIn()) {
/*  99 */         bool2 = Boolean.valueOf(bool); continue;
/*     */       } 
/* 101 */       bool1 = Boolean.valueOf(bool);
/*     */     } 
/*     */     
/* 104 */     return assertJudge(bool2, bool1);
/*     */   }
/*     */   
/*     */   private boolean assertJudge(Boolean paramBoolean1, Boolean paramBoolean2) {
/* 108 */     if (Boolean.FALSE.equals(paramBoolean2)) return false; 
/* 109 */     if (Boolean.FALSE.equals(paramBoolean1)) return false; 
/* 110 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   private void pull() {
/* 115 */     String str1 = this.resourceComInfo.getDepartmentID();
/* 116 */     loopPullDeptId(str1);
/* 117 */     String str2 = this.resourceComInfo.getSubCompanyID();
/* 118 */     loopPullSubcompany(str2);
/*     */   }
/*     */   
/*     */   private void loopPullSubcompany(String paramString) {
/* 122 */     if (HrmOrgUtil.isRightOrgId(paramString)) {
/* 123 */       this.viewSubCompanySet.add(paramString);
/* 124 */       loopPullSubcompany(this.subCompanyComInfo.getSupsubcomid(paramString));
/*     */     } 
/*     */   }
/*     */   
/*     */   private void loopPullDeptId(String paramString) {
/* 129 */     if (HrmOrgUtil.isRightOrgId(paramString)) {
/* 130 */       this.viewDeptSet.add(paramString);
/* 131 */       loopPullDeptId(this.departmentComInfo.getDepartmentsupdepid(paramString));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isPositivePass(Boolean paramBoolean, OrgtypeInfo paramOrgtypeInfo) {
/* 139 */     return (paramBoolean != null && paramBoolean.booleanValue() && paramOrgtypeInfo.getIsIn());
/*     */   }
/*     */   
/*     */   private boolean isReversePass(Boolean paramBoolean, OrgtypeInfo paramOrgtypeInfo) {
/* 143 */     return (paramBoolean != null && !paramBoolean.booleanValue() && !paramOrgtypeInfo.getIsIn());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkResource(String paramString) {
/* 152 */     if (this.isNeed && this.list.size() > 0) {
/* 153 */       return assertPull(paramString);
/*     */     }
/* 155 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean checkOne(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/* 160 */     Boolean bool = Boolean.valueOf(false);
/* 161 */     String str1 = this.resourceComInfo.getSubCompanyID(paramString);
/* 162 */     String str2 = this.resourceComInfo.getSeclevel(paramString);
/* 163 */     String str3 = this.resourceComInfo.getDepartmentID(paramString);
/* 164 */     String str4 = this.resourceComInfo.getJobTitle(paramString);
/*     */ 
/*     */     
/* 167 */     String str5 = this.resourceComInfo.getStatus(paramString);
/* 168 */     if (!"0,1,2,3".contains(str5)) {
/* 169 */       bool = Boolean.valueOf(false);
/*     */     } else {
/* 171 */       boolean bool1; String str6; String str7; switch (paramOrgtypeInfo.getType()) {
/*     */         case SUBCOMPANY:
/* 173 */           bool = Boolean.valueOf((checkSimpleSubcompany(paramOrgtypeInfo, str1) && checkSeclevel(paramOrgtypeInfo, str2)));
/*     */           break;
/*     */         case DEPARTMENT:
/* 176 */           bool = Boolean.valueOf((checkSimpleDepartment(paramOrgtypeInfo, str3) && checkSeclevel(paramOrgtypeInfo, str2)));
/*     */           break;
/*     */         case RESOURCE:
/* 179 */           bool = Boolean.valueOf(checkSimpleResource(paramOrgtypeInfo, paramString));
/*     */           break;
/*     */         case ROLE:
/* 182 */           bool = Boolean.valueOf((checkSimpleRole(paramOrgtypeInfo, paramString) && checkSeclevel(paramOrgtypeInfo, str2)));
/*     */           break;
/*     */         case JOBTITLE:
/* 185 */           bool1 = true;
/* 186 */           str6 = paramOrgtypeInfo.getJoblevel();
/* 187 */           str7 = paramOrgtypeInfo.getJobobjid();
/* 188 */           if ("0".equals(str6)) {
/* 189 */             bool1 = checkSimpleDepartment(OrgtypeInfo.simpleWrap(str7, HrmTypeStyle.DEPARTMENT), str3);
/* 190 */           } else if ("1".equals(str6)) {
/* 191 */             bool1 = checkSimpleSubcompany(OrgtypeInfo.simpleWrap(str7, HrmTypeStyle.SUBCOMPANY), str1);
/*     */           } 
/*     */           
/* 194 */           bool = Boolean.valueOf((bool1 && checkSimpleJob(paramOrgtypeInfo, str4)));
/*     */           break;
/*     */         case ALL:
/* 197 */           bool = Boolean.valueOf(checkSeclevel(paramOrgtypeInfo, str2));
/*     */           break;
/*     */       } 
/*     */     } 
/* 201 */     return bool.booleanValue() ? paramOrgtypeInfo.getIsIn() : (!paramOrgtypeInfo.getIsIn());
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean checkSubcompanyView(String paramString) {
/* 206 */     if (!this.isNeed) return true; 
/* 207 */     if (this.viewSubCompanySet.contains(paramString)) return true; 
/* 208 */     return false;
/*     */   }
/*     */   
/*     */   public boolean checkDepartmentView(String paramString) {
/* 212 */     if (!this.isNeed) return true; 
/* 213 */     if (this.viewDeptSet.contains(paramString)) return true; 
/* 214 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public String resetSqlwhere(String paramString1, String paramString2) {
/* 219 */     if (!this.isNeed) return paramString1; 
/* 220 */     StringBuffer stringBuffer = new StringBuffer(" (");
/*     */     
/* 222 */     if (this.list.size() == 0) {
/* 223 */       stringBuffer.append(" (8=9) ");
/*     */     } else {
/* 225 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 226 */       if (this.positiveList.size() > 0) {
/* 227 */         stringBuffer1.append(" ( ");
/* 228 */         for (byte b = 0; b < this.positiveList.size(); b++) {
/* 229 */           if (b > 0) stringBuffer1.append(" or "); 
/* 230 */           stringBuffer1.append(((OrgtypeInfo)this.positiveList.get(b)).getSql(paramString2));
/*     */         } 
/* 232 */         stringBuffer1.append(" ) ");
/*     */       } 
/*     */       
/* 235 */       StringBuffer stringBuffer2 = new StringBuffer();
/* 236 */       if (this.reverseList.size() > 0) {
/* 237 */         stringBuffer2.append(" ( ");
/* 238 */         for (byte b = 0; b < this.reverseList.size(); b++) {
/* 239 */           if (b > 0) stringBuffer2.append(" and "); 
/* 240 */           stringBuffer2.append(((OrgtypeInfo)this.reverseList.get(b)).getSql(paramString2));
/*     */         } 
/* 242 */         stringBuffer2.append(" ) ");
/*     */       } 
/*     */       
/* 245 */       String str = stringBuffer1.toString();
/* 246 */       if (stringBuffer2.length() > 0) {
/* 247 */         str = str + ((str.length() > 0) ? " and " : "") + stringBuffer2.toString();
/*     */       }
/*     */       
/* 250 */       stringBuffer.append(str);
/*     */     } 
/*     */     
/* 253 */     stringBuffer.append(") ");
/*     */     
/* 255 */     if (StringUtils.isNotBlank(paramString1)) {
/* 256 */       paramString1 = paramString1 + SqlConstStyle.AND.getValue();
/*     */     }
/* 258 */     paramString1 = paramString1 + stringBuffer.toString();
/*     */     
/* 260 */     return paramString1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/controller/analysis/HrmOrgForwardBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */