/*    */ package weaver.hrm.resource.controller.analysis;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.cachecenter.entry.OrgtypeInfo;
/*    */ import weaver.hrm.resource.controller.HrmWorkflowAdvanceManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmResourceScopeInterface
/*    */ {
/*    */   public static HrmOrgForwardBean getForwardBean(String paramString1, String paramString2, String paramString3, User paramUser) {
/* 19 */     List<OrgtypeInfo> list = HrmWorkflowAdvanceManager.loadDataForWF(paramString1, paramString2, paramString3, paramUser);
/* 20 */     return new HrmOrgForwardBean(list, false, paramUser);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/controller/analysis/HrmResourceScopeInterface.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */