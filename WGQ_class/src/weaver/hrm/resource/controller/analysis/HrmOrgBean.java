/*     */ package weaver.hrm.resource.controller.analysis;
/*     */ 
/*     */ import java.util.Set;
/*     */ import java.util.StringTokenizer;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.cachecenter.bean.KVRoleComInfo;
/*     */ import weaver.hrm.cachecenter.bean.LoadComInfo;
/*     */ import weaver.hrm.cachecenter.entry.HrmTypeStyle;
/*     */ import weaver.hrm.cachecenter.entry.OrgUpOrDownTypeStyle;
/*     */ import weaver.hrm.cachecenter.entry.OrgtypeInfo;
/*     */ import weaver.hrm.cachecenter.util.HrmOrgUtil;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmOrgBean
/*     */ {
/*     */   protected boolean checkSimpleSubcompany(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/*  24 */     String str = paramOrgtypeInfo.getId();
/*  25 */     OrgUpOrDownTypeStyle orgUpOrDownTypeStyle = paramOrgtypeInfo.getOrgUpOrDownType();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  30 */     if (StringUtils.isBlank(str)) return false; 
/*  31 */     StringTokenizer stringTokenizer = new StringTokenizer(str, ",");
/*  32 */     while (stringTokenizer.hasMoreTokens()) {
/*  33 */       String str1 = stringTokenizer.nextToken();
/*  34 */       Integer integer = compare(str1, paramString, true);
/*  35 */       if (dualFlag(integer, orgUpOrDownTypeStyle)) {
/*  36 */         return true;
/*     */       }
/*     */     } 
/*  39 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean checkSimpleDepartment(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/*  45 */     String str = paramOrgtypeInfo.getId();
/*  46 */     OrgUpOrDownTypeStyle orgUpOrDownTypeStyle = paramOrgtypeInfo.getOrgUpOrDownType();
/*     */ 
/*     */     
/*  49 */     if (StringUtils.isBlank(str)) return false; 
/*  50 */     StringTokenizer stringTokenizer = new StringTokenizer(str, ",");
/*  51 */     while (stringTokenizer.hasMoreTokens()) {
/*  52 */       String str1 = stringTokenizer.nextToken();
/*  53 */       Integer integer = compare(str1, paramString, false);
/*  54 */       if (dualFlag(integer, orgUpOrDownTypeStyle)) {
/*  55 */         return true;
/*     */       }
/*     */     } 
/*  58 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   protected boolean checkSimpleResource(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/*  63 */     String str = paramOrgtypeInfo.getId();
/*  64 */     if (StringUtils.isBlank(str)) return false;
/*     */     
/*  66 */     StringTokenizer stringTokenizer = new StringTokenizer(str, ",");
/*  67 */     while (stringTokenizer.hasMoreTokens()) {
/*  68 */       String str1 = stringTokenizer.nextToken();
/*  69 */       if (str1.equals(paramString)) {
/*  70 */         return true;
/*     */       }
/*     */     } 
/*  73 */     return false;
/*     */   }
/*     */   
/*     */   protected boolean checkSimpleRole(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/*  77 */     String str1 = paramOrgtypeInfo.getId();
/*  78 */     String str2 = paramOrgtypeInfo.getRolelevel();
/*  79 */     if (StringUtils.isBlank(str1)) return false; 
/*  80 */     StringTokenizer stringTokenizer = new StringTokenizer(str1, ",");
/*  81 */     KVRoleComInfo kVRoleComInfo = (KVRoleComInfo)LoadComInfo.getInstance(KVRoleComInfo.class);
/*  82 */     while (stringTokenizer.hasMoreTokens()) {
/*  83 */       String str3 = stringTokenizer.nextToken();
/*  84 */       String str4 = kVRoleComInfo.getResourceidByRoleIdAndLevel(str3, str2);
/*  85 */       if (checkSimpleResource(OrgtypeInfo.simpleWrap(str4, HrmTypeStyle.RESOURCE), paramString)) {
/*  86 */         return true;
/*     */       }
/*     */     } 
/*  89 */     return false;
/*     */   }
/*     */   
/*     */   protected boolean checkSimpleJob(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/*  93 */     String str = paramOrgtypeInfo.getId();
/*  94 */     if (StringUtils.isBlank(str)) return false; 
/*  95 */     StringTokenizer stringTokenizer = new StringTokenizer(str, ",");
/*     */     
/*  97 */     while (stringTokenizer.hasMoreTokens()) {
/*  98 */       String str1 = stringTokenizer.nextToken();
/*  99 */       if (str1.equals(paramString)) {
/* 100 */         return true;
/*     */       }
/*     */     } 
/* 103 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean checkSeclevel(OrgtypeInfo paramOrgtypeInfo, String paramString) {
/* 109 */     boolean bool = !paramOrgtypeInfo.getIsIn() ? true : false;
/* 110 */     int i = Util.getIntValue(paramString, 0);
/* 111 */     int j = paramOrgtypeInfo.getSeclevelBegin();
/* 112 */     int k = paramOrgtypeInfo.getSeclevelEnd();
/*     */     
/* 114 */     return (i >= j && i <= k);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean dualFlag(Integer paramInteger, OrgUpOrDownTypeStyle paramOrgUpOrDownTypeStyle) {
/* 121 */     if (paramInteger == null) return false; 
/* 122 */     if (paramInteger.intValue() > 0)
/* 123 */       return OrgUpOrDownTypeStyle.ORG_ALL_DOWN.equals(paramOrgUpOrDownTypeStyle); 
/* 124 */     if (paramInteger.intValue() == 0)
/* 125 */       return (paramOrgUpOrDownTypeStyle == null); 
/* 126 */     if (paramInteger.intValue() < 0) {
/* 127 */       return OrgUpOrDownTypeStyle.ORG_ALL_UP.equals(paramOrgUpOrDownTypeStyle);
/*     */     }
/* 129 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/* 134 */   protected DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 135 */   protected SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer compare(String paramString1, String paramString2, boolean paramBoolean) {
/* 148 */     if (!HrmOrgUtil.isRightOrgId(paramString1) || 
/* 149 */       !HrmOrgUtil.isRightOrgId(paramString2)) return null; 
/* 150 */     if (paramString1.equals(paramString2)) return Integer.valueOf(0); 
/* 151 */     Set set1 = null;
/* 152 */     if (paramBoolean) {
/* 153 */       set1 = HrmOrgUtil.getAllSuperSubcompanyIdById(paramString1, this.subCompanyComInfo);
/*     */     } else {
/* 155 */       set1 = HrmOrgUtil.getAllSuperDepartmentIdById(paramString1, this.departmentComInfo);
/*     */     } 
/* 157 */     if (set1.contains(paramString2)) return Integer.valueOf(-1);
/*     */     
/* 159 */     Set set2 = null;
/* 160 */     if (paramBoolean) {
/* 161 */       set2 = HrmOrgUtil.getAllSuperSubcompanyIdById(paramString2, this.subCompanyComInfo);
/*     */     } else {
/* 163 */       set2 = HrmOrgUtil.getAllSuperDepartmentIdById(paramString2, this.departmentComInfo);
/*     */     } 
/* 165 */     if (set2.contains(paramString1)) return Integer.valueOf(1);
/*     */     
/* 167 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/controller/analysis/HrmOrgBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */