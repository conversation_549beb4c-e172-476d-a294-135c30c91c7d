/*     */ package weaver.hrm.resource.controller;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.cachecenter.CacheLoadQueueManager;
/*     */ import weaver.hrm.cachecenter.bean.LoadComInfo;
/*     */ import weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo;
/*     */ import weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo;
/*     */ import weaver.hrm.cachecenter.entry.OrgtypeInfo;
/*     */ import weaver.hrm.cachecenter.entry.SqlConstStyle;
/*     */ import weaver.hrm.cachecenter.util.ObjtypeParser;
/*     */ import weaver.hrm.resource.controller.analysis.HrmOrgForwardBean;
/*     */ import weaver.hrm.resource.controller.analysis.HrmOrgSourceBean;
/*     */ import weaver.workflow.workflow.WfFwLimitUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmWorkflowAdvanceManager
/*     */ {
/*     */   private static final String BEGIN_KEY = "WfAdVaNcE";
/*  30 */   private static final Pattern p = Pattern.compile("[']WfAdVaNcE(\\d+)[_](\\d+)[_](\\d+)['][=][']WfAdVaNcE");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String wrapUnqinueSqlwhere(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  40 */     String str = getUnqinueKey(paramString2, paramString3, paramString4);
/*  41 */     if (paramString1.length() > 0) {
/*  42 */       paramString1 = paramString1 + SqlConstStyle.AND.getValue();
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  49 */     paramString1 = paramString1 + SqlConstStyle.L_PARENTHESIS.getValue().concat(str).concat(SqlConstStyle.EQ.getValue()).concat(str).concat(SqlConstStyle.R_PARENTHESIS.getValue());
/*     */     
/*  51 */     return paramString1;
/*     */   }
/*     */   
/*     */   public static String getUnqinueKey(String paramString1, String paramString2, String paramString3) {
/*  55 */     return "'".concat("WfAdVaNcE").concat(paramString1).concat("_").concat(paramString2).concat("_").concat(paramString3).concat("'");
/*     */   }
/*     */ 
/*     */   
/*     */   public static String[] checkSqlwhere(String paramString) {
/*  60 */     if (StringUtils.isBlank(paramString)) return null; 
/*  61 */     Matcher matcher = p.matcher(paramString);
/*  62 */     if (matcher.find()) {
/*  63 */       String[] arrayOfString = new String[3];
/*  64 */       arrayOfString[0] = matcher.group(1);
/*  65 */       arrayOfString[1] = matcher.group(2);
/*  66 */       arrayOfString[2] = matcher.group(3);
/*  67 */       return arrayOfString;
/*     */     } 
/*  69 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<OrgtypeInfo> loadDataForWF(String paramString1, String paramString2, String paramString3, User paramUser) {
/*  82 */     if (!CacheLoadQueueManager.isOpen()) return null;
/*     */ 
/*     */     
/*  85 */     if (StringUtils.isBlank(paramString2) || StringUtils.isBlank(paramString1) || 
/*  86 */       StringUtils.isBlank(paramString3)) return null;
/*     */ 
/*     */     
/*  89 */     if (!WfFwLimitUtil.checkForwardflag(paramString3)) return null;
/*     */ 
/*     */     
/*  92 */     if ("5".equals(paramString3)) {
/*  93 */       paramString3 = "2";
/*     */     }
/*     */ 
/*     */     
/*  97 */     RecordSet recordSet = new RecordSet();
/*  98 */     recordSet.executeQuery("select * from workflow_FwLimitSet where wfid=? and nodeid=? and fwtype=? ", new Object[] { paramString1, paramString2, paramString3 });
/*  99 */     if (!recordSet.next()) return null; 
/* 100 */     String str1 = recordSet.getString("isopen");
/* 101 */     String str2 = recordSet.getString("modetype");
/*     */ 
/*     */     
/* 104 */     if (!"1".equals(str1)) return null;
/*     */ 
/*     */ 
/*     */     
/* 108 */     if ("0".equals(str2)) {
/*     */       
/* 110 */       WorkflowRecSimpleComInfo workflowRecSimpleComInfo = (WorkflowRecSimpleComInfo)LoadComInfo.getInstance(WorkflowRecSimpleComInfo.class);
/* 111 */       String str = workflowRecSimpleComInfo.getRecSimple(paramString1, paramString2, paramString3);
/* 112 */       return ObjtypeParser.getInstance().parse(str, paramUser);
/*     */     } 
/*     */ 
/*     */     
/* 116 */     WorkflowAdvancedComInfo workflowAdvancedComInfo = (WorkflowAdvancedComInfo)LoadComInfo.getInstance(WorkflowAdvancedComInfo.class);
/* 117 */     List list = workflowAdvancedComInfo.getLimitDesc(paramString1, paramString2, paramString3);
/* 118 */     ArrayList<OrgtypeInfo> arrayList = new ArrayList();
/* 119 */     if (list == null) return arrayList;
/*     */     
/* 121 */     HrmOrgSourceBean hrmOrgSourceBean = new HrmOrgSourceBean();
/*     */ 
/*     */     
/* 124 */     ObjtypeParser objtypeParser = ObjtypeParser.getInstance();
/* 125 */     for (String str : list) {
/* 126 */       String[] arrayOfString = str.split("[$]");
/* 127 */       List list1 = objtypeParser.parse(arrayOfString[0], paramUser);
/* 128 */       List<? extends OrgtypeInfo> list2 = objtypeParser.parse(arrayOfString[1], paramUser);
/* 129 */       boolean bool = false;
/* 130 */       for (OrgtypeInfo orgtypeInfo : list1) {
/* 131 */         if (hrmOrgSourceBean.checkContain(orgtypeInfo, Integer.toString(paramUser.getUID()))) {
/* 132 */           bool = true;
/*     */           break;
/*     */         } 
/*     */       } 
/* 136 */       if (bool) {
/* 137 */         arrayList.addAll(list2);
/*     */       }
/*     */     } 
/*     */     
/* 141 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String resetSqlWhere(HrmOrgForwardBean paramHrmOrgForwardBean, String paramString1, String paramString2) {
/* 150 */     if (paramHrmOrgForwardBean == null) return paramString1; 
/* 151 */     return paramHrmOrgForwardBean.resetSqlwhere(paramString1, paramString2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/controller/HrmWorkflowAdvanceManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */