/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.io.StringReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import javax.xml.parsers.DocumentBuilder;
/*     */ import javax.xml.parsers.DocumentBuilderFactory;
/*     */ import javax.xml.xpath.XPath;
/*     */ import javax.xml.xpath.XPathConstants;
/*     */ import javax.xml.xpath.XPathExpression;
/*     */ import javax.xml.xpath.XPathFactory;
/*     */ import org.apache.axiom.om.OMAbstractFactory;
/*     */ import org.apache.axiom.om.OMElement;
/*     */ import org.apache.axiom.om.OMFactory;
/*     */ import org.apache.axiom.om.OMNamespace;
/*     */ import org.apache.axiom.om.OMNode;
/*     */ import org.apache.axis2.addressing.EndpointReference;
/*     */ import org.apache.axis2.client.Options;
/*     */ import org.apache.axis2.rpc.client.RPCServiceClient;
/*     */ import org.w3c.dom.Document;
/*     */ import org.w3c.dom.Node;
/*     */ import org.w3c.dom.NodeList;
/*     */ import org.xml.sax.InputSource;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ApiService
/*     */   extends BaseBean
/*     */ {
/*     */   public String serviceSend(String paramString1, String paramString2, String paramString3, Map paramMap1, Map paramMap2) {
/*  52 */     if (!"".equals(paramString2) && !"".equals(paramString3)) {
/*  53 */       RPCServiceClient rPCServiceClient = null;
/*     */       try {
/*  55 */         rPCServiceClient = new RPCServiceClient();
/*  56 */         Options options = new Options();
/*  57 */         options.setSoapVersionURI("http://schemas.xmlsoap.org/soap/envelope/");
/*  58 */         options.setAction(paramString1 + paramString3);
/*  59 */         options.setTo(new EndpointReference(paramString2));
/*  60 */         options.setTransportInProtocol("http");
/*  61 */         options.setTimeOutInMilliSeconds(30000L);
/*  62 */         rPCServiceClient.setOptions(options);
/*     */         
/*  64 */         OMFactory oMFactory = OMAbstractFactory.getOMFactory();
/*  65 */         OMNamespace oMNamespace = oMFactory.createOMNamespace(paramString1, "");
/*  66 */         OMElement oMElement1 = oMFactory.createOMElement(paramString3, oMNamespace);
/*  67 */         writeLog("webservicenmathod : " + paramString3 + " webservicespace : " + paramString1 + " webservicename : " + paramString2);
/*     */         
/*  69 */         if (null != paramMap1) {
/*     */           
/*  71 */           Set set = paramMap1.keySet();
/*  72 */           for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/*     */             
/*  74 */             String str1 = Util.null2String(iterator.next());
/*  75 */             String str2 = Util.null2String((String)paramMap1.get(str1));
/*  76 */             String str3 = Util.null2String((String)paramMap2.get(str1));
/*  77 */             writeLog("Parameter paramname : " + str1 + " paramtype : " + str2 + " paramvalue : " + str3);
/*  78 */             if (!"".equals(str1) && !"".equals(str2))
/*     */             {
/*  80 */               if (str2.equalsIgnoreCase("string")) {
/*     */                 
/*  82 */                 OMElement oMElement = oMFactory.createOMElement(str1, oMNamespace);
/*  83 */                 oMElement.setText(str3);
/*  84 */                 oMElement1.addChild((OMNode)oMElement);
/*     */               } 
/*     */             }
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  93 */         OMElement oMElement2 = rPCServiceClient.sendReceive(oMElement1);
/*     */         
/*  95 */         String str = Util.null2String(oMElement2.getFirstElement().getText());
/*  96 */         writeLog("result : " + str);
/*  97 */         return str;
/*  98 */       } catch (Exception exception) {
/*  99 */         exception.printStackTrace();
/* 100 */         writeLog(exception.toString());
/*     */       }
/*     */       finally {
/*     */         
/* 104 */         if (null != rPCServiceClient) {
/*     */           
/*     */           try {
/*     */             
/* 108 */             rPCServiceClient.cleanupTransport();
/* 109 */           } catch (Exception exception) {
/* 110 */             exception.printStackTrace();
/* 111 */             writeLog(exception.toString());
/*     */           } 
/*     */         }
/*     */       } 
/*     */     } 
/* 116 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void test() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List parseServiceResult(String paramString1, String paramString2) {
/* 173 */     List list = null;
/*     */ 
/*     */     
/*     */     try {
/* 177 */       DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
/* 178 */       SecurityMethodUtil.setDBFFeature(documentBuilderFactory);
/* 179 */       documentBuilderFactory.setNamespaceAware(true);
/* 180 */       DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
/* 181 */       StringReader stringReader = new StringReader(paramString1);
/*     */       
/* 183 */       InputSource inputSource = new InputSource(stringReader);
/* 184 */       Document document = documentBuilder.parse(inputSource);
/*     */       
/* 186 */       XPathFactory xPathFactory = XPathFactory.newInstance();
/* 187 */       XPath xPath = xPathFactory.newXPath();
/* 188 */       if (!"".equals(paramString2))
/*     */       {
/* 190 */         list = getElementByName(xPath, document, paramString2);
/*     */       }
/*     */     }
/* 193 */     catch (Exception exception) {
/* 194 */       exception.printStackTrace();
/* 195 */       writeLog(exception.toString());
/*     */     } 
/* 197 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List getElementByName(XPath paramXPath, Document paramDocument, String paramString) throws Exception {
/* 210 */     ArrayList<String> arrayList = new ArrayList();
/* 211 */     String str = "";
/* 212 */     str = paramString;
/* 213 */     XPathExpression xPathExpression = paramXPath.compile(str);
/* 214 */     Object object = xPathExpression.evaluate(paramDocument, XPathConstants.NODESET);
/* 215 */     NodeList nodeList = (NodeList)object;
/* 216 */     for (byte b = 0; b < nodeList.getLength(); b++) {
/* 217 */       Node node = nodeList.item(b);
/* 218 */       arrayList.add(node.getTextContent());
/*     */     } 
/* 220 */     return arrayList;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/ApiService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */