/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UpdateManagerStr
/*     */ {
/*  23 */   private Logger logger = LoggerFactory.getLogger(UpdateManagerStr.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static UpdateManagerStr updateManagerStr;
/*     */ 
/*     */ 
/*     */   
/*  32 */   private List<String> synedData = new ArrayList<>();
/*     */ 
/*     */ 
/*     */   
/*  36 */   private List<String> synerrData = new ArrayList<>();
/*     */ 
/*     */ 
/*     */   
/*  40 */   private Map<String, String[]> cache = (Map)new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized UpdateManagerStr getInstance() {
/*  46 */     if (updateManagerStr == null) {
/*  47 */       updateManagerStr = new UpdateManagerStr();
/*     */     }
/*  49 */     return updateManagerStr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized Map execute() throws Exception {
/*  61 */     this.logger.info("------------------------进入UpdateManagerStr-----------------------------");
/*  62 */     long l1 = System.currentTimeMillis();
/*  63 */     this.synedData.clear();
/*  64 */     this.synerrData.clear();
/*  65 */     this.cache.clear();
/*     */     
/*  67 */     RecordSet recordSet = new RecordSet();
/*  68 */     String str1 = "select id,managerid,managerstr from hrmresource where (managerid is null or managerid = 0)";
/*  69 */     recordSet.executeQuery(str1, new Object[0]);
/*  70 */     ArrayList<String> arrayList = new ArrayList();
/*  71 */     ArrayList<ArrayList<String>> arrayList1 = new ArrayList();
/*  72 */     while (recordSet.next()) {
/*  73 */       ArrayList<String> arrayList2 = new ArrayList();
/*  74 */       String str3 = Util.null2String(recordSet.getString(1));
/*  75 */       String str4 = Util.null2String(recordSet.getString(2));
/*  76 */       this.cache.put(str3, new String[] { str4, "" });
/*  77 */       arrayList2.add(str3);
/*  78 */       arrayList1.add(arrayList2);
/*  79 */       arrayList.add(str3);
/*     */     } 
/*  81 */     String str2 = arrayList1.toString();
/*     */     
/*  83 */     recordSet.executeBatchSql("update hrmresource set managerstr = '' where id = ?", arrayList1);
/*     */     
/*  85 */     Map<String, String> map = loopMangerStr(arrayList, 1);
/*     */     
/*  87 */     long l2 = System.currentTimeMillis();
/*     */     
/*  89 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  90 */     resourceComInfo.removeCache();
/*  91 */     ResourceVirtualComInfo resourceVirtualComInfo = new ResourceVirtualComInfo();
/*  92 */     resourceVirtualComInfo.removeCache();
/*     */     
/*  94 */     map.put("time", (l2 - l1) + "ms");
/*  95 */     this.logger.info("--------------------结束UpdateManagerStr----------------------");
/*  96 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Map loopMangerStr(List<String> paramList, int paramInt) throws Exception {
/* 104 */     if (paramInt > 300) {
/* 105 */       return buildReturnMap(false, 10002, "" + SystemEnv.getHtmlLabelName(10003670, ThreadVarLanguage.getLang()) + "100" + SystemEnv.getHtmlLabelName(10003671, ThreadVarLanguage.getLang()) + "");
/*     */     }
/*     */     
/* 108 */     ArrayList<String> arrayList1 = new ArrayList();
/* 109 */     ArrayList<String> arrayList2 = new ArrayList();
/* 110 */     for (String str1 : paramList) {
/* 111 */       if (this.synedData.contains(str1)) {
/* 112 */         return buildReturnMap(false, 10001, "" + SystemEnv.getHtmlLabelName(10003672, ThreadVarLanguage.getLang()) + "");
/*     */       }
/* 114 */       if (parseList(str1)) {
/* 115 */         this.synedData.add(str1);
/* 116 */         arrayList1.add(str1); continue;
/*     */       } 
/* 118 */       this.synerrData.add(str1);
/*     */     } 
/*     */     
/* 121 */     RecordSet recordSet = new RecordSet();
/* 122 */     arrayList1.remove("");
/* 123 */     String str = arrayList1.toString();
/* 124 */     str = str.substring(1, str.length() - 1);
/* 125 */     recordSet.executeQuery("select id,managerid,managerstr from hrmresource where managerid in (" + str + ")", new Object[0]);
/* 126 */     while (recordSet.next()) {
/* 127 */       String str1 = Util.null2String(recordSet.getString(1));
/* 128 */       String str2 = Util.null2String(recordSet.getString(2));
/* 129 */       String str3 = Util.null2String(recordSet.getString(3));
/* 130 */       this.cache.put(str1, new String[] { str2, str3 });
/* 131 */       arrayList2.add(str1);
/*     */     } 
/* 133 */     if (arrayList2.size() > 0) {
/* 134 */       return loopMangerStr(arrayList2, ++paramInt);
/*     */     }
/* 136 */     return buildReturnMap(true, 0, "" + SystemEnv.getHtmlLabelName(506797, ThreadVarLanguage.getLang()) + " " + SystemEnv.getHtmlLabelName(10003673, ThreadVarLanguage.getLang()) + "managerstr" + SystemEnv.getHtmlLabelName(10003674, ThreadVarLanguage.getLang()) + "->" + this.synedData.size());
/*     */   }
/*     */   
/*     */   private Map buildReturnMap(boolean paramBoolean, int paramInt, String paramString) {
/* 140 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 141 */     hashMap.put("status", Boolean.valueOf(paramBoolean));
/* 142 */     hashMap.put("code", Integer.valueOf(paramInt));
/* 143 */     hashMap.put("msg", paramString);
/* 144 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean parseList(String paramString) throws Exception {
/* 155 */     String str1 = "";
/*     */     
/* 157 */     String[] arrayOfString1 = this.cache.get(paramString);
/*     */     
/* 159 */     String str2 = arrayOfString1[0];
/* 160 */     if ("0".equalsIgnoreCase(str2) || StringUtils.isBlank(str2))
/*     */     {
/* 162 */       return true;
/*     */     }
/* 164 */     String[] arrayOfString2 = this.cache.get(str2);
/*     */     
/* 166 */     String str3 = arrayOfString2[1];
/*     */     
/* 168 */     if (StringUtils.isBlank(str3)) {
/* 169 */       str1 = "," + str2 + ",";
/*     */     } else {
/* 171 */       str1 = "," + str2 + str3;
/*     */     } 
/* 173 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 174 */     recordSetTrans.setAutoCommit(false);
/* 175 */     if (recordSetTrans.executeUpdate("update hrmresource set managerstr = ? where id = ?", new Object[] { str1, paramString })) {
/* 176 */       if (replaceMap(this.cache, paramString, new String[] { str2, str1 }) != null) {
/* 177 */         recordSetTrans.commit();
/* 178 */         return true;
/*     */       } 
/* 180 */       recordSetTrans.rollback();
/*     */     } 
/* 182 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object replaceMap(Map<Object, Object> paramMap, Object paramObject1, Object paramObject2) {
/* 198 */     if (paramMap.containsKey(paramObject1)) {
/* 199 */       return paramMap.put(paramObject1, paramObject2);
/*     */     }
/* 201 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/UpdateManagerStr.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */