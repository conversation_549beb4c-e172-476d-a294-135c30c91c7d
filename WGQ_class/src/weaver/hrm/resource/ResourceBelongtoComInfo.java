/*     */ package weaver.hrm.resource;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ public class ResourceBelongtoComInfo
/*     */   extends CacheBase
/*     */ {
/*  21 */   protected static String TABLE_NAME = "hrmresource";
/*     */   
/*  23 */   protected static String TABLE_WHERE = null;
/*     */   
/*  25 */   protected static String TABLE_ORDER = "dsporder asc,id asc";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  28 */   protected static String PK_NAME = "id";
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "belongtouser")
/*     */   protected static int belongtouser;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean autoInitIfNotFound() {
/*  39 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   protected CacheMap initCache() throws Exception {
/*  44 */     CacheMap cacheMap = createCacheMap();
/*  45 */     RecordSet recordSet = new RecordSet();
/*  46 */     ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/*  47 */     String str1 = "";
/*  48 */     String str2 = " select id, belongto from Hrmresource a where belongto > 0 and status<4 order by dsporder asc,id asc ";
/*  49 */     recordSet.execute(str2);
/*  50 */     while (recordSet.next()) {
/*  51 */       String str = recordSet.getString("belongto");
/*  52 */       if (concurrentHashMap.get(str) == null) {
/*  53 */         str1 = "";
/*     */       } else {
/*  55 */         str1 = (String)concurrentHashMap.get(str);
/*     */       } 
/*  57 */       if (str1.length() > 0) str1 = str1 + ","; 
/*  58 */       str1 = str1 + recordSet.getString("id");
/*  59 */       if (Util.null2String(str1).length() > 0) {
/*  60 */         concurrentHashMap.put(str, str1);
/*     */       }
/*     */     } 
/*     */     
/*  64 */     for (Map.Entry<Object, Object> entry : concurrentHashMap.entrySet()) {
/*  65 */       CacheItem cacheItem = createCacheItem();
/*  66 */       String str = (String)entry.getKey();
/*  67 */       str1 = (String)entry.getValue();
/*  68 */       if (Util.null2String(str1).length() == 0)
/*  69 */         continue;  cacheItem.set(0, str);
/*  70 */       cacheItem.set(belongtouser, str1);
/*  71 */       modifyCacheItem(str, cacheItem);
/*  72 */       cacheMap.put(str, cacheItem);
/*     */     } 
/*  74 */     return cacheMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected CacheItem initCache(String paramString) {
/*  79 */     if (Util.null2String(paramString).equals("-1") || Util.null2String(paramString).equals("0")) {
/*  80 */       return null;
/*     */     }
/*  82 */     String str1 = "";
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     String str2 = " select id, belongto,status from Hrmresource a where belongto=" + paramString + " and belongto > 0 order by dsporder asc,id asc ";
/*  85 */     recordSet.execute(str2);
/*  86 */     boolean bool = false;
/*  87 */     while (recordSet.next()) {
/*  88 */       String str = recordSet.getString("status");
/*  89 */       if ("0,1,2,3".indexOf(str) < 0) {
/*  90 */         bool = true;
/*     */         continue;
/*     */       } 
/*  93 */       if (str1.length() > 0) str1 = str1 + ","; 
/*  94 */       str1 = str1 + recordSet.getString("id");
/*     */     } 
/*  96 */     if (Util.null2String(str1).length() == 0 && bool) {
/*  97 */       deleteCache(paramString);
/*  98 */       return null;
/*     */     } 
/* 100 */     CacheItem cacheItem = createCacheItem();
/* 101 */     cacheItem.set(0, paramString);
/* 102 */     cacheItem.set(belongtouser, str1);
/* 103 */     modifyCacheItem(paramString, cacheItem);
/* 104 */     return cacheItem;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getResourceBelongtoNum() {
/* 112 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/* 122 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getId() {
/* 130 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getBelongtousers() {
/* 138 */     String str = (String)getRowValue(belongtouser);
/* 139 */     ArrayList<User> arrayList = new ArrayList();
/* 140 */     User user = null;
/* 141 */     String[] arrayOfString = str.split(",");
/* 142 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 143 */       user = User.getUser(Util.getIntValue(arrayOfString[b]), 0);
/* 144 */       if (user != null)
/* 145 */         arrayList.add(user); 
/*     */     } 
/* 147 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getBelongtousers(String paramString) {
/* 155 */     String str = (String)getValue(belongtouser, paramString);
/* 156 */     ArrayList<User> arrayList = new ArrayList();
/* 157 */     User user = null;
/* 158 */     String[] arrayOfString = str.split(",");
/* 159 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 160 */       user = User.getUser(Util.getIntValue(arrayOfString[b]), 0);
/* 161 */       if (user != null)
/* 162 */         arrayList.add(user); 
/*     */     } 
/* 164 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBelongtoids() {
/* 183 */     return ((String)getRowValue(belongtouser)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBelongtoids(String paramString) {
/* 203 */     return ((String)getValue(belongtouser, paramString)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeResourceBelongtoCache() {
/* 210 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resource/ResourceBelongtoComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */