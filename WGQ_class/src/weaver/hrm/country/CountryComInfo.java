/*     */ package weaver.hrm.country;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class CountryComInfo
/*     */   extends CacheBase
/*     */ {
/*  12 */   protected static String TABLE_NAME = "HrmCountry";
/*     */   
/*  14 */   protected static String TABLE_WHERE = null;
/*     */   
/*  16 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  19 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "countryname")
/*     */   protected static int countryname;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "countrydesc")
/*     */   protected static int countrydesc;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "canceled")
/*     */   protected static int canceled;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "quicksearch")
/*     */   protected static int quicksearch;
/*     */ 
/*     */   
/*     */   public int getCountryNum() {
/*  39 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  48 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCountryid() {
/*  56 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCountryiscanceled() {
/*  64 */     return (String)getRowValue(canceled);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCountryname() {
/*  72 */     return (String)getRowValue(countryname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCountryname(String paramString) {
/*  82 */     return (String)getValue(countryname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCountrydesc() {
/*  90 */     return (String)getRowValue(countrydesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCountrydesc(String paramString) {
/* 100 */     return (String)getValue(countrydesc, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQuickSearch() {
/* 108 */     return (String)getRowValue(quicksearch);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQuickSearch(String paramString) {
/* 118 */     return (String)getValue(quicksearch, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCountryCache() {
/* 125 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/country/CountryComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */