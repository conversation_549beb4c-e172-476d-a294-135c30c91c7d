/*     */ package weaver.hrm.country;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.hrm.common.Tools;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CountryManager
/*     */ {
/*     */   public TreeNode getCountryTree(TreeNode paramTreeNode, ArrayList<TreeNode> paramArrayList, String paramString) throws Exception {
/*  25 */     Iterator<TreeNode> iterator = paramArrayList.iterator();
/*  26 */     while (iterator.hasNext()) {
/*  27 */       TreeNode treeNode = iterator.next();
/*     */       
/*  29 */       if ((treeNode.getTreeNode()).length > 0 && travel(paramTreeNode, treeNode, true)) {
/*  30 */         paramArrayList.remove(treeNode);
/*  31 */         iterator = paramArrayList.iterator();
/*     */         
/*     */         continue;
/*     */       } 
/*  35 */       String str1 = treeNode.getNodeId();
/*  36 */       String str2 = str1.substring(str1.lastIndexOf('_') + 1);
/*  37 */       if (str2.equals(paramString)) {
/*     */         continue;
/*     */       }
/*  40 */       if (!str2.equals("") && !str2.equals("0")) {
/*  41 */         TreeNode treeNode1 = new TreeNode();
/*     */         
/*  43 */         treeNode1.setTitle((new CountryComInfo()).getCountryname(str2));
/*  44 */         treeNode1.setNodeId("country_" + str2);
/*     */         
/*  46 */         String str = null;
/*  47 */         if ("0".equals(str2)) {
/*  48 */           str = "/images/treeimages/home16_wev8.gif";
/*     */         } else {
/*  50 */           str = "/LeftMenu/ThemeXP/page_wev8.gif";
/*     */         } 
/*     */         
/*  53 */         treeNode1.setIcon(str);
/*  54 */         treeNode1.setValue(str2);
/*     */         
/*  56 */         treeNode1.setHref("javascript:setCountry('" + treeNode1.getNodeId() + "')");
/*  57 */         treeNode1.setTarget("_self");
/*     */         
/*  59 */         getCountryTree(treeNode1, str2, 0, 2, paramString, treeNode, paramArrayList);
/*  60 */         paramArrayList.add(0, treeNode1);
/*  61 */         getCountryTree(paramTreeNode, paramArrayList, paramString);
/*     */       } else {
/*  63 */         getCountryTree(paramTreeNode, "0", 0, 2, paramString, treeNode, paramArrayList);
/*     */       } 
/*  65 */       iterator = paramArrayList.iterator();
/*     */     } 
/*  67 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getCountryTree(TreeNode paramTreeNode1, String paramString1, int paramInt1, int paramInt2, String paramString2, TreeNode paramTreeNode2, ArrayList paramArrayList) throws Exception {
/*  82 */     int i = paramInt1;
/*  83 */     CountryComInfo countryComInfo = new CountryComInfo();
/*  84 */     countryComInfo.setTofirstRow();
/*     */     
/*  86 */     i++;
/*     */     
/*  88 */     String str1 = "0";
/*  89 */     String str2 = "";
/*  90 */     TreeNode treeNode = null;
/*  91 */     while (countryComInfo.next() && i < paramInt2) {
/*  92 */       if (!str1.equals(paramString1) || Tools.vString(countryComInfo.getCountryiscanceled()).equals("1")) {
/*     */         continue;
/*     */       }
/*  95 */       str2 = countryComInfo.getCountryid();
/*  96 */       treeNode = new TreeNode();
/*  97 */       treeNode.setTitle(toScreen(countryComInfo.getCountryname()));
/*  98 */       treeNode.setNodeId("country_" + str2);
/*     */       
/* 100 */       if (i == paramInt2 - 1) {
/* 101 */         treeNode.setNodeXmlSrc("/hrm/province/HrmProvince_leftXml.jsp?cid=" + str2);
/*     */       }
/*     */       
/* 104 */       treeNode.setHref("javascript:setCountry('" + treeNode.getNodeId() + "')");
/* 105 */       treeNode.setTarget("_self");
/*     */       
/* 107 */       if (paramTreeNode2 != null && (paramTreeNode2.getTreeNode()).length > 0 && paramTreeNode2.equals(treeNode)) {
/* 108 */         treeNode = paramTreeNode2;
/*     */       }
/* 110 */       if (!str2.equals(paramString2)) {
/* 111 */         paramTreeNode1.addTreeNode(treeNode);
/*     */       }
/* 113 */       if (paramArrayList != null) {
/* 114 */         paramArrayList.remove(treeNode);
/*     */       }
/*     */       
/* 117 */       getCountryTree(treeNode, str2, i, paramInt2, paramString2, paramTreeNode2, paramArrayList);
/*     */     } 
/* 119 */     return paramTreeNode1;
/*     */   }
/*     */   
/*     */   private boolean travel(TreeNode paramTreeNode1, TreeNode paramTreeNode2, boolean paramBoolean) {
/* 123 */     if (paramTreeNode1.equals(paramTreeNode2)) {
/* 124 */       if (paramBoolean) {
/* 125 */         if ((paramTreeNode2.getTreeNode()).length > 0)
/* 126 */           paramTreeNode1.setNodeXmlSrc(null); 
/* 127 */         paramTreeNode1.setTreeNode(paramTreeNode2.getTreeNode());
/*     */       } 
/* 129 */       return true;
/*     */     } 
/* 131 */     TreeNode[] arrayOfTreeNode = paramTreeNode1.getTreeNode();
/* 132 */     for (byte b = 0; b < arrayOfTreeNode.length; b++) {
/* 133 */       if (travel(arrayOfTreeNode[b], paramTreeNode2, paramBoolean)) {
/* 134 */         return true;
/*     */       }
/*     */     } 
/* 137 */     return false;
/*     */   }
/*     */   
/*     */   private static String toScreen(String paramString) {
/* 141 */     String str = Tools.vString(paramString);
/* 142 */     str = Tools.replace(str, "&lt;", "<");
/* 143 */     str = Tools.replace(str, "&gt;", ">");
/* 144 */     str = Tools.replace(str, "&quot;", "\"");
/* 145 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/country/CountryManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */