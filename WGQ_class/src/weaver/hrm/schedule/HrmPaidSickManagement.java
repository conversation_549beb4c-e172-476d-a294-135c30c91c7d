/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmPaidSickManagement
/*     */ {
/*     */   public static float getPaidSickDays(HashMap paramHashMap, int[] paramArrayOfint, int paramInt) {
/*  23 */     float f = 0.0F;
/*  24 */     int i = -1;
/*     */     
/*  26 */     for (byte b = 0; b < paramArrayOfint.length; b++) {
/*  27 */       if (paramArrayOfint[b] <= paramInt) {
/*  28 */         i = paramArrayOfint[b];
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*  33 */     if (i > -1) {
/*  34 */       f = Util.getFloatValue(Util.null2String((String)paramHashMap.get(i + "")), 0.0F);
/*     */     }
/*     */     
/*  37 */     return f;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBatchProcess(String paramString1, String paramString2) throws Exception {
/*  49 */     return getBatchProcess(paramString1, paramString2, "-12");
/*     */   }
/*     */   public static String getBatchProcess(String paramString1, String paramString2, String paramString3) throws Exception {
/*  52 */     RecordSet recordSet = new RecordSet();
/*  53 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */     
/*  55 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  56 */     recordSet.executeSql("select distinct subcompanyid from HrmPSLBatchProcess " + ("".equals(paramString3) ? "" : (" where leavetype=" + paramString3)));
/*  57 */     while (recordSet.next()) {
/*  58 */       hashMap.put(recordSet.getString("subcompanyid"), recordSet.getString("subcompanyid"));
/*     */     }
/*     */     
/*  61 */     String str = paramString1 + "," + subCompanyComInfo.getAllSupCompany(paramString1) + "0";
/*  62 */     String[] arrayOfString = Util.TokenizerString2(str, ",");
/*  63 */     if (arrayOfString != null) {
/*  64 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  65 */         if (hashMap.containsKey(arrayOfString[b])) return arrayOfString[b];
/*     */       
/*     */       } 
/*     */     }
/*  69 */     return "-1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getUserPaidSickInfo(String paramString1, String paramString2) throws Exception {
/*  79 */     return getUserPaidSickInfo(paramString1, paramString2, "-12");
/*     */   }
/*     */   public static String getUserPaidSickInfo(String paramString1, String paramString2, String paramString3) throws Exception {
/*  82 */     RecordSet recordSet = new RecordSet();
/*  83 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  84 */     String str1 = "";
/*  85 */     String str2 = "";
/*  86 */     String str3 = "";
/*  87 */     String str4 = "";
/*  88 */     String str5 = "";
/*  89 */     float f = 0.0F;
/*  90 */     String str6 = paramString2.substring(0, 4);
/*  91 */     String str7 = (Util.getIntValue(str6) - 1) + "";
/*  92 */     String str8 = resourceComInfo.getSubCompanyID(paramString1);
/*     */ 
/*     */     
/*  95 */     str5 = getPaidSickPeriod(str8, str6, paramString3);
/*  96 */     str3 = Util.TokenizerString2(str5, "#")[0];
/*  97 */     str4 = Util.TokenizerString2(str5, "#")[1];
/*  98 */     if (str3.compareTo(paramString2) < 1 && str4.compareTo(paramString2) > -1) {
/*  99 */       str1 = "select * from HrmPSLManagement where resourceid = '" + paramString1 + "' and pslyear = '" + str6 + "'" + ("".equals(paramString3) ? "" : (" and leavetype=" + paramString3));
/* 100 */       recordSet.executeSql(str1);
/* 101 */       if (recordSet.next()) {
/* 102 */         str2 = "" + Util.round(recordSet.getFloat("psldays") + "", 2);
/* 103 */         f = recordSet.getFloat("psldays");
/*     */       } else {
/* 105 */         str2 = "0.0";
/*     */       } 
/*     */     } else {
/* 108 */       str2 = "0.0";
/*     */     } 
/*     */ 
/*     */     
/* 112 */     str5 = getPaidSickPeriod(str8, str7, paramString3);
/* 113 */     str3 = Util.TokenizerString2(str5, "#")[0];
/* 114 */     str4 = Util.TokenizerString2(str5, "#")[1];
/* 115 */     if (str3.compareTo(paramString2) < 1 && str4.compareTo(paramString2) > -1) {
/* 116 */       str1 = "select * from HrmPSLManagement where resourceid = '" + paramString1 + "' and pslyear = '" + str7 + "' " + ("".equals(paramString3) ? "" : (" and leavetype=" + paramString3));
/* 117 */       recordSet.executeSql(str1);
/* 118 */       if (recordSet.next()) {
/* 119 */         str2 = str2 + "#" + Util.round(recordSet.getFloat("psldays") + "", 2);
/* 120 */         f += recordSet.getFloat("psldays");
/*     */       } else {
/* 122 */         str2 = str2 + "#0.0";
/*     */       } 
/*     */     } else {
/* 125 */       str2 = str2 + "#0.0";
/*     */     } 
/*     */     
/* 128 */     str2 = str2 + "#" + Util.round(f + "", 2);
/*     */     
/* 130 */     return str2;
/*     */   }
/*     */   
/*     */   public static String getPaidSickPeriod(String paramString1, String paramString2) throws Exception {
/* 134 */     return getPaidSickPeriod(paramString1, paramString2, "-12");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getPaidSickPeriod(String paramString1, String paramString2, String paramString3) throws Exception {
/* 144 */     String str1 = "";
/* 145 */     String str2 = "";
/* 146 */     RecordSet recordSet = new RecordSet();
/* 147 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */     
/* 149 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 150 */     str1 = "select distinct subcompanyid from HrmPSLPeriod where PSLyear = '" + paramString2 + "' " + ("".equals(paramString3) ? "" : (" and leavetype=" + paramString3));
/* 151 */     recordSet.executeSql(str1);
/* 152 */     while (recordSet.next()) {
/* 153 */       hashMap.put(recordSet.getString("subcompanyid"), recordSet.getString("subcompanyid"));
/*     */     }
/*     */     
/* 156 */     String str3 = paramString1 + "," + subCompanyComInfo.getAllSupCompany(paramString1) + "0";
/*     */     
/* 158 */     String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 159 */     if (arrayOfString != null) {
/* 160 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 161 */         if (hashMap.containsKey(arrayOfString[b])) {
/* 162 */           str2 = arrayOfString[b];
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     }
/* 168 */     if (!str2.equals("")) {
/* 169 */       str1 = "select * from HrmPSLPeriod where PSLyear = '" + paramString2 + "' and subcompanyid = '" + str2 + "' " + ("".equals(paramString3) ? "" : (" and leavetype=" + paramString3));
/* 170 */       recordSet.executeSql(str1);
/* 171 */       if (recordSet.next()) {
/* 172 */         return recordSet.getString("startdate") + "#" + recordSet.getString("enddate");
/*     */       }
/*     */     } 
/*     */     
/* 176 */     return paramString2 + "-01-01#" + (Util.getIntValue(paramString2) + 1) + "-12-31";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLeaveColor(String paramString) throws Exception {
/* 186 */     String str1 = "";
/* 187 */     String str2 = "";
/* 188 */     RecordSet recordSet = new RecordSet();
/* 189 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 190 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 191 */     str1 = "select distinct subcompanyid from hrmleavetypecolor";
/* 192 */     recordSet.executeSql(str1);
/* 193 */     while (recordSet.next()) {
/* 194 */       hashMap.put(recordSet.getString("subcompanyid"), recordSet.getString("subcompanyid"));
/*     */     }
/*     */     
/* 197 */     String str3 = paramString + "," + subCompanyComInfo.getAllSupCompany(paramString) + "0";
/*     */     
/* 199 */     String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 200 */     if (arrayOfString != null) {
/* 201 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 202 */         if (hashMap.containsKey(arrayOfString[b])) {
/* 203 */           str2 = arrayOfString[b];
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     }
/* 209 */     return str2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmPaidSickManagement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */