/*    */ package weaver.hrm.schedule;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmTimeCardManage
/*    */   extends BaseBean
/*    */ {
/* 23 */   private RecordSet rt = null;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean initTimecardInfo(String paramString1, String paramString2) {
/* 32 */     HrmTimeCardInit hrmTimeCardInit = new HrmTimeCardInit();
/* 33 */     hrmTimeCardInit.initTimecardInfo(paramString1, paramString2);
/*    */     
/* 35 */     RecordSet recordSet = new RecordSet();
/* 36 */     int i = 0;
/* 37 */     recordSet.executeSql(" select count(id) from HrmRightCardInfo where carddate>='" + paramString1 + "' and carddate<='" + paramString2 + "' and islegal = 2 ");
/*    */     
/* 39 */     if (recordSet.next()) i = Util.getIntValue(recordSet.getString(1), 0);
/*    */     
/* 41 */     if (i > 0) return false; 
/* 42 */     return true;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmTimeCardManage.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */