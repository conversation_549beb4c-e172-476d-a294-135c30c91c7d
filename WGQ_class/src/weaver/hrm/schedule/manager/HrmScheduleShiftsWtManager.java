/*    */ package weaver.hrm.schedule.manager;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.framework.BaseDao;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.schedule.cache.HrmScheduleShiftsWtCache;
/*    */ import weaver.hrm.schedule.dao.HrmScheduleShiftsWtDao;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleShiftsWt;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftsWtManager
/*    */   extends BaseManager<HrmScheduleShiftsWt>
/*    */ {
/* 22 */   private HrmScheduleShiftsWtDao dao = null;
/*    */   
/*    */   public HrmScheduleShiftsWtManager() {
/* 25 */     this.dao = new HrmScheduleShiftsWtDao();
/* 26 */     setDao((BaseDao)this.dao);
/*    */   }
/*    */   
/*    */   public Long save(HrmScheduleShiftsWt paramHrmScheduleShiftsWt) {
/* 30 */     return save(paramHrmScheduleShiftsWt, false);
/*    */   }
/*    */   
/*    */   public Long save(HrmScheduleShiftsWt paramHrmScheduleShiftsWt, boolean paramBoolean) {
/* 34 */     String str = String.valueOf(paramHrmScheduleShiftsWt.getId());
/* 35 */     if (paramBoolean || str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmScheduleShiftsWt)); }
/* 36 */     else { update(paramHrmScheduleShiftsWt); }
/* 37 */      return Long.valueOf(str);
/*    */   }
/*    */   
/*    */   public int count(String paramString) {
/* 41 */     return this.dao.count(getMapParam(paramString));
/*    */   }
/*    */   
/*    */   public void delete(Map<String, Comparable> paramMap) {
/* 45 */     this.dao.delete(paramMap);
/*    */   }
/*    */   
/*    */   private List<HrmScheduleShiftsWt> getList(String paramString1, String paramString2) {
/* 49 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/* 50 */     ArrayList<HrmScheduleShiftsWt> arrayList = new ArrayList();
/* 51 */     List list = (new HrmScheduleShiftsWtCache()).getResult();
/* 52 */     for (String str : arrayOfString) {
/* 53 */       for (HrmScheduleShiftsWt hrmScheduleShiftsWt : list) {
/* 54 */         if (str.equals(StringUtil.vString(hrmScheduleShiftsWt.getId()))) {
/* 55 */           arrayList.add(hrmScheduleShiftsWt);
/*    */           break;
/*    */         } 
/*    */       } 
/*    */     } 
/* 60 */     return arrayList;
/*    */   }
/*    */   
/*    */   public String getDescription(String paramString) {
/* 64 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*    */   }
/*    */   
/*    */   public String getDescription(String paramString1, String paramString2) {
/* 68 */     String str = getAppendStr(paramString2);
/* 69 */     StringBuffer stringBuffer = new StringBuffer();
/* 70 */     List<HrmScheduleShiftsWt> list = getList(paramString1, paramString2);
/* 71 */     for (HrmScheduleShiftsWt hrmScheduleShiftsWt : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmScheduleShiftsWt.getField003()); 
/* 72 */     return stringBuffer.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmScheduleShiftsWtManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */