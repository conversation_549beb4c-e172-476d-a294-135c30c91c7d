/*    */ package weaver.hrm.schedule.manager;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.framework.BaseDao;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.schedule.cache.HrmScheduleSetPersonCache;
/*    */ import weaver.hrm.schedule.dao.HrmScheduleSetPersonDao;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleSetPerson;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleSetPersonManager
/*    */   extends BaseManager<HrmScheduleSetPerson>
/*    */ {
/* 22 */   private HrmScheduleSetPersonDao dao = null;
/*    */   
/*    */   public HrmScheduleSetPersonManager() {
/* 25 */     this.dao = new HrmScheduleSetPersonDao();
/* 26 */     setDao((BaseDao)this.dao);
/*    */   }
/*    */   
/*    */   public Long save(HrmScheduleSetPerson paramHrmScheduleSetPerson) {
/* 30 */     return save(paramHrmScheduleSetPerson, false);
/*    */   }
/*    */   
/*    */   public Long save(HrmScheduleSetPerson paramHrmScheduleSetPerson, boolean paramBoolean) {
/* 34 */     String str = String.valueOf(paramHrmScheduleSetPerson.getId());
/* 35 */     if (paramBoolean || str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmScheduleSetPerson)); }
/* 36 */     else { update(paramHrmScheduleSetPerson); }
/* 37 */      return Long.valueOf(str);
/*    */   }
/*    */   
/*    */   public int count(String paramString) {
/* 41 */     return this.dao.count(getMapParam(paramString));
/*    */   }
/*    */   
/*    */   public void delete(Map<String, Comparable> paramMap) {
/* 45 */     this.dao.delete(paramMap);
/*    */   }
/*    */   
/*    */   private List<HrmScheduleSetPerson> getList(String paramString1, String paramString2) {
/* 49 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/* 50 */     ArrayList<HrmScheduleSetPerson> arrayList = new ArrayList();
/* 51 */     List list = (new HrmScheduleSetPersonCache()).getResult();
/* 52 */     for (String str : arrayOfString) {
/* 53 */       for (HrmScheduleSetPerson hrmScheduleSetPerson : list) {
/* 54 */         if (str.equals(StringUtil.vString(hrmScheduleSetPerson.getId()))) {
/* 55 */           arrayList.add(hrmScheduleSetPerson);
/*    */           break;
/*    */         } 
/*    */       } 
/*    */     } 
/* 60 */     return arrayList;
/*    */   }
/*    */   
/*    */   public String getDescription(String paramString) {
/* 64 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*    */   }
/*    */   
/*    */   public String getDescription(String paramString1, String paramString2) {
/* 68 */     String str = getAppendStr(paramString2);
/* 69 */     StringBuffer stringBuffer = new StringBuffer();
/* 70 */     List<HrmScheduleSetPerson> list = getList(paramString1, paramString2);
/* 71 */     for (HrmScheduleSetPerson hrmScheduleSetPerson : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmScheduleSetPerson.getField002()); 
/* 72 */     return stringBuffer.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmScheduleSetPersonManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */