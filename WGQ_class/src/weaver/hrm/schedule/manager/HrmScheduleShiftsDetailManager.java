/*     */ package weaver.hrm.schedule.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleShiftsDetailCache;
/*     */ import weaver.hrm.schedule.dao.HrmScheduleShiftsDetailDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsDetail;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleShiftsDetailManager
/*     */   extends BaseManager<HrmScheduleShiftsDetail>
/*     */ {
/*  27 */   private HrmScheduleShiftsDetailDao dao = null;
/*     */   
/*     */   public HrmScheduleShiftsDetailManager() {
/*  30 */     this.dao = new HrmScheduleShiftsDetailDao();
/*  31 */     setDao((BaseDao)this.dao);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail) {
/*  35 */     return save(paramHrmScheduleShiftsDetail, false);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail, boolean paramBoolean) {
/*  39 */     String str = String.valueOf(paramHrmScheduleShiftsDetail.getId());
/*  40 */     if (paramBoolean || str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmScheduleShiftsDetail)); }
/*  41 */     else { update(paramHrmScheduleShiftsDetail); }
/*  42 */      return Long.valueOf(str);
/*     */   }
/*     */   
/*     */   public int count(String paramString) {
/*  46 */     return this.dao.count(getMapParam(paramString));
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/*  50 */     this.dao.delete(paramMap);
/*     */   }
/*     */   
/*     */   public String getDateValue(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10, String paramString11, String paramString12, String paramString13, String paramString14, String paramString15, String paramString16, String paramString17, String paramString18, String paramString19, String paramString20, String paramString21, String paramString22, String paramString23, String paramString24, String paramString25, String paramString26, String paramString27, String paramString28, String paramString29, String paramString30, String paramString31, String paramString32, String paramString33, String paramString34, String paramString35, String paramString36, String paramString37, String paramString38, String paramString39, String paramString40, String paramString41, String paramString42, String paramString43) {
/*  54 */     String str1 = "";
/*  55 */     String str2 = getAppendStr(paramString1);
/*  56 */     int[] arrayOfInt = { 398, 392, 393, 394, 395, 396, 397 };
/*  57 */     String[] arrayOfString = { paramString43, paramString37, paramString38, paramString39, paramString40, paramString41, paramString42 };
/*  58 */     for (byte b = 0; b < arrayOfString.length; ) { if (StringUtil.parseToInt(arrayOfString[b]) == 1) str1 = str1 + ((str1.length() == 0) ? "" : str2) + getLabelName(Integer.valueOf(arrayOfInt[b]), paramString1);  b++; }
/*     */     
/*  60 */     if (StringUtil.isNull(str1)) {
/*  61 */       String str = getLabelName(Integer.valueOf(390), paramString1);
/*  62 */       arrayOfString = new String[] { paramString6, paramString7, paramString8, paramString9, paramString10, paramString11, paramString12, paramString13, paramString14, paramString15, paramString16, paramString17, paramString18, paramString19, paramString20, paramString21, paramString22, paramString23, paramString24, paramString25, paramString26, paramString27, paramString28, paramString29, paramString30, paramString31, paramString32, paramString33, paramString34, paramString35, paramString36 };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  68 */       for (byte b1 = 0; b1 < arrayOfString.length; ) { if (StringUtil.parseToInt(arrayOfString[b1]) == 1) str1 = str1 + ((str1.length() == 0) ? "" : str2) + (b1 + 1) + str;  b1++; }
/*     */     
/*  70 */     }  return str1;
/*     */   }
/*     */   
/*     */   public String getDateValue(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail, String paramString) {
/*  74 */     return getDateValue(paramString, String.valueOf(paramHrmScheduleShiftsDetail.getT2Field003()), String.valueOf(paramHrmScheduleShiftsDetail.getField003()), String.valueOf(paramHrmScheduleShiftsDetail.getField004()), String.valueOf(paramHrmScheduleShiftsDetail.getField005()), String.valueOf(paramHrmScheduleShiftsDetail.getD001()), String.valueOf(paramHrmScheduleShiftsDetail.getD002()), String.valueOf(paramHrmScheduleShiftsDetail.getD003()), String.valueOf(paramHrmScheduleShiftsDetail.getD004()), String.valueOf(paramHrmScheduleShiftsDetail.getD005()), String.valueOf(paramHrmScheduleShiftsDetail.getD006()), String.valueOf(paramHrmScheduleShiftsDetail.getD007()), String.valueOf(paramHrmScheduleShiftsDetail.getD008()), String.valueOf(paramHrmScheduleShiftsDetail.getD009()), String.valueOf(paramHrmScheduleShiftsDetail.getD010()), String.valueOf(paramHrmScheduleShiftsDetail.getD011()), String.valueOf(paramHrmScheduleShiftsDetail.getD012()), String.valueOf(paramHrmScheduleShiftsDetail.getD013()), String.valueOf(paramHrmScheduleShiftsDetail.getD014()), String.valueOf(paramHrmScheduleShiftsDetail.getD015()), String.valueOf(paramHrmScheduleShiftsDetail.getD016()), String.valueOf(paramHrmScheduleShiftsDetail.getD017()), String.valueOf(paramHrmScheduleShiftsDetail.getD018()), String.valueOf(paramHrmScheduleShiftsDetail.getD019()), String.valueOf(paramHrmScheduleShiftsDetail.getD020()), String.valueOf(paramHrmScheduleShiftsDetail.getD021()), String.valueOf(paramHrmScheduleShiftsDetail.getD022()), String.valueOf(paramHrmScheduleShiftsDetail.getD023()), String.valueOf(paramHrmScheduleShiftsDetail.getD024()), String.valueOf(paramHrmScheduleShiftsDetail.getD025()), String.valueOf(paramHrmScheduleShiftsDetail.getD026()), String.valueOf(paramHrmScheduleShiftsDetail.getD027()), String.valueOf(paramHrmScheduleShiftsDetail.getD028()), String.valueOf(paramHrmScheduleShiftsDetail.getD029()), String.valueOf(paramHrmScheduleShiftsDetail.getD030()), String.valueOf(paramHrmScheduleShiftsDetail.getD031()), String.valueOf(paramHrmScheduleShiftsDetail.getW001()), String.valueOf(paramHrmScheduleShiftsDetail.getW002()), String.valueOf(paramHrmScheduleShiftsDetail.getW003()), String.valueOf(paramHrmScheduleShiftsDetail.getW004()), String.valueOf(paramHrmScheduleShiftsDetail.getW005()), String.valueOf(paramHrmScheduleShiftsDetail.getW006()), String.valueOf(paramHrmScheduleShiftsDetail.getW007()));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean check(int paramInt, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail1, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail2) {
/*  88 */     boolean bool = false;
/*  89 */     if (paramInt == 0) {
/*  90 */       bool = wCheck(paramHrmScheduleShiftsDetail1, paramHrmScheduleShiftsDetail2);
/*     */     } else {
/*  92 */       bool = dCheck(paramHrmScheduleShiftsDetail1, paramHrmScheduleShiftsDetail2);
/*     */     } 
/*  94 */     return bool;
/*     */   }
/*     */   
/*     */   private boolean wCheck(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail1, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail2) {
/*  98 */     if (paramHrmScheduleShiftsDetail1.getW001().intValue() == 1 && paramHrmScheduleShiftsDetail2.getW001().intValue() == 1) return true; 
/*  99 */     if (paramHrmScheduleShiftsDetail1.getW002().intValue() == 1 && paramHrmScheduleShiftsDetail2.getW002().intValue() == 1) return true; 
/* 100 */     if (paramHrmScheduleShiftsDetail1.getW003().intValue() == 1 && paramHrmScheduleShiftsDetail2.getW003().intValue() == 1) return true; 
/* 101 */     if (paramHrmScheduleShiftsDetail1.getW004().intValue() == 1 && paramHrmScheduleShiftsDetail2.getW004().intValue() == 1) return true; 
/* 102 */     if (paramHrmScheduleShiftsDetail1.getW005().intValue() == 1 && paramHrmScheduleShiftsDetail2.getW005().intValue() == 1) return true; 
/* 103 */     if (paramHrmScheduleShiftsDetail1.getW006().intValue() == 1 && paramHrmScheduleShiftsDetail2.getW006().intValue() == 1) return true; 
/* 104 */     if (paramHrmScheduleShiftsDetail1.getW007().intValue() == 1 && paramHrmScheduleShiftsDetail2.getW007().intValue() == 1) return true; 
/* 105 */     return false;
/*     */   }
/*     */   
/*     */   private boolean dCheck(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail1, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail2) {
/* 109 */     if (paramHrmScheduleShiftsDetail1.getD001().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD001().intValue() == 1) return true; 
/* 110 */     if (paramHrmScheduleShiftsDetail1.getD002().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD002().intValue() == 1) return true; 
/* 111 */     if (paramHrmScheduleShiftsDetail1.getD003().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD003().intValue() == 1) return true; 
/* 112 */     if (paramHrmScheduleShiftsDetail1.getD004().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD004().intValue() == 1) return true; 
/* 113 */     if (paramHrmScheduleShiftsDetail1.getD005().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD005().intValue() == 1) return true; 
/* 114 */     if (paramHrmScheduleShiftsDetail1.getD006().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD006().intValue() == 1) return true; 
/* 115 */     if (paramHrmScheduleShiftsDetail1.getD007().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD007().intValue() == 1) return true; 
/* 116 */     if (paramHrmScheduleShiftsDetail1.getD008().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD008().intValue() == 1) return true; 
/* 117 */     if (paramHrmScheduleShiftsDetail1.getD009().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD009().intValue() == 1) return true; 
/* 118 */     if (paramHrmScheduleShiftsDetail1.getD010().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD010().intValue() == 1) return true; 
/* 119 */     if (paramHrmScheduleShiftsDetail1.getD011().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD011().intValue() == 1) return true; 
/* 120 */     if (paramHrmScheduleShiftsDetail1.getD012().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD012().intValue() == 1) return true; 
/* 121 */     if (paramHrmScheduleShiftsDetail1.getD013().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD013().intValue() == 1) return true; 
/* 122 */     if (paramHrmScheduleShiftsDetail1.getD014().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD014().intValue() == 1) return true; 
/* 123 */     if (paramHrmScheduleShiftsDetail1.getD015().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD015().intValue() == 1) return true; 
/* 124 */     if (paramHrmScheduleShiftsDetail1.getD016().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD016().intValue() == 1) return true; 
/* 125 */     if (paramHrmScheduleShiftsDetail1.getD017().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD017().intValue() == 1) return true; 
/* 126 */     if (paramHrmScheduleShiftsDetail1.getD018().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD018().intValue() == 1) return true; 
/* 127 */     if (paramHrmScheduleShiftsDetail1.getD019().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD019().intValue() == 1) return true; 
/* 128 */     if (paramHrmScheduleShiftsDetail1.getD020().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD020().intValue() == 1) return true; 
/* 129 */     if (paramHrmScheduleShiftsDetail1.getD021().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD021().intValue() == 1) return true; 
/* 130 */     if (paramHrmScheduleShiftsDetail1.getD022().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD022().intValue() == 1) return true; 
/* 131 */     if (paramHrmScheduleShiftsDetail1.getD023().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD023().intValue() == 1) return true; 
/* 132 */     if (paramHrmScheduleShiftsDetail1.getD024().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD024().intValue() == 1) return true; 
/* 133 */     if (paramHrmScheduleShiftsDetail1.getD025().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD025().intValue() == 1) return true; 
/* 134 */     if (paramHrmScheduleShiftsDetail1.getD026().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD026().intValue() == 1) return true; 
/* 135 */     if (paramHrmScheduleShiftsDetail1.getD027().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD027().intValue() == 1) return true; 
/* 136 */     if (paramHrmScheduleShiftsDetail1.getD028().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD028().intValue() == 1) return true; 
/* 137 */     if (paramHrmScheduleShiftsDetail1.getD029().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD029().intValue() == 1) return true; 
/* 138 */     if (paramHrmScheduleShiftsDetail1.getD030().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD030().intValue() == 1) return true; 
/* 139 */     if (paramHrmScheduleShiftsDetail1.getD031().intValue() == 1 && paramHrmScheduleShiftsDetail2.getD031().intValue() == 1) return true; 
/* 140 */     return false;
/*     */   }
/*     */   
/*     */   public void initBean(HttpServletRequest paramHttpServletRequest, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail) {
/* 144 */     paramHrmScheduleShiftsDetail.setD001(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd001"))));
/* 145 */     paramHrmScheduleShiftsDetail.setD002(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd002"))));
/* 146 */     paramHrmScheduleShiftsDetail.setD003(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd003"))));
/* 147 */     paramHrmScheduleShiftsDetail.setD004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd004"))));
/* 148 */     paramHrmScheduleShiftsDetail.setD005(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd005"))));
/* 149 */     paramHrmScheduleShiftsDetail.setD006(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd006"))));
/* 150 */     paramHrmScheduleShiftsDetail.setD007(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd007"))));
/* 151 */     paramHrmScheduleShiftsDetail.setD008(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd008"))));
/* 152 */     paramHrmScheduleShiftsDetail.setD009(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd009"))));
/* 153 */     paramHrmScheduleShiftsDetail.setD010(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd010"))));
/* 154 */     paramHrmScheduleShiftsDetail.setD011(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd011"))));
/* 155 */     paramHrmScheduleShiftsDetail.setD012(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd012"))));
/* 156 */     paramHrmScheduleShiftsDetail.setD013(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd013"))));
/* 157 */     paramHrmScheduleShiftsDetail.setD014(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd014"))));
/* 158 */     paramHrmScheduleShiftsDetail.setD015(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd015"))));
/* 159 */     paramHrmScheduleShiftsDetail.setD016(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd016"))));
/* 160 */     paramHrmScheduleShiftsDetail.setD017(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd017"))));
/* 161 */     paramHrmScheduleShiftsDetail.setD018(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd018"))));
/* 162 */     paramHrmScheduleShiftsDetail.setD019(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd019"))));
/* 163 */     paramHrmScheduleShiftsDetail.setD020(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd020"))));
/* 164 */     paramHrmScheduleShiftsDetail.setD021(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd021"))));
/* 165 */     paramHrmScheduleShiftsDetail.setD022(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd022"))));
/* 166 */     paramHrmScheduleShiftsDetail.setD023(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd023"))));
/* 167 */     paramHrmScheduleShiftsDetail.setD024(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd024"))));
/* 168 */     paramHrmScheduleShiftsDetail.setD025(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd025"))));
/* 169 */     paramHrmScheduleShiftsDetail.setD026(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd026"))));
/* 170 */     paramHrmScheduleShiftsDetail.setD027(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd027"))));
/* 171 */     paramHrmScheduleShiftsDetail.setD028(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd028"))));
/* 172 */     paramHrmScheduleShiftsDetail.setD029(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd029"))));
/* 173 */     paramHrmScheduleShiftsDetail.setD030(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd030"))));
/* 174 */     paramHrmScheduleShiftsDetail.setD031(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramd031"))));
/* 175 */     paramHrmScheduleShiftsDetail.setW001(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramw001"))));
/* 176 */     paramHrmScheduleShiftsDetail.setW002(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramw002"))));
/* 177 */     paramHrmScheduleShiftsDetail.setW003(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramw003"))));
/* 178 */     paramHrmScheduleShiftsDetail.setW004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramw004"))));
/* 179 */     paramHrmScheduleShiftsDetail.setW005(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramw005"))));
/* 180 */     paramHrmScheduleShiftsDetail.setW006(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramw006"))));
/* 181 */     paramHrmScheduleShiftsDetail.setW007(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("paramw007"))));
/* 182 */     paramHrmScheduleShiftsDetail.setField001(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field001"))));
/* 183 */     paramHrmScheduleShiftsDetail.setField002(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field002")));
/* 184 */     paramHrmScheduleShiftsDetail.setField003(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field003"))));
/* 185 */     paramHrmScheduleShiftsDetail.setField004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field004"))));
/* 186 */     paramHrmScheduleShiftsDetail.setField005(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field005"))));
/* 187 */     if (paramHrmScheduleShiftsDetail.getField003().intValue() == 1) paramHrmScheduleShiftsDetail.resetFieldValue(); 
/*     */   }
/*     */   
/*     */   private List<HrmScheduleShiftsDetail> getList(String paramString1, String paramString2) {
/* 191 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/* 192 */     ArrayList<HrmScheduleShiftsDetail> arrayList = new ArrayList();
/* 193 */     List list = (new HrmScheduleShiftsDetailCache()).getResult();
/* 194 */     for (String str : arrayOfString) {
/* 195 */       for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : list) {
/* 196 */         if (str.equals(StringUtil.vString(hrmScheduleShiftsDetail.getId()))) {
/* 197 */           arrayList.add(hrmScheduleShiftsDetail);
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 202 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString) {
/* 206 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString1, String paramString2) {
/* 210 */     String str = getAppendStr(paramString2);
/* 211 */     StringBuffer stringBuffer = new StringBuffer();
/* 212 */     List<HrmScheduleShiftsDetail> list = getList(paramString1, paramString2);
/* 213 */     for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmScheduleShiftsDetail.getField002()); 
/* 214 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getWorkTime(String paramString1, String paramString2) {
/* 218 */     return getWorkTime(paramString1, paramString2, -1, (int[])null);
/*     */   }
/*     */   
/*     */   public String getWorkTime(String paramString1, String paramString2, int paramInt, int[] paramArrayOfint) {
/* 222 */     long l = StringUtil.parseToLong(paramString1);
/* 223 */     List list = (new HrmScheduleShiftsDetailCache()).getResult();
/* 224 */     StringBuffer stringBuffer = new StringBuffer();
/* 225 */     boolean bool = ((paramInt == 0 || paramInt == 1) && paramArrayOfint != null && paramArrayOfint.length == 2) ? true : false;
/* 226 */     for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : list) {
/* 227 */       if (hrmScheduleShiftsDetail.getField001().longValue() != l)
/*     */         continue; 
/* 229 */       if (!StringUtil.isNotNull(hrmScheduleShiftsDetail.getField002()) || (
/* 230 */         bool && !hrmScheduleShiftsDetail.isSelect(paramInt, paramArrayOfint[paramInt])))
/* 231 */         continue;  stringBuffer.append((stringBuffer.length() == 0) ? "" : ";").append(hrmScheduleShiftsDetail.getField002());
/*     */     } 
/*     */     
/* 234 */     return getSelfWorkTime(stringBuffer.toString(), paramString2);
/*     */   }
/*     */   
/*     */   public String getSelfWorkTime(String paramString1, String paramString2) {
/* 238 */     return (new HrmScheduleWorktimeManager()).getWorkTime(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   private boolean isOverlap(String paramString1, String paramString2) {
/* 242 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 243 */     int i = 0;
/* 244 */     StringBuffer stringBuffer = (new StringBuffer(paramString1)).append(",").append(paramString2);
/* 245 */     String[] arrayOfString = stringBuffer.toString().split(",");
/* 246 */     for (String str : arrayOfString) {
/* 247 */       String[] arrayOfString1 = str.split("-");
/* 248 */       if (arrayOfString1.length == 2)
/*     */       {
/* 250 */         i += DateUtil.totalTime(arrayOfString1[0], arrayOfString1[1], hashMap); } 
/*     */     } 
/* 252 */     return !(i == hashMap.size());
/*     */   }
/*     */   
/*     */   public boolean checkAcrossTime(int paramInt, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail1, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail2, String paramString1, String paramString2) {
/* 256 */     String str = String.valueOf(getLanguageId());
/* 257 */     String[] arrayOfString = getSelfWorkTime(paramHrmScheduleShiftsDetail1.getField002(), str).split(getAppendStr(str));
/* 258 */     StringBuffer stringBuffer1 = new StringBuffer(), stringBuffer2 = new StringBuffer();
/* 259 */     for (String str1 : arrayOfString) {
/* 260 */       String[] arrayOfString1 = str1.split("-");
/* 261 */       if (arrayOfString1.length == 2)
/*     */       {
/* 263 */         if (arrayOfString1[0].compareTo(arrayOfString1[1]) > 0 && !arrayOfString1[1].equals("00:00")) {
/* 264 */           stringBuffer2.append((stringBuffer2.length() == 0) ? "" : ",").append(arrayOfString1[0]).append("-00:00");
/* 265 */           stringBuffer1.append((stringBuffer1.length() == 0) ? "" : ",").append("00:00-").append(arrayOfString1[1]);
/*     */         } else {
/* 267 */           stringBuffer2.append((stringBuffer2.length() == 0) ? "" : ",").append(arrayOfString1[0]).append("-").append(arrayOfString1[1]);
/*     */         }  } 
/*     */     } 
/* 270 */     boolean bool1 = (StringUtil.isNotNull(new String[] { stringBuffer1.toString(), paramString2 }) && isOverlap(stringBuffer1.toString(), paramString2)) ? true : false;
/* 271 */     boolean bool2 = (StringUtil.isNotNull(new String[] { stringBuffer2.toString(), paramString1 }) && isOverlap(stringBuffer2.toString(), paramString1)) ? true : false;
/* 272 */     return (paramInt == 0) ? wCheckAcrossTime(paramHrmScheduleShiftsDetail1, paramHrmScheduleShiftsDetail2, bool1, bool2) : dCheckAcrossTime(paramHrmScheduleShiftsDetail1, paramHrmScheduleShiftsDetail2, bool1, bool2);
/*     */   }
/*     */   
/*     */   private boolean wCheckAcrossTime(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail1, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail2, boolean paramBoolean1, boolean paramBoolean2) {
/* 276 */     if (paramHrmScheduleShiftsDetail2.getW007().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getW006().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getW001().intValue() == 1 && paramBoolean2))) return true; 
/* 277 */     if (paramHrmScheduleShiftsDetail2.getW001().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getW007().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getW002().intValue() == 1 && paramBoolean2))) return true; 
/* 278 */     if (paramHrmScheduleShiftsDetail2.getW002().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getW001().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getW003().intValue() == 1 && paramBoolean2))) return true; 
/* 279 */     if (paramHrmScheduleShiftsDetail2.getW003().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getW002().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getW004().intValue() == 1 && paramBoolean2))) return true; 
/* 280 */     if (paramHrmScheduleShiftsDetail2.getW004().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getW003().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getW005().intValue() == 1 && paramBoolean2))) return true; 
/* 281 */     if (paramHrmScheduleShiftsDetail2.getW005().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getW004().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getW006().intValue() == 1 && paramBoolean2))) return true; 
/* 282 */     if (paramHrmScheduleShiftsDetail2.getW006().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getW005().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getW007().intValue() == 1 && paramBoolean2))) return true; 
/* 283 */     return false;
/*     */   }
/*     */   
/*     */   private boolean dCheckAcrossTime(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail1, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail2, boolean paramBoolean1, boolean paramBoolean2) {
/* 287 */     if (paramHrmScheduleShiftsDetail2.getD031().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD030().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD001().intValue() == 1 && paramBoolean2))) return true; 
/* 288 */     if (paramHrmScheduleShiftsDetail2.getD001().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD031().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD002().intValue() == 1 && paramBoolean2))) return true; 
/* 289 */     if (paramHrmScheduleShiftsDetail2.getD002().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD001().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD003().intValue() == 1 && paramBoolean2))) return true; 
/* 290 */     if (paramHrmScheduleShiftsDetail2.getD003().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD002().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD004().intValue() == 1 && paramBoolean2))) return true; 
/* 291 */     if (paramHrmScheduleShiftsDetail2.getD004().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD003().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD005().intValue() == 1 && paramBoolean2))) return true; 
/* 292 */     if (paramHrmScheduleShiftsDetail2.getD005().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD004().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD006().intValue() == 1 && paramBoolean2))) return true; 
/* 293 */     if (paramHrmScheduleShiftsDetail2.getD006().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD005().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD007().intValue() == 1 && paramBoolean2))) return true; 
/* 294 */     if (paramHrmScheduleShiftsDetail2.getD007().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD006().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD008().intValue() == 1 && paramBoolean2))) return true; 
/* 295 */     if (paramHrmScheduleShiftsDetail2.getD008().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD007().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD009().intValue() == 1 && paramBoolean2))) return true; 
/* 296 */     if (paramHrmScheduleShiftsDetail2.getD009().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD008().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD010().intValue() == 1 && paramBoolean2))) return true; 
/* 297 */     if (paramHrmScheduleShiftsDetail2.getD010().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD009().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD011().intValue() == 1 && paramBoolean2))) return true; 
/* 298 */     if (paramHrmScheduleShiftsDetail2.getD011().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD010().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD012().intValue() == 1 && paramBoolean2))) return true; 
/* 299 */     if (paramHrmScheduleShiftsDetail2.getD012().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD011().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD013().intValue() == 1 && paramBoolean2))) return true; 
/* 300 */     if (paramHrmScheduleShiftsDetail2.getD013().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD012().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD014().intValue() == 1 && paramBoolean2))) return true; 
/* 301 */     if (paramHrmScheduleShiftsDetail2.getD014().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD013().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD015().intValue() == 1 && paramBoolean2))) return true; 
/* 302 */     if (paramHrmScheduleShiftsDetail2.getD015().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD014().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD016().intValue() == 1 && paramBoolean2))) return true; 
/* 303 */     if (paramHrmScheduleShiftsDetail2.getD016().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD015().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD017().intValue() == 1 && paramBoolean2))) return true; 
/* 304 */     if (paramHrmScheduleShiftsDetail2.getD017().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD016().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD018().intValue() == 1 && paramBoolean2))) return true; 
/* 305 */     if (paramHrmScheduleShiftsDetail2.getD018().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD017().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD019().intValue() == 1 && paramBoolean2))) return true; 
/* 306 */     if (paramHrmScheduleShiftsDetail2.getD019().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD018().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD020().intValue() == 1 && paramBoolean2))) return true; 
/* 307 */     if (paramHrmScheduleShiftsDetail2.getD020().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD019().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD021().intValue() == 1 && paramBoolean2))) return true; 
/* 308 */     if (paramHrmScheduleShiftsDetail2.getD021().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD021().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD022().intValue() == 1 && paramBoolean2))) return true; 
/* 309 */     if (paramHrmScheduleShiftsDetail2.getD022().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD021().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD023().intValue() == 1 && paramBoolean2))) return true; 
/* 310 */     if (paramHrmScheduleShiftsDetail2.getD023().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD022().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD024().intValue() == 1 && paramBoolean2))) return true; 
/* 311 */     if (paramHrmScheduleShiftsDetail2.getD024().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD023().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD025().intValue() == 1 && paramBoolean2))) return true; 
/* 312 */     if (paramHrmScheduleShiftsDetail2.getD025().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD024().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD026().intValue() == 1 && paramBoolean2))) return true; 
/* 313 */     if (paramHrmScheduleShiftsDetail2.getD026().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD025().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD027().intValue() == 1 && paramBoolean2))) return true; 
/* 314 */     if (paramHrmScheduleShiftsDetail2.getD027().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD026().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD028().intValue() == 1 && paramBoolean2))) return true; 
/* 315 */     if (paramHrmScheduleShiftsDetail2.getD028().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD027().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD029().intValue() == 1 && paramBoolean2))) return true; 
/* 316 */     if (paramHrmScheduleShiftsDetail2.getD029().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD028().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD030().intValue() == 1 && paramBoolean2))) return true; 
/* 317 */     if (paramHrmScheduleShiftsDetail2.getD030().intValue() == 1 && ((paramHrmScheduleShiftsDetail1.getD029().intValue() == 1 && paramBoolean1) || (paramHrmScheduleShiftsDetail1.getD031().intValue() == 1 && paramBoolean2))) return true; 
/* 318 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmScheduleShiftsDetailManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */