/*     */ package weaver.hrm.schedule.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.attendance.domain.HrmPubHoliday;
/*     */ import weaver.hrm.attendance.manager.HrmPubHolidayManager;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleSetCache;
/*     */ import weaver.hrm.schedule.dao.HrmScheduleSetDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSet;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSetManager
/*     */   extends BaseManager<HrmScheduleSet>
/*     */ {
/*     */   private static final int DH = 38;
/*     */   private static final int WD = 7;
/*     */   private boolean showResource = false;
/*  39 */   private String cursor = "";
/*     */   
/*  41 */   private HrmScheduleSetDao dao = null;
/*     */   
/*  43 */   private HrmPubHolidayManager holidayManager = null;
/*     */   
/*  45 */   private HrmScheduleSetDetailManager detailManager = null;
/*     */   
/*  47 */   private HrmScheduleWorktimeManager worktimeManager = null;
/*     */   
/*  49 */   private HrmScheduleShiftsSetManager shiftsSetManager = null;
/*     */   
/*     */   public HrmScheduleSetManager() {
/*  52 */     this((HttpServletRequest)null, (HttpServletResponse)null);
/*     */   }
/*     */   
/*     */   public HrmScheduleSetManager(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  56 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*  57 */     this.dao = new HrmScheduleSetDao();
/*  58 */     setDao((BaseDao)this.dao);
/*  59 */     this.cursor = "pointer";
/*  60 */     this.holidayManager = new HrmPubHolidayManager();
/*  61 */     this.detailManager = new HrmScheduleSetDetailManager(paramHttpServletRequest, paramHttpServletResponse);
/*  62 */     this.worktimeManager = new HrmScheduleWorktimeManager(paramHttpServletRequest, paramHttpServletResponse);
/*  63 */     this.shiftsSetManager = new HrmScheduleShiftsSetManager(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleSet paramHrmScheduleSet) {
/*  67 */     return save(paramHrmScheduleSet, false);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleSet paramHrmScheduleSet, boolean paramBoolean) {
/*  71 */     String str = String.valueOf(paramHrmScheduleSet.getId());
/*  72 */     if (paramBoolean || str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmScheduleSet)); }
/*  73 */     else { update(paramHrmScheduleSet); }
/*  74 */      return Long.valueOf(str);
/*     */   }
/*     */   
/*     */   public int count(String paramString) {
/*  78 */     return this.dao.count(getMapParam(paramString));
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/*  82 */     this.dao.delete(paramMap);
/*     */   }
/*     */   public String getId(HrmScheduleSet paramHrmScheduleSet) {
/*  85 */     return String.valueOf(paramHrmScheduleSet.getId());
/*     */   }
/*     */   public void save(List<HrmScheduleSet> paramList) {
/*  88 */     this.dao.insert(paramList);
/*     */   }
/*     */   
/*     */   public void setShowResource(boolean paramBoolean) {
/*  92 */     this.showResource = paramBoolean;
/*  93 */     this.cursor = paramBoolean ? "" : "pointer";
/*     */   }
/*     */   
/*     */   private List<HrmScheduleSet> getList(String paramString1, String paramString2) {
/*  97 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/*  98 */     ArrayList<HrmScheduleSet> arrayList = new ArrayList();
/*  99 */     List list = (new HrmScheduleSetCache()).getResult();
/* 100 */     for (String str : arrayOfString) {
/* 101 */       for (HrmScheduleSet hrmScheduleSet : list) {
/* 102 */         if (str.equals(StringUtil.vString(hrmScheduleSet.getId()))) {
/* 103 */           arrayList.add(hrmScheduleSet);
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 108 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString) {
/* 112 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString1, String paramString2) {
/* 116 */     String str = getAppendStr(paramString2);
/* 117 */     StringBuffer stringBuffer = new StringBuffer();
/* 118 */     List<HrmScheduleSet> list = getList(paramString1, paramString2);
/* 119 */     for (HrmScheduleSet hrmScheduleSet : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmScheduleSet.getField001()); 
/* 120 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public String showYear(List<Map<String, String>> paramList, List<HrmPubHoliday> paramList1, String paramString1, String paramString2, int paramInt) {
/* 125 */     StringBuffer stringBuffer = (new StringBuffer("<div id='mvEventContainer' style='background-color:#f7f7f7;height:")).append(paramInt).append("px'>").append("<table class='yearTable' cellspacing='0' cellpadding='0'><colgroup><col width='25%'><col width='25%'><col width='25%'><col width='25%'></colgroup>");
/* 126 */     int i = StringUtil.parseToInt(DateUtil.getYear(DateUtil.getCalendar(paramString1)));
/* 127 */     String str1 = DateUtil.getCurrentDate(), str2 = getLabelName(Integer.valueOf(6076)), str3 = getLabelName(Integer.valueOf(125807)), str4 = getLabelName(Integer.valueOf(125806));
/* 128 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 129 */     hashMap.put("today", str1);
/* 130 */     hashMap.put("monthName", str2);
/* 131 */     hashMap.put("wLabel", str3);
/* 132 */     hashMap.put("rLabel", str4);
/* 133 */     for (byte b = 0; b < 12; b++) {
/* 134 */       if (b == 0 || b % 4 == 0) {
/* 135 */         stringBuffer.append("<tr style='height:210px;'>");
/*     */       }
/* 137 */       stringBuffer.append("<td>").append(getMonthContent(i, b + 1, (Map)hashMap, paramList1, paramList)).append("</td>");
/* 138 */       if ((b + 1) % 4 == 0) {
/* 139 */         stringBuffer.append("</tr>");
/*     */       }
/*     */     } 
/* 142 */     stringBuffer.append("</table></div><script type=\"text/javascript\">jQuery('#mvEventContainer').perfectScrollbar();</script>");
/* 143 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getMonthContent(int paramInt1, int paramInt2, Map<String, String> paramMap, List<HrmPubHoliday> paramList, List<Map<String, String>> paramList1) {
/* 147 */     String str1 = paramInt1 + "-" + ((paramInt2 < 10) ? "0" : "") + paramInt2 + "-01";
/* 148 */     Calendar calendar = DateUtil.getCalendar(str1);
/* 149 */     String str2 = DateUtil.getLastDayOfMonthToString(calendar.getTime());
/* 150 */     String str3 = "", str4 = StringUtil.vString(paramMap.get("monthName"));
/* 151 */     int i = DateUtil.getWeek(calendar.getTime());
/* 152 */     if ((i == 7) ? false : i) calendar = DateUtil.addDay(calendar, -i); 
/* 153 */     String str5 = " onclick=\"ShowMonth('" + str1 + "')\" style='cursor:pointer;'";
/*     */     
/* 155 */     StringBuffer stringBuffer = (new StringBuffer("<div id='monthDiv'")).append(str5).append("><table class='monthTable' cellspacing='0' cellpadding='0'><colgroup><col width='14%'><col width='14%'><col width='14%'><col width='14%'><col width='14%'><col width='14%'><col width='14%'></colgroup>").append("<tr><td colspan='").append(7).append("' class='monthName'>").append(paramInt2).append(str4).append("</td></tr>");
/* 156 */     for (byte b = 0; b < 6; b++) {
/* 157 */       stringBuffer.append("<tr>");
/* 158 */       for (byte b1 = 0; b1 < 7; b1++) {
/* 159 */         str3 = DateUtil.getDate(calendar.getTime());
/* 160 */         stringBuffer.append("<td>")
/* 161 */           .append(getFormatDateTable(DateUtil.isInDateRange(str3, str1, str2), DateUtil.getDate(calendar), str3, b1, paramMap, paramList, paramList1))
/* 162 */           .append("</td>");
/* 163 */         calendar.add(5, 1);
/*     */       } 
/* 165 */       stringBuffer.append("</tr>");
/*     */     } 
/* 167 */     stringBuffer.append("</table></div>");
/* 168 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getFormatDateTable(boolean paramBoolean, String paramString1, String paramString2, int paramInt, Map<String, String> paramMap, List<HrmPubHoliday> paramList, List<Map<String, String>> paramList1) {
/* 172 */     boolean bool = paramString2.equals(StringUtil.vString(paramMap.get("today")));
/* 173 */     String str1 = bool ? "color:#FFFFFF;" : "";
/* 174 */     String str2 = "", str3 = "", str4 = "", str5 = "", str6 = "padding-bottom:5px;";
/* 175 */     String str7 = (!bool && (paramInt == 0 || paramInt == 6)) ? " class='st-dtitle-nonmonth'" : "";
/* 176 */     if (paramBoolean) {
/* 177 */       str5 = getSpanBody(this.holidayManager.getDateType(paramString2, paramList), StringUtil.vString(paramMap.get("wLabel")), StringUtil.vString(paramMap.get("rLabel")));
/* 178 */       if (str5.length() != 0) str1 = str1 + "margin-left:10px;"; 
/* 179 */       str4 = "<span style='" + str1 + "'" + str7 + ">" + paramString1 + "</span>";
/* 180 */       Map<String, String> map = getBackgroundInfo(paramList1, paramString2);
/* 181 */       str2 = StringUtil.vString(map.get("color"));
/* 182 */       int i = StringUtil.parseToInt(paramMap.get("size"));
/* 183 */       i = ((i <= 0) ? 1 : ((i > 10) ? 10 : i)) * 10;
/* 184 */       if (str2.length() > 0) {
/* 185 */         double d = StringUtil.round(100.0D - i * 1.0D / 100.0D, 1);
/* 186 */         str2 = str2 + "filter:alpha(opacity=" + i + ");-moz-opacity:" + d + ";-khtml-opacity:" + d + ";opacity:" + d + ";";
/*     */       } 
/* 188 */       if (bool) {
/* 189 */         str3 = " style='" + str6 + "background:url(/appres/hrm/image/schedule/img001.png) no-repeat center;'";
/* 190 */         str6 = "";
/*     */       } 
/*     */     } 
/* 193 */     return "<div style='margin:0 auto;" + str6 + str2 + "'><p" + str3 + ">" + str4 + "<sup>" + str5 + "</sup></p></div>";
/*     */   }
/*     */   
/*     */   private Map<String, String> getBackgroundInfo(List<Map<String, String>> paramList, String paramString) {
/* 197 */     List<Map<String, String>> list = getResultMap(paramList, paramString);
/* 198 */     String str = "";
/* 199 */     int i = 0;
/* 200 */     if (list != null) {
/* 201 */       i = list.size();
/* 202 */       Iterator<Map<String, String>> iterator = list.iterator(); if (iterator.hasNext()) { Map map = iterator.next();
/* 203 */         str = "background-color:" + StringUtil.vString((String)map.get("field007")) + ";"; }
/*     */     
/*     */     } 
/*     */     
/* 207 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 208 */     hashMap.put("color", str);
/* 209 */     hashMap.put("size", String.valueOf(i));
/* 210 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String showMonth(List<Map<String, String>> paramList, List<HrmPubHoliday> paramList1, String paramString1, String paramString2, int paramInt) {
/* 214 */     int i = DateUtil.countWeekOfMonth(paramString1);
/* 215 */     Map<String, String> map = getRowContent(paramList, paramList1, paramString1, paramString2, paramInt, i);
/* 216 */     String str1 = StringUtil.vString(map.get("height"));
/* 217 */     String str2 = StringUtil.vString(map.get("mveContainer"));
/* 218 */     String str3 = StringUtil.vString(map.get("containerIds"));
/* 219 */     return showCalendar(str1, getWeekTHBody(), str2, str3);
/*     */   }
/*     */   
/*     */   private List<Map<String, String>> getResultMap(List<Map<String, String>> paramList, String paramString) {
/* 223 */     ArrayList<Map> arrayList = new ArrayList();
/* 224 */     if (paramList != null)
/* 225 */       for (Map<String, String> map : paramList) {
/* 226 */         if (StringUtil.vString((String)map.get("date")).equals(paramString)) arrayList.add(map);
/*     */       
/*     */       }  
/* 229 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private String getCBodyDiv(List<Map<String, String>> paramList, String paramString, int paramInt) {
/* 233 */     String str1 = getLabelName(Integer.valueOf(127));
/* 234 */     StringBuffer stringBuffer = new StringBuffer();
/* 235 */     String str2 = String.valueOf(getLanguageId());
/* 236 */     List<Map<String, String>> list = getResultMap(paramList, paramString);
/* 237 */     Calendar calendar = DateUtil.getCalendar(paramString);
/* 238 */     int i = StringUtil.parseToInt(DateUtil.getDate(calendar));
/* 239 */     int j = DateUtil.getWeek(calendar.getTime());
/* 240 */     String str3 = "", str4 = "", str5 = "", str6 = "", str7 = "", str8 = "", str9 = "", str10 = "";
/* 241 */     HrmScheduleShiftsDetailManager hrmScheduleShiftsDetailManager = new HrmScheduleShiftsDetailManager();
/* 242 */     if (list != null) {
/* 243 */       for (Map<String, String> map : list) {
/* 244 */         str3 = StringUtil.vString((String)map.get("sId"));
/* 245 */         str4 = StringUtil.vString((String)map.get("cnt")) + str1;
/* 246 */         str8 = StringUtil.vString((String)map.get("field001"));
/* 247 */         str9 = StringUtil.vString((String)map.get("field007"));
/* 248 */         str10 = hrmScheduleShiftsDetailManager.getWorkTime(str3, str2, StringUtil.parseToInt((String)map.get("field003")), new int[] { j, i });
/* 249 */         str6 = str8 + " " + (this.showResource ? str10 : bracket(str10));
/* 250 */         str5 = str6 + (this.showResource ? "" : (" " + str4));
/* 251 */         str7 = this.showResource ? str6 : (str8 + bracket(str4));
/* 252 */         stringBuffer.append("<div title='").append(str5).append("' style='width:90%;padding-left:5px;margin:0px auto 1px auto;cursor:" + this.cursor + ";background:").append(str9)
/* 253 */           .append(";' onclick=\"showContent('" + paramString + "', '" + paramString + "', '" + ((paramInt == 1) ? "3" : "2") + "', '" + str3 + "', '" + str6 + "')\">").append(str7).append("</div>");
/*     */       } 
/*     */     }
/* 256 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getWeekTHBody() {
/* 260 */     int[] arrayOfInt = { 390, 27290, 27291, 27292, 27293, 27294, 27295 };
/* 261 */     String str = "";
/* 262 */     StringBuffer stringBuffer = new StringBuffer();
/* 263 */     for (byte b = 0; b < arrayOfInt.length; b++) {
/* 264 */       str = getLabelName(Integer.valueOf(arrayOfInt[b]));
/* 265 */       stringBuffer.append("<th class='mv-dayname' title='").append(str).append("'>").append(str).append("</th>");
/*     */     } 
/* 267 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getHBody(String paramString1, boolean paramBoolean, String paramString2, String paramString3, int paramInt) {
/* 271 */     StringBuffer stringBuffer = new StringBuffer();
/* 272 */     stringBuffer.append("<td class='st-dtitle' style='border-top: 0px solid #93AA39;").append(StringUtil.isNotNull(paramString1) ? ("cursor:" + this.cursor + ";") : "").append("'").append(paramString2).append("><table style='width:100%;height:100%'><tr>")
/* 273 */       .append("<td style='text-align:left'><span").append(paramBoolean ? " style='color:#39F'" : ((paramInt == 0 || paramInt == 6) ? " class='st-dtitle-nonmonth'" : "")).append(">").append(StringUtil.isNull(paramString1) ? "" : DateUtil.geCalendartDate(paramString1)).append("</span></td>")
/* 274 */       .append("<td style='text-align:right'>").append(paramString3).append("</td></tr></table></td>");
/* 275 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getSpanBody(int paramInt, String paramString1, String paramString2) {
/* 279 */     String str = "";
/* 280 */     if (paramInt == 2) {
/* 281 */       str = "<span style='color:red'>" + paramString1 + "</span>";
/* 282 */     } else if (paramInt != 0) {
/* 283 */       str = "<span style='color:green'>" + paramString2 + "</span>";
/*     */     } 
/* 285 */     return str;
/*     */   }
/*     */   
/*     */   private Map<String, String> getRowContent(List<Map<String, String>> paramList, List<HrmPubHoliday> paramList1, String paramString1, String paramString2, int paramInt1, int paramInt2) {
/* 289 */     paramInt1 -= 38;
/* 290 */     Calendar calendar = DateUtil.getCalendar(paramString1);
/* 291 */     StringBuffer stringBuffer = new StringBuffer();
/* 292 */     String str1 = "", str2 = "", str3 = DateUtil.getCurrentDate(), str4 = getLabelName(Integer.valueOf(125807)), str5 = getLabelName(Integer.valueOf(125806));
/* 293 */     double d1 = 0.0D, d2 = StringUtil.round(paramInt1 / ((paramInt2 == 1) ? 5 : paramInt2) * 1.0D, 1), d3 = StringUtil.round(100.0D / paramInt2, 1);
/* 294 */     int i = DateUtil.getWeek(calendar.getTime());
/* 295 */     if ((i == 7) ? false : i) calendar = DateUtil.addDay(calendar, -i); 
/* 296 */     for (byte b = 0; b < paramInt2; b++) {
/* 297 */       StringBuffer stringBuffer1 = new StringBuffer(), stringBuffer2 = new StringBuffer();
/* 298 */       for (byte b1 = 0; b1 < 7; b1++) {
/* 299 */         str1 = DateUtil.getDate(calendar.getTime());
/* 300 */         if (DateUtil.isInDateRange(str1, paramString1, paramString2)) {
/* 301 */           String str6 = "div" + DateUtil.getDate(calendar.getTime(), "yyyyMMdd");
/* 302 */           str2 = str2 + ((str2.length() == 0) ? "" : ",") + "#" + str6;
/* 303 */           String str7 = getCBodyDiv(paramList, str1, paramInt2);
/* 304 */           String str8 = " onclick=\"showContent('" + str1 + "', '" + str1 + "', '" + ((paramInt2 == 1) ? "3" : "2") + "')\"";
/* 305 */           stringBuffer1.append("<td abbr='" + str1 + "' class='st-bg'").append(StringUtil.isNull(str7) ? (" style='cursor:" + this.cursor + ";'" + str8) : "").append(">").append("<div id='").append(str6).append("' style='width:100%;margin-top:30px;height:").append(StringUtil.round(d2 - 30.0D, 1)).append("px;overflow:hidden;'>").append(str7).append("</div></td>");
/* 306 */           stringBuffer2.append(getHBody(str1, str1.equals(str3), str8, getSpanBody(this.holidayManager.getDateType(str1, paramList1), str4, str5), b1));
/*     */         } else {
/* 308 */           stringBuffer1.append("<td abbr='" + str1 + "' class='st-bg'></td>");
/* 309 */           stringBuffer2.append(getHBody("", false, "", "", -1));
/*     */         } 
/* 311 */         calendar.add(5, 1);
/*     */       } 
/* 313 */       stringBuffer.append("<div style='height:").append(d2).append("px;top:").append(StringUtil.round(d1, 1)).append("%;border-bottom:1px solid #d0d0d0;' class='month-row'>")
/* 314 */         .append("<table class='st-bg-table' cellspacing='0' cellpadding='0'><tr>").append(stringBuffer1.toString())
/* 315 */         .append("</tr></table><table class='st-grid' cellspacing='0' cellpadding='0'><tr>").append(stringBuffer2.toString()).append("</tr></table></div>");
/* 316 */       d1 += d3;
/*     */     } 
/* 318 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 319 */     hashMap.put("height", String.valueOf((paramInt2 == 1) ? d2 : paramInt1));
/* 320 */     hashMap.put("mveContainer", stringBuffer.toString());
/* 321 */     hashMap.put("containerIds", str2);
/* 322 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String showWeek(List<Map<String, String>> paramList, List<HrmPubHoliday> paramList1, String paramString1, String paramString2, int paramInt) {
/* 326 */     Map<String, String> map = getRowContent(paramList, paramList1, paramString1, paramString2, paramInt, 1);
/* 327 */     String str1 = StringUtil.vString(map.get("height"));
/* 328 */     String str2 = StringUtil.vString(map.get("mveContainer"));
/* 329 */     String str3 = StringUtil.vString(map.get("containerIds"));
/* 330 */     return showCalendar(str1, getWeekTHBody(), str2, str3);
/*     */   }
/*     */   
/*     */   private String showCalendar(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 334 */     return "<div id='mvcontainer' class='mv-container'>" + "<table id='sTable' class='mv-daynames-table' cellspacing='0' cellpadding='0' style='border-collapse:collapse;background:rgb(248, 248, 248);'><tr>" + paramString2 + "</tr></table></div>" + "<div id='mvEventContainer' class='mv-event-container' style='height:" + 
/* 335 */       paramString1 + "px;'>" + paramString3 + "</div>" + "<script type=\"text/javascript\">jQuery('" + 
/* 336 */       paramString4 + "').perfectScrollbar();</script>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String showDate(List<Map<String, String>> paramList, String paramString, int paramInt) {
/* 341 */     StringBuffer stringBuffer = new StringBuffer();
/* 342 */     List<Map<String, String>> list = paramList;
/* 343 */     String str1 = getLabelName(Integer.valueOf(127)), str2 = String.valueOf(getLanguageId()), str3 = DateUtil.addDate(paramString, -1);
/* 344 */     String str4 = "", str5 = "", str6 = "", str7 = "", str8 = "", str9 = "", str10 = "";
/* 345 */     Calendar calendar1 = DateUtil.getCalendar(paramString), calendar2 = DateUtil.getCalendar(str3);
/* 346 */     int i = StringUtil.parseToInt(DateUtil.getDate(calendar1)), j = StringUtil.parseToInt(DateUtil.getDate(calendar2));
/* 347 */     int k = DateUtil.getWeek(calendar1.getTime()), m = DateUtil.getWeek(calendar2.getTime());
/* 348 */     HrmScheduleShiftsDetailManager hrmScheduleShiftsDetailManager = new HrmScheduleShiftsDetailManager();
/* 349 */     boolean bool = false;
/* 350 */     byte b = 0;
/* 351 */     if (list != null) {
/* 352 */       for (Map<String, String> map : list) {
/* 353 */         str4 = StringUtil.vString((String)map.get("sId"));
/* 354 */         str5 = StringUtil.vString((String)map.get("cnt")) + str1;
/* 355 */         str8 = StringUtil.vString((String)map.get("field001"));
/* 356 */         str9 = StringUtil.vString((String)map.get("field007"));
/* 357 */         bool = StringUtil.vString((String)map.get("date")).equals(str3);
/* 358 */         (new int[2])[0] = m; (new int[2])[1] = j; (new int[2])[0] = k; (new int[2])[1] = i; str10 = hrmScheduleShiftsDetailManager.getWorkTime(str4, str2, StringUtil.parseToInt((String)map.get("field003")), bool ? new int[2] : new int[2]);
/* 359 */         if (this.showResource) {
/* 360 */           str7 = str6 = str8 + " " + str10 + (bool ? ("[" + str3 + "]") : "");
/*     */         } else {
/* 362 */           str6 = str8 + " " + bracket(str10);
/* 363 */           str7 = str8 + bracket(str5) + (bool ? ("[" + str3 + "]") : "");
/*     */         } 
/* 365 */         Map<String, Integer[]> map1 = getTimes(str10, bool);
/* 366 */         if (map1.isEmpty())
/* 367 */           continue;  stringBuffer.append("<tr>");
/* 368 */         int[] arrayOfInt = { -1, -1 };
/* 369 */         for (byte b1 = 0; b1 < 24; b1++) {
/* 370 */           int n = 1;
/* 371 */           String str11 = "", str12 = "", str13 = "", str14 = "white", str15 = "";
/* 372 */           int[] arrayOfInt1 = getArrayInfo(map1, b1), arrayOfInt2 = equalsNotFirstValue(map1, b1);
/* 373 */           if (arrayOfInt1[0] != -1)
/* 374 */           { n = arrayOfInt1[1];
/* 375 */             str11 = str6 + (this.showResource ? "" : (" " + str5));
/* 376 */             str12 = str7;
/* 377 */             str14 = str9;
/* 378 */             str15 = "showContent('" + paramString + "', '" + paramString + "', '4', '" + str4 + "', '" + str6 + "')";
/* 379 */             arrayOfInt = arrayOfInt1;
/* 380 */             str13 = ";cursor:" + str13 + ";"; }
/* 381 */           else if (arrayOfInt2[0] == arrayOfInt[0] && arrayOfInt2[1] != -1) { continue; }
/* 382 */            stringBuffer.append("<td colspan='").append(n).append("' style='text-align:center;background-color:").append(str14).append(str13).append("' onclick=\"").append(str15).append("\" title='").append(str11).append("' align='center' valign='middle'>").append(str12).append("</td>"); continue;
/*     */         } 
/* 384 */         b++;
/* 385 */         stringBuffer.append("</tr>");
/*     */       } 
/*     */     } else {
/* 388 */       stringBuffer.append("<tr>");
/* 389 */       for (byte b1 = 0; b1 < 24; ) { stringBuffer.append("<td style='text-align:center;background-color:white' title='' align='center' valign='middle'></td>"); b1++; }
/* 390 */        stringBuffer.append("</tr>");
/*     */     } 
/* 392 */     return getDateTable(stringBuffer.toString(), (((b >= 3) ? 3 : ((b == 0) ? 1 : b)) * 30 + 34));
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDateTable(String paramString, double paramDouble) {
/* 397 */     StringBuffer stringBuffer = (new StringBuffer("<div id='mvEventContainer' style='overflow-x:hidden;height:")).append(paramDouble).append("px'><table class='altrowstable' cellspacing='0' cellpadding='0' style='width:100%;'>").append("<tr style='background-color:#f7f7f7;border-bottom:1px solid #59b0f2;'>");
/* 398 */     for (byte b = 0; b < 24; ) { stringBuffer.append("<th style='width:4%;text-align:center;'>").append(b).append("</th>"); b++; }
/* 399 */      stringBuffer.append("</tr>").append(paramString).append("</table></div><script type=\"text/javascript\">jQuery('#mvEventContainer').perfectScrollbar();</script>");
/* 400 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private Map<String, Integer[]> getTimes(String paramString, boolean paramBoolean) {
/* 404 */     String[] arrayOfString = StringUtil.vString(paramString).split(getAppendStr());
/* 405 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 406 */     for (String str : arrayOfString) {
/* 407 */       String[] arrayOfString1 = str.split("-");
/* 408 */       if (arrayOfString1 != null && arrayOfString1.length == 2 && (!paramBoolean || (!arrayOfString1[1].equals("00:00") && arrayOfString1[0].compareTo(arrayOfString1[1]) >= 0))) {
/*     */         
/* 410 */         Integer[] arrayOfInteger = DateUtil.getHours(paramBoolean ? "00:00" : arrayOfString1[0], arrayOfString1[1]);
/* 411 */         if (arrayOfInteger != null && arrayOfInteger.length != 0)
/*     */         {
/* 413 */           hashMap.put(String.valueOf(arrayOfInteger[0]), arrayOfInteger); } 
/*     */       } 
/* 415 */     }  return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private Integer[] getTValue(Map<String, Integer[]> paramMap, String paramString) {
/* 419 */     return (paramMap != null && paramMap.containsKey(paramString)) ? paramMap.get(paramString) : null;
/*     */   }
/*     */   
/*     */   private int[] getArrayInfo(Map<String, Integer[]> paramMap, int paramInt) {
/* 423 */     int[] arrayOfInt = { -1, -1 };
/* 424 */     Integer[] arrayOfInteger = getTValue(paramMap, String.valueOf(paramInt));
/* 425 */     if (arrayOfInteger != null) {
/* 426 */       arrayOfInt[0] = arrayOfInteger[0].intValue();
/* 427 */       arrayOfInt[1] = arrayOfInteger.length;
/*     */     } 
/* 429 */     return arrayOfInt;
/*     */   }
/*     */   
/*     */   private int[] equalsNotFirstValue(Map<String, Integer[]> paramMap, int paramInt) {
/* 433 */     int[] arrayOfInt = { -1, -1 };
/* 434 */     if (paramMap == null) return arrayOfInt;
/*     */     
/* 436 */     Iterator<Map.Entry> iterator = paramMap.entrySet().iterator();
/* 437 */     Integer[] arrayOfInteger = null;
/* 438 */     while (iterator.hasNext()) {
/* 439 */       Map.Entry entry = iterator.next();
/* 440 */       arrayOfInteger = (Integer[])entry.getValue();
/* 441 */       byte b1 = (arrayOfInteger == null) ? 0 : arrayOfInteger.length;
/* 442 */       int i = -1;
/* 443 */       for (byte b2 = 0; b2 < b1; b2++) {
/* 444 */         if (b2 == 0) {
/* 445 */           i = arrayOfInteger[b2].intValue();
/* 446 */         } else if (arrayOfInteger[b2].intValue() == paramInt) {
/* 447 */           arrayOfInt[0] = i;
/* 448 */           arrayOfInt[1] = 1;
/*     */           break;
/*     */         } 
/*     */       } 
/* 452 */       if (arrayOfInt[1] != -1)
/*     */         break; 
/* 454 */     }  return arrayOfInt;
/*     */   }
/*     */   
/*     */   public List<HrmScheduleSetDetail> getScheduleDetail(int paramInt) {
/* 458 */     return getScheduleDetail(paramInt, DateUtil.getCurrentDate());
/*     */   }
/*     */   
/*     */   public List<HrmScheduleSetDetail> getScheduleDetail(int paramInt, String paramString) {
/* 462 */     return getScheduleDetail(paramInt, paramString, paramString);
/*     */   }
/*     */   
/*     */   public List<HrmScheduleSetDetail> getScheduleDetail(int paramInt, String paramString1, String paramString2) {
/* 466 */     return getScheduleDetail(String.valueOf(paramInt), paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Map<String, String>> getScheduleSignDetail(String paramString1, String paramString2) {
/* 479 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 480 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 481 */     List list = this.detailManager.find("[map]sql_field003:and t.field003 between '" + paramString1 + "' and '" + paramString2 + "';delflag:0;sqlorderby:t.field002,t.field003");
/*     */ 
/*     */     
/* 484 */     String str = "";
/* 485 */     for (HrmScheduleSetDetail hrmScheduleSetDetail : list) {
/* 486 */       str = StringUtil.vString(hrmScheduleSetDetail.getField002());
/* 487 */       if (hashMap1.containsKey(str)) {
/* 488 */         ((Map<String, String>)hashMap1.get(str)).put(hrmScheduleSetDetail.getField003(), this.worktimeManager.getSignDateTime(hrmScheduleSetDetail.getField003(), hrmScheduleSetDetail.getField005(), "8")); continue;
/*     */       } 
/* 490 */       hashMap2 = new HashMap<>();
/* 491 */       hashMap2.put(hrmScheduleSetDetail.getField003(), this.worktimeManager.getSignDateTime(hrmScheduleSetDetail.getField003(), hrmScheduleSetDetail.getField005(), "8"));
/* 492 */       hashMap1.put(str, hashMap2);
/*     */     } 
/*     */     
/* 495 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public List<HrmScheduleSetDetail> getScheduleDetail(String paramString1, String paramString2, String paramString3) {
/* 499 */     List<HrmScheduleSetDetail> list = this.detailManager.find("[map]sql_field002:and (" + Tools.getOracleSQLIn(paramString1, "t.field002") + ");sql_field003:and t.field003 between '" + paramString2 + "' and '" + paramString3 + "';delflag:0;sqlorderby:t.field003");
/* 500 */     Map<String, String> map = null;
/*     */     
/* 502 */     for (HrmScheduleSetDetail hrmScheduleSetDetail : list) {
/* 503 */       map = this.worktimeManager.getTime(hrmScheduleSetDetail.getField005(), "8");
/* 504 */       hrmScheduleSetDetail.setWorkTime(StringUtil.vString(map.get("workTime")));
/* 505 */       hrmScheduleSetDetail.setSignTime(StringUtil.vString(map.get("signTime")));
/* 506 */       hrmScheduleSetDetail.setRestTime(StringUtil.vString(map.get("restTime")));
/* 507 */       hrmScheduleSetDetail.setField001Name(this.shiftsSetManager.getDescription(hrmScheduleSetDetail.getField001(), "8"));
/* 508 */       hrmScheduleSetDetail.setField001Title(hrmScheduleSetDetail.getField001Name() + bracket(hrmScheduleSetDetail.getWorkTime()));
/*     */     } 
/* 510 */     return list;
/*     */   }
/*     */   
/*     */   public boolean isSchedulePerson(int paramInt) {
/* 514 */     return isSchedulePerson(paramInt, DateUtil.getCurrentDate());
/*     */   }
/*     */   
/*     */   public boolean isSchedulePerson(int paramInt, String paramString) {
/* 518 */     return isSchedulePerson(paramInt, paramString, paramString);
/*     */   }
/*     */   
/*     */   public boolean isSchedulePerson(int paramInt, String paramString1, String paramString2) {
/* 522 */     return (getScheduleDetail(paramInt, paramString1, paramString2).size() > 0);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmScheduleSetManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */