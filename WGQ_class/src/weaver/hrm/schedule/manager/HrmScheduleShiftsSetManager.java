/*     */ package weaver.hrm.schedule.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DataBook;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.attendance.domain.HrmPubHoliday;
/*     */ import weaver.hrm.attendance.manager.HrmPubHolidayManager;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleShiftsSetCache;
/*     */ import weaver.hrm.schedule.dao.HrmScheduleShiftsSetDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsDetail;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleShiftsSetManager
/*     */   extends BaseManager<HrmScheduleShiftsSet>
/*     */ {
/*  32 */   private HrmScheduleShiftsSetDao dao = null;
/*     */   
/*  34 */   private HrmPubHolidayManager holidayManager = null;
/*     */   
/*     */   public HrmScheduleShiftsSetManager() {
/*  37 */     this((HttpServletRequest)null, (HttpServletResponse)null);
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsSetManager(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  41 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*  42 */     this.dao = new HrmScheduleShiftsSetDao();
/*  43 */     setDao((BaseDao)this.dao);
/*  44 */     this.holidayManager = new HrmPubHolidayManager();
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleShiftsSet paramHrmScheduleShiftsSet) {
/*  48 */     return save(paramHrmScheduleShiftsSet, false);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleShiftsSet paramHrmScheduleShiftsSet, boolean paramBoolean) {
/*  52 */     String str = String.valueOf(paramHrmScheduleShiftsSet.getId());
/*  53 */     if (paramBoolean || str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmScheduleShiftsSet)); }
/*  54 */     else { update(paramHrmScheduleShiftsSet); }
/*  55 */      return Long.valueOf(str);
/*     */   }
/*     */   
/*     */   public int count(String paramString) {
/*  59 */     return this.dao.count(getMapParam(paramString));
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/*  63 */     this.dao.delete(paramMap);
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsSet getByRealId(String paramString) {
/*  67 */     return this.dao.getByRealId(paramString);
/*     */   }
/*     */   public String getField003Name(String paramString1, String paramString2, String paramString3, String paramString4) {
/*     */     int i, j;
/*  71 */     String str = "";
/*  72 */     switch (StringUtil.parseToInt(paramString2))
/*     */     { case 1:
/*  74 */         str = getLabelName(Integer.valueOf(125828), paramString1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 100 */         return str;case 2: str = getLabelName(Integer.valueOf(125823), paramString1); return str;case 3: str = getLabelName(Integer.valueOf(125824), paramString1); return str;case 4: str = getLabelName(Integer.valueOf(125825), paramString1); return str;case 5: str = getLabelName(Integer.valueOf(125826), paramString1); return str;case 6: str = getLabelName(Integer.valueOf(19516), paramString1); i = StringUtil.parseToInt(paramString3, 0); i = (i < 0) ? 0 : i; j = StringUtil.parseToInt(paramString4, 0); j = (j < 0) ? 0 : j; str = str + "(" + getLabelName(Integer.valueOf(125900), paramString1) + i + getLabelName(Integer.valueOf(125806), paramString1) + j + ")"; return str; }  str = getLabelName(Integer.valueOf(125827), paramString1); return str;
/*     */   }
/*     */   
/*     */   private List<HrmScheduleShiftsSet> getList(String paramString1, String paramString2) {
/* 104 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/* 105 */     ArrayList<HrmScheduleShiftsSet> arrayList = new ArrayList();
/* 106 */     List list = (new HrmScheduleShiftsSetCache()).getResult();
/* 107 */     for (String str : arrayOfString) {
/* 108 */       for (HrmScheduleShiftsSet hrmScheduleShiftsSet : list) {
/* 109 */         if (str.equals(StringUtil.vString(hrmScheduleShiftsSet.getId()))) {
/* 110 */           arrayList.add(hrmScheduleShiftsSet);
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 115 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString) {
/* 119 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString1, String paramString2) {
/* 123 */     String str = getAppendStr(paramString2);
/* 124 */     StringBuffer stringBuffer = new StringBuffer();
/* 125 */     List<HrmScheduleShiftsSet> list = getList(paramString1, paramString2);
/* 126 */     for (HrmScheduleShiftsSet hrmScheduleShiftsSet : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmScheduleShiftsSet.getField001()); 
/* 127 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getField007(String paramString) {
/* 131 */     String str1 = String.valueOf(getLanguageId());
/* 132 */     String str2 = getAppendStr(str1);
/* 133 */     StringBuffer stringBuffer = new StringBuffer();
/* 134 */     List<HrmScheduleShiftsSet> list = getList(paramString, str1);
/* 135 */     for (HrmScheduleShiftsSet hrmScheduleShiftsSet : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str2).append(hrmScheduleShiftsSet.getField007()); 
/* 136 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public List<DataBook> getWorkTimeList(String paramString1, int paramInt, String paramString2) {
/* 140 */     List list = (new HrmScheduleShiftsDetailManager()).find("[map]field001:" + paramString1);
/* 141 */     HrmScheduleWorktimeManager hrmScheduleWorktimeManager = new HrmScheduleWorktimeManager();
/* 142 */     DataBook[] arrayOfDataBook = null;
/* 143 */     if (paramInt == 0) {
/* 144 */       int[] arrayOfInt = { 398, 392, 393, 394, 395, 396, 397 };
/* 145 */       arrayOfDataBook = new DataBook[arrayOfInt.length];
/* 146 */       for (byte b = 0; b < arrayOfInt.length; b++) {
/* 147 */         DataBook dataBook = new DataBook();
/* 148 */         dataBook.setId(getLabelName(Integer.valueOf(arrayOfInt[b]), paramString2));
/* 149 */         arrayOfDataBook[b] = dataBook;
/*     */       } 
/* 151 */       String str = "";
/* 152 */       for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : list) {
/* 153 */         str = StringUtil.isNull(hrmScheduleShiftsDetail.getField002()) ? "" : hrmScheduleWorktimeManager.getWorkTime(hrmScheduleShiftsDetail.getField002(), paramString2);
/* 154 */         if (hrmScheduleShiftsDetail.getW007().intValue() == 1) arrayOfDataBook[0].setValue(str); 
/* 155 */         if (hrmScheduleShiftsDetail.getW001().intValue() == 1) arrayOfDataBook[1].setValue(str); 
/* 156 */         if (hrmScheduleShiftsDetail.getW002().intValue() == 1) arrayOfDataBook[2].setValue(str); 
/* 157 */         if (hrmScheduleShiftsDetail.getW003().intValue() == 1) arrayOfDataBook[3].setValue(str); 
/* 158 */         if (hrmScheduleShiftsDetail.getW004().intValue() == 1) arrayOfDataBook[4].setValue(str); 
/* 159 */         if (hrmScheduleShiftsDetail.getW005().intValue() == 1) arrayOfDataBook[5].setValue(str); 
/* 160 */         if (hrmScheduleShiftsDetail.getW006().intValue() == 1) arrayOfDataBook[6].setValue(str); 
/*     */       } 
/* 162 */     } else if (paramInt == 1) {
/* 163 */       String str1 = getLabelName(Integer.valueOf(390), paramString2);
/* 164 */       String[] arrayOfString = (new HrmScheduleShiftsDetail()).getDStrFields();
/* 165 */       arrayOfDataBook = new DataBook[arrayOfString.length];
/* 166 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 167 */         DataBook dataBook = new DataBook();
/* 168 */         dataBook.setId((b + 1) + str1);
/* 169 */         arrayOfDataBook[b] = dataBook;
/*     */       } 
/* 171 */       String str2 = "";
/* 172 */       for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : list) {
/* 173 */         str2 = StringUtil.isNull(hrmScheduleShiftsDetail.getField002()) ? "" : hrmScheduleWorktimeManager.getWorkTime(hrmScheduleShiftsDetail.getField002(), paramString2);
/* 174 */         if (hrmScheduleShiftsDetail.getD001().intValue() == 1) arrayOfDataBook[0].setValue(str2); 
/* 175 */         if (hrmScheduleShiftsDetail.getD002().intValue() == 1) arrayOfDataBook[1].setValue(str2); 
/* 176 */         if (hrmScheduleShiftsDetail.getD003().intValue() == 1) arrayOfDataBook[2].setValue(str2); 
/* 177 */         if (hrmScheduleShiftsDetail.getD004().intValue() == 1) arrayOfDataBook[3].setValue(str2); 
/* 178 */         if (hrmScheduleShiftsDetail.getD005().intValue() == 1) arrayOfDataBook[4].setValue(str2); 
/* 179 */         if (hrmScheduleShiftsDetail.getD006().intValue() == 1) arrayOfDataBook[5].setValue(str2); 
/* 180 */         if (hrmScheduleShiftsDetail.getD007().intValue() == 1) arrayOfDataBook[6].setValue(str2); 
/* 181 */         if (hrmScheduleShiftsDetail.getD008().intValue() == 1) arrayOfDataBook[7].setValue(str2); 
/* 182 */         if (hrmScheduleShiftsDetail.getD009().intValue() == 1) arrayOfDataBook[8].setValue(str2); 
/* 183 */         if (hrmScheduleShiftsDetail.getD010().intValue() == 1) arrayOfDataBook[9].setValue(str2); 
/* 184 */         if (hrmScheduleShiftsDetail.getD011().intValue() == 1) arrayOfDataBook[10].setValue(str2); 
/* 185 */         if (hrmScheduleShiftsDetail.getD012().intValue() == 1) arrayOfDataBook[11].setValue(str2); 
/* 186 */         if (hrmScheduleShiftsDetail.getD013().intValue() == 1) arrayOfDataBook[12].setValue(str2); 
/* 187 */         if (hrmScheduleShiftsDetail.getD014().intValue() == 1) arrayOfDataBook[13].setValue(str2); 
/* 188 */         if (hrmScheduleShiftsDetail.getD015().intValue() == 1) arrayOfDataBook[14].setValue(str2); 
/* 189 */         if (hrmScheduleShiftsDetail.getD016().intValue() == 1) arrayOfDataBook[15].setValue(str2); 
/* 190 */         if (hrmScheduleShiftsDetail.getD017().intValue() == 1) arrayOfDataBook[16].setValue(str2); 
/* 191 */         if (hrmScheduleShiftsDetail.getD018().intValue() == 1) arrayOfDataBook[17].setValue(str2); 
/* 192 */         if (hrmScheduleShiftsDetail.getD019().intValue() == 1) arrayOfDataBook[18].setValue(str2); 
/* 193 */         if (hrmScheduleShiftsDetail.getD020().intValue() == 1) arrayOfDataBook[19].setValue(str2); 
/* 194 */         if (hrmScheduleShiftsDetail.getD021().intValue() == 1) arrayOfDataBook[20].setValue(str2); 
/* 195 */         if (hrmScheduleShiftsDetail.getD022().intValue() == 1) arrayOfDataBook[21].setValue(str2); 
/* 196 */         if (hrmScheduleShiftsDetail.getD023().intValue() == 1) arrayOfDataBook[22].setValue(str2); 
/* 197 */         if (hrmScheduleShiftsDetail.getD024().intValue() == 1) arrayOfDataBook[23].setValue(str2); 
/* 198 */         if (hrmScheduleShiftsDetail.getD025().intValue() == 1) arrayOfDataBook[24].setValue(str2); 
/* 199 */         if (hrmScheduleShiftsDetail.getD026().intValue() == 1) arrayOfDataBook[25].setValue(str2); 
/* 200 */         if (hrmScheduleShiftsDetail.getD027().intValue() == 1) arrayOfDataBook[26].setValue(str2); 
/* 201 */         if (hrmScheduleShiftsDetail.getD028().intValue() == 1) arrayOfDataBook[27].setValue(str2); 
/* 202 */         if (hrmScheduleShiftsDetail.getD029().intValue() == 1) arrayOfDataBook[28].setValue(str2); 
/* 203 */         if (hrmScheduleShiftsDetail.getD030().intValue() == 1) arrayOfDataBook[29].setValue(str2); 
/* 204 */         if (hrmScheduleShiftsDetail.getD031().intValue() == 1) arrayOfDataBook[30].setValue(str2); 
/*     */       } 
/*     */     } 
/* 207 */     return (arrayOfDataBook == null) ? new ArrayList<>() : Arrays.<DataBook>asList(arrayOfDataBook);
/*     */   }
/*     */   
/*     */   public List<HrmScheduleSetDetail> getScheduleSetDetailList(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, String paramString1, String paramString2, List<HrmPubHoliday> paramList) {
/* 211 */     List<HrmScheduleSetDetail> list = null;
/* 212 */     List<DataBook> list1 = DateUtil.toDataBookList(paramString1, paramString2);
/* 213 */     List<HrmScheduleShiftsDetail> list2 = (new HrmScheduleShiftsDetailManager()).find("[map]field001:" + paramHrmScheduleShiftsSet.getId());
/* 214 */     StringBuffer stringBuffer = new StringBuffer();
/* 215 */     for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : list2) stringBuffer.append((stringBuffer.length() == 0) ? "" : ";").append(hrmScheduleShiftsDetail.getField002()); 
/* 216 */     switch (paramHrmScheduleShiftsSet.getField003().intValue()) {
/*     */       case 0:
/* 218 */         list = getW0List(paramLong, paramHrmScheduleShiftsSet, list1, list2, paramList);
/*     */         break;
/*     */       case 1:
/* 221 */         list = getW1List(paramLong, paramHrmScheduleShiftsSet, list1, list2, paramList);
/*     */         break;
/*     */       case 2:
/* 224 */         list = getW2List(paramLong, paramHrmScheduleShiftsSet, list1, stringBuffer.toString(), paramList);
/*     */         break;
/*     */       case 3:
/* 227 */         list = getW3List(paramLong, paramHrmScheduleShiftsSet, list1, stringBuffer.toString(), paramList);
/*     */         break;
/*     */       case 4:
/* 230 */         list = getW4List(paramLong, paramHrmScheduleShiftsSet, list1, stringBuffer.toString(), paramList);
/*     */         break;
/*     */       case 5:
/* 233 */         list = getW5List(paramLong, paramHrmScheduleShiftsSet, list1, stringBuffer.toString(), paramList);
/*     */         break;
/*     */       case 6:
/* 236 */         list = getW6List(paramLong, paramHrmScheduleShiftsSet, list1, stringBuffer.toString(), paramList);
/*     */         break;
/*     */     } 
/* 239 */     return (list == null) ? new ArrayList<>() : list;
/*     */   }
/*     */   
/*     */   private HrmScheduleSetDetail getBean(long paramLong, String paramString1, String paramString2, int paramInt, String paramString3) {
/* 243 */     HrmScheduleSetDetail hrmScheduleSetDetail = new HrmScheduleSetDetail();
/* 244 */     hrmScheduleSetDetail.setSetId(Long.valueOf(paramLong));
/* 245 */     hrmScheduleSetDetail.setField001(paramString1);
/* 246 */     hrmScheduleSetDetail.setField003(paramString2);
/* 247 */     hrmScheduleSetDetail.setField004(Integer.valueOf(paramInt));
/* 248 */     hrmScheduleSetDetail.setField005(paramString3);
/* 249 */     return hrmScheduleSetDetail;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmScheduleSetDetail> getW0List(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, List<DataBook> paramList, List<HrmScheduleShiftsDetail> paramList1, List<HrmPubHoliday> paramList2) {
/* 260 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 261 */     int i = -1;
/* 262 */     String str = "";
/* 263 */     for (DataBook dataBook : paramList) {
/* 264 */       i = StringUtil.parseToInt(dataBook.getId());
/* 265 */       str = dataBook.getValue();
/* 266 */       String str1 = "";
/* 267 */       if (!isContinue(paramHrmScheduleShiftsSet, str, paramList2))
/* 268 */         for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : paramList1) {
/* 269 */           str1 = getWorkTimeByWeek(i, hrmScheduleShiftsDetail);
/* 270 */           if (StringUtil.isNotNull(str1))
/*     */             break; 
/*     */         }  
/* 273 */       arrayList.add(getBean(paramLong, String.valueOf(paramHrmScheduleShiftsSet.getId()), str, i, str1));
/*     */     } 
/* 275 */     return arrayList;
/*     */   }
/*     */   
/*     */   private String getWorkTimeByWeek(int paramInt, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail) {
/* 279 */     String str = "";
/* 280 */     switch (paramInt) {
/*     */       case 1:
/* 282 */         if (paramHrmScheduleShiftsDetail.getW001().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 2:
/* 285 */         if (paramHrmScheduleShiftsDetail.getW002().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 3:
/* 288 */         if (paramHrmScheduleShiftsDetail.getW003().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 4:
/* 291 */         if (paramHrmScheduleShiftsDetail.getW004().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 5:
/* 294 */         if (paramHrmScheduleShiftsDetail.getW005().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 6:
/* 297 */         if (paramHrmScheduleShiftsDetail.getW006().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 7:
/* 300 */         if (paramHrmScheduleShiftsDetail.getW007().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */     } 
/* 303 */     return str;
/*     */   }
/*     */   
/*     */   private boolean checkDate(List<DataBook> paramList, String paramString, int paramInt, boolean paramBoolean) {
/* 307 */     boolean bool = false;
/*     */     
/* 309 */     byte b1 = (paramInt < 1) ? 0 : ((paramInt > 31) ? 31 : paramInt);
/* 310 */     byte b2 = (paramList == null) ? 0 : paramList.size();
/* 311 */     DataBook dataBook = null;
/* 312 */     if (paramBoolean) {
/* 313 */       for (byte b = 0; b < b1; b++) {
/* 314 */         dataBook = paramList.get(b);
/* 315 */         if (dataBook.getValue().equals(paramString)) {
/* 316 */           bool = true;
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } else {
/* 321 */       for (int i = b2 - 1; i >= b2 - b1; i--) {
/* 322 */         dataBook = paramList.get(i);
/* 323 */         if (dataBook.getValue().equals(paramString)) {
/* 324 */           bool = true;
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 329 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getDateType(String paramString, List<HrmPubHoliday> paramList) {
/* 334 */     int i = 0;
/* 335 */     if (paramList != null) {
/* 336 */       for (HrmPubHoliday hrmPubHoliday : paramList) {
/* 337 */         if (!hrmPubHoliday.getHolidaydate().equals(paramString))
/*     */           continue; 
/* 339 */         i = hrmPubHoliday.getChangetype().intValue();
/*     */       } 
/*     */     }
/*     */     
/* 343 */     return i;
/*     */   }
/*     */   
/*     */   private boolean isContinue(HrmScheduleShiftsSet paramHrmScheduleShiftsSet, String paramString, List<HrmPubHoliday> paramList) {
/* 347 */     return (this.holidayManager.getDateType(paramString, paramList) == 1 && paramHrmScheduleShiftsSet.isContinue());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmScheduleSetDetail> getW1List(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, List<DataBook> paramList, List<HrmScheduleShiftsDetail> paramList1, List<HrmPubHoliday> paramList2) {
/* 358 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 359 */     int i = -1;
/* 360 */     String str = "";
/* 361 */     int j = 1;
/* 362 */     for (DataBook dataBook : paramList) {
/* 363 */       i = StringUtil.parseToInt(dataBook.getId());
/* 364 */       str = dataBook.getValue();
/* 365 */       String str1 = "";
/* 366 */       if (!isContinue(paramHrmScheduleShiftsSet, str, paramList2)) {
/* 367 */         j = StringUtil.parseToInt(str.split("-")[2]);
/* 368 */         for (HrmScheduleShiftsDetail hrmScheduleShiftsDetail : paramList1) {
/* 369 */           if (hrmScheduleShiftsDetail.getField003().intValue() == 1) {
/* 370 */             if (hrmScheduleShiftsDetail.getField004().intValue() == 0) {
/* 371 */               if (checkDate(paramList, str, hrmScheduleShiftsDetail.getField005().intValue(), true)) {
/* 372 */                 str1 = hrmScheduleShiftsDetail.getField002();
/*     */               }
/*     */             }
/* 375 */             else if (checkDate(paramList, str, hrmScheduleShiftsDetail.getField005().intValue(), false)) {
/* 376 */               str1 = hrmScheduleShiftsDetail.getField002();
/*     */             } 
/*     */           } else {
/*     */             
/* 380 */             str1 = getWorkTimeByDay(j, hrmScheduleShiftsDetail);
/*     */           } 
/* 382 */           if (StringUtil.isNotNull(str1))
/*     */             break; 
/*     */         } 
/* 385 */       }  arrayList.add(getBean(paramLong, String.valueOf(paramHrmScheduleShiftsSet.getId()), str, i, str1));
/*     */     } 
/* 387 */     return arrayList;
/*     */   }
/*     */   
/*     */   private String getWorkTimeByDay(int paramInt, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail) {
/* 391 */     String str = "";
/* 392 */     switch (paramInt) {
/*     */       case 1:
/* 394 */         if (paramHrmScheduleShiftsDetail.getD001().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 2:
/* 397 */         if (paramHrmScheduleShiftsDetail.getD002().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 3:
/* 400 */         if (paramHrmScheduleShiftsDetail.getD003().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 4:
/* 403 */         if (paramHrmScheduleShiftsDetail.getD004().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 5:
/* 406 */         if (paramHrmScheduleShiftsDetail.getD005().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 6:
/* 409 */         if (paramHrmScheduleShiftsDetail.getD006().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 7:
/* 412 */         if (paramHrmScheduleShiftsDetail.getD007().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 8:
/* 415 */         if (paramHrmScheduleShiftsDetail.getD008().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 9:
/* 418 */         if (paramHrmScheduleShiftsDetail.getD009().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 10:
/* 421 */         if (paramHrmScheduleShiftsDetail.getD010().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 11:
/* 424 */         if (paramHrmScheduleShiftsDetail.getD011().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 12:
/* 427 */         if (paramHrmScheduleShiftsDetail.getD012().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 13:
/* 430 */         if (paramHrmScheduleShiftsDetail.getD013().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 14:
/* 433 */         if (paramHrmScheduleShiftsDetail.getD014().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 15:
/* 436 */         if (paramHrmScheduleShiftsDetail.getD015().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 16:
/* 439 */         if (paramHrmScheduleShiftsDetail.getD016().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 17:
/* 442 */         if (paramHrmScheduleShiftsDetail.getD017().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 18:
/* 445 */         if (paramHrmScheduleShiftsDetail.getD018().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 19:
/* 448 */         if (paramHrmScheduleShiftsDetail.getD019().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 20:
/* 451 */         if (paramHrmScheduleShiftsDetail.getD020().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 21:
/* 454 */         if (paramHrmScheduleShiftsDetail.getD021().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 22:
/* 457 */         if (paramHrmScheduleShiftsDetail.getD022().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 23:
/* 460 */         if (paramHrmScheduleShiftsDetail.getD023().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 24:
/* 463 */         if (paramHrmScheduleShiftsDetail.getD024().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 25:
/* 466 */         if (paramHrmScheduleShiftsDetail.getD025().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 26:
/* 469 */         if (paramHrmScheduleShiftsDetail.getD026().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 27:
/* 472 */         if (paramHrmScheduleShiftsDetail.getD027().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 28:
/* 475 */         if (paramHrmScheduleShiftsDetail.getD028().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 29:
/* 478 */         if (paramHrmScheduleShiftsDetail.getD029().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 30:
/* 481 */         if (paramHrmScheduleShiftsDetail.getD030().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */       case 31:
/* 484 */         if (paramHrmScheduleShiftsDetail.getD031().intValue() == 1) str = paramHrmScheduleShiftsDetail.getField002(); 
/*     */         break;
/*     */     } 
/* 487 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmScheduleSetDetail> getW2List(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, List<DataBook> paramList, String paramString, List<HrmPubHoliday> paramList1) {
/* 498 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 499 */     boolean bool = true;
/* 500 */     String str = "";
/* 501 */     for (DataBook dataBook : paramList) {
/* 502 */       str = isContinue(paramHrmScheduleShiftsSet, dataBook.getValue(), paramList1) ? "" : (bool ? paramString : "");
/* 503 */       arrayList.add(getBean(paramLong, String.valueOf(paramHrmScheduleShiftsSet.getId()), dataBook.getValue(), StringUtil.parseToInt(dataBook.getId()), str));
/* 504 */       bool = bool ? false : true;
/*     */     } 
/* 506 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmScheduleSetDetail> getW3List(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, List<DataBook> paramList, String paramString, List<HrmPubHoliday> paramList1) {
/* 517 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 518 */     boolean bool = true;
/* 519 */     byte b = 0;
/* 520 */     String str = "";
/* 521 */     for (DataBook dataBook : paramList) {
/* 522 */       if (b == 2) {
/* 523 */         bool = false;
/* 524 */         b = 0;
/*     */       } else {
/* 526 */         bool = true;
/*     */       } 
/* 528 */       str = isContinue(paramHrmScheduleShiftsSet, dataBook.getValue(), paramList1) ? "" : (bool ? paramString : "");
/* 529 */       arrayList.add(getBean(paramLong, String.valueOf(paramHrmScheduleShiftsSet.getId()), dataBook.getValue(), StringUtil.parseToInt(dataBook.getId()), str));
/*     */       
/* 531 */       if (bool) b++; 
/*     */     } 
/* 533 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmScheduleSetDetail> getW4List(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, List<DataBook> paramList, String paramString, List<HrmPubHoliday> paramList1) {
/* 544 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 545 */     boolean bool = true;
/* 546 */     int i = -1;
/* 547 */     String str = "";
/* 548 */     for (DataBook dataBook : paramList) {
/* 549 */       i = StringUtil.parseToInt(dataBook.getId());
/* 550 */       bool = (i != 7) ? true : false;
/* 551 */       str = isContinue(paramHrmScheduleShiftsSet, dataBook.getValue(), paramList1) ? "" : (bool ? paramString : "");
/* 552 */       arrayList.add(getBean(paramLong, String.valueOf(paramHrmScheduleShiftsSet.getId()), dataBook.getValue(), i, str));
/*     */     } 
/* 554 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmScheduleSetDetail> getW5List(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, List<DataBook> paramList, String paramString, List<HrmPubHoliday> paramList1) {
/* 565 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 566 */     boolean bool = true;
/* 567 */     int i = -1;
/* 568 */     String str = "";
/* 569 */     for (DataBook dataBook : paramList) {
/* 570 */       i = StringUtil.parseToInt(dataBook.getId());
/* 571 */       bool = (i != 6 && i != 7) ? true : false;
/* 572 */       str = isContinue(paramHrmScheduleShiftsSet, dataBook.getValue(), paramList1) ? "" : (bool ? paramString : "");
/* 573 */       arrayList.add(getBean(paramLong, String.valueOf(paramHrmScheduleShiftsSet.getId()), dataBook.getValue(), i, str));
/*     */     } 
/* 575 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<HrmScheduleSetDetail> getW6List(long paramLong, HrmScheduleShiftsSet paramHrmScheduleShiftsSet, List<DataBook> paramList, String paramString, List<HrmPubHoliday> paramList1) {
/* 586 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 587 */     boolean bool = true;
/* 588 */     int i = paramHrmScheduleShiftsSet.getField004().intValue();
/* 589 */     int j = paramHrmScheduleShiftsSet.getField005().intValue();
/* 590 */     int k = 0, m = 0;
/* 591 */     String str = "";
/* 592 */     for (DataBook dataBook : paramList) {
/* 593 */       if (bool) {
/* 594 */         if (k == i) {
/* 595 */           bool = false;
/* 596 */           k = 0;
/*     */         }
/*     */       
/* 599 */       } else if (m == j) {
/* 600 */         bool = true;
/* 601 */         m = 0;
/*     */       } 
/*     */       
/* 604 */       str = isContinue(paramHrmScheduleShiftsSet, dataBook.getValue(), paramList1) ? "" : (bool ? paramString : "");
/* 605 */       arrayList.add(getBean(paramLong, String.valueOf(paramHrmScheduleShiftsSet.getId()), dataBook.getValue(), StringUtil.parseToInt(dataBook.getId()), str));
/*     */       
/* 607 */       if (bool) { k++; continue; }
/* 608 */        m++;
/*     */     } 
/* 610 */     return arrayList;
/*     */   }
/*     */   
/*     */   public List<HrmScheduleShiftsSet> getScheduleShifts(String paramString1, String paramString2, String paramString3) {
/* 614 */     return this.dao.getScheduleShifts(paramString1, paramString2, paramString3);
/*     */   }
/*     */   
/*     */   public Long getRealShiftsSetId(String paramString) {
/* 618 */     return this.dao.getRealShiftsSetId(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmScheduleShiftsSetManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */