/*     */ package weaver.hrm.schedule.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleSetDetailCache;
/*     */ import weaver.hrm.schedule.dao.HrmScheduleSetDetailDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSetDetailManager
/*     */   extends BaseManager<HrmScheduleSetDetail>
/*     */ {
/*  26 */   private HrmScheduleSetDetailDao dao = null;
/*     */   
/*     */   public HrmScheduleSetDetailManager() {
/*  29 */     this((HttpServletRequest)null, (HttpServletResponse)null);
/*     */   }
/*     */   public HrmScheduleSetDetailManager(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  32 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*  33 */     this.dao = new HrmScheduleSetDetailDao();
/*  34 */     setDao((BaseDao)this.dao);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleSetDetail paramHrmScheduleSetDetail) {
/*  38 */     return save(paramHrmScheduleSetDetail, false);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleSetDetail paramHrmScheduleSetDetail, boolean paramBoolean) {
/*  42 */     String str = String.valueOf(paramHrmScheduleSetDetail.getId());
/*  43 */     if (paramBoolean || str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmScheduleSetDetail)); }
/*  44 */     else { update(paramHrmScheduleSetDetail); }
/*  45 */      return Long.valueOf(str);
/*     */   }
/*     */   
/*     */   public void saveSchedulePerson(HrmScheduleSetDetail paramHrmScheduleSetDetail) {
/*  49 */     saveSchedulePerson(paramHrmScheduleSetDetail, (String)null);
/*     */   }
/*     */   
/*     */   public void saveSchedulePerson(HrmScheduleSetDetail paramHrmScheduleSetDetail, String paramString) {
/*  53 */     this.dao.saveSchedulePerson(paramHrmScheduleSetDetail, paramString);
/*     */   }
/*     */   
/*     */   public void deleteBeforeSaveSchedulePerson(Long paramLong, String paramString1, String paramString2) {
/*  57 */     deleteBeforeSaveSchedulePerson(paramLong, paramString1, paramString2, (String)null);
/*     */   }
/*     */   
/*     */   public void deleteBeforeSaveSchedulePerson(Long paramLong, String paramString1, String paramString2, String paramString3) {
/*  61 */     this.dao.deleteBeforeSaveSchedulePerson(paramLong, paramString1, paramString2, paramString3);
/*     */   }
/*     */   
/*     */   public void deletePersonSchedule(Long paramLong, String paramString1, String paramString2, String paramString3) {
/*  65 */     this.dao.deletePersonSchedule(paramLong, paramString1, paramString2, paramString3);
/*     */   }
/*     */   
/*     */   public int count(String paramString) {
/*  69 */     return this.dao.count(getMapParam(paramString));
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getGroupData(String paramString1, String paramString2) {
/*  73 */     return getGroupData(paramString1, paramString2, -1);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getGroupData(String paramString1, String paramString2, int paramInt) {
/*  77 */     return getGroupData(paramString1, paramString2, (String)null, (String)null, paramInt);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getGroupData(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  81 */     return getGroupData(paramString1, paramString2, paramString3, paramString4, -1);
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getGroupData(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/*  85 */     return this.dao.getGroupData(paramString1, paramString2, paramString3, paramString4, paramInt);
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/*  89 */     this.dao.delete(paramMap);
/*     */   }
/*     */   
/*     */   public Map<String, HrmScheduleSetDetail> findMap(String paramString) {
/*  93 */     List list = find(paramString);
/*  94 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*  95 */     if (list != null) {
/*  96 */       for (HrmScheduleSetDetail hrmScheduleSetDetail : list) {
/*  97 */         hashMap.put(hrmScheduleSetDetail.getField003(), hrmScheduleSetDetail);
/*     */       }
/*     */     }
/* 100 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private List<HrmScheduleSetDetail> getList(String paramString1, String paramString2) {
/* 104 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/* 105 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/* 106 */     List list = (new HrmScheduleSetDetailCache()).getResult();
/* 107 */     for (String str : arrayOfString) {
/* 108 */       for (HrmScheduleSetDetail hrmScheduleSetDetail : list) {
/* 109 */         if (str.equals(StringUtil.vString(hrmScheduleSetDetail.getId()))) {
/* 110 */           arrayList.add(hrmScheduleSetDetail);
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 115 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString) {
/* 119 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString1, String paramString2) {
/* 123 */     String str = getAppendStr(paramString2);
/* 124 */     StringBuffer stringBuffer = new StringBuffer();
/* 125 */     List<HrmScheduleSetDetail> list = getList(paramString1, paramString2);
/* 126 */     for (HrmScheduleSetDetail hrmScheduleSetDetail : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmScheduleSetDetail.getField005()); 
/* 127 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmScheduleSetDetailManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */