/*     */ package weaver.hrm.schedule.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleWorktimeCache;
/*     */ import weaver.hrm.schedule.dao.HrmScheduleWorktimeDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleWorktime;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleWorktimeManager
/*     */   extends BaseManager<HrmScheduleWorktime>
/*     */ {
/*  29 */   private HrmScheduleWorktimeDao dao = null;
/*     */   
/*     */   public HrmScheduleWorktimeManager() {
/*  32 */     this((HttpServletRequest)null, (HttpServletResponse)null);
/*     */   }
/*     */   
/*     */   public HrmScheduleWorktimeManager(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  36 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*  37 */     this.dao = new HrmScheduleWorktimeDao();
/*  38 */     setDao((BaseDao)this.dao);
/*     */   }
/*     */   
/*     */   public Long save(HrmScheduleWorktime paramHrmScheduleWorktime) {
/*  42 */     String str = String.valueOf(paramHrmScheduleWorktime.getId());
/*  43 */     if (str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmScheduleWorktime)); }
/*  44 */     else { update(paramHrmScheduleWorktime); }
/*  45 */      return Long.valueOf(str);
/*     */   }
/*     */   
/*     */   public int count(String paramString) {
/*  49 */     return this.dao.count(getMapParam(paramString));
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/*  53 */     this.dao.delete(paramMap);
/*     */   }
/*     */   
/*     */   private List<HrmScheduleWorktime> getList(String paramString1, String paramString2) {
/*  57 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/*  58 */     ArrayList<HrmScheduleWorktime> arrayList = new ArrayList();
/*  59 */     List list = (new HrmScheduleWorktimeCache()).getResult();
/*  60 */     for (String str : arrayOfString) {
/*  61 */       for (HrmScheduleWorktime hrmScheduleWorktime : list) {
/*  62 */         if (str.equals(StringUtil.vString(hrmScheduleWorktime.getId()))) {
/*  63 */           arrayList.add(hrmScheduleWorktime);
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/*  68 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString) {
/*  72 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString1, String paramString2) {
/*  76 */     String str = getAppendStr(paramString2);
/*  77 */     StringBuffer stringBuffer = new StringBuffer();
/*  78 */     List<HrmScheduleWorktime> list = getList(paramString1, paramString2);
/*  79 */     for (HrmScheduleWorktime hrmScheduleWorktime : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmScheduleWorktime.getField001()); 
/*  80 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSignDateTime(String paramString1, String paramString2, String paramString3) {
/*  90 */     String str = getAppendStr(paramString3);
/*  91 */     List<HrmScheduleWorktime> list = getList(paramString2, paramString3);
/*  92 */     boolean bool = (list == null) ? false : list.size();
/*  93 */     String[] arrayOfString = new String[bool];
/*  94 */     byte b = 0;
/*  95 */     for (HrmScheduleWorktime hrmScheduleWorktime : list) {
/*  96 */       arrayOfString[b] = hrmScheduleWorktime.getSignDateTime(paramString1);
/*  97 */       b++;
/*     */     } 
/*     */     
/* 100 */     Arrays.sort((Object[])arrayOfString);
/*     */     
/* 102 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 104 */     for (String str1 : arrayOfString) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(str1);
/*     */     
/* 106 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public Map<String, String> getTime(String paramString1, String paramString2) {
/* 110 */     String str = getAppendStr(paramString2);
/* 111 */     List<HrmScheduleWorktime> list = getList(paramString1, paramString2);
/* 112 */     boolean bool = (list == null) ? false : list.size();
/* 113 */     String[] arrayOfString1 = new String[bool], arrayOfString2 = new String[bool], arrayOfString3 = new String[bool];
/* 114 */     byte b = 0;
/* 115 */     for (HrmScheduleWorktime hrmScheduleWorktime : list) {
/* 116 */       arrayOfString1[b] = hrmScheduleWorktime.getTime();
/* 117 */       arrayOfString2[b] = hrmScheduleWorktime.getSignTime();
/* 118 */       arrayOfString3[b] = hrmScheduleWorktime.getRestTimes();
/* 119 */       b++;
/*     */     } 
/*     */     
/* 122 */     Arrays.sort((Object[])arrayOfString1);
/* 123 */     Arrays.sort((Object[])arrayOfString2);
/* 124 */     Arrays.sort((Object[])arrayOfString3);
/*     */     
/* 126 */     StringBuffer stringBuffer1 = new StringBuffer(), stringBuffer2 = new StringBuffer(), stringBuffer3 = new StringBuffer();
/* 127 */     for (String str1 : arrayOfString1) stringBuffer1.append((stringBuffer1.length() == 0) ? "" : str).append(str1); 
/* 128 */     for (String str1 : arrayOfString2) stringBuffer2.append((stringBuffer2.length() == 0) ? "" : str).append(str1); 
/* 129 */     for (String str1 : arrayOfString3) stringBuffer3.append((stringBuffer3.length() == 0) ? "" : str).append(str1);
/*     */     
/* 131 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 132 */     hashMap.put("workTime", stringBuffer1.toString());
/* 133 */     hashMap.put("signTime", stringBuffer2.toString());
/* 134 */     hashMap.put("restTime", stringBuffer3.toString());
/* 135 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getWorkTime(String paramString1, String paramString2) {
/* 139 */     return StringUtil.vString(getTime(paramString1, paramString2).get("workTime"));
/*     */   }
/*     */   
/*     */   public boolean isOverlap(String paramString) {
/* 143 */     List list = find("[map]sql_id:and t.id in(" + StringUtil.replace(paramString, ";", ",") + ");delflag:0");
/* 144 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 145 */     int i = 0;
/* 146 */     for (HrmScheduleWorktime hrmScheduleWorktime : list) {
/* 147 */       i += DateUtil.totalTime(hrmScheduleWorktime.getField002(), hrmScheduleWorktime.getField003(), hashMap);
/*     */     }
/* 149 */     return (i != hashMap.size());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmScheduleWorktimeManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */