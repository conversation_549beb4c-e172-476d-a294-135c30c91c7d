/*     */ package weaver.hrm.schedule.manager;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.hrm.schedule.cache.HrmSchedulePersonCache;
/*     */ import weaver.hrm.schedule.cache.HrmSchedulePersonnelCache;
/*     */ import weaver.hrm.schedule.dao.HrmSchedulePersonnelDao;
/*     */ import weaver.hrm.schedule.domain.HrmSchedulePerson;
/*     */ import weaver.hrm.schedule.domain.HrmSchedulePersonnel;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSchedulePersonnelManager
/*     */   extends BaseManager<HrmSchedulePersonnel>
/*     */ {
/*  36 */   private HrmSchedulePersonnelDao dao = null;
/*     */   
/*     */   public HrmSchedulePersonnelManager() {
/*  39 */     this((HttpServletRequest)null, (HttpServletResponse)null);
/*     */   }
/*     */   public HrmSchedulePersonnelManager(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  42 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*  43 */     this.dao = new HrmSchedulePersonnelDao();
/*  44 */     setDao((BaseDao)this.dao);
/*     */   }
/*     */   
/*     */   public Long save(HrmSchedulePersonnel paramHrmSchedulePersonnel) {
/*  48 */     String str = String.valueOf(paramHrmSchedulePersonnel.getId());
/*  49 */     if (str.equals("0") || str.equals("-1")) { str = String.valueOf(insert(paramHrmSchedulePersonnel)); }
/*  50 */     else { update(paramHrmSchedulePersonnel); }
/*  51 */      return Long.valueOf(str);
/*     */   }
/*     */   
/*     */   public int count(String paramString) {
/*  55 */     return this.dao.count(getMapParam(paramString));
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/*  59 */     this.dao.delete(paramMap);
/*     */   }
/*     */   
/*     */   public String getField002Value(String paramString1, String paramString2) {
/*  63 */     return getField002Value(paramString1, paramString2, "-1", "-1", "-1", String.valueOf(getLanguageId()));
/*     */   }
/*     */   
/*     */   public String getField007Value(String paramString1, String paramString2) {
/*  67 */     String str = "";
/*  68 */     if (paramString2.equals("0")) return str = ""; 
/*     */     try {
/*  70 */       switch (StringUtil.parseToInt(paramString1)) {
/*     */         case 0:
/*  72 */           str = SystemEnv.getHtmlLabelName(24665, getLanguageId());
/*     */           break;
/*     */         case 1:
/*  75 */           str = getMultiDepartmentName(paramString2);
/*     */           break;
/*     */         case 2:
/*  78 */           str = getMultiSubcompanyName(paramString2);
/*     */           break;
/*     */       } 
/*  81 */     } catch (Exception exception) {}
/*  82 */     return str;
/*     */   }
/*     */   
/*     */   public String getField002Value(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/*  86 */     String str = ""; try {
/*     */       int arrayOfInt1[], i, arrayOfInt2[], j;
/*  88 */       paramString2 = StringUtil.replace(paramString2, ";", ",");
/*  89 */       switch (StringUtil.parseToInt(paramString1)) {
/*     */         case 0:
/*  91 */           str = getMultiResourceName(paramString2);
/*     */           break;
/*     */         case 1:
/*  94 */           str = getMultiDepartmentName(paramString2);
/*     */           break;
/*     */         case 2:
/*  97 */           str = getMultiSubcompanyName(paramString2);
/*     */           break;
/*     */         case 3:
/* 100 */           str = (new RolesComInfo()).getRolesRemark(paramString2);
/* 101 */           arrayOfInt1 = new int[] { 124, 141, 140 };
/* 102 */           i = StringUtil.parseToInt(paramString3);
/* 103 */           if (i >= 0 && i < arrayOfInt1.length) {
/* 104 */             str = str + "/" + getLabelName(Integer.valueOf(arrayOfInt1[i]), paramString6);
/*     */           }
/*     */           break;
/*     */         case 7:
/* 108 */           str = (new JobTitlesComInfo()).getJobTitlesname(paramString2);
/* 109 */           arrayOfInt2 = new int[] { 140, 19438, 19437 };
/* 110 */           j = StringUtil.parseToInt(paramString4);
/* 111 */           if (j >= 0 && j < arrayOfInt2.length) {
/* 112 */             if (j == 0) {
/* 113 */               str = str + "/" + SystemEnv.getHtmlLabelName(140, getLanguageId()); break;
/* 114 */             }  if (j == 1) {
/* 115 */               str = str + "/" + SystemEnv.getHtmlLabelName(19438, getLanguageId());
/* 116 */               str = str + "(" + getMultiDepartmentName(paramString5) + ")"; break;
/* 117 */             }  if (j == 2) {
/* 118 */               str = str + "/" + SystemEnv.getHtmlLabelName(19437, getLanguageId());
/* 119 */               str = str + "(" + getMultiSubcompanyName(paramString5) + ")";
/*     */             } 
/*     */           } 
/*     */           break;
/*     */       } 
/* 124 */     } catch (Exception exception) {}
/* 125 */     return str;
/*     */   }
/*     */   
/*     */   private List<HrmSchedulePersonnel> getAllResult() {
/* 129 */     return (new HrmSchedulePersonnelCache()).getResult();
/*     */   }
/*     */   
/*     */   private List<HrmSchedulePersonnel> getList(String paramString1, String paramString2) {
/* 133 */     String[] arrayOfString = StringUtil.vString(paramString1).split(";");
/* 134 */     ArrayList<HrmSchedulePersonnel> arrayList = new ArrayList();
/* 135 */     List<HrmSchedulePersonnel> list = getAllResult();
/* 136 */     for (String str : arrayOfString) {
/* 137 */       for (HrmSchedulePersonnel hrmSchedulePersonnel : list) {
/* 138 */         if (str.equals(StringUtil.vString(hrmSchedulePersonnel.getId()))) {
/* 139 */           arrayList.add(hrmSchedulePersonnel);
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 144 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getPersonIds() {
/* 148 */     return getPersonIds((User)null, false);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPersonIds(User paramUser, boolean paramBoolean) {
/* 153 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 154 */     boolean bool = true;
/* 155 */     if (paramUser != null) {
/* 156 */       int[] arrayOfInt = null;
/* 157 */       bool = paramUser.getLoginid().equalsIgnoreCase("sysadmin");
/* 158 */       if (!bool && paramBoolean) {
/*     */         try {
/* 160 */           arrayOfInt = (new CheckSubCompanyRight()).getSubComByUserRightId(paramUser.getUID(), "HrmScheduling:set");
/* 161 */         } catch (Exception exception) {}
/*     */       }
/*     */       
/* 164 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 165 */       if (arrayOfInt != null) {
/* 166 */         for (int i : arrayOfInt) {
/* 167 */           stringBuffer1.append((stringBuffer1.length() == 0) ? "" : ",").append(i);
/* 168 */           hashMap.put(String.valueOf(i), Integer.valueOf(i));
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 173 */     StringBuffer stringBuffer = new StringBuffer();
/* 174 */     List list = (new HrmSchedulePersonCache()).getResult();
/* 175 */     for (HrmSchedulePerson hrmSchedulePerson : list) {
/* 176 */       if (bool || !paramBoolean || (hashMap.size() != 0 && hashMap.containsKey(StringUtil.vString(hrmSchedulePerson.getSubcompanyid1())))) {
/* 177 */         stringBuffer.append((stringBuffer.length() == 0) ? "" : ",").append(hrmSchedulePerson.getId());
/*     */       }
/*     */     } 
/* 180 */     return (stringBuffer.length() == 0) ? "-1" : stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString) {
/* 184 */     return getDescription(paramString, String.valueOf(getLanguageId()));
/*     */   }
/*     */   
/*     */   public String getDescription(String paramString1, String paramString2) {
/* 188 */     String str = getAppendStr(paramString2);
/* 189 */     StringBuffer stringBuffer = new StringBuffer();
/* 190 */     List<HrmSchedulePersonnel> list = getList(paramString1, paramString2);
/* 191 */     for (HrmSchedulePersonnel hrmSchedulePersonnel : list) stringBuffer.append((stringBuffer.length() == 0) ? "" : str).append(hrmSchedulePersonnel.getField001()); 
/* 192 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getSchedulePersons(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 196 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/* 197 */     if (paramMap == null) return arrayList; 
/* 198 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 199 */     Iterator<Map.Entry> iterator = paramMap.entrySet().iterator();
/* 200 */     while (iterator.hasNext()) {
/* 201 */       Map.Entry entry = iterator.next();
/* 202 */       String str = StringUtil.vString(entry.getValue());
/* 203 */       if (str.length() == 0 || str.equals("-1"))
/* 204 */         continue;  hashMap1.put(entry.getKey(), str);
/*     */     } 
/* 206 */     HashMap<Object, Object> hashMap2 = null;
/* 207 */     List<HrmSchedulePerson> list = getSchedulePersons((Map)hashMap1);
/* 208 */     for (HrmSchedulePerson hrmSchedulePerson : list) {
/* 209 */       hashMap2 = new HashMap<>();
/* 210 */       hashMap2.put("id", StringUtil.vString(hrmSchedulePerson.getId()));
/* 211 */       hashMap2.put("workcode", StringUtil.vString(hrmSchedulePerson.getWorkcode()));
/* 212 */       hashMap2.put("lastname", StringUtil.vString(hrmSchedulePerson.getLastname()));
/* 213 */       hashMap2.put("subcompanyName", StringUtil.vString(hrmSchedulePerson.getSubcompanyName()));
/* 214 */       hashMap2.put("departmentName", StringUtil.vString(hrmSchedulePerson.getDepartmentName()));
/* 215 */       hashMap2.put("jobtitleName", StringUtil.vString(hrmSchedulePerson.getJobtitleName()));
/* 216 */       hashMap2.put("stype", StringUtil.vString(hrmSchedulePerson.getStype()));
/* 217 */       arrayList.add(hashMap2);
/*     */     } 
/* 219 */     return arrayList;
/*     */   }
/*     */   
/*     */   public List<HrmSchedulePerson> getSchedulePersons(Map<String, Comparable> paramMap) {
/* 223 */     return this.dao.findSchedulePersons(paramMap);
/*     */   }
/*     */   
/*     */   public String getSchedulePersonsSQL(Map<String, Comparable> paramMap) {
/* 227 */     return this.dao.getSchedulePersonsSQL(paramMap);
/*     */   }
/*     */   
/*     */   public boolean isSchedulePerson(int paramInt) {
/* 231 */     return isSchedulePerson(paramInt, DateUtil.getCurrentDate());
/*     */   }
/*     */   
/*     */   public boolean isSchedulePerson(int paramInt, String paramString) {
/* 235 */     return isSchedulePerson(paramInt, paramString, 1);
/*     */   }
/*     */   
/*     */   public boolean isSchedulePerson(int paramInt1, String paramString, int paramInt2) {
/* 239 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 240 */     hashMap.put("userId", Integer.valueOf(paramInt1));
/* 241 */     hashMap.put("paramField001Start", paramString);
/* 242 */     hashMap.put("paramField001End", paramString);
/* 243 */     hashMap.put("stype", Integer.valueOf(paramInt2));
/* 244 */     return (getSchedulePersons((Map)hashMap).size() > 0);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/HrmSchedulePersonnelManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */