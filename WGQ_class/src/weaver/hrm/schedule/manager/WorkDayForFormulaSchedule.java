/*    */ package weaver.hrm.schedule.manager;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Date;
/*    */ import java.util.Iterator;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkDayForFormulaSchedule
/*    */   extends BaseCronJob
/*    */ {
/*    */   public void execute() {
/* 25 */     RecordSet recordSet = new RecordSet();
/* 26 */     BaseBean baseBean = new BaseBean();
/* 27 */     baseBean.writeLog("WorkDayForFormulaSchedule : start");
/*    */     
/* 29 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*    */ 
/*    */     
/* 32 */     String str1 = baseBean.getPropValue("scheduleWorkDayForFormula", "fromDate");
/* 33 */     str1 = (str1 == null) ? "" : str1.trim();
/* 34 */     if (str1.length() == 0) {
/* 35 */       str1 = simpleDateFormat.format(new Date());
/*    */     }
/*    */ 
/*    */     
/* 39 */     String str2 = baseBean.getPropValue("scheduleWorkDayForFormula", "endDate");
/* 40 */     str2 = (str2 == null) ? "" : str2.trim();
/* 41 */     if (str2.length() == 0) {
/* 42 */       str2 = simpleDateFormat.format(new Date());
/*    */     }
/*    */     
/* 45 */     HrmScheduleManager hrmScheduleManager = null;
/* 46 */     ResourceComInfo resourceComInfo = null;
/* 47 */     int i = -1;
/* 48 */     Map<String, Boolean> map = null;
/*    */     
/*    */     try {
/* 51 */       resourceComInfo = new ResourceComInfo();
/* 52 */     } catch (Exception exception) {
/* 53 */       baseBean.writeLog(exception.toString());
/*    */     } 
/* 55 */     String str3 = "insert into workflow_workday_formula(userid, currdate, isworkday) values(?,?,?)";
/* 56 */     String str4 = "delete from workflow_workday_formula where userid = ? and currdate >= ? and currdate <= ?";
/* 57 */     ArrayList arrayList1 = new ArrayList();
/* 58 */     ArrayList arrayList2 = new ArrayList();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 64 */     if (resourceComInfo != null)
/* 65 */       while (resourceComInfo.next()) {
/* 66 */         User user = new User(Integer.valueOf(resourceComInfo.getResourceid()).intValue());
/* 67 */         int j = user.getUID();
/*    */         
/* 69 */         recordSet.executeUpdate(str4, new Object[] { Integer.valueOf(j), str1, str2 });
/* 70 */         hrmScheduleManager = new HrmScheduleManager(user);
/* 71 */         i = hrmScheduleManager.getSubCompanyId();
/* 72 */         map = hrmScheduleManager.isWorkday(str1, str2, i);
/* 73 */         if (map != null && map.size() > 0) {
/* 74 */           Iterator<String> iterator = map.keySet().iterator();
/* 75 */           while (iterator.hasNext()) {
/* 76 */             String str = iterator.next();
/* 77 */             Boolean bool = map.get(str);
/* 78 */             boolean bool1 = bool.booleanValue() ? true : false;
/* 79 */             recordSet.executeUpdate(str3, new Object[] { Integer.valueOf(j), str, Integer.valueOf(bool1) });
/*    */           } 
/*    */         } 
/*    */       }  
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/manager/WorkDayForFormulaSchedule.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */