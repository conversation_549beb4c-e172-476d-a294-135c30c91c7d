/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleShiftsSet
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Long id;
/*     */   private Integer delflag;
/*     */   private Long creater;
/*     */   private String createTime;
/*     */   private Long lastModifier;
/*     */   private String lastModificationTime;
/*     */   private Integer sn;
/*     */   private String field001;
/*     */   private Integer field002;
/*     */   private Integer field003;
/*     */   private Integer field004;
/*     */   private Integer field005;
/*     */   private Integer field006;
/*     */   private String field007;
/*     */   public static final int SCHEDULE_SHIFTS_LABEL = 125845;
/*     */   
/*     */   public HrmScheduleShiftsSet() {
/*  48 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsSet(Long paramLong) {
/*  52 */     this(true);
/*  53 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsSet(boolean paramBoolean) {
/*  57 */     if (paramBoolean) init(); 
/*     */   }
/*     */   
/*     */   public void init() {
/*  61 */     this.id = Long.valueOf(0L);
/*  62 */     this.delflag = Integer.valueOf(0);
/*  63 */     this.creater = Long.valueOf(0L);
/*  64 */     this.createTime = "";
/*  65 */     this.lastModifier = Long.valueOf(0L);
/*  66 */     this.lastModificationTime = "";
/*  67 */     this.sn = Integer.valueOf(0);
/*  68 */     this.field001 = "";
/*  69 */     this.field002 = Integer.valueOf(0);
/*  70 */     this.field003 = Integer.valueOf(0);
/*  71 */     this.field004 = Integer.valueOf(3);
/*  72 */     this.field005 = Integer.valueOf(1);
/*  73 */     this.field006 = Integer.valueOf(0);
/*  74 */     this.field007 = "#d9ead3";
/*     */   }
/*     */   
/*     */   public void setId(Long paramLong) {
/*  78 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public Long getId() {
/*  82 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setDelflag(Integer paramInteger) {
/*  86 */     this.delflag = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getDelflag() {
/*  90 */     return this.delflag;
/*     */   }
/*     */   
/*     */   public void setCreater(Long paramLong) {
/*  94 */     this.creater = paramLong;
/*     */   }
/*     */   
/*     */   public Long getCreater() {
/*  98 */     return this.creater;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 102 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/* 106 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setLastModifier(Long paramLong) {
/* 110 */     this.lastModifier = paramLong;
/*     */   }
/*     */   
/*     */   public Long getLastModifier() {
/* 114 */     return this.lastModifier;
/*     */   }
/*     */   
/*     */   public void setLastModificationTime(String paramString) {
/* 118 */     this.lastModificationTime = paramString;
/*     */   }
/*     */   
/*     */   public String getLastModificationTime() {
/* 122 */     return this.lastModificationTime;
/*     */   }
/*     */   
/*     */   public void setSn(Integer paramInteger) {
/* 126 */     this.sn = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getSn() {
/* 130 */     return this.sn;
/*     */   }
/*     */   
/*     */   public void setField001(String paramString) {
/* 134 */     this.field001 = paramString;
/*     */   }
/*     */   
/*     */   public String getField001() {
/* 138 */     return this.field001;
/*     */   }
/*     */   
/*     */   public void setField002(Integer paramInteger) {
/* 142 */     this.field002 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField002() {
/* 146 */     return this.field002;
/*     */   }
/*     */   
/*     */   public void setField003(Integer paramInteger) {
/* 150 */     this.field003 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField003() {
/* 154 */     return this.field003;
/*     */   }
/*     */   
/*     */   public void setField004(Integer paramInteger) {
/* 158 */     this.field004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField004() {
/* 162 */     return this.field004;
/*     */   }
/*     */   
/*     */   public void setField005(Integer paramInteger) {
/* 166 */     this.field005 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField005() {
/* 170 */     return this.field005;
/*     */   }
/*     */   
/*     */   public void setField006(Integer paramInteger) {
/* 174 */     this.field006 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField006() {
/* 178 */     return this.field006;
/*     */   }
/*     */   
/*     */   public void setField007(String paramString) {
/* 182 */     this.field007 = paramString;
/*     */   }
/*     */   
/*     */   public String getField007() {
/* 186 */     return this.field007;
/*     */   }
/*     */   
/*     */   public String getField003Name(int paramInt) {
/* 190 */     String str = "";
/* 191 */     switch (this.field003.intValue())
/*     */     { case 0:
/* 193 */         str = SystemEnv.getHtmlLabelName(125827, paramInt);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 214 */         return str;case 1: str = SystemEnv.getHtmlLabelName(125828, paramInt); return str;case 2: str = SystemEnv.getHtmlLabelName(125823, paramInt); return str;case 3: str = SystemEnv.getHtmlLabelName(125824, paramInt); return str;case 4: str = SystemEnv.getHtmlLabelName(125825, paramInt); return str;case 5: str = SystemEnv.getHtmlLabelName(125826, paramInt); return str; }  str = SystemEnv.getHtmlLabelName(19516, paramInt); return str;
/*     */   }
/*     */   
/*     */   public boolean isContinue() {
/* 218 */     return (this.field006.intValue() == 0);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleShiftsSet.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */