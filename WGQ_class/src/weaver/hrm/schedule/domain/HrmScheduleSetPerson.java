/*    */ package weaver.hrm.schedule.domain;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleSetPerson
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private Long id;
/*    */   private Integer delflag;
/*    */   private Long field001;
/*    */   private Integer field002;
/*    */   
/*    */   public HrmScheduleSetPerson() {
/* 24 */     this(true);
/*    */   }
/*    */   
/*    */   public HrmScheduleSetPerson(Long paramLong) {
/* 28 */     this(true);
/* 29 */     this.id = paramLong;
/*    */   }
/*    */   
/*    */   public HrmScheduleSetPerson(boolean paramBoolean) {
/* 33 */     if (paramBoolean) init(); 
/*    */   }
/*    */   
/*    */   public void init() {
/* 37 */     this.id = Long.valueOf(0L);
/* 38 */     this.delflag = Integer.valueOf(0);
/* 39 */     this.field001 = Long.valueOf(0L);
/* 40 */     this.field002 = Integer.valueOf(0);
/*    */   }
/*    */   
/*    */   public void setId(Long paramLong) {
/* 44 */     this.id = paramLong;
/*    */   }
/*    */   
/*    */   public Long getId() {
/* 48 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setDelflag(Integer paramInteger) {
/* 52 */     this.delflag = paramInteger;
/*    */   }
/*    */   
/*    */   public Integer getDelflag() {
/* 56 */     return this.delflag;
/*    */   }
/*    */   
/*    */   public void setField001(Long paramLong) {
/* 60 */     this.field001 = paramLong;
/*    */   }
/*    */   
/*    */   public Long getField001() {
/* 64 */     return this.field001;
/*    */   }
/*    */   
/*    */   public void setField002(Integer paramInteger) {
/* 68 */     this.field002 = paramInteger;
/*    */   }
/*    */   
/*    */   public Integer getField002() {
/* 72 */     return this.field002;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleSetPerson.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */