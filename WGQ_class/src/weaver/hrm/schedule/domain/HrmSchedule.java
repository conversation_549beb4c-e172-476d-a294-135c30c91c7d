/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSchedule
/*     */   implements Serializable, Comparable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private int userId;
/*     */   private String userType;
/*     */   private String signType;
/*     */   private String signDate;
/*     */   private String oldSignDate;
/*     */   private String signTime;
/*     */   private String clientAddress;
/*     */   private String isInCom;
/*     */   private String signFrom;
/*     */   private String longitude;
/*     */   private String latitude;
/*     */   private String addr;
/*     */   private int subCompanyId;
/*     */   private int departmentId;
/*     */   private int resourceId;
/*     */   private String resourceName;
/*     */   private String departmentName;
/*     */   private String status;
/*     */   private String signStartTime;
/*     */   private String signEndTime;
/*     */   private boolean needSign;
/*     */   private Integer id;
/*     */   private Integer relatedid;
/*     */   private int week;
/*     */   private String curDate;
/*     */   private String monstarttime1;
/*     */   private String monendtime1;
/*     */   private String monstarttime2;
/*     */   private String monendtime2;
/*     */   private String tuestarttime1;
/*     */   private String tueendtime1;
/*     */   private String tuestarttime2;
/*     */   private String tueendtime2;
/*     */   private String wedstarttime1;
/*     */   private String wedendtime1;
/*     */   private String wedstarttime2;
/*     */   private String wedendtime2;
/*     */   private String thustarttime1;
/*     */   private String thuendtime1;
/*     */   private String thustarttime2;
/*     */   private String thuendtime2;
/*     */   private String fristarttime1;
/*     */   private String friendtime1;
/*     */   private String fristarttime2;
/*     */   private String friendtime2;
/*     */   private String satstarttime1;
/*     */   private String satendtime1;
/*     */   private String satstarttime2;
/*     */   private String satendtime2;
/*     */   private String sunstarttime1;
/*     */   private String sunendtime1;
/*     */   private String sunstarttime2;
/*     */   private String sunendtime2;
/*     */   private String totaltime;
/*     */   private String scheduletype;
/*     */   private String validedatefrom;
/*     */   private String validedateto;
/*     */   private String startWorkTime;
/*     */   private String onDutyTimeAM;
/*     */   private String offDutyTimeAM;
/*     */   private String onDutyTimePM;
/*     */   private String offDutyTimePM;
/*     */   private String $scheduleName;
/*     */   private String $scheduleTitle;
/*     */   private String $signType;
/*     */   private String $signStartTime;
/*     */   private List<String[]> workTimes;
/*     */   private List<String[]> signTimes;
/*     */   private List<String[]> restTimes;
/*     */   private String dsSignType;
/*     */   private boolean isSchedulePerson;
/*     */   private String workcode;
/*     */   public List<String> fDates;
/*     */   public List<String> sDates;
/*     */   
/*     */   public HrmSchedule() {
/* 178 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmSchedule(boolean paramBoolean) {
/* 182 */     if (paramBoolean) init(); 
/*     */   }
/*     */   
/*     */   public void init() {
/* 186 */     this.id = Integer.valueOf(0);
/* 187 */     this.week = 0;
/* 188 */     this.relatedid = Integer.valueOf(0);
/* 189 */     this.curDate = "";
/* 190 */     this.monstarttime1 = "";
/* 191 */     this.monendtime1 = "";
/* 192 */     this.monstarttime2 = "";
/* 193 */     this.monendtime2 = "";
/* 194 */     this.tuestarttime1 = "";
/* 195 */     this.tueendtime1 = "";
/* 196 */     this.tuestarttime2 = "";
/* 197 */     this.tueendtime2 = "";
/* 198 */     this.wedstarttime1 = "";
/* 199 */     this.wedendtime1 = "";
/* 200 */     this.wedstarttime2 = "";
/* 201 */     this.wedendtime2 = "";
/* 202 */     this.thustarttime1 = "";
/* 203 */     this.thuendtime1 = "";
/* 204 */     this.thustarttime2 = "";
/* 205 */     this.thuendtime2 = "";
/* 206 */     this.fristarttime1 = "";
/* 207 */     this.friendtime1 = "";
/* 208 */     this.fristarttime2 = "";
/* 209 */     this.friendtime2 = "";
/* 210 */     this.satstarttime1 = "";
/* 211 */     this.satendtime1 = "";
/* 212 */     this.satstarttime2 = "";
/* 213 */     this.satendtime2 = "";
/* 214 */     this.sunstarttime1 = "";
/* 215 */     this.sunendtime1 = "";
/* 216 */     this.sunstarttime2 = "";
/* 217 */     this.sunendtime2 = "";
/* 218 */     this.totaltime = "";
/* 219 */     this.scheduletype = "";
/* 220 */     this.validedatefrom = "";
/* 221 */     this.validedateto = "";
/* 222 */     this.signType = "";
/* 223 */     this.signTime = "";
/* 224 */     this.signStartTime = "";
/* 225 */     this.startWorkTime = "";
/* 226 */     this.onDutyTimeAM = "";
/* 227 */     this.offDutyTimeAM = "";
/* 228 */     this.onDutyTimePM = "";
/* 229 */     this.offDutyTimePM = "";
/* 230 */     this.dsSignType = "";
/* 231 */     this.isSchedulePerson = false;
/* 232 */     this.fDates = new ArrayList<>();
/* 233 */     this.sDates = new ArrayList<>();
/*     */   }
/*     */   
/*     */   public String getClientAddress() {
/* 237 */     return this.clientAddress;
/*     */   }
/*     */   
/*     */   public void setClientAddress(String paramString) {
/* 241 */     this.clientAddress = paramString;
/*     */   }
/*     */   
/*     */   public String getIsInCom() {
/* 245 */     return this.isInCom;
/*     */   }
/*     */   
/*     */   public void setIsInCom(String paramString) {
/* 249 */     this.isInCom = paramString;
/*     */   }
/*     */   
/*     */   public String getSignDate() {
/* 253 */     return this.signDate;
/*     */   }
/*     */   
/*     */   public void setSignDate(String paramString) {
/* 257 */     this.signDate = paramString;
/* 258 */     this.oldSignDate = paramString;
/*     */   }
/*     */   
/*     */   public void setTempSignDate(String paramString) {
/* 262 */     this.signDate = paramString;
/*     */   }
/*     */   
/*     */   public String getOldSignDate() {
/* 266 */     return this.oldSignDate;
/*     */   }
/*     */   
/*     */   public String getSignTime() {
/* 270 */     return this.signTime;
/*     */   }
/*     */   
/*     */   public void setSignTime(String paramString) {
/* 274 */     String str = StringUtil.vString(paramString);
/* 275 */     if (StringUtil.isNotNull(str)) {
/* 276 */       StringBuffer stringBuffer = new StringBuffer();
/* 277 */       String[] arrayOfString = str.split("\\:");
/* 278 */       for (String str1 : arrayOfString) {
/* 279 */         stringBuffer.append((stringBuffer.length() == 0) ? "" : ":").append((str1.length() == 1) ? ("0" + str1) : str1);
/*     */       }
/* 281 */       str = stringBuffer.toString();
/*     */     } 
/* 283 */     this.signTime = str;
/*     */   }
/*     */   
/*     */   public String getSignFullTime() {
/* 287 */     return this.signDate + " " + this.signTime;
/*     */   }
/*     */   
/*     */   public String getSignType() {
/* 291 */     return this.signType;
/*     */   }
/*     */   
/*     */   public void setSignType(String paramString) {
/* 295 */     this.signType = paramString;
/*     */   }
/*     */   
/*     */   public String getDepartmentName() {
/* 299 */     return this.departmentName;
/*     */   }
/*     */   
/*     */   public void setDepartmentName(String paramString) {
/* 303 */     this.departmentName = paramString;
/*     */   }
/*     */   
/*     */   public int getUserId() {
/* 307 */     return this.userId;
/*     */   }
/*     */   
/*     */   public void setUserId(int paramInt) {
/* 311 */     this.userId = paramInt;
/*     */   }
/*     */   
/*     */   public String getUserType() {
/* 315 */     return this.userType;
/*     */   }
/*     */   
/*     */   public void setUserType(String paramString) {
/* 319 */     this.userType = paramString;
/*     */   }
/*     */   
/*     */   public String getSignEndTime() {
/* 323 */     return this.signEndTime;
/*     */   }
/*     */   
/*     */   public void setSignEndTime(String paramString) {
/* 327 */     this.signEndTime = paramString;
/*     */   }
/*     */   
/*     */   public String getSignStartTime() {
/* 331 */     return this.signStartTime;
/*     */   }
/*     */   
/*     */   public void setSignStartTime(String paramString) {
/* 335 */     this.signStartTime = paramString;
/*     */   }
/*     */   
/*     */   public boolean isNeedSign() {
/* 339 */     return this.needSign;
/*     */   }
/*     */   
/*     */   public void setNeedSign(boolean paramBoolean) {
/* 343 */     this.needSign = paramBoolean;
/*     */   }
/*     */   
/*     */   public String getCurDate() {
/* 347 */     return this.curDate;
/*     */   }
/*     */   
/*     */   public void setCurDate(String paramString) {
/* 351 */     this.curDate = paramString;
/*     */   }
/*     */   
/*     */   public String getFriendtime1() {
/* 355 */     return this.friendtime1;
/*     */   }
/*     */   
/*     */   public void setFriendtime1(String paramString) {
/* 359 */     this.friendtime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getFriendtime2() {
/* 363 */     return this.friendtime2;
/*     */   }
/*     */   
/*     */   public void setFriendtime2(String paramString) {
/* 367 */     this.friendtime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getFristarttime1() {
/* 371 */     return this.fristarttime1;
/*     */   }
/*     */   
/*     */   public void setFristarttime1(String paramString) {
/* 375 */     this.fristarttime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getFristarttime2() {
/* 379 */     return this.fristarttime2;
/*     */   }
/*     */   
/*     */   public void setFristarttime2(String paramString) {
/* 383 */     this.fristarttime2 = paramString;
/*     */   }
/*     */   
/*     */   public Integer getId() {
/* 387 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(Integer paramInteger) {
/* 391 */     this.id = paramInteger;
/*     */   }
/*     */   
/*     */   public String getMonendtime1() {
/* 395 */     return this.monendtime1;
/*     */   }
/*     */   
/*     */   public void setMonendtime1(String paramString) {
/* 399 */     this.monendtime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getMonendtime2() {
/* 403 */     return this.monendtime2;
/*     */   }
/*     */   
/*     */   public void setMonendtime2(String paramString) {
/* 407 */     this.monendtime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getMonstarttime1() {
/* 411 */     return this.monstarttime1;
/*     */   }
/*     */   
/*     */   public void setMonstarttime1(String paramString) {
/* 415 */     this.monstarttime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getMonstarttime2() {
/* 419 */     return this.monstarttime2;
/*     */   }
/*     */   
/*     */   public void setMonstarttime2(String paramString) {
/* 423 */     this.monstarttime2 = paramString;
/*     */   }
/*     */   
/*     */   public Integer getRelatedid() {
/* 427 */     return this.relatedid;
/*     */   }
/*     */   
/*     */   public void setRelatedid(Integer paramInteger) {
/* 431 */     this.relatedid = paramInteger;
/*     */   }
/*     */   
/*     */   public String getSatendtime1() {
/* 435 */     return this.satendtime1;
/*     */   }
/*     */   
/*     */   public void setSatendtime1(String paramString) {
/* 439 */     this.satendtime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getSatendtime2() {
/* 443 */     return this.satendtime2;
/*     */   }
/*     */   
/*     */   public void setSatendtime2(String paramString) {
/* 447 */     this.satendtime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getSatstarttime1() {
/* 451 */     return this.satstarttime1;
/*     */   }
/*     */   
/*     */   public void setSatstarttime1(String paramString) {
/* 455 */     this.satstarttime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getSatstarttime2() {
/* 459 */     return this.satstarttime2;
/*     */   }
/*     */   
/*     */   public void setSatstarttime2(String paramString) {
/* 463 */     this.satstarttime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getScheduletype() {
/* 467 */     return this.scheduletype;
/*     */   }
/*     */   
/*     */   public void setScheduletype(String paramString) {
/* 471 */     this.scheduletype = paramString;
/*     */   }
/*     */   
/*     */   public String getSunendtime1() {
/* 475 */     return this.sunendtime1;
/*     */   }
/*     */   
/*     */   public void setSunendtime1(String paramString) {
/* 479 */     this.sunendtime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getSunendtime2() {
/* 483 */     return this.sunendtime2;
/*     */   }
/*     */   
/*     */   public void setSunendtime2(String paramString) {
/* 487 */     this.sunendtime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getSunstarttime1() {
/* 491 */     return this.sunstarttime1;
/*     */   }
/*     */   
/*     */   public void setSunstarttime1(String paramString) {
/* 495 */     this.sunstarttime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getSunstarttime2() {
/* 499 */     return this.sunstarttime2;
/*     */   }
/*     */   
/*     */   public void setSunstarttime2(String paramString) {
/* 503 */     this.sunstarttime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getThuendtime1() {
/* 507 */     return this.thuendtime1;
/*     */   }
/*     */   
/*     */   public void setThuendtime1(String paramString) {
/* 511 */     this.thuendtime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getThuendtime2() {
/* 515 */     return this.thuendtime2;
/*     */   }
/*     */   
/*     */   public void setThuendtime2(String paramString) {
/* 519 */     this.thuendtime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getThustarttime1() {
/* 523 */     return this.thustarttime1;
/*     */   }
/*     */   
/*     */   public void setThustarttime1(String paramString) {
/* 527 */     this.thustarttime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getThustarttime2() {
/* 531 */     return this.thustarttime2;
/*     */   }
/*     */   
/*     */   public void setThustarttime2(String paramString) {
/* 535 */     this.thustarttime2 = paramString;
/*     */   }
/*     */   
/*     */   public int getWeek() {
/* 539 */     return this.week;
/*     */   }
/*     */   
/*     */   public void setWeek(int paramInt) {
/* 543 */     this.week = (paramInt == 7) ? 0 : paramInt;
/*     */     
/* 545 */     switch (this.week) {
/*     */       case 0:
/* 547 */         this.startWorkTime = this.sunstarttime1;
/* 548 */         this.onDutyTimeAM = this.sunstarttime1;
/* 549 */         this.offDutyTimeAM = this.sunendtime1;
/* 550 */         this.onDutyTimePM = this.sunstarttime2;
/* 551 */         this.offDutyTimePM = this.sunendtime2;
/*     */         return;
/*     */       case 1:
/* 554 */         this.startWorkTime = this.monstarttime1;
/* 555 */         this.onDutyTimeAM = this.monstarttime1;
/* 556 */         this.offDutyTimeAM = this.monendtime1;
/* 557 */         this.onDutyTimePM = this.monstarttime2;
/* 558 */         this.offDutyTimePM = this.monendtime2;
/*     */         return;
/*     */       case 2:
/* 561 */         this.startWorkTime = this.tuestarttime1;
/* 562 */         this.onDutyTimeAM = this.tuestarttime1;
/* 563 */         this.offDutyTimeAM = this.tueendtime1;
/* 564 */         this.onDutyTimePM = this.tuestarttime2;
/* 565 */         this.offDutyTimePM = this.tueendtime2;
/*     */         return;
/*     */       case 3:
/* 568 */         this.startWorkTime = this.wedstarttime1;
/* 569 */         this.onDutyTimeAM = this.wedstarttime1;
/* 570 */         this.offDutyTimeAM = this.wedendtime1;
/* 571 */         this.onDutyTimePM = this.wedstarttime2;
/* 572 */         this.offDutyTimePM = this.wedendtime2;
/*     */         return;
/*     */       case 4:
/* 575 */         this.startWorkTime = this.thustarttime1;
/* 576 */         this.onDutyTimeAM = this.thustarttime1;
/* 577 */         this.offDutyTimeAM = this.thuendtime1;
/* 578 */         this.onDutyTimePM = this.thustarttime2;
/* 579 */         this.offDutyTimePM = this.thuendtime2;
/*     */         return;
/*     */       case 5:
/* 582 */         this.startWorkTime = this.fristarttime1;
/* 583 */         this.onDutyTimeAM = this.fristarttime1;
/* 584 */         this.offDutyTimeAM = this.friendtime1;
/* 585 */         this.onDutyTimePM = this.fristarttime2;
/* 586 */         this.offDutyTimePM = this.friendtime2;
/*     */         return;
/*     */     } 
/* 589 */     this.startWorkTime = this.satstarttime1;
/* 590 */     this.onDutyTimeAM = this.satstarttime1;
/* 591 */     this.offDutyTimeAM = this.satendtime1;
/* 592 */     this.onDutyTimePM = this.satstarttime2;
/* 593 */     this.offDutyTimePM = this.satendtime2;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTotaltime() {
/* 599 */     return this.totaltime;
/*     */   }
/*     */   
/*     */   public void setTotaltime(String paramString) {
/* 603 */     this.totaltime = paramString;
/*     */   }
/*     */   
/*     */   public String getTueendtime1() {
/* 607 */     return this.tueendtime1;
/*     */   }
/*     */   
/*     */   public void setTueendtime1(String paramString) {
/* 611 */     this.tueendtime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getTueendtime2() {
/* 615 */     return this.tueendtime2;
/*     */   }
/*     */   
/*     */   public void setTueendtime2(String paramString) {
/* 619 */     this.tueendtime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getTuestarttime1() {
/* 623 */     return this.tuestarttime1;
/*     */   }
/*     */   
/*     */   public void setTuestarttime1(String paramString) {
/* 627 */     this.tuestarttime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getTuestarttime2() {
/* 631 */     return this.tuestarttime2;
/*     */   }
/*     */   
/*     */   public void setTuestarttime2(String paramString) {
/* 635 */     this.tuestarttime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getValidedatefrom() {
/* 639 */     return this.validedatefrom;
/*     */   }
/*     */   
/*     */   public void setValidedatefrom(String paramString) {
/* 643 */     this.validedatefrom = paramString;
/*     */   }
/*     */   
/*     */   public String getAddr() {
/* 647 */     return this.addr;
/*     */   }
/*     */   
/*     */   public void setAddr(String paramString) {
/* 651 */     this.addr = paramString;
/*     */   }
/*     */   
/*     */   public int getDepartmentId() {
/* 655 */     return this.departmentId;
/*     */   }
/*     */   
/*     */   public void setDepartmentId(int paramInt) {
/* 659 */     this.departmentId = paramInt;
/*     */   }
/*     */   
/*     */   public String getLatitude() {
/* 663 */     return this.latitude;
/*     */   }
/*     */   
/*     */   public void setLatitude(String paramString) {
/* 667 */     this.latitude = paramString;
/*     */   }
/*     */   
/*     */   public String getLongitude() {
/* 671 */     return this.longitude;
/*     */   }
/*     */   
/*     */   public void setLongitude(String paramString) {
/* 675 */     this.longitude = paramString;
/*     */   }
/*     */   
/*     */   public int getResourceId() {
/* 679 */     return this.resourceId;
/*     */   }
/*     */   
/*     */   public void setResourceId(int paramInt) {
/* 683 */     this.resourceId = paramInt;
/*     */   }
/*     */   
/*     */   public String getResourceName() {
/* 687 */     return this.resourceName;
/*     */   }
/*     */   
/*     */   public void setResourceName(String paramString) {
/* 691 */     this.resourceName = paramString;
/*     */   }
/*     */   
/*     */   public String getSignFrom() {
/* 695 */     return this.signFrom;
/*     */   }
/*     */   
/*     */   public void setSignFrom(String paramString) {
/* 699 */     this.signFrom = paramString;
/*     */   }
/*     */   
/*     */   public String getStatus() {
/* 703 */     return this.status;
/*     */   }
/*     */   
/*     */   public void setStatus(String paramString) {
/* 707 */     this.status = paramString;
/*     */   }
/*     */   
/*     */   public int getSubCompanyId() {
/* 711 */     return this.subCompanyId;
/*     */   }
/*     */   
/*     */   public void setSubCompanyId(int paramInt) {
/* 715 */     this.subCompanyId = paramInt;
/*     */   }
/*     */   
/*     */   public String getValidedateto() {
/* 719 */     return this.validedateto;
/*     */   }
/*     */   
/*     */   public void setValidedateto(String paramString) {
/* 723 */     this.validedateto = paramString;
/*     */   }
/*     */   
/*     */   public String getWedendtime1() {
/* 727 */     return this.wedendtime1;
/*     */   }
/*     */   
/*     */   public void setWedendtime1(String paramString) {
/* 731 */     this.wedendtime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getWedendtime2() {
/* 735 */     return this.wedendtime2;
/*     */   }
/*     */   
/*     */   public void setWedendtime2(String paramString) {
/* 739 */     this.wedendtime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getWedstarttime1() {
/* 743 */     return this.wedstarttime1;
/*     */   }
/*     */   
/*     */   public void setWedstarttime1(String paramString) {
/* 747 */     this.wedstarttime1 = paramString;
/*     */   }
/*     */   
/*     */   public String getWedstarttime2() {
/* 751 */     return this.wedstarttime2;
/*     */   }
/*     */   
/*     */   public void setWedstarttime2(String paramString) {
/* 755 */     this.wedstarttime2 = paramString;
/*     */   }
/*     */   
/*     */   public String getStartWorkTime() {
/* 759 */     return this.startWorkTime;
/*     */   }
/*     */   
/*     */   public void setStartWorkTime(String paramString) {
/* 763 */     this.startWorkTime = paramString;
/*     */   }
/*     */   
/*     */   public String getOffDutyTimeAM() {
/* 767 */     return this.offDutyTimeAM;
/*     */   }
/*     */   
/*     */   public void setOffDutyTimeAM(String paramString) {
/* 771 */     this.offDutyTimeAM = paramString;
/*     */   }
/*     */   
/*     */   public String getOffDutyTimePM() {
/* 775 */     return this.offDutyTimePM;
/*     */   }
/*     */   
/*     */   public void setOffDutyTimePM(String paramString) {
/* 779 */     this.offDutyTimePM = paramString;
/*     */   }
/*     */   
/*     */   public String getOnDutyTimeAM() {
/* 783 */     return this.onDutyTimeAM;
/*     */   }
/*     */   
/*     */   public void setOnDutyTimeAM(String paramString) {
/* 787 */     this.onDutyTimeAM = paramString;
/*     */   }
/*     */   
/*     */   public String getOnDutyTimePM() {
/* 791 */     return this.onDutyTimePM;
/*     */   }
/*     */   
/*     */   public void setOnDutyTimePM(String paramString) {
/* 795 */     this.onDutyTimePM = paramString;
/*     */   }
/*     */   
/*     */   public Map<String, String> getOnDutyAndOffDutyTimeMap() {
/* 799 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 800 */     hashMap.put("onDutyTimeAM", this.onDutyTimeAM);
/* 801 */     hashMap.put("offDutyTimeAM", this.offDutyTimeAM);
/* 802 */     hashMap.put("onDutyTimePM", this.onDutyTimePM);
/* 803 */     hashMap.put("offDutyTimePM", this.offDutyTimePM);
/* 804 */     hashMap.put("signType", this.signType);
/* 805 */     hashMap.put("signStartTime", this.signStartTime);
/* 806 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public boolean isEmpty() {
/* 810 */     return StringUtil.isNull(new String[] { this.onDutyTimeAM, this.offDutyTimeAM, this.onDutyTimePM, this.offDutyTimePM });
/*     */   }
/*     */   
/*     */   public String getWorkTime() {
/* 814 */     return this.onDutyTimeAM + "-" + this.offDutyTimeAM + "," + this.onDutyTimePM + "," + this.offDutyTimePM;
/*     */   }
/*     */   
/*     */   public int compareTo(Object paramObject) {
/* 818 */     HrmSchedule hrmSchedule = (HrmSchedule)paramObject;
/* 819 */     if ((this.oldSignDate + " " + this.signTime).compareTo(hrmSchedule.getOldSignDate() + " " + hrmSchedule.getSignTime()) < 0) {
/* 820 */       return -1;
/*     */     }
/* 822 */     return 1;
/*     */   }
/*     */   
/*     */   public String get$ScheduleName() {
/* 826 */     return this.$scheduleName;
/*     */   }
/*     */   
/*     */   public void set$ScheduleName(String paramString) {
/* 830 */     this.$scheduleName = paramString;
/*     */   }
/*     */   
/*     */   public String get$SignStartTime() {
/* 834 */     return this.$signStartTime;
/*     */   }
/*     */   
/*     */   public void set$SignStartTime(String paramString) {
/* 838 */     this.$signStartTime = paramString;
/*     */   }
/*     */   
/*     */   public String get$ScheduleTitle() {
/* 842 */     return this.$scheduleTitle;
/*     */   }
/*     */   
/*     */   public void set$ScheduleTitle(String paramString) {
/* 846 */     this.$scheduleTitle = paramString;
/*     */   }
/*     */   
/*     */   public String get$SignType() {
/* 850 */     return this.$signType;
/*     */   }
/*     */   
/*     */   public void set$SignType(String paramString) {
/* 854 */     this.$signType = paramString;
/*     */   }
/*     */   
/*     */   public List<String[]> getSignTimes() {
/* 858 */     return (this.signTimes == null) ? (List)new ArrayList<>() : this.signTimes;
/*     */   }
/*     */   
/*     */   public void setSignTimes(List<String[]> paramList) {
/* 862 */     this.signTimes = paramList;
/*     */   }
/*     */   
/*     */   public List<String[]> getRestTimes() {
/* 866 */     return (this.restTimes == null) ? (List)new ArrayList<>() : this.restTimes;
/*     */   }
/*     */   
/*     */   public void setRestTimes(List<String[]> paramList) {
/* 870 */     this.restTimes = paramList;
/*     */   }
/*     */   
/*     */   public List<String[]> getWorkTimes() {
/* 874 */     return (this.workTimes == null) ? (List)new ArrayList<>() : this.workTimes;
/*     */   }
/*     */   
/*     */   public void setWorkTimes(List<String[]> paramList) {
/* 878 */     this.workTimes = paramList;
/*     */   }
/*     */   
/*     */   public String getDsSignType() {
/* 882 */     return this.dsSignType;
/*     */   }
/*     */   
/*     */   public void setDsSignType(String paramString) {
/* 886 */     this.dsSignType = paramString;
/*     */   }
/*     */   
/*     */   public boolean isSchedulePerson() {
/* 890 */     return this.isSchedulePerson;
/*     */   }
/*     */   
/*     */   public void setSchedulePerson(boolean paramBoolean) {
/* 894 */     this.isSchedulePerson = paramBoolean;
/*     */   }
/*     */   
/*     */   public String getWorkcode() {
/* 898 */     return this.workcode;
/*     */   }
/*     */   
/*     */   public void setWorkcode(String paramString) {
/* 902 */     this.workcode = paramString;
/*     */   }
/*     */   
/*     */   public List<String> getFDates() {
/* 906 */     return this.fDates;
/*     */   }
/*     */   
/*     */   public List<String> getSDates() {
/* 910 */     return this.sDates;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 915 */     return this.resourceId + ">>" + this.isSchedulePerson;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmSchedule.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */