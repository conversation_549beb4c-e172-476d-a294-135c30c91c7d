/*    */ package weaver.hrm.schedule.domain;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftsWt
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private Long id;
/*    */   private Long field001;
/*    */   private Long field002;
/*    */   private Long field003;
/*    */   
/*    */   public HrmScheduleShiftsWt() {
/* 24 */     this(true);
/*    */   }
/*    */   
/*    */   public HrmScheduleShiftsWt(Long paramLong) {
/* 28 */     this(true);
/* 29 */     this.id = paramLong;
/*    */   }
/*    */   
/*    */   public HrmScheduleShiftsWt(boolean paramBoolean) {
/* 33 */     if (paramBoolean) init(); 
/*    */   }
/*    */   
/*    */   public void init() {
/* 37 */     this.id = Long.valueOf(0L);
/* 38 */     this.field001 = Long.valueOf(0L);
/* 39 */     this.field002 = Long.valueOf(0L);
/* 40 */     this.field003 = Long.valueOf(0L);
/*    */   }
/*    */   
/*    */   public void setId(Long paramLong) {
/* 44 */     this.id = paramLong;
/*    */   }
/*    */   
/*    */   public Long getId() {
/* 48 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setField001(Long paramLong) {
/* 52 */     this.field001 = paramLong;
/*    */   }
/*    */   
/*    */   public Long getField001() {
/* 56 */     return this.field001;
/*    */   }
/*    */   
/*    */   public void setField002(Long paramLong) {
/* 60 */     this.field002 = paramLong;
/*    */   }
/*    */   
/*    */   public Long getField002() {
/* 64 */     return this.field002;
/*    */   }
/*    */   
/*    */   public void setField003(Long paramLong) {
/* 68 */     this.field003 = paramLong;
/*    */   }
/*    */   
/*    */   public Long getField003() {
/* 72 */     return this.field003;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleShiftsWt.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */