/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import weaver.framework.BaseEntity;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSet
/*     */   extends BaseEntity
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Long id;
/*     */   private Integer delflag;
/*     */   private Long creater;
/*     */   private String createTime;
/*     */   private Long lastModifier;
/*     */   private String lastModificationTime;
/*     */   private Integer sn;
/*     */   private String field001;
/*     */   private String field002;
/*     */   private Long field003;
/*     */   private Integer field004;
/*     */   private Long t2Field001;
/*     */   
/*     */   public HrmScheduleSet() {
/*  43 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmScheduleSet(Long paramLong) {
/*  47 */     this(true);
/*  48 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public HrmScheduleSet(boolean paramBoolean) {
/*  52 */     if (paramBoolean) init(); 
/*     */   }
/*     */   
/*     */   public void init() {
/*  56 */     this.id = Long.valueOf(0L);
/*  57 */     this.delflag = Integer.valueOf(0);
/*  58 */     this.creater = Long.valueOf(0L);
/*  59 */     this.createTime = "";
/*  60 */     this.lastModifier = Long.valueOf(0L);
/*  61 */     this.lastModificationTime = "";
/*  62 */     this.sn = Integer.valueOf(0);
/*  63 */     this.field001 = "";
/*  64 */     this.field002 = "";
/*  65 */     this.field003 = Long.valueOf(0L);
/*  66 */     this.field004 = Integer.valueOf(0);
/*  67 */     this.t2Field001 = Long.valueOf(0L);
/*     */   }
/*     */   
/*     */   public void setId(Long paramLong) {
/*  71 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public Long getId() {
/*  75 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setDelflag(Integer paramInteger) {
/*  79 */     this.delflag = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getDelflag() {
/*  83 */     return this.delflag;
/*     */   }
/*     */   
/*     */   public void setCreater(Long paramLong) {
/*  87 */     this.creater = paramLong;
/*     */   }
/*     */   
/*     */   public Long getCreater() {
/*  91 */     return this.creater;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/*  95 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/*  99 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setLastModifier(Long paramLong) {
/* 103 */     this.lastModifier = paramLong;
/*     */   }
/*     */   
/*     */   public Long getLastModifier() {
/* 107 */     return this.lastModifier;
/*     */   }
/*     */   
/*     */   public void setLastModificationTime(String paramString) {
/* 111 */     this.lastModificationTime = paramString;
/*     */   }
/*     */   
/*     */   public String getLastModificationTime() {
/* 115 */     return this.lastModificationTime;
/*     */   }
/*     */   
/*     */   public void setSn(Integer paramInteger) {
/* 119 */     this.sn = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getSn() {
/* 123 */     return this.sn;
/*     */   }
/*     */   
/*     */   public void setField001(String paramString) {
/* 127 */     this.field001 = paramString;
/*     */   }
/*     */   
/*     */   public String getField001() {
/* 131 */     return this.field001;
/*     */   }
/*     */   
/*     */   public void setField002(String paramString) {
/* 135 */     this.field002 = paramString;
/*     */   }
/*     */   
/*     */   public String getField002() {
/* 139 */     return this.field002;
/*     */   }
/*     */   
/*     */   public void setField003(Long paramLong) {
/* 143 */     this.field003 = paramLong;
/*     */   }
/*     */   
/*     */   public Long getField003() {
/* 147 */     return this.field003;
/*     */   }
/*     */   
/*     */   public void setField004(Integer paramInteger) {
/* 151 */     this.field004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField004() {
/* 155 */     return this.field004;
/*     */   }
/*     */   
/*     */   public Long getT2Field001() {
/* 159 */     return this.t2Field001;
/*     */   }
/*     */   
/*     */   public void setT2Field001(Long paramLong) {
/* 163 */     this.t2Field001 = paramLong;
/*     */   }
/*     */   
/*     */   public String toString() {
/* 167 */     return this.field001 + ";" + this.field002 + ";" + this.field003 + ";" + this.field004;
/*     */   }
/*     */   
/*     */   public String getSql() {
/* 171 */     return "select " + this.id + 
/* 172 */       "," + this.delflag + 
/* 173 */       "," + this.creater + 
/* 174 */       "," + "'" + 
/* 175 */       this.createTime + "'," + this.lastModifier + 
/* 176 */       "," + "'" + 
/* 177 */       this.lastModificationTime + "'," + this.sn + 
/* 178 */       "," + "'" + 
/* 179 */       this.field001 + "'," + "'" + 
/* 180 */       this.field002 + "'," + this.field003 + 
/* 181 */       "," + this.field004;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object[] toInsertObj() {
/* 186 */     return new Object[] { this.id, this.delflag, this.creater, this.createTime, this.lastModifier, this.lastModificationTime, this.sn, this.field001, this.field002, this.field003, this.field004 };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object[] toUpdateObj() {
/* 201 */     return new Object[] { this.delflag, this.creater, this.createTime, this.lastModifier, this.lastModificationTime, this.sn, this.field001, this.field002, this.field003, this.field004, this.id };
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleSet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */