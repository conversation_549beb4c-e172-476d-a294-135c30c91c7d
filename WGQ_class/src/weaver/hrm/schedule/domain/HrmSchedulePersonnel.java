/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import weaver.common.StringUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSchedulePersonnel
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Long id;
/*     */   private Integer delflag;
/*     */   private Long creater;
/*     */   private String createTime;
/*     */   private Long lastModifier;
/*     */   private String lastModificationTime;
/*     */   private Integer sn;
/*     */   private Integer field001;
/*     */   private String field002;
/*     */   private Integer field003;
/*     */   private Integer field004;
/*     */   private Integer field005;
/*     */   private Integer field006;
/*     */   private String field007;
/*     */   
/*     */   public HrmSchedulePersonnel() {
/*  46 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmSchedulePersonnel(Long paramLong) {
/*  50 */     this(true);
/*  51 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public HrmSchedulePersonnel(boolean paramBoolean) {
/*  55 */     if (paramBoolean) init(); 
/*     */   }
/*     */   
/*     */   public void init() {
/*  59 */     this.id = Long.valueOf(0L);
/*  60 */     this.delflag = Integer.valueOf(0);
/*  61 */     this.creater = Long.valueOf(0L);
/*  62 */     this.createTime = "";
/*  63 */     this.lastModifier = Long.valueOf(0L);
/*  64 */     this.lastModificationTime = "";
/*  65 */     this.sn = Integer.valueOf(0);
/*  66 */     this.field001 = Integer.valueOf(1);
/*  67 */     this.field002 = "";
/*  68 */     this.field003 = Integer.valueOf(0);
/*  69 */     this.field004 = Integer.valueOf(100);
/*  70 */     this.field005 = Integer.valueOf(0);
/*  71 */     this.field006 = Integer.valueOf(0);
/*  72 */     this.field007 = "";
/*     */   }
/*     */   
/*     */   public void setId(Long paramLong) {
/*  76 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public Long getId() {
/*  80 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setDelflag(Integer paramInteger) {
/*  84 */     this.delflag = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getDelflag() {
/*  88 */     return this.delflag;
/*     */   }
/*     */   
/*     */   public void setCreater(Long paramLong) {
/*  92 */     this.creater = paramLong;
/*     */   }
/*     */   
/*     */   public Long getCreater() {
/*  96 */     return this.creater;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 100 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/* 104 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setLastModifier(Long paramLong) {
/* 108 */     this.lastModifier = paramLong;
/*     */   }
/*     */   
/*     */   public Long getLastModifier() {
/* 112 */     return this.lastModifier;
/*     */   }
/*     */   
/*     */   public void setLastModificationTime(String paramString) {
/* 116 */     this.lastModificationTime = paramString;
/*     */   }
/*     */   
/*     */   public String getLastModificationTime() {
/* 120 */     return this.lastModificationTime;
/*     */   }
/*     */   
/*     */   public void setSn(Integer paramInteger) {
/* 124 */     this.sn = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getSn() {
/* 128 */     return this.sn;
/*     */   }
/*     */   
/*     */   public void setField001(Integer paramInteger) {
/* 132 */     this.field001 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField001() {
/* 136 */     return this.field001;
/*     */   }
/*     */   
/*     */   public void setField002(String paramString) {
/* 140 */     this.field002 = paramString;
/*     */   }
/*     */   
/*     */   public String getRealField002() {
/* 144 */     return this.field002;
/*     */   }
/*     */   
/*     */   public String getField002() {
/* 148 */     return StringUtil.replace(this.field002, ";", ",");
/*     */   }
/*     */   
/*     */   public void setField003(Integer paramInteger) {
/* 152 */     this.field003 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField003() {
/* 156 */     return this.field003;
/*     */   }
/*     */   
/*     */   public void setField004(Integer paramInteger) {
/* 160 */     this.field004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField004() {
/* 164 */     return this.field004;
/*     */   }
/*     */   
/*     */   public void setField005(Integer paramInteger) {
/* 168 */     this.field005 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField005() {
/* 172 */     return this.field005;
/*     */   }
/*     */   
/*     */   public void setField006(Integer paramInteger) {
/* 176 */     this.field006 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField006() {
/* 180 */     return this.field006;
/*     */   }
/*     */   
/*     */   public void setField007(String paramString) {
/* 184 */     this.field007 = paramString;
/*     */   }
/*     */   
/*     */   public String getField007() {
/* 188 */     return this.field007;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmSchedulePersonnel.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */