/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import weaver.common.StringUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmLeaveDay
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String fromDate;
/*     */   private String fromTime;
/*     */   private String toDate;
/*     */   private String toTime;
/*     */   private String resourceId;
/*     */   private boolean worktime = true;
/*     */   private boolean gettime = false;
/*  32 */   private int scale = 2;
/*     */   
/*     */   private String newLeaveType;
/*     */   
/*     */   public String getFromDate() {
/*  37 */     return this.fromDate;
/*     */   }
/*     */   
/*     */   public void setFromDate(String paramString) {
/*  41 */     this.fromDate = paramString;
/*     */   }
/*     */   
/*     */   public String getFromTime() {
/*  45 */     return this.fromTime;
/*     */   }
/*     */   
/*     */   public void setFromTime(String paramString) {
/*  49 */     this.fromTime = paramString;
/*     */   }
/*     */   
/*     */   public String getResourceId() {
/*  53 */     return this.resourceId;
/*     */   }
/*     */   
/*     */   public void setResourceId(String paramString) {
/*  57 */     this.resourceId = paramString;
/*     */   }
/*     */   
/*     */   public String getToDate() {
/*  61 */     return this.toDate;
/*     */   }
/*     */   
/*     */   public void setToDate(String paramString) {
/*  65 */     this.toDate = paramString;
/*     */   }
/*     */   
/*     */   public String getToTime() {
/*  69 */     return this.toTime;
/*     */   }
/*     */   
/*     */   public void setToTime(String paramString) {
/*  73 */     this.toTime = paramString;
/*     */   }
/*     */   
/*     */   public boolean isWorktime() {
/*  77 */     return this.worktime;
/*     */   }
/*     */   
/*     */   public void setWorktime(boolean paramBoolean) {
/*  81 */     this.worktime = paramBoolean;
/*     */   }
/*     */   
/*     */   public boolean isGettime() {
/*  85 */     return this.gettime;
/*     */   }
/*     */   
/*     */   public void setGettime(boolean paramBoolean) {
/*  89 */     this.gettime = paramBoolean;
/*     */   }
/*     */   
/*     */   public int getScale() {
/*  93 */     return this.scale;
/*     */   }
/*     */   
/*     */   public void setScale(int paramInt) {
/*  97 */     this.scale = paramInt;
/*     */   }
/*     */   
/*     */   public boolean isEmpty() {
/* 101 */     return (StringUtil.isNull(this.resourceId) || 
/* 102 */       StringUtil.parseToInt(this.resourceId) == -1 || 
/* 103 */       StringUtil.isNull(this.fromDate) || 
/* 104 */       StringUtil.isNull(this.toDate));
/*     */   }
/*     */   
/*     */   public String getNewLeaveType() {
/* 108 */     return this.newLeaveType;
/*     */   }
/*     */   
/*     */   public void setNewLeaveType(String paramString) {
/* 112 */     this.newLeaveType = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmLeaveDay.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */