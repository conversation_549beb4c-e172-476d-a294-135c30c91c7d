/*    */ package weaver.hrm.schedule.domain;
/*    */ 
/*    */ import weaver.hrm.passwordprotection.domain.HrmResource;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSchedulePerson
/*    */   extends HrmResource
/*    */ {
/*    */   private String subcompanyName;
/*    */   private String departmentName;
/*    */   private String jobtitleName;
/*    */   private Integer stype;
/*    */   
/*    */   public String getDepartmentName() {
/* 24 */     return this.departmentName;
/*    */   }
/*    */   
/*    */   public void setDepartmentName(String paramString) {
/* 28 */     this.departmentName = paramString;
/*    */   }
/*    */   
/*    */   public String getJobtitleName() {
/* 32 */     return this.jobtitleName;
/*    */   }
/*    */   
/*    */   public void setJobtitleName(String paramString) {
/* 36 */     this.jobtitleName = paramString;
/*    */   }
/*    */   
/*    */   public Integer getStype() {
/* 40 */     return this.stype;
/*    */   }
/*    */   
/*    */   public void setStype(Integer paramInteger) {
/* 44 */     this.stype = paramInteger;
/*    */   }
/*    */   
/*    */   public String getSubcompanyName() {
/* 48 */     return this.subcompanyName;
/*    */   }
/*    */   
/*    */   public void setSubcompanyName(String paramString) {
/* 52 */     this.subcompanyName = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmSchedulePerson.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */