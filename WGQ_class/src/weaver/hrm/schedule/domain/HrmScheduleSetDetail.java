/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSetDetail
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Long id;
/*     */   private Integer delflag;
/*     */   private Long setId;
/*     */   private String field001;
/*     */   private Long field002;
/*     */   private String field003;
/*     */   private Integer field004;
/*     */   private String field005;
/*     */   private String workTime;
/*     */   private String signTime;
/*     */   private String field001Name;
/*     */   private String field001Title;
/*     */   private String restTime;
/*     */   
/*     */   public HrmScheduleSetDetail() {
/*  42 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmScheduleSetDetail(Long paramLong) {
/*  46 */     this(true);
/*  47 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public HrmScheduleSetDetail(boolean paramBoolean) {
/*  51 */     if (paramBoolean) init(); 
/*     */   }
/*     */   
/*     */   public void init() {
/*  55 */     this.id = Long.valueOf(0L);
/*  56 */     this.delflag = Integer.valueOf(0);
/*  57 */     this.setId = Long.valueOf(0L);
/*  58 */     this.field001 = "";
/*  59 */     this.field002 = Long.valueOf(0L);
/*  60 */     this.field003 = "";
/*  61 */     this.field004 = Integer.valueOf(0);
/*  62 */     this.field005 = "";
/*  63 */     this.workTime = "";
/*  64 */     this.signTime = "";
/*  65 */     this.field001Name = "";
/*  66 */     this.restTime = "";
/*     */   }
/*     */   
/*     */   public void setId(Long paramLong) {
/*  70 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public Long getId() {
/*  74 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setDelflag(Integer paramInteger) {
/*  78 */     this.delflag = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getDelflag() {
/*  82 */     return this.delflag;
/*     */   }
/*     */   
/*     */   public Long getSetId() {
/*  86 */     return this.setId;
/*     */   }
/*     */   
/*     */   public void setSetId(Long paramLong) {
/*  90 */     this.setId = paramLong;
/*     */   }
/*     */   
/*     */   public String getRestTime() {
/*  94 */     return this.restTime;
/*     */   }
/*     */   
/*     */   public void setRestTime(String paramString) {
/*  98 */     this.restTime = paramString;
/*     */   }
/*     */   
/*     */   public void setField001(String paramString) {
/* 102 */     this.field001 = paramString;
/*     */   }
/*     */   
/*     */   public String getField001() {
/* 106 */     return this.field001;
/*     */   }
/*     */   
/*     */   public void setField002(Long paramLong) {
/* 110 */     this.field002 = paramLong;
/*     */   }
/*     */   
/*     */   public Long getField002() {
/* 114 */     return this.field002;
/*     */   }
/*     */   
/*     */   public void setField003(String paramString) {
/* 118 */     this.field003 = paramString;
/*     */   }
/*     */   
/*     */   public String getField003() {
/* 122 */     return this.field003;
/*     */   }
/*     */   
/*     */   public void setField004(Integer paramInteger) {
/* 126 */     this.field004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField004() {
/* 130 */     return this.field004;
/*     */   }
/*     */   
/*     */   public void setField005(String paramString) {
/* 134 */     this.field005 = paramString;
/*     */   }
/*     */   
/*     */   public String getField005() {
/* 138 */     return this.field005;
/*     */   }
/*     */   
/*     */   public String getWorkTime() {
/* 142 */     return this.workTime;
/*     */   }
/*     */   
/*     */   public void setWorkTime(String paramString) {
/* 146 */     this.workTime = paramString;
/*     */   }
/*     */   
/*     */   public String getSignTime() {
/* 150 */     return this.signTime;
/*     */   }
/*     */   
/*     */   public void setSignTime(String paramString) {
/* 154 */     this.signTime = paramString;
/*     */   }
/*     */   
/*     */   public String getField001Name() {
/* 158 */     return this.field001Name;
/*     */   }
/*     */   
/*     */   public void setField001Name(String paramString) {
/* 162 */     this.field001Name = paramString;
/*     */   }
/*     */   
/*     */   public String getField001Title() {
/* 166 */     return this.field001Title;
/*     */   }
/*     */   
/*     */   public void setField001Title(String paramString) {
/* 170 */     this.field001Title = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleSetDetail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */