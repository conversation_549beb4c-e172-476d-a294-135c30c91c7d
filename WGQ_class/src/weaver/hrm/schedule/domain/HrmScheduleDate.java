/*    */ package weaver.hrm.schedule.domain;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleDate
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private int resourceId;
/*    */   private String date;
/*    */   private boolean isWorkDay;
/*    */   private boolean isSchedulePerson;
/*    */   private double workHours;
/*    */   private HrmSchedule sBean;
/*    */   private HrmScheduleSetDetail dBean;
/*    */   
/*    */   public String getDate() {
/* 31 */     return this.date;
/*    */   }
/*    */   
/*    */   public void setDate(String paramString) {
/* 35 */     this.date = paramString;
/*    */   }
/*    */   
/*    */   public boolean isWorkDay() {
/* 39 */     return this.isWorkDay;
/*    */   }
/*    */   
/*    */   public void setWorkDay(boolean paramBoolean) {
/* 43 */     this.isWorkDay = paramBoolean;
/*    */   }
/*    */   
/*    */   public int getResourceId() {
/* 47 */     return this.resourceId;
/*    */   }
/*    */   
/*    */   public void setResourceId(int paramInt) {
/* 51 */     this.resourceId = paramInt;
/*    */   }
/*    */   
/*    */   public double getWorkHours() {
/* 55 */     return this.workHours;
/*    */   }
/*    */   
/*    */   public void setWorkHours(double paramDouble) {
/* 59 */     this.workHours = paramDouble;
/*    */   }
/*    */   
/*    */   public boolean isSchedulePerson() {
/* 63 */     return this.isSchedulePerson;
/*    */   }
/*    */   
/*    */   public void setSchedulePerson(boolean paramBoolean) {
/* 67 */     this.isSchedulePerson = paramBoolean;
/*    */   }
/*    */   
/*    */   public HrmScheduleSetDetail getDBean() {
/* 71 */     return this.dBean;
/*    */   }
/*    */   
/*    */   public void setDBean(HrmScheduleSetDetail paramHrmScheduleSetDetail) {
/* 75 */     this.dBean = paramHrmScheduleSetDetail;
/*    */   }
/*    */   
/*    */   public HrmSchedule getSBean() {
/* 79 */     return this.sBean;
/*    */   }
/*    */   
/*    */   public void setSBean(HrmSchedule paramHrmSchedule) {
/* 83 */     this.sBean = paramHrmSchedule;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleDate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */