/*    */ package weaver.hrm.schedule.domain;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSchduleResttime
/*    */   extends BaseBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String restTimeId;
/*    */   private String restStartTime1;
/*    */   private String restEndTime1;
/*    */   private String restStartTime2;
/*    */   private String restEndTime2;
/*    */   private String delflag;
/*    */   private String worktimeId;
/*    */   private String restStartTime;
/*    */   private String restEndTime;
/*    */   
/*    */   public String getRestTimeId() {
/* 30 */     return this.restTimeId;
/*    */   }
/*    */   public String getDelflag() {
/* 33 */     return this.delflag;
/*    */   }
/*    */   public void setDelflag(String paramString) {
/* 36 */     this.delflag = paramString;
/*    */   }
/*    */   public void setRestTimeId(String paramString) {
/* 39 */     this.restTimeId = paramString;
/*    */   }
/*    */   public String getRestStartTime1() {
/* 42 */     return this.restStartTime1;
/*    */   }
/*    */   public void setRestStartTime1(String paramString) {
/* 45 */     this.restStartTime1 = paramString;
/*    */   }
/*    */   public String getRestEndTime1() {
/* 48 */     return this.restEndTime1;
/*    */   }
/*    */   public void setRestEndTime1(String paramString) {
/* 51 */     this.restEndTime1 = paramString.equals("00:00") ? "24:00" : paramString;
/*    */   }
/*    */   public String getRestStartTime2() {
/* 54 */     return this.restStartTime2;
/*    */   }
/*    */   public void setRestStartTime2(String paramString) {
/* 57 */     this.restStartTime2 = paramString;
/*    */   }
/*    */   public String getRestEndTime2() {
/* 60 */     return this.restEndTime2;
/*    */   }
/*    */   public void setRestEndTime2(String paramString) {
/* 63 */     this.restEndTime2 = paramString.equals("00:00") ? "24:00" : paramString;
/*    */   }
/*    */   public String getWorktimeId() {
/* 66 */     return this.worktimeId;
/*    */   }
/*    */   public void setWorktimeId(String paramString) {
/* 69 */     this.worktimeId = paramString;
/*    */   }
/*    */   public String getRestTime() {
/* 72 */     StringBuffer stringBuffer = new StringBuffer();
/* 73 */     if (StringUtil.isNotNull(new String[] { this.restStartTime1, this.restEndTime1 })) {
/* 74 */       stringBuffer.append(this.restStartTime1 + "-" + this.restEndTime1);
/*    */     }
/* 76 */     if (StringUtil.isNotNull(new String[] { this.restStartTime2, this.restEndTime2 })) {
/* 77 */       stringBuffer.append((stringBuffer.length() == 0) ? "" : ",").append(this.restStartTime2 + "-" + this.restEndTime2);
/*    */     }
/* 79 */     return stringBuffer.toString();
/*    */   }
/*    */   public String getRestTime1() {
/* 82 */     return this.restStartTime1 + "-" + this.restEndTime1;
/*    */   }
/*    */   public String getRestTime2() {
/* 85 */     return this.restStartTime2 + "-" + this.restEndTime2;
/*    */   }
/*    */   public String getRestStartTime() {
/* 88 */     return this.restStartTime;
/*    */   }
/*    */   public void setRestStartTime(String paramString) {
/* 91 */     this.restStartTime = paramString;
/*    */   }
/*    */   public String getRestEndTime() {
/* 94 */     return this.restEndTime;
/*    */   }
/*    */   public void setRestEndTime(String paramString) {
/* 97 */     this.restEndTime = paramString.equals("00:00") ? "24:00" : paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmSchduleResttime.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */