/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.List;
/*     */ import weaver.common.DateUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleWorktime
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Long id;
/*     */   private Integer delflag;
/*     */   private Long creater;
/*     */   private String createTime;
/*     */   private Long lastModifier;
/*     */   private String lastModificationTime;
/*     */   private String field001;
/*     */   private String field002;
/*     */   private String field003;
/*     */   private Integer field004;
/*     */   private Integer field005;
/*     */   private String field006;
/*     */   private Double field007;
/*     */   private String restTimes;
/*     */   private List<HrmSchduleResttime> resttimeList;
/*     */   
/*     */   public HrmScheduleWorktime() {
/*  52 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmScheduleWorktime(Long paramLong) {
/*  56 */     this(true);
/*  57 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public HrmScheduleWorktime(boolean paramBoolean) {
/*  61 */     if (paramBoolean) init(); 
/*     */   }
/*     */   
/*     */   public void init() {
/*  65 */     this.id = Long.valueOf(0L);
/*  66 */     this.delflag = Integer.valueOf(0);
/*  67 */     this.creater = Long.valueOf(0L);
/*  68 */     this.createTime = "";
/*  69 */     this.lastModifier = Long.valueOf(0L);
/*  70 */     this.lastModificationTime = "";
/*  71 */     this.field001 = "";
/*  72 */     this.field002 = "";
/*  73 */     this.field003 = "";
/*  74 */     this.field004 = Integer.valueOf(30);
/*  75 */     this.field005 = Integer.valueOf(30);
/*  76 */     this.field006 = "";
/*  77 */     this.field007 = Double.valueOf(0.0D);
/*  78 */     this.restTimes = "";
/*  79 */     this.resttimeList = new ArrayList<>();
/*     */   }
/*     */   
/*     */   public String getRestTimes() {
/*  83 */     if (this.resttimeList != null && this.resttimeList.size() > 0) {
/*  84 */       StringBuilder stringBuilder = new StringBuilder();
/*  85 */       for (HrmSchduleResttime hrmSchduleResttime : this.resttimeList) {
/*  86 */         stringBuilder.append((stringBuilder.length() == 0) ? "" : ",").append(hrmSchduleResttime.getRestTime());
/*     */       }
/*  88 */       this.restTimes = stringBuilder.toString();
/*  89 */       return this.restTimes;
/*     */     } 
/*  91 */     return "";
/*     */   }
/*     */   
/*     */   public void setRestTimes(String paramString) {
/*  95 */     this.restTimes = paramString;
/*     */   }
/*     */   
/*     */   public void setId(Long paramLong) {
/*  99 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public Long getId() {
/* 103 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setDelflag(Integer paramInteger) {
/* 107 */     this.delflag = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getDelflag() {
/* 111 */     return this.delflag;
/*     */   }
/*     */   
/*     */   public void setCreater(Long paramLong) {
/* 115 */     this.creater = paramLong;
/*     */   }
/*     */   
/*     */   public Long getCreater() {
/* 119 */     return this.creater;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 123 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/* 127 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setLastModifier(Long paramLong) {
/* 131 */     this.lastModifier = paramLong;
/*     */   }
/*     */   
/*     */   public Long getLastModifier() {
/* 135 */     return this.lastModifier;
/*     */   }
/*     */   
/*     */   public void setLastModificationTime(String paramString) {
/* 139 */     this.lastModificationTime = paramString;
/*     */   }
/*     */   
/*     */   public String getLastModificationTime() {
/* 143 */     return this.lastModificationTime;
/*     */   }
/*     */   
/*     */   public void setField001(String paramString) {
/* 147 */     this.field001 = paramString;
/*     */   }
/*     */   
/*     */   public String getField001() {
/* 151 */     return this.field001;
/*     */   }
/*     */   
/*     */   public void setField002(String paramString) {
/* 155 */     this.field002 = paramString;
/*     */   }
/*     */   
/*     */   public String getField002() {
/* 159 */     return this.field002;
/*     */   }
/*     */   
/*     */   public void setField003(String paramString) {
/* 163 */     this.field003 = paramString;
/*     */   }
/*     */   
/*     */   public String getField003() {
/* 167 */     return this.field003;
/*     */   }
/*     */   
/*     */   public void setField004(Integer paramInteger) {
/* 171 */     this.field004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField004() {
/* 175 */     return this.field004;
/*     */   }
/*     */   
/*     */   public void setField005(Integer paramInteger) {
/* 179 */     this.field005 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField005() {
/* 183 */     return this.field005;
/*     */   }
/*     */   
/*     */   public void setField006(String paramString) {
/* 187 */     this.field006 = paramString;
/*     */   }
/*     */   
/*     */   public String getField006() {
/* 191 */     return this.field006;
/*     */   }
/*     */   
/*     */   public Double getField007() {
/* 195 */     return this.field007;
/*     */   }
/*     */   
/*     */   public void setField007(Double paramDouble) {
/* 199 */     this.field007 = paramDouble;
/*     */   }
/*     */   
/*     */   public String getDateTime(String paramString) {
/* 203 */     return paramString + " " + this.field002 + "-" + paramString + " " + this.field003;
/*     */   }
/*     */   
/*     */   public String getTime() {
/* 207 */     return this.field002 + "-" + this.field003;
/*     */   }
/*     */   public String getSignDateTime(String paramString) {
/* 210 */     if (this.field002.compareTo(this.field003) > 0)
/*     */     {
/* 212 */       return getStartSignInTime(paramString) + "#" + getEndSignOutTime(DateUtil.addDate(paramString, 1));
/*     */     }
/* 214 */     return getStartSignInTime(paramString) + "#" + getEndSignOutTime(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSignTime() {
/* 219 */     return getStartSignInTime() + "-" + getEndSignOutTime();
/*     */   }
/*     */   
/*     */   public String getStartSignInTime(String paramString) {
/* 223 */     return getSignTime(paramString, this.field002, -this.field004.intValue());
/*     */   }
/*     */   public String getStartSignInTime() {
/* 226 */     return getSignTime(this.field002, -this.field004.intValue());
/*     */   }
/*     */   
/*     */   public String getEndSignOutTime() {
/* 230 */     return getSignTime(this.field003, this.field005.intValue());
/*     */   }
/*     */   public String getEndSignOutTime(String paramString) {
/* 233 */     return getSignTime(paramString, this.field003, this.field005.intValue());
/*     */   }
/*     */   
/*     */   private String getSignTime(String paramString, int paramInt) {
/* 237 */     Calendar calendar = DateUtil.getCalendar(DateUtil.getCurrentDate() + " " + paramString);
/* 238 */     calendar = DateUtil.addMinute(calendar, paramInt);
/* 239 */     return DateUtil.getCalendarDate(calendar, "HH:mm");
/*     */   }
/*     */   
/*     */   private String getSignTime(String paramString1, String paramString2, int paramInt) {
/* 243 */     Calendar calendar = DateUtil.getCalendar(paramString1 + " " + paramString2);
/* 244 */     calendar = DateUtil.addMinute(calendar, paramInt);
/* 245 */     return DateUtil.getCalendarDate(calendar, "yyyy-MM-dd HH:mm");
/*     */   }
/*     */   
/*     */   public List<HrmSchduleResttime> getResttimeList() {
/* 249 */     return this.resttimeList;
/*     */   }
/*     */   
/*     */   public void setResttimeList(List<HrmSchduleResttime> paramList) {
/* 253 */     this.resttimeList = paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleWorktime.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */