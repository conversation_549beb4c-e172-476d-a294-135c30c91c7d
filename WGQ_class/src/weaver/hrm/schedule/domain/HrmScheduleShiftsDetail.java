/*     */ package weaver.hrm.schedule.domain;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import weaver.common.StringUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleShiftsDetail
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Long id;
/*     */   private Integer delflag;
/*     */   private Long mfid;
/*     */   private Integer d001;
/*     */   private Integer d002;
/*     */   private Integer d003;
/*     */   private Integer d004;
/*     */   private Integer d005;
/*     */   private Integer d006;
/*     */   private Integer d007;
/*     */   private Integer d008;
/*     */   private Integer d009;
/*     */   private Integer d010;
/*     */   private Integer d011;
/*     */   private Integer d012;
/*     */   private Integer d013;
/*     */   private Integer d014;
/*     */   private Integer d015;
/*     */   private Integer d016;
/*     */   private Integer d017;
/*     */   private Integer d018;
/*     */   private Integer d019;
/*     */   private Integer d020;
/*     */   private Integer d021;
/*     */   private Integer d022;
/*     */   private Integer d023;
/*     */   private Integer d024;
/*     */   private Integer d025;
/*     */   private Integer d026;
/*     */   private Integer d027;
/*     */   private Integer d028;
/*     */   private Integer d029;
/*     */   private Integer d030;
/*     */   private Integer d031;
/*     */   private Integer w001;
/*     */   private Integer w002;
/*     */   private Integer w003;
/*     */   private Integer w004;
/*     */   private Integer w005;
/*     */   private Integer w006;
/*     */   private Integer w007;
/*     */   private Long field001;
/*     */   private String field002;
/*     */   private Integer field003;
/*     */   private Integer field004;
/*     */   private Integer field005;
/*     */   private Integer t2Field003;
/*     */   
/*     */   public HrmScheduleShiftsDetail() {
/* 112 */     this(true);
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsDetail(Long paramLong) {
/* 116 */     this(true);
/* 117 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsDetail(boolean paramBoolean) {
/* 121 */     if (paramBoolean) init(); 
/*     */   }
/*     */   
/*     */   public void init() {
/* 125 */     this.id = Long.valueOf(0L);
/* 126 */     this.delflag = Integer.valueOf(0);
/* 127 */     this.mfid = Long.valueOf(StringUtil.getUUID());
/* 128 */     this.d001 = Integer.valueOf(0);
/* 129 */     this.d002 = Integer.valueOf(0);
/* 130 */     this.d003 = Integer.valueOf(0);
/* 131 */     this.d004 = Integer.valueOf(0);
/* 132 */     this.d005 = Integer.valueOf(0);
/* 133 */     this.d006 = Integer.valueOf(0);
/* 134 */     this.d007 = Integer.valueOf(0);
/* 135 */     this.d008 = Integer.valueOf(0);
/* 136 */     this.d009 = Integer.valueOf(0);
/* 137 */     this.d010 = Integer.valueOf(0);
/* 138 */     this.d011 = Integer.valueOf(0);
/* 139 */     this.d012 = Integer.valueOf(0);
/* 140 */     this.d013 = Integer.valueOf(0);
/* 141 */     this.d014 = Integer.valueOf(0);
/* 142 */     this.d015 = Integer.valueOf(0);
/* 143 */     this.d016 = Integer.valueOf(0);
/* 144 */     this.d017 = Integer.valueOf(0);
/* 145 */     this.d018 = Integer.valueOf(0);
/* 146 */     this.d019 = Integer.valueOf(0);
/* 147 */     this.d020 = Integer.valueOf(0);
/* 148 */     this.d021 = Integer.valueOf(0);
/* 149 */     this.d022 = Integer.valueOf(0);
/* 150 */     this.d023 = Integer.valueOf(0);
/* 151 */     this.d024 = Integer.valueOf(0);
/* 152 */     this.d025 = Integer.valueOf(0);
/* 153 */     this.d026 = Integer.valueOf(0);
/* 154 */     this.d027 = Integer.valueOf(0);
/* 155 */     this.d028 = Integer.valueOf(0);
/* 156 */     this.d029 = Integer.valueOf(0);
/* 157 */     this.d030 = Integer.valueOf(0);
/* 158 */     this.d031 = Integer.valueOf(0);
/* 159 */     this.w001 = Integer.valueOf(0);
/* 160 */     this.w002 = Integer.valueOf(0);
/* 161 */     this.w003 = Integer.valueOf(0);
/* 162 */     this.w004 = Integer.valueOf(0);
/* 163 */     this.w005 = Integer.valueOf(0);
/* 164 */     this.w006 = Integer.valueOf(0);
/* 165 */     this.w007 = Integer.valueOf(0);
/* 166 */     this.field001 = Long.valueOf(0L);
/* 167 */     this.field002 = "";
/* 168 */     this.field003 = Integer.valueOf(0);
/* 169 */     this.field004 = Integer.valueOf(0);
/* 170 */     this.field005 = Integer.valueOf(1);
/* 171 */     this.t2Field003 = Integer.valueOf(0);
/*     */   }
/*     */   
/*     */   public void setId(Long paramLong) {
/* 175 */     this.id = paramLong;
/*     */   }
/*     */   
/*     */   public Long getId() {
/* 179 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setDelflag(Integer paramInteger) {
/* 183 */     this.delflag = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getDelflag() {
/* 187 */     return this.delflag;
/*     */   }
/*     */   
/*     */   public Long getMfid() {
/* 191 */     return this.mfid;
/*     */   }
/*     */   
/*     */   public void setMfid(Long paramLong) {
/* 195 */     this.mfid = paramLong;
/*     */   }
/*     */   
/*     */   public void setD001(Integer paramInteger) {
/* 199 */     this.d001 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD001() {
/* 203 */     return this.d001;
/*     */   }
/*     */   
/*     */   public void setD002(Integer paramInteger) {
/* 207 */     this.d002 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD002() {
/* 211 */     return this.d002;
/*     */   }
/*     */   
/*     */   public void setD003(Integer paramInteger) {
/* 215 */     this.d003 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD003() {
/* 219 */     return this.d003;
/*     */   }
/*     */   
/*     */   public void setD004(Integer paramInteger) {
/* 223 */     this.d004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD004() {
/* 227 */     return this.d004;
/*     */   }
/*     */   
/*     */   public void setD005(Integer paramInteger) {
/* 231 */     this.d005 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD005() {
/* 235 */     return this.d005;
/*     */   }
/*     */   
/*     */   public void setD006(Integer paramInteger) {
/* 239 */     this.d006 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD006() {
/* 243 */     return this.d006;
/*     */   }
/*     */   
/*     */   public void setD007(Integer paramInteger) {
/* 247 */     this.d007 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD007() {
/* 251 */     return this.d007;
/*     */   }
/*     */   
/*     */   public void setD008(Integer paramInteger) {
/* 255 */     this.d008 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD008() {
/* 259 */     return this.d008;
/*     */   }
/*     */   
/*     */   public void setD009(Integer paramInteger) {
/* 263 */     this.d009 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD009() {
/* 267 */     return this.d009;
/*     */   }
/*     */   
/*     */   public void setD010(Integer paramInteger) {
/* 271 */     this.d010 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD010() {
/* 275 */     return this.d010;
/*     */   }
/*     */   
/*     */   public void setD011(Integer paramInteger) {
/* 279 */     this.d011 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD011() {
/* 283 */     return this.d011;
/*     */   }
/*     */   
/*     */   public void setD012(Integer paramInteger) {
/* 287 */     this.d012 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD012() {
/* 291 */     return this.d012;
/*     */   }
/*     */   
/*     */   public void setD013(Integer paramInteger) {
/* 295 */     this.d013 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD013() {
/* 299 */     return this.d013;
/*     */   }
/*     */   
/*     */   public void setD014(Integer paramInteger) {
/* 303 */     this.d014 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD014() {
/* 307 */     return this.d014;
/*     */   }
/*     */   
/*     */   public void setD015(Integer paramInteger) {
/* 311 */     this.d015 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD015() {
/* 315 */     return this.d015;
/*     */   }
/*     */   
/*     */   public void setD016(Integer paramInteger) {
/* 319 */     this.d016 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD016() {
/* 323 */     return this.d016;
/*     */   }
/*     */   
/*     */   public void setD017(Integer paramInteger) {
/* 327 */     this.d017 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD017() {
/* 331 */     return this.d017;
/*     */   }
/*     */   
/*     */   public void setD018(Integer paramInteger) {
/* 335 */     this.d018 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD018() {
/* 339 */     return this.d018;
/*     */   }
/*     */   
/*     */   public void setD019(Integer paramInteger) {
/* 343 */     this.d019 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD019() {
/* 347 */     return this.d019;
/*     */   }
/*     */   
/*     */   public void setD020(Integer paramInteger) {
/* 351 */     this.d020 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD020() {
/* 355 */     return this.d020;
/*     */   }
/*     */   
/*     */   public void setD021(Integer paramInteger) {
/* 359 */     this.d021 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD021() {
/* 363 */     return this.d021;
/*     */   }
/*     */   
/*     */   public void setD022(Integer paramInteger) {
/* 367 */     this.d022 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD022() {
/* 371 */     return this.d022;
/*     */   }
/*     */   
/*     */   public void setD023(Integer paramInteger) {
/* 375 */     this.d023 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD023() {
/* 379 */     return this.d023;
/*     */   }
/*     */   
/*     */   public void setD024(Integer paramInteger) {
/* 383 */     this.d024 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD024() {
/* 387 */     return this.d024;
/*     */   }
/*     */   
/*     */   public void setD025(Integer paramInteger) {
/* 391 */     this.d025 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD025() {
/* 395 */     return this.d025;
/*     */   }
/*     */   
/*     */   public void setD026(Integer paramInteger) {
/* 399 */     this.d026 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD026() {
/* 403 */     return this.d026;
/*     */   }
/*     */   
/*     */   public void setD027(Integer paramInteger) {
/* 407 */     this.d027 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD027() {
/* 411 */     return this.d027;
/*     */   }
/*     */   
/*     */   public void setD028(Integer paramInteger) {
/* 415 */     this.d028 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD028() {
/* 419 */     return this.d028;
/*     */   }
/*     */   
/*     */   public void setD029(Integer paramInteger) {
/* 423 */     this.d029 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD029() {
/* 427 */     return this.d029;
/*     */   }
/*     */   
/*     */   public void setD030(Integer paramInteger) {
/* 431 */     this.d030 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD030() {
/* 435 */     return this.d030;
/*     */   }
/*     */   
/*     */   public void setD031(Integer paramInteger) {
/* 439 */     this.d031 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getD031() {
/* 443 */     return this.d031;
/*     */   }
/*     */   
/*     */   public void setW001(Integer paramInteger) {
/* 447 */     this.w001 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getW001() {
/* 451 */     return this.w001;
/*     */   }
/*     */   
/*     */   public void setW002(Integer paramInteger) {
/* 455 */     this.w002 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getW002() {
/* 459 */     return this.w002;
/*     */   }
/*     */   
/*     */   public void setW003(Integer paramInteger) {
/* 463 */     this.w003 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getW003() {
/* 467 */     return this.w003;
/*     */   }
/*     */   
/*     */   public void setW004(Integer paramInteger) {
/* 471 */     this.w004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getW004() {
/* 475 */     return this.w004;
/*     */   }
/*     */   
/*     */   public void setW005(Integer paramInteger) {
/* 479 */     this.w005 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getW005() {
/* 483 */     return this.w005;
/*     */   }
/*     */   
/*     */   public void setW006(Integer paramInteger) {
/* 487 */     this.w006 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getW006() {
/* 491 */     return this.w006;
/*     */   }
/*     */   
/*     */   public void setW007(Integer paramInteger) {
/* 495 */     this.w007 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getW007() {
/* 499 */     return this.w007;
/*     */   }
/*     */   
/*     */   public void setField001(Long paramLong) {
/* 503 */     this.field001 = paramLong;
/*     */   }
/*     */   
/*     */   public Long getField001() {
/* 507 */     return this.field001;
/*     */   }
/*     */   
/*     */   public void setField002(String paramString) {
/* 511 */     this.field002 = paramString;
/*     */   }
/*     */   
/*     */   public String getField002() {
/* 515 */     return this.field002;
/*     */   }
/*     */   
/*     */   public void setField003(Integer paramInteger) {
/* 519 */     this.field003 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField003() {
/* 523 */     return this.field003;
/*     */   }
/*     */   
/*     */   public void setField004(Integer paramInteger) {
/* 527 */     this.field004 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField004() {
/* 531 */     return this.field004;
/*     */   }
/*     */   
/*     */   public void setField005(Integer paramInteger) {
/* 535 */     this.field005 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getField005() {
/* 539 */     return this.field005;
/*     */   }
/*     */   
/*     */   public Integer getT2Field003() {
/* 543 */     return this.t2Field003;
/*     */   }
/*     */   
/*     */   public void setT2Field003(Integer paramInteger) {
/* 547 */     this.t2Field003 = paramInteger;
/*     */   }
/*     */   
/*     */   public int[] getDIntFields() {
/* 551 */     return new int[] { this.d001.intValue(), this.d002.intValue(), this.d003.intValue(), this.d004.intValue(), this.d005.intValue(), this.d006.intValue(), this.d007.intValue(), this.d008.intValue(), this.d009.intValue(), this.d010.intValue(), this.d011.intValue(), this.d012.intValue(), this.d013.intValue(), this.d014.intValue(), this.d015.intValue(), this.d016.intValue(), this.d017.intValue(), this.d018.intValue(), this.d019.intValue(), this.d020.intValue(), this.d021.intValue(), this.d022.intValue(), this.d023.intValue(), this.d024.intValue(), this.d025.intValue(), this.d026.intValue(), this.d027.intValue(), this.d028.intValue(), this.d029.intValue(), this.d030.intValue(), this.d031.intValue() };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int totalDNum() {
/* 560 */     byte b = 0;
/* 561 */     int[] arrayOfInt = getDIntFields();
/* 562 */     for (int i : arrayOfInt) { if (i == 1) b++;  }
/* 563 */      return b;
/*     */   }
/*     */   
/*     */   public String getDValues() {
/* 567 */     return getValues(getDStrFields(), getDIntFields());
/*     */   }
/*     */   
/*     */   public String getWValues() {
/* 571 */     return getValues(getWStrFields(), getWFields());
/*     */   }
/*     */   
/*     */   private String getValues(String[] paramArrayOfString, int[] paramArrayOfint) {
/* 575 */     StringBuffer stringBuffer = new StringBuffer();
/* 576 */     for (byte b = 0; b < paramArrayOfint.length; b++) {
/* 577 */       stringBuffer.append((stringBuffer.length() == 0) ? "" : ",").append(paramArrayOfString[b]).append(":").append(paramArrayOfint[b]);
/*     */     }
/* 579 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public int[] getWFields() {
/* 583 */     return new int[] { this.w007.intValue(), this.w001.intValue(), this.w002.intValue(), this.w003.intValue(), this.w004.intValue(), this.w005.intValue(), this.w006.intValue() };
/*     */   }
/*     */   
/*     */   public int totalWNum() {
/* 587 */     byte b = 0;
/* 588 */     int[] arrayOfInt = getWFields();
/* 589 */     for (int i : arrayOfInt) { if (i == 1) b++;  }
/* 590 */      return b;
/*     */   }
/*     */   
/*     */   public String[] getWStrFields() {
/* 594 */     return new String[] { "w007", "w001", "w002", "w003", "w004", "w005", "w006" };
/*     */   }
/*     */   
/*     */   public String[] getDStrFields() {
/* 598 */     return new String[] { "d001", "d002", "d003", "d004", "d005", "d006", "d007", "d008", "d009", "d010", "d011", "d012", "d013", "d014", "d015", "d016", "d017", "d018", "d019", "d020", "d021", "d022", "d023", "d024", "d025", "d026", "d027", "d028", "d029", "d030", "d031" };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetFieldValue() {
/* 608 */     byte b = (this.field005.intValue() < 1) ? 0 : ((this.field005.intValue() > 31) ? 31 : this.field005.intValue());
/* 609 */     String[] arrayOfString = getDStrFields();
/* 610 */     if (this.field004.intValue() == 0)
/* 611 */     { byte b1; for (b1 = 0; b1 < b; ) { setFieldValue(arrayOfString[b1], 1); b1++; }
/* 612 */        if (b < 31) for (b1 = b; b1 < 31; ) { setFieldValue(arrayOfString[b1], -1); b1++; }
/*     */           }
/* 614 */     else { byte b1; for (b1 = 30; b1 >= 31 - b; ) { setFieldValue(arrayOfString[b1], 1); b1--; }
/* 615 */        if (b < 31) for (b1 = 0; b1 < 31 - b; ) { setFieldValue(arrayOfString[b1], -1); b1++; }
/*     */           }
/*     */   
/*     */   }
/*     */   private void setFieldValue(String paramString, int paramInt) {
/* 620 */     if (paramString.equals("d001")) { this.d001 = Integer.valueOf(paramInt); }
/* 621 */     else if (paramString.equals("d002")) { this.d002 = Integer.valueOf(paramInt); }
/* 622 */     else if (paramString.equals("d003")) { this.d003 = Integer.valueOf(paramInt); }
/* 623 */     else if (paramString.equals("d004")) { this.d004 = Integer.valueOf(paramInt); }
/* 624 */     else if (paramString.equals("d005")) { this.d005 = Integer.valueOf(paramInt); }
/* 625 */     else if (paramString.equals("d006")) { this.d006 = Integer.valueOf(paramInt); }
/* 626 */     else if (paramString.equals("d007")) { this.d007 = Integer.valueOf(paramInt); }
/* 627 */     else if (paramString.equals("d008")) { this.d008 = Integer.valueOf(paramInt); }
/* 628 */     else if (paramString.equals("d009")) { this.d009 = Integer.valueOf(paramInt); }
/* 629 */     else if (paramString.equals("d010")) { this.d010 = Integer.valueOf(paramInt); }
/* 630 */     else if (paramString.equals("d011")) { this.d011 = Integer.valueOf(paramInt); }
/* 631 */     else if (paramString.equals("d012")) { this.d012 = Integer.valueOf(paramInt); }
/* 632 */     else if (paramString.equals("d013")) { this.d013 = Integer.valueOf(paramInt); }
/* 633 */     else if (paramString.equals("d014")) { this.d014 = Integer.valueOf(paramInt); }
/* 634 */     else if (paramString.equals("d015")) { this.d015 = Integer.valueOf(paramInt); }
/* 635 */     else if (paramString.equals("d016")) { this.d016 = Integer.valueOf(paramInt); }
/* 636 */     else if (paramString.equals("d017")) { this.d017 = Integer.valueOf(paramInt); }
/* 637 */     else if (paramString.equals("d018")) { this.d018 = Integer.valueOf(paramInt); }
/* 638 */     else if (paramString.equals("d019")) { this.d019 = Integer.valueOf(paramInt); }
/* 639 */     else if (paramString.equals("d020")) { this.d020 = Integer.valueOf(paramInt); }
/* 640 */     else if (paramString.equals("d021")) { this.d021 = Integer.valueOf(paramInt); }
/* 641 */     else if (paramString.equals("d022")) { this.d022 = Integer.valueOf(paramInt); }
/* 642 */     else if (paramString.equals("d023")) { this.d023 = Integer.valueOf(paramInt); }
/* 643 */     else if (paramString.equals("d024")) { this.d024 = Integer.valueOf(paramInt); }
/* 644 */     else if (paramString.equals("d025")) { this.d025 = Integer.valueOf(paramInt); }
/* 645 */     else if (paramString.equals("d026")) { this.d026 = Integer.valueOf(paramInt); }
/* 646 */     else if (paramString.equals("d027")) { this.d027 = Integer.valueOf(paramInt); }
/* 647 */     else if (paramString.equals("d028")) { this.d028 = Integer.valueOf(paramInt); }
/* 648 */     else if (paramString.equals("d029")) { this.d029 = Integer.valueOf(paramInt); }
/* 649 */     else if (paramString.equals("d030")) { this.d030 = Integer.valueOf(paramInt); }
/* 650 */     else if (paramString.equals("d031")) { this.d031 = Integer.valueOf(paramInt); }
/*     */   
/*     */   }
/*     */   public boolean isSelect(int paramInt1, int paramInt2) {
/* 654 */     boolean bool = false;
/* 655 */     if (paramInt1 == 0) {
/* 656 */       bool = isSelectWeek(paramInt2);
/* 657 */     } else if (paramInt1 == 1) {
/* 658 */       bool = isSelectDate(paramInt2);
/*     */     } 
/* 660 */     return bool;
/*     */   }
/*     */   
/*     */   public boolean isSelectWeek(int paramInt) {
/* 664 */     paramInt = (paramInt == 7) ? 0 : paramInt;
/* 665 */     return (paramInt >= 0 && paramInt <= 6) ? ((getWFields()[paramInt] == 1)) : false;
/*     */   }
/*     */   
/*     */   public boolean isSelectDate(int paramInt) {
/* 669 */     paramInt--; return (
/* 670 */       paramInt >= 0 && paramInt <= 30) ? ((getDIntFields()[paramInt] == 1)) : false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/domain/HrmScheduleShiftsDetail.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */