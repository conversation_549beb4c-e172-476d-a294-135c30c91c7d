/*    */ package weaver.hrm.schedule;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.company.CompanyComInfo;
/*    */ import weaver.hrm.company.DepartmentComInfo;
/*    */ import weaver.hrm.company.SubCompanyComInfo;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmDefaultSchedule
/*    */ {
/*    */   public String getScheduleTypeName(String paramString1, String paramString2) {
/* 16 */     String str = "";
/*    */     
/* 18 */     if (paramString1.equals("3")) {
/* 19 */       str = SystemEnv.getHtmlLabelName(140, Util.getIntValue(paramString2));
/* 20 */     } else if (paramString1.equals("4")) {
/* 21 */       str = SystemEnv.getHtmlLabelName(141, Util.getIntValue(paramString2));
/* 22 */     } else if (paramString1.equals("5")) {
/* 23 */       str = SystemEnv.getHtmlLabelName(124, Util.getIntValue(paramString2));
/* 24 */     } else if (paramString1.equals("6")) {
/* 25 */       str = SystemEnv.getHtmlLabelName(179, Util.getIntValue(paramString2));
/*    */     } 
/*    */     
/* 28 */     return str;
/*    */   }
/*    */   
/*    */   public String getRelatedName(String paramString1, String paramString2) throws Exception {
/* 32 */     String str = "";
/*    */     
/* 34 */     if (paramString2.equals("3")) {
/* 35 */       str = (new CompanyComInfo()).getCompanyname(paramString1);
/* 36 */     } else if (paramString2.equals("4")) {
/* 37 */       str = (new SubCompanyComInfo()).getSubcompanynames(paramString1);
/* 38 */     } else if (paramString2.equals("5")) {
/* 39 */       str = (new DepartmentComInfo()).getDepartmentname(paramString1);
/* 40 */     } else if (paramString2.equals("6")) {
/* 41 */       str = (new ResourceComInfo()).getResourcename(paramString1);
/*    */     } 
/*    */     
/* 44 */     return str;
/*    */   }
/*    */   
/*    */   public ArrayList<String> getHrmDefaultScheduleOperate(String paramString1, String paramString2, String paramString3) {
/* 48 */     ArrayList<String> arrayList = new ArrayList();
/* 49 */     String str1 = paramString3.split(":")[0];
/* 50 */     String str2 = paramString3.split(":")[1];
/* 51 */     if (paramString2.equals("true")) {
/* 52 */       arrayList.add("true");
/*    */     } else {
/* 54 */       arrayList.add("false");
/*    */     } 
/* 56 */     if (str1.equals("true")) {
/* 57 */       arrayList.add("true");
/*    */     } else {
/* 59 */       arrayList.add("false");
/*    */     } 
/* 61 */     if (str2.equals("true")) {
/* 62 */       arrayList.add("true");
/*    */     } else {
/* 64 */       arrayList.add("false");
/*    */     } 
/* 66 */     if (paramString2.equals("true")) {
/* 67 */       arrayList.add("true");
/*    */     } else {
/* 69 */       arrayList.add("false");
/*    */     } 
/* 71 */     return arrayList;
/*    */   }
/*    */   
/*    */   public String getHrmDefaultScheduleCheckbox(String paramString) {
/* 75 */     return "true";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmDefaultSchedule.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */