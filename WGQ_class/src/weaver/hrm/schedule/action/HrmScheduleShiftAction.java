/*    */ package weaver.hrm.schedule.action;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import weaver.common.DateUtil;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.common.WeaverAction;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*    */ import weaver.hrm.schedule.controller.HrmScheduleSetController;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftAction
/*    */   extends WeaverAction
/*    */ {
/* 20 */   private HrmScheduleSetController controller = null;
/*    */   
/*    */   public HrmScheduleShiftAction() {
/* 23 */     this.controller = new HrmScheduleSetController();
/*    */   }
/*    */ 
/*    */   
/*    */   protected void handle() {
/* 28 */     RecordSet recordSet = new RecordSet();
/* 29 */     HrmScheduleSet hrmScheduleSet = null;
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     hashMap.put("requestId", "and t.requestId = " + this.requestId);
/* 32 */     String str = "select id from hrm_att_proc_set where field001 = " + this.workflowId;
/* 33 */     recordSet.executeSql((new HrmAttProcSetManager()).getSQLByField006(5, hashMap, false, true, str));
/* 34 */     if (recordSet.next()) {
/* 35 */       hrmScheduleSet = new HrmScheduleSet(Long.valueOf(StringUtil.getUUID()));
/* 36 */       hrmScheduleSet.setField001(recordSet.getString("fromDate"));
/* 37 */       hrmScheduleSet.setField002(recordSet.getString("toDate"));
/* 38 */       hrmScheduleSet.setField003(Long.valueOf(StringUtil.parseToLong(recordSet.getString("newShift"))));
/* 39 */       hrmScheduleSet.setCreater(Long.valueOf(this.creater));
/* 40 */       hrmScheduleSet.setCreateTime(DateUtil.getFullDate());
/* 41 */       hrmScheduleSet.setLastModifier(Long.valueOf(this.creater));
/* 42 */       hrmScheduleSet.setLastModificationTime(DateUtil.getFullDate());
/* 43 */       this.controller.saveSchedule(hrmScheduleSet, true, recordSet.getInt("scheduleResourceId"));
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/action/HrmScheduleShiftAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */