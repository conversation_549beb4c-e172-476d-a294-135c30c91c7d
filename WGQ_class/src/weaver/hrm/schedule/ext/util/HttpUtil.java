/*     */ package weaver.hrm.schedule.ext.util;
/*     */ 
/*     */ import java.io.InputStream;
/*     */ import java.security.SecureRandom;
/*     */ import java.util.Map;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.net.ssl.HttpsURLConnection;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import javax.net.ssl.SSLSession;
/*     */ import javax.net.ssl.TrustManager;
/*     */ import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
/*     */ import org.apache.commons.httpclient.HttpClient;
/*     */ import org.apache.commons.httpclient.HttpMethod;
/*     */ import org.apache.commons.httpclient.methods.GetMethod;
/*     */ import org.apache.commons.httpclient.methods.PostMethod;
/*     */ import org.apache.commons.httpclient.methods.RequestEntity;
/*     */ import org.apache.commons.httpclient.methods.StringRequestEntity;
/*     */ import org.apache.commons.httpclient.params.HttpClientParams;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HttpUtil
/*     */ {
/*  34 */   private static BaseBean log = new BaseBean();
/*     */   
/*     */   private static void writeLog(Object paramObject) {
/*  37 */     log.writeLog(HttpUtil.class.getName(), paramObject);
/*     */   }
/*     */   
/*     */   public static String doGet(String paramString) throws Exception {
/*  41 */     HttpClient httpClient = FWHttpConnectionManager.getHttpClient();
/*  42 */     HttpClientParams httpClientParams = httpClient.getParams();
/*  43 */     httpClientParams.setContentCharset("utf-8");
/*  44 */     httpClientParams.setHttpElementCharset("utf-8");
/*     */     
/*  46 */     GetMethod getMethod = new GetMethod(paramString);
/*  47 */     int i = httpClient.executeMethod((HttpMethod)getMethod);
/*  48 */     InputStream inputStream = getMethod.getResponseBodyAsStream();
/*     */     
/*  50 */     String str = IOUtils.toString(inputStream, "utf-8");
/*     */     
/*  52 */     writeLog("get url>>" + paramString + ";;status>>>" + i + ";;;json>>>>" + str);
/*     */     
/*  54 */     getMethod.releaseConnection();
/*     */     
/*  56 */     return str;
/*     */   }
/*     */   
/*     */   public static String doPost(String paramString, Map<String, String> paramMap) throws Exception {
/*  60 */     return doPost(paramString, paramMap, "utf-8");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map<String, String> paramMap, String paramString2) throws Exception {
/*  66 */     return doPost(paramString1, paramMap, null, paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map<String, String> paramMap1, Map<String, String> paramMap2, String paramString2) throws Exception {
/*  71 */     HttpClient httpClient = FWHttpConnectionManager.getHttpClient();
/*  72 */     HttpClientParams httpClientParams = httpClient.getParams();
/*  73 */     httpClientParams.setContentCharset("utf-8");
/*  74 */     httpClientParams.setHttpElementCharset("utf-8");
/*     */     
/*  76 */     PostMethod postMethod = new PostMethod(paramString1);
/*     */     
/*  78 */     if (paramMap2 != null) {
/*  79 */       for (Map.Entry<String, String> entry : paramMap2.entrySet()) {
/*  80 */         postMethod.addRequestHeader((String)entry.getKey(), (String)entry.getValue());
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*  85 */     for (Map.Entry<String, String> entry : paramMap1.entrySet()) {
/*  86 */       postMethod.addParameter((String)entry.getKey(), (String)entry.getValue());
/*     */     }
/*  88 */     int i = httpClient.executeMethod((HttpMethod)postMethod);
/*  89 */     InputStream inputStream = postMethod.getResponseBodyAsStream();
/*  90 */     String str = IOUtils.toString(inputStream, paramString2);
/*  91 */     postMethod.releaseConnection();
/*  92 */     return str;
/*     */   }
/*     */   
/*     */   public static String doPostForJson(String paramString1, String paramString2, Map<String, String> paramMap) throws Exception {
/*  96 */     String str1 = "";
/*  97 */     HttpClient httpClient = FWHttpConnectionManager.getHttpClient();
/*  98 */     Object object = null;
/*  99 */     PostMethod postMethod = new PostMethod(paramString1);
/* 100 */     HttpClientParams httpClientParams = httpClient.getParams();
/* 101 */     httpClientParams.setContentCharset("utf-8");
/* 102 */     httpClientParams.setHttpElementCharset("utf-8");
/*     */     
/* 104 */     if (paramMap != null) {
/* 105 */       for (Map.Entry<String, String> entry : paramMap.entrySet()) {
/* 106 */         postMethod.addRequestHeader((String)entry.getKey(), (String)entry.getValue());
/*     */       }
/*     */     }
/*     */     
/* 110 */     StringRequestEntity stringRequestEntity = new StringRequestEntity(paramString2, "application/json", "UTF-8");
/* 111 */     postMethod.setRequestEntity((RequestEntity)stringRequestEntity);
/*     */     
/* 113 */     postMethod.getParams().setParameter("http.method.retry-handler", new DefaultHttpMethodRetryHandler());
/*     */     
/* 115 */     postMethod.getParams().setParameter("http.socket.timeout", Integer.valueOf(30000));
/*     */ 
/*     */     
/* 118 */     int i = httpClient.executeMethod((HttpMethod)postMethod);
/*     */     
/* 120 */     InputStream inputStream = postMethod.getResponseBodyAsStream();
/* 121 */     String str2 = IOUtils.toString(inputStream, "UTF-8");
/* 122 */     postMethod.releaseConnection();
/*     */     
/* 124 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String doPostForJson(String paramString1, String paramString2) throws Exception {
/* 129 */     return doPostForJson(paramString1, paramString2, null);
/*     */   }
/*     */ 
/*     */   
/*     */   static {
/*     */     try {
/* 135 */       SSLContext sSLContext = SSLContext.getInstance("SSL", "SunJSSE");
/* 136 */       sSLContext.init(null, new TrustManager[] { new MyX509TrustManager() }, new SecureRandom());
/*     */       
/* 138 */       HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier()
/*     */           {
/*     */             public boolean verify(String param1String, SSLSession param1SSLSession) {
/* 141 */               return true;
/*     */             }
/*     */           });
/* 144 */       HttpsURLConnection.setDefaultSSLSocketFactory(sSLContext.getSocketFactory());
/* 145 */     } catch (Exception exception) {
/* 146 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/ext/util/HttpUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */