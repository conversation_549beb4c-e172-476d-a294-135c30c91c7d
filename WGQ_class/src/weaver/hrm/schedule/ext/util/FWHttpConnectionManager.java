/*    */ package weaver.hrm.schedule.ext.util;
/*    */ 
/*    */ import org.apache.commons.httpclient.HttpClient;
/*    */ import org.apache.commons.httpclient.HttpConnectionManager;
/*    */ import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
/*    */ 
/*    */ 
/*    */ public class FWHttpConnectionManager
/*    */ {
/* 10 */   private static HttpConnectionManager connectionManager = (HttpConnectionManager)new MultiThreadedHttpConnectionManager();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static HttpConnectionManager getInstance() {
/* 16 */     return connectionManager;
/*    */   }
/*    */   
/*    */   public static HttpClient getHttpClient() {
/* 20 */     HttpClient httpClient = new HttpClient();
/* 21 */     httpClient.getHttpConnectionManager().getParams().setSoTimeout(10000);
/* 22 */     httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(10000);
/*    */     
/* 24 */     return httpClient;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/ext/util/FWHttpConnectionManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */