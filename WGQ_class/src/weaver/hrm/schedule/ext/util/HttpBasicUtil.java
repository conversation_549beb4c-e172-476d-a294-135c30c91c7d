/*    */ package weaver.hrm.schedule.ext.util;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ import weaver.general.Base64;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HttpBasicUtil
/*    */ {
/*    */   private String username;
/*    */   private String password;
/* 16 */   private String format_up = "%s:%s";
/* 17 */   private String format_header = "Basic %s";
/* 18 */   private String header_key = "Authorization";
/*    */   
/*    */   public HttpBasicUtil(String paramString1, String paramString2) {
/* 21 */     this.username = paramString1;
/* 22 */     this.password = paramString2;
/*    */   }
/*    */   
/*    */   public void writeLog(Object paramObject) {
/* 26 */     writeLog(HttpBasicUtil.class.getName(), paramObject);
/*    */   }
/*    */   
/*    */   public void writeLog(String paramString, Object paramObject) {
/* 30 */     Log log = LogFactory.getLog(paramString);
/* 31 */     if (paramObject instanceof Exception) {
/* 32 */       log.error(paramString, (Exception)paramObject);
/*    */     } else {
/* 34 */       log.error(paramObject);
/*    */     } 
/*    */   }
/*    */   
/*    */   public String doPost(String paramString, Map<String, String> paramMap) throws Exception {
/* 39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 40 */     String str1 = String.format(this.format_up, new Object[] { this.username, this.password });
/* 41 */     String str2 = new String(Base64.encode(str1.getBytes()));
/* 42 */     String str3 = String.format(this.format_header, new Object[] { str2 });
/* 43 */     hashMap.put(this.header_key, str3);
/*    */     
/* 45 */     return HttpUtil.doPost(paramString, paramMap, (Map)hashMap, "utf-8");
/*    */   }
/*    */ 
/*    */   
/*    */   public String doPostForJson(String paramString1, String paramString2) throws Exception {
/* 50 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 51 */     String str1 = String.format(this.format_up, new Object[] { this.username, this.password });
/* 52 */     String str2 = new String(Base64.encode(str1.getBytes()));
/* 53 */     String str3 = String.format(this.format_header, new Object[] { str2 });
/* 54 */     hashMap.put(this.header_key, str3);
/* 55 */     hashMap.put("Content-Type", "application/json");
/*    */ 
/*    */     
/* 58 */     return HttpUtil.doPostForJson(paramString1, paramString2, (Map)hashMap);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/ext/util/HttpBasicUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */