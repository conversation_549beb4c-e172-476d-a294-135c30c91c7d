/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.Hashtable;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmTimeShiftInit
/*     */   extends BaseBean
/*     */ {
/*  24 */   ArrayList reesourceshifts = null;
/*  25 */   ArrayList reesourcedeparts = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  30 */   ArrayList shiftids = null;
/*  31 */   ArrayList shiftbegintimes = null;
/*  32 */   ArrayList shiftendtimes = null;
/*  33 */   ArrayList shifttypes = null;
/*     */ 
/*     */ 
/*     */   
/*  37 */   ArrayList reesourcecarddates = null;
/*  38 */   ArrayList cardtimes = null;
/*  39 */   ArrayList cardworkouts = null;
/*     */   
/*  41 */   String fromdate = "";
/*  42 */   String enddate = "";
/*  43 */   int blurminitus = 0;
/*  44 */   char separator = Util.getSeparator();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFromDate(String paramString) {
/*  51 */     this.fromdate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEndDate(String paramString) {
/*  59 */     this.enddate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReesourceshifts(ArrayList paramArrayList) {
/*  67 */     this.reesourceshifts = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReesourcedeparts(ArrayList paramArrayList) {
/*  75 */     this.reesourcedeparts = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShiftids(ArrayList paramArrayList) {
/*  83 */     this.shiftids = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShiftbegintimes(ArrayList paramArrayList) {
/*  91 */     this.shiftbegintimes = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShiftendtimes(ArrayList paramArrayList) {
/*  99 */     this.shiftendtimes = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShifttypes(ArrayList paramArrayList) {
/* 107 */     this.shifttypes = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReesourcecarddates(ArrayList paramArrayList) {
/* 115 */     this.reesourcecarddates = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCardtimes(ArrayList paramArrayList) {
/* 123 */     this.cardtimes = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCardworkouts(ArrayList paramArrayList) {
/* 131 */     this.cardworkouts = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBlurminitus(int paramInt) {
/* 139 */     this.blurminitus = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doBlurShift() {
/* 147 */     Hashtable<Object, Object> hashtable1 = new Hashtable<Object, Object>();
/* 148 */     Hashtable<Object, Object> hashtable2 = null;
/* 149 */     ArrayList<String> arrayList1 = null;
/* 150 */     ArrayList<String> arrayList2 = null;
/* 151 */     ArrayList<String> arrayList3 = null;
/* 152 */     ArrayList<String> arrayList4 = null;
/* 153 */     ArrayList<String> arrayList5 = null;
/*     */ 
/*     */     
/* 156 */     Calendar calendar = Calendar.getInstance();
/* 157 */     String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */     
/* 160 */     ArrayList<String> arrayList6 = new ArrayList();
/*     */     
/* 162 */     int i = Util.getIntValue(this.fromdate.substring(0, 4));
/* 163 */     int j = Util.getIntValue(this.fromdate.substring(5, 7));
/* 164 */     int k = Util.getIntValue(this.fromdate.substring(8, 10));
/* 165 */     String str2 = this.fromdate;
/*     */     
/* 167 */     calendar.set(i, j - 1, k);
/* 168 */     while (str2.compareTo(this.enddate) <= 0) {
/* 169 */       arrayList6.add(str2);
/* 170 */       calendar.add(5, 1);
/* 171 */       str2 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 177 */     ArrayList<String> arrayList7 = new ArrayList();
/* 178 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/* 179 */     RecordSet recordSet = new RecordSet();
/* 180 */     recordSet.executeSql("select a.departmentid,a.shiftid from HrmDepartmentShift a , HrmArrangeShift b where a.shiftid=b.id order by a.departmentid , b.shiftbegintime ");
/*     */     
/* 182 */     while (recordSet.next()) {
/* 183 */       String str3 = Util.null2String(recordSet.getString(1));
/* 184 */       String str4 = Util.null2String(recordSet.getString(2));
/*     */       
/* 186 */       int m = arrayList7.indexOf(str3);
/* 187 */       if (m == -1) {
/* 188 */         arrayList7.add(str3);
/* 189 */         ArrayList<String> arrayList9 = new ArrayList();
/* 190 */         arrayList9.add(str4);
/* 191 */         arrayList.add(arrayList9);
/*     */         continue;
/*     */       } 
/* 194 */       ArrayList<String> arrayList8 = arrayList.get(m);
/* 195 */       arrayList8.add(str4);
/* 196 */       arrayList.set(m, arrayList8);
/*     */     } 
/*     */     
/*     */     byte b;
/*     */     
/* 201 */     for (b = 0; b < arrayList7.size(); b++) {
/* 202 */       String str = arrayList7.get(b);
/* 203 */       ArrayList<String> arrayList8 = arrayList.get(b);
/*     */       
/* 205 */       hashtable2 = new Hashtable<Object, Object>();
/* 206 */       arrayList1 = new ArrayList();
/* 207 */       arrayList2 = new ArrayList();
/* 208 */       arrayList3 = new ArrayList();
/* 209 */       arrayList4 = new ArrayList();
/* 210 */       arrayList5 = new ArrayList();
/*     */       
/* 212 */       hashtable2.put("id", arrayList1);
/* 213 */       hashtable2.put("date", arrayList2);
/* 214 */       hashtable2.put("begtime", arrayList3);
/* 215 */       hashtable2.put("endtime", arrayList4);
/* 216 */       hashtable2.put("count", arrayList5);
/*     */       
/* 218 */       for (byte b1 = 0; b1 < arrayList6.size(); b1++) {
/* 219 */         String str3 = arrayList6.get(b1);
/* 220 */         String str4 = "";
/* 221 */         if (b1 + 1 < arrayList6.size()) str4 = arrayList6.get(b1 + 1);
/*     */         
/* 223 */         for (byte b2 = 0; b2 < arrayList8.size(); b2++) {
/* 224 */           String str5 = arrayList8.get(b2);
/* 225 */           int m = this.shiftids.indexOf(str5);
/* 226 */           if (m != -1) {
/* 227 */             String str6 = this.shiftbegintimes.get(m);
/* 228 */             String str7 = this.shiftendtimes.get(m);
/* 229 */             String str8 = this.shifttypes.get(m);
/*     */             
/* 231 */             if (!str8.equals("1") || !str4.equals("")) {
/*     */               
/* 233 */               arrayList1.add(str5);
/* 234 */               arrayList2.add(str3);
/* 235 */               arrayList5.add("0");
/* 236 */               arrayList3.add(str3 + " " + str6);
/* 237 */               if (str8.equals("0")) {
/* 238 */                 arrayList4.add(str3 + " " + str7);
/*     */               } else {
/* 240 */                 arrayList4.add(str4 + " " + str7);
/*     */               } 
/*     */               
/* 243 */               if (arrayList8.size() > 1)
/* 244 */                 if (b2 + 1 < arrayList8.size()) {
/* 245 */                   String str9 = arrayList8.get(b2 + 1);
/* 246 */                   int n = this.shiftids.indexOf(str9);
/* 247 */                   if (n != -1) {
/* 248 */                     String str10 = this.shiftbegintimes.get(n);
/* 249 */                     String str11 = this.shiftendtimes.get(n);
/* 250 */                     String str12 = this.shifttypes.get(n);
/*     */                     
/* 252 */                     if (!str12.equals("1") || !str4.equals(""))
/*     */                     
/* 254 */                     { arrayList1.add(str5);
/* 255 */                       arrayList2.add(str3);
/* 256 */                       arrayList5.add("0");
/* 257 */                       arrayList3.add(str3 + " " + str6);
/* 258 */                       if (str12.equals("0")) {
/* 259 */                         arrayList4.add(str3 + " " + str11);
/*     */                       } else {
/* 261 */                         arrayList4.add(str4 + " " + str11);
/*     */                       } 
/* 263 */                       arrayList1.add(str9);
/* 264 */                       arrayList2.add(str3);
/* 265 */                       arrayList5.add("0");
/* 266 */                       arrayList3.add(str3 + " " + str6);
/* 267 */                       if (str12.equals("0"))
/* 268 */                       { arrayList4.add(str3 + " " + str11); }
/*     */                       else
/* 270 */                       { arrayList4.add(str4 + " " + str11); }  } 
/*     */                   } 
/* 272 */                 } else if (!str4.equals("")) {
/* 273 */                   String str9 = arrayList8.get(0);
/* 274 */                   int n = this.shiftids.indexOf(str9);
/* 275 */                   if (n != -1) {
/* 276 */                     String str10 = this.shiftbegintimes.get(n);
/* 277 */                     String str11 = this.shiftendtimes.get(n);
/*     */                     
/* 279 */                     arrayList1.add(str5);
/* 280 */                     arrayList2.add(str3);
/* 281 */                     arrayList5.add("0");
/* 282 */                     arrayList3.add(str3 + " " + str6);
/* 283 */                     arrayList4.add(str4 + " " + str11);
/*     */                     
/* 285 */                     arrayList1.add(str9);
/* 286 */                     arrayList2.add(str4);
/* 287 */                     arrayList5.add("0");
/* 288 */                     arrayList3.add(str3 + " " + str6);
/* 289 */                     arrayList4.add(str4 + " " + str11);
/*     */                   } 
/*     */                 }  
/*     */             } 
/*     */           } 
/*     */         } 
/* 295 */       }  hashtable1.put(str, hashtable2);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 302 */     for (b = 0; b < this.reesourcecarddates.size(); b++) {
/* 303 */       String str = this.cardworkouts.get(b);
/* 304 */       if (!str.equals("1")) {
/*     */         
/* 306 */         String str3 = this.reesourcecarddates.get(b);
/* 307 */         String[] arrayOfString = Util.TokenizerString2(str3, "_");
/* 308 */         String str4 = arrayOfString[0];
/* 309 */         String str5 = arrayOfString[1];
/*     */         
/* 311 */         int m = this.reesourceshifts.indexOf(str4);
/* 312 */         if (m != -1) {
/*     */           
/* 314 */           String str6 = this.reesourcedeparts.get(b);
/* 315 */           hashtable2 = (Hashtable<Object, Object>)hashtable1.get(str6);
/* 316 */           if (hashtable2 != null) {
/*     */             
/* 318 */             String str7 = this.cardtimes.get(b);
/*     */             
/* 320 */             arrayList1 = (ArrayList<String>)hashtable2.get("id");
/* 321 */             arrayList2 = (ArrayList<String>)hashtable2.get("date");
/* 322 */             arrayList3 = (ArrayList<String>)hashtable2.get("begtime");
/* 323 */             arrayList4 = (ArrayList<String>)hashtable2.get("endtime");
/* 324 */             arrayList5 = (ArrayList<String>)hashtable2.get("count");
/*     */             
/*     */             byte b1;
/* 327 */             for (b1 = 0; b1 < arrayList1.size(); b1++) {
/* 328 */               arrayList5.set(b1, "0");
/*     */             }
/*     */             
/* 331 */             for (b1 = 0; b1 < arrayList1.size(); b1++) {
/* 332 */               String str8 = arrayList2.get(b1);
/* 333 */               int n = Util.dayDiff(str5, str8);
/* 334 */               if (n >= -2) {
/* 335 */                 if (n > 2)
/*     */                   break; 
/* 337 */                 String str9 = arrayList2.get(b1);
/* 338 */                 String str10 = arrayList4.get(b1);
/*     */                 
/* 340 */                 if (isLessthanInterval(str5 + " " + str7, str9) || isLessthanInterval(str5 + " " + str7, str10)) {
/* 341 */                   arrayList5.set(b1, "" + Util.getIntValue((String)arrayList5.get(b1) + '\001', 0));
/*     */                 }
/*     */               } 
/*     */             } 
/* 345 */             recordSet.executeSql("delete from HrmArrangeShiftInfo where resourceid=" + str4 + " and shiftdate > '" + this.fromdate + "' and shiftdate < '" + this.enddate + "')");
/*     */ 
/*     */             
/* 348 */             for (b1 = 0; b1 < arrayList1.size(); b1++) {
/* 349 */               int n = Util.getIntValue(arrayList5.get(b1), 0);
/* 350 */               if (n > 1) {
/* 351 */                 String str8 = arrayList2.get(b1);
/* 352 */                 String str9 = arrayList1.get(b1);
/* 353 */                 String str10 = str4 + this.separator + str8 + this.separator + str9;
/* 354 */                 recordSet.executeProc("HrmArrangeShiftInfo_Insert", str10);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isLessthanInterval(String paramString1, String paramString2) {
/* 367 */     if (paramString1.length() != 19 || paramString2.length() != 19) return false;
/*     */     
/* 369 */     int i = Util.timediff2(paramString1.substring(0, 10), paramString1.substring(11, 19), paramString2.substring(0, 10), paramString2.substring(11, 19));
/* 370 */     if (i < 0) i = -1 * i;
/*     */     
/* 372 */     if (i < this.blurminitus) return true; 
/* 373 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmTimeShiftInit.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */