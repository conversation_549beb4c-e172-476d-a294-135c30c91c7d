/*    */ package weaver.hrm.schedule;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.hrm.common.Tools;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ public class HrmScheduleSignImportJob
/*    */   extends BaseCronJob {
/*    */   public void execute() {
/* 10 */     RecordSet recordSet = new RecordSet();
/*    */     
/*    */     try {
/* 13 */       recordSet.writeLog("begin do HrmScheduleSignImportJob invoke ...");
/*    */       
/* 15 */       HrmScheduleSignImport hrmScheduleSignImport = new HrmScheduleSignImport();
/*    */       
/* 17 */       String str1 = Tools.getDate(Tools.getToday(), -1);
/* 18 */       String str2 = Tools.getDate(Tools.getToday(), -1);
/*    */       
/* 20 */       recordSet.writeLog("do HrmScheduleSignImportJob invoke..[" + str1 + ";" + str2 + "]");
/*    */       
/* 22 */       hrmScheduleSignImport.importData(str1, str2, false);
/*    */       
/* 24 */       recordSet.writeLog("end do HrmScheduleSignImportJob invoke  ...");
/* 25 */     } catch (Exception exception) {
/* 26 */       recordSet.writeLog(exception);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmScheduleSignImportJob.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */