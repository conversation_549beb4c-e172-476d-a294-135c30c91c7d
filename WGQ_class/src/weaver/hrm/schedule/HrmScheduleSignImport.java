/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.io.FileInputStream;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.Timestamp;
/*     */ import java.text.DateFormat;
/*     */ import java.text.DecimalFormat;
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDataFormat;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDateUtil;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*     */ import org.apache.poi.ss.usermodel.Cell;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileUploadToPath;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.schedule.imp.HrmScheduleImpManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*     */ import weaver.interfaces.datasource.DataSource;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class HrmScheduleSignImport
/*     */   extends BaseBean {
/*  47 */   private List errorInfo = new ArrayList();
/*  48 */   private String fileName = "";
/*  49 */   private String keyField = "";
/*  50 */   private int userlanguage = 7;
/*     */ 
/*     */   
/*     */   public HrmScheduleSignImport() {}
/*     */   
/*     */   public HrmScheduleSignImport(FileUploadToPath paramFileUploadToPath) {
/*  56 */     this.keyField = paramFileUploadToPath.getParameter("keyField");
/*  57 */     this.fileName = paramFileUploadToPath.uploadFiles("excelfile");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getCellValue(HSSFCell paramHSSFCell, HSSFRow paramHSSFRow) {
/*  66 */     if (paramHSSFCell == null) return ""; 
/*  67 */     String str = "";
/*  68 */     switch (paramHSSFCell.getCellType()) {
/*     */ 
/*     */       
/*     */       case NUMERIC:
/*  72 */         if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/*  73 */           SimpleDateFormat simpleDateFormat = null;
/*  74 */           if (paramHSSFCell.getCellStyle().getDataFormat() == HSSFDataFormat.getBuiltinFormat("h:mm:ss")) {
/*  75 */             simpleDateFormat = new SimpleDateFormat("HH:mm:ss");
/*     */           } else {
/*  77 */             simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*     */           } 
/*  79 */           Date date = paramHSSFCell.getDateCellValue();
/*  80 */           str = simpleDateFormat.format(date); break;
/*     */         } 
/*  82 */         str = (new DecimalFormat("0")).format(paramHSSFCell.getNumericCellValue());
/*     */         break;
/*     */       
/*     */       case STRING:
/*  86 */         str = paramHSSFCell.getStringCellValue();
/*     */         break;
/*     */       case FORMULA:
/*  89 */         str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString();
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  96 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void ExcelToDB(FileUploadToPath paramFileUploadToPath) {
/* 104 */     RecordSet recordSet = new RecordSet();
/*     */     
/*     */     try {
/* 107 */       FileInputStream fileInputStream = new FileInputStream(this.fileName);
/* 108 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/* 109 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/* 110 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/* 112 */       fileInputStream.close();
/* 113 */       HSSFRow hSSFRow = null;
/*     */       
/* 115 */       int i = hSSFSheet.getLastRowNum();
/*     */ 
/*     */       
/* 118 */       ArrayList<HrmScheduleSign> arrayList = new ArrayList();
/*     */       
/* 120 */       HrmScheduleSign hrmScheduleSign = null;
/* 121 */       for (byte b = 1; b < i + 1; b++) {
/* 122 */         hSSFRow = hSSFSheet.getRow(b);
/* 123 */         if (ScanRow(hSSFRow)) {
/* 124 */           hrmScheduleSign = new HrmScheduleSign();
/* 125 */           String str1 = "", str2 = "";
/* 126 */           for (byte b1 = 0; b1 < hSSFRow.getLastCellNum(); b1++) {
/* 127 */             if (b1 == 0) {
/* 128 */               str1 = getCellValue(hSSFRow.getCell((short)b1), hSSFRow);
/* 129 */             } else if (b1 == 1) {
/* 130 */               str2 = getCellValue(hSSFRow.getCell((short)b1), hSSFRow);
/*     */ 
/*     */ 
/*     */             
/*     */             }
/* 135 */             else if (b1 == 2) {
/* 136 */               String str3 = Util.null2String(getCellValue(hSSFRow.getCell((short)b1), hSSFRow));
/* 137 */               SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 138 */               Calendar calendar = Calendar.getInstance();
/* 139 */               calendar.setTime(simpleDateFormat.parse(str3 + " 00:00:00"));
/* 140 */               str3 = TimeUtil.getFormartString(calendar, "yyyy-MM-dd");
/* 141 */               hrmScheduleSign.setSigndate(str3);
/* 142 */             } else if (b1 == 3) {
/* 143 */               String str3 = Util.null2String(getCellValue(hSSFRow.getCell((short)b1), hSSFRow)).trim();
/* 144 */               if (str3.length() > 0) {
/* 145 */                 String[] arrayOfString = str3.split(":");
/* 146 */                 if (arrayOfString != null && arrayOfString.length < 3) {
/* 147 */                   str3 = str3 + ":00";
/*     */                 }
/*     */               } 
/* 150 */               SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 151 */               Calendar calendar = Calendar.getInstance();
/* 152 */               calendar.setTime(simpleDateFormat.parse("1999-01-01 " + str3));
/* 153 */               str3 = TimeUtil.getFormartString(calendar, "HH:mm:ss");
/*     */               
/* 155 */               hrmScheduleSign.setSigntime(str3);
/* 156 */             } else if (b1 == 4) {
/* 157 */               String str3 = Util.null2String(getCellValue(hSSFRow.getCell((short)b1), hSSFRow));
/* 158 */               hrmScheduleSign.setClientaddress(str3);
/* 159 */             } else if (b1 == 5) {
/* 160 */               boolean bool = true;
/* 161 */               hrmScheduleSign.setIsincom(bool);
/*     */             } 
/*     */           } 
/* 164 */           if (this.keyField.equals("workcode")) {
/* 165 */             hrmScheduleSign.setUserid(getUserId(str1));
/* 166 */           } else if (this.keyField.equals("lastname")) {
/* 167 */             hrmScheduleSign.setUserid(getUserId(str2));
/*     */           } 
/*     */ 
/*     */           
/* 171 */           arrayList.add(hrmScheduleSign);
/*     */         } 
/*     */       } 
/* 174 */       HrmScheduleSign.deleteSignImp();
/* 175 */       List<HrmScheduleSign> list = null;
/* 176 */       String str = UUID.randomUUID().toString();
/* 177 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 178 */       for (HrmScheduleSign hrmScheduleSign1 : arrayList) {
/*     */         
/* 180 */         String str1 = hrmScheduleSign1.getSigndate();
/* 181 */         String str2 = hrmScheduleSign1.getSigndate();
/* 182 */         String str3 = hrmScheduleSign1.getSigntime();
/* 183 */         String str4 = hrmScheduleSign1.getClientaddress();
/* 184 */         int j = hrmScheduleSign1.getUserid();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 194 */         if (hashMap.containsKey(str1)) {
/* 195 */           list = (List)hashMap.get(str1);
/*     */         } else {
/* 197 */           list = new ArrayList();
/*     */         } 
/* 199 */         boolean bool = saveScheduleImpl(list, j, str1, str3, str4, str);
/* 200 */         if (!bool) {
/*     */           continue;
/*     */         }
/* 203 */         hashMap.put(str1, list);
/*     */       } 
/* 205 */       for (Map.Entry<Object, Object> entry : hashMap.entrySet()) {
/* 206 */         String str1 = (String)entry.getKey();
/* 207 */         String str2 = (String)entry.getKey();
/* 208 */         List<HrmScheduleSign> list1 = (List)entry.getValue();
/* 209 */         findOldSignData(list1, str1, str2, 1);
/*     */         
/* 211 */         String str3 = " delete from HrmScheduleSign where signdate >='" + str1 + "' and signdate < '" + str2 + "'";
/* 212 */         recordSet.execute(str3);
/* 213 */         saveScheduleData(list1, str1, str2, 1);
/*     */       } 
/* 215 */     } catch (Exception exception) {
/* 216 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean ScanRow(HSSFRow paramHSSFRow) {
/* 226 */     boolean bool = true;
/*     */     try {
/* 228 */       int i = 0;
/* 229 */       String str1 = "", str2 = "";
/* 230 */       for (byte b = 0; b < paramHSSFRow.getLastCellNum(); b++) {
/* 231 */         if (b == 0) {
/*     */           
/* 233 */           str1 = getCellValue(paramHSSFRow.getCell((short)b), paramHSSFRow);
/* 234 */         } else if (b == 1) {
/*     */           
/* 236 */           str2 = getCellValue(paramHSSFRow.getCell((short)b), paramHSSFRow);
/* 237 */         } else if (b == 2) {
/*     */           
/* 239 */           String str3 = Util.null2String(getCellValue(paramHSSFRow.getCell((short)b), paramHSSFRow));
/* 240 */           String str4 = Util.null2String(getCellValue(paramHSSFRow.getCell(3), paramHSSFRow)).trim();
/* 241 */           if (str3.length() == 0) {
/* 242 */             bool = false;
/*     */           }
/* 244 */           if (str4.length() == 0) {
/* 245 */             bool = false;
/*     */           }
/* 247 */           if (str4.length() > 0) {
/* 248 */             String[] arrayOfString = str4.split(":");
/* 249 */             if (arrayOfString != null && arrayOfString.length < 3) {
/* 250 */               str4 = str4 + ":00";
/*     */             }
/*     */           } 
/*     */           
/* 254 */           String str5 = str3 + " " + str4;
/* 255 */           if (str3.length() > 0 && str4.length() > 0 && checkData(str5, "datetime")) bool = false;
/*     */         
/*     */         } 
/*     */       } 
/* 259 */       if (this.keyField.equals("workcode")) {
/* 260 */         if (str1.length() == 0) bool = false; 
/* 261 */         i = getUserId(str1);
/* 262 */       } else if (this.keyField.equals("lastname")) {
/* 263 */         if (str2.length() == 0) bool = false; 
/* 264 */         i = getUserId(str2);
/*     */       } 
/* 266 */       if (i == 0)
/* 267 */         if (this.keyField.equals("workcode")) {
/* 268 */           bool = false;
/* 269 */         } else if (this.keyField.equals("lastname")) {
/* 270 */           bool = false;
/*     */         }  
/* 272 */     } catch (Exception exception) {
/* 273 */       writeLog(exception);
/*     */     } 
/* 275 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List ScanFile(FileUploadToPath paramFileUploadToPath) {
/*     */     try {
/* 284 */       FileInputStream fileInputStream = new FileInputStream(this.fileName);
/* 285 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/* 286 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/* 287 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/* 289 */       fileInputStream.close();
/* 290 */       HSSFRow hSSFRow = null;
/*     */       
/* 292 */       int i = 0;
/* 293 */       String str1 = "", str2 = "";
/* 294 */       int j = hSSFSheet.getLastRowNum();
/* 295 */       for (byte b = 1; b < j + 1; b++) {
/* 296 */         hSSFRow = hSSFSheet.getRow(b);
/* 297 */         for (byte b1 = 0; b1 < hSSFRow.getLastCellNum(); b1++) {
/* 298 */           if (b1 == 0) {
/*     */             
/* 300 */             str1 = getCellValue(hSSFRow.getCell((short)b1), hSSFRow);
/* 301 */           } else if (b1 == 1) {
/*     */             
/* 303 */             str2 = getCellValue(hSSFRow.getCell((short)b1), hSSFRow);
/* 304 */           } else if (b1 == 2) {
/*     */             
/* 306 */             HSSFCell hSSFCell1 = hSSFRow.getCell(2);
/* 307 */             String str3 = Util.null2String(getCellValue(hSSFCell1, hSSFRow));
/* 308 */             HSSFCell hSSFCell2 = hSSFRow.getCell(3);
/* 309 */             String str4 = Util.null2String(getCellValue(hSSFCell2, hSSFRow)).trim();
/*     */             
/* 311 */             if (str4.length() > 0) {
/* 312 */               String[] arrayOfString = str4.split(":");
/* 313 */               if (arrayOfString != null && arrayOfString.length < 3) {
/* 314 */                 str4 = str4 + ":00";
/*     */               }
/*     */             } 
/*     */             
/* 318 */             if (str3.length() == 0) {
/* 319 */               this.errorInfo.add(SystemEnv.getHtmlLabelName(15323, this.userlanguage) + " " + b + " " + SystemEnv.getHtmlLabelName(83762, this.userlanguage));
/*     */             }
/* 321 */             if (str4.length() == 0) {
/* 322 */               this.errorInfo.add(SystemEnv.getHtmlLabelName(15323, this.userlanguage) + " " + b + " " + SystemEnv.getHtmlLabelName(83763, this.userlanguage));
/*     */             }
/* 324 */             String str5 = str3 + " " + str4;
/* 325 */             if (str3.length() > 0 && str4.length() > 0 && checkData(str5, "datetime")) this.errorInfo.add(SystemEnv.getHtmlLabelName(15323, this.userlanguage) + " " + b + " " + SystemEnv.getHtmlLabelName(83768, this.userlanguage)); 
/*     */           } 
/*     */         } 
/* 328 */         if (this.keyField.equals("workcode")) {
/* 329 */           if (str1.length() == 0) {
/* 330 */             this.errorInfo.add(SystemEnv.getHtmlLabelName(15323, this.userlanguage) + " " + b + " " + SystemEnv.getHtmlLabelName(83770, this.userlanguage));
/*     */           } else {
/* 332 */             i = getUserId(str1);
/* 333 */             if (i == 0) {
/* 334 */               this.errorInfo.add(SystemEnv.getHtmlLabelName(15323, this.userlanguage) + " " + b + " " + SystemEnv.getHtmlLabelName(83772, this.userlanguage));
/*     */             }
/*     */           } 
/* 337 */         } else if (this.keyField.equals("lastname")) {
/* 338 */           if (str2.length() == 0) {
/* 339 */             this.errorInfo.add(SystemEnv.getHtmlLabelName(15323, this.userlanguage) + " " + b + " " + SystemEnv.getHtmlLabelName(83774, this.userlanguage));
/*     */           } else {
/* 341 */             i = getUserId(str2);
/* 342 */             if (i == 0) {
/* 343 */               this.errorInfo.add(SystemEnv.getHtmlLabelName(15323, this.userlanguage) + " " + b + " " + SystemEnv.getHtmlLabelName(83777, this.userlanguage));
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/* 348 */     } catch (Exception exception) {
/* 349 */       writeLog(exception);
/* 350 */       this.errorInfo.add(SystemEnv.getHtmlLabelName(83779, this.userlanguage));
/*     */     } 
/* 352 */     return this.errorInfo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean checkData(String paramString1, String paramString2) {
/* 362 */     boolean bool = false;
/*     */     
/* 364 */     if (paramString2.equals("int")) { 
/* 365 */       try { Integer.parseInt(paramString1); } catch (Exception exception) { bool = true; }  }
/* 366 */     else if (paramString2.equals("float")) { 
/* 367 */       try { Float.parseFloat(paramString1); } catch (Exception exception) { bool = true; }  }
/* 368 */     else if (paramString2.equals("datetime"))
/*     */     { try {
/* 370 */         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 371 */         Calendar calendar = Calendar.getInstance();
/* 372 */         calendar.setTime(simpleDateFormat.parse(paramString1));
/*     */       } catch (ParseException parseException) {
/* 374 */         bool = true;
/*     */       }  }
/* 376 */      return bool;
/*     */   }
/*     */   
/*     */   public int getUserId(String paramString) {
/* 380 */     int i = 0;
/* 381 */     String str = " select id from hrmresource ";
/* 382 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 384 */     if (paramString != null) {
/* 385 */       paramString = paramString.trim();
/*     */     }
/* 387 */     if (this.keyField.equals("lastname")) {
/* 388 */       str = str + " where lastname='" + paramString + "' ";
/* 389 */     } else if (this.keyField.equals("workcode")) {
/* 390 */       str = str + " where workcode='" + paramString + "' ";
/*     */     } 
/* 392 */     str = str + " and status in (0,1,2,3) and (accounttype is null or accounttype='' or accounttype=0)";
/* 393 */     recordSet.execute(str);
/* 394 */     if (recordSet.next()) {
/* 395 */       i = recordSet.getInt("id");
/*     */     }
/* 397 */     return i;
/*     */   }
/*     */   
/*     */   public static int getUserId(String paramString1, String paramString2) {
/* 401 */     int i = 0;
/* 402 */     String str = " select id from hrmresource ";
/* 403 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 405 */     if (paramString1 != null) {
/* 406 */       paramString1 = paramString1.trim();
/*     */     }
/* 408 */     if (paramString2.equals("lastname")) {
/* 409 */       str = str + " where lastname='" + paramString1 + "' ";
/* 410 */     } else if (paramString2.equals("workcode")) {
/* 411 */       str = str + " where workcode='" + paramString1 + "' ";
/*     */     } 
/* 413 */     str = str + " and status in (0,1,2,3) and (accounttype is null or accounttype='' or accounttype=0)";
/* 414 */     recordSet.execute(str);
/* 415 */     if (recordSet.next()) {
/* 416 */       i = recordSet.getInt("id");
/*     */     }
/* 418 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getSignType(HrmScheduleSign paramHrmScheduleSign) {
/* 427 */     String str1 = "1";
/* 428 */     String str2 = "1";
/* 429 */     String str3 = "";
/*     */     
/* 431 */     User user = User.getUser(paramHrmScheduleSign.getUserid(), 0);
/* 432 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 434 */     String str4 = paramHrmScheduleSign.getSigndate() + " " + paramHrmScheduleSign.getSigntime();
/* 435 */     String str5 = paramHrmScheduleSign.getSigndate() + " 00:00:00";
/* 436 */     String str6 = paramHrmScheduleSign.getSigndate();
/* 437 */     long l = TimeUtil.timeInterval(str4, str5);
/*     */ 
/*     */     
/* 440 */     HrmScheduleManager hrmScheduleManager = new HrmScheduleManager(user, str6, str6);
/* 441 */     boolean bool = hrmScheduleManager.isWorkday(str6);
/*     */     
/* 443 */     if (bool && l < 0L) {
/* 444 */       Calendar calendar = TimeUtil.getCalendar(str6);
/* 445 */       String str7 = Util.add0(calendar.get(1), 4);
/* 446 */       String str8 = Util.add0(calendar.get(2) + 1, 2);
/* 447 */       String str9 = Util.add0(calendar.get(5), 2);
/*     */       
/* 449 */       String str10 = str7 + "-" + str8 + "-" + str9;
/* 450 */       Map map = hrmScheduleManager.getOnDutyAndOffDutyTimeMap(str10, user.getUserSubCompany1());
/* 451 */       if (map != null) {
/* 452 */         if (map.containsKey("signType")) str2 = StringUtil.vString(map.get("signType"), "1"); 
/* 453 */         if (map.containsKey("signStartTime")) str3 = StringUtil.vString(map.get("signStartTime")); 
/*     */       } 
/* 455 */       String str11 = "SELECT 1 FROM HrmScheduleSign where userId=" + user.getUID() + " and  userType='" + user.getLogintype() + "' and signDate='" + str10 + "' and signType='1'  and isInCom='1'";
/* 456 */       if (str2.equals("2") && str3.length() > 0) {
/* 457 */         Calendar calendar1 = DateUtil.getCalendar(str6 + " " + str3 + ":00");
/* 458 */         if (TimeUtil.getCalendar(str4).after(calendar1)) {
/* 459 */           str11 = str11 + " and signTime >= '" + str3 + ":00'";
/*     */         }
/*     */       } 
/* 462 */       recordSet.executeSql(str11);
/* 463 */       if (recordSet.next()) {
/* 464 */         str1 = "2";
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 481 */     return Util.getIntValue(str1);
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean saveScheduleImpl(List<HrmScheduleSign> paramList, int paramInt, String paramString1, String paramString2, String paramString3, String paramString4) {
/* 486 */     HrmScheduleSign hrmScheduleSign = new HrmScheduleSign();
/* 487 */     if (paramInt == 0) {
/* 488 */       return false;
/*     */     }
/* 490 */     hrmScheduleSign.setUserid(paramInt);
/* 491 */     hrmScheduleSign.setUsertype(1);
/* 492 */     hrmScheduleSign.setSigndate(paramString1);
/* 493 */     hrmScheduleSign.setSigntime(paramString2);
/* 494 */     hrmScheduleSign.setClientaddress(paramString3);
/* 495 */     hrmScheduleSign.setIsincom(1);
/* 496 */     paramList.add(hrmScheduleSign);
/* 497 */     String str = UUID.randomUUID().toString();
/* 498 */     hrmScheduleSign.setUuid(str);
/* 499 */     hrmScheduleSign.setSuuid(paramString4);
/*     */     try {
/* 501 */       hrmScheduleSign.saveImp();
/* 502 */     } catch (Exception exception) {
/* 503 */       exception.printStackTrace();
/*     */     } 
/* 505 */     return true;
/*     */   }
/*     */   public void findOldSignData(List<HrmScheduleSign> paramList, String paramString1, String paramString2, int paramInt) {
/* 508 */     RecordSet recordSet = new RecordSet();
/* 509 */     String str = " select userid,usertype,signdate,signtime,clientaddress,isincom,signfrom,longitude,latitude,addr from HrmScheduleSign  where signdate>= '" + paramString1 + "' and signdate<'" + paramString2 + "' and (isimport is null  or isimport = 0 " + ((paramInt == 1) ? "" : "or isimport = 1 ") + " ) ";
/*     */     
/* 511 */     recordSet.executeSql(str);
/* 512 */     HrmScheduleSign hrmScheduleSign = null;
/* 513 */     while (recordSet.next()) {
/* 514 */       hrmScheduleSign = new HrmScheduleSign();
/* 515 */       hrmScheduleSign.setUserid(recordSet.getInt("userid"));
/* 516 */       hrmScheduleSign.setUsertype(recordSet.getInt("usertype"));
/* 517 */       hrmScheduleSign.setSigndate(recordSet.getString("signdate"));
/* 518 */       hrmScheduleSign.setSigntime(recordSet.getString("signtime"));
/* 519 */       hrmScheduleSign.setClientaddress(recordSet.getString("clientaddress"));
/* 520 */       hrmScheduleSign.setIsincom(recordSet.getInt("isincom"));
/* 521 */       hrmScheduleSign.setSignfrom(recordSet.getString("signfrom"));
/* 522 */       hrmScheduleSign.setLongitude(recordSet.getString("longitude"));
/* 523 */       hrmScheduleSign.setLatitude(recordSet.getString("latitude"));
/* 524 */       hrmScheduleSign.setAddr(recordSet.getString("addr"));
/* 525 */       hrmScheduleSign.setIsimport(0);
/* 526 */       paramList.add(hrmScheduleSign);
/*     */     } 
/*     */   }
/*     */   public void saveScheduleData(List<HrmScheduleSign> paramList, String paramString1, String paramString2, int paramInt) {
/* 530 */     Collections.sort(paramList, new Comparator<HrmScheduleSign>() {
/*     */           public int compare(HrmScheduleSign param1HrmScheduleSign1, HrmScheduleSign param1HrmScheduleSign2) {
/* 532 */             String str1 = param1HrmScheduleSign1.getSigndate() + " " + param1HrmScheduleSign1.getSigntime();
/* 533 */             String str2 = param1HrmScheduleSign2.getSigndate() + " " + param1HrmScheduleSign2.getSigntime();
/* 534 */             return (int)TimeUtil.timeInterval(str2, str1);
/*     */           }
/*     */         });
/* 537 */     HrmScheduleSign hrmScheduleSign = null;
/* 538 */     HrmScheduleImpManager hrmScheduleImpManager = new HrmScheduleImpManager(null, null, paramString1, paramString2);
/* 539 */     HrmScheduleManager hrmScheduleManager = new HrmScheduleManager();
/* 540 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 541 */     for (byte b = 0; b < paramList.size(); b++) {
/* 542 */       hrmScheduleSign = paramList.get(b);
/* 543 */       Map map = (Map)hrmScheduleImpManager.getUserDetailTimeMap().get(hrmScheduleSign.getUserid() + "");
/* 544 */       if (map != null && !map.isEmpty() && map.get(hrmScheduleSign.getSigndate()) != null && !"".equals(StringUtil.vString((String)map.get(hrmScheduleSign.getSigndate())))) {
/* 545 */         hrmScheduleManager.initUser(hrmScheduleImpManager.getUser(hrmScheduleSign.getUserid()), hrmScheduleSign.getSigndate(), hrmScheduleSign.getSigndate());
/* 546 */         boolean bool = hrmScheduleManager.isSchedulePerson();
/* 547 */         if (bool) {
/* 548 */           hrmScheduleImpManager.importSchedule(hrmScheduleSign, map);
/*     */         } else {
/* 550 */           hrmScheduleImpManager.importCommon(hrmScheduleSign, map);
/*     */         } 
/*     */       } else {
/* 553 */         hrmScheduleImpManager.importCommon(hrmScheduleSign, map);
/*     */       } 
/* 555 */       if (!hrmScheduleSign.getSigndate().equals(DateUtil.addDate(paramString1, -1)) && !hrmScheduleSign.getSigndate().equals(DateUtil.addDate(paramString2, 1)))
/*     */         
/*     */         try {
/*     */           
/* 559 */           hrmScheduleSign.save();
/* 560 */         } catch (Exception exception) {
/* 561 */           exception.printStackTrace();
/*     */         }  
/*     */     } 
/*     */   }
/*     */   
/*     */   public void importData(String paramString1, String paramString2, boolean paramBoolean) throws Exception {
/* 567 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 569 */     ArrayList<HrmScheduleSign> arrayList = new ArrayList();
/* 570 */     paramString2 = Tools.getDate(Tools.formatDate(paramString2, "yyyy-MM-dd"), 1);
/*     */ 
/*     */     
/* 573 */     HrmScheduleSign.delete(paramString1, paramString2);
/*     */ 
/*     */     
/* 576 */     HrmScheduleSign.deleteSignImp();
/*     */     
/* 578 */     String str1 = "";
/* 579 */     String str2 = " select datasourceid, workcode, lastname, signdate, signtime, tablename, clientaddress from HrmScheduleSignSet ";
/*     */ 
/*     */     
/* 582 */     String str3 = UUID.randomUUID().toString();
/* 583 */     recordSet.execute(str2);
/* 584 */     while (recordSet.next()) {
/* 585 */       String str4 = Util.null2String(recordSet.getString("datasourceid")).trim();
/* 586 */       String str5 = Util.null2String(recordSet.getString("workcode")).trim();
/* 587 */       String str6 = Util.null2String(recordSet.getString("lastname")).trim();
/* 588 */       String str7 = Util.null2String(recordSet.getString("signdate")).trim();
/* 589 */       String str8 = Util.null2String(recordSet.getString("signtime")).trim();
/* 590 */       String str9 = Util.null2String(recordSet.getString("tablename")).trim();
/* 591 */       String str10 = Util.null2String(recordSet.getString("clientaddress")).trim();
/*     */       
/* 593 */       DataSource dataSource = null;
/* 594 */       Connection connection = null;
/*     */       try {
/* 596 */         dataSource = (DataSource)StaticObj.getServiceByFullname("datasource." + str4, DataSource.class);
/* 597 */         connection = dataSource.getConnection();
/* 598 */         connection.setAutoCommit(true);
/* 599 */         PreparedStatement preparedStatement = null;
/* 600 */         ResultSet resultSet = null;
/*     */         
/* 602 */         String str11 = "";
/* 603 */         String str12 = "";
/* 604 */         String str13 = "";
/* 605 */         String str14 = "";
/* 606 */         String str15 = "";
/* 607 */         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm:ss");
/*     */         
/* 609 */         str1 = "select * from " + str9 + " where " + str7 + " >= '" + DateUtil.addDate(paramString1, -1) + "' and " + str7 + " < '" + paramString2 + "' order by " + str7 + ((str8.length() == 0 || str8.equalsIgnoreCase(str7)) ? "" : (", " + str8));
/* 610 */         preparedStatement = connection.prepareStatement(str1);
/* 611 */         resultSet = preparedStatement.executeQuery();
/*     */         
/* 613 */         while (resultSet.next()) {
/* 614 */           if (str5.length() > 0) str11 = Util.null2String(resultSet.getString(str5)).trim(); 
/* 615 */           if (str6.length() > 0) str12 = Util.null2String(resultSet.getString(str6)).trim(); 
/* 616 */           if (str7.length() > 0) str13 = Util.null2String(resultSet.getString(str7)).trim(); 
/* 617 */           if (str8.length() > 0) {
/* 618 */             str14 = Util.null2String(resultSet.getString(str8)).trim();
/*     */             
/* 620 */             if (str14.length() > 8) {
/* 621 */               str14 = simpleDateFormat.format(Timestamp.valueOf(str14));
/* 622 */             } else if (str14.length() < 8) {
/*     */               
/* 624 */               str14 = str14 + ":00";
/*     */             } 
/*     */           } else {
/*     */             
/* 628 */             str14 = "";
/*     */           } 
/*     */           
/* 631 */           if (str10.length() > 0) str15 = Tools.vString(resultSet.getString(str10));
/*     */           
/* 633 */           if (str13.length() > 10) {
/*     */             
/* 635 */             if (str13.length() == 16)
/*     */             {
/* 637 */               str13 = str13 + ":00";
/*     */             }
/* 639 */             if (str14.length() == 0) {
/* 640 */               str14 = simpleDateFormat.format(Timestamp.valueOf(str13));
/*     */             }
/* 642 */             str13 = Tools.formatDate(str13, "yyyy-MM-dd");
/*     */           } 
/*     */ 
/*     */           
/* 646 */           if (str14.length() <= 5) {
/* 647 */             str14 = str14 + ":00";
/*     */           }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 654 */           int i = 0;
/* 655 */           if (Util.null2String(str11).length() > 0) {
/* 656 */             i = getUserId(str11, "workcode");
/*     */           } else {
/* 658 */             i = getUserId(str12, "lastname");
/*     */           } 
/* 660 */           boolean bool = saveScheduleImpl(arrayList, i, str13, str14, str15, str3);
/* 661 */           if (!bool);
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 669 */         resultSet.close();
/* 670 */         preparedStatement.close();
/*     */       }
/* 672 */       catch (Exception exception) {
/*     */         
/* 674 */         writeLog(exception);
/*     */       } finally {
/*     */         
/* 677 */         if (connection != null) connection.close();
/*     */       
/*     */       } 
/*     */     } 
/* 681 */     findOldSignData(arrayList, paramString1, paramString2, 1);
/*     */ 
/*     */     
/* 684 */     str2 = " delete from HrmScheduleSign where signdate >='" + paramString1 + "' and signdate < '" + paramString2 + "'";
/* 685 */     recordSet.execute(str2);
/* 686 */     saveScheduleData(arrayList, paramString1, paramString2, 1);
/*     */   }
/*     */   
/*     */   public int getUserlanguage() {
/* 690 */     return this.userlanguage;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setUserlanguage(int paramInt) {
/* 695 */     this.userlanguage = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmScheduleSignImport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */