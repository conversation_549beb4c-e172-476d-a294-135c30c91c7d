/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.common.database.dialect.DbDialectFactory;
/*     */ import weaver.hrm.common.database.dialect.DialectUtil;
/*     */ import weaver.hrm.common.database.dialect.IDbDialectSql;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSignManager
/*     */   extends BaseBean
/*     */ {
/*  37 */   private User user = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  44 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsInCom(String paramString) {
/*  56 */     if (paramString == null || paramString.trim().equals("")) {
/*  57 */       return "0";
/*     */     }
/*     */     
/*  60 */     paramString = paramString.trim();
/*     */     
/*  62 */     String str1 = "0";
/*     */     
/*  64 */     String str2 = "";
/*  65 */     RecordSet recordSet = new RecordSet();
/*  66 */     boolean bool = HrmScheduleKqUtil.hasHrmSchedule(this.user);
/*  67 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  68 */     if (bool) {
/*  69 */       Map map = HrmScheduleKqUtil.getHrmScheduleInfo(this.user);
/*  70 */       str2 = (String)map.get("signIpScope");
/*     */     } else {
/*  72 */       recordSet.executeSql("select signIpScope from HrmkqSystemSet");
/*  73 */       if (recordSet.next()) {
/*  74 */         str2 = Util.null2String(recordSet.getString(1));
/*     */       }
/*     */     } 
/*  77 */     if (str2.trim().equals("")) {
/*  78 */       return "0";
/*     */     }
/*     */     
/*  81 */     ArrayList<String> arrayList = Util.TokenizerString(str2, ";");
/*  82 */     String str3 = "";
/*  83 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  84 */       str3 = Util.null2String(arrayList.get(b));
/*  85 */       str3 = str3.trim();
/*     */       
/*  87 */       if (str3.indexOf("*") > -1) {
/*  88 */         ArrayList<String> arrayList1 = Util.TokenizerString(str3, ".");
/*  89 */         ArrayList<String> arrayList2 = Util.TokenizerString(paramString, ".");
/*  90 */         if (arrayList1.size() == 4 && arrayList2
/*  91 */           .size() == 4 && ("*"
/*  92 */           .equals(arrayList1.get(0)) || 
/*  93 */           Util.null2String(arrayList2.get(0)).equals(arrayList1.get(0))) && ("*"
/*  94 */           .equals(arrayList1.get(1)) || 
/*  95 */           Util.null2String(arrayList2.get(1)).equals(arrayList1.get(1))) && ("*"
/*  96 */           .equals(arrayList1.get(2)) || 
/*  97 */           Util.null2String(arrayList2.get(2)).equals(arrayList1.get(2))) && ("*"
/*  98 */           .equals(arrayList1.get(3)) || 
/*  99 */           Util.null2String(arrayList2.get(3)).equals(arrayList1.get(2)))) {
/*     */           
/* 101 */           str1 = "1";
/*     */           
/*     */           break;
/*     */         } 
/* 105 */       } else if (str3.indexOf("-") > -1) {
/* 106 */         String str4 = str3.substring(0, str3.indexOf("-"));
/* 107 */         str4 = Util.null2String(str4).trim();
/* 108 */         String str5 = "";
/* 109 */         if (!str3.endsWith("-")) {
/* 110 */           str5 = str3.substring(str3.indexOf("-") + 1);
/*     */         }
/* 112 */         str5 = Util.null2String(str5).trim();
/*     */ 
/*     */         
/* 115 */         String str6 = addToThreeFigure(paramString, "clientAddress");
/* 116 */         str4 = addToThreeFigure(str4, "signIpFrom");
/* 117 */         str5 = addToThreeFigure(str5, "signIpTo");
/*     */         
/* 119 */         if (str4.equals("") && str5.equals("")) {
/* 120 */           str1 = "1";
/*     */           
/*     */           break;
/*     */         } 
/* 124 */         if (str4.equals("") && !str5.equals("") && 
/* 125 */           str6.compareTo(str5) <= 0) {
/* 126 */           str1 = "1";
/*     */           
/*     */           break;
/*     */         } 
/*     */         
/* 131 */         if (!str4.equals("") && str5.equals("") && 
/* 132 */           str6.compareTo(str4) >= 0) {
/* 133 */           str1 = "1";
/*     */           
/*     */           break;
/*     */         } 
/*     */         
/* 138 */         if (!str4.equals("") && !str5.equals("") && 
/* 139 */           str6.compareTo(str4) >= 0 && str6.compareTo(str5) <= 0) {
/* 140 */           str1 = "1";
/*     */ 
/*     */ 
/*     */           
/*     */           break;
/*     */         } 
/* 146 */       } else if (paramString.equals(str3)) {
/* 147 */         str1 = "1";
/*     */ 
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*     */     
/* 154 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getIsAbnormal(int paramInt, String paramString1, String paramString2) {
/* 167 */     boolean bool1 = true;
/*     */ 
/*     */     
/* 170 */     if (paramInt <= 0 || paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/* 171 */       .trim().length() != 10) {
/* 172 */       return bool1;
/*     */     }
/*     */ 
/*     */     
/* 176 */     String str1 = "";
/* 177 */     String str2 = "";
/* 178 */     RecordSet recordSet = new RecordSet();
/* 179 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 181 */     if ("oracle".equals(recordSet.getDBType())) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 193 */       stringBuffer.append(" select TempTable.* ")
/* 194 */         .append(" from ")
/* 195 */         .append(" ( ")
/* 196 */         .append(" select  signDate,signTime")
/* 197 */         .append("  from HrmScheduleSign ")
/* 198 */         .append(" where userid= ").append(paramInt)
/* 199 */         .append("   and signType='1' ")
/* 200 */         .append("   and userType='").append(paramString1).append("' ")
/* 201 */         .append("   and isInCom='1' ")
/* 202 */         .append("   and signDate<'").append(paramString2).append("' ")
/* 203 */         .append(" order by signDate desc ")
/* 204 */         .append(" )TempTable ")
/* 205 */         .append(" where rowNum=1 ");
/*     */     }
/* 207 */     else if (DialectUtil.isMySql(recordSet.getDBType())) {
/* 208 */       stringBuffer.append(" select t.signDate,t.signTime ")
/* 209 */         .append(" from ")
/* 210 */         .append(" ( ")
/* 211 */         .append(" select  signDate,signTime,@r:=@r+1 as _rownum ")
/* 212 */         .append("  from HrmScheduleSign,(select @r:=0) vars ")
/* 213 */         .append(" where userid= ").append(paramInt)
/* 214 */         .append("   and signType='1' ")
/* 215 */         .append("   and userType='").append(paramString1).append("' ")
/* 216 */         .append("   and isInCom='1' ")
/* 217 */         .append("   and signDate<'").append(paramString2).append("' ")
/* 218 */         .append(" order by signDate desc ")
/* 219 */         .append(" ) t ")
/* 220 */         .append(" where _rownum=1 ");
/*     */     } else {
/*     */       
/* 223 */       stringBuffer.append(" select top 1 signDate,signTime  ")
/* 224 */         .append("  from HrmScheduleSign ")
/* 225 */         .append(" where userid= ").append(paramInt)
/* 226 */         .append("   and signType='1' ")
/* 227 */         .append("   and userType='").append(paramString1).append("' ")
/* 228 */         .append("   and isInCom='1' ")
/* 229 */         .append("   and signDate<'").append(paramString2).append("' ")
/* 230 */         .append(" order by signDate desc ");
/*     */     } 
/*     */     
/* 233 */     recordSet.executeSql(stringBuffer.toString());
/* 234 */     if (recordSet.next()) {
/* 235 */       str1 = Util.null2String(recordSet.getString("signDate"));
/* 236 */       str2 = Util.null2String(recordSet.getString("signTime"));
/* 237 */       str1 = str1.trim();
/* 238 */       str2 = str2.trim();
/*     */     } 
/*     */     
/* 241 */     if (str1.length() != 10) {
/* 242 */       bool1 = false;
/* 243 */       return bool1;
/*     */     } 
/*     */ 
/*     */     
/* 247 */     String str3 = TimeUtil.dateAdd(paramString2, -1);
/* 248 */     String str4 = "";
/* 249 */     boolean bool2 = false;
/* 250 */     boolean bool = false;
/* 251 */     HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 252 */     hrmScheduleDiffUtil.setUser(this.user);
/*     */     
/* 254 */     while (!bool2) {
/*     */       
/* 256 */       if (str3.equals(str1)) {
/* 257 */         bool2 = true;
/*     */       }
/*     */       
/* 260 */       bool = hrmScheduleDiffUtil.getIsWorkday(str3);
/*     */ 
/*     */       
/* 263 */       if (bool && !str3.equals(str1)) {
/* 264 */         bool1 = true;
/* 265 */         return bool1;
/*     */       } 
/*     */ 
/*     */       
/* 269 */       str4 = TimeUtil.dateAdd(str3, -1);
/* 270 */       str3 = str4;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 276 */     if (!bool) {
/* 277 */       bool1 = false;
/* 278 */       return bool1;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 284 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 285 */       String str5 = resourceComInfo.getDepartmentID("" + paramInt);
/* 286 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 287 */       String str6 = departmentComInfo.getSubcompanyid1(str5);
/* 288 */       Map map = hrmScheduleDiffUtil.getOnDutyAndOffDutyTimeMap(str1, Util.getIntValue(str6, 0));
/*     */       
/* 290 */       String str7 = Util.null2String((String)map.get("onDutyTimeAM"));
/* 291 */       String str8 = Util.null2String((String)map.get("offDutyTimePM"));
/*     */ 
/*     */       
/* 294 */       String str9 = "";
/* 295 */       stringBuffer = new StringBuffer();
/* 296 */       if ("oracle".equals(recordSet.getDBType())) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 308 */         stringBuffer.append(" select TempTable.* ")
/* 309 */           .append(" from ")
/* 310 */           .append(" ( ")
/* 311 */           .append(" select signTime ")
/* 312 */           .append("  from HrmScheduleSign ")
/* 313 */           .append(" where userid= ").append(paramInt)
/* 314 */           .append("   and signDate='").append(str1).append("' ")
/* 315 */           .append("   and signType='2' ")
/* 316 */           .append("   and userType='").append(paramString1).append("' ")
/* 317 */           .append("   and isInCom='1' ")
/* 318 */           .append(" order by signTime desc ")
/* 319 */           .append(" )TempTable ")
/* 320 */           .append(" where rowNum=1 ");
/*     */       }
/* 322 */       else if (DialectUtil.isMySql(recordSet.getDBType())) {
/* 323 */         stringBuffer.append(" select t.signTime ")
/* 324 */           .append(" from ")
/* 325 */           .append(" ( ")
/* 326 */           .append(" select  signTime,@r:=@r+1 as _rownum ")
/* 327 */           .append("  from HrmScheduleSign,(select @r:=0) vars ")
/* 328 */           .append(" where userid= ").append(paramInt)
/* 329 */           .append("   and signDate='").append(str1).append("' ")
/* 330 */           .append("   and signType='2' ")
/* 331 */           .append("   and userType='").append(paramString1).append("' ")
/* 332 */           .append("   and isInCom='1' ")
/* 333 */           .append(" order by signTime desc ")
/* 334 */           .append(" ) t ")
/* 335 */           .append(" where _rownum=1 ");
/*     */       } else {
/*     */         
/* 338 */         stringBuffer.append(" select top 1 signTime  ")
/* 339 */           .append("  from HrmScheduleSign ")
/* 340 */           .append(" where userid= ").append(paramInt)
/* 341 */           .append("   and signDate='").append(str1).append("' ")
/* 342 */           .append("   and signType='2' ")
/* 343 */           .append("   and userType='").append(paramString1).append("' ")
/* 344 */           .append("   and isInCom='1' ")
/* 345 */           .append(" order by signTime desc ");
/*     */       } 
/*     */       
/* 348 */       recordSet.executeSql(stringBuffer.toString());
/* 349 */       if (recordSet.next()) {
/* 350 */         str9 = Util.null2String(recordSet.getString("signTime"));
/* 351 */         str9 = str9.trim();
/*     */       } 
/*     */       
/* 354 */       if (str9.equals("")) {
/* 355 */         str9 = str2;
/*     */       }
/*     */ 
/*     */       
/* 359 */       if (str2.compareTo(str7 + ":00") <= 0 && str9.compareTo(str8 + ":00") >= 0) {
/* 360 */         bool1 = false;
/* 361 */         return bool1;
/*     */       } 
/*     */ 
/*     */       
/* 365 */       String str10 = getNextMinOfSignOnTime(str1 + " " + str2);
/* 366 */       String str11 = getLastMinOfSignOutTime(str1 + " " + str9);
/*     */       
/* 368 */       stringBuffer = new StringBuffer();
/* 369 */       stringBuffer.append(" select 1 ")
/* 370 */         .append("   from Workflow_Requestbase a,Bill_BoHaiLeave b ")
/* 371 */         .append("  where a.requestId=b.requestId ")
/* 372 */         .append("    and a.currentNodeType='3' ")
/* 373 */         .append("    and b.resourceId=").append(paramInt);
/*     */       
/* 375 */       if (recordSet.getDBType().equals("oracle")) {
/* 376 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 377 */           stringBuffer.append("    and concat(concat(fromDate,' '),fromTime)<='").append(str1).append(" ").append(str7).append("' ")
/* 378 */             .append("    and concat(concat(toDate,' '),toTime)>='").append(str10).append("' ");
/*     */         }
/*     */         
/* 381 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 382 */           stringBuffer.append("    and concat(concat(fromDate,' '),fromTime)<='").append(str11).append("' ")
/* 383 */             .append("    and concat(concat(toDate,' '),toTime)>='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/* 385 */       } else if (DialectUtil.isMySql(recordSet.getDBType())) {
/* 386 */         IDbDialectSql iDbDialectSql = DbDialectFactory.get(recordSet.getDBType());
/* 387 */         String str12 = iDbDialectSql.concatStr("fromDate", new String[] { "' '", "fromTime" });
/* 388 */         String str13 = iDbDialectSql.concatStr("toDate", new String[] { "' '", "toTime" });
/* 389 */         if (str2.compareTo(str7 + ":00") > 0)
/*     */         {
/* 391 */           stringBuffer.append("    and " + str12 + "<='").append(str1).append(" ").append(str7).append("' ")
/* 392 */             .append("    and " + str13 + ">='").append(str10).append("' ");
/*     */         }
/*     */         
/* 395 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 396 */           stringBuffer.append("    and " + str12 + "<='").append(str11).append("' ")
/* 397 */             .append("    and " + str13 + ">='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/*     */       } else {
/* 400 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 401 */           stringBuffer.append("    and fromDate+' '+fromTime<='").append(str1).append(" ").append(str7).append("' ")
/* 402 */             .append("    and toDate+' '+toTime>='").append(str10).append("' ");
/*     */         }
/*     */         
/* 405 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 406 */           stringBuffer.append("    and fromDate+' '+fromTime<='").append(str11).append("' ")
/* 407 */             .append("    and toDate+' '+toTime>='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 412 */       recordSet.executeSql(stringBuffer.toString());
/* 413 */       if (recordSet.next()) {
/* 414 */         bool1 = false;
/* 415 */         return bool1;
/*     */       } 
/*     */ 
/*     */       
/* 419 */       stringBuffer = new StringBuffer();
/* 420 */       stringBuffer.append(" select 1 ")
/* 421 */         .append("   from Workflow_Requestbase a,Bill_BoHaiEvection b ")
/* 422 */         .append("  where a.requestId=b.requestId ")
/* 423 */         .append("    and a.currentNodeType='3' ")
/* 424 */         .append("    and b.resourceId=").append(paramInt);
/*     */       
/* 426 */       if (recordSet.getDBType().equals("oracle")) {
/* 427 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 428 */           stringBuffer.append("    and concat(concat(fromDate,' '),fromTime)<='").append(str1).append(" ").append(str7).append("' ")
/* 429 */             .append("    and concat(concat(toDate,' '),toTime)>='").append(str10).append("' ");
/*     */         }
/*     */         
/* 432 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 433 */           stringBuffer.append("    and concat(concat(fromDate,' '),fromTime)<='").append(str11).append("' ")
/* 434 */             .append("    and concat(concat(toDate,' '),toTime)>='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/* 436 */       } else if (DialectUtil.isMySql(recordSet.getDBType())) {
/* 437 */         IDbDialectSql iDbDialectSql = DbDialectFactory.get(recordSet.getDBType());
/* 438 */         String str12 = iDbDialectSql.concatStr("fromDate", new String[] { "' '", "fromTime" });
/* 439 */         String str13 = iDbDialectSql.concatStr("toDate", new String[] { "' '", "toTime" });
/* 440 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 441 */           stringBuffer.append("    and " + str12 + "<='").append(str1).append(" ").append(str7).append("' ")
/* 442 */             .append("    and " + str13 + ">='").append(str10).append("' ");
/*     */         }
/*     */         
/* 445 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 446 */           stringBuffer.append("    and " + str12 + "<='").append(str11).append("' ")
/* 447 */             .append("    and " + str13 + ">='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/*     */       } else {
/* 450 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 451 */           stringBuffer.append("    and fromDate+' '+fromTime<='").append(str1).append(" ").append(str7).append("' ")
/* 452 */             .append("    and toDate+' '+toTime>='").append(str10).append("' ");
/*     */         }
/*     */         
/* 455 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 456 */           stringBuffer.append("    and fromDate+' '+fromTime<='").append(str11).append("' ")
/* 457 */             .append("    and toDate+' '+toTime>='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 462 */       recordSet.executeSql(stringBuffer.toString());
/* 463 */       if (recordSet.next()) {
/* 464 */         bool1 = false;
/* 465 */         return bool1;
/*     */       } 
/*     */       
/* 468 */       stringBuffer = new StringBuffer();
/* 469 */       stringBuffer.append(" select 1 ")
/* 470 */         .append("   from Workflow_Requestbase a,Bill_BoHaiOut b ")
/* 471 */         .append("  where a.requestId=b.requestId ")
/* 472 */         .append("    and a.currentNodeType='3' ")
/* 473 */         .append("    and b.resourceId=").append(paramInt);
/*     */       
/* 475 */       if (recordSet.getDBType().equals("oracle")) {
/* 476 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 477 */           stringBuffer.append("    and concat(concat(fromDate,' '),fromTime)<='").append(str1).append(" ").append(str7).append("' ")
/* 478 */             .append("    and concat(concat(toDate,' '),toTime)>='").append(str10).append("' ");
/*     */         }
/*     */         
/* 481 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 482 */           stringBuffer.append("    and concat(concat(fromDate,' '),fromTime)<='").append(str11).append("' ")
/* 483 */             .append("    and concat(concat(toDate,' '),toTime)>='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/* 485 */       } else if (DialectUtil.isMySql(recordSet.getDBType())) {
/* 486 */         IDbDialectSql iDbDialectSql = DbDialectFactory.get(recordSet.getDBType());
/* 487 */         String str12 = iDbDialectSql.concatStr("fromDate", new String[] { "' '", "fromTime" });
/* 488 */         String str13 = iDbDialectSql.concatStr("toDate", new String[] { "' '", "toTime" });
/* 489 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 490 */           stringBuffer.append("    and " + str12 + "<='").append(str1).append(" ").append(str7).append("' ")
/* 491 */             .append("    and " + str13 + ">='").append(str10).append("' ");
/*     */         }
/*     */         
/* 494 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 495 */           stringBuffer.append("    and " + str12 + "<='").append(str11).append("' ")
/* 496 */             .append("    and " + str13 + ">='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/*     */       } else {
/* 499 */         if (str2.compareTo(str7 + ":00") > 0) {
/* 500 */           stringBuffer.append("    and fromDate+' '+fromTime<='").append(str1).append(" ").append(str7).append("' ")
/* 501 */             .append("    and toDate+' '+toTime>='").append(str10).append("' ");
/*     */         }
/*     */         
/* 504 */         if (str9.compareTo(str8 + ":00") < 0) {
/* 505 */           stringBuffer.append("    and fromDate+' '+fromTime<='").append(str11).append("' ")
/* 506 */             .append("    and toDate+' '+toTime>='").append(str1).append(" ").append(str8).append("' ");
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 511 */       recordSet.executeSql(stringBuffer.toString());
/* 512 */       if (recordSet.next()) {
/* 513 */         bool1 = false;
/* 514 */         return bool1;
/*     */       } 
/*     */       
/* 517 */       return bool1;
/* 518 */     } catch (Exception exception) {
/* 519 */       return bool1;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getNextMinOfSignOnTime(String paramString) {
/* 535 */     String str1 = "";
/*     */     
/* 537 */     if (paramString == null || paramString.trim().equals("") || paramString
/* 538 */       .length() < 19) {
/* 539 */       return str1;
/*     */     }
/*     */     
/* 542 */     String str2 = paramString.substring(17);
/*     */     
/* 544 */     if (str2.equals("00")) {
/* 545 */       str1 = paramString.substring(0, 16);
/*     */     } else {
/* 547 */       paramString = TimeUtil.timeAdd(paramString, 60);
/* 548 */       str1 = paramString.substring(0, 16);
/*     */     } 
/*     */     
/* 551 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getLastMinOfSignOutTime(String paramString) {
/* 566 */     String str = "";
/*     */     
/* 568 */     if (paramString == null || paramString.trim().equals("") || paramString
/* 569 */       .length() < 19) {
/* 570 */       return str;
/*     */     }
/*     */     
/* 573 */     str = paramString.substring(0, 16);
/*     */ 
/*     */     
/* 576 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String addToThreeFigure(String paramString1, String paramString2) {
/* 591 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/* 592 */       .trim().equals(""))
/*     */     {
/* 594 */       return "";
/*     */     }
/*     */     
/* 597 */     String str1 = "";
/*     */     
/* 599 */     ArrayList<String> arrayList = Util.TokenizerString(paramString1, ".");
/* 600 */     String str2 = null;
/*     */     
/* 602 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 603 */       str2 = arrayList.get(b);
/*     */       
/* 605 */       if (paramString2.equals("signIpTo")) {
/* 606 */         str1 = str1 + "." + Util.add0(Util.getIntValue(str2, 255), 3);
/*     */       } else {
/* 608 */         str1 = str1 + "." + Util.add0(Util.getIntValue(str2, 0), 3);
/*     */       } 
/*     */     } 
/*     */     
/* 612 */     if (!str1.equals("")) {
/* 613 */       str1 = str1.substring(1);
/*     */     }
/*     */     
/* 616 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmScheduleSignManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */