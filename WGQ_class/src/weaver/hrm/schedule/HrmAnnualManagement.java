/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmAnnualManagement
/*     */ {
/*     */   public static float getAnnualDays(HashMap paramHashMap, int[] paramArrayOfint, int paramInt) {
/*  25 */     float f = -1.0F;
/*  26 */     int i = -1;
/*     */     
/*  28 */     for (byte b = 0; b < paramArrayOfint.length; b++) {
/*  29 */       if (paramArrayOfint[b] <= paramInt) {
/*  30 */         i = paramArrayOfint[b];
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*  35 */     if (i > -1) {
/*  36 */       f = Util.getFloatValue(Util.null2String((String)paramHashMap.get(i + "")), 0.0F);
/*     */     }
/*     */     
/*  39 */     return f;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBatchProcess(String paramString1, String paramString2) throws Exception {
/*  51 */     RecordSet recordSet = new RecordSet();
/*  52 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */     
/*  54 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  55 */     recordSet.executeSql("select distinct subcompanyid from HrmAnnualBatchProcess");
/*  56 */     while (recordSet.next()) {
/*  57 */       hashMap.put(recordSet.getString("subcompanyid"), recordSet.getString("subcompanyid"));
/*     */     }
/*     */     
/*  60 */     String str = paramString1 + "," + subCompanyComInfo.getAllSupCompany(paramString1) + "0";
/*  61 */     String[] arrayOfString = Util.TokenizerString2(str, ",");
/*  62 */     if (arrayOfString != null) {
/*  63 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  64 */         if (hashMap.containsKey(arrayOfString[b])) return arrayOfString[b];
/*     */       
/*     */       } 
/*     */     }
/*  68 */     return "-1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getUserAannualInfo(String paramString1, String paramString2) throws Exception {
/*  78 */     DecimalFormat decimalFormat = new DecimalFormat("0.##");
/*  79 */     RecordSet recordSet = new RecordSet();
/*  80 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  81 */     String str1 = "";
/*  82 */     String str2 = "";
/*  83 */     String str3 = "";
/*  84 */     String str4 = "";
/*  85 */     String str5 = "";
/*  86 */     float f = 0.0F;
/*  87 */     if (Util.null2String(paramString2).trim().length() < 10) {
/*  88 */       paramString2 = TimeUtil.getCurrentDateString();
/*     */     }
/*  90 */     String str6 = paramString2.substring(0, 4);
/*  91 */     String str7 = (Util.getIntValue(str6) - 1) + "";
/*  92 */     String str8 = resourceComInfo.getSubCompanyID(paramString1);
/*     */ 
/*     */     
/*  95 */     str5 = getAnnualPeriod(str8, str6);
/*  96 */     str3 = Util.TokenizerString2(str5, "#")[0];
/*  97 */     str4 = Util.TokenizerString2(str5, "#")[1];
/*  98 */     if (str3.compareTo(paramString2) < 1 && str4.compareTo(paramString2) > -1) {
/*  99 */       str1 = "select * from hrmannualmanagement where resourceid = '" + paramString1 + "' and annualyear = '" + str6 + "' and status <> 0 ";
/* 100 */       recordSet.executeSql(str1);
/* 101 */       if (recordSet.next()) {
/* 102 */         str2 = "" + recordSet.getFloat("annualdays");
/* 103 */         f = recordSet.getFloat("annualdays");
/*     */       } else {
/* 105 */         str2 = "0.0";
/*     */       } 
/*     */     } else {
/* 108 */       str2 = "0.0";
/*     */     } 
/*     */ 
/*     */     
/* 112 */     str5 = getAnnualPeriod(str8, str7);
/* 113 */     str3 = Util.TokenizerString2(str5, "#")[0];
/* 114 */     str4 = Util.TokenizerString2(str5, "#")[1];
/* 115 */     if (str3.compareTo(paramString2) < 1 && str4.compareTo(paramString2) > -1) {
/* 116 */       str1 = "select * from hrmannualmanagement where resourceid = '" + paramString1 + "' and annualyear = '" + str7 + "' and status <> 0 ";
/* 117 */       recordSet.executeSql(str1);
/* 118 */       if (recordSet.next()) {
/* 119 */         str2 = str2 + "#" + recordSet.getFloat("annualdays");
/* 120 */         f += recordSet.getFloat("annualdays");
/*     */       } else {
/* 122 */         str2 = str2 + "#0.0";
/*     */       } 
/*     */     } else {
/* 125 */       str2 = str2 + "#0.0";
/*     */     } 
/* 127 */     str2 = str2 + "#" + decimalFormat.format(f);
/*     */     
/* 129 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAnnualPeriod(String paramString1, String paramString2) throws Exception {
/* 140 */     String str1 = "";
/* 141 */     String str2 = "";
/* 142 */     RecordSet recordSet = new RecordSet();
/* 143 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*     */     
/* 145 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 146 */     str1 = "select distinct subcompanyid from HrmAnnualPeriod where annualyear = '" + paramString2 + "'";
/* 147 */     recordSet.executeSql(str1);
/* 148 */     while (recordSet.next()) {
/* 149 */       hashMap.put(recordSet.getString("subcompanyid"), recordSet.getString("subcompanyid"));
/*     */     }
/*     */     
/* 152 */     String str3 = paramString1 + "," + subCompanyComInfo.getAllSupCompany(paramString1) + "0";
/*     */     
/* 154 */     String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 155 */     if (arrayOfString != null) {
/* 156 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 157 */         if (hashMap.containsKey(arrayOfString[b])) {
/* 158 */           str2 = arrayOfString[b];
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     }
/* 164 */     if (!str2.equals("")) {
/* 165 */       str1 = "select * from HrmAnnualPeriod where annualyear = '" + paramString2 + "' and subcompanyid = '" + str2 + "'";
/* 166 */       recordSet.executeSql(str1);
/* 167 */       if (recordSet.next()) {
/* 168 */         return recordSet.getString("startdate") + "#" + recordSet.getString("enddate");
/*     */       }
/*     */     } 
/*     */     
/* 172 */     return paramString2 + "-01-01#" + (Util.getIntValue(paramString2) + 1) + "-12-31";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLeaveColor(String paramString) throws Exception {
/* 182 */     String str1 = "";
/* 183 */     String str2 = "";
/* 184 */     RecordSet recordSet = new RecordSet();
/* 185 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 186 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 187 */     str1 = "select distinct subcompanyid from hrmleavetypecolor";
/* 188 */     recordSet.executeSql(str1);
/* 189 */     while (recordSet.next()) {
/* 190 */       hashMap.put(recordSet.getString("subcompanyid"), recordSet.getString("subcompanyid"));
/*     */     }
/*     */     
/* 193 */     String str3 = paramString + "," + subCompanyComInfo.getAllSupCompany(paramString) + "0";
/*     */     
/* 195 */     String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 196 */     if (arrayOfString != null) {
/* 197 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 198 */         if (hashMap.containsKey(arrayOfString[b])) {
/* 199 */           str2 = arrayOfString[b];
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     }
/* 205 */     return str2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmAnnualManagement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */