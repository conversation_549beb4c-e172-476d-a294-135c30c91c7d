/*     */ package weaver.hrm.schedule.controller;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.json.JSONObject;
/*     */ import weaver.common.DataBook;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseController;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleShiftsSetCache;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsSet;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsDetailManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsSetManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleShiftsSetControllerE9
/*     */   extends BaseController<HrmScheduleShiftsSet>
/*     */ {
/*  28 */   private HrmScheduleShiftsSetManager manager = null;
/*  29 */   private HrmScheduleShiftsDetailManager detailManager = null;
/*     */   
/*     */   public HrmScheduleShiftsSetControllerE9() {
/*  32 */     this.manager = new HrmScheduleShiftsSetManager();
/*  33 */     this.detailManager = new HrmScheduleShiftsDetailManager();
/*  34 */     setManager((BaseManager)this.manager);
/*     */   }
/*     */   
/*     */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  38 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  39 */     this.detailManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  43 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/*  44 */     JSONObject jSONObject = new JSONObject();
/*  45 */     boolean bool = true;
/*  46 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/*  47 */     if (str.equals("save")) {
/*  48 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/*  49 */       HrmScheduleShiftsSet hrmScheduleShiftsSet = (long_.longValue() != 0L) ? (HrmScheduleShiftsSet)this.manager.get(long_) : null;
/*  50 */       boolean bool1 = (hrmScheduleShiftsSet == null) ? true : false;
/*  51 */       if (bool1) {  } else {  }  hrmScheduleShiftsSet = hrmScheduleShiftsSet;
/*  52 */       hrmScheduleShiftsSet.setSn(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("sn"))));
/*  53 */       hrmScheduleShiftsSet.setField001(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field001")));
/*  54 */       hrmScheduleShiftsSet.setField002(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field002"))));
/*  55 */       hrmScheduleShiftsSet.setField003(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field003"))));
/*  56 */       hrmScheduleShiftsSet.setField004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field004"))));
/*  57 */       hrmScheduleShiftsSet.setField005(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field005"))));
/*  58 */       hrmScheduleShiftsSet.setField006(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field006"))));
/*  59 */       hrmScheduleShiftsSet.setField007(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field007")));
/*  60 */       if (bool1) {
/*  61 */         hrmScheduleShiftsSet.setCreater(Long.valueOf(this.manager.getUID()));
/*  62 */         hrmScheduleShiftsSet.setCreateTime(DateUtil.getFullDate());
/*     */       } 
/*  64 */       hrmScheduleShiftsSet.setLastModifier(Long.valueOf(this.manager.getUID()));
/*  65 */       hrmScheduleShiftsSet.setLastModificationTime(DateUtil.getFullDate());
/*  66 */       this.manager.save(hrmScheduleShiftsSet, bool1);
/*  67 */     } else if (str.equals("delete")) {
/*  68 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/*  69 */     } else if (str.equals("getContent")) {
/*  70 */       bool = false;
/*  71 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/*  72 */       HrmScheduleShiftsSet hrmScheduleShiftsSet = (HrmScheduleShiftsSet)this.manager.get(long_);
/*  73 */       StringBuffer stringBuffer = new StringBuffer();
/*  74 */       String str1 = String.valueOf(this.manager.getLanguageId());
/*  75 */       if (hrmScheduleShiftsSet != null) {
/*  76 */         boolean bool1; boolean bool2; List list = this.manager.getWorkTimeList(String.valueOf(long_), hrmScheduleShiftsSet.getField003().intValue(), str1);
/*  77 */         switch (hrmScheduleShiftsSet.getField003().intValue()) {
/*     */           case 0:
/*     */           case 1:
/*  80 */             stringBuffer.append("<table border='1' cellspacing='0' cellpadding='0' class='wtable'>");
/*  81 */             bool1 = false; bool2 = StringUtil.isNull(hrmScheduleShiftsSet.getField007());
/*  82 */             for (DataBook dataBook : list) {
/*  83 */               bool1 = (bool2 || StringUtil.isNull(dataBook.getValue())) ? true : false;
/*  84 */               stringBuffer.append("<tr>")
/*  85 */                 .append("<td class='ltd'>")
/*  86 */                 .append(StringUtil.vString(dataBook.getId()))
/*  87 */                 .append("</td>")
/*  88 */                 .append("<td class='").append(bool1 ? "r" : "c").append("td'")
/*  89 */                 .append(bool1 ? "" : "style='background:").append(hrmScheduleShiftsSet.getField007()).append("'").append(">")
/*  90 */                 .append(StringUtil.vString(dataBook.getValue()))
/*  91 */                 .append("</td>")
/*  92 */                 .append("</tr>");
/*     */             } 
/*  94 */             stringBuffer.append("</table>");
/*     */             break;
/*     */           default:
/*  97 */             stringBuffer.append(this.manager.getLabelName(Integer.valueOf(125932)));
/*     */             break;
/*     */         } 
/*     */       } else {
/* 101 */         stringBuffer.append(this.manager.getLabelName(Integer.valueOf(125822)));
/*     */       } 
/* 103 */       jSONObject.put("content", stringBuffer.toString());
/*     */     } 
/* 105 */     if (bool) (new HrmScheduleShiftsSetCache()).remove(); 
/* 106 */     return jSONObject;
/*     */   }
/*     */   
/*     */   public List<Object> getContent(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 110 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 111 */     HashMap<Object, Object> hashMap = null;
/* 112 */     Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 113 */     HrmScheduleShiftsSet hrmScheduleShiftsSet = (HrmScheduleShiftsSet)this.manager.get(long_);
/* 114 */     String str = String.valueOf(this.manager.getLanguageId());
/* 115 */     if (hrmScheduleShiftsSet != null) {
/* 116 */       List list = this.manager.getWorkTimeList(String.valueOf(long_), hrmScheduleShiftsSet.getField003().intValue(), str);
/* 117 */       switch (hrmScheduleShiftsSet.getField003().intValue()) {
/*     */         case 0:
/*     */         case 1:
/* 120 */           for (DataBook dataBook : list) {
/* 121 */             hashMap = new HashMap<>();
/* 122 */             hashMap.put("id", StringUtil.vString(dataBook.getId()));
/* 123 */             hashMap.put("background", hrmScheduleShiftsSet.getField007());
/* 124 */             hashMap.put("value", StringUtil.vString(dataBook.getValue()));
/* 125 */             arrayList.add(hashMap);
/*     */           } 
/*     */           break;
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     } 
/* 135 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*     */ 
/*     */   
/*     */   protected String getValue(int paramInt, HrmScheduleShiftsSet paramHrmScheduleShiftsSet) {
/* 143 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleShiftsSetControllerE9.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */