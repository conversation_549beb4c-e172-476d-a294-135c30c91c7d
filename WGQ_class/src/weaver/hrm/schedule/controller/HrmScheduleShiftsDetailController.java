/*    */ package weaver.hrm.schedule.controller;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.util.HashMap;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*    */ import org.json.JSONObject;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.framework.BaseController;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.schedule.cache.HrmScheduleShiftsDetailCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleShiftsDetail;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleShiftsWt;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleShiftsDetailManager;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleShiftsWtManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftsDetailController
/*    */   extends BaseController<HrmScheduleShiftsDetail>
/*    */ {
/* 29 */   private HrmScheduleShiftsDetailManager manager = null;
/*    */   
/* 31 */   private HrmScheduleShiftsWtManager wtManager = null;
/*    */   
/*    */   public HrmScheduleShiftsDetailController() {
/* 34 */     this.manager = new HrmScheduleShiftsDetailManager();
/* 35 */     this.wtManager = new HrmScheduleShiftsWtManager();
/* 36 */     setManager((BaseManager)this.manager);
/*    */   }
/*    */   
/*    */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 40 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/* 41 */     this.wtManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/*    */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException {
/* 45 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/* 46 */     JSONObject jSONObject = new JSONObject();
/* 47 */     boolean bool = true;
/* 48 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/* 49 */     if (str.equals("save")) {
/* 50 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 51 */       HrmScheduleShiftsDetail hrmScheduleShiftsDetail = (long_.longValue() != 0L) ? (HrmScheduleShiftsDetail)this.manager.get(long_) : null;
/* 52 */       boolean bool1 = (hrmScheduleShiftsDetail == null) ? true : false;
/* 53 */       hrmScheduleShiftsDetail = bool1 ? new HrmScheduleShiftsDetail() : hrmScheduleShiftsDetail;
/* 54 */       this.manager.initBean(paramHttpServletRequest, hrmScheduleShiftsDetail);
/* 55 */       long_ = this.manager.save(hrmScheduleShiftsDetail, bool1);
/*    */       
/* 57 */       if (!bool1) {
/* 58 */         HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 59 */         hashMap.put("field001", hrmScheduleShiftsDetail.getField001());
/* 60 */         hashMap.put("field003", hrmScheduleShiftsDetail.getId());
/* 61 */         this.wtManager.delete(hashMap);
/*    */       } 
/* 63 */       HrmScheduleShiftsWt hrmScheduleShiftsWt = null;
/* 64 */       String[] arrayOfString = hrmScheduleShiftsDetail.getField002().split(";");
/* 65 */       for (String str1 : arrayOfString) {
/* 66 */         hrmScheduleShiftsWt = new HrmScheduleShiftsWt();
/* 67 */         hrmScheduleShiftsWt.setField001(hrmScheduleShiftsDetail.getField001());
/* 68 */         hrmScheduleShiftsWt.setField002(Long.valueOf(StringUtil.parseToLong(str1)));
/* 69 */         hrmScheduleShiftsWt.setField003(long_);
/* 70 */         this.wtManager.save(hrmScheduleShiftsWt);
/*    */       } 
/* 72 */     } else if (str.equals("delete")) {
/* 73 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("ids"));
/* 74 */       this.manager.delete(str1);
/*    */       
/* 76 */       HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 77 */       hashMap.put("sql_field003", "and field003 in (" + str1 + ")");
/* 78 */       this.wtManager.delete(hashMap);
/* 79 */     } else if (str.equals("deleteBySet")) {
/* 80 */       HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 81 */       hashMap.put("field001", Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L)));
/* 82 */       this.manager.delete(hashMap);
/* 83 */       this.wtManager.delete(hashMap);
/*    */     } 
/* 85 */     if (bool) (new HrmScheduleShiftsDetailCache()).remove(); 
/* 86 */     return jSONObject;
/*    */   }
/*    */ 
/*    */   
/*    */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*    */ 
/*    */   
/*    */   protected String getValue(int paramInt, HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail) {
/* 94 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleShiftsDetailController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */