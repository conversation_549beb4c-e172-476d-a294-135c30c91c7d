/*     */ package weaver.hrm.schedule.controller;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.json.JSONObject;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseController;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.attendance.manager.HrmPubHolidayManager;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleSetCache;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSet;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetPerson;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsSet;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetDetailManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetManagerE9;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetPersonManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsSetManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleWorktimeManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSetControllerE9
/*     */   extends BaseController<HrmScheduleSet>
/*     */ {
/*  42 */   private HrmScheduleSetManagerE9 manager = null;
/*  43 */   private HrmPubHolidayManager holidayManager = null;
/*  44 */   private HrmScheduleShiftsSetManager setManager = null;
/*  45 */   private HrmScheduleSetPersonManager personManager = null;
/*  46 */   private HrmScheduleSetDetailManager detailManager = null;
/*  47 */   private HrmScheduleWorktimeManager worktimeManager = null;
/*     */   
/*     */   public HrmScheduleSetControllerE9() {
/*  50 */     this.manager = new HrmScheduleSetManagerE9();
/*  51 */     this.holidayManager = new HrmPubHolidayManager();
/*  52 */     this.setManager = new HrmScheduleShiftsSetManager();
/*  53 */     this.personManager = new HrmScheduleSetPersonManager();
/*  54 */     this.detailManager = new HrmScheduleSetDetailManager();
/*  55 */     this.worktimeManager = new HrmScheduleWorktimeManager();
/*  56 */     setManager((BaseManager)this.manager);
/*     */   }
/*     */   
/*     */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  60 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  61 */     this.setManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  62 */     this.personManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  63 */     this.detailManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  64 */     this.holidayManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  65 */     this.worktimeManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  69 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/*  70 */     JSONObject jSONObject = new JSONObject();
/*  71 */     String[] arrayOfString = { "save", "delete", "cancel" };
/*  72 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/*  73 */     if (str.equals("save")) {
/*  74 */       jSONObject.put("canSave", doSave(paramHttpServletRequest, paramHttpServletResponse));
/*  75 */     } else if (str.equals("delete")) {
/*  76 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/*  77 */     } else if (str.equals("cancel")) {
/*  78 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/*  79 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("sIds"));
/*  80 */       String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/*  81 */       String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/*  82 */       this.detailManager.deletePersonSchedule(long_, str2, str3, str1);
/*  83 */     } else if (str.equals("getContent")) {
/*  84 */       jSONObject.put("content", getContent(paramHttpServletRequest, paramHttpServletResponse));
/*  85 */     } else if (str.equals("getScheduleShifts")) {
/*  86 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("id"));
/*  87 */       String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/*  88 */       String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/*  89 */       List<HrmScheduleShiftsSet> list = this.setManager.getScheduleShifts(str1, str2, str3);
/*  90 */       boolean bool = (list == null) ? false : list.size();
/*  91 */       jSONObject.put("size", bool);
/*  92 */       jSONObject.put("sid", (bool != true) ? "" : ((HrmScheduleShiftsSet)list.get(0)).getId().toString());
/*     */     } 
/*  94 */     if (this.manager.exist(str, arrayOfString)) (new HrmScheduleSetCache()).remove(); 
/*  95 */     return jSONObject;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*     */ 
/*     */   
/*     */   protected String getValue(int paramInt, HrmScheduleSet paramHrmScheduleSet) {
/* 103 */     return "";
/*     */   }
/*     */   
/*     */   private String doSave(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 107 */     Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 108 */     int i = StringUtil.parseToInt(paramHttpServletRequest.getParameter("resourceId"), 0);
/* 109 */     HrmScheduleSet hrmScheduleSet = (long_.longValue() != 0L) ? (HrmScheduleSet)this.manager.get(long_) : null;
/* 110 */     boolean bool1 = (hrmScheduleSet == null) ? true : false;
/* 111 */     if (bool1) {  } else {  }  hrmScheduleSet = hrmScheduleSet;
/* 112 */     hrmScheduleSet.setSn(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("sn"))));
/* 113 */     hrmScheduleSet.setField001(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field001")));
/* 114 */     hrmScheduleSet.setField002(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field002")));
/* 115 */     hrmScheduleSet.setField003(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field003"))));
/* 116 */     hrmScheduleSet.setField004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field004"))));
/* 117 */     hrmScheduleSet.setLastModifier(Long.valueOf(this.manager.getUID()));
/* 118 */     hrmScheduleSet.setLastModificationTime(DateUtil.getFullDate());
/*     */     
/* 120 */     boolean bool2 = false;
/* 121 */     if (bool1) {
/* 122 */       hrmScheduleSet.setCreater(Long.valueOf(this.manager.getUID()));
/* 123 */       hrmScheduleSet.setCreateTime(DateUtil.getFullDate());
/* 124 */       List list = this.personManager.find("[map]field001:" + hrmScheduleSet.getId().longValue() + ";sql_field002:and t.field002 in (select id from hrmresource);delflag:0");
/* 125 */       bool2 = (i != 0 || list.size() > 0) ? true : false;
/*     */     } 
/*     */     
/* 128 */     if (bool2) saveSchedule(hrmScheduleSet, bool1, i);
/*     */     
/* 130 */     return String.valueOf(bool2);
/*     */   }
/*     */   
/*     */   public void saveSchedule(HrmScheduleSet paramHrmScheduleSet, boolean paramBoolean, int paramInt) {
/* 134 */     this.manager.save(paramHrmScheduleSet, paramBoolean);
/*     */     
/* 136 */     if (paramInt != 0) {
/* 137 */       HrmScheduleSetPerson hrmScheduleSetPerson = new HrmScheduleSetPerson();
/* 138 */       hrmScheduleSetPerson.setField001(paramHrmScheduleSet.getId());
/* 139 */       hrmScheduleSetPerson.setField002(Integer.valueOf(paramInt));
/* 140 */       this.personManager.save(hrmScheduleSetPerson);
/*     */     } 
/* 142 */     HrmScheduleShiftsSet hrmScheduleShiftsSet = this.setManager.getByRealId(String.valueOf(paramHrmScheduleSet.getField003()));
/* 143 */     if (hrmScheduleShiftsSet != null) {
/* 144 */       String str1 = paramHrmScheduleSet.getField001(), str2 = paramHrmScheduleSet.getField002();
/* 145 */       List list1 = this.holidayManager.find("[map]sql_holidaydate:and t.holidaydate between '" + str1 + "' and '" + str2 + "';sql_changetype:and t.changetype between 1 and 3;countryid:" + this.manager.getCountryId());
/* 146 */       List list2 = this.setManager.getScheduleSetDetailList(paramHrmScheduleSet.getId().longValue(), hrmScheduleShiftsSet, str1, str2, list1);
/* 147 */       this.detailManager.deleteBeforeSaveSchedulePerson(paramHrmScheduleSet.getId(), str1, str2);
/* 148 */       for (HrmScheduleSetDetail hrmScheduleSetDetail : list2)
/* 149 */         this.detailManager.saveSchedulePerson(hrmScheduleSetDetail); 
/*     */     } 
/*     */   } public List<Object> getContent(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*     */     List list;
/*     */     String str4;
/*     */     Map map;
/* 155 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/* 156 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 157 */     HashMap<Object, Object> hashMap = null;
/* 158 */     Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 159 */     String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/* 160 */     String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/* 161 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("createdateselect"));
/* 162 */     if (!str3.equals("") && !str3.equals("0") && !str3.equals("6")) {
/* 163 */       str1 = TimeUtil.getDateByOption(str3, "0");
/* 164 */       str2 = TimeUtil.getDateByOption(str3, "1");
/*     */     } 
/* 166 */     switch (StringUtil.parseToInt(paramHttpServletRequest.getParameter("stype"), 0)) {
/*     */       case 0:
/*     */       case 1:
/* 169 */         list = this.manager.getDateList(str1, str2);
/* 170 */         str4 = this.manager.getLabelName(Integer.valueOf(125806));
/* 171 */         map = this.detailManager.findMap("[map]field002:" + long_ + ";sql_field003:and t.field003 between '" + str1 + "' and '" + str2 + "';sqlorderby:t.field003");
/* 172 */         if (list != null && list.size() != 0) {
/* 173 */           boolean bool1 = false, bool2 = false;
/* 174 */           String str5 = "";
/* 175 */           String str6 = String.valueOf(this.manager.getLanguageId());
/* 176 */           int[] arrayOfInt = { 392, 393, 394, 395, 396, 397, 398 };
/* 177 */           for (String str : list) {
/* 178 */             HrmScheduleSetDetail hrmScheduleSetDetail = map.containsKey(str) ? (HrmScheduleSetDetail)map.get(str) : null;
/* 179 */             bool1 = (hrmScheduleSetDetail == null) ? true : false;
/* 180 */             bool2 = bool1 ? false : StringUtil.isNull(hrmScheduleSetDetail.getField005());
/* 181 */             int i = bool1 ? DateUtil.getWeek(str) : hrmScheduleSetDetail.getField004().intValue();
/* 182 */             hashMap = new HashMap<>();
/* 183 */             hashMap.put("background", bool1 ? "white" : this.setManager.getField007(hrmScheduleSetDetail.getField001()));
/* 184 */             hashMap.put("date", DateUtil.formatDate(str, "MM/dd"));
/* 185 */             hashMap.put("weekday", (i > arrayOfInt.length || i <= 0) ? "" : this.manager.getLabelName(Integer.valueOf(arrayOfInt[i - 1]), str6));
/* 186 */             hashMap.put("worktime", bool1 ? "" : (bool2 ? str4 : this.worktimeManager.getWorkTime(hrmScheduleSetDetail.getField005(), str6)));
/* 187 */             hashMap.put("description", bool1 ? "" : this.setManager.getDescription(hrmScheduleSetDetail.getField001()));
/* 188 */             arrayList.add(hashMap);
/*     */           } 
/*     */         } 
/*     */         break;
/*     */     } 
/*     */     
/* 194 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   private String getAllIds(int paramInt, boolean paramBoolean) {
/* 198 */     String str = "";
/* 199 */     if (paramInt != 1 && StringUtil.vString((new ManageDetachComInfo()).getDetachable()).equals("1")) {
/*     */       try {
/* 201 */         ArrayList arrayList = (new SubCompanyComInfo()).getRightSubCompany(paramInt, "HrmScheduling:set");
/* 202 */         for (byte b = 0; b < arrayList.size(); ) { str = str + ((str.length() == 0) ? "" : ",") + StringUtil.vString(arrayList.get(b)); b++; } 
/* 203 */       } catch (Exception exception) {}
/* 204 */       if (StringUtil.isNull(str) || paramInt == -1) {
/* 205 */         if (!paramBoolean) {
/* 206 */           str = "-99999";
/*     */         } else {
/* 208 */           str = (new SubCompanyComInfo()).getSubCompanyid(paramInt);
/*     */         } 
/*     */       }
/*     */     } 
/* 212 */     return str;
/*     */   }
/*     */   public List<Object> getCalendar(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*     */     List<Object> list;
/* 216 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/* 217 */     ArrayList arrayList = new ArrayList();
/* 218 */     int i = StringUtil.parseToInt(paramHttpServletRequest.getParameter("B"), 0);
/* 219 */     String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("S"));
/* 220 */     String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("E"));
/* 221 */     int j = StringUtil.parseToInt(paramHttpServletRequest.getParameter("D"), 0);
/* 222 */     String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("M"));
/* 223 */     String str4 = StringUtil.vString(paramHttpServletRequest.getParameter("U"));
/* 224 */     boolean bool1 = str3.equals(str4);
/* 225 */     String str5 = getAllIds(StringUtil.parseToInt(str4), bool1);
/* 226 */     int k = StringUtil.parseToInt(paramHttpServletRequest.getParameter("C"));
/* 227 */     boolean bool2 = Boolean.valueOf(StringUtil.vString(paramHttpServletRequest.getParameter("R"))).booleanValue();
/* 228 */     this.manager.setShowResource(bool2);
/*     */     
/* 230 */     List list1 = this.detailManager.getGroupData((i == 4) ? DateUtil.addDate(str1, -1) : str1, str2, bool2 ? str3 : "", str5, k);
/* 231 */     List list2 = this.holidayManager.find("[map]sql_holidaydate:and t.holidaydate between '" + str1 + "' and '" + str2 + "';sql_changetype:and t.changetype between 1 and 3;countryid:" + this.manager.getCountryId());
/* 232 */     switch (i) {
/*     */       case 1:
/* 234 */         list = this.manager.showYear(list1, list2, str1, str2, j);
/*     */         break;
/*     */       case 2:
/* 237 */         list = this.manager.showMonth(list1, list2, str1, str2, j);
/*     */         break;
/*     */       case 3:
/* 240 */         list = this.manager.showWeek(list1, list2, str1, str2, j);
/*     */         break;
/*     */       case 4:
/* 243 */         list = this.manager.showDate(list1, str1, j);
/*     */         break;
/*     */     } 
/* 246 */     return list;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleSetControllerE9.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */