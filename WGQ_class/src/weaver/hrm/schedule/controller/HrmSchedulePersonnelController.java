/*    */ package weaver.hrm.schedule.controller;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*    */ import org.json.JSONObject;
/*    */ import weaver.common.DateUtil;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.framework.BaseController;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.schedule.cache.HrmSchedulePersonCache;
/*    */ import weaver.hrm.schedule.cache.HrmSchedulePersonnelCache;
/*    */ import weaver.hrm.schedule.domain.HrmSchedulePersonnel;
/*    */ import weaver.hrm.schedule.manager.HrmSchedulePersonnelManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSchedulePersonnelController
/*    */   extends BaseController<HrmSchedulePersonnel>
/*    */ {
/* 28 */   private HrmSchedulePersonnelManager manager = null;
/*    */   
/*    */   public HrmSchedulePersonnelController() {
/* 31 */     this.manager = new HrmSchedulePersonnelManager();
/* 32 */     setManager((BaseManager)this.manager);
/*    */   }
/*    */   
/*    */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 36 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/*    */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException {
/* 40 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/* 41 */     JSONObject jSONObject = new JSONObject();
/* 42 */     String[] arrayOfString = { "save", "delete" };
/* 43 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/* 44 */     if (str.equals("save")) {
/* 45 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 46 */       String str1 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field002"));
/* 47 */       if (StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field001")).equals("4")) {
/* 48 */         str1 = "0";
/*    */       }
/* 50 */       String[] arrayOfString1 = StringUtil.split(str1, ",");
/* 51 */       for (String str2 : arrayOfString1) {
/* 52 */         HrmSchedulePersonnel hrmSchedulePersonnel = (long_.longValue() != 0L) ? (HrmSchedulePersonnel)this.manager.get(long_) : null;
/* 53 */         boolean bool = (hrmSchedulePersonnel == null) ? true : false;
/* 54 */         hrmSchedulePersonnel = bool ? new HrmSchedulePersonnel() : hrmSchedulePersonnel;
/* 55 */         hrmSchedulePersonnel.setSn(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("sn"))));
/* 56 */         hrmSchedulePersonnel.setField001(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field001"))));
/* 57 */         hrmSchedulePersonnel.setField002(str2);
/* 58 */         hrmSchedulePersonnel.setField003(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field003"))));
/* 59 */         hrmSchedulePersonnel.setField004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field004"))));
/* 60 */         hrmSchedulePersonnel.setField005(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field005"))));
/* 61 */         int i = StringUtil.parseToInt(paramHttpServletRequest.getParameter("jobtitlelevel"));
/* 62 */         String str3 = "";
/* 63 */         String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("jobtitlesubcompany"));
/* 64 */         String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("jobtitledepartment"));
/* 65 */         if (i == 1) {
/* 66 */           str3 = str5;
/* 67 */         } else if (i == 2) {
/* 68 */           str3 = str4;
/*    */         } 
/*    */         
/* 71 */         hrmSchedulePersonnel.setField006(Integer.valueOf(i));
/* 72 */         hrmSchedulePersonnel.setField007(str3);
/* 73 */         if (bool) {
/* 74 */           hrmSchedulePersonnel.setCreater(Long.valueOf(this.manager.getUID()));
/* 75 */           hrmSchedulePersonnel.setCreateTime(DateUtil.getFullDate());
/*    */         } 
/* 77 */         hrmSchedulePersonnel.setLastModifier(Long.valueOf(this.manager.getUID()));
/* 78 */         hrmSchedulePersonnel.setLastModificationTime(DateUtil.getFullDate());
/* 79 */         this.manager.save(hrmSchedulePersonnel);
/*    */       } 
/* 81 */     } else if (str.equals("delete")) {
/* 82 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/*    */     } 
/* 84 */     if (this.manager.exist(str, arrayOfString)) {
/* 85 */       (new HrmSchedulePersonnelCache()).remove();
/* 86 */       (new HrmSchedulePersonCache()).remove();
/*    */     } 
/* 88 */     return jSONObject;
/*    */   }
/*    */ 
/*    */   
/*    */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*    */ 
/*    */   
/*    */   protected String getValue(int paramInt, HrmSchedulePersonnel paramHrmSchedulePersonnel) {
/* 96 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmSchedulePersonnelController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */