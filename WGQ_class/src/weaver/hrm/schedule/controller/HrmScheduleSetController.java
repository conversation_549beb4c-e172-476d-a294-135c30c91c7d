/*     */ package weaver.hrm.schedule.controller;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.json.JSONObject;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseController;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.attendance.manager.HrmPubHolidayManager;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleSetCache;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSet;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetPerson;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsSet;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetDetailManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetPersonManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsSetManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleWorktimeManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSetController
/*     */   extends BaseController<HrmScheduleSet>
/*     */ {
/*  39 */   private HrmScheduleSetManager manager = null;
/*  40 */   private HrmPubHolidayManager holidayManager = null;
/*  41 */   private HrmScheduleShiftsSetManager setManager = null;
/*  42 */   private HrmScheduleSetPersonManager personManager = null;
/*  43 */   private HrmScheduleSetDetailManager detailManager = null;
/*  44 */   private HrmScheduleWorktimeManager worktimeManager = null;
/*     */   
/*     */   public HrmScheduleSetController() {
/*  47 */     this.manager = new HrmScheduleSetManager();
/*  48 */     this.holidayManager = new HrmPubHolidayManager();
/*  49 */     this.setManager = new HrmScheduleShiftsSetManager();
/*  50 */     this.personManager = new HrmScheduleSetPersonManager();
/*  51 */     this.detailManager = new HrmScheduleSetDetailManager();
/*  52 */     this.worktimeManager = new HrmScheduleWorktimeManager();
/*  53 */     setManager((BaseManager)this.manager);
/*     */   }
/*     */   
/*     */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  57 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  58 */     this.setManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  59 */     this.personManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  60 */     this.detailManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  61 */     this.holidayManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  62 */     this.worktimeManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  66 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/*  67 */     JSONObject jSONObject = new JSONObject();
/*  68 */     String[] arrayOfString = { "save", "delete", "cancel" };
/*  69 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/*  70 */     if (str.equals("save")) {
/*  71 */       jSONObject.put("canSave", doSave(paramHttpServletRequest, paramHttpServletResponse));
/*  72 */     } else if (str.equals("delete")) {
/*  73 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/*  74 */     } else if (str.equals("cancel")) {
/*  75 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/*  76 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("sIds"));
/*  77 */       String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/*  78 */       String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/*  79 */       this.detailManager.deletePersonSchedule(long_, str2, str3, str1);
/*  80 */     } else if (str.equals("getContent")) {
/*  81 */       jSONObject.put("content", getContent(paramHttpServletRequest, paramHttpServletResponse));
/*  82 */     } else if (str.equals("showCalendar")) {
/*  83 */       jSONObject.put("content", getCalendar(paramHttpServletRequest, paramHttpServletResponse));
/*  84 */     } else if (str.equals("getScheduleShifts")) {
/*  85 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("id"));
/*  86 */       String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/*  87 */       String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/*  88 */       List<HrmScheduleShiftsSet> list = this.setManager.getScheduleShifts(str1, str2, str3);
/*  89 */       boolean bool = (list == null) ? false : list.size();
/*  90 */       jSONObject.put("size", bool);
/*  91 */       jSONObject.put("sid", (bool != true) ? "" : ((HrmScheduleShiftsSet)list.get(0)).getId().toString());
/*     */     } 
/*  93 */     if (this.manager.exist(str, arrayOfString)) (new HrmScheduleSetCache()).remove(); 
/*  94 */     return jSONObject;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*     */ 
/*     */   
/*     */   protected String getValue(int paramInt, HrmScheduleSet paramHrmScheduleSet) {
/* 102 */     return "";
/*     */   }
/*     */   
/*     */   private String doSave(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 106 */     Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 107 */     int i = StringUtil.parseToInt(paramHttpServletRequest.getParameter("resourceId"), 0);
/* 108 */     HrmScheduleSet hrmScheduleSet = (long_.longValue() != 0L) ? (HrmScheduleSet)this.manager.get(long_) : null;
/* 109 */     boolean bool1 = (hrmScheduleSet == null) ? true : false;
/* 110 */     if (bool1) {  } else {  }  hrmScheduleSet = hrmScheduleSet;
/* 111 */     hrmScheduleSet.setSn(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("sn"))));
/* 112 */     hrmScheduleSet.setField001(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field001")));
/* 113 */     hrmScheduleSet.setField002(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field002")));
/* 114 */     hrmScheduleSet.setField003(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field003"))));
/* 115 */     hrmScheduleSet.setField004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field004"))));
/* 116 */     hrmScheduleSet.setLastModifier(Long.valueOf(this.manager.getUID()));
/* 117 */     hrmScheduleSet.setLastModificationTime(DateUtil.getFullDate());
/*     */     
/* 119 */     boolean bool2 = false;
/* 120 */     if (bool1) {
/* 121 */       hrmScheduleSet.setCreater(Long.valueOf(this.manager.getUID()));
/* 122 */       hrmScheduleSet.setCreateTime(DateUtil.getFullDate());
/* 123 */       List list = this.personManager.find("[map]field001:" + hrmScheduleSet.getId().longValue() + ";sql_field002:and t.field002 in (select id from hrmresource);delflag:0");
/* 124 */       bool2 = (i != 0 || list.size() > 0) ? true : false;
/*     */     } 
/*     */     
/* 127 */     if (bool2) saveSchedule(hrmScheduleSet, bool1, i);
/*     */     
/* 129 */     return String.valueOf(bool2);
/*     */   }
/*     */   
/*     */   public void saveSchedule(HrmScheduleSet paramHrmScheduleSet, boolean paramBoolean, int paramInt) {
/* 133 */     this.manager.save(paramHrmScheduleSet, paramBoolean);
/*     */     
/* 135 */     if (paramInt != 0) {
/* 136 */       HrmScheduleSetPerson hrmScheduleSetPerson = new HrmScheduleSetPerson();
/* 137 */       hrmScheduleSetPerson.setField001(paramHrmScheduleSet.getId());
/* 138 */       hrmScheduleSetPerson.setField002(Integer.valueOf(paramInt));
/* 139 */       this.personManager.save(hrmScheduleSetPerson);
/*     */     } 
/* 141 */     HrmScheduleShiftsSet hrmScheduleShiftsSet = this.setManager.getByRealId(String.valueOf(paramHrmScheduleSet.getField003()));
/* 142 */     if (hrmScheduleShiftsSet != null) {
/* 143 */       String str1 = paramHrmScheduleSet.getField001(), str2 = paramHrmScheduleSet.getField002();
/* 144 */       List list1 = this.holidayManager.find("[map]sql_holidaydate:and t.holidaydate between '" + str1 + "' and '" + str2 + "';sql_changetype:and t.changetype between 1 and 3;countryid:" + this.manager.getCountryId());
/* 145 */       List list2 = this.setManager.getScheduleSetDetailList(paramHrmScheduleSet.getId().longValue(), hrmScheduleShiftsSet, str1, str2, list1);
/* 146 */       this.detailManager.deleteBeforeSaveSchedulePerson(paramHrmScheduleSet.getId(), str1, str2);
/* 147 */       for (HrmScheduleSetDetail hrmScheduleSetDetail : list2)
/* 148 */         this.detailManager.saveSchedulePerson(hrmScheduleSetDetail); 
/*     */     } 
/*     */   } private String getContent(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*     */     List list;
/*     */     String str3;
/*     */     Map map;
/* 154 */     StringBuffer stringBuffer = new StringBuffer();
/* 155 */     Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 156 */     String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/* 157 */     String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/* 158 */     switch (StringUtil.parseToInt(paramHttpServletRequest.getParameter("stype"), 0)) {
/*     */       case 0:
/* 160 */         stringBuffer.append(this.manager.getLabelName(Integer.valueOf(125942)));
/*     */         break;
/*     */       case 1:
/* 163 */         list = this.manager.getDateList(str1, str2);
/* 164 */         str3 = this.manager.getLabelName(Integer.valueOf(125806));
/* 165 */         map = this.detailManager.findMap("[map]field002:" + long_ + ";sql_field003:and t.field003 between '" + str1 + "' and '" + str2 + "';sqlorderby:t.field003");
/* 166 */         if (list != null && list.size() != 0) {
/* 167 */           stringBuffer.append("<table border='1' cellspacing='0' cellpadding='0' class='dtable'>");
/* 168 */           boolean bool1 = false, bool2 = false;
/* 169 */           String str4 = "";
/* 170 */           String str5 = String.valueOf(this.manager.getLanguageId());
/* 171 */           int[] arrayOfInt = { 392, 393, 394, 395, 396, 397, 398 };
/* 172 */           for (String str : list) {
/* 173 */             HrmScheduleSetDetail hrmScheduleSetDetail = map.containsKey(str) ? (HrmScheduleSetDetail)map.get(str) : null;
/* 174 */             bool1 = (hrmScheduleSetDetail == null) ? true : false;
/* 175 */             bool2 = bool1 ? false : StringUtil.isNull(hrmScheduleSetDetail.getField005());
/* 176 */             int i = bool1 ? DateUtil.getWeek(str) : hrmScheduleSetDetail.getField004().intValue();
/* 177 */             str4 = "style='background:" + (bool1 ? "white" : this.setManager.getField007(hrmScheduleSetDetail.getField001())) + "'";
/* 178 */             stringBuffer.append("<tr>").append("<td class='dtd'>").append(DateUtil.formatDate(str, "MM/dd")).append("</td>")
/* 179 */               .append("<td class='wtd'>").append((i > arrayOfInt.length || i <= 0) ? "" : this.manager.getLabelName(Integer.valueOf(arrayOfInt[i - 1]), str5)).append("</td>")
/* 180 */               .append("<td class='ttd'").append(str4).append(">").append(bool1 ? "" : (bool2 ? str3 : this.worktimeManager.getWorkTime(hrmScheduleSetDetail.getField005(), str5))).append("</td>")
/* 181 */               .append("<td class='ntd'").append(str4).append(">").append(bool1 ? "" : this.setManager.getDescription(hrmScheduleSetDetail.getField001())).append("</td>").append("</tr>");
/*     */           } 
/* 183 */           stringBuffer.append("</table>"); break;
/*     */         } 
/* 185 */         stringBuffer.append(this.manager.getLabelName(Integer.valueOf(125834)));
/*     */         break;
/*     */     } 
/*     */     
/* 189 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getAllIds(int paramInt, boolean paramBoolean) {
/* 193 */     String str = "";
/* 194 */     if (paramInt != 1 && StringUtil.vString((new ManageDetachComInfo()).getDetachable()).equals("1")) {
/*     */       try {
/* 196 */         ArrayList arrayList = (new SubCompanyComInfo()).getRightSubCompany(paramInt, "HrmScheduling:set");
/* 197 */         for (byte b = 0; b < arrayList.size(); ) { str = str + ((str.length() == 0) ? "" : ",") + StringUtil.vString(arrayList.get(b)); b++; } 
/* 198 */       } catch (Exception exception) {}
/* 199 */       if (StringUtil.isNull(str) || paramInt == -1) {
/* 200 */         if (!paramBoolean) {
/* 201 */           str = "-99999";
/*     */         } else {
/* 203 */           str = (new SubCompanyComInfo()).getSubCompanyid(paramInt);
/*     */         } 
/*     */       }
/*     */     } 
/* 207 */     return str;
/*     */   }
/*     */   
/*     */   private String getCalendar(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 211 */     StringBuffer stringBuffer = new StringBuffer();
/* 212 */     int i = StringUtil.parseToInt(paramHttpServletRequest.getParameter("B"), 0);
/* 213 */     String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("S"));
/* 214 */     String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("E"));
/* 215 */     int j = StringUtil.parseToInt(paramHttpServletRequest.getParameter("D"), 0);
/* 216 */     String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("M"));
/* 217 */     String str4 = StringUtil.vString(paramHttpServletRequest.getParameter("U"));
/* 218 */     boolean bool1 = str3.equals(str4);
/* 219 */     String str5 = getAllIds(StringUtil.parseToInt(str4), bool1);
/* 220 */     int k = StringUtil.parseToInt(paramHttpServletRequest.getParameter("C"));
/* 221 */     boolean bool2 = Boolean.valueOf(StringUtil.vString(paramHttpServletRequest.getParameter("R"))).booleanValue();
/* 222 */     this.manager.setShowResource(bool2);
/*     */     
/* 224 */     List list1 = this.detailManager.getGroupData((i == 4) ? DateUtil.addDate(str1, -1) : str1, str2, bool2 ? str3 : "", str5, k);
/* 225 */     List list2 = this.holidayManager.find("[map]sql_holidaydate:and t.holidaydate between '" + str1 + "' and '" + str2 + "';sql_changetype:and t.changetype between 1 and 3;countryid:" + this.manager.getCountryId());
/* 226 */     switch (i) {
/*     */       case 1:
/* 228 */         stringBuffer.append(this.manager.showYear(list1, list2, str1, str2, j));
/*     */         break;
/*     */       case 2:
/* 231 */         stringBuffer.append(this.manager.showMonth(list1, list2, str1, str2, j));
/*     */         break;
/*     */       case 3:
/* 234 */         stringBuffer.append(this.manager.showWeek(list1, list2, str1, str2, j));
/*     */         break;
/*     */       case 4:
/* 237 */         stringBuffer.append(this.manager.showDate(list1, str1, j));
/*     */         break;
/*     */     } 
/* 240 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleSetController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */