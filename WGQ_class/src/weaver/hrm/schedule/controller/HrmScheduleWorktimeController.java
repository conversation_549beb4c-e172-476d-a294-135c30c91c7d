/*     */ package weaver.hrm.schedule.controller;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.json.JSONObject;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseController;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleWorktimeCache;
/*     */ import weaver.hrm.schedule.domain.HrmSchduleResttime;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleWorktime;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleWorktimeManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleWorktimeController
/*     */   extends BaseController<HrmScheduleWorktime>
/*     */ {
/*  29 */   private HrmScheduleWorktimeManager manager = null;
/*     */   
/*     */   public HrmScheduleWorktimeController() {
/*  32 */     this.manager = new HrmScheduleWorktimeManager();
/*  33 */     setManager((BaseManager)this.manager);
/*     */   }
/*     */   
/*     */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  37 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  41 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/*  42 */     JSONObject jSONObject = new JSONObject();
/*  43 */     String[] arrayOfString = { "save", "delete" };
/*  44 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/*  45 */     if (str.equals("save")) {
/*  46 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/*  47 */       HrmScheduleWorktime hrmScheduleWorktime = (long_.longValue() != 0L) ? (HrmScheduleWorktime)this.manager.get(long_) : null;
/*  48 */       boolean bool = (hrmScheduleWorktime == null) ? true : false;
/*  49 */       hrmScheduleWorktime = bool ? new HrmScheduleWorktime() : hrmScheduleWorktime;
/*  50 */       String str1 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("restTimeId1"));
/*  51 */       String str2 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("restTimeId2"));
/*  52 */       String str3 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("reststarttime1"));
/*  53 */       String str4 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("restendtime1"));
/*  54 */       String str5 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("reststarttime2"));
/*  55 */       String str6 = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("restendtime2"));
/*     */       
/*  57 */       ArrayList<HrmSchduleResttime> arrayList = new ArrayList();
/*  58 */       HrmSchduleResttime hrmSchduleResttime = null;
/*  59 */       hrmSchduleResttime = new HrmSchduleResttime();
/*  60 */       hrmSchduleResttime.setRestTimeId(str1);
/*  61 */       hrmSchduleResttime.setRestStartTime(StringUtil.isNull(new String[] { str3, str4 }) ? "" : (str3 + "-" + str4));
/*  62 */       hrmSchduleResttime.setRestEndTime(StringUtil.isNull(new String[] { str5, str6 }) ? "" : (str5 + "-" + str6));
/*     */       
/*  64 */       hrmSchduleResttime.setRestStartTime1(str3);
/*  65 */       hrmSchduleResttime.setRestEndTime1(str4);
/*  66 */       hrmSchduleResttime.setRestStartTime2(str5);
/*  67 */       hrmSchduleResttime.setRestEndTime2(str6);
/*     */       
/*  69 */       hrmSchduleResttime.setDelflag("0");
/*  70 */       arrayList.add(hrmSchduleResttime);
/*     */       
/*  72 */       hrmScheduleWorktime.setResttimeList(arrayList);
/*  73 */       hrmScheduleWorktime.setField001(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field001")));
/*  74 */       hrmScheduleWorktime.setField002(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field002")));
/*  75 */       hrmScheduleWorktime.setField003(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field003")));
/*  76 */       hrmScheduleWorktime.setField004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field004"))));
/*  77 */       hrmScheduleWorktime.setField005(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field005"))));
/*  78 */       hrmScheduleWorktime.setField006(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field006")));
/*  79 */       hrmScheduleWorktime.setField007(Double.valueOf(StringUtil.round(StringUtil.parseToDouble(paramHttpServletRequest.getParameter("field007")), 1)));
/*  80 */       if (bool) {
/*  81 */         hrmScheduleWorktime.setCreater(Long.valueOf(this.manager.getUID()));
/*  82 */         hrmScheduleWorktime.setCreateTime(DateUtil.getFullDate());
/*     */       } 
/*  84 */       hrmScheduleWorktime.setLastModifier(Long.valueOf(this.manager.getUID()));
/*  85 */       hrmScheduleWorktime.setLastModificationTime(DateUtil.getFullDate());
/*  86 */       StringBuffer stringBuffer = new StringBuffer();
/*  87 */       if (StringUtil.isNotNull(new String[] { hrmScheduleWorktime.getField002(), hrmScheduleWorktime.getField003() })) {
/*  88 */         String str7 = DateUtil.getCurrentDate();
/*  89 */         String str8 = str7;
/*  90 */         String str9 = (hrmScheduleWorktime.getField002().compareTo(hrmScheduleWorktime.getField003()) > 0) ? DateUtil.getTDate(1) : str8;
/*  91 */         str8 = str8 + " " + hrmScheduleWorktime.getField002();
/*  92 */         str9 = str9 + " " + hrmScheduleWorktime.getField003();
/*     */         
/*  94 */         ArrayList arrayList1 = new ArrayList();
/*  95 */         arrayList1.addAll(Arrays.asList(DateUtil.getDateMinutes(str8, str9)));
/*     */         
/*  97 */         String str10 = "";
/*  98 */         String str11 = "";
/*  99 */         if (StringUtil.isNotNull(new String[] { str3, str4 })) {
/* 100 */           if (str3.equals(hrmScheduleWorktime.getField002()) || str4.equals(hrmScheduleWorktime.getField003())) {
/* 101 */             stringBuffer.append(this.manager.getLabelName(Integer.valueOf(387920)) + "<br>");
/*     */           } else {
/* 103 */             str10 = str7 + " " + str3;
/* 104 */             str11 = ((str3.compareTo(str4) > 0) ? DateUtil.getTDate(1) : str7) + " " + str4;
/* 105 */             String[] arrayOfString1 = DateUtil.getDateMinutes(str10, str11);
/* 106 */             for (String str12 : arrayOfString1) {
/* 107 */               if (!arrayList1.contains(str12)) {
/* 108 */                 stringBuffer.append(this.manager.getLabelName(Integer.valueOf(387921)) + "<br>");
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         }
/* 114 */         if (StringUtil.isNotNull(new String[] { str5, str6 })) {
/* 115 */           if (str5.equals(hrmScheduleWorktime.getField002()) || str6.equals(hrmScheduleWorktime.getField003())) {
/* 116 */             stringBuffer.append(this.manager.getLabelName(Integer.valueOf(387924)) + "<br>");
/*     */           } else {
/* 118 */             str10 = str7 + " " + str5;
/* 119 */             str11 = ((str5.compareTo(str6) > 0) ? DateUtil.getTDate(1) : str7) + " " + str6;
/* 120 */             String[] arrayOfString1 = DateUtil.getDateMinutes(str10, str11);
/* 121 */             for (String str12 : arrayOfString1) {
/* 122 */               if (!arrayList1.contains(str12)) {
/* 123 */                 stringBuffer.append(this.manager.getLabelName(Integer.valueOf(387925)) + "<br>");
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         }
/*     */       } 
/* 130 */       if (stringBuffer.toString().length() == 0) this.manager.save(hrmScheduleWorktime); 
/* 131 */       jSONObject.put("msg", stringBuffer.toString());
/* 132 */     } else if (str.equals("delete")) {
/* 133 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/* 134 */     } else if (str.equals("getWorkTime")) {
/* 135 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("startTime"));
/* 136 */       String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("endTime"));
/* 137 */       jSONObject.put("result", StringUtil.round(DateUtil.subTime(str1, str2), 1));
/* 138 */     } else if (str.equals("getWorkTime1")) {
/* 139 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("startTime1"));
/* 140 */       String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("endTime1"));
/* 141 */       String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("startTime2"));
/* 142 */       String str4 = StringUtil.vString(paramHttpServletRequest.getParameter("endTime2"));
/* 143 */       String str5 = StringUtil.vString(paramHttpServletRequest.getParameter("field002"));
/* 144 */       String str6 = StringUtil.vString(paramHttpServletRequest.getParameter("field003"));
/* 145 */       double d1 = StringUtil.round(DateUtil.subTime(str5, str6), 1);
/* 146 */       double d2 = 0.0D;
/* 147 */       if (!"".equals(str1) && !"".equals(str2)) {
/* 148 */         d2 += StringUtil.round(DateUtil.subTime(str1, str2), 1);
/*     */       }
/* 150 */       if (!"".equals(str3) && !"".equals(str4)) {
/* 151 */         d2 += StringUtil.round(DateUtil.subTime(str3, str4), 1);
/*     */       }
/* 153 */       jSONObject.put("result", StringUtil.round(d1 - d2, 1));
/*     */     } 
/* 155 */     if (this.manager.exist(str, arrayOfString)) (new HrmScheduleWorktimeCache()).remove(); 
/* 156 */     return jSONObject;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*     */ 
/*     */   
/*     */   protected String getValue(int paramInt, HrmScheduleWorktime paramHrmScheduleWorktime) {
/* 164 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleWorktimeController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */