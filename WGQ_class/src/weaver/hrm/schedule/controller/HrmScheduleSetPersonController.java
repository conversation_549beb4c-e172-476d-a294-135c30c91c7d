/*     */ package weaver.hrm.schedule.controller;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.json.JSONObject;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseController;
/*     */ import weaver.framework.BaseManager;
/*     */ import weaver.hrm.attendance.manager.HrmPubHolidayManager;
/*     */ import weaver.hrm.schedule.cache.HrmScheduleSetPersonCache;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetPerson;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsSet;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetDetailManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetPersonManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsSetManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSetPersonController
/*     */   extends BaseController<HrmScheduleSetPerson>
/*     */ {
/*  34 */   private HrmScheduleSetPersonManager manager = null;
/*  35 */   private HrmPubHolidayManager holidayManager = null;
/*  36 */   private HrmScheduleShiftsSetManager setManager = null;
/*  37 */   private HrmScheduleSetDetailManager detailManager = null;
/*     */   
/*     */   public HrmScheduleSetPersonController() {
/*  40 */     this.manager = new HrmScheduleSetPersonManager();
/*  41 */     this.holidayManager = new HrmPubHolidayManager();
/*  42 */     this.setManager = new HrmScheduleShiftsSetManager();
/*  43 */     this.detailManager = new HrmScheduleSetDetailManager();
/*  44 */     setManager((BaseManager)this.manager);
/*     */   }
/*     */   
/*     */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  48 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  49 */     this.setManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  50 */     this.detailManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*  51 */     this.holidayManager.set(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException {
/*  55 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/*  56 */     JSONObject jSONObject = new JSONObject();
/*  57 */     boolean bool = true;
/*  58 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/*  59 */     if (str.equals("save")) {
/*  60 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/*  61 */       HrmScheduleSetPerson hrmScheduleSetPerson = (long_.longValue() != 0L) ? (HrmScheduleSetPerson)this.manager.get(long_) : null;
/*  62 */       boolean bool1 = (hrmScheduleSetPerson == null) ? true : false;
/*  63 */       hrmScheduleSetPerson = bool1 ? new HrmScheduleSetPerson() : hrmScheduleSetPerson;
/*  64 */       hrmScheduleSetPerson.setField001(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field001"))));
/*  65 */       hrmScheduleSetPerson.setField002(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field002"))));
/*  66 */       this.manager.save(hrmScheduleSetPerson, bool1);
/*  67 */     } else if (str.equals("delete")) {
/*  68 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/*  69 */     } else if (str.equals("mInsert")) {
/*  70 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("field002"));
/*  71 */       if (StringUtil.isNotNull(str1)) {
/*  72 */         long l1 = StringUtil.parseToLong(paramHttpServletRequest.getParameter("sId"), 0L);
/*  73 */         long l2 = StringUtil.parseToLong(paramHttpServletRequest.getParameter("field001"), 0L);
/*  74 */         if (l2 == 0L) l2 = l1;
/*     */         
/*  76 */         String[] arrayOfString = str1.split(",");
/*  77 */         HrmScheduleSetPerson hrmScheduleSetPerson = null;
/*  78 */         for (String str2 : arrayOfString) {
/*  79 */           hrmScheduleSetPerson = new HrmScheduleSetPerson();
/*  80 */           hrmScheduleSetPerson.setField001(Long.valueOf(l2));
/*  81 */           hrmScheduleSetPerson.setField002(Integer.valueOf(StringUtil.parseToInt(str2)));
/*  82 */           this.manager.save(hrmScheduleSetPerson, true);
/*     */         } 
/*     */         
/*  85 */         HrmScheduleShiftsSet hrmScheduleShiftsSet = (l1 != 0L) ? (HrmScheduleShiftsSet)this.setManager.get(Long.valueOf(l1)) : null;
/*  86 */         if (hrmScheduleShiftsSet != null) {
/*  87 */           String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/*  88 */           String str3 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/*  89 */           List list1 = this.holidayManager.find("[map]sql_holidaydate:and t.holidaydate between '" + str2 + "' and '" + str3 + "';sql_changetype:and t.changetype between 1 and 3;countryid:" + this.manager.getCountryId());
/*  90 */           List list2 = this.setManager.getScheduleSetDetailList(hrmScheduleSetPerson.getField001().longValue(), hrmScheduleShiftsSet, str2, str3, list1);
/*  91 */           this.detailManager.deleteBeforeSaveSchedulePerson(hrmScheduleSetPerson.getField001(), str2, str3, str1);
/*  92 */           for (HrmScheduleSetDetail hrmScheduleSetDetail : list2) {
/*  93 */             this.detailManager.saveSchedulePerson(hrmScheduleSetDetail, str1);
/*     */           }
/*     */         } 
/*     */       } 
/*  97 */     } else if (str.equals("clean")) {
/*  98 */       long l = StringUtil.parseToLong(paramHttpServletRequest.getParameter("sId"), 0L);
/*  99 */       String str1 = StringUtil.vString(paramHttpServletRequest.getParameter("startDate"));
/* 100 */       String str2 = StringUtil.vString(paramHttpServletRequest.getParameter("endDate"));
/* 101 */       HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 102 */       hashMap.put("field001", Long.valueOf(l));
/* 103 */       hashMap.put("sql_field003", "and field003 between '" + str1 + "' and '" + str2 + "' ");
/* 104 */       hashMap.put("delflag", Integer.valueOf(0));
/* 105 */       this.detailManager.delete(hashMap);
/*     */     } 
/* 107 */     if (bool) (new HrmScheduleSetPersonCache()).remove(); 
/* 108 */     return jSONObject;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*     */ 
/*     */   
/*     */   protected String getValue(int paramInt, HrmScheduleSetPerson paramHrmScheduleSetPerson) {
/* 116 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleSetPersonController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */