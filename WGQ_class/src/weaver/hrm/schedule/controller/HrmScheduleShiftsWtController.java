/*    */ package weaver.hrm.schedule.controller;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*    */ import org.json.JSONObject;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.framework.BaseController;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.schedule.cache.HrmScheduleShiftsWtCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleShiftsWt;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleShiftsWtManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftsWtController
/*    */   extends BaseController<HrmScheduleShiftsWt>
/*    */ {
/* 25 */   private HrmScheduleShiftsWtManager manager = null;
/*    */   
/*    */   public HrmScheduleShiftsWtController() {
/* 28 */     this.manager = new HrmScheduleShiftsWtManager();
/* 29 */     setManager((BaseManager)this.manager);
/*    */   }
/*    */   
/*    */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 33 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/*    */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException {
/* 37 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/* 38 */     JSONObject jSONObject = new JSONObject();
/* 39 */     boolean bool = true;
/* 40 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/* 41 */     if (str.equals("save")) {
/* 42 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 43 */       HrmScheduleShiftsWt hrmScheduleShiftsWt = (long_.longValue() != 0L) ? (HrmScheduleShiftsWt)this.manager.get(long_) : null;
/* 44 */       boolean bool1 = (hrmScheduleShiftsWt == null) ? true : false;
/* 45 */       hrmScheduleShiftsWt = bool1 ? new HrmScheduleShiftsWt() : hrmScheduleShiftsWt;
/* 46 */       hrmScheduleShiftsWt.setField001(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field001"))));
/* 47 */       hrmScheduleShiftsWt.setField002(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field002"))));
/* 48 */       hrmScheduleShiftsWt.setField003(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field003"))));
/* 49 */       this.manager.save(hrmScheduleShiftsWt, bool1);
/* 50 */     } else if (str.equals("delete")) {
/* 51 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/*    */     } 
/* 53 */     if (bool) (new HrmScheduleShiftsWtCache()).remove(); 
/* 54 */     return jSONObject;
/*    */   }
/*    */ 
/*    */   
/*    */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*    */ 
/*    */   
/*    */   protected String getValue(int paramInt, HrmScheduleShiftsWt paramHrmScheduleShiftsWt) {
/* 62 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleShiftsWtController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */