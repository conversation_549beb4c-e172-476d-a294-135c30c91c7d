/*    */ package weaver.hrm.schedule.controller;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*    */ import org.json.JSONObject;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.framework.BaseController;
/*    */ import weaver.framework.BaseManager;
/*    */ import weaver.hrm.schedule.cache.HrmScheduleSetDetailCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleSetDetailManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleSetDetailController
/*    */   extends BaseController<HrmScheduleSetDetail>
/*    */ {
/* 24 */   private HrmScheduleSetDetailManager manager = null;
/*    */   
/*    */   public HrmScheduleSetDetailController() {
/* 27 */     this.manager = new HrmScheduleSetDetailManager();
/* 28 */     setManager((BaseManager)this.manager);
/*    */   }
/*    */   
/*    */   private void initController(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 32 */     this.manager.set(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/*    */   public JSONObject handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException {
/* 36 */     initController(paramHttpServletRequest, paramHttpServletResponse);
/* 37 */     JSONObject jSONObject = new JSONObject();
/* 38 */     boolean bool = true;
/* 39 */     String str = StringUtil.getURLDecode(paramHttpServletRequest.getParameter("cmd"));
/* 40 */     if (str.equals("save")) {
/* 41 */       Long long_ = Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("id"), 0L));
/* 42 */       HrmScheduleSetDetail hrmScheduleSetDetail = (long_.longValue() != 0L) ? (HrmScheduleSetDetail)this.manager.get(long_) : null;
/* 43 */       boolean bool1 = (hrmScheduleSetDetail == null) ? true : false;
/* 44 */       hrmScheduleSetDetail = bool1 ? new HrmScheduleSetDetail() : hrmScheduleSetDetail;
/* 45 */       hrmScheduleSetDetail.setField001(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field001")));
/* 46 */       hrmScheduleSetDetail.setField002(Long.valueOf(StringUtil.parseToLong(paramHttpServletRequest.getParameter("field002"))));
/* 47 */       hrmScheduleSetDetail.setField003(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field003")));
/* 48 */       hrmScheduleSetDetail.setField004(Integer.valueOf(StringUtil.parseToInt(paramHttpServletRequest.getParameter("field004"))));
/* 49 */       hrmScheduleSetDetail.setField005(StringUtil.getURLDecode(paramHttpServletRequest.getParameter("field005")));
/* 50 */       this.manager.save(hrmScheduleSetDetail, bool1);
/* 51 */     } else if (str.equals("delete")) {
/* 52 */       this.manager.delete(StringUtil.vString(paramHttpServletRequest.getParameter("ids")));
/*    */     } 
/* 54 */     if (bool) (new HrmScheduleSetDetailCache()).remove(); 
/* 55 */     return jSONObject;
/*    */   }
/*    */ 
/*    */   
/*    */   protected void saveBean(HSSFRow paramHSSFRow) {}
/*    */ 
/*    */   
/*    */   protected String getValue(int paramInt, HrmScheduleSetDetail paramHrmScheduleSetDetail) {
/* 63 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/controller/HrmScheduleSetDetailController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */