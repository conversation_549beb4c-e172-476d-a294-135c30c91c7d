/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.io.FileInputStream;
/*     */ import java.io.InputStream;
/*     */ import java.text.DateFormat;
/*     */ import java.util.Vector;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDateUtil;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*     */ import org.apache.poi.ss.usermodel.Cell;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.AESCoder;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class HrmPaidSickImport extends BaseBean {
/*  22 */   public int celltype = 8;
/*  23 */   private String cellValue = "";
/*  24 */   private Vector msg1 = new Vector();
/*  25 */   private Vector msg2 = new Vector();
/*  26 */   private String isaesencrypt = "";
/*  27 */   private String aescode = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getCellValue(HSSFCell paramHSSFCell, HSSFRow paramHSSFRow) {
/*  38 */     this.cellValue = "";
/*  39 */     switch (paramHSSFCell.getCellType()) {
/*     */       
/*     */       case NUMERIC:
/*  42 */         if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/*  43 */           this.cellValue = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString(); break;
/*     */         } 
/*  45 */         this.cellValue = String.valueOf(paramHSSFCell.getNumericCellValue());
/*     */         break;
/*     */ 
/*     */       
/*     */       case STRING:
/*  50 */         this.cellValue = paramHSSFCell.getStringCellValue();
/*     */         break;
/*     */       case FORMULA:
/*  53 */         this.cellValue = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString();
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  60 */     this.cellValue = Util.null2String(this.cellValue).trim();
/*  61 */     return this.cellValue;
/*     */   }
/*     */   
/*     */   public int getUserId(String paramString) {
/*  65 */     int i = 0;
/*  66 */     String str = " select id from hrmresource where workcode='" + paramString + "' ";
/*  67 */     RecordSet recordSet = new RecordSet();
/*  68 */     recordSet.execute(str);
/*  69 */     if (recordSet.next()) {
/*  70 */       i = recordSet.getInt("id");
/*     */     }
/*  72 */     return i;
/*     */   }
/*     */   public void ExcelToDB(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  75 */     ExcelToDB(paramString1, paramString2, paramString3, paramString4, "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void ExcelToDB(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*     */     try {
/*  84 */       FileInputStream fileInputStream = new FileInputStream(paramString1);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  89 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/*  90 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/*  91 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/*  93 */       fileInputStream.close();
/*  94 */       HSSFRow hSSFRow = null;
/*     */       
/*  96 */       int i = hSSFSheet.getLastRowNum();
/*     */       
/*  98 */       for (byte b = 1; b < i + 1; b++) {
/*     */         
/* 100 */         hSSFRow = hSSFSheet.getRow(b);
/* 101 */         HSSFCell hSSFCell1 = null;
/* 102 */         HSSFCell hSSFCell2 = null;
/* 103 */         HSSFCell hSSFCell3 = null;
/* 104 */         HSSFCell hSSFCell4 = null;
/* 105 */         HSSFCell hSSFCell5 = null;
/*     */         
/* 107 */         String str1 = "";
/* 108 */         String str2 = "";
/* 109 */         hSSFCell1 = hSSFRow.getCell(0);
/* 110 */         if (hSSFCell1 == null) {
/* 111 */           hSSFCell5 = hSSFRow.getCell(1);
/* 112 */           str2 = getCellValue(hSSFCell5, hSSFRow);
/*     */         } else {
/* 114 */           str1 = getCellValue(hSSFCell1, hSSFRow);
/*     */         } 
/*     */         
/* 117 */         if ("".equals(str1) && !"".equals(str2)) {
/* 118 */           str1 = "" + getUserId(str2);
/*     */         }
/* 120 */         hSSFCell2 = hSSFRow.getCell(3);
/* 121 */         String str3 = getCellValue(hSSFCell2, hSSFRow);
/*     */         
/* 123 */         hSSFCell3 = hSSFRow.getCell(7);
/* 124 */         String str4 = getCellValue(hSSFCell3, hSSFRow);
/*     */         
/* 126 */         hSSFCell4 = hSSFRow.getCell(8);
/* 127 */         String str5 = getCellValue(hSSFCell4, hSSFRow);
/*     */         
/* 129 */         String str6 = "delete from HrmPSLManagement where resourceid = " + str1 + " and pslyear = " + str4 + " and leavetype=" + paramString5;
/* 130 */         RecordSet recordSet = new RecordSet();
/* 131 */         recordSet.executeSql(str6);
/* 132 */         str6 = "insert into HrmPSLManagement(resourceid,pslyear,psldays,status,leavetype) values('" + str1 + "','" + str4 + "','" + str5 + "',1," + paramString5 + ")";
/* 133 */         recordSet.executeSql(str6);
/*     */       } 
/* 135 */     } catch (Exception exception) {
/* 136 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void ScanFile(String paramString) {
/*     */     try {
/* 147 */       InputStream inputStream = new FileInputStream(paramString);
/* 148 */       if (this.isaesencrypt.equals("1")) {
/* 149 */         inputStream = AESCoder.decrypt(inputStream, this.aescode);
/*     */       }
/* 151 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(inputStream);
/* 152 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/* 153 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*     */       
/* 155 */       inputStream.close();
/* 156 */       HSSFRow hSSFRow = null;
/*     */       
/* 158 */       int i = hSSFSheet.getLastRowNum();
/* 159 */       for (byte b = 1; b < i + 1; b++) {
/*     */         
/* 161 */         hSSFRow = hSSFSheet.getRow(b);
/* 162 */         HSSFCell hSSFCell1 = null;
/* 163 */         HSSFCell hSSFCell2 = null;
/* 164 */         HSSFCell hSSFCell3 = null;
/* 165 */         HSSFCell hSSFCell4 = null;
/* 166 */         HSSFCell hSSFCell5 = null;
/*     */         
/* 168 */         String str1 = "";
/* 169 */         String str2 = "";
/* 170 */         hSSFCell1 = hSSFRow.getCell(0);
/* 171 */         if (hSSFCell1 == null) {
/* 172 */           hSSFCell2 = hSSFRow.getCell(1);
/* 173 */           str2 = getCellValue(hSSFCell2, hSSFRow);
/*     */         } else {
/* 175 */           str1 = getCellValue(hSSFCell1, hSSFRow);
/*     */         } 
/*     */         
/* 178 */         hSSFCell3 = hSSFRow.getCell(3);
/* 179 */         String str3 = getCellValue(hSSFCell3, hSSFRow);
/*     */         
/* 181 */         hSSFCell4 = hSSFRow.getCell(7);
/* 182 */         String str4 = getCellValue(hSSFCell4, hSSFRow);
/*     */         
/* 184 */         hSSFCell5 = hSSFRow.getCell(8);
/* 185 */         String str5 = getCellValue(hSSFCell5, hSSFRow);
/*     */         
/* 187 */         if (!checkPaidSickData(str1, "int") || 
/* 188 */           !"".equals(StringUtil.vString(str2, "")))
/*     */         
/*     */         { 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 195 */           if (checkPaidSickData(str4, "int") || str4.length() != 4)
/* 196 */           { this.msg1.add((b + 1) + "");
/* 197 */             this.msg2.add("6");
/*     */              }
/*     */           
/* 200 */           else if (checkPaidSickData(str5, "float"))
/* 201 */           { this.msg1.add((b + 1) + "");
/* 202 */             this.msg2.add("7"); }  }
/*     */         else { this.msg1.add((b + 1) + ""); this.msg2.add("1"); }
/*     */       
/*     */       } 
/* 206 */     } catch (Exception exception) {
/* 207 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean checkPaidSickData(String paramString1, String paramString2) {
/* 219 */     boolean bool = false;
/*     */     
/* 221 */     if (paramString2.equals("int"))
/* 222 */       try { Integer.parseInt(paramString1); } catch (Exception exception) { bool = true; }
/*     */        
/* 224 */     if (paramString2.equals("float"))
/* 225 */       try { Float.parseFloat(paramString1); } catch (Exception exception) { bool = true; }
/*     */        
/* 227 */     return bool;
/*     */   }
/*     */   
/*     */   public boolean initCheck(String paramString) {
/*     */     try {
/* 232 */       FileInputStream fileInputStream = new FileInputStream(paramString);
/* 233 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/* 234 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/* 235 */       return true;
/* 236 */     } catch (Exception exception) {
/* 237 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   public Vector getMsg1() {
/* 242 */     return this.msg1;
/*     */   }
/*     */   
/*     */   public void setMsg1(Vector paramVector) {
/* 246 */     this.msg1 = paramVector;
/*     */   }
/*     */   
/*     */   public Vector getMsg2() {
/* 250 */     return this.msg2;
/*     */   }
/*     */   
/*     */   public void setMsg2(Vector paramVector) {
/* 254 */     this.msg2 = paramVector;
/*     */   }
/*     */   public String getAescode() {
/* 257 */     return this.aescode;
/*     */   }
/*     */   
/*     */   public void setAescode(String paramString) {
/* 261 */     this.aescode = paramString;
/*     */   }
/*     */   
/*     */   public String getIsaesencrypt() {
/* 265 */     return this.isaesencrypt;
/*     */   }
/*     */   
/*     */   public void setIsaesencrypt(String paramString) {
/* 269 */     this.isaesencrypt = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmPaidSickImport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */