/*    */ package weaver.hrm.schedule;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.LogMan;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleDiffComInfo
/*    */ {
/* 13 */   LogMan lm = LogMan.getInstance();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getDiffname(String paramString) {
/*    */     try {
/* 22 */       RecordSet recordSet = new RecordSet();
/* 23 */       String str = "select diffname from HrmScheduleDiff where id = '" + paramString + "'";
/* 24 */       recordSet.executeSql(str);
/* 25 */       recordSet.next();
/* 26 */       return Util.null2String(recordSet.getString("diffname"));
/* 27 */     } catch (Exception exception) {
/* 28 */       this.lm.writeLog(exception);
/*    */       
/* 30 */       return "";
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getColor(String paramString) {
/*    */     try {
/* 40 */       RecordSet recordSet = new RecordSet();
/* 41 */       String str = "select color from HrmScheduleDiff where id = " + paramString;
/* 42 */       recordSet.executeSql(str);
/* 43 */       recordSet.next();
/* 44 */       return Util.null2String(recordSet.getString("color"));
/* 45 */     } catch (Exception exception) {
/* 46 */       this.lm.writeLog(exception);
/*    */       
/* 48 */       return "white";
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getDifftype(String paramString) {
/*    */     try {
/* 58 */       RecordSet recordSet = new RecordSet();
/* 59 */       String str = "select difftype from HrmScheduleDiff where id = '" + paramString + "'";
/* 60 */       recordSet.executeSql(str);
/* 61 */       recordSet.next();
/* 62 */       return Util.getIntValue(recordSet.getString("difftype"), 9);
/* 63 */     } catch (Exception exception) {
/* 64 */       this.lm.writeLog(exception);
/*    */       
/* 66 */       return 9;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmScheduleDiffComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */