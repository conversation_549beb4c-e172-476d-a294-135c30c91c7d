/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmTimeCardInit
/*     */   extends BaseBean
/*     */ {
/*  24 */   private RecordSet rt = null;
/*  25 */   char separator = Util.getSeparator();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initTimecardDate() {
/*  31 */     Calendar calendar = Calendar.getInstance();
/*  32 */     String str1 = "2000-01-01";
/*     */     
/*  34 */     calendar.add(5, -1);
/*  35 */     String str2 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */ 
/*     */     
/*  39 */     initTimecardInfo(str1, str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initTimecardInfo(String paramString1, String paramString2) {
/*  49 */     RecordSet recordSet = new RecordSet();
/*  50 */     Calendar calendar = Calendar.getInstance();
/*     */ 
/*     */     
/*  53 */     HrmKqSystemComInfo hrmKqSystemComInfo = new HrmKqSystemComInfo();
/*  54 */     boolean bool = hrmKqSystemComInfo.getBlurshift().equals("1");
/*  55 */     int i = hrmKqSystemComInfo.getBlurminitus();
/*     */     
/*  57 */     ResourceComInfo resourceComInfo = null;
/*     */     
/*  59 */     if (bool) {
/*     */       try {
/*  61 */         resourceComInfo = new ResourceComInfo();
/*     */       }
/*  63 */       catch (Exception exception) {}
/*     */     }
/*     */ 
/*     */     
/*  67 */     ArrayList<String> arrayList1 = new ArrayList();
/*  68 */     ArrayList<String> arrayList2 = null;
/*  69 */     if (bool) arrayList2 = new ArrayList(); 
/*  70 */     recordSet.executeSql("  select resourceid from HrmArrangeShiftSet ");
/*  71 */     while (recordSet.next()) {
/*  72 */       String str1 = Util.null2String(recordSet.getString("resourceid"));
/*  73 */       arrayList1.add(str1);
/*  74 */       if (bool) arrayList2.add(resourceComInfo.getDepartmentID(str1));
/*     */     
/*     */     } 
/*     */ 
/*     */     
/*  79 */     ArrayList<String> arrayList3 = new ArrayList();
/*  80 */     ArrayList<String> arrayList4 = new ArrayList();
/*  81 */     ArrayList<String> arrayList5 = new ArrayList();
/*  82 */     ArrayList<String> arrayList6 = new ArrayList();
/*     */     
/*  84 */     recordSet.executeProc("HrmArrangeShift_Select", "");
/*  85 */     while (recordSet.next()) {
/*  86 */       String str1 = Util.null2String(recordSet.getString("id"));
/*  87 */       String str2 = Util.null2String(recordSet.getString("shiftbegintime"));
/*  88 */       String str3 = Util.null2String(recordSet.getString("shiftendtime"));
/*  89 */       arrayList3.add(str1);
/*  90 */       arrayList4.add(str2);
/*  91 */       arrayList5.add(str3);
/*  92 */       if (str2.compareTo(str3) <= 0) { arrayList6.add("0"); continue; }
/*  93 */        arrayList6.add("1");
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  98 */     ArrayList<String> arrayList7 = new ArrayList();
/*  99 */     ArrayList<ArrayList<String>> arrayList8 = new ArrayList();
/* 100 */     ArrayList<String> arrayList9 = new ArrayList();
/*     */     
/* 102 */     recordSet.executeSql(" select * from HrmRightCardInfo where carddate>='" + paramString1 + "' and carddate<='" + paramString2 + "' and islegal = 0 order by carddate, resourceid, workout , cardtime ");
/*     */     
/* 104 */     while (recordSet.next()) {
/* 105 */       String str1 = Util.null2String(recordSet.getString("resourceid"));
/* 106 */       String str2 = Util.null2String(recordSet.getString("carddate"));
/* 107 */       String str3 = Util.null2String(recordSet.getString("cardtime"));
/* 108 */       int n = Util.getIntValue(recordSet.getString("workout"), 0);
/*     */ 
/*     */       
/* 111 */       int i1 = arrayList7.indexOf(str1 + "_" + str2);
/* 112 */       if (i1 == -1) {
/* 113 */         arrayList7.add(str1 + "_" + str2);
/* 114 */         ArrayList<String> arrayList12 = new ArrayList();
/* 115 */         arrayList12.add(str3);
/* 116 */         arrayList8.add(arrayList12);
/* 117 */         if (n != 2) { arrayList9.add("0"); continue; }
/* 118 */          arrayList9.add("1");
/*     */         continue;
/*     */       } 
/* 121 */       ArrayList<String> arrayList = arrayList8.get(i1);
/* 122 */       arrayList.add(str3);
/* 123 */       arrayList8.set(i1, arrayList);
/* 124 */       if (n == 2) {
/* 125 */         int i2 = Util.getIntValue(arrayList9.get(i1), 0);
/* 126 */         arrayList9.set(i1, "" + (i2 + 1));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 133 */     if (bool) {
/* 134 */       HrmTimeShiftInit hrmTimeShiftInit = new HrmTimeShiftInit();
/*     */       
/* 136 */       hrmTimeShiftInit.setFromDate(paramString1);
/* 137 */       hrmTimeShiftInit.setEndDate(paramString2);
/* 138 */       hrmTimeShiftInit.setReesourceshifts(arrayList1);
/* 139 */       hrmTimeShiftInit.setReesourcedeparts(arrayList2);
/* 140 */       hrmTimeShiftInit.setShiftids(arrayList3);
/* 141 */       hrmTimeShiftInit.setShiftbegintimes(arrayList4);
/* 142 */       hrmTimeShiftInit.setShiftendtimes(arrayList5);
/* 143 */       hrmTimeShiftInit.setShifttypes(arrayList6);
/* 144 */       hrmTimeShiftInit.setReesourcecarddates(arrayList7);
/* 145 */       hrmTimeShiftInit.setCardtimes(arrayList8);
/* 146 */       hrmTimeShiftInit.setCardworkouts(arrayList9);
/* 147 */       hrmTimeShiftInit.setBlurminitus(i);
/*     */       
/* 149 */       hrmTimeShiftInit.doBlurShift();
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 155 */     ArrayList<String> arrayList10 = new ArrayList();
/* 156 */     ArrayList<ArrayList<String>> arrayList11 = new ArrayList();
/*     */     
/* 158 */     int j = Util.getIntValue(paramString1.substring(0, 4));
/* 159 */     int k = Util.getIntValue(paramString1.substring(5, 7));
/* 160 */     int m = Util.getIntValue(paramString1.substring(8, 10));
/*     */     
/* 162 */     calendar.set(j, k - 1, m);
/* 163 */     calendar.add(5, -1);
/* 164 */     String str = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */     
/* 167 */     recordSet.executeSql(" select a.* from HrmArrangeShiftInfo a , HrmArrangeShift b where  a.shiftid=b.id and  a.shiftdate >= '" + str + "' and a.shiftdate <= '" + paramString2 + "' " + " order by a.shiftdate, a.resourceid , b.shiftbegintime ");
/*     */ 
/*     */ 
/*     */     
/* 171 */     while (recordSet.next()) {
/* 172 */       String str1 = Util.null2String(recordSet.getString("resourceid"));
/* 173 */       String str2 = Util.null2String(recordSet.getString("shiftdate"));
/* 174 */       String str3 = Util.null2String(recordSet.getString("shiftid"));
/*     */       
/* 176 */       int n = arrayList10.indexOf(str1 + "_" + str2);
/* 177 */       if (n == -1) {
/* 178 */         arrayList10.add(str1 + "_" + str2);
/* 179 */         ArrayList<String> arrayList12 = new ArrayList();
/* 180 */         arrayList12.add(str3);
/* 181 */         arrayList11.add(arrayList12);
/*     */         continue;
/*     */       } 
/* 184 */       ArrayList<String> arrayList = arrayList11.get(n);
/* 185 */       arrayList.add(str3);
/* 186 */       arrayList11.set(n, arrayList);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 191 */     for (byte b = 0; b < arrayList7.size(); b++) {
/* 192 */       String str1 = "";
/* 193 */       String str2 = "";
/* 194 */       String str3 = arrayList7.get(b);
/* 195 */       String[] arrayOfString = Util.TokenizerString2(str3, "_");
/* 196 */       String str4 = arrayOfString[0];
/* 197 */       String str5 = arrayOfString[1];
/* 198 */       ArrayList<String> arrayList = arrayList8.get(b);
/* 199 */       int n = Util.getIntValue(arrayList9.get(b), 0);
/*     */ 
/*     */       
/* 202 */       int i1 = arrayList1.indexOf(str4);
/*     */       
/* 204 */       if (i1 == -1) {
/* 205 */         if (arrayList.size() < 2) {
/* 206 */           String str6 = arrayList.get(0);
/* 207 */           if (str6.compareTo("12:00") < 0) { str1 = str6; }
/* 208 */           else { str2 = str6; }
/*     */           
/* 210 */           String str7 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + "0" + this.separator + "0";
/* 211 */           recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */         }
/* 213 */         else if (arrayList.size() == 2) {
/* 214 */           str1 = arrayList.get(0);
/* 215 */           str2 = arrayList.get(1);
/* 216 */           String str6 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + "0" + this.separator + "0";
/* 217 */           recordSet.executeProc("HrmTimecardInfo_Update", str6);
/*     */         }
/* 219 */         else if (arrayList.size() > 2) {
/* 220 */           if (arrayList.size() - n <= 2) {
/* 221 */             int i2; for (i2 = 0; i2 < arrayList.size(); i2 += 2) {
/* 222 */               if (i2 < arrayList.size()) {
/* 223 */                 str1 = arrayList.get(i2);
/* 224 */                 if (i2 + 1 >= arrayList.size()) { str2 = ""; }
/* 225 */                 else { str2 = arrayList.get(i2 + 1); }
/*     */                 
/* 227 */                 String str6 = "";
/* 228 */                 if (i2 == 0) { str6 = "0"; }
/* 229 */                 else { str6 = "-1"; }
/*     */                 
/* 231 */                 String str7 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + str6 + this.separator + "0";
/* 232 */                 recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */               } 
/*     */             } 
/*     */           } else {
/* 236 */             recordSet.executeSql(" update HrmRightCardInfo set islegal = 2 where resourceid =" + str4 + " and carddate = '" + str5 + "' ");
/*     */           } 
/*     */         } 
/*     */       } else {
/*     */         
/* 241 */         ArrayList<String> arrayList12 = new ArrayList();
/* 242 */         ArrayList<String> arrayList13 = new ArrayList();
/* 243 */         ArrayList<String> arrayList14 = new ArrayList();
/*     */ 
/*     */         
/* 246 */         int i2 = Util.getIntValue(str5.substring(0, 4));
/* 247 */         int i3 = Util.getIntValue(str5.substring(5, 7));
/* 248 */         int i4 = Util.getIntValue(str5.substring(8, 10));
/* 249 */         calendar.set(i2, i3 - 1, i4);
/* 250 */         calendar.add(5, -1);
/* 251 */         String str6 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */ 
/*     */         
/* 255 */         int i5 = arrayList10.indexOf(str4 + "_" + str6);
/* 256 */         if (i5 != -1) {
/* 257 */           ArrayList<String> arrayList15 = arrayList11.get(i5);
/* 258 */           for (byte b1 = 0; b1 < arrayList15.size(); b1++) {
/* 259 */             String str7 = arrayList15.get(b1);
/* 260 */             int i7 = arrayList3.indexOf(str7);
/* 261 */             if (i7 != -1) {
/* 262 */               String str8 = arrayList6.get(i7);
/* 263 */               if (str8.equals("1")) {
/* 264 */                 String str9 = arrayList5.get(i7);
/* 265 */                 arrayList12.add(str9);
/* 266 */                 arrayList13.add("1");
/* 267 */                 arrayList14.add(str7);
/*     */ 
/*     */                 
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 276 */         int i6 = arrayList10.indexOf(str4 + "_" + str5);
/* 277 */         if (i6 != -1) {
/* 278 */           ArrayList<String> arrayList15 = arrayList11.get(i6);
/* 279 */           for (byte b1 = 0; b1 < arrayList15.size(); b1++) {
/* 280 */             String str7 = arrayList15.get(b1);
/* 281 */             int i7 = arrayList3.indexOf(str7);
/* 282 */             if (i7 != -1) {
/* 283 */               String str8 = arrayList6.get(i7);
/* 284 */               if (str8.equals("1")) {
/* 285 */                 String str9 = arrayList4.get(i7);
/* 286 */                 arrayList12.add(str9);
/* 287 */                 arrayList13.add("2");
/* 288 */                 arrayList14.add(str7);
/*     */               } else {
/*     */                 
/* 291 */                 String str9 = arrayList4.get(i7);
/* 292 */                 String str10 = arrayList5.get(i7);
/* 293 */                 arrayList12.add(str9);
/* 294 */                 arrayList12.add(str10);
/* 295 */                 arrayList13.add("0");
/* 296 */                 arrayList13.add("0");
/* 297 */                 arrayList14.add(str7);
/* 298 */                 arrayList14.add(str7);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 304 */         if (arrayList12.size() == 0) {
/* 305 */           if (arrayList.size() <= 2) {
/* 306 */             if (arrayList.size() < 2) {
/* 307 */               String str8 = arrayList.get(0);
/* 308 */               if (str8.compareTo("12:00") < 0) { str1 = str8; }
/* 309 */               else { str2 = str8; }
/*     */             
/*     */             } else {
/* 312 */               str1 = arrayList.get(0);
/* 313 */               str2 = arrayList.get(1);
/*     */             } 
/*     */             
/* 316 */             String str7 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + "-1" + this.separator + "0";
/* 317 */             recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */           } else {
/*     */             
/* 320 */             recordSet.executeSql(" update HrmRightCardInfo set islegal = 2 where resourceid =" + str4 + " and carddate = '" + str5 + "' ");
/*     */           } 
/*     */         } else {
/*     */           
/* 324 */           int i7 = arrayList.size();
/* 325 */           int i8 = arrayList12.size();
/*     */           
/* 327 */           if (i7 <= i8) {
/*     */             
/* 329 */             if (i7 < i8) {
/*     */               
/* 331 */               for (byte b1 = 0; arrayList.size() < i8; b1++) {
/* 332 */                 if (b1 >= i7) {
/* 333 */                   arrayList.add("");
/*     */                 }
/*     */                 else {
/*     */                   
/* 337 */                   String str7 = arrayList12.get(b1);
/* 338 */                   String str8 = arrayList.get(b1);
/*     */                   
/* 340 */                   if (Math.abs(Util.timediff1(str7, str8)) > 240) {
/* 341 */                     arrayList.add("");
/* 342 */                     for (int i10 = arrayList.size() - 1; i10 > b1; i10--) {
/* 343 */                       arrayList.set(i10, arrayList.get(i10 - 1));
/*     */                     }
/* 345 */                     arrayList.set(b1, "");
/*     */                   } 
/*     */                 } 
/* 348 */               }  i7 = arrayList.size();
/*     */             } 
/*     */ 
/*     */             
/* 352 */             for (int i9 = 0; i9 < i7; i9++) {
/* 353 */               String str7 = arrayList13.get(i9);
/* 354 */               String str8 = arrayList14.get(i9);
/*     */               
/* 356 */               if (str7.equals("1")) {
/* 357 */                 str1 = "";
/* 358 */                 str2 = arrayList.get(i9);
/*     */                 
/* 360 */                 String str9 = str4 + this.separator + str6 + this.separator + str1 + this.separator + str2 + this.separator + str8 + this.separator + "1";
/* 361 */                 recordSet.executeProc("HrmTimecardInfo_Update", str9);
/*     */               }
/* 363 */               else if (str7.equals("2")) {
/* 364 */                 str1 = arrayList.get(i9);
/* 365 */                 str2 = "";
/*     */                 
/* 367 */                 String str9 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + str8 + this.separator + "2";
/* 368 */                 recordSet.executeProc("HrmTimecardInfo_Update", str9);
/*     */               } else {
/*     */                 
/* 371 */                 str1 = arrayList.get(i9);
/* 372 */                 i9++;
/* 373 */                 if (i9 < i7) { str2 = arrayList.get(i9); }
/* 374 */                 else { str2 = ""; }
/*     */                 
/* 376 */                 String str9 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + str8 + this.separator + "0";
/* 377 */                 recordSet.executeProc("HrmTimecardInfo_Update", str9);
/*     */               }
/*     */             
/*     */             }
/*     */           
/* 382 */           } else if (i7 - n <= i8) {
/* 383 */             for (int i9 = 0; i9 < i7; i9++) {
/* 384 */               if (i9 >= i8) {
/* 385 */                 str1 = arrayList.get(i9);
/* 386 */                 i9++;
/* 387 */                 if (i9 >= i7) { str2 = ""; }
/* 388 */                 else { str2 = arrayList.get(i9); }
/*     */ 
/*     */                 
/* 391 */                 String str7 = "-1";
/*     */                 
/* 393 */                 String str8 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + str7 + this.separator + "0";
/* 394 */                 recordSet.executeProc("HrmTimecardInfo_Update", str8);
/*     */               } else {
/*     */                 
/* 397 */                 String str7 = arrayList13.get(i9);
/* 398 */                 String str8 = arrayList14.get(i9);
/*     */                 
/* 400 */                 if (str7.equals("1")) {
/* 401 */                   str1 = "";
/* 402 */                   str2 = arrayList.get(i9);
/* 403 */                   String str9 = str4 + this.separator + str6 + this.separator + str1 + this.separator + str2 + this.separator + str8 + this.separator + "1";
/* 404 */                   recordSet.executeProc("HrmTimecardInfo_Update", str9);
/*     */                 }
/* 406 */                 else if (str7.equals("2")) {
/* 407 */                   str1 = arrayList.get(i9);
/* 408 */                   str2 = "";
/* 409 */                   String str9 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + str8 + this.separator + "2";
/* 410 */                   recordSet.executeProc("HrmTimecardInfo_Update", str9);
/*     */                 } else {
/*     */                   
/* 413 */                   str1 = arrayList.get(i9);
/* 414 */                   i9++;
/* 415 */                   if (i9 < i7) { str2 = arrayList.get(i9); }
/* 416 */                   else { str2 = ""; }
/*     */                   
/* 418 */                   String str9 = str4 + this.separator + str5 + this.separator + str1 + this.separator + str2 + this.separator + str8 + this.separator + "0";
/* 419 */                   recordSet.executeProc("HrmTimecardInfo_Update", str9);
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } else {
/*     */             
/* 425 */             recordSet.executeSql(" update HrmRightCardInfo set islegal = 2 where resourceid =" + str4 + " and carddate = '" + str5 + "' ");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 432 */     recordSet.executeSql(" update HrmRightCardInfo set islegal = 1 where carddate>='" + paramString1 + "' and carddate<='" + paramString2 + "' and islegal = 0 ");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initTimecardInfo(String paramString1, String paramString2, String paramString3) {
/* 444 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/* 447 */     boolean bool = false;
/* 448 */     recordSet.executeSql("  select resourceid from HrmArrangeShiftSet where resourceid = " + paramString1);
/* 449 */     if (recordSet.next()) bool = true;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 454 */     ArrayList<String> arrayList1 = new ArrayList();
/* 455 */     ArrayList<String> arrayList2 = new ArrayList();
/* 456 */     ArrayList<String> arrayList3 = new ArrayList();
/* 457 */     ArrayList<String> arrayList4 = new ArrayList();
/*     */     
/* 459 */     if (bool) {
/* 460 */       recordSet.executeProc("HrmArrangeShift_Select", "");
/* 461 */       while (recordSet.next()) {
/* 462 */         String str1 = Util.null2String(recordSet.getString("id"));
/* 463 */         String str2 = Util.null2String(recordSet.getString("shiftbegintime"));
/* 464 */         String str3 = Util.null2String(recordSet.getString("shiftendtime"));
/* 465 */         arrayList1.add(str1);
/* 466 */         arrayList2.add(str2);
/* 467 */         arrayList3.add(str3);
/* 468 */         if (str2.compareTo(str3) <= 0) { arrayList4.add("0"); continue; }
/* 469 */          arrayList4.add("1");
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 476 */     ArrayList<String> arrayList5 = new ArrayList();
/* 477 */     ArrayList<ArrayList<String>> arrayList6 = new ArrayList();
/* 478 */     Calendar calendar = Calendar.getInstance();
/*     */     
/* 480 */     if (bool) {
/* 481 */       int i = Util.getIntValue(paramString2.substring(0, 4));
/* 482 */       int j = Util.getIntValue(paramString2.substring(5, 7));
/* 483 */       int k = Util.getIntValue(paramString2.substring(8, 10));
/*     */       
/* 485 */       calendar.set(i, j - 1, k);
/* 486 */       calendar.add(5, -1);
/* 487 */       String str = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */       
/* 490 */       recordSet.executeSql(" select a.* from HrmArrangeShiftInfo a , HrmArrangeShift b where  a.shiftid=b.id and  a.shiftdate >= '" + str + "' and a.shiftdate <= '" + paramString3 + "' and a.resourceid = " + paramString1 + " order by a.shiftdate, b.shiftbegintime ");
/*     */ 
/*     */ 
/*     */       
/* 494 */       while (recordSet.next()) {
/* 495 */         String str1 = Util.null2String(recordSet.getString("shiftdate"));
/* 496 */         String str2 = Util.null2String(recordSet.getString("shiftid"));
/*     */         
/* 498 */         int m = arrayList5.indexOf(paramString1 + "_" + str1);
/* 499 */         if (m == -1) {
/* 500 */           arrayList5.add(paramString1 + "_" + str1);
/* 501 */           ArrayList<String> arrayList10 = new ArrayList();
/* 502 */           arrayList10.add(str2);
/* 503 */           arrayList6.add(arrayList10);
/*     */           continue;
/*     */         } 
/* 506 */         ArrayList<String> arrayList = arrayList6.get(m);
/* 507 */         arrayList.add(str2);
/* 508 */         arrayList6.set(m, arrayList);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 515 */     ArrayList<String> arrayList7 = new ArrayList();
/* 516 */     ArrayList<ArrayList<String>> arrayList8 = new ArrayList();
/* 517 */     ArrayList<String> arrayList9 = new ArrayList();
/*     */     
/* 519 */     recordSet.executeSql(" select * from HrmRightCardInfo where carddate>='" + paramString2 + "' and carddate<='" + paramString3 + "' and islegal = 0 and resourceid = " + paramString1 + " order by carddate, resourceid, workout , cardtime ");
/*     */     
/* 521 */     while (recordSet.next()) {
/* 522 */       String str1 = Util.null2String(recordSet.getString("carddate"));
/* 523 */       String str2 = Util.null2String(recordSet.getString("cardtime"));
/* 524 */       int i = Util.getIntValue(recordSet.getString("workout"), 0);
/*     */ 
/*     */       
/* 527 */       int j = arrayList7.indexOf(paramString1 + "_" + str1);
/* 528 */       if (j == -1) {
/* 529 */         arrayList7.add(paramString1 + "_" + str1);
/* 530 */         ArrayList<String> arrayList10 = new ArrayList();
/* 531 */         arrayList10.add(str2);
/* 532 */         arrayList8.add(arrayList10);
/* 533 */         if (i != 2) { arrayList9.add("0"); continue; }
/* 534 */          arrayList9.add("1");
/*     */         continue;
/*     */       } 
/* 537 */       ArrayList<String> arrayList = arrayList8.get(j);
/* 538 */       arrayList.add(str2);
/* 539 */       arrayList8.set(j, arrayList);
/* 540 */       if (i == 2) {
/* 541 */         int k = Util.getIntValue(arrayList9.get(j), 0);
/* 542 */         arrayList9.set(j, "" + (k + 1));
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 547 */     for (byte b = 0; b < arrayList7.size(); b++) {
/* 548 */       String str1 = "";
/* 549 */       String str2 = "";
/* 550 */       String str3 = arrayList7.get(b);
/* 551 */       String[] arrayOfString = Util.TokenizerString2(str3, "_");
/* 552 */       String str4 = arrayOfString[1];
/* 553 */       ArrayList<String> arrayList = arrayList8.get(b);
/* 554 */       int i = Util.getIntValue(arrayList9.get(b), 0);
/*     */       
/* 556 */       if (!bool) {
/* 557 */         if (arrayList.size() < 2) {
/* 558 */           String str5 = arrayList.get(0);
/* 559 */           if (str5.compareTo("12:00") < 0) { str1 = str5; }
/* 560 */           else { str2 = str5; }
/*     */           
/* 562 */           String str6 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + "0" + this.separator + "0";
/* 563 */           recordSet.executeProc("HrmTimecardInfo_Update", str6);
/*     */         }
/* 565 */         else if (arrayList.size() == 2) {
/* 566 */           str1 = arrayList.get(0);
/* 567 */           str2 = arrayList.get(1);
/* 568 */           String str = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + "0" + this.separator + "0";
/* 569 */           recordSet.executeProc("HrmTimecardInfo_Update", str);
/*     */         }
/* 571 */         else if (arrayList.size() > 2) {
/* 572 */           if (arrayList.size() - i <= 2) {
/* 573 */             int j; for (j = 0; j < arrayList.size(); j += 2) {
/* 574 */               if (j < arrayList.size()) {
/* 575 */                 str1 = arrayList.get(j);
/* 576 */                 if (j + 1 >= arrayList.size()) { str2 = ""; }
/* 577 */                 else { str2 = arrayList.get(j + 1); }
/*     */                 
/* 579 */                 String str5 = "";
/* 580 */                 if (j == 0) { str5 = "0"; }
/* 581 */                 else { str5 = "-1"; }
/*     */                 
/* 583 */                 String str6 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + str5 + this.separator + "0";
/* 584 */                 recordSet.executeProc("HrmTimecardInfo_Update", str6);
/*     */               } 
/*     */             } 
/*     */           } else {
/* 588 */             recordSet.executeSql(" update HrmRightCardInfo set islegal = 2 where resourceid =" + paramString1 + " and carddate = '" + str4 + "' ");
/*     */           }
/*     */         
/*     */         }
/*     */       
/*     */       } else {
/*     */         
/* 595 */         ArrayList<String> arrayList10 = new ArrayList();
/* 596 */         ArrayList<String> arrayList11 = new ArrayList();
/* 597 */         ArrayList<String> arrayList12 = new ArrayList();
/*     */ 
/*     */         
/* 600 */         int j = Util.getIntValue(str4.substring(0, 4));
/* 601 */         int k = Util.getIntValue(str4.substring(5, 7));
/* 602 */         int m = Util.getIntValue(str4.substring(8, 10));
/* 603 */         calendar.set(j, k - 1, m);
/* 604 */         calendar.add(5, -1);
/* 605 */         String str = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */ 
/*     */         
/* 609 */         int n = arrayList5.indexOf(paramString1 + "_" + str);
/* 610 */         if (n != -1) {
/* 611 */           ArrayList<String> arrayList13 = arrayList6.get(n);
/* 612 */           for (byte b1 = 0; b1 < arrayList13.size(); b1++) {
/* 613 */             String str5 = arrayList13.get(b1);
/* 614 */             int i2 = arrayList1.indexOf(str5);
/* 615 */             if (i2 != -1) {
/* 616 */               String str6 = arrayList4.get(i2);
/* 617 */               if (str6.equals("1")) {
/* 618 */                 String str7 = arrayList3.get(i2);
/* 619 */                 arrayList10.add(str7);
/* 620 */                 arrayList11.add("1");
/* 621 */                 arrayList12.add(str5);
/*     */ 
/*     */                 
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 630 */         int i1 = arrayList5.indexOf(paramString1 + "_" + str4);
/* 631 */         if (i1 != -1) {
/* 632 */           ArrayList<String> arrayList13 = arrayList6.get(i1);
/* 633 */           for (byte b1 = 0; b1 < arrayList13.size(); b1++) {
/* 634 */             String str5 = arrayList13.get(b1);
/* 635 */             int i2 = arrayList1.indexOf(str5);
/* 636 */             if (i2 != -1) {
/* 637 */               String str6 = arrayList4.get(i2);
/* 638 */               if (str6.equals("1")) {
/* 639 */                 String str7 = arrayList2.get(i2);
/* 640 */                 arrayList10.add(str7);
/* 641 */                 arrayList11.add("2");
/* 642 */                 arrayList12.add(str5);
/*     */               } else {
/*     */                 
/* 645 */                 String str7 = arrayList2.get(i2);
/* 646 */                 String str8 = arrayList3.get(i2);
/* 647 */                 arrayList10.add(str7);
/* 648 */                 arrayList10.add(str8);
/* 649 */                 arrayList11.add("0");
/* 650 */                 arrayList11.add("0");
/* 651 */                 arrayList12.add(str5);
/* 652 */                 arrayList12.add(str5);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 658 */         if (arrayList10.size() == 0) {
/* 659 */           if (arrayList.size() < 2) {
/* 660 */             String str6 = arrayList.get(0);
/* 661 */             if (str6.compareTo("12:00") < 0) { str1 = str6; }
/* 662 */             else { str2 = str6; }
/*     */           
/*     */           } else {
/* 665 */             str1 = arrayList.get(0);
/* 666 */             str2 = arrayList.get(1);
/*     */           } 
/*     */           
/* 669 */           String str5 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + "-1" + this.separator + "0";
/* 670 */           recordSet.executeProc("HrmTimecardInfo_Update", str5);
/*     */         } else {
/*     */           
/* 673 */           int i2 = arrayList.size();
/* 674 */           int i3 = arrayList10.size();
/*     */           
/* 676 */           if (i2 <= i3) {
/*     */             
/* 678 */             if (i2 < i3) {
/*     */               
/* 680 */               for (byte b1 = 0; arrayList.size() < i3; b1++) {
/* 681 */                 if (b1 >= i2) {
/* 682 */                   arrayList.add("");
/*     */                 }
/*     */                 else {
/*     */                   
/* 686 */                   String str5 = arrayList10.get(b1);
/* 687 */                   String str6 = arrayList.get(b1);
/*     */                   
/* 689 */                   if (Math.abs(Util.timediff1(str5, str6)) > 240) {
/* 690 */                     arrayList.add("");
/* 691 */                     for (int i5 = arrayList.size() - 1; i5 > b1; i5--)
/* 692 */                       arrayList.set(i5, arrayList.get(i5 - 1)); 
/* 693 */                     arrayList.set(b1, "");
/*     */                   } 
/*     */                 } 
/* 696 */               }  i2 = arrayList.size();
/*     */             } 
/*     */             
/* 699 */             for (int i4 = 0; i4 < i2; i4++) {
/* 700 */               String str5 = arrayList11.get(i4);
/* 701 */               String str6 = arrayList12.get(i4);
/*     */               
/* 703 */               if (str5.equals("1")) {
/* 704 */                 str1 = "";
/* 705 */                 str2 = arrayList.get(i4);
/* 706 */                 String str7 = paramString1 + this.separator + str + this.separator + str1 + this.separator + str2 + this.separator + str6 + this.separator + "1";
/* 707 */                 recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */               }
/* 709 */               else if (str5.equals("2")) {
/* 710 */                 str1 = arrayList.get(i4);
/* 711 */                 str2 = "";
/* 712 */                 String str7 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + str6 + this.separator + "2";
/* 713 */                 recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */               } else {
/*     */                 
/* 716 */                 str1 = arrayList.get(i4);
/* 717 */                 i4++;
/* 718 */                 if (i4 < i2) { str2 = arrayList.get(i4); }
/* 719 */                 else { str2 = ""; }
/*     */                 
/* 721 */                 String str7 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + str6 + this.separator + "0";
/* 722 */                 recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */               }
/*     */             
/*     */             }
/*     */           
/* 727 */           } else if (i2 - i <= i3) {
/* 728 */             for (int i4 = 0; i4 < i2; i4++) {
/* 729 */               if (i4 >= i3) {
/* 730 */                 str1 = arrayList.get(i4);
/* 731 */                 i4++;
/* 732 */                 if (i4 >= i2) { str2 = ""; }
/* 733 */                 else { str2 = arrayList.get(i4); }
/*     */ 
/*     */                 
/* 736 */                 String str5 = "-1";
/*     */                 
/* 738 */                 String str6 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + str5 + this.separator + "0";
/* 739 */                 recordSet.executeProc("HrmTimecardInfo_Update", str6);
/*     */               } else {
/*     */                 
/* 742 */                 String str5 = arrayList11.get(i4);
/* 743 */                 String str6 = arrayList12.get(i4);
/*     */                 
/* 745 */                 if (str5.equals("1")) {
/* 746 */                   str1 = "";
/* 747 */                   str2 = arrayList.get(i4);
/* 748 */                   String str7 = paramString1 + this.separator + str + this.separator + str1 + this.separator + str2 + this.separator + str6 + this.separator + "1";
/* 749 */                   recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */                 }
/* 751 */                 else if (str5.equals("2")) {
/* 752 */                   str1 = arrayList.get(i4);
/* 753 */                   str2 = "";
/* 754 */                   String str7 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + str6 + this.separator + "2";
/* 755 */                   recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */                 } else {
/*     */                   
/* 758 */                   str1 = arrayList.get(i4);
/* 759 */                   i4++;
/* 760 */                   if (i4 < i2) { str2 = arrayList.get(i4); }
/* 761 */                   else { str2 = ""; }
/*     */                   
/* 763 */                   String str7 = paramString1 + this.separator + str4 + this.separator + str1 + this.separator + str2 + this.separator + str6 + this.separator + "0";
/* 764 */                   recordSet.executeProc("HrmTimecardInfo_Update", str7);
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } else {
/*     */             
/* 770 */             recordSet.executeSql(" update HrmRightCardInfo set islegal = 2 where resourceid =" + paramString1 + " and carddate = '" + str4 + "' ");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 777 */     recordSet.executeSql(" update HrmRightCardInfo set islegal = 1 where carddate>='" + paramString2 + "' and carddate<='" + paramString3 + "' and islegal = 0 and resourceid = " + paramString1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmTimeCardInit.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */