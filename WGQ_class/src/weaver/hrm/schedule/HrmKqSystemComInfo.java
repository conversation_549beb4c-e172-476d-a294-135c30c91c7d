/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmKqSystemComInfo
/*     */   extends BaseBean
/*     */ {
/*  27 */   private RecordSet rt = null;
/*  28 */   private StaticObj staticobj = null;
/*     */   
/*  30 */   private String tosomeone = "";
/*  31 */   private String timeinterval = "";
/*  32 */   private String getdatatype = "";
/*  33 */   private String getdatavalue = "";
/*  34 */   private String avgworkhour = "";
/*  35 */   private String salaryenddate = "";
/*  36 */   private String signipscope = "";
/*  37 */   private String needsign = "";
/*  38 */   private String needsignhasinit = "";
/*  39 */   private String onlyworkday = "";
/*  40 */   private String signtimescope = "";
/*  41 */   private static ConcurrentHashMap<String, ConcurrentHashMap<String, String>> mapSubcomNeedSign = new ConcurrentHashMap<>();
/*  42 */   private ConcurrentHashMap<String, String> mapNeedSign = new ConcurrentHashMap<>();
/*     */   
/*  44 */   private static Object lock = new Object();
/*     */ 
/*     */   
/*     */   public HrmKqSystemComInfo() {
/*  48 */     this.staticobj = StaticObj.getInstance();
/*  49 */     getHrmKqSystemInfo();
/*     */   }
/*     */   
/*     */   public HrmKqSystemComInfo(User paramUser) {
/*  53 */     this.staticobj = StaticObj.getInstance();
/*  54 */     getHrmKqSystemInfo(paramUser);
/*     */   }
/*     */   
/*     */   private void getHrmKqSystemInfo() {
/*  58 */     synchronized (lock) {
/*  59 */       if (this.staticobj.getObject("HrmKqSystemInfo") == null) setHrmKqSystemInfo(); 
/*  60 */       this.tosomeone = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "tosomeone");
/*  61 */       this.timeinterval = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "timeinterval");
/*  62 */       this.getdatatype = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "getdatatype");
/*  63 */       this.getdatavalue = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "getdatavalue");
/*  64 */       this.avgworkhour = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "avgworkhour");
/*  65 */       this.salaryenddate = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "salaryenddate");
/*  66 */       this.signipscope = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "signipscope");
/*  67 */       this.needsign = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "needsign");
/*  68 */       this.needsignhasinit = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "needsignhasinit");
/*  69 */       this.onlyworkday = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "onlyworkday");
/*  70 */       this.signtimescope = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "signtimescope");
/*  71 */       if (this.tosomeone == null) setHrmKqSystemInfo(); 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void getHrmKqSystemInfo(User paramUser) {
/*  76 */     synchronized (lock) {
/*  77 */       if (this.staticobj.getObject("HrmKqSystemInfo") == null) setHrmKqSystemInfo(); 
/*  78 */       this.tosomeone = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "tosomeone");
/*  79 */       this.timeinterval = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "timeinterval");
/*  80 */       this.getdatatype = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "getdatatype");
/*  81 */       this.getdatavalue = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "getdatavalue");
/*  82 */       this.avgworkhour = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "avgworkhour");
/*  83 */       this.salaryenddate = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "salaryenddate");
/*     */ 
/*     */       
/*  86 */       this.needsignhasinit = (String)this.staticobj.getRecordFromObj("HrmKqSystemInfo", "needsignhasinit");
/*     */ 
/*     */       
/*  89 */       if (this.tosomeone == null) setHrmKqSystemInfo();
/*     */       
/*  91 */       setHrmKqSystemInfo(paramUser);
/*  92 */       this.signipscope = this.mapNeedSign.get("signipscope");
/*  93 */       this.needsign = this.mapNeedSign.get("needsign");
/*  94 */       this.onlyworkday = this.mapNeedSign.get("onlyworkday");
/*  95 */       this.signtimescope = this.mapNeedSign.get("signtimescope");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void setHrmKqSystemInfo() {
/* 102 */     RecordSet recordSet = new RecordSet();
/* 103 */     recordSet.executeProc("HrmkqSystemSet_Select", "");
/* 104 */     if (recordSet.next()) {
/* 105 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "tosomeone", Util.null2String(recordSet.getString("tosomeone")));
/* 106 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "timeinterval", Util.null2String(recordSet.getString("timeinterval")));
/* 107 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "getdatatype", Util.null2String(recordSet.getString("getdatatype")));
/* 108 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "getdatavalue", Util.null2String(recordSet.getString("getdatavalue")));
/* 109 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "avgworkhour", Util.null2String(recordSet.getString("avgworkhour")));
/* 110 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "salaryenddate", Util.null2String(recordSet.getString("salaryenddate")));
/* 111 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "signipscope", Util.null2String(recordSet.getString("signipscope")));
/* 112 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "needsign", Util.null2String(recordSet.getString("needsign")));
/* 113 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "needsignhasinit", Util.null2String(recordSet.getString("needsignhasinit")));
/* 114 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "onlyworkday", Util.null2String(recordSet.getString("onlyworkday")));
/* 115 */       this.staticobj.putRecordToObj("HrmKqSystemInfo", "signtimescope", Util.null2String(recordSet.getString("signtimescope")));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setHrmKqSystemInfo(User paramUser) {
/* 132 */     RecordSet recordSet = new RecordSet();
/* 133 */     boolean bool = false;
/* 134 */     String str = StringUtil.vString(Integer.valueOf(paramUser.getUserSubCompany1()), "0");
/* 135 */     if (mapSubcomNeedSign.get(str) != null) {
/* 136 */       this.mapNeedSign = mapSubcomNeedSign.get(str);
/*     */       
/*     */       return;
/*     */     } 
/* 140 */     recordSet.executeQuery("select needSign, onlyworkday, signTimeScope, signIpScope,relatedid from HrmSchedule where Scheduletype=4 and relatedid=" + StringUtil.vString(Integer.valueOf(paramUser.getUserSubCompany1()), "0") + " and Validedateto>='" + TimeUtil.getCurrentDateString() + "'", new Object[0]);
/*     */     
/* 142 */     if (recordSet.next()) {
/* 143 */       bool = true;
/* 144 */       ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/* 145 */       concurrentHashMap.put("signipscope", Util.null2String(recordSet.getString("signipscope")));
/* 146 */       concurrentHashMap.put("needsign", Util.null2String(recordSet.getString("needsign")));
/* 147 */       concurrentHashMap.put("onlyworkday", Util.null2String(recordSet.getString("onlyworkday")));
/* 148 */       concurrentHashMap.put("signtimescope", Util.null2String(recordSet.getString("signtimescope")));
/* 149 */       this.mapNeedSign = (ConcurrentHashMap)concurrentHashMap;
/* 150 */       mapSubcomNeedSign.put(str, concurrentHashMap);
/*     */     } 
/*     */     
/* 153 */     if (!bool) {
/* 154 */       recordSet.executeQuery("select needSign, onlyworkday, signTimeScope, signIpScope,relatedid from HrmSchedule where Scheduletype=3 and relatedid=0 and Validedateto>='" + TimeUtil.getCurrentDateString() + "'", new Object[0]);
/* 155 */       if (recordSet.next()) {
/* 156 */         this.mapNeedSign.clear();
/* 157 */         this.mapNeedSign.put("signipscope", Util.null2String(recordSet.getString("signipscope")));
/* 158 */         this.mapNeedSign.put("needsign", Util.null2String(recordSet.getString("needsign")));
/* 159 */         this.mapNeedSign.put("onlyworkday", Util.null2String(recordSet.getString("onlyworkday")));
/* 160 */         this.mapNeedSign.put("signtimescope", Util.null2String(recordSet.getString("signtimescope")));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTosomeone() {
/* 170 */     return this.tosomeone;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTimeinterval() {
/* 178 */     return this.timeinterval;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDatatype() {
/* 186 */     return this.getdatatype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAvgworkhour() {
/* 194 */     return this.avgworkhour;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDatavalue() {
/* 202 */     return this.getdatavalue;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSalaryenddate() {
/* 210 */     return this.salaryenddate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBlurshift() {
/* 218 */     return "";
/*     */   }
/*     */   
/*     */   public int getBlurminitus() {
/* 222 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSignipscope() {
/* 230 */     return this.signipscope;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNeedsign() {
/* 238 */     return this.needsign;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNeedsignhasinit() {
/* 246 */     return this.needsignhasinit;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOnlyworkday() {
/* 254 */     return this.onlyworkday;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSigntimescope() {
/* 262 */     return this.signtimescope;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeSystemCache() {
/* 269 */     this.staticobj.removeObject("HrmKqSystemInfo");
/* 270 */     mapSubcomNeedSign.clear();
/* 271 */     this.mapNeedSign.clear();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setKqNeedSign(int paramInt) throws Exception {
/* 281 */     RecordSet recordSet = new RecordSet();
/* 282 */     recordSet.executeSql("update HrmKqSystemSet set needsign=" + paramInt + ", needsignhasinit =1");
/*     */     
/* 284 */     removeSystemCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmKqSystemComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */