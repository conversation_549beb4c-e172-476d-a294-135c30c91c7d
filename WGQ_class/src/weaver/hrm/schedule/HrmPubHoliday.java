/*    */ package weaver.hrm.schedule;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class HrmPubHoliday
/*    */ {
/*    */   public String getChangeTypeName(String paramString1, String paramString2) throws Exception {
/*  9 */     String str = "";
/* 10 */     int i = Integer.parseInt(paramString2);
/* 11 */     if (Integer.parseInt(paramString1) == 1) {
/* 12 */       str = SystemEnv.getHtmlLabelName(16478, i);
/* 13 */     } else if (Integer.parseInt(paramString1) == 2) {
/* 14 */       str = SystemEnv.getHtmlLabelName(16751, i);
/* 15 */     } else if (Integer.parseInt(paramString1) == 3) {
/* 16 */       str = SystemEnv.getHtmlLabelName(16752, i);
/*    */     } 
/* 18 */     return str;
/*    */   }
/*    */   
/*    */   public String getRelateWeekdayDescE9(String paramString1, String paramString2) throws Exception {
/* 22 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 23 */     String str1 = "";
/* 24 */     String str2 = arrayOfString[0];
/* 25 */     int i = Util.getIntValue(arrayOfString[1]);
/* 26 */     if (Integer.parseInt(str2) == 2) {
/* 27 */       switch (Integer.parseInt(paramString1)) {
/*    */         case 1:
/* 29 */           str1 = SystemEnv.getHtmlLabelName(398, i);
/*    */           break;
/*    */         case 2:
/* 32 */           str1 = SystemEnv.getHtmlLabelName(392, i);
/*    */           break;
/*    */         case 3:
/* 35 */           str1 = SystemEnv.getHtmlLabelName(393, i);
/*    */           break;
/*    */         case 4:
/* 38 */           str1 = SystemEnv.getHtmlLabelName(394, i);
/*    */           break;
/*    */         case 5:
/* 41 */           str1 = SystemEnv.getHtmlLabelName(395, i);
/*    */           break;
/*    */         case 6:
/* 44 */           str1 = SystemEnv.getHtmlLabelName(396, i);
/*    */           break;
/*    */         case 7:
/* 47 */           str1 = SystemEnv.getHtmlLabelName(397, i);
/*    */           break;
/*    */       } 
/* 50 */       str1 = SystemEnv.getHtmlLabelName(16753, i) + str1;
/*    */     } 
/* 52 */     return str1;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getRelateWeekdayDesc(String paramString1, String paramString2) throws Exception {
/* 57 */     String str = "";
/* 58 */     byte b = 7;
/* 59 */     if (Integer.parseInt(paramString2) == 2) {
/* 60 */       switch (Integer.parseInt(paramString1)) {
/*    */         case 1:
/* 62 */           str = SystemEnv.getHtmlLabelName(398, b);
/*    */           break;
/*    */         case 2:
/* 65 */           str = SystemEnv.getHtmlLabelName(392, b);
/*    */           break;
/*    */         case 3:
/* 68 */           str = SystemEnv.getHtmlLabelName(393, b);
/*    */           break;
/*    */         case 4:
/* 71 */           str = SystemEnv.getHtmlLabelName(394, b);
/*    */           break;
/*    */         case 5:
/* 74 */           str = SystemEnv.getHtmlLabelName(395, b);
/*    */           break;
/*    */         case 6:
/* 77 */           str = SystemEnv.getHtmlLabelName(396, b);
/*    */           break;
/*    */         case 7:
/* 80 */           str = SystemEnv.getHtmlLabelName(397, b);
/*    */           break;
/*    */       } 
/* 83 */       str = SystemEnv.getHtmlLabelName(16753, b) + str;
/*    */     } 
/* 85 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmPubHoliday.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */