/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleKqUtil
/*     */   extends BaseBean
/*     */ {
/*  26 */   private RecordSet rt = null;
/*  27 */   private StaticObj staticobj = null;
/*     */   
/*  29 */   private String signipscope = "";
/*  30 */   private String needsign = "";
/*  31 */   private String needsignhasinit = "";
/*  32 */   private String onlyworkday = "";
/*  33 */   private String signtimescope = "";
/*     */   
/*  35 */   private static Object lock = new Object();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean hasHrmSchedule(User paramUser) {
/*  43 */     RecordSet recordSet = new RecordSet();
/*  44 */     boolean bool = false;
/*  45 */     recordSet.executeSql("select needSign, onlyworkday, signTimeScope, signIpScope,relatedid from HrmSchedule where Scheduletype=4 and relatedid=" + StringUtil.vString(Integer.valueOf(paramUser.getUserSubCompany1()), "0") + " and Validedateto>='" + TimeUtil.getCurrentDateString() + "'");
/*     */     
/*  47 */     while (recordSet.next()) {
/*  48 */       if ("1".equals(StringUtil.vString(recordSet.getString("needSign")))) {
/*  49 */         bool = true;
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*  54 */     if (!bool) {
/*  55 */       recordSet.executeSql("select needSign, onlyworkday, signTimeScope, signIpScope,relatedid from HrmSchedule where Scheduletype=3 and relatedid=0 and Validedateto>='" + TimeUtil.getCurrentDateString() + "'");
/*  56 */       while (recordSet.next()) {
/*  57 */         if ("1".equals(StringUtil.vString(recordSet.getString("needSign")))) {
/*  58 */           bool = true;
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/*  63 */     return bool;
/*     */   }
/*     */   
/*     */   public static Map getHrmScheduleInfo(User paramUser) {
/*  67 */     RecordSet recordSet = new RecordSet();
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*  69 */     boolean bool = false;
/*  70 */     recordSet.executeSql("select needSign, onlyworkday, signTimeScope, signIpScope,relatedid from HrmSchedule where Scheduletype=4 and relatedid=" + StringUtil.vString(Integer.valueOf(paramUser.getUserSubCompany1()), "0") + " and Validedateto>='" + TimeUtil.getCurrentDateString() + "'");
/*     */     
/*  72 */     while (recordSet.next()) {
/*  73 */       if ("1".equals(StringUtil.vString(recordSet.getString("needSign")))) {
/*  74 */         bool = true;
/*  75 */         hashMap.put("needSign", StringUtil.vString(recordSet.getString("needSign")));
/*  76 */         hashMap.put("onlyworkday", StringUtil.vString(recordSet.getString("onlyworkday")));
/*  77 */         hashMap.put("signTimeScope", StringUtil.vString(recordSet.getString("signTimeScope")));
/*  78 */         hashMap.put("signIpScope", StringUtil.vString(recordSet.getString("signIpScope")));
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*  83 */     if (!bool) {
/*  84 */       recordSet.executeSql("select needSign, onlyworkday, signTimeScope, signIpScope,relatedid from HrmSchedule where Scheduletype=3 and relatedid=0 and Validedateto>='" + TimeUtil.getCurrentDateString() + "'");
/*  85 */       while (recordSet.next()) {
/*  86 */         if ("1".equals(StringUtil.vString(recordSet.getString("needSign")))) {
/*  87 */           bool = true;
/*  88 */           hashMap.put("needSign", StringUtil.vString(recordSet.getString("needSign")));
/*  89 */           hashMap.put("onlyworkday", StringUtil.vString(recordSet.getString("onlyworkday")));
/*  90 */           hashMap.put("signTimeScope", StringUtil.vString(recordSet.getString("signTimeScope")));
/*  91 */           hashMap.put("signIpScope", StringUtil.vString(recordSet.getString("signIpScope")));
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/*  96 */     return hashMap;
/*     */   }
/*     */   
/*     */   public HrmScheduleKqUtil(User paramUser) {
/* 100 */     this.staticobj = StaticObj.getInstance();
/* 101 */     getHrmKqSystemInfo(paramUser);
/*     */   }
/*     */   
/*     */   private void getHrmKqSystemInfo(User paramUser) {
/* 105 */     synchronized (lock) {
/* 106 */       if (this.staticobj.getObject("HrmScheduleKqComInfo") == null) setHrmKqSystemInfo(paramUser); 
/* 107 */       this.signipscope = (String)this.staticobj.getRecordFromObj("HrmScheduleKqComInfo", "signipscope");
/* 108 */       this.needsign = (String)this.staticobj.getRecordFromObj("HrmScheduleKqComInfo", "needsign");
/* 109 */       this.needsignhasinit = (String)this.staticobj.getRecordFromObj("HrmScheduleKqComInfo", "needsignhasinit");
/* 110 */       this.onlyworkday = (String)this.staticobj.getRecordFromObj("HrmScheduleKqComInfo", "onlyworkday");
/* 111 */       this.signtimescope = (String)this.staticobj.getRecordFromObj("HrmScheduleKqComInfo", "signtimescope");
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setHrmKqSystemInfo(User paramUser) {
/* 116 */     int i = 0;
/* 117 */     if (paramUser != null) i = paramUser.getUID(); 
/* 118 */     RecordSet recordSet = new RecordSet();
/* 119 */     recordSet.executeSql("select needSign, onlyworkday, signTimeScope, signIpScope,relatedid from HrmSchedule where relatedid=" + i);
/* 120 */     if (recordSet.next()) {
/* 121 */       this.staticobj.putRecordToObj("HrmScheduleKqComInfo", "signipscope", Util.null2String(recordSet.getString("signipscope")));
/* 122 */       this.staticobj.putRecordToObj("HrmScheduleKqComInfo", "needsign", Util.null2String(recordSet.getString("needsign")));
/* 123 */       this.staticobj.putRecordToObj("HrmScheduleKqComInfo", "needsignhasinit", Util.null2String(recordSet.getString("needsignhasinit")));
/* 124 */       this.staticobj.putRecordToObj("HrmScheduleKqComInfo", "onlyworkday", Util.null2String(recordSet.getString("onlyworkday")));
/* 125 */       this.staticobj.putRecordToObj("HrmScheduleKqComInfo", "signtimescope", Util.null2String(recordSet.getString("signtimescope")));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSignipscope() {
/* 134 */     return this.signipscope;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNeedsign() {
/* 142 */     return this.needsign;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNeedsignhasinit() {
/* 150 */     return this.needsignhasinit;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOnlyworkday() {
/* 158 */     return this.onlyworkday;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSigntimescope() {
/* 166 */     return this.signtimescope;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeSystemCache() {
/* 173 */     this.staticobj.removeObject("HrmScheduleKqComInfo");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmScheduleKqUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */