/*    */ package weaver.hrm.schedule;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Calendar;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.system.ThreadWork;
/*    */ 
/*    */ public class ClearPaidSickLeave extends BaseBean implements ThreadWork {
/* 12 */   private String sql = "";
/*    */ 
/*    */   
/*    */   public void doThreadWork() {
/* 16 */     checkPaidSickMaturity();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void checkPaidSickMaturity() {
/* 24 */     Calendar calendar = Calendar.getInstance();
/*    */ 
/*    */     
/* 27 */     String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/* 28 */     ArrayList<String> arrayList = new ArrayList();
/* 29 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 31 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 33 */     this.sql = "select id from hrmsubcompany order by id asc";
/* 34 */     recordSet.executeSql(this.sql);
/* 35 */     while (recordSet.next()) {
/* 36 */       arrayList.add(recordSet.getString("id"));
/*    */     }
/*    */     
/* 39 */     this.sql = "select distinct pslyear,leavetype from HrmPSLManagement where psldays > 0";
/* 40 */     recordSet.executeSql(this.sql);
/* 41 */     while (recordSet.next()) {
/* 42 */       hashMap = new HashMap<>();
/* 43 */       hashMap.put(recordSet.getString("pslyear"), recordSet.getString("leavetype"));
/* 44 */       arrayList1.add(hashMap);
/*    */     } 
/*    */     
/* 47 */     String str2 = "";
/* 48 */     HrmPaidSickManagement hrmPaidSickManagement = new HrmPaidSickManagement();
/*    */     try {
/* 50 */       if (arrayList != null && arrayList1 != null) {
/* 51 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 52 */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 53 */             Map map = arrayList1.get(b1);
/* 54 */             String str = Util.null2String(arrayList.get(b));
/* 55 */             for (Map.Entry entry : map.entrySet()) {
/* 56 */               str2 = HrmPaidSickManagement.getPaidSickPeriod(str, (String)entry.getKey(), (String)entry.getValue());
/* 57 */               String str3 = Util.TokenizerString2(str2, "#")[1];
/*    */               
/* 59 */               if (str3.compareTo(str1) < 0 && 
/* 60 */                 !"".equals(entry.getValue()) && !"".equals(entry.getKey()) && !"".equals(str)) {
/* 61 */                 clearPaidSickDays(str, (String)entry.getKey(), (String)entry.getValue());
/*    */               }
/*    */             }
/*    */           
/*    */           } 
/*    */         } 
/*    */       }
/* 68 */     } catch (Exception exception) {
/* 69 */       writeLog(exception);
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void clearPaidSickDays(String paramString1, String paramString2, String paramString3) {
/* 78 */     RecordSet recordSet = new RecordSet();
/* 79 */     this
/* 80 */       .sql = "update HrmPSLManagement set status = 0 where resourceid in (select id from hrmresource where subcompanyid1 = " + paramString1 + ") and pslyear = " + paramString2 + ("".equals(paramString3) ? "" : (" and leavetype=" + paramString3));
/* 81 */     recordSet.executeSql(this.sql);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/ClearPaidSickLeave.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */