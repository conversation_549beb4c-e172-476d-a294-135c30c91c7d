/*     */ package weaver.hrm.schedule.imp;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.location.LocationComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.schedule.HrmScheduleSign;
/*     */ import weaver.hrm.schedule.dao.HrmScheduleDao;
/*     */ import weaver.hrm.schedule.domain.HrmSchedule;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleSetManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleImpManager
/*     */ {
/*     */   private String fromDate;
/*     */   private String toDate;
/*     */   private String currentDate;
/*  30 */   private HrmScheduleManager schedulemanager = null;
/*     */   
/*  32 */   private HrmScheduleSetManager manager = null;
/*     */   
/*  34 */   private HrmScheduleDao dao = null;
/*     */   
/*  36 */   private Map<String, Map<String, String>> userDetailTimeMap = null;
/*     */   
/*     */   public HrmScheduleImpManager() {}
/*     */   
/*     */   public HrmScheduleImpManager(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, String paramString1, String paramString2) {
/*  41 */     this(paramHttpServletRequest, paramHttpServletResponse, null, paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public HrmScheduleImpManager(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, User paramUser, String paramString1, String paramString2) {
/*  45 */     this.schedulemanager = new HrmScheduleManager();
/*  46 */     this.manager = new HrmScheduleSetManager();
/*  47 */     this.dao = new HrmScheduleDao();
/*  48 */     this.userDetailTimeMap = new HashMap<>();
/*  49 */     init(paramString1, paramString2);
/*  50 */     this.fromDate = StringUtil.vString(paramString1, this.currentDate);
/*  51 */     this.toDate = StringUtil.vString(paramString2, this.currentDate);
/*     */   }
/*     */   
/*     */   private void init(String paramString1, String paramString2) {
/*  55 */     this.currentDate = DateUtil.getCurrentDate();
/*     */     
/*  57 */     if (StringUtil.isNull(new String[] { paramString1, paramString2 }) || paramString1.compareTo(paramString2) > 0)
/*     */       return; 
/*  59 */     this.userDetailTimeMap = this.manager.getScheduleSignDetail(DateUtil.addDate(paramString1, -1), DateUtil.addDate(paramString2, 1));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void importSchedule(HrmScheduleSign paramHrmScheduleSign, Map<String, String> paramMap) {
/*  68 */     String str1 = paramHrmScheduleSign.getSigndate() + " " + paramHrmScheduleSign.getSigntime();
/*  69 */     String str2 = "1";
/*  70 */     for (Map.Entry<String, String> entry : paramMap.entrySet()) {
/*  71 */       String str = paramMap.get(entry.getKey());
/*  72 */       if (str != null && !"".equals(str)) {
/*  73 */         String[] arrayOfString = str.split(",");
/*  74 */         for (byte b = 0; arrayOfString != null && b < arrayOfString.length; b++) {
/*  75 */           String[] arrayOfString1 = arrayOfString[b].split("#");
/*  76 */           if (arrayOfString1 != null && arrayOfString1.length == 2) {
/*  77 */             String str3 = arrayOfString1[0] + ":00";
/*  78 */             String str4 = arrayOfString1[1] + ":00";
/*  79 */             if (DateUtil.isInDateTimeRange(str1, str3, str4)) {
/*  80 */               User user = getUser(paramHrmScheduleSign.getUserid());
/*  81 */               if (user != null) str2 = getSignType(user, str3, str4, paramHrmScheduleSign); 
/*  82 */               HrmScheduleSign.updateSignType(paramHrmScheduleSign.getUuid(), StringUtil.parseToInt(str2, 1), "" + SystemEnv.getHtmlLabelName(125839, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(10003678, ThreadVarLanguage.getLang()) + ":" + str3 + ";" + SystemEnv.getHtmlLabelName(10003679, ThreadVarLanguage.getLang()) + ":" + str4);
/*  83 */               paramHrmScheduleSign.setSigntype(StringUtil.parseToInt(str2, 1));
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public User getUser(int paramInt) {
/*  92 */     User user = null;
/*  93 */     if (paramInt != -1) {
/*  94 */       int i = 0;
/*  95 */       String str = "";
/*     */       try {
/*  97 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  98 */         LocationComInfo locationComInfo = new LocationComInfo();
/*  99 */         str = locationComInfo.getLocationcountry(resourceComInfo.getLocationid(String.valueOf(paramInt)));
/* 100 */         i = StringUtil.parseToInt(resourceComInfo.getSubCompanyID(String.valueOf(paramInt)), 0);
/* 101 */       } catch (Exception exception) {}
/* 102 */       if (i != 0) {
/* 103 */         user = new User();
/* 104 */         user.setUid(paramInt);
/* 105 */         user.setCountryid(str);
/* 106 */         user.setUserSubCompany1(i);
/* 107 */         if (user.getLogintype() == null) {
/* 108 */           user.setLogintype("1");
/*     */         }
/*     */       } 
/*     */     } 
/* 112 */     return user;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getSignType(User paramUser, String paramString1, String paramString2, HrmScheduleSign paramHrmScheduleSign) {
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     hashMap.put("signStartTime", paramString1);
/* 126 */     hashMap.put("signEndTime", paramString2);
/* 127 */     return this.dao.getImportSignType(paramUser, hashMap, paramHrmScheduleSign);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void importCommon(HrmScheduleSign paramHrmScheduleSign, Map<String, String> paramMap) {
/* 136 */     String str1 = paramHrmScheduleSign.getSigndate();
/* 137 */     User user = getUser(paramHrmScheduleSign.getUserid());
/* 138 */     if (user == null) {
/*     */       return;
/*     */     }
/* 141 */     int i = user.getUserSubCompany1();
/* 142 */     String str2 = "1";
/* 143 */     this.schedulemanager.setUser(user);
/* 144 */     HrmSchedule hrmSchedule = this.schedulemanager.getSubcompanyWorkTime(str1, i);
/* 145 */     if (hrmSchedule == null)
/* 146 */       return;  String str3 = paramHrmScheduleSign.getSigndate() + " " + hrmSchedule.getOnDutyTimeAM() + ":00";
/* 147 */     String str4 = paramHrmScheduleSign.getSigndate() + " " + hrmSchedule.getOffDutyTimePM() + ":00";
/* 148 */     String str5 = "";
/* 149 */     if ("2".equals(hrmSchedule.getSignType())) {
/* 150 */       str5 = paramHrmScheduleSign.getSigndate() + " " + hrmSchedule.getSignStartTime() + ":00";
/* 151 */       if (paramHrmScheduleSign.getSigntime().compareTo(hrmSchedule.getSignStartTime()) <= 0) {
/* 152 */         str3 = paramHrmScheduleSign.getSigndate() + " 00:00:00";
/* 153 */         str4 = str5;
/* 154 */         str2 = getSignType(user, str3, str5, paramHrmScheduleSign);
/*     */       } else {
/* 156 */         str3 = str5;
/* 157 */         str4 = paramHrmScheduleSign.getSigndate() + " 23:59:59";
/* 158 */         str2 = getSignType(user, str5, str4, paramHrmScheduleSign);
/*     */       } 
/*     */     } else {
/* 161 */       str3 = paramHrmScheduleSign.getSigndate() + " 00:00:00";
/* 162 */       str4 = paramHrmScheduleSign.getSigndate() + " 23:59:59";
/* 163 */       str2 = getSignType(user, str3, str4, paramHrmScheduleSign);
/*     */     } 
/* 165 */     HrmScheduleSign.updateSignType(paramHrmScheduleSign.getUuid(), StringUtil.parseToInt(str2, 1), "" + SystemEnv.getHtmlLabelName(10003680, ThreadVarLanguage.getLang()) + ";" + hrmSchedule.getSignType() + "" + SystemEnv.getHtmlLabelName(10003681, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(505767, ThreadVarLanguage.getLang()) + "" + str3 + ";" + SystemEnv.getHtmlLabelName(83826, ThreadVarLanguage.getLang()) + ":" + str4 + ";" + SystemEnv.getHtmlLabelName(10003682, ThreadVarLanguage.getLang()) + ":" + str5);
/* 166 */     paramHrmScheduleSign.setSigntype(StringUtil.parseToInt(str2, 1));
/*     */   }
/*     */   
/*     */   public Map<String, Map<String, String>> getUserDetailTimeMap() {
/* 170 */     return this.userDetailTimeMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/imp/HrmScheduleImpManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */