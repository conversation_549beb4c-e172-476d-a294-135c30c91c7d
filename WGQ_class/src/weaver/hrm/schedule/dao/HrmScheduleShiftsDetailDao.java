/*      */ package weaver.hrm.schedule.dao;
/*      */ 
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.framework.BaseConnection;
/*      */ import weaver.framework.BaseDao;
/*      */ import weaver.hrm.schedule.domain.HrmScheduleShiftsDetail;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class HrmScheduleShiftsDetailDao
/*      */   extends BaseConnection
/*      */   implements BaseDao<HrmScheduleShiftsDetail>
/*      */ {
/*      */   public Comparable insert(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail) {
/*   23 */     if (paramHrmScheduleShiftsDetail == null) return Integer.valueOf(-1); 
/*   24 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_schedule_shifts_detail (delflag,mfid,d001,d002,d003,d004,").append(" d005,d006,d007,d008,d009,d010,").append(" d011,d012,d013,d014,d015,d016,").append(" d017,d018,d019,d020,d021,d022,").append(" d023,d024,d025,d026,d027,d028,").append(" d029,d030,d031,w001,w002,w003,").append(" w004,w005,w006,w007,field001,field002,").append(" field003,field004,field005 )").append(" values(" + paramHrmScheduleShiftsDetail.getDelflag() + "," + paramHrmScheduleShiftsDetail.getMfid() + "," + paramHrmScheduleShiftsDetail.getD001() + "," + paramHrmScheduleShiftsDetail.getD002() + ",").append(" " + paramHrmScheduleShiftsDetail.getD003() + "," + paramHrmScheduleShiftsDetail.getD004() + "," + paramHrmScheduleShiftsDetail.getD005() + "," + paramHrmScheduleShiftsDetail.getD006() + ",").append(" " + paramHrmScheduleShiftsDetail.getD007() + "," + paramHrmScheduleShiftsDetail.getD008() + "," + paramHrmScheduleShiftsDetail.getD009() + "," + paramHrmScheduleShiftsDetail.getD010() + ",").append(" " + paramHrmScheduleShiftsDetail.getD011() + "," + paramHrmScheduleShiftsDetail.getD012() + "," + paramHrmScheduleShiftsDetail.getD013() + "," + paramHrmScheduleShiftsDetail.getD014() + ",").append(" " + paramHrmScheduleShiftsDetail.getD015() + "," + paramHrmScheduleShiftsDetail.getD016() + "," + paramHrmScheduleShiftsDetail.getD017() + "," + paramHrmScheduleShiftsDetail.getD018() + ",").append(" " + paramHrmScheduleShiftsDetail.getD019() + "," + paramHrmScheduleShiftsDetail.getD020() + "," + paramHrmScheduleShiftsDetail.getD021() + "," + paramHrmScheduleShiftsDetail.getD022() + ",").append(" " + paramHrmScheduleShiftsDetail.getD023() + "," + paramHrmScheduleShiftsDetail.getD024() + "," + paramHrmScheduleShiftsDetail.getD025() + "," + paramHrmScheduleShiftsDetail.getD026() + ",").append(" " + paramHrmScheduleShiftsDetail.getD027() + "," + paramHrmScheduleShiftsDetail.getD028() + "," + paramHrmScheduleShiftsDetail.getD029() + "," + paramHrmScheduleShiftsDetail.getD030() + ",").append(" " + paramHrmScheduleShiftsDetail.getD031() + "," + paramHrmScheduleShiftsDetail.getW001() + "," + paramHrmScheduleShiftsDetail.getW002() + "," + paramHrmScheduleShiftsDetail.getW003() + ",").append(" " + paramHrmScheduleShiftsDetail.getW004() + "," + paramHrmScheduleShiftsDetail.getW005() + "," + paramHrmScheduleShiftsDetail.getW006() + "," + paramHrmScheduleShiftsDetail.getW007() + ",").append(" " + paramHrmScheduleShiftsDetail.getField001() + ",'" + paramHrmScheduleShiftsDetail.getField002() + "'," + paramHrmScheduleShiftsDetail.getField003() + "," + paramHrmScheduleShiftsDetail.getField004() + ",").append(" " + paramHrmScheduleShiftsDetail.getField005() + " )");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   45 */     this.rs.executeSql(stringBuffer.toString());
/*   46 */     this.rs.executeSql("select id from hrm_schedule_shifts_detail where mfid = " + paramHrmScheduleShiftsDetail.getMfid());
/*   47 */     return Integer.valueOf(this.rs.next() ? this.rs.getInt(1) : 0);
/*      */   }
/*      */   
/*      */   public void update(HrmScheduleShiftsDetail paramHrmScheduleShiftsDetail) {
/*   51 */     if (paramHrmScheduleShiftsDetail == null)
/*   52 */       return;  StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_schedule_shifts_detail set").append(" delflag = " + paramHrmScheduleShiftsDetail.getDelflag() + ",mfid = " + paramHrmScheduleShiftsDetail.getMfid() + ",d001 = " + paramHrmScheduleShiftsDetail.getD001() + ",").append(" d002 = " + paramHrmScheduleShiftsDetail.getD002() + ",d003 = " + paramHrmScheduleShiftsDetail.getD003() + ",d004 = " + paramHrmScheduleShiftsDetail.getD004() + ",").append(" d005 = " + paramHrmScheduleShiftsDetail.getD005() + ",d006 = " + paramHrmScheduleShiftsDetail.getD006() + ",d007 = " + paramHrmScheduleShiftsDetail.getD007() + ",").append(" d008 = " + paramHrmScheduleShiftsDetail.getD008() + ",d009 = " + paramHrmScheduleShiftsDetail.getD009() + ",d010 = " + paramHrmScheduleShiftsDetail.getD010() + ",").append(" d011 = " + paramHrmScheduleShiftsDetail.getD011() + ",d012 = " + paramHrmScheduleShiftsDetail.getD012() + ",d013 = " + paramHrmScheduleShiftsDetail.getD013() + ",").append(" d014 = " + paramHrmScheduleShiftsDetail.getD014() + ",d015 = " + paramHrmScheduleShiftsDetail.getD015() + ",d016 = " + paramHrmScheduleShiftsDetail.getD016() + ",").append(" d017 = " + paramHrmScheduleShiftsDetail.getD017() + ",d018 = " + paramHrmScheduleShiftsDetail.getD018() + ",d019 = " + paramHrmScheduleShiftsDetail.getD019() + ",").append(" d020 = " + paramHrmScheduleShiftsDetail.getD020() + ",d021 = " + paramHrmScheduleShiftsDetail.getD021() + ",d022 = " + paramHrmScheduleShiftsDetail.getD022() + ",").append(" d023 = " + paramHrmScheduleShiftsDetail.getD023() + ",d024 = " + paramHrmScheduleShiftsDetail.getD024() + ",d025 = " + paramHrmScheduleShiftsDetail.getD025() + ",").append(" d026 = " + paramHrmScheduleShiftsDetail.getD026() + ",d027 = " + paramHrmScheduleShiftsDetail.getD027() + ",d028 = " + paramHrmScheduleShiftsDetail.getD028() + ",").append(" d029 = " + paramHrmScheduleShiftsDetail.getD029() + ",d030 = " + paramHrmScheduleShiftsDetail.getD030() + ",d031 = " + paramHrmScheduleShiftsDetail.getD031() + ",").append(" w001 = " + paramHrmScheduleShiftsDetail.getW001() + ",w002 = " + paramHrmScheduleShiftsDetail.getW002() + ",w003 = " + paramHrmScheduleShiftsDetail.getW003() + ",").append(" w004 = " + paramHrmScheduleShiftsDetail.getW004() + ",w005 = " + paramHrmScheduleShiftsDetail.getW005() + ",w006 = " + paramHrmScheduleShiftsDetail.getW006() + ",").append(" w007 = " + paramHrmScheduleShiftsDetail.getW007() + ",field001 = " + paramHrmScheduleShiftsDetail.getField001() + ",field002 = '" + paramHrmScheduleShiftsDetail.getField002() + "',").append(" field003 = " + paramHrmScheduleShiftsDetail.getField003() + ",field004 = " + paramHrmScheduleShiftsDetail.getField004() + ",field005 = " + paramHrmScheduleShiftsDetail.getField005() + "").append(" where id = " + paramHrmScheduleShiftsDetail.getId() + "");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   70 */     this.rs.executeSql(stringBuffer.toString());
/*      */   }
/*      */   
/*      */   public List<HrmScheduleShiftsDetail> find(Map<String, Comparable> paramMap) {
/*   74 */     ArrayList<HrmScheduleShiftsDetail> arrayList = new ArrayList();
/*   75 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.delflag,t.mfid,t.d001,t.d002,t.d003,t.d004,").append(" t.d005,t.d006,t.d007,t.d008,t.d009,t.d010,").append(" t.d011,t.d012,t.d013,t.d014,t.d015,t.d016,").append(" t.d017,t.d018,t.d019,t.d020,t.d021,t.d022,").append(" t.d023,t.d024,t.d025,t.d026,t.d027,t.d028,").append(" t.d029,t.d030,t.d031,t.w001,t.w002,t.w003,").append(" t.w004,t.w005,t.w006,t.w007,t.field001,t.field002,").append(" t.field003,t.field004,t.field005,t2.field003 as t2Field003").append(" from hrm_schedule_shifts_detail t left join hrm_schedule_shifts_set t2 on t.field001 = t2.id").append(" where t.delflag = 0");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   86 */     if (paramMap != null) {
/*   87 */       if (paramMap.containsKey("id")) {
/*   88 */         stringBuffer.append(" and t.id = ").append(StringUtil.vString(paramMap.get("id")));
/*      */       }
/*   90 */       if (paramMap.containsKey("begin_id")) {
/*   91 */         stringBuffer.append(" and t.id >= ").append(StringUtil.vString(paramMap.get("begin_id")));
/*      */       }
/*   93 */       if (paramMap.containsKey("end_id")) {
/*   94 */         stringBuffer.append(" and t.id < ").append(StringUtil.vString(paramMap.get("end_id")));
/*      */       }
/*   96 */       if (paramMap.containsKey("sql_id")) {
/*   97 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*      */       }
/*   99 */       if (paramMap.containsKey("delflag")) {
/*  100 */         stringBuffer.append(" and t.delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*      */       }
/*  102 */       if (paramMap.containsKey("begin_delflag")) {
/*  103 */         stringBuffer.append(" and t.delflag >= ").append(StringUtil.vString(paramMap.get("begin_delflag")));
/*      */       }
/*  105 */       if (paramMap.containsKey("end_delflag")) {
/*  106 */         stringBuffer.append(" and t.delflag < ").append(StringUtil.vString(paramMap.get("end_delflag")));
/*      */       }
/*  108 */       if (paramMap.containsKey("sql_delflag")) {
/*  109 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*      */       }
/*  111 */       if (paramMap.containsKey("mfid")) {
/*  112 */         stringBuffer.append(" and t.mfid = ").append(StringUtil.vString(paramMap.get("mfid")));
/*      */       }
/*  114 */       if (paramMap.containsKey("begin_mfid")) {
/*  115 */         stringBuffer.append(" and t.mfid >= ").append(StringUtil.vString(paramMap.get("begin_mfid")));
/*      */       }
/*  117 */       if (paramMap.containsKey("end_mfid")) {
/*  118 */         stringBuffer.append(" and t.mfid < ").append(StringUtil.vString(paramMap.get("end_mfid")));
/*      */       }
/*  120 */       if (paramMap.containsKey("sql_mfid")) {
/*  121 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_mfid")));
/*      */       }
/*  123 */       if (paramMap.containsKey("d001")) {
/*  124 */         stringBuffer.append(" and t.d001 = ").append(StringUtil.vString(paramMap.get("d001")));
/*      */       }
/*  126 */       if (paramMap.containsKey("begin_d001")) {
/*  127 */         stringBuffer.append(" and t.d001 >= ").append(StringUtil.vString(paramMap.get("begin_d001")));
/*      */       }
/*  129 */       if (paramMap.containsKey("end_d001")) {
/*  130 */         stringBuffer.append(" and t.d001 < ").append(StringUtil.vString(paramMap.get("end_d001")));
/*      */       }
/*  132 */       if (paramMap.containsKey("sql_d001")) {
/*  133 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d001")));
/*      */       }
/*  135 */       if (paramMap.containsKey("d002")) {
/*  136 */         stringBuffer.append(" and t.d002 = ").append(StringUtil.vString(paramMap.get("d002")));
/*      */       }
/*  138 */       if (paramMap.containsKey("begin_d002")) {
/*  139 */         stringBuffer.append(" and t.d002 >= ").append(StringUtil.vString(paramMap.get("begin_d002")));
/*      */       }
/*  141 */       if (paramMap.containsKey("end_d002")) {
/*  142 */         stringBuffer.append(" and t.d002 < ").append(StringUtil.vString(paramMap.get("end_d002")));
/*      */       }
/*  144 */       if (paramMap.containsKey("sql_d002")) {
/*  145 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d002")));
/*      */       }
/*  147 */       if (paramMap.containsKey("d003")) {
/*  148 */         stringBuffer.append(" and t.d003 = ").append(StringUtil.vString(paramMap.get("d003")));
/*      */       }
/*  150 */       if (paramMap.containsKey("begin_d003")) {
/*  151 */         stringBuffer.append(" and t.d003 >= ").append(StringUtil.vString(paramMap.get("begin_d003")));
/*      */       }
/*  153 */       if (paramMap.containsKey("end_d003")) {
/*  154 */         stringBuffer.append(" and t.d003 < ").append(StringUtil.vString(paramMap.get("end_d003")));
/*      */       }
/*  156 */       if (paramMap.containsKey("sql_d003")) {
/*  157 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d003")));
/*      */       }
/*  159 */       if (paramMap.containsKey("d004")) {
/*  160 */         stringBuffer.append(" and t.d004 = ").append(StringUtil.vString(paramMap.get("d004")));
/*      */       }
/*  162 */       if (paramMap.containsKey("begin_d004")) {
/*  163 */         stringBuffer.append(" and t.d004 >= ").append(StringUtil.vString(paramMap.get("begin_d004")));
/*      */       }
/*  165 */       if (paramMap.containsKey("end_d004")) {
/*  166 */         stringBuffer.append(" and t.d004 < ").append(StringUtil.vString(paramMap.get("end_d004")));
/*      */       }
/*  168 */       if (paramMap.containsKey("sql_d004")) {
/*  169 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d004")));
/*      */       }
/*  171 */       if (paramMap.containsKey("d005")) {
/*  172 */         stringBuffer.append(" and t.d005 = ").append(StringUtil.vString(paramMap.get("d005")));
/*      */       }
/*  174 */       if (paramMap.containsKey("begin_d005")) {
/*  175 */         stringBuffer.append(" and t.d005 >= ").append(StringUtil.vString(paramMap.get("begin_d005")));
/*      */       }
/*  177 */       if (paramMap.containsKey("end_d005")) {
/*  178 */         stringBuffer.append(" and t.d005 < ").append(StringUtil.vString(paramMap.get("end_d005")));
/*      */       }
/*  180 */       if (paramMap.containsKey("sql_d005")) {
/*  181 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d005")));
/*      */       }
/*  183 */       if (paramMap.containsKey("d006")) {
/*  184 */         stringBuffer.append(" and t.d006 = ").append(StringUtil.vString(paramMap.get("d006")));
/*      */       }
/*  186 */       if (paramMap.containsKey("begin_d006")) {
/*  187 */         stringBuffer.append(" and t.d006 >= ").append(StringUtil.vString(paramMap.get("begin_d006")));
/*      */       }
/*  189 */       if (paramMap.containsKey("end_d006")) {
/*  190 */         stringBuffer.append(" and t.d006 < ").append(StringUtil.vString(paramMap.get("end_d006")));
/*      */       }
/*  192 */       if (paramMap.containsKey("sql_d006")) {
/*  193 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d006")));
/*      */       }
/*  195 */       if (paramMap.containsKey("d007")) {
/*  196 */         stringBuffer.append(" and t.d007 = ").append(StringUtil.vString(paramMap.get("d007")));
/*      */       }
/*  198 */       if (paramMap.containsKey("begin_d007")) {
/*  199 */         stringBuffer.append(" and t.d007 >= ").append(StringUtil.vString(paramMap.get("begin_d007")));
/*      */       }
/*  201 */       if (paramMap.containsKey("end_d007")) {
/*  202 */         stringBuffer.append(" and t.d007 < ").append(StringUtil.vString(paramMap.get("end_d007")));
/*      */       }
/*  204 */       if (paramMap.containsKey("sql_d007")) {
/*  205 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d007")));
/*      */       }
/*  207 */       if (paramMap.containsKey("d008")) {
/*  208 */         stringBuffer.append(" and t.d008 = ").append(StringUtil.vString(paramMap.get("d008")));
/*      */       }
/*  210 */       if (paramMap.containsKey("begin_d008")) {
/*  211 */         stringBuffer.append(" and t.d008 >= ").append(StringUtil.vString(paramMap.get("begin_d008")));
/*      */       }
/*  213 */       if (paramMap.containsKey("end_d008")) {
/*  214 */         stringBuffer.append(" and t.d008 < ").append(StringUtil.vString(paramMap.get("end_d008")));
/*      */       }
/*  216 */       if (paramMap.containsKey("sql_d008")) {
/*  217 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d008")));
/*      */       }
/*  219 */       if (paramMap.containsKey("d009")) {
/*  220 */         stringBuffer.append(" and t.d009 = ").append(StringUtil.vString(paramMap.get("d009")));
/*      */       }
/*  222 */       if (paramMap.containsKey("begin_d009")) {
/*  223 */         stringBuffer.append(" and t.d009 >= ").append(StringUtil.vString(paramMap.get("begin_d009")));
/*      */       }
/*  225 */       if (paramMap.containsKey("end_d009")) {
/*  226 */         stringBuffer.append(" and t.d009 < ").append(StringUtil.vString(paramMap.get("end_d009")));
/*      */       }
/*  228 */       if (paramMap.containsKey("sql_d009")) {
/*  229 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d009")));
/*      */       }
/*  231 */       if (paramMap.containsKey("d010")) {
/*  232 */         stringBuffer.append(" and t.d010 = ").append(StringUtil.vString(paramMap.get("d010")));
/*      */       }
/*  234 */       if (paramMap.containsKey("begin_d010")) {
/*  235 */         stringBuffer.append(" and t.d010 >= ").append(StringUtil.vString(paramMap.get("begin_d010")));
/*      */       }
/*  237 */       if (paramMap.containsKey("end_d010")) {
/*  238 */         stringBuffer.append(" and t.d010 < ").append(StringUtil.vString(paramMap.get("end_d010")));
/*      */       }
/*  240 */       if (paramMap.containsKey("sql_d010")) {
/*  241 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d010")));
/*      */       }
/*  243 */       if (paramMap.containsKey("d011")) {
/*  244 */         stringBuffer.append(" and t.d011 = ").append(StringUtil.vString(paramMap.get("d011")));
/*      */       }
/*  246 */       if (paramMap.containsKey("begin_d011")) {
/*  247 */         stringBuffer.append(" and t.d011 >= ").append(StringUtil.vString(paramMap.get("begin_d011")));
/*      */       }
/*  249 */       if (paramMap.containsKey("end_d011")) {
/*  250 */         stringBuffer.append(" and t.d011 < ").append(StringUtil.vString(paramMap.get("end_d011")));
/*      */       }
/*  252 */       if (paramMap.containsKey("sql_d011")) {
/*  253 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d011")));
/*      */       }
/*  255 */       if (paramMap.containsKey("d012")) {
/*  256 */         stringBuffer.append(" and t.d012 = ").append(StringUtil.vString(paramMap.get("d012")));
/*      */       }
/*  258 */       if (paramMap.containsKey("begin_d012")) {
/*  259 */         stringBuffer.append(" and t.d012 >= ").append(StringUtil.vString(paramMap.get("begin_d012")));
/*      */       }
/*  261 */       if (paramMap.containsKey("end_d012")) {
/*  262 */         stringBuffer.append(" and t.d012 < ").append(StringUtil.vString(paramMap.get("end_d012")));
/*      */       }
/*  264 */       if (paramMap.containsKey("sql_d012")) {
/*  265 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d012")));
/*      */       }
/*  267 */       if (paramMap.containsKey("d013")) {
/*  268 */         stringBuffer.append(" and t.d013 = ").append(StringUtil.vString(paramMap.get("d013")));
/*      */       }
/*  270 */       if (paramMap.containsKey("begin_d013")) {
/*  271 */         stringBuffer.append(" and t.d013 >= ").append(StringUtil.vString(paramMap.get("begin_d013")));
/*      */       }
/*  273 */       if (paramMap.containsKey("end_d013")) {
/*  274 */         stringBuffer.append(" and t.d013 < ").append(StringUtil.vString(paramMap.get("end_d013")));
/*      */       }
/*  276 */       if (paramMap.containsKey("sql_d013")) {
/*  277 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d013")));
/*      */       }
/*  279 */       if (paramMap.containsKey("d014")) {
/*  280 */         stringBuffer.append(" and t.d014 = ").append(StringUtil.vString(paramMap.get("d014")));
/*      */       }
/*  282 */       if (paramMap.containsKey("begin_d014")) {
/*  283 */         stringBuffer.append(" and t.d014 >= ").append(StringUtil.vString(paramMap.get("begin_d014")));
/*      */       }
/*  285 */       if (paramMap.containsKey("end_d014")) {
/*  286 */         stringBuffer.append(" and t.d014 < ").append(StringUtil.vString(paramMap.get("end_d014")));
/*      */       }
/*  288 */       if (paramMap.containsKey("sql_d014")) {
/*  289 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d014")));
/*      */       }
/*  291 */       if (paramMap.containsKey("d015")) {
/*  292 */         stringBuffer.append(" and t.d015 = ").append(StringUtil.vString(paramMap.get("d015")));
/*      */       }
/*  294 */       if (paramMap.containsKey("begin_d015")) {
/*  295 */         stringBuffer.append(" and t.d015 >= ").append(StringUtil.vString(paramMap.get("begin_d015")));
/*      */       }
/*  297 */       if (paramMap.containsKey("end_d015")) {
/*  298 */         stringBuffer.append(" and t.d015 < ").append(StringUtil.vString(paramMap.get("end_d015")));
/*      */       }
/*  300 */       if (paramMap.containsKey("sql_d015")) {
/*  301 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d015")));
/*      */       }
/*  303 */       if (paramMap.containsKey("d016")) {
/*  304 */         stringBuffer.append(" and t.d016 = ").append(StringUtil.vString(paramMap.get("d016")));
/*      */       }
/*  306 */       if (paramMap.containsKey("begin_d016")) {
/*  307 */         stringBuffer.append(" and t.d016 >= ").append(StringUtil.vString(paramMap.get("begin_d016")));
/*      */       }
/*  309 */       if (paramMap.containsKey("end_d016")) {
/*  310 */         stringBuffer.append(" and t.d016 < ").append(StringUtil.vString(paramMap.get("end_d016")));
/*      */       }
/*  312 */       if (paramMap.containsKey("sql_d016")) {
/*  313 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d016")));
/*      */       }
/*  315 */       if (paramMap.containsKey("d017")) {
/*  316 */         stringBuffer.append(" and t.d017 = ").append(StringUtil.vString(paramMap.get("d017")));
/*      */       }
/*  318 */       if (paramMap.containsKey("begin_d017")) {
/*  319 */         stringBuffer.append(" and t.d017 >= ").append(StringUtil.vString(paramMap.get("begin_d017")));
/*      */       }
/*  321 */       if (paramMap.containsKey("end_d017")) {
/*  322 */         stringBuffer.append(" and t.d017 < ").append(StringUtil.vString(paramMap.get("end_d017")));
/*      */       }
/*  324 */       if (paramMap.containsKey("sql_d017")) {
/*  325 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d017")));
/*      */       }
/*  327 */       if (paramMap.containsKey("d018")) {
/*  328 */         stringBuffer.append(" and t.d018 = ").append(StringUtil.vString(paramMap.get("d018")));
/*      */       }
/*  330 */       if (paramMap.containsKey("begin_d018")) {
/*  331 */         stringBuffer.append(" and t.d018 >= ").append(StringUtil.vString(paramMap.get("begin_d018")));
/*      */       }
/*  333 */       if (paramMap.containsKey("end_d018")) {
/*  334 */         stringBuffer.append(" and t.d018 < ").append(StringUtil.vString(paramMap.get("end_d018")));
/*      */       }
/*  336 */       if (paramMap.containsKey("sql_d018")) {
/*  337 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d018")));
/*      */       }
/*  339 */       if (paramMap.containsKey("d019")) {
/*  340 */         stringBuffer.append(" and t.d019 = ").append(StringUtil.vString(paramMap.get("d019")));
/*      */       }
/*  342 */       if (paramMap.containsKey("begin_d019")) {
/*  343 */         stringBuffer.append(" and t.d019 >= ").append(StringUtil.vString(paramMap.get("begin_d019")));
/*      */       }
/*  345 */       if (paramMap.containsKey("end_d019")) {
/*  346 */         stringBuffer.append(" and t.d019 < ").append(StringUtil.vString(paramMap.get("end_d019")));
/*      */       }
/*  348 */       if (paramMap.containsKey("sql_d019")) {
/*  349 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d019")));
/*      */       }
/*  351 */       if (paramMap.containsKey("d020")) {
/*  352 */         stringBuffer.append(" and t.d020 = ").append(StringUtil.vString(paramMap.get("d020")));
/*      */       }
/*  354 */       if (paramMap.containsKey("begin_d020")) {
/*  355 */         stringBuffer.append(" and t.d020 >= ").append(StringUtil.vString(paramMap.get("begin_d020")));
/*      */       }
/*  357 */       if (paramMap.containsKey("end_d020")) {
/*  358 */         stringBuffer.append(" and t.d020 < ").append(StringUtil.vString(paramMap.get("end_d020")));
/*      */       }
/*  360 */       if (paramMap.containsKey("sql_d020")) {
/*  361 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d020")));
/*      */       }
/*  363 */       if (paramMap.containsKey("d021")) {
/*  364 */         stringBuffer.append(" and t.d021 = ").append(StringUtil.vString(paramMap.get("d021")));
/*      */       }
/*  366 */       if (paramMap.containsKey("begin_d021")) {
/*  367 */         stringBuffer.append(" and t.d021 >= ").append(StringUtil.vString(paramMap.get("begin_d021")));
/*      */       }
/*  369 */       if (paramMap.containsKey("end_d021")) {
/*  370 */         stringBuffer.append(" and t.d021 < ").append(StringUtil.vString(paramMap.get("end_d021")));
/*      */       }
/*  372 */       if (paramMap.containsKey("sql_d021")) {
/*  373 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d021")));
/*      */       }
/*  375 */       if (paramMap.containsKey("d022")) {
/*  376 */         stringBuffer.append(" and t.d022 = ").append(StringUtil.vString(paramMap.get("d022")));
/*      */       }
/*  378 */       if (paramMap.containsKey("begin_d022")) {
/*  379 */         stringBuffer.append(" and t.d022 >= ").append(StringUtil.vString(paramMap.get("begin_d022")));
/*      */       }
/*  381 */       if (paramMap.containsKey("end_d022")) {
/*  382 */         stringBuffer.append(" and t.d022 < ").append(StringUtil.vString(paramMap.get("end_d022")));
/*      */       }
/*  384 */       if (paramMap.containsKey("sql_d022")) {
/*  385 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d022")));
/*      */       }
/*  387 */       if (paramMap.containsKey("d023")) {
/*  388 */         stringBuffer.append(" and t.d023 = ").append(StringUtil.vString(paramMap.get("d023")));
/*      */       }
/*  390 */       if (paramMap.containsKey("begin_d023")) {
/*  391 */         stringBuffer.append(" and t.d023 >= ").append(StringUtil.vString(paramMap.get("begin_d023")));
/*      */       }
/*  393 */       if (paramMap.containsKey("end_d023")) {
/*  394 */         stringBuffer.append(" and t.d023 < ").append(StringUtil.vString(paramMap.get("end_d023")));
/*      */       }
/*  396 */       if (paramMap.containsKey("sql_d023")) {
/*  397 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d023")));
/*      */       }
/*  399 */       if (paramMap.containsKey("d024")) {
/*  400 */         stringBuffer.append(" and t.d024 = ").append(StringUtil.vString(paramMap.get("d024")));
/*      */       }
/*  402 */       if (paramMap.containsKey("begin_d024")) {
/*  403 */         stringBuffer.append(" and t.d024 >= ").append(StringUtil.vString(paramMap.get("begin_d024")));
/*      */       }
/*  405 */       if (paramMap.containsKey("end_d024")) {
/*  406 */         stringBuffer.append(" and t.d024 < ").append(StringUtil.vString(paramMap.get("end_d024")));
/*      */       }
/*  408 */       if (paramMap.containsKey("sql_d024")) {
/*  409 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d024")));
/*      */       }
/*  411 */       if (paramMap.containsKey("d025")) {
/*  412 */         stringBuffer.append(" and t.d025 = ").append(StringUtil.vString(paramMap.get("d025")));
/*      */       }
/*  414 */       if (paramMap.containsKey("begin_d025")) {
/*  415 */         stringBuffer.append(" and t.d025 >= ").append(StringUtil.vString(paramMap.get("begin_d025")));
/*      */       }
/*  417 */       if (paramMap.containsKey("end_d025")) {
/*  418 */         stringBuffer.append(" and t.d025 < ").append(StringUtil.vString(paramMap.get("end_d025")));
/*      */       }
/*  420 */       if (paramMap.containsKey("sql_d025")) {
/*  421 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d025")));
/*      */       }
/*  423 */       if (paramMap.containsKey("d026")) {
/*  424 */         stringBuffer.append(" and t.d026 = ").append(StringUtil.vString(paramMap.get("d026")));
/*      */       }
/*  426 */       if (paramMap.containsKey("begin_d026")) {
/*  427 */         stringBuffer.append(" and t.d026 >= ").append(StringUtil.vString(paramMap.get("begin_d026")));
/*      */       }
/*  429 */       if (paramMap.containsKey("end_d026")) {
/*  430 */         stringBuffer.append(" and t.d026 < ").append(StringUtil.vString(paramMap.get("end_d026")));
/*      */       }
/*  432 */       if (paramMap.containsKey("sql_d026")) {
/*  433 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d026")));
/*      */       }
/*  435 */       if (paramMap.containsKey("d027")) {
/*  436 */         stringBuffer.append(" and t.d027 = ").append(StringUtil.vString(paramMap.get("d027")));
/*      */       }
/*  438 */       if (paramMap.containsKey("begin_d027")) {
/*  439 */         stringBuffer.append(" and t.d027 >= ").append(StringUtil.vString(paramMap.get("begin_d027")));
/*      */       }
/*  441 */       if (paramMap.containsKey("end_d027")) {
/*  442 */         stringBuffer.append(" and t.d027 < ").append(StringUtil.vString(paramMap.get("end_d027")));
/*      */       }
/*  444 */       if (paramMap.containsKey("sql_d027")) {
/*  445 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d027")));
/*      */       }
/*  447 */       if (paramMap.containsKey("d028")) {
/*  448 */         stringBuffer.append(" and t.d028 = ").append(StringUtil.vString(paramMap.get("d028")));
/*      */       }
/*  450 */       if (paramMap.containsKey("begin_d028")) {
/*  451 */         stringBuffer.append(" and t.d028 >= ").append(StringUtil.vString(paramMap.get("begin_d028")));
/*      */       }
/*  453 */       if (paramMap.containsKey("end_d028")) {
/*  454 */         stringBuffer.append(" and t.d028 < ").append(StringUtil.vString(paramMap.get("end_d028")));
/*      */       }
/*  456 */       if (paramMap.containsKey("sql_d028")) {
/*  457 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d028")));
/*      */       }
/*  459 */       if (paramMap.containsKey("d029")) {
/*  460 */         stringBuffer.append(" and t.d029 = ").append(StringUtil.vString(paramMap.get("d029")));
/*      */       }
/*  462 */       if (paramMap.containsKey("begin_d029")) {
/*  463 */         stringBuffer.append(" and t.d029 >= ").append(StringUtil.vString(paramMap.get("begin_d029")));
/*      */       }
/*  465 */       if (paramMap.containsKey("end_d029")) {
/*  466 */         stringBuffer.append(" and t.d029 < ").append(StringUtil.vString(paramMap.get("end_d029")));
/*      */       }
/*  468 */       if (paramMap.containsKey("sql_d029")) {
/*  469 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d029")));
/*      */       }
/*  471 */       if (paramMap.containsKey("d030")) {
/*  472 */         stringBuffer.append(" and t.d030 = ").append(StringUtil.vString(paramMap.get("d030")));
/*      */       }
/*  474 */       if (paramMap.containsKey("begin_d030")) {
/*  475 */         stringBuffer.append(" and t.d030 >= ").append(StringUtil.vString(paramMap.get("begin_d030")));
/*      */       }
/*  477 */       if (paramMap.containsKey("end_d030")) {
/*  478 */         stringBuffer.append(" and t.d030 < ").append(StringUtil.vString(paramMap.get("end_d030")));
/*      */       }
/*  480 */       if (paramMap.containsKey("sql_d030")) {
/*  481 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d030")));
/*      */       }
/*  483 */       if (paramMap.containsKey("d031")) {
/*  484 */         stringBuffer.append(" and t.d031 = ").append(StringUtil.vString(paramMap.get("d031")));
/*      */       }
/*  486 */       if (paramMap.containsKey("begin_d031")) {
/*  487 */         stringBuffer.append(" and t.d031 >= ").append(StringUtil.vString(paramMap.get("begin_d031")));
/*      */       }
/*  489 */       if (paramMap.containsKey("end_d031")) {
/*  490 */         stringBuffer.append(" and t.d031 < ").append(StringUtil.vString(paramMap.get("end_d031")));
/*      */       }
/*  492 */       if (paramMap.containsKey("sql_d031")) {
/*  493 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d031")));
/*      */       }
/*  495 */       if (paramMap.containsKey("w001")) {
/*  496 */         stringBuffer.append(" and t.w001 = ").append(StringUtil.vString(paramMap.get("w001")));
/*      */       }
/*  498 */       if (paramMap.containsKey("begin_w001")) {
/*  499 */         stringBuffer.append(" and t.w001 >= ").append(StringUtil.vString(paramMap.get("begin_w001")));
/*      */       }
/*  501 */       if (paramMap.containsKey("end_w001")) {
/*  502 */         stringBuffer.append(" and t.w001 < ").append(StringUtil.vString(paramMap.get("end_w001")));
/*      */       }
/*  504 */       if (paramMap.containsKey("sql_w001")) {
/*  505 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w001")));
/*      */       }
/*  507 */       if (paramMap.containsKey("w002")) {
/*  508 */         stringBuffer.append(" and t.w002 = ").append(StringUtil.vString(paramMap.get("w002")));
/*      */       }
/*  510 */       if (paramMap.containsKey("begin_w002")) {
/*  511 */         stringBuffer.append(" and t.w002 >= ").append(StringUtil.vString(paramMap.get("begin_w002")));
/*      */       }
/*  513 */       if (paramMap.containsKey("end_w002")) {
/*  514 */         stringBuffer.append(" and t.w002 < ").append(StringUtil.vString(paramMap.get("end_w002")));
/*      */       }
/*  516 */       if (paramMap.containsKey("sql_w002")) {
/*  517 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w002")));
/*      */       }
/*  519 */       if (paramMap.containsKey("w003")) {
/*  520 */         stringBuffer.append(" and t.w003 = ").append(StringUtil.vString(paramMap.get("w003")));
/*      */       }
/*  522 */       if (paramMap.containsKey("begin_w003")) {
/*  523 */         stringBuffer.append(" and t.w003 >= ").append(StringUtil.vString(paramMap.get("begin_w003")));
/*      */       }
/*  525 */       if (paramMap.containsKey("end_w003")) {
/*  526 */         stringBuffer.append(" and t.w003 < ").append(StringUtil.vString(paramMap.get("end_w003")));
/*      */       }
/*  528 */       if (paramMap.containsKey("sql_w003")) {
/*  529 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w003")));
/*      */       }
/*  531 */       if (paramMap.containsKey("w004")) {
/*  532 */         stringBuffer.append(" and t.w004 = ").append(StringUtil.vString(paramMap.get("w004")));
/*      */       }
/*  534 */       if (paramMap.containsKey("begin_w004")) {
/*  535 */         stringBuffer.append(" and t.w004 >= ").append(StringUtil.vString(paramMap.get("begin_w004")));
/*      */       }
/*  537 */       if (paramMap.containsKey("end_w004")) {
/*  538 */         stringBuffer.append(" and t.w004 < ").append(StringUtil.vString(paramMap.get("end_w004")));
/*      */       }
/*  540 */       if (paramMap.containsKey("sql_w004")) {
/*  541 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w004")));
/*      */       }
/*  543 */       if (paramMap.containsKey("w005")) {
/*  544 */         stringBuffer.append(" and t.w005 = ").append(StringUtil.vString(paramMap.get("w005")));
/*      */       }
/*  546 */       if (paramMap.containsKey("begin_w005")) {
/*  547 */         stringBuffer.append(" and t.w005 >= ").append(StringUtil.vString(paramMap.get("begin_w005")));
/*      */       }
/*  549 */       if (paramMap.containsKey("end_w005")) {
/*  550 */         stringBuffer.append(" and t.w005 < ").append(StringUtil.vString(paramMap.get("end_w005")));
/*      */       }
/*  552 */       if (paramMap.containsKey("sql_w005")) {
/*  553 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w005")));
/*      */       }
/*  555 */       if (paramMap.containsKey("w006")) {
/*  556 */         stringBuffer.append(" and t.w006 = ").append(StringUtil.vString(paramMap.get("w006")));
/*      */       }
/*  558 */       if (paramMap.containsKey("begin_w006")) {
/*  559 */         stringBuffer.append(" and t.w006 >= ").append(StringUtil.vString(paramMap.get("begin_w006")));
/*      */       }
/*  561 */       if (paramMap.containsKey("end_w006")) {
/*  562 */         stringBuffer.append(" and t.w006 < ").append(StringUtil.vString(paramMap.get("end_w006")));
/*      */       }
/*  564 */       if (paramMap.containsKey("sql_w006")) {
/*  565 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w006")));
/*      */       }
/*  567 */       if (paramMap.containsKey("w007")) {
/*  568 */         stringBuffer.append(" and t.w007 = ").append(StringUtil.vString(paramMap.get("w007")));
/*      */       }
/*  570 */       if (paramMap.containsKey("begin_w007")) {
/*  571 */         stringBuffer.append(" and t.w007 >= ").append(StringUtil.vString(paramMap.get("begin_w007")));
/*      */       }
/*  573 */       if (paramMap.containsKey("end_w007")) {
/*  574 */         stringBuffer.append(" and t.w007 < ").append(StringUtil.vString(paramMap.get("end_w007")));
/*      */       }
/*  576 */       if (paramMap.containsKey("sql_w007")) {
/*  577 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w007")));
/*      */       }
/*  579 */       if (paramMap.containsKey("field001")) {
/*  580 */         stringBuffer.append(" and t.field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*      */       }
/*  582 */       if (paramMap.containsKey("begin_field001")) {
/*  583 */         stringBuffer.append(" and t.field001 >= ").append(StringUtil.vString(paramMap.get("begin_field001")));
/*      */       }
/*  585 */       if (paramMap.containsKey("end_field001")) {
/*  586 */         stringBuffer.append(" and t.field001 < ").append(StringUtil.vString(paramMap.get("end_field001")));
/*      */       }
/*  588 */       if (paramMap.containsKey("sql_field001")) {
/*  589 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*      */       }
/*  591 */       if (paramMap.containsKey("field003")) {
/*  592 */         stringBuffer.append(" and t.field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*      */       }
/*  594 */       if (paramMap.containsKey("begin_field003")) {
/*  595 */         stringBuffer.append(" and t.field003 >= ").append(StringUtil.vString(paramMap.get("begin_field003")));
/*      */       }
/*  597 */       if (paramMap.containsKey("end_field003")) {
/*  598 */         stringBuffer.append(" and t.field003 < ").append(StringUtil.vString(paramMap.get("end_field003")));
/*      */       }
/*  600 */       if (paramMap.containsKey("sql_field003")) {
/*  601 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*      */       }
/*  603 */       if (paramMap.containsKey("field004")) {
/*  604 */         stringBuffer.append(" and t.field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*      */       }
/*  606 */       if (paramMap.containsKey("begin_field004")) {
/*  607 */         stringBuffer.append(" and t.field004 >= ").append(StringUtil.vString(paramMap.get("begin_field004")));
/*      */       }
/*  609 */       if (paramMap.containsKey("end_field004")) {
/*  610 */         stringBuffer.append(" and t.field004 < ").append(StringUtil.vString(paramMap.get("end_field004")));
/*      */       }
/*  612 */       if (paramMap.containsKey("sql_field004")) {
/*  613 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*      */       }
/*  615 */       if (paramMap.containsKey("field005")) {
/*  616 */         stringBuffer.append(" and t.field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*      */       }
/*  618 */       if (paramMap.containsKey("begin_field005")) {
/*  619 */         stringBuffer.append(" and t.field005 >= ").append(StringUtil.vString(paramMap.get("begin_field005")));
/*      */       }
/*  621 */       if (paramMap.containsKey("end_field005")) {
/*  622 */         stringBuffer.append(" and t.field005 < ").append(StringUtil.vString(paramMap.get("end_field005")));
/*      */       }
/*  624 */       if (paramMap.containsKey("sql_field005")) {
/*  625 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*      */       }
/*  627 */       if (paramMap.containsKey("field002")) {
/*  628 */         stringBuffer.append(" and t.field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*      */       }
/*  630 */       if (paramMap.containsKey("like_field002")) {
/*  631 */         stringBuffer.append(" and t.field002 like '%").append(StringUtil.vString(paramMap.get("like_field002"))).append("%'");
/*      */       }
/*  633 */       if (paramMap.containsKey("sql_field002")) {
/*  634 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*      */       }
/*  636 */       if (paramMap.containsKey("mfsql")) {
/*  637 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("mfsql")));
/*      */       }
/*  639 */       if (paramMap.containsKey("sqlorderby")) {
/*  640 */         stringBuffer.append(" order by " + StringUtil.vString(paramMap.get("sqlorderby")));
/*      */       } else {
/*  642 */         stringBuffer.append(" order by t.id ").append((StringUtil.vString(paramMap.get("sqlsortway")).length() > 0) ? StringUtil.vString(paramMap.get("sqlsortway")) : "desc");
/*      */       } 
/*      */     } 
/*  645 */     this.rs.executeSql(stringBuffer.toString());
/*  646 */     HrmScheduleShiftsDetail hrmScheduleShiftsDetail = null;
/*  647 */     while (this.rs.next()) {
/*  648 */       hrmScheduleShiftsDetail = new HrmScheduleShiftsDetail();
/*  649 */       hrmScheduleShiftsDetail.setId(Long.valueOf(StringUtil.parseToLong(this.rs.getString("id"))));
/*  650 */       hrmScheduleShiftsDetail.setDelflag(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("delflag"))));
/*  651 */       hrmScheduleShiftsDetail.setMfid(Long.valueOf(StringUtil.parseToLong(this.rs.getString("mfid"))));
/*  652 */       hrmScheduleShiftsDetail.setD001(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d001"))));
/*  653 */       hrmScheduleShiftsDetail.setD002(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d002"))));
/*  654 */       hrmScheduleShiftsDetail.setD003(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d003"))));
/*  655 */       hrmScheduleShiftsDetail.setD004(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d004"))));
/*  656 */       hrmScheduleShiftsDetail.setD005(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d005"))));
/*  657 */       hrmScheduleShiftsDetail.setD006(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d006"))));
/*  658 */       hrmScheduleShiftsDetail.setD007(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d007"))));
/*  659 */       hrmScheduleShiftsDetail.setD008(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d008"))));
/*  660 */       hrmScheduleShiftsDetail.setD009(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d009"))));
/*  661 */       hrmScheduleShiftsDetail.setD010(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d010"))));
/*  662 */       hrmScheduleShiftsDetail.setD011(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d011"))));
/*  663 */       hrmScheduleShiftsDetail.setD012(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d012"))));
/*  664 */       hrmScheduleShiftsDetail.setD013(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d013"))));
/*  665 */       hrmScheduleShiftsDetail.setD014(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d014"))));
/*  666 */       hrmScheduleShiftsDetail.setD015(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d015"))));
/*  667 */       hrmScheduleShiftsDetail.setD016(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d016"))));
/*  668 */       hrmScheduleShiftsDetail.setD017(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d017"))));
/*  669 */       hrmScheduleShiftsDetail.setD018(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d018"))));
/*  670 */       hrmScheduleShiftsDetail.setD019(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d019"))));
/*  671 */       hrmScheduleShiftsDetail.setD020(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d020"))));
/*  672 */       hrmScheduleShiftsDetail.setD021(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d021"))));
/*  673 */       hrmScheduleShiftsDetail.setD022(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d022"))));
/*  674 */       hrmScheduleShiftsDetail.setD023(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d023"))));
/*  675 */       hrmScheduleShiftsDetail.setD024(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d024"))));
/*  676 */       hrmScheduleShiftsDetail.setD025(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d025"))));
/*  677 */       hrmScheduleShiftsDetail.setD026(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d026"))));
/*  678 */       hrmScheduleShiftsDetail.setD027(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d027"))));
/*  679 */       hrmScheduleShiftsDetail.setD028(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d028"))));
/*  680 */       hrmScheduleShiftsDetail.setD029(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d029"))));
/*  681 */       hrmScheduleShiftsDetail.setD030(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d030"))));
/*  682 */       hrmScheduleShiftsDetail.setD031(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("d031"))));
/*  683 */       hrmScheduleShiftsDetail.setW001(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("w001"))));
/*  684 */       hrmScheduleShiftsDetail.setW002(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("w002"))));
/*  685 */       hrmScheduleShiftsDetail.setW003(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("w003"))));
/*  686 */       hrmScheduleShiftsDetail.setW004(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("w004"))));
/*  687 */       hrmScheduleShiftsDetail.setW005(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("w005"))));
/*  688 */       hrmScheduleShiftsDetail.setW006(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("w006"))));
/*  689 */       hrmScheduleShiftsDetail.setW007(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("w007"))));
/*  690 */       hrmScheduleShiftsDetail.setField001(Long.valueOf(StringUtil.parseToLong(this.rs.getString("field001"))));
/*  691 */       hrmScheduleShiftsDetail.setField002(StringUtil.vString(this.rs.getString("field002")));
/*  692 */       hrmScheduleShiftsDetail.setField003(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field003"))));
/*  693 */       hrmScheduleShiftsDetail.setField004(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field004"))));
/*  694 */       hrmScheduleShiftsDetail.setField005(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field005"))));
/*  695 */       hrmScheduleShiftsDetail.setT2Field003(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("t2Field003"))));
/*  696 */       arrayList.add(hrmScheduleShiftsDetail);
/*      */     } 
/*  698 */     return arrayList;
/*      */   }
/*      */   
/*      */   public HrmScheduleShiftsDetail get(Comparable paramComparable) {
/*  702 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*  703 */     hashMap.put("id", paramComparable);
/*  704 */     List<HrmScheduleShiftsDetail> list = find((Map)hashMap);
/*  705 */     return (list != null && list.size() > 0) ? list.get(0) : null;
/*      */   }
/*      */   
/*      */   public int count(Map<String, Comparable> paramMap) {
/*  709 */     StringBuffer stringBuffer = new StringBuffer("select count(id) as result from hrm_schedule_shifts_detail where 1 = 1");
/*  710 */     if (paramMap != null) {
/*  711 */       if (paramMap.containsKey("id")) {
/*  712 */         stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*      */       }
/*  714 */       if (paramMap.containsKey("sql_id")) {
/*  715 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*      */       }
/*  717 */       if (paramMap.containsKey("delflag")) {
/*  718 */         stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*      */       }
/*  720 */       if (paramMap.containsKey("sql_delflag")) {
/*  721 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*      */       }
/*  723 */       if (paramMap.containsKey("d001")) {
/*  724 */         stringBuffer.append(" and d001 = ").append(StringUtil.vString(paramMap.get("d001")));
/*      */       }
/*  726 */       if (paramMap.containsKey("sql_d001")) {
/*  727 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d001")));
/*      */       }
/*  729 */       if (paramMap.containsKey("d002")) {
/*  730 */         stringBuffer.append(" and d002 = ").append(StringUtil.vString(paramMap.get("d002")));
/*      */       }
/*  732 */       if (paramMap.containsKey("sql_d002")) {
/*  733 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d002")));
/*      */       }
/*  735 */       if (paramMap.containsKey("d003")) {
/*  736 */         stringBuffer.append(" and d003 = ").append(StringUtil.vString(paramMap.get("d003")));
/*      */       }
/*  738 */       if (paramMap.containsKey("sql_d003")) {
/*  739 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d003")));
/*      */       }
/*  741 */       if (paramMap.containsKey("d004")) {
/*  742 */         stringBuffer.append(" and d004 = ").append(StringUtil.vString(paramMap.get("d004")));
/*      */       }
/*  744 */       if (paramMap.containsKey("sql_d004")) {
/*  745 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d004")));
/*      */       }
/*  747 */       if (paramMap.containsKey("d005")) {
/*  748 */         stringBuffer.append(" and d005 = ").append(StringUtil.vString(paramMap.get("d005")));
/*      */       }
/*  750 */       if (paramMap.containsKey("sql_d005")) {
/*  751 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d005")));
/*      */       }
/*  753 */       if (paramMap.containsKey("d006")) {
/*  754 */         stringBuffer.append(" and d006 = ").append(StringUtil.vString(paramMap.get("d006")));
/*      */       }
/*  756 */       if (paramMap.containsKey("sql_d006")) {
/*  757 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d006")));
/*      */       }
/*  759 */       if (paramMap.containsKey("d007")) {
/*  760 */         stringBuffer.append(" and d007 = ").append(StringUtil.vString(paramMap.get("d007")));
/*      */       }
/*  762 */       if (paramMap.containsKey("sql_d007")) {
/*  763 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d007")));
/*      */       }
/*  765 */       if (paramMap.containsKey("d008")) {
/*  766 */         stringBuffer.append(" and d008 = ").append(StringUtil.vString(paramMap.get("d008")));
/*      */       }
/*  768 */       if (paramMap.containsKey("sql_d008")) {
/*  769 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d008")));
/*      */       }
/*  771 */       if (paramMap.containsKey("d009")) {
/*  772 */         stringBuffer.append(" and d009 = ").append(StringUtil.vString(paramMap.get("d009")));
/*      */       }
/*  774 */       if (paramMap.containsKey("sql_d009")) {
/*  775 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d009")));
/*      */       }
/*  777 */       if (paramMap.containsKey("d010")) {
/*  778 */         stringBuffer.append(" and d010 = ").append(StringUtil.vString(paramMap.get("d010")));
/*      */       }
/*  780 */       if (paramMap.containsKey("sql_d010")) {
/*  781 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d010")));
/*      */       }
/*  783 */       if (paramMap.containsKey("d011")) {
/*  784 */         stringBuffer.append(" and d011 = ").append(StringUtil.vString(paramMap.get("d011")));
/*      */       }
/*  786 */       if (paramMap.containsKey("sql_d011")) {
/*  787 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d011")));
/*      */       }
/*  789 */       if (paramMap.containsKey("d012")) {
/*  790 */         stringBuffer.append(" and d012 = ").append(StringUtil.vString(paramMap.get("d012")));
/*      */       }
/*  792 */       if (paramMap.containsKey("sql_d012")) {
/*  793 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d012")));
/*      */       }
/*  795 */       if (paramMap.containsKey("d013")) {
/*  796 */         stringBuffer.append(" and d013 = ").append(StringUtil.vString(paramMap.get("d013")));
/*      */       }
/*  798 */       if (paramMap.containsKey("sql_d013")) {
/*  799 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d013")));
/*      */       }
/*  801 */       if (paramMap.containsKey("d014")) {
/*  802 */         stringBuffer.append(" and d014 = ").append(StringUtil.vString(paramMap.get("d014")));
/*      */       }
/*  804 */       if (paramMap.containsKey("sql_d014")) {
/*  805 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d014")));
/*      */       }
/*  807 */       if (paramMap.containsKey("d015")) {
/*  808 */         stringBuffer.append(" and d015 = ").append(StringUtil.vString(paramMap.get("d015")));
/*      */       }
/*  810 */       if (paramMap.containsKey("sql_d015")) {
/*  811 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d015")));
/*      */       }
/*  813 */       if (paramMap.containsKey("d016")) {
/*  814 */         stringBuffer.append(" and d016 = ").append(StringUtil.vString(paramMap.get("d016")));
/*      */       }
/*  816 */       if (paramMap.containsKey("sql_d016")) {
/*  817 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d016")));
/*      */       }
/*  819 */       if (paramMap.containsKey("d017")) {
/*  820 */         stringBuffer.append(" and d017 = ").append(StringUtil.vString(paramMap.get("d017")));
/*      */       }
/*  822 */       if (paramMap.containsKey("sql_d017")) {
/*  823 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d017")));
/*      */       }
/*  825 */       if (paramMap.containsKey("d018")) {
/*  826 */         stringBuffer.append(" and d018 = ").append(StringUtil.vString(paramMap.get("d018")));
/*      */       }
/*  828 */       if (paramMap.containsKey("sql_d018")) {
/*  829 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d018")));
/*      */       }
/*  831 */       if (paramMap.containsKey("d019")) {
/*  832 */         stringBuffer.append(" and d019 = ").append(StringUtil.vString(paramMap.get("d019")));
/*      */       }
/*  834 */       if (paramMap.containsKey("sql_d019")) {
/*  835 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d019")));
/*      */       }
/*  837 */       if (paramMap.containsKey("d020")) {
/*  838 */         stringBuffer.append(" and d020 = ").append(StringUtil.vString(paramMap.get("d020")));
/*      */       }
/*  840 */       if (paramMap.containsKey("sql_d020")) {
/*  841 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d020")));
/*      */       }
/*  843 */       if (paramMap.containsKey("d021")) {
/*  844 */         stringBuffer.append(" and d021 = ").append(StringUtil.vString(paramMap.get("d021")));
/*      */       }
/*  846 */       if (paramMap.containsKey("sql_d021")) {
/*  847 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d021")));
/*      */       }
/*  849 */       if (paramMap.containsKey("d022")) {
/*  850 */         stringBuffer.append(" and d022 = ").append(StringUtil.vString(paramMap.get("d022")));
/*      */       }
/*  852 */       if (paramMap.containsKey("sql_d022")) {
/*  853 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d022")));
/*      */       }
/*  855 */       if (paramMap.containsKey("d023")) {
/*  856 */         stringBuffer.append(" and d023 = ").append(StringUtil.vString(paramMap.get("d023")));
/*      */       }
/*  858 */       if (paramMap.containsKey("sql_d023")) {
/*  859 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d023")));
/*      */       }
/*  861 */       if (paramMap.containsKey("d024")) {
/*  862 */         stringBuffer.append(" and d024 = ").append(StringUtil.vString(paramMap.get("d024")));
/*      */       }
/*  864 */       if (paramMap.containsKey("sql_d024")) {
/*  865 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d024")));
/*      */       }
/*  867 */       if (paramMap.containsKey("d025")) {
/*  868 */         stringBuffer.append(" and d025 = ").append(StringUtil.vString(paramMap.get("d025")));
/*      */       }
/*  870 */       if (paramMap.containsKey("sql_d025")) {
/*  871 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d025")));
/*      */       }
/*  873 */       if (paramMap.containsKey("d026")) {
/*  874 */         stringBuffer.append(" and d026 = ").append(StringUtil.vString(paramMap.get("d026")));
/*      */       }
/*  876 */       if (paramMap.containsKey("sql_d026")) {
/*  877 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d026")));
/*      */       }
/*  879 */       if (paramMap.containsKey("d027")) {
/*  880 */         stringBuffer.append(" and d027 = ").append(StringUtil.vString(paramMap.get("d027")));
/*      */       }
/*  882 */       if (paramMap.containsKey("sql_d027")) {
/*  883 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d027")));
/*      */       }
/*  885 */       if (paramMap.containsKey("d028")) {
/*  886 */         stringBuffer.append(" and d028 = ").append(StringUtil.vString(paramMap.get("d028")));
/*      */       }
/*  888 */       if (paramMap.containsKey("sql_d028")) {
/*  889 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d028")));
/*      */       }
/*  891 */       if (paramMap.containsKey("d029")) {
/*  892 */         stringBuffer.append(" and d029 = ").append(StringUtil.vString(paramMap.get("d029")));
/*      */       }
/*  894 */       if (paramMap.containsKey("sql_d029")) {
/*  895 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d029")));
/*      */       }
/*  897 */       if (paramMap.containsKey("d030")) {
/*  898 */         stringBuffer.append(" and d030 = ").append(StringUtil.vString(paramMap.get("d030")));
/*      */       }
/*  900 */       if (paramMap.containsKey("sql_d030")) {
/*  901 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d030")));
/*      */       }
/*  903 */       if (paramMap.containsKey("d031")) {
/*  904 */         stringBuffer.append(" and d031 = ").append(StringUtil.vString(paramMap.get("d031")));
/*      */       }
/*  906 */       if (paramMap.containsKey("sql_d031")) {
/*  907 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d031")));
/*      */       }
/*  909 */       if (paramMap.containsKey("w001")) {
/*  910 */         stringBuffer.append(" and w001 = ").append(StringUtil.vString(paramMap.get("w001")));
/*      */       }
/*  912 */       if (paramMap.containsKey("sql_w001")) {
/*  913 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w001")));
/*      */       }
/*  915 */       if (paramMap.containsKey("w002")) {
/*  916 */         stringBuffer.append(" and w002 = ").append(StringUtil.vString(paramMap.get("w002")));
/*      */       }
/*  918 */       if (paramMap.containsKey("sql_w002")) {
/*  919 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w002")));
/*      */       }
/*  921 */       if (paramMap.containsKey("w003")) {
/*  922 */         stringBuffer.append(" and w003 = ").append(StringUtil.vString(paramMap.get("w003")));
/*      */       }
/*  924 */       if (paramMap.containsKey("sql_w003")) {
/*  925 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w003")));
/*      */       }
/*  927 */       if (paramMap.containsKey("w004")) {
/*  928 */         stringBuffer.append(" and w004 = ").append(StringUtil.vString(paramMap.get("w004")));
/*      */       }
/*  930 */       if (paramMap.containsKey("sql_w004")) {
/*  931 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w004")));
/*      */       }
/*  933 */       if (paramMap.containsKey("w005")) {
/*  934 */         stringBuffer.append(" and w005 = ").append(StringUtil.vString(paramMap.get("w005")));
/*      */       }
/*  936 */       if (paramMap.containsKey("sql_w005")) {
/*  937 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w005")));
/*      */       }
/*  939 */       if (paramMap.containsKey("w006")) {
/*  940 */         stringBuffer.append(" and w006 = ").append(StringUtil.vString(paramMap.get("w006")));
/*      */       }
/*  942 */       if (paramMap.containsKey("sql_w006")) {
/*  943 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w006")));
/*      */       }
/*  945 */       if (paramMap.containsKey("w007")) {
/*  946 */         stringBuffer.append(" and w007 = ").append(StringUtil.vString(paramMap.get("w007")));
/*      */       }
/*  948 */       if (paramMap.containsKey("sql_w007")) {
/*  949 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w007")));
/*      */       }
/*  951 */       if (paramMap.containsKey("field001")) {
/*  952 */         stringBuffer.append(" and field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*      */       }
/*  954 */       if (paramMap.containsKey("sql_field001")) {
/*  955 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*      */       }
/*  957 */       if (paramMap.containsKey("field003")) {
/*  958 */         stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*      */       }
/*  960 */       if (paramMap.containsKey("sql_field003")) {
/*  961 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*      */       }
/*  963 */       if (paramMap.containsKey("field004")) {
/*  964 */         stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*      */       }
/*  966 */       if (paramMap.containsKey("sql_field004")) {
/*  967 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*      */       }
/*  969 */       if (paramMap.containsKey("field005")) {
/*  970 */         stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*      */       }
/*  972 */       if (paramMap.containsKey("sql_field005")) {
/*  973 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*      */       }
/*  975 */       if (paramMap.containsKey("field002")) {
/*  976 */         stringBuffer.append(" and field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*      */       }
/*  978 */       if (paramMap.containsKey("sql_field002")) {
/*  979 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*      */       }
/*      */     } 
/*  982 */     this.rs.executeSql(stringBuffer.toString());
/*  983 */     return this.rs.next() ? this.rs.getInt(1) : 0;
/*      */   }
/*      */   
/*      */   public void delete(Comparable paramComparable) {
/*  987 */     this.rs.executeSql("update hrm_schedule_shifts_detail set delflag = 1 where id in ( " + paramComparable + " ) ");
/*      */   }
/*      */   
/*      */   public void delete(Map<String, Comparable> paramMap) {
/*  991 */     if (paramMap == null || paramMap.isEmpty())
/*  992 */       return;  StringBuffer stringBuffer = new StringBuffer("update hrm_schedule_shifts_detail set delflag = 1  where 1 = 1");
/*  993 */     if (paramMap.containsKey("id")) {
/*  994 */       stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*      */     }
/*  996 */     if (paramMap.containsKey("sql_id")) {
/*  997 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*      */     }
/*  999 */     if (paramMap.containsKey("delflag")) {
/* 1000 */       stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*      */     }
/* 1002 */     if (paramMap.containsKey("sql_delflag")) {
/* 1003 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*      */     }
/* 1005 */     if (paramMap.containsKey("d001")) {
/* 1006 */       stringBuffer.append(" and d001 = ").append(StringUtil.vString(paramMap.get("d001")));
/*      */     }
/* 1008 */     if (paramMap.containsKey("sql_d001")) {
/* 1009 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d001")));
/*      */     }
/* 1011 */     if (paramMap.containsKey("d002")) {
/* 1012 */       stringBuffer.append(" and d002 = ").append(StringUtil.vString(paramMap.get("d002")));
/*      */     }
/* 1014 */     if (paramMap.containsKey("sql_d002")) {
/* 1015 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d002")));
/*      */     }
/* 1017 */     if (paramMap.containsKey("d003")) {
/* 1018 */       stringBuffer.append(" and d003 = ").append(StringUtil.vString(paramMap.get("d003")));
/*      */     }
/* 1020 */     if (paramMap.containsKey("sql_d003")) {
/* 1021 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d003")));
/*      */     }
/* 1023 */     if (paramMap.containsKey("d004")) {
/* 1024 */       stringBuffer.append(" and d004 = ").append(StringUtil.vString(paramMap.get("d004")));
/*      */     }
/* 1026 */     if (paramMap.containsKey("sql_d004")) {
/* 1027 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d004")));
/*      */     }
/* 1029 */     if (paramMap.containsKey("d005")) {
/* 1030 */       stringBuffer.append(" and d005 = ").append(StringUtil.vString(paramMap.get("d005")));
/*      */     }
/* 1032 */     if (paramMap.containsKey("sql_d005")) {
/* 1033 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d005")));
/*      */     }
/* 1035 */     if (paramMap.containsKey("d006")) {
/* 1036 */       stringBuffer.append(" and d006 = ").append(StringUtil.vString(paramMap.get("d006")));
/*      */     }
/* 1038 */     if (paramMap.containsKey("sql_d006")) {
/* 1039 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d006")));
/*      */     }
/* 1041 */     if (paramMap.containsKey("d007")) {
/* 1042 */       stringBuffer.append(" and d007 = ").append(StringUtil.vString(paramMap.get("d007")));
/*      */     }
/* 1044 */     if (paramMap.containsKey("sql_d007")) {
/* 1045 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d007")));
/*      */     }
/* 1047 */     if (paramMap.containsKey("d008")) {
/* 1048 */       stringBuffer.append(" and d008 = ").append(StringUtil.vString(paramMap.get("d008")));
/*      */     }
/* 1050 */     if (paramMap.containsKey("sql_d008")) {
/* 1051 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d008")));
/*      */     }
/* 1053 */     if (paramMap.containsKey("d009")) {
/* 1054 */       stringBuffer.append(" and d009 = ").append(StringUtil.vString(paramMap.get("d009")));
/*      */     }
/* 1056 */     if (paramMap.containsKey("sql_d009")) {
/* 1057 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d009")));
/*      */     }
/* 1059 */     if (paramMap.containsKey("d010")) {
/* 1060 */       stringBuffer.append(" and d010 = ").append(StringUtil.vString(paramMap.get("d010")));
/*      */     }
/* 1062 */     if (paramMap.containsKey("sql_d010")) {
/* 1063 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d010")));
/*      */     }
/* 1065 */     if (paramMap.containsKey("d011")) {
/* 1066 */       stringBuffer.append(" and d011 = ").append(StringUtil.vString(paramMap.get("d011")));
/*      */     }
/* 1068 */     if (paramMap.containsKey("sql_d011")) {
/* 1069 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d011")));
/*      */     }
/* 1071 */     if (paramMap.containsKey("d012")) {
/* 1072 */       stringBuffer.append(" and d012 = ").append(StringUtil.vString(paramMap.get("d012")));
/*      */     }
/* 1074 */     if (paramMap.containsKey("sql_d012")) {
/* 1075 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d012")));
/*      */     }
/* 1077 */     if (paramMap.containsKey("d013")) {
/* 1078 */       stringBuffer.append(" and d013 = ").append(StringUtil.vString(paramMap.get("d013")));
/*      */     }
/* 1080 */     if (paramMap.containsKey("sql_d013")) {
/* 1081 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d013")));
/*      */     }
/* 1083 */     if (paramMap.containsKey("d014")) {
/* 1084 */       stringBuffer.append(" and d014 = ").append(StringUtil.vString(paramMap.get("d014")));
/*      */     }
/* 1086 */     if (paramMap.containsKey("sql_d014")) {
/* 1087 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d014")));
/*      */     }
/* 1089 */     if (paramMap.containsKey("d015")) {
/* 1090 */       stringBuffer.append(" and d015 = ").append(StringUtil.vString(paramMap.get("d015")));
/*      */     }
/* 1092 */     if (paramMap.containsKey("sql_d015")) {
/* 1093 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d015")));
/*      */     }
/* 1095 */     if (paramMap.containsKey("d016")) {
/* 1096 */       stringBuffer.append(" and d016 = ").append(StringUtil.vString(paramMap.get("d016")));
/*      */     }
/* 1098 */     if (paramMap.containsKey("sql_d016")) {
/* 1099 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d016")));
/*      */     }
/* 1101 */     if (paramMap.containsKey("d017")) {
/* 1102 */       stringBuffer.append(" and d017 = ").append(StringUtil.vString(paramMap.get("d017")));
/*      */     }
/* 1104 */     if (paramMap.containsKey("sql_d017")) {
/* 1105 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d017")));
/*      */     }
/* 1107 */     if (paramMap.containsKey("d018")) {
/* 1108 */       stringBuffer.append(" and d018 = ").append(StringUtil.vString(paramMap.get("d018")));
/*      */     }
/* 1110 */     if (paramMap.containsKey("sql_d018")) {
/* 1111 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d018")));
/*      */     }
/* 1113 */     if (paramMap.containsKey("d019")) {
/* 1114 */       stringBuffer.append(" and d019 = ").append(StringUtil.vString(paramMap.get("d019")));
/*      */     }
/* 1116 */     if (paramMap.containsKey("sql_d019")) {
/* 1117 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d019")));
/*      */     }
/* 1119 */     if (paramMap.containsKey("d020")) {
/* 1120 */       stringBuffer.append(" and d020 = ").append(StringUtil.vString(paramMap.get("d020")));
/*      */     }
/* 1122 */     if (paramMap.containsKey("sql_d020")) {
/* 1123 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d020")));
/*      */     }
/* 1125 */     if (paramMap.containsKey("d021")) {
/* 1126 */       stringBuffer.append(" and d021 = ").append(StringUtil.vString(paramMap.get("d021")));
/*      */     }
/* 1128 */     if (paramMap.containsKey("sql_d021")) {
/* 1129 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d021")));
/*      */     }
/* 1131 */     if (paramMap.containsKey("d022")) {
/* 1132 */       stringBuffer.append(" and d022 = ").append(StringUtil.vString(paramMap.get("d022")));
/*      */     }
/* 1134 */     if (paramMap.containsKey("sql_d022")) {
/* 1135 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d022")));
/*      */     }
/* 1137 */     if (paramMap.containsKey("d023")) {
/* 1138 */       stringBuffer.append(" and d023 = ").append(StringUtil.vString(paramMap.get("d023")));
/*      */     }
/* 1140 */     if (paramMap.containsKey("sql_d023")) {
/* 1141 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d023")));
/*      */     }
/* 1143 */     if (paramMap.containsKey("d024")) {
/* 1144 */       stringBuffer.append(" and d024 = ").append(StringUtil.vString(paramMap.get("d024")));
/*      */     }
/* 1146 */     if (paramMap.containsKey("sql_d024")) {
/* 1147 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d024")));
/*      */     }
/* 1149 */     if (paramMap.containsKey("d025")) {
/* 1150 */       stringBuffer.append(" and d025 = ").append(StringUtil.vString(paramMap.get("d025")));
/*      */     }
/* 1152 */     if (paramMap.containsKey("sql_d025")) {
/* 1153 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d025")));
/*      */     }
/* 1155 */     if (paramMap.containsKey("d026")) {
/* 1156 */       stringBuffer.append(" and d026 = ").append(StringUtil.vString(paramMap.get("d026")));
/*      */     }
/* 1158 */     if (paramMap.containsKey("sql_d026")) {
/* 1159 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d026")));
/*      */     }
/* 1161 */     if (paramMap.containsKey("d027")) {
/* 1162 */       stringBuffer.append(" and d027 = ").append(StringUtil.vString(paramMap.get("d027")));
/*      */     }
/* 1164 */     if (paramMap.containsKey("sql_d027")) {
/* 1165 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d027")));
/*      */     }
/* 1167 */     if (paramMap.containsKey("d028")) {
/* 1168 */       stringBuffer.append(" and d028 = ").append(StringUtil.vString(paramMap.get("d028")));
/*      */     }
/* 1170 */     if (paramMap.containsKey("sql_d028")) {
/* 1171 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d028")));
/*      */     }
/* 1173 */     if (paramMap.containsKey("d029")) {
/* 1174 */       stringBuffer.append(" and d029 = ").append(StringUtil.vString(paramMap.get("d029")));
/*      */     }
/* 1176 */     if (paramMap.containsKey("sql_d029")) {
/* 1177 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d029")));
/*      */     }
/* 1179 */     if (paramMap.containsKey("d030")) {
/* 1180 */       stringBuffer.append(" and d030 = ").append(StringUtil.vString(paramMap.get("d030")));
/*      */     }
/* 1182 */     if (paramMap.containsKey("sql_d030")) {
/* 1183 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d030")));
/*      */     }
/* 1185 */     if (paramMap.containsKey("d031")) {
/* 1186 */       stringBuffer.append(" and d031 = ").append(StringUtil.vString(paramMap.get("d031")));
/*      */     }
/* 1188 */     if (paramMap.containsKey("sql_d031")) {
/* 1189 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_d031")));
/*      */     }
/* 1191 */     if (paramMap.containsKey("w001")) {
/* 1192 */       stringBuffer.append(" and w001 = ").append(StringUtil.vString(paramMap.get("w001")));
/*      */     }
/* 1194 */     if (paramMap.containsKey("sql_w001")) {
/* 1195 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w001")));
/*      */     }
/* 1197 */     if (paramMap.containsKey("w002")) {
/* 1198 */       stringBuffer.append(" and w002 = ").append(StringUtil.vString(paramMap.get("w002")));
/*      */     }
/* 1200 */     if (paramMap.containsKey("sql_w002")) {
/* 1201 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w002")));
/*      */     }
/* 1203 */     if (paramMap.containsKey("w003")) {
/* 1204 */       stringBuffer.append(" and w003 = ").append(StringUtil.vString(paramMap.get("w003")));
/*      */     }
/* 1206 */     if (paramMap.containsKey("sql_w003")) {
/* 1207 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w003")));
/*      */     }
/* 1209 */     if (paramMap.containsKey("w004")) {
/* 1210 */       stringBuffer.append(" and w004 = ").append(StringUtil.vString(paramMap.get("w004")));
/*      */     }
/* 1212 */     if (paramMap.containsKey("sql_w004")) {
/* 1213 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w004")));
/*      */     }
/* 1215 */     if (paramMap.containsKey("w005")) {
/* 1216 */       stringBuffer.append(" and w005 = ").append(StringUtil.vString(paramMap.get("w005")));
/*      */     }
/* 1218 */     if (paramMap.containsKey("sql_w005")) {
/* 1219 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w005")));
/*      */     }
/* 1221 */     if (paramMap.containsKey("w006")) {
/* 1222 */       stringBuffer.append(" and w006 = ").append(StringUtil.vString(paramMap.get("w006")));
/*      */     }
/* 1224 */     if (paramMap.containsKey("sql_w006")) {
/* 1225 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w006")));
/*      */     }
/* 1227 */     if (paramMap.containsKey("w007")) {
/* 1228 */       stringBuffer.append(" and w007 = ").append(StringUtil.vString(paramMap.get("w007")));
/*      */     }
/* 1230 */     if (paramMap.containsKey("sql_w007")) {
/* 1231 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_w007")));
/*      */     }
/* 1233 */     if (paramMap.containsKey("field001")) {
/* 1234 */       stringBuffer.append(" and field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*      */     }
/* 1236 */     if (paramMap.containsKey("sql_field001")) {
/* 1237 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*      */     }
/* 1239 */     if (paramMap.containsKey("field003")) {
/* 1240 */       stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*      */     }
/* 1242 */     if (paramMap.containsKey("sql_field003")) {
/* 1243 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*      */     }
/* 1245 */     if (paramMap.containsKey("field004")) {
/* 1246 */       stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*      */     }
/* 1248 */     if (paramMap.containsKey("sql_field004")) {
/* 1249 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*      */     }
/* 1251 */     if (paramMap.containsKey("field005")) {
/* 1252 */       stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*      */     }
/* 1254 */     if (paramMap.containsKey("sql_field005")) {
/* 1255 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*      */     }
/* 1257 */     if (paramMap.containsKey("field002")) {
/* 1258 */       stringBuffer.append(" and field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*      */     }
/* 1260 */     if (paramMap.containsKey("sql_field002")) {
/* 1261 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*      */     }
/* 1263 */     this.rs.executeSql(stringBuffer.toString());
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/dao/HrmScheduleShiftsDetailDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */