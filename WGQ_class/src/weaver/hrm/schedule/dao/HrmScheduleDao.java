/*     */ package weaver.hrm.schedule.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.dateformat.TimeZoneVar;
/*     */ import weaver.framework.BaseConnection;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*     */ import weaver.hrm.attendance.domain.HrmPubHoliday;
/*     */ import weaver.hrm.attendance.manager.HrmAttProcSetManager;
/*     */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*     */ import weaver.hrm.common.database.dialect.DbDialectFactory;
/*     */ import weaver.hrm.common.database.dialect.DialectUtil;
/*     */ import weaver.hrm.report.domain.HrmReport;
/*     */ import weaver.hrm.schedule.HrmScheduleSign;
/*     */ import weaver.hrm.schedule.domain.HrmSchedule;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleDao
/*     */   extends BaseConnection
/*     */   implements BaseDao<HrmSchedule>
/*     */ {
/*     */   public Comparable insert(HrmSchedule paramHrmSchedule) {
/*  41 */     StringBuffer stringBuffer = (new StringBuffer("insert into HrmScheduleSign(userId,userType,signType,signDate,signTime,clientAddress,isInCom,timeZone) values(")).append(paramHrmSchedule.getUserId()).append(",'").append(paramHrmSchedule.getUserType()).append("','").append(paramHrmSchedule.getSignType()).append("','").append(paramHrmSchedule.getOldSignDate()).append("','").append(paramHrmSchedule.getSignTime()).append("','").append(paramHrmSchedule.getClientAddress()).append("','").append(paramHrmSchedule.getIsInCom()).append("','" + Util.null2String(TimeZoneVar.getTimeZone(), "") + "')");
/*  42 */     this.rs.executeSql(stringBuffer.toString());
/*  43 */     return Integer.valueOf(0);
/*     */   }
/*     */ 
/*     */   
/*     */   public void update(HrmSchedule paramHrmSchedule) {}
/*     */ 
/*     */   
/*     */   public List<HrmSchedule> find(Map<String, Comparable> paramMap) {
/*  51 */     return new ArrayList<>();
/*     */   }
/*     */   
/*     */   public HrmSchedule get(Comparable paramComparable) {
/*  55 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public void delete(Comparable paramComparable) {}
/*     */ 
/*     */   
/*     */   public String getImportSignType(User paramUser, Map<String, String> paramMap, HrmScheduleSign paramHrmScheduleSign) {
/*  63 */     StringBuffer stringBuffer = new StringBuffer(getScheduleImportSignSql(paramUser.getUID(), paramUser.getLogintype(), 1));
/*  64 */     String str1 = StringUtil.vString(paramMap.get("signStartTime"));
/*  65 */     String str2 = StringUtil.vString(paramMap.get("signEndTime"));
/*     */     
/*  67 */     boolean bool = this.rs.getDBType().toLowerCase().equalsIgnoreCase("oracle");
/*  68 */     if (StringUtil.isNotNull(str1) && 
/*  69 */       StringUtil.isNotNull(str2)) {
/*  70 */       stringBuffer.append(" and");
/*  71 */       if (bool) {
/*  72 */         stringBuffer.append(" (signdate||' '||signTime between '" + str1 + "' and '" + str2 + "')");
/*  73 */       } else if (DialectUtil.isMySql(this.rs.getDBType())) {
/*  74 */         stringBuffer.append(" (" + DbDialectFactory.get(this.rs.getDBType()).concatStr("signdate", new String[] { "' '", "signTime" }) + " between '" + str1 + "' and '" + str2 + "')");
/*     */       } else {
/*  76 */         stringBuffer.append(" (signdate+' '+signTime between '" + str1 + "' and '" + str2 + "')");
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  82 */     this.rs.executeSql(stringBuffer.toString());
/*  83 */     return this.rs.next() ? "2" : "1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSignType(User paramUser, String paramString, Map<String, String> paramMap) {
/*  94 */     String str1 = StringUtil.vString(paramMap.get("signStartTime"));
/*  95 */     String str2 = StringUtil.vString(paramMap.get("signEndTime"));
/*  96 */     String str3 = StringUtil.vString(paramMap.get("signType"));
/*  97 */     boolean bool = (str1.compareTo(str2) > 0) ? true : false;
/*  98 */     StringBuffer stringBuffer = new StringBuffer(getScheduleSignSql(paramUser.getUID(), paramUser.getLogintype(), paramString, 1, bool));
/*  99 */     if (StringUtil.isNotNull(str1)) {
/* 100 */       if (str3.equals("2")) {
/* 101 */         Calendar calendar = DateUtil.getCalendar(paramString + " " + str1 + ":00");
/* 102 */         if (DateUtil.getCalendar().after(calendar)) {
/* 103 */           stringBuffer.append(" and signTime >= '" + str1 + ":00'");
/*     */         }
/* 105 */       } else if (StringUtil.isNotNull(str2)) {
/* 106 */         stringBuffer.append(" and");
/* 107 */         if (str1.compareTo(str2) > 0) {
/* 108 */           stringBuffer.append(" (signTime between '" + str1 + ":00' and '23:59:59' or signTime between '00:00:00' and '" + str2 + ":00')");
/*     */         } else {
/* 110 */           stringBuffer.append(" signTime between '" + str1 + ":00' and '" + str2 + ":00'");
/*     */         } 
/*     */       } 
/*     */     }
/* 114 */     this.rs.executeSql(stringBuffer.toString());
/* 115 */     return this.rs.next() ? "2" : "1";
/*     */   }
/*     */   private String getScheduleImportSignSql(int paramInt1, String paramString, int paramInt2) {
/* 118 */     return "select 1 from HrmScheduleSign where userId=" + paramInt1 + " and userType='" + 
/* 119 */       StringUtil.vString(paramString, "1") + "' and isInCom='1' and signType='" + 
/* 120 */       paramInt2 + "'";
/*     */   }
/*     */   
/*     */   private String getScheduleSignSql(int paramInt1, String paramString1, String paramString2, int paramInt2, boolean paramBoolean) {
/* 124 */     return "select 1 from HrmScheduleSign where userId=" + paramInt1 + " and userType='" + 
/* 125 */       StringUtil.vString(paramString1, "1") + "'" + (paramBoolean ? "" : (" and signDate='" + paramString2 + "'")) + 
/* 126 */       " and isInCom='1' and signType='" + 
/* 127 */       paramInt2 + "'";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getSecScheduleSignSql(int paramInt, String paramString1, String paramString2) {
/* 134 */     StringBuffer stringBuffer = (new StringBuffer("where userId=")).append(paramInt).append(" and userType='").append(StringUtil.vString(paramString1, "1")).append("' and signDate='").append(paramString2).append("' and isInCom='1'");
/*     */     
/* 136 */     return "select (case when (select count(*) from HrmScheduleSign " + stringBuffer.toString() + " and signType='1' and signTime < '[signInTime]') > 0 then 1 else 0 end) as fSignIn,(case when (select count(*) from HrmScheduleSign " + 
/* 137 */       stringBuffer.toString() + " and signType='2' and signTime < '[signOutTime]') > 0 then 1 else 0 end) as fSignOut from HrmScheduleSign " + 
/* 138 */       stringBuffer
/* 139 */       .toString() + "group by signDate,userId";
/*     */   }
/*     */   
/*     */   public boolean insertSignAndReturnThisIsSecSign(HrmSchedule paramHrmSchedule, Map<String, Object> paramMap, boolean paramBoolean) {
/* 143 */     if (paramHrmSchedule == null || paramMap == null) return false; 
/* 144 */     Calendar calendar = (Calendar)paramMap.get("curDate");
/* 145 */     String str1 = "", str2 = StringUtil.vString(paramMap.get("$signType"));
/* 146 */     String str3 = StringUtil.vString(paramMap.get("$offDutyTimeAM"));
/* 147 */     String str4 = StringUtil.vString(paramMap.get("$onDutyTimePM"));
/* 148 */     String str5 = StringUtil.vString(paramMap.get("$signStartTime"));
/* 149 */     boolean bool = false; boolean bool1 = false;
/* 150 */     if (paramHrmSchedule.getSignType().equals("1")) {
/*     */ 
/*     */       
/* 153 */       StringBuffer stringBuffer = new StringBuffer(getScheduleSignSql(paramHrmSchedule.getUserId(), paramHrmSchedule.getUserType(), paramHrmSchedule.getSignDate(), 1, paramBoolean));
/* 154 */       str1 = getSecScheduleSignSql(paramHrmSchedule.getUserId(), paramHrmSchedule.getUserType(), paramHrmSchedule.getSignDate());
/* 155 */       if (paramBoolean) {
/* 156 */         stringBuffer.append(" and");
/* 157 */         if (paramHrmSchedule.getSignStartTime().compareTo(paramHrmSchedule.getSignEndTime()) > 0) {
/* 158 */           String str = DateUtil.addDate(paramHrmSchedule.getSignDate(), 1);
/* 159 */           stringBuffer.append(" ((signDate = '" + paramHrmSchedule.getSignDate() + "' and signTime between '" + paramHrmSchedule.getSignStartTime() + ":00' and '23:59:59') or (signDate = '" + str + "' and signTime between '00:00:00' and '" + paramHrmSchedule.getSignEndTime() + ":00'))");
/*     */         } else {
/* 161 */           stringBuffer.append(" signDate = '" + paramHrmSchedule.getSignDate() + "' and signTime between '" + paramHrmSchedule.getSignStartTime() + ":00' and '" + paramHrmSchedule.getSignEndTime() + ":00'");
/*     */         } 
/* 163 */       } else if (str2.equals("2") && str5.length() > 0 && 
/* 164 */         calendar.after(paramMap.get("compTime"))) {
/* 165 */         paramHrmSchedule.setOnDutyTimePM(str4 + ":00");
/* 166 */         paramHrmSchedule.setSignStartTime(str5 + ":00");
/* 167 */         bool = true;
/* 168 */         stringBuffer.append(" and signTime >= '" + str5 + ":00'");
/* 169 */         str1 = StringUtil.replace(str1, "[signInTime]", str3 + ":00");
/* 170 */         str1 = StringUtil.replace(str1, "[signOutTime]", str4 + ":00");
/*     */       } 
/*     */       
/* 173 */       this.rs.executeSql(stringBuffer.toString());
/* 174 */       bool1 = this.rs.next();
/* 175 */     } else if (paramHrmSchedule.getSignType().equals("2")) {
/*     */       
/* 177 */       StringBuffer stringBuffer = new StringBuffer(getScheduleSignSql(paramHrmSchedule.getUserId(), paramHrmSchedule.getUserType(), paramHrmSchedule.getSignDate(), 1, paramBoolean));
/* 178 */       str1 = getSecScheduleSignSql(paramHrmSchedule.getUserId(), paramHrmSchedule.getUserType(), paramHrmSchedule.getSignDate());
/* 179 */       if (paramBoolean) {
/* 180 */         stringBuffer.append(" and");
/* 181 */         if (paramHrmSchedule.getSignStartTime().compareTo(paramHrmSchedule.getSignEndTime()) > 0) {
/* 182 */           String str = DateUtil.addDate(paramHrmSchedule.getSignDate(), 1);
/* 183 */           stringBuffer.append(" ((signDate = '" + paramHrmSchedule.getSignDate() + "' and signTime between '" + paramHrmSchedule.getSignStartTime() + ":00' and '23:59:59') or (signDate = '" + str + "' and signTime between '00:00:00' and '" + paramHrmSchedule.getSignEndTime() + ":00'))");
/*     */         } else {
/* 185 */           stringBuffer.append(" signDate = '" + paramHrmSchedule.getSignDate() + "' and signTime between '" + paramHrmSchedule.getSignStartTime() + ":00' and '" + paramHrmSchedule.getSignEndTime() + ":00'");
/*     */         } 
/* 187 */       } else if (str2.equals("2") && str5.length() > 0 && 
/* 188 */         calendar.after(paramMap.get("compTime"))) {
/* 189 */         paramHrmSchedule.setOnDutyTimePM(str4 + ":00");
/* 190 */         paramHrmSchedule.setSignStartTime(str5 + ":00");
/* 191 */         bool = true;
/* 192 */         stringBuffer.append(" and signTime >= '" + str5 + ":00'");
/* 193 */         str1 = StringUtil.replace(str1, "[signInTime]", str3 + ":00");
/* 194 */         str1 = StringUtil.replace(str1, "[signOutTime]", str4 + ":00");
/*     */       } 
/*     */       
/* 197 */       this.rs.executeSql(stringBuffer.toString());
/*     */       
/* 199 */       if (!this.rs.next()) {
/* 200 */         bool1 = true;
/*     */       }
/*     */     } 
/* 203 */     insetBean(bool1, bool, str1, paramHrmSchedule);
/* 204 */     return paramBoolean ? false : bool;
/*     */   }
/*     */   
/*     */   private void insetBean(boolean paramBoolean1, boolean paramBoolean2, String paramString, HrmSchedule paramHrmSchedule) {
/* 208 */     if (paramBoolean1)
/*     */       return; 
/* 210 */     insert(paramHrmSchedule);
/* 211 */     if (paramBoolean2) {
/* 212 */       this.rs.executeSql(paramString);
/* 213 */       boolean bool1 = false, bool2 = false;
/* 214 */       if (this.rs.next()) {
/* 215 */         bool1 = (this.rs.getInt(1) == 1) ? true : false;
/* 216 */         bool2 = (this.rs.getInt(2) == 1) ? true : false;
/* 217 */         if (bool1 && !bool2) {
/* 218 */           String str1 = paramHrmSchedule.getSignType();
/* 219 */           String str2 = paramHrmSchedule.getSignTime();
/* 220 */           HrmSchedule hrmSchedule = paramHrmSchedule;
/* 221 */           hrmSchedule.setSignType("2");
/* 222 */           if (hrmSchedule.getSignTime().compareTo(paramHrmSchedule.getOnDutyTimePM()) >= 0) {
/* 223 */             hrmSchedule.setSignTime(paramHrmSchedule.getSignStartTime());
/*     */           }
/* 225 */           insert(hrmSchedule);
/*     */           
/* 227 */           paramHrmSchedule.setSignType(str1);
/* 228 */           paramHrmSchedule.setSignTime(str2);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private HrmSchedule getScheduleBean(RecordSet paramRecordSet) {
/* 235 */     HrmSchedule hrmSchedule = new HrmSchedule();
/* 236 */     hrmSchedule.setId(Integer.valueOf(StringUtil.parseToInt(paramRecordSet.getString("id"))));
/* 237 */     hrmSchedule.setRelatedid(Integer.valueOf(StringUtil.parseToInt(paramRecordSet.getString("relatedid"))));
/* 238 */     hrmSchedule.setMonstarttime1(StringUtil.vString(paramRecordSet.getString("monstarttime1")));
/* 239 */     hrmSchedule.setMonendtime1(StringUtil.vString(paramRecordSet.getString("monendtime1")));
/* 240 */     hrmSchedule.setMonstarttime2(StringUtil.vString(paramRecordSet.getString("monstarttime2")));
/* 241 */     hrmSchedule.setMonendtime2(StringUtil.vString(paramRecordSet.getString("monendtime2")));
/* 242 */     hrmSchedule.setTuestarttime1(StringUtil.vString(paramRecordSet.getString("tuestarttime1")));
/* 243 */     hrmSchedule.setTueendtime1(StringUtil.vString(paramRecordSet.getString("tueendtime1")));
/* 244 */     hrmSchedule.setTuestarttime2(StringUtil.vString(paramRecordSet.getString("tuestarttime2")));
/* 245 */     hrmSchedule.setTueendtime2(StringUtil.vString(paramRecordSet.getString("tueendtime2")));
/* 246 */     hrmSchedule.setWedstarttime1(StringUtil.vString(paramRecordSet.getString("wedstarttime1")));
/* 247 */     hrmSchedule.setWedendtime1(StringUtil.vString(paramRecordSet.getString("wedendtime1")));
/* 248 */     hrmSchedule.setWedstarttime2(StringUtil.vString(paramRecordSet.getString("wedstarttime2")));
/* 249 */     hrmSchedule.setWedendtime2(StringUtil.vString(paramRecordSet.getString("wedendtime2")));
/* 250 */     hrmSchedule.setThustarttime1(StringUtil.vString(paramRecordSet.getString("thustarttime1")));
/* 251 */     hrmSchedule.setThuendtime1(StringUtil.vString(paramRecordSet.getString("thuendtime1")));
/* 252 */     hrmSchedule.setThustarttime2(StringUtil.vString(paramRecordSet.getString("thustarttime2")));
/* 253 */     hrmSchedule.setThuendtime2(StringUtil.vString(paramRecordSet.getString("thuendtime2")));
/* 254 */     hrmSchedule.setFristarttime1(StringUtil.vString(paramRecordSet.getString("fristarttime1")));
/* 255 */     hrmSchedule.setFriendtime1(StringUtil.vString(paramRecordSet.getString("friendtime1")));
/* 256 */     hrmSchedule.setFristarttime2(StringUtil.vString(paramRecordSet.getString("fristarttime2")));
/* 257 */     hrmSchedule.setFriendtime2(StringUtil.vString(paramRecordSet.getString("friendtime2")));
/* 258 */     hrmSchedule.setSatstarttime1(StringUtil.vString(paramRecordSet.getString("satstarttime1")));
/* 259 */     hrmSchedule.setSatendtime1(StringUtil.vString(paramRecordSet.getString("satendtime1")));
/* 260 */     hrmSchedule.setSatstarttime2(StringUtil.vString(paramRecordSet.getString("satstarttime2")));
/* 261 */     hrmSchedule.setSatendtime2(StringUtil.vString(paramRecordSet.getString("satendtime2")));
/* 262 */     hrmSchedule.setSunstarttime1(StringUtil.vString(paramRecordSet.getString("sunstarttime1")));
/* 263 */     hrmSchedule.setSunendtime1(StringUtil.vString(paramRecordSet.getString("sunendtime1")));
/* 264 */     hrmSchedule.setSunstarttime2(StringUtil.vString(paramRecordSet.getString("sunstarttime2")));
/* 265 */     hrmSchedule.setSunendtime2(StringUtil.vString(paramRecordSet.getString("sunendtime2")));
/* 266 */     hrmSchedule.setTotaltime(StringUtil.vString(paramRecordSet.getString("totaltime")));
/* 267 */     hrmSchedule.setScheduletype(StringUtil.vString(paramRecordSet.getString("scheduletype")));
/* 268 */     hrmSchedule.setValidedatefrom(StringUtil.vString(paramRecordSet.getString("validedatefrom")));
/* 269 */     hrmSchedule.setValidedateto(StringUtil.vString(paramRecordSet.getString("validedateto")));
/* 270 */     hrmSchedule.setSignType(StringUtil.vString(paramRecordSet.getString("sign_type")));
/* 271 */     hrmSchedule.setSignStartTime(StringUtil.vString(paramRecordSet.getString("sign_start_time")));
/* 272 */     return hrmSchedule;
/*     */   }
/*     */   
/*     */   public Map<String, HrmSchedule> findByDateList(int paramInt, List<String> paramList, Map<String, HrmPubHoliday> paramMap) {
/* 276 */     return findByDateList("", paramInt, paramList, paramMap);
/*     */   }
/*     */   
/*     */   public Map<String, HrmSchedule> findByDateList(String paramString, int paramInt, List<String> paramList, Map<String, HrmPubHoliday> paramMap) {
/* 280 */     return findByDateList(paramString, paramString, paramInt, paramList, paramMap);
/*     */   }
/*     */   
/*     */   public Map<String, HrmSchedule> findByDateList(String paramString1, String paramString2, int paramInt, List<String> paramList, Map<String, HrmPubHoliday> paramMap) {
/* 284 */     return findByDateList(paramString1, paramString2, paramInt, paramList, paramMap, 1, 2);
/*     */   }
/*     */   
/*     */   public Map<String, HrmSchedule> findByDateList(String paramString, int paramInt1, List<String> paramList, Map<String, HrmPubHoliday> paramMap, int paramInt2, int paramInt3) {
/* 288 */     return findByDateList(paramString, paramString, paramInt1, paramList, paramMap, paramInt2, paramInt3);
/*     */   }
/*     */   
/*     */   public Map<String, HrmSchedule> findByDateList(String paramString1, String paramString2, int paramInt1, List<String> paramList, Map<String, HrmPubHoliday> paramMap, int paramInt2, int paramInt3) {
/* 292 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 293 */     String str1 = "";
/* 294 */     if (!"".equals(paramString1)) {
/* 295 */       str1 = " and '" + paramString1 + "' between valideDateFrom and valideDateTo";
/*     */     }
/* 297 */     if (!"".equals(paramString2)) {
/* 298 */       str1 = " and '" + paramString2 + "' between valideDateFrom and valideDateTo";
/*     */     }
/* 300 */     String str2 = "select t.* from HrmSchedule t where (t.scheduleType='3' or (t.scheduleType='4' and t.relatedId = " + paramInt1 + ")) " + str1 + " order by t.scheduleType desc, t.relatedId asc, t.id desc";
/* 301 */     this.rs.executeSql(str2);
/* 302 */     HrmSchedule hrmSchedule1 = this.rs.next() ? getScheduleBean(this.rs) : null;
/* 303 */     String str3 = StringUtil.isNull(paramString1) ? ((hrmSchedule1 == null) ? "" : hrmSchedule1.getValidedatefrom()) : paramString1;
/* 304 */     String str4 = StringUtil.isNull(paramString2) ? ((hrmSchedule1 == null) ? "" : hrmSchedule1.getValidedateto()) : paramString2;
/* 305 */     if (StringUtil.isNull(new String[] { str3, str4 }) || str3.compareTo(str4) > 0) return (Map)hashMap;
/*     */     
/* 307 */     if (hrmSchedule1 == null)
/*     */     {
/* 309 */       return (Map)hashMap;
/*     */     }
/* 311 */     if (paramMap == null) paramMap = new HashMap<>();
/*     */ 
/*     */     
/* 314 */     HrmSchedule hrmSchedule2 = null;
/* 315 */     HrmPubHoliday hrmPubHoliday = null;
/* 316 */     for (String str : paramList) {
/*     */       
/* 318 */       if (str.compareTo(str3) < 0 || str.compareTo(str4) > 0 || hashMap.containsKey(str))
/* 319 */         continue;  hrmSchedule2 = new HrmSchedule();
/*     */       
/* 321 */       hrmSchedule2 = copyScheduleBean(hrmSchedule1);
/*     */       
/* 323 */       hrmSchedule2.setCurDate(str);
/* 324 */       hrmPubHoliday = paramMap.get(hrmSchedule2.getCurDate());
/*     */       
/* 326 */       if (paramInt2 == 0) {
/* 327 */         hrmSchedule2.setWeek(paramInt3 - 1);
/*     */       }
/* 329 */       else if (hrmPubHoliday != null && hrmPubHoliday.getChangetype().intValue() == 2) {
/* 330 */         hrmSchedule2.setWeek(hrmPubHoliday.getRelateweekday().intValue() - 1);
/*     */       } else {
/* 332 */         hrmSchedule2.setWeek(DateUtil.getWeek(hrmSchedule2.getCurDate()));
/*     */       } 
/*     */       
/* 335 */       if (paramInt2 == 0) {
/* 336 */         hashMap.put(hrmSchedule2.getCurDate(), hrmSchedule2); continue;
/*     */       } 
/* 338 */       if (hrmPubHoliday != null) {
/* 339 */         switch (hrmPubHoliday.getChangetype().intValue()) {
/*     */           case 1:
/* 341 */             hrmSchedule2.setOnDutyTimeAM("");
/* 342 */             hrmSchedule2.setOffDutyTimeAM("");
/* 343 */             hrmSchedule2.setOnDutyTimePM("");
/* 344 */             hrmSchedule2.setOffDutyTimePM("");
/* 345 */             hashMap.put(hrmSchedule2.getCurDate(), hrmSchedule2);
/*     */             continue;
/*     */           case 3:
/* 348 */             hrmSchedule2.setOnDutyTimeAM("");
/* 349 */             hrmSchedule2.setOffDutyTimeAM("");
/* 350 */             hrmSchedule2.setOnDutyTimePM("");
/* 351 */             hrmSchedule2.setOffDutyTimePM("");
/* 352 */             hashMap.put(hrmSchedule2.getCurDate(), hrmSchedule2);
/*     */             continue;
/*     */           case 2:
/* 355 */             hashMap.put(hrmSchedule2.getCurDate(), hrmSchedule2); continue;
/*     */         } 
/*     */         continue;
/*     */       } 
/* 359 */       hashMap.put(hrmSchedule2.getCurDate(), hrmSchedule2);
/*     */     } 
/*     */ 
/*     */     
/* 363 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getSchedulePersons(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/* 367 */     StringBuffer stringBuffer = (new StringBuffer("select t.id,t.lastName,t.departmentId,t2.departmentname,t.countryid,t.subCompanyId1,t.status,t.workcode")).append(" from HrmResource t left join HrmDepartment t2 on t.departmentid = t2.id where 1=1");
/* 368 */     if (StringUtil.isNotNull(paramString2) && !paramString2.equals("8") && !paramString2.equals("9")) {
/* 369 */       stringBuffer.append(" and t.status=").append(paramString2);
/* 370 */     } else if (paramString2.equals("8") || paramString2.equals("")) {
/* 371 */       stringBuffer.append(" and t.status in (0,1,2,3)");
/*     */     } 
/* 373 */     if (StringUtil.isNotNull(paramString1)) {
/* 374 */       stringBuffer.append(" and t.id in (").append(paramString1).append(")");
/*     */     }
/* 376 */     if (paramInt1 > 0) {
/* 377 */       stringBuffer.append(" and t.subCompanyId1=").append(paramInt1);
/*     */     }
/* 379 */     if (paramInt2 > 0) {
/* 380 */       stringBuffer.append(" and t.departmentId=").append(paramInt2);
/*     */     }
/*     */     
/* 383 */     stringBuffer.append(" order by t.subCompanyId1 asc,t.departmentId asc,t.id asc");
/* 384 */     this.rs.executeSql(stringBuffer.toString());
/*     */     
/* 386 */     byte b = 0;
/* 387 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 388 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 389 */     String str = "";
/* 390 */     while (this.rs.next()) {
/* 391 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 392 */       hashMap.put("departmentName", StringUtil.vString(this.rs.getString("departmentname")));
/* 393 */       hashMap.put("resourceName", StringUtil.vString(this.rs.getString("lastName")));
/* 394 */       hashMap.put("resourceId", StringUtil.vString(this.rs.getString("id")));
/* 395 */       hashMap.put("subCompanyId", StringUtil.vString(this.rs.getString("subCompanyId1")));
/* 396 */       hashMap.put("departmentId", StringUtil.vString(this.rs.getString("departmentId")));
/* 397 */       hashMap.put("countryId", StringUtil.vString(this.rs.getString("countryid")));
/* 398 */       hashMap.put("pStatus", StringUtil.vString(this.rs.getString("status")));
/* 399 */       hashMap.put("workcode", StringUtil.vString(this.rs.getString("workcode")));
/* 400 */       arrayList.add(hashMap);
/* 401 */       hashMap1.put(this.rs.getString("id"), String.valueOf(b++));
/* 402 */       if (str.length() == 0) str = StringUtil.vString(this.rs.getString("subCompanyId1")); 
/*     */     } 
/* 404 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 405 */     hashMap2.put("indexMap", hashMap1);
/* 406 */     hashMap2.put("personList", arrayList);
/* 407 */     hashMap2.put("subCompanyId", str);
/* 408 */     return (Map)hashMap2;
/*     */   }
/*     */   
/*     */   public Map<String, Map<String, List<HrmReport>>> getAttFlowData(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 412 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 413 */     hashMap.put("leaveMap", getLeaveMap(paramString1, paramString2, paramInt1, paramInt2, paramString3));
/* 414 */     hashMap.put("evectionMap", getEvevationMap(paramString1, paramString2, paramInt1, paramInt2, paramString3));
/* 415 */     hashMap.put("outMap", getOutMap(paramString1, paramString2, paramInt1, paramInt2, paramString3));
/* 416 */     hashMap.put("otherMap", getOtherMap(paramString1, paramString2, paramInt1, paramInt2, paramString3));
/*     */     
/* 418 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public Map<String, List<HrmReport>> getLeaveMap(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 422 */     return getAttFlowData(0, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */   }
/*     */   
/*     */   private String getAttFlowSql(int paramInt1, String paramString1, String paramString2, int paramInt2, int paramInt3, String paramString3, String paramString4, String paramString5) {
/* 426 */     List list = (paramInt1 == 0) ? (new HrmLeaveTypeColorManager()).find("[map]subcompanyid:0;field002:1") : new ArrayList();
/* 427 */     String str1 = "";
/* 428 */     for (HrmLeaveTypeColor hrmLeaveTypeColor : list) {
/* 429 */       str1 = str1 + ((str1.length() == 0) ? "" : ",") + hrmLeaveTypeColor.getField004().intValue();
/*     */     }
/* 431 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 432 */     if (paramInt1 == 0 && str1.length() > 0) { hashMap.put("newLeaveType", " and t.newLeaveType in (" + str1 + ")"); }
/* 433 */     else if (paramInt1 == 3) { hashMap.put("oType", " and t.otype in ('0', '1')"); }
/* 434 */      String str2 = " and (t." + paramString4 + " between '" + paramString1 + "' and '" + paramString2 + "' or t." + paramString5 + " between '" + paramString1 + "' and '" + paramString2 + "' or '" + paramString1 + "' between t." + paramString4 + " and t." + paramString5 + " or '" + paramString2 + "' between t." + paramString4 + " and t." + paramString5 + ")";
/* 435 */     if (StringUtil.isNotNull(new String[] { paramString1, paramString2 })) {
/* 436 */       hashMap.put("fromDate", str2);
/* 437 */     } else if (StringUtil.isNotNull(paramString1)) {
/* 438 */       hashMap.put("fromDate", " and (t." + paramString4 + " >= '" + paramString1 + "' or '" + paramString1 + "' between t." + paramString4 + " and t." + paramString5 + ")");
/* 439 */     } else if (StringUtil.isNotNull(paramString2)) {
/* 440 */       hashMap.put("toDate", " and (t." + paramString5 + " <= '" + paramString2 + "' or '" + paramString2 + "' between t." + paramString4 + " and t." + paramString5 + ")");
/* 441 */     }  if (paramInt3 > 0)
/* 442 */       hashMap.put("departmentId", " and t.resourceId in  (select id from hrmresource where departmentid = " + paramInt3 + ")"); 
/* 443 */     if (paramInt2 > 0)
/* 444 */       hashMap.put("subCompanyId", " and t.resourceId in  (select id from hrmresource where subcompanyid1 = " + paramInt2 + ")"); 
/* 445 */     if (StringUtil.isNotNull(paramString3))
/* 446 */       hashMap.put("resourceId", " and t.resourceId in (" + paramString3 + ")"); 
/* 447 */     return (new HrmAttProcSetManager()).getSQLByField006(paramInt1, hashMap);
/*     */   }
/*     */   
/*     */   public Map<String, List<HrmReport>> getEvevationMap(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 451 */     return getAttFlowData(1, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */   }
/*     */   
/*     */   public Map<String, List<HrmReport>> getOutMap(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 455 */     return getAttFlowData(2, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */   }
/*     */   
/*     */   public Map<String, List<HrmReport>> getOverTimeMap(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 459 */     return getAttFlowData(3, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */   }
/*     */   
/*     */   public Map<String, List<HrmReport>> getOtherMap(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 463 */     return getAttFlowData(4, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */   }
/*     */   
/*     */   public Map<String, List<HrmReport>> getScheduleMap(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/* 467 */     return getAttFlowData(5, paramString1, paramString2, paramInt1, paramInt2, paramString3);
/*     */   }
/*     */   
/*     */   private Map<String, List<HrmReport>> getAttFlowData(int paramInt1, String paramString1, String paramString2, int paramInt2, int paramInt3, String paramString3) {
/* 471 */     String str1 = "fromDate", str2 = "fromTime", str3 = "toDate", str4 = "toTime";
/* 472 */     if (paramInt1 == 3) {
/* 473 */       str1 = "fromdate";
/* 474 */       str2 = "fromtime";
/* 475 */       str3 = "tilldate";
/* 476 */       str4 = "tilltime";
/*     */     } 
/* 478 */     String str5 = getAttFlowSql(paramInt1, paramString1, paramString2, paramInt2, paramInt3, paramString3, str1, str3);
/* 479 */     if (str5 != null && !"".equals(str5.trim())) {
/* 480 */       this.rs.executeSql(str5);
/* 481 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 482 */       HrmReport hrmReport = null;
/* 483 */       while (this.rs.next()) {
/* 484 */         hrmReport = new HrmReport();
/* 485 */         hrmReport.setResId(StringUtil.vString(this.rs.getString("resourceId")));
/* 486 */         hrmReport.setFromDate(StringUtil.vString(this.rs.getString(str1)));
/* 487 */         hrmReport.setFromTime(StringUtil.vString(this.rs.getString(str2)));
/* 488 */         hrmReport.setToDate(StringUtil.vString(this.rs.getString(str3)));
/* 489 */         hrmReport.setToTime(StringUtil.vString(this.rs.getString(str4)));
/* 490 */         if (paramInt1 == 3) { hrmReport.setOtype(this.rs.getInt("otype")); }
/* 491 */         else if (paramInt1 == 0) { hrmReport.setNewLeaveType(this.rs.getInt("newLeaveType")); }
/*     */         
/* 493 */         List<HrmReport> list = null;
/* 494 */         if (hashMap.containsKey(hrmReport.getResId())) list = (List)hashMap.get(hrmReport.getResId()); 
/* 495 */         if (list == null) list = new ArrayList(); 
/* 496 */         list.add(hrmReport);
/*     */         
/* 498 */         hashMap.put(hrmReport.getResId(), list);
/*     */       } 
/* 500 */       return (Map)hashMap;
/*     */     } 
/* 502 */     return new HashMap<>();
/*     */   }
/*     */   
/*     */   public HrmSchedule copyScheduleBean(HrmSchedule paramHrmSchedule) {
/* 506 */     HrmSchedule hrmSchedule = new HrmSchedule();
/* 507 */     if (paramHrmSchedule == null) return hrmSchedule; 
/* 508 */     hrmSchedule.setId(paramHrmSchedule.getId());
/* 509 */     hrmSchedule.setRelatedid(paramHrmSchedule.getRelatedid());
/* 510 */     hrmSchedule.setMonstarttime1(paramHrmSchedule.getMonstarttime1());
/* 511 */     hrmSchedule.setMonendtime1(paramHrmSchedule.getMonendtime1());
/* 512 */     hrmSchedule.setMonstarttime2(paramHrmSchedule.getMonstarttime2());
/* 513 */     hrmSchedule.setMonendtime2(paramHrmSchedule.getMonendtime2());
/* 514 */     hrmSchedule.setTuestarttime1(paramHrmSchedule.getTuestarttime1());
/* 515 */     hrmSchedule.setTueendtime1(paramHrmSchedule.getTueendtime1());
/* 516 */     hrmSchedule.setTuestarttime2(paramHrmSchedule.getTuestarttime2());
/* 517 */     hrmSchedule.setTueendtime2(paramHrmSchedule.getTueendtime2());
/* 518 */     hrmSchedule.setWedstarttime1(paramHrmSchedule.getWedstarttime1());
/* 519 */     hrmSchedule.setWedendtime1(paramHrmSchedule.getWedendtime1());
/* 520 */     hrmSchedule.setWedstarttime2(paramHrmSchedule.getWedstarttime2());
/* 521 */     hrmSchedule.setWedendtime2(paramHrmSchedule.getWedendtime2());
/* 522 */     hrmSchedule.setThustarttime1(paramHrmSchedule.getThustarttime1());
/* 523 */     hrmSchedule.setThuendtime1(paramHrmSchedule.getThuendtime1());
/* 524 */     hrmSchedule.setThustarttime2(paramHrmSchedule.getThustarttime2());
/* 525 */     hrmSchedule.setThuendtime2(paramHrmSchedule.getThuendtime2());
/* 526 */     hrmSchedule.setFristarttime1(paramHrmSchedule.getFristarttime1());
/* 527 */     hrmSchedule.setFriendtime1(paramHrmSchedule.getFriendtime1());
/* 528 */     hrmSchedule.setFristarttime2(paramHrmSchedule.getFristarttime2());
/* 529 */     hrmSchedule.setFriendtime2(paramHrmSchedule.getFriendtime2());
/* 530 */     hrmSchedule.setSatstarttime1(paramHrmSchedule.getSatstarttime1());
/* 531 */     hrmSchedule.setSatendtime1(paramHrmSchedule.getSatendtime1());
/* 532 */     hrmSchedule.setSatstarttime2(paramHrmSchedule.getSatstarttime2());
/* 533 */     hrmSchedule.setSatendtime2(paramHrmSchedule.getSatendtime2());
/* 534 */     hrmSchedule.setSunstarttime1(paramHrmSchedule.getSunstarttime1());
/* 535 */     hrmSchedule.setSunendtime1(paramHrmSchedule.getSunendtime1());
/* 536 */     hrmSchedule.setSunstarttime2(paramHrmSchedule.getSunstarttime2());
/* 537 */     hrmSchedule.setSunendtime2(paramHrmSchedule.getSunendtime2());
/* 538 */     hrmSchedule.setTotaltime(paramHrmSchedule.getTotaltime());
/* 539 */     hrmSchedule.setScheduletype(paramHrmSchedule.getScheduletype());
/* 540 */     hrmSchedule.setValidedatefrom(paramHrmSchedule.getValidedatefrom());
/* 541 */     hrmSchedule.setValidedateto(paramHrmSchedule.getValidedateto());
/* 542 */     hrmSchedule.setSignType(paramHrmSchedule.getSignType());
/* 543 */     hrmSchedule.setSignStartTime(paramHrmSchedule.getSignStartTime());
/* 544 */     return hrmSchedule;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/dao/HrmScheduleDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */