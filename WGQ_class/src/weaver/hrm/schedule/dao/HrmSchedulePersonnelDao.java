/*     */ package weaver.hrm.schedule.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseConnection;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.schedule.domain.HrmSchedulePerson;
/*     */ import weaver.hrm.schedule.domain.HrmSchedulePersonnel;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSchedulePersonnelDao
/*     */   extends BaseConnection
/*     */   implements BaseDao<HrmSchedulePersonnel>
/*     */ {
/*     */   public Comparable insert(HrmSchedulePersonnel paramHrmSchedulePersonnel) {
/*  25 */     if (paramHrmSchedulePersonnel == null) return Integer.valueOf(-1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  31 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_schedule_personnel (delflag,creater,create_time,last_modifier,last_modification_time,sn,").append(" field001,field002,field003,field004,field005,field006,field007 )").append(" values(" + paramHrmSchedulePersonnel.getDelflag() + "," + paramHrmSchedulePersonnel.getCreater() + ",'" + paramHrmSchedulePersonnel.getCreateTime() + "'," + paramHrmSchedulePersonnel.getLastModifier() + ",").append(" '" + paramHrmSchedulePersonnel.getLastModificationTime() + "'," + paramHrmSchedulePersonnel.getSn() + "," + paramHrmSchedulePersonnel.getField001() + ",'" + paramHrmSchedulePersonnel.getRealField002() + "',").append(" " + paramHrmSchedulePersonnel.getField003() + "," + paramHrmSchedulePersonnel.getField004() + "," + paramHrmSchedulePersonnel.getField005() + "," + paramHrmSchedulePersonnel.getField006() + ",'" + paramHrmSchedulePersonnel.getField007() + "' )");
/*  32 */     this.rs.executeSql(stringBuffer.toString());
/*  33 */     stringBuffer.setLength(0);
/*     */     
/*  35 */     stringBuffer.append("select id from hrm_schedule_personnel where delflag = ").append(paramHrmSchedulePersonnel.getDelflag())
/*  36 */       .append(" and field001 = ").append(paramHrmSchedulePersonnel.getField001())
/*  37 */       .append(" and field002 = '").append(paramHrmSchedulePersonnel.getRealField002()).append("'")
/*  38 */       .append(" and field003 = ").append(paramHrmSchedulePersonnel.getField003())
/*  39 */       .append(" and field004 = ").append(paramHrmSchedulePersonnel.getField004())
/*  40 */       .append(" and field005 = ").append(paramHrmSchedulePersonnel.getField005())
/*  41 */       .append(" and field006 = ").append(paramHrmSchedulePersonnel.getField006())
/*  42 */       .append(" and field007 = ").append(paramHrmSchedulePersonnel.getField007())
/*  43 */       .append(" order by id desc");
/*  44 */     this.rs.executeSql(stringBuffer.toString());
/*  45 */     insertVal(Long.valueOf(this.rs.next() ? StringUtil.parseToLong(this.rs.getString(1)) : -1L), paramHrmSchedulePersonnel.getRealField002());
/*  46 */     return Integer.valueOf(0);
/*     */   }
/*     */   
/*     */   public void update(HrmSchedulePersonnel paramHrmSchedulePersonnel) {
/*  50 */     if (paramHrmSchedulePersonnel == null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  57 */     StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_schedule_personnel set").append(" delflag = " + paramHrmSchedulePersonnel.getDelflag() + ",creater = " + paramHrmSchedulePersonnel.getCreater() + ",create_time = '" + paramHrmSchedulePersonnel.getCreateTime() + "',").append(" last_modifier = " + paramHrmSchedulePersonnel.getLastModifier() + ",last_modification_time = '" + paramHrmSchedulePersonnel.getLastModificationTime() + "',sn = " + paramHrmSchedulePersonnel.getSn() + ",").append(" field001 = " + paramHrmSchedulePersonnel.getField001() + ",field002 = '" + paramHrmSchedulePersonnel.getRealField002() + "',field003 = " + paramHrmSchedulePersonnel.getField003() + ",").append(" field004 = " + paramHrmSchedulePersonnel.getField004() + ",field005 = " + paramHrmSchedulePersonnel.getField005() + ",field006 = " + paramHrmSchedulePersonnel.getField006() + ",field007 = '" + paramHrmSchedulePersonnel.getField007() + "'").append(" where id = " + paramHrmSchedulePersonnel.getId() + "");
/*  58 */     this.rs.executeSql(stringBuffer.toString());
/*  59 */     deleteVal(paramHrmSchedulePersonnel.getId());
/*  60 */     insertVal(paramHrmSchedulePersonnel.getId(), paramHrmSchedulePersonnel.getRealField002());
/*     */   }
/*     */   
/*     */   private void insertVal(Long paramLong, String paramString) {
/*  64 */     if (paramLong.longValue() <= 0L)
/*  65 */       return;  String[] arrayOfString = paramString.split(";");
/*  66 */     StringBuffer stringBuffer = new StringBuffer();
/*  67 */     for (String str : arrayOfString) stringBuffer.append((stringBuffer.length() == 0) ? "" : "union all").append(" select 0, ").append(paramLong).append(", ").append(str).append(isOracle() ? " from dual" : ""); 
/*  68 */     this.rs.executeSql("insert into hrm_schedule_personnel_val (delflag,field001,field002 )" + stringBuffer.toString());
/*     */   }
/*     */   
/*     */   private void deleteVal(Comparable paramComparable) {
/*  72 */     this.rs.executeSql("delete from hrm_schedule_personnel_val where field001 in (" + paramComparable + ")");
/*     */   }
/*     */   
/*     */   public List<HrmSchedulePersonnel> find(Map<String, Comparable> paramMap) {
/*  76 */     ArrayList<HrmSchedulePersonnel> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  81 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.delflag,t.creater,t.create_time,t.last_modifier,t.last_modification_time,t.sn,").append(" t.field001,t.field002,t.field003,t.field004,t.field005,t.field005,t.field006,t.field007").append(" from hrm_schedule_personnel t").append(" where t.delflag = 0");
/*  82 */     if (paramMap != null) {
/*  83 */       if (paramMap.containsKey("id")) {
/*  84 */         stringBuffer.append(" and t.id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/*  86 */       if (paramMap.containsKey("begin_id")) {
/*  87 */         stringBuffer.append(" and t.id >= ").append(StringUtil.vString(paramMap.get("begin_id")));
/*     */       }
/*  89 */       if (paramMap.containsKey("end_id")) {
/*  90 */         stringBuffer.append(" and t.id < ").append(StringUtil.vString(paramMap.get("end_id")));
/*     */       }
/*  92 */       if (paramMap.containsKey("sql_id")) {
/*  93 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/*  95 */       if (paramMap.containsKey("delflag")) {
/*  96 */         stringBuffer.append(" and t.delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/*  98 */       if (paramMap.containsKey("begin_delflag")) {
/*  99 */         stringBuffer.append(" and t.delflag >= ").append(StringUtil.vString(paramMap.get("begin_delflag")));
/*     */       }
/* 101 */       if (paramMap.containsKey("end_delflag")) {
/* 102 */         stringBuffer.append(" and t.delflag < ").append(StringUtil.vString(paramMap.get("end_delflag")));
/*     */       }
/* 104 */       if (paramMap.containsKey("sql_delflag")) {
/* 105 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 107 */       if (paramMap.containsKey("creater")) {
/* 108 */         stringBuffer.append(" and t.creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */       }
/* 110 */       if (paramMap.containsKey("begin_creater")) {
/* 111 */         stringBuffer.append(" and t.creater >= ").append(StringUtil.vString(paramMap.get("begin_creater")));
/*     */       }
/* 113 */       if (paramMap.containsKey("end_creater")) {
/* 114 */         stringBuffer.append(" and t.creater < ").append(StringUtil.vString(paramMap.get("end_creater")));
/*     */       }
/* 116 */       if (paramMap.containsKey("sql_creater")) {
/* 117 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */       }
/* 119 */       if (paramMap.containsKey("lastModifier")) {
/* 120 */         stringBuffer.append(" and t.last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */       }
/* 122 */       if (paramMap.containsKey("begin_lastModifier")) {
/* 123 */         stringBuffer.append(" and t.last_modifier >= ").append(StringUtil.vString(paramMap.get("begin_lastModifier")));
/*     */       }
/* 125 */       if (paramMap.containsKey("end_lastModifier")) {
/* 126 */         stringBuffer.append(" and t.last_modifier < ").append(StringUtil.vString(paramMap.get("end_lastModifier")));
/*     */       }
/* 128 */       if (paramMap.containsKey("sql_lastModifier")) {
/* 129 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */       }
/* 131 */       if (paramMap.containsKey("sn")) {
/* 132 */         stringBuffer.append(" and t.sn = ").append(StringUtil.vString(paramMap.get("sn")));
/*     */       }
/* 134 */       if (paramMap.containsKey("begin_sn")) {
/* 135 */         stringBuffer.append(" and t.sn >= ").append(StringUtil.vString(paramMap.get("begin_sn")));
/*     */       }
/* 137 */       if (paramMap.containsKey("end_sn")) {
/* 138 */         stringBuffer.append(" and t.sn < ").append(StringUtil.vString(paramMap.get("end_sn")));
/*     */       }
/* 140 */       if (paramMap.containsKey("sql_sn")) {
/* 141 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_sn")));
/*     */       }
/* 143 */       if (paramMap.containsKey("field001")) {
/* 144 */         stringBuffer.append(" and t.field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*     */       }
/* 146 */       if (paramMap.containsKey("begin_field001")) {
/* 147 */         stringBuffer.append(" and t.field001 >= ").append(StringUtil.vString(paramMap.get("begin_field001")));
/*     */       }
/* 149 */       if (paramMap.containsKey("end_field001")) {
/* 150 */         stringBuffer.append(" and t.field001 < ").append(StringUtil.vString(paramMap.get("end_field001")));
/*     */       }
/* 152 */       if (paramMap.containsKey("sql_field001")) {
/* 153 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 155 */       if (paramMap.containsKey("field003")) {
/* 156 */         stringBuffer.append(" and t.field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */       }
/* 158 */       if (paramMap.containsKey("begin_field003")) {
/* 159 */         stringBuffer.append(" and t.field003 >= ").append(StringUtil.vString(paramMap.get("begin_field003")));
/*     */       }
/* 161 */       if (paramMap.containsKey("end_field003")) {
/* 162 */         stringBuffer.append(" and t.field003 < ").append(StringUtil.vString(paramMap.get("end_field003")));
/*     */       }
/* 164 */       if (paramMap.containsKey("sql_field003")) {
/* 165 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 167 */       if (paramMap.containsKey("field004")) {
/* 168 */         stringBuffer.append(" and t.field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 170 */       if (paramMap.containsKey("begin_field004")) {
/* 171 */         stringBuffer.append(" and t.field004 >= ").append(StringUtil.vString(paramMap.get("begin_field004")));
/*     */       }
/* 173 */       if (paramMap.containsKey("end_field004")) {
/* 174 */         stringBuffer.append(" and t.field004 < ").append(StringUtil.vString(paramMap.get("end_field004")));
/*     */       }
/* 176 */       if (paramMap.containsKey("sql_field004")) {
/* 177 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 179 */       if (paramMap.containsKey("field005")) {
/* 180 */         stringBuffer.append(" and t.field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */       }
/* 182 */       if (paramMap.containsKey("begin_field005")) {
/* 183 */         stringBuffer.append(" and t.field005 >= ").append(StringUtil.vString(paramMap.get("begin_field005")));
/*     */       }
/* 185 */       if (paramMap.containsKey("end_field005")) {
/* 186 */         stringBuffer.append(" and t.field005 < ").append(StringUtil.vString(paramMap.get("end_field005")));
/*     */       }
/* 188 */       if (paramMap.containsKey("sql_field005")) {
/* 189 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/* 191 */       if (paramMap.containsKey("paramField001Start") && paramMap.containsKey("paramField001End")) {
/* 192 */         stringBuffer.append("");
/*     */       }
/* 194 */       if (paramMap.containsKey("shiftsSet")) {
/* 195 */         stringBuffer.append("");
/*     */       }
/* 197 */       if (paramMap.containsKey("subcompany")) {
/* 198 */         stringBuffer.append("");
/*     */       }
/* 200 */       if (paramMap.containsKey("workcode")) {
/* 201 */         stringBuffer.append("");
/*     */       }
/* 203 */       if (paramMap.containsKey("lastname")) {
/* 204 */         stringBuffer.append("");
/*     */       }
/* 206 */       if (paramMap.containsKey("department")) {
/* 207 */         stringBuffer.append("");
/*     */       }
/* 209 */       if (paramMap.containsKey("jobtitle")) {
/* 210 */         stringBuffer.append("");
/*     */       }
/* 212 */       if (paramMap.containsKey("stype")) {
/* 213 */         stringBuffer.append("");
/*     */       }
/* 215 */       if (paramMap.containsKey("createTime")) {
/* 216 */         stringBuffer.append(" and t.create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */       }
/* 218 */       if (paramMap.containsKey("like_createTime")) {
/* 219 */         stringBuffer.append(" and t.create_time like '%").append(StringUtil.vString(paramMap.get("like_createTime"))).append("%'");
/*     */       }
/* 221 */       if (paramMap.containsKey("sql_createTime")) {
/* 222 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */       }
/* 224 */       if (paramMap.containsKey("lastModificationTime")) {
/* 225 */         stringBuffer.append(" and t.last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */       }
/* 227 */       if (paramMap.containsKey("like_lastModificationTime")) {
/* 228 */         stringBuffer.append(" and t.last_modification_time like '%").append(StringUtil.vString(paramMap.get("like_lastModificationTime"))).append("%'");
/*     */       }
/* 230 */       if (paramMap.containsKey("sql_lastModificationTime")) {
/* 231 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */       }
/* 233 */       if (paramMap.containsKey("field002")) {
/* 234 */         stringBuffer.append(" and t.field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*     */       }
/* 236 */       if (paramMap.containsKey("like_field002")) {
/* 237 */         stringBuffer.append(" and t.field002 like '%").append(StringUtil.vString(paramMap.get("like_field002"))).append("%'");
/*     */       }
/* 239 */       if (paramMap.containsKey("sql_field002")) {
/* 240 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 242 */       if (paramMap.containsKey("mfsql")) {
/* 243 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("mfsql")));
/*     */       }
/* 245 */       if (paramMap.containsKey("sqlorderby")) {
/* 246 */         stringBuffer.append(" order by " + StringUtil.vString(paramMap.get("sqlorderby")));
/*     */       } else {
/* 248 */         stringBuffer.append(" order by t.id ").append((StringUtil.vString(paramMap.get("sqlsortway")).length() > 0) ? StringUtil.vString(paramMap.get("sqlsortway")) : "desc");
/*     */       } 
/*     */     } 
/* 251 */     this.rs.executeSql(stringBuffer.toString());
/* 252 */     HrmSchedulePersonnel hrmSchedulePersonnel = null;
/* 253 */     while (this.rs.next()) {
/* 254 */       hrmSchedulePersonnel = new HrmSchedulePersonnel();
/* 255 */       hrmSchedulePersonnel.setId(Long.valueOf(StringUtil.parseToLong(this.rs.getString("id"))));
/* 256 */       hrmSchedulePersonnel.setDelflag(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("delflag"))));
/* 257 */       hrmSchedulePersonnel.setCreater(Long.valueOf(StringUtil.parseToLong(this.rs.getString("creater"))));
/* 258 */       hrmSchedulePersonnel.setCreateTime(StringUtil.vString(this.rs.getString("create_time")));
/* 259 */       hrmSchedulePersonnel.setLastModifier(Long.valueOf(StringUtil.parseToLong(this.rs.getString("last_modifier"))));
/* 260 */       hrmSchedulePersonnel.setLastModificationTime(StringUtil.vString(this.rs.getString("last_modification_time")));
/* 261 */       hrmSchedulePersonnel.setSn(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("sn"))));
/* 262 */       hrmSchedulePersonnel.setField001(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field001"))));
/* 263 */       hrmSchedulePersonnel.setField002(StringUtil.vString(this.rs.getString("field002")));
/* 264 */       hrmSchedulePersonnel.setField003(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field003"))));
/* 265 */       hrmSchedulePersonnel.setField004(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field004"))));
/* 266 */       hrmSchedulePersonnel.setField005(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field005"))));
/* 267 */       hrmSchedulePersonnel.setField006(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field006"))));
/* 268 */       hrmSchedulePersonnel.setField007(Util.null2String(this.rs.getString("field007")));
/* 269 */       arrayList.add(hrmSchedulePersonnel);
/*     */     } 
/* 271 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getSchedulePersonsSQL(Map<String, Comparable> paramMap) {
/* 275 */     StringBuffer stringBuffer1 = new StringBuffer("[appendSQL]");
/* 276 */     if (paramMap != null) {
/* 277 */       if (paramMap.containsKey("paramField001Start") && paramMap.containsKey("paramField001End")) {
/* 278 */         stringBuffer1.append(" and field003 between '").append(StringUtil.vString(paramMap.get("paramField001Start"))).append("' and '").append(StringUtil.vString(paramMap.get("paramField001End"))).append("'");
/*     */       }
/* 280 */       if (paramMap.containsKey("shiftsSet")) {
/* 281 */         stringBuffer1.append(" and field001 = '").append(StringUtil.vString(paramMap.get("shiftsSet"))).append("'");
/*     */       }
/*     */     } 
/* 284 */     String str1 = StringUtil.replace(stringBuffer1.toString(), "[appendSQL]", "select count(id) from hrm_schedule_set_detail where delflag = 0 and field002 = t.id");
/* 285 */     String str2 = StringUtil.replace(stringBuffer1.toString(), "[appendSQL]", "select field002 from hrm_schedule_set_detail where delflag = 0");
/* 286 */     String str3 = (stringBuffer1.length() == "[appendSQL]".length()) ? "" : StringUtil.replace(stringBuffer1.toString(), "[appendSQL]", "select field002 as id from hrm_schedule_set_detail where delflag = 0");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 294 */     StringBuffer stringBuffer2 = (new StringBuffer()).append(" select distinct t.id as tId, t.* from (").append(" select t.id,t.workcode,t.lastname,t.subcompanyid1,t3.subcompanyname,t.departmentid,t4.departmentname,t.jobtitle,t5.jobtitlename,t.seclevel, (case when (").append(str1).append(") > 0 then 1 else 0 end) as stype").append(" from hrmresource t right join (").append(" select t.id from hrm_schedule_personnel t2 left join hrmresource t on t.id = t2.field002 and t.status in (0,1,2,3) where t2.delflag = 0 and t2.field001 = 0 union all").append(" select t.id from hrm_schedule_personnel t2 left join hrmresource t on t.departmentid = t2.field002 and t.status in (0,1,2,3) where t2.delflag = 0 and t2.field001 = 1 and t.seclevel between t2.field003 and t2.field004 union all").append(" select t.id from hrm_schedule_personnel t2 left join hrmresource t on t.subcompanyid1 = t2.field002 and t.status in (0,1,2,3) where t2.delflag = 0 and t2.field001 = 2 and t.seclevel between t2.field003 and t2.field004 union all").append(" select t.id from hrm_schedule_personnel t2 left join HrmRoleMembers t3 on t3.roleid = t2.field002 and t3.rolelevel = t2.field005 left join hrmresource t  on  t3.resourcetype=1 and t3.resourceid = t.id and t.status in (0,1,2,3) where t2.delflag = 0 and t2.field001 = 3 and t.seclevel between t2.field003 and t2.field004 union all");
/* 295 */     if (StringUtil.isNotNull(str3)) stringBuffer2.append(" ").append(str3).append(" group by field002 union all"); 
/* 296 */     stringBuffer2.append(" select t.id from hrm_schedule_personnel t2, hrmresource t where t2.delflag = 0 and t.status in (0,1,2,3) and t2.field001 = 4 and t.seclevel between t2.field003 and t2.field004 UNION ALL")
/* 297 */       .append(" SELECT t.id FROM hrm_schedule_personnel t2,hrmresource t WHERE t2.delflag = 0 AND t2.field001 = 7 AND t2.field006=0 AND t2.field002=t.jobtitle AND t.status IN ( 0, 1, 2, 3 ) UNION ALL");
/* 298 */     if (isOracle()) {
/* 299 */       stringBuffer2.append(" SELECT t.id FROM hrm_schedule_personnel t2,hrmresource t WHERE t2.delflag = 0 AND t2.field001 = 7 AND t2.field006=1 AND t2.field002=t.jobtitle AND t.status IN ( 0, 1, 2, 3 ) AND to_char(t.departmentid) IN( t2.field007 ) UNION ALL");
/* 300 */       stringBuffer2.append(" SELECT t.id FROM hrm_schedule_personnel t2,hrmresource t WHERE t2.delflag = 0 AND t2.field001 = 7 AND t2.field006=2 AND t2.field002=t.jobtitle AND t.status IN ( 0, 1, 2, 3 ) AND to_char(t.subcompanyid1) IN( t2.field007 )");
/*     */     } else {
/* 302 */       stringBuffer2.append(" SELECT t.id FROM hrm_schedule_personnel t2,hrmresource t WHERE t2.delflag = 0 AND t2.field001 = 7 AND t2.field006=1 AND t2.field002=t.jobtitle AND t.status IN ( 0, 1, 2, 3 ) AND t.departmentid IN( t2.field007 ) UNION ALL");
/* 303 */       stringBuffer2.append(" SELECT t.id FROM hrm_schedule_personnel t2,hrmresource t WHERE t2.delflag = 0 AND t2.field001 = 7 AND t2.field006=2 AND t2.field002=t.jobtitle AND t.status IN ( 0, 1, 2, 3 ) AND t.subcompanyid1 IN( t2.field007 )");
/*     */     } 
/* 305 */     stringBuffer2.append(" ) t2 on t.id = t2.id")
/* 306 */       .append(" left join HrmSubCompany t3 on t.subcompanyid1 = t3.id")
/* 307 */       .append(" left join HrmDepartment t4 on t.departmentid = t4.id")
/* 308 */       .append(" left join HrmJobTitles t5 on t.jobtitle = t5.id");
/* 309 */     if (paramMap != null) {
/* 310 */       String str = " where";
/* 311 */       if (paramMap.containsKey("workcode")) {
/* 312 */         stringBuffer2.append(str).append(" t.workcode like '%").append(StringUtil.vString(paramMap.get("workcode"))).append("%'");
/* 313 */         str = getJoinStr(str);
/*     */       } 
/* 315 */       if (paramMap.containsKey("lastname")) {
/* 316 */         stringBuffer2.append(str).append(" t.lastname like '%").append(StringUtil.vString(paramMap.get("lastname"))).append("%'");
/* 317 */         str = getJoinStr(str);
/*     */       } 
/* 319 */       if (paramMap.containsKey("subcompany")) {
/* 320 */         stringBuffer2.append(str).append(" t.subcompanyid1 = ").append(StringUtil.vString(paramMap.get("subcompany")));
/* 321 */         str = getJoinStr(str);
/*     */       } 
/* 323 */       if (paramMap.containsKey("department")) {
/* 324 */         stringBuffer2.append(str).append(" t.departmentid = ").append(StringUtil.vString(paramMap.get("department")));
/* 325 */         str = getJoinStr(str);
/*     */       } 
/* 327 */       if (paramMap.containsKey("jobtitle")) {
/* 328 */         stringBuffer2.append(str).append(" t.jobtitle = ").append(StringUtil.vString(paramMap.get("jobtitle")));
/* 329 */         str = getJoinStr(str);
/*     */       } 
/* 331 */       if (paramMap.containsKey("userId")) {
/* 332 */         stringBuffer2.append(str).append(" t.id = ").append(StringUtil.vString(paramMap.get("userId")));
/* 333 */         str = getJoinStr(str);
/*     */       } 
/* 335 */       if (paramMap.containsKey("userIds")) {
/* 336 */         stringBuffer2.append(str).append(" t.id in (").append(StringUtil.vString(paramMap.get("userIds"))).append(")");
/* 337 */         str = getJoinStr(str);
/*     */       } 
/* 339 */       if (paramMap.containsKey("shiftsSet")) {
/* 340 */         stringBuffer2.append(str).append(" t.id in (").append(str2).append(")");
/* 341 */         str = getJoinStr(str);
/*     */       } 
/* 343 */       if (paramMap.containsKey("allIds")) {
/* 344 */         stringBuffer2.append(str).append(" t.subcompanyid1 in (").append(StringUtil.vString(paramMap.get("allIds"))).append(")");
/* 345 */         str = getJoinStr(str);
/*     */       } 
/*     */     } 
/* 348 */     stringBuffer2.append(" ) t where t.id is not null");
/* 349 */     if (paramMap != null) {
/* 350 */       if (paramMap.containsKey("stype")) {
/* 351 */         stringBuffer2.append(" and t.stype = ").append(StringUtil.vString(paramMap.get("stype")));
/*     */       }
/* 353 */       if (paramMap.containsKey("orderby")) {
/* 354 */         stringBuffer2.append(" ").append(StringUtil.vString(paramMap.get("orderby")));
/*     */       }
/*     */     } 
/* 357 */     return stringBuffer2.toString();
/*     */   }
/*     */   
/*     */   public List<HrmSchedulePerson> findSchedulePersons(Map<String, Comparable> paramMap) {
/* 361 */     ArrayList<HrmSchedulePerson> arrayList = new ArrayList();
/* 362 */     if (paramMap != null && !paramMap.containsKey("orderby")) {
/* 363 */       paramMap.put("orderby", " order by t.seclevel");
/*     */     }
/* 365 */     this.rs.executeSql(getSchedulePersonsSQL(paramMap));
/* 366 */     HrmSchedulePerson hrmSchedulePerson = null;
/* 367 */     while (this.rs.next()) {
/* 368 */       hrmSchedulePerson = new HrmSchedulePerson();
/* 369 */       hrmSchedulePerson.setId(Integer.valueOf(this.rs.getInt("id")));
/* 370 */       hrmSchedulePerson.setWorkcode(StringUtil.vString(this.rs.getString("workcode")));
/* 371 */       hrmSchedulePerson.setLastname(StringUtil.vString(this.rs.getString("lastname")));
/* 372 */       hrmSchedulePerson.setSubcompanyid1(Integer.valueOf(this.rs.getInt("subcompanyid1")));
/* 373 */       hrmSchedulePerson.setSubcompanyName(StringUtil.vString(this.rs.getString("subcompanyname")));
/* 374 */       hrmSchedulePerson.setDepartmentid(Integer.valueOf(this.rs.getInt("departmentid")));
/* 375 */       hrmSchedulePerson.setDepartmentName(StringUtil.vString(this.rs.getString("departmentname")));
/* 376 */       hrmSchedulePerson.setJobtitle(Integer.valueOf(this.rs.getInt("jobtitle")));
/* 377 */       hrmSchedulePerson.setJobtitleName(StringUtil.vString(this.rs.getString("jobtitlename")));
/* 378 */       hrmSchedulePerson.setStype(Integer.valueOf(this.rs.getInt("stype")));
/* 379 */       arrayList.add(hrmSchedulePerson);
/*     */     } 
/* 381 */     return arrayList;
/*     */   }
/*     */   
/*     */   public HrmSchedulePersonnel get(Comparable paramComparable) {
/* 385 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 386 */     hashMap.put("id", paramComparable);
/* 387 */     List<HrmSchedulePersonnel> list = find((Map)hashMap);
/* 388 */     return (list != null && list.size() > 0) ? list.get(0) : null;
/*     */   }
/*     */   
/*     */   public int count(Map<String, Comparable> paramMap) {
/* 392 */     StringBuffer stringBuffer = new StringBuffer("select count(id) as result from hrm_schedule_personnel where 1 = 1");
/* 393 */     if (paramMap != null) {
/* 394 */       if (paramMap.containsKey("id")) {
/* 395 */         stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/* 397 */       if (paramMap.containsKey("sql_id")) {
/* 398 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/* 400 */       if (paramMap.containsKey("delflag")) {
/* 401 */         stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/* 403 */       if (paramMap.containsKey("sql_delflag")) {
/* 404 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 406 */       if (paramMap.containsKey("creater")) {
/* 407 */         stringBuffer.append(" and creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */       }
/* 409 */       if (paramMap.containsKey("sql_creater")) {
/* 410 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */       }
/* 412 */       if (paramMap.containsKey("lastModifier")) {
/* 413 */         stringBuffer.append(" and last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */       }
/* 415 */       if (paramMap.containsKey("sql_lastModifier")) {
/* 416 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */       }
/* 418 */       if (paramMap.containsKey("sn")) {
/* 419 */         stringBuffer.append(" and sn = ").append(StringUtil.vString(paramMap.get("sn")));
/*     */       }
/* 421 */       if (paramMap.containsKey("sql_sn")) {
/* 422 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_sn")));
/*     */       }
/* 424 */       if (paramMap.containsKey("field001")) {
/* 425 */         stringBuffer.append(" and field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*     */       }
/* 427 */       if (paramMap.containsKey("sql_field001")) {
/* 428 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 430 */       if (paramMap.containsKey("field003")) {
/* 431 */         stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */       }
/* 433 */       if (paramMap.containsKey("sql_field003")) {
/* 434 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 436 */       if (paramMap.containsKey("field004")) {
/* 437 */         stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 439 */       if (paramMap.containsKey("sql_field004")) {
/* 440 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 442 */       if (paramMap.containsKey("field005")) {
/* 443 */         stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */       }
/* 445 */       if (paramMap.containsKey("sql_field005")) {
/* 446 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/* 448 */       if (paramMap.containsKey("createTime")) {
/* 449 */         stringBuffer.append(" and create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */       }
/* 451 */       if (paramMap.containsKey("sql_createTime")) {
/* 452 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */       }
/* 454 */       if (paramMap.containsKey("lastModificationTime")) {
/* 455 */         stringBuffer.append(" and last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */       }
/* 457 */       if (paramMap.containsKey("sql_lastModificationTime")) {
/* 458 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */       }
/* 460 */       if (paramMap.containsKey("field002")) {
/* 461 */         stringBuffer.append(" and field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*     */       }
/* 463 */       if (paramMap.containsKey("sql_field002")) {
/* 464 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/*     */     } 
/* 467 */     this.rs.executeSql(stringBuffer.toString());
/* 468 */     return this.rs.next() ? this.rs.getInt(1) : 0;
/*     */   }
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/* 472 */     this.rs.executeSql("update hrm_schedule_personnel set delflag = 1 where id in ( " + paramComparable + " ) ");
/* 473 */     deleteVal(paramComparable);
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/* 477 */     if (paramMap == null || paramMap.isEmpty())
/* 478 */       return;  StringBuffer stringBuffer = new StringBuffer("select id from hrm_schedule_personnel where 1 = 1");
/* 479 */     if (paramMap.containsKey("id")) {
/* 480 */       stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */     }
/* 482 */     if (paramMap.containsKey("sql_id")) {
/* 483 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */     }
/* 485 */     if (paramMap.containsKey("delflag")) {
/* 486 */       stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */     }
/* 488 */     if (paramMap.containsKey("sql_delflag")) {
/* 489 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */     }
/* 491 */     if (paramMap.containsKey("creater")) {
/* 492 */       stringBuffer.append(" and creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */     }
/* 494 */     if (paramMap.containsKey("sql_creater")) {
/* 495 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */     }
/* 497 */     if (paramMap.containsKey("lastModifier")) {
/* 498 */       stringBuffer.append(" and last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */     }
/* 500 */     if (paramMap.containsKey("sql_lastModifier")) {
/* 501 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */     }
/* 503 */     if (paramMap.containsKey("sn")) {
/* 504 */       stringBuffer.append(" and sn = ").append(StringUtil.vString(paramMap.get("sn")));
/*     */     }
/* 506 */     if (paramMap.containsKey("sql_sn")) {
/* 507 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_sn")));
/*     */     }
/* 509 */     if (paramMap.containsKey("field001")) {
/* 510 */       stringBuffer.append(" and field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*     */     }
/* 512 */     if (paramMap.containsKey("sql_field001")) {
/* 513 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */     }
/* 515 */     if (paramMap.containsKey("field003")) {
/* 516 */       stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */     }
/* 518 */     if (paramMap.containsKey("sql_field003")) {
/* 519 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */     }
/* 521 */     if (paramMap.containsKey("field004")) {
/* 522 */       stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */     }
/* 524 */     if (paramMap.containsKey("sql_field004")) {
/* 525 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */     }
/* 527 */     if (paramMap.containsKey("field005")) {
/* 528 */       stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */     }
/* 530 */     if (paramMap.containsKey("sql_field005")) {
/* 531 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */     }
/* 533 */     if (paramMap.containsKey("createTime")) {
/* 534 */       stringBuffer.append(" and create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */     }
/* 536 */     if (paramMap.containsKey("sql_createTime")) {
/* 537 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */     }
/* 539 */     if (paramMap.containsKey("lastModificationTime")) {
/* 540 */       stringBuffer.append(" and last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */     }
/* 542 */     if (paramMap.containsKey("sql_lastModificationTime")) {
/* 543 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */     }
/* 545 */     if (paramMap.containsKey("field002")) {
/* 546 */       stringBuffer.append(" and field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*     */     }
/* 548 */     if (paramMap.containsKey("sql_field002")) {
/* 549 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */     }
/* 551 */     this.rs.executeSql((new StringBuffer("update hrm_schedule_personnel set delflag = 1  where id in (" + stringBuffer.toString() + ")")).toString());
/* 552 */     deleteVal(stringBuffer.toString());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/dao/HrmSchedulePersonnelDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */