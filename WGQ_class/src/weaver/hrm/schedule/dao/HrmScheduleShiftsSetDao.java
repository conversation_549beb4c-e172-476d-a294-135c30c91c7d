/*     */ package weaver.hrm.schedule.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseConnection;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsSet;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleShiftsDetailManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleShiftsSetDao
/*     */   extends BaseConnection
/*     */   implements BaseDao<HrmScheduleShiftsSet>
/*     */ {
/*     */   public Comparable insert(HrmScheduleShiftsSet paramHrmScheduleShiftsSet) {
/*  24 */     if (paramHrmScheduleShiftsSet == null) return Integer.valueOf(-1); 
/*  25 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_schedule_shifts_set (id,delflag,creater,create_time,last_modifier,last_modification_time,sn,").append(" field001,field002,field003,field004,field005,field006,").append(" field007 )").append(" values(" + paramHrmScheduleShiftsSet.getId() + "," + paramHrmScheduleShiftsSet.getDelflag() + "," + paramHrmScheduleShiftsSet.getCreater() + ",'" + paramHrmScheduleShiftsSet.getCreateTime() + "'," + paramHrmScheduleShiftsSet.getLastModifier() + ",").append(" '" + paramHrmScheduleShiftsSet.getLastModificationTime() + "'," + paramHrmScheduleShiftsSet.getSn() + ",'" + paramHrmScheduleShiftsSet.getField001() + "'," + paramHrmScheduleShiftsSet.getField002() + ",").append(" " + paramHrmScheduleShiftsSet.getField003() + "," + paramHrmScheduleShiftsSet.getField004() + "," + paramHrmScheduleShiftsSet.getField005() + "," + paramHrmScheduleShiftsSet.getField006() + ",").append(" '" + paramHrmScheduleShiftsSet.getField007() + "' )");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  33 */     this.rs.executeSql(stringBuffer.toString());
/*  34 */     this.rs.executeSql("insert into hrm_schedule_shifts_set_id(field001,field002) values(" + paramHrmScheduleShiftsSet.getId() + ",'" + paramHrmScheduleShiftsSet.getField001() + "')");
/*  35 */     return Integer.valueOf(0);
/*     */   }
/*     */   
/*     */   public void update(HrmScheduleShiftsSet paramHrmScheduleShiftsSet) {
/*  39 */     if (paramHrmScheduleShiftsSet == null)
/*  40 */       return;  StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_schedule_shifts_set set").append(" delflag = " + paramHrmScheduleShiftsSet.getDelflag() + ",creater = " + paramHrmScheduleShiftsSet.getCreater() + ",create_time = '" + paramHrmScheduleShiftsSet.getCreateTime() + "',").append(" last_modifier = " + paramHrmScheduleShiftsSet.getLastModifier() + ",last_modification_time = '" + paramHrmScheduleShiftsSet.getLastModificationTime() + "',sn = " + paramHrmScheduleShiftsSet.getSn() + ",").append(" field001 = '" + paramHrmScheduleShiftsSet.getField001() + "',field002 = " + paramHrmScheduleShiftsSet.getField002() + ",field003 = " + paramHrmScheduleShiftsSet.getField003() + ",").append(" field004 = " + paramHrmScheduleShiftsSet.getField004() + ",field005 = " + paramHrmScheduleShiftsSet.getField005() + ",field006 = " + paramHrmScheduleShiftsSet.getField006() + ",").append(" field007 = '" + paramHrmScheduleShiftsSet.getField007() + "'").append(" where id = " + paramHrmScheduleShiftsSet.getId() + "");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  48 */     this.rs.executeSql(stringBuffer.toString());
/*  49 */     this.rs.executeSql("update hrm_schedule_shifts_set_id set field002 = '" + paramHrmScheduleShiftsSet.getField001() + "' where field001 = " + paramHrmScheduleShiftsSet.getId());
/*     */   }
/*     */   
/*     */   public List<HrmScheduleShiftsSet> find(Map<String, Comparable> paramMap) {
/*  53 */     ArrayList<HrmScheduleShiftsSet> arrayList = new ArrayList();
/*  54 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.delflag,t.creater,t.create_time,t.last_modifier,t.last_modification_time,t.sn,").append(" t.field001,t.field002,t.field003,t.field004,t.field005,t.field006,").append(" t.field007").append(" from hrm_schedule_shifts_set t").append(" where t.delflag = 0");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  60 */     if (paramMap != null) {
/*  61 */       if (paramMap.containsKey("id")) {
/*  62 */         stringBuffer.append(" and t.id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/*  64 */       if (paramMap.containsKey("begin_id")) {
/*  65 */         stringBuffer.append(" and t.id >= ").append(StringUtil.vString(paramMap.get("begin_id")));
/*     */       }
/*  67 */       if (paramMap.containsKey("end_id")) {
/*  68 */         stringBuffer.append(" and t.id < ").append(StringUtil.vString(paramMap.get("end_id")));
/*     */       }
/*  70 */       if (paramMap.containsKey("realId")) {
/*  71 */         stringBuffer.append(" and t.id in (select field001 from hrm_schedule_shifts_set_id where id = ").append(StringUtil.vString(paramMap.get("realId"))).append(")");
/*     */       }
/*  73 */       if (paramMap.containsKey("sql_id")) {
/*  74 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/*  76 */       if (paramMap.containsKey("delflag")) {
/*  77 */         stringBuffer.append(" and t.delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/*  79 */       if (paramMap.containsKey("begin_delflag")) {
/*  80 */         stringBuffer.append(" and t.delflag >= ").append(StringUtil.vString(paramMap.get("begin_delflag")));
/*     */       }
/*  82 */       if (paramMap.containsKey("end_delflag")) {
/*  83 */         stringBuffer.append(" and t.delflag < ").append(StringUtil.vString(paramMap.get("end_delflag")));
/*     */       }
/*  85 */       if (paramMap.containsKey("sql_delflag")) {
/*  86 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/*  88 */       if (paramMap.containsKey("creater")) {
/*  89 */         stringBuffer.append(" and t.creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */       }
/*  91 */       if (paramMap.containsKey("begin_creater")) {
/*  92 */         stringBuffer.append(" and t.creater >= ").append(StringUtil.vString(paramMap.get("begin_creater")));
/*     */       }
/*  94 */       if (paramMap.containsKey("end_creater")) {
/*  95 */         stringBuffer.append(" and t.creater < ").append(StringUtil.vString(paramMap.get("end_creater")));
/*     */       }
/*  97 */       if (paramMap.containsKey("sql_creater")) {
/*  98 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */       }
/* 100 */       if (paramMap.containsKey("lastModifier")) {
/* 101 */         stringBuffer.append(" and t.last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */       }
/* 103 */       if (paramMap.containsKey("begin_lastModifier")) {
/* 104 */         stringBuffer.append(" and t.last_modifier >= ").append(StringUtil.vString(paramMap.get("begin_lastModifier")));
/*     */       }
/* 106 */       if (paramMap.containsKey("end_lastModifier")) {
/* 107 */         stringBuffer.append(" and t.last_modifier < ").append(StringUtil.vString(paramMap.get("end_lastModifier")));
/*     */       }
/* 109 */       if (paramMap.containsKey("sql_lastModifier")) {
/* 110 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */       }
/* 112 */       if (paramMap.containsKey("sn")) {
/* 113 */         stringBuffer.append(" and t.sn = ").append(StringUtil.vString(paramMap.get("sn")));
/*     */       }
/* 115 */       if (paramMap.containsKey("begin_sn")) {
/* 116 */         stringBuffer.append(" and t.sn >= ").append(StringUtil.vString(paramMap.get("begin_sn")));
/*     */       }
/* 118 */       if (paramMap.containsKey("end_sn")) {
/* 119 */         stringBuffer.append(" and t.sn < ").append(StringUtil.vString(paramMap.get("end_sn")));
/*     */       }
/* 121 */       if (paramMap.containsKey("sql_sn")) {
/* 122 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_sn")));
/*     */       }
/* 124 */       if (paramMap.containsKey("field002")) {
/* 125 */         stringBuffer.append(" and t.field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */       }
/* 127 */       if (paramMap.containsKey("begin_field002")) {
/* 128 */         stringBuffer.append(" and t.field002 >= ").append(StringUtil.vString(paramMap.get("begin_field002")));
/*     */       }
/* 130 */       if (paramMap.containsKey("end_field002")) {
/* 131 */         stringBuffer.append(" and t.field002 < ").append(StringUtil.vString(paramMap.get("end_field002")));
/*     */       }
/* 133 */       if (paramMap.containsKey("sql_field002")) {
/* 134 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 136 */       if (paramMap.containsKey("field003")) {
/* 137 */         stringBuffer.append(" and t.field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */       }
/* 139 */       if (paramMap.containsKey("begin_field003")) {
/* 140 */         stringBuffer.append(" and t.field003 >= ").append(StringUtil.vString(paramMap.get("begin_field003")));
/*     */       }
/* 142 */       if (paramMap.containsKey("end_field003")) {
/* 143 */         stringBuffer.append(" and t.field003 < ").append(StringUtil.vString(paramMap.get("end_field003")));
/*     */       }
/* 145 */       if (paramMap.containsKey("sql_field003")) {
/* 146 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 148 */       if (paramMap.containsKey("field004")) {
/* 149 */         stringBuffer.append(" and t.field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 151 */       if (paramMap.containsKey("begin_field004")) {
/* 152 */         stringBuffer.append(" and t.field004 >= ").append(StringUtil.vString(paramMap.get("begin_field004")));
/*     */       }
/* 154 */       if (paramMap.containsKey("end_field004")) {
/* 155 */         stringBuffer.append(" and t.field004 < ").append(StringUtil.vString(paramMap.get("end_field004")));
/*     */       }
/* 157 */       if (paramMap.containsKey("sql_field004")) {
/* 158 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 160 */       if (paramMap.containsKey("field005")) {
/* 161 */         stringBuffer.append(" and t.field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */       }
/* 163 */       if (paramMap.containsKey("begin_field005")) {
/* 164 */         stringBuffer.append(" and t.field005 >= ").append(StringUtil.vString(paramMap.get("begin_field005")));
/*     */       }
/* 166 */       if (paramMap.containsKey("end_field005")) {
/* 167 */         stringBuffer.append(" and t.field005 < ").append(StringUtil.vString(paramMap.get("end_field005")));
/*     */       }
/* 169 */       if (paramMap.containsKey("sql_field005")) {
/* 170 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/* 172 */       if (paramMap.containsKey("field006")) {
/* 173 */         stringBuffer.append(" and t.field006 = ").append(StringUtil.vString(paramMap.get("field006")));
/*     */       }
/* 175 */       if (paramMap.containsKey("begin_field006")) {
/* 176 */         stringBuffer.append(" and t.field006 >= ").append(StringUtil.vString(paramMap.get("begin_field006")));
/*     */       }
/* 178 */       if (paramMap.containsKey("end_field006")) {
/* 179 */         stringBuffer.append(" and t.field006 < ").append(StringUtil.vString(paramMap.get("end_field006")));
/*     */       }
/* 181 */       if (paramMap.containsKey("sql_field006")) {
/* 182 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field006")));
/*     */       }
/* 184 */       if (paramMap.containsKey("createTime")) {
/* 185 */         stringBuffer.append(" and t.create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */       }
/* 187 */       if (paramMap.containsKey("like_createTime")) {
/* 188 */         stringBuffer.append(" and t.create_time like '%").append(StringUtil.vString(paramMap.get("like_createTime"))).append("%'");
/*     */       }
/* 190 */       if (paramMap.containsKey("sql_createTime")) {
/* 191 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */       }
/* 193 */       if (paramMap.containsKey("lastModificationTime")) {
/* 194 */         stringBuffer.append(" and t.last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */       }
/* 196 */       if (paramMap.containsKey("like_lastModificationTime")) {
/* 197 */         stringBuffer.append(" and t.last_modification_time like '%").append(StringUtil.vString(paramMap.get("like_lastModificationTime"))).append("%'");
/*     */       }
/* 199 */       if (paramMap.containsKey("sql_lastModificationTime")) {
/* 200 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */       }
/* 202 */       if (paramMap.containsKey("field001")) {
/* 203 */         stringBuffer.append(" and t.field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */       }
/* 205 */       if (paramMap.containsKey("like_field001")) {
/* 206 */         stringBuffer.append(" and t.field001 like '%").append(StringUtil.vString(paramMap.get("like_field001"))).append("%'");
/*     */       }
/* 208 */       if (paramMap.containsKey("sql_field001")) {
/* 209 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 211 */       if (paramMap.containsKey("field007")) {
/* 212 */         stringBuffer.append(" and t.field007 = '").append(StringUtil.vString(paramMap.get("field007"))).append("'");
/*     */       }
/* 214 */       if (paramMap.containsKey("like_field007")) {
/* 215 */         stringBuffer.append(" and t.field007 like '%").append(StringUtil.vString(paramMap.get("like_field007"))).append("%'");
/*     */       }
/* 217 */       if (paramMap.containsKey("sql_field007")) {
/* 218 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field007")));
/*     */       }
/* 220 */       if (paramMap.containsKey("mfsql")) {
/* 221 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("mfsql")));
/*     */       }
/* 223 */       if (paramMap.containsKey("like_qCondition")) {
/* 224 */         stringBuffer.append(" and t.field001 like '%" + StringUtil.vString(paramMap.get("like_qCondition")) + "%'");
/*     */       }
/* 226 */       if (paramMap.containsKey("sqlorderby")) {
/* 227 */         stringBuffer.append(" order by " + StringUtil.vString(paramMap.get("sqlorderby")));
/*     */       } else {
/* 229 */         stringBuffer.append(" order by t.last_modification_time ").append((StringUtil.vString(paramMap.get("sqlsortway")).length() > 0) ? StringUtil.vString(paramMap.get("sqlsortway")) : "desc");
/*     */       } 
/*     */     } 
/* 232 */     this.rs.executeSql(stringBuffer.toString());
/* 233 */     HrmScheduleShiftsSet hrmScheduleShiftsSet = null;
/* 234 */     while (this.rs.next()) {
/* 235 */       hrmScheduleShiftsSet = new HrmScheduleShiftsSet();
/* 236 */       hrmScheduleShiftsSet.setId(Long.valueOf(StringUtil.parseToLong(this.rs.getString("id"))));
/* 237 */       hrmScheduleShiftsSet.setDelflag(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("delflag"))));
/* 238 */       hrmScheduleShiftsSet.setCreater(Long.valueOf(StringUtil.parseToLong(this.rs.getString("creater"))));
/* 239 */       hrmScheduleShiftsSet.setCreateTime(StringUtil.vString(this.rs.getString("create_time")));
/* 240 */       hrmScheduleShiftsSet.setLastModifier(Long.valueOf(StringUtil.parseToLong(this.rs.getString("last_modifier"))));
/* 241 */       hrmScheduleShiftsSet.setLastModificationTime(StringUtil.vString(this.rs.getString("last_modification_time")));
/* 242 */       hrmScheduleShiftsSet.setSn(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("sn"))));
/* 243 */       hrmScheduleShiftsSet.setField001(StringUtil.vString(this.rs.getString("field001")));
/* 244 */       hrmScheduleShiftsSet.setField002(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field002"))));
/* 245 */       hrmScheduleShiftsSet.setField003(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field003"))));
/* 246 */       hrmScheduleShiftsSet.setField004(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field004"))));
/* 247 */       hrmScheduleShiftsSet.setField005(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field005"))));
/* 248 */       hrmScheduleShiftsSet.setField006(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field006"))));
/* 249 */       hrmScheduleShiftsSet.setField007(StringUtil.vString(this.rs.getString("field007")));
/* 250 */       arrayList.add(hrmScheduleShiftsSet);
/*     */     } 
/* 252 */     return arrayList;
/*     */   }
/*     */   
/*     */   public List<HrmScheduleShiftsSet> getScheduleShifts(String paramString1, String paramString2, String paramString3) {
/* 256 */     ArrayList<HrmScheduleShiftsSet> arrayList = new ArrayList();
/* 257 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select id, field001 from hrm_schedule_shifts_set where id in (select t.field001 from hrm_schedule_set_detail t where t.delflag = 0").append(" and t.field002 = ").append(paramString1).append(" and t.field003 between '").append(paramString2).append("' and '").append(paramString3).append("'").append(" group by t.field001)");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 262 */     this.rs.executeSql(stringBuffer.toString());
/* 263 */     HrmScheduleShiftsSet hrmScheduleShiftsSet = null;
/* 264 */     HrmScheduleShiftsDetailManager hrmScheduleShiftsDetailManager = new HrmScheduleShiftsDetailManager();
/* 265 */     String str = String.valueOf(hrmScheduleShiftsDetailManager.getLanguageId());
/* 266 */     while (this.rs.next()) {
/* 267 */       hrmScheduleShiftsSet = new HrmScheduleShiftsSet();
/* 268 */       hrmScheduleShiftsSet.setId(Long.valueOf(StringUtil.parseToLong(this.rs.getString("id"))));
/* 269 */       hrmScheduleShiftsSet.setField001(StringUtil.vString(this.rs.getString("field001")));
/* 270 */       hrmScheduleShiftsSet.setField007(hrmScheduleShiftsDetailManager.getWorkTime(String.valueOf(hrmScheduleShiftsSet.getId()), str));
/* 271 */       arrayList.add(hrmScheduleShiftsSet);
/*     */     } 
/* 273 */     return arrayList;
/*     */   }
/*     */   
/*     */   public Long getRealShiftsSetId(String paramString) {
/* 277 */     this.rs.executeSql("select field001 from hrm_schedule_shifts_set_id where id = " + paramString);
/* 278 */     return Long.valueOf(this.rs.next() ? StringUtil.parseToLong(this.rs.getString(1)) : 0L);
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsSet getByRealId(Comparable paramComparable) {
/* 282 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 283 */     hashMap.put("realId", paramComparable);
/* 284 */     List<HrmScheduleShiftsSet> list = find((Map)hashMap);
/* 285 */     return (list != null && list.size() > 0) ? list.get(0) : null;
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsSet get(Comparable paramComparable) {
/* 289 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 290 */     hashMap.put("id", paramComparable);
/* 291 */     List<HrmScheduleShiftsSet> list = find((Map)hashMap);
/* 292 */     return (list != null && list.size() > 0) ? list.get(0) : null;
/*     */   }
/*     */   
/*     */   public int count(Map<String, Comparable> paramMap) {
/* 296 */     StringBuffer stringBuffer = new StringBuffer("select count(id) as result from hrm_schedule_shifts_set where 1 = 1");
/* 297 */     if (paramMap != null) {
/* 298 */       if (paramMap.containsKey("id")) {
/* 299 */         stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/* 301 */       if (paramMap.containsKey("sql_id")) {
/* 302 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/* 304 */       if (paramMap.containsKey("delflag")) {
/* 305 */         stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/* 307 */       if (paramMap.containsKey("sql_delflag")) {
/* 308 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 310 */       if (paramMap.containsKey("creater")) {
/* 311 */         stringBuffer.append(" and creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */       }
/* 313 */       if (paramMap.containsKey("sql_creater")) {
/* 314 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */       }
/* 316 */       if (paramMap.containsKey("lastModifier")) {
/* 317 */         stringBuffer.append(" and last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */       }
/* 319 */       if (paramMap.containsKey("sql_lastModifier")) {
/* 320 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */       }
/* 322 */       if (paramMap.containsKey("sn")) {
/* 323 */         stringBuffer.append(" and sn = ").append(StringUtil.vString(paramMap.get("sn")));
/*     */       }
/* 325 */       if (paramMap.containsKey("sql_sn")) {
/* 326 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_sn")));
/*     */       }
/* 328 */       if (paramMap.containsKey("field002")) {
/* 329 */         stringBuffer.append(" and field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */       }
/* 331 */       if (paramMap.containsKey("sql_field002")) {
/* 332 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 334 */       if (paramMap.containsKey("field003")) {
/* 335 */         stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */       }
/* 337 */       if (paramMap.containsKey("sql_field003")) {
/* 338 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 340 */       if (paramMap.containsKey("field004")) {
/* 341 */         stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 343 */       if (paramMap.containsKey("sql_field004")) {
/* 344 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 346 */       if (paramMap.containsKey("field005")) {
/* 347 */         stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */       }
/* 349 */       if (paramMap.containsKey("sql_field005")) {
/* 350 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/* 352 */       if (paramMap.containsKey("field006")) {
/* 353 */         stringBuffer.append(" and field006 = ").append(StringUtil.vString(paramMap.get("field006")));
/*     */       }
/* 355 */       if (paramMap.containsKey("sql_field006")) {
/* 356 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field006")));
/*     */       }
/* 358 */       if (paramMap.containsKey("createTime")) {
/* 359 */         stringBuffer.append(" and create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */       }
/* 361 */       if (paramMap.containsKey("sql_createTime")) {
/* 362 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */       }
/* 364 */       if (paramMap.containsKey("lastModificationTime")) {
/* 365 */         stringBuffer.append(" and last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */       }
/* 367 */       if (paramMap.containsKey("sql_lastModificationTime")) {
/* 368 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */       }
/* 370 */       if (paramMap.containsKey("field001")) {
/* 371 */         stringBuffer.append(" and field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */       }
/* 373 */       if (paramMap.containsKey("sql_field001")) {
/* 374 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 376 */       if (paramMap.containsKey("field007")) {
/* 377 */         stringBuffer.append(" and field007 = '").append(StringUtil.vString(paramMap.get("field007"))).append("'");
/*     */       }
/* 379 */       if (paramMap.containsKey("sql_field007")) {
/* 380 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field007")));
/*     */       }
/*     */     } 
/* 383 */     this.rs.executeSql(stringBuffer.toString());
/* 384 */     return this.rs.next() ? this.rs.getInt(1) : 0;
/*     */   }
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/* 388 */     this.rs.executeSql("update hrm_schedule_shifts_set set delflag = 1 where id in ( " + paramComparable + " ) ");
/* 389 */     this.rs.executeSql("update hrm_schedule_shifts_detail set delflag = 1 where field001 in ( " + paramComparable + " ) ");
/* 390 */     this.rs.executeSql("delete from hrm_schedule_shifts_set_id where field001 in (" + paramComparable + ")");
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/* 394 */     if (paramMap == null || paramMap.isEmpty())
/* 395 */       return;  StringBuffer stringBuffer = new StringBuffer("select id from hrm_schedule_shifts_set where 1 = 1");
/* 396 */     if (paramMap.containsKey("id")) {
/* 397 */       stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */     }
/* 399 */     if (paramMap.containsKey("sql_id")) {
/* 400 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */     }
/* 402 */     if (paramMap.containsKey("delflag")) {
/* 403 */       stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */     }
/* 405 */     if (paramMap.containsKey("sql_delflag")) {
/* 406 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */     }
/* 408 */     if (paramMap.containsKey("creater")) {
/* 409 */       stringBuffer.append(" and creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */     }
/* 411 */     if (paramMap.containsKey("sql_creater")) {
/* 412 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */     }
/* 414 */     if (paramMap.containsKey("lastModifier")) {
/* 415 */       stringBuffer.append(" and last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */     }
/* 417 */     if (paramMap.containsKey("sql_lastModifier")) {
/* 418 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */     }
/* 420 */     if (paramMap.containsKey("sn")) {
/* 421 */       stringBuffer.append(" and sn = ").append(StringUtil.vString(paramMap.get("sn")));
/*     */     }
/* 423 */     if (paramMap.containsKey("sql_sn")) {
/* 424 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_sn")));
/*     */     }
/* 426 */     if (paramMap.containsKey("field002")) {
/* 427 */       stringBuffer.append(" and field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */     }
/* 429 */     if (paramMap.containsKey("sql_field002")) {
/* 430 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */     }
/* 432 */     if (paramMap.containsKey("field003")) {
/* 433 */       stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */     }
/* 435 */     if (paramMap.containsKey("sql_field003")) {
/* 436 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */     }
/* 438 */     if (paramMap.containsKey("field004")) {
/* 439 */       stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */     }
/* 441 */     if (paramMap.containsKey("sql_field004")) {
/* 442 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */     }
/* 444 */     if (paramMap.containsKey("field005")) {
/* 445 */       stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */     }
/* 447 */     if (paramMap.containsKey("sql_field005")) {
/* 448 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */     }
/* 450 */     if (paramMap.containsKey("field006")) {
/* 451 */       stringBuffer.append(" and field006 = ").append(StringUtil.vString(paramMap.get("field006")));
/*     */     }
/* 453 */     if (paramMap.containsKey("sql_field006")) {
/* 454 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field006")));
/*     */     }
/* 456 */     if (paramMap.containsKey("createTime")) {
/* 457 */       stringBuffer.append(" and create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */     }
/* 459 */     if (paramMap.containsKey("sql_createTime")) {
/* 460 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */     }
/* 462 */     if (paramMap.containsKey("lastModificationTime")) {
/* 463 */       stringBuffer.append(" and last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */     }
/* 465 */     if (paramMap.containsKey("sql_lastModificationTime")) {
/* 466 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */     }
/* 468 */     if (paramMap.containsKey("field001")) {
/* 469 */       stringBuffer.append(" and field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */     }
/* 471 */     if (paramMap.containsKey("sql_field001")) {
/* 472 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */     }
/* 474 */     if (paramMap.containsKey("field007")) {
/* 475 */       stringBuffer.append(" and field007 = '").append(StringUtil.vString(paramMap.get("field007"))).append("'");
/*     */     }
/* 477 */     if (paramMap.containsKey("sql_field007")) {
/* 478 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field007")));
/*     */     }
/* 480 */     this.rs.executeSql((new StringBuffer("update hrm_schedule_shifts_set set delflag = 1  where id in (" + stringBuffer.toString() + ")")).toString());
/* 481 */     this.rs.executeSql((new StringBuffer("delete from hrm_schedule_shifts_set_id where field001 in (" + stringBuffer.toString() + ")")).toString());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/dao/HrmScheduleShiftsSetDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */