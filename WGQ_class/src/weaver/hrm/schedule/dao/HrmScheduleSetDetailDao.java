/*     */ package weaver.hrm.schedule.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.framework.BaseConnection;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleSetDetailDao
/*     */   extends BaseConnection
/*     */   implements BaseDao<HrmScheduleSetDetail>
/*     */ {
/*     */   public Comparable insert(HrmScheduleSetDetail paramHrmScheduleSetDetail) {
/*  24 */     if (paramHrmScheduleSetDetail == null) return Integer.valueOf(-1);
/*     */ 
/*     */ 
/*     */     
/*  28 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_schedule_set_detail (delflag,field001,field002,field003,field004,field005 )").append(" values(" + paramHrmScheduleSetDetail.getDelflag() + ",'" + paramHrmScheduleSetDetail.getField001() + "'," + paramHrmScheduleSetDetail.getField002() + ",'" + paramHrmScheduleSetDetail.getField003() + "',").append(" " + paramHrmScheduleSetDetail.getField004() + ",'" + ((isOracle() && StringUtil.isNull(paramHrmScheduleSetDetail.getField005())) ? "-1" : paramHrmScheduleSetDetail.getField005()) + "' )");
/*  29 */     this.rs.executeSql(stringBuffer.toString());
/*  30 */     return Integer.valueOf(0);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteBeforeSaveSchedulePerson(Long paramLong, String paramString1, String paramString2, String paramString3) {
/*  36 */     StringBuffer stringBuffer = (new StringBuffer("update hrm_schedule_set_detail set delflag = 1")).append(" where field003 between '").append(paramString1).append("' and '").append(paramString2).append("' and delflag = 0").append(" and field002 in (").append(StringUtil.isNotNull(paramString3) ? paramString3 : ("select field002 from hrm_schedule_set_person where field001 = " + paramLong)).append(")");
/*  37 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deletePersonSchedule(Long paramLong, String paramString1, String paramString2, String paramString3) {
/*  44 */     StringBuffer stringBuffer = (new StringBuffer("update hrm_schedule_set_detail set delflag = 1")).append(" where field003 between '").append(paramString1).append("' and '").append(paramString2).append("' and delflag = 0").append(" and field002 = ").append(paramLong).append(" and field001 in (").append(paramString3).append(")");
/*  45 */     this.rs.executeSql(stringBuffer.toString());
/*  46 */     stringBuffer.setLength(0);
/*  47 */     stringBuffer.append("select 1 from hrm_schedule_set_detail ")
/*  48 */       .append(" where field001 in (").append(paramString3).append(") and delflag = 0 ");
/*  49 */     this.rs.executeSql(stringBuffer.toString());
/*  50 */     if (this.rs.getCounts() == 0) {
/*  51 */       RecordSet recordSet1 = new RecordSet();
/*  52 */       RecordSet recordSet2 = new RecordSet();
/*  53 */       stringBuffer.setLength(0);
/*  54 */       stringBuffer.append("select setId from hrm_schedule_set_detail ")
/*  55 */         .append(" where field001 in (").append(paramString3).append(") ");
/*  56 */       recordSet1.executeSql(stringBuffer.toString());
/*  57 */       if (recordSet1.next()) {
/*  58 */         String str = recordSet1.getString("setId");
/*  59 */         stringBuffer.setLength(0);
/*  60 */         stringBuffer.append("update hrm_schedule_set set delflag=1 ")
/*  61 */           .append(" where id='" + str + "'");
/*  62 */         recordSet2.executeSql(stringBuffer.toString());
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveSchedulePerson(HrmScheduleSetDetail paramHrmScheduleSetDetail, String paramString) {
/*  71 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_schedule_set_detail (delflag,field001,field002,field003,field004,field005,setId )").append(" select 0, '" + paramHrmScheduleSetDetail.getField001() + "', field002, '" + paramHrmScheduleSetDetail.getField003() + "', " + paramHrmScheduleSetDetail.getField004() + ", '" + ((isOracle() && StringUtil.isNull(paramHrmScheduleSetDetail.getField005())) ? "-1" : paramHrmScheduleSetDetail.getField005()) + "','" + paramHrmScheduleSetDetail.getSetId() + "' from hrm_schedule_set_person where field001 = " + paramHrmScheduleSetDetail.getSetId()).append(" and delflag = 0");
/*  72 */     if (StringUtil.isNotNull(paramString)) stringBuffer.append(" and field002 in (").append(paramString).append(")"); 
/*  73 */     stringBuffer.append(" group by field002");
/*  74 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */   
/*     */   public void update(HrmScheduleSetDetail paramHrmScheduleSetDetail) {
/*  78 */     if (paramHrmScheduleSetDetail == null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/*  83 */     StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_schedule_set_detail set").append(" delflag = " + paramHrmScheduleSetDetail.getDelflag() + ",field001 = '" + paramHrmScheduleSetDetail.getField001() + "',field002 = " + paramHrmScheduleSetDetail.getField002() + ",").append(" field003 = '" + paramHrmScheduleSetDetail.getField003() + "',field004 = " + paramHrmScheduleSetDetail.getField004() + ",field005 = '" + ((isOracle() && StringUtil.isNull(paramHrmScheduleSetDetail.getField005())) ? "-1" : paramHrmScheduleSetDetail.getField005()) + "'").append(" where id = " + paramHrmScheduleSetDetail.getId() + "");
/*  84 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */   
/*     */   public List<HrmScheduleSetDetail> find(Map<String, Comparable> paramMap) {
/*  88 */     ArrayList<HrmScheduleSetDetail> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/*  92 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.delflag,t.field001,t.field002,t.field003,t.field004,t.field005").append(" from hrm_schedule_set_detail t").append(" where t.delflag = 0");
/*  93 */     if (paramMap != null) {
/*  94 */       if (paramMap.containsKey("id")) {
/*  95 */         stringBuffer.append(" and t.id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/*  97 */       if (paramMap.containsKey("begin_id")) {
/*  98 */         stringBuffer.append(" and t.id >= ").append(StringUtil.vString(paramMap.get("begin_id")));
/*     */       }
/* 100 */       if (paramMap.containsKey("end_id")) {
/* 101 */         stringBuffer.append(" and t.id < ").append(StringUtil.vString(paramMap.get("end_id")));
/*     */       }
/* 103 */       if (paramMap.containsKey("sql_id")) {
/* 104 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/* 106 */       if (paramMap.containsKey("delflag")) {
/* 107 */         stringBuffer.append(" and t.delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/* 109 */       if (paramMap.containsKey("begin_delflag")) {
/* 110 */         stringBuffer.append(" and t.delflag >= ").append(StringUtil.vString(paramMap.get("begin_delflag")));
/*     */       }
/* 112 */       if (paramMap.containsKey("end_delflag")) {
/* 113 */         stringBuffer.append(" and t.delflag < ").append(StringUtil.vString(paramMap.get("end_delflag")));
/*     */       }
/* 115 */       if (paramMap.containsKey("sql_delflag")) {
/* 116 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 118 */       if (paramMap.containsKey("field002")) {
/* 119 */         stringBuffer.append(" and t.field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */       }
/* 121 */       if (paramMap.containsKey("begin_field002")) {
/* 122 */         stringBuffer.append(" and t.field002 >= ").append(StringUtil.vString(paramMap.get("begin_field002")));
/*     */       }
/* 124 */       if (paramMap.containsKey("end_field002")) {
/* 125 */         stringBuffer.append(" and t.field002 < ").append(StringUtil.vString(paramMap.get("end_field002")));
/*     */       }
/* 127 */       if (paramMap.containsKey("sql_field002")) {
/* 128 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 130 */       if (paramMap.containsKey("field004")) {
/* 131 */         stringBuffer.append(" and t.field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 133 */       if (paramMap.containsKey("begin_field004")) {
/* 134 */         stringBuffer.append(" and t.field004 >= ").append(StringUtil.vString(paramMap.get("begin_field004")));
/*     */       }
/* 136 */       if (paramMap.containsKey("end_field004")) {
/* 137 */         stringBuffer.append(" and t.field004 < ").append(StringUtil.vString(paramMap.get("end_field004")));
/*     */       }
/* 139 */       if (paramMap.containsKey("sql_field004")) {
/* 140 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 142 */       if (paramMap.containsKey("field001")) {
/* 143 */         stringBuffer.append(" and t.field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */       }
/* 145 */       if (paramMap.containsKey("like_field001")) {
/* 146 */         stringBuffer.append(" and t.field001 like '%").append(StringUtil.vString(paramMap.get("like_field001"))).append("%'");
/*     */       }
/* 148 */       if (paramMap.containsKey("sql_field001")) {
/* 149 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 151 */       if (paramMap.containsKey("field003")) {
/* 152 */         stringBuffer.append(" and t.field003 = '").append(StringUtil.vString(paramMap.get("field003"))).append("'");
/*     */       }
/* 154 */       if (paramMap.containsKey("like_field003")) {
/* 155 */         stringBuffer.append(" and t.field003 like '%").append(StringUtil.vString(paramMap.get("like_field003"))).append("%'");
/*     */       }
/* 157 */       if (paramMap.containsKey("sql_field003")) {
/* 158 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 160 */       if (paramMap.containsKey("field005")) {
/* 161 */         stringBuffer.append(" and t.field005 = '").append(StringUtil.vString(paramMap.get("field005"))).append("'");
/*     */       }
/* 163 */       if (paramMap.containsKey("like_field005")) {
/* 164 */         stringBuffer.append(" and t.field005 like '%").append(StringUtil.vString(paramMap.get("like_field005"))).append("%'");
/*     */       }
/* 166 */       if (paramMap.containsKey("sql_field005")) {
/* 167 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/* 169 */       if (paramMap.containsKey("mfsql")) {
/* 170 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("mfsql")));
/*     */       }
/* 172 */       if (paramMap.containsKey("sqlorderby")) {
/* 173 */         stringBuffer.append(" order by " + StringUtil.vString(paramMap.get("sqlorderby")));
/*     */       } else {
/* 175 */         stringBuffer.append(" order by t.id ").append((StringUtil.vString(paramMap.get("sqlsortway")).length() > 0) ? StringUtil.vString(paramMap.get("sqlsortway")) : "desc");
/*     */       } 
/*     */     } 
/* 178 */     this.rs.executeSql(stringBuffer.toString());
/* 179 */     HrmScheduleSetDetail hrmScheduleSetDetail = null;
/* 180 */     while (this.rs.next()) {
/* 181 */       hrmScheduleSetDetail = new HrmScheduleSetDetail();
/* 182 */       hrmScheduleSetDetail.setId(Long.valueOf(StringUtil.parseToLong(this.rs.getString("id"))));
/* 183 */       hrmScheduleSetDetail.setDelflag(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("delflag"))));
/* 184 */       hrmScheduleSetDetail.setField001(StringUtil.vString(this.rs.getString("field001")));
/* 185 */       hrmScheduleSetDetail.setField002(Long.valueOf(StringUtil.parseToLong(this.rs.getString("field002"))));
/* 186 */       hrmScheduleSetDetail.setField003(StringUtil.vString(this.rs.getString("field003")));
/* 187 */       hrmScheduleSetDetail.setField004(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field004"))));
/* 188 */       hrmScheduleSetDetail.setField005(StringUtil.vString(this.rs.getString("field005")));
/* 189 */       if (isOracle() && hrmScheduleSetDetail.getField005().equals("-1")) hrmScheduleSetDetail.setField005(""); 
/* 190 */       arrayList.add(hrmScheduleSetDetail);
/*     */     } 
/* 192 */     return arrayList;
/*     */   }
/*     */   
/*     */   public List<Map<String, String>> getGroupData(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/* 196 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 204 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.*,t2.field001,t2.field003 as t2Field003,t2.field007 from (select t.field001 as sId, count(t.field002) cnt, field003").append(" from hrm_schedule_set_detail t right join hrmresource t2 on t.field002 = t2.id where t.delflag = 0 and t.field005 != '").append(isOracle() ? "-1" : "").append("'").append(" and t.field003 between '").append(paramString1).append("' and '").append(paramString2).append("'").append(StringUtil.isNotNull(paramString3) ? (" and t.field002 = " + paramString3) : "").append(StringUtil.isNotNull(paramString4) ? (" and t.field002 in (select id from hrmresource where subcompanyid1 in (" + paramString4 + ") and status between 0 and 3)") : "").append(" group by t.field001,field003) t left join hrm_schedule_shifts_set t2 on t.sId = t2.id").append(" where ").append((paramInt > 0) ? ("t2.field002 = " + paramInt + " and ") : "").append("t2.delflag = 0 order by t.field003 asc, t.cnt desc");
/* 205 */     this.rs.executeSql(stringBuffer.toString());
/* 206 */     HashMap<Object, Object> hashMap = null;
/* 207 */     while (this.rs.next()) {
/* 208 */       hashMap = new HashMap<>();
/* 209 */       hashMap.put("sId", this.rs.getString("sId"));
/* 210 */       hashMap.put("cnt", this.rs.getString("cnt"));
/* 211 */       hashMap.put("date", this.rs.getString("field003"));
/* 212 */       hashMap.put("field001", this.rs.getString("field001"));
/* 213 */       hashMap.put("field003", this.rs.getString("t2Field003"));
/* 214 */       hashMap.put("field007", this.rs.getString("field007"));
/* 215 */       arrayList.add(hashMap);
/*     */     } 
/* 217 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public HrmScheduleSetDetail get(Comparable paramComparable) {
/* 221 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 222 */     hashMap.put("id", paramComparable);
/* 223 */     List<HrmScheduleSetDetail> list = find((Map)hashMap);
/* 224 */     return (list != null && list.size() > 0) ? list.get(0) : null;
/*     */   }
/*     */   
/*     */   public int count(Map<String, Comparable> paramMap) {
/* 228 */     StringBuffer stringBuffer = new StringBuffer("select count(id) as result from hrm_schedule_set_detail where 1 = 1");
/* 229 */     if (paramMap != null) {
/* 230 */       if (paramMap.containsKey("id")) {
/* 231 */         stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/* 233 */       if (paramMap.containsKey("sql_id")) {
/* 234 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/* 236 */       if (paramMap.containsKey("delflag")) {
/* 237 */         stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/* 239 */       if (paramMap.containsKey("sql_delflag")) {
/* 240 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 242 */       if (paramMap.containsKey("field002")) {
/* 243 */         stringBuffer.append(" and field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */       }
/* 245 */       if (paramMap.containsKey("sql_field002")) {
/* 246 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 248 */       if (paramMap.containsKey("field004")) {
/* 249 */         stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 251 */       if (paramMap.containsKey("sql_field004")) {
/* 252 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 254 */       if (paramMap.containsKey("field001")) {
/* 255 */         stringBuffer.append(" and field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */       }
/* 257 */       if (paramMap.containsKey("sql_field001")) {
/* 258 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 260 */       if (paramMap.containsKey("field003")) {
/* 261 */         stringBuffer.append(" and field003 = '").append(StringUtil.vString(paramMap.get("field003"))).append("'");
/*     */       }
/* 263 */       if (paramMap.containsKey("sql_field003")) {
/* 264 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 266 */       if (paramMap.containsKey("field005")) {
/* 267 */         stringBuffer.append(" and field005 = '").append(StringUtil.vString(paramMap.get("field005"))).append("'");
/*     */       }
/* 269 */       if (paramMap.containsKey("sql_field005")) {
/* 270 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/*     */     } 
/* 273 */     this.rs.executeSql(stringBuffer.toString());
/* 274 */     return this.rs.next() ? this.rs.getInt(1) : 0;
/*     */   }
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/* 278 */     this.rs.executeSql("update hrm_schedule_set_detail set delflag = 1 where id in ( " + paramComparable + " ) ");
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/* 282 */     if (paramMap == null || paramMap.isEmpty())
/* 283 */       return;  StringBuffer stringBuffer = new StringBuffer("update hrm_schedule_set_detail set delflag = 1  where 1 = 1");
/* 284 */     if (paramMap.containsKey("id")) {
/* 285 */       stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */     }
/* 287 */     if (paramMap.containsKey("sql_id")) {
/* 288 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */     }
/* 290 */     if (paramMap.containsKey("delflag")) {
/* 291 */       stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */     }
/* 293 */     if (paramMap.containsKey("sql_delflag")) {
/* 294 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */     }
/* 296 */     if (paramMap.containsKey("field002")) {
/* 297 */       stringBuffer.append(" and field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */     }
/* 299 */     if (paramMap.containsKey("sql_field002")) {
/* 300 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */     }
/* 302 */     if (paramMap.containsKey("field004")) {
/* 303 */       stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */     }
/* 305 */     if (paramMap.containsKey("sql_field004")) {
/* 306 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */     }
/* 308 */     if (paramMap.containsKey("field001")) {
/* 309 */       stringBuffer.append(" and field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */     }
/* 311 */     if (paramMap.containsKey("sql_field001")) {
/* 312 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */     }
/* 314 */     if (paramMap.containsKey("field003")) {
/* 315 */       stringBuffer.append(" and field003 = '").append(StringUtil.vString(paramMap.get("field003"))).append("'");
/*     */     }
/* 317 */     if (paramMap.containsKey("sql_field003")) {
/* 318 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */     }
/* 320 */     if (paramMap.containsKey("field005")) {
/* 321 */       stringBuffer.append(" and field005 = '").append(StringUtil.vString(paramMap.get("field005"))).append("'");
/*     */     }
/* 323 */     if (paramMap.containsKey("sql_field005")) {
/* 324 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */     }
/* 326 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/dao/HrmScheduleSetDetailDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */