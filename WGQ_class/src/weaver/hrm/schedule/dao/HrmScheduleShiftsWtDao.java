/*     */ package weaver.hrm.schedule.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.framework.BaseConnection;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleShiftsWt;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleShiftsWtDao
/*     */   extends BaseConnection
/*     */   implements BaseDao<HrmScheduleShiftsWt>
/*     */ {
/*     */   public Comparable insert(HrmScheduleShiftsWt paramHrmScheduleShiftsWt) {
/*  23 */     if (paramHrmScheduleShiftsWt == null) return Integer.valueOf(-1); 
/*  24 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_schedule_shifts_wt (field001,field002,field003 )").append(" values(" + paramHrmScheduleShiftsWt.getField001() + "," + paramHrmScheduleShiftsWt.getField002() + "," + paramHrmScheduleShiftsWt.getField003() + " )");
/*     */ 
/*     */     
/*  27 */     this.rs.executeSql(stringBuffer.toString());
/*  28 */     return Integer.valueOf(0);
/*     */   }
/*     */   
/*     */   public void update(HrmScheduleShiftsWt paramHrmScheduleShiftsWt) {
/*  32 */     if (paramHrmScheduleShiftsWt == null)
/*  33 */       return;  StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_schedule_shifts_wt set").append(" field001 = " + paramHrmScheduleShiftsWt.getField001() + ",field002 = " + paramHrmScheduleShiftsWt.getField002() + ",field003 = " + paramHrmScheduleShiftsWt.getField003() + "").append(" where id = " + paramHrmScheduleShiftsWt.getId() + "");
/*     */ 
/*     */ 
/*     */     
/*  37 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */   
/*     */   public List<HrmScheduleShiftsWt> find(Map<String, Comparable> paramMap) {
/*  41 */     ArrayList<HrmScheduleShiftsWt> arrayList = new ArrayList();
/*  42 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.field001,t.field002,t.field003").append(" from hrm_schedule_shifts_wt t").append(" where 1 = 1");
/*     */ 
/*     */ 
/*     */     
/*  46 */     if (paramMap != null) {
/*  47 */       if (paramMap.containsKey("id")) {
/*  48 */         stringBuffer.append(" and t.id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/*  50 */       if (paramMap.containsKey("begin_id")) {
/*  51 */         stringBuffer.append(" and t.id >= ").append(StringUtil.vString(paramMap.get("begin_id")));
/*     */       }
/*  53 */       if (paramMap.containsKey("end_id")) {
/*  54 */         stringBuffer.append(" and t.id < ").append(StringUtil.vString(paramMap.get("end_id")));
/*     */       }
/*  56 */       if (paramMap.containsKey("sql_id")) {
/*  57 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/*  59 */       if (paramMap.containsKey("field001")) {
/*  60 */         stringBuffer.append(" and t.field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*     */       }
/*  62 */       if (paramMap.containsKey("begin_field001")) {
/*  63 */         stringBuffer.append(" and t.field001 >= ").append(StringUtil.vString(paramMap.get("begin_field001")));
/*     */       }
/*  65 */       if (paramMap.containsKey("end_field001")) {
/*  66 */         stringBuffer.append(" and t.field001 < ").append(StringUtil.vString(paramMap.get("end_field001")));
/*     */       }
/*  68 */       if (paramMap.containsKey("sql_field001")) {
/*  69 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/*  71 */       if (paramMap.containsKey("field002")) {
/*  72 */         stringBuffer.append(" and t.field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */       }
/*  74 */       if (paramMap.containsKey("begin_field002")) {
/*  75 */         stringBuffer.append(" and t.field002 >= ").append(StringUtil.vString(paramMap.get("begin_field002")));
/*     */       }
/*  77 */       if (paramMap.containsKey("end_field002")) {
/*  78 */         stringBuffer.append(" and t.field002 < ").append(StringUtil.vString(paramMap.get("end_field002")));
/*     */       }
/*  80 */       if (paramMap.containsKey("sql_field002")) {
/*  81 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/*  83 */       if (paramMap.containsKey("field003")) {
/*  84 */         stringBuffer.append(" and t.field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */       }
/*  86 */       if (paramMap.containsKey("begin_field003")) {
/*  87 */         stringBuffer.append(" and t.field003 >= ").append(StringUtil.vString(paramMap.get("begin_field003")));
/*     */       }
/*  89 */       if (paramMap.containsKey("end_field003")) {
/*  90 */         stringBuffer.append(" and t.field003 < ").append(StringUtil.vString(paramMap.get("end_field003")));
/*     */       }
/*  92 */       if (paramMap.containsKey("sql_field003")) {
/*  93 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/*  95 */       if (paramMap.containsKey("mfsql")) {
/*  96 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("mfsql")));
/*     */       }
/*  98 */       if (paramMap.containsKey("sqlorderby")) {
/*  99 */         stringBuffer.append(" order by " + StringUtil.vString(paramMap.get("sqlorderby")));
/*     */       } else {
/* 101 */         stringBuffer.append(" order by t.id ").append((StringUtil.vString(paramMap.get("sqlsortway")).length() > 0) ? StringUtil.vString(paramMap.get("sqlsortway")) : "desc");
/*     */       } 
/*     */     } 
/* 104 */     this.rs.executeSql(stringBuffer.toString());
/* 105 */     HrmScheduleShiftsWt hrmScheduleShiftsWt = null;
/* 106 */     while (this.rs.next()) {
/* 107 */       hrmScheduleShiftsWt = new HrmScheduleShiftsWt();
/* 108 */       hrmScheduleShiftsWt.setId(Long.valueOf(StringUtil.parseToLong(this.rs.getString("id"))));
/* 109 */       hrmScheduleShiftsWt.setField001(Long.valueOf(StringUtil.parseToLong(this.rs.getString("field001"))));
/* 110 */       hrmScheduleShiftsWt.setField002(Long.valueOf(StringUtil.parseToLong(this.rs.getString("field002"))));
/* 111 */       hrmScheduleShiftsWt.setField003(Long.valueOf(StringUtil.parseToLong(this.rs.getString("field003"))));
/* 112 */       arrayList.add(hrmScheduleShiftsWt);
/*     */     } 
/* 114 */     return arrayList;
/*     */   }
/*     */   
/*     */   public HrmScheduleShiftsWt get(Comparable paramComparable) {
/* 118 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 119 */     hashMap.put("id", paramComparable);
/* 120 */     List<HrmScheduleShiftsWt> list = find((Map)hashMap);
/* 121 */     return (list != null && list.size() > 0) ? list.get(0) : null;
/*     */   }
/*     */   
/*     */   public int count(Map<String, Comparable> paramMap) {
/* 125 */     StringBuffer stringBuffer = new StringBuffer("select count(id) as result from hrm_schedule_shifts_wt where 1 = 1");
/* 126 */     if (paramMap != null) {
/* 127 */       if (paramMap.containsKey("id")) {
/* 128 */         stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/* 130 */       if (paramMap.containsKey("sql_id")) {
/* 131 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/* 133 */       if (paramMap.containsKey("field001")) {
/* 134 */         stringBuffer.append(" and field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*     */       }
/* 136 */       if (paramMap.containsKey("sql_field001")) {
/* 137 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 139 */       if (paramMap.containsKey("field002")) {
/* 140 */         stringBuffer.append(" and field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */       }
/* 142 */       if (paramMap.containsKey("sql_field002")) {
/* 143 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 145 */       if (paramMap.containsKey("field003")) {
/* 146 */         stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */       }
/* 148 */       if (paramMap.containsKey("sql_field003")) {
/* 149 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/*     */     } 
/* 152 */     this.rs.executeSql(stringBuffer.toString());
/* 153 */     return this.rs.next() ? this.rs.getInt(1) : 0;
/*     */   }
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/* 157 */     this.rs.executeSql("delete from hrm_schedule_shifts_wt where id in ( " + paramComparable + " )");
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/* 161 */     if (paramMap == null || paramMap.isEmpty())
/* 162 */       return;  StringBuffer stringBuffer = new StringBuffer("delete from hrm_schedule_shifts_wt where 1 = 1");
/* 163 */     if (paramMap.containsKey("id")) {
/* 164 */       stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */     }
/* 166 */     if (paramMap.containsKey("sql_id")) {
/* 167 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */     }
/* 169 */     if (paramMap.containsKey("field001")) {
/* 170 */       stringBuffer.append(" and field001 = ").append(StringUtil.vString(paramMap.get("field001")));
/*     */     }
/* 172 */     if (paramMap.containsKey("sql_field001")) {
/* 173 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */     }
/* 175 */     if (paramMap.containsKey("field002")) {
/* 176 */       stringBuffer.append(" and field002 = ").append(StringUtil.vString(paramMap.get("field002")));
/*     */     }
/* 178 */     if (paramMap.containsKey("sql_field002")) {
/* 179 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */     }
/* 181 */     if (paramMap.containsKey("field003")) {
/* 182 */       stringBuffer.append(" and field003 = ").append(StringUtil.vString(paramMap.get("field003")));
/*     */     }
/* 184 */     if (paramMap.containsKey("sql_field003")) {
/* 185 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */     }
/* 187 */     this.rs.executeSql(stringBuffer.toString());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/dao/HrmScheduleShiftsWtDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */